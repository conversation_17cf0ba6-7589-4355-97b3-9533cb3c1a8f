{"ast": null, "code": "import { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as moment from 'moment';\nimport { CommonModule } from '@angular/common';\nimport { CalendarModule, CalendarView } from 'angular-calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { BehaviorSubject, concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@nebular/theme\";\nimport * as i6 from \"src/app/shared/services/event.service\";\nimport * as i7 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i8 from \"angular-calendar\";\nfunction EditAvailableTimeSlotComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogConfirm_r4 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogConfirm_r4));\n    });\n    i0.ɵɵtext(1, \" \\u8907\\u88FD\\u4E0A\\u4E00\\u5468\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, segment_r5.date, \"HH:mm\"));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementStart(2, \"input\", 25);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_27_div_2_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const segment_r5 = i0.ɵɵnextContext().segment;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheck($event, segment_r5.date));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"className\", i0.ɵɵpipeBind1(1, 4, ctx_r2.listEvent$) && ctx_r2.isCellEmpty(segment_r5.date) ? \"bg-event\" : \"bg-inherit\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", segment_r5.date.toISOString())(\"checked\", i0.ɵɵpipeBind1(3, 6, ctx_r2.listEvent$) && ctx_r2.isCellEmpty(segment_r5.date))(\"disabled\", i0.ɵɵpipeBind1(4, 8, ctx_r2.listEvent$) && ctx_r2.checkDisable(segment_r5.date));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, EditAvailableTimeSlotComponent_ng_template_27_span_1_Template, 3, 4, \"span\", 22)(2, EditAvailableTimeSlotComponent_ng_template_27_div_2_Template, 5, 10, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const isTimeLabel_r7 = ctx.isTimeLabel;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", isTimeLabel_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !isTimeLabel_r7);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 26);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\", 27);\n    i0.ɵɵtext(2, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u8A2D\\u5B9A\\u70BA\\u4E0A\\u5468\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-footer\", 28)(4, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_4_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(5, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_6_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmCopy(ref_r9));\n    });\n    i0.ɵɵtext(7, \"\\u78BA\\u8A8D\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let EditAvailableTimeSlotComponent = /*#__PURE__*/(() => {\n  class EditAvailableTimeSlotComponent {\n    constructor(changeDetector, preOderSettingService, route, _router, messageService, _location, dialogService, _eventService) {\n      this.changeDetector = changeDetector;\n      this.preOderSettingService = preOderSettingService;\n      this.route = route;\n      this._router = _router;\n      this.messageService = messageService;\n      this._location = _location;\n      this.dialogService = dialogService;\n      this._eventService = _eventService;\n      this.locale = 'zh';\n      this.getPreOderSettingRes = [];\n      this.activeDayIsOpen = true;\n      this.savePreOrderSetting = [];\n      this.listEvent$ = new BehaviorSubject([]);\n      this.view = CalendarView.Week;\n      this.viewDate = new Date();\n      this.paramInfo = null;\n      this.listDate = [];\n      this.test = 0;\n      this.buildCaseId = this.route.snapshot.paramMap.get('id');\n      this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'));\n    }\n    ngOnInit() {\n      if (this.paramInfo) {\n        this.viewDate = new Date(this.paramInfo.CDateStart);\n      }\n      this.getPreOrderSetting().subscribe();\n    }\n    getPreOrderSetting() {\n      return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : [];\n          this.initEvent();\n        }\n      }));\n    }\n    initEvent() {\n      let temp = [];\n      if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n        this.getPreOderSettingRes.forEach(e => {\n          if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n            let date = e.CDate ? new Date(e.CDate) : undefined;\n            date = date ? new Date(date.setMinutes(0)) : date;\n            date = date ? new Date(date.setSeconds(0)) : date;\n            let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0;\n            let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0;\n            temp.push({\n              id: e.CID,\n              CBuildCaseId: this.buildCaseId,\n              CDate: e.CDate,\n              CHour: e.CHour,\n              CIsDelete: false,\n              isChange: false,\n              start: startDate,\n              end: endDate,\n              display: \"background\",\n              IsOld: true\n            });\n          }\n        });\n      }\n      this.listEvent$.next(temp);\n    }\n    handleDateSelect(selectInfo) {\n      if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\n        let temp = [...this.listEvent$.value];\n        const calendarApi = selectInfo.view.calendar;\n        calendarApi.unselect(); // clear date selection\n        calendarApi.addEvent({\n          start: selectInfo.startStr,\n          end: selectInfo.endStr,\n          display: 'background'\n        });\n        temp.push({\n          CBuildCaseId: this.buildCaseId,\n          CDate: selectInfo.startStr,\n          CHour: selectInfo.start.getHours(),\n          CIsDelete: false,\n          isChange: true,\n          start: selectInfo.start,\n          end: selectInfo.end\n        });\n        this.listEvent$.next(temp);\n      }\n    }\n    save() {\n      this.savedData(true).subscribe(res => {\n        this.backToList();\n      });\n    }\n    backToList() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this._location.back();\n    }\n    // Function to check if the cell is empty\n    isCellEmpty(day) {\n      const startHour = day.getHours();\n      let check = false;\n      this.listEvent$.value.some(event => {\n        // console.log(event);\n        if (event.CHour === startHour && new Date(event.start).getDate() === day.getDate() && new Date(event.start).getMonth() === day.getMonth() && new Date(event.start).getFullYear() === day.getFullYear() && !event.CIsDelete) check = true;\n      });\n      return check;\n    }\n    checkDisable(day) {\n      if (day.getTime() < new Date().getTime()) {\n        return true;\n      }\n      return false;\n    }\n    handleEventClick(clickInfo) {\n      let eventIndex = this.listEvent$.value.findIndex(x => typeof x.start === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime() && typeof x.end === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime());\n      if (eventIndex !== -1) {\n        this.listEvent$.value[eventIndex].CIsDelete = true;\n        this.listEvent$.value[eventIndex].isChange = true;\n        clickInfo.event.remove();\n      }\n    }\n    onCheck(e, segment) {\n      let eventIndex = this.listEvent$.value.findIndex(x => x.start.getFullYear() === segment.getFullYear() && x.start.getMonth() === segment.getMonth() && x.start.getDate() === segment.getDate() && x.CHour === segment.getHours());\n      // click empty checkbox\n      if (eventIndex === -1) {\n        // create new event\n        if (e.target.checked) {\n          let temp = [...this.listEvent$.value];\n          temp.push({\n            CBuildCaseId: this.buildCaseId,\n            CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\n            CHour: segment.getHours(),\n            CIsDelete: false,\n            isChange: true,\n            start: segment,\n            IsOld: false\n          });\n          this.listEvent$.next(temp);\n        }\n      } else {\n        // unchecked checkbox\n        if (e.target.checked) {\n          this.listEvent$.value[eventIndex].CIsDelete = false;\n          this.listEvent$.value[eventIndex].isChange = true;\n        } else {\n          this.listEvent$.value[eventIndex].CIsDelete = true;\n          this.listEvent$.value[eventIndex].isChange = true;\n        }\n      }\n    }\n    addNew(ref) {\n      if (this.handleDataWeek().dataFromCurrent.length > 0) {\n        this.dialogService.open(ref);\n      } else {\n        this.getPreOderSettingRes = this.handleDataWeek().dataFromPrevious;\n        const updatedEvents = [...this.listEvent$.value];\n        this.handleDataWeek().dataFromPrevious.forEach(e => {\n          updatedEvents.push({\n            CBuildCaseId: e.CBuildCaseId,\n            CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete,\n            isChange: true,\n            start: this.add_weeks(e.CDate, 1),\n            IsOld: false\n          });\n        });\n        this.listEvent$.next(updatedEvents);\n        this.changeDetector.markForCheck();\n      }\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    confirmCopy(ref) {\n      this.savedData(false).subscribe(() => this.onClose(ref));\n    }\n    handleDataWeek() {\n      let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1);\n      let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1);\n      let dataFromPrevious = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime() && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime());\n      let dataFromCurrent = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime() && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime());\n      return {\n        dataFromCurrent,\n        dataFromPrevious\n      };\n    }\n    handleShowButton() {\n      var result = new Date(this.viewDate);\n      result.setDate(result.getDate() + 7);\n      if (result.getTime() < Date.now()) {\n        return false;\n      }\n      if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\n        return false;\n      }\n      return true;\n    }\n    savedData(needToFilter) {\n      if (needToFilter) {\n        this.savePreOrderSetting = this.listEvent$.value.filter(x => x.isChange === true).map(e => {\n          this.listDate.push(new Date(e.CDate));\n          return {\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: e.CDate,\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete\n          };\n        });\n      } else {\n        this.handleDataWeek().dataFromCurrent.forEach(e => {\n          if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\n            this.savePreOrderSetting.push({\n              CBuildCaseID: parseInt(e.CBuildCaseId),\n              CDate: this.fromDateToString(this.get_date(e.CDate)),\n              CHour: e.CHour,\n              CIsDelete: e.CIsDelete\n            });\n          } else {\n            this.savePreOrderSetting.push({\n              CBuildCaseID: parseInt(e.CBuildCaseId),\n              CDate: this.fromDateToString(this.get_date(e.CDate)),\n              CHour: e.CHour,\n              CIsDelete: true\n            });\n          }\n        });\n        this.handleDataWeek().dataFromPrevious.forEach(e => {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete\n          });\n        });\n        this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\n          this.listDate.push(new Date(x.CDate));\n        });\n      }\n      if (this.savePreOrderSetting.length == 0) {\n        this.backToList();\n        return of();\n      }\n      let minDate = new Date(Math.min.apply(null, this.listDate));\n      let paramSave = {\n        buildCaseId: this.buildCaseId,\n        minDate: minDate\n      };\n      return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\n        body: {\n          CSavePreOrderSetting: this.savePreOrderSetting\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.messageService.showSucessMSG('Save Successfully!');\n          this.savePreOrderSetting = [];\n          this.listDate = [];\n          this.listEvent$.value.forEach(x => {\n            if (x.isChange) {\n              let id = document.getElementById(x.start.toISOString());\n              setTimeout(() => {\n                id?.click();\n              }, 200);\n            }\n          });\n          LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave));\n        }\n      }), concatMap(() => this.getPreOrderSetting()));\n    }\n    add_weeks(dt, n) {\n      return new Date(new Date(dt).setDate(new Date(dt).getDate() + n * 7));\n    }\n    sub_weeks(dt, n) {\n      return new Date(new Date(dt).setDate(new Date(dt).getDate() - n * 7));\n    }\n    get_date(dt) {\n      return new Date(new Date(dt).setDate(new Date(dt).getDate()));\n    }\n    fromDateToString(date) {\n      date = new Date(+date);\n      date.setTime(date.getTime() - date.getTimezoneOffset() * 60000);\n      let dateAsString = date.toISOString().substr(0, 19);\n      return dateAsString;\n    }\n    static {\n      this.ɵfac = function EditAvailableTimeSlotComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || EditAvailableTimeSlotComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PreOrderSettingService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.NbDialogService), i0.ɵɵdirectiveInject(i6.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: EditAvailableTimeSlotComponent,\n        selectors: [[\"app-edit-available-time-slot\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 33,\n        vars: 19,\n        consts: [[\"hourSegmentTemplate\", \"\"], [\"currentTimeMarkerTemplate\", \"\"], [\"dialogConfirm\", \"\"], [\"accent\", \"success\"], [1, \"w-full\", \"flex\", \"justify-end\", \"mb-4\"], [\"class\", \"text-white p-3 bg-[#169bd5] cursor-pointer rounded-md\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"text-left\", \"mb-4\"], [1, \"col-md-4\"], [1, \"btn-group\"], [\"mwlCalendarPreviousView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-left\"], [1, \"col-md-4\", \"text-center\"], [1, \"col-md-4\", \"text-right\"], [\"mwlCalendarNextView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-right\"], [3, \"hourDuration\", \"hourSegments\", \"viewDate\", \"locale\", \"dayStartHour\", \"dayEndHour\", \"hourSegmentTemplate\", \"currentTimeMarkerTemplate\", \"eventSnapSize\"], [1, \"row\"], [1, \"col-md-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-1\", 3, \"click\"], [1, \"text-white\", \"p-3\", \"bg-[#169bd5]\", \"cursor-pointer\", \"rounded-md\", 3, \"click\"], [1, \"hour-segment\", \"text-center\", \"border\", 2, \"height\", \"100%\"], [4, \"ngIf\"], [\"style\", \"height: 100%;\", 3, \"className\", 4, \"ngIf\"], [2, \"height\", \"100%\", 3, \"className\"], [\"type\", \"checkbox\", 1, \"align-middle\", 3, \"click\", \"id\", \"checked\", \"disabled\"], [2, \"display\", \"none\"], [1, \"px-4\"], [1, \"flex\", \"justify-center\", \"w-full\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n        template: function EditAvailableTimeSlotComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4);\n            i0.ɵɵtemplate(5, EditAvailableTimeSlotComponent_div_5_Template, 2, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n            i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelement(10, \"i\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 11)(12, \"h2\");\n            i0.ɵɵtext(13);\n            i0.ɵɵpipe(14, \"calendarDate\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 8)(17, \"div\", 13);\n            i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelement(18, \"i\", 14);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelement(19, \"mwl-calendar-week-view\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"nb-card-body\")(21, \"div\", 16)(22, \"div\", 17)(23, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_23_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.backToList());\n            });\n            i0.ɵɵtext(24, \"\\u53D6\\u6D88\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_25_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.save());\n            });\n            i0.ɵɵtext(26, \"\\u5132\\u5B58\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(27, EditAvailableTimeSlotComponent_ng_template_27_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(29, EditAvailableTimeSlotComponent_ng_template_29_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(31, EditAvailableTimeSlotComponent_ng_template_31_Template, 8, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            const hourSegmentTemplate_r10 = i0.ɵɵreference(28);\n            const currentTimeMarkerTemplate_r11 = i0.ɵɵreference(30);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.handleShowButton());\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"view\", ctx.view);\n            i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(14, 15, ctx.viewDate, ctx.view + \"ViewTitle\", \"zh\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"view\", ctx.view);\n            i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"hourDuration\", 60)(\"hourSegments\", 1)(\"viewDate\", ctx.viewDate)(\"locale\", ctx.locale)(\"dayStartHour\", 9)(\"dayEndHour\", 21)(\"hourSegmentTemplate\", hourSegmentTemplate_r10)(\"currentTimeMarkerTemplate\", currentTimeMarkerTemplate_r11)(\"eventSnapSize\", 0);\n          }\n        },\n        dependencies: [CommonModule, i4.NgIf, i4.AsyncPipe, i4.DatePipe, SharedModule, i5.NbCardComponent, i5.NbCardBodyComponent, i5.NbCardFooterComponent, i5.NbCardHeaderComponent, i7.BreadcrumbComponent, CalendarModule, i8.ɵCalendarPreviousViewDirective, i8.ɵCalendarNextViewDirective, i8.ɵCalendarDatePipe, i8.CalendarWeekViewComponent, DialogModule],\n        styles: [\".empty-cell[_ngcontent-%COMP%]{position:relative}.empty-cell[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);color:#00f;text-decoration:underline;cursor:pointer}input[type=checkbox][_ngcontent-%COMP%]{width:20px;height:20px;color:#fffc;box-shadow:none}input[type=checkbox][_ngcontent-%COMP%]:after{box-shadow:none;border:1px solid black}input[type=checkbox][_ngcontent-%COMP%]:before{color:transparent!important;border:2px solid black}input[type=checkbox][_ngcontent-%COMP%]:checked:before{color:#000!important}.bg-inherit[_ngcontent-%COMP%]{background-color:inherit!important}.bg-event[_ngcontent-%COMP%]{background-color:#89d0ff!important}\"],\n        changeDetection: 0\n      });\n    }\n  }\n  return EditAvailableTimeSlotComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}