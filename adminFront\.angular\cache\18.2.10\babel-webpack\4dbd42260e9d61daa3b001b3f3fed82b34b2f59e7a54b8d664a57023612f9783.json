{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiMaterialExportExcelMaterialListPost$Json } from '../fn/material/api-material-export-excel-material-list-post-json';\nimport { apiMaterialExportExcelMaterialListPost$Plain } from '../fn/material/api-material-export-excel-material-list-post-plain';\nimport { apiMaterialGetMaterialInvidualPost$Json } from '../fn/material/api-material-get-material-invidual-post-json';\nimport { apiMaterialGetMaterialInvidualPost$Plain } from '../fn/material/api-material-get-material-invidual-post-plain';\nimport { apiMaterialGetMaterialListPost$Json } from '../fn/material/api-material-get-material-list-post-json';\nimport { apiMaterialGetMaterialListPost$Plain } from '../fn/material/api-material-get-material-list-post-plain';\nimport { apiMaterialGetMaterialPlanPost$Json } from '../fn/material/api-material-get-material-plan-post-json';\nimport { apiMaterialGetMaterialPlanPost$Plain } from '../fn/material/api-material-get-material-plan-post-plain';\nimport { apiMaterialGetSaveMaterialPost$Json } from '../fn/material/api-material-get-save-material-post-json';\nimport { apiMaterialGetSaveMaterialPost$Plain } from '../fn/material/api-material-get-save-material-post-plain';\nimport { apiMaterialImportExcelMaterialListPost$Json } from '../fn/material/api-material-import-excel-material-list-post-json';\nimport { apiMaterialImportExcelMaterialListPost$Plain } from '../fn/material/api-material-import-excel-material-list-post-plain';\nimport { apiMaterialSaveMaterialAdminPost$Json } from '../fn/material/api-material-save-material-admin-post-json';\nimport { apiMaterialSaveMaterialAdminPost$Plain } from '../fn/material/api-material-save-material-admin-post-plain';\nimport { apiMaterialSaveMaterialPost$Json } from '../fn/material/api-material-save-material-post-json';\nimport { apiMaterialSaveMaterialPost$Plain } from '../fn/material/api-material-save-material-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class MaterialService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiMaterialGetMaterialPlanPost()` */\n  static {\n    this.ApiMaterialGetMaterialPlanPostPath = '/api/Material/GetMaterialPlan';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialPlanPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialPlanPost$Plain$Response(params, context) {\n    return apiMaterialGetMaterialPlanPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialPlanPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialPlanPost$Plain(params, context) {\n    return this.apiMaterialGetMaterialPlanPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialPlanPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialPlanPost$Json$Response(params, context) {\n    return apiMaterialGetMaterialPlanPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialPlanPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialPlanPost$Json(params, context) {\n    return this.apiMaterialGetMaterialPlanPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialGetMaterialInvidualPost()` */\n  static {\n    this.ApiMaterialGetMaterialInvidualPostPath = '/api/Material/GetMaterialInvidual';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialInvidualPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialInvidualPost$Plain$Response(params, context) {\n    return apiMaterialGetMaterialInvidualPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialInvidualPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialInvidualPost$Plain(params, context) {\n    return this.apiMaterialGetMaterialInvidualPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialInvidualPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialInvidualPost$Json$Response(params, context) {\n    return apiMaterialGetMaterialInvidualPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialInvidualPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialInvidualPost$Json(params, context) {\n    return this.apiMaterialGetMaterialInvidualPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialSaveMaterialPost()` */\n  static {\n    this.ApiMaterialSaveMaterialPostPath = '/api/Material/SaveMaterial';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialPost$Plain$Response(params, context) {\n    return apiMaterialSaveMaterialPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialPost$Plain(params, context) {\n    return this.apiMaterialSaveMaterialPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialPost$Json$Response(params, context) {\n    return apiMaterialSaveMaterialPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialPost$Json(params, context) {\n    return this.apiMaterialSaveMaterialPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialGetSaveMaterialPost()` */\n  static {\n    this.ApiMaterialGetSaveMaterialPostPath = '/api/Material/GetSaveMaterial';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetSaveMaterialPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialGetSaveMaterialPost$Plain$Response(params, context) {\n    return apiMaterialGetSaveMaterialPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetSaveMaterialPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialGetSaveMaterialPost$Plain(params, context) {\n    return this.apiMaterialGetSaveMaterialPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetSaveMaterialPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialGetSaveMaterialPost$Json$Response(params, context) {\n    return apiMaterialGetSaveMaterialPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetSaveMaterialPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialGetSaveMaterialPost$Json(params, context) {\n    return this.apiMaterialGetSaveMaterialPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialGetMaterialListPost()` */\n  static {\n    this.ApiMaterialGetMaterialListPostPath = '/api/Material/GetMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialGetMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Plain(params, context) {\n    return this.apiMaterialGetMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Json$Response(params, context) {\n    return apiMaterialGetMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialGetMaterialListPost$Json(params, context) {\n    return this.apiMaterialGetMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialSaveMaterialAdminPost()` */\n  static {\n    this.ApiMaterialSaveMaterialAdminPostPath = '/api/Material/SaveMaterialAdmin';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Plain$Response(params, context) {\n    return apiMaterialSaveMaterialAdminPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Plain(params, context) {\n    return this.apiMaterialSaveMaterialAdminPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Json$Response(params, context) {\n    return apiMaterialSaveMaterialAdminPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiMaterialSaveMaterialAdminPost$Json(params, context) {\n    return this.apiMaterialSaveMaterialAdminPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialExportExcelMaterialListPost()` */\n  static {\n    this.ApiMaterialExportExcelMaterialListPostPath = '/api/Material/ExportExcelMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialExportExcelMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialExportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialExportExcelMaterialListPost$Plain(params, context) {\n    return this.apiMaterialExportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialExportExcelMaterialListPost$Json$Response(params, context) {\n    return apiMaterialExportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiMaterialExportExcelMaterialListPost$Json(params, context) {\n    return this.apiMaterialExportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiMaterialImportExcelMaterialListPost()` */\n  static {\n    this.ApiMaterialImportExcelMaterialListPostPath = '/api/Material/ImportExcelMaterialList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Plain$Response(params, context) {\n    return apiMaterialImportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Plain(params, context) {\n    return this.apiMaterialImportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Json$Response(params, context) {\n    return apiMaterialImportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiMaterialImportExcelMaterialListPost$Json(params, context) {\n    return this.apiMaterialImportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function MaterialService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MaterialService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MaterialService,\n      factory: MaterialService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiMaterialExportExcelMaterialListPost$Json", "apiMaterialExportExcelMaterialListPost$Plain", "apiMaterialGetMaterialInvidualPost$Json", "apiMaterialGetMaterialInvidualPost$Plain", "apiMaterialGetMaterialListPost$Json", "apiMaterialGetMaterialListPost$Plain", "apiMaterialGetMaterialPlanPost$Json", "apiMaterialGetMaterialPlanPost$Plain", "apiMaterialGetSaveMaterialPost$Json", "apiMaterialGetSaveMaterialPost$Plain", "apiMaterialImportExcelMaterialListPost$Json", "apiMaterialImportExcelMaterialListPost$Plain", "apiMaterialSaveMaterialAdminPost$Json", "apiMaterialSaveMaterialAdminPost$Plain", "apiMaterialSaveMaterialPost$Json", "apiMaterialSaveMaterialPost$Plain", "MaterialService", "constructor", "config", "http", "ApiMaterialGetMaterialPlanPostPath", "apiMaterialGetMaterialPlanPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiMaterialGetMaterialPlanPost$Json$Response", "ApiMaterialGetMaterialInvidualPostPath", "apiMaterialGetMaterialInvidualPost$Plain$Response", "apiMaterialGetMaterialInvidualPost$Json$Response", "ApiMaterialSaveMaterialPostPath", "apiMaterialSaveMaterialPost$Plain$Response", "apiMaterialSaveMaterialPost$Json$Response", "ApiMaterialGetSaveMaterialPostPath", "apiMaterialGetSaveMaterialPost$Plain$Response", "apiMaterialGetSaveMaterialPost$Json$Response", "ApiMaterialGetMaterialListPostPath", "apiMaterialGetMaterialListPost$Plain$Response", "apiMaterialGetMaterialListPost$Json$Response", "ApiMaterialSaveMaterialAdminPostPath", "apiMaterialSaveMaterialAdminPost$Plain$Response", "apiMaterialSaveMaterialAdminPost$Json$Response", "ApiMaterialExportExcelMaterialListPostPath", "apiMaterialExportExcelMaterialListPost$Plain$Response", "apiMaterialExportExcelMaterialListPost$Json$Response", "ApiMaterialImportExcelMaterialListPostPath", "apiMaterialImportExcelMaterialListPost$Plain$Response", "apiMaterialImportExcelMaterialListPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\material.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiMaterialExportExcelMaterialListPost$Json } from '../fn/material/api-material-export-excel-material-list-post-json';\r\nimport { ApiMaterialExportExcelMaterialListPost$Json$Params } from '../fn/material/api-material-export-excel-material-list-post-json';\r\nimport { apiMaterialExportExcelMaterialListPost$Plain } from '../fn/material/api-material-export-excel-material-list-post-plain';\r\nimport { ApiMaterialExportExcelMaterialListPost$Plain$Params } from '../fn/material/api-material-export-excel-material-list-post-plain';\r\nimport { apiMaterialGetMaterialInvidualPost$Json } from '../fn/material/api-material-get-material-invidual-post-json';\r\nimport { ApiMaterialGetMaterialInvidualPost$Json$Params } from '../fn/material/api-material-get-material-invidual-post-json';\r\nimport { apiMaterialGetMaterialInvidualPost$Plain } from '../fn/material/api-material-get-material-invidual-post-plain';\r\nimport { ApiMaterialGetMaterialInvidualPost$Plain$Params } from '../fn/material/api-material-get-material-invidual-post-plain';\r\nimport { apiMaterialGetMaterialListPost$Json } from '../fn/material/api-material-get-material-list-post-json';\r\nimport { ApiMaterialGetMaterialListPost$Json$Params } from '../fn/material/api-material-get-material-list-post-json';\r\nimport { apiMaterialGetMaterialListPost$Plain } from '../fn/material/api-material-get-material-list-post-plain';\r\nimport { ApiMaterialGetMaterialListPost$Plain$Params } from '../fn/material/api-material-get-material-list-post-plain';\r\nimport { apiMaterialGetMaterialPlanPost$Json } from '../fn/material/api-material-get-material-plan-post-json';\r\nimport { ApiMaterialGetMaterialPlanPost$Json$Params } from '../fn/material/api-material-get-material-plan-post-json';\r\nimport { apiMaterialGetMaterialPlanPost$Plain } from '../fn/material/api-material-get-material-plan-post-plain';\r\nimport { ApiMaterialGetMaterialPlanPost$Plain$Params } from '../fn/material/api-material-get-material-plan-post-plain';\r\nimport { apiMaterialGetSaveMaterialPost$Json } from '../fn/material/api-material-get-save-material-post-json';\r\nimport { ApiMaterialGetSaveMaterialPost$Json$Params } from '../fn/material/api-material-get-save-material-post-json';\r\nimport { apiMaterialGetSaveMaterialPost$Plain } from '../fn/material/api-material-get-save-material-post-plain';\r\nimport { ApiMaterialGetSaveMaterialPost$Plain$Params } from '../fn/material/api-material-get-save-material-post-plain';\r\nimport { apiMaterialImportExcelMaterialListPost$Json } from '../fn/material/api-material-import-excel-material-list-post-json';\r\nimport { ApiMaterialImportExcelMaterialListPost$Json$Params } from '../fn/material/api-material-import-excel-material-list-post-json';\r\nimport { apiMaterialImportExcelMaterialListPost$Plain } from '../fn/material/api-material-import-excel-material-list-post-plain';\r\nimport { ApiMaterialImportExcelMaterialListPost$Plain$Params } from '../fn/material/api-material-import-excel-material-list-post-plain';\r\nimport { apiMaterialSaveMaterialAdminPost$Json } from '../fn/material/api-material-save-material-admin-post-json';\r\nimport { ApiMaterialSaveMaterialAdminPost$Json$Params } from '../fn/material/api-material-save-material-admin-post-json';\r\nimport { apiMaterialSaveMaterialAdminPost$Plain } from '../fn/material/api-material-save-material-admin-post-plain';\r\nimport { ApiMaterialSaveMaterialAdminPost$Plain$Params } from '../fn/material/api-material-save-material-admin-post-plain';\r\nimport { apiMaterialSaveMaterialPost$Json } from '../fn/material/api-material-save-material-post-json';\r\nimport { ApiMaterialSaveMaterialPost$Json$Params } from '../fn/material/api-material-save-material-post-json';\r\nimport { apiMaterialSaveMaterialPost$Plain } from '../fn/material/api-material-save-material-post-plain';\r\nimport { ApiMaterialSaveMaterialPost$Plain$Params } from '../fn/material/api-material-save-material-post-plain';\r\nimport { ExportExcelMaterialsResponseBase } from '../models/export-excel-materials-response-base';\r\nimport { GetSaveMaterialResponseListResponseBase } from '../models/get-save-material-response-list-response-base';\r\nimport { MaterialPlanResponseListResponseBase } from '../models/material-plan-response-list-response-base';\r\nimport { SelectMaterialInvidualListResponseBase } from '../models/select-material-invidual-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\nimport { TblMaterialsListResponseBase } from '../models/tbl-materials-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class MaterialService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialGetMaterialPlanPost()` */\r\n  static readonly ApiMaterialGetMaterialPlanPostPath = '/api/Material/GetMaterialPlan';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialPlanPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialPlanPost$Plain$Response(params?: ApiMaterialGetMaterialPlanPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<MaterialPlanResponseListResponseBase>> {\r\n    return apiMaterialGetMaterialPlanPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialPlanPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialPlanPost$Plain(params?: ApiMaterialGetMaterialPlanPost$Plain$Params, context?: HttpContext): Observable<MaterialPlanResponseListResponseBase> {\r\n    return this.apiMaterialGetMaterialPlanPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<MaterialPlanResponseListResponseBase>): MaterialPlanResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialPlanPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialPlanPost$Json$Response(params?: ApiMaterialGetMaterialPlanPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<MaterialPlanResponseListResponseBase>> {\r\n    return apiMaterialGetMaterialPlanPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialPlanPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialPlanPost$Json(params?: ApiMaterialGetMaterialPlanPost$Json$Params, context?: HttpContext): Observable<MaterialPlanResponseListResponseBase> {\r\n    return this.apiMaterialGetMaterialPlanPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<MaterialPlanResponseListResponseBase>): MaterialPlanResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialGetMaterialInvidualPost()` */\r\n  static readonly ApiMaterialGetMaterialInvidualPostPath = '/api/Material/GetMaterialInvidual';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialInvidualPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialInvidualPost$Plain$Response(params?: ApiMaterialGetMaterialInvidualPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SelectMaterialInvidualListResponseBase>> {\r\n    return apiMaterialGetMaterialInvidualPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialInvidualPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialInvidualPost$Plain(params?: ApiMaterialGetMaterialInvidualPost$Plain$Params, context?: HttpContext): Observable<SelectMaterialInvidualListResponseBase> {\r\n    return this.apiMaterialGetMaterialInvidualPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SelectMaterialInvidualListResponseBase>): SelectMaterialInvidualListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialInvidualPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialInvidualPost$Json$Response(params?: ApiMaterialGetMaterialInvidualPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SelectMaterialInvidualListResponseBase>> {\r\n    return apiMaterialGetMaterialInvidualPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialInvidualPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialInvidualPost$Json(params?: ApiMaterialGetMaterialInvidualPost$Json$Params, context?: HttpContext): Observable<SelectMaterialInvidualListResponseBase> {\r\n    return this.apiMaterialGetMaterialInvidualPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SelectMaterialInvidualListResponseBase>): SelectMaterialInvidualListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialSaveMaterialPost()` */\r\n  static readonly ApiMaterialSaveMaterialPostPath = '/api/Material/SaveMaterial';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialPost$Plain$Response(params?: ApiMaterialSaveMaterialPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialPost$Plain(params?: ApiMaterialSaveMaterialPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialPost$Json$Response(params?: ApiMaterialSaveMaterialPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialPost$Json(params?: ApiMaterialSaveMaterialPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialGetSaveMaterialPost()` */\r\n  static readonly ApiMaterialGetSaveMaterialPostPath = '/api/Material/GetSaveMaterial';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetSaveMaterialPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialGetSaveMaterialPost$Plain$Response(params?: ApiMaterialGetSaveMaterialPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSaveMaterialResponseListResponseBase>> {\r\n    return apiMaterialGetSaveMaterialPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetSaveMaterialPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialGetSaveMaterialPost$Plain(params?: ApiMaterialGetSaveMaterialPost$Plain$Params, context?: HttpContext): Observable<GetSaveMaterialResponseListResponseBase> {\r\n    return this.apiMaterialGetSaveMaterialPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSaveMaterialResponseListResponseBase>): GetSaveMaterialResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetSaveMaterialPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialGetSaveMaterialPost$Json$Response(params?: ApiMaterialGetSaveMaterialPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSaveMaterialResponseListResponseBase>> {\r\n    return apiMaterialGetSaveMaterialPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetSaveMaterialPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialGetSaveMaterialPost$Json(params?: ApiMaterialGetSaveMaterialPost$Json$Params, context?: HttpContext): Observable<GetSaveMaterialResponseListResponseBase> {\r\n    return this.apiMaterialGetSaveMaterialPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetSaveMaterialResponseListResponseBase>): GetSaveMaterialResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialGetMaterialListPost()` */\r\n  static readonly ApiMaterialGetMaterialListPostPath = '/api/Material/GetMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Plain$Response(params?: ApiMaterialGetMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblMaterialsListResponseBase>> {\r\n    return apiMaterialGetMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Plain(params?: ApiMaterialGetMaterialListPost$Plain$Params, context?: HttpContext): Observable<TblMaterialsListResponseBase> {\r\n    return this.apiMaterialGetMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblMaterialsListResponseBase>): TblMaterialsListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialGetMaterialListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Json$Response(params?: ApiMaterialGetMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblMaterialsListResponseBase>> {\r\n    return apiMaterialGetMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialGetMaterialListPost$Json(params?: ApiMaterialGetMaterialListPost$Json$Params, context?: HttpContext): Observable<TblMaterialsListResponseBase> {\r\n    return this.apiMaterialGetMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblMaterialsListResponseBase>): TblMaterialsListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialSaveMaterialAdminPost()` */\r\n  static readonly ApiMaterialSaveMaterialAdminPostPath = '/api/Material/SaveMaterialAdmin';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Plain$Response(params?: ApiMaterialSaveMaterialAdminPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialAdminPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Plain(params?: ApiMaterialSaveMaterialAdminPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialAdminPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Json$Response(params?: ApiMaterialSaveMaterialAdminPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialSaveMaterialAdminPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiMaterialSaveMaterialAdminPost$Json(params?: ApiMaterialSaveMaterialAdminPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialSaveMaterialAdminPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialExportExcelMaterialListPost()` */\r\n  static readonly ApiMaterialExportExcelMaterialListPostPath = '/api/Material/ExportExcelMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Plain$Response(params?: ApiMaterialExportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Plain(params?: ApiMaterialExportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Json$Response(params?: ApiMaterialExportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n    return apiMaterialExportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiMaterialExportExcelMaterialListPost$Json(params?: ApiMaterialExportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<ExportExcelMaterialsResponseBase> {\r\n    return this.apiMaterialExportExcelMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelMaterialsResponseBase>): ExportExcelMaterialsResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiMaterialImportExcelMaterialListPost()` */\r\n  static readonly ApiMaterialImportExcelMaterialListPostPath = '/api/Material/ImportExcelMaterialList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Plain$Response(params?: ApiMaterialImportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialImportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Plain(params?: ApiMaterialImportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialImportExcelMaterialListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Json$Response(params?: ApiMaterialImportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiMaterialImportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiMaterialImportExcelMaterialListPost$Json(params?: ApiMaterialImportExcelMaterialListPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiMaterialImportExcelMaterialListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,2CAA2C,QAAQ,kEAAkE;AAE9H,SAASC,4CAA4C,QAAQ,mEAAmE;AAEhI,SAASC,uCAAuC,QAAQ,6DAA6D;AAErH,SAASC,wCAAwC,QAAQ,8DAA8D;AAEvH,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,oCAAoC,QAAQ,0DAA0D;AAE/G,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,oCAAoC,QAAQ,0DAA0D;AAE/G,SAASC,mCAAmC,QAAQ,yDAAyD;AAE7G,SAASC,oCAAoC,QAAQ,0DAA0D;AAE/G,SAASC,2CAA2C,QAAQ,kEAAkE;AAE9H,SAASC,4CAA4C,QAAQ,mEAAmE;AAEhI,SAASC,qCAAqC,QAAQ,2DAA2D;AAEjH,SAASC,sCAAsC,QAAQ,4DAA4D;AAEnH,SAASC,gCAAgC,QAAQ,qDAAqD;AAEtG,SAASC,iCAAiC,QAAQ,sDAAsD;;;;AAUxG,OAAM,MAAOC,eAAgB,SAAQjB,WAAW;EAC9CkB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACC,MAAoD,EAAEC,OAAqB;IACvH,OAAOhB,oCAAoC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAhB,oCAAoCA,CAACe,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACF,6CAA6C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E3B,GAAG,CAAE4B,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMAC,4CAA4CA,CAACN,MAAmD,EAAEC,OAAqB;IACrH,OAAOjB,mCAAmC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAjB,mCAAmCA,CAACgB,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACK,4CAA4C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E3B,GAAG,CAAE4B,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;IACgB,KAAAE,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAACR,MAAwD,EAAEC,OAAqB;IAC/H,OAAOpB,wCAAwC,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMApB,wCAAwCA,CAACmB,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACO,iDAAiD,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF3B,GAAG,CAAE4B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;;;;;EAMAI,gDAAgDA,CAACT,MAAuD,EAAEC,OAAqB;IAC7H,OAAOrB,uCAAuC,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMArB,uCAAuCA,CAACoB,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACQ,gDAAgD,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF3B,GAAG,CAAE4B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;IACgB,KAAAK,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACX,MAAiD,EAAEC,OAAqB;IACjH,OAAOR,iCAAiC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAR,iCAAiCA,CAACO,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACU,0CAA0C,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,yCAAyCA,CAACZ,MAAgD,EAAEC,OAAqB;IAC/G,OAAOT,gCAAgC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAT,gCAAgCA,CAACQ,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACW,yCAAyC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACd,MAAoD,EAAEC,OAAqB;IACvH,OAAOd,oCAAoC,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAd,oCAAoCA,CAACa,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACa,6CAA6C,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E3B,GAAG,CAAE4B,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAU,4CAA4CA,CAACf,MAAmD,EAAEC,OAAqB;IACrH,OAAOf,mCAAmC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAf,mCAAmCA,CAACc,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACc,4CAA4C,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E3B,GAAG,CAAE4B,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;IACgB,KAAAW,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACjB,MAAoD,EAAEC,OAAqB;IACvH,OAAOlB,oCAAoC,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAlB,oCAAoCA,CAACiB,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACgB,6CAA6C,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E3B,GAAG,CAAE4B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAa,4CAA4CA,CAAClB,MAAmD,EAAEC,OAAqB;IACrH,OAAOnB,mCAAmC,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAnB,mCAAmCA,CAACkB,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACiB,4CAA4C,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E3B,GAAG,CAAE4B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAc,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACpB,MAAsD,EAAEC,OAAqB;IAC3H,OAAOV,sCAAsC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAV,sCAAsCA,CAACS,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACmB,+CAA+C,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAgB,8CAA8CA,CAACrB,MAAqD,EAAEC,OAAqB;IACzH,OAAOX,qCAAqC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAX,qCAAqCA,CAACU,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACoB,8CAA8C,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAiB,0CAA0C,GAAG,uCAAuC;EAAC;EAErG;;;;;;EAMAC,qDAAqDA,CAACvB,MAA4D,EAAEC,OAAqB;IACvI,OAAOtB,4CAA4C,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAtB,4CAA4CA,CAACqB,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACsB,qDAAqD,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrF3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAmB,oDAAoDA,CAACxB,MAA2D,EAAEC,OAAqB;IACrI,OAAOvB,2CAA2C,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAvB,2CAA2CA,CAACsB,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACuB,oDAAoD,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpF3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAoB,0CAA0C,GAAG,uCAAuC;EAAC;EAErG;;;;;;EAMAC,qDAAqDA,CAAC1B,MAA4D,EAAEC,OAAqB;IACvI,OAAOZ,4CAA4C,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAZ,4CAA4CA,CAACW,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACyB,qDAAqD,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAsB,oDAAoDA,CAAC3B,MAA2D,EAAEC,OAAqB;IACrI,OAAOb,2CAA2C,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAb,2CAA2CA,CAACY,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAAC0B,oDAAoD,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA3XWX,eAAe,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfvC,eAAe;MAAAwC,OAAA,EAAfxC,eAAe,CAAAyC,IAAA;MAAAC,UAAA,EADF;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}