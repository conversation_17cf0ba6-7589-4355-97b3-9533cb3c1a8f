{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumStatus, EnumStatusHelper } from 'src/app/shared/enum/enumStatus';\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\"; // 示意圖片\n})(PictureCategory || (PictureCategory = {}));\nlet BuildingMaterialComponent = class BuildingMaterialComponent extends BaseComponent {\n  // 強制轉換狀態值為數字\n  onStatusChange(value) {\n    console.log('狀態變更:', value, typeof value);\n    this.selectedMaterial.CStatus = typeof value === 'string' ? parseInt(value) : value;\n    console.log('轉換後的狀態:', this.selectedMaterial.CStatus, typeof this.selectedMaterial.CStatus);\n  }\n  constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService, _pictureService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this._materialService = _materialService;\n    this._utilityService = _utilityService;\n    this._pictureService = _pictureService;\n    this.isNew = true;\n    this.listBuildCases = [];\n    this.materialOptions = [{\n      value: null,\n      label: '全部'\n    }, {\n      value: false,\n      label: '方案'\n    }, {\n      value: true,\n      label: '選樣'\n    }];\n    this.materialOptionsId = null;\n    this.CSelectName = \"\";\n    // 移除圖片檔名相關欄位\n    // CImageCode: string = \"\"\n    // CInfoImageCode: string = \"\"\n    // 啟用建材代號欄位\n    this.CImageCode = \"\";\n    this.ShowPrice = false;\n    this.currentImageShowing = \"\";\n    this.filterMapping = false;\n    this.CIsMapping = true;\n    // 圖片綁定相關屬性\n    this.availableImages = [];\n    this.filteredImages = [];\n    this.selectedImages = [];\n    this.imageSearchTerm = \"\";\n    this.previewingImage = null;\n    this.currentPreviewIndex = 0;\n    // 圖片綁定篩選選項\n    this.filterBindingOption = \"all\"; // 預設顯示全部圖片\n    this.filterBindingOptions = [{\n      value: \"all\",\n      label: \"顯示全部\"\n    }, {\n      value: \"bound\",\n      label: \"僅顯示已綁定\"\n    }];\n    // 圖片綁定分頁屬性\n    this.imageCurrentPage = 1;\n    this.imagePageSize = 50;\n    this.imageTotalRecords = 0;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    this.selectedCategory = PictureCategory.BUILDING_MATERIAL;\n    this.isCategorySelected = true; // 預設選擇建材圖片\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory; // 狀態相關屬性\n    this.EnumStatus = EnumStatus;\n    this.EnumStatusHelper = EnumStatusHelper;\n    this.statusOptions = [{\n      value: EnumStatus.Enable,\n      label: '啟用'\n    }, {\n      value: EnumStatus.Disable,\n      label: '停用'\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n    // 調試狀態選項\n    console.log('狀態選項:', this.statusOptions);\n    console.log('EnumStatus.Enable:', EnumStatus.Enable);\n    console.log('EnumStatus.Disable:', EnumStatus.Disable);\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries?.length ? res.Entries : [];\n        this.selectedBuildCaseId = this.listBuildCases[0].cID;\n      }\n    }), mergeMap(() => this.getMaterialList())).subscribe();\n  }\n  getMaterialList(pageIndex = 1) {\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPlanUse: this.materialOptionsId,\n        CSelectName: this.CSelectName,\n        // 啟用建材代號查詢條件\n        CImageCode: this.CImageCode,\n        // CInfoImageCode: this.CInfoImageCode,\n        PageSize: this.pageSize,\n        PageIndex: pageIndex,\n        CIsMapping: this.CIsMapping\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.materialList = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n        if (this.materialList.length > 0) {\n          this.ShowPrice = this.materialList[0].CShowPrice;\n        }\n      }\n    }));\n  }\n  search() {\n    this.getMaterialList().subscribe();\n  }\n  pageChanged(pageIndex) {\n    this.getMaterialList(pageIndex).subscribe();\n  }\n  exportExelMaterialList() {\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  exportExelMaterialTemplate() {\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\n      body: this.selectedBuildCaseId\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries.FileByte) {\n          this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n        }\n      }\n    });\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.selectedMaterial = {\n      CStatus: EnumStatus.Enable // 預設狀態為啟用 (數字 1)\n    };\n    console.log('新增時的預設狀態:', this.selectedMaterial.CStatus);\n    this.dialogService.open(ref);\n  }\n  onSelectedMaterial(data, ref) {\n    this.isNew = false;\n    this.selectedMaterial = {\n      ...data\n    };\n    // 確保 CStatus 是正確的數字類型\n    if (this.selectedMaterial.CStatus === undefined || this.selectedMaterial.CStatus === null) {\n      this.selectedMaterial.CStatus = EnumStatus.Enable;\n    }\n    console.log('編輯時的狀態:', this.selectedMaterial.CStatus, typeof this.selectedMaterial.CStatus);\n    this.dialogService.open(ref);\n  }\n  bindImageForMaterial(data, ref) {\n    this.selectedMaterial = {\n      ...data\n    };\n    this.loadImages();\n    // 清空當前選擇的圖片，避免舊數據干擾\n    this.clearAllSelection();\n    // 重設篩選選項為顯示全部\n    this.filterBindingOption = \"all\";\n    this.imageSearchTerm = \"\";\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[名稱]', this.selectedMaterial.CName);\n    this.valid.required('[項目]', this.selectedMaterial.CPart);\n    this.valid.required('[位置]', this.selectedMaterial.CLocation);\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus);\n    // 啟用建材代號驗證\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode);\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n    // 啟用建材代號長度驗證\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30);\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        // 暫時保留 CImageCode 給圖片綁定功能使用\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  detectFileExcel(event) {\n    const target = event.target;\n    const reader = new FileReader();\n    reader.readAsBinaryString(target.files[0]);\n    reader.onload = e => {\n      const binarystr = e.target.result;\n      const wb = XLSX.read(binarystr, {\n        type: 'binary'\n      });\n      const wsname = wb.SheetNames[0];\n      const ws = wb.Sheets[wsname];\n      let isValidFile = true;\n      const data = XLSX.utils.sheet_to_json(ws);\n      if (data && data.length > 0) {\n        data.forEach(x => {\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n            isValidFile = false;\n          }\n        });\n        if (!isValidFile) {\n          this.message.showErrorMSG(\"导入文件时出现错误\");\n        } else {\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n            body: {\n              CBuildCaseId: this.selectedBuildCaseId,\n              CFile: target.files[0]\n            }\n          }).pipe(tap(res => {\n            if (res.StatusCode == 0) {\n              this.message.showSucessMSG(\"執行成功\");\n            } else {\n              this.message.showErrorMSG(res.Message);\n            }\n          }), mergeMap(() => this.getMaterialList(1))).subscribe();\n        }\n      } else {\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\");\n      }\n      event.target.value = null;\n    };\n  }\n  showImage(imageUrl, dialog) {\n    this.currentImageShowing = imageUrl;\n    this.dialogService.open(dialog);\n  }\n  changeFilter() {\n    if (this.filterMapping) {\n      this.CIsMapping = false;\n      this.getMaterialList().subscribe();\n    } else {\n      this.CIsMapping = true;\n      this.getMaterialList().subscribe();\n    }\n  }\n  // 圖片綁定功能方法\n  openImageBinder(ref) {\n    this.loadImages();\n    // 重設篩選選項為顯示全部\n    this.filterBindingOption = \"all\";\n    this.imageSearchTerm = \"\";\n    this.dialogService.open(ref, {\n      closeOnBackdropClick: false\n    });\n  }\n  loadImages() {\n    // 使用 PictureService API 載入圖片列表\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\n      this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory,\n          PageIndex: this.imageCurrentPage,\n          // 使用圖片當前頁碼\n          PageSize: this.imagePageSize // 使用圖片每頁筆數\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          // 將 API 回應轉換為 ImageItem 格式\n          this.availableImages = res.Entries?.map(picture => ({\n            id: picture.CId || 0,\n            // 修復類型錯誤，直接使用數字\n            name: picture.CPictureCode || picture.CName || '',\n            size: 0,\n            // API 中沒有檔案大小資訊\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\n          })) || [];\n          this.filteredImages = [...this.availableImages];\n          this.imageTotalRecords = res.TotalItems || 0; // 更新圖片總筆數\n          // 如果有已綁定的圖片，自動選中\n          if (this.selectedMaterial && this.selectedMaterial.CSelectPictureId && this.selectedMaterial.CSelectPictureId.length > 0) {\n            // 將已綁定的圖片ID轉換為數字陣列\n            const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\n            // 篩選出已綁定的圖片項目\n            const boundImages = this.availableImages.filter(image => boundImageIds.includes(image.id));\n            // 將已綁定的圖片設為選中狀態\n            this.selectedImages = [...boundImages];\n          }\n        } else {\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\n          this.availableImages = [];\n          this.filteredImages = [];\n        }\n      });\n    } else {\n      // 如果沒有選擇類別或建案，清空圖片列表\n      this.availableImages = [];\n      this.filteredImages = [];\n    }\n  }\n  filterImages() {\n    // 首先根據搜尋詞進行篩選\n    let filtered = [...this.availableImages];\n    if (this.imageSearchTerm.trim()) {\n      const searchTerm = this.imageSearchTerm.toLowerCase();\n      filtered = filtered.filter(image => image.name.toLowerCase().includes(searchTerm));\n    }\n    // 然後根據綁定狀態進行篩選\n    if (this.filterBindingOption === \"bound\" && this.selectedMaterial && this.selectedMaterial.CSelectPictureId) {\n      // 轉換已綁定的圖片ID為數字陣列\n      const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\n      // 只顯示已綁定的圖片\n      filtered = filtered.filter(image => boundImageIds.includes(image.id));\n    }\n    this.filteredImages = filtered;\n  }\n  toggleImageSelection(image) {\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\n    if (index > -1) {\n      this.selectedImages.splice(index, 1);\n    } else {\n      this.selectedImages.push(image);\n    }\n  }\n  isImageSelected(image) {\n    return this.selectedImages.some(selected => selected.id === image.id);\n  }\n  selectAllImages() {\n    this.selectedImages = [...this.filteredImages];\n  }\n  clearAllSelection() {\n    this.selectedImages = [];\n  }\n  previewImage(image, imagePreviewRef, event) {\n    event.stopPropagation();\n    this.previewingImage = image;\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\n    this.dialogService.open(imagePreviewRef);\n  }\n  previousImage() {\n    if (this.currentPreviewIndex > 0) {\n      this.currentPreviewIndex--;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  nextImage() {\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\n      this.currentPreviewIndex++;\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\n    }\n  }\n  toggleImageSelectionInPreview() {\n    if (this.previewingImage) {\n      this.toggleImageSelection(this.previewingImage);\n    }\n  }\n  onConfirmImageSelection(ref) {\n    if (this.selectedImages.length > 0) {\n      // 收集選中圖片的 ID\n      const selectedImageIds = this.selectedImages.map(img => img.id); // 如果只選取一張圖片，直接設定圖片 ID\n      if (this.selectedImages.length === 1) {\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      } else {\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\n      }\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\n      this.selectedMaterial.selectedImageIds = selectedImageIds;\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\n      if (this.selectedMaterial.CId) {\n        this.saveImageBinding();\n      }\n    }\n    this.clearAllSelection();\n    ref.close();\n  }\n  // 新增方法：保存圖片綁定\n  saveImageBinding() {\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CImageCode: this.selectedMaterial.CImageCode,\n        CName: this.selectedMaterial.CName,\n        CPart: this.selectedMaterial.CPart,\n        CLocation: this.selectedMaterial.CLocation,\n        CSelectName: this.selectedMaterial.CSelectName,\n        CDescription: this.selectedMaterial.CDescription,\n        CMaterialId: this.selectedMaterial.CId,\n        CPrice: this.selectedMaterial.CPrice,\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\n        CPictureId: this.selectedMaterial.selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(`圖片綁定成功`);\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), mergeMap(() => this.getMaterialList()), finalize(() => {\n      // 清空選取的建材\n      this.selectedMaterial = {};\n    })).subscribe();\n  }\n  onCloseImageBinder(ref) {\n    this.clearAllSelection();\n    this.imageSearchTerm = \"\";\n    this.filterBindingOption = \"all\"; // 重設篩選選項為顯示全部\n    this.imageCurrentPage = 1; // 重設圖片頁碼\n    ref.close();\n  } // 類別變更處理方法\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true;\n    // 當類別變更時重設頁碼並重新載入圖片\n    this.imageCurrentPage = 1;\n    if (this.selectedBuildCaseId) {\n      this.loadImages();\n    }\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  // 圖片分頁變更處理方法\n  imagePageChanged(page) {\n    this.imageCurrentPage = page;\n    this.loadImages();\n  }\n};\nBuildingMaterialComponent = __decorate([Component({\n  selector: 'ngx-building-material',\n  templateUrl: './building-material.component.html',\n  styleUrls: ['./building-material.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], BuildingMaterialComponent);\nexport { BuildingMaterialComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "finalize", "mergeMap", "tap", "XLSX", "SharedModule", "BaseComponent", "EnumStatus", "EnumStatusHelper", "PictureCategory", "BuildingMaterialComponent", "onStatusChange", "value", "console", "log", "selectedMaterial", "CStatus", "parseInt", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "_materialService", "_utilityService", "_pictureService", "isNew", "listBuildCases", "materialOptions", "label", "materialOptionsId", "CSelectName", "CImageCode", "ShowPrice", "currentImageShowing", "filterMapping", "CIsMapping", "availableImages", "filteredImages", "selectedImages", "imageSearchTerm", "previewingImage", "currentPreviewIndex", "filterBindingOption", "filterBindingOptions", "imageCurrentPage", "imagePageSize", "imageTotalRecords", "categoryOptions", "BUILDING_MATERIAL", "SCHEMATIC", "selectedCate<PERSON><PERSON>", "isCategorySelected", "statusOptions", "Enable", "Disable", "ngOnInit", "getListBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "body", "CIsPagi", "pipe", "res", "StatusCode", "Entries", "length", "selectedBuildCaseId", "cID", "getMaterialList", "subscribe", "pageIndex", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPlanUse", "PageSize", "pageSize", "PageIndex", "materialList", "totalRecords", "TotalItems", "CShowPrice", "search", "pageChanged", "exportExelMaterialList", "apiMaterialExportExcelMaterialListPost$Json", "FileByte", "downloadExcelFile", "exportExelMaterialTemplate", "apiMaterialExportExcelMaterialTemplatePost$Json", "addNew", "ref", "open", "onSelectedMaterial", "data", "undefined", "bindImageForMaterial", "loadImages", "clearAllSelection", "closeOnBackdropClick", "validation", "clear", "required", "CName", "<PERSON>art", "CLocation", "isStringMaxLength", "onSubmit", "errorMessages", "showErrorMSGs", "apiMaterialSaveMaterialAdminPost$Json", "CDescription", "CMaterialId", "CId", "CPrice", "CPictureId", "selectedImageIds", "showSucessMSG", "showErrorMSG", "Message", "close", "onClose", "detectFileExcel", "event", "target", "reader", "FileReader", "readAsBinaryString", "files", "onload", "e", "binarystr", "result", "wb", "read", "type", "wsname", "SheetNames", "ws", "Sheets", "isValidFile", "utils", "sheet_to_json", "for<PERSON>ach", "x", "apiMaterialImportExcelMaterialListPost$Json", "CFile", "showImage", "imageUrl", "dialog", "changeFilter", "openImageBinder", "apiPictureGetPicturelListPost$Json", "cPictureType", "map", "picture", "id", "name", "CPictureCode", "size", "thumbnailUrl", "CBase64", "fullUrl", "lastModified", "CUpdateDT", "Date", "CSelectPictureId", "boundImageIds", "boundImages", "filter", "image", "includes", "filterImages", "filtered", "trim", "searchTerm", "toLowerCase", "toggleImageSelection", "index", "findIndex", "selected", "splice", "push", "isImageSelected", "some", "selectAllImages", "previewImage", "imagePreviewRef", "stopPropagation", "img", "previousImage", "nextImage", "toggleImageSelectionInPreview", "onConfirmImageSelection", "imageNames", "join", "saveImageBinding", "onCloseImageBinder", "categoryChanged", "category", "getCategoryLabel", "option", "find", "opt", "imagePageChanged", "page", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\building-material\\building-material.component.ts"], "sourcesContent": ["import { Component, OnInit, TemplateRef, } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, MaterialService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';\r\nimport { finalize, mergeMap, tap } from 'rxjs';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport * as XLSX from 'xlsx';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumStatus, EnumStatusHelper } from 'src/app/shared/enum/enumStatus';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2       // 示意圖片\r\n}\r\n\r\n// 圖片項目介面\r\ninterface ImageItem {\r\n  id: number;\r\n  name: string;\r\n  size: number;\r\n  thumbnailUrl?: string;\r\n  fullUrl?: string;\r\n  lastModified?: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-building-material',\r\n  templateUrl: './building-material.component.html',\r\n  styleUrls: ['./building-material.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class BuildingMaterialComponent extends BaseComponent implements OnInit {\r\n  isNew = true  materialList: GetMaterialListResponse[]\r\n  selectedMaterial: GetMaterialListResponse // 恢復原來的類型定義\r\n\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n\r\n  materialOptions = [\r\n    {\r\n      value: null,\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: false,\r\n      label: '方案',\r\n    },\r\n    {\r\n      value: true,\r\n      label: '選樣',\r\n    }]; materialOptionsId = null;\r\n  CSelectName: string = \"\"\r\n  // 移除圖片檔名相關欄位\r\n  // CImageCode: string = \"\"\r\n  // CInfoImageCode: string = \"\"\r\n  // 啟用建材代號欄位\r\n  CImageCode: string = \"\"\r\n  ShowPrice: boolean = false\r\n  currentImageShowing: string = \"\"\r\n  filterMapping: boolean = false\r\n  CIsMapping: boolean = true\r\n  // 圖片綁定相關屬性\r\n  availableImages: ImageItem[] = []\r\n  filteredImages: ImageItem[] = []\r\n  selectedImages: ImageItem[] = []\r\n  imageSearchTerm: string = \"\"\r\n  previewingImage: ImageItem | null = null\r\n  currentPreviewIndex: number = 0\r\n\r\n  // 圖片綁定篩選選項\r\n  filterBindingOption: string = \"all\" // 預設顯示全部圖片\r\n  filterBindingOptions = [\r\n    { value: \"all\", label: \"顯示全部\" },\r\n    { value: \"bound\", label: \"僅顯示已綁定\" }\r\n  ]\r\n\r\n  // 圖片綁定分頁屬性\r\n  imageCurrentPage: number = 1\r\n  imagePageSize: number = 50\r\n  imageTotalRecords: number = 0\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n  selectedCategory: PictureCategory = PictureCategory.BUILDING_MATERIAL\r\n  isCategorySelected: boolean = true // 預設選擇建材圖片\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;  // 狀態相關屬性\r\n  EnumStatus = EnumStatus;\r\n  EnumStatusHelper = EnumStatusHelper;\r\n  statusOptions = [\r\n    { value: EnumStatus.Enable, label: '啟用' },\r\n    { value: EnumStatus.Disable, label: '停用' }\r\n  ];\r\n\r\n  // 強制轉換狀態值為數字\r\n  onStatusChange(value: any) {\r\n    console.log('狀態變更:', value, typeof value)\r\n    this.selectedMaterial.CStatus = typeof value === 'string' ? parseInt(value) : value\r\n    console.log('轉換後的狀態:', this.selectedMaterial.CStatus, typeof this.selectedMaterial.CStatus)\r\n  }\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _materialService: MaterialService,\r\n    private _utilityService: UtilityService,\r\n    private _pictureService: PictureService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase()\r\n    // 調試狀態選項\r\n    console.log('狀態選項:', this.statusOptions)\r\n    console.log('EnumStatus.Enable:', EnumStatus.Enable)\r\n    console.log('EnumStatus.Disable:', EnumStatus.Disable)\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.listBuildCases = res.Entries?.length ? res.Entries : []\r\n            this.selectedBuildCaseId = this.listBuildCases[0].cID!\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList())\r\n      ).subscribe()\r\n  } getMaterialList(pageIndex: number = 1) {\r\n    return this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPlanUse: this.materialOptionsId,\r\n        CSelectName: this.CSelectName,\r\n        // 啟用建材代號查詢條件\r\n        CImageCode: this.CImageCode,\r\n        // CInfoImageCode: this.CInfoImageCode,\r\n        PageSize: this.pageSize,\r\n        PageIndex: pageIndex,\r\n        CIsMapping: this.CIsMapping\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.materialList = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n\r\n          if (this.materialList.length > 0) {\r\n            this.ShowPrice = this.materialList[0].CShowPrice!;\r\n          }\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  search() {\r\n    this.getMaterialList().subscribe()\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    this.getMaterialList(pageIndex).subscribe()\r\n  }\r\n\r\n  exportExelMaterialList() {\r\n    this._materialService.apiMaterialExportExcelMaterialListPost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  exportExelMaterialTemplate() {\r\n    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({\r\n      body: this.selectedBuildCaseId\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        if (res.Entries!.FileByte) {\r\n          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')\r\n        }\r\n      }\r\n    })\r\n  }  addNew(ref: any) {\r\n    this.isNew = true\r\n    this.selectedMaterial = {\r\n      CStatus: EnumStatus.Enable // 預設狀態為啟用 (數字 1)\r\n    }\r\n    console.log('新增時的預設狀態:', this.selectedMaterial.CStatus)\r\n    this.dialogService.open(ref)\r\n  }  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {\r\n    this.isNew = false\r\n    this.selectedMaterial = { ...data }\r\n    // 確保 CStatus 是正確的數字類型\r\n    if (this.selectedMaterial.CStatus === undefined || this.selectedMaterial.CStatus === null) {\r\n      this.selectedMaterial.CStatus = EnumStatus.Enable\r\n    }\r\n    console.log('編輯時的狀態:', this.selectedMaterial.CStatus, typeof this.selectedMaterial.CStatus)\r\n    this.dialogService.open(ref)\r\n  }\r\n  bindImageForMaterial(data: GetMaterialListResponse, ref: TemplateRef<any>) {\r\n    this.selectedMaterial = { ...data }\r\n    this.loadImages()\r\n    // 清空當前選擇的圖片，避免舊數據干擾\r\n    this.clearAllSelection()\r\n    // 重設篩選選項為顯示全部\r\n    this.filterBindingOption = \"all\"\r\n    this.imageSearchTerm = \"\"\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false })\r\n  } validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[名稱]', this.selectedMaterial.CName)\r\n    this.valid.required('[項目]', this.selectedMaterial.CPart)\r\n    this.valid.required('[位置]', this.selectedMaterial.CLocation)\r\n    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)\r\n    this.valid.required('[狀態]', this.selectedMaterial.CStatus)\r\n    // 啟用建材代號驗證\r\n    this.valid.required('[建材代號]', this.selectedMaterial.CImageCode)\r\n    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)\r\n    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)\r\n    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)\r\n    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)\r\n    // 啟用建材代號長度驗證\r\n    this.valid.isStringMaxLength('[建材代號]', this.selectedMaterial.CImageCode, 30)\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    } this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        // 暫時保留 CImageCode 給圖片綁定功能使用\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation, CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 使用暫存的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    })\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        }),\r\n        mergeMap(() => this.getMaterialList()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  detectFileExcel(event: any) {\r\n    const target: DataTransfer = <DataTransfer>(event.target);\r\n    const reader: FileReader = new FileReader();\r\n    reader.readAsBinaryString(target.files[0]);\r\n    reader.onload = (e: any) => {\r\n      const binarystr: string = e.target.result;\r\n      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });\r\n\r\n      const wsname: string = wb.SheetNames[0];\r\n      const ws: XLSX.WorkSheet = wb.Sheets[wsname];\r\n\r\n      let isValidFile: boolean = true;\r\n      const data = XLSX.utils.sheet_to_json(ws);\r\n      if (data && data.length > 0) {\r\n        data.forEach((x: any) => {\r\n          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&\r\n            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\r\n            isValidFile = false;\r\n          }\r\n        })\r\n\r\n        if (!isValidFile) {\r\n          this.message.showErrorMSG(\"导入文件时出现错误\")\r\n        } else {\r\n          this._materialService.apiMaterialImportExcelMaterialListPost$Json({\r\n            body: {\r\n              CBuildCaseId: this.selectedBuildCaseId,\r\n              CFile: target.files[0]\r\n            }\r\n          }).pipe(\r\n            tap(res => {\r\n              if (res.StatusCode == 0) {\r\n                this.message.showSucessMSG(\"執行成功\")\r\n              } else {\r\n                this.message.showErrorMSG(res.Message!)\r\n              }\r\n            }),\r\n            mergeMap(() => this.getMaterialList(1))\r\n          ).subscribe();\r\n        }\r\n      } else {\r\n        this.message.showErrorMSG(\"匯入的檔案內容為空，請檢查檔案並重新上傳。\")\r\n      }\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  showImage(imageUrl: string, dialog: TemplateRef<any>) {\r\n    this.currentImageShowing = imageUrl;\r\n    this.dialogService.open(dialog);\r\n  }\r\n  changeFilter() {\r\n    if (this.filterMapping) {\r\n      this.CIsMapping = false;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n    else {\r\n      this.CIsMapping = true;\r\n      this.getMaterialList().subscribe();\r\n    }\r\n  }\r\n  // 圖片綁定功能方法\r\n  openImageBinder(ref: TemplateRef<any>) {\r\n    this.loadImages();\r\n    // 重設篩選選項為顯示全部\r\n    this.filterBindingOption = \"all\";\r\n    this.imageSearchTerm = \"\";\r\n    this.dialogService.open(ref, { closeOnBackdropClick: false });\r\n  } loadImages() {\r\n    // 使用 PictureService API 載入圖片列表\r\n    if (this.isCategorySelected && this.selectedBuildCaseId) {\r\n      this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory,\r\n          PageIndex: this.imageCurrentPage, // 使用圖片當前頁碼\r\n          PageSize: this.imagePageSize // 使用圖片每頁筆數\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          // 將 API 回應轉換為 ImageItem 格式\r\n          this.availableImages = res.Entries?.map(picture => ({\r\n            id: picture.CId || 0, // 修復類型錯誤，直接使用數字\r\n            name: picture.CPictureCode || picture.CName || '',\r\n            size: 0, // API 中沒有檔案大小資訊\r\n            thumbnailUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            fullUrl: picture.CBase64 ? `data:image/jpeg;base64,${picture.CBase64}` : '',\r\n            lastModified: picture.CUpdateDT ? new Date(picture.CUpdateDT) : new Date()\r\n          })) || [];\r\n          this.filteredImages = [...this.availableImages];\r\n          this.imageTotalRecords = res.TotalItems || 0; // 更新圖片總筆數\r\n\r\n          // 如果有已綁定的圖片，自動選中\r\n          if (this.selectedMaterial && this.selectedMaterial.CSelectPictureId && this.selectedMaterial.CSelectPictureId.length > 0) {\r\n            // 將已綁定的圖片ID轉換為數字陣列\r\n            const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id => typeof id === 'string' ? parseInt(id) : id);\r\n\r\n            // 篩選出已綁定的圖片項目\r\n            const boundImages = this.availableImages.filter(image =>\r\n              boundImageIds.includes(image.id)\r\n            );\r\n\r\n            // 將已綁定的圖片設為選中狀態\r\n            this.selectedImages = [...boundImages];\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message || '載入圖片失敗');\r\n          this.availableImages = [];\r\n          this.filteredImages = [];\r\n        }\r\n      });\r\n    } else {\r\n      // 如果沒有選擇類別或建案，清空圖片列表\r\n      this.availableImages = [];\r\n      this.filteredImages = [];\r\n    }\r\n  }\r\n  filterImages() {\r\n    // 首先根據搜尋詞進行篩選\r\n    let filtered = [...this.availableImages];\r\n\r\n    if (this.imageSearchTerm.trim()) {\r\n      const searchTerm = this.imageSearchTerm.toLowerCase();\r\n      filtered = filtered.filter(image =>\r\n        image.name.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    // 然後根據綁定狀態進行篩選\r\n    if (this.filterBindingOption === \"bound\" && this.selectedMaterial && this.selectedMaterial.CSelectPictureId) {\r\n      // 轉換已綁定的圖片ID為數字陣列\r\n      const boundImageIds = this.selectedMaterial.CSelectPictureId.map(id =>\r\n        typeof id === 'string' ? parseInt(id) : id\r\n      );\r\n\r\n      // 只顯示已綁定的圖片\r\n      filtered = filtered.filter(image => boundImageIds.includes(image.id));\r\n    }\r\n\r\n    this.filteredImages = filtered;\r\n  }\r\n\r\n  toggleImageSelection(image: ImageItem) {\r\n    const index = this.selectedImages.findIndex(selected => selected.id === image.id);\r\n    if (index > -1) {\r\n      this.selectedImages.splice(index, 1);\r\n    } else {\r\n      this.selectedImages.push(image);\r\n    }\r\n  }\r\n\r\n  isImageSelected(image: ImageItem): boolean {\r\n    return this.selectedImages.some(selected => selected.id === image.id);\r\n  }\r\n\r\n  selectAllImages() {\r\n    this.selectedImages = [...this.filteredImages];\r\n  }\r\n\r\n  clearAllSelection() {\r\n    this.selectedImages = [];\r\n  } previewImage(image: ImageItem, imagePreviewRef: TemplateRef<any>, event: Event) {\r\n    event.stopPropagation();\r\n    this.previewingImage = image;\r\n    this.currentPreviewIndex = this.filteredImages.findIndex(img => img.id === image.id);\r\n    this.dialogService.open(imagePreviewRef);\r\n  }\r\n\r\n  previousImage() {\r\n    if (this.currentPreviewIndex > 0) {\r\n      this.currentPreviewIndex--;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  nextImage() {\r\n    if (this.currentPreviewIndex < this.filteredImages.length - 1) {\r\n      this.currentPreviewIndex++;\r\n      this.previewingImage = this.filteredImages[this.currentPreviewIndex];\r\n    }\r\n  }\r\n\r\n  toggleImageSelectionInPreview() {\r\n    if (this.previewingImage) {\r\n      this.toggleImageSelection(this.previewingImage);\r\n    }\r\n  }\r\n  onConfirmImageSelection(ref: any) {\r\n    if (this.selectedImages.length > 0) {\r\n      // 收集選中圖片的 ID\r\n      const selectedImageIds = this.selectedImages.map(img => img.id);      // 如果只選取一張圖片，直接設定圖片 ID\r\n      if (this.selectedImages.length === 1) {\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      } else {\r\n        // 多張圖片的話，可以設定第一張，或者讓使用者選擇主要圖片\r\n        const imageNames = this.selectedImages.map(img => img.name).join(', ');\r\n        this.message.showSucessMSG(`已選取 ${this.selectedImages.length} 張圖片: ${imageNames}`);\r\n        // 設定第一張圖片的 ID 到 CPictureId（單一數字）\r\n        this.selectedMaterial.CPictureId = this.selectedImages[0].id;\r\n      }\r\n\r\n      // 暫存所有選中的圖片 ID，供 API 呼叫使用\r\n      (this.selectedMaterial as any).selectedImageIds = selectedImageIds;\r\n\r\n      // 如果是從表格直接綁定圖片（有 CId），則直接保存到資料庫\r\n      if (this.selectedMaterial.CId) {\r\n        this.saveImageBinding();\r\n      }\r\n    }\r\n\r\n    this.clearAllSelection();\r\n    ref.close();\r\n  }\r\n  // 新增方法：保存圖片綁定\r\n  saveImageBinding() {\r\n    this._materialService.apiMaterialSaveMaterialAdminPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CImageCode: this.selectedMaterial.CImageCode,\r\n        CName: this.selectedMaterial.CName,\r\n        CPart: this.selectedMaterial.CPart,\r\n        CLocation: this.selectedMaterial.CLocation, CSelectName: this.selectedMaterial.CSelectName,\r\n        CDescription: this.selectedMaterial.CDescription,\r\n        CMaterialId: this.selectedMaterial.CId!,\r\n        CPrice: this.selectedMaterial.CPrice,\r\n        CStatus: this.selectedMaterial.CStatus || EnumStatus.Enable,\r\n        CPictureId: (this.selectedMaterial as any).selectedImageIds || [] // 選中的圖片 ID 陣列，預設為空陣列\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(`圖片綁定成功`);\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      }),\r\n      mergeMap(() => this.getMaterialList()),\r\n      finalize(() => {\r\n        // 清空選取的建材\r\n        this.selectedMaterial = {};\r\n      })\r\n    ).subscribe()\r\n  }\r\n  onCloseImageBinder(ref: any) {\r\n    this.clearAllSelection();\r\n    this.imageSearchTerm = \"\";\r\n    this.filterBindingOption = \"all\"; // 重設篩選選項為顯示全部\r\n    this.imageCurrentPage = 1; // 重設圖片頁碼\r\n    ref.close();\r\n  }// 類別變更處理方法\r\n  categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true;\r\n    // 當類別變更時重設頁碼並重新載入圖片\r\n    this.imageCurrentPage = 1;\r\n    if (this.selectedBuildCaseId) {\r\n      this.loadImages();\r\n    }\r\n  }\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n\r\n  // 圖片分頁變更處理方法\r\n  imagePageChanged(page: number) {\r\n    this.imageCurrentPage = page;\r\n    this.loadImages();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAA8B,eAAe;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAG9C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,UAAU,EAAEC,gBAAgB,QAAQ,gCAAgC;AAE7E;AACA,IAAKC,eAIJ;AAJD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa,EAAO;AACtB,CAAC,EAJIA,eAAe,KAAfA,eAAe;AAwBb,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQJ,aAAa;EAkE1D;EACAK,cAAcA,CAACC,KAAU;IACvBC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,KAAK,EAAE,OAAOA,KAAK,CAAC;IACzC,IAAI,CAACG,gBAAgB,CAACC,OAAO,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGK,QAAQ,CAACL,KAAK,CAAC,GAAGA,KAAK;IACnFC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAACC,OAAO,EAAE,OAAO,IAAI,CAACD,gBAAgB,CAACC,OAAO,CAAC;EAC7F;EAEAE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,eAA+B,EAC/BC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAhFzB,KAAAC,KAAK,GAAG,IAAI;IAGZ,KAAAC,cAAc,GAA8B,EAAE;IAG9C,KAAAC,eAAe,GAAG,CAChB;MACEjB,KAAK,EAAE,IAAI;MACXkB,KAAK,EAAE;KACR,EACD;MACElB,KAAK,EAAE,KAAK;MACZkB,KAAK,EAAE;KACR,EACD;MACElB,KAAK,EAAE,IAAI;MACXkB,KAAK,EAAE;KACR,CAAC;IAAE,KAAAC,iBAAiB,GAAG,IAAI;IAC9B,KAAAC,WAAW,GAAW,EAAE;IACxB;IACA;IACA;IACA;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,UAAU,GAAY,IAAI;IAC1B;IACA,KAAAC,eAAe,GAAgB,EAAE;IACjC,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,eAAe,GAAqB,IAAI;IACxC,KAAAC,mBAAmB,GAAW,CAAC;IAE/B;IACA,KAAAC,mBAAmB,GAAW,KAAK,EAAC;IACpC,KAAAC,oBAAoB,GAAG,CACrB;MAAEjC,KAAK,EAAE,KAAK;MAAEkB,KAAK,EAAE;IAAM,CAAE,EAC/B;MAAElB,KAAK,EAAE,OAAO;MAAEkB,KAAK,EAAE;IAAQ,CAAE,CACpC;IAED;IACA,KAAAgB,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,iBAAiB,GAAW,CAAC;IAE7B;IACA,KAAAC,eAAe,GAAG,CAChB;MAAErC,KAAK,EAAEH,eAAe,CAACyC,iBAAiB;MAAEpB,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAElB,KAAK,EAAEH,eAAe,CAAC0C,SAAS;MAAErB,KAAK,EAAE;IAAM,CAAE,CACpD;IACD,KAAAsB,gBAAgB,GAAoB3C,eAAe,CAACyC,iBAAiB;IACrE,KAAAG,kBAAkB,GAAY,IAAI,EAAC;IACnC;IACA,KAAA5C,eAAe,GAAGA,eAAe,CAAC,CAAE;IACpC,KAAAF,UAAU,GAAGA,UAAU;IACvB,KAAAC,gBAAgB,GAAGA,gBAAgB;IACnC,KAAA8C,aAAa,GAAG,CACd;MAAE1C,KAAK,EAAEL,UAAU,CAACgD,MAAM;MAAEzB,KAAK,EAAE;IAAI,CAAE,EACzC;MAAElB,KAAK,EAAEL,UAAU,CAACiD,OAAO;MAAE1B,KAAK,EAAE;IAAI,CAAE,CAC3C;EAoBD;EACS2B,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB;IACA7C,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACwC,aAAa,CAAC;IACxCzC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,UAAU,CAACgD,MAAM,CAAC;IACpD1C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEP,UAAU,CAACiD,OAAO,CAAC;EACxD;EAEAE,gBAAgBA,CAAA;IACd,IAAI,CAACnC,iBAAiB,CAACoC,6CAA6C,CAAC;MACnEC,IAAI,EAAE;QACJC,OAAO,EAAE,KAAK;QACd7C,OAAO,EAAE;;KAEZ,CAAC,CACC8C,IAAI,CACH3D,GAAG,CAAC4D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACpC,cAAc,GAAGmC,GAAG,CAACE,OAAO,EAAEC,MAAM,GAAGH,GAAG,CAACE,OAAO,GAAG,EAAE;QAC5D,IAAI,CAACE,mBAAmB,GAAG,IAAI,CAACvC,cAAc,CAAC,CAAC,CAAC,CAACwC,GAAI;MACxD;IACF,CAAC,CAAC,EACFlE,QAAQ,CAAC,MAAM,IAAI,CAACmE,eAAe,EAAE,CAAC,CACvC,CAACC,SAAS,EAAE;EACjB;EAAED,eAAeA,CAACE,SAAA,GAAoB,CAAC;IACrC,OAAO,IAAI,CAAC/C,gBAAgB,CAACgD,mCAAmC,CAAC;MAC/DZ,IAAI,EAAE;QACJa,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtCO,QAAQ,EAAE,IAAI,CAAC3C,iBAAiB;QAChCC,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B;QACAC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B;QACA0C,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,SAAS,EAAEN,SAAS;QACpBlC,UAAU,EAAE,IAAI,CAACA;;KAEpB,CAAC,CAACyB,IAAI,CACL3D,GAAG,CAAC4D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACc,YAAY,GAAGf,GAAG,CAACE,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QAEnC,IAAI,IAAI,CAACF,YAAY,CAACZ,MAAM,GAAG,CAAC,EAAE;UAChC,IAAI,CAAChC,SAAS,GAAG,IAAI,CAAC4C,YAAY,CAAC,CAAC,CAAC,CAACG,UAAW;QACnD;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACb,eAAe,EAAE,CAACC,SAAS,EAAE;EACpC;EAEAa,WAAWA,CAACZ,SAAiB;IAC3B,IAAI,CAACF,eAAe,CAACE,SAAS,CAAC,CAACD,SAAS,EAAE;EAC7C;EAEAc,sBAAsBA,CAAA;IACpB,IAAI,CAAC5D,gBAAgB,CAAC6D,2CAA2C,CAAC;MAChEzB,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACG,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE;UACzB,IAAI,CAAC7D,eAAe,CAAC8D,iBAAiB,CAACxB,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CAAA;IACxB,IAAI,CAAChE,gBAAgB,CAACiE,+CAA+C,CAAC;MACpE7B,IAAI,EAAE,IAAI,CAACO;KACZ,CAAC,CAACG,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE;UACzB,IAAI,CAAC7D,eAAe,CAAC8D,iBAAiB,CAACxB,GAAG,CAACE,OAAQ,CAACqB,QAAQ,EAAE,MAAM,CAAC;QACvE;MACF;IACF,CAAC,CAAC;EACJ;EAAGI,MAAMA,CAACC,GAAQ;IAChB,IAAI,CAAChE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACZ,gBAAgB,GAAG;MACtBC,OAAO,EAAET,UAAU,CAACgD,MAAM,CAAC;KAC5B;IACD1C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACC,gBAAgB,CAACC,OAAO,CAAC;IACvD,IAAI,CAACI,aAAa,CAACwE,IAAI,CAACD,GAAG,CAAC;EAC9B;EAAGE,kBAAkBA,CAACC,IAA6B,EAAEH,GAAQ;IAC3D,IAAI,CAAChE,KAAK,GAAG,KAAK;IAClB,IAAI,CAACZ,gBAAgB,GAAG;MAAE,GAAG+E;IAAI,CAAE;IACnC;IACA,IAAI,IAAI,CAAC/E,gBAAgB,CAACC,OAAO,KAAK+E,SAAS,IAAI,IAAI,CAAChF,gBAAgB,CAACC,OAAO,KAAK,IAAI,EAAE;MACzF,IAAI,CAACD,gBAAgB,CAACC,OAAO,GAAGT,UAAU,CAACgD,MAAM;IACnD;IACA1C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAACC,OAAO,EAAE,OAAO,IAAI,CAACD,gBAAgB,CAACC,OAAO,CAAC;IAC3F,IAAI,CAACI,aAAa,CAACwE,IAAI,CAACD,GAAG,CAAC;EAC9B;EACAK,oBAAoBA,CAACF,IAA6B,EAAEH,GAAqB;IACvE,IAAI,CAAC5E,gBAAgB,GAAG;MAAE,GAAG+E;IAAI,CAAE;IACnC,IAAI,CAACG,UAAU,EAAE;IACjB;IACA,IAAI,CAACC,iBAAiB,EAAE;IACxB;IACA,IAAI,CAACtD,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,eAAe,GAAG,EAAE;IACzB,IAAI,CAACrB,aAAa,CAACwE,IAAI,CAACD,GAAG,EAAE;MAAEQ,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC9E,KAAK,CAAC+E,KAAK,EAAE;IAClB,IAAI,CAAC/E,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvF,gBAAgB,CAACwF,KAAK,CAAC;IACxD,IAAI,CAACjF,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvF,gBAAgB,CAACyF,KAAK,CAAC;IACxD,IAAI,CAAClF,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvF,gBAAgB,CAAC0F,SAAS,CAAC;IAC5D,IAAI,CAACnF,KAAK,CAACgF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACvF,gBAAgB,CAACiB,WAAW,CAAC;IAClE,IAAI,CAACV,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACvF,gBAAgB,CAACC,OAAO,CAAC;IAC1D;IACA,IAAI,CAACM,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvF,gBAAgB,CAACkB,UAAU,CAAC;IAC/D,IAAI,CAACX,KAAK,CAACoF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC3F,gBAAgB,CAACwF,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAACjF,KAAK,CAACoF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC3F,gBAAgB,CAACyF,KAAK,EAAE,EAAE,CAAC;IACrE,IAAI,CAAClF,KAAK,CAACoF,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC3F,gBAAgB,CAAC0F,SAAS,EAAE,EAAE,CAAC;IACzE,IAAI,CAACnF,KAAK,CAACoF,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC3F,gBAAgB,CAACiB,WAAW,EAAE,EAAE,CAAC;IAC/E;IACA,IAAI,CAACV,KAAK,CAACoF,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC3F,gBAAgB,CAACkB,UAAU,EAAE,EAAE,CAAC;EAC9E;EAEA0E,QAAQA,CAAChB,GAAQ;IACf,IAAI,CAACS,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC9E,KAAK,CAACsF,aAAa,CAAC1C,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC7C,OAAO,CAACwF,aAAa,CAAC,IAAI,CAACvF,KAAK,CAACsF,aAAa,CAAC;MACpD;IACF;IAAE,IAAI,CAACpF,gBAAgB,CAACsF,qCAAqC,CAAC;MAC5DlD,IAAI,EAAE;QACJa,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtC;QACAlC,UAAU,EAAE,IAAI,CAAClB,gBAAgB,CAACkB,UAAU;QAC5CsE,KAAK,EAAE,IAAI,CAACxF,gBAAgB,CAACwF,KAAK;QAClCC,KAAK,EAAE,IAAI,CAACzF,gBAAgB,CAACyF,KAAK;QAClCC,SAAS,EAAE,IAAI,CAAC1F,gBAAgB,CAAC0F,SAAS;QAAEzE,WAAW,EAAE,IAAI,CAACjB,gBAAgB,CAACiB,WAAW;QAC1F+E,YAAY,EAAE,IAAI,CAAChG,gBAAgB,CAACgG,YAAY;QAChDC,WAAW,EAAE,IAAI,CAACrF,KAAK,GAAG,IAAI,GAAG,IAAI,CAACZ,gBAAgB,CAACkG,GAAI;QAC3DC,MAAM,EAAE,IAAI,CAACnG,gBAAgB,CAACmG,MAAM;QACpClG,OAAO,EAAE,IAAI,CAACD,gBAAgB,CAACC,OAAO,IAAIT,UAAU,CAACgD,MAAM;QAC3D4D,UAAU,EAAG,IAAI,CAACpG,gBAAwB,CAACqG,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CACCtD,IAAI,CACH3D,GAAG,CAAC4D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC3C,OAAO,CAACgG,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAChG,OAAO,CAACiG,YAAY,CAACvD,GAAG,CAACwD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFrH,QAAQ,CAAC,MAAM,IAAI,CAACmE,eAAe,EAAE,CAAC,EACtCpE,QAAQ,CAAC,MAAM0F,GAAG,CAAC6B,KAAK,EAAE,CAAC,CAC5B,CAAClD,SAAS,EAAE;EACjB;EAEAmD,OAAOA,CAAC9B,GAAQ;IACdA,GAAG,CAAC6B,KAAK,EAAE;EACb;EAEAE,eAAeA,CAACC,KAAU;IACxB,MAAMC,MAAM,GAAgCD,KAAK,CAACC,MAAO;IACzD,MAAMC,MAAM,GAAe,IAAIC,UAAU,EAAE;IAC3CD,MAAM,CAACE,kBAAkB,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACI,MAAM,GAAIC,CAAM,IAAI;MACzB,MAAMC,SAAS,GAAWD,CAAC,CAACN,MAAM,CAACQ,MAAM;MACzC,MAAMC,EAAE,GAAkBjI,IAAI,CAACkI,IAAI,CAACH,SAAS,EAAE;QAAEI,IAAI,EAAE;MAAQ,CAAE,CAAC;MAElE,MAAMC,MAAM,GAAWH,EAAE,CAACI,UAAU,CAAC,CAAC,CAAC;MACvC,MAAMC,EAAE,GAAmBL,EAAE,CAACM,MAAM,CAACH,MAAM,CAAC;MAE5C,IAAII,WAAW,GAAY,IAAI;MAC/B,MAAM9C,IAAI,GAAG1F,IAAI,CAACyI,KAAK,CAACC,aAAa,CAACJ,EAAE,CAAC;MACzC,IAAI5C,IAAI,IAAIA,IAAI,CAAC5B,MAAM,GAAG,CAAC,EAAE;QAC3B4B,IAAI,CAACiD,OAAO,CAAEC,CAAM,IAAI;UACtB,IAAI,EAAEA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,IAAI,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,KAC/CA,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,MAAM,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5CJ,WAAW,GAAG,KAAK;UACrB;QACF,CAAC,CAAC;QAEF,IAAI,CAACA,WAAW,EAAE;UAChB,IAAI,CAACvH,OAAO,CAACiG,YAAY,CAAC,WAAW,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAAC9F,gBAAgB,CAACyH,2CAA2C,CAAC;YAChErF,IAAI,EAAE;cACJa,YAAY,EAAE,IAAI,CAACN,mBAAmB;cACtC+E,KAAK,EAAEtB,MAAM,CAACI,KAAK,CAAC,CAAC;;WAExB,CAAC,CAAClE,IAAI,CACL3D,GAAG,CAAC4D,GAAG,IAAG;YACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;cACvB,IAAI,CAAC3C,OAAO,CAACgG,aAAa,CAAC,MAAM,CAAC;YACpC,CAAC,MAAM;cACL,IAAI,CAAChG,OAAO,CAACiG,YAAY,CAACvD,GAAG,CAACwD,OAAQ,CAAC;YACzC;UACF,CAAC,CAAC,EACFrH,QAAQ,CAAC,MAAM,IAAI,CAACmE,eAAe,CAAC,CAAC,CAAC,CAAC,CACxC,CAACC,SAAS,EAAE;QACf;MACF,CAAC,MAAM;QACL,IAAI,CAACjD,OAAO,CAACiG,YAAY,CAAC,uBAAuB,CAAC;MACpD;MACAK,KAAK,CAACC,MAAM,CAAChH,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAuI,SAASA,CAACC,QAAgB,EAAEC,MAAwB;IAClD,IAAI,CAAClH,mBAAmB,GAAGiH,QAAQ;IACnC,IAAI,CAAChI,aAAa,CAACwE,IAAI,CAACyD,MAAM,CAAC;EACjC;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClH,aAAa,EAAE;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACgC,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC,CAAC,MACI;MACH,IAAI,CAACjC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACgC,eAAe,EAAE,CAACC,SAAS,EAAE;IACpC;EACF;EACA;EACAiF,eAAeA,CAAC5D,GAAqB;IACnC,IAAI,CAACM,UAAU,EAAE;IACjB;IACA,IAAI,CAACrD,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACH,eAAe,GAAG,EAAE;IACzB,IAAI,CAACrB,aAAa,CAACwE,IAAI,CAACD,GAAG,EAAE;MAAEQ,oBAAoB,EAAE;IAAK,CAAE,CAAC;EAC/D;EAAEF,UAAUA,CAAA;IACV;IACA,IAAI,IAAI,CAAC5C,kBAAkB,IAAI,IAAI,CAACc,mBAAmB,EAAE;MACvD,IAAI,CAACzC,eAAe,CAAC8H,kCAAkC,CAAC;QACtD5F,IAAI,EAAE;UACJa,YAAY,EAAE,IAAI,CAACN,mBAAmB;UACtCsF,YAAY,EAAE,IAAI,CAACrG,gBAAgB;UACnCyB,SAAS,EAAE,IAAI,CAAC/B,gBAAgB;UAAE;UAClC6B,QAAQ,EAAE,IAAI,CAAC5B,aAAa,CAAC;;OAEhC,CAAC,CAACuB,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB;UACA,IAAI,CAAC1B,eAAe,GAAGyB,GAAG,CAACE,OAAO,EAAEyF,GAAG,CAACC,OAAO,KAAK;YAClDC,EAAE,EAAED,OAAO,CAAC1C,GAAG,IAAI,CAAC;YAAE;YACtB4C,IAAI,EAAEF,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACpD,KAAK,IAAI,EAAE;YACjDwD,IAAI,EAAE,CAAC;YAAE;YACTC,YAAY,EAAEL,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAChFC,OAAO,EAAEP,OAAO,CAACM,OAAO,GAAG,0BAA0BN,OAAO,CAACM,OAAO,EAAE,GAAG,EAAE;YAC3EE,YAAY,EAAER,OAAO,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,OAAO,CAACS,SAAS,CAAC,GAAG,IAAIC,IAAI;WACzE,CAAC,CAAC,IAAI,EAAE;UACT,IAAI,CAAC9H,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;UAC/C,IAAI,CAACU,iBAAiB,GAAGe,GAAG,CAACiB,UAAU,IAAI,CAAC,CAAC,CAAC;UAE9C;UACA,IAAI,IAAI,CAACjE,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACuJ,gBAAgB,IAAI,IAAI,CAACvJ,gBAAgB,CAACuJ,gBAAgB,CAACpG,MAAM,GAAG,CAAC,EAAE;YACxH;YACA,MAAMqG,aAAa,GAAG,IAAI,CAACxJ,gBAAgB,CAACuJ,gBAAgB,CAACZ,GAAG,CAACE,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,GAAG3I,QAAQ,CAAC2I,EAAE,CAAC,GAAGA,EAAE,CAAC;YAElH;YACA,MAAMY,WAAW,GAAG,IAAI,CAAClI,eAAe,CAACmI,MAAM,CAACC,KAAK,IACnDH,aAAa,CAACI,QAAQ,CAACD,KAAK,CAACd,EAAE,CAAC,CACjC;YAED;YACA,IAAI,CAACpH,cAAc,GAAG,CAAC,GAAGgI,WAAW,CAAC;UACxC;QACF,CAAC,MAAM;UACL,IAAI,CAACnJ,OAAO,CAACiG,YAAY,CAACvD,GAAG,CAACwD,OAAO,IAAI,QAAQ,CAAC;UAClD,IAAI,CAACjF,eAAe,GAAG,EAAE;UACzB,IAAI,CAACC,cAAc,GAAG,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACD,eAAe,GAAG,EAAE;MACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IAC1B;EACF;EACAqI,YAAYA,CAAA;IACV;IACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACvI,eAAe,CAAC;IAExC,IAAI,IAAI,CAACG,eAAe,CAACqI,IAAI,EAAE,EAAE;MAC/B,MAAMC,UAAU,GAAG,IAAI,CAACtI,eAAe,CAACuI,WAAW,EAAE;MACrDH,QAAQ,GAAGA,QAAQ,CAACJ,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAACb,IAAI,CAACmB,WAAW,EAAE,CAACL,QAAQ,CAACI,UAAU,CAAC,CAC9C;IACH;IAEA;IACA,IAAI,IAAI,CAACnI,mBAAmB,KAAK,OAAO,IAAI,IAAI,CAAC7B,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACuJ,gBAAgB,EAAE;MAC3G;MACA,MAAMC,aAAa,GAAG,IAAI,CAACxJ,gBAAgB,CAACuJ,gBAAgB,CAACZ,GAAG,CAACE,EAAE,IACjE,OAAOA,EAAE,KAAK,QAAQ,GAAG3I,QAAQ,CAAC2I,EAAE,CAAC,GAAGA,EAAE,CAC3C;MAED;MACAiB,QAAQ,GAAGA,QAAQ,CAACJ,MAAM,CAACC,KAAK,IAAIH,aAAa,CAACI,QAAQ,CAACD,KAAK,CAACd,EAAE,CAAC,CAAC;IACvE;IAEA,IAAI,CAACrH,cAAc,GAAGsI,QAAQ;EAChC;EAEAI,oBAAoBA,CAACP,KAAgB;IACnC,MAAMQ,KAAK,GAAG,IAAI,CAAC1I,cAAc,CAAC2I,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACxB,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;IACjF,IAAIsB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAAC1I,cAAc,CAAC6I,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAAC1I,cAAc,CAAC8I,IAAI,CAACZ,KAAK,CAAC;IACjC;EACF;EAEAa,eAAeA,CAACb,KAAgB;IAC9B,OAAO,IAAI,CAAClI,cAAc,CAACgJ,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACxB,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;EACvE;EAEA6B,eAAeA,CAAA;IACb,IAAI,CAACjJ,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,cAAc,CAAC;EAChD;EAEA2D,iBAAiBA,CAAA;IACf,IAAI,CAAC1D,cAAc,GAAG,EAAE;EAC1B;EAAEkJ,YAAYA,CAAChB,KAAgB,EAAEiB,eAAiC,EAAEhE,KAAY;IAC9EA,KAAK,CAACiE,eAAe,EAAE;IACvB,IAAI,CAAClJ,eAAe,GAAGgI,KAAK;IAC5B,IAAI,CAAC/H,mBAAmB,GAAG,IAAI,CAACJ,cAAc,CAAC4I,SAAS,CAACU,GAAG,IAAIA,GAAG,CAACjC,EAAE,KAAKc,KAAK,CAACd,EAAE,CAAC;IACpF,IAAI,CAACxI,aAAa,CAACwE,IAAI,CAAC+F,eAAe,CAAC;EAC1C;EAEAG,aAAaA,CAAA;IACX,IAAI,IAAI,CAACnJ,mBAAmB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACA,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACH,cAAc,CAAC,IAAI,CAACI,mBAAmB,CAAC;IACtE;EACF;EAEAoJ,SAASA,CAAA;IACP,IAAI,IAAI,CAACpJ,mBAAmB,GAAG,IAAI,CAACJ,cAAc,CAAC2B,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACvB,mBAAmB,EAAE;MAC1B,IAAI,CAACD,eAAe,GAAG,IAAI,CAACH,cAAc,CAAC,IAAI,CAACI,mBAAmB,CAAC;IACtE;EACF;EAEAqJ,6BAA6BA,CAAA;IAC3B,IAAI,IAAI,CAACtJ,eAAe,EAAE;MACxB,IAAI,CAACuI,oBAAoB,CAAC,IAAI,CAACvI,eAAe,CAAC;IACjD;EACF;EACAuJ,uBAAuBA,CAACtG,GAAQ;IAC9B,IAAI,IAAI,CAACnD,cAAc,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAClC;MACA,MAAMkD,gBAAgB,GAAG,IAAI,CAAC5E,cAAc,CAACkH,GAAG,CAACmC,GAAG,IAAIA,GAAG,CAACjC,EAAE,CAAC,CAAC,CAAM;MACtE,IAAI,IAAI,CAACpH,cAAc,CAAC0B,MAAM,KAAK,CAAC,EAAE;QACpC;QACA,IAAI,CAACnD,gBAAgB,CAACoG,UAAU,GAAG,IAAI,CAAC3E,cAAc,CAAC,CAAC,CAAC,CAACoH,EAAE;MAC9D,CAAC,MAAM;QACL;QACA,MAAMsC,UAAU,GAAG,IAAI,CAAC1J,cAAc,CAACkH,GAAG,CAACmC,GAAG,IAAIA,GAAG,CAAChC,IAAI,CAAC,CAACsC,IAAI,CAAC,IAAI,CAAC;QACtE,IAAI,CAAC9K,OAAO,CAACgG,aAAa,CAAC,OAAO,IAAI,CAAC7E,cAAc,CAAC0B,MAAM,SAASgI,UAAU,EAAE,CAAC;QAClF;QACA,IAAI,CAACnL,gBAAgB,CAACoG,UAAU,GAAG,IAAI,CAAC3E,cAAc,CAAC,CAAC,CAAC,CAACoH,EAAE;MAC9D;MAEA;MACC,IAAI,CAAC7I,gBAAwB,CAACqG,gBAAgB,GAAGA,gBAAgB;MAElE;MACA,IAAI,IAAI,CAACrG,gBAAgB,CAACkG,GAAG,EAAE;QAC7B,IAAI,CAACmF,gBAAgB,EAAE;MACzB;IACF;IAEA,IAAI,CAAClG,iBAAiB,EAAE;IACxBP,GAAG,CAAC6B,KAAK,EAAE;EACb;EACA;EACA4E,gBAAgBA,CAAA;IACd,IAAI,CAAC5K,gBAAgB,CAACsF,qCAAqC,CAAC;MAC1DlD,IAAI,EAAE;QACJa,YAAY,EAAE,IAAI,CAACN,mBAAmB;QACtClC,UAAU,EAAE,IAAI,CAAClB,gBAAgB,CAACkB,UAAU;QAC5CsE,KAAK,EAAE,IAAI,CAACxF,gBAAgB,CAACwF,KAAK;QAClCC,KAAK,EAAE,IAAI,CAACzF,gBAAgB,CAACyF,KAAK;QAClCC,SAAS,EAAE,IAAI,CAAC1F,gBAAgB,CAAC0F,SAAS;QAAEzE,WAAW,EAAE,IAAI,CAACjB,gBAAgB,CAACiB,WAAW;QAC1F+E,YAAY,EAAE,IAAI,CAAChG,gBAAgB,CAACgG,YAAY;QAChDC,WAAW,EAAE,IAAI,CAACjG,gBAAgB,CAACkG,GAAI;QACvCC,MAAM,EAAE,IAAI,CAACnG,gBAAgB,CAACmG,MAAM;QACpClG,OAAO,EAAE,IAAI,CAACD,gBAAgB,CAACC,OAAO,IAAIT,UAAU,CAACgD,MAAM;QAC3D4D,UAAU,EAAG,IAAI,CAACpG,gBAAwB,CAACqG,gBAAgB,IAAI,EAAE,CAAC;;KAErE,CAAC,CAACtD,IAAI,CACL3D,GAAG,CAAC4D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC3C,OAAO,CAACgG,aAAa,CAAC,QAAQ,CAAC;MACtC,CAAC,MAAM;QACL,IAAI,CAAChG,OAAO,CAACiG,YAAY,CAACvD,GAAG,CAACwD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFrH,QAAQ,CAAC,MAAM,IAAI,CAACmE,eAAe,EAAE,CAAC,EACtCpE,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACc,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC,CACH,CAACuD,SAAS,EAAE;EACf;EACA+H,kBAAkBA,CAAC1G,GAAQ;IACzB,IAAI,CAACO,iBAAiB,EAAE;IACxB,IAAI,CAACzD,eAAe,GAAG,EAAE;IACzB,IAAI,CAACG,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAClC,IAAI,CAACE,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC3B6C,GAAG,CAAC6B,KAAK,EAAE;EACb,CAAC;EACD8E,eAAeA,CAACC,QAAyB;IACvC,IAAI,CAACnJ,gBAAgB,GAAGmJ,QAAQ;IAChC,IAAI,CAAClJ,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACP,gBAAgB,GAAG,CAAC;IACzB,IAAI,IAAI,CAACqB,mBAAmB,EAAE;MAC5B,IAAI,CAAC8B,UAAU,EAAE;IACnB;EACF;EAEA;EACAuG,gBAAgBA,CAACD,QAAgB;IAC/B,MAAME,MAAM,GAAG,IAAI,CAACxJ,eAAe,CAACyJ,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/L,KAAK,KAAK2L,QAAQ,CAAC;IACvE,OAAOE,MAAM,GAAGA,MAAM,CAAC3K,KAAK,GAAG,MAAM;EACvC;EAEA;EACA8K,gBAAgBA,CAACC,IAAY;IAC3B,IAAI,CAAC/J,gBAAgB,GAAG+J,IAAI;IAC5B,IAAI,CAAC5G,UAAU,EAAE;EACnB;CACD;AAtgBYvF,yBAAyB,GAAAoM,UAAA,EARrC/M,SAAS,CAAC;EACTgN,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACnN,YAAY,EAAEK,YAAY;CACrC,CAAC,C,EAEWK,yBAAyB,CAsgBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}