{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bosnian [bs]\n//! author : <PERSON><PERSON> : https://github.com/frontyard\n//! based on (hr) translation by <PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function translate(number, withoutSuffix, key) {\n    var result = number + ' ';\n    switch (key) {\n      case 'ss':\n        if (number === 1) {\n          result += 'sekunda';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'sekunde';\n        } else {\n          result += 'sekundi';\n        }\n        return result;\n      case 'm':\n        return withoutSuffix ? 'jedna minuta' : 'jedne minute';\n      case 'mm':\n        if (number === 1) {\n          result += 'minuta';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'minute';\n        } else {\n          result += 'minuta';\n        }\n        return result;\n      case 'h':\n        return withoutSuffix ? 'jedan sat' : 'jednog sata';\n      case 'hh':\n        if (number === 1) {\n          result += 'sat';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'sata';\n        } else {\n          result += 'sati';\n        }\n        return result;\n      case 'dd':\n        if (number === 1) {\n          result += 'dan';\n        } else {\n          result += 'dana';\n        }\n        return result;\n      case 'MM':\n        if (number === 1) {\n          result += 'mjesec';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'mjeseca';\n        } else {\n          result += 'mjeseci';\n        }\n        return result;\n      case 'yy':\n        if (number === 1) {\n          result += 'godina';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'godine';\n        } else {\n          result += 'godina';\n        }\n        return result;\n    }\n  }\n  var bs = moment.defineLocale('bs', {\n    months: 'januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar'.split('_'),\n    monthsShort: 'jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota'.split('_'),\n    weekdaysShort: 'ned._pon._uto._sri._čet._pet._sub.'.split('_'),\n    weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[danas u] LT',\n      nextDay: '[sutra u] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[u] [nedjelju] [u] LT';\n          case 3:\n            return '[u] [srijedu] [u] LT';\n          case 6:\n            return '[u] [subotu] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[u] dddd [u] LT';\n        }\n      },\n      lastDay: '[jučer u] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n            return '[prošlu] dddd [u] LT';\n          case 6:\n            return '[prošle] [subote] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[prošli] dddd [u] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'prije %s',\n      s: 'par sekundi',\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: 'dan',\n      dd: translate,\n      M: 'mjesec',\n      MM: translate,\n      y: 'godinu',\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return bs;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}