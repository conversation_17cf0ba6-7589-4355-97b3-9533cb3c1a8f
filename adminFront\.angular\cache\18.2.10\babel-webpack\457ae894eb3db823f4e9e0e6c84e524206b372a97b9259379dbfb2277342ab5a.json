{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ImageCarouselComponent } from '../image-carousel/image-carousel.component';\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\nlet ImageGalleryComponent = class ImageGalleryComponent {\n  constructor() {\n    this.images = [];\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square';\n    this.containerClass = '';\n    this.currentIndex = 0;\n    this.isModalVisible = false;\n  }\n  ngOnInit() {\n    this.currentIndex = 0;\n  }\n  ngOnDestroy() {\n    // 確保模態關閉時移除body class\n    if (this.isModalVisible) {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  onImageClick(event) {\n    this.currentIndex = event.index;\n    this.openModal();\n  }\n  onCarouselIndexChange(index) {\n    this.currentIndex = index;\n  }\n  onModalIndexChange(index) {\n    this.currentIndex = index;\n  }\n  openModal() {\n    this.isModalVisible = true;\n    document.body.classList.add('modal-open');\n  }\n  closeModal() {\n    this.isModalVisible = false;\n    document.body.classList.remove('modal-open');\n  }\n};\n__decorate([Input()], ImageGalleryComponent.prototype, \"images\", void 0);\n__decorate([Input()], ImageGalleryComponent.prototype, \"showThumbnails\", void 0);\n__decorate([Input()], ImageGalleryComponent.prototype, \"showCounter\", void 0);\n__decorate([Input()], ImageGalleryComponent.prototype, \"showNavigation\", void 0);\n__decorate([Input()], ImageGalleryComponent.prototype, \"aspectRatio\", void 0);\n__decorate([Input()], ImageGalleryComponent.prototype, \"containerClass\", void 0);\nImageGalleryComponent = __decorate([Component({\n  selector: 'app-image-gallery',\n  templateUrl: './image-gallery.component.html',\n  styleUrls: ['./image-gallery.component.scss'],\n  standalone: true,\n  imports: [CommonModule, ImageCarouselComponent, ImageModalComponent]\n})], ImageGalleryComponent);\nexport { ImageGalleryComponent };", "map": {"version": 3, "names": ["Component", "Input", "CommonModule", "ImageCarouselComponent", "ImageModalComponent", "ImageGalleryComponent", "constructor", "images", "showThumbnails", "showCounter", "showNavigation", "aspectRatio", "containerClass", "currentIndex", "isModalVisible", "ngOnInit", "ngOnDestroy", "document", "body", "classList", "remove", "onImageClick", "event", "index", "openModal", "onCarouselIndexChange", "onModalIndexChange", "add", "closeModal", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-gallery\\image-gallery.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImageData } from '../image-carousel/image-carousel.component';\r\nimport { ImageCarouselComponent } from '../image-carousel/image-carousel.component';\r\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\r\n\r\n@Component({\r\n    selector: 'app-image-gallery',\r\n    templateUrl: './image-gallery.component.html',\r\n    styleUrls: ['./image-gallery.component.scss'],\r\n    standalone: true,\r\n    imports: [CommonModule, ImageCarouselComponent, ImageModalComponent]\r\n})\r\nexport class ImageGalleryComponent implements OnInit, OnDestroy {\r\n    @Input() images: ImageData[] = [];\r\n    @Input() showThumbnails: boolean = true;\r\n    @Input() showCounter: boolean = true;\r\n    @Input() showNavigation: boolean = true;\r\n    @Input() aspectRatio: string = 'aspect-square';\r\n    @Input() containerClass: string = '';\r\n\r\n    currentIndex: number = 0;\r\n    isModalVisible: boolean = false;\r\n\r\n    ngOnInit() {\r\n        this.currentIndex = 0;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        // 確保模態關閉時移除body class\r\n        if (this.isModalVisible) {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    onImageClick(event: { image: ImageData, index: number }): void {\r\n        this.currentIndex = event.index;\r\n        this.openModal();\r\n    }\r\n\r\n    onCarouselIndexChange(index: number): void {\r\n        this.currentIndex = index;\r\n    }\r\n\r\n    onModalIndexChange(index: number): void {\r\n        this.currentIndex = index;\r\n    }\r\n\r\n    openModal(): void {\r\n        this.isModalVisible = true;\r\n        document.body.classList.add('modal-open');\r\n    }\r\n\r\n    closeModal(): void {\r\n        this.isModalVisible = false;\r\n        document.body.classList.remove('modal-open');\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAA2B,eAAe;AACnE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,mBAAmB,QAAQ,sCAAsC;AASnE,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAA3BC,YAAA;IACM,KAAAC,MAAM,GAAgB,EAAE;IACxB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAW,eAAe;IACrC,KAAAC,cAAc,GAAW,EAAE;IAEpC,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,cAAc,GAAY,KAAK;EAmCnC;EAjCIC,QAAQA,CAAA;IACJ,IAAI,CAACF,YAAY,GAAG,CAAC;EACzB;EAEAG,WAAWA,CAAA;IACP;IACA,IAAI,IAAI,CAACF,cAAc,EAAE;MACrBG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAEAC,YAAYA,CAACC,KAA0C;IACnD,IAAI,CAACT,YAAY,GAAGS,KAAK,CAACC,KAAK;IAC/B,IAAI,CAACC,SAAS,EAAE;EACpB;EAEAC,qBAAqBA,CAACF,KAAa;IAC/B,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC7B;EAEAG,kBAAkBA,CAACH,KAAa;IAC5B,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC7B;EAEAC,SAASA,CAAA;IACL,IAAI,CAACV,cAAc,GAAG,IAAI;IAC1BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACQ,GAAG,CAAC,YAAY,CAAC;EAC7C;EAEAC,UAAUA,CAAA;IACN,IAAI,CAACd,cAAc,GAAG,KAAK;IAC3BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;EAChD;CACH;AA3CYS,UAAA,EAAR5B,KAAK,EAAE,C,oDAA0B;AACzB4B,UAAA,EAAR5B,KAAK,EAAE,C,4DAAgC;AAC/B4B,UAAA,EAAR5B,KAAK,EAAE,C,yDAA6B;AAC5B4B,UAAA,EAAR5B,KAAK,EAAE,C,4DAAgC;AAC/B4B,UAAA,EAAR5B,KAAK,EAAE,C,yDAAuC;AACtC4B,UAAA,EAAR5B,KAAK,EAAE,C,4DAA6B;AAN5BI,qBAAqB,GAAAwB,UAAA,EAPjC7B,SAAS,CAAC;EACP8B,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChC,YAAY,EAAEC,sBAAsB,EAAEC,mBAAmB;CACtE,CAAC,C,EACWC,qBAAqB,CA4CjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}