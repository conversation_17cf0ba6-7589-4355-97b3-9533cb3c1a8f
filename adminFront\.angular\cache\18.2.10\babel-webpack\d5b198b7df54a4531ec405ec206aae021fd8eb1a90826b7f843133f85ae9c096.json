{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PetternHelper {\n  constructor() {\n    this._AccountPettern = '^[a-zA-Z0-9]{3,20}$';\n    this._PasswordPettern = '^[a-zA-Z0-9]{3,20}$';\n    this._MailPettern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$';\n  }\n  get AccountPettern() {\n    return this._AccountPettern;\n  }\n  set AccountPettern(value) {\n    this._AccountPettern = value;\n  }\n  get PasswordPettern() {\n    return this._PasswordPettern;\n  }\n  set PasswordPettern(value) {\n    this._PasswordPettern = value;\n  }\n  get MailPettern() {\n    return this._MailPettern;\n  }\n  set MailPettern(value) {\n    this._MailPettern = value;\n  }\n  static {\n    this.ɵfac = function PetternHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PetternHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n      factory: PetternHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["PetternHelper", "constructor", "_Account<PERSON><PERSON><PERSON>", "_PasswordPettern", "_MailPettern", "Account<PERSON><PERSON><PERSON>", "value", "PasswordPettern", "MailPettern", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\helper\\petternHelper.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class PetternHelper {\r\n\r\n  constructor() {\r\n  }\r\n\r\n  private _AccountPettern = '^[a-zA-Z0-9]{3,20}$';\r\n  public get AccountPettern() {\r\n    return this._AccountPettern;\r\n  }\r\n  public set AccountPettern(value) {\r\n    this._AccountPettern = value;\r\n  }\r\n  private _PasswordPettern = '^[a-zA-Z0-9]{3,20}$';\r\n  public get PasswordPettern() {\r\n    return this._PasswordPettern;\r\n  }\r\n  public set PasswordPettern(value) {\r\n    this._PasswordPettern = value;\r\n  }\r\n  private _MailPettern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$';\r\n  public get MailPettern() {\r\n    return this._MailPettern;\r\n  }\r\n  public set MailPettern(value) {\r\n    this._MailPettern = value;\r\n  }\r\n}\r\n"], "mappings": ";AAGA,OAAM,MAAOA,aAAa;EAExBC,YAAA;IAGQ,KAAAC,eAAe,GAAG,qBAAqB;IAOvC,KAAAC,gBAAgB,GAAG,qBAAqB;IAOxC,KAAAC,YAAY,GAAG,mDAAmD;EAhB1E;EAGA,IAAWC,cAAcA,CAAA;IACvB,OAAO,IAAI,CAACH,eAAe;EAC7B;EACA,IAAWG,cAAcA,CAACC,KAAK;IAC7B,IAAI,CAACJ,eAAe,GAAGI,KAAK;EAC9B;EAEA,IAAWC,eAAeA,CAAA;IACxB,OAAO,IAAI,CAACJ,gBAAgB;EAC9B;EACA,IAAWI,eAAeA,CAACD,KAAK;IAC9B,IAAI,CAACH,gBAAgB,GAAGG,KAAK;EAC/B;EAEA,IAAWE,WAAWA,CAAA;IACpB,OAAO,IAAI,CAACJ,YAAY;EAC1B;EACA,IAAWI,WAAWA,CAACF,KAAK;IAC1B,IAAI,CAACF,YAAY,GAAGE,KAAK;EAC3B;;;uCAzBWN,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAS,OAAA,EAAbT,aAAa,CAAAU,IAAA;MAAAC,UAAA,EADA;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}