{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChild, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys, Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport * as i3 from 'primeng/focustrap';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport * as i5 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport * as i4 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nconst _c0 = [\"titlebar\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"footer\"];\nconst _c3 = [\"*\", [[\"p-header\"]], [[\"p-footer\"]]];\nconst _c4 = [\"*\", \"p-header\", \"p-footer\"];\nconst _c5 = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9) => ({\n  \"p-dialog-mask\": true,\n  \"p-component-overlay p-component-overlay-enter\": a0,\n  \"p-dialog-mask-scrollblocker\": a1,\n  \"p-dialog-left\": a2,\n  \"p-dialog-right\": a3,\n  \"p-dialog-top\": a4,\n  \"p-dialog-top-left\": a5,\n  \"p-dialog-top-right\": a6,\n  \"p-dialog-bottom\": a7,\n  \"p-dialog-bottom-left\": a8,\n  \"p-dialog-bottom-right\": a9\n});\nconst _c6 = (a0, a1, a2, a3) => ({\n  \"p-dialog p-component\": true,\n  \"p-dialog-rtl\": a0,\n  \"p-dialog-draggable\": a1,\n  \"p-dialog-resizable\": a2,\n  \"p-dialog-maximized\": a3\n});\nconst _c7 = (a0, a1) => ({\n  transform: a0,\n  transition: a1\n});\nconst _c8 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c9 = () => ({\n  \"p-dialog-header-icon p-dialog-header-maximize p-link\": true\n});\nconst _c10 = () => ({\n  \"p-dialog-header-icon p-dialog-header-close p-link\": true\n});\nconst _c11 = () => ({\n  \"min-width\": 0\n});\nfunction Dialog_div_0_div_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headlessTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_ng_template_3_div_0_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.initResize($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"id\", ctx_r1.ariaLabelledBy);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.header);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"id\", ctx_r1.ariaLabelledBy);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.maximized ? ctx_r1.minimizeIcon : ctx_r1.maximizeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_WindowMaximizeIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMaximizeIcon\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_WindowMinimizeIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"WindowMinimizeIcon\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-maximize-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_WindowMaximizeIcon_1_Template, 1, 1, \"WindowMaximizeIcon\", 26)(2, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_WindowMinimizeIcon_2_Template, 1, 1, \"WindowMinimizeIcon\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximized && !ctx_r1.maximizeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximized && !ctx_r1.minimizeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.maximizeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.minimizeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function Dialog_div_0_div_1_ng_template_3_div_1_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.maximize());\n    })(\"keydown.enter\", function Dialog_div_0_div_1_ng_template_3_div_1_button_6_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.maximize());\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_6_span_1_Template, 1, 1, \"span\", 23)(2, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_2_Template, 3, 2, \"ng-container\", 24)(3, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_3_Template, 2, 1, \"ng-container\", 24)(4, Dialog_div_0_div_1_ng_template_3_div_1_button_6_ng_container_4_Template, 2, 1, \"ng-container\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(7, _c9));\n    i0.ɵɵattribute(\"tabindex\", ctx_r1.maximizable ? \"0\" : \"-1\")(\"aria-label\", ctx_r1.maximizeLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximizeIcon && !ctx_r1.maximizeIconTemplate && !ctx_r1.minimizeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximizeIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.maximized);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximized);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_TimesIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-dialog-header-close-icon\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_span_1_Template, 1, 1, \"span\", 29)(2, Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_TimesIcon_2_Template, 1, 1, \"TimesIcon\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closeIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.closeIcon);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_1_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function Dialog_div_0_div_1_ng_template_3_div_1_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown.enter\", function Dialog_div_0_div_1_ng_template_3_div_1_button_7_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    });\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_ng_template_3_div_1_button_7_ng_container_1_Template, 3, 2, \"ng-container\", 24)(2, Dialog_div_0_div_1_ng_template_3_div_1_button_7_span_2_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(6, _c10))(\"ngStyle\", i0.ɵɵpureFunction0(7, _c11));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.closeAriaLabel)(\"tabindex\", ctx_r1.closeTabindex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closeIconTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16, 3);\n    i0.ɵɵlistener(\"mousedown\", function Dialog_div_0_div_1_ng_template_3_div_1_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.initDrag($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_ng_template_3_div_1_span_2_Template, 2, 2, \"span\", 17)(3, Dialog_div_0_div_1_ng_template_3_div_1_span_3_Template, 2, 1, \"span\", 17)(4, Dialog_div_0_div_1_ng_template_3_div_1_ng_container_4_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵtemplate(6, Dialog_div_0_div_1_ng_template_3_div_1_button_6_Template, 5, 8, \"button\", 19)(7, Dialog_div_0_div_1_ng_template_3_div_1_button_7_Template, 3, 8, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.headerFacet && !ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.headerFacet);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maximizable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closable);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31, 4);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵtemplate(3, Dialog_div_0_div_1_ng_template_3_div_6_ng_container_3_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dialog_div_0_div_1_ng_template_3_div_0_Template, 1, 0, \"div\", 11)(1, Dialog_div_0_div_1_ng_template_3_div_1_Template, 8, 5, \"div\", 12);\n    i0.ɵɵelementStart(2, \"div\", 13, 2);\n    i0.ɵɵprojection(4);\n    i0.ɵɵtemplate(5, Dialog_div_0_div_1_ng_template_3_ng_container_5_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, Dialog_div_0_div_1_ng_template_3_div_6_Template, 4, 1, \"div\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.resizable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showHeader);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.contentStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-dialog-content\")(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.footerFacet || ctx_r1.footerTemplate);\n  }\n}\nfunction Dialog_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8, 0);\n    i0.ɵɵlistener(\"@animation.start\", function Dialog_div_0_div_1_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Dialog_div_0_div_1_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, Dialog_div_0_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 9)(3, Dialog_div_0_div_1_ng_template_3_Template, 7, 8, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notHeadless_r7 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(10, _c6, ctx_r1.rtl, ctx_r1.draggable, ctx_r1.resizable, ctx_r1.maximized))(\"ngStyle\", ctx_r1.style)(\"pFocusTrapDisabled\", ctx_r1.focusTrap === false)(\"@animation\", i0.ɵɵpureFunction1(18, _c8, i0.ɵɵpureFunction2(15, _c7, ctx_r1.transformOptions, ctx_r1.transitionOptions)));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-modal\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.headlessTemplate)(\"ngIfElse\", notHeadless_r7);\n  }\n}\nfunction Dialog_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, Dialog_div_0_div_1_Template, 5, 20, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.maskStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.maskStyle)(\"ngClass\", i0.ɵɵpureFunctionV(5, _c5, [ctx_r1.modal, ctx_r1.modal || ctx_r1.blockScroll, ctx_r1.position === \"left\", ctx_r1.position === \"right\", ctx_r1.position === \"top\", ctx_r1.position === \"topleft\" || ctx_r1.position === \"top-left\", ctx_r1.position === \"topright\" || ctx_r1.position === \"top-right\", ctx_r1.position === \"bottom\", ctx_r1.position === \"bottomleft\" || ctx_r1.position === \"bottom-left\", ctx_r1.position === \"bottomright\" || ctx_r1.position === \"bottom-right\"]));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * Dialog is a container to display content in an overlay window.\n * @group Components\n */\nlet Dialog = /*#__PURE__*/(() => {\n  class Dialog {\n    document;\n    platformId;\n    el;\n    renderer;\n    zone;\n    cd;\n    config;\n    /**\n     * Title text of the dialog.\n     * @group Props\n     */\n    header;\n    /**\n     * Enables dragging to change the position using header.\n     * @group Props\n     */\n    draggable = true;\n    /**\n     * Enables resizing of the content.\n     * @group Props\n     */\n    resizable = true;\n    /**\n     * Defines the left offset of dialog.\n     * @group Props\n     * @deprecated positionLeft property is deprecated.\n     */\n    get positionLeft() {\n      return 0;\n    }\n    set positionLeft(_positionLeft) {\n      console.log('positionLeft property is deprecated.');\n    }\n    /**\n     * Defines the top offset of dialog.\n     * @group Props\n     * @deprecated positionTop property is deprecated.\n     */\n    get positionTop() {\n      return 0;\n    }\n    set positionTop(_positionTop) {\n      console.log('positionTop property is deprecated.');\n    }\n    /**\n     * Style of the content section.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Defines if background should be blocked when dialog is displayed.\n     * @group Props\n     */\n    modal = false;\n    /**\n     * Specifies if pressing escape key should hide the dialog.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Specifies if clicking the modal background should hide the dialog.\n     * @group Props\n     */\n    dismissableMask = false;\n    /**\n     * When enabled dialog is displayed in RTL direction.\n     * @group Props\n     */\n    rtl = false;\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    closable = true;\n    /**\n     * Defines if the component is responsive.\n     * @group Props\n     * @deprecated Responsive property is deprecated.\n     */\n    get responsive() {\n      return false;\n    }\n    set responsive(_responsive) {\n      console.log('Responsive property is deprecated.');\n    }\n    /**\n     * Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Object literal to define widths per screen size.\n     * @group Props\n     */\n    breakpoints;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the mask.\n     * @group Props\n     */\n    maskStyleClass;\n    /**\n     * Style of the mask.\n     * @group Props\n     */\n    maskStyle;\n    /**\n     * Whether to show the header or not.\n     * @group Props\n     */\n    showHeader = true;\n    /**\n     * Defines the breakpoint of the component responsive.\n     * @group Props\n     * @deprecated Breakpoint property is not utilized and deprecated. Use breakpoints or CSS media queries instead.\n     */\n    get breakpoint() {\n      return 649;\n    }\n    set breakpoint(_breakpoint) {\n      console.log('Breakpoint property is not utilized and deprecated, use breakpoints or CSS media queries instead.');\n    }\n    /**\n     * Whether background scroll should be blocked when dialog is visible.\n     * @group Props\n     */\n    blockScroll = false;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Minimum value for the left coordinate of dialog in dragging.\n     * @group Props\n     */\n    minX = 0;\n    /**\n     * Minimum value for the top coordinate of dialog in dragging.\n     * @group Props\n     */\n    minY = 0;\n    /**\n     * When enabled, first focusable element receives focus on show.\n     * @group Props\n     */\n    focusOnShow = true;\n    /**\n     * Whether the dialog can be displayed full screen.\n     * @group Props\n     */\n    maximizable = false;\n    /**\n     * Keeps dialog in the viewport.\n     * @group Props\n     */\n    keepInViewport = true;\n    /**\n     * When enabled, can only focus on elements inside the dialog.\n     * @group Props\n     */\n    focusTrap = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Name of the close icon.\n     * @group Props\n     */\n    closeIcon;\n    /**\n     * Defines a string that labels the close button for accessibility.\n     * @group Props\n     */\n    closeAriaLabel;\n    /**\n     * Index of the close button in tabbing order.\n     * @group Props\n     */\n    closeTabindex = '0';\n    /**\n     * Name of the minimize icon.\n     * @group Props\n     */\n    minimizeIcon;\n    /**\n     * Name of the maximize icon.\n     * @group Props\n     */\n    maximizeIcon;\n    /**\n     * Specifies the visibility of the dialog.\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(value) {\n      this._visible = value;\n      if (this._visible && !this.maskVisible) {\n        this.maskVisible = true;\n      }\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n      return this._style;\n    }\n    set style(value) {\n      if (value) {\n        this._style = {\n          ...value\n        };\n        this.originalStyle = value;\n      }\n    }\n    /**\n     * Position of the dialog.\n     * @group Props\n     */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      this._position = value;\n      switch (value) {\n        case 'topleft':\n        case 'bottomleft':\n        case 'left':\n          this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n          break;\n        case 'topright':\n        case 'bottomright':\n        case 'right':\n          this.transformOptions = 'translate3d(100%, 0px, 0px)';\n          break;\n        case 'bottom':\n          this.transformOptions = 'translate3d(0px, 100%, 0px)';\n          break;\n        case 'top':\n          this.transformOptions = 'translate3d(0px, -100%, 0px)';\n          break;\n        default:\n          this.transformOptions = 'scale(0.7)';\n          break;\n      }\n    }\n    /**\n     * Callback to invoke when dialog is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {boolean} value - New value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is initiated.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeInit = new EventEmitter();\n    /**\n     * Callback to invoke when dialog resizing is completed.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onResizeEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog dragging is completed.\n     * @param {DragEvent} event - Drag event.\n     * @group Emits\n     */\n    onDragEnd = new EventEmitter();\n    /**\n     * Callback to invoke when dialog maximized or unmaximized.\n     * @group Emits\n     */\n    onMaximize = new EventEmitter();\n    headerFacet;\n    footerFacet;\n    templates;\n    headerViewChild;\n    contentViewChild;\n    footerViewChild;\n    headerTemplate;\n    contentTemplate;\n    footerTemplate;\n    maximizeIconTemplate;\n    closeIconTemplate;\n    minimizeIconTemplate;\n    headlessTemplate;\n    _visible = false;\n    maskVisible;\n    container;\n    wrapper;\n    dragging;\n    ariaLabelledBy = this.getAriaLabelledBy();\n    documentDragListener;\n    documentDragEndListener;\n    resizing;\n    documentResizeListener;\n    documentResizeEndListener;\n    documentEscapeListener;\n    maskClickListener;\n    lastPageX;\n    lastPageY;\n    preventVisibleChangePropagation;\n    maximized;\n    preMaximizeContentHeight;\n    preMaximizeContainerWidth;\n    preMaximizeContainerHeight;\n    preMaximizePageX;\n    preMaximizePageY;\n    id = UniqueComponentId();\n    _style = {};\n    _position = 'center';\n    originalStyle;\n    transformOptions = 'scale(0.7)';\n    styleElement;\n    window;\n    get maximizeLabel() {\n      return this.config.getTranslation(TranslationKeys.ARIA)['maximizeLabel'];\n    }\n    constructor(document, platformId, el, renderer, zone, cd, config) {\n      this.document = document;\n      this.platformId = platformId;\n      this.el = el;\n      this.renderer = renderer;\n      this.zone = zone;\n      this.cd = cd;\n      this.config = config;\n      this.window = this.document.defaultView;\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'closeicon':\n            this.closeIconTemplate = item.template;\n            break;\n          case 'maximizeicon':\n            this.maximizeIconTemplate = item.template;\n            break;\n          case 'minimizeicon':\n            this.minimizeIconTemplate = item.template;\n            break;\n          case 'headless':\n            this.headlessTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngOnInit() {\n      if (this.breakpoints) {\n        this.createStyle();\n      }\n    }\n    getAriaLabelledBy() {\n      return this.header !== null ? UniqueComponentId() + '_header' : null;\n    }\n    parseDurationToMilliseconds(durationString) {\n      const transitionTimeRegex = /([\\d\\.]+)(ms|s)\\b/g;\n      let totalMilliseconds = 0;\n      let match;\n      while ((match = transitionTimeRegex.exec(durationString)) !== null) {\n        const value = parseFloat(match[1]);\n        const unit = match[2];\n        if (unit === 'ms') {\n          totalMilliseconds += value;\n        } else if (unit === 's') {\n          totalMilliseconds += value * 1000;\n        }\n      }\n      if (totalMilliseconds === 0) {\n        return undefined;\n      }\n      return totalMilliseconds;\n    }\n    focus(focusParentElement = this.contentViewChild?.nativeElement) {\n      const timeoutDuration = this.parseDurationToMilliseconds(this.transitionOptions);\n      let focusable = DomHandler.getFocusableElement(focusParentElement, '[autofocus]');\n      if (focusable) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => focusable.focus(), timeoutDuration || 5);\n        });\n        return;\n      }\n      const focusableElement = DomHandler.getFocusableElement(focusParentElement);\n      if (focusableElement) {\n        this.zone.runOutsideAngular(() => {\n          setTimeout(() => focusableElement.focus(), timeoutDuration || 5);\n        });\n      } else if (this.footerViewChild && focusParentElement !== this.footerViewChild.nativeElement) {\n        // If the content section is empty try to focus on footer\n        this.focus(this.footerViewChild.nativeElement);\n      }\n    }\n    close(event) {\n      this.visibleChange.emit(false);\n      event.preventDefault();\n    }\n    enableModality() {\n      if (this.closable && this.dismissableMask) {\n        this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', event => {\n          if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n            this.close(event);\n          }\n        });\n      }\n      if (this.modal) {\n        DomHandler.blockBodyScroll();\n      }\n    }\n    disableModality() {\n      if (this.wrapper) {\n        if (this.dismissableMask) {\n          this.unbindMaskClickListener();\n        }\n        // for nested dialogs w/modal\n        const scrollBlockers = document.querySelectorAll('.p-dialog-mask-scrollblocker');\n        if (this.modal && scrollBlockers && scrollBlockers.length == 1) {\n          DomHandler.unblockBodyScroll();\n        }\n        if (!this.cd.destroyed) {\n          this.cd.detectChanges();\n        }\n      }\n    }\n    maximize() {\n      this.maximized = !this.maximized;\n      if (!this.modal && !this.blockScroll) {\n        if (this.maximized) {\n          DomHandler.blockBodyScroll();\n        } else {\n          DomHandler.unblockBodyScroll();\n        }\n      }\n      this.onMaximize.emit({\n        maximized: this.maximized\n      });\n    }\n    unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.maskClickListener();\n        this.maskClickListener = null;\n      }\n    }\n    moveOnTop() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('modal', this.container, this.baseZIndex + this.config.zIndex.modal);\n        this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n      }\n    }\n    createStyle() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.styleElement) {\n          this.styleElement = this.renderer.createElement('style');\n          this.styleElement.type = 'text/css';\n          DomHandler.setAttribute(this.styleElement, 'nonce', this.config?.csp()?.nonce);\n          this.renderer.appendChild(this.document.head, this.styleElement);\n          let innerHTML = '';\n          for (let breakpoint in this.breakpoints) {\n            innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            .p-dialog[${this.id}]:not(.p-dialog-maximized) {\n                                width: ${this.breakpoints[breakpoint]} !important;\n                            }\n                        }\n                    `;\n          }\n          this.renderer.setProperty(this.styleElement, 'innerHTML', innerHTML);\n        }\n      }\n    }\n    initDrag(event) {\n      if (DomHandler.hasClass(event.target, 'p-dialog-header-icon') || DomHandler.hasClass(event.target, 'p-dialog-header-close-icon') || DomHandler.hasClass(event.target.parentElement, 'p-dialog-header-icon')) {\n        return;\n      }\n      if (this.draggable) {\n        this.dragging = true;\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n        this.container.style.margin = '0';\n        DomHandler.addClass(this.document.body, 'p-unselectable-text');\n      }\n    }\n    onDrag(event) {\n      if (this.dragging) {\n        const containerWidth = DomHandler.getOuterWidth(this.container);\n        const containerHeight = DomHandler.getOuterHeight(this.container);\n        const deltaX = event.pageX - this.lastPageX;\n        const deltaY = event.pageY - this.lastPageY;\n        const offset = this.container.getBoundingClientRect();\n        const containerComputedStyle = getComputedStyle(this.container);\n        const leftMargin = parseFloat(containerComputedStyle.marginLeft);\n        const topMargin = parseFloat(containerComputedStyle.marginTop);\n        const leftPos = offset.left + deltaX - leftMargin;\n        const topPos = offset.top + deltaY - topMargin;\n        const viewport = DomHandler.getViewport();\n        this.container.style.position = 'fixed';\n        if (this.keepInViewport) {\n          if (leftPos >= this.minX && leftPos + containerWidth < viewport.width) {\n            this._style.left = `${leftPos}px`;\n            this.lastPageX = event.pageX;\n            this.container.style.left = `${leftPos}px`;\n          }\n          if (topPos >= this.minY && topPos + containerHeight < viewport.height) {\n            this._style.top = `${topPos}px`;\n            this.lastPageY = event.pageY;\n            this.container.style.top = `${topPos}px`;\n          }\n        } else {\n          this.lastPageX = event.pageX;\n          this.container.style.left = `${leftPos}px`;\n          this.lastPageY = event.pageY;\n          this.container.style.top = `${topPos}px`;\n        }\n      }\n    }\n    endDrag(event) {\n      if (this.dragging) {\n        this.dragging = false;\n        DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n        this.cd.detectChanges();\n        this.onDragEnd.emit(event);\n      }\n    }\n    resetPosition() {\n      this.container.style.position = '';\n      this.container.style.left = '';\n      this.container.style.top = '';\n      this.container.style.margin = '';\n    }\n    //backward compatibility\n    center() {\n      this.resetPosition();\n    }\n    initResize(event) {\n      if (this.resizable) {\n        this.resizing = true;\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n        DomHandler.addClass(this.document.body, 'p-unselectable-text');\n        this.onResizeInit.emit(event);\n      }\n    }\n    onResize(event) {\n      if (this.resizing) {\n        let deltaX = event.pageX - this.lastPageX;\n        let deltaY = event.pageY - this.lastPageY;\n        let containerWidth = DomHandler.getOuterWidth(this.container);\n        let containerHeight = DomHandler.getOuterHeight(this.container);\n        let contentHeight = DomHandler.getOuterHeight(this.contentViewChild?.nativeElement);\n        let newWidth = containerWidth + deltaX;\n        let newHeight = containerHeight + deltaY;\n        let minWidth = this.container.style.minWidth;\n        let minHeight = this.container.style.minHeight;\n        let offset = this.container.getBoundingClientRect();\n        let viewport = DomHandler.getViewport();\n        let hasBeenDragged = !parseInt(this.container.style.top) || !parseInt(this.container.style.left);\n        if (hasBeenDragged) {\n          newWidth += deltaX;\n          newHeight += deltaY;\n        }\n        if ((!minWidth || newWidth > parseInt(minWidth)) && offset.left + newWidth < viewport.width) {\n          this._style.width = newWidth + 'px';\n          this.container.style.width = this._style.width;\n        }\n        if ((!minHeight || newHeight > parseInt(minHeight)) && offset.top + newHeight < viewport.height) {\n          this.contentViewChild.nativeElement.style.height = contentHeight + newHeight - containerHeight + 'px';\n          if (this._style.height) {\n            this._style.height = newHeight + 'px';\n            this.container.style.height = this._style.height;\n          }\n        }\n        this.lastPageX = event.pageX;\n        this.lastPageY = event.pageY;\n      }\n    }\n    resizeEnd(event) {\n      if (this.resizing) {\n        this.resizing = false;\n        DomHandler.removeClass(this.document.body, 'p-unselectable-text');\n        this.onResizeEnd.emit(event);\n      }\n    }\n    bindGlobalListeners() {\n      if (this.draggable) {\n        this.bindDocumentDragListener();\n        this.bindDocumentDragEndListener();\n      }\n      if (this.resizable) {\n        this.bindDocumentResizeListeners();\n      }\n      if (this.closeOnEscape && this.closable) {\n        this.bindDocumentEscapeListener();\n      }\n    }\n    unbindGlobalListeners() {\n      this.unbindDocumentDragListener();\n      this.unbindDocumentDragEndListener();\n      this.unbindDocumentResizeListeners();\n      this.unbindDocumentEscapeListener();\n    }\n    bindDocumentDragListener() {\n      if (!this.documentDragListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentDragListener = this.renderer.listen(this.window, 'mousemove', this.onDrag.bind(this));\n        });\n      }\n    }\n    unbindDocumentDragListener() {\n      if (this.documentDragListener) {\n        this.documentDragListener();\n        this.documentDragListener = null;\n      }\n    }\n    bindDocumentDragEndListener() {\n      if (!this.documentDragEndListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentDragEndListener = this.renderer.listen(this.window, 'mouseup', this.endDrag.bind(this));\n        });\n      }\n    }\n    unbindDocumentDragEndListener() {\n      if (this.documentDragEndListener) {\n        this.documentDragEndListener();\n        this.documentDragEndListener = null;\n      }\n    }\n    bindDocumentResizeListeners() {\n      if (!this.documentResizeListener && !this.documentResizeEndListener) {\n        this.zone.runOutsideAngular(() => {\n          this.documentResizeListener = this.renderer.listen(this.window, 'mousemove', this.onResize.bind(this));\n          this.documentResizeEndListener = this.renderer.listen(this.window, 'mouseup', this.resizeEnd.bind(this));\n        });\n      }\n    }\n    unbindDocumentResizeListeners() {\n      if (this.documentResizeListener && this.documentResizeEndListener) {\n        this.documentResizeListener();\n        this.documentResizeEndListener();\n        this.documentResizeListener = null;\n        this.documentResizeEndListener = null;\n      }\n    }\n    bindDocumentEscapeListener() {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n        if (event.key == 'Escape') {\n          this.close(event);\n        }\n      });\n    }\n    unbindDocumentEscapeListener() {\n      if (this.documentEscapeListener) {\n        this.documentEscapeListener();\n        this.documentEscapeListener = null;\n      }\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.wrapper);else DomHandler.appendChild(this.wrapper, this.appendTo);\n      }\n    }\n    restoreAppend() {\n      if (this.container && this.appendTo) {\n        this.renderer.appendChild(this.el.nativeElement, this.wrapper);\n      }\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.container = event.element;\n          this.wrapper = this.container?.parentElement;\n          this.moveOnTop();\n          this.appendContainer();\n          this.bindGlobalListeners();\n          this.container?.setAttribute(this.id, '');\n          if (this.modal) {\n            this.enableModality();\n          }\n          if (!this.modal && this.blockScroll) {\n            DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n          }\n          if (this.focusOnShow) {\n            this.focus();\n          }\n          break;\n        case 'void':\n          if (this.wrapper && this.modal) {\n            DomHandler.addClass(this.wrapper, 'p-component-overlay-leave');\n          }\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          this.onContainerDestroy();\n          this.onHide.emit({});\n          this.cd.markForCheck();\n          break;\n        case 'visible':\n          this.onShow.emit({});\n          break;\n      }\n    }\n    onContainerDestroy() {\n      this.unbindGlobalListeners();\n      this.dragging = false;\n      this.maskVisible = false;\n      if (this.maximized) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n        this.document.body.style.removeProperty('--scrollbar-width');\n        this.maximized = false;\n      }\n      if (this.modal) {\n        this.disableModality();\n      }\n      if (this.blockScroll) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.container = null;\n      this.wrapper = null;\n      this._style = this.originalStyle ? {\n        ...this.originalStyle\n      } : {};\n    }\n    destroyStyle() {\n      if (this.styleElement) {\n        this.renderer.removeChild(this.document.head, this.styleElement);\n        this.styleElement = null;\n      }\n    }\n    ngOnDestroy() {\n      if (this.container) {\n        this.restoreAppend();\n        this.onContainerDestroy();\n      }\n      this.destroyStyle();\n    }\n    static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Dialog)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Dialog,\n      selectors: [[\"p-dialog\"]],\n      contentQueries: function Dialog_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Dialog_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        header: \"header\",\n        draggable: [2, \"draggable\", \"draggable\", booleanAttribute],\n        resizable: [2, \"resizable\", \"resizable\", booleanAttribute],\n        positionLeft: \"positionLeft\",\n        positionTop: \"positionTop\",\n        contentStyle: \"contentStyle\",\n        contentStyleClass: \"contentStyleClass\",\n        modal: [2, \"modal\", \"modal\", booleanAttribute],\n        closeOnEscape: [2, \"closeOnEscape\", \"closeOnEscape\", booleanAttribute],\n        dismissableMask: [2, \"dismissableMask\", \"dismissableMask\", booleanAttribute],\n        rtl: [2, \"rtl\", \"rtl\", booleanAttribute],\n        closable: [2, \"closable\", \"closable\", booleanAttribute],\n        responsive: \"responsive\",\n        appendTo: \"appendTo\",\n        breakpoints: \"breakpoints\",\n        styleClass: \"styleClass\",\n        maskStyleClass: \"maskStyleClass\",\n        maskStyle: \"maskStyle\",\n        showHeader: [2, \"showHeader\", \"showHeader\", booleanAttribute],\n        breakpoint: \"breakpoint\",\n        blockScroll: [2, \"blockScroll\", \"blockScroll\", booleanAttribute],\n        autoZIndex: [2, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n        baseZIndex: [2, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n        minX: [2, \"minX\", \"minX\", numberAttribute],\n        minY: [2, \"minY\", \"minY\", numberAttribute],\n        focusOnShow: [2, \"focusOnShow\", \"focusOnShow\", booleanAttribute],\n        maximizable: [2, \"maximizable\", \"maximizable\", booleanAttribute],\n        keepInViewport: [2, \"keepInViewport\", \"keepInViewport\", booleanAttribute],\n        focusTrap: [2, \"focusTrap\", \"focusTrap\", booleanAttribute],\n        transitionOptions: \"transitionOptions\",\n        closeIcon: \"closeIcon\",\n        closeAriaLabel: \"closeAriaLabel\",\n        closeTabindex: \"closeTabindex\",\n        minimizeIcon: \"minimizeIcon\",\n        maximizeIcon: \"maximizeIcon\",\n        visible: \"visible\",\n        style: \"style\",\n        position: \"position\"\n      },\n      outputs: {\n        onShow: \"onShow\",\n        onHide: \"onHide\",\n        visibleChange: \"visibleChange\",\n        onResizeInit: \"onResizeInit\",\n        onResizeEnd: \"onResizeEnd\",\n        onDragEnd: \"onDragEnd\",\n        onMaximize: \"onMaximize\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      ngContentSelectors: _c4,\n      decls: 1,\n      vars: 1,\n      consts: [[\"container\", \"\"], [\"notHeadless\", \"\"], [\"content\", \"\"], [\"titlebar\", \"\"], [\"footer\", \"\"], [3, \"class\", \"ngStyle\", \"ngClass\", 4, \"ngIf\"], [3, \"ngStyle\", \"ngClass\"], [\"pFocusTrap\", \"\", \"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"class\", \"pFocusTrapDisabled\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", \"role\", \"dialog\", 3, \"ngClass\", \"ngStyle\", \"pFocusTrapDisabled\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-resizable-handle\", 3, \"mousedown\", 4, \"ngIf\"], [\"class\", \"p-dialog-header\", 3, \"mousedown\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-dialog-footer\", 4, \"ngIf\"], [1, \"p-resizable-handle\", 3, \"mousedown\"], [1, \"p-dialog-header\", 3, \"mousedown\"], [\"class\", \"p-dialog-title\", 3, \"id\", 4, \"ngIf\"], [1, \"p-dialog-header-icons\"], [\"role\", \"button\", \"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"ngClass\", \"ngStyle\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-dialog-title\", 3, \"id\"], [\"role\", \"button\", \"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"click\", \"keydown.enter\", \"ngClass\"], [\"class\", \"p-dialog-header-maximize-icon\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-dialog-header-maximize-icon\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [\"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"click\", \"keydown.enter\", \"ngClass\", \"ngStyle\"], [\"class\", \"p-dialog-header-close-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-dialog-header-close-icon\", 3, \"ngClass\"], [1, \"p-dialog-footer\"]],\n      template: function Dialog_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, Dialog_div_0_Template, 2, 16, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.maskVisible);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.FocusTrap, i4.ButtonDirective, i5.Ripple, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon],\n      styles: [\"@layer primeng{.p-dialog-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;pointer-events:none}.p-dialog-mask.p-component-overlay{pointer-events:auto}.p-dialog{display:flex;flex-direction:column;pointer-events:auto;max-height:90%;transform:scale(1);position:relative}.p-dialog-content{overflow-y:auto;flex-grow:1}.p-dialog-header{display:flex;align-items:center;justify-content:space-between;flex-shrink:0}.p-dialog-draggable .p-dialog-header{cursor:move}.p-dialog-footer{flex-shrink:0}.p-dialog .p-dialog-header-icons{display:flex;align-items:center}.p-dialog .p-dialog-header-icon{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-fluid .p-dialog-footer .p-button{width:auto}.p-dialog-top .p-dialog,.p-dialog-bottom .p-dialog,.p-dialog-left .p-dialog,.p-dialog-right .p-dialog,.p-dialog-top-left .p-dialog,.p-dialog-top-right .p-dialog,.p-dialog-bottom-left .p-dialog,.p-dialog-bottom-right .p-dialog{margin:.75rem;transform:translateZ(0)}.p-dialog-maximized{-webkit-transition:none;transition:none;transform:none;width:100vw!important;height:100vh!important;top:0!important;left:0!important;max-height:100%;height:100%}.p-dialog-maximized .p-dialog-content{flex-grow:1}.p-dialog-left{justify-content:flex-start}.p-dialog-right{justify-content:flex-end}.p-dialog-top{align-items:flex-start}.p-dialog-top-left{justify-content:flex-start;align-items:flex-start}.p-dialog-top-right{justify-content:flex-end;align-items:flex-start}.p-dialog-bottom{align-items:flex-end}.p-dialog-bottom-left{justify-content:flex-start;align-items:flex-end}.p-dialog-bottom-right{justify-content:flex-end;align-items:flex-end}.p-dialog .p-resizable-handle{position:absolute;font-size:.1px;display:block;cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.p-confirm-dialog .p-dialog-content{display:flex;align-items:center}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Dialog;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet DialogModule = /*#__PURE__*/(() => {\n  class DialogModule {\n    static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, FocusTrapModule, ButtonModule, RippleModule, TimesIcon, WindowMaximizeIcon, WindowMinimizeIcon, SharedModule]\n    });\n  }\n  return DialogModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Dialog, DialogModule };\n//# sourceMappingURL=primeng-dialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}