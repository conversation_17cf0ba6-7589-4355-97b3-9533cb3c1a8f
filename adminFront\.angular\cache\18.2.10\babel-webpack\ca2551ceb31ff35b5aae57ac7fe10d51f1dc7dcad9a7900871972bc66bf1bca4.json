{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r3)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.prevImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 58);\n    i0.ɵɵelement(2, \"path\", 59);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.nextImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 58);\n    i0.ɵɵelement(2, \"path\", 61);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r3.currentImageIndex || 0) + 1, \" / \", formItemReq_r3.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r3.currentImageIndex = i_r8);\n    });\n    i0.ɵɵelement(1, \"img\", 66);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r9 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"border-gray-300\", i_r8 !== (formItemReq_r3.currentImageIndex || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 5, imageUrl_r9), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template, 3, 7, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template, 2, 3, \"img\", 48);\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 50);\n    i0.ɵɵelement(5, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template, 3, 0, \"button\", 52)(7, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template, 3, 0, \"button\", 53)(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template, 2, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r3));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" \\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r10.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"img\", 72);\n    i0.ɵɵelementStart(2, \"input\", 73);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id, formItemReq_r3));\n    });\n    i0.ɵɵtext(4, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", picture_r14.name)(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u5DF2\\u4E0A\\u50B3\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template, 5, 4, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 76);\n    i0.ɵɵpipe(4, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 1, formItemReq_r3.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" \\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 78)(1, \"nb-checkbox\", 79);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.allSelected, $event) || (formItemReq_r3.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r3));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 78)(1, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedItems[item_r17], $event) || (formItemReq_r3.selectedItems[item_r17] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r17, formItemReq_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedItems[item_r17]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r17, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template, 3, 3, \"label\", 80);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedRemarkType[remark_r19], $event) || (formItemReq_r3.selectedRemarkType[remark_r19] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r19, formItemReq_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r19 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedRemarkType[remark_r19]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r19, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 78);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template, 2, 1, \"label\", 80);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 41);\n    i0.ɵɵtext(2, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r20 = i0.ɵɵreference(6);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CRemarkTypeOptions && ctx_r3.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r20);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"div\", 16)(6, \"label\", 17);\n    i0.ɵɵtext(7, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template, 10, 5, \"div\", 18)(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"div\", 21)(12, \"label\", 22);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CItemName, $event) || (formItemReq_r3.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CRequireAnswer, $event) || (formItemReq_r3.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 28)(23, \"label\", 26);\n    i0.ɵɵtext(24, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-select\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedCUiType, $event) || (formItemReq_r3.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener() {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(26, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template, 2, 2, \"nb-option\", 30);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(27, \"div\", 31)(28, \"div\", 32)(29, \"div\", 33)(30, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r11 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(31, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"input\", 35, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 1, \"div\", 36)(35, DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template, 5, 3, \"div\", 37)(36, DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(37, \"hr\", 39);\n    i0.ɵɵelementStart(38, \"div\", 40)(39, \"div\", 41);\n    i0.ɵɵtext(40, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 42);\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template, 3, 2, \"label\", 43)(43, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template, 2, 1, \"ng-container\", 44)(44, DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template, 7, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    const formItemReq_r3 = ctx.$implicit;\n    const idx_r21 = ctx.index;\n    const noHouseholds_r22 = i0.ɵɵreference(45);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl && formItemReq_r3.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CMatrialUrl || formItemReq_r3.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r21);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r3.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.listPictures && formItemReq_r3.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CMatrialUrl: o.CPicture ? [o.CPicture] : [],\n            currentImageIndex: 0,\n            isModalOpen: false\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 3,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [\"accent\", \"success\"], [1, \"pb-4\"], [1, \"font-bold\", \"text-[#818181]\", \"mb-4\"], [1, \"space-y-6\"], [1, \"font-bold\", \"text-xl\", \"pb-2\", \"border-b\", \"mb-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"p-3\", \"sticky\", \"bottom-0\", \"bg-white\", \"border-t\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"p-4\", \"border\", \"rounded-lg\", \"shadow-sm\", \"bg-white\"], [1, \"flex\", \"flex-wrap\", \"-mx-2\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [1, \"flex\", \"flex-wrap\"], [1, \"w-full\", \"md:w-1/3\", \"pr-0\", \"md:pr-3\", \"mb-4\", \"md:mb-0\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-2/3\", \"md:pl-3\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\", \"mb-3\"], [1, \"label\", \"w-1/3\", \"text-base\", \"pr-2\", \"shrink-0\", 3, \"for\"], [1, \"input-group\", \"items-baseline\", \"w-2/3\"], [1, \"text-gray-600\", \"text-sm\", \"mr-1\", \"shrink-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"w-full\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"label\", \"w-1/3\", \"pr-2\", \"shrink-0\", 3, \"for\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-2/3\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-2/3\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"mt-4\", \"md:mt-0\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\", \"mb-2\"], [1, \"btn\", \"btn-info\", \"h-fit\", \"w-full\", 3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"mt-2 space-y-3\", 4, \"ngIf\"], [\"class\", \"w-full text-center mt-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\", 4, \"ngIf\"], [1, \"my-4\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\", \"mb-3\"], [1, \"w-full\", \"md:w-1/5\", \"px-2\", \"pb-1\", \"md:pb-0\", \"font-medium\", \"text-gray-700\"], [1, \"w-full\", \"md:w-4/5\", \"px-2\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"flex flex-wrap w-full items-center\", 4, \"ngIf\"], [1, \"relative\"], [1, \"h-[160px]\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded\", \"border\", \"cursor-pointer\", \"group\", 3, \"click\"], [\"class\", \"h-full w-full object-cover transition-all duration-300 group-hover:scale-105\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2 overflow-x-auto\", 4, \"ngIf\"], [1, \"h-full\", \"w-full\", \"object-cover\", \"transition-all\", \"duration-300\", \"group-hover:scale-105\", 3, \"src\"], [1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", \"z-10\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", \"z-10\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded\"], [1, \"flex\", \"gap-1\", \"mt-2\", \"overflow-x-auto\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden\", 3, \"border-blue-500\", \"border-gray-300\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded\", \"overflow-hidden\", 3, \"click\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\"], [1, \"h-[160px]\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\"], [3, \"value\"], [1, \"mt-2\", \"space-y-3\"], [\"class\", \"border p-2 rounded-md bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"p-2\", \"rounded-md\", \"bg-gray-50\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"mb-2\", \"border\", 3, \"src\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-1\", \"text-xs\", \"mb-1\", 3, \"blur\", \"value\", \"disabled\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", \"w-full\", 3, \"click\", \"disabled\"], [1, \"w-full\", \"text-center\", \"mt-2\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"border\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\", \"mt-2\"], [1, \"mr-3\", \"cursor-pointer\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 4);\n          i0.ɵɵelement(4, \"h1\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 47, 26, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_1_0 = ctx.listFormItem.CIsLock) !== null && tmp_1_0 !== undefined ? tmp_1_0 : false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_2_0 = ctx.listFormItem.CIsLock) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvZGV0YWlsLWNvbnRlbnQtbWFuYWdlbWVudC1zYWxlcy1hY2NvdW50L2RldGFpbC1jb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ05BQWdOIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r3", "getCurrentImage", "formItemReq_r3", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "prevImage", "ɵɵresetView", "stopPropagation", "ɵɵelementEnd", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template_button_click_0_listener", "_r6", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "length", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template_button_click_0_listener", "i_r8", "_r7", "index", "ɵɵclassProp", "imageUrl_r9", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template_div_click_1_listener", "_r2", "openImageModal", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template", "case_r10", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener", "i_r13", "_r12", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener", "picture_r14", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener", "_r15", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r17", "_r16", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r18", "remark_r19", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r20", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener", "inputFile_r11", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template", "idx_r21", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "noHouseholds_r22", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "isModalOpen", "document", "body", "style", "overflow", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_8_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_10_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_12_listener", "tmp_1_0", "tmp_2_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],              selectedCUiType: this.CUiTypeOptions[0],\r\n              CMatrialUrl: o.CPicture ? [o.CPicture] : [],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"pb-4\"> <!-- Added padding to bottom of card body -->\r\n    <h1 class=\"font-bold text-[#818181] mb-4\"></h1> <!-- Retained original empty h1, added margin-bottom -->\r\n    <div class=\"space-y-6\"> <!-- Add vertical spacing between main sections -->\r\n      <h4 class=\"font-bold text-xl pb-2 border-b mb-4\">類型-獨立選樣</h4> <!-- Styled section title -->\r\n\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <!-- Enhanced container for each form item -->\r\n        <div class=\"p-4 border rounded-lg shadow-sm bg-white\">\r\n          <div class=\"flex flex-wrap -mx-2\"> <!-- Row container with negative margin for column padding -->\r\n\r\n            <!-- Left Column: Main Material Image + Form Fields -->\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <div class=\"flex flex-wrap\"> <!-- Main Material Images (CMatrialUrl) -->\r\n                <div class=\"w-full md:w-1/3 pr-0 md:pr-3 mb-4 md:mb-0\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">主要材料示意</label>                  <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                    <!-- Image carousel container -->\r\n                    <div class=\"h-[160px] w-full relative overflow-hidden rounded border cursor-pointer group\"\r\n                      (click)=\"openImageModal(formItemReq)\">\r\n                      <img class=\"h-full w-full object-cover transition-all duration-300 group-hover:scale-105\"\r\n                        [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n                      \r\n                      <!-- Zoom overlay -->\r\n                      <div class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\r\n                        <svg class=\"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-all duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                        </svg>\r\n                      </div>\r\n\r\n                      <!-- Previous button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\"\r\n                        (click)=\"prevImage(formItemReq); $event.stopPropagation()\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                          </path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Next button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\"\r\n                        (click)=\"nextImage(formItemReq); $event.stopPropagation()\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Image counter -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\r\n                        {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Thumbnail navigation for multiple images -->\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-1 mt-2 overflow-x-auto\">\r\n                      <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                        class=\"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden\"\r\n                        [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                        [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                        (click)=\"formItemReq.currentImageIndex = i\">\r\n                        <img class=\"w-full h-full object-cover\" [src]=\"imageUrl | base64Image\">\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                    class=\"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\">\r\n                    無主要材料示意\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields -->\r\n                <div class=\"w-full md:w-2/3 md:pl-3\">\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'CItemName_' + idx\" class=\"label w-1/3 text-base pr-2 shrink-0\">項目名稱</label>\r\n                    <div class=\"input-group items-baseline w-2/3\">\r\n                      <span\r\n                        class=\"text-gray-600 text-sm mr-1 shrink-0\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:</span>\r\n                      <input type=\"text\" [id]=\"'CItemName_' + idx\" class=\"w-full\" nbInput\r\n                        [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                        [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'cRequireAnswer_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">必填數量</label>\r\n                    <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\" class=\"w-2/3\" nbInput placeholder=\"輸入數量\"\r\n                      [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                      [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem.CIsLock ?? false)\" />\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full\">\r\n                    <label [for]=\"'uiType_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">前台UI類型</label>\r\n                    <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                      class=\"w-2/3\" (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                      <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                        {{ case.label }}\r\n                      </nb-option>\r\n                    </nb-select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Concept Design Image Upload and Preview -->\r\n            <div class=\"w-full md:w-1/4 px-2 mt-4 md:mt-0\">\r\n              <div class=\"w-full flex flex-col\">\r\n                <div class=\"flex justify-end w-full mb-2\">\r\n                  <button class=\"btn btn-info h-fit w-full\" [disabled]=\"listFormItem.CIsLock\"\r\n                    (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n                  <!-- Note: #inputFile in *ngFor creates a local template variable for each iteration -->\r\n                  <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                    accept=\"image/png, image/gif, image/jpeg\">\r\n                </div>\r\n\r\n                <!-- Uploaded Pictures List -->\r\n                <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"mt-2 space-y-3\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">已上傳概念圖</label>\r\n                  <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                    class=\"border p-2 rounded-md bg-gray-50\">\r\n                    <img class=\"h-32 w-full object-cover rounded mb-2 border\" [src]=\"picture.data\">\r\n                    <input nbInput class=\"w-full p-1 text-xs mb-1\" type=\"text\" placeholder=\"圖片說明/檔名\"\r\n                      [value]=\"picture.name\" (blur)=\"renameFile($event, i, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                    <button class=\"btn btn-outline-danger btn-xs w-full\" (click)=\"removeImage(picture.id, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">删除</button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Default Concept Design Image (if no uploaded list) -->\r\n                <div class=\"w-full text-center mt-2\"\r\n                  *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">預設概念圖</label>\r\n                  <img class=\"h-32 w-full object-cover rounded border\" [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                </div>\r\n\r\n                <div\r\n                  *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                  class=\"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\">\r\n                  無概念設計圖\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Separator Line -->\r\n          <hr class=\"my-4\">\r\n\r\n          <!-- Applicable Households -->\r\n          <div class=\"flex flex-wrap w-full items-center mb-3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">適用戶別</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <label class=\"mr-3 cursor-pointer\" *ngIf=\"houseHoldList.length\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                  全選\r\n                </nb-checkbox>\r\n              </label>\r\n              <ng-container *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                <label *ngFor=\"let item of houseHoldList\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                    [disabled]=\"listFormItem.CIsLock\"\r\n                    (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                    {{ item }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noHouseholds>\r\n                <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Remark Options (Conditional) -->\r\n          <div class=\"flex flex-wrap w-full items-center\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">備註選項</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <ng-container *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                    [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                    (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                    {{ remark }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noRemarkOptions>\r\n                <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n        </div> <!-- End of enhanced container for each form item -->\r\n      </ng-container>\r\n\r\n      <!-- Removed commented out old ngFor structure -->\r\n\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-end p-3 sticky bottom-0 bg-white border-t\"> <!-- Made footer sticky -->\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-primary\" (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;;;;;;;ICQtDC,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,cAAA,IAAAN,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAUpDP,EAAA,CAAAQ,cAAA,iBAE6D;IAA3DR,EAAA,CAAAS,UAAA,mBAAAC,4GAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAY,SAAA,CAAAV,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eACO;IAEXD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAGTnB,EAAA,CAAAQ,cAAA,iBAE6D;IAA3DR,EAAA,CAAAS,UAAA,mBAAAW,4GAAAT,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAf,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAkB,SAAA,CAAAhB,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAA8F;IAElGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAGTnB,EAAA,CAAAQ,cAAA,cACgG;IAC9FR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;IADJnB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAyB,kBAAA,OAAAnB,cAAA,CAAAoB,iBAAA,mBAAApB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,MACF;;;;;;IAKA5B,EAAA,CAAAQ,cAAA,iBAI8C;IAA5CR,EAAA,CAAAS,UAAA,mBAAAoB,kHAAA;MAAA,MAAAC,IAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAmB,GAAA,EAAAC,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAAAX,cAAA,CAAAoB,iBAAA,GAAAI,IAAA;IAAA,EAA2C;IAC3C9B,EAAA,CAAAC,SAAA,cAAuE;;IACzED,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAHPnB,EADA,CAAAiC,WAAA,oBAAAH,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OAAoE,oBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACA;IAE5B1B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAA+B,WAAA,GAAAlC,EAAA,CAAAO,aAAA,CAA8B;;;;;IAN1EP,EAAA,CAAAQ,cAAA,cAAwF;IACtFR,EAAA,CAAAmC,UAAA,IAAAC,yFAAA,qBAI8C;IAGhDpC,EAAA,CAAAmB,YAAA,EAAM;;;;IAPyBnB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAqB,WAAA,CAA4B;;;;;;IAxC3D3B,EAF0F,CAAAQ,cAAA,cAA4F,cAG9I;IAAtCR,EAAA,CAAAS,UAAA,mBAAA4B,gGAAA;MAAArC,EAAA,CAAAY,aAAA,CAAA0B,GAAA;MAAA,MAAAhC,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAmC,cAAA,CAAAjC,cAAA,CAA2B;IAAA,EAAC;IACrCN,EAAA,CAAAmC,UAAA,IAAAK,gFAAA,kBAC0F;IAG1FxC,EAAA,CAAAQ,cAAA,cAA2I;;IACzIR,EAAA,CAAAQ,cAAA,cAAoJ;IAClJR,EAAA,CAAAC,SAAA,eAAuJ;IAE3JD,EADE,CAAAmB,YAAA,EAAM,EACF;IAsBNnB,EAnBA,CAAAmC,UAAA,IAAAM,mFAAA,qBAE6D,IAAAC,mFAAA,qBAUA,IAAAC,gFAAA,kBAQmC;IAGlG3C,EAAA,CAAAmB,YAAA,EAAM;IAGNnB,EAAA,CAAAmC,UAAA,IAAAS,gFAAA,kBAAwF;IAS1F5C,EAAA,CAAAmB,YAAA,EAAM;;;;;IA7CoDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAAC,cAAA,EAAkC;IAU/EN,EAAA,CAAAwB,SAAA,GAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAUxC5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAS3C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAO1C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;;;;;IAUhD5B,EAAA,CAAAQ,cAAA,cACoG;IAClGR,EAAA,CAAAuB,MAAA,mDACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;;IA0BFnB,EAAA,CAAAQ,cAAA,oBAA8D;IAC5DR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAY;;;;IAFmCnB,EAAA,CAAAE,UAAA,UAAA2C,QAAA,CAAc;IAC3D7C,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAqBJ/C,EAAA,CAAAQ,cAAA,cAC2C;IACzCR,EAAA,CAAAC,SAAA,cAA+E;IAC/ED,EAAA,CAAAQ,cAAA,gBAE6C;IADpBR,EAAA,CAAAS,UAAA,kBAAAuC,wGAAArC,MAAA;MAAA,MAAAsC,KAAA,GAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAlB,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAQb,MAAA,CAAA+C,UAAA,CAAAxC,MAAA,EAAAsC,KAAA,EAAA3C,cAAA,CAAkC;IAAA,EAAC;IADpEN,EAAA,CAAAmB,YAAA,EAE6C;IAC7CnB,EAAA,CAAAQ,cAAA,iBAC6C;IADQR,EAAA,CAAAS,UAAA,mBAAA2C,0GAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAnC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAkD,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAjD,cAAA,CAAoC;IAAA,EAAC;IACtDN,EAAA,CAAAuB,MAAA,mBAAE;IACjDvB,EADiD,CAAAmB,YAAA,EAAS,EACpD;;;;;;;IANsDnB,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAAE,UAAA,QAAAmD,WAAA,CAAAG,IAAA,EAAAxD,EAAA,CAAAO,aAAA,CAAoB;IAE5EP,EAAA,CAAAwB,SAAA,EAAsB;IACtBxB,EADA,CAAAE,UAAA,UAAAmD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SACoB;IAE1C1D,EAAA,CAAAwB,SAAA,EAA0C;IAA1CxB,EAAA,CAAAE,UAAA,cAAA4D,QAAA,GAAA1D,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAR9C9D,EADF,CAAAQ,cAAA,cAAoG,gBACtC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAC1EnB,EAAA,CAAAmC,UAAA,IAAA4B,iFAAA,kBAC2C;IAQ7C/D,EAAA,CAAAmB,YAAA,EAAM;;;;IATqBnB,EAAA,CAAAwB,SAAA,GAA6B;IAA7BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAA0D,YAAA,CAA6B;;;;;IActDhE,EAFF,CAAAQ,cAAA,cAC6G,gBAC/C;IAAAR,EAAA,CAAAuB,MAAA,qCAAK;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACzEnB,EAAA,CAAAC,SAAA,cAAsG;;IACxGD,EAAA,CAAAmB,YAAA,EAAM;;;;IADiDnB,EAAA,CAAAwB,SAAA,GAAgD;IAAhDxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAG,cAAA,CAAA2D,cAAA,GAAAjE,EAAA,CAAAO,aAAA,CAAgD;;;;;IAGvGP,EAAA,CAAAQ,cAAA,cAEoG;IAClGR,EAAA,CAAAuB,MAAA,6CACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;;;IAaNnB,EADF,CAAAQ,cAAA,gBAAgE,sBAEJ;IAD7CR,EAAA,CAAAkE,gBAAA,2BAAAC,mHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgE,WAAA,EAAA3D,MAAA,MAAAL,cAAA,CAAAgE,WAAA,GAAA3D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IAChDX,EAAA,CAAAS,UAAA,2BAAA0D,mHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAmE,gBAAA,CAAA5D,MAAA,EAAAL,cAAA,CAAqC;IAAA,EAAC;IACvDN,EAAA,CAAAuB,MAAA,qBACF;IACFvB,EADE,CAAAmB,YAAA,EAAc,EACR;;;;;IAJOnB,EAAA,CAAAwB,SAAA,EAAqC;IAArCxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgE,WAAA,CAAqC;IAACtE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAOlF5D,EADF,CAAAQ,cAAA,gBAAsE,sBAGS;IAFhER,EAAA,CAAAkE,gBAAA,2BAAAO,kIAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAsE,aAAA,CAAAF,QAAA,GAAA/D,MAAA,MAAAL,cAAA,CAAAsE,aAAA,CAAAF,QAAA,IAAA/D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAA6C;IAExDX,EAAA,CAAAS,UAAA,2BAAAgE,kIAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAyE,6BAAA,CAAAlE,MAAA,EAAA+D,QAAA,EAAApE,cAAA,CAAwD;IAAA,EAAC;IAC1EN,EAAA,CAAAuB,MAAA,GACF;IACFvB,EADE,CAAAmB,YAAA,EAAc,EACR;;;;;;IALOnB,EAAA,CAAAwB,SAAA,EAA6C;IAA7CxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAsE,aAAA,CAAAF,QAAA,EAA6C;IACxD1E,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAEjC5D,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAA4B,QAAA,MACF;;;;;IANJ1E,EAAA,CAAA8E,uBAAA,GAAkE;IAChE9E,EAAA,CAAAmC,UAAA,IAAA4C,4FAAA,oBAAsE;;;;;IAA9C/E,EAAA,CAAAwB,SAAA,EAAgB;IAAhBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4E,aAAA,CAAgB;;;;;IASxChF,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;;;;;;IAW/CnB,EAAA,CAAAQ,cAAA,sBAEwE;IADtER,EAAA,CAAAkE,gBAAA,2BAAAe,sJAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,GAAAxE,MAAA,MAAAL,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,IAAAxE,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAoD;IACpDX,EAAA,CAAAS,UAAA,2BAAAwE,sJAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAiF,sBAAA,CAAA1E,MAAA,EAAAwE,UAAA,EAAA7E,cAAA,CAAmD;IAAA,EAAC;IACrEN,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAc;;;;;;IAHZnB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,EAAoD;IAACnF,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAEtF5D,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAqC,UAAA,MACF;;;;;IALFnF,EAAA,CAAAQ,cAAA,gBAA6E;IAC3ER,EAAA,CAAAmC,UAAA,IAAAmD,gHAAA,0BAEwE;IAG1EtF,EAAA,CAAAmB,YAAA,EAAQ;;;;IALQnB,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA8E,kBAAA,CAAoC;;;;;IAFtDpF,EAAA,CAAA8E,uBAAA,GAAgG;IAC9F9E,EAAA,CAAAmC,UAAA,IAAAoD,kGAAA,oBAA6E;;;;;IAAnDvF,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAoF,kBAAA,CAAqB;;;;;IAS/CxF,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;;;;;IAZrDnB,EADF,CAAAQ,cAAA,cAAgG,cACrB;IAAAR,EAAA,CAAAuB,MAAA,+BAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IACnFnB,EAAA,CAAAQ,cAAA,cAAkC;IAUhCR,EATA,CAAAmC,UAAA,IAAAsD,0FAAA,2BAAgG,IAAAC,yFAAA,gCAAA1F,EAAA,CAAA2F,sBAAA,CASlE;IAIlC3F,EADE,CAAAmB,YAAA,EAAM,EACF;;;;;IAbanB,EAAA,CAAAwB,SAAA,GAA2D;IAAAxB,EAA3D,CAAAE,UAAA,SAAAE,MAAA,CAAAoF,kBAAA,IAAApF,MAAA,CAAAoF,kBAAA,CAAA5D,MAAA,KAA2D,aAAAgE,mBAAA,CAAoB;;;;;;IA3KtG5F,EAAA,CAAA8E,uBAAA,GAA8E;IASlE9E,EAPV,CAAAQ,cAAA,cAAsD,cAClB,cAGE,cACJ,cAC6B,gBACO;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAmD1EnB,EAnD4F,CAAAmC,UAAA,IAAA0D,0EAAA,mBAA4F,IAAAC,0EAAA,kBAoDpF;IAGtG9F,EAAA,CAAAmB,YAAA,EAAM;IAKFnB,EAFJ,CAAAQ,cAAA,eAAqC,eACmB,iBAC0B;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAExFnB,EADF,CAAAQ,cAAA,eAA8C,gBAEE;IAAAR,EAAA,CAAAuB,MAAA,IAAsE;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;IAC3HnB,EAAA,CAAAQ,cAAA,iBAE+C;IAD7CR,EAAA,CAAAkE,gBAAA,2BAAA6B,qGAAApF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA2F,SAAA,EAAAtF,MAAA,MAAAL,cAAA,CAAA2F,SAAA,GAAAtF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAmC;IAGzCX,EAJI,CAAAmB,YAAA,EAE+C,EAC3C,EACF;IAEJnB,EADF,CAAAQ,cAAA,eAAsD,iBACqB;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACrFnB,EAAA,CAAAQ,cAAA,iBAE4F;IAD1FR,EAAA,CAAAkE,gBAAA,2BAAAgC,qGAAAvF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA6F,cAAA,EAAAxF,MAAA,MAAAL,cAAA,CAAA6F,cAAA,GAAAxF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAE5CX,EAHE,CAAAmB,YAAA,EAE4F,EACxF;IAEJnB,EADF,CAAAQ,cAAA,eAAiD,iBACkB;IAAAR,EAAA,CAAAuB,MAAA,kCAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAC/EnB,EAAA,CAAAQ,cAAA,qBAE6C;IAFUR,EAAA,CAAAkE,gBAAA,2BAAAkC,yGAAAzF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA+F,eAAA,EAAA1F,MAAA,MAAAL,cAAA,CAAA+F,eAAA,GAAA1F,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAyC;IAChFX,EAAA,CAAAS,UAAA,4BAAA6F,0GAAA;MAAA,MAAAhG,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAkBb,MAAA,CAAAmG,mBAAA,CAAAjG,cAAA,CAAgC;IAAA,EAAC;IAEjEN,EAAA,CAAAmC,UAAA,KAAAqE,iFAAA,wBAA8D;IAOxExG,EAJQ,CAAAmB,YAAA,EAAY,EACR,EACF,EACF,EACF;IAMAnB,EAHN,CAAAQ,cAAA,eAA+C,eACX,eACU,kBAEV;IAA5BR,EAAA,CAAAS,UAAA,mBAAAgG,8FAAA;MAAAzG,EAAA,CAAAY,aAAA,CAAAoF,GAAA;MAAA,MAAAU,aAAA,GAAA1G,EAAA,CAAA2G,WAAA;MAAA,OAAA3G,EAAA,CAAAiB,WAAA,CAASyF,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAC5G,EAAA,CAAAuB,MAAA,kDAAO;IAAAvB,EAAA,CAAAmB,YAAA,EAAS;IAE9CnB,EAAA,CAAAQ,cAAA,oBAC4C;IADCR,EAAA,CAAAS,UAAA,oBAAAoG,8FAAAlG,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUb,MAAA,CAAA0G,WAAA,CAAAnG,MAAA,EAAAL,cAAA,CAAgC;IAAA,EAAC;IAE1FN,EAFE,CAAAmB,YAAA,EAC4C,EACxC;IAuBNnB,EApBA,CAAAmC,UAAA,KAAA4E,2EAAA,kBAAoG,KAAAC,2EAAA,kBAeS,KAAAC,2EAAA,kBAOT;IAK1GjH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;IAGNnB,EAAA,CAAAC,SAAA,cAAiB;IAIfD,EADF,CAAAQ,cAAA,eAAqD,eACsB;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IACnFnB,EAAA,CAAAQ,cAAA,eAAkC;IAgBhCR,EAfA,CAAAmC,UAAA,KAAA+E,6EAAA,oBAAgE,KAAAC,oFAAA,2BAME,KAAAC,mFAAA,gCAAApH,EAAA,CAAA2F,sBAAA,CASvC;IAI/B3F,EADE,CAAAmB,YAAA,EAAM,EACF;IAGNnB,EAAA,CAAAmC,UAAA,KAAAkF,2EAAA,kBAAgG;IAkBlGrH,EAAA,CAAAmB,YAAA,EAAM;;;;;;;;;;;IAjLsGnB,EAAA,CAAAwB,SAAA,GAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAmE;IAmD/J5B,EAAA,CAAAwB,SAAA,EAAsE;IAAtExB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,OAAsE;IASnE5B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAAE,UAAA,uBAAAoH,OAAA,CAA0B;IAGetH,EAAA,CAAAwB,SAAA,GAAsE;IAAtExB,EAAA,CAAAuH,kBAAA,KAAAjH,cAAA,CAAAkH,KAAA,OAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,SAAA,MAAsE;IACjG1H,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAE,UAAA,sBAAAoH,OAAA,CAAyB;IAC1CtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA2F,SAAA,CAAmC;IACnCjG,EAAA,CAAAE,UAAA,cAAAwD,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA0C;IAIvC1D,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAE,UAAA,4BAAAoH,OAAA,CAA+B;IACjBtH,EAAA,CAAAwB,SAAA,GAA8B;IAA9BxB,EAAA,CAAAE,UAAA,2BAAAoH,OAAA,CAA8B;IACjDtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA6F,cAAA,CAAwC;IACxCnG,EAAA,CAAAE,UAAA,aAAAI,cAAA,CAAA+F,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAAxH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,UAAuF;IAGlF5H,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAE,UAAA,oBAAAoH,OAAA,CAAuB;IACEtH,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAE,UAAA,mBAAAoH,OAAA,CAAsB;IAACtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA+F,eAAA,CAAyC;IAE9FrG,EAAA,CAAAE,UAAA,cAAA2H,QAAA,GAAAzH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAiE,QAAA,KAAAhE,SAAA,GAAAgE,QAAA,SAA0C;IACd7H,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA0H,cAAA,CAAiB;IAaP9H,EAAA,CAAAwB,SAAA,GAAiC;IAAjCxB,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAQvE5D,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,KAAqE;IAexE5B,EAAA,CAAAwB,SAAA,EAAwG;IAAxGxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAwG;IAMxG5B,EAAA,CAAAwB,SAAA,EAAyG;IAAzGxB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAyG;IAe1E5B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,CAA0B;IAM/C5B,EAAA,CAAAwB,SAAA,EAAgC;IAAAxB,EAAhC,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,KAAgC,aAAAmG,gBAAA,CAAiB;IAgBnB/H,EAAA,CAAAwB,SAAA,GAA6C;IAA7CxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA+F,eAAA,CAAAsB,KAAA,OAA6C;;;ADhIxG,OAAM,MAAOK,4CAA6C,SAAQnI,aAAa;EAC7EoI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAhB,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,EACD;MACE4E,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,EAAE;MACD4E,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,CACF;IACD,KAAAyC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAqNnD,KAAA2D,KAAK,GAAY,IAAI;EAlQrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM9F,EAAE,GAAG6F,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG/F,EAAE;QACrB,IAAI,IAAI,CAAC+F,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC7B,KAAU,EAAE8B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA5C,WAAWA,CAAC6C,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC5F,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;UACxCgI,YAAY,CAAC5F,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC5F,YAAY,CAAC4G,IAAI,CAAC;YAC7BrH,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEArE,WAAWA,CAACuH,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC5F,YAAY,CAACpC,MAAM,EAAE;MACpCgI,YAAY,CAAC5F,YAAY,GAAG4F,YAAY,CAAC5F,YAAY,CAAC8G,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACxH,EAAE,IAAIsH,SAAS,CAAC;IAC7F;EACF;EACA1H,UAAUA,CAACwG,KAAU,EAAE3H,KAAa,EAAE4H,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAAC2I,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAAC2I,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAAC2I,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,GAAG,GAAGiC,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAACyI,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAAC2I,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC5F,YAAY,CAAChC,KAAK,CAAC,CAAC2I,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA9J,SAASA,CAACgK,WAAgB;IACxB,IAAIA,WAAW,CAAC3J,WAAW,IAAI2J,WAAW,CAAC3J,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE0J,WAAW,CAAC5J,iBAAiB,GAAG,CAAC4J,WAAW,CAAC5J,iBAAiB,GAAG,CAAC,IAAI4J,WAAW,CAAC3J,WAAW,CAACC,MAAM;IACtG;EACF;EAEAZ,SAASA,CAACsK,WAAgB;IACxB,IAAIA,WAAW,CAAC3J,WAAW,IAAI2J,WAAW,CAAC3J,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE0J,WAAW,CAAC5J,iBAAiB,GAAG4J,WAAW,CAAC5J,iBAAiB,KAAK,CAAC,GAC/D4J,WAAW,CAAC3J,WAAW,CAACC,MAAM,GAAG,CAAC,GAClC0J,WAAW,CAAC5J,iBAAiB,GAAG,CAAC;IACvC;EACF;EACArB,eAAeA,CAACiL,WAAgB;IAC9B,IAAIA,WAAW,CAAC3J,WAAW,IAAI2J,WAAW,CAAC3J,WAAW,CAACC,MAAM,GAAG,CAAC,IAAI0J,WAAW,CAAC5J,iBAAiB,KAAKmC,SAAS,EAAE;MAChH,OAAOyH,WAAW,CAAC3J,WAAW,CAAC2J,WAAW,CAAC5J,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAa,cAAcA,CAAC+I,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAC3J,WAAW,IAAI2J,WAAW,CAAC3J,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI2J,UAAU,KAAK1H,SAAS,EAAE;QAC5ByH,WAAW,CAAC5J,iBAAiB,GAAG6J,UAAU;MAC5C;MACAD,WAAW,CAACE,WAAW,GAAG,IAAI;MAC9B;MACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAC,eAAeA,CAACP,WAAgB;IAC9BA,WAAW,CAACE,WAAW,GAAG,KAAK;IAC/B;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAE,cAAcA,CAACR,WAAgB;IAC7B,IAAI,CAAChK,SAAS,CAACgK,WAAW,CAAC;EAC7B;EAEAS,cAAcA,CAACT,WAAgB;IAC7B,IAAI,CAACtK,SAAS,CAACsK,WAAW,CAAC;EAC7B;EAEA;EACAU,SAASA,CAACrC,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAACE,WAAW,EAAE;MAC3B,QAAQ7B,KAAK,CAACsC,GAAG;QACf,KAAK,WAAW;UACdtC,KAAK,CAACuC,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACT,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAACuC,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAACR,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAACuC,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACP,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA/G,gBAAgBA,CAAC4H,OAAgB,EAAEvC,YAAiB;IAClDA,YAAY,CAACtF,WAAW,GAAG6H,OAAO;IAClC,IAAI,CAACnH,aAAa,CAACoH,OAAO,CAAC1C,IAAI,IAAG;MAChCE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAGyC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAtH,6BAA6BA,CAACsH,OAAgB,EAAEzC,IAAY,EAAEE,YAAiB;IAC7E,IAAIuC,OAAO,EAAE;MACXvC,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAGyC,OAAO;MAC1CvC,YAAY,CAACtF,WAAW,GAAG,IAAI,CAACU,aAAa,CAACqH,KAAK,CAAC3C,IAAI,IAAIE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,IAAIyC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLvC,YAAY,CAACtF,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAAC8G,OAAgB,EAAEzC,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACxE,kBAAkB,CAACsE,IAAI,CAAC,GAAGyC,OAAO;EACjD;EAEAG,kBAAkBA,CAAC9G,kBAA4B,EAAE+G,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAIjH,kBAAkB,EAAE;MACvCgH,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAC/B,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIuB,WAAW,EAAE;MAC9B,IAAIlH,kBAAkB,CAACmH,QAAQ,CAACxB,IAAI,CAAC,EAAE;QACrCqB,YAAY,CAACrB,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOqB,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAAC1C,IAAI,IAAG;MACnB,MAAMuC,GAAG,GAAG,GAAGvC,IAAI,CAAChC,SAAS,IAAIgC,IAAI,CAAClC,KAAK,IAAIkC,IAAI,CAACjC,KAAK,EAAE;MAC3D,IAAIqF,GAAG,CAACE,GAAG,CAACf,GAAG,CAAC,EAAE;QAChB,MAAMgB,QAAQ,GAAGH,GAAG,CAACzD,GAAG,CAAC4C,GAAG,CAAE;QAC9BgB,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,GAAG,CAACK,GAAG,CAAClB,GAAG,EAAE;UAAEvC,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEwD,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,EAAE,CAAC,CAACR,GAAG,CAAC,CAAC;MAAEpD,IAAI;MAAEwD;IAAK,CAAE,MAAM;MACxD,GAAGxD,IAAI;MACP6D,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAC9E,gBAAgB,CAAC+E,mCAAmC,CAAC;MACxD/B,IAAI,EAAE;QACJgC,YAAY,EAAE,IAAI,CAACpE,WAAW;QAC9BqE,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLjO,GAAG,CAACkO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAAC/I,aAAa,CAACoH,OAAO,CAAC1C,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAClE,kBAAkB,CAAC4G,OAAO,CAAC1C,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAACsE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAChB,GAAG,CAAEmB,CAAM,IAAI;UACnD,OAAO;YACLhK,cAAc,EAAE,IAAI;YACpBiK,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbzG,SAAS,EAAEuG,CAAC,CAACvG,SAAS;YACtBF,KAAK,EAAEyG,CAAC,CAACzG,KAAK;YACdC,KAAK,EAAEwG,CAAC,CAACxG,KAAK;YACdxB,SAAS,EAAE,GAAGgI,CAAC,CAACzG,KAAK,IAAIyG,CAAC,CAACxG,KAAK,IAAIwG,CAAC,CAACvG,SAAS,EAAE;YACjD6E,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACfpH,cAAc,EAAE,CAAC;YACjBiI,OAAO,EAAE,CAAC;YAAExJ,aAAa,EAAE,EAAE;YAC7BQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAAeqC,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACtEnG,WAAW,EAAEsM,CAAC,CAACI,QAAQ,GAAG,CAACJ,CAAC,CAACI,QAAQ,CAAC,GAAG,EAAE;YAC3C3M,iBAAiB,EAAE,CAAC;YACpB8J,WAAW,EAAE;WACd;QACH,CAAC,CAAC;QACF,IAAI,CAACwC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC9E,SAAS,EAAE;EACf;EAKAoF,eAAeA,CAAA;IACb,IAAI,CAACjG,gBAAgB,CAACkG,mCAAmC,CAAC;MACxD7C,IAAI,EAAE;QACJgC,YAAY,EAAE,IAAI,CAACpE,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3D2F,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACLjO,GAAG,CAACkO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACpK,YAAY,GAAGkK,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAC/E,KAAK,GAAG8E,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UACzB,IAAI,CAACzJ,aAAa,CAACoH,OAAO,CAAC1C,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAClE,kBAAkB,CAAC4G,OAAO,CAAC1C,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACsE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC3B,GAAG,CAAEmB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAACxK,YAAY,CAACwK,OAAO;cAClClK,cAAc,EAAEgK,CAAC,CAAChK,cAAc;cAChCtC,WAAW,EAAEsM,CAAC,CAACtM,WAAW,KAAKsM,CAAC,CAACS,gBAAgB,GAAG,CAACT,CAAC,CAACS,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9E/D,KAAK,EAAEsD,CAAC,CAACtD,KAAK;cACduD,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1BjH,SAAS,EAAEuG,CAAC,CAACvG,SAAS;cACtBF,KAAK,EAAEyG,CAAC,CAACzG,KAAK;cACdC,KAAK,EAAEwG,CAAC,CAACxG,KAAK;cACdxB,SAAS,EAAEgI,CAAC,CAAChI,SAAS,GAAGgI,CAAC,CAAChI,SAAS,GAAG,GAAGgI,CAAC,CAACzG,KAAK,IAAIyG,CAAC,CAACxG,KAAK,IAAIwG,CAAC,CAACvG,SAAS,EAAE;cAC7E6E,WAAW,EAAE0B,CAAC,CAAC1B,WAAW;cAC1BgB,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5BpH,cAAc,EAAE8H,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAAC9H,cAAc;cACtDiI,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBxJ,aAAa,EAAEqJ,CAAC,CAACW,qBAAqB,CAAChN,MAAM,GAAG,IAAI,CAACiN,0BAA0B,CAAC,IAAI,CAAC7J,aAAa,EAAEiJ,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAChK;cAAa,CAAE;cAAEQ,kBAAkB,EAAE6I,CAAC,CAAC1B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC9G,kBAAkB,EAAEyI,CAAC,CAAC1B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACnH;cAAkB,CAAE;cAC9Rd,WAAW,EAAE2J,CAAC,CAACW,qBAAqB,CAAChN,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzEoC,YAAY,EAAE,EAAE;cAAiBqC,eAAe,EAAE4H,CAAC,CAACG,OAAO,GAAG,IAAI,CAAC5E,cAAc,CAACyE,CAAC,CAACG,OAAO,EAAE,IAAI,CAACtG,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC1IpG,iBAAiB,EAAE,CAAC;cACpB8J,WAAW,EAAE;aACd;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACgC,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACtE,SAAS,EAAE;EACf;EAEA3C,mBAAmBA,CAAC+E,WAAgB;IAClC,IAAIA,WAAW,CAACjF,eAAe,IAAIiF,WAAW,CAACjF,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E2D,WAAW,CAACnF,cAAc,GAAG,CAAC;IAChC;EACF;EACA2I,4BAA4BA,CAACtL,IAAW;IACtC,KAAK,IAAIkG,IAAI,IAAIlG,IAAI,EAAE;MACrB,IAAIkG,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOY,IAAI,CAACqF,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAACnE,MAAM,CAACmB,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC;EACjD;EAEAmD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpBnE,MAAM,CAACmB,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC,CACvBoD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACjJ,eAAoB,EAAEjB,kBAAuB;IAC1D,IAAIiB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACyH,0BAA0B,CAAChK,kBAAkB,CAAC;IAC5D;EACF;EAEAmK,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAChF,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIiF,KAAK,CAAC7N,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO6N,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC1L,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL+N,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACvL,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpEoM,aAAa,EAAE5L,YAAY,CAAC,CAAC,CAAC,CAACyG,SAAS,IAAI,IAAI;QAChDoF,QAAQ,EAAE7L,YAAY,CAAC,CAAC,CAAC,CAAC2G,KAAK,CAAClH,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAiM,UAAUA,CAAA;IACR,IAAI,CAACtH,KAAK,CAACuH,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMxG,IAAI,IAAI,IAAI,CAACyG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACtG,IAAI,CAAC0E,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACvG,IAAI,CAACvD,cAAe,EAAE;QACvD8J,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIvG,IAAI,CAAC6D,YAAY,IAAI7D,IAAI,CAACvD,cAAc,EAAE;QAC5C,IAAIuD,IAAI,CAACvD,cAAc,GAAGuD,IAAI,CAAC6D,YAAY,IAAI7D,IAAI,CAACvD,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACqC,KAAK,CAAC4H,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG1G,IAAI,CAAC6D,YAAY,GAAG,KAAK7D,IAAI,CAACzD,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACiK,kBAAkB,IAAK,CAACxG,IAAI,CAACzD,SAAU,EAAE;QAC5CiK,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACxH,KAAK,CAAC4H,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACzH,KAAK,CAAC4H,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC1H,KAAK,CAAC4H,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAAClB,GAAG,CAAEwD,CAAM,IAAI;MAChE,OAAO;QACLrM,cAAc,EAAEqM,CAAC,CAACrM,cAAc,GAAGqM,CAAC,CAACrM,cAAc,GAAG,IAAI;QAC1D0G,KAAK,EAAE2F,CAAC,CAACtM,YAAY,GAAG,IAAI,CAAC0L,UAAU,CAACY,CAAC,CAACtM,YAAY,CAAC,GAAGH,SAAS;QACnEqK,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAAC1L,aAAa,CAAC;QAC9D+J,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACxH,KAAK,GAAG,IAAI,GAAG,IAAI,CAACpF,YAAY,CAACwK,OAAO;QACtD3G,KAAK,EAAE8I,CAAC,CAAC9I,KAAK;QACdC,KAAK,EAAE6I,CAAC,CAAC7I,KAAK;QACdC,SAAS,EAAE4I,CAAC,CAAC5I,SAAS;QACtBzB,SAAS,EAAEqK,CAAC,CAACrK,SAAS;QAAE;QACxBsG,WAAW,EAAE+D,CAAC,CAACjK,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC2H,cAAc,CAACgB,CAAC,CAACjK,eAAe,EAAEiK,CAAC,CAAClL,kBAAkB,CAAC,IAAI,IAAI;QACxHmI,YAAY,EAAE+C,CAAC,CAAC/C,YAAY;QAC5BpH,cAAc,EAAEmK,CAAC,CAACnK,cAAc;QAChCiI,OAAO,EAAEkC,CAAC,CAACjK,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACmI,UAAU,EAAE;IACjB,IAAI,IAAI,CAACtH,KAAK,CAACgI,aAAa,CAAC5O,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACwG,OAAO,CAACqI,aAAa,CAAC,IAAI,CAACjI,KAAK,CAACgI,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACzH,KAAK,EAAE;MACd,IAAI,CAAC2H,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACtI,gBAAgB,CAACuI,oCAAoC,CAAC;MACzDlF,IAAI,EAAE,IAAI,CAACyE;KACZ,CAAC,CAACjH,SAAS,CAAC2E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3F,OAAO,CAACyI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAACpE,WAAW;MAC9B0H,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3CtH,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAAC4I,sCAAsC,CAAC;MAC3DvF,IAAI,EAAE,IAAI,CAACqF;KACZ,CAAC,CAAC7H,SAAS,CAAC2E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC3F,OAAO,CAACyI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAjC,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM1H,IAAI,IAAIwH,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK9H,IAAI,IAAI6H,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC1H,IAAI,CAAC,GAAG,CAAC,CAAC2H,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKF7H,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACoJ,8DAA8D,CAAC;MAC5FhG,IAAI,EAAE,IAAI,CAACpC;KACZ,CAAC,CAACsE,IAAI,CACLjO,GAAG,CAACkO,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC/I,aAAa,GAAG,IAAI,CAAC8J,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACpF,SAAS,EAAE;EACf;EACA4H,MAAMA,CAAA;IACJ,IAAI,CAACnI,aAAa,CAACiC,IAAI,CAAC;MACtB+G,MAAM;MACNC,OAAO,EAAE,IAAI,CAACtI;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACoJ,IAAI,EAAE;EACtB;;;uCArfW7J,4CAA4C,EAAAhI,EAAA,CAAA8R,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhS,EAAA,CAAA8R,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAlS,EAAA,CAAA8R,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAApS,EAAA,CAAA8R,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAtS,EAAA,CAAA8R,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAAvS,EAAA,CAAA8R,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAzS,EAAA,CAAA8R,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA3S,EAAA,CAAA8R,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA7S,EAAA,CAAA8R,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAA9S,EAAA,CAAA8R,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5ChL,4CAA4C;MAAAiL,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnT,EAAA,CAAAoT,0BAAA,EAAApT,EAAA,CAAAqT,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChDvD3T,EADF,CAAAQ,cAAA,iBAA0B,qBACR;UACdR,EAAA,CAAAC,SAAA,qBAAiC;UACnCD,EAAA,CAAAmB,YAAA,EAAiB;UACjBnB,EAAA,CAAAQ,cAAA,sBAA2B;UACzBR,EAAA,CAAAC,SAAA,YAA+C;UAE7CD,EADF,CAAAQ,cAAA,aAAuB,YAC4B;UAAAR,EAAA,CAAAuB,MAAA,4CAAO;UAAAvB,EAAA,CAAAmB,YAAA,EAAK;UAE7DnB,EAAA,CAAAmC,UAAA,IAAA0R,oEAAA,4BAA8E;UAgMlF7T,EADE,CAAAmB,YAAA,EAAM,EACO;UAEbnB,EADF,CAAAQ,cAAA,wBAAyF,kBACc;UAA9DR,EAAA,CAAAS,UAAA,mBAAAqT,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvD9Q,EAAA,CAAAuB,MAAA,sBACF;UAAAvB,EAAA,CAAAmB,YAAA,EAAS;UACTnB,EAAA,CAAAQ,cAAA,kBAAgG;UAAhER,EAAA,CAAAS,UAAA,mBAAAsT,+EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAClDrQ,EAAA,CAAAuB,MAAA,sBACF;UAEJvB,EAFI,CAAAmB,YAAA,EAAS,EACM,EACT;;;;;UAzMkCnB,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAA0T,GAAA,CAAA5F,kBAAA,CAAuB;UAkMLhO,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAA8T,OAAA,GAAAJ,GAAA,CAAAjQ,YAAA,CAAAC,OAAA,cAAAoQ,OAAA,KAAAnQ,SAAA,GAAAmQ,OAAA,SAA0C;UAG/ChU,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAA+T,OAAA,GAAAL,GAAA,CAAAjQ,YAAA,CAAAC,OAAA,cAAAqQ,OAAA,KAAApQ,SAAA,GAAAoQ,OAAA,SAA0C;;;qBDhKvFxU,YAAY,EAAAmT,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,IAAA,EAAEvU,YAAY,EAAAwU,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAE3V,gBAAgB,EAAgBK,eAAe;MAAAuV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}