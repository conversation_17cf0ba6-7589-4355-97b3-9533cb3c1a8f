{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ApproveStatusPipe {\n  transform(value) {\n    switch (value) {\n      case true:\n        return '已通過';\n      case false:\n        return '已駁回';\n      case null:\n        return '待審核';\n      case undefined:\n        return '待審核';\n      default:\n        return '待審核';\n    }\n  }\n  static {\n    this.ɵfac = function ApproveStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApproveStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"approveStatus\",\n      type: ApproveStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["ApproveStatusPipe", "transform", "value", "undefined", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\approveStatus.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from \"@angular/core\";\r\n\r\n@Pipe({\r\n  name: 'approveStatus',\r\n  standalone: true\r\n})\r\n\r\nexport class ApproveStatusPipe implements PipeTransform {\r\n  transform(value: boolean | null | undefined) {\r\n    switch(value){\r\n      case true: \r\n       return '已通過'\r\n      case false:\r\n        return '已駁回'\r\n      case null:\r\n        return '待審核'\r\n      case undefined:\r\n        return '待審核';\r\n      default:\r\n        return '待審核'\r\n    }\r\n  }\r\n}"], "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;EAC5BC,SAASA,CAACC,KAAiC;IACzC,QAAOA,KAAK;MACV,KAAK,IAAI;QACR,OAAO,KAAK;MACb,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,IAAI;QACP,OAAO,KAAK;MACd,KAAKC,SAAS;QACZ,OAAO,KAAK;MACd;QACE,OAAO,KAAK;IAChB;EACF;;;uCAdWH,iBAAiB;IAAA;EAAA;;;;YAAjBA,iBAAiB;MAAAI,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}