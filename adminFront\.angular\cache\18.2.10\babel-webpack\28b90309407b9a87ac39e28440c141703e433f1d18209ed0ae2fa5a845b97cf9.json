{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function (undefined) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var CipherParams = C_lib.CipherParams;\n    var C_enc = C.enc;\n    var Hex = C_enc.Hex;\n    var C_format = C.format;\n    var HexFormatter = C_format.Hex = {\n      /**\n       * Converts the ciphertext of a cipher params object to a hexadecimally encoded string.\n       *\n       * @param {CipherParams} cipherParams The cipher params object.\n       *\n       * @return {string} The hexadecimally encoded string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var hexString = CryptoJS.format.Hex.stringify(cipherParams);\n       */\n      stringify: function (cipherParams) {\n        return cipherParams.ciphertext.toString(Hex);\n      },\n      /**\n       * Converts a hexadecimally encoded ciphertext string to a cipher params object.\n       *\n       * @param {string} input The hexadecimally encoded string.\n       *\n       * @return {CipherParams} The cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipherParams = CryptoJS.format.Hex.parse(hexString);\n       */\n      parse: function (input) {\n        var ciphertext = Hex.parse(input);\n        return CipherParams.create({\n          ciphertext: ciphertext\n        });\n      }\n    };\n  })();\n  return CryptoJS.format.Hex;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}