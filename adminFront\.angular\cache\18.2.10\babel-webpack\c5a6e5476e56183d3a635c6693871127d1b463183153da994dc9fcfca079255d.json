{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Track {}\nexport class PlayerService {\n  constructor() {\n    this.playlist = [{\n      name: '<PERSON>\\'t Wanna Fight',\n      artist: 'Alabama Shakes',\n      url: 'https://p.scdn.co/mp3-preview/6156cdbca425a894972c02fca9d76c0b70e001af',\n      cover: 'assets/images/cover1.jpg'\n    }, {\n      name: '<PERSON><PERSON>',\n      artist: 'Daft Punk',\n      url: 'https://p.scdn.co/mp3-preview/92a04c7c0e96bf93a1b1b1cae7dfff1921969a7b',\n      cover: 'assets/images/cover2.jpg'\n    }, {\n      name: 'Come Together',\n      artist: 'Beatles',\n      url: 'https://p.scdn.co/mp3-preview/83090a4db6899eaca689ae35f69126dbe65d94c9',\n      cover: 'assets/images/cover3.jpg'\n    }];\n  }\n  random() {\n    this.current = Math.floor(Math.random() * this.playlist.length);\n    return this.playlist[this.current];\n  }\n  next() {\n    return this.getNextTrack();\n  }\n  prev() {\n    return this.getPrevTrack();\n  }\n  getNextTrack() {\n    if (this.current === this.playlist.length - 1) {\n      this.current = 0;\n    } else {\n      this.current++;\n    }\n    return this.playlist[this.current];\n  }\n  getPrevTrack() {\n    if (this.current === 0) {\n      this.current = this.playlist.length - 1;\n    } else {\n      this.current--;\n    }\n    return this.playlist[this.current];\n  }\n  static {\n    this.ɵfac = function PlayerService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlayerService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PlayerService,\n      factory: PlayerService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["Track", "PlayerService", "constructor", "playlist", "name", "artist", "url", "cover", "random", "current", "Math", "floor", "length", "next", "getNextTrack", "prev", "getPrevTrack", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\utils\\player.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\nexport class Track {\r\n  name?: string;\r\n  artist?: string;\r\n  url?: string;\r\n  cover?: string;\r\n}\r\n\r\n@Injectable()\r\nexport class PlayerService {\r\n  current?: number;\r\n  playlist: Track[] = [\r\n    {\r\n      name: 'Don\\'t Wanna Fight',\r\n      artist: 'Alabama Shakes',\r\n      url: 'https://p.scdn.co/mp3-preview/6156cdbca425a894972c02fca9d76c0b70e001af',\r\n      cover: 'assets/images/cover1.jpg',\r\n    },\r\n    {\r\n      name: 'Harder',\r\n      artist: 'Daft Punk',\r\n      url: 'https://p.scdn.co/mp3-preview/92a04c7c0e96bf93a1b1b1cae7dfff1921969a7b',\r\n      cover: 'assets/images/cover2.jpg',\r\n    },\r\n    {\r\n      name: 'Come Together',\r\n      artist: 'Beatles',\r\n      url: 'https://p.scdn.co/mp3-preview/83090a4db6899eaca689ae35f69126dbe65d94c9',\r\n      cover: 'assets/images/cover3.jpg',\r\n    },\r\n  ];\r\n\r\n  random(): Track {\r\n    this.current = Math.floor(Math.random() * this.playlist.length);\r\n    return this.playlist[this.current];\r\n  }\r\n\r\n  next(): Track {\r\n    return this.getNextTrack();\r\n  }\r\n\r\n  prev() {\r\n    return this.getPrevTrack();\r\n  }\r\n\r\n  private getNextTrack(): Track {\r\n    if (this.current === this.playlist.length - 1) {\r\n      this.current = 0;\r\n    } else {\r\n      this.current!++;\r\n    }\r\n\r\n    return this.playlist[this.current!];\r\n  }\r\n\r\n  private getPrevTrack(): Track {\r\n    if (this.current === 0) {\r\n      this.current = this.playlist.length - 1;\r\n    } else {\r\n      this.current!--;\r\n    }\r\n\r\n    return this.playlist[this.current!];\r\n  }\r\n}\r\n"], "mappings": ";AAEA,OAAM,MAAOA,KAAK;AAQlB,OAAM,MAAOC,aAAa;EAD1BC,YAAA;IAGE,KAAAC,QAAQ,GAAY,CAClB;MACEC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE,gBAAgB;MACxBC,GAAG,EAAE,wEAAwE;MAC7EC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,WAAW;MACnBC,GAAG,EAAE,wEAAwE;MAC7EC,KAAK,EAAE;KACR,EACD;MACEH,IAAI,EAAE,eAAe;MACrBC,MAAM,EAAE,SAAS;MACjBC,GAAG,EAAE,wEAAwE;MAC7EC,KAAK,EAAE;KACR,CACF;;EAEDC,MAAMA,CAAA;IACJ,IAAI,CAACC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACF,MAAM,EAAE,GAAG,IAAI,CAACL,QAAQ,CAACS,MAAM,CAAC;IAC/D,OAAO,IAAI,CAACT,QAAQ,CAAC,IAAI,CAACM,OAAO,CAAC;EACpC;EAEAI,IAAIA,CAAA;IACF,OAAO,IAAI,CAACC,YAAY,EAAE;EAC5B;EAEAC,IAAIA,CAAA;IACF,OAAO,IAAI,CAACC,YAAY,EAAE;EAC5B;EAEQF,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACL,OAAO,KAAK,IAAI,CAACN,QAAQ,CAACS,MAAM,GAAG,CAAC,EAAE;MAC7C,IAAI,CAACH,OAAO,GAAG,CAAC;IAClB,CAAC,MAAM;MACL,IAAI,CAACA,OAAQ,EAAE;IACjB;IAEA,OAAO,IAAI,CAACN,QAAQ,CAAC,IAAI,CAACM,OAAQ,CAAC;EACrC;EAEQO,YAAYA,CAAA;IAClB,IAAI,IAAI,CAACP,OAAO,KAAK,CAAC,EAAE;MACtB,IAAI,CAACA,OAAO,GAAG,IAAI,CAACN,QAAQ,CAACS,MAAM,GAAG,CAAC;IACzC,CAAC,MAAM;MACL,IAAI,CAACH,OAAQ,EAAE;IACjB;IAEA,OAAO,IAAI,CAACN,QAAQ,CAAC,IAAI,CAACM,OAAQ,CAAC;EACrC;;;uCAtDWR,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAgB,OAAA,EAAbhB,aAAa,CAAAiB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}