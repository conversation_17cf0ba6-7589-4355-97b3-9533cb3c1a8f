{"ast": null, "code": "import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport endOfYear from \"../endOfYear/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @param {Date|Number} date - the given year\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport default function eachWeekendOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfYear(dirtyDate);\n  var endDate = endOfYear(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}