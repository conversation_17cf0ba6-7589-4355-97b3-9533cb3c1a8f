{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nlet RegularNoticeComponent = class RegularNoticeComponent extends BaseComponent {\n  constructor(_allow) {\n    super(_allow);\n    this._allow = _allow;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n  }\n};\nRegularNoticeComponent = __decorate([Component({\n  selector: 'ngx-regular-notice',\n  templateUrl: './regular-notice.component.html',\n  styleUrls: ['./regular-notice.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, TypeMailPipe]\n})], RegularNoticeComponent);\nexport { RegularNoticeComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "TypeMailPipe", "SharedModule", "BaseComponent", "RegularNoticeComponent", "constructor", "_allow", "pageFirst", "pageSize", "pageIndex", "totalRecords", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\regular-notice\\regular-notice.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\n\r\n\r\n@Component({\r\n  selector: 'ngx-regular-notice',\r\n  templateUrl: './regular-notice.component.html',\r\n  styleUrls: ['./regular-notice.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe],\r\n})\r\n\r\nexport class RegularNoticeComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n \r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,mCAAmC;AAEhE,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAWpE,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAuB,SAAQD,aAAa;EACvDE,YACUC,MAAmB;IACzB,KAAK,CAACA,MAAM,CAAC;IADP,KAAAA,MAAM,GAANA,MAAM;IAGP,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;EALP;CAQnB;AAXYN,sBAAsB,GAAAO,UAAA,EARlCZ,SAAS,CAAC;EACTa,QAAQ,EAAE,oBAAoB;EAC9BC,WAAW,EAAE,iCAAiC;EAC9CC,SAAS,EAAE,CAAC,iCAAiC,CAAC;EAC9CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChB,YAAY,EAAEE,YAAY,EAAED,YAAY;CACnD,CAAC,C,EAEWG,sBAAsB,CAWlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}