{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetAppointmentByIdPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetAppointmentByIdPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetAppointmentByIdPost$Json.PATH = '/api/House/GetAppointmentByID';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetAppointmentByIdPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-appointment-by-id-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetAppoinmentResResponseBase } from '../../models/get-appoinment-res-response-base';\r\n\r\nexport interface ApiHouseGetAppointmentByIdPost$Json$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiHouseGetAppointmentByIdPost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseGetAppointmentByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetAppoinmentResResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetAppointmentByIdPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetAppoinmentResResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetAppointmentByIdPost$Json.PATH = '/api/House/GetAppointmentByID';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,mCAAmCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAmD,EAAEC,OAAqB;EAC/J,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,mCAAmC,CAACM,IAAI,EAAE,MAAM,CAAC;EACxF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAqD;EAC9D,CAAC,CAAC,CACH;AACH;AAEAb,mCAAmC,CAACM,IAAI,GAAG,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}