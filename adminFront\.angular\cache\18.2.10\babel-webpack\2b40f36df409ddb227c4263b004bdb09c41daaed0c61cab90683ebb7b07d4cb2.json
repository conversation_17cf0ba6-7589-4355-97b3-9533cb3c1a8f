{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let RoundPipe = /*#__PURE__*/(() => {\n  class RoundPipe {\n    transform(input) {\n      return Math.round(input);\n    }\n    static {\n      this.ɵfac = function RoundPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RoundPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"ngxRound\",\n        type: RoundPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return RoundPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}