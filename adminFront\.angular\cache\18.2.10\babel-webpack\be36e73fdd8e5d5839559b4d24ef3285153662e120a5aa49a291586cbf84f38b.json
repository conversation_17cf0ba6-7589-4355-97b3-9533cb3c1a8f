{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"fileInput\"];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 19);\n    i0.ɵɵtemplate(10, CustomerChangePictureComponent_tr_25_button_10_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(21);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u6A94\\u6848 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵelement(1, \"img\", 55);\n    i0.ɵɵelementStart(2, \"div\", 56);\n    i0.ɵɵelement(3, \"i\", 57);\n    i0.ɵɵtext(4, \"\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", file_r11.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵelementStart(2, \"span\", 60);\n    i0.ɵɵtext(3, \"PDF\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"span\", 63);\n    i0.ɵɵtext(3, \"CAD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵtemplate(2, CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_2_Template, 5, 1, \"div\", 48)(3, CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_3_Template, 4, 0, \"div\", 49)(4, CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_4_Template, 4, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 52);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_24_div_1_Template_span_click_7_listener() {\n      const i_r12 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r12));\n    });\n    i0.ɵɵelement(8, \"i\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImage(file_r11.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImage(file_r11.CFileType) && !ctx_r2.isCad(file_r11.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCad(file_r11.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r11.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r11.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_24_div_1_Template, 9, 5, \"div\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.imageUrlList);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"img\", 70);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r14.CFile));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 71);\n    i0.ɵɵelement(3, \"i\", 57);\n    i0.ɵɵtext(4, \"\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", file_r14.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const file_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r14.CFile));\n    });\n    i0.ɵɵelement(1, \"i\", 73);\n    i0.ɵɵelementStart(2, \"span\", 60);\n    i0.ɵɵtext(3, \"PDF\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const file_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r14.CFile));\n    });\n    i0.ɵɵelement(1, \"i\", 75);\n    i0.ɵɵelementStart(2, \"span\", 63);\n    i0.ɵɵtext(3, \"CAD\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_1_Template, 5, 1, \"div\", 48)(2, CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_2_Template, 4, 0, \"div\", 67)(3, CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_3_Template, 4, 0, \"div\", 68);\n    i0.ɵɵelementStart(4, \"p\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isPDFString(file_r14.CFile) && !ctx_r2.isCadString(file_r14.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPDFString(file_r14.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCadString(file_r14.CFile));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r14.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template, 6, 4, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ref_r17 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r17));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 22)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 23)(4, \"div\", 24)(5, \"label\", 25, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"label\", 27);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 24)(14, \"label\", 29);\n    i0.ɵɵtext(15, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 30);\n    i0.ɵɵtemplate(17, CustomerChangePictureComponent_ng_template_32_button_17_Template, 3, 0, \"button\", 31);\n    i0.ɵɵelementStart(18, \"small\", 32);\n    i0.ɵɵtext(19, \" \\u652F\\u63F4\\u683C\\u5F0F\\uFF1A\\u5716\\u7247 (JPG, JPEG)\\u3001PDF\\u3001CAD (DWG, DXF) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"input\", 33, 2);\n    i0.ɵɵlistener(\"change\", function CustomerChangePictureComponent_ng_template_32_Template_input_change_20_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 34);\n    i0.ɵɵelement(23, \"label\", 35);\n    i0.ɵɵtemplate(24, CustomerChangePictureComponent_ng_template_32_div_24_Template, 2, 1, \"div\", 36)(25, CustomerChangePictureComponent_ng_template_32_div_25_Template, 2, 1, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 34)(27, \"label\", 38);\n    i0.ɵɵtext(28, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"textarea\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 14)(31, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_31_listener() {\n      const ref_r17 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r17));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, CustomerChangePictureComponent_ng_template_32_button_33_Template, 2, 0, \"button\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.isEdit && ctx_r2.SpecialChange.CIsApprove === null));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  detectFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\n        const firstFile = files[0];\n        const fileName = firstFile.name;\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\n      }\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!fileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          continue;\n        }\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            // 判斷檔案類型\n            let fileType = 1; // 預設為其他\n            const fileName = file.name.toLowerCase();\n            if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n              fileType = 2; // 圖片\n            } else if (fileName.endsWith('.pdf')) {\n              fileType = 1; // PDF\n            } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n              fileType = 3; // CAD\n            }\n            this.imageUrlList.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            if (this.imageUrlList.length === files.length) {\n              console.log('this.imageUrlList', this.imageUrlList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(extension) {\n    return extension.toLowerCase() === 'pdf';\n  }\n  removeFile(index) {\n    this.imageUrlList.splice(index, 1);\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"flex\", \"flex-col\"], [\"class\", \"btn btn-info mb-2\", 3, \"click\", 4, \"ngIf\"], [1, \"text-gray-500\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf, .dwg, .dxf\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"baseLabel\", \"\", 1, \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"class\", \"flex flex-wrap mt-2 file-type-container\", 4, \"ngIf\"], [\"class\", \"flex flex-wrap mt-2\", 4, \"ngIf\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mb-2\", 3, \"click\"], [1, \"fa\", \"fa-upload\", \"mr-2\"], [1, \"flex\", \"flex-wrap\", \"mt-2\", \"file-type-container\"], [\"class\", \"relative w-28 h-28 mr-3 mb-6 file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-6\", \"file-item\"], [1, \"w-full\", \"h-full\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full relative rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-6\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\", \"text-gray-600\", 3, \"title\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"absolute\", \"-top-2\", \"-right-2\", \"cursor-pointer\", \"bg-red-500\", \"text-white\", \"rounded-full\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-red-600\", \"transition-colors\", \"duration-200\", \"remove-btn\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-xs\"], [1, \"w-full\", \"h-full\", \"relative\", \"rounded-lg\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"src\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\", \"file-type-label\"], [1, \"fa\", \"fa-image\", \"mr-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-red-50\", \"rounded-lg\"], [1, \"fa\", \"fa-file-pdf-o\", \"text-red-500\", \"text-2xl\", \"mb-1\", \"pdf-icon\"], [1, \"text-red-600\", \"text-xs\", \"font-medium\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-green-50\", \"rounded-lg\"], [1, \"fa\", \"fa-cube\", \"text-green-600\", \"text-2xl\", \"mb-1\", \"cad-icon\"], [1, \"text-green-700\", \"text-xs\", \"font-medium\"], [1, \"flex\", \"flex-wrap\", \"mt-2\"], [\"class\", \"relative w-28 h-28 mr-3 mb-4 border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-4\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\", 3, \"click\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-6\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\", \"text-gray-600\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"click\", \"src\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-red-50\", \"rounded-lg\", 3, \"click\"], [1, \"fa\", \"fa-file-pdf-o\", \"text-red-500\", \"text-2xl\", \"mb-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-green-50\", \"rounded-lg\", 3, \"click\"], [1, \"fa\", \"fa-cube\", \"text-green-600\", \"text-2xl\", \"mb-1\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"table\", 10)(12, \"thead\")(13, \"tr\", 11)(14, \"th\", 12);\n          i0.ɵɵtext(15, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 12);\n          i0.ɵɵtext(17, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 12);\n          i0.ɵɵtext(19, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 12);\n          i0.ɵɵtext(21, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 12);\n          i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"tbody\");\n          i0.ɵɵtemplate(25, CustomerChangePictureComponent_tr_25_Template, 11, 5, \"tr\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"nb-card-footer\", 14)(27, \"ngb-pagination\", 15);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\")(29, \"div\", 14)(30, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(31, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(32, CustomerChangePictureComponent_ng_template_32_Template, 34, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.Calendar],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-type-label[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  background-color: rgba(0, 0, 0, 0.7);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.pdf-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n.cad-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.cad-icon[_ngcontent-%COMP%]:hover {\\n  transform: rotate(15deg);\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKOztBQUlFO0VBQ0UsZ0NBQUE7QUFESjtBQUdJO0VBQ0UsMkJBQUE7QUFETjtBQUtFO0VBQ0Usa0NBQUE7VUFBQSwwQkFBQTtFQUNBLG9DQUFBO0FBSEo7QUFNRTtFQUNFLGdDQUFBO0FBSko7QUFNSTtFQUNFLHFCQUFBO0FBSk47O0FBVUE7RUFDRSw0QkFBQTtBQVBGOztBQVVBO0VBQ0U7SUFDRSxVQUFBO0VBUEY7RUFTQTtJQUNFLFlBQUE7RUFQRjtBQUNGO0FBV0E7RUFDRSwrQkFBQTtBQVRGO0FBV0U7RUFDRSx3QkFBQTtBQVRKIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcblxyXG4vLyDmqpTmoYjpoZ7lnovpoa/npLrmqKPlvI9cclxuLmZpbGUtdHlwZS1jb250YWluZXIge1xyXG4gIC5maWxlLWl0ZW0ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcbiAgICBcclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC5maWxlLXR5cGUtbGFiZWwge1xyXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDRweCk7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNyk7XHJcbiAgfVxyXG4gIFxyXG4gIC5yZW1vdmUtYnRuIHtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG4gICAgXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gUERG5paH5Lu25ZyW5qiZ5YuV55WrXHJcbi5wZGYtaWNvbiB7XHJcbiAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcclxufVxyXG5cclxuQGtleWZyYW1lcyBwdWxzZSB7XHJcbiAgMCUsIDEwMCUge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICB9XHJcbiAgNTAlIHtcclxuICAgIG9wYWNpdHk6IDAuNztcclxuICB9XHJcbn1cclxuXHJcbi8vIENBROaWh+S7tuWcluaomeaXi+i9ieaViOaenFxyXG4uY2FkLWljb24ge1xyXG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XHJcbiAgXHJcbiAgJjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxNWRlZyk7XHJcbiAgfVxyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7O0FBSUU7RUFDRSxnQ0FBQTtBQURKO0FBR0k7RUFDRSwyQkFBQTtBQUROO0FBS0U7RUFDRSxrQ0FBQTtVQUFBLDBCQUFBO0VBQ0Esb0NBQUE7QUFISjtBQU1FO0VBQ0UsZ0NBQUE7QUFKSjtBQU1JO0VBQ0UscUJBQUE7QUFKTjs7QUFVQTtFQUNFLDRCQUFBO0FBUEY7O0FBVUE7RUFDRTtJQUNFLFVBQUE7RUFQRjtFQVNBO0lBQ0UsWUFBQTtFQVBGO0FBQ0Y7QUFXQTtFQUNFLCtCQUFBO0FBVEY7QUFXRTtFQUNFLHdCQUFBO0FBVEo7QUFFQSxnMkRBQWcyRCIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAjaWNvbmRpc3BsYXkge1xyXG4gICAgd2lkdGg6IDMxOHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgW2lkXj1cInBuX2lkX1wiXSB7XHJcbiAgICB6LWluZGV4OiAxMDtcclxufVxyXG5cclxuLy8gw6bCqsKUw6bCocKIw6nCocKew6XCnsKLw6nCocKvw6fCpMK6w6bCqMKjw6XCvMKPXHJcbi5maWxlLXR5cGUtY29udGFpbmVyIHtcclxuICAuZmlsZS1pdGVtIHtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG4gICAgXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xyXG4gICAgfVxyXG4gIH1cclxuICBcclxuICAuZmlsZS10eXBlLWxhYmVsIHtcclxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig0cHgpO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjcpO1xyXG4gIH1cclxuICBcclxuICAucmVtb3ZlLWJ0biB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluLW91dDtcclxuICAgIFxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIFBERsOmwpbCh8OkwrvCtsOlwpzClsOmwqjCmcOlwovClcOnwpXCq1xyXG4ucGRmLWljb24ge1xyXG4gIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgcHVsc2Uge1xyXG4gIDAlLCAxMDAlIHtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgfVxyXG4gIDUwJSB7XHJcbiAgICBvcGFjaXR5OiAwLjc7XHJcbiAgfVxyXG59XHJcblxyXG4vLyBDQUTDpsKWwofDpMK7wrbDpcKcwpbDpsKowpnDpsKXwovDqMK9wonDpsKVwojDpsKewpxcclxuLmNhZC1pY29uIHtcclxuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG4gIFxyXG4gICY6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMTVkZWcpO1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_25_button_10_Template", "ɵɵadvance", "ɵɵtextInterpolate", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "CustomerChangePictureComponent_ng_template_32_button_17_Template_button_click_0_listener", "_r8", "inputFile_r9", "click", "ɵɵelement", "file_r11", "data", "ɵɵsanitizeUrl", "CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_2_Template", "CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_3_Template", "CustomerChangePictureComponent_ng_template_32_div_24_div_1_div_4_Template", "CustomerChangePictureComponent_ng_template_32_div_24_div_1_Template_span_click_7_listener", "i_r12", "_r10", "index", "removeFile", "isImage", "CFileType", "isCad", "CFileName", "CustomerChangePictureComponent_ng_template_32_div_24_div_1_Template", "imageUrlList", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_1_Template_img_click_1_listener", "_r13", "file_r14", "openNewTab", "CFile", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_2_Template_div_click_0_listener", "_r15", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_3_Template_div_click_0_listener", "_r16", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_1_Template", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_2_Template", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_div_3_Template", "isPDFString", "isCadString", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template", "SpecialChange", "CFileRes", "CustomerChangePictureComponent_ng_template_32_button_33_Template_button_click_0_listener", "_r18", "ref_r17", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_32_button_17_Template", "CustomerChangePictureComponent_ng_template_32_Template_input_change_20_listener", "detectFiles", "CustomerChangePictureComponent_ng_template_32_div_24_Template", "CustomerChangePictureComponent_ng_template_32_div_25_Template", "CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_29_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_32_Template_button_click_31_listener", "onClose", "CustomerChangePictureComponent_ng_template_32_button_33_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "statusOptions", "value", "key", "label", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "clear", "required", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "event", "files", "target", "allowedTypes", "fileRegex", "firstFile", "fileName", "name", "fileNameWithoutExtension", "lastIndexOf", "i", "file", "test", "showErrorMSG", "includes", "type", "reader", "FileReader", "onload", "e", "fileType", "toLowerCase", "match", "endsWith", "push", "CFileBlood", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "str", "lowerStr", "isPdf", "extension", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_25_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_30_listener", "CustomerChangePictureComponent_ng_template_32_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate1", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }  detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n\r\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\r\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\r\n        const firstFile = files[0];\r\n        const fileName = firstFile.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\r\n      }\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.name)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n          continue;\r\n        }\r\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n          const reader = new FileReader(); \r\n          reader.onload = (e: any) => {\r\n            // 判斷檔案類型\r\n            let fileType = 1; // 預設為其他\r\n            const fileName = file.name.toLowerCase();\r\n            \r\n            if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n              fileType = 2; // 圖片\r\n            } else if (fileName.endsWith('.pdf')) {\r\n              fileType = 1; // PDF\r\n            } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n              fileType = 3; // CAD\r\n            }\r\n\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  isPDFString(str: any): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isCadString(str: any): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false\r\n  }\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1);\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <div class=\"flex flex-col\">\r\n          <button *ngIf=\"!(isEdit && SpecialChange.CIsApprove === null)\" class=\"btn btn-info mb-2\"\r\n            (click)=\"inputFile.click()\">\r\n            <i class=\"fa fa-upload mr-2\"></i>選擇檔案\r\n          </button>\r\n          <small class=\"text-gray-500\">\r\n            支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)\r\n          </small>\r\n        </div>\r\n        <input #inputFile type=\"file\" id=\"fileInput\" class=\"hidden\"\r\n          (change)=\"detectFiles($event)\" [disabled]=\"isEdit\" accept=\"image/jpeg, image/jpg, application/pdf, .dwg, .dxf\"\r\n          multiple>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label baseLabel class=\"align-self-start mr-4\" style=\"min-width:75px\">\r\n        </label>        <div class=\"flex flex-wrap mt-2 file-type-container\" *ngIf=\"!isEdit\">\r\n          <div *ngFor=\"let file of imageUrlList; let i = index\" class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n              <!-- 圖片類型 -->\r\n              <div *ngIf=\"isImage(file.CFileType)\" class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"file.data\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label\">\r\n                  <i class=\"fa fa-image mr-1\"></i>圖片\r\n                </div>\r\n              </div>\r\n              \r\n              <!-- PDF類型 -->\r\n              <div *ngIf=\"!isImage(file.CFileType) && !isCad(file.CFileType)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\">\r\n                <i class=\"fa fa-file-pdf-o text-red-500 text-2xl mb-1 pdf-icon\"></i>\r\n                <span class=\"text-red-600 text-xs font-medium\">PDF</span>\r\n              </div>\r\n              \r\n              <!-- CAD類型 -->\r\n              <div *ngIf=\"isCad(file.CFileType)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\">\r\n                <i class=\"fa fa-cube text-green-600 text-2xl mb-1 cad-icon\"></i>\r\n                <span class=\"text-green-700 text-xs font-medium\">CAD</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\" \r\n               [title]=\"file.CFileName\">{{ file.CFileName }}</p>\r\n            <span class=\"absolute -top-2 -right-2 cursor-pointer bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors duration-200 remove-btn\" \r\n                  (click)=\"removeFile(i)\" \r\n                  title=\"移除檔案\">\r\n              <i class=\"fa fa-times text-xs\"></i>\r\n            </span>\r\n          </div>\r\n        </div><div class=\"flex flex-wrap mt-2\" *ngIf=\"isEdit\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\" class=\"relative w-28 h-28 mr-3 mb-4 border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n            <!-- 圖片類型 -->\r\n            <div *ngIf=\"!isPDFString(file.CFile) && !isCadString(file.CFile)\" class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n              <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"file.CFile\" (click)=\"openNewTab(file.CFile)\">\r\n              <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded\">\r\n                <i class=\"fa fa-image mr-1\"></i>圖片\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- PDF類型 -->\r\n            <div *ngIf=\"isPDFString(file.CFile)\"\r\n              class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\"\r\n              (click)=\"openNewTab(file.CFile)\">\r\n              <i class=\"fa fa-file-pdf-o text-red-500 text-2xl mb-1\"></i>\r\n              <span class=\"text-red-600 text-xs font-medium\">PDF</span>\r\n            </div>\r\n            \r\n            <!-- CAD類型 -->\r\n            <div *ngIf=\"isCadString(file.CFile)\"\r\n              class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\"\r\n              (click)=\"openNewTab(file.CFile)\">\r\n              <i class=\"fa fa-cube text-green-600 text-2xl mb-1\"></i>\r\n              <span class=\"text-green-700 text-xs font-medium\">CAD</span>\r\n            </div>\r\n            \r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\" (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAuBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAN3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAuE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChFb,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IARCb,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAQ,WAAA,EAAiC;IACjCxB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAS,YAAA,CAAsB;IACtBzB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAU,SAAA,EAA8B;IAC9B1B,EAAA,CAAAqB,SAAA,GAAuE;IAAvErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,UAAA,kCAAAX,OAAA,CAAAW,UAAA,mCAAuE;IAErB3B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuB,QAAA,CAAc;;;;;;IAiDtE7B,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAE,UAAA,mBAAA4B,yFAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,GAAA;MAAA/B,EAAA,CAAAO,aAAA;MAAA,MAAAyB,YAAA,GAAAhC,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASsB,YAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAC3BjC,EAAA,CAAAkC,SAAA,YAAiC;IAAAlC,EAAA,CAAAY,MAAA,gCACnC;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAgBLb,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAkC,SAAA,cAAyE;IACzElC,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAY,MAAA,oBAClC;IACFZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAJmDb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,QAAAO,QAAA,CAAAC,IAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAiB;;;;;IAO1ErC,EAAA,CAAAC,cAAA,cACsG;IACpGD,EAAA,CAAAkC,SAAA,YAAoE;IACpElC,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACpDZ,EADoD,CAAAa,YAAA,EAAO,EACrD;;;;;IAGNb,EAAA,CAAAC,cAAA,cACwG;IACtGD,EAAA,CAAAkC,SAAA,YAAgE;IAChElC,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACtDZ,EADsD,CAAAa,YAAA,EAAO,EACvD;;;;;;IArBRb,EADF,CAAAC,cAAA,cAAqG,cAC4B;IAiB7HD,EAfA,CAAAmB,UAAA,IAAAmB,yEAAA,kBAA+F,IAAAC,yEAAA,kBASO,IAAAC,yEAAA,kBAOE;IAI1GxC,EAAA,CAAAa,YAAA,EAAM;IAENb,EAAA,CAAAC,cAAA,YAC4B;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACpDb,EAAA,CAAAC,cAAA,eAEmB;IADbD,EAAA,CAAAE,UAAA,mBAAAuC,0FAAA;MAAA,MAAAC,KAAA,GAAA1C,EAAA,CAAAI,aAAA,CAAAuC,IAAA,EAAAC,KAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAE3B1C,EAAA,CAAAkC,SAAA,YAAmC;IAEvClC,EADE,CAAAa,YAAA,EAAO,EACH;;;;;IA7BIb,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAwC,OAAA,CAAAX,QAAA,CAAAY,SAAA,EAA6B;IAQ7B/C,EAAA,CAAAqB,SAAA,EAAwD;IAAxDrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAwC,OAAA,CAAAX,QAAA,CAAAY,SAAA,MAAAzC,MAAA,CAAA0C,KAAA,CAAAb,QAAA,CAAAY,SAAA,EAAwD;IAOxD/C,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0C,KAAA,CAAAb,QAAA,CAAAY,SAAA,EAA2B;IAQhC/C,EAAA,CAAAqB,SAAA,EAAwB;IAAxBrB,EAAA,CAAA4B,UAAA,UAAAO,QAAA,CAAAc,SAAA,CAAwB;IAACjD,EAAA,CAAAqB,SAAA,EAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAa,QAAA,CAAAc,SAAA,CAAoB;;;;;IA3BpCjD,EAAA,CAAAC,cAAA,cAAqE;IACnFD,EAAA,CAAAmB,UAAA,IAAA+B,mEAAA,kBAAqG;IAiCvGlD,EAAA,CAAAa,YAAA,EAAM;;;;IAjCkBb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAA6C,YAAA,CAAiB;;;;;;IAqCnCnD,EADF,CAAAC,cAAA,cAA4H,cACf;IAAjCD,EAAA,CAAAE,UAAA,mBAAAkD,+FAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAC,QAAA,GAAAtD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAA1GxD,EAAA,CAAAa,YAAA,EAA2G;IAC3Gb,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAY,MAAA,oBAClC;IACFZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAJmDb,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAA4B,UAAA,QAAA0B,QAAA,CAAAE,KAAA,EAAAxD,EAAA,CAAAqC,aAAA,CAAkB;;;;;;IAO3ErC,EAAA,CAAAC,cAAA,cAEmC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAuD,+FAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAAJ,QAAA,GAAAtD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAChCxD,EAAA,CAAAkC,SAAA,YAA2D;IAC3DlC,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACpDZ,EADoD,CAAAa,YAAA,EAAO,EACrD;;;;;;IAGNb,EAAA,CAAAC,cAAA,cAEmC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAyD,+FAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAN,QAAA,GAAAtD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiD,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAChCxD,EAAA,CAAAkC,SAAA,YAAuD;IACvDlC,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAY,MAAA,UAAG;IACtDZ,EADsD,CAAAa,YAAA,EAAO,EACvD;;;;;IAvBRb,EAAA,CAAAC,cAAA,cAAyM;IAkBvMD,EAhBA,CAAAmB,UAAA,IAAA0C,yEAAA,kBAA4H,IAAAC,yEAAA,kBAUzF,IAAAC,yEAAA,kBAQA;IAKnC/D,EAAA,CAAAC,cAAA,YAA4F;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAClHZ,EADkH,CAAAa,YAAA,EAAI,EAChH;;;;;IAxBEb,EAAA,CAAAqB,SAAA,EAA0D;IAA1DrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA0D,WAAA,CAAAV,QAAA,CAAAE,KAAA,MAAAlD,MAAA,CAAA2D,WAAA,CAAAX,QAAA,CAAAE,KAAA,EAA0D;IAQ1DxD,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0D,WAAA,CAAAV,QAAA,CAAAE,KAAA,EAA6B;IAQ7BxD,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA2D,WAAA,CAAAX,QAAA,CAAAE,KAAA,EAA6B;IAOyDxD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAgC,QAAA,CAAAL,SAAA,CAAoB;;;;;IA1B9GjD,EAAA,CAAAC,cAAA,cAAgD;IACpDD,EAAA,CAAAmB,UAAA,IAAA+C,mEAAA,kBAAyM;IA2B3MlE,EAAA,CAAAa,YAAA,EAAM;;;;IA3BkBb,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAA6D,aAAA,CAAAC,QAAA,CAA2B;;;;;;IAsCnDpE,EAAA,CAAAC,cAAA,iBAAuF;IAAnCD,EAAA,CAAAE,UAAA,mBAAAmE,yFAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAO,aAAA,GAAAiE,SAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmE,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAACvE,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAhHxGb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4C;IADED,EAAA,CAAA0E,gBAAA,2BAAAC,2FAAAC,MAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8E,kBAAA,CAAAxE,MAAA,CAAAyE,iBAAA,CAAAvD,WAAA,EAAAoD,MAAA,MAAAtE,MAAA,CAAAyE,iBAAA,CAAAvD,WAAA,GAAAoD,MAAA;MAAA,OAAA5E,EAAA,CAAAU,WAAA,CAAAkE,MAAA;IAAA,EAA2C;IAE3F5E,EAD8C,CAAAa,YAAA,EAAa,EACrD;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAA0E,gBAAA,2BAAAM,uFAAAJ,MAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8E,kBAAA,CAAAxE,MAAA,CAAAyE,iBAAA,CAAAtD,YAAA,EAAAmD,MAAA,MAAAtE,MAAA,CAAAyE,iBAAA,CAAAtD,YAAA,GAAAmD,MAAA;MAAA,OAAA5E,EAAA,CAAAU,WAAA,CAAAkE,MAAA;IAAA,EAA4C;IAE3G5E,EAFE,CAAAa,YAAA,EACwB,EACpB;IACJb,EADU,CAAAC,cAAA,eAAwB,iBACoC;IACpED,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAA8D,gEAAA,qBAC8B;IAG9BjF,EAAA,CAAAC,cAAA,iBAA6B;IAC3BD,EAAA,CAAAY,MAAA,6FACF;IACFZ,EADE,CAAAa,YAAA,EAAQ,EACJ;IACNb,EAAA,CAAAC,cAAA,oBAEW;IADTD,EAAA,CAAAE,UAAA,oBAAAgF,gFAAAN,MAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAA6E,WAAA,CAAAP,MAAA,CAAmB;IAAA,EAAC;IAElC5E,EAHE,CAAAa,YAAA,EAEW,EACP;IAENb,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAAkC,SAAA,iBACQ;IAkCFlC,EAlCU,CAAAmB,UAAA,KAAAiE,6DAAA,kBAAqE,KAAAC,6DAAA,kBAkC/B;IA6BxDrF,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAAkD,iBAEvB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,oBACoF;IAA/CD,EAAA,CAAA0E,gBAAA,2BAAAY,0FAAAV,MAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAyE,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA8E,kBAAA,CAAAxE,MAAA,CAAAyE,iBAAA,CAAAQ,cAAA,EAAAX,MAAA,MAAAtE,MAAA,CAAAyE,iBAAA,CAAAQ,cAAA,GAAAX,MAAA;MAAA,OAAA5E,EAAA,CAAAU,WAAA,CAAAkE,MAAA;IAAA,EAA8C;IACrF5E,EADsF,CAAAa,YAAA,EAAW,EAC3F;IAGJb,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAsF,gFAAA;MAAA,MAAAjB,OAAA,GAAAvE,EAAA,CAAAI,aAAA,CAAAyE,GAAA,EAAAL,SAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmF,OAAA,CAAAlB,OAAA,CAAY;IAAA,EAAC;IAACvE,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAAuE,gEAAA,qBAAuF;IAG7F1F,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IAlHNb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA2F,kBAAA,wEAAArF,MAAA,CAAAsF,KAAA,CAAAC,UAAA,cAAAvF,MAAA,CAAAsF,KAAA,CAAAE,MAAA,OACF;IAOgB9F,EAAA,CAAAqB,SAAA,GAA0B;IAA6CrB,EAAvE,CAAA4B,UAAA,2BAA0B,wBAA4C,kBAAkB;IACtD5B,EAAA,CAAA+F,gBAAA,YAAAzF,MAAA,CAAAyE,iBAAA,CAAAvD,WAAA,CAA2C;IACvFxB,EADwF,CAAA4B,UAAA,aAAAtB,MAAA,CAAA0F,MAAA,CAAmB,uBACrF;IAMqChG,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAA+F,gBAAA,YAAAzF,MAAA,CAAAyE,iBAAA,CAAAtD,YAAA,CAA4C;IACvGzB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA0F,MAAA,CAAmB;IAMVhG,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAA4B,UAAA,WAAAtB,MAAA,CAAA0F,MAAA,IAAA1F,MAAA,CAAA6D,aAAA,CAAAxC,UAAA,WAAoD;IAS9B3B,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA0F,MAAA,CAAmB;IAMkBhG,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA0F,MAAA,CAAa;IAkC3ChG,EAAA,CAAAqB,SAAA,EAAY;IAAZrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAA0F,MAAA,CAAY;IAkClDhG,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAA0F,MAAA,CAAmB;IAAgBhG,EAAA,CAAA+F,gBAAA,YAAAzF,MAAA,CAAAyE,iBAAA,CAAAQ,cAAA,CAA8C;IAK9CvF,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAA0F,MAAA,CAAa;;;ADnJ1D,OAAM,MAAOC,8BAA+B,SAAQnG,aAAa;EAC/DoG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAxD,YAAY,GAAU,EAAE;IACxB,KAAA6C,MAAM,GAAG,KAAK;IACd,KAAAY,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAEQ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IA2PzB,KAAAC,YAAY,GAAU,EAAE;EAhRN;EA8BTC,QAAQA,CAAA;IAEf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAAC9F,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAACgC,QAAQ,CAACZ,KAAK,EAAE2E,MAAM,CAACC,IAAI,CAAChG,IAAI,CAACgC,QAAQ,CAACZ,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAwE,oBAAoBA,CAAA;IAClB,IAAI,CAAC1B,qBAAqB,CAAC+B,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACR,OAAO;QACtBS,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;KAElB,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAV,YAAYA,CAAA;IACV,IAAI,CAAC1B,aAAa,CAACwC,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACjB;;KAElB,CAAC,CAACR,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACjD,KAAK,GAAG8C,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAACrD,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAoD,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC9C,qBAAqB,CAAC+C,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC7B,SAAS,CAACmB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC1E,aAAa,GAAGuE,GAAG,CAACE,OAAO;QAChC,IAAI,CAAC7D,iBAAiB,GAAG;UACvBQ,cAAc,EAAE,IAAI,CAACpB,aAAa,CAACoB,cAAc;UACjD+D,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9BnG,YAAY,EAAE,IAAI,CAAC0C,aAAa,CAAC1C,YAAY;UAC7CuH,QAAQ,EAAE,IAAI,CAACjB,OAAO;UACtBwB,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAACpF,aAAa,CAAC3C,WAAW,EAAE;UAClC,IAAI,CAACuD,iBAAiB,CAACvD,WAAW,GAAG,IAAIgI,IAAI,CAAC,IAAI,CAACrF,aAAa,CAAC3C,WAAW,CAAC;QAC/E;QACA,IAAI,CAAC4E,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA1E,mBAAmBA,CAAC0E,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACpD,KAAK,CAACqD,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClD,OAAO,CAACmD,aAAa,CAAC,IAAI,CAACvD,KAAK,CAACqD,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACpD,qBAAqB,CAACuD,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACmB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpC,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BmB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAGArH,MAAMA,CAACwI,GAAQ;IACb,IAAI,CAAChG,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6C,MAAM,GAAG,KAAK;IACnB,IAAI,CAACjB,iBAAiB,GAAG;MACvBQ,cAAc,EAAE,EAAE;MAClB+D,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9BpG,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBuH,QAAQ,EAAE,IAAI,CAACjB,OAAO;MACtBwB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAACnD,aAAa,CAACgC,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAjI,MAAMA,CAACiI,GAAQ,EAAEgB,aAAkB;IACjC,IAAI,CAAChH,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACkD,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAK,UAAUA,CAAA;IACR,IAAI,CAACpD,KAAK,CAAC+D,KAAK,EAAE;IAClB,IAAI,CAAC/D,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACvD,WAAW,CAAC;IACjE,IAAI,CAAC6E,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACtD,YAAY,CAAC;IAClE,IAAI,CAAC4E,KAAK,CAACgE,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtF,iBAAiB,CAACQ,cAAc,CAAC;EACtE;EAEAhE,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3B,MAAM,CAAC2B,WAAW,CAAC,CAAC8I,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAACrI,IAAI;IAClB;IACA,OAAOoI,KAAK;EACd;EAEAV,WAAWA,CAAA;IACT,MAAMY,MAAM,GAAG;MACb,GAAG,IAAI,CAAC3F,iBAAiB;MACzBwE,kBAAkB,EAAE,IAAI,CAACpG;KAC1B;IACD,IAAI,CAACoH,gBAAgB,CAACG,MAAM,CAACnB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAACxE,iBAAiB,CAACvD,WAAW,EAAE;MACtCkJ,MAAM,CAAClJ,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACwD,iBAAiB,CAACvD,WAAW,CAAC;IAC1E;IACA,OAAOkJ,MAAM;EACf;EAEAjF,OAAOA,CAAC0D,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEAW,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAAGzF,WAAWA,CAAC6F,KAAU;IACvB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMwB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAChJ,MAAMC,SAAS,GAAG,2BAA2B;MAE7C;MACA,IAAI,CAAC,IAAI,CAACrG,iBAAiB,CAACtD,YAAY,IAAIwJ,KAAK,CAACtB,MAAM,GAAG,CAAC,EAAE;QAC5D,MAAM0B,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMK,QAAQ,GAAGD,SAAS,CAACE,IAAI;QAC/B,MAAMC,wBAAwB,GAAGF,QAAQ,CAACP,SAAS,CAAC,CAAC,EAAEO,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIH,QAAQ;QAC7F,IAAI,CAACvG,iBAAiB,CAACtD,YAAY,GAAG+J,wBAAwB;MAChE;MAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACtB,MAAM,EAAE+B,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGV,KAAK,CAACS,CAAC,CAAC;QACrB,IAAI,CAACN,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UAC9B,IAAI,CAAC9E,OAAO,CAACoF,YAAY,CAAC,kCAAkC,CAAC;UAC7D;QACF;QACA,IAAIV,YAAY,CAACW,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,IAAIX,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UACjE,MAAMS,MAAM,GAAG,IAAIC,UAAU,EAAE;UAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB;YACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAClB,MAAMd,QAAQ,GAAGK,IAAI,CAACJ,IAAI,CAACc,WAAW,EAAE;YAExC,IAAIf,QAAQ,CAACgB,KAAK,CAAC,mBAAmB,CAAC,EAAE;cACvCF,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAId,QAAQ,CAACiB,QAAQ,CAAC,MAAM,CAAC,EAAE;cACpCH,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAId,QAAQ,CAACgB,KAAK,CAAC,cAAc,CAAC,EAAE;cACzCF,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;YAEA,IAAI,CAACjJ,YAAY,CAACqJ,IAAI,CAAC;cACrBpK,IAAI,EAAE+J,CAAC,CAACjB,MAAM,CAACR,MAAM;cACrB+B,UAAU,EAAE,IAAI,CAAC9B,kBAAkB,CAACwB,CAAC,CAACjB,MAAM,CAACR,MAAM,CAAC;cACpDzH,SAAS,EAAE0I,IAAI,CAACJ,IAAI;cACpBxI,SAAS,EAAEqJ;aACZ,CAAC;YAEF,IAAI,IAAI,CAACjJ,YAAY,CAACwG,MAAM,KAAKsB,KAAK,CAACtB,MAAM,EAAE;cAC7C+C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACxJ,YAAY,CAAC;cACnD,IAAI,IAAI,CAACyJ,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAChG,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACDmF,MAAM,CAACc,aAAa,CAACnB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAEA3H,WAAWA,CAAC+I,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACV,WAAW,EAAE,CAACE,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAtI,WAAWA,CAAC8I,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,MAAMC,QAAQ,GAAGD,GAAG,CAACV,WAAW,EAAE;MAClC,OAAOW,QAAQ,CAACT,QAAQ,CAAC,MAAM,CAAC,IAAIS,QAAQ,CAACT,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACAzJ,OAAOA,CAACsJ,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEApJ,KAAKA,CAACoJ,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAa,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACb,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIAxJ,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACO,YAAY,CAACgK,MAAM,CAACvK,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAwK,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACjG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5F,EAAE,IAAI0F,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAACrE,GAAQ,GACpB;EAEAsE,UAAUA,CAACzC,KAAU,EAAEpI,KAAa;IAClC,IAAI8K,IAAI,GAAG,IAAI,CAACtG,YAAY,CAACxE,KAAK,CAAC,CAACY,KAAK,CAACmK,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvG,YAAY,CAACxE,KAAK,CAAC,CAACY,KAAK,CAACoK,IAAI,EAAE,IAAI,CAACxG,YAAY,CAACxE,KAAK,CAAC,CAACY,KAAK,CAACuI,IAAI,CAAC;IAC5H,IAAI8B,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAG1C,KAAK,CAACE,MAAM,CAACrE,KAAK,GAAG,GAAG,GAAG,IAAI,CAACO,YAAY,CAACxE,KAAK,CAAC,CAACsK,SAAS,EAAE,EAAE;MAAEnB,IAAI,EAAE,IAAI,CAAC3E,YAAY,CAACxE,KAAK,CAAC,CAACY,KAAK,CAACuI;IAAI,CAAE,CAAC;IACjJ,IAAI,CAAC3E,YAAY,CAACxE,KAAK,CAAC,CAACY,KAAK,GAAGqK,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAACpH,aAAa,CAAC6F,IAAI,CAAC;MACtBwB,MAAM;MACNC,OAAO,EAAE,IAAI,CAACrG;KACf,CAAC;IACF,IAAI,CAAClB,QAAQ,CAACwH,IAAI,EAAE;EACtB;EACA3K,UAAUA,CAAC4K,GAAQ;IACjB,IAAIA,GAAG,EAAEhG,MAAM,CAACC,IAAI,CAAC+F,GAAG,EAAE,QAAQ,CAAC;EACrC;;;uCAxTWlI,8BAA8B,EAAAjG,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxO,EAAA,CAAAoO,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA1O,EAAA,CAAAoO,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAA5O,EAAA,CAAAoO,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAA7O,EAAA,CAAAoO,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA/O,EAAA,CAAAoO,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAjP,EAAA,CAAAoO,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAnP,EAAA,CAAAoO,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BpJ,8BAA8B;MAAAqJ,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCxBzCzP,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAAwO,gDAAA,oBAAoF;UAI1F3P,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,iBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAAyO,6CAAA,kBAA+E;UAavF5P,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAA0E,gBAAA,wBAAAmL,8EAAAjL,MAAA;YAAA5E,EAAA,CAAAI,aAAA,CAAA0P,GAAA;YAAA9P,EAAA,CAAA8E,kBAAA,CAAA4K,GAAA,CAAAxI,SAAA,EAAAtC,MAAA,MAAA8K,GAAA,CAAAxI,SAAA,GAAAtC,MAAA;YAAA,OAAA5E,EAAA,CAAAU,WAAA,CAAAkE,MAAA;UAAA,EAAoB;UAClC5E,EAAA,CAAAE,UAAA,wBAAA2P,8EAAAjL,MAAA;YAAA5E,EAAA,CAAAI,aAAA,CAAA0P,GAAA;YAAA,OAAA9P,EAAA,CAAAU,WAAA,CAAcgP,GAAA,CAAAzF,WAAA,CAAArF,MAAA,CAAmB;UAAA,EAAC;UAEtC5E,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAE,UAAA,mBAAA6P,iEAAA;YAAA/P,EAAA,CAAAI,aAAA,CAAA0P,GAAA;YAAA,OAAA9P,EAAA,CAAAU,WAAA,CAASgP,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzD/N,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAA6O,sDAAA,kCAAAhQ,EAAA,CAAAiQ,sBAAA,CAAiE;;;UAxD7DjQ,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAAkQ,kBAAA,wEAAAR,GAAA,CAAAzG,UAAA,MACF;UAQ4EjJ,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA4B,UAAA,SAAA8N,GAAA,CAAAS,QAAA,CAAc;UAkB7DnQ,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA4B,UAAA,YAAA8N,GAAA,CAAA5G,iBAAA,CAAuB;UAelC9I,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAA+F,gBAAA,SAAA2J,GAAA,CAAAxI,SAAA,CAAoB;UAAuBlH,EAAtB,CAAA4B,UAAA,aAAA8N,GAAA,CAAAzI,QAAA,CAAqB,mBAAAyI,GAAA,CAAAvI,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}