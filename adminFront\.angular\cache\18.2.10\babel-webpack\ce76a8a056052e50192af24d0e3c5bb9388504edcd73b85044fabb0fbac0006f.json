{"ast": null, "code": "var defaultOptions = {};\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}", "map": {"version": 3, "names": ["defaultOptions", "getDefaultOptions", "setDefaultOptions", "newOptions"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/_lib/defaultOptions/index.js"], "sourcesContent": ["var defaultOptions = {};\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}"], "mappings": "AAAA,IAAIA,cAAc,GAAG,CAAC,CAAC;AACvB,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,OAAOD,cAAc;AACvB;AACA,OAAO,SAASE,iBAAiBA,CAACC,UAAU,EAAE;EAC5CH,cAAc,GAAGG,UAAU;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}