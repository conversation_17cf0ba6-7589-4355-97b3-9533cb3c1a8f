{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBuildCaseDeleteBuildCasePost$Json } from '../fn/build-case/api-build-case-delete-build-case-post-json';\nimport { apiBuildCaseDeleteBuildCasePost$Plain } from '../fn/build-case/api-build-case-delete-build-case-post-plain';\nimport { apiBuildCaseGetAllBuildCasePost$Json } from '../fn/build-case/api-build-case-get-all-build-case-post-json';\nimport { apiBuildCaseGetAllBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-all-build-case-post-plain';\nimport { apiBuildCaseGetAnnouncementPost$Json } from '../fn/build-case/api-build-case-get-announcement-post-json';\nimport { apiBuildCaseGetAnnouncementPost$Plain } from '../fn/build-case/api-build-case-get-announcement-post-plain';\nimport { apiBuildCaseGetBuildCaseByIdPost$Json } from '../fn/build-case/api-build-case-get-build-case-by-id-post-json';\nimport { apiBuildCaseGetBuildCaseByIdPost$Plain } from '../fn/build-case/api-build-case-get-build-case-by-id-post-plain';\nimport { apiBuildCaseGetBuildCaseFilePost$Json } from '../fn/build-case/api-build-case-get-build-case-file-post-json';\nimport { apiBuildCaseGetBuildCaseFilePost$Plain } from '../fn/build-case/api-build-case-get-build-case-file-post-plain';\nimport { apiBuildCaseGetBuildCaseListPost$Json } from '../fn/build-case/api-build-case-get-build-case-list-post-json';\nimport { apiBuildCaseGetBuildCaseListPost$Plain } from '../fn/build-case/api-build-case-get-build-case-list-post-plain';\nimport { apiBuildCaseGetDisclaimerPost$Json } from '../fn/build-case/api-build-case-get-disclaimer-post-json';\nimport { apiBuildCaseGetDisclaimerPost$Plain } from '../fn/build-case/api-build-case-get-disclaimer-post-plain';\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-json';\nimport { apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain } from '../fn/build-case/api-build-case-get-house-and-floor-by-build-case-id-post-plain';\nimport { apiBuildCaseGetSystemInstructionPost$Json } from '../fn/build-case/api-build-case-get-system-instruction-post-json';\nimport { apiBuildCaseGetSystemInstructionPost$Plain } from '../fn/build-case/api-build-case-get-system-instruction-post-plain';\nimport { apiBuildCaseGetUserBuildCasePost$Json } from '../fn/build-case/api-build-case-get-user-build-case-post-json';\nimport { apiBuildCaseGetUserBuildCasePost$Plain } from '../fn/build-case/api-build-case-get-user-build-case-post-plain';\nimport { apiBuildCaseSaveBuildCasePost$Json } from '../fn/build-case/api-build-case-save-build-case-post-json';\nimport { apiBuildCaseSaveBuildCasePost$Plain } from '../fn/build-case/api-build-case-save-build-case-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let BuildCaseService = /*#__PURE__*/(() => {\n  class BuildCaseService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiBuildCaseGetBuildCaseListPost()` */\n    static {\n      this.ApiBuildCaseGetBuildCaseListPostPath = '/api/BuildCase/GetBuildCaseList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetBuildCaseListPost$Plain$Response(params, context) {\n      return apiBuildCaseGetBuildCaseListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetBuildCaseListPost$Plain(params, context) {\n      return this.apiBuildCaseGetBuildCaseListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseListPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetBuildCaseListPost$Json$Response(params, context) {\n      return apiBuildCaseGetBuildCaseListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseListPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetBuildCaseListPost$Json(params, context) {\n      return this.apiBuildCaseGetBuildCaseListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetAnnouncementPost()` */\n    static {\n      this.ApiBuildCaseGetAnnouncementPostPath = '/api/BuildCase/GetAnnouncement';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetAnnouncementPost$Plain$Response(params, context) {\n      return apiBuildCaseGetAnnouncementPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetAnnouncementPost$Plain(params, context) {\n      return this.apiBuildCaseGetAnnouncementPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetAnnouncementPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetAnnouncementPost$Json$Response(params, context) {\n      return apiBuildCaseGetAnnouncementPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetAnnouncementPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetAnnouncementPost$Json(params, context) {\n      return this.apiBuildCaseGetAnnouncementPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetBuildCaseFilePost()` */\n    static {\n      this.ApiBuildCaseGetBuildCaseFilePostPath = '/api/BuildCase/GetBuildCaseFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseFilePost$Plain$Response(params, context) {\n      return apiBuildCaseGetBuildCaseFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseFilePost$Plain(params, context) {\n      return this.apiBuildCaseGetBuildCaseFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseFilePost$Json$Response(params, context) {\n      return apiBuildCaseGetBuildCaseFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseFilePost$Json(params, context) {\n      return this.apiBuildCaseGetBuildCaseFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetDisclaimerPost()` */\n    static {\n      this.ApiBuildCaseGetDisclaimerPostPath = '/api/BuildCase/GetDisclaimer';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetDisclaimerPost$Plain$Response(params, context) {\n      return apiBuildCaseGetDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetDisclaimerPost$Plain(params, context) {\n      return this.apiBuildCaseGetDisclaimerPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetDisclaimerPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetDisclaimerPost$Json$Response(params, context) {\n      return apiBuildCaseGetDisclaimerPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetDisclaimerPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetDisclaimerPost$Json(params, context) {\n      return this.apiBuildCaseGetDisclaimerPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetSystemInstructionPost()` */\n    static {\n      this.ApiBuildCaseGetSystemInstructionPostPath = '/api/BuildCase/GetSystemInstruction';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetSystemInstructionPost$Plain$Response(params, context) {\n      return apiBuildCaseGetSystemInstructionPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetSystemInstructionPost$Plain(params, context) {\n      return this.apiBuildCaseGetSystemInstructionPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetSystemInstructionPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetSystemInstructionPost$Json$Response(params, context) {\n      return apiBuildCaseGetSystemInstructionPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetSystemInstructionPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetSystemInstructionPost$Json(params, context) {\n      return this.apiBuildCaseGetSystemInstructionPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost()` */\n    static {\n      this.ApiBuildCaseGetHouseAndFloorByBuildCaseIdPostPath = '/api/BuildCase/GetHouseAndFloorByBuildCaseId';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params, context) {\n      return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain(params, context) {\n      return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params, context) {\n      return apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(params, context) {\n      return this.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetUserBuildCasePost()` */\n    static {\n      this.ApiBuildCaseGetUserBuildCasePostPath = '/api/BuildCase/GetUserBuildCase';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetUserBuildCasePost$Plain$Response(params, context) {\n      return apiBuildCaseGetUserBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetUserBuildCasePost$Plain(params, context) {\n      return this.apiBuildCaseGetUserBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetUserBuildCasePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetUserBuildCasePost$Json$Response(params, context) {\n      return apiBuildCaseGetUserBuildCasePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetUserBuildCasePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetUserBuildCasePost$Json(params, context) {\n      return this.apiBuildCaseGetUserBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetAllBuildCasePost()` */\n    static {\n      this.ApiBuildCaseGetAllBuildCasePostPath = '/api/BuildCase/GetAllBuildCase';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetAllBuildCasePost$Plain$Response(params, context) {\n      return apiBuildCaseGetAllBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetAllBuildCasePost$Plain(params, context) {\n      return this.apiBuildCaseGetAllBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetAllBuildCasePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetAllBuildCasePost$Json$Response(params, context) {\n      return apiBuildCaseGetAllBuildCasePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetAllBuildCasePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetAllBuildCasePost$Json(params, context) {\n      return this.apiBuildCaseGetAllBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseDeleteBuildCasePost()` */\n    static {\n      this.ApiBuildCaseDeleteBuildCasePostPath = '/api/BuildCase/DeleteBuildCase';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseDeleteBuildCasePost$Plain$Response(params, context) {\n      return apiBuildCaseDeleteBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseDeleteBuildCasePost$Plain(params, context) {\n      return this.apiBuildCaseDeleteBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseDeleteBuildCasePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseDeleteBuildCasePost$Json$Response(params, context) {\n      return apiBuildCaseDeleteBuildCasePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseDeleteBuildCasePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseDeleteBuildCasePost$Json(params, context) {\n      return this.apiBuildCaseDeleteBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseGetBuildCaseByIdPost()` */\n    static {\n      this.ApiBuildCaseGetBuildCaseByIdPostPath = '/api/BuildCase/GetBuildCaseByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params, context) {\n      return apiBuildCaseGetBuildCaseByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseByIdPost$Plain(params, context) {\n      return this.apiBuildCaseGetBuildCaseByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseGetBuildCaseByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseByIdPost$Json$Response(params, context) {\n      return apiBuildCaseGetBuildCaseByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseGetBuildCaseByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseGetBuildCaseByIdPost$Json(params, context) {\n      return this.apiBuildCaseGetBuildCaseByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseSaveBuildCasePost()` */\n    static {\n      this.ApiBuildCaseSaveBuildCasePostPath = '/api/BuildCase/SaveBuildCase';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseSaveBuildCasePost$Plain$Response(params, context) {\n      return apiBuildCaseSaveBuildCasePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseSaveBuildCasePost$Plain(params, context) {\n      return this.apiBuildCaseSaveBuildCasePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseSaveBuildCasePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseSaveBuildCasePost$Json$Response(params, context) {\n      return apiBuildCaseSaveBuildCasePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseSaveBuildCasePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseSaveBuildCasePost$Json(params, context) {\n      return this.apiBuildCaseSaveBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    apiGetAllBuildCaseForSelectPost$Json(params, context) {\n      return this.apiBuildCaseGetAllBuildCasePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    apiGetAllBuildCaseForSelectPost$Json$Response(params, context) {\n      return apiBuildCaseGetAllBuildCasePost$Json(this.http, this.rootUrl, params, context);\n    }\n    static {\n      this.ɵfac = function BuildCaseService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BuildCaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BuildCaseService,\n        factory: BuildCaseService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BuildCaseService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}