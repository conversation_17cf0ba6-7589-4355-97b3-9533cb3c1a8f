{"ast": null, "code": "import * as CryptoJ<PERSON> from \"crypto-js\";\nimport * as i0 from \"@angular/core\";\nexport class CryptoService {\n  static {\n    this._key = CryptoJS.enc.Utf8.parse(\"DVROrKiHmncUgWBj\" /* CRYPTO.TOKEN_KEY */);\n  }\n  static {\n    this._iv = CryptoJS.enc.Utf8.parse(\"xcsJYPvUkxjdLi8b\" /* CRYPTO.TOKEN_IV */);\n  }\n  static EncryptUsingAES256(req) {\n    let result = CryptoJS.AES.encrypt(JSON.stringify(req), this._key, {\n      keySize: 16,\n      iv: this._iv,\n      mode: CryptoJS.mode.ECB,\n      padding: CryptoJS.pad.Pkcs7\n    }).toString();\n    return result;\n  }\n  static DecryptUsingAES256(encrypted) {\n    let result = CryptoJS.AES.decrypt(encrypted, this._key, {\n      keySize: 16,\n      iv: this._iv,\n      mode: CryptoJS.mode.ECB,\n      padding: CryptoJS.pad.Pkcs7\n    }).toString(CryptoJS.enc.Utf8);\n    return result;\n  }\n  static {\n    this.ɵfac = function CryptoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CryptoService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CryptoService,\n      factory: CryptoService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["CryptoJS", "CryptoService", "_key", "enc", "Utf8", "parse", "_iv", "EncryptUsingAES256", "req", "result", "AES", "encrypt", "JSON", "stringify", "keySize", "iv", "mode", "ECB", "padding", "pad", "Pkcs7", "toString", "DecryptUsingAES256", "encrypted", "decrypt", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\services\\crypto.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport * as CryptoJ<PERSON> from \"crypto-js\";\r\nimport { CRYPTO } from \"../constant/constant\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport abstract class CryptoService {\r\n  private static _key: any = CryptoJS.enc.Utf8.parse(CRYPTO.TOKEN_KEY);\r\n  private static _iv: any = CryptoJS.enc.Utf8.parse(CRYPTO.TOKEN_IV);\r\n\r\n  public static EncryptUsingAES256(req: string): string {\r\n    let result = CryptoJS.AES.encrypt(JSON.stringify(req), this._key, {\r\n      keySize: 16,\r\n      iv: this._iv,\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    }).toString();\r\n\r\n    return result;\r\n  }\r\n\r\n  public static DecryptUsingAES256(encrypted: string): string {\r\n    let result = CryptoJS.AES.decrypt(encrypted, this._key, {\r\n      keySize: 16,\r\n      iv: this._iv,\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    }).toString(CryptoJS.enc.Utf8);\r\n\r\n    return result;\r\n  }\r\n}\r\n\r\n"], "mappings": "AACA,OAAO,KAAKA,QAAQ,MAAM,WAAW;;AAMrC,OAAM,MAAgBC,aAAa;;IAClB,KAAAC,IAAI,GAAQF,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACC,KAAK,2CAAkB;EAAC;;IACtD,KAAAC,GAAG,GAAQN,QAAQ,CAACG,GAAG,CAACC,IAAI,CAACC,KAAK,0CAAiB;EAAC;EAE5D,OAAOE,kBAAkBA,CAACC,GAAW;IAC1C,IAAIC,MAAM,GAAGT,QAAQ,CAACU,GAAG,CAACC,OAAO,CAACC,IAAI,CAACC,SAAS,CAACL,GAAG,CAAC,EAAE,IAAI,CAACN,IAAI,EAAE;MAChEY,OAAO,EAAE,EAAE;MACXC,EAAE,EAAE,IAAI,CAACT,GAAG;MACZU,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAElB,QAAQ,CAACmB,GAAG,CAACC;KACvB,CAAC,CAACC,QAAQ,EAAE;IAEb,OAAOZ,MAAM;EACf;EAEO,OAAOa,kBAAkBA,CAACC,SAAiB;IAChD,IAAId,MAAM,GAAGT,QAAQ,CAACU,GAAG,CAACc,OAAO,CAACD,SAAS,EAAE,IAAI,CAACrB,IAAI,EAAE;MACtDY,OAAO,EAAE,EAAE;MACXC,EAAE,EAAE,IAAI,CAACT,GAAG;MACZU,IAAI,EAAEhB,QAAQ,CAACgB,IAAI,CAACC,GAAG;MACvBC,OAAO,EAAElB,QAAQ,CAACmB,GAAG,CAACC;KACvB,CAAC,CAACC,QAAQ,CAACrB,QAAQ,CAACG,GAAG,CAACC,IAAI,CAAC;IAE9B,OAAOK,MAAM;EACf;;;uCAxBoBR,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAwB,OAAA,EAAbxB,aAAa,CAAAyB,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}