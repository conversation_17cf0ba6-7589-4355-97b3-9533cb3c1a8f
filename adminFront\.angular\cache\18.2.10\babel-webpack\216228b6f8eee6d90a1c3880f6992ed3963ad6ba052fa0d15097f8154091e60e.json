{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbAuthModule, NbDummyAuthStrategy } from '@nebular/auth';\nimport { NbSecurityModule, NbRoleProvider } from '@nebular/security';\nimport { of as observableOf } from 'rxjs';\nimport { throwIfAlreadyLoaded } from './module-import-guard';\nimport { AnalyticsService, LayoutService, PlayerService, SeoService, StateService } from './utils';\nimport { UserData } from './data/users';\nimport { ElectricityData } from './data/electricity';\nimport { SmartTableData } from './data/smart-table';\nimport { UserActivityData } from './data/user-activity';\nimport { OrdersChartData } from './data/orders-chart';\nimport { ProfitChartData } from './data/profit-chart';\nimport { TrafficListData } from './data/traffic-list';\nimport { OrdersProfitChartData } from './data/orders-profit-chart';\nimport { TrafficBarData } from './data/traffic-bar';\nimport { ProfitBarAnimationChartData } from './data/profit-bar-animation-chart';\nimport { TemperatureHumidityData } from './data/temperature-humidity';\nimport { SolarData } from './data/solar';\nimport { TrafficChartData } from './data/traffic-chart';\nimport { StatsBarData } from './data/stats-bar';\nimport { CountryOrderData } from './data/country-order';\nimport { StatsProgressBarData } from './data/stats-progress-bar';\nimport { VisitorsAnalyticsData } from './data/visitors-analytics';\nimport { SecurityCamerasData } from './data/security-cameras';\nimport { UserService } from './mock/users.service';\nimport { ElectricityService } from './mock/electricity.service';\nimport { SmartTableService } from './mock/smart-table.service';\nimport { UserActivityService } from './mock/user-activity.service';\nimport { OrdersChartService } from './mock/orders-chart.service';\nimport { ProfitChartService } from './mock/profit-chart.service';\nimport { TrafficListService } from './mock/traffic-list.service';\n// import { EarningService } from './mock/earning.service';\nimport { OrdersProfitChartService } from './mock/orders-profit-chart.service';\nimport { TrafficBarService } from './mock/traffic-bar.service';\nimport { ProfitBarAnimationChartService } from './mock/profit-bar-animation-chart.service';\nimport { TemperatureHumidityService } from './mock/temperature-humidity.service';\nimport { SolarService } from './mock/solar.service';\nimport { TrafficChartService } from './mock/traffic-chart.service';\nimport { StatsBarService } from './mock/stats-bar.service';\nimport { CountryOrderService } from './mock/country-order.service';\nimport { StatsProgressBarService } from './mock/stats-progress-bar.service';\nimport { VisitorsAnalyticsService } from './mock/visitors-analytics.service';\nimport { SecurityCamerasService } from './mock/security-cameras.service';\nimport { MockDataModule } from './mock/mock-data.module';\nimport * as i0 from \"@angular/core\";\nconst socialLinks = [{\n  url: 'https://github.com/akveo/nebular',\n  target: '_blank',\n  icon: 'github'\n}, {\n  url: 'https://www.facebook.com/akveo/',\n  target: '_blank',\n  icon: 'facebook'\n}, {\n  url: 'https://twitter.com/akveo_inc',\n  target: '_blank',\n  icon: 'twitter'\n}];\nconst DATA_SERVICES = [{\n  provide: UserData,\n  useClass: UserService\n}, {\n  provide: ElectricityData,\n  useClass: ElectricityService\n}, {\n  provide: SmartTableData,\n  useClass: SmartTableService\n}, {\n  provide: UserActivityData,\n  useClass: UserActivityService\n}, {\n  provide: OrdersChartData,\n  useClass: OrdersChartService\n}, {\n  provide: ProfitChartData,\n  useClass: ProfitChartService\n}, {\n  provide: TrafficListData,\n  useClass: TrafficListService\n},\n// { provide: EarningData, useClass: EarningService },\n{\n  provide: OrdersProfitChartData,\n  useClass: OrdersProfitChartService\n}, {\n  provide: TrafficBarData,\n  useClass: TrafficBarService\n}, {\n  provide: ProfitBarAnimationChartData,\n  useClass: ProfitBarAnimationChartService\n}, {\n  provide: TemperatureHumidityData,\n  useClass: TemperatureHumidityService\n}, {\n  provide: SolarData,\n  useClass: SolarService\n}, {\n  provide: TrafficChartData,\n  useClass: TrafficChartService\n}, {\n  provide: StatsBarData,\n  useClass: StatsBarService\n}, {\n  provide: CountryOrderData,\n  useClass: CountryOrderService\n}, {\n  provide: StatsProgressBarData,\n  useClass: StatsProgressBarService\n}, {\n  provide: VisitorsAnalyticsData,\n  useClass: VisitorsAnalyticsService\n}, {\n  provide: SecurityCamerasData,\n  useClass: SecurityCamerasService\n}];\nexport class NbSimpleRoleProvider extends NbRoleProvider {\n  getRole() {\n    // here you could provide any role based on any auth flow\n    return observableOf('guest');\n  }\n}\nexport const NB_CORE_PROVIDERS = [...MockDataModule.forRoot().providers, ...DATA_SERVICES, ...NbAuthModule.forRoot({\n  strategies: [NbDummyAuthStrategy.setup({\n    name: 'email',\n    delay: 3000\n  })],\n  forms: {\n    login: {\n      socialLinks: socialLinks\n    },\n    register: {\n      socialLinks: socialLinks\n    }\n  }\n}).providers, NbSecurityModule.forRoot({\n  accessControl: {\n    guest: {\n      view: '*'\n    },\n    user: {\n      parent: 'guest',\n      create: '*',\n      edit: '*',\n      remove: '*'\n    }\n  }\n}).providers, {\n  provide: NbRoleProvider,\n  useClass: NbSimpleRoleProvider\n}, AnalyticsService, LayoutService, PlayerService, SeoService, StateService];\nexport let CoreModule = /*#__PURE__*/(() => {\n  class CoreModule {\n    constructor(parentModule) {\n      throwIfAlreadyLoaded(parentModule, 'CoreModule');\n    }\n    static forRoot() {\n      return {\n        ngModule: CoreModule,\n        providers: [...NB_CORE_PROVIDERS]\n      };\n    }\n    static {\n      this.ɵfac = function CoreModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: CoreModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, NbAuthModule]\n      });\n    }\n  }\n  return CoreModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}