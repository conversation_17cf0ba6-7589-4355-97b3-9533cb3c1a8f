{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"fileInput\"];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 19);\n    i0.ɵɵtemplate(10, CustomerChangePictureComponent_tr_25_button_10_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 49);\n  }\n  if (rf & 2) {\n    const file_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r9.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1, \"CAD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_25_div_1_img_1_Template, 1, 1, \"img\", 44)(2, CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_2_Template, 2, 0, \"span\", 45)(3, CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_3_Template, 2, 0, \"span\", 45);\n    i0.ɵɵelementStart(4, \"p\", 46);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 47);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template_span_click_6_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r10));\n    });\n    i0.ɵɵelement(7, \"i\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImage(file_r9.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImage(file_r9.CFileType) && !ctx_r2.isCad(file_r9.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCad(file_r9.CFileType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r9.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template, 8, 4, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.imageUrlList);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_26_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 53);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_26_div_1_img_1_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r12.CFile));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r12.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r12.CFile));\n    });\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_3_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r12.CFile));\n    });\n    i0.ɵɵtext(1, \"CAD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_26_div_1_img_1_Template, 1, 1, \"img\", 51)(2, CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_2_Template, 2, 0, \"span\", 52)(3, CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementStart(4, \"p\", 46);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isPDFString(file_r12.CFile) && !ctx_r2.isCadString(file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPDFString(file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isCadString(file_r12.CFile));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r12.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_26_div_1_Template, 6, 4, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r15));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 22)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 23)(4, \"div\", 24)(5, \"label\", 25, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"label\", 27);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 29)(14, \"div\", 30)(15, \"label\", 31);\n    i0.ɵɵtext(16, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"input\", 33, 2);\n    i0.ɵɵlistener(\"change\", function CustomerChangePictureComponent_ng_template_32_Template_input_change_18_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 34);\n    i0.ɵɵelement(21, \"i\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 29);\n    i0.ɵɵelement(24, \"label\", 35);\n    i0.ɵɵtemplate(25, CustomerChangePictureComponent_ng_template_32_div_25_Template, 2, 1, \"div\", 36)(26, CustomerChangePictureComponent_ng_template_32_div_26_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 29)(28, \"label\", 37);\n    i0.ɵɵtext(29, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 14)(32, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_32_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r15));\n    });\n    i0.ɵɵtext(33, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, CustomerChangePictureComponent_ng_template_32_button_34_Template, 2, 0, \"button\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"required-field\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"accept\", ctx_r2.fileUploadConfig.acceptAttribute)(\"disabled\", ctx_r2.fileUploadConfigWithState.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"opacity-50\", ctx_r2.fileUploadConfigWithState.disabled)(\"cursor-pointer\", !ctx_r2.fileUploadConfigWithState.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.fileUploadConfig.buttonIcon + \" mr-2\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.fileUploadConfig.buttonText, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    // 檔案上傳配置\n    this.fileUploadConfig = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '選樣結果',\n      helpText: '',\n      required: false,\n      disabled: false,\n      autoFillName: true,\n      buttonText: '選擇檔案',\n      buttonIcon: 'fa-solid fa-upload',\n      maxFileSize: 10\n    };\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  // 處理共用元件的檔案上傳事件\n  onFileUploaded(result) {\n    // 將檔案加入到 imageUrlList\n    this.imageUrlList.push({\n      data: `data:${result.CFileUpload.type};base64,${result.CFile}`,\n      CFileBlood: result.CFile,\n      CFileName: result.CName,\n      CFileType: result.CFileType\n    });\n    // 如果圖面名稱為空且啟用自動填入，使用檔案名稱（去除副檔名）\n    if (this.fileUploadConfig.autoFillName && !this.formSpecialChange.CDrawingName) {\n      const fileName = result.CName;\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n      this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\n    }\n  }\n  // 處理檔案清除事件\n  onFileCleared() {\n    // 清除最後一個檔案或根據需要清除\n    // 這裡可能需要根據實際需求調整\n  }\n  // 動態配置，根據編輯狀態調整\n  get fileUploadConfigWithState() {\n    return {\n      ...this.fileUploadConfig,\n      disabled: this.isEdit && this.SpecialChange?.CIsApprove === null\n    };\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  detectFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\n        const firstFile = files[0];\n        const fileName = firstFile.name;\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\n      }\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!fileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          continue;\n        }\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            let fileType;\n            if (file.type.startsWith('image/')) {\n              fileType = 2; // 圖片\n            } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n              fileType = 3; // CAD檔案\n            } else {\n              fileType = 1; // PDF\n            }\n            this.imageUrlList.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            if (this.imageUrlList.length === files.length) {\n              console.log('this.imageUrlList', this.imageUrlList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(extension) {\n    return extension.toLowerCase() === 'pdf';\n  }\n  removeFile(index) {\n    this.imageUrlList.splice(index, 1);\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"fileInput\", 1, \"label\", \"col-3\", 2, \"min-width\", \"75px\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", \"multiple\", \"\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"accept\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [\"baseLabel\", \"\", 1, \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"class\", \"flex flex-wrap mt-2\", 4, \"ngIf\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\"], [\"class\", \"relative w-24 h-24 mr-2 mb-2 border\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-24\", \"h-24\", \"mr-2\", \"mb-2\", \"border\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-4\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\"], [1, \"absolute\", \"top-0\", \"right-0\", \"cursor-pointer\", \"bg-white\", \"rounded-full\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-red-600\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"click\", \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"table\", 10)(12, \"thead\")(13, \"tr\", 11)(14, \"th\", 12);\n          i0.ɵɵtext(15, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 12);\n          i0.ɵɵtext(17, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 12);\n          i0.ɵɵtext(19, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 12);\n          i0.ɵɵtext(21, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 12);\n          i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"tbody\");\n          i0.ɵɵtemplate(25, CustomerChangePictureComponent_tr_25_Template, 11, 5, \"tr\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"nb-card-footer\", 14)(27, \"ngb-pagination\", 15);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\")(29, \"div\", 14)(30, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(31, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(32, CustomerChangePictureComponent_ng_template_32_Template, 35, 26, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.Calendar],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7QUFDQSw0ZEFBNGQiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAgI2ljb25kaXNwbGF5IHtcclxuICAgIHdpZHRoOiAzMThweDtcclxufVxyXG5cclxuOjpuZy1kZWVwIFtpZF49XCJwbl9pZF9cIl0ge1xyXG4gICAgei1pbmRleDogMTA7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_25_button_10_Template", "ɵɵadvance", "ɵɵtextInterpolate", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "ɵɵelement", "file_r9", "data", "ɵɵsanitizeUrl", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_2_Template", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_span_3_Template", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template_span_click_6_listener", "i_r10", "_r8", "index", "removeFile", "isImage", "CFileType", "isCad", "CFileName", "CustomerChangePictureComponent_ng_template_32_div_25_div_1_Template", "imageUrlList", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_img_1_Template_img_click_0_listener", "_r11", "file_r12", "openNewTab", "CFile", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_2_Template_span_click_0_listener", "_r13", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_3_Template_span_click_0_listener", "_r14", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_img_1_Template", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_2_Template", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_span_3_Template", "isPDFString", "isCadString", "CustomerChangePictureComponent_ng_template_32_div_26_div_1_Template", "SpecialChange", "CFileRes", "CustomerChangePictureComponent_ng_template_32_button_34_Template_button_click_0_listener", "_r16", "ref_r15", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_32_Template_input_change_18_listener", "detectFiles", "CustomerChangePictureComponent_ng_template_32_div_25_Template", "CustomerChangePictureComponent_ng_template_32_div_26_Template", "CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_30_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_32_Template_button_click_32_listener", "onClose", "CustomerChangePictureComponent_ng_template_32_button_34_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "ɵɵclassProp", "fileUploadConfig", "acceptAttribute", "fileUploadConfigWithState", "disabled", "ɵɵclassMap", "buttonIcon", "ɵɵtextInterpolate1", "buttonText", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "acceptedTypes", "acceptedFileRegex", "label", "helpText", "required", "autoFillName", "maxFileSize", "statusOptions", "value", "key", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "clear", "format", "deleteDataFields", "array", "item", "result", "onFileUploaded", "push", "CFileUpload", "type", "CFileBlood", "CName", "fileName", "fileNameWithoutExtension", "substring", "lastIndexOf", "onFileCleared", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "event", "files", "target", "allowedTypes", "fileRegex", "firstFile", "name", "i", "file", "test", "showErrorMSG", "includes", "reader", "FileReader", "onload", "e", "fileType", "startsWith", "toLowerCase", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "str", "endsWith", "lowerStr", "isPdf", "extension", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_25_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_30_listener", "CustomerChangePictureComponent_ng_template_32_Template", "ɵɵtemplateRefExtractor", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent, FileUploadConfig, FileUploadResult } from '../../components/file-upload/file-upload.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n\r\n  // 檔案上傳配置\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', '.dwg', '.dxf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n    label: '選樣結果',\r\n    helpText: '',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: true,\r\n    buttonText: '選擇檔案',\r\n    buttonIcon: 'fa-solid fa-upload',\r\n    maxFileSize: 10\r\n  };\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  // 處理共用元件的檔案上傳事件\r\n  onFileUploaded(result: FileUploadResult) {\r\n    // 將檔案加入到 imageUrlList\r\n    this.imageUrlList.push({\r\n      data: `data:${result.CFileUpload.type};base64,${result.CFile}`,\r\n      CFileBlood: result.CFile,\r\n      CFileName: result.CName,\r\n      CFileType: result.CFileType\r\n    });\r\n\r\n    // 如果圖面名稱為空且啟用自動填入，使用檔案名稱（去除副檔名）\r\n    if (this.fileUploadConfig.autoFillName && !this.formSpecialChange.CDrawingName) {\r\n      const fileName = result.CName;\r\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n      this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\r\n    }\r\n  }\r\n\r\n  // 處理檔案清除事件\r\n  onFileCleared() {\r\n    // 清除最後一個檔案或根據需要清除\r\n    // 這裡可能需要根據實際需求調整\r\n  }\r\n\r\n  // 動態配置，根據編輯狀態調整\r\n  get fileUploadConfigWithState(): FileUploadConfig {\r\n    return {\r\n      ...this.fileUploadConfig,\r\n      disabled: this.isEdit && this.SpecialChange?.CIsApprove === null\r\n    };\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  } detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n\r\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\r\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\r\n        const firstFile = files[0];\r\n        const fileName = firstFile.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\r\n      }\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.name)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n          continue;\r\n        }\r\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n          const reader = new FileReader();\r\n\r\n          reader.onload = (e: any) => {\r\n            let fileType: number;\r\n            if (file.type.startsWith('image/')) {\r\n              fileType = 2; // 圖片\r\n            } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n              fileType = 3; // CAD檔案\r\n            } else {\r\n              fileType = 1; // PDF\r\n            }\r\n\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  isPDFString(str: any): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isCadString(str: any): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false\r\n  }\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1);\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"fileInput\" class=\"label col-3\" [class.required-field]=\"false\" style=\"min-width:75px\">\r\n            選樣結果\r\n          </label>\r\n        </div>\r\n        \r\n        <div class=\"flex flex-col col-9 px-0 items-start\">\r\n          <input #inputFile type=\"file\" id=\"fileInput\" [accept]=\"fileUploadConfig.acceptAttribute\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"detectFiles($event)\" [disabled]=\"fileUploadConfigWithState.disabled\" multiple>\r\n\r\n          <label for=\"fileInput\" [class.opacity-50]=\"fileUploadConfigWithState.disabled\" \r\n            [class.cursor-pointer]=\"!fileUploadConfigWithState.disabled\"\r\n            class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i [class]=\"fileUploadConfig.buttonIcon + ' mr-2'\"></i> {{ fileUploadConfig.buttonText }}\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label baseLabel class=\"align-self-start mr-4\" style=\"min-width:75px\">\r\n        </label>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"!isEdit\">\r\n          <div *ngFor=\"let file of imageUrlList; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"isImage(file.CFileType)\" class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.data\">\r\n            <span *ngIf=\"!isImage(file.CFileType) && !isCad(file.CFileType)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\">PDF</span>\r\n            <span *ngIf=\"isCad(file.CFileType)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\">CAD</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n            <span class=\"absolute top-0 right-0 cursor-pointer bg-white rounded-full\" (click)=\"removeFile(i)\">\r\n              <i class=\"fa fa-times text-red-600\"></i>\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex flex-wrap mt-2\" *ngIf=\"isEdit\">\r\n          <div *ngFor=\"let file of SpecialChange.CFileRes; let i = index\" class=\"relative w-24 h-24 mr-2 mb-2 border\">\r\n            <img *ngIf=\"!isPDFString(file.CFile) && !isCadString(file.CFile)\"\r\n              class=\"w-full h-full object-contain cursor-pointer\" [src]=\"file.CFile\" (click)=\"openNewTab(file.CFile)\">\r\n            <span *ngIf=\"isPDFString(file.CFile)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\"\r\n              (click)=\"openNewTab(file.CFile)\">PDF</span>\r\n            <span *ngIf=\"isCadString(file.CFile)\"\r\n              class=\"absolute inset-0 flex items-center justify-center cursor-pointer\"\r\n              (click)=\"openNewTab(file.CFile)\">CAD</span>\r\n            <p class=\"absolute -bottom-4 left-0 w-full text-xs truncate px-1 text-center\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\" (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAuBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAN3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAuE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChFb,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IARCb,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAQ,WAAA,EAAiC;IACjCxB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAS,YAAA,CAAsB;IACtBzB,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAiB,UAAA,CAAAP,OAAA,CAAAU,SAAA,EAA8B;IAC9B1B,EAAA,CAAAqB,SAAA,GAAuE;IAAvErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,UAAA,kCAAAX,OAAA,CAAAW,UAAA,mCAAuE;IAErB3B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuB,QAAA,CAAc;;;;;IAoEpE7B,EAAA,CAAA8B,SAAA,cAA2G;;;;IAAlB9B,EAAA,CAAA4B,UAAA,QAAAG,OAAA,CAAAC,IAAA,EAAAhC,EAAA,CAAAiC,aAAA,CAAiB;;;;;IAC1GjC,EAAA,CAAAC,cAAA,eAC2E;IAAAD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IACrFb,EAAA,CAAAC,cAAA,eAC2E;IAAAD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;;IALvFb,EAAA,CAAAC,cAAA,cAAkG;IAIhGD,EAHA,CAAAmB,UAAA,IAAAe,yEAAA,kBAA2G,IAAAC,0EAAA,mBAEhC,IAAAC,0EAAA,mBAEA;IAC3EpC,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IACtGb,EAAA,CAAAC,cAAA,eAAkG;IAAxBD,EAAA,CAAAE,UAAA,mBAAAmC,0FAAA;MAAA,MAAAC,KAAA,GAAAtC,EAAA,CAAAI,aAAA,CAAAmC,GAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAC/FtC,EAAA,CAAA8B,SAAA,YAAwC;IAE5C9B,EADE,CAAAa,YAAA,EAAO,EACH;;;;;IATEb,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAoC,OAAA,CAAAX,OAAA,CAAAY,SAAA,EAA6B;IAC5B3C,EAAA,CAAAqB,SAAA,EAAwD;IAAxDrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAoC,OAAA,CAAAX,OAAA,CAAAY,SAAA,MAAArC,MAAA,CAAAsC,KAAA,CAAAb,OAAA,CAAAY,SAAA,EAAwD;IAExD3C,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAsC,KAAA,CAAAb,OAAA,CAAAY,SAAA,EAA2B;IAE4C3C,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAAS,OAAA,CAAAc,SAAA,CAAoB;;;;;IAPtG7C,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAmB,UAAA,IAAA2B,mEAAA,kBAAkG;IAWpG9C,EAAA,CAAAa,YAAA,EAAM;;;;IAXkBb,EAAA,CAAAqB,SAAA,EAAiB;IAAjBrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAyC,YAAA,CAAiB;;;;;;IAcrC/C,EAAA,CAAAC,cAAA,cAC0G;IAAjCD,EAAA,CAAAE,UAAA,mBAAA8C,+FAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAA6C,IAAA;MAAA,MAAAC,QAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6C,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IADzGpD,EAAA,CAAAa,YAAA,EAC0G;;;;IAApDb,EAAA,CAAA4B,UAAA,QAAAsB,QAAA,CAAAE,KAAA,EAAApD,EAAA,CAAAiC,aAAA,CAAkB;;;;;;IACxEjC,EAAA,CAAAC,cAAA,eAEmC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAmD,iGAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAJ,QAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6C,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAACpD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;;IAC7Cb,EAAA,CAAAC,cAAA,eAEmC;IAAjCD,EAAA,CAAAE,UAAA,mBAAAqD,iGAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAN,QAAA,GAAAlD,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6C,UAAA,CAAAD,QAAA,CAAAE,KAAA,CAAsB;IAAA,EAAC;IAACpD,EAAA,CAAAY,MAAA,UAAG;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;;IAR/Cb,EAAA,CAAAC,cAAA,cAA4G;IAM1GD,EALA,CAAAmB,UAAA,IAAAsC,yEAAA,kBAC0G,IAAAC,0EAAA,mBAGvE,IAAAC,0EAAA,mBAGA;IACnC3D,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAY,MAAA,GAAoB;IACpGZ,EADoG,CAAAa,YAAA,EAAI,EAClG;;;;;IATEb,EAAA,CAAAqB,SAAA,EAA0D;IAA1DrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAsD,WAAA,CAAAV,QAAA,CAAAE,KAAA,MAAA9C,MAAA,CAAAuD,WAAA,CAAAX,QAAA,CAAAE,KAAA,EAA0D;IAEzDpD,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAsD,WAAA,CAAAV,QAAA,CAAAE,KAAA,EAA6B;IAG7BpD,EAAA,CAAAqB,SAAA,EAA6B;IAA7BrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAuD,WAAA,CAAAX,QAAA,CAAAE,KAAA,EAA6B;IAG0CpD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAsB,iBAAA,CAAA4B,QAAA,CAAAL,SAAA,CAAoB;;;;;IAVtG7C,EAAA,CAAAC,cAAA,cAAgD;IAC9CD,EAAA,CAAAmB,UAAA,IAAA2C,mEAAA,kBAA4G;IAW9G9D,EAAA,CAAAa,YAAA,EAAM;;;;IAXkBb,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAA4B,UAAA,YAAAtB,MAAA,CAAAyD,aAAA,CAAAC,QAAA,CAA2B;;;;;;IAsBnDhE,EAAA,CAAAC,cAAA,iBAAuF;IAAnCD,EAAA,CAAAE,UAAA,mBAAA+D,yFAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAO,aAAA,GAAA6D,SAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA+D,mBAAA,CAAAF,OAAA,CAAwB;IAAA,EAAC;IAACnE,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IA7ExGb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4C;IADED,EAAA,CAAAsE,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0E,kBAAA,CAAApE,MAAA,CAAAqE,iBAAA,CAAAnD,WAAA,EAAAgD,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAnD,WAAA,GAAAgD,MAAA;MAAA,OAAAxE,EAAA,CAAAU,WAAA,CAAA8D,MAAA;IAAA,EAA2C;IAE3FxE,EAD8C,CAAAa,YAAA,EAAa,EACrD;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAAsE,gBAAA,2BAAAM,uFAAAJ,MAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0E,kBAAA,CAAApE,MAAA,CAAAqE,iBAAA,CAAAlD,YAAA,EAAA+C,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAlD,YAAA,GAAA+C,MAAA;MAAA,OAAAxE,EAAA,CAAAU,WAAA,CAAA8D,MAAA;IAAA,EAA4C;IAE3GxE,EAFE,CAAAa,YAAA,EACwB,EACpB;IAEFb,EAFQ,CAAAC,cAAA,eAAkD,eAC1B,iBACiE;IAC/FD,EAAA,CAAAY,MAAA,kCACF;IACFZ,EADE,CAAAa,YAAA,EAAQ,EACJ;IAGJb,EADF,CAAAC,cAAA,eAAkD,oBAEgE;IAAxFD,EAAA,CAAAE,UAAA,oBAAA2E,gFAAAL,MAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAUJ,MAAA,CAAAwE,WAAA,CAAAN,MAAA,CAAmB;IAAA,EAAC;IADtDxE,EAAA,CAAAa,YAAA,EACgH;IAEhHb,EAAA,CAAAC,cAAA,iBAE+E;IAC7ED,EAAA,CAAA8B,SAAA,SAAuD;IAAC9B,EAAA,CAAAY,MAAA,IAC1D;IAEJZ,EAFI,CAAAa,YAAA,EAAQ,EACJ,EACF;IAENb,EAAA,CAAAC,cAAA,eAAkD;IAChDD,EAAA,CAAA8B,SAAA,iBACQ;IAcR9B,EAbA,CAAAmB,UAAA,KAAA4D,6DAAA,kBAAiD,KAAAC,6DAAA,kBAaD;IAalDhF,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAAkD,iBAEvB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,oBACoF;IAA/CD,EAAA,CAAAsE,gBAAA,2BAAAW,0FAAAT,MAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0E,kBAAA,CAAApE,MAAA,CAAAqE,iBAAA,CAAAO,cAAA,EAAAV,MAAA,MAAAlE,MAAA,CAAAqE,iBAAA,CAAAO,cAAA,GAAAV,MAAA;MAAA,OAAAxE,EAAA,CAAAU,WAAA,CAAA8D,MAAA;IAAA,EAA8C;IACrFxE,EADsF,CAAAa,YAAA,EAAW,EAC3F;IAGJb,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAiF,gFAAA;MAAA,MAAAhB,OAAA,GAAAnE,EAAA,CAAAI,aAAA,CAAAqE,GAAA,EAAAL,SAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8E,OAAA,CAAAjB,OAAA,CAAY;IAAA,EAAC;IAACnE,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAAkE,gEAAA,qBAAuF;IAG7FrF,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IA/ENb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsF,kBAAA,wEAAAhF,MAAA,CAAAiF,KAAA,CAAAC,UAAA,cAAAlF,MAAA,CAAAiF,KAAA,CAAAE,MAAA,OACF;IAOgBzF,EAAA,CAAAqB,SAAA,GAA0B;IAA6CrB,EAAvE,CAAA4B,UAAA,2BAA0B,wBAA4C,kBAAkB;IACtD5B,EAAA,CAAA0F,gBAAA,YAAApF,MAAA,CAAAqE,iBAAA,CAAAnD,WAAA,CAA2C;IACvFxB,EADwF,CAAA4B,UAAA,aAAAtB,MAAA,CAAAqF,MAAA,CAAmB,uBACrF;IAMqC3F,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAA0F,gBAAA,YAAApF,MAAA,CAAAqE,iBAAA,CAAAlD,YAAA,CAA4C;IACvGzB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAAqF,MAAA,CAAmB;IAGwB3F,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAA4F,WAAA,yBAA8B;IAM5B5F,EAAA,CAAAqB,SAAA,GAA2C;IACjCrB,EADV,CAAA4B,UAAA,WAAAtB,MAAA,CAAAuF,gBAAA,CAAAC,eAAA,CAA2C,aAAAxF,MAAA,CAAAyF,yBAAA,CAAAC,QAAA,CACc;IAE/EhG,EAAA,CAAAqB,SAAA,GAAuD;IAC5ErB,EADqB,CAAA4F,WAAA,eAAAtF,MAAA,CAAAyF,yBAAA,CAAAC,QAAA,CAAuD,oBAAA1F,MAAA,CAAAyF,yBAAA,CAAAC,QAAA,CAChB;IAEzDhG,EAAA,CAAAqB,SAAA,EAA+C;IAA/CrB,EAAA,CAAAiG,UAAA,CAAA3F,MAAA,CAAAuF,gBAAA,CAAAK,UAAA,WAA+C;IAAMlG,EAAA,CAAAqB,SAAA,EAC1D;IAD0DrB,EAAA,CAAAmG,kBAAA,MAAA7F,MAAA,CAAAuF,gBAAA,CAAAO,UAAA,MAC1D;IAOgCpG,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAqF,MAAA,CAAa;IAab3F,EAAA,CAAAqB,SAAA,EAAY;IAAZrB,EAAA,CAAA4B,UAAA,SAAAtB,MAAA,CAAAqF,MAAA,CAAY;IAkB5C3F,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA4B,UAAA,aAAAtB,MAAA,CAAAqF,MAAA,CAAmB;IAAgB3F,EAAA,CAAA0F,gBAAA,YAAApF,MAAA,CAAAqE,iBAAA,CAAAO,cAAA,CAA8C;IAK9ClF,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA4B,UAAA,UAAAtB,MAAA,CAAAqF,MAAA,CAAa;;;AD/G1D,OAAM,MAAOU,8BAA+B,SAAQvG,aAAa;EAC/DwG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAGvB,KAAAhE,YAAY,GAAU,EAAE;IACxB,KAAA4C,MAAM,GAAG,KAAK;IAEd;IACA,KAAAE,gBAAgB,GAAqB;MACnCmB,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;MAC7EC,iBAAiB,EAAE,2BAA2B;MAC9CnB,eAAe,EAAE,oDAAoD;MACrEoB,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,KAAK;MACfpB,QAAQ,EAAE,KAAK;MACfqB,YAAY,EAAE,IAAI;MAClBjB,UAAU,EAAE,MAAM;MAClBF,UAAU,EAAE,oBAAoB;MAChCoB,WAAW,EAAE;KACd;IACD,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZP,KAAK,EAAE;KACR,EACD;MACEM,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBP,KAAK,EAAE;KACR,CACF;IAEQ,KAAAQ,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAwRzB,KAAAC,YAAY,GAAU,EAAE;EA3TN;EA4CTC,QAAQA,CAAA;IAEf,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAAC5G,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAACgC,QAAQ,CAACZ,KAAK,EAAEyF,MAAM,CAACC,IAAI,CAAC9G,IAAI,CAACgC,QAAQ,CAACZ,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAsF,oBAAoBA,CAAA;IAClB,IAAI,CAAChC,qBAAqB,CAACqC,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACR,OAAO;QACtBS,SAAS,EAAE,IAAI,CAACtB,SAAS;QACzBuB,QAAQ,EAAE,IAAI,CAACxB;;KAElB,CAAC,CAACM,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAACzB,YAAY,GAAGuB,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAV,YAAYA,CAAA;IACV,IAAI,CAAChC,aAAa,CAAC8C,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACjB;;KAElB,CAAC,CAACR,SAAS,CAACmB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAChE,KAAK,GAAG6D,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAACpE,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAmE,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAACpD,qBAAqB,CAACqD,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAC7B,SAAS,CAACmB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACxF,aAAa,GAAGqF,GAAG,CAACE,OAAO;QAChC,IAAI,CAAC3E,iBAAiB,GAAG;UACvBO,cAAc,EAAE,IAAI,CAACnB,aAAa,CAACmB,cAAc;UACjD8E,YAAY,EAAE,IAAI,CAAC1B,WAAW;UAC9B7G,YAAY,EAAE,IAAI,CAACsC,aAAa,CAACtC,YAAY;UAC7CiI,QAAQ,EAAE,IAAI,CAACjB,OAAO;UACtBwB,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAAClG,aAAa,CAACvC,WAAW,EAAE;UAClC,IAAI,CAACmD,iBAAiB,CAACnD,WAAW,GAAG,IAAI0I,IAAI,CAAC,IAAI,CAACnG,aAAa,CAACvC,WAAW,CAAC;QAC/E;QACA,IAAI,CAACgF,aAAa,CAACsC,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAxF,mBAAmBA,CAACwF,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1D,KAAK,CAAC2D,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxD,OAAO,CAACyD,aAAa,CAAC,IAAI,CAAC7D,KAAK,CAAC2D,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC1D,qBAAqB,CAAC6D,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAACvC,SAAS,CAACmB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1C,OAAO,CAAC4D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/B,oBAAoB,EAAE;QAC3BmB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAGA/H,MAAMA,CAACkJ,GAAQ;IACb,IAAI,CAAC9G,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC4C,MAAM,GAAG,KAAK;IACnB,IAAI,CAAChB,iBAAiB,GAAG;MACvBO,cAAc,EAAE,EAAE;MAClB8E,YAAY,EAAE,IAAI,CAAC1B,WAAW;MAC9B9G,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBiI,QAAQ,EAAE,IAAI,CAACjB,OAAO;MACtBwB,kBAAkB,EAAE;KACrB;IACD,IAAI,CAACzD,aAAa,CAACsC,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEA3I,MAAMA,CAAC2I,GAAQ,EAAEgB,aAAkB;IACjC,IAAI,CAAC9H,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC4C,MAAM,GAAG,IAAI;IAClB,IAAI,CAACiE,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAK,UAAUA,CAAA;IACR,IAAI,CAAC1D,KAAK,CAACqE,KAAK,EAAE;IAClB,IAAI,CAACrE,KAAK,CAACW,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzC,iBAAiB,CAACnD,WAAW,CAAC;IACjE,IAAI,CAACiF,KAAK,CAACW,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzC,iBAAiB,CAAClD,YAAY,CAAC;IAClE,IAAI,CAACgF,KAAK,CAACW,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACzC,iBAAiB,CAACO,cAAc,CAAC;EACtE;EAEA3D,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO3B,MAAM,CAAC2B,WAAW,CAAC,CAACuJ,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAAClJ,IAAI;IAClB;IACA,OAAOiJ,KAAK;EACd;EAEAT,WAAWA,CAAA;IACT,MAAMW,MAAM,GAAG;MACb,GAAG,IAAI,CAACxG,iBAAiB;MACzBsF,kBAAkB,EAAE,IAAI,CAAClH;KAC1B;IACD,IAAI,CAACiI,gBAAgB,CAACG,MAAM,CAAClB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAACtF,iBAAiB,CAACnD,WAAW,EAAE;MACtC2J,MAAM,CAAC3J,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACoD,iBAAiB,CAACnD,WAAW,CAAC;IAC1E;IACA,OAAO2J,MAAM;EACf;EACA/F,OAAOA,CAACyE,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EAEA;EACAU,cAAcA,CAACD,MAAwB;IACrC;IACA,IAAI,CAACpI,YAAY,CAACsI,IAAI,CAAC;MACrBrJ,IAAI,EAAE,QAAQmJ,MAAM,CAACG,WAAW,CAACC,IAAI,WAAWJ,MAAM,CAAC/H,KAAK,EAAE;MAC9DoI,UAAU,EAAEL,MAAM,CAAC/H,KAAK;MACxBP,SAAS,EAAEsI,MAAM,CAACM,KAAK;MACvB9I,SAAS,EAAEwI,MAAM,CAACxI;KACnB,CAAC;IAEF;IACA,IAAI,IAAI,CAACkD,gBAAgB,CAACwB,YAAY,IAAI,CAAC,IAAI,CAAC1C,iBAAiB,CAAClD,YAAY,EAAE;MAC9E,MAAMiK,QAAQ,GAAGP,MAAM,CAACM,KAAK;MAC7B,MAAME,wBAAwB,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIH,QAAQ;MAC7F,IAAI,CAAC/G,iBAAiB,CAAClD,YAAY,GAAGkK,wBAAwB;IAChE;EACF;EAEA;EACAG,aAAaA,CAAA;IACX;IACA;EAAA;EAGF;EACA,IAAI/F,yBAAyBA,CAAA;IAC3B,OAAO;MACL,GAAG,IAAI,CAACF,gBAAgB;MACxBG,QAAQ,EAAE,IAAI,CAACL,MAAM,IAAI,IAAI,CAAC5B,aAAa,EAAEpC,UAAU,KAAK;KAC7D;EACH;EAEAoK,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACJ,SAAS,CAACK,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAAElH,WAAWA,CAACqH,KAAU;IACtB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAAC/B,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMiC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAChJ,MAAMC,SAAS,GAAG,2BAA2B;MAE7C;MACA,IAAI,CAAC,IAAI,CAAC5H,iBAAiB,CAAClD,YAAY,IAAI2K,KAAK,CAAC/B,MAAM,GAAG,CAAC,EAAE;QAC5D,MAAMmC,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMV,QAAQ,GAAGc,SAAS,CAACC,IAAI;QAC/B,MAAMd,wBAAwB,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIH,QAAQ;QAC7F,IAAI,CAAC/G,iBAAiB,CAAClD,YAAY,GAAGkK,wBAAwB;MAChE;MAEA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAAC/B,MAAM,EAAEqC,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGP,KAAK,CAACM,CAAC,CAAC;QACrB,IAAI,CAACH,SAAS,CAACK,IAAI,CAACD,IAAI,CAACF,IAAI,CAAC,EAAE;UAC9B,IAAI,CAAC5F,OAAO,CAACgG,YAAY,CAAC,kCAAkC,CAAC;UAC7D;QACF;QACA,IAAIP,YAAY,CAACQ,QAAQ,CAACH,IAAI,CAACpB,IAAI,CAAC,IAAIgB,SAAS,CAACK,IAAI,CAACD,IAAI,CAACF,IAAI,CAAC,EAAE;UACjE,MAAMM,MAAM,GAAG,IAAIC,UAAU,EAAE;UAE/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB,IAAIC,QAAgB;YACpB,IAAIR,IAAI,CAACpB,IAAI,CAAC6B,UAAU,CAAC,QAAQ,CAAC,EAAE;cAClCD,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAIR,IAAI,CAACF,IAAI,CAACY,WAAW,EAAE,CAACP,QAAQ,CAAC,MAAM,CAAC,IAAIH,IAAI,CAACF,IAAI,CAACY,WAAW,EAAE,CAACP,QAAQ,CAAC,MAAM,CAAC,EAAE;cAC/FK,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM;cACLA,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;YAEA,IAAI,CAACpK,YAAY,CAACsI,IAAI,CAAC;cACrBrJ,IAAI,EAAEkL,CAAC,CAACb,MAAM,CAAClB,MAAM;cACrBK,UAAU,EAAE,IAAI,CAACO,kBAAkB,CAACmB,CAAC,CAACb,MAAM,CAAClB,MAAM,CAAC;cACpDtI,SAAS,EAAE8J,IAAI,CAACF,IAAI;cACpB9J,SAAS,EAAEwK;aACZ,CAAC;YAEF,IAAI,IAAI,CAACpK,YAAY,CAACsH,MAAM,KAAK+B,KAAK,CAAC/B,MAAM,EAAE;cAC7CiD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACxK,YAAY,CAAC;cACnD,IAAI,IAAI,CAACyK,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACjG,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACDuF,MAAM,CAACW,aAAa,CAACf,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAEA/I,WAAWA,CAAC+J,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACN,WAAW,EAAE,CAACO,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA/J,WAAWA,CAAC8J,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAACN,WAAW,EAAE;MAClC,OAAOQ,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACAlL,OAAOA,CAACyK,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAvK,KAAKA,CAACuK,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAW,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACV,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIA5K,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACO,YAAY,CAACiL,MAAM,CAACxL,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAyL,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACpG,YAAY,GAAG,IAAI,CAACA,YAAY,CAACqG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/F,EAAE,IAAI6F,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAACxE,GAAQ,GACpB;EAEAyE,UAAUA,CAACnC,KAAU,EAAE3J,KAAa;IAClC,IAAI+L,IAAI,GAAG,IAAI,CAACzG,YAAY,CAACtF,KAAK,CAAC,CAACY,KAAK,CAACoL,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC1G,YAAY,CAACtF,KAAK,CAAC,CAACY,KAAK,CAACqL,IAAI,EAAE,IAAI,CAAC3G,YAAY,CAACtF,KAAK,CAAC,CAACY,KAAK,CAACmI,IAAI,CAAC;IAC5H,IAAImD,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAGpC,KAAK,CAACE,MAAM,CAAC7E,KAAK,GAAG,GAAG,GAAG,IAAI,CAACM,YAAY,CAACtF,KAAK,CAAC,CAACuL,SAAS,EAAE,EAAE;MAAExC,IAAI,EAAE,IAAI,CAACzD,YAAY,CAACtF,KAAK,CAAC,CAACY,KAAK,CAACmI;IAAI,CAAE,CAAC;IACjJ,IAAI,CAACzD,YAAY,CAACtF,KAAK,CAAC,CAACY,KAAK,GAAGsL,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC7H,aAAa,CAACsE,IAAI,CAAC;MACtBwD,MAAM;MACNC,OAAO,EAAE,IAAI,CAACxG;KACf,CAAC;IACF,IAAI,CAACxB,QAAQ,CAACiI,IAAI,EAAE;EACtB;EACA5L,UAAUA,CAAC6L,GAAQ;IACjB,IAAIA,GAAG,EAAEnG,MAAM,CAACC,IAAI,CAACkG,GAAG,EAAE,QAAQ,CAAC;EACrC;;;uCAnWW3I,8BAA8B,EAAArG,EAAA,CAAAiP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnP,EAAA,CAAAiP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArP,EAAA,CAAAiP,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAvP,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAAzP,EAAA,CAAAiP,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAA1P,EAAA,CAAAiP,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA5P,EAAA,CAAAiP,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAA9P,EAAA,CAAAiP,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAhQ,EAAA,CAAAiP,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9B7J,8BAA8B;MAAA8J,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCzBzCtQ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAAqP,gDAAA,oBAAoF;UAI1FxQ,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,iBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAAsP,6CAAA,kBAA+E;UAavFzQ,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAsE,gBAAA,wBAAAoM,8EAAAlM,MAAA;YAAAxE,EAAA,CAAAI,aAAA,CAAAuQ,GAAA;YAAA3Q,EAAA,CAAA0E,kBAAA,CAAA6L,GAAA,CAAA3I,SAAA,EAAApD,MAAA,MAAA+L,GAAA,CAAA3I,SAAA,GAAApD,MAAA;YAAA,OAAAxE,EAAA,CAAAU,WAAA,CAAA8D,MAAA;UAAA,EAAoB;UAClCxE,EAAA,CAAAE,UAAA,wBAAAwQ,8EAAAlM,MAAA;YAAAxE,EAAA,CAAAI,aAAA,CAAAuQ,GAAA;YAAA,OAAA3Q,EAAA,CAAAU,WAAA,CAAc6P,GAAA,CAAA5F,WAAA,CAAAnG,MAAA,CAAmB;UAAA,EAAC;UAEtCxE,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAE,UAAA,mBAAA0Q,iEAAA;YAAA5Q,EAAA,CAAAI,aAAA,CAAAuQ,GAAA;YAAA,OAAA3Q,EAAA,CAAAU,WAAA,CAAS6P,GAAA,CAAA3B,MAAA,EAAQ;UAAA,EAAC;UACzD5O,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAA0P,sDAAA,kCAAA7Q,EAAA,CAAA8Q,sBAAA,CAAiE;;;UAxD7D9Q,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAAmG,kBAAA,wEAAAoK,GAAA,CAAA5G,UAAA,MACF;UAQ4E3J,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA4B,UAAA,SAAA2O,GAAA,CAAAQ,QAAA,CAAc;UAkB7D/Q,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA4B,UAAA,YAAA2O,GAAA,CAAA/G,iBAAA,CAAuB;UAelCxJ,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAA0F,gBAAA,SAAA6K,GAAA,CAAA3I,SAAA,CAAoB;UAAuB5H,EAAtB,CAAA4B,UAAA,aAAA2O,GAAA,CAAA5I,QAAA,CAAqB,mBAAA4I,GAAA,CAAA1I,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}