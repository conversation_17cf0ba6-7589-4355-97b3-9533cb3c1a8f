{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { ThemeModule } from \"src/app/@theme/theme.module\";\nimport { NbMenuModule } from \"@nebular/theme\";\nimport { SharedModule } from \"../components/shared.module\";\nimport { CalendarModule } from \"primeng/calendar\";\nimport { HouseholdRoutingModule } from \"./household-management-routing.module\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport let HouseholdManagementModule = /*#__PURE__*/(() => {\n  class HouseholdManagementModule {\n    static {\n      this.ɵfac = function HouseholdManagementModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdManagementModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: HouseholdManagementModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, HouseholdRoutingModule, SharedModule, ThemeModule, CalendarModule, NbMenuModule.forRoot()]\n      });\n    }\n  }\n  return HouseholdManagementModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}