{"ast": null, "code": "import { SmartTableData } from '../data/smart-table';\nimport * as i0 from \"@angular/core\";\nexport let SmartTableService = /*#__PURE__*/(() => {\n  class SmartTableService extends SmartTableData {\n    constructor() {\n      super(...arguments);\n      this.data = [{\n        id: 1,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        username: '@mdo',\n        email: '<EMAIL>',\n        age: '28'\n      }, {\n        id: 2,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        username: '@fat',\n        email: '<EMAIL>',\n        age: '45'\n      }, {\n        id: 3,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        username: '@twitter',\n        email: '<EMAIL>',\n        age: '18'\n      }, {\n        id: 4,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        username: '@snow',\n        email: '<EMAIL>',\n        age: '20'\n      }, {\n        id: 5,\n        firstName: 'Jack',\n        lastName: 'Sparrow',\n        username: '@jack',\n        email: '<EMAIL>',\n        age: '30'\n      }, {\n        id: 6,\n        firstName: '<PERSON>',\n        lastName: '<PERSON>',\n        username: '@ann',\n        email: '<EMAIL>',\n        age: '21'\n      }, {\n        id: 7,\n        firstName: '<PERSON>',\n        lastName: 'Black',\n        username: '@barbara',\n        email: '<EMAIL>',\n        age: '43'\n      }, {\n        id: 8,\n        firstName: 'Sevan',\n        lastName: 'Bagrat',\n        username: '@sevan',\n        email: '<EMAIL>',\n        age: '13'\n      }, {\n        id: 9,\n        firstName: 'Ruben',\n        lastName: 'Vardan',\n        username: '@ruben',\n        email: '<EMAIL>',\n        age: '22'\n      }, {\n        id: 10,\n        firstName: 'Karen',\n        lastName: 'Sevan',\n        username: '@karen',\n        email: '<EMAIL>',\n        age: '33'\n      }, {\n        id: 11,\n        firstName: 'Mark',\n        lastName: 'Otto',\n        username: '@mark',\n        email: '<EMAIL>',\n        age: '38'\n      }, {\n        id: 12,\n        firstName: 'Jacob',\n        lastName: 'Thornton',\n        username: '@jacob',\n        email: '<EMAIL>',\n        age: '48'\n      }, {\n        id: 13,\n        firstName: 'Haik',\n        lastName: 'Hakob',\n        username: '@haik',\n        email: '<EMAIL>',\n        age: '48'\n      }, {\n        id: 14,\n        firstName: 'Garegin',\n        lastName: 'Jirair',\n        username: '@garegin',\n        email: '<EMAIL>',\n        age: '40'\n      }, {\n        id: 15,\n        firstName: 'Krikor',\n        lastName: 'Bedros',\n        username: '@krikor',\n        email: '<EMAIL>',\n        age: '32'\n      }, {\n        'id': 16,\n        'firstName': 'Francisca',\n        'lastName': 'Brady',\n        'username': '@Gibson',\n        'email': '<EMAIL>',\n        'age': 11\n      }, {\n        'id': 17,\n        'firstName': 'Tillman',\n        'lastName': 'Figueroa',\n        'username': '@Snow',\n        'email': '<EMAIL>',\n        'age': 34\n      }, {\n        'id': 18,\n        'firstName': 'Jimenez',\n        'lastName': 'Morris',\n        'username': '@Bryant',\n        'email': '<EMAIL>',\n        'age': 45\n      }, {\n        'id': 19,\n        'firstName': 'Sandoval',\n        'lastName': 'Jacobson',\n        'username': '@Mcbride',\n        'email': '<EMAIL>',\n        'age': 32\n      }, {\n        'id': 20,\n        'firstName': 'Griffin',\n        'lastName': 'Torres',\n        'username': '@Charles',\n        'email': '<EMAIL>',\n        'age': 19\n      }, {\n        'id': 21,\n        'firstName': 'Cora',\n        'lastName': 'Parker',\n        'username': '@Caldwell',\n        'email': '<EMAIL>',\n        'age': 27\n      }, {\n        'id': 22,\n        'firstName': 'Cindy',\n        'lastName': 'Bond',\n        'username': '@Velez',\n        'email': '<EMAIL>',\n        'age': 24\n      }, {\n        'id': 23,\n        'firstName': 'Frieda',\n        'lastName': 'Tyson',\n        'username': '@Craig',\n        'email': '<EMAIL>',\n        'age': 45\n      }, {\n        'id': 24,\n        'firstName': 'Cote',\n        'lastName': 'Holcomb',\n        'username': '@Rowe',\n        'email': '<EMAIL>',\n        'age': 20\n      }, {\n        'id': 25,\n        'firstName': 'Trujillo',\n        'lastName': 'Mejia',\n        'username': '@Valenzuela',\n        'email': '<EMAIL>',\n        'age': 16\n      }, {\n        'id': 26,\n        'firstName': 'Pruitt',\n        'lastName': 'Shepard',\n        'username': '@Sloan',\n        'email': '<EMAIL>',\n        'age': 44\n      }, {\n        'id': 27,\n        'firstName': 'Sutton',\n        'lastName': 'Ortega',\n        'username': '@Black',\n        'email': '<EMAIL>',\n        'age': 42\n      }, {\n        'id': 28,\n        'firstName': 'Marion',\n        'lastName': 'Heath',\n        'username': '@Espinoza',\n        'email': '<EMAIL>',\n        'age': 47\n      }, {\n        'id': 29,\n        'firstName': 'Newman',\n        'lastName': 'Hicks',\n        'username': '@Keith',\n        'email': '<EMAIL>',\n        'age': 15\n      }, {\n        'id': 30,\n        'firstName': 'Boyle',\n        'lastName': 'Larson',\n        'username': '@Summers',\n        'email': '<EMAIL>',\n        'age': 32\n      }, {\n        'id': 31,\n        'firstName': 'Haynes',\n        'lastName': 'Vinson',\n        'username': '@Mckenzie',\n        'email': '<EMAIL>',\n        'age': 15\n      }, {\n        'id': 32,\n        'firstName': 'Miller',\n        'lastName': 'Acosta',\n        'username': '@Young',\n        'email': '<EMAIL>',\n        'age': 55\n      }, {\n        'id': 33,\n        'firstName': 'Johnston',\n        'lastName': 'Brown',\n        'username': '@Knight',\n        'email': '<EMAIL>',\n        'age': 29\n      }, {\n        'id': 34,\n        'firstName': 'Lena',\n        'lastName': 'Pitts',\n        'username': '@Forbes',\n        'email': '<EMAIL>',\n        'age': 25\n      }, {\n        'id': 35,\n        'firstName': 'Terrie',\n        'lastName': 'Kennedy',\n        'username': '@Branch',\n        'email': '<EMAIL>',\n        'age': 37\n      }, {\n        'id': 36,\n        'firstName': 'Louise',\n        'lastName': 'Aguirre',\n        'username': '@Kirby',\n        'email': '<EMAIL>',\n        'age': 44\n      }, {\n        'id': 37,\n        'firstName': 'David',\n        'lastName': 'Patton',\n        'username': '@Sanders',\n        'email': '<EMAIL>',\n        'age': 26\n      }, {\n        'id': 38,\n        'firstName': 'Holden',\n        'lastName': 'Barlow',\n        'username': '@Mckinney',\n        'email': '<EMAIL>',\n        'age': 11\n      }, {\n        'id': 39,\n        'firstName': 'Baker',\n        'lastName': 'Rivera',\n        'username': '@Montoya',\n        'email': '<EMAIL>',\n        'age': 47\n      }, {\n        'id': 40,\n        'firstName': 'Belinda',\n        'lastName': 'Lloyd',\n        'username': '@Calderon',\n        'email': '<EMAIL>',\n        'age': 21\n      }, {\n        'id': 41,\n        'firstName': 'Pearson',\n        'lastName': 'Patrick',\n        'username': '@Clements',\n        'email': '<EMAIL>',\n        'age': 42\n      }, {\n        'id': 42,\n        'firstName': 'Alyce',\n        'lastName': 'Mckee',\n        'username': '@Daugherty',\n        'email': '<EMAIL>',\n        'age': 55\n      }, {\n        'id': 43,\n        'firstName': 'Valencia',\n        'lastName': 'Spence',\n        'username': '@Olsen',\n        'email': '<EMAIL>',\n        'age': 20\n      }, {\n        'id': 44,\n        'firstName': 'Leach',\n        'lastName': 'Holcomb',\n        'username': '@Humphrey',\n        'email': '<EMAIL>',\n        'age': 28\n      }, {\n        'id': 45,\n        'firstName': 'Moss',\n        'lastName': 'Baxter',\n        'username': '@Fitzpatrick',\n        'email': '<EMAIL>',\n        'age': 51\n      }, {\n        'id': 46,\n        'firstName': 'Jeanne',\n        'lastName': 'Cooke',\n        'username': '@Ward',\n        'email': '<EMAIL>',\n        'age': 59\n      }, {\n        'id': 47,\n        'firstName': 'Wilma',\n        'lastName': 'Briggs',\n        'username': '@Kidd',\n        'email': '<EMAIL>',\n        'age': 53\n      }, {\n        'id': 48,\n        'firstName': 'Beatrice',\n        'lastName': 'Perry',\n        'username': '@Gilbert',\n        'email': '<EMAIL>',\n        'age': 39\n      }, {\n        'id': 49,\n        'firstName': 'Whitaker',\n        'lastName': 'Hyde',\n        'username': '@Mcdonald',\n        'email': '<EMAIL>',\n        'age': 35\n      }, {\n        'id': 50,\n        'firstName': 'Rebekah',\n        'lastName': 'Duran',\n        'username': '@Gross',\n        'email': '<EMAIL>',\n        'age': 40\n      }, {\n        'id': 51,\n        'firstName': 'Earline',\n        'lastName': 'Mayer',\n        'username': '@Woodward',\n        'email': '<EMAIL>',\n        'age': 52\n      }, {\n        'id': 52,\n        'firstName': 'Moran',\n        'lastName': 'Baxter',\n        'username': '@Johns',\n        'email': '<EMAIL>',\n        'age': 20\n      }, {\n        'id': 53,\n        'firstName': 'Nanette',\n        'lastName': 'Hubbard',\n        'username': '@Cooke',\n        'email': '<EMAIL>',\n        'age': 55\n      }, {\n        'id': 54,\n        'firstName': 'Dalton',\n        'lastName': 'Walker',\n        'username': '@Hendricks',\n        'email': '<EMAIL>',\n        'age': 25\n      }, {\n        'id': 55,\n        'firstName': 'Bennett',\n        'lastName': 'Blake',\n        'username': '@Pena',\n        'email': '<EMAIL>',\n        'age': 13\n      }, {\n        'id': 56,\n        'firstName': 'Kellie',\n        'lastName': 'Horton',\n        'username': '@Weiss',\n        'email': '<EMAIL>',\n        'age': 48\n      }, {\n        'id': 57,\n        'firstName': 'Hobbs',\n        'lastName': 'Talley',\n        'username': '@Sanford',\n        'email': '<EMAIL>',\n        'age': 28\n      }, {\n        'id': 58,\n        'firstName': 'Mcguire',\n        'lastName': 'Donaldson',\n        'username': '@Roman',\n        'email': '<EMAIL>',\n        'age': 38\n      }, {\n        'id': 59,\n        'firstName': 'Rodriquez',\n        'lastName': 'Saunders',\n        'username': '@Harper',\n        'email': '<EMAIL>',\n        'age': 20\n      }, {\n        'id': 60,\n        'firstName': 'Lou',\n        'lastName': 'Conner',\n        'username': '@Sanchez',\n        'email': '<EMAIL>',\n        'age': 16\n      }];\n    }\n    getData() {\n      return this.data;\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵSmartTableService_BaseFactory;\n        return function SmartTableService_Factory(__ngFactoryType__) {\n          return (ɵSmartTableService_BaseFactory || (ɵSmartTableService_BaseFactory = i0.ɵɵgetInheritedFactory(SmartTableService)))(__ngFactoryType__ || SmartTableService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SmartTableService,\n        factory: SmartTableService.ɵfac\n      });\n    }\n  }\n  return SmartTableService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}