{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nlet HouseholdManagementComponent = class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    // if (this.searchQuery.CBuildingNameSelected) {\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\n    // }\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        console.log('getQuotationByHouseId response:', response); // 除錯用\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationID || 0;\n          console.log('載入報價單ID:', _this.currentQuotationId); // 除錯用\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              cIsDefault: entry.CIsDefault || false\n            }));\n            _this.calculateTotal();\n            console.log('成功載入報價項目:', _this.quotationItems.length, '項'); // 除錯用\n          } else {\n            console.warn('Entries.Items 不是陣列或不存在:', response.Entries);\n          }\n        } else {\n          console.warn('API 回傳資料格式不正確或 StatusCode 不是 0:', response);\n        }\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      cIsDefault: false\n    });\n  }\n  // 載入預設項目\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            cIsDefault: true\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入預設項目成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入預設項目失敗');\n        }\n      } catch (error) {\n        console.error('載入預設項目錯誤:', error);\n        _this2.message.showErrorMSG('載入預設項目失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.quotationItems.length === 0) {\n        _this3.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位\n      const invalidItems = _this3.quotationItems.filter(item => !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1);\n      if (invalidItems.length > 0) {\n        _this3.message.showErrorMSG('請確認所有項目名稱、單價及數量都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this3.currentHouse.CID,\n          items: _this3.quotationItems,\n          quotationId: _this3.currentQuotationId // 傳遞當前的報價單ID\n        };\n        console.log('儲存報價單請求:', request); // 除錯用\n        const response = yield _this3.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this3.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this3.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this3.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this4.quotationService.exportQuotation(_this4.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this4.currentHouse.CHouseHold}_${_this4.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this4.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n};\n__decorate([ViewChild('fileInput')], HouseholdManagementComponent.prototype, \"fileInput\", void 0);\nHouseholdManagementComponent = __decorate([Component({\n  selector: 'ngx-household-management',\n  templateUrl: './household-management.component.html',\n  styleUrls: ['./household-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule]\n})], HouseholdManagementComponent);\nexport { HouseholdManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "LocalStorageService", "STORAGE_KEY", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "label", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "progressOptions", "houseTypeOptions", "payStatusOptions", "signStatusOptions", "options", "getEnumOptions", "initDetail", "CHouseID", "CMail", "CIsChange", "CPayStatus", "CIsEnable", "CCustomerName", "CNationalID", "CProgress", "CHouseType", "CHouseHold", "CPhone", "quotationItems", "totalAmount", "currentHouse", "currentQuotationId", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "searchQuery", "CBuildCaseSelected", "CHouseHoldSelected", "find", "x", "CHouseTypeSelected", "CPayStatusSelected", "CProgressSelected", "CSignStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "CBuildingNameSelected", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "cID", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "length", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "index", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "CFloor", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "userBuildCaseOptions", "CBuildCaseName", "setTimeout", "getHouseById", "CID", "ref", "detailSelected", "apiHouseGetHouseByIdPost$Json", "houseDetail", "changeStartDate", "CChangeStartDate", "Date", "changeEndDate", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "houseHoldMain", "open", "array", "item", "openModelDetail", "openModel", "CBuildingName", "CHouseHoldCount", "formatDate", "CChangeDate", "format", "onSubmitDetail", "editHouseArgsParam", "CHousehold", "CId", "CNationalId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onClose", "onNavidateId", "type", "id", "idURL", "navigate", "onNavidateBuildCaseIdHouseId", "buildCaseId", "houseId", "resetSecureKey", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "addHouseHoldMain", "apiHouseHoldMainAddHouseHoldMainPost$Json", "openQuotation", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "console", "log", "CQuotationID", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "cItemName", "CItemName", "cUnitPrice", "CUnitPrice", "cCount", "CCount", "cStatus", "cIsDefault", "CIsDefault", "calculateTotal", "warn", "error", "context", "closeOnBackdropClick", "addQuotationItem", "push", "loadDefaultItems", "_this2", "request", "success", "data", "defaultItems", "removeQuotationItem", "splice", "reduce", "sum", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "saveQuotation", "_this3", "invalidItems", "filter", "trim", "items", "quotationId", "exportQuotation", "_this4", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\n<<<<<<< HEAD\r\nimport { QuotationItem } from 'src/app/models/quotation.model';\r\n=======\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n\r\n    // if (this.searchQuery.CBuildingNameSelected) {\r\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    // }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n<<<<<<< HEAD\r\n      console.log('getQuotationByHouseId response:', response); // 除錯用\r\n=======\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationID || 0;\r\n<<<<<<< HEAD\r\n        console.log('載入報價單ID:', this.currentQuotationId); // 除錯用\r\n=======\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n<<<<<<< HEAD\r\n            cIsDefault: entry.CIsDefault || false\r\n          }));\r\n          this.calculateTotal();\r\n          console.log('成功載入報價項目:', this.quotationItems.length, '項'); // 除錯用\r\n        } else {\r\n          console.warn('Entries.Items 不是陣列或不存在:', response.Entries);\r\n        }\r\n      } else {\r\n        console.warn('API 回傳資料格式不正確或 StatusCode 不是 0:', response);\r\n=======\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n<<<<<<< HEAD\r\n      cItemName: '', cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      cIsDefault: false\r\n    });\r\n  }\r\n  // 載入預設項目\r\n=======\r\n      cItemName: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義\r\n    });\r\n  }\r\n  // 載入客變需求\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n<<<<<<< HEAD\r\n          cIsDefault: true\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入預設項目成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入預設項目失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入預設項目錯誤:', error);\r\n      this.message.showErrorMSG('載入預設項目失敗');\r\n=======\r\n          CQuotationItemType: CQuotationItemType.客變需求 // 客變需求\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣 // 選樣資料\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim() || item.cUnitPrice < 0 || item.cCount < 1\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱、單價及數量都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId // 傳遞當前的報價單ID\r\n      };\r\n\r\n<<<<<<< HEAD\r\n      console.log('儲存報價單請求:', request); // 除錯用\r\n=======\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n<<<<<<< HEAD\r\n=======\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      itemsHtml += `\r\n        <tr>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${index + 1}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px;\">${item.cItemName}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${item.cCount}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(subtotal)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${quotationType}</td>\r\n        </tr>\r\n      `;\r\n    });\r\n\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <title>報價單列印</title>\r\n        <style>\r\n          body {\r\n            font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;\r\n            margin: 20px;\r\n            font-size: 14px;\r\n          }\r\n          .header {\r\n            text-align: center;\r\n            margin-bottom: 30px;\r\n          }\r\n          .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            color: #333;\r\n          }\r\n          .info-section {\r\n            margin-bottom: 20px;\r\n            border-bottom: 1px solid #ddd;\r\n            padding-bottom: 15px;\r\n          }\r\n          .info-row {\r\n            display: flex;\r\n            margin-bottom: 8px;\r\n          }\r\n          .info-label {\r\n            font-weight: bold;\r\n            width: 100px;\r\n            flex-shrink: 0;\r\n          }\r\n          .info-value {\r\n            flex: 1;\r\n          }\r\n          table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-bottom: 20px;\r\n          }\r\n          th {\r\n            background-color: #27ae60;\r\n            color: white;\r\n            border: 1px solid #ddd;\r\n            padding: 10px 8px;\r\n            text-align: center;\r\n            font-weight: bold;\r\n          }\r\n          td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n          .total-section {\r\n            text-align: right;\r\n            margin-top: 20px;\r\n            padding-top: 15px;\r\n            border-top: 2px solid #27ae60;\r\n          }\r\n          .total-amount {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #27ae60;\r\n          }\r\n          .footer {\r\n            margin-top: 40px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #666;\r\n          }\r\n          .signature-section {\r\n            margin-top: 40px;\r\n            page-break-inside: avoid;\r\n          }\r\n          .signature-row {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            margin-bottom: 30px;\r\n          }\r\n          .signature-box {\r\n            width: 45%;\r\n            text-align: center;\r\n          }\r\n          .signature-label {\r\n            font-weight: bold;\r\n            margin-bottom: 40px;\r\n            font-size: 16px;\r\n          }\r\n          .signature-line {\r\n            border-bottom: 2px solid #000;\r\n            height: 60px;\r\n            margin-bottom: 10px;\r\n            position: relative;\r\n          }\r\n          .signature-date {\r\n            font-size: 14px;\r\n            margin-top: 15px;\r\n          }\r\n          .signature-notes {\r\n            margin-top: 30px;\r\n            padding: 15px;\r\n            background-color: #f9f9f9;\r\n            border-left: 4px solid #27ae60;\r\n          }\r\n          .signature-notes p {\r\n            margin: 0 0 10px 0;\r\n            font-weight: bold;\r\n          }\r\n          .signature-notes ul {\r\n            margin: 0;\r\n            padding-left: 20px;\r\n          }\r\n          .signature-notes li {\r\n            margin-bottom: 5px;\r\n            line-height: 1.4;\r\n          }\r\n          @media print {\r\n            body { margin: 0; }\r\n            .header { page-break-inside: avoid; }\r\n            .signature-section { page-break-inside: avoid; }\r\n          }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"header\">\r\n          <h1>報價單</h1>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">建案名稱：</span>\r\n            <span class=\"info-value\">${buildCaseName}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">戶別：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CHouseHold || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">樓層：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CFloor || ''}樓</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">客戶姓名：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CCustomerName || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">列印日期：</span>\r\n            <span class=\"info-value\">${currentDate}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th width=\"8%\">序號</th>\r\n              <th width=\"35%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"10%\">數量</th>\r\n              <th width=\"20%\">小計 (元)</th>\r\n              <th width=\"12%\">類型</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            ${itemsHtml}\r\n          </tbody>\r\n        </table>\r\n\r\n        <div class=\"total-section\">\r\n          <div class=\"total-amount\">\r\n            總金額：${this.formatCurrency(this.totalAmount)}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"signature-section\">\r\n          <div class=\"signature-row\">\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">客戶簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">業務簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"signature-notes\">\r\n            <p><strong>注意事項：</strong></p>\r\n            <ul>\r\n              <li>此報價單有效期限為30天，逾期需重新報價</li>\r\n              <li>報價內容若有異動，請重新確認</li>\r\n              <li>簽名確認後即視為同意此報價內容</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"footer\">\r\n          此報價單由系統自動產生，列印時間：${new Date().toLocaleString('zh-TW')}\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n>>>>>>> feature/#0000_報價單&加減帳相關調整\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAwCvD,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA6B,SAAQX,aAAa;EAE7DY,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAED,KAAAC,gBAAgB,GAAG,CACjB;MACEH,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;KACR,CACF;IAKD,KAAAE,gBAAgB,GAAU,CAAC;MAAEF,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAK,gBAAgB,GAAU,CAAC;MAAEH,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAM,eAAe,GAAU,CAAC;MAAEJ,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAO,gBAAgB,GAAU,CAAC;MAAEL,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAQ,gBAAgB,GAAU,CAAC;MAAEN,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAS,iBAAiB,GAAU,CAAC;MAAEP,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAEvD,KAAAU,OAAO,GAAG;MACRJ,eAAe,EAAE,IAAI,CAACxB,UAAU,CAAC6B,cAAc,CAACtC,iBAAiB,CAAC;MAClEmC,gBAAgB,EAAE,IAAI,CAAC1B,UAAU,CAAC6B,cAAc,CAACpC,aAAa,CAAC;MAC/DgC,gBAAgB,EAAE,IAAI,CAACzB,UAAU,CAAC6B,cAAc,CAACrC,aAAa;KAC/D;IAGD,KAAAsC,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;KACT;IACD,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,kBAAkB,GAAW,CAAC;IA8G9B,KAAAC,YAAY,GAAgB,IAAI;IAwKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACE7B,KAAK,EAAE,EAAE;MAAEE,KAAK,EAAE;KACnB,CACF;IAxWC,IAAI,CAACX,aAAa,CAACuC,OAAO,EAAE,CAACC,IAAI,CAC/B7D,GAAG,CAAE8D,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACxC,eAAe,GAAGsC,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAyESC,QAAQA,CAAA;IACf,IAAI,CAAC9B,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAACxB,UAAU,CAAC6B,cAAc,CAACtC,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACkC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACzB,UAAU,CAAC6B,cAAc,CAACrC,aAAa,CAAC,CACjD;IACD,IAAI,CAACkC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC1B,UAAU,CAAC6B,cAAc,CAACpC,aAAa,CAAC,CACjD;IACD,IAAI,CAACkC,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAAC3B,UAAU,CAAC6B,cAAc,CAACnC,cAAc,CAAC,CAClD;IAED,IAAIC,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,IAAI,IACtE7D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAIC,SAAS,IAC5E9D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACjE,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,CAAC;MACjG,IAAI,CAACK,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAC,kBAAkB,EAAEL,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,GAC7G,IAAI,CAAClC,gBAAgB,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACK,kBAAkB,CAAC7C,KAAK,CAAC,GACpF,IAAI,CAACK,gBAAgB,CAAC,CAAC,CAAC;QAC5B2C,kBAAkB,EAAER,eAAe,CAACQ,kBAAkB,IAAI,IAAI,IAAIR,eAAe,CAACQ,kBAAkB,IAAIT,SAAS,GAC7G,IAAI,CAAChC,gBAAgB,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACQ,kBAAkB,CAAChD,KAAK,CAAC,GACpF,IAAI,CAACO,gBAAgB,CAAC,CAAC,CAAC;QAC5B0C,kBAAkB,EAAET,eAAe,CAACS,kBAAkB,IAAI,IAAI,IAAIT,eAAe,CAACS,kBAAkB,IAAIV,SAAS,GAC7G,IAAI,CAAC/B,gBAAgB,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACS,kBAAkB,CAACjD,KAAK,CAAC,GACpF,IAAI,CAACQ,gBAAgB,CAAC,CAAC,CAAC;QAC5B0C,iBAAiB,EAAEV,eAAe,CAACU,iBAAiB,IAAI,IAAI,IAAIV,eAAe,CAACU,iBAAiB,IAAIX,SAAS,GAC1G,IAAI,CAACjC,eAAe,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACU,iBAAiB,CAAClD,KAAK,CAAC,GAClF,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC;QAC3B6C,mBAAmB,EAAEX,eAAe,CAACW,mBAAmB,IAAI,IAAI,IAAIX,eAAe,CAACW,mBAAmB,IAAIZ,SAAS,GAChH,IAAI,CAAC9B,iBAAiB,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACW,mBAAmB,CAACnD,KAAK,CAAC,GACtF,IAAI,CAACS,iBAAiB,CAAC,CAAC,CAAC;QAC7B2C,gBAAgB,EAAEZ,eAAe,CAACY,gBAAgB,IAAI,IAAI,IAAIZ,eAAe,CAACY,gBAAgB,IAAIb,SAAS,GACvG,IAAI,CAACpC,gBAAgB,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACY,gBAAgB,CAACpD,KAAK,CAAC,GAClF,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC;QAC5BkD,KAAK,EAAEb,eAAe,CAACa,KAAK,IAAI,IAAI,IAAIb,eAAe,CAACa,KAAK,IAAId,SAAS,GACtEC,eAAe,CAACa,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEd,eAAe,CAACc,GAAG,IAAI,IAAI,IAAId,eAAe,CAACc,GAAG,IAAIf,SAAS,GAChEC,eAAe,CAACc,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACX,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxBW,qBAAqB,EAAE,IAAI,CAAC1B,uBAAuB,CAAC,CAAC,CAAC;QACtDgB,kBAAkB,EAAE,IAAI,CAACxC,gBAAgB,CAAC,CAAC,CAAC;QAC5C2C,kBAAkB,EAAE,IAAI,CAACzC,gBAAgB,CAAC,CAAC,CAAC;QAC5C0C,kBAAkB,EAAE,IAAI,CAACzC,gBAAgB,CAAC,CAAC,CAAC;QAC5C0C,iBAAiB,EAAE,IAAI,CAAC5C,eAAe,CAAC,CAAC,CAAC;QAC1C6C,mBAAmB,EAAE,IAAI,CAAC1C,iBAAiB,CAAC,CAAC,CAAC;QAC9C2C,gBAAgB,EAAE,IAAI,CAACjD,gBAAgB,CAAC,CAAC,CAAC;QAC1CkD,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACE,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBd,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAS,KAAK,EAAE,IAAI,CAACV,WAAW,CAACU,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACX,WAAW,CAACW,GAAG;MACzBT,kBAAkB,EAAE,IAAI,CAACF,WAAW,CAACE,kBAAkB;MACvDG,kBAAkB,EAAE,IAAI,CAACL,WAAW,CAACK,kBAAkB;MACvDI,gBAAgB,EAAE,IAAI,CAACT,WAAW,CAACS,gBAAgB;MACnDH,kBAAkB,EAAE,IAAI,CAACN,WAAW,CAACM,kBAAkB;MACvDC,iBAAiB,EAAE,IAAI,CAACP,WAAW,CAACO,iBAAiB;MACrDC,mBAAmB,EAAE,IAAI,CAACR,WAAW,CAACQ;KACvC;IACD1E,mBAAmB,CAACkF,iBAAiB,CAACjF,WAAW,CAAC4D,YAAY,EAAEG,IAAI,CAACmB,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA2B,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAClE,SAAS,GAAGkE,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA6B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EAAE;MAC3C,IAAI,CAAC/E,aAAa,CAACgF,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;OACnD,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC7E,gBAAgB,CAAC8E,iBAAiB,CACrCtC,GAAG,CAACoC,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAACpF,OAAO,CAACuF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACtD,YAAY,GAAGmD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACE,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvD,YAAY,EAAE;MACrB,MAAMwD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC1D,YAAY,CAAC;MAC3C,IAAI,CAAC1C,aAAa,CAACqG,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJrB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;UACrDwB,KAAK,EAAE,IAAI,CAAC7D;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACrF,OAAO,CAAC0G,aAAa,CAAC1D,GAAG,CAACwC,OAAQ,CAAC;UACxC,IAAI,CAACX,YAAY,EAAE,CAAC1B,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACnD,OAAO,CAACuF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAmB,gBAAgBA,CAAA;IACd,IAAI,CAACzG,aAAa,CAAC0G,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAErB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;MAAG;KAC9D,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChE,gBAAgB,GAAG,CAAC;UACvBL,KAAK,EAAE,EAAE;UAAEE,KAAK,EAAE;SACnB,EAAE,GAAG8B,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE9F,KAAK,EAAE8F,CAAC;YAAE5F,KAAK,EAAE4F;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIrH,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,IAAI,IACtE7D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAIC,SAAS,IAC5E9D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACjE,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAAC1F,gBAAgB,CAAC2F,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAAC/C,KAAK,IAAIwC,eAAe,CAACK,kBAAkB,CAAC7C,KAAK,CAAC;YAC5G,IAAI,CAAC2C,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAACxC,gBAAgB,CAAC0F,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAACxC,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKA4F,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB/B,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;MACrDkC,SAAS,EAAE,IAAI,CAACtG,SAAS;MACzBuG,QAAQ,EAAE,IAAI,CAACxG;KAChB;IAED;IACA;IACA;IACA,IAAI,IAAI,CAAC+C,WAAW,CAACU,KAAK,IAAI,IAAI,CAACV,WAAW,CAACW,GAAG,EAAE;MAClD,IAAI,CAAC4C,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE7C,KAAK,EAAE,IAAI,CAACV,WAAW,CAACU,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACX,WAAW,CAACW;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACX,WAAW,CAACE,kBAAkB,EAAE;MACvC,IAAI,CAACqD,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACE,kBAAkB,CAAC7C,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC2C,WAAW,CAACK,kBAAkB,CAAChD,KAAK,EAAE;MAC7C,IAAI,CAACkG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACK,kBAAkB,CAAChD,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAAC2C,WAAW,CAACS,gBAAgB,CAACpD,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAACkG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACS,gBAAgB,CAACpD,KAAK;IACzE;IACA,IAAI,IAAI,CAAC2C,WAAW,CAACM,kBAAkB,CAACjD,KAAK,EAAE;MAC7C,IAAI,CAACkG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACM,kBAAkB,CAACjD,KAAK;IAC5E;IACA,IAAI,IAAI,CAAC2C,WAAW,CAACO,iBAAiB,CAAClD,KAAK,EAAE;MAC5C,IAAI,CAACkG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACO,iBAAiB,CAAClD,KAAK;IAC1E;IACA,IAAI,IAAI,CAAC2C,WAAW,CAACQ,mBAAmB,CAACnD,KAAK,EAAE;MAC9C,IAAI,CAACkG,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACQ,mBAAmB,CAACnD,KAAK;IAC9E;IAEA,OAAO,IAAI,CAACkG,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,MAAM,IAAI,CAAC,KAAKF,CAAC,CAACE,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA7C,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC3E,aAAa,CAACyH,6BAA6B,CAAC;MACtDnB,IAAI,EAAE,IAAI,CAACS,WAAW;KACvB,CAAC,CAAClE,IAAI,CACL7D,GAAG,CAAC8D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACuC,SAAS,GAAG5E,GAAG,CAACoC,OAAO;QAC5B,IAAI,CAACtE,YAAY,GAAGkC,GAAG,CAAC6E,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACnB,gBAAgB,EAAE;IACvB,IAAI,CAAC9B,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EACAqB,gBAAgBA,CAAA;IACd,IAAI,CAACpE,iBAAiB,CAAC2H,6CAA6C,CAAC;MACnEvB,IAAI,EAAE;QACJwB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAAClF,IAAI,CACL7D,GAAG,CAAC8D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC6C,oBAAoB,GAAGlF,GAAG,CAACoC,OAAO,EAAEc,MAAM,GAAGlD,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAAC7D,GAAG,IAAG;UACtE,OAAO;YACLmF,cAAc,EAAEnF,GAAG,CAACmF,cAAc;YAClClD,GAAG,EAAEjC,GAAG,CAACiC;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAIxF,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,IAAI,IACtE7D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAIC,SAAS,IAC5E9D,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACjE,mBAAmB,CAAC4D,iBAAiB,CAAC3D,WAAW,CAAC4D,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACI,kBAAkB,IAAI,IAAI,IAAIJ,eAAe,CAACI,kBAAkB,IAAIL,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAACmB,oBAAoB,CAAClB,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAACkB,GAAG,IAAIzB,eAAe,CAACI,kBAAkB,CAACqB,GAAG,CAAC;YAC5G,IAAI,CAACtB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAACnB,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACvE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACFhJ,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAACyH,gBAAgB,EAAE;MACvByB,UAAU,CAAC,MAAK;QACd,IAAI,CAACvD,YAAY,EAAE,CAAC1B,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAkF,YAAYA,CAACC,GAAQ,EAAEC,GAAQ;IAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACtI,aAAa,CAACuI,6BAA6B,CAAC;MAC/CjC,IAAI,EAAE;QAAE3E,QAAQ,EAAEyG;MAAG;KACtB,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACqD,WAAW,GAAG;UACjB,GAAG1F,GAAG,CAACoC,OAAO;UACduD,eAAe,EAAE3F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,GAAG,IAAIC,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,CAAC,GAAGrF,SAAS;UAClGuF,aAAa,EAAE9F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,GAAG,IAAIF,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,CAAC,GAAGxF;SACpF;QAED,IAAIP,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,CAACR,cAAc,CAAC5E,kBAAkB,GAAG,IAAI,CAACqF,eAAe,CAAC,IAAI,CAACf,oBAAoB,EAAE,KAAK,EAAElF,GAAG,CAACoC,OAAO,CAAC4D,YAAY,CAAC;QAC3H;QACA,IAAI,CAACR,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACgF,eAAe,CAAC,IAAI,CAACvH,OAAO,CAACF,gBAAgB,EAAE,OAAO,EAAEwB,GAAG,CAACoC,OAAO,CAACpD,UAAU,CAAC;QAC7H,IAAIgB,GAAG,CAACoC,OAAO,CAAC/C,UAAU,EAAE;UAC1B,IAAI,CAACmG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACiF,eAAe,CAAC,IAAI,CAACvH,OAAO,CAACH,gBAAgB,EAAE,OAAO,EAAEyB,GAAG,CAACoC,OAAO,CAAC/C,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACmG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACtC,OAAO,CAACH,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACiH,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAAC+E,eAAe,CAAC,IAAI,CAACvH,OAAO,CAACJ,eAAe,EAAE,OAAO,EAAE0B,GAAG,CAACoC,OAAO,CAAChD,SAAS,CAAC;QAE1H,IAAIY,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACE,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAC/D,YAAY,GAAGnC,GAAG,CAACoC,OAAO,CAAC4D,YAAY;UAC5D;QACF;QACA,IAAI,CAACjJ,aAAa,CAACoJ,IAAI,CAACZ,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAU,eAAeA,CAACG,KAAY,EAAEnI,GAAW,EAAED,KAAU;IACnD,OAAOoI,KAAK,CAACtF,IAAI,CAACuF,IAAI,IAAIA,IAAI,CAACpI,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGAsI,eAAeA,CAACf,GAAQ,EAAEc,IAAS;IACjC,IAAI,CAAChB,YAAY,CAACgB,IAAI,CAACf,GAAG,EAAEC,GAAG,CAAC;EAClC;EAEAgB,SAASA,CAAChB,GAAQ;IAChB,IAAI,CAACW,aAAa,GAAG;MACnBM,aAAa,EAAE,EAAE;MACjB9B,MAAM,EAAEnE,SAAS;MACjBkG,eAAe,EAAElG;KAClB;IACD,IAAI,CAACxD,aAAa,CAACoJ,IAAI,CAACZ,GAAG,CAAC;EAC9B;EAKAmB,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOvK,MAAM,CAACuK,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,cAAcA,CAACtB,GAAQ;IACrB,IAAI,CAACG,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACC,eAAe,GAAG,IAAI,CAACe,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACC,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACD,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACI,aAAa,GAAG,IAAI,CAACY,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACI,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACgB,kBAAkB,GAAG;MACxB5H,aAAa,EAAE,IAAI,CAACwG,WAAW,CAACxG,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAACoG,WAAW,CAACqB,UAAU;MACvClI,QAAQ,EAAE,IAAI,CAAC6G,WAAW,CAACsB,GAAG;MAC9B3H,UAAU,EAAE,IAAI,CAACmG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACwE,cAAc,CAACxE,kBAAkB,CAAChD,KAAK,GAAG,IAAI;MACxGe,SAAS,EAAE,IAAI,CAAC2G,WAAW,CAAC3G,SAAS;MACrCE,SAAS,EAAE,IAAI,CAACyG,WAAW,CAACzG,SAAS;MACrCH,KAAK,EAAE,IAAI,CAAC4G,WAAW,CAAC5G,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAACuG,WAAW,CAACuB,WAAW;MACzCjI,UAAU,EAAE,IAAI,CAACwG,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACuE,cAAc,CAACvE,kBAAkB,CAACjD,KAAK,GAAG,IAAI;MACxGuB,MAAM,EAAE,IAAI,CAACmG,WAAW,CAACnG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAACoG,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAACsE,cAAc,CAACtE,iBAAiB,CAAClD,KAAK,GAAG,IAAI;MACrG4H,gBAAgB,EAAE,IAAI,CAACF,WAAW,CAACE,gBAAgB;MACnDG,cAAc,EAAE,IAAI,CAACL,WAAW,CAACK;KAClC;IACH,IAAI,CAACmB,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjK,KAAK,CAACkK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClG,OAAO,CAACoK,aAAa,CAAC,IAAI,CAACnK,KAAK,CAACkK,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACjK,aAAa,CAACmK,0BAA0B,CAAC;MAC5C7D,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAAC/G,IAAI,CACL7D,GAAG,CAAC8D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrF,OAAO,CAAC0G,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtK,OAAO,CAACuF,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACvC+C,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFrL,SAAS,CAAC,MAAM,IAAI,CAAC4F,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf;EAGAoH,QAAQA,CAAChC,GAAQ;IACf,IAAIiC,OAAO,GAAkB;MAC3BtI,aAAa,EAAE,IAAI,CAACwG,WAAW,CAACxG,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAACoG,WAAW,CAACqB,UAAU;MACvClI,QAAQ,EAAE,IAAI,CAAC6G,WAAW,CAACsB,GAAG;MAC9B3H,UAAU,EAAE,IAAI,CAACqG,WAAW,CAACrG,UAAU;MACvCN,SAAS,EAAE,IAAI,CAAC2G,WAAW,CAAC3G,SAAS;MACrCE,SAAS,EAAE,IAAI,CAACyG,WAAW,CAACzG,SAAS;MACrCH,KAAK,EAAE,IAAI,CAAC4G,WAAW,CAAC5G,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAACuG,WAAW,CAACuB,WAAW;MACzCjI,UAAU,EAAE,IAAI,CAAC0G,WAAW,CAAC1G,UAAU;MACvCO,MAAM,EAAE,IAAI,CAACmG,WAAW,CAACnG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAACsG,WAAW,CAACtG;KAC7B;IACD,IAAI,CAAClC,aAAa,CAACmK,0BAA0B,CAAC;MAC5C7D,IAAI,EAAEgE;KACP,CAAC,CAACrH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrF,OAAO,CAAC0G,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAG,OAAOA,CAAClC,GAAQ;IACdA,GAAG,CAAC+B,KAAK,EAAE;EACb;EAEAI,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACjH,WAAW,CAACC,kBAAkB,CAACqB,GAAG;IAC/D,IAAI,CAAC3E,MAAM,CAACwK,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEAE,4BAA4BA,CAACJ,IAAS,EAAEK,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAAC3K,MAAM,CAACwK,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEK,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEAC,cAAcA,CAAC7B,IAAS;IACtB,IAAI8B,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACjL,aAAa,CAACkL,oCAAoC,CAAC;QACtD5E,IAAI,EAAE6C,IAAI,CAACf;OACZ,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACrF,OAAO,CAAC0G,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAwD,UAAUA,CAAA;IACR,IAAI,CAACjK,KAAK,CAACoL,KAAK,EAAE;IAClB,IAAI,CAACpL,KAAK,CAACqL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5C,WAAW,CAACsB,GAAG,CAAC;IACnD,IAAI,CAAC/J,KAAK,CAACqL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxB,kBAAkB,CAACxH,UAAU,CAAC;IACjE,IAAI,CAACrC,KAAK,CAACsL,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzB,kBAAkB,CAACxH,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACrC,KAAK,CAACqL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,WAAW,CAAChB,MAAM,CAAC;IACpD,IAAI,CAACzH,KAAK,CAACsL,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACzB,kBAAkB,CAAC5H,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACjC,KAAK,CAACuL,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC1B,kBAAkB,CAAChI,KAAK,EAAE,IAAI,CAACzB,OAAO,CAACoL,WAAW,CAAC;IACrF,IAAI,CAACxL,KAAK,CAACyL,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC5B,kBAAkB,CAACvH,MAAM,CAAC;IAClE,IAAI,CAACtC,KAAK,CAACqL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxB,kBAAkB,CAAC1H,SAAS,CAAC;IAC9D,IAAI,CAACnC,KAAK,CAACqL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACxE,kBAAkB,CAAChD,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAACqL,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACvE,kBAAkB,CAACjD,KAAK,CAAC;IAC3E,IAAI,IAAI,CAAC0H,WAAW,CAACE,gBAAgB,EAAE;MACrC,IAAI,CAAC3I,KAAK,CAACqL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACK,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACL,WAAW,CAACK,cAAc,EAAE;MACnC,IAAI,CAAC9I,KAAK,CAACqL,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACE,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAC3I,KAAK,CAAC0L,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACjD,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACE,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACF,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEA6C,uBAAuBA,CAAA;IACrB,IAAI,CAAC3L,KAAK,CAACoL,KAAK,EAAE;IAClB,IAAI,CAACpL,KAAK,CAACqL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAAC/D,YAAY,CAAC;IAC5D,IAAI,CAAClF,KAAK,CAACqL,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAACM,aAAa,CAAC;IAC7D,IAAI,CAACvJ,KAAK,CAACsL,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrC,aAAa,CAACM,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAACvJ,KAAK,CAAC4L,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC3C,aAAa,CAACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAACzH,KAAK,CAAC4L,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC3C,aAAa,CAACO,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAqC,gBAAgBA,CAACvD,GAAQ;IACvB,IAAI,CAACW,aAAa,CAAC/D,YAAY,GAAG,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EACvE,IAAI,CAAC2G,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAAC3L,KAAK,CAACkK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAClG,OAAO,CAACoK,aAAa,CAAC,IAAI,CAACnK,KAAK,CAACkK,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAChK,qBAAqB,CAAC4L,yCAAyC,CAAC;MACnEvF,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAACnG,IAAI,CACL7D,GAAG,CAAC8D,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACrF,OAAO,CAAC0G,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACFrL,SAAS,CAAC,MAAM,IAAI,CAAC4F,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf,CAAC,CAAE;EACG6I,aAAaA,CAACC,MAAW,EAAE5C,IAAS;IAAA,IAAA6C,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAACxJ,YAAY,GAAG2G,IAAI;MACxB6C,KAAI,CAAC1J,cAAc,GAAG,EAAE;MACxB0J,KAAI,CAACzJ,WAAW,GAAG,CAAC;MACpByJ,KAAI,CAACvJ,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAE7B;MACA,IAAI;QACF,MAAMyJ,QAAQ,SAASF,KAAI,CAACzL,gBAAgB,CAAC4L,qBAAqB,CAAChD,IAAI,CAACf,GAAG,CAAC,CAACgE,SAAS,EAAE;QAExFC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEJ,QAAQ,CAAC,CAAC,CAAC;QAI1D,IAAIA,QAAQ,IAAIA,QAAQ,CAAC/G,UAAU,KAAK,CAAC,IAAI+G,QAAQ,CAAChH,OAAO,EAAE;UAC7D;UACA8G,KAAI,CAACvJ,kBAAkB,GAAGyJ,QAAQ,CAAChH,OAAO,CAACqH,YAAY,IAAI,CAAC;UAE5DF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEN,KAAI,CAACvJ,kBAAkB,CAAC,CAAC,CAAC;UAKlD,IAAIyJ,QAAQ,CAAChH,OAAO,CAACsH,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAChH,OAAO,CAACsH,KAAK,CAAC,EAAE;YACnE;YACAR,KAAI,CAAC1J,cAAc,GAAG4J,QAAQ,CAAChH,OAAO,CAACsH,KAAK,CAAC7F,GAAG,CAAEgG,KAAU,KAAM;cAChEC,QAAQ,EAAEV,QAAQ,CAAChH,OAAO,CAACvD,QAAQ,IAAIwH,IAAI,CAACf,GAAG;cAC/CyE,YAAY,EAAEX,QAAQ,CAAChH,OAAO,CAACqH,YAAY;cAC3CO,SAAS,EAAEH,KAAK,CAACI,SAAS,IAAI,EAAE;cAChCC,UAAU,EAAEL,KAAK,CAACM,UAAU,IAAI,CAAC;cACjCC,MAAM,EAAEP,KAAK,CAACQ,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAET,KAAK,CAAC5E,OAAO,IAAI,CAAC;cAE3BsF,UAAU,EAAEV,KAAK,CAACW,UAAU,IAAI;aACjC,CAAC,CAAC;YACHtB,KAAI,CAACuB,cAAc,EAAE;YACrBlB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEN,KAAI,CAAC1J,cAAc,CAAC0D,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;UAC7D,CAAC,MAAM;YACLqG,OAAO,CAACmB,IAAI,CAAC,yBAAyB,EAAEtB,QAAQ,CAAChH,OAAO,CAAC;UAC3D;QACF,CAAC,MAAM;UACLmH,OAAO,CAACmB,IAAI,CAAC,iCAAiC,EAAEtB,QAAQ,CAAC;QAW3D;MACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAzB,KAAI,CAACnM,aAAa,CAACoJ,IAAI,CAAC8C,MAAM,EAAE;QAC9B2B,OAAO,EAAEvE,IAAI;QACbwE,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EACA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACtL,cAAc,CAACuL,IAAI,CAAC;MACvBjB,QAAQ,EAAE,IAAI,CAACpK,YAAY,EAAE4F,GAAG,IAAI,CAAC;MAErC0E,SAAS,EAAE,EAAE;MAAEE,UAAU,EAAE,CAAC;MAC5BE,MAAM,EAAE,CAAC;MACTE,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE;KACb,CAAC;EACJ;EACA;EAWMS,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MACpB,IAAI;QACF,IAAI,CAAC8B,MAAI,CAACvL,YAAY,EAAE4F,GAAG,EAAE;UAC3B2F,MAAI,CAACjO,OAAO,CAACuF,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM2I,OAAO,GAAG;UACd/I,YAAY,EAAE8I,MAAI,CAACvL,YAAY,CAACyC,YAAY,IAAI,CAAC;UACjDtD,QAAQ,EAAEoM,MAAI,CAACvL,YAAY,CAAC4F;SAC7B;QAED,MAAM8D,QAAQ,SAAS6B,MAAI,CAACxN,gBAAgB,CAACuN,gBAAgB,CAACE,OAAO,CAAC,CAAC5B,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE+B,OAAO,IAAI/B,QAAQ,CAACgC,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAGjC,QAAQ,CAACgC,IAAI,CAACvH,GAAG,CAAE9C,CAAM,KAAM;YAClDgJ,YAAY,EAAEhJ,CAAC,CAAC0I,YAAY;YAC5BK,QAAQ,EAAEmB,MAAI,CAACvL,YAAY,EAAE4F,GAAG;YAChC0E,SAAS,EAAEjJ,CAAC,CAACkJ,SAAS;YACtBC,UAAU,EAAEnJ,CAAC,CAACoJ,UAAU;YACxBC,MAAM,EAAErJ,CAAC,CAACsJ,MAAM;YAChBC,OAAO,EAAEvJ,CAAC,CAACkE,OAAO;YAElBsF,UAAU,EAAE;WACb,CAAC,CAAC;UACHU,MAAI,CAACzL,cAAc,CAACuL,IAAI,CAAC,GAAGM,YAAY,CAAC;UACzCJ,MAAI,CAACR,cAAc,EAAE;UACrBQ,MAAI,CAACjO,OAAO,CAAC0G,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLuH,MAAI,CAACjO,OAAO,CAACuF,YAAY,CAAC6G,QAAQ,EAAEpM,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO2N,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCM,MAAI,CAACjO,OAAO,CAACuF,YAAY,CAAC,UAAU,CAAC;MAkDvC;IAAC;EACH;EAEA;EACA+I,mBAAmBA,CAACvH,KAAa;IAC/B,MAAMsC,IAAI,GAAG,IAAI,CAAC7G,cAAc,CAACuE,KAAK,CAAC;IACvC,IAAI,CAACvE,cAAc,CAAC+L,MAAM,CAACxH,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAAC0G,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAAChL,WAAW,GAAG,IAAI,CAACD,cAAc,CAACgM,MAAM,CAAC,CAACC,GAAG,EAAEpF,IAAI,KAAI;MAC1D,OAAOoF,GAAG,GAAIpF,IAAI,CAAC6D,UAAU,GAAG7D,IAAI,CAAC+D,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAsB,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACpF,MAAM,CAAC+E,MAAM,CAAC;EACnB;EAEA;EACMM,aAAaA,CAAC1G,GAAQ;IAAA,IAAA2G,MAAA;IAAA,OAAA/C,iBAAA;MAC1B,IAAI+C,MAAI,CAAC1M,cAAc,CAAC0D,MAAM,KAAK,CAAC,EAAE;QACpCgJ,MAAI,CAAClP,OAAO,CAACuF,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAM4J,YAAY,GAAGD,MAAI,CAAC1M,cAAc,CAAC4M,MAAM,CAAC/F,IAAI,IAClD,CAACA,IAAI,CAAC2D,SAAS,CAACqC,IAAI,EAAE,IAAIhG,IAAI,CAAC6D,UAAU,GAAG,CAAC,IAAI7D,IAAI,CAAC+D,MAAM,GAAG,CAAC,CACjE;MAED,IAAI+B,YAAY,CAACjJ,MAAM,GAAG,CAAC,EAAE;QAC3BgJ,MAAI,CAAClP,OAAO,CAACuF,YAAY,CAAC,uBAAuB,CAAC;QAClD;MACF;MAAE,IAAI;QACJ,MAAM2I,OAAO,GAAG;UACdjD,OAAO,EAAEiE,MAAI,CAACxM,YAAY,CAAC4F,GAAG;UAC9BgH,KAAK,EAAEJ,MAAI,CAAC1M,cAAc;UAC1B+M,WAAW,EAAEL,MAAI,CAACvM,kBAAkB,CAAC;SACtC;QAGD4J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,OAAO,CAAC,CAAC,CAAC;QAGlC,MAAM9B,QAAQ,SAAS8C,MAAI,CAACzO,gBAAgB,CAACwO,aAAa,CAACf,OAAO,CAAC,CAAC5B,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAE+B,OAAO,EAAE;UACrBe,MAAI,CAAClP,OAAO,CAAC0G,aAAa,CAAC,SAAS,CAAC;UACrC6B,GAAG,CAAC+B,KAAK,EAAE;QACb,CAAC,MAAM;UACL4E,MAAI,CAAClP,OAAO,CAACuF,YAAY,CAAC6G,QAAQ,EAAEpM,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAO2N,KAAK,EAAE;QACduB,MAAI,CAAClP,OAAO,CAACuF,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMiK,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtD,iBAAA;MACnB,IAAI;QACF,MAAMuD,IAAI,SAA2BD,MAAI,CAAChP,gBAAgB,CAAC+O,eAAe,CAACC,MAAI,CAAC/M,YAAY,CAAC4F,GAAG,CAAC,CAACgE,SAAS,EAAE;QAC7G,IAAIoD,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAAC/M,YAAY,CAACJ,UAAU,IAAImN,MAAI,CAAC/M,YAAY,CAACgF,MAAM,OAAO;UACtFqI,IAAI,CAACnK,KAAK,EAAE;UACZgK,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAACzP,OAAO,CAACuF,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOoI,KAAK,EAAE;QACd8B,MAAI,CAACzP,OAAO,CAACuF,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EAuRH;CACD;AAh7ByB8K,UAAA,EAAvBzR,SAAS,CAAC,WAAW,CAAC,C,8DAAyC;AA/MrDe,4BAA4B,GAAA0Q,UAAA,EARxC1R,SAAS,CAAC;EACT2R,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC,CAAC;EACpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC5R,YAAY,EAAED,YAAY,EAAEE,kBAAkB,EAAEI,mBAAmB;CAC9E,CAAC,C,EAEWQ,4BAA4B,CA+nCxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}