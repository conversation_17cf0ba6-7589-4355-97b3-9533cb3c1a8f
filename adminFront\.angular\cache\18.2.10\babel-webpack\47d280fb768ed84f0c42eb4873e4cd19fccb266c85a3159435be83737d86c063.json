{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SystemManagementRoutingModule } from './system-management-routing.module';\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\nimport { UserManagementComponent } from './user-management/user-management.component';\nimport { LogsManagementComponent } from './logs-management/logs-management.component';\nimport { SharedModule } from '../components/shared.module';\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\nlet SystemManagementModule = class SystemManagementModule {};\nSystemManagementModule = __decorate([NgModule({\n  imports: [CommonModule, SharedModule, SystemManagementRoutingModule, RolePermissionsComponent, UserManagementComponent, LogsManagementComponent, NotificationSettingComponent]\n})], SystemManagementModule);\nexport { SystemManagementModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "SystemManagementRoutingModule", "RolePermissionsComponent", "UserManagementComponent", "LogsManagementComponent", "SharedModule", "NotificationSettingComponent", "SystemManagementModule", "__decorate", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\system-management.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SystemManagementRoutingModule } from './system-management-routing.module';\r\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\r\nimport { UserManagementComponent } from './user-management/user-management.component';\r\nimport { LogsManagementComponent } from './logs-management/logs-management.component';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\r\n\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        SharedModule,\r\n        SystemManagementRoutingModule,\r\n        RolePermissionsComponent,\r\n        UserManagementComponent,\r\n        LogsManagementComponent,\r\n        NotificationSettingComponent\r\n    ]\r\n})\r\nexport class SystemManagementModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,6BAA6B,QAAQ,oCAAoC;AAClF,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,4BAA4B,QAAQ,uDAAuD;AAc7F,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB,GAAI;AAA1BA,sBAAsB,GAAAC,UAAA,EAXlCT,QAAQ,CAAC;EACNU,OAAO,EAAE,CACLT,YAAY,EACZK,YAAY,EACZJ,6BAA6B,EAC7BC,wBAAwB,EACxBC,uBAAuB,EACvBC,uBAAuB,EACvBE,4BAA4B;CAEnC,CAAC,C,EACWC,sBAAsB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}