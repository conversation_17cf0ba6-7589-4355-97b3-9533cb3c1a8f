{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ApproveWaiting5Component {\n  static {\n    this.ɵfac = function ApproveWaiting5Component_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApproveWaiting5Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApproveWaiting5Component,\n      selectors: [[\"app-approve-waiting5\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 0,\n      template: function ApproveWaiting5Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"approve-waiting5 works!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(2, \",\\n\");\n        }\n      },\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcHJvdmUtd2FpdGluZzUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJmaWxlIjoiYXBwcm92ZS13YWl0aW5nNS5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xuICBkaXNwbGF5OiBibG9jaztcbn1cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYXBwcm92ZS13YWl0aW5nNS9hcHByb3ZlLXdhaXRpbmc1LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBLDRUQUE0VCIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgZGlzcGxheTogYmxvY2s7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["ApproveWaiting5Component", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "ApproveWaiting5Component_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting5\\approve-waiting5.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting5\\approve-waiting5.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n  selector: 'app-approve-waiting5',\n  standalone: true,\n  imports: [],\n  templateUrl: './approve-waiting5.component.html',\n  styleUrl: './approve-waiting5.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ApproveWaiting5Component { }\n", "<p>approve-waiting5 works!</p>,\n"], "mappings": ";AAUA,OAAM,MAAOA,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVrCN,EAAA,CAAAQ,cAAA,QAAG;UAAAR,EAAA,CAAAS,MAAA,8BAAuB;UAAAT,EAAA,CAAAU,YAAA,EAAI;UAAAV,EAAA,CAAAS,MAAA,UAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}