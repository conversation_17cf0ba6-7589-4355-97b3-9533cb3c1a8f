{"ast": null, "code": "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\nfunction effect(_ref) {\n  var state = _ref.state,\n    instance = _ref.instance,\n    options = _ref.options;\n  var _options$scroll = options.scroll,\n    scroll = _options$scroll === void 0 ? true : _options$scroll,\n    _options$resize = options.resize,\n    resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "map": {"version": 3, "names": ["getWindow", "passive", "effect", "_ref", "state", "instance", "options", "_options$scroll", "scroll", "_options$resize", "resize", "window", "elements", "popper", "scrollParents", "concat", "reference", "for<PERSON>ach", "scrollParent", "addEventListener", "update", "removeEventListener", "name", "enabled", "phase", "fn", "data"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@popperjs/core/lib/modifiers/eventListeners.js"], "sourcesContent": ["import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};"], "mappings": "AAAA,OAAOA,SAAS,MAAM,2BAA2B,CAAC,CAAC;;AAEnD,IAAIC,OAAO,GAAG;EACZA,OAAO,EAAE;AACX,CAAC;AAED,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,OAAO,GAAGH,IAAI,CAACG,OAAO;EAC1B,IAAIC,eAAe,GAAGD,OAAO,CAACE,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC5DE,eAAe,GAAGH,OAAO,CAACI,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;EAChE,IAAIE,MAAM,GAAGX,SAAS,CAACI,KAAK,CAACQ,QAAQ,CAACC,MAAM,CAAC;EAC7C,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACX,KAAK,CAACU,aAAa,CAACE,SAAS,EAAEZ,KAAK,CAACU,aAAa,CAACD,MAAM,CAAC;EAExF,IAAIL,MAAM,EAAE;IACVM,aAAa,CAACG,OAAO,CAAC,UAAUC,YAAY,EAAE;MAC5CA,YAAY,CAACC,gBAAgB,CAAC,QAAQ,EAAEd,QAAQ,CAACe,MAAM,EAAEnB,OAAO,CAAC;IACnE,CAAC,CAAC;EACJ;EAEA,IAAIS,MAAM,EAAE;IACVC,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAEd,QAAQ,CAACe,MAAM,EAAEnB,OAAO,CAAC;EAC7D;EAEA,OAAO,YAAY;IACjB,IAAIO,MAAM,EAAE;MACVM,aAAa,CAACG,OAAO,CAAC,UAAUC,YAAY,EAAE;QAC5CA,YAAY,CAACG,mBAAmB,CAAC,QAAQ,EAAEhB,QAAQ,CAACe,MAAM,EAAEnB,OAAO,CAAC;MACtE,CAAC,CAAC;IACJ;IAEA,IAAIS,MAAM,EAAE;MACVC,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEhB,QAAQ,CAACe,MAAM,EAAEnB,OAAO,CAAC;IAChE;EACF,CAAC;AACH,CAAC,CAAC;;AAGF,eAAe;EACbqB,IAAI,EAAE,gBAAgB;EACtBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,OAAO;EACdC,EAAE,EAAE,SAASA,EAAEA,CAAA,EAAG,CAAC,CAAC;EACpBvB,MAAM,EAAEA,MAAM;EACdwB,IAAI,EAAE,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}