{"ast": null, "code": "export class TrafficBarData {}", "map": {"version": 3, "names": ["TrafficBarData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\traffic-bar.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface TrafficBar {\r\n  data: number[];\r\n  labels: string[];\r\n  formatter: string;\r\n}\r\n\r\nexport abstract class TrafficBarData {\r\n  abstract getTrafficBarData(period: string): Observable<TrafficBar>;\r\n}\r\n"], "mappings": "AAQA,OAAM,MAAgBA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}