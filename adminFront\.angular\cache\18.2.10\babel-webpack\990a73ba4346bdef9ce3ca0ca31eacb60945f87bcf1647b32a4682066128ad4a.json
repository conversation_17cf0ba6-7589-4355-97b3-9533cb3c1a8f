{"ast": null, "code": "export class UserData {}", "map": {"version": 3, "names": ["UserData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\users.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface User {\r\n  name: string;\r\n  picture: string;\r\n}\r\n\r\nexport interface Contacts {\r\n  user: User;\r\n  type: string;\r\n}\r\n\r\nexport interface RecentUsers extends Contacts {\r\n  time: number;\r\n}\r\n\r\nexport abstract class UserData {\r\n  abstract getUsers(): Observable<User[]>;\r\n  abstract getContacts(): Observable<Contacts[]>;\r\n  abstract getRecentUsers(): Observable<RecentUsers[]>;\r\n}\r\n"], "mappings": "AAgBA,OAAM,MAAgBA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}