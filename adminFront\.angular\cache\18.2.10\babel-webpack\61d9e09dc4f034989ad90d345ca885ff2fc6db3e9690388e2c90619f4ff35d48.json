{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Turkmen [tk]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/atamyratabdy\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    1: \"'inji\",\n    5: \"'inji\",\n    8: \"'inji\",\n    70: \"'inji\",\n    80: \"'inji\",\n    2: \"'nji\",\n    7: \"'nji\",\n    20: \"'nji\",\n    50: \"'nji\",\n    3: \"'ünji\",\n    4: \"'ünji\",\n    100: \"'ünji\",\n    6: \"'njy\",\n    9: \"'unjy\",\n    10: \"'unjy\",\n    30: \"'unjy\",\n    60: \"'ynjy\",\n    90: \"'ynjy\"\n  };\n  var tk = moment.defineLocale('tk', {\n    months: 'Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr'.split('_'),\n    monthsShort: 'Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek'.split('_'),\n    weekdays: 'Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe'.split('_'),\n    weekdaysShort: 'Ýek_Duş_Siş_Çar_Pen_Ann_Şen'.split('_'),\n    weekdaysMin: 'Ýk_Dş_Sş_Çr_Pn_An_Şn'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[bugün sagat] LT',\n      nextDay: '[ertir sagat] LT',\n      nextWeek: '[indiki] dddd [sagat] LT',\n      lastDay: '[düýn] LT',\n      lastWeek: '[geçen] dddd [sagat] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s soň',\n      past: '%s öň',\n      s: 'birnäçe sekunt',\n      m: 'bir minut',\n      mm: '%d minut',\n      h: 'bir sagat',\n      hh: '%d sagat',\n      d: 'bir gün',\n      dd: '%d gün',\n      M: 'bir aý',\n      MM: '%d aý',\n      y: 'bir ýyl',\n      yy: '%d ýyl'\n    },\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'Do':\n        case 'DD':\n          return number;\n        default:\n          if (number === 0) {\n            // special case for zero\n            return number + \"'unjy\";\n          }\n          var a = number % 10,\n            b = number % 100 - a,\n            c = number >= 100 ? 100 : null;\n          return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return tk;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "suffixes", "tk", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "ordinal", "number", "period", "a", "b", "c", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/tk.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Turkmen [tk]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/atamyratabdy\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var suffixes = {\n        1: \"'inji\",\n        5: \"'inji\",\n        8: \"'inji\",\n        70: \"'inji\",\n        80: \"'inji\",\n        2: \"'nji\",\n        7: \"'nji\",\n        20: \"'nji\",\n        50: \"'nji\",\n        3: \"'ünji\",\n        4: \"'ünji\",\n        100: \"'ünji\",\n        6: \"'njy\",\n        9: \"'unjy\",\n        10: \"'unjy\",\n        30: \"'unjy\",\n        60: \"'ynjy\",\n        90: \"'ynjy\",\n    };\n\n    var tk = moment.defineLocale('tk', {\n        months: 'Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr'.split(\n            '_'\n        ),\n        monthsShort: 'Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek'.split('_'),\n        weekdays: 'Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe'.split(\n            '_'\n        ),\n        weekdaysShort: 'Ýek_Duş_Siş_Çar_Pen_Ann_Şen'.split('_'),\n        weekdaysMin: 'Ýk_Dş_Sş_Çr_Pn_An_Şn'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[bugün sagat] LT',\n            nextDay: '[ertir sagat] LT',\n            nextWeek: '[indiki] dddd [sagat] LT',\n            lastDay: '[düýn] LT',\n            lastWeek: '[geçen] dddd [sagat] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s soň',\n            past: '%s öň',\n            s: 'birnäçe sekunt',\n            m: 'bir minut',\n            mm: '%d minut',\n            h: 'bir sagat',\n            hh: '%d sagat',\n            d: 'bir gün',\n            dd: '%d gün',\n            M: 'bir aý',\n            MM: '%d aý',\n            y: 'bir ýyl',\n            yy: '%d ýyl',\n        },\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'Do':\n                case 'DD':\n                    return number;\n                default:\n                    if (number === 0) {\n                        // special case for zero\n                        return number + \"'unjy\";\n                    }\n                    var a = number % 10,\n                        b = (number % 100) - a,\n                        c = number >= 100 ? 100 : null;\n                    return number + (suffixes[a] || suffixes[b] || suffixes[c]);\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return tk;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,QAAQ,GAAG;IACX,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,MAAM;IACT,EAAE,EAAE,MAAM;IACV,EAAE,EAAE,MAAM;IACV,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,OAAO;IACV,GAAG,EAAE,OAAO;IACZ,CAAC,EAAE,MAAM;IACT,CAAC,EAAE,OAAO;IACV,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;IACX,EAAE,EAAE;EACR,CAAC;EAED,IAAIC,EAAE,GAAGF,MAAM,CAACG,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,8EAA8E,CAACC,KAAK,CACxF,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,uDAAuD,CAACF,KAAK,CACnE,GACJ,CAAC;IACDG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,kBAAkB;MAC3BC,OAAO,EAAE,kBAAkB;MAC3BC,QAAQ,EAAE,0BAA0B;MACpCC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,yBAAyB;MACnCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,gBAAgB;MACnBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,IAAI;QACT,KAAK,IAAI;UACL,OAAOD,MAAM;QACjB;UACI,IAAIA,MAAM,KAAK,CAAC,EAAE;YACd;YACA,OAAOA,MAAM,GAAG,OAAO;UAC3B;UACA,IAAIE,CAAC,GAAGF,MAAM,GAAG,EAAE;YACfG,CAAC,GAAIH,MAAM,GAAG,GAAG,GAAIE,CAAC;YACtBE,CAAC,GAAGJ,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;UAClC,OAAOA,MAAM,IAAItC,QAAQ,CAACwC,CAAC,CAAC,IAAIxC,QAAQ,CAACyC,CAAC,CAAC,IAAIzC,QAAQ,CAAC0C,CAAC,CAAC,CAAC;MACnE;IACJ,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}