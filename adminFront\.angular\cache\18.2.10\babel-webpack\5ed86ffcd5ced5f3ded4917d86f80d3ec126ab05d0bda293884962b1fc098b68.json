{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { NavigationEnd } from '@angular/router';\nimport { NB_DOCUMENT } from '@nebular/theme';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SeoService {\n  constructor(router, document, platformId) {\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.isBrowser = isPlatformBrowser(platformId);\n    this.dom = document;\n    if (this.isBrowser) {\n      this.createCanonicalTag();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  createCanonicalTag() {\n    this.linkCanonical = this.dom.createElement('link');\n    this.linkCanonical.setAttribute('rel', 'canonical');\n    this.dom.head.appendChild(this.linkCanonical);\n    this.linkCanonical.setAttribute('href', this.getCanonicalUrl());\n  }\n  trackCanonicalChanges() {\n    if (!this.isBrowser) {\n      return;\n    }\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(() => {\n      this.linkCanonical.setAttribute('href', this.getCanonicalUrl());\n    });\n  }\n  getCanonicalUrl() {\n    return this.dom.location.origin + this.dom.location.pathname;\n  }\n  static {\n    this.ɵfac = function SeoService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SeoService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(NB_DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SeoService,\n      factory: SeoService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["PLATFORM_ID", "isPlatformBrowser", "NavigationEnd", "NB_DOCUMENT", "filter", "takeUntil", "Subject", "SeoService", "constructor", "router", "document", "platformId", "destroy$", "<PERSON><PERSON><PERSON><PERSON>", "dom", "createCanonicalTag", "ngOnDestroy", "next", "complete", "linkCanonical", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "getCanonicalUrl", "trackCanonicalChanges", "events", "pipe", "event", "subscribe", "location", "origin", "pathname", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\utils\\seo.service.ts"], "sourcesContent": ["import { Injectable, Inject, PLATFORM_ID, OnDestroy } from '@angular/core';\r\nimport { isPlatformBrowser } from '@angular/common';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { NB_DOCUMENT } from '@nebular/theme';\r\nimport { filter, takeUntil } from 'rxjs/operators';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class SeoService implements OnDestroy {\r\n\r\n  private readonly destroy$ = new Subject<void>();\r\n  private readonly dom: Document;\r\n  private readonly isBrowser: boolean;\r\n  private linkCanonical?: HTMLLinkElement;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    @Inject(NB_DOCUMENT) document: any,\r\n    @Inject(PLATFORM_ID) platformId: any,\r\n  ) {\r\n    this.isBrowser = isPlatformBrowser(platformId);\r\n    this.dom = document;\r\n\r\n    if (this.isBrowser) {\r\n      this.createCanonicalTag();\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  createCanonicalTag() {\r\n    this.linkCanonical = this.dom.createElement('link');\r\n    this.linkCanonical.setAttribute('rel', 'canonical');\r\n    this.dom.head.appendChild(this.linkCanonical);\r\n    this.linkCanonical.setAttribute('href', this.getCanonicalUrl());\r\n  }\r\n\r\n  trackCanonicalChanges() {\r\n    if (!this.isBrowser) {\r\n      return;\r\n    }\r\n\r\n    this.router.events.pipe(\r\n      filter((event) => event instanceof NavigationEnd),\r\n      takeUntil(this.destroy$),\r\n    )\r\n      .subscribe(() => {\r\n        this.linkCanonical!.setAttribute('href', this.getCanonicalUrl());\r\n      });\r\n  }\r\n\r\n  private getCanonicalUrl(): string {\r\n    return this.dom.location.origin + this.dom.location.pathname;\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAA6BA,WAAW,QAAmB,eAAe;AAC1E,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,aAAa,QAAgB,iBAAiB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,MAAM;;;AAG9B,OAAM,MAAOC,UAAU;EAOrBC,YACUC,MAAc,EACDC,QAAa,EACbC,UAAe;IAF5B,KAAAF,MAAM,GAANA,MAAM;IANC,KAAAG,QAAQ,GAAG,IAAIN,OAAO,EAAQ;IAU7C,IAAI,CAACO,SAAS,GAAGZ,iBAAiB,CAACU,UAAU,CAAC;IAC9C,IAAI,CAACG,GAAG,GAAGJ,QAAQ;IAEnB,IAAI,IAAI,CAACG,SAAS,EAAE;MAClB,IAAI,CAACE,kBAAkB,EAAE;IAC3B;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAACI,aAAa,GAAG,IAAI,CAACL,GAAG,CAACM,aAAa,CAAC,MAAM,CAAC;IACnD,IAAI,CAACD,aAAa,CAACE,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC;IACnD,IAAI,CAACP,GAAG,CAACQ,IAAI,CAACC,WAAW,CAAC,IAAI,CAACJ,aAAa,CAAC;IAC7C,IAAI,CAACA,aAAa,CAACE,YAAY,CAAC,MAAM,EAAE,IAAI,CAACG,eAAe,EAAE,CAAC;EACjE;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACZ,SAAS,EAAE;MACnB;IACF;IAEA,IAAI,CAACJ,MAAM,CAACiB,MAAM,CAACC,IAAI,CACrBvB,MAAM,CAAEwB,KAAK,IAAKA,KAAK,YAAY1B,aAAa,CAAC,EACjDG,SAAS,CAAC,IAAI,CAACO,QAAQ,CAAC,CACzB,CACEiB,SAAS,CAAC,MAAK;MACd,IAAI,CAACV,aAAc,CAACE,YAAY,CAAC,MAAM,EAAE,IAAI,CAACG,eAAe,EAAE,CAAC;IAClE,CAAC,CAAC;EACN;EAEQA,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACV,GAAG,CAACgB,QAAQ,CAACC,MAAM,GAAG,IAAI,CAACjB,GAAG,CAACgB,QAAQ,CAACE,QAAQ;EAC9D;;;uCAhDWzB,UAAU,EAAA0B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CASX/B,WAAW,GAAA8B,EAAA,CAAAC,QAAA,CACXlC,WAAW;IAAA;EAAA;;;aAVVO,UAAU;MAAA8B,OAAA,EAAV9B,UAAU,CAAA+B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}