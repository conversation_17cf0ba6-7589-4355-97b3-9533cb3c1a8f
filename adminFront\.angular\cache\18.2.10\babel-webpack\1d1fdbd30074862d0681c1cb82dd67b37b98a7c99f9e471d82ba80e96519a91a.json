{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\build-case-file\\api-build-case-file-save-multiple-build-case-file-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Params {\r\n      body?: {\r\n'CBuildCaseID'?: number;\r\n'CCategoryName'?: string;\r\n'CFiles'?: Array<Blob>;\r\n}\r\n}\r\n\r\nexport function apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseFileSaveMultipleBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/SaveMultipleBuildCaseFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAYtD,OAAM,SAAUC,mDAAmDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAmE,EAAEC,OAAqB;EAC/L,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,mDAAmD,CAACM,IAAI,EAAE,MAAM,CAAC;EACxG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,mDAAmD,CAACM,IAAI,GAAG,8CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}