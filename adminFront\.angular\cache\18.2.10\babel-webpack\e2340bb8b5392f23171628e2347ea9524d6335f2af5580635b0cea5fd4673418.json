{"ast": null, "code": "export var EnumSignStatus = /*#__PURE__*/function (EnumSignStatus) {\n  EnumSignStatus[EnumSignStatus[\"\\u5DF2\\u7C3D\\u56DE\"] = 1] = \"\\u5DF2\\u7C3D\\u56DE\";\n  EnumSignStatus[EnumSignStatus[\"\\u672A\\u7C3D\\u56DE\"] = 2] = \"\\u672A\\u7C3D\\u56DE\"; //not signed back\n  return EnumSignStatus;\n}(EnumSignStatus || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}