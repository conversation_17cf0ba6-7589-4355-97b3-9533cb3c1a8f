{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiInfoPictureGetInfoPicturelListPost$Json } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-json';\nimport { apiInfoPictureGetInfoPicturelListPost$Plain } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-plain';\nimport { apiInfoPictureUploadListInfoPicturePost$Json } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-json';\nimport { apiInfoPictureUploadListInfoPicturePost$Plain } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let InfoPictureService = /*#__PURE__*/(() => {\n  class InfoPictureService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiInfoPictureGetInfoPicturelListPost()` */\n    static {\n      this.ApiInfoPictureGetInfoPicturelListPostPath = '/api/InfoPicture/GetInfoPicturelList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiInfoPictureGetInfoPicturelListPost$Plain$Response(params, context) {\n      return apiInfoPictureGetInfoPicturelListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiInfoPictureGetInfoPicturelListPost$Plain(params, context) {\n      return this.apiInfoPictureGetInfoPicturelListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiInfoPictureGetInfoPicturelListPost$Json$Response(params, context) {\n      return apiInfoPictureGetInfoPicturelListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiInfoPictureGetInfoPicturelListPost$Json(params, context) {\n      return this.apiInfoPictureGetInfoPicturelListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiInfoPictureUploadListInfoPicturePost()` */\n    static {\n      this.ApiInfoPictureUploadListInfoPicturePostPath = '/api/InfoPicture/UploadListInfoPicture';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiInfoPictureUploadListInfoPicturePost$Plain$Response(params, context) {\n      return apiInfoPictureUploadListInfoPicturePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiInfoPictureUploadListInfoPicturePost$Plain(params, context) {\n      return this.apiInfoPictureUploadListInfoPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiInfoPictureUploadListInfoPicturePost$Json$Response(params, context) {\n      return apiInfoPictureUploadListInfoPicturePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiInfoPictureUploadListInfoPicturePost$Json(params, context) {\n      return this.apiInfoPictureUploadListInfoPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function InfoPictureService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || InfoPictureService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: InfoPictureService,\n        factory: InfoPictureService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return InfoPictureService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}