{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { config, Emitter, elementClosest, applyStyle, whenTransitionDone, removeElement, ScrollController, ElementScrollController, computeInnerRect, WindowScrollController, ElementDragging, preventSelection, preventContextMenu, allowSelection, allowContextMenu, computeRect, getClippingParents, pointInsideRect, constrainPoint, intersectRects, getRectCenter, diffPoints, mapHash, rangeContainsRange, isDateSpansEqual, Interaction, interactionSettingsToStore, isDateSelectionValid, enableCursor, disableCursor, triggerDateSelect, compareNumbers, getElSeg, getRelevantEvents, EventImpl, createEmptyEventStore, applyMutationToEventStore, isInteractionValid, buildEventApis, interactionSettingsStore, startOfDay, diffDates, createDuration, getEventTargetViaRoot, identity, eventTupleToStore, parseDragMeta, elementMatches, refineEventDef, parseEventDef, getDefaultEventEnd, createEventInstance, BASE_OPTION_DEFAULTS } from '@fullcalendar/core/internal.js';\nconfig.touchMouseIgnoreWait = 500;\nlet ignoreMouseDepth = 0;\nlet listenerCnt = 0;\nlet isWindowTouchMoveCancelled = false;\n/*\nUses a \"pointer\" abstraction, which monitors UI events for both mouse and touch.\nTracks when the pointer \"drags\" on a certain element, meaning down+move+up.\n\nAlso, tracks if there was touch-scrolling.\nAlso, can prevent touch-scrolling from happening.\nAlso, can fire pointermove events when scrolling happens underneath, even when no real pointer movement.\n\nemits:\n- pointerdown\n- pointermove\n- pointerup\n*/\nclass PointerDragging {\n  constructor(containerEl) {\n    this.subjectEl = null;\n    // options that can be directly assigned by caller\n    this.selector = ''; // will cause subjectEl in all emitted events to be this element\n    this.handleSelector = '';\n    this.shouldIgnoreMove = false;\n    this.shouldWatchScroll = true; // for simulating pointermove on scroll\n    // internal states\n    this.isDragging = false;\n    this.isTouchDragging = false;\n    this.wasTouchScroll = false;\n    // Mouse\n    // ----------------------------------------------------------------------------------------------------\n    this.handleMouseDown = ev => {\n      if (!this.shouldIgnoreMouse() && isPrimaryMouseButton(ev) && this.tryStart(ev)) {\n        let pev = this.createEventFromMouse(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        if (!this.shouldIgnoreMove) {\n          document.addEventListener('mousemove', this.handleMouseMove);\n        }\n        document.addEventListener('mouseup', this.handleMouseUp);\n      }\n    };\n    this.handleMouseMove = ev => {\n      let pev = this.createEventFromMouse(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleMouseUp = ev => {\n      document.removeEventListener('mousemove', this.handleMouseMove);\n      document.removeEventListener('mouseup', this.handleMouseUp);\n      this.emitter.trigger('pointerup', this.createEventFromMouse(ev));\n      this.cleanup(); // call last so that pointerup has access to props\n    };\n    // Touch\n    // ----------------------------------------------------------------------------------------------------\n    this.handleTouchStart = ev => {\n      if (this.tryStart(ev)) {\n        this.isTouchDragging = true;\n        let pev = this.createEventFromTouch(ev, true);\n        this.emitter.trigger('pointerdown', pev);\n        this.initScrollWatch(pev);\n        // unlike mouse, need to attach to target, not document\n        // https://stackoverflow.com/a/45760014\n        let targetEl = ev.target;\n        if (!this.shouldIgnoreMove) {\n          targetEl.addEventListener('touchmove', this.handleTouchMove);\n        }\n        targetEl.addEventListener('touchend', this.handleTouchEnd);\n        targetEl.addEventListener('touchcancel', this.handleTouchEnd); // treat it as a touch end\n        // attach a handler to get called when ANY scroll action happens on the page.\n        // this was impossible to do with normal on/off because 'scroll' doesn't bubble.\n        // http://stackoverflow.com/a/32954565/96342\n        window.addEventListener('scroll', this.handleTouchScroll, true);\n      }\n    };\n    this.handleTouchMove = ev => {\n      let pev = this.createEventFromTouch(ev);\n      this.recordCoords(pev);\n      this.emitter.trigger('pointermove', pev);\n    };\n    this.handleTouchEnd = ev => {\n      if (this.isDragging) {\n        // done to guard against touchend followed by touchcancel\n        let targetEl = ev.target;\n        targetEl.removeEventListener('touchmove', this.handleTouchMove);\n        targetEl.removeEventListener('touchend', this.handleTouchEnd);\n        targetEl.removeEventListener('touchcancel', this.handleTouchEnd);\n        window.removeEventListener('scroll', this.handleTouchScroll, true); // useCaptured=true\n        this.emitter.trigger('pointerup', this.createEventFromTouch(ev));\n        this.cleanup(); // call last so that pointerup has access to props\n        this.isTouchDragging = false;\n        startIgnoringMouse();\n      }\n    };\n    this.handleTouchScroll = () => {\n      this.wasTouchScroll = true;\n    };\n    this.handleScroll = ev => {\n      if (!this.shouldIgnoreMove) {\n        let pageX = window.scrollX - this.prevScrollX + this.prevPageX;\n        let pageY = window.scrollY - this.prevScrollY + this.prevPageY;\n        this.emitter.trigger('pointermove', {\n          origEvent: ev,\n          isTouch: this.isTouchDragging,\n          subjectEl: this.subjectEl,\n          pageX,\n          pageY,\n          deltaX: pageX - this.origPageX,\n          deltaY: pageY - this.origPageY\n        });\n      }\n    };\n    this.containerEl = containerEl;\n    this.emitter = new Emitter();\n    containerEl.addEventListener('mousedown', this.handleMouseDown);\n    containerEl.addEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerCreated();\n  }\n  destroy() {\n    this.containerEl.removeEventListener('mousedown', this.handleMouseDown);\n    this.containerEl.removeEventListener('touchstart', this.handleTouchStart, {\n      passive: true\n    });\n    listenerDestroyed();\n  }\n  tryStart(ev) {\n    let subjectEl = this.querySubjectEl(ev);\n    let downEl = ev.target;\n    if (subjectEl && (!this.handleSelector || elementClosest(downEl, this.handleSelector))) {\n      this.subjectEl = subjectEl;\n      this.isDragging = true; // do this first so cancelTouchScroll will work\n      this.wasTouchScroll = false;\n      return true;\n    }\n    return false;\n  }\n  cleanup() {\n    isWindowTouchMoveCancelled = false;\n    this.isDragging = false;\n    this.subjectEl = null;\n    // keep wasTouchScroll around for later access\n    this.destroyScrollWatch();\n  }\n  querySubjectEl(ev) {\n    if (this.selector) {\n      return elementClosest(ev.target, this.selector);\n    }\n    return this.containerEl;\n  }\n  shouldIgnoreMouse() {\n    return ignoreMouseDepth || this.isTouchDragging;\n  }\n  // can be called by user of this class, to cancel touch-based scrolling for the current drag\n  cancelTouchScroll() {\n    if (this.isDragging) {\n      isWindowTouchMoveCancelled = true;\n    }\n  }\n  // Scrolling that simulates pointermoves\n  // ----------------------------------------------------------------------------------------------------\n  initScrollWatch(ev) {\n    if (this.shouldWatchScroll) {\n      this.recordCoords(ev);\n      window.addEventListener('scroll', this.handleScroll, true); // useCapture=true\n    }\n  }\n  recordCoords(ev) {\n    if (this.shouldWatchScroll) {\n      this.prevPageX = ev.pageX;\n      this.prevPageY = ev.pageY;\n      this.prevScrollX = window.scrollX;\n      this.prevScrollY = window.scrollY;\n    }\n  }\n  destroyScrollWatch() {\n    if (this.shouldWatchScroll) {\n      window.removeEventListener('scroll', this.handleScroll, true); // useCaptured=true\n    }\n  }\n  // Event Normalization\n  // ----------------------------------------------------------------------------------------------------\n  createEventFromMouse(ev, isFirst) {\n    let deltaX = 0;\n    let deltaY = 0;\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = ev.pageX;\n      this.origPageY = ev.pageY;\n    } else {\n      deltaX = ev.pageX - this.origPageX;\n      deltaY = ev.pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: false,\n      subjectEl: this.subjectEl,\n      pageX: ev.pageX,\n      pageY: ev.pageY,\n      deltaX,\n      deltaY\n    };\n  }\n  createEventFromTouch(ev, isFirst) {\n    let touches = ev.touches;\n    let pageX;\n    let pageY;\n    let deltaX = 0;\n    let deltaY = 0;\n    // if touch coords available, prefer,\n    // because FF would give bad ev.pageX ev.pageY\n    if (touches && touches.length) {\n      pageX = touches[0].pageX;\n      pageY = touches[0].pageY;\n    } else {\n      pageX = ev.pageX;\n      pageY = ev.pageY;\n    }\n    // TODO: repeat code\n    if (isFirst) {\n      this.origPageX = pageX;\n      this.origPageY = pageY;\n    } else {\n      deltaX = pageX - this.origPageX;\n      deltaY = pageY - this.origPageY;\n    }\n    return {\n      origEvent: ev,\n      isTouch: true,\n      subjectEl: this.subjectEl,\n      pageX,\n      pageY,\n      deltaX,\n      deltaY\n    };\n  }\n}\n// Returns a boolean whether this was a left mouse click and no ctrl key (which means right click on Mac)\nfunction isPrimaryMouseButton(ev) {\n  return ev.button === 0 && !ev.ctrlKey;\n}\n// Ignoring fake mouse events generated by touch\n// ----------------------------------------------------------------------------------------------------\nfunction startIgnoringMouse() {\n  ignoreMouseDepth += 1;\n  setTimeout(() => {\n    ignoreMouseDepth -= 1;\n  }, config.touchMouseIgnoreWait);\n}\n// We want to attach touchmove as early as possible for Safari\n// ----------------------------------------------------------------------------------------------------\nfunction listenerCreated() {\n  listenerCnt += 1;\n  if (listenerCnt === 1) {\n    window.addEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction listenerDestroyed() {\n  listenerCnt -= 1;\n  if (!listenerCnt) {\n    window.removeEventListener('touchmove', onWindowTouchMove, {\n      passive: false\n    });\n  }\n}\nfunction onWindowTouchMove(ev) {\n  if (isWindowTouchMoveCancelled) {\n    ev.preventDefault();\n  }\n}\n\n/*\nAn effect in which an element follows the movement of a pointer across the screen.\nThe moving element is a clone of some other element.\nMust call start + handleMove + stop.\n*/\nclass ElementMirror {\n  constructor() {\n    this.isVisible = false; // must be explicitly enabled\n    this.sourceEl = null;\n    this.mirrorEl = null;\n    this.sourceElRect = null; // screen coords relative to viewport\n    // options that can be set directly by caller\n    this.parentNode = document.body; // HIGHLY SUGGESTED to set this to sidestep ShadowDOM issues\n    this.zIndex = 9999;\n    this.revertDuration = 0;\n  }\n  start(sourceEl, pageX, pageY) {\n    this.sourceEl = sourceEl;\n    this.sourceElRect = this.sourceEl.getBoundingClientRect();\n    this.origScreenX = pageX - window.scrollX;\n    this.origScreenY = pageY - window.scrollY;\n    this.deltaX = 0;\n    this.deltaY = 0;\n    this.updateElPosition();\n  }\n  handleMove(pageX, pageY) {\n    this.deltaX = pageX - window.scrollX - this.origScreenX;\n    this.deltaY = pageY - window.scrollY - this.origScreenY;\n    this.updateElPosition();\n  }\n  // can be called before start\n  setIsVisible(bool) {\n    if (bool) {\n      if (!this.isVisible) {\n        if (this.mirrorEl) {\n          this.mirrorEl.style.display = '';\n        }\n        this.isVisible = bool; // needs to happen before updateElPosition\n        this.updateElPosition(); // because was not updating the position while invisible\n      }\n    } else if (this.isVisible) {\n      if (this.mirrorEl) {\n        this.mirrorEl.style.display = 'none';\n      }\n      this.isVisible = bool;\n    }\n  }\n  // always async\n  stop(needsRevertAnimation, callback) {\n    let done = () => {\n      this.cleanup();\n      callback();\n    };\n    if (needsRevertAnimation && this.mirrorEl && this.isVisible && this.revertDuration && (\n    // if 0, transition won't work\n    this.deltaX || this.deltaY) // if same coords, transition won't work\n    ) {\n      this.doRevertAnimation(done, this.revertDuration);\n    } else {\n      setTimeout(done, 0);\n    }\n  }\n  doRevertAnimation(callback, revertDuration) {\n    let mirrorEl = this.mirrorEl;\n    let finalSourceElRect = this.sourceEl.getBoundingClientRect(); // because autoscrolling might have happened\n    mirrorEl.style.transition = 'top ' + revertDuration + 'ms,' + 'left ' + revertDuration + 'ms';\n    applyStyle(mirrorEl, {\n      left: finalSourceElRect.left,\n      top: finalSourceElRect.top\n    });\n    whenTransitionDone(mirrorEl, () => {\n      mirrorEl.style.transition = '';\n      callback();\n    });\n  }\n  cleanup() {\n    if (this.mirrorEl) {\n      removeElement(this.mirrorEl);\n      this.mirrorEl = null;\n    }\n    this.sourceEl = null;\n  }\n  updateElPosition() {\n    if (this.sourceEl && this.isVisible) {\n      applyStyle(this.getMirrorEl(), {\n        left: this.sourceElRect.left + this.deltaX,\n        top: this.sourceElRect.top + this.deltaY\n      });\n    }\n  }\n  getMirrorEl() {\n    let sourceElRect = this.sourceElRect;\n    let mirrorEl = this.mirrorEl;\n    if (!mirrorEl) {\n      mirrorEl = this.mirrorEl = this.sourceEl.cloneNode(true); // cloneChildren=true\n      // we don't want long taps or any mouse interaction causing selection/menus.\n      // would use preventSelection(), but that prevents selectstart, causing problems.\n      mirrorEl.style.userSelect = 'none';\n      mirrorEl.style.webkitUserSelect = 'none';\n      mirrorEl.style.pointerEvents = 'none';\n      mirrorEl.classList.add('fc-event-dragging');\n      applyStyle(mirrorEl, {\n        position: 'fixed',\n        zIndex: this.zIndex,\n        visibility: '',\n        boxSizing: 'border-box',\n        width: sourceElRect.right - sourceElRect.left,\n        height: sourceElRect.bottom - sourceElRect.top,\n        right: 'auto',\n        bottom: 'auto',\n        margin: 0\n      });\n      this.parentNode.appendChild(mirrorEl);\n    }\n    return mirrorEl;\n  }\n}\n\n/*\nIs a cache for a given element's scroll information (all the info that ScrollController stores)\nin addition the \"client rectangle\" of the element.. the area within the scrollbars.\n\nThe cache can be in one of two modes:\n- doesListening:false - ignores when the container is scrolled by someone else\n- doesListening:true - watch for scrolling and update the cache\n*/\nclass ScrollGeomCache extends ScrollController {\n  constructor(scrollController, doesListening) {\n    super();\n    this.handleScroll = () => {\n      this.scrollTop = this.scrollController.getScrollTop();\n      this.scrollLeft = this.scrollController.getScrollLeft();\n      this.handleScrollChange();\n    };\n    this.scrollController = scrollController;\n    this.doesListening = doesListening;\n    this.scrollTop = this.origScrollTop = scrollController.getScrollTop();\n    this.scrollLeft = this.origScrollLeft = scrollController.getScrollLeft();\n    this.scrollWidth = scrollController.getScrollWidth();\n    this.scrollHeight = scrollController.getScrollHeight();\n    this.clientWidth = scrollController.getClientWidth();\n    this.clientHeight = scrollController.getClientHeight();\n    this.clientRect = this.computeClientRect(); // do last in case it needs cached values\n    if (this.doesListening) {\n      this.getEventTarget().addEventListener('scroll', this.handleScroll);\n    }\n  }\n  destroy() {\n    if (this.doesListening) {\n      this.getEventTarget().removeEventListener('scroll', this.handleScroll);\n    }\n  }\n  getScrollTop() {\n    return this.scrollTop;\n  }\n  getScrollLeft() {\n    return this.scrollLeft;\n  }\n  setScrollTop(top) {\n    this.scrollController.setScrollTop(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollTop = Math.max(Math.min(top, this.getMaxScrollTop()), 0);\n      this.handleScrollChange();\n    }\n  }\n  setScrollLeft(top) {\n    this.scrollController.setScrollLeft(top);\n    if (!this.doesListening) {\n      // we are not relying on the element to normalize out-of-bounds scroll values\n      // so we need to sanitize ourselves\n      this.scrollLeft = Math.max(Math.min(top, this.getMaxScrollLeft()), 0);\n      this.handleScrollChange();\n    }\n  }\n  getClientWidth() {\n    return this.clientWidth;\n  }\n  getClientHeight() {\n    return this.clientHeight;\n  }\n  getScrollWidth() {\n    return this.scrollWidth;\n  }\n  getScrollHeight() {\n    return this.scrollHeight;\n  }\n  handleScrollChange() {}\n}\nclass ElementScrollGeomCache extends ScrollGeomCache {\n  constructor(el, doesListening) {\n    super(new ElementScrollController(el), doesListening);\n  }\n  getEventTarget() {\n    return this.scrollController.el;\n  }\n  computeClientRect() {\n    return computeInnerRect(this.scrollController.el);\n  }\n}\nclass WindowScrollGeomCache extends ScrollGeomCache {\n  constructor(doesListening) {\n    super(new WindowScrollController(), doesListening);\n  }\n  getEventTarget() {\n    return window;\n  }\n  computeClientRect() {\n    return {\n      left: this.scrollLeft,\n      right: this.scrollLeft + this.clientWidth,\n      top: this.scrollTop,\n      bottom: this.scrollTop + this.clientHeight\n    };\n  }\n  // the window is the only scroll object that changes it's rectangle relative\n  // to the document's topleft as it scrolls\n  handleScrollChange() {\n    this.clientRect = this.computeClientRect();\n  }\n}\n\n// If available we are using native \"performance\" API instead of \"Date\"\n// Read more about it on MDN:\n// https://developer.mozilla.org/en-US/docs/Web/API/Performance\nconst getTime = typeof performance === 'function' ? performance.now : Date.now;\n/*\nFor a pointer interaction, automatically scrolls certain scroll containers when the pointer\napproaches the edge.\n\nThe caller must call start + handleMove + stop.\n*/\nclass AutoScroller {\n  constructor() {\n    // options that can be set by caller\n    this.isEnabled = true;\n    this.scrollQuery = [window, '.fc-scroller'];\n    this.edgeThreshold = 50; // pixels\n    this.maxVelocity = 300; // pixels per second\n    // internal state\n    this.pointerScreenX = null;\n    this.pointerScreenY = null;\n    this.isAnimating = false;\n    this.scrollCaches = null;\n    // protect against the initial pointerdown being too close to an edge and starting the scroll\n    this.everMovedUp = false;\n    this.everMovedDown = false;\n    this.everMovedLeft = false;\n    this.everMovedRight = false;\n    this.animate = () => {\n      if (this.isAnimating) {\n        // wasn't cancelled between animation calls\n        let edge = this.computeBestEdge(this.pointerScreenX + window.scrollX, this.pointerScreenY + window.scrollY);\n        if (edge) {\n          let now = getTime();\n          this.handleSide(edge, (now - this.msSinceRequest) / 1000);\n          this.requestAnimation(now);\n        } else {\n          this.isAnimating = false; // will stop animation\n        }\n      }\n    };\n  }\n  start(pageX, pageY, scrollStartEl) {\n    if (this.isEnabled) {\n      this.scrollCaches = this.buildCaches(scrollStartEl);\n      this.pointerScreenX = null;\n      this.pointerScreenY = null;\n      this.everMovedUp = false;\n      this.everMovedDown = false;\n      this.everMovedLeft = false;\n      this.everMovedRight = false;\n      this.handleMove(pageX, pageY);\n    }\n  }\n  handleMove(pageX, pageY) {\n    if (this.isEnabled) {\n      let pointerScreenX = pageX - window.scrollX;\n      let pointerScreenY = pageY - window.scrollY;\n      let yDelta = this.pointerScreenY === null ? 0 : pointerScreenY - this.pointerScreenY;\n      let xDelta = this.pointerScreenX === null ? 0 : pointerScreenX - this.pointerScreenX;\n      if (yDelta < 0) {\n        this.everMovedUp = true;\n      } else if (yDelta > 0) {\n        this.everMovedDown = true;\n      }\n      if (xDelta < 0) {\n        this.everMovedLeft = true;\n      } else if (xDelta > 0) {\n        this.everMovedRight = true;\n      }\n      this.pointerScreenX = pointerScreenX;\n      this.pointerScreenY = pointerScreenY;\n      if (!this.isAnimating) {\n        this.isAnimating = true;\n        this.requestAnimation(getTime());\n      }\n    }\n  }\n  stop() {\n    if (this.isEnabled) {\n      this.isAnimating = false; // will stop animation\n      for (let scrollCache of this.scrollCaches) {\n        scrollCache.destroy();\n      }\n      this.scrollCaches = null;\n    }\n  }\n  requestAnimation(now) {\n    this.msSinceRequest = now;\n    requestAnimationFrame(this.animate);\n  }\n  handleSide(edge, seconds) {\n    let {\n      scrollCache\n    } = edge;\n    let {\n      edgeThreshold\n    } = this;\n    let invDistance = edgeThreshold - edge.distance;\n    let velocity =\n    // the closer to the edge, the faster we scroll\n    invDistance * invDistance / (edgeThreshold * edgeThreshold) *\n    // quadratic\n    this.maxVelocity * seconds;\n    let sign = 1;\n    switch (edge.name) {\n      case 'left':\n        sign = -1;\n      // falls through\n      case 'right':\n        scrollCache.setScrollLeft(scrollCache.getScrollLeft() + velocity * sign);\n        break;\n      case 'top':\n        sign = -1;\n      // falls through\n      case 'bottom':\n        scrollCache.setScrollTop(scrollCache.getScrollTop() + velocity * sign);\n        break;\n    }\n  }\n  // left/top are relative to document topleft\n  computeBestEdge(left, top) {\n    let {\n      edgeThreshold\n    } = this;\n    let bestSide = null;\n    let scrollCaches = this.scrollCaches || [];\n    for (let scrollCache of scrollCaches) {\n      let rect = scrollCache.clientRect;\n      let leftDist = left - rect.left;\n      let rightDist = rect.right - left;\n      let topDist = top - rect.top;\n      let bottomDist = rect.bottom - top;\n      // completely within the rect?\n      if (leftDist >= 0 && rightDist >= 0 && topDist >= 0 && bottomDist >= 0) {\n        if (topDist <= edgeThreshold && this.everMovedUp && scrollCache.canScrollUp() && (!bestSide || bestSide.distance > topDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'top',\n            distance: topDist\n          };\n        }\n        if (bottomDist <= edgeThreshold && this.everMovedDown && scrollCache.canScrollDown() && (!bestSide || bestSide.distance > bottomDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'bottom',\n            distance: bottomDist\n          };\n        }\n        /*\n        TODO: fix broken RTL scrolling. canScrollLeft always returning false\n        https://github.com/fullcalendar/fullcalendar/issues/4837\n        */\n        if (leftDist <= edgeThreshold && this.everMovedLeft && scrollCache.canScrollLeft() && (!bestSide || bestSide.distance > leftDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'left',\n            distance: leftDist\n          };\n        }\n        if (rightDist <= edgeThreshold && this.everMovedRight && scrollCache.canScrollRight() && (!bestSide || bestSide.distance > rightDist)) {\n          bestSide = {\n            scrollCache,\n            name: 'right',\n            distance: rightDist\n          };\n        }\n      }\n    }\n    return bestSide;\n  }\n  buildCaches(scrollStartEl) {\n    return this.queryScrollEls(scrollStartEl).map(el => {\n      if (el === window) {\n        return new WindowScrollGeomCache(false); // false = don't listen to user-generated scrolls\n      }\n      return new ElementScrollGeomCache(el, false); // false = don't listen to user-generated scrolls\n    });\n  }\n  queryScrollEls(scrollStartEl) {\n    let els = [];\n    for (let query of this.scrollQuery) {\n      if (typeof query === 'object') {\n        els.push(query);\n      } else {\n        /*\n        TODO: in the future, always have auto-scroll happen on element where current Hit came from\n        Ticket: https://github.com/fullcalendar/fullcalendar/issues/4593\n        */\n        els.push(...Array.prototype.slice.call(scrollStartEl.getRootNode().querySelectorAll(query)));\n      }\n    }\n    return els;\n  }\n}\n\n/*\nMonitors dragging on an element. Has a number of high-level features:\n- minimum distance required before dragging\n- minimum wait time (\"delay\") before dragging\n- a mirror element that follows the pointer\n*/\nclass FeaturefulElementDragging extends ElementDragging {\n  constructor(containerEl, selector) {\n    super(containerEl);\n    this.containerEl = containerEl;\n    // options that can be directly set by caller\n    // the caller can also set the PointerDragging's options as well\n    this.delay = null;\n    this.minDistance = 0;\n    this.touchScrollAllowed = true; // prevents drag from starting and blocks scrolling during drag\n    this.mirrorNeedsRevert = false;\n    this.isInteracting = false; // is the user validly moving the pointer? lasts until pointerup\n    this.isDragging = false; // is it INTENTFULLY dragging? lasts until after revert animation\n    this.isDelayEnded = false;\n    this.isDistanceSurpassed = false;\n    this.delayTimeoutId = null;\n    this.onPointerDown = ev => {\n      if (!this.isDragging) {\n        // so new drag doesn't happen while revert animation is going\n        this.isInteracting = true;\n        this.isDelayEnded = false;\n        this.isDistanceSurpassed = false;\n        preventSelection(document.body);\n        preventContextMenu(document.body);\n        // prevent links from being visited if there's an eventual drag.\n        // also prevents selection in older browsers (maybe?).\n        // not necessary for touch, besides, browser would complain about passiveness.\n        if (!ev.isTouch) {\n          ev.origEvent.preventDefault();\n        }\n        this.emitter.trigger('pointerdown', ev);\n        if (this.isInteracting &&\n        // not destroyed via pointerdown handler\n        !this.pointer.shouldIgnoreMove) {\n          // actions related to initiating dragstart+dragmove+dragend...\n          this.mirror.setIsVisible(false); // reset. caller must set-visible\n          this.mirror.start(ev.subjectEl, ev.pageX, ev.pageY); // must happen on first pointer down\n          this.startDelay(ev);\n          if (!this.minDistance) {\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n      }\n    };\n    this.onPointerMove = ev => {\n      if (this.isInteracting) {\n        this.emitter.trigger('pointermove', ev);\n        if (!this.isDistanceSurpassed) {\n          let minDistance = this.minDistance;\n          let distanceSq; // current distance from the origin, squared\n          let {\n            deltaX,\n            deltaY\n          } = ev;\n          distanceSq = deltaX * deltaX + deltaY * deltaY;\n          if (distanceSq >= minDistance * minDistance) {\n            // use pythagorean theorem\n            this.handleDistanceSurpassed(ev);\n          }\n        }\n        if (this.isDragging) {\n          // a real pointer move? (not one simulated by scrolling)\n          if (ev.origEvent.type !== 'scroll') {\n            this.mirror.handleMove(ev.pageX, ev.pageY);\n            this.autoScroller.handleMove(ev.pageX, ev.pageY);\n          }\n          this.emitter.trigger('dragmove', ev);\n        }\n      }\n    };\n    this.onPointerUp = ev => {\n      if (this.isInteracting) {\n        this.isInteracting = false;\n        allowSelection(document.body);\n        allowContextMenu(document.body);\n        this.emitter.trigger('pointerup', ev); // can potentially set mirrorNeedsRevert\n        if (this.isDragging) {\n          this.autoScroller.stop();\n          this.tryStopDrag(ev); // which will stop the mirror\n        }\n        if (this.delayTimeoutId) {\n          clearTimeout(this.delayTimeoutId);\n          this.delayTimeoutId = null;\n        }\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.onPointerDown);\n    pointer.emitter.on('pointermove', this.onPointerMove);\n    pointer.emitter.on('pointerup', this.onPointerUp);\n    if (selector) {\n      pointer.selector = selector;\n    }\n    this.mirror = new ElementMirror();\n    this.autoScroller = new AutoScroller();\n  }\n  destroy() {\n    this.pointer.destroy();\n    // HACK: simulate a pointer-up to end the current drag\n    // TODO: fire 'dragend' directly and stop interaction. discourage use of pointerup event (b/c might not fire)\n    this.onPointerUp({});\n  }\n  startDelay(ev) {\n    if (typeof this.delay === 'number') {\n      this.delayTimeoutId = setTimeout(() => {\n        this.delayTimeoutId = null;\n        this.handleDelayEnd(ev);\n      }, this.delay); // not assignable to number!\n    } else {\n      this.handleDelayEnd(ev);\n    }\n  }\n  handleDelayEnd(ev) {\n    this.isDelayEnded = true;\n    this.tryStartDrag(ev);\n  }\n  handleDistanceSurpassed(ev) {\n    this.isDistanceSurpassed = true;\n    this.tryStartDrag(ev);\n  }\n  tryStartDrag(ev) {\n    if (this.isDelayEnded && this.isDistanceSurpassed) {\n      if (!this.pointer.wasTouchScroll || this.touchScrollAllowed) {\n        this.isDragging = true;\n        this.mirrorNeedsRevert = false;\n        this.autoScroller.start(ev.pageX, ev.pageY, this.containerEl);\n        this.emitter.trigger('dragstart', ev);\n        if (this.touchScrollAllowed === false) {\n          this.pointer.cancelTouchScroll();\n        }\n      }\n    }\n  }\n  tryStopDrag(ev) {\n    // .stop() is ALWAYS asynchronous, which we NEED because we want all pointerup events\n    // that come from the document to fire beforehand. much more convenient this way.\n    this.mirror.stop(this.mirrorNeedsRevert, this.stopDrag.bind(this, ev));\n  }\n  stopDrag(ev) {\n    this.isDragging = false;\n    this.emitter.trigger('dragend', ev);\n  }\n  // fill in the implementations...\n  setIgnoreMove(bool) {\n    this.pointer.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    this.mirror.setIsVisible(bool);\n  }\n  setMirrorNeedsRevert(bool) {\n    this.mirrorNeedsRevert = bool;\n  }\n  setAutoScrollEnabled(bool) {\n    this.autoScroller.isEnabled = bool;\n  }\n}\n\n/*\nWhen this class is instantiated, it records the offset of an element (relative to the document topleft),\nand continues to monitor scrolling, updating the cached coordinates if it needs to.\nDoes not access the DOM after instantiation, so highly performant.\n\nAlso keeps track of all scrolling/overflow:hidden containers that are parents of the given element\nand an determine if a given point is inside the combined clipping rectangle.\n*/\nclass OffsetTracker {\n  constructor(el) {\n    this.el = el;\n    this.origRect = computeRect(el);\n    // will work fine for divs that have overflow:hidden\n    this.scrollCaches = getClippingParents(el).map(scrollEl => new ElementScrollGeomCache(scrollEl, true));\n  }\n  destroy() {\n    for (let scrollCache of this.scrollCaches) {\n      scrollCache.destroy();\n    }\n  }\n  computeLeft() {\n    let left = this.origRect.left;\n    for (let scrollCache of this.scrollCaches) {\n      left += scrollCache.origScrollLeft - scrollCache.getScrollLeft();\n    }\n    return left;\n  }\n  computeTop() {\n    let top = this.origRect.top;\n    for (let scrollCache of this.scrollCaches) {\n      top += scrollCache.origScrollTop - scrollCache.getScrollTop();\n    }\n    return top;\n  }\n  isWithinClipping(pageX, pageY) {\n    let point = {\n      left: pageX,\n      top: pageY\n    };\n    for (let scrollCache of this.scrollCaches) {\n      if (!isIgnoredClipping(scrollCache.getEventTarget()) && !pointInsideRect(point, scrollCache.clientRect)) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n// certain clipping containers should never constrain interactions, like <html> and <body>\n// https://github.com/fullcalendar/fullcalendar/issues/3615\nfunction isIgnoredClipping(node) {\n  let tagName = node.tagName;\n  return tagName === 'HTML' || tagName === 'BODY';\n}\n\n/*\nTracks movement over multiple droppable areas (aka \"hits\")\nthat exist in one or more DateComponents.\nRelies on an existing draggable.\n\nemits:\n- pointerdown\n- dragstart\n- hitchange - fires initially, even if not over a hit\n- pointerup\n- (hitchange - again, to null, if ended over a hit)\n- dragend\n*/\nclass HitDragging {\n  constructor(dragging, droppableStore) {\n    // options that can be set by caller\n    this.useSubjectCenter = false;\n    this.requireInitial = true; // if doesn't start out on a hit, won't emit any events\n    this.disablePointCheck = false;\n    this.initialHit = null;\n    this.movingHit = null;\n    this.finalHit = null; // won't ever be populated if shouldIgnoreMove\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      this.initialHit = null;\n      this.movingHit = null;\n      this.finalHit = null;\n      this.prepareHits();\n      this.processFirstCoord(ev);\n      if (this.initialHit || !this.requireInitial) {\n        dragging.setIgnoreMove(false);\n        // TODO: fire this before computing processFirstCoord, so listeners can cancel. this gets fired by almost every handler :(\n        this.emitter.trigger('pointerdown', ev);\n      } else {\n        dragging.setIgnoreMove(true);\n      }\n    };\n    this.handleDragStart = ev => {\n      this.emitter.trigger('dragstart', ev);\n      this.handleMove(ev, true); // force = fire even if initially null\n    };\n    this.handleDragMove = ev => {\n      this.emitter.trigger('dragmove', ev);\n      this.handleMove(ev);\n    };\n    this.handlePointerUp = ev => {\n      this.releaseHits();\n      this.emitter.trigger('pointerup', ev);\n    };\n    this.handleDragEnd = ev => {\n      if (this.movingHit) {\n        this.emitter.trigger('hitupdate', null, true, ev);\n      }\n      this.finalHit = this.movingHit;\n      this.movingHit = null;\n      this.emitter.trigger('dragend', ev);\n    };\n    this.droppableStore = droppableStore;\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    dragging.emitter.on('dragmove', this.handleDragMove);\n    dragging.emitter.on('pointerup', this.handlePointerUp);\n    dragging.emitter.on('dragend', this.handleDragEnd);\n    this.dragging = dragging;\n    this.emitter = new Emitter();\n  }\n  // sets initialHit\n  // sets coordAdjust\n  processFirstCoord(ev) {\n    let origPoint = {\n      left: ev.pageX,\n      top: ev.pageY\n    };\n    let adjustedPoint = origPoint;\n    let subjectEl = ev.subjectEl;\n    let subjectRect;\n    if (subjectEl instanceof HTMLElement) {\n      // i.e. not a Document/ShadowRoot\n      subjectRect = computeRect(subjectEl);\n      adjustedPoint = constrainPoint(adjustedPoint, subjectRect);\n    }\n    let initialHit = this.initialHit = this.queryHitForOffset(adjustedPoint.left, adjustedPoint.top);\n    if (initialHit) {\n      if (this.useSubjectCenter && subjectRect) {\n        let slicedSubjectRect = intersectRects(subjectRect, initialHit.rect);\n        if (slicedSubjectRect) {\n          adjustedPoint = getRectCenter(slicedSubjectRect);\n        }\n      }\n      this.coordAdjust = diffPoints(adjustedPoint, origPoint);\n    } else {\n      this.coordAdjust = {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n  handleMove(ev, forceHandle) {\n    let hit = this.queryHitForOffset(ev.pageX + this.coordAdjust.left, ev.pageY + this.coordAdjust.top);\n    if (forceHandle || !isHitsEqual(this.movingHit, hit)) {\n      this.movingHit = hit;\n      this.emitter.trigger('hitupdate', hit, false, ev);\n    }\n  }\n  prepareHits() {\n    this.offsetTrackers = mapHash(this.droppableStore, interactionSettings => {\n      interactionSettings.component.prepareHits();\n      return new OffsetTracker(interactionSettings.el);\n    });\n  }\n  releaseHits() {\n    let {\n      offsetTrackers\n    } = this;\n    for (let id in offsetTrackers) {\n      offsetTrackers[id].destroy();\n    }\n    this.offsetTrackers = {};\n  }\n  queryHitForOffset(offsetLeft, offsetTop) {\n    let {\n      droppableStore,\n      offsetTrackers\n    } = this;\n    let bestHit = null;\n    for (let id in droppableStore) {\n      let component = droppableStore[id].component;\n      let offsetTracker = offsetTrackers[id];\n      if (offsetTracker &&\n      // wasn't destroyed mid-drag\n      offsetTracker.isWithinClipping(offsetLeft, offsetTop)) {\n        let originLeft = offsetTracker.computeLeft();\n        let originTop = offsetTracker.computeTop();\n        let positionLeft = offsetLeft - originLeft;\n        let positionTop = offsetTop - originTop;\n        let {\n          origRect\n        } = offsetTracker;\n        let width = origRect.right - origRect.left;\n        let height = origRect.bottom - origRect.top;\n        if (\n        // must be within the element's bounds\n        positionLeft >= 0 && positionLeft < width && positionTop >= 0 && positionTop < height) {\n          let hit = component.queryHit(positionLeft, positionTop, width, height);\n          if (hit &&\n          // make sure the hit is within activeRange, meaning it's not a dead cell\n          rangeContainsRange(hit.dateProfile.activeRange, hit.dateSpan.range) && (\n          // Ensure the component we are querying for the hit is accessibly my the pointer\n          // Prevents obscured calendars (ex: under a modal dialog) from accepting hit\n          // https://github.com/fullcalendar/fullcalendar/issues/5026\n          this.disablePointCheck || offsetTracker.el.contains(offsetTracker.el.getRootNode().elementFromPoint(\n          // add-back origins to get coordinate relative to top-left of window viewport\n          positionLeft + originLeft - window.scrollX, positionTop + originTop - window.scrollY))) && (!bestHit || hit.layer > bestHit.layer)) {\n            hit.componentId = id;\n            hit.context = component.context;\n            // TODO: better way to re-orient rectangle\n            hit.rect.left += originLeft;\n            hit.rect.right += originLeft;\n            hit.rect.top += originTop;\n            hit.rect.bottom += originTop;\n            bestHit = hit;\n          }\n        }\n      }\n    }\n    return bestHit;\n  }\n}\nfunction isHitsEqual(hit0, hit1) {\n  if (!hit0 && !hit1) {\n    return true;\n  }\n  if (Boolean(hit0) !== Boolean(hit1)) {\n    return false;\n  }\n  return isDateSpansEqual(hit0.dateSpan, hit1.dateSpan);\n}\nfunction buildDatePointApiWithContext(dateSpan, context) {\n  let props = {};\n  for (let transform of context.pluginHooks.datePointTransforms) {\n    Object.assign(props, transform(dateSpan, context));\n  }\n  Object.assign(props, buildDatePointApi(dateSpan, context.dateEnv));\n  return props;\n}\nfunction buildDatePointApi(span, dateEnv) {\n  return {\n    date: dateEnv.toDate(span.range.start),\n    dateStr: dateEnv.formatIso(span.range.start, {\n      omitTime: span.allDay\n    }),\n    allDay: span.allDay\n  };\n}\n\n/*\nMonitors when the user clicks on a specific date/time of a component.\nA pointerdown+pointerup on the same \"hit\" constitutes a click.\n*/\nclass DateClicking extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.handlePointerDown = pev => {\n      let {\n        dragging\n      } = this;\n      let downEl = pev.origEvent.target;\n      // do this in pointerdown (not dragend) because DOM might be mutated by the time dragend is fired\n      dragging.setIgnoreMove(!this.component.isValidDateDownEl(downEl));\n    };\n    // won't even fire if moving was ignored\n    this.handleDragEnd = ev => {\n      let {\n        component\n      } = this;\n      let {\n        pointer\n      } = this.dragging;\n      if (!pointer.wasTouchScroll) {\n        let {\n          initialHit,\n          finalHit\n        } = this.hitDragging;\n        if (initialHit && finalHit && isHitsEqual(initialHit, finalHit)) {\n          let {\n            context\n          } = component;\n          let arg = Object.assign(Object.assign({}, buildDatePointApiWithContext(initialHit.dateSpan, context)), {\n            dayEl: initialHit.dayEl,\n            jsEvent: ev.origEvent,\n            view: context.viewApi || context.calendarApi.view\n          });\n          context.emitter.trigger('dateClick', arg);\n        }\n      }\n    };\n    // we DO want to watch pointer moves because otherwise finalHit won't get populated\n    this.dragging = new FeaturefulElementDragging(settings.el);\n    this.dragging.autoScroller.isEnabled = false;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nTracks when the user selects a portion of time of a component,\nconstituted by a drag over date cells, with a possible delay at the beginning of the drag.\n*/\nclass DateSelecting extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.dragSelection = null;\n    this.handlePointerDown = ev => {\n      let {\n        component,\n        dragging\n      } = this;\n      let {\n        options\n      } = component.context;\n      let canSelect = options.selectable && component.isValidDateDownEl(ev.origEvent.target);\n      // don't bother to watch expensive moves if component won't do selection\n      dragging.setIgnoreMove(!canSelect);\n      // if touch, require user to hold down\n      dragging.delay = ev.isTouch ? getComponentTouchDelay$1(component) : null;\n    };\n    this.handleDragStart = ev => {\n      this.component.context.calendarApi.unselect(ev); // unselect previous selections\n    };\n    this.handleHitUpdate = (hit, isFinal) => {\n      let {\n        context\n      } = this.component;\n      let dragSelection = null;\n      let isInvalid = false;\n      if (hit) {\n        let initialHit = this.hitDragging.initialHit;\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          dragSelection = joinHitsIntoSelection(initialHit, hit, context.pluginHooks.dateSelectionTransformers);\n        }\n        if (!dragSelection || !isDateSelectionValid(dragSelection, hit.dateProfile, context)) {\n          isInvalid = true;\n          dragSelection = null;\n        }\n      }\n      if (dragSelection) {\n        context.dispatch({\n          type: 'SELECT_DATES',\n          selection: dragSelection\n        });\n      } else if (!isFinal) {\n        // only unselect if moved away while dragging\n        context.dispatch({\n          type: 'UNSELECT_DATES'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        this.dragSelection = dragSelection; // only clear if moved away from all hits while dragging\n      }\n    };\n    this.handlePointerUp = pev => {\n      if (this.dragSelection) {\n        // selection is already rendered, so just need to report selection\n        triggerDateSelect(this.dragSelection, pev, this.component.context);\n        this.dragSelection = null;\n      }\n    };\n    let {\n      component\n    } = settings;\n    let {\n      options\n    } = component.context;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.touchScrollAllowed = false;\n    dragging.minDistance = options.selectMinDistance || 0;\n    dragging.autoScroller.isEnabled = options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nfunction getComponentTouchDelay$1(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.selectLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nfunction joinHitsIntoSelection(hit0, hit1, dateSelectionTransformers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let ms = [dateSpan0.range.start, dateSpan0.range.end, dateSpan1.range.start, dateSpan1.range.end];\n  ms.sort(compareNumbers);\n  let props = {};\n  for (let transformer of dateSelectionTransformers) {\n    let res = transformer(hit0, hit1);\n    if (res === false) {\n      return null;\n    }\n    if (res) {\n      Object.assign(props, res);\n    }\n  }\n  props.range = {\n    start: ms[0],\n    end: ms[3]\n  };\n  props.allDay = dateSpan0.allDay;\n  return props;\n}\nlet EventDragging = /*#__PURE__*/(() => {\n  class EventDragging extends Interaction {\n    constructor(settings) {\n      super(settings);\n      // internal state\n      this.subjectEl = null;\n      this.subjectSeg = null; // the seg being selected/dragged\n      this.isDragging = false;\n      this.eventRange = null;\n      this.relevantEvents = null; // the events being dragged\n      this.receivingContext = null;\n      this.validMutation = null;\n      this.mutatedRelevantEvents = null;\n      this.handlePointerDown = ev => {\n        let origTarget = ev.origEvent.target;\n        let {\n          component,\n          dragging\n        } = this;\n        let {\n          mirror\n        } = dragging;\n        let {\n          options\n        } = component.context;\n        let initialContext = component.context;\n        this.subjectEl = ev.subjectEl;\n        let subjectSeg = this.subjectSeg = getElSeg(ev.subjectEl);\n        let eventRange = this.eventRange = subjectSeg.eventRange;\n        let eventInstanceId = eventRange.instance.instanceId;\n        this.relevantEvents = getRelevantEvents(initialContext.getCurrentData().eventStore, eventInstanceId);\n        dragging.minDistance = ev.isTouch ? 0 : options.eventDragMinDistance;\n        dragging.delay =\n        // only do a touch delay if touch and this event hasn't been selected yet\n        ev.isTouch && eventInstanceId !== component.props.eventSelection ? getComponentTouchDelay(component) : null;\n        if (options.fixedMirrorParent) {\n          mirror.parentNode = options.fixedMirrorParent;\n        } else {\n          mirror.parentNode = elementClosest(origTarget, '.fc');\n        }\n        mirror.revertDuration = options.dragRevertDuration;\n        let isValid = component.isValidSegDownEl(origTarget) && !elementClosest(origTarget, '.fc-event-resizer'); // NOT on a resizer\n        dragging.setIgnoreMove(!isValid);\n        // disable dragging for elements that are resizable (ie, selectable)\n        // but are not draggable\n        this.isDragging = isValid && ev.subjectEl.classList.contains('fc-event-draggable');\n      };\n      this.handleDragStart = ev => {\n        let initialContext = this.component.context;\n        let eventRange = this.eventRange;\n        let eventInstanceId = eventRange.instance.instanceId;\n        if (ev.isTouch) {\n          // need to select a different event?\n          if (eventInstanceId !== this.component.props.eventSelection) {\n            initialContext.dispatch({\n              type: 'SELECT_EVENT',\n              eventInstanceId\n            });\n          }\n        } else {\n          // if now using mouse, but was previous touch interaction, clear selected event\n          initialContext.dispatch({\n            type: 'UNSELECT_EVENT'\n          });\n        }\n        if (this.isDragging) {\n          initialContext.calendarApi.unselect(ev); // unselect *date* selection\n          initialContext.emitter.trigger('eventDragStart', {\n            el: this.subjectEl,\n            event: new EventImpl(initialContext, eventRange.def, eventRange.instance),\n            jsEvent: ev.origEvent,\n            view: initialContext.viewApi\n          });\n        }\n      };\n      this.handleHitUpdate = (hit, isFinal) => {\n        if (!this.isDragging) {\n          return;\n        }\n        let relevantEvents = this.relevantEvents;\n        let initialHit = this.hitDragging.initialHit;\n        let initialContext = this.component.context;\n        // states based on new hit\n        let receivingContext = null;\n        let mutation = null;\n        let mutatedRelevantEvents = null;\n        let isInvalid = false;\n        let interaction = {\n          affectedEvents: relevantEvents,\n          mutatedEvents: createEmptyEventStore(),\n          isEvent: true\n        };\n        if (hit) {\n          receivingContext = hit.context;\n          let receivingOptions = receivingContext.options;\n          if (initialContext === receivingContext || receivingOptions.editable && receivingOptions.droppable) {\n            mutation = computeEventMutation(initialHit, hit, this.eventRange.instance.range.start, receivingContext.getCurrentData().pluginHooks.eventDragMutationMassagers);\n            if (mutation) {\n              mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, receivingContext.getCurrentData().eventUiBases, mutation, receivingContext);\n              interaction.mutatedEvents = mutatedRelevantEvents;\n              if (!isInteractionValid(interaction, hit.dateProfile, receivingContext)) {\n                isInvalid = true;\n                mutation = null;\n                mutatedRelevantEvents = null;\n                interaction.mutatedEvents = createEmptyEventStore();\n              }\n            }\n          } else {\n            receivingContext = null;\n          }\n        }\n        this.displayDrag(receivingContext, interaction);\n        if (!isInvalid) {\n          enableCursor();\n        } else {\n          disableCursor();\n        }\n        if (!isFinal) {\n          if (initialContext === receivingContext &&\n          // TODO: write test for this\n          isHitsEqual(initialHit, hit)) {\n            mutation = null;\n          }\n          this.dragging.setMirrorNeedsRevert(!mutation);\n          // render the mirror if no already-rendered mirror\n          // TODO: wish we could somehow wait for dispatch to guarantee render\n          this.dragging.setMirrorIsVisible(!hit || !this.subjectEl.getRootNode().querySelector('.fc-event-mirror'));\n          // assign states based on new hit\n          this.receivingContext = receivingContext;\n          this.validMutation = mutation;\n          this.mutatedRelevantEvents = mutatedRelevantEvents;\n        }\n      };\n      this.handlePointerUp = () => {\n        if (!this.isDragging) {\n          this.cleanup(); // because handleDragEnd won't fire\n        }\n      };\n      this.handleDragEnd = ev => {\n        if (this.isDragging) {\n          let initialContext = this.component.context;\n          let initialView = initialContext.viewApi;\n          let {\n            receivingContext,\n            validMutation\n          } = this;\n          let eventDef = this.eventRange.def;\n          let eventInstance = this.eventRange.instance;\n          let eventApi = new EventImpl(initialContext, eventDef, eventInstance);\n          let relevantEvents = this.relevantEvents;\n          let mutatedRelevantEvents = this.mutatedRelevantEvents;\n          let {\n            finalHit\n          } = this.hitDragging;\n          this.clearDrag(); // must happen after revert animation\n          initialContext.emitter.trigger('eventDragStop', {\n            el: this.subjectEl,\n            event: eventApi,\n            jsEvent: ev.origEvent,\n            view: initialView\n          });\n          if (validMutation) {\n            // dropped within same calendar\n            if (receivingContext === initialContext) {\n              let updatedEventApi = new EventImpl(initialContext, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n              initialContext.dispatch({\n                type: 'MERGE_EVENTS',\n                eventStore: mutatedRelevantEvents\n              });\n              let eventChangeArg = {\n                oldEvent: eventApi,\n                event: updatedEventApi,\n                relatedEvents: buildEventApis(mutatedRelevantEvents, initialContext, eventInstance),\n                revert() {\n                  initialContext.dispatch({\n                    type: 'MERGE_EVENTS',\n                    eventStore: relevantEvents // the pre-change data\n                  });\n                }\n              };\n              let transformed = {};\n              for (let transformer of initialContext.getCurrentData().pluginHooks.eventDropTransformers) {\n                Object.assign(transformed, transformer(validMutation, initialContext));\n              }\n              initialContext.emitter.trigger('eventDrop', Object.assign(Object.assign(Object.assign({}, eventChangeArg), transformed), {\n                el: ev.subjectEl,\n                delta: validMutation.datesDelta,\n                jsEvent: ev.origEvent,\n                view: initialView\n              }));\n              initialContext.emitter.trigger('eventChange', eventChangeArg);\n              // dropped in different calendar\n            } else if (receivingContext) {\n              let eventRemoveArg = {\n                event: eventApi,\n                relatedEvents: buildEventApis(relevantEvents, initialContext, eventInstance),\n                revert() {\n                  initialContext.dispatch({\n                    type: 'MERGE_EVENTS',\n                    eventStore: relevantEvents\n                  });\n                }\n              };\n              initialContext.emitter.trigger('eventLeave', Object.assign(Object.assign({}, eventRemoveArg), {\n                draggedEl: ev.subjectEl,\n                view: initialView\n              }));\n              initialContext.dispatch({\n                type: 'REMOVE_EVENTS',\n                eventStore: relevantEvents\n              });\n              initialContext.emitter.trigger('eventRemove', eventRemoveArg);\n              let addedEventDef = mutatedRelevantEvents.defs[eventDef.defId];\n              let addedEventInstance = mutatedRelevantEvents.instances[eventInstance.instanceId];\n              let addedEventApi = new EventImpl(receivingContext, addedEventDef, addedEventInstance);\n              receivingContext.dispatch({\n                type: 'MERGE_EVENTS',\n                eventStore: mutatedRelevantEvents\n              });\n              let eventAddArg = {\n                event: addedEventApi,\n                relatedEvents: buildEventApis(mutatedRelevantEvents, receivingContext, addedEventInstance),\n                revert() {\n                  receivingContext.dispatch({\n                    type: 'REMOVE_EVENTS',\n                    eventStore: mutatedRelevantEvents\n                  });\n                }\n              };\n              receivingContext.emitter.trigger('eventAdd', eventAddArg);\n              if (ev.isTouch) {\n                receivingContext.dispatch({\n                  type: 'SELECT_EVENT',\n                  eventInstanceId: eventInstance.instanceId\n                });\n              }\n              receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n                draggedEl: ev.subjectEl,\n                jsEvent: ev.origEvent,\n                view: finalHit.context.viewApi\n              }));\n              receivingContext.emitter.trigger('eventReceive', Object.assign(Object.assign({}, eventAddArg), {\n                draggedEl: ev.subjectEl,\n                view: finalHit.context.viewApi\n              }));\n            }\n          } else {\n            initialContext.emitter.trigger('_noEventDrop');\n          }\n        }\n        this.cleanup();\n      };\n      let {\n        component\n      } = this;\n      let {\n        options\n      } = component.context;\n      let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n      dragging.pointer.selector = EventDragging.SELECTOR;\n      dragging.touchScrollAllowed = false;\n      dragging.autoScroller.isEnabled = options.dragScroll;\n      let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsStore);\n      hitDragging.useSubjectCenter = settings.useEventCenter;\n      hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n      hitDragging.emitter.on('dragstart', this.handleDragStart);\n      hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n      hitDragging.emitter.on('pointerup', this.handlePointerUp);\n      hitDragging.emitter.on('dragend', this.handleDragEnd);\n    }\n    destroy() {\n      this.dragging.destroy();\n    }\n    // render a drag state on the next receivingCalendar\n    displayDrag(nextContext, state) {\n      let initialContext = this.component.context;\n      let prevContext = this.receivingContext;\n      // does the previous calendar need to be cleared?\n      if (prevContext && prevContext !== nextContext) {\n        // does the initial calendar need to be cleared?\n        // if so, don't clear all the way. we still need to to hide the affectedEvents\n        if (prevContext === initialContext) {\n          prevContext.dispatch({\n            type: 'SET_EVENT_DRAG',\n            state: {\n              affectedEvents: state.affectedEvents,\n              mutatedEvents: createEmptyEventStore(),\n              isEvent: true\n            }\n          });\n          // completely clear the old calendar if it wasn't the initial\n        } else {\n          prevContext.dispatch({\n            type: 'UNSET_EVENT_DRAG'\n          });\n        }\n      }\n      if (nextContext) {\n        nextContext.dispatch({\n          type: 'SET_EVENT_DRAG',\n          state\n        });\n      }\n    }\n    clearDrag() {\n      let initialCalendar = this.component.context;\n      let {\n        receivingContext\n      } = this;\n      if (receivingContext) {\n        receivingContext.dispatch({\n          type: 'UNSET_EVENT_DRAG'\n        });\n      }\n      // the initial calendar might have an dummy drag state from displayDrag\n      if (initialCalendar !== receivingContext) {\n        initialCalendar.dispatch({\n          type: 'UNSET_EVENT_DRAG'\n        });\n      }\n    }\n    cleanup() {\n      this.subjectSeg = null;\n      this.isDragging = false;\n      this.eventRange = null;\n      this.relevantEvents = null;\n      this.receivingContext = null;\n      this.validMutation = null;\n      this.mutatedRelevantEvents = null;\n    }\n  }\n  // TODO: test this in IE11\n  // QUESTION: why do we need it on the resizable???\n  EventDragging.SELECTOR = '.fc-event-draggable, .fc-event-resizable';\n  return EventDragging;\n})();\nfunction computeEventMutation(hit0, hit1, eventInstanceStart, massagers) {\n  let dateSpan0 = hit0.dateSpan;\n  let dateSpan1 = hit1.dateSpan;\n  let date0 = dateSpan0.range.start;\n  let date1 = dateSpan1.range.start;\n  let standardProps = {};\n  if (dateSpan0.allDay !== dateSpan1.allDay) {\n    standardProps.allDay = dateSpan1.allDay;\n    standardProps.hasEnd = hit1.context.options.allDayMaintainDuration;\n    if (dateSpan1.allDay) {\n      // means date1 is already start-of-day,\n      // but date0 needs to be converted\n      date0 = startOfDay(eventInstanceStart);\n    } else {\n      // Moving from allDate->timed\n      // Doesn't matter where on the event the drag began, mutate the event's start-date to date1\n      date0 = eventInstanceStart;\n    }\n  }\n  let delta = diffDates(date0, date1, hit0.context.dateEnv, hit0.componentId === hit1.componentId ? hit0.largeUnit : null);\n  if (delta.milliseconds) {\n    // has hours/minutes/seconds\n    standardProps.allDay = false;\n  }\n  let mutation = {\n    datesDelta: delta,\n    standardProps\n  };\n  for (let massager of massagers) {\n    massager(mutation, hit0, hit1);\n  }\n  return mutation;\n}\nfunction getComponentTouchDelay(component) {\n  let {\n    options\n  } = component.context;\n  let delay = options.eventLongPressDelay;\n  if (delay == null) {\n    delay = options.longPressDelay;\n  }\n  return delay;\n}\nclass EventResizing extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // internal state\n    this.draggingSegEl = null;\n    this.draggingSeg = null; // TODO: rename to resizingSeg? subjectSeg?\n    this.eventRange = null;\n    this.relevantEvents = null;\n    this.validMutation = null;\n    this.mutatedRelevantEvents = null;\n    this.handlePointerDown = ev => {\n      let {\n        component\n      } = this;\n      let segEl = this.querySegEl(ev);\n      let seg = getElSeg(segEl);\n      let eventRange = this.eventRange = seg.eventRange;\n      this.dragging.minDistance = component.context.options.eventDragMinDistance;\n      // if touch, need to be working with a selected event\n      this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(ev.origEvent.target) || ev.isTouch && this.component.props.eventSelection !== eventRange.instance.instanceId);\n    };\n    this.handleDragStart = ev => {\n      let {\n        context\n      } = this.component;\n      let eventRange = this.eventRange;\n      this.relevantEvents = getRelevantEvents(context.getCurrentData().eventStore, this.eventRange.instance.instanceId);\n      let segEl = this.querySegEl(ev);\n      this.draggingSegEl = segEl;\n      this.draggingSeg = getElSeg(segEl);\n      context.calendarApi.unselect();\n      context.emitter.trigger('eventResizeStart', {\n        el: segEl,\n        event: new EventImpl(context, eventRange.def, eventRange.instance),\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        context\n      } = this.component;\n      let relevantEvents = this.relevantEvents;\n      let initialHit = this.hitDragging.initialHit;\n      let eventInstance = this.eventRange.instance;\n      let mutation = null;\n      let mutatedRelevantEvents = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: relevantEvents,\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: true\n      };\n      if (hit) {\n        let disallowed = hit.componentId === initialHit.componentId && this.isHitComboAllowed && !this.isHitComboAllowed(initialHit, hit);\n        if (!disallowed) {\n          mutation = computeMutation(initialHit, hit, ev.subjectEl.classList.contains('fc-event-resizer-start'), eventInstance.range);\n        }\n      }\n      if (mutation) {\n        mutatedRelevantEvents = applyMutationToEventStore(relevantEvents, context.getCurrentData().eventUiBases, mutation, context);\n        interaction.mutatedEvents = mutatedRelevantEvents;\n        if (!isInteractionValid(interaction, hit.dateProfile, context)) {\n          isInvalid = true;\n          mutation = null;\n          mutatedRelevantEvents = null;\n          interaction.mutatedEvents = null;\n        }\n      }\n      if (mutatedRelevantEvents) {\n        context.dispatch({\n          type: 'SET_EVENT_RESIZE',\n          state: interaction\n        });\n      } else {\n        context.dispatch({\n          type: 'UNSET_EVENT_RESIZE'\n        });\n      }\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        if (mutation && isHitsEqual(initialHit, hit)) {\n          mutation = null;\n        }\n        this.validMutation = mutation;\n        this.mutatedRelevantEvents = mutatedRelevantEvents;\n      }\n    };\n    this.handleDragEnd = ev => {\n      let {\n        context\n      } = this.component;\n      let eventDef = this.eventRange.def;\n      let eventInstance = this.eventRange.instance;\n      let eventApi = new EventImpl(context, eventDef, eventInstance);\n      let relevantEvents = this.relevantEvents;\n      let mutatedRelevantEvents = this.mutatedRelevantEvents;\n      context.emitter.trigger('eventResizeStop', {\n        el: this.draggingSegEl,\n        event: eventApi,\n        jsEvent: ev.origEvent,\n        view: context.viewApi\n      });\n      if (this.validMutation) {\n        let updatedEventApi = new EventImpl(context, mutatedRelevantEvents.defs[eventDef.defId], eventInstance ? mutatedRelevantEvents.instances[eventInstance.instanceId] : null);\n        context.dispatch({\n          type: 'MERGE_EVENTS',\n          eventStore: mutatedRelevantEvents\n        });\n        let eventChangeArg = {\n          oldEvent: eventApi,\n          event: updatedEventApi,\n          relatedEvents: buildEventApis(mutatedRelevantEvents, context, eventInstance),\n          revert() {\n            context.dispatch({\n              type: 'MERGE_EVENTS',\n              eventStore: relevantEvents // the pre-change events\n            });\n          }\n        };\n        context.emitter.trigger('eventResize', Object.assign(Object.assign({}, eventChangeArg), {\n          el: this.draggingSegEl,\n          startDelta: this.validMutation.startDelta || createDuration(0),\n          endDelta: this.validMutation.endDelta || createDuration(0),\n          jsEvent: ev.origEvent,\n          view: context.viewApi\n        }));\n        context.emitter.trigger('eventChange', eventChangeArg);\n      } else {\n        context.emitter.trigger('_noEventResize');\n      }\n      // reset all internal state\n      this.draggingSeg = null;\n      this.relevantEvents = null;\n      this.validMutation = null;\n      // okay to keep eventInstance around. useful to set it in handlePointerDown\n    };\n    let {\n      component\n    } = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(settings.el);\n    dragging.pointer.selector = '.fc-event-resizer';\n    dragging.touchScrollAllowed = false;\n    dragging.autoScroller.isEnabled = component.context.options.dragScroll;\n    let hitDragging = this.hitDragging = new HitDragging(this.dragging, interactionSettingsToStore(settings));\n    hitDragging.emitter.on('pointerdown', this.handlePointerDown);\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n  querySegEl(ev) {\n    return elementClosest(ev.subjectEl, '.fc-event');\n  }\n}\nfunction computeMutation(hit0, hit1, isFromStart, instanceRange) {\n  let dateEnv = hit0.context.dateEnv;\n  let date0 = hit0.dateSpan.range.start;\n  let date1 = hit1.dateSpan.range.start;\n  let delta = diffDates(date0, date1, dateEnv, hit0.largeUnit);\n  if (isFromStart) {\n    if (dateEnv.add(instanceRange.start, delta) < instanceRange.end) {\n      return {\n        startDelta: delta\n      };\n    }\n  } else if (dateEnv.add(instanceRange.end, delta) > instanceRange.start) {\n    return {\n      endDelta: delta\n    };\n  }\n  return null;\n}\nclass UnselectAuto {\n  constructor(context) {\n    this.context = context;\n    this.isRecentPointerDateSelect = false; // wish we could use a selector to detect date selection, but uses hit system\n    this.matchesCancel = false;\n    this.matchesEvent = false;\n    this.onSelect = selectInfo => {\n      if (selectInfo.jsEvent) {\n        this.isRecentPointerDateSelect = true;\n      }\n    };\n    this.onDocumentPointerDown = pev => {\n      let unselectCancel = this.context.options.unselectCancel;\n      let downEl = getEventTargetViaRoot(pev.origEvent);\n      this.matchesCancel = !!elementClosest(downEl, unselectCancel);\n      this.matchesEvent = !!elementClosest(downEl, EventDragging.SELECTOR); // interaction started on an event?\n    };\n    this.onDocumentPointerUp = pev => {\n      let {\n        context\n      } = this;\n      let {\n        documentPointer\n      } = this;\n      let calendarState = context.getCurrentData();\n      // touch-scrolling should never unfocus any type of selection\n      if (!documentPointer.wasTouchScroll) {\n        if (calendarState.dateSelection &&\n        // an existing date selection?\n        !this.isRecentPointerDateSelect // a new pointer-initiated date selection since last onDocumentPointerUp?\n        ) {\n          let unselectAuto = context.options.unselectAuto;\n          if (unselectAuto && (!unselectAuto || !this.matchesCancel)) {\n            context.calendarApi.unselect(pev);\n          }\n        }\n        if (calendarState.eventSelection &&\n        // an existing event selected?\n        !this.matchesEvent // interaction DIDN'T start on an event\n        ) {\n          context.dispatch({\n            type: 'UNSELECT_EVENT'\n          });\n        }\n      }\n      this.isRecentPointerDateSelect = false;\n    };\n    let documentPointer = this.documentPointer = new PointerDragging(document);\n    documentPointer.shouldIgnoreMove = true;\n    documentPointer.shouldWatchScroll = false;\n    documentPointer.emitter.on('pointerdown', this.onDocumentPointerDown);\n    documentPointer.emitter.on('pointerup', this.onDocumentPointerUp);\n    /*\n    TODO: better way to know about whether there was a selection with the pointer\n    */\n    context.emitter.on('select', this.onSelect);\n  }\n  destroy() {\n    this.context.emitter.off('select', this.onSelect);\n    this.documentPointer.destroy();\n  }\n}\nconst OPTION_REFINERS = {\n  fixedMirrorParent: identity\n};\nconst LISTENER_REFINERS = {\n  dateClick: identity,\n  eventDragStart: identity,\n  eventDragStop: identity,\n  eventDrop: identity,\n  eventResizeStart: identity,\n  eventResizeStop: identity,\n  eventResize: identity,\n  drop: identity,\n  eventReceive: identity,\n  eventLeave: identity\n};\n\n/*\nGiven an already instantiated draggable object for one-or-more elements,\nInterprets any dragging as an attempt to drag an events that lives outside\nof a calendar onto a calendar.\n*/\nclass ExternalElementDragging {\n  constructor(dragging, suppliedDragMeta) {\n    this.receivingContext = null;\n    this.droppableEvent = null; // will exist for all drags, even if create:false\n    this.suppliedDragMeta = null;\n    this.dragMeta = null;\n    this.handleDragStart = ev => {\n      this.dragMeta = this.buildDragMeta(ev.subjectEl);\n    };\n    this.handleHitUpdate = (hit, isFinal, ev) => {\n      let {\n        dragging\n      } = this.hitDragging;\n      let receivingContext = null;\n      let droppableEvent = null;\n      let isInvalid = false;\n      let interaction = {\n        affectedEvents: createEmptyEventStore(),\n        mutatedEvents: createEmptyEventStore(),\n        isEvent: this.dragMeta.create\n      };\n      if (hit) {\n        receivingContext = hit.context;\n        if (this.canDropElOnCalendar(ev.subjectEl, receivingContext)) {\n          droppableEvent = computeEventForDateSpan(hit.dateSpan, this.dragMeta, receivingContext);\n          interaction.mutatedEvents = eventTupleToStore(droppableEvent);\n          isInvalid = !isInteractionValid(interaction, hit.dateProfile, receivingContext);\n          if (isInvalid) {\n            interaction.mutatedEvents = createEmptyEventStore();\n            droppableEvent = null;\n          }\n        }\n      }\n      this.displayDrag(receivingContext, interaction);\n      // show mirror if no already-rendered mirror element OR if we are shutting down the mirror (?)\n      // TODO: wish we could somehow wait for dispatch to guarantee render\n      dragging.setMirrorIsVisible(isFinal || !droppableEvent || !document.querySelector('.fc-event-mirror'));\n      if (!isInvalid) {\n        enableCursor();\n      } else {\n        disableCursor();\n      }\n      if (!isFinal) {\n        dragging.setMirrorNeedsRevert(!droppableEvent);\n        this.receivingContext = receivingContext;\n        this.droppableEvent = droppableEvent;\n      }\n    };\n    this.handleDragEnd = pev => {\n      let {\n        receivingContext,\n        droppableEvent\n      } = this;\n      this.clearDrag();\n      if (receivingContext && droppableEvent) {\n        let finalHit = this.hitDragging.finalHit;\n        let finalView = finalHit.context.viewApi;\n        let dragMeta = this.dragMeta;\n        receivingContext.emitter.trigger('drop', Object.assign(Object.assign({}, buildDatePointApiWithContext(finalHit.dateSpan, receivingContext)), {\n          draggedEl: pev.subjectEl,\n          jsEvent: pev.origEvent,\n          view: finalView\n        }));\n        if (dragMeta.create) {\n          let addingEvents = eventTupleToStore(droppableEvent);\n          receivingContext.dispatch({\n            type: 'MERGE_EVENTS',\n            eventStore: addingEvents\n          });\n          if (pev.isTouch) {\n            receivingContext.dispatch({\n              type: 'SELECT_EVENT',\n              eventInstanceId: droppableEvent.instance.instanceId\n            });\n          }\n          // signal that an external event landed\n          receivingContext.emitter.trigger('eventReceive', {\n            event: new EventImpl(receivingContext, droppableEvent.def, droppableEvent.instance),\n            relatedEvents: [],\n            revert() {\n              receivingContext.dispatch({\n                type: 'REMOVE_EVENTS',\n                eventStore: addingEvents\n              });\n            },\n            draggedEl: pev.subjectEl,\n            view: finalView\n          });\n        }\n      }\n      this.receivingContext = null;\n      this.droppableEvent = null;\n    };\n    let hitDragging = this.hitDragging = new HitDragging(dragging, interactionSettingsStore);\n    hitDragging.requireInitial = false; // will start outside of a component\n    hitDragging.emitter.on('dragstart', this.handleDragStart);\n    hitDragging.emitter.on('hitupdate', this.handleHitUpdate);\n    hitDragging.emitter.on('dragend', this.handleDragEnd);\n    this.suppliedDragMeta = suppliedDragMeta;\n  }\n  buildDragMeta(subjectEl) {\n    if (typeof this.suppliedDragMeta === 'object') {\n      return parseDragMeta(this.suppliedDragMeta);\n    }\n    if (typeof this.suppliedDragMeta === 'function') {\n      return parseDragMeta(this.suppliedDragMeta(subjectEl));\n    }\n    return getDragMetaFromEl(subjectEl);\n  }\n  displayDrag(nextContext, state) {\n    let prevContext = this.receivingContext;\n    if (prevContext && prevContext !== nextContext) {\n      prevContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n    if (nextContext) {\n      nextContext.dispatch({\n        type: 'SET_EVENT_DRAG',\n        state\n      });\n    }\n  }\n  clearDrag() {\n    if (this.receivingContext) {\n      this.receivingContext.dispatch({\n        type: 'UNSET_EVENT_DRAG'\n      });\n    }\n  }\n  canDropElOnCalendar(el, receivingContext) {\n    let dropAccept = receivingContext.options.dropAccept;\n    if (typeof dropAccept === 'function') {\n      return dropAccept.call(receivingContext.calendarApi, el);\n    }\n    if (typeof dropAccept === 'string' && dropAccept) {\n      return Boolean(elementMatches(el, dropAccept));\n    }\n    return true;\n  }\n}\n// Utils for computing event store from the DragMeta\n// ----------------------------------------------------------------------------------------------------\nfunction computeEventForDateSpan(dateSpan, dragMeta, context) {\n  let defProps = Object.assign({}, dragMeta.leftoverProps);\n  for (let transform of context.pluginHooks.externalDefTransforms) {\n    Object.assign(defProps, transform(dateSpan, dragMeta));\n  }\n  let {\n    refined,\n    extra\n  } = refineEventDef(defProps, context);\n  let def = parseEventDef(refined, extra, dragMeta.sourceId, dateSpan.allDay, context.options.forceEventDuration || Boolean(dragMeta.duration),\n  // hasEnd\n  context);\n  let start = dateSpan.range.start;\n  // only rely on time info if drop zone is all-day,\n  // otherwise, we already know the time\n  if (dateSpan.allDay && dragMeta.startTime) {\n    start = context.dateEnv.add(start, dragMeta.startTime);\n  }\n  let end = dragMeta.duration ? context.dateEnv.add(start, dragMeta.duration) : getDefaultEventEnd(dateSpan.allDay, start, context);\n  let instance = createEventInstance(def.defId, {\n    start,\n    end\n  });\n  return {\n    def,\n    instance\n  };\n}\n// Utils for extracting data from element\n// ----------------------------------------------------------------------------------------------------\nfunction getDragMetaFromEl(el) {\n  let str = getEmbeddedElData(el, 'event');\n  let obj = str ? JSON.parse(str) : {\n    create: false\n  }; // if no embedded data, assume no event creation\n  return parseDragMeta(obj);\n}\nconfig.dataAttrPrefix = '';\nfunction getEmbeddedElData(el, name) {\n  let prefix = config.dataAttrPrefix;\n  let prefixedName = (prefix ? prefix + '-' : '') + name;\n  return el.getAttribute('data-' + prefixedName) || '';\n}\n\n/*\nMakes an element (that is *external* to any calendar) draggable.\nCan pass in data that determines how an event will be created when dropped onto a calendar.\nLeverages FullCalendar's internal drag-n-drop functionality WITHOUT a third-party drag system.\n*/\nclass ExternalDraggable {\n  constructor(el, settings = {}) {\n    this.handlePointerDown = ev => {\n      let {\n        dragging\n      } = this;\n      let {\n        minDistance,\n        longPressDelay\n      } = this.settings;\n      dragging.minDistance = minDistance != null ? minDistance : ev.isTouch ? 0 : BASE_OPTION_DEFAULTS.eventDragMinDistance;\n      dragging.delay = ev.isTouch ?\n      // TODO: eventually read eventLongPressDelay instead vvv\n      longPressDelay != null ? longPressDelay : BASE_OPTION_DEFAULTS.longPressDelay : 0;\n    };\n    this.handleDragStart = ev => {\n      if (ev.isTouch && this.dragging.delay && ev.subjectEl.classList.contains('fc-event')) {\n        this.dragging.mirror.getMirrorEl().classList.add('fc-event-selected');\n      }\n    };\n    this.settings = settings;\n    let dragging = this.dragging = new FeaturefulElementDragging(el);\n    dragging.touchScrollAllowed = false;\n    if (settings.itemSelector != null) {\n      dragging.pointer.selector = settings.itemSelector;\n    }\n    if (settings.appendTo != null) {\n      dragging.mirror.parentNode = settings.appendTo; // TODO: write tests\n    }\n    dragging.emitter.on('pointerdown', this.handlePointerDown);\n    dragging.emitter.on('dragstart', this.handleDragStart);\n    new ExternalElementDragging(dragging, settings.eventData); // eslint-disable-line no-new\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\n\n/*\nDetects when a *THIRD-PARTY* drag-n-drop system interacts with elements.\nThe third-party system is responsible for drawing the visuals effects of the drag.\nThis class simply monitors for pointer movements and fires events.\nIt also has the ability to hide the moving element (the \"mirror\") during the drag.\n*/\nclass InferredElementDragging extends ElementDragging {\n  constructor(containerEl) {\n    super(containerEl);\n    this.shouldIgnoreMove = false;\n    this.mirrorSelector = '';\n    this.currentMirrorEl = null;\n    this.handlePointerDown = ev => {\n      this.emitter.trigger('pointerdown', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragstart right away. does not support delay or min-distance\n        this.emitter.trigger('dragstart', ev);\n      }\n    };\n    this.handlePointerMove = ev => {\n      if (!this.shouldIgnoreMove) {\n        this.emitter.trigger('dragmove', ev);\n      }\n    };\n    this.handlePointerUp = ev => {\n      this.emitter.trigger('pointerup', ev);\n      if (!this.shouldIgnoreMove) {\n        // fire dragend right away. does not support a revert animation\n        this.emitter.trigger('dragend', ev);\n      }\n    };\n    let pointer = this.pointer = new PointerDragging(containerEl);\n    pointer.emitter.on('pointerdown', this.handlePointerDown);\n    pointer.emitter.on('pointermove', this.handlePointerMove);\n    pointer.emitter.on('pointerup', this.handlePointerUp);\n  }\n  destroy() {\n    this.pointer.destroy();\n  }\n  setIgnoreMove(bool) {\n    this.shouldIgnoreMove = bool;\n  }\n  setMirrorIsVisible(bool) {\n    if (bool) {\n      // restore a previously hidden element.\n      // use the reference in case the selector class has already been removed.\n      if (this.currentMirrorEl) {\n        this.currentMirrorEl.style.visibility = '';\n        this.currentMirrorEl = null;\n      }\n    } else {\n      let mirrorEl = this.mirrorSelector\n      // TODO: somehow query FullCalendars WITHIN shadow-roots\n      ? document.querySelector(this.mirrorSelector) : null;\n      if (mirrorEl) {\n        this.currentMirrorEl = mirrorEl;\n        mirrorEl.style.visibility = 'hidden';\n      }\n    }\n  }\n}\n\n/*\nBridges third-party drag-n-drop systems with FullCalendar.\nMust be instantiated and destroyed by caller.\n*/\nclass ThirdPartyDraggable {\n  constructor(containerOrSettings, settings) {\n    let containerEl = document;\n    if (\n    // wish we could just test instanceof EventTarget, but doesn't work in IE11\n    containerOrSettings === document || containerOrSettings instanceof Element) {\n      containerEl = containerOrSettings;\n      settings = settings || {};\n    } else {\n      settings = containerOrSettings || {};\n    }\n    let dragging = this.dragging = new InferredElementDragging(containerEl);\n    if (typeof settings.itemSelector === 'string') {\n      dragging.pointer.selector = settings.itemSelector;\n    } else if (containerEl === document) {\n      dragging.pointer.selector = '[data-event]';\n    }\n    if (typeof settings.mirrorSelector === 'string') {\n      dragging.mirrorSelector = settings.mirrorSelector;\n    }\n    let externalDragging = new ExternalElementDragging(dragging, settings.eventData);\n    // The hit-detection system requires that the dnd-mirror-element be pointer-events:none,\n    // but this can't be guaranteed for third-party draggables, so disable\n    externalDragging.hitDragging.disablePointCheck = true;\n  }\n  destroy() {\n    this.dragging.destroy();\n  }\n}\nvar index = createPlugin({\n  name: '@fullcalendar/interaction',\n  componentInteractions: [DateClicking, DateSelecting, EventDragging, EventResizing],\n  calendarInteractions: [UnselectAuto],\n  elementDraggingImpl: FeaturefulElementDragging,\n  optionRefiners: OPTION_REFINERS,\n  listenerRefiners: LISTENER_REFINERS\n});\nexport { ExternalDraggable as Draggable, ThirdPartyDraggable, index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}