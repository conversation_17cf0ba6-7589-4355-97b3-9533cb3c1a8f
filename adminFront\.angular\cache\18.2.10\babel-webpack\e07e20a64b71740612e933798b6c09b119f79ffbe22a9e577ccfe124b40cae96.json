{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  return _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n}();\nexport { WebSocketClient as default };", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "prototype", "t", "_toPrimitive", "r", "e", "Symbol", "toPrimitive", "call", "String", "Number", "log", "WebSocketClient", "url", "client", "WebSocket", "onerror", "error", "value", "onOpen", "f", "onopen", "onClose", "onclose", "onMessage", "onmessage", "data", "default"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/webpack-dev-server/client/clients/WebSocketClient.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  return _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n}();\nexport { WebSocketClient as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACkB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASa,cAAcA,CAACM,CAAC,EAAE;EAAE,IAAId,CAAC,GAAGe,YAAY,CAACD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOd,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASe,YAAYA,CAACD,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIG,CAAC,GAAGH,CAAC,CAACI,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKF,CAAC,EAAE;IAAE,IAAIjB,CAAC,GAAGiB,CAAC,CAACG,IAAI,CAACN,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOhB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKoB,CAAC,GAAGK,MAAM,GAAGC,MAAM,EAAER,CAAC,CAAC;AAAE;AACvT,SAASS,GAAG,QAAQ,iBAAiB;AACrC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C;AACF;AACA;EACE,SAASA,eAAeA,CAACC,GAAG,EAAE;IAC5BhC,eAAe,CAAC,IAAI,EAAE+B,eAAe,CAAC;IACtC,IAAI,CAACE,MAAM,GAAG,IAAIC,SAAS,CAACF,GAAG,CAAC;IAChC,IAAI,CAACC,MAAM,CAACE,OAAO,GAAG,UAAUC,KAAK,EAAE;MACrCN,GAAG,CAACM,KAAK,CAACA,KAAK,CAAC;IAClB,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOnB,YAAY,CAACc,eAAe,EAAE,CAAC;IACpCf,GAAG,EAAE,QAAQ;IACbqB,KAAK,EAAE,SAASC,MAAMA,CAACC,CAAC,EAAE;MACxB,IAAI,CAACN,MAAM,CAACO,MAAM,GAAGD,CAAC;IACxB;;IAEA;AACJ;AACA;EACE,CAAC,EAAE;IACDvB,GAAG,EAAE,SAAS;IACdqB,KAAK,EAAE,SAASI,OAAOA,CAACF,CAAC,EAAE;MACzB,IAAI,CAACN,MAAM,CAACS,OAAO,GAAGH,CAAC;IACzB;;IAEA;IACA;AACJ;AACA;EACE,CAAC,EAAE;IACDvB,GAAG,EAAE,WAAW;IAChBqB,KAAK,EAAE,SAASM,SAASA,CAACJ,CAAC,EAAE;MAC3B,IAAI,CAACN,MAAM,CAACW,SAAS,GAAG,UAAUpB,CAAC,EAAE;QACnCe,CAAC,CAACf,CAAC,CAACqB,IAAI,CAAC;MACX,CAAC;IACH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,SAASd,eAAe,IAAIe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}