{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class EnumHelper {\n  getEnumOptions(enumObj) {\n    return Object.keys(enumObj).filter(key => !isNaN(Number(enumObj[key]))) // Lọc chỉ số (value) của enum\n    .map(key => ({\n      label: key,\n      value: enumObj[key]\n    }));\n  }\n  static {\n    this.ɵfac = function EnumHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EnumHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EnumHelper,\n      factory: EnumHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "getEnumOptions", "enumObj", "Object", "keys", "filter", "key", "isNaN", "Number", "map", "label", "value", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\helper\\enumHelper.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class EnumHelper {\r\n  getEnumOptions(enumObj: any): { label: string; value: number }[] {\r\n    return Object.keys(enumObj)\r\n      .filter((key) => !isNaN(Number(enumObj[key]))) // Lọc chỉ số (value) của enum\r\n      .map((key) => ({\r\n        label: key,\r\n        value: enumObj[key],\r\n      }));\r\n  }\r\n}\r\n"], "mappings": ";AAGA,OAAM,MAAOA,UAAU;EACrBC,cAAcA,CAACC,OAAY;IACzB,OAAOC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CACxBG,MAAM,CAAEC,GAAG,IAAK,CAACC,KAAK,CAACC,MAAM,CAACN,OAAO,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,CAC9CG,GAAG,CAAEH,GAAG,KAAM;MACbI,KAAK,EAAEJ,GAAG;MACVK,KAAK,EAAET,OAAO,CAACI,GAAG;KACnB,CAAC,CAAC;EACP;;;uCARWN,UAAU;IAAA;EAAA;;;aAAVA,UAAU;MAAAY,OAAA,EAAVZ,UAAU,CAAAa,IAAA;MAAAC,UAAA,EADG;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}