{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { SolarData } from '../data/solar';\nimport * as i0 from \"@angular/core\";\nexport let SolarService = /*#__PURE__*/(() => {\n  class SolarService extends SolarData {\n    constructor() {\n      super(...arguments);\n      this.value = 42;\n    }\n    getSolarData() {\n      return observableOf(this.value);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵSolarService_BaseFactory;\n        return function SolarService_Factory(__ngFactoryType__) {\n          return (ɵSolarService_BaseFactory || (ɵSolarService_BaseFactory = i0.ɵɵgetInheritedFactory(SolarService)))(__ngFactoryType__ || SolarService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SolarService,\n        factory: SolarService.ɵfac\n      });\n    }\n  }\n  return SolarService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}