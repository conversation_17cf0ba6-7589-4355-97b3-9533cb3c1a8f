{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function getUTCWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n\n  // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, options);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, options);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "startOfUTCWeek", "toInteger", "getDefaultOptions", "getUTCWeekYear", "dirtyDate", "options", "_ref", "_ref2", "_ref3", "_options$firstWeekCon", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "date", "year", "getUTCFullYear", "defaultOptions", "firstWeekContainsDate", "locale", "RangeError", "firstWeekOfNextYear", "Date", "setUTCFullYear", "setUTCHours", "startOfNextYear", "firstWeekOfThisYear", "startOfThisYear", "getTime"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/_lib/getUTCWeekYear/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport toInteger from \"../toInteger/index.js\";\nimport { getDefaultOptions } from \"../defaultOptions/index.js\";\nexport default function getUTCWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getUTCFullYear();\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n\n  // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, options);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, options);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACzD,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpId,YAAY,CAAC,CAAC,EAAEe,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGjB,MAAM,CAACM,SAAS,CAAC;EAC5B,IAAIY,IAAI,GAAGD,IAAI,CAACE,cAAc,CAAC,CAAC;EAChC,IAAIC,cAAc,GAAGhB,iBAAiB,CAAC,CAAC;EACxC,IAAIiB,qBAAqB,GAAGlB,SAAS,CAAC,CAACK,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,qBAAqB,MAAM,IAAI,IAAIV,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACe,MAAM,MAAM,IAAI,IAAIV,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACQ,qBAAqB,MAAM,IAAI,IAAIX,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGU,cAAc,CAACC,qBAAqB,MAAM,IAAI,IAAIZ,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGM,cAAc,CAACE,MAAM,MAAM,IAAI,IAAIR,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACM,qBAAqB,MAAM,IAAI,IAAIb,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;;EAEl7B;EACA,IAAI,EAAEa,qBAAqB,IAAI,CAAC,IAAIA,qBAAqB,IAAI,CAAC,CAAC,EAAE;IAC/D,MAAM,IAAIE,UAAU,CAAC,2DAA2D,CAAC;EACnF;EACA,IAAIC,mBAAmB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACrCD,mBAAmB,CAACE,cAAc,CAACR,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEG,qBAAqB,CAAC;EACtEG,mBAAmB,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,IAAIC,eAAe,GAAG1B,cAAc,CAACsB,mBAAmB,EAAEjB,OAAO,CAAC;EAClE,IAAIsB,mBAAmB,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAAC;EACrCI,mBAAmB,CAACH,cAAc,CAACR,IAAI,EAAE,CAAC,EAAEG,qBAAqB,CAAC;EAClEQ,mBAAmB,CAACF,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,IAAIG,eAAe,GAAG5B,cAAc,CAAC2B,mBAAmB,EAAEtB,OAAO,CAAC;EAClE,IAAIU,IAAI,CAACc,OAAO,CAAC,CAAC,IAAIH,eAAe,CAACG,OAAO,CAAC,CAAC,EAAE;IAC/C,OAAOb,IAAI,GAAG,CAAC;EACjB,CAAC,MAAM,IAAID,IAAI,CAACc,OAAO,CAAC,CAAC,IAAID,eAAe,CAACC,OAAO,CAAC,CAAC,EAAE;IACtD,OAAOb,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,GAAG,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}