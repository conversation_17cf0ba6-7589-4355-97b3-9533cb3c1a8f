{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Gujarati [gu]\n//! author : <PERSON><PERSON><PERSON> Thank<PERSON> : https://github.com/Kaushik1987\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '૧',\n      2: '૨',\n      3: '૩',\n      4: '૪',\n      5: '૫',\n      6: '૬',\n      7: '૭',\n      8: '૮',\n      9: '૯',\n      0: '૦'\n    },\n    numberMap = {\n      '૧': '1',\n      '૨': '2',\n      '૩': '3',\n      '૪': '4',\n      '૫': '5',\n      '૬': '6',\n      '૭': '7',\n      '૮': '8',\n      '૯': '9',\n      '૦': '0'\n    };\n  var gu = moment.defineLocale('gu', {\n    months: 'જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર'.split('_'),\n    monthsShort: 'જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર'.split('_'),\n    weekdaysShort: 'રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ'.split('_'),\n    weekdaysMin: 'ર_સો_મં_બુ_ગુ_શુ_શ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm વાગ્યે',\n      LTS: 'A h:mm:ss વાગ્યે',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm વાગ્યે',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm વાગ્યે'\n    },\n    calendar: {\n      sameDay: '[આજ] LT',\n      nextDay: '[કાલે] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[ગઇકાલે] LT',\n      lastWeek: '[પાછલા] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s મા',\n      past: '%s પહેલા',\n      s: 'અમુક પળો',\n      ss: '%d સેકંડ',\n      m: 'એક મિનિટ',\n      mm: '%d મિનિટ',\n      h: 'એક કલાક',\n      hh: '%d કલાક',\n      d: 'એક દિવસ',\n      dd: '%d દિવસ',\n      M: 'એક મહિનો',\n      MM: '%d મહિનો',\n      y: 'એક વર્ષ',\n      yy: '%d વર્ષ'\n    },\n    preparse: function (string) {\n      return string.replace(/[૧૨૩૪૫૬૭૮૯૦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // Gujarati notation for meridiems are quite fuzzy in practice. While there exists\n    // a rigid notion of a 'Pahar' it is not used as rigidly in modern Gujarati.\n    meridiemParse: /રાત|બપોર|સવાર|સાંજ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'રાત') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'સવાર') {\n        return hour;\n      } else if (meridiem === 'બપોર') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'સાંજ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'રાત';\n      } else if (hour < 10) {\n        return 'સવાર';\n      } else if (hour < 17) {\n        return 'બપોર';\n      } else if (hour < 20) {\n        return 'સાંજ';\n      } else {\n        return 'રાત';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return gu;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "gu", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "meridiemParse", "meridiemHour", "hour", "meridiem", "minute", "isLower", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/gu.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Gujarati [gu]\n//! author : <PERSON><PERSON><PERSON> Thank<PERSON> : https://github.com/Kaushik1987\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '૧',\n            2: '૨',\n            3: '૩',\n            4: '૪',\n            5: '૫',\n            6: '૬',\n            7: '૭',\n            8: '૮',\n            9: '૯',\n            0: '૦',\n        },\n        numberMap = {\n            '૧': '1',\n            '૨': '2',\n            '૩': '3',\n            '૪': '4',\n            '૫': '5',\n            '૬': '6',\n            '૭': '7',\n            '૮': '8',\n            '૯': '9',\n            '૦': '0',\n        };\n\n    var gu = moment.defineLocale('gu', {\n        months: 'જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર'.split(\n            '_'\n        ),\n        monthsShort:\n            'જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર'.split(\n            '_'\n        ),\n        weekdaysShort: 'રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ'.split('_'),\n        weekdaysMin: 'ર_સો_મં_બુ_ગુ_શુ_શ'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm વાગ્યે',\n            LTS: 'A h:mm:ss વાગ્યે',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY, A h:mm વાગ્યે',\n            LLLL: 'dddd, D MMMM YYYY, A h:mm વાગ્યે',\n        },\n        calendar: {\n            sameDay: '[આજ] LT',\n            nextDay: '[કાલે] LT',\n            nextWeek: 'dddd, LT',\n            lastDay: '[ગઇકાલે] LT',\n            lastWeek: '[પાછલા] dddd, LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s મા',\n            past: '%s પહેલા',\n            s: 'અમુક પળો',\n            ss: '%d સેકંડ',\n            m: 'એક મિનિટ',\n            mm: '%d મિનિટ',\n            h: 'એક કલાક',\n            hh: '%d કલાક',\n            d: 'એક દિવસ',\n            dd: '%d દિવસ',\n            M: 'એક મહિનો',\n            MM: '%d મહિનો',\n            y: 'એક વર્ષ',\n            yy: '%d વર્ષ',\n        },\n        preparse: function (string) {\n            return string.replace(/[૧૨૩૪૫૬૭૮૯૦]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        // Gujarati notation for meridiems are quite fuzzy in practice. While there exists\n        // a rigid notion of a 'Pahar' it is not used as rigidly in modern Gujarati.\n        meridiemParse: /રાત|બપોર|સવાર|સાંજ/,\n        meridiemHour: function (hour, meridiem) {\n            if (hour === 12) {\n                hour = 0;\n            }\n            if (meridiem === 'રાત') {\n                return hour < 4 ? hour : hour + 12;\n            } else if (meridiem === 'સવાર') {\n                return hour;\n            } else if (meridiem === 'બપોર') {\n                return hour >= 10 ? hour : hour + 12;\n            } else if (meridiem === 'સાંજ') {\n                return hour + 12;\n            }\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'રાત';\n            } else if (hour < 10) {\n                return 'સવાર';\n            } else if (hour < 17) {\n                return 'બપોર';\n            } else if (hour < 20) {\n                return 'સાંજ';\n            } else {\n                return 'રાત';\n            }\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 6, // The week that contains Jan 6th is the first week of the year.\n        },\n    });\n\n    return gu;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wFAAwF,CAACC,KAAK,CAClG,GACJ,CAAC;IACDC,WAAW,EACP,wEAAwE,CAACD,KAAK,CAC1E,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,uDAAuD,CAACH,KAAK,CACnE,GACJ,CAAC;IACDI,aAAa,EAAE,kCAAkC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC5DK,WAAW,EAAE,oBAAoB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC5CM,cAAc,EAAE;MACZC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE,kBAAkB;MACvBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,4BAA4B;MACjCC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,WAAW;MACpBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,kBAAkB;MAC5BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAO1C,SAAS,CAAC0C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAO3C,SAAS,CAAC2C,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACD;IACA;IACAE,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,SAAAA,CAAUC,IAAI,EAAEC,QAAQ,EAAE;MACpC,IAAID,IAAI,KAAK,EAAE,EAAE;QACbA,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,QAAQ,KAAK,KAAK,EAAE;QACpB,OAAOD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACtC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI;MACf,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI,IAAI,EAAE,GAAGA,IAAI,GAAGA,IAAI,GAAG,EAAE;MACxC,CAAC,MAAM,IAAIC,QAAQ,KAAK,MAAM,EAAE;QAC5B,OAAOD,IAAI,GAAG,EAAE;MACpB;IACJ,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUD,IAAI,EAAEE,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIH,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,KAAK;MAChB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,MAAM;MACjB,CAAC,MAAM;QACH,OAAO,KAAK;MAChB;IACJ,CAAC;IACDI,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOnD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}