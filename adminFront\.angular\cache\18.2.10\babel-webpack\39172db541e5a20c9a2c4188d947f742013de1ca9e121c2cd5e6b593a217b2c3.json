{"ast": null, "code": "export var STORAGE_KEY;\n(function (STORAGE_KEY) {\n  STORAGE_KEY[\"TOKEN\"] = \"cdp_token\";\n  STORAGE_KEY[\"ALLOW\"] = \"cdp_allow\";\n  STORAGE_KEY[\"BUID\"] = \"cdp_buId\";\n})(STORAGE_KEY || (STORAGE_KEY = {}));\nexport const DEFAULT_DATE = {\n  START_DATE: \"01/01/1990\",\n  END_DATE: \"12/31/2100\"\n};\nexport class FormBodyBuilder {\n  static BuildBodyContent(value) {\n    var _bodyContent = {};\n    if (value !== null && value !== undefined) {\n      const formData = new FormData();\n      for (const key of Object.keys(value)) {\n        var val = value[key];\n        if (val instanceof Array) {\n          var count = 0;\n          for (const v of val) {\n            if (v instanceof Object) {\n              for (const objName of Object.keys(v)) {\n                const toAppend = v[objName];\n                var keyName = `${key}[${count}].${objName}`;\n                if (toAppend !== null) {\n                  formData.append(keyName, toAppend);\n                }\n              }\n            } else {\n              for (const v of val) {\n                const toAppend = this._formDataValue(v);\n                if (toAppend !== null) {\n                  formData.append(key, toAppend);\n                  val = val.filter(x => x != v);\n                }\n              }\n            }\n            count++;\n          }\n        } else {\n          const toAppend = this._formDataValue(val);\n          if (toAppend !== null) {\n            formData.set(key, toAppend);\n          }\n        }\n      }\n      console.log(_bodyContent);\n      _bodyContent = formData;\n    }\n    return _bodyContent;\n  }\n  static _formDataValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (value instanceof Blob) {\n      return value;\n    }\n    if (typeof value === 'object') {\n      return JSON.stringify(value);\n    }\n    return String(value);\n  }\n}", "map": {"version": 3, "names": ["STORAGE_KEY", "DEFAULT_DATE", "START_DATE", "END_DATE", "FormBodyBuilder", "BuildBodyContent", "value", "_bodyContent", "undefined", "formData", "FormData", "key", "Object", "keys", "val", "Array", "count", "v", "objName", "toAppend", "keyName", "append", "_formDataValue", "filter", "x", "set", "console", "log", "Blob", "JSON", "stringify", "String"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\constant\\constant.ts"], "sourcesContent": ["export enum STORAGE_KEY {\r\n  TOKEN = 'cdp_token',\r\n  ALLOW = 'cdp_allow',\r\n  BUID = 'cdp_buId'\r\n}\r\n\r\nexport const enum CRYPTO {\r\n  TOKEN_KEY = 'DVROrKiHmncUgWBj',\r\n  TOKEN_IV = 'xcsJYPvUkxjdLi8b',\r\n}\r\n\r\nexport const DEFAULT_DATE = {\r\n  START_DATE: \"01/01/1990\",\r\n  END_DATE: \"12/31/2100\"\r\n}\r\n\r\nexport abstract class FormBodyBuilder {\r\n  public static BuildBodyContent(value: any) {\r\n    var _bodyContent: any = {};\r\n    if (value !== null && value !== undefined) {\r\n      const formData = new FormData();\r\n      for (const key of Object.keys(value)) {\r\n        var val = value[key];\r\n        if (val instanceof Array) {\r\n          var count = 0;\r\n          for (const v of val) {\r\n            if (v instanceof Object) {\r\n              for (const objName of Object.keys(v)) {\r\n                const toAppend = v[objName];\r\n                var keyName = `${key}[${count}].${objName}`;\r\n                if (toAppend !== null) {\r\n                  formData.append(keyName, toAppend);\r\n                }\r\n              }\r\n            } else {\r\n              for (const v of val) {\r\n                const toAppend = this._formDataValue(v);\r\n                if (toAppend !== null) {\r\n                  formData.append(key, toAppend);\r\n                  val = val.filter((x: any) => x != v);\r\n                }\r\n              }\r\n            }\r\n            count++;\r\n          }\r\n        } else {\r\n          const toAppend = this._formDataValue(val);\r\n          if (toAppend !== null) {\r\n            formData.set(key, toAppend);\r\n          }\r\n        }\r\n      }\r\n      console.log(_bodyContent);\r\n      \r\n      _bodyContent = formData;\r\n    }\r\n    return _bodyContent;\r\n  }\r\n  private static _formDataValue(value: any): any {\r\n    if (value === null || value === undefined) {\r\n      return null;\r\n    }\r\n    if (value instanceof Blob) {\r\n      return value;\r\n    }\r\n    if (typeof value === 'object') {\r\n      return JSON.stringify(value);\r\n    }\r\n    return String(value);\r\n  }\r\n}\r\n\r\n\r\n"], "mappings": "AAAA,WAAYA,WAIX;AAJD,WAAYA,WAAW;EACrBA,WAAA,uBAAmB;EACnBA,WAAA,uBAAmB;EACnBA,WAAA,qBAAiB;AACnB,CAAC,EAJWA,WAAW,KAAXA,WAAW;AAWvB,OAAO,MAAMC,YAAY,GAAG;EAC1BC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE;CACX;AAED,OAAM,MAAgBC,eAAe;EAC5B,OAAOC,gBAAgBA,CAACC,KAAU;IACvC,IAAIC,YAAY,GAAQ,EAAE;IAC1B,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;MACzC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC,EAAE;QACpC,IAAIQ,GAAG,GAAGR,KAAK,CAACK,GAAG,CAAC;QACpB,IAAIG,GAAG,YAAYC,KAAK,EAAE;UACxB,IAAIC,KAAK,GAAG,CAAC;UACb,KAAK,MAAMC,CAAC,IAAIH,GAAG,EAAE;YACnB,IAAIG,CAAC,YAAYL,MAAM,EAAE;cACvB,KAAK,MAAMM,OAAO,IAAIN,MAAM,CAACC,IAAI,CAACI,CAAC,CAAC,EAAE;gBACpC,MAAME,QAAQ,GAAGF,CAAC,CAACC,OAAO,CAAC;gBAC3B,IAAIE,OAAO,GAAG,GAAGT,GAAG,IAAIK,KAAK,KAAKE,OAAO,EAAE;gBAC3C,IAAIC,QAAQ,KAAK,IAAI,EAAE;kBACrBV,QAAQ,CAACY,MAAM,CAACD,OAAO,EAAED,QAAQ,CAAC;gBACpC;cACF;YACF,CAAC,MAAM;cACL,KAAK,MAAMF,CAAC,IAAIH,GAAG,EAAE;gBACnB,MAAMK,QAAQ,GAAG,IAAI,CAACG,cAAc,CAACL,CAAC,CAAC;gBACvC,IAAIE,QAAQ,KAAK,IAAI,EAAE;kBACrBV,QAAQ,CAACY,MAAM,CAACV,GAAG,EAAEQ,QAAQ,CAAC;kBAC9BL,GAAG,GAAGA,GAAG,CAACS,MAAM,CAAEC,CAAM,IAAKA,CAAC,IAAIP,CAAC,CAAC;gBACtC;cACF;YACF;YACAD,KAAK,EAAE;UACT;QACF,CAAC,MAAM;UACL,MAAMG,QAAQ,GAAG,IAAI,CAACG,cAAc,CAACR,GAAG,CAAC;UACzC,IAAIK,QAAQ,KAAK,IAAI,EAAE;YACrBV,QAAQ,CAACgB,GAAG,CAACd,GAAG,EAAEQ,QAAQ,CAAC;UAC7B;QACF;MACF;MACAO,OAAO,CAACC,GAAG,CAACpB,YAAY,CAAC;MAEzBA,YAAY,GAAGE,QAAQ;IACzB;IACA,OAAOF,YAAY;EACrB;EACQ,OAAOe,cAAcA,CAAChB,KAAU;IACtC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;MACzC,OAAO,IAAI;IACb;IACA,IAAIF,KAAK,YAAYsB,IAAI,EAAE;MACzB,OAAOtB,KAAK;IACd;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOuB,IAAI,CAACC,SAAS,CAACxB,KAAK,CAAC;IAC9B;IACA,OAAOyB,MAAM,CAACzB,KAAK,CAAC;EACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}