{"ast": null, "code": "import lastDayOfWeek from \"../lastDayOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the last day of an ISO week for the given date.\n *\n * @description\n * Return the last day of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of an ISO week for 2 September 2014 11:55:00:\n * const result = lastDayOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport default function lastDayOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return lastDayOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}