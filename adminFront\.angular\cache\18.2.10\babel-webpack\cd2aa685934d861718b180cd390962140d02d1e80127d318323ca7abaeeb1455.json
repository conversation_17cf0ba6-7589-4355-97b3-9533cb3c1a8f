{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n  var _a, _b;\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxBufferSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let bufferRecords = [];\n    let restartOnEmit = false;\n    const emit = record => {\n      const {\n        buffer,\n        subs\n      } = record;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n    const startBuffer = () => {\n      if (bufferRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const buffer = [];\n        const record = {\n          buffer,\n          subs\n        };\n        bufferRecords.push(record);\n        executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n      }\n    };\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n    startBuffer();\n    const bufferTimeSubscriber = createOperatorSubscriber(subscriber, value => {\n      const recordsCopy = bufferRecords.slice();\n      for (const record of recordsCopy) {\n        const {\n          buffer\n        } = record;\n        buffer.push(value);\n        maxBufferSize <= buffer.length && emit(record);\n      }\n    }, () => {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, () => bufferRecords = null);\n    source.subscribe(bufferTimeSubscriber);\n  });\n}\n//# sourceMappingURL=bufferTime.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}