{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NbDatepickerModule, NbDialogModule, NbMenuModule, NbSidebarModule, NbThemeModule, NbTimepickerModule, NbToastrModule, NbWindowModule } from '@nebular/theme';\nimport { ThemeModule } from './@theme/theme.module';\nimport { PagesComponent } from './pages/pages.component';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { NgbModule } from '@ng-bootstrap/ng-bootstrap';\nimport { FormsModule } from '@angular/forms';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { LoginComponent } from './pages/login/login.component';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { CoreModule } from './@core/core.module';\nimport { appConfig } from './app.config';\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { SharedModule } from './pages/components/shared.module';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { registerLocaleData } from '@angular/common';\nimport localeZh from '@angular/common/locales/zh';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"./@theme/theme.module\";\nimport * as i3 from \"./@core/core.module\";\nimport * as i4 from \"angular-calendar\";\nregisterLocaleData(localeZh);\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static {\n      this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AppModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppModule,\n        bootstrap: [AppComponent]\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [{\n          provide: HTTP_INTERCEPTORS,\n          useClass: TokenInterceptor,\n          multi: true\n        }, appConfig.providers],\n        imports: [NgxSpinnerModule, BrowserModule, BrowserAnimationsModule, AppRoutingModule, FormsModule, SharedModule, HttpClientModule, NbThemeModule.forRoot(), ThemeModule.forRoot(), NbSidebarModule.forRoot(), NbMenuModule.forRoot(), NbDatepickerModule.forRoot(), NbDialogModule.forRoot(), NbWindowModule.forRoot(), NbToastrModule.forRoot(), NbTimepickerModule.forRoot(), NgbModule, CoreModule.forRoot(), PagesComponent, HomeComponent, LoginComponent, FullCalendarModule, CalendarModule.forRoot({\n          provide: DateAdapter,\n          useFactory: adapterFactory\n        })]\n      });\n    }\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}