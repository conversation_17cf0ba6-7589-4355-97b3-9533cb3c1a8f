{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function ignoreElements() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, noop));\n  });\n}\n//# sourceMappingURL=ignoreElements.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}