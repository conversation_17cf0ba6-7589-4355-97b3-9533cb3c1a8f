{"ast": null, "code": "export function throwIfAlreadyLoaded(parentModule, moduleName) {\n  if (parentModule) {\n    throw new Error(`${moduleName} has already been loaded. Import Core modules in the AppModule only.`);\n  }\n}", "map": {"version": 3, "names": ["throwIfAlreadyLoaded", "parentModule", "moduleName", "Error"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\module-import-guard.ts"], "sourcesContent": ["export function throwIfAlreadyLoaded(parentModule: any, moduleName: string) {\r\n  if (parentModule) {\r\n    throw new Error(`${moduleName} has already been loaded. Import Core modules in the AppModule only.`);\r\n  }\r\n}\r\n"], "mappings": "AAAA,OAAM,SAAUA,oBAAoBA,CAACC,YAAiB,EAAEC,UAAkB;EACxE,IAAID,YAAY,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,GAAGD,UAAU,sEAAsE,CAAC;EACtG;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}