{"ast": null, "code": "import { Subject } from './Subject';\nexport class BehaviorSubject extends Subject {\n  constructor(_value) {\n    super();\n    this._value = _value;\n  }\n  get value() {\n    return this.getValue();\n  }\n  _subscribe(subscriber) {\n    const subscription = super._subscribe(subscriber);\n    !subscription.closed && subscriber.next(this._value);\n    return subscription;\n  }\n  getValue() {\n    const {\n      hasError,\n      thrownError,\n      _value\n    } = this;\n    if (hasError) {\n      throw thrownError;\n    }\n    this._throwIfClosed();\n    return _value;\n  }\n  next(value) {\n    super.next(this._value = value);\n  }\n}\n//# sourceMappingURL=BehaviorSubject.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}