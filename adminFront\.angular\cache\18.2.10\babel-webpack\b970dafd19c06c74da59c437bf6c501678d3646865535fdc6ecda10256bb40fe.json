{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /** @preserve\n  (c) 2012 by <PERSON><PERSON><PERSON>. All rights reserved.\n  \tRedistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:\n  \t    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.\n      - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.\n  \tTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n  */\n\n  (function (Math) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo;\n\n    // Constants table\n    var _zl = WordArray.create([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]);\n    var _zr = WordArray.create([5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]);\n    var _sl = WordArray.create([11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]);\n    var _sr = WordArray.create([8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]);\n    var _hl = WordArray.create([0x00000000, 0x5A827999, 0x6ED9EBA1, 0x8F1BBCDC, 0xA953FD4E]);\n    var _hr = WordArray.create([0x50A28BE6, 0x5C4DD124, 0x6D703EF3, 0x7A6D76E9, 0x00000000]);\n\n    /**\n     * RIPEMD160 hash algorithm.\n     */\n    var RIPEMD160 = C_algo.RIPEMD160 = Hasher.extend({\n      _doReset: function () {\n        this._hash = WordArray.create([0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476, 0xC3D2E1F0]);\n      },\n      _doProcessBlock: function (M, offset) {\n        // Swap endian\n        for (var i = 0; i < 16; i++) {\n          // Shortcuts\n          var offset_i = offset + i;\n          var M_offset_i = M[offset_i];\n\n          // Swap\n          M[offset_i] = (M_offset_i << 8 | M_offset_i >>> 24) & 0x00ff00ff | (M_offset_i << 24 | M_offset_i >>> 8) & 0xff00ff00;\n        }\n        // Shortcut\n        var H = this._hash.words;\n        var hl = _hl.words;\n        var hr = _hr.words;\n        var zl = _zl.words;\n        var zr = _zr.words;\n        var sl = _sl.words;\n        var sr = _sr.words;\n\n        // Working variables\n        var al, bl, cl, dl, el;\n        var ar, br, cr, dr, er;\n        ar = al = H[0];\n        br = bl = H[1];\n        cr = cl = H[2];\n        dr = dl = H[3];\n        er = el = H[4];\n        // Computation\n        var t;\n        for (var i = 0; i < 80; i += 1) {\n          t = al + M[offset + zl[i]] | 0;\n          if (i < 16) {\n            t += f1(bl, cl, dl) + hl[0];\n          } else if (i < 32) {\n            t += f2(bl, cl, dl) + hl[1];\n          } else if (i < 48) {\n            t += f3(bl, cl, dl) + hl[2];\n          } else if (i < 64) {\n            t += f4(bl, cl, dl) + hl[3];\n          } else {\n            // if (i<80) {\n            t += f5(bl, cl, dl) + hl[4];\n          }\n          t = t | 0;\n          t = rotl(t, sl[i]);\n          t = t + el | 0;\n          al = el;\n          el = dl;\n          dl = rotl(cl, 10);\n          cl = bl;\n          bl = t;\n          t = ar + M[offset + zr[i]] | 0;\n          if (i < 16) {\n            t += f5(br, cr, dr) + hr[0];\n          } else if (i < 32) {\n            t += f4(br, cr, dr) + hr[1];\n          } else if (i < 48) {\n            t += f3(br, cr, dr) + hr[2];\n          } else if (i < 64) {\n            t += f2(br, cr, dr) + hr[3];\n          } else {\n            // if (i<80) {\n            t += f1(br, cr, dr) + hr[4];\n          }\n          t = t | 0;\n          t = rotl(t, sr[i]);\n          t = t + er | 0;\n          ar = er;\n          er = dr;\n          dr = rotl(cr, 10);\n          cr = br;\n          br = t;\n        }\n        // Intermediate hash value\n        t = H[1] + cl + dr | 0;\n        H[1] = H[2] + dl + er | 0;\n        H[2] = H[3] + el + ar | 0;\n        H[3] = H[4] + al + br | 0;\n        H[4] = H[0] + bl + cr | 0;\n        H[0] = t;\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = (nBitsTotal << 8 | nBitsTotal >>> 24) & 0x00ff00ff | (nBitsTotal << 24 | nBitsTotal >>> 8) & 0xff00ff00;\n        data.sigBytes = (dataWords.length + 1) * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Shortcuts\n        var hash = this._hash;\n        var H = hash.words;\n\n        // Swap endian\n        for (var i = 0; i < 5; i++) {\n          // Shortcut\n          var H_i = H[i];\n\n          // Swap\n          H[i] = (H_i << 8 | H_i >>> 24) & 0x00ff00ff | (H_i << 24 | H_i >>> 8) & 0xff00ff00;\n        }\n\n        // Return final computed hash\n        return hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n    function f1(x, y, z) {\n      return x ^ y ^ z;\n    }\n    function f2(x, y, z) {\n      return x & y | ~x & z;\n    }\n    function f3(x, y, z) {\n      return (x | ~y) ^ z;\n    }\n    function f4(x, y, z) {\n      return x & z | y & ~z;\n    }\n    function f5(x, y, z) {\n      return x ^ (y | ~z);\n    }\n    function rotl(x, n) {\n      return x << n | x >>> 32 - n;\n    }\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.RIPEMD160('message');\n     *     var hash = CryptoJS.RIPEMD160(wordArray);\n     */\n    C.RIPEMD160 = Hasher._createHelper(RIPEMD160);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacRIPEMD160(message, key);\n     */\n    C.HmacRIPEMD160 = Hasher._createHmacHelper(RIPEMD160);\n  })(Math);\n  return CryptoJS.RIPEMD160;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}