{"ast": null, "code": "import { DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { forkJoin, map, switchMap, tap } from 'rxjs';\nimport { CalendarModule } from 'primeng/calendar';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"../../../@theme/pipes/BooleanString.pipe\";\nimport * as i13 from \"../../../@theme/pipes/timing.pipe\";\nimport * as i14 from \"primeng/calendar\";\nfunction PreOrderComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r2.CBuildCaseName);\n  }\n}\nfunction PreOrderComponent_tr_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 23);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 23);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"hour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 25);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"booleanString\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 23);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 25)(24, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_tr_58_Template_button_click_24_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(62);\n      return i0.ɵɵresetView(ctx_r5.edit(dialog_r3, item_r5));\n    });\n    i0.ɵɵelement(25, \"i\", 31);\n    i0.ɵɵtext(26, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_tr_58_Template_button_click_27_listener() {\n      const ctx_r6 = i0.ɵɵrestoreView(_r4);\n      const item_r5 = ctx_r6.$implicit;\n      const i_r8 = ctx_r6.index;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteAppointment(item_r5, i_r8));\n    });\n    i0.ɵɵelement(28, \"i\", 33);\n    i0.ɵɵtext(29, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CCustomerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CPhone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, item_r5.CPreOrderDate, \"yyyy/MM/dd\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(14, 12, item_r5.CHour));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.CPeoples);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 14, item_r5.CHasDesigner));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 16, item_r5.CCreateDT, \"yyyy/MM/dd\"));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.isNewAppointment)(\"value\", item_r10.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CHouseHold);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_14_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r12 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"value\", floor_r12)(\"disabled\", !ctx_r5.isNewAppointment);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(floor_r12);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_nb_select_14_Template_nb_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CFloor, $event) || (ctx_r5.appointment.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, PreOrderComponent_ng_template_61_nb_select_14_nb_option_1_Template, 2, 3, \"nb-option\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.floorList);\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r13.CDate);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, item_r13.CDate, \"yyyy/MM/dd\"));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_23_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"hour\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", hour_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 2, hour_r15));\n  }\n}\nfunction PreOrderComponent_ng_template_61_nb_select_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 62);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_nb_select_23_Template_nb_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHour, $event) || (ctx_r5.appointment.CHour = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, PreOrderComponent_ng_template_61_nb_select_23_nb_option_1_Template, 3, 4, \"nb-option\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHour);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.hourList);\n  }\n}\nfunction PreOrderComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 34)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"p\", 35);\n    i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u65BC\\u6B64\\u70BA\\u5BA2\\u6236\\u5EFA\\u7ACB\\u4E00\\u7B46\\u9810\\u7D04\\uFF0C\\u4F46\\u50C5\\u9650\\u65BC\\u5728\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u5167\\u4E4B\\u5BA2\\u6236\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 5)(7, \"label\", 36);\n    i0.ɵɵtext(8, \"\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"nb-select\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHouseHold, $event) || (ctx_r5.appointment.CHouseHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_ng_template_61_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onHouseHoldChange($event));\n    });\n    i0.ɵɵtemplate(10, PreOrderComponent_ng_template_61_nb_option_10_Template, 2, 3, \"nb-option\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 5)(12, \"label\", 39);\n    i0.ɵɵtext(13, \"\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, PreOrderComponent_ng_template_61_nb_select_14_Template, 2, 2, \"nb-select\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 5)(16, \"label\", 41);\n    i0.ɵɵtext(17, \"\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-select\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CPreOrderDate, $event) || (ctx_r5.appointment.CPreOrderDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_ng_template_61_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onDateTimeChange($event));\n    });\n    i0.ɵɵtemplate(19, PreOrderComponent_ng_template_61_nb_option_19_Template, 3, 5, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 5)(21, \"label\", 43);\n    i0.ɵɵtext(22, \"\\u6642\\u6BB5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, PreOrderComponent_ng_template_61_nb_select_23_Template, 2, 2, \"nb-select\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 5)(25, \"label\", 45);\n    i0.ɵɵtext(26, \"\\u51FA\\u5E2D\\u4EBA\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_27_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CPeoples, $event) || (ctx_r5.appointment.CPeoples = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 5)(29, \"label\", 47);\n    i0.ɵɵtext(30, \"\\u8A2D\\u8A08\\u5E2B\\u51FA\\u5E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\")(32, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHasDesigner, $event) || (ctx_r5.appointment.CHasDesigner = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"label\", 49);\n    i0.ɵɵtext(34, \"\\u662F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_input_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHasDesigner, $event) || (ctx_r5.appointment.CHasDesigner = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"label\", 51);\n    i0.ɵɵtext(37, \"\\u5426\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 5)(39, \"label\", 52);\n    i0.ɵɵtext(40, \"\\u5099\\u8A3B\\u4E8B\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"textarea\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_41_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CRemark, $event) || (ctx_r5.appointment.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 5)(43, \"label\", 52);\n    i0.ɵɵtext(44, \"\\u8B8A\\u66F4\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"textarea\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r5 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r5.appointment.CHouseRequirement, $event) || (ctx_r5.appointment.CHouseRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"nb-card-footer\", 55)(47, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_ng_template_61_Template_button_click_47_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r9).dialogRef;\n      return i0.ɵɵresetView(ref_r16.close());\n    });\n    i0.ɵɵtext(48, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function PreOrderComponent_ng_template_61_Template_button_click_49_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.save(ref_r16));\n    });\n    i0.ɵɵtext(50, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.isNewAppointment ? \"\\u65B0\\u589E\" : \"\\u7DE8\\u8F2F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"hidden\", !ctx_r5.isNewAppointment);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.houseHoldList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.floorList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CPreOrderDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.dateTimeList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hourList && ctx_r5.hourList.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CPeoples);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHasDesigner);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHasDesigner);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r5.appointment.CHouseRequirement);\n  }\n}\nexport class PreOrderComponent extends BaseComponent {\n  constructor(allow, dialogService, houseService, buildCaseService, valid, message, _ultilityService) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this.houseService = houseService;\n    this.buildCaseService = buildCaseService;\n    this.valid = valid;\n    this.message = message;\n    this._ultilityService = _ultilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.buildCaseList = [];\n    this.allBuildCaseList = [];\n    this.caseList = [];\n    this.houseHoldList = [];\n    this.newHouseHightestList = [];\n    this.dateTimeList = [];\n    this.appointment = {};\n    this.searchAppointment = {};\n    this.floorList = [];\n    this.hourList = [];\n    this.destroy = inject(DestroyRef);\n  }\n  ngOnInit() {\n    this.isNewAppointment = false;\n    this.initialList();\n  }\n  initialList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(switchMap(res => {\n      this.caseList = res.Entries ?? [];\n      this.currentBuildCase = res.Entries?.[0]?.cID;\n      let listAppointmentRequest = {\n        body: {\n          CBuildCaseID: this.currentBuildCase\n        }\n      };\n      this.getHourListAppointment();\n      this.getHouseAndFloorByBuildCaseId();\n      return this.houseService.apiHouseGetListAppointmentsPost$Json(listAppointmentRequest);\n    }), takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.allBuildCaseList = res.Entries ?? [];\n      if (res.TotalItems || res.TotalItems === 0) {\n        this.controllPagination(res.TotalItems);\n      }\n    });\n  }\n  controllPagination(totalItems) {\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\n    this.totalRecords = totalItems;\n    let lastIndex = this.totalRecords < this.pageIndex * this.pageSize ? this.totalRecords + 1 : this.pageIndex * this.pageSize;\n    this.buildCaseList = this.allBuildCaseList.slice(this.pageFirst - 1, lastIndex);\n  }\n  getHouseAndFloorByBuildCaseId() {\n    return this.buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json({\n      buildCaseId: this.currentBuildCase\n    }).pipe(tap(res => {\n      if (res.Entries && res.Entries.length) {\n        this.houseHoldList = res.Entries;\n        console.log('getHouseAndFloorByBuildCaseId');\n      }\n    }), takeUntilDestroyed(this.destroy));\n  }\n  groupByCDate(data) {\n    const groupedData = data.reduce((acc, entry) => {\n      if (!acc[entry.CDate]) {\n        acc[entry.CDate] = [];\n      }\n      acc[entry.CDate].push(entry.CHour);\n      return acc;\n    }, {});\n    return Object.keys(groupedData).map(CDate => ({\n      CDate,\n      CHour: groupedData[CDate]\n    }));\n  }\n  getHourListAppointment() {\n    const req = {\n      CBuildCaseID: this.currentBuildCase\n    };\n    return this.houseService.apiHouseGetHourListAppointmentPost$Json({\n      body: req\n    }).pipe(map(res => {\n      res.Entries?.forEach(i => {\n        i.CDate = i.CDate?.split('T')[0];\n        i.CHour = i.CHour;\n      });\n      res.Entries = res.Entries?.filter(i => i.CHour >= 9 && i.CHour <= 21);\n      return res;\n    }), tap(res => {\n      if (res.Entries && res.Entries.length) {\n        this.listCurrent = res.Entries;\n        this.dateTimeList = this.groupByCDate(res.Entries);\n      }\n    }), takeUntilDestroyed(this.destroy));\n  }\n  filterOrders(listAppointmentsByCustomer, selectedData) {\n    let result;\n    if (selectedData && selectedData?.CHour && selectedData.CDate) {\n      result = selectedData.CHour ? [...selectedData.CHour] : [];\n      // Bước 1: Lọc mảng A với các phần tử có CPreOrderDate trùng với B.CDate\n      const filteredOrders = listAppointmentsByCustomer.filter(order => order.CPreOrderDate.startsWith(selectedData.CDate));\n      // Bước 2: Loại bỏ các phần tử B.CHour trùng với các giá trị CHour trong mảng filteredOrders\n      filteredOrders.forEach(order => {\n        const hourIndex = result.indexOf(order.CHour);\n        if (hourIndex !== -1) {\n          result.splice(hourIndex, 1); // Xóa phần tử trùng khỏi B.CHour\n        }\n      });\n      if (this.selectedAppointment.CPreOrderDate.startsWith(selectedData.CDate) && this.selectedAppointment.CHour) {\n        result = [...result, this.selectedAppointment.CHour];\n      }\n    }\n    return result;\n  }\n  onDateTimeChange(selectedDate) {\n    const selectedData = this.dateTimeList.find(item => item.CDate === selectedDate);\n    this.hourList = selectedData ? selectedData.CHour.sort((a, b) => a - b) : [];\n    this.hourList = this.filterOrders(this.listAppointmentsByCustomer, selectedData);\n    this.appointment.CHour = undefined;\n  }\n  getUserBuildcaseList() {\n    let request = {};\n    request.body = {};\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.caseList = res.Entries ?? [];\n    });\n  }\n  getPreorderList(params) {\n    let request = {\n      body: {\n        CBuildCaseID: params.body.CBuildCaseID,\n        CCustomerName: params.body.CCustomerName,\n        CPhone: params.body && params.body.CPhone,\n        CPreOrderDate: params.body && params.body.CPreOrderDate?.split(\"T\")[0]\n      }\n    };\n    this.houseService.apiHouseGetListAppointmentsPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.allBuildCaseList = res.Entries ?? [];\n        if (res.TotalItems || res.TotalItems === 0) {\n          this.controllPagination(res.TotalItems);\n        }\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  search() {\n    this.searchAppointment = {\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CCustomerName: this.currentCustomerName,\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //=== '全部' ? undefined : this.currentSelectDate\n      }\n    };\n    this.getPreorderList(this.searchAppointment);\n  }\n  onHouseHoldChange(selectedHouseHold) {\n    const selectedData = this.houseHoldList.find(item => item.CHouseHold === selectedHouseHold);\n    this.floorList = selectedData ? selectedData.Floors : [];\n    this.appointment.CFloor = undefined; // Reset selected floor\n  }\n  edit(ref, item) {\n    forkJoin({\n      hourList: this.getHourListAppointment(),\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n    }).subscribe({\n      next: () => {\n        this.selectedAppointment = item;\n        this.isNewAppointment = false;\n        this.editChangepreoderID = item.CChangePreOrderID;\n        let selectedHouseHold = this.houseHoldList.find(i => i.CHouseHold === item.CHouseHold);\n        this.appointment.CHouseHold = selectedHouseHold?.CHouseHold;\n        this.floorList = selectedHouseHold?.Floors;\n        // let currentTemp = this.listCurrent.find((i: any) => {\n        //   return (i.CDate === item.CPreOrderDate.split(\"T\")[0] && i.CHour === item.CHour)\n        // });\n        // if (!currentTemp) {\n        this.dateTimeList = this.groupByCDate([...this.listCurrent]); //, { CDate: item.CPreOrderDate.split(\"T\")[0], CHour: item.CHour }])\n        // }\n        this.listAppointmentsByCustomer = this.allBuildCaseList.filter(o => o.CCustomerName == item.CCustomerName);\n        this.onDateTimeChange(item.CPreOrderDate.split(\"T\")[0]);\n        this.appointment = {\n          CFloor: item.CFloor,\n          CHasDesigner: item.CHasDesigner,\n          CHour: item.CHour,\n          CHouseHold: item.CHouseHold,\n          CPeoples: item.CPeoples,\n          CPreOrderDate: item.CPreOrderDate.split(\"T\")[0],\n          CRemark: item.CRemark,\n          CNeedMail: item.CNeedMail,\n          CHouseRequirement: item.CHouseRequirement\n        };\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  addNewAppointment(ref) {\n    forkJoin({\n      hourList: this.getHourListAppointment(),\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n    }).subscribe({\n      next: () => {\n        this.isNewAppointment = true;\n        this.appointment = {};\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  checkAppointmentExists() {\n    const formattedPreOrderDate = this.appointment.CPreOrderDate + 'T00:00:00';\n    return this.listAppointmentsByCustomer.some(appointment => appointment.CPreOrderDate === formattedPreOrderDate && appointment.CHour === this.appointment.CHour);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required(`戶別`, this.appointment.CHouseHold);\n    this.valid.required(`樓層`, this.appointment.CFloor);\n    this.valid.required(`日期`, this.appointment.CPreOrderDate);\n    this.valid.required(`時段`, this.appointment.CHour);\n    this.valid.required(`出席人數`, this.appointment.CPeoples);\n    this.valid.required(`設計師出席`, this.appointment.CHasDesigner);\n    // console.log('this.listAppointmentsByCustomer', this.listAppointmentsByCustomer.filter((item: any)=> item.CHouseHold ==this.appointment.CHouseHold ));\n    // console.log('this.appointment', this.appointment);\n    // if (this.listAppointmentsByCustomer && this.listAppointmentsByCustomer.length) {\n    //   if (this.checkAppointmentExists()) {\n    //     this.valid.addErrorMessage('無法選擇尚未被加入可 預約時段的日期及時段');\n    //   }\n    // }\n  }\n  deleteAppointment(item, i) {\n    if (window.confirm(`確定要刪除【項目${this.pageFirst + i}】?`)) {\n      this.houseService.apiHouseDeleteAppointmentPost$Json({\n        body: item.CChangePreOrderID\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getPreorderList({\n            body: {\n              CBuildCaseID: this.currentBuildCase\n            }\n          });\n        }\n      });\n    }\n  }\n  saveEdit(ref) {\n    let request = {\n      body: {\n        CID: this.editChangepreoderID,\n        CHasDesigner: this.appointment.CHasDesigner,\n        CHour: this.appointment.CHour,\n        CPeoples: this.appointment.CPeoples,\n        CPreOrderDate: this.appointment.CPreOrderDate,\n        CRemark: this.appointment.CRemark,\n        CStatus: this.selectedAppointment.CStatus\n      }\n    };\n    this.houseService.apiHouseEditAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getPreorderList({\n          body: {\n            CBuildCaseID: this.currentBuildCase\n          }\n        });\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let request;\n    request = {\n      body: {\n        ...this.appointment,\n        CBuildcaseID: this.currentBuildCase\n      }\n    };\n    if (this.isNewAppointment) {\n      this.houseService.apiHouseCreateAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(`執行成功`);\n          this.getPreorderList({\n            body: {\n              CBuildCaseID: this.currentBuildCase\n            }\n          });\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    } else {\n      this.saveEdit(ref);\n    }\n  }\n  exportExcel() {\n    this.houseService.apiHouseExportExcelListAppointmentsPost$Json({\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CCustomerName: this.currentCustomerName,\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //this.currentSelectDate === '全部' ? undefined : this.currentSelectDate\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this._ultilityService.downloadExcelFile(res.Entries?.FileByte, '預約');\n      }\n    })).subscribe();\n  }\n  static {\n    this.ɵfac = function PreOrderComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreOrderComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i3.BuildCaseService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PreOrderComponent,\n      selectors: [[\"app-pre-order\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 63,\n      vars: 13,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"mb-4\", 2, \"color\", \"#818181\", \"font-weight\", \"700\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [3, \"ngModelChange\", \"selectedChange\", \"placeholder\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"name\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BA2\\u6236\\u59D3\\u540D\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"preOrderDate\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-full\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\"], [\"for\", \"phone\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BA2\\u6236\\u96FB\\u8A71\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"justify-end\"], [1, \"btn\", \"btn-info\", \"mr-3\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"mr-3\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [2, \"min-width\", \"5%\"], [2, \"min-width\", \"10%\"], [2, \"min-width\", \"15%\"], [1, \"text-center\", 2, \"min-width\", \"15%\"], [4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [3, \"value\"], [1, \"text-center\", 2, \"min-width\", \"10%\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [2, \"max-height\", \"95vh\", \"min-width\", \"400px\"], [1, \"mb-4\", 2, \"color\", \"#818181\", \"font-weight\", \"700\", 3, \"hidden\"], [\"for\", \"distinguish\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"disabled\", \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"floor\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"for\", \"date\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u65E5\\u671F\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"for\", \"timeline\", \"baseLabel\", \"\", 1, \"required-field\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6642\\u6BB5\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"for\", \"headcount\", \"baseLabel\", \"\", 1, \"required-field\"], [\"type\", \"number\", \"nbInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"designer\", \"baseLabel\", \"\", 1, \"required-field\"], [\"type\", \"radio\", \"id\", \"designerAttend\", \"name\", \"attend\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"designerAttend\", 1, \"label\", \"ml-1\"], [\"type\", \"radio\", \"id\", \"designerAbsent\", \"name\", \"attend\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"designerAbsent\", 1, \"label\", \"ml-1\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"align-self-start\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"remark\", \"id\", \"remark\", \"rows\", \"5\", \"nbInput\", \"\", \"disabled\", \"true\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [3, \"disabled\", \"value\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6A13\\u5C64\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", \"disabled\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"disabled\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6642\\u6BB5\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function PreOrderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"p\", 2);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u65BC\\u6B64\\u6AA2\\u8996/\\u7DE8\\u8F2F\\u76EE\\u524D\\u4E4B\\u9810\\u7D04\\u8CC7\\u6599\\uFF0C\\u4EA6\\u53EF\\u70BA\\u5BA2\\u6236\\u65B0\\u589E\\u4E00\\u7B46\\u8CC7\\u6599\\uFF08\\u4F46\\u6BCF\\u500B\\u6236\\u5225\\u53EA\\u80FD\\u6709\\u4E00\\u7B46\\u9810\\u7D04\\u8CC7\\u6599\\uFF09\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.currentBuildCase, $event) || (ctx.currentBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PreOrderComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.search());\n          });\n          i0.ɵɵtemplate(12, PreOrderComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 5)(14, \"label\", 9);\n          i0.ɵɵtext(15, \"\\u5BA2\\u6236\\u59D3\\u540D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.currentCustomerName, $event) || (ctx.currentCustomerName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 5)(19, \"label\", 11);\n          i0.ɵɵtext(20, \"\\u9810\\u7D04\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p-calendar\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_p_calendar_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.currentSelectDate, $event) || (ctx.currentSelectDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 13);\n          i0.ɵɵtext(24, \"\\u5BA2\\u6236\\u96FB\\u8A71\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PreOrderComponent_Template_input_ngModelChange_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.currentCustomerPhone, $event) || (ctx.currentCustomerPhone = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 15)(27, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_27_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.search());\n          });\n          i0.ɵɵtext(28, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_29_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialog_r3 = i0.ɵɵreference(62);\n            return i0.ɵɵresetView(ctx.addNewAppointment(dialog_r3));\n          });\n          i0.ɵɵtext(30, \"\\u65B0\\u589E\\u9810\\u7D04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function PreOrderComponent_Template_button_click_31_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportExcel());\n          });\n          i0.ɵɵtext(32, \"\\u532F\\u51FA\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 19)(34, \"table\", 20)(35, \"thead\")(36, \"tr\", 21)(37, \"th\", 22);\n          i0.ɵɵtext(38, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 22);\n          i0.ɵɵtext(40, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\", 23);\n          i0.ɵɵtext(42, \"\\u5BA2\\u6236\\u59D3\\u540D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\", 23);\n          i0.ɵɵtext(44, \"\\u5BA2\\u6236\\u96FB\\u8A71\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\", 23);\n          i0.ɵɵtext(46, \"\\u9810\\u7D04\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 23);\n          i0.ɵɵtext(48, \"\\u9810\\u7D04\\u6642\\u6BB5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 23);\n          i0.ɵɵtext(50, \"\\u51FA\\u5E2D\\u4EBA\\u6578\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 24);\n          i0.ɵɵtext(52, \"\\u8A2D\\u8A08\\u5E2B\\u51FA\\u5E2D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 23);\n          i0.ɵɵtext(54, \"\\u586B\\u55AE\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 25);\n          i0.ɵɵtext(56, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"tbody\");\n          i0.ɵɵtemplate(58, PreOrderComponent_tr_58_Template, 30, 19, \"tr\", 26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"nb-card-footer\")(60, \"ngx-pagination\", 27);\n          i0.ɵɵtwoWayListener(\"PageChange\", function PreOrderComponent_Template_ngx_pagination_PageChange_60_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function PreOrderComponent_Template_ngx_pagination_PageChange_60_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.controllPagination(ctx.totalRecords));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(61, PreOrderComponent_ng_template_61_Template, 51, 15, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.caseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentCustomerName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"appendTo\", \"body\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentSelectDate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentCustomerPhone);\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.RadioControlValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseLabelDirective, i12.BooleanStringPipe, i13.FormatHourPipe, CalendarModule, i14.Calendar],\n      styles: [\"label[_ngcontent-%COMP%] {\\n  min-width: 75px;\\n  margin: 0;\\n}\\n\\ninput[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%], nb-select[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n  p-calendar > span {\\n  width: 100%;\\n  max-width: 20rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByZS1vcmRlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGVBQUE7RUFDQSxTQUFBO0FBQ0Y7O0FBR0E7RUFDRSxPQUFBO0FBQUY7O0FBSUE7RUFDRSxXQUFBO0VBQ0EsZ0JBQUE7QUFERiIsImZpbGUiOiJwcmUtb3JkZXIuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyJsYWJlbCB7XHJcbiAgbWluLXdpZHRoOiA3NXB4O1xyXG4gIG1hcmdpbjogMDtcclxufVxyXG5cclxuXHJcbmlucHV0LCB0ZXh0YXJlYSwgbmItc2VsZWN0e1xyXG4gIGZsZXg6MVxyXG59XHJcblxyXG5cclxuOjpuZy1kZWVwIHAtY2FsZW5kYXIgPiBzcGFuIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDIwcmVtO1xyXG59Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVzZXJ2YXRpb24tdGltZS1tYW5hZ2VtZW50L3ByZS1vcmRlci9wcmUtb3JkZXIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFBO0VBQ0EsU0FBQTtBQUNGOztBQUdBO0VBQ0UsT0FBQTtBQUFGOztBQUlBO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0FBREY7QUFDQSxvbEJBQW9sQiIsInNvdXJjZXNDb250ZW50IjpbImxhYmVsIHtcclxuICBtaW4td2lkdGg6IDc1cHg7XHJcbiAgbWFyZ2luOiAwO1xyXG59XHJcblxyXG5cclxuaW5wdXQsIHRleHRhcmVhLCBuYi1zZWxlY3R7XHJcbiAgZmxleDoxXHJcbn1cclxuXHJcblxyXG46Om5nLWRlZXAgcC1jYWxlbmRhciA+IHNwYW4ge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIG1heC13aWR0aDogMjByZW07XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DestroyRef", "inject", "CommonModule", "takeUntilDestroyed", "fork<PERSON><PERSON>n", "map", "switchMap", "tap", "CalendarModule", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate", "CBuildCaseName", "ɵɵlistener", "PreOrderComponent_tr_58_Template_button_click_24_listener", "item_r5", "ɵɵrestoreView", "_r4", "$implicit", "ctx_r5", "ɵɵnextContext", "dialog_r3", "ɵɵreference", "ɵɵresetView", "edit", "ɵɵelement", "PreOrderComponent_tr_58_Template_button_click_27_listener", "ctx_r6", "i_r8", "index", "deleteAppointment", "CHouseHold", "CFloor", "CCustomerName", "CPhone", "ɵɵpipeBind2", "CPreOrderDate", "ɵɵpipeBind1", "CHour", "CPeoples", "CHasDesigner", "CCreateDT", "isNewAppointment", "item_r10", "floor_r12", "ɵɵtwoWayListener", "PreOrderComponent_ng_template_61_nb_select_14_Template_nb_select_ngModelChange_0_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "appointment", "ɵɵtemplate", "PreOrderComponent_ng_template_61_nb_select_14_nb_option_1_Template", "ɵɵtwoWayProperty", "floorList", "item_r13", "CDate", "hour_r15", "PreOrderComponent_ng_template_61_nb_select_23_Template_nb_select_ngModelChange_0_listener", "_r14", "PreOrderComponent_ng_template_61_nb_select_23_nb_option_1_Template", "hourList", "PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_9_listener", "_r9", "PreOrderComponent_ng_template_61_Template_nb_select_selectedChange_9_listener", "onHouseHoldChange", "PreOrderComponent_ng_template_61_nb_option_10_Template", "PreOrderComponent_ng_template_61_nb_select_14_Template", "PreOrderComponent_ng_template_61_Template_nb_select_ngModelChange_18_listener", "PreOrderComponent_ng_template_61_Template_nb_select_selected<PERSON>hange_18_listener", "onDateTimeChange", "PreOrderComponent_ng_template_61_nb_option_19_Template", "PreOrderComponent_ng_template_61_nb_select_23_Template", "PreOrderComponent_ng_template_61_Template_input_ngModelChange_27_listener", "PreOrderComponent_ng_template_61_Template_input_ngModelChange_32_listener", "PreOrderComponent_ng_template_61_Template_input_ngModelChange_35_listener", "PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_41_listener", "CRemark", "PreOrderComponent_ng_template_61_Template_textarea_ngModelChange_45_listener", "CHouseRequirement", "PreOrderComponent_ng_template_61_Template_button_click_47_listener", "ref_r16", "dialogRef", "close", "PreOrderComponent_ng_template_61_Template_button_click_49_listener", "save", "houseHoldList", "length", "dateTimeList", "PreOrderComponent", "constructor", "allow", "dialogService", "houseService", "buildCaseService", "valid", "message", "_ultilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "buildCaseList", "allBuildCaseList", "caseList", "newHouseHightestList", "searchAppointment", "destroy", "ngOnInit", "initialList", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "Entries", "currentBuildCase", "listAppointmentRequest", "body", "CBuildCaseID", "getHourListAppointment", "getHouseAndFloorByBuildCaseId", "apiHouseGetListAppointmentsPost$Json", "subscribe", "TotalItems", "controllPagination", "totalItems", "lastIndex", "slice", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json", "buildCaseId", "console", "log", "groupByCDate", "data", "groupedData", "reduce", "acc", "entry", "push", "Object", "keys", "req", "apiHouseGetHourListAppointmentPost$Json", "for<PERSON>ach", "i", "split", "filter", "listCurrent", "filterOrders", "listAppointmentsByCustomer", "selectedData", "result", "filteredOrders", "order", "startsWith", "hourIndex", "indexOf", "splice", "selectedAppointment", "selectedDate", "find", "item", "sort", "a", "b", "undefined", "getUserBuildcaseList", "request", "getPreorderList", "params", "StatusCode", "showErrorMSG", "Message", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "search", "currentCustomerName", "currentCustomerPhone", "currentSelectDate", "selectedHouseHold", "Floors", "ref", "houseAndFloor", "next", "editChangepreoderID", "CChangePreOrderID", "o", "CNeedMail", "open", "addNewAppointment", "checkAppointmentExists", "formattedPreOrderDate", "some", "validation", "clear", "required", "window", "confirm", "apiHouseDeleteAppointmentPost$Json", "showSucessMSG", "saveEdit", "CID", "CStatus", "apiHouseEditAppointmentPost$Json", "errorMessages", "showErrorMSGs", "CBuildcaseID", "apiHouseCreateAppointmentPost$Json", "exportExcel", "apiHouseExportExcelListAppointmentsPost$Json", "downloadExcelFile", "FileByte", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "HouseService", "BuildCaseService", "i4", "ValidationHelper", "i5", "MessageService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PreOrderComponent_Template", "rf", "ctx", "PreOrderComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "PreOrderComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "PreOrderComponent_nb_option_12_Template", "PreOrderComponent_Template_input_ngModelChange_16_listener", "PreOrderComponent_Template_p_calendar_ngModelChange_21_listener", "PreOrderComponent_Template_input_ngModelChange_25_listener", "PreOrderComponent_Template_button_click_27_listener", "PreOrderComponent_Template_button_click_29_listener", "PreOrderComponent_Template_button_click_31_listener", "PreOrderComponent_tr_58_Template", "PreOrderComponent_Template_ngx_pagination_PageChange_60_listener", "PreOrderComponent_ng_template_61_Template", "ɵɵtemplateRefExtractor", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "DefaultValueAccessor", "NumberValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "i11", "BaseLabelDirective", "i12", "BooleanStringPipe", "i13", "FormatHourPipe", "i14", "Calendar", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\pre-order\\pre-order.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\pre-order\\pre-order.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ApiBuildCaseGetUserBuildCasePost$Json$Params } from 'src/services/api/fn/build-case/api-build-case-get-user-build-case-post-json';\r\nimport { ApiHouseGetListAppointmentsPost$Json$Params } from 'src/services/api/fn/house/api-house-get-list-appointments-post-json';\r\nimport { BuildCaseGetListReponse, CreateAppointmentArgs, GetAppoinmentRes, GetHouseAndFloorByBuildCaseIdRes } from 'src/services/api/models';\r\nimport { ApiHouseCreateAppointmentPost$Json$Params } from 'src/services/api/fn/house/api-house-create-appointment-post-json';\r\nimport { Observable, forkJoin, map, switchMap, tap, } from 'rxjs';\r\nimport { ApiHouseEditAppointmentPost$Json$Params } from 'src/services/api/fn/house/api-house-edit-appointment-post-json';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { CalendarModule } from 'primeng/calendar'\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n\r\nexport interface HourData {\r\n  CDate: string;\r\n  CHour: any[];\r\n}\r\n@Component({\r\n  selector: 'app-pre-order',\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, CalendarModule],\r\n  templateUrl: './pre-order.component.html',\r\n  styleUrls: ['./pre-order.component.scss']\r\n})\r\nexport class PreOrderComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private houseService: HouseService,\r\n    private buildCaseService: BuildCaseService,\r\n    private valid: ValidationHelper,\r\n    private message: MessageService,\r\n    private _ultilityService: UtilityService\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  buildCaseList: GetAppoinmentRes[] = [];\r\n  allBuildCaseList: GetAppoinmentRes[] = [];\r\n  caseList: BuildCaseGetListReponse[] = [];\r\n  houseHoldList: GetHouseAndFloorByBuildCaseIdRes[] = [];\r\n  newHouseHightestList: number[] | null | undefined = [];\r\n  dateTimeList: HourData[] = [];\r\n\r\n  appointment: CreateAppointmentArgs & {\r\n    CHouseRequirement?: string | null\r\n  } = {};\r\n  searchAppointment: ApiHouseGetListAppointmentsPost$Json$Params = {}\r\n\r\n  currentBuildCase: any;\r\n  currentSelectDate: string | undefined;\r\n  currentCustomerName: string | undefined;\r\n  currentCustomerPhone: string | undefined\r\n  isNewAppointment: boolean | undefined;\r\n  editChangepreoderID: number | undefined;\r\n  floorList: any = [];\r\n  hourList: any = [];\r\n  selectedAppointment: any\r\n  destroy = inject(DestroyRef)\r\n\r\n  override ngOnInit(): void {\r\n    this.isNewAppointment = false;\r\n    this.initialList();\r\n  }\r\n\r\n\r\n  initialList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(\r\n      switchMap(res => {\r\n        this.caseList = res.Entries ?? [];\r\n        this.currentBuildCase = res.Entries?.[0]?.cID;\r\n        let listAppointmentRequest: ApiHouseGetListAppointmentsPost$Json$Params = {\r\n          body: { CBuildCaseID: this.currentBuildCase }\r\n        };\r\n        this.getHourListAppointment();\r\n        this.getHouseAndFloorByBuildCaseId()\r\n        return this.houseService.apiHouseGetListAppointmentsPost$Json(listAppointmentRequest);\r\n      }),\r\n      takeUntilDestroyed(this.destroy)\r\n    ).subscribe(res => {\r\n      this.allBuildCaseList = res.Entries ?? [];\r\n      if (res.TotalItems || res.TotalItems === 0) {\r\n        this.controllPagination(res.TotalItems);\r\n      }\r\n    });\r\n  }\r\n\r\n  controllPagination(totalItems: number) {\r\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\r\n    this.totalRecords = totalItems;\r\n    let lastIndex = (this.totalRecords < this.pageIndex * this.pageSize) ? this.totalRecords + 1 : (this.pageIndex * this.pageSize);\r\n    this.buildCaseList = this.allBuildCaseList.slice(this.pageFirst - 1, lastIndex);\r\n  }\r\n\r\n  getHouseAndFloorByBuildCaseId(): Observable<any> {\r\n    return this.buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(\r\n      { buildCaseId: this.currentBuildCase }\r\n    )\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.Entries && res.Entries.length) {\r\n            this.houseHoldList = res.Entries;\r\n            console.log('getHouseAndFloorByBuildCaseId');\r\n          }\r\n        }),\r\n        takeUntilDestroyed(this.destroy)\r\n      );\r\n  }\r\n\r\n  groupByCDate(data: { CDate?: string; CHour?: number }[]): HourData[] {\r\n    const groupedData: { [key: string]: number[] } = data.reduce((acc: any, entry: any) => {\r\n      if (!acc[entry.CDate]) {\r\n        acc[entry.CDate] = [];\r\n      }\r\n      acc[entry.CDate].push(entry.CHour);\r\n      return acc;\r\n    }, {});\r\n\r\n    return Object.keys(groupedData).map(CDate => ({\r\n      CDate,\r\n      CHour: groupedData[CDate]\r\n    }));\r\n  }\r\n\r\n  listCurrent: any\r\n  getHourListAppointment(): Observable<any> {\r\n    const req = { CBuildCaseID: this.currentBuildCase };\r\n    return this.houseService.apiHouseGetHourListAppointmentPost$Json({ body: req })\r\n      .pipe(\r\n        map(res => {\r\n          res.Entries?.forEach(i => {\r\n            i.CDate = i.CDate?.split('T')[0];\r\n            i.CHour = i.CHour;\r\n          });\r\n          res.Entries = res.Entries?.filter((i: any) => i.CHour >= 9 && i.CHour <= 21);\r\n          return res\r\n        }),\r\n        tap(res => {\r\n          if (res.Entries && res.Entries.length) {\r\n            this.listCurrent = res.Entries;\r\n            this.dateTimeList = this.groupByCDate(res.Entries);\r\n          }\r\n        }),\r\n        takeUntilDestroyed(this.destroy)\r\n      );\r\n  }\r\n\r\n  filterOrders(listAppointmentsByCustomer: any[], selectedData: any) { //The same customer cannot book multiple house at the same time.\r\n    let result: any\r\n    if (selectedData && selectedData?.CHour && selectedData.CDate) {\r\n      result = selectedData.CHour ? [...selectedData.CHour] : []\r\n      // Bước 1: Lọc mảng A với các phần tử có CPreOrderDate trùng với B.CDate\r\n      const filteredOrders = listAppointmentsByCustomer.filter(order => order.CPreOrderDate.startsWith(selectedData.CDate));\r\n      // Bước 2: Loại bỏ các phần tử B.CHour trùng với các giá trị CHour trong mảng filteredOrders\r\n      filteredOrders.forEach(order => {\r\n        const hourIndex = result.indexOf(order.CHour);\r\n        if (hourIndex !== -1) {\r\n          result.splice(hourIndex, 1); // Xóa phần tử trùng khỏi B.CHour\r\n        }\r\n      });\r\n      if (this.selectedAppointment.CPreOrderDate.startsWith(selectedData.CDate) && this.selectedAppointment.CHour) {\r\n        result = [...result, this.selectedAppointment.CHour]\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  onDateTimeChange(selectedDate: string) {\r\n    const selectedData: HourData | any = this.dateTimeList.find(item => item.CDate === selectedDate);\r\n    this.hourList = selectedData ? selectedData.CHour.sort((a: number, b: number) => a - b) : [];\r\n    this.hourList = this.filterOrders(this.listAppointmentsByCustomer, selectedData)\r\n    this.appointment.CHour = undefined;\r\n  }\r\n\r\n  getUserBuildcaseList() {\r\n    let request: ApiBuildCaseGetUserBuildCasePost$Json$Params = {};\r\n    request.body = {}\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n      this.caseList = res.Entries ?? [];\r\n    })\r\n  }\r\n\r\n  getPreorderList(params: ApiHouseGetListAppointmentsPost$Json$Params) {\r\n    let request: ApiHouseGetListAppointmentsPost$Json$Params = {\r\n      body: {\r\n        CBuildCaseID: params.body!.CBuildCaseID,\r\n        CCustomerName: params.body!.CCustomerName,\r\n        CPhone: params.body && params.body.CPhone,\r\n        CPreOrderDate: params.body && params.body.CPreOrderDate?.split(\"T\")[0],\r\n      }\r\n    };\r\n    this.houseService.apiHouseGetListAppointmentsPost$Json(request)\r\n      .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.allBuildCaseList = res.Entries ?? [];\r\n          if (res.TotalItems || res.TotalItems === 0) {\r\n            this.controllPagination(res.TotalItems);\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      })\r\n  }\r\n  listAppointmentsByCustomer: any\r\n\r\n  formatDate(date: any) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  search() {\r\n    this.searchAppointment = {\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CCustomerName: this.currentCustomerName,\r\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\r\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //=== '全部' ? undefined : this.currentSelectDate\r\n      }\r\n    }\r\n    this.getPreorderList(this.searchAppointment)\r\n  }\r\n\r\n\r\n  onHouseHoldChange(selectedHouseHold: string) {\r\n    const selectedData = this.houseHoldList.find(item => item.CHouseHold === selectedHouseHold);\r\n    this.floorList = selectedData ? selectedData.Floors : [];\r\n    this.appointment.CFloor = undefined; // Reset selected floor\r\n  }\r\n\r\n  edit(ref: any, item: any) {\r\n    forkJoin({\r\n      hourList: this.getHourListAppointment(),\r\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\r\n    }).subscribe({\r\n      next: () => {\r\n        this.selectedAppointment = item\r\n        this.isNewAppointment = false;\r\n        this.editChangepreoderID = item.CChangePreOrderID;\r\n        let selectedHouseHold = this.houseHoldList.find(i => i.CHouseHold === item.CHouseHold);\r\n        this.appointment.CHouseHold = selectedHouseHold?.CHouseHold\r\n        this.floorList = selectedHouseHold?.Floors\r\n        // let currentTemp = this.listCurrent.find((i: any) => {\r\n        //   return (i.CDate === item.CPreOrderDate.split(\"T\")[0] && i.CHour === item.CHour)\r\n        // });\r\n        // if (!currentTemp) {\r\n        this.dateTimeList = this.groupByCDate([...this.listCurrent])//, { CDate: item.CPreOrderDate.split(\"T\")[0], CHour: item.CHour }])\r\n        // }\r\n        this.listAppointmentsByCustomer = this.allBuildCaseList.filter((o: any) => o.CCustomerName == item.CCustomerName)\r\n\r\n        this.onDateTimeChange(item.CPreOrderDate.split(\"T\")[0])\r\n        this.appointment = {\r\n          CFloor: item.CFloor,\r\n          CHasDesigner: item.CHasDesigner,\r\n          CHour: item.CHour,\r\n          CHouseHold: item.CHouseHold,\r\n          CPeoples: item.CPeoples,\r\n          CPreOrderDate: item.CPreOrderDate.split(\"T\")[0],\r\n          CRemark: item.CRemark,\r\n          CNeedMail: item.CNeedMail,\r\n          CHouseRequirement: item.CHouseRequirement\r\n        }\r\n        this.dialogService.open(ref);\r\n      },\r\n    });\r\n  }\r\n\r\n  addNewAppointment(ref: any) {\r\n    forkJoin({\r\n      hourList: this.getHourListAppointment(),\r\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\r\n    }).subscribe({\r\n      next: () => {\r\n        this.isNewAppointment = true;\r\n        this.appointment = {};\r\n        this.dialogService.open(ref);\r\n      },\r\n    });\r\n  }\r\n\r\n  checkAppointmentExists(): boolean {\r\n    const formattedPreOrderDate = this.appointment.CPreOrderDate + 'T00:00:00';\r\n    return this.listAppointmentsByCustomer.some((appointment: any) =>\r\n      appointment.CPreOrderDate === formattedPreOrderDate &&\r\n      appointment.CHour === this.appointment.CHour\r\n    );\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required(`戶別`, this.appointment.CHouseHold);\r\n    this.valid.required(`樓層`, this.appointment.CFloor);\r\n    this.valid.required(`日期`, this.appointment.CPreOrderDate);\r\n    this.valid.required(`時段`, this.appointment.CHour);\r\n    this.valid.required(`出席人數`, this.appointment.CPeoples);\r\n    this.valid.required(`設計師出席`, this.appointment.CHasDesigner);\r\n    // console.log('this.listAppointmentsByCustomer', this.listAppointmentsByCustomer.filter((item: any)=> item.CHouseHold ==this.appointment.CHouseHold ));\r\n    // console.log('this.appointment', this.appointment);\r\n\r\n    // if (this.listAppointmentsByCustomer && this.listAppointmentsByCustomer.length) {\r\n    //   if (this.checkAppointmentExists()) {\r\n    //     this.valid.addErrorMessage('無法選擇尚未被加入可 預約時段的日期及時段');\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  deleteAppointment(item: any, i: number) {\r\n    if (window.confirm(`確定要刪除【項目${this.pageFirst + i}】?`)) {\r\n      this.houseService.apiHouseDeleteAppointmentPost$Json({ body: item.CChangePreOrderID })\r\n        .pipe(takeUntilDestroyed(this.destroy))\r\n        .subscribe(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n            this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  saveEdit(ref: any) {\r\n    let request: ApiHouseEditAppointmentPost$Json$Params = {\r\n      body: {\r\n        CID: this.editChangepreoderID,\r\n        CHasDesigner: this.appointment.CHasDesigner,\r\n        CHour: this.appointment.CHour,\r\n        CPeoples: this.appointment.CPeoples,\r\n        CPreOrderDate: this.appointment.CPreOrderDate,\r\n        CRemark: this.appointment.CRemark,\r\n        CStatus: this.selectedAppointment.CStatus\r\n      }\r\n    }\r\n\r\n    this.houseService.apiHouseEditAppointmentPost$Json(request)\r\n      .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n          ref.close()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let request: ApiHouseCreateAppointmentPost$Json$Params;\r\n\r\n    request = {\r\n      body: {\r\n        ...this.appointment,\r\n        CBuildcaseID: this.currentBuildCase\r\n      }\r\n    };\r\n\r\n    if (this.isNewAppointment) {\r\n      this.houseService.apiHouseCreateAppointmentPost$Json(request)\r\n        .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`執行成功`);\r\n            this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n            ref.close()\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        })\r\n    } else {\r\n      this.saveEdit(ref)\r\n    }\r\n  }\r\n\r\n  exportExcel() {\r\n    this.houseService.apiHouseExportExcelListAppointmentsPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CCustomerName: this.currentCustomerName,\r\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\r\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined//this.currentSelectDate === '全部' ? undefined : this.currentSelectDate\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(res.Entries?.FileByte!, '預約')\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <p class=\"mb-4\" style=\"color: #818181;font-weight: 700;\">您可於此檢視/編輯目前之預約資料，亦可為客戶新增一筆資料（但每個戶別只能有一筆預約資料）。</p>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select [placeholder]=\"'請選擇建案'\" [(ngModel)]=\"currentBuildCase\"\r\n            (selectedChange)=\"search()\">\r\n            <nb-option *ngFor=\"let item of caseList\" [value]=\"item.cID\">{{item.CBuildCaseName}}</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"name\" class=\"label mr-2\">客戶姓名</label>\r\n          <input type=\"text\" nbInput placeholder=\"請輸入客戶姓名\" [(ngModel)]=\"currentCustomerName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"preOrderDate\" class=\"label mr-2\">預約日期</label>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\" class=\"!w-full\"\r\n            inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"currentSelectDate\"></p-calendar>\r\n        </div>\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"phone\" class=\"label mr-2\">客戶電話</label>\r\n          <input type=\"text\" nbInput placeholder=\"請輸入客戶電話\" [(ngModel)]=\"currentCustomerPhone\">\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"flex items-center justify-end\">\r\n      <button class=\"btn btn-info mr-3\" (click)=\"search()\">查詢</button>\r\n      <button class=\"btn btn-success mr-3\" (click)=\"addNewAppointment(dialog)\">新增預約</button>\r\n      <button class=\"btn btn-info\" (click)=\"exportExcel()\">匯出</button>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th style=\"min-width: 5%\">戶型</th>\r\n            <th style=\"min-width: 5%\">樓層</th>\r\n            <th style=\"min-width: 10%\">客戶姓名</th>\r\n            <th style=\"min-width: 10%\">客戶電話</th>\r\n            <th style=\"min-width: 10%\">預約日期</th>\r\n            <th style=\"min-width: 10%\">預約時段</th>\r\n            <th style=\"min-width: 10%\">出席人數</th>\r\n            <th style=\"min-width: 15%\">設計師出席</th>\r\n            <th style=\"min-width: 10%\">填單時間</th>         \r\n            <th style=\"min-width: 15%\" class=\"text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of buildCaseList; let i = index\">\r\n            <td style=\"min-width: 5%\">{{ item.CHouseHold }}</td>\r\n            <td style=\"min-width: 5%\">{{ item.CFloor }}</td>\r\n            <td style=\"min-width: 10%\">{{ item.CCustomerName }}</td>\r\n            <td style=\"min-width: 10%\">{{ item.CPhone }}</td>\r\n            <td style=\"min-width: 10%\">{{ item.CPreOrderDate | date: 'yyyy/MM/dd' }}</td>\r\n            <td style=\"min-width: 10%\">{{ item.CHour | hour }}</td>\r\n            <td style=\"min-width: 10%\" class=\"text-center\">{{ item.CPeoples }}</td>\r\n            <td style=\"min-width: 15%\" class=\"text-center\">{{ item.CHasDesigner | booleanString }}</td>\r\n            <td style=\"min-width: 10%\">{{ item.CCreateDT | date: 'yyyy/MM/dd' }}</td>\r\n            <td style=\"min-width: 15%\" class=\"text-center\">\r\n              <button type=\"button\" class=\"btn btn-outline-success m-1 btn-sm\" (click)=\"edit(dialog, item)\">\r\n                <i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n              <button type=\"button\" class=\"btn btn-outline-danger m-1 btn-sm\" (click)=\"deleteAppointment(item, i)\">\r\n                <i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n      (PageChange)=\"controllPagination(totalRecords)\"></ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"max-height: 95vh; min-width: 400px;\">\r\n    <nb-card-header>{{ isNewAppointment ? '新增' : '編輯'}}</nb-card-header>\r\n    <nb-card-body>\r\n      <p class=\"mb-4\" style=\"color: #818181;font-weight: 700;\" [hidden]=\"!isNewAppointment\">\r\n        您可於此為客戶建立一筆預約，但僅限於在選樣開放時段內之客戶。</p>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"distinguish\" baseLabel class=\"required-field\">戶別</label>\r\n        <nb-select placeholder=\"請選擇戶別\" [(ngModel)]=\"appointment.CHouseHold\" (selectedChange)=\"onHouseHoldChange($event)\" >\r\n          <nb-option [disabled]=\"!isNewAppointment\" *ngFor=\"let item of houseHoldList\" [value]=\"item.CHouseHold\">{{ item.CHouseHold }}</nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"floor\" baseLabel class=\"required-field\">樓層</label>\r\n        <nb-select placeholder=\"請選擇樓層\" *ngIf=\"floorList.length > 0\" [(ngModel)]=\"appointment.CFloor\" >\r\n          <nb-option *ngFor=\"let floor of floorList\" [value]=\"floor\" [disabled]=\"!isNewAppointment\">{{ floor }}</nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"date\" baseLabel class=\"required-field\">日期</label>\r\n        <nb-select placeholder=\"請選擇日期\" [(ngModel)]=\"appointment.CPreOrderDate\" (selectedChange)=\"onDateTimeChange($event)\" >\r\n          <nb-option *ngFor=\"let item of dateTimeList\" [value]=\"item.CDate\">{{ item.CDate | date: 'yyyy/MM/dd'}}</nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"timeline\" baseLabel class=\"required-field\">時段</label>\r\n        <nb-select placeholder=\"請選擇時段\" [(ngModel)]=\"appointment.CHour\" \r\n          *ngIf=\"hourList && hourList.length > 0\">\r\n          <nb-option *ngFor=\"let hour of hourList\" [value]=\"hour\">{{ hour | hour }}</nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"headcount\" baseLabel class=\"required-field\">出席人數</label>\r\n        <input type=\"number\" nbInput [(ngModel)]=\"appointment.CPeoples\">\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"designer\" baseLabel class=\"required-field\">設計師出席</label>\r\n        <div>\r\n          <input type=\"radio\" id=\"designerAttend\" name=\"attend\" [value]=\"true\" [(ngModel)]=\"appointment.CHasDesigner\">\r\n          <label for=\"designerAttend\" class=\"label ml-1\">是</label>\r\n          <input type=\"radio\" id=\"designerAbsent\" name=\"attend\" [value]=\"false\" [(ngModel)]=\"appointment.CHasDesigner\">\r\n          <label for=\"designerAbsent\" class=\"label ml-1\">否</label>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"align-self-start\">備註事項</label>\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none;\"\r\n          [(ngModel)]=\"appointment.CRemark\"></textarea>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"remark\" baseLabel class=\"align-self-start\">變更需求</label>\r\n        <textarea name=\"remark\" id=\"remark\" rows=\"5\" nbInput style=\"resize: none;\" disabled=\"true\"\r\n          [(ngModel)]=\"appointment.CHouseRequirement\"></textarea>\r\n      </div>    \r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success\" (click)=\"save(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAoBA,UAAU,EAAUC,MAAM,QAAQ,eAAe;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,kBAAkB,QAAQ,4BAA4B;AAM/D,SAAqBC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAS,MAAM;AAMjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;;;;;ICJvDC,EAAA,CAAAC,cAAA,oBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAtDH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,EAAuB;IAAvBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,cAAA,CAAuB;;;;;;IA4CnFT,EADF,CAAAC,cAAA,SAAsD,aAC1B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,aAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA6C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7EH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAuB;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,cAA+C;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvEH,EAAA,CAAAC,cAAA,cAA+C;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3FH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEvEH,EADF,CAAAC,cAAA,cAA+C,kBACiD;IAA7BD,EAAA,CAAAU,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAC,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAK,IAAA,CAAAH,SAAA,EAAAN,OAAA,CAAkB;IAAA,EAAC;IAC3FZ,EAAA,CAAAsB,SAAA,aAAgC;IAAAtB,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAAqG;IAArCD,EAAA,CAAAU,UAAA,mBAAAa,0DAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAF,OAAA,GAAAY,MAAA,CAAAT,SAAA;MAAA,MAAAU,IAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAAV,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAW,iBAAA,CAAAf,OAAA,EAAAa,IAAA,CAA0B;IAAA,EAAC;IAClGzB,EAAA,CAAAsB,SAAA,aAAqC;IAAAtB,EAAA,CAAAE,MAAA,oBAAE;IAE7CF,EAF6C,CAAAG,YAAA,EAAS,EAC/C,EACF;;;;IAfuBH,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAgB,UAAA,CAAqB;IACrB5B,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAiB,MAAA,CAAiB;IAChB7B,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAkB,aAAA,CAAwB;IACxB9B,EAAA,CAAAO,SAAA,GAAiB;IAAjBP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAmB,MAAA,CAAiB;IACjB/B,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAgC,WAAA,QAAApB,OAAA,CAAAqB,aAAA,gBAA6C;IAC7CjC,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAkC,WAAA,SAAAtB,OAAA,CAAAuB,KAAA,EAAuB;IACHnC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAQ,iBAAA,CAAAI,OAAA,CAAAwB,QAAA,CAAmB;IACnBpC,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAkC,WAAA,SAAAtB,OAAA,CAAAyB,YAAA,EAAuC;IAC3DrC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAgC,WAAA,SAAApB,OAAA,CAAA0B,SAAA,gBAAyC;;;;;IA2BtEtC,EAAA,CAAAC,cAAA,oBAAuG;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAA3DH,EAAlE,CAAAI,UAAA,cAAAY,MAAA,CAAAuB,gBAAA,CAA8B,UAAAC,QAAA,CAAAZ,UAAA,CAA6D;IAAC5B,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAgC,QAAA,CAAAZ,UAAA,CAAqB;;;;;IAM5H5B,EAAA,CAAAC,cAAA,oBAA0F;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAAtDH,EAAhB,CAAAI,UAAA,UAAAqC,SAAA,CAAe,cAAAzB,MAAA,CAAAuB,gBAAA,CAA+B;IAACvC,EAAA,CAAAO,SAAA,EAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAiC,SAAA,CAAW;;;;;;IADvGzC,EAAA,CAAAC,cAAA,oBAA8F;IAAlCD,EAAA,CAAA0C,gBAAA,2BAAAC,0FAAAC,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,IAAA;MAAA,MAAA7B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAlB,MAAA,EAAAe,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAlB,MAAA,GAAAe,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAgC;IAC1F5C,EAAA,CAAAgD,UAAA,IAAAC,kEAAA,wBAA0F;IAC5FjD,EAAA,CAAAG,YAAA,EAAY;;;;IAFgDH,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAlB,MAAA,CAAgC;IAC7D7B,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAmC,SAAA,CAAY;;;;;IAMzCnD,EAAA,CAAAC,cAAA,oBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAArEH,EAAA,CAAAI,UAAA,UAAAgD,QAAA,CAAAC,KAAA,CAAoB;IAACrD,EAAA,CAAAO,SAAA,EAAoC;IAApCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAgC,WAAA,OAAAoB,QAAA,CAAAC,KAAA,gBAAoC;;;;;IAOtGrD,EAAA,CAAAC,cAAA,oBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAiB;;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAA5CH,EAAA,CAAAI,UAAA,UAAAkD,QAAA,CAAc;IAACtD,EAAA,CAAAO,SAAA,EAAiB;IAAjBP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAkC,WAAA,OAAAoB,QAAA,EAAiB;;;;;;IAF3EtD,EAAA,CAAAC,cAAA,oBAC0C;IADXD,EAAA,CAAA0C,gBAAA,2BAAAa,0FAAAX,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA2C,IAAA;MAAA,MAAAxC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAZ,KAAA,EAAAS,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAZ,KAAA,GAAAS,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAA+B;IAE5D5C,EAAA,CAAAgD,UAAA,IAAAS,kEAAA,uBAAwD;IAC1DzD,EAAA,CAAAG,YAAA,EAAY;;;;IAHmBH,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAZ,KAAA,CAA+B;IAEhCnC,EAAA,CAAAO,SAAA,EAAW;IAAXP,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA0C,QAAA,CAAW;;;;;;IA1B7C1D,EADF,CAAAC,cAAA,kBAAqD,qBACnC;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAElEH,EADF,CAAAC,cAAA,mBAAc,YAC0E;IACpFD,EAAA,CAAAE,MAAA,4LAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAElCH,EADF,CAAAC,cAAA,aAAkD,gBACU;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAC,cAAA,oBAAkH;IAAnFD,EAAA,CAAA0C,gBAAA,2BAAAiB,6EAAAf,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAnB,UAAA,EAAAgB,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAnB,UAAA,GAAAgB,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAoC;IAAC5C,EAAA,CAAAU,UAAA,4BAAAmD,8EAAAjB,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAAkBJ,MAAA,CAAA8C,iBAAA,CAAAlB,MAAA,CAAyB;IAAA,EAAC;IAC9G5C,EAAA,CAAAgD,UAAA,KAAAe,sDAAA,wBAAuG;IAE3G/D,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAgD,UAAA,KAAAgB,sDAAA,wBAA8F;IAGhGhE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACG;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAC,cAAA,qBAAoH;IAArFD,EAAA,CAAA0C,gBAAA,2BAAAuB,8EAAArB,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAd,aAAA,EAAAW,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAd,aAAA,GAAAW,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAuC;IAAC5C,EAAA,CAAAU,UAAA,4BAAAwD,+EAAAtB,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAAkBJ,MAAA,CAAAmD,gBAAA,CAAAvB,MAAA,CAAwB;IAAA,EAAC;IAChH5C,EAAA,CAAAgD,UAAA,KAAAoB,sDAAA,uBAAkE;IAEtEpE,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACO;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAgD,UAAA,KAAAqB,sDAAA,wBAC0C;IAG5CrE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACQ;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpEH,EAAA,CAAAC,cAAA,iBAAgE;IAAnCD,EAAA,CAAA0C,gBAAA,2BAAA4B,0EAAA1B,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAX,QAAA,EAAAQ,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAX,QAAA,GAAAQ,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAkC;IACjE5C,EADE,CAAAG,YAAA,EAAgE,EAC5D;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACO;IAAAD,EAAA,CAAAE,MAAA,sCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAElEH,EADF,CAAAC,cAAA,WAAK,iBACyG;IAAvCD,EAAA,CAAA0C,gBAAA,2BAAA6B,0EAAA3B,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAV,YAAA,EAAAO,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAV,YAAA,GAAAO,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAsC;IAA3G5C,EAAA,CAAAG,YAAA,EAA4G;IAC5GH,EAAA,CAAAC,cAAA,iBAA+C;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAAC,cAAA,iBAA6G;IAAvCD,EAAA,CAAA0C,gBAAA,2BAAA8B,0EAAA5B,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAAV,YAAA,EAAAO,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAAV,YAAA,GAAAO,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAsC;IAA5G5C,EAAA,CAAAG,YAAA,EAA6G;IAC7GH,EAAA,CAAAC,cAAA,iBAA+C;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAEpDF,EAFoD,CAAAG,YAAA,EAAQ,EACpD,EACF;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACO;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAC,cAAA,oBACoC;IAAlCD,EAAA,CAAA0C,gBAAA,2BAAA+B,6EAAA7B,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAA2B,OAAA,EAAA9B,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAA2B,OAAA,GAAA9B,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAAiC;IACrC5C,EADsC,CAAAG,YAAA,EAAW,EAC3C;IAEJH,EADF,CAAAC,cAAA,cAAkD,iBACO;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAC,cAAA,oBAC8C;IAA5CD,EAAA,CAAA0C,gBAAA,2BAAAiC,6EAAA/B,MAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAA+C,GAAA;MAAA,MAAA5C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA8C,kBAAA,CAAA9B,MAAA,CAAA+B,WAAA,CAAA6B,iBAAA,EAAAhC,MAAA,MAAA5B,MAAA,CAAA+B,WAAA,CAAA6B,iBAAA,GAAAhC,MAAA;MAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;IAAA,EAA2C;IAEjD5C,EAFkD,CAAAG,YAAA,EAAW,EACrD,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACM;IAAtBD,EAAA,CAAAU,UAAA,mBAAAmE,mEAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAa,aAAA,CAAA+C,GAAA,EAAAmB,SAAA;MAAA,OAAA/E,EAAA,CAAAoB,WAAA,CAAS0D,OAAA,CAAAE,KAAA,EAAW;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrEH,EAAA,CAAAC,cAAA,kBAAoD;IAApBD,EAAA,CAAAU,UAAA,mBAAAuE,mEAAA;MAAA,MAAAH,OAAA,GAAA9E,EAAA,CAAAa,aAAA,CAAA+C,GAAA,EAAAmB,SAAA;MAAA,MAAA/D,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAoB,WAAA,CAASJ,MAAA,CAAAkE,IAAA,CAAAJ,OAAA,CAAS;IAAA,EAAC;IAAC9E,EAAA,CAAAE,MAAA,oBAAE;IAE1DF,EAF0D,CAAAG,YAAA,EAAS,EAChD,EACT;;;;IAzDQH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,iBAAA,CAAAQ,MAAA,CAAAuB,gBAAA,mCAAmC;IAEQvC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAuB,gBAAA,CAA4B;IAIpDvC,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAnB,UAAA,CAAoC;IACN5B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAmE,aAAA,CAAgB;IAK7CnF,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAmC,SAAA,CAAAiC,MAAA,KAA0B;IAM3BpF,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAd,aAAA,CAAuC;IACxCjC,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAqE,YAAA,CAAe;IAM1CrF,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAA0C,QAAA,IAAA1C,MAAA,CAAA0C,QAAA,CAAA0B,MAAA,KAAqC;IAMXpF,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAX,QAAA,CAAkC;IAKPpC,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,eAAc;IAACJ,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAV,YAAA,CAAsC;IAErDrC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAI,UAAA,gBAAe;IAACJ,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAAV,YAAA,CAAsC;IAO5GrC,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAA2B,OAAA,CAAiC;IAKjC1E,EAAA,CAAAO,SAAA,GAA2C;IAA3CP,EAAA,CAAAkD,gBAAA,YAAAlC,MAAA,CAAA+B,WAAA,CAAA6B,iBAAA,CAA2C;;;ADxGrD,OAAM,MAAOU,iBAAkB,SAAQvF,aAAa;EAClDwF,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,YAA0B,EAC1BC,gBAAkC,EAClCC,KAAuB,EACvBC,OAAuB,EACvBC,gBAAgC;IAExC,KAAK,CAACN,KAAK,CAAC;IARO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAKjB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAuB,EAAE;IACtC,KAAAC,gBAAgB,GAAuB,EAAE;IACzC,KAAAC,QAAQ,GAA8B,EAAE;IACxC,KAAAlB,aAAa,GAAuC,EAAE;IACtD,KAAAmB,oBAAoB,GAAgC,EAAE;IACtD,KAAAjB,YAAY,GAAe,EAAE;IAE7B,KAAAtC,WAAW,GAEP,EAAE;IACN,KAAAwD,iBAAiB,GAAgD,EAAE;IAQnE,KAAApD,SAAS,GAAQ,EAAE;IACnB,KAAAO,QAAQ,GAAQ,EAAE;IAElB,KAAA8C,OAAO,GAAGlH,MAAM,CAACD,UAAU,CAAC;EA5B5B;EA8BSoH,QAAQA,CAAA;IACf,IAAI,CAAClE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACmE,WAAW,EAAE;EACpB;EAGAA,WAAWA,CAAA;IACT,IAAI,CAACf,gBAAgB,CAACgB,qCAAqC,CAAC,EAAE,CAAC,CAACC,IAAI,CAClEjH,SAAS,CAACkH,GAAG,IAAG;MACd,IAAI,CAACR,QAAQ,GAAGQ,GAAG,CAACC,OAAO,IAAI,EAAE;MACjC,IAAI,CAACC,gBAAgB,GAAGF,GAAG,CAACC,OAAO,GAAG,CAAC,CAAC,EAAExG,GAAG;MAC7C,IAAI0G,sBAAsB,GAAgD;QACxEC,IAAI,EAAE;UAAEC,YAAY,EAAE,IAAI,CAACH;QAAgB;OAC5C;MACD,IAAI,CAACI,sBAAsB,EAAE;MAC7B,IAAI,CAACC,6BAA6B,EAAE;MACpC,OAAO,IAAI,CAAC1B,YAAY,CAAC2B,oCAAoC,CAACL,sBAAsB,CAAC;IACvF,CAAC,CAAC,EACFxH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CACjC,CAACc,SAAS,CAACT,GAAG,IAAG;MAChB,IAAI,CAACT,gBAAgB,GAAGS,GAAG,CAACC,OAAO,IAAI,EAAE;MACzC,IAAID,GAAG,CAACU,UAAU,IAAIV,GAAG,CAACU,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACC,kBAAkB,CAACX,GAAG,CAACU,UAAU,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAACC,UAAkB;IACnC,IAAI,CAAC1B,SAAS,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,CAAC,IAAI,IAAI,CAACD,QAAQ,GAAG,CAAC;IACzD,IAAI,CAACE,YAAY,GAAGuB,UAAU;IAC9B,IAAIC,SAAS,GAAI,IAAI,CAACxB,YAAY,GAAG,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,GAAI,IAAI,CAACE,YAAY,GAAG,CAAC,GAAI,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAS;IAC/H,IAAI,CAACG,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACuB,KAAK,CAAC,IAAI,CAAC5B,SAAS,GAAG,CAAC,EAAE2B,SAAS,CAAC;EACjF;EAEAN,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAACzB,gBAAgB,CAACiC,kDAAkD,CAC7E;MAAEC,WAAW,EAAE,IAAI,CAACd;IAAgB,CAAE,CACvC,CACEH,IAAI,CACHhH,GAAG,CAACiH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC1B,MAAM,EAAE;QACrC,IAAI,CAACD,aAAa,GAAG0B,GAAG,CAACC,OAAO;QAChCgB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,EACFvI,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CACjC;EACL;EAEAwB,YAAYA,CAACC,IAA0C;IACrD,MAAMC,WAAW,GAAgCD,IAAI,CAACE,MAAM,CAAC,CAACC,GAAQ,EAAEC,KAAU,KAAI;MACpF,IAAI,CAACD,GAAG,CAACC,KAAK,CAAChF,KAAK,CAAC,EAAE;QACrB+E,GAAG,CAACC,KAAK,CAAChF,KAAK,CAAC,GAAG,EAAE;MACvB;MACA+E,GAAG,CAACC,KAAK,CAAChF,KAAK,CAAC,CAACiF,IAAI,CAACD,KAAK,CAAClG,KAAK,CAAC;MAClC,OAAOiG,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAEN,OAAOG,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAACxI,GAAG,CAAC2D,KAAK,KAAK;MAC5CA,KAAK;MACLlB,KAAK,EAAE+F,WAAW,CAAC7E,KAAK;KACzB,CAAC,CAAC;EACL;EAGA8D,sBAAsBA,CAAA;IACpB,MAAMsB,GAAG,GAAG;MAAEvB,YAAY,EAAE,IAAI,CAACH;IAAgB,CAAE;IACnD,OAAO,IAAI,CAACrB,YAAY,CAACgD,uCAAuC,CAAC;MAAEzB,IAAI,EAAEwB;IAAG,CAAE,CAAC,CAC5E7B,IAAI,CACHlH,GAAG,CAACmH,GAAG,IAAG;MACRA,GAAG,CAACC,OAAO,EAAE6B,OAAO,CAACC,CAAC,IAAG;QACvBA,CAAC,CAACvF,KAAK,GAAGuF,CAAC,CAACvF,KAAK,EAAEwF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChCD,CAAC,CAACzG,KAAK,GAAGyG,CAAC,CAACzG,KAAK;MACnB,CAAC,CAAC;MACF0E,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,EAAEgC,MAAM,CAAEF,CAAM,IAAKA,CAAC,CAACzG,KAAK,IAAI,CAAC,IAAIyG,CAAC,CAACzG,KAAK,IAAI,EAAE,CAAC;MAC5E,OAAO0E,GAAG;IACZ,CAAC,CAAC,EACFjH,GAAG,CAACiH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAAC1B,MAAM,EAAE;QACrC,IAAI,CAAC2D,WAAW,GAAGlC,GAAG,CAACC,OAAO;QAC9B,IAAI,CAACzB,YAAY,GAAG,IAAI,CAAC2C,YAAY,CAACnB,GAAG,CAACC,OAAO,CAAC;MACpD;IACF,CAAC,CAAC,EACFtH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CACjC;EACL;EAEAwC,YAAYA,CAACC,0BAAiC,EAAEC,YAAiB;IAC/D,IAAIC,MAAW;IACf,IAAID,YAAY,IAAIA,YAAY,EAAE/G,KAAK,IAAI+G,YAAY,CAAC7F,KAAK,EAAE;MAC7D8F,MAAM,GAAGD,YAAY,CAAC/G,KAAK,GAAG,CAAC,GAAG+G,YAAY,CAAC/G,KAAK,CAAC,GAAG,EAAE;MAC1D;MACA,MAAMiH,cAAc,GAAGH,0BAA0B,CAACH,MAAM,CAACO,KAAK,IAAIA,KAAK,CAACpH,aAAa,CAACqH,UAAU,CAACJ,YAAY,CAAC7F,KAAK,CAAC,CAAC;MACrH;MACA+F,cAAc,CAACT,OAAO,CAACU,KAAK,IAAG;QAC7B,MAAME,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAACH,KAAK,CAAClH,KAAK,CAAC;QAC7C,IAAIoH,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBJ,MAAM,CAACM,MAAM,CAACF,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MACF,IAAI,IAAI,CAACG,mBAAmB,CAACzH,aAAa,CAACqH,UAAU,CAACJ,YAAY,CAAC7F,KAAK,CAAC,IAAI,IAAI,CAACqG,mBAAmB,CAACvH,KAAK,EAAE;QAC3GgH,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAE,IAAI,CAACO,mBAAmB,CAACvH,KAAK,CAAC;MACtD;IACF;IACA,OAAOgH,MAAM;EACf;EAEAhF,gBAAgBA,CAACwF,YAAoB;IACnC,MAAMT,YAAY,GAAmB,IAAI,CAAC7D,YAAY,CAACuE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxG,KAAK,KAAKsG,YAAY,CAAC;IAChG,IAAI,CAACjG,QAAQ,GAAGwF,YAAY,GAAGA,YAAY,CAAC/G,KAAK,CAAC2H,IAAI,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;IAC5F,IAAI,CAACtG,QAAQ,GAAG,IAAI,CAACsF,YAAY,CAAC,IAAI,CAACC,0BAA0B,EAAEC,YAAY,CAAC;IAChF,IAAI,CAACnG,WAAW,CAACZ,KAAK,GAAG8H,SAAS;EACpC;EAEAC,oBAAoBA,CAAA;IAClB,IAAIC,OAAO,GAAiD,EAAE;IAC9DA,OAAO,CAAClD,IAAI,GAAG,EAAE;IACjB,IAAI,CAACtB,gBAAgB,CAACgB,qCAAqC,CAACwD,OAAO,CAAC,CAACvD,IAAI,CAACpH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CAAC,CAACc,SAAS,CAACT,GAAG,IAAG;MAC1H,IAAI,CAACR,QAAQ,GAAGQ,GAAG,CAACC,OAAO,IAAI,EAAE;IACnC,CAAC,CAAC;EACJ;EAEAsD,eAAeA,CAACC,MAAmD;IACjE,IAAIF,OAAO,GAAgD;MACzDlD,IAAI,EAAE;QACJC,YAAY,EAAEmD,MAAM,CAACpD,IAAK,CAACC,YAAY;QACvCpF,aAAa,EAAEuI,MAAM,CAACpD,IAAK,CAACnF,aAAa;QACzCC,MAAM,EAAEsI,MAAM,CAACpD,IAAI,IAAIoD,MAAM,CAACpD,IAAI,CAAClF,MAAM;QACzCE,aAAa,EAAEoI,MAAM,CAACpD,IAAI,IAAIoD,MAAM,CAACpD,IAAI,CAAChF,aAAa,EAAE4G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;KAExE;IACD,IAAI,CAACnD,YAAY,CAAC2B,oCAAoC,CAAC8C,OAAO,CAAC,CAC5DvD,IAAI,CAACpH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CAAC,CAACc,SAAS,CAACT,GAAG,IAAG;MACtD,IAAIA,GAAG,CAACyD,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClE,gBAAgB,GAAGS,GAAG,CAACC,OAAO,IAAI,EAAE;QACzC,IAAID,GAAG,CAACU,UAAU,IAAIV,GAAG,CAACU,UAAU,KAAK,CAAC,EAAE;UAC1C,IAAI,CAACC,kBAAkB,CAACX,GAAG,CAACU,UAAU,CAAC;QACzC;MACF,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAAC0E,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACN;EAGAC,UAAUA,CAACC,IAAS;IAClB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC5E,iBAAiB,GAAG;MACvBU,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACH,gBAAgB;QACnCjF,aAAa,EAAE,IAAI,CAACsJ,mBAAmB;QACvCrJ,MAAM,EAAE,IAAI,CAACsJ,oBAAoB,KAAKpB,SAAS,GAAG,IAAI,CAACoB,oBAAoB,GAAGpB,SAAS;QACvFhI,aAAa,EAAE,IAAI,CAACqJ,iBAAiB,GAAG,IAAI,CAACb,UAAU,CAAC,IAAI,CAACa,iBAAiB,CAAC,GAAGrB,SAAS,CAAC;;KAE/F;IACD,IAAI,CAACG,eAAe,CAAC,IAAI,CAAC7D,iBAAiB,CAAC;EAC9C;EAGAzC,iBAAiBA,CAACyH,iBAAyB;IACzC,MAAMrC,YAAY,GAAG,IAAI,CAAC/D,aAAa,CAACyE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACjI,UAAU,KAAK2J,iBAAiB,CAAC;IAC3F,IAAI,CAACpI,SAAS,GAAG+F,YAAY,GAAGA,YAAY,CAACsC,MAAM,GAAG,EAAE;IACxD,IAAI,CAACzI,WAAW,CAAClB,MAAM,GAAGoI,SAAS,CAAC,CAAC;EACvC;EAEA5I,IAAIA,CAACoK,GAAQ,EAAE5B,IAAS;IACtBpK,QAAQ,CAAC;MACPiE,QAAQ,EAAE,IAAI,CAACyD,sBAAsB,EAAE;MACvCuE,aAAa,EAAE,IAAI,CAACtE,6BAA6B;KAClD,CAAC,CAACE,SAAS,CAAC;MACXqE,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACjC,mBAAmB,GAAGG,IAAI;QAC/B,IAAI,CAACtH,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACqJ,mBAAmB,GAAG/B,IAAI,CAACgC,iBAAiB;QACjD,IAAIN,iBAAiB,GAAG,IAAI,CAACpG,aAAa,CAACyE,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAChH,UAAU,KAAKiI,IAAI,CAACjI,UAAU,CAAC;QACtF,IAAI,CAACmB,WAAW,CAACnB,UAAU,GAAG2J,iBAAiB,EAAE3J,UAAU;QAC3D,IAAI,CAACuB,SAAS,GAAGoI,iBAAiB,EAAEC,MAAM;QAC1C;QACA;QACA;QACA;QACA,IAAI,CAACnG,YAAY,GAAG,IAAI,CAAC2C,YAAY,CAAC,CAAC,GAAG,IAAI,CAACe,WAAW,CAAC,CAAC;QAC5D;QACA,IAAI,CAACE,0BAA0B,GAAG,IAAI,CAAC7C,gBAAgB,CAAC0C,MAAM,CAAEgD,CAAM,IAAKA,CAAC,CAAChK,aAAa,IAAI+H,IAAI,CAAC/H,aAAa,CAAC;QAEjH,IAAI,CAACqC,gBAAgB,CAAC0F,IAAI,CAAC5H,aAAa,CAAC4G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC9F,WAAW,GAAG;UACjBlB,MAAM,EAAEgI,IAAI,CAAChI,MAAM;UACnBQ,YAAY,EAAEwH,IAAI,CAACxH,YAAY;UAC/BF,KAAK,EAAE0H,IAAI,CAAC1H,KAAK;UACjBP,UAAU,EAAEiI,IAAI,CAACjI,UAAU;UAC3BQ,QAAQ,EAAEyH,IAAI,CAACzH,QAAQ;UACvBH,aAAa,EAAE4H,IAAI,CAAC5H,aAAa,CAAC4G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC/CnE,OAAO,EAAEmF,IAAI,CAACnF,OAAO;UACrBqH,SAAS,EAAElC,IAAI,CAACkC,SAAS;UACzBnH,iBAAiB,EAAEiF,IAAI,CAACjF;SACzB;QACD,IAAI,CAACa,aAAa,CAACuG,IAAI,CAACP,GAAG,CAAC;MAC9B;KACD,CAAC;EACJ;EAEAQ,iBAAiBA,CAACR,GAAQ;IACxBhM,QAAQ,CAAC;MACPiE,QAAQ,EAAE,IAAI,CAACyD,sBAAsB,EAAE;MACvCuE,aAAa,EAAE,IAAI,CAACtE,6BAA6B;KAClD,CAAC,CAACE,SAAS,CAAC;MACXqE,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACpJ,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACQ,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC0C,aAAa,CAACuG,IAAI,CAACP,GAAG,CAAC;MAC9B;KACD,CAAC;EACJ;EAEAS,sBAAsBA,CAAA;IACpB,MAAMC,qBAAqB,GAAG,IAAI,CAACpJ,WAAW,CAACd,aAAa,GAAG,WAAW;IAC1E,OAAO,IAAI,CAACgH,0BAA0B,CAACmD,IAAI,CAAErJ,WAAgB,IAC3DA,WAAW,CAACd,aAAa,KAAKkK,qBAAqB,IACnDpJ,WAAW,CAACZ,KAAK,KAAK,IAAI,CAACY,WAAW,CAACZ,KAAK,CAC7C;EACH;EAEAkK,UAAUA,CAAA;IACR,IAAI,CAACzG,KAAK,CAAC0G,KAAK,EAAE;IAClB,IAAI,CAAC1G,KAAK,CAAC2G,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACxJ,WAAW,CAACnB,UAAU,CAAC;IACtD,IAAI,CAACgE,KAAK,CAAC2G,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACxJ,WAAW,CAAClB,MAAM,CAAC;IAClD,IAAI,CAAC+D,KAAK,CAAC2G,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACxJ,WAAW,CAACd,aAAa,CAAC;IACzD,IAAI,CAAC2D,KAAK,CAAC2G,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACxJ,WAAW,CAACZ,KAAK,CAAC;IACjD,IAAI,CAACyD,KAAK,CAAC2G,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxJ,WAAW,CAACX,QAAQ,CAAC;IACtD,IAAI,CAACwD,KAAK,CAAC2G,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACxJ,WAAW,CAACV,YAAY,CAAC;IAC3D;IACA;IAEA;IACA;IACA;IACA;IACA;EACF;EAEAV,iBAAiBA,CAACkI,IAAS,EAAEjB,CAAS;IACpC,IAAI4D,MAAM,CAACC,OAAO,CAAC,WAAW,IAAI,CAAC1G,SAAS,GAAG6C,CAAC,IAAI,CAAC,EAAE;MACrD,IAAI,CAAClD,YAAY,CAACgH,kCAAkC,CAAC;QAAEzF,IAAI,EAAE4C,IAAI,CAACgC;MAAiB,CAAE,CAAC,CACnFjF,IAAI,CAACpH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CAAC,CACtCc,SAAS,CAACT,GAAG,IAAG;QACf,IAAIA,GAAG,CAACyD,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACzE,OAAO,CAAC8G,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACvC,eAAe,CAAC;YAAEnD,IAAI,EAAE;cAAEC,YAAY,EAAE,IAAI,CAACH;YAAgB;UAAE,CAAE,CAAC;QACzE;MACF,CAAC,CAAC;IACN;EACF;EAEA6F,QAAQA,CAACnB,GAAQ;IACf,IAAItB,OAAO,GAA4C;MACrDlD,IAAI,EAAE;QACJ4F,GAAG,EAAE,IAAI,CAACjB,mBAAmB;QAC7BvJ,YAAY,EAAE,IAAI,CAACU,WAAW,CAACV,YAAY;QAC3CF,KAAK,EAAE,IAAI,CAACY,WAAW,CAACZ,KAAK;QAC7BC,QAAQ,EAAE,IAAI,CAACW,WAAW,CAACX,QAAQ;QACnCH,aAAa,EAAE,IAAI,CAACc,WAAW,CAACd,aAAa;QAC7CyC,OAAO,EAAE,IAAI,CAAC3B,WAAW,CAAC2B,OAAO;QACjCoI,OAAO,EAAE,IAAI,CAACpD,mBAAmB,CAACoD;;KAErC;IAED,IAAI,CAACpH,YAAY,CAACqH,gCAAgC,CAAC5C,OAAO,CAAC,CACxDvD,IAAI,CAACpH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CAAC,CAACc,SAAS,CAACT,GAAG,IAAG;MACtD,IAAIA,GAAG,CAACyD,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzE,OAAO,CAAC8G,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACvC,eAAe,CAAC;UAAEnD,IAAI,EAAE;YAAEC,YAAY,EAAE,IAAI,CAACH;UAAgB;QAAE,CAAE,CAAC;QACvE0E,GAAG,CAACzG,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACa,OAAO,CAAC0E,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACN;EAEAtF,IAAIA,CAACuG,GAAQ;IACX,IAAI,CAACY,UAAU,EAAE;IACjB,IAAI,IAAI,CAACzG,KAAK,CAACoH,aAAa,CAAC5H,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACS,OAAO,CAACoH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;MACpD;IACF;IAEA,IAAI7C,OAAkD;IAEtDA,OAAO,GAAG;MACRlD,IAAI,EAAE;QACJ,GAAG,IAAI,CAAClE,WAAW;QACnBmK,YAAY,EAAE,IAAI,CAACnG;;KAEtB;IAED,IAAI,IAAI,CAACxE,gBAAgB,EAAE;MACzB,IAAI,CAACmD,YAAY,CAACyH,kCAAkC,CAAChD,OAAO,CAAC,CAC1DvD,IAAI,CAACpH,kBAAkB,CAAC,IAAI,CAACgH,OAAO,CAAC,CAAC,CAACc,SAAS,CAACT,GAAG,IAAG;QACtD,IAAIA,GAAG,CAACyD,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACzE,OAAO,CAAC8G,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACvC,eAAe,CAAC;YAAEnD,IAAI,EAAE;cAAEC,YAAY,EAAE,IAAI,CAACH;YAAgB;UAAE,CAAE,CAAC;UACvE0E,GAAG,CAACzG,KAAK,EAAE;QACb,CAAC,MAAM;UACL,IAAI,CAACa,OAAO,CAAC0E,YAAY,CAAC1D,GAAG,CAAC2D,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAI,CAACoC,QAAQ,CAACnB,GAAG,CAAC;IACpB;EACF;EAEA2B,WAAWA,CAAA;IACT,IAAI,CAAC1H,YAAY,CAAC2H,4CAA4C,CAAC;MAC7DpG,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACH,gBAAgB;QACnCjF,aAAa,EAAE,IAAI,CAACsJ,mBAAmB;QACvCrJ,MAAM,EAAE,IAAI,CAACsJ,oBAAoB,KAAKpB,SAAS,GAAG,IAAI,CAACoB,oBAAoB,GAAGpB,SAAS;QACvFhI,aAAa,EAAE,IAAI,CAACqJ,iBAAiB,GAAG,IAAI,CAACb,UAAU,CAAC,IAAI,CAACa,iBAAiB,CAAC,GAAGrB,SAAS;;KAE9F,CAAC,CAACrD,IAAI,CACLhH,GAAG,CAACiH,GAAG,IAAG;MACR,IAAIA,GAAG,CAACyD,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxE,gBAAgB,CAACwH,iBAAiB,CAACzG,GAAG,CAACC,OAAO,EAAEyG,QAAS,EAAE,IAAI,CAAC;MACvE;IACF,CAAC,CAAC,CACH,CAACjG,SAAS,EAAE;EACf;;;uCArXWhC,iBAAiB,EAAAtF,EAAA,CAAAwN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1N,EAAA,CAAAwN,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5N,EAAA,CAAAwN,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA9N,EAAA,CAAAwN,iBAAA,CAAAK,EAAA,CAAAE,gBAAA,GAAA/N,EAAA,CAAAwN,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAjO,EAAA,CAAAwN,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAnO,EAAA,CAAAwN,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjB/I,iBAAiB;MAAAgJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxO,EAAA,CAAAyO,0BAAA,EAAAzO,EAAA,CAAA0O,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC9B5BhP,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAsB,SAAA,qBAAiC;UACnCtB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,WAC6C;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKpGH,EAHN,CAAAC,cAAA,aAA8B,aACN,aAC8B,eACH;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,oBAC8B;UADKD,EAAA,CAAA0C,gBAAA,2BAAAwM,+DAAAtM,MAAA;YAAA5C,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAA8C,kBAAA,CAAAmM,GAAA,CAAAlI,gBAAA,EAAAnE,MAAA,MAAAqM,GAAA,CAAAlI,gBAAA,GAAAnE,MAAA;YAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;UAAA,EAA8B;UAC/D5C,EAAA,CAAAU,UAAA,4BAAA0O,gEAAA;YAAApP,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAkB6N,GAAA,CAAA9D,MAAA,EAAQ;UAAA,EAAC;UAC3BnL,EAAA,CAAAgD,UAAA,KAAAqM,uCAAA,uBAA4D;UAEhErP,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAkD,gBACX;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,iBAAmF;UAAlCD,EAAA,CAAA0C,gBAAA,2BAAA4M,2DAAA1M,MAAA;YAAA5C,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAA8C,kBAAA,CAAAmM,GAAA,CAAA7D,mBAAA,EAAAxI,MAAA,MAAAqM,GAAA,CAAA7D,mBAAA,GAAAxI,MAAA;YAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;UAAA,EAAiC;UAEtF5C,EAFI,CAAAG,YAAA,EAAmF,EAC/E,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,iBACH;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzDH,EAAA,CAAAC,cAAA,sBAC8E;UAAhCD,EAAA,CAAA0C,gBAAA,2BAAA6M,gEAAA3M,MAAA;YAAA5C,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAA8C,kBAAA,CAAAmM,GAAA,CAAA3D,iBAAA,EAAA1I,MAAA,MAAAqM,GAAA,CAAA3D,iBAAA,GAAA1I,MAAA;YAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;UAAA,EAA+B;UAC/E5C,EADgF,CAAAG,YAAA,EAAa,EACvF;UAEJH,EADF,CAAAC,cAAA,cAAkD,iBACV;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClDH,EAAA,CAAAC,cAAA,iBAAoF;UAAnCD,EAAA,CAAA0C,gBAAA,2BAAA8M,2DAAA5M,MAAA;YAAA5C,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAA8C,kBAAA,CAAAmM,GAAA,CAAA5D,oBAAA,EAAAzI,MAAA,MAAAqM,GAAA,CAAA5D,oBAAA,GAAAzI,MAAA;YAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;UAAA,EAAkC;UAGzF5C,EAHM,CAAAG,YAAA,EAAoF,EAChF,EACF,EACF;UAEJH,EADF,CAAAC,cAAA,eAA2C,kBACY;UAAnBD,EAAA,CAAAU,UAAA,mBAAA+O,oDAAA;YAAAzP,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAS6N,GAAA,CAAA9D,MAAA,EAAQ;UAAA,EAAC;UAACnL,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAyE;UAApCD,EAAA,CAAAU,UAAA,mBAAAgP,oDAAA;YAAA1P,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,MAAAjO,SAAA,GAAAlB,EAAA,CAAAmB,WAAA;YAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAS6N,GAAA,CAAAhD,iBAAA,CAAA/K,SAAA,CAAyB;UAAA,EAAC;UAAClB,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtFH,EAAA,CAAAC,cAAA,kBAAqD;UAAxBD,EAAA,CAAAU,UAAA,mBAAAiP,oDAAA;YAAA3P,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAS6N,GAAA,CAAA7B,WAAA,EAAa;UAAA,EAAC;UAACpN,EAAA,CAAAE,MAAA,oBAAE;UACzDF,EADyD,CAAAG,YAAA,EAAS,EAC5D;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACzB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA2B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,cAA+C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAErDF,EAFqD,CAAAG,YAAA,EAAK,EACnD,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgD,UAAA,KAAA4M,gCAAA,mBAAsD;UAoB9D5P,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,sBAAgB,0BAEoC;UADFD,EAAA,CAAA0C,gBAAA,wBAAAmN,iEAAAjN,MAAA;YAAA5C,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAAnP,EAAA,CAAA8C,kBAAA,CAAAmM,GAAA,CAAAhJ,SAAA,EAAArD,MAAA,MAAAqM,GAAA,CAAAhJ,SAAA,GAAArD,MAAA;YAAA,OAAA5C,EAAA,CAAAoB,WAAA,CAAAwB,MAAA;UAAA,EAAoB;UAClE5C,EAAA,CAAAU,UAAA,wBAAAmP,iEAAA;YAAA7P,EAAA,CAAAa,aAAA,CAAAsO,GAAA;YAAA,OAAAnP,EAAA,CAAAoB,WAAA,CAAc6N,GAAA,CAAAzH,kBAAA,CAAAyH,GAAA,CAAA/I,YAAA,CAAgC;UAAA,EAAC;UAErDlG,EAFsD,CAAAG,YAAA,EAAiB,EACpD,EACT;UAEVH,EAAA,CAAAgD,UAAA,KAAA8M,yCAAA,kCAAA9P,EAAA,CAAA+P,sBAAA,CAAkD;;;UAxE7B/P,EAAA,CAAAO,SAAA,IAAuB;UAAvBP,EAAA,CAAAI,UAAA,iDAAuB;UAACJ,EAAA,CAAAkD,gBAAA,YAAA+L,GAAA,CAAAlI,gBAAA,CAA8B;UAEnC/G,EAAA,CAAAO,SAAA,EAAW;UAAXP,EAAA,CAAAI,UAAA,YAAA6O,GAAA,CAAA5I,QAAA,CAAW;UAKQrG,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAkD,gBAAA,YAAA+L,GAAA,CAAA7D,mBAAA,CAAiC;UAMtEpL,EAAA,CAAAO,SAAA,GAAmB;UAA6CP,EAAhE,CAAAI,UAAA,oBAAmB,wBAA4C,kBAAkB;UAC/CJ,EAAA,CAAAkD,gBAAA,YAAA+L,GAAA,CAAA3D,iBAAA,CAA+B;UAI5BtL,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAkD,gBAAA,YAAA+L,GAAA,CAAA5D,oBAAA,CAAkC;UA2B9DrL,EAAA,CAAAO,SAAA,IAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAA6O,GAAA,CAAA9I,aAAA,CAAkB;UAsB7BnG,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA6O,GAAA,CAAA/I,YAAA,CAA+B;UAAClG,EAAA,CAAAkD,gBAAA,SAAA+L,GAAA,CAAAhJ,SAAA,CAAoB;UAACjG,EAAA,CAAAI,UAAA,aAAA6O,GAAA,CAAAjJ,QAAA,CAAqB;;;qBDnDlFzG,YAAY,EAAAyQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAErQ,YAAY,EAAAsQ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,yBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAA9C,EAAA,CAAA+C,eAAA,EAAA/C,EAAA,CAAAgD,mBAAA,EAAAhD,EAAA,CAAAiD,qBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,gBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAApD,EAAA,CAAAqD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,iBAAA,EAAAC,GAAA,CAAAC,cAAA,EAAE7R,cAAc,EAAA8R,GAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}