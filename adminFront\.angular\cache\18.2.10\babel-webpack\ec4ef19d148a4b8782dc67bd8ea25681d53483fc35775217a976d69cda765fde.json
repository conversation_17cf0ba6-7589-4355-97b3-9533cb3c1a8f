{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ANSI X.923 padding strategy.\n   */\n  CryptoJS.pad.AnsiX923 = {\n    pad: function (data, blockSize) {\n      // Shortcuts\n      var dataSigBytes = data.sigBytes;\n      var blockSizeBytes = blockSize * 4;\n\n      // Count padding bytes\n      var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes;\n\n      // Compute last byte position\n      var lastBytePos = dataSigBytes + nPaddingBytes - 1;\n\n      // Pad\n      data.clamp();\n      data.words[lastBytePos >>> 2] |= nPaddingBytes << 24 - lastBytePos % 4 * 8;\n      data.sigBytes += nPaddingBytes;\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n      // Remove padding\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Ansix923;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "AnsiX923", "data", "blockSize", "dataSigBytes", "sigBytes", "blockSizeBytes", "nPaddingBytes", "lastBytePos", "clamp", "words", "unpad", "Ansix923"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/pad-ansix923.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * ANSI X.923 padding strategy.\n\t */\n\tCryptoJS.pad.AnsiX923 = {\n\t    pad: function (data, blockSize) {\n\t        // Shortcuts\n\t        var dataSigBytes = data.sigBytes;\n\t        var blockSizeBytes = blockSize * 4;\n\n\t        // Count padding bytes\n\t        var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes;\n\n\t        // Compute last byte position\n\t        var lastBytePos = dataSigBytes + nPaddingBytes - 1;\n\n\t        // Pad\n\t        data.clamp();\n\t        data.words[lastBytePos >>> 2] |= nPaddingBytes << (24 - (lastBytePos % 4) * 8);\n\t        data.sigBytes += nPaddingBytes;\n\t    },\n\n\t    unpad: function (data) {\n\t        // Get number of padding bytes from last byte\n\t        var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n\n\t        // Remove padding\n\t        data.sigBytes -= nPaddingBytes;\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.Ansix923;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAG,CAACC,QAAQ,GAAG;IACpBD,GAAG,EAAE,SAAAA,CAAUE,IAAI,EAAEC,SAAS,EAAE;MAC5B;MACA,IAAIC,YAAY,GAAGF,IAAI,CAACG,QAAQ;MAChC,IAAIC,cAAc,GAAGH,SAAS,GAAG,CAAC;;MAElC;MACA,IAAII,aAAa,GAAGD,cAAc,GAAGF,YAAY,GAAGE,cAAc;;MAElE;MACA,IAAIE,WAAW,GAAGJ,YAAY,GAAGG,aAAa,GAAG,CAAC;;MAElD;MACAL,IAAI,CAACO,KAAK,CAAC,CAAC;MACZP,IAAI,CAACQ,KAAK,CAACF,WAAW,KAAK,CAAC,CAAC,IAAID,aAAa,IAAK,EAAE,GAAIC,WAAW,GAAG,CAAC,GAAI,CAAE;MAC9EN,IAAI,CAACG,QAAQ,IAAIE,aAAa;IAClC,CAAC;IAEDI,KAAK,EAAE,SAAAA,CAAUT,IAAI,EAAE;MACnB;MACA,IAAIK,aAAa,GAAGL,IAAI,CAACQ,KAAK,CAAER,IAAI,CAACG,QAAQ,GAAG,CAAC,KAAM,CAAC,CAAC,GAAG,IAAI;;MAEhE;MACAH,IAAI,CAACG,QAAQ,IAAIE,aAAa;IAClC;EACJ,CAAC;EAGD,OAAOR,QAAQ,CAACC,GAAG,CAACY,QAAQ;AAE7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}