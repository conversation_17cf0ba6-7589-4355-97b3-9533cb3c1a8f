{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Welsh [cy]\n//! author : <PERSON> : https://github.com/robgallen\n//! author : https://github.com/ryangreaves\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var cy = moment.defineLocale('cy', {\n    months: 'Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr'.split('_'),\n    monthsShort: 'Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag'.split('_'),\n    weekdays: 'Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn'.split('_'),\n    weekdaysShort: 'Sul_Llun_Maw_Mer_Iau_Gwe_Sad'.split('_'),\n    weekdaysMin: 'Su_Ll_Ma_Me_Ia_Gw_Sa'.split('_'),\n    weekdaysParseExact: true,\n    // time formats are the same as en-gb\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Heddiw am] LT',\n      nextDay: '[Yfory am] LT',\n      nextWeek: 'dddd [am] LT',\n      lastDay: '[Ddoe am] LT',\n      lastWeek: 'dddd [diwethaf am] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'mewn %s',\n      past: '%s yn ôl',\n      s: 'ychydig eiliadau',\n      ss: '%d eiliad',\n      m: 'munud',\n      mm: '%d munud',\n      h: 'awr',\n      hh: '%d awr',\n      d: 'diwrnod',\n      dd: '%d diwrnod',\n      M: 'mis',\n      MM: '%d mis',\n      y: 'blwyddyn',\n      yy: '%d flynedd'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,\n    // traditional ordinal numbers above 31 are not commonly used in colloquial Welsh\n    ordinal: function (number) {\n      var b = number,\n        output = '',\n        lookup = ['', 'af', 'il', 'ydd', 'ydd', 'ed', 'ed', 'ed', 'fed', 'fed', 'fed',\n        // 1af to 10fed\n        'eg', 'fed', 'eg', 'eg', 'fed', 'eg', 'eg', 'fed', 'eg', 'fed' // 11eg to 20fed\n        ];\n      if (b > 20) {\n        if (b === 40 || b === 50 || b === 60 || b === 80 || b === 100) {\n          output = 'fed'; // not 30ain, 70ain or 90ain\n        } else {\n          output = 'ain';\n        }\n      } else if (b > 0) {\n        output = lookup[b];\n      }\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return cy;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}