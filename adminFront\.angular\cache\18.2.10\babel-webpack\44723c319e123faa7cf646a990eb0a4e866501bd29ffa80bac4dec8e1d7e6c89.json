{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { cApproveStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { tap } from 'rxjs';\nlet RelatedDocumentsComponent = class RelatedDocumentsComponent extends BaseComponent {\n  constructor(allow, dialogService, valid, message, buildCaseFileService, activatedRoute, _utilityService) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this.message = message;\n    this.buildCaseFileService = buildCaseFileService;\n    this.activatedRoute = activatedRoute;\n    this._utilityService = _utilityService;\n    this.filterCategoryName = '';\n    this.filterFileName = '';\n    this.buildCaseId = 1;\n    this.listBuildCaseFile = [];\n    this.isAddNew = false;\n    this.uploadedFile = undefined;\n    this.listFiles = [];\n    this.destroy = inject(DestroyRef);\n    this.currentPage = -1;\n  }\n  ngOnInit() {\n    this.buildCaseId = 1;\n    this.buildCaseId = parseInt(this.activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.getListBuildCaseFile(1);\n  }\n  getListBuildCaseFile(page) {\n    this.buildCaseFileService.apiBuildCaseFileGetListBuildCaseFilePost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId,\n        CName: this.filterFileName ? this.filterFileName : undefined,\n        CCategoryName: this.filterCategoryName ? this.filterCategoryName : undefined,\n        PageIndex: page,\n        PageSize: 10\n      }\n    }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.listBuildCaseFile = res.Entries ?? [];\n      this.totalRecords = res.TotalItems;\n    });\n  }\n  onAddNew(dialog) {\n    this.isAddNew = true;\n    this.uploadedFile = undefined;\n    this.currentItem = {\n      CStatus: 0\n    };\n    this.dialogService.open(dialog);\n  }\n  onAddMultiplesFile(dialog) {\n    this.isAddNew = true;\n    this.uploadedFile = undefined;\n    this.currentItem = {\n      CCategoryName: \"\"\n    };\n    this.dialogService.open(dialog);\n  }\n  onEdit(ref, item) {\n    this.isAddNew = false;\n    this.uploadedFile = undefined;\n    this.currentItem = {};\n    this.buildCaseFileService.apiBuildCaseFileGetBuildCaseFileByIdPost$Json({\n      body: {\n        CBuildCaseFileID: item.CID\n      }\n    }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.currentItem = res.Entries;\n    });\n    this.dialogService.open(ref);\n  }\n  onDelete(item, index) {\n    if (window.confirm(`確定要刪除【項目${item.CID}】?`)) {\n      this.buildCaseFileService.apiBuildCaseFileDeleteBuildCaseFilePost$Json({\n        body: {\n          CBuildCaseFileID: item.CID\n        }\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getListBuildCaseFile(this.currentPage);\n        }\n      });\n    }\n  }\n  showReason(ref, item) {\n    this.dialogService.open(ref);\n    this.currentItem = item;\n  }\n  base64ToBlob(base64, contentType = '') {\n    const base64Data = base64.split(',')[1] || base64;\n    // Decode base64 string\n    const byteCharacters = atob(base64Data);\n    // Create a byte array with length equal to the number of bytes in the string\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  onSave(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let request = {\n      body: {\n        CBuildCaseID: this.isAddNew ? this.buildCaseId : undefined,\n        CBuildCaseFileID: this.isAddNew ? undefined : this.currentItem.CID,\n        CStatus: this.currentItem.CStatus,\n        CSubmitRemark: this.currentItem.CSubmitRemark ? this.currentItem.CSubmitRemark : undefined,\n        CFile: this.isAddNew == false ? null : this.uploadedFile.CFileUpload,\n        CCategoryName: this.currentItem.CCategoryName ? this.currentItem.CCategoryName : undefined\n      }\n    };\n    this.buildCaseFileService.apiBuildCaseFileSaveBuildCaseFilePost$Json(request).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('儲存成功');\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      ref.close();\n      this.getListBuildCaseFile(1);\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required(`送審說明`, this.currentItem.CSubmitRemark);\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n    if (this.isAddNew && !this.uploadedFile) {\n      this.valid.addErrorMessage(`上傳 必填`);\n    }\n  }\n  onChooseFile(event) {\n    let files = event.target.files;\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(files[0].type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n      return;\n    }\n    let reader = new FileReader();\n    reader.readAsDataURL(files[0]);\n    reader.onload = e => {\n      let file = \"\";\n      if (!files[0].name.includes('pdf')) {\n        file = URL.createObjectURL(files[0]);\n      }\n      this.uploadedFile = {\n        CName: files[0].name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: files[0].name.includes('pdf') ? files[0] : file,\n        CFileUpload: files[0],\n        CFileType: EnumFileType.PDF\n      };\n      event.target.value = null;\n    };\n  }\n  onMultipleFile(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          file.id = new Date().getTime();\n          this.listFiles.push({\n            id: new Date().getTime(),\n            name: file.name,\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  deleteItem(item) {\n    if (window.confirm(`確定要移除【${item.CName}】?`)) {\n      this.uploadedFile = undefined;\n    }\n  }\n  removeFile(id) {\n    this.listFiles = this.listFiles.filter(x => x.id != id);\n  }\n  validationMultiple() {\n    this.valid.clear();\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\n    if (this.isAddNew && this.listFiles.length == 0) {\n      this.valid.addErrorMessage(`上傳 必填`);\n    }\n  }\n  onSaveMultiple(ref) {\n    this.validationMultiple();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let listFilesUpload = this.listFiles.map(x => x.CFile);\n    this.buildCaseFileService.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId,\n        CCategoryName: this.currentItem.CCategoryName,\n        CFiles: listFilesUpload\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('儲存成功');\n        ref.close();\n        this.getListBuildCaseFile(1);\n        this.listFiles = [];\n        this.currentItem = {};\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    })).subscribe();\n  }\n  getListPageChange(page) {\n    this.currentPage = page;\n    this.getListBuildCaseFile(page);\n  }\n  checkImage(fileName) {\n    return this._utilityService.getFileExtension(fileName).includes('png') || this._utilityService.getFileExtension(fileName).includes('jpeg') || this._utilityService.getFileExtension(fileName).includes('jpg');\n  }\n  openNewFile(fileName) {\n    console.log(fileName);\n    window.open(`${fileName}`, '_blank');\n  }\n};\nRelatedDocumentsComponent = __decorate([Component({\n  selector: 'app-related-documents',\n  standalone: true,\n  imports: [CommonModule, SharedModule, DateFormatHourPipe, cApproveStatusPipe],\n  templateUrl: './related-documents.component.html',\n  styleUrls: ['./related-documents.component.scss']\n})], RelatedDocumentsComponent);\nexport { RelatedDocumentsComponent };", "map": {"version": 3, "names": ["Component", "DestroyRef", "inject", "CommonModule", "takeUntilDestroyed", "EnumFileType", "DateFormatHourPipe", "SharedModule", "BaseComponent", "cApproveStatusPipe", "tap", "RelatedDocumentsComponent", "constructor", "allow", "dialogService", "valid", "message", "buildCaseFileService", "activatedRoute", "_utilityService", "filterCategoryName", "filterFileName", "buildCaseId", "listBuildCaseFile", "isAddNew", "uploadedFile", "undefined", "listFiles", "destroy", "currentPage", "ngOnInit", "parseInt", "snapshot", "paramMap", "get", "getListBuildCaseFile", "page", "apiBuildCaseFileGetListBuildCaseFilePost$Json", "body", "CBuildCaseID", "CName", "CCategoryName", "PageIndex", "PageSize", "pipe", "subscribe", "res", "Entries", "totalRecords", "TotalItems", "onAddNew", "dialog", "currentItem", "CStatus", "open", "onAddMultiplesFile", "onEdit", "ref", "item", "apiBuildCaseFileGetBuildCaseFileByIdPost$Json", "CBuildCaseFileID", "CID", "onDelete", "index", "window", "confirm", "apiBuildCaseFileDeleteBuildCaseFilePost$Json", "StatusCode", "showSucessMSG", "showReason", "base64ToBlob", "base64", "contentType", "base64Data", "split", "byteCharacters", "atob", "byteArrays", "offset", "length", "slice", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "push", "Blob", "type", "onSave", "validation", "errorMessages", "showErrorMSGs", "request", "CSubmitRemark", "CFile", "CFileUpload", "apiBuildCaseFileSaveBuildCaseFilePost$Json", "showErrorMSG", "Message", "close", "clear", "required", "addErrorMessage", "onChooseFile", "event", "files", "target", "fileRegex", "test", "reader", "FileReader", "readAsDataURL", "onload", "e", "file", "name", "includes", "URL", "createObjectURL", "result", "toString", "Cimg", "CFileType", "PDF", "value", "onMultipleFile", "base64Str", "id", "Date", "getTime", "data", "extension", "getFileExtension", "deleteItem", "removeFile", "filter", "x", "validationMultiple", "onSaveMultiple", "listFilesUpload", "map", "apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json", "CFiles", "getListPageChange", "checkImage", "fileName", "openNewFile", "console", "log", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\related-documents\\related-documents.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseFileService } from 'src/services/api/services';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseFileRes, EnumStatusCode } from 'src/services/api/models';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params } from 'src/services/api/fn/build-case-file/api-build-case-file-save-build-case-file-post-json';\r\nimport * as moment from 'moment';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { cApproveStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { tap } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-related-documents',\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, DateFormatHourPipe, cApproveStatusPipe],\r\n  templateUrl: './related-documents.component.html',\r\n  styleUrls: ['./related-documents.component.scss'],\r\n})\r\nexport class RelatedDocumentsComponent extends BaseComponent implements OnInit {\r\n  filterCategoryName: string = '';\r\n  filterFileName: string = '';\r\n  buildCaseId: number = 1;\r\n\r\n  listBuildCaseFile: BuildCaseFileRes[] = [];\r\n\r\n  isAddNew = false;\r\n  currentItem: BuildCaseFileRes;\r\n  uploadedFile: any = undefined;\r\n\r\n  listFiles: any[] = []\r\n\r\n  destroy = inject(DestroyRef);\r\n\r\n  currentPage: number = -1;\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private message: MessageService,\r\n    private buildCaseFileService: BuildCaseFileService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.buildCaseId = 1;\r\n    this.buildCaseId = parseInt(this.activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.getListBuildCaseFile(1);\r\n  }\r\n\r\n  getListBuildCaseFile(page: number) {\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileGetListBuildCaseFilePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CName: this.filterFileName ? this.filterFileName : undefined,\r\n          CCategoryName: this.filterCategoryName\r\n            ? this.filterCategoryName\r\n            : undefined,\r\n          PageIndex: page,\r\n          PageSize: 10\r\n        },\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroy))\r\n      .subscribe((res) => {\r\n        this.listBuildCaseFile = res.Entries ?? [];\r\n        this.totalRecords = res.TotalItems!\r\n      });\r\n  }\r\n  onAddNew(dialog: any) {\r\n    this.isAddNew = true;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {\r\n      CStatus: 0,\r\n    };\r\n    this.dialogService.open(dialog);\r\n  }\r\n  onAddMultiplesFile(dialog: any) {\r\n    this.isAddNew = true;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {\r\n      CCategoryName: \"\"\r\n    };\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  onEdit(ref: any, item?: any) {\r\n    this.isAddNew = false;\r\n    this.uploadedFile = undefined;\r\n    this.currentItem = {};\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileGetBuildCaseFileByIdPost$Json({\r\n        body: {\r\n          CBuildCaseFileID: item.CID,\r\n        },\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroy))\r\n      .subscribe((res) => {\r\n        this.currentItem = res.Entries!;\r\n      });\r\n\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  onDelete(item: BuildCaseFileRes, index: number) {\r\n    if (window.confirm(`確定要刪除【項目${item.CID}】?`)) {\r\n      this.buildCaseFileService\r\n        .apiBuildCaseFileDeleteBuildCaseFilePost$Json({\r\n          body: {\r\n            CBuildCaseFileID: item.CID,\r\n          },\r\n        })\r\n        .pipe(takeUntilDestroyed(this.destroy))\r\n        .subscribe((res) => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG('執行成功');\r\n            this.getListBuildCaseFile(this.currentPage);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  showReason(ref: any, item: BuildCaseFileRes) {\r\n    this.dialogService.open(ref);\r\n    this.currentItem = item;\r\n  }\r\n\r\n  base64ToBlob(base64: string, contentType: string = ''): Blob {\r\n    const base64Data = base64.split(',')[1] || base64;\r\n    // Decode base64 string\r\n    const byteCharacters = atob(base64Data);\r\n    // Create a byte array with length equal to the number of bytes in the string\r\n    const byteArrays: Uint8Array[] = [];\r\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\r\n      const slice = byteCharacters.slice(offset, offset + 512);\r\n      const byteNumbers = new Array(slice.length);\r\n      for (let i = 0; i < slice.length; i++) {\r\n        byteNumbers[i] = slice.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      byteArrays.push(byteArray);\r\n    }\r\n    return new Blob(byteArrays, { type: contentType });\r\n  }\r\n\r\n  onSave(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let request: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params = {\r\n      body: {\r\n        CBuildCaseID: this.isAddNew ? this.buildCaseId : undefined,\r\n        CBuildCaseFileID: this.isAddNew ? undefined : this.currentItem.CID,\r\n        CStatus: this.currentItem.CStatus,\r\n        CSubmitRemark: this.currentItem.CSubmitRemark ? this.currentItem.CSubmitRemark : undefined,\r\n        CFile: this.isAddNew == false ? null : this.uploadedFile.CFileUpload,\r\n        CCategoryName: this.currentItem.CCategoryName ? this.currentItem.CCategoryName : undefined,\r\n      },\r\n    };\r\n    this.buildCaseFileService\r\n      .apiBuildCaseFileSaveBuildCaseFilePost$Json(request)\r\n      .subscribe((res) => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('儲存成功');\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n        ref.close();\r\n        this.getListBuildCaseFile(1);\r\n      });\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required(`送審說明`, this.currentItem.CSubmitRemark);\r\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\r\n    if (this.isAddNew && !this.uploadedFile) {\r\n      this.valid.addErrorMessage(`上傳 必填`);\r\n    }\r\n  }\r\n\r\n  onChooseFile(event: any) {\r\n    let files = event.target.files;\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n\r\n    if (!fileRegex.test(files[0].type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n      return;\r\n    }\r\n\r\n    let reader = new FileReader();\r\n    reader.readAsDataURL(files[0]);\r\n    reader.onload = (e) => {\r\n      let file: string = \"\"\r\n      if (!files[0].name.includes('pdf')) {\r\n        file = URL.createObjectURL(files[0]);\r\n      }\r\n      this.uploadedFile = {\r\n        CName: files[0].name,\r\n        CFile: e.target?.result?.toString().split(',')[1],\r\n        Cimg: files[0].name.includes('pdf') ? files[0] : file,\r\n        CFileUpload: files[0],\r\n        CFileType: EnumFileType.PDF,\r\n      };\r\n      event.target.value = null;\r\n    };\r\n  }\r\n\r\n  onMultipleFile(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          file.id = new Date().getTime();\r\n          this.listFiles.push({\r\n            id: new Date().getTime(),\r\n            name: file.name,\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  deleteItem(item: any) {\r\n    if (window.confirm(`確定要移除【${item.CName}】?`)) {\r\n      this.uploadedFile = undefined;\r\n    }\r\n  }\r\n\r\n  removeFile(id: number) {\r\n    this.listFiles = this.listFiles.filter(x => x.id != id);\r\n  }\r\n\r\n  validationMultiple() {\r\n    this.valid.clear();\r\n    this.valid.required(`分類名稱`, this.currentItem.CCategoryName);\r\n    if (this.isAddNew && this.listFiles.length! == 0) {\r\n      this.valid.addErrorMessage(`上傳 必填`);\r\n    }\r\n  }\r\n\r\n  onSaveMultiple(ref: any) {\r\n    this.validationMultiple();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let listFilesUpload = this.listFiles.map(x => x.CFile)\r\n\r\n    this.buildCaseFileService.apiBuildCaseFileSaveMultipleBuildCaseFilePost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId,\r\n        CCategoryName: this.currentItem.CCategoryName!,\r\n        CFiles: listFilesUpload\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('儲存成功');\r\n          ref.close();\r\n          this.getListBuildCaseFile(1);\r\n          this.listFiles = []\r\n          this.currentItem = {}\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  getListPageChange(page: number) {\r\n    this.currentPage = page;\r\n    this.getListBuildCaseFile(page)\r\n  }\r\n\r\n  checkImage(fileName: string) {\r\n    return this._utilityService.getFileExtension(fileName).includes('png') || this._utilityService.getFileExtension(fileName).includes('jpeg') || this._utilityService.getFileExtension(fileName).includes('jpg')\r\n  }\r\n\r\n  openNewFile(fileName: string) {\r\n    console.log(fileName);\r\n\r\n    window.open(`${fileName}`, '_blank');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,UAAU,EAAUC,MAAM,QAAQ,eAAe;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,YAAY,QAAQ,kCAAkC;AAI/D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,kBAAkB,QAAQ,mCAAmC;AAEtE,SAASC,GAAG,QAAQ,MAAM;AASnB,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQH,aAAa;EAgB1DI,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,KAAuB,EACvBC,OAAuB,EACvBC,oBAA0C,EAC1CC,cAA8B,EAC9BC,eAA+B;IAEvC,KAAK,CAACN,KAAK,CAAC;IARO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAtBzB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,WAAW,GAAW,CAAC;IAEvB,KAAAC,iBAAiB,GAAuB,EAAE;IAE1C,KAAAC,QAAQ,GAAG,KAAK;IAEhB,KAAAC,YAAY,GAAQC,SAAS;IAE7B,KAAAC,SAAS,GAAU,EAAE;IAErB,KAAAC,OAAO,GAAG1B,MAAM,CAACD,UAAU,CAAC;IAE5B,KAAA4B,WAAW,GAAW,CAAC,CAAC;EAWxB;EAESC,QAAQA,CAAA;IACf,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACA,WAAW,GAAGS,QAAQ,CAAC,IAAI,CAACb,cAAc,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IAC9E,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;EAC9B;EAEAA,oBAAoBA,CAACC,IAAY;IAC/B,IAAI,CAACnB,oBAAoB,CACtBoB,6CAA6C,CAAC;MAC7CC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACjB,WAAW;QAC9BkB,KAAK,EAAE,IAAI,CAACnB,cAAc,GAAG,IAAI,CAACA,cAAc,GAAGK,SAAS;QAC5De,aAAa,EAAE,IAAI,CAACrB,kBAAkB,GAClC,IAAI,CAACA,kBAAkB,GACvBM,SAAS;QACbgB,SAAS,EAAEN,IAAI;QACfO,QAAQ,EAAE;;KAEb,CAAC,CACDC,IAAI,CAACxC,kBAAkB,CAAC,IAAI,CAACwB,OAAO,CAAC,CAAC,CACtCiB,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAACvB,iBAAiB,GAAGuB,GAAG,CAACC,OAAO,IAAI,EAAE;MAC1C,IAAI,CAACC,YAAY,GAAGF,GAAG,CAACG,UAAW;IACrC,CAAC,CAAC;EACN;EACAC,QAAQA,CAACC,MAAW;IAClB,IAAI,CAAC3B,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAGC,SAAS;IAC7B,IAAI,CAAC0B,WAAW,GAAG;MACjBC,OAAO,EAAE;KACV;IACD,IAAI,CAACvC,aAAa,CAACwC,IAAI,CAACH,MAAM,CAAC;EACjC;EACAI,kBAAkBA,CAACJ,MAAW;IAC5B,IAAI,CAAC3B,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,YAAY,GAAGC,SAAS;IAC7B,IAAI,CAAC0B,WAAW,GAAG;MACjBX,aAAa,EAAE;KAChB;IACD,IAAI,CAAC3B,aAAa,CAACwC,IAAI,CAACH,MAAM,CAAC;EACjC;EAEAK,MAAMA,CAACC,GAAQ,EAAEC,IAAU;IACzB,IAAI,CAAClC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,YAAY,GAAGC,SAAS;IAC7B,IAAI,CAAC0B,WAAW,GAAG,EAAE;IACrB,IAAI,CAACnC,oBAAoB,CACtB0C,6CAA6C,CAAC;MAC7CrB,IAAI,EAAE;QACJsB,gBAAgB,EAAEF,IAAI,CAACG;;KAE1B,CAAC,CACDjB,IAAI,CAACxC,kBAAkB,CAAC,IAAI,CAACwB,OAAO,CAAC,CAAC,CACtCiB,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAI,CAACM,WAAW,GAAGN,GAAG,CAACC,OAAQ;IACjC,CAAC,CAAC;IAEJ,IAAI,CAACjC,aAAa,CAACwC,IAAI,CAACG,GAAG,CAAC;EAC9B;EAEAK,QAAQA,CAACJ,IAAsB,EAAEK,KAAa;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,WAAWP,IAAI,CAACG,GAAG,IAAI,CAAC,EAAE;MAC3C,IAAI,CAAC5C,oBAAoB,CACtBiD,4CAA4C,CAAC;QAC5C5B,IAAI,EAAE;UACJsB,gBAAgB,EAAEF,IAAI,CAACG;;OAE1B,CAAC,CACDjB,IAAI,CAACxC,kBAAkB,CAAC,IAAI,CAACwB,OAAO,CAAC,CAAC,CACtCiB,SAAS,CAAEC,GAAG,IAAI;QACjB,IAAIA,GAAG,CAACqB,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACjC,oBAAoB,CAAC,IAAI,CAACN,WAAW,CAAC;QAC7C;MACF,CAAC,CAAC;IACN;EACF;EAEAwC,UAAUA,CAACZ,GAAQ,EAAEC,IAAsB;IACzC,IAAI,CAAC5C,aAAa,CAACwC,IAAI,CAACG,GAAG,CAAC;IAC5B,IAAI,CAACL,WAAW,GAAGM,IAAI;EACzB;EAEAY,YAAYA,CAACC,MAAc,EAAEC,WAAA,GAAsB,EAAE;IACnD,MAAMC,UAAU,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIH,MAAM;IACjD;IACA,MAAMI,cAAc,GAAGC,IAAI,CAACH,UAAU,CAAC;IACvC;IACA,MAAMI,UAAU,GAAiB,EAAE;IACnC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,cAAc,CAACI,MAAM,EAAED,MAAM,IAAI,GAAG,EAAE;MAClE,MAAME,KAAK,GAAGL,cAAc,CAACK,KAAK,CAACF,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MACxD,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACF,KAAK,CAACD,MAAM,CAAC;MAC3C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACD,MAAM,EAAEI,CAAC,EAAE,EAAE;QACrCF,WAAW,CAACE,CAAC,CAAC,GAAGH,KAAK,CAACI,UAAU,CAACD,CAAC,CAAC;MACtC;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7CJ,UAAU,CAACU,IAAI,CAACF,SAAS,CAAC;IAC5B;IACA,OAAO,IAAIG,IAAI,CAACX,UAAU,EAAE;MAAEY,IAAI,EAAEjB;IAAW,CAAE,CAAC;EACpD;EAEAkB,MAAMA,CAACjC,GAAQ;IACb,IAAI,CAACkC,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5E,KAAK,CAAC6E,aAAa,CAACb,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/D,OAAO,CAAC6E,aAAa,CAAC,IAAI,CAAC9E,KAAK,CAAC6E,aAAa,CAAC;MACpD;IACF;IAEA,IAAIE,OAAO,GAAsD;MAC/DxD,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACf,QAAQ,GAAG,IAAI,CAACF,WAAW,GAAGI,SAAS;QAC1DkC,gBAAgB,EAAE,IAAI,CAACpC,QAAQ,GAAGE,SAAS,GAAG,IAAI,CAAC0B,WAAW,CAACS,GAAG;QAClER,OAAO,EAAE,IAAI,CAACD,WAAW,CAACC,OAAO;QACjC0C,aAAa,EAAE,IAAI,CAAC3C,WAAW,CAAC2C,aAAa,GAAG,IAAI,CAAC3C,WAAW,CAAC2C,aAAa,GAAGrE,SAAS;QAC1FsE,KAAK,EAAE,IAAI,CAACxE,QAAQ,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,CAACC,YAAY,CAACwE,WAAW;QACpExD,aAAa,EAAE,IAAI,CAACW,WAAW,CAACX,aAAa,GAAG,IAAI,CAACW,WAAW,CAACX,aAAa,GAAGf;;KAEpF;IACD,IAAI,CAACT,oBAAoB,CACtBiF,0CAA0C,CAACJ,OAAO,CAAC,CACnDjD,SAAS,CAAEC,GAAG,IAAI;MACjB,IAAIA,GAAG,CAACqB,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACpD,OAAO,CAACmF,YAAY,CAACrD,GAAG,CAACsD,OAAQ,CAAC;MACzC;MACA3C,GAAG,CAAC4C,KAAK,EAAE;MACX,IAAI,CAAClE,oBAAoB,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;EACN;EAEAwD,UAAUA,CAAA;IACR,IAAI,CAAC5E,KAAK,CAACuF,KAAK,EAAE;IAClB,IAAI,CAACvF,KAAK,CAACwF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnD,WAAW,CAAC2C,aAAa,CAAC;IAC3D,IAAI,CAAChF,KAAK,CAACwF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnD,WAAW,CAACX,aAAa,CAAC;IAC3D,IAAI,IAAI,CAACjB,QAAQ,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACvC,IAAI,CAACV,KAAK,CAACyF,eAAe,CAAC,OAAO,CAAC;IACrC;EACF;EAEAC,YAAYA,CAACC,KAAU;IACrB,IAAIC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC9B,MAAME,SAAS,GAAG,mBAAmB;IAErC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAClB,IAAI,CAAC,EAAE;MAClC,IAAI,CAACzE,OAAO,CAACmF,YAAY,CAAC,kBAAkB,CAAC;MAC7C;IACF;IAEA,IAAIY,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC7BD,MAAM,CAACE,aAAa,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9BI,MAAM,CAACG,MAAM,GAAIC,CAAC,IAAI;MACpB,IAAIC,IAAI,GAAW,EAAE;MACrB,IAAI,CAACT,KAAK,CAAC,CAAC,CAAC,CAACU,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAClCF,IAAI,GAAGG,GAAG,CAACC,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC;MACA,IAAI,CAAClF,YAAY,GAAG;QAClBe,KAAK,EAAEmE,KAAK,CAAC,CAAC,CAAC,CAACU,IAAI;QACpBrB,KAAK,EAAEmB,CAAC,CAACP,MAAM,EAAEa,MAAM,EAAEC,QAAQ,EAAE,CAAChD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDiD,IAAI,EAAEhB,KAAK,CAAC,CAAC,CAAC,CAACU,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,GAAGX,KAAK,CAAC,CAAC,CAAC,GAAGS,IAAI;QACrDnB,WAAW,EAAEU,KAAK,CAAC,CAAC,CAAC;QACrBiB,SAAS,EAAEvH,YAAY,CAACwH;OACzB;MACDnB,KAAK,CAACE,MAAM,CAACkB,KAAK,GAAG,IAAI;IAC3B,CAAC;EACH;EAEAC,cAAcA,CAACrB,KAAU;IACvB,KAAK,IAAI3C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG2C,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC5B,MAAM,EAAEhB,KAAK,EAAE,EAAE;MAC9D,MAAMqD,IAAI,GAAGV,KAAK,CAACE,MAAM,CAACD,KAAK,CAAC5C,KAAK,CAAC;MACtC,IAAIqD,IAAI,EAAE;QACR,IAAIL,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACG,IAAI,CAAC;QAC1BL,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIc,SAAS,GAAWjB,MAAM,CAACU,MAAgB;UAC/C,IAAI,CAACO,SAAS,EAAE;YACd;UACF;UACAZ,IAAI,CAACa,EAAE,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;UAC9B,IAAI,CAACxG,SAAS,CAAC4D,IAAI,CAAC;YAClB0C,EAAE,EAAE,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBd,IAAI,EAAED,IAAI,CAACC,IAAI;YACfe,IAAI,EAAEJ,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClH,eAAe,CAACmH,gBAAgB,CAAClB,IAAI,CAACC,IAAI,CAAC;YAC3DrB,KAAK,EAAEoB;WACR,CAAC;UACFV,KAAK,CAACE,MAAM,CAACkB,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEAS,UAAUA,CAAC7E,IAAS;IAClB,IAAIM,MAAM,CAACC,OAAO,CAAC,SAASP,IAAI,CAAClB,KAAK,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACf,YAAY,GAAGC,SAAS;IAC/B;EACF;EAEA8G,UAAUA,CAACP,EAAU;IACnB,IAAI,CAACtG,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8G,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,IAAIA,EAAE,CAAC;EACzD;EAEAU,kBAAkBA,CAAA;IAChB,IAAI,CAAC5H,KAAK,CAACuF,KAAK,EAAE;IAClB,IAAI,CAACvF,KAAK,CAACwF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnD,WAAW,CAACX,aAAa,CAAC;IAC3D,IAAI,IAAI,CAACjB,QAAQ,IAAI,IAAI,CAACG,SAAS,CAACoD,MAAO,IAAI,CAAC,EAAE;MAChD,IAAI,CAAChE,KAAK,CAACyF,eAAe,CAAC,OAAO,CAAC;IACrC;EACF;EAEAoC,cAAcA,CAACnF,GAAQ;IACrB,IAAI,CAACkF,kBAAkB,EAAE;IACzB,IAAI,IAAI,CAAC5H,KAAK,CAAC6E,aAAa,CAACb,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/D,OAAO,CAAC6E,aAAa,CAAC,IAAI,CAAC9E,KAAK,CAAC6E,aAAa,CAAC;MACpD;IACF;IAEA,IAAIiD,eAAe,GAAG,IAAI,CAAClH,SAAS,CAACmH,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAAC1C,KAAK,CAAC;IAEtD,IAAI,CAAC/E,oBAAoB,CAAC8H,kDAAkD,CAAC;MAC3EzG,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACjB,WAAW;QAC9BmB,aAAa,EAAE,IAAI,CAACW,WAAW,CAACX,aAAc;QAC9CuG,MAAM,EAAEH;;KAEX,CAAC,CAACjG,IAAI,CACLlC,GAAG,CAACoC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqB,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;QAClCX,GAAG,CAAC4C,KAAK,EAAE;QACX,IAAI,CAAClE,oBAAoB,CAAC,CAAC,CAAC;QAC5B,IAAI,CAACR,SAAS,GAAG,EAAE;QACnB,IAAI,CAACyB,WAAW,GAAG,EAAE;MACvB,CAAC,MAAM;QACL,IAAI,CAACpC,OAAO,CAACmF,YAAY,CAACrD,GAAG,CAACsD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,CACH,CAACvD,SAAS,EAAE;EACf;EAEAoG,iBAAiBA,CAAC7G,IAAY;IAC5B,IAAI,CAACP,WAAW,GAAGO,IAAI;IACvB,IAAI,CAACD,oBAAoB,CAACC,IAAI,CAAC;EACjC;EAEA8G,UAAUA,CAACC,QAAgB;IACzB,OAAO,IAAI,CAAChI,eAAe,CAACmH,gBAAgB,CAACa,QAAQ,CAAC,CAAC7B,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAACnG,eAAe,CAACmH,gBAAgB,CAACa,QAAQ,CAAC,CAAC7B,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAACnG,eAAe,CAACmH,gBAAgB,CAACa,QAAQ,CAAC,CAAC7B,QAAQ,CAAC,KAAK,CAAC;EAC/M;EAEA8B,WAAWA,CAACD,QAAgB;IAC1BE,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;IAErBnF,MAAM,CAACV,IAAI,CAAC,GAAG6F,QAAQ,EAAE,EAAE,QAAQ,CAAC;EACtC;CACD;AA1RYxI,yBAAyB,GAAA4I,UAAA,EAPrCvJ,SAAS,CAAC;EACTwJ,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACvJ,YAAY,EAAEI,YAAY,EAAED,kBAAkB,EAAEG,kBAAkB,CAAC;EAC7EkJ,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACWjJ,yBAAyB,CA0RrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}