{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport let HtmlPipe = /*#__PURE__*/(() => {\n  class HtmlPipe {\n    constructor(sanitizer) {\n      this.sanitizer = sanitizer;\n    }\n    transform(style) {\n      return this.sanitizer.bypassSecurityTrustHtml(style);\n    }\n    static {\n      this.ɵfac = function HtmlPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HtmlPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"html\",\n        type: HtmlPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return HtmlPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}