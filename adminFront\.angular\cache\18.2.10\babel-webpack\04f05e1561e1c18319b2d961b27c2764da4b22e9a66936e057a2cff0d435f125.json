{"ast": null, "code": "import { BaseComponent } from './components/base/baseComponent';\nimport { NbMenuItem, NbMenuModule } from '@nebular/theme';\nimport { RouterOutlet } from '@angular/router';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { OneColumnLayoutComponent } from '../@theme/layouts/one-column/one-column.layout';\nimport { LocalStorageService } from '../shared/services/local-storage.service';\nimport { STORAGE_KEY } from '../shared/constant/constant';\nimport { EEvent } from '../shared/services/event.service';\nimport { tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"./components/shared.observable\";\nimport * as i3 from \"../shared/helper/allowHelper\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"../shared/services/event.service\";\nimport * as i6 from \"@nebular/theme\";\nimport * as i7 from \"ngx-spinner\";\nexport let PagesComponent = /*#__PURE__*/(() => {\n  class PagesComponent extends BaseComponent {\n    constructor(userService, share, allow, router, _eventService) {\n      super(allow);\n      this.userService = userService;\n      this.share = share;\n      this.allow = allow;\n      this.router = router;\n      this._eventService = _eventService;\n      this.allowMenu = [];\n      this.nbMenu = [];\n      this.menu = {};\n      console.log('%cWelcome cdp', 'color: green; font-family: sans-serif; font-size: 4.5em; font-weight: bolder; text-shadow: #000 1px 1px;');\n      this.share.SharedMenu.subscribe(res => {\n        this.menu = res;\n      });\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"CHANGE_ROLE\" /* EEvent.CHANGE_ROLE */) {\n          this.getMenu();\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getMenu();\n    }\n    getMenu() {\n      this.userService.apiUserGetMenuPost$Json({\n        body: {}\n      }).subscribe(res => {\n        let _menu;\n        _menu = [];\n        res.Entries?.Menu.filter(x => x.CIsMenu === true).forEach(item => {\n          const _item = new NbMenuItem();\n          _item.title = item.CName;\n          _item.icon = item.CCssStyle;\n          _item.link = item.CPageUrl;\n          if (item.Child.length > 0) {\n            _item.children = [];\n            item.Child.forEach(subItem => {\n              if (subItem.Child.length > 0) {\n                const _subItem = new NbMenuItem();\n                _subItem.title = subItem.CName;\n                _subItem.link = subItem.CPageUrl;\n                _item.children.push(_subItem);\n              }\n              // 取得權限\n              subItem.Child.forEach(threeItem => {\n                this.allowMenu.push({\n                  CPageUrl: subItem.CPageUrl,\n                  CCompetenceType: threeItem.CCompetenceType\n                });\n              });\n            });\n            console.log(item);\n            console.log(item.Child.some(x => x.Child.length > 0));\n            if (item.Child.some(x => x.Child.length > 0)) {\n              _menu.push(_item);\n            }\n          }\n        });\n        this.nbMenu = [..._menu];\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.ALLOW, JSON.stringify(this.allowMenu));\n        this.share.SetMenu(res.Entries);\n        this.share.SetMenuTab(this.nbMenu);\n      });\n    }\n    static {\n      this.ɵfac = function PagesComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PagesComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.SharedObservable), i0.ɵɵdirectiveInject(i3.AllowHelper), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PagesComponent,\n        selectors: [[\"ngx-pages\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 6,\n        vars: 2,\n        consts: [[3, \"items\"], [\"bdColor\", \"rgba(0, 0, 0, 0.8)\", \"size\", \"medium\", \"color\", \"#fff\", \"type\", \"square-jelly-box\", 3, \"fullScreen\"], [2, \"color\", \"white\"]],\n        template: function PagesComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"ngx-one-column-layout\");\n            i0.ɵɵelement(1, \"nb-menu\", 0)(2, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"ngx-spinner\", 1)(4, \"p\", 2);\n            i0.ɵɵtext(5, \" Loading... \");\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"items\", ctx.nbMenu);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"fullScreen\", true);\n          }\n        },\n        dependencies: [OneColumnLayoutComponent, NbMenuModule, i6.NbMenuComponent, RouterOutlet, NgxSpinnerModule, i7.NgxSpinnerComponent],\n        styles: [\"\\n\\n\\n\\n\\n[_nghost-%COMP%]     router-outlet+*{display:block;animation:_ngcontent-%COMP%_fade 1s}@keyframes _ngcontent-%COMP%_fade{0%{opacity:0}to{opacity:1}}\"]\n      });\n    }\n  }\n  return PagesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}