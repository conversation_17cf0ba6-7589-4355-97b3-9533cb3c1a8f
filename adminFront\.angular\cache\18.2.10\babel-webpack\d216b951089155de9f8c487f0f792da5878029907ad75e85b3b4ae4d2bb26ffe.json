{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ImageCarouselComponent } from '../image-carousel/image-carousel.component';\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class ImageGalleryComponent {\n  constructor() {\n    this.images = [];\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square';\n    this.autoplay = false;\n    this.autoplayInterval = 3000;\n    this.enableKeyboard = true;\n    this.enableModal = true;\n    this.enableClickOutsideToClose = true;\n    this.showImageInfo = true;\n    this.containerClass = '';\n    this.imageClass = '';\n    this.imageClick = new EventEmitter();\n    this.indexChange = new EventEmitter();\n    this.modalOpen = new EventEmitter();\n    this.modalClose = new EventEmitter();\n    this.currentIndex = 0;\n    this.isModalVisible = false;\n  }\n  ngOnInit() {\n    this.currentIndex = 0;\n  }\n  ngOnDestroy() {\n    // 確保模態關閉時移除body class\n    if (this.isModalVisible) {\n      document.body.classList.remove('modal-open');\n      document.body.style.overflow = 'auto';\n    }\n  }\n  // 處理輪播圖片點擊\n  onImageClick(event) {\n    this.currentIndex = event.index;\n    const galleryEvent = {\n      index: event.index,\n      image: this.images[event.index],\n      openModal: this.enableModal\n    };\n    this.imageClick.emit(galleryEvent);\n    if (this.enableModal) {\n      this.openModal();\n    }\n  }\n  // 處理輪播索引變化\n  onCarouselIndexChange(index) {\n    this.currentIndex = index;\n    this.indexChange.emit(index);\n  }\n  // 打開模態窗口\n  openModal(index) {\n    if (index !== undefined && index >= 0 && index < this.images.length) {\n      this.currentIndex = index;\n    }\n    this.isModalVisible = true;\n    this.modalOpen.emit(this.currentIndex);\n  }\n  // 關閉模態窗口\n  closeModal() {\n    this.isModalVisible = false;\n    this.modalClose.emit();\n  }\n  // 處理模態窗口索引變化\n  onModalIndexChange(index) {\n    this.currentIndex = index;\n    this.indexChange.emit(index);\n  }\n  // 轉換數據格式：輪播組件格式\n  get carouselImages() {\n    return this.images.map(image => ({\n      url: image.thumbnailUrl || image.url,\n      name: image.name,\n      description: image.description,\n      alt: image.alt\n    }));\n  }\n  // 轉換數據格式：模態組件格式\n  get modalImages() {\n    return this.images.map(image => ({\n      url: image.url,\n      name: image.name,\n      description: image.description,\n      alt: image.alt,\n      title: image.title\n    }));\n  }\n  // 手動導航方法\n  goToNext() {\n    if (this.images.length <= 1) return;\n    const newIndex = (this.currentIndex + 1) % this.images.length;\n    this.currentIndex = newIndex;\n    this.indexChange.emit(newIndex);\n  }\n  goToPrevious() {\n    if (this.images.length <= 1) return;\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n    this.currentIndex = newIndex;\n    this.indexChange.emit(newIndex);\n  }\n  goToIndex(index) {\n    if (index >= 0 && index < this.images.length) {\n      this.currentIndex = index;\n      this.indexChange.emit(index);\n    }\n  }\n  // 取得當前圖片\n  getCurrentImage() {\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\n      return null;\n    }\n    return this.images[this.currentIndex];\n  }\n  static {\n    this.ɵfac = function ImageGalleryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageGalleryComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageGalleryComponent,\n      selectors: [[\"ngx-image-gallery\"]],\n      inputs: {\n        images: \"images\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        aspectRatio: \"aspectRatio\",\n        autoplay: \"autoplay\",\n        autoplayInterval: \"autoplayInterval\",\n        enableKeyboard: \"enableKeyboard\",\n        enableModal: \"enableModal\",\n        enableClickOutsideToClose: \"enableClickOutsideToClose\",\n        showImageInfo: \"showImageInfo\",\n        containerClass: \"containerClass\",\n        imageClass: \"imageClass\"\n      },\n      outputs: {\n        imageClick: \"imageClick\",\n        indexChange: \"indexChange\",\n        modalOpen: \"modalOpen\",\n        modalClose: \"modalClose\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 21,\n      consts: [[1, \"image-gallery-wrapper\"], [3, \"imageClick\", \"indexChange\", \"images\", \"currentIndex\", \"showThumbnails\", \"showCounter\", \"showNavigation\", \"aspectRatio\", \"autoplay\", \"autoplayInterval\", \"enableKeyboard\", \"imageClass\"], [3, \"close\", \"indexChange\", \"images\", \"currentIndex\", \"isVisible\", \"showThumbnails\", \"showCounter\", \"showNavigation\", \"showImageInfo\", \"enableKeyboard\", \"enableClickOutsideToClose\"]],\n      template: function ImageGalleryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ngx-image-carousel\", 1);\n          i0.ɵɵlistener(\"imageClick\", function ImageGalleryComponent_Template_ngx_image_carousel_imageClick_1_listener($event) {\n            return ctx.onImageClick($event);\n          })(\"indexChange\", function ImageGalleryComponent_Template_ngx_image_carousel_indexChange_1_listener($event) {\n            return ctx.onCarouselIndexChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"ngx-image-modal\", 2);\n          i0.ɵɵlistener(\"close\", function ImageGalleryComponent_Template_ngx_image_modal_close_2_listener() {\n            return ctx.closeModal();\n          })(\"indexChange\", function ImageGalleryComponent_Template_ngx_image_modal_indexChange_2_listener($event) {\n            return ctx.onModalIndexChange($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.carouselImages)(\"currentIndex\", ctx.currentIndex)(\"showThumbnails\", ctx.showThumbnails)(\"showCounter\", ctx.showCounter)(\"showNavigation\", ctx.showNavigation)(\"aspectRatio\", ctx.aspectRatio)(\"autoplay\", ctx.autoplay)(\"autoplayInterval\", ctx.autoplayInterval)(\"enableKeyboard\", ctx.enableKeyboard && !ctx.isModalVisible)(\"imageClass\", ctx.imageClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.modalImages)(\"currentIndex\", ctx.currentIndex)(\"isVisible\", ctx.isModalVisible)(\"showThumbnails\", ctx.showThumbnails)(\"showCounter\", ctx.showCounter)(\"showNavigation\", ctx.showNavigation)(\"showImageInfo\", ctx.showImageInfo)(\"enableKeyboard\", ctx.enableKeyboard)(\"enableClickOutsideToClose\", ctx.enableClickOutsideToClose);\n        }\n      },\n      dependencies: [CommonModule, ImageCarouselComponent, ImageModalComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  width: 100%;\\n}\\n\\n.image-gallery-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.image-gallery-wrapper[_ngcontent-%COMP%]   ngx-image-modal[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 9999;\\n}\\n.image-gallery-wrapper.modal-open[_ngcontent-%COMP%]   ngx-image-carousel[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  opacity: 0.8;\\n  filter: blur(1px);\\n  transition: all 0.3s ease;\\n}\\n[_ngcontent-%COMP%]:global   .modal-open[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n[_ngcontent-%COMP%]:global   .modal-open[_ngcontent-%COMP%]   .image-gallery-wrapper[_ngcontent-%COMP%]   ngx-image-carousel[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n}\\n\\n.image-gallery-wrapper[_ngcontent-%COMP%] {\\n  --gallery-border-radius: 0.75rem;\\n  --gallery-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\\n  --gallery-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --gallery-transition: all 0.3s ease;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ImageCarouselComponent", "ImageModalComponent", "ImageGalleryComponent", "constructor", "images", "showThumbnails", "showCounter", "showNavigation", "aspectRatio", "autoplay", "autoplayInterval", "enableKeyboard", "enableModal", "enableClickOutsideToClose", "showImageInfo", "containerClass", "imageClass", "imageClick", "indexChange", "modalOpen", "modalClose", "currentIndex", "isModalVisible", "ngOnInit", "ngOnDestroy", "document", "body", "classList", "remove", "style", "overflow", "onImageClick", "event", "index", "galleryEvent", "image", "openModal", "emit", "onCarouselIndexChange", "undefined", "length", "closeModal", "onModalIndexChange", "carouselImages", "map", "url", "thumbnailUrl", "name", "description", "alt", "modalImages", "title", "goToNext", "newIndex", "goToPrevious", "goToIndex", "getCurrentImage", "selectors", "inputs", "outputs", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ImageGalleryComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ImageGalleryComponent_Template_ngx_image_carousel_imageClick_1_listener", "$event", "ImageGalleryComponent_Template_ngx_image_carousel_indexChange_1_listener", "ɵɵelementEnd", "ImageGalleryComponent_Template_ngx_image_modal_close_2_listener", "ImageGalleryComponent_Template_ngx_image_modal_indexChange_2_listener", "ɵɵclassMap", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-gallery\\image-gallery.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-gallery\\image-gallery.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImageCarouselComponent, ImageCarouselItem, CarouselClickEvent } from '../image-carousel/image-carousel.component';\r\nimport { ImageModalComponent, ImageModalItem } from '../image-modal/image-modal.component';\r\n\r\nexport interface ImageGalleryItem {\r\n  url: string;\r\n  name?: string;\r\n  description?: string;\r\n  alt?: string;\r\n  title?: string;\r\n  thumbnailUrl?: string; // 可選的縮略圖URL\r\n}\r\n\r\nexport interface GalleryImageClickEvent {\r\n  index: number;\r\n  image: ImageGalleryItem;\r\n  openModal: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-image-gallery',\r\n  standalone: true,\r\n  imports: [CommonModule, ImageCarouselComponent, ImageModalComponent],\r\n  templateUrl: './image-gallery.component.html',\r\n  styleUrls: ['./image-gallery.component.scss']\r\n})\r\nexport class ImageGalleryComponent implements OnInit, OnDestroy {\r\n  @Input() images: ImageGalleryItem[] = [];\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() aspectRatio: string = 'aspect-square';\r\n  @Input() autoplay: boolean = false;\r\n  @Input() autoplayInterval: number = 3000;\r\n  @Input() enableKeyboard: boolean = true;\r\n  @Input() enableModal: boolean = true;\r\n  @Input() enableClickOutsideToClose: boolean = true;\r\n  @Input() showImageInfo: boolean = true;\r\n  @Input() containerClass: string = '';\r\n  @Input() imageClass: string = '';\r\n\r\n  @Output() imageClick = new EventEmitter<GalleryImageClickEvent>();\r\n  @Output() indexChange = new EventEmitter<number>();\r\n  @Output() modalOpen = new EventEmitter<number>();\r\n  @Output() modalClose = new EventEmitter<void>();\r\n\r\n  currentIndex: number = 0;\r\n  isModalVisible: boolean = false;\r\n\r\n  ngOnInit(): void {\r\n    this.currentIndex = 0;\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保模態關閉時移除body class\r\n    if (this.isModalVisible) {\r\n      document.body.classList.remove('modal-open');\r\n      document.body.style.overflow = 'auto';\r\n    }\r\n  }\r\n\r\n  // 處理輪播圖片點擊\r\n  onImageClick(event: CarouselClickEvent): void {\r\n    this.currentIndex = event.index;\r\n    \r\n    const galleryEvent: GalleryImageClickEvent = {\r\n      index: event.index,\r\n      image: this.images[event.index],\r\n      openModal: this.enableModal\r\n    };\r\n    \r\n    this.imageClick.emit(galleryEvent);\r\n    \r\n    if (this.enableModal) {\r\n      this.openModal();\r\n    }\r\n  }\r\n\r\n  // 處理輪播索引變化\r\n  onCarouselIndexChange(index: number): void {\r\n    this.currentIndex = index;\r\n    this.indexChange.emit(index);\r\n  }\r\n\r\n  // 打開模態窗口\r\n  openModal(index?: number): void {\r\n    if (index !== undefined && index >= 0 && index < this.images.length) {\r\n      this.currentIndex = index;\r\n    }\r\n    \r\n    this.isModalVisible = true;\r\n    this.modalOpen.emit(this.currentIndex);\r\n  }\r\n\r\n  // 關閉模態窗口\r\n  closeModal(): void {\r\n    this.isModalVisible = false;\r\n    this.modalClose.emit();\r\n  }\r\n\r\n  // 處理模態窗口索引變化\r\n  onModalIndexChange(index: number): void {\r\n    this.currentIndex = index;\r\n    this.indexChange.emit(index);\r\n  }\r\n\r\n  // 轉換數據格式：輪播組件格式\r\n  get carouselImages(): ImageCarouselItem[] {\r\n    return this.images.map(image => ({\r\n      url: image.thumbnailUrl || image.url,\r\n      name: image.name,\r\n      description: image.description,\r\n      alt: image.alt\r\n    }));\r\n  }\r\n\r\n  // 轉換數據格式：模態組件格式\r\n  get modalImages(): ImageModalItem[] {\r\n    return this.images.map(image => ({\r\n      url: image.url,\r\n      name: image.name,\r\n      description: image.description,\r\n      alt: image.alt,\r\n      title: image.title\r\n    }));\r\n  }\r\n\r\n  // 手動導航方法\r\n  goToNext(): void {\r\n    if (this.images.length <= 1) return;\r\n    const newIndex = (this.currentIndex + 1) % this.images.length;\r\n    this.currentIndex = newIndex;\r\n    this.indexChange.emit(newIndex);\r\n  }\r\n\r\n  goToPrevious(): void {\r\n    if (this.images.length <= 1) return;\r\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n    this.currentIndex = newIndex;\r\n    this.indexChange.emit(newIndex);\r\n  }\r\n\r\n  goToIndex(index: number): void {\r\n    if (index >= 0 && index < this.images.length) {\r\n      this.currentIndex = index;\r\n      this.indexChange.emit(index);\r\n    }\r\n  }\r\n\r\n  // 取得當前圖片\r\n  getCurrentImage(): ImageGalleryItem | null {\r\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\r\n      return null;\r\n    }\r\n    return this.images[this.currentIndex];\r\n  }\r\n}\r\n", "<!-- 圖片畫廊組件 -->\r\n<div class=\"image-gallery-wrapper\" [class]=\"containerClass\">\r\n  \r\n  <!-- 輪播組件 -->\r\n  <ngx-image-carousel\r\n    [images]=\"carouselImages\"\r\n    [currentIndex]=\"currentIndex\"\r\n    [showThumbnails]=\"showThumbnails\"\r\n    [showCounter]=\"showCounter\"\r\n    [showNavigation]=\"showNavigation\"\r\n    [aspectRatio]=\"aspectRatio\"\r\n    [autoplay]=\"autoplay\"\r\n    [autoplayInterval]=\"autoplayInterval\"\r\n    [enableKeyboard]=\"enableKeyboard && !isModalVisible\"\r\n    [imageClass]=\"imageClass\"\r\n    (imageClick)=\"onImageClick($event)\"\r\n    (indexChange)=\"onCarouselIndexChange($event)\">\r\n  </ngx-image-carousel>\r\n\r\n  <!-- 模態窗口組件 -->\r\n  <ngx-image-modal\r\n    [images]=\"modalImages\"\r\n    [currentIndex]=\"currentIndex\"\r\n    [isVisible]=\"isModalVisible\"\r\n    [showThumbnails]=\"showThumbnails\"\r\n    [showCounter]=\"showCounter\"\r\n    [showNavigation]=\"showNavigation\"\r\n    [showImageInfo]=\"showImageInfo\"\r\n    [enableKeyboard]=\"enableKeyboard\"\r\n    [enableClickOutsideToClose]=\"enableClickOutsideToClose\"\r\n    (close)=\"closeModal()\"\r\n    (indexChange)=\"onModalIndexChange($event)\">\r\n  </ngx-image-modal>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAA+C,4CAA4C;AAC1H,SAASC,mBAAmB,QAAwB,sCAAsC;;AAwB1F,OAAM,MAAOC,qBAAqB;EAPlCC,YAAA;IAQW,KAAAC,MAAM,GAAuB,EAAE;IAC/B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAW,eAAe;IACrC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAW,IAAI;IAC/B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,yBAAyB,GAAY,IAAI;IACzC,KAAAC,aAAa,GAAY,IAAI;IAC7B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,UAAU,GAAW,EAAE;IAEtB,KAAAC,UAAU,GAAG,IAAInB,YAAY,EAA0B;IACvD,KAAAoB,WAAW,GAAG,IAAIpB,YAAY,EAAU;IACxC,KAAAqB,SAAS,GAAG,IAAIrB,YAAY,EAAU;IACtC,KAAAsB,UAAU,GAAG,IAAItB,YAAY,EAAQ;IAE/C,KAAAuB,YAAY,GAAW,CAAC;IACxB,KAAAC,cAAc,GAAY,KAAK;;EAE/BC,QAAQA,CAAA;IACN,IAAI,CAACF,YAAY,GAAG,CAAC;EACvB;EAEAG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACF,cAAc,EAAE;MACvBG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC5CH,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,MAAM;IACvC;EACF;EAEA;EACAC,YAAYA,CAACC,KAAyB;IACpC,IAAI,CAACX,YAAY,GAAGW,KAAK,CAACC,KAAK;IAE/B,MAAMC,YAAY,GAA2B;MAC3CD,KAAK,EAAED,KAAK,CAACC,KAAK;MAClBE,KAAK,EAAE,IAAI,CAAC/B,MAAM,CAAC4B,KAAK,CAACC,KAAK,CAAC;MAC/BG,SAAS,EAAE,IAAI,CAACxB;KACjB;IAED,IAAI,CAACK,UAAU,CAACoB,IAAI,CAACH,YAAY,CAAC;IAElC,IAAI,IAAI,CAACtB,WAAW,EAAE;MACpB,IAAI,CAACwB,SAAS,EAAE;IAClB;EACF;EAEA;EACAE,qBAAqBA,CAACL,KAAa;IACjC,IAAI,CAACZ,YAAY,GAAGY,KAAK;IACzB,IAAI,CAACf,WAAW,CAACmB,IAAI,CAACJ,KAAK,CAAC;EAC9B;EAEA;EACAG,SAASA,CAACH,KAAc;IACtB,IAAIA,KAAK,KAAKM,SAAS,IAAIN,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC7B,MAAM,CAACoC,MAAM,EAAE;MACnE,IAAI,CAACnB,YAAY,GAAGY,KAAK;IAC3B;IAEA,IAAI,CAACX,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACH,SAAS,CAACkB,IAAI,CAAC,IAAI,CAAChB,YAAY,CAAC;EACxC;EAEA;EACAoB,UAAUA,CAAA;IACR,IAAI,CAACnB,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,UAAU,CAACiB,IAAI,EAAE;EACxB;EAEA;EACAK,kBAAkBA,CAACT,KAAa;IAC9B,IAAI,CAACZ,YAAY,GAAGY,KAAK;IACzB,IAAI,CAACf,WAAW,CAACmB,IAAI,CAACJ,KAAK,CAAC;EAC9B;EAEA;EACA,IAAIU,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACvC,MAAM,CAACwC,GAAG,CAACT,KAAK,KAAK;MAC/BU,GAAG,EAAEV,KAAK,CAACW,YAAY,IAAIX,KAAK,CAACU,GAAG;MACpCE,IAAI,EAAEZ,KAAK,CAACY,IAAI;MAChBC,WAAW,EAAEb,KAAK,CAACa,WAAW;MAC9BC,GAAG,EAAEd,KAAK,CAACc;KACZ,CAAC,CAAC;EACL;EAEA;EACA,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC9C,MAAM,CAACwC,GAAG,CAACT,KAAK,KAAK;MAC/BU,GAAG,EAAEV,KAAK,CAACU,GAAG;MACdE,IAAI,EAAEZ,KAAK,CAACY,IAAI;MAChBC,WAAW,EAAEb,KAAK,CAACa,WAAW;MAC9BC,GAAG,EAAEd,KAAK,CAACc,GAAG;MACdE,KAAK,EAAEhB,KAAK,CAACgB;KACd,CAAC,CAAC;EACL;EAEA;EACAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChD,MAAM,CAACoC,MAAM,IAAI,CAAC,EAAE;IAC7B,MAAMa,QAAQ,GAAG,CAAC,IAAI,CAAChC,YAAY,GAAG,CAAC,IAAI,IAAI,CAACjB,MAAM,CAACoC,MAAM;IAC7D,IAAI,CAACnB,YAAY,GAAGgC,QAAQ;IAC5B,IAAI,CAACnC,WAAW,CAACmB,IAAI,CAACgB,QAAQ,CAAC;EACjC;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClD,MAAM,CAACoC,MAAM,IAAI,CAAC,EAAE;IAC7B,MAAMa,QAAQ,GAAG,IAAI,CAAChC,YAAY,KAAK,CAAC,GAAG,IAAI,CAACjB,MAAM,CAACoC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnB,YAAY,GAAG,CAAC;IACzF,IAAI,CAACA,YAAY,GAAGgC,QAAQ;IAC5B,IAAI,CAACnC,WAAW,CAACmB,IAAI,CAACgB,QAAQ,CAAC;EACjC;EAEAE,SAASA,CAACtB,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC7B,MAAM,CAACoC,MAAM,EAAE;MAC5C,IAAI,CAACnB,YAAY,GAAGY,KAAK;MACzB,IAAI,CAACf,WAAW,CAACmB,IAAI,CAACJ,KAAK,CAAC;IAC9B;EACF;EAEA;EACAuB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpD,MAAM,CAACoC,MAAM,KAAK,CAAC,IAAI,IAAI,CAACnB,YAAY,GAAG,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACjB,MAAM,CAACoC,MAAM,EAAE;MAChG,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACpC,MAAM,CAAC,IAAI,CAACiB,YAAY,CAAC;EACvC;;;uCAjIWnB,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAuD,SAAA;MAAAC,MAAA;QAAAtD,MAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,QAAA;QAAAC,gBAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,yBAAA;QAAAC,aAAA;QAAAC,cAAA;QAAAC,UAAA;MAAA;MAAA2C,OAAA;QAAA1C,UAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,UAAA;MAAA;MAAAwC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBhCP,EAHF,CAAAS,cAAA,aAA4D,4BAeV;UAA9CT,EADA,CAAAU,UAAA,wBAAAC,wEAAAC,MAAA;YAAA,OAAcJ,GAAA,CAAAvC,YAAA,CAAA2C,MAAA,CAAoB;UAAA,EAAC,yBAAAC,yEAAAD,MAAA;YAAA,OACpBJ,GAAA,CAAAhC,qBAAA,CAAAoC,MAAA,CAA6B;UAAA,EAAC;UAC/CZ,EAAA,CAAAc,YAAA,EAAqB;UAGrBd,EAAA,CAAAS,cAAA,yBAW6C;UAA3CT,EADA,CAAAU,UAAA,mBAAAK,gEAAA;YAAA,OAASP,GAAA,CAAA7B,UAAA,EAAY;UAAA,EAAC,yBAAAqC,sEAAAJ,MAAA;YAAA,OACPJ,GAAA,CAAA5B,kBAAA,CAAAgC,MAAA,CAA0B;UAAA,EAAC;UAG9CZ,EAFE,CAAAc,YAAA,EAAkB,EAEd;;;UAjC6Bd,EAAA,CAAAiB,UAAA,CAAAT,GAAA,CAAAvD,cAAA,CAAwB;UAIvD+C,EAAA,CAAAkB,SAAA,EAAyB;UASzBlB,EATA,CAAAmB,UAAA,WAAAX,GAAA,CAAA3B,cAAA,CAAyB,iBAAA2B,GAAA,CAAAjD,YAAA,CACI,mBAAAiD,GAAA,CAAAjE,cAAA,CACI,gBAAAiE,GAAA,CAAAhE,WAAA,CACN,mBAAAgE,GAAA,CAAA/D,cAAA,CACM,gBAAA+D,GAAA,CAAA9D,WAAA,CACN,aAAA8D,GAAA,CAAA7D,QAAA,CACN,qBAAA6D,GAAA,CAAA5D,gBAAA,CACgB,mBAAA4D,GAAA,CAAA3D,cAAA,KAAA2D,GAAA,CAAAhD,cAAA,CACe,eAAAgD,GAAA,CAAAtD,UAAA,CAC3B;UAOzB8C,EAAA,CAAAkB,SAAA,EAAsB;UAQtBlB,EARA,CAAAmB,UAAA,WAAAX,GAAA,CAAApB,WAAA,CAAsB,iBAAAoB,GAAA,CAAAjD,YAAA,CACO,cAAAiD,GAAA,CAAAhD,cAAA,CACD,mBAAAgD,GAAA,CAAAjE,cAAA,CACK,gBAAAiE,GAAA,CAAAhE,WAAA,CACN,mBAAAgE,GAAA,CAAA/D,cAAA,CACM,kBAAA+D,GAAA,CAAAxD,aAAA,CACF,mBAAAwD,GAAA,CAAA3D,cAAA,CACE,8BAAA2D,GAAA,CAAAzD,yBAAA,CACsB;;;qBDN/Cd,YAAY,EAAEC,sBAAsB,EAAEC,mBAAmB;MAAAiF,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}