{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function (Math) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n    var Hasher = C_lib.Hasher;\n    var C_algo = C.algo;\n\n    // Constants table\n    var T = [];\n\n    // Compute constants\n    (function () {\n      for (var i = 0; i < 64; i++) {\n        T[i] = Math.abs(Math.sin(i + 1)) * 0x100000000 | 0;\n      }\n    })();\n\n    /**\n     * MD5 hash algorithm.\n     */\n    var MD5 = C_algo.MD5 = Hasher.extend({\n      _doReset: function () {\n        this._hash = new WordArray.init([0x67452301, 0xefcdab89, 0x98badc<PERSON>, 0x10325476]);\n      },\n      _doProcessBlock: function (M, offset) {\n        // Swap endian\n        for (var i = 0; i < 16; i++) {\n          // Shortcuts\n          var offset_i = offset + i;\n          var M_offset_i = M[offset_i];\n          M[offset_i] = (M_offset_i << 8 | M_offset_i >>> 24) & 0x00ff00ff | (M_offset_i << 24 | M_offset_i >>> 8) & 0xff00ff00;\n        }\n\n        // Shortcuts\n        var H = this._hash.words;\n        var M_offset_0 = M[offset + 0];\n        var M_offset_1 = M[offset + 1];\n        var M_offset_2 = M[offset + 2];\n        var M_offset_3 = M[offset + 3];\n        var M_offset_4 = M[offset + 4];\n        var M_offset_5 = M[offset + 5];\n        var M_offset_6 = M[offset + 6];\n        var M_offset_7 = M[offset + 7];\n        var M_offset_8 = M[offset + 8];\n        var M_offset_9 = M[offset + 9];\n        var M_offset_10 = M[offset + 10];\n        var M_offset_11 = M[offset + 11];\n        var M_offset_12 = M[offset + 12];\n        var M_offset_13 = M[offset + 13];\n        var M_offset_14 = M[offset + 14];\n        var M_offset_15 = M[offset + 15];\n\n        // Working variables\n        var a = H[0];\n        var b = H[1];\n        var c = H[2];\n        var d = H[3];\n\n        // Computation\n        a = FF(a, b, c, d, M_offset_0, 7, T[0]);\n        d = FF(d, a, b, c, M_offset_1, 12, T[1]);\n        c = FF(c, d, a, b, M_offset_2, 17, T[2]);\n        b = FF(b, c, d, a, M_offset_3, 22, T[3]);\n        a = FF(a, b, c, d, M_offset_4, 7, T[4]);\n        d = FF(d, a, b, c, M_offset_5, 12, T[5]);\n        c = FF(c, d, a, b, M_offset_6, 17, T[6]);\n        b = FF(b, c, d, a, M_offset_7, 22, T[7]);\n        a = FF(a, b, c, d, M_offset_8, 7, T[8]);\n        d = FF(d, a, b, c, M_offset_9, 12, T[9]);\n        c = FF(c, d, a, b, M_offset_10, 17, T[10]);\n        b = FF(b, c, d, a, M_offset_11, 22, T[11]);\n        a = FF(a, b, c, d, M_offset_12, 7, T[12]);\n        d = FF(d, a, b, c, M_offset_13, 12, T[13]);\n        c = FF(c, d, a, b, M_offset_14, 17, T[14]);\n        b = FF(b, c, d, a, M_offset_15, 22, T[15]);\n        a = GG(a, b, c, d, M_offset_1, 5, T[16]);\n        d = GG(d, a, b, c, M_offset_6, 9, T[17]);\n        c = GG(c, d, a, b, M_offset_11, 14, T[18]);\n        b = GG(b, c, d, a, M_offset_0, 20, T[19]);\n        a = GG(a, b, c, d, M_offset_5, 5, T[20]);\n        d = GG(d, a, b, c, M_offset_10, 9, T[21]);\n        c = GG(c, d, a, b, M_offset_15, 14, T[22]);\n        b = GG(b, c, d, a, M_offset_4, 20, T[23]);\n        a = GG(a, b, c, d, M_offset_9, 5, T[24]);\n        d = GG(d, a, b, c, M_offset_14, 9, T[25]);\n        c = GG(c, d, a, b, M_offset_3, 14, T[26]);\n        b = GG(b, c, d, a, M_offset_8, 20, T[27]);\n        a = GG(a, b, c, d, M_offset_13, 5, T[28]);\n        d = GG(d, a, b, c, M_offset_2, 9, T[29]);\n        c = GG(c, d, a, b, M_offset_7, 14, T[30]);\n        b = GG(b, c, d, a, M_offset_12, 20, T[31]);\n        a = HH(a, b, c, d, M_offset_5, 4, T[32]);\n        d = HH(d, a, b, c, M_offset_8, 11, T[33]);\n        c = HH(c, d, a, b, M_offset_11, 16, T[34]);\n        b = HH(b, c, d, a, M_offset_14, 23, T[35]);\n        a = HH(a, b, c, d, M_offset_1, 4, T[36]);\n        d = HH(d, a, b, c, M_offset_4, 11, T[37]);\n        c = HH(c, d, a, b, M_offset_7, 16, T[38]);\n        b = HH(b, c, d, a, M_offset_10, 23, T[39]);\n        a = HH(a, b, c, d, M_offset_13, 4, T[40]);\n        d = HH(d, a, b, c, M_offset_0, 11, T[41]);\n        c = HH(c, d, a, b, M_offset_3, 16, T[42]);\n        b = HH(b, c, d, a, M_offset_6, 23, T[43]);\n        a = HH(a, b, c, d, M_offset_9, 4, T[44]);\n        d = HH(d, a, b, c, M_offset_12, 11, T[45]);\n        c = HH(c, d, a, b, M_offset_15, 16, T[46]);\n        b = HH(b, c, d, a, M_offset_2, 23, T[47]);\n        a = II(a, b, c, d, M_offset_0, 6, T[48]);\n        d = II(d, a, b, c, M_offset_7, 10, T[49]);\n        c = II(c, d, a, b, M_offset_14, 15, T[50]);\n        b = II(b, c, d, a, M_offset_5, 21, T[51]);\n        a = II(a, b, c, d, M_offset_12, 6, T[52]);\n        d = II(d, a, b, c, M_offset_3, 10, T[53]);\n        c = II(c, d, a, b, M_offset_10, 15, T[54]);\n        b = II(b, c, d, a, M_offset_1, 21, T[55]);\n        a = II(a, b, c, d, M_offset_8, 6, T[56]);\n        d = II(d, a, b, c, M_offset_15, 10, T[57]);\n        c = II(c, d, a, b, M_offset_6, 15, T[58]);\n        b = II(b, c, d, a, M_offset_13, 21, T[59]);\n        a = II(a, b, c, d, M_offset_4, 6, T[60]);\n        d = II(d, a, b, c, M_offset_11, 10, T[61]);\n        c = II(c, d, a, b, M_offset_2, 15, T[62]);\n        b = II(b, c, d, a, M_offset_9, 21, T[63]);\n\n        // Intermediate hash value\n        H[0] = H[0] + a | 0;\n        H[1] = H[1] + b | 0;\n        H[2] = H[2] + c | 0;\n        H[3] = H[3] + d | 0;\n      },\n      _doFinalize: function () {\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var nBitsTotal = this._nDataBytes * 8;\n        var nBitsLeft = data.sigBytes * 8;\n\n        // Add padding\n        dataWords[nBitsLeft >>> 5] |= 0x80 << 24 - nBitsLeft % 32;\n        var nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);\n        var nBitsTotalL = nBitsTotal;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = (nBitsTotalH << 8 | nBitsTotalH >>> 24) & 0x00ff00ff | (nBitsTotalH << 24 | nBitsTotalH >>> 8) & 0xff00ff00;\n        dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = (nBitsTotalL << 8 | nBitsTotalL >>> 24) & 0x00ff00ff | (nBitsTotalL << 24 | nBitsTotalL >>> 8) & 0xff00ff00;\n        data.sigBytes = (dataWords.length + 1) * 4;\n\n        // Hash final blocks\n        this._process();\n\n        // Shortcuts\n        var hash = this._hash;\n        var H = hash.words;\n\n        // Swap endian\n        for (var i = 0; i < 4; i++) {\n          // Shortcut\n          var H_i = H[i];\n          H[i] = (H_i << 8 | H_i >>> 24) & 0x00ff00ff | (H_i << 24 | H_i >>> 8) & 0xff00ff00;\n        }\n\n        // Return final computed hash\n        return hash;\n      },\n      clone: function () {\n        var clone = Hasher.clone.call(this);\n        clone._hash = this._hash.clone();\n        return clone;\n      }\n    });\n    function FF(a, b, c, d, x, s, t) {\n      var n = a + (b & c | ~b & d) + x + t;\n      return (n << s | n >>> 32 - s) + b;\n    }\n    function GG(a, b, c, d, x, s, t) {\n      var n = a + (b & d | c & ~d) + x + t;\n      return (n << s | n >>> 32 - s) + b;\n    }\n    function HH(a, b, c, d, x, s, t) {\n      var n = a + (b ^ c ^ d) + x + t;\n      return (n << s | n >>> 32 - s) + b;\n    }\n    function II(a, b, c, d, x, s, t) {\n      var n = a + (c ^ (b | ~d)) + x + t;\n      return (n << s | n >>> 32 - s) + b;\n    }\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.MD5('message');\n     *     var hash = CryptoJS.MD5(wordArray);\n     */\n    C.MD5 = Hasher._createHelper(MD5);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacMD5(message, key);\n     */\n    C.HmacMD5 = Hasher._createHmacHelper(MD5);\n  })(Math);\n  return CryptoJS.MD5;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}