{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRemoveHousehold(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 4, 2, \"span\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r5, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getBuildingSelectedHouseholds(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasBuildingSelected(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"nb-icon\", 12);\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 24);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r1.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r1.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_17_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r1.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getBuildingCount(building_r8), \"\\u6236\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_24_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_div_24_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_div_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_8_div_24_button_5_Template, 2, 0, \"button\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_25_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r1.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"div\", 69)(2, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_div_8_div_25_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_div_8_div_25_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_8_div_25_div_4_Template, 2, 1, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm && ctx_r1.filteredHouseholds.length === 0);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵelement(1, \"nb-icon\", 75);\n    i0.ɵɵelementStart(2, \"p\", 76);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_28_button_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_28_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_div_28_button_1_Template_button_click_0_listener() {\n      const householdCode_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(householdCode_r13));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_div_8_div_28_button_1_div_3_Template, 2, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r13) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#fff\" : ctx_r1.isHouseholdExcluded(householdCode_r13) ? \"#6c757d\" : \"#495057\")(\"border-color\", ctx_r1.isHouseholdSelected(householdCode_r13) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r13) ? \"#dee2e6\" : \"#ced4da\")(\"opacity\", ctx_r1.isHouseholdDisabled(householdCode_r13) ? \"0.6\" : \"1\")(\"cursor\", ctx_r1.isHouseholdDisabled(householdCode_r13) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isHouseholdDisabled(householdCode_r13));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r1.isHouseholdExcluded(householdCode_r13) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r13, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isHouseholdExcluded(householdCode_r13));\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_8_div_28_button_1_Template, 4, 15, \"button\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"nb-icon\", 82);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵelement(1, \"nb-icon\", 29);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedBuilding);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r1.searchTerm, \"\\\" (\", ctx_r1.filteredHouseholds.length, \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_44_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 85);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_45_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 87);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵelement(4, \"nb-icon\", 29);\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 31);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 31);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 32)(12, \"div\", 33)(13, \"div\", 34)(14, \"h6\", 35);\n    i0.ɵɵtext(15, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 36);\n    i0.ɵɵtemplate(17, HouseholdBindingComponent_div_8_button_17_Template, 5, 6, \"button\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 38)(19, \"div\", 34)(20, \"div\", 39)(21, \"h6\", 35);\n    i0.ɵɵtext(22, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(23, HouseholdBindingComponent_div_8_span_23_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_div_8_div_24_Template, 6, 8, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, HouseholdBindingComponent_div_8_div_25_Template, 5, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 43);\n    i0.ɵɵtemplate(27, HouseholdBindingComponent_div_8_div_27_Template, 4, 0, \"div\", 44)(28, HouseholdBindingComponent_div_8_div_28_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 46)(30, \"div\", 47)(31, \"div\", 48)(32, \"div\", 49);\n    i0.ɵɵelement(33, \"nb-icon\", 50);\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(36, \"strong\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(39, HouseholdBindingComponent_div_8_div_39_Template, 7, 1, \"div\", 51)(40, HouseholdBindingComponent_div_8_div_40_Template, 6, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, HouseholdBindingComponent_div_8_div_41_Template, 2, 2, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 53)(43, \"div\", 54);\n    i0.ɵɵtemplate(44, HouseholdBindingComponent_div_8_button_44_Template, 3, 0, \"button\", 55)(45, HouseholdBindingComponent_div_8_button_45_Template, 3, 0, \"button\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 54)(47, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(48, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelement(50, \"nb-icon\", 59);\n    i0.ɵɵtext(51, \" \\u78BA\\u5B9A\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.selectedBuilding);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    if (this.buildCaseId) {\n      // 使用API載入資料\n      this.loadBuildingDataFromApi();\n    } else {\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n        this.buildingData = this.generateMockData();\n      }\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with buildings:', this.buildings);\n      this.updateSelectedByBuilding();\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // 如果API載入失敗，使用mock資料作為備援\n        this.buildingData = this.generateMockData();\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋過濾\n    this.filteredHouseholds = households.map(h => h.code).filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseholds: \"excludedHouseholds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 10,\n      consts: [[1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\", 2, \"position\", \"relative\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"chevron-down-outline\", 1, \"chevron-icon\"], [\"style\", \"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\", 4, \"ngIf\"], [\"class\", \"backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"position\", \"absolute\", \"top\", \"calc(100% + 4px)\", \"left\", \"0\", \"right\", \"0\", \"z-index\", \"99999\", \"background\", \"white\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"8px\", \"box-shadow\", \"0 4px 12px rgba(0,0,0,0.15)\", \"padding\", \"0\", \"margin\", \"0\", \"max-height\", \"min(80vh, 600px)\", \"overflow\", \"hidden\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"display\", \"flex\", \"height\", \"calc(100% - 160px)\", \"min-height\", \"300px\", \"max-height\", \"400px\"], [2, \"width\", \"40%\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"max-height\", \"calc(100% - 52px)\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(80px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 32px; position: relative;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"cursor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"8px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"32px\", \"position\", \"relative\", 3, \"click\", \"disabled\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"12px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"backdrop\", 3, \"click\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 5)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_div_8_Template, 52, 14, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, HouseholdBindingComponent_div_9_Template, 1, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"rotated\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #1976d2;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImhvdXNlaG9sZC1iaW5kaW5nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0FBQ0o7QUFFSTtFQUNJLGtDQUFBO0FBQVI7QUFHSTtFQUNJO0lBQU8sdUJBQUE7RUFBYjtFQUNNO0lBQUsseUJBQUE7RUFFWDtBQUNGO0FBQUk7RUFDSSxtQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7QUFFUjtBQUFRO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxzQkFBQTtBQUVaO0FBQVk7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0FBRWhCO0FBQWdCO0VBQ0ksbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFFcEI7QUFJWTtFQUNJLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxxQkFBQTtBQUZoQjtBQUlnQjtFQUNJLGdCQUFBO0FBRnBCO0FBS2dCO0VBQ0ksZUFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUFIcEI7QUFNZ0I7RUFDSSxhQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFKcEI7QUFNb0I7RUFDSSxvQkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLHVCQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQUp4QjtBQU13QjtFQUNJLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLFVBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFKNUI7QUFNNEI7RUFDSSx5QkFBQTtBQUpoQztBQU80QjtFQUNJLFlBQUE7RUFDQSxtQkFBQTtBQUxoQztBQVE0QjtFQUNJLGtCQUFBO0FBTmhDO0FBZUk7RUFDSSxrQkFBQTtBQWJSO0FBZVE7RUFDSSxXQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7RUFDQSxpQ0FBQTtBQWJaO0FBZVk7RUFDSSx5QkFBQTtFQUNBLHFCQUFBO0FBYmhCO0FBZ0JZO0VBQ0ksYUFBQTtFQUNBLHFCQUFBO0VBQ0EsZ0RBQUE7QUFkaEI7QUFpQlk7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQWZoQjtBQWtCWTtFQUNJLGNBQUE7RUFDQSxtQkFBQTtBQWhCaEI7QUFtQlk7RUFDSSxjQUFBO0VBQ0EsdUNBQUE7QUFqQmhCO0FBbUJnQjtFQUNJLHlCQUFBO0FBakJwQjtBQXNCUTtFQUNJLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2Q0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFwQlo7QUFzQlk7RUFDSSxhQUFBO0VBQ0EsYUFBQTtBQXBCaEI7QUFzQmdCO0VBQ0ksY0FBQTtFQUNBLCtCQUFBO0VBQ0EseUJBQUE7QUFwQnBCO0FBc0JvQjtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EseUJBQUE7QUFwQnhCO0FBc0J3QjtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBcEI1QjtBQXdCb0I7RUFDSSxpQkFBQTtFQUNBLGdCQUFBO0FBdEJ4QjtBQXdCd0I7RUFDSSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSw4Q0FBQTtBQXRCNUI7QUF3QjRCO0VBQ0kseUJBQUE7QUF0QmhDO0FBeUI0QjtFQUNJLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBdkJoQztBQTBCNEI7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQXhCaEM7QUEwQmdDO0VBQ0ksbUJBQUE7QUF4QnBDO0FBMkJnQztFQUNJLGtCQUFBO0VBQ0EsY0FBQTtBQXpCcEM7QUFnQ2dCO0VBQ0ksY0FBQTtBQTlCcEI7QUFnQ29CO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EseUJBQUE7QUE5QnhCO0FBZ0N3QjtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUE5QjVCO0FBZ0M0QjtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBOUJoQztBQWdDZ0M7RUFDSSxjQUFBO0FBOUJwQztBQW1Dd0I7RUFDSSxhQUFBO0VBQ0EsWUFBQTtBQWpDNUI7QUFxQ29CO0VBQ0ksZUFBQTtFQUNBLGdDQUFBO0FBbkN4QjtBQXNDb0I7RUFDSSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQXBDeEI7QUFzQ3dCO0VBQ0ksYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0FBcEM1QjtBQXNDNEI7RUFDSSxlQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBcENoQztBQXVDNEI7RUFDSSxtQkFBQTtFQUNBLFNBQUE7QUFyQ2hDO0FBeUN3QjtFQUNJLGFBQUE7RUFDQSxxQ0FBQTtFQUNBLFlBQUE7QUF2QzVCO0FBeUM0QjtFQUNJLGVBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0Esc0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUNBQUE7RUFDQSxrQkFBQTtBQXZDaEM7QUF5Q2dDO0VBQ0kseUJBQUE7QUF2Q3BDO0FBMENnQztFQUNJLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBeENwQztBQTJDZ0M7RUFDSSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7QUF6Q3BDO0FBNENnQztFQUNJLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBMUNwQztBQTZDZ0M7RUFDSSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0Esb0JBQUE7QUEzQ3BDO0FBbURZO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0VBQ0EseUJBQUE7QUFqRGhCO0FBbURnQjtFQUNJLGFBQUE7RUFDQSxTQUFBO0FBakRwQjtBQW1Eb0I7O0VBRUksa0JBQUE7RUFDQSxjQUFBO0FBakR4QjtBQXdESTtFQUNJLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0FBdERSOztBQTJEQTtFQUlnQjtJQUNJLHNCQUFBO0lBQ0EsWUFBQTtJQUNBLGlCQUFBO0VBM0RsQjtFQTZEa0I7SUFDSSxXQUFBO0lBQ0EsZ0JBQUE7SUFDQSxrQkFBQTtJQUNBLGdDQUFBO0VBM0R0QjtFQTZEc0I7SUFDSSxnQkFBQTtFQTNEMUI7RUErRGtCO0lBQ0ksV0FBQTtFQTdEdEI7RUFnRTBCO0lBQ0kscUNBQUE7RUE5RDlCO0FBQ0Y7QUF3RUE7RUFFUTtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBdkVWO0VBeUVVO0lBQ0ksY0FBQTtFQXZFZDtFQTBFVTtJQUNJLGNBQUE7RUF4RWQ7RUE2RVU7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQTNFZDtFQTZFYztJQUNJLHlCQUFBO0VBM0VsQjtFQThFYztJQUNJLHlCQUFBO0VBNUVsQjtFQWdGVTtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7RUE5RWQ7RUFpRmtCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtFQS9FdEI7RUFpRnNCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUEvRTFCO0VBa0ZzQjtJQUNJLGNBQUE7RUFoRjFCO0VBa0YwQjtJQUNJLHlCQUFBO0VBaEY5QjtFQW1GMEI7SUFDSSx5QkFBQTtJQUNBLFdBQUE7RUFqRjlCO0VBdUZzQjtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBckYxQjtFQXdGc0I7SUFDSSxxQkFBQTtFQXRGMUI7RUEwRjBCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUF4RjlCO0VBMEY4QjtJQUNJLHlCQUFBO0VBeEZsQztFQTJGOEI7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsV0FBQTtFQXpGbEM7RUFnR2M7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQTlGbEI7QUFDRiIsImZpbGUiOiJob3VzZWhvbGQtYmluZGluZy5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi5ob3VzZWhvbGQtYmluZGluZy1jb250YWluZXIge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcblxyXG4gICAgLy8g6LyJ5YWl5YuV55Wr5qij5byPXHJcbiAgICAuc3BpbiB7XHJcbiAgICAgICAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcclxuICAgIH1cclxuXHJcbiAgICBAa2V5ZnJhbWVzIHNwaW4ge1xyXG4gICAgICAgIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfVxyXG4gICAgICAgIHRvIHsgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsgfVxyXG4gICAgfVxyXG5cclxuICAgIC5zZWxlY3RlZC1ob3VzZWhvbGRzLWFyZWEge1xyXG4gICAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbiAgICAgICAgcGFkZGluZzogMXJlbTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcblxyXG4gICAgICAgIC5zZWxlY3RlZC1oZWFkZXIge1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XHJcblxyXG4gICAgICAgICAgICAuc2VsZWN0ZWQtaW5mbyB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgIC5zZWxlY3RlZC1jb3VudCB7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc2VsZWN0ZWQtY29udGVudCB7XHJcbiAgICAgICAgICAgIC5idWlsZGluZy1ncm91cCB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAmOmxhc3QtY2hpbGQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLmJ1aWxkaW5nLWxhYmVsIHtcclxuICAgICAgICAgICAgICAgICAgICBtaW4td2lkdGg6IDNyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuaG91c2Vob2xkcy10YWdzIHtcclxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgIGZsZXgtd3JhcDogd3JhcDtcclxuICAgICAgICAgICAgICAgICAgICBnYXA6IDAuMjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGQtdGFnIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC4yNXJlbSAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMTk3NmQyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDFyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAucmVtb3ZlLWJ0biB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjMTk3NmQyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDFyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDFyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNiYmRlZmI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpkaXNhYmxlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC41O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmItaWNvbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5zZWxlY3Rvci1jb250YWluZXIge1xyXG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuXHJcbiAgICAgICAgLnNlbGVjdG9yLWJ1dHRvbiB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbSAwLjc1cmVtO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4xNXMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICAgICAmOmhvdmVyOm5vdCguZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICNhZGI1YmQ7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICY6Zm9jdXMge1xyXG4gICAgICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzgwYmRmZjtcclxuICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDAuMnJlbSByZ2JhKDAsIDEyMywgMjU1LCAwLjI1KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJi5kaXNhYmxlZCB7XHJcbiAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjY1O1xyXG4gICAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3Rvci10ZXh0IHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmNoZXZyb24taWNvbiB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjE1cyBlYXNlLWluLW91dDtcclxuXHJcbiAgICAgICAgICAgICAgICAmLnJvdGF0ZWQge1xyXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDE4MGRlZyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5kcm9wZG93bi1tZW51IHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICB0b3A6IGNhbGMoMTAwJSArIDAuMjVyZW0pO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICByaWdodDogMDtcclxuICAgICAgICAgICAgei1pbmRleDogMTA1MDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2NlZDRkYTtcclxuICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMC4zNzVyZW07XHJcbiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMC41cmVtIDFyZW0gcmdiYSgwLCAwLCAwLCAwLjE1KTtcclxuICAgICAgICAgICAgbWF4LWhlaWdodDogMjRyZW07XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcblxyXG4gICAgICAgICAgICAuZHJvcGRvd24tY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAyMHJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLXNpZGViYXIge1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAzMy4zMzMlO1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLnNpZGViYXItaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTllY2VmO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLnNpZGViYXItdGl0bGUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5ncy1saXN0IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogY2FsYygyMHJlbSAtIDNyZW0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5nLWl0ZW0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctaW5mbyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5nLW5hbWUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5nLWNvdW50IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtbWFpbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDY2LjY2NyU7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5tYWluLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuaGVhZGVyLWxlZnQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAubWFpbi10aXRsZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctaW5kaWNhdG9yIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuaGVhZGVyLWFjdGlvbnMge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLnNlYXJjaC1ib3gge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtZ3JpZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogY2FsYygyMHJlbSAtIDZyZW0pO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBvdmVyZmxvdy15OiBhdXRvO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmVtcHR5LXN0YXRlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMnJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5lbXB0eS1pY29uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5lbXB0eS10ZXh0IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmdyaWQtY29udGFpbmVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCAxZnIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGQtYnV0dG9uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2NlZDRkYTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlLWluLW91dDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICY6aG92ZXI6bm90KC5kaXNhYmxlZCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJi5zZWxlY3RlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlM2YyZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogbm90LWFsbG93ZWQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNjU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuaG91c2Vob2xkLWNvZGUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGQtZmxvb3Ige1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMC4xMjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuZHJvcGRvd24tZm9vdGVyIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmO1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuXHJcbiAgICAgICAgICAgICAgICAuZm9vdGVyLWluZm8ge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgZ2FwOiAxcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAubWF4LXNlbGVjdGlvbnMtdGV4dCxcclxuICAgICAgICAgICAgICAgICAgICAuY3VycmVudC1zZWxlY3Rpb25zLXRleHQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYmFja2Ryb3Age1xyXG4gICAgICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgbGVmdDogMDtcclxuICAgICAgICByaWdodDogMDtcclxuICAgICAgICBib3R0b206IDA7XHJcbiAgICAgICAgei1pbmRleDogMTA0MDtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgIH1cclxufVxyXG5cclxuLy8g6Z+/5oeJ5byP6Kit6KiIXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgLmhvdXNlaG9sZC1iaW5kaW5nLWNvbnRhaW5lciB7XHJcbiAgICAgICAgLnNlbGVjdG9yLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgIC5kcm9wZG93bi1tZW51IHtcclxuICAgICAgICAgICAgICAgIC5kcm9wZG93bi1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogYXV0bztcclxuICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiAyMHJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5ncy1zaWRlYmFyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1heC1oZWlnaHQ6IDhyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogbm9uZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLWxpc3Qge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogY2FsYyg4cmVtIC0gM3JlbSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLW1haW4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLWdyaWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmdyaWQtY29udGFpbmVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyDmt7HoibLkuLvpoYzmlK/mj7RcclxuQG1lZGlhIChwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaykge1xyXG4gICAgLmhvdXNlaG9sZC1iaW5kaW5nLWNvbnRhaW5lciB7XHJcbiAgICAgICAgLnNlbGVjdGVkLWhvdXNlaG9sZHMtYXJlYSB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMzNDNhNDA7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcblxyXG4gICAgICAgICAgICAuc2VsZWN0ZWQtaGVhZGVyIC5zZWxlY3RlZC1pbmZvIC5zZWxlY3RlZC1jb3VudCB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnNlbGVjdGVkLWNvbnRlbnQgLmJ1aWxkaW5nLWdyb3VwIC5idWlsZGluZy1sYWJlbCB7XHJcbiAgICAgICAgICAgICAgICBjb2xvcjogI2FkYjViZDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnNlbGVjdG9yLWNvbnRhaW5lciB7XHJcbiAgICAgICAgICAgIC5zZWxlY3Rvci1idXR0b24ge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICY6aG92ZXI6bm90KC5kaXNhYmxlZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1YTYyNjg7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgJi5kaXNhYmxlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmRyb3Bkb3duLW1lbnUge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuXHJcbiAgICAgICAgICAgICAgICAuZHJvcGRvd24tY29udGVudCB7XHJcbiAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5ncy1zaWRlYmFyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM0M2E0MDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDk1MDU3O1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLnNpZGViYXItaGVhZGVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmJ1aWxkaW5ncy1saXN0IC5idWlsZGluZy1pdGVtIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMwZDZlZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLW1haW4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAubWFpbi1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc2VhcmNoLWJveCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLWdyaWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmdyaWQtY29udGFpbmVyIC5ob3VzZWhvbGQtYnV0dG9uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2FkYjViZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3Zlcjpub3QoLmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1YTYyNjg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLnNlbGVjdGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzBkNmVmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMGQ2ZWZkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgLmRyb3Bkb3duLWZvb3RlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM0M2E0MDtcclxuICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r5", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r1", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "HouseholdBindingComponent_div_8_button_17_Template_button_click_0_listener", "building_r8", "_r7", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "ɵɵtextInterpolate", "getBuildingCount", "HouseholdBindingComponent_div_8_div_24_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_div_8_div_24_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_div_8_div_24_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_div_8_div_24_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_div_8_div_25_Template_input_ngModelChange_2_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_div_8_div_25_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_div_8_div_25_div_4_Template", "ɵɵtwoWayProperty", "filteredHouseholds", "length", "HouseholdBindingComponent_div_8_div_28_button_1_Template_button_click_0_listener", "householdCode_r13", "_r12", "onHouseholdToggle", "HouseholdBindingComponent_div_8_div_28_button_1_div_3_Template", "isHouseholdSelected", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_div_8_div_28_button_1_Template", "maxSelections", "ɵɵtextInterpolate2", "HouseholdBindingComponent_div_8_button_44_Template_button_click_0_listener", "_r14", "HouseholdBindingComponent_div_8_button_45_Template_button_click_0_listener", "_r15", "resetSearch", "HouseholdBindingComponent_div_8_button_17_Template", "HouseholdBindingComponent_div_8_span_23_Template", "HouseholdBindingComponent_div_8_div_24_Template", "HouseholdBindingComponent_div_8_div_25_Template", "HouseholdBindingComponent_div_8_div_27_Template", "HouseholdBindingComponent_div_8_div_28_Template", "HouseholdBindingComponent_div_8_div_39_Template", "HouseholdBindingComponent_div_8_div_40_Template", "HouseholdBindingComponent_div_8_div_41_Template", "HouseholdBindingComponent_div_8_button_44_Template", "HouseholdBindingComponent_div_8_button_45_Template", "HouseholdBindingComponent_div_8_Template_button_click_47_listener", "_r6", "closeDropdown", "HouseholdBindingComponent_div_8_Template_button_click_49_listener", "allowBatchSelect", "allowSearch", "selectedHouseholds", "HouseholdBindingComponent_div_9_Template_div_click_0_listener", "_r16", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "buildCaseId", "buildingData", "showSelectedArea", "excludedHouseholds", "selectionChange", "isOpen", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "console", "log", "loadBuildingDataFromApi", "generateMockData", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "code", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "grouped", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "codes", "Math", "parseInt", "slice", "onBuildingClick", "households", "filter", "toLowerCase", "includes", "event", "target", "householdCode", "newSelection", "emitChanges", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "buildingHouseholds", "unselectedBuilding", "selectedItems", "emit", "toggleDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "rf", "ctx", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_div_8_Template", "HouseholdBindingComponent_div_9_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseName?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedHouseholds: string[] = [];  buildings: string[] = [];\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n  \r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  }  ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  }\r\n\r\n  private initializeData() {\r\n    if (this.buildCaseId) {\r\n      // 使用API載入資料\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\r\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n        this.buildingData = this.generateMockData();\r\n      }\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with buildings:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // 如果API載入失敗，使用mock資料作為備援\r\n        this.buildingData = this.generateMockData();\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: HouseItem[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n    \r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋過濾\r\n    this.filteredHouseholds = households\r\n      .map(h => h.code)\r\n      .filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              {{householdCode}}\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>  <!-- 選擇器 -->\r\n  <div class=\"selector-container\" style=\"position: relative;\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\" [disabled]=\"disabled || isLoading\"\r\n      (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"chevron-down-outline\" [class.rotated]=\"isOpen\" class=\"chevron-icon\">\r\n      </nb-icon>\r\n    </button> <!-- 下拉選單 --> <!-- 下拉選單 - 完全簡化版本 --> <!-- 下拉選單 -->\r\n    <div *ngIf=\"isOpen\"\r\n      style=\"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\">\r\n\r\n      <!-- 標題區域 -->\r\n      <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n          <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span style=\"font-weight: 500; color: #495057;\">選擇戶別</span>\r\n            <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n          </div>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n        </div>\r\n      </div> <!-- 主要內容區域 -->\r\n      <div style=\"display: flex; height: calc(100% - 160px); min-height: 300px; max-height: 400px;\">\r\n\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div style=\"width: 40%; border-right: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n          </div>\r\n          <div style=\"max-height: calc(100% - 52px); overflow-y: auto;\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n              <span\r\n                style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">{{getBuildingCount(building)}}戶</span>\r\n            </button>\r\n          </div>\r\n        </div> <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                戶別選擇\r\n                <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n              </h6>\r\n              <div *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\"\r\n                style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\"\r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && filteredHouseholds.length === 0\"\r\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                找不到符合 \"{{searchTerm}}\" 的戶別\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); gap: 8px;\">\r\n              <button *ngFor=\"let householdCode of filteredHouseholds\" type=\"button\"\r\n                (click)=\"onHouseholdToggle(householdCode)\" [disabled]=\"isHouseholdDisabled(householdCode)\"\r\n                [style.background-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#f8f9fa' : '#fff')\"\r\n                [style.color]=\"isHouseholdSelected(householdCode) ? '#fff' : (isHouseholdExcluded(householdCode) ? '#6c757d' : '#495057')\"\r\n                [style.border-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#dee2e6' : '#ced4da')\"\r\n                [style.opacity]=\"isHouseholdDisabled(householdCode) ? '0.6' : '1'\"\r\n                [style.cursor]=\"isHouseholdDisabled(householdCode) ? 'not-allowed' : 'pointer'\"\r\n                style=\"padding: 8px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 32px; position: relative;\">\r\n                <span [style.text-decoration]=\"isHouseholdExcluded(householdCode) ? 'line-through' : 'none'\">\r\n                  {{householdCode}}\r\n                </span>\r\n                <div *ngIf=\"isHouseholdExcluded(householdCode)\"\r\n                  style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                  ✕\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> <!-- 優化的底部操作區 -->\r\n      <div style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <!-- 統計資訊行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n          <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n            <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n              <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n            </div>\r\n            <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n              <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n              <span>當前棟別: <strong>{{selectedBuilding}}</strong></span>\r\n            </div>\r\n          </div>\r\n          <div *ngIf=\"searchTerm\"\r\n            style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\">\r\n            搜尋: \"{{searchTerm}}\" ({{filteredHouseholds.length}} 個結果)\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 操作按鈕行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" *ngIf=\"selectedHouseholds.length > 0\" (click)=\"onClearAll()\"\r\n              style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n              清空全部\r\n            </button>\r\n            <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n              style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n              重置搜尋\r\n            </button>\r\n          </div>\r\n\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n              取消\r\n            </button>\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n              確定選擇\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 點擊外部關閉下拉選單 -->\r\n  <div *ngIf=\"isOpen\" class=\"backdrop\" (click)=\"closeDropdown()\"></div>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICgB5DC,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAG,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,gBAAA,CAAgC;IAAA,EAAC;IACvGL,EAAA,CAAAa,SAAA,kBAAwC;IAE5Cb,EADE,CAAAc,YAAA,EAAS,EACJ;;;;;IAJLd,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAgB,kBAAA,MAAAX,gBAAA,MACA;IAAyCL,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;;;;;IALpElB,EAAA,CAAAmB,uBAAA,GAAoD;IAClDnB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAC,oEAAA,mBAAkG;IAMpGrB,EAAA,CAAAc,YAAA,EAAM;;;;;;IARsBd,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,kBAAA,KAAAM,WAAA,MAAa;IAEPtB,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAc,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhFtB,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAoB,UAAA,IAAAI,6DAAA,0BAAoD;IAWtDxB,EAAA,CAAAc,YAAA,EAAM;;;;;IAXWd,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpDtB,EAFJ,CAAAC,cAAA,aAAgG,cACjE,cACA;IACzBD,EAAA,CAAAa,SAAA,kBAA8D;IAC9Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAc,YAAA,EAAO,EAC9D;IACNd,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAG,UAAA,mBAAAuB,iEAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IACtG5B,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAc,YAAA,EAAS,EACL;IACNd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAS,8CAAA,kBAA+D;IAcnE7B,EADE,CAAAc,YAAA,EAAM,EACF;;;;IArB6Bd,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,kBAAA,qCAAAP,MAAA,CAAAqB,gBAAA,QAA8B;IAED9B,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;IAKvDlB,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;;;;;IAoBpC/B,EAAA,CAAAmB,uBAAA,GAAgC;IAC9BnB,EAAA,CAAAa,SAAA,kBAAsD;IACtDb,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAmB,uBAAA,GAAiC;IAC/BnB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAqB,gBAAA,iCAAArB,MAAA,CAAAqB,gBAAA,6BAAArB,MAAA,CAAAuB,WAAA,MACF;;;;;;IA2BIhC,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAG,UAAA,mBAAA8B,2EAAA;MAAA,MAAAC,WAAA,GAAAlC,EAAA,CAAAM,aAAA,CAAA6B,GAAA,EAAA3B,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FlC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACnEd,EAAA,CAAAC,cAAA,eACgH;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACjJF,EADiJ,CAAAc,YAAA,EAAO,EAC/I;;;;;IALPd,EADA,CAAAqC,WAAA,qBAAA5B,MAAA,CAAA6B,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAzB,MAAA,CAAA6B,gBAAA,KAAAJ,WAAA,iDACe;IAEnDlC,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAuC,iBAAA,CAAAL,WAAA,CAAY;IAEoDlC,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAgB,kBAAA,KAAAP,MAAA,CAAA+B,gBAAA,CAAAN,WAAA,YAA+B;;;;;IAS7IlC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA7Bd,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAA6B,gBAAA,MAAsB;;;;;;IAc7EtC,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAG,UAAA,mBAAAsC,iFAAA;MAAAzC,EAAA,CAAAM,aAAA,CAAAoC,IAAA;MAAA,MAAAjC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkC,qBAAA,EAAuB;IAAA,EAAC;IAEtF3C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAbTd,EAFF,CAAAC,cAAA,cACmC,iBAGqG;IAFlFD,EAAA,CAAAG,UAAA,mBAAAyC,wEAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqC,mBAAA,EAAqB;IAAA,EAAC;IAGjF9C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAG,UAAA,mBAAA4C,wEAAA;MAAA/C,EAAA,CAAAM,aAAA,CAAAuC,GAAA;MAAA,MAAApC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuC,mBAAA,EAAqB;IAAA,EAAC;IAGjFhD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAoB,UAAA,IAAA6B,wDAAA,qBACsI;IAGxIjD,EAAA,CAAAc,YAAA,EAAM;;;;IAbFd,EAAA,CAAAe,SAAA,EAA+C;IAA/Cf,EAAA,CAAAqC,WAAA,YAAA5B,MAAA,CAAAyC,aAAA,iBAA+C;IAD3BlD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAyC,aAAA,GAA6B;IAMjDlD,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAqC,WAAA,YAAA5B,MAAA,CAAAyC,aAAA,iBAA+C;IAD3BlD,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAyC,aAAA,GAA6B;IAGjDlD,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,kBAAAP,MAAA,CAAA6B,gBAAA,MACF;IACuBtC,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA0C,sBAAA,GAA8B;;;;;IAevDnD,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,uCAAAP,MAAA,CAAA2C,UAAA,2BACF;;;;;;IAREpD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAAqD,gBAAA,2BAAAC,+EAAAC,MAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAyD,kBAAA,CAAAhD,MAAA,CAAA2C,UAAA,EAAAG,MAAA,MAAA9C,MAAA,CAAA2C,UAAA,GAAAG,MAAA;MAAA,OAAAvD,EAAA,CAAAW,WAAA,CAAA4C,MAAA;IAAA,EAAwB;IAACvD,EAAA,CAAAG,UAAA,mBAAAuD,uEAAAH,MAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5EvD,EAAA,CAAAc,YAAA,EACsI;IACtId,EAAA,CAAAa,SAAA,kBACiI;IACnIb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAoB,UAAA,IAAAwC,qDAAA,kBAC+D;IAGjE5D,EAAA,CAAAc,YAAA,EAAM;;;;IATiBd,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAA6D,gBAAA,YAAApD,MAAA,CAAA2C,UAAA,CAAwB;IAKvCpD,EAAA,CAAAe,SAAA,GAAmD;IAAnDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA2C,UAAA,IAAA3C,MAAA,CAAAqD,kBAAA,CAAAC,MAAA,OAAmD;;;;;IAS3D/D,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAa,SAAA,kBAAkG;IAClGb,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAc,YAAA,EAAI,EACjD;;;;;IAcFd,EAAA,CAAAC,cAAA,cACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;;;IAdRd,EAAA,CAAAC,cAAA,iBAO6K;IAN3KD,EAAA,CAAAG,UAAA,mBAAA6D,iFAAA;MAAA,MAAAC,iBAAA,GAAAjE,EAAA,CAAAM,aAAA,CAAA4D,IAAA,EAAA1D,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0D,iBAAA,CAAAF,iBAAA,CAAgC;IAAA,EAAC;IAO1CjE,EAAA,CAAAC,cAAA,WAA6F;IAC3FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACPd,EAAA,CAAAoB,UAAA,IAAAgD,8DAAA,kBACsN;IAGxNpE,EAAA,CAAAc,YAAA,EAAS;;;;;IATPd,EAJA,CAAAqC,WAAA,qBAAA5B,MAAA,CAAA4D,mBAAA,CAAAJ,iBAAA,gBAAAxD,MAAA,CAAA6D,mBAAA,CAAAL,iBAAA,uBAAqI,UAAAxD,MAAA,CAAA4D,mBAAA,CAAAJ,iBAAA,aAAAxD,MAAA,CAAA6D,mBAAA,CAAAL,iBAAA,0BACX,iBAAAxD,MAAA,CAAA4D,mBAAA,CAAAJ,iBAAA,gBAAAxD,MAAA,CAAA6D,mBAAA,CAAAL,iBAAA,0BACU,YAAAxD,MAAA,CAAA8D,mBAAA,CAAAN,iBAAA,gBAClE,WAAAxD,MAAA,CAAA8D,mBAAA,CAAAN,iBAAA,8BACa;IALpCjE,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAA8D,mBAAA,CAAAN,iBAAA,EAA+C;IAOpFjE,EAAA,CAAAe,SAAA,EAAsF;IAAtFf,EAAA,CAAAqC,WAAA,oBAAA5B,MAAA,CAAA6D,mBAAA,CAAAL,iBAAA,4BAAsF;IAC1FjE,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,MAAAiD,iBAAA,MACF;IACMjE,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6D,mBAAA,CAAAL,iBAAA,EAAwC;;;;;IAblDjE,EAAA,CAAAC,cAAA,cACgG;IAC9FD,EAAA,CAAAoB,UAAA,IAAAoD,wDAAA,sBAO6K;IAS/KxE,EAAA,CAAAc,YAAA,EAAM;;;;IAhB8Bd,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAqD,kBAAA,CAAqB;;;;;IA4BzD9D,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAa,SAAA,kBAAuE;IACvEb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAc,YAAA,EAAS;IAACd,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAc,YAAA,EAAO,EACpD;;;;IADiBd,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAgE,aAAA,CAAiB;;;;;IAExCzE,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAa,SAAA,kBAA+D;IAC/Db,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1CF,EAD0C,CAAAc,YAAA,EAAS,EAAO,EACpD;;;;IADgBd,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAA6B,gBAAA,CAAoB;;;;;IAG5CtC,EAAA,CAAAC,cAAA,cACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAM;;;;IADJd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAA0E,kBAAA,sBAAAjE,MAAA,CAAA2C,UAAA,UAAA3C,MAAA,CAAAqD,kBAAA,CAAAC,MAAA,0BACF;;;;;;IAME/D,EAAA,CAAAC,cAAA,iBACsL;IAD1HD,EAAA,CAAAG,UAAA,mBAAAwE,2EAAA;MAAA3E,EAAA,CAAAM,aAAA,CAAAsE,IAAA;MAAA,MAAAnE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IAEhF5B,EAAA,CAAAa,SAAA,kBAA0C;IAC1Cb,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBACsL;IAD9HD,EAAA,CAAAG,UAAA,mBAAA0E,2EAAA;MAAA7E,EAAA,CAAAM,aAAA,CAAAwE,IAAA;MAAA,MAAArE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsE,WAAA,EAAa;IAAA,EAAC;IAE7E/E,EAAA,CAAAa,SAAA,kBAA0C;IAC1Cb,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAlIXd,EANN,CAAAC,cAAA,cAC0Q,cAG1K,cACX,cACpB;IACzDD,EAAA,CAAAa,SAAA,kBAA+D;IAC/Db,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAc,YAAA,EAAO;IAC3Dd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAc,YAAA,EAAO,EAChF;IACNd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAc,YAAA,EAAO,EACjF,EACF;IAMAd,EALN,CAAAC,cAAA,eAA8F,eAGP,eAChB,cACa;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACpFF,EADoF,CAAAc,YAAA,EAAK,EACnF;IACNd,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAoB,UAAA,KAAA4D,kDAAA,qBAG4L;IAMhMhF,EADE,CAAAc,YAAA,EAAM,EACF;IAIAd,EAHN,CAAAC,cAAA,eAA6D,eACQ,eACoC,cACrB;IAC5ED,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAoB,UAAA,KAAA6D,gDAAA,mBAAuD;IACzDjF,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAoB,UAAA,KAAA8D,+CAAA,kBACmC;IAgBrClF,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAoB,UAAA,KAAA+D,+CAAA,kBAAsE;IAYxEnF,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAC,cAAA,eAAuD;IAKrDD,EAJA,CAAAoB,UAAA,KAAAgE,+CAAA,kBAA+F,KAAAC,+CAAA,kBAKC;IAoBtGrF,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IAKAd,EAJN,CAAAC,cAAA,eAAsF,eAEkB,eACH,eACpC;IACzDD,EAAA,CAAAa,SAAA,mBAA2E;IAC3Eb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAS;IAACd,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAc,YAAA,EAAO,EACzD;IAKNd,EAJA,CAAAoB,UAAA,KAAAkE,+CAAA,kBAAiF,KAAAC,+CAAA,kBAIG;IAItFvF,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAoB,UAAA,KAAAoE,+CAAA,kBACgH;IAGlHxF,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAoB,UAAA,KAAAqE,kDAAA,qBACsL,KAAAC,kDAAA,qBAKA;IAIxL1F,EAAA,CAAAc,YAAA,EAAM;IAGJd,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAG,UAAA,mBAAAwF,kEAAA;MAAA3F,EAAA,CAAAM,aAAA,CAAAsF,GAAA;MAAA,MAAAnF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoF,aAAA,EAAe;IAAA,EAAC;IAE7C7F,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAG,UAAA,mBAAA2F,kEAAA;MAAA9F,EAAA,CAAAM,aAAA,CAAAsF,GAAA;MAAA,MAAAnF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoF,aAAA,EAAe;IAAA,EAAC;IAE7C7F,EAAA,CAAAa,SAAA,mBAA4C;IAC5Cb,EAAA,CAAAE,MAAA,kCACF;IAIRF,EAJQ,CAAAc,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IA/IqDd,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAAsB,SAAA,CAAAgC,MAAA,yBAA0B;IAE5B/D,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,kBAAA,yBAAAP,MAAA,CAAAqB,gBAAA,OAA2B;IAW/C9B,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;IAe9B/B,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,CAAsB;IAEzBtC,EAAA,CAAAe,SAAA,EAA2E;IAA3Ef,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAsF,gBAAA,IAAAtF,MAAA,CAAA6B,gBAAA,IAAA7B,MAAA,CAAAqD,kBAAA,CAAAC,MAAA,KAA2E;IAoB7E/D,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAuF,WAAA,IAAAvF,MAAA,CAAA6B,gBAAA,CAAqC;IAgBrCtC,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAiB,UAAA,UAAAR,MAAA,CAAA6B,gBAAA,CAAuB;IAIvBtC,EAAA,CAAAe,SAAA,EAAuD;IAAvDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,IAAA7B,MAAA,CAAAqD,kBAAA,CAAAC,MAAA,KAAuD;IA4BxC/D,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAuC,iBAAA,CAAA9B,MAAA,CAAAqB,gBAAA,GAAsB;IAErC9B,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgE,aAAA,CAAmB;IAInBzE,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA6B,gBAAA,CAAsB;IAKxBtC,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA2C,UAAA,CAAgB;IASGpD,EAAA,CAAAe,SAAA,GAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAwF,kBAAA,CAAAlC,MAAA,KAAmC;IAKnC/D,EAAA,CAAAe,SAAA,EAA+B;IAA/Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAuF,WAAA,IAAAvF,MAAA,CAAA2C,UAAA,CAA+B;;;;;;IAwBhEpD,EAAA,CAAAC,cAAA,cAA+D;IAA1BD,EAAA,CAAAG,UAAA,mBAAA+F,8DAAA;MAAAlG,EAAA,CAAAM,aAAA,CAAA6F,IAAA;MAAA,MAAA1F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoF,aAAA,EAAe;IAAA,EAAC;IAAC7F,EAAA,CAAAc,YAAA,EAAM;;;ADpKvE,OAAM,MAAOsF,yBAAyB;EAyBpCC,YACUC,GAAsB,EACtBC,kBAAsC;IADtC,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA1BnB,KAAAvE,WAAW,GAAW,OAAO;IAC7B,KAAAyC,aAAa,GAAkB,IAAI;IACnC,KAAAvD,QAAQ,GAAY,KAAK;IACzB,KAAAsF,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAV,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAAY,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAI/G,YAAY,EAAmB;IAE/D,KAAAgH,MAAM,GAAG,KAAK;IACd,KAAAvE,gBAAgB,GAAG,EAAE;IACrB,KAAAc,UAAU,GAAG,EAAE;IACf,KAAA6C,kBAAkB,GAAa,EAAE;IAAG,KAAAlE,SAAS,GAAa,EAAE;IAC5D,KAAA+B,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAgD,kBAAkB,GAAqC,EAAE;IACzD,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAAChB,kBAAkB,GAAGgB,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACvG,QAAQ,GAAGuG,UAAU;EAC5B;EAAGC,QAAQA,CAAA;IACT,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACrB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACmB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAAC9F,SAAS,GAAG+F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;MAC/C,IAAI,CAACuB,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACvB,kBAAkB,CAAC;IACtE;EACF;EAEQgB,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACnB,WAAW,EAAE;MACpB;MACA,IAAI,CAAC2B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAAC1B,YAAY,IAAIqB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC,CAAC1C,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAC0C,YAAY,GAAG,IAAI,CAAC2B,gBAAgB,EAAE;MAC7C;MACA,IAAI,CAACrG,SAAS,GAAG+F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;MAC/CwB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACnG,SAAS,CAAC;MACpE,IAAI,CAACqF,wBAAwB,EAAE;IACjC;EACF;EAEQe,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE;IAEvB,IAAI,CAACO,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,kBAAkB,CAAC8B,WAAW,CAAC,IAAI,CAAC7B,WAAW,CAAC,CAAC8B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,QAAQ,CAAC;QACtC,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACgC,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC3G,SAAS,GAAG+F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;QAC/C,IAAI,CAACW,wBAAwB,EAAE;QAC/B,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACT,GAAG,CAACqC,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAACnC,YAAY,GAAG,IAAI,CAAC2B,gBAAgB,EAAE;QAC3C,IAAI,CAACrG,SAAS,GAAG+F,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;QAC/C,IAAI,CAACW,wBAAwB,EAAE;QAC/B,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACT,GAAG,CAACqC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,gCAAgCA,CAACI,OAAuC;IAC9E,MAAMpC,YAAY,GAAiB,EAAE;IAErCqB,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDvC,YAAY,CAACsC,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,IAAI,EAAED,KAAK,CAACE,SAAS;QACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;QACxBC,KAAK,EAAEJ,KAAK,CAACK,KAAK;QAClBC,OAAO,EAAEN,KAAK,CAACO,OAAO;QACtBC,SAAS,EAAER,KAAK,CAACE,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOhB,YAAY;EACrB;EAEQW,wBAAwBA,CAAA;IAC9B,MAAMwC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAC3D,kBAAkB,CAAC6C,OAAO,CAACK,IAAI,IAAG;MACrC,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAAChH,SAAS,EAAE;QACrC,MAAM8H,IAAI,GAAG,IAAI,CAACpD,YAAY,CAACsC,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACb,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACrC,kBAAkB,GAAG8C,OAAO;EACnC;EACQxB,gBAAgBA,CAAA;IACtB;IACA,MAAM6B,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEpG,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqG,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpG,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqG,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpG,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqG,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpG,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqG,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpG,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqG,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAM9D,YAAY,GAAiB,EAAE;IACrCqB,MAAM,CAACe,OAAO,CAACoB,cAAc,CAAC,CAACnB,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEyB,KAAK,CAAC,KAAI;MAC3D/D,YAAY,CAACsC,QAAQ,CAAC,GAAGyB,KAAK,CAACvB,GAAG,CAACE,IAAI,KAAK;QAC1CA,IAAI;QACJJ,QAAQ;QACRO,KAAK,EAAE,GAAGmB,IAAI,CAACnB,KAAK,CAAC,CAACoB,QAAQ,CAACvB,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DhB,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOhB,YAAY;EACrB;EAAErE,gBAAgBA,CAAC2G,QAAgB;IACjCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,QAAQ,CAAC;IAC3C,IAAI,CAACzG,gBAAgB,GAAGyG,QAAQ;IAChC,IAAI,CAAC3F,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4E,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpE,kBAAkB,CAACC,MAAM,CAAC;IACzE;IACA,IAAI,CAACuC,GAAG,CAACqC,aAAa,EAAE;EAC1B;EAEAiC,eAAeA,CAAC7B,QAAgB;IAC9Bd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,QAAQ,CAAC;EACxD;EAAEf,wBAAwBA,CAAA;IACxBC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC5F,gBAAgB,CAAC;IAChF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAACwB,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM+G,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACnE,gBAAgB,CAAC,IAAI,EAAE;IACjE2F,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,UAAU,CAAC9G,MAAM,CAAC;IAEpE;IACA,IAAI,CAACD,kBAAkB,GAAG+G,UAAU,CACjC5B,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,CAChB2B,MAAM,CAAC3B,IAAI,IAAIA,IAAI,CAAC4B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5H,UAAU,CAAC2H,WAAW,EAAE,CAAC,CAAC;IAE7E9C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACpE,kBAAkB,CAACC,MAAM,CAAC;EAC5E;EAEAJ,cAAcA,CAACsH,KAAU;IACvB,IAAI,CAAC7H,UAAU,GAAG6H,KAAK,CAACC,MAAM,CAACjE,KAAK;IACpC,IAAI,CAACe,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC9E,UAAU,CAAC;EACtD;EAEA2B,WAAWA,CAAA;IACT,IAAI,CAAC3B,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4E,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACA/D,iBAAiBA,CAACgH,aAAqB;IACrC;IACA,IAAI,IAAI,CAAC7G,mBAAmB,CAAC6G,aAAa,CAAC,EAAE;MAC3ClD,OAAO,CAACC,GAAG,CAAC,MAAMiD,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAMxB,UAAU,GAAG,IAAI,CAAC1D,kBAAkB,CAAC+E,QAAQ,CAACG,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAIzB,UAAU,EAAE;MACdyB,YAAY,GAAG,IAAI,CAACnF,kBAAkB,CAAC6E,MAAM,CAACf,CAAC,IAAIA,CAAC,KAAKoB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAAC1G,aAAa,IAAI,IAAI,CAACwB,kBAAkB,CAAClC,MAAM,IAAI,IAAI,CAACU,aAAa,EAAE;QAC9EwD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAkD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACnF,kBAAkB,EAAEkF,aAAa,CAAC;IAC5D;IAEA,IAAI,CAAClF,kBAAkB,GAAGmF,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAzK,iBAAiBA,CAACuK,aAAqB;IACrC,IAAI,CAAClF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6E,MAAM,CAACf,CAAC,IAAIA,CAAC,KAAKoB,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EAAEvI,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACwB,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAMuH,YAAY,GAAG,IAAI,CAACrF,kBAAkB,CAAClC,MAAM;IACnD,MAAMwH,UAAU,GAAG,IAAI,CAAC9G,aAAa,IAAI+G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAC5H,kBAAkB,CAACgH,MAAM,CAAC3B,IAAI,IAC5D,CAAC,IAAI,CAAClD,kBAAkB,CAAC+E,QAAQ,CAAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC7E,mBAAmB,CAAC6E,IAAI,CAAC,CAC3E;IAED;IACA,MAAMwC,KAAK,GAAGD,kBAAkB,CAACf,KAAK,CAAC,CAAC,EAAEc,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC5H,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACkC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG0F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBpD,OAAO,CAACC,GAAG,CAAC,aAAayD,KAAK,CAAC5H,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAf,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAMsJ,kBAAkB,GAAG,IAAI,CAACnF,YAAY,CAAC,IAAI,CAACnE,gBAAgB,CAAC,EAAE2G,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAE3F;IACA,MAAMmC,YAAY,GAAG,IAAI,CAACrF,kBAAkB,CAAClC,MAAM;IACnD,MAAMwH,UAAU,GAAG,IAAI,CAAC9G,aAAa,IAAI+G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMO,kBAAkB,GAAGD,kBAAkB,CAACd,MAAM,CAAC3B,IAAI,IACvD,CAAC,IAAI,CAAClD,kBAAkB,CAAC+E,QAAQ,CAAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC7E,mBAAmB,CAAC6E,IAAI,CAAC,CAC3E;IAED;IACA,MAAMwC,KAAK,GAAGE,kBAAkB,CAAClB,KAAK,CAAC,CAAC,EAAEc,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC5H,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACkC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG0F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBpD,OAAO,CAACC,GAAG,CAAC,aAAayD,KAAK,CAAC5H,MAAM,MAAM,CAAC;IAC9C;EACF;EACApB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAMsJ,kBAAkB,GAAG,IAAI,CAACnF,YAAY,CAAC,IAAI,CAACnE,gBAAgB,CAAC,EAAE2G,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAAClD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6E,MAAM,CAACf,CAAC,IAAI,CAAC6B,kBAAkB,CAACZ,QAAQ,CAACjB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACsB,WAAW,EAAE;EACpB;EACAzJ,UAAUA,CAAA;IACR,IAAI,CAACqE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACoF,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACjE,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACf,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACiB,SAAS,EAAE;IAEhB,MAAM4E,aAAa,GAAG,IAAI,CAAC7F,kBAAkB,CAACgD,GAAG,CAACE,IAAI,IAAG;MACvD,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAAChH,SAAS,EAAE;QACrC,MAAM8H,IAAI,GAAG,IAAI,CAACpD,YAAY,CAACsC,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACiB,MAAM,CAACjB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAACjD,eAAe,CAACmF,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC9K,QAAQ,EAAE;MAClB,IAAI,CAAC2F,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1BoB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACrB,MAAM,CAAC;MACrDoB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnG,SAAS,CAAC;IACrD;EACF;EAEA8D,aAAaA,CAAA;IACX,IAAI,CAACgB,MAAM,GAAG,KAAK;EACrB;EAEAxC,mBAAmBA,CAAC8G,aAAqB;IACvC,OAAO,IAAI,CAAClF,kBAAkB,CAAC+E,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEA7G,mBAAmBA,CAAC6G,aAAqB;IACvC,OAAO,IAAI,CAACxE,kBAAkB,CAACqE,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEA5G,mBAAmBA,CAAC4G,aAAqB;IACvC,OAAO,IAAI,CAAC7G,mBAAmB,CAAC6G,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACjI,aAAa,EAAE,IAAI,CAAC,IAAI,CAACmB,mBAAmB,CAAC8G,aAAa,CAAE;EACvE;EAEAjI,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACuB,aAAa,IAAI,IAAI,CAACwB,kBAAkB,CAAClC,MAAM,GAAG,IAAI,CAACU,aAAa;EACnF;EAEAwH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC3J,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMsJ,kBAAkB,GAAG,IAAI,CAACnF,YAAY,CAAC,IAAI,CAACnE,gBAAgB,CAAC,CAChEwI,MAAM,CAACf,CAAC,IAAI,CAACA,CAAC,CAACtC,UAAU,CAAC,CAC1BwB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC;IACnB,OAAOyC,kBAAkB,CAAC7H,MAAM,GAAG,CAAC,IAClC6H,kBAAkB,CAACM,KAAK,CAAC/C,IAAI,IAAI,IAAI,CAAClD,kBAAkB,CAAC+E,QAAQ,CAAC7B,IAAI,CAAC,CAAC;EAC5E;EACAhG,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMsJ,kBAAkB,GAAG,IAAI,CAACnF,YAAY,CAAC,IAAI,CAACnE,gBAAgB,CAAC,EAAE2G,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAOyC,kBAAkB,CAACO,IAAI,CAAChD,IAAI,IAAI,IAAI,CAAClD,kBAAkB,CAAC+E,QAAQ,CAAC7B,IAAI,CAAC,CAAC;EAChF;EACAiD,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACtF,kBAAkB;EAChC;EAEAtE,gBAAgBA,CAACuG,QAAgB;IAC/B,OAAO,IAAI,CAACtC,YAAY,CAACsC,QAAQ,CAAC,EAAEhF,MAAM,IAAI,CAAC;EACjD;EAEAjC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACmE,kBAAkB,CAAClC,MAAM;EACvC;EAEA;EACAxC,6BAA6BA,CAACwH,QAAgB;IAC5C,OAAO,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAtH,mBAAmBA,CAACsH,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,IAAI,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,CAAChF,MAAM,GAAG,CAAC,CAAC;EAC9F;;;uCA3WWqC,yBAAyB,EAAApG,EAAA,CAAAqM,iBAAA,CAAArM,EAAA,CAAAsM,iBAAA,GAAAtM,EAAA,CAAAqM,iBAAA,CAAAE,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAzBpG,yBAAyB;MAAAqG,SAAA;MAAAC,MAAA;QAAA1K,WAAA;QAAAyC,aAAA;QAAAvD,QAAA;QAAAsF,WAAA;QAAAC,YAAA;QAAAC,gBAAA;QAAAV,WAAA;QAAAD,gBAAA;QAAAY,kBAAA;MAAA;MAAAgG,OAAA;QAAA/F,eAAA;MAAA;MAAAgG,QAAA,GAAA5M,EAAA,CAAA6M,kBAAA,CARzB,CACT;QACEC,OAAO,EAAE/M,iBAAiB;QAC1BgN,WAAW,EAAEjN,UAAU,CAAC,MAAMsG,yBAAyB,CAAC;QACxD4G,KAAK,EAAE;OACR,CACF,GAAAhN,EAAA,CAAAiN,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCHvN,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAoB,UAAA,IAAAqM,wCAAA,kBAAgG;UA2B9FzN,EADF,CAAAC,cAAA,aAA4D,gBAGuJ;UAD/MD,EAAA,CAAAG,UAAA,mBAAAuN,2DAAA;YAAA,OAASF,GAAA,CAAAxB,cAAA,EAAgB;UAAA,EAAC;UAE1BhM,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAoB,UAAA,IAAAuM,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnC5N,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAa,SAAA,iBACU;UACZb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAoB,UAAA,IAAAyM,wCAAA,mBAC0Q;UAwJ5Q7N,EAAA,CAAAc,YAAA,EAAM;UAGNd,EAAA,CAAAoB,UAAA,IAAA0M,wCAAA,iBAA+D;UAxMjE9N,EAAA,CAAAc,YAAA,EAAyC;;;UAEjCd,EAAA,CAAAe,SAAA,EAAuD;UAAvDf,EAAA,CAAAiB,UAAA,SAAAuM,GAAA,CAAA9G,gBAAA,IAAA8G,GAAA,CAAAvH,kBAAA,CAAAlC,MAAA,KAAuD;UA2Bb/D,EAAA,CAAAe,SAAA,GAAwC;UAAxCf,EAAA,CAAA+N,WAAA,aAAAP,GAAA,CAAAtM,QAAA,IAAAsM,GAAA,CAAAzG,SAAA,CAAwC;UAAC/G,EAAA,CAAAiB,UAAA,aAAAuM,GAAA,CAAAtM,QAAA,IAAAsM,GAAA,CAAAzG,SAAA,CAAkC;UAItG/G,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAiB,UAAA,SAAAuM,GAAA,CAAAzG,SAAA,CAAe;UAIf/G,EAAA,CAAAe,SAAA,EAAgB;UAAhBf,EAAA,CAAAiB,UAAA,UAAAuM,GAAA,CAAAzG,SAAA,CAAgB;UAII/G,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAA+N,WAAA,YAAAP,GAAA,CAAA3G,MAAA,CAAwB;UAGzD7G,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAuM,GAAA,CAAA3G,MAAA,CAAY;UA4Jd7G,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAuM,GAAA,CAAA3G,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}