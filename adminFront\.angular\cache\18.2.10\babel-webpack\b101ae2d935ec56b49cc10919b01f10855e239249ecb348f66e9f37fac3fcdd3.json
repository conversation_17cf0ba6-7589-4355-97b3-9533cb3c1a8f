{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {'extended'|'basic'} [options.format='extended'] - if 'basic', hide delimiters between date and time values.\n * @param {'complete'|'date'|'time'} [options.representation='complete'] - format date, time, or both.\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.format` must be 'extended' or 'basic'\n * @throws {RangeError} `options.representation` must be 'date', 'time' or 'complete'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport default function formatISO9075(dirtyDate, options) {\n  var _options$format, _options$representati;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var format = String((_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : 'extended');\n  var representation = String((_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : 'complete');\n  if (format !== 'extended' && format !== 'basic') {\n    throw new RangeError(\"format must be 'extended' or 'basic'\");\n  }\n  if (representation !== 'date' && representation !== 'time' && representation !== 'complete') {\n    throw new RangeError(\"representation must be 'date', 'time', or 'complete'\");\n  }\n  var result = '';\n  var dateDelimiter = format === 'extended' ? '-' : '';\n  var timeDelimiter = format === 'extended' ? ':' : '';\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== 'time') {\n    var day = addLeadingZeros(originalDate.getDate(), 2);\n    var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n    var year = addLeadingZeros(originalDate.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = \"\".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== 'date') {\n    var hour = addLeadingZeros(originalDate.getHours(), 2);\n    var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n    var second = addLeadingZeros(originalDate.getSeconds(), 2);\n\n    // If there's also date, separate it with time with a space\n    var separator = result === '' ? '' : ' ';\n\n    // HHmmss or HH:mm:ss.\n    result = \"\".concat(result).concat(separator).concat(hour).concat(timeDelimiter).concat(minute).concat(timeDelimiter).concat(second);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["toDate", "<PERSON><PERSON><PERSON><PERSON>", "addLeadingZeros", "formatISO9075", "dirtyDate", "options", "_options$format", "_options$representati", "arguments", "length", "TypeError", "concat", "originalDate", "RangeError", "format", "String", "representation", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeDelimiter", "day", "getDate", "month", "getMonth", "year", "getFullYear", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "separator"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/formatISO9075/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\n/**\n * @name formatISO9075\n * @category Common Helpers\n * @summary Format the date according to the ISO 9075 standard (https://dev.mysql.com/doc/refman/5.7/en/date-and-time-functions.html#function_get-format).\n *\n * @description\n * Return the formatted date string in ISO 9075 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {'extended'|'basic'} [options.format='extended'] - if 'basic', hide delimiters between date and time values.\n * @param {'complete'|'date'|'time'} [options.representation='complete'] - format date, time, or both.\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.format` must be 'extended' or 'basic'\n * @throws {RangeError} `options.representation` must be 'date', 'time' or 'complete'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18 19:00:52'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075, short format:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918 190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, date only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 9075 format, time only:\n * const result = formatISO9075(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52'\n */\nexport default function formatISO9075(dirtyDate, options) {\n  var _options$format, _options$representati;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var format = String((_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : 'extended');\n  var representation = String((_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : 'complete');\n  if (format !== 'extended' && format !== 'basic') {\n    throw new RangeError(\"format must be 'extended' or 'basic'\");\n  }\n  if (representation !== 'date' && representation !== 'time' && representation !== 'complete') {\n    throw new RangeError(\"representation must be 'date', 'time', or 'complete'\");\n  }\n  var result = '';\n  var dateDelimiter = format === 'extended' ? '-' : '';\n  var timeDelimiter = format === 'extended' ? ':' : '';\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== 'time') {\n    var day = addLeadingZeros(originalDate.getDate(), 2);\n    var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n    var year = addLeadingZeros(originalDate.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = \"\".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== 'date') {\n    var hour = addLeadingZeros(originalDate.getHours(), 2);\n    var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n    var second = addLeadingZeros(originalDate.getSeconds(), 2);\n\n    // If there's also date, separate it with time with a space\n    var separator = result === '' ? '' : ' ';\n\n    // HHmmss or HH:mm:ss.\n    result = \"\".concat(result).concat(separator).concat(hour).concat(timeDelimiter).concat(minute).concat(timeDelimiter).concat(second);\n  }\n  return result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACxD,IAAIC,eAAe,EAAEC,qBAAqB;EAC1C,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,gCAAgC,CAACC,MAAM,CAACH,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC,CAAC;EAC5F;EACA,IAAIG,YAAY,GAAGZ,MAAM,CAACI,SAAS,CAAC;EACpC,IAAI,CAACH,OAAO,CAACW,YAAY,CAAC,EAAE;IAC1B,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EACA,IAAIC,MAAM,GAAGC,MAAM,CAAC,CAACT,eAAe,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACS,MAAM,MAAM,IAAI,IAAIR,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,UAAU,CAAC;EAC/K,IAAIU,cAAc,GAAGD,MAAM,CAAC,CAACR,qBAAqB,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,cAAc,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,UAAU,CAAC;EACjN,IAAIO,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,OAAO,EAAE;IAC/C,MAAM,IAAID,UAAU,CAAC,sCAAsC,CAAC;EAC9D;EACA,IAAIG,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,UAAU,EAAE;IAC3F,MAAM,IAAIH,UAAU,CAAC,sDAAsD,CAAC;EAC9E;EACA,IAAII,MAAM,GAAG,EAAE;EACf,IAAIC,aAAa,GAAGJ,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;EACpD,IAAIK,aAAa,GAAGL,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE;;EAEpD;EACA,IAAIE,cAAc,KAAK,MAAM,EAAE;IAC7B,IAAII,GAAG,GAAGlB,eAAe,CAACU,YAAY,CAACS,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACpD,IAAIC,KAAK,GAAGpB,eAAe,CAACU,YAAY,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC3D,IAAIC,IAAI,GAAGtB,eAAe,CAACU,YAAY,CAACa,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEzD;IACAR,MAAM,GAAG,EAAE,CAACN,MAAM,CAACa,IAAI,CAAC,CAACb,MAAM,CAACO,aAAa,CAAC,CAACP,MAAM,CAACW,KAAK,CAAC,CAACX,MAAM,CAACO,aAAa,CAAC,CAACP,MAAM,CAACS,GAAG,CAAC;EAChG;;EAEA;EACA,IAAIJ,cAAc,KAAK,MAAM,EAAE;IAC7B,IAAIU,IAAI,GAAGxB,eAAe,CAACU,YAAY,CAACe,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD,IAAIC,MAAM,GAAG1B,eAAe,CAACU,YAAY,CAACiB,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1D,IAAIC,MAAM,GAAG5B,eAAe,CAACU,YAAY,CAACmB,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;;IAE1D;IACA,IAAIC,SAAS,GAAGf,MAAM,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG;;IAExC;IACAA,MAAM,GAAG,EAAE,CAACN,MAAM,CAACM,MAAM,CAAC,CAACN,MAAM,CAACqB,SAAS,CAAC,CAACrB,MAAM,CAACe,IAAI,CAAC,CAACf,MAAM,CAACQ,aAAa,CAAC,CAACR,MAAM,CAACiB,MAAM,CAAC,CAACjB,MAAM,CAACQ,aAAa,CAAC,CAACR,MAAM,CAACmB,MAAM,CAAC;EACrI;EACA,OAAOb,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}