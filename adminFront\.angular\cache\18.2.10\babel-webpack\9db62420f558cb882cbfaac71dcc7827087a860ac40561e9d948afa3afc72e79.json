{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBaseFunctionGetFunctionPost$Json } from '../fn/base-function/api-base-function-get-function-post-json';\nimport { apiBaseFunctionGetFunctionPost$Plain } from '../fn/base-function/api-base-function-get-function-post-plain';\nimport { apiBaseFunctionGetStatusPost$Json } from '../fn/base-function/api-base-function-get-status-post-json';\nimport { apiBaseFunctionGetStatusPost$Plain } from '../fn/base-function/api-base-function-get-status-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class BaseFunctionService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiBaseFunctionGetStatusPost()` */\n  static {\n    this.ApiBaseFunctionGetStatusPostPath = '/api/BaseFunction/GetStatus';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseFunctionGetStatusPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetStatusPost$Plain$Response(params, context) {\n    return apiBaseFunctionGetStatusPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetStatusPost$Plain(params, context) {\n    return this.apiBaseFunctionGetStatusPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseFunctionGetStatusPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetStatusPost$Json$Response(params, context) {\n    return apiBaseFunctionGetStatusPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetStatusPost$Json(params, context) {\n    return this.apiBaseFunctionGetStatusPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBaseFunctionGetFunctionPost()` */\n  static {\n    this.ApiBaseFunctionGetFunctionPostPath = '/api/BaseFunction/GetFunction';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseFunctionGetFunctionPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetFunctionPost$Plain$Response(params, context) {\n    return apiBaseFunctionGetFunctionPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetFunctionPost$Plain(params, context) {\n    return this.apiBaseFunctionGetFunctionPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseFunctionGetFunctionPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetFunctionPost$Json$Response(params, context) {\n    return apiBaseFunctionGetFunctionPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseFunctionGetFunctionPost$Json(params, context) {\n    return this.apiBaseFunctionGetFunctionPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function BaseFunctionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseFunctionService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BaseFunctionService,\n      factory: BaseFunctionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiBaseFunctionGetFunctionPost$Json", "apiBaseFunctionGetFunctionPost$Plain", "apiBaseFunctionGetStatusPost$Json", "apiBaseFunctionGetStatusPost$Plain", "BaseFunctionService", "constructor", "config", "http", "ApiBaseFunctionGetStatusPostPath", "apiBaseFunctionGetStatusPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiBaseFunctionGetStatusPost$Json$Response", "ApiBaseFunctionGetFunctionPostPath", "apiBaseFunctionGetFunctionPost$Plain$Response", "apiBaseFunctionGetFunctionPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\base-function.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiBaseFunctionGetFunctionPost$Json } from '../fn/base-function/api-base-function-get-function-post-json';\r\nimport { ApiBaseFunctionGetFunctionPost$Json$Params } from '../fn/base-function/api-base-function-get-function-post-json';\r\nimport { apiBaseFunctionGetFunctionPost$Plain } from '../fn/base-function/api-base-function-get-function-post-plain';\r\nimport { ApiBaseFunctionGetFunctionPost$Plain$Params } from '../fn/base-function/api-base-function-get-function-post-plain';\r\nimport { apiBaseFunctionGetStatusPost$Json } from '../fn/base-function/api-base-function-get-status-post-json';\r\nimport { ApiBaseFunctionGetStatusPost$Json$Params } from '../fn/base-function/api-base-function-get-status-post-json';\r\nimport { apiBaseFunctionGetStatusPost$Plain } from '../fn/base-function/api-base-function-get-status-post-plain';\r\nimport { ApiBaseFunctionGetStatusPost$Plain$Params } from '../fn/base-function/api-base-function-get-status-post-plain';\r\nimport { EnumResponseListResponseBase } from '../models/enum-response-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class BaseFunctionService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiBaseFunctionGetStatusPost()` */\r\n  static readonly ApiBaseFunctionGetStatusPostPath = '/api/BaseFunction/GetStatus';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseFunctionGetStatusPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetStatusPost$Plain$Response(params?: ApiBaseFunctionGetStatusPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseFunctionGetStatusPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetStatusPost$Plain(params?: ApiBaseFunctionGetStatusPost$Plain$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseFunctionGetStatusPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseFunctionGetStatusPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetStatusPost$Json$Response(params?: ApiBaseFunctionGetStatusPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseFunctionGetStatusPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseFunctionGetStatusPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetStatusPost$Json(params?: ApiBaseFunctionGetStatusPost$Json$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseFunctionGetStatusPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBaseFunctionGetFunctionPost()` */\r\n  static readonly ApiBaseFunctionGetFunctionPostPath = '/api/BaseFunction/GetFunction';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseFunctionGetFunctionPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetFunctionPost$Plain$Response(params?: ApiBaseFunctionGetFunctionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseFunctionGetFunctionPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetFunctionPost$Plain(params?: ApiBaseFunctionGetFunctionPost$Plain$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseFunctionGetFunctionPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseFunctionGetFunctionPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetFunctionPost$Json$Response(params?: ApiBaseFunctionGetFunctionPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseFunctionGetFunctionPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseFunctionGetFunctionPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseFunctionGetFunctionPost$Json(params?: ApiBaseFunctionGetFunctionPost$Json$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseFunctionGetFunctionPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,mCAAmC,QAAQ,8DAA8D;AAElH,SAASC,oCAAoC,QAAQ,+DAA+D;AAEpH,SAASC,iCAAiC,QAAQ,4DAA4D;AAE9G,SAASC,kCAAkC,QAAQ,6DAA6D;;;;AAKhH,OAAM,MAAOC,mBAAoB,SAAQL,WAAW;EAClDM,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,gCAAgC,GAAG,6BAA6B;EAAC;EAEjF;;;;;;EAMAC,2CAA2CA,CAACC,MAAkD,EAAEC,OAAqB;IACnH,OAAOR,kCAAkC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAR,kCAAkCA,CAACO,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACF,2CAA2C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3Ef,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAC,0CAA0CA,CAACN,MAAiD,EAAEC,OAAqB;IACjH,OAAOT,iCAAiC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAT,iCAAiCA,CAACQ,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACK,0CAA0C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1Ef,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAE,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACR,MAAoD,EAAEC,OAAqB;IACvH,OAAOV,oCAAoC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAV,oCAAoCA,CAACS,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACO,6CAA6C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7Ef,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAI,4CAA4CA,CAACT,MAAmD,EAAEC,OAAqB;IACrH,OAAOX,mCAAmC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAX,mCAAmCA,CAACU,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACQ,4CAA4C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5Ef,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;;;uCAjGWX,mBAAmB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBrB,mBAAmB;MAAAsB,OAAA,EAAnBtB,mBAAmB,CAAAuB,IAAA;MAAAC,UAAA,EADN;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}