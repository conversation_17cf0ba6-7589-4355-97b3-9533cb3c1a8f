{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /** @preserve\n   * Counter block mode compatible with  Dr <PERSON> fileenc.c\n   * derived from CryptoJS.mode.CTR\n   * <NAME_EMAIL>\n   */\n  CryptoJS.mode.CTRGladman = function () {\n    var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();\n    function incWord(word) {\n      if ((word >> 24 & 0xff) === 0xff) {\n        //overflow\n        var b1 = word >> 16 & 0xff;\n        var b2 = word >> 8 & 0xff;\n        var b3 = word & 0xff;\n        if (b1 === 0xff)\n          // overflow b1\n          {\n            b1 = 0;\n            if (b2 === 0xff) {\n              b2 = 0;\n              if (b3 === 0xff) {\n                b3 = 0;\n              } else {\n                ++b3;\n              }\n            } else {\n              ++b2;\n            }\n          } else {\n          ++b1;\n        }\n        word = 0;\n        word += b1 << 16;\n        word += b2 << 8;\n        word += b3;\n      } else {\n        word += 0x01 << 24;\n      }\n      return word;\n    }\n    function incCounter(counter) {\n      if ((counter[0] = incWord(counter[0])) === 0) {\n        // encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8\n        counter[1] = incWord(counter[1]);\n      }\n      return counter;\n    }\n    var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var counter = this._counter;\n\n        // Generate keystream\n        if (iv) {\n          counter = this._counter = iv.slice(0);\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        }\n        incCounter(counter);\n        var keystream = counter.slice(0);\n        cipher.encryptBlock(keystream, 0);\n\n        // Encrypt\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    CTRGladman.Decryptor = Encryptor;\n    return CTRGladman;\n  }();\n  return CryptoJS.mode.CTRGladman;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "CTRGladman", "lib", "BlockCipherMode", "extend", "incWord", "word", "b1", "b2", "b3", "incCounter", "counter", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "iv", "_iv", "_counter", "slice", "undefined", "keystream", "encryptBlock", "i", "Decryptor"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/mode-ctr-gladman.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/** @preserve\n\t * Counter block mode compatible with  Dr <PERSON> fileenc.c\n\t * derived from CryptoJS.mode.CTR\n\t * <NAME_EMAIL>\n\t */\n\tCryptoJS.mode.CTRGladman = (function () {\n\t    var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();\n\n\t\tfunction incWord(word)\n\t\t{\n\t\t\tif (((word >> 24) & 0xff) === 0xff) { //overflow\n\t\t\tvar b1 = (word >> 16)&0xff;\n\t\t\tvar b2 = (word >> 8)&0xff;\n\t\t\tvar b3 = word & 0xff;\n\n\t\t\tif (b1 === 0xff) // overflow b1\n\t\t\t{\n\t\t\tb1 = 0;\n\t\t\tif (b2 === 0xff)\n\t\t\t{\n\t\t\t\tb2 = 0;\n\t\t\t\tif (b3 === 0xff)\n\t\t\t\t{\n\t\t\t\t\tb3 = 0;\n\t\t\t\t}\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t++b3;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\t++b2;\n\t\t\t}\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t++b1;\n\t\t\t}\n\n\t\t\tword = 0;\n\t\t\tword += (b1 << 16);\n\t\t\tword += (b2 << 8);\n\t\t\tword += b3;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\tword += (0x01 << 24);\n\t\t\t}\n\t\t\treturn word;\n\t\t}\n\n\t\tfunction incCounter(counter)\n\t\t{\n\t\t\tif ((counter[0] = incWord(counter[0])) === 0)\n\t\t\t{\n\t\t\t\t// encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8\n\t\t\t\tcounter[1] = incWord(counter[1]);\n\t\t\t}\n\t\t\treturn counter;\n\t\t}\n\n\t    var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher\n\t            var blockSize = cipher.blockSize;\n\t            var iv = this._iv;\n\t            var counter = this._counter;\n\n\t            // Generate keystream\n\t            if (iv) {\n\t                counter = this._counter = iv.slice(0);\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            }\n\n\t\t\t\tincCounter(counter);\n\n\t\t\t\tvar keystream = counter.slice(0);\n\t            cipher.encryptBlock(keystream, 0);\n\n\t            // Encrypt\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= keystream[i];\n\t            }\n\t        }\n\t    });\n\n\t    CTRGladman.Decryptor = Encryptor;\n\n\t    return CTRGladman;\n\t}());\n\n\n\n\n\treturn CryptoJS.mode.CTRGladman;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;AACA;AACA;EACCA,QAAQ,CAACC,IAAI,CAACC,UAAU,GAAI,YAAY;IACpC,IAAIA,UAAU,GAAGF,QAAQ,CAACG,GAAG,CAACC,eAAe,CAACC,MAAM,CAAC,CAAC;IAEzD,SAASC,OAAOA,CAACC,IAAI,EACrB;MACC,IAAI,CAAEA,IAAI,IAAI,EAAE,GAAI,IAAI,MAAM,IAAI,EAAE;QAAE;QACtC,IAAIC,EAAE,GAAID,IAAI,IAAI,EAAE,GAAE,IAAI;QAC1B,IAAIE,EAAE,GAAIF,IAAI,IAAI,CAAC,GAAE,IAAI;QACzB,IAAIG,EAAE,GAAGH,IAAI,GAAG,IAAI;QAEpB,IAAIC,EAAE,KAAK,IAAI;UAAE;UACjB;YACAA,EAAE,GAAG,CAAC;YACN,IAAIC,EAAE,KAAK,IAAI,EACf;cACCA,EAAE,GAAG,CAAC;cACN,IAAIC,EAAE,KAAK,IAAI,EACf;gBACCA,EAAE,GAAG,CAAC;cACP,CAAC,MAED;gBACC,EAAEA,EAAE;cACL;YACD,CAAC,MAED;cACC,EAAED,EAAE;YACL;UACA,CAAC,MAED;UACA,EAAED,EAAE;QACJ;QAEAD,IAAI,GAAG,CAAC;QACRA,IAAI,IAAKC,EAAE,IAAI,EAAG;QAClBD,IAAI,IAAKE,EAAE,IAAI,CAAE;QACjBF,IAAI,IAAIG,EAAE;MACV,CAAC,MAED;QACAH,IAAI,IAAK,IAAI,IAAI,EAAG;MACpB;MACA,OAAOA,IAAI;IACZ;IAEA,SAASI,UAAUA,CAACC,OAAO,EAC3B;MACC,IAAI,CAACA,OAAO,CAAC,CAAC,CAAC,GAAGN,OAAO,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAC5C;QACC;QACAA,OAAO,CAAC,CAAC,CAAC,GAAGN,OAAO,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC;MACA,OAAOA,OAAO;IACf;IAEG,IAAIC,SAAS,GAAGX,UAAU,CAACW,SAAS,GAAGX,UAAU,CAACG,MAAM,CAAC;MACrDS,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC;QACA,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;QACzB,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS;QAChC,IAAIC,EAAE,GAAG,IAAI,CAACC,GAAG;QACjB,IAAIT,OAAO,GAAG,IAAI,CAACU,QAAQ;;QAE3B;QACA,IAAIF,EAAE,EAAE;UACJR,OAAO,GAAG,IAAI,CAACU,QAAQ,GAAGF,EAAE,CAACG,KAAK,CAAC,CAAC,CAAC;;UAErC;UACA,IAAI,CAACF,GAAG,GAAGG,SAAS;QACxB;QAETb,UAAU,CAACC,OAAO,CAAC;QAEnB,IAAIa,SAAS,GAAGb,OAAO,CAACW,KAAK,CAAC,CAAC,CAAC;QACvBN,MAAM,CAACS,YAAY,CAACD,SAAS,EAAE,CAAC,CAAC;;QAEjC;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,EAAEQ,CAAC,EAAE,EAAE;UAChCZ,KAAK,CAACC,MAAM,GAAGW,CAAC,CAAC,IAAIF,SAAS,CAACE,CAAC,CAAC;QACrC;MACJ;IACJ,CAAC,CAAC;IAEFzB,UAAU,CAAC0B,SAAS,GAAGf,SAAS;IAEhC,OAAOX,UAAU;EACrB,CAAC,CAAC,CAAE;EAKJ,OAAOF,QAAQ,CAACC,IAAI,CAACC,UAAU;AAEhC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}