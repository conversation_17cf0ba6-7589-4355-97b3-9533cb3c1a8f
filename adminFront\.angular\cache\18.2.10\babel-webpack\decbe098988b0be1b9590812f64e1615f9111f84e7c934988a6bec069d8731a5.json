{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class NumberWithCommasPipe {\n  transform(input) {\n    return new Intl.NumberFormat().format(input);\n  }\n  static {\n    this.ɵfac = function NumberWithCommasPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NumberWithCommasPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"ngxNumberWithCommas\",\n      type: NumberWithCommasPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["NumberWithCommasPipe", "transform", "input", "Intl", "NumberFormat", "format", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\number-with-commas.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'ngxNumberWithCommas',\r\n    standalone: true\r\n})\r\nexport class NumberWithCommasPipe implements PipeTransform {\r\n\r\n  transform(input: number): string {\r\n    return new Intl.NumberFormat().format(input);\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,oBAAoB;EAE/BC,SAASA,CAACC,KAAa;IACrB,OAAO,IAAIC,IAAI,CAACC,YAAY,EAAE,CAACC,MAAM,CAACH,KAAK,CAAC;EAC9C;;;uCAJWF,oBAAoB;IAAA;EAAA;;;;YAApBA,oBAAoB;MAAAM,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}