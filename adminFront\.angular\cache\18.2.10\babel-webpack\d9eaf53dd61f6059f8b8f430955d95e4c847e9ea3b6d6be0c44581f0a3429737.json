{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { ValueSetter } from \"./Setter.js\";\nexport var Parser = /*#__PURE__*/function () {\n  function Parser() {\n    _classCallCheck(this, Parser);\n    _defineProperty(this, \"incompatibleTokens\", void 0);\n    _defineProperty(this, \"priority\", void 0);\n    _defineProperty(this, \"subPriority\", void 0);\n  }\n  _createClass(Parser, [{\n    key: \"run\",\n    value: function run(dateString, token, match, options) {\n      var result = this.parse(dateString, token, match, options);\n      if (!result) {\n        return null;\n      }\n      return {\n        setter: new ValueSetter(result.value, this.validate, this.set, this.priority, this.subPriority),\n        rest: result.rest\n      };\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_utcDate, _value, _options) {\n      return true;\n    }\n  }]);\n  return Parser;\n}();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}