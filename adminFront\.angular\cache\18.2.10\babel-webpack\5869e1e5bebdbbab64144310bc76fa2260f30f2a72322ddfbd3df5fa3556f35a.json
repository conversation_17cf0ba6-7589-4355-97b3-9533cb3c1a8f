{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs/90001437-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param {Duration} duration - the duration to format\n *\n * @returns {String} The ISO 8601 duration string\n * @throws {TypeError} Requires 1 argument\n * @throws {Error} Argument must be an object\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport default function formatISODuration(duration) {\n  requiredArgs(1, arguments);\n  if (_typeof(duration) !== 'object') throw new Error('Duration must be an object');\n  var _duration$years = duration.years,\n    years = _duration$years === void 0 ? 0 : _duration$years,\n    _duration$months = duration.months,\n    months = _duration$months === void 0 ? 0 : _duration$months,\n    _duration$days = duration.days,\n    days = _duration$days === void 0 ? 0 : _duration$days,\n    _duration$hours = duration.hours,\n    hours = _duration$hours === void 0 ? 0 : _duration$hours,\n    _duration$minutes = duration.minutes,\n    minutes = _duration$minutes === void 0 ? 0 : _duration$minutes,\n    _duration$seconds = duration.seconds,\n    seconds = _duration$seconds === void 0 ? 0 : _duration$seconds;\n  return \"P\".concat(years, \"Y\").concat(months, \"M\").concat(days, \"DT\").concat(hours, \"H\").concat(minutes, \"M\").concat(seconds, \"S\");\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}