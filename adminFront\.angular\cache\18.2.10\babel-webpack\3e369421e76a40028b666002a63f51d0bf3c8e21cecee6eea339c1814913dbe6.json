{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { PagesComponent } from './pages/pages.component';\nimport { HomeComponent } from './pages/home/<USER>';\nimport { LoginComponent } from './pages/login/login.component';\nimport { LogoutComponent } from './pages/logout/logout.component';\nimport { AuthGuard } from './shared/auth/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n//_import保留字\nexport const routes = [{\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'logout',\n  component: LogoutComponent\n}, {\n  path: '',\n  component: PagesComponent,\n  canActivate: [AuthGuard],\n  children: [{\n    path: '',\n    component: HomeComponent\n  },\n  // { path: 'TempSaveSampleComponent', component: TempSaveSampleComponent },\n  {\n    path: 'pages',\n    loadChildren: () => import('./pages/system-management/system-management.module').then(mod => mod.SystemManagementModule)\n  }]\n}, {\n  path: '**',\n  redirectTo: '/'\n}];\nconst config = {\n  useHash: false\n};\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes, {\n          onSameUrlNavigation: `reload`\n        }), RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}