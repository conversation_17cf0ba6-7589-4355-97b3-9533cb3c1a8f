{"ast": null, "code": "export var EnumPayStatus;\n(function (EnumPayStatus) {\n  EnumPayStatus[EnumPayStatus[\"\\u672A\\u4ED8\\u6B3E\"] = 0] = \"\\u672A\\u4ED8\\u6B3E\";\n  EnumPayStatus[EnumPayStatus[\"\\u5DF2\\u4ED8\\u6B3E\"] = 1] = \"\\u5DF2\\u4ED8\\u6B3E\";\n  EnumPayStatus[EnumPayStatus[\"\\u7121\\u9808\\u4ED8\\u6B3E\"] = 2] = \"\\u7121\\u9808\\u4ED8\\u6B3E\";\n})(EnumPayStatus || (EnumPayStatus = {}));", "map": {"version": 3, "names": ["EnumPayStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumPayStatus.ts"], "sourcesContent": ["export enum EnumPayStatus {\r\n  未付款 = 0, //unpaid\r\n  已付款 = 1,  //paid\r\n  無須付款 = 2, //No payment required\r\n}\r\n"], "mappings": "AAAA,WAAYA,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,kDAAO;EACPA,aAAA,CAAAA,aAAA,kDAAO;EACPA,aAAA,CAAAA,aAAA,8DAAQ;AACV,CAAC,EAJWA,aAAa,KAAbA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}