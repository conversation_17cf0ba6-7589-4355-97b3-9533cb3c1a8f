{"ast": null, "code": "import { CORPORATE_THEME as baseTheme } from '@nebular/theme';\nconst baseThemeVariables = baseTheme.variables;\nexport const CORPORATE_THEME = {\n  name: 'corporate',\n  base: 'corporate',\n  variables: {\n    temperature: {\n      arcFill: ['#ffa36b', '#ffa36b', '#ff9e7a', '#ff9888', '#ff8ea0'],\n      arcEmpty: baseThemeVariables['bg2'],\n      thumbBg: baseThemeVariables['bg2'],\n      thumbBorder: '#ffa36b'\n    },\n    solar: {\n      gradientLeft: baseThemeVariables['primary'],\n      gradientRight: baseThemeVariables['primary'],\n      shadowColor: 'rgba(0, 0, 0, 0)',\n      secondSeriesFill: baseThemeVariables['bg2'],\n      radius: ['80%', '90%']\n    },\n    traffic: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      yAxisSplitLine: 'rgba(0, 0, 0, 0)',\n      lineBg: baseThemeVariables['primary'],\n      lineShadowBlur: '0',\n      itemColor: baseThemeVariables['border4'],\n      itemBorderColor: baseThemeVariables['border4'],\n      itemEmphasisBorderColor: baseThemeVariables['primaryLight'],\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\n      gradFrom: baseThemeVariables['bg'],\n      gradTo: baseThemeVariables['bg']\n    },\n    electricity: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: baseThemeVariables['fgText'],\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      axisLineColor: baseThemeVariables['border3'],\n      xAxisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      lineGradFrom: baseThemeVariables['primary'],\n      lineGradTo: baseThemeVariables['primary'],\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: 'rgba(0, 0, 0, 0)',\n      areaGradTo: 'rgba(0, 0, 0, 0)',\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    bubbleMap: {\n      titleColor: baseThemeVariables['fgText'],\n      areaColor: baseThemeVariables['bg4'],\n      areaHoverColor: baseThemeVariables['fgHighlight'],\n      areaBorderColor: baseThemeVariables['border5']\n    },\n    profitBarAnimationEchart: {\n      textColor: baseThemeVariables['fgText'],\n      firstAnimationBarColor: baseThemeVariables['primary'],\n      secondAnimationBarColor: baseThemeVariables['success'],\n      splitLineStyleOpacity: '1',\n      splitLineStyleWidth: '1',\n      splitLineStyleColor: baseThemeVariables['separator'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    },\n    trafficBarEchart: {\n      gradientFrom: baseThemeVariables['warningLight'],\n      gradientTo: baseThemeVariables['warning'],\n      shadow: baseThemeVariables['warningLight'],\n      shadowBlur: '0',\n      axisTextColor: baseThemeVariables['fgText'],\n      axisFontSize: '12',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal'\n    },\n    countryOrders: {\n      countryBorderColor: baseThemeVariables['border4'],\n      countryFillColor: baseThemeVariables['bg4'],\n      countryBorderWidth: '1',\n      hoveredCountryBorderColor: baseThemeVariables['primary'],\n      hoveredCountryFillColor: baseThemeVariables['primaryLight'],\n      hoveredCountryBorderWidth: '1',\n      chartAxisLineColor: baseThemeVariables['border4'],\n      chartAxisTextColor: baseThemeVariables['fg'],\n      chartAxisFontSize: '16',\n      chartGradientTo: baseThemeVariables['primary'],\n      chartGradientFrom: baseThemeVariables['primaryLight'],\n      chartAxisSplitLine: baseThemeVariables['separator'],\n      chartShadowLineColor: baseThemeVariables['primaryLight'],\n      chartLineBottomShadowColor: baseThemeVariables['primary'],\n      chartInnerLineColor: baseThemeVariables['bg2']\n    },\n    echarts: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['fgText'],\n      splitLineColor: baseThemeVariables['separator'],\n      itemHoverShadowColor: 'rgba(0, 0, 0, 0.5)',\n      tooltipBackgroundColor: baseThemeVariables['primary'],\n      areaOpacity: '0.7'\n    },\n    chartjs: {\n      axisLineColor: baseThemeVariables['separator'],\n      textColor: baseThemeVariables['fgText']\n    },\n    orders: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      // first line\n      firstAreaGradFrom: baseThemeVariables['bg3'],\n      firstAreaGradTo: baseThemeVariables['bg3'],\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // second line\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondAreaGradFrom: 'rgba(0, 0, 0, 0)',\n      secondAreaGradTo: 'rgba(0, 0, 0, 0)',\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // third line\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['successLight'],\n      thirdAreaGradFrom: 'rgba(0, 0, 0, 0)',\n      thirdAreaGradTo: 'rgba(0, 0, 0, 0)',\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    profit: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['border4'],\n      splitLineColor: baseThemeVariables['separator'],\n      areaOpacity: '1',\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      // first bar\n      firstLineGradFrom: baseThemeVariables['bg3'],\n      firstLineGradTo: baseThemeVariables['bg3'],\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\n      // second bar\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\n      // third bar\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['success'],\n      thirdLineShadow: 'rgba(0, 0, 0, 0)'\n    },\n    orderProfitLegend: {\n      firstItem: baseThemeVariables['success'],\n      secondItem: baseThemeVariables['primary'],\n      thirdItem: baseThemeVariables['bg3']\n    },\n    visitors: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '1',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'dotted',\n      lineWidth: '6',\n      lineGradFrom: '#ffffff',\n      lineGradTo: '#ffffff',\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: baseThemeVariables['primary'],\n      areaGradTo: baseThemeVariables['primaryLight'],\n      innerLineStyle: 'solid',\n      innerLineWidth: '1',\n      innerAreaGradFrom: baseThemeVariables['success'],\n      innerAreaGradTo: baseThemeVariables['success']\n    },\n    visitorsLegend: {\n      firstIcon: baseThemeVariables['success'],\n      secondIcon: baseThemeVariables['primary']\n    },\n    visitorsPie: {\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      firstPieRadius: ['65%', '90%'],\n      secondPieGradientLeft: baseThemeVariables['warning'],\n      secondPieGradientRight: baseThemeVariables['warningLight'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieRadius: ['63%', '92%'],\n      shadowOffsetX: '-4',\n      shadowOffsetY: '-4'\n    },\n    visitorsPieLegend: {\n      firstSection: baseThemeVariables['warning'],\n      secondSection: baseThemeVariables['success']\n    },\n    earningPie: {\n      radius: ['65%', '100%'],\n      center: ['50%', '50%'],\n      fontSize: '22',\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieGradientLeft: baseThemeVariables['primary'],\n      secondPieGradientRight: baseThemeVariables['primary'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      thirdPieGradientLeft: baseThemeVariables['warning'],\n      thirdPieGradientRight: baseThemeVariables['warning'],\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)'\n    },\n    earningLine: {\n      gradFrom: baseThemeVariables['primary'],\n      gradTo: baseThemeVariables['primary'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    }\n  }\n};", "map": {"version": 3, "names": ["CORPORATE_THEME", "baseTheme", "baseThemeVariables", "variables", "name", "base", "temperature", "arcFill", "arcEmpty", "thumbBg", "thumbBorder", "solar", "gradientLeft", "gradientRight", "shadowColor", "secondSeriesFill", "radius", "traffic", "tooltipBg", "tooltipBorderColor", "tooltipExtraCss", "tooltipTextColor", "tooltipFontWeight", "yAxisSplitLine", "lineBg", "lineShadowBlur", "itemColor", "itemBorderColor", "itemEmphasisBorderColor", "shadowLineDarkBg", "shadowLineShadow", "gradFrom", "gradTo", "electricity", "tooltipLineColor", "tooltipLineWidth", "axisLineColor", "xAxisTextColor", "lineStyle", "lineWidth", "lineGradFrom", "lineGradTo", "lineShadow", "areaGradFrom", "areaGradTo", "bubbleMap", "titleColor", "areaColor", "areaHoverColor", "areaBorderColor", "profitBarAnimationEchart", "textColor", "firstAnimationBarColor", "secondAnimationBarColor", "splitLineStyleOpacity", "splitLineStyleWidth", "splitLineStyleColor", "tooltipFontSize", "tooltipBorderWidth", "trafficBarEchart", "gradientFrom", "gradientTo", "shadow", "<PERSON><PERSON><PERSON><PERSON>", "axisTextColor", "axisFontSize", "countryOrders", "countryBorderColor", "countryFillColor", "countryBorderWidth", "hoveredCountryBorderColor", "hoveredCountryFillColor", "hoveredCountryBorderWidth", "chartAxisLineColor", "chartAxisTextColor", "chartAxisFontSize", "chartGradientTo", "chartGradientFrom", "chartAxisSplitLine", "chartShadowLineColor", "chartLineBottomShadowColor", "chartInnerLineColor", "echarts", "bg", "splitLineColor", "itemHoverShadowColor", "tooltipBackgroundColor", "areaOpacity", "chartjs", "orders", "firstAreaGradFrom", "firstAreaGradTo", "firstShadowLineDarkBg", "secondLineGradFrom", "secondLineGradTo", "secondAreaGradFrom", "secondAreaGradTo", "secondShadowLineDarkBg", "thirdLineGradFrom", "thirdLineGradTo", "thirdAreaGradFrom", "thirdAreaGradTo", "thirdShadowLineDarkBg", "profit", "firstLineGradFrom", "firstLineGradTo", "firstLineShadow", "secondLineShadow", "thirdLineShadow", "orderProfitLegend", "firstItem", "secondItem", "thirdItem", "visitors", "innerLineStyle", "innerLineWidth", "innerAreaGradFrom", "innerAreaGradTo", "visitorsLegend", "firstIcon", "secondIcon", "visitors<PERSON>ie", "firstPieGradientLeft", "firstPieGradientRight", "firstPieShadowColor", "firstPieRadius", "secondPieGradientLeft", "secondPieGradientRight", "secondPieShadowColor", "secondPieRadius", "shadowOffsetX", "shadowOffsetY", "visitorsPieLegend", "firstSection", "secondSection", "earning<PERSON>ie", "center", "fontSize", "thirdPieGradientLeft", "thirdPieGradientRight", "thirdPieShadowColor", "earningLine"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\styles\\theme.corporate.ts"], "sourcesContent": ["import { NbJSThemeOptions, CORPORATE_THEME as baseTheme } from '@nebular/theme';\r\n\r\nconst baseThemeVariables = baseTheme.variables!;\r\n\r\nexport const CORPORATE_THEME = {\r\n  name: 'corporate',\r\n  base: 'corporate',\r\n  variables: {\r\n    temperature: {\r\n      arcFill: [ '#ffa36b', '#ffa36b', '#ff9e7a', '#ff9888', '#ff8ea0' ],\r\n      arcEmpty: baseThemeVariables['bg2'],\r\n      thumbBg: baseThemeVariables['bg2'],\r\n      thumbBorder: '#ffa36b',\r\n    },\r\n\r\n    solar: {\r\n      gradientLeft: baseThemeVariables['primary'],\r\n      gradientRight: baseThemeVariables['primary'],\r\n      shadowColor: 'rgba(0, 0, 0, 0)',\r\n      secondSeriesFill: baseThemeVariables['bg2'],\r\n      radius: ['80%', '90%'],\r\n    },\r\n\r\n    traffic: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n\r\n      yAxisSplitLine: 'rgba(0, 0, 0, 0)',\r\n\r\n      lineBg: baseThemeVariables['primary'],\r\n      lineShadowBlur: '0',\r\n      itemColor: baseThemeVariables['border4'],\r\n      itemBorderColor: baseThemeVariables['border4'],\r\n      itemEmphasisBorderColor: baseThemeVariables['primaryLight'],\r\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\r\n      gradFrom: baseThemeVariables['bg'],\r\n      gradTo: baseThemeVariables['bg'],\r\n    },\r\n\r\n    electricity: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: baseThemeVariables['fgText'],\r\n      tooltipLineWidth: '0',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n\r\n      axisLineColor: baseThemeVariables['border3'],\r\n      xAxisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'solid',\r\n      lineWidth: '4',\r\n      lineGradFrom: baseThemeVariables['primary'],\r\n      lineGradTo: baseThemeVariables['primary'],\r\n      lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      areaGradFrom: 'rgba(0, 0, 0, 0)',\r\n      areaGradTo: 'rgba(0, 0, 0, 0)',\r\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    bubbleMap: {\r\n      titleColor: baseThemeVariables['fgText'],\r\n      areaColor: baseThemeVariables['bg4'],\r\n      areaHoverColor: baseThemeVariables['fgHighlight'],\r\n      areaBorderColor: baseThemeVariables['border5'],\r\n    },\r\n\r\n    profitBarAnimationEchart: {\r\n      textColor: baseThemeVariables['fgText'],\r\n\r\n      firstAnimationBarColor: baseThemeVariables['primary'],\r\n      secondAnimationBarColor: baseThemeVariables['success'],\r\n\r\n      splitLineStyleOpacity: '1',\r\n      splitLineStyleWidth: '1',\r\n      splitLineStyleColor: baseThemeVariables['separator'],\r\n\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '16',\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipBorderWidth: '1',\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n    },\r\n\r\n    trafficBarEchart: {\r\n      gradientFrom: baseThemeVariables['warningLight'],\r\n      gradientTo: baseThemeVariables['warning'],\r\n      shadow: baseThemeVariables['warningLight'],\r\n      shadowBlur: '0',\r\n\r\n      axisTextColor: baseThemeVariables['fgText'],\r\n      axisFontSize: '12',\r\n\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n    },\r\n\r\n    countryOrders: {\r\n      countryBorderColor: baseThemeVariables['border4'],\r\n      countryFillColor: baseThemeVariables['bg4'],\r\n      countryBorderWidth: '1',\r\n      hoveredCountryBorderColor: baseThemeVariables['primary'],\r\n      hoveredCountryFillColor: baseThemeVariables['primaryLight'],\r\n      hoveredCountryBorderWidth: '1',\r\n\r\n      chartAxisLineColor: baseThemeVariables['border4'],\r\n      chartAxisTextColor: baseThemeVariables['fg'],\r\n      chartAxisFontSize: '16',\r\n      chartGradientTo: baseThemeVariables['primary'],\r\n      chartGradientFrom: baseThemeVariables['primaryLight'],\r\n      chartAxisSplitLine: baseThemeVariables['separator'],\r\n      chartShadowLineColor: baseThemeVariables['primaryLight'],\r\n\r\n      chartLineBottomShadowColor: baseThemeVariables['primary'],\r\n\r\n      chartInnerLineColor: baseThemeVariables['bg2'],\r\n    },\r\n\r\n    echarts: {\r\n      bg: baseThemeVariables['bg'],\r\n      textColor: baseThemeVariables['fgText'],\r\n      axisLineColor: baseThemeVariables['fgText'],\r\n      splitLineColor: baseThemeVariables['separator'],\r\n      itemHoverShadowColor: 'rgba(0, 0, 0, 0.5)',\r\n      tooltipBackgroundColor: baseThemeVariables['primary'],\r\n      areaOpacity: '0.7',\r\n    },\r\n\r\n    chartjs: {\r\n      axisLineColor: baseThemeVariables['separator'],\r\n      textColor: baseThemeVariables['fgText'],\r\n    },\r\n\r\n    orders: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n      tooltipLineWidth: '0',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '20',\r\n\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'solid',\r\n      lineWidth: '4',\r\n\r\n      // first line\r\n      firstAreaGradFrom: baseThemeVariables['bg3'],\r\n      firstAreaGradTo: baseThemeVariables['bg3'],\r\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n      // second line\r\n      secondLineGradFrom: baseThemeVariables['primary'],\r\n      secondLineGradTo: baseThemeVariables['primary'],\r\n\r\n      secondAreaGradFrom: 'rgba(0, 0, 0, 0)',\r\n      secondAreaGradTo: 'rgba(0, 0, 0, 0)',\r\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n      // third line\r\n      thirdLineGradFrom: baseThemeVariables['success'],\r\n      thirdLineGradTo: baseThemeVariables['successLight'],\r\n\r\n      thirdAreaGradFrom: 'rgba(0, 0, 0, 0)',\r\n      thirdAreaGradTo: 'rgba(0, 0, 0, 0)',\r\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    profit: {\r\n      bg: baseThemeVariables['bg'],\r\n      textColor: baseThemeVariables['fgText'],\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      splitLineColor: baseThemeVariables['separator'],\r\n      areaOpacity: '1',\r\n\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n\r\n      // first bar\r\n      firstLineGradFrom: baseThemeVariables['bg3'],\r\n      firstLineGradTo: baseThemeVariables['bg3'],\r\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      // second bar\r\n      secondLineGradFrom: baseThemeVariables['primary'],\r\n      secondLineGradTo: baseThemeVariables['primary'],\r\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      // third bar\r\n      thirdLineGradFrom: baseThemeVariables['success'],\r\n      thirdLineGradTo: baseThemeVariables['success'],\r\n      thirdLineShadow: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    orderProfitLegend: {\r\n      firstItem: baseThemeVariables['success'],\r\n      secondItem: baseThemeVariables['primary'],\r\n      thirdItem: baseThemeVariables['bg3'],\r\n    },\r\n\r\n    visitors: {\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n      tooltipLineWidth: '1',\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '20',\r\n\r\n      axisLineColor: baseThemeVariables['border4'],\r\n      axisFontSize: '16',\r\n      axisTextColor: baseThemeVariables['fg'],\r\n      yAxisSplitLine: baseThemeVariables['separator'],\r\n\r\n      itemBorderColor: baseThemeVariables['primary'],\r\n      lineStyle: 'dotted',\r\n      lineWidth: '6',\r\n      lineGradFrom: '#ffffff',\r\n      lineGradTo: '#ffffff',\r\n      lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n      areaGradFrom: baseThemeVariables['primary'],\r\n      areaGradTo: baseThemeVariables['primaryLight'],\r\n\r\n      innerLineStyle: 'solid',\r\n      innerLineWidth: '1',\r\n\r\n      innerAreaGradFrom: baseThemeVariables['success'],\r\n      innerAreaGradTo: baseThemeVariables['success'],\r\n    },\r\n\r\n    visitorsLegend: {\r\n      firstIcon: baseThemeVariables['success'],\r\n      secondIcon: baseThemeVariables['primary'],\r\n    },\r\n\r\n    visitorsPie: {\r\n      firstPieGradientLeft: baseThemeVariables['success'],\r\n      firstPieGradientRight: baseThemeVariables['success'],\r\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n      firstPieRadius: ['65%', '90%'],\r\n\r\n      secondPieGradientLeft: baseThemeVariables['warning'],\r\n      secondPieGradientRight: baseThemeVariables['warningLight'],\r\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n      secondPieRadius: ['63%', '92%'],\r\n      shadowOffsetX: '-4',\r\n      shadowOffsetY: '-4',\r\n    },\r\n\r\n    visitorsPieLegend: {\r\n      firstSection: baseThemeVariables['warning'],\r\n      secondSection: baseThemeVariables['success'],\r\n    },\r\n\r\n    earningPie: {\r\n      radius: ['65%', '100%'],\r\n      center: ['50%', '50%'],\r\n\r\n      fontSize: '22',\r\n\r\n      firstPieGradientLeft: baseThemeVariables['success'],\r\n      firstPieGradientRight: baseThemeVariables['success'],\r\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n      secondPieGradientLeft: baseThemeVariables['primary'],\r\n      secondPieGradientRight: baseThemeVariables['primary'],\r\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n      thirdPieGradientLeft: baseThemeVariables['warning'],\r\n      thirdPieGradientRight: baseThemeVariables['warning'],\r\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n    },\r\n\r\n    earningLine: {\r\n      gradFrom: baseThemeVariables['primary'],\r\n      gradTo: baseThemeVariables['primary'],\r\n\r\n      tooltipTextColor: baseThemeVariables['fgText'],\r\n      tooltipFontWeight: 'normal',\r\n      tooltipFontSize: '16',\r\n      tooltipBg: baseThemeVariables['bg'],\r\n      tooltipBorderColor: baseThemeVariables['border2'],\r\n      tooltipBorderWidth: '1',\r\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n    },\r\n  },\r\n} as NbJSThemeOptions;\r\n"], "mappings": "AAAA,SAA2BA,eAAe,IAAIC,SAAS,QAAQ,gBAAgB;AAE/E,MAAMC,kBAAkB,GAAGD,SAAS,CAACE,SAAU;AAE/C,OAAO,MAAMH,eAAe,GAAG;EAC7BI,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,WAAW;EACjBF,SAAS,EAAE;IACTG,WAAW,EAAE;MACXC,OAAO,EAAE,CAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAE;MAClEC,QAAQ,EAAEN,kBAAkB,CAAC,KAAK,CAAC;MACnCO,OAAO,EAAEP,kBAAkB,CAAC,KAAK,CAAC;MAClCQ,WAAW,EAAE;KACd;IAEDC,KAAK,EAAE;MACLC,YAAY,EAAEV,kBAAkB,CAAC,SAAS,CAAC;MAC3CW,aAAa,EAAEX,kBAAkB,CAAC,SAAS,CAAC;MAC5CY,WAAW,EAAE,kBAAkB;MAC/BC,gBAAgB,EAAEb,kBAAkB,CAAC,KAAK,CAAC;MAC3Cc,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK;KACtB;IAEDC,OAAO,EAAE;MACPC,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCiB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDkB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAE3BC,cAAc,EAAE,kBAAkB;MAElCC,MAAM,EAAEtB,kBAAkB,CAAC,SAAS,CAAC;MACrCuB,cAAc,EAAE,GAAG;MACnBC,SAAS,EAAExB,kBAAkB,CAAC,SAAS,CAAC;MACxCyB,eAAe,EAAEzB,kBAAkB,CAAC,SAAS,CAAC;MAC9C0B,uBAAuB,EAAE1B,kBAAkB,CAAC,cAAc,CAAC;MAC3D2B,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,QAAQ,EAAE7B,kBAAkB,CAAC,IAAI,CAAC;MAClC8B,MAAM,EAAE9B,kBAAkB,CAAC,IAAI;KAChC;IAED+B,WAAW,EAAE;MACXf,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCgC,gBAAgB,EAAEhC,kBAAkB,CAAC,QAAQ,CAAC;MAC9CiC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDkB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAE3Bc,aAAa,EAAElC,kBAAkB,CAAC,SAAS,CAAC;MAC5CmC,cAAc,EAAEnC,kBAAkB,CAAC,IAAI,CAAC;MACxCqB,cAAc,EAAErB,kBAAkB,CAAC,WAAW,CAAC;MAE/CyB,eAAe,EAAEzB,kBAAkB,CAAC,SAAS,CAAC;MAC9CoC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAEtC,kBAAkB,CAAC,SAAS,CAAC;MAC3CuC,UAAU,EAAEvC,kBAAkB,CAAC,SAAS,CAAC;MACzCwC,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAE,kBAAkB;MAChCC,UAAU,EAAE,kBAAkB;MAC9Bf,gBAAgB,EAAE;KACnB;IAEDgB,SAAS,EAAE;MACTC,UAAU,EAAE5C,kBAAkB,CAAC,QAAQ,CAAC;MACxC6C,SAAS,EAAE7C,kBAAkB,CAAC,KAAK,CAAC;MACpC8C,cAAc,EAAE9C,kBAAkB,CAAC,aAAa,CAAC;MACjD+C,eAAe,EAAE/C,kBAAkB,CAAC,SAAS;KAC9C;IAEDgD,wBAAwB,EAAE;MACxBC,SAAS,EAAEjD,kBAAkB,CAAC,QAAQ,CAAC;MAEvCkD,sBAAsB,EAAElD,kBAAkB,CAAC,SAAS,CAAC;MACrDmD,uBAAuB,EAAEnD,kBAAkB,CAAC,SAAS,CAAC;MAEtDoD,qBAAqB,EAAE,GAAG;MAC1BC,mBAAmB,EAAE,GAAG;MACxBC,mBAAmB,EAAEtD,kBAAkB,CAAC,WAAW,CAAC;MAEpDmB,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCiB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDwD,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;KAClB;IAEDuC,gBAAgB,EAAE;MAChBC,YAAY,EAAE1D,kBAAkB,CAAC,cAAc,CAAC;MAChD2D,UAAU,EAAE3D,kBAAkB,CAAC,SAAS,CAAC;MACzC4D,MAAM,EAAE5D,kBAAkB,CAAC,cAAc,CAAC;MAC1C6D,UAAU,EAAE,GAAG;MAEfC,aAAa,EAAE9D,kBAAkB,CAAC,QAAQ,CAAC;MAC3C+D,YAAY,EAAE,IAAI;MAElB/C,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCiB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDkB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE;KACpB;IAED4C,aAAa,EAAE;MACbC,kBAAkB,EAAEjE,kBAAkB,CAAC,SAAS,CAAC;MACjDkE,gBAAgB,EAAElE,kBAAkB,CAAC,KAAK,CAAC;MAC3CmE,kBAAkB,EAAE,GAAG;MACvBC,yBAAyB,EAAEpE,kBAAkB,CAAC,SAAS,CAAC;MACxDqE,uBAAuB,EAAErE,kBAAkB,CAAC,cAAc,CAAC;MAC3DsE,yBAAyB,EAAE,GAAG;MAE9BC,kBAAkB,EAAEvE,kBAAkB,CAAC,SAAS,CAAC;MACjDwE,kBAAkB,EAAExE,kBAAkB,CAAC,IAAI,CAAC;MAC5CyE,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE1E,kBAAkB,CAAC,SAAS,CAAC;MAC9C2E,iBAAiB,EAAE3E,kBAAkB,CAAC,cAAc,CAAC;MACrD4E,kBAAkB,EAAE5E,kBAAkB,CAAC,WAAW,CAAC;MACnD6E,oBAAoB,EAAE7E,kBAAkB,CAAC,cAAc,CAAC;MAExD8E,0BAA0B,EAAE9E,kBAAkB,CAAC,SAAS,CAAC;MAEzD+E,mBAAmB,EAAE/E,kBAAkB,CAAC,KAAK;KAC9C;IAEDgF,OAAO,EAAE;MACPC,EAAE,EAAEjF,kBAAkB,CAAC,IAAI,CAAC;MAC5BiD,SAAS,EAAEjD,kBAAkB,CAAC,QAAQ,CAAC;MACvCkC,aAAa,EAAElC,kBAAkB,CAAC,QAAQ,CAAC;MAC3CkF,cAAc,EAAElF,kBAAkB,CAAC,WAAW,CAAC;MAC/CmF,oBAAoB,EAAE,oBAAoB;MAC1CC,sBAAsB,EAAEpF,kBAAkB,CAAC,SAAS,CAAC;MACrDqF,WAAW,EAAE;KACd;IAEDC,OAAO,EAAE;MACPpD,aAAa,EAAElC,kBAAkB,CAAC,WAAW,CAAC;MAC9CiD,SAAS,EAAEjD,kBAAkB,CAAC,QAAQ;KACvC;IAEDuF,MAAM,EAAE;MACNvE,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCgC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDkB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAElC,kBAAkB,CAAC,SAAS,CAAC;MAC5C+D,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAE9D,kBAAkB,CAAC,IAAI,CAAC;MACvCqB,cAAc,EAAErB,kBAAkB,CAAC,WAAW,CAAC;MAE/CyB,eAAe,EAAEzB,kBAAkB,CAAC,SAAS,CAAC;MAC9CoC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MAEd;MACAmD,iBAAiB,EAAExF,kBAAkB,CAAC,KAAK,CAAC;MAC5CyF,eAAe,EAAEzF,kBAAkB,CAAC,KAAK,CAAC;MAC1C0F,qBAAqB,EAAE,kBAAkB;MAEzC;MACAC,kBAAkB,EAAE3F,kBAAkB,CAAC,SAAS,CAAC;MACjD4F,gBAAgB,EAAE5F,kBAAkB,CAAC,SAAS,CAAC;MAE/C6F,kBAAkB,EAAE,kBAAkB;MACtCC,gBAAgB,EAAE,kBAAkB;MACpCC,sBAAsB,EAAE,kBAAkB;MAE1C;MACAC,iBAAiB,EAAEhG,kBAAkB,CAAC,SAAS,CAAC;MAChDiG,eAAe,EAAEjG,kBAAkB,CAAC,cAAc,CAAC;MAEnDkG,iBAAiB,EAAE,kBAAkB;MACrCC,eAAe,EAAE,kBAAkB;MACnCC,qBAAqB,EAAE;KACxB;IAEDC,MAAM,EAAE;MACNpB,EAAE,EAAEjF,kBAAkB,CAAC,IAAI,CAAC;MAC5BiD,SAAS,EAAEjD,kBAAkB,CAAC,QAAQ,CAAC;MACvCkC,aAAa,EAAElC,kBAAkB,CAAC,SAAS,CAAC;MAC5CkF,cAAc,EAAElF,kBAAkB,CAAC,WAAW,CAAC;MAC/CqF,WAAW,EAAE,GAAG;MAEhBtB,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAE9D,kBAAkB,CAAC,IAAI,CAAC;MAEvC;MACAsG,iBAAiB,EAAEtG,kBAAkB,CAAC,KAAK,CAAC;MAC5CuG,eAAe,EAAEvG,kBAAkB,CAAC,KAAK,CAAC;MAC1CwG,eAAe,EAAE,kBAAkB;MAEnC;MACAb,kBAAkB,EAAE3F,kBAAkB,CAAC,SAAS,CAAC;MACjD4F,gBAAgB,EAAE5F,kBAAkB,CAAC,SAAS,CAAC;MAC/CyG,gBAAgB,EAAE,kBAAkB;MAEpC;MACAT,iBAAiB,EAAEhG,kBAAkB,CAAC,SAAS,CAAC;MAChDiG,eAAe,EAAEjG,kBAAkB,CAAC,SAAS,CAAC;MAC9C0G,eAAe,EAAE;KAClB;IAEDC,iBAAiB,EAAE;MACjBC,SAAS,EAAE5G,kBAAkB,CAAC,SAAS,CAAC;MACxC6G,UAAU,EAAE7G,kBAAkB,CAAC,SAAS,CAAC;MACzC8G,SAAS,EAAE9G,kBAAkB,CAAC,KAAK;KACpC;IAED+G,QAAQ,EAAE;MACR/F,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCgC,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDkB,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAElC,kBAAkB,CAAC,SAAS,CAAC;MAC5C+D,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAE9D,kBAAkB,CAAC,IAAI,CAAC;MACvCqB,cAAc,EAAErB,kBAAkB,CAAC,WAAW,CAAC;MAE/CyB,eAAe,EAAEzB,kBAAkB,CAAC,SAAS,CAAC;MAC9CoC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE,SAAS;MACvBC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAEzC,kBAAkB,CAAC,SAAS,CAAC;MAC3C0C,UAAU,EAAE1C,kBAAkB,CAAC,cAAc,CAAC;MAE9CgH,cAAc,EAAE,OAAO;MACvBC,cAAc,EAAE,GAAG;MAEnBC,iBAAiB,EAAElH,kBAAkB,CAAC,SAAS,CAAC;MAChDmH,eAAe,EAAEnH,kBAAkB,CAAC,SAAS;KAC9C;IAEDoH,cAAc,EAAE;MACdC,SAAS,EAAErH,kBAAkB,CAAC,SAAS,CAAC;MACxCsH,UAAU,EAAEtH,kBAAkB,CAAC,SAAS;KACzC;IAEDuH,WAAW,EAAE;MACXC,oBAAoB,EAAExH,kBAAkB,CAAC,SAAS,CAAC;MACnDyH,qBAAqB,EAAEzH,kBAAkB,CAAC,SAAS,CAAC;MACpD0H,mBAAmB,EAAE,kBAAkB;MACvCC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAE9BC,qBAAqB,EAAE5H,kBAAkB,CAAC,SAAS,CAAC;MACpD6H,sBAAsB,EAAE7H,kBAAkB,CAAC,cAAc,CAAC;MAC1D8H,oBAAoB,EAAE,kBAAkB;MACxCC,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAC/BC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE;KAChB;IAEDC,iBAAiB,EAAE;MACjBC,YAAY,EAAEnI,kBAAkB,CAAC,SAAS,CAAC;MAC3CoI,aAAa,EAAEpI,kBAAkB,CAAC,SAAS;KAC5C;IAEDqI,UAAU,EAAE;MACVvH,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACvBwH,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAEtBC,QAAQ,EAAE,IAAI;MAEdf,oBAAoB,EAAExH,kBAAkB,CAAC,SAAS,CAAC;MACnDyH,qBAAqB,EAAEzH,kBAAkB,CAAC,SAAS,CAAC;MACpD0H,mBAAmB,EAAE,kBAAkB;MAEvCE,qBAAqB,EAAE5H,kBAAkB,CAAC,SAAS,CAAC;MACpD6H,sBAAsB,EAAE7H,kBAAkB,CAAC,SAAS,CAAC;MACrD8H,oBAAoB,EAAE,kBAAkB;MAExCU,oBAAoB,EAAExI,kBAAkB,CAAC,SAAS,CAAC;MACnDyI,qBAAqB,EAAEzI,kBAAkB,CAAC,SAAS,CAAC;MACpD0I,mBAAmB,EAAE;KACtB;IAEDC,WAAW,EAAE;MACX9G,QAAQ,EAAE7B,kBAAkB,CAAC,SAAS,CAAC;MACvC8B,MAAM,EAAE9B,kBAAkB,CAAC,SAAS,CAAC;MAErCmB,gBAAgB,EAAEnB,kBAAkB,CAAC,QAAQ,CAAC;MAC9CoB,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAEhB,kBAAkB,CAAC,IAAI,CAAC;MACnCiB,kBAAkB,EAAEjB,kBAAkB,CAAC,SAAS,CAAC;MACjDwD,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;;;CAGF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}