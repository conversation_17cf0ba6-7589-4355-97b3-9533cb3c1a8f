{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r3.add(dialog_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_53_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_53_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const data_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(56);\n      return i0.ɵɵresetView(ctx_r3.onEdit(data_r7, dialog_r5));\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_53_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_53_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const data_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onDelete(data_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 39);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 32);\n    i0.ɵɵtemplate(13, RequirementManagementComponent_tr_53_button_13_Template, 3, 0, \"button\", 34)(14, RequirementManagementComponent_tr_53_button_14_Template, 3, 0, \"button\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getHouseType(data_r7.CHouseType || i0.ɵɵpureFunction0(9, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r7.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 7, data_r7.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_55_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_55_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_55_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r10.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r10.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_55_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r11.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r11.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 40)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_55_span_2_Template, 2, 0, \"span\", 41)(3, RequirementManagementComponent_ng_template_55_span_3_Template, 2, 0, \"span\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 42)(5, \"div\", 43)(6, \"div\", 44)(7, \"div\", 4)(8, \"app-form-group\", 45)(9, \"nb-select\", 46);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_55_Template_nb_select_selectedChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveRequirement.CBuildCaseID, $event) || (ctx_r3.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_55_nb_option_10_Template, 2, 2, \"nb-option\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"app-form-group\", 45)(12, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_55_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveRequirement.CRequirement, $event) || (ctx_r3.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 45)(14, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_55_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveRequirement.CSort, $event) || (ctx_r3.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 45)(16, \"nb-checkbox\", 50);\n    i0.ɵɵlistener(\"checkedChange\", function RequirementManagementComponent_ng_template_55_Template_nb_checkbox_checkedChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseTypeChange(1, $event));\n    });\n    i0.ɵɵtext(17, \"\\u5730\\u4E3B\\u6236\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-checkbox\", 51);\n    i0.ɵɵlistener(\"checkedChange\", function RequirementManagementComponent_ng_template_55_Template_nb_checkbox_checkedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onHouseTypeChange(2, $event));\n    });\n    i0.ɵɵtext(19, \"\\u92B7\\u552E\\u6236\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 45)(21, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_55_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveRequirement.CStatus, $event) || (ctx_r3.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_55_nb_option_22_Template, 2, 2, \"nb-option\", 47);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(23, \"nb-card-footer\")(24, \"div\", 4)(25, \"div\", 53)(26, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_55_Template_button_click_26_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.save(ref_r12));\n    });\n    i0.ɵɵtext(27, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_55_Template_button_click_28_listener() {\n      const ref_r12 = i0.ɵɵrestoreView(_r9).dialogRef;\n      return i0.ɵɵresetView(ref_r12.close());\n    });\n    i0.ɵɵtext(29, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r3.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u9700\\u6C42\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r3.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.statusOptions);\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.getBuildCaseList();\n    this.getListRequirementRequest.CBuildCaseID = -1;\n    this.getListRequirementRequest.CStatus = -1;\n    this.getList();\n  }\n  ngOnInit() {}\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.saveRequirement.CStatus = 1;\n    if (this.currentBuildCase != 0) {\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n    }\n    this.getBuildCaseList();\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: []\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 57,\n      vars: 13,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-5\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"keyWord\", \"name\", \"keyWord\", \"placeholder\", \"\\u95DC\\u9375\\u5B57\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-3\"], [3, \"selectedChange\", \"selected\"], [1, \"col-md-7\"], [1, \"form-group\", \"col-12\", \"col-md-5\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-3\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"row\", \"p-2\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u9700\\u6C42\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"1\", 3, \"checkedChange\"], [\"value\", \"2\", 3, \"checkedChange\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"langg\", \"\", 3, \"value\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"label\", 6);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CBuildCaseID, $event) || (ctx.getListRequirementRequest.CBuildCaseID = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(10, \"nb-option\", 8);\n          i0.ɵɵtext(11, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, RequirementManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"label\", 11);\n          i0.ɵɵtext(15, \"\\u95DC\\u9375\\u5B57\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"label\", 6);\n          i0.ɵɵtext(19, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-select\", 14);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_Template_nb_select_selectedChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(21, \"nb-option\", 8);\n          i0.ɵɵtext(22, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-option\", 8);\n          i0.ɵɵtext(24, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 8);\n          i0.ɵɵtext(26, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 4);\n          i0.ɵɵelement(28, \"div\", 15);\n          i0.ɵɵelementStart(29, \"div\", 16)(30, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_30_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(31, \"i\", 18);\n          i0.ɵɵtext(32, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, RequirementManagementComponent_button_33_Template, 3, 0, \"button\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(34, \"nb-card-body\", 2)(35, \"div\", 3)(36, \"div\", 20)(37, \"table\", 21)(38, \"thead\")(39, \"tr\", 22)(40, \"th\", 23);\n          i0.ɵɵtext(41, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"th\", 23);\n          i0.ɵɵtext(43, \"\\u9700\\u6C42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"th\", 24);\n          i0.ɵɵtext(45, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"th\", 25);\n          i0.ɵɵtext(47, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"th\", 25);\n          i0.ɵɵtext(49, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"th\", 24);\n          i0.ɵɵtext(51, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"tbody\");\n          i0.ɵɵtemplate(53, RequirementManagementComponent_tr_53_Template, 15, 10, \"tr\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"ngx-pagination\", 27);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_54_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_54_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(55, RequirementManagementComponent_ng_template_55_Template, 30, 23, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CBuildCaseID);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "RequirementManagementComponent_button_33_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "RequirementManagementComponent_tr_53_button_13_Template_button_click_0_listener", "_r6", "data_r7", "$implicit", "onEdit", "RequirementManagementComponent_tr_53_button_14_Template_button_click_0_listener", "_r8", "onDelete", "ɵɵtemplate", "RequirementManagementComponent_tr_53_button_13_Template", "RequirementManagementComponent_tr_53_button_14_Template", "ɵɵtextInterpolate", "CRequirement", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "isUpdate", "isDelete", "b_r10", "status_r11", "value", "label", "RequirementManagementComponent_ng_template_55_span_2_Template", "RequirementManagementComponent_ng_template_55_span_3_Template", "ɵɵtwoWayListener", "RequirementManagementComponent_ng_template_55_Template_nb_select_selectedChange_9_listener", "$event", "_r9", "ɵɵtwoWayBindingSet", "saveRequirement", "CBuildCaseID", "RequirementManagementComponent_ng_template_55_nb_option_10_Template", "RequirementManagementComponent_ng_template_55_Template_input_ngModelChange_12_listener", "RequirementManagementComponent_ng_template_55_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_55_Template_nb_checkbox_checkedChange_16_listener", "onHouseTypeChange", "RequirementManagementComponent_ng_template_55_Template_nb_checkbox_checkedChange_18_listener", "RequirementManagementComponent_ng_template_55_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_55_nb_option_22_Template", "RequirementManagementComponent_ng_template_55_Template_button_click_26_listener", "ref_r12", "dialogRef", "save", "RequirementManagementComponent_ng_template_55_Template_button_click_28_listener", "close", "isNew", "ɵɵtwoWayProperty", "buildCaseList", "statusOptions", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "requirementList", "houseType", "getEnumOptions", "currentBuildCase", "getBuildCaseList", "getList", "ngOnInit", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "errorMessages", "length", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "apiRequirementGetListPost$Json", "totalRecords", "TotalItems", "apiRequirementGetDataPost$Json", "checked", "includes", "filter", "v", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_Template_nb_select_ngModelChange_9_listener", "_r1", "RequirementManagementComponent_nb_option_12_Template", "RequirementManagementComponent_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_Template_nb_select_selected<PERSON>hange_20_listener", "RequirementManagementComponent_Template_button_click_30_listener", "RequirementManagementComponent_button_33_Template", "RequirementManagementComponent_tr_53_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_54_listener", "RequirementManagementComponent_ng_template_55_Template", "ɵɵtemplateRefExtractor", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.getBuildCaseList();\r\n    this.getListRequirementRequest.CBuildCaseID = -1;\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getList();\r\n  }\r\n\r\n  // request\r\n  getListRequirementRequest: GetListRequirementRequest = {};\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n\r\n  override ngOnInit(): void { }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [] };\r\n    this.saveRequirement.CStatus = 1;\r\n    if (this.currentBuildCase != 0) {\r\n      this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n    }\r\n    this.getBuildCaseList();\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n      this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n    }\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [] };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-5\">\r\n          <label for=\"status\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"keyWord\" class=\"label mr-2\">關鍵字</label>\r\n          <input type=\"text\" nbInput id=\"keyWord\" name=\"keyWord\" placeholder=\"關鍵字\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(selected)]=\"getListRequirementRequest.CStatus\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-7\"></div>\r\n        <div class=\"form-group col-12 col-md-5 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-3\">建案名稱</th>\r\n              <th scope=\"col\" class=\"col-3\">需求</th>\r\n              <th scope=\"col\" class=\"col-2\">類型</th>\r\n              <th scope=\"col\" class=\"col-1\">排序</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-3\">{{ data.CBuildCaseName }}</td>\r\n              <td class=\"col-3\">{{ data.CRequirement }}</td>\r\n              <td class=\"col-2\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n              <td class=\"col-1\">{{ data.CSort }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-2\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增</span>\r\n      <span *ngIf=\"isNew===false\">編輯</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row  p-2\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'需求'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"需求\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-checkbox value=\"1\" (checkedChange)=\"onHouseTypeChange(1, $event)\">地主戶</nb-checkbox>\r\n              <nb-checkbox value=\"2\" (checkedChange)=\"onHouseTypeChange(2, $event)\">銷售戶</nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/H,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;ICPrDC,EAAA,CAAAC,cAAA,mBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;;IAqBFT,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAU,UAAA,mBAAAC,0EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBhB,EAAA,CAAAoB,SAAA,YAC3C;IAAApB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA4BtCH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAW,gFAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAe,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAW,MAAA,CAAAF,OAAA,EAAAP,SAAA,CAAmB;IAAA,EAAC;IAAChB,EAAA,CAAAoB,SAAA,YAAgC;IAAApB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAU,UAAA,mBAAAgB,gFAAA;MAAA1B,EAAA,CAAAY,aAAA,CAAAe,GAAA;MAAA,MAAAJ,OAAA,GAAAvB,EAAA,CAAAe,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAc,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAACvB,EAAA,CAAAoB,SAAA,YAAqC;IAAApB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAT7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAA6B,UAAA,KAAAC,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/B/B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAXeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAgC,iBAAA,CAAAT,OAAA,CAAAd,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAgC,iBAAA,CAAAT,OAAA,CAAAU,YAAA,CAAuB;IACvBjC,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAgC,iBAAA,CAAAlB,MAAA,CAAAoB,YAAA,CAAAX,OAAA,CAAAY,UAAA,IAAAnC,EAAA,CAAAoC,eAAA,IAAAC,GAAA,GAAyC;IACzCrC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAgC,iBAAA,CAAAT,OAAA,CAAAe,KAAA,CAAgB;IAChBtC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAgC,iBAAA,CAAAhC,EAAA,CAAAuC,WAAA,QAAAhB,OAAA,CAAAiB,OAAA,EAAkC;IAEzCxC,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA2B,QAAA,CAAc;IAEdzC,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAA4B,QAAA,CAAc;;;;;IAkBjC1C,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS3BH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAAuC,KAAA,CAAArC,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAAmC,KAAA,CAAAlC,cAAA,KAAoB;;;;;IAkBtFT,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAwC,UAAA,CAAAC,KAAA,CAAsB;IAC1E7C,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAoC,UAAA,CAAAE,KAAA,KAAgB;;;;;;IA9B9B9C,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAA6B,UAAA,IAAAkB,6DAAA,mBAA2B,IAAAC,6DAAA,mBACC;IAC9BhD,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,cAChB,cACU,aACX,yBACkE,oBAEjC;IAA5CD,EAAA,CAAAiD,gBAAA,4BAAAC,2FAAAC,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqD,kBAAA,CAAAvC,MAAA,CAAAwC,eAAA,CAAAC,YAAA,EAAAJ,MAAA,MAAArC,MAAA,CAAAwC,eAAA,CAAAC,YAAA,GAAAJ,MAAA;MAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;IAAA,EAA2C;IAC3CnD,EAAA,CAAA6B,UAAA,KAAA2B,mEAAA,wBAAiE;IAErExD,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA+E,iBAEhC;IAA3CD,EAAA,CAAAiD,gBAAA,2BAAAQ,uFAAAN,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqD,kBAAA,CAAAvC,MAAA,CAAAwC,eAAA,CAAArB,YAAA,EAAAkB,MAAA,MAAArC,MAAA,CAAAwC,eAAA,CAAArB,YAAA,GAAAkB,MAAA;MAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;IAAA,EAA0C;IAC9CnD,EAFE,CAAAG,YAAA,EAC6C,EAC9B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAiD,gBAAA,2BAAAS,uFAAAP,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqD,kBAAA,CAAAvC,MAAA,CAAAwC,eAAA,CAAAhB,KAAA,EAAAa,MAAA,MAAArC,MAAA,CAAAwC,eAAA,CAAAhB,KAAA,GAAAa,MAAA;MAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;IAAA,EAAmC;IACvCnD,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,uBACL;IAA/CD,EAAA,CAAAU,UAAA,2BAAAiD,6FAAAR,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAiBJ,MAAA,CAAA8C,iBAAA,CAAkB,CAAC,EAAAT,MAAA,CAAS;IAAA,EAAC;IAACnD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACvFH,EAAA,CAAAC,cAAA,uBAAsE;IAA/CD,EAAA,CAAAU,UAAA,2BAAAmD,6FAAAV,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAiBJ,MAAA,CAAA8C,iBAAA,CAAkB,CAAC,EAAAT,MAAA,CAAS;IAAA,EAAC;IAACnD,EAAA,CAAAE,MAAA,0BAAG;IAC3EF,EAD2E,CAAAG,YAAA,EAAc,EACxE;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAiD,gBAAA,4BAAAa,4FAAAX,MAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAAqD,kBAAA,CAAAvC,MAAA,CAAAwC,eAAA,CAAAd,OAAA,EAAAW,MAAA,MAAArC,MAAA,CAAAwC,eAAA,CAAAd,OAAA,GAAAW,MAAA;MAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;IAAA,EAAsC;IACtCnD,EAAA,CAAA6B,UAAA,KAAAkC,mEAAA,wBAA6E;IAOzF/D,EALU,CAAAG,YAAA,EAAY,EACG,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAU,UAAA,mBAAAsD,gFAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAY,aAAA,CAAAwC,GAAA,EAAAc,SAAA;MAAA,MAAApD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAqD,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAACjE,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0D,gFAAA;MAAA,MAAAH,OAAA,GAAAjE,EAAA,CAAAY,aAAA,CAAAwC,GAAA,EAAAc,SAAA;MAAA,OAAAlE,EAAA,CAAAkB,WAAA,CAAS+C,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAACrE,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA5CCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAwD,KAAA,UAAkB;IAClBtE,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAwD,KAAA,WAAmB;IAMJtE,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAuE,gBAAA,aAAAzD,MAAA,CAAAwC,eAAA,CAAAC,YAAA,CAA2C;IACZvD,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA0D,aAAA,CAAgB;IAGnCxE,EAAA,CAAAO,SAAA,EAAc;IAA6BP,EAA3C,CAAAI,UAAA,yBAAc,4BAA4B,oBAAoB;IAE1EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuE,gBAAA,YAAAzD,MAAA,CAAAwC,eAAA,CAAArB,YAAA,CAA0C;IAE9BjC,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuE,gBAAA,YAAAzD,MAAA,CAAAwC,eAAA,CAAAhB,KAAA,CAAmC;IAEvBtC,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAI5DJ,EAAA,CAAAO,SAAA,GAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuE,gBAAA,aAAAzD,MAAA,CAAAwC,eAAA,CAAAd,OAAA,CAAsC;IACFxC,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAA2D,aAAA,CAAgB;;;ADvEpE,OAAM,MAAOC,8BAA+B,SAAQpF,aAAa;EAC/DqF,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IASpB;IACA,KAAAC,yBAAyB,GAA8B,EAAE;IACzD,KAAAC,qBAAqB,GAA8B,EAAE;IAErD;IACA,KAAAf,aAAa,GAA8B,EAAE;IAC7C,KAAAgB,eAAe,GAAqB,EAAE;IACtC,KAAAlC,eAAe,GAAwB;MAAEnB,UAAU,EAAE;IAAE,CAAE;IAEzD,KAAAsC,aAAa,GAAG,CACd;MAAE5B,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAA2C,SAAS,GAAG,IAAI,CAACZ,UAAU,CAACa,cAAc,CAAC3F,aAAa,CAAC;IACzD,KAAAuE,KAAK,GAAG,KAAK;IACb,KAAAqB,gBAAgB,GAAG,CAAC;IArBlB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACN,yBAAyB,CAAC/B,YAAY,GAAG,CAAC,CAAC;IAChD,IAAI,CAAC+B,yBAAyB,CAAC9C,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACqD,OAAO,EAAE;EAChB;EAmBSC,QAAQA,CAAA,GAAW;EAE5B5D,YAAYA,CAAC6D,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACZ,SAAS,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,KAAK,IAAIuD,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACvD,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOoD,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAC1B,KAAK,CAAC2B,KAAK,EAAE;IAClB,IAAI,CAAC3B,KAAK,CAAC4B,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtD,eAAe,CAACC,YAAY,CAAC;IAChE,IAAI,CAACyB,KAAK,CAAC4B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtD,eAAe,CAACrB,YAAY,CAAC;IAC9D,IAAI,CAAC+C,KAAK,CAAC4B,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACtD,eAAe,CAACnB,UAAU,CAAC;IAC7D,IAAI,CAAC6C,KAAK,CAAC4B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtD,eAAe,CAAChB,KAAK,CAAC;IACvD,IAAI,CAAC0C,KAAK,CAAC4B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtD,eAAe,CAACd,OAAO,CAAC;EAC3D;EAEArB,GAAGA,CAAC0F,MAAwB;IAC1B,IAAI,CAACvC,KAAK,GAAG,IAAI;IACjB,IAAI,CAAChB,eAAe,GAAG;MAAEnB,UAAU,EAAE;IAAE,CAAE;IACzC,IAAI,CAACmB,eAAe,CAACd,OAAO,GAAG,CAAC;IAChC,IAAI,IAAI,CAACmD,gBAAgB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACrC,eAAe,CAACC,YAAY,GAAG,IAAI,CAACoC,gBAAgB;IAC3D;IACA,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACd,aAAa,CAACgC,IAAI,CAACD,MAAM,CAAC;EACjC;EAEMpF,MAAMA,CAACsF,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAACzB,qBAAqB,CAAC2B,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAAC1C,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM0C,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAClC,aAAa,CAACgC,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAjD,IAAIA,CAACoD,GAAQ;IACX,IAAI,CAACb,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1B,KAAK,CAACwC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC1C,OAAO,CAAC2C,aAAa,CAAC,IAAI,CAAC1C,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACtC,kBAAkB,CAACyC,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACtE;KACZ,CAAC,CAACuE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChD,OAAO,CAACiD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAACd,OAAO,CAACkD,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFX,GAAG,CAAClD,KAAK,EAAE;EACb;EAEAzC,QAAQA,CAACmF,IAAoB;IAC3B,IAAI,CAACzD,eAAe,CAAC4D,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAAC5C,KAAK,GAAG,KAAK;IAClB,IAAI6D,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAACnD,kBAAkB,CAACoD,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJV,cAAc,EAAE,IAAI,CAAC5D,eAAe,CAAC4D;;KAExC,CAAC,CAACW,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAAC/C,OAAO,CAACiD,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACnC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACX,gBAAgB,CAACsD,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACjJ,kBAAkB,CAAC,IAAI,CAAC8F,UAAU,CAAC,CAAC,CAACwC,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACtD,aAAa,GAAGsD,GAAG,CAACW,OAAQ;IACnC,CAAC,CAAC;EACN;EAEA5C,OAAOA,CAAA;IACL,IAAI,CAACP,yBAAyB,CAACoD,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAACrD,yBAAyB,CAACsD,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,IAAI,CAACvD,yBAAyB,CAAC/B,YAAY,IAAI,IAAI,CAAC+B,yBAAyB,CAAC/B,YAAY,IAAI,CAAC,EAAE;MACnG,IAAI,CAACoC,gBAAgB,GAAG,IAAI,CAACL,yBAAyB,CAAC/B,YAAY;IACrE;IACA,IAAI,CAAC2B,kBAAkB,CAAC4D,8BAA8B,CAAC;MAAElB,IAAI,EAAE,IAAI,CAACtC;IAAyB,CAAE,CAAC,CAC7FkD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACjD,eAAe,GAAGsC,GAAG,CAACW,OAAO;UAClC,IAAI,CAACM,YAAY,GAAGjB,GAAG,CAACkB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAEA7B,OAAOA,CAAA;IACL,IAAI,CAACjC,kBAAkB,CAAC+D,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAACrC;IAAqB,CAAE,CAAC,CACzFiD,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACnF,eAAe,GAAG;YAAEnB,UAAU,EAAE;UAAE,CAAE;UACzC,IAAI,CAACmB,eAAe,CAACC,YAAY,GAAGuE,GAAG,CAACW,OAAO,CAAClF,YAAY;UAC5D,IAAI,CAACD,eAAe,CAACnB,UAAU,GAAG2F,GAAG,CAACW,OAAO,CAACtG,UAAU,GAAI6D,KAAK,CAACC,OAAO,CAAC6B,GAAG,CAACW,OAAO,CAACtG,UAAU,CAAC,GAAG2F,GAAG,CAACW,OAAO,CAACtG,UAAU,GAAG,CAAC2F,GAAG,CAACW,OAAO,CAACtG,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACmB,eAAe,CAACrB,YAAY,GAAG6F,GAAG,CAACW,OAAO,CAACxG,YAAY;UAC5D,IAAI,CAACqB,eAAe,CAAC4D,cAAc,GAAGY,GAAG,CAACW,OAAO,CAACvB,cAAc;UAChE,IAAI,CAAC5D,eAAe,CAAChB,KAAK,GAAGwF,GAAG,CAACW,OAAO,CAACnG,KAAK;UAC9C,IAAI,CAACgB,eAAe,CAACd,OAAO,GAAGsF,GAAG,CAACW,OAAO,CAACjG,OAAO;QACpD;MACF;IACF,CAAC,CAAC;EACN;EAEAoB,iBAAiBA,CAACf,KAAa,EAAEqG,OAAY;IAC3C7B,OAAO,CAACC,GAAG,CAAC4B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAC5F,eAAe,CAACnB,UAAU,EAAEgH,QAAQ,CAACtG,KAAK,CAAC,EAAE;QACrD,IAAI,CAACS,eAAe,CAACnB,UAAU,EAAEqE,IAAI,CAAC3D,KAAK,CAAC;MAC9C;MACAwE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChE,eAAe,CAACnB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACmB,eAAe,CAACnB,UAAU,GAAG,IAAI,CAACmB,eAAe,CAACnB,UAAU,EAAEiH,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKxG,KAAK,CAAC;IAC7F;EACF;;;uCAxLW6B,8BAA8B,EAAA1E,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9J,EAAA,CAAAsJ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhK,EAAA,CAAAsJ,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAlK,EAAA,CAAAsJ,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAAnK,EAAA,CAAAsJ,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAArK,EAAA,CAAAsJ,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAvK,EAAA,CAAAsJ,iBAAA,CAAAtJ,EAAA,CAAAwK,UAAA;IAAA;EAAA;;;YAA9B9F,8BAA8B;MAAA+F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3K,EAAA,CAAA4K,0BAAA,EAAA5K,EAAA,CAAA6K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCvCzCnL,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACC;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,mBAA8E;UAAnED,EAAA,CAAAiD,gBAAA,2BAAAoI,2EAAAlI,MAAA;YAAAnD,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAAtL,EAAA,CAAAqD,kBAAA,CAAA+H,GAAA,CAAA9F,yBAAA,CAAA/B,YAAA,EAAAJ,MAAA,MAAAiI,GAAA,CAAA9F,yBAAA,CAAA/B,YAAA,GAAAJ,MAAA;YAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;UAAA,EAAoD;UAC7DnD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAA6B,UAAA,KAAA0J,oDAAA,uBAAiE;UAIrEvL,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,eAAwC,iBACE;UAAAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAiD,gBAAA,2BAAAuI,wEAAArI,MAAA;YAAAnD,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAAtL,EAAA,CAAAqD,kBAAA,CAAA+H,GAAA,CAAA9F,yBAAA,CAAArD,YAAA,EAAAkB,MAAA,MAAAiI,GAAA,CAAA9F,yBAAA,CAAArD,YAAA,GAAAkB,MAAA;YAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;UAAA,EAAoD;UACxDnD,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,eAAwC,gBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,qBAA4D;UAAjDD,EAAA,CAAAiD,gBAAA,4BAAAwI,6EAAAtI,MAAA;YAAAnD,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAAtL,EAAA,CAAAqD,kBAAA,CAAA+H,GAAA,CAAA9F,yBAAA,CAAA9C,OAAA,EAAAW,MAAA,MAAAiI,GAAA,CAAA9F,yBAAA,CAAA9C,OAAA,GAAAW,MAAA;YAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;UAAA,EAAgD;UACzDnD,EAAA,CAAAC,cAAA,oBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,oBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAG/BF,EAH+B,CAAAG,YAAA,EAAY,EAC3B,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAoB,SAAA,eAA4B;UAE1BpB,EADF,CAAAC,cAAA,eAAmD,kBACK;UAApBD,EAAA,CAAAU,UAAA,mBAAAgL,iEAAA;YAAA1L,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAA,OAAAtL,EAAA,CAAAkB,WAAA,CAASkK,GAAA,CAAAvF,OAAA,EAAS;UAAA,EAAC;UAAC7F,EAAA,CAAAoB,SAAA,aAAkC;UAAApB,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAA6B,UAAA,KAAA8J,iDAAA,qBAA4E;UAKpF3L,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAQHH,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6B,UAAA,KAAA+J,6CAAA,mBAAuE;UAe7E5L,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAiD,gBAAA,wBAAA4I,8EAAA1I,MAAA;YAAAnD,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAAtL,EAAA,CAAAqD,kBAAA,CAAA+H,GAAA,CAAAvC,SAAA,EAAA1F,MAAA,MAAAiI,GAAA,CAAAvC,SAAA,GAAA1F,MAAA;YAAA,OAAAnD,EAAA,CAAAkB,WAAA,CAAAiC,MAAA;UAAA,EAAoB;UAClEnD,EAAA,CAAAU,UAAA,wBAAAmL,8EAAA;YAAA7L,EAAA,CAAAY,aAAA,CAAA0K,GAAA;YAAA,OAAAtL,EAAA,CAAAkB,WAAA,CAAckK,GAAA,CAAAvF,OAAA,EAAS;UAAA,EAAC;UAIhC7F,EAHM,CAAAG,YAAA,EAAiB,EACb,EACO,EACP;UAGVH,EAAA,CAAA6B,UAAA,KAAAiK,sDAAA,kCAAA9L,EAAA,CAAA+L,sBAAA,CAAkD;;;UAvE7B/L,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAuE,gBAAA,YAAA6G,GAAA,CAAA9F,yBAAA,CAAA/B,YAAA,CAAoD;UAClDvD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACKJ,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAA5G,aAAA,CAAgB;UAQ5CxE,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAuE,gBAAA,YAAA6G,GAAA,CAAA9F,yBAAA,CAAArD,YAAA,CAAoD;UAI3CjC,EAAA,CAAAO,SAAA,GAAgD;UAAhDP,EAAA,CAAAuE,gBAAA,aAAA6G,GAAA,CAAA9F,yBAAA,CAAA9C,OAAA,CAAgD;UAC9CxC,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAQoCJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAgL,GAAA,CAAAY,QAAA,CAAc;UAsBnDhM,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAA5F,eAAA,CAAoB;UAgB/BxF,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAAgL,GAAA,CAAArC,YAAA,CAA+B;UAAC/I,EAAA,CAAAuE,gBAAA,SAAA6G,GAAA,CAAAvC,SAAA,CAAoB;UAAC7I,EAAA,CAAAI,UAAA,aAAAgL,GAAA,CAAAzC,QAAA,CAAqB;;;qBDhD5F1J,YAAY,EAAA0K,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,mBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EACZ5M,mBAAmB,EACnBL,aAAa,EAAAwK,EAAA,CAAA0C,gBAAA,EACb5M,WAAW,EAAA6M,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EACXrN,cAAc,EAAAsK,EAAA,CAAAgD,iBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EACdxN,cAAc,EACdO,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVX,gBAAgB,EAAAyK,EAAA,CAAAkD,mBAAA,EAChB/M,kBAAkB;MAAAgN,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}