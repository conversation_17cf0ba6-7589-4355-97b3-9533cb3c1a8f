{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"templates\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"templateDetails\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"sharedData\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"addTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"saveTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"deleteTemplate\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss']\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "sharedData", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "showCreateTemplate", "newTemplateName", "newTemplateDesc", "selectedTemplate", "createTemplate", "selected", "filter", "x", "length", "alert", "template", "TemplateName", "Description", "details", "map", "TemplateID", "RefID", "ID", "CRequirementID", "ModuleType", "FieldName", "FieldValue", "CRequirement", "emit", "for<PERSON>ach", "onSelectTemplate", "onDeleteTemplate", "templateID", "confirm", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss']\r\n})\r\nexport class TemplateViewerComponent {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{template: Template, details: TemplateDetail[]}>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({template, details});\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AAO/D,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAA7BC,YAAA;IACI,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,UAAU,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAG,IAAIR,YAAY,EAAY;IAC1C,KAAAS,cAAc,GAAG,IAAIT,YAAY,EAAY;IAC7C,KAAAU,YAAY,GAAG,IAAIV,YAAY,EAAmD;IAClF,KAAAW,cAAc,GAAG,IAAIX,YAAY,EAAU;IAErD,KAAAY,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAoB,IAAI;EAwC1C;EAtCE;EACAC,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACV,UAAU,IAAI,EAAE,EAAEW,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACJ,eAAe,IAAII,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMC,QAAQ,GAAa;MACzBC,YAAY,EAAE,IAAI,CAACV,eAAe;MAClCW,WAAW,EAAE,IAAI,CAACV;KACnB;IACD;IACA,MAAMW,OAAO,GAAqBR,QAAQ,CAACS,GAAG,CAACP,CAAC,KAAK;MACnDQ,UAAU,EAAE,CAAC;MAAE;MACfC,KAAK,EAAET,CAAC,CAACU,EAAE,IAAIV,CAAC,CAACW,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAEZ,CAAC,CAACY,UAAU,IAAI,aAAa;MACzCC,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAEd,CAAC,CAACe;KACf,CAAC,CAAC;IACH,IAAI,CAACxB,YAAY,CAACyB,IAAI,CAAC;MAACb,QAAQ;MAAEG;IAAO,CAAC,CAAC;IAC3C,IAAI,CAACb,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACP,UAAU,IAAI,EAAE,EAAE6B,OAAO,CAACjB,CAAC,IAAIA,CAAC,CAACF,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACAoB,gBAAgBA,CAACf,QAAkB;IACjC,IAAI,CAACP,gBAAgB,GAAGO,QAAQ;IAChC,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACb,QAAQ,CAAC;EACpC;EAEA;EACAgB,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC7B,cAAc,CAACwB,IAAI,CAACI,UAAU,CAAC;IACtC;EACF;CACD;AAnDUE,UAAA,EAARxC,KAAK,EAAE,C,yDAA4B;AAC3BwC,UAAA,EAARxC,KAAK,EAAE,C,+DAAwC;AACvCwC,UAAA,EAARxC,KAAK,EAAE,C,0DAAwB;AACtBwC,UAAA,EAATvC,MAAM,EAAE,C,2DAA4C;AAC3CuC,UAAA,EAATvC,MAAM,EAAE,C,8DAA+C;AAC9CuC,UAAA,EAATvC,MAAM,EAAE,C,4DAAoF;AACnFuC,UAAA,EAATvC,MAAM,EAAE,C,8DAA6C;AAP3CC,uBAAuB,GAAAsC,UAAA,EALnC1C,SAAS,CAAC;EACT2C,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWzC,uBAAuB,CAoDnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}