{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ModifyHouseTypeComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r1.label, \" \");\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 34)(1, \"div\", 36)(2, \"nb-checkbox\", 37);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyHouseTypeComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const idx_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r3));\n    });\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\\u70BA\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const idx_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r3));\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33);\n    i0.ɵɵelement(1, \"th\", 34);\n    i0.ɵɵtemplate(2, ModifyHouseTypeComponent_div_37_tr_3_th_2_Template, 5, 1, \"th\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList[0]);\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 37);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const house_r8 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r8.CHouseTypeBool, $event) || (house_r8.CHouseTypeBool = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"span\", 38);\n    i0.ɵɵtext(2, \" \\u662F\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const house_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"checked\", house_r8.CHouseTypeBool);\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_tr_5_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 40)(1, \"div\", 36)(2, \"p\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template, 3, 1, \"nb-checkbox\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const house_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", !house_r8.CIsEnable ? \"bg-slate-400\" : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", house_r8.CHouseHold || \"null\", \" - \", house_r8.CFloor, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", house_r8.CIsEnable);\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 33)(1, \"td\", 34)(2, \"div\", 36)(3, \"p\");\n    i0.ɵɵtext(4, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-checkbox\", 37);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyHouseTypeComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener($event) {\n      const row_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, row_r6));\n    });\n    i0.ɵɵelementStart(6, \"span\", 38);\n    i0.ɵɵtext(7, \"\\u5168\\u9078\\u70BA\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, ModifyHouseTypeComponent_div_37_tr_5_td_8_Template, 5, 4, \"td\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(row_r6));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r6);\n  }\n}\nfunction ModifyHouseTypeComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"table\", 30)(2, \"thead\");\n    i0.ɵɵtemplate(3, ModifyHouseTypeComponent_div_37_tr_3_Template, 3, 1, \"tr\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ModifyHouseTypeComponent_div_37_tr_5_Template, 9, 2, \"tr\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList);\n  }\n}\nexport class ModifyHouseTypeComponent extends BaseComponent {\n  constructor(_allow, dialogService, _houseService, route, location, message, router) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.location = location;\n    this.message = message;\n    this.router = router;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.isHouseList = false;\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n      }\n    });\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array\n      if (floorIndex !== -1) {\n        let custemp = {\n          ...customer\n        };\n        if (customer.CIsEnable) {\n          custemp = {\n            ...custemp,\n            CHouseTypeBool: customer.CHouseType && customer.CHouseType == 1 ? true : false\n          };\n        }\n        groupedData[floorIndex].push(custemp);\n      }\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    this.isHouseList = false;\n    if (this.buildCaseId) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId,\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\n          CFloor: {\n            CFrom: this.searchQuery.CFrom,\n            CTo: this.searchQuery.CTo\n          },\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseList = this.groupByFloor(rest);\n          this.isHouseList = true;\n          console.log('this.houseList', this.houseList);\n        }\n      });\n    }\n  }\n  goBack() {\n    this.location.back();\n  }\n  onSubmit() {\n    let bodyParam = this.houseList.flat().map(item => {\n      return {\n        CHouseType: item.CHouseTypeBool === true ? 1 : 2,\n        CHouseID: item.CID,\n        CIsEnable: item.CIsEnable\n      };\n    });\n    this._houseService.apiHouseEditListHousePost$Json({\n      body: {\n        Args: bodyParam.filter(e => e.CIsEnable)\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseList();\n      }\n    });\n  }\n  isCheckAllRowChecked(row) {\n    let count = 0;\n    for (let i = 0; i < row.length; i++) {\n      const item = row[i];\n      if (!item.CHouseTypeBool && item.CIsEnable) {\n        return false;\n      }\n      if (!item.CIsEnable) {\n        count = count + 1;\n      }\n    }\n    if (count === row.length) {\n      return false; //If all row are disabled, they will not be checked.\n    }\n    return true;\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      let count = 0;\n      for (const floorData of this.houseList) {\n        if (floorData[index].CIsEnable) {\n          if (index >= floorData.length || !floorData[index].CHouseTypeBool) {\n            return false; // Found a customer with CHouseTypeBool not true (or missing)\n          }\n        } else {\n          count = count + 1;\n        }\n        if (count === this.houseList.length) {\n          return false; //If all columns are disabled, they will not be checked.\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        if (floorData[index].CIsEnable) {\n          floorData[index].CHouseTypeBool = checked;\n        }\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      if (item.CIsEnable) {\n        item.CHouseTypeBool = checked;\n      }\n    }\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\n      CFrom: 1,\n      CTo: 100\n    };\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        this.getListBuilding();\n        this.getHouseList();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  clear() {\n    this.searchQuery = {\n      CFrom: 1,\n      CTo: 100,\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\n    };\n  }\n  onOpen(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavigateWithId(type) {\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);\n  }\n  static {\n    this.ɵfac = function ModifyHouseTypeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ModifyHouseTypeComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyHouseTypeComponent,\n      selectors: [[\"ngx-modify-house-type\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 47,\n      vars: 5,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-5\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"inline\"], [1, \"d-flex\", \"justify-content-center\", \"w-full\"], [3, \"value\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-bordered\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\"], [1, \"px-1\"], [\"class\", \" px-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-max\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"font-medium\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [1, \"font-bold\"], [\"status\", \"basic\", 3, \"checked\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function ModifyHouseTypeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 1);\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n          i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseTypeComponent_Template_nb_select_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(11, ModifyHouseTypeComponent_nb_option_11_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"label\", 10);\n          i0.ɵɵtext(15, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-form-field\", 11)(17, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseTypeComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"label\", 13);\n          i0.ɵɵtext(19, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-form-field\", 14)(21, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseTypeComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_24_listener() {\n            return ctx.clear();\n          });\n          i0.ɵɵtext(25, \" \\u6E05\\u9664 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_26_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(27, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(28, \"i\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 21)(30, \"div\", 17)(31, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_31_listener() {\n            return ctx.onNavigateWithId(\"modify-floor-plan\");\n          });\n          i0.ɵɵtext(32, \" 1.\\u8ABF\\u6574\\u6236\\u578B\\u7D44\\u6210 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_33_listener() {\n            return ctx.onNavigateWithId(\"modify-household\");\n          });\n          i0.ɵɵtext(34, \" 2.\\u4FEE\\u6539\\u6236\\u578B\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_35_listener() {\n            return ctx.onNavigateWithId(\"modify-house-type\");\n          });\n          i0.ɵɵtext(36, \" 3.\\u8A2D\\u5B9A\\u5730\\u4E3B\\u6236 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(37, ModifyHouseTypeComponent_div_37_Template, 6, 2, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-card-footer\", 25)(39, \"div\", 26)(40, \"div\", 27)(41, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_41_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(42, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_43_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(44, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseTypeComponent_Template_button_click_45_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.isHouseList);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktaG91c2UtdHlwZS5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWhvdXNlLXR5cGUvbW9kaWZ5LWhvdXNlLXR5cGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLGdMQUFnTCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "building_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "ModifyHouseTypeComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener", "$event", "idx_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "enableAllAtIndex", "isCheckAllColumnChecked", "ɵɵelement", "ɵɵtemplate", "ModifyHouseTypeComponent_div_37_tr_3_th_2_Template", "houseList", "ɵɵtwoWayListener", "ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template_nb_checkbox_checkedChange_0_listener", "_r7", "house_r8", "$implicit", "ɵɵtwoWayBindingSet", "CHouseTypeBool", "ɵɵtwoWayProperty", "ModifyHouseTypeComponent_div_37_tr_5_td_8_nb_checkbox_4_Template", "CIsEnable", "ɵɵtextInterpolate2", "CHouseHold", "CFloor", "ModifyHouseTypeComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener", "row_r6", "_r5", "enableAllRow", "ModifyHouseTypeComponent_div_37_tr_5_td_8_Template", "isCheckAllRowChecked", "ModifyHouseTypeComponent_div_37_tr_3_Template", "ModifyHouseTypeComponent_div_37_tr_5_Template", "length", "ModifyHouseTypeComponent", "constructor", "_allow", "dialogService", "_houseService", "route", "location", "message", "router", "buildingSelectedOptions", "value", "isHouseList", "getListBuilding", "apiHouseGetListBuildingPost$Json", "body", "CBuildCaseID", "buildCaseId", "subscribe", "res", "Entries", "StatusCode", "map", "e", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "filter", "floor", "push", "floorIndex", "indexOf", "custemp", "CHouseType", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildingName", "searchQuery", "CBuildingNameSelected", "CFrom", "CTo", "CIsPagi", "rest", "console", "log", "goBack", "back", "onSubmit", "bodyParam", "flat", "item", "CHouseID", "CID", "apiHouseEditListHousePost$Json", "<PERSON><PERSON><PERSON>", "showSucessMSG", "row", "count", "i", "Error", "floorData", "checked", "ngOnInit", "paramMap", "params", "idParam", "get", "id", "pageChanged", "newPage", "pageIndex", "clear", "onOpen", "ref", "open", "onClose", "close", "onNavigateWithId", "type", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "HouseService", "i4", "ActivatedRoute", "i5", "Location", "i6", "MessageService", "Router", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ModifyHouseTypeComponent_Template", "rf", "ctx", "ModifyHouseTypeComponent_Template_nb_select_ngModelChange_10_listener", "ModifyHouseTypeComponent_nb_option_11_Template", "ModifyHouseTypeComponent_Template_input_ngModelChange_17_listener", "ModifyHouseTypeComponent_Template_input_ngModelChange_21_listener", "ModifyHouseTypeComponent_Template_button_click_24_listener", "ModifyHouseTypeComponent_Template_button_click_26_listener", "ModifyHouseTypeComponent_Template_button_click_31_listener", "ModifyHouseTypeComponent_Template_button_click_33_listener", "ModifyHouseTypeComponent_Template_button_click_35_listener", "ModifyHouseTypeComponent_div_37_Template", "ModifyHouseTypeComponent_Template_button_click_41_listener", "ModifyHouseTypeComponent_Template_button_click_43_listener", "ModifyHouseTypeComponent_Template_button_click_45_listener"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-house-type\\modify-house-type.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-house-type\\modify-house-type.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\nexport interface GetHouseListResCus {\r\n  CBuildingName?: string | null;\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseType?: number | null;\r\n  CHouseTypeBool?: boolean | null;\r\n  CID?: number;\r\n  CIsChange?: boolean;\r\n  CIsEnable?: boolean | null;\r\n  CPayStatus?: number;\r\n  CProgress?: number;\r\n  CProgressName?: string | null;\r\n  CSignStatus?: number | null;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'ngx-modify-house-type',\r\n  templateUrl: './modify-house-type.component.html',\r\n  styleUrls: ['./modify-house-type.component.scss'],\r\n})\r\n\r\nexport class ModifyHouseTypeComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router\r\n\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListResCus[][] {\r\n    const groupedData: GetHouseListResCus[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        let custemp: GetHouseListResCus = { ...customer }\r\n        if (customer.CIsEnable) {\r\n          custemp = { ...custemp, CHouseTypeBool: (customer.CHouseType && customer.CHouseType == 1) ? true : false }\r\n        }\r\n        groupedData[floorIndex].push(custemp);\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseList = this.groupByFloor(rest)\r\n          this.isHouseList = true\r\n          console.log('this.houseList', this.houseList);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n\r\n  goBack() { this.location.back() }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CHouseType: item.CHouseTypeBool === true ? 1 : 2,\r\n        CHouseID: item.CID,\r\n        CIsEnable: item.CIsEnable\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        Args: bodyParam.filter((e: any) => e.CIsEnable)\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    let count = 0\r\n    for (let i = 0; i < row.length; i++) {\r\n        const item = row[i];\r\n        if (!item.CHouseTypeBool && item.CIsEnable) {\r\n            return false;\r\n        }\r\n        if(!item.CIsEnable) {\r\n          count = count + 1\r\n        }\r\n    }\r\n    if(count === row.length ) {\r\n      return false //If all row are disabled, they will not be checked.\r\n    }\r\n    return true;\r\n}\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      let count = 0\r\n\r\n      for (const floorData of this.houseList) {\r\n        if (floorData[index].CIsEnable) {\r\n\r\n          if (index >= floorData.length || !floorData[index].CHouseTypeBool) {\r\n            return false; // Found a customer with CHouseTypeBool not true (or missing)\r\n          }\r\n        } else {\r\n          count = count + 1\r\n        }\r\n        if(count === this.houseList.length ) {\r\n          return false //If all columns are disabled, they will not be checked.\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        if (floorData[index].CIsEnable) {\r\n          floorData[index].CHouseTypeBool = checked;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      if(item.CIsEnable) {\r\n        item.CHouseTypeBool = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\" class=\"text-center\">\r\n            <th class=\"px-1\"></th>\r\n            <th *ngFor=\"let house of houseList[0]; let idx = index;\" class=\" px-1\">\r\n              <div class=\"w-max\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span class=\"font-medium\">全選為地主戶 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\" class=\"text-center\">\r\n            <td class=\"px-1\">\r\n              <div class=\"w-max\">\r\n                <p>&nbsp;</p>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\">\r\n                  <span class=\"font-medium\">全選為地主戶 </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n            <td *ngFor=\"let house of row\" [ngClass]=\"!house.CIsEnable ? 'bg-slate-400' : ''\">\r\n              <div class=\"w-max\">\r\n                <p class=\"font-bold\">{{ house.CHouseHold || 'null' }} - {{ house.CFloor }}</p>\r\n                <nb-checkbox *ngIf=\"house.CIsEnable\" status=\"basic\" [(checked)]=\"house.CHouseTypeBool\">\r\n                  <span class=\"font-medium\">\r\n                    是地主戶\r\n                  </span>\r\n                </nb-checkbox>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAIA,SAASA,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;ICOvDC,EAAA,CAAAC,cAAA,oBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAC5EL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,WAAA,CAAAG,KAAA,MACF;;;;;;IAkDIR,EAFJ,CAAAC,cAAA,aAAuE,cAClD,sBAE0B;IADfD,EAAA,CAAAS,UAAA,2BAAAC,wFAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAG,gBAAA,CAAAR,MAAA,EAAAC,MAAA,CAA6B;IAAA,EAAC;IAEzEZ,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EAC5B,EACV,EACH;;;;;IAJCH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAI,uBAAA,CAAAR,MAAA,EAAwC;;;;;IALhDZ,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAqB,SAAA,aAAsB;IACtBrB,EAAA,CAAAsB,UAAA,IAAAC,kDAAA,iBAAuE;IAQzEvB,EAAA,CAAAG,YAAA,EAAK;;;;IARmBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAQ,SAAA,IAAiB;;;;;;IAwBnCxB,EAAA,CAAAC,cAAA,sBAAuF;IAAnCD,EAAA,CAAAyB,gBAAA,2BAAAC,sGAAAf,MAAA;MAAAX,EAAA,CAAAa,aAAA,CAAAc,GAAA;MAAA,MAAAC,QAAA,GAAA5B,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA7B,EAAA,CAAA8B,kBAAA,CAAAF,QAAA,CAAAG,cAAA,EAAApB,MAAA,MAAAiB,QAAA,CAAAG,cAAA,GAAApB,MAAA;MAAA,OAAAX,EAAA,CAAAkB,WAAA,CAAAP,MAAA;IAAA,EAAkC;IACpFX,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACK;;;;IAJsCH,EAAA,CAAAgC,gBAAA,YAAAJ,QAAA,CAAAG,cAAA,CAAkC;;;;;IADtF/B,EAFJ,CAAAC,cAAA,aAAiF,cAC5D,YACI;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9EH,EAAA,CAAAsB,UAAA,IAAAW,gEAAA,0BAAuF;IAM3FjC,EADE,CAAAG,YAAA,EAAM,EACH;;;;IATyBH,EAAA,CAAAI,UAAA,aAAAwB,QAAA,CAAAM,SAAA,uBAAkD;IAEvDlC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAmC,kBAAA,KAAAP,QAAA,CAAAQ,UAAA,mBAAAR,QAAA,CAAAS,MAAA,KAAqD;IAC5DrC,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,SAAAwB,QAAA,CAAAM,SAAA,CAAqB;;;;;;IAVnClC,EAHN,CAAAC,cAAA,aAAsD,aACnC,cACI,QACd;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACbH,EAAA,CAAAC,cAAA,sBAC6C;IAA3CD,EAAA,CAAAS,UAAA,2BAAA6B,mFAAA3B,MAAA;MAAA,MAAA4B,MAAA,GAAAvC,EAAA,CAAAa,aAAA,CAAA2B,GAAA,EAAAX,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAyB,YAAA,CAAA9B,MAAA,EAAA4B,MAAA,CAAwB;IAAA,EAAC;IAC1CvC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EAC5B,EACV,EACH;IACLH,EAAA,CAAAsB,UAAA,IAAAoB,kDAAA,iBAAiF;IAUnF1C,EAAA,CAAAG,YAAA,EAAK;;;;;IAhB6BH,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA2B,oBAAA,CAAAJ,MAAA,EAAqC;IAM/CvC,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAAmC,MAAA,CAAM;;;;;IAxBhCvC,EAFJ,CAAAC,cAAA,cAAuD,gBACoC,YAChF;IACLD,EAAA,CAAAsB,UAAA,IAAAsB,6CAAA,iBAAiD;IAWnD5C,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAAsB,UAAA,IAAAuB,6CAAA,iBAAsD;IAuB5D7C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IApCKH,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAQ,SAAA,CAAAsB,MAAA,CAAsB;IAaP9C,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAQ,SAAA,CAAY;;;ADjC1C,OAAM,MAAOuB,wBAAyB,SAAQhD,aAAa;EACzDiD,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,aAA2B,EAC3BC,KAAqB,EACrBC,QAAkB,EAClBC,OAAuB,EACvBC,MAAc;IAGtB,KAAK,CAACN,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IAOhB,KAAAC,uBAAuB,GAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEjD,KAAK,EAAE;IAAI,CAAE,CAAC;IAyC7D,KAAAkD,WAAW,GAAG,KAAK;EA5CnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACR,aAAa,CAACS,gCAAgC,CAAC;MAClDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACX,uBAAuB,GAAG,CAAC;UAC9BC,KAAK,EAAE,EAAE;UAAEjD,KAAK,EAAE;SACnB,EAAE,GAAGyD,GAAG,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEZ,KAAK,EAAEY,CAAC;YAAE7D,KAAK,EAAE6D;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAIAC,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAA2B,EAAE;IAC9C,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACH,GAAG,CAACS,QAAQ,IAAIA,QAAQ,CAACxC,MAAM,CAAC,CAACyC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACQ,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMH,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGR,YAAY,CAACS,OAAO,CAACL,QAAQ,CAACxC,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAI4C,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB,IAAIE,OAAO,GAAuB;UAAE,GAAGN;QAAQ,CAAE;QACjD,IAAIA,QAAQ,CAAC3C,SAAS,EAAE;UACtBiD,OAAO,GAAG;YAAE,GAAGA,OAAO;YAAEpD,cAAc,EAAG8C,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACO,UAAU,IAAI,CAAC,GAAI,IAAI,GAAG;UAAK,CAAE;QAC5G;QACAZ,WAAW,CAACS,UAAU,CAAC,CAACD,IAAI,CAACG,OAAO,CAAC;MACvC;IACF;IACA,OAAOX,WAAW;EACpB;EAIAa,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACpD,MAAM,IAAI,CAAC,KAAKmD,CAAC,CAACnD,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAqD,YAAYA,CAAA;IACV,IAAI,CAAChC,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACK,WAAW,EAAE;MACpB,IAAI,CAACZ,aAAa,CAACwC,6BAA6B,CAAC;QAC/C9B,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI,CAACC,WAAW;UAC9B6B,aAAa,EAAE,IAAI,CAACC,WAAW,CAACC,qBAAqB,CAACrC,KAAK,IAAI,IAAI;UACnEpB,MAAM,EAAE;YACN0D,KAAK,EAAE,IAAI,CAACF,WAAW,CAACE,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAACH,WAAW,CAACG;WACvB;UACDC,OAAO,EAAE;;OAEZ,CAAC,CAACjC,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM+B,IAAI,GAAG,IAAI,CAACb,qBAAqB,CAACpB,GAAG,CAACC,OAAO,CAAC;UACpD,IAAI,CAAC1C,SAAS,GAAG,IAAI,CAAC8C,YAAY,CAAC4B,IAAI,CAAC;UACxC,IAAI,CAACxC,WAAW,GAAG,IAAI;UACvByC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC5E,SAAS,CAAC;QAC/C;MACF,CAAC,CAAC;IACJ;EACF;EAGA6E,MAAMA,CAAA;IAAK,IAAI,CAAChD,QAAQ,CAACiD,IAAI,EAAE;EAAC;EAEhCC,QAAQA,CAAA;IACN,IAAIC,SAAS,GAAG,IAAI,CAAChF,SAAS,CAACiF,IAAI,EAAE,CAACrC,GAAG,CAAEsC,IAAS,IAAI;MACtD,OAAO;QACLtB,UAAU,EAAEsB,IAAI,CAAC3E,cAAc,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;QAChD4E,QAAQ,EAAED,IAAI,CAACE,GAAG;QAClB1E,SAAS,EAAEwE,IAAI,CAACxE;OACjB;IACH,CAAC,CAAC;IAEF,IAAI,CAACiB,aAAa,CAAC0D,8BAA8B,CAAC;MAChDhD,IAAI,EAAE;QACJiD,IAAI,EAAEN,SAAS,CAAC1B,MAAM,CAAET,CAAM,IAAKA,CAAC,CAACnC,SAAS;;KAEjD,CAAC,CAAC8B,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACb,OAAO,CAACyD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACrB,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEA/C,oBAAoBA,CAACqE,GAAU;IAC7B,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAAClE,MAAM,EAAEoE,CAAC,EAAE,EAAE;MACjC,MAAMR,IAAI,GAAGM,GAAG,CAACE,CAAC,CAAC;MACnB,IAAI,CAACR,IAAI,CAAC3E,cAAc,IAAI2E,IAAI,CAACxE,SAAS,EAAE;QACxC,OAAO,KAAK;MAChB;MACA,IAAG,CAACwE,IAAI,CAACxE,SAAS,EAAE;QAClB+E,KAAK,GAAGA,KAAK,GAAG,CAAC;MACnB;IACJ;IACA,IAAGA,KAAK,KAAKD,GAAG,CAAClE,MAAM,EAAG;MACxB,OAAO,KAAK,EAAC;IACf;IACA,OAAO,IAAI;EACf;EAEE1B,uBAAuBA,CAACL,KAAa;IACnC,IAAI,IAAI,CAAC2C,WAAW,EAAE;MACpB,IAAI3C,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACS,SAAS,CAAC,CAAC,CAAC,CAACsB,MAAM,EAAE;QAClD,MAAM,IAAIqE,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,IAAIF,KAAK,GAAG,CAAC;MAEb,KAAK,MAAMG,SAAS,IAAI,IAAI,CAAC5F,SAAS,EAAE;QACtC,IAAI4F,SAAS,CAACrG,KAAK,CAAC,CAACmB,SAAS,EAAE;UAE9B,IAAInB,KAAK,IAAIqG,SAAS,CAACtE,MAAM,IAAI,CAACsE,SAAS,CAACrG,KAAK,CAAC,CAACgB,cAAc,EAAE;YACjE,OAAO,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,MAAM;UACLkF,KAAK,GAAGA,KAAK,GAAG,CAAC;QACnB;QACA,IAAGA,KAAK,KAAK,IAAI,CAACzF,SAAS,CAACsB,MAAM,EAAG;UACnC,OAAO,KAAK,EAAC;QACf;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEA3B,gBAAgBA,CAACkG,OAAgB,EAAEtG,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIoG,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC5F,SAAS,EAAE;MACtC,IAAIT,KAAK,GAAGqG,SAAS,CAACtE,MAAM,EAAE;QAAE;QAC9B,IAAIsE,SAAS,CAACrG,KAAK,CAAC,CAACmB,SAAS,EAAE;UAC9BkF,SAAS,CAACrG,KAAK,CAAC,CAACgB,cAAc,GAAGsF,OAAO;QAC3C;MACF;IACF;EACF;EAEA5E,YAAYA,CAAC4E,OAAgB,EAAEL,GAAU;IACvC,KAAK,MAAMN,IAAI,IAAIM,GAAG,EAAE;MACtB,IAAGN,IAAI,CAACxE,SAAS,EAAE;QACjBwE,IAAI,CAAC3E,cAAc,GAAGsF,OAAO;MAC/B;IACF;EACF;EAKSC,QAAQA,CAAA;IACf,IAAI,CAACzB,WAAW,GAAG;MACjBC,qBAAqB,EAAE,IAAI,CAACtC,uBAAuB,CAAC,CAAC,CAAC;MACtDuC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAAC5C,KAAK,CAACmE,QAAQ,CAACvD,SAAS,CAACwD,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAAC1D,WAAW,GAAG4D,EAAE;QACrB,IAAI,CAAChE,eAAe,EAAE;QACtB,IAAI,CAAC+B,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEAkC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACC,SAAS,GAAGD,OAAO;EAC1B;EAEAE,KAAKA,CAAA;IACH,IAAI,CAAClC,WAAW,GAAG;MACjBE,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACRF,qBAAqB,EAAE,IAAI,CAACtC,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEAwE,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC/E,aAAa,CAACgF,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAE,OAAOA,CAACF,GAAQ;IACdA,GAAG,CAACG,KAAK,EAAE;EACb;EAEAC,gBAAgBA,CAACC,IAAS;IACxB,IAAI,CAAC/E,MAAM,CAACgF,QAAQ,CAAC,CAAC,+BAA+BD,IAAI,EAAE,EAAE,IAAI,CAACvE,WAAW,CAAC,CAAC;EACjF;;;uCAxNWhB,wBAAwB,EAAA/C,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1I,EAAA,CAAAwI,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5I,EAAA,CAAAwI,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA9I,EAAA,CAAAwI,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAhJ,EAAA,CAAAwI,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAAlJ,EAAA,CAAAwI,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAApJ,EAAA,CAAAwI,iBAAA,CAAAO,EAAA,CAAAM,MAAA;IAAA;EAAA;;;YAAxBtG,wBAAwB;MAAAuG,SAAA;MAAAC,QAAA,GAAAvJ,EAAA,CAAAwJ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtCnC9J,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAqB,SAAA,qBAAiC;UACnCrB,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAqB,SAAA,YAA0C;UAIpCrB,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBAA2F;UAA/DD,EAAA,CAAAyB,gBAAA,2BAAAuI,sEAAArJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAiI,GAAA,CAAAlE,WAAA,CAAAC,qBAAA,EAAAnF,MAAA,MAAAoJ,GAAA,CAAAlE,WAAA,CAAAC,qBAAA,GAAAnF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzEX,EAAA,CAAAsB,UAAA,KAAA2I,8CAAA,uBAA+E;UAMrFjK,EAHI,CAAAG,YAAA,EAAY,EACR,EAEF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACoE;UAAhCD,EAAA,CAAAyB,gBAAA,2BAAAyI,kEAAAvJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAiI,GAAA,CAAAlE,WAAA,CAAAE,KAAA,EAAApF,MAAA,MAAAoJ,GAAA,CAAAlE,WAAA,CAAAE,KAAA,GAAApF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FX,EADE,CAAAG,YAAA,EAA8F,EAChF;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBAC4D;UAA9BD,EAAA,CAAAyB,gBAAA,2BAAA0I,kEAAAxJ,MAAA;YAAAX,EAAA,CAAA8B,kBAAA,CAAAiI,GAAA,CAAAlE,WAAA,CAAAG,GAAA,EAAArF,MAAA,MAAAoJ,GAAA,CAAAlE,WAAA,CAAAG,GAAA,GAAArF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FX,EAHM,CAAAG,YAAA,EAAsF,EACxE,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC2B,kBACY;UAAlBD,EAAA,CAAAS,UAAA,mBAAA2J,2DAAA;YAAA,OAASL,GAAA,CAAAhC,KAAA,EAAO;UAAA,EAAC;UACtD/H,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA2D;UAAzBD,EAAA,CAAAS,UAAA,mBAAA4J,2DAAA;YAAA,OAASN,GAAA,CAAArE,YAAA,EAAc;UAAA,EAAC;UACxD1F,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAqB,SAAA,aAA6B;UAGtCrB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,kBACmC;UAAhDD,EAAA,CAAAS,UAAA,mBAAA6J,2DAAA;YAAA,OAASP,GAAA,CAAA1B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7ErI,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoF;UAA/CD,EAAA,CAAAS,UAAA,mBAAA8J,2DAAA;YAAA,OAASR,GAAA,CAAA1B,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFrI,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgF;UAAhDD,EAAA,CAAAS,UAAA,mBAAA+J,2DAAA;YAAA,OAAST,GAAA,CAAA1B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7ErI,EAAA,CAAAE,MAAA,0CACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UACNH,EAAA,CAAAsB,UAAA,KAAAmJ,wCAAA,kBAAuD;UAwCzDzK,EAAA,CAAAG,YAAA,EAAe;UAITH,EAHN,CAAAC,cAAA,0BAAsD,eAChC,eACgC,kBACG;UAAnBD,EAAA,CAAAS,UAAA,mBAAAiK,2DAAA;YAAA,OAASX,GAAA,CAAA1D,MAAA,EAAQ;UAAA,EAAC;UAChDrG,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8D;UAAzBD,EAAA,CAAAS,UAAA,mBAAAkK,2DAAA;YAAA,OAASZ,GAAA,CAAArE,YAAA,EAAc;UAAA,EAAC;UAC3D1F,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqD;UAArBD,EAAA,CAAAS,UAAA,mBAAAmK,2DAAA;YAAA,OAASb,GAAA,CAAAxD,QAAA,EAAU;UAAA,EAAC;UAClDvG,EAAA,CAAAE,MAAA,sBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;UAtG4BH,EAAA,CAAAM,SAAA,IAA+C;UAA/CN,EAAA,CAAAgC,gBAAA,YAAA+H,GAAA,CAAAlE,WAAA,CAAAC,qBAAA,CAA+C;UACzC9F,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA2J,GAAA,CAAAvG,uBAAA,CAA0B;UAYIxD,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAgC,gBAAA,YAAA+H,GAAA,CAAAlE,WAAA,CAAAE,KAAA,CAA+B;UAKrC/F,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAgC,gBAAA,YAAA+H,GAAA,CAAAlE,WAAA,CAAAG,GAAA,CAA6B;UA4BzDhG,EAAA,CAAAM,SAAA,IAAiB;UAAjBN,EAAA,CAAAI,UAAA,SAAA2J,GAAA,CAAArG,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}