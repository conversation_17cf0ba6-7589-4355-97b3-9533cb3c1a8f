{"ast": null, "code": "export var EnumFileType;\n(function (EnumFileType) {\n  EnumFileType[EnumFileType[\"PDF\"] = 1] = \"PDF\";\n  EnumFileType[EnumFileType[\"JPG\"] = 2] = \"JPG\";\n})(EnumFileType || (EnumFileType = {}));", "map": {"version": 3, "names": ["EnumFileType"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\enum\\enumFileType.ts"], "sourcesContent": ["export enum EnumFileType {\r\n  PDF = 1,\r\n  JPG = 2\r\n}\r\n"], "mappings": "AAAA,WAAYA,YAGX;AAHD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,oBAAO;EACPA,YAAA,CAAAA,YAAA,oBAAO;AACT,CAAC,EAHWA,YAAY,KAAZA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}