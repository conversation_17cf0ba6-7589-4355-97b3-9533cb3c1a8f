{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemGetListRegularChangeItemPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetListRegularChangeItemPost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemGetListRegularChangeItemPost$Json.PATH = '/api/RegularChangeItem/GetListRegularChangeItem';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemGetListRegularChangeItemPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-get-list-regular-change-item-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetListRegularChangeItemResListResponseBase } from '../../models/get-list-regular-change-item-res-list-response-base';\r\n\r\nexport interface ApiRegularChangeItemGetListRegularChangeItemPost$Json$Params {\r\n}\r\n\r\nexport function apiRegularChangeItemGetListRegularChangeItemPost$Json(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemGetListRegularChangeItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListRegularChangeItemResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetListRegularChangeItemPost$Json.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetListRegularChangeItemResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemGetListRegularChangeItemPost$Json.PATH = '/api/RegularChangeItem/GetListRegularChangeItem';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,qDAAqDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAqE,EAAEC,OAAqB;EACnM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,qDAAqD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC1G,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN;EAAO,CAAE,CAAC,CACjE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAoE;EAC7E,CAAC,CAAC,CACH;AACH;AAEAZ,qDAAqD,CAACM,IAAI,GAAG,iDAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}