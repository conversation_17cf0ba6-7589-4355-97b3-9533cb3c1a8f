{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ISO 10126 padding strategy.\n   */\n  CryptoJS.pad.Iso10126 = {\n    pad: function (data, blockSize) {\n      // Shortcut\n      var blockSizeBytes = blockSize * 4;\n\n      // Count padding bytes\n      var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n      // Pad\n      data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n      // Remove padding\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Iso10126;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}