{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = (a0, a1) => ({\n  \"bg-success\": a0,\n  \"bg-danger\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"pr-7\": a0,\n  \"pl-7\": a1\n});\nconst _c2 = (a0, a1) => ({\n  \"translate-x-14\": a0,\n  \"translate-x-0\": a1\n});\nfunction ContentManagementSalesAccountComponent_nb_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_17_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(ctx_r2.listFormItem.CIsLock));\n    })(\"keydown.space\", function ContentManagementSalesAccountComponent_ng_container_17_Template_div_keydown_space_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(ctx_r2.listFormItem.CIsLock));\n    });\n    i0.ɵɵelementStart(2, \"span\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c0, !ctx_r2.listFormItem.CIsLock, ctx_r2.listFormItem.CIsLock));\n    i0.ɵɵattribute(\"aria-checked\", !ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, ctx_r2.listFormItem.CIsLock, !ctx_r2.listFormItem.CIsLock));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r2.listFormItem.CIsLock ? \"\\u89E3\\u9664\" : \"\\u9396\\u5B9A\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c2, ctx_r2.listFormItem.CIsLock, !ctx_r2.listFormItem.CIsLock));\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"status\", ctx_r2.listFormItem && ctx_r2.listFormItem.CIsLock ? \"basic\" : \"primary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.listFormItem.CIsLock ? \"\\u67E5\\u770B\\u5167\\u5BB9\" : \"\\u7DE8\\u8F2F\\u5167\\u5BB9\", \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27)(2, \"h6\", 28);\n    i0.ɵɵtext(3, \"\\u66AB\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_30_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r6 = ctx.$implicit;\n    const ix_r7 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ix_r7 > 0 ? \"\\u3001\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6.CHousehold);\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 29)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementSalesAccountComponent_tr_30_span_4_Template, 4, 2, \"span\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r8.tblFormItemHouseholds);\n  }\n}\nfunction ContentManagementSalesAccountComponent_nb_card_footer_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-footer\", 33)(1, \"ngb-pagination\", 34);\n    i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementSalesAccountComponent_nb_card_footer_31_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.pageIndex, $event) || (ctx_r2.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function ContentManagementSalesAccountComponent_nb_card_footer_31_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.pageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r2.pageIndex);\n    i0.ɵɵproperty(\"pageSize\", ctx_r2.pageSize)(\"collectionSize\", ctx_r2.totalRecords);\n  }\n}\nexport class ContentManagementSalesAccountComponent extends BaseComponent {\n  toggleSwitch(CIsLock) {\n    if (CIsLock) {\n      this.unLock();\n    } else {\n      this.onLock();\n    }\n  }\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    this.tempBuildCaseID = -1;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.pageSize = 20;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected.cID) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        // this.message.showErrorMSG(res.Message!);\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  navidateDetai() {\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\n  }\n  static {\n    this.ɵfac = function ContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 32,\n      vars: 8,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-hint-color\", \"mb-4\"], [\"size\", \"tiny\", 1, \"mb-4\"], [1, \"p-3\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\", \"mb-3\", \"mb-md-0\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\", \"mb-0\"], [\"fullWidth\", \"\", \"status\", \"primary\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"aria-label\", \"\\u9078\\u64C7\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-6\"], [1, \"d-flex\", \"justify-content-end\", \"align-items-center\", \"w-full\"], [4, \"ngIf\"], [\"nbButton\", \"\", \"class\", \"ml-2\", 3, \"status\", \"click\", 4, \"ngIf\"], [\"nbButton\", \"\", \"status\", \"success\", \"class\", \"ml-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-hover\", \"border\"], [1, \"text-white\"], [\"scope\", \"col\", 1, \"bg-primary\"], [\"class\", \"cursor-pointer hover:bg-light\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-center py-3\", 4, \"ngIf\"], [3, \"value\"], [\"role\", \"switch\", \"tabindex\", \"0\", 1, \"relative\", \"inline-block\", \"w-24\", \"h-10\", \"rounded-full\", \"cursor-pointer\", \"transition-colors\", \"duration-300\", \"mx-2\", \"shadow-sm\", 3, \"click\", \"keydown.space\", \"ngClass\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"bottom-0\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", \"font-medium\", 3, \"ngClass\"], [1, \"absolute\", \"left-0\", \"top-0\", \"h-10\", \"w-10\", \"bg-white\", \"rounded-full\", \"shadow-md\", \"transform\", \"transition-transform\", \"duration-300\", \"ease-in-out\", 3, \"ngClass\"], [\"nbButton\", \"\", 1, \"ml-2\", 3, \"click\", \"status\"], [\"nbButton\", \"\", \"status\", \"success\", 1, \"ml-2\", 3, \"click\"], [\"colspan\", \"2\", 1, \"text-center\", \"py-5\"], [1, \"text-muted\", \"mb-0\"], [1, \"cursor-pointer\", \"hover:bg-light\"], [\"class\", \"mr-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"mr-1\"], [1, \"font-medium\"], [1, \"d-flex\", \"justify-content-center\", \"py-3\"], [\"aria-label\", \"\\u9801\\u9762\\u5C0E\\u822A\", 1, \"pagination-modern\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"]],\n      template: function ContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n          i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"nb-card\", 2)(7, \"nb-card-body\", 3)(8, \"div\", 4)(9, \"div\", 5)(10, \"div\", 6)(11, \"label\", 7);\n          i0.ɵɵtext(12, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"nb-select\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_13_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_13_listener() {\n            return ctx.onSelectionChangeBuildCase();\n          });\n          i0.ɵɵtemplate(14, ContentManagementSalesAccountComponent_nb_option_14_Template, 2, 2, \"nb-option\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11);\n          i0.ɵɵtemplate(17, ContentManagementSalesAccountComponent_ng_container_17_Template, 5, 14, \"ng-container\", 12)(18, ContentManagementSalesAccountComponent_button_18_Template, 2, 2, \"button\", 13)(19, ContentManagementSalesAccountComponent_button_19_Template, 2, 0, \"button\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"table\", 16)(22, \"thead\")(23, \"tr\", 17)(24, \"th\", 18);\n          i0.ɵɵtext(25, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 18);\n          i0.ɵɵtext(27, \"\\u9069\\u7528\\u6236\\u5225\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"tbody\");\n          i0.ɵɵtemplate(29, ContentManagementSalesAccountComponent_tr_29_Template, 4, 0, \"tr\", 12)(30, ContentManagementSalesAccountComponent_tr_30_Template, 5, 2, \"tr\", 19);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(31, ContentManagementSalesAccountComponent_nb_card_footer_31_Template, 2, 3, \"nb-card-footer\", 20);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.isUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.formItems && ctx.isUpdate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.formItems && ctx.isCreate);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", !ctx.formItems || ctx.formItems.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.totalRecords > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbButtonComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9NQUFvTSIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵelementContainerStart", "ɵɵlistener", "ContentManagementSalesAccountComponent_ng_container_17_Template_div_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "toggleSwitch", "listFormItem", "CIsLock", "ContentManagementSalesAccountComponent_ng_container_17_Template_div_keydown_space_1_listener", "$event", "preventDefault", "ɵɵelement", "ɵɵpureFunction2", "_c0", "_c1", "_c2", "ContentManagementSalesAccountComponent_button_18_Template_button_click_0_listener", "_r4", "navid<PERSON><PERSON><PERSON><PERSON>", "ContentManagementSalesAccountComponent_button_19_Template_button_click_0_listener", "_r5", "ix_r7", "ɵɵtextInterpolate", "i_r6", "CHousehold", "ɵɵtemplate", "ContentManagementSalesAccountComponent_tr_30_span_4_Template", "item_r8", "CItemName", "tblFormItemHouseholds", "ɵɵtwoWayListener", "ContentManagementSalesAccountComponent_nb_card_footer_31_Template_ngb_pagination_pageChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "pageIndex", "pageChanged", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "ContentManagementSalesAccountComponent", "unLock", "onLock", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "tempBuildCaseID", "buildingSelectedOptions", "value", "label", "typeContentManagementSalesAccount", "CFormType", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "cBuildCaseSelected", "getUserBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "body", "CBuildCaseId", "buildCaseId", "Entries", "StatusCode", "userBuildCaseOptions", "map", "cID", "index", "findIndex", "x", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "PageIndex", "PageSize", "CIsPaging", "formItems", "TotalItems", "onSelectionChangeBuildCase", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "Message", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "Router", "i3", "MessageService", "i4", "BuildCaseService", "FormItemService", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContentManagementSalesAccountComponent_Template", "rf", "ctx", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_13_listener", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_13_listener", "ContentManagementSalesAccountComponent_nb_option_14_Template", "ContentManagementSalesAccountComponent_ng_container_17_Template", "ContentManagementSalesAccountComponent_button_18_Template", "ContentManagementSalesAccountComponent_button_19_Template", "ContentManagementSalesAccountComponent_tr_29_Template", "ContentManagementSalesAccountComponent_tr_30_Template", "ContentManagementSalesAccountComponent_nb_card_footer_31_Template", "isUpdate", "isCreate", "length", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "NgControlStatus", "NgModel", "i8", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbButtonComponent", "NbSelectComponent", "NbOptionComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { tap } from 'rxjs/operators';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { GetListFormItemRes } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-sales-account',\r\n  templateUrl: './content-management-sales-account.component.html',\r\n  styleUrls: ['./content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule,],\r\n})\r\nexport class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n\r\n\r\n  toggleSwitch(CIsLock: any) {\r\n    if(CIsLock) {\r\n      this.unLock()\r\n    } else {\r\n      this.onLock()\r\n    }\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: any;\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }];\r\n\r\n  formItems: any;\r\n  listFormItem: GetListFormItemRes;\r\n  override pageSize = 20;\r\n\r\n  buildCaseId: number;\r\n  cBuildCaseSelected: any;\r\n  userBuildCaseOptions: any;\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow);\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null;\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          });\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected.cID) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n  }\r\n\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems;\r\n          this.listFormItem = res.Entries;\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem();\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        // this.message.showErrorMSG(res.Message!);\r\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  navidateDetai() {\r\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\r\n  }\r\n}\r\n", "<!-- 3.6  3.7 = 1, 3.6 = 2-->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-hint-color mb-4\">\r\n      您可將於建材管理及方案管理設定好的方案及材料，於此組合成選樣內容，並可設定各方案、材料可選擇之戶型。\r\n    </h1>\r\n    \r\n    <nb-card size=\"tiny\" class=\"mb-4\">\r\n      <nb-card-body class=\"p-3\">\r\n        <div class=\"d-flex flex-wrap\">\r\n          <div class=\"col-md-6 mb-3 mb-md-0\">\r\n            <div class=\"form-group d-flex align-items-center w-full\">\r\n              <label for=\"buildingName\" class=\"label col-3 mb-0\">建案</label>\r\n              <nb-select fullWidth status=\"primary\" placeholder=\"請選擇建案\" [(ngModel)]=\"cBuildCaseSelected\" class=\"col-9\"\r\n                (selectedChange)=\"onSelectionChangeBuildCase()\" aria-label=\"選擇建案\">\r\n                <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                  {{ case.CBuildCaseName }}\r\n                </nb-option>\r\n              </nb-select>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-md-6\">\r\n            <div class=\"d-flex justify-content-end align-items-center w-full\">\r\n\r\n              <ng-container *ngIf=\"listFormItem && isUpdate\">\r\n                <div class=\"relative inline-block w-24 h-10 rounded-full cursor-pointer transition-colors duration-300 mx-2 shadow-sm\"\r\n                  [ngClass]=\"{'bg-success': !listFormItem.CIsLock, 'bg-danger': listFormItem.CIsLock}\"\r\n                  (click)=\"toggleSwitch(listFormItem.CIsLock)\" role=\"switch\" [attr.aria-checked]=\"!listFormItem.CIsLock\" tabindex=\"0\"\r\n                  (keydown.space)=\"$event.preventDefault(); toggleSwitch(listFormItem.CIsLock)\">\r\n                  <span class=\"absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center text-white text-lg font-medium\"\r\n                    [ngClass]=\"{'pr-7': listFormItem.CIsLock, 'pl-7': !listFormItem.CIsLock}\">\r\n                    {{ !listFormItem.CIsLock ? '解除' : '鎖定' }}\r\n                  </span>\r\n                  <span\r\n                    class=\"absolute left-0 top-0 h-10 w-10 bg-white rounded-full shadow-md transform transition-transform duration-300 ease-in-out\"\r\n                    [ngClass]=\"{'translate-x-14': listFormItem.CIsLock, 'translate-x-0': !listFormItem.CIsLock}\"></span>\r\n                </div>\r\n              </ng-container>\r\n              \r\n              <button nbButton [status]=\"listFormItem && listFormItem.CIsLock ? 'basic' : 'primary'\" \r\n                *ngIf=\"listFormItem && listFormItem.formItems && isUpdate\"\r\n                (click)=\"navidateDetai()\" class=\"ml-2\">\r\n                {{listFormItem.CIsLock ? '查看內容' : '編輯內容'}}\r\n              </button>\r\n              \r\n              <button nbButton status=\"success\" *ngIf=\"listFormItem && !listFormItem.formItems && isCreate\"\r\n                (click)=\"navidateDetai()\" class=\"ml-2\">\r\n                新增\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"table-responsive mt-4\">\r\n          <table class=\"table table-hover border\">\r\n            <thead>\r\n              <tr class=\"text-white\">\r\n                <th scope=\"col\" class=\"bg-primary\">方案名稱/建材位置</th>\r\n                <th scope=\"col\" class=\"bg-primary\">適用戶別</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngIf=\"!formItems || formItems.length === 0\">\r\n                <td colspan=\"2\" class=\"text-center py-5\">\r\n                  <h6 class=\"text-muted mb-0\">暫無資料</h6>\r\n                </td>\r\n              </tr>\r\n              <tr *ngFor=\"let item of formItems; let i = index\" class=\"cursor-pointer hover:bg-light\">\r\n                <td>{{ item.CItemName }}</td>\r\n                <td>\r\n                  <span *ngFor=\"let i of item.tblFormItemHouseholds; let ix = index\" class=\"mr-1\">\r\n                    {{ix > 0 ? '、' :''}} <span class=\"font-medium\">{{i.CHousehold}}</span>\r\n                  </span>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  </nb-card-body>\r\n  \r\n  <nb-card-footer class=\"d-flex justify-content-center py-3\" *ngIf=\"totalRecords > 0\">\r\n    <ngb-pagination \r\n      [(page)]=\"pageIndex\" \r\n      [pageSize]=\"pageSize\" \r\n      [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" \r\n      aria-label=\"頁面導航\"\r\n      class=\"pagination-modern\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,GAAG,QAAQ,gBAAgB;AAKpC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAA+BC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;ICQpEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;;IAOFR,EAAA,CAAAS,uBAAA,GAA+C;IAC7CT,EAAA,CAAAC,cAAA,cAGgF;IAA9ED,EADA,CAAAU,UAAA,mBAAAC,qFAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAH,MAAA,CAAAI,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC,2BAAAC,6FAAAC,MAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAC3BM,MAAA,CAAAC,cAAA,EAAuB;MAAA,OAAAtB,EAAA,CAAAgB,WAAA,CAAEF,MAAA,CAAAG,YAAA,CAAAH,MAAA,CAAAI,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC;IAC7EnB,EAAA,CAAAC,cAAA,eAC4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAuB,SAAA,eAEsG;IACxGvB,EAAA,CAAAG,YAAA,EAAM;;;;;IAVJH,EAAA,CAAAM,SAAA,EAAoF;IAApFN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwB,eAAA,IAAAC,GAAA,GAAAX,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAoF;;IAIlFnB,EAAA,CAAAM,SAAA,EAAyE;IAAzEN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwB,eAAA,IAAAE,GAAA,EAAAZ,MAAA,CAAAI,YAAA,CAAAC,OAAA,GAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAyE;IACzEnB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,OAAAO,MAAA,CAAAI,YAAA,CAAAC,OAAA,wCACF;IAGEnB,EAAA,CAAAM,SAAA,EAA4F;IAA5FN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwB,eAAA,KAAAG,GAAA,EAAAb,MAAA,CAAAI,YAAA,CAAAC,OAAA,GAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAA4F;;;;;;IAIlGnB,EAAA,CAAAC,cAAA,iBAEyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAAkB,kFAAA;MAAA5B,EAAA,CAAAY,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgB,aAAA,EAAe;IAAA,EAAC;IACzB9B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJQH,EAAA,CAAAI,UAAA,WAAAU,MAAA,CAAAI,YAAA,IAAAJ,MAAA,CAAAI,YAAA,CAAAC,OAAA,uBAAqE;IAGpFnB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,MAAA,CAAAI,YAAA,CAAAC,OAAA,gEACF;;;;;;IAEAnB,EAAA,CAAAC,cAAA,iBACyC;IAAvCD,EAAA,CAAAU,UAAA,mBAAAqB,kFAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAgB,aAAA,EAAe;IAAA,EAAC;IACzB9B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAgBLH,EAFJ,CAAAC,cAAA,SAAiD,aACN,aACX;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACF;;;;;IAIDH,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACjEF,EADiE,CAAAG,YAAA,EAAO,EACjE;;;;;IADLH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,kBAAA,MAAA0B,KAAA,0BAAqB;IAA0BjC,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkC,iBAAA,CAAAC,IAAA,CAAAC,UAAA,CAAgB;;;;;IAHnEpC,EADF,CAAAC,cAAA,aAAwF,SAClF;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAqC,UAAA,IAAAC,4DAAA,mBAAgF;IAIpFtC,EADE,CAAAG,YAAA,EAAK,EACF;;;;IANCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAK,OAAA,CAAAC,SAAA,CAAoB;IAEFxC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAmC,OAAA,CAAAE,qBAAA,CAA+B;;;;;;IAajEzC,EADF,CAAAC,cAAA,yBAAoF,yBAOtD;IAL1BD,EAAA,CAAA0C,gBAAA,wBAAAC,uGAAAtB,MAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA6C,kBAAA,CAAA/B,MAAA,CAAAgC,SAAA,EAAAzB,MAAA,MAAAP,MAAA,CAAAgC,SAAA,GAAAzB,MAAA;MAAA,OAAArB,EAAA,CAAAgB,WAAA,CAAAK,MAAA;IAAA,EAAoB;IAGpBrB,EAAA,CAAAU,UAAA,wBAAAiC,uGAAAtB,MAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAcF,MAAA,CAAAiC,WAAA,CAAA1B,MAAA,CAAmB;IAAA,EAAC;IAItCrB,EADE,CAAAG,YAAA,EAAiB,EACF;;;;IAPbH,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAgD,gBAAA,SAAAlC,MAAA,CAAAgC,SAAA,CAAoB;IAEpB9C,EADA,CAAAI,UAAA,aAAAU,MAAA,CAAAmC,QAAA,CAAqB,mBAAAnC,MAAA,CAAAoC,YAAA,CACU;;;ADhErC,OAAM,MAAOC,sCAAuC,SAAQrD,aAAa;EAGvEmB,YAAYA,CAACE,OAAY;IACvB,IAAGA,OAAO,EAAE;MACV,IAAI,CAACiC,MAAM,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,EAAE;IACf;EACF;EAcAC,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAlBvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAC,uBAAuB,GAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IAIpD,KAAAf,QAAQ,GAAG,EAAE;IA0DtB,KAAAgB,iCAAiC,GAAG;MAClCC,SAAS,EAAE;KACZ;IA7CC,IAAI,CAACN,aAAa,CAACO,OAAO,EAAE,CAACC,IAAI,CAC/BxE,GAAG,CAAEyE,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACV,eAAe,GAAGQ,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACjB,iBAAiB,CAACkB,qCAAqC,CAAC;MAC3DC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACX,IAAI,CACLxE,GAAG,CAACyE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACW,OAAO,IAAIX,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGb,GAAG,CAACW,OAAO,CAACG,GAAG,CAACd,GAAG,IAAG;UAChD,OAAO;YACL7D,cAAc,EAAE6D,GAAG,CAAC7D,cAAc;YAClC4E,GAAG,EAAEf,GAAG,CAACe;WACV;QACH,CAAC,CAAC;QAEF,IAAI,IAAI,CAACvB,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIwB,KAAK,GAAG,IAAI,CAACH,oBAAoB,CAACI,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACvB,eAAe,CAAC;UAC1F,IAAI,CAACa,kBAAkB,GAAG,IAAI,CAACQ,oBAAoB,CAACG,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACX,kBAAkB,GAAG,IAAI,CAACQ,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACR,kBAAkB,CAACU,GAAG,EAAE;UAC/B,IAAI,CAACI,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAChB,SAAS,EAAE;EACf;EAOAgB,eAAeA,CAAA;IACb,IAAI,CAAC7B,gBAAgB,CAAC8B,mCAAmC,CAAC;MACxDZ,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,kBAAkB,CAACU,GAAG;QACzClB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DwB,SAAS,EAAE,IAAI,CAAC5C,SAAS;QACzB6C,QAAQ,EAAE,IAAI,CAAC1C,QAAQ;QACvB2C,SAAS,EAAE;;KAEd,CAAC,CAACxB,IAAI,CACLxE,GAAG,CAACyE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACW,OAAO,IAAIX,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACY,SAAS,GAAGxB,GAAG,CAACW,OAAO,CAACa,SAAS;QACtC,IAAI,CAAC3E,YAAY,GAAGmD,GAAG,CAACW,OAAO;QAC/B,IAAI,CAAC9B,YAAY,GAAGmB,GAAG,CAACyB,UAAU,GAAGzB,GAAG,CAACyB,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EAEAuB,0BAA0BA,CAAA;IACxB,IAAI,CAACP,eAAe,EAAE;EACxB;EAEAzC,WAAWA,CAACiD,OAAe;IACzB,IAAI,CAAClD,SAAS,GAAGkD,OAAO;IACxB,IAAI,CAACR,eAAe,EAAE;EACxB;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAACM,gBAAgB,CAACsC,gCAAgC,CAAC;MACrDpB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,kBAAkB,CAACU,GAAG;QACzCc,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF;;KAE9B,CAAC,CAAC1B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxB,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAAC1C,OAAO,CAAC2C,YAAY,CAAC,UAAU,CAAC;MACvC;MACA,IAAI,CAACZ,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEApC,MAAMA,CAAA;IACJ,IAAI,CAACO,gBAAgB,CAAC0C,kCAAkC,CAAC;MACvDxB,IAAI,EAAE;QACJyB,YAAY,EAAE,IAAI,CAAC5B,kBAAkB,CAACU,GAAG;QACzCc,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF;;KAE9B,CAAC,CAAC1B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACY,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACxB,OAAO,CAAC0C,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC1C,OAAO,CAAC2C,YAAY,CAAC/B,GAAG,CAACkC,OAAQ,CAAC;MACzC;MACA,IAAI,CAACf,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEA1D,aAAaA,CAAA;IACX,IAAI,CAAC0B,MAAM,CAACgD,QAAQ,CAAC,CAAC,0CAA0C,IAAI,CAAC9B,kBAAkB,CAACU,GAAG,EAAE,CAAC,CAAC;EACjG;;;uCAhJWjC,sCAAsC,EAAAnD,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3G,EAAA,CAAAyG,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7G,EAAA,CAAAyG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/G,EAAA,CAAAyG,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAjH,EAAA,CAAAyG,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAAlH,EAAA,CAAAyG,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtCjE,sCAAsC;MAAAkE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvH,EAAA,CAAAwH,0BAAA,EAAAxH,EAAA,CAAAyH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBjD/H,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAuB,SAAA,qBAAiC;UACnCvB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YAC+B;UACzCD,EAAA,CAAAE,MAAA,qTACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAOKH,EALV,CAAAC,cAAA,iBAAkC,sBACN,aACM,aACO,cACwB,gBACJ;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7DH,EAAA,CAAAC,cAAA,oBACoE;UADVD,EAAA,CAAA0C,gBAAA,2BAAAuF,oFAAA5G,MAAA;YAAArB,EAAA,CAAA6C,kBAAA,CAAAmF,GAAA,CAAAtD,kBAAA,EAAArD,MAAA,MAAA2G,GAAA,CAAAtD,kBAAA,GAAArD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UACxFrB,EAAA,CAAAU,UAAA,4BAAAwH,qFAAA;YAAA,OAAkBF,GAAA,CAAAjC,0BAAA,EAA4B;UAAA,EAAC;UAC/C/F,EAAA,CAAAqC,UAAA,KAAA8F,4DAAA,uBAAoE;UAK1EnI,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,eAAsB,eAC8C;UAuBhED,EArBA,CAAAqC,UAAA,KAAA+F,+DAAA,4BAA+C,KAAAC,yDAAA,qBAiBN,KAAAC,yDAAA,qBAKA;UAK/CtI,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBACO,aAC/B,cACkB,cACc;UAAAD,EAAA,CAAAE,MAAA,yDAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjDH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAE3CF,EAF2C,CAAAG,YAAA,EAAK,EACzC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UAMLD,EALA,CAAAqC,UAAA,KAAAkG,qDAAA,iBAAiD,KAAAC,qDAAA,iBAKuC;UAapGxI,EALU,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO,EACP,EACG;UAEfH,EAAA,CAAAqC,UAAA,KAAAoG,iEAAA,6BAAoF;UAUtFzI,EAAA,CAAAG,YAAA,EAAU;;;UA/E8DH,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAgD,gBAAA,YAAAgF,GAAA,CAAAtD,kBAAA,CAAgC;UAE5D1E,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA4H,GAAA,CAAA9C,oBAAA,CAAuB;UAStClF,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAAU,QAAA,CAA8B;UAgB1C1I,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAA9G,YAAA,CAAA2E,SAAA,IAAAmC,GAAA,CAAAU,QAAA,CAAwD;UAKxB1I,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,KAAA8G,GAAA,CAAA9G,YAAA,CAAA2E,SAAA,IAAAmC,GAAA,CAAAW,QAAA,CAAyD;UAiBvF3I,EAAA,CAAAM,SAAA,IAA0C;UAA1CN,EAAA,CAAAI,UAAA,UAAA4H,GAAA,CAAAnC,SAAA,IAAAmC,GAAA,CAAAnC,SAAA,CAAA+C,MAAA,OAA0C;UAK1B5I,EAAA,CAAAM,SAAA,EAAc;UAAdN,EAAA,CAAAI,UAAA,YAAA4H,GAAA,CAAAnC,SAAA,CAAc;UAea7F,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9E,YAAA,KAAsB;;;qBD9DxEvD,YAAY,EAAAkJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAEnJ,YAAY,EAAAoJ,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,iBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}