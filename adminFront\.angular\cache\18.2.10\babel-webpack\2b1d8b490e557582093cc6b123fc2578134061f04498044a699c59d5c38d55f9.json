{"ast": null, "code": "import { STORAGE_KEY } from \"../../constant/constant\";\nimport { LocalStorageService } from \"../../services/local-storage.service\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(router) {\n      this.router = router;\n    }\n    canActivate(route, state) {\n      if (!LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)) {\n        LocalStorageService.ClearLocalStorage();\n        this.router.navigate(['login']);\n        return false;\n      }\n      return true;\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: \"root\"\n      });\n    }\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}