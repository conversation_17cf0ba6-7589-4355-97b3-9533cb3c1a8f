{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isLeapYear from \"../isLeapYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport default function getDaysInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  if (String(new Date(date)) === 'Invalid Date') {\n    return NaN;\n  }\n  return isLeapYear(date) ? 366 : 365;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}