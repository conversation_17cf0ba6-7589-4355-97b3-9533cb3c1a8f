{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\n// import { AccountService } from '../services/account.service';\nimport { catchError, map } from 'rxjs/operators';\nimport { LocalStorageService } from '../services/local-storage.service';\nimport { STORAGE_KEY } from '../constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/message.service\";\nimport * as i3 from \"ngx-spinner\";\nexport let TokenInterceptor = /*#__PURE__*/(() => {\n  class TokenInterceptor {\n    constructor(router, messageService, spinner) {\n      this.router = router;\n      this.messageService = messageService;\n      this.spinner = spinner;\n      this.LoadingQue = [];\n    }\n    intercept(request, next) {\n      const token = LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN);\n      if (token !== null && token !== undefined && token !== '') {\n        request = request.clone({\n          setHeaders: {\n            Authorization: `Bearer ${LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)}`,\n            BuId: 'BU00000'\n          }\n        });\n      }\n      if (request.url.indexOf('GetAutocompleteList') === -1 && request.url.indexOf('GetAutocompleteByTagValueList') === -1) {\n        this.spinner.show();\n        this.LoadingQue.push(request);\n      }\n      return next.handle(request).pipe(map(event => {\n        if (event instanceof HttpResponse) {\n          this.LoadingQue.pop();\n          if (this.LoadingQue.length == 0) {\n            this.spinner.hide();\n          }\n          if (event.body.StatusCode > 0) {\n            this.messageService.showErrorMSG(event.body.Message);\n          }\n        }\n        return event;\n      }), catchError(error => {\n        if (error.status === 401) {\n          // this.toastMessage.showErrorMSG('請先登入');\n          this.router.navigateByUrl('login');\n        }\n        if (error.status === 403) {\n          // this.toastMessage.showErrorMSG('請先登入');\n          this.messageService.showErrorMSG(\"權限不足\");\n          this.router.navigateByUrl('home');\n        }\n        return throwError(error);\n      }));\n    }\n    static {\n      this.ɵfac = function TokenInterceptor_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TokenInterceptor)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i3.NgxSpinnerService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TokenInterceptor,\n        factory: TokenInterceptor.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TokenInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}