{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiQuotationBatchSaveDataPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiQuotationBatchSaveDataPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiQuotationBatchSaveDataPost$Json.PATH = '/api/Quotation/BatchSaveData';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiQuotationBatchSaveDataPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\quotation\\api-quotation-batch-save-data-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { BatchSaveDataQuotation } from '../../models/batch-save-data-quotation';\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiQuotationBatchSaveDataPost$Json$Params {\r\n    body?: BatchSaveDataQuotation\r\n}\r\n\r\nexport function apiQuotationBatchSaveDataPost$Json(http: HttpClient, rootUrl: string, params?: ApiQuotationBatchSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    const rb = new RequestBuilder(rootUrl, apiQuotationBatchSaveDataPost$Json.PATH, 'post');\r\n    if (params) {\r\n        rb.body(params.body, 'application/*+json');\r\n    }\r\n\r\n    return http.request(\r\n        rb.build({ responseType: 'json', accept: 'text/json', context })\r\n    ).pipe(\r\n        filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n        map((r: HttpResponse<any>) => {\r\n            return r as StrictHttpResponse<StringResponseBase>;\r\n        })\r\n    );\r\n}\r\n\r\napiQuotationBatchSaveDataPost$Json.PATH = '/api/Quotation/BatchSaveData';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,kCAAkCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAkD,EAAEC,OAAqB;EAC3J,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,kCAAkC,CAACM,IAAI,EAAE,MAAM,CAAC;EACvF,IAAIH,MAAM,EAAE;IACRE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC9C;EAEA,OAAON,IAAI,CAACO,OAAO,CACfH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACnE,CAACQ,IAAI,CACFf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IACzB,OAAOA,CAA2C;EACtD,CAAC,CAAC,CACL;AACL;AAEAb,kCAAkC,CAACM,IAAI,GAAG,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}