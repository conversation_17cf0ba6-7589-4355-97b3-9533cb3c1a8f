{"ast": null, "code": "import { ProfitChartData } from '../data/profit-chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport let ProfitChartService = /*#__PURE__*/(() => {\n  class ProfitChartService extends ProfitChartData {\n    constructor(period) {\n      super();\n      this.period = period;\n      this.year = ['2012', '2013', '2014', '2015', '2016', '2017', '2018'];\n      this.data = {};\n      this.data = {\n        week: this.getDataForWeekPeriod(),\n        month: this.getDataForMonthPeriod(),\n        year: this.getDataForYearPeriod()\n      };\n    }\n    getDataForWeekPeriod() {\n      const nPoint = this.period.getWeeks().length;\n      return {\n        chartLabel: this.period.getWeeks(),\n        data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n      };\n    }\n    getDataForMonthPeriod() {\n      const nPoint = this.period.getMonths().length;\n      return {\n        chartLabel: this.period.getMonths(),\n        data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n      };\n    }\n    getDataForYearPeriod() {\n      const nPoint = this.year.length;\n      return {\n        chartLabel: this.year,\n        data: [this.getRandomData(nPoint), this.getRandomData(nPoint), this.getRandomData(nPoint)]\n      };\n    }\n    getRandomData(nPoints) {\n      return Array.from(Array(nPoints)).map(() => {\n        return Math.round(Math.random() * 500);\n      });\n    }\n    getProfitChartData(period) {\n      return this.data[period];\n    }\n    static {\n      this.ɵfac = function ProfitChartService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ProfitChartService)(i0.ɵɵinject(i1.PeriodsService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProfitChartService,\n        factory: ProfitChartService.ɵfac\n      });\n    }\n  }\n  return ProfitChartService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}