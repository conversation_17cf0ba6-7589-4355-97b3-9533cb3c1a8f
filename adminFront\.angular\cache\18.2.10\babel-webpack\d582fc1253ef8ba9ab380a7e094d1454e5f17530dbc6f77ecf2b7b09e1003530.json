{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { BootstrapTheme } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nvar index = createPlugin({\n  name: '@fullcalendar/bootstrap',\n  themeClasses: {\n    bootstrap: BootstrapTheme\n  }\n});\nexport { index as default };", "map": {"version": 3, "names": ["createPlugin", "BootstrapTheme", "index", "name", "themeClasses", "bootstrap", "default"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@fullcalendar/bootstrap/index.js"], "sourcesContent": ["import { createPlugin } from '@fullcalendar/core/index.js';\nimport { BootstrapTheme } from './internal.js';\nimport '@fullcalendar/core/internal.js';\n\nvar index = createPlugin({\n    name: '@fullcalendar/bootstrap',\n    themeClasses: {\n        bootstrap: BootstrapTheme,\n    },\n});\n\nexport { index as default };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAO,gCAAgC;AAEvC,IAAIC,KAAK,GAAGF,YAAY,CAAC;EACrBG,IAAI,EAAE,yBAAyB;EAC/BC,YAAY,EAAE;IACVC,SAAS,EAAEJ;EACf;AACJ,CAAC,CAAC;AAEF,SAASC,KAAK,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}