{"ast": null, "code": "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "map": {"version": 3, "names": ["getBoundingClientRect", "getDocumentElement", "getWindowScroll", "getWindowScrollBarX", "element", "left", "scrollLeft"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js"], "sourcesContent": ["import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,eAAe,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACnD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOJ,qBAAqB,CAACC,kBAAkB,CAACG,OAAO,CAAC,CAAC,CAACC,IAAI,GAAGH,eAAe,CAACE,OAAO,CAAC,CAACE,UAAU;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}