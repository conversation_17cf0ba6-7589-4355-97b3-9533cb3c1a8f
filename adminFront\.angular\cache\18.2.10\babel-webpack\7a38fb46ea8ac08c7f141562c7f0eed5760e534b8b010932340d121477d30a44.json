{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"img\", 51);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, imageUrl_r2), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_div_1_Template, 3, 3, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"img\", 51);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, formItemReq_r3.CMatrialUrl[0]), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_Template, 2, 1, \"div\", 46)(2, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_2_Template, 3, 3, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length === 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" \\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"img\", 58);\n    i0.ɵɵelementStart(2, \"input\", 59);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener($event) {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r8, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener() {\n      const picture_r9 = i0.ɵɵrestoreView(_r7).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r9.id, formItemReq_r3));\n    });\n    i0.ɵɵtext(4, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r9 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", picture_r9.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", picture_r9.name)(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u5DF2\\u4E0A\\u50B3\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template, 5, 4, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 62);\n    i0.ɵɵpipe(4, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 1, formItemReq_r3.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" \\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 64)(1, \"nb-checkbox\", 65);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.allSelected, $event) || (formItemReq_r3.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r3));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 64)(1, \"nb-checkbox\", 67);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedItems[item_r12], $event) || (formItemReq_r3.selectedItems[item_r12] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r12, formItemReq_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedItems[item_r12]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r12, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template, 3, 3, \"label\", 66);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 67);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const remark_r14 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedRemarkType[remark_r14], $event) || (formItemReq_r3.selectedRemarkType[remark_r14] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const remark_r14 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r14, formItemReq_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r14 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedRemarkType[remark_r14]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r14, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 64);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template, 2, 1, \"label\", 66);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 41);\n    i0.ɵɵtext(2, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r15 = i0.ɵɵreference(6);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CRemarkTypeOptions && ctx_r3.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r15);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"div\", 16)(6, \"label\", 17);\n    i0.ɵɵtext(7, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template, 3, 2, \"div\", 18)(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"div\", 21)(12, \"label\", 22);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CItemName, $event) || (formItemReq_r3.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CRequireAnswer, $event) || (formItemReq_r3.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 28)(23, \"label\", 26);\n    i0.ɵɵtext(24, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-select\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedCUiType, $event) || (formItemReq_r3.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener() {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(26, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template, 2, 2, \"nb-option\", 30);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(27, \"div\", 31)(28, \"div\", 32)(29, \"div\", 33)(30, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r6 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(inputFile_r6.click());\n    });\n    i0.ɵɵtext(31, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"input\", 35, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 1, \"div\", 36)(35, DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template, 5, 3, \"div\", 37)(36, DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(37, \"hr\", 39);\n    i0.ɵɵelementStart(38, \"div\", 40)(39, \"div\", 41);\n    i0.ɵɵtext(40, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 42);\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template, 3, 2, \"label\", 43)(43, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template, 2, 1, \"ng-container\", 44)(44, DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template, 7, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    const formItemReq_r3 = ctx.$implicit;\n    const idx_r16 = ctx.index;\n    const noHouseholds_r17 = i0.ɵɵreference(45);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl && formItemReq_r3.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CMatrialUrl || formItemReq_r3.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r16);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r16);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r3.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r16);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r16);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.listPictures && formItemReq_r3.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r17);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CMatrialUrl: o.CPicture ? [o.CPicture] : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 3,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [\"accent\", \"success\"], [1, \"pb-4\"], [1, \"font-bold\", \"text-[#818181]\", \"mb-4\"], [1, \"space-y-6\"], [1, \"font-bold\", \"text-xl\", \"pb-2\", \"border-b\", \"mb-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"p-3\", \"sticky\", \"bottom-0\", \"bg-white\", \"border-t\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"p-4\", \"border\", \"rounded-lg\", \"shadow-sm\", \"bg-white\"], [1, \"flex\", \"flex-wrap\", \"-mx-2\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [1, \"flex\", \"flex-wrap\"], [1, \"w-full\", \"md:w-1/3\", \"pr-0\", \"md:pr-3\", \"mb-4\", \"md:mb-0\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [4, \"ngIf\"], [\"class\", \"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-2/3\", \"md:pl-3\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\", \"mb-3\"], [1, \"label\", \"w-1/3\", \"text-base\", \"pr-2\", \"shrink-0\", 3, \"for\"], [1, \"input-group\", \"items-baseline\", \"w-2/3\"], [1, \"text-gray-600\", \"text-sm\", \"mr-1\", \"shrink-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"w-full\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"label\", \"w-1/3\", \"pr-2\", \"shrink-0\", 3, \"for\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-2/3\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-2/3\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"mt-4\", \"md:mt-0\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\", \"mb-2\"], [1, \"btn\", \"btn-info\", \"h-fit\", \"w-full\", 3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"mt-2 space-y-3\", 4, \"ngIf\"], [\"class\", \"w-full text-center mt-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\", 4, \"ngIf\"], [1, \"my-4\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\", \"mb-3\"], [1, \"w-full\", \"md:w-1/5\", \"px-2\", \"pb-1\", \"md:pb-0\", \"font-medium\", \"text-gray-700\"], [1, \"w-full\", \"md:w-4/5\", \"px-2\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"flex flex-wrap w-full items-center\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-2 gap-2\", 4, \"ngIf\"], [\"class\", \"h-[160px] w-full\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-2\", \"gap-2\"], [\"class\", \"h-[80px] w-full\", 4, \"ngFor\", \"ngForOf\"], [1, \"h-[80px]\", \"w-full\"], [1, \"h-full\", \"w-full\", \"object-cover\", \"rounded\", \"border\", 3, \"src\"], [1, \"h-[160px]\", \"w-full\"], [1, \"h-[160px]\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\"], [3, \"value\"], [1, \"mt-2\", \"space-y-3\"], [\"class\", \"border p-2 rounded-md bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"p-2\", \"rounded-md\", \"bg-gray-50\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"mb-2\", \"border\", 3, \"src\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-1\", \"text-xs\", \"mb-1\", 3, \"blur\", \"value\", \"disabled\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", \"w-full\", 3, \"click\", \"disabled\"], [1, \"w-full\", \"text-center\", \"mt-2\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"border\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\", \"mt-2\"], [1, \"mr-3\", \"cursor-pointer\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 4);\n          i0.ɵɵelement(4, \"h1\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 47, 26, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_1_0 = ctx.listFormItem.CIsLock) !== null && tmp_1_0 !== undefined ? tmp_1_0 : false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_2_0 = ctx.listFormItem.CIsLock) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvZGV0YWlsLWNvbnRlbnQtbWFuYWdlbWVudC1zYWxlcy1hY2NvdW50L2RldGFpbC1jb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ05BQWdOIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpipeBind1", "imageUrl_r2", "ɵɵsanitizeUrl", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_div_1_Template", "formItemReq_r3", "CMatrialUrl", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_2_Template", "length", "ɵɵtext", "case_r5", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener", "$event", "i_r8", "ɵɵrestoreView", "_r7", "index", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener", "picture_r9", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener", "_r10", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r12", "_r11", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r13", "remark_r14", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r15", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener", "inputFile_r6", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template", "idx_r16", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "noHouseholds_r17", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "nextImage", "formItemReq", "currentImageIndex", "prevImage", "getCurrentImage", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "key", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_8_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_10_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_12_listener", "tmp_1_0", "tmp_2_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 \r\n        ? formItemReq.CMatrialUrl.length - 1 \r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0],\r\n              CMatrialUrl: o.CPicture ? [o.CPicture] : [],\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"pb-4\"> <!-- Added padding to bottom of card body -->\r\n    <h1 class=\"font-bold text-[#818181] mb-4\"></h1> <!-- Retained original empty h1, added margin-bottom -->\r\n    <div class=\"space-y-6\"> <!-- Add vertical spacing between main sections -->\r\n      <h4 class=\"font-bold text-xl pb-2 border-b mb-4\">類型-獨立選樣</h4> <!-- Styled section title -->\r\n\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <!-- Enhanced container for each form item -->\r\n        <div class=\"p-4 border rounded-lg shadow-sm bg-white\">\r\n          <div class=\"flex flex-wrap -mx-2\"> <!-- Row container with negative margin for column padding -->\r\n\r\n            <!-- Left Column: Main Material Image + Form Fields -->\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <div class=\"flex flex-wrap\"> <!-- Main Material Images (CMatrialUrl) -->\r\n                <div class=\"w-full md:w-1/3 pr-0 md:pr-3 mb-4 md:mb-0\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">主要材料示意</label>\r\n                  <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\">\r\n                    <!-- Multiple images display -->\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"grid grid-cols-2 gap-2\">\r\n                      <div *ngFor=\"let imageUrl of formItemReq.CMatrialUrl\" class=\"h-[80px] w-full\">\r\n                        <img class=\"h-full w-full object-cover rounded border\" [src]=\"imageUrl | base64Image\">\r\n                      </div>\r\n                    </div>\r\n                    <!-- Single image display -->\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl.length === 1\" class=\"h-[160px] w-full\">\r\n                      <img class=\"h-full w-full object-cover rounded border\"\r\n                        [src]=\"formItemReq.CMatrialUrl[0] | base64Image\">\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                    class=\"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\">\r\n                    無主要材料示意\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields -->\r\n                <div class=\"w-full md:w-2/3 md:pl-3\">\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'CItemName_' + idx\" class=\"label w-1/3 text-base pr-2 shrink-0\">項目名稱</label>\r\n                    <div class=\"input-group items-baseline w-2/3\">\r\n                      <span\r\n                        class=\"text-gray-600 text-sm mr-1 shrink-0\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:</span>\r\n                      <input type=\"text\" [id]=\"'CItemName_' + idx\" class=\"w-full\" nbInput\r\n                        [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                        [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'cRequireAnswer_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">必填數量</label>\r\n                    <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\" class=\"w-2/3\" nbInput placeholder=\"輸入數量\"\r\n                      [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                      [disabled]=\"formItemReq.selectedCUiType.value === 3 || listFormItem.CIsLock ?? false\" />\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full\">\r\n                    <label [for]=\"'uiType_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">前台UI類型</label>\r\n                    <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                      class=\"w-2/3\" (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                      <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                        {{ case.label }}\r\n                      </nb-option>\r\n                    </nb-select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Concept Design Image Upload and Preview -->\r\n            <div class=\"w-full md:w-1/4 px-2 mt-4 md:mt-0\">\r\n              <div class=\"w-full flex flex-col\">\r\n                <div class=\"flex justify-end w-full mb-2\">\r\n                  <button class=\"btn btn-info h-fit w-full\" [disabled]=\"listFormItem.CIsLock\"\r\n                    (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n                  <!-- Note: #inputFile in *ngFor creates a local template variable for each iteration -->\r\n                  <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                    accept=\"image/png, image/gif, image/jpeg\">\r\n                </div>\r\n\r\n                <!-- Uploaded Pictures List -->\r\n                <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"mt-2 space-y-3\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">已上傳概念圖</label>\r\n                  <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                    class=\"border p-2 rounded-md bg-gray-50\">\r\n                    <img class=\"h-32 w-full object-cover rounded mb-2 border\" [src]=\"picture.data\">\r\n                    <input nbInput class=\"w-full p-1 text-xs mb-1\" type=\"text\" placeholder=\"圖片說明/檔名\"\r\n                      [value]=\"picture.name\" (blur)=\"renameFile($event, i, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                    <button class=\"btn btn-outline-danger btn-xs w-full\" (click)=\"removeImage(picture.id, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">删除</button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Default Concept Design Image (if no uploaded list) -->\r\n                <div class=\"w-full text-center mt-2\"\r\n                  *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">預設概念圖</label>\r\n                  <img class=\"h-32 w-full object-cover rounded border\" [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                </div>\r\n\r\n                <div\r\n                  *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                  class=\"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\">\r\n                  無概念設計圖\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Separator Line -->\r\n          <hr class=\"my-4\">\r\n\r\n          <!-- Applicable Households -->\r\n          <div class=\"flex flex-wrap w-full items-center mb-3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">適用戶別</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <label class=\"mr-3 cursor-pointer\" *ngIf=\"houseHoldList.length\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                  全選\r\n                </nb-checkbox>\r\n              </label>\r\n              <ng-container *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                <label *ngFor=\"let item of houseHoldList\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                    [disabled]=\"listFormItem.CIsLock\"\r\n                    (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                    {{ item }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noHouseholds>\r\n                <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Remark Options (Conditional) -->\r\n          <div class=\"flex flex-wrap w-full items-center\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">備註選項</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <ng-container *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                    [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                    (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                    {{ remark }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noRemarkOptions>\r\n                <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n        </div> <!-- End of enhanced container for each form item -->\r\n      </ng-container>\r\n\r\n      <!-- Removed commented out old ngFor structure -->\r\n\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-end p-3 sticky bottom-0 bg-white border-t\"> <!-- Made footer sticky -->\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-primary\" (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;;;;;;;ICQtDC,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,SAAA,cAAsF;;IACxFF,EAAA,CAAAG,YAAA,EAAM;;;;IADmDH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAK,UAAA,QAAAL,EAAA,CAAAM,WAAA,OAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAA8B;;;;;IAFzFR,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAS,UAAA,IAAAC,sFAAA,kBAA8E;IAGhFV,EAAA,CAAAG,YAAA,EAAM;;;;IAHsBH,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAK,UAAA,YAAAM,cAAA,CAAAC,WAAA,CAA0B;;;;;IAKtDZ,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAE,SAAA,cACmD;;IACrDF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EAAgD;IAAhDJ,EAAA,CAAAK,UAAA,QAAAL,EAAA,CAAAM,WAAA,OAAAK,cAAA,CAAAC,WAAA,MAAAZ,EAAA,CAAAQ,aAAA,CAAgD;;;;;IAVtDR,EAAA,CAAAC,cAAA,UAA2E;IAQzED,EANA,CAAAS,UAAA,IAAAI,gFAAA,kBAA+E,IAAAC,gFAAA,kBAMJ;IAI7Ed,EAAA,CAAAG,YAAA,EAAM;;;;IAVEH,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAC,WAAA,CAAAG,MAAA,KAAwC;IAMxCf,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAC,WAAA,CAAAG,MAAA,OAA0C;;;;;IAKlDf,EAAA,CAAAC,cAAA,cACoG;IAClGD,EAAA,CAAAgB,MAAA,mDACF;IAAAhB,EAAA,CAAAG,YAAA,EAAM;;;;;IA0BFH,EAAA,CAAAC,cAAA,oBAA8D;IAC5DD,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAG,YAAA,EAAY;;;;IAFmCH,EAAA,CAAAK,UAAA,UAAAY,OAAA,CAAc;IAC3DjB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IAqBJnB,EAAA,CAAAC,cAAA,cAC2C;IACzCD,EAAA,CAAAE,SAAA,cAA+E;IAC/EF,EAAA,CAAAC,cAAA,gBAE6C;IADpBD,EAAA,CAAAoB,UAAA,kBAAAC,wGAAAC,MAAA;MAAA,MAAAC,IAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAf,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAQD,MAAA,CAAAE,UAAA,CAAAT,MAAA,EAAAC,IAAA,EAAAZ,cAAA,CAAkC;IAAA,EAAC;IADpEX,EAAA,CAAAG,YAAA,EAE6C;IAC7CH,EAAA,CAAAC,cAAA,iBAC6C;IADQD,EAAA,CAAAoB,UAAA,mBAAAY,0GAAA;MAAA,MAAAC,UAAA,GAAAjC,EAAA,CAAAwB,aAAA,CAAAC,GAAA,EAAAG,SAAA;MAAA,MAAAjB,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAASD,MAAA,CAAAK,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAAxB,cAAA,CAAoC;IAAA,EAAC;IACtDX,EAAA,CAAAgB,MAAA,mBAAE;IACjDhB,EADiD,CAAAG,YAAA,EAAS,EACpD;;;;;;;IANsDH,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,UAAA,QAAA4B,UAAA,CAAAG,IAAA,EAAApC,EAAA,CAAAQ,aAAA,CAAoB;IAE5ER,EAAA,CAAAI,SAAA,EAAsB;IACtBJ,EADA,CAAAK,UAAA,UAAA4B,UAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAT,MAAA,CAAAU,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SACoB;IAE1CtC,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAK,UAAA,cAAAqC,QAAA,GAAAb,MAAA,CAAAU,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAR9C1C,EADF,CAAAC,cAAA,cAAoG,gBACtC;IAAAD,EAAA,CAAAgB,MAAA,2CAAM;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAS,UAAA,IAAAkC,iFAAA,kBAC2C;IAQ7C3C,EAAA,CAAAG,YAAA,EAAM;;;;IATqBH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,UAAA,YAAAM,cAAA,CAAAiC,YAAA,CAA6B;;;;;IActD5C,EAFF,CAAAC,cAAA,cAC6G,gBAC/C;IAAAD,EAAA,CAAAgB,MAAA,qCAAK;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IACzEH,EAAA,CAAAE,SAAA,cAAsG;;IACxGF,EAAA,CAAAG,YAAA,EAAM;;;;IADiDH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,UAAA,QAAAL,EAAA,CAAAM,WAAA,OAAAK,cAAA,CAAAkC,cAAA,GAAA7C,EAAA,CAAAQ,aAAA,CAAgD;;;;;IAGvGR,EAAA,CAAAC,cAAA,cAEoG;IAClGD,EAAA,CAAAgB,MAAA,6CACF;IAAAhB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAaNH,EADF,CAAAC,cAAA,gBAAgE,sBAEJ;IAD7CD,EAAA,CAAA8C,gBAAA,2BAAAC,mHAAAzB,MAAA;MAAAtB,EAAA,CAAAwB,aAAA,CAAAwB,IAAA;MAAA,MAAArC,cAAA,GAAAX,EAAA,CAAA2B,aAAA,GAAAC,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAAuC,WAAA,EAAA5B,MAAA,MAAAX,cAAA,CAAAuC,WAAA,GAAA5B,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAChDtB,EAAA,CAAAoB,UAAA,2BAAA2B,mHAAAzB,MAAA;MAAAtB,EAAA,CAAAwB,aAAA,CAAAwB,IAAA;MAAA,MAAArC,cAAA,GAAAX,EAAA,CAAA2B,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAiBD,MAAA,CAAAsB,gBAAA,CAAA7B,MAAA,EAAAX,cAAA,CAAqC;IAAA,EAAC;IACvDX,EAAA,CAAAgB,MAAA,qBACF;IACFhB,EADE,CAAAG,YAAA,EAAc,EACR;;;;;IAJOH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAAuC,WAAA,CAAqC;IAAClD,EAAA,CAAAK,UAAA,aAAAwB,MAAA,CAAAU,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAOlFxC,EADF,CAAAC,cAAA,gBAAsE,sBAGS;IAFhED,EAAA,CAAA8C,gBAAA,2BAAAO,kIAAA/B,MAAA;MAAA,MAAAgC,QAAA,GAAAtD,EAAA,CAAAwB,aAAA,CAAA+B,IAAA,EAAA3B,SAAA;MAAA,MAAAjB,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAA6C,aAAA,CAAAF,QAAA,GAAAhC,MAAA,MAAAX,cAAA,CAAA6C,aAAA,CAAAF,QAAA,IAAAhC,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAA6C;IAExDtB,EAAA,CAAAoB,UAAA,2BAAAiC,kIAAA/B,MAAA;MAAA,MAAAgC,QAAA,GAAAtD,EAAA,CAAAwB,aAAA,CAAA+B,IAAA,EAAA3B,SAAA;MAAA,MAAAjB,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAiBD,MAAA,CAAA4B,6BAAA,CAAAnC,MAAA,EAAAgC,QAAA,EAAA3C,cAAA,CAAwD;IAAA,EAAC;IAC1EX,EAAA,CAAAgB,MAAA,GACF;IACFhB,EADE,CAAAG,YAAA,EAAc,EACR;;;;;;IALOH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAA6C,aAAA,CAAAF,QAAA,EAA6C;IACxDtD,EAAA,CAAAK,UAAA,aAAAwB,MAAA,CAAAU,YAAA,CAAAC,OAAA,CAAiC;IAEjCxC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAAoC,QAAA,MACF;;;;;IANJtD,EAAA,CAAA0D,uBAAA,GAAkE;IAChE1D,EAAA,CAAAS,UAAA,IAAAkD,4FAAA,oBAAsE;;;;;IAA9C3D,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,UAAA,YAAAwB,MAAA,CAAA+B,aAAA,CAAgB;;;;;IASxC5D,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAgB,MAAA,2CAAM;IAAAhB,EAAA,CAAAG,YAAA,EAAO;;;;;;IAW/CH,EAAA,CAAAC,cAAA,sBAEwE;IADtED,EAAA,CAAA8C,gBAAA,2BAAAe,sJAAAvC,MAAA;MAAAtB,EAAA,CAAAwB,aAAA,CAAAsC,IAAA;MAAA,MAAAC,UAAA,GAAA/D,EAAA,CAAA2B,aAAA,GAAAC,SAAA;MAAA,MAAAjB,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAAqD,kBAAA,CAAAD,UAAA,GAAAzC,MAAA,MAAAX,cAAA,CAAAqD,kBAAA,CAAAD,UAAA,IAAAzC,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAAoD;IACpDtB,EAAA,CAAAoB,UAAA,2BAAAyC,sJAAAvC,MAAA;MAAAtB,EAAA,CAAAwB,aAAA,CAAAsC,IAAA;MAAA,MAAAC,UAAA,GAAA/D,EAAA,CAAA2B,aAAA,GAAAC,SAAA;MAAA,MAAAjB,cAAA,GAAAX,EAAA,CAAA2B,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAiBD,MAAA,CAAAoC,sBAAA,CAAA3C,MAAA,EAAAyC,UAAA,EAAApD,cAAA,CAAmD;IAAA,EAAC;IACrEX,EAAA,CAAAgB,MAAA,GACF;IAAAhB,EAAA,CAAAG,YAAA,EAAc;;;;;;IAHZH,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAAqD,kBAAA,CAAAD,UAAA,EAAoD;IAAC/D,EAAA,CAAAK,UAAA,aAAAwB,MAAA,CAAAU,YAAA,CAAAC,OAAA,CAAiC;IAEtFxC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAkB,kBAAA,MAAA6C,UAAA,MACF;;;;;IALF/D,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAS,UAAA,IAAAyD,gHAAA,0BAEwE;IAG1ElE,EAAA,CAAAG,YAAA,EAAQ;;;;IALQH,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAqD,kBAAA,CAAoC;;;;;IAFtDhE,EAAA,CAAA0D,uBAAA,GAAgG;IAC9F1D,EAAA,CAAAS,UAAA,IAAA0D,kGAAA,oBAA6E;;;;;IAAnDnE,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,YAAAwB,MAAA,CAAAuC,kBAAA,CAAqB;;;;;IAS/CpE,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAgB,MAAA,2CAAM;IAAAhB,EAAA,CAAAG,YAAA,EAAO;;;;;IAZrDH,EADF,CAAAC,cAAA,cAAgG,cACrB;IAAAD,EAAA,CAAAgB,MAAA,+BAAI;IAAAhB,EAAA,CAAAG,YAAA,EAAM;IACnFH,EAAA,CAAAC,cAAA,cAAkC;IAUhCD,EATA,CAAAS,UAAA,IAAA4D,0FAAA,2BAAgG,IAAAC,yFAAA,gCAAAtE,EAAA,CAAAuE,sBAAA,CASlE;IAIlCvE,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAbaH,EAAA,CAAAI,SAAA,GAA2D;IAAAJ,EAA3D,CAAAK,UAAA,SAAAwB,MAAA,CAAAuC,kBAAA,IAAAvC,MAAA,CAAAuC,kBAAA,CAAArD,MAAA,KAA2D,aAAAyD,mBAAA,CAAoB;;;;;;IAtItGxE,EAAA,CAAA0D,uBAAA,GAA8E;IASlE1D,EAPV,CAAAC,cAAA,cAAsD,cAClB,cAGE,cACJ,cAC6B,gBACO;IAAAD,EAAA,CAAAgB,MAAA,2CAAM;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IAc1EH,EAbA,CAAAS,UAAA,IAAAgE,0EAAA,kBAA2E,IAAAC,0EAAA,kBAcyB;IAGtG1E,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAAqC,eACmB,iBAC0B;IAAAD,EAAA,CAAAgB,MAAA,gCAAI;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IAExFH,EADF,CAAAC,cAAA,eAA8C,gBAEE;IAAAD,EAAA,CAAAgB,MAAA,IAAsE;IAAAhB,EAAA,CAAAG,YAAA,EAAO;IAC3HH,EAAA,CAAAC,cAAA,iBAE+C;IAD7CD,EAAA,CAAA8C,gBAAA,2BAAA6B,qGAAArD,MAAA;MAAA,MAAAX,cAAA,GAAAX,EAAA,CAAAwB,aAAA,CAAAoD,GAAA,EAAAhD,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAAkE,SAAA,EAAAvD,MAAA,MAAAX,cAAA,CAAAkE,SAAA,GAAAvD,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAGzCtB,EAJI,CAAAG,YAAA,EAE+C,EAC3C,EACF;IAEJH,EADF,CAAAC,cAAA,eAAsD,iBACqB;IAAAD,EAAA,CAAAgB,MAAA,gCAAI;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IACrFH,EAAA,CAAAC,cAAA,iBAE0F;IADxFD,EAAA,CAAA8C,gBAAA,2BAAAgC,qGAAAxD,MAAA;MAAA,MAAAX,cAAA,GAAAX,EAAA,CAAAwB,aAAA,CAAAoD,GAAA,EAAAhD,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAAoE,cAAA,EAAAzD,MAAA,MAAAX,cAAA,CAAAoE,cAAA,GAAAzD,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAE5CtB,EAHE,CAAAG,YAAA,EAE0F,EACtF;IAEJH,EADF,CAAAC,cAAA,eAAiD,iBACkB;IAAAD,EAAA,CAAAgB,MAAA,kCAAM;IAAAhB,EAAA,CAAAG,YAAA,EAAQ;IAC/EH,EAAA,CAAAC,cAAA,qBAE6C;IAFUD,EAAA,CAAA8C,gBAAA,2BAAAkC,yGAAA1D,MAAA;MAAA,MAAAX,cAAA,GAAAX,EAAA,CAAAwB,aAAA,CAAAoD,GAAA,EAAAhD,SAAA;MAAA5B,EAAA,CAAAiD,kBAAA,CAAAtC,cAAA,CAAAsE,eAAA,EAAA3D,MAAA,MAAAX,cAAA,CAAAsE,eAAA,GAAA3D,MAAA;MAAA,OAAAtB,EAAA,CAAA8B,WAAA,CAAAR,MAAA;IAAA,EAAyC;IAChFtB,EAAA,CAAAoB,UAAA,4BAAA8D,0GAAA;MAAA,MAAAvE,cAAA,GAAAX,EAAA,CAAAwB,aAAA,CAAAoD,GAAA,EAAAhD,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAkBD,MAAA,CAAAsD,mBAAA,CAAAxE,cAAA,CAAgC;IAAA,EAAC;IAEjEX,EAAA,CAAAS,UAAA,KAAA2E,iFAAA,wBAA8D;IAOxEpF,EAJQ,CAAAG,YAAA,EAAY,EACR,EACF,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,eAA+C,eACX,eACU,kBAEV;IAA5BD,EAAA,CAAAoB,UAAA,mBAAAiE,8FAAA;MAAArF,EAAA,CAAAwB,aAAA,CAAAoD,GAAA;MAAA,MAAAU,YAAA,GAAAtF,EAAA,CAAAuF,WAAA;MAAA,OAAAvF,EAAA,CAAA8B,WAAA,CAASwD,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAACxF,EAAA,CAAAgB,MAAA,kDAAO;IAAAhB,EAAA,CAAAG,YAAA,EAAS;IAE9CH,EAAA,CAAAC,cAAA,oBAC4C;IADCD,EAAA,CAAAoB,UAAA,oBAAAqE,8FAAAnE,MAAA;MAAA,MAAAX,cAAA,GAAAX,EAAA,CAAAwB,aAAA,CAAAoD,GAAA,EAAAhD,SAAA;MAAA,MAAAC,MAAA,GAAA7B,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA8B,WAAA,CAAUD,MAAA,CAAA6D,WAAA,CAAApE,MAAA,EAAAX,cAAA,CAAgC;IAAA,EAAC;IAE1FX,EAFE,CAAAG,YAAA,EAC4C,EACxC;IAuBNH,EApBA,CAAAS,UAAA,KAAAkF,2EAAA,kBAAoG,KAAAC,2EAAA,kBAeS,KAAAC,2EAAA,kBAOT;IAK1G7F,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGNH,EAAA,CAAAE,SAAA,cAAiB;IAIfF,EADF,CAAAC,cAAA,eAAqD,eACsB;IAAAD,EAAA,CAAAgB,MAAA,gCAAI;IAAAhB,EAAA,CAAAG,YAAA,EAAM;IACnFH,EAAA,CAAAC,cAAA,eAAkC;IAgBhCD,EAfA,CAAAS,UAAA,KAAAqF,6EAAA,oBAAgE,KAAAC,oFAAA,2BAME,KAAAC,mFAAA,gCAAAhG,EAAA,CAAAuE,sBAAA,CASvC;IAI/BvE,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAS,UAAA,KAAAwF,2EAAA,kBAAgG;IAkBlGjG,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IA3IUH,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAC,WAAA,IAAAD,cAAA,CAAAC,WAAA,CAAAG,MAAA,KAAmE;IAanEf,EAAA,CAAAI,SAAA,EAAsE;IAAtEJ,EAAA,CAAAK,UAAA,UAAAM,cAAA,CAAAC,WAAA,IAAAD,cAAA,CAAAC,WAAA,CAAAG,MAAA,OAAsE;IASnEf,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,UAAA,uBAAA6F,OAAA,CAA0B;IAGelG,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAmG,kBAAA,KAAAxF,cAAA,CAAAyF,KAAA,OAAAzF,cAAA,CAAA0F,KAAA,OAAA1F,cAAA,CAAA2F,SAAA,MAAsE;IACjGtG,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,UAAA,sBAAA6F,OAAA,CAAyB;IAC1ClG,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAAkE,SAAA,CAAmC;IACnC7E,EAAA,CAAAK,UAAA,cAAAiC,QAAA,GAAAT,MAAA,CAAAU,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA0C;IAIvCtC,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,UAAA,4BAAA6F,OAAA,CAA+B;IACjBlG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,UAAA,2BAAA6F,OAAA,CAA8B;IACjDlG,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAAoE,cAAA,CAAwC;IACxC/E,EAAA,CAAAK,UAAA,aAAAM,cAAA,CAAAsE,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAA3E,MAAA,CAAAU,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,UAAqF;IAGhFxG,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,UAAA,oBAAA6F,OAAA,CAAuB;IACElG,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,UAAA,mBAAA6F,OAAA,CAAsB;IAAClG,EAAA,CAAAoD,gBAAA,YAAAzC,cAAA,CAAAsE,eAAA,CAAyC;IAE9FjF,EAAA,CAAAK,UAAA,cAAAoG,QAAA,GAAA5E,MAAA,CAAAU,YAAA,CAAAC,OAAA,cAAAiE,QAAA,KAAAhE,SAAA,GAAAgE,QAAA,SAA0C;IACdzG,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAK,UAAA,YAAAwB,MAAA,CAAA6E,cAAA,CAAiB;IAaP1G,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,UAAA,aAAAwB,MAAA,CAAAU,YAAA,CAAAC,OAAA,CAAiC;IAQvExC,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAiC,YAAA,IAAAjC,cAAA,CAAAiC,YAAA,CAAA7B,MAAA,KAAqE;IAexEf,EAAA,CAAAI,SAAA,EAAwG;IAAxGJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAkC,cAAA,MAAAlC,cAAA,CAAAiC,YAAA,IAAAjC,cAAA,CAAAiC,YAAA,CAAA7B,MAAA,QAAwG;IAMxGf,EAAA,CAAAI,SAAA,EAAyG;IAAzGJ,EAAA,CAAAK,UAAA,UAAAM,cAAA,CAAAkC,cAAA,MAAAlC,cAAA,CAAAiC,YAAA,IAAAjC,cAAA,CAAAiC,YAAA,CAAA7B,MAAA,QAAyG;IAe1Ef,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,UAAA,SAAAwB,MAAA,CAAA+B,aAAA,CAAA7C,MAAA,CAA0B;IAM/Cf,EAAA,CAAAI,SAAA,EAAgC;IAAAJ,EAAhC,CAAAK,UAAA,SAAAwB,MAAA,CAAA+B,aAAA,CAAA7C,MAAA,KAAgC,aAAA4F,gBAAA,CAAiB;IAgBnB3G,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,UAAA,SAAAM,cAAA,CAAAsE,eAAA,CAAAsB,KAAA,OAA6C;;;AD5FxG,OAAM,MAAOK,4CAA6C,SAAQ/G,aAAa;EAC7EgH,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAAhB,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAEpF,KAAK,EAAE;KAClB,EACD;MACEoF,KAAK,EAAE,CAAC;MAAEpF,KAAK,EAAE;KAClB,EAAE;MACDoF,KAAK,EAAE,CAAC;MAAEpF,KAAK,EAAE;KAClB,CACF;IACD,KAAAiD,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAuKnD,KAAA2D,KAAK,GAAY,IAAI;EApNrB;EAoBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAM9F,EAAE,GAAG6F,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAG/F,EAAE;QACrB,IAAI,IAAI,CAAC+F,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC7B,KAAU,EAAE8B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA5C,WAAWA,CAAC6C,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC5F,YAAY,CAAC7B,MAAM,GAAG,CAAC,EAAE;UACxCyH,YAAY,CAAC5F,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC5F,YAAY,CAAC4G,IAAI,CAAC;YAC7BrH,EAAE,EAAE,IAAI+G,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9G,IAAI,EAAEoG,IAAI,CAACpG,IAAI,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhH,IAAI,EAAE4G,SAAS;YACfK,SAAS,EAAE,IAAI,CAAClC,eAAe,CAACmC,gBAAgB,CAACb,IAAI,CAACpG,IAAI,CAAC;YAC3DkH,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEArE,WAAWA,CAACuH,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC5F,YAAY,CAAC7B,MAAM,EAAE;MACpCyH,YAAY,CAAC5F,YAAY,GAAG4F,YAAY,CAAC5F,YAAY,CAAC8G,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACxH,EAAE,IAAIsH,SAAS,CAAC;IAC7F;EACF;EACA1H,UAAUA,CAACwG,KAAU,EAAE7G,KAAa,EAAE8G,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC6H,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC6H,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC6H,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACnC,KAAK,GAAG,GAAG,GAAGiC,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC2H,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC6H,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC5F,YAAY,CAAClB,KAAK,CAAC,CAAC6H,KAAK,GAAGS,OAAO;EAClD;EAEA;EACAE,SAASA,CAACC,WAAgB;IACxB,IAAIA,WAAW,CAACvJ,WAAW,IAAIuJ,WAAW,CAACvJ,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;MACjEoJ,WAAW,CAACC,iBAAiB,GAAG,CAACD,WAAW,CAACC,iBAAiB,GAAG,CAAC,IAAID,WAAW,CAACvJ,WAAW,CAACG,MAAM;IACtG;EACF;EAEAsJ,SAASA,CAACF,WAAgB;IACxB,IAAIA,WAAW,CAACvJ,WAAW,IAAIuJ,WAAW,CAACvJ,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;MACjEoJ,WAAW,CAACC,iBAAiB,GAAGD,WAAW,CAACC,iBAAiB,KAAK,CAAC,GAC/DD,WAAW,CAACvJ,WAAW,CAACG,MAAM,GAAG,CAAC,GAClCoJ,WAAW,CAACC,iBAAiB,GAAG,CAAC;IACvC;EACF;EAEAE,eAAeA,CAACH,WAAgB;IAC9B,IAAIA,WAAW,CAACvJ,WAAW,IAAIuJ,WAAW,CAACvJ,WAAW,CAACG,MAAM,GAAG,CAAC,IAAIoJ,WAAW,CAACC,iBAAiB,KAAK3H,SAAS,EAAE;MAChH,OAAO0H,WAAW,CAACvJ,WAAW,CAACuJ,WAAW,CAACC,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEAjH,gBAAgBA,CAACoH,OAAgB,EAAE/B,YAAiB;IAClDA,YAAY,CAACtF,WAAW,GAAGqH,OAAO;IAClC,IAAI,CAAC3G,aAAa,CAAC4G,OAAO,CAAClC,IAAI,IAAG;MAChCE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAGiC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEA9G,6BAA6BA,CAAC8G,OAAgB,EAAEjC,IAAY,EAAEE,YAAiB;IAC7E,IAAI+B,OAAO,EAAE;MACX/B,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,GAAGiC,OAAO;MAC1C/B,YAAY,CAACtF,WAAW,GAAG,IAAI,CAACU,aAAa,CAAC6G,KAAK,CAACnC,IAAI,IAAIE,YAAY,CAAChF,aAAa,CAAC8E,IAAI,CAAC,IAAIiC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL/B,YAAY,CAACtF,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAACsG,OAAgB,EAAEjC,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACxE,kBAAkB,CAACsE,IAAI,CAAC,GAAGiC,OAAO;EACjD;EAEAG,kBAAkBA,CAACtG,kBAA4B,EAAEuG,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAIzG,kBAAkB,EAAE;MACvCwG,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAACvB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIe,WAAW,EAAE;MAC9B,IAAI1G,kBAAkB,CAAC2G,QAAQ,CAAChB,IAAI,CAAC,EAAE;QACrCa,YAAY,CAACb,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOa,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAAClC,IAAI,IAAG;MACnB,MAAM8C,GAAG,GAAG,GAAG9C,IAAI,CAAChC,SAAS,IAAIgC,IAAI,CAAClC,KAAK,IAAIkC,IAAI,CAACjC,KAAK,EAAE;MAC3D,IAAI6E,GAAG,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;QAChB,MAAME,QAAQ,GAAGJ,GAAG,CAACjD,GAAG,CAACmD,GAAG,CAAE;QAC9BE,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLL,GAAG,CAACM,GAAG,CAACJ,GAAG,EAAE;UAAE9C,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEiD,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACR,GAAG,CAACS,MAAM,EAAE,CAAC,CAACT,GAAG,CAAC,CAAC;MAAE5C,IAAI;MAAEiD;IAAK,CAAE,MAAM;MACxD,GAAGjD,IAAI;MACPsD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAACvE,gBAAgB,CAACwE,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC9D,WAAW;QAC9B+D,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLvM,GAAG,CAACwM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACzI,aAAa,CAAC4G,OAAO,CAAClC,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAClE,kBAAkB,CAACoG,OAAO,CAAClC,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAACgE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAClB,GAAG,CAAEqB,CAAM,IAAI;UACnD,OAAO;YACL1J,cAAc,EAAE,IAAI;YACpB2J,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbnG,SAAS,EAAEiG,CAAC,CAACjG,SAAS;YACtBF,KAAK,EAAEmG,CAAC,CAACnG,KAAK;YACdC,KAAK,EAAEkG,CAAC,CAAClG,KAAK;YACdxB,SAAS,EAAE,GAAG0H,CAAC,CAACnG,KAAK,IAAImG,CAAC,CAAClG,KAAK,IAAIkG,CAAC,CAACjG,SAAS,EAAE;YACjDqE,WAAW,EAAE,IAAI;YACjBiB,YAAY,EAAE,CAAC;YACf7G,cAAc,EAAE,CAAC;YACjB2H,OAAO,EAAE,CAAC;YACVlJ,aAAa,EAAE,EAAE;YACjBQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAChBqC,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACvC9F,WAAW,EAAE2L,CAAC,CAACI,QAAQ,GAAG,CAACJ,CAAC,CAACI,QAAQ,CAAC,GAAG;WAC1C;QACH,CAAC,CAAC;QACF,IAAI,CAACL,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACtB,UAAU,CAAC,IAAI,CAACsB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACxE,SAAS,EAAE;EACf;EAKA8E,eAAeA,CAAA;IACb,IAAI,CAAC3F,gBAAgB,CAAC4F,mCAAmC,CAAC;MACxDd,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC9D,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DqF,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACLvM,GAAG,CAACwM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC9J,YAAY,GAAG4J,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACzE,KAAK,GAAGwE,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UACzB,IAAI,CAACnJ,aAAa,CAAC4G,OAAO,CAAClC,IAAI,IAAI,IAAI,CAAC9E,aAAa,CAAC8E,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAClE,kBAAkB,CAACoG,OAAO,CAAClC,IAAI,IAAI,IAAI,CAACtE,kBAAkB,CAACsE,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAACgE,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC7B,GAAG,CAAEqB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAClK,YAAY,CAACkK,OAAO;cAClC5J,cAAc,EAAE0J,CAAC,CAAC1J,cAAc;cAChCjC,WAAW,EAAE2L,CAAC,CAAC3L,WAAW,KAAK2L,CAAC,CAACS,gBAAgB,GAAG,CAACT,CAAC,CAACS,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EzD,KAAK,EAAEgD,CAAC,CAAChD,KAAK;cACdiD,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1B3G,SAAS,EAAEiG,CAAC,CAACjG,SAAS;cACtBF,KAAK,EAAEmG,CAAC,CAACnG,KAAK;cACdC,KAAK,EAAEkG,CAAC,CAAClG,KAAK;cACdxB,SAAS,EAAE0H,CAAC,CAAC1H,SAAS,GAAG0H,CAAC,CAAC1H,SAAS,GAAG,GAAG0H,CAAC,CAACnG,KAAK,IAAImG,CAAC,CAAClG,KAAK,IAAIkG,CAAC,CAACjG,SAAS,EAAE;cAC7EqE,WAAW,EAAE4B,CAAC,CAAC5B,WAAW;cAC1BiB,YAAY,EAAEW,CAAC,CAACX,YAAY;cAC5B7G,cAAc,EAAEwH,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAACxH,cAAc;cACtD2H,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClBlJ,aAAa,EAAE+I,CAAC,CAACW,qBAAqB,CAACnM,MAAM,GAAG,IAAI,CAACoM,0BAA0B,CAAC,IAAI,CAACvJ,aAAa,EAAE2I,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1J;cAAa,CAAE;cACxJQ,kBAAkB,EAAEuI,CAAC,CAAC5B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACtG,kBAAkB,EAAEmI,CAAC,CAAC5B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC3G;cAAkB,CAAE;cACpId,WAAW,EAAEqJ,CAAC,CAACW,qBAAqB,CAACnM,MAAM,KAAK,IAAI,CAAC6C,aAAa,CAAC7C,MAAM;cACzE6B,YAAY,EAAE,EAAE;cAChBqC,eAAe,EAAEsH,CAAC,CAACG,OAAO,GAAG,IAAI,CAACtE,cAAc,CAACmE,CAAC,CAACG,OAAO,EAAE,IAAI,CAAChG,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACmF,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC/D,SAAS,EAAE;EACf;EAEA3C,mBAAmBA,CAACgF,WAAgB;IAClC,IAAIA,WAAW,CAAClF,eAAe,IAAIkF,WAAW,CAAClF,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E4D,WAAW,CAACpF,cAAc,GAAG,CAAC;IAChC;EACF;EACAqI,4BAA4BA,CAAChL,IAAW;IACtC,KAAK,IAAIkG,IAAI,IAAIlG,IAAI,EAAE;MACrB,IAAIkG,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOY,IAAI,CAAC+E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC7D,MAAM,CAAC0B,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC;EACjD;EAEAsC,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB7D,MAAM,CAAC0B,GAAG,IAAImC,GAAG,CAACnC,GAAG,CAAC,CAAC,CACvBuC,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAC3I,eAAoB,EAAEjB,kBAAuB;IAC1D,IAAIiB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACmH,0BAA0B,CAAC1J,kBAAkB,CAAC;IAC5D;EACF;EAEA6J,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAC1E,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI2E,KAAK,CAAChN,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOgN,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACpL,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAAC7B,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLkN,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAACjL,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpE8L,aAAa,EAAEtL,YAAY,CAAC,CAAC,CAAC,CAACyG,SAAS,IAAI,IAAI;QAChD8E,QAAQ,EAAEvL,YAAY,CAAC,CAAC,CAAC,CAAC2G,KAAK,CAAClH,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGA2L,UAAUA,CAAA;IACR,IAAI,CAAChH,KAAK,CAACiH,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMlG,IAAI,IAAI,IAAI,CAACmG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAChG,IAAI,CAACoE,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACjG,IAAI,CAACvD,cAAe,EAAE;QACvDwJ,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIjG,IAAI,CAACsD,YAAY,IAAItD,IAAI,CAACvD,cAAc,EAAE;QAC5C,IAAIuD,IAAI,CAACvD,cAAc,GAAGuD,IAAI,CAACsD,YAAY,IAAItD,IAAI,CAACvD,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACqC,KAAK,CAACsH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGpG,IAAI,CAACsD,YAAY,GAAG,KAAKtD,IAAI,CAACzD,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAAC2J,kBAAkB,IAAK,CAAClG,IAAI,CAACzD,SAAU,EAAE;QAC5C2J,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAClH,KAAK,CAACsH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACnH,KAAK,CAACsH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACpH,KAAK,CAACsH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAACpB,GAAG,CAAE0D,CAAM,IAAI;MAChE,OAAO;QACL/L,cAAc,EAAE+L,CAAC,CAAC/L,cAAc,GAAG+L,CAAC,CAAC/L,cAAc,GAAG,IAAI;QAC1D0G,KAAK,EAAEqF,CAAC,CAAChM,YAAY,GAAG,IAAI,CAACoL,UAAU,CAACY,CAAC,CAAChM,YAAY,CAAC,GAAGH,SAAS;QACnE+J,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAACpL,aAAa,CAAC;QAC9DyJ,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAAClH,KAAK,GAAG,IAAI,GAAG,IAAI,CAACpF,YAAY,CAACkK,OAAO;QACtDrG,KAAK,EAAEwI,CAAC,CAACxI,KAAK;QACdC,KAAK,EAAEuI,CAAC,CAACvI,KAAK;QACdC,SAAS,EAAEsI,CAAC,CAACtI,SAAS;QACtBzB,SAAS,EAAE+J,CAAC,CAAC/J,SAAS;QAAE;QACxB8F,WAAW,EAAEiE,CAAC,CAAC3J,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACqH,cAAc,CAACgB,CAAC,CAAC3J,eAAe,EAAE2J,CAAC,CAAC5K,kBAAkB,CAAC,IAAI,IAAI;QACxH4H,YAAY,EAAEgD,CAAC,CAAChD,YAAY;QAC5B7G,cAAc,EAAE6J,CAAC,CAAC7J,cAAc;QAChC2H,OAAO,EAAEkC,CAAC,CAAC3J,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAAC6H,UAAU,EAAE;IACjB,IAAI,IAAI,CAAChH,KAAK,CAAC0H,aAAa,CAAC/N,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACiG,OAAO,CAAC+H,aAAa,CAAC,IAAI,CAAC3H,KAAK,CAAC0H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACnH,KAAK,EAAE;MACd,IAAI,CAACqH,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAChI,gBAAgB,CAACiI,oCAAoC,CAAC;MACzDnD,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAAC3G,SAAS,CAACqE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACrF,OAAO,CAACmI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAAC9D,WAAW;MAC9BoH,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3ChH,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACsI,sCAAsC,CAAC;MAC3DxD,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAACvH,SAAS,CAACqE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACrF,OAAO,CAACmI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAjC,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMpH,IAAI,IAAIkH,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKxH,IAAI,IAAIuH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAACpH,IAAI,CAAC,GAAG,CAAC,CAACqH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFvH,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAAC8I,8DAA8D,CAAC;MAC5FjE,IAAI,EAAE,IAAI,CAAC7D;KACZ,CAAC,CAACgE,IAAI,CACLvM,GAAG,CAACwM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACzI,aAAa,GAAG,IAAI,CAACwJ,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC9E,SAAS,EAAE;EACf;EACAsH,MAAMA,CAAA;IACJ,IAAI,CAAC7H,aAAa,CAACiC,IAAI,CAAC;MACtByG,MAAM;MACNC,OAAO,EAAE,IAAI,CAAChI;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAAC8I,IAAI,EAAE;EACtB;;;uCAvcWvJ,4CAA4C,EAAA5G,EAAA,CAAAoQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtQ,EAAA,CAAAoQ,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxQ,EAAA,CAAAoQ,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1Q,EAAA,CAAAoQ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5Q,EAAA,CAAAoQ,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA7Q,EAAA,CAAAoQ,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA/Q,EAAA,CAAAoQ,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAjR,EAAA,CAAAoQ,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAnR,EAAA,CAAAoQ,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAApR,EAAA,CAAAoQ,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5C1K,4CAA4C;MAAA2K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzR,EAAA,CAAA0R,0BAAA,EAAA1R,EAAA,CAAA2R,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/CvDjS,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAE,SAAA,qBAAiC;UACnCF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,sBAA2B;UACzBD,EAAA,CAAAE,SAAA,YAA+C;UAE7CF,EADF,CAAAC,cAAA,aAAuB,YAC4B;UAAAD,EAAA,CAAAgB,MAAA,4CAAO;UAAAhB,EAAA,CAAAG,YAAA,EAAK;UAE7DH,EAAA,CAAAS,UAAA,IAAA0R,oEAAA,4BAA8E;UA2JlFnS,EADE,CAAAG,YAAA,EAAM,EACO;UAEbH,EADF,CAAAC,cAAA,wBAAyF,kBACc;UAA9DD,EAAA,CAAAoB,UAAA,mBAAAgR,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvDpP,EAAA,CAAAgB,MAAA,sBACF;UAAAhB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgG;UAAhED,EAAA,CAAAoB,UAAA,mBAAAiR,+EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAClD3O,EAAA,CAAAgB,MAAA,sBACF;UAEJhB,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;UApKkCH,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAK,UAAA,YAAA6R,GAAA,CAAA5F,kBAAA,CAAuB;UA6JLtM,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAK,UAAA,cAAAiS,OAAA,GAAAJ,GAAA,CAAA3P,YAAA,CAAAC,OAAA,cAAA8P,OAAA,KAAA7P,SAAA,GAAA6P,OAAA,SAA0C;UAG/CtS,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAK,UAAA,cAAAkS,OAAA,GAAAL,GAAA,CAAA3P,YAAA,CAAAC,OAAA,cAAA+P,OAAA,KAAA9P,SAAA,GAAA8P,OAAA,SAA0C;;;qBD5HvF9S,YAAY,EAAAyR,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,IAAA,EAAE7S,YAAY,EAAA8S,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAEjU,gBAAgB,EAAgBK,eAAe;MAAA6T,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}