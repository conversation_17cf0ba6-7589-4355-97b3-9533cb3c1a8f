{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemGetBuildingSampleSelectionPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetBuildingSampleSelectionPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemGetBuildingSampleSelectionPost$Plain.PATH = '/api/RegularChangeItem/GetBuildingSampleSelection';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemGetBuildingSampleSelectionPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-get-building-sample-selection-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetBuildingSampleSelectionResResponseBase } from '../../models/get-building-sample-selection-res-response-base';\r\n\r\nexport interface ApiRegularChangeItemGetBuildingSampleSelectionPost$Plain$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiRegularChangeItemGetBuildingSampleSelectionPost$Plain(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemGetBuildingSampleSelectionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetBuildingSampleSelectionResResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetBuildingSampleSelectionPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetBuildingSampleSelectionResResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemGetBuildingSampleSelectionPost$Plain.PATH = '/api/RegularChangeItem/GetBuildingSampleSelection';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,wDAAwDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAwE,EAAEC,OAAqB;EACzM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,wDAAwD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC7G,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAkE;EAC3E,CAAC,CAAC,CACH;AACH;AAEAb,wDAAwD,CAACM,IAAI,GAAG,mDAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}