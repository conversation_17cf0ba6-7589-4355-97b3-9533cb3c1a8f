{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISO\n * @category Common Helpers\n * @summary Format the date according to the ISO 8601 standard (https://support.sas.com/documentation/cdl/en/lrdict/64316/HTML/default/viewer.htm#a003169814.htm).\n *\n * @description\n * Return the formatted date string in ISO 8601 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {'extended'|'basic'} [options.format='extended'] - if 'basic', hide delimiters between date and time values.\n * @param {'complete'|'date'|'time'} [options.representation='complete'] - format date, time with local time zone, or both.\n * @returns {String} the formatted date string (in local time zone)\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.format` must be 'extended' or 'basic'\n * @throws {RangeError} `options.representation` must be 'date', 'time' or 'complete'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601, short format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918T190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, date only:\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, time only (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52Z'\n */\nexport default function formatISO(date, options) {\n  var _options$format, _options$representati;\n  requiredArgs(1, arguments);\n  var originalDate = toDate(date);\n  if (isNaN(originalDate.getTime())) {\n    throw new RangeError('Invalid time value');\n  }\n  var format = String((_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : 'extended');\n  var representation = String((_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : 'complete');\n  if (format !== 'extended' && format !== 'basic') {\n    throw new RangeError(\"format must be 'extended' or 'basic'\");\n  }\n  if (representation !== 'date' && representation !== 'time' && representation !== 'complete') {\n    throw new RangeError(\"representation must be 'date', 'time', or 'complete'\");\n  }\n  var result = '';\n  var tzOffset = '';\n  var dateDelimiter = format === 'extended' ? '-' : '';\n  var timeDelimiter = format === 'extended' ? ':' : '';\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== 'time') {\n    var day = addLeadingZeros(originalDate.getDate(), 2);\n    var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n    var year = addLeadingZeros(originalDate.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = \"\".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== 'date') {\n    // Add the timezone.\n    var offset = originalDate.getTimezoneOffset();\n    if (offset !== 0) {\n      var absoluteOffset = Math.abs(offset);\n      var hourOffset = addLeadingZeros(Math.floor(absoluteOffset / 60), 2);\n      var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n      // If less than 0, the sign is +, because it is ahead of time.\n      var sign = offset < 0 ? '+' : '-';\n      tzOffset = \"\".concat(sign).concat(hourOffset, \":\").concat(minuteOffset);\n    } else {\n      tzOffset = 'Z';\n    }\n    var hour = addLeadingZeros(originalDate.getHours(), 2);\n    var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n    var second = addLeadingZeros(originalDate.getSeconds(), 2);\n\n    // If there's also date, separate it with time with 'T'\n    var separator = result === '' ? '' : 'T';\n\n    // Creates a time string consisting of hour, minute, and second, separated by delimiters, if defined.\n    var time = [hour, minute, second].join(timeDelimiter);\n\n    // HHmmss or HH:mm:ss.\n    result = \"\".concat(result).concat(separator).concat(time).concat(tzOffset);\n  }\n  return result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}