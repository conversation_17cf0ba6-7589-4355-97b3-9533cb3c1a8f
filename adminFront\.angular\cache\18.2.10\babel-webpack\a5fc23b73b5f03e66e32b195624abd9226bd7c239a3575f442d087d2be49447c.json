{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nlet RequirementManagementComponent = class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    // 模板資料\n    this.templateList = [{\n      TemplateID: 1,\n      TemplateName: '模板A',\n      Description: '範例模板A'\n    }, {\n      TemplateID: 2,\n      TemplateName: '模板B',\n      Description: '範例模板B'\n    }];\n    this.templateDetailList = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目C'\n    }];\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onAddTemplate() {\n    // 新增模板邏輯\n    alert('新增模板功能');\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n    alert('查看模板: ' + tpl.TemplateName);\n  }\n  onSaveTemplate(e) {\n    // 實際應呼叫API，這裡先本地模擬\n    const newId = Math.max(...this.templateList.map(t => t.TemplateID || 0), 0) + 1;\n    const tpl = {\n      ...e.template,\n      TemplateID: newId\n    };\n    this.templateList.push(tpl);\n    e.details.forEach((d, idx) => {\n      this.templateDetailList.push({\n        ...d,\n        TemplateDetailID: this.templateDetailList.length + idx + 1,\n        TemplateID: newId\n      });\n    });\n  }\n  onDeleteTemplate(templateID) {\n    this.templateList = this.templateList.filter(t => t.TemplateID !== templateID);\n    this.templateDetailList = this.templateDetailList.filter(d => d.TemplateID !== templateID);\n  }\n};\nRequirementManagementComponent = __decorate([Component({\n  selector: 'app-requirement-management',\n  standalone: true,\n  imports: [NbCardModule, BreadcrumbComponent, NbInputModule, FormsModule, NbSelectModule, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, NbDialogModule, TemplateViewerComponent],\n  templateUrl: './requirement-management.component.html',\n  styleUrl: './requirement-management.component.scss'\n})], RequirementManagementComponent);\nexport { RequirementManagementComponent };", "map": {"version": 3, "names": ["Component", "NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getListRequirementRequest", "getRequirementRequest", "buildCaseList", "requirementList", "saveRequirement", "CHouseType", "statusOptions", "value", "label", "houseType", "getEnumOptions", "isNew", "currentBuildCase", "currentTab", "templateList", "TemplateID", "TemplateName", "Description", "templateDetailList", "TemplateDetailID", "RefID", "ModuleType", "FieldName", "FieldValue", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "CStatus", "CIsShow", "CRequirement", "CGroupName", "map", "type", "resetSearch", "length", "setTimeout", "CBuildCaseID", "cID", "getList", "getHouseType", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "CSort", "CUnitPrice", "CUnit", "errorMessages", "CRemark", "add", "dialog", "open", "onEdit", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "save", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "close", "onDelete", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "getCIsShowText", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "saveTemplate", "templateData", "openTemplateViewer", "templateViewerDialog", "onAddTemplate", "alert", "onSelectTemplate", "tpl", "onSaveTemplate", "e", "newId", "Math", "max", "t", "template", "details", "d", "idx", "onDeleteTemplate", "templateID", "__decorate", "selector", "standalone", "imports", "NbDialogModule", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    NbDialogModule,\r\n    TemplateViewerComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  // 模板資料\r\n  templateList: Template[] = [\r\n    { TemplateID: 1, TemplateName: '模板A', Description: '範例模板A' },\r\n    { TemplateID: 2, TemplateName: '模板B', Description: '範例模板B' }\r\n  ];\r\n  templateDetailList: TemplateDetail[] = [\r\n    { TemplateDetailID: 1, TemplateID: 1, RefID: 101, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目A' },\r\n    { TemplateDetailID: 2, TemplateID: 1, RefID: 102, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目B' },\r\n    { TemplateDetailID: 3, TemplateID: 2, RefID: 201, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目C' }\r\n  ];\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n  onAddTemplate() {\r\n    // 新增模板邏輯\r\n    alert('新增模板功能');\r\n  }\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n    alert('查看模板: ' + tpl.TemplateName);\r\n  }\r\n  onSaveTemplate(e: { template: Template, details: TemplateDetail[] }) {\r\n    // 實際應呼叫API，這裡先本地模擬\r\n    const newId = Math.max(...this.templateList.map(t => t.TemplateID || 0), 0) + 1;\r\n    const tpl = { ...e.template, TemplateID: newId };\r\n    this.templateList.push(tpl);\r\n    e.details.forEach((d, idx) => {\r\n      this.templateDetailList.push({\r\n        ...d,\r\n        TemplateDetailID: this.templateDetailList.length + idx + 1,\r\n        TemplateID: newId\r\n      });\r\n    });\r\n  }\r\n  onDeleteTemplate(templateID: number) {\r\n    this.templateList = this.templateList.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetailList = this.templateDetailList.filter(d => d.TemplateID !== templateID);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAyC,eAAe;AAE1E,SAASC,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;AAyBhI,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQZ,aAAa;EAC/Da,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAAC,yBAAyB,GAAG,EAA8D;IAC1F,KAAAC,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,eAAe,GAAgD;MAAEC,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAAC,SAAS,GAAG,IAAI,CAAClB,UAAU,CAACmB,cAAc,CAACxB,aAAa,CAAC;IACzD,KAAAyB,KAAK,GAAG,KAAK;IACb,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAEhB;IACA,KAAAC,YAAY,GAAe,CACzB;MAAEC,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,EAC5D;MAAEF,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAO,CAAE,CAC7D;IACD,KAAAC,kBAAkB,GAAqB,CACrC;MAAEC,gBAAgB,EAAE,CAAC;MAAEJ,UAAU,EAAE,CAAC;MAAEK,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAEJ,gBAAgB,EAAE,CAAC;MAAEJ,UAAU,EAAE,CAAC;MAAEK,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAEJ,gBAAgB,EAAE,CAAC;MAAEJ,UAAU,EAAE,CAAC;MAAEK,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,CAC9H;IA4OD;IACQ,KAAAC,gBAAgB,GAAG,IAAI;IA1Q7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EA6BSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACzB,yBAAyB,CAAC4B,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAAC5B,yBAAyB,CAAC6B,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC7B,yBAAyB,CAAC8B,YAAY,GAAG,EAAE;IAChD,IAAI,CAAC9B,yBAAyB,CAAC+B,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAAC/B,yBAAyB,CAACK,UAAU,GAAG,IAAI,CAACI,SAAS,CAACuB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC1B,KAAK,CAAC;EACpF;EAEA;EACA2B,WAAWA,CAAA;IACT,IAAI,CAACT,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACvB,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiC,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACvB,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACb,yBAAyB,CAACqC,YAAY,GAAG,IAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,CAACoC,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAACtC,yBAAyB,CAACqC,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACE,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAC,YAAYA,CAACC,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACtC,SAAS,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1C,KAAK,IAAIuC,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACvC,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOoC,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAACxC,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACnB,KAAK,CAAC4D,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClD,eAAe,CAACiC,YAAY,CAAC;IAClE;IAEA,IAAI,CAAC3C,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAAC0B,YAAY,CAAC;IAC9D,IAAI,CAACpC,KAAK,CAAC4D,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAClD,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACmD,KAAK,CAAC;IACvD,IAAI,CAAC7D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACwB,OAAO,CAAC;IACzD,IAAI,CAAClC,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACoD,UAAU,CAAC;IAC5D,IAAI,CAAC9D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACqD,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACrD,eAAe,CAAC2B,UAAU,IAAI,IAAI,CAAC3B,eAAe,CAAC2B,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACzC,KAAK,CAACgE,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC9C,eAAe,CAACuD,OAAO,IAAI,IAAI,CAACvD,eAAe,CAACuD,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACzC,KAAK,CAACgE,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEAU,GAAGA,CAACC,MAAwB;IAC1B,IAAI,CAAClD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEwB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACzB,eAAe,CAACwB,OAAO,GAAG,CAAC;IAChC,IAAI,CAACxB,eAAe,CAACoD,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC3C,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAACD,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACR,eAAe,CAACiC,YAAY,GAAG,IAAI,CAACzB,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiC,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAAC/B,eAAe,CAACiC,YAAY,GAAG,IAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,CAACoC,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAAClC,eAAe,CAACiC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC7C,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEME,MAAMA,CAACC,IAAoB,EAAEH,MAAwB;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAAChE,qBAAqB,CAACkE,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACtD,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMsD,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAACzE,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAG,IAAIA,CAACC,GAAQ;IACX,IAAI,CAACrB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1D,KAAK,CAACgE,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC1C,OAAO,CAACiF,aAAa,CAAC,IAAI,CAAChF,KAAK,CAACgE,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAAC7C,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACT,eAAe,CAACiC,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACzC,kBAAkB,CAAC+E,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACxE;KACZ,CAAC,CAACyE,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtF,OAAO,CAACuF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC9C,OAAO,CAACwF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,QAAQA,CAACpB,IAAoB;IAC3B,IAAI,CAAC5D,eAAe,CAAC+D,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACxD,KAAK,GAAG,KAAK;IAClB,IAAI0E,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAC3F,kBAAkB,CAAC4F,iCAAiC,CAAC;MACxDZ,IAAI,EAAE;QACJT,cAAc,EAAE,IAAI,CAAC/D,eAAe,CAAC+D;;KAExC,CAAC,CAACU,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACrF,OAAO,CAACuF,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACzC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAb,gBAAgBA,CAAA;IACd,IAAI,CAAC/B,gBAAgB,CAAC8F,qCAAqC,CAAC;MAAEb,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEc,IAAI,CAACjH,kBAAkB,CAAC,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC8E,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC5E,aAAa,GAAG4E,GAAG,CAACa,OAAQ;MACjC;MACA,IAAI,IAAI,CAAC9E,UAAU,KAAK,CAAC,IAAI,IAAI,CAACX,aAAa,CAACiC,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAACnC,yBAAyB,CAACqC,YAAY,GAAG,IAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,CAACoC,GAAG;QACvE,IAAI,CAACC,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAC1B,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACb,yBAAyB,CAACqC,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACvC,yBAAyB,CAAC4F,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC7F,yBAAyB,CAAC8F,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAC5F,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAAC6F,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAACnF,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACb,yBAAyB,CAACqC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACrC,yBAAyB,CAACqC,YAAY,IAAI,IAAI,CAACrC,yBAAyB,CAACqC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACzB,gBAAgB,GAAG,IAAI,CAACZ,yBAAyB,CAACqC,YAAY;MACrE;IACF;IAEA,IAAI,CAACzC,kBAAkB,CAACqG,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAAC5E;IAAyB,CAAE,CAAC,CAC7F0F,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAACxF,eAAe,GAAG2E,GAAG,CAACa,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGlB,GAAG,CAACoB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE9B,OAAOA,CAAA;IACP,IAAI,CAACxE,kBAAkB,CAACuG,8BAA8B,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAAC3E;IAAqB,CAAE,CAAC,CACzFyF,IAAI,EAAE,CACNb,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACa,OAAO,EAAE;UACf,IAAI,CAACvF,eAAe,GAAG;YAAEC,UAAU,EAAE,EAAE;YAAEwB,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACzB,eAAe,CAACiC,YAAY,GAAGyC,GAAG,CAACa,OAAO,CAACtD,YAAY;UAC5D,IAAI,CAACjC,eAAe,CAAC2B,UAAU,GAAG+C,GAAG,CAACa,OAAO,CAAC5D,UAAU;UACxD,IAAI,CAAC3B,eAAe,CAACC,UAAU,GAAGyE,GAAG,CAACa,OAAO,CAACtF,UAAU,GAAIqC,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACa,OAAO,CAACtF,UAAU,CAAC,GAAGyE,GAAG,CAACa,OAAO,CAACtF,UAAU,GAAG,CAACyE,GAAG,CAACa,OAAO,CAACtF,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACD,eAAe,CAACuD,OAAO,GAAGmB,GAAG,CAACa,OAAO,CAAChC,OAAO;UAClD,IAAI,CAACvD,eAAe,CAAC0B,YAAY,GAAGgD,GAAG,CAACa,OAAO,CAAC7D,YAAY;UAC5D,IAAI,CAAC1B,eAAe,CAAC+D,cAAc,GAAGW,GAAG,CAACa,OAAO,CAACxB,cAAc;UAChE,IAAI,CAAC/D,eAAe,CAACmD,KAAK,GAAGuB,GAAG,CAACa,OAAO,CAACpC,KAAK;UAC9C,IAAI,CAACnD,eAAe,CAACwB,OAAO,GAAGkD,GAAG,CAACa,OAAO,CAAC/D,OAAO;UAClD,IAAI,CAACxB,eAAe,CAACoD,UAAU,GAAGsB,GAAG,CAACa,OAAO,CAACnC,UAAU;UACxD,IAAI,CAACpD,eAAe,CAACqD,KAAK,GAAGqB,GAAG,CAACa,OAAO,CAAClC,KAAK;UAC9C;UACA,IAAI,CAACrD,eAAe,CAACyB,OAAO,GAAIiD,GAAG,CAACa,OAAe,CAAC9D,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEAuE,iBAAiBA,CAAC7F,KAAa,EAAE8F,OAAY;IAC3C/B,OAAO,CAACC,GAAG,CAAC8B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAACjG,eAAe,CAACC,UAAU,EAAEiG,QAAQ,CAAC/F,KAAK,CAAC,EAAE;QACrD,IAAI,CAACH,eAAe,CAACC,UAAU,EAAE6C,IAAI,CAAC3C,KAAK,CAAC;MAC9C;MACA+D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnE,eAAe,CAACC,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACD,eAAe,CAACC,UAAU,GAAG,IAAI,CAACD,eAAe,CAACC,UAAU,EAAEkG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKjG,KAAK,CAAC;IAC7F;EACF;EAEAkG,cAAcA,CAACzC,IAAS;IACtB,OAAOA,IAAI,CAACnC,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAIA6E,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAACnF,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAImF,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAAC/F,UAAU,GAAG,CAAC;MACnB,IAAI,CAACb,yBAAyB,CAACqC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACxB,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAACX,aAAa,IAAI,IAAI,CAACA,aAAa,CAACiC,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAACnC,yBAAyB,CAACqC,YAAY,GAAG,IAAI,CAACnC,aAAa,CAAC,CAAC,CAAC,CAACoC,GAAG;MACzE;IACF;IACA,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACAsE,WAAWA,CAAChD,MAAwB;IAClC,IAAI,CAAClD,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,eAAe,GAAG;MAAEC,UAAU,EAAE,EAAE;MAAEwB,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACzB,eAAe,CAACwB,OAAO,GAAG,CAAC;IAChC,IAAI,CAACxB,eAAe,CAACoD,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAACpD,eAAe,CAACiC,YAAY,GAAG,CAAC;IACrC,IAAI,CAAC7C,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACMiD,cAAcA,CAAC9C,IAAoB,EAAEH,MAAwB;IAAA,IAAAkD,MAAA;IAAA,OAAA7C,iBAAA;MACjE6C,MAAI,CAAC9G,qBAAqB,CAACkE,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChE4C,MAAI,CAACpG,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMoG,MAAI,CAAC3C,OAAO,EAAE;QACpB2C,MAAI,CAACvH,aAAa,CAACsE,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA2C,YAAYA,CAACvC,GAAQ;IACnB;IACA,IAAI,CAAC/E,KAAK,CAAC2D,KAAK,EAAE;IAClB,IAAI,CAAC3D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAAC0B,YAAY,CAAC;IAC9D,IAAI,CAACpC,KAAK,CAAC4D,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAClD,eAAe,CAACC,UAAU,CAAC;IAC7D,IAAI,CAACX,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACmD,KAAK,CAAC;IACvD,IAAI,CAAC7D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACwB,OAAO,CAAC;IACzD,IAAI,CAAClC,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACoD,UAAU,CAAC;IAC5D,IAAI,CAAC9D,KAAK,CAAC4D,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClD,eAAe,CAACqD,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACrD,eAAe,CAAC2B,UAAU,IAAI,IAAI,CAAC3B,eAAe,CAAC2B,UAAU,CAACI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACzC,KAAK,CAACgE,aAAa,CAACR,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAAC9C,eAAe,CAACuD,OAAO,IAAI,IAAI,CAACvD,eAAe,CAACuD,OAAO,CAACxB,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACzC,KAAK,CAACgE,aAAa,CAACR,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAACxD,KAAK,CAACgE,aAAa,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC1C,OAAO,CAACiF,aAAa,CAAC,IAAI,CAAChF,KAAK,CAACgE,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMuD,YAAY,GAAG;MAAE,GAAG,IAAI,CAAC7G;IAAe,CAAE;IAChD6G,YAAY,CAAC5E,YAAY,GAAG,CAAC;IAE7B,IAAI,CAACzC,kBAAkB,CAAC+E,+BAA+B,CAAC;MACtDC,IAAI,EAAEqC;KACP,CAAC,CAACpC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACtF,OAAO,CAACuF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACzC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC9C,OAAO,CAACwF,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACU,KAAK,EAAE;EACb;EAEA+B,kBAAkBA,CAACC,oBAAsC;IACvD,IAAI,CAAC3H,aAAa,CAACsE,IAAI,CAACqD,oBAAoB,CAAC;EAC/C;EACAC,aAAaA,CAAA;IACX;IACAC,KAAK,CAAC,QAAQ,CAAC;EACjB;EACAC,gBAAgBA,CAACC,GAAa;IAC5B;IACAF,KAAK,CAAC,QAAQ,GAAGE,GAAG,CAACvG,YAAY,CAAC;EACpC;EACAwG,cAAcA,CAACC,CAAoD;IACjE;IACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAAC9G,YAAY,CAACkB,GAAG,CAAC6F,CAAC,IAAIA,CAAC,CAAC9G,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAC/E,MAAMwG,GAAG,GAAG;MAAE,GAAGE,CAAC,CAACK,QAAQ;MAAE/G,UAAU,EAAE2G;IAAK,CAAE;IAChD,IAAI,CAAC5G,YAAY,CAACoC,IAAI,CAACqE,GAAG,CAAC;IAC3BE,CAAC,CAACM,OAAO,CAAClF,OAAO,CAAC,CAACmF,CAAC,EAAEC,GAAG,KAAI;MAC3B,IAAI,CAAC/G,kBAAkB,CAACgC,IAAI,CAAC;QAC3B,GAAG8E,CAAC;QACJ7G,gBAAgB,EAAE,IAAI,CAACD,kBAAkB,CAACiB,MAAM,GAAG8F,GAAG,GAAG,CAAC;QAC1DlH,UAAU,EAAE2G;OACb,CAAC;IACJ,CAAC,CAAC;EACJ;EACAQ,gBAAgBA,CAACC,UAAkB;IACjC,IAAI,CAACrH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACyF,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC9G,UAAU,KAAKoH,UAAU,CAAC;IAC9E,IAAI,CAACjH,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqF,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACjH,UAAU,KAAKoH,UAAU,CAAC;EAC5F;CACD;AA3YY/I,8BAA8B,GAAAgJ,UAAA,EAvB1CnK,SAAS,CAAC;EACToK,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,CACzBrK,YAAY,EACZQ,mBAAmB,EACnBN,aAAa,EACbO,WAAW,EACXL,cAAc,EACdD,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAChBa,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EACdiK,cAAc,EACdrJ,uBAAuB,CACxB;EACDsJ,WAAW,EAAE,yCAAyC;EACtDC,QAAQ,EAAE;CACX,CAAC,C,EACWtJ,8BAA8B,CA2Y1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}