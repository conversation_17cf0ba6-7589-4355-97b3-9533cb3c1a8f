{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet ContentManagementLandownerComponent = class ContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    this.tempBuildCaseID = -1;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.pageSize = 20;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  toggleSwitch(CIsLock) {\n    if (CIsLock) {\n      this.unLock();\n    } else {\n      this.onLock();\n    }\n  }\n  navidateDetai() {\n    this.router.navigate([`pages/content-management-landowner/${this.cBuildCaseSelected.cID}`]);\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      // if (res.Entries && res.StatusCode == 0) {\n      //   this.userBuildCaseOptions = res.Entries.map(res => {\n      //     return {\n      //       CBuildCaseName: res.CBuildCaseName,\n      //       cID: res.cID\n      //     }\n      //   })\n      //   this.cBuildCaseSelected = this.userBuildCaseOptions[0]\n      //   if (this.cBuildCaseSelected.cID) {\n      //     this.getListFormItem()\n      //   }\n      // }\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected.cID) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n};\nContentManagementLandownerComponent = __decorate([Component({\n  selector: 'ngx-content-management-landowner',\n  templateUrl: './content-management-landowner.component.html',\n  styleUrls: ['./content-management-landowner.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule]\n})], ContentManagementLandownerComponent);\nexport { ContentManagementLandownerComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "tap", "SharedModule", "BaseComponent", "ContentManagementLandownerComponent", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "tempBuildCaseID", "buildingSelectedOptions", "value", "label", "pageSize", "typeContentManagementLandowner", "CFormType", "CNoticeType", "receive", "pipe", "res", "action", "payload", "subscribe", "toggleSwitch", "CIsLock", "unLock", "onLock", "navid<PERSON><PERSON><PERSON><PERSON>", "navigate", "cBuildCaseSelected", "cID", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "body", "CBuildCaseId", "PageIndex", "pageIndex", "PageSize", "CIsPaging", "Entries", "StatusCode", "formItems", "listFormItem", "totalRecords", "TotalItems", "ngOnInit", "getUserBuildCase", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "map", "CBuildCaseName", "index", "findIndex", "x", "pageChanged", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "Message", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\content-management-landowner.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { GetListFormItemRes } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-landowner',\r\n  templateUrl: './content-management-landowner.component.html',\r\n  styleUrls: ['./content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule],\r\n})\r\n\r\nexport class ContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  toggleSwitch(CIsLock: any) {\r\n    if(CIsLock) {\r\n      this.unLock()\r\n    } else {\r\n      this.onLock()\r\n    }\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: any\r\n  buildingSelectedOptions: any[] = [{value: '', label: '全部'}]\r\n\r\n  formItems: any\r\n  listFormItem: GetListFormItemRes\r\n  override pageSize = 20\r\n\r\n  navidateDetai() {\r\n    this.router.navigate([`pages/content-management-landowner/${this.cBuildCaseSelected.cID}`])\r\n  }\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems\r\n          this.listFormItem = res.Entries\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n\r\n  buildCaseId: number\r\n  cBuildCaseSelected: any\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        // if (res.Entries && res.StatusCode == 0) {\r\n        //   this.userBuildCaseOptions = res.Entries.map(res => {\r\n        //     return {\r\n        //       CBuildCaseName: res.CBuildCaseName,\r\n        //       cID: res.cID\r\n        //     }\r\n        //   })\r\n        //   this.cBuildCaseSelected = this.userBuildCaseOptions[0]\r\n        //   if (this.cBuildCaseSelected.cID) {\r\n        //     this.getListFormItem()\r\n        //   }\r\n        // }\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          });\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected.cID) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem()\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem()\r\n    })\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAK9C,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAiB5D,IAAMC,mCAAmC,GAAzC,MAAMA,mCAAoC,SAAQD,aAAa;EACpEE,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAoBvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IAE5B,KAAAC,uBAAuB,GAAU,CAAC;MAACC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAC,CAAC;IAIlD,KAAAC,QAAQ,GAAG,EAAE;IAKtB,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IA/BC,IAAI,CAACR,aAAa,CAACS,OAAO,EAAE,CAACC,IAAI,CAC/BpB,GAAG,CAAEqB,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACZ,eAAe,GAAGU,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,OAAY;IACvB,IAAGA,OAAO,EAAE;MACV,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,EAAE;IACf;EACF;EAUAC,aAAaA,CAAA;IACX,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,sCAAsC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,CAAC,CAAC;EAC7F;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACxB,gBAAgB,CAACyB,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACL,kBAAkB,CAACC,GAAG;QACzCf,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDoB,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAACxB,QAAQ;QACvByB,SAAS,EAAE;;KAEd,CAAC,CAACpB,IAAI,CACLpB,GAAG,CAACqB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoB,OAAO,IAAIpB,GAAG,CAACqB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,SAAS,GAAGtB,GAAG,CAACoB,OAAO,CAACE,SAAS;QACtC,IAAI,CAACC,YAAY,GAAGvB,GAAG,CAACoB,OAAO;QAC/B,IAAI,CAACI,YAAY,GAAGxB,GAAG,CAACyB,UAAU,GAAGzB,GAAG,CAACyB,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACtB,SAAS,EAAE;EACf;EAOSuB,QAAQA,CAAA;IACf,IAAI,CAAChB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACiB,gBAAgB,EAAE;EACzB;EAIAC,0BAA0BA,CAAA;IACxB,IAAI,CAAChB,eAAe,EAAE;EACxB;EAEAe,gBAAgBA,CAAA;IACd,IAAI,CAACxC,iBAAiB,CAAC0C,qCAAqC,CAAC;MAAEf,IAAI,EAAE;IAAE,CAAE,CAAC,CAACf,IAAI,CAC7EpB,GAAG,CAACqB,GAAG,IAAG;MACR;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,GAAG,CAACoB,OAAO,IAAIpB,GAAG,CAACqB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACS,oBAAoB,GAAG9B,GAAG,CAACoB,OAAO,CAACW,GAAG,CAAC/B,GAAG,IAAG;UAChD,OAAO;YACLgC,cAAc,EAAEhC,GAAG,CAACgC,cAAc;YAClCrB,GAAG,EAAEX,GAAG,CAACW;WACV;QACH,CAAC,CAAC;QAEF,IAAI,IAAI,CAACrB,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI2C,KAAK,GAAG,IAAI,CAACH,oBAAoB,CAACI,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACxB,GAAG,IAAI,IAAI,CAACrB,eAAe,CAAC;UAC1F,IAAI,CAACoB,kBAAkB,GAAG,IAAI,CAACoB,oBAAoB,CAACG,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACvB,kBAAkB,GAAG,IAAI,CAACoB,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACpB,kBAAkB,CAACC,GAAG,EAAE;UAC/B,IAAI,CAACC,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACT,SAAS,EAAE;EACf;EAEAiC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACpB,SAAS,GAAGoB,OAAO;IACxB,IAAI,CAACzB,eAAe,EAAE;EACxB;EAEAL,MAAMA,CAAA;IACJ,IAAI,CAACnB,gBAAgB,CAACkD,gCAAgC,CAAC;MACrDxB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACL,kBAAkB,CAACC,GAAG;QACzC4B,OAAO,EAAE,IAAI,CAAChB,YAAY,CAACgB;;KAE9B,CAAC,CAACpC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACqB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAACzC,GAAG,CAAC0C,OAAQ,CAAC;MACzC;MACA,IAAI,CAAC9B,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAN,MAAMA,CAAA;IACJ,IAAI,CAAClB,gBAAgB,CAACuD,kCAAkC,CAAC;MACvD7B,IAAI,EAAE;QACJ8B,YAAY,EAAE,IAAI,CAAClC,kBAAkB,CAACC,GAAG;QACzC4B,OAAO,EAAE,IAAI,CAAChB,YAAY,CAACgB;;KAE9B,CAAC,CAACpC,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACqB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnC,OAAO,CAACsD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACtD,OAAO,CAACuD,YAAY,CAACzC,GAAG,CAAC0C,OAAQ,CAAC;MACzC;MACA,IAAI,CAAC9B,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;CAED;AAxJY9B,mCAAmC,GAAA+D,UAAA,EAR/CpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,kCAAkC;EAC5CC,WAAW,EAAE,+CAA+C;EAC5DC,SAAS,EAAE,CAAC,+CAA+C,CAAC;EAC5DC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxE,YAAY,EAAEE,YAAY;CACrC,CAAC,C,EAEWE,mCAAmC,CAwJ/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}