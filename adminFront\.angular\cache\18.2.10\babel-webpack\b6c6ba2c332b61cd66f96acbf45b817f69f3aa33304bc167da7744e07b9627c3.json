{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarMonths from \"../differenceInCalendarMonths/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isLastDayOfMonth from \"../isLastDayOfMonth/index.js\";\n/**\n * @name differenceInMonths\n * @category Month Helpers\n * @summary Get the number of full months between the given dates.\n *\n * @description\n * Get the number of full months between the given dates using trunc as a default rounding method.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full months\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInMonths(new Date(2014, 8, 1), new Date(2014, 0, 31))\n * //=> 7\n */\nexport default function differenceInMonths(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarMonths(dateLeft, dateRight));\n  var result;\n\n  // Check for the difference of less than month\n  if (difference < 1) {\n    result = 0;\n  } else {\n    if (dateLeft.getMonth() === 1 && dateLeft.getDate() > 27) {\n      // This will check if the date is end of Feb and assign a higher end of month date\n      // to compare it with Jan\n      dateLeft.setDate(30);\n    }\n    dateLeft.setMonth(dateLeft.getMonth() - sign * difference);\n\n    // Math.abs(diff in full months - diff in calendar months) === 1 if last calendar month is not full\n    // If so, result must be decreased by 1 in absolute value\n    var isLastMonthNotFull = compareAsc(dateLeft, dateRight) === -sign;\n\n    // Check for cases of one full calendar month\n    if (isLastDayOfMonth(toDate(dirtyDateLeft)) && difference === 1 && compareAsc(dirtyDateLeft, dateRight) === 1) {\n      isLastMonthNotFull = false;\n    }\n    result = sign * (difference - Number(isLastMonthNotFull));\n  }\n\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}