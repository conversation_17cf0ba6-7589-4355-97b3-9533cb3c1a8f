{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nexport class AnalyticsService {\n  constructor(location, router) {\n    this.location = location;\n    this.router = router;\n    this.enabled = false;\n  }\n  trackPageViews() {\n    if (this.enabled) {\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n        ga('send', {\n          hitType: 'pageview',\n          page: this.location.path()\n        });\n      });\n    }\n  }\n  trackEvent(eventName) {\n    if (this.enabled) {\n      ga('send', 'event', eventName);\n    }\n  }\n  static {\n    this.ɵfac = function AnalyticsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AnalyticsService)(i0.ɵɵinject(i1.Location), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AnalyticsService,\n      factory: AnalyticsService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "AnalyticsService", "constructor", "location", "router", "enabled", "trackPageViews", "events", "pipe", "event", "subscribe", "ga", "hitType", "page", "path", "trackEvent", "eventName", "i0", "ɵɵinject", "i1", "Location", "i2", "Router", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\utils\\analytics.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { NavigationEnd, Router } from '@angular/router';\r\nimport { Location } from '@angular/common';\r\nimport { filter } from 'rxjs/operators';\r\n\r\ndeclare const ga: any;\r\n\r\n@Injectable()\r\nexport class AnalyticsService {\r\n  private enabled: boolean;\r\n\r\n  constructor(private location: Location, private router: Router) {\r\n    this.enabled = false;\r\n  }\r\n\r\n  trackPageViews() {\r\n    if (this.enabled) {\r\n      this.router.events.pipe(\r\n        filter((event) => event instanceof NavigationEnd),\r\n      )\r\n        .subscribe(() => {\r\n          ga('send', {hitType: 'pageview', page: this.location.path()});\r\n        });\r\n    }\r\n  }\r\n\r\n  trackEvent(eventName: string) {\r\n    if (this.enabled) {\r\n      ga('send', 'event', eventName);\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAgB,iBAAiB;AAEvD,SAASC,MAAM,QAAQ,gBAAgB;;;;AAKvC,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,QAAkB,EAAUC,MAAc;IAA1C,KAAAD,QAAQ,GAARA,QAAQ;IAAoB,KAAAC,MAAM,GAANA,MAAM;IACpD,IAAI,CAACC,OAAO,GAAG,KAAK;EACtB;EAEAC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACD,OAAO,EAAE;MAChB,IAAI,CAACD,MAAM,CAACG,MAAM,CAACC,IAAI,CACrBR,MAAM,CAAES,KAAK,IAAKA,KAAK,YAAYV,aAAa,CAAC,CAClD,CACEW,SAAS,CAAC,MAAK;QACdC,EAAE,CAAC,MAAM,EAAE;UAACC,OAAO,EAAE,UAAU;UAAEC,IAAI,EAAE,IAAI,CAACV,QAAQ,CAACW,IAAI;QAAE,CAAC,CAAC;MAC/D,CAAC,CAAC;IACN;EACF;EAEAC,UAAUA,CAACC,SAAiB;IAC1B,IAAI,IAAI,CAACX,OAAO,EAAE;MAChBM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAEK,SAAS,CAAC;IAChC;EACF;;;uCAtBWf,gBAAgB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAhBrB,gBAAgB;MAAAsB,OAAA,EAAhBtB,gBAAgB,CAAAuB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}