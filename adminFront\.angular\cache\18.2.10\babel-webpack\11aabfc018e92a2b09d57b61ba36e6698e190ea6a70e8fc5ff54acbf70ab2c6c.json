{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bosnian [bs]\n//! author : <PERSON><PERSON> : https://github.com/frontyard\n//! based on (hr) translation by <PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function translate(number, withoutSuffix, key) {\n    var result = number + ' ';\n    switch (key) {\n      case 'ss':\n        if (number === 1) {\n          result += 'sekunda';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'sekunde';\n        } else {\n          result += 'sekundi';\n        }\n        return result;\n      case 'm':\n        return withoutSuffix ? 'jedna minuta' : 'jedne minute';\n      case 'mm':\n        if (number === 1) {\n          result += 'minuta';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'minute';\n        } else {\n          result += 'minuta';\n        }\n        return result;\n      case 'h':\n        return withoutSuffix ? 'jedan sat' : 'jednog sata';\n      case 'hh':\n        if (number === 1) {\n          result += 'sat';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'sata';\n        } else {\n          result += 'sati';\n        }\n        return result;\n      case 'dd':\n        if (number === 1) {\n          result += 'dan';\n        } else {\n          result += 'dana';\n        }\n        return result;\n      case 'MM':\n        if (number === 1) {\n          result += 'mjesec';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'mjeseca';\n        } else {\n          result += 'mjeseci';\n        }\n        return result;\n      case 'yy':\n        if (number === 1) {\n          result += 'godina';\n        } else if (number === 2 || number === 3 || number === 4) {\n          result += 'godine';\n        } else {\n          result += 'godina';\n        }\n        return result;\n    }\n  }\n  var bs = moment.defineLocale('bs', {\n    months: 'januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar'.split('_'),\n    monthsShort: 'jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota'.split('_'),\n    weekdaysShort: 'ned._pon._uto._sri._čet._pet._sub.'.split('_'),\n    weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd, D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[danas u] LT',\n      nextDay: '[sutra u] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[u] [nedjelju] [u] LT';\n          case 3:\n            return '[u] [srijedu] [u] LT';\n          case 6:\n            return '[u] [subotu] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[u] dddd [u] LT';\n        }\n      },\n      lastDay: '[jučer u] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n            return '[prošlu] dddd [u] LT';\n          case 6:\n            return '[prošle] [subote] [u] LT';\n          case 1:\n          case 2:\n          case 4:\n          case 5:\n            return '[prošli] dddd [u] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'prije %s',\n      s: 'par sekundi',\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: 'dan',\n      dd: translate,\n      M: 'mjesec',\n      MM: translate,\n      y: 'godinu',\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return bs;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "translate", "number", "withoutSuffix", "key", "result", "bs", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/bs.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Bosnian [bs]\n//! author : <PERSON><PERSON> : https://github.com/frontyard\n//! based on (hr) translation by <PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function translate(number, withoutSuffix, key) {\n        var result = number + ' ';\n        switch (key) {\n            case 'ss':\n                if (number === 1) {\n                    result += 'sekunda';\n                } else if (number === 2 || number === 3 || number === 4) {\n                    result += 'sekunde';\n                } else {\n                    result += 'sekundi';\n                }\n                return result;\n            case 'm':\n                return withoutSuffix ? 'jedna minuta' : 'jedne minute';\n            case 'mm':\n                if (number === 1) {\n                    result += 'minuta';\n                } else if (number === 2 || number === 3 || number === 4) {\n                    result += 'minute';\n                } else {\n                    result += 'minuta';\n                }\n                return result;\n            case 'h':\n                return withoutSuffix ? 'jedan sat' : 'jednog sata';\n            case 'hh':\n                if (number === 1) {\n                    result += 'sat';\n                } else if (number === 2 || number === 3 || number === 4) {\n                    result += 'sata';\n                } else {\n                    result += 'sati';\n                }\n                return result;\n            case 'dd':\n                if (number === 1) {\n                    result += 'dan';\n                } else {\n                    result += 'dana';\n                }\n                return result;\n            case 'MM':\n                if (number === 1) {\n                    result += 'mjesec';\n                } else if (number === 2 || number === 3 || number === 4) {\n                    result += 'mjeseca';\n                } else {\n                    result += 'mjeseci';\n                }\n                return result;\n            case 'yy':\n                if (number === 1) {\n                    result += 'godina';\n                } else if (number === 2 || number === 3 || number === 4) {\n                    result += 'godine';\n                } else {\n                    result += 'godina';\n                }\n                return result;\n        }\n    }\n\n    var bs = moment.defineLocale('bs', {\n        months: 'januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar'.split(\n            '_'\n        ),\n        monthsShort:\n            'jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.'.split(\n                '_'\n            ),\n        monthsParseExact: true,\n        weekdays: 'nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota'.split(\n            '_'\n        ),\n        weekdaysShort: 'ned._pon._uto._sri._čet._pet._sub.'.split('_'),\n        weekdaysMin: 'ne_po_ut_sr_če_pe_su'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY H:mm',\n            LLLL: 'dddd, D. MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[danas u] LT',\n            nextDay: '[sutra u] LT',\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[u] [nedjelju] [u] LT';\n                    case 3:\n                        return '[u] [srijedu] [u] LT';\n                    case 6:\n                        return '[u] [subotu] [u] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[u] dddd [u] LT';\n                }\n            },\n            lastDay: '[jučer u] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                    case 3:\n                        return '[prošlu] dddd [u] LT';\n                    case 6:\n                        return '[prošle] [subote] [u] LT';\n                    case 1:\n                    case 2:\n                    case 4:\n                    case 5:\n                        return '[prošli] dddd [u] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'za %s',\n            past: 'prije %s',\n            s: 'par sekundi',\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: 'dan',\n            dd: translate,\n            M: 'mjesec',\n            MM: translate,\n            y: 'godinu',\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return bs;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IAC3C,IAAIC,MAAM,GAAGH,MAAM,GAAG,GAAG;IACzB,QAAQE,GAAG;MACP,KAAK,IAAI;QACL,IAAIF,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,SAAS;QACvB,CAAC,MAAM,IAAIH,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;UACrDG,MAAM,IAAI,SAAS;QACvB,CAAC,MAAM;UACHA,MAAM,IAAI,SAAS;QACvB;QACA,OAAOA,MAAM;MACjB,KAAK,GAAG;QACJ,OAAOF,aAAa,GAAG,cAAc,GAAG,cAAc;MAC1D,KAAK,IAAI;QACL,IAAID,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,QAAQ;QACtB,CAAC,MAAM,IAAIH,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;UACrDG,MAAM,IAAI,QAAQ;QACtB,CAAC,MAAM;UACHA,MAAM,IAAI,QAAQ;QACtB;QACA,OAAOA,MAAM;MACjB,KAAK,GAAG;QACJ,OAAOF,aAAa,GAAG,WAAW,GAAG,aAAa;MACtD,KAAK,IAAI;QACL,IAAID,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,KAAK;QACnB,CAAC,MAAM,IAAIH,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;UACrDG,MAAM,IAAI,MAAM;QACpB,CAAC,MAAM;UACHA,MAAM,IAAI,MAAM;QACpB;QACA,OAAOA,MAAM;MACjB,KAAK,IAAI;QACL,IAAIH,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,KAAK;QACnB,CAAC,MAAM;UACHA,MAAM,IAAI,MAAM;QACpB;QACA,OAAOA,MAAM;MACjB,KAAK,IAAI;QACL,IAAIH,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,QAAQ;QACtB,CAAC,MAAM,IAAIH,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;UACrDG,MAAM,IAAI,SAAS;QACvB,CAAC,MAAM;UACHA,MAAM,IAAI,SAAS;QACvB;QACA,OAAOA,MAAM;MACjB,KAAK,IAAI;QACL,IAAIH,MAAM,KAAK,CAAC,EAAE;UACdG,MAAM,IAAI,QAAQ;QACtB,CAAC,MAAM,IAAIH,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,EAAE;UACrDG,MAAM,IAAI,QAAQ;QACtB,CAAC,MAAM;UACHA,MAAM,IAAI,QAAQ;QACtB;QACA,OAAOA,MAAM;IACrB;EACJ;EAEA,IAAIC,EAAE,GAAGN,MAAM,CAACO,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EACP,6DAA6D,CAACD,KAAK,CAC/D,GACJ,CAAC;IACLE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,2DAA2D,CAACH,KAAK,CACvE,GACJ,CAAC;IACDI,aAAa,EAAE,oCAAoC,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9DK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,uBAAuB;UAClC,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;YACF,OAAO,qBAAqB;UAChC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,iBAAiB;QAChC;MACJ,CAAC;MACDC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACF,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;YACF,OAAO,0BAA0B;UACrC,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,sBAAsB;QACrC;MACJ,CAAC;MACDG,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAElC,SAAS;MACbmC,CAAC,EAAEnC,SAAS;MACZoC,EAAE,EAAEpC,SAAS;MACbqC,CAAC,EAAErC,SAAS;MACZsC,EAAE,EAAEtC,SAAS;MACbuC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAExC,SAAS;MACbyC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE1C,SAAS;MACb2C,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE5C;IACR,CAAC;IACD6C,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO5C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}