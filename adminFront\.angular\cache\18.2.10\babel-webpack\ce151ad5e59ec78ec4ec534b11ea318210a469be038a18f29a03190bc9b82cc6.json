{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction ImageCarouselComponent_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 14);\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", \"data:image/jpeg;base64,\" + ((tmp_2_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_2_0.base64), i0.ɵɵsanitizeUrl)(\"alt\", (tmp_3_0 = ctx_r1.getCurrentImage()) == null ? null : tmp_3_0.name);\n  }\n}\nfunction ImageCarouselComponent_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_div_1_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.prevImage($event));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"title\", ctx_r1.getNavigationTitle(\"prev\"));\n  }\n}\nfunction ImageCarouselComponent_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_div_1_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.nextImage($event));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 16);\n    i0.ɵɵelement(2, \"path\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"title\", ctx_r1.getNavigationTitle(\"next\"));\n  }\n}\nfunction ImageCarouselComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentIndex + 1, \" / \", ctx_r1.images.length, \" \");\n  }\n}\nfunction ImageCarouselComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onImageClick());\n    });\n    i0.ɵɵtemplate(1, ImageCarouselComponent_div_1_img_1_Template, 1, 2, \"img\", 5);\n    i0.ɵɵelementStart(2, \"div\", 6)(3, \"div\", 7)(4, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 9);\n    i0.ɵɵelement(6, \"path\", 10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(7, ImageCarouselComponent_div_1_button_7_Template, 3, 1, \"button\", 11)(8, ImageCarouselComponent_div_1_button_8_Template, 3, 1, \"button\", 12)(9, ImageCarouselComponent_div_1_div_9_Template, 2, 2, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.aspectRatio);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentImage());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showNavigation && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showNavigation && ctx_r1.images.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCounter && ctx_r1.images.length > 1);\n  }\n}\nfunction ImageCarouselComponent_div_2_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ImageCarouselComponent_div_2_button_1_Template_button_click_0_listener($event) {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setCurrentImage(i_r6, $event));\n    });\n    i0.ɵɵelement(1, \"img\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const image_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"border-blue-500\", i_r6 === ctx_r1.currentIndex)(\"border-gray-300\", i_r6 !== ctx_r1.currentIndex)(\"ring-2\", i_r6 === ctx_r1.currentIndex)(\"ring-blue-200\", i_r6 === ctx_r1.currentIndex);\n    i0.ɵɵproperty(\"title\", ctx_r1.getThumbnailTitle(i_r6));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", \"data:image/jpeg;base64,\" + image_r7.base64, i0.ɵɵsanitizeUrl)(\"alt\", image_r7.name);\n  }\n}\nfunction ImageCarouselComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ImageCarouselComponent_div_2_button_1_Template, 2, 11, \"button\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.images);\n  }\n}\nfunction ImageCarouselComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 26);\n    i0.ɵɵelement(2, \"path\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"\\u7121\\u5716\\u7247\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.aspectRatio);\n  }\n}\nexport class ImageCarouselComponent {\n  constructor() {\n    this.images = [];\n    this.currentIndex = 0;\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square'; // Tailwind CSS class\n    this.containerClass = '';\n    this.imageClick = new EventEmitter();\n    this.indexChange = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n  }\n  getCurrentImage() {\n    if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\n      return this.images[this.currentIndex];\n    }\n    return null;\n  }\n  nextImage(event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = (this.currentIndex + 1) % this.images.length;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  prevImage(event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    if (this.images && this.images.length > 1) {\n      this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  setCurrentImage(index, event) {\n    if (event) {\n      event.stopPropagation();\n    }\n    if (index >= 0 && index < this.images.length) {\n      this.currentIndex = index;\n      this.indexChange.emit(this.currentIndex);\n    }\n  }\n  onImageClick() {\n    const currentImage = this.getCurrentImage();\n    if (currentImage) {\n      this.imageClick.emit({\n        image: currentImage,\n        index: this.currentIndex\n      });\n    }\n  }\n  getThumbnailTitle(index) {\n    return `點選切換到第 ${index + 1} 張圖片`;\n  }\n  getNavigationTitle(direction) {\n    return direction === 'prev' ? '上一張圖片' : '下一張圖片';\n  }\n  static {\n    this.ɵfac = function ImageCarouselComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageCarouselComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageCarouselComponent,\n      selectors: [[\"app-image-carousel\"]],\n      inputs: {\n        images: \"images\",\n        currentIndex: \"currentIndex\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        aspectRatio: \"aspectRatio\",\n        containerClass: \"containerClass\"\n      },\n      outputs: {\n        imageClick: \"imageClick\",\n        indexChange: \"indexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[1, \"image-carousel\", 3, \"ngClass\"], [\"class\", \"relative group cursor-pointer overflow-hidden rounded-xl border-2 border-gray-200 shadow-md hover:shadow-lg transition-all duration-300\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"flex thumbnail-gap mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [\"class\", \"w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"relative\", \"group\", \"cursor-pointer\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\", \"ngClass\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\", \"alt\"], [1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\", \"title\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\", \"title\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"thumbnail-gap\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 thumbnail-container border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"thumbnail-container\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\", \"alt\"], [1, \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\", 3, \"ngClass\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"]],\n      template: function ImageCarouselComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ImageCarouselComponent_div_1_Template, 10, 5, \"div\", 1)(2, ImageCarouselComponent_div_2_Template, 2, 1, \"div\", 2)(3, ImageCarouselComponent_div_3_Template, 5, 1, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.images && ctx.images.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showThumbnails && ctx.images.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.images || ctx.images.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgClass, i1.NgForOf, i1.NgIf],\n      styles: [\".image-carousel[_ngcontent-%COMP%]   .aspect-square[_ngcontent-%COMP%] {\\n  aspect-ratio: 1/1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .aspect-video[_ngcontent-%COMP%] {\\n  aspect-ratio: 16/9;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .aspect-ratio-3-4[_ngcontent-%COMP%] {\\n  aspect-ratio: 3/4;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .aspect-ratio-4-3[_ngcontent-%COMP%] {\\n  aspect-ratio: 4/3;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .thumbnail-container[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .thumbnail-gap[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-carousel[_ngcontent-%COMP%]   .thumbnail-container[_ngcontent-%COMP%] {\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .image-carousel[_ngcontent-%COMP%]   .thumbnail-gap[_ngcontent-%COMP%] {\\n    gap: 0.375rem;\\n  }\\n}\\n.image-carousel[_ngcontent-%COMP%]   .overflow-x-auto[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: rgba(156, 163, 175, 0.5);\\n  border-radius: 2px;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .overflow-x-auto[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background-color: rgba(156, 163, 175, 0.7);\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "i0", "ɵɵelement", "ɵɵproperty", "tmp_2_0", "ctx_r1", "getCurrentImage", "base64", "ɵɵsanitizeUrl", "tmp_3_0", "name", "ɵɵelementStart", "ɵɵlistener", "ImageCarouselComponent_div_1_button_7_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "prevImage", "ɵɵelementEnd", "getNavigationTitle", "ImageCarouselComponent_div_1_button_8_Template_button_click_0_listener", "_r4", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentIndex", "images", "length", "ImageCarouselComponent_div_1_Template_div_click_0_listener", "_r1", "onImageClick", "ɵɵtemplate", "ImageCarouselComponent_div_1_img_1_Template", "ImageCarouselComponent_div_1_button_7_Template", "ImageCarouselComponent_div_1_button_8_Template", "ImageCarouselComponent_div_1_div_9_Template", "aspectRatio", "showNavigation", "showCounter", "ImageCarouselComponent_div_2_button_1_Template_button_click_0_listener", "i_r6", "_r5", "index", "setCurrentImage", "ɵɵclassProp", "getThumbnailTitle", "image_r7", "ImageCarouselComponent_div_2_button_1_Template", "ImageCarouselComponent", "constructor", "showThumbnails", "containerClass", "imageClick", "indexChange", "ngOnInit", "event", "stopPropagation", "emit", "currentImage", "image", "direction", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ImageCarouselComponent_Template", "rf", "ctx", "ImageCarouselComponent_div_1_Template", "ImageCarouselComponent_div_2_Template", "ImageCarouselComponent_div_3_Template", "i1", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-carousel\\image-carousel.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-carousel\\image-carousel.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface ImageData {\r\n  base64: string;\r\n  name: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-image-carousel',\r\n  templateUrl: './image-carousel.component.html',\r\n  styleUrls: ['./image-carousel.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule]\r\n})\r\nexport class ImageCarouselComponent implements OnInit {\r\n  @Input() images: ImageData[] = [];\r\n  @Input() currentIndex: number = 0;\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() aspectRatio: string = 'aspect-square'; // Tailwind CSS class\r\n  @Input() containerClass: string = '';\r\n\r\n  @Output() imageClick = new EventEmitter<{ image: ImageData, index: number }>();\r\n  @Output() indexChange = new EventEmitter<number>();\r\n\r\n  ngOnInit() {\r\n    if (this.currentIndex >= this.images.length) {\r\n      this.currentIndex = 0;\r\n    }\r\n  }\r\n\r\n  getCurrentImage(): ImageData | null {\r\n    if (this.images && this.images.length > 0 && this.currentIndex < this.images.length) {\r\n      return this.images[this.currentIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  nextImage(event?: Event): void {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n    if (this.images && this.images.length > 1) {\r\n      this.currentIndex = (this.currentIndex + 1) % this.images.length;\r\n      this.indexChange.emit(this.currentIndex);\r\n    }\r\n  }\r\n\r\n  prevImage(event?: Event): void {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n    if (this.images && this.images.length > 1) {\r\n      this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n      this.indexChange.emit(this.currentIndex);\r\n    }\r\n  }\r\n\r\n  setCurrentImage(index: number, event?: Event): void {\r\n    if (event) {\r\n      event.stopPropagation();\r\n    }\r\n    if (index >= 0 && index < this.images.length) {\r\n      this.currentIndex = index;\r\n      this.indexChange.emit(this.currentIndex);\r\n    }\r\n  }\r\n\r\n  onImageClick(): void {\r\n    const currentImage = this.getCurrentImage();\r\n    if (currentImage) {\r\n      this.imageClick.emit({ image: currentImage, index: this.currentIndex });\r\n    }\r\n  }\r\n\r\n  getThumbnailTitle(index: number): string {\r\n    return `點選切換到第 ${index + 1} 張圖片`;\r\n  }\r\n\r\n  getNavigationTitle(direction: 'prev' | 'next'): string {\r\n    return direction === 'prev' ? '上一張圖片' : '下一張圖片';\r\n  }\r\n}\r\n", "<div class=\"image-carousel\" [ngClass]=\"containerClass\">\r\n    <!-- 主要圖片區域 -->\r\n    <div *ngIf=\"images && images.length > 0\"\r\n        class=\"relative group cursor-pointer overflow-hidden rounded-xl border-2 border-gray-200 shadow-md hover:shadow-lg transition-all duration-300\"\r\n        [ngClass]=\"aspectRatio\" (click)=\"onImageClick()\">\r\n\r\n        <!-- 主要圖片 -->\r\n        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n            [src]=\"'data:image/jpeg;base64,' + getCurrentImage()?.base64\" [alt]=\"getCurrentImage()?.name\"\r\n            *ngIf=\"getCurrentImage()\">\r\n\r\n        <!-- 放大覆蓋層 -->\r\n        <div\r\n            class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n            <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                    <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                            d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                    </svg>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 導航按鈕 -->\r\n        <button *ngIf=\"showNavigation && images.length > 1\"\r\n            class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n            (click)=\"prevImage($event)\" [title]=\"getNavigationTitle('prev')\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n            </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"showNavigation && images.length > 1\"\r\n            class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n            (click)=\"nextImage($event)\" [title]=\"getNavigationTitle('next')\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n            </svg>\r\n        </button>\r\n\r\n        <!-- 圖片計數器 -->\r\n        <div *ngIf=\"showCounter && images.length > 1\"\r\n            class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n            {{currentIndex + 1}} / {{images.length}}\r\n        </div>\r\n    </div> <!-- 縮略圖導航 -->\r\n    <div *ngIf=\"showThumbnails && images.length > 1\" class=\"flex thumbnail-gap mt-3 overflow-x-auto pb-2\">\r\n        <button *ngFor=\"let image of images; let i = index\"\r\n            class=\"flex-shrink-0 thumbnail-container border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n            [class.border-blue-500]=\"i === currentIndex\" [class.border-gray-300]=\"i !== currentIndex\"\r\n            [class.ring-2]=\"i === currentIndex\" [class.ring-blue-200]=\"i === currentIndex\"\r\n            (click)=\"setCurrentImage(i, $event)\" [title]=\"getThumbnailTitle(i)\">\r\n            <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                [src]=\"'data:image/jpeg;base64,' + image.base64\" [alt]=\"image.name\">\r\n        </button>\r\n    </div>\r\n\r\n    <!-- 無圖片時的佔位符 -->\r\n    <div *ngIf=\"!images || images.length === 0\"\r\n        class=\"w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\"\r\n        [ngClass]=\"aspectRatio\">\r\n        <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n            </path>\r\n        </svg>\r\n        <span class=\"text-sm\">無圖片</span>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;;;;;ICMtCC,EAAA,CAAAC,SAAA,cAE8B;;;;;;IADoCD,EAA9D,CAAAE,UAAA,sCAAAC,OAAA,GAAAC,MAAA,CAAAC,eAAA,qBAAAF,OAAA,CAAAG,MAAA,GAAAN,EAAA,CAAAO,aAAA,CAA6D,SAAAC,OAAA,GAAAJ,MAAA,CAAAC,eAAA,qBAAAG,OAAA,CAAAC,IAAA,CAAgC;;;;;;IAiBjGT,EAAA,CAAAU,cAAA,iBAEqE;IAAjEV,EAAA,CAAAW,UAAA,mBAAAC,uEAAAC,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAc,SAAA,CAAAL,MAAA,CAAiB;IAAA,EAAC;;IAC3Bb,EAAA,CAAAU,cAAA,cAA2E;IACvEV,EAAA,CAAAC,SAAA,eAAmG;IAE3GD,EADI,CAAAmB,YAAA,EAAM,EACD;;;;IAJuBnB,EAAA,CAAAE,UAAA,UAAAE,MAAA,CAAAgB,kBAAA,SAAoC;;;;;;IAMpEpB,EAAA,CAAAU,cAAA,iBAEqE;IAAjEV,EAAA,CAAAW,UAAA,mBAAAU,uEAAAR,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAlB,MAAA,GAAAJ,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAmB,SAAA,CAAAV,MAAA,CAAiB;IAAA,EAAC;;IAC3Bb,EAAA,CAAAU,cAAA,cAA2E;IACvEV,EAAA,CAAAC,SAAA,eAAgG;IAExGD,EADI,CAAAmB,YAAA,EAAM,EACD;;;;IAJuBnB,EAAA,CAAAE,UAAA,UAAAE,MAAA,CAAAgB,kBAAA,SAAoC;;;;;IAOpEpB,EAAA,CAAAU,cAAA,cACsH;IAClHV,EAAA,CAAAwB,MAAA,GACJ;IAAAxB,EAAA,CAAAmB,YAAA,EAAM;;;;IADFnB,EAAA,CAAAyB,SAAA,EACJ;IADIzB,EAAA,CAAA0B,kBAAA,MAAAtB,MAAA,CAAAuB,YAAA,aAAAvB,MAAA,CAAAwB,MAAA,CAAAC,MAAA,MACJ;;;;;;IA3CJ7B,EAAA,CAAAU,cAAA,aAEqD;IAAzBV,EAAA,CAAAW,UAAA,mBAAAmB,2DAAA;MAAA9B,EAAA,CAAAc,aAAA,CAAAiB,GAAA;MAAA,MAAA3B,MAAA,GAAAJ,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA4B,YAAA,EAAc;IAAA,EAAC;IAGhDhC,EAAA,CAAAiC,UAAA,IAAAC,2CAAA,iBAE8B;IAMtBlC,EAHR,CAAAU,cAAA,aAC0I,aAC9C,aACQ;;IACxFV,EAAA,CAAAU,cAAA,aAAyF;IACrFV,EAAA,CAAAC,SAAA,eACqF;IAIrGD,EAHY,CAAAmB,YAAA,EAAM,EACJ,EACJ,EACJ;IAoBNnB,EAjBA,CAAAiC,UAAA,IAAAE,8CAAA,qBAEqE,IAAAC,8CAAA,qBAQA,IAAAC,2CAAA,kBAQiD;IAG1HrC,EAAA,CAAAmB,YAAA,EAAM;;;;IA1CFnB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAkC,WAAA,CAAuB;IAKlBtC,EAAA,CAAAyB,SAAA,EAAuB;IAAvBzB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,GAAuB;IAgBnBL,EAAA,CAAAyB,SAAA,GAAyC;IAAzCzB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAwB,MAAA,CAAAC,MAAA,KAAyC;IAQzC7B,EAAA,CAAAyB,SAAA,EAAyC;IAAzCzB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAmC,cAAA,IAAAnC,MAAA,CAAAwB,MAAA,CAAAC,MAAA,KAAyC;IAS5C7B,EAAA,CAAAyB,SAAA,EAAsC;IAAtCzB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAoC,WAAA,IAAApC,MAAA,CAAAwB,MAAA,CAAAC,MAAA,KAAsC;;;;;;IAM5C7B,EAAA,CAAAU,cAAA,iBAIwE;IAApEV,EAAA,CAAAW,UAAA,mBAAA8B,uEAAA5B,MAAA;MAAA,MAAA6B,IAAA,GAAA1C,EAAA,CAAAc,aAAA,CAAA6B,GAAA,EAAAC,KAAA;MAAA,MAAAxC,MAAA,GAAAJ,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAyC,eAAA,CAAAH,IAAA,EAAA7B,MAAA,CAA0B;IAAA,EAAC;IACpCb,EAAA,CAAAC,SAAA,cACwE;IAC5ED,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAJ+BnB,EADpC,CAAA8C,WAAA,oBAAAJ,IAAA,KAAAtC,MAAA,CAAAuB,YAAA,CAA4C,oBAAAe,IAAA,KAAAtC,MAAA,CAAAuB,YAAA,CAA6C,WAAAe,IAAA,KAAAtC,MAAA,CAAAuB,YAAA,CACtD,kBAAAe,IAAA,KAAAtC,MAAA,CAAAuB,YAAA,CAA2C;IACzC3B,EAAA,CAAAE,UAAA,UAAAE,MAAA,CAAA2C,iBAAA,CAAAL,IAAA,EAA8B;IAE/D1C,EAAA,CAAAyB,SAAA,EAAgD;IAACzB,EAAjD,CAAAE,UAAA,oCAAA8C,QAAA,CAAA1C,MAAA,EAAAN,EAAA,CAAAO,aAAA,CAAgD,QAAAyC,QAAA,CAAAvC,IAAA,CAAmB;;;;;IAP/ET,EAAA,CAAAU,cAAA,cAAsG;IAClGV,EAAA,CAAAiC,UAAA,IAAAgB,8CAAA,sBAIwE;IAI5EjD,EAAA,CAAAmB,YAAA,EAAM;;;;IARwBnB,EAAA,CAAAyB,SAAA,EAAW;IAAXzB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAwB,MAAA,CAAW;;;;;IAWzC5B,EAAA,CAAAU,cAAA,cAE4B;;IACxBV,EAAA,CAAAU,cAAA,cAAkF;IAC9EV,EAAA,CAAAC,SAAA,eAEO;IACXD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAU,cAAA,eAAsB;IAAAV,EAAA,CAAAwB,MAAA,yBAAG;IAC7BxB,EAD6B,CAAAmB,YAAA,EAAO,EAC9B;;;;IAPFnB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAkC,WAAA,CAAuB;;;AD9C/B,OAAM,MAAOY,sBAAsB;EAPnCC,YAAA;IAQW,KAAAvB,MAAM,GAAgB,EAAE;IACxB,KAAAD,YAAY,GAAW,CAAC;IACxB,KAAAyB,cAAc,GAAY,IAAI;IAC9B,KAAAZ,WAAW,GAAY,IAAI;IAC3B,KAAAD,cAAc,GAAY,IAAI;IAC9B,KAAAD,WAAW,GAAW,eAAe,CAAC,CAAC;IACvC,KAAAe,cAAc,GAAW,EAAE;IAE1B,KAAAC,UAAU,GAAG,IAAIxD,YAAY,EAAuC;IACpE,KAAAyD,WAAW,GAAG,IAAIzD,YAAY,EAAU;;EAElD0D,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7B,YAAY,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MAC3C,IAAI,CAACF,YAAY,GAAG,CAAC;IACvB;EACF;EAEAtB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACuB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACF,YAAY,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,EAAE;MACnF,OAAO,IAAI,CAACD,MAAM,CAAC,IAAI,CAACD,YAAY,CAAC;IACvC;IACA,OAAO,IAAI;EACb;EAEAJ,SAASA,CAACkC,KAAa;IACrB,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,eAAe,EAAE;IACzB;IACA,IAAI,IAAI,CAAC9B,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACF,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY,GAAG,CAAC,IAAI,IAAI,CAACC,MAAM,CAACC,MAAM;MAChE,IAAI,CAAC0B,WAAW,CAACI,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC;IAC1C;EACF;EAEAT,SAASA,CAACuC,KAAa;IACrB,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,eAAe,EAAE;IACzB;IACA,IAAI,IAAI,CAAC9B,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACF,YAAY,GAAG,IAAI,CAACA,YAAY,KAAK,CAAC,GAAG,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACF,YAAY,GAAG,CAAC;MAC5F,IAAI,CAAC4B,WAAW,CAACI,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC;IAC1C;EACF;EAEAkB,eAAeA,CAACD,KAAa,EAAEa,KAAa;IAC1C,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,eAAe,EAAE;IACzB;IACA,IAAId,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAChB,MAAM,CAACC,MAAM,EAAE;MAC5C,IAAI,CAACF,YAAY,GAAGiB,KAAK;MACzB,IAAI,CAACW,WAAW,CAACI,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC;IAC1C;EACF;EAEAK,YAAYA,CAAA;IACV,MAAM4B,YAAY,GAAG,IAAI,CAACvD,eAAe,EAAE;IAC3C,IAAIuD,YAAY,EAAE;MAChB,IAAI,CAACN,UAAU,CAACK,IAAI,CAAC;QAAEE,KAAK,EAAED,YAAY;QAAEhB,KAAK,EAAE,IAAI,CAACjB;MAAY,CAAE,CAAC;IACzE;EACF;EAEAoB,iBAAiBA,CAACH,KAAa;IAC7B,OAAO,UAAUA,KAAK,GAAG,CAAC,MAAM;EAClC;EAEAxB,kBAAkBA,CAAC0C,SAA0B;IAC3C,OAAOA,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG,OAAO;EACjD;;;uCApEWZ,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAa,SAAA;MAAAC,MAAA;QAAApC,MAAA;QAAAD,YAAA;QAAAyB,cAAA;QAAAZ,WAAA;QAAAD,cAAA;QAAAD,WAAA;QAAAe,cAAA;MAAA;MAAAY,OAAA;QAAAX,UAAA;QAAAC,WAAA;MAAA;MAAAW,UAAA;MAAAC,QAAA,GAAAnE,EAAA,CAAAoE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfnC1E,EAAA,CAAAU,cAAA,aAAuD;UA2DnDV,EAzDA,CAAAiC,UAAA,IAAA2C,qCAAA,kBAEqD,IAAAC,qCAAA,iBA2CiD,IAAAC,qCAAA,iBAc1E;UAQhC9E,EAAA,CAAAmB,YAAA,EAAM;;;UArEsBnB,EAAA,CAAAE,UAAA,YAAAyE,GAAA,CAAAtB,cAAA,CAA0B;UAE5CrD,EAAA,CAAAyB,SAAA,EAAiC;UAAjCzB,EAAA,CAAAE,UAAA,SAAAyE,GAAA,CAAA/C,MAAA,IAAA+C,GAAA,CAAA/C,MAAA,CAAAC,MAAA,KAAiC;UA6CjC7B,EAAA,CAAAyB,SAAA,EAAyC;UAAzCzB,EAAA,CAAAE,UAAA,SAAAyE,GAAA,CAAAvB,cAAA,IAAAuB,GAAA,CAAA/C,MAAA,CAAAC,MAAA,KAAyC;UAYzC7B,EAAA,CAAAyB,SAAA,EAAoC;UAApCzB,EAAA,CAAAE,UAAA,UAAAyE,GAAA,CAAA/C,MAAA,IAAA+C,GAAA,CAAA/C,MAAA,CAAAC,MAAA,OAAoC;;;qBD9ClC9B,YAAY,EAAAgF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}