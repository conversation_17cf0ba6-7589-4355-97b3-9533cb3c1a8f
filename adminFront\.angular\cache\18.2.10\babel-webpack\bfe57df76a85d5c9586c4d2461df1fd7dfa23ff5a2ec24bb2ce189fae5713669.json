{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 56);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r3)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.prevImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 58);\n    i0.ɵɵelement(2, \"path\", 59);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.nextImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 58);\n    i0.ɵɵelement(2, \"path\", 61);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r3.currentImageIndex || 0) + 1, \" / \", formItemReq_r3.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3, i_r8));\n    });\n    i0.ɵɵelement(1, \"img\", 66);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r9 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"border-gray-300\", i_r8 !== (formItemReq_r3.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r8 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 6, imageUrl_r9), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template, 3, 8, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template, 2, 3, \"img\", 48);\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 50);\n    i0.ɵɵelement(5, \"path\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template, 3, 0, \"button\", 52)(7, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template, 3, 0, \"button\", 53)(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template, 2, 2, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template, 2, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r3));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" \\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r10.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"img\", 72);\n    i0.ɵɵelementStart(2, \"input\", 73);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id, formItemReq_r3));\n    });\n    i0.ɵɵtext(4, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", picture_r14.name)(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u5DF2\\u4E0A\\u50B3\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template, 5, 4, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"label\", 17);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 76);\n    i0.ɵɵpipe(4, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 1, formItemReq_r3.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵtext(1, \" \\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 78)(1, \"nb-checkbox\", 79);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.allSelected, $event) || (formItemReq_r3.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r3));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 78)(1, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedItems[item_r17], $event) || (formItemReq_r3.selectedItems[item_r17] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r17, formItemReq_r3));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedItems[item_r17]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r17, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template, 3, 3, \"label\", 80);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedRemarkType[remark_r19], $event) || (formItemReq_r3.selectedRemarkType[remark_r19] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r19, formItemReq_r3));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r19 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedRemarkType[remark_r19]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r19, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 78);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template, 2, 1, \"label\", 80);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 41);\n    i0.ɵɵtext(2, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template, 2, 1, \"ng-container\", 44)(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r20 = i0.ɵɵreference(6);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CRemarkTypeOptions && ctx_r3.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r20);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"div\", 16)(6, \"label\", 17);\n    i0.ɵɵtext(7, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template, 10, 5, \"div\", 18)(9, DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"div\", 21)(12, \"label\", 22);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 23)(15, \"span\", 24);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CItemName, $event) || (formItemReq_r3.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CRequireAnswer, $event) || (formItemReq_r3.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 28)(23, \"label\", 26);\n    i0.ɵɵtext(24, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"nb-select\", 29);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedCUiType, $event) || (formItemReq_r3.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener() {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(26, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template, 2, 2, \"nb-option\", 30);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(27, \"div\", 31)(28, \"div\", 32)(29, \"div\", 33)(30, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r11 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(31, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"input\", 35, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 1, \"div\", 36)(35, DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template, 5, 3, \"div\", 37)(36, DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(37, \"hr\", 39);\n    i0.ɵɵelementStart(38, \"div\", 40)(39, \"div\", 41);\n    i0.ɵɵtext(40, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 42);\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template, 3, 2, \"label\", 43)(43, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template, 2, 1, \"ng-container\", 44)(44, DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(46, DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template, 7, 2, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_15_0;\n    let tmp_19_0;\n    const formItemReq_r3 = ctx.$implicit;\n    const idx_r21 = ctx.index;\n    const noHouseholds_r22 = i0.ɵɵreference(45);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl && formItemReq_r3.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CMatrialUrl || formItemReq_r3.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r21);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \":\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r3.selectedCUiType.value === 3 || ((tmp_15_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_19_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.listPictures && formItemReq_r3.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 98);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r24)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.prevImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 89);\n    i0.ɵɵelement(2, \"path\", 59);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 89);\n    i0.ɵɵelement(2, \"path\", 61);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r24.currentImageIndex || 0) + 1, \" / \", formItemReq_r24.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_button_1_Template_button_click_0_listener() {\n      const i_r28 = i0.ɵɵrestoreView(_r27).index;\n      const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r24.currentImageIndex = i_r28);\n    });\n    i0.ɵɵelement(1, \"img\", 105);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r29 = ctx.$implicit;\n    const i_r28 = ctx.index;\n    const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"border-gray-400\", i_r28 !== (formItemReq_r24.currentImageIndex || 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 5, imageUrl_r29), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_button_1_Template, 3, 7, \"button\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r24.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onKeydown($event, formItemReq_r24));\n    });\n    i0.ɵɵelementStart(1, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 89);\n    i0.ɵɵelement(4, \"path\", 90);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 91);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_img_6_Template, 2, 3, \"img\", 92)(7, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_7_Template, 3, 0, \"button\", 93)(8, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_8_Template, 3, 0, \"button\", 94)(9, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_9_Template, 2, 2, \"div\", 95);\n    i0.ɵɵelementStart(10, \"div\", 96);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_Template, 2, 1, \"div\", 97);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r24));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r24.CName, \"-\", formItemReq_r24.CPart, \"-\", formItemReq_r24.CLocation, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template, 13, 8, \"div\", 85);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.isModalOpen);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            CMatrialUrl: o.CPicture ? [o.CPicture] : [],\n            currentImageIndex: 0,\n            isModalOpen: false\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [\"accent\", \"success\"], [1, \"pb-4\"], [1, \"font-bold\", \"text-[#818181]\", \"mb-4\"], [1, \"space-y-6\"], [1, \"font-bold\", \"text-xl\", \"pb-2\", \"border-b\", \"mb-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"p-3\", \"sticky\", \"bottom-0\", \"bg-white\", \"border-t\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"p-4\", \"border\", \"rounded-lg\", \"shadow-sm\", \"bg-white\"], [1, \"flex\", \"flex-wrap\", \"-mx-2\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [1, \"flex\", \"flex-wrap\"], [1, \"w-full\", \"md:w-1/3\", \"pr-0\", \"md:pr-3\", \"mb-4\", \"md:mb-0\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-2/3\", \"md:pl-3\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\", \"mb-3\"], [1, \"label\", \"w-1/3\", \"text-base\", \"pr-2\", \"shrink-0\", 3, \"for\"], [1, \"input-group\", \"items-baseline\", \"w-2/3\"], [1, \"text-gray-600\", \"text-sm\", \"mr-1\", \"shrink-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"w-full\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"label\", \"w-1/3\", \"pr-2\", \"shrink-0\", 3, \"for\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-2/3\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-2/3\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"mt-4\", \"md:mt-0\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\", \"mb-2\"], [1, \"btn\", \"btn-info\", \"h-fit\", \"w-full\", 3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"mt-2 space-y-3\", 4, \"ngIf\"], [\"class\", \"w-full text-center mt-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\", 4, \"ngIf\"], [1, \"my-4\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\", \"mb-3\"], [1, \"w-full\", \"md:w-1/5\", \"px-2\", \"pb-1\", \"md:pb-0\", \"font-medium\", \"text-gray-700\"], [1, \"w-full\", \"md:w-4/5\", \"px-2\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"flex flex-wrap w-full items-center\", 4, \"ngIf\"], [1, \"relative\"], [1, \"h-[160px]\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded\", \"border\", \"cursor-pointer\", \"group\", 3, \"click\"], [\"class\", \"h-full w-full object-cover transition-all duration-300 group-hover:scale-105\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-white\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-all\", \"duration-300\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\", 4, \"ngIf\"], [\"class\", \"flex gap-1 mt-2 overflow-x-auto\", 4, \"ngIf\"], [1, \"h-full\", \"w-full\", \"object-cover\", \"transition-all\", \"duration-300\", \"group-hover:scale-105\", 3, \"src\"], [1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", \"z-10\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 19l-7-7 7-7\"], [1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", \"z-10\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded\"], [1, \"flex\", \"gap-1\", \"mt-2\", \"overflow-x-auto\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden hover:border-blue-400 transition-colors cursor-pointer\", 3, \"border-blue-500\", \"border-gray-300\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-colors\", \"cursor-pointer\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"hover:scale-110\", \"transition-transform\", 3, \"src\"], [1, \"h-[160px]\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\"], [3, \"value\"], [1, \"mt-2\", \"space-y-3\"], [\"class\", \"border p-2 rounded-md bg-gray-50\", 4, \"ngFor\", \"ngForOf\"], [1, \"border\", \"p-2\", \"rounded-md\", \"bg-gray-50\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"mb-2\", \"border\", 3, \"src\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-1\", \"text-xs\", \"mb-1\", 3, \"blur\", \"value\", \"disabled\"], [1, \"btn\", \"btn-outline-danger\", \"btn-xs\", \"w-full\", 3, \"click\", \"disabled\"], [1, \"w-full\", \"text-center\", \"mt-2\"], [1, \"h-32\", \"w-full\", \"object-cover\", \"rounded\", \"border\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"rounded\", \"bg-gray-50\", \"text-gray-400\", \"mt-2\"], [1, \"mr-3\", \"cursor-pointer\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"class\", \"mr-3 cursor-pointer\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"flex\", \"flex-wrap\", \"w-full\", \"items-center\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [\"class\", \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-50\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-75\", \"p-4\", 3, \"click\", \"keydown\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", 3, \"click\"], [1, \"absolute\", \"top-4\", \"right-4\", \"z-10\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-10\", \"h-10\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-full\", \"max-h-full\"], [\"class\", \"max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-4\", \"right-4\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"px-3\", \"py-1\", \"rounded\", \"text-sm\"], [\"class\", \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 bg-black bg-opacity-50 p-2 rounded-lg max-w-full overflow-x-auto\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[90vh]\", \"object-contain\", \"rounded-lg\", \"shadow-2xl\", 3, \"src\"], [1, \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-12\", \"h-12\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"rounded-full\", \"w-12\", \"h-12\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-70\", \"transition-all\", 3, \"click\"], [1, \"absolute\", \"bottom-4\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-50\", \"text-white\", \"px-4\", \"py-2\", \"rounded-full\"], [1, \"absolute\", \"bottom-4\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"flex\", \"gap-2\", \"bg-black\", \"bg-opacity-50\", \"p-2\", \"rounded-lg\", \"max-w-full\", \"overflow-x-auto\"], [\"class\", \"flex-shrink-0 w-16 h-16 border-2 rounded overflow-hidden hover:border-white transition-colors\", 3, \"border-white\", \"border-gray-400\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-16\", \"h-16\", \"border-2\", \"rounded\", \"overflow-hidden\", \"hover:border-white\", \"transition-colors\", 3, \"click\"], [1, \"w-full\", \"h-full\", \"object-cover\", 3, \"src\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 4);\n          i0.ɵɵelement(4, \"h1\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 47, 26, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 9)(10, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(14, DetailContentManagementSalesAccountComponent_ng_container_14_Template, 2, 1, \"ng-container\", 8);\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_1_0 = ctx.listFormItem.CIsLock) !== null && tmp_1_0 !== undefined ? tmp_1_0 : false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_2_0 = ctx.listFormItem.CIsLock) !== null && tmp_2_0 !== undefined ? tmp_2_0 : false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\".image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 2px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 2px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.3s ease-out;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 3px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: rgba(255, 255, 255, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n}\\n.image-loading[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.image-loading[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 2rem;\\n  height: 2rem;\\n  border: 2px solid #f3f3f3;\\n  border-top: 2px solid #3498db;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: translate(-50%, -50%) rotate(0deg);\\n  }\\n  100% {\\n    transform: translate(-50%, -50%) rotate(360deg);\\n  }\\n}\\n.tooltip[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.tooltip[_ngcontent-%COMP%]:hover::after {\\n  content: attr(title);\\n  position: absolute;\\n  bottom: 100%;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  padding: 0.5rem;\\n  background: rgba(0, 0, 0, 0.8);\\n  color: white;\\n  font-size: 0.75rem;\\n  border-radius: 0.25rem;\\n  white-space: nowrap;\\n  z-index: 10;\\n  animation: _ngcontent-%COMP%_tooltipFadeIn 0.3s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_tooltipFadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-50%) translateY(0.25rem);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(-50%) translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r3", "getCurrentImage", "formItemReq_r3", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "prevImage", "ɵɵresetView", "stopPropagation", "ɵɵelementEnd", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template_button_click_0_listener", "_r6", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "length", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template_button_click_0_listener", "i_r8", "_r7", "index", "openImageModal", "ɵɵclassProp", "imageUrl_r9", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template_div_click_1_listener", "_r2", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_div_9_Template", "case_r10", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_input_blur_2_listener", "i_r13", "_r12", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template_button_click_3_listener", "picture_r14", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_div_3_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template_nb_checkbox_checkedChange_1_listener", "_r15", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r17", "_r16", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r18", "remark_r19", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_container_4_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_ng_template_5_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r20", "DetailContentManagementSalesAccountComponent_ng_container_8_div_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_17_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_21_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_25_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_25_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_26_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_30_listener", "inputFile_r11", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_32_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_35_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_36_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_label_42_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_43_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_ng_template_44_Template", "DetailContentManagementSalesAccountComponent_ng_container_8_div_46_Template", "idx_r21", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "value", "tmp_15_0", "tmp_19_0", "CUiTypeOptions", "noHouseholds_r22", "formItemReq_r24", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_7_Template_button_click_0_listener", "_r25", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_8_Template_button_click_0_listener", "_r26", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_button_1_Template_button_click_0_listener", "i_r28", "_r27", "imageUrl_r29", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_click_0_listener", "_r23", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_div_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template_button_click_2_listener", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_div_12_Template", "DetailContentManagementSalesAccountComponent_ng_container_14_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "key", "preventDefault", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CPicture", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_8_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_10_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_12_listener", "DetailContentManagementSalesAccountComponent_ng_container_14_Template", "tmp_1_0", "tmp_2_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              CMatrialUrl: o.CPicture ? [o.CPicture] : [],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"pb-4\"> <!-- Added padding to bottom of card body -->\r\n    <h1 class=\"font-bold text-[#818181] mb-4\"></h1> <!-- Retained original empty h1, added margin-bottom -->\r\n    <div class=\"space-y-6\"> <!-- Add vertical spacing between main sections -->\r\n      <h4 class=\"font-bold text-xl pb-2 border-b mb-4\">類型-獨立選樣</h4> <!-- Styled section title -->\r\n\r\n      <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <!-- Enhanced container for each form item -->\r\n        <div class=\"p-4 border rounded-lg shadow-sm bg-white\">\r\n          <div class=\"flex flex-wrap -mx-2\"> <!-- Row container with negative margin for column padding -->\r\n\r\n            <!-- Left Column: Main Material Image + Form Fields -->\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <div class=\"flex flex-wrap\"> <!-- Main Material Images (CMatrialUrl) -->\r\n                <div class=\"w-full md:w-1/3 pr-0 md:pr-3 mb-4 md:mb-0\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">主要材料示意</label>\r\n                  <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                    <!-- Image carousel container -->\r\n                    <div class=\"h-[160px] w-full relative overflow-hidden rounded border cursor-pointer group\"\r\n                      (click)=\"openImageModal(formItemReq)\">\r\n                      <img class=\"h-full w-full object-cover transition-all duration-300 group-hover:scale-105\"\r\n                        [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                      <!-- Zoom overlay -->\r\n                      <div\r\n                        class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center\">\r\n                        <svg class=\"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-all duration-300\"\r\n                          fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                            d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                        </svg>\r\n                      </div>\r\n\r\n                      <!-- Previous button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\"\r\n                        (click)=\"prevImage(formItemReq); $event.stopPropagation()\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\">\r\n                          </path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Next button -->\r\n                      <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-70 transition-all z-10\"\r\n                        (click)=\"nextImage(formItemReq); $event.stopPropagation()\">\r\n                        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      <!-- Image counter -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                        class=\"absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\r\n                        {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                      </div>\r\n                    </div> <!-- Thumbnail navigation for multiple images -->\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-1 mt-2 overflow-x-auto\">\r\n                      <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                        class=\"flex-shrink-0 w-12 h-12 border-2 rounded overflow-hidden hover:border-blue-400 transition-colors cursor-pointer\"\r\n                        [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                        [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                        (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                        <img class=\"w-full h-full object-cover hover:scale-110 transition-transform\"\r\n                          [src]=\"imageUrl | base64Image\">\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                  <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                    class=\"h-[160px] w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400\">\r\n                    無主要材料示意\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields -->\r\n                <div class=\"w-full md:w-2/3 md:pl-3\">\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'CItemName_' + idx\" class=\"label w-1/3 text-base pr-2 shrink-0\">項目名稱</label>\r\n                    <div class=\"input-group items-baseline w-2/3\">\r\n                      <span\r\n                        class=\"text-gray-600 text-sm mr-1 shrink-0\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:</span>\r\n                      <input type=\"text\" [id]=\"'CItemName_' + idx\" class=\"w-full\" nbInput\r\n                        [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                        [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full mb-3\">\r\n                    <label [for]=\"'cRequireAnswer_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">必填數量</label>\r\n                    <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\" class=\"w-2/3\" nbInput placeholder=\"輸入數量\"\r\n                      [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                      [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem.CIsLock ?? false)\" />\r\n                  </div>\r\n                  <div class=\"form-group flex items-center w-full\">\r\n                    <label [for]=\"'uiType_' + idx\" class=\"label w-1/3 pr-2 shrink-0\">前台UI類型</label>\r\n                    <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                      class=\"w-2/3\" (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                      <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                        {{ case.label }}\r\n                      </nb-option>\r\n                    </nb-select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Right Column: Concept Design Image Upload and Preview -->\r\n            <div class=\"w-full md:w-1/4 px-2 mt-4 md:mt-0\">\r\n              <div class=\"w-full flex flex-col\">\r\n                <div class=\"flex justify-end w-full mb-2\">\r\n                  <button class=\"btn btn-info h-fit w-full\" [disabled]=\"listFormItem.CIsLock\"\r\n                    (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n                  <!-- Note: #inputFile in *ngFor creates a local template variable for each iteration -->\r\n                  <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                    accept=\"image/png, image/gif, image/jpeg\">\r\n                </div>\r\n\r\n                <!-- Uploaded Pictures List -->\r\n                <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"mt-2 space-y-3\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">已上傳概念圖</label>\r\n                  <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                    class=\"border p-2 rounded-md bg-gray-50\">\r\n                    <img class=\"h-32 w-full object-cover rounded mb-2 border\" [src]=\"picture.data\">\r\n                    <input nbInput class=\"w-full p-1 text-xs mb-1\" type=\"text\" placeholder=\"圖片說明/檔名\"\r\n                      [value]=\"picture.name\" (blur)=\"renameFile($event, i, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                    <button class=\"btn btn-outline-danger btn-xs w-full\" (click)=\"removeImage(picture.id, formItemReq)\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\">删除</button>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Default Concept Design Image (if no uploaded list) -->\r\n                <div class=\"w-full text-center mt-2\"\r\n                  *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                  <label class=\"block text-sm font-medium text-gray-700 mb-1\">預設概念圖</label>\r\n                  <img class=\"h-32 w-full object-cover rounded border\" [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                </div>\r\n\r\n                <div\r\n                  *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                  class=\"h-32 w-full flex items-center justify-center border rounded bg-gray-50 text-gray-400 mt-2\">\r\n                  無概念設計圖\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Separator Line -->\r\n          <hr class=\"my-4\">\r\n\r\n          <!-- Applicable Households -->\r\n          <div class=\"flex flex-wrap w-full items-center mb-3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">適用戶別</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <label class=\"mr-3 cursor-pointer\" *ngIf=\"houseHoldList.length\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                  全選\r\n                </nb-checkbox>\r\n              </label>\r\n              <ng-container *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                <label *ngFor=\"let item of houseHoldList\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                    [disabled]=\"listFormItem.CIsLock\"\r\n                    (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                    {{ item }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noHouseholds>\r\n                <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Remark Options (Conditional) -->\r\n          <div class=\"flex flex-wrap w-full items-center\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n            <div class=\"w-full md:w-1/5 px-2 pb-1 md:pb-0 font-medium text-gray-700\">備註選項</div>\r\n            <div class=\"w-full md:w-4/5 px-2\">\r\n              <ng-container *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-3 cursor-pointer\">\r\n                  <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                    [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                    (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                    {{ remark }}\r\n                  </nb-checkbox>\r\n                </label>\r\n              </ng-container>\r\n              <ng-template #noRemarkOptions>\r\n                <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n              </ng-template>\r\n            </div>\r\n          </div>\r\n\r\n        </div> <!-- End of enhanced container for each form item -->\r\n      </ng-container>\r\n\r\n      <!-- Removed commented out old ngFor structure -->\r\n\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-end p-3 sticky bottom-0 bg-white border-t\"> <!-- Made footer sticky -->\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-primary\" (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n      儲存\r\n    </button> </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n\r\n    <!-- Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Close Button -->\r\n      <button\r\n        class=\"absolute top-4 right-4 z-10 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-70 transition-all\"\r\n        (click)=\"closeImageModal(formItemReq)\">\r\n        <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n        </svg>\r\n      </button>\r\n\r\n      <!-- Main Image -->\r\n      <div class=\"relative max-w-full max-h-full\">\r\n        <img class=\"max-w-full max-h-[90vh] object-contain rounded-lg shadow-2xl\"\r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n        <!-- Previous Button -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all\"\r\n          (click)=\"prevImageModal(formItemReq)\">\r\n          <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <!-- Next Button -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full w-12 h-12 flex items-center justify-center hover:bg-opacity-70 transition-all\"\r\n          (click)=\"nextImageModal(formItemReq)\">\r\n          <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <!-- Image Counter -->\r\n        <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full\">\r\n          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n        </div>\r\n\r\n        <!-- Image Info -->\r\n        <div class=\"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm\">\r\n          {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 bg-black bg-opacity-50 p-2 rounded-lg max-w-full overflow-x-auto\">\r\n        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n          class=\"flex-shrink-0 w-16 h-16 border-2 rounded overflow-hidden hover:border-white transition-colors\"\r\n          [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n          [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n          (click)=\"formItemReq.currentImageIndex = i\">\r\n          <img class=\"w-full h-full object-cover\" [src]=\"imageUrl | base64Image\">\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;;;;;;;;;;;;;;;;ICStDC,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,cAAA,IAAAN,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAapDP,EAAA,CAAAQ,cAAA,iBAE6D;IAA3DR,EAAA,CAAAS,UAAA,mBAAAC,4GAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAY,SAAA,CAAAV,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eACO;IAEXD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAGTnB,EAAA,CAAAQ,cAAA,iBAE6D;IAA3DR,EAAA,CAAAS,UAAA,mBAAAW,4GAAAT,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAf,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAkB,SAAA,CAAAhB,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAA8F;IAElGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAGTnB,EAAA,CAAAQ,cAAA,cACgG;IAC9FR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;IADJnB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAyB,kBAAA,OAAAnB,cAAA,CAAAoB,iBAAA,mBAAApB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,MACF;;;;;;IAGA5B,EAAA,CAAAQ,cAAA,iBAIiF;IAA/ER,EAAA,CAAAS,UAAA,mBAAAoB,kHAAA;MAAA,MAAAC,IAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAmB,GAAA,EAAAC,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,EAAAwB,IAAA,CAA8B;IAAA,EAAC;IACxC9B,EAAA,CAAAC,SAAA,cACiC;;IACnCD,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAJPnB,EADA,CAAAkC,WAAA,oBAAAJ,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OAAoE,oBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACA;IAC3B1B,EAAA,CAAAE,UAAA,+CAAA4B,IAAA,8BAAqC;IAE5E9B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAgC,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAA8B;;;;;IAPpCP,EAAA,CAAAQ,cAAA,cAAwF;IACtFR,EAAA,CAAAoC,UAAA,IAAAC,yFAAA,qBAIiF;IAInFrC,EAAA,CAAAmB,YAAA,EAAM;;;;IARyBnB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAqB,WAAA,CAA4B;;;;;;IAzC3D3B,EAFF,CAAAQ,cAAA,cAA4F,cAGlD;IAAtCR,EAAA,CAAAS,UAAA,mBAAA6B,gGAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,CAA2B;IAAA,EAAC;IACrCN,EAAA,CAAAoC,UAAA,IAAAI,gFAAA,kBAC0F;IAG1FxC,EAAA,CAAAQ,cAAA,cACwI;;IACtIR,EAAA,CAAAQ,cAAA,cACwD;IACtDR,EAAA,CAAAC,SAAA,eACmF;IAEvFD,EADE,CAAAmB,YAAA,EAAM,EACF;IAsBNnB,EAnBA,CAAAoC,UAAA,IAAAK,mFAAA,qBAE6D,IAAAC,mFAAA,qBAUA,IAAAC,gFAAA,kBAQmC;IAGlG3C,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAoC,UAAA,IAAAQ,gFAAA,kBAAwF;IAU1F5C,EAAA,CAAAmB,YAAA,EAAM;;;;;IA/CoDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAAC,cAAA,EAAkC;IAa/EN,EAAA,CAAAwB,SAAA,GAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAUxC5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAS3C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAK1C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;;;;;IAWhD5B,EAAA,CAAAQ,cAAA,cACoG;IAClGR,EAAA,CAAAuB,MAAA,mDACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;;IA0BFnB,EAAA,CAAAQ,cAAA,oBAA8D;IAC5DR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAY;;;;IAFmCnB,EAAA,CAAAE,UAAA,UAAA2C,QAAA,CAAc;IAC3D7C,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAqBJ/C,EAAA,CAAAQ,cAAA,cAC2C;IACzCR,EAAA,CAAAC,SAAA,cAA+E;IAC/ED,EAAA,CAAAQ,cAAA,gBAE6C;IADpBR,EAAA,CAAAS,UAAA,kBAAAuC,wGAAArC,MAAA;MAAA,MAAAsC,KAAA,GAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAlB,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAQb,MAAA,CAAA+C,UAAA,CAAAxC,MAAA,EAAAsC,KAAA,EAAA3C,cAAA,CAAkC;IAAA,EAAC;IADpEN,EAAA,CAAAmB,YAAA,EAE6C;IAC7CnB,EAAA,CAAAQ,cAAA,iBAC6C;IADQR,EAAA,CAAAS,UAAA,mBAAA2C,0GAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAnC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAkD,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAjD,cAAA,CAAoC;IAAA,EAAC;IACtDN,EAAA,CAAAuB,MAAA,mBAAE;IACjDvB,EADiD,CAAAmB,YAAA,EAAS,EACpD;;;;;;;IANsDnB,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAAE,UAAA,QAAAmD,WAAA,CAAAG,IAAA,EAAAxD,EAAA,CAAAO,aAAA,CAAoB;IAE5EP,EAAA,CAAAwB,SAAA,EAAsB;IACtBxB,EADA,CAAAE,UAAA,UAAAmD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SACoB;IAE1C1D,EAAA,CAAAwB,SAAA,EAA0C;IAA1CxB,EAAA,CAAAE,UAAA,cAAA4D,QAAA,GAAA1D,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAR9C9D,EADF,CAAAQ,cAAA,cAAoG,gBACtC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAC1EnB,EAAA,CAAAoC,UAAA,IAAA2B,iFAAA,kBAC2C;IAQ7C/D,EAAA,CAAAmB,YAAA,EAAM;;;;IATqBnB,EAAA,CAAAwB,SAAA,GAA6B;IAA7BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAA0D,YAAA,CAA6B;;;;;IActDhE,EAFF,CAAAQ,cAAA,cAC6G,gBAC/C;IAAAR,EAAA,CAAAuB,MAAA,qCAAK;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACzEnB,EAAA,CAAAC,SAAA,cAAsG;;IACxGD,EAAA,CAAAmB,YAAA,EAAM;;;;IADiDnB,EAAA,CAAAwB,SAAA,GAAgD;IAAhDxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAG,cAAA,CAAA2D,cAAA,GAAAjE,EAAA,CAAAO,aAAA,CAAgD;;;;;IAGvGP,EAAA,CAAAQ,cAAA,cAEoG;IAClGR,EAAA,CAAAuB,MAAA,6CACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;;;IAaNnB,EADF,CAAAQ,cAAA,gBAAgE,sBAEJ;IAD7CR,EAAA,CAAAkE,gBAAA,2BAAAC,mHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgE,WAAA,EAAA3D,MAAA,MAAAL,cAAA,CAAAgE,WAAA,GAAA3D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IAChDX,EAAA,CAAAS,UAAA,2BAAA0D,mHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAmE,gBAAA,CAAA5D,MAAA,EAAAL,cAAA,CAAqC;IAAA,EAAC;IACvDN,EAAA,CAAAuB,MAAA,qBACF;IACFvB,EADE,CAAAmB,YAAA,EAAc,EACR;;;;;IAJOnB,EAAA,CAAAwB,SAAA,EAAqC;IAArCxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgE,WAAA,CAAqC;IAACtE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAOlF5D,EADF,CAAAQ,cAAA,gBAAsE,sBAGS;IAFhER,EAAA,CAAAkE,gBAAA,2BAAAO,kIAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAsE,aAAA,CAAAF,QAAA,GAAA/D,MAAA,MAAAL,cAAA,CAAAsE,aAAA,CAAAF,QAAA,IAAA/D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAA6C;IAExDX,EAAA,CAAAS,UAAA,2BAAAgE,kIAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAyE,6BAAA,CAAAlE,MAAA,EAAA+D,QAAA,EAAApE,cAAA,CAAwD;IAAA,EAAC;IAC1EN,EAAA,CAAAuB,MAAA,GACF;IACFvB,EADE,CAAAmB,YAAA,EAAc,EACR;;;;;;IALOnB,EAAA,CAAAwB,SAAA,EAA6C;IAA7CxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAsE,aAAA,CAAAF,QAAA,EAA6C;IACxD1E,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAEjC5D,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAA4B,QAAA,MACF;;;;;IANJ1E,EAAA,CAAA8E,uBAAA,GAAkE;IAChE9E,EAAA,CAAAoC,UAAA,IAAA2C,4FAAA,oBAAsE;;;;;IAA9C/E,EAAA,CAAAwB,SAAA,EAAgB;IAAhBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4E,aAAA,CAAgB;;;;;IASxChF,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;;;;;;IAW/CnB,EAAA,CAAAQ,cAAA,sBAEwE;IADtER,EAAA,CAAAkE,gBAAA,2BAAAe,sJAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,GAAAxE,MAAA,MAAAL,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,IAAAxE,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAoD;IACpDX,EAAA,CAAAS,UAAA,2BAAAwE,sJAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAiF,sBAAA,CAAA1E,MAAA,EAAAwE,UAAA,EAAA7E,cAAA,CAAmD;IAAA,EAAC;IACrEN,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAc;;;;;;IAHZnB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,EAAoD;IAACnF,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAEtF5D,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAqC,UAAA,MACF;;;;;IALFnF,EAAA,CAAAQ,cAAA,gBAA6E;IAC3ER,EAAA,CAAAoC,UAAA,IAAAkD,gHAAA,0BAEwE;IAG1EtF,EAAA,CAAAmB,YAAA,EAAQ;;;;IALQnB,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA8E,kBAAA,CAAoC;;;;;IAFtDpF,EAAA,CAAA8E,uBAAA,GAAgG;IAC9F9E,EAAA,CAAAoC,UAAA,IAAAmD,kGAAA,oBAA6E;;;;;IAAnDvF,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAoF,kBAAA,CAAqB;;;;;IAS/CxF,EAAA,CAAAQ,cAAA,eAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;;;;;IAZrDnB,EADF,CAAAQ,cAAA,cAAgG,cACrB;IAAAR,EAAA,CAAAuB,MAAA,+BAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IACnFnB,EAAA,CAAAQ,cAAA,cAAkC;IAUhCR,EATA,CAAAoC,UAAA,IAAAqD,0FAAA,2BAAgG,IAAAC,yFAAA,gCAAA1F,EAAA,CAAA2F,sBAAA,CASlE;IAIlC3F,EADE,CAAAmB,YAAA,EAAM,EACF;;;;;IAbanB,EAAA,CAAAwB,SAAA,GAA2D;IAAAxB,EAA3D,CAAAE,UAAA,SAAAE,MAAA,CAAAoF,kBAAA,IAAApF,MAAA,CAAAoF,kBAAA,CAAA5D,MAAA,KAA2D,aAAAgE,mBAAA,CAAoB;;;;;;IA9KtG5F,EAAA,CAAA8E,uBAAA,GAA8E;IASlE9E,EAPV,CAAAQ,cAAA,cAAsD,cAClB,cAGE,cACJ,cAC6B,gBACO;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAsD1EnB,EArDA,CAAAoC,UAAA,IAAAyD,0EAAA,mBAA4F,IAAAC,0EAAA,kBAsDQ;IAGtG9F,EAAA,CAAAmB,YAAA,EAAM;IAKFnB,EAFJ,CAAAQ,cAAA,eAAqC,eACmB,iBAC0B;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAExFnB,EADF,CAAAQ,cAAA,eAA8C,gBAEE;IAAAR,EAAA,CAAAuB,MAAA,IAAsE;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;IAC3HnB,EAAA,CAAAQ,cAAA,iBAE+C;IAD7CR,EAAA,CAAAkE,gBAAA,2BAAA6B,qGAAApF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA2F,SAAA,EAAAtF,MAAA,MAAAL,cAAA,CAAA2F,SAAA,GAAAtF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAmC;IAGzCX,EAJI,CAAAmB,YAAA,EAE+C,EAC3C,EACF;IAEJnB,EADF,CAAAQ,cAAA,eAAsD,iBACqB;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACrFnB,EAAA,CAAAQ,cAAA,iBAE4F;IAD1FR,EAAA,CAAAkE,gBAAA,2BAAAgC,qGAAAvF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA6F,cAAA,EAAAxF,MAAA,MAAAL,cAAA,CAAA6F,cAAA,GAAAxF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAE5CX,EAHE,CAAAmB,YAAA,EAE4F,EACxF;IAEJnB,EADF,CAAAQ,cAAA,eAAiD,iBACkB;IAAAR,EAAA,CAAAuB,MAAA,kCAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAC/EnB,EAAA,CAAAQ,cAAA,qBAE6C;IAFUR,EAAA,CAAAkE,gBAAA,2BAAAkC,yGAAAzF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA+F,eAAA,EAAA1F,MAAA,MAAAL,cAAA,CAAA+F,eAAA,GAAA1F,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAyC;IAChFX,EAAA,CAAAS,UAAA,4BAAA6F,0GAAA;MAAA,MAAAhG,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAkBb,MAAA,CAAAmG,mBAAA,CAAAjG,cAAA,CAAgC;IAAA,EAAC;IAEjEN,EAAA,CAAAoC,UAAA,KAAAoE,iFAAA,wBAA8D;IAOxExG,EAJQ,CAAAmB,YAAA,EAAY,EACR,EACF,EACF,EACF;IAMAnB,EAHN,CAAAQ,cAAA,eAA+C,eACX,eACU,kBAEV;IAA5BR,EAAA,CAAAS,UAAA,mBAAAgG,8FAAA;MAAAzG,EAAA,CAAAY,aAAA,CAAAoF,GAAA;MAAA,MAAAU,aAAA,GAAA1G,EAAA,CAAA2G,WAAA;MAAA,OAAA3G,EAAA,CAAAiB,WAAA,CAASyF,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAC5G,EAAA,CAAAuB,MAAA,kDAAO;IAAAvB,EAAA,CAAAmB,YAAA,EAAS;IAE9CnB,EAAA,CAAAQ,cAAA,oBAC4C;IADCR,EAAA,CAAAS,UAAA,oBAAAoG,8FAAAlG,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAoF,GAAA,EAAAjF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUb,MAAA,CAAA0G,WAAA,CAAAnG,MAAA,EAAAL,cAAA,CAAgC;IAAA,EAAC;IAE1FN,EAFE,CAAAmB,YAAA,EAC4C,EACxC;IAuBNnB,EApBA,CAAAoC,UAAA,KAAA2E,2EAAA,kBAAoG,KAAAC,2EAAA,kBAeS,KAAAC,2EAAA,kBAOT;IAK1GjH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;IAGNnB,EAAA,CAAAC,SAAA,cAAiB;IAIfD,EADF,CAAAQ,cAAA,eAAqD,eACsB;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IACnFnB,EAAA,CAAAQ,cAAA,eAAkC;IAgBhCR,EAfA,CAAAoC,UAAA,KAAA8E,6EAAA,oBAAgE,KAAAC,oFAAA,2BAME,KAAAC,mFAAA,gCAAApH,EAAA,CAAA2F,sBAAA,CASvC;IAI/B3F,EADE,CAAAmB,YAAA,EAAM,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAiF,2EAAA,kBAAgG;IAkBlGrH,EAAA,CAAAmB,YAAA,EAAM;;;;;;;;;;;IAnLUnB,EAAA,CAAAwB,SAAA,GAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAmE;IAqDnE5B,EAAA,CAAAwB,SAAA,EAAsE;IAAtExB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,OAAsE;IASnE5B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAAE,UAAA,uBAAAoH,OAAA,CAA0B;IAGetH,EAAA,CAAAwB,SAAA,GAAsE;IAAtExB,EAAA,CAAAuH,kBAAA,KAAAjH,cAAA,CAAAkH,KAAA,OAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,SAAA,MAAsE;IACjG1H,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAE,UAAA,sBAAAoH,OAAA,CAAyB;IAC1CtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA2F,SAAA,CAAmC;IACnCjG,EAAA,CAAAE,UAAA,cAAAwD,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAA0C;IAIvC1D,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAE,UAAA,4BAAAoH,OAAA,CAA+B;IACjBtH,EAAA,CAAAwB,SAAA,GAA8B;IAA9BxB,EAAA,CAAAE,UAAA,2BAAAoH,OAAA,CAA8B;IACjDtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA6F,cAAA,CAAwC;IACxCnG,EAAA,CAAAE,UAAA,aAAAI,cAAA,CAAA+F,eAAA,CAAAsB,KAAA,YAAAC,QAAA,GAAAxH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,UAAuF;IAGlF5H,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAE,UAAA,oBAAAoH,OAAA,CAAuB;IACEtH,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAE,UAAA,mBAAAoH,OAAA,CAAsB;IAACtH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA+F,eAAA,CAAyC;IAE9FrG,EAAA,CAAAE,UAAA,cAAA2H,QAAA,GAAAzH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAiE,QAAA,KAAAhE,SAAA,GAAAgE,QAAA,SAA0C;IACd7H,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA0H,cAAA,CAAiB;IAaP9H,EAAA,CAAAwB,SAAA,GAAiC;IAAjCxB,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAQvE5D,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,KAAqE;IAexE5B,EAAA,CAAAwB,SAAA,EAAwG;IAAxGxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAwG;IAMxG5B,EAAA,CAAAwB,SAAA,EAAyG;IAAzGxB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAyG;IAe1E5B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,CAA0B;IAM/C5B,EAAA,CAAAwB,SAAA,EAAgC;IAAAxB,EAAhC,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,KAAgC,aAAAmG,gBAAA,CAAiB;IAgBnB/H,EAAA,CAAAwB,SAAA,GAA6C;IAA7CxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA+F,eAAA,CAAAsB,KAAA,OAA6C;;;;;IAuDhG3H,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAA2H,eAAA,IAAAhI,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAGpDP,EAAA,CAAAQ,cAAA,iBAEwC;IAAtCR,EAAA,CAAAS,UAAA,mBAAAwH,6GAAA;MAAAjI,EAAA,CAAAY,aAAA,CAAAsH,IAAA;MAAA,MAAAF,eAAA,GAAAhI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA+H,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrChI,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAAiG;IAErGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAGTnB,EAAA,CAAAQ,cAAA,kBAEwC;IAAtCR,EAAA,CAAAS,UAAA,mBAAA2H,6GAAA;MAAApI,EAAA,CAAAY,aAAA,CAAAyH,IAAA;MAAA,MAAAL,eAAA,GAAAhI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAkI,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrChI,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAA8F;IAElGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAGTnB,EAAA,CAAAQ,cAAA,eACyH;IACvHR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;IADJnB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAyB,kBAAA,OAAAuG,eAAA,CAAAtG,iBAAA,mBAAAsG,eAAA,CAAArG,WAAA,CAAAC,MAAA,MACF;;;;;;IAWA5B,EAAA,CAAAQ,cAAA,kBAI8C;IAA5CR,EAAA,CAAAS,UAAA,mBAAA8H,oHAAA;MAAA,MAAAC,KAAA,GAAAxI,EAAA,CAAAY,aAAA,CAAA6H,IAAA,EAAAzG,KAAA;MAAA,MAAAgG,eAAA,GAAAhI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAAA+G,eAAA,CAAAtG,iBAAA,GAAA8G,KAAA;IAAA,EAA2C;IAC3CxI,EAAA,CAAAC,SAAA,eAAuE;;IACzED,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAHPnB,EADA,CAAAkC,WAAA,iBAAAsG,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OAAiE,oBAAA8G,KAAA,MAAAR,eAAA,CAAAtG,iBAAA,OACG;IAE5B1B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAuI,YAAA,GAAA1I,EAAA,CAAAO,aAAA,CAA8B;;;;;IAP1EP,EAAA,CAAAQ,cAAA,eAC4I;IAC1IR,EAAA,CAAAoC,UAAA,IAAAuG,2FAAA,sBAI8C;IAGhD3I,EAAA,CAAAmB,YAAA,EAAM;;;;IAPyBnB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAA8H,eAAA,CAAArG,WAAA,CAA4B;;;;;;IAvD/D3B,EAAA,CAAAQ,cAAA,cAEiG;IAAxDR,EAAvC,CAAAS,UAAA,mBAAAmI,iGAAA;MAAA5I,EAAA,CAAAY,aAAA,CAAAiI,IAAA;MAAA,MAAAb,eAAA,GAAAhI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA0I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAApI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAiI,IAAA;MAAA,MAAAb,eAAA,GAAAhI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAYb,MAAA,CAAA4I,SAAA,CAAArI,MAAA,EAAAqH,eAAA,CAA8B;IAAA,EAAC;IAGjFhI,EAAA,CAAAQ,cAAA,cACqC;IAAnCR,EAAA,CAAAS,UAAA,mBAAAwI,iGAAAtI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAiI,IAAA;MAAA,OAAA7I,EAAA,CAAAiB,WAAA,CAASN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;IAGlClB,EAAA,CAAAQ,cAAA,iBAEyC;IAAvCR,EAAA,CAAAS,UAAA,mBAAAyI,oGAAA;MAAAlJ,EAAA,CAAAY,aAAA,CAAAiI,IAAA;MAAA,MAAAb,eAAA,GAAAhI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA0I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtChI,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAAsG;IAE1GD,EADE,CAAAmB,YAAA,EAAM,EACC;;IAGTnB,EAAA,CAAAQ,cAAA,cAA4C;IAuB1CR,EAtBA,CAAAoC,UAAA,IAAA+G,iFAAA,kBAC0F,IAAAC,oFAAA,qBAKlD,IAAAC,oFAAA,qBASA,IAAAC,iFAAA,kBAQiF;IAKzHtJ,EAAA,CAAAQ,cAAA,eAAmG;IACjGR,EAAA,CAAAuB,MAAA,IACF;IACFvB,EADE,CAAAmB,YAAA,EAAM,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAmH,kFAAA,kBAC4I;IAUhJvJ,EADE,CAAAmB,YAAA,EAAM,EACF;;;;;IA5CsDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAA2H,eAAA,EAAkC;IAG/EhI,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAA8H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAC,MAAA,KAAmE;IASnE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAA8H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAC,MAAA,KAAmE;IAStE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAA8H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAC,MAAA,KAAmE;IAOvE5B,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAuH,kBAAA,MAAAS,eAAA,CAAAR,KAAA,OAAAQ,eAAA,CAAAP,KAAA,OAAAO,eAAA,CAAAN,SAAA,MACF;IAII1H,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAA8H,eAAA,CAAArG,WAAA,IAAAqG,eAAA,CAAArG,WAAA,CAAAC,MAAA,KAAmE;;;;;IAtD/E5B,EAAA,CAAA8E,uBAAA,GAA8E;IAC5E9E,EAAA,CAAAoC,UAAA,IAAAoH,2EAAA,mBAEiG;;;;;IAF3FxJ,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAE,UAAA,SAAA8H,eAAA,CAAAyB,WAAA,CAA6B;;;ADvKrC,OAAM,MAAOC,4CAA6C,SAAQ7J,aAAa;EAC7E8J,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAED,KAAA1C,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,EACD;MACE4E,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,EAAE;MACD4E,KAAK,EAAE,CAAC;MAAE5E,KAAK,EAAE;KAClB,CACF;IACD,KAAAyC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA8BjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAqNnD,KAAAqF,KAAK,GAAY,IAAI;EArQrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMxH,EAAE,GAAGuH,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGzH,EAAE;QACrB,IAAI,IAAI,CAACyH,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAC5D,KAAU,EAAE6D,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC9D,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO8D,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA3E,WAAWA,CAAC4E,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC3H,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;UACxC+J,YAAY,CAAC3H,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAI8I,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB7I,IAAI,EAAEmI,IAAI,CAACnI,IAAI,CAAC8I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B/I,IAAI,EAAE2I,SAAS;YACfK,SAAS,EAAE,IAAI,CAACvC,eAAe,CAACwC,gBAAgB,CAACb,IAAI,CAACnI,IAAI,CAAC;YAC3DiJ,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC3H,YAAY,CAAC2I,IAAI,CAAC;YAC7BpJ,EAAE,EAAE,IAAI8I,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB7I,IAAI,EAAEmI,IAAI,CAACnI,IAAI,CAAC8I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B/I,IAAI,EAAE2I,SAAS;YACfK,SAAS,EAAE,IAAI,CAACvC,eAAe,CAACwC,gBAAgB,CAACb,IAAI,CAACnI,IAAI,CAAC;YAC3DiJ,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAAClE,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEArE,WAAWA,CAACsJ,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC3H,YAAY,CAACpC,MAAM,EAAE;MACpC+J,YAAY,CAAC3H,YAAY,GAAG2H,YAAY,CAAC3H,YAAY,CAAC6I,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACvJ,EAAE,IAAIqJ,SAAS,CAAC;IAC7F;EACF;EACAzJ,UAAUA,CAACuI,KAAU,EAAE1J,KAAa,EAAE2J,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAAC0K,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAAC0K,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAAC0K,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAAClE,KAAK,GAAG,GAAG,GAAGgE,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAACwK,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAAC0K,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC3H,YAAY,CAAChC,KAAK,CAAC,CAAC0K,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA7L,SAASA,CAAC+L,WAAgB;IACxB,IAAIA,WAAW,CAAC1L,WAAW,IAAI0L,WAAW,CAAC1L,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjEyL,WAAW,CAAC3L,iBAAiB,GAAG,CAAC2L,WAAW,CAAC3L,iBAAiB,GAAG,CAAC,IAAI2L,WAAW,CAAC1L,WAAW,CAACC,MAAM;IACtG;EACF;EAEAZ,SAASA,CAACqM,WAAgB;IACxB,IAAIA,WAAW,CAAC1L,WAAW,IAAI0L,WAAW,CAAC1L,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjEyL,WAAW,CAAC3L,iBAAiB,GAAG2L,WAAW,CAAC3L,iBAAiB,KAAK,CAAC,GAC/D2L,WAAW,CAAC1L,WAAW,CAACC,MAAM,GAAG,CAAC,GAClCyL,WAAW,CAAC3L,iBAAiB,GAAG,CAAC;IACvC;EACF;EACArB,eAAeA,CAACgN,WAAgB;IAC9B,IAAIA,WAAW,CAAC1L,WAAW,IAAI0L,WAAW,CAAC1L,WAAW,CAACC,MAAM,GAAG,CAAC,IAAIyL,WAAW,CAAC3L,iBAAiB,KAAKmC,SAAS,EAAE;MAChH,OAAOwJ,WAAW,CAAC1L,WAAW,CAAC0L,WAAW,CAAC3L,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAO,cAAcA,CAACoL,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAC1L,WAAW,IAAI0L,WAAW,CAAC1L,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI0L,UAAU,KAAKzJ,SAAS,EAAE;QAC5BwJ,WAAW,CAAC3L,iBAAiB,GAAG4L,UAAU;MAC5C;MACAD,WAAW,CAAC5D,WAAW,GAAG,IAAI;MAC9B;MACA0B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAxC,eAAeA,CAACuE,WAAgB;IAC9BA,WAAW,CAAC5D,WAAW,GAAG,KAAK;IAC/B;IACA0B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAhD,cAAcA,CAAC+E,WAAgB;IAC7B,IAAI,CAAC/L,SAAS,CAAC+L,WAAW,CAAC;EAC7B;EAEAlF,cAAcA,CAACkF,WAAgB;IAC7B,IAAI,CAACrM,SAAS,CAACqM,WAAW,CAAC;EAC7B;EAEA;EACArE,SAASA,CAAC0C,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAAC5D,WAAW,EAAE;MAC3B,QAAQiC,KAAK,CAAC6B,GAAG;QACf,KAAK,WAAW;UACd7B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACrF,cAAc,CAACkF,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAClF,cAAc,CAAC+E,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAAC1E,eAAe,CAACuE,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA9I,gBAAgBA,CAACkJ,OAAgB,EAAE9B,YAAiB;IAClDA,YAAY,CAACrH,WAAW,GAAGmJ,OAAO;IAClC,IAAI,CAACzI,aAAa,CAAC0I,OAAO,CAACjC,IAAI,IAAG;MAChCE,YAAY,CAAC/G,aAAa,CAAC6G,IAAI,CAAC,GAAGgC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEA5I,6BAA6BA,CAAC4I,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IAC7E,IAAI8B,OAAO,EAAE;MACX9B,YAAY,CAAC/G,aAAa,CAAC6G,IAAI,CAAC,GAAGgC,OAAO;MAC1C9B,YAAY,CAACrH,WAAW,GAAG,IAAI,CAACU,aAAa,CAAC2I,KAAK,CAAClC,IAAI,IAAIE,YAAY,CAAC/G,aAAa,CAAC6G,IAAI,CAAC,IAAIgC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL9B,YAAY,CAACrH,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAACoI,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACvG,kBAAkB,CAACqG,IAAI,CAAC,GAAGgC,OAAO;EACjD;EAEAG,kBAAkBA,CAACpI,kBAA4B,EAAEqI,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAIvI,kBAAkB,EAAE;MACvCsI,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAACtB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIc,WAAW,EAAE;MAC9B,IAAIxI,kBAAkB,CAACyI,QAAQ,CAACf,IAAI,CAAC,EAAE;QACrCY,YAAY,CAACZ,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOY,YAAY;EACrB;EAEAI,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACT,OAAO,CAACjC,IAAI,IAAG;MACnB,MAAM8B,GAAG,GAAG,GAAG9B,IAAI,CAAC/D,SAAS,IAAI+D,IAAI,CAACjE,KAAK,IAAIiE,IAAI,CAAChE,KAAK,EAAE;MAC3D,IAAI2G,GAAG,CAACE,GAAG,CAACf,GAAG,CAAC,EAAE;QAChB,MAAMgB,QAAQ,GAAGH,GAAG,CAACrD,GAAG,CAACwC,GAAG,CAAE;QAC9BgB,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,GAAG,CAACK,GAAG,CAAClB,GAAG,EAAE;UAAE9B,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE+C,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,EAAE,CAAC,CAACR,GAAG,CAAC,CAAC;MAAE3C,IAAI;MAAE+C;IAAK,CAAE,MAAM;MACxD,GAAG/C,IAAI;MACPoD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAAC1E,gBAAgB,CAAC2E,mCAAmC,CAAC;MACxD3D,IAAI,EAAE;QACJ4D,YAAY,EAAE,IAAI,CAAChE,WAAW;QAC9BiE,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLvP,GAAG,CAACwP,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACrK,aAAa,CAAC0I,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC7G,aAAa,CAAC6G,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAACjG,kBAAkB,CAACkI,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACrG,kBAAkB,CAACqG,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC6D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAChB,GAAG,CAAEmB,CAAM,IAAI;UACnD,OAAO;YACLtL,cAAc,EAAE,IAAI;YACpBuL,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb/H,SAAS,EAAE6H,CAAC,CAAC7H,SAAS;YACtBF,KAAK,EAAE+H,CAAC,CAAC/H,KAAK;YACdC,KAAK,EAAE8H,CAAC,CAAC9H,KAAK;YACdxB,SAAS,EAAE,GAAGsJ,CAAC,CAAC/H,KAAK,IAAI+H,CAAC,CAAC9H,KAAK,IAAI8H,CAAC,CAAC7H,SAAS,EAAE;YACjDmG,WAAW,EAAE,IAAI;YACjBgB,YAAY,EAAE,CAAC;YACf1I,cAAc,EAAE,CAAC;YACjBuJ,OAAO,EAAE,CAAC;YAAE9K,aAAa,EAAE,EAAE;YAC7BQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAAEqC,eAAe,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC,CAAC;YACzDnG,WAAW,EAAE4N,CAAC,CAACI,QAAQ,GAAG,CAACJ,CAAC,CAACI,QAAQ,CAAC,GAAG,EAAE;YAC3CjO,iBAAiB,EAAE,CAAC;YACpB+H,WAAW,EAAE;WACd;QACH,CAAC,CAAC;QACF,IAAI,CAAC6F,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC1E,SAAS,EAAE;EACf;EAKAgF,eAAeA,CAAA;IACb,IAAI,CAAC7F,gBAAgB,CAAC8F,mCAAmC,CAAC;MACxDzE,IAAI,EAAE;QACJ4D,YAAY,EAAE,IAAI,CAAChE,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DuF,SAAS,EAAE;;KAEd,CAAC,CAACZ,IAAI,CACLvP,GAAG,CAACwP,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1L,YAAY,GAAGwL,GAAG,CAACC,OAAO;QAC/B,IAAI,CAAC3E,KAAK,GAAG0E,GAAG,CAACC,OAAO,CAACW,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAIZ,GAAG,CAACC,OAAO,CAACW,SAAS,EAAE;UACzB,IAAI,CAAC/K,aAAa,CAAC0I,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC7G,aAAa,CAAC6G,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAACjG,kBAAkB,CAACkI,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACrG,kBAAkB,CAACqG,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC6D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACW,SAAS,CAAC3B,GAAG,CAAEmB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAC9L,YAAY,CAAC8L,OAAO;cAClCxL,cAAc,EAAEsL,CAAC,CAACtL,cAAc;cAChCtC,WAAW,EAAE4N,CAAC,CAAC5N,WAAW,KAAK4N,CAAC,CAACS,gBAAgB,GAAG,CAACT,CAAC,CAACS,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EtD,KAAK,EAAE6C,CAAC,CAAC7C,KAAK;cACd8C,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1BvI,SAAS,EAAE6H,CAAC,CAAC7H,SAAS;cACtBF,KAAK,EAAE+H,CAAC,CAAC/H,KAAK;cACdC,KAAK,EAAE8H,CAAC,CAAC9H,KAAK;cACdxB,SAAS,EAAEsJ,CAAC,CAACtJ,SAAS,GAAGsJ,CAAC,CAACtJ,SAAS,GAAG,GAAGsJ,CAAC,CAAC/H,KAAK,IAAI+H,CAAC,CAAC9H,KAAK,IAAI8H,CAAC,CAAC7H,SAAS,EAAE;cAC7EmG,WAAW,EAAE0B,CAAC,CAAC1B,WAAW;cAC1BgB,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5B1I,cAAc,EAAEoJ,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAACpJ,cAAc;cACtDuJ,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClB9K,aAAa,EAAE2K,CAAC,CAACW,qBAAqB,CAACtO,MAAM,GAAG,IAAI,CAACuO,0BAA0B,CAAC,IAAI,CAACnL,aAAa,EAAEuK,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACtL;cAAa,CAAE;cAAEQ,kBAAkB,EAAEmK,CAAC,CAAC1B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACpI,kBAAkB,EAAE+J,CAAC,CAAC1B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACzI;cAAkB,CAAE;cAC9Rd,WAAW,EAAEiL,CAAC,CAACW,qBAAqB,CAACtO,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzEoC,YAAY,EAAE,EAAE;cAAEqC,eAAe,EAAEkJ,CAAC,CAACG,OAAO,GAAG,IAAI,CAACnE,cAAc,CAACgE,CAAC,CAACG,OAAO,EAAE,IAAI,CAAC5H,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HpG,iBAAiB,EAAE,CAAC;cACpB+H,WAAW,EAAE;aACd;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACqF,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAClE,SAAS,EAAE;EACf;EAEArE,mBAAmBA,CAAC8G,WAAgB;IAClC,IAAIA,WAAW,CAAChH,eAAe,IAAIgH,WAAW,CAAChH,eAAe,CAACsB,KAAK,KAAK,CAAC,EAAE;MAC1E0F,WAAW,CAAClH,cAAc,GAAG,CAAC;IAChC;EACF;EACAiK,4BAA4BA,CAAC5M,IAAW;IACtC,KAAK,IAAIiI,IAAI,IAAIjI,IAAI,EAAE;MACrB,IAAIiI,IAAI,CAACjB,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOiB,IAAI,CAAC4E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC1D,MAAM,CAACU,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC;EACjD;EAEAmD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB1D,MAAM,CAACU,GAAG,IAAIgD,GAAG,CAAChD,GAAG,CAAC,CAAC,CACvBoD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACvK,eAAoB,EAAEjB,kBAAuB;IAC1D,IAAIiB,eAAe,IAAIA,eAAe,CAACsB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC+I,0BAA0B,CAACtL,kBAAkB,CAAC;IAC5D;EACF;EAEAyL,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACvE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIwE,KAAK,CAACnP,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOmP,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAChN,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLqP,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC7M,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpE0N,aAAa,EAAElN,YAAY,CAAC,CAAC,CAAC,CAACwI,SAAS,IAAI,IAAI;QAChD2E,QAAQ,EAAEnN,YAAY,CAAC,CAAC,CAAC,CAAC0I,KAAK,CAACjJ,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAuN,UAAUA,CAAA;IACR,IAAI,CAAClH,KAAK,CAACmH,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAM/F,IAAI,IAAI,IAAI,CAACgG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC7F,IAAI,CAACiE,OAAQ,EAAE;QACzC4B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC9F,IAAI,CAACtF,cAAe,EAAE;QACvDoL,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI9F,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAACtF,cAAc,EAAE;QAC5C,IAAIsF,IAAI,CAACtF,cAAc,GAAGsF,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAACtF,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAAC+D,KAAK,CAACwH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGjG,IAAI,CAACoD,YAAY,GAAG,KAAKpD,IAAI,CAACxF,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACuL,kBAAkB,IAAK,CAAC/F,IAAI,CAACxF,SAAU,EAAE;QAC5CuL,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACpH,KAAK,CAACwH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACrH,KAAK,CAACwH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACtH,KAAK,CAACwH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACnC,kBAAkB,CAAClB,GAAG,CAAEwD,CAAM,IAAI;MAChE,OAAO;QACL3N,cAAc,EAAE2N,CAAC,CAAC3N,cAAc,GAAG2N,CAAC,CAAC3N,cAAc,GAAG,IAAI;QAC1DyI,KAAK,EAAEkF,CAAC,CAAC5N,YAAY,GAAG,IAAI,CAACgN,UAAU,CAACY,CAAC,CAAC5N,YAAY,CAAC,GAAGH,SAAS;QACnE2L,kBAAkB,EAAE,IAAI,CAACc,oBAAoB,CAACsB,CAAC,CAAChN,aAAa,CAAC;QAC9DqL,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACpH,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC9G,YAAY,CAAC8L,OAAO;QACtDjI,KAAK,EAAEoK,CAAC,CAACpK,KAAK;QACdC,KAAK,EAAEmK,CAAC,CAACnK,KAAK;QACdC,SAAS,EAAEkK,CAAC,CAAClK,SAAS;QACtBzB,SAAS,EAAE2L,CAAC,CAAC3L,SAAS;QAAE;QACxB4H,WAAW,EAAE+D,CAAC,CAACvL,eAAe,CAACsB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACiJ,cAAc,CAACgB,CAAC,CAACvL,eAAe,EAAEuL,CAAC,CAACxM,kBAAkB,CAAC,IAAI,IAAI;QACxHyJ,YAAY,EAAE+C,CAAC,CAAC/C,YAAY;QAC5B1I,cAAc,EAAEyL,CAAC,CAACzL,cAAc;QAChCuJ,OAAO,EAAEkC,CAAC,CAACvL,eAAe,CAACsB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACyJ,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClH,KAAK,CAAC4H,aAAa,CAAClQ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACkI,OAAO,CAACiI,aAAa,CAAC,IAAI,CAAC7H,KAAK,CAAC4H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAACrH,KAAK,EAAE;MACd,IAAI,CAACuH,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClI,gBAAgB,CAACmI,oCAAoC,CAAC;MACzD9G,IAAI,EAAE,IAAI,CAACqG;KACZ,CAAC,CAAC7G,SAAS,CAACuE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvF,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBrD,YAAY,EAAE,IAAI,CAAChE,WAAW;MAC9BsH,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3ClH,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACwI,sCAAsC,CAAC;MAC3DnH,IAAI,EAAE,IAAI,CAACiH;KACZ,CAAC,CAACzH,SAAS,CAACuE,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACvF,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAjC,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMjH,IAAI,IAAI+G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKrH,IAAI,IAAIoH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAACjH,IAAI,CAAC,GAAG,CAAC,CAACkH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFzH,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACgJ,8DAA8D,CAAC;MAC5F5H,IAAI,EAAE,IAAI,CAACJ;KACZ,CAAC,CAACkE,IAAI,CACLvP,GAAG,CAACwP,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrK,aAAa,GAAG,IAAI,CAACoL,4BAA4B,CAACjB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACQ,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAChF,SAAS,EAAE;EACf;EACAwH,MAAMA,CAAA;IACJ,IAAI,CAAC/H,aAAa,CAACsC,IAAI,CAAC;MACtBsG,MAAM;MACNC,OAAO,EAAE,IAAI,CAAClI;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACgJ,IAAI,EAAE;EACtB;;;uCAxfWzJ,4CAA4C,EAAA1J,EAAA,CAAAoT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtT,EAAA,CAAAoT,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxT,EAAA,CAAAoT,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1T,EAAA,CAAAoT,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5T,EAAA,CAAAoT,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA7T,EAAA,CAAAoT,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA/T,EAAA,CAAAoT,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAjU,EAAA,CAAAoT,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAnU,EAAA,CAAAoT,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAApU,EAAA,CAAAoT,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5C5K,4CAA4C;MAAA6K,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzU,EAAA,CAAA0U,0BAAA,EAAA1U,EAAA,CAAA2U,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChDvDjV,EADF,CAAAQ,cAAA,iBAA0B,qBACR;UACdR,EAAA,CAAAC,SAAA,qBAAiC;UACnCD,EAAA,CAAAmB,YAAA,EAAiB;UACjBnB,EAAA,CAAAQ,cAAA,sBAA2B;UACzBR,EAAA,CAAAC,SAAA,YAA+C;UAE7CD,EADF,CAAAQ,cAAA,aAAuB,YAC4B;UAAAR,EAAA,CAAAuB,MAAA,4CAAO;UAAAvB,EAAA,CAAAmB,YAAA,EAAK;UAE7DnB,EAAA,CAAAoC,UAAA,IAAA+S,oEAAA,4BAA8E;UAmMlFnV,EADE,CAAAmB,YAAA,EAAM,EACO;UAEbnB,EADF,CAAAQ,cAAA,wBAAyF,kBACc;UAA9DR,EAAA,CAAAS,UAAA,mBAAA2U,+EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvDpS,EAAA,CAAAuB,MAAA,sBACF;UAAAvB,EAAA,CAAAmB,YAAA,EAAS;UACTnB,EAAA,CAAAQ,cAAA,kBAAgG;UAAhER,EAAA,CAAAS,UAAA,mBAAA4U,+EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAClD3R,EAAA,CAAAuB,MAAA,sBACF;UACJvB,EADI,CAAAmB,YAAA,EAAS,EAAkB,EACrB;UAGVnB,EAAA,CAAAoC,UAAA,KAAAkT,qEAAA,0BAA8E;;;;;UA9MlCtV,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAgV,GAAA,CAAA5F,kBAAA,CAAuB;UAqMLtP,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAAqV,OAAA,GAAAL,GAAA,CAAAvR,YAAA,CAAAC,OAAA,cAAA2R,OAAA,KAAA1R,SAAA,GAAA0R,OAAA,SAA0C;UAG/CvV,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAAsV,OAAA,GAAAN,GAAA,CAAAvR,YAAA,CAAAC,OAAA,cAAA4R,OAAA,KAAA3R,SAAA,GAAA2R,OAAA,SAA0C;UAM7DxV,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAgV,GAAA,CAAA5F,kBAAA,CAAuB;;;qBDzKjD7P,YAAY,EAAAyU,EAAA,CAAAuB,OAAA,EAAAvB,EAAA,CAAAwB,IAAA,EAAE9V,YAAY,EAAA+V,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAElX,gBAAgB,EAAgBK,eAAe;MAAA8W,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}