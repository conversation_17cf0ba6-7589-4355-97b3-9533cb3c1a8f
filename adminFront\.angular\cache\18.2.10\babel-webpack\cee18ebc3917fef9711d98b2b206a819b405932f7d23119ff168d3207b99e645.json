{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, DestroyRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { forkJoin, map, switchMap, tap } from 'rxjs';\nimport { CalendarModule } from 'primeng/calendar';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet PreOrderComponent = class PreOrderComponent extends BaseComponent {\n  constructor(allow, dialogService, houseService, buildCaseService, valid, message, _ultilityService) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this.houseService = houseService;\n    this.buildCaseService = buildCaseService;\n    this.valid = valid;\n    this.message = message;\n    this._ultilityService = _ultilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.buildCaseList = [];\n    this.allBuildCaseList = [];\n    this.caseList = [];\n    this.houseHoldList = [];\n    this.newHouseHightestList = [];\n    this.dateTimeList = [];\n    this.appointment = {};\n    this.searchAppointment = {};\n    this.floorList = [];\n    this.hourList = [];\n    this.destroy = inject(DestroyRef);\n  }\n  ngOnInit() {\n    this.isNewAppointment = false;\n    this.initialList();\n  }\n  initialList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(switchMap(res => {\n      this.caseList = res.Entries ?? [];\n      this.currentBuildCase = res.Entries?.[0]?.cID;\n      let listAppointmentRequest = {\n        body: {\n          CBuildCaseID: this.currentBuildCase\n        }\n      };\n      this.getHourListAppointment();\n      this.getHouseAndFloorByBuildCaseId();\n      return this.houseService.apiHouseGetListAppointmentsPost$Json(listAppointmentRequest);\n    }), takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.allBuildCaseList = res.Entries ?? [];\n      if (res.TotalItems || res.TotalItems === 0) {\n        this.controllPagination(res.TotalItems);\n      }\n    });\n  }\n  controllPagination(totalItems) {\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\n    this.totalRecords = totalItems;\n    let lastIndex = this.totalRecords < this.pageIndex * this.pageSize ? this.totalRecords + 1 : this.pageIndex * this.pageSize;\n    this.buildCaseList = this.allBuildCaseList.slice(this.pageFirst - 1, lastIndex);\n  }\n  getHouseAndFloorByBuildCaseId() {\n    return this.buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json({\n      buildCaseId: this.currentBuildCase\n    }).pipe(tap(res => {\n      if (res.Entries && res.Entries.length) {\n        this.houseHoldList = res.Entries;\n        console.log('getHouseAndFloorByBuildCaseId');\n      }\n    }), takeUntilDestroyed(this.destroy));\n  }\n  groupByCDate(data) {\n    const groupedData = data.reduce((acc, entry) => {\n      if (!acc[entry.CDate]) {\n        acc[entry.CDate] = [];\n      }\n      acc[entry.CDate].push(entry.CHour);\n      return acc;\n    }, {});\n    return Object.keys(groupedData).map(CDate => ({\n      CDate,\n      CHour: groupedData[CDate]\n    }));\n  }\n  getHourListAppointment() {\n    const req = {\n      CBuildCaseID: this.currentBuildCase\n    };\n    return this.houseService.apiHouseGetHourListAppointmentPost$Json({\n      body: req\n    }).pipe(map(res => {\n      res.Entries?.forEach(i => {\n        i.CDate = i.CDate?.split('T')[0];\n        i.CHour = i.CHour;\n      });\n      res.Entries = res.Entries?.filter(i => i.CHour >= 9 && i.CHour <= 21);\n      return res;\n    }), tap(res => {\n      if (res.Entries && res.Entries.length) {\n        this.listCurrent = res.Entries;\n        this.dateTimeList = this.groupByCDate(res.Entries);\n      }\n    }), takeUntilDestroyed(this.destroy));\n  }\n  filterOrders(listAppointmentsByCustomer, selectedData) {\n    let result;\n    if (selectedData && selectedData?.CHour && selectedData.CDate) {\n      result = selectedData.CHour ? [...selectedData.CHour] : [];\n      // Bước 1: Lọc mảng A với các phần tử có CPreOrderDate trùng với B.CDate\n      const filteredOrders = listAppointmentsByCustomer.filter(order => order.CPreOrderDate.startsWith(selectedData.CDate));\n      // Bước 2: Loại bỏ các phần tử B.CHour trùng với các giá trị CHour trong mảng filteredOrders\n      filteredOrders.forEach(order => {\n        const hourIndex = result.indexOf(order.CHour);\n        if (hourIndex !== -1) {\n          result.splice(hourIndex, 1); // Xóa phần tử trùng khỏi B.CHour\n        }\n      });\n      if (this.selectedAppointment.CPreOrderDate.startsWith(selectedData.CDate) && this.selectedAppointment.CHour) {\n        result = [...result, this.selectedAppointment.CHour];\n      }\n    }\n    return result;\n  }\n  onDateTimeChange(selectedDate) {\n    const selectedData = this.dateTimeList.find(item => item.CDate === selectedDate);\n    this.hourList = selectedData ? selectedData.CHour.sort((a, b) => a - b) : [];\n    this.hourList = this.filterOrders(this.listAppointmentsByCustomer, selectedData);\n    this.appointment.CHour = undefined;\n  }\n  getUserBuildcaseList() {\n    let request = {};\n    request.body = {};\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      this.caseList = res.Entries ?? [];\n    });\n  }\n  getPreorderList(params) {\n    let request = {\n      body: {\n        CBuildCaseID: params.body.CBuildCaseID,\n        CCustomerName: params.body.CCustomerName,\n        CPhone: params.body && params.body.CPhone,\n        CPreOrderDate: params.body && params.body.CPreOrderDate?.split(\"T\")[0]\n      }\n    };\n    this.houseService.apiHouseGetListAppointmentsPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.allBuildCaseList = res.Entries ?? [];\n        if (res.TotalItems || res.TotalItems === 0) {\n          this.controllPagination(res.TotalItems);\n        }\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  search() {\n    this.searchAppointment = {\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CCustomerName: this.currentCustomerName,\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //=== '全部' ? undefined : this.currentSelectDate\n      }\n    };\n    this.getPreorderList(this.searchAppointment);\n  }\n  onHouseHoldChange(selectedHouseHold) {\n    const selectedData = this.houseHoldList.find(item => item.CHouseHold === selectedHouseHold);\n    this.floorList = selectedData ? selectedData.Floors : [];\n    this.appointment.CFloor = undefined; // Reset selected floor\n  }\n  edit(ref, item) {\n    forkJoin({\n      hourList: this.getHourListAppointment(),\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n    }).subscribe({\n      next: () => {\n        this.selectedAppointment = item;\n        this.isNewAppointment = false;\n        this.editChangepreoderID = item.CChangePreOrderID;\n        let selectedHouseHold = this.houseHoldList.find(i => i.CHouseHold === item.CHouseHold);\n        this.appointment.CHouseHold = selectedHouseHold?.CHouseHold;\n        this.floorList = selectedHouseHold?.Floors;\n        // let currentTemp = this.listCurrent.find((i: any) => {\n        //   return (i.CDate === item.CPreOrderDate.split(\"T\")[0] && i.CHour === item.CHour)\n        // });\n        // if (!currentTemp) {\n        this.dateTimeList = this.groupByCDate([...this.listCurrent]); //, { CDate: item.CPreOrderDate.split(\"T\")[0], CHour: item.CHour }])\n        // }\n        this.listAppointmentsByCustomer = this.allBuildCaseList.filter(o => o.CCustomerName == item.CCustomerName);\n        this.onDateTimeChange(item.CPreOrderDate.split(\"T\")[0]);\n        this.appointment = {\n          CFloor: item.CFloor,\n          CHasDesigner: item.CHasDesigner,\n          CHour: item.CHour,\n          CHouseHold: item.CHouseHold,\n          CPeoples: item.CPeoples,\n          CPreOrderDate: item.CPreOrderDate.split(\"T\")[0],\n          CRemark: item.CRemark,\n          CNeedMail: item.CNeedMail,\n          CHouseRequirement: item.CHouseRequirement\n        };\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  addNewAppointment(ref) {\n    forkJoin({\n      hourList: this.getHourListAppointment(),\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\n    }).subscribe({\n      next: () => {\n        this.isNewAppointment = true;\n        this.appointment = {};\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  checkAppointmentExists() {\n    const formattedPreOrderDate = this.appointment.CPreOrderDate + 'T00:00:00';\n    return this.listAppointmentsByCustomer.some(appointment => appointment.CPreOrderDate === formattedPreOrderDate && appointment.CHour === this.appointment.CHour);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required(`戶別`, this.appointment.CHouseHold);\n    this.valid.required(`樓層`, this.appointment.CFloor);\n    this.valid.required(`日期`, this.appointment.CPreOrderDate);\n    this.valid.required(`時段`, this.appointment.CHour);\n    this.valid.required(`出席人數`, this.appointment.CPeoples);\n    this.valid.required(`設計師出席`, this.appointment.CHasDesigner);\n    // console.log('this.listAppointmentsByCustomer', this.listAppointmentsByCustomer.filter((item: any)=> item.CHouseHold ==this.appointment.CHouseHold ));\n    // console.log('this.appointment', this.appointment);\n    // if (this.listAppointmentsByCustomer && this.listAppointmentsByCustomer.length) {\n    //   if (this.checkAppointmentExists()) {\n    //     this.valid.addErrorMessage('無法選擇尚未被加入可 預約時段的日期及時段');\n    //   }\n    // }\n  }\n  deleteAppointment(item, i) {\n    if (window.confirm(`確定要刪除【項目${this.pageFirst + i}】?`)) {\n      this.houseService.apiHouseDeleteAppointmentPost$Json({\n        body: item.CChangePreOrderID\n      }).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getPreorderList({\n            body: {\n              CBuildCaseID: this.currentBuildCase\n            }\n          });\n        }\n      });\n    }\n  }\n  saveEdit(ref) {\n    let request = {\n      body: {\n        CID: this.editChangepreoderID,\n        CHasDesigner: this.appointment.CHasDesigner,\n        CHour: this.appointment.CHour,\n        CPeoples: this.appointment.CPeoples,\n        CPreOrderDate: this.appointment.CPreOrderDate,\n        CRemark: this.appointment.CRemark,\n        CStatus: this.selectedAppointment.CStatus\n      }\n    };\n    this.houseService.apiHouseEditAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getPreorderList({\n          body: {\n            CBuildCaseID: this.currentBuildCase\n          }\n        });\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    let request;\n    request = {\n      body: {\n        ...this.appointment,\n        CBuildcaseID: this.currentBuildCase\n      }\n    };\n    if (this.isNewAppointment) {\n      this.houseService.apiHouseCreateAppointmentPost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(`執行成功`);\n          this.getPreorderList({\n            body: {\n              CBuildCaseID: this.currentBuildCase\n            }\n          });\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    } else {\n      this.saveEdit(ref);\n    }\n  }\n  exportExcel() {\n    this.houseService.apiHouseExportExcelListAppointmentsPost$Json({\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CCustomerName: this.currentCustomerName,\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //this.currentSelectDate === '全部' ? undefined : this.currentSelectDate\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this._ultilityService.downloadExcelFile(res.Entries?.FileByte, '預約');\n      }\n    })).subscribe();\n  }\n};\nPreOrderComponent = __decorate([Component({\n  selector: 'app-pre-order',\n  standalone: true,\n  imports: [CommonModule, SharedModule, CalendarModule],\n  templateUrl: './pre-order.component.html',\n  styleUrls: ['./pre-order.component.scss']\n})], PreOrderComponent);\nexport { PreOrderComponent };", "map": {"version": 3, "names": ["Component", "DestroyRef", "inject", "CommonModule", "takeUntilDestroyed", "fork<PERSON><PERSON>n", "map", "switchMap", "tap", "CalendarModule", "SharedModule", "BaseComponent", "PreOrderComponent", "constructor", "allow", "dialogService", "houseService", "buildCaseService", "valid", "message", "_ultilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "buildCaseList", "allBuildCaseList", "caseList", "houseHoldList", "newHouseHightestList", "dateTimeList", "appointment", "searchAppointment", "floorList", "hourList", "destroy", "ngOnInit", "isNewAppointment", "initialList", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "Entries", "currentBuildCase", "cID", "listAppointmentRequest", "body", "CBuildCaseID", "getHourListAppointment", "getHouseAndFloorByBuildCaseId", "apiHouseGetListAppointmentsPost$Json", "subscribe", "TotalItems", "controllPagination", "totalItems", "lastIndex", "slice", "apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json", "buildCaseId", "length", "console", "log", "groupByCDate", "data", "groupedData", "reduce", "acc", "entry", "CDate", "push", "CHour", "Object", "keys", "req", "apiHouseGetHourListAppointmentPost$Json", "for<PERSON>ach", "i", "split", "filter", "listCurrent", "filterOrders", "listAppointmentsByCustomer", "selectedData", "result", "filteredOrders", "order", "CPreOrderDate", "startsWith", "hourIndex", "indexOf", "splice", "selectedAppointment", "onDateTimeChange", "selectedDate", "find", "item", "sort", "a", "b", "undefined", "getUserBuildcaseList", "request", "getPreorderList", "params", "CCustomerName", "CPhone", "StatusCode", "showErrorMSG", "Message", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "search", "currentCustomerName", "currentCustomerPhone", "currentSelectDate", "onHouseHoldChange", "selectedHouseHold", "CHouseHold", "Floors", "CFloor", "edit", "ref", "houseAndFloor", "next", "editChangepreoderID", "CChangePreOrderID", "o", "CHasDesigner", "CPeoples", "CRemark", "CNeedMail", "CHouseRequirement", "open", "addNewAppointment", "checkAppointmentExists", "formattedPreOrderDate", "some", "validation", "clear", "required", "deleteAppointment", "window", "confirm", "apiHouseDeleteAppointmentPost$Json", "showSucessMSG", "saveEdit", "CID", "CStatus", "apiHouseEditAppointmentPost$Json", "close", "save", "errorMessages", "showErrorMSGs", "CBuildcaseID", "apiHouseCreateAppointmentPost$Json", "exportExcel", "apiHouseExportExcelListAppointmentsPost$Json", "downloadExcelFile", "FileByte", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\pre-order\\pre-order.component.ts"], "sourcesContent": ["import { Component, DestroyRef, OnInit, inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ApiBuildCaseGetUserBuildCasePost$Json$Params } from 'src/services/api/fn/build-case/api-build-case-get-user-build-case-post-json';\r\nimport { ApiHouseGetListAppointmentsPost$Json$Params } from 'src/services/api/fn/house/api-house-get-list-appointments-post-json';\r\nimport { BuildCaseGetListReponse, CreateAppointmentArgs, GetAppoinmentRes, GetHouseAndFloorByBuildCaseIdRes } from 'src/services/api/models';\r\nimport { ApiHouseCreateAppointmentPost$Json$Params } from 'src/services/api/fn/house/api-house-create-appointment-post-json';\r\nimport { Observable, forkJoin, map, switchMap, tap, } from 'rxjs';\r\nimport { ApiHouseEditAppointmentPost$Json$Params } from 'src/services/api/fn/house/api-house-edit-appointment-post-json';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { CalendarModule } from 'primeng/calendar'\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n\r\nexport interface HourData {\r\n  CDate: string;\r\n  CHour: any[];\r\n}\r\n@Component({\r\n  selector: 'app-pre-order',\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, CalendarModule],\r\n  templateUrl: './pre-order.component.html',\r\n  styleUrls: ['./pre-order.component.scss']\r\n})\r\nexport class PreOrderComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private houseService: HouseService,\r\n    private buildCaseService: BuildCaseService,\r\n    private valid: ValidationHelper,\r\n    private message: MessageService,\r\n    private _ultilityService: UtilityService\r\n  ) {\r\n    super(allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  buildCaseList: GetAppoinmentRes[] = [];\r\n  allBuildCaseList: GetAppoinmentRes[] = [];\r\n  caseList: BuildCaseGetListReponse[] = [];\r\n  houseHoldList: GetHouseAndFloorByBuildCaseIdRes[] = [];\r\n  newHouseHightestList: number[] | null | undefined = [];\r\n  dateTimeList: HourData[] = [];\r\n\r\n  appointment: CreateAppointmentArgs & {\r\n    CHouseRequirement?: string | null\r\n  } = {};\r\n  searchAppointment: ApiHouseGetListAppointmentsPost$Json$Params = {}\r\n\r\n  currentBuildCase: any;\r\n  currentSelectDate: string | undefined;\r\n  currentCustomerName: string | undefined;\r\n  currentCustomerPhone: string | undefined\r\n  isNewAppointment: boolean | undefined;\r\n  editChangepreoderID: number | undefined;\r\n  floorList: any = [];\r\n  hourList: any = [];\r\n  selectedAppointment: any\r\n  destroy = inject(DestroyRef)\r\n\r\n  override ngOnInit(): void {\r\n    this.isNewAppointment = false;\r\n    this.initialList();\r\n  }\r\n\r\n\r\n  initialList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(\r\n      switchMap(res => {\r\n        this.caseList = res.Entries ?? [];\r\n        this.currentBuildCase = res.Entries?.[0]?.cID;\r\n        let listAppointmentRequest: ApiHouseGetListAppointmentsPost$Json$Params = {\r\n          body: { CBuildCaseID: this.currentBuildCase }\r\n        };\r\n        this.getHourListAppointment();\r\n        this.getHouseAndFloorByBuildCaseId()\r\n        return this.houseService.apiHouseGetListAppointmentsPost$Json(listAppointmentRequest);\r\n      }),\r\n      takeUntilDestroyed(this.destroy)\r\n    ).subscribe(res => {\r\n      this.allBuildCaseList = res.Entries ?? [];\r\n      if (res.TotalItems || res.TotalItems === 0) {\r\n        this.controllPagination(res.TotalItems);\r\n      }\r\n    });\r\n  }\r\n\r\n  controllPagination(totalItems: number) {\r\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\r\n    this.totalRecords = totalItems;\r\n    let lastIndex = (this.totalRecords < this.pageIndex * this.pageSize) ? this.totalRecords + 1 : (this.pageIndex * this.pageSize);\r\n    this.buildCaseList = this.allBuildCaseList.slice(this.pageFirst - 1, lastIndex);\r\n  }\r\n\r\n  getHouseAndFloorByBuildCaseId(): Observable<any> {\r\n    return this.buildCaseService.apiBuildCaseGetHouseAndFloorByBuildCaseIdPost$Json(\r\n      { buildCaseId: this.currentBuildCase }\r\n    )\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.Entries && res.Entries.length) {\r\n            this.houseHoldList = res.Entries;\r\n            console.log('getHouseAndFloorByBuildCaseId');\r\n          }\r\n        }),\r\n        takeUntilDestroyed(this.destroy)\r\n      );\r\n  }\r\n\r\n  groupByCDate(data: { CDate?: string; CHour?: number }[]): HourData[] {\r\n    const groupedData: { [key: string]: number[] } = data.reduce((acc: any, entry: any) => {\r\n      if (!acc[entry.CDate]) {\r\n        acc[entry.CDate] = [];\r\n      }\r\n      acc[entry.CDate].push(entry.CHour);\r\n      return acc;\r\n    }, {});\r\n\r\n    return Object.keys(groupedData).map(CDate => ({\r\n      CDate,\r\n      CHour: groupedData[CDate]\r\n    }));\r\n  }\r\n\r\n  listCurrent: any\r\n  getHourListAppointment(): Observable<any> {\r\n    const req = { CBuildCaseID: this.currentBuildCase };\r\n    return this.houseService.apiHouseGetHourListAppointmentPost$Json({ body: req })\r\n      .pipe(\r\n        map(res => {\r\n          res.Entries?.forEach(i => {\r\n            i.CDate = i.CDate?.split('T')[0];\r\n            i.CHour = i.CHour;\r\n          });\r\n          res.Entries = res.Entries?.filter((i: any) => i.CHour >= 9 && i.CHour <= 21);\r\n          return res\r\n        }),\r\n        tap(res => {\r\n          if (res.Entries && res.Entries.length) {\r\n            this.listCurrent = res.Entries;\r\n            this.dateTimeList = this.groupByCDate(res.Entries);\r\n          }\r\n        }),\r\n        takeUntilDestroyed(this.destroy)\r\n      );\r\n  }\r\n\r\n  filterOrders(listAppointmentsByCustomer: any[], selectedData: any) { //The same customer cannot book multiple house at the same time.\r\n    let result: any\r\n    if (selectedData && selectedData?.CHour && selectedData.CDate) {\r\n      result = selectedData.CHour ? [...selectedData.CHour] : []\r\n      // Bước 1: Lọc mảng A với các phần tử có CPreOrderDate trùng với B.CDate\r\n      const filteredOrders = listAppointmentsByCustomer.filter(order => order.CPreOrderDate.startsWith(selectedData.CDate));\r\n      // Bước 2: Loại bỏ các phần tử B.CHour trùng với các giá trị CHour trong mảng filteredOrders\r\n      filteredOrders.forEach(order => {\r\n        const hourIndex = result.indexOf(order.CHour);\r\n        if (hourIndex !== -1) {\r\n          result.splice(hourIndex, 1); // Xóa phần tử trùng khỏi B.CHour\r\n        }\r\n      });\r\n      if (this.selectedAppointment.CPreOrderDate.startsWith(selectedData.CDate) && this.selectedAppointment.CHour) {\r\n        result = [...result, this.selectedAppointment.CHour]\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  onDateTimeChange(selectedDate: string) {\r\n    const selectedData: HourData | any = this.dateTimeList.find(item => item.CDate === selectedDate);\r\n    this.hourList = selectedData ? selectedData.CHour.sort((a: number, b: number) => a - b) : [];\r\n    this.hourList = this.filterOrders(this.listAppointmentsByCustomer, selectedData)\r\n    this.appointment.CHour = undefined;\r\n  }\r\n\r\n  getUserBuildcaseList() {\r\n    let request: ApiBuildCaseGetUserBuildCasePost$Json$Params = {};\r\n    request.body = {}\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json(request).pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n      this.caseList = res.Entries ?? [];\r\n    })\r\n  }\r\n\r\n  getPreorderList(params: ApiHouseGetListAppointmentsPost$Json$Params) {\r\n    let request: ApiHouseGetListAppointmentsPost$Json$Params = {\r\n      body: {\r\n        CBuildCaseID: params.body!.CBuildCaseID,\r\n        CCustomerName: params.body!.CCustomerName,\r\n        CPhone: params.body && params.body.CPhone,\r\n        CPreOrderDate: params.body && params.body.CPreOrderDate?.split(\"T\")[0],\r\n      }\r\n    };\r\n    this.houseService.apiHouseGetListAppointmentsPost$Json(request)\r\n      .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.allBuildCaseList = res.Entries ?? [];\r\n          if (res.TotalItems || res.TotalItems === 0) {\r\n            this.controllPagination(res.TotalItems);\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      })\r\n  }\r\n  listAppointmentsByCustomer: any\r\n\r\n  formatDate(date: any) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  search() {\r\n    this.searchAppointment = {\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CCustomerName: this.currentCustomerName,\r\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\r\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined //=== '全部' ? undefined : this.currentSelectDate\r\n      }\r\n    }\r\n    this.getPreorderList(this.searchAppointment)\r\n  }\r\n\r\n\r\n  onHouseHoldChange(selectedHouseHold: string) {\r\n    const selectedData = this.houseHoldList.find(item => item.CHouseHold === selectedHouseHold);\r\n    this.floorList = selectedData ? selectedData.Floors : [];\r\n    this.appointment.CFloor = undefined; // Reset selected floor\r\n  }\r\n\r\n  edit(ref: any, item: any) {\r\n    forkJoin({\r\n      hourList: this.getHourListAppointment(),\r\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\r\n    }).subscribe({\r\n      next: () => {\r\n        this.selectedAppointment = item\r\n        this.isNewAppointment = false;\r\n        this.editChangepreoderID = item.CChangePreOrderID;\r\n        let selectedHouseHold = this.houseHoldList.find(i => i.CHouseHold === item.CHouseHold);\r\n        this.appointment.CHouseHold = selectedHouseHold?.CHouseHold\r\n        this.floorList = selectedHouseHold?.Floors\r\n        // let currentTemp = this.listCurrent.find((i: any) => {\r\n        //   return (i.CDate === item.CPreOrderDate.split(\"T\")[0] && i.CHour === item.CHour)\r\n        // });\r\n        // if (!currentTemp) {\r\n        this.dateTimeList = this.groupByCDate([...this.listCurrent])//, { CDate: item.CPreOrderDate.split(\"T\")[0], CHour: item.CHour }])\r\n        // }\r\n        this.listAppointmentsByCustomer = this.allBuildCaseList.filter((o: any) => o.CCustomerName == item.CCustomerName)\r\n\r\n        this.onDateTimeChange(item.CPreOrderDate.split(\"T\")[0])\r\n        this.appointment = {\r\n          CFloor: item.CFloor,\r\n          CHasDesigner: item.CHasDesigner,\r\n          CHour: item.CHour,\r\n          CHouseHold: item.CHouseHold,\r\n          CPeoples: item.CPeoples,\r\n          CPreOrderDate: item.CPreOrderDate.split(\"T\")[0],\r\n          CRemark: item.CRemark,\r\n          CNeedMail: item.CNeedMail,\r\n          CHouseRequirement: item.CHouseRequirement\r\n        }\r\n        this.dialogService.open(ref);\r\n      },\r\n    });\r\n  }\r\n\r\n  addNewAppointment(ref: any) {\r\n    forkJoin({\r\n      hourList: this.getHourListAppointment(),\r\n      houseAndFloor: this.getHouseAndFloorByBuildCaseId()\r\n    }).subscribe({\r\n      next: () => {\r\n        this.isNewAppointment = true;\r\n        this.appointment = {};\r\n        this.dialogService.open(ref);\r\n      },\r\n    });\r\n  }\r\n\r\n  checkAppointmentExists(): boolean {\r\n    const formattedPreOrderDate = this.appointment.CPreOrderDate + 'T00:00:00';\r\n    return this.listAppointmentsByCustomer.some((appointment: any) =>\r\n      appointment.CPreOrderDate === formattedPreOrderDate &&\r\n      appointment.CHour === this.appointment.CHour\r\n    );\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required(`戶別`, this.appointment.CHouseHold);\r\n    this.valid.required(`樓層`, this.appointment.CFloor);\r\n    this.valid.required(`日期`, this.appointment.CPreOrderDate);\r\n    this.valid.required(`時段`, this.appointment.CHour);\r\n    this.valid.required(`出席人數`, this.appointment.CPeoples);\r\n    this.valid.required(`設計師出席`, this.appointment.CHasDesigner);\r\n    // console.log('this.listAppointmentsByCustomer', this.listAppointmentsByCustomer.filter((item: any)=> item.CHouseHold ==this.appointment.CHouseHold ));\r\n    // console.log('this.appointment', this.appointment);\r\n\r\n    // if (this.listAppointmentsByCustomer && this.listAppointmentsByCustomer.length) {\r\n    //   if (this.checkAppointmentExists()) {\r\n    //     this.valid.addErrorMessage('無法選擇尚未被加入可 預約時段的日期及時段');\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  deleteAppointment(item: any, i: number) {\r\n    if (window.confirm(`確定要刪除【項目${this.pageFirst + i}】?`)) {\r\n      this.houseService.apiHouseDeleteAppointmentPost$Json({ body: item.CChangePreOrderID })\r\n        .pipe(takeUntilDestroyed(this.destroy))\r\n        .subscribe(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n            this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n          }\r\n        })\r\n    }\r\n  }\r\n\r\n  saveEdit(ref: any) {\r\n    let request: ApiHouseEditAppointmentPost$Json$Params = {\r\n      body: {\r\n        CID: this.editChangepreoderID,\r\n        CHasDesigner: this.appointment.CHasDesigner,\r\n        CHour: this.appointment.CHour,\r\n        CPeoples: this.appointment.CPeoples,\r\n        CPreOrderDate: this.appointment.CPreOrderDate,\r\n        CRemark: this.appointment.CRemark,\r\n        CStatus: this.selectedAppointment.CStatus\r\n      }\r\n    }\r\n\r\n    this.houseService.apiHouseEditAppointmentPost$Json(request)\r\n      .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n          ref.close()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    let request: ApiHouseCreateAppointmentPost$Json$Params;\r\n\r\n    request = {\r\n      body: {\r\n        ...this.appointment,\r\n        CBuildcaseID: this.currentBuildCase\r\n      }\r\n    };\r\n\r\n    if (this.isNewAppointment) {\r\n      this.houseService.apiHouseCreateAppointmentPost$Json(request)\r\n        .pipe(takeUntilDestroyed(this.destroy)).subscribe(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`執行成功`);\r\n            this.getPreorderList({ body: { CBuildCaseID: this.currentBuildCase } });\r\n            ref.close()\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!);\r\n          }\r\n        })\r\n    } else {\r\n      this.saveEdit(ref)\r\n    }\r\n  }\r\n\r\n  exportExcel() {\r\n    this.houseService.apiHouseExportExcelListAppointmentsPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CCustomerName: this.currentCustomerName,\r\n        CPhone: this.currentCustomerPhone !== undefined ? this.currentCustomerPhone : undefined,\r\n        CPreOrderDate: this.currentSelectDate ? this.formatDate(this.currentSelectDate) : undefined//this.currentSelectDate === '全部' ? undefined : this.currentSelectDate\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(res.Entries?.FileByte!, '預約')\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,UAAU,EAAUC,MAAM,QAAQ,eAAe;AACrE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,kBAAkB,QAAQ,4BAA4B;AAM/D,SAAqBC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAS,MAAM;AAMjE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AAc5D,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAkB,SAAQD,aAAa;EAClDE,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,YAA0B,EAC1BC,gBAAkC,EAClCC,KAAuB,EACvBC,OAAuB,EACvBC,gBAAgC;IAExC,KAAK,CAACN,KAAK,CAAC;IARO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAKjB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAuB,EAAE;IACtC,KAAAC,gBAAgB,GAAuB,EAAE;IACzC,KAAAC,QAAQ,GAA8B,EAAE;IACxC,KAAAC,aAAa,GAAuC,EAAE;IACtD,KAAAC,oBAAoB,GAAgC,EAAE;IACtD,KAAAC,YAAY,GAAe,EAAE;IAE7B,KAAAC,WAAW,GAEP,EAAE;IACN,KAAAC,iBAAiB,GAAgD,EAAE;IAQnE,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,QAAQ,GAAQ,EAAE;IAElB,KAAAC,OAAO,GAAGjC,MAAM,CAACD,UAAU,CAAC;EA5B5B;EA8BSmC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,WAAW,EAAE;EACpB;EAGAA,WAAWA,CAAA;IACT,IAAI,CAACrB,gBAAgB,CAACsB,qCAAqC,CAAC,EAAE,CAAC,CAACC,IAAI,CAClEjC,SAAS,CAACkC,GAAG,IAAG;MACd,IAAI,CAACd,QAAQ,GAAGc,GAAG,CAACC,OAAO,IAAI,EAAE;MACjC,IAAI,CAACC,gBAAgB,GAAGF,GAAG,CAACC,OAAO,GAAG,CAAC,CAAC,EAAEE,GAAG;MAC7C,IAAIC,sBAAsB,GAAgD;QACxEC,IAAI,EAAE;UAAEC,YAAY,EAAE,IAAI,CAACJ;QAAgB;OAC5C;MACD,IAAI,CAACK,sBAAsB,EAAE;MAC7B,IAAI,CAACC,6BAA6B,EAAE;MACpC,OAAO,IAAI,CAACjC,YAAY,CAACkC,oCAAoC,CAACL,sBAAsB,CAAC;IACvF,CAAC,CAAC,EACFzC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CACjC,CAACgB,SAAS,CAACV,GAAG,IAAG;MAChB,IAAI,CAACf,gBAAgB,GAAGe,GAAG,CAACC,OAAO,IAAI,EAAE;MACzC,IAAID,GAAG,CAACW,UAAU,IAAIX,GAAG,CAACW,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACC,kBAAkB,CAACZ,GAAG,CAACW,UAAU,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAACC,UAAkB;IACnC,IAAI,CAACjC,SAAS,GAAG,CAAC,IAAI,CAACE,SAAS,GAAG,CAAC,IAAI,IAAI,CAACD,QAAQ,GAAG,CAAC;IACzD,IAAI,CAACE,YAAY,GAAG8B,UAAU;IAC9B,IAAIC,SAAS,GAAI,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAQ,GAAI,IAAI,CAACE,YAAY,GAAG,CAAC,GAAI,IAAI,CAACD,SAAS,GAAG,IAAI,CAACD,QAAS;IAC/H,IAAI,CAACG,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC8B,KAAK,CAAC,IAAI,CAACnC,SAAS,GAAG,CAAC,EAAEkC,SAAS,CAAC;EACjF;EAEAN,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAAChC,gBAAgB,CAACwC,kDAAkD,CAC7E;MAAEC,WAAW,EAAE,IAAI,CAACf;IAAgB,CAAE,CACvC,CACEH,IAAI,CACHhC,GAAG,CAACiC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACiB,MAAM,EAAE;QACrC,IAAI,CAAC/B,aAAa,GAAGa,GAAG,CAACC,OAAO;QAChCkB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,EACFzD,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CACjC;EACL;EAEA2B,YAAYA,CAACC,IAA0C;IACrD,MAAMC,WAAW,GAAgCD,IAAI,CAACE,MAAM,CAAC,CAACC,GAAQ,EAAEC,KAAU,KAAI;MACpF,IAAI,CAACD,GAAG,CAACC,KAAK,CAACC,KAAK,CAAC,EAAE;QACrBF,GAAG,CAACC,KAAK,CAACC,KAAK,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACC,KAAK,CAACC,KAAK,CAAC,CAACC,IAAI,CAACF,KAAK,CAACG,KAAK,CAAC;MAClC,OAAOJ,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;IAEN,OAAOK,MAAM,CAACC,IAAI,CAACR,WAAW,CAAC,CAAC1D,GAAG,CAAC8D,KAAK,KAAK;MAC5CA,KAAK;MACLE,KAAK,EAAEN,WAAW,CAACI,KAAK;KACzB,CAAC,CAAC;EACL;EAGApB,sBAAsBA,CAAA;IACpB,MAAMyB,GAAG,GAAG;MAAE1B,YAAY,EAAE,IAAI,CAACJ;IAAgB,CAAE;IACnD,OAAO,IAAI,CAAC3B,YAAY,CAAC0D,uCAAuC,CAAC;MAAE5B,IAAI,EAAE2B;IAAG,CAAE,CAAC,CAC5EjC,IAAI,CACHlC,GAAG,CAACmC,GAAG,IAAG;MACRA,GAAG,CAACC,OAAO,EAAEiC,OAAO,CAACC,CAAC,IAAG;QACvBA,CAAC,CAACR,KAAK,GAAGQ,CAAC,CAACR,KAAK,EAAES,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChCD,CAAC,CAACN,KAAK,GAAGM,CAAC,CAACN,KAAK;MACnB,CAAC,CAAC;MACF7B,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,EAAEoC,MAAM,CAAEF,CAAM,IAAKA,CAAC,CAACN,KAAK,IAAI,CAAC,IAAIM,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;MAC5E,OAAO7B,GAAG;IACZ,CAAC,CAAC,EACFjC,GAAG,CAACiC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACiB,MAAM,EAAE;QACrC,IAAI,CAACoB,WAAW,GAAGtC,GAAG,CAACC,OAAO;QAC9B,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACgC,YAAY,CAACrB,GAAG,CAACC,OAAO,CAAC;MACpD;IACF,CAAC,CAAC,EACFtC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CACjC;EACL;EAEA6C,YAAYA,CAACC,0BAAiC,EAAEC,YAAiB;IAC/D,IAAIC,MAAW;IACf,IAAID,YAAY,IAAIA,YAAY,EAAEZ,KAAK,IAAIY,YAAY,CAACd,KAAK,EAAE;MAC7De,MAAM,GAAGD,YAAY,CAACZ,KAAK,GAAG,CAAC,GAAGY,YAAY,CAACZ,KAAK,CAAC,GAAG,EAAE;MAC1D;MACA,MAAMc,cAAc,GAAGH,0BAA0B,CAACH,MAAM,CAACO,KAAK,IAAIA,KAAK,CAACC,aAAa,CAACC,UAAU,CAACL,YAAY,CAACd,KAAK,CAAC,CAAC;MACrH;MACAgB,cAAc,CAACT,OAAO,CAACU,KAAK,IAAG;QAC7B,MAAMG,SAAS,GAAGL,MAAM,CAACM,OAAO,CAACJ,KAAK,CAACf,KAAK,CAAC;QAC7C,IAAIkB,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBL,MAAM,CAACO,MAAM,CAACF,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;MACF,IAAI,IAAI,CAACG,mBAAmB,CAACL,aAAa,CAACC,UAAU,CAACL,YAAY,CAACd,KAAK,CAAC,IAAI,IAAI,CAACuB,mBAAmB,CAACrB,KAAK,EAAE;QAC3Ga,MAAM,GAAG,CAAC,GAAGA,MAAM,EAAE,IAAI,CAACQ,mBAAmB,CAACrB,KAAK,CAAC;MACtD;IACF;IACA,OAAOa,MAAM;EACf;EAEAS,gBAAgBA,CAACC,YAAoB;IACnC,MAAMX,YAAY,GAAmB,IAAI,CAACpD,YAAY,CAACgE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC3B,KAAK,KAAKyB,YAAY,CAAC;IAChG,IAAI,CAAC3D,QAAQ,GAAGgD,YAAY,GAAGA,YAAY,CAACZ,KAAK,CAAC0B,IAAI,CAAC,CAACC,CAAS,EAAEC,CAAS,KAAKD,CAAC,GAAGC,CAAC,CAAC,GAAG,EAAE;IAC5F,IAAI,CAAChE,QAAQ,GAAG,IAAI,CAAC8C,YAAY,CAAC,IAAI,CAACC,0BAA0B,EAAEC,YAAY,CAAC;IAChF,IAAI,CAACnD,WAAW,CAACuC,KAAK,GAAG6B,SAAS;EACpC;EAEAC,oBAAoBA,CAAA;IAClB,IAAIC,OAAO,GAAiD,EAAE;IAC9DA,OAAO,CAACvD,IAAI,GAAG,EAAE;IACjB,IAAI,CAAC7B,gBAAgB,CAACsB,qCAAqC,CAAC8D,OAAO,CAAC,CAAC7D,IAAI,CAACpC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAACgB,SAAS,CAACV,GAAG,IAAG;MAC1H,IAAI,CAACd,QAAQ,GAAGc,GAAG,CAACC,OAAO,IAAI,EAAE;IACnC,CAAC,CAAC;EACJ;EAEA4D,eAAeA,CAACC,MAAmD;IACjE,IAAIF,OAAO,GAAgD;MACzDvD,IAAI,EAAE;QACJC,YAAY,EAAEwD,MAAM,CAACzD,IAAK,CAACC,YAAY;QACvCyD,aAAa,EAAED,MAAM,CAACzD,IAAK,CAAC0D,aAAa;QACzCC,MAAM,EAAEF,MAAM,CAACzD,IAAI,IAAIyD,MAAM,CAACzD,IAAI,CAAC2D,MAAM;QACzCnB,aAAa,EAAEiB,MAAM,CAACzD,IAAI,IAAIyD,MAAM,CAACzD,IAAI,CAACwC,aAAa,EAAET,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;KAExE;IACD,IAAI,CAAC7D,YAAY,CAACkC,oCAAoC,CAACmD,OAAO,CAAC,CAC5D7D,IAAI,CAACpC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAACgB,SAAS,CAACV,GAAG,IAAG;MACtD,IAAIA,GAAG,CAACiE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChF,gBAAgB,GAAGe,GAAG,CAACC,OAAO,IAAI,EAAE;QACzC,IAAID,GAAG,CAACW,UAAU,IAAIX,GAAG,CAACW,UAAU,KAAK,CAAC,EAAE;UAC1C,IAAI,CAACC,kBAAkB,CAACZ,GAAG,CAACW,UAAU,CAAC;QACzC;MACF,CAAC,MAAM;QACL,IAAI,CAACjC,OAAO,CAACwF,YAAY,CAAClE,GAAG,CAACmE,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACN;EAGAC,UAAUA,CAACC,IAAS;IAClB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACJ,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACJ,IAAI,CAACQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAACvF,iBAAiB,GAAG;MACvBc,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,gBAAgB;QACnC6D,aAAa,EAAE,IAAI,CAACgB,mBAAmB;QACvCf,MAAM,EAAE,IAAI,CAACgB,oBAAoB,KAAKtB,SAAS,GAAG,IAAI,CAACsB,oBAAoB,GAAGtB,SAAS;QACvFb,aAAa,EAAE,IAAI,CAACoC,iBAAiB,GAAG,IAAI,CAACb,UAAU,CAAC,IAAI,CAACa,iBAAiB,CAAC,GAAGvB,SAAS,CAAC;;KAE/F;IACD,IAAI,CAACG,eAAe,CAAC,IAAI,CAACtE,iBAAiB,CAAC;EAC9C;EAGA2F,iBAAiBA,CAACC,iBAAyB;IACzC,MAAM1C,YAAY,GAAG,IAAI,CAACtD,aAAa,CAACkE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC8B,UAAU,KAAKD,iBAAiB,CAAC;IAC3F,IAAI,CAAC3F,SAAS,GAAGiD,YAAY,GAAGA,YAAY,CAAC4C,MAAM,GAAG,EAAE;IACxD,IAAI,CAAC/F,WAAW,CAACgG,MAAM,GAAG5B,SAAS,CAAC,CAAC;EACvC;EAEA6B,IAAIA,CAACC,GAAQ,EAAElC,IAAS;IACtB1F,QAAQ,CAAC;MACP6B,QAAQ,EAAE,IAAI,CAACc,sBAAsB,EAAE;MACvCkF,aAAa,EAAE,IAAI,CAACjF,6BAA6B;KAClD,CAAC,CAACE,SAAS,CAAC;MACXgF,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxC,mBAAmB,GAAGI,IAAI;QAC/B,IAAI,CAAC1D,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAAC+F,mBAAmB,GAAGrC,IAAI,CAACsC,iBAAiB;QACjD,IAAIT,iBAAiB,GAAG,IAAI,CAAChG,aAAa,CAACkE,IAAI,CAAClB,CAAC,IAAIA,CAAC,CAACiD,UAAU,KAAK9B,IAAI,CAAC8B,UAAU,CAAC;QACtF,IAAI,CAAC9F,WAAW,CAAC8F,UAAU,GAAGD,iBAAiB,EAAEC,UAAU;QAC3D,IAAI,CAAC5F,SAAS,GAAG2F,iBAAiB,EAAEE,MAAM;QAC1C;QACA;QACA;QACA;QACA,IAAI,CAAChG,YAAY,GAAG,IAAI,CAACgC,YAAY,CAAC,CAAC,GAAG,IAAI,CAACiB,WAAW,CAAC,CAAC;QAC5D;QACA,IAAI,CAACE,0BAA0B,GAAG,IAAI,CAACvD,gBAAgB,CAACoD,MAAM,CAAEwD,CAAM,IAAKA,CAAC,CAAC9B,aAAa,IAAIT,IAAI,CAACS,aAAa,CAAC;QAEjH,IAAI,CAACZ,gBAAgB,CAACG,IAAI,CAACT,aAAa,CAACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC9C,WAAW,GAAG;UACjBgG,MAAM,EAAEhC,IAAI,CAACgC,MAAM;UACnBQ,YAAY,EAAExC,IAAI,CAACwC,YAAY;UAC/BjE,KAAK,EAAEyB,IAAI,CAACzB,KAAK;UACjBuD,UAAU,EAAE9B,IAAI,CAAC8B,UAAU;UAC3BW,QAAQ,EAAEzC,IAAI,CAACyC,QAAQ;UACvBlD,aAAa,EAAES,IAAI,CAACT,aAAa,CAACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAC/C4D,OAAO,EAAE1C,IAAI,CAAC0C,OAAO;UACrBC,SAAS,EAAE3C,IAAI,CAAC2C,SAAS;UACzBC,iBAAiB,EAAE5C,IAAI,CAAC4C;SACzB;QACD,IAAI,CAAC5H,aAAa,CAAC6H,IAAI,CAACX,GAAG,CAAC;MAC9B;KACD,CAAC;EACJ;EAEAY,iBAAiBA,CAACZ,GAAQ;IACxB5H,QAAQ,CAAC;MACP6B,QAAQ,EAAE,IAAI,CAACc,sBAAsB,EAAE;MACvCkF,aAAa,EAAE,IAAI,CAACjF,6BAA6B;KAClD,CAAC,CAACE,SAAS,CAAC;MACXgF,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9F,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAACN,WAAW,GAAG,EAAE;QACrB,IAAI,CAAChB,aAAa,CAAC6H,IAAI,CAACX,GAAG,CAAC;MAC9B;KACD,CAAC;EACJ;EAEAa,sBAAsBA,CAAA;IACpB,MAAMC,qBAAqB,GAAG,IAAI,CAAChH,WAAW,CAACuD,aAAa,GAAG,WAAW;IAC1E,OAAO,IAAI,CAACL,0BAA0B,CAAC+D,IAAI,CAAEjH,WAAgB,IAC3DA,WAAW,CAACuD,aAAa,KAAKyD,qBAAqB,IACnDhH,WAAW,CAACuC,KAAK,KAAK,IAAI,CAACvC,WAAW,CAACuC,KAAK,CAC7C;EACH;EAEA2E,UAAUA,CAAA;IACR,IAAI,CAAC/H,KAAK,CAACgI,KAAK,EAAE;IAClB,IAAI,CAAChI,KAAK,CAACiI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACpH,WAAW,CAAC8F,UAAU,CAAC;IACtD,IAAI,CAAC3G,KAAK,CAACiI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACpH,WAAW,CAACgG,MAAM,CAAC;IAClD,IAAI,CAAC7G,KAAK,CAACiI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACpH,WAAW,CAACuD,aAAa,CAAC;IACzD,IAAI,CAACpE,KAAK,CAACiI,QAAQ,CAAC,IAAI,EAAE,IAAI,CAACpH,WAAW,CAACuC,KAAK,CAAC;IACjD,IAAI,CAACpD,KAAK,CAACiI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpH,WAAW,CAACyG,QAAQ,CAAC;IACtD,IAAI,CAACtH,KAAK,CAACiI,QAAQ,CAAC,OAAO,EAAE,IAAI,CAACpH,WAAW,CAACwG,YAAY,CAAC;IAC3D;IACA;IAEA;IACA;IACA;IACA;IACA;EACF;EAEAa,iBAAiBA,CAACrD,IAAS,EAAEnB,CAAS;IACpC,IAAIyE,MAAM,CAACC,OAAO,CAAC,WAAW,IAAI,CAACjI,SAAS,GAAGuD,CAAC,IAAI,CAAC,EAAE;MACrD,IAAI,CAAC5D,YAAY,CAACuI,kCAAkC,CAAC;QAAEzG,IAAI,EAAEiD,IAAI,CAACsC;MAAiB,CAAE,CAAC,CACnF7F,IAAI,CAACpC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CACtCgB,SAAS,CAACV,GAAG,IAAG;QACf,IAAIA,GAAG,CAACiE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACvF,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAClD,eAAe,CAAC;YAAExD,IAAI,EAAE;cAAEC,YAAY,EAAE,IAAI,CAACJ;YAAgB;UAAE,CAAE,CAAC;QACzE;MACF,CAAC,CAAC;IACN;EACF;EAEA8G,QAAQA,CAACxB,GAAQ;IACf,IAAI5B,OAAO,GAA4C;MACrDvD,IAAI,EAAE;QACJ4G,GAAG,EAAE,IAAI,CAACtB,mBAAmB;QAC7BG,YAAY,EAAE,IAAI,CAACxG,WAAW,CAACwG,YAAY;QAC3CjE,KAAK,EAAE,IAAI,CAACvC,WAAW,CAACuC,KAAK;QAC7BkE,QAAQ,EAAE,IAAI,CAACzG,WAAW,CAACyG,QAAQ;QACnClD,aAAa,EAAE,IAAI,CAACvD,WAAW,CAACuD,aAAa;QAC7CmD,OAAO,EAAE,IAAI,CAAC1G,WAAW,CAAC0G,OAAO;QACjCkB,OAAO,EAAE,IAAI,CAAChE,mBAAmB,CAACgE;;KAErC;IAED,IAAI,CAAC3I,YAAY,CAAC4I,gCAAgC,CAACvD,OAAO,CAAC,CACxD7D,IAAI,CAACpC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAACgB,SAAS,CAACV,GAAG,IAAG;MACtD,IAAIA,GAAG,CAACiE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvF,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAClD,eAAe,CAAC;UAAExD,IAAI,EAAE;YAAEC,YAAY,EAAE,IAAI,CAACJ;UAAgB;QAAE,CAAE,CAAC;QACvEsF,GAAG,CAAC4B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAC1I,OAAO,CAACwF,YAAY,CAAClE,GAAG,CAACmE,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACN;EAEAkD,IAAIA,CAAC7B,GAAQ;IACX,IAAI,CAACgB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC/H,KAAK,CAAC6I,aAAa,CAACpG,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxC,OAAO,CAAC6I,aAAa,CAAC,IAAI,CAAC9I,KAAK,CAAC6I,aAAa,CAAC;MACpD;IACF;IAEA,IAAI1D,OAAkD;IAEtDA,OAAO,GAAG;MACRvD,IAAI,EAAE;QACJ,GAAG,IAAI,CAACf,WAAW;QACnBkI,YAAY,EAAE,IAAI,CAACtH;;KAEtB;IAED,IAAI,IAAI,CAACN,gBAAgB,EAAE;MACzB,IAAI,CAACrB,YAAY,CAACkJ,kCAAkC,CAAC7D,OAAO,CAAC,CAC1D7D,IAAI,CAACpC,kBAAkB,CAAC,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAACgB,SAAS,CAACV,GAAG,IAAG;QACtD,IAAIA,GAAG,CAACiE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACvF,OAAO,CAACqI,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAClD,eAAe,CAAC;YAAExD,IAAI,EAAE;cAAEC,YAAY,EAAE,IAAI,CAACJ;YAAgB;UAAE,CAAE,CAAC;UACvEsF,GAAG,CAAC4B,KAAK,EAAE;QACb,CAAC,MAAM;UACL,IAAI,CAAC1I,OAAO,CAACwF,YAAY,CAAClE,GAAG,CAACmE,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAI,CAAC6C,QAAQ,CAACxB,GAAG,CAAC;IACpB;EACF;EAEAkC,WAAWA,CAAA;IACT,IAAI,CAACnJ,YAAY,CAACoJ,4CAA4C,CAAC;MAC7DtH,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACJ,gBAAgB;QACnC6D,aAAa,EAAE,IAAI,CAACgB,mBAAmB;QACvCf,MAAM,EAAE,IAAI,CAACgB,oBAAoB,KAAKtB,SAAS,GAAG,IAAI,CAACsB,oBAAoB,GAAGtB,SAAS;QACvFb,aAAa,EAAE,IAAI,CAACoC,iBAAiB,GAAG,IAAI,CAACb,UAAU,CAAC,IAAI,CAACa,iBAAiB,CAAC,GAAGvB,SAAS;;KAE9F,CAAC,CAAC3D,IAAI,CACLhC,GAAG,CAACiC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACiE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACtF,gBAAgB,CAACiJ,iBAAiB,CAAC5H,GAAG,CAACC,OAAO,EAAE4H,QAAS,EAAE,IAAI,CAAC;MACvE;IACF,CAAC,CAAC,CACH,CAACnH,SAAS,EAAE;EACf;CAED;AAvXYvC,iBAAiB,GAAA2J,UAAA,EAP7BvK,SAAS,CAAC;EACTwK,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACvK,YAAY,EAAEO,YAAY,EAAED,cAAc,CAAC;EACrDkK,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACWhK,iBAAiB,CAuX7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}