{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPreOrderSettingGetPreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';\nimport { apiPreOrderSettingGetPreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';\nimport { apiPreOrderSettingSavePreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';\nimport { apiPreOrderSettingSavePreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class PreOrderSettingService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiPreOrderSettingGetPreOrderSettingPost()` */\n  static {\n    this.ApiPreOrderSettingGetPreOrderSettingPostPath = '/api/PreOrderSetting/GetPreOrderSetting';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context) {\n    return apiPreOrderSettingGetPreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingGetPreOrderSettingPost$Plain(params, context) {\n    return this.apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context) {\n    return apiPreOrderSettingGetPreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingGetPreOrderSettingPost$Json(params, context) {\n    return this.apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiPreOrderSettingSavePreOrderSettingPost()` */\n  static {\n    this.ApiPreOrderSettingSavePreOrderSettingPostPath = '/api/PreOrderSetting/SavePreOrderSetting';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context) {\n    return apiPreOrderSettingSavePreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingSavePreOrderSettingPost$Plain(params, context) {\n    return this.apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context) {\n    return apiPreOrderSettingSavePreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiPreOrderSettingSavePreOrderSettingPost$Json(params, context) {\n    return this.apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function PreOrderSettingService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PreOrderSettingService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PreOrderSettingService,\n      factory: PreOrderSettingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiPreOrderSettingGetPreOrderSettingPost$Json", "apiPreOrderSettingGetPreOrderSettingPost$Plain", "apiPreOrderSettingSavePreOrderSettingPost$Json", "apiPreOrderSettingSavePreOrderSettingPost$Plain", "PreOrderSettingService", "constructor", "config", "http", "ApiPreOrderSettingGetPreOrderSettingPostPath", "apiPreOrderSettingGetPreOrderSettingPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiPreOrderSettingGetPreOrderSettingPost$Json$Response", "ApiPreOrderSettingSavePreOrderSettingPostPath", "apiPreOrderSettingSavePreOrderSettingPost$Plain$Response", "apiPreOrderSettingSavePreOrderSettingPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\pre-order-setting.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiPreOrderSettingGetPreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';\r\nimport { ApiPreOrderSettingGetPreOrderSettingPost$Json$Params } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-json';\r\nimport { apiPreOrderSettingGetPreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';\r\nimport { ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params } from '../fn/pre-order-setting/api-pre-order-setting-get-pre-order-setting-post-plain';\r\nimport { apiPreOrderSettingSavePreOrderSettingPost$Json } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';\r\nimport { ApiPreOrderSettingSavePreOrderSettingPost$Json$Params } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-json';\r\nimport { apiPreOrderSettingSavePreOrderSettingPost$Plain } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';\r\nimport { ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params } from '../fn/pre-order-setting/api-pre-order-setting-save-pre-order-setting-post-plain';\r\nimport { GetPreOrderSettingResponseListResponseBase } from '../models/get-pre-order-setting-response-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class PreOrderSettingService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiPreOrderSettingGetPreOrderSettingPost()` */\r\n  static readonly ApiPreOrderSettingGetPreOrderSettingPostPath = '/api/PreOrderSetting/GetPreOrderSetting';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params?: ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>> {\r\n    return apiPreOrderSettingGetPreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingGetPreOrderSettingPost$Plain(params?: ApiPreOrderSettingGetPreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<GetPreOrderSettingResponseListResponseBase> {\r\n    return this.apiPreOrderSettingGetPreOrderSettingPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>): GetPreOrderSettingResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPreOrderSettingGetPreOrderSettingPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params?: ApiPreOrderSettingGetPreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>> {\r\n    return apiPreOrderSettingGetPreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPreOrderSettingGetPreOrderSettingPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingGetPreOrderSettingPost$Json(params?: ApiPreOrderSettingGetPreOrderSettingPost$Json$Params, context?: HttpContext): Observable<GetPreOrderSettingResponseListResponseBase> {\r\n    return this.apiPreOrderSettingGetPreOrderSettingPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetPreOrderSettingResponseListResponseBase>): GetPreOrderSettingResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiPreOrderSettingSavePreOrderSettingPost()` */\r\n  static readonly ApiPreOrderSettingSavePreOrderSettingPostPath = '/api/PreOrderSetting/SavePreOrderSetting';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params?: ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiPreOrderSettingSavePreOrderSettingPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingSavePreOrderSettingPost$Plain(params?: ApiPreOrderSettingSavePreOrderSettingPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiPreOrderSettingSavePreOrderSettingPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPreOrderSettingSavePreOrderSettingPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params?: ApiPreOrderSettingSavePreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiPreOrderSettingSavePreOrderSettingPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPreOrderSettingSavePreOrderSettingPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiPreOrderSettingSavePreOrderSettingPost$Json(params?: ApiPreOrderSettingSavePreOrderSettingPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiPreOrderSettingSavePreOrderSettingPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,6CAA6C,QAAQ,+EAA+E;AAE7I,SAASC,8CAA8C,QAAQ,gFAAgF;AAE/I,SAASC,8CAA8C,QAAQ,gFAAgF;AAE/I,SAASC,+CAA+C,QAAQ,iFAAiF;;;;AAMjJ,OAAM,MAAOC,sBAAuB,SAAQL,WAAW;EACrDM,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACC,MAA8D,EAAEC,OAAqB;IAC3I,OAAOV,8CAA8C,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAV,8CAA8CA,CAACS,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACF,uDAAuD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFf,GAAG,CAAEgB,CAAiE,IAAiDA,CAAC,CAACC,IAAI,CAAC,CAC/H;EACH;EAEA;;;;;;EAMAC,sDAAsDA,CAACN,MAA6D,EAAEC,OAAqB;IACzI,OAAOX,6CAA6C,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAX,6CAA6CA,CAACU,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACK,sDAAsD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFf,GAAG,CAAEgB,CAAiE,IAAiDA,CAAC,CAACC,IAAI,CAAC,CAC/H;EACH;EAEA;;IACgB,KAAAE,6CAA6C,GAAG,0CAA0C;EAAC;EAE3G;;;;;;EAMAC,wDAAwDA,CAACR,MAA+D,EAAEC,OAAqB;IAC7I,OAAOR,+CAA+C,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClG;EAEA;;;;;;EAMAR,+CAA+CA,CAACO,MAA+D,EAAEC,OAAqB;IACpI,OAAO,IAAI,CAACO,wDAAwD,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxFf,GAAG,CAAEgB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,uDAAuDA,CAACT,MAA8D,EAAEC,OAAqB;IAC3I,OAAOT,8CAA8C,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAT,8CAA8CA,CAACQ,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACQ,uDAAuD,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFf,GAAG,CAAEgB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCAjGWX,sBAAsB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAtBrB,sBAAsB;MAAAsB,OAAA,EAAtBtB,sBAAsB,CAAAuB,IAAA;MAAAC,UAAA,EADT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}