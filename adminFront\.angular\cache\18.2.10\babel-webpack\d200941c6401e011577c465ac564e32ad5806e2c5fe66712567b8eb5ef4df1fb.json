{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./sha256\"), require(\"./hmac\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./sha256\", \"./hmac\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var C_algo = C.algo;\n    var SHA256 = C_algo.SHA256;\n    var HMAC = C_algo.HMAC;\n\n    /**\n     * Password-Based Key Derivation Function 2 algorithm.\n     */\n    var PBKDF2 = C_algo.PBKDF2 = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n       * @property {Hasher} hasher The hasher to use. Default: SHA256\n       * @property {number} iterations The number of iterations to perform. Default: 250000\n       */\n      cfg: Base.extend({\n        keySize: 128 / 32,\n        hasher: SHA256,\n        iterations: 250000\n      }),\n      /**\n       * Initializes a newly created key derivation function.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n       *\n       * @example\n       *\n       *     var kdf = CryptoJS.algo.PBKDF2.create();\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });\n       *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });\n       */\n      init: function (cfg) {\n        this.cfg = this.cfg.extend(cfg);\n      },\n      /**\n       * Computes the Password-Based Key Derivation Function 2.\n       *\n       * @param {WordArray|string} password The password.\n       * @param {WordArray|string} salt A salt.\n       *\n       * @return {WordArray} The derived key.\n       *\n       * @example\n       *\n       *     var key = kdf.compute(password, salt);\n       */\n      compute: function (password, salt) {\n        // Shortcut\n        var cfg = this.cfg;\n\n        // Init HMAC\n        var hmac = HMAC.create(cfg.hasher, password);\n\n        // Initial values\n        var derivedKey = WordArray.create();\n        var blockIndex = WordArray.create([0x00000001]);\n\n        // Shortcuts\n        var derivedKeyWords = derivedKey.words;\n        var blockIndexWords = blockIndex.words;\n        var keySize = cfg.keySize;\n        var iterations = cfg.iterations;\n\n        // Generate key\n        while (derivedKeyWords.length < keySize) {\n          var block = hmac.update(salt).finalize(blockIndex);\n          hmac.reset();\n\n          // Shortcuts\n          var blockWords = block.words;\n          var blockWordsLength = blockWords.length;\n\n          // Iterations\n          var intermediate = block;\n          for (var i = 1; i < iterations; i++) {\n            intermediate = hmac.finalize(intermediate);\n            hmac.reset();\n\n            // Shortcut\n            var intermediateWords = intermediate.words;\n\n            // XOR intermediate with block\n            for (var j = 0; j < blockWordsLength; j++) {\n              blockWords[j] ^= intermediateWords[j];\n            }\n          }\n          derivedKey.concat(block);\n          blockIndexWords[0]++;\n        }\n        derivedKey.sigBytes = keySize * 4;\n        return derivedKey;\n      }\n    });\n\n    /**\n     * Computes the Password-Based Key Derivation Function 2.\n     *\n     * @param {WordArray|string} password The password.\n     * @param {WordArray|string} salt A salt.\n     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n     *\n     * @return {WordArray} The derived key.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var key = CryptoJS.PBKDF2(password, salt);\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });\n     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });\n     */\n    C.PBKDF2 = function (password, salt, cfg) {\n      return PBKDF2.create(cfg).compute(password, salt);\n    };\n  })();\n  return CryptoJS.PBKDF2;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "Base", "WordArray", "C_algo", "algo", "SHA256", "HMAC", "PBKDF2", "extend", "cfg", "keySize", "hasher", "iterations", "init", "compute", "password", "salt", "hmac", "create", "<PERSON><PERSON><PERSON>", "blockIndex", "derived<PERSON>eyWords", "words", "blockIndexWords", "length", "block", "update", "finalize", "reset", "blockWords", "blockWordsLength", "intermediate", "i", "intermediateWords", "j", "concat", "sigBytes"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/pbkdf2.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./sha256\"), require(\"./hmac\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./sha256\", \"./hmac\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var WordArray = C_lib.WordArray;\n\t    var C_algo = C.algo;\n\t    var SHA256 = C_algo.SHA256;\n\t    var HMAC = C_algo.HMAC;\n\n\t    /**\n\t     * Password-Based Key Derivation Function 2 algorithm.\n\t     */\n\t    var PBKDF2 = C_algo.PBKDF2 = Base.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)\n\t         * @property {Hasher} hasher The hasher to use. Default: SHA256\n\t         * @property {number} iterations The number of iterations to perform. Default: 250000\n\t         */\n\t        cfg: Base.extend({\n\t            keySize: 128/32,\n\t            hasher: SHA256,\n\t            iterations: 250000\n\t        }),\n\n\t        /**\n\t         * Initializes a newly created key derivation function.\n\t         *\n\t         * @param {Object} cfg (Optional) The configuration options to use for the derivation.\n\t         *\n\t         * @example\n\t         *\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create();\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });\n\t         *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });\n\t         */\n\t        init: function (cfg) {\n\t            this.cfg = this.cfg.extend(cfg);\n\t        },\n\n\t        /**\n\t         * Computes the Password-Based Key Derivation Function 2.\n\t         *\n\t         * @param {WordArray|string} password The password.\n\t         * @param {WordArray|string} salt A salt.\n\t         *\n\t         * @return {WordArray} The derived key.\n\t         *\n\t         * @example\n\t         *\n\t         *     var key = kdf.compute(password, salt);\n\t         */\n\t        compute: function (password, salt) {\n\t            // Shortcut\n\t            var cfg = this.cfg;\n\n\t            // Init HMAC\n\t            var hmac = HMAC.create(cfg.hasher, password);\n\n\t            // Initial values\n\t            var derivedKey = WordArray.create();\n\t            var blockIndex = WordArray.create([0x00000001]);\n\n\t            // Shortcuts\n\t            var derivedKeyWords = derivedKey.words;\n\t            var blockIndexWords = blockIndex.words;\n\t            var keySize = cfg.keySize;\n\t            var iterations = cfg.iterations;\n\n\t            // Generate key\n\t            while (derivedKeyWords.length < keySize) {\n\t                var block = hmac.update(salt).finalize(blockIndex);\n\t                hmac.reset();\n\n\t                // Shortcuts\n\t                var blockWords = block.words;\n\t                var blockWordsLength = blockWords.length;\n\n\t                // Iterations\n\t                var intermediate = block;\n\t                for (var i = 1; i < iterations; i++) {\n\t                    intermediate = hmac.finalize(intermediate);\n\t                    hmac.reset();\n\n\t                    // Shortcut\n\t                    var intermediateWords = intermediate.words;\n\n\t                    // XOR intermediate with block\n\t                    for (var j = 0; j < blockWordsLength; j++) {\n\t                        blockWords[j] ^= intermediateWords[j];\n\t                    }\n\t                }\n\n\t                derivedKey.concat(block);\n\t                blockIndexWords[0]++;\n\t            }\n\t            derivedKey.sigBytes = keySize * 4;\n\n\t            return derivedKey;\n\t        }\n\t    });\n\n\t    /**\n\t     * Computes the Password-Based Key Derivation Function 2.\n\t     *\n\t     * @param {WordArray|string} password The password.\n\t     * @param {WordArray|string} salt A salt.\n\t     * @param {Object} cfg (Optional) The configuration options to use for this computation.\n\t     *\n\t     * @return {WordArray} The derived key.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var key = CryptoJS.PBKDF2(password, salt);\n\t     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });\n\t     *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });\n\t     */\n\t    C.PBKDF2 = function (password, salt, cfg) {\n\t        return PBKDF2.create(cfg).compute(password, salt);\n\t    };\n\t}());\n\n\n\treturn CryptoJS.PBKDF2;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,UAAU,CAAC,EAAEA,OAAO,CAAC,QAAQ,CAAC,CAAC;EAC9F,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAEL,OAAO,CAAC;EAClD,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC/B,IAAIC,MAAM,GAAGL,CAAC,CAACM,IAAI;IACnB,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;IAC1B,IAAIC,IAAI,GAAGH,MAAM,CAACG,IAAI;;IAEtB;AACL;AACA;IACK,IAAIC,MAAM,GAAGJ,MAAM,CAACI,MAAM,GAAGN,IAAI,CAACO,MAAM,CAAC;MACrC;AACT;AACA;AACA;AACA;AACA;AACA;MACSC,GAAG,EAAER,IAAI,CAACO,MAAM,CAAC;QACbE,OAAO,EAAE,GAAG,GAAC,EAAE;QACfC,MAAM,EAAEN,MAAM;QACdO,UAAU,EAAE;MAChB,CAAC,CAAC;MAEF;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,IAAI,EAAE,SAAAA,CAAUJ,GAAG,EAAE;QACjB,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;MACnC,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSK,OAAO,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,IAAI,EAAE;QAC/B;QACA,IAAIP,GAAG,GAAG,IAAI,CAACA,GAAG;;QAElB;QACA,IAAIQ,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACT,GAAG,CAACE,MAAM,EAAEI,QAAQ,CAAC;;QAE5C;QACA,IAAII,UAAU,GAAGjB,SAAS,CAACgB,MAAM,CAAC,CAAC;QACnC,IAAIE,UAAU,GAAGlB,SAAS,CAACgB,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC;;QAE/C;QACA,IAAIG,eAAe,GAAGF,UAAU,CAACG,KAAK;QACtC,IAAIC,eAAe,GAAGH,UAAU,CAACE,KAAK;QACtC,IAAIZ,OAAO,GAAGD,GAAG,CAACC,OAAO;QACzB,IAAIE,UAAU,GAAGH,GAAG,CAACG,UAAU;;QAE/B;QACA,OAAOS,eAAe,CAACG,MAAM,GAAGd,OAAO,EAAE;UACrC,IAAIe,KAAK,GAAGR,IAAI,CAACS,MAAM,CAACV,IAAI,CAAC,CAACW,QAAQ,CAACP,UAAU,CAAC;UAClDH,IAAI,CAACW,KAAK,CAAC,CAAC;;UAEZ;UACA,IAAIC,UAAU,GAAGJ,KAAK,CAACH,KAAK;UAC5B,IAAIQ,gBAAgB,GAAGD,UAAU,CAACL,MAAM;;UAExC;UACA,IAAIO,YAAY,GAAGN,KAAK;UACxB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,UAAU,EAAEoB,CAAC,EAAE,EAAE;YACjCD,YAAY,GAAGd,IAAI,CAACU,QAAQ,CAACI,YAAY,CAAC;YAC1Cd,IAAI,CAACW,KAAK,CAAC,CAAC;;YAEZ;YACA,IAAIK,iBAAiB,GAAGF,YAAY,CAACT,KAAK;;YAE1C;YACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,gBAAgB,EAAEI,CAAC,EAAE,EAAE;cACvCL,UAAU,CAACK,CAAC,CAAC,IAAID,iBAAiB,CAACC,CAAC,CAAC;YACzC;UACJ;UAEAf,UAAU,CAACgB,MAAM,CAACV,KAAK,CAAC;UACxBF,eAAe,CAAC,CAAC,CAAC,EAAE;QACxB;QACAJ,UAAU,CAACiB,QAAQ,GAAG1B,OAAO,GAAG,CAAC;QAEjC,OAAOS,UAAU;MACrB;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKrB,CAAC,CAACS,MAAM,GAAG,UAAUQ,QAAQ,EAAEC,IAAI,EAAEP,GAAG,EAAE;MACtC,OAAOF,MAAM,CAACW,MAAM,CAACT,GAAG,CAAC,CAACK,OAAO,CAACC,QAAQ,EAAEC,IAAI,CAAC;IACrD,CAAC;EACL,CAAC,EAAC,CAAC;EAGH,OAAOnB,QAAQ,CAACU,MAAM;AAEvB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}