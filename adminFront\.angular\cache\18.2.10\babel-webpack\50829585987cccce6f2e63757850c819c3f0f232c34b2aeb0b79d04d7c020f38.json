{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./enc-base64\"), require(\"./md5\"), require(\"./evpkdf\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./enc-base64\", \"./md5\", \"./evpkdf\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var StreamCipher = C_lib.StreamCipher;\n    var C_algo = C.algo;\n\n    /**\n     * RC4 stream cipher algorithm.\n     */\n    var RC4 = C_algo.RC4 = StreamCipher.extend({\n      _doReset: function () {\n        // Shortcuts\n        var key = this._key;\n        var keyWords = key.words;\n        var keySigBytes = key.sigBytes;\n\n        // Init sbox\n        var S = this._S = [];\n        for (var i = 0; i < 256; i++) {\n          S[i] = i;\n        }\n\n        // Key setup\n        for (var i = 0, j = 0; i < 256; i++) {\n          var keyByteIndex = i % keySigBytes;\n          var keyByte = keyWords[keyByteIndex >>> 2] >>> 24 - keyByteIndex % 4 * 8 & 0xff;\n          j = (j + S[i] + keyByte) % 256;\n\n          // Swap\n          var t = S[i];\n          S[i] = S[j];\n          S[j] = t;\n        }\n\n        // Counters\n        this._i = this._j = 0;\n      },\n      _doProcessBlock: function (M, offset) {\n        M[offset] ^= generateKeystreamWord.call(this);\n      },\n      keySize: 256 / 32,\n      ivSize: 0\n    });\n    function generateKeystreamWord() {\n      // Shortcuts\n      var S = this._S;\n      var i = this._i;\n      var j = this._j;\n\n      // Generate keystream word\n      var keystreamWord = 0;\n      for (var n = 0; n < 4; n++) {\n        i = (i + 1) % 256;\n        j = (j + S[i]) % 256;\n\n        // Swap\n        var t = S[i];\n        S[i] = S[j];\n        S[j] = t;\n        keystreamWord |= S[(S[i] + S[j]) % 256] << 24 - n * 8;\n      }\n\n      // Update counters\n      this._i = i;\n      this._j = j;\n      return keystreamWord;\n    }\n\n    /**\n     * Shortcut functions to the cipher's object interface.\n     *\n     * @example\n     *\n     *     var ciphertext = CryptoJS.RC4.encrypt(message, key, cfg);\n     *     var plaintext  = CryptoJS.RC4.decrypt(ciphertext, key, cfg);\n     */\n    C.RC4 = StreamCipher._createHelper(RC4);\n\n    /**\n     * Modified RC4 stream cipher algorithm.\n     */\n    var RC4Drop = C_algo.RC4Drop = RC4.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {number} drop The number of keystream words to drop. Default 192\n       */\n      cfg: RC4.cfg.extend({\n        drop: 192\n      }),\n      _doReset: function () {\n        RC4._doReset.call(this);\n\n        // Drop\n        for (var i = this.cfg.drop; i > 0; i--) {\n          generateKeystreamWord.call(this);\n        }\n      }\n    });\n\n    /**\n     * Shortcut functions to the cipher's object interface.\n     *\n     * @example\n     *\n     *     var ciphertext = CryptoJS.RC4Drop.encrypt(message, key, cfg);\n     *     var plaintext  = CryptoJS.RC4Drop.decrypt(ciphertext, key, cfg);\n     */\n    C.RC4Drop = StreamCipher._createHelper(RC4Drop);\n  })();\n  return CryptoJS.RC4;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}