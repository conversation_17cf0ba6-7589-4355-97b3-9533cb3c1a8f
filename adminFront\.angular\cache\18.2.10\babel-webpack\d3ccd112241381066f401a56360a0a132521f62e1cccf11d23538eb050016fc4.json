{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ModifyFloorPlanComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r1.label, \" \");\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"div\", 33)(2, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_3_th_2_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const idx_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r3));\n    });\n    i0.ɵɵelementStart(3, \"span\", 35);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\\u7121\\u6B64\\u6236\\u578B \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const idx_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r3));\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, ModifyFloorPlanComponent_div_37_tr_3_th_2_Template, 5, 1, \"th\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList[0]);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_5_td_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"div\", 33)(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_5_td_8_Template_nb_checkbox_checkedChange_4_listener($event) {\n      const house_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r8.CIsSelected, $event) || (house_r8.CIsSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(5, \"span\", 35);\n    i0.ɵɵtext(6, \"\\u7121\\u6B64\\u6236\\u578B\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", house_r8.CHouseHold || \"null\", \" - \", house_r8.CFloor, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", house_r8.CIsSelected);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 33)(3, \"p\");\n    i0.ɵɵtext(4, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function ModifyFloorPlanComponent_div_37_tr_5_Template_nb_checkbox_checkedChange_5_listener($event) {\n      const row_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, row_r6));\n    });\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7, \"\\u5168\\u9078\\u7121\\u6B64\\u6236\\u578B\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, ModifyFloorPlanComponent_div_37_tr_5_td_8_Template, 7, 3, \"td\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r6 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(row_r6));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r6);\n  }\n}\nfunction ModifyFloorPlanComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"table\", 30)(2, \"thead\");\n    i0.ɵɵtemplate(3, ModifyFloorPlanComponent_div_37_tr_3_Template, 3, 1, \"tr\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ModifyFloorPlanComponent_div_37_tr_5_Template, 9, 2, \"tr\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList);\n  }\n}\nexport let ModifyFloorPlanComponent = /*#__PURE__*/(() => {\n  class ModifyFloorPlanComponent extends BaseComponent {\n    constructor(_allow, dialogService, _houseService, route, location, message, router, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this._houseService = _houseService;\n      this.route = route;\n      this.location = location;\n      this.message = message;\n      this.router = router;\n      this._eventService = _eventService;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this.isHouseList = false;\n    }\n    getListBuilding() {\n      this._houseService.apiHouseGetListBuildingPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.buildingSelectedOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n        }\n      });\n    }\n    clear() {\n      this.searchQuery = {\n        CFrom: 1,\n        CTo: 100,\n        CBuildingNameSelected: this.buildingSelectedOptions[0]\n      };\n    }\n    groupByFloor(customerData) {\n      const groupedData = [];\n      // Get all unique floor numbers (handling potential nulls)\n      const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n      // Create an empty array for each unique floor\n      for (const floor of uniqueFloors) {\n        groupedData.push([]);\n      }\n      // Place each customer in the correct floor array\n      for (const customer of customerData) {\n        const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array\n        if (floorIndex !== -1) {\n          groupedData[floorIndex].push({\n            ...customer,\n            CIsSelected: customer.CIsEnable === false ? true : false\n          });\n        } // Add customer to the corresponding array in groupedData\n      }\n      return groupedData;\n    }\n    sortByFloorDescending(arr) {\n      return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n    }\n    getHouseList() {\n      this.isHouseList = false;\n      if (this.buildCaseId) {\n        this._houseService.apiHouseGetHouseListPost$Json({\n          body: {\n            CBuildCaseID: this.buildCaseId,\n            CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\n            CFloor: {\n              CFrom: this.searchQuery.CFrom,\n              CTo: this.searchQuery.CTo\n            },\n            CIsPagi: false\n          }\n        }).subscribe(res => {\n          if (res.StatusCode == 0) {\n            if (res.Entries) {\n              const rest = this.sortByFloorDescending(res.Entries);\n              this.houseList = this.groupByFloor(rest);\n              this.isHouseList = true;\n            }\n          }\n        });\n      }\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    onSubmit() {\n      let bodyParam = this.houseList.flat().map(item => {\n        return {\n          CIsEnable: !item.CIsSelected,\n          CHouseID: item.CID\n        };\n      });\n      this._houseService.apiHouseEditListHousePost$Json({\n        body: {\n          Args: bodyParam\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          console.log(res);\n          this.getHouseList();\n        }\n      });\n    }\n    isCheckAllRowChecked(row) {\n      return row.every(item => item.CIsSelected);\n    }\n    isCheckAllColumnChecked(index) {\n      if (this.isHouseList) {\n        if (index < 0 || index >= this.houseList[0].length) {\n          throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n        }\n        for (const floorData of this.houseList) {\n          if (index >= floorData.length || !floorData[index].CIsSelected) {\n            return false; // Found a customer with CIsSelected not true (or missing)\n          }\n        }\n        return true; // All customers at the given index have CIsSelected as true\n      }\n      return false;\n    }\n    enableAllAtIndex(checked, index) {\n      if (index < 0) {\n        throw new Error(\"Invalid index. Index must be a non-negative number.\");\n      }\n      for (const floorData of this.houseList) {\n        if (index < floorData.length) {\n          // Check if index is valid for this floor\n          floorData[index].CIsSelected = checked;\n        }\n      }\n    }\n    enableAllRow(checked, row) {\n      for (const item of row) {\n        item.CIsSelected = checked;\n      }\n    }\n    ngOnInit() {\n      this.searchQuery = {\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CFrom: 1,\n        CTo: 100\n      };\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          this.getListBuilding();\n          this.getHouseList();\n        }\n      });\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n    }\n    onOpen(ref) {\n      this.dialogService.open(ref);\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onNavigateWithId(type) {\n      this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);\n    }\n    static {\n      this.ɵfac = function ModifyFloorPlanComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ModifyFloorPlanComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i7.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ModifyFloorPlanComponent,\n        selectors: [[\"ngx-modify-floor-plan\"]],\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 47,\n        vars: 5,\n        consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-5\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"inline\"], [1, \"d-flex\", \"justify-content-center\", \"w-full\"], [3, \"value\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-bordered\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"w-max\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"font-medium\"], [1, \"font-bold\"]],\n        template: function ModifyFloorPlanComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\");\n            i0.ɵɵelement(4, \"h1\", 1);\n            i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n            i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"nb-select\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_nb_select_ngModelChange_10_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(11, ModifyFloorPlanComponent_nb_option_11_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"label\", 10);\n            i0.ɵɵtext(15, \"\\u6A13 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"nb-form-field\", 11)(17, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(18, \"label\", 13);\n            i0.ɵɵtext(19, \"~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"nb-form-field\", 14)(21, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyFloorPlanComponent_Template_input_ngModelChange_21_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"button\", 18);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_24_listener() {\n              return ctx.clear();\n            });\n            i0.ɵɵtext(25, \" \\u6E05\\u9664 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_26_listener() {\n              return ctx.getHouseList();\n            });\n            i0.ɵɵtext(27, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(28, \"i\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"div\", 21)(30, \"div\", 17)(31, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_31_listener() {\n              return ctx.onNavigateWithId(\"modify-floor-plan\");\n            });\n            i0.ɵɵtext(32, \" 1.\\u8ABF\\u6574\\u6236\\u578B\\u7D44\\u6210 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_33_listener() {\n              return ctx.onNavigateWithId(\"modify-household\");\n            });\n            i0.ɵɵtext(34, \" 2.\\u4FEE\\u6539\\u6236\\u578B\\u540D\\u7A31 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_35_listener() {\n              return ctx.onNavigateWithId(\"modify-house-type\");\n            });\n            i0.ɵɵtext(36, \" 3.\\u8A2D\\u5B9A\\u5730\\u4E3B\\u6236 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(37, ModifyFloorPlanComponent_div_37_Template, 6, 2, \"div\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"nb-card-footer\", 25)(39, \"div\", 26)(40, \"div\", 27)(41, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_41_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵtext(42, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_43_listener() {\n              return ctx.getHouseList();\n            });\n            i0.ɵɵtext(44, \" \\u53D6\\u6D88 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function ModifyFloorPlanComponent_Template_button_click_45_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ctx.isHouseList);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i9.BreadcrumbComponent]\n      });\n    }\n  }\n  return ModifyFloorPlanComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}