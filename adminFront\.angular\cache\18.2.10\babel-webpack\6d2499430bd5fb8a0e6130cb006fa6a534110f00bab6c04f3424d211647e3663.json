{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, Component, ChangeDetectionStrategy, Injector, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i4 from '@nebular/theme';\nimport { NB_WINDOW, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { filter, share, map, switchMap, delay, catchError, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpResponse, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\nconst _c0 = [\"*\"];\nfunction NbLoginComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbLoginComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 23)(1, \"p\", 24)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 25);\n    i0.ɵɵtemplate(5, NbLoginComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbLoginComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbLoginComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 28)(1, \"p\", 24)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 25);\n    i0.ɵɵtemplate(5, NbLoginComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbLoginComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 29)(2, NbLoginComponent_ng_container_13_p_2_Template, 2, 0, \"p\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.pattern);\n  }\n}\nfunction NbLoginComponent_ng_container_22_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbLoginComponent_ng_container_22_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contain from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbLoginComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_ng_container_22_p_1_Template, 2, 0, \"p\", 29)(2, NbLoginComponent_ng_container_22_p_2_Template, 2, 2, \"p\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r6 = i0.ɵɵreference(21);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r6.errors == null ? null : password_r6.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r6.errors == null ? null : password_r6.errors.minlength) || (password_r6.errors == null ? null : password_r6.errors.maxlength));\n  }\n}\nfunction NbLoginComponent_nb_checkbox_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_nb_checkbox_24_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.rememberMe, $event) || (ctx_r2.user.rememberMe = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1, \"Remember me\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.rememberMe);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 39);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r8.icon);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r8.title);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 37);\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_1_nb_icon_1_Template, 1, 1, \"nb-icon\", 38)(2, NbLoginComponent_section_27_ng_container_3_a_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r9 = i0.ɵɵreference(3);\n    const socialLink_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r8.icon);\n    i0.ɵɵproperty(\"routerLink\", socialLink_r8.link);\n    i0.ɵɵattribute(\"target\", socialLink_r8.target)(\"class\", socialLink_r8.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.icon)(\"ngIfElse\", title_r9);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 39);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r8.icon);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r8 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r8.title);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_2_nb_icon_1_Template, 1, 1, \"nb-icon\", 38)(2, NbLoginComponent_section_27_ng_container_3_a_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r10 = i0.ɵɵreference(3);\n    const socialLink_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r8.icon);\n    i0.ɵɵattribute(\"href\", socialLink_r8.url, i0.ɵɵsanitizeUrl)(\"target\", socialLink_r8.target)(\"class\", socialLink_r8.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.icon)(\"ngIfElse\", title_r10);\n  }\n}\nfunction NbLoginComponent_section_27_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbLoginComponent_section_27_ng_container_3_a_1_Template, 4, 7, \"a\", 35)(2, NbLoginComponent_section_27_ng_container_3_a_2_Template, 4, 7, \"a\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const socialLink_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r8.url);\n  }\n}\nfunction NbLoginComponent_section_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 32);\n    i0.ɵɵtext(1, \" or enter with: \");\n    i0.ɵɵelementStart(2, \"div\", 33);\n    i0.ɵɵtemplate(3, NbLoginComponent_section_27_ng_container_3_Template, 3, 2, \"ng-container\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.socialLinks);\n  }\n}\nfunction NbRegisterComponent_nb_alert_2_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbRegisterComponent_nb_alert_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 25)(1, \"p\", 26)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 27);\n    i0.ɵɵtemplate(5, NbRegisterComponent_nb_alert_2_li_5_Template, 2, 1, \"li\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbRegisterComponent_nb_alert_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbRegisterComponent_nb_alert_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 30)(1, \"p\", 26)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 27);\n    i0.ɵɵtemplate(5, NbRegisterComponent_nb_alert_3_li_5_Template, 2, 1, \"li\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbRegisterComponent_ng_container_11_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Full name is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_11_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Full name should contains from \", ctx_r2.getConfigValue(\"forms.validation.fullName.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.fullName.maxLength\"), \" characters \");\n  }\n}\nfunction NbRegisterComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_11_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_11_p_2_Template, 2, 2, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const fullName_r5 = i0.ɵɵreference(10);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", fullName_r5.errors == null ? null : fullName_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (fullName_r5.errors == null ? null : fullName_r5.errors.minlength) || (fullName_r5.errors == null ? null : fullName_r5.errors.maxlength));\n  }\n}\nfunction NbRegisterComponent_ng_container_17_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_17_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_17_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_17_p_2_Template, 2, 0, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r6 = i0.ɵɵreference(16);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r6.errors == null ? null : email_r6.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r6.errors == null ? null : email_r6.errors.pattern);\n  }\n}\nfunction NbRegisterComponent_ng_container_23_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_23_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contain from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbRegisterComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_23_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_23_p_2_Template, 2, 2, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r7 = i0.ɵɵreference(22);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r7.errors == null ? null : password_r7.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r7.errors == null ? null : password_r7.errors.minlength) || (password_r7.errors == null ? null : password_r7.errors.maxlength));\n  }\n}\nfunction NbRegisterComponent_ng_container_29_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password confirmation is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_29_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 32);\n    i0.ɵɵtext(1, \" Password does not match the confirm password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRegisterComponent_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_ng_container_29_p_1_Template, 2, 0, \"p\", 31)(2, NbRegisterComponent_ng_container_29_p_2_Template, 2, 0, \"p\", 31);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r7 = i0.ɵɵreference(22);\n    const rePass_r8 = i0.ɵɵreference(28);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", rePass_r8.errors == null ? null : rePass_r8.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r7.value != rePass_r8.value && !(rePass_r8.errors == null ? null : rePass_r8.errors.required));\n  }\n}\nfunction NbRegisterComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_div_30_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.terms, $event) || (ctx_r2.user.terms = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2, \" Agree to \");\n    i0.ɵɵelementStart(3, \"a\", 35)(4, \"strong\");\n    i0.ɵɵtext(5, \"Terms & Conditions\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.terms);\n    i0.ɵɵproperty(\"required\", ctx_r2.getConfigValue(\"forms.register.terms\"));\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 43);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r10.icon);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r10.title);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 41);\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_1_nb_icon_1_Template, 1, 1, \"nb-icon\", 42)(2, NbRegisterComponent_section_33_ng_container_3_a_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r11 = i0.ɵɵreference(3);\n    const socialLink_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r10.icon);\n    i0.ɵɵproperty(\"routerLink\", socialLink_r10.link);\n    i0.ɵɵattribute(\"target\", socialLink_r10.target)(\"class\", socialLink_r10.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.icon)(\"ngIfElse\", title_r11);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_nb_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-icon\", 43);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"icon\", socialLink_r10.icon);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const socialLink_r10 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵtextInterpolate(socialLink_r10.title);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_2_nb_icon_1_Template, 1, 1, \"nb-icon\", 42)(2, NbRegisterComponent_section_33_ng_container_3_a_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const title_r12 = i0.ɵɵreference(3);\n    const socialLink_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"with-icon\", socialLink_r10.icon);\n    i0.ɵɵattribute(\"href\", socialLink_r10.url, i0.ɵɵsanitizeUrl)(\"target\", socialLink_r10.target)(\"class\", socialLink_r10.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.icon)(\"ngIfElse\", title_r12);\n  }\n}\nfunction NbRegisterComponent_section_33_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRegisterComponent_section_33_ng_container_3_a_1_Template, 4, 7, \"a\", 39)(2, NbRegisterComponent_section_33_ng_container_3_a_2_Template, 4, 7, \"a\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const socialLink_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.link);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", socialLink_r10.url);\n  }\n}\nfunction NbRegisterComponent_section_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 36);\n    i0.ɵɵtext(1, \" or enter with: \");\n    i0.ɵɵelementStart(2, \"div\", 37);\n    i0.ɵɵtemplate(3, NbRegisterComponent_section_33_ng_container_3_Template, 3, 2, \"ng-container\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.socialLinks);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 15)(1, \"p\", 16)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 17);\n    i0.ɵɵtemplate(5, NbRequestPasswordComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbRequestPasswordComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 20)(1, \"p\", 16)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 17);\n    i0.ɵɵtemplate(5, NbRequestPasswordComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1, \" Email is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 22);\n    i0.ɵɵtext(1, \" Email should be the real one! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbRequestPasswordComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbRequestPasswordComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 21)(2, NbRequestPasswordComponent_ng_container_13_p_2_Template, 2, 0, \"p\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const email_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", email_r5.errors == null ? null : email_r5.errors.pattern);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_4_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(error_r2);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 19)(1, \"p\", 20)(2, \"b\");\n    i0.ɵɵtext(3, \"Oh snap!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 21);\n    i0.ɵɵtemplate(5, NbResetPasswordComponent_nb_alert_4_li_5_Template, 2, 1, \"li\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_5_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(message_r4);\n  }\n}\nfunction NbResetPasswordComponent_nb_alert_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-alert\", 24)(1, \"p\", 20)(2, \"b\");\n    i0.ɵɵtext(3, \"Hooray!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ul\", 21);\n    i0.ɵɵtemplate(5, NbResetPasswordComponent_nb_alert_5_li_5_Template, 2, 1, \"li\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" Password should contains from \", ctx_r2.getConfigValue(\"forms.validation.password.minLength\"), \" to \", ctx_r2.getConfigValue(\"forms.validation.password.maxLength\"), \" characters \");\n  }\n}\nfunction NbResetPasswordComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbResetPasswordComponent_ng_container_13_p_1_Template, 2, 0, \"p\", 25)(2, NbResetPasswordComponent_ng_container_13_p_2_Template, 2, 2, \"p\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r5 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r5.errors == null ? null : password_r5.errors.required);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (password_r5.errors == null ? null : password_r5.errors.minlength) || (password_r5.errors == null ? null : password_r5.errors.maxlength));\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password confirmation is required! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 26);\n    i0.ɵɵtext(1, \" Password does not match the confirm password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NbResetPasswordComponent_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NbResetPasswordComponent_ng_container_19_p_1_Template, 2, 0, \"p\", 25)(2, NbResetPasswordComponent_ng_container_19_p_2_Template, 2, 0, \"p\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const password_r5 = i0.ɵɵreference(12);\n    const rePass_r6 = i0.ɵɵreference(18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", rePass_r6.invalid && (rePass_r6.errors == null ? null : rePass_r6.errors.required));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", password_r5.value != rePass_r6.value && !(rePass_r6.errors == null ? null : rePass_r6.errors.required));\n  }\n}\nconst _c1 = \"[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]:last-of-type{margin-bottom:3rem}\\n\\n\\n\\n\\n\\n\";\nconst socialLinks = [];\nconst defaultAuthOptions = {\n  strategies: [],\n  forms: {\n    login: {\n      redirectDelay: 500,\n      // delay before redirect after a successful login, while success message is shown to the user\n      strategy: 'email',\n      // provider id key. If you have multiple strategies, or what to use your own\n      rememberMe: true,\n      // whether to show or not the `rememberMe` checkbox\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks // social links at the bottom of a page\n    },\n    register: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      terms: true,\n      socialLinks: socialLinks\n    },\n    requestPassword: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks\n    },\n    resetPassword: {\n      redirectDelay: 500,\n      strategy: 'email',\n      showMessages: {\n        success: true,\n        error: true\n      },\n      socialLinks: socialLinks\n    },\n    logout: {\n      redirectDelay: 500,\n      strategy: 'email'\n    },\n    validation: {\n      password: {\n        required: true,\n        minLength: 4,\n        maxLength: 50\n      },\n      email: {\n        required: true\n      },\n      fullName: {\n        required: false,\n        minLength: 4,\n        maxLength: 50\n      }\n    }\n  }\n};\nconst NB_AUTH_OPTIONS = new InjectionToken('Nebular Auth Options');\nconst NB_AUTH_USER_OPTIONS = new InjectionToken('Nebular User Auth Options');\nconst NB_AUTH_STRATEGIES = new InjectionToken('Nebular Auth Strategies');\nconst NB_AUTH_TOKENS = new InjectionToken('Nebular Auth Tokens');\nconst NB_AUTH_INTERCEPTOR_HEADER = new InjectionToken('Nebular Simple Interceptor Header');\nconst NB_AUTH_TOKEN_INTERCEPTOR_FILTER = new InjectionToken('Nebular Interceptor Filter');\n\n/**\n * Extending object that entered in first argument.\n *\n * Returns extended object or false if have no target object or incorrect type.\n *\n * If you wish to clone source object (without modify it), just use empty new\n * object as first argument, like this:\n *   deepExtend({}, yourObj_1, [yourObj_N]);\n */\nconst deepExtend = function (...objects) {\n  if (arguments.length < 1 || typeof arguments[0] !== 'object') {\n    return false;\n  }\n  if (arguments.length < 2) {\n    return arguments[0];\n  }\n  const target = arguments[0];\n  // convert arguments to array and cut off target object\n  const args = Array.prototype.slice.call(arguments, 1);\n  let val, src;\n  args.forEach(function (obj) {\n    // skip argument if it is array or isn't object\n    if (typeof obj !== 'object' || Array.isArray(obj)) {\n      return;\n    }\n    Object.keys(obj).forEach(function (key) {\n      src = target[key]; // source value\n      val = obj[key]; // new value\n      // recursion prevention\n      if (val === target) {\n        return;\n        /**\n         * if new value isn't object then just overwrite by new value\n         * instead of extending.\n         */\n      } else if (typeof val !== 'object' || val === null) {\n        target[key] = val;\n        return;\n        // just clone arrays (and recursive clone objects inside)\n      } else if (Array.isArray(val)) {\n        target[key] = deepCloneArray(val);\n        return;\n        // custom cloning and overwrite for specific objects\n      } else if (isSpecificValue(val)) {\n        target[key] = cloneSpecificValue(val);\n        return;\n        // overwrite by new value if source isn't object or array\n      } else if (typeof src !== 'object' || src === null || Array.isArray(src)) {\n        target[key] = deepExtend({}, val);\n        return;\n        // source value and new value is objects both, extending...\n      } else {\n        target[key] = deepExtend(src, val);\n        return;\n      }\n    });\n  });\n  return target;\n};\nfunction isSpecificValue(val) {\n  return val instanceof Date || val instanceof RegExp ? true : false;\n}\nfunction cloneSpecificValue(val) {\n  if (val instanceof Date) {\n    return new Date(val.getTime());\n  } else if (val instanceof RegExp) {\n    return new RegExp(val);\n  } else {\n    throw new Error('cloneSpecificValue: Unexpected situation');\n  }\n}\n/**\n * Recursive cloning array.\n */\nfunction deepCloneArray(arr) {\n  const clone = [];\n  arr.forEach(function (item, index) {\n    if (typeof item === 'object' && item !== null) {\n      if (Array.isArray(item)) {\n        clone[index] = deepCloneArray(item);\n      } else if (isSpecificValue(item)) {\n        clone[index] = cloneSpecificValue(item);\n      } else {\n        clone[index] = deepExtend({}, item);\n      }\n    } else {\n      clone[index] = item;\n    }\n  });\n  return clone;\n}\n// getDeepFromObject({result: {data: 1}}, 'result.data', 2); // returns 1\nfunction getDeepFromObject(object = {}, name, defaultValue) {\n  const keys = name.split('.');\n  // clone the object\n  let level = deepExtend({}, object || {});\n  keys.forEach(k => {\n    if (level && typeof level[k] !== 'undefined') {\n      level = level[k];\n    } else {\n      level = undefined;\n    }\n  });\n  return typeof level === 'undefined' ? defaultValue : level;\n}\nfunction urlBase64Decode(str) {\n  let output = str.replace(/-/g, '+').replace(/_/g, '/');\n  switch (output.length % 4) {\n    case 0:\n      {\n        break;\n      }\n    case 2:\n      {\n        output += '==';\n        break;\n      }\n    case 3:\n      {\n        output += '=';\n        break;\n      }\n    default:\n      {\n        throw new Error('Illegal base64url string!');\n      }\n  }\n  return b64DecodeUnicode(output);\n}\nfunction b64decode(str) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n  let output = '';\n  str = String(str).replace(/=+$/, '');\n  if (str.length % 4 === 1) {\n    throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n  }\n  for (\n  // initialize result and counters\n  let bc = 0, bs, buffer, idx = 0;\n  // get next character\n  buffer = str.charAt(idx++);\n  // character found in table? initialize bit storage and add its ascii value;\n  ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,\n  // and if not first of each 4 characters,\n  // convert the first 8 bits to one ascii character\n  bc++ % 4) ? output += String.fromCharCode(255 & bs >> (-2 * bc & 6)) : 0) {\n    // try to find character in table (0-63, not found => -1)\n    buffer = chars.indexOf(buffer);\n  }\n  return output;\n}\n// https://developer.mozilla.org/en/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_Unicode_Problem\nfunction b64DecodeUnicode(str) {\n  return decodeURIComponent(Array.prototype.map.call(b64decode(str), c => {\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n  }).join(''));\n}\nclass NbAuthToken {\n  constructor() {\n    this.payload = null;\n  }\n  getName() {\n    return this.constructor.NAME;\n  }\n  getPayload() {\n    return this.payload;\n  }\n}\nclass NbAuthTokenNotFoundError extends Error {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthIllegalTokenError extends Error {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthEmptyTokenError extends NbAuthIllegalTokenError {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nclass NbAuthIllegalJWTTokenError extends NbAuthIllegalTokenError {\n  constructor(message) {\n    super(message);\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nfunction nbAuthCreateToken(tokenClass, token, ownerStrategyName, createdAt) {\n  return new tokenClass(token, ownerStrategyName, createdAt);\n}\nfunction decodeJwtPayload(payload) {\n  if (payload.length === 0) {\n    throw new NbAuthEmptyTokenError('Cannot extract from an empty payload.');\n  }\n  const parts = payload.split('.');\n  if (parts.length !== 3) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and must consist of three parts.`);\n  }\n  let decoded;\n  try {\n    decoded = urlBase64Decode(parts[1]);\n  } catch (e) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be parsed.`);\n  }\n  if (!decoded) {\n    throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be decoded.`);\n  }\n  return JSON.parse(decoded);\n}\n/**\n * Wrapper for simple (text) token\n */\nclass NbAuthSimpleToken extends NbAuthToken {\n  static {\n    this.NAME = 'nb:auth:simple:token';\n  }\n  constructor(token, ownerStrategyName, createdAt) {\n    super();\n    this.token = token;\n    this.ownerStrategyName = ownerStrategyName;\n    this.createdAt = createdAt;\n    try {\n      this.parsePayload();\n    } catch (err) {\n      if (!(err instanceof NbAuthTokenNotFoundError)) {\n        // token is present but has got a problem, including illegal\n        throw err;\n      }\n    }\n    this.createdAt = this.prepareCreatedAt(createdAt);\n  }\n  parsePayload() {\n    this.payload = null;\n  }\n  prepareCreatedAt(date) {\n    return date ? date : new Date();\n  }\n  /**\n   * Returns the token's creation date\n   * @returns {Date}\n   */\n  getCreatedAt() {\n    return this.createdAt;\n  }\n  /**\n   * Returns the token value\n   * @returns string\n   */\n  getValue() {\n    return this.token;\n  }\n  getOwnerStrategyName() {\n    return this.ownerStrategyName;\n  }\n  /**\n   * Is non empty and valid\n   * @returns {boolean}\n   */\n  isValid() {\n    return !!this.getValue();\n  }\n  /**\n   * Validate value and convert to string, if value is not valid return empty string\n   * @returns {string}\n   */\n  toString() {\n    return !!this.token ? this.token : '';\n  }\n}\n/**\n * Wrapper for JWT token with additional methods.\n */\nclass NbAuthJWTToken extends NbAuthSimpleToken {\n  static {\n    this.NAME = 'nb:auth:jwt:token';\n  }\n  /**\n   * for JWT token, the iat (issued at) field of the token payload contains the creation Date\n   */\n  prepareCreatedAt(date) {\n    const decoded = this.getPayload();\n    return decoded && decoded.iat ? new Date(Number(decoded.iat) * 1000) : super.prepareCreatedAt(date);\n  }\n  /**\n   * Returns payload object\n   * @returns any\n   */\n  parsePayload() {\n    if (!this.token) {\n      throw new NbAuthTokenNotFoundError('Token not found. ');\n    }\n    this.payload = decodeJwtPayload(this.token);\n  }\n  /**\n   * Returns expiration date\n   * @returns Date\n   */\n  getTokenExpDate() {\n    const decoded = this.getPayload();\n    if (decoded && !decoded.hasOwnProperty('exp')) {\n      return null;\n    }\n    const date = new Date(0);\n    date.setUTCSeconds(decoded.exp); // 'cause jwt token are set in seconds\n    return date;\n  }\n  /**\n   * Is data expired\n   * @returns {boolean}\n   */\n  isValid() {\n    return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n  }\n}\nconst prepareOAuth2Token = data => {\n  if (typeof data === 'string') {\n    try {\n      return JSON.parse(data);\n    } catch (e) {}\n  }\n  return data;\n};\n/**\n * Wrapper for OAuth2 token whose access_token is a JWT Token\n */\nclass NbAuthOAuth2Token extends NbAuthSimpleToken {\n  static {\n    this.NAME = 'nb:auth:oauth2:token';\n  }\n  constructor(data = {}, ownerStrategyName, createdAt) {\n    // we may get it as string when retrieving from a storage\n    super(prepareOAuth2Token(data), ownerStrategyName, createdAt);\n  }\n  /**\n   * Returns the token value\n   * @returns string\n   */\n  getValue() {\n    return this.token.access_token;\n  }\n  /**\n   * Returns the refresh token\n   * @returns string\n   */\n  getRefreshToken() {\n    return this.token.refresh_token;\n  }\n  /**\n   *  put refreshToken in the token payload\n    * @param refreshToken\n   */\n  setRefreshToken(refreshToken) {\n    this.token.refresh_token = refreshToken;\n  }\n  /**\n   * Parses token payload\n   * @returns any\n   */\n  parsePayload() {\n    if (!this.token) {\n      throw new NbAuthTokenNotFoundError('Token not found.');\n    } else {\n      if (!Object.keys(this.token).length) {\n        throw new NbAuthEmptyTokenError('Cannot extract payload from an empty token.');\n      }\n    }\n    this.payload = this.token;\n  }\n  /**\n   * Returns the token type\n   * @returns string\n   */\n  getType() {\n    return this.token.token_type;\n  }\n  /**\n   * Is data expired\n   * @returns {boolean}\n   */\n  isValid() {\n    return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n  }\n  /**\n   * Returns expiration date\n   * @returns Date\n   */\n  getTokenExpDate() {\n    if (!this.token.hasOwnProperty('expires_in')) {\n      return null;\n    }\n    return new Date(this.createdAt.getTime() + Number(this.token.expires_in) * 1000);\n  }\n  /**\n   * Convert to string\n   * @returns {string}\n   */\n  toString() {\n    return JSON.stringify(this.token);\n  }\n}\n/**\n * Wrapper for OAuth2 token embedding JWT tokens\n */\nclass NbAuthOAuth2JWTToken extends NbAuthOAuth2Token {\n  static {\n    this.NAME = 'nb:auth:oauth2:jwt:token';\n  }\n  parsePayload() {\n    super.parsePayload();\n    this.parseAccessTokenPayload();\n  }\n  parseAccessTokenPayload() {\n    const accessToken = this.getValue();\n    if (!accessToken) {\n      throw new NbAuthTokenNotFoundError('access_token key not found.');\n    }\n    this.accessTokenPayload = decodeJwtPayload(accessToken);\n  }\n  /**\n   * Returns access token payload\n   * @returns any\n   */\n  getAccessTokenPayload() {\n    return this.accessTokenPayload;\n  }\n  /**\n   * for Oauth2 JWT token, the iat (issued at) field of the access_token payload\n   */\n  prepareCreatedAt(date) {\n    const payload = this.accessTokenPayload;\n    return payload && payload.iat ? new Date(Number(payload.iat) * 1000) : super.prepareCreatedAt(date);\n  }\n  /**\n   * Is token valid\n   * @returns {boolean}\n   */\n  isValid() {\n    return this.accessTokenPayload && super.isValid();\n  }\n  /**\n   * Returns expiration date :\n   * - exp if set,\n   * - super.getExpDate() otherwise\n   * @returns Date\n   */\n  getTokenExpDate() {\n    if (this.accessTokenPayload && this.accessTokenPayload.hasOwnProperty('exp')) {\n      const date = new Date(0);\n      date.setUTCSeconds(this.accessTokenPayload.exp);\n      return date;\n    } else {\n      return super.getTokenExpDate();\n    }\n  }\n}\nconst NB_AUTH_FALLBACK_TOKEN = new InjectionToken('Nebular Auth Options');\n/**\n * Creates a token parcel which could be stored/restored\n */\nclass NbAuthTokenParceler {\n  constructor(fallbackClass, tokenClasses) {\n    this.fallbackClass = fallbackClass;\n    this.tokenClasses = tokenClasses;\n  }\n  wrap(token) {\n    return JSON.stringify({\n      name: token.getName(),\n      ownerStrategyName: token.getOwnerStrategyName(),\n      createdAt: token.getCreatedAt().getTime(),\n      value: token.toString()\n    });\n  }\n  unwrap(value) {\n    let tokenClass = this.fallbackClass;\n    let tokenValue = '';\n    let tokenOwnerStrategyName = '';\n    let tokenCreatedAt = null;\n    const tokenPack = this.parseTokenPack(value);\n    if (tokenPack) {\n      tokenClass = this.getClassByName(tokenPack.name) || this.fallbackClass;\n      tokenValue = tokenPack.value;\n      tokenOwnerStrategyName = tokenPack.ownerStrategyName;\n      tokenCreatedAt = new Date(Number(tokenPack.createdAt));\n    }\n    return nbAuthCreateToken(tokenClass, tokenValue, tokenOwnerStrategyName, tokenCreatedAt);\n  }\n  // TODO: this could be moved to a separate token registry\n  getClassByName(name) {\n    return this.tokenClasses.find(tokenClass => tokenClass.NAME === name);\n  }\n  parseTokenPack(value) {\n    try {\n      return JSON.parse(value);\n    } catch (e) {}\n    return null;\n  }\n  static {\n    this.ɵfac = function NbAuthTokenParceler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthTokenParceler)(i0.ɵɵinject(NB_AUTH_FALLBACK_TOKEN), i0.ɵɵinject(NB_AUTH_TOKENS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAuthTokenParceler,\n      factory: NbAuthTokenParceler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthTokenParceler, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_FALLBACK_TOKEN]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_TOKENS]\n    }]\n  }], null);\n})();\nclass NbTokenStorage {}\n/**\n * Service that uses browser localStorage as a storage.\n *\n * The token storage is provided into auth module the following way:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenLocalStorage },\n * ```\n *\n * If you need to change the storage behaviour or provide your own - just extend your class from basic `NbTokenStorage`\n * or `NbTokenLocalStorage` and provide in your `app.module`:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenCustomStorage },\n * ```\n *\n */\nclass NbTokenLocalStorage extends NbTokenStorage {\n  constructor(parceler) {\n    super();\n    this.parceler = parceler;\n    this.key = 'auth_app_token';\n  }\n  /**\n   * Returns token from localStorage\n   * @returns {NbAuthToken}\n   */\n  get() {\n    const raw = localStorage.getItem(this.key);\n    return this.parceler.unwrap(raw);\n  }\n  /**\n   * Sets token to localStorage\n   * @param {NbAuthToken} token\n   */\n  set(token) {\n    const raw = this.parceler.wrap(token);\n    localStorage.setItem(this.key, raw);\n  }\n  /**\n   * Clears token from localStorage\n   */\n  clear() {\n    localStorage.removeItem(this.key);\n  }\n  static {\n    this.ɵfac = function NbTokenLocalStorage_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbTokenLocalStorage)(i0.ɵɵinject(NbAuthTokenParceler));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbTokenLocalStorage,\n      factory: NbTokenLocalStorage.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbTokenLocalStorage, [{\n    type: Injectable\n  }], () => [{\n    type: NbAuthTokenParceler\n  }], null);\n})();\n\n/**\n * Service that allows you to manage authentication token - get, set, clear and also listen to token changes over time.\n */\nclass NbTokenService {\n  constructor(tokenStorage) {\n    this.tokenStorage = tokenStorage;\n    this.token$ = new BehaviorSubject(null);\n    this.publishStoredToken();\n  }\n  /**\n   * Publishes token when it changes.\n   * @returns {Observable<NbAuthToken>}\n   */\n  tokenChange() {\n    return this.token$.pipe(filter(value => !!value), share());\n  }\n  /**\n   * Sets a token into the storage. This method is used by the NbAuthService automatically.\n   *\n   * @param {NbAuthToken} token\n   * @returns {Observable<any>}\n   */\n  set(token) {\n    this.tokenStorage.set(token);\n    this.publishStoredToken();\n    return of(null);\n  }\n  /**\n   * Returns observable of current token\n   * @returns {Observable<NbAuthToken>}\n   */\n  get() {\n    const token = this.tokenStorage.get();\n    return of(token);\n  }\n  /**\n   * Removes the token and published token value\n   *\n   * @returns {Observable<any>}\n   */\n  clear() {\n    this.tokenStorage.clear();\n    this.publishStoredToken();\n    return of(null);\n  }\n  publishStoredToken() {\n    this.token$.next(this.tokenStorage.get());\n  }\n  static {\n    this.ɵfac = function NbTokenService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbTokenService)(i0.ɵɵinject(NbTokenStorage));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbTokenService,\n      factory: NbTokenService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbTokenService, [{\n    type: Injectable\n  }], () => [{\n    type: NbTokenStorage\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Common authentication service.\n * Should be used to as an interlayer between UI Components and Auth Strategy.\n */\nclass NbAuthService {\n  constructor(tokenService, strategies) {\n    this.tokenService = tokenService;\n    this.strategies = strategies;\n  }\n  /**\n   * Retrieves current authenticated token stored\n   * @returns {Observable<any>}\n   */\n  getToken() {\n    return this.tokenService.get();\n  }\n  /**\n   * Returns true if auth token is present in the token storage\n   * @returns {Observable<boolean>}\n   */\n  isAuthenticated() {\n    return this.getToken().pipe(map(token => token.isValid()));\n  }\n  /**\n   * Returns true if valid auth token is present in the token storage.\n   * If not, calls the strategy refreshToken, and returns isAuthenticated() if success, false otherwise\n   * @returns {Observable<boolean>}\n   */\n  isAuthenticatedOrRefresh() {\n    return this.getToken().pipe(switchMap(token => {\n      if (token.getValue() && !token.isValid()) {\n        return this.refreshToken(token.getOwnerStrategyName(), token).pipe(switchMap(res => {\n          if (res.isSuccess()) {\n            return this.isAuthenticated();\n          } else {\n            return of(false);\n          }\n        }));\n      } else {\n        return of(token.isValid());\n      }\n    }));\n  }\n  /**\n   * Returns tokens stream\n   * @returns {Observable<NbAuthSimpleToken>}\n   */\n  onTokenChange() {\n    return this.tokenService.tokenChange();\n  }\n  /**\n   * Returns authentication status stream\n   * @returns {Observable<boolean>}\n   */\n  onAuthenticationChange() {\n    return this.onTokenChange().pipe(map(token => token.isValid()));\n  }\n  /**\n   * Authenticates with the selected strategy\n   * Stores received token in the token storage\n   *\n   * Example:\n   * authenticate('email', {email: '<EMAIL>', password: 'test'})\n   *\n   * @param strategyName\n   * @param data\n   * @returns {Observable<NbAuthResult>}\n   */\n  authenticate(strategyName, data) {\n    return this.getStrategy(strategyName).authenticate(data).pipe(switchMap(result => {\n      return this.processResultToken(result);\n    }));\n  }\n  /**\n   * Registers with the selected strategy\n   * Stores received token in the token storage\n   *\n   * Example:\n   * register('email', {email: '<EMAIL>', name: 'Some Name', password: 'test'})\n   *\n   * @param strategyName\n   * @param data\n   * @returns {Observable<NbAuthResult>}\n   */\n  register(strategyName, data) {\n    return this.getStrategy(strategyName).register(data).pipe(switchMap(result => {\n      return this.processResultToken(result);\n    }));\n  }\n  /**\n   * Sign outs with the selected strategy\n   * Removes token from the token storage\n   *\n   * Example:\n   * logout('email')\n   *\n   * @param strategyName\n   * @returns {Observable<NbAuthResult>}\n   */\n  logout(strategyName) {\n    return this.getStrategy(strategyName).logout().pipe(switchMap(result => {\n      if (result.isSuccess()) {\n        this.tokenService.clear().pipe(map(() => result));\n      }\n      return of(result);\n    }));\n  }\n  /**\n   * Sends forgot password request to the selected strategy\n   *\n   * Example:\n   * requestPassword('email', {email: '<EMAIL>'})\n   *\n   * @param strategyName\n   * @param data\n   * @returns {Observable<NbAuthResult>}\n   */\n  requestPassword(strategyName, data) {\n    return this.getStrategy(strategyName).requestPassword(data);\n  }\n  /**\n   * Tries to reset password with the selected strategy\n   *\n   * Example:\n   * resetPassword('email', {newPassword: 'test'})\n   *\n   * @param strategyName\n   * @param data\n   * @returns {Observable<NbAuthResult>}\n   */\n  resetPassword(strategyName, data) {\n    return this.getStrategy(strategyName).resetPassword(data);\n  }\n  /**\n   * Sends a refresh token request\n   * Stores received token in the token storage\n   *\n   * Example:\n   * refreshToken('email', {token: token})\n   *\n   * @param {string} strategyName\n   * @param data\n   * @returns {Observable<NbAuthResult>}\n   */\n  refreshToken(strategyName, data) {\n    return this.getStrategy(strategyName).refreshToken(data).pipe(switchMap(result => {\n      return this.processResultToken(result);\n    }));\n  }\n  /**\n   * Get registered strategy by name\n   *\n   * Example:\n   * getStrategy('email')\n   *\n   * @param {string} provider\n   * @returns {NbAbstractAuthProvider}\n   */\n  getStrategy(strategyName) {\n    const found = this.strategies.find(strategy => strategy.getName() === strategyName);\n    if (!found) {\n      throw new TypeError(`There is no Auth Strategy registered under '${strategyName}' name`);\n    }\n    return found;\n  }\n  processResultToken(result) {\n    if (result.isSuccess() && result.getToken()) {\n      return this.tokenService.set(result.getToken()).pipe(map(token => {\n        return result;\n      }));\n    }\n    return of(result);\n  }\n  static {\n    this.ɵfac = function NbAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthService)(i0.ɵɵinject(NbTokenService), i0.ɵɵinject(NB_AUTH_STRATEGIES));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAuthService,\n      factory: NbAuthService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthService, [{\n    type: Injectable\n  }], () => [{\n    type: NbTokenService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_STRATEGIES]\n    }]\n  }], null);\n})();\nclass NbAuthStrategy {\n  // we should keep this any and validation should be done in `register` method instead\n  // otherwise it won't be possible to pass an empty object\n  setOptions(options) {\n    this.options = deepExtend({}, this.defaultOptions, options);\n  }\n  getOption(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  createToken(value, failWhenInvalidToken) {\n    const token = nbAuthCreateToken(this.getOption('token.class'), value, this.getName());\n    // At this point, nbAuthCreateToken failed with NbAuthIllegalTokenError which MUST be intercepted by strategies\n    // Or token is created. It MAY be created even if backend did not return any token, in this case it is !Valid\n    if (failWhenInvalidToken && !token.isValid()) {\n      // If we require a valid token (i.e. isValid), then we MUST throw NbAuthIllegalTokenError so that the strategies\n      // intercept it\n      throw new NbAuthIllegalTokenError('Token is empty or invalid.');\n    }\n    return token;\n  }\n  getName() {\n    return this.getOption('name');\n  }\n  createFailResponse(data) {\n    return new HttpResponse({\n      body: {},\n      status: 401\n    });\n  }\n  createSuccessResponse(data) {\n    return new HttpResponse({\n      body: {},\n      status: 200\n    });\n  }\n  getActionEndpoint(action) {\n    const actionEndpoint = this.getOption(`${action}.endpoint`);\n    const baseEndpoint = this.getOption('baseEndpoint');\n    return actionEndpoint ? baseEndpoint + actionEndpoint : '';\n  }\n  getHeaders() {\n    const customHeaders = this.getOption('headers') ?? {};\n    if (customHeaders instanceof HttpHeaders) {\n      return customHeaders;\n    }\n    let headers = new HttpHeaders();\n    Object.entries(customHeaders).forEach(([key, value]) => {\n      headers = headers.append(key, value);\n    });\n    return headers;\n  }\n}\nclass NbAuthResult {\n  // TODO: better pass object\n  constructor(success, response, redirect, errors, messages, token = null) {\n    this.success = success;\n    this.response = response;\n    this.redirect = redirect;\n    this.errors = [];\n    this.messages = [];\n    this.errors = this.errors.concat([errors]);\n    if (errors instanceof Array) {\n      this.errors = errors;\n    }\n    this.messages = this.messages.concat([messages]);\n    if (messages instanceof Array) {\n      this.messages = messages;\n    }\n    this.token = token;\n  }\n  getResponse() {\n    return this.response;\n  }\n  getToken() {\n    return this.token;\n  }\n  getRedirect() {\n    return this.redirect;\n  }\n  getErrors() {\n    return this.errors.filter(val => !!val);\n  }\n  getMessages() {\n    return this.messages.filter(val => !!val);\n  }\n  isSuccess() {\n    return this.success;\n  }\n  isFailure() {\n    return !this.success;\n  }\n}\nclass NbAuthStrategyOptions {}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.token = {\n      class: NbAuthSimpleToken\n    };\n    this.delay = 1000;\n    this.alwaysFail = false;\n  }\n}\nconst dummyStrategyOptions = new NbDummyAuthStrategyOptions();\n\n/**\n * Dummy auth strategy. Could be useful for auth setup when backend is not available yet.\n *\n *\n * Strategy settings.\n *\n * ```ts\n * export class NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n *   name = 'dummy';\n *   token = {\n *     class: NbAuthSimpleToken,\n *   };\n *   delay? = 1000;\n *   alwaysFail? = false;\n * }\n * ```\n */\nclass NbDummyAuthStrategy extends NbAuthStrategy {\n  constructor() {\n    super(...arguments);\n    this.defaultOptions = dummyStrategyOptions;\n  }\n  static setup(options) {\n    return [NbDummyAuthStrategy, options];\n  }\n  authenticate(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  register(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  requestPassword(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  resetPassword(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  logout(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  refreshToken(data) {\n    return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n  }\n  createDummyResult(data) {\n    if (this.getOption('alwaysFail')) {\n      return new NbAuthResult(false, this.createFailResponse(data), null, ['Something went wrong.']);\n    }\n    try {\n      const token = this.createToken('test token', true);\n      return new NbAuthResult(true, this.createSuccessResponse(data), '/', [], ['Successfully logged in.'], token);\n    } catch (err) {\n      return new NbAuthResult(false, this.createFailResponse(data), null, [err.message]);\n    }\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵNbDummyAuthStrategy_BaseFactory;\n      return function NbDummyAuthStrategy_Factory(__ngFactoryType__) {\n        return (ɵNbDummyAuthStrategy_BaseFactory || (ɵNbDummyAuthStrategy_BaseFactory = i0.ɵɵgetInheritedFactory(NbDummyAuthStrategy)))(__ngFactoryType__ || NbDummyAuthStrategy);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbDummyAuthStrategy,\n      factory: NbDummyAuthStrategy.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbDummyAuthStrategy, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nvar NbOAuth2ResponseType;\n(function (NbOAuth2ResponseType) {\n  NbOAuth2ResponseType[\"CODE\"] = \"code\";\n  NbOAuth2ResponseType[\"TOKEN\"] = \"token\";\n})(NbOAuth2ResponseType || (NbOAuth2ResponseType = {}));\n// TODO: client_credentials\nvar NbOAuth2GrantType;\n(function (NbOAuth2GrantType) {\n  NbOAuth2GrantType[\"AUTHORIZATION_CODE\"] = \"authorization_code\";\n  NbOAuth2GrantType[\"PASSWORD\"] = \"password\";\n  NbOAuth2GrantType[\"REFRESH_TOKEN\"] = \"refresh_token\";\n})(NbOAuth2GrantType || (NbOAuth2GrantType = {}));\nvar NbOAuth2ClientAuthMethod;\n(function (NbOAuth2ClientAuthMethod) {\n  NbOAuth2ClientAuthMethod[\"NONE\"] = \"none\";\n  NbOAuth2ClientAuthMethod[\"BASIC\"] = \"basic\";\n  NbOAuth2ClientAuthMethod[\"REQUEST_BODY\"] = \"request-body\";\n})(NbOAuth2ClientAuthMethod || (NbOAuth2ClientAuthMethod = {}));\nclass NbOAuth2AuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.baseEndpoint = '';\n    this.clientId = '';\n    this.clientSecret = '';\n    this.clientAuthMethod = NbOAuth2ClientAuthMethod.NONE;\n    this.redirect = {\n      success: '/',\n      failure: null\n    };\n    this.defaultErrors = ['Something went wrong, please try again.'];\n    this.defaultMessages = ['You have been successfully authenticated.'];\n    this.authorize = {\n      endpoint: 'authorize',\n      responseType: NbOAuth2ResponseType.CODE,\n      requireValidToken: true\n    };\n    this.token = {\n      endpoint: 'token',\n      grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n      requireValidToken: true,\n      class: NbAuthOAuth2Token\n    };\n    this.refresh = {\n      endpoint: 'token',\n      grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n      requireValidToken: true\n    };\n  }\n}\nconst auth2StrategyOptions = new NbOAuth2AuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * OAuth2 authentication strategy.\n *\n * Strategy settings:\n *\n * ```ts\n * export enum NbOAuth2ResponseType {\n *   CODE = 'code',\n *   TOKEN = 'token',\n * }\n *\n * export enum NbOAuth2GrantType {\n *   AUTHORIZATION_CODE = 'authorization_code',\n *   PASSWORD = 'password',\n *   REFRESH_TOKEN = 'refresh_token',\n * }\n *\n * export class NbOAuth2AuthStrategyOptions {\n *   name: string;\n *   baseEndpoint?: string = '';\n *   clientId: string = '';\n *   clientSecret: string = '';\n *   clientAuthMethod: string = NbOAuth2ClientAuthMethod.NONE;\n *   redirect?: { success?: string; failure?: string } = {\n *     success: '/',\n *     failure: null,\n *   };\n *   defaultErrors?: any[] = ['Something went wrong, please try again.'];\n *   defaultMessages?: any[] = ['You have been successfully authenticated.'];\n *   authorize?: {\n *     endpoint?: string;\n *     redirectUri?: string;\n *     responseType?: string;\n *     requireValidToken: true,\n *     scope?: string;\n *     state?: string;\n *     params?: { [key: string]: string };\n *   } = {\n *     endpoint: 'authorize',\n *     responseType: NbOAuth2ResponseType.CODE,\n *   };\n *   token?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     requireValidToken: true,\n *     redirectUri?: string;\n *     scope?: string;\n *     class: NbAuthTokenClass,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n *     class: NbAuthOAuth2Token,\n *   };\n *   refresh?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     scope?: string;\n *     requireValidToken: true,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n *   };\n * }\n * ```\n *\n */\nclass NbOAuth2AuthStrategy extends NbAuthStrategy {\n  static setup(options) {\n    return [NbOAuth2AuthStrategy, options];\n  }\n  get responseType() {\n    return this.getOption('authorize.responseType');\n  }\n  get clientAuthMethod() {\n    return this.getOption('clientAuthMethod');\n  }\n  constructor(http, route, window) {\n    super();\n    this.http = http;\n    this.route = route;\n    this.window = window;\n    this.redirectResultHandlers = {\n      [NbOAuth2ResponseType.CODE]: () => {\n        return of(this.route.snapshot.queryParams).pipe(switchMap(params => {\n          if (params.code) {\n            return this.requestToken(params.code);\n          }\n          return of(new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []));\n        }));\n      },\n      [NbOAuth2ResponseType.TOKEN]: () => {\n        const module = 'authorize';\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return of(this.route.snapshot.fragment).pipe(map(fragment => this.parseHashAsQueryParams(fragment)), map(params => {\n          if (!params.error) {\n            return new NbAuthResult(true, params, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(params, requireValidToken));\n          }\n          return new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []);\n        }), catchError(err => {\n          const errors = [];\n          if (err instanceof NbAuthIllegalTokenError) {\n            errors.push(err.message);\n          } else {\n            errors.push('Something went wrong.');\n          }\n          return of(new NbAuthResult(false, err, this.getOption('redirect.failure'), errors));\n        }));\n      }\n    };\n    this.redirectResults = {\n      [NbOAuth2ResponseType.CODE]: () => {\n        return of(this.route.snapshot.queryParams).pipe(map(params => !!(params && (params.code || params.error))));\n      },\n      [NbOAuth2ResponseType.TOKEN]: () => {\n        return of(this.route.snapshot.fragment).pipe(map(fragment => this.parseHashAsQueryParams(fragment)), map(params => !!(params && (params.access_token || params.error))));\n      }\n    };\n    this.defaultOptions = auth2StrategyOptions;\n  }\n  authenticate(data) {\n    if (this.getOption('token.grantType') === NbOAuth2GrantType.PASSWORD) {\n      return this.passwordToken(data.email, data.password);\n    } else {\n      return this.isRedirectResult().pipe(switchMap(result => {\n        if (!result) {\n          this.authorizeRedirect();\n          return of(new NbAuthResult(true));\n        }\n        return this.getAuthorizationResult();\n      }));\n    }\n  }\n  getAuthorizationResult() {\n    const redirectResultHandler = this.redirectResultHandlers[this.responseType];\n    if (redirectResultHandler) {\n      return redirectResultHandler.call(this);\n    }\n    throw new Error(`'${this.responseType}' responseType is not supported,\n                      only 'token' and 'code' are supported now`);\n  }\n  refreshToken(token) {\n    const module = 'refresh';\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.post(url, this.buildRefreshRequestData(token), {\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createRefreshedToken(res, token, requireValidToken));\n    }), catchError(res => this.handleResponseError(res)));\n  }\n  passwordToken(username, password) {\n    const module = 'token';\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.post(url, this.buildPasswordRequestData(username, password), {\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n    }), catchError(res => this.handleResponseError(res)));\n  }\n  authorizeRedirect() {\n    this.window.location.href = this.buildRedirectUrl();\n  }\n  isRedirectResult() {\n    return this.redirectResults[this.responseType].call(this);\n  }\n  requestToken(code) {\n    const module = 'token';\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.post(url, this.buildCodeRequestData(code), {\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n    }), catchError(res => this.handleResponseError(res)));\n  }\n  buildCodeRequestData(code) {\n    const params = {\n      grant_type: this.getOption('token.grantType'),\n      code: code,\n      redirect_uri: this.getOption('token.redirectUri'),\n      client_id: this.getOption('clientId')\n    };\n    return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n  }\n  buildRefreshRequestData(token) {\n    const params = {\n      grant_type: this.getOption('refresh.grantType'),\n      refresh_token: token.getRefreshToken(),\n      scope: this.getOption('refresh.scope'),\n      client_id: this.getOption('clientId')\n    };\n    return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n  }\n  buildPasswordRequestData(username, password) {\n    const params = {\n      grant_type: this.getOption('token.grantType'),\n      username: username,\n      password: password,\n      scope: this.getOption('token.scope')\n    };\n    return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n  }\n  buildAuthHeader() {\n    if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.BASIC) {\n      if (this.getOption('clientId') && this.getOption('clientSecret')) {\n        return new HttpHeaders({\n          Authorization: 'Basic ' + btoa(this.getOption('clientId') + ':' + this.getOption('clientSecret'))\n        });\n      } else {\n        throw Error('For basic client authentication method, please provide both clientId & clientSecret.');\n      }\n    }\n    return undefined;\n  }\n  getHeaders() {\n    let headers = super.getHeaders();\n    headers = headers.append('Content-Type', 'application/x-www-form-urlencoded');\n    const authHeaders = this.buildAuthHeader();\n    if (authHeaders === undefined) {\n      return headers;\n    }\n    for (const headerKey of authHeaders.keys()) {\n      for (const headerValue of authHeaders.getAll(headerKey)) {\n        headers = headers.append(headerKey, headerValue);\n      }\n    }\n    return headers;\n  }\n  cleanParams(params) {\n    Object.entries(params).forEach(([key, val]) => !val && delete params[key]);\n    return params;\n  }\n  addCredentialsToParams(params) {\n    if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.REQUEST_BODY) {\n      if (this.getOption('clientId') && this.getOption('clientSecret')) {\n        return {\n          ...params,\n          client_id: this.getOption('clientId'),\n          client_secret: this.getOption('clientSecret')\n        };\n      } else {\n        throw Error('For request body client authentication method, please provide both clientId & clientSecret.');\n      }\n    }\n    return params;\n  }\n  handleResponseError(res) {\n    let errors = [];\n    if (res instanceof HttpErrorResponse) {\n      if (res.error.error_description) {\n        errors.push(res.error.error_description);\n      } else {\n        errors = this.getOption('defaultErrors');\n      }\n    } else if (res instanceof NbAuthIllegalTokenError) {\n      errors.push(res.message);\n    } else {\n      errors.push('Something went wrong.');\n    }\n    return of(new NbAuthResult(false, res, this.getOption('redirect.failure'), errors, []));\n  }\n  buildRedirectUrl() {\n    const params = {\n      response_type: this.getOption('authorize.responseType'),\n      client_id: this.getOption('clientId'),\n      redirect_uri: this.getOption('authorize.redirectUri'),\n      scope: this.getOption('authorize.scope'),\n      state: this.getOption('authorize.state'),\n      ...this.getOption('authorize.params')\n    };\n    const endpoint = this.getActionEndpoint('authorize');\n    const query = this.urlEncodeParameters(this.cleanParams(params));\n    return `${endpoint}?${query}`;\n  }\n  parseHashAsQueryParams(hash) {\n    return hash ? hash.split('&').reduce((acc, part) => {\n      const item = part.split('=');\n      acc[item[0]] = decodeURIComponent(item[1]);\n      return acc;\n    }, {}) : {};\n  }\n  urlEncodeParameters(params) {\n    return Object.keys(params).map(k => {\n      return `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`;\n    }).join('&');\n  }\n  createRefreshedToken(res, existingToken, requireValidToken) {\n    const refreshedToken = this.createToken(res, requireValidToken);\n    if (!refreshedToken.getRefreshToken() && existingToken.getRefreshToken()) {\n      refreshedToken.setRefreshToken(existingToken.getRefreshToken());\n    }\n    return refreshedToken;\n  }\n  register(data) {\n    throw new Error('`register` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n  }\n  requestPassword(data) {\n    throw new Error('`requestPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n  }\n  resetPassword(data = {}) {\n    throw new Error('`resetPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n  }\n  logout() {\n    return of(new NbAuthResult(true));\n  }\n  static {\n    this.ɵfac = function NbOAuth2AuthStrategy_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbOAuth2AuthStrategy)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ActivatedRoute), i0.ɵɵinject(NB_WINDOW));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbOAuth2AuthStrategy,\n      factory: NbOAuth2AuthStrategy.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbOAuth2AuthStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: i1.HttpClient\n  }, {\n    type: i2.ActivatedRoute\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_WINDOW]\n    }]\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n  constructor() {\n    super(...arguments);\n    this.baseEndpoint = '/api/auth/';\n    this.login = {\n      alwaysFail: false,\n      endpoint: 'login',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Login/Email combination is not correct, please try again.'],\n      defaultMessages: ['You have been successfully logged in.']\n    };\n    this.register = {\n      alwaysFail: false,\n      endpoint: 'register',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['You have been successfully registered.']\n    };\n    this.requestPass = {\n      endpoint: 'request-pass',\n      method: 'post',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Reset password instructions have been sent to your email.']\n    };\n    this.resetPass = {\n      endpoint: 'reset-pass',\n      method: 'put',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      resetPasswordTokenKey: 'reset_password_token',\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Your password has been successfully changed.']\n    };\n    this.logout = {\n      alwaysFail: false,\n      endpoint: 'logout',\n      method: 'delete',\n      redirect: {\n        success: '/',\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['You have been successfully logged out.']\n    };\n    this.refreshToken = {\n      endpoint: 'refresh-token',\n      method: 'post',\n      requireValidToken: true,\n      redirect: {\n        success: null,\n        failure: null\n      },\n      defaultErrors: ['Something went wrong, please try again.'],\n      defaultMessages: ['Your token has been successfully refreshed.']\n    };\n    this.token = {\n      class: NbAuthSimpleToken,\n      key: 'data.token',\n      getter: (module, res, options) => getDeepFromObject(res.body, options.token.key)\n    };\n    this.errors = {\n      key: 'data.errors',\n      getter: (module, res, options) => getDeepFromObject(res.error, options.errors.key, options[module].defaultErrors)\n    };\n    this.messages = {\n      key: 'data.messages',\n      getter: (module, res, options) => getDeepFromObject(res.body, options.messages.key, options[module].defaultMessages)\n    };\n  }\n}\nconst passwordStrategyOptions = new NbPasswordAuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * The most common authentication provider for email/password strategy.\n *\n * Strategy settings. Note, there is no need to copy over the whole object to change the settings you need.\n * Also, this.getOption call won't work outside of the default options declaration\n * (which is inside of the `NbPasswordAuthStrategy` class), so you have to replace it with a custom helper function\n * if you need it.\n *\n * ```ts\n *export class NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n *  name: string;\n *  baseEndpoint? = '/api/auth/';\n *  login?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'login',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Login/Email combination is not correct, please try again.'],\n *    defaultMessages: ['You have been successfully logged in.'],\n *  };\n *  register?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'register',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully registered.'],\n *  };\n *  requestPass?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'request-pass',\n *    method: 'post',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Reset password instructions have been sent to your email.'],\n *  };\n *  resetPass?: boolean | NbPasswordStrategyReset = {\n *    endpoint: 'reset-pass',\n *    method: 'put',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    resetPasswordTokenKey: 'reset_password_token',\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your password has been successfully changed.'],\n *  };\n *  logout?: boolean | NbPasswordStrategyReset = {\n *    alwaysFail: false,\n *    endpoint: 'logout',\n *    method: 'delete',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully logged out.'],\n *  };\n *  refreshToken?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'refresh-token',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: null,\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your token has been successfully refreshed.'],\n *  };\n *  token?: NbPasswordStrategyToken = {\n *    class: NbAuthSimpleToken,\n *    key: 'data.token',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.token.key,\n *    ),\n *  };\n *  errors?: NbPasswordStrategyMessage = {\n *    key: 'data.errors',\n *    getter: (module: string, res: HttpErrorResponse, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.error,\n *      options.errors.key,\n *      options[module].defaultErrors,\n *    ),\n *  };\n *  messages?: NbPasswordStrategyMessage = {\n *    key: 'data.messages',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.messages.key,\n *      options[module].defaultMessages,\n *    ),\n *  };\n *  validation?: {\n *    password?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *    email?: {\n *      required?: boolean;\n *      regexp?: string | null;\n *    };\n *    fullName?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *  };\n *}\n * ```\n */\nclass NbPasswordAuthStrategy extends NbAuthStrategy {\n  static setup(options) {\n    return [NbPasswordAuthStrategy, options];\n  }\n  constructor(http, route) {\n    super();\n    this.http = http;\n    this.route = route;\n    this.defaultOptions = passwordStrategyOptions;\n  }\n  authenticate(data) {\n    const module = 'login';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.request(method, url, {\n      body: data,\n      observe: 'response',\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse(data);\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  register(data) {\n    const module = 'register';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.request(method, url, {\n      body: data,\n      observe: 'response',\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse(data);\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')('login', res, this.options), requireValidToken));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  requestPassword(data) {\n    const module = 'requestPass';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    return this.http.request(method, url, {\n      body: data,\n      observe: 'response',\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse();\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  resetPassword(data = {}) {\n    const module = 'resetPass';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    const tokenKey = this.getOption(`${module}.resetPasswordTokenKey`);\n    data[tokenKey] = this.route.snapshot.queryParams[tokenKey];\n    return this.http.request(method, url, {\n      body: data,\n      observe: 'response',\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse();\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  logout() {\n    const module = 'logout';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    return of({}).pipe(switchMap(res => {\n      if (!url) {\n        return of(res);\n      }\n      return this.http.request(method, url, {\n        observe: 'response',\n        headers: this.getHeaders()\n      });\n    }), map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse();\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  refreshToken(data) {\n    const module = 'refreshToken';\n    const method = this.getOption(`${module}.method`);\n    const url = this.getActionEndpoint(module);\n    const requireValidToken = this.getOption(`${module}.requireValidToken`);\n    return this.http.request(method, url, {\n      body: data,\n      observe: 'response',\n      headers: this.getHeaders()\n    }).pipe(map(res => {\n      if (this.getOption(`${module}.alwaysFail`)) {\n        throw this.createFailResponse(data);\n      }\n      return res;\n    }), map(res => {\n      return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n    }), catchError(res => {\n      return this.handleResponseError(res, module);\n    }));\n  }\n  handleResponseError(res, module) {\n    let errors = [];\n    if (res instanceof HttpErrorResponse) {\n      errors = this.getOption('errors.getter')(module, res, this.options);\n    } else if (res instanceof NbAuthIllegalTokenError) {\n      errors.push(res.message);\n    } else {\n      errors.push('Something went wrong.');\n    }\n    return of(new NbAuthResult(false, res, this.getOption(`${module}.redirect.failure`), errors));\n  }\n  static {\n    this.ɵfac = function NbPasswordAuthStrategy_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbPasswordAuthStrategy)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbPasswordAuthStrategy,\n      factory: NbPasswordAuthStrategy.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbPasswordAuthStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: i1.HttpClient\n  }, {\n    type: i2.ActivatedRoute\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbAuthBlockComponent {\n  static {\n    this.ɵfac = function NbAuthBlockComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthBlockComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbAuthBlockComponent,\n      selectors: [[\"nb-auth-block\"]],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NbAuthBlockComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{display:block;width:100%;max-width:35rem}[_nghost-%COMP%]     form{width:100%}[_nghost-%COMP%]     .label{display:block;margin-bottom:.5rem}[_nghost-%COMP%]     .forgot-password{text-decoration:none;margin-bottom:.5rem}[_nghost-%COMP%]     .caption{margin-top:.5rem}[_nghost-%COMP%]     .alert{text-align:center}[_nghost-%COMP%]     .title{margin-top:0;margin-bottom:.75rem;text-align:center}[_nghost-%COMP%]     .sub-title{margin-bottom:2rem;text-align:center}[_nghost-%COMP%]     .form-control-group{margin-bottom:2rem}[_nghost-%COMP%]     .form-control-group.accept-group{display:flex;justify-content:space-between;margin:2rem 0}[_nghost-%COMP%]     .label-with-link{display:flex;justify-content:space-between}[_nghost-%COMP%]     .links{text-align:center;margin-top:1.75rem}[_nghost-%COMP%]     .links .socials{margin-top:1.5rem}[_nghost-%COMP%]     .links .socials a{margin:0 1rem;text-decoration:none;vertical-align:middle}[_nghost-%COMP%]     .links .socials a.with-icon{font-size:2rem}[_nghost-%COMP%]     .another-action{margin-top:2rem;text-align:center}[_nghost-%COMP%]     .sign-in-or-up{margin-top:2rem;display:flex;justify-content:space-between}[_nghost-%COMP%]     nb-alert .alert-title, [_nghost-%COMP%]     nb-alert .alert-message{margin:0 0 .5rem}[_nghost-%COMP%]     nb-alert .alert-message-list{list-style-type:none;padding:0;margin:0}\\n\\n\\n\\n\\n\\n\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthBlockComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-auth-block',\n      template: `\n    <ng-content></ng-content>\n  `,\n      styles: [\":host{display:block;width:100%;max-width:35rem}:host ::ng-deep form{width:100%}:host ::ng-deep .label{display:block;margin-bottom:.5rem}:host ::ng-deep .forgot-password{text-decoration:none;margin-bottom:.5rem}:host ::ng-deep .caption{margin-top:.5rem}:host ::ng-deep .alert{text-align:center}:host ::ng-deep .title{margin-top:0;margin-bottom:.75rem;text-align:center}:host ::ng-deep .sub-title{margin-bottom:2rem;text-align:center}:host ::ng-deep .form-control-group{margin-bottom:2rem}:host ::ng-deep .form-control-group.accept-group{display:flex;justify-content:space-between;margin:2rem 0}:host ::ng-deep .label-with-link{display:flex;justify-content:space-between}:host ::ng-deep .links{text-align:center;margin-top:1.75rem}:host ::ng-deep .links .socials{margin-top:1.5rem}:host ::ng-deep .links .socials a{margin:0 1rem;text-decoration:none;vertical-align:middle}:host ::ng-deep .links .socials a.with-icon{font-size:2rem}:host ::ng-deep .another-action{margin-top:2rem;text-align:center}:host ::ng-deep .sign-in-or-up{margin-top:2rem;display:flex;justify-content:space-between}:host ::ng-deep nb-alert .alert-title,:host ::ng-deep nb-alert .alert-message{margin:0 0 .5rem}:host ::ng-deep nb-alert .alert-message-list{list-style-type:none;padding:0;margin:0}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"]\n    }]\n  }], null, null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbAuthComponent {\n  // showcase of how to use the onAuthenticationChange method\n  constructor(auth, location) {\n    this.auth = auth;\n    this.location = location;\n    this.destroy$ = new Subject();\n    this.authenticated = false;\n    this.token = '';\n    this.subscription = auth.onAuthenticationChange().pipe(takeUntil(this.destroy$)).subscribe(authenticated => {\n      this.authenticated = authenticated;\n    });\n  }\n  back() {\n    this.location.back();\n    return false;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NbAuthComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(i3.Location));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbAuthComponent,\n      selectors: [[\"nb-auth\"]],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"navigation\"], [\"href\", \"#\", \"aria-label\", \"Back\", 1, \"link\", \"back-link\", 3, \"click\"], [\"icon\", \"arrow-back\"]],\n      template: function NbAuthComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-layout\")(1, \"nb-layout-column\")(2, \"nb-card\")(3, \"nb-card-header\")(4, \"nav\", 0)(5, \"a\", 1);\n          i0.ɵɵlistener(\"click\", function NbAuthComponent_Template_a_click_5_listener() {\n            return ctx.back();\n          });\n          i0.ɵɵelement(6, \"nb-icon\", 2);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"nb-card-body\")(8, \"nb-auth-block\");\n          i0.ɵɵelement(9, \"router-outlet\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i4.NbLayoutComponent, i4.NbLayoutColumnComponent, i4.NbCardComponent, i4.NbCardBodyComponent, i4.NbCardHeaderComponent, i2.RouterOutlet, i4.NbIconComponent, NbAuthBlockComponent],\n      styles: [\".visually-hidden[_ngcontent-%COMP%]{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.cdk-overlay-container[_ngcontent-%COMP%], .cdk-global-overlay-wrapper[_ngcontent-%COMP%]{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container[_ngcontent-%COMP%]{position:fixed;z-index:1000}.cdk-overlay-container[_ngcontent-%COMP%]:empty{display:none}.cdk-global-overlay-wrapper[_ngcontent-%COMP%]{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane[_ngcontent-%COMP%]{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop[_ngcontent-%COMP%]{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:1}.cdk-high-contrast-active[_ngcontent-%COMP%]   .cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:.6}.cdk-overlay-dark-backdrop[_ngcontent-%COMP%]{background:#00000052}.cdk-overlay-transparent-backdrop[_ngcontent-%COMP%]{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation[_ngcontent-%COMP%]{transition:none}.cdk-overlay-connected-position-bounding-box[_ngcontent-%COMP%]{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock[_ngcontent-%COMP%]{position:fixed;width:100%;overflow-y:scroll}.nb-global-scrollblock[_ngcontent-%COMP%]{position:static;width:auto;overflow:hidden}html[_ngcontent-%COMP%]{box-sizing:border-box}*[_ngcontent-%COMP%], *[_ngcontent-%COMP%]:before, *[_ngcontent-%COMP%]:after{box-sizing:inherit}html[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{margin:0;padding:0}html[_ngcontent-%COMP%]{line-height:1.15;-webkit-text-size-adjust:100%}body[_ngcontent-%COMP%]{margin:0}h1[_ngcontent-%COMP%]{font-size:2em;margin:.67em 0}hr[_ngcontent-%COMP%]{box-sizing:content-box;height:0;overflow:visible}pre[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}a[_ngcontent-%COMP%]{background-color:transparent}abbr[title][_ngcontent-%COMP%]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b[_ngcontent-%COMP%], strong[_ngcontent-%COMP%]{font-weight:bolder}code[_ngcontent-%COMP%], kbd[_ngcontent-%COMP%], samp[_ngcontent-%COMP%]{font-family:monospace,monospace;font-size:1em}small[_ngcontent-%COMP%]{font-size:80%}sub[_ngcontent-%COMP%], sup[_ngcontent-%COMP%]{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub[_ngcontent-%COMP%]{bottom:-.25em}sup[_ngcontent-%COMP%]{top:-.5em}img[_ngcontent-%COMP%]{border-style:none}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%], optgroup[_ngcontent-%COMP%], select[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%]{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button[_ngcontent-%COMP%], input[_ngcontent-%COMP%]{overflow:visible}button[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{text-transform:none}button[_ngcontent-%COMP%], [type=button][_ngcontent-%COMP%], [type=reset][_ngcontent-%COMP%], [type=submit][_ngcontent-%COMP%]{-webkit-appearance:button}button[_ngcontent-%COMP%]::-moz-focus-inner, [type=button][_ngcontent-%COMP%]::-moz-focus-inner, [type=reset][_ngcontent-%COMP%]::-moz-focus-inner, [type=submit][_ngcontent-%COMP%]::-moz-focus-inner{border-style:none;padding:0}button[_ngcontent-%COMP%]:-moz-focusring, [type=button][_ngcontent-%COMP%]:-moz-focusring, [type=reset][_ngcontent-%COMP%]:-moz-focusring, [type=submit][_ngcontent-%COMP%]:-moz-focusring{outline:1px dotted ButtonText}fieldset[_ngcontent-%COMP%]{padding:.35em .75em .625em}legend[_ngcontent-%COMP%]{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress[_ngcontent-%COMP%]{vertical-align:baseline}textarea[_ngcontent-%COMP%]{overflow:auto}[type=checkbox][_ngcontent-%COMP%], [type=radio][_ngcontent-%COMP%]{box-sizing:border-box;padding:0}[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, [type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{height:auto}[type=search][_ngcontent-%COMP%]{-webkit-appearance:textfield;outline-offset:-2px}[type=search][_ngcontent-%COMP%]::-webkit-search-decoration{-webkit-appearance:none}[_ngcontent-%COMP%]::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details[_ngcontent-%COMP%]{display:block}summary[_ngcontent-%COMP%]{display:list-item}template[_ngcontent-%COMP%]{display:none}[hidden][_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]   nb-card[_ngcontent-%COMP%]{margin:0;height:calc(100vh - 5rem)}[_nghost-%COMP%]   .navigation[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]{display:inline-block;text-decoration:none}[_nghost-%COMP%]   .navigation[_ngcontent-%COMP%]   .link[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:2rem;vertical-align:middle}[_nghost-%COMP%]   .links[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%]{font-size:2.5rem}[_nghost-%COMP%]   nb-card-body[_ngcontent-%COMP%]{display:flex;width:100%}[_nghost-%COMP%]   nb-auth-block[_ngcontent-%COMP%]{margin:auto}@media (max-width: 767.98px){[_nghost-%COMP%]   nb-card[_ngcontent-%COMP%]{border-radius:0;height:100vh}}[_nghost-%COMP%]     nb-layout .layout .layout-container .content .columns nb-layout-column{padding:2.5rem}@media (max-width: 767.98px){[_nghost-%COMP%]     nb-layout .layout .layout-container .content .columns nb-layout-column{padding:0}}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-auth',\n      template: `\n    <nb-layout>\n      <nb-layout-column>\n        <nb-card>\n          <nb-card-header>\n            <nav class=\"navigation\">\n              <a href=\"#\" (click)=\"back()\" class=\"link back-link\" aria-label=\"Back\">\n                <nb-icon icon=\"arrow-back\"></nb-icon>\n              </a>\n            </nav>\n          </nb-card-header>\n          <nb-card-body>\n            <nb-auth-block>\n              <router-outlet></router-outlet>\n            </nb-auth-block>\n          </nb-card-body>\n        </nb-card>\n      </nb-layout-column>\n    </nb-layout>\n  `,\n      styles: [\".visually-hidden{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:#00000052}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}.nb-global-scrollblock{position:static;width:auto;overflow:hidden}html{box-sizing:border-box}*,*:before,*:after{box-sizing:inherit}html,body{margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:.35em .75em .625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}:host nb-card{margin:0;height:calc(100vh - 5rem)}:host .navigation .link{display:inline-block;text-decoration:none}:host .navigation .link nb-icon{font-size:2rem;vertical-align:middle}:host .links nb-icon{font-size:2.5rem}:host nb-card-body{display:flex;width:100%}:host nb-auth-block{margin:auto}@media (max-width: 767.98px){:host nb-card{border-radius:0;height:100vh}}:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:2.5rem}@media (max-width: 767.98px){:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:0}}\\n/*\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/*!\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"]\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: i3.Location\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbLoginComponent {\n  constructor(service, options = {}, cd, router) {\n    this.service = service;\n    this.options = options;\n    this.cd = cd;\n    this.router = router;\n    this.redirectDelay = 0;\n    this.showMessages = {};\n    this.strategy = '';\n    this.errors = [];\n    this.messages = [];\n    this.user = {};\n    this.submitted = false;\n    this.socialLinks = [];\n    this.rememberMe = false;\n    this.redirectDelay = this.getConfigValue('forms.login.redirectDelay');\n    this.showMessages = this.getConfigValue('forms.login.showMessages');\n    this.strategy = this.getConfigValue('forms.login.strategy');\n    this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n    this.rememberMe = this.getConfigValue('forms.login.rememberMe');\n  }\n  login() {\n    this.errors = [];\n    this.messages = [];\n    this.submitted = true;\n    this.service.authenticate(this.strategy, this.user).subscribe(result => {\n      this.submitted = false;\n      if (result.isSuccess()) {\n        this.messages = result.getMessages();\n      } else {\n        this.errors = result.getErrors();\n      }\n      const redirect = result.getRedirect();\n      if (redirect) {\n        setTimeout(() => {\n          return this.router.navigateByUrl(redirect);\n        }, this.redirectDelay);\n      }\n      this.cd.detectChanges();\n    });\n  }\n  getConfigValue(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  static {\n    this.ɵfac = function NbLoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbLoginComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbLoginComponent,\n      selectors: [[\"nb-login\"]],\n      decls: 32,\n      vars: 19,\n      consts: [[\"form\", \"ngForm\"], [\"email\", \"ngModel\"], [\"password\", \"ngModel\"], [\"title\", \"\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"email\", \"id\", \"input-email\", \"pattern\", \".+@.+\\\\..+\", \"placeholder\", \"Email address\", \"fieldSize\", \"large\", \"autofocus\", \"\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [4, \"ngIf\"], [1, \"label-with-link\"], [\"for\", \"input-password\", 1, \"label\"], [\"routerLink\", \"../request-password\", 1, \"forgot-password\", \"caption-2\"], [\"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"password\", \"type\", \"password\", \"id\", \"input-password\", \"placeholder\", \"Password\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [1, \"form-control-group\", \"accept-group\"], [\"name\", \"rememberMe\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"class\", \"links\", \"aria-label\", \"Social sign in\", 4, \"ngIf\"], [\"aria-label\", \"Register\", 1, \"another-action\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"], [\"name\", \"rememberMe\", 3, \"ngModelChange\", \"ngModel\"], [\"aria-label\", \"Social sign in\", 1, \"links\"], [1, \"socials\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\", \"with-icon\", 4, \"ngIf\"], [3, \"with-icon\", 4, \"ngIf\"], [3, \"routerLink\"], [3, \"icon\", 4, \"ngIf\", \"ngIfElse\"], [3, \"icon\"]],\n      template: function NbLoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h1\", 4);\n          i0.ɵɵtext(1, \"Login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"p\", 5);\n          i0.ɵɵtext(3, \"Hello! Log in with your email.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NbLoginComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 6)(5, NbLoginComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 7);\n          i0.ɵɵelementStart(6, \"form\", 8, 0);\n          i0.ɵɵlistener(\"ngSubmit\", function NbLoginComponent_Template_form_ngSubmit_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.login());\n          });\n          i0.ɵɵelementStart(8, \"div\", 9)(9, \"label\", 10);\n          i0.ɵɵtext(10, \"Email address:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 11, 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, NbLoginComponent_ng_container_13_Template, 3, 2, \"ng-container\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"span\", 13)(16, \"label\", 14);\n          i0.ɵɵtext(17, \"Password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"a\", 15);\n          i0.ɵɵtext(19, \"Forgot Password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"input\", 16, 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbLoginComponent_Template_input_ngModelChange_20_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, NbLoginComponent_ng_container_22_Template, 3, 2, \"ng-container\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 17);\n          i0.ɵɵtemplate(24, NbLoginComponent_nb_checkbox_24_Template, 2, 1, \"nb-checkbox\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 19);\n          i0.ɵɵtext(26, \" Log In \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, NbLoginComponent_section_27_Template, 4, 1, \"section\", 20);\n          i0.ɵɵelementStart(28, \"section\", 21);\n          i0.ɵɵtext(29, \" Don't have an account? \");\n          i0.ɵɵelementStart(30, \"a\", 22);\n          i0.ɵɵtext(31, \"Register\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const form_r11 = i0.ɵɵreference(7);\n          const email_r5 = i0.ɵɵreference(12);\n          const password_r6 = i0.ɵɵreference(21);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵproperty(\"status\", email_r5.dirty ? email_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n          i0.ɵɵattribute(\"aria-invalid\", email_r5.invalid && email_r5.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", email_r5.invalid && email_r5.touched);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵproperty(\"status\", password_r6.dirty ? password_r6.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n          i0.ɵɵattribute(\"aria-invalid\", password_r6.invalid && password_r6.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", password_r6.invalid && password_r6.touched);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.rememberMe);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n          i0.ɵɵproperty(\"disabled\", ctx.submitted || !form_r11.valid);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.socialLinks && ctx.socialLinks.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NbCheckboxComponent, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.PatternValidator, i5.NgModel, i5.NgForm, i4.NbIconComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbLoginComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-login',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Login</h1>\\n<p class=\\\"sub-title\\\">Hello! Log in with your email.</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"login()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           name=\\\"email\\\"\\n           id=\\\"input-email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fieldSize=\\\"large\\\"\\n           autofocus\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <span class=\\\"label-with-link\\\">\\n      <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n      <a class=\\\"forgot-password caption-2\\\" routerLink=\\\"../request-password\\\">Forgot Password?</a>\\n    </span>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           name=\\\"password\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           placeholder=\\\"Password\\\"\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched \\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\">\\n    <nb-checkbox name=\\\"rememberMe\\\" [(ngModel)]=\\\"user.rememberMe\\\" *ngIf=\\\"rememberMe\\\">Remember me</nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Log In\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Register\\\">\\n  Don't have an account? <a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a>\\n</section>\\n\"\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Router\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbRegisterComponent {\n  constructor(service, options = {}, cd, router) {\n    this.service = service;\n    this.options = options;\n    this.cd = cd;\n    this.router = router;\n    this.redirectDelay = 0;\n    this.showMessages = {};\n    this.strategy = '';\n    this.submitted = false;\n    this.errors = [];\n    this.messages = [];\n    this.user = {};\n    this.socialLinks = [];\n    this.redirectDelay = this.getConfigValue('forms.register.redirectDelay');\n    this.showMessages = this.getConfigValue('forms.register.showMessages');\n    this.strategy = this.getConfigValue('forms.register.strategy');\n    this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n  }\n  register() {\n    this.errors = this.messages = [];\n    this.submitted = true;\n    this.service.register(this.strategy, this.user).subscribe(result => {\n      this.submitted = false;\n      if (result.isSuccess()) {\n        this.messages = result.getMessages();\n      } else {\n        this.errors = result.getErrors();\n      }\n      const redirect = result.getRedirect();\n      if (redirect) {\n        setTimeout(() => {\n          return this.router.navigateByUrl(redirect);\n        }, this.redirectDelay);\n      }\n      this.cd.detectChanges();\n    });\n  }\n  getConfigValue(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  static {\n    this.ɵfac = function NbRegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbRegisterComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbRegisterComponent,\n      selectors: [[\"nb-register\"]],\n      decls: 38,\n      vars: 31,\n      consts: [[\"form\", \"ngForm\"], [\"fullName\", \"ngModel\"], [\"email\", \"ngModel\"], [\"password\", \"ngModel\"], [\"rePass\", \"ngModel\"], [\"title\", \"\"], [\"id\", \"title\", 1, \"title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-name\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-name\", \"name\", \"fullName\", \"placeholder\", \"Full name\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [4, \"ngIf\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-email\", \"name\", \"email\", \"pattern\", \".+@.+..+\", \"placeholder\", \"Email address\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"for\", \"input-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-password\", \"name\", \"password\", \"placeholder\", \"Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [\"for\", \"input-re-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-re-password\", \"name\", \"rePass\", \"placeholder\", \"Confirm Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"class\", \"form-control-group accept-group\", 4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"class\", \"links\", \"aria-label\", \"Social sign in\", 4, \"ngIf\"], [\"aria-label\", \"Sign in\", 1, \"another-action\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"], [1, \"form-control-group\", \"accept-group\"], [\"name\", \"terms\", 3, \"ngModelChange\", \"ngModel\", \"required\"], [\"href\", \"#\", \"target\", \"_blank\"], [\"aria-label\", \"Social sign in\", 1, \"links\"], [1, \"socials\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\", \"with-icon\", 4, \"ngIf\"], [3, \"with-icon\", 4, \"ngIf\"], [3, \"routerLink\"], [3, \"icon\", 4, \"ngIf\", \"ngIfElse\"], [3, \"icon\"]],\n      template: function NbRegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h1\", 6);\n          i0.ɵɵtext(1, \"Register\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, NbRegisterComponent_nb_alert_2_Template, 6, 1, \"nb-alert\", 7)(3, NbRegisterComponent_nb_alert_3_Template, 6, 1, \"nb-alert\", 8);\n          i0.ɵɵelementStart(4, \"form\", 9, 0);\n          i0.ɵɵlistener(\"ngSubmit\", function NbRegisterComponent_Template_form_ngSubmit_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.register());\n          });\n          i0.ɵɵelementStart(6, \"div\", 10)(7, \"label\", 11);\n          i0.ɵɵtext(8, \"Full name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 12, 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.fullName, $event) || (ctx.user.fullName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, NbRegisterComponent_ng_container_11_Template, 3, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 14);\n          i0.ɵɵtext(14, \"Email address:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 15, 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_15_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(17, NbRegisterComponent_ng_container_17_Template, 3, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"label\", 16);\n          i0.ɵɵtext(20, \"Password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 17, 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(23, NbRegisterComponent_ng_container_23_Template, 3, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"label\", 18);\n          i0.ɵɵtext(26, \"Repeat password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 19, 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRegisterComponent_Template_input_ngModelChange_27_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.confirmPassword, $event) || (ctx.user.confirmPassword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, NbRegisterComponent_ng_container_29_Template, 3, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(30, NbRegisterComponent_div_30_Template, 6, 2, \"div\", 20);\n          i0.ɵɵelementStart(31, \"button\", 21);\n          i0.ɵɵtext(32, \" Register \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, NbRegisterComponent_section_33_Template, 4, 1, \"section\", 22);\n          i0.ɵɵelementStart(34, \"section\", 23);\n          i0.ɵɵtext(35, \" Already have an account? \");\n          i0.ɵɵelementStart(36, \"a\", 24);\n          i0.ɵɵtext(37, \"Log in\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const form_r13 = i0.ɵɵreference(5);\n          const fullName_r5 = i0.ɵɵreference(10);\n          const email_r6 = i0.ɵɵreference(16);\n          const password_r7 = i0.ɵɵreference(22);\n          const rePass_r8 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.fullName);\n          i0.ɵɵproperty(\"status\", fullName_r5.dirty ? fullName_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.fullName.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.fullName.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.fullName.maxLength\"));\n          i0.ɵɵattribute(\"aria-invalid\", fullName_r5.invalid && fullName_r5.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", fullName_r5.invalid && fullName_r5.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵproperty(\"status\", email_r6.dirty ? email_r6.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n          i0.ɵɵattribute(\"aria-invalid\", email_r6.invalid && email_r6.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", email_r6.invalid && email_r6.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵproperty(\"status\", password_r7.dirty ? password_r7.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n          i0.ɵɵattribute(\"aria-invalid\", password_r7.invalid && password_r7.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", password_r7.invalid && password_r7.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.confirmPassword);\n          i0.ɵɵproperty(\"status\", rePass_r8.dirty ? rePass_r8.invalid || password_r7.value != rePass_r8.value ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"));\n          i0.ɵɵattribute(\"aria-invalid\", rePass_r8.invalid && rePass_r8.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", rePass_r8.invalid && rePass_r8.touched);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getConfigValue(\"forms.register.terms\"));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n          i0.ɵɵproperty(\"disabled\", ctx.submitted || !form_r13.valid);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.socialLinks && ctx.socialLinks.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NbCheckboxComponent, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.PatternValidator, i5.NgModel, i5.NgForm, i4.NbIconComponent],\n      styles: [\"[_nghost-%COMP%]   .title[_ngcontent-%COMP%]{margin-bottom:2rem}\\n\\n\\n\\n\\n\\n\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbRegisterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-register',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Register</h1>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"register()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-name\\\">Full name:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.fullName\\\"\\n           #fullName=\\\"ngModel\\\"\\n           id=\\\"input-name\\\"\\n           name=\\\"fullName\\\"\\n           placeholder=\\\"Full name\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"fullName.dirty ? (fullName.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.fullName.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.fullName.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.fullName.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"fullName.invalid && fullName.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"fullName.invalid && fullName.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.required\\\">\\n        Full name is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.minlength || fullName.errors?.maxlength\\\">\\n        Full name should contains\\n        from {{getConfigValue('forms.validation.fullName.minLength')}}\\n        to {{getConfigValue('forms.validation.fullName.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           placeholder=\\\"Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Repeat password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.dirty ? (rePass.invalid || password.value != rePass.value  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.invalid && rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\" *ngIf=\\\"getConfigValue('forms.register.terms')\\\">\\n    <nb-checkbox name=\\\"terms\\\" [(ngModel)]=\\\"user.terms\\\" [required]=\\\"getConfigValue('forms.register.terms')\\\">\\n      Agree to <a href=\\\"#\\\" target=\\\"_blank\\\"><strong>Terms & Conditions</strong></a>\\n    </nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Register\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Sign in\\\">\\n  Already have an account? <a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Log in</a>\\n</section>\\n\",\n      styles: [\":host .title{margin-bottom:2rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"]\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Router\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbLogoutComponent {\n  constructor(service, options = {}, router) {\n    this.service = service;\n    this.options = options;\n    this.router = router;\n    this.redirectDelay = 0;\n    this.strategy = '';\n    this.redirectDelay = this.getConfigValue('forms.logout.redirectDelay');\n    this.strategy = this.getConfigValue('forms.logout.strategy');\n  }\n  ngOnInit() {\n    this.logout(this.strategy);\n  }\n  logout(strategy) {\n    this.service.logout(strategy).subscribe(result => {\n      const redirect = result.getRedirect();\n      if (redirect) {\n        setTimeout(() => {\n          return this.router.navigateByUrl(redirect);\n        }, this.redirectDelay);\n      }\n    });\n  }\n  getConfigValue(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  static {\n    this.ɵfac = function NbLogoutComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbLogoutComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbLogoutComponent,\n      selectors: [[\"nb-logout\"]],\n      decls: 2,\n      vars: 0,\n      template: function NbLogoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵtext(1, \"Logging out, please wait...\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbLogoutComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-logout',\n      template: \"<div>Logging out, please wait...</div>\\n\"\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_OPTIONS]\n    }]\n  }, {\n    type: i2.Router\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbRequestPasswordComponent {\n  constructor(service, options = {}, cd, router) {\n    this.service = service;\n    this.options = options;\n    this.cd = cd;\n    this.router = router;\n    this.redirectDelay = 0;\n    this.showMessages = {};\n    this.strategy = '';\n    this.submitted = false;\n    this.errors = [];\n    this.messages = [];\n    this.user = {};\n    this.redirectDelay = this.getConfigValue('forms.requestPassword.redirectDelay');\n    this.showMessages = this.getConfigValue('forms.requestPassword.showMessages');\n    this.strategy = this.getConfigValue('forms.requestPassword.strategy');\n  }\n  requestPass() {\n    this.errors = this.messages = [];\n    this.submitted = true;\n    this.service.requestPassword(this.strategy, this.user).subscribe(result => {\n      this.submitted = false;\n      if (result.isSuccess()) {\n        this.messages = result.getMessages();\n      } else {\n        this.errors = result.getErrors();\n      }\n      const redirect = result.getRedirect();\n      if (redirect) {\n        setTimeout(() => {\n          return this.router.navigateByUrl(redirect);\n        }, this.redirectDelay);\n      }\n      this.cd.detectChanges();\n    });\n  }\n  getConfigValue(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  static {\n    this.ɵfac = function NbRequestPasswordComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbRequestPasswordComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbRequestPasswordComponent,\n      selectors: [[\"nb-request-password-page\"]],\n      decls: 23,\n      vars: 10,\n      consts: [[\"requestPassForm\", \"ngForm\"], [\"email\", \"ngModel\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-email\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-email\", \"name\", \"email\", \"pattern\", \".+@.+\\\\..+\", \"placeholder\", \"Email address\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [4, \"ngIf\"], [\"nbButton\", \"\", \"fullWidth\", \"\", \"status\", \"primary\", \"size\", \"large\", 3, \"disabled\"], [\"aria-label\", \"Sign in or sign up\", 1, \"sign-in-or-up\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"]],\n      template: function NbRequestPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h1\", 2);\n          i0.ɵɵtext(1, \"Forgot Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"p\", 3);\n          i0.ɵɵtext(3, \"Enter your email address and we\\u2019ll send a link to reset your password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NbRequestPasswordComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 4)(5, NbRequestPasswordComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 5);\n          i0.ɵɵelementStart(6, \"form\", 6, 0);\n          i0.ɵɵlistener(\"ngSubmit\", function NbRequestPasswordComponent_Template_form_ngSubmit_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.requestPass());\n          });\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"label\", 8);\n          i0.ɵɵtext(10, \"Enter your email address:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 9, 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbRequestPasswordComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.email, $event) || (ctx.user.email = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, NbRequestPasswordComponent_ng_container_13_Template, 3, 2, \"ng-container\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 11);\n          i0.ɵɵtext(15, \" Request password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"section\", 12)(17, \"p\")(18, \"a\", 13);\n          i0.ɵɵtext(19, \"Back to Log In\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"p\")(21, \"a\", 14);\n          i0.ɵɵtext(22, \"Register\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const requestPassForm_r6 = i0.ɵɵreference(7);\n          const email_r5 = i0.ɵɵreference(12);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵproperty(\"status\", email_r5.dirty ? email_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.email.required\"));\n          i0.ɵɵattribute(\"aria-invalid\", email_r5.invalid && email_r5.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", email_r5.invalid && email_r5.touched);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n          i0.ɵɵproperty(\"disabled\", ctx.submitted || !requestPassForm_r6.valid);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.PatternValidator, i5.NgModel, i5.NgForm],\n      styles: [\"[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]:last-of-type{margin-bottom:3rem}\\n\\n\\n\\n\\n\\n\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbRequestPasswordComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-request-password-page',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Forgot Password</h1>\\n<p class=\\\"sub-title\\\">Enter your email address and we\\u2019ll send a link to reset your password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"requestPass()\\\" #requestPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Enter your email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !requestPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Request password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a routerLink=\\\"../register\\\" class=\\\"text-link\\\">Register</a></p>\\n</section>\\n\",\n      styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"]\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Router\n  }], null);\n})();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbResetPasswordComponent {\n  constructor(service, options = {}, cd, router) {\n    this.service = service;\n    this.options = options;\n    this.cd = cd;\n    this.router = router;\n    this.redirectDelay = 0;\n    this.showMessages = {};\n    this.strategy = '';\n    this.submitted = false;\n    this.errors = [];\n    this.messages = [];\n    this.user = {};\n    this.redirectDelay = this.getConfigValue('forms.resetPassword.redirectDelay');\n    this.showMessages = this.getConfigValue('forms.resetPassword.showMessages');\n    this.strategy = this.getConfigValue('forms.resetPassword.strategy');\n  }\n  resetPass() {\n    this.errors = this.messages = [];\n    this.submitted = true;\n    this.service.resetPassword(this.strategy, this.user).subscribe(result => {\n      this.submitted = false;\n      if (result.isSuccess()) {\n        this.messages = result.getMessages();\n      } else {\n        this.errors = result.getErrors();\n      }\n      const redirect = result.getRedirect();\n      if (redirect) {\n        setTimeout(() => {\n          return this.router.navigateByUrl(redirect);\n        }, this.redirectDelay);\n      }\n      this.cd.detectChanges();\n    });\n  }\n  getConfigValue(key) {\n    return getDeepFromObject(this.options, key, null);\n  }\n  static {\n    this.ɵfac = function NbResetPasswordComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbResetPasswordComponent)(i0.ɵɵdirectiveInject(NbAuthService), i0.ɵɵdirectiveInject(NB_AUTH_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NbResetPasswordComponent,\n      selectors: [[\"nb-reset-password-page\"]],\n      decls: 29,\n      vars: 17,\n      consts: [[\"resetPassForm\", \"ngForm\"], [\"password\", \"ngModel\"], [\"rePass\", \"ngModel\"], [\"id\", \"title\", 1, \"title\"], [1, \"sub-title\"], [\"outline\", \"danger\", \"role\", \"alert\", 4, \"ngIf\"], [\"outline\", \"success\", \"role\", \"alert\", 4, \"ngIf\"], [\"aria-labelledby\", \"title\", 3, \"ngSubmit\"], [1, \"form-control-group\"], [\"for\", \"input-password\", 1, \"label\"], [\"nbInput\", \"\", \"type\", \"password\", \"id\", \"input-password\", \"name\", \"password\", \"placeholder\", \"New Password\", \"autofocus\", \"\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 1, \"first\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\", \"minlength\", \"maxlength\"], [4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"input-re-password\", 1, \"label\"], [\"nbInput\", \"\", \"id\", \"input-re-password\", \"name\", \"rePass\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"fullWidth\", \"\", \"fieldSize\", \"large\", 1, \"last\", 3, \"ngModelChange\", \"ngModel\", \"status\", \"required\"], [\"nbButton\", \"\", \"status\", \"primary\", \"fullWidth\", \"\", \"size\", \"large\", 3, \"disabled\"], [\"aria-label\", \"Sign in or sign up\", 1, \"sign-in-or-up\"], [\"routerLink\", \"../login\", 1, \"text-link\"], [\"routerLink\", \"../register\", 1, \"text-link\"], [\"outline\", \"danger\", \"role\", \"alert\"], [1, \"alert-title\"], [1, \"alert-message-list\"], [\"class\", \"alert-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"alert-message\"], [\"outline\", \"success\", \"role\", \"alert\"], [\"class\", \"caption status-danger\", 4, \"ngIf\"], [1, \"caption\", \"status-danger\"]],\n      template: function NbResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"h1\", 3);\n          i0.ɵɵtext(1, \"Change password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"p\", 4);\n          i0.ɵɵtext(3, \"Please set a new password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NbResetPasswordComponent_nb_alert_4_Template, 6, 1, \"nb-alert\", 5)(5, NbResetPasswordComponent_nb_alert_5_Template, 6, 1, \"nb-alert\", 6);\n          i0.ɵɵelementStart(6, \"form\", 7, 0);\n          i0.ɵɵlistener(\"ngSubmit\", function NbResetPasswordComponent_Template_form_ngSubmit_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetPass());\n          });\n          i0.ɵɵelementStart(8, \"div\", 8)(9, \"label\", 9);\n          i0.ɵɵtext(10, \"New Password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 10, 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbResetPasswordComponent_Template_input_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.password, $event) || (ctx.user.password = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, NbResetPasswordComponent_ng_container_13_Template, 3, 2, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n          i0.ɵɵtext(16, \"Confirm Password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 14, 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NbResetPasswordComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.user.confirmPassword, $event) || (ctx.user.confirmPassword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(19, NbResetPasswordComponent_ng_container_19_Template, 3, 2, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 15);\n          i0.ɵɵtext(21, \" Change password \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"section\", 16)(23, \"p\")(24, \"a\", 17);\n          i0.ɵɵtext(25, \"Back to Log In\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"p\")(27, \"a\", 18);\n          i0.ɵɵtext(28, \"Register\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          const resetPassForm_r7 = i0.ɵɵreference(7);\n          const password_r5 = i0.ɵɵreference(12);\n          const rePass_r6 = i0.ɵɵreference(18);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.error && (ctx.errors == null ? null : ctx.errors.length) && !ctx.submitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showMessages.success && (ctx.messages == null ? null : ctx.messages.length) && !ctx.submitted);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵproperty(\"status\", password_r5.dirty ? password_r5.invalid ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"))(\"minlength\", ctx.getConfigValue(\"forms.validation.password.minLength\"))(\"maxlength\", ctx.getConfigValue(\"forms.validation.password.maxLength\"));\n          i0.ɵɵattribute(\"aria-invalid\", password_r5.invalid && password_r5.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", password_r5.invalid && password_r5.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.user.confirmPassword);\n          i0.ɵɵproperty(\"status\", rePass_r6.touched ? rePass_r6.invalid || password_r5.value != rePass_r6.value ? \"danger\" : \"success\" : \"basic\")(\"required\", ctx.getConfigValue(\"forms.validation.password.required\"));\n          i0.ɵɵattribute(\"aria-invalid\", rePass_r6.invalid && rePass_r6.touched ? true : null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", rePass_r6.touched);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"btn-pulse\", ctx.submitted);\n          i0.ɵɵproperty(\"disabled\", ctx.submitted || !resetPassForm_r7.valid);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NbAlertComponent, i4.NbInputDirective, i4.NbButtonComponent, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.MaxLengthValidator, i5.NgModel, i5.NgForm],\n      styles: [_c1],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbResetPasswordComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nb-reset-password-page',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Change password</h1>\\n<p class=\\\"sub-title\\\">Please set a new password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"resetPass()\\\" #resetPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">New Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           class=\\\"first\\\"\\n           placeholder=\\\"New Password\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contains\\n        from {{getConfigValue('forms.validation.password.minLength')}}\\n        to {{getConfigValue('forms.validation.password.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Confirm Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           type=\\\"password\\\"\\n           class=\\\"last\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.touched\\n               ? (rePass.invalid || password.value != rePass.value ? 'danger' : 'success')\\n               : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.invalid && rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          status=\\\"primary\\\"\\n          fullWidth\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !resetPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Change password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a></p>\\n</section>\\n\",\n      styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"]\n    }]\n  }], () => [{\n    type: NbAuthService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Router\n  }], null);\n})();\nfunction nbStrategiesFactory(options, injector) {\n  const strategies = [];\n  options.strategies.forEach(([strategyClass, strategyOptions]) => {\n    const strategy = injector.get(strategyClass);\n    strategy.setOptions(strategyOptions);\n    strategies.push(strategy);\n  });\n  return strategies;\n}\nfunction nbTokensFactory(strategies) {\n  const tokens = [];\n  strategies.forEach(strategy => {\n    tokens.push(strategy.getOption('token.class'));\n  });\n  return tokens;\n}\nfunction nbOptionsFactory(options) {\n  return deepExtend(defaultAuthOptions, options);\n}\nfunction nbNoOpInterceptorFilter(req) {\n  return true;\n}\nclass NbAuthModule {\n  static forRoot(nbAuthOptions) {\n    return {\n      ngModule: NbAuthModule,\n      providers: [{\n        provide: NB_AUTH_USER_OPTIONS,\n        useValue: nbAuthOptions\n      }, {\n        provide: NB_AUTH_OPTIONS,\n        useFactory: nbOptionsFactory,\n        deps: [NB_AUTH_USER_OPTIONS]\n      }, {\n        provide: NB_AUTH_STRATEGIES,\n        useFactory: nbStrategiesFactory,\n        deps: [NB_AUTH_OPTIONS, Injector]\n      }, {\n        provide: NB_AUTH_TOKENS,\n        useFactory: nbTokensFactory,\n        deps: [NB_AUTH_STRATEGIES]\n      }, {\n        provide: NB_AUTH_FALLBACK_TOKEN,\n        useValue: NbAuthSimpleToken\n      }, {\n        provide: NB_AUTH_INTERCEPTOR_HEADER,\n        useValue: 'Authorization'\n      }, {\n        provide: NB_AUTH_TOKEN_INTERCEPTOR_FILTER,\n        useValue: nbNoOpInterceptorFilter\n      }, {\n        provide: NbTokenStorage,\n        useClass: NbTokenLocalStorage\n      }, NbAuthTokenParceler, NbAuthService, NbTokenService, NbDummyAuthStrategy, NbPasswordAuthStrategy, NbOAuth2AuthStrategy]\n    };\n  }\n  static {\n    this.ɵfac = function NbAuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NbAuthModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, RouterModule, FormsModule, NbIconModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, RouterModule, FormsModule, NbIconModule],\n      declarations: [NbAuthComponent, NbAuthBlockComponent, NbLoginComponent, NbRegisterComponent, NbRequestPasswordComponent, NbResetPasswordComponent, NbLogoutComponent],\n      exports: [NbAuthComponent, NbAuthBlockComponent, NbLoginComponent, NbRegisterComponent, NbRequestPasswordComponent, NbResetPasswordComponent, NbLogoutComponent]\n    }]\n  }], null, null);\n})();\nconst routes = [{\n  path: 'auth',\n  component: NbAuthComponent,\n  children: [{\n    path: '',\n    component: NbLoginComponent\n  }, {\n    path: 'login',\n    component: NbLoginComponent\n  }, {\n    path: 'register',\n    component: NbRegisterComponent\n  }, {\n    path: 'logout',\n    component: NbLogoutComponent\n  }, {\n    path: 'request-password',\n    component: NbRequestPasswordComponent\n  }, {\n    path: 'reset-password',\n    component: NbResetPasswordComponent\n  }]\n}];\nclass NbAuthJWTInterceptor {\n  constructor(injector, filter) {\n    this.injector = injector;\n    this.filter = filter;\n  }\n  intercept(req, next) {\n    // do not intercept request whose urls are filtered by the injected filter\n    if (!this.filter(req)) {\n      return this.authService.isAuthenticatedOrRefresh().pipe(switchMap(authenticated => {\n        if (authenticated) {\n          return this.authService.getToken().pipe(switchMap(token => {\n            const JWT = `Bearer ${token.getValue()}`;\n            req = req.clone({\n              setHeaders: {\n                Authorization: JWT\n              }\n            });\n            return next.handle(req);\n          }));\n        } else {\n          // Request is sent to server without authentication so that the client code\n          // receives the 401/403 error and can act as desired ('session expired', redirect to login, aso)\n          return next.handle(req);\n        }\n      }));\n    } else {\n      return next.handle(req);\n    }\n  }\n  get authService() {\n    return this.injector.get(NbAuthService);\n  }\n  static {\n    this.ɵfac = function NbAuthJWTInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthJWTInterceptor)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(NB_AUTH_TOKEN_INTERCEPTOR_FILTER));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAuthJWTInterceptor,\n      factory: NbAuthJWTInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthJWTInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_TOKEN_INTERCEPTOR_FILTER]\n    }]\n  }], null);\n})();\nclass NbAuthSimpleInterceptor {\n  constructor(injector, headerName = 'Authorization') {\n    this.injector = injector;\n    this.headerName = headerName;\n  }\n  intercept(req, next) {\n    return this.authService.getToken().pipe(switchMap(token => {\n      if (token && token.getValue()) {\n        req = req.clone({\n          setHeaders: {\n            [this.headerName]: token.getValue()\n          }\n        });\n      }\n      return next.handle(req);\n    }));\n  }\n  get authService() {\n    return this.injector.get(NbAuthService);\n  }\n  static {\n    this.ɵfac = function NbAuthSimpleInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbAuthSimpleInterceptor)(i0.ɵɵinject(i0.Injector), i0.ɵɵinject(NB_AUTH_INTERCEPTOR_HEADER));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NbAuthSimpleInterceptor,\n      factory: NbAuthSimpleInterceptor.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbAuthSimpleInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NB_AUTH_INTERCEPTOR_HEADER]\n    }]\n  }], null);\n})();\nclass NbUser {\n  constructor(id, email, password, rememberMe, terms, confirmPassword, fullName) {\n    this.id = id;\n    this.email = email;\n    this.password = password;\n    this.rememberMe = rememberMe;\n    this.terms = terms;\n    this.confirmPassword = confirmPassword;\n    this.fullName = fullName;\n  }\n}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_AUTH_FALLBACK_TOKEN, NB_AUTH_INTERCEPTOR_HEADER, NB_AUTH_OPTIONS, NB_AUTH_STRATEGIES, NB_AUTH_TOKENS, NB_AUTH_TOKEN_INTERCEPTOR_FILTER, NB_AUTH_USER_OPTIONS, NbAuthBlockComponent, NbAuthComponent, NbAuthEmptyTokenError, NbAuthIllegalJWTTokenError, NbAuthIllegalTokenError, NbAuthJWTInterceptor, NbAuthJWTToken, NbAuthModule, NbAuthOAuth2JWTToken, NbAuthOAuth2Token, NbAuthResult, NbAuthService, NbAuthSimpleInterceptor, NbAuthSimpleToken, NbAuthStrategy, NbAuthStrategyOptions, NbAuthToken, NbAuthTokenNotFoundError, NbAuthTokenParceler, NbDummyAuthStrategy, NbDummyAuthStrategyOptions, NbLoginComponent, NbLogoutComponent, NbOAuth2AuthStrategy, NbOAuth2AuthStrategyOptions, NbOAuth2ClientAuthMethod, NbOAuth2GrantType, NbOAuth2ResponseType, NbPasswordAuthStrategy, NbPasswordAuthStrategyOptions, NbRegisterComponent, NbRequestPasswordComponent, NbResetPasswordComponent, NbTokenLocalStorage, NbTokenService, NbTokenStorage, NbUser, auth2StrategyOptions, b64DecodeUnicode, b64decode, decodeJwtPayload, deepExtend, defaultAuthOptions, dummyStrategyOptions, getDeepFromObject, nbAuthCreateToken, nbNoOpInterceptorFilter, nbOptionsFactory, nbStrategiesFactory, nbTokensFactory, passwordStrategyOptions, routes, urlBase64Decode };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Injectable", "Inject", "Component", "ChangeDetectionStrategy", "Injector", "NgModule", "i3", "CommonModule", "i2", "RouterModule", "i5", "FormsModule", "i4", "NB_WINDOW", "NbLayoutModule", "NbCardModule", "NbCheckboxModule", "NbAlertModule", "NbInputModule", "NbButtonModule", "NbIconModule", "BehaviorSubject", "of", "Subject", "filter", "share", "map", "switchMap", "delay", "catchError", "takeUntil", "i1", "HttpResponse", "HttpHeaders", "HttpErrorResponse", "_c0", "NbLoginComponent_nb_alert_4_li_5_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "error_r2", "$implicit", "ɵɵadvance", "ɵɵtextInterpolate", "NbLoginComponent_nb_alert_4_Template", "ɵɵtemplate", "ctx_r2", "ɵɵnextContext", "ɵɵproperty", "errors", "NbLoginComponent_nb_alert_5_li_5_Template", "message_r4", "NbLoginComponent_nb_alert_5_Template", "messages", "NbLoginComponent_ng_container_13_p_1_Template", "NbLoginComponent_ng_container_13_p_2_Template", "NbLoginComponent_ng_container_13_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "email_r5", "ɵɵreference", "required", "pattern", "NbLoginComponent_ng_container_22_p_1_Template", "NbLoginComponent_ng_container_22_p_2_Template", "ɵɵtextInterpolate2", "getConfigValue", "NbLoginComponent_ng_container_22_Template", "password_r6", "minlength", "maxlength", "NbLoginComponent_nb_checkbox_24_Template", "_r7", "ɵɵgetCurrentView", "ɵɵtwoWayListener", "NbLoginComponent_nb_checkbox_24_Template_nb_checkbox_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "ɵɵtwoWayBindingSet", "user", "rememberMe", "ɵɵresetView", "ɵɵtwoWayProperty", "NbLoginComponent_section_27_ng_container_3_a_1_nb_icon_1_Template", "ɵɵelement", "socialLink_r8", "icon", "NbLoginComponent_section_27_ng_container_3_a_1_ng_template_2_Template", "title", "NbLoginComponent_section_27_ng_container_3_a_1_Template", "ɵɵtemplateRefExtractor", "title_r9", "ɵɵclassProp", "link", "ɵɵattribute", "target", "NbLoginComponent_section_27_ng_container_3_a_2_nb_icon_1_Template", "NbLoginComponent_section_27_ng_container_3_a_2_ng_template_2_Template", "NbLoginComponent_section_27_ng_container_3_a_2_Template", "title_r10", "url", "ɵɵsanitizeUrl", "NbLoginComponent_section_27_ng_container_3_Template", "NbLoginComponent_section_27_Template", "socialLinks", "NbRegisterComponent_nb_alert_2_li_5_Template", "NbRegisterComponent_nb_alert_2_Template", "NbRegisterComponent_nb_alert_3_li_5_Template", "NbRegisterComponent_nb_alert_3_Template", "NbRegisterComponent_ng_container_11_p_1_Template", "NbRegisterComponent_ng_container_11_p_2_Template", "NbRegisterComponent_ng_container_11_Template", "fullName_r5", "NbRegisterComponent_ng_container_17_p_1_Template", "NbRegisterComponent_ng_container_17_p_2_Template", "NbRegisterComponent_ng_container_17_Template", "email_r6", "NbRegisterComponent_ng_container_23_p_1_Template", "NbRegisterComponent_ng_container_23_p_2_Template", "NbRegisterComponent_ng_container_23_Template", "password_r7", "NbRegisterComponent_ng_container_29_p_1_Template", "NbRegisterComponent_ng_container_29_p_2_Template", "NbRegisterComponent_ng_container_29_Template", "rePass_r8", "value", "NbRegisterComponent_div_30_Template", "_r9", "NbRegisterComponent_div_30_Template_nb_checkbox_ngModelChange_1_listener", "terms", "NbRegisterComponent_section_33_ng_container_3_a_1_nb_icon_1_Template", "socialLink_r10", "NbRegisterComponent_section_33_ng_container_3_a_1_ng_template_2_Template", "NbRegisterComponent_section_33_ng_container_3_a_1_Template", "title_r11", "NbRegisterComponent_section_33_ng_container_3_a_2_nb_icon_1_Template", "NbRegisterComponent_section_33_ng_container_3_a_2_ng_template_2_Template", "NbRegisterComponent_section_33_ng_container_3_a_2_Template", "title_r12", "NbRegisterComponent_section_33_ng_container_3_Template", "NbRegisterComponent_section_33_Template", "NbRequestPasswordComponent_nb_alert_4_li_5_Template", "NbRequestPasswordComponent_nb_alert_4_Template", "NbRequestPasswordComponent_nb_alert_5_li_5_Template", "NbRequestPasswordComponent_nb_alert_5_Template", "NbRequestPasswordComponent_ng_container_13_p_1_Template", "NbRequestPasswordComponent_ng_container_13_p_2_Template", "NbRequestPasswordComponent_ng_container_13_Template", "NbResetPasswordComponent_nb_alert_4_li_5_Template", "NbResetPasswordComponent_nb_alert_4_Template", "NbResetPasswordComponent_nb_alert_5_li_5_Template", "NbResetPasswordComponent_nb_alert_5_Template", "NbResetPasswordComponent_ng_container_13_p_1_Template", "NbResetPasswordComponent_ng_container_13_p_2_Template", "NbResetPasswordComponent_ng_container_13_Template", "password_r5", "NbResetPasswordComponent_ng_container_19_p_1_Template", "NbResetPasswordComponent_ng_container_19_p_2_Template", "NbResetPasswordComponent_ng_container_19_Template", "rePass_r6", "invalid", "_c1", "defaultAuthOptions", "strategies", "forms", "login", "redirectDelay", "strategy", "showMessages", "success", "error", "register", "requestPassword", "resetPassword", "logout", "validation", "password", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "email", "fullName", "NB_AUTH_OPTIONS", "NB_AUTH_USER_OPTIONS", "NB_AUTH_STRATEGIES", "NB_AUTH_TOKENS", "NB_AUTH_INTERCEPTOR_HEADER", "NB_AUTH_TOKEN_INTERCEPTOR_FILTER", "deepExtend", "objects", "arguments", "length", "args", "Array", "prototype", "slice", "call", "val", "src", "for<PERSON>ach", "obj", "isArray", "Object", "keys", "key", "deepCloneArray", "isSpecificValue", "cloneSpecificValue", "Date", "RegExp", "getTime", "Error", "arr", "clone", "item", "index", "getDeepFromObject", "object", "name", "defaultValue", "split", "level", "k", "undefined", "urlBase64Decode", "str", "output", "replace", "b64DecodeUnicode", "b64decode", "chars", "String", "bc", "bs", "buffer", "idx", "char<PERSON>t", "fromCharCode", "indexOf", "decodeURIComponent", "c", "charCodeAt", "toString", "join", "NbAuthToken", "constructor", "payload", "getName", "NAME", "getPayload", "NbAuthTokenNotFoundError", "message", "setPrototypeOf", "new", "NbAuthIllegalTokenError", "NbAuthEmptyTokenError", "NbAuthIllegalJWTTokenError", "nbAuthCreateToken", "tokenClass", "token", "ownerStrategyName", "createdAt", "decodeJwtPayload", "parts", "decoded", "e", "JSON", "parse", "NbAuthSimpleToken", "parsePayload", "err", "prepareCreatedAt", "date", "getCreatedAt", "getValue", "getOwnerStrategyName", "<PERSON><PERSON><PERSON><PERSON>", "NbAuthJWTToken", "iat", "Number", "getTokenExpDate", "hasOwnProperty", "setUTCSeconds", "exp", "prepareOAuth2Token", "data", "NbAuthOAuth2Token", "access_token", "getRefreshToken", "refresh_token", "setRefreshToken", "refreshToken", "getType", "token_type", "expires_in", "stringify", "NbAuthOAuth2JWTToken", "parseAccessTokenPayload", "accessToken", "accessTokenPayload", "getAccessTokenPayload", "NB_AUTH_FALLBACK_TOKEN", "NbAuthTokenParceler", "fallbackClass", "tokenClasses", "wrap", "unwrap", "tokenValue", "tokenOwnerStrategyName", "tokenCreatedAt", "tokenPack", "parseTokenPack", "getClassByName", "find", "ɵfac", "NbAuthTokenParceler_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "decorators", "NbTokenStorage", "NbTokenLocalStorage", "parceler", "get", "raw", "localStorage", "getItem", "set", "setItem", "clear", "removeItem", "NbTokenLocalStorage_Factory", "NbTokenService", "tokenStorage", "token$", "publishStoredToken", "tokenChange", "pipe", "next", "NbTokenService_Factory", "NbAuthService", "tokenService", "getToken", "isAuthenticated", "isAuthenticatedOrRefresh", "res", "isSuccess", "onTokenChange", "onAuthenticationChange", "authenticate", "strategyName", "getStrategy", "result", "processResultToken", "found", "TypeError", "NbAuthService_Factory", "NbAuthStrategy", "setOptions", "options", "defaultOptions", "getOption", "createToken", "failWhenInvalidToken", "createFailResponse", "body", "status", "createSuccessResponse", "getActionEndpoint", "action", "actionEndpoint", "baseEndpoint", "getHeaders", "customHeaders", "headers", "entries", "append", "NbAuthResult", "response", "redirect", "concat", "getResponse", "getRedirect", "getErrors", "getMessages", "isFailure", "NbAuthStrategyOptions", "NbDummyAuthStrategyOptions", "class", "alwaysFail", "dummyStrategyOptions", "NbDummyAuthStrategy", "setup", "createDummyResult", "ɵNbDummyAuthStrategy_BaseFactory", "NbDummyAuthStrategy_Factory", "ɵɵgetInheritedFactory", "NbOAuth2ResponseType", "NbOAuth2GrantType", "NbOAuth2ClientAuthMethod", "NbOAuth2AuthStrategyOptions", "clientId", "clientSecret", "clientAuthMethod", "NONE", "failure", "defaultErrors", "defaultMessages", "authorize", "endpoint", "responseType", "CODE", "requireValidToken", "grantType", "AUTHORIZATION_CODE", "refresh", "REFRESH_TOKEN", "auth2StrategyOptions", "NbOAuth2AuthStrategy", "http", "route", "window", "redirectResultHandlers", "snapshot", "queryParams", "params", "code", "requestToken", "TOKEN", "module", "fragment", "parseHashAsQueryParams", "push", "redirectResults", "PASSWORD", "passwordToken", "isRedirectResult", "authorizeRedirect", "getAuthorizationResult", "redirectResultHandler", "post", "buildRefreshRequestData", "createRefreshedToken", "handleResponseError", "username", "buildPasswordRequestData", "location", "href", "buildRedirectUrl", "buildCodeRequestData", "grant_type", "redirect_uri", "client_id", "urlEncodeParameters", "cleanParams", "addCredentialsToParams", "scope", "buildAuthHeader", "BASIC", "Authorization", "btoa", "authHeaders", "<PERSON><PERSON><PERSON>", "headerValue", "getAll", "REQUEST_BODY", "client_secret", "error_description", "response_type", "state", "query", "hash", "reduce", "acc", "part", "encodeURIComponent", "existingToken", "refreshedToken", "NbOAuth2AuthStrategy_Factory", "HttpClient", "ActivatedRoute", "NbPasswordAuthStrategyOptions", "method", "requestPass", "resetPass", "resetPasswordTokenKey", "getter", "passwordStrategyOptions", "NbPasswordAuthStrategy", "request", "observe", "<PERSON><PERSON><PERSON>", "NbPasswordAuthStrategy_Factory", "NbAuthBlockComponent", "NbAuthBlockComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "ngContentSelectors", "decls", "vars", "template", "NbAuthBlockComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "selector", "NbAuthComponent", "auth", "destroy$", "authenticated", "subscription", "subscribe", "back", "ngOnDestroy", "complete", "NbAuthComponent_Factory", "ɵɵdirectiveInject", "Location", "consts", "NbAuthComponent_Template", "ɵɵlistener", "NbAuthComponent_Template_a_click_5_listener", "dependencies", "NbLayoutComponent", "NbLayoutColumnComponent", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "RouterOutlet", "NbIconComponent", "NbLoginComponent", "service", "cd", "router", "submitted", "setTimeout", "navigateByUrl", "detectChanges", "NbLoginComponent_Factory", "ChangeDetectorRef", "Router", "NbLoginComponent_Template", "_r1", "NbLoginComponent_Template_form_ngSubmit_6_listener", "NbLoginComponent_Template_input_ngModelChange_11_listener", "NbLoginComponent_Template_input_ngModelChange_20_listener", "form_r11", "dirty", "touched", "valid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NbCheckboxComponent", "NbAlertComponent", "NbInputDirective", "NbButtonComponent", "RouterLink", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinLengthValidator", "MaxLengthValidator", "Pat<PERSON>Vali<PERSON><PERSON>", "NgModel", "NgForm", "encapsulation", "changeDetection", "OnPush", "NbRegisterComponent", "NbRegisterComponent_Factory", "NbRegisterComponent_Template", "NbRegisterComponent_Template_form_ngSubmit_4_listener", "NbRegisterComponent_Template_input_ngModelChange_9_listener", "NbRegisterComponent_Template_input_ngModelChange_15_listener", "NbRegisterComponent_Template_input_ngModelChange_21_listener", "NbRegisterComponent_Template_input_ngModelChange_27_listener", "confirmPassword", "form_r13", "NbLogoutComponent", "ngOnInit", "NbLogoutComponent_Factory", "NbLogoutComponent_Template", "NbRequestPasswordComponent", "NbRequestPasswordComponent_Factory", "NbRequestPasswordComponent_Template", "NbRequestPasswordComponent_Template_form_ngSubmit_6_listener", "NbRequestPasswordComponent_Template_input_ngModelChange_11_listener", "requestPassForm_r6", "NbResetPasswordComponent", "NbResetPasswordComponent_Factory", "NbResetPasswordComponent_Template", "NbResetPasswordComponent_Template_form_ngSubmit_6_listener", "NbResetPasswordComponent_Template_input_ngModelChange_11_listener", "NbResetPasswordComponent_Template_input_ngModelChange_17_listener", "resetPassForm_r7", "nbStrategiesFactory", "injector", "strategyClass", "strategyOptions", "nbTokensFactory", "tokens", "nbOptionsFactory", "nbNoOpInterceptorFilter", "req", "NbAuthModule", "forRoot", "nbAuthOptions", "ngModule", "providers", "provide", "useValue", "useFactory", "deps", "useClass", "NbAuthModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports", "routes", "path", "component", "children", "NbAuthJWTInterceptor", "intercept", "authService", "JWT", "setHeaders", "handle", "NbAuthJWTInterceptor_Factory", "NbAuthSimpleInterceptor", "headerName", "NbAuthSimpleInterceptor_Factory", "NbUser", "id"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@nebular/auth/fesm2022/nebular-auth.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, Inject, Component, ChangeDetectionStrategy, Injector, NgModule } from '@angular/core';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i4 from '@nebular/theme';\nimport { NB_WINDOW, NbLayoutModule, NbCardModule, NbCheckboxModule, NbAlertModule, NbInputModule, NbButtonModule, NbIconModule } from '@nebular/theme';\nimport { BehaviorSubject, of, Subject } from 'rxjs';\nimport { filter, share, map, switchMap, delay, catchError, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpResponse, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\n\nconst socialLinks = [];\nconst defaultAuthOptions = {\n    strategies: [],\n    forms: {\n        login: {\n            redirectDelay: 500, // delay before redirect after a successful login, while success message is shown to the user\n            strategy: 'email', // provider id key. If you have multiple strategies, or what to use your own\n            rememberMe: true, // whether to show or not the `rememberMe` checkbox\n            showMessages: {\n                success: true,\n                error: true,\n            },\n            socialLinks: socialLinks, // social links at the bottom of a page\n        },\n        register: {\n            redirectDelay: 500,\n            strategy: 'email',\n            showMessages: {\n                success: true,\n                error: true,\n            },\n            terms: true,\n            socialLinks: socialLinks,\n        },\n        requestPassword: {\n            redirectDelay: 500,\n            strategy: 'email',\n            showMessages: {\n                success: true,\n                error: true,\n            },\n            socialLinks: socialLinks,\n        },\n        resetPassword: {\n            redirectDelay: 500,\n            strategy: 'email',\n            showMessages: {\n                success: true,\n                error: true,\n            },\n            socialLinks: socialLinks,\n        },\n        logout: {\n            redirectDelay: 500,\n            strategy: 'email',\n        },\n        validation: {\n            password: {\n                required: true,\n                minLength: 4,\n                maxLength: 50,\n            },\n            email: {\n                required: true,\n            },\n            fullName: {\n                required: false,\n                minLength: 4,\n                maxLength: 50,\n            },\n        },\n    },\n};\nconst NB_AUTH_OPTIONS = new InjectionToken('Nebular Auth Options');\nconst NB_AUTH_USER_OPTIONS = new InjectionToken('Nebular User Auth Options');\nconst NB_AUTH_STRATEGIES = new InjectionToken('Nebular Auth Strategies');\nconst NB_AUTH_TOKENS = new InjectionToken('Nebular Auth Tokens');\nconst NB_AUTH_INTERCEPTOR_HEADER = new InjectionToken('Nebular Simple Interceptor Header');\nconst NB_AUTH_TOKEN_INTERCEPTOR_FILTER = new InjectionToken('Nebular Interceptor Filter');\n\n/**\n * Extending object that entered in first argument.\n *\n * Returns extended object or false if have no target object or incorrect type.\n *\n * If you wish to clone source object (without modify it), just use empty new\n * object as first argument, like this:\n *   deepExtend({}, yourObj_1, [yourObj_N]);\n */\nconst deepExtend = function (...objects) {\n    if (arguments.length < 1 || typeof arguments[0] !== 'object') {\n        return false;\n    }\n    if (arguments.length < 2) {\n        return arguments[0];\n    }\n    const target = arguments[0];\n    // convert arguments to array and cut off target object\n    const args = Array.prototype.slice.call(arguments, 1);\n    let val, src;\n    args.forEach(function (obj) {\n        // skip argument if it is array or isn't object\n        if (typeof obj !== 'object' || Array.isArray(obj)) {\n            return;\n        }\n        Object.keys(obj).forEach(function (key) {\n            src = target[key]; // source value\n            val = obj[key]; // new value\n            // recursion prevention\n            if (val === target) {\n                return;\n                /**\n                 * if new value isn't object then just overwrite by new value\n                 * instead of extending.\n                 */\n            }\n            else if (typeof val !== 'object' || val === null) {\n                target[key] = val;\n                return;\n                // just clone arrays (and recursive clone objects inside)\n            }\n            else if (Array.isArray(val)) {\n                target[key] = deepCloneArray(val);\n                return;\n                // custom cloning and overwrite for specific objects\n            }\n            else if (isSpecificValue(val)) {\n                target[key] = cloneSpecificValue(val);\n                return;\n                // overwrite by new value if source isn't object or array\n            }\n            else if (typeof src !== 'object' || src === null || Array.isArray(src)) {\n                target[key] = deepExtend({}, val);\n                return;\n                // source value and new value is objects both, extending...\n            }\n            else {\n                target[key] = deepExtend(src, val);\n                return;\n            }\n        });\n    });\n    return target;\n};\nfunction isSpecificValue(val) {\n    return (val instanceof Date\n        || val instanceof RegExp) ? true : false;\n}\nfunction cloneSpecificValue(val) {\n    if (val instanceof Date) {\n        return new Date(val.getTime());\n    }\n    else if (val instanceof RegExp) {\n        return new RegExp(val);\n    }\n    else {\n        throw new Error('cloneSpecificValue: Unexpected situation');\n    }\n}\n/**\n * Recursive cloning array.\n */\nfunction deepCloneArray(arr) {\n    const clone = [];\n    arr.forEach(function (item, index) {\n        if (typeof item === 'object' && item !== null) {\n            if (Array.isArray(item)) {\n                clone[index] = deepCloneArray(item);\n            }\n            else if (isSpecificValue(item)) {\n                clone[index] = cloneSpecificValue(item);\n            }\n            else {\n                clone[index] = deepExtend({}, item);\n            }\n        }\n        else {\n            clone[index] = item;\n        }\n    });\n    return clone;\n}\n// getDeepFromObject({result: {data: 1}}, 'result.data', 2); // returns 1\nfunction getDeepFromObject(object = {}, name, defaultValue) {\n    const keys = name.split('.');\n    // clone the object\n    let level = deepExtend({}, object || {});\n    keys.forEach((k) => {\n        if (level && typeof level[k] !== 'undefined') {\n            level = level[k];\n        }\n        else {\n            level = undefined;\n        }\n    });\n    return typeof level === 'undefined' ? defaultValue : level;\n}\nfunction urlBase64Decode(str) {\n    let output = str.replace(/-/g, '+').replace(/_/g, '/');\n    switch (output.length % 4) {\n        case 0: {\n            break;\n        }\n        case 2: {\n            output += '==';\n            break;\n        }\n        case 3: {\n            output += '=';\n            break;\n        }\n        default: {\n            throw new Error('Illegal base64url string!');\n        }\n    }\n    return b64DecodeUnicode(output);\n}\nfunction b64decode(str) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    let output = '';\n    str = String(str).replace(/=+$/, '');\n    if (str.length % 4 === 1) {\n        throw new Error(`'atob' failed: The string to be decoded is not correctly encoded.`);\n    }\n    for (\n    // initialize result and counters\n    let bc = 0, bs, buffer, idx = 0; \n    // get next character\n    buffer = str.charAt(idx++); \n    // character found in table? initialize bit storage and add its ascii value;\n    ~buffer && (bs = bc % 4 ? bs * 64 + buffer : buffer,\n        // and if not first of each 4 characters,\n        // convert the first 8 bits to one ascii character\n        bc++ % 4) ? output += String.fromCharCode(255 & bs >> (-2 * bc & 6)) : 0) {\n        // try to find character in table (0-63, not found => -1)\n        buffer = chars.indexOf(buffer);\n    }\n    return output;\n}\n// https://developer.mozilla.org/en/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_Unicode_Problem\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(Array.prototype.map.call(b64decode(str), (c) => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n}\n\nclass NbAuthToken {\n    constructor() {\n        this.payload = null;\n    }\n    getName() {\n        return this.constructor.NAME;\n    }\n    getPayload() {\n        return this.payload;\n    }\n}\nclass NbAuthTokenNotFoundError extends Error {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nclass NbAuthIllegalTokenError extends Error {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nclass NbAuthEmptyTokenError extends NbAuthIllegalTokenError {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nclass NbAuthIllegalJWTTokenError extends NbAuthIllegalTokenError {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, new.target.prototype);\n    }\n}\nfunction nbAuthCreateToken(tokenClass, token, ownerStrategyName, createdAt) {\n    return new tokenClass(token, ownerStrategyName, createdAt);\n}\nfunction decodeJwtPayload(payload) {\n    if (payload.length === 0) {\n        throw new NbAuthEmptyTokenError('Cannot extract from an empty payload.');\n    }\n    const parts = payload.split('.');\n    if (parts.length !== 3) {\n        throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and must consist of three parts.`);\n    }\n    let decoded;\n    try {\n        decoded = urlBase64Decode(parts[1]);\n    }\n    catch (e) {\n        throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be parsed.`);\n    }\n    if (!decoded) {\n        throw new NbAuthIllegalJWTTokenError(`The payload ${payload} is not valid JWT payload and cannot be decoded.`);\n    }\n    return JSON.parse(decoded);\n}\n/**\n * Wrapper for simple (text) token\n */\nclass NbAuthSimpleToken extends NbAuthToken {\n    static { this.NAME = 'nb:auth:simple:token'; }\n    constructor(token, ownerStrategyName, createdAt) {\n        super();\n        this.token = token;\n        this.ownerStrategyName = ownerStrategyName;\n        this.createdAt = createdAt;\n        try {\n            this.parsePayload();\n        }\n        catch (err) {\n            if (!(err instanceof NbAuthTokenNotFoundError)) {\n                // token is present but has got a problem, including illegal\n                throw err;\n            }\n        }\n        this.createdAt = this.prepareCreatedAt(createdAt);\n    }\n    parsePayload() {\n        this.payload = null;\n    }\n    prepareCreatedAt(date) {\n        return date ? date : new Date();\n    }\n    /**\n     * Returns the token's creation date\n     * @returns {Date}\n     */\n    getCreatedAt() {\n        return this.createdAt;\n    }\n    /**\n     * Returns the token value\n     * @returns string\n     */\n    getValue() {\n        return this.token;\n    }\n    getOwnerStrategyName() {\n        return this.ownerStrategyName;\n    }\n    /**\n     * Is non empty and valid\n     * @returns {boolean}\n     */\n    isValid() {\n        return !!this.getValue();\n    }\n    /**\n     * Validate value and convert to string, if value is not valid return empty string\n     * @returns {string}\n     */\n    toString() {\n        return !!this.token ? this.token : '';\n    }\n}\n/**\n * Wrapper for JWT token with additional methods.\n */\nclass NbAuthJWTToken extends NbAuthSimpleToken {\n    static { this.NAME = 'nb:auth:jwt:token'; }\n    /**\n     * for JWT token, the iat (issued at) field of the token payload contains the creation Date\n     */\n    prepareCreatedAt(date) {\n        const decoded = this.getPayload();\n        return decoded && decoded.iat ? new Date(Number(decoded.iat) * 1000) : super.prepareCreatedAt(date);\n    }\n    /**\n     * Returns payload object\n     * @returns any\n     */\n    parsePayload() {\n        if (!this.token) {\n            throw new NbAuthTokenNotFoundError('Token not found. ');\n        }\n        this.payload = decodeJwtPayload(this.token);\n    }\n    /**\n     * Returns expiration date\n     * @returns Date\n     */\n    getTokenExpDate() {\n        const decoded = this.getPayload();\n        if (decoded && !decoded.hasOwnProperty('exp')) {\n            return null;\n        }\n        const date = new Date(0);\n        date.setUTCSeconds(decoded.exp); // 'cause jwt token are set in seconds\n        return date;\n    }\n    /**\n     * Is data expired\n     * @returns {boolean}\n     */\n    isValid() {\n        return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n    }\n}\nconst prepareOAuth2Token = (data) => {\n    if (typeof data === 'string') {\n        try {\n            return JSON.parse(data);\n        }\n        catch (e) { }\n    }\n    return data;\n};\n/**\n * Wrapper for OAuth2 token whose access_token is a JWT Token\n */\nclass NbAuthOAuth2Token extends NbAuthSimpleToken {\n    static { this.NAME = 'nb:auth:oauth2:token'; }\n    constructor(data = {}, ownerStrategyName, createdAt) {\n        // we may get it as string when retrieving from a storage\n        super(prepareOAuth2Token(data), ownerStrategyName, createdAt);\n    }\n    /**\n     * Returns the token value\n     * @returns string\n     */\n    getValue() {\n        return this.token.access_token;\n    }\n    /**\n     * Returns the refresh token\n     * @returns string\n     */\n    getRefreshToken() {\n        return this.token.refresh_token;\n    }\n    /**\n     *  put refreshToken in the token payload\n      * @param refreshToken\n     */\n    setRefreshToken(refreshToken) {\n        this.token.refresh_token = refreshToken;\n    }\n    /**\n     * Parses token payload\n     * @returns any\n     */\n    parsePayload() {\n        if (!this.token) {\n            throw new NbAuthTokenNotFoundError('Token not found.');\n        }\n        else {\n            if (!Object.keys(this.token).length) {\n                throw new NbAuthEmptyTokenError('Cannot extract payload from an empty token.');\n            }\n        }\n        this.payload = this.token;\n    }\n    /**\n     * Returns the token type\n     * @returns string\n     */\n    getType() {\n        return this.token.token_type;\n    }\n    /**\n     * Is data expired\n     * @returns {boolean}\n     */\n    isValid() {\n        return super.isValid() && (!this.getTokenExpDate() || new Date() < this.getTokenExpDate());\n    }\n    /**\n     * Returns expiration date\n     * @returns Date\n     */\n    getTokenExpDate() {\n        if (!this.token.hasOwnProperty('expires_in')) {\n            return null;\n        }\n        return new Date(this.createdAt.getTime() + Number(this.token.expires_in) * 1000);\n    }\n    /**\n     * Convert to string\n     * @returns {string}\n     */\n    toString() {\n        return JSON.stringify(this.token);\n    }\n}\n/**\n * Wrapper for OAuth2 token embedding JWT tokens\n */\nclass NbAuthOAuth2JWTToken extends NbAuthOAuth2Token {\n    static { this.NAME = 'nb:auth:oauth2:jwt:token'; }\n    parsePayload() {\n        super.parsePayload();\n        this.parseAccessTokenPayload();\n    }\n    parseAccessTokenPayload() {\n        const accessToken = this.getValue();\n        if (!accessToken) {\n            throw new NbAuthTokenNotFoundError('access_token key not found.');\n        }\n        this.accessTokenPayload = decodeJwtPayload(accessToken);\n    }\n    /**\n     * Returns access token payload\n     * @returns any\n     */\n    getAccessTokenPayload() {\n        return this.accessTokenPayload;\n    }\n    /**\n     * for Oauth2 JWT token, the iat (issued at) field of the access_token payload\n     */\n    prepareCreatedAt(date) {\n        const payload = this.accessTokenPayload;\n        return payload && payload.iat ? new Date(Number(payload.iat) * 1000) : super.prepareCreatedAt(date);\n    }\n    /**\n     * Is token valid\n     * @returns {boolean}\n     */\n    isValid() {\n        return this.accessTokenPayload && super.isValid();\n    }\n    /**\n     * Returns expiration date :\n     * - exp if set,\n     * - super.getExpDate() otherwise\n     * @returns Date\n     */\n    getTokenExpDate() {\n        if (this.accessTokenPayload && this.accessTokenPayload.hasOwnProperty('exp')) {\n            const date = new Date(0);\n            date.setUTCSeconds(this.accessTokenPayload.exp);\n            return date;\n        }\n        else {\n            return super.getTokenExpDate();\n        }\n    }\n}\n\nconst NB_AUTH_FALLBACK_TOKEN = new InjectionToken('Nebular Auth Options');\n/**\n * Creates a token parcel which could be stored/restored\n */\nclass NbAuthTokenParceler {\n    constructor(fallbackClass, tokenClasses) {\n        this.fallbackClass = fallbackClass;\n        this.tokenClasses = tokenClasses;\n    }\n    wrap(token) {\n        return JSON.stringify({\n            name: token.getName(),\n            ownerStrategyName: token.getOwnerStrategyName(),\n            createdAt: token.getCreatedAt().getTime(),\n            value: token.toString(),\n        });\n    }\n    unwrap(value) {\n        let tokenClass = this.fallbackClass;\n        let tokenValue = '';\n        let tokenOwnerStrategyName = '';\n        let tokenCreatedAt = null;\n        const tokenPack = this.parseTokenPack(value);\n        if (tokenPack) {\n            tokenClass = this.getClassByName(tokenPack.name) || this.fallbackClass;\n            tokenValue = tokenPack.value;\n            tokenOwnerStrategyName = tokenPack.ownerStrategyName;\n            tokenCreatedAt = new Date(Number(tokenPack.createdAt));\n        }\n        return nbAuthCreateToken(tokenClass, tokenValue, tokenOwnerStrategyName, tokenCreatedAt);\n    }\n    // TODO: this could be moved to a separate token registry\n    getClassByName(name) {\n        return this.tokenClasses.find((tokenClass) => tokenClass.NAME === name);\n    }\n    parseTokenPack(value) {\n        try {\n            return JSON.parse(value);\n        }\n        catch (e) { }\n        return null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthTokenParceler, deps: [{ token: NB_AUTH_FALLBACK_TOKEN }, { token: NB_AUTH_TOKENS }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthTokenParceler }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthTokenParceler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_FALLBACK_TOKEN]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_TOKENS]\n                }] }] });\n\nclass NbTokenStorage {\n}\n/**\n * Service that uses browser localStorage as a storage.\n *\n * The token storage is provided into auth module the following way:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenLocalStorage },\n * ```\n *\n * If you need to change the storage behaviour or provide your own - just extend your class from basic `NbTokenStorage`\n * or `NbTokenLocalStorage` and provide in your `app.module`:\n * ```ts\n * { provide: NbTokenStorage, useClass: NbTokenCustomStorage },\n * ```\n *\n */\nclass NbTokenLocalStorage extends NbTokenStorage {\n    constructor(parceler) {\n        super();\n        this.parceler = parceler;\n        this.key = 'auth_app_token';\n    }\n    /**\n     * Returns token from localStorage\n     * @returns {NbAuthToken}\n     */\n    get() {\n        const raw = localStorage.getItem(this.key);\n        return this.parceler.unwrap(raw);\n    }\n    /**\n     * Sets token to localStorage\n     * @param {NbAuthToken} token\n     */\n    set(token) {\n        const raw = this.parceler.wrap(token);\n        localStorage.setItem(this.key, raw);\n    }\n    /**\n     * Clears token from localStorage\n     */\n    clear() {\n        localStorage.removeItem(this.key);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenLocalStorage, deps: [{ token: NbAuthTokenParceler }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenLocalStorage }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenLocalStorage, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NbAuthTokenParceler }] });\n\n/**\n * Service that allows you to manage authentication token - get, set, clear and also listen to token changes over time.\n */\nclass NbTokenService {\n    constructor(tokenStorage) {\n        this.tokenStorage = tokenStorage;\n        this.token$ = new BehaviorSubject(null);\n        this.publishStoredToken();\n    }\n    /**\n     * Publishes token when it changes.\n     * @returns {Observable<NbAuthToken>}\n     */\n    tokenChange() {\n        return this.token$\n            .pipe(filter(value => !!value), share());\n    }\n    /**\n     * Sets a token into the storage. This method is used by the NbAuthService automatically.\n     *\n     * @param {NbAuthToken} token\n     * @returns {Observable<any>}\n     */\n    set(token) {\n        this.tokenStorage.set(token);\n        this.publishStoredToken();\n        return of(null);\n    }\n    /**\n     * Returns observable of current token\n     * @returns {Observable<NbAuthToken>}\n     */\n    get() {\n        const token = this.tokenStorage.get();\n        return of(token);\n    }\n    /**\n     * Removes the token and published token value\n     *\n     * @returns {Observable<any>}\n     */\n    clear() {\n        this.tokenStorage.clear();\n        this.publishStoredToken();\n        return of(null);\n    }\n    publishStoredToken() {\n        this.token$.next(this.tokenStorage.get());\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenService, deps: [{ token: NbTokenStorage }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbTokenService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NbTokenStorage }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * Common authentication service.\n * Should be used to as an interlayer between UI Components and Auth Strategy.\n */\nclass NbAuthService {\n    constructor(tokenService, strategies) {\n        this.tokenService = tokenService;\n        this.strategies = strategies;\n    }\n    /**\n     * Retrieves current authenticated token stored\n     * @returns {Observable<any>}\n     */\n    getToken() {\n        return this.tokenService.get();\n    }\n    /**\n     * Returns true if auth token is present in the token storage\n     * @returns {Observable<boolean>}\n     */\n    isAuthenticated() {\n        return this.getToken()\n            .pipe(map((token) => token.isValid()));\n    }\n    /**\n     * Returns true if valid auth token is present in the token storage.\n     * If not, calls the strategy refreshToken, and returns isAuthenticated() if success, false otherwise\n     * @returns {Observable<boolean>}\n     */\n    isAuthenticatedOrRefresh() {\n        return this.getToken()\n            .pipe(switchMap(token => {\n            if (token.getValue() && !token.isValid()) {\n                return this.refreshToken(token.getOwnerStrategyName(), token)\n                    .pipe(switchMap(res => {\n                    if (res.isSuccess()) {\n                        return this.isAuthenticated();\n                    }\n                    else {\n                        return of(false);\n                    }\n                }));\n            }\n            else {\n                return of(token.isValid());\n            }\n        }));\n    }\n    /**\n     * Returns tokens stream\n     * @returns {Observable<NbAuthSimpleToken>}\n     */\n    onTokenChange() {\n        return this.tokenService.tokenChange();\n    }\n    /**\n     * Returns authentication status stream\n     * @returns {Observable<boolean>}\n     */\n    onAuthenticationChange() {\n        return this.onTokenChange()\n            .pipe(map((token) => token.isValid()));\n    }\n    /**\n     * Authenticates with the selected strategy\n     * Stores received token in the token storage\n     *\n     * Example:\n     * authenticate('email', {email: '<EMAIL>', password: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    authenticate(strategyName, data) {\n        return this.getStrategy(strategyName).authenticate(data)\n            .pipe(switchMap((result) => {\n            return this.processResultToken(result);\n        }));\n    }\n    /**\n     * Registers with the selected strategy\n     * Stores received token in the token storage\n     *\n     * Example:\n     * register('email', {email: '<EMAIL>', name: 'Some Name', password: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    register(strategyName, data) {\n        return this.getStrategy(strategyName).register(data)\n            .pipe(switchMap((result) => {\n            return this.processResultToken(result);\n        }));\n    }\n    /**\n     * Sign outs with the selected strategy\n     * Removes token from the token storage\n     *\n     * Example:\n     * logout('email')\n     *\n     * @param strategyName\n     * @returns {Observable<NbAuthResult>}\n     */\n    logout(strategyName) {\n        return this.getStrategy(strategyName).logout()\n            .pipe(switchMap((result) => {\n            if (result.isSuccess()) {\n                this.tokenService.clear()\n                    .pipe(map(() => result));\n            }\n            return of(result);\n        }));\n    }\n    /**\n     * Sends forgot password request to the selected strategy\n     *\n     * Example:\n     * requestPassword('email', {email: '<EMAIL>'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    requestPassword(strategyName, data) {\n        return this.getStrategy(strategyName).requestPassword(data);\n    }\n    /**\n     * Tries to reset password with the selected strategy\n     *\n     * Example:\n     * resetPassword('email', {newPassword: 'test'})\n     *\n     * @param strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    resetPassword(strategyName, data) {\n        return this.getStrategy(strategyName).resetPassword(data);\n    }\n    /**\n     * Sends a refresh token request\n     * Stores received token in the token storage\n     *\n     * Example:\n     * refreshToken('email', {token: token})\n     *\n     * @param {string} strategyName\n     * @param data\n     * @returns {Observable<NbAuthResult>}\n     */\n    refreshToken(strategyName, data) {\n        return this.getStrategy(strategyName).refreshToken(data)\n            .pipe(switchMap((result) => {\n            return this.processResultToken(result);\n        }));\n    }\n    /**\n     * Get registered strategy by name\n     *\n     * Example:\n     * getStrategy('email')\n     *\n     * @param {string} provider\n     * @returns {NbAbstractAuthProvider}\n     */\n    getStrategy(strategyName) {\n        const found = this.strategies.find((strategy) => strategy.getName() === strategyName);\n        if (!found) {\n            throw new TypeError(`There is no Auth Strategy registered under '${strategyName}' name`);\n        }\n        return found;\n    }\n    processResultToken(result) {\n        if (result.isSuccess() && result.getToken()) {\n            return this.tokenService.set(result.getToken())\n                .pipe(map((token) => {\n                return result;\n            }));\n        }\n        return of(result);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthService, deps: [{ token: NbTokenService }, { token: NB_AUTH_STRATEGIES }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NbTokenService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_STRATEGIES]\n                }] }] });\n\nclass NbAuthStrategy {\n    // we should keep this any and validation should be done in `register` method instead\n    // otherwise it won't be possible to pass an empty object\n    setOptions(options) {\n        this.options = deepExtend({}, this.defaultOptions, options);\n    }\n    getOption(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    createToken(value, failWhenInvalidToken) {\n        const token = nbAuthCreateToken(this.getOption('token.class'), value, this.getName());\n        // At this point, nbAuthCreateToken failed with NbAuthIllegalTokenError which MUST be intercepted by strategies\n        // Or token is created. It MAY be created even if backend did not return any token, in this case it is !Valid\n        if (failWhenInvalidToken && !token.isValid()) {\n            // If we require a valid token (i.e. isValid), then we MUST throw NbAuthIllegalTokenError so that the strategies\n            // intercept it\n            throw new NbAuthIllegalTokenError('Token is empty or invalid.');\n        }\n        return token;\n    }\n    getName() {\n        return this.getOption('name');\n    }\n    createFailResponse(data) {\n        return new HttpResponse({ body: {}, status: 401 });\n    }\n    createSuccessResponse(data) {\n        return new HttpResponse({ body: {}, status: 200 });\n    }\n    getActionEndpoint(action) {\n        const actionEndpoint = this.getOption(`${action}.endpoint`);\n        const baseEndpoint = this.getOption('baseEndpoint');\n        return actionEndpoint ? baseEndpoint + actionEndpoint : '';\n    }\n    getHeaders() {\n        const customHeaders = this.getOption('headers') ?? {};\n        if (customHeaders instanceof HttpHeaders) {\n            return customHeaders;\n        }\n        let headers = new HttpHeaders();\n        Object.entries(customHeaders).forEach(([key, value]) => {\n            headers = headers.append(key, value);\n        });\n        return headers;\n    }\n}\n\nclass NbAuthResult {\n    // TODO: better pass object\n    constructor(success, response, redirect, errors, messages, token = null) {\n        this.success = success;\n        this.response = response;\n        this.redirect = redirect;\n        this.errors = [];\n        this.messages = [];\n        this.errors = this.errors.concat([errors]);\n        if (errors instanceof Array) {\n            this.errors = errors;\n        }\n        this.messages = this.messages.concat([messages]);\n        if (messages instanceof Array) {\n            this.messages = messages;\n        }\n        this.token = token;\n    }\n    getResponse() {\n        return this.response;\n    }\n    getToken() {\n        return this.token;\n    }\n    getRedirect() {\n        return this.redirect;\n    }\n    getErrors() {\n        return this.errors.filter(val => !!val);\n    }\n    getMessages() {\n        return this.messages.filter(val => !!val);\n    }\n    isSuccess() {\n        return this.success;\n    }\n    isFailure() {\n        return !this.success;\n    }\n}\n\nclass NbAuthStrategyOptions {\n}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n    constructor() {\n        super(...arguments);\n        this.token = {\n            class: NbAuthSimpleToken,\n        };\n        this.delay = 1000;\n        this.alwaysFail = false;\n    }\n}\nconst dummyStrategyOptions = new NbDummyAuthStrategyOptions();\n\n/**\n * Dummy auth strategy. Could be useful for auth setup when backend is not available yet.\n *\n *\n * Strategy settings.\n *\n * ```ts\n * export class NbDummyAuthStrategyOptions extends NbAuthStrategyOptions {\n *   name = 'dummy';\n *   token = {\n *     class: NbAuthSimpleToken,\n *   };\n *   delay? = 1000;\n *   alwaysFail? = false;\n * }\n * ```\n */\nclass NbDummyAuthStrategy extends NbAuthStrategy {\n    constructor() {\n        super(...arguments);\n        this.defaultOptions = dummyStrategyOptions;\n    }\n    static setup(options) {\n        return [NbDummyAuthStrategy, options];\n    }\n    authenticate(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    register(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    requestPassword(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    resetPassword(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    logout(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    refreshToken(data) {\n        return of(this.createDummyResult(data)).pipe(delay(this.getOption('delay')));\n    }\n    createDummyResult(data) {\n        if (this.getOption('alwaysFail')) {\n            return new NbAuthResult(false, this.createFailResponse(data), null, ['Something went wrong.']);\n        }\n        try {\n            const token = this.createToken('test token', true);\n            return new NbAuthResult(true, this.createSuccessResponse(data), '/', [], ['Successfully logged in.'], token);\n        }\n        catch (err) {\n            return new NbAuthResult(false, this.createFailResponse(data), null, [err.message]);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbDummyAuthStrategy, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbDummyAuthStrategy }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbDummyAuthStrategy, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nvar NbOAuth2ResponseType;\n(function (NbOAuth2ResponseType) {\n    NbOAuth2ResponseType[\"CODE\"] = \"code\";\n    NbOAuth2ResponseType[\"TOKEN\"] = \"token\";\n})(NbOAuth2ResponseType || (NbOAuth2ResponseType = {}));\n// TODO: client_credentials\nvar NbOAuth2GrantType;\n(function (NbOAuth2GrantType) {\n    NbOAuth2GrantType[\"AUTHORIZATION_CODE\"] = \"authorization_code\";\n    NbOAuth2GrantType[\"PASSWORD\"] = \"password\";\n    NbOAuth2GrantType[\"REFRESH_TOKEN\"] = \"refresh_token\";\n})(NbOAuth2GrantType || (NbOAuth2GrantType = {}));\nvar NbOAuth2ClientAuthMethod;\n(function (NbOAuth2ClientAuthMethod) {\n    NbOAuth2ClientAuthMethod[\"NONE\"] = \"none\";\n    NbOAuth2ClientAuthMethod[\"BASIC\"] = \"basic\";\n    NbOAuth2ClientAuthMethod[\"REQUEST_BODY\"] = \"request-body\";\n})(NbOAuth2ClientAuthMethod || (NbOAuth2ClientAuthMethod = {}));\nclass NbOAuth2AuthStrategyOptions extends NbAuthStrategyOptions {\n    constructor() {\n        super(...arguments);\n        this.baseEndpoint = '';\n        this.clientId = '';\n        this.clientSecret = '';\n        this.clientAuthMethod = NbOAuth2ClientAuthMethod.NONE;\n        this.redirect = {\n            success: '/',\n            failure: null,\n        };\n        this.defaultErrors = ['Something went wrong, please try again.'];\n        this.defaultMessages = ['You have been successfully authenticated.'];\n        this.authorize = {\n            endpoint: 'authorize',\n            responseType: NbOAuth2ResponseType.CODE,\n            requireValidToken: true,\n        };\n        this.token = {\n            endpoint: 'token',\n            grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n            requireValidToken: true,\n            class: NbAuthOAuth2Token,\n        };\n        this.refresh = {\n            endpoint: 'token',\n            grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n            requireValidToken: true,\n        };\n    }\n}\nconst auth2StrategyOptions = new NbOAuth2AuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * OAuth2 authentication strategy.\n *\n * Strategy settings:\n *\n * ```ts\n * export enum NbOAuth2ResponseType {\n *   CODE = 'code',\n *   TOKEN = 'token',\n * }\n *\n * export enum NbOAuth2GrantType {\n *   AUTHORIZATION_CODE = 'authorization_code',\n *   PASSWORD = 'password',\n *   REFRESH_TOKEN = 'refresh_token',\n * }\n *\n * export class NbOAuth2AuthStrategyOptions {\n *   name: string;\n *   baseEndpoint?: string = '';\n *   clientId: string = '';\n *   clientSecret: string = '';\n *   clientAuthMethod: string = NbOAuth2ClientAuthMethod.NONE;\n *   redirect?: { success?: string; failure?: string } = {\n *     success: '/',\n *     failure: null,\n *   };\n *   defaultErrors?: any[] = ['Something went wrong, please try again.'];\n *   defaultMessages?: any[] = ['You have been successfully authenticated.'];\n *   authorize?: {\n *     endpoint?: string;\n *     redirectUri?: string;\n *     responseType?: string;\n *     requireValidToken: true,\n *     scope?: string;\n *     state?: string;\n *     params?: { [key: string]: string };\n *   } = {\n *     endpoint: 'authorize',\n *     responseType: NbOAuth2ResponseType.CODE,\n *   };\n *   token?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     requireValidToken: true,\n *     redirectUri?: string;\n *     scope?: string;\n *     class: NbAuthTokenClass,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.AUTHORIZATION_CODE,\n *     class: NbAuthOAuth2Token,\n *   };\n *   refresh?: {\n *     endpoint?: string;\n *     grantType?: string;\n *     scope?: string;\n *     requireValidToken: true,\n *   } = {\n *     endpoint: 'token',\n *     grantType: NbOAuth2GrantType.REFRESH_TOKEN,\n *   };\n * }\n * ```\n *\n */\nclass NbOAuth2AuthStrategy extends NbAuthStrategy {\n    static setup(options) {\n        return [NbOAuth2AuthStrategy, options];\n    }\n    get responseType() {\n        return this.getOption('authorize.responseType');\n    }\n    get clientAuthMethod() {\n        return this.getOption('clientAuthMethod');\n    }\n    constructor(http, route, window) {\n        super();\n        this.http = http;\n        this.route = route;\n        this.window = window;\n        this.redirectResultHandlers = {\n            [NbOAuth2ResponseType.CODE]: () => {\n                return of(this.route.snapshot.queryParams).pipe(switchMap((params) => {\n                    if (params.code) {\n                        return this.requestToken(params.code);\n                    }\n                    return of(new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []));\n                }));\n            },\n            [NbOAuth2ResponseType.TOKEN]: () => {\n                const module = 'authorize';\n                const requireValidToken = this.getOption(`${module}.requireValidToken`);\n                return of(this.route.snapshot.fragment).pipe(map((fragment) => this.parseHashAsQueryParams(fragment)), map((params) => {\n                    if (!params.error) {\n                        return new NbAuthResult(true, params, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(params, requireValidToken));\n                    }\n                    return new NbAuthResult(false, params, this.getOption('redirect.failure'), this.getOption('defaultErrors'), []);\n                }), catchError((err) => {\n                    const errors = [];\n                    if (err instanceof NbAuthIllegalTokenError) {\n                        errors.push(err.message);\n                    }\n                    else {\n                        errors.push('Something went wrong.');\n                    }\n                    return of(new NbAuthResult(false, err, this.getOption('redirect.failure'), errors));\n                }));\n            },\n        };\n        this.redirectResults = {\n            [NbOAuth2ResponseType.CODE]: () => {\n                return of(this.route.snapshot.queryParams).pipe(map((params) => !!(params && (params.code || params.error))));\n            },\n            [NbOAuth2ResponseType.TOKEN]: () => {\n                return of(this.route.snapshot.fragment).pipe(map((fragment) => this.parseHashAsQueryParams(fragment)), map((params) => !!(params && (params.access_token || params.error))));\n            },\n        };\n        this.defaultOptions = auth2StrategyOptions;\n    }\n    authenticate(data) {\n        if (this.getOption('token.grantType') === NbOAuth2GrantType.PASSWORD) {\n            return this.passwordToken(data.email, data.password);\n        }\n        else {\n            return this.isRedirectResult().pipe(switchMap((result) => {\n                if (!result) {\n                    this.authorizeRedirect();\n                    return of(new NbAuthResult(true));\n                }\n                return this.getAuthorizationResult();\n            }));\n        }\n    }\n    getAuthorizationResult() {\n        const redirectResultHandler = this.redirectResultHandlers[this.responseType];\n        if (redirectResultHandler) {\n            return redirectResultHandler.call(this);\n        }\n        throw new Error(`'${this.responseType}' responseType is not supported,\n                      only 'token' and 'code' are supported now`);\n    }\n    refreshToken(token) {\n        const module = 'refresh';\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.post(url, this.buildRefreshRequestData(token), { headers: this.getHeaders() }).pipe(map((res) => {\n            return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createRefreshedToken(res, token, requireValidToken));\n        }), catchError((res) => this.handleResponseError(res)));\n    }\n    passwordToken(username, password) {\n        const module = 'token';\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.post(url, this.buildPasswordRequestData(username, password), { headers: this.getHeaders() }).pipe(map((res) => {\n            return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n        }), catchError((res) => this.handleResponseError(res)));\n    }\n    authorizeRedirect() {\n        this.window.location.href = this.buildRedirectUrl();\n    }\n    isRedirectResult() {\n        return this.redirectResults[this.responseType].call(this);\n    }\n    requestToken(code) {\n        const module = 'token';\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.post(url, this.buildCodeRequestData(code), { headers: this.getHeaders() }).pipe(map((res) => {\n            return new NbAuthResult(true, res, this.getOption('redirect.success'), [], this.getOption('defaultMessages'), this.createToken(res, requireValidToken));\n        }), catchError((res) => this.handleResponseError(res)));\n    }\n    buildCodeRequestData(code) {\n        const params = {\n            grant_type: this.getOption('token.grantType'),\n            code: code,\n            redirect_uri: this.getOption('token.redirectUri'),\n            client_id: this.getOption('clientId'),\n        };\n        return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildRefreshRequestData(token) {\n        const params = {\n            grant_type: this.getOption('refresh.grantType'),\n            refresh_token: token.getRefreshToken(),\n            scope: this.getOption('refresh.scope'),\n            client_id: this.getOption('clientId'),\n        };\n        return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildPasswordRequestData(username, password) {\n        const params = {\n            grant_type: this.getOption('token.grantType'),\n            username: username,\n            password: password,\n            scope: this.getOption('token.scope'),\n        };\n        return this.urlEncodeParameters(this.cleanParams(this.addCredentialsToParams(params)));\n    }\n    buildAuthHeader() {\n        if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.BASIC) {\n            if (this.getOption('clientId') && this.getOption('clientSecret')) {\n                return new HttpHeaders({\n                    Authorization: 'Basic ' + btoa(this.getOption('clientId') + ':' + this.getOption('clientSecret')),\n                });\n            }\n            else {\n                throw Error('For basic client authentication method, please provide both clientId & clientSecret.');\n            }\n        }\n        return undefined;\n    }\n    getHeaders() {\n        let headers = super.getHeaders();\n        headers = headers.append('Content-Type', 'application/x-www-form-urlencoded');\n        const authHeaders = this.buildAuthHeader();\n        if (authHeaders === undefined) {\n            return headers;\n        }\n        for (const headerKey of authHeaders.keys()) {\n            for (const headerValue of authHeaders.getAll(headerKey)) {\n                headers = headers.append(headerKey, headerValue);\n            }\n        }\n        return headers;\n    }\n    cleanParams(params) {\n        Object.entries(params).forEach(([key, val]) => !val && delete params[key]);\n        return params;\n    }\n    addCredentialsToParams(params) {\n        if (this.clientAuthMethod === NbOAuth2ClientAuthMethod.REQUEST_BODY) {\n            if (this.getOption('clientId') && this.getOption('clientSecret')) {\n                return {\n                    ...params,\n                    client_id: this.getOption('clientId'),\n                    client_secret: this.getOption('clientSecret'),\n                };\n            }\n            else {\n                throw Error('For request body client authentication method, please provide both clientId & clientSecret.');\n            }\n        }\n        return params;\n    }\n    handleResponseError(res) {\n        let errors = [];\n        if (res instanceof HttpErrorResponse) {\n            if (res.error.error_description) {\n                errors.push(res.error.error_description);\n            }\n            else {\n                errors = this.getOption('defaultErrors');\n            }\n        }\n        else if (res instanceof NbAuthIllegalTokenError) {\n            errors.push(res.message);\n        }\n        else {\n            errors.push('Something went wrong.');\n        }\n        return of(new NbAuthResult(false, res, this.getOption('redirect.failure'), errors, []));\n    }\n    buildRedirectUrl() {\n        const params = {\n            response_type: this.getOption('authorize.responseType'),\n            client_id: this.getOption('clientId'),\n            redirect_uri: this.getOption('authorize.redirectUri'),\n            scope: this.getOption('authorize.scope'),\n            state: this.getOption('authorize.state'),\n            ...this.getOption('authorize.params'),\n        };\n        const endpoint = this.getActionEndpoint('authorize');\n        const query = this.urlEncodeParameters(this.cleanParams(params));\n        return `${endpoint}?${query}`;\n    }\n    parseHashAsQueryParams(hash) {\n        return hash\n            ? hash.split('&').reduce((acc, part) => {\n                const item = part.split('=');\n                acc[item[0]] = decodeURIComponent(item[1]);\n                return acc;\n            }, {})\n            : {};\n    }\n    urlEncodeParameters(params) {\n        return Object.keys(params)\n            .map((k) => {\n            return `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`;\n        })\n            .join('&');\n    }\n    createRefreshedToken(res, existingToken, requireValidToken) {\n        const refreshedToken = this.createToken(res, requireValidToken);\n        if (!refreshedToken.getRefreshToken() && existingToken.getRefreshToken()) {\n            refreshedToken.setRefreshToken(existingToken.getRefreshToken());\n        }\n        return refreshedToken;\n    }\n    register(data) {\n        throw new Error('`register` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    requestPassword(data) {\n        throw new Error('`requestPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    resetPassword(data = {}) {\n        throw new Error('`resetPassword` is not supported by `NbOAuth2AuthStrategy`, use `authenticate`.');\n    }\n    logout() {\n        return of(new NbAuthResult(true));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbOAuth2AuthStrategy, deps: [{ token: i1.HttpClient }, { token: i2.ActivatedRoute }, { token: NB_WINDOW }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbOAuth2AuthStrategy }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbOAuth2AuthStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i1.HttpClient }, { type: i2.ActivatedRoute }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_WINDOW]\n                }] }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n    constructor() {\n        super(...arguments);\n        this.baseEndpoint = '/api/auth/';\n        this.login = {\n            alwaysFail: false,\n            endpoint: 'login',\n            method: 'post',\n            requireValidToken: true,\n            redirect: {\n                success: '/',\n                failure: null,\n            },\n            defaultErrors: ['Login/Email combination is not correct, please try again.'],\n            defaultMessages: ['You have been successfully logged in.'],\n        };\n        this.register = {\n            alwaysFail: false,\n            endpoint: 'register',\n            method: 'post',\n            requireValidToken: true,\n            redirect: {\n                success: '/',\n                failure: null,\n            },\n            defaultErrors: ['Something went wrong, please try again.'],\n            defaultMessages: ['You have been successfully registered.'],\n        };\n        this.requestPass = {\n            endpoint: 'request-pass',\n            method: 'post',\n            redirect: {\n                success: '/',\n                failure: null,\n            },\n            defaultErrors: ['Something went wrong, please try again.'],\n            defaultMessages: ['Reset password instructions have been sent to your email.'],\n        };\n        this.resetPass = {\n            endpoint: 'reset-pass',\n            method: 'put',\n            redirect: {\n                success: '/',\n                failure: null,\n            },\n            resetPasswordTokenKey: 'reset_password_token',\n            defaultErrors: ['Something went wrong, please try again.'],\n            defaultMessages: ['Your password has been successfully changed.'],\n        };\n        this.logout = {\n            alwaysFail: false,\n            endpoint: 'logout',\n            method: 'delete',\n            redirect: {\n                success: '/',\n                failure: null,\n            },\n            defaultErrors: ['Something went wrong, please try again.'],\n            defaultMessages: ['You have been successfully logged out.'],\n        };\n        this.refreshToken = {\n            endpoint: 'refresh-token',\n            method: 'post',\n            requireValidToken: true,\n            redirect: {\n                success: null,\n                failure: null,\n            },\n            defaultErrors: ['Something went wrong, please try again.'],\n            defaultMessages: ['Your token has been successfully refreshed.'],\n        };\n        this.token = {\n            class: NbAuthSimpleToken,\n            key: 'data.token',\n            getter: (module, res, options) => getDeepFromObject(res.body, options.token.key),\n        };\n        this.errors = {\n            key: 'data.errors',\n            getter: (module, res, options) => getDeepFromObject(res.error, options.errors.key, options[module].defaultErrors),\n        };\n        this.messages = {\n            key: 'data.messages',\n            getter: (module, res, options) => getDeepFromObject(res.body, options.messages.key, options[module].defaultMessages),\n        };\n    }\n}\nconst passwordStrategyOptions = new NbPasswordAuthStrategyOptions();\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n/**\n * The most common authentication provider for email/password strategy.\n *\n * Strategy settings. Note, there is no need to copy over the whole object to change the settings you need.\n * Also, this.getOption call won't work outside of the default options declaration\n * (which is inside of the `NbPasswordAuthStrategy` class), so you have to replace it with a custom helper function\n * if you need it.\n *\n * ```ts\n *export class NbPasswordAuthStrategyOptions extends NbAuthStrategyOptions {\n *  name: string;\n *  baseEndpoint? = '/api/auth/';\n *  login?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'login',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Login/Email combination is not correct, please try again.'],\n *    defaultMessages: ['You have been successfully logged in.'],\n *  };\n *  register?: boolean | NbPasswordStrategyModule = {\n *    alwaysFail: false,\n *    endpoint: 'register',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully registered.'],\n *  };\n *  requestPass?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'request-pass',\n *    method: 'post',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Reset password instructions have been sent to your email.'],\n *  };\n *  resetPass?: boolean | NbPasswordStrategyReset = {\n *    endpoint: 'reset-pass',\n *    method: 'put',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    resetPasswordTokenKey: 'reset_password_token',\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your password has been successfully changed.'],\n *  };\n *  logout?: boolean | NbPasswordStrategyReset = {\n *    alwaysFail: false,\n *    endpoint: 'logout',\n *    method: 'delete',\n *    redirect: {\n *      success: '/',\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['You have been successfully logged out.'],\n *  };\n *  refreshToken?: boolean | NbPasswordStrategyModule = {\n *    endpoint: 'refresh-token',\n *    method: 'post',\n *    requireValidToken: true,\n *    redirect: {\n *      success: null,\n *      failure: null,\n *    },\n *    defaultErrors: ['Something went wrong, please try again.'],\n *    defaultMessages: ['Your token has been successfully refreshed.'],\n *  };\n *  token?: NbPasswordStrategyToken = {\n *    class: NbAuthSimpleToken,\n *    key: 'data.token',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.token.key,\n *    ),\n *  };\n *  errors?: NbPasswordStrategyMessage = {\n *    key: 'data.errors',\n *    getter: (module: string, res: HttpErrorResponse, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.error,\n *      options.errors.key,\n *      options[module].defaultErrors,\n *    ),\n *  };\n *  messages?: NbPasswordStrategyMessage = {\n *    key: 'data.messages',\n *    getter: (module: string, res: HttpResponse<Object>, options: NbPasswordAuthStrategyOptions) => getDeepFromObject(\n *      res.body,\n *      options.messages.key,\n *      options[module].defaultMessages,\n *    ),\n *  };\n *  validation?: {\n *    password?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *    email?: {\n *      required?: boolean;\n *      regexp?: string | null;\n *    };\n *    fullName?: {\n *      required?: boolean;\n *      minLength?: number | null;\n *      maxLength?: number | null;\n *      regexp?: string | null;\n *    };\n *  };\n *}\n * ```\n */\nclass NbPasswordAuthStrategy extends NbAuthStrategy {\n    static setup(options) {\n        return [NbPasswordAuthStrategy, options];\n    }\n    constructor(http, route) {\n        super();\n        this.http = http;\n        this.route = route;\n        this.defaultOptions = passwordStrategyOptions;\n    }\n    authenticate(data) {\n        const module = 'login';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.request(method, url, { body: data, observe: 'response', headers: this.getHeaders() }).pipe(map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse(data);\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    register(data) {\n        const module = 'register';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.request(method, url, { body: data, observe: 'response', headers: this.getHeaders() }).pipe(map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse(data);\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')('login', res, this.options), requireValidToken));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    requestPassword(data) {\n        const module = 'requestPass';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        return this.http.request(method, url, { body: data, observe: 'response', headers: this.getHeaders() }).pipe(map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse();\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    resetPassword(data = {}) {\n        const module = 'resetPass';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        const tokenKey = this.getOption(`${module}.resetPasswordTokenKey`);\n        data[tokenKey] = this.route.snapshot.queryParams[tokenKey];\n        return this.http.request(method, url, { body: data, observe: 'response', headers: this.getHeaders() }).pipe(map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse();\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    logout() {\n        const module = 'logout';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        return of({}).pipe(switchMap((res) => {\n            if (!url) {\n                return of(res);\n            }\n            return this.http.request(method, url, { observe: 'response', headers: this.getHeaders() });\n        }), map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse();\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    refreshToken(data) {\n        const module = 'refreshToken';\n        const method = this.getOption(`${module}.method`);\n        const url = this.getActionEndpoint(module);\n        const requireValidToken = this.getOption(`${module}.requireValidToken`);\n        return this.http.request(method, url, { body: data, observe: 'response', headers: this.getHeaders() }).pipe(map((res) => {\n            if (this.getOption(`${module}.alwaysFail`)) {\n                throw this.createFailResponse(data);\n            }\n            return res;\n        }), map((res) => {\n            return new NbAuthResult(true, res, this.getOption(`${module}.redirect.success`), [], this.getOption('messages.getter')(module, res, this.options), this.createToken(this.getOption('token.getter')(module, res, this.options), requireValidToken));\n        }), catchError((res) => {\n            return this.handleResponseError(res, module);\n        }));\n    }\n    handleResponseError(res, module) {\n        let errors = [];\n        if (res instanceof HttpErrorResponse) {\n            errors = this.getOption('errors.getter')(module, res, this.options);\n        }\n        else if (res instanceof NbAuthIllegalTokenError) {\n            errors.push(res.message);\n        }\n        else {\n            errors.push('Something went wrong.');\n        }\n        return of(new NbAuthResult(false, res, this.getOption(`${module}.redirect.failure`), errors));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbPasswordAuthStrategy, deps: [{ token: i1.HttpClient }, { token: i2.ActivatedRoute }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbPasswordAuthStrategy }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbPasswordAuthStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i1.HttpClient }, { type: i2.ActivatedRoute }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbAuthBlockComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthBlockComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbAuthBlockComponent, selector: \"nb-auth-block\", ngImport: i0, template: `\n    <ng-content></ng-content>\n  `, isInline: true, styles: [\":host{display:block;width:100%;max-width:35rem}:host ::ng-deep form{width:100%}:host ::ng-deep .label{display:block;margin-bottom:.5rem}:host ::ng-deep .forgot-password{text-decoration:none;margin-bottom:.5rem}:host ::ng-deep .caption{margin-top:.5rem}:host ::ng-deep .alert{text-align:center}:host ::ng-deep .title{margin-top:0;margin-bottom:.75rem;text-align:center}:host ::ng-deep .sub-title{margin-bottom:2rem;text-align:center}:host ::ng-deep .form-control-group{margin-bottom:2rem}:host ::ng-deep .form-control-group.accept-group{display:flex;justify-content:space-between;margin:2rem 0}:host ::ng-deep .label-with-link{display:flex;justify-content:space-between}:host ::ng-deep .links{text-align:center;margin-top:1.75rem}:host ::ng-deep .links .socials{margin-top:1.5rem}:host ::ng-deep .links .socials a{margin:0 1rem;text-decoration:none;vertical-align:middle}:host ::ng-deep .links .socials a.with-icon{font-size:2rem}:host ::ng-deep .another-action{margin-top:2rem;text-align:center}:host ::ng-deep .sign-in-or-up{margin-top:2rem;display:flex;justify-content:space-between}:host ::ng-deep nb-alert .alert-title,:host ::ng-deep nb-alert .alert-message{margin:0 0 .5rem}:host ::ng-deep nb-alert .alert-message-list{list-style-type:none;padding:0;margin:0}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthBlockComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-auth-block', template: `\n    <ng-content></ng-content>\n  `, styles: [\":host{display:block;width:100%;max-width:35rem}:host ::ng-deep form{width:100%}:host ::ng-deep .label{display:block;margin-bottom:.5rem}:host ::ng-deep .forgot-password{text-decoration:none;margin-bottom:.5rem}:host ::ng-deep .caption{margin-top:.5rem}:host ::ng-deep .alert{text-align:center}:host ::ng-deep .title{margin-top:0;margin-bottom:.75rem;text-align:center}:host ::ng-deep .sub-title{margin-bottom:2rem;text-align:center}:host ::ng-deep .form-control-group{margin-bottom:2rem}:host ::ng-deep .form-control-group.accept-group{display:flex;justify-content:space-between;margin:2rem 0}:host ::ng-deep .label-with-link{display:flex;justify-content:space-between}:host ::ng-deep .links{text-align:center;margin-top:1.75rem}:host ::ng-deep .links .socials{margin-top:1.5rem}:host ::ng-deep .links .socials a{margin:0 1rem;text-decoration:none;vertical-align:middle}:host ::ng-deep .links .socials a.with-icon{font-size:2rem}:host ::ng-deep .another-action{margin-top:2rem;text-align:center}:host ::ng-deep .sign-in-or-up{margin-top:2rem;display:flex;justify-content:space-between}:host ::ng-deep nb-alert .alert-title,:host ::ng-deep nb-alert .alert-message{margin:0 0 .5rem}:host ::ng-deep nb-alert .alert-message-list{list-style-type:none;padding:0;margin:0}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }]\n        }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbAuthComponent {\n    // showcase of how to use the onAuthenticationChange method\n    constructor(auth, location) {\n        this.auth = auth;\n        this.location = location;\n        this.destroy$ = new Subject();\n        this.authenticated = false;\n        this.token = '';\n        this.subscription = auth.onAuthenticationChange()\n            .pipe(takeUntil(this.destroy$))\n            .subscribe((authenticated) => {\n            this.authenticated = authenticated;\n        });\n    }\n    back() {\n        this.location.back();\n        return false;\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthComponent, deps: [{ token: NbAuthService }, { token: i3.Location }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbAuthComponent, selector: \"nb-auth\", ngImport: i0, template: `\n    <nb-layout>\n      <nb-layout-column>\n        <nb-card>\n          <nb-card-header>\n            <nav class=\"navigation\">\n              <a href=\"#\" (click)=\"back()\" class=\"link back-link\" aria-label=\"Back\">\n                <nb-icon icon=\"arrow-back\"></nb-icon>\n              </a>\n            </nav>\n          </nb-card-header>\n          <nb-card-body>\n            <nb-auth-block>\n              <router-outlet></router-outlet>\n            </nb-auth-block>\n          </nb-card-body>\n        </nb-card>\n      </nb-layout-column>\n    </nb-layout>\n  `, isInline: true, styles: [\".visually-hidden{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:#00000052}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}.nb-global-scrollblock{position:static;width:auto;overflow:hidden}html{box-sizing:border-box}*,*:before,*:after{box-sizing:inherit}html,body{margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:.35em .75em .625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}:host nb-card{margin:0;height:calc(100vh - 5rem)}:host .navigation .link{display:inline-block;text-decoration:none}:host .navigation .link nb-icon{font-size:2rem;vertical-align:middle}:host .links nb-icon{font-size:2.5rem}:host nb-card-body{display:flex;width:100%}:host nb-auth-block{margin:auto}@media (max-width: 767.98px){:host nb-card{border-radius:0;height:100vh}}:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:2.5rem}@media (max-width: 767.98px){:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:0}}\\n/*\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/*!\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"], dependencies: [{ kind: \"component\", type: i4.NbLayoutComponent, selector: \"nb-layout\", inputs: [\"center\", \"windowMode\", \"withScroll\", \"restoreScrollTop\"] }, { kind: \"component\", type: i4.NbLayoutColumnComponent, selector: \"nb-layout-column\", inputs: [\"left\", \"start\"] }, { kind: \"component\", type: i4.NbCardComponent, selector: \"nb-card\", inputs: [\"size\", \"status\", \"accent\"] }, { kind: \"component\", type: i4.NbCardBodyComponent, selector: \"nb-card-body\" }, { kind: \"component\", type: i4.NbCardHeaderComponent, selector: \"nb-card-header\" }, { kind: \"directive\", type: i2.RouterOutlet, selector: \"router-outlet\", inputs: [\"name\"], outputs: [\"activate\", \"deactivate\", \"attach\", \"detach\"], exportAs: [\"outlet\"] }, { kind: \"component\", type: i4.NbIconComponent, selector: \"nb-icon\", inputs: [\"icon\", \"pack\", \"options\", \"status\", \"config\"] }, { kind: \"component\", type: NbAuthBlockComponent, selector: \"nb-auth-block\" }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-auth', template: `\n    <nb-layout>\n      <nb-layout-column>\n        <nb-card>\n          <nb-card-header>\n            <nav class=\"navigation\">\n              <a href=\"#\" (click)=\"back()\" class=\"link back-link\" aria-label=\"Back\">\n                <nb-icon icon=\"arrow-back\"></nb-icon>\n              </a>\n            </nav>\n          </nb-card-header>\n          <nb-card-body>\n            <nb-auth-block>\n              <router-outlet></router-outlet>\n            </nb-auth-block>\n          </nb-card-body>\n        </nb-card>\n      </nb-layout-column>\n    </nb-layout>\n  `, styles: [\".visually-hidden{position:absolute!important;height:1px;width:1px;overflow:hidden;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px)}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:#00000052}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}.nb-global-scrollblock{position:static;width:auto;overflow:hidden}html{box-sizing:border-box}*,*:before,*:after{box-sizing:inherit}html,body{margin:0;padding:0}html{line-height:1.15;-webkit-text-size-adjust:100%}body{margin:0}h1{font-size:2em;margin:.67em 0}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}img{border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}button,input{overflow:visible}button,select{text-transform:none}button,[type=button],[type=reset],[type=submit]{-webkit-appearance:button}button::-moz-focus-inner,[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring{outline:1px dotted ButtonText}fieldset{padding:.35em .75em .625em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{vertical-align:baseline}textarea{overflow:auto}[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details{display:block}summary{display:list-item}template{display:none}[hidden]{display:none}:host nb-card{margin:0;height:calc(100vh - 5rem)}:host .navigation .link{display:inline-block;text-decoration:none}:host .navigation .link nb-icon{font-size:2rem;vertical-align:middle}:host .links nb-icon{font-size:2.5rem}:host nb-card-body{display:flex;width:100%}:host nb-auth-block{margin:auto}@media (max-width: 767.98px){:host nb-card{border-radius:0;height:100vh}}:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:2.5rem}@media (max-width: 767.98px){:host ::ng-deep nb-layout .layout .layout-container .content .columns nb-layout-column{padding:0}}\\n/*\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/*!\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: i3.Location }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbLoginComponent {\n    constructor(service, options = {}, cd, router) {\n        this.service = service;\n        this.options = options;\n        this.cd = cd;\n        this.router = router;\n        this.redirectDelay = 0;\n        this.showMessages = {};\n        this.strategy = '';\n        this.errors = [];\n        this.messages = [];\n        this.user = {};\n        this.submitted = false;\n        this.socialLinks = [];\n        this.rememberMe = false;\n        this.redirectDelay = this.getConfigValue('forms.login.redirectDelay');\n        this.showMessages = this.getConfigValue('forms.login.showMessages');\n        this.strategy = this.getConfigValue('forms.login.strategy');\n        this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n        this.rememberMe = this.getConfigValue('forms.login.rememberMe');\n    }\n    login() {\n        this.errors = [];\n        this.messages = [];\n        this.submitted = true;\n        this.service.authenticate(this.strategy, this.user).subscribe((result) => {\n            this.submitted = false;\n            if (result.isSuccess()) {\n                this.messages = result.getMessages();\n            }\n            else {\n                this.errors = result.getErrors();\n            }\n            const redirect = result.getRedirect();\n            if (redirect) {\n                setTimeout(() => {\n                    return this.router.navigateByUrl(redirect);\n                }, this.redirectDelay);\n            }\n            this.cd.detectChanges();\n        });\n    }\n    getConfigValue(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbLoginComponent, deps: [{ token: NbAuthService }, { token: NB_AUTH_OPTIONS }, { token: i0.ChangeDetectorRef }, { token: i2.Router }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbLoginComponent, selector: \"nb-login\", ngImport: i0, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Login</h1>\\n<p class=\\\"sub-title\\\">Hello! Log in with your email.</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"login()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           name=\\\"email\\\"\\n           id=\\\"input-email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fieldSize=\\\"large\\\"\\n           autofocus\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <span class=\\\"label-with-link\\\">\\n      <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n      <a class=\\\"forgot-password caption-2\\\" routerLink=\\\"../request-password\\\">Forgot Password?</a>\\n    </span>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           name=\\\"password\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           placeholder=\\\"Password\\\"\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched \\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\">\\n    <nb-checkbox name=\\\"rememberMe\\\" [(ngModel)]=\\\"user.rememberMe\\\" *ngIf=\\\"rememberMe\\\">Remember me</nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Log In\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Register\\\">\\n  Don't have an account? <a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a>\\n</section>\\n\", dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i4.NbCheckboxComponent, selector: \"nb-checkbox\", inputs: [\"checked\", \"disabled\", \"status\", \"indeterminate\"], outputs: [\"checkedChange\"] }, { kind: \"component\", type: i4.NbAlertComponent, selector: \"nb-alert\", inputs: [\"size\", \"status\", \"accent\", \"outline\", \"closable\"], outputs: [\"close\"] }, { kind: \"directive\", type: i4.NbInputDirective, selector: \"input[nbInput],textarea[nbInput]\", inputs: [\"fieldSize\", \"status\", \"shape\", \"fullWidth\"] }, { kind: \"component\", type: i4.NbButtonComponent, selector: \"button[nbButton],a[nbButton],input[type=\\\"button\\\"][nbButton],input[type=\\\"submit\\\"][nbButton]\", inputs: [\"hero\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i5.ɵNgNoValidate, selector: \"form:not([ngNoForm]):not([ngNativeValidate])\" }, { kind: \"directive\", type: i5.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i5.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i5.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { kind: \"directive\", type: i5.RequiredValidator, selector: \":not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]\", inputs: [\"required\"] }, { kind: \"directive\", type: i5.MinLengthValidator, selector: \"[minlength][formControlName],[minlength][formControl],[minlength][ngModel]\", inputs: [\"minlength\"] }, { kind: \"directive\", type: i5.MaxLengthValidator, selector: \"[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]\", inputs: [\"maxlength\"] }, { kind: \"directive\", type: i5.PatternValidator, selector: \"[pattern][formControlName],[pattern][formControl],[pattern][ngModel]\", inputs: [\"pattern\"] }, { kind: \"directive\", type: i5.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: i5.NgForm, selector: \"form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]\", inputs: [\"ngFormOptions\"], outputs: [\"ngSubmit\"], exportAs: [\"ngForm\"] }, { kind: \"component\", type: i4.NbIconComponent, selector: \"nb-icon\", inputs: [\"icon\", \"pack\", \"options\", \"status\", \"config\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbLoginComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-login', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Login</h1>\\n<p class=\\\"sub-title\\\">Hello! Log in with your email.</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"login()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           name=\\\"email\\\"\\n           id=\\\"input-email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fieldSize=\\\"large\\\"\\n           autofocus\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <span class=\\\"label-with-link\\\">\\n      <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n      <a class=\\\"forgot-password caption-2\\\" routerLink=\\\"../request-password\\\">Forgot Password?</a>\\n    </span>\\n    <input nbInput\\n           fullWidth\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           name=\\\"password\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           placeholder=\\\"Password\\\"\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched \\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\">\\n    <nb-checkbox name=\\\"rememberMe\\\" [(ngModel)]=\\\"user.rememberMe\\\" *ngIf=\\\"rememberMe\\\">Remember me</nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Log In\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Register\\\">\\n  Don't have an account? <a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a>\\n</section>\\n\" }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.Router }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbRegisterComponent {\n    constructor(service, options = {}, cd, router) {\n        this.service = service;\n        this.options = options;\n        this.cd = cd;\n        this.router = router;\n        this.redirectDelay = 0;\n        this.showMessages = {};\n        this.strategy = '';\n        this.submitted = false;\n        this.errors = [];\n        this.messages = [];\n        this.user = {};\n        this.socialLinks = [];\n        this.redirectDelay = this.getConfigValue('forms.register.redirectDelay');\n        this.showMessages = this.getConfigValue('forms.register.showMessages');\n        this.strategy = this.getConfigValue('forms.register.strategy');\n        this.socialLinks = this.getConfigValue('forms.login.socialLinks');\n    }\n    register() {\n        this.errors = this.messages = [];\n        this.submitted = true;\n        this.service.register(this.strategy, this.user).subscribe((result) => {\n            this.submitted = false;\n            if (result.isSuccess()) {\n                this.messages = result.getMessages();\n            }\n            else {\n                this.errors = result.getErrors();\n            }\n            const redirect = result.getRedirect();\n            if (redirect) {\n                setTimeout(() => {\n                    return this.router.navigateByUrl(redirect);\n                }, this.redirectDelay);\n            }\n            this.cd.detectChanges();\n        });\n    }\n    getConfigValue(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbRegisterComponent, deps: [{ token: NbAuthService }, { token: NB_AUTH_OPTIONS }, { token: i0.ChangeDetectorRef }, { token: i2.Router }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbRegisterComponent, selector: \"nb-register\", ngImport: i0, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Register</h1>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"register()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-name\\\">Full name:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.fullName\\\"\\n           #fullName=\\\"ngModel\\\"\\n           id=\\\"input-name\\\"\\n           name=\\\"fullName\\\"\\n           placeholder=\\\"Full name\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"fullName.dirty ? (fullName.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.fullName.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.fullName.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.fullName.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"fullName.invalid && fullName.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"fullName.invalid && fullName.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.required\\\">\\n        Full name is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.minlength || fullName.errors?.maxlength\\\">\\n        Full name should contains\\n        from {{getConfigValue('forms.validation.fullName.minLength')}}\\n        to {{getConfigValue('forms.validation.fullName.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           placeholder=\\\"Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Repeat password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.dirty ? (rePass.invalid || password.value != rePass.value  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.invalid && rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\" *ngIf=\\\"getConfigValue('forms.register.terms')\\\">\\n    <nb-checkbox name=\\\"terms\\\" [(ngModel)]=\\\"user.terms\\\" [required]=\\\"getConfigValue('forms.register.terms')\\\">\\n      Agree to <a href=\\\"#\\\" target=\\\"_blank\\\"><strong>Terms & Conditions</strong></a>\\n    </nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Register\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Sign in\\\">\\n  Already have an account? <a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Log in</a>\\n</section>\\n\", styles: [\":host .title{margin-bottom:2rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i4.NbCheckboxComponent, selector: \"nb-checkbox\", inputs: [\"checked\", \"disabled\", \"status\", \"indeterminate\"], outputs: [\"checkedChange\"] }, { kind: \"component\", type: i4.NbAlertComponent, selector: \"nb-alert\", inputs: [\"size\", \"status\", \"accent\", \"outline\", \"closable\"], outputs: [\"close\"] }, { kind: \"directive\", type: i4.NbInputDirective, selector: \"input[nbInput],textarea[nbInput]\", inputs: [\"fieldSize\", \"status\", \"shape\", \"fullWidth\"] }, { kind: \"component\", type: i4.NbButtonComponent, selector: \"button[nbButton],a[nbButton],input[type=\\\"button\\\"][nbButton],input[type=\\\"submit\\\"][nbButton]\", inputs: [\"hero\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i5.ɵNgNoValidate, selector: \"form:not([ngNoForm]):not([ngNativeValidate])\" }, { kind: \"directive\", type: i5.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i5.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i5.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { kind: \"directive\", type: i5.RequiredValidator, selector: \":not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]\", inputs: [\"required\"] }, { kind: \"directive\", type: i5.MinLengthValidator, selector: \"[minlength][formControlName],[minlength][formControl],[minlength][ngModel]\", inputs: [\"minlength\"] }, { kind: \"directive\", type: i5.MaxLengthValidator, selector: \"[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]\", inputs: [\"maxlength\"] }, { kind: \"directive\", type: i5.PatternValidator, selector: \"[pattern][formControlName],[pattern][formControl],[pattern][ngModel]\", inputs: [\"pattern\"] }, { kind: \"directive\", type: i5.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: i5.NgForm, selector: \"form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]\", inputs: [\"ngFormOptions\"], outputs: [\"ngSubmit\"], exportAs: [\"ngForm\"] }, { kind: \"component\", type: i4.NbIconComponent, selector: \"nb-icon\", inputs: [\"icon\", \"pack\", \"options\", \"status\", \"config\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbRegisterComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-register', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Register</h1>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"register()\\\" #form=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-name\\\">Full name:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.fullName\\\"\\n           #fullName=\\\"ngModel\\\"\\n           id=\\\"input-name\\\"\\n           name=\\\"fullName\\\"\\n           placeholder=\\\"Full name\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"fullName.dirty ? (fullName.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.fullName.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.fullName.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.fullName.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"fullName.invalid && fullName.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"fullName.invalid && fullName.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.required\\\">\\n        Full name is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"fullName.errors?.minlength || fullName.errors?.maxlength\\\">\\n        Full name should contains\\n        from {{getConfigValue('forms.validation.fullName.minLength')}}\\n        to {{getConfigValue('forms.validation.fullName.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           placeholder=\\\"Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contain\\n        from {{ getConfigValue('forms.validation.password.minLength') }}\\n        to {{ getConfigValue('forms.validation.password.maxLength') }}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Repeat password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.dirty ? (rePass.invalid || password.value != rePass.value  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.invalid && rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-control-group accept-group\\\" *ngIf=\\\"getConfigValue('forms.register.terms')\\\">\\n    <nb-checkbox name=\\\"terms\\\" [(ngModel)]=\\\"user.terms\\\" [required]=\\\"getConfigValue('forms.register.terms')\\\">\\n      Agree to <a href=\\\"#\\\" target=\\\"_blank\\\"><strong>Terms & Conditions</strong></a>\\n    </nb-checkbox>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !form.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Register\\n  </button>\\n</form>\\n\\n<section *ngIf=\\\"socialLinks && socialLinks.length > 0\\\" class=\\\"links\\\" aria-label=\\\"Social sign in\\\">\\n  or enter with:\\n  <div class=\\\"socials\\\">\\n    <ng-container *ngFor=\\\"let socialLink of socialLinks\\\">\\n      <a *ngIf=\\\"socialLink.link\\\"\\n         [routerLink]=\\\"socialLink.link\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n      <a *ngIf=\\\"socialLink.url\\\"\\n         [attr.href]=\\\"socialLink.url\\\"\\n         [attr.target]=\\\"socialLink.target\\\"\\n         [attr.class]=\\\"socialLink.icon\\\"\\n         [class.with-icon]=\\\"socialLink.icon\\\">\\n        <nb-icon *ngIf=\\\"socialLink.icon; else title\\\" [icon]=\\\"socialLink.icon\\\"></nb-icon>\\n        <ng-template #title>{{ socialLink.title }}</ng-template>\\n      </a>\\n    </ng-container>\\n  </div>\\n</section>\\n\\n<section class=\\\"another-action\\\" aria-label=\\\"Sign in\\\">\\n  Already have an account? <a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Log in</a>\\n</section>\\n\", styles: [\":host .title{margin-bottom:2rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.Router }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbLogoutComponent {\n    constructor(service, options = {}, router) {\n        this.service = service;\n        this.options = options;\n        this.router = router;\n        this.redirectDelay = 0;\n        this.strategy = '';\n        this.redirectDelay = this.getConfigValue('forms.logout.redirectDelay');\n        this.strategy = this.getConfigValue('forms.logout.strategy');\n    }\n    ngOnInit() {\n        this.logout(this.strategy);\n    }\n    logout(strategy) {\n        this.service.logout(strategy).subscribe((result) => {\n            const redirect = result.getRedirect();\n            if (redirect) {\n                setTimeout(() => {\n                    return this.router.navigateByUrl(redirect);\n                }, this.redirectDelay);\n            }\n        });\n    }\n    getConfigValue(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbLogoutComponent, deps: [{ token: NbAuthService }, { token: NB_AUTH_OPTIONS }, { token: i2.Router }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbLogoutComponent, selector: \"nb-logout\", ngImport: i0, template: \"<div>Logging out, please wait...</div>\\n\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbLogoutComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-logout', template: \"<div>Logging out, please wait...</div>\\n\" }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_OPTIONS]\n                }] }, { type: i2.Router }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbRequestPasswordComponent {\n    constructor(service, options = {}, cd, router) {\n        this.service = service;\n        this.options = options;\n        this.cd = cd;\n        this.router = router;\n        this.redirectDelay = 0;\n        this.showMessages = {};\n        this.strategy = '';\n        this.submitted = false;\n        this.errors = [];\n        this.messages = [];\n        this.user = {};\n        this.redirectDelay = this.getConfigValue('forms.requestPassword.redirectDelay');\n        this.showMessages = this.getConfigValue('forms.requestPassword.showMessages');\n        this.strategy = this.getConfigValue('forms.requestPassword.strategy');\n    }\n    requestPass() {\n        this.errors = this.messages = [];\n        this.submitted = true;\n        this.service.requestPassword(this.strategy, this.user).subscribe((result) => {\n            this.submitted = false;\n            if (result.isSuccess()) {\n                this.messages = result.getMessages();\n            }\n            else {\n                this.errors = result.getErrors();\n            }\n            const redirect = result.getRedirect();\n            if (redirect) {\n                setTimeout(() => {\n                    return this.router.navigateByUrl(redirect);\n                }, this.redirectDelay);\n            }\n            this.cd.detectChanges();\n        });\n    }\n    getConfigValue(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbRequestPasswordComponent, deps: [{ token: NbAuthService }, { token: NB_AUTH_OPTIONS }, { token: i0.ChangeDetectorRef }, { token: i2.Router }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbRequestPasswordComponent, selector: \"nb-request-password-page\", ngImport: i0, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Forgot Password</h1>\\n<p class=\\\"sub-title\\\">Enter your email address and we\\u2019ll send a link to reset your password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"requestPass()\\\" #requestPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Enter your email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !requestPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Request password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a routerLink=\\\"../register\\\" class=\\\"text-link\\\">Register</a></p>\\n</section>\\n\", styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i4.NbAlertComponent, selector: \"nb-alert\", inputs: [\"size\", \"status\", \"accent\", \"outline\", \"closable\"], outputs: [\"close\"] }, { kind: \"directive\", type: i4.NbInputDirective, selector: \"input[nbInput],textarea[nbInput]\", inputs: [\"fieldSize\", \"status\", \"shape\", \"fullWidth\"] }, { kind: \"component\", type: i4.NbButtonComponent, selector: \"button[nbButton],a[nbButton],input[type=\\\"button\\\"][nbButton],input[type=\\\"submit\\\"][nbButton]\", inputs: [\"hero\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i5.ɵNgNoValidate, selector: \"form:not([ngNoForm]):not([ngNativeValidate])\" }, { kind: \"directive\", type: i5.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i5.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i5.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { kind: \"directive\", type: i5.RequiredValidator, selector: \":not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]\", inputs: [\"required\"] }, { kind: \"directive\", type: i5.PatternValidator, selector: \"[pattern][formControlName],[pattern][formControl],[pattern][ngModel]\", inputs: [\"pattern\"] }, { kind: \"directive\", type: i5.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: i5.NgForm, selector: \"form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]\", inputs: [\"ngFormOptions\"], outputs: [\"ngSubmit\"], exportAs: [\"ngForm\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbRequestPasswordComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-request-password-page', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Forgot Password</h1>\\n<p class=\\\"sub-title\\\">Enter your email address and we\\u2019ll send a link to reset your password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"requestPass()\\\" #requestPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-email\\\">Enter your email address:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.email\\\"\\n           #email=\\\"ngModel\\\"\\n           id=\\\"input-email\\\"\\n           name=\\\"email\\\"\\n           pattern=\\\".+@.+\\\\..+\\\"\\n           placeholder=\\\"Email address\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"email.dirty ? (email.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.email.required')\\\"\\n           [attr.aria-invalid]=\\\"email.invalid && email.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"email.invalid && email.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.required\\\">\\n        Email is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"email.errors?.pattern\\\">\\n        Email should be the real one!\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          fullWidth\\n          status=\\\"primary\\\"\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !requestPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Request password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a routerLink=\\\"../register\\\" class=\\\"text-link\\\">Register</a></p>\\n</section>\\n\", styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.Router }] });\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbResetPasswordComponent {\n    constructor(service, options = {}, cd, router) {\n        this.service = service;\n        this.options = options;\n        this.cd = cd;\n        this.router = router;\n        this.redirectDelay = 0;\n        this.showMessages = {};\n        this.strategy = '';\n        this.submitted = false;\n        this.errors = [];\n        this.messages = [];\n        this.user = {};\n        this.redirectDelay = this.getConfigValue('forms.resetPassword.redirectDelay');\n        this.showMessages = this.getConfigValue('forms.resetPassword.showMessages');\n        this.strategy = this.getConfigValue('forms.resetPassword.strategy');\n    }\n    resetPass() {\n        this.errors = this.messages = [];\n        this.submitted = true;\n        this.service.resetPassword(this.strategy, this.user).subscribe((result) => {\n            this.submitted = false;\n            if (result.isSuccess()) {\n                this.messages = result.getMessages();\n            }\n            else {\n                this.errors = result.getErrors();\n            }\n            const redirect = result.getRedirect();\n            if (redirect) {\n                setTimeout(() => {\n                    return this.router.navigateByUrl(redirect);\n                }, this.redirectDelay);\n            }\n            this.cd.detectChanges();\n        });\n    }\n    getConfigValue(key) {\n        return getDeepFromObject(this.options, key, null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbResetPasswordComponent, deps: [{ token: NbAuthService }, { token: NB_AUTH_OPTIONS }, { token: i0.ChangeDetectorRef }, { token: i2.Router }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.1.3\", type: NbResetPasswordComponent, selector: \"nb-reset-password-page\", ngImport: i0, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Change password</h1>\\n<p class=\\\"sub-title\\\">Please set a new password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"resetPass()\\\" #resetPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">New Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           class=\\\"first\\\"\\n           placeholder=\\\"New Password\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contains\\n        from {{getConfigValue('forms.validation.password.minLength')}}\\n        to {{getConfigValue('forms.validation.password.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Confirm Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           type=\\\"password\\\"\\n           class=\\\"last\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.touched\\n               ? (rePass.invalid || password.value != rePass.value ? 'danger' : 'success')\\n               : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.invalid && rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          status=\\\"primary\\\"\\n          fullWidth\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !resetPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Change password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a></p>\\n</section>\\n\", styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"], dependencies: [{ kind: \"directive\", type: i3.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: i4.NbAlertComponent, selector: \"nb-alert\", inputs: [\"size\", \"status\", \"accent\", \"outline\", \"closable\"], outputs: [\"close\"] }, { kind: \"directive\", type: i4.NbInputDirective, selector: \"input[nbInput],textarea[nbInput]\", inputs: [\"fieldSize\", \"status\", \"shape\", \"fullWidth\"] }, { kind: \"component\", type: i4.NbButtonComponent, selector: \"button[nbButton],a[nbButton],input[type=\\\"button\\\"][nbButton],input[type=\\\"submit\\\"][nbButton]\", inputs: [\"hero\"] }, { kind: \"directive\", type: i2.RouterLink, selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"info\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i5.ɵNgNoValidate, selector: \"form:not([ngNoForm]):not([ngNativeValidate])\" }, { kind: \"directive\", type: i5.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { kind: \"directive\", type: i5.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i5.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { kind: \"directive\", type: i5.RequiredValidator, selector: \":not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]\", inputs: [\"required\"] }, { kind: \"directive\", type: i5.MinLengthValidator, selector: \"[minlength][formControlName],[minlength][formControl],[minlength][ngModel]\", inputs: [\"minlength\"] }, { kind: \"directive\", type: i5.MaxLengthValidator, selector: \"[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]\", inputs: [\"maxlength\"] }, { kind: \"directive\", type: i5.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"directive\", type: i5.NgForm, selector: \"form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]\", inputs: [\"ngFormOptions\"], outputs: [\"ngSubmit\"], exportAs: [\"ngForm\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbResetPasswordComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nb-reset-password-page', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<h1 id=\\\"title\\\" class=\\\"title\\\">Change password</h1>\\n<p class=\\\"sub-title\\\">Please set a new password</p>\\n\\n<nb-alert *ngIf=\\\"showMessages.error && errors?.length && !submitted\\\" outline=\\\"danger\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Oh snap!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let error of errors\\\" class=\\\"alert-message\\\">{{ error }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<nb-alert *ngIf=\\\"showMessages.success && messages?.length && !submitted\\\" outline=\\\"success\\\" role=\\\"alert\\\">\\n  <p class=\\\"alert-title\\\"><b>Hooray!</b></p>\\n  <ul class=\\\"alert-message-list\\\">\\n    <li *ngFor=\\\"let message of messages\\\" class=\\\"alert-message\\\">{{ message }}</li>\\n  </ul>\\n</nb-alert>\\n\\n<form (ngSubmit)=\\\"resetPass()\\\" #resetPassForm=\\\"ngForm\\\" aria-labelledby=\\\"title\\\">\\n\\n  <div class=\\\"form-control-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-password\\\">New Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.password\\\"\\n           #password=\\\"ngModel\\\"\\n           type=\\\"password\\\"\\n           id=\\\"input-password\\\"\\n           name=\\\"password\\\"\\n           class=\\\"first\\\"\\n           placeholder=\\\"New Password\\\"\\n           autofocus\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"password.dirty ? (password.invalid  ? 'danger' : 'success') : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [minlength]=\\\"getConfigValue('forms.validation.password.minLength')\\\"\\n           [maxlength]=\\\"getConfigValue('forms.validation.password.maxLength')\\\"\\n           [attr.aria-invalid]=\\\"password.invalid && password.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"password.invalid && password.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.required\\\">\\n        Password is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.errors?.minlength || password.errors?.maxlength\\\">\\n        Password should contains\\n        from {{getConfigValue('forms.validation.password.minLength')}}\\n        to {{getConfigValue('forms.validation.password.maxLength')}}\\n        characters\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <div class=\\\"form-group\\\">\\n    <label class=\\\"label\\\" for=\\\"input-re-password\\\">Confirm Password:</label>\\n    <input nbInput\\n           [(ngModel)]=\\\"user.confirmPassword\\\"\\n           #rePass=\\\"ngModel\\\"\\n           id=\\\"input-re-password\\\"\\n           name=\\\"rePass\\\"\\n           type=\\\"password\\\"\\n           class=\\\"last\\\"\\n           placeholder=\\\"Confirm Password\\\"\\n           fullWidth\\n           fieldSize=\\\"large\\\"\\n           [status]=\\\"rePass.touched\\n               ? (rePass.invalid || password.value != rePass.value ? 'danger' : 'success')\\n               : 'basic'\\\"\\n           [required]=\\\"getConfigValue('forms.validation.password.required')\\\"\\n           [attr.aria-invalid]=\\\"rePass.invalid && rePass.touched ? true : null\\\">\\n    <ng-container *ngIf=\\\"rePass.touched\\\">\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"rePass.invalid && rePass.errors?.required\\\">\\n        Password confirmation is required!\\n      </p>\\n      <p class=\\\"caption status-danger\\\" *ngIf=\\\"password.value != rePass.value && !rePass.errors?.required\\\">\\n        Password does not match the confirm password.\\n      </p>\\n    </ng-container>\\n  </div>\\n\\n  <button nbButton\\n          status=\\\"primary\\\"\\n          fullWidth\\n          size=\\\"large\\\"\\n          [disabled]=\\\"submitted || !resetPassForm.valid\\\"\\n          [class.btn-pulse]=\\\"submitted\\\">\\n    Change password\\n  </button>\\n</form>\\n\\n<section class=\\\"sign-in-or-up\\\" aria-label=\\\"Sign in or sign up\\\">\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../login\\\">Back to Log In</a></p>\\n  <p><a class=\\\"text-link\\\" routerLink=\\\"../register\\\">Register</a></p>\\n</section>\\n\", styles: [\":host .form-group:last-of-type{margin-bottom:3rem}\\n/**\\n * @license\\n * Copyright Akveo. All Rights Reserved.\\n * Licensed under the MIT License. See License.txt in the project root for license information.\\n */\\n\"] }]\n        }], ctorParameters: () => [{ type: NbAuthService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.Router }] });\n\nfunction nbStrategiesFactory(options, injector) {\n    const strategies = [];\n    options.strategies\n        .forEach(([strategyClass, strategyOptions]) => {\n        const strategy = injector.get(strategyClass);\n        strategy.setOptions(strategyOptions);\n        strategies.push(strategy);\n    });\n    return strategies;\n}\nfunction nbTokensFactory(strategies) {\n    const tokens = [];\n    strategies\n        .forEach((strategy) => {\n        tokens.push(strategy.getOption('token.class'));\n    });\n    return tokens;\n}\nfunction nbOptionsFactory(options) {\n    return deepExtend(defaultAuthOptions, options);\n}\nfunction nbNoOpInterceptorFilter(req) {\n    return true;\n}\nclass NbAuthModule {\n    static forRoot(nbAuthOptions) {\n        return {\n            ngModule: NbAuthModule,\n            providers: [\n                { provide: NB_AUTH_USER_OPTIONS, useValue: nbAuthOptions },\n                { provide: NB_AUTH_OPTIONS, useFactory: nbOptionsFactory, deps: [NB_AUTH_USER_OPTIONS] },\n                { provide: NB_AUTH_STRATEGIES, useFactory: nbStrategiesFactory, deps: [NB_AUTH_OPTIONS, Injector] },\n                { provide: NB_AUTH_TOKENS, useFactory: nbTokensFactory, deps: [NB_AUTH_STRATEGIES] },\n                { provide: NB_AUTH_FALLBACK_TOKEN, useValue: NbAuthSimpleToken },\n                { provide: NB_AUTH_INTERCEPTOR_HEADER, useValue: 'Authorization' },\n                { provide: NB_AUTH_TOKEN_INTERCEPTOR_FILTER, useValue: nbNoOpInterceptorFilter },\n                { provide: NbTokenStorage, useClass: NbTokenLocalStorage },\n                NbAuthTokenParceler,\n                NbAuthService,\n                NbTokenService,\n                NbDummyAuthStrategy,\n                NbPasswordAuthStrategy,\n                NbOAuth2AuthStrategy,\n            ],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthModule, declarations: [NbAuthComponent,\n            NbAuthBlockComponent,\n            NbLoginComponent,\n            NbRegisterComponent,\n            NbRequestPasswordComponent,\n            NbResetPasswordComponent,\n            NbLogoutComponent], imports: [CommonModule,\n            NbLayoutModule,\n            NbCardModule,\n            NbCheckboxModule,\n            NbAlertModule,\n            NbInputModule,\n            NbButtonModule,\n            RouterModule,\n            FormsModule,\n            NbIconModule], exports: [NbAuthComponent,\n            NbAuthBlockComponent,\n            NbLoginComponent,\n            NbRegisterComponent,\n            NbRequestPasswordComponent,\n            NbResetPasswordComponent,\n            NbLogoutComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthModule, imports: [CommonModule,\n            NbLayoutModule,\n            NbCardModule,\n            NbCheckboxModule,\n            NbAlertModule,\n            NbInputModule,\n            NbButtonModule,\n            RouterModule,\n            FormsModule,\n            NbIconModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        NbLayoutModule,\n                        NbCardModule,\n                        NbCheckboxModule,\n                        NbAlertModule,\n                        NbInputModule,\n                        NbButtonModule,\n                        RouterModule,\n                        FormsModule,\n                        NbIconModule,\n                    ],\n                    declarations: [\n                        NbAuthComponent,\n                        NbAuthBlockComponent,\n                        NbLoginComponent,\n                        NbRegisterComponent,\n                        NbRequestPasswordComponent,\n                        NbResetPasswordComponent,\n                        NbLogoutComponent,\n                    ],\n                    exports: [\n                        NbAuthComponent,\n                        NbAuthBlockComponent,\n                        NbLoginComponent,\n                        NbRegisterComponent,\n                        NbRequestPasswordComponent,\n                        NbResetPasswordComponent,\n                        NbLogoutComponent,\n                    ],\n                }]\n        }] });\n\nconst routes = [\n    {\n        path: 'auth',\n        component: NbAuthComponent,\n        children: [\n            {\n                path: '',\n                component: NbLoginComponent,\n            },\n            {\n                path: 'login',\n                component: NbLoginComponent,\n            },\n            {\n                path: 'register',\n                component: NbRegisterComponent,\n            },\n            {\n                path: 'logout',\n                component: NbLogoutComponent,\n            },\n            {\n                path: 'request-password',\n                component: NbRequestPasswordComponent,\n            },\n            {\n                path: 'reset-password',\n                component: NbResetPasswordComponent,\n            },\n        ],\n    },\n];\n\nclass NbAuthJWTInterceptor {\n    constructor(injector, filter) {\n        this.injector = injector;\n        this.filter = filter;\n    }\n    intercept(req, next) {\n        // do not intercept request whose urls are filtered by the injected filter\n        if (!this.filter(req)) {\n            return this.authService.isAuthenticatedOrRefresh()\n                .pipe(switchMap(authenticated => {\n                if (authenticated) {\n                    return this.authService.getToken().pipe(switchMap((token) => {\n                        const JWT = `Bearer ${token.getValue()}`;\n                        req = req.clone({\n                            setHeaders: {\n                                Authorization: JWT,\n                            },\n                        });\n                        return next.handle(req);\n                    }));\n                }\n                else {\n                    // Request is sent to server without authentication so that the client code\n                    // receives the 401/403 error and can act as desired ('session expired', redirect to login, aso)\n                    return next.handle(req);\n                }\n            }));\n        }\n        else {\n            return next.handle(req);\n        }\n    }\n    get authService() {\n        return this.injector.get(NbAuthService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthJWTInterceptor, deps: [{ token: i0.Injector }, { token: NB_AUTH_TOKEN_INTERCEPTOR_FILTER }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthJWTInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthJWTInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_TOKEN_INTERCEPTOR_FILTER]\n                }] }] });\n\nclass NbAuthSimpleInterceptor {\n    constructor(injector, headerName = 'Authorization') {\n        this.injector = injector;\n        this.headerName = headerName;\n    }\n    intercept(req, next) {\n        return this.authService.getToken().pipe(switchMap((token) => {\n            if (token && token.getValue()) {\n                req = req.clone({\n                    setHeaders: {\n                        [this.headerName]: token.getValue(),\n                    },\n                });\n            }\n            return next.handle(req);\n        }));\n    }\n    get authService() {\n        return this.injector.get(NbAuthService);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthSimpleInterceptor, deps: [{ token: i0.Injector }, { token: NB_AUTH_INTERCEPTOR_HEADER }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthSimpleInterceptor }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbAuthSimpleInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NB_AUTH_INTERCEPTOR_HEADER]\n                }] }] });\n\nclass NbUser {\n    constructor(id, email, password, rememberMe, terms, confirmPassword, fullName) {\n        this.id = id;\n        this.email = email;\n        this.password = password;\n        this.rememberMe = rememberMe;\n        this.terms = terms;\n        this.confirmPassword = confirmPassword;\n        this.fullName = fullName;\n    }\n}\n\n/**\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NB_AUTH_FALLBACK_TOKEN, NB_AUTH_INTERCEPTOR_HEADER, NB_AUTH_OPTIONS, NB_AUTH_STRATEGIES, NB_AUTH_TOKENS, NB_AUTH_TOKEN_INTERCEPTOR_FILTER, NB_AUTH_USER_OPTIONS, NbAuthBlockComponent, NbAuthComponent, NbAuthEmptyTokenError, NbAuthIllegalJWTTokenError, NbAuthIllegalTokenError, NbAuthJWTInterceptor, NbAuthJWTToken, NbAuthModule, NbAuthOAuth2JWTToken, NbAuthOAuth2Token, NbAuthResult, NbAuthService, NbAuthSimpleInterceptor, NbAuthSimpleToken, NbAuthStrategy, NbAuthStrategyOptions, NbAuthToken, NbAuthTokenNotFoundError, NbAuthTokenParceler, NbDummyAuthStrategy, NbDummyAuthStrategyOptions, NbLoginComponent, NbLogoutComponent, NbOAuth2AuthStrategy, NbOAuth2AuthStrategyOptions, NbOAuth2ClientAuthMethod, NbOAuth2GrantType, NbOAuth2ResponseType, NbPasswordAuthStrategy, NbPasswordAuthStrategyOptions, NbRegisterComponent, NbRequestPasswordComponent, NbResetPasswordComponent, NbTokenLocalStorage, NbTokenService, NbTokenStorage, NbUser, auth2StrategyOptions, b64DecodeUnicode, b64decode, decodeJwtPayload, deepExtend, defaultAuthOptions, dummyStrategyOptions, getDeepFromObject, nbAuthCreateToken, nbNoOpInterceptorFilter, nbOptionsFactory, nbStrategiesFactory, nbTokensFactory, passwordStrategyOptions, routes, urlBase64Decode };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC1H,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,SAAS,EAAEC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AACtJ,SAASC,eAAe,EAAEC,EAAE,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AAC5F,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,YAAY,EAAEC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AAAC,MAAAC,GAAA;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqkBgBvC,EAAE,CAAAyC,cAAA,YAu1Cqa,CAAC;IAv1CxazC,EAAE,CAAA0C,MAAA,EAu1Cgb,CAAC;IAv1Cnb1C,EAAE,CAAA2C,YAAA,CAu1Cqb,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAK,SAAA;IAv1Cxb7C,EAAE,CAAA8C,SAAA,CAu1Cgb,CAAC;IAv1Cnb9C,EAAE,CAAA+C,iBAAA,CAAAH,QAu1Cgb,CAAC;EAAA;AAAA;AAAA,SAAAI,qCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CnbvC,EAAE,CAAAyC,cAAA,kBAu1C+Q,CAAC,WAA4B,CAAC,OAAE,CAAC;IAv1ClTzC,EAAE,CAAA0C,MAAA,cAu1CuT,CAAC;IAv1C1T1C,EAAE,CAAA2C,YAAA,CAu1C2T,CAAC,CAAG,CAAC;IAv1ClU3C,EAAE,CAAAyC,cAAA,YAu1CoW,CAAC;IAv1CvWzC,EAAE,CAAAiD,UAAA,IAAAX,yCAAA,gBAu1Cqa,CAAC;IAv1CxatC,EAAE,CAAA2C,YAAA,CAu1C8b,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAv1C9clD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAu1C0Y,CAAC;IAv1C7Y9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAG,MAu1C0Y,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1C7YvC,EAAE,CAAAyC,cAAA,YAu1CstB,CAAC;IAv1CztBzC,EAAE,CAAA0C,MAAA,EAu1CmuB,CAAC;IAv1CtuB1C,EAAE,CAAA2C,YAAA,CAu1CwuB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,UAAA,GAAAf,GAAA,CAAAK,SAAA;IAv1C3uB7C,EAAE,CAAA8C,SAAA,CAu1CmuB,CAAC;IAv1CtuB9C,EAAE,CAAA+C,iBAAA,CAAAQ,UAu1CmuB,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CtuBvC,EAAE,CAAAyC,cAAA,kBAu1C6jB,CAAC,WAA4B,CAAC,OAAE,CAAC;IAv1ChmBzC,EAAE,CAAA0C,MAAA,aAu1ComB,CAAC;IAv1CvmB1C,EAAE,CAAA2C,YAAA,CAu1CwmB,CAAC,CAAG,CAAC;IAv1C/mB3C,EAAE,CAAAyC,cAAA,YAu1CipB,CAAC;IAv1CppBzC,EAAE,CAAAiD,UAAA,IAAAK,yCAAA,gBAu1CstB,CAAC;IAv1CztBtD,EAAE,CAAA2C,YAAA,CAu1CivB,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAv1CjwBlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAu1C2rB,CAAC;IAv1C9rB9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAO,QAu1C2rB,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1C9rBvC,EAAE,CAAAyC,cAAA,WAu1CumD,CAAC;IAv1C1mDzC,EAAE,CAAA0C,MAAA,0BAu1C2oD,CAAC;IAv1C9oD1C,EAAE,CAAA2C,YAAA,CAu1C+oD,CAAC;EAAA;AAAA;AAAA,SAAAgB,8CAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1ClpDvC,EAAE,CAAAyC,cAAA,WAu1C0tD,CAAC;IAv1C7tDzC,EAAE,CAAA0C,MAAA,qCAu1CywD,CAAC;IAv1C5wD1C,EAAE,CAAA2C,YAAA,CAu1C6wD,CAAC;EAAA;AAAA;AAAA,SAAAiB,0CAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1ChxDvC,EAAE,CAAA6D,uBAAA,EAu1C2hD,CAAC;IAv1C9hD7D,EAAE,CAAAiD,UAAA,IAAAS,6CAAA,eAu1CumD,CAAC,IAAAC,6CAAA,eAAkH,CAAC;IAv1C7tD3D,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAY,QAAA,GAAF/D,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAu1ComD,CAAC;IAv1CvmD9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAV,MAAA,kBAAAU,QAAA,CAAAV,MAAA,CAAAY,QAu1ComD,CAAC;IAv1CvmDjE,EAAE,CAAA8C,SAAA,CAu1CutD,CAAC;IAv1C1tD9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAV,MAAA,kBAAAU,QAAA,CAAAV,MAAA,CAAAa,OAu1CutD,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1C1tDvC,EAAE,CAAAyC,cAAA,WAu1C24F,CAAC;IAv1C94FzC,EAAE,CAAA0C,MAAA,6BAu1Ck7F,CAAC;IAv1Cr7F1C,EAAE,CAAA2C,YAAA,CAu1Cs7F,CAAC;EAAA;AAAA;AAAA,SAAAyB,8CAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1Cz7FvC,EAAE,CAAAyC,cAAA,WAu1CoiG,CAAC;IAv1CviGzC,EAAE,CAAA0C,MAAA,EAu1CmvG,CAAC;IAv1CtvG1C,EAAE,CAAA2C,YAAA,CAu1CuvG,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAv1C1vGlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,CAu1CmvG,CAAC;IAv1CtvG9C,EAAE,CAAAqE,kBAAA,mCAAAnB,MAAA,CAAAoB,cAAA,iDAAApB,MAAA,CAAAoB,cAAA,uDAu1CmvG,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CtvGvC,EAAE,CAAA6D,uBAAA,EAu1C4zF,CAAC;IAv1C/zF7D,EAAE,CAAAiD,UAAA,IAAAkB,6CAAA,eAu1C24F,CAAC,IAAAC,6CAAA,eAAwJ,CAAC;IAv1CviGpE,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAqB,WAAA,GAAFxE,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAu1Cw4F,CAAC;IAv1C34F9C,EAAE,CAAAoD,UAAA,SAAAoB,WAAA,CAAAnB,MAAA,kBAAAmB,WAAA,CAAAnB,MAAA,CAAAY,QAu1Cw4F,CAAC;IAv1C34FjE,EAAE,CAAA8C,SAAA,CAu1CiiG,CAAC;IAv1CpiG9C,EAAE,CAAAoD,UAAA,UAAAoB,WAAA,CAAAnB,MAAA,kBAAAmB,WAAA,CAAAnB,MAAA,CAAAoB,SAAA,MAAAD,WAAA,CAAAnB,MAAA,kBAAAmB,WAAA,CAAAnB,MAAA,CAAAqB,SAAA,CAu1CiiG,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqC,GAAA,GAv1CpiG5E,EAAE,CAAA6E,gBAAA;IAAF7E,EAAE,CAAAyC,cAAA,qBAu1Cu6G,CAAC;IAv1C16GzC,EAAE,CAAA8E,gBAAA,2BAAAC,8EAAAC,MAAA;MAAFhF,EAAE,CAAAiF,aAAA,CAAAL,GAAA;MAAA,MAAA1B,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAFnD,EAAE,CAAAkF,kBAAA,CAAAhC,MAAA,CAAAiC,IAAA,CAAAC,UAAA,EAAAJ,MAAA,MAAA9B,MAAA,CAAAiC,IAAA,CAAAC,UAAA,GAAAJ,MAAA;MAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;IAAA,CAu1Ci5G,CAAC;IAv1Cp5GhF,EAAE,CAAA0C,MAAA,iBAu1Ck7G,CAAC;IAv1Cr7G1C,EAAE,CAAA2C,YAAA,CAu1Cg8G,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAv1Cn8GlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAAsF,gBAAA,YAAApC,MAAA,CAAAiC,IAAA,CAAAC,UAu1Ci5G,CAAC;EAAA;AAAA;AAAA,SAAAG,kEAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1Cp5GvC,EAAE,CAAAwF,SAAA,iBAu1C0rI,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAkD,aAAA,GAv1C7rIzF,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAC,IAu1C+qI,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1ClrIvC,EAAE,CAAA0C,MAAA,EAu1C8uI,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAkD,aAAA,GAv1CjvIzF,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAA+C,iBAAA,CAAA0C,aAAA,CAAAG,KAu1C8uI,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CjvIvC,EAAE,CAAAyC,cAAA,WAu1C4lI,CAAC;IAv1C/lIzC,EAAE,CAAAiD,UAAA,IAAAsC,iEAAA,qBAu1CgrI,CAAC,IAAAI,qEAAA,gCAv1CnrI3F,EAAE,CAAA8F,sBAu1CwtI,CAAC;IAv1C3tI9F,EAAE,CAAA2C,YAAA,CAu1CwwI,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAwD,QAAA,GAv1C3wI/F,EAAE,CAAAgE,WAAA;IAAA,MAAAyB,aAAA,GAAFzF,EAAE,CAAAmD,aAAA,GAAAN,SAAA;IAAF7C,EAAE,CAAAgG,WAAA,cAAAP,aAAA,CAAAC,IAu1C2lI,CAAC;IAv1C9lI1F,EAAE,CAAAoD,UAAA,eAAAqC,aAAA,CAAAQ,IAu1Ck9H,CAAC;IAv1Cr9HjG,EAAE,CAAAkG,WAAA,WAAAT,aAAA,CAAAU,MAAA,WAAAV,aAAA,CAAAC,IAAA;IAAF1F,EAAE,CAAA8C,SAAA,CAu1CwoI,CAAC;IAv1C3oI9C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAC,IAu1CwoI,CAAC,aAAAK,QAAS,CAAC;EAAA;AAAA;AAAA,SAAAK,kEAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CrpIvC,EAAE,CAAAwF,SAAA,iBAu1C4jJ,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAkD,aAAA,GAv1C/jJzF,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAC,IAu1CijJ,CAAC;EAAA;AAAA;AAAA,SAAAW,sEAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CpjJvC,EAAE,CAAA0C,MAAA,EAu1CgnJ,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAkD,aAAA,GAv1CnnJzF,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAA+C,iBAAA,CAAA0C,aAAA,CAAAG,KAu1CgnJ,CAAC;EAAA;AAAA;AAAA,SAAAU,wDAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CnnJvC,EAAE,CAAAyC,cAAA,OAu1C89I,CAAC;IAv1Cj+IzC,EAAE,CAAAiD,UAAA,IAAAmD,iEAAA,qBAu1CkjJ,CAAC,IAAAC,qEAAA,gCAv1CrjJrG,EAAE,CAAA8F,sBAu1C0lJ,CAAC;IAv1C7lJ9F,EAAE,CAAA2C,YAAA,CAu1C0oJ,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgE,SAAA,GAv1C7oJvG,EAAE,CAAAgE,WAAA;IAAA,MAAAyB,aAAA,GAAFzF,EAAE,CAAAmD,aAAA,GAAAN,SAAA;IAAF7C,EAAE,CAAAgG,WAAA,cAAAP,aAAA,CAAAC,IAu1C69I,CAAC;IAv1Ch+I1F,EAAE,CAAAkG,WAAA,SAAAT,aAAA,CAAAe,GAAA,EAAFxG,EAAE,CAAAyG,aAAA,YAAAhB,aAAA,CAAAU,MAAA,WAAAV,aAAA,CAAAC,IAAA;IAAF1F,EAAE,CAAA8C,SAAA,CAu1C0gJ,CAAC;IAv1C7gJ9C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAC,IAu1C0gJ,CAAC,aAAAa,SAAS,CAAC;EAAA;AAAA;AAAA,SAAAG,oDAAAnE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1CvhJvC,EAAE,CAAA6D,uBAAA,EAu1Cm4H,CAAC;IAv1Ct4H7D,EAAE,CAAAiD,UAAA,IAAA4C,uDAAA,eAu1C4lI,CAAC,IAAAS,uDAAA,eAAiY,CAAC;IAv1Cj+ItG,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAkD,aAAA,GAAAjD,GAAA,CAAAK,SAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAu1Cq6H,CAAC;IAv1Cx6H9C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAQ,IAu1Cq6H,CAAC;IAv1Cx6HjG,EAAE,CAAA8C,SAAA,CAu1CyyI,CAAC;IAv1C5yI9C,EAAE,CAAAoD,UAAA,SAAAqC,aAAA,CAAAe,GAu1CyyI,CAAC;EAAA;AAAA;AAAA,SAAAG,qCAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1C5yIvC,EAAE,CAAAyC,cAAA,iBAu1CyxH,CAAC;IAv1C5xHzC,EAAE,CAAA0C,MAAA,sBAu1C+yH,CAAC;IAv1ClzH1C,EAAE,CAAAyC,cAAA,aAu1Cs0H,CAAC;IAv1Cz0HzC,EAAE,CAAAiD,UAAA,IAAAyD,mDAAA,0BAu1Cm4H,CAAC;IAv1Ct4H1G,EAAE,CAAA2C,YAAA,CAu1CyqJ,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAv1CxrJlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAu1Cg4H,CAAC;IAv1Cn4H9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAA0D,WAu1Cg4H,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAtE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAv1Cn4HvC,EAAE,CAAAyC,cAAA,YAi5CmX,CAAC;IAj5CtXzC,EAAE,CAAA0C,MAAA,EAi5C8X,CAAC;IAj5CjY1C,EAAE,CAAA2C,YAAA,CAi5CmY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAK,SAAA;IAj5CtY7C,EAAE,CAAA8C,SAAA,CAi5C8X,CAAC;IAj5CjY9C,EAAE,CAAA+C,iBAAA,CAAAH,QAi5C8X,CAAC;EAAA;AAAA;AAAA,SAAAkE,wCAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CjYvC,EAAE,CAAAyC,cAAA,kBAi5C6N,CAAC,WAA4B,CAAC,OAAE,CAAC;IAj5ChQzC,EAAE,CAAA0C,MAAA,cAi5CqQ,CAAC;IAj5CxQ1C,EAAE,CAAA2C,YAAA,CAi5CyQ,CAAC,CAAG,CAAC;IAj5ChR3C,EAAE,CAAAyC,cAAA,YAi5CkT,CAAC;IAj5CrTzC,EAAE,CAAAiD,UAAA,IAAA4D,4CAAA,gBAi5CmX,CAAC;IAj5CtX7G,EAAE,CAAA2C,YAAA,CAi5C4Y,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5C5ZlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAi5CwV,CAAC;IAj5C3V9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAG,MAi5CwV,CAAC;EAAA;AAAA;AAAA,SAAA0D,6CAAAxE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C3VvC,EAAE,CAAAyC,cAAA,YAi5CoqB,CAAC;IAj5CvqBzC,EAAE,CAAA0C,MAAA,EAi5CirB,CAAC;IAj5CprB1C,EAAE,CAAA2C,YAAA,CAi5CsrB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,UAAA,GAAAf,GAAA,CAAAK,SAAA;IAj5CzrB7C,EAAE,CAAA8C,SAAA,CAi5CirB,CAAC;IAj5CprB9C,EAAE,CAAA+C,iBAAA,CAAAQ,UAi5CirB,CAAC;EAAA;AAAA;AAAA,SAAAyD,wCAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CprBvC,EAAE,CAAAyC,cAAA,kBAi5C2gB,CAAC,WAA4B,CAAC,OAAE,CAAC;IAj5C9iBzC,EAAE,CAAA0C,MAAA,aAi5CkjB,CAAC;IAj5CrjB1C,EAAE,CAAA2C,YAAA,CAi5CsjB,CAAC,CAAG,CAAC;IAj5C7jB3C,EAAE,CAAAyC,cAAA,YAi5C+lB,CAAC;IAj5ClmBzC,EAAE,CAAAiD,UAAA,IAAA8D,4CAAA,gBAi5CoqB,CAAC;IAj5CvqB/G,EAAE,CAAA2C,YAAA,CAi5C+rB,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5C/sBlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAi5CyoB,CAAC;IAj5C5oB9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAO,QAi5CyoB,CAAC;EAAA;AAAA;AAAA,SAAAwD,iDAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C5oBvC,EAAE,CAAAyC,cAAA,WAi5CgtD,CAAC;IAj5CntDzC,EAAE,CAAA0C,MAAA,8BAi5CwvD,CAAC;IAj5C3vD1C,EAAE,CAAA2C,YAAA,CAi5C4vD,CAAC;EAAA;AAAA;AAAA,SAAAuE,iDAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C/vDvC,EAAE,CAAAyC,cAAA,WAi5C02D,CAAC;IAj5C72DzC,EAAE,CAAA0C,MAAA,EAi5CujE,CAAC;IAj5C1jE1C,EAAE,CAAA2C,YAAA,CAi5C2jE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5C9jElD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,CAi5CujE,CAAC;IAj5C1jE9C,EAAE,CAAAqE,kBAAA,qCAAAnB,MAAA,CAAAoB,cAAA,iDAAApB,MAAA,CAAAoB,cAAA,uDAi5CujE,CAAC;EAAA;AAAA;AAAA,SAAA6C,6CAAA5E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C1jEvC,EAAE,CAAA6D,uBAAA,EAi5CioD,CAAC;IAj5CpoD7D,EAAE,CAAAiD,UAAA,IAAAgE,gDAAA,eAi5CgtD,CAAC,IAAAC,gDAAA,eAAyJ,CAAC;IAj5C72DlH,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAiE,WAAA,GAAFpH,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAi5C6sD,CAAC;IAj5ChtD9C,EAAE,CAAAoD,UAAA,SAAAgE,WAAA,CAAA/D,MAAA,kBAAA+D,WAAA,CAAA/D,MAAA,CAAAY,QAi5C6sD,CAAC;IAj5ChtDjE,EAAE,CAAA8C,SAAA,CAi5Cu2D,CAAC;IAj5C12D9C,EAAE,CAAAoD,UAAA,UAAAgE,WAAA,CAAA/D,MAAA,kBAAA+D,WAAA,CAAA/D,MAAA,CAAAoB,SAAA,MAAA2C,WAAA,CAAA/D,MAAA,kBAAA+D,WAAA,CAAA/D,MAAA,CAAAqB,SAAA,CAi5Cu2D,CAAC;EAAA;AAAA;AAAA,SAAA2C,iDAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C12DvC,EAAE,CAAAyC,cAAA,WAi5C+1F,CAAC;IAj5Cl2FzC,EAAE,CAAA0C,MAAA,0BAi5Cm4F,CAAC;IAj5Ct4F1C,EAAE,CAAA2C,YAAA,CAi5Cu4F,CAAC;EAAA;AAAA;AAAA,SAAA2E,iDAAA/E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C14FvC,EAAE,CAAAyC,cAAA,WAi5Ck9F,CAAC;IAj5Cr9FzC,EAAE,CAAA0C,MAAA,qCAi5CigG,CAAC;IAj5CpgG1C,EAAE,CAAA2C,YAAA,CAi5CqgG,CAAC;EAAA;AAAA;AAAA,SAAA4E,6CAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CxgGvC,EAAE,CAAA6D,uBAAA,EAi5CmxF,CAAC;IAj5CtxF7D,EAAE,CAAAiD,UAAA,IAAAoE,gDAAA,eAi5C+1F,CAAC,IAAAC,gDAAA,eAAkH,CAAC;IAj5Cr9FtH,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAqE,QAAA,GAAFxH,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAi5C41F,CAAC;IAj5C/1F9C,EAAE,CAAAoD,UAAA,SAAAoE,QAAA,CAAAnE,MAAA,kBAAAmE,QAAA,CAAAnE,MAAA,CAAAY,QAi5C41F,CAAC;IAj5C/1FjE,EAAE,CAAA8C,SAAA,CAi5C+8F,CAAC;IAj5Cl9F9C,EAAE,CAAAoD,UAAA,SAAAoE,QAAA,CAAAnE,MAAA,kBAAAmE,QAAA,CAAAnE,MAAA,CAAAa,OAi5C+8F,CAAC;EAAA;AAAA;AAAA,SAAAuD,iDAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cl9FvC,EAAE,CAAAyC,cAAA,WAi5Cu+H,CAAC;IAj5C1+HzC,EAAE,CAAA0C,MAAA,6BAi5C8gI,CAAC;IAj5CjhI1C,EAAE,CAAA2C,YAAA,CAi5CkhI,CAAC;EAAA;AAAA;AAAA,SAAA+E,iDAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CrhIvC,EAAE,CAAAyC,cAAA,WAi5CgoI,CAAC;IAj5CnoIzC,EAAE,CAAA0C,MAAA,EAi5C+0I,CAAC;IAj5Cl1I1C,EAAE,CAAA2C,YAAA,CAi5Cm1I,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5Ct1IlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,CAi5C+0I,CAAC;IAj5Cl1I9C,EAAE,CAAAqE,kBAAA,mCAAAnB,MAAA,CAAAoB,cAAA,iDAAApB,MAAA,CAAAoB,cAAA,uDAi5C+0I,CAAC;EAAA;AAAA;AAAA,SAAAqD,6CAAApF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cl1IvC,EAAE,CAAA6D,uBAAA,EAi5Cw5H,CAAC;IAj5C35H7D,EAAE,CAAAiD,UAAA,IAAAwE,gDAAA,eAi5Cu+H,CAAC,IAAAC,gDAAA,eAAwJ,CAAC;IAj5CnoI1H,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAyE,WAAA,GAAF5H,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAi5Co+H,CAAC;IAj5Cv+H9C,EAAE,CAAAoD,UAAA,SAAAwE,WAAA,CAAAvE,MAAA,kBAAAuE,WAAA,CAAAvE,MAAA,CAAAY,QAi5Co+H,CAAC;IAj5Cv+HjE,EAAE,CAAA8C,SAAA,CAi5C6nI,CAAC;IAj5ChoI9C,EAAE,CAAAoD,UAAA,UAAAwE,WAAA,CAAAvE,MAAA,kBAAAuE,WAAA,CAAAvE,MAAA,CAAAoB,SAAA,MAAAmD,WAAA,CAAAvE,MAAA,kBAAAuE,WAAA,CAAAvE,MAAA,CAAAqB,SAAA,CAi5C6nI,CAAC;EAAA;AAAA;AAAA,SAAAmD,iDAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5ChoIvC,EAAE,CAAAyC,cAAA,WAi5C6rK,CAAC;IAj5ChsKzC,EAAE,CAAA0C,MAAA,0CAi5CivK,CAAC;IAj5CpvK1C,EAAE,CAAA2C,YAAA,CAi5CqvK,CAAC;EAAA;AAAA;AAAA,SAAAmF,iDAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CxvKvC,EAAE,CAAAyC,cAAA,WAi5Cq2K,CAAC;IAj5Cx2KzC,EAAE,CAAA0C,MAAA,qDAi5Co6K,CAAC;IAj5Cv6K1C,EAAE,CAAA2C,YAAA,CAi5Cw6K,CAAC;EAAA;AAAA;AAAA,SAAAoF,6CAAAxF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C36KvC,EAAE,CAAA6D,uBAAA,EAi5CgnK,CAAC;IAj5CnnK7D,EAAE,CAAAiD,UAAA,IAAA4E,gDAAA,eAi5C6rK,CAAC,IAAAC,gDAAA,eAAuK,CAAC;IAj5Cx2K9H,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAyE,WAAA,GAAF5H,EAAE,CAAAgE,WAAA;IAAA,MAAAgE,SAAA,GAAFhI,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAi5C0rK,CAAC;IAj5C7rK9C,EAAE,CAAAoD,UAAA,SAAA4E,SAAA,CAAA3E,MAAA,kBAAA2E,SAAA,CAAA3E,MAAA,CAAAY,QAi5C0rK,CAAC;IAj5C7rKjE,EAAE,CAAA8C,SAAA,CAi5Ck2K,CAAC;IAj5Cr2K9C,EAAE,CAAAoD,UAAA,SAAAwE,WAAA,CAAAK,KAAA,IAAAD,SAAA,CAAAC,KAAA,MAAAD,SAAA,CAAA3E,MAAA,kBAAA2E,SAAA,CAAA3E,MAAA,CAAAY,QAAA,CAi5Ck2K,CAAC;EAAA;AAAA;AAAA,SAAAiE,oCAAA3F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4F,GAAA,GAj5Cr2KnI,EAAE,CAAA6E,gBAAA;IAAF7E,EAAE,CAAAyC,cAAA,aAi5C6iL,CAAC,qBAAkH,CAAC;IAj5CnqLzC,EAAE,CAAA8E,gBAAA,2BAAAsD,yEAAApD,MAAA;MAAFhF,EAAE,CAAAiF,aAAA,CAAAkD,GAAA;MAAA,MAAAjF,MAAA,GAAFlD,EAAE,CAAAmD,aAAA;MAAFnD,EAAE,CAAAkF,kBAAA,CAAAhC,MAAA,CAAAiC,IAAA,CAAAkD,KAAA,EAAArD,MAAA,MAAA9B,MAAA,CAAAiC,IAAA,CAAAkD,KAAA,GAAArD,MAAA;MAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;IAAA,CAi5CymL,CAAC;IAj5C5mLhF,EAAE,CAAA0C,MAAA,gBAi5CirL,CAAC;IAj5CprL1C,EAAE,CAAAyC,cAAA,WAi5CitL,CAAC,YAAO,CAAC;IAj5C5tLzC,EAAE,CAAA0C,MAAA,wBAi5C2uL,CAAC;IAj5C9uL1C,EAAE,CAAA2C,YAAA,CAi5CovL,CAAC,CAAG,CAAC,CAAmB,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5CzxLlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,CAi5CymL,CAAC;IAj5C5mL9C,EAAE,CAAAsF,gBAAA,YAAApC,MAAA,CAAAiC,IAAA,CAAAkD,KAi5CymL,CAAC;IAj5C5mLrI,EAAE,CAAAoD,UAAA,aAAAF,MAAA,CAAAoB,cAAA,wBAi5C+pL,CAAC;EAAA;AAAA;AAAA,SAAAgE,qEAAA/F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5ClqLvC,EAAE,CAAAwF,SAAA,iBAi5CwgN,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAgG,cAAA,GAj5C3gNvI,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAA7C,IAi5C6/M,CAAC;EAAA;AAAA;AAAA,SAAA8C,yEAAAjG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5ChgNvC,EAAE,CAAA0C,MAAA,EAi5C4jN,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAgG,cAAA,GAj5C/jNvI,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAA+C,iBAAA,CAAAwF,cAAA,CAAA3C,KAi5C4jN,CAAC;EAAA;AAAA;AAAA,SAAA6C,2DAAAlG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C/jNvC,EAAE,CAAAyC,cAAA,WAi5C06M,CAAC;IAj5C76MzC,EAAE,CAAAiD,UAAA,IAAAqF,oEAAA,qBAi5C8/M,CAAC,IAAAE,wEAAA,gCAj5CjgNxI,EAAE,CAAA8F,sBAi5CsiN,CAAC;IAj5CziN9F,EAAE,CAAA2C,YAAA,CAi5CslN,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAmG,SAAA,GAj5CzlN1I,EAAE,CAAAgE,WAAA;IAAA,MAAAuE,cAAA,GAAFvI,EAAE,CAAAmD,aAAA,GAAAN,SAAA;IAAF7C,EAAE,CAAAgG,WAAA,cAAAuC,cAAA,CAAA7C,IAi5Cy6M,CAAC;IAj5C56M1F,EAAE,CAAAoD,UAAA,eAAAmF,cAAA,CAAAtC,IAi5CgyM,CAAC;IAj5CnyMjG,EAAE,CAAAkG,WAAA,WAAAqC,cAAA,CAAApC,MAAA,WAAAoC,cAAA,CAAA7C,IAAA;IAAF1F,EAAE,CAAA8C,SAAA,CAi5Cs9M,CAAC;IAj5Cz9M9C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAA7C,IAi5Cs9M,CAAC,aAAAgD,SAAS,CAAC;EAAA;AAAA;AAAA,SAAAC,qEAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cn+MvC,EAAE,CAAAwF,SAAA,iBAi5C04N,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAgG,cAAA,GAj5C74NvI,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAA7C,IAi5C+3N,CAAC;EAAA;AAAA;AAAA,SAAAkD,yEAAArG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cl4NvC,EAAE,CAAA0C,MAAA,EAi5C87N,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAgG,cAAA,GAj5Cj8NvI,EAAE,CAAAmD,aAAA,IAAAN,SAAA;IAAF7C,EAAE,CAAA+C,iBAAA,CAAAwF,cAAA,CAAA3C,KAi5C87N,CAAC;EAAA;AAAA;AAAA,SAAAiD,2DAAAtG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cj8NvC,EAAE,CAAAyC,cAAA,OAi5C4yN,CAAC;IAj5C/yNzC,EAAE,CAAAiD,UAAA,IAAA0F,oEAAA,qBAi5Cg4N,CAAC,IAAAC,wEAAA,gCAj5Cn4N5I,EAAE,CAAA8F,sBAi5Cw6N,CAAC;IAj5C36N9F,EAAE,CAAA2C,YAAA,CAi5Cw9N,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAuG,SAAA,GAj5C39N9I,EAAE,CAAAgE,WAAA;IAAA,MAAAuE,cAAA,GAAFvI,EAAE,CAAAmD,aAAA,GAAAN,SAAA;IAAF7C,EAAE,CAAAgG,WAAA,cAAAuC,cAAA,CAAA7C,IAi5C2yN,CAAC;IAj5C9yN1F,EAAE,CAAAkG,WAAA,SAAAqC,cAAA,CAAA/B,GAAA,EAAFxG,EAAE,CAAAyG,aAAA,YAAA8B,cAAA,CAAApC,MAAA,WAAAoC,cAAA,CAAA7C,IAAA;IAAF1F,EAAE,CAAA8C,SAAA,CAi5Cw1N,CAAC;IAj5C31N9C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAA7C,IAi5Cw1N,CAAC,aAAAoD,SAAS,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5Cr2NvC,EAAE,CAAA6D,uBAAA,EAi5CitM,CAAC;IAj5CptM7D,EAAE,CAAAiD,UAAA,IAAAwF,0DAAA,eAi5C06M,CAAC,IAAAI,0DAAA,eAAiY,CAAC;IAj5C/yN7I,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAA,MAAAgG,cAAA,GAAA/F,GAAA,CAAAK,SAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAi5CmvM,CAAC;IAj5CtvM9C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAAtC,IAi5CmvM,CAAC;IAj5CtvMjG,EAAE,CAAA8C,SAAA,CAi5CunN,CAAC;IAj5C1nN9C,EAAE,CAAAoD,UAAA,SAAAmF,cAAA,CAAA/B,GAi5CunN,CAAC;EAAA;AAAA;AAAA,SAAAwC,wCAAAzG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5C1nNvC,EAAE,CAAAyC,cAAA,iBAi5CumM,CAAC;IAj5C1mMzC,EAAE,CAAA0C,MAAA,sBAi5C6nM,CAAC;IAj5ChoM1C,EAAE,CAAAyC,cAAA,aAi5CopM,CAAC;IAj5CvpMzC,EAAE,CAAAiD,UAAA,IAAA8F,sDAAA,0BAi5CitM,CAAC;IAj5CptM/I,EAAE,CAAA2C,YAAA,CAi5Cu/N,CAAC,CAAW,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAj5CtgOlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAi5C8sM,CAAC;IAj5CjtM9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAA0D,WAi5C8sM,CAAC;EAAA;AAAA;AAAA,SAAAqC,oDAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj5CjtMvC,EAAE,CAAAyC,cAAA,YAm/Cqf,CAAC;IAn/CxfzC,EAAE,CAAA0C,MAAA,EAm/CggB,CAAC;IAn/CngB1C,EAAE,CAAA2C,YAAA,CAm/CqgB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAK,SAAA;IAn/CxgB7C,EAAE,CAAA8C,SAAA,CAm/CggB,CAAC;IAn/CngB9C,EAAE,CAAA+C,iBAAA,CAAAH,QAm/CggB,CAAC;EAAA;AAAA;AAAA,SAAAsG,+CAAA3G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/CngBvC,EAAE,CAAAyC,cAAA,kBAm/C+V,CAAC,WAA4B,CAAC,OAAE,CAAC;IAn/ClYzC,EAAE,CAAA0C,MAAA,cAm/CuY,CAAC;IAn/C1Y1C,EAAE,CAAA2C,YAAA,CAm/C2Y,CAAC,CAAG,CAAC;IAn/ClZ3C,EAAE,CAAAyC,cAAA,YAm/Cob,CAAC;IAn/CvbzC,EAAE,CAAAiD,UAAA,IAAAgG,mDAAA,gBAm/Cqf,CAAC;IAn/CxfjJ,EAAE,CAAA2C,YAAA,CAm/C8gB,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAn/C9hBlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAm/C0d,CAAC;IAn/C7d9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAG,MAm/C0d,CAAC;EAAA;AAAA;AAAA,SAAA8F,oDAAA5G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/C7dvC,EAAE,CAAAyC,cAAA,YAm/CsyB,CAAC;IAn/CzyBzC,EAAE,CAAA0C,MAAA,EAm/CmzB,CAAC;IAn/CtzB1C,EAAE,CAAA2C,YAAA,CAm/CwzB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,UAAA,GAAAf,GAAA,CAAAK,SAAA;IAn/C3zB7C,EAAE,CAAA8C,SAAA,CAm/CmzB,CAAC;IAn/CtzB9C,EAAE,CAAA+C,iBAAA,CAAAQ,UAm/CmzB,CAAC;EAAA;AAAA;AAAA,SAAA6F,+CAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/CtzBvC,EAAE,CAAAyC,cAAA,kBAm/C6oB,CAAC,WAA4B,CAAC,OAAE,CAAC;IAn/ChrBzC,EAAE,CAAA0C,MAAA,aAm/CorB,CAAC;IAn/CvrB1C,EAAE,CAAA2C,YAAA,CAm/CwrB,CAAC,CAAG,CAAC;IAn/C/rB3C,EAAE,CAAAyC,cAAA,YAm/CiuB,CAAC;IAn/CpuBzC,EAAE,CAAAiD,UAAA,IAAAkG,mDAAA,gBAm/CsyB,CAAC;IAn/CzyBnJ,EAAE,CAAA2C,YAAA,CAm/Ci0B,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GAn/Cj1BlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EAm/C2wB,CAAC;IAn/C9wB9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAO,QAm/C2wB,CAAC;EAAA;AAAA;AAAA,SAAA4F,wDAAA9G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/C9wBvC,EAAE,CAAAyC,cAAA,WAm/CmtD,CAAC;IAn/CttDzC,EAAE,CAAA0C,MAAA,0BAm/CuvD,CAAC;IAn/C1vD1C,EAAE,CAAA2C,YAAA,CAm/C2vD,CAAC;EAAA;AAAA;AAAA,SAAA2G,wDAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/C9vDvC,EAAE,CAAAyC,cAAA,WAm/Cs0D,CAAC;IAn/Cz0DzC,EAAE,CAAA0C,MAAA,qCAm/Cq3D,CAAC;IAn/Cx3D1C,EAAE,CAAA2C,YAAA,CAm/Cy3D,CAAC;EAAA;AAAA;AAAA,SAAA4G,oDAAAhH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/C53DvC,EAAE,CAAA6D,uBAAA,EAm/CuoD,CAAC;IAn/C1oD7D,EAAE,CAAAiD,UAAA,IAAAoG,uDAAA,eAm/CmtD,CAAC,IAAAC,uDAAA,eAAkH,CAAC;IAn/Cz0DtJ,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAAY,QAAA,GAAF/D,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CAm/CgtD,CAAC;IAn/CntD9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAV,MAAA,kBAAAU,QAAA,CAAAV,MAAA,CAAAY,QAm/CgtD,CAAC;IAn/CntDjE,EAAE,CAAA8C,SAAA,CAm/Cm0D,CAAC;IAn/Ct0D9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAV,MAAA,kBAAAU,QAAA,CAAAV,MAAA,CAAAa,OAm/Cm0D,CAAC;EAAA;AAAA;AAAA,SAAAsF,kDAAAjH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn/Ct0DvC,EAAE,CAAAyC,cAAA,YA2iDgc,CAAC;IA3iDnczC,EAAE,CAAA0C,MAAA,EA2iD2c,CAAC;IA3iD9c1C,EAAE,CAAA2C,YAAA,CA2iDgd,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,QAAA,GAAAJ,GAAA,CAAAK,SAAA;IA3iDnd7C,EAAE,CAAA8C,SAAA,CA2iD2c,CAAC;IA3iD9c9C,EAAE,CAAA+C,iBAAA,CAAAH,QA2iD2c,CAAC;EAAA;AAAA;AAAA,SAAA6G,6CAAAlH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iD9cvC,EAAE,CAAAyC,cAAA,kBA2iD0S,CAAC,WAA4B,CAAC,OAAE,CAAC;IA3iD7UzC,EAAE,CAAA0C,MAAA,cA2iDkV,CAAC;IA3iDrV1C,EAAE,CAAA2C,YAAA,CA2iDsV,CAAC,CAAG,CAAC;IA3iD7V3C,EAAE,CAAAyC,cAAA,YA2iD+X,CAAC;IA3iDlYzC,EAAE,CAAAiD,UAAA,IAAAuG,iDAAA,gBA2iDgc,CAAC;IA3iDncxJ,EAAE,CAAA2C,YAAA,CA2iDyd,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GA3iDzelD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EA2iDqa,CAAC;IA3iDxa9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAG,MA2iDqa,CAAC;EAAA;AAAA;AAAA,SAAAqG,kDAAAnH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDxavC,EAAE,CAAAyC,cAAA,YA2iDivB,CAAC;IA3iDpvBzC,EAAE,CAAA0C,MAAA,EA2iD8vB,CAAC;IA3iDjwB1C,EAAE,CAAA2C,YAAA,CA2iDmwB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAgB,UAAA,GAAAf,GAAA,CAAAK,SAAA;IA3iDtwB7C,EAAE,CAAA8C,SAAA,CA2iD8vB,CAAC;IA3iDjwB9C,EAAE,CAAA+C,iBAAA,CAAAQ,UA2iD8vB,CAAC;EAAA;AAAA;AAAA,SAAAoG,6CAAApH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDjwBvC,EAAE,CAAAyC,cAAA,kBA2iDwlB,CAAC,WAA4B,CAAC,OAAE,CAAC;IA3iD3nBzC,EAAE,CAAA0C,MAAA,aA2iD+nB,CAAC;IA3iDloB1C,EAAE,CAAA2C,YAAA,CA2iDmoB,CAAC,CAAG,CAAC;IA3iD1oB3C,EAAE,CAAAyC,cAAA,YA2iD4qB,CAAC;IA3iD/qBzC,EAAE,CAAAiD,UAAA,IAAAyG,iDAAA,gBA2iDivB,CAAC;IA3iDpvB1J,EAAE,CAAA2C,YAAA,CA2iD4wB,CAAC,CAAY,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GA3iD5xBlD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,EA2iDstB,CAAC;IA3iDztB9C,EAAE,CAAAoD,UAAA,YAAAF,MAAA,CAAAO,QA2iDstB,CAAC;EAAA;AAAA;AAAA,SAAAmG,sDAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDztBvC,EAAE,CAAAyC,cAAA,WA2iD+2D,CAAC;IA3iDl3DzC,EAAE,CAAA0C,MAAA,6BA2iDs5D,CAAC;IA3iDz5D1C,EAAE,CAAA2C,YAAA,CA2iD05D,CAAC;EAAA;AAAA;AAAA,SAAAkH,sDAAAtH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iD75DvC,EAAE,CAAAyC,cAAA,WA2iDwgE,CAAC;IA3iD3gEzC,EAAE,CAAA0C,MAAA,EA2iDotE,CAAC;IA3iDvtE1C,EAAE,CAAA2C,YAAA,CA2iDwtE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAW,MAAA,GA3iD3tElD,EAAE,CAAAmD,aAAA;IAAFnD,EAAE,CAAA8C,SAAA,CA2iDotE,CAAC;IA3iDvtE9C,EAAE,CAAAqE,kBAAA,oCAAAnB,MAAA,CAAAoB,cAAA,iDAAApB,MAAA,CAAAoB,cAAA,uDA2iDotE,CAAC;EAAA;AAAA;AAAA,SAAAwF,kDAAAvH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDvtEvC,EAAE,CAAA6D,uBAAA,EA2iDgyD,CAAC;IA3iDnyD7D,EAAE,CAAAiD,UAAA,IAAA2G,qDAAA,eA2iD+2D,CAAC,IAAAC,qDAAA,eAAwJ,CAAC;IA3iD3gE7J,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAA4G,WAAA,GAAF/J,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CA2iD42D,CAAC;IA3iD/2D9C,EAAE,CAAAoD,UAAA,SAAA2G,WAAA,CAAA1G,MAAA,kBAAA0G,WAAA,CAAA1G,MAAA,CAAAY,QA2iD42D,CAAC;IA3iD/2DjE,EAAE,CAAA8C,SAAA,CA2iDqgE,CAAC;IA3iDxgE9C,EAAE,CAAAoD,UAAA,UAAA2G,WAAA,CAAA1G,MAAA,kBAAA0G,WAAA,CAAA1G,MAAA,CAAAoB,SAAA,MAAAsF,WAAA,CAAA1G,MAAA,kBAAA0G,WAAA,CAAA1G,MAAA,CAAAqB,SAAA,CA2iDqgE,CAAC;EAAA;AAAA;AAAA,SAAAsF,sDAAAzH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDxgEvC,EAAE,CAAAyC,cAAA,WA2iDunG,CAAC;IA3iD1nGzC,EAAE,CAAA0C,MAAA,0CA2iD2qG,CAAC;IA3iD9qG1C,EAAE,CAAA2C,YAAA,CA2iD+qG,CAAC;EAAA;AAAA;AAAA,SAAAsH,sDAAA1H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDlrGvC,EAAE,CAAAyC,cAAA,WA2iD+xG,CAAC;IA3iDlyGzC,EAAE,CAAA0C,MAAA,qDA2iD81G,CAAC;IA3iDj2G1C,EAAE,CAAA2C,YAAA,CA2iDk2G,CAAC;EAAA;AAAA;AAAA,SAAAuH,kDAAA3H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3iDr2GvC,EAAE,CAAA6D,uBAAA,EA2iDwhG,CAAC;IA3iD3hG7D,EAAE,CAAAiD,UAAA,IAAA+G,qDAAA,eA2iDunG,CAAC,IAAAC,qDAAA,eAAuK,CAAC;IA3iDlyGjK,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAAvB,EAAA;IAAFvC,EAAE,CAAAmD,aAAA;IAAA,MAAA4G,WAAA,GAAF/J,EAAE,CAAAgE,WAAA;IAAA,MAAAmG,SAAA,GAAFnK,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,SAAA,CA2iDonG,CAAC;IA3iDvnG9C,EAAE,CAAAoD,UAAA,SAAA+G,SAAA,CAAAC,OAAA,KAAAD,SAAA,CAAA9G,MAAA,kBAAA8G,SAAA,CAAA9G,MAAA,CAAAY,QAAA,CA2iDonG,CAAC;IA3iDvnGjE,EAAE,CAAA8C,SAAA,CA2iD4xG,CAAC;IA3iD/xG9C,EAAE,CAAAoD,UAAA,SAAA2G,WAAA,CAAA9B,KAAA,IAAAkC,SAAA,CAAAlC,KAAA,MAAAkC,SAAA,CAAA9G,MAAA,kBAAA8G,SAAA,CAAA9G,MAAA,CAAAY,QAAA,CA2iD4xG,CAAC;EAAA;AAAA;AAAA,MAAAoG,GAAA;AA9mEn4G,MAAMzD,WAAW,GAAG,EAAE;AACtB,MAAM0D,kBAAkB,GAAG;EACvBC,UAAU,EAAE,EAAE;EACdC,KAAK,EAAE;IACHC,KAAK,EAAE;MACHC,aAAa,EAAE,GAAG;MAAE;MACpBC,QAAQ,EAAE,OAAO;MAAE;MACnBvF,UAAU,EAAE,IAAI;MAAE;MAClBwF,YAAY,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACX,CAAC;MACDlE,WAAW,EAAEA,WAAW,CAAE;IAC9B,CAAC;IACDmE,QAAQ,EAAE;MACNL,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE,OAAO;MACjBC,YAAY,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACX,CAAC;MACDzC,KAAK,EAAE,IAAI;MACXzB,WAAW,EAAEA;IACjB,CAAC;IACDoE,eAAe,EAAE;MACbN,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE,OAAO;MACjBC,YAAY,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACX,CAAC;MACDlE,WAAW,EAAEA;IACjB,CAAC;IACDqE,aAAa,EAAE;MACXP,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE,OAAO;MACjBC,YAAY,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,KAAK,EAAE;MACX,CAAC;MACDlE,WAAW,EAAEA;IACjB,CAAC;IACDsE,MAAM,EAAE;MACJR,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE;IACd,CAAC;IACDQ,UAAU,EAAE;MACRC,QAAQ,EAAE;QACNnH,QAAQ,EAAE,IAAI;QACdoH,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MACf,CAAC;MACDC,KAAK,EAAE;QACHtH,QAAQ,EAAE;MACd,CAAC;MACDuH,QAAQ,EAAE;QACNvH,QAAQ,EAAE,KAAK;QACfoH,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MACf;IACJ;EACJ;AACJ,CAAC;AACD,MAAMG,eAAe,GAAG,IAAIxL,cAAc,CAAC,sBAAsB,CAAC;AAClE,MAAMyL,oBAAoB,GAAG,IAAIzL,cAAc,CAAC,2BAA2B,CAAC;AAC5E,MAAM0L,kBAAkB,GAAG,IAAI1L,cAAc,CAAC,yBAAyB,CAAC;AACxE,MAAM2L,cAAc,GAAG,IAAI3L,cAAc,CAAC,qBAAqB,CAAC;AAChE,MAAM4L,0BAA0B,GAAG,IAAI5L,cAAc,CAAC,mCAAmC,CAAC;AAC1F,MAAM6L,gCAAgC,GAAG,IAAI7L,cAAc,CAAC,4BAA4B,CAAC;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8L,UAAU,GAAG,SAAAA,CAAU,GAAGC,OAAO,EAAE;EACrC,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC1D,OAAO,KAAK;EAChB;EACA,IAAIA,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACtB,OAAOD,SAAS,CAAC,CAAC,CAAC;EACvB;EACA,MAAM9F,MAAM,GAAG8F,SAAS,CAAC,CAAC,CAAC;EAC3B;EACA,MAAME,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACN,SAAS,EAAE,CAAC,CAAC;EACrD,IAAIO,GAAG,EAAEC,GAAG;EACZN,IAAI,CAACO,OAAO,CAAC,UAAUC,GAAG,EAAE;IACxB;IACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIP,KAAK,CAACQ,OAAO,CAACD,GAAG,CAAC,EAAE;MAC/C;IACJ;IACAE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAACD,OAAO,CAAC,UAAUK,GAAG,EAAE;MACpCN,GAAG,GAAGtG,MAAM,CAAC4G,GAAG,CAAC,CAAC,CAAC;MACnBP,GAAG,GAAGG,GAAG,CAACI,GAAG,CAAC,CAAC,CAAC;MAChB;MACA,IAAIP,GAAG,KAAKrG,MAAM,EAAE;QAChB;QACA;AAChB;AACA;AACA;MACY,CAAC,MACI,IAAI,OAAOqG,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;QAC9CrG,MAAM,CAAC4G,GAAG,CAAC,GAAGP,GAAG;QACjB;QACA;MACJ,CAAC,MACI,IAAIJ,KAAK,CAACQ,OAAO,CAACJ,GAAG,CAAC,EAAE;QACzBrG,MAAM,CAAC4G,GAAG,CAAC,GAAGC,cAAc,CAACR,GAAG,CAAC;QACjC;QACA;MACJ,CAAC,MACI,IAAIS,eAAe,CAACT,GAAG,CAAC,EAAE;QAC3BrG,MAAM,CAAC4G,GAAG,CAAC,GAAGG,kBAAkB,CAACV,GAAG,CAAC;QACrC;QACA;MACJ,CAAC,MACI,IAAI,OAAOC,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIL,KAAK,CAACQ,OAAO,CAACH,GAAG,CAAC,EAAE;QACpEtG,MAAM,CAAC4G,GAAG,CAAC,GAAGhB,UAAU,CAAC,CAAC,CAAC,EAAES,GAAG,CAAC;QACjC;QACA;MACJ,CAAC,MACI;QACDrG,MAAM,CAAC4G,GAAG,CAAC,GAAGhB,UAAU,CAACU,GAAG,EAAED,GAAG,CAAC;QAClC;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOrG,MAAM;AACjB,CAAC;AACD,SAAS8G,eAAeA,CAACT,GAAG,EAAE;EAC1B,OAAQA,GAAG,YAAYW,IAAI,IACpBX,GAAG,YAAYY,MAAM,GAAI,IAAI,GAAG,KAAK;AAChD;AACA,SAASF,kBAAkBA,CAACV,GAAG,EAAE;EAC7B,IAAIA,GAAG,YAAYW,IAAI,EAAE;IACrB,OAAO,IAAIA,IAAI,CAACX,GAAG,CAACa,OAAO,CAAC,CAAC,CAAC;EAClC,CAAC,MACI,IAAIb,GAAG,YAAYY,MAAM,EAAE;IAC5B,OAAO,IAAIA,MAAM,CAACZ,GAAG,CAAC;EAC1B,CAAC,MACI;IACD,MAAM,IAAIc,KAAK,CAAC,0CAA0C,CAAC;EAC/D;AACJ;AACA;AACA;AACA;AACA,SAASN,cAAcA,CAACO,GAAG,EAAE;EACzB,MAAMC,KAAK,GAAG,EAAE;EAChBD,GAAG,CAACb,OAAO,CAAC,UAAUe,IAAI,EAAEC,KAAK,EAAE;IAC/B,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC3C,IAAIrB,KAAK,CAACQ,OAAO,CAACa,IAAI,CAAC,EAAE;QACrBD,KAAK,CAACE,KAAK,CAAC,GAAGV,cAAc,CAACS,IAAI,CAAC;MACvC,CAAC,MACI,IAAIR,eAAe,CAACQ,IAAI,CAAC,EAAE;QAC5BD,KAAK,CAACE,KAAK,CAAC,GAAGR,kBAAkB,CAACO,IAAI,CAAC;MAC3C,CAAC,MACI;QACDD,KAAK,CAACE,KAAK,CAAC,GAAG3B,UAAU,CAAC,CAAC,CAAC,EAAE0B,IAAI,CAAC;MACvC;IACJ,CAAC,MACI;MACDD,KAAK,CAACE,KAAK,CAAC,GAAGD,IAAI;IACvB;EACJ,CAAC,CAAC;EACF,OAAOD,KAAK;AAChB;AACA;AACA,SAASG,iBAAiBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACxD,MAAMhB,IAAI,GAAGe,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC5B;EACA,IAAIC,KAAK,GAAGjC,UAAU,CAAC,CAAC,CAAC,EAAE6B,MAAM,IAAI,CAAC,CAAC,CAAC;EACxCd,IAAI,CAACJ,OAAO,CAAEuB,CAAC,IAAK;IAChB,IAAID,KAAK,IAAI,OAAOA,KAAK,CAACC,CAAC,CAAC,KAAK,WAAW,EAAE;MAC1CD,KAAK,GAAGA,KAAK,CAACC,CAAC,CAAC;IACpB,CAAC,MACI;MACDD,KAAK,GAAGE,SAAS;IACrB;EACJ,CAAC,CAAC;EACF,OAAO,OAAOF,KAAK,KAAK,WAAW,GAAGF,YAAY,GAAGE,KAAK;AAC9D;AACA,SAASG,eAAeA,CAACC,GAAG,EAAE;EAC1B,IAAIC,MAAM,GAAGD,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACtD,QAAQD,MAAM,CAACnC,MAAM,GAAG,CAAC;IACrB,KAAK,CAAC;MAAE;QACJ;MACJ;IACA,KAAK,CAAC;MAAE;QACJmC,MAAM,IAAI,IAAI;QACd;MACJ;IACA,KAAK,CAAC;MAAE;QACJA,MAAM,IAAI,GAAG;QACb;MACJ;IACA;MAAS;QACL,MAAM,IAAIf,KAAK,CAAC,2BAA2B,CAAC;MAChD;EACJ;EACA,OAAOiB,gBAAgB,CAACF,MAAM,CAAC;AACnC;AACA,SAASG,SAASA,CAACJ,GAAG,EAAE;EACpB,MAAMK,KAAK,GAAG,mEAAmE;EACjF,IAAIJ,MAAM,GAAG,EAAE;EACfD,GAAG,GAAGM,MAAM,CAACN,GAAG,CAAC,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACpC,IAAIF,GAAG,CAAClC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACtB,MAAM,IAAIoB,KAAK,CAAC,mEAAmE,CAAC;EACxF;EACA;EACA;EACA,IAAIqB,EAAE,GAAG,CAAC,EAAEC,EAAE,EAAEC,MAAM,EAAEC,GAAG,GAAG,CAAC;EAC/B;EACAD,MAAM,GAAGT,GAAG,CAACW,MAAM,CAACD,GAAG,EAAE,CAAC;EAC1B;EACA,CAACD,MAAM,KAAKD,EAAE,GAAGD,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAG,EAAE,GAAGC,MAAM,GAAGA,MAAM;EAC/C;EACA;EACAF,EAAE,EAAE,GAAG,CAAC,CAAC,GAAGN,MAAM,IAAIK,MAAM,CAACM,YAAY,CAAC,GAAG,GAAGJ,EAAE,KAAK,CAAC,CAAC,GAAGD,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IAC1E;IACAE,MAAM,GAAGJ,KAAK,CAACQ,OAAO,CAACJ,MAAM,CAAC;EAClC;EACA,OAAOR,MAAM;AACjB;AACA;AACA,SAASE,gBAAgBA,CAACH,GAAG,EAAE;EAC3B,OAAOc,kBAAkB,CAAC9C,KAAK,CAACC,SAAS,CAACzK,GAAG,CAAC2K,IAAI,CAACiC,SAAS,CAACJ,GAAG,CAAC,EAAGe,CAAC,IAAK;IACtE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAE/C,KAAK,CAAC,CAAC,CAAC,CAAC;EAChE,CAAC,CAAC,CAACgD,IAAI,CAAC,EAAE,CAAC,CAAC;AAChB;AAEA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACF,WAAW,CAACG,IAAI;EAChC;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,OAAO;EACvB;AACJ;AACA,MAAMI,wBAAwB,SAASvC,KAAK,CAAC;EACzCkC,WAAWA,CAACM,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACdjD,MAAM,CAACkD,cAAc,CAAC,IAAI,EAAEC,GAAG,CAAC7J,MAAM,CAACkG,SAAS,CAAC;EACrD;AACJ;AACA,MAAM4D,uBAAuB,SAAS3C,KAAK,CAAC;EACxCkC,WAAWA,CAACM,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACdjD,MAAM,CAACkD,cAAc,CAAC,IAAI,EAAEC,GAAG,CAAC7J,MAAM,CAACkG,SAAS,CAAC;EACrD;AACJ;AACA,MAAM6D,qBAAqB,SAASD,uBAAuB,CAAC;EACxDT,WAAWA,CAACM,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACdjD,MAAM,CAACkD,cAAc,CAAC,IAAI,EAAEC,GAAG,CAAC7J,MAAM,CAACkG,SAAS,CAAC;EACrD;AACJ;AACA,MAAM8D,0BAA0B,SAASF,uBAAuB,CAAC;EAC7DT,WAAWA,CAACM,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACdjD,MAAM,CAACkD,cAAc,CAAC,IAAI,EAAEC,GAAG,CAAC7J,MAAM,CAACkG,SAAS,CAAC;EACrD;AACJ;AACA,SAAS+D,iBAAiBA,CAACC,UAAU,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;EACxE,OAAO,IAAIH,UAAU,CAACC,KAAK,EAAEC,iBAAiB,EAAEC,SAAS,CAAC;AAC9D;AACA,SAASC,gBAAgBA,CAAChB,OAAO,EAAE;EAC/B,IAAIA,OAAO,CAACvD,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM,IAAIgE,qBAAqB,CAAC,uCAAuC,CAAC;EAC5E;EACA,MAAMQ,KAAK,GAAGjB,OAAO,CAAC1B,KAAK,CAAC,GAAG,CAAC;EAChC,IAAI2C,KAAK,CAACxE,MAAM,KAAK,CAAC,EAAE;IACpB,MAAM,IAAIiE,0BAA0B,CAAC,eAAeV,OAAO,4DAA4D,CAAC;EAC5H;EACA,IAAIkB,OAAO;EACX,IAAI;IACAA,OAAO,GAAGxC,eAAe,CAACuC,KAAK,CAAC,CAAC,CAAC,CAAC;EACvC,CAAC,CACD,OAAOE,CAAC,EAAE;IACN,MAAM,IAAIT,0BAA0B,CAAC,eAAeV,OAAO,iDAAiD,CAAC;EACjH;EACA,IAAI,CAACkB,OAAO,EAAE;IACV,MAAM,IAAIR,0BAA0B,CAAC,eAAeV,OAAO,kDAAkD,CAAC;EAClH;EACA,OAAOoB,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;AAC9B;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,SAASxB,WAAW,CAAC;EACxC;IAAS,IAAI,CAACI,IAAI,GAAG,sBAAsB;EAAE;EAC7CH,WAAWA,CAACc,KAAK,EAAEC,iBAAiB,EAAEC,SAAS,EAAE;IAC7C,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI;MACA,IAAI,CAACQ,YAAY,CAAC,CAAC;IACvB,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,IAAI,EAAEA,GAAG,YAAYpB,wBAAwB,CAAC,EAAE;QAC5C;QACA,MAAMoB,GAAG;MACb;IACJ;IACA,IAAI,CAACT,SAAS,GAAG,IAAI,CAACU,gBAAgB,CAACV,SAAS,CAAC;EACrD;EACAQ,YAAYA,CAAA,EAAG;IACX,IAAI,CAACvB,OAAO,GAAG,IAAI;EACvB;EACAyB,gBAAgBA,CAACC,IAAI,EAAE;IACnB,OAAOA,IAAI,GAAGA,IAAI,GAAG,IAAIhE,IAAI,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;EACIiE,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACZ,SAAS;EACzB;EACA;AACJ;AACA;AACA;EACIa,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,KAAK;EACrB;EACAgB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACf,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACIgB,OAAOA,CAAA,EAAG;IACN,OAAO,CAAC,CAAC,IAAI,CAACF,QAAQ,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIhC,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC,IAAI,CAACiB,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,EAAE;EACzC;AACJ;AACA;AACA;AACA;AACA,MAAMkB,cAAc,SAAST,iBAAiB,CAAC;EAC3C;IAAS,IAAI,CAACpB,IAAI,GAAG,mBAAmB;EAAE;EAC1C;AACJ;AACA;EACIuB,gBAAgBA,CAACC,IAAI,EAAE;IACnB,MAAMR,OAAO,GAAG,IAAI,CAACf,UAAU,CAAC,CAAC;IACjC,OAAOe,OAAO,IAAIA,OAAO,CAACc,GAAG,GAAG,IAAItE,IAAI,CAACuE,MAAM,CAACf,OAAO,CAACc,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAACP,gBAAgB,CAACC,IAAI,CAAC;EACvG;EACA;AACJ;AACA;AACA;EACIH,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACV,KAAK,EAAE;MACb,MAAM,IAAIT,wBAAwB,CAAC,mBAAmB,CAAC;IAC3D;IACA,IAAI,CAACJ,OAAO,GAAGgB,gBAAgB,CAAC,IAAI,CAACH,KAAK,CAAC;EAC/C;EACA;AACJ;AACA;AACA;EACIqB,eAAeA,CAAA,EAAG;IACd,MAAMhB,OAAO,GAAG,IAAI,CAACf,UAAU,CAAC,CAAC;IACjC,IAAIe,OAAO,IAAI,CAACA,OAAO,CAACiB,cAAc,CAAC,KAAK,CAAC,EAAE;MAC3C,OAAO,IAAI;IACf;IACA,MAAMT,IAAI,GAAG,IAAIhE,IAAI,CAAC,CAAC,CAAC;IACxBgE,IAAI,CAACU,aAAa,CAAClB,OAAO,CAACmB,GAAG,CAAC,CAAC,CAAC;IACjC,OAAOX,IAAI;EACf;EACA;AACJ;AACA;AACA;EACII,OAAOA,CAAA,EAAG;IACN,OAAO,KAAK,CAACA,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAACI,eAAe,CAAC,CAAC,IAAI,IAAIxE,IAAI,CAAC,CAAC,GAAG,IAAI,CAACwE,eAAe,CAAC,CAAC,CAAC;EAC9F;AACJ;AACA,MAAMI,kBAAkB,GAAIC,IAAI,IAAK;EACjC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,IAAI;MACA,OAAOnB,IAAI,CAACC,KAAK,CAACkB,IAAI,CAAC;IAC3B,CAAC,CACD,OAAOpB,CAAC,EAAE,CAAE;EAChB;EACA,OAAOoB,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,iBAAiB,SAASlB,iBAAiB,CAAC;EAC9C;IAAS,IAAI,CAACpB,IAAI,GAAG,sBAAsB;EAAE;EAC7CH,WAAWA,CAACwC,IAAI,GAAG,CAAC,CAAC,EAAEzB,iBAAiB,EAAEC,SAAS,EAAE;IACjD;IACA,KAAK,CAACuB,kBAAkB,CAACC,IAAI,CAAC,EAAEzB,iBAAiB,EAAEC,SAAS,CAAC;EACjE;EACA;AACJ;AACA;AACA;EACIa,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,KAAK,CAAC4B,YAAY;EAClC;EACA;AACJ;AACA;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC7B,KAAK,CAAC8B,aAAa;EACnC;EACA;AACJ;AACA;AACA;EACIC,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAAChC,KAAK,CAAC8B,aAAa,GAAGE,YAAY;EAC3C;EACA;AACJ;AACA;AACA;EACItB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACV,KAAK,EAAE;MACb,MAAM,IAAIT,wBAAwB,CAAC,kBAAkB,CAAC;IAC1D,CAAC,MACI;MACD,IAAI,CAAChD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACwD,KAAK,CAAC,CAACpE,MAAM,EAAE;QACjC,MAAM,IAAIgE,qBAAqB,CAAC,6CAA6C,CAAC;MAClF;IACJ;IACA,IAAI,CAACT,OAAO,GAAG,IAAI,CAACa,KAAK;EAC7B;EACA;AACJ;AACA;AACA;EACIiC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACjC,KAAK,CAACkC,UAAU;EAChC;EACA;AACJ;AACA;AACA;EACIjB,OAAOA,CAAA,EAAG;IACN,OAAO,KAAK,CAACA,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAACI,eAAe,CAAC,CAAC,IAAI,IAAIxE,IAAI,CAAC,CAAC,GAAG,IAAI,CAACwE,eAAe,CAAC,CAAC,CAAC;EAC9F;EACA;AACJ;AACA;AACA;EACIA,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACrB,KAAK,CAACsB,cAAc,CAAC,YAAY,CAAC,EAAE;MAC1C,OAAO,IAAI;IACf;IACA,OAAO,IAAIzE,IAAI,CAAC,IAAI,CAACqD,SAAS,CAACnD,OAAO,CAAC,CAAC,GAAGqE,MAAM,CAAC,IAAI,CAACpB,KAAK,CAACmC,UAAU,CAAC,GAAG,IAAI,CAAC;EACpF;EACA;AACJ;AACA;AACA;EACIpD,QAAQA,CAAA,EAAG;IACP,OAAOwB,IAAI,CAAC6B,SAAS,CAAC,IAAI,CAACpC,KAAK,CAAC;EACrC;AACJ;AACA;AACA;AACA;AACA,MAAMqC,oBAAoB,SAASV,iBAAiB,CAAC;EACjD;IAAS,IAAI,CAACtC,IAAI,GAAG,0BAA0B;EAAE;EACjDqB,YAAYA,CAAA,EAAG;IACX,KAAK,CAACA,YAAY,CAAC,CAAC;IACpB,IAAI,CAAC4B,uBAAuB,CAAC,CAAC;EAClC;EACAA,uBAAuBA,CAAA,EAAG;IACtB,MAAMC,WAAW,GAAG,IAAI,CAACxB,QAAQ,CAAC,CAAC;IACnC,IAAI,CAACwB,WAAW,EAAE;MACd,MAAM,IAAIhD,wBAAwB,CAAC,6BAA6B,CAAC;IACrE;IACA,IAAI,CAACiD,kBAAkB,GAAGrC,gBAAgB,CAACoC,WAAW,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACIE,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACD,kBAAkB;EAClC;EACA;AACJ;AACA;EACI5B,gBAAgBA,CAACC,IAAI,EAAE;IACnB,MAAM1B,OAAO,GAAG,IAAI,CAACqD,kBAAkB;IACvC,OAAOrD,OAAO,IAAIA,OAAO,CAACgC,GAAG,GAAG,IAAItE,IAAI,CAACuE,MAAM,CAACjC,OAAO,CAACgC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAACP,gBAAgB,CAACC,IAAI,CAAC;EACvG;EACA;AACJ;AACA;AACA;EACII,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACuB,kBAAkB,IAAI,KAAK,CAACvB,OAAO,CAAC,CAAC;EACrD;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACmB,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAClB,cAAc,CAAC,KAAK,CAAC,EAAE;MAC1E,MAAMT,IAAI,GAAG,IAAIhE,IAAI,CAAC,CAAC,CAAC;MACxBgE,IAAI,CAACU,aAAa,CAAC,IAAI,CAACiB,kBAAkB,CAAChB,GAAG,CAAC;MAC/C,OAAOX,IAAI;IACf,CAAC,MACI;MACD,OAAO,KAAK,CAACQ,eAAe,CAAC,CAAC;IAClC;EACJ;AACJ;AAEA,MAAMqB,sBAAsB,GAAG,IAAI/S,cAAc,CAAC,sBAAsB,CAAC;AACzE;AACA;AACA;AACA,MAAMgT,mBAAmB,CAAC;EACtBzD,WAAWA,CAAC0D,aAAa,EAAEC,YAAY,EAAE;IACrC,IAAI,CAACD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;EACAC,IAAIA,CAAC9C,KAAK,EAAE;IACR,OAAOO,IAAI,CAAC6B,SAAS,CAAC;MAClB7E,IAAI,EAAEyC,KAAK,CAACZ,OAAO,CAAC,CAAC;MACrBa,iBAAiB,EAAED,KAAK,CAACgB,oBAAoB,CAAC,CAAC;MAC/Cd,SAAS,EAAEF,KAAK,CAACc,YAAY,CAAC,CAAC,CAAC/D,OAAO,CAAC,CAAC;MACzCpF,KAAK,EAAEqI,KAAK,CAACjB,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACN;EACAgE,MAAMA,CAACpL,KAAK,EAAE;IACV,IAAIoI,UAAU,GAAG,IAAI,CAAC6C,aAAa;IACnC,IAAII,UAAU,GAAG,EAAE;IACnB,IAAIC,sBAAsB,GAAG,EAAE;IAC/B,IAAIC,cAAc,GAAG,IAAI;IACzB,MAAMC,SAAS,GAAG,IAAI,CAACC,cAAc,CAACzL,KAAK,CAAC;IAC5C,IAAIwL,SAAS,EAAE;MACXpD,UAAU,GAAG,IAAI,CAACsD,cAAc,CAACF,SAAS,CAAC5F,IAAI,CAAC,IAAI,IAAI,CAACqF,aAAa;MACtEI,UAAU,GAAGG,SAAS,CAACxL,KAAK;MAC5BsL,sBAAsB,GAAGE,SAAS,CAAClD,iBAAiB;MACpDiD,cAAc,GAAG,IAAIrG,IAAI,CAACuE,MAAM,CAAC+B,SAAS,CAACjD,SAAS,CAAC,CAAC;IAC1D;IACA,OAAOJ,iBAAiB,CAACC,UAAU,EAAEiD,UAAU,EAAEC,sBAAsB,EAAEC,cAAc,CAAC;EAC5F;EACA;EACAG,cAAcA,CAAC9F,IAAI,EAAE;IACjB,OAAO,IAAI,CAACsF,YAAY,CAACS,IAAI,CAAEvD,UAAU,IAAKA,UAAU,CAACV,IAAI,KAAK9B,IAAI,CAAC;EAC3E;EACA6F,cAAcA,CAACzL,KAAK,EAAE;IAClB,IAAI;MACA,OAAO4I,IAAI,CAACC,KAAK,CAAC7I,KAAK,CAAC;IAC5B,CAAC,CACD,OAAO2I,CAAC,EAAE,CAAE;IACZ,OAAO,IAAI;EACf;EACA;IAAS,IAAI,CAACiD,IAAI,YAAAC,4BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFd,mBAAmB,EAA7BjT,EAAE,CAAAgU,QAAA,CAA6ChB,sBAAsB,GAArEhT,EAAE,CAAAgU,QAAA,CAAgFpI,cAAc;IAAA,CAA6C;EAAE;EAC/O;IAAS,IAAI,CAACqI,KAAK,kBAD6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EACY2C,mBAAmB;MAAAkB,OAAA,EAAnBlB,mBAAmB,CAAAY;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHoGpU,EAAE,CAAAqU,iBAAA,CAGXpB,mBAAmB,EAAc,CAAC;IACjHqB,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MAC/CD,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAAC6G,sBAAsB;IACjC,CAAC;EAAE,CAAC,EAAE;IAAEsB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MAClCD,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACP,cAAc;IACzB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAM4I,cAAc,CAAC;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,SAASD,cAAc,CAAC;EAC7ChF,WAAWA,CAACkF,QAAQ,EAAE;IAClB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3H,GAAG,GAAG,gBAAgB;EAC/B;EACA;AACJ;AACA;AACA;EACI4H,GAAGA,CAAA,EAAG;IACF,MAAMC,GAAG,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC/H,GAAG,CAAC;IAC1C,OAAO,IAAI,CAAC2H,QAAQ,CAACrB,MAAM,CAACuB,GAAG,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACIG,GAAGA,CAACzE,KAAK,EAAE;IACP,MAAMsE,GAAG,GAAG,IAAI,CAACF,QAAQ,CAACtB,IAAI,CAAC9C,KAAK,CAAC;IACrCuE,YAAY,CAACG,OAAO,CAAC,IAAI,CAACjI,GAAG,EAAE6H,GAAG,CAAC;EACvC;EACA;AACJ;AACA;EACIK,KAAKA,CAAA,EAAG;IACJJ,YAAY,CAACK,UAAU,CAAC,IAAI,CAACnI,GAAG,CAAC;EACrC;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAsB,4BAAApB,iBAAA;MAAA,YAAAA,iBAAA,IAAwFU,mBAAmB,EA1D7BzU,EAAE,CAAAgU,QAAA,CA0D6Cf,mBAAmB;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACgB,KAAK,kBA3D6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EA2DYmE,mBAAmB;MAAAN,OAAA,EAAnBM,mBAAmB,CAAAZ;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA7DoGpU,EAAE,CAAAqU,iBAAA,CA6DXI,mBAAmB,EAAc,CAAC;IACjHH,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAErB;EAAoB,CAAC,CAAC;AAAA;;AAEjE;AACA;AACA;AACA,MAAMmC,cAAc,CAAC;EACjB5F,WAAWA,CAAC6F,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAG,IAAI/T,eAAe,CAAC,IAAI,CAAC;IACvC,IAAI,CAACgU,kBAAkB,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,MAAM,CACbG,IAAI,CAAC/T,MAAM,CAACuG,KAAK,IAAI,CAAC,CAACA,KAAK,CAAC,EAAEtG,KAAK,CAAC,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoT,GAAGA,CAACzE,KAAK,EAAE;IACP,IAAI,CAAC+E,YAAY,CAACN,GAAG,CAACzE,KAAK,CAAC;IAC5B,IAAI,CAACiF,kBAAkB,CAAC,CAAC;IACzB,OAAO/T,EAAE,CAAC,IAAI,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACImT,GAAGA,CAAA,EAAG;IACF,MAAMrE,KAAK,GAAG,IAAI,CAAC+E,YAAY,CAACV,GAAG,CAAC,CAAC;IACrC,OAAOnT,EAAE,CAAC8O,KAAK,CAAC;EACpB;EACA;AACJ;AACA;AACA;AACA;EACI2E,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACI,YAAY,CAACJ,KAAK,CAAC,CAAC;IACzB,IAAI,CAACM,kBAAkB,CAAC,CAAC;IACzB,OAAO/T,EAAE,CAAC,IAAI,CAAC;EACnB;EACA+T,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,MAAM,CAACI,IAAI,CAAC,IAAI,CAACL,YAAY,CAACV,GAAG,CAAC,CAAC,CAAC;EAC7C;EACA;IAAS,IAAI,CAACd,IAAI,YAAA8B,uBAAA5B,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqB,cAAc,EAlHxBpV,EAAE,CAAAgU,QAAA,CAkHwCQ,cAAc;IAAA,CAA6C;EAAE;EACvM;IAAS,IAAI,CAACP,KAAK,kBAnH6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EAmHY8E,cAAc;MAAAjB,OAAA,EAAdiB,cAAc,CAAAvB;IAAA,EAAG;EAAE;AACrI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KArHoGpU,EAAE,CAAAqU,iBAAA,CAqHXe,cAAc,EAAc,CAAC;IAC5Gd,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAEE;EAAe,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,aAAa,CAAC;EAChBpG,WAAWA,CAACqG,YAAY,EAAEtL,UAAU,EAAE;IAClC,IAAI,CAACsL,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACtL,UAAU,GAAGA,UAAU;EAChC;EACA;AACJ;AACA;AACA;EACIuL,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACD,YAAY,CAAClB,GAAG,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACIoB,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CACjBL,IAAI,CAAC7T,GAAG,CAAE0O,KAAK,IAAKA,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIyE,wBAAwBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACF,QAAQ,CAAC,CAAC,CACjBL,IAAI,CAAC5T,SAAS,CAACyO,KAAK,IAAI;MACzB,IAAIA,KAAK,CAACe,QAAQ,CAAC,CAAC,IAAI,CAACf,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE;QACtC,OAAO,IAAI,CAACe,YAAY,CAAChC,KAAK,CAACgB,oBAAoB,CAAC,CAAC,EAAEhB,KAAK,CAAC,CACxDmF,IAAI,CAAC5T,SAAS,CAACoU,GAAG,IAAI;UACvB,IAAIA,GAAG,CAACC,SAAS,CAAC,CAAC,EAAE;YACjB,OAAO,IAAI,CAACH,eAAe,CAAC,CAAC;UACjC,CAAC,MACI;YACD,OAAOvU,EAAE,CAAC,KAAK,CAAC;UACpB;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,MACI;QACD,OAAOA,EAAE,CAAC8O,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC;MAC9B;IACJ,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;EACI4E,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,YAAY,CAACL,WAAW,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIY,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACD,aAAa,CAAC,CAAC,CACtBV,IAAI,CAAC7T,GAAG,CAAE0O,KAAK,IAAKA,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8E,YAAYA,CAACC,YAAY,EAAEtE,IAAI,EAAE;IAC7B,OAAO,IAAI,CAACuE,WAAW,CAACD,YAAY,CAAC,CAACD,YAAY,CAACrE,IAAI,CAAC,CACnDyD,IAAI,CAAC5T,SAAS,CAAE2U,MAAM,IAAK;MAC5B,OAAO,IAAI,CAACC,kBAAkB,CAACD,MAAM,CAAC;IAC1C,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzL,QAAQA,CAACuL,YAAY,EAAEtE,IAAI,EAAE;IACzB,OAAO,IAAI,CAACuE,WAAW,CAACD,YAAY,CAAC,CAACvL,QAAQ,CAACiH,IAAI,CAAC,CAC/CyD,IAAI,CAAC5T,SAAS,CAAE2U,MAAM,IAAK;MAC5B,OAAO,IAAI,CAACC,kBAAkB,CAACD,MAAM,CAAC;IAC1C,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItL,MAAMA,CAACoL,YAAY,EAAE;IACjB,OAAO,IAAI,CAACC,WAAW,CAACD,YAAY,CAAC,CAACpL,MAAM,CAAC,CAAC,CACzCuK,IAAI,CAAC5T,SAAS,CAAE2U,MAAM,IAAK;MAC5B,IAAIA,MAAM,CAACN,SAAS,CAAC,CAAC,EAAE;QACpB,IAAI,CAACL,YAAY,CAACZ,KAAK,CAAC,CAAC,CACpBQ,IAAI,CAAC7T,GAAG,CAAC,MAAM4U,MAAM,CAAC,CAAC;MAChC;MACA,OAAOhV,EAAE,CAACgV,MAAM,CAAC;IACrB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxL,eAAeA,CAACsL,YAAY,EAAEtE,IAAI,EAAE;IAChC,OAAO,IAAI,CAACuE,WAAW,CAACD,YAAY,CAAC,CAACtL,eAAe,CAACgH,IAAI,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/G,aAAaA,CAACqL,YAAY,EAAEtE,IAAI,EAAE;IAC9B,OAAO,IAAI,CAACuE,WAAW,CAACD,YAAY,CAAC,CAACrL,aAAa,CAAC+G,IAAI,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,YAAYA,CAACgE,YAAY,EAAEtE,IAAI,EAAE;IAC7B,OAAO,IAAI,CAACuE,WAAW,CAACD,YAAY,CAAC,CAAChE,YAAY,CAACN,IAAI,CAAC,CACnDyD,IAAI,CAAC5T,SAAS,CAAE2U,MAAM,IAAK;MAC5B,OAAO,IAAI,CAACC,kBAAkB,CAACD,MAAM,CAAC;IAC1C,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,WAAWA,CAACD,YAAY,EAAE;IACtB,MAAMI,KAAK,GAAG,IAAI,CAACnM,UAAU,CAACqJ,IAAI,CAAEjJ,QAAQ,IAAKA,QAAQ,CAAC+E,OAAO,CAAC,CAAC,KAAK4G,YAAY,CAAC;IACrF,IAAI,CAACI,KAAK,EAAE;MACR,MAAM,IAAIC,SAAS,CAAC,+CAA+CL,YAAY,QAAQ,CAAC;IAC5F;IACA,OAAOI,KAAK;EAChB;EACAD,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAIA,MAAM,CAACN,SAAS,CAAC,CAAC,IAAIM,MAAM,CAACV,QAAQ,CAAC,CAAC,EAAE;MACzC,OAAO,IAAI,CAACD,YAAY,CAACd,GAAG,CAACyB,MAAM,CAACV,QAAQ,CAAC,CAAC,CAAC,CAC1CL,IAAI,CAAC7T,GAAG,CAAE0O,KAAK,IAAK;QACrB,OAAOkG,MAAM;MACjB,CAAC,CAAC,CAAC;IACP;IACA,OAAOhV,EAAE,CAACgV,MAAM,CAAC;EACrB;EACA;IAAS,IAAI,CAAC3C,IAAI,YAAA+C,sBAAA7C,iBAAA;MAAA,YAAAA,iBAAA,IAAwF6B,aAAa,EAvTvB5V,EAAE,CAAAgU,QAAA,CAuTuCoB,cAAc,GAvTvDpV,EAAE,CAAAgU,QAAA,CAuTkErI,kBAAkB;IAAA,CAA6C;EAAE;EACrO;IAAS,IAAI,CAACsI,KAAK,kBAxT6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EAwTYsF,aAAa;MAAAzB,OAAA,EAAbyB,aAAa,CAAA/B;IAAA,EAAG;EAAE;AACpI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA1ToGpU,EAAE,CAAAqU,iBAAA,CA0TXuB,aAAa,EAAc,CAAC;IAC3GtB,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAEc;EAAe,CAAC,EAAE;IAAEd,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACzED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACR,kBAAkB;IAC7B,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMkL,cAAc,CAAC;EACjB;EACA;EACAC,UAAUA,CAACC,OAAO,EAAE;IAChB,IAAI,CAACA,OAAO,GAAGhL,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiL,cAAc,EAAED,OAAO,CAAC;EAC/D;EACAE,SAASA,CAAClK,GAAG,EAAE;IACX,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACAmK,WAAWA,CAACjP,KAAK,EAAEkP,oBAAoB,EAAE;IACrC,MAAM7G,KAAK,GAAGF,iBAAiB,CAAC,IAAI,CAAC6G,SAAS,CAAC,aAAa,CAAC,EAAEhP,KAAK,EAAE,IAAI,CAACyH,OAAO,CAAC,CAAC,CAAC;IACrF;IACA;IACA,IAAIyH,oBAAoB,IAAI,CAAC7G,KAAK,CAACiB,OAAO,CAAC,CAAC,EAAE;MAC1C;MACA;MACA,MAAM,IAAItB,uBAAuB,CAAC,4BAA4B,CAAC;IACnE;IACA,OAAOK,KAAK;EAChB;EACAZ,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACuH,SAAS,CAAC,MAAM,CAAC;EACjC;EACAG,kBAAkBA,CAACpF,IAAI,EAAE;IACrB,OAAO,IAAI9P,YAAY,CAAC;MAAEmV,IAAI,EAAE,CAAC,CAAC;MAAEC,MAAM,EAAE;IAAI,CAAC,CAAC;EACtD;EACAC,qBAAqBA,CAACvF,IAAI,EAAE;IACxB,OAAO,IAAI9P,YAAY,CAAC;MAAEmV,IAAI,EAAE,CAAC,CAAC;MAAEC,MAAM,EAAE;IAAI,CAAC,CAAC;EACtD;EACAE,iBAAiBA,CAACC,MAAM,EAAE;IACtB,MAAMC,cAAc,GAAG,IAAI,CAACT,SAAS,CAAC,GAAGQ,MAAM,WAAW,CAAC;IAC3D,MAAME,YAAY,GAAG,IAAI,CAACV,SAAS,CAAC,cAAc,CAAC;IACnD,OAAOS,cAAc,GAAGC,YAAY,GAAGD,cAAc,GAAG,EAAE;EAC9D;EACAE,UAAUA,CAAA,EAAG;IACT,MAAMC,aAAa,GAAG,IAAI,CAACZ,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACrD,IAAIY,aAAa,YAAY1V,WAAW,EAAE;MACtC,OAAO0V,aAAa;IACxB;IACA,IAAIC,OAAO,GAAG,IAAI3V,WAAW,CAAC,CAAC;IAC/B0K,MAAM,CAACkL,OAAO,CAACF,aAAa,CAAC,CAACnL,OAAO,CAAC,CAAC,CAACK,GAAG,EAAE9E,KAAK,CAAC,KAAK;MACpD6P,OAAO,GAAGA,OAAO,CAACE,MAAM,CAACjL,GAAG,EAAE9E,KAAK,CAAC;IACxC,CAAC,CAAC;IACF,OAAO6P,OAAO;EAClB;AACJ;AAEA,MAAMG,YAAY,CAAC;EACf;EACAzI,WAAWA,CAAC3E,OAAO,EAAEqN,QAAQ,EAAEC,QAAQ,EAAE9U,MAAM,EAAEI,QAAQ,EAAE6M,KAAK,GAAG,IAAI,EAAE;IACrE,IAAI,CAACzF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqN,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9U,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC+U,MAAM,CAAC,CAAC/U,MAAM,CAAC,CAAC;IAC1C,IAAIA,MAAM,YAAY+I,KAAK,EAAE;MACzB,IAAI,CAAC/I,MAAM,GAAGA,MAAM;IACxB;IACA,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC2U,MAAM,CAAC,CAAC3U,QAAQ,CAAC,CAAC;IAChD,IAAIA,QAAQ,YAAY2I,KAAK,EAAE;MAC3B,IAAI,CAAC3I,QAAQ,GAAGA,QAAQ;IAC5B;IACA,IAAI,CAAC6M,KAAK,GAAGA,KAAK;EACtB;EACA+H,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACH,QAAQ;EACxB;EACApC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxF,KAAK;EACrB;EACAgI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACH,QAAQ;EACxB;EACAI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAClV,MAAM,CAAC3B,MAAM,CAAC8K,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC;EAC3C;EACAgM,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/U,QAAQ,CAAC/B,MAAM,CAAC8K,GAAG,IAAI,CAAC,CAACA,GAAG,CAAC;EAC7C;EACA0J,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACrL,OAAO;EACvB;EACA4N,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAAC5N,OAAO;EACxB;AACJ;AAEA,MAAM6N,qBAAqB,CAAC;;AAG5B;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,SAASD,qBAAqB,CAAC;EAC3DlJ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGvD,SAAS,CAAC;IACnB,IAAI,CAACqE,KAAK,GAAG;MACTsI,KAAK,EAAE7H;IACX,CAAC;IACD,IAAI,CAACjP,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC+W,UAAU,GAAG,KAAK;EAC3B;AACJ;AACA,MAAMC,oBAAoB,GAAG,IAAIH,0BAA0B,CAAC,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,mBAAmB,SAASlC,cAAc,CAAC;EAC7CrH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGvD,SAAS,CAAC;IACnB,IAAI,CAAC+K,cAAc,GAAG8B,oBAAoB;EAC9C;EACA,OAAOE,KAAKA,CAACjC,OAAO,EAAE;IAClB,OAAO,CAACgC,mBAAmB,EAAEhC,OAAO,CAAC;EACzC;EACAV,YAAYA,CAACrE,IAAI,EAAE;IACf,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACAlM,QAAQA,CAACiH,IAAI,EAAE;IACX,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACAjM,eAAeA,CAACgH,IAAI,EAAE;IAClB,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACAhM,aAAaA,CAAC+G,IAAI,EAAE;IAChB,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACA/L,MAAMA,CAAC8G,IAAI,EAAE;IACT,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACA3E,YAAYA,CAACN,IAAI,EAAE;IACf,OAAOxQ,EAAE,CAAC,IAAI,CAACyX,iBAAiB,CAACjH,IAAI,CAAC,CAAC,CAACyD,IAAI,CAAC3T,KAAK,CAAC,IAAI,CAACmV,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;EAChF;EACAgC,iBAAiBA,CAACjH,IAAI,EAAE;IACpB,IAAI,IAAI,CAACiF,SAAS,CAAC,YAAY,CAAC,EAAE;MAC9B,OAAO,IAAIgB,YAAY,CAAC,KAAK,EAAE,IAAI,CAACb,kBAAkB,CAACpF,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,uBAAuB,CAAC,CAAC;IAClG;IACA,IAAI;MACA,MAAM1B,KAAK,GAAG,IAAI,CAAC4G,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC;MAClD,OAAO,IAAIe,YAAY,CAAC,IAAI,EAAE,IAAI,CAACV,qBAAqB,CAACvF,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,yBAAyB,CAAC,EAAE1B,KAAK,CAAC;IAChH,CAAC,CACD,OAAOW,GAAG,EAAE;MACR,OAAO,IAAIgH,YAAY,CAAC,KAAK,EAAE,IAAI,CAACb,kBAAkB,CAACpF,IAAI,CAAC,EAAE,IAAI,EAAE,CAACf,GAAG,CAACnB,OAAO,CAAC,CAAC;IACtF;EACJ;EACA;IAAS,IAAI,CAAC+D,IAAI;MAAA,IAAAqF,gCAAA;MAAA,gBAAAC,4BAAApF,iBAAA;QAAA,QAAAmF,gCAAA,KAAAA,gCAAA,GApe8ElZ,EAAE,CAAAoZ,qBAAA,CAoeQL,mBAAmB,IAAAhF,iBAAA,IAAnBgF,mBAAmB;MAAA;IAAA,IAAsD;EAAE;EACrL;IAAS,IAAI,CAAC9E,KAAK,kBAre6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EAqeYyI,mBAAmB;MAAA5E,OAAA,EAAnB4E,mBAAmB,CAAAlF;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAveoGpU,EAAE,CAAAqU,iBAAA,CAueX0E,mBAAmB,EAAc,CAAC;IACjHzE,IAAI,EAAEpU;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,IAAImZ,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAAC,MAAM,CAAC,GAAG,MAAM;EACrCA,oBAAoB,CAAC,OAAO,CAAC,GAAG,OAAO;AAC3C,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA,IAAIC,iBAAiB;AACrB,CAAC,UAAUA,iBAAiB,EAAE;EAC1BA,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC9DA,iBAAiB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC1CA,iBAAiB,CAAC,eAAe,CAAC,GAAG,eAAe;AACxD,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,IAAIC,wBAAwB;AAC5B,CAAC,UAAUA,wBAAwB,EAAE;EACjCA,wBAAwB,CAAC,MAAM,CAAC,GAAG,MAAM;EACzCA,wBAAwB,CAAC,OAAO,CAAC,GAAG,OAAO;EAC3CA,wBAAwB,CAAC,cAAc,CAAC,GAAG,cAAc;AAC7D,CAAC,EAAEA,wBAAwB,KAAKA,wBAAwB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,MAAMC,2BAA2B,SAASd,qBAAqB,CAAC;EAC5DlJ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGvD,SAAS,CAAC;IACnB,IAAI,CAAC0L,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC8B,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAGJ,wBAAwB,CAACK,IAAI;IACrD,IAAI,CAACzB,QAAQ,GAAG;MACZtN,OAAO,EAAE,GAAG;MACZgP,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,CAAC,yCAAyC,CAAC;IAChE,IAAI,CAACC,eAAe,GAAG,CAAC,2CAA2C,CAAC;IACpE,IAAI,CAACC,SAAS,GAAG;MACbC,QAAQ,EAAE,WAAW;MACrBC,YAAY,EAAEb,oBAAoB,CAACc,IAAI;MACvCC,iBAAiB,EAAE;IACvB,CAAC;IACD,IAAI,CAAC9J,KAAK,GAAG;MACT2J,QAAQ,EAAE,OAAO;MACjBI,SAAS,EAAEf,iBAAiB,CAACgB,kBAAkB;MAC/CF,iBAAiB,EAAE,IAAI;MACvBxB,KAAK,EAAE3G;IACX,CAAC;IACD,IAAI,CAACsI,OAAO,GAAG;MACXN,QAAQ,EAAE,OAAO;MACjBI,SAAS,EAAEf,iBAAiB,CAACkB,aAAa;MAC1CJ,iBAAiB,EAAE;IACvB,CAAC;EACL;AACJ;AACA,MAAMK,oBAAoB,GAAG,IAAIjB,2BAA2B,CAAC,CAAC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,oBAAoB,SAAS7D,cAAc,CAAC;EAC9C,OAAOmC,KAAKA,CAACjC,OAAO,EAAE;IAClB,OAAO,CAAC2D,oBAAoB,EAAE3D,OAAO,CAAC;EAC1C;EACA,IAAImD,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjD,SAAS,CAAC,wBAAwB,CAAC;EACnD;EACA,IAAI0C,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC1C,SAAS,CAAC,kBAAkB,CAAC;EAC7C;EACAzH,WAAWA,CAACmL,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC7B,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,sBAAsB,GAAG;MAC1B,CAACzB,oBAAoB,CAACc,IAAI,GAAG,MAAM;QAC/B,OAAO3Y,EAAE,CAAC,IAAI,CAACoZ,KAAK,CAACG,QAAQ,CAACC,WAAW,CAAC,CAACvF,IAAI,CAAC5T,SAAS,CAAEoZ,MAAM,IAAK;UAClE,IAAIA,MAAM,CAACC,IAAI,EAAE;YACb,OAAO,IAAI,CAACC,YAAY,CAACF,MAAM,CAACC,IAAI,CAAC;UACzC;UACA,OAAO1Z,EAAE,CAAC,IAAIyW,YAAY,CAAC,KAAK,EAAEgD,MAAM,EAAE,IAAI,CAAChE,SAAS,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;QACvH,CAAC,CAAC,CAAC;MACP,CAAC;MACD,CAACoC,oBAAoB,CAAC+B,KAAK,GAAG,MAAM;QAChC,MAAMC,MAAM,GAAG,WAAW;QAC1B,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;QACvE,OAAO7Z,EAAE,CAAC,IAAI,CAACoZ,KAAK,CAACG,QAAQ,CAACO,QAAQ,CAAC,CAAC7F,IAAI,CAAC7T,GAAG,CAAE0Z,QAAQ,IAAK,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,CAAC,EAAE1Z,GAAG,CAAEqZ,MAAM,IAAK;UACnH,IAAI,CAACA,MAAM,CAACnQ,KAAK,EAAE;YACf,OAAO,IAAImN,YAAY,CAAC,IAAI,EAAEgD,MAAM,EAAE,IAAI,CAAChE,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACA,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAACC,WAAW,CAAC+D,MAAM,EAAEb,iBAAiB,CAAC,CAAC;UACjK;UACA,OAAO,IAAInC,YAAY,CAAC,KAAK,EAAEgD,MAAM,EAAE,IAAI,CAAChE,SAAS,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC;QACnH,CAAC,CAAC,EAAElV,UAAU,CAAEkP,GAAG,IAAK;UACpB,MAAM5N,MAAM,GAAG,EAAE;UACjB,IAAI4N,GAAG,YAAYhB,uBAAuB,EAAE;YACxC5M,MAAM,CAACmY,IAAI,CAACvK,GAAG,CAACnB,OAAO,CAAC;UAC5B,CAAC,MACI;YACDzM,MAAM,CAACmY,IAAI,CAAC,uBAAuB,CAAC;UACxC;UACA,OAAOha,EAAE,CAAC,IAAIyW,YAAY,CAAC,KAAK,EAAEhH,GAAG,EAAE,IAAI,CAACgG,SAAS,CAAC,kBAAkB,CAAC,EAAE5T,MAAM,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;MACP;IACJ,CAAC;IACD,IAAI,CAACoY,eAAe,GAAG;MACnB,CAACpC,oBAAoB,CAACc,IAAI,GAAG,MAAM;QAC/B,OAAO3Y,EAAE,CAAC,IAAI,CAACoZ,KAAK,CAACG,QAAQ,CAACC,WAAW,CAAC,CAACvF,IAAI,CAAC7T,GAAG,CAAEqZ,MAAM,IAAK,CAAC,EAAEA,MAAM,KAAKA,MAAM,CAACC,IAAI,IAAID,MAAM,CAACnQ,KAAK,CAAC,CAAC,CAAC,CAAC;MACjH,CAAC;MACD,CAACuO,oBAAoB,CAAC+B,KAAK,GAAG,MAAM;QAChC,OAAO5Z,EAAE,CAAC,IAAI,CAACoZ,KAAK,CAACG,QAAQ,CAACO,QAAQ,CAAC,CAAC7F,IAAI,CAAC7T,GAAG,CAAE0Z,QAAQ,IAAK,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC,CAAC,EAAE1Z,GAAG,CAAEqZ,MAAM,IAAK,CAAC,EAAEA,MAAM,KAAKA,MAAM,CAAC/I,YAAY,IAAI+I,MAAM,CAACnQ,KAAK,CAAC,CAAC,CAAC,CAAC;MAChL;IACJ,CAAC;IACD,IAAI,CAACkM,cAAc,GAAGyD,oBAAoB;EAC9C;EACApE,YAAYA,CAACrE,IAAI,EAAE;IACf,IAAI,IAAI,CAACiF,SAAS,CAAC,iBAAiB,CAAC,KAAKqC,iBAAiB,CAACoC,QAAQ,EAAE;MAClE,OAAO,IAAI,CAACC,aAAa,CAAC3J,IAAI,CAACzG,KAAK,EAAEyG,IAAI,CAAC5G,QAAQ,CAAC;IACxD,CAAC,MACI;MACD,OAAO,IAAI,CAACwQ,gBAAgB,CAAC,CAAC,CAACnG,IAAI,CAAC5T,SAAS,CAAE2U,MAAM,IAAK;QACtD,IAAI,CAACA,MAAM,EAAE;UACT,IAAI,CAACqF,iBAAiB,CAAC,CAAC;UACxB,OAAOra,EAAE,CAAC,IAAIyW,YAAY,CAAC,IAAI,CAAC,CAAC;QACrC;QACA,OAAO,IAAI,CAAC6D,sBAAsB,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC;IACP;EACJ;EACAA,sBAAsBA,CAAA,EAAG;IACrB,MAAMC,qBAAqB,GAAG,IAAI,CAACjB,sBAAsB,CAAC,IAAI,CAACZ,YAAY,CAAC;IAC5E,IAAI6B,qBAAqB,EAAE;MACvB,OAAOA,qBAAqB,CAACxP,IAAI,CAAC,IAAI,CAAC;IAC3C;IACA,MAAM,IAAIe,KAAK,CAAC,IAAI,IAAI,CAAC4M,YAAY;AAC7C,gEAAgE,CAAC;EAC7D;EACA5H,YAAYA,CAAChC,KAAK,EAAE;IAChB,MAAM+K,MAAM,GAAG,SAAS;IACxB,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACqB,IAAI,CAACxV,GAAG,EAAE,IAAI,CAACyV,uBAAuB,CAAC3L,KAAK,CAAC,EAAE;MAAEwH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MAC9G,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACA,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAACiF,oBAAoB,CAACjG,GAAG,EAAE3F,KAAK,EAAE8J,iBAAiB,CAAC,CAAC;IAC3K,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,CAAC,CAAC,CAAC;EAC3D;EACA0F,aAAaA,CAACS,QAAQ,EAAEhR,QAAQ,EAAE;IAC9B,MAAMiQ,MAAM,GAAG,OAAO;IACtB,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACqB,IAAI,CAACxV,GAAG,EAAE,IAAI,CAAC6V,wBAAwB,CAACD,QAAQ,EAAEhR,QAAQ,CAAC,EAAE;MAAE0M,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MAC5H,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACA,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAACC,WAAW,CAACjB,GAAG,EAAEmE,iBAAiB,CAAC,CAAC;IAC3J,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,CAAC,CAAC,CAAC;EAC3D;EACA4F,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAChB,MAAM,CAACyB,QAAQ,CAACC,IAAI,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;EACvD;EACAZ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACH,eAAe,CAAC,IAAI,CAACvB,YAAY,CAAC,CAAC3N,IAAI,CAAC,IAAI,CAAC;EAC7D;EACA4O,YAAYA,CAACD,IAAI,EAAE;IACf,MAAMG,MAAM,GAAG,OAAO;IACtB,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACqB,IAAI,CAACxV,GAAG,EAAE,IAAI,CAACiW,oBAAoB,CAACvB,IAAI,CAAC,EAAE;MAAEpD,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MAC1G,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACA,SAAS,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAACC,WAAW,CAACjB,GAAG,EAAEmE,iBAAiB,CAAC,CAAC;IAC3J,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,CAAC,CAAC,CAAC;EAC3D;EACAwG,oBAAoBA,CAACvB,IAAI,EAAE;IACvB,MAAMD,MAAM,GAAG;MACXyB,UAAU,EAAE,IAAI,CAACzF,SAAS,CAAC,iBAAiB,CAAC;MAC7CiE,IAAI,EAAEA,IAAI;MACVyB,YAAY,EAAE,IAAI,CAAC1F,SAAS,CAAC,mBAAmB,CAAC;MACjD2F,SAAS,EAAE,IAAI,CAAC3F,SAAS,CAAC,UAAU;IACxC,CAAC;IACD,OAAO,IAAI,CAAC4F,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,sBAAsB,CAAC9B,MAAM,CAAC,CAAC,CAAC;EAC1F;EACAgB,uBAAuBA,CAAC3L,KAAK,EAAE;IAC3B,MAAM2K,MAAM,GAAG;MACXyB,UAAU,EAAE,IAAI,CAACzF,SAAS,CAAC,mBAAmB,CAAC;MAC/C7E,aAAa,EAAE9B,KAAK,CAAC6B,eAAe,CAAC,CAAC;MACtC6K,KAAK,EAAE,IAAI,CAAC/F,SAAS,CAAC,eAAe,CAAC;MACtC2F,SAAS,EAAE,IAAI,CAAC3F,SAAS,CAAC,UAAU;IACxC,CAAC;IACD,OAAO,IAAI,CAAC4F,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,sBAAsB,CAAC9B,MAAM,CAAC,CAAC,CAAC;EAC1F;EACAoB,wBAAwBA,CAACD,QAAQ,EAAEhR,QAAQ,EAAE;IACzC,MAAM6P,MAAM,GAAG;MACXyB,UAAU,EAAE,IAAI,CAACzF,SAAS,CAAC,iBAAiB,CAAC;MAC7CmF,QAAQ,EAAEA,QAAQ;MAClBhR,QAAQ,EAAEA,QAAQ;MAClB4R,KAAK,EAAE,IAAI,CAAC/F,SAAS,CAAC,aAAa;IACvC,CAAC;IACD,OAAO,IAAI,CAAC4F,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,sBAAsB,CAAC9B,MAAM,CAAC,CAAC,CAAC;EAC1F;EACAgC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACtD,gBAAgB,KAAKJ,wBAAwB,CAAC2D,KAAK,EAAE;MAC1D,IAAI,IAAI,CAACjG,SAAS,CAAC,UAAU,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,cAAc,CAAC,EAAE;QAC9D,OAAO,IAAI9U,WAAW,CAAC;UACnBgb,aAAa,EAAE,QAAQ,GAAGC,IAAI,CAAC,IAAI,CAACnG,SAAS,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,IAAI,CAACA,SAAS,CAAC,cAAc,CAAC;QACpG,CAAC,CAAC;MACN,CAAC,MACI;QACD,MAAM3J,KAAK,CAAC,sFAAsF,CAAC;MACvG;IACJ;IACA,OAAOY,SAAS;EACpB;EACA0J,UAAUA,CAAA,EAAG;IACT,IAAIE,OAAO,GAAG,KAAK,CAACF,UAAU,CAAC,CAAC;IAChCE,OAAO,GAAGA,OAAO,CAACE,MAAM,CAAC,cAAc,EAAE,mCAAmC,CAAC;IAC7E,MAAMqF,WAAW,GAAG,IAAI,CAACJ,eAAe,CAAC,CAAC;IAC1C,IAAII,WAAW,KAAKnP,SAAS,EAAE;MAC3B,OAAO4J,OAAO;IAClB;IACA,KAAK,MAAMwF,SAAS,IAAID,WAAW,CAACvQ,IAAI,CAAC,CAAC,EAAE;MACxC,KAAK,MAAMyQ,WAAW,IAAIF,WAAW,CAACG,MAAM,CAACF,SAAS,CAAC,EAAE;QACrDxF,OAAO,GAAGA,OAAO,CAACE,MAAM,CAACsF,SAAS,EAAEC,WAAW,CAAC;MACpD;IACJ;IACA,OAAOzF,OAAO;EAClB;EACAgF,WAAWA,CAAC7B,MAAM,EAAE;IAChBpO,MAAM,CAACkL,OAAO,CAACkD,MAAM,CAAC,CAACvO,OAAO,CAAC,CAAC,CAACK,GAAG,EAAEP,GAAG,CAAC,KAAK,CAACA,GAAG,IAAI,OAAOyO,MAAM,CAAClO,GAAG,CAAC,CAAC;IAC1E,OAAOkO,MAAM;EACjB;EACA8B,sBAAsBA,CAAC9B,MAAM,EAAE;IAC3B,IAAI,IAAI,CAACtB,gBAAgB,KAAKJ,wBAAwB,CAACkE,YAAY,EAAE;MACjE,IAAI,IAAI,CAACxG,SAAS,CAAC,UAAU,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,cAAc,CAAC,EAAE;QAC9D,OAAO;UACH,GAAGgE,MAAM;UACT2B,SAAS,EAAE,IAAI,CAAC3F,SAAS,CAAC,UAAU,CAAC;UACrCyG,aAAa,EAAE,IAAI,CAACzG,SAAS,CAAC,cAAc;QAChD,CAAC;MACL,CAAC,MACI;QACD,MAAM3J,KAAK,CAAC,6FAA6F,CAAC;MAC9G;IACJ;IACA,OAAO2N,MAAM;EACjB;EACAkB,mBAAmBA,CAAClG,GAAG,EAAE;IACrB,IAAI5S,MAAM,GAAG,EAAE;IACf,IAAI4S,GAAG,YAAY7T,iBAAiB,EAAE;MAClC,IAAI6T,GAAG,CAACnL,KAAK,CAAC6S,iBAAiB,EAAE;QAC7Bta,MAAM,CAACmY,IAAI,CAACvF,GAAG,CAACnL,KAAK,CAAC6S,iBAAiB,CAAC;MAC5C,CAAC,MACI;QACDta,MAAM,GAAG,IAAI,CAAC4T,SAAS,CAAC,eAAe,CAAC;MAC5C;IACJ,CAAC,MACI,IAAIhB,GAAG,YAAYhG,uBAAuB,EAAE;MAC7C5M,MAAM,CAACmY,IAAI,CAACvF,GAAG,CAACnG,OAAO,CAAC;IAC5B,CAAC,MACI;MACDzM,MAAM,CAACmY,IAAI,CAAC,uBAAuB,CAAC;IACxC;IACA,OAAOha,EAAE,CAAC,IAAIyW,YAAY,CAAC,KAAK,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,kBAAkB,CAAC,EAAE5T,MAAM,EAAE,EAAE,CAAC,CAAC;EAC3F;EACAmZ,gBAAgBA,CAAA,EAAG;IACf,MAAMvB,MAAM,GAAG;MACX2C,aAAa,EAAE,IAAI,CAAC3G,SAAS,CAAC,wBAAwB,CAAC;MACvD2F,SAAS,EAAE,IAAI,CAAC3F,SAAS,CAAC,UAAU,CAAC;MACrC0F,YAAY,EAAE,IAAI,CAAC1F,SAAS,CAAC,uBAAuB,CAAC;MACrD+F,KAAK,EAAE,IAAI,CAAC/F,SAAS,CAAC,iBAAiB,CAAC;MACxC4G,KAAK,EAAE,IAAI,CAAC5G,SAAS,CAAC,iBAAiB,CAAC;MACxC,GAAG,IAAI,CAACA,SAAS,CAAC,kBAAkB;IACxC,CAAC;IACD,MAAMgD,QAAQ,GAAG,IAAI,CAACzC,iBAAiB,CAAC,WAAW,CAAC;IACpD,MAAMsG,KAAK,GAAG,IAAI,CAACjB,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC7B,MAAM,CAAC,CAAC;IAChE,OAAO,GAAGhB,QAAQ,IAAI6D,KAAK,EAAE;EACjC;EACAvC,sBAAsBA,CAACwC,IAAI,EAAE;IACzB,OAAOA,IAAI,GACLA,IAAI,CAAChQ,KAAK,CAAC,GAAG,CAAC,CAACiQ,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACpC,MAAMzQ,IAAI,GAAGyQ,IAAI,CAACnQ,KAAK,CAAC,GAAG,CAAC;MAC5BkQ,GAAG,CAACxQ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGyB,kBAAkB,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C,OAAOwQ,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,GACJ,CAAC,CAAC;EACZ;EACApB,mBAAmBA,CAAC5B,MAAM,EAAE;IACxB,OAAOpO,MAAM,CAACC,IAAI,CAACmO,MAAM,CAAC,CACrBrZ,GAAG,CAAEqM,CAAC,IAAK;MACZ,OAAO,GAAGkQ,kBAAkB,CAAClQ,CAAC,CAAC,IAAIkQ,kBAAkB,CAAClD,MAAM,CAAChN,CAAC,CAAC,CAAC,EAAE;IACtE,CAAC,CAAC,CACGqB,IAAI,CAAC,GAAG,CAAC;EAClB;EACA4M,oBAAoBA,CAACjG,GAAG,EAAEmI,aAAa,EAAEhE,iBAAiB,EAAE;IACxD,MAAMiE,cAAc,GAAG,IAAI,CAACnH,WAAW,CAACjB,GAAG,EAAEmE,iBAAiB,CAAC;IAC/D,IAAI,CAACiE,cAAc,CAAClM,eAAe,CAAC,CAAC,IAAIiM,aAAa,CAACjM,eAAe,CAAC,CAAC,EAAE;MACtEkM,cAAc,CAAChM,eAAe,CAAC+L,aAAa,CAACjM,eAAe,CAAC,CAAC,CAAC;IACnE;IACA,OAAOkM,cAAc;EACzB;EACAtT,QAAQA,CAACiH,IAAI,EAAE;IACX,MAAM,IAAI1E,KAAK,CAAC,4EAA4E,CAAC;EACjG;EACAtC,eAAeA,CAACgH,IAAI,EAAE;IAClB,MAAM,IAAI1E,KAAK,CAAC,mFAAmF,CAAC;EACxG;EACArC,aAAaA,CAAC+G,IAAI,GAAG,CAAC,CAAC,EAAE;IACrB,MAAM,IAAI1E,KAAK,CAAC,iFAAiF,CAAC;EACtG;EACApC,MAAMA,CAAA,EAAG;IACL,OAAO1J,EAAE,CAAC,IAAIyW,YAAY,CAAC,IAAI,CAAC,CAAC;EACrC;EACA;IAAS,IAAI,CAACpE,IAAI,YAAAyK,6BAAAvK,iBAAA;MAAA,YAAAA,iBAAA,IAAwF2G,oBAAoB,EA/1B9B1a,EAAE,CAAAgU,QAAA,CA+1B8C/R,EAAE,CAACsc,UAAU,GA/1B7Dve,EAAE,CAAAgU,QAAA,CA+1BwEtT,EAAE,CAAC8d,cAAc,GA/1B3Fxe,EAAE,CAAAgU,QAAA,CA+1BsGjT,SAAS;IAAA,CAA6C;EAAE;EAChQ;IAAS,IAAI,CAACkT,KAAK,kBAh2B6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EAg2BYoK,oBAAoB;MAAAvG,OAAA,EAApBuG,oBAAoB,CAAA7G;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAl2BoGpU,EAAE,CAAAqU,iBAAA,CAk2BXqG,oBAAoB,EAAc,CAAC;IAClHpG,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAErS,EAAE,CAACsc;EAAW,CAAC,EAAE;IAAEjK,IAAI,EAAE5T,EAAE,CAAC8d;EAAe,CAAC,EAAE;IAAElK,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACrGD,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACpL,SAAS;IACpB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAM0d,6BAA6B,SAAS/F,qBAAqB,CAAC;EAC9DlJ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGvD,SAAS,CAAC;IACnB,IAAI,CAAC0L,YAAY,GAAG,YAAY;IAChC,IAAI,CAAClN,KAAK,GAAG;MACToO,UAAU,EAAE,KAAK;MACjBoB,QAAQ,EAAE,OAAO;MACjByE,MAAM,EAAE,MAAM;MACdtE,iBAAiB,EAAE,IAAI;MACvBjC,QAAQ,EAAE;QACNtN,OAAO,EAAE,GAAG;QACZgP,OAAO,EAAE;MACb,CAAC;MACDC,aAAa,EAAE,CAAC,2DAA2D,CAAC;MAC5EC,eAAe,EAAE,CAAC,uCAAuC;IAC7D,CAAC;IACD,IAAI,CAAChP,QAAQ,GAAG;MACZ8N,UAAU,EAAE,KAAK;MACjBoB,QAAQ,EAAE,UAAU;MACpByE,MAAM,EAAE,MAAM;MACdtE,iBAAiB,EAAE,IAAI;MACvBjC,QAAQ,EAAE;QACNtN,OAAO,EAAE,GAAG;QACZgP,OAAO,EAAE;MACb,CAAC;MACDC,aAAa,EAAE,CAAC,yCAAyC,CAAC;MAC1DC,eAAe,EAAE,CAAC,wCAAwC;IAC9D,CAAC;IACD,IAAI,CAAC4E,WAAW,GAAG;MACf1E,QAAQ,EAAE,cAAc;MACxByE,MAAM,EAAE,MAAM;MACdvG,QAAQ,EAAE;QACNtN,OAAO,EAAE,GAAG;QACZgP,OAAO,EAAE;MACb,CAAC;MACDC,aAAa,EAAE,CAAC,yCAAyC,CAAC;MAC1DC,eAAe,EAAE,CAAC,2DAA2D;IACjF,CAAC;IACD,IAAI,CAAC6E,SAAS,GAAG;MACb3E,QAAQ,EAAE,YAAY;MACtByE,MAAM,EAAE,KAAK;MACbvG,QAAQ,EAAE;QACNtN,OAAO,EAAE,GAAG;QACZgP,OAAO,EAAE;MACb,CAAC;MACDgF,qBAAqB,EAAE,sBAAsB;MAC7C/E,aAAa,EAAE,CAAC,yCAAyC,CAAC;MAC1DC,eAAe,EAAE,CAAC,8CAA8C;IACpE,CAAC;IACD,IAAI,CAAC7O,MAAM,GAAG;MACV2N,UAAU,EAAE,KAAK;MACjBoB,QAAQ,EAAE,QAAQ;MAClByE,MAAM,EAAE,QAAQ;MAChBvG,QAAQ,EAAE;QACNtN,OAAO,EAAE,GAAG;QACZgP,OAAO,EAAE;MACb,CAAC;MACDC,aAAa,EAAE,CAAC,yCAAyC,CAAC;MAC1DC,eAAe,EAAE,CAAC,wCAAwC;IAC9D,CAAC;IACD,IAAI,CAACzH,YAAY,GAAG;MAChB2H,QAAQ,EAAE,eAAe;MACzByE,MAAM,EAAE,MAAM;MACdtE,iBAAiB,EAAE,IAAI;MACvBjC,QAAQ,EAAE;QACNtN,OAAO,EAAE,IAAI;QACbgP,OAAO,EAAE;MACb,CAAC;MACDC,aAAa,EAAE,CAAC,yCAAyC,CAAC;MAC1DC,eAAe,EAAE,CAAC,6CAA6C;IACnE,CAAC;IACD,IAAI,CAACzJ,KAAK,GAAG;MACTsI,KAAK,EAAE7H,iBAAiB;MACxBhE,GAAG,EAAE,YAAY;MACjB+R,MAAM,EAAEA,CAACzD,MAAM,EAAEpF,GAAG,EAAEc,OAAO,KAAKpJ,iBAAiB,CAACsI,GAAG,CAACoB,IAAI,EAAEN,OAAO,CAACzG,KAAK,CAACvD,GAAG;IACnF,CAAC;IACD,IAAI,CAAC1J,MAAM,GAAG;MACV0J,GAAG,EAAE,aAAa;MAClB+R,MAAM,EAAEA,CAACzD,MAAM,EAAEpF,GAAG,EAAEc,OAAO,KAAKpJ,iBAAiB,CAACsI,GAAG,CAACnL,KAAK,EAAEiM,OAAO,CAAC1T,MAAM,CAAC0J,GAAG,EAAEgK,OAAO,CAACsE,MAAM,CAAC,CAACvB,aAAa;IACpH,CAAC;IACD,IAAI,CAACrW,QAAQ,GAAG;MACZsJ,GAAG,EAAE,eAAe;MACpB+R,MAAM,EAAEA,CAACzD,MAAM,EAAEpF,GAAG,EAAEc,OAAO,KAAKpJ,iBAAiB,CAACsI,GAAG,CAACoB,IAAI,EAAEN,OAAO,CAACtT,QAAQ,CAACsJ,GAAG,EAAEgK,OAAO,CAACsE,MAAM,CAAC,CAACtB,eAAe;IACvH,CAAC;EACL;AACJ;AACA,MAAMgF,uBAAuB,GAAG,IAAIN,6BAA6B,CAAC,CAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,sBAAsB,SAASnI,cAAc,CAAC;EAChD,OAAOmC,KAAKA,CAACjC,OAAO,EAAE;IAClB,OAAO,CAACiI,sBAAsB,EAAEjI,OAAO,CAAC;EAC5C;EACAvH,WAAWA,CAACmL,IAAI,EAAEC,KAAK,EAAE;IACrB,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC5D,cAAc,GAAG+H,uBAAuB;EACjD;EACA1I,YAAYA,CAACrE,IAAI,EAAE;IACf,MAAMqJ,MAAM,GAAG,OAAO;IACtB,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;MAAE6Q,IAAI,EAAErF,IAAI;MAAEkN,OAAO,EAAE,UAAU;MAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MACrH,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAACpF,IAAI,CAAC;MACvC;MACA,OAAOiE,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACD,SAAS,CAAC,cAAc,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAEqD,iBAAiB,CAAC,CAAC;IACtP,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACAtQ,QAAQA,CAACiH,IAAI,EAAE;IACX,MAAMqJ,MAAM,GAAG,UAAU;IACzB,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;MAAE6Q,IAAI,EAAErF,IAAI;MAAEkN,OAAO,EAAE,UAAU;MAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MACrH,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAACpF,IAAI,CAAC;MACvC;MACA,OAAOiE,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACD,SAAS,CAAC,cAAc,CAAC,CAAC,OAAO,EAAEhB,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAEqD,iBAAiB,CAAC,CAAC;IACvP,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACArQ,eAAeA,CAACgH,IAAI,EAAE;IAClB,MAAMqJ,MAAM,GAAG,aAAa;IAC5B,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,OAAO,IAAI,CAACV,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;MAAE6Q,IAAI,EAAErF,IAAI;MAAEkN,OAAO,EAAE,UAAU;MAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MACrH,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAAC,CAAC;MACnC;MACA,OAAOnB,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,CAAC;IACtJ,CAAC,CAAC,EAAEhV,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACApQ,aAAaA,CAAC+G,IAAI,GAAG,CAAC,CAAC,EAAE;IACrB,MAAMqJ,MAAM,GAAG,WAAW;IAC1B,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAM8D,QAAQ,GAAG,IAAI,CAAClI,SAAS,CAAC,GAAGoE,MAAM,wBAAwB,CAAC;IAClErJ,IAAI,CAACmN,QAAQ,CAAC,GAAG,IAAI,CAACvE,KAAK,CAACG,QAAQ,CAACC,WAAW,CAACmE,QAAQ,CAAC;IAC1D,OAAO,IAAI,CAACxE,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;MAAE6Q,IAAI,EAAErF,IAAI;MAAEkN,OAAO,EAAE,UAAU;MAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MACrH,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAAC,CAAC;MACnC;MACA,OAAOnB,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,CAAC;IACtJ,CAAC,CAAC,EAAEhV,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACAnQ,MAAMA,CAAA,EAAG;IACL,MAAMmQ,MAAM,GAAG,QAAQ;IACvB,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,OAAO7Z,EAAE,CAAC,CAAC,CAAC,CAAC,CAACiU,IAAI,CAAC5T,SAAS,CAAEoU,GAAG,IAAK;MAClC,IAAI,CAACzP,GAAG,EAAE;QACN,OAAOhF,EAAE,CAACyU,GAAG,CAAC;MAClB;MACA,OAAO,IAAI,CAAC0E,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;QAAE0Y,OAAO,EAAE,UAAU;QAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;MAAE,CAAC,CAAC;IAC9F,CAAC,CAAC,EAAEhW,GAAG,CAAEqU,GAAG,IAAK;MACb,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAAC,CAAC;MACnC;MACA,OAAOnB,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,CAAC;IACtJ,CAAC,CAAC,EAAEhV,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACA/I,YAAYA,CAACN,IAAI,EAAE;IACf,MAAMqJ,MAAM,GAAG,cAAc;IAC7B,MAAMqD,MAAM,GAAG,IAAI,CAACzH,SAAS,CAAC,GAAGoE,MAAM,SAAS,CAAC;IACjD,MAAM7U,GAAG,GAAG,IAAI,CAACgR,iBAAiB,CAAC6D,MAAM,CAAC;IAC1C,MAAMjB,iBAAiB,GAAG,IAAI,CAACnD,SAAS,CAAC,GAAGoE,MAAM,oBAAoB,CAAC;IACvE,OAAO,IAAI,CAACV,IAAI,CAACsE,OAAO,CAACP,MAAM,EAAElY,GAAG,EAAE;MAAE6Q,IAAI,EAAErF,IAAI;MAAEkN,OAAO,EAAE,UAAU;MAAEpH,OAAO,EAAE,IAAI,CAACF,UAAU,CAAC;IAAE,CAAC,CAAC,CAACnC,IAAI,CAAC7T,GAAG,CAAEqU,GAAG,IAAK;MACrH,IAAI,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,aAAa,CAAC,EAAE;QACxC,MAAM,IAAI,CAACjE,kBAAkB,CAACpF,IAAI,CAAC;MACvC;MACA,OAAOiE,GAAG;IACd,CAAC,CAAC,EAAErU,GAAG,CAAEqU,GAAG,IAAK;MACb,OAAO,IAAIgC,YAAY,CAAC,IAAI,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAE,EAAE,EAAE,IAAI,CAACpE,SAAS,CAAC,iBAAiB,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACD,SAAS,CAAC,cAAc,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC,EAAEqD,iBAAiB,CAAC,CAAC;IACtP,CAAC,CAAC,EAAErY,UAAU,CAAEkU,GAAG,IAAK;MACpB,OAAO,IAAI,CAACkG,mBAAmB,CAAClG,GAAG,EAAEoF,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC;EACP;EACAc,mBAAmBA,CAAClG,GAAG,EAAEoF,MAAM,EAAE;IAC7B,IAAIhY,MAAM,GAAG,EAAE;IACf,IAAI4S,GAAG,YAAY7T,iBAAiB,EAAE;MAClCiB,MAAM,GAAG,IAAI,CAAC4T,SAAS,CAAC,eAAe,CAAC,CAACoE,MAAM,EAAEpF,GAAG,EAAE,IAAI,CAACc,OAAO,CAAC;IACvE,CAAC,MACI,IAAId,GAAG,YAAYhG,uBAAuB,EAAE;MAC7C5M,MAAM,CAACmY,IAAI,CAACvF,GAAG,CAACnG,OAAO,CAAC;IAC5B,CAAC,MACI;MACDzM,MAAM,CAACmY,IAAI,CAAC,uBAAuB,CAAC;IACxC;IACA,OAAOha,EAAE,CAAC,IAAIyW,YAAY,CAAC,KAAK,EAAEhC,GAAG,EAAE,IAAI,CAACgB,SAAS,CAAC,GAAGoE,MAAM,mBAAmB,CAAC,EAAEhY,MAAM,CAAC,CAAC;EACjG;EACA;IAAS,IAAI,CAACwQ,IAAI,YAAAuL,+BAAArL,iBAAA;MAAA,YAAAA,iBAAA,IAAwFiL,sBAAsB,EAlsChChf,EAAE,CAAAgU,QAAA,CAksCgD/R,EAAE,CAACsc,UAAU,GAlsC/Dve,EAAE,CAAAgU,QAAA,CAksC0EtT,EAAE,CAAC8d,cAAc;IAAA,CAA6C;EAAE;EAC5O;IAAS,IAAI,CAACvK,KAAK,kBAnsC6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EAmsCY0O,sBAAsB;MAAA7K,OAAA,EAAtB6K,sBAAsB,CAAAnL;IAAA,EAAG;EAAE;AAC7I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KArsCoGpU,EAAE,CAAAqU,iBAAA,CAqsCX2K,sBAAsB,EAAc,CAAC;IACpH1K,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAErS,EAAE,CAACsc;EAAW,CAAC,EAAE;IAAEjK,IAAI,EAAE5T,EAAE,CAAC8d;EAAe,CAAC,CAAC;AAAA;;AAExF;AACA;AACA;AACA;AACA;AACA,MAAMa,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACxL,IAAI,YAAAyL,6BAAAvL,iBAAA;MAAA,YAAAA,iBAAA,IAAwFsL,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACE,IAAI,kBAhtC8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EAgtCJ+K,oBAAoB;MAAAI,SAAA;MAAAC,kBAAA,EAAArd,GAAA;MAAAsd,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAvd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhtClBvC,EAAE,CAAA+f,eAAA;UAAF/f,EAAE,CAAAggB,YAAA,EAitC1E,CAAC;QAAA;MAAA;MAAAC,MAAA;IAAA,EAC45C;EAAE;AAC37C;AACA;EAAA,QAAA7L,SAAA,oBAAAA,SAAA,KAptCoGpU,EAAE,CAAAqU,iBAAA,CAotCXgL,oBAAoB,EAAc,CAAC;IAClH/K,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,eAAe;MAAEL,QAAQ,EAAE;AAC1D;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,u5CAAu5C;IAAE,CAAC;EACh6C,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClB;EACA3Q,WAAWA,CAAC4Q,IAAI,EAAE9D,QAAQ,EAAE;IACxB,IAAI,CAAC8D,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC9D,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC+D,QAAQ,GAAG,IAAI5e,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC6e,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAChQ,KAAK,GAAG,EAAE;IACf,IAAI,CAACiQ,YAAY,GAAGH,IAAI,CAAChK,sBAAsB,CAAC,CAAC,CAC5CX,IAAI,CAACzT,SAAS,CAAC,IAAI,CAACqe,QAAQ,CAAC,CAAC,CAC9BG,SAAS,CAAEF,aAAa,IAAK;MAC9B,IAAI,CAACA,aAAa,GAAGA,aAAa;IACtC,CAAC,CAAC;EACN;EACAG,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnE,QAAQ,CAACmE,IAAI,CAAC,CAAC;IACpB,OAAO,KAAK;EAChB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,QAAQ,CAAC3K,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC2K,QAAQ,CAACM,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC9M,IAAI,YAAA+M,wBAAA7M,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoM,eAAe,EAtvCzBngB,EAAE,CAAA6gB,iBAAA,CAsvCyCjL,aAAa,GAtvCxD5V,EAAE,CAAA6gB,iBAAA,CAsvCmErgB,EAAE,CAACsgB,QAAQ;IAAA,CAA4C;EAAE;EAC9N;IAAS,IAAI,CAACvB,IAAI,kBAvvC8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EAuvCJ6L,eAAe;MAAAV,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAmB,MAAA;MAAAlB,QAAA,WAAAmB,yBAAAze,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvvCbvC,EAAE,CAAAyC,cAAA,eAwvCxF,CAAC,sBACQ,CAAC,aACR,CAAC,oBACQ,CAAC,YACS,CAAC,UAC+C,CAAC;UA7vCgBzC,EAAE,CAAAihB,UAAA,mBAAAC,4CAAA;YAAA,OA6vCnE1e,GAAA,CAAAie,IAAA,CAAK,CAAC;UAAA,EAAC;UA7vC0DzgB,EAAE,CAAAwF,SAAA,gBA8vClD,CAAC;UA9vC+CxF,EAAE,CAAA2C,YAAA,CA+vCrF,CAAC,CACD,CAAC,CACQ,CAAC;UAjwCyE3C,EAAE,CAAAyC,cAAA,kBAkwC/E,CAAC,mBACE,CAAC;UAnwCyEzC,EAAE,CAAAwF,SAAA,mBAowC1D,CAAC;UApwCuDxF,EAAE,CAAA2C,YAAA,CAqwC3E,CAAC,CACJ,CAAC,CACR,CAAC,CACM,CAAC,CACV,CAAC;QAAA;MAAA;MAAAwe,YAAA,GACi0IrgB,EAAE,CAACsgB,iBAAiB,EAA0HtgB,EAAE,CAACugB,uBAAuB,EAAwFvgB,EAAE,CAACwgB,eAAe,EAA0FxgB,EAAE,CAACygB,mBAAmB,EAAyDzgB,EAAE,CAAC0gB,qBAAqB,EAA2D9gB,EAAE,CAAC+gB,YAAY,EAA2J3gB,EAAE,CAAC4gB,eAAe,EAA6GrC,oBAAoB;MAAAY,MAAA;IAAA,EAAiC;EAAE;AAC/rK;AACA;EAAA,QAAA7L,SAAA,oBAAAA,SAAA,KA5wCoGpU,EAAE,CAAAqU,iBAAA,CA4wCX8L,eAAe,EAAc,CAAC;IAC7G7L,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,SAAS;MAAEL,QAAQ,EAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MAAEI,MAAM,EAAE,CAAC,swIAAswI;IAAE,CAAC;EAC/wI,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3L,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAE9T,EAAE,CAACsgB;EAAS,CAAC,CAAC;AAAA;;AAElF;AACA;AACA;AACA;AACA;AACA,MAAMa,gBAAgB,CAAC;EACnBnS,WAAWA,CAACoS,OAAO,EAAE7K,OAAO,GAAG,CAAC,CAAC,EAAE8K,EAAE,EAAEC,MAAM,EAAE;IAC3C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8K,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACpX,aAAa,GAAG,CAAC;IACtB,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACtH,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC0B,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAAC4c,SAAS,GAAG,KAAK;IACtB,IAAI,CAACnb,WAAW,GAAG,EAAE;IACrB,IAAI,CAACxB,UAAU,GAAG,KAAK;IACvB,IAAI,CAACsF,aAAa,GAAG,IAAI,CAACpG,cAAc,CAAC,2BAA2B,CAAC;IACrE,IAAI,CAACsG,YAAY,GAAG,IAAI,CAACtG,cAAc,CAAC,0BAA0B,CAAC;IACnE,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACrG,cAAc,CAAC,sBAAsB,CAAC;IAC3D,IAAI,CAACsC,WAAW,GAAG,IAAI,CAACtC,cAAc,CAAC,yBAAyB,CAAC;IACjE,IAAI,CAACc,UAAU,GAAG,IAAI,CAACd,cAAc,CAAC,wBAAwB,CAAC;EACnE;EACAmG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACpH,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACse,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,OAAO,CAACvL,YAAY,CAAC,IAAI,CAAC1L,QAAQ,EAAE,IAAI,CAACxF,IAAI,CAAC,CAACqb,SAAS,CAAEhK,MAAM,IAAK;MACtE,IAAI,CAACuL,SAAS,GAAG,KAAK;MACtB,IAAIvL,MAAM,CAACN,SAAS,CAAC,CAAC,EAAE;QACpB,IAAI,CAACzS,QAAQ,GAAG+S,MAAM,CAACgC,WAAW,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACnV,MAAM,GAAGmT,MAAM,CAAC+B,SAAS,CAAC,CAAC;MACpC;MACA,MAAMJ,QAAQ,GAAG3B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACrC,IAAIH,QAAQ,EAAE;QACV6J,UAAU,CAAC,MAAM;UACb,OAAO,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC9J,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAACzN,aAAa,CAAC;MAC1B;MACA,IAAI,CAACmX,EAAE,CAACK,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA5d,cAAcA,CAACyI,GAAG,EAAE;IAChB,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAsO,yBAAApO,iBAAA;MAAA,YAAAA,iBAAA,IAAwF4N,gBAAgB,EAt1C1B3hB,EAAE,CAAA6gB,iBAAA,CAs1C0CjL,aAAa,GAt1CzD5V,EAAE,CAAA6gB,iBAAA,CAs1CoEpV,eAAe,GAt1CrFzL,EAAE,CAAA6gB,iBAAA,CAs1CgG7gB,EAAE,CAACoiB,iBAAiB,GAt1CtHpiB,EAAE,CAAA6gB,iBAAA,CAs1CiIngB,EAAE,CAAC2hB,MAAM;IAAA,CAA4C;EAAE;EAC1R;IAAS,IAAI,CAAC9C,IAAI,kBAv1C8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EAu1CJqN,gBAAgB;MAAAlC,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAmB,MAAA;MAAAlB,QAAA,WAAAyC,0BAAA/f,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAggB,GAAA,GAv1CdviB,EAAE,CAAA6E,gBAAA;UAAF7E,EAAE,CAAAyC,cAAA,WAu1C6F,CAAC;UAv1ChGzC,EAAE,CAAA0C,MAAA,WAu1CkG,CAAC;UAv1CrG1C,EAAE,CAAA2C,YAAA,CAu1CuG,CAAC;UAv1C1G3C,EAAE,CAAAyC,cAAA,UAu1CgI,CAAC;UAv1CnIzC,EAAE,CAAA0C,MAAA,oCAu1C8J,CAAC;UAv1CjK1C,EAAE,CAAA2C,YAAA,CAu1CkK,CAAC;UAv1CrK3C,EAAE,CAAAiD,UAAA,IAAAD,oCAAA,qBAu1C+Q,CAAC,IAAAQ,oCAAA,qBAA6S,CAAC;UAv1ChkBxD,EAAE,CAAAyC,cAAA,gBAu1C00B,CAAC;UAv1C70BzC,EAAE,CAAAihB,UAAA,sBAAAuB,mDAAA;YAAFxiB,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAA,OAAFviB,EAAE,CAAAqF,WAAA,CAu1CsxB7C,GAAA,CAAAiI,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC;UAv1CjyBzK,EAAE,CAAAyC,cAAA,YAu1Ck3B,CAAC,eAAgD,CAAC;UAv1Ct6BzC,EAAE,CAAA0C,MAAA,qBAu1Ci7B,CAAC;UAv1Cp7B1C,EAAE,CAAA2C,YAAA,CAu1Cy7B,CAAC;UAv1C57B3C,EAAE,CAAAyC,cAAA,mBAu1C89C,CAAC;UAv1Cj+CzC,EAAE,CAAA8E,gBAAA,2BAAA2d,0DAAAzd,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,EAAAvG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,GAAAvG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAu1C0gC,CAAC;UAv1C7gChF,EAAE,CAAA2C,YAAA,CAu1C89C,CAAC;UAv1Cj+C3C,EAAE,CAAAiD,UAAA,KAAAW,yCAAA,0BAu1C2hD,CAAC;UAv1C9hD5D,EAAE,CAAA2C,YAAA,CAu1C4yD,CAAC;UAv1C/yD3C,EAAE,CAAAyC,cAAA,aAu1Co1D,CAAC,eAAqC,CAAC,gBAAqD,CAAC;UAv1Cn7DzC,EAAE,CAAA0C,MAAA,gBAu1Cy7D,CAAC;UAv1C57D1C,EAAE,CAAA2C,YAAA,CAu1Ci8D,CAAC;UAv1Cp8D3C,EAAE,CAAAyC,cAAA,YAu1CmhE,CAAC;UAv1CthEzC,EAAE,CAAA0C,MAAA,uBAu1CmiE,CAAC;UAv1CtiE1C,EAAE,CAAA2C,YAAA,CAu1CuiE,CAAC,CAAY,CAAC;UAv1CvjE3C,EAAE,CAAAyC,cAAA,mBAu1CwvF,CAAC;UAv1C3vFzC,EAAE,CAAA8E,gBAAA,2BAAA4d,0DAAA1d,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,EAAApG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,GAAApG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAu1CwoE,CAAC;UAv1C3oEhF,EAAE,CAAA2C,YAAA,CAu1CwvF,CAAC;UAv1C3vF3C,EAAE,CAAAiD,UAAA,KAAAsB,yCAAA,0BAu1C4zF,CAAC;UAv1C/zFvE,EAAE,CAAA2C,YAAA,CAu1CsxG,CAAC;UAv1CzxG3C,EAAE,CAAAyC,cAAA,cAu1C20G,CAAC;UAv1C90GzC,EAAE,CAAAiD,UAAA,KAAA0B,wCAAA,yBAu1Cu6G,CAAC;UAv1C16G3E,EAAE,CAAA2C,YAAA,CAu1C08G,CAAC;UAv1C78G3C,EAAE,CAAAyC,cAAA,iBAu1C4oH,CAAC;UAv1C/oHzC,EAAE,CAAA0C,MAAA,eAu1C4pH,CAAC;UAv1C/pH1C,EAAE,CAAA2C,YAAA,CAu1CqqH,CAAC,CAAQ,CAAC;UAv1CjrH3C,EAAE,CAAAiD,UAAA,KAAA0D,oCAAA,qBAu1CyxH,CAAC;UAv1C5xH3G,EAAE,CAAAyC,cAAA,kBAu1CmvJ,CAAC;UAv1CtvJzC,EAAE,CAAA0C,MAAA,+BAu1C8wJ,CAAC;UAv1CjxJ1C,EAAE,CAAAyC,cAAA,YAu1Cg0J,CAAC;UAv1Cn0JzC,EAAE,CAAA0C,MAAA,eAu1Cw0J,CAAC;UAv1C30J1C,EAAE,CAAA2C,YAAA,CAu1C40J,CAAC,CAAW,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAAA,MAAAogB,QAAA,GAv1C31J3iB,EAAE,CAAAgE,WAAA;UAAA,MAAAD,QAAA,GAAF/D,EAAE,CAAAgE,WAAA;UAAA,MAAAQ,WAAA,GAAFxE,EAAE,CAAAgE,WAAA;UAAFhE,EAAE,CAAA8C,SAAA,EAu1C0O,CAAC;UAv1C7O9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAE,KAAA,KAAAtI,GAAA,CAAAa,MAAA,kBAAAb,GAAA,CAAAa,MAAA,CAAA6I,MAAA,MAAA1J,GAAA,CAAAuf,SAu1C0O,CAAC;UAv1C7O/hB,EAAE,CAAA8C,SAAA,CAu1CuhB,CAAC;UAv1C1hB9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAC,OAAA,KAAArI,GAAA,CAAAiB,QAAA,kBAAAjB,GAAA,CAAAiB,QAAA,CAAAyI,MAAA,MAAA1J,GAAA,CAAAuf,SAu1CuhB,CAAC;UAv1C1hB/hB,EAAE,CAAA8C,SAAA,EAu1C0gC,CAAC;UAv1C7gC9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAoG,KAu1C0gC,CAAC;UAv1C7gCvL,EAAE,CAAAoD,UAAA,WAAAW,QAAA,CAAA6e,KAAA,GAAA7e,QAAA,CAAAqG,OAAA,iCAu1C+zC,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,mCAA4E,CAAC;UAv1C/4CtE,EAAE,CAAAkG,WAAA,iBAAAnC,QAAA,CAAAqG,OAAA,IAAArG,QAAA,CAAA8e,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAu1CwhD,CAAC;UAv1C3hD9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAqG,OAAA,IAAArG,QAAA,CAAA8e,OAu1CwhD,CAAC;UAv1C3hD7iB,EAAE,CAAA8C,SAAA,EAu1CwoE,CAAC;UAv1C3oE9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAiG,QAu1CwoE,CAAC;UAv1C3oEpL,EAAE,CAAAoD,UAAA,WAAAoB,WAAA,CAAAoe,KAAA,GAAApe,WAAA,CAAA4F,OAAA,iCAu1C46E,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,sCAA+E,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC;UAv1CnqFtE,EAAE,CAAAkG,WAAA,iBAAA1B,WAAA,CAAA4F,OAAA,IAAA5F,WAAA,CAAAqe,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAu1CwzF,CAAC;UAv1C3zF9C,EAAE,CAAAoD,UAAA,SAAAoB,WAAA,CAAA4F,OAAA,IAAA5F,WAAA,CAAAqe,OAu1CwzF,CAAC;UAv1C3zF7iB,EAAE,CAAA8C,SAAA,EAu1Co6G,CAAC;UAv1Cv6G9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAA4C,UAu1Co6G,CAAC;UAv1Cv6GpF,EAAE,CAAA8C,SAAA,CAu1C2oH,CAAC;UAv1C9oH9C,EAAE,CAAAgG,WAAA,cAAAxD,GAAA,CAAAuf,SAu1C2oH,CAAC;UAv1C9oH/hB,EAAE,CAAAoD,UAAA,aAAAZ,GAAA,CAAAuf,SAAA,KAAAY,QAAA,CAAAG,KAu1CgmH,CAAC;UAv1CnmH9iB,EAAE,CAAA8C,SAAA,EAu1CwuH,CAAC;UAv1C3uH9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoE,WAAA,IAAApE,GAAA,CAAAoE,WAAA,CAAAsF,MAAA,IAu1CwuH,CAAC;QAAA;MAAA;MAAAiV,YAAA,GAA+pC3gB,EAAE,CAACuiB,OAAO,EAAmHviB,EAAE,CAACwiB,IAAI,EAA6FliB,EAAE,CAACmiB,mBAAmB,EAAgJniB,EAAE,CAACoiB,gBAAgB,EAAsIpiB,EAAE,CAACqiB,gBAAgB,EAAoIriB,EAAE,CAACsiB,iBAAiB,EAA6J1iB,EAAE,CAAC2iB,UAAU,EAAoOziB,EAAE,CAAC0iB,aAAa,EAAyF1iB,EAAE,CAAC2iB,oBAAoB,EAAyP3iB,EAAE,CAAC4iB,eAAe,EAAsF5iB,EAAE,CAAC6iB,oBAAoB,EAAqI7iB,EAAE,CAAC8iB,iBAAiB,EAAyM9iB,EAAE,CAAC+iB,kBAAkB,EAA8I/iB,EAAE,CAACgjB,kBAAkB,EAA8IhjB,EAAE,CAACijB,gBAAgB,EAAsIjjB,EAAE,CAACkjB,OAAO,EAA8MljB,EAAE,CAACmjB,MAAM,EAA2KjjB,EAAE,CAAC4gB,eAAe;MAAAsC,aAAA;MAAAC,eAAA;IAAA,EAAwI;EAAE;AACx6P;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KAz1CoGpU,EAAE,CAAAqU,iBAAA,CAy1CXsN,gBAAgB,EAAc,CAAC;IAC9GrN,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,UAAU;MAAE+D,eAAe,EAAE5jB,uBAAuB,CAAC6jB,MAAM;MAAErE,QAAQ,EAAE;IAAiyJ,CAAC;EACh4J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvL,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACxED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACV,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEtU,EAAE,CAACoiB;EAAkB,CAAC,EAAE;IAAE9N,IAAI,EAAE5T,EAAE,CAAC2hB;EAAO,CAAC,CAAC;AAAA;;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAM8B,mBAAmB,CAAC;EACtB3U,WAAWA,CAACoS,OAAO,EAAE7K,OAAO,GAAG,CAAC,CAAC,EAAE8K,EAAE,EAAEC,MAAM,EAAE;IAC3C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8K,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACpX,aAAa,GAAG,CAAC;IACtB,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACoX,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC1e,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC0B,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACyB,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC8D,aAAa,GAAG,IAAI,CAACpG,cAAc,CAAC,8BAA8B,CAAC;IACxE,IAAI,CAACsG,YAAY,GAAG,IAAI,CAACtG,cAAc,CAAC,6BAA6B,CAAC;IACtE,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACrG,cAAc,CAAC,yBAAyB,CAAC;IAC9D,IAAI,CAACsC,WAAW,GAAG,IAAI,CAACtC,cAAc,CAAC,yBAAyB,CAAC;EACrE;EACAyG,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1H,MAAM,GAAG,IAAI,CAACI,QAAQ,GAAG,EAAE;IAChC,IAAI,CAACse,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,OAAO,CAAC7W,QAAQ,CAAC,IAAI,CAACJ,QAAQ,EAAE,IAAI,CAACxF,IAAI,CAAC,CAACqb,SAAS,CAAEhK,MAAM,IAAK;MAClE,IAAI,CAACuL,SAAS,GAAG,KAAK;MACtB,IAAIvL,MAAM,CAACN,SAAS,CAAC,CAAC,EAAE;QACpB,IAAI,CAACzS,QAAQ,GAAG+S,MAAM,CAACgC,WAAW,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACnV,MAAM,GAAGmT,MAAM,CAAC+B,SAAS,CAAC,CAAC;MACpC;MACA,MAAMJ,QAAQ,GAAG3B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACrC,IAAIH,QAAQ,EAAE;QACV6J,UAAU,CAAC,MAAM;UACb,OAAO,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC9J,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAACzN,aAAa,CAAC;MAC1B;MACA,IAAI,CAACmX,EAAE,CAACK,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA5d,cAAcA,CAACyI,GAAG,EAAE;IAChB,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAuQ,4BAAArQ,iBAAA;MAAA,YAAAA,iBAAA,IAAwFoQ,mBAAmB,EAh5C7BnkB,EAAE,CAAA6gB,iBAAA,CAg5C6CjL,aAAa,GAh5C5D5V,EAAE,CAAA6gB,iBAAA,CAg5CuEpV,eAAe,GAh5CxFzL,EAAE,CAAA6gB,iBAAA,CAg5CmG7gB,EAAE,CAACoiB,iBAAiB,GAh5CzHpiB,EAAE,CAAA6gB,iBAAA,CAg5CoIngB,EAAE,CAAC2hB,MAAM;IAAA,CAA4C;EAAE;EAC7R;IAAS,IAAI,CAAC9C,IAAI,kBAj5C8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EAi5CJ6P,mBAAmB;MAAA1E,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAmB,MAAA;MAAAlB,QAAA,WAAAwE,6BAAA9hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAggB,GAAA,GAj5CjBviB,EAAE,CAAA6E,gBAAA;UAAF7E,EAAE,CAAAyC,cAAA,WAi5CmG,CAAC;UAj5CtGzC,EAAE,CAAA0C,MAAA,cAi5C2G,CAAC;UAj5C9G1C,EAAE,CAAA2C,YAAA,CAi5CgH,CAAC;UAj5CnH3C,EAAE,CAAAiD,UAAA,IAAA6D,uCAAA,qBAi5C6N,CAAC,IAAAE,uCAAA,qBAA6S,CAAC;UAj5C9gBhH,EAAE,CAAAyC,cAAA,gBAi5C2xB,CAAC;UAj5C9xBzC,EAAE,CAAAihB,UAAA,sBAAAqD,sDAAA;YAAFtkB,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAA,OAAFviB,EAAE,CAAAqF,WAAA,CAi5CouB7C,GAAA,CAAAuI,QAAA,CAAS,CAAC;UAAA,CAAC,CAAC;UAj5ClvB/K,EAAE,CAAAyC,cAAA,aAi5Cm0B,CAAC,eAA+C,CAAC;UAj5Ct3BzC,EAAE,CAAA0C,MAAA,gBAi5C63B,CAAC;UAj5Ch4B1C,EAAE,CAAA2C,YAAA,CAi5Cq4B,CAAC;UAj5Cx4B3C,EAAE,CAAAyC,cAAA,kBAi5C8jD,CAAC;UAj5CjkDzC,EAAE,CAAA8E,gBAAA,2BAAAyf,4DAAAvf,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAqG,QAAA,EAAAxG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAqG,QAAA,GAAAxG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAi5Cm8B,CAAC;UAj5Ct8BhF,EAAE,CAAA2C,YAAA,CAi5C8jD,CAAC;UAj5CjkD3C,EAAE,CAAAiD,UAAA,KAAAkE,4CAAA,0BAi5CioD,CAAC;UAj5CpoDnH,EAAE,CAAA2C,YAAA,CAi5C0lE,CAAC;UAj5C7lE3C,EAAE,CAAAyC,cAAA,cAi5CkoE,CAAC,gBAAgD,CAAC;UAj5CtrEzC,EAAE,CAAA0C,MAAA,qBAi5CisE,CAAC;UAj5CpsE1C,EAAE,CAAA2C,YAAA,CAi5CysE,CAAC;UAj5C5sE3C,EAAE,CAAAyC,cAAA,mBAi5CstF,CAAC;UAj5CztFzC,EAAE,CAAA8E,gBAAA,2BAAA0f,6DAAAxf,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,EAAAvG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,GAAAvG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAi5CowE,CAAC;UAj5CvwEhF,EAAE,CAAA2C,YAAA,CAi5CstF,CAAC;UAj5CztF3C,EAAE,CAAAiD,UAAA,KAAAsE,4CAAA,0BAi5CmxF,CAAC;UAj5CtxFvH,EAAE,CAAA2C,YAAA,CAi5CoiG,CAAC;UAj5CviG3C,EAAE,CAAAyC,cAAA,cAi5C4kG,CAAC,gBAAmD,CAAC;UAj5CnoGzC,EAAE,CAAA0C,MAAA,gBAi5CyoG,CAAC;UAj5C5oG1C,EAAE,CAAA2C,YAAA,CAi5CipG,CAAC;UAj5CppG3C,EAAE,CAAAyC,cAAA,mBAi5Cq1H,CAAC;UAj5Cx1HzC,EAAE,CAAA8E,gBAAA,2BAAA2f,6DAAAzf,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,EAAApG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,GAAApG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAi5C+sG,CAAC;UAj5CltGhF,EAAE,CAAA2C,YAAA,CAi5Cq1H,CAAC;UAj5Cx1H3C,EAAE,CAAAiD,UAAA,KAAA0E,4CAAA,0BAi5Cw5H,CAAC;UAj5C35H3H,EAAE,CAAA2C,YAAA,CAi5Ck3I,CAAC;UAj5Cr3I3C,EAAE,CAAAyC,cAAA,cAi5C05I,CAAC,gBAAsD,CAAC;UAj5Cp9IzC,EAAE,CAAA0C,MAAA,uBAi5Ci+I,CAAC;UAj5Cp+I1C,EAAE,CAAA2C,YAAA,CAi5Cy+I,CAAC;UAj5C5+I3C,EAAE,CAAAyC,cAAA,mBAi5CijK,CAAC;UAj5CpjKzC,EAAE,CAAA8E,gBAAA,2BAAA4f,6DAAA1f,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAwf,eAAA,EAAA3f,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAwf,eAAA,GAAA3f,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAi5C8iJ,CAAC;UAj5CjjJhF,EAAE,CAAA2C,YAAA,CAi5CijK,CAAC;UAj5CpjK3C,EAAE,CAAAiD,UAAA,KAAA8E,4CAAA,0BAi5CgnK,CAAC;UAj5CnnK/H,EAAE,CAAA2C,YAAA,CAi5Cu8K,CAAC;UAj5C18K3C,EAAE,CAAAiD,UAAA,KAAAiF,mCAAA,iBAi5C6iL,CAAC;UAj5ChjLlI,EAAE,CAAAyC,cAAA,iBAi5Cw9L,CAAC;UAj5C39LzC,EAAE,CAAA0C,MAAA,iBAi5C0+L,CAAC;UAj5C7+L1C,EAAE,CAAA2C,YAAA,CAi5Cm/L,CAAC,CAAQ,CAAC;UAj5C//L3C,EAAE,CAAAiD,UAAA,KAAA+F,uCAAA,qBAi5CumM,CAAC;UAj5C1mMhJ,EAAE,CAAAyC,cAAA,kBAi5CgkO,CAAC;UAj5CnkOzC,EAAE,CAAA0C,MAAA,iCAi5C6lO,CAAC;UAj5ChmO1C,EAAE,CAAAyC,cAAA,YAi5C4oO,CAAC;UAj5C/oOzC,EAAE,CAAA0C,MAAA,aAi5CkpO,CAAC;UAj5CrpO1C,EAAE,CAAA2C,YAAA,CAi5CspO,CAAC,CAAW,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAAA,MAAAqiB,QAAA,GAj5CrqO5kB,EAAE,CAAAgE,WAAA;UAAA,MAAAoD,WAAA,GAAFpH,EAAE,CAAAgE,WAAA;UAAA,MAAAwD,QAAA,GAAFxH,EAAE,CAAAgE,WAAA;UAAA,MAAA4D,WAAA,GAAF5H,EAAE,CAAAgE,WAAA;UAAA,MAAAgE,SAAA,GAAFhI,EAAE,CAAAgE,WAAA;UAAFhE,EAAE,CAAA8C,SAAA,EAi5CwL,CAAC;UAj5C3L9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAE,KAAA,KAAAtI,GAAA,CAAAa,MAAA,kBAAAb,GAAA,CAAAa,MAAA,CAAA6I,MAAA,MAAA1J,GAAA,CAAAuf,SAi5CwL,CAAC;UAj5C3L/hB,EAAE,CAAA8C,SAAA,CAi5Cqe,CAAC;UAj5Cxe9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAC,OAAA,KAAArI,GAAA,CAAAiB,QAAA,kBAAAjB,GAAA,CAAAiB,QAAA,CAAAyI,MAAA,MAAA1J,GAAA,CAAAuf,SAi5Cqe,CAAC;UAj5Cxe/hB,EAAE,CAAA8C,SAAA,EAi5Cm8B,CAAC;UAj5Ct8B9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAqG,QAi5Cm8B,CAAC;UAj5Ct8BxL,EAAE,CAAAoD,UAAA,WAAAgE,WAAA,CAAAwb,KAAA,GAAAxb,WAAA,CAAAgD,OAAA,iCAi5CkvC,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,sCAA+E,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC;UAj5Cz+CtE,EAAE,CAAAkG,WAAA,iBAAAkB,WAAA,CAAAgD,OAAA,IAAAhD,WAAA,CAAAyb,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAi5C8nD,CAAC;UAj5CjoD9C,EAAE,CAAAoD,UAAA,SAAAgE,WAAA,CAAAgD,OAAA,IAAAhD,WAAA,CAAAyb,OAi5C8nD,CAAC;UAj5CjoD7iB,EAAE,CAAA8C,SAAA,EAi5CowE,CAAC;UAj5CvwE9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAoG,KAi5CowE,CAAC;UAj5CvwEvL,EAAE,CAAAoD,UAAA,WAAAoE,QAAA,CAAAob,KAAA,GAAApb,QAAA,CAAA4C,OAAA,iCAi5CujF,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,mCAA4E,CAAC;UAj5CvoFtE,EAAE,CAAAkG,WAAA,iBAAAsB,QAAA,CAAA4C,OAAA,IAAA5C,QAAA,CAAAqb,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAi5CgxF,CAAC;UAj5CnxF9C,EAAE,CAAAoD,UAAA,SAAAoE,QAAA,CAAA4C,OAAA,IAAA5C,QAAA,CAAAqb,OAi5CgxF,CAAC;UAj5CnxF7iB,EAAE,CAAA8C,SAAA,EAi5C+sG,CAAC;UAj5CltG9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAiG,QAi5C+sG,CAAC;UAj5CltGpL,EAAE,CAAAoD,UAAA,WAAAwE,WAAA,CAAAgb,KAAA,GAAAhb,WAAA,CAAAwC,OAAA,iCAi5CygH,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,sCAA+E,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC;UAj5ChwHtE,EAAE,CAAAkG,WAAA,iBAAA0B,WAAA,CAAAwC,OAAA,IAAAxC,WAAA,CAAAib,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAi5Cq5H,CAAC;UAj5Cx5H9C,EAAE,CAAAoD,UAAA,SAAAwE,WAAA,CAAAwC,OAAA,IAAAxC,WAAA,CAAAib,OAi5Cq5H,CAAC;UAj5Cx5H7iB,EAAE,CAAA8C,SAAA,EAi5C8iJ,CAAC;UAj5CjjJ9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAwf,eAi5C8iJ,CAAC;UAj5CjjJ3kB,EAAE,CAAAoD,UAAA,WAAA4E,SAAA,CAAA4a,KAAA,GAAA5a,SAAA,CAAAoC,OAAA,IAAAxC,WAAA,CAAAK,KAAA,IAAAD,SAAA,CAAAC,KAAA,iCAi5C64J,CAAC,aAAAzF,GAAA,CAAA8B,cAAA,sCAA+E,CAAC;UAj5Ch+JtE,EAAE,CAAAkG,WAAA,iBAAA8B,SAAA,CAAAoC,OAAA,IAAApC,SAAA,CAAA6a,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAi5C6mK,CAAC;UAj5ChnK9C,EAAE,CAAAoD,UAAA,SAAA4E,SAAA,CAAAoC,OAAA,IAAApC,SAAA,CAAA6a,OAi5C6mK,CAAC;UAj5ChnK7iB,EAAE,CAAA8C,SAAA,CAi5C0iL,CAAC;UAj5C7iL9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAA8B,cAAA,wBAi5C0iL,CAAC;UAj5C7iLtE,EAAE,CAAA8C,SAAA,CAi5Cu9L,CAAC;UAj5C19L9C,EAAE,CAAAgG,WAAA,cAAAxD,GAAA,CAAAuf,SAi5Cu9L,CAAC;UAj5C19L/hB,EAAE,CAAAoD,UAAA,aAAAZ,GAAA,CAAAuf,SAAA,KAAA6C,QAAA,CAAA9B,KAi5C46L,CAAC;UAj5C/6L9iB,EAAE,CAAA8C,SAAA,EAi5CsjM,CAAC;UAj5CzjM9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoE,WAAA,IAAApE,GAAA,CAAAoE,WAAA,CAAAsF,MAAA,IAi5CsjM,CAAC;QAAA;MAAA;MAAAiV,YAAA,GAA62C3gB,EAAE,CAACuiB,OAAO,EAAmHviB,EAAE,CAACwiB,IAAI,EAA6FliB,EAAE,CAACmiB,mBAAmB,EAAgJniB,EAAE,CAACoiB,gBAAgB,EAAsIpiB,EAAE,CAACqiB,gBAAgB,EAAoIriB,EAAE,CAACsiB,iBAAiB,EAA6J1iB,EAAE,CAAC2iB,UAAU,EAAoOziB,EAAE,CAAC0iB,aAAa,EAAyF1iB,EAAE,CAAC2iB,oBAAoB,EAAyP3iB,EAAE,CAAC4iB,eAAe,EAAsF5iB,EAAE,CAAC6iB,oBAAoB,EAAqI7iB,EAAE,CAAC8iB,iBAAiB,EAAyM9iB,EAAE,CAAC+iB,kBAAkB,EAA8I/iB,EAAE,CAACgjB,kBAAkB,EAA8IhjB,EAAE,CAACijB,gBAAgB,EAAsIjjB,EAAE,CAACkjB,OAAO,EAA8MljB,EAAE,CAACmjB,MAAM,EAA2KjjB,EAAE,CAAC4gB,eAAe;MAAAzB,MAAA;MAAAgE,eAAA;IAAA,EAAwI;EAAE;AACp8U;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KAn5CoGpU,EAAE,CAAAqU,iBAAA,CAm5CX8P,mBAAmB,EAAc,CAAC;IACjH7P,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,aAAa;MAAE+D,eAAe,EAAE5jB,uBAAuB,CAAC6jB,MAAM;MAAErE,QAAQ,EAAE,omOAAomO;MAAEI,MAAM,EAAE,CAAC,sMAAsM;IAAE,CAAC;EACz5O,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3L,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACxED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACV,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEtU,EAAE,CAACoiB;EAAkB,CAAC,EAAE;IAAE9N,IAAI,EAAE5T,EAAE,CAAC2hB;EAAO,CAAC,CAAC;AAAA;;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAMwC,iBAAiB,CAAC;EACpBrV,WAAWA,CAACoS,OAAO,EAAE7K,OAAO,GAAG,CAAC,CAAC,EAAE+K,MAAM,EAAE;IACvC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC+K,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACpX,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACD,aAAa,GAAG,IAAI,CAACpG,cAAc,CAAC,4BAA4B,CAAC;IACtE,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACrG,cAAc,CAAC,uBAAuB,CAAC;EAChE;EACAwgB,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC5Z,MAAM,CAAC,IAAI,CAACP,QAAQ,CAAC;EAC9B;EACAO,MAAMA,CAACP,QAAQ,EAAE;IACb,IAAI,CAACiX,OAAO,CAAC1W,MAAM,CAACP,QAAQ,CAAC,CAAC6V,SAAS,CAAEhK,MAAM,IAAK;MAChD,MAAM2B,QAAQ,GAAG3B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACrC,IAAIH,QAAQ,EAAE;QACV6J,UAAU,CAAC,MAAM;UACb,OAAO,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC9J,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAACzN,aAAa,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACApG,cAAcA,CAACyI,GAAG,EAAE;IAChB,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAkR,0BAAAhR,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8Q,iBAAiB,EA17C3B7kB,EAAE,CAAA6gB,iBAAA,CA07C2CjL,aAAa,GA17C1D5V,EAAE,CAAA6gB,iBAAA,CA07CqEpV,eAAe,GA17CtFzL,EAAE,CAAA6gB,iBAAA,CA07CiGngB,EAAE,CAAC2hB,MAAM;IAAA,CAA4C;EAAE;EAC1P;IAAS,IAAI,CAAC9C,IAAI,kBA37C8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EA27CJuQ,iBAAiB;MAAApF,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAmF,2BAAAziB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA37CfvC,EAAE,CAAAyC,cAAA,SA27CmE,CAAC;UA37CtEzC,EAAE,CAAA0C,MAAA,iCA27C8F,CAAC;UA37CjG1C,EAAE,CAAA2C,YAAA,CA27CoG,CAAC;QAAA;MAAA;MAAAqhB,aAAA;IAAA,EAAM;EAAE;AACnN;AACA;EAAA,QAAA5P,SAAA,oBAAAA,SAAA,KA77CoGpU,EAAE,CAAAqU,iBAAA,CA67CXwQ,iBAAiB,EAAc,CAAC;IAC/GvQ,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,WAAW;MAAEL,QAAQ,EAAE;IAA2C,CAAC;EAC1F,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvL,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACxED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACV,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAE5T,EAAE,CAAC2hB;EAAO,CAAC,CAAC;AAAA;;AAE1C;AACA;AACA;AACA;AACA;AACA,MAAM4C,0BAA0B,CAAC;EAC7BzV,WAAWA,CAACoS,OAAO,EAAE7K,OAAO,GAAG,CAAC,CAAC,EAAE8K,EAAE,EAAEC,MAAM,EAAE;IAC3C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8K,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACpX,aAAa,GAAG,CAAC;IACtB,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACoX,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC1e,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC0B,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACuF,aAAa,GAAG,IAAI,CAACpG,cAAc,CAAC,qCAAqC,CAAC;IAC/E,IAAI,CAACsG,YAAY,GAAG,IAAI,CAACtG,cAAc,CAAC,oCAAoC,CAAC;IAC7E,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACrG,cAAc,CAAC,gCAAgC,CAAC;EACzE;EACAqa,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtb,MAAM,GAAG,IAAI,CAACI,QAAQ,GAAG,EAAE;IAChC,IAAI,CAACse,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,OAAO,CAAC5W,eAAe,CAAC,IAAI,CAACL,QAAQ,EAAE,IAAI,CAACxF,IAAI,CAAC,CAACqb,SAAS,CAAEhK,MAAM,IAAK;MACzE,IAAI,CAACuL,SAAS,GAAG,KAAK;MACtB,IAAIvL,MAAM,CAACN,SAAS,CAAC,CAAC,EAAE;QACpB,IAAI,CAACzS,QAAQ,GAAG+S,MAAM,CAACgC,WAAW,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACnV,MAAM,GAAGmT,MAAM,CAAC+B,SAAS,CAAC,CAAC;MACpC;MACA,MAAMJ,QAAQ,GAAG3B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACrC,IAAIH,QAAQ,EAAE;QACV6J,UAAU,CAAC,MAAM;UACb,OAAO,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC9J,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAACzN,aAAa,CAAC;MAC1B;MACA,IAAI,CAACmX,EAAE,CAACK,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA5d,cAAcA,CAACyI,GAAG,EAAE;IAChB,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAAqR,mCAAAnR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFkR,0BAA0B,EAl/CpCjlB,EAAE,CAAA6gB,iBAAA,CAk/CoDjL,aAAa,GAl/CnE5V,EAAE,CAAA6gB,iBAAA,CAk/C8EpV,eAAe,GAl/C/FzL,EAAE,CAAA6gB,iBAAA,CAk/C0G7gB,EAAE,CAACoiB,iBAAiB,GAl/ChIpiB,EAAE,CAAA6gB,iBAAA,CAk/C2IngB,EAAE,CAAC2hB,MAAM;IAAA,CAA4C;EAAE;EACpS;IAAS,IAAI,CAAC9C,IAAI,kBAn/C8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EAm/CJ2Q,0BAA0B;MAAAxF,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAmB,MAAA;MAAAlB,QAAA,WAAAsF,oCAAA5iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAggB,GAAA,GAn/CxBviB,EAAE,CAAA6E,gBAAA;UAAF7E,EAAE,CAAAyC,cAAA,WAm/CuH,CAAC;UAn/C1HzC,EAAE,CAAA0C,MAAA,qBAm/CsI,CAAC;UAn/CzI1C,EAAE,CAAA2C,YAAA,CAm/C2I,CAAC;UAn/C9I3C,EAAE,CAAAyC,cAAA,UAm/CoK,CAAC;UAn/CvKzC,EAAE,CAAA0C,MAAA,gFAm/C8O,CAAC;UAn/CjP1C,EAAE,CAAA2C,YAAA,CAm/CkP,CAAC;UAn/CrP3C,EAAE,CAAAiD,UAAA,IAAAiG,8CAAA,qBAm/C+V,CAAC,IAAAE,8CAAA,qBAA6S,CAAC;UAn/ChpBpJ,EAAE,CAAAyC,cAAA,gBAm/C26B,CAAC;UAn/C96BzC,EAAE,CAAAihB,UAAA,sBAAAmE,6DAAA;YAAFplB,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAA,OAAFviB,EAAE,CAAAqF,WAAA,CAm/Cs2B7C,GAAA,CAAAmc,WAAA,CAAY,CAAC;UAAA,CAAC,CAAC;UAn/Cv3B3e,EAAE,CAAAyC,cAAA,YAm/Cm9B,CAAC,cAAgD,CAAC;UAn/CvgCzC,EAAE,CAAA0C,MAAA,gCAm/C6hC,CAAC;UAn/ChiC1C,EAAE,CAAA2C,YAAA,CAm/CqiC,CAAC;UAn/CxiC3C,EAAE,CAAAyC,cAAA,kBAm/C0kD,CAAC;UAn/C7kDzC,EAAE,CAAA8E,gBAAA,2BAAAugB,oEAAArgB,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,EAAAvG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAoG,KAAA,GAAAvG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CAm/CgmC,CAAC;UAn/CnmChF,EAAE,CAAA2C,YAAA,CAm/C0kD,CAAC;UAn/C7kD3C,EAAE,CAAAiD,UAAA,KAAAsG,mDAAA,0BAm/CuoD,CAAC;UAn/C1oDvJ,EAAE,CAAA2C,YAAA,CAm/Cw5D,CAAC;UAn/C35D3C,EAAE,CAAAyC,cAAA,iBAm/CqmE,CAAC;UAn/CxmEzC,EAAE,CAAA0C,MAAA,yBAm/C+nE,CAAC;UAn/CloE1C,EAAE,CAAA2C,YAAA,CAm/CwoE,CAAC,CAAQ,CAAC;UAn/CppE3C,EAAE,CAAAyC,cAAA,kBAm/CwtE,CAAC,QAAM,CAAC,YAA8C,CAAC;UAn/CjxEzC,EAAE,CAAA0C,MAAA,qBAm/C4xE,CAAC;UAn/C/xE1C,EAAE,CAAA2C,YAAA,CAm/CgyE,CAAC,CAAG,CAAC;UAn/CvyE3C,EAAE,CAAAyC,cAAA,QAm/C2yE,CAAC,YAAiD,CAAC;UAn/Ch2EzC,EAAE,CAAA0C,MAAA,eAm/Cq2E,CAAC;UAn/Cx2E1C,EAAE,CAAA2C,YAAA,CAm/Cy2E,CAAC,CAAG,CAAC,CAAW,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAAA,MAAA+iB,kBAAA,GAn/C53EtlB,EAAE,CAAAgE,WAAA;UAAA,MAAAD,QAAA,GAAF/D,EAAE,CAAAgE,WAAA;UAAFhE,EAAE,CAAA8C,SAAA,EAm/C0T,CAAC;UAn/C7T9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAE,KAAA,KAAAtI,GAAA,CAAAa,MAAA,kBAAAb,GAAA,CAAAa,MAAA,CAAA6I,MAAA,MAAA1J,GAAA,CAAAuf,SAm/C0T,CAAC;UAn/C7T/hB,EAAE,CAAA8C,SAAA,CAm/CumB,CAAC;UAn/C1mB9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAC,OAAA,KAAArI,GAAA,CAAAiB,QAAA,kBAAAjB,GAAA,CAAAiB,QAAA,CAAAyI,MAAA,MAAA1J,GAAA,CAAAuf,SAm/CumB,CAAC;UAn/C1mB/hB,EAAE,CAAA8C,SAAA,EAm/CgmC,CAAC;UAn/CnmC9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAoG,KAm/CgmC,CAAC;UAn/CnmCvL,EAAE,CAAAoD,UAAA,WAAAW,QAAA,CAAA6e,KAAA,GAAA7e,QAAA,CAAAqG,OAAA,iCAm/C26C,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,mCAA4E,CAAC;UAn/C3/CtE,EAAE,CAAAkG,WAAA,iBAAAnC,QAAA,CAAAqG,OAAA,IAAArG,QAAA,CAAA8e,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EAm/CooD,CAAC;UAn/CvoD9C,EAAE,CAAAoD,UAAA,SAAAW,QAAA,CAAAqG,OAAA,IAAArG,QAAA,CAAA8e,OAm/CooD,CAAC;UAn/CvoD7iB,EAAE,CAAA8C,SAAA,CAm/ComE,CAAC;UAn/CvmE9C,EAAE,CAAAgG,WAAA,cAAAxD,GAAA,CAAAuf,SAm/ComE,CAAC;UAn/CvmE/hB,EAAE,CAAAoD,UAAA,aAAAZ,GAAA,CAAAuf,SAAA,KAAAuD,kBAAA,CAAAxC,KAm/CyjE,CAAC;QAAA;MAAA;MAAA3B,YAAA,GAAmlB3gB,EAAE,CAACuiB,OAAO,EAAmHviB,EAAE,CAACwiB,IAAI,EAA6FliB,EAAE,CAACoiB,gBAAgB,EAAsIpiB,EAAE,CAACqiB,gBAAgB,EAAoIriB,EAAE,CAACsiB,iBAAiB,EAA6J1iB,EAAE,CAAC2iB,UAAU,EAAoOziB,EAAE,CAAC0iB,aAAa,EAAyF1iB,EAAE,CAAC2iB,oBAAoB,EAAyP3iB,EAAE,CAAC4iB,eAAe,EAAsF5iB,EAAE,CAAC6iB,oBAAoB,EAAqI7iB,EAAE,CAAC8iB,iBAAiB,EAAyM9iB,EAAE,CAACijB,gBAAgB,EAAsIjjB,EAAE,CAACkjB,OAAO,EAA8MljB,EAAE,CAACmjB,MAAM;MAAA9D,MAAA;MAAAgE,eAAA;IAAA,EAAsM;EAAE;AAClkK;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KAr/CoGpU,EAAE,CAAAqU,iBAAA,CAq/CX4Q,0BAA0B,EAAc,CAAC;IACxH3Q,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,0BAA0B;MAAE+D,eAAe,EAAE5jB,uBAAuB,CAAC6jB,MAAM;MAAErE,QAAQ,EAAE,uyEAAuyE;MAAEI,MAAM,EAAE,CAAC,wNAAwN;IAAE,CAAC;EAC3nF,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3L,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACxED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACV,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEtU,EAAE,CAACoiB;EAAkB,CAAC,EAAE;IAAE9N,IAAI,EAAE5T,EAAE,CAAC2hB;EAAO,CAAC,CAAC;AAAA;;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAMkD,wBAAwB,CAAC;EAC3B/V,WAAWA,CAACoS,OAAO,EAAE7K,OAAO,GAAG,CAAC,CAAC,EAAE8K,EAAE,EAAEC,MAAM,EAAE;IAC3C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8K,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACpX,aAAa,GAAG,CAAC;IACtB,IAAI,CAACE,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACoX,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC1e,MAAM,GAAG,EAAE;IAChB,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC0B,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACuF,aAAa,GAAG,IAAI,CAACpG,cAAc,CAAC,mCAAmC,CAAC;IAC7E,IAAI,CAACsG,YAAY,GAAG,IAAI,CAACtG,cAAc,CAAC,kCAAkC,CAAC;IAC3E,IAAI,CAACqG,QAAQ,GAAG,IAAI,CAACrG,cAAc,CAAC,8BAA8B,CAAC;EACvE;EACAsa,SAASA,CAAA,EAAG;IACR,IAAI,CAACvb,MAAM,GAAG,IAAI,CAACI,QAAQ,GAAG,EAAE;IAChC,IAAI,CAACse,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,OAAO,CAAC3W,aAAa,CAAC,IAAI,CAACN,QAAQ,EAAE,IAAI,CAACxF,IAAI,CAAC,CAACqb,SAAS,CAAEhK,MAAM,IAAK;MACvE,IAAI,CAACuL,SAAS,GAAG,KAAK;MACtB,IAAIvL,MAAM,CAACN,SAAS,CAAC,CAAC,EAAE;QACpB,IAAI,CAACzS,QAAQ,GAAG+S,MAAM,CAACgC,WAAW,CAAC,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACnV,MAAM,GAAGmT,MAAM,CAAC+B,SAAS,CAAC,CAAC;MACpC;MACA,MAAMJ,QAAQ,GAAG3B,MAAM,CAAC8B,WAAW,CAAC,CAAC;MACrC,IAAIH,QAAQ,EAAE;QACV6J,UAAU,CAAC,MAAM;UACb,OAAO,IAAI,CAACF,MAAM,CAACG,aAAa,CAAC9J,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAACzN,aAAa,CAAC;MAC1B;MACA,IAAI,CAACmX,EAAE,CAACK,aAAa,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA5d,cAAcA,CAACyI,GAAG,EAAE;IAChB,OAAOY,iBAAiB,CAAC,IAAI,CAACoJ,OAAO,EAAEhK,GAAG,EAAE,IAAI,CAAC;EACrD;EACA;IAAS,IAAI,CAAC8G,IAAI,YAAA2R,iCAAAzR,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwR,wBAAwB,EA1iDlCvlB,EAAE,CAAA6gB,iBAAA,CA0iDkDjL,aAAa,GA1iDjE5V,EAAE,CAAA6gB,iBAAA,CA0iD4EpV,eAAe,GA1iD7FzL,EAAE,CAAA6gB,iBAAA,CA0iDwG7gB,EAAE,CAACoiB,iBAAiB,GA1iD9HpiB,EAAE,CAAA6gB,iBAAA,CA0iDyIngB,EAAE,CAAC2hB,MAAM;IAAA,CAA4C;EAAE;EAClS;IAAS,IAAI,CAAC9C,IAAI,kBA3iD8Evf,EAAE,CAAAwf,iBAAA;MAAAlL,IAAA,EA2iDJiR,wBAAwB;MAAA9F,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAmB,MAAA;MAAAlB,QAAA,WAAA4F,kCAAAljB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAggB,GAAA,GA3iDtBviB,EAAE,CAAA6E,gBAAA;UAAF7E,EAAE,CAAAyC,cAAA,WA2iDmH,CAAC;UA3iDtHzC,EAAE,CAAA0C,MAAA,qBA2iDkI,CAAC;UA3iDrI1C,EAAE,CAAA2C,YAAA,CA2iDuI,CAAC;UA3iD1I3C,EAAE,CAAAyC,cAAA,UA2iDgK,CAAC;UA3iDnKzC,EAAE,CAAA0C,MAAA,+BA2iDyL,CAAC;UA3iD5L1C,EAAE,CAAA2C,YAAA,CA2iD6L,CAAC;UA3iDhM3C,EAAE,CAAAiD,UAAA,IAAAwG,4CAAA,qBA2iD0S,CAAC,IAAAE,4CAAA,qBAA6S,CAAC;UA3iD3lB3J,EAAE,CAAAyC,cAAA,gBA2iDk3B,CAAC;UA3iDr3BzC,EAAE,CAAAihB,UAAA,sBAAAyE,2DAAA;YAAF1lB,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAA,OAAFviB,EAAE,CAAAqF,WAAA,CA2iDizB7C,GAAA,CAAAoc,SAAA,CAAU,CAAC;UAAA,CAAC,CAAC;UA3iDh0B5e,EAAE,CAAAyC,cAAA,YA2iD05B,CAAC,cAAmD,CAAC;UA3iDj9BzC,EAAE,CAAA0C,MAAA,oBA2iD29B,CAAC;UA3iD99B1C,EAAE,CAAA2C,YAAA,CA2iDm+B,CAAC;UA3iDt+B3C,EAAE,CAAAyC,cAAA,mBA2iD6tD,CAAC;UA3iDhuDzC,EAAE,CAAA8E,gBAAA,2BAAA6gB,kEAAA3gB,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,EAAApG,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAiG,QAAA,GAAApG,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CA2iDiiC,CAAC;UA3iDpiChF,EAAE,CAAA2C,YAAA,CA2iD6tD,CAAC;UA3iDhuD3C,EAAE,CAAAiD,UAAA,KAAA6G,iDAAA,0BA2iDgyD,CAAC;UA3iDnyD9J,EAAE,CAAA2C,YAAA,CA2iDuvE,CAAC;UA3iD1vE3C,EAAE,CAAAyC,cAAA,cA2iDuxE,CAAC,gBAAsD,CAAC;UA3iDj1EzC,EAAE,CAAA0C,MAAA,wBA2iD+1E,CAAC;UA3iDl2E1C,EAAE,CAAA2C,YAAA,CA2iDu2E,CAAC;UA3iD12E3C,EAAE,CAAAyC,cAAA,mBA2iD2+F,CAAC;UA3iD9+FzC,EAAE,CAAA8E,gBAAA,2BAAA8gB,kEAAA5gB,MAAA;YAAFhF,EAAE,CAAAiF,aAAA,CAAAsd,GAAA;YAAFviB,EAAE,CAAAkF,kBAAA,CAAA1C,GAAA,CAAA2C,IAAA,CAAAwf,eAAA,EAAA3f,MAAA,MAAAxC,GAAA,CAAA2C,IAAA,CAAAwf,eAAA,GAAA3f,MAAA;YAAA,OAAFhF,EAAE,CAAAqF,WAAA,CAAAL,MAAA;UAAA,CA2iD46E,CAAC;UA3iD/6EhF,EAAE,CAAA2C,YAAA,CA2iD2+F,CAAC;UA3iD9+F3C,EAAE,CAAAiD,UAAA,KAAAiH,iDAAA,0BA2iDwhG,CAAC;UA3iD3hGlK,EAAE,CAAA2C,YAAA,CA2iDi4G,CAAC;UA3iDp4G3C,EAAE,CAAAyC,cAAA,iBA2iD4kH,CAAC;UA3iD/kHzC,EAAE,CAAA0C,MAAA,wBA2iDqmH,CAAC;UA3iDxmH1C,EAAE,CAAA2C,YAAA,CA2iD8mH,CAAC,CAAQ,CAAC;UA3iD1nH3C,EAAE,CAAAyC,cAAA,kBA2iD8rH,CAAC,QAAM,CAAC,YAA8C,CAAC;UA3iDvvHzC,EAAE,CAAA0C,MAAA,qBA2iDkwH,CAAC;UA3iDrwH1C,EAAE,CAAA2C,YAAA,CA2iDswH,CAAC,CAAG,CAAC;UA3iD7wH3C,EAAE,CAAAyC,cAAA,QA2iDixH,CAAC,YAAiD,CAAC;UA3iDt0HzC,EAAE,CAAA0C,MAAA,eA2iD20H,CAAC;UA3iD90H1C,EAAE,CAAA2C,YAAA,CA2iD+0H,CAAC,CAAG,CAAC,CAAW,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAAA,MAAAsjB,gBAAA,GA3iDl2H7lB,EAAE,CAAAgE,WAAA;UAAA,MAAA+F,WAAA,GAAF/J,EAAE,CAAAgE,WAAA;UAAA,MAAAmG,SAAA,GAAFnK,EAAE,CAAAgE,WAAA;UAAFhE,EAAE,CAAA8C,SAAA,EA2iDqQ,CAAC;UA3iDxQ9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAE,KAAA,KAAAtI,GAAA,CAAAa,MAAA,kBAAAb,GAAA,CAAAa,MAAA,CAAA6I,MAAA,MAAA1J,GAAA,CAAAuf,SA2iDqQ,CAAC;UA3iDxQ/hB,EAAE,CAAA8C,SAAA,CA2iDkjB,CAAC;UA3iDrjB9C,EAAE,CAAAoD,UAAA,SAAAZ,GAAA,CAAAoI,YAAA,CAAAC,OAAA,KAAArI,GAAA,CAAAiB,QAAA,kBAAAjB,GAAA,CAAAiB,QAAA,CAAAyI,MAAA,MAAA1J,GAAA,CAAAuf,SA2iDkjB,CAAC;UA3iDrjB/hB,EAAE,CAAA8C,SAAA,EA2iDiiC,CAAC;UA3iDpiC9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAiG,QA2iDiiC,CAAC;UA3iDpiCpL,EAAE,CAAAoD,UAAA,WAAA2G,WAAA,CAAA6Y,KAAA,GAAA7Y,WAAA,CAAAK,OAAA,iCA2iDi5C,CAAC,aAAA5H,GAAA,CAAA8B,cAAA,sCAA+E,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC,cAAA9B,GAAA,CAAA8B,cAAA,uCAAiF,CAAC;UA3iDxoDtE,EAAE,CAAAkG,WAAA,iBAAA6D,WAAA,CAAAK,OAAA,IAAAL,WAAA,CAAA8Y,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EA2iD6xD,CAAC;UA3iDhyD9C,EAAE,CAAAoD,UAAA,SAAA2G,WAAA,CAAAK,OAAA,IAAAL,WAAA,CAAA8Y,OA2iD6xD,CAAC;UA3iDhyD7iB,EAAE,CAAA8C,SAAA,EA2iD46E,CAAC;UA3iD/6E9C,EAAE,CAAAsF,gBAAA,YAAA9C,GAAA,CAAA2C,IAAA,CAAAwf,eA2iD46E,CAAC;UA3iD/6E3kB,EAAE,CAAAoD,UAAA,WAAA+G,SAAA,CAAA0Y,OAAA,GAAA1Y,SAAA,CAAAC,OAAA,IAAAL,WAAA,CAAA9B,KAAA,IAAAkC,SAAA,CAAAlC,KAAA,iCA2iDu0F,CAAC,aAAAzF,GAAA,CAAA8B,cAAA,sCAA+E,CAAC;UA3iD15FtE,EAAE,CAAAkG,WAAA,iBAAAiE,SAAA,CAAAC,OAAA,IAAAD,SAAA,CAAA0Y,OAAA;UAAF7iB,EAAE,CAAA8C,SAAA,EA2iDqhG,CAAC;UA3iDxhG9C,EAAE,CAAAoD,UAAA,SAAA+G,SAAA,CAAA0Y,OA2iDqhG,CAAC;UA3iDxhG7iB,EAAE,CAAA8C,SAAA,CA2iD2kH,CAAC;UA3iD9kH9C,EAAE,CAAAgG,WAAA,cAAAxD,GAAA,CAAAuf,SA2iD2kH,CAAC;UA3iD9kH/hB,EAAE,CAAAoD,UAAA,aAAAZ,GAAA,CAAAuf,SAAA,KAAA8D,gBAAA,CAAA/C,KA2iDgiH,CAAC;QAAA;MAAA;MAAA3B,YAAA,GAAklB3gB,EAAE,CAACuiB,OAAO,EAAmHviB,EAAE,CAACwiB,IAAI,EAA6FliB,EAAE,CAACoiB,gBAAgB,EAAsIpiB,EAAE,CAACqiB,gBAAgB,EAAoIriB,EAAE,CAACsiB,iBAAiB,EAA6J1iB,EAAE,CAAC2iB,UAAU,EAAoOziB,EAAE,CAAC0iB,aAAa,EAAyF1iB,EAAE,CAAC2iB,oBAAoB,EAAyP3iB,EAAE,CAAC4iB,eAAe,EAAsF5iB,EAAE,CAAC6iB,oBAAoB,EAAqI7iB,EAAE,CAAC8iB,iBAAiB,EAAyM9iB,EAAE,CAAC+iB,kBAAkB,EAA8I/iB,EAAE,CAACgjB,kBAAkB,EAA8IhjB,EAAE,CAACkjB,OAAO,EAA8MljB,EAAE,CAACmjB,MAAM;MAAA9D,MAAA,GAAA5V,GAAA;MAAA4Z,eAAA;IAAA,EAAsM;EAAE;AACrtN;AACA;EAAA,QAAA7P,SAAA,oBAAAA,SAAA,KA7iDoGpU,EAAE,CAAAqU,iBAAA,CA6iDXkR,wBAAwB,EAAc,CAAC;IACtHjR,IAAI,EAAElU,SAAS;IACf+L,IAAI,EAAE,CAAC;MAAE+T,QAAQ,EAAE,wBAAwB;MAAE+D,eAAe,EAAE5jB,uBAAuB,CAAC6jB,MAAM;MAAErE,QAAQ,EAAE,ixHAAixH;MAAEI,MAAM,EAAE,CAAC,wNAAwN;IAAE,CAAC;EACnmI,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3L,IAAI,EAAEsB;EAAc,CAAC,EAAE;IAAEtB,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACxED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACV,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEtU,EAAE,CAACoiB;EAAkB,CAAC,EAAE;IAAE9N,IAAI,EAAE5T,EAAE,CAAC2hB;EAAO,CAAC,CAAC;AAAA;AAE1E,SAASyD,mBAAmBA,CAAC/O,OAAO,EAAEgP,QAAQ,EAAE;EAC5C,MAAMxb,UAAU,GAAG,EAAE;EACrBwM,OAAO,CAACxM,UAAU,CACbmC,OAAO,CAAC,CAAC,CAACsZ,aAAa,EAAEC,eAAe,CAAC,KAAK;IAC/C,MAAMtb,QAAQ,GAAGob,QAAQ,CAACpR,GAAG,CAACqR,aAAa,CAAC;IAC5Crb,QAAQ,CAACmM,UAAU,CAACmP,eAAe,CAAC;IACpC1b,UAAU,CAACiR,IAAI,CAAC7Q,QAAQ,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOJ,UAAU;AACrB;AACA,SAAS2b,eAAeA,CAAC3b,UAAU,EAAE;EACjC,MAAM4b,MAAM,GAAG,EAAE;EACjB5b,UAAU,CACLmC,OAAO,CAAE/B,QAAQ,IAAK;IACvBwb,MAAM,CAAC3K,IAAI,CAAC7Q,QAAQ,CAACsM,SAAS,CAAC,aAAa,CAAC,CAAC;EAClD,CAAC,CAAC;EACF,OAAOkP,MAAM;AACjB;AACA,SAASC,gBAAgBA,CAACrP,OAAO,EAAE;EAC/B,OAAOhL,UAAU,CAACzB,kBAAkB,EAAEyM,OAAO,CAAC;AAClD;AACA,SAASsP,uBAAuBA,CAACC,GAAG,EAAE;EAClC,OAAO,IAAI;AACf;AACA,MAAMC,YAAY,CAAC;EACf,OAAOC,OAAOA,CAACC,aAAa,EAAE;IAC1B,OAAO;MACHC,QAAQ,EAAEH,YAAY;MACtBI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAElb,oBAAoB;QAAEmb,QAAQ,EAAEJ;MAAc,CAAC,EAC1D;QAAEG,OAAO,EAAEnb,eAAe;QAAEqb,UAAU,EAAEV,gBAAgB;QAAEW,IAAI,EAAE,CAACrb,oBAAoB;MAAE,CAAC,EACxF;QAAEkb,OAAO,EAAEjb,kBAAkB;QAAEmb,UAAU,EAAEhB,mBAAmB;QAAEiB,IAAI,EAAE,CAACtb,eAAe,EAAEnL,QAAQ;MAAE,CAAC,EACnG;QAAEsmB,OAAO,EAAEhb,cAAc;QAAEkb,UAAU,EAAEZ,eAAe;QAAEa,IAAI,EAAE,CAACpb,kBAAkB;MAAE,CAAC,EACpF;QAAEib,OAAO,EAAE5T,sBAAsB;QAAE6T,QAAQ,EAAE9V;MAAkB,CAAC,EAChE;QAAE6V,OAAO,EAAE/a,0BAA0B;QAAEgb,QAAQ,EAAE;MAAgB,CAAC,EAClE;QAAED,OAAO,EAAE9a,gCAAgC;QAAE+a,QAAQ,EAAER;MAAwB,CAAC,EAChF;QAAEO,OAAO,EAAEpS,cAAc;QAAEwS,QAAQ,EAAEvS;MAAoB,CAAC,EAC1DxB,mBAAmB,EACnB2C,aAAa,EACbR,cAAc,EACd2D,mBAAmB,EACnBiG,sBAAsB,EACtBtE,oBAAoB;IAE5B,CAAC;EACL;EACA;IAAS,IAAI,CAAC7G,IAAI,YAAAoT,qBAAAlT,iBAAA;MAAA,YAAAA,iBAAA,IAAwFwS,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACW,IAAI,kBApmD8ElnB,EAAE,CAAAmnB,gBAAA;MAAA7S,IAAA,EAomDSiS;IAAY,EAqB1F;EAAE;EAC/B;IAAS,IAAI,CAACa,IAAI,kBA1nD8EpnB,EAAE,CAAAqnB,gBAAA;MAAAC,OAAA,GA0nDiC7mB,YAAY,EACvIO,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdV,YAAY,EACZE,WAAW,EACXS,YAAY;IAAA,EAAI;EAAE;AAC9B;AACA;EAAA,QAAA8S,SAAA,oBAAAA,SAAA,KAroDoGpU,EAAE,CAAAqU,iBAAA,CAqoDXkS,YAAY,EAAc,CAAC;IAC1GjS,IAAI,EAAE/T,QAAQ;IACd4L,IAAI,EAAE,CAAC;MACCmb,OAAO,EAAE,CACL7mB,YAAY,EACZO,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdV,YAAY,EACZE,WAAW,EACXS,YAAY,CACf;MACDimB,YAAY,EAAE,CACVpH,eAAe,EACfd,oBAAoB,EACpBsC,gBAAgB,EAChBwC,mBAAmB,EACnBc,0BAA0B,EAC1BM,wBAAwB,EACxBV,iBAAiB,CACpB;MACD2C,OAAO,EAAE,CACLrH,eAAe,EACfd,oBAAoB,EACpBsC,gBAAgB,EAChBwC,mBAAmB,EACnBc,0BAA0B,EAC1BM,wBAAwB,EACxBV,iBAAiB;IAEzB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM4C,MAAM,GAAG,CACX;EACIC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAExH,eAAe;EAC1ByH,QAAQ,EAAE,CACN;IACIF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEhG;EACf,CAAC,EACD;IACI+F,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEhG;EACf,CAAC,EACD;IACI+F,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAExD;EACf,CAAC,EACD;IACIuD,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE9C;EACf,CAAC,EACD;IACI6C,IAAI,EAAE,kBAAkB;IACxBC,SAAS,EAAE1C;EACf,CAAC,EACD;IACIyC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEpC;EACf,CAAC;AAET,CAAC,CACJ;AAED,MAAMsC,oBAAoB,CAAC;EACvBrY,WAAWA,CAACuW,QAAQ,EAAErkB,MAAM,EAAE;IAC1B,IAAI,CAACqkB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACrkB,MAAM,GAAGA,MAAM;EACxB;EACAomB,SAASA,CAACxB,GAAG,EAAE5Q,IAAI,EAAE;IACjB;IACA,IAAI,CAAC,IAAI,CAAChU,MAAM,CAAC4kB,GAAG,CAAC,EAAE;MACnB,OAAO,IAAI,CAACyB,WAAW,CAAC/R,wBAAwB,CAAC,CAAC,CAC7CP,IAAI,CAAC5T,SAAS,CAACye,aAAa,IAAI;QACjC,IAAIA,aAAa,EAAE;UACf,OAAO,IAAI,CAACyH,WAAW,CAACjS,QAAQ,CAAC,CAAC,CAACL,IAAI,CAAC5T,SAAS,CAAEyO,KAAK,IAAK;YACzD,MAAM0X,GAAG,GAAG,UAAU1X,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE;YACxCiV,GAAG,GAAGA,GAAG,CAAC9Y,KAAK,CAAC;cACZya,UAAU,EAAE;gBACR9K,aAAa,EAAE6K;cACnB;YACJ,CAAC,CAAC;YACF,OAAOtS,IAAI,CAACwS,MAAM,CAAC5B,GAAG,CAAC;UAC3B,CAAC,CAAC,CAAC;QACP,CAAC,MACI;UACD;UACA;UACA,OAAO5Q,IAAI,CAACwS,MAAM,CAAC5B,GAAG,CAAC;QAC3B;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,MACI;MACD,OAAO5Q,IAAI,CAACwS,MAAM,CAAC5B,GAAG,CAAC;IAC3B;EACJ;EACA,IAAIyB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAChC,QAAQ,CAACpR,GAAG,CAACiB,aAAa,CAAC;EAC3C;EACA;IAAS,IAAI,CAAC/B,IAAI,YAAAsU,6BAAApU,iBAAA;MAAA,YAAAA,iBAAA,IAAwF8T,oBAAoB,EA7uD9B7nB,EAAE,CAAAgU,QAAA,CA6uD8ChU,EAAE,CAACM,QAAQ,GA7uD3DN,EAAE,CAAAgU,QAAA,CA6uDsElI,gCAAgC;IAAA,CAA6C;EAAE;EACvP;IAAS,IAAI,CAACmI,KAAK,kBA9uD6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EA8uDYuX,oBAAoB;MAAA1T,OAAA,EAApB0T,oBAAoB,CAAAhU;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAhvDoGpU,EAAE,CAAAqU,iBAAA,CAgvDXwT,oBAAoB,EAAc,CAAC;IAClHvT,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAEtU,EAAE,CAACM;EAAS,CAAC,EAAE;IAAEgU,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACtED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACL,gCAAgC;IAC3C,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMsc,uBAAuB,CAAC;EAC1B5Y,WAAWA,CAACuW,QAAQ,EAAEsC,UAAU,GAAG,eAAe,EAAE;IAChD,IAAI,CAACtC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACsC,UAAU,GAAGA,UAAU;EAChC;EACAP,SAASA,CAACxB,GAAG,EAAE5Q,IAAI,EAAE;IACjB,OAAO,IAAI,CAACqS,WAAW,CAACjS,QAAQ,CAAC,CAAC,CAACL,IAAI,CAAC5T,SAAS,CAAEyO,KAAK,IAAK;MACzD,IAAIA,KAAK,IAAIA,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE;QAC3BiV,GAAG,GAAGA,GAAG,CAAC9Y,KAAK,CAAC;UACZya,UAAU,EAAE;YACR,CAAC,IAAI,CAACI,UAAU,GAAG/X,KAAK,CAACe,QAAQ,CAAC;UACtC;QACJ,CAAC,CAAC;MACN;MACA,OAAOqE,IAAI,CAACwS,MAAM,CAAC5B,GAAG,CAAC;IAC3B,CAAC,CAAC,CAAC;EACP;EACA,IAAIyB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAChC,QAAQ,CAACpR,GAAG,CAACiB,aAAa,CAAC;EAC3C;EACA;IAAS,IAAI,CAAC/B,IAAI,YAAAyU,gCAAAvU,iBAAA;MAAA,YAAAA,iBAAA,IAAwFqU,uBAAuB,EA3wDjCpoB,EAAE,CAAAgU,QAAA,CA2wDiDhU,EAAE,CAACM,QAAQ,GA3wD9DN,EAAE,CAAAgU,QAAA,CA2wDyEnI,0BAA0B;IAAA,CAA6C;EAAE;EACpP;IAAS,IAAI,CAACoI,KAAK,kBA5wD6EjU,EAAE,CAAAkU,kBAAA;MAAA5D,KAAA,EA4wDY8X,uBAAuB;MAAAjU,OAAA,EAAvBiU,uBAAuB,CAAAvU;IAAA,EAAG;EAAE;AAC9I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA9wDoGpU,EAAE,CAAAqU,iBAAA,CA8wDX+T,uBAAuB,EAAc,CAAC;IACrH9T,IAAI,EAAEpU;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEoU,IAAI,EAAEtU,EAAE,CAACM;EAAS,CAAC,EAAE;IAAEgU,IAAI,EAAEpG,SAAS;IAAEqG,UAAU,EAAE,CAAC;MACtED,IAAI,EAAEnU,MAAM;MACZgM,IAAI,EAAE,CAACN,0BAA0B;IACrC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAM0c,MAAM,CAAC;EACT/Y,WAAWA,CAACgZ,EAAE,EAAEjd,KAAK,EAAEH,QAAQ,EAAEhG,UAAU,EAAEiD,KAAK,EAAEsc,eAAe,EAAEnZ,QAAQ,EAAE;IAC3E,IAAI,CAACgd,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACjd,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACiD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsc,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACnZ,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASwH,sBAAsB,EAAEnH,0BAA0B,EAAEJ,eAAe,EAAEE,kBAAkB,EAAEC,cAAc,EAAEE,gCAAgC,EAAEJ,oBAAoB,EAAE2T,oBAAoB,EAAEc,eAAe,EAAEjQ,qBAAqB,EAAEC,0BAA0B,EAAEF,uBAAuB,EAAE4X,oBAAoB,EAAErW,cAAc,EAAE+U,YAAY,EAAE5T,oBAAoB,EAAEV,iBAAiB,EAAEgG,YAAY,EAAErC,aAAa,EAAEwS,uBAAuB,EAAErX,iBAAiB,EAAE8F,cAAc,EAAE6B,qBAAqB,EAAEnJ,WAAW,EAAEM,wBAAwB,EAAEoD,mBAAmB,EAAE8F,mBAAmB,EAAEJ,0BAA0B,EAAEgJ,gBAAgB,EAAEkD,iBAAiB,EAAEnK,oBAAoB,EAAElB,2BAA2B,EAAED,wBAAwB,EAAED,iBAAiB,EAAED,oBAAoB,EAAE2F,sBAAsB,EAAEP,6BAA6B,EAAE0F,mBAAmB,EAAEc,0BAA0B,EAAEM,wBAAwB,EAAE9Q,mBAAmB,EAAEW,cAAc,EAAEZ,cAAc,EAAE+T,MAAM,EAAE9N,oBAAoB,EAAElM,gBAAgB,EAAEC,SAAS,EAAEiC,gBAAgB,EAAE1E,UAAU,EAAEzB,kBAAkB,EAAEwO,oBAAoB,EAAEnL,iBAAiB,EAAEyC,iBAAiB,EAAEiW,uBAAuB,EAAED,gBAAgB,EAAEN,mBAAmB,EAAEI,eAAe,EAAEnH,uBAAuB,EAAE0I,MAAM,EAAEtZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}