{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(apiQuotationService, http) {\n    this.apiQuotationService = apiQuotationService;\n    this.http = http;\n    this.apiUrl = '/api/Quotation';\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  } // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 根據 swagger.json，GetListByHouseID 返回 SaveDataQuotationResponseBase\n      // 其中 Entries 是單一的 SaveDataQuotation 對象，不是陣列\n      // 需要將其轉換為前端期望的格式\n      if (response && response.StatusCode === 0) {\n        let entries = [];\n        if (response.Entries) {\n          // 如果 Entries 是 SaveDataQuotation 對象，提取其 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            entries = response.Entries.Items.map(item => ({\n              CQuotationID: response.Entries.CQuotationID,\n              CHouseID: response.Entries.CHouseID,\n              CItemName: item.CItemName,\n              CUnitPrice: item.CUnitPrice,\n              CCount: item.CCount,\n              CStatus: item.CStatus,\n              CIsDefault: item.CIsDefault\n            }));\n          }\n        }\n        return {\n          ...response,\n          Entries: entries\n        };\n      }\n      return response;\n    }));\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  } // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\n  saveQuotation(request) {\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\n    const quotationItems = request.items.map(item => ({\n      CItemName: item.cItemName,\n      CUnitPrice: item.cUnitPrice,\n      CCount: item.cCount,\n      CStatus: item.cStatus || 1,\n      CIsDefault: item.cIsDefault || false\n    }));\n    // 建立 SaveDataQuotation 請求\n    const saveRequest = {\n      CHouseID: request.houseId,\n      CQuotationID: 0,\n      // 對於新的報價單或更新現有的報價單\n      Items: quotationItems\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveRequest\n    }).pipe(map(response => ({\n      success: response?.StatusCode === 0,\n      message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\n      data: request.items\n    })));\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  } // 載入預設報價項目 (LoadDefaultItems API)\n  loadDefaultItems(request) {\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\n    return this.http.post(`${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`, request).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: response.Entries || []\n      };\n    }));\n  } // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      CHouseID: item.cHouseID,\n      CQuotationID: quotationId,\n      Items: [{\n        CItemName: item.cItemName,\n        CUnitPrice: item.cUnitPrice,\n        CCount: item.cCount,\n        CStatus: item.cStatus || 1,\n        CIsDefault: item.cIsDefault || false\n      }]\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "QuotationService", "constructor", "apiQuotationService", "http", "apiUrl", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "pipe", "response", "StatusCode", "entries", "Entries", "Items", "Array", "isArray", "item", "CQuotationID", "CHouseID", "CItemName", "CUnitPrice", "CCount", "CStatus", "CIsDefault", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "quotationItems", "items", "cItemName", "cUnitPrice", "cCount", "cStatus", "cIsDefault", "saveRequest", "success", "message", "data", "getDefaultQuotationItems", "pageIndex", "pageSize", "statusCode", "loadDefaultItems", "post", "rootUrl", "Message", "updateQuotationItem", "saveData", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  QuotationItemModel,\r\n  GetListByHouseIdRequest,\r\n  LoadDefaultItemsRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private readonly apiUrl = '/api/Quotation';\r\n\r\n  constructor(\r\n    private apiQuotationService: ApiQuotationService,\r\n    private http: HttpClient\r\n  ) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 根據 swagger.json，GetListByHouseID 返回 SaveDataQuotationResponseBase\r\n        // 其中 Entries 是單一的 SaveDataQuotation 對象，不是陣列\r\n        // 需要將其轉換為前端期望的格式\r\n        if (response && response.StatusCode === 0) {\r\n          let entries = [];\r\n          if (response.Entries) {\r\n            // 如果 Entries 是 SaveDataQuotation 對象，提取其 Items 陣列\r\n            if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n              entries = response.Entries.Items.map((item: any) => ({\r\n                CQuotationID: response.Entries.CQuotationID,\r\n                CHouseID: response.Entries.CHouseID,\r\n                CItemName: item.CItemName,\r\n                CUnitPrice: item.CUnitPrice,\r\n                CCount: item.CCount,\r\n                CStatus: item.CStatus,\r\n                CIsDefault: item.CIsDefault\r\n              }));\r\n            }\r\n          }\r\n          return {\r\n            ...response,\r\n            Entries: entries\r\n          };\r\n        }\r\n        return response;\r\n      })\r\n    );\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }  // 儲存報價單 (支援批量保存多個項目) - 使用單一請求\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 將 QuotationItem 轉換為 QuotationItemModel 格式\r\n    const quotationItems: QuotationItemModel[] = request.items.map(item => ({\r\n      CItemName: item.cItemName,\r\n      CUnitPrice: item.cUnitPrice,\r\n      CCount: item.cCount,\r\n      CStatus: item.cStatus || 1,\r\n      CIsDefault: item.cIsDefault || false,\r\n    }));\r\n\r\n    // 建立 SaveDataQuotation 請求\r\n    const saveRequest: SaveDataQuotation = {\r\n      CHouseID: request.houseId,\r\n      CQuotationID: 0, // 對於新的報價單或更新現有的報價單\r\n      Items: quotationItems\r\n    };\r\n\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveRequest }).pipe(\r\n      map(response => ({\r\n        success: response?.StatusCode === 0,\r\n        message: response?.StatusCode === 0 ? '報價單保存成功' : '報價單保存失敗',\r\n        data: request.items\r\n      } as QuotationResponse))\r\n    );\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 載入預設報價項目 (LoadDefaultItems API)\r\n  loadDefaultItems(request: LoadDefaultItemsRequest): Observable<QuotationResponse> {\r\n    // 使用注入的 HttpClient 直接發送請求，但使用 apiQuotationService 的 rootUrl\r\n    return this.http.post<any>(\r\n      `${this.apiQuotationService.rootUrl}/api/Quotation/LoadDefaultItems`,\r\n      request\r\n    ).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: response.Entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      CHouseID: item.cHouseID,\r\n      CQuotationID: quotationId,\r\n      Items: [{\r\n        CItemName: item.cItemName,\r\n        CUnitPrice: item.cUnitPrice,\r\n        CCount: item.cCount,\r\n        CStatus: item.cStatus || 1,\r\n        CIsDefault: item.cIsDefault || false\r\n      }]\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,GAAG,QAAmB,gBAAgB;;;;AAgB/C,OAAM,MAAOC,gBAAgB;EAG3BC,YACUC,mBAAwC,EACxCC,IAAgB;IADhB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,IAAI,GAAJA,IAAI;IAJG,KAAAC,MAAM,GAAG,gBAAgB;EAKtC;EACJ;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACJ,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACR,mBAAmB,CAACU,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF,CAAC,CAAE;EACHO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACZ,mBAAmB,CAACc,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAC3FlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA;MACA;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QACzC,IAAIC,OAAO,GAAG,EAAE;QAChB,IAAIF,QAAQ,CAACG,OAAO,EAAE;UACpB;UACA,IAAIH,QAAQ,CAACG,OAAO,CAACC,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACG,OAAO,CAACC,KAAK,CAAC,EAAE;YACnEF,OAAO,GAAGF,QAAQ,CAACG,OAAO,CAACC,KAAK,CAACvB,GAAG,CAAE0B,IAAS,KAAM;cACnDC,YAAY,EAAER,QAAQ,CAACG,OAAO,CAACK,YAAY;cAC3CC,QAAQ,EAAET,QAAQ,CAACG,OAAO,CAACM,QAAQ;cACnCC,SAAS,EAAEH,IAAI,CAACG,SAAS;cACzBC,UAAU,EAAEJ,IAAI,CAACI,UAAU;cAC3BC,MAAM,EAAEL,IAAI,CAACK,MAAM;cACnBC,OAAO,EAAEN,IAAI,CAACM,OAAO;cACrBC,UAAU,EAAEP,IAAI,CAACO;aAClB,CAAC,CAAC;UACL;QACF;QACA,OAAO;UACL,GAAGd,QAAQ;UACXG,OAAO,EAAED;SACV;MACH;MACA,OAAOF,QAAQ;IACjB,CAAC,CAAC,CACH;EACH;EACA;EACAe,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAAChC,mBAAmB,CAACiC,6BAA6B,CAAC;MAAE3B,IAAI,EAAE0B;IAAS,CAAE,CAAC;EACpF,CAAC,CAAE;EACHE,aAAaA,CAAC9B,OAAoD;IAChE;IACA,MAAM+B,cAAc,GAAyB/B,OAAO,CAACgC,KAAK,CAACvC,GAAG,CAAC0B,IAAI,KAAK;MACtEG,SAAS,EAAEH,IAAI,CAACc,SAAS;MACzBV,UAAU,EAAEJ,IAAI,CAACe,UAAU;MAC3BV,MAAM,EAAEL,IAAI,CAACgB,MAAM;MACnBV,OAAO,EAAEN,IAAI,CAACiB,OAAO,IAAI,CAAC;MAC1BV,UAAU,EAAEP,IAAI,CAACkB,UAAU,IAAI;KAChC,CAAC,CAAC;IAEH;IACA,MAAMC,WAAW,GAAsB;MACrCjB,QAAQ,EAAErB,OAAO,CAACQ,OAAO;MACzBY,YAAY,EAAE,CAAC;MAAE;MACjBJ,KAAK,EAAEe;KACR;IAED,OAAO,IAAI,CAACnC,mBAAmB,CAACiC,6BAA6B,CAAC;MAAE3B,IAAI,EAAEoC;IAAW,CAAE,CAAC,CAAC3B,IAAI,CACvFlB,GAAG,CAACmB,QAAQ,KAAK;MACf2B,OAAO,EAAE3B,QAAQ,EAAEC,UAAU,KAAK,CAAC;MACnC2B,OAAO,EAAE5B,QAAQ,EAAEC,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;MAC3D4B,IAAI,EAAEzC,OAAO,CAACgC;KACO,EAAC,CACzB;EACH;EAEA;EACAU,wBAAwBA,CAAA;IACtB;IACA,MAAM1C,OAAO,GAA4B;MACvC2C,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAChD,mBAAmB,CAACK,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACW,IAAI,CAClFlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,OAAO;QACL2B,OAAO,EAAE3B,QAAQ,CAACiC,UAAU,KAAK,CAAC;QAAE;QACpCL,OAAO,EAAE5B,QAAQ,CAAC4B,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAE7B,QAAQ,CAACE,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHgC,gBAAgBA,CAAC9C,OAAgC;IAC/C;IACA,OAAO,IAAI,CAACH,IAAI,CAACkD,IAAI,CACnB,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,OAAO,iCAAiC,EACpEhD,OAAO,CACR,CAACW,IAAI,CACJlB,GAAG,CAACmB,QAAQ,IAAG;MACb;MACA,OAAO;QACL2B,OAAO,EAAE3B,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpC2B,OAAO,EAAE5B,QAAQ,CAACqC,OAAO,IAAI,EAAE;QAC/BR,IAAI,EAAE7B,QAAQ,CAACG,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH,CAAC,CAAE;EACHmC,mBAAmBA,CAAC9C,WAAmB,EAAEe,IAAmB;IAC1D,MAAMgC,QAAQ,GAAsB;MAClC9B,QAAQ,EAAEF,IAAI,CAACV,QAAQ;MACvBW,YAAY,EAAEhB,WAAW;MACzBY,KAAK,EAAE,CAAC;QACNM,SAAS,EAAEH,IAAI,CAACc,SAAS;QACzBV,UAAU,EAAEJ,IAAI,CAACe,UAAU;QAC3BV,MAAM,EAAEL,IAAI,CAACgB,MAAM;QACnBV,OAAO,EAAEN,IAAI,CAACiB,OAAO,IAAI,CAAC;QAC1BV,UAAU,EAAEP,IAAI,CAACkB,UAAU,IAAI;OAChC;KACF;IACD,OAAO,IAAI,CAACzC,mBAAmB,CAACiC,6BAA6B,CAAC;MAAE3B,IAAI,EAAEiD;IAAQ,CAAE,CAAC,CAACxC,IAAI,CACpFlB,GAAG,CAACmB,QAAQ,IAAG;MACb,OAAO;QACL2B,OAAO,EAAE3B,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpC2B,OAAO,EAAE5B,QAAQ,CAACqC,OAAO,IAAI,EAAE;QAC/BR,IAAI,EAAE,CAACtB,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAiC,eAAeA,CAAC5C,OAAe;IAC7B;IACA;IACA,MAAM,IAAI6C,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCA7IW3D,gBAAgB,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA9D,gBAAA,GAAA4D,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBhE,gBAAgB;MAAAiE,OAAA,EAAhBjE,gBAAgB,CAAAkE,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}