{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseHouseLoginStep2Post$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseHouseLoginStep2Post$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseHouseLoginStep2Post$Plain.PATH = '/api/House/HouseLoginStep2';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseHouseLoginStep2Post$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-house-login-step-2-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { HouseLoginStep2Request } from '../../models/house-login-step-2-request';\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiHouseHouseLoginStep2Post$Plain$Params {\r\n      body?: HouseLoginStep2Request\r\n}\r\n\r\nexport function apiHouseHouseLoginStep2Post$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseHouseLoginStep2Post$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseHouseLoginStep2Post$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseHouseLoginStep2Post$Plain.PATH = '/api/House/HouseLoginStep2';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,iCAAiCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAiD,EAAEC,OAAqB;EAC3J,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,iCAAiC,CAACM,IAAI,EAAE,MAAM,CAAC;EACtF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,iCAAiC,CAACM,IAAI,GAAG,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}