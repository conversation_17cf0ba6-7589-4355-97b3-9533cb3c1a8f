{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbCheckboxModule } from '@nebular/theme';\nimport { FormsModule } from '@angular/forms';\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\nimport { NgIf, NgFor } from '@angular/common';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { lastValueFrom } from 'rxjs';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"../../components/shared.observable\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/allowHelper\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"src/app/shared/helper/petternHelper\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = [\"checkAllItem\"];\nconst _c1 = [\"checkItem\"];\nfunction UserManagementComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(47);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_tr_44_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_tr_44_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r4 = i0.ɵɵreference(47);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r6, dialog_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_tr_44_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_tr_44_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const data_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_tr_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 28)(1, \"td\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 30);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 29);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 31);\n    i0.ɵɵtemplate(13, UserManagementComponent_tr_44_button_13_Template, 3, 0, \"button\", 32)(14, UserManagementComponent_tr_44_button_14_Template, 3, 0, \"button\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r6 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r8 + ctx_r2.pageFirst);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CAccount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r6.CUserGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 7, data_r6.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction UserManagementComponent_ng_template_46_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_ng_template_46_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_ng_template_46_app_form_group_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 43)(1, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_app_form_group_14_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.OldPassword, $event) || (ctx_r2.user.OldPassword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u820A\\u5BC6\\u78BC\")(\"labelFor\", \"OldPassword\")(\"isRequired\", ctx_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.OldPassword);\n    i0.ɵɵproperty(\"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u820A\\u5BC6\\u78BC\");\n  }\n}\nfunction UserManagementComponent_ng_template_46_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r11.CId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", group_r11.CName, \"\");\n  }\n}\nfunction UserManagementComponent_ng_template_46_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r12 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r12.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r12.label, \" \");\n  }\n}\nfunction UserManagementComponent_ng_template_46_nb_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r13.name);\n  }\n}\nfunction UserManagementComponent_ng_template_46_nb_select_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 66);\n    i0.ɵɵlistener(\"selectedChange\", function UserManagementComponent_ng_template_46_nb_select_35_Template_nb_select_selectedChange_0_listener($event) {\n      const item_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.isCheck($event, item_r15));\n    });\n    i0.ɵɵelementStart(1, \"nb-option\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-option\", 67);\n    i0.ɵɵtext(4, \"Option 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-option\", 68);\n    i0.ɵɵtext(6, \"Option 3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-option\", 69);\n    i0.ɵɵtext(8, \"Option 4\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", item_r15.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\">\", item_r15.CBuildCaseName, \"\");\n  }\n}\nfunction UserManagementComponent_ng_template_46_label_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 70)(1, \"span\", 58);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 71, 2);\n    i0.ɵɵlistener(\"input\", function UserManagementComponent_ng_template_46_label_36_Template_input_input_3_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.isCheck($event, item_r17));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r17.CBuildCaseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", item_r17.cID)(\"value\", item_r17.cID);\n  }\n}\nfunction UserManagementComponent_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 38)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, UserManagementComponent_ng_template_46_span_2_Template, 2, 0, \"span\", 39)(3, UserManagementComponent_ng_template_46_span_3_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 40)(5, \"div\", 41)(6, \"div\", 42)(7, \"div\", 6)(8, \"app-form-group\", 43)(9, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CAccount, $event) || (ctx_r2.user.CAccount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 43)(11, \"input\", 45);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CMail, $event) || (ctx_r2.user.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 43)(13, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CName, $event) || (ctx_r2.user.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, UserManagementComponent_ng_template_46_app_form_group_14_Template, 2, 5, \"app-form-group\", 47);\n    i0.ɵɵelementStart(15, \"app-form-group\", 43)(16, \"input\", 48);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.Password, $event) || (ctx_r2.user.Password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 43)(18, \"input\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CfmPassword, $event) || (ctx_r2.user.CfmPassword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"app-form-group\", 43)(20, \"nb-select\", 50);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.ListUserGroup, $event) || (ctx_r2.user.ListUserGroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(21, UserManagementComponent_ng_template_46_nb_option_21_Template, 2, 2, \"nb-option\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 43)(23, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CStatus, $event) || (ctx_r2.user.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(24, UserManagementComponent_ng_template_46_nb_option_24_Template, 2, 2, \"nb-option\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 43)(26, \"nb-select\", 53);\n    i0.ɵɵlistener(\"selectedChange\", function UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.useTypeSelect($event));\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_ng_template_46_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.user.CUserType, $event) || (ctx_r2.user.CUserType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(27, UserManagementComponent_ng_template_46_nb_option_27_Template, 2, 2, \"nb-option\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"app-form-group\", 55)(29, \"div\", 56)(30, \"label\", 57)(31, \"span\", 58);\n    i0.ɵɵtext(32, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"input\", 59, 1);\n    i0.ɵɵlistener(\"input\", function UserManagementComponent_ng_template_46_Template_input_input_33_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, UserManagementComponent_ng_template_46_nb_select_35_Template, 9, 2, \"nb-select\", 60)(36, UserManagementComponent_ng_template_46_label_36_Template, 5, 3, \"label\", 61);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(37, \"nb-card-footer\")(38, \"div\", 6)(39, \"div\", 62)(40, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_template_46_Template_button_click_40_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r18));\n    });\n    i0.ɵɵtext(41, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_template_46_Template_button_click_42_listener() {\n      const ref_r18 = i0.ɵɵrestoreView(_r9).dialogRef;\n      return i0.ɵɵresetView(ref_r18.close());\n    });\n    i0.ɵɵtext(43, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5E33\\u865F\")(\"labelFor\", \"CAccount\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.CAccount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\")(\"labelFor\", \"CEmail\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.CMail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u540D\\u7A31\")(\"labelFor\", \"CName\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.CName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BC6\\u78BC\")(\"labelFor\", \"Password\")(\"isRequired\", ctx_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.Password);\n    i0.ɵɵproperty(\"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5BC6\\u78BC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u78BA\\u8A8D\\u5BC6\\u78BC\")(\"labelFor\", \"CfmPassword\")(\"isRequired\", ctx_r2.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.CfmPassword);\n    i0.ɵɵproperty(\"placeholder\", \"\\u8ACB\\u518D\\u6B21\\u8F38\\u5165\\u5BC6\\u78BC\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6B0A\\u9650\")(\"labelFor\", \"CUserGroupId\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.user.ListUserGroup);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userGroups);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.user.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u8EAB\\u5206\")(\"labelFor\", \"identity\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.user.CUserType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\")(\"labelFor\", \"buildCase\")(\"isRequired\", true)(\"hidden\", ctx_r2.user.CUserType === 0 || ctx_r2.user.CUserType === undefined);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nexport class UserManagementComponent extends BaseComponent {\n  constructor(dialogService, share, userService, userGroupService, message, allow, valid, router, pettern, buildCaseService, destroyref) {\n    super(allow);\n    this.dialogService = dialogService;\n    this.share = share;\n    this.userService = userService;\n    this.userGroupService = userGroupService;\n    this.message = message;\n    this.allow = allow;\n    this.valid = valid;\n    this.router = router;\n    this.pettern = pettern;\n    this.buildCaseService = buildCaseService;\n    this.destroyref = destroyref;\n    this.pageFirst = 1;\n    this.request = new ShareRequest();\n    this.userList = [];\n    this.userLisrPerPage = [];\n    this.userGroups = [];\n    this.isNew = false;\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.userType = [{\n      value: 0,\n      name: '系統管理員'\n    }, {\n      value: 1,\n      name: '一般使用者'\n    }];\n    this.selectedBuildcaseList = [];\n    this.buildCaseList = [];\n    this.share.SharedUser.subscribe(res => {\n      this.userList = res;\n    });\n    this.share.SharedUserGroup.subscribe(res => {\n      this.userGroups = res;\n    });\n  }\n  ngOnInit() {\n    this.getList();\n    this.getUserGroup();\n    this.getBuildCaseList();\n  }\n  // 取得群組列表\n  getUserGroup() {\n    this.userGroupService.apiUserGroupGetListPost$Json({\n      body: {\n        CName: \"\",\n        CStatus: 1,\n        PageIndex: 1,\n        PageSize: 1000\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.userGroups = res.Entries;\n      this.share.SetUserGroup(this.userGroups);\n    });\n  }\n  // 取得使用者列表\n  getList() {\n    this.userService.apiUserGetListPost$Json({\n      body: {\n        CStatus: this.request.CStatus,\n        CUserName: this.request.CUserName\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.userList = res.Entries;\n      this.totalRecords = res.TotalItems;\n      this.handlepagination();\n      this.share.SetUser(this.userList);\n    });\n  }\n  handlepagination() {\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\n    let lastIndex = this.totalRecords < this.pageIndex * this.pageSize ? this.totalRecords + 1 : this.pageIndex * this.pageSize;\n    this.userLisrPerPage = this.userList.slice(this.pageFirst - 1, lastIndex);\n  }\n  // 取得使用者資料\n  getData() {\n    return lastValueFrom(this.userService.apiUserGetDataPost$Json({\n      body: {\n        CUserId: this.request.CUserId.toString()\n      }\n    })).then(res => {\n      this.user = res.Entries;\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n    });\n  }\n  useTypeSelect(e) {\n    this.user.CUserType = e;\n  }\n  isCheck(e, item) {\n    let isSelected = this.selectedBuildcaseList.find(i => i.CBuilCaseId === item.cID);\n    if (!isSelected) {\n      this.selectedBuildcaseList.push({\n        CBuilCaseId: item.cID,\n        CBuilCaseName: item.CBuildCaseName\n      });\n    } else {\n      this.selectedBuildcaseList = this.selectedBuildcaseList.filter(i => i.CBuilCaseId !== item.cID);\n    }\n  }\n  toggleSelectAll(checked) {\n    // toggle all option status\n    this.checkItemEl.forEach(i => {\n      i.nativeElement.checked = checked.target.checked ? true : false;\n    });\n    // set all buildcase in request \n    if (checked.target.checked) {\n      this.selectedBuildcaseList = this.buildCaseList.map(i => {\n        return {\n          CBuilCaseId: i.cID,\n          CBuilCaseName: i.CBuildCaseName\n        };\n      });\n    } else {\n      this.selectedBuildcaseList = [];\n    }\n  }\n  add(dialog) {\n    this.user = {\n      CAccount: '',\n      CfmPassword: '',\n      CName: '',\n      CStatus: undefined,\n      CUserId: '',\n      ListUserGroup: [],\n      OldPassword: '',\n      Password: ''\n    };\n    this.isNew = true;\n    this.dialogService.open(dialog);\n  }\n  setUserBuildCase() {\n    switch (this.user.CUserType) {\n      case 0:\n        this.user.UserBuildCases = [];\n        break;\n      case 1:\n        this.user.UserBuildCases = this.selectedBuildcaseList.map(i => {\n          return {\n            CBuilCaseId: i.CBuilCaseId,\n            CBuilCaseName: i.CBuilCaseName\n          };\n        });\n        break;\n      default:\n    }\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.setUserBuildCase();\n    if (this.isNew) {\n      this.userService.apiUserAddDataPost$Json({\n        body: this.user\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(`${res.Message}`);\n        }\n      });\n    } else {\n      this.userService.apiUserSaveDataPost$Json({\n        body: this.user\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(`${res.Message}`);\n        }\n      });\n    }\n  }\n  remove() {\n    this.userService.apiUserRemoveUserPost$Json({\n      body: {\n        CUserId: this.request.CUserId.toString()\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      }\n    });\n  }\n  onDelete(data) {\n    if (window.confirm('是否確定刪除?')) {\n      this.userService.apiUserRemoveUserPost$Json({\n        body: {\n          CUserId: data.CUserId\n        }\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG('執行成功');\n          this.getList();\n        }\n      });\n    } else {\n      return;\n    }\n  }\n  onEdit(data, dialog) {\n    this.isNew = false;\n    this.userService.apiUserGetDataPost$Json({\n      body: {\n        CUserId: data.CUserId\n      }\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.user = res.Entries;\n      this.selectedBuildcaseList = [...(this.user.UserBuildCases ?? [])];\n      setTimeout(() => {\n        this.checkAllItemEl.nativeElement.checked = true;\n        this.checkItemEl.forEach(i => {\n          let findBuildcae = res.Entries?.UserBuildCases?.find(item => item.CBuilCaseId?.toString() === i.nativeElement.id);\n          if (findBuildcae) {\n            i.nativeElement.checked = true;\n          } else {\n            this.checkAllItemEl.nativeElement.checked = false;\n          }\n        });\n      }, 0);\n      this.dialogService.open(dialog);\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[帳號]', this.user.CAccount);\n    this.valid.pattern('[帳號]', this.user.CAccount, this.pettern.AccountPettern, '只限英文字母及數字，長度介於3~20個字元!');\n    this.valid.required('[電子郵件]', this.user.CMail);\n    this.valid.pattern('[電子郵件]', this.user.CMail, this.pettern.MailPettern);\n    this.valid.required('[名稱]', this.user.CName);\n    if (this.isNew) {\n      this.valid.required('[密碼]', this.user.Password);\n      this.valid.required('[確認密碼]', this.user.CfmPassword);\n      this.valid.pattern('[確認密碼]', this.user.CfmPassword, this.pettern.PasswordPettern);\n      this.valid.pattern('[密碼]', this.user.Password, this.pettern.PasswordPettern);\n    }\n    this.valid.equal('[密碼]', '[確認密碼]', this.user.Password, this.user.CfmPassword);\n    if (this.user.ListUserGroup.length < 1) {\n      this.valid.required('[權限]', null);\n    }\n    this.valid.required('[狀態]', this.user.CStatus);\n    this.valid.required('[身分]', this.user.CUserType);\n    if (this.user.CUserType === 1 && this.selectedBuildcaseList.length < 1) {\n      this.valid.addErrorMessage('[建案] 必填');\n    }\n  }\n  static {\n    this.ɵfac = function UserManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserManagementComponent)(i0.ɵɵdirectiveInject(i1.NbDialogService), i0.ɵɵdirectiveInject(i2.SharedObservable), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i3.UserGroupService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.AllowHelper), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.PetternHelper), i0.ɵɵdirectiveInject(i3.BuildCaseService), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserManagementComponent,\n      selectors: [[\"ngx-user-management\"]],\n      viewQuery: function UserManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkAllItemEl = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.checkItemEl = _t);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 48,\n      vars: 10,\n      consts: [[\"dialog\", \"\"], [\"checkAllItem\", \"\"], [\"checkItem\", \"\"], [\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"keyWord\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"keyWord\", \"name\", \"keyWord\", \"placeholder\", \"\\u95DC\\u9375\\u5B57\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"col-12\", \"col-md-3\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [3, \"selectedChange\", \"selected\"], [3, \"value\"], [1, \"form-group\", \"col-12\", \"col-md-5\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", 1, \"col-2\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-1\"], [1, \"col-3\"], [1, \"col-2\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"row\", \"p-2\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CAccount\", \"name\", \"CAccount\", \"placeholder\", \"\\u5E33\\u865F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CEmail\", \"name\", \"CEmail\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CName\", \"name\", \"CName\", \"placeholder\", \"\\u540D\\u7A31\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [\"type\", \"Password\", \"nbInput\", \"\", \"id\", \"Password\", \"name\", \"Password\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"type\", \"Password\", \"nbInput\", \"\", \"id\", \"CfmPassword\", \"name\", \"CfmPassword\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"multiple\", \"\", \"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CUserGroupId\", \"name\", \"CUserGroupId\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u8EAB\\u5206\", \"id\", \"'identity'\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"label\", \"labelFor\", \"isRequired\", \"hidden\"], [1, \"d-flex\", \"flex-wrap\", \"flex-grow-1\", 2, \"gap\", \"0.8rem\"], [\"for\", \"all\", 1, \"d-flex\", \"align-items-center\"], [1, \"mr-1\"], [\"type\", \"checkbox\", \"id\", \"all\", 3, \"input\"], [\"multiple\", \"\", 3, \"selectedChange\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex align-items-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"type\", \"Password\", \"nbInput\", \"\", \"id\", \"OldPassword\", \"name\", \"OldPassword\", 3, \"ngModelChange\", \"ngModel\", \"placeholder\"], [\"langg\", \"\", 3, \"value\"], [\"multiple\", \"\", 3, \"selectedChange\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"checkbox\", 3, \"input\", \"id\", \"value\"]],\n      template: function UserManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"div\", 7)(7, \"label\", 8);\n          i0.ɵɵtext(8, \"\\u95DC\\u9375\\u5B57\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.request.CUserName, $event) || (ctx.request.CUserName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 10)(11, \"label\", 11);\n          i0.ɵɵtext(12, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function UserManagementComponent_Template_nb_select_selectedChange_13_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.request.CStatus, $event) || (ctx.request.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(14, \"nb-option\", 13);\n          i0.ɵɵtext(15, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-option\", 13);\n          i0.ɵɵtext(17, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"nb-option\", 13);\n          i0.ɵɵtext(19, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 14)(21, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_21_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(22, \"i\", 16);\n          i0.ɵɵtext(23, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, UserManagementComponent_button_24_Template, 3, 0, \"button\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"nb-card-body\", 4)(26, \"div\", 5)(27, \"div\", 18)(28, \"table\", 19)(29, \"thead\")(30, \"tr\", 20)(31, \"th\", 21);\n          i0.ɵɵtext(32, \"\\u5E8F\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"th\", 22);\n          i0.ɵɵtext(34, \"\\u4F7F\\u7528\\u8005\\u5E33\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"th\", 23);\n          i0.ɵɵtext(36, \"\\u4F7F\\u7528\\u8005\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"th\", 22);\n          i0.ɵɵtext(38, \"\\u4F7F\\u7528\\u8005\\u6B0A\\u9650\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"th\", 21);\n          i0.ɵɵtext(40, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"th\", 23);\n          i0.ɵɵtext(42, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"tbody\");\n          i0.ɵɵtemplate(44, UserManagementComponent_tr_44_Template, 15, 9, \"tr\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"ngx-pagination\", 25);\n          i0.ɵɵtwoWayListener(\"PageChange\", function UserManagementComponent_Template_ngx_pagination_PageChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function UserManagementComponent_Template_ngx_pagination_PageChange_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.handlepagination());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(46, UserManagementComponent_ng_template_46_Template, 44, 47, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.request.CUserName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.request.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.userLisrPerPage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i1.NbInputDirective, FormsModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, NbSelectModule, i1.NbSelectComponent, i1.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, FormGroupComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ1c2VyLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc3lzdGVtLW1hbmFnZW1lbnQvdXNlci1tYW5hZ2VtZW50L3VzZXItbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "NbCardModule", "NbInputModule", "NbSelectModule", "NbOptionModule", "NbCheckboxModule", "FormsModule", "StatusPipe", "NgIf", "<PERSON><PERSON><PERSON>", "ShareRequest", "BreadcrumbComponent", "PaginationComponent", "FormGroupComponent", "lastValueFrom", "takeUntilDestroyed", "i0", "ɵɵelementStart", "ɵɵlistener", "UserManagementComponent_button_24_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialog_r4", "ɵɵreference", "ɵɵresetView", "add", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "UserManagementComponent_tr_44_button_13_Template_button_click_0_listener", "_r5", "data_r6", "$implicit", "onEdit", "UserManagementComponent_tr_44_button_14_Template_button_click_0_listener", "_r7", "onDelete", "ɵɵtemplate", "UserManagementComponent_tr_44_button_13_Template", "UserManagementComponent_tr_44_button_14_Template", "ɵɵadvance", "ɵɵtextInterpolate", "i_r8", "pageFirst", "CAccount", "CName", "CUserGroupName", "ɵɵpipeBind1", "CStatus", "ɵɵproperty", "isUpdate", "isDelete", "ɵɵtwoWayListener", "UserManagementComponent_ng_template_46_app_form_group_14_Template_input_ngModelChange_1_listener", "$event", "_r10", "ɵɵtwoWayBindingSet", "user", "OldPassword", "isNew", "ɵɵtwoWayProperty", "group_r11", "CId", "ɵɵtextInterpolate1", "status_r12", "value", "label", "item_r13", "name", "UserManagementComponent_ng_template_46_nb_select_35_Template_nb_select_selectedChange_0_listener", "item_r15", "_r14", "is<PERSON><PERSON><PERSON>", "cID", "CBuildCaseName", "UserManagementComponent_ng_template_46_label_36_Template_input_input_3_listener", "item_r17", "_r16", "UserManagementComponent_ng_template_46_span_2_Template", "UserManagementComponent_ng_template_46_span_3_Template", "UserManagementComponent_ng_template_46_Template_input_ngModelChange_9_listener", "_r9", "UserManagementComponent_ng_template_46_Template_input_ngModelChange_11_listener", "CMail", "UserManagementComponent_ng_template_46_Template_input_ngModelChange_13_listener", "UserManagementComponent_ng_template_46_app_form_group_14_Template", "UserManagementComponent_ng_template_46_Template_input_ngModelChange_16_listener", "Password", "UserManagementComponent_ng_template_46_Template_input_ngModelChange_18_listener", "CfmPassword", "UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_20_listener", "ListUserGroup", "UserManagementComponent_ng_template_46_nb_option_21_Template", "UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_23_listener", "UserManagementComponent_ng_template_46_nb_option_24_Template", "UserManagementComponent_ng_template_46_Template_nb_select_selectedChange_26_listener", "useTypeSelect", "UserManagementComponent_ng_template_46_Template_nb_select_ngModelChange_26_listener", "CUserType", "UserManagementComponent_ng_template_46_nb_option_27_Template", "UserManagementComponent_ng_template_46_Template_input_input_33_listener", "toggleSelectAll", "UserManagementComponent_ng_template_46_nb_select_35_Template", "UserManagementComponent_ng_template_46_label_36_Template", "UserManagementComponent_ng_template_46_Template_button_click_40_listener", "ref_r18", "dialogRef", "save", "UserManagementComponent_ng_template_46_Template_button_click_42_listener", "close", "userGroups", "statusOptions", "userType", "undefined", "buildCaseList", "UserManagementComponent", "constructor", "dialogService", "share", "userService", "userGroupService", "message", "allow", "valid", "router", "pettern", "buildCaseService", "destroyref", "request", "userList", "userLisrPerPage", "selectedBuildcaseList", "SharedUser", "subscribe", "res", "SharedUserGroup", "ngOnInit", "getList", "getUserGroup", "getBuildCaseList", "apiUserGroupGetListPost$Json", "body", "PageIndex", "PageSize", "pipe", "Entries", "SetUserGroup", "apiUserGetListPost$Json", "CUserName", "totalRecords", "TotalItems", "handlepagination", "SetUser", "pageIndex", "pageSize", "lastIndex", "slice", "getData", "apiUserGetDataPost$Json", "CUserId", "toString", "then", "apiBuildCaseGetBuildCaseListPost$Json", "e", "item", "isSelected", "find", "i", "CBuilCaseId", "push", "CBuilCaseName", "filter", "checked", "checkItemEl", "for<PERSON>ach", "nativeElement", "target", "map", "dialog", "open", "setUserBuildCase", "UserBuildCases", "ref", "validation", "errorMessages", "length", "showErrorMSGs", "apiUserAddDataPost$Json", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "apiUserSaveDataPost$Json", "remove", "apiUserRemoveUserPost$Json", "data", "window", "confirm", "setTimeout", "checkAllItemEl", "findBuildcae", "id", "clear", "required", "pattern", "Account<PERSON><PERSON><PERSON>", "MailPettern", "PasswordPettern", "equal", "addErrorMessage", "ɵɵdirectiveInject", "i1", "NbDialogService", "i2", "SharedObservable", "i3", "UserService", "UserGroupService", "i4", "MessageService", "i5", "AllowHelper", "i6", "ValidationHelper", "i7", "Router", "i8", "PetternHelper", "BuildCaseService", "DestroyRef", "selectors", "viewQuery", "UserManagementComponent_Query", "rf", "ctx", "UserManagementComponent_Template_input_ngModelChange_9_listener", "_r1", "UserManagementComponent_Template_nb_select_selected<PERSON>hange_13_listener", "UserManagementComponent_Template_button_click_21_listener", "UserManagementComponent_button_24_Template", "UserManagementComponent_tr_44_Template", "UserManagementComponent_Template_ngx_pagination_PageChange_45_listener", "UserManagementComponent_ng_template_46_Template", "ɵɵtemplateRefExtractor", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbSelectComponent", "NbOptionComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\system-management\\user-management\\user-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\system-management\\user-management\\user-management.component.html"], "sourcesContent": ["\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, DestroyRef, ElementRef, OnInit, QueryList, TemplateRef, ViewChild, ViewChildren } from '@angular/core';\r\nimport { NbDialogService, NbCardModule, NbInputModule, NbSelectModule, NbOptionModule, NbCheckboxComponent, NbCheckboxModule } from '@nebular/theme';\r\n\r\nimport { EnumStatusCode } from '../../../shared/enum/enumStatusCode';\r\nimport { FormGroup, Validators, FormsModule } from '@angular/forms';\r\n\r\nimport { Router } from '@angular/router';\r\n\r\nimport { StatusPipe } from '../../../@theme/pipes/mapping.pipe';\r\nimport { NgIf, NgFor } from '@angular/common';\r\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\r\nimport { BuildCaseService, UserGroupService, UserService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { BuildCaseGetListReponse, UserBuildCase, UserGetDataResponse, UserGetListResponse, UserGroupGetListResponse, UserSaveDataArgs } from 'src/services/api/models';\r\nimport { User } from 'src/app/shared/model/user.model';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { SharedObservable } from '../../components/shared.observable';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { lastValueFrom } from 'rxjs';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BaseLabelDirective } from 'src/app/@theme/directives/label.directive';\r\n\r\n@Component({\r\n  selector: 'ngx-user-management',\r\n  templateUrl: './user-management.component.html',\r\n  styleUrls: ['./user-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    BaseLabelDirective,\r\n    NbCheckboxModule,\r\n    FormGroupComponent\r\n  ],\r\n})\r\nexport class UserManagementComponent extends BaseComponent implements OnInit {\r\n  override pageFirst = 1\r\n  request = new ShareRequest();\r\n  userList = [] as UserGetListResponse[];\r\n  userLisrPerPage = [] as UserGetListResponse[];\r\n  user: UserSaveDataArgs;\r\n  userGroups = [] as UserGroupGetListResponse[];\r\n\r\n  isNew = false;\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n\r\n  userType = [\r\n    { value: 0, name: '系統管理員' },\r\n    { value: 1, name: '一般使用者' },\r\n  ];\r\n  selectedBuildcaseList:UserBuildCase[]=[];\r\n\r\n  buildCaseList:BuildCaseGetListReponse[] | null | undefined = []\r\n  @ViewChildren(\"checkItem\") checkItemEl: QueryList<any>;\r\n  @ViewChild('checkAllItem') checkAllItemEl: ElementRef;\r\n\r\n  constructor(\r\n    private dialogService: NbDialogService,\r\n    private share: SharedObservable,\r\n    private userService: UserService,\r\n    private userGroupService: UserGroupService,\r\n    private message: MessageService,\r\n    protected override allow: AllowHelper,\r\n    private valid: ValidationHelper,\r\n    private router: Router,\r\n    private pettern: PetternHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(allow);\r\n\r\n    this.share.SharedUser.subscribe(res => {\r\n      this.userList = res;\r\n    });\r\n    this.share.SharedUserGroup.subscribe(res => {\r\n      this.userGroups = res;\r\n    });\r\n    \r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getList();\r\n    this.getUserGroup();\r\n    this.getBuildCaseList();\r\n  }\r\n\r\n  // 取得群組列表\r\n  getUserGroup() {\r\n    this.userGroupService.apiUserGroupGetListPost$Json({\r\n      body: {\r\n        CName: \"\",\r\n        CStatus: 1,\r\n        PageIndex: 1,\r\n        PageSize: 1000\r\n      }\r\n    })\r\n    .pipe(takeUntilDestroyed(this.destroyref))\r\n    .subscribe(res => {\r\n      this.userGroups = res.Entries!;\r\n      this.share.SetUserGroup(this.userGroups);\r\n    });\r\n  }\r\n\r\n  // 取得使用者列表\r\n  getList() {\r\n    this.userService.apiUserGetListPost$Json({\r\n      body: {\r\n        CStatus: this.request.CStatus,\r\n        CUserName: this.request.CUserName,\r\n      }\r\n    })\r\n    .pipe(takeUntilDestroyed(this.destroyref))\r\n    .subscribe(res => {\r\n      this.userList = res.Entries!;\r\n      this.totalRecords = res.TotalItems!;\r\n      this.handlepagination();\r\n      this.share.SetUser(this.userList);\r\n    });\r\n  }\r\n\r\n  handlepagination(){\r\n    this.pageFirst = (this.pageIndex - 1) * this.pageSize + 1;\r\n    let lastIndex = (this.totalRecords < this.pageIndex * this.pageSize) ? this.totalRecords + 1 : (this.pageIndex * this.pageSize);\r\n    this.userLisrPerPage = this.userList.slice(this.pageFirst - 1, lastIndex);\r\n  }\r\n\r\n\r\n  // 取得使用者資料\r\n  getData(): Promise<void> {\r\n    return lastValueFrom(this.userService.apiUserGetDataPost$Json({\r\n      body: {\r\n        CUserId: this.request.CUserId!.toString()\r\n      }\r\n    })).then(res => {\r\n      this.user = res.Entries!;\r\n    }); \r\n  }\r\n\r\n  getBuildCaseList(){\r\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json()\r\n    .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      this.buildCaseList = res.Entries;\r\n    })\r\n  }\r\n\r\n  useTypeSelect(e:any){\r\n    this.user.CUserType = e;\r\n  }\r\n\r\n  isCheck(e:any, item:BuildCaseGetListReponse){\r\n    let isSelected = this.selectedBuildcaseList.find(i => i.CBuilCaseId === item.cID);\r\n    if(!isSelected){\r\n      this.selectedBuildcaseList.push({\r\n        CBuilCaseId: item.cID,\r\n        CBuilCaseName: item.CBuildCaseName\r\n      })\r\n    } else {\r\n      this.selectedBuildcaseList = this.selectedBuildcaseList.filter(i => i.CBuilCaseId !== item.cID)\r\n    }\r\n  }\r\n\r\n  toggleSelectAll(checked:any){\r\n    // toggle all option status\r\n    this.checkItemEl.forEach(i => {\r\n      i.nativeElement.checked = checked.target.checked ? true : false;\r\n    });\r\n\r\n    // set all buildcase in request \r\n    if(checked.target.checked){\r\n      this.selectedBuildcaseList = this.buildCaseList!.map(i => {\r\n        return {\r\n          CBuilCaseId: i.cID,\r\n          CBuilCaseName: i.CBuildCaseName\r\n        }\r\n      })\r\n    } else {\r\n      this.selectedBuildcaseList = [];\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.user = {\r\n      CAccount: '',\r\n      CfmPassword: '',\r\n      CName: '',\r\n      CStatus: undefined,\r\n      CUserId: '',\r\n      ListUserGroup: [],\r\n      OldPassword: '',\r\n      Password: ''\r\n    }\r\n    this.isNew = true;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  setUserBuildCase(){\r\n    switch(this.user.CUserType){\r\n      case 0:\r\n        this.user.UserBuildCases = [];\r\n        break\r\n      case 1:\r\n        this.user.UserBuildCases = this.selectedBuildcaseList.map(i => {\r\n          return {\r\n            CBuilCaseId: i.CBuilCaseId,\r\n            CBuilCaseName: i.CBuilCaseName\r\n          }\r\n        })\r\n        break \r\n      default:\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.setUserBuildCase();\r\n\r\n    if (this.isNew) {\r\n      this.userService.apiUserAddDataPost$Json({\r\n        body: this.user\r\n      })\r\n      .pipe(takeUntilDestroyed(this.destroyref))\r\n      .subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(`${res.Message}`)\r\n        }\r\n      });\r\n    } else {\r\n      this.userService.apiUserSaveDataPost$Json({\r\n        body: this.user\r\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n          ref.close();\r\n        }else {\r\n          this.message.showErrorMSG(`${res.Message}`)\r\n        }\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  remove() {\r\n    this.userService.apiUserRemoveUserPost$Json({\r\n      body: {\r\n        CUserId: this.request.CUserId.toString()\r\n      }\r\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      if(res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      }\r\n    });\r\n  }\r\n\r\n  onDelete(data: UserGetDataResponse) {\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.userService.apiUserRemoveUserPost$Json({\r\n        body: {\r\n          CUserId: data.CUserId\r\n        }\r\n      }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        if(res.StatusCode === 0) {\r\n          this.message.showSucessMSG('執行成功');\r\n          this.getList();\r\n        }\r\n      });\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  onEdit(data: UserGetDataResponse, dialog: TemplateRef<any>) {\r\n    this.isNew = false;\r\n    this.userService.apiUserGetDataPost$Json({\r\n      body:{\r\n        CUserId: data.CUserId\r\n      }\r\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n      this.user = res.Entries!;\r\n      this.selectedBuildcaseList = [...(this.user.UserBuildCases ?? [])];\r\n      \r\n      setTimeout(() => {\r\n        this.checkAllItemEl.nativeElement.checked = true\r\n        this.checkItemEl.forEach(i => {          \r\n          let findBuildcae = res.Entries?.UserBuildCases?.find(item => item.CBuilCaseId?.toString() === i.nativeElement.id);\r\n          if(findBuildcae){\r\n            i.nativeElement.checked = true\r\n          } else {\r\n            this.checkAllItemEl.nativeElement.checked = false\r\n          }\r\n        })\r\n      }, 0);\r\n           \r\n      this.dialogService.open(dialog);\r\n    })\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[帳號]', this.user.CAccount);\r\n    this.valid.pattern('[帳號]', this.user.CAccount, this.pettern.AccountPettern, '只限英文字母及數字，長度介於3~20個字元!');\r\n    this.valid.required('[電子郵件]', this.user.CMail);\r\n    this.valid.pattern('[電子郵件]',this.user.CMail, this.pettern.MailPettern)\r\n    this.valid.required('[名稱]', this.user.CName);\r\n    if (this.isNew) {\r\n      this.valid.required('[密碼]', this.user.Password);\r\n      this.valid.required('[確認密碼]', this.user.CfmPassword);\r\n      this.valid.pattern('[確認密碼]', this.user.CfmPassword, this.pettern.PasswordPettern);\r\n      this.valid.pattern('[密碼]', this.user.Password, this.pettern.PasswordPettern);\r\n    }\r\n    this.valid.equal('[密碼]', '[確認密碼]', this.user.Password, this.user.CfmPassword);\r\n    if(this.user.ListUserGroup!.length < 1){\r\n      this.valid.required('[權限]', null);\r\n    }\r\n    this.valid.required('[狀態]', this.user.CStatus);\r\n    this.valid.required('[身分]', this.user.CUserType);\r\n\r\n    if(this.user.CUserType === 1 && this.selectedBuildcaseList.length < 1){\r\n      this.valid.addErrorMessage('[建案] 必填' )\r\n    }\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"keyWord\" class=\"label mr-2\">關鍵字</label>\r\n          <input type=\"text\" nbInput id=\"keyWord\" name=\"keyWord\" placeholder=\"關鍵字\" [(ngModel)]=\"request.CUserName\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(selected)]=\"request.CStatus\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-5 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增使用者</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-1\">序號</th>\r\n              <th scope=\"col\" class=\"col-3\">使用者帳號</th>\r\n              <th scope=\"col\" class=\"col-2\">使用者名稱</th>\r\n              <th scope=\"col\" class=\"col-3\">使用者權限</th>\r\n              <th scope=\"col\" class=\"col-1\">狀態</th>\r\n              <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of userLisrPerPage; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-1\">{{ i + pageFirst }}</td>\r\n              <td class=\"col-3\">{{ data.CAccount }}</td>\r\n              <td class=\"col-2\">{{ data.CName }}</td>\r\n              <td class=\"col-3\">{{ data.CUserGroupName }}</td>\r\n              <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n              <td class=\"col-2\">\r\n                <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                  (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                  (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"handlepagination()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增使用者</span>\r\n      <span *ngIf=\"isNew===false\">編輯使用者</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row  p-2\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'帳號'\" [labelFor]=\"'CAccount'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CAccount\" name=\"CAccount\" placeholder=\"帳號\"\r\n                [(ngModel)]=\"user.CAccount\" [disabled]=\"!isNew\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'電子郵件'\" [labelFor]=\"'CEmail'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CEmail\" name=\"CEmail\" placeholder=\"電子郵件\"\r\n                [(ngModel)]=\"user.CMail\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'名稱'\" [labelFor]=\"'CName'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CName\" name=\"CName\" placeholder=\"名稱\"\r\n                [(ngModel)]=\"user.CName\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'舊密碼'\" [labelFor]=\"'OldPassword'\" *ngIf=\"!isNew\" [isRequired]=\"isNew\">\r\n              <input type=\"Password\" nbInput id=\"OldPassword\" name=\"OldPassword\" [(ngModel)]=\"user.OldPassword\"\r\n                [placeholder]=\"'請輸入舊密碼'\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'密碼'\" [labelFor]=\"'Password'\" [isRequired]=\"isNew\">\r\n              <input type=\"Password\" nbInput class=\"flex-grow-1\" id=\"Password\" name=\"Password\"\r\n                [(ngModel)]=\"user.Password\" [placeholder]=\"'請輸入密碼'\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'確認密碼'\" [labelFor]=\"'CfmPassword'\" [isRequired]=\"isNew\">\r\n              <input type=\"Password\" nbInput class=\"flex-grow-1\" id=\"CfmPassword\" name=\"CfmPassword\"\r\n                [(ngModel)]=\"user.CfmPassword\" [placeholder]=\"'請再次輸入密碼'\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'權限'\" [labelFor]=\"'CUserGroupId'\" [isRequired]=\"true\">\r\n              <nb-select multiple class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CUserGroupId\" name=\"CUserGroupId\"\r\n                [(selected)]=\"user.ListUserGroup\">\r\n                <nb-option langg *ngFor=\"let group of userGroups\" [value]=\"group.CId\"> {{group.CName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\" [(selected)]=\"user.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\"> {{status.label}}\r\n                </nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'身分'\" [labelFor]=\"'identity'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇身分\" id=\"'identity'\"\r\n                (selectedChange)=\"useTypeSelect($event)\" [(ngModel)]=\"user.CUserType\">\r\n                <nb-option *ngFor=\"let item of userType\" [value]=\"item.value\">{{item.name}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'建案'\" [labelFor]=\"'buildCase'\" [isRequired]=\"true\"\r\n              [hidden]=\"user.CUserType === 0 || user.CUserType === undefined\">\r\n              <div class=\"d-flex flex-wrap flex-grow-1\" style=\"gap: 0.8rem\">\r\n                <label for=\"all\" class=\"d-flex align-items-center\">\r\n                  <span class=\"mr-1\">全選</span>\r\n                  <input type=\"checkbox\" id=\"all\" (input)=\"toggleSelectAll($event)\" #checkAllItem />\r\n                </label>\r\n                <nb-select multiple *ngFor=\"let item of buildCaseList\" (selectedChange)=\"isCheck($event, item)\">\r\n                  <nb-option [value]=\"item.cID\">>{{item.CBuildCaseName}}</nb-option>\r\n                  <nb-option value=\"2\">Option 2</nb-option>\r\n                  <nb-option value=\"3\">Option 3</nb-option>\r\n                  <nb-option value=\"4\">Option 4</nb-option>\r\n                </nb-select>\r\n                <label class=\"d-flex align-items-center\" *ngFor=\"let item of buildCaseList\">\r\n                  <span class=\"mr-1\">{{item.CBuildCaseName}}</span>\r\n                  <input type=\"checkbox\" [id]=\"item.cID\" [value]=\"item.cID\" (input)=\"isCheck($event, item)\"\r\n                    #checkItem />\r\n                </label>\r\n              </div>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAA0BC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAuBC,gBAAgB,QAAQ,gBAAgB;AAGpJ,SAAgCC,WAAW,QAAQ,gBAAgB;AAInE,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AAC7C,SAASC,YAAY,QAAQ,4CAA4C;AAQzE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,mBAAmB,QAAQ,kDAAkD;AAEtF,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,aAAa,QAAQ,MAAM;AACpC,SAASC,kBAAkB,QAAQ,4BAA4B;;;;;;;;;;;;;;;;ICJrDC,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,GAAA,CAAAH,SAAA,CAAW;IAAA,EAAC;IAAkBR,EAAA,CAAAY,SAAA,YAC3C;IAAAZ,EAAA,CAAAa,MAAA,qCAAK;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IA4BzCd,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAAE,UAAA,mBAAAa,yEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,SAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAa,MAAA,CAAAF,OAAA,EAAAT,SAAA,CAAmB;IAAA,EAAC;IAACR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,mBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAC3Ed,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAAE,UAAA,mBAAAkB,yEAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAJ,OAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgB,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAACjB,EAAA,CAAAY,SAAA,YAAqC;IAAAZ,EAAA,CAAAa,MAAA,mBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAT7Ed,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC1Cd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC1Cd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,GAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAChDd,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAa,MAAA,IAAkC;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACzDd,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAuB,UAAA,KAAAC,gDAAA,qBACgC,KAAAC,gDAAA,qBAEL;IAE/BzB,EADE,CAAAc,YAAA,EAAK,EACF;;;;;;IAXed,EAAA,CAAA0B,SAAA,GAAmB;IAAnB1B,EAAA,CAAA2B,iBAAA,CAAAC,IAAA,GAAAtB,MAAA,CAAAuB,SAAA,CAAmB;IACnB7B,EAAA,CAAA0B,SAAA,GAAmB;IAAnB1B,EAAA,CAAA2B,iBAAA,CAAAV,OAAA,CAAAa,QAAA,CAAmB;IACnB9B,EAAA,CAAA0B,SAAA,GAAgB;IAAhB1B,EAAA,CAAA2B,iBAAA,CAAAV,OAAA,CAAAc,KAAA,CAAgB;IAChB/B,EAAA,CAAA0B,SAAA,GAAyB;IAAzB1B,EAAA,CAAA2B,iBAAA,CAAAV,OAAA,CAAAe,cAAA,CAAyB;IACzBhC,EAAA,CAAA0B,SAAA,GAAkC;IAAlC1B,EAAA,CAAA2B,iBAAA,CAAA3B,EAAA,CAAAiC,WAAA,QAAAhB,OAAA,CAAAiB,OAAA,EAAkC;IAEzClC,EAAA,CAAA0B,SAAA,GAAc;IAAd1B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA8B,QAAA,CAAc;IAEdpC,EAAA,CAAA0B,SAAA,EAAc;IAAd1B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA+B,QAAA,CAAc;;;;;IAkBjCrC,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAa,MAAA,qCAAK;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IACvCd,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAa,MAAA,qCAAK;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;;IAmBhCd,EADF,CAAAC,cAAA,yBAA+F,gBAElE;IADwCD,EAAA,CAAAsC,gBAAA,2BAAAC,iGAAAC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,IAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAC,WAAA,EAAAJ,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAC,WAAA,GAAAJ,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA8B;IAEnGxC,EAFE,CAAAc,YAAA,EAC2B,EACZ;;;;IAHyDd,EAA1D,CAAAmC,UAAA,+BAAe,2BAA2B,eAAA7B,MAAA,CAAAuC,KAAA,CAAoC;IACzB7C,EAAA,CAAA0B,SAAA,EAA8B;IAA9B1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAAC,WAAA,CAA8B;IAC/F5C,EAAA,CAAAmC,UAAA,uDAAwB;;;;;IAaxBnC,EAAA,CAAAC,cAAA,oBAAsE;IAACD,EAAA,CAAAa,MAAA,GAAe;IAAAb,EAAA,CAAAc,YAAA,EAAY;;;;IAAhDd,EAAA,CAAAmC,UAAA,UAAAY,SAAA,CAAAC,GAAA,CAAmB;IAAEhD,EAAA,CAAA0B,SAAA,EAAe;IAAf1B,EAAA,CAAAiD,kBAAA,MAAAF,SAAA,CAAAhB,KAAA,KAAe;;;;;IAKtF/B,EAAA,CAAAC,cAAA,oBAA6E;IAACD,EAAA,CAAAa,MAAA,GAC9E;IAAAb,EAAA,CAAAc,YAAA,EAAY;;;;IAD0Cd,EAAA,CAAAmC,UAAA,UAAAe,UAAA,CAAAC,KAAA,CAAsB;IAAEnD,EAAA,CAAA0B,SAAA,EAC9E;IAD8E1B,EAAA,CAAAiD,kBAAA,MAAAC,UAAA,CAAAE,KAAA,MAC9E;;;;;IAMApD,EAAA,CAAAC,cAAA,oBAA8D;IAAAD,EAAA,CAAAa,MAAA,GAAa;IAAAb,EAAA,CAAAc,YAAA,EAAY;;;;IAA9Cd,EAAA,CAAAmC,UAAA,UAAAkB,QAAA,CAAAF,KAAA,CAAoB;IAACnD,EAAA,CAAA0B,SAAA,EAAa;IAAb1B,EAAA,CAAA2B,iBAAA,CAAA0B,QAAA,CAAAC,IAAA,CAAa;;;;;;IAU3EtD,EAAA,CAAAC,cAAA,oBAAgG;IAAzCD,EAAA,CAAAE,UAAA,4BAAAqD,iGAAAf,MAAA;MAAA,MAAAgB,QAAA,GAAAxD,EAAA,CAAAI,aAAA,CAAAqD,IAAA,EAAAvC,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAkBJ,MAAA,CAAAoD,OAAA,CAAAlB,MAAA,EAAAgB,QAAA,CAAqB;IAAA,EAAC;IAC7FxD,EAAA,CAAAC,cAAA,oBAA8B;IAAAD,EAAA,CAAAa,MAAA,GAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAY;IAClEd,EAAA,CAAAC,cAAA,oBAAqB;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAY;IACzCd,EAAA,CAAAC,cAAA,oBAAqB;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAY;IACzCd,EAAA,CAAAC,cAAA,oBAAqB;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAC/Bb,EAD+B,CAAAc,YAAA,EAAY,EAC/B;;;;IAJCd,EAAA,CAAA0B,SAAA,EAAkB;IAAlB1B,EAAA,CAAAmC,UAAA,UAAAqB,QAAA,CAAAG,GAAA,CAAkB;IAAC3D,EAAA,CAAA0B,SAAA,EAAwB;IAAxB1B,EAAA,CAAAiD,kBAAA,MAAAO,QAAA,CAAAI,cAAA,KAAwB;;;;;;IAMtD5D,EADF,CAAAC,cAAA,gBAA4E,eACvD;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACjDd,EAAA,CAAAC,cAAA,mBACe;IAD2CD,EAAA,CAAAE,UAAA,mBAAA2D,gFAAArB,MAAA;MAAA,MAAAsB,QAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA,EAAA7C,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoD,OAAA,CAAAlB,MAAA,EAAAsB,QAAA,CAAqB;IAAA,EAAC;IAE3F9D,EAFE,CAAAc,YAAA,EACe,EACT;;;;IAHad,EAAA,CAAA0B,SAAA,GAAuB;IAAvB1B,EAAA,CAAA2B,iBAAA,CAAAmC,QAAA,CAAAF,cAAA,CAAuB;IACnB5D,EAAA,CAAA0B,SAAA,EAAe;IAAC1B,EAAhB,CAAAmC,UAAA,OAAA2B,QAAA,CAAAH,GAAA,CAAe,UAAAG,QAAA,CAAAH,GAAA,CAAmB;;;;;;IAjEvE3D,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAuB,UAAA,IAAAyC,sDAAA,mBAA2B,IAAAC,sDAAA,mBACC;IAC9BjE,EAAA,CAAAc,YAAA,EAAiB;IAMPd,EALV,CAAAC,cAAA,uBAAwC,cAChB,cACU,aACX,yBAC4D,gBAEvB;IAAhDD,EAAA,CAAAsC,gBAAA,2BAAA4B,+EAAA1B,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAb,QAAA,EAAAU,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAb,QAAA,GAAAU,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA2B;IAC/BxC,EAFE,CAAAc,YAAA,EACkD,EACnC;IAEfd,EADF,CAAAC,cAAA,0BAA2E,iBAE9C;IAAzBD,EAAA,CAAAsC,gBAAA,2BAAA8B,gFAAA5B,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAA0B,KAAA,EAAA7B,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAA0B,KAAA,GAAA7B,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IAC5BxC,EAFE,CAAAc,YAAA,EAC2B,EACZ;IAEfd,EADF,CAAAC,cAAA,0BAAwE,iBAE3C;IAAzBD,EAAA,CAAAsC,gBAAA,2BAAAgC,gFAAA9B,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAZ,KAAA,EAAAS,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAZ,KAAA,GAAAS,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAwB;IAC5BxC,EAFE,CAAAc,YAAA,EAC2B,EACZ;IACjBd,EAAA,CAAAuB,UAAA,KAAAgD,iEAAA,6BAA+F;IAK7FvE,EADF,CAAAC,cAAA,0BAA4E,iBAEpB;IAApDD,EAAA,CAAAsC,gBAAA,2BAAAkC,gFAAAhC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAA8B,QAAA,EAAAjC,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAA8B,QAAA,GAAAjC,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA2B;IAC/BxC,EAFE,CAAAc,YAAA,EACsD,EACvC;IAEfd,EADF,CAAAC,cAAA,0BAAiF,iBAEpB;IAAzDD,EAAA,CAAAsC,gBAAA,2BAAAoC,gFAAAlC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAgC,WAAA,EAAAnC,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAgC,WAAA,GAAAnC,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA8B;IAClCxC,EAFE,CAAAc,YAAA,EAC2D,EAC5C;IAEfd,EADF,CAAAC,cAAA,0BAA+E,qBAEzC;IAAlCD,EAAA,CAAAsC,gBAAA,4BAAAsC,qFAAApC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAkC,aAAA,EAAArC,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAkC,aAAA,GAAArC,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAAiC;IACjCxC,EAAA,CAAAuB,UAAA,KAAAuD,4DAAA,wBAAsE;IAE1E9E,EADE,CAAAc,YAAA,EAAY,EACG;IAEfd,EADF,CAAAC,cAAA,0BAA0E,qBACiC;IAA5BD,EAAA,CAAAsC,gBAAA,4BAAAyC,qFAAAvC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAT,OAAA,EAAAM,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAT,OAAA,GAAAM,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA2B;IACtGxC,EAAA,CAAAuB,UAAA,KAAAyD,4DAAA,wBAA6E;IAGjFhF,EADE,CAAAc,YAAA,EAAY,EACG;IAEfd,EADF,CAAAC,cAAA,0BAA2E,qBAED;IAAtED,EAAA,CAAAE,UAAA,4BAAA+E,qFAAAzC,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAkBJ,MAAA,CAAA4E,aAAA,CAAA1C,MAAA,CAAqB;IAAA,EAAC;IAACxC,EAAA,CAAAsC,gBAAA,2BAAA6C,oFAAA3C,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA0C,kBAAA,CAAApC,MAAA,CAAAqC,IAAA,CAAAyC,SAAA,EAAA5C,MAAA,MAAAlC,MAAA,CAAAqC,IAAA,CAAAyC,SAAA,GAAA5C,MAAA;MAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;IAAA,EAA4B;IACrExC,EAAA,CAAAuB,UAAA,KAAA8D,4DAAA,wBAA8D;IAElErF,EADE,CAAAc,YAAA,EAAY,EACG;IAKXd,EAJN,CAAAC,cAAA,0BACkE,eACF,iBACT,gBAC9B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC5Bd,EAAA,CAAAC,cAAA,oBAAkF;IAAlDD,EAAA,CAAAE,UAAA,mBAAAoF,wEAAA9C,MAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiF,eAAA,CAAA/C,MAAA,CAAuB;IAAA,EAAC;IACnExC,EADE,CAAAc,YAAA,EAAkF,EAC5E;IAORd,EANA,CAAAuB,UAAA,KAAAiE,4DAAA,wBAAgG,KAAAC,wDAAA,oBAMpB;IAUxFzF,EALU,CAAAc,YAAA,EAAM,EACS,EACb,EACF,EACF,EACO;IAITd,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAAE,UAAA,mBAAAwF,yEAAA;MAAA,MAAAC,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAA+D,GAAA,EAAAyB,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuF,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAC3F,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACpEd,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAAE,UAAA,mBAAA4F,yEAAA;MAAA,MAAAH,OAAA,GAAA3F,EAAA,CAAAI,aAAA,CAAA+D,GAAA,EAAAyB,SAAA;MAAA,OAAA5F,EAAA,CAAAU,WAAA,CAASiF,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAAC/F,EAAA,CAAAa,MAAA,oBAAE;IAIpEb,EAJoE,CAAAc,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IAjFCd,EAAA,CAAA0B,SAAA,GAAkB;IAAlB1B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAuC,KAAA,UAAkB;IAClB7C,EAAA,CAAA0B,SAAA,EAAmB;IAAnB1B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAuC,KAAA,WAAmB;IAMJ7C,EAAA,CAAA0B,SAAA,GAAc;IAAyB1B,EAAvC,CAAAmC,UAAA,yBAAc,wBAAwB,oBAAoB;IAEtEnC,EAAA,CAAA0B,SAAA,EAA2B;IAA3B1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAAb,QAAA,CAA2B;IAAC9B,EAAA,CAAAmC,UAAA,cAAA7B,MAAA,CAAAuC,KAAA,CAAmB;IAEnC7C,EAAA,CAAA0B,SAAA,EAAgB;IAAuB1B,EAAvC,CAAAmC,UAAA,qCAAgB,sBAAsB,oBAAoB;IAEtEnC,EAAA,CAAA0B,SAAA,EAAwB;IAAxB1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAA0B,KAAA,CAAwB;IAEZrE,EAAA,CAAA0B,SAAA,EAAc;IAAsB1B,EAApC,CAAAmC,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEnC,EAAA,CAAA0B,SAAA,EAAwB;IAAxB1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAAZ,KAAA,CAAwB;IAEgC/B,EAAA,CAAA0B,SAAA,EAAY;IAAZ1B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAuC,KAAA,CAAY;IAIxD7C,EAAA,CAAA0B,SAAA,EAAc;IAAyB1B,EAAvC,CAAAmC,UAAA,yBAAc,wBAAwB,eAAA7B,MAAA,CAAAuC,KAAA,CAAqB;IAEvE7C,EAAA,CAAA0B,SAAA,EAA2B;IAA3B1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAA8B,QAAA,CAA2B;IAACzE,EAAA,CAAAmC,UAAA,iDAAuB;IAEvCnC,EAAA,CAAA0B,SAAA,EAAgB;IAA4B1B,EAA5C,CAAAmC,UAAA,qCAAgB,2BAA2B,eAAA7B,MAAA,CAAAuC,KAAA,CAAqB;IAE5E7C,EAAA,CAAA0B,SAAA,EAA8B;IAA9B1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAAgC,WAAA,CAA8B;IAAC3E,EAAA,CAAAmC,UAAA,6DAAyB;IAE5CnC,EAAA,CAAA0B,SAAA,EAAc;IAA6B1B,EAA3C,CAAAmC,UAAA,yBAAc,4BAA4B,oBAAoB;IAE1EnC,EAAA,CAAA0B,SAAA,EAAiC;IAAjC1B,EAAA,CAAA8C,gBAAA,aAAAxC,MAAA,CAAAqC,IAAA,CAAAkC,aAAA,CAAiC;IACE7E,EAAA,CAAA0B,SAAA,EAAa;IAAb1B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA0F,UAAA,CAAa;IAGpChG,EAAA,CAAA0B,SAAA,EAAc;IAAwB1B,EAAtC,CAAAmC,UAAA,yBAAc,uBAAuB,oBAAoB;IACMnC,EAAA,CAAA0B,SAAA,EAA2B;IAA3B1B,EAAA,CAAA8C,gBAAA,aAAAxC,MAAA,CAAAqC,IAAA,CAAAT,OAAA,CAA2B;IAClElC,EAAA,CAAA0B,SAAA,EAAgB;IAAhB1B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA2F,aAAA,CAAgB;IAIxCjG,EAAA,CAAA0B,SAAA,EAAc;IAAyB1B,EAAvC,CAAAmC,UAAA,yBAAc,wBAAwB,oBAAoB;IAE7BnC,EAAA,CAAA0B,SAAA,EAA4B;IAA5B1B,EAAA,CAAA8C,gBAAA,YAAAxC,MAAA,CAAAqC,IAAA,CAAAyC,SAAA,CAA4B;IACzCpF,EAAA,CAAA0B,SAAA,EAAW;IAAX1B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA4F,QAAA,CAAW;IAG3BlG,EAAA,CAAA0B,SAAA,EAAc;IAC5B1B,EADc,CAAAmC,UAAA,yBAAc,yBAAyB,oBAAoB,WAAA7B,MAAA,CAAAqC,IAAA,CAAAyC,SAAA,UAAA9E,MAAA,CAAAqC,IAAA,CAAAyC,SAAA,KAAAe,SAAA,CACV;IAMxBnG,EAAA,CAAA0B,SAAA,GAAgB;IAAhB1B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA8F,aAAA,CAAgB;IAMKpG,EAAA,CAAA0B,SAAA,EAAgB;IAAhB1B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAA8F,aAAA,CAAgB;;;ADnF1F,OAAM,MAAOC,uBAAwB,SAAQrH,aAAa;EAyBxDsH,YACUC,aAA8B,EAC9BC,KAAuB,EACvBC,WAAwB,EACxBC,gBAAkC,EAClCC,OAAuB,EACZC,KAAkB,EAC7BC,KAAuB,EACvBC,MAAc,EACdC,OAAsB,EACtBC,gBAAkC,EAClCC,UAAsB;IAE9B,KAAK,CAACL,KAAK,CAAC;IAZJ,KAAAL,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,OAAO,GAAPA,OAAO;IACI,KAAAC,KAAK,GAALA,KAAK;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,UAAU,GAAVA,UAAU;IAnCX,KAAApF,SAAS,GAAG,CAAC;IACtB,KAAAqF,OAAO,GAAG,IAAIxH,YAAY,EAAE;IAC5B,KAAAyH,QAAQ,GAAG,EAA2B;IACtC,KAAAC,eAAe,GAAG,EAA2B;IAE7C,KAAApB,UAAU,GAAG,EAAgC;IAE7C,KAAAnD,KAAK,GAAG,KAAK;IAEb,KAAAoD,aAAa,GAAG,CACd;MAAE9C,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IAED,KAAA8C,QAAQ,GAAG,CACT;MAAE/C,KAAK,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAO,CAAE,EAC3B;MAAEH,KAAK,EAAE,CAAC;MAAEG,IAAI,EAAE;IAAO,CAAE,CAC5B;IACD,KAAA+D,qBAAqB,GAAiB,EAAE;IAExC,KAAAjB,aAAa,GAAgD,EAAE;IAmB7D,IAAI,CAACI,KAAK,CAACc,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;MACpC,IAAI,CAACL,QAAQ,GAAGK,GAAG;IACrB,CAAC,CAAC;IACF,IAAI,CAAChB,KAAK,CAACiB,eAAe,CAACF,SAAS,CAACC,GAAG,IAAG;MACzC,IAAI,CAACxB,UAAU,GAAGwB,GAAG;IACvB,CAAC,CAAC;EAEJ;EAESE,QAAQA,CAAA;IACf,IAAI,CAACC,OAAO,EAAE;IACd,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;EACAD,YAAYA,CAAA;IACV,IAAI,CAAClB,gBAAgB,CAACoB,4BAA4B,CAAC;MACjDC,IAAI,EAAE;QACJhG,KAAK,EAAE,EAAE;QACTG,OAAO,EAAE,CAAC;QACV8F,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE;;KAEb,CAAC,CACDC,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CACzCM,SAAS,CAACC,GAAG,IAAG;MACf,IAAI,CAACxB,UAAU,GAAGwB,GAAG,CAACW,OAAQ;MAC9B,IAAI,CAAC3B,KAAK,CAAC4B,YAAY,CAAC,IAAI,CAACpC,UAAU,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEA;EACA2B,OAAOA,CAAA;IACL,IAAI,CAAClB,WAAW,CAAC4B,uBAAuB,CAAC;MACvCN,IAAI,EAAE;QACJ7F,OAAO,EAAE,IAAI,CAACgF,OAAO,CAAChF,OAAO;QAC7BoG,SAAS,EAAE,IAAI,CAACpB,OAAO,CAACoB;;KAE3B,CAAC,CACDJ,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CACzCM,SAAS,CAACC,GAAG,IAAG;MACf,IAAI,CAACL,QAAQ,GAAGK,GAAG,CAACW,OAAQ;MAC5B,IAAI,CAACI,YAAY,GAAGf,GAAG,CAACgB,UAAW;MACnC,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACjC,KAAK,CAACkC,OAAO,CAAC,IAAI,CAACvB,QAAQ,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAsB,gBAAgBA,CAAA;IACd,IAAI,CAAC5G,SAAS,GAAG,CAAC,IAAI,CAAC8G,SAAS,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ,GAAG,CAAC;IACzD,IAAIC,SAAS,GAAI,IAAI,CAACN,YAAY,GAAG,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,QAAQ,GAAI,IAAI,CAACL,YAAY,GAAG,CAAC,GAAI,IAAI,CAACI,SAAS,GAAG,IAAI,CAACC,QAAS;IAC/H,IAAI,CAACxB,eAAe,GAAG,IAAI,CAACD,QAAQ,CAAC2B,KAAK,CAAC,IAAI,CAACjH,SAAS,GAAG,CAAC,EAAEgH,SAAS,CAAC;EAC3E;EAGA;EACAE,OAAOA,CAAA;IACL,OAAOjJ,aAAa,CAAC,IAAI,CAAC2G,WAAW,CAACuC,uBAAuB,CAAC;MAC5DjB,IAAI,EAAE;QACJkB,OAAO,EAAE,IAAI,CAAC/B,OAAO,CAAC+B,OAAQ,CAACC,QAAQ;;KAE1C,CAAC,CAAC,CAACC,IAAI,CAAC3B,GAAG,IAAG;MACb,IAAI,CAAC7E,IAAI,GAAG6E,GAAG,CAACW,OAAQ;IAC1B,CAAC,CAAC;EACJ;EAEAN,gBAAgBA,CAAA;IACd,IAAI,CAACb,gBAAgB,CAACoC,qCAAqC,EAAE,CAC5DlB,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CAACM,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAACpB,aAAa,GAAGoB,GAAG,CAACW,OAAO;IAClC,CAAC,CAAC;EACJ;EAEAjD,aAAaA,CAACmE,CAAK;IACjB,IAAI,CAAC1G,IAAI,CAACyC,SAAS,GAAGiE,CAAC;EACzB;EAEA3F,OAAOA,CAAC2F,CAAK,EAAEC,IAA4B;IACzC,IAAIC,UAAU,GAAG,IAAI,CAAClC,qBAAqB,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,IAAI,CAAC3F,GAAG,CAAC;IACjF,IAAG,CAAC4F,UAAU,EAAC;MACb,IAAI,CAAClC,qBAAqB,CAACsC,IAAI,CAAC;QAC9BD,WAAW,EAAEJ,IAAI,CAAC3F,GAAG;QACrBiG,aAAa,EAAEN,IAAI,CAAC1F;OACrB,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACyD,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACwC,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKJ,IAAI,CAAC3F,GAAG,CAAC;IACjG;EACF;EAEA4B,eAAeA,CAACuE,OAAW;IACzB;IACA,IAAI,CAACC,WAAW,CAACC,OAAO,CAACP,CAAC,IAAG;MAC3BA,CAAC,CAACQ,aAAa,CAACH,OAAO,GAAGA,OAAO,CAACI,MAAM,CAACJ,OAAO,GAAG,IAAI,GAAG,KAAK;IACjE,CAAC,CAAC;IAEF;IACA,IAAGA,OAAO,CAACI,MAAM,CAACJ,OAAO,EAAC;MACxB,IAAI,CAACzC,qBAAqB,GAAG,IAAI,CAACjB,aAAc,CAAC+D,GAAG,CAACV,CAAC,IAAG;QACvD,OAAO;UACLC,WAAW,EAAED,CAAC,CAAC9F,GAAG;UAClBiG,aAAa,EAAEH,CAAC,CAAC7F;SAClB;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACyD,qBAAqB,GAAG,EAAE;IACjC;EACF;EAEA1G,GAAGA,CAACyJ,MAAwB;IAC1B,IAAI,CAACzH,IAAI,GAAG;MACVb,QAAQ,EAAE,EAAE;MACZ6C,WAAW,EAAE,EAAE;MACf5C,KAAK,EAAE,EAAE;MACTG,OAAO,EAAEiE,SAAS;MAClB8C,OAAO,EAAE,EAAE;MACXpE,aAAa,EAAE,EAAE;MACjBjC,WAAW,EAAE,EAAE;MACf6B,QAAQ,EAAE;KACX;IACD,IAAI,CAAC5B,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC0D,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;EACjC;EAEAE,gBAAgBA,CAAA;IACd,QAAO,IAAI,CAAC3H,IAAI,CAACyC,SAAS;MACxB,KAAK,CAAC;QACJ,IAAI,CAACzC,IAAI,CAAC4H,cAAc,GAAG,EAAE;QAC7B;MACF,KAAK,CAAC;QACJ,IAAI,CAAC5H,IAAI,CAAC4H,cAAc,GAAG,IAAI,CAAClD,qBAAqB,CAAC8C,GAAG,CAACV,CAAC,IAAG;UAC5D,OAAO;YACLC,WAAW,EAAED,CAAC,CAACC,WAAW;YAC1BE,aAAa,EAAEH,CAAC,CAACG;WAClB;QACH,CAAC,CAAC;QACF;MACF;IACF;EACF;EAEA/D,IAAIA,CAAC2E,GAAQ;IAEX,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5D,KAAK,CAAC6D,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChE,OAAO,CAACiE,aAAa,CAAC,IAAI,CAAC/D,KAAK,CAAC6D,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACJ,gBAAgB,EAAE;IAEvB,IAAI,IAAI,CAACzH,KAAK,EAAE;MACd,IAAI,CAAC4D,WAAW,CAACoE,uBAAuB,CAAC;QACvC9C,IAAI,EAAE,IAAI,CAACpF;OACZ,CAAC,CACDuF,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CACzCM,SAAS,CAACC,GAAG,IAAG;QACf,IAAIA,GAAG,CAACsD,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnE,OAAO,CAACoE,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACpD,OAAO,EAAE;UACd6C,GAAG,CAACzE,KAAK,EAAE;QACb,CAAC,MAAM;UACL,IAAI,CAACY,OAAO,CAACqE,YAAY,CAAC,GAAGxD,GAAG,CAACyD,OAAO,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACxE,WAAW,CAACyE,wBAAwB,CAAC;QACxCnD,IAAI,EAAE,IAAI,CAACpF;OACZ,CAAC,CAACuF,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CAACM,SAAS,CAACC,GAAG,IAAG;QAC3D,IAAIA,GAAG,CAACsD,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACnE,OAAO,CAACoE,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACpD,OAAO,EAAE;UACd6C,GAAG,CAACzE,KAAK,EAAE;QACb,CAAC,MAAK;UACJ,IAAI,CAACY,OAAO,CAACqE,YAAY,CAAC,GAAGxD,GAAG,CAACyD,OAAO,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;EAEF;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC1E,WAAW,CAAC2E,0BAA0B,CAAC;MAC1CrD,IAAI,EAAE;QACJkB,OAAO,EAAE,IAAI,CAAC/B,OAAO,CAAC+B,OAAO,CAACC,QAAQ;;KAEzC,CAAC,CAAChB,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CAACM,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAGA,GAAG,CAACsD,UAAU,KAAK,CAAC,EAAE;QACvB,IAAI,CAACnE,OAAO,CAACoE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACpD,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACJ;EAEArG,QAAQA,CAAC+J,IAAyB;IAChC,IAAIC,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAAC9E,WAAW,CAAC2E,0BAA0B,CAAC;QAC1CrD,IAAI,EAAE;UACJkB,OAAO,EAAEoC,IAAI,CAACpC;;OAEjB,CAAC,CAACf,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CAACM,SAAS,CAACC,GAAG,IAAG;QAC3D,IAAGA,GAAG,CAACsD,UAAU,KAAK,CAAC,EAAE;UACvB,IAAI,CAACnE,OAAO,CAACoE,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACpD,OAAO,EAAE;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;IACF;EACF;EAEAxG,MAAMA,CAACkK,IAAyB,EAAEjB,MAAwB;IACxD,IAAI,CAACvH,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC4D,WAAW,CAACuC,uBAAuB,CAAC;MACvCjB,IAAI,EAAC;QACHkB,OAAO,EAAEoC,IAAI,CAACpC;;KAEjB,CAAC,CAACf,IAAI,CAACnI,kBAAkB,CAAC,IAAI,CAACkH,UAAU,CAAC,CAAC,CAACM,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAAC7E,IAAI,GAAG6E,GAAG,CAACW,OAAQ;MACxB,IAAI,CAACd,qBAAqB,GAAG,CAAC,IAAI,IAAI,CAAC1E,IAAI,CAAC4H,cAAc,IAAI,EAAE,CAAC,CAAC;MAElEiB,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,cAAc,CAACxB,aAAa,CAACH,OAAO,GAAG,IAAI;QAChD,IAAI,CAACC,WAAW,CAACC,OAAO,CAACP,CAAC,IAAG;UAC3B,IAAIiC,YAAY,GAAGlE,GAAG,CAACW,OAAO,EAAEoC,cAAc,EAAEf,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACI,WAAW,EAAER,QAAQ,EAAE,KAAKO,CAAC,CAACQ,aAAa,CAAC0B,EAAE,CAAC;UACjH,IAAGD,YAAY,EAAC;YACdjC,CAAC,CAACQ,aAAa,CAACH,OAAO,GAAG,IAAI;UAChC,CAAC,MAAM;YACL,IAAI,CAAC2B,cAAc,CAACxB,aAAa,CAACH,OAAO,GAAG,KAAK;UACnD;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,CAAC,CAAC;MAEL,IAAI,CAACvD,aAAa,CAAC8D,IAAI,CAACD,MAAM,CAAC;IACjC,CAAC,CAAC;EACJ;EAEAK,UAAUA,CAAA;IACR,IAAI,CAAC5D,KAAK,CAAC+E,KAAK,EAAE;IAClB,IAAI,CAAC/E,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClJ,IAAI,CAACb,QAAQ,CAAC;IAC/C,IAAI,CAAC+E,KAAK,CAACiF,OAAO,CAAC,MAAM,EAAE,IAAI,CAACnJ,IAAI,CAACb,QAAQ,EAAE,IAAI,CAACiF,OAAO,CAACgF,cAAc,EAAE,wBAAwB,CAAC;IACrG,IAAI,CAAClF,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClJ,IAAI,CAAC0B,KAAK,CAAC;IAC9C,IAAI,CAACwC,KAAK,CAACiF,OAAO,CAAC,QAAQ,EAAC,IAAI,CAACnJ,IAAI,CAAC0B,KAAK,EAAE,IAAI,CAAC0C,OAAO,CAACiF,WAAW,CAAC;IACtE,IAAI,CAACnF,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClJ,IAAI,CAACZ,KAAK,CAAC;IAC5C,IAAI,IAAI,CAACc,KAAK,EAAE;MACd,IAAI,CAACgE,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClJ,IAAI,CAAC8B,QAAQ,CAAC;MAC/C,IAAI,CAACoC,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAClJ,IAAI,CAACgC,WAAW,CAAC;MACpD,IAAI,CAACkC,KAAK,CAACiF,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACnJ,IAAI,CAACgC,WAAW,EAAE,IAAI,CAACoC,OAAO,CAACkF,eAAe,CAAC;MACjF,IAAI,CAACpF,KAAK,CAACiF,OAAO,CAAC,MAAM,EAAE,IAAI,CAACnJ,IAAI,CAAC8B,QAAQ,EAAE,IAAI,CAACsC,OAAO,CAACkF,eAAe,CAAC;IAC9E;IACA,IAAI,CAACpF,KAAK,CAACqF,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAACvJ,IAAI,CAAC8B,QAAQ,EAAE,IAAI,CAAC9B,IAAI,CAACgC,WAAW,CAAC;IAC7E,IAAG,IAAI,CAAChC,IAAI,CAACkC,aAAc,CAAC8F,MAAM,GAAG,CAAC,EAAC;MACrC,IAAI,CAAC9D,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC;IACnC;IACA,IAAI,CAAChF,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClJ,IAAI,CAACT,OAAO,CAAC;IAC9C,IAAI,CAAC2E,KAAK,CAACgF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAClJ,IAAI,CAACyC,SAAS,CAAC;IAEhD,IAAG,IAAI,CAACzC,IAAI,CAACyC,SAAS,KAAK,CAAC,IAAI,IAAI,CAACiC,qBAAqB,CAACsD,MAAM,GAAG,CAAC,EAAC;MACpE,IAAI,CAAC9D,KAAK,CAACsF,eAAe,CAAC,SAAS,CAAE;IACxC;EACF;;;uCA5SW9F,uBAAuB,EAAArG,EAAA,CAAAoM,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAtM,EAAA,CAAAoM,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAxM,EAAA,CAAAoM,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1M,EAAA,CAAAoM,iBAAA,CAAAK,EAAA,CAAAE,gBAAA,GAAA3M,EAAA,CAAAoM,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA7M,EAAA,CAAAoM,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA/M,EAAA,CAAAoM,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAjN,EAAA,CAAAoM,iBAAA,CAAAc,EAAA,CAAAC,MAAA,GAAAnN,EAAA,CAAAoM,iBAAA,CAAAgB,EAAA,CAAAC,aAAA,GAAArN,EAAA,CAAAoM,iBAAA,CAAAK,EAAA,CAAAa,gBAAA,GAAAtN,EAAA,CAAAoM,iBAAA,CAAApM,EAAA,CAAAuN,UAAA;IAAA;EAAA;;;YAAvBlH,uBAAuB;MAAAmH,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;UChDlC3N,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAKTd,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyB,eACE;UAAAD,EAAA,CAAAa,MAAA,yBAAG;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACnDd,EAAA,CAAAC,cAAA,eAAyG;UAAhCD,EAAA,CAAAsC,gBAAA,2BAAAuL,gEAAArL,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAA0N,GAAA;YAAA9N,EAAA,CAAA0C,kBAAA,CAAAkL,GAAA,CAAA1G,OAAA,CAAAoB,SAAA,EAAA9F,MAAA,MAAAoL,GAAA,CAAA1G,OAAA,CAAAoB,SAAA,GAAA9F,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAA+B;UAC1GxC,EADE,CAAAc,YAAA,EAAyG,EACrG;UAEJd,EADF,CAAAC,cAAA,eAAwC,iBACC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UACjDd,EAAA,CAAAC,cAAA,qBAA0C;UAA/BD,EAAA,CAAAsC,gBAAA,4BAAAyL,sEAAAvL,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAA0N,GAAA;YAAA9N,EAAA,CAAA0C,kBAAA,CAAAkL,GAAA,CAAA1G,OAAA,CAAAhF,OAAA,EAAAM,MAAA,MAAAoL,GAAA,CAAA1G,OAAA,CAAAhF,OAAA,GAAAM,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAA8B;UACvCxC,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACtCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAE7Bb,EAF6B,CAAAc,YAAA,EAAY,EAC3B,EACR;UAEJd,EADF,CAAAC,cAAA,eAAmD,kBACK;UAApBD,EAAA,CAAAE,UAAA,mBAAA8N,0DAAA;YAAAhO,EAAA,CAAAI,aAAA,CAAA0N,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CAASkN,GAAA,CAAAjG,OAAA,EAAS;UAAA,EAAC;UAAC3H,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACnGd,EAAA,CAAAuB,UAAA,KAAA0M,0CAAA,qBAA4E;UAKpFjO,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACO;UAQHd,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,sCAAK;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,sCAAK;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,sCAAK;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuB,UAAA,KAAA2M,sCAAA,kBAAuE;UAe7ElO,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;UACNd,EAAA,CAAAC,cAAA,0BACoC;UADYD,EAAA,CAAAsC,gBAAA,wBAAA6L,uEAAA3L,MAAA;YAAAxC,EAAA,CAAAI,aAAA,CAAA0N,GAAA;YAAA9N,EAAA,CAAA0C,kBAAA,CAAAkL,GAAA,CAAAjF,SAAA,EAAAnG,MAAA,MAAAoL,GAAA,CAAAjF,SAAA,GAAAnG,MAAA;YAAA,OAAAxC,EAAA,CAAAU,WAAA,CAAA8B,MAAA;UAAA,EAAoB;UAClExC,EAAA,CAAAE,UAAA,wBAAAiO,uEAAA;YAAAnO,EAAA,CAAAI,aAAA,CAAA0N,GAAA;YAAA,OAAA9N,EAAA,CAAAU,WAAA,CAAckN,GAAA,CAAAnF,gBAAA,EAAkB;UAAA,EAAC;UAIzCzI,EAHM,CAAAc,YAAA,EAAiB,EACb,EACO,EACP;UAGVd,EAAA,CAAAuB,UAAA,KAAA6M,+CAAA,kCAAApO,EAAA,CAAAqO,sBAAA,CAAkD;;;UA1DiCrO,EAAA,CAAA0B,SAAA,GAA+B;UAA/B1B,EAAA,CAAA8C,gBAAA,YAAA8K,GAAA,CAAA1G,OAAA,CAAAoB,SAAA,CAA+B;UAI7FtI,EAAA,CAAA0B,SAAA,GAA8B;UAA9B1B,EAAA,CAAA8C,gBAAA,aAAA8K,GAAA,CAAA1G,OAAA,CAAAhF,OAAA,CAA8B;UAC5BlC,EAAA,CAAA0B,SAAA,EAAY;UAAZ1B,EAAA,CAAAmC,UAAA,aAAY;UACZnC,EAAA,CAAA0B,SAAA,GAAW;UAAX1B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA0B,SAAA,GAAW;UAAX1B,EAAA,CAAAmC,UAAA,YAAW;UAKoCnC,EAAA,CAAA0B,SAAA,GAAc;UAAd1B,EAAA,CAAAmC,UAAA,SAAAyL,GAAA,CAAAU,QAAA,CAAc;UAsBnDtO,EAAA,CAAA0B,SAAA,IAAoB;UAApB1B,EAAA,CAAAmC,UAAA,YAAAyL,GAAA,CAAAxG,eAAA,CAAoB;UAgB/BpH,EAAA,CAAA0B,SAAA,EAA+B;UAA/B1B,EAAA,CAAAmC,UAAA,mBAAAyL,GAAA,CAAArF,YAAA,CAA+B;UAACvI,EAAA,CAAA8C,gBAAA,SAAA8K,GAAA,CAAAjF,SAAA,CAAoB;UAAC3I,EAAA,CAAAmC,UAAA,aAAAyL,GAAA,CAAAhF,QAAA,CAAqB;;;qBDzB5F3J,YAAY,EAAAoN,EAAA,CAAAkC,eAAA,EAAAlC,EAAA,CAAAmC,mBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EAAApC,EAAA,CAAAqC,qBAAA,EACZ/O,mBAAmB,EACnBT,aAAa,EAAAmN,EAAA,CAAAsC,gBAAA,EACbrP,WAAW,EAAAsP,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EACX5P,cAAc,EAAAkN,EAAA,CAAA2C,iBAAA,EAAA3C,EAAA,CAAA4C,iBAAA,EACd7P,cAAc,EACdI,IAAI,EACJC,KAAK,EACLG,mBAAmB,EACnBL,UAAU,EAEVF,gBAAgB,EAChBQ,kBAAkB;MAAAqP,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}