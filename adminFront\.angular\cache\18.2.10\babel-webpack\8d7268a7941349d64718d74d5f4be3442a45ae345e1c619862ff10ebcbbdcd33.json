{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiUserGroupAddDataPost$Json } from '../fn/user-group/api-user-group-add-data-post-json';\nimport { apiUserGroupAddDataPost$Plain } from '../fn/user-group/api-user-group-add-data-post-plain';\nimport { apiUserGroupGetDataPost$Json } from '../fn/user-group/api-user-group-get-data-post-json';\nimport { apiUserGroupGetDataPost$Plain } from '../fn/user-group/api-user-group-get-data-post-plain';\nimport { apiUserGroupGetListPost$Json } from '../fn/user-group/api-user-group-get-list-post-json';\nimport { apiUserGroupGetListPost$Plain } from '../fn/user-group/api-user-group-get-list-post-plain';\nimport { apiUserGroupRemoveDataPost$Json } from '../fn/user-group/api-user-group-remove-data-post-json';\nimport { apiUserGroupRemoveDataPost$Plain } from '../fn/user-group/api-user-group-remove-data-post-plain';\nimport { apiUserGroupSaveDataPost$Json } from '../fn/user-group/api-user-group-save-data-post-json';\nimport { apiUserGroupSaveDataPost$Plain } from '../fn/user-group/api-user-group-save-data-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class UserGroupService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiUserGroupGetListPost()` */\n  static {\n    this.ApiUserGroupGetListPostPath = '/api/UserGroup/GetList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupGetListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetListPost$Plain$Response(params, context) {\n    return apiUserGroupGetListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupGetListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetListPost$Plain(params, context) {\n    return this.apiUserGroupGetListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupGetListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetListPost$Json$Response(params, context) {\n    return apiUserGroupGetListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupGetListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetListPost$Json(params, context) {\n    return this.apiUserGroupGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGroupGetDataPost()` */\n  static {\n    this.ApiUserGroupGetDataPostPath = '/api/UserGroup/GetData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupGetDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetDataPost$Plain$Response(params, context) {\n    return apiUserGroupGetDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetDataPost$Plain(params, context) {\n    return this.apiUserGroupGetDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupGetDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetDataPost$Json$Response(params, context) {\n    return apiUserGroupGetDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupGetDataPost$Json(params, context) {\n    return this.apiUserGroupGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGroupAddDataPost()` */\n  static {\n    this.ApiUserGroupAddDataPostPath = '/api/UserGroup/AddData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupAddDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupAddDataPost$Plain$Response(params, context) {\n    return apiUserGroupAddDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupAddDataPost$Plain(params, context) {\n    return this.apiUserGroupAddDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupAddDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupAddDataPost$Json$Response(params, context) {\n    return apiUserGroupAddDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupAddDataPost$Json(params, context) {\n    return this.apiUserGroupAddDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGroupSaveDataPost()` */\n  static {\n    this.ApiUserGroupSaveDataPostPath = '/api/UserGroup/SaveData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupSaveDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupSaveDataPost$Plain$Response(params, context) {\n    return apiUserGroupSaveDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupSaveDataPost$Plain(params, context) {\n    return this.apiUserGroupSaveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupSaveDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupSaveDataPost$Json$Response(params, context) {\n    return apiUserGroupSaveDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupSaveDataPost$Json(params, context) {\n    return this.apiUserGroupSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiUserGroupRemoveDataPost()` */\n  static {\n    this.ApiUserGroupRemoveDataPostPath = '/api/UserGroup/RemoveData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupRemoveDataPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupRemoveDataPost$Plain$Response(params, context) {\n    return apiUserGroupRemoveDataPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupRemoveDataPost$Plain(params, context) {\n    return this.apiUserGroupRemoveDataPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiUserGroupRemoveDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupRemoveDataPost$Json$Response(params, context) {\n    return apiUserGroupRemoveDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiUserGroupRemoveDataPost$Json(params, context) {\n    return this.apiUserGroupRemoveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function UserGroupService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UserGroupService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserGroupService,\n      factory: UserGroupService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiUserGroupAddDataPost$Json", "apiUserGroupAddDataPost$Plain", "apiUserGroupGetDataPost$Json", "apiUserGroupGetDataPost$Plain", "apiUserGroupGetListPost$Json", "apiUserGroupGetListPost$Plain", "apiUserGroupRemoveDataPost$Json", "apiUserGroupRemoveDataPost$Plain", "apiUserGroupSaveDataPost$Json", "apiUserGroupSaveDataPost$Plain", "UserGroupService", "constructor", "config", "http", "ApiUserGroupGetListPostPath", "apiUserGroupGetListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiUserGroupGetListPost$Json$Response", "ApiUserGroupGetDataPostPath", "apiUserGroupGetDataPost$Plain$Response", "apiUserGroupGetDataPost$Json$Response", "ApiUserGroupAddDataPostPath", "apiUserGroupAddDataPost$Plain$Response", "apiUserGroupAddDataPost$Json$Response", "ApiUserGroupSaveDataPostPath", "apiUserGroupSaveDataPost$Plain$Response", "apiUserGroupSaveDataPost$Json$Response", "ApiUserGroupRemoveDataPostPath", "apiUserGroupRemoveDataPost$Plain$Response", "apiUserGroupRemoveDataPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\user-group.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiUserGroupAddDataPost$Json } from '../fn/user-group/api-user-group-add-data-post-json';\r\nimport { ApiUserGroupAddDataPost$Json$Params } from '../fn/user-group/api-user-group-add-data-post-json';\r\nimport { apiUserGroupAddDataPost$Plain } from '../fn/user-group/api-user-group-add-data-post-plain';\r\nimport { ApiUserGroupAddDataPost$Plain$Params } from '../fn/user-group/api-user-group-add-data-post-plain';\r\nimport { apiUserGroupGetDataPost$Json } from '../fn/user-group/api-user-group-get-data-post-json';\r\nimport { ApiUserGroupGetDataPost$Json$Params } from '../fn/user-group/api-user-group-get-data-post-json';\r\nimport { apiUserGroupGetDataPost$Plain } from '../fn/user-group/api-user-group-get-data-post-plain';\r\nimport { ApiUserGroupGetDataPost$Plain$Params } from '../fn/user-group/api-user-group-get-data-post-plain';\r\nimport { apiUserGroupGetListPost$Json } from '../fn/user-group/api-user-group-get-list-post-json';\r\nimport { ApiUserGroupGetListPost$Json$Params } from '../fn/user-group/api-user-group-get-list-post-json';\r\nimport { apiUserGroupGetListPost$Plain } from '../fn/user-group/api-user-group-get-list-post-plain';\r\nimport { ApiUserGroupGetListPost$Plain$Params } from '../fn/user-group/api-user-group-get-list-post-plain';\r\nimport { apiUserGroupRemoveDataPost$Json } from '../fn/user-group/api-user-group-remove-data-post-json';\r\nimport { ApiUserGroupRemoveDataPost$Json$Params } from '../fn/user-group/api-user-group-remove-data-post-json';\r\nimport { apiUserGroupRemoveDataPost$Plain } from '../fn/user-group/api-user-group-remove-data-post-plain';\r\nimport { ApiUserGroupRemoveDataPost$Plain$Params } from '../fn/user-group/api-user-group-remove-data-post-plain';\r\nimport { apiUserGroupSaveDataPost$Json } from '../fn/user-group/api-user-group-save-data-post-json';\r\nimport { ApiUserGroupSaveDataPost$Json$Params } from '../fn/user-group/api-user-group-save-data-post-json';\r\nimport { apiUserGroupSaveDataPost$Plain } from '../fn/user-group/api-user-group-save-data-post-plain';\r\nimport { ApiUserGroupSaveDataPost$Plain$Params } from '../fn/user-group/api-user-group-save-data-post-plain';\r\nimport { UserGroupGetDataResponseResponseBase } from '../models/user-group-get-data-response-response-base';\r\nimport { UserGroupGetListResponseListResponseBase } from '../models/user-group-get-list-response-list-response-base';\r\nimport { UserGroupRemoveDataResponseResponseBase } from '../models/user-group-remove-data-response-response-base';\r\nimport { UserGroupSaveDataResponseResponseBase } from '../models/user-group-save-data-response-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class UserGroupService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiUserGroupGetListPost()` */\r\n  static readonly ApiUserGroupGetListPostPath = '/api/UserGroup/GetList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupGetListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetListPost$Plain$Response(params?: ApiUserGroupGetListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupGetListResponseListResponseBase>> {\r\n    return apiUserGroupGetListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupGetListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetListPost$Plain(params?: ApiUserGroupGetListPost$Plain$Params, context?: HttpContext): Observable<UserGroupGetListResponseListResponseBase> {\r\n    return this.apiUserGroupGetListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupGetListResponseListResponseBase>): UserGroupGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupGetListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetListPost$Json$Response(params?: ApiUserGroupGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupGetListResponseListResponseBase>> {\r\n    return apiUserGroupGetListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupGetListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetListPost$Json(params?: ApiUserGroupGetListPost$Json$Params, context?: HttpContext): Observable<UserGroupGetListResponseListResponseBase> {\r\n    return this.apiUserGroupGetListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupGetListResponseListResponseBase>): UserGroupGetListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGroupGetDataPost()` */\r\n  static readonly ApiUserGroupGetDataPostPath = '/api/UserGroup/GetData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupGetDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetDataPost$Plain$Response(params?: ApiUserGroupGetDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupGetDataResponseResponseBase>> {\r\n    return apiUserGroupGetDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetDataPost$Plain(params?: ApiUserGroupGetDataPost$Plain$Params, context?: HttpContext): Observable<UserGroupGetDataResponseResponseBase> {\r\n    return this.apiUserGroupGetDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupGetDataResponseResponseBase>): UserGroupGetDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupGetDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetDataPost$Json$Response(params?: ApiUserGroupGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupGetDataResponseResponseBase>> {\r\n    return apiUserGroupGetDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupGetDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupGetDataPost$Json(params?: ApiUserGroupGetDataPost$Json$Params, context?: HttpContext): Observable<UserGroupGetDataResponseResponseBase> {\r\n    return this.apiUserGroupGetDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupGetDataResponseResponseBase>): UserGroupGetDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGroupAddDataPost()` */\r\n  static readonly ApiUserGroupAddDataPostPath = '/api/UserGroup/AddData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupAddDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupAddDataPost$Plain$Response(params?: ApiUserGroupAddDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupSaveDataResponseResponseBase>> {\r\n    return apiUserGroupAddDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupAddDataPost$Plain(params?: ApiUserGroupAddDataPost$Plain$Params, context?: HttpContext): Observable<UserGroupSaveDataResponseResponseBase> {\r\n    return this.apiUserGroupAddDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupSaveDataResponseResponseBase>): UserGroupSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupAddDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupAddDataPost$Json$Response(params?: ApiUserGroupAddDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupSaveDataResponseResponseBase>> {\r\n    return apiUserGroupAddDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupAddDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupAddDataPost$Json(params?: ApiUserGroupAddDataPost$Json$Params, context?: HttpContext): Observable<UserGroupSaveDataResponseResponseBase> {\r\n    return this.apiUserGroupAddDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupSaveDataResponseResponseBase>): UserGroupSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGroupSaveDataPost()` */\r\n  static readonly ApiUserGroupSaveDataPostPath = '/api/UserGroup/SaveData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupSaveDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupSaveDataPost$Plain$Response(params?: ApiUserGroupSaveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupSaveDataResponseResponseBase>> {\r\n    return apiUserGroupSaveDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupSaveDataPost$Plain(params?: ApiUserGroupSaveDataPost$Plain$Params, context?: HttpContext): Observable<UserGroupSaveDataResponseResponseBase> {\r\n    return this.apiUserGroupSaveDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupSaveDataResponseResponseBase>): UserGroupSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupSaveDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupSaveDataPost$Json$Response(params?: ApiUserGroupSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupSaveDataResponseResponseBase>> {\r\n    return apiUserGroupSaveDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupSaveDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupSaveDataPost$Json(params?: ApiUserGroupSaveDataPost$Json$Params, context?: HttpContext): Observable<UserGroupSaveDataResponseResponseBase> {\r\n    return this.apiUserGroupSaveDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupSaveDataResponseResponseBase>): UserGroupSaveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiUserGroupRemoveDataPost()` */\r\n  static readonly ApiUserGroupRemoveDataPostPath = '/api/UserGroup/RemoveData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupRemoveDataPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupRemoveDataPost$Plain$Response(params?: ApiUserGroupRemoveDataPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupRemoveDataResponseResponseBase>> {\r\n    return apiUserGroupRemoveDataPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupRemoveDataPost$Plain(params?: ApiUserGroupRemoveDataPost$Plain$Params, context?: HttpContext): Observable<UserGroupRemoveDataResponseResponseBase> {\r\n    return this.apiUserGroupRemoveDataPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupRemoveDataResponseResponseBase>): UserGroupRemoveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiUserGroupRemoveDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupRemoveDataPost$Json$Response(params?: ApiUserGroupRemoveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UserGroupRemoveDataResponseResponseBase>> {\r\n    return apiUserGroupRemoveDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiUserGroupRemoveDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiUserGroupRemoveDataPost$Json(params?: ApiUserGroupRemoveDataPost$Json$Params, context?: HttpContext): Observable<UserGroupRemoveDataResponseResponseBase> {\r\n    return this.apiUserGroupRemoveDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UserGroupRemoveDataResponseResponseBase>): UserGroupRemoveDataResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,4BAA4B,QAAQ,oDAAoD;AAEjG,SAASC,6BAA6B,QAAQ,qDAAqD;AAEnG,SAASC,4BAA4B,QAAQ,oDAAoD;AAEjG,SAASC,6BAA6B,QAAQ,qDAAqD;AAEnG,SAASC,4BAA4B,QAAQ,oDAAoD;AAEjG,SAASC,6BAA6B,QAAQ,qDAAqD;AAEnG,SAASC,+BAA+B,QAAQ,uDAAuD;AAEvG,SAASC,gCAAgC,QAAQ,wDAAwD;AAEzG,SAASC,6BAA6B,QAAQ,qDAAqD;AAEnG,SAASC,8BAA8B,QAAQ,sDAAsD;;;;AAQrG,OAAM,MAAOC,gBAAiB,SAAQX,WAAW;EAC/CY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACC,MAA6C,EAAEC,OAAqB;IACzG,OAAOZ,6BAA6B,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAZ,6BAA6BA,CAACW,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACF,sCAAsC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAA+D,IAA+CA,CAAC,CAACC,IAAI,CAAC,CAC3H;EACH;EAEA;;;;;;EAMAC,qCAAqCA,CAACN,MAA4C,EAAEC,OAAqB;IACvG,OAAOb,4BAA4B,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAb,4BAA4BA,CAACY,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACK,qCAAqC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrErB,GAAG,CAAEsB,CAA+D,IAA+CA,CAAC,CAACC,IAAI,CAAC,CAC3H;EACH;EAEA;;IACgB,KAAAE,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACR,MAA6C,EAAEC,OAAqB;IACzG,OAAOd,6BAA6B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAd,6BAA6BA,CAACa,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACO,sCAAsC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMAI,qCAAqCA,CAACT,MAA4C,EAAEC,OAAqB;IACvG,OAAOf,4BAA4B,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAf,4BAA4BA,CAACc,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACQ,qCAAqC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrErB,GAAG,CAAEsB,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;IACgB,KAAAK,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACX,MAA6C,EAAEC,OAAqB;IACzG,OAAOhB,6BAA6B,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAhB,6BAA6BA,CAACe,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACU,sCAAsC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;;;;;EAMAO,qCAAqCA,CAACZ,MAA4C,EAAEC,OAAqB;IACvG,OAAOjB,4BAA4B,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAjB,4BAA4BA,CAACgB,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACW,qCAAqC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrErB,GAAG,CAAEsB,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;IACgB,KAAAQ,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAACd,MAA8C,EAAEC,OAAqB;IAC3G,OAAOR,8BAA8B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAR,8BAA8BA,CAACO,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACa,uCAAuC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvErB,GAAG,CAAEsB,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;;;;;EAMAU,sCAAsCA,CAACf,MAA6C,EAAEC,OAAqB;IACzG,OAAOT,6BAA6B,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAT,6BAA6BA,CAACQ,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACc,sCAAsC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtErB,GAAG,CAAEsB,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;IACgB,KAAAW,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,yCAAyCA,CAACjB,MAAgD,EAAEC,OAAqB;IAC/G,OAAOV,gCAAgC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAV,gCAAgCA,CAACS,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACgB,yCAAyC,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzErB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;EAEA;;;;;;EAMAa,wCAAwCA,CAAClB,MAA+C,EAAEC,OAAqB;IAC7G,OAAOX,+BAA+B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAX,+BAA+BA,CAACU,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACiB,wCAAwC,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxErB,GAAG,CAAEsB,CAA8D,IAA8CA,CAAC,CAACC,IAAI,CAAC,CACzH;EACH;;;uCA9OWX,gBAAgB,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhB9B,gBAAgB;MAAA+B,OAAA,EAAhB/B,gBAAgB,CAAAgC,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}