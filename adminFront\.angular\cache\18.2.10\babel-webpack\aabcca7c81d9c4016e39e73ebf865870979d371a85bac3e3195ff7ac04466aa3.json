{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\nexport default function minutesToHours(minutes) {\n  requiredArgs(1, arguments);\n  var hours = minutes / minutesInHour;\n  return Math.floor(hours);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}