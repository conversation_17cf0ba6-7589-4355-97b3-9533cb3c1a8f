{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMinutes\n * @category Minute Helpers\n * @summary Set the minutes to the given date.\n *\n * @description\n * Set the minutes to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} minutes - the minutes of the new date\n * @returns {Date} the new date with the minutes set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 45 minutes to 1 September 2014 11:30:40:\n * const result = setMinutes(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:45:40\n */\nexport default function setMinutes(dirtyDate, dirtyMinutes) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var minutes = toInteger(dirtyMinutes);\n  date.setMinutes(minutes);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setMinutes", "dirtyDate", "dirtyMinutes", "arguments", "date", "minutes"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/setMinutes/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMinutes\n * @category Minute Helpers\n * @summary Set the minutes to the given date.\n *\n * @description\n * Set the minutes to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} minutes - the minutes of the new date\n * @returns {Date} the new date with the minutes set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 45 minutes to 1 September 2014 11:30:40:\n * const result = setMinutes(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:45:40\n */\nexport default function setMinutes(dirtyDate, dirtyMinutes) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var minutes = toInteger(dirtyMinutes);\n  date.setMinutes(minutes);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC1DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,OAAO,GAAGR,SAAS,CAACK,YAAY,CAAC;EACrCE,IAAI,CAACJ,UAAU,CAACK,OAAO,CAAC;EACxB,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}