{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRemoveHousehold(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 4, 2, \"span\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r5, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getBuildingSelectedHouseholds(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasBuildingSelected(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction HouseholdBindingComponent_div_7_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_button_8_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 49)(2, \"span\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", ctx_r1.selectedBuilding === building_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getBuildingCount(building_r8), \"\\u6236\");\n  }\n}\nfunction HouseholdBindingComponent_div_7_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u53D6\\u6D88\\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_16_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_7_div_16_button_5_Template, 2, 0, \"button\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"input\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_div_7_div_17_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_div_7_div_17_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"nb-icon\", 60);\n    i0.ɵɵelementStart(2, \"p\", 61);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"nb-icon\", 62);\n    i0.ɵɵelementStart(2, \"p\", 61);\n    i0.ɵɵtext(3, \"\\u6C92\\u6709\\u627E\\u5230\\u7B26\\u5408\\u7684\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_21_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(household_r13.floor);\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_21_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_div_21_button_1_Template_button_click_0_listener() {\n      const household_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(household_r13));\n    });\n    i0.ɵɵelementStart(1, \"span\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_div_7_div_21_button_1_span_3_Template, 2, 1, \"span\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.isHouseholdSelected(household_r13.code))(\"disabled\", household_r13.isDisabled || !ctx_r1.canSelectMore() && !ctx_r1.isHouseholdSelected(household_r13.code));\n    i0.ɵɵproperty(\"disabled\", household_r13.isDisabled || !ctx_r1.canSelectMore() && !ctx_r1.isHouseholdSelected(household_r13.code));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(household_r13.code);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", household_r13.floor);\n  }\n}\nfunction HouseholdBindingComponent_div_7_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_7_div_21_button_1_Template, 4, 7, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_div_7_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u6700\\u591A\\u53EF\\u9078 \", ctx_r1.maxSelections, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27);\n    i0.ɵɵelement(4, \"nb-icon\", 28);\n    i0.ɵɵelementStart(5, \"span\", 29);\n    i0.ɵɵtext(6, \"\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 30);\n    i0.ɵɵtemplate(8, HouseholdBindingComponent_div_7_button_8_Template, 6, 4, \"button\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 32)(10, \"div\", 33)(11, \"div\", 34);\n    i0.ɵɵelement(12, \"nb-icon\", 35);\n    i0.ɵɵelementStart(13, \"span\", 36);\n    i0.ɵɵtext(14, \" \\u6236\\u5225 \");\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_div_7_span_15_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, HouseholdBindingComponent_div_7_div_16_Template, 6, 4, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, HouseholdBindingComponent_div_7_div_17_Template, 2, 1, \"div\", 39);\n    i0.ɵɵelementStart(18, \"div\", 40);\n    i0.ɵɵtemplate(19, HouseholdBindingComponent_div_7_div_19_Template, 4, 0, \"div\", 41)(20, HouseholdBindingComponent_div_7_div_20_Template, 4, 0, \"div\", 41)(21, HouseholdBindingComponent_div_7_div_21_Template, 2, 1, \"div\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 43)(23, \"div\", 44);\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_div_7_span_24_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementStart(25, \"span\", 46);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_7_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(28, \" \\u78BA\\u5B9A \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.selectedBuilding);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length === 0 && ctx_r1.searchTerm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.filteredHouseholds = [];\n    this.selectedByBuilding = {};\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    // 如果沒有提供 buildingData，使用 mock 資料\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n      this.buildingData = this.generateMockData();\n    }\n    this.buildings = Object.keys(this.buildingData);\n    this.updateSelectedByBuilding();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    return {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => ({\n        code: `A${String(i + 1).padStart(3, '0')}`,\n        building: 'A棟',\n        floor: `${Math.floor(i / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      })),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => ({\n        code: `B${String(i + 1).padStart(3, '0')}`,\n        building: 'B棟',\n        floor: `${Math.floor(i / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      })),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => ({\n        code: `C${String(i + 1).padStart(3, '0')}`,\n        building: 'C棟',\n        floor: `${Math.floor(i / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      })),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => ({\n        code: `D${String(i + 1).padStart(3, '0')}`,\n        building: 'D棟',\n        floor: `${Math.floor(i / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      })),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => ({\n        code: `E${String(i + 1).padStart(3, '0')}`,\n        building: 'E棟',\n        floor: `${Math.floor(i / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }))\n    };\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    this.filteredHouseholds = households.filter(household => household.code.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n  }\n  onHouseholdToggle(household) {\n    const isSelected = this.selectedHouseholds.includes(household.code);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== household.code);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, household.code];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    const availableHouseholds = this.filteredHouseholds.filter(h => !h.isDisabled).map(h => h.code);\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].map(h => h.code);\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].map(h => h.code);\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 9,\n      vars: 9,\n      consts: [[1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [\"icon\", \"chevron-down-outline\", 1, \"chevron-icon\"], [\"class\", \"dropdown-menu\", 4, \"ngIf\"], [\"class\", \"backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [4, \"ngIf\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"dropdown-menu\"], [1, \"dropdown-content\"], [1, \"buildings-sidebar\"], [1, \"sidebar-header\"], [\"icon\", \"home-outline\", 1, \"text-primary\"], [1, \"sidebar-title\"], [1, \"buildings-list\"], [\"type\", \"button\", \"class\", \"building-item\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"households-main\"], [1, \"main-header\"], [1, \"header-left\"], [\"icon\", \"grid-outline\", 1, \"text-primary\"], [1, \"main-title\"], [\"class\", \"building-indicator\", 4, \"ngIf\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [\"class\", \"search-box\", 4, \"ngIf\"], [1, \"households-grid\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"grid-container\", 4, \"ngIf\"], [1, \"dropdown-footer\"], [1, \"footer-info\"], [\"class\", \"max-selections-text\", 4, \"ngIf\"], [1, \"current-selections-text\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"building-item\", 3, \"click\"], [1, \"building-info\"], [1, \"building-name\"], [1, \"building-count\"], [1, \"building-indicator\"], [1, \"header-actions\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"class\", \"btn btn-outline-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"search-box\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225...\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [1, \"empty-state\"], [\"icon\", \"home-outline\", 1, \"empty-icon\"], [1, \"empty-text\"], [\"icon\", \"search-outline\", 1, \"empty-icon\"], [1, \"grid-container\"], [\"type\", \"button\", \"class\", \"household-button\", 3, \"selected\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"household-button\", 3, \"click\", \"disabled\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [1, \"household-floor\"], [1, \"max-selections-text\"], [1, \"backdrop\", 3, \"click\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"nb-icon\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, HouseholdBindingComponent_div_7_Template, 29, 9, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_div_8_Template, 1, 0, \"div\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx.placeholder, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"rotated\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #1976d2;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImhvdXNlaG9sZC1iaW5kaW5nLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0FBQ0o7QUFDSTtFQUNJLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtBQUNSO0FBQ1E7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0FBQ1o7QUFDWTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFDaEI7QUFDZ0I7RUFDSSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQUNwQjtBQUtZO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0VBQ0EsV0FBQTtFQUNBLHFCQUFBO0FBSGhCO0FBS2dCO0VBQ0ksZ0JBQUE7QUFIcEI7QUFNZ0I7RUFDSSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQUpwQjtBQU9nQjtFQUNJLGFBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtBQUxwQjtBQU9vQjtFQUNJLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FBTHhCO0FBT3dCO0VBQ0ksZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtBQUw1QjtBQU80QjtFQUNJLHlCQUFBO0FBTGhDO0FBUTRCO0VBQ0ksWUFBQTtFQUNBLG1CQUFBO0FBTmhDO0FBUzRCO0VBQ0ksa0JBQUE7QUFQaEM7QUFnQkk7RUFDSSxrQkFBQTtBQWRSO0FBZ0JRO0VBQ0ksV0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsdUJBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUNBQUE7QUFkWjtBQWdCWTtFQUNJLHlCQUFBO0VBQ0EscUJBQUE7QUFkaEI7QUFpQlk7RUFDSSxhQUFBO0VBQ0EscUJBQUE7RUFDQSxnREFBQTtBQWZoQjtBQWtCWTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHlCQUFBO0FBaEJoQjtBQW1CWTtFQUNJLGNBQUE7RUFDQSxtQkFBQTtBQWpCaEI7QUFvQlk7RUFDSSxjQUFBO0VBQ0EsdUNBQUE7QUFsQmhCO0FBb0JnQjtFQUNJLHlCQUFBO0FBbEJwQjtBQXVCUTtFQUNJLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSw2Q0FBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFyQlo7QUF1Qlk7RUFDSSxhQUFBO0VBQ0EsYUFBQTtBQXJCaEI7QUF1QmdCO0VBQ0ksY0FBQTtFQUNBLCtCQUFBO0VBQ0EseUJBQUE7QUFyQnBCO0FBdUJvQjtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EseUJBQUE7QUFyQnhCO0FBdUJ3QjtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBckI1QjtBQXlCb0I7RUFDSSxpQkFBQTtFQUNBLGdCQUFBO0FBdkJ4QjtBQXlCd0I7RUFDSSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSw4Q0FBQTtBQXZCNUI7QUF5QjRCO0VBQ0kseUJBQUE7QUF2QmhDO0FBMEI0QjtFQUNJLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBeEJoQztBQTJCNEI7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBQXpCaEM7QUEyQmdDO0VBQ0ksbUJBQUE7QUF6QnBDO0FBNEJnQztFQUNJLGtCQUFBO0VBQ0EsY0FBQTtBQTFCcEM7QUFpQ2dCO0VBQ0ksY0FBQTtBQS9CcEI7QUFpQ29CO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0VBQ0EseUJBQUE7QUEvQnhCO0FBaUN3QjtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUEvQjVCO0FBaUM0QjtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBL0JoQztBQWlDZ0M7RUFDSSxjQUFBO0FBL0JwQztBQW9Dd0I7RUFDSSxhQUFBO0VBQ0EsWUFBQTtBQWxDNUI7QUFzQ29CO0VBQ0ksZUFBQTtFQUNBLGdDQUFBO0FBcEN4QjtBQXVDb0I7RUFDSSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQXJDeEI7QUF1Q3dCO0VBQ0ksYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0FBckM1QjtBQXVDNEI7RUFDSSxlQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBckNoQztBQXdDNEI7RUFDSSxtQkFBQTtFQUNBLFNBQUE7QUF0Q2hDO0FBMEN3QjtFQUNJLGFBQUE7RUFDQSxxQ0FBQTtFQUNBLFlBQUE7QUF4QzVCO0FBMEM0QjtFQUNJLGVBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0VBQ0Esc0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUNBQUE7RUFDQSxrQkFBQTtBQXhDaEM7QUEwQ2dDO0VBQ0kseUJBQUE7QUF4Q3BDO0FBMkNnQztFQUNJLHlCQUFBO0VBQ0EscUJBQUE7RUFDQSxjQUFBO0FBekNwQztBQTRDZ0M7RUFDSSx5QkFBQTtFQUNBLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7QUExQ3BDO0FBNkNnQztFQUNJLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBM0NwQztBQThDZ0M7RUFDSSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0Esb0JBQUE7QUE1Q3BDO0FBb0RZO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDZCQUFBO0VBQ0EseUJBQUE7QUFsRGhCO0FBb0RnQjtFQUNJLGFBQUE7RUFDQSxTQUFBO0FBbERwQjtBQW9Eb0I7O0VBRUksa0JBQUE7RUFDQSxjQUFBO0FBbER4QjtBQXlESTtFQUNJLGVBQUE7RUFDQSxNQUFBO0VBQ0EsT0FBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0FBdkRSOztBQTREQTtFQUlnQjtJQUNJLHNCQUFBO0lBQ0EsWUFBQTtJQUNBLGlCQUFBO0VBNURsQjtFQThEa0I7SUFDSSxXQUFBO0lBQ0EsZ0JBQUE7SUFDQSxrQkFBQTtJQUNBLGdDQUFBO0VBNUR0QjtFQThEc0I7SUFDSSxnQkFBQTtFQTVEMUI7RUFnRWtCO0lBQ0ksV0FBQTtFQTlEdEI7RUFpRTBCO0lBQ0kscUNBQUE7RUEvRDlCO0FBQ0Y7QUF5RUE7RUFFUTtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBeEVWO0VBMEVVO0lBQ0ksY0FBQTtFQXhFZDtFQTJFVTtJQUNJLGNBQUE7RUF6RWQ7RUE4RVU7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQTVFZDtFQThFYztJQUNJLHlCQUFBO0VBNUVsQjtFQStFYztJQUNJLHlCQUFBO0VBN0VsQjtFQWlGVTtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7RUEvRWQ7RUFrRmtCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtFQWhGdEI7RUFrRnNCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUFoRjFCO0VBbUZzQjtJQUNJLGNBQUE7RUFqRjFCO0VBbUYwQjtJQUNJLHlCQUFBO0VBakY5QjtFQW9GMEI7SUFDSSx5QkFBQTtJQUNBLFdBQUE7RUFsRjlCO0VBd0ZzQjtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBdEYxQjtFQXlGc0I7SUFDSSxxQkFBQTtFQXZGMUI7RUEyRjBCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUF6RjlCO0VBMkY4QjtJQUNJLHlCQUFBO0VBekZsQztFQTRGOEI7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsV0FBQTtFQTFGbEM7RUFpR2M7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQS9GbEI7QUFDRiIsImZpbGUiOiJob3VzZWhvbGQtYmluZGluZy5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi5ob3VzZWhvbGQtYmluZGluZy1jb250YWluZXIge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcblxyXG4gICAgLnNlbGVjdGVkLWhvdXNlaG9sZHMtYXJlYSB7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuICAgICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuXHJcbiAgICAgICAgLnNlbGVjdGVkLWhlYWRlciB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3RlZC1pbmZvIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgLnNlbGVjdGVkLWNvdW50IHtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5zZWxlY3RlZC1jb250ZW50IHtcclxuICAgICAgICAgICAgLmJ1aWxkaW5nLWdyb3VwIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgICAgICAgICAgICAgIGdhcDogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuYnVpbGRpbmctbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgICAgIG1pbi13aWR0aDogM3JlbTtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLXRhZ3Mge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgZmxleC13cmFwOiB3cmFwO1xyXG4gICAgICAgICAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC10YWcge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5yZW1vdmUtYnRuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2JiZGVmYjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYi1pY29uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNlbGVjdG9yLWNvbnRhaW5lciB7XHJcbiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgICAgICAuc2VsZWN0b3ItYnV0dG9uIHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgcGFkZGluZzogMC41cmVtIDAuNzVyZW07XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlLWluLW91dDtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXI6bm90KC5kaXNhYmxlZCkge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2FkYjViZDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjODBiZGZmO1xyXG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMC4ycmVtIHJnYmEoMCwgMTIzLCAyNTUsIDAuMjUpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAmLmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNjU7XHJcbiAgICAgICAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U5ZWNlZjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnNlbGVjdG9yLXRleHQge1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuY2hldnJvbi1pY29uIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG5cclxuICAgICAgICAgICAgICAgICYucm90YXRlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmRyb3Bkb3duLW1lbnUge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogY2FsYygxMDAlICsgMC4yNXJlbSk7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgICB6LWluZGV4OiAxMDUwO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwLjVyZW0gMXJlbSByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gICAgICAgICAgICBtYXgtaGVpZ2h0OiAyNHJlbTtcclxuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAgICAgICAgIC5kcm9wZG93bi1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDIwcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgIC5idWlsZGluZ3Mtc2lkZWJhciB7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDMzLjMzMyU7XHJcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci10aXRsZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLWxpc3Qge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDIwcmVtIC0gM3JlbSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctaXRlbSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZy1pbmZvIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctbmFtZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctY291bnQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuaG91c2Vob2xkcy1tYWluIHtcclxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogNjYuNjY3JTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLm1haW4taGVhZGVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U5ZWNlZjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5oZWFkZXItbGVmdCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYWluLXRpdGxlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZy1pbmRpY2F0b3Ige1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5oZWFkZXItYWN0aW9ucyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuc2VhcmNoLWJveCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuaG91c2Vob2xkcy1ncmlkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDIwcmVtIC0gNnJlbSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZW1wdHktc3RhdGUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAycmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmVtcHR5LWljb24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNhZGI1YmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmVtcHR5LXRleHQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZ3JpZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuMjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC1idXR0b24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3Zlcjpub3QoLmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLnNlbGVjdGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTk3NmQyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICYuZGlzYWJsZWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42NTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGQtY29kZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC1mbG9vciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiAwLjEyNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5kcm9wZG93bi1mb290ZXIge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgIC5mb290ZXItaW5mbyB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICBnYXA6IDFyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5tYXgtc2VsZWN0aW9ucy10ZXh0LFxyXG4gICAgICAgICAgICAgICAgICAgIC5jdXJyZW50LXNlbGVjdGlvbnMtdGV4dCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5iYWNrZHJvcCB7XHJcbiAgICAgICAgcG9zaXRpb246IGZpeGVkO1xyXG4gICAgICAgIHRvcDogMDtcclxuICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICB6LWluZGV4OiAxMDQwO1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgfVxyXG59XHJcblxyXG4vLyDpn7/mh4nlvI/oqK3oqIhcclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAuaG91c2Vob2xkLWJpbmRpbmctY29udGFpbmVyIHtcclxuICAgICAgICAuc2VsZWN0b3ItY29udGFpbmVyIHtcclxuICAgICAgICAgICAgLmRyb3Bkb3duLW1lbnUge1xyXG4gICAgICAgICAgICAgICAgLmRyb3Bkb3duLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBhdXRvO1xyXG4gICAgICAgICAgICAgICAgICAgIG1heC1oZWlnaHQ6IDIwcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLXNpZGViYXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogOHJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZ3MtbGlzdCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDhyZW0gLSAzcmVtKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtbWFpbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtZ3JpZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIOa3seiJsuS4u+mhjOaUr+aPtFxyXG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKSB7XHJcbiAgICAuaG91c2Vob2xkLWJpbmRpbmctY29udGFpbmVyIHtcclxuICAgICAgICAuc2VsZWN0ZWQtaG91c2Vob2xkcy1hcmVhIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM0M2E0MDtcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3RlZC1oZWFkZXIgLnNlbGVjdGVkLWluZm8gLnNlbGVjdGVkLWNvdW50IHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuc2VsZWN0ZWQtY29udGVudCAuYnVpbGRpbmctZ3JvdXAgLmJ1aWxkaW5nLWxhYmVsIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc2VsZWN0b3ItY29udGFpbmVyIHtcclxuICAgICAgICAgICAgLnNlbGVjdG9yLWJ1dHRvbiB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpob3Zlcjpub3QoLmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzVhNjI2ODtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmLmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuZHJvcGRvd24tbWVudSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG5cclxuICAgICAgICAgICAgICAgIC5kcm9wZG93bi1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLXNpZGViYXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQzYTQwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM0OTUwNTc7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLWxpc3QgLmJ1aWxkaW5nLWl0ZW0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzBkNmVmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtbWFpbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYWluLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5zZWFyY2gtYm94IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtZ3JpZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIgLmhvdXNlaG9sZC1idXR0b24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmOmhvdmVyOm5vdCguZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzVhNjI2ODtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICYuc2VsZWN0ZWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMGQ2ZWZkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMwZDZlZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuZHJvcGRvd24tZm9vdGVyIHtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQzYTQwO1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvaG91c2Vob2xkLWJpbmRpbmcvaG91c2Vob2xkLWJpbmRpbmcuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxrQkFBQTtFQUNBLFdBQUE7QUFDSjtBQUNJO0VBQ0ksbUJBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7RUFDQSx5QkFBQTtFQUNBLHVCQUFBO0FBQ1I7QUFDUTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esc0JBQUE7QUFDWjtBQUNZO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQUNoQjtBQUNnQjtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBQ3BCO0FBS1k7RUFDSSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxXQUFBO0VBQ0EscUJBQUE7QUFIaEI7QUFLZ0I7RUFDSSxnQkFBQTtBQUhwQjtBQU1nQjtFQUNJLGVBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBSnBCO0FBT2dCO0VBQ0ksYUFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0FBTHBCO0FBT29CO0VBQ0ksb0JBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7QUFMeEI7QUFPd0I7RUFDSSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0EsU0FBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBTDVCO0FBTzRCO0VBQ0kseUJBQUE7QUFMaEM7QUFRNEI7RUFDSSxZQUFBO0VBQ0EsbUJBQUE7QUFOaEM7QUFTNEI7RUFDSSxrQkFBQTtBQVBoQztBQWdCSTtFQUNJLGtCQUFBO0FBZFI7QUFnQlE7RUFDSSxXQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7RUFDQSxpQ0FBQTtBQWRaO0FBZ0JZO0VBQ0kseUJBQUE7RUFDQSxxQkFBQTtBQWRoQjtBQWlCWTtFQUNJLGFBQUE7RUFDQSxxQkFBQTtFQUNBLGdEQUFBO0FBZmhCO0FBa0JZO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUJBQUE7QUFoQmhCO0FBbUJZO0VBQ0ksY0FBQTtFQUNBLG1CQUFBO0FBakJoQjtBQW9CWTtFQUNJLGNBQUE7RUFDQSx1Q0FBQTtBQWxCaEI7QUFvQmdCO0VBQ0kseUJBQUE7QUFsQnBCO0FBdUJRO0VBQ0ksa0JBQUE7RUFDQSx5QkFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsYUFBQTtFQUNBLHNCQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtFQUNBLDZDQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQXJCWjtBQXVCWTtFQUNJLGFBQUE7RUFDQSxhQUFBO0FBckJoQjtBQXVCZ0I7RUFDSSxjQUFBO0VBQ0EsK0JBQUE7RUFDQSx5QkFBQTtBQXJCcEI7QUF1Qm9CO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx5QkFBQTtBQXJCeEI7QUF1QndCO0VBQ0ksbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFyQjVCO0FBeUJvQjtFQUNJLGlCQUFBO0VBQ0EsZ0JBQUE7QUF2QnhCO0FBeUJ3QjtFQUNJLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLDhDQUFBO0FBdkI1QjtBQXlCNEI7RUFDSSx5QkFBQTtBQXZCaEM7QUEwQjRCO0VBQ0kseUJBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUF4QmhDO0FBMkI0QjtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0FBekJoQztBQTJCZ0M7RUFDSSxtQkFBQTtBQXpCcEM7QUE0QmdDO0VBQ0ksa0JBQUE7RUFDQSxjQUFBO0FBMUJwQztBQWlDZ0I7RUFDSSxjQUFBO0FBL0JwQjtBQWlDb0I7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7RUFDQSx5QkFBQTtBQS9CeEI7QUFpQ3dCO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBQS9CNUI7QUFpQzRCO0VBQ0ksbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUEvQmhDO0FBaUNnQztFQUNJLGNBQUE7QUEvQnBDO0FBb0N3QjtFQUNJLGFBQUE7RUFDQSxZQUFBO0FBbEM1QjtBQXNDb0I7RUFDSSxlQUFBO0VBQ0EsZ0NBQUE7QUFwQ3hCO0FBdUNvQjtFQUNJLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0FBckN4QjtBQXVDd0I7RUFDSSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLGNBQUE7QUFyQzVCO0FBdUM0QjtFQUNJLGVBQUE7RUFDQSxxQkFBQTtFQUNBLGNBQUE7QUFyQ2hDO0FBd0M0QjtFQUNJLG1CQUFBO0VBQ0EsU0FBQTtBQXRDaEM7QUEwQ3dCO0VBQ0ksYUFBQTtFQUNBLHFDQUFBO0VBQ0EsWUFBQTtBQXhDNUI7QUEwQzRCO0VBQ0ksZUFBQTtFQUNBLHlCQUFBO0VBQ0Esc0JBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7RUFDQSxpQ0FBQTtFQUNBLGtCQUFBO0FBeENoQztBQTBDZ0M7RUFDSSx5QkFBQTtBQXhDcEM7QUEyQ2dDO0VBQ0kseUJBQUE7RUFDQSxxQkFBQTtFQUNBLGNBQUE7QUF6Q3BDO0FBNENnQztFQUNJLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtBQTFDcEM7QUE2Q2dDO0VBQ0ksY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUEzQ3BDO0FBOENnQztFQUNJLGNBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxvQkFBQTtBQTVDcEM7QUFvRFk7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsNkJBQUE7RUFDQSx5QkFBQTtBQWxEaEI7QUFvRGdCO0VBQ0ksYUFBQTtFQUNBLFNBQUE7QUFsRHBCO0FBb0RvQjs7RUFFSSxrQkFBQTtFQUNBLGNBQUE7QUFsRHhCO0FBeURJO0VBQ0ksZUFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7QUF2RFI7O0FBNERBO0VBSWdCO0lBQ0ksc0JBQUE7SUFDQSxZQUFBO0lBQ0EsaUJBQUE7RUE1RGxCO0VBOERrQjtJQUNJLFdBQUE7SUFDQSxnQkFBQTtJQUNBLGtCQUFBO0lBQ0EsZ0NBQUE7RUE1RHRCO0VBOERzQjtJQUNJLGdCQUFBO0VBNUQxQjtFQWdFa0I7SUFDSSxXQUFBO0VBOUR0QjtFQWlFMEI7SUFDSSxxQ0FBQTtFQS9EOUI7QUFDRjtBQXlFQTtFQUVRO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUF4RVY7RUEwRVU7SUFDSSxjQUFBO0VBeEVkO0VBMkVVO0lBQ0ksY0FBQTtFQXpFZDtFQThFVTtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBNUVkO0VBOEVjO0lBQ0kseUJBQUE7RUE1RWxCO0VBK0VjO0lBQ0kseUJBQUE7RUE3RWxCO0VBaUZVO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtFQS9FZDtFQWtGa0I7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0VBaEZ0QjtFQWtGc0I7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQWhGMUI7RUFtRnNCO0lBQ0ksY0FBQTtFQWpGMUI7RUFtRjBCO0lBQ0kseUJBQUE7RUFqRjlCO0VBb0YwQjtJQUNJLHlCQUFBO0lBQ0EsV0FBQTtFQWxGOUI7RUF3RnNCO0lBQ0kseUJBQUE7SUFDQSxxQkFBQTtJQUNBLGNBQUE7RUF0RjFCO0VBeUZzQjtJQUNJLHFCQUFBO0VBdkYxQjtFQTJGMEI7SUFDSSx5QkFBQTtJQUNBLHFCQUFBO0lBQ0EsY0FBQTtFQXpGOUI7RUEyRjhCO0lBQ0kseUJBQUE7RUF6RmxDO0VBNEY4QjtJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxXQUFBO0VBMUZsQztFQWlHYztJQUNJLHlCQUFBO0lBQ0EscUJBQUE7SUFDQSxjQUFBO0VBL0ZsQjtBQUNGO0FBQ0EsdzMzQkFBdzMzQiIsInNvdXJjZXNDb250ZW50IjpbIi5ob3VzZWhvbGQtYmluZGluZy1jb250YWluZXIge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcblxyXG4gICAgLnNlbGVjdGVkLWhvdXNlaG9sZHMtYXJlYSB7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuICAgICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuXHJcbiAgICAgICAgLnNlbGVjdGVkLWhlYWRlciB7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3RlZC1pbmZvIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgZ2FwOiAwLjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgLnNlbGVjdGVkLWNvdW50IHtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5zZWxlY3RlZC1jb250ZW50IHtcclxuICAgICAgICAgICAgLmJ1aWxkaW5nLWdyb3VwIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgICAgICAgICAgICAgIGdhcDogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICY6bGFzdC1jaGlsZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuYnVpbGRpbmctbGFiZWwge1xyXG4gICAgICAgICAgICAgICAgICAgIG1pbi13aWR0aDogM3JlbTtcclxuICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC5ob3VzZWhvbGRzLXRhZ3Mge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgZmxleC13cmFwOiB3cmFwO1xyXG4gICAgICAgICAgICAgICAgICAgIGdhcDogMC4yNXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC10YWcge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMXJlbTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5yZW1vdmUtYnRuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICMxOTc2ZDI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogMXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2JiZGVmYjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcGFjaXR5OiAwLjU7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYi1pY29uIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnNlbGVjdG9yLWNvbnRhaW5lciB7XHJcbiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG5cclxuICAgICAgICAuc2VsZWN0b3ItYnV0dG9uIHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgcGFkZGluZzogMC41cmVtIDAuNzVyZW07XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlLWluLW91dDtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXI6bm90KC5kaXNhYmxlZCkge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2FkYjViZDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjpmb2N1cyB7XHJcbiAgICAgICAgICAgICAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjODBiZGZmO1xyXG4gICAgICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMC4ycmVtIHJnYmEoMCwgMTIzLCAyNTUsIDAuMjUpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAmLmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNjU7XHJcbiAgICAgICAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U5ZWNlZjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLnNlbGVjdG9yLXRleHQge1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcbiAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuY2hldnJvbi1pY29uIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG5cclxuICAgICAgICAgICAgICAgICYucm90YXRlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmRyb3Bkb3duLW1lbnUge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogY2FsYygxMDAlICsgMC4yNXJlbSk7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgICB6LWluZGV4OiAxMDUwO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwLjVyZW0gMXJlbSByZ2JhKDAsIDAsIDAsIDAuMTUpO1xyXG4gICAgICAgICAgICBtYXgtaGVpZ2h0OiAyNHJlbTtcclxuICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAgICAgICAgIC5kcm9wZG93bi1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDIwcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgIC5idWlsZGluZ3Mtc2lkZWJhciB7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDMzLjMzMyU7XHJcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNlOWVjZWY7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci10aXRsZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLWxpc3Qge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDIwcmVtIC0gM3JlbSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctaXRlbSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4xNXMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZy1pbmZvIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctbmFtZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmctY291bnQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuaG91c2Vob2xkcy1tYWluIHtcclxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogNjYuNjY3JTtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLm1haW4taGVhZGVyIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2U5ZWNlZjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5oZWFkZXItbGVmdCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogMC41cmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYWluLXRpdGxlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM0OTUwNTc7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZy1pbmRpY2F0b3Ige1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5oZWFkZXItYWN0aW9ucyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAwLjI1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuc2VhcmNoLWJveCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAuaG91c2Vob2xkcy1ncmlkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogMC41cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDIwcmVtIC0gNnJlbSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZW1wdHktc3RhdGUge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAycmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmVtcHR5LWljb24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNhZGI1YmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmVtcHR5LXRleHQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZ3JpZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIDFmcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnYXA6IDAuMjVyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC1idXR0b24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMTVzIGVhc2UtaW4tb3V0O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3Zlcjpub3QoLmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLnNlbGVjdGVkIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2UzZjJmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTk3NmQyO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzE5NzZkMjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICYuZGlzYWJsZWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eTogMC42NTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5ob3VzZWhvbGQtY29kZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZC1mbG9vciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiAwLjEyNXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5kcm9wZG93bi1mb290ZXIge1xyXG4gICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLjc1cmVtO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVjZWY7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgIC5mb290ZXItaW5mbyB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgICAgICAgICBnYXA6IDFyZW07XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5tYXgtc2VsZWN0aW9ucy10ZXh0LFxyXG4gICAgICAgICAgICAgICAgICAgIC5jdXJyZW50LXNlbGVjdGlvbnMtdGV4dCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5iYWNrZHJvcCB7XHJcbiAgICAgICAgcG9zaXRpb246IGZpeGVkO1xyXG4gICAgICAgIHRvcDogMDtcclxuICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICB6LWluZGV4OiAxMDQwO1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgfVxyXG59XHJcblxyXG4vLyDDqcKfwr/DpsKHwonDpcK8wo/DqMKowq3DqMKowohcclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAuaG91c2Vob2xkLWJpbmRpbmctY29udGFpbmVyIHtcclxuICAgICAgICAuc2VsZWN0b3ItY29udGFpbmVyIHtcclxuICAgICAgICAgICAgLmRyb3Bkb3duLW1lbnUge1xyXG4gICAgICAgICAgICAgICAgLmRyb3Bkb3duLWNvbnRlbnQge1xyXG4gICAgICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBhdXRvO1xyXG4gICAgICAgICAgICAgICAgICAgIG1heC1oZWlnaHQ6IDIwcmVtO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLXNpZGViYXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4LWhlaWdodDogOHJlbTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiBub25lO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjtcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5idWlsZGluZ3MtbGlzdCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiBjYWxjKDhyZW0gLSAzcmVtKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtbWFpbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtZ3JpZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIDFmcik7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIMOmwrfCscOowonCssOkwrjCu8OpwqHCjMOmwpTCr8Omwo/CtFxyXG5AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKSB7XHJcbiAgICAuaG91c2Vob2xkLWJpbmRpbmctY29udGFpbmVyIHtcclxuICAgICAgICAuc2VsZWN0ZWQtaG91c2Vob2xkcy1hcmVhIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM0M2E0MDtcclxuICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuXHJcbiAgICAgICAgICAgIC5zZWxlY3RlZC1oZWFkZXIgLnNlbGVjdGVkLWluZm8gLnNlbGVjdGVkLWNvdW50IHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuc2VsZWN0ZWQtY29udGVudCAuYnVpbGRpbmctZ3JvdXAgLmJ1aWxkaW5nLWxhYmVsIHtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc2VsZWN0b3ItY29udGFpbmVyIHtcclxuICAgICAgICAgICAgLnNlbGVjdG9yLWJ1dHRvbiB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcblxyXG4gICAgICAgICAgICAgICAgJjpob3Zlcjpub3QoLmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzVhNjI2ODtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAmLmRpc2FibGVkIHtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuZHJvcGRvd24tbWVudSB7XHJcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG5cclxuICAgICAgICAgICAgICAgIC5kcm9wZG93bi1jb250ZW50IHtcclxuICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLXNpZGViYXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQzYTQwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICM0OTUwNTc7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuc2lkZWJhci1oZWFkZXIge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAuYnVpbGRpbmdzLWxpc3QgLmJ1aWxkaW5nLWl0ZW0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzBkNmVmZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2ZmZjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtbWFpbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5tYWluLWhlYWRlciB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDk1MDU3O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjNmM3NTdkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICNmOGY5ZmE7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC5zZWFyY2gtYm94IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzZjNzU3ZDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLmhvdXNlaG9sZHMtZ3JpZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZ3JpZC1jb250YWluZXIgLmhvdXNlaG9sZC1idXR0b24ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYWRiNWJkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZjhmOWZhO1xyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAmOmhvdmVyOm5vdCguZGlzYWJsZWQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzVhNjI2ODtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICYuc2VsZWN0ZWQge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMGQ2ZWZkO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXItY29sb3I6ICMwZDZlZmQ7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICAuZHJvcGRvd24tZm9vdGVyIHtcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQzYTQwO1xyXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQ5NTA1NztcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogI2Y4ZjlmYTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r5", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r1", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "HouseholdBindingComponent_div_7_button_8_Template_button_click_0_listener", "building_r8", "_r7", "onBuildingSelect", "ɵɵclassProp", "selectedBuilding", "ɵɵtextInterpolate", "getBuildingCount", "HouseholdBindingComponent_div_7_div_16_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_div_7_div_16_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_div_7_div_16_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_div_7_div_16_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "ɵɵtwoWayListener", "HouseholdBindingComponent_div_7_div_17_Template_input_ngModelChange_1_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "searchTerm", "HouseholdBindingComponent_div_7_div_17_Template_input_input_1_listener", "onSearchChange", "ɵɵtwoWayProperty", "household_r13", "floor", "HouseholdBindingComponent_div_7_div_21_button_1_Template_button_click_0_listener", "_r12", "onHouseholdToggle", "HouseholdBindingComponent_div_7_div_21_button_1_span_3_Template", "isHouseholdSelected", "code", "isDisabled", "HouseholdBindingComponent_div_7_div_21_button_1_Template", "filteredHouseholds", "maxSelections", "HouseholdBindingComponent_div_7_button_8_Template", "HouseholdBindingComponent_div_7_span_15_Template", "HouseholdBindingComponent_div_7_div_16_Template", "HouseholdBindingComponent_div_7_div_17_Template", "HouseholdBindingComponent_div_7_div_19_Template", "HouseholdBindingComponent_div_7_div_20_Template", "HouseholdBindingComponent_div_7_div_21_Template", "HouseholdBindingComponent_div_7_span_24_Template", "HouseholdBindingComponent_div_7_Template_button_click_27_listener", "_r6", "closeDropdown", "allowBatchSelect", "length", "allowSearch", "HouseholdBindingComponent_div_8_Template_div_click_0_listener", "_r14", "HouseholdBindingComponent", "constructor", "cdr", "placeholder", "buildingData", "showSelectedArea", "selectionChange", "isOpen", "selectedHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "ngOnInit", "Object", "keys", "generateMockData", "ngOnChanges", "changes", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "building", "item", "find", "h", "push", "Array", "from", "_", "i", "String", "padStart", "Math", "isSelected", "console", "log", "households", "filter", "household", "toLowerCase", "includes", "event", "target", "newSelection", "emitChanges", "householdCode", "availableHouseholds", "map", "Set", "buildingHouseholds", "selectedItems", "emit", "toggleDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "ɵɵdirectiveInject", "ChangeDetectorRef", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "rf", "ctx", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "HouseholdBindingComponent_div_7_Template", "HouseholdBindingComponent_div_8_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  filteredHouseholds: HouseholdItem[] = [];\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(private cdr: ChangeDetectorRef) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  }\r\n  ngOnInit() {\r\n    // 如果沒有提供 buildingData，使用 mock 資料\r\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n      this.buildingData = this.generateMockData();\r\n    }\r\n\r\n    this.buildings = Object.keys(this.buildingData);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n\r\n  private generateMockData(): BuildingData {\r\n    return {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => ({\r\n        code: `A${String(i + 1).padStart(3, '0')}`,\r\n        building: 'A棟',\r\n        floor: `${Math.floor(i / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      })),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => ({\r\n        code: `B${String(i + 1).padStart(3, '0')}`,\r\n        building: 'B棟',\r\n        floor: `${Math.floor(i / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      })),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => ({\r\n        code: `C${String(i + 1).padStart(3, '0')}`,\r\n        building: 'C棟',\r\n        floor: `${Math.floor(i / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      })),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => ({\r\n        code: `D${String(i + 1).padStart(3, '0')}`,\r\n        building: 'D棟',\r\n        floor: `${Math.floor(i / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      })),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => ({\r\n        code: `E${String(i + 1).padStart(3, '0')}`,\r\n        building: 'E棟',\r\n        floor: `${Math.floor(i / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }))\r\n    };\r\n  }\r\n  onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n  }\r\n  updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n    this.filteredHouseholds = households.filter(household =>\r\n      household.code.toLowerCase().includes(this.searchTerm.toLowerCase())\r\n    );\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n  }\r\n\r\n  onHouseholdToggle(household: HouseholdItem) {\r\n    const isSelected = this.selectedHouseholds.includes(household.code);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== household.code);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, household.code];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  }\r\n\r\n  onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    const availableHouseholds = this.filteredHouseholds\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].map(h => h.code);\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].map(h => h.code);\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              {{householdCode}}\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 選擇器 -->\r\n  <div class=\"selector-container\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled\" [disabled]=\"disabled\"\r\n      (click)=\"toggleDropdown()\">\r\n      <span class=\"selector-text\">\r\n        {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n      </span>\r\n      <nb-icon icon=\"chevron-down-outline\" [class.rotated]=\"isOpen\" class=\"chevron-icon\">\r\n      </nb-icon>\r\n    </button>\r\n\r\n    <!-- 下拉選單 -->\r\n    <div *ngIf=\"isOpen\" class=\"dropdown-menu\">\r\n      <div class=\"dropdown-content\">\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div class=\"buildings-sidebar\">\r\n          <div class=\"sidebar-header\">\r\n            <nb-icon icon=\"home-outline\" class=\"text-primary\"></nb-icon>\r\n            <span class=\"sidebar-title\">棟別</span>\r\n          </div>\r\n          <div class=\"buildings-list\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" class=\"building-item\"\r\n              [class.active]=\"selectedBuilding === building\" (click)=\"onBuildingSelect(building)\">\r\n              <div class=\"building-info\">\r\n                <span class=\"building-name\">{{building}}</span>\r\n                <span class=\"building-count\">{{getBuildingCount(building)}}戶</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 戶別選擇主區域 -->\r\n        <div class=\"households-main\">\r\n          <div class=\"main-header\">\r\n            <div class=\"header-left\">\r\n              <nb-icon icon=\"grid-outline\" class=\"text-primary\"></nb-icon>\r\n              <span class=\"main-title\">\r\n                戶別\r\n                <span *ngIf=\"selectedBuilding\" class=\"building-indicator\">({{selectedBuilding}})</span>\r\n              </span>\r\n            </div>\r\n            <div class=\"header-actions\" *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\">\r\n              <button type=\"button\" class=\"btn btn-outline-primary btn-sm\" [disabled]=\"!canSelectMore()\"\r\n                (click)=\"onSelectAllFiltered()\">\r\n                全選當前\r\n              </button>\r\n              <button type=\"button\" class=\"btn btn-outline-primary btn-sm\" [disabled]=\"!canSelectMore()\"\r\n                (click)=\"onSelectAllBuilding()\">\r\n                全選{{selectedBuilding}}\r\n              </button>\r\n              <button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" *ngIf=\"isSomeBuildingSelected()\"\r\n                (click)=\"onUnselectAllBuilding()\">\r\n                取消全選\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 搜尋框 -->\r\n          <div *ngIf=\"allowSearch && selectedBuilding\" class=\"search-box\">\r\n            <input type=\"text\" class=\"form-control form-control-sm\" placeholder=\"搜尋戶別...\" [(ngModel)]=\"searchTerm\"\r\n              (input)=\"onSearchChange($event)\">\r\n          </div>\r\n\r\n          <!-- 戶別網格 -->\r\n          <div class=\"households-grid\">\r\n            <div *ngIf=\"!selectedBuilding\" class=\"empty-state\">\r\n              <nb-icon icon=\"home-outline\" class=\"empty-icon\"></nb-icon>\r\n              <p class=\"empty-text\">請先選擇棟別</p>\r\n            </div>\r\n\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length === 0 && searchTerm\" class=\"empty-state\">\r\n              <nb-icon icon=\"search-outline\" class=\"empty-icon\"></nb-icon>\r\n              <p class=\"empty-text\">沒有找到符合的戶別</p>\r\n            </div>\r\n\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\" class=\"grid-container\">\r\n              <button *ngFor=\"let household of filteredHouseholds\" type=\"button\" class=\"household-button\"\r\n                [class.selected]=\"isHouseholdSelected(household.code)\"\r\n                [class.disabled]=\"household.isDisabled || (!canSelectMore() && !isHouseholdSelected(household.code))\"\r\n                [disabled]=\"household.isDisabled || (!canSelectMore() && !isHouseholdSelected(household.code))\"\r\n                (click)=\"onHouseholdToggle(household)\">\r\n                <span class=\"household-code\">{{household.code}}</span>\r\n                <span *ngIf=\"household.floor\" class=\"household-floor\">{{household.floor}}</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部操作區 -->\r\n      <div class=\"dropdown-footer\">\r\n        <div class=\"footer-info\">\r\n          <span *ngIf=\"maxSelections\" class=\"max-selections-text\">\r\n            最多可選 {{maxSelections}} 個戶別\r\n          </span>\r\n          <span class=\"current-selections-text\">\r\n            已選擇: {{getSelectedCount()}}\r\n          </span>\r\n        </div>\r\n        <button type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"closeDropdown()\">\r\n          確定\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- 點擊外部關閉下拉選單 -->\r\n<div *ngIf=\"isOpen\" class=\"backdrop\" (click)=\"closeDropdown()\"></div>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;ICgB5DC,EAAA,CAAAC,cAAA,eAAkG;IAChGD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAG,UAAA,mBAAAC,6FAAA;MAAA,MAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,gBAAA,CAAgC;IAAA,EAAC;IACvGL,EAAA,CAAAa,SAAA,kBAAwC;IAE5Cb,EADE,CAAAc,YAAA,EAAS,EACJ;;;;;IAJLd,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAgB,kBAAA,MAAAX,gBAAA,MACA;IAAyCL,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;;;;;IALpElB,EAAA,CAAAmB,uBAAA,GAAoD;IAClDnB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAc,YAAA,EAAM;IAC/Cd,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAoB,UAAA,IAAAC,oEAAA,mBAAkG;IAMpGrB,EAAA,CAAAc,YAAA,EAAM;;;;;;IARsBd,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,kBAAA,KAAAM,WAAA,MAAa;IAEPtB,EAAA,CAAAe,SAAA,GAA0C;IAA1Cf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAc,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhFtB,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAoB,UAAA,IAAAI,6DAAA,2BAAoD;IAWtDxB,EAAA,CAAAc,YAAA,EAAM;;;;;IAXWd,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAgB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpDtB,EAFJ,CAAAC,cAAA,aAAgG,aACjE,cACA;IACzBD,EAAA,CAAAa,SAAA,kBAA8D;IAC9Db,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAc,YAAA,EAAO,EAC9D;IACNd,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAG,UAAA,mBAAAuB,iEAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,UAAA,EAAY;IAAA,EAAC;IACtG5B,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAc,YAAA,EAAS,EACL;IACNd,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAoB,UAAA,IAAAS,8CAAA,kBAA+D;IAcnE7B,EADE,CAAAc,YAAA,EAAM,EACF;;;;IArB6Bd,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,kBAAA,qCAAAP,MAAA,CAAAqB,gBAAA,QAA8B;IAED9B,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,aAAAR,MAAA,CAAAS,QAAA,CAAqB;IAKvDlB,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;;;;;;IAqChC/B,EAAA,CAAAC,cAAA,iBACsF;IAArCD,EAAA,CAAAG,UAAA,mBAAA6B,0EAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAM,aAAA,CAAA4B,GAAA,EAAA1B,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0B,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAEjFjC,EADF,CAAAC,cAAA,cAA2B,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAc,YAAA,EAAO;IAC/Cd,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAEhEF,EAFgE,CAAAc,YAAA,EAAO,EAC/D,EACC;;;;;IALPd,EAAA,CAAAoC,WAAA,WAAA3B,MAAA,CAAA4B,gBAAA,KAAAJ,WAAA,CAA8C;IAEhBjC,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAsC,iBAAA,CAAAL,WAAA,CAAY;IACXjC,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAgB,kBAAA,KAAAP,MAAA,CAAA8B,gBAAA,CAAAN,WAAA,YAA+B;;;;;IAa5DjC,EAAA,CAAAC,cAAA,eAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA7Bd,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,kBAAA,MAAAP,MAAA,CAAA4B,gBAAA,MAAsB;;;;;;IAYlFrC,EAAA,CAAAC,cAAA,iBACoC;IAAlCD,EAAA,CAAAG,UAAA,mBAAAqC,iFAAA;MAAAxC,EAAA,CAAAM,aAAA,CAAAmC,IAAA;MAAA,MAAAhC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiC,qBAAA,EAAuB;IAAA,EAAC;IACjC1C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;;;IAXTd,EADF,CAAAC,cAAA,cAA0G,iBAEtE;IAAhCD,EAAA,CAAAG,UAAA,mBAAAwC,wEAAA;MAAA3C,EAAA,CAAAM,aAAA,CAAAsC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,mBAAA,EAAqB;IAAA,EAAC;IAC/B7C,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,iBACkC;IAAhCD,EAAA,CAAAG,UAAA,mBAAA2C,wEAAA;MAAA9C,EAAA,CAAAM,aAAA,CAAAsC,GAAA;MAAA,MAAAnC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsC,mBAAA,EAAqB;IAAA,EAAC;IAC/B/C,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAoB,UAAA,IAAA4B,wDAAA,qBACoC;IAGtChD,EAAA,CAAAc,YAAA,EAAM;;;;IAZyDd,EAAA,CAAAe,SAAA,EAA6B;IAA7Bf,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAwC,aAAA,GAA6B;IAI7BjD,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAiB,UAAA,cAAAR,MAAA,CAAAwC,aAAA,GAA6B;IAExFjD,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,kBAAAP,MAAA,CAAA4B,gBAAA,MACF;IACgErC,EAAA,CAAAe,SAAA,EAA8B;IAA9Bf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAAyC,sBAAA,GAA8B;;;;;;IAShGlD,EADF,CAAAC,cAAA,cAAgE,gBAE3B;IAD2CD,EAAA,CAAAmD,gBAAA,2BAAAC,+EAAAC,MAAA;MAAArD,EAAA,CAAAM,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuD,kBAAA,CAAA9C,MAAA,CAAA+C,UAAA,EAAAH,MAAA,MAAA5C,MAAA,CAAA+C,UAAA,GAAAH,MAAA;MAAA,OAAArD,EAAA,CAAAW,WAAA,CAAA0C,MAAA;IAAA,EAAwB;IACpGrD,EAAA,CAAAG,UAAA,mBAAAsD,uEAAAJ,MAAA;MAAArD,EAAA,CAAAM,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAL,MAAA,CAAsB;IAAA,EAAC;IACpCrD,EAFE,CAAAc,YAAA,EACmC,EAC/B;;;;IAF0Ed,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAA2D,gBAAA,YAAAlD,MAAA,CAAA+C,UAAA,CAAwB;;;;;IAMtGxD,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAa,SAAA,kBAA0D;IAC1Db,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAC9BF,EAD8B,CAAAc,YAAA,EAAI,EAC5B;;;;;IAENd,EAAA,CAAAC,cAAA,cAAmG;IACjGD,EAAA,CAAAa,SAAA,kBAA4D;IAC5Db,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,6DAAS;IACjCF,EADiC,CAAAc,YAAA,EAAI,EAC/B;;;;;IASFd,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IAA1Bd,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAsC,iBAAA,CAAAsB,aAAA,CAAAC,KAAA,CAAmB;;;;;;IAN3E7D,EAAA,CAAAC,cAAA,iBAIyC;IAAvCD,EAAA,CAAAG,UAAA,mBAAA2D,iFAAA;MAAA,MAAAF,aAAA,GAAA5D,EAAA,CAAAM,aAAA,CAAAyD,IAAA,EAAAvD,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAuD,iBAAA,CAAAJ,aAAA,CAA4B;IAAA,EAAC;IACtC5D,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAc,YAAA,EAAO;IACtDd,EAAA,CAAAoB,UAAA,IAAA6C,+DAAA,mBAAsD;IACxDjE,EAAA,CAAAc,YAAA,EAAS;;;;;IALPd,EADA,CAAAoC,WAAA,aAAA3B,MAAA,CAAAyD,mBAAA,CAAAN,aAAA,CAAAO,IAAA,EAAsD,aAAAP,aAAA,CAAAQ,UAAA,KAAA3D,MAAA,CAAAwC,aAAA,OAAAxC,MAAA,CAAAyD,mBAAA,CAAAN,aAAA,CAAAO,IAAA,EAC+C;IACrGnE,EAAA,CAAAiB,UAAA,aAAA2C,aAAA,CAAAQ,UAAA,KAAA3D,MAAA,CAAAwC,aAAA,OAAAxC,MAAA,CAAAyD,mBAAA,CAAAN,aAAA,CAAAO,IAAA,EAA+F;IAElEnE,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAsC,iBAAA,CAAAsB,aAAA,CAAAO,IAAA,CAAkB;IACxCnE,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,SAAA2C,aAAA,CAAAC,KAAA,CAAqB;;;;;IAPhC7D,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAoB,UAAA,IAAAiD,wDAAA,qBAIyC;IAI3CrE,EAAA,CAAAc,YAAA,EAAM;;;;IAR0Bd,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAA6D,kBAAA,CAAqB;;;;;IAgBvDtE,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAc,YAAA,EAAO;;;;IADLd,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAgB,kBAAA,+BAAAP,MAAA,CAAA8D,aAAA,yBACF;;;;;;IA9EAvE,EAJN,CAAAC,cAAA,cAA0C,cACV,cAEG,cACD;IAC1BD,EAAA,CAAAa,SAAA,kBAA4D;IAC5Db,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAChCF,EADgC,CAAAc,YAAA,EAAO,EACjC;IACNd,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAoB,UAAA,IAAAoD,iDAAA,qBACsF;IAO1FxE,EADE,CAAAc,YAAA,EAAM,EACF;IAKFd,EAFJ,CAAAC,cAAA,cAA6B,eACF,eACE;IACvBD,EAAA,CAAAa,SAAA,mBAA4D;IAC5Db,EAAA,CAAAC,cAAA,gBAAyB;IACvBD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAoB,UAAA,KAAAqD,gDAAA,mBAA0D;IAE9DzE,EADE,CAAAc,YAAA,EAAO,EACH;IACNd,EAAA,CAAAoB,UAAA,KAAAsD,+CAAA,kBAA0G;IAc5G1E,EAAA,CAAAc,YAAA,EAAM;IAGNd,EAAA,CAAAoB,UAAA,KAAAuD,+CAAA,kBAAgE;IAMhE3E,EAAA,CAAAC,cAAA,eAA6B;IAW3BD,EAVA,CAAAoB,UAAA,KAAAwD,+CAAA,kBAAmD,KAAAC,+CAAA,kBAKgD,KAAAC,+CAAA,kBAKb;IAY5F9E,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IAIJd,EADF,CAAAC,cAAA,eAA6B,eACF;IACvBD,EAAA,CAAAoB,UAAA,KAAA2D,gDAAA,mBAAwD;IAGxD/E,EAAA,CAAAC,cAAA,gBAAsC;IACpCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAc,YAAA,EAAO,EACH;IACNd,EAAA,CAAAC,cAAA,kBAA+E;IAA1BD,EAAA,CAAAG,UAAA,mBAAA6E,kEAAA;MAAAhF,EAAA,CAAAM,aAAA,CAAA2E,GAAA;MAAA,MAAAxE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyE,aAAA,EAAe;IAAA,EAAC;IAC5ElF,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;;;;IAlF+Bd,EAAA,CAAAe,SAAA,GAAY;IAAZf,EAAA,CAAAiB,UAAA,YAAAR,MAAA,CAAAsB,SAAA,CAAY;IAiB9B/B,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4B,gBAAA,CAAsB;IAGJrC,EAAA,CAAAe,SAAA,EAA2E;IAA3Ef,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA0E,gBAAA,IAAA1E,MAAA,CAAA4B,gBAAA,IAAA5B,MAAA,CAAA6D,kBAAA,CAAAc,MAAA,KAA2E;IAiBpGpF,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4E,WAAA,IAAA5E,MAAA,CAAA4B,gBAAA,CAAqC;IAOnCrC,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAiB,UAAA,UAAAR,MAAA,CAAA4B,gBAAA,CAAuB;IAKvBrC,EAAA,CAAAe,SAAA,EAAuE;IAAvEf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4B,gBAAA,IAAA5B,MAAA,CAAA6D,kBAAA,CAAAc,MAAA,UAAA3E,MAAA,CAAA+C,UAAA,CAAuE;IAKvExD,EAAA,CAAAe,SAAA,EAAuD;IAAvDf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA4B,gBAAA,IAAA5B,MAAA,CAAA6D,kBAAA,CAAAc,MAAA,KAAuD;IAiBxDpF,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAiB,UAAA,SAAAR,MAAA,CAAA8D,aAAA,CAAmB;IAIxBvE,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAgB,kBAAA,0BAAAP,MAAA,CAAAqB,gBAAA,QACF;;;;;;IAWV9B,EAAA,CAAAC,cAAA,cAA+D;IAA1BD,EAAA,CAAAG,UAAA,mBAAAmF,8DAAA;MAAAtF,EAAA,CAAAM,aAAA,CAAAiF,IAAA;MAAA,MAAA9E,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyE,aAAA,EAAe;IAAA,EAAC;IAAClF,EAAA,CAAAc,YAAA,EAAM;;;AD9GrE,OAAM,MAAO0E,yBAAyB;EAsBpCC,YAAoBC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IArBd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAApB,aAAa,GAAkB,IAAI;IACnC,KAAArD,QAAQ,GAAY,KAAK;IACzB,KAAA0E,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAR,WAAW,GAAY,IAAI;IAC3B,KAAAF,gBAAgB,GAAY,IAAI;IAE/B,KAAAW,eAAe,GAAG,IAAIjG,YAAY,EAAmB;IAE/D,KAAAkG,MAAM,GAAG,KAAK;IACd,KAAA1D,gBAAgB,GAAG,EAAE;IACrB,KAAAmB,UAAU,GAAG,EAAE;IACf,KAAAwC,kBAAkB,GAAa,EAAE;IACjC,KAAAjE,SAAS,GAAa,EAAE;IACxB,KAAAuC,kBAAkB,GAAoB,EAAE;IACxC,KAAA2B,kBAAkB,GAAqC,EAAE;IACzD;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAEiB;EAE9CC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACH,kBAAkB,GAAGG,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACtC,UAAmB;IAClC,IAAI,CAAClD,QAAQ,GAAGkD,UAAU;EAC5B;EACAuC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACf,YAAY,IAAIgB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjB,YAAY,CAAC,CAACR,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAACQ,YAAY,GAAG,IAAI,CAACkB,gBAAgB,EAAE;IAC7C;IAEA,IAAI,CAAC/E,SAAS,GAAG6E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjB,YAAY,CAAC;IAC/C,IAAI,CAACU,wBAAwB,EAAE;EACjC;EAEAS,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAACjF,SAAS,GAAG6E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjB,YAAY,CAAC;MAC/C,IAAI,CAACqB,wBAAwB,EAAE;MAC/B,IAAI,CAACX,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,MAAMY,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAClB,kBAAkB,CAACmB,OAAO,CAAChD,IAAI,IAAG;MACrC,KAAK,MAAMiD,QAAQ,IAAI,IAAI,CAACrF,SAAS,EAAE;QACrC,MAAMsF,IAAI,GAAG,IAAI,CAACzB,YAAY,CAACwB,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIkD,IAAI,EAAE;UACR,IAAI,CAACH,OAAO,CAACE,QAAQ,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,GAAG,EAAE;UAC9CF,OAAO,CAACE,QAAQ,CAAC,CAACI,IAAI,CAACrD,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC8B,kBAAkB,GAAGiB,OAAO;EACnC;EAEQJ,gBAAgBA,CAAA;IACtB,OAAO;MACL,IAAI,EAAEW,KAAK,CAACC,IAAI,CAAC;QAAEtC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACuC,CAAC,EAAEC,CAAC,MAAM;QAC1CzD,IAAI,EAAE,IAAI0D,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAE,GAAGkE,IAAI,CAAClE,KAAK,CAAC+D,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;QAClCI,UAAU,EAAE,KAAK;QACjB5D,UAAU,EAAE;OACb,CAAC,CAAC;MACH,IAAI,EAAEqD,KAAK,CAACC,IAAI,CAAC;QAAEtC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACuC,CAAC,EAAEC,CAAC,MAAM;QAC1CzD,IAAI,EAAE,IAAI0D,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAE,GAAGkE,IAAI,CAAClE,KAAK,CAAC+D,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;QAClCI,UAAU,EAAE,KAAK;QACjB5D,UAAU,EAAE;OACb,CAAC,CAAC;MACH,IAAI,EAAEqD,KAAK,CAACC,IAAI,CAAC;QAAEtC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACuC,CAAC,EAAEC,CAAC,MAAM;QAC1CzD,IAAI,EAAE,IAAI0D,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAE,GAAGkE,IAAI,CAAClE,KAAK,CAAC+D,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;QAClCI,UAAU,EAAE,KAAK;QACjB5D,UAAU,EAAE;OACb,CAAC,CAAC;MACH,IAAI,EAAEqD,KAAK,CAACC,IAAI,CAAC;QAAEtC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACuC,CAAC,EAAEC,CAAC,MAAM;QAC1CzD,IAAI,EAAE,IAAI0D,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAE,GAAGkE,IAAI,CAAClE,KAAK,CAAC+D,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;QAClCI,UAAU,EAAE,KAAK;QACjB5D,UAAU,EAAE;OACb,CAAC,CAAC;MACH,IAAI,EAAEqD,KAAK,CAACC,IAAI,CAAC;QAAEtC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACuC,CAAC,EAAEC,CAAC,MAAM;QAC1CzD,IAAI,EAAE,IAAI0D,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CV,QAAQ,EAAE,IAAI;QACdvD,KAAK,EAAE,GAAGkE,IAAI,CAAClE,KAAK,CAAC+D,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG;QAClCI,UAAU,EAAE,KAAK;QACjB5D,UAAU,EAAE;OACb,CAAC;KACH;EACH;EACAjC,gBAAgBA,CAACiF,QAAgB;IAC/Ba,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEd,QAAQ,CAAC;IAC3C,IAAI,CAAC/E,gBAAgB,GAAG+E,QAAQ;IAChC,IAAI,CAAC5D,UAAU,GAAG,EAAE;IACpB,IAAI,CAACyD,wBAAwB,EAAE;IAC/BgB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC5D,kBAAkB,CAACc,MAAM,CAAC;EAC3E;EACA6B,wBAAwBA,CAAA;IACtBgB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC7F,gBAAgB,CAAC;IAChF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAACiC,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM6D,UAAU,GAAG,IAAI,CAACvC,YAAY,CAAC,IAAI,CAACvD,gBAAgB,CAAC,IAAI,EAAE;IACjE4F,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEC,UAAU,CAAC/C,MAAM,CAAC;IACpE,IAAI,CAACd,kBAAkB,GAAG6D,UAAU,CAACC,MAAM,CAACC,SAAS,IACnDA,SAAS,CAAClE,IAAI,CAACmE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/E,UAAU,CAAC8E,WAAW,EAAE,CAAC,CACrE;IACDL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC5D,kBAAkB,CAACc,MAAM,CAAC;EAC5E;EAEA1B,cAAcA,CAAC8E,KAAU;IACvB,IAAI,CAAChF,UAAU,GAAGgF,KAAK,CAACC,MAAM,CAACtC,KAAK;IACpC,IAAI,CAACc,wBAAwB,EAAE;EACjC;EAEAjD,iBAAiBA,CAACqE,SAAwB;IACxC,MAAML,UAAU,GAAG,IAAI,CAAChC,kBAAkB,CAACuC,QAAQ,CAACF,SAAS,CAAClE,IAAI,CAAC;IACnE,IAAIuE,YAAsB;IAE1B,IAAIV,UAAU,EAAE;MACdU,YAAY,GAAG,IAAI,CAAC1C,kBAAkB,CAACoC,MAAM,CAACb,CAAC,IAAIA,CAAC,KAAKc,SAAS,CAAClE,IAAI,CAAC;IAC1E,CAAC,MAAM;MACL,IAAI,IAAI,CAACI,aAAa,IAAI,IAAI,CAACyB,kBAAkB,CAACZ,MAAM,IAAI,IAAI,CAACb,aAAa,EAAE;QAC9E,OAAO,CAAC;MACV;MACAmE,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC1C,kBAAkB,EAAEqC,SAAS,CAAClE,IAAI,CAAC;IAC7D;IAEA,IAAI,CAAC6B,kBAAkB,GAAG0C,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA/H,iBAAiBA,CAACgI,aAAqB;IACrC,IAAI,CAAC5C,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACoC,MAAM,CAACb,CAAC,IAAIA,CAAC,KAAKqB,aAAa,CAAC;IAClF,IAAI,CAACD,WAAW,EAAE;EACpB;EAEA9F,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACiC,kBAAkB,CAACc,MAAM,KAAK,CAAC,EAAE;IAEpE,MAAMyD,mBAAmB,GAAG,IAAI,CAACvE,kBAAkB,CAChD8D,MAAM,CAACb,CAAC,IAAI,CAACA,CAAC,CAACnD,UAAU,CAAC,CAC1B0E,GAAG,CAACvB,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC;IAEnB,MAAMuE,YAAY,GAAG,CAAC,GAAG,IAAIK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/C,kBAAkB,EAAE,GAAG6C,mBAAmB,CAAC,CAAC,CAAC;IAEvF,IAAI,IAAI,CAACtE,aAAa,IAAImE,YAAY,CAACtD,MAAM,GAAG,IAAI,CAACb,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACyB,kBAAkB,GAAG0C,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA5F,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B,MAAM2G,kBAAkB,GAAG,IAAI,CAACpD,YAAY,CAAC,IAAI,CAACvD,gBAAgB,CAAC,CAChE+F,MAAM,CAACb,CAAC,IAAI,CAACA,CAAC,CAACnD,UAAU,CAAC,CAC1B0E,GAAG,CAACvB,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC;IAEnB,MAAMuE,YAAY,GAAG,CAAC,GAAG,IAAIK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/C,kBAAkB,EAAE,GAAGgD,kBAAkB,CAAC,CAAC,CAAC;IAEtF,IAAI,IAAI,CAACzE,aAAa,IAAImE,YAAY,CAACtD,MAAM,GAAG,IAAI,CAACb,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACyB,kBAAkB,GAAG0C,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAjG,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAM2G,kBAAkB,GAAG,IAAI,CAACpD,YAAY,CAAC,IAAI,CAACvD,gBAAgB,CAAC,CAACyG,GAAG,CAACvB,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC;IACpF,IAAI,CAAC6B,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACoC,MAAM,CAACb,CAAC,IAAI,CAACyB,kBAAkB,CAACT,QAAQ,CAAChB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACoB,WAAW,EAAE;EACpB;EACA/G,UAAUA,CAAA;IACR,IAAI,CAACoE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC2C,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACrC,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACF,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACI,SAAS,EAAE;IAEhB,MAAM6C,aAAa,GAAG,IAAI,CAACjD,kBAAkB,CAAC8C,GAAG,CAAC3E,IAAI,IAAG;MACvD,KAAK,MAAMiD,QAAQ,IAAI,IAAI,CAACrF,SAAS,EAAE;QACrC,MAAMsF,IAAI,GAAG,IAAI,CAACzB,YAAY,CAACwB,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIkD,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACe,MAAM,CAACf,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAACvB,eAAe,CAACoD,IAAI,CAACD,aAAa,CAAC;EAC1C;EAEAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjI,QAAQ,EAAE;MAClB,IAAI,CAAC6E,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC5B;EACF;EAEAb,aAAaA,CAAA;IACX,IAAI,CAACa,MAAM,GAAG,KAAK;EACrB;EAEA7B,mBAAmBA,CAAC0E,aAAqB;IACvC,OAAO,IAAI,CAAC5C,kBAAkB,CAACuC,QAAQ,CAACK,aAAa,CAAC;EACxD;EAEA3F,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACsB,aAAa,IAAI,IAAI,CAACyB,kBAAkB,CAACZ,MAAM,GAAG,IAAI,CAACb,aAAa;EACnF;EAEA6E,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC/G,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM2G,kBAAkB,GAAG,IAAI,CAACpD,YAAY,CAAC,IAAI,CAACvD,gBAAgB,CAAC,CAChE+F,MAAM,CAACb,CAAC,IAAI,CAACA,CAAC,CAACnD,UAAU,CAAC,CAC1B0E,GAAG,CAACvB,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC;IACnB,OAAO6E,kBAAkB,CAAC5D,MAAM,GAAG,CAAC,IAClC4D,kBAAkB,CAACK,KAAK,CAAClF,IAAI,IAAI,IAAI,CAAC6B,kBAAkB,CAACuC,QAAQ,CAACpE,IAAI,CAAC,CAAC;EAC5E;EAEAjB,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM2G,kBAAkB,GAAG,IAAI,CAACpD,YAAY,CAAC,IAAI,CAACvD,gBAAgB,CAAC,CAACyG,GAAG,CAACvB,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC;IACpF,OAAO6E,kBAAkB,CAACM,IAAI,CAACnF,IAAI,IAAI,IAAI,CAAC6B,kBAAkB,CAACuC,QAAQ,CAACpE,IAAI,CAAC,CAAC;EAChF;EACAoF,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACtD,kBAAkB;EAChC;EAEA1D,gBAAgBA,CAAC6E,QAAgB;IAC/B,OAAO,IAAI,CAACxB,YAAY,CAACwB,QAAQ,CAAC,EAAEhC,MAAM,IAAI,CAAC;EACjD;EAEAtD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACkE,kBAAkB,CAACZ,MAAM;EACvC;EAEA;EACA7D,6BAA6BA,CAAC6F,QAAgB;IAC5C,OAAO,IAAI,CAACnB,kBAAkB,CAACmB,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA3F,mBAAmBA,CAAC2F,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACnB,kBAAkB,CAACmB,QAAQ,CAAC,IAAI,IAAI,CAACnB,kBAAkB,CAACmB,QAAQ,CAAC,CAAChC,MAAM,GAAG,CAAC,CAAC;EAC9F;;;uCApRWI,yBAAyB,EAAAxF,EAAA,CAAAwJ,iBAAA,CAAAxJ,EAAA,CAAAyJ,iBAAA;IAAA;EAAA;;;YAAzBjE,yBAAyB;MAAAkE,SAAA;MAAAC,MAAA;QAAAhE,WAAA;QAAApB,aAAA;QAAArD,QAAA;QAAA0E,YAAA;QAAAC,gBAAA;QAAAR,WAAA;QAAAF,gBAAA;MAAA;MAAAyE,OAAA;QAAA9D,eAAA;MAAA;MAAA+D,QAAA,GAAA7J,EAAA,CAAA8J,kBAAA,CARzB,CACT;QACEC,OAAO,EAAEhK,iBAAiB;QAC1BiK,WAAW,EAAElK,UAAU,CAAC,MAAM0F,yBAAyB,CAAC;QACxDyE,KAAK,EAAE;OACR,CACF,GAAAjK,EAAA,CAAAkK,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBHxK,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAoB,UAAA,IAAAsJ,wCAAA,kBAAgG;UA6B9F1K,EADF,CAAAC,cAAA,aAAgC,gBAED;UAA3BD,EAAA,CAAAG,UAAA,mBAAAwK,2DAAA;YAAA,OAASF,GAAA,CAAAtB,cAAA,EAAgB;UAAA,EAAC;UAC1BnJ,EAAA,CAAAC,cAAA,cAA4B;UAC1BD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAc,YAAA,EAAO;UACPd,EAAA,CAAAa,SAAA,iBACU;UACZb,EAAA,CAAAc,YAAA,EAAS;UAGTd,EAAA,CAAAoB,UAAA,IAAAwJ,wCAAA,kBAA0C;UA6F9C5K,EADE,CAAAc,YAAA,EAAM,EACF;UAGNd,EAAA,CAAAoB,UAAA,IAAAyJ,wCAAA,iBAA+D;;;UAvIvD7K,EAAA,CAAAe,SAAA,EAAuD;UAAvDf,EAAA,CAAAiB,UAAA,SAAAwJ,GAAA,CAAA5E,gBAAA,IAAA4E,GAAA,CAAAzE,kBAAA,CAAAZ,MAAA,KAAuD;UA6BbpF,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAoC,WAAA,aAAAqI,GAAA,CAAAvJ,QAAA,CAA2B;UAAClB,EAAA,CAAAiB,UAAA,aAAAwJ,GAAA,CAAAvJ,QAAA,CAAqB;UAG3FlB,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAgB,kBAAA,MAAAyJ,GAAA,CAAA3I,gBAAA,iCAAA2I,GAAA,CAAA3I,gBAAA,6BAAA2I,GAAA,CAAA9E,WAAA,MACF;UACqC3F,EAAA,CAAAe,SAAA,EAAwB;UAAxBf,EAAA,CAAAoC,WAAA,YAAAqI,GAAA,CAAA1E,MAAA,CAAwB;UAKzD/F,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAwJ,GAAA,CAAA1E,MAAA,CAAY;UAgGhB/F,EAAA,CAAAe,SAAA,EAAY;UAAZf,EAAA,CAAAiB,UAAA,SAAAwJ,GAAA,CAAA1E,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}