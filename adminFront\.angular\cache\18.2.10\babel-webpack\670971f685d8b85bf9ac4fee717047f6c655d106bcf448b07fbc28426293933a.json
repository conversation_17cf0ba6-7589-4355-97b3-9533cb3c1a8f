{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction TemplateViewerComponent_div_11_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"input\", 20);\n    i0.ɵɵlistener(\"change\", function TemplateViewerComponent_div_11_div_14_Template_input_change_1_listener($event) {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      return i0.ɵɵresetView(item_r4.selected = $event.target.checked);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item\", i_r5, \"\");\n    i0.ɵɵproperty(\"checked\", item_r4.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r4.CRequirement, \" (\", item_r4.CGroupName, \") \");\n  }\n}\nfunction TemplateViewerComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"form\", 13);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_11_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createTemplate());\n    });\n    i0.ɵɵelementStart(2, \"div\", 14)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_11_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplateName, $event) || (ctx_r1.newTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_11_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplateDesc, $event) || (ctx_r1.newTemplateDesc = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 14)(11, \"label\");\n    i0.ɵɵtext(12, \"\\u9078\\u64C7\\u8981\\u5B58\\u6210\\u6A21\\u677F\\u7684\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 17);\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_11_div_14_Template, 3, 5, \"div\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 18);\n    i0.ɵɵtext(16, \"\\u5132\\u5B58\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_11_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCreateTemplate = false);\n    });\n    i0.ɵɵtext(18, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplateName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplateDesc);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.sharedData);\n  }\n}\nfunction TemplateViewerComponent_tr_23_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_23_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const tpl_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r7.TemplateID && ctx_r1.onDeleteTemplate(tpl_r7.TemplateID));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_23_Template_button_click_6_listener() {\n      const tpl_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r7));\n    });\n    i0.ɵɵtext(7, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, TemplateViewerComponent_tr_23_button_8_Template, 2, 0, \"button\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r7.TemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r7.Description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", tpl_r7.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 24);\n    i0.ɵɵtext(2, \"\\u66AB\\u7121\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_25_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", detail_r10.FieldName, \": \", detail_r10.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_25_li_4_Template, 2, 2, \"li\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_25_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵtext(6, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 新增模板\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\",\n        templateDetails: \"templateDetails\",\n        sharedData: \"sharedData\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\",\n        saveTemplate: \"saveTemplate\",\n        deleteTemplate: \"deleteTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 26,\n      vars: 4,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [\"class\", \"mb-3 p-2 border rounded bg-light\", 4, \"ngIf\"], [1, \"template-list\"], [1, \"table\", \"table-bordered\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"class\", \"mt-3 p-2 border rounded bg-white\", 4, \"ngIf\"], [1, \"mb-3\", \"p-2\", \"border\", \"rounded\", \"bg-light\"], [3, \"ngSubmit\"], [1, \"form-group\", \"mb-2\"], [\"type\", \"text\", \"name\", \"templateName\", \"required\", \"\", \"maxlength\", \"30\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDesc\", \"maxlength\", \"100\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [2, \"max-height\", \"120px\", \"overflow\", \"auto\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mr-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\", \"name\"], [1, \"btn\", \"btn-info\", \"btn-sm\", \"mr-1\", 3, \"click\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"], [1, \"mt-3\", \"p-2\", \"border\", \"rounded\", \"bg-white\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n          i0.ɵɵtext(3, \"\\u6A21\\u677F\\u6E05\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\")(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.showCreateTemplate = !ctx.showCreateTemplate;\n          });\n          i0.ɵɵelement(6, \"i\", 4);\n          i0.ɵɵtext(7, \"\\u5F9E\\u5171\\u7528\\u8CC7\\u6599\\u5EFA\\u7ACB\\u6A21\\u677F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_8_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(9, \"i\", 4);\n          i0.ɵɵtext(10, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(11, TemplateViewerComponent_div_11_Template, 19, 3, \"div\", 6);\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"table\", 8)(14, \"thead\")(15, \"tr\")(16, \"th\");\n          i0.ɵɵtext(17, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\");\n          i0.ɵɵtext(19, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\");\n          i0.ɵɵtext(21, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"tbody\");\n          i0.ɵɵtemplate(23, TemplateViewerComponent_tr_23_Template, 9, 3, \"tr\", 9)(24, TemplateViewerComponent_tr_24_Template, 3, 0, \"tr\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(25, TemplateViewerComponent_div_25_Template, 7, 2, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCreateTemplate);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.templates || ctx.templates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MaxLengthValidator, i2.NgModel, i2.NgForm],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_div_11_div_14_Template_input_change_1_listener", "$event", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵresetView", "selected", "target", "checked", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵpropertyInterpolate1", "i_r5", "ɵɵproperty", "ɵɵtextInterpolate2", "CRequirement", "CGroupName", "TemplateViewerComponent_div_11_Template_form_ngSubmit_1_listener", "_r1", "ctx_r1", "ɵɵnextContext", "createTemplate", "ɵɵtwoWayListener", "TemplateViewerComponent_div_11_Template_input_ngModelChange_5_listener", "ɵɵtwoWayBindingSet", "newTemplateName", "TemplateViewerComponent_div_11_Template_input_ngModelChange_9_listener", "newTemplateDesc", "ɵɵtemplate", "TemplateViewerComponent_div_11_div_14_Template", "TemplateViewerComponent_div_11_Template_button_click_17_listener", "showCreateTemplate", "ɵɵtwoWayProperty", "sharedData", "TemplateViewerComponent_tr_23_button_8_Template_button_click_0_listener", "_r8", "tpl_r7", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_23_Template_button_click_6_listener", "_r6", "onSelectTemplate", "TemplateViewerComponent_tr_23_button_8_Template", "ɵɵtextInterpolate", "TemplateName", "Description", "detail_r10", "FieldName", "FieldValue", "TemplateViewerComponent_div_25_li_4_Template", "TemplateViewerComponent_div_25_Template_button_click_5_listener", "_r9", "closeTemplateDetail", "ɵɵtextInterpolate1", "selectedTemplate", "currentTemplateDetails", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "searchKeyword", "filteredTemplates", "filter", "x", "length", "alert", "template", "details", "map", "RefID", "ID", "CRequirementID", "ModuleType", "emit", "for<PERSON>ach", "onAddTemplate", "templateID", "confirm", "d", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "ɵɵelement", "TemplateViewerComponent_Template_button_click_8_listener", "TemplateViewerComponent_div_11_Template", "TemplateViewerComponent_tr_23_Template", "TemplateViewerComponent_tr_24_Template", "TemplateViewerComponent_div_25_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n})\r\nexport class TemplateViewerComponent {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({ template, details });\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 新增模板\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header d-flex justify-content-between align-items-center mb-3\">\r\n    <h5 class=\"mb-0\">模板清單</h5>\r\n    <div>\r\n      <button class=\"btn btn-outline-primary btn-sm mr-2\" (click)=\"showCreateTemplate = !showCreateTemplate\">\r\n        <i class=\"fas fa-plus mr-1\"></i>從共用資料建立模板\r\n      </button>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 建立模板區塊 -->\r\n  <div *ngIf=\"showCreateTemplate\" class=\"mb-3 p-2 border rounded bg-light\">\r\n    <form (ngSubmit)=\"createTemplate()\">\r\n      <div class=\"form-group mb-2\">\r\n        <label>模板名稱</label>\r\n        <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplateName\" name=\"templateName\" required\r\n          maxlength=\"30\">\r\n      </div>\r\n      <div class=\"form-group mb-2\">\r\n        <label>模板描述</label>\r\n        <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplateDesc\" name=\"templateDesc\" maxlength=\"100\">\r\n      </div>\r\n      <div class=\"form-group mb-2\">\r\n        <label>選擇要存成模板的資料</label>\r\n        <div style=\"max-height:120px;overflow:auto;\">\r\n          <div *ngFor=\"let item of sharedData; let i = index\">\r\n            <input type=\"checkbox\" [checked]=\"item.selected\" (change)=\"item.selected = $any($event.target).checked\"\r\n              name=\"item{{i}}\"> {{item.CRequirement}}\r\n            ({{item.CGroupName}})\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button class=\"btn btn-primary btn-sm mr-2\" type=\"submit\">儲存模板</button>\r\n      <button class=\"btn btn-secondary btn-sm\" type=\"button\" (click)=\"showCreateTemplate = false\">取消</button>\r\n    </form>\r\n  </div>\r\n\r\n  <div class=\"template-list\">\r\n    <table class=\"table table-bordered table-hover\">\r\n      <thead>\r\n        <tr>\r\n          <th>模板名稱</th>\r\n          <th>描述</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let tpl of templates\">\r\n          <td>{{ tpl.TemplateName }}</td>\r\n          <td>{{ tpl.Description }}</td>\r\n          <td>\r\n            <button class=\"btn btn-info btn-sm mr-1\" (click)=\"onSelectTemplate(tpl)\">查看</button>\r\n            <button class=\"btn btn-danger btn-sm\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n              *ngIf=\"tpl.TemplateID\">刪除</button>\r\n          </td>\r\n        </tr>\r\n        <tr *ngIf=\"!templates || templates.length === 0\">\r\n          <td colspan=\"3\" class=\"text-center\">暫無模板</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n  </div>\r\n\r\n  <!-- 查看模板細節 -->\r\n  <div *ngIf=\"selectedTemplate\" class=\"mt-3 p-2 border rounded bg-white\">\r\n    <h6>模板細節：{{selectedTemplate!.TemplateName}}</h6>\r\n    <ul>\r\n      <li *ngFor=\"let detail of currentTemplateDetails\">\r\n        {{detail.FieldName}}: {{detail.FieldValue}}\r\n      </li>\r\n    </ul>\r\n    <button class=\"btn btn-secondary btn-sm\" (click)=\"closeTemplateDetail()\">關閉</button>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;AACtE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;IC2BhCC,EADF,CAAAC,cAAA,UAAoD,gBAE/B;IAD8BD,EAAA,CAAAE,UAAA,oBAAAC,uEAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAAJ,OAAA,CAAAK,QAAA,GAAAN,MAAA,CAAAO,MAAA,CAAAC,OAAA;IAAA,EAAsD;IAAvGZ,EAAA,CAAAa,YAAA,EACmB;IAACb,EAAA,CAAAc,MAAA,GAEtB;IAAAd,EAAA,CAAAa,YAAA,EAAM;;;;;IAFFb,EAAA,CAAAe,SAAA,EAAgB;IAAhBf,EAAA,CAAAgB,sBAAA,iBAAAC,IAAA,KAAgB;IADKjB,EAAA,CAAAkB,UAAA,YAAAb,OAAA,CAAAK,QAAA,CAAyB;IAC5BV,EAAA,CAAAe,SAAA,EAEtB;IAFsBf,EAAA,CAAAmB,kBAAA,MAAAd,OAAA,CAAAe,YAAA,QAAAf,OAAA,CAAAgB,UAAA,OAEtB;;;;;;IAjBNrB,EADF,CAAAC,cAAA,cAAyE,eACnC;IAA9BD,EAAA,CAAAE,UAAA,sBAAAoB,iEAAA;MAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAS,WAAA,CAAYe,MAAA,CAAAE,cAAA,EAAgB;IAAA,EAAC;IAE/B1B,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAc,MAAA,+BAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IACnBb,EAAA,CAAAC,cAAA,gBACiB;IADuBD,EAAA,CAAA2B,gBAAA,2BAAAC,uEAAAxB,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAAzB,EAAA,CAAA6B,kBAAA,CAAAL,MAAA,CAAAM,eAAA,EAAA1B,MAAA,MAAAoB,MAAA,CAAAM,eAAA,GAAA1B,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA6B;IAEvEJ,EAFE,CAAAa,YAAA,EACiB,EACb;IAEJb,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAc,MAAA,+BAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IACnBb,EAAA,CAAAC,cAAA,gBAA0G;IAAlED,EAAA,CAAA2B,gBAAA,2BAAAI,uEAAA3B,MAAA;MAAAJ,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAAzB,EAAA,CAAA6B,kBAAA,CAAAL,MAAA,CAAAQ,eAAA,EAAA5B,MAAA,MAAAoB,MAAA,CAAAQ,eAAA,GAAA5B,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA6B;IACvEJ,EADE,CAAAa,YAAA,EAA0G,EACtG;IAEJb,EADF,CAAAC,cAAA,eAA6B,aACpB;IAAAD,EAAA,CAAAc,MAAA,oEAAU;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IACzBb,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAiC,UAAA,KAAAC,8CAAA,iBAAoD;IAMxDlC,EADE,CAAAa,YAAA,EAAM,EACF;IACNb,EAAA,CAAAC,cAAA,kBAA0D;IAAAD,EAAA,CAAAc,MAAA,gCAAI;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACvEb,EAAA,CAAAC,cAAA,kBAA4F;IAArCD,EAAA,CAAAE,UAAA,mBAAAiC,iEAAA;MAAAnC,EAAA,CAAAM,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAS,WAAA,CAAAe,MAAA,CAAAY,kBAAA,GAA8B,KAAK;IAAA,EAAC;IAACpC,EAAA,CAAAc,MAAA,oBAAE;IAElGd,EAFkG,CAAAa,YAAA,EAAS,EAClG,EACH;;;;IApBwCb,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAqC,gBAAA,YAAAb,MAAA,CAAAM,eAAA,CAA6B;IAK7B9B,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAqC,gBAAA,YAAAb,MAAA,CAAAQ,eAAA,CAA6B;IAK7ChC,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAkB,UAAA,YAAAM,MAAA,CAAAc,UAAA,CAAe;;;;;;IA2BnCtC,EAAA,CAAAC,cAAA,iBACyB;IADaD,EAAA,CAAAE,UAAA,mBAAAqC,wEAAA;MAAAvC,EAAA,CAAAM,aAAA,CAAAkC,GAAA;MAAA,MAAAC,MAAA,GAAAzC,EAAA,CAAAyB,aAAA,GAAAjB,SAAA;MAAA,MAAAgB,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAS,WAAA,CAAAgC,MAAA,CAAAC,UAAA,IAA2BlB,MAAA,CAAAmB,gBAAA,CAAAF,MAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IACzE1C,EAAA,CAAAc,MAAA,mBAAE;IAAAd,EAAA,CAAAa,YAAA,EAAS;;;;;;IALtCb,EADF,CAAAC,cAAA,SAAkC,SAC5B;IAAAD,EAAA,CAAAc,MAAA,GAAsB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAc,MAAA,GAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAE5Bb,EADF,CAAAC,cAAA,SAAI,iBACuE;IAAhCD,EAAA,CAAAE,UAAA,mBAAA0C,+DAAA;MAAA,MAAAH,MAAA,GAAAzC,EAAA,CAAAM,aAAA,CAAAuC,GAAA,EAAArC,SAAA;MAAA,MAAAgB,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAS,WAAA,CAASe,MAAA,CAAAsB,gBAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IAACzC,EAAA,CAAAc,MAAA,mBAAE;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACpFb,EAAA,CAAAiC,UAAA,IAAAc,+CAAA,qBACyB;IAE7B/C,EADE,CAAAa,YAAA,EAAK,EACF;;;;IAPCb,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAgD,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,CAAsB;IACtBjD,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAgD,iBAAA,CAAAP,MAAA,CAAAS,WAAA,CAAqB;IAIpBlD,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAkB,UAAA,SAAAuB,MAAA,CAAAC,UAAA,CAAoB;;;;;IAIzB1C,EADF,CAAAC,cAAA,SAAiD,aACX;IAAAD,EAAA,CAAAc,MAAA,+BAAI;IAC1Cd,EAD0C,CAAAa,YAAA,EAAK,EAC1C;;;;;IASPb,EAAA,CAAAC,cAAA,SAAkD;IAChDD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;;;;IADHb,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAgC,UAAA,CAAAC,SAAA,QAAAD,UAAA,CAAAE,UAAA,MACF;;;;;;IAJFrD,EADF,CAAAC,cAAA,cAAuE,SACjE;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAChDb,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAiC,UAAA,IAAAqB,4CAAA,gBAAkD;IAGpDtD,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,iBAAyE;IAAhCD,EAAA,CAAAE,UAAA,mBAAAqD,gEAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAAhC,MAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAAS,WAAA,CAASe,MAAA,CAAAiC,mBAAA,EAAqB;IAAA,EAAC;IAACzD,EAAA,CAAAc,MAAA,mBAAE;IAC7Ed,EAD6E,CAAAa,YAAA,EAAS,EAChF;;;;IAPAb,EAAA,CAAAe,SAAA,GAAuC;IAAvCf,EAAA,CAAA0D,kBAAA,mCAAAlC,MAAA,CAAAmC,gBAAA,CAAAV,YAAA,KAAuC;IAElBjD,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAkB,UAAA,YAAAM,MAAA,CAAAoC,sBAAA,CAAyB;;;AD3DtD,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAA1B,UAAU,GAAU,EAAE;IACrB,KAAA2B,WAAW,GAAG,IAAIpE,YAAY,EAAY;IAC1C,KAAAqE,cAAc,GAAG,IAAIrE,YAAY,EAAY;IAC7C,KAAAsE,YAAY,GAAG,IAAItE,YAAY,EAAqD;IACpF,KAAAuE,cAAc,GAAG,IAAIvE,YAAY,EAAU;IAErD,KAAAuC,kBAAkB,GAAG,KAAK;IAC1B,KAAAN,eAAe,GAAG,EAAE;IACpB,KAAAE,eAAe,GAAG,EAAE;IACpB,KAAA2B,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAU,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;;EAElC;EACA5C,cAAcA,CAAA;IACZ,MAAMhB,QAAQ,GAAG,CAAC,IAAI,CAAC4B,UAAU,IAAI,EAAE,EAAEiC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9D,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACoB,eAAe,IAAIpB,QAAQ,CAAC+D,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMC,QAAQ,GAAa;MACzB1B,YAAY,EAAE,IAAI,CAACnB,eAAe;MAClCoB,WAAW,EAAE,IAAI,CAAClB;KACnB;IACD;IACA,MAAM4C,OAAO,GAAqBlE,QAAQ,CAACmE,GAAG,CAACL,CAAC,KAAK;MACnD9B,UAAU,EAAE,CAAC;MAAE;MACfoC,KAAK,EAAEN,CAAC,CAACO,EAAE,IAAIP,CAAC,CAACQ,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAET,CAAC,CAACS,UAAU,IAAI,aAAa;MACzC7B,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAEmB,CAAC,CAACpD;KACf,CAAC,CAAC;IACH,IAAI,CAAC+C,YAAY,CAACe,IAAI,CAAC;MAAEP,QAAQ;MAAEC;IAAO,CAAE,CAAC;IAC7C,IAAI,CAACxC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACN,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACM,UAAU,IAAI,EAAE,EAAE6C,OAAO,CAACX,CAAC,IAAIA,CAAC,CAAC9D,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACA0E,aAAaA,CAAA;IACX,IAAI,CAACnB,WAAW,CAACiB,IAAI,EAAE;EACzB;EAEA;EACApC,gBAAgBA,CAAC6B,QAAkB;IACjC,IAAI,CAAChB,gBAAgB,GAAGgB,QAAQ;IAChC,IAAI,CAACT,cAAc,CAACgB,IAAI,CAACP,QAAQ,CAAC;EACpC;EAEA;EACAhC,gBAAgBA,CAAC0C,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAClB,cAAc,CAACc,IAAI,CAACG,UAAU,CAAC;IACtC;EACF;EAEA;EACA5B,mBAAmBA,CAAA;IACjB,IAAI,CAACE,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACK,eAAe,CAACO,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC7C,UAAU,KAAK,IAAI,CAACiB,gBAAiB,CAACjB,UAAU,CAAC;EAC7F;;;uCAzEWmB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAA2B,SAAA;MAAAC,MAAA;QAAA1B,SAAA;QAAAC,eAAA;QAAA1B,UAAA;MAAA;MAAAoD,OAAA;QAAAzB,WAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GAAA5F,EAAA,CAAA6F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAArB,QAAA,WAAAsB,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCThClG,EAFJ,CAAAC,cAAA,aAAmC,aAC0D,YACxE;UAAAD,EAAA,CAAAc,MAAA,+BAAI;UAAAd,EAAA,CAAAa,YAAA,EAAK;UAExBb,EADF,CAAAC,cAAA,UAAK,gBACoG;UAAnDD,EAAA,CAAAE,UAAA,mBAAAkG,yDAAA;YAAA,OAAAD,GAAA,CAAA/D,kBAAA,IAAA+D,GAAA,CAAA/D,kBAAA;UAAA,EAAkD;UACpGpC,EAAA,CAAAqG,SAAA,WAAgC;UAAArG,EAAA,CAAAc,MAAA,8DAClC;UAAAd,EAAA,CAAAa,YAAA,EAAS;UACTb,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAAoG,yDAAA;YAAA,OAASH,GAAA,CAAAf,aAAA,EAAe;UAAA,EAAC;UAC9DpF,EAAA,CAAAqG,SAAA,WAAgC;UAAArG,EAAA,CAAAc,MAAA,qBAClC;UAEJd,EAFI,CAAAa,YAAA,EAAS,EACL,EACF;UAGNb,EAAA,CAAAiC,UAAA,KAAAsE,uCAAA,kBAAyE;UA8BjEvG,EAJR,CAAAC,cAAA,cAA2B,gBACuB,aACvC,UACD,UACE;UAAAD,EAAA,CAAAc,MAAA,gCAAI;UAAAd,EAAA,CAAAa,YAAA,EAAK;UACbb,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,oBAAE;UAAAd,EAAA,CAAAa,YAAA,EAAK;UACXb,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAc,MAAA,oBAAE;UAEVd,EAFU,CAAAa,YAAA,EAAK,EACR,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UAULD,EATA,CAAAiC,UAAA,KAAAuE,sCAAA,gBAAkC,KAAAC,sCAAA,iBASe;UAKvDzG,EAFI,CAAAa,YAAA,EAAQ,EACF,EACJ;UAGNb,EAAA,CAAAiC,UAAA,KAAAyE,uCAAA,kBAAuE;UASzE1G,EAAA,CAAAa,YAAA,EAAM;;;UA9DEb,EAAA,CAAAe,SAAA,IAAwB;UAAxBf,EAAA,CAAAkB,UAAA,SAAAiF,GAAA,CAAA/D,kBAAA,CAAwB;UAoCJpC,EAAA,CAAAe,SAAA,IAAY;UAAZf,EAAA,CAAAkB,UAAA,YAAAiF,GAAA,CAAApC,SAAA,CAAY;UAS3B/D,EAAA,CAAAe,SAAA,EAA0C;UAA1Cf,EAAA,CAAAkB,UAAA,UAAAiF,GAAA,CAAApC,SAAA,IAAAoC,GAAA,CAAApC,SAAA,CAAAU,MAAA,OAA0C;UAQ/CzE,EAAA,CAAAe,SAAA,EAAsB;UAAtBf,EAAA,CAAAkB,UAAA,SAAAiF,GAAA,CAAAxC,gBAAA,CAAsB;;;qBD1DlB7D,YAAY,EAAA6G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9G,WAAW,EAAA+G,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}