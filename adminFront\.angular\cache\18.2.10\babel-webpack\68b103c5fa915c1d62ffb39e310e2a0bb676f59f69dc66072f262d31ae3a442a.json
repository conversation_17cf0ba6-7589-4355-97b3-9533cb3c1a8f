{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of years to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the years added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport default function addYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addMonths(dirtyDate, amount * 12);\n}", "map": {"version": 3, "names": ["toInteger", "addMonths", "requiredArgs", "addYears", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/addYears/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of years to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the years added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport default function addYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addMonths(dirtyDate, amount * 12);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACvDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGP,SAAS,CAACK,WAAW,CAAC;EACnC,OAAOJ,SAAS,CAACG,SAAS,EAAEG,MAAM,GAAG,EAAE,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}