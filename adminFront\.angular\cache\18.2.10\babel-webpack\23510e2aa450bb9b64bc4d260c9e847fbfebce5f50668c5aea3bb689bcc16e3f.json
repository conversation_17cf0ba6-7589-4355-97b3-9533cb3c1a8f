{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n}\n//# sourceMappingURL=map.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}