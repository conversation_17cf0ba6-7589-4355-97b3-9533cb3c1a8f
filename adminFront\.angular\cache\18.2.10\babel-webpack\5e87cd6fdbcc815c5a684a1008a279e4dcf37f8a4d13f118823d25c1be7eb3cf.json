{"ast": null, "code": "import { BaseComponent } from '../base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../shared.observable\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/helper/allowHelper\";\nexport class BreadcrumbComponent extends BaseComponent {\n  constructor(share, router, allow) {\n    super(allow);\n    this.share = share;\n    this.router = router;\n    this.allow = allow;\n  }\n  ngOnInit() {\n    this.share.SharedMenu.subscribe(res => {\n      if (res.Menu !== undefined) {\n        const subMenu = this.getSubMenu(res, this.router.url);\n        if (subMenu !== undefined) {\n          const masterMenu = res.Menu.find(x => x.CId === subMenu.CParentId);\n          this.masterMenu = masterMenu.CName;\n          this.subMenu = subMenu.CName;\n        }\n      }\n    });\n  }\n  static {\n    this.ɵfac = function BreadcrumbComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BreadcrumbComponent)(i0.ɵɵdirectiveInject(i1.SharedObservable), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AllowHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BreadcrumbComponent,\n      selectors: [[\"ngx-breadcrumb\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 2,\n      template: function BreadcrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtext(0);\n        }\n        if (rf & 2) {\n          i0.ɵɵtextInterpolate2(\"\", ctx.masterMenu, \" / \", ctx.subMenu, \"\\n\");\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJicmVhZGNydW1iLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29tcG9uZW50cy9icmVhZGNydW1iL2JyZWFkY3J1bWIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLHdLQUF3SyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "BreadcrumbComponent", "constructor", "share", "router", "allow", "ngOnInit", "SharedMenu", "subscribe", "res", "<PERSON><PERSON>", "undefined", "subMenu", "getSubMenu", "url", "masterMenu", "find", "x", "CId", "CParentId", "CName", "i0", "ɵɵdirectiveInject", "i1", "SharedObservable", "i2", "Router", "i3", "AllowHelper", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "template", "BreadcrumbComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\components\\breadcrumb\\breadcrumb.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\components\\breadcrumb\\breadcrumb.component.html"], "sourcesContent": ["import { BaseComponent } from '../base/baseComponent';\r\nimport { Component, Input, OnChanges, OnInit } from '@angular/core';\r\nimport { SharedObservable } from '../shared.observable';\r\nimport { Router } from '@angular/router';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\n\r\n@Component({\r\n    selector: 'ngx-breadcrumb',\r\n    templateUrl: './breadcrumb.component.html',\r\n    styleUrls: ['./breadcrumb.component.scss'],\r\n    standalone: true\r\n})\r\nexport class BreadcrumbComponent extends BaseComponent implements OnInit {\r\n\r\n  masterMenu?: string;\r\n  subMenu?: string;\r\n\r\n  constructor(\r\n    private share: SharedObservable,\r\n    private router: Router,\r\n    protected override allow: AllowHelper) {\r\n    super(allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n\r\n    this.share.SharedMenu.subscribe(res => {\r\n      if (res.Menu !== undefined) {\r\n        const subMenu = this.getSubMenu(res, this.router.url);\r\n        if (subMenu !== undefined) {\r\n          const masterMenu = res.Menu!.find(x => x.CId === subMenu.CParentId)!;\r\n          this.masterMenu = masterMenu!.CName!;\r\n          this.subMenu = subMenu.CName!;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n", "{{masterMenu}} / {{subMenu}}\r\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uBAAuB;;;;;AAYrD,OAAM,MAAOC,mBAAoB,SAAQD,aAAa;EAKpDE,YACUC,KAAuB,EACvBC,MAAc,EACHC,KAAkB;IACrC,KAAK,CAACA,KAAK,CAAC;IAHJ,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACK,KAAAC,KAAK,GAALA,KAAK;EAE1B;EAESC,QAAQA,CAAA;IAEf,IAAI,CAACH,KAAK,CAACI,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;MACpC,IAAIA,GAAG,CAACC,IAAI,KAAKC,SAAS,EAAE;QAC1B,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACJ,GAAG,EAAE,IAAI,CAACL,MAAM,CAACU,GAAG,CAAC;QACrD,IAAIF,OAAO,KAAKD,SAAS,EAAE;UACzB,MAAMI,UAAU,GAAGN,GAAG,CAACC,IAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKN,OAAO,CAACO,SAAS,CAAE;UACpE,IAAI,CAACJ,UAAU,GAAGA,UAAW,CAACK,KAAM;UACpC,IAAI,CAACR,OAAO,GAAGA,OAAO,CAACQ,KAAM;QAC/B;MACF;IACF,CAAC,CAAC;EACJ;;;uCAxBWnB,mBAAmB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnB3B,mBAAmB;MAAA4B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAV,EAAA,CAAAW,0BAAA,EAAAX,EAAA,CAAAY,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCjB,EAAA,CAAAmB,MAAA,GACA;;;UADAnB,EAAA,CAAAoB,kBAAA,KAAAF,GAAA,CAAAxB,UAAA,SAAAwB,GAAA,CAAA3B,OAAA,OACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}