{"ast": null, "code": "export class TrafficChartData {}", "map": {"version": 3, "names": ["TrafficChartData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\traffic-chart.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport abstract class TrafficChartData {\r\n  abstract getTrafficChartData(): Observable<number[]>;\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAgBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}