<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]"> 可設定單筆或批次匯入設定各區域及方案對應之建材。
    </h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建案</label>
          <nb-select placeholder="建案" [(ngModel)]="selectedBuildCaseId" class="w-full">
            <nb-option *ngFor="let buildCase of listBuildCases" [value]="buildCase.cID">
              {{ buildCase.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <!-- <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2  w-[22%]">建材類別</label>
          <nb-select placeholder="建材類別" [(ngModel)]="materialOptionsId" class="w-full">
            <nb-option *ngFor="let option of materialOptions" [value]="option.value">
              {{ option.label }}
            </nb-option>
          </nb-select>
        </div>
      </div> -->
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材選項名稱 </label>
          <input type="text" nbInput placeholder="建材選項名稱" [(ngModel)]="CSelectName" class="w-full">
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">建材圖片檔名
          </label>
          <input type="text" nbInput placeholder="建材圖片檔名" [(ngModel)]="CImageCode" class="w-full">
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label mr-2 w-[22%]">示意圖片檔名
          </label>
          <input type="text" nbInput placeholder="示意圖片檔名" [(ngModel)]="CInfoImageCode" class="w-full">
        </div>
      </div>
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full">
          <nb-checkbox status="basic" class="flex" style="flex:auto" [(checked)]="filterMapping" (change)="changeFilter()">
            只顯示缺少建材圖片或示意圖片的建材
          </nb-checkbox>
          <button *ngIf="isExcelExport" class="btn btn-success mr-2" (click)="exportExelMaterialList()">匯出 <i
            class="fas fa-file-download"></i></button>
          <button *ngIf="isRead" class="btn btn-info mr-2 text-white ml-2" (click)="search()">
            查詢 <i class="fas fa-search"></i></button>
          <button *ngIf="isCreate" class="btn btn-info mx-1 ml-2 mr-2" (click)="addNew(dialog)">單筆新增 <i
              class="fas fa-plus"></i></button>
          <button class="btn btn-info mx-1" *ngIf="isExcelImport" (click)="inputFile.click()"> 批次匯入 </button>
          <input class="hidden" type="file" accept=".xls, .xlsx" #inputFile (change)="detectFileExcel($event)">
          <button class="btn btn-success ml-2" (click)="exportExelMaterialTemplate()">下載範例檔案 <i
              class="fas fa-file-download"></i></button>
        </div>
      </div>
    </div>
    <div class="table-responsive mt-4">
      <table class="table table-striped border " style="min-width: 1200px; background-color:#f3f3f3;">
        <thead>
          <tr style="background-color: #27ae60; color: white;">
            <th scope="col" class="col-1">項次</th>
            <!-- <th scope="col" class="col-1">建材類別</th> -->
            <th scope="col" class="col-1">名稱</th>
            <th scope="col" class="col-1">項目</th>
            <th scope="col" class="col-1">位置</th>
            <th scope="col" class="col-1">建材選項名稱</th>
            <th scope="col" class="col-1">建材圖片檔名（相同建案不可重複）</th>
            <th scope="col" class="col-1">示意圖片檔名（相同建案不可重複）</th>
            <th scope="col" class="col-3">建材說明</th>
            <th scope="col" class="col-1" *ngIf="ShowPrice == true">價格</th>
            <th scope="col" class="col-1 text-center">操作</th>
          </tr>
        </thead>
        <tbody *ngIf="materialList != null && materialList.length > 0">
          <tr *ngFor="let item of materialList ; let i = index">
            <td>{{ item.CId}}</td>
            <!-- <td>{{ item.CPlanUse! | getPlanUse}}</td> -->
            <td>{{ item.CName}}</td>
            <td>{{ item.CPart}}</td>
            <td>{{ item.CLocation}}</td>
            <td [style]="!item.CIsMapping ? 'color: red' : ''">{{ item.CSelectName}}</td>
            <td>
              <div class="flex justify-between">
                <span style="overflow-wrap: break-word; width: 80%"
                  [class]="item.CPicture == null ? 'empty-image' : ''">
                  {{ item.CImageCode }}
                </span>
                <span class="width-[50px]" *ngIf="item.CPicture">
                  <img [src]="item.CPicture" class="image-table" (click)="showImage(item.CPicture, dialogImage)" />
                </span>
              </div>
            </td>
            <td>
              <div class="flex justify-between">
                <span style="overflow-wrap: break-word; width: 80%;"
                  [class]="item.CInfoPicture == null ? 'empty-image' : ''">
                  {{ item.CInfoImageCode }}
                </span>
                <span class="width-[50px]" *ngIf="item.CInfoPicture">
                  <img [src]="item.CInfoPicture" class="image-table" (click)="showImage(item.CInfoPicture, dialogImage)" />
                </span>
              </div>
            </td>
            <td>{{ item.CDescription}}</td>
            <td *ngIf="item.CShowPrice == true">{{ item.CPrice}}</td>
            <td class="text-center w-32">
              <button class="btn btn-outline-primary btn-sm m-1" (click)="onSelectedMaterial(item, dialog)"
                *ngIf="isRead">編輯</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(CollectionSize)]="totalRecords" [(PageSize)]="pageSize" [(Page)]="pageIndex"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<ng-template #dialog let-dialog let-ref="dialogRef">
  <nb-card class="w-[700px]">
    <nb-card-header>
      建材管理 > 新增建材
    </nb-card-header>
    <nb-card-body class="px-4">
      <h5 class="text-base">請輸入下方內容新增建材，請留意建材圖片編號不可與該建案內其他編號重複。</h5>
      <div class="w-full mt-3">
        <div class="flex items-center">
          <label class="required-field w-[150px]">名稱</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CName" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">項目</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CPart" />
        </div>
        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">位置</label>
          <input type="text" class="w-full !max-w-full p-2 rounded text-[13px]" nbInput maxlength="30"
            [(ngModel)]="selectedMaterial.CLocation" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">建材選項名稱</label>
          <input type="text" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" maxlength="30"
            [(ngModel)]="selectedMaterial.CSelectName" />
        </div>

        <div class="flex items-center mt-3">
          <label class="required-field w-[150px]">建材圖片檔名</label>
          <input type="text" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" maxlength="30"
            [(ngModel)]="selectedMaterial.CImageCode" />
        </div>

        <div class="flex items-center mt-3">
          <label class="w-[150px]">建材說明</label>
          <textarea nbInput [(ngModel)]="selectedMaterial.CDescription" [rows]="4"
            class="resize-none w-full !max-w-full p-2 rounded text-[13px]"></textarea>
        </div>

        <div class="flex items-center mt-3">
          <label class="w-[150px]">價格</label>
          <input type="number" nbInput class="w-full !max-w-full p-2 rounded text-[13px]" maxlength="30"
            [(ngModel)]="selectedMaterial.CPrice" />
        </div>
      </div>
    </nb-card-body>
    <nb-card-footer class="d-flex justify-content-center">
      <button class="btn btn-danger btn-sm mr-4" (click)="onClose(ref)">關閉</button>
      <button class="btn btn-success btn-sm" (click)="onSubmit(ref)">儲存</button>
    </nb-card-footer>
  </nb-card>
</ng-template>

<ng-template #dialogImage let-data let-ref="dialogRef">
  <nb-card style="height: 100%; overflow: auto; width: 700px;" class="mr-md-5 ml-md-5">
    <nb-card-header>
      <span>
        檢視
      </span>
    </nb-card-header>
    <nb-card-body style="padding:1rem 2rem">
      <div class="w-full h-auto">
        <img class="fit-size" [src]="currentImageShowing">
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="flex justify-center items-center">
        <button class="btn btn-danger mr-2" (click)="ref.close()">取消</button>
      </div>
    </nb-card-footer>
  </nb-card>
</ng-template>
