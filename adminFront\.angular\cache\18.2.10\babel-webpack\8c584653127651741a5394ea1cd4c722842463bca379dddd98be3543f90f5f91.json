{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nlet SampleSelectionResultComponent = class SampleSelectionResultComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _finalDocumentService, message, route, location, _eventService, _houseService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._finalDocumentService = _finalDocumentService;\n    this.message = message;\n    this.route = route;\n    this.location = location;\n    this._eventService = _eventService;\n    this._houseService = _houseService;\n    this.documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回'];\n    this.isChecked = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        this.buildCaseId = +(params.get('id1') ?? 0);\n        this.houseID = +(params.get('id2') ?? 0);\n        if (this.houseID) {\n          this.getHouseById();\n        }\n        this.getListFinalDoc();\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseByID = res.Entries;\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFile) window.open(data.CFile, '_blank');\n  }\n  getListFinalDoc() {\n    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\n      body: {\n        CHouseID: this.houseID,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listFinalDoc = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getListSpecialChangeAvailable() {\n    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\n      body: {\n        CHouseID: this.houseID\n      }\n    }).subscribe(res => {\n      this.listSpecialChangeAvailable = [];\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChangeAvailable = res.Entries ?? [];\n        if (res.Entries.length) {\n          this.listSpecialChangeAvailable = res.Entries.map(e => {\n            return {\n              ...e,\n              isChecked: false\n            };\n          });\n        }\n      }\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName);\n    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark);\n    this.valid.required('[系統操作說明]', this.finalDoc.CNote);\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  getCheckedCIDs(changeArray) {\n    if (changeArray && changeArray.length) {\n      return changeArray.filter(change => change.isChecked).map(change => change.CID);\n    }\n    return [];\n  }\n  onCreateFinalDoc(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      ...this.finalDoc,\n      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\n    };\n    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\n      body: param\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.getListFinalDoc();\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFinalDoc();\n  }\n  addNew(ref) {\n    this.finalDoc = {\n      CHouseID: this.houseID,\n      CDocumentName: '',\n      CApproveRemark: '',\n      CNote: \"\"\n    };\n    this.getListSpecialChangeAvailable();\n    this.dialogService.open(ref);\n  }\n  onOpenModel(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  formatDate(date) {\n    return moment(date).format('YYYY/MM/DD HH:mm');\n  }\n};\nSampleSelectionResultComponent = __decorate([Component({\n  selector: 'ngx-sample-selection-result',\n  templateUrl: './sample-selection-result.component.html',\n  styleUrls: ['./sample-selection-result.component.scss']\n})], SampleSelectionResultComponent);\nexport { SampleSelectionResultComponent };", "map": {"version": 3, "names": ["Component", "BaseComponent", "moment", "SampleSelectionResultComponent", "constructor", "_allow", "dialogService", "valid", "_finalDocumentService", "message", "route", "location", "_eventService", "_houseService", "documentStatusOptions", "isChecked", "ngOnInit", "paramMap", "subscribe", "params", "buildCaseId", "get", "houseID", "getHouseById", "getListFinalDoc", "apiHouseGetHouseByIdPost$Json", "body", "CHouseID", "res", "Entries", "StatusCode", "houseByID", "openPdfInNewTab", "data", "CFile", "window", "open", "apiFinalDocumentGetListFinalDocPost$Json", "PageIndex", "pageIndex", "PageSize", "pageSize", "TotalItems", "listFinalDoc", "totalRecords", "getListSpecialChangeAvailable", "apiFinalDocumentGetListSpecialChangeAvailablePost$Json", "listSpecialChangeAvailable", "length", "map", "e", "validation", "clear", "required", "finalDoc", "CDocumentName", "CApproveRemark", "CNote", "goBack", "push", "action", "payload", "back", "getCheckedCIDs", "changeArray", "filter", "change", "CID", "onCreateFinalDoc", "ref", "errorMessages", "showErrorMSGs", "param", "CSpecialChange", "apiFinalDocumentCreateFinalDocPost$Json", "showSucessMSG", "close", "showErrorMSG", "Message", "pageChanged", "newPage", "addNew", "onOpenModel", "onClose", "formatDate", "date", "format", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\sample-selection-result\\sample-selection-result.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService, HouseService } from 'src/services/api/services';\r\nimport { CreateFinalDocArgs, GetListFinalDocRes, TblHouse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Location } from '@angular/common';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport * as moment from 'moment';\r\n\r\n@Component({\r\n  selector: 'ngx-sample-selection-result',\r\n  templateUrl: './sample-selection-result.component.html',\r\n  styleUrls: ['./sample-selection-result.component.scss'],\r\n})\r\n\r\nexport class SampleSelectionResultComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _finalDocumentService: FinalDocumentService,\r\n    private message: MessageService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private _eventService: EventService,\r\n    private _houseService: HouseService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  houseID: any\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        this.buildCaseId = +(params.get('id1') ?? 0);\r\n        this.houseID = +(params.get('id2') ?? 0);\r\n        if(this.houseID) {\r\n          this.getHouseById()\r\n        }\r\n        this.getListFinalDoc()\r\n      }\r\n    });\r\n  }\r\n\r\n  houseByID: TblHouse\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: this.houseID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseByID = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  documentStatusOptions = ['待審核', '已駁回', '待客戶簽回', '客戶已簽回']\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFile) window.open(data.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listFinalDoc: GetListFinalDocRes[]\r\n\r\n\r\n  getListFinalDoc() {\r\n    this._finalDocumentService.apiFinalDocumentGetListFinalDocPost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listFinalDoc = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  listSpecialChangeAvailable: any[]\r\n  getListSpecialChangeAvailable() {\r\n    this._finalDocumentService.apiFinalDocumentGetListSpecialChangeAvailablePost$Json({\r\n      body: {\r\n        CHouseID: this.houseID,\r\n      }\r\n    }).subscribe(res => {\r\n      this.listSpecialChangeAvailable = []\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChangeAvailable = res.Entries! ?? []\r\n        if (res.Entries.length) {\r\n          this.listSpecialChangeAvailable = res.Entries.map((e: any) => {\r\n            return { ...e, isChecked: false }\r\n          })\r\n        }\r\n      }\r\n    })\r\n  }\r\n  finalDoc: CreateFinalDocArgs\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[文件名稱]', this.finalDoc.CDocumentName)\r\n    this.valid.required('[送審資訊]', this.finalDoc.CApproveRemark)\r\n    this.valid.required('[系統操作說明]', this.finalDoc.CNote)\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  getCheckedCIDs(changeArray: any[]) {\r\n    if (changeArray && changeArray.length) {\r\n      return changeArray.filter(change => change.isChecked).map(change => change.CID);\r\n    } return []\r\n  }\r\n\r\n  onCreateFinalDoc(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      ...this.finalDoc,\r\n      CSpecialChange: this.getCheckedCIDs(this.listSpecialChangeAvailable)\r\n    }\r\n    this._finalDocumentService.apiFinalDocumentCreateFinalDocPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.getListFinalDoc();\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    })\r\n  }\r\n\r\n  isChecked = true\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFinalDoc();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.finalDoc = {\r\n      CHouseID: this.houseID,\r\n      CDocumentName: '',\r\n      CApproveRemark: '',\r\n      CNote:\"\"\r\n    }\r\n    this.getListSpecialChangeAvailable()\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onOpenModel(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  formatDate(date:string){\r\n    return moment(date).format('YYYY/MM/DD HH:mm');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAQjD,SAASC,aAAa,QAAQ,qCAAqC;AAGnE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAQzB,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQF,aAAa;EAC/DG,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,OAAuB,EACvBC,KAAqB,EACrBC,QAAkB,EAClBC,aAA2B,EAC3BC,aAA2B;IAEnC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAiCvB,KAAAC,qBAAqB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;IA0FxD,KAAAC,SAAS,GAAG,IAAI;EAxHhB;EAKSC,QAAQA,CAAA;IACf,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,IAAI,CAACC,WAAW,GAAG,EAAED,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,CAACC,OAAO,GAAG,EAAEH,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxC,IAAG,IAAI,CAACC,OAAO,EAAE;UACf,IAAI,CAACC,YAAY,EAAE;QACrB;QACA,IAAI,CAACC,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAIAD,YAAYA,CAAA;IACV,IAAI,CAACV,aAAa,CAACY,6BAA6B,CAAC;MAC/CC,IAAI,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAACL;MAAO;KAC/B,CAAC,CAACJ,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,SAAS,GAAGH,GAAG,CAACC,OAAO;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAG,eAAeA,CAACC,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAACC,KAAK,EAAEC,MAAM,CAACC,IAAI,CAACH,IAAI,CAACC,KAAK,EAAE,QAAQ,CAAC;EAC3D;EAMAV,eAAeA,CAAA;IACb,IAAI,CAAChB,qBAAqB,CAAC6B,wCAAwC,CAAC;MAClEX,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACL,OAAO;QACtBgB,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAACC;;KAElB,CAAC,CAACvB,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACc,UAAU,IAAId,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACa,YAAY,GAAGf,GAAG,CAACC,OAAQ,IAAI,EAAE;QACtC,IAAI,CAACe,YAAY,GAAGhB,GAAG,CAACc,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAIAG,6BAA6BA,CAAA;IAC3B,IAAI,CAACrC,qBAAqB,CAACsC,sDAAsD,CAAC;MAChFpB,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACL;;KAElB,CAAC,CAACJ,SAAS,CAACU,GAAG,IAAG;MACjB,IAAI,CAACmB,0BAA0B,GAAG,EAAE;MACpC,IAAInB,GAAG,CAACc,UAAU,IAAId,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACiB,0BAA0B,GAAGnB,GAAG,CAACC,OAAQ,IAAI,EAAE;QACpD,IAAID,GAAG,CAACC,OAAO,CAACmB,MAAM,EAAE;UACtB,IAAI,CAACD,0BAA0B,GAAGnB,GAAG,CAACC,OAAO,CAACoB,GAAG,CAAEC,CAAM,IAAI;YAC3D,OAAO;cAAE,GAAGA,CAAC;cAAEnC,SAAS,EAAE;YAAK,CAAE;UACnC,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;EACJ;EAGAoC,UAAUA,CAAA;IACR,IAAI,CAAC5C,KAAK,CAAC6C,KAAK,EAAE;IAClB,IAAI,CAAC7C,KAAK,CAAC8C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC;IAC1D,IAAI,CAAChD,KAAK,CAAC8C,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAACE,cAAc,CAAC;IAC3D,IAAI,CAACjD,KAAK,CAAC8C,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACC,QAAQ,CAACG,KAAK,CAAC;EACtD;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC9C,aAAa,CAAC+C,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAACzC;KACf,CAAC;IACF,IAAI,CAACT,QAAQ,CAACmD,IAAI,EAAE;EACtB;EAEAC,cAAcA,CAACC,WAAkB;IAC/B,IAAIA,WAAW,IAAIA,WAAW,CAAChB,MAAM,EAAE;MACrC,OAAOgB,WAAW,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACnD,SAAS,CAAC,CAACkC,GAAG,CAACiB,MAAM,IAAIA,MAAM,CAACC,GAAG,CAAC;IACjF;IAAE,OAAO,EAAE;EACb;EAEAC,gBAAgBA,CAACC,GAAQ;IACvB,IAAI,CAAClB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5C,KAAK,CAAC+D,aAAa,CAACtB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACvC,OAAO,CAAC8D,aAAa,CAAC,IAAI,CAAChE,KAAK,CAAC+D,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZ,GAAG,IAAI,CAAClB,QAAQ;MAChBmB,cAAc,EAAE,IAAI,CAACV,cAAc,CAAC,IAAI,CAAChB,0BAA0B;KACpE;IACD,IAAI,CAACvC,qBAAqB,CAACkE,uCAAuC,CAAC;MACjEhD,IAAI,EAAE8C;KACP,CAAC,CAACtD,SAAS,CAACU,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACN,eAAe,EAAE;QACtB,IAAI,CAACf,OAAO,CAACkE,aAAa,CAAC,MAAM,CAAC;QAClCN,GAAG,CAACO,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACnE,OAAO,CAACoE,YAAY,CAACjD,GAAG,CAACkD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;EAGAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACzC,SAAS,GAAGyC,OAAO;IACxB,IAAI,CAACxD,eAAe,EAAE;EACxB;EAGAyD,MAAMA,CAACZ,GAAQ;IACb,IAAI,CAACf,QAAQ,GAAG;MACd3B,QAAQ,EAAE,IAAI,CAACL,OAAO;MACtBiC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAC;KACP;IACD,IAAI,CAACZ,6BAA6B,EAAE;IACpC,IAAI,CAACvC,aAAa,CAAC8B,IAAI,CAACiC,GAAG,CAAC;EAC9B;EAEAa,WAAWA,CAACb,GAAQ;IAClB,IAAI,CAAC/D,aAAa,CAAC8B,IAAI,CAACiC,GAAG,CAAC;EAC9B;EAEAc,OAAOA,CAACd,GAAQ;IACdA,GAAG,CAACO,KAAK,EAAE;EACb;EAEAQ,UAAUA,CAACC,IAAW;IACpB,OAAOnF,MAAM,CAACmF,IAAI,CAAC,CAACC,MAAM,CAAC,kBAAkB,CAAC;EAChD;CACD;AAlKYnF,8BAA8B,GAAAoF,UAAA,EAN1CvF,SAAS,CAAC;EACTwF,QAAQ,EAAE,6BAA6B;EACvCC,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EAEWvF,8BAA8B,CAkK1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}