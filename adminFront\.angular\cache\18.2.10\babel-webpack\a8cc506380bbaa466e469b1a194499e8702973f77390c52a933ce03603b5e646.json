{"ast": null, "code": "import isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isSaturday from \"../isSaturday/index.js\";\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport default function addBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var startedOnWeekend = isWeekend(date);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) return new Date(NaN);\n  var hours = date.getHours();\n  var sign = amount < 0 ? -1 : 1;\n  var fullWeeks = toInteger(amount / 5);\n  date.setDate(date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  var restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    date.setDate(date.getDate() + sign);\n    if (!isWeekend(date)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(date) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(date)) date.setDate(date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(date)) date.setDate(date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  date.setHours(hours);\n  return date;\n}", "map": {"version": 3, "names": ["isWeekend", "toDate", "toInteger", "requiredArgs", "is<PERSON><PERSON><PERSON>", "isSaturday", "addBusinessDays", "dirtyDate", "dirtyAmount", "arguments", "date", "startedOnWeekend", "amount", "isNaN", "Date", "NaN", "hours", "getHours", "sign", "fullWeeks", "setDate", "getDate", "restDays", "Math", "abs", "setHours"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/addBusinessDays/index.js"], "sourcesContent": ["import isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isSaturday from \"../isSaturday/index.js\";\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport default function addBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var startedOnWeekend = isWeekend(date);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) return new Date(NaN);\n  var hours = date.getHours();\n  var sign = amount < 0 ? -1 : 1;\n  var fullWeeks = toInteger(amount / 5);\n  date.setDate(date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  var restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    date.setDate(date.getDate() + sign);\n    if (!isWeekend(date)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(date) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(date)) date.setDate(date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(date)) date.setDate(date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  date.setHours(hours);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC9DL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGT,MAAM,CAACM,SAAS,CAAC;EAC5B,IAAII,gBAAgB,GAAGX,SAAS,CAACU,IAAI,CAAC;EACtC,IAAIE,MAAM,GAAGV,SAAS,CAACM,WAAW,CAAC;EACnC,IAAIK,KAAK,CAACD,MAAM,CAAC,EAAE,OAAO,IAAIE,IAAI,CAACC,GAAG,CAAC;EACvC,IAAIC,KAAK,GAAGN,IAAI,CAACO,QAAQ,CAAC,CAAC;EAC3B,IAAIC,IAAI,GAAGN,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC9B,IAAIO,SAAS,GAAGjB,SAAS,CAACU,MAAM,GAAG,CAAC,CAAC;EACrCF,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,OAAO,CAAC,CAAC,GAAGF,SAAS,GAAG,CAAC,CAAC;;EAE5C;EACA,IAAIG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACZ,MAAM,GAAG,CAAC,CAAC;;EAEnC;EACA,OAAOU,QAAQ,GAAG,CAAC,EAAE;IACnBZ,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAC;IACnC,IAAI,CAAClB,SAAS,CAACU,IAAI,CAAC,EAAEY,QAAQ,IAAI,CAAC;EACrC;;EAEA;EACA;EACA;EACA,IAAIX,gBAAgB,IAAIX,SAAS,CAACU,IAAI,CAAC,IAAIE,MAAM,KAAK,CAAC,EAAE;IACvD;IACA;IACA,IAAIP,UAAU,CAACK,IAAI,CAAC,EAAEA,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,OAAO,CAAC,CAAC,IAAIH,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxE,IAAId,QAAQ,CAACM,IAAI,CAAC,EAAEA,IAAI,CAACU,OAAO,CAACV,IAAI,CAACW,OAAO,CAAC,CAAC,IAAIH,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxE;;EAEA;EACAR,IAAI,CAACe,QAAQ,CAACT,KAAK,CAAC;EACpB,OAAON,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}