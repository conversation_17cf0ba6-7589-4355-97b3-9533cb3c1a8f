{"ast": null, "code": "var n,\n  l,\n  u,\n  i,\n  t,\n  r,\n  o,\n  f,\n  e,\n  c = {},\n  s = [],\n  a = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\nfunction h(n, l) {\n  for (var u in l) n[u] = l[u];\n  return n;\n}\nfunction v(n) {\n  var l = n.parentNode;\n  l && l.removeChild(n);\n}\nfunction y(l, u, i) {\n  var t,\n    r,\n    o,\n    f = {};\n  for (o in u) \"key\" == o ? t = u[o] : \"ref\" == o ? r = u[o] : f[o] = u[o];\n  if (arguments.length > 2 && (f.children = arguments.length > 3 ? n.call(arguments, 2) : i), \"function\" == typeof l && null != l.defaultProps) for (o in l.defaultProps) void 0 === f[o] && (f[o] = l.defaultProps[o]);\n  return p(l, f, t, r, null);\n}\nfunction p(n, i, t, r, o) {\n  var f = {\n    type: n,\n    props: i,\n    key: t,\n    ref: r,\n    __k: null,\n    __: null,\n    __b: 0,\n    __e: null,\n    __d: void 0,\n    __c: null,\n    __h: null,\n    constructor: void 0,\n    __v: null == o ? ++u : o\n  };\n  return null == o && null != l.vnode && l.vnode(f), f;\n}\nfunction d() {\n  return {\n    current: null\n  };\n}\nfunction _(n) {\n  return n.children;\n}\nfunction k(n, l, u, i, t) {\n  var r;\n  for (r in u) \"children\" === r || \"key\" === r || r in l || g(n, r, null, u[r], i);\n  for (r in l) t && \"function\" != typeof l[r] || \"children\" === r || \"key\" === r || \"value\" === r || \"checked\" === r || u[r] === l[r] || g(n, r, l[r], u[r], i);\n}\nfunction b(n, l, u) {\n  \"-\" === l[0] ? n.setProperty(l, null == u ? \"\" : u) : n[l] = null == u ? \"\" : \"number\" != typeof u || a.test(l) ? u : u + \"px\";\n}\nfunction g(n, l, u, i, t) {\n  var r;\n  n: if (\"style\" === l) {\n    if (\"string\" == typeof u) n.style.cssText = u;else {\n      if (\"string\" == typeof i && (n.style.cssText = i = \"\"), i) for (l in i) u && l in u || b(n.style, l, \"\");\n      if (u) for (l in u) i && u[l] === i[l] || b(n.style, l, u[l]);\n    }\n  } else if (\"o\" === l[0] && \"n\" === l[1]) r = l !== (l = l.replace(/Capture$/, \"\")), l = l.toLowerCase() in n ? l.toLowerCase().slice(2) : l.slice(2), n.l || (n.l = {}), n.l[l + r] = u, u ? i || n.addEventListener(l, r ? w : m, r) : n.removeEventListener(l, r ? w : m, r);else if (\"dangerouslySetInnerHTML\" !== l) {\n    if (t) l = l.replace(/xlink(H|:h)/, \"h\").replace(/sName$/, \"s\");else if (\"width\" !== l && \"height\" !== l && \"href\" !== l && \"list\" !== l && \"form\" !== l && \"tabIndex\" !== l && \"download\" !== l && l in n) try {\n      n[l] = null == u ? \"\" : u;\n      break n;\n    } catch (n) {}\n    \"function\" == typeof u || (null == u || !1 === u && -1 == l.indexOf(\"-\") ? n.removeAttribute(l) : n.setAttribute(l, u));\n  }\n}\nfunction m(n) {\n  t = !0;\n  try {\n    return this.l[n.type + !1](l.event ? l.event(n) : n);\n  } finally {\n    t = !1;\n  }\n}\nfunction w(n) {\n  t = !0;\n  try {\n    return this.l[n.type + !0](l.event ? l.event(n) : n);\n  } finally {\n    t = !1;\n  }\n}\nfunction x(n, l) {\n  this.props = n, this.context = l;\n}\nfunction A(n, l) {\n  if (null == l) return n.__ ? A(n.__, n.__.__k.indexOf(n) + 1) : null;\n  for (var u; l < n.__k.length; l++) if (null != (u = n.__k[l]) && null != u.__e) return u.__e;\n  return \"function\" == typeof n.type ? A(n) : null;\n}\nfunction P(n) {\n  var l, u;\n  if (null != (n = n.__) && null != n.__c) {\n    for (n.__e = n.__c.base = null, l = 0; l < n.__k.length; l++) if (null != (u = n.__k[l]) && null != u.__e) {\n      n.__e = n.__c.base = u.__e;\n      break;\n    }\n    return P(n);\n  }\n}\nfunction C(n) {\n  t ? setTimeout(n) : f(n);\n}\nfunction T(n) {\n  (!n.__d && (n.__d = !0) && r.push(n) && !$.__r++ || o !== l.debounceRendering) && ((o = l.debounceRendering) || C)($);\n}\nfunction $() {\n  var n, l, u, i, t, o, f, e;\n  for (r.sort(function (n, l) {\n    return n.__v.__b - l.__v.__b;\n  }); n = r.shift();) n.__d && (l = r.length, i = void 0, t = void 0, f = (o = (u = n).__v).__e, (e = u.__P) && (i = [], (t = h({}, o)).__v = o.__v + 1, M(e, o, t, u.__n, void 0 !== e.ownerSVGElement, null != o.__h ? [f] : null, i, null == f ? A(o) : f, o.__h), N(i, o), o.__e != f && P(o)), r.length > l && r.sort(function (n, l) {\n    return n.__v.__b - l.__v.__b;\n  }));\n  $.__r = 0;\n}\nfunction H(n, l, u, i, t, r, o, f, e, a) {\n  var h,\n    v,\n    y,\n    d,\n    k,\n    b,\n    g,\n    m = i && i.__k || s,\n    w = m.length;\n  for (u.__k = [], h = 0; h < l.length; h++) if (null != (d = u.__k[h] = null == (d = l[h]) || \"boolean\" == typeof d ? null : \"string\" == typeof d || \"number\" == typeof d || \"bigint\" == typeof d ? p(null, d, null, null, d) : Array.isArray(d) ? p(_, {\n    children: d\n  }, null, null, null) : d.__b > 0 ? p(d.type, d.props, d.key, d.ref ? d.ref : null, d.__v) : d)) {\n    if (d.__ = u, d.__b = u.__b + 1, null === (y = m[h]) || y && d.key == y.key && d.type === y.type) m[h] = void 0;else for (v = 0; v < w; v++) {\n      if ((y = m[v]) && d.key == y.key && d.type === y.type) {\n        m[v] = void 0;\n        break;\n      }\n      y = null;\n    }\n    M(n, d, y = y || c, t, r, o, f, e, a), k = d.__e, (v = d.ref) && y.ref != v && (g || (g = []), y.ref && g.push(y.ref, null, d), g.push(v, d.__c || k, d)), null != k ? (null == b && (b = k), \"function\" == typeof d.type && d.__k === y.__k ? d.__d = e = I(d, e, n) : e = z(n, d, y, m, k, e), \"function\" == typeof u.type && (u.__d = e)) : e && y.__e == e && e.parentNode != n && (e = A(y));\n  }\n  for (u.__e = b, h = w; h--;) null != m[h] && (\"function\" == typeof u.type && null != m[h].__e && m[h].__e == u.__d && (u.__d = L(i).nextSibling), q(m[h], m[h]));\n  if (g) for (h = 0; h < g.length; h++) S(g[h], g[++h], g[++h]);\n}\nfunction I(n, l, u) {\n  for (var i, t = n.__k, r = 0; t && r < t.length; r++) (i = t[r]) && (i.__ = n, l = \"function\" == typeof i.type ? I(i, l, u) : z(u, i, i, t, i.__e, l));\n  return l;\n}\nfunction j(n, l) {\n  return l = l || [], null == n || \"boolean\" == typeof n || (Array.isArray(n) ? n.some(function (n) {\n    j(n, l);\n  }) : l.push(n)), l;\n}\nfunction z(n, l, u, i, t, r) {\n  var o, f, e;\n  if (void 0 !== l.__d) o = l.__d, l.__d = void 0;else if (null == u || t != r || null == t.parentNode) n: if (null == r || r.parentNode !== n) n.appendChild(t), o = null;else {\n    for (f = r, e = 0; (f = f.nextSibling) && e < i.length; e += 1) if (f == t) break n;\n    n.insertBefore(t, r), o = r;\n  }\n  return void 0 !== o ? o : t.nextSibling;\n}\nfunction L(n) {\n  var l, u, i;\n  if (null == n.type || \"string\" == typeof n.type) return n.__e;\n  if (n.__k) for (l = n.__k.length - 1; l >= 0; l--) if ((u = n.__k[l]) && (i = L(u))) return i;\n  return null;\n}\nfunction M(n, u, i, t, r, o, f, e, c) {\n  var s,\n    a,\n    v,\n    y,\n    p,\n    d,\n    k,\n    b,\n    g,\n    m,\n    w,\n    A,\n    P,\n    C,\n    T,\n    $ = u.type;\n  if (void 0 !== u.constructor) return null;\n  null != i.__h && (c = i.__h, e = u.__e = i.__e, u.__h = null, o = [e]), (s = l.__b) && s(u);\n  try {\n    n: if (\"function\" == typeof $) {\n      if (b = u.props, g = (s = $.contextType) && t[s.__c], m = s ? g ? g.props.value : s.__ : t, i.__c ? k = (a = u.__c = i.__c).__ = a.__E : (\"prototype\" in $ && $.prototype.render ? u.__c = a = new $(b, m) : (u.__c = a = new x(b, m), a.constructor = $, a.render = B), g && g.sub(a), a.props = b, a.state || (a.state = {}), a.context = m, a.__n = t, v = a.__d = !0, a.__h = [], a._sb = []), null == a.__s && (a.__s = a.state), null != $.getDerivedStateFromProps && (a.__s == a.state && (a.__s = h({}, a.__s)), h(a.__s, $.getDerivedStateFromProps(b, a.__s))), y = a.props, p = a.state, a.__v = u, v) null == $.getDerivedStateFromProps && null != a.componentWillMount && a.componentWillMount(), null != a.componentDidMount && a.__h.push(a.componentDidMount);else {\n        if (null == $.getDerivedStateFromProps && b !== y && null != a.componentWillReceiveProps && a.componentWillReceiveProps(b, m), !a.__e && null != a.shouldComponentUpdate && !1 === a.shouldComponentUpdate(b, a.__s, m) || u.__v === i.__v) {\n          for (u.__v !== i.__v && (a.props = b, a.state = a.__s, a.__d = !1), u.__e = i.__e, u.__k = i.__k, u.__k.forEach(function (n) {\n            n && (n.__ = u);\n          }), w = 0; w < a._sb.length; w++) a.__h.push(a._sb[w]);\n          a._sb = [], a.__h.length && f.push(a);\n          break n;\n        }\n        null != a.componentWillUpdate && a.componentWillUpdate(b, a.__s, m), null != a.componentDidUpdate && a.__h.push(function () {\n          a.componentDidUpdate(y, p, d);\n        });\n      }\n      if (a.context = m, a.props = b, a.__P = n, A = l.__r, P = 0, \"prototype\" in $ && $.prototype.render) {\n        for (a.state = a.__s, a.__d = !1, A && A(u), s = a.render(a.props, a.state, a.context), C = 0; C < a._sb.length; C++) a.__h.push(a._sb[C]);\n        a._sb = [];\n      } else do {\n        a.__d = !1, A && A(u), s = a.render(a.props, a.state, a.context), a.state = a.__s;\n      } while (a.__d && ++P < 25);\n      a.state = a.__s, null != a.getChildContext && (t = h(h({}, t), a.getChildContext())), v || null == a.getSnapshotBeforeUpdate || (d = a.getSnapshotBeforeUpdate(y, p)), T = null != s && s.type === _ && null == s.key ? s.props.children : s, H(n, Array.isArray(T) ? T : [T], u, i, t, r, o, f, e, c), a.base = u.__e, u.__h = null, a.__h.length && f.push(a), k && (a.__E = a.__ = null), a.__e = !1;\n    } else null == o && u.__v === i.__v ? (u.__k = i.__k, u.__e = i.__e) : u.__e = O(i.__e, u, i, t, r, o, f, c);\n    (s = l.diffed) && s(u);\n  } catch (n) {\n    u.__v = null, (c || null != o) && (u.__e = e, u.__h = !!c, o[o.indexOf(e)] = null), l.__e(n, u, i);\n  }\n}\nfunction N(n, u) {\n  l.__c && l.__c(u, n), n.some(function (u) {\n    try {\n      n = u.__h, u.__h = [], n.some(function (n) {\n        n.call(u);\n      });\n    } catch (n) {\n      l.__e(n, u.__v);\n    }\n  });\n}\nfunction O(l, u, i, t, r, o, f, e) {\n  var s,\n    a,\n    h,\n    y = i.props,\n    p = u.props,\n    d = u.type,\n    _ = 0;\n  if (\"svg\" === d && (r = !0), null != o) for (; _ < o.length; _++) if ((s = o[_]) && \"setAttribute\" in s == !!d && (d ? s.localName === d : 3 === s.nodeType)) {\n    l = s, o[_] = null;\n    break;\n  }\n  if (null == l) {\n    if (null === d) return document.createTextNode(p);\n    l = r ? document.createElementNS(\"http://www.w3.org/2000/svg\", d) : document.createElement(d, p.is && p), o = null, e = !1;\n  }\n  if (null === d) y === p || e && l.data === p || (l.data = p);else {\n    if (o = o && n.call(l.childNodes), a = (y = i.props || c).dangerouslySetInnerHTML, h = p.dangerouslySetInnerHTML, !e) {\n      if (null != o) for (y = {}, _ = 0; _ < l.attributes.length; _++) y[l.attributes[_].name] = l.attributes[_].value;\n      (h || a) && (h && (a && h.__html == a.__html || h.__html === l.innerHTML) || (l.innerHTML = h && h.__html || \"\"));\n    }\n    if (k(l, p, y, r, e), h) u.__k = [];else if (_ = u.props.children, H(l, Array.isArray(_) ? _ : [_], u, i, t, r && \"foreignObject\" !== d, o, f, o ? o[0] : i.__k && A(i, 0), e), null != o) for (_ = o.length; _--;) null != o[_] && v(o[_]);\n    e || (\"value\" in p && void 0 !== (_ = p.value) && (_ !== l.value || \"progress\" === d && !_ || \"option\" === d && _ !== y.value) && g(l, \"value\", _, y.value, !1), \"checked\" in p && void 0 !== (_ = p.checked) && _ !== l.checked && g(l, \"checked\", _, y.checked, !1));\n  }\n  return l;\n}\nfunction S(n, u, i) {\n  try {\n    \"function\" == typeof n ? n(u) : n.current = u;\n  } catch (n) {\n    l.__e(n, i);\n  }\n}\nfunction q(n, u, i) {\n  var t, r;\n  if (l.unmount && l.unmount(n), (t = n.ref) && (t.current && t.current !== n.__e || S(t, null, u)), null != (t = n.__c)) {\n    if (t.componentWillUnmount) try {\n      t.componentWillUnmount();\n    } catch (n) {\n      l.__e(n, u);\n    }\n    t.base = t.__P = null, n.__c = void 0;\n  }\n  if (t = n.__k) for (r = 0; r < t.length; r++) t[r] && q(t[r], u, i || \"function\" != typeof n.type);\n  i || null == n.__e || v(n.__e), n.__ = n.__e = n.__d = void 0;\n}\nfunction B(n, l, u) {\n  return this.constructor(n, u);\n}\nfunction D(u, i, t) {\n  var r, o, f;\n  l.__ && l.__(u, i), o = (r = \"function\" == typeof t) ? null : t && t.__k || i.__k, f = [], M(i, u = (!r && t || i).__k = y(_, null, [u]), o || c, c, void 0 !== i.ownerSVGElement, !r && t ? [t] : o ? null : i.firstChild ? n.call(i.childNodes) : null, f, !r && t ? t : o ? o.__e : i.firstChild, r), N(f, u);\n}\nfunction E(n, l) {\n  D(n, l, E);\n}\nfunction F(l, u, i) {\n  var t,\n    r,\n    o,\n    f = h({}, l.props);\n  for (o in u) \"key\" == o ? t = u[o] : \"ref\" == o ? r = u[o] : f[o] = u[o];\n  return arguments.length > 2 && (f.children = arguments.length > 3 ? n.call(arguments, 2) : i), p(l.type, f, t || l.key, r || l.ref, null);\n}\nfunction G(n, l) {\n  var u = {\n    __c: l = \"__cC\" + e++,\n    __: n,\n    Consumer: function (n, l) {\n      return n.children(l);\n    },\n    Provider: function (n) {\n      var u, i;\n      return this.getChildContext || (u = [], (i = {})[l] = this, this.getChildContext = function () {\n        return i;\n      }, this.shouldComponentUpdate = function (n) {\n        this.props.value !== n.value && u.some(function (n) {\n          n.__e = !0, T(n);\n        });\n      }, this.sub = function (n) {\n        u.push(n);\n        var l = n.componentWillUnmount;\n        n.componentWillUnmount = function () {\n          u.splice(u.indexOf(n), 1), l && l.call(n);\n        };\n      }), n.children;\n    }\n  };\n  return u.Provider.__ = u.Consumer.contextType = u;\n}\nn = s.slice, l = {\n  __e: function (n, l, u, i) {\n    for (var t, r, o; l = l.__;) if ((t = l.__c) && !t.__) try {\n      if ((r = t.constructor) && null != r.getDerivedStateFromError && (t.setState(r.getDerivedStateFromError(n)), o = t.__d), null != t.componentDidCatch && (t.componentDidCatch(n, i || {}), o = t.__d), o) return t.__E = t;\n    } catch (l) {\n      n = l;\n    }\n    throw n;\n  }\n}, u = 0, i = function (n) {\n  return null != n && void 0 === n.constructor;\n}, t = !1, x.prototype.setState = function (n, l) {\n  var u;\n  u = null != this.__s && this.__s !== this.state ? this.__s : this.__s = h({}, this.state), \"function\" == typeof n && (n = n(h({}, u), this.props)), n && h(u, n), null != n && this.__v && (l && this._sb.push(l), T(this));\n}, x.prototype.forceUpdate = function (n) {\n  this.__v && (this.__e = !0, n && this.__h.push(n), T(this));\n}, x.prototype.render = _, r = [], f = \"function\" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, $.__r = 0, e = 0;\nexport { x as Component, _ as Fragment, F as cloneElement, G as createContext, y as createElement, d as createRef, y as h, E as hydrate, i as isValidElement, l as options, D as render, j as toChildArray };\n//# sourceMappingURL=preact.module.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}