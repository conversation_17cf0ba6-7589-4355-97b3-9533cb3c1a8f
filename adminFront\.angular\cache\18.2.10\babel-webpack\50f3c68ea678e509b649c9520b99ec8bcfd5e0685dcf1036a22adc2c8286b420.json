{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport addDays from \"../addDays/index.js\";\nimport addMonths from \"../addMonths/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name add\n * @category Common Helpers\n * @summary Add the specified years, months, weeks, days, hours, minutes and seconds to the given date.\n *\n * @description\n * Add the specified years, months, weeks, days, hours, minutes and seconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n *\n * | Key            | Description                        |\n * |----------------|------------------------------------|\n * | years          | Amount of years to be added        |\n * | months         | Amount of months to be added       |\n * | weeks          | Amount of weeks to be added        |\n * | days           | Amount of days to be added         |\n * | hours          | Amount of hours to be added        |\n * | minutes        | Amount of minutes to be added      |\n * | seconds        | Amount of seconds to be added      |\n *\n * All values default to 0\n *\n * @returns {Date} the new date with the seconds added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add the following duration to 1 September 2014, 10:19:50\n * const result = add(new Date(2014, 8, 1, 10, 19, 50), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30,\n * })\n * //=> Thu Jun 15 2017 15:29:20\n */\nexport default function add(dirtyDate, duration) {\n  requiredArgs(2, arguments);\n  if (!duration || _typeof(duration) !== 'object') return new Date(NaN);\n  var years = duration.years ? toInteger(duration.years) : 0;\n  var months = duration.months ? toInteger(duration.months) : 0;\n  var weeks = duration.weeks ? toInteger(duration.weeks) : 0;\n  var days = duration.days ? toInteger(duration.days) : 0;\n  var hours = duration.hours ? toInteger(duration.hours) : 0;\n  var minutes = duration.minutes ? toInteger(duration.minutes) : 0;\n  var seconds = duration.seconds ? toInteger(duration.seconds) : 0;\n\n  // Add years and months\n  var date = toDate(dirtyDate);\n  var dateWithMonths = months || years ? addMonths(date, months + years * 12) : date;\n\n  // Add weeks and days\n  var dateWithDays = days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;\n\n  // Add days, hours, minutes and seconds\n  var minutesToAdd = minutes + hours * 60;\n  var secondsToAdd = seconds + minutesToAdd * 60;\n  var msToAdd = secondsToAdd * 1000;\n  var finalDate = new Date(dateWithDays.getTime() + msToAdd);\n  return finalDate;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}