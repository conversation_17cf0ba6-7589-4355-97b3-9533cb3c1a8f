{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @param {Date|Number} date - the date that should be before the other one to return true\n * @param {Date|Number} dateToCompare - the date to compare with\n * @returns {Boolean} the first date is before the second date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport default function isBefore(dirtyDate, dirtyDateToCompare) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  return date.getTime() < dateToCompare.getTime();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}