{"ast": null, "code": "/**\n * @license Angular v18.2.9\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵ$localize } from '@angular/localize';\nexport { ɵ$localize as $localize } from '@angular/localize';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = ɵ$localize;\n//# sourceMappingURL=init.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}