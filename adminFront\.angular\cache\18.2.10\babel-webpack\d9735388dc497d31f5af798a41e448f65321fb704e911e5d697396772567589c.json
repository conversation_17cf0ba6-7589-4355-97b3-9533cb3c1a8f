{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPictureGetPicturelListPost$Json } from '../fn/picture/api-picture-get-picturel-list-post-json';\nimport { apiPictureGetPicturelListPost$Plain } from '../fn/picture/api-picture-get-picturel-list-post-plain';\nimport { apiPictureUpdatePicturePost$Json } from '../fn/picture/api-picture-update-picture-post-json';\nimport { apiPictureUpdatePicturePost$Plain } from '../fn/picture/api-picture-update-picture-post-plain';\nimport { apiPictureUploadListPicturePost$Json } from '../fn/picture/api-picture-upload-list-picture-post-json';\nimport { apiPictureUploadListPicturePost$Plain } from '../fn/picture/api-picture-upload-list-picture-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let PictureService = /*#__PURE__*/(() => {\n  class PictureService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiPictureGetPicturelListPost()` */\n    static {\n      this.ApiPictureGetPicturelListPostPath = '/api/Picture/GetPicturelList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureGetPicturelListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPictureGetPicturelListPost$Plain$Response(params, context) {\n      return apiPictureGetPicturelListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureGetPicturelListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPictureGetPicturelListPost$Plain(params, context) {\n      return this.apiPictureGetPicturelListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureGetPicturelListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPictureGetPicturelListPost$Json$Response(params, context) {\n      return apiPictureGetPicturelListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureGetPicturelListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiPictureGetPicturelListPost$Json(params, context) {\n      return this.apiPictureGetPicturelListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiPictureUploadListPicturePost()` */\n    static {\n      this.ApiPictureUploadListPicturePostPath = '/api/Picture/UploadListPicture';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureUploadListPicturePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUploadListPicturePost$Plain$Response(params, context) {\n      return apiPictureUploadListPicturePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUploadListPicturePost$Plain(params, context) {\n      return this.apiPictureUploadListPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureUploadListPicturePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUploadListPicturePost$Json$Response(params, context) {\n      return apiPictureUploadListPicturePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureUploadListPicturePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUploadListPicturePost$Json(params, context) {\n      return this.apiPictureUploadListPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiPictureUpdatePicturePost()` */\n    static {\n      this.ApiPictureUpdatePicturePostPath = '/api/Picture/UpdatePicture';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureUpdatePicturePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUpdatePicturePost$Plain$Response(params, context) {\n      return apiPictureUpdatePicturePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUpdatePicturePost$Plain(params, context) {\n      return this.apiPictureUpdatePicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPictureUpdatePicturePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUpdatePicturePost$Json$Response(params, context) {\n      return apiPictureUpdatePicturePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPictureUpdatePicturePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiPictureUpdatePicturePost$Json(params, context) {\n      return this.apiPictureUpdatePicturePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function PictureService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PictureService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PictureService,\n        factory: PictureService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PictureService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}