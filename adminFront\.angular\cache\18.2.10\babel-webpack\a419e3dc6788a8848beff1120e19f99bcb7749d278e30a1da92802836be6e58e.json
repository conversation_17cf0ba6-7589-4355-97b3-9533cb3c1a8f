{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bambara [bm]\n//! author : <PERSON><PERSON><PERSON> Comment : https://github.com/estellecomment\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var bm = moment.defineLocale('bm', {\n    months: 'Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo'.split('_'),\n    monthsShort: 'Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des'.split('_'),\n    weekdays: 'Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri'.split('_'),\n    weekdaysShort: 'Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib'.split('_'),\n    weekdaysMin: 'Ka_Nt_Ta_Ar_Al_Ju_Si'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'MMMM [tile] D [san] YYYY',\n      LLL: 'MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm',\n      LLLL: 'dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm'\n    },\n    calendar: {\n      sameDay: '[Bi lɛrɛ] LT',\n      nextDay: '[Sini lɛrɛ] LT',\n      nextWeek: 'dddd [don lɛrɛ] LT',\n      lastDay: '[Kunu lɛrɛ] LT',\n      lastWeek: 'dddd [tɛmɛnen lɛrɛ] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s kɔnɔ',\n      past: 'a bɛ %s bɔ',\n      s: 'sanga dama dama',\n      ss: 'sekondi %d',\n      m: 'miniti kelen',\n      mm: 'miniti %d',\n      h: 'lɛrɛ kelen',\n      hh: 'lɛrɛ %d',\n      d: 'tile kelen',\n      dd: 'tile %d',\n      M: 'kalo kelen',\n      MM: 'kalo %d',\n      y: 'san kelen',\n      yy: 'san %d'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return bm;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "bm", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/bm.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Bambara [bm]\n//! author : <PERSON><PERSON><PERSON> Comment : https://github.com/estellecomment\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var bm = moment.defineLocale('bm', {\n        months: 'Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo'.split(\n            '_'\n        ),\n        monthsShort: 'Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des'.split('_'),\n        weekdays: 'Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri'.split('_'),\n        weekdaysShort: 'Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib'.split('_'),\n        weekdaysMin: 'Ka_Nt_Ta_Ar_Al_Ju_Si'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'MMMM [tile] D [san] YYYY',\n            LLL: 'MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm',\n            LLLL: 'dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm',\n        },\n        calendar: {\n            sameDay: '[Bi lɛrɛ] LT',\n            nextDay: '[Sini lɛrɛ] LT',\n            nextWeek: 'dddd [don lɛrɛ] LT',\n            lastDay: '[Kunu lɛrɛ] LT',\n            lastWeek: 'dddd [tɛmɛnen lɛrɛ] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s kɔnɔ',\n            past: 'a bɛ %s bɔ',\n            s: 'sanga dama dama',\n            ss: 'sekondi %d',\n            m: 'miniti kelen',\n            mm: 'miniti %d',\n            h: 'lɛrɛ kelen',\n            hh: 'lɛrɛ %d',\n            d: 'tile kelen',\n            dd: 'tile %d',\n            M: 'kalo kelen',\n            MM: 'kalo %d',\n            y: 'san kelen',\n            yy: 'san %d',\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return bm;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,6IAA6I,CAACC,KAAK,CACvJ,GACJ,CAAC;IACDC,WAAW,EAAE,gDAAgD,CAACD,KAAK,CAAC,GAAG,CAAC;IACxEE,QAAQ,EAAE,8CAA8C,CAACF,KAAK,CAAC,GAAG,CAAC;IACnEG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,0BAA0B;MAC9BC,GAAG,EAAE,uCAAuC;MAC5CC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,cAAc;MACvBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,oBAAoB;MAC9BC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,wBAAwB;MAClCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,YAAY;MAClBC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOvC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}