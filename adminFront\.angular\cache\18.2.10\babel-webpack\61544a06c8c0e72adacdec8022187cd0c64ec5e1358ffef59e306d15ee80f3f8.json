{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"./household-binding.component\";\nimport * as i5 from \"./test-dropdown.component\";\nimport * as i6 from \"./simple-dropdown-test.component\";\nfunction HouseholdBindingDemoComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"small\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.apiLoadingStatus);\n  }\n}\nfunction HouseholdBindingDemoComponent_div_32_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_div_32_span_10_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeFromSelection2(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n  }\n}\nfunction HouseholdBindingDemoComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"div\", 33);\n    i0.ɵɵelement(3, \"nb-icon\", 34);\n    i0.ɵɵelementStart(4, \"span\", 35);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.clearSelection2());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"div\", 38);\n    i0.ɵɵtemplate(10, HouseholdBindingDemoComponent_div_32_span_10_Template, 4, 1, \"span\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r0.selectedHouseholds2.length, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedHouseholds2);\n  }\n}\nfunction HouseholdBindingDemoComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u7D50\\u679C (\\u57FA\\u672C\\u4F7F\\u7528 - Mock \\u8CC7\\u6599)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedHouseholds1));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9078\\u64C7\\u7D50\\u679C (API \\u8CC7\\u6599 - \\u5EFA\\u6848ID: \", ctx_r0.appliedBuildCaseId, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, ctx_r0.selectedHouseholdsApi));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u57FA\\u672C\\u4F7F\\u7528 - Mock \\u8CC7\\u6599)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedItems));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (API \\u8CC7\\u6599 - \\u5EFA\\u6848ID: \", ctx_r0.appliedBuildCaseId, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 2, ctx_r0.selectedItemsApi));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u81EA\\u5B9A\\u7FA9\\u6236\\u5225\\u8CC7\\u6599 - \\u542B\\u6A13\\u5C64\\u8CC7\\u8A0A)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedItemsCustom));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u6A13\\u5C64\\u6E2C\\u8A66 - \\u9AD8\\u6A13\\u5EFA\\u7BC9)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedItemsFloor));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.selectedItems2));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"small\", 45);\n    i0.ɵɵtext(2, \"\\u8CC7\\u6599\\u72C0\\u614B\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r0.debugData));\n  }\n}\nexport class HouseholdBindingDemoComponent {\n  constructor() {\n    this.selectedHouseholds1 = [];\n    this.selectedHouseholds2 = [];\n    this.selectedHouseholds3 = [];\n    this.selectedHouseholds4 = [];\n    this.selectedHouseholdsApi = []; // 新增：API 測試用\n    this.selectedHouseholdsFloor = []; // 新增：樓層測試用\n    this.selectedItems = [];\n    this.selectedItems2 = [];\n    this.selectedItemsApi = []; // 新增：API 測試用\n    this.selectedItemsCustom = []; // 新增：自定義測試用\n    this.selectedItemsFloor = []; // 新增：樓層測試用\n    this.debugData = null;\n    // 新增：API 測試相關屬性\n    this.testBuildCaseId = 1; // 預設建案ID\n    this.appliedBuildCaseId = null; // 實際套用的建案ID\n    this.apiLoadingStatus = '';\n    this.customBuildingData = {\n      '總統套房': [{\n        code: 'P001',\n        building: '總統套房',\n        floor: '50F',\n        isSelected: false,\n        isDisabled: false\n      }, {\n        code: 'P002',\n        building: '總統套房',\n        floor: '51F',\n        isSelected: false,\n        isDisabled: false\n      }],\n      '景觀樓層': Array.from({\n        length: 20\n      }, (_, i) => ({\n        code: `V${String(i + 1).padStart(3, '0')}`,\n        building: '景觀樓層',\n        floor: `${30 + Math.floor(i / 2)}F`,\n        isSelected: false,\n        isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\n      }))\n    };\n    // 新增：樓層測試資料 - 高樓建築\n    this.floorTestData = {\n      '摩天大樓': Array.from({\n        length: 60\n      }, (_, i) => {\n        const floor = Math.floor(i / 4) + 1; // 每4戶一層\n        const unit = String.fromCharCode(65 + i % 4); // A, B, C, D\n        return {\n          code: `${floor.toString().padStart(2, '0')}${unit}`,\n          building: '摩天大樓',\n          floor: `${floor}F`,\n          isSelected: false,\n          isDisabled: false\n        };\n      })\n    };\n  }\n  ngOnInit() {\n    // 初始化一些預選的戶別\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\n  }\n  onSelectionChange(selectedItems) {\n    this.selectedItems = selectedItems;\n    console.log('Selection changed:', selectedItems);\n  }\n  onSelectionChange2(selectedItems) {\n    this.selectedItems2 = selectedItems;\n    console.log('Selection 2 changed:', selectedItems);\n  }\n  onApiSelectionChange(selectedItems) {\n    this.selectedItemsApi = selectedItems;\n    console.log('API Selection changed:', selectedItems);\n  }\n  // 新增：自定義選擇變更處理\n  onCustomSelectionChange(selectedItems) {\n    this.selectedItemsCustom = selectedItems;\n    console.log('Custom Selection changed:', selectedItems);\n  }\n  // 新增：樓層測試選擇變更處理\n  onFloorSelectionChange(selectedItems) {\n    this.selectedItemsFloor = selectedItems;\n    console.log('Floor Selection changed:', selectedItems);\n  }\n  // 新增：API 測試相關方法\n  applyBuildCaseId() {\n    if (this.testBuildCaseId && this.testBuildCaseId > 0) {\n      this.appliedBuildCaseId = this.testBuildCaseId;\n      this.selectedHouseholdsApi = []; // 清空之前的選擇\n      this.selectedItemsApi = [];\n      this.apiLoadingStatus = `正在載入建案 ${this.testBuildCaseId} 的戶別資料...`;\n      // 模擬載入狀態更新\n      setTimeout(() => {\n        this.apiLoadingStatus = '';\n      }, 2000);\n      console.log('Applied build case ID:', this.appliedBuildCaseId);\n    } else {\n      alert('請輸入有效的建案ID');\n    }\n  }\n  clearBuildCaseId() {\n    this.appliedBuildCaseId = null;\n    this.selectedHouseholdsApi = [];\n    this.selectedItemsApi = [];\n    this.apiLoadingStatus = '';\n    console.log('Cleared build case ID');\n  }\n  debugInfo() {\n    this.debugData = {\n      selectedHouseholds1: this.selectedHouseholds1,\n      selectedHouseholds2: this.selectedHouseholds2,\n      selectedHouseholds3: this.selectedHouseholds3,\n      selectedHouseholds4: this.selectedHouseholds4,\n      selectedHouseholdsApi: this.selectedHouseholdsApi,\n      customBuildingData: this.customBuildingData,\n      selectedItems: this.selectedItems,\n      selectedItems2: this.selectedItems2,\n      selectedItemsApi: this.selectedItemsApi,\n      appliedBuildCaseId: this.appliedBuildCaseId,\n      testBuildCaseId: this.testBuildCaseId\n    };\n    console.log('Debug info:', this.debugData);\n  }\n  testBuildingSelect() {\n    console.log('Testing building selection...');\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\n    // 主要是為了觸發除錯訊息\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\n  }\n  clearSelection2() {\n    this.selectedHouseholds2 = [];\n    this.selectedItems2 = [];\n  }\n  removeFromSelection2(householdCode) {\n    this.selectedHouseholds2 = this.selectedHouseholds2.filter(code => code !== householdCode);\n    this.selectedItems2 = this.selectedItems2.filter(item => item.code !== householdCode);\n  }\n  static {\n    this.ɵfac = function HouseholdBindingDemoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingDemoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingDemoComponent,\n      selectors: [[\"app-household-binding-demo\"]],\n      decls: 104,\n      vars: 33,\n      consts: [[1, \"demo-section\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"maxSelections\"], [1, \"control-panel\"], [\"for\", \"buildCaseId\"], [\"id\", \"buildCaseId\", \"type\", \"number\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u5EFA\\u6848ID\", 1, \"form-control\", 2, \"width\", \"150px\", \"display\", \"inline-block\", \"margin\", \"0 10px\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"ms-2\", 3, \"click\"], [1, \"mt-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"buildCaseId\", \"maxSelections\"], [\"class\", \"mt-2 alert alert-info\", 4, \"ngIf\"], [1, \"alert\", \"alert-info\", 2, \"margin-bottom\", \"1rem\", \"padding\", \"0.75rem\", \"background-color\", \"#d1ecf1\", \"border\", \"1px solid #b6d4db\", \"border-radius\", \"0.375rem\"], [2, \"color\", \"#0c5460\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"showSelectedArea\", \"maxSelections\", \"excludedHouseholds\"], [\"class\", \"custom-selected-display\", 4, \"ngIf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"allowSearch\", \"allowBatchSelect\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"buildingData\"], [1, \"alert\", \"alert-warning\", 2, \"margin-bottom\", \"1rem\", \"padding\", \"0.75rem\", \"background-color\", \"#fff3cd\", \"border\", \"1px solid #ffeaa7\", \"border-radius\", \"0.375rem\"], [2, \"color\", \"#856404\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"buildingData\", \"maxSelections\"], [\"class\", \"demo-section\", 4, \"ngIf\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"1fr 1fr\", \"gap\", \"1rem\"], [2, \"padding\", \"1rem\", \"border\", \"1px solid #007bff\", \"border-radius\", \"0.375rem\", \"background-color\", \"#f8f9fa\"], [2, \"color\", \"#007bff\", \"margin-bottom\", \"0.5rem\"], [2, \"font-size\", \"0.875rem\", \"margin-bottom\", \"0.5rem\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"1rem\", \"border\", \"1px solid #28a745\", \"border-radius\", \"0.375rem\", \"background-color\", \"#f8f9fa\"], [2, \"color\", \"#28a745\", \"margin-bottom\", \"0.5rem\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"mt-2\", \"alert\", \"alert-info\"], [1, \"custom-selected-display\"], [1, \"custom-selected-header\"], [1, \"selected-info\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [1, \"selected-title\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"custom-selected-content\"], [1, \"selected-items-grid\"], [\"class\", \"selected-item-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"selected-item-chip\"], [\"type\", \"button\", 1, \"remove-chip-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"result-display\"], [1, \"mt-2\"], [1, \"text-muted\"]],\n      template: function HouseholdBindingDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h4\");\n          i0.ɵɵtext(3, \"\\u6236\\u5225\\u7D81\\u5B9A\\u5143\\u4EF6\\u793A\\u4F8B\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"div\", 0)(6, \"h5\");\n          i0.ɵɵtext(7, \"\\u57FA\\u672C\\u4F7F\\u7528 (Mock \\u8CC7\\u6599)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"app-household-binding\", 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds1, $event) || (ctx.selectedHouseholds1 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 0)(10, \"h5\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 3);\n          i0.ɵɵtext(14, \"\\u5EFA\\u6848ID\\uFF1A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_input_ngModelChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.testBuildCaseId, $event) || (ctx.testBuildCaseId = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_16_listener() {\n            return ctx.applyBuildCaseId();\n          });\n          i0.ɵɵtext(17, \" \\u5957\\u7528 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_18_listener() {\n            return ctx.clearBuildCaseId();\n          });\n          i0.ɵɵtext(19, \" \\u6E05\\u9664 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"app-household-binding\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholdsApi, $event) || (ctx.selectedHouseholdsApi = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_21_listener($event) {\n            return ctx.onApiSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, HouseholdBindingDemoComponent_div_22_Template, 3, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 0)(24, \"h5\");\n          i0.ɵɵtext(25, \"\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"small\", 11)(28, \"strong\");\n          i0.ɵɵtext(29, \"\\u667A\\u80FD\\u6392\\u9664\\uFF1A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \"\\u6B64\\u5143\\u4EF6\\u6703\\u81EA\\u52D5\\u6392\\u9664\\u300C\\u57FA\\u672C\\u4F7F\\u7528\\u300D\\u5143\\u4EF6\\u5DF2\\u9078\\u64C7\\u7684\\u6236\\u5225\\uFF0C\\u907F\\u514D\\u91CD\\u8907\\u9078\\u64C7\\u3002\\u5DF2\\u6392\\u9664\\u7684\\u6236\\u5225\\u6703\\u986F\\u793A\\u70BA\\u7070\\u8272\\u4E14\\u5E36\\u6709\\u522A\\u9664\\u7DDA\\u548C\\u7D05\\u8272 \\u2715 \\u6A19\\u8A18\\u3002 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"app-household-binding\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds2, $event) || (ctx.selectedHouseholds2 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_31_listener($event) {\n            return ctx.onSelectionChange2($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, HouseholdBindingDemoComponent_div_32_Template, 11, 2, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 0)(34, \"h5\");\n          i0.ɵɵtext(35, \"\\u7981\\u7528\\u641C\\u5C0B\\u548C\\u6279\\u6B21\\u9078\\u64C7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"app-household-binding\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_36_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds3, $event) || (ctx.selectedHouseholds3 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 0)(38, \"h5\");\n          i0.ɵɵtext(39, \"\\u81EA\\u5B9A\\u7FA9\\u6236\\u5225\\u8CC7\\u6599 (\\u6E2C\\u8A66\\u6A13\\u5C64\\u529F\\u80FD)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 10)(41, \"small\", 11)(42, \"strong\");\n          i0.ɵɵtext(43, \"\\u6A13\\u5C64\\u529F\\u80FD\\u6E2C\\u8A66\\uFF1A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \"\\u6B64\\u5143\\u4EF6\\u5305\\u542B\\u591A\\u500B\\u6A13\\u5C64\\u7684\\u6236\\u5225\\u8CC7\\u6599\\u3002\\u60A8\\u53EF\\u4EE5\\u6E2C\\u8A66\\uFF1A \");\n          i0.ɵɵelement(45, \"br\");\n          i0.ɵɵtext(46, \"\\u2022 \\u9078\\u64C7\\u68DF\\u5225\\u5F8C\\u67E5\\u770B\\u6A13\\u5C64\\u7BE9\\u9078\\u5668 \");\n          i0.ɵɵelement(47, \"br\");\n          i0.ɵɵtext(48, \"\\u2022 \\u9EDE\\u64CA\\u6A13\\u5C64\\u6309\\u9215\\u9032\\u884C\\u7BE9\\u9078 \");\n          i0.ɵɵelement(49, \"br\");\n          i0.ɵɵtext(50, \"\\u2022 \\u6236\\u5225\\u6309\\u9215\\u6703\\u986F\\u793A\\u6A13\\u5C64\\u8CC7\\u8A0A \");\n          i0.ɵɵelement(51, \"br\");\n          i0.ɵɵtext(52, \"\\u2022 \\u300C\\u7E3D\\u7D71\\u5957\\u623F\\u300D\\u67092\\u500B\\u6A13\\u5C64\\uFF0850F, 51F\\uFF09\\uFF0C\\u300C\\u666F\\u89C0\\u6A13\\u5C64\\u300D\\u670910\\u500B\\u6A13\\u5C64\\uFF0830F-39F\\uFF09 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"app-household-binding\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_53_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds4, $event) || (ctx.selectedHouseholds4 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_53_listener($event) {\n            return ctx.onCustomSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 0)(55, \"h5\");\n          i0.ɵɵtext(56, \"\\u6A13\\u5C64\\u529F\\u80FD\\u6E2C\\u8A66 - \\u591A\\u6A13\\u5C64\\u5EFA\\u7BC9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 16)(58, \"small\", 17)(59, \"strong\");\n          i0.ɵɵtext(60, \"\\u9AD8\\u6A13\\u6E2C\\u8A66\\uFF1A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(61, \"\\u6B64\\u5EFA\\u7BC9\\u670915\\u500B\\u6A13\\u5C64\\uFF0C\\u6BCF\\u6A13\\u5C644\\u6236\\uFF0C\\u7E3D\\u517160\\u6236\\u3002\\u6E2C\\u8A66\\u6A13\\u5C64\\u7BE9\\u9078\\u7684\\u6548\\u679C\\u3002 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"app-household-binding\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_62_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholdsFloor, $event) || (ctx.selectedHouseholdsFloor = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_62_listener($event) {\n            return ctx.onFloorSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(63, HouseholdBindingDemoComponent_div_63_Template, 6, 3, \"div\", 19)(64, HouseholdBindingDemoComponent_div_64_Template, 6, 4, \"div\", 19)(65, HouseholdBindingDemoComponent_div_65_Template, 6, 3, \"div\", 19)(66, HouseholdBindingDemoComponent_div_66_Template, 6, 4, \"div\", 19)(67, HouseholdBindingDemoComponent_div_67_Template, 6, 3, \"div\", 19)(68, HouseholdBindingDemoComponent_div_68_Template, 6, 3, \"div\", 19)(69, HouseholdBindingDemoComponent_div_69_Template, 6, 3, \"div\", 19);\n          i0.ɵɵelementStart(70, \"div\", 0)(71, \"h5\");\n          i0.ɵɵtext(72, \"\\u6BD4\\u8F03\\u5169\\u7A2E\\u986F\\u793A\\u6A21\\u5F0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 20)(74, \"div\", 21)(75, \"h6\", 22);\n          i0.ɵɵtext(76, \"\\u57FA\\u672C\\u4F7F\\u7528 (\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\", 23);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 24);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"div\", 25)(82, \"h6\", 26);\n          i0.ɵɵtext(83, \"\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF (\\u81EA\\u5B9A\\u7FA9\\u986F\\u793A)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"p\", 23);\n          i0.ɵɵtext(85);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"div\", 24);\n          i0.ɵɵtext(87);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(88, \"div\", 0)(89, \"h5\");\n          i0.ɵɵtext(90, \"\\u9664\\u932F\\u8CC7\\u8A0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_91_listener() {\n            return ctx.debugInfo();\n          });\n          i0.ɵɵtext(92, \" \\u6AA2\\u67E5\\u8CC7\\u6599 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_93_listener() {\n            return ctx.testBuildingSelect();\n          });\n          i0.ɵɵtext(94, \" \\u6E2C\\u8A66\\u9078\\u64C7 A\\u68DF \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(95, HouseholdBindingDemoComponent_div_95_Template, 6, 3, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"div\", 0)(97, \"h5\");\n          i0.ɵɵtext(98, \"\\u6E2C\\u8A66\\u7C21\\u5316\\u4E0B\\u62C9\\u9078\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(99, \"app-test-dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"div\", 0)(101, \"h5\");\n          i0.ɵɵtext(102, \"\\u6975\\u7C21\\u4E0B\\u62C9\\u9078\\u55AE\\u6E2C\\u8A66\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(103, \"app-simple-dropdown-test\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds1);\n          i0.ɵɵproperty(\"maxSelections\", 20);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\\u4F7F\\u7528 API \\u8CC7\\u6599 (\\u5EFA\\u6848ID: \", ctx.testBuildCaseId, \")\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.testBuildCaseId);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholdsApi);\n          i0.ɵɵproperty(\"buildCaseId\", ctx.appliedBuildCaseId)(\"maxSelections\", 15);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.apiLoadingStatus);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds2);\n          i0.ɵɵproperty(\"showSelectedArea\", false)(\"maxSelections\", 10)(\"excludedHouseholds\", ctx.selectedHouseholds1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds2.length > 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds3);\n          i0.ɵɵproperty(\"allowSearch\", false)(\"allowBatchSelect\", false);\n          i0.ɵɵadvance(17);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds4);\n          i0.ɵɵproperty(\"buildingData\", ctx.customBuildingData);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholdsFloor);\n          i0.ɵɵproperty(\"buildingData\", ctx.floorTestData)(\"maxSelections\", 10);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds1.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholdsApi.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItemsApi.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItemsCustom.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItemsFloor.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems2.length > 0);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx.selectedHouseholds1.length, \" \\u500B\\u6236\\u5225\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedHouseholds1.join(\", \") || \"\\u5C1A\\u672A\\u9078\\u64C7\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx.selectedHouseholds2.length, \" \\u500B\\u6236\\u5225\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedHouseholds2.join(\", \") || \"\\u5C1A\\u672A\\u9078\\u64C7\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.debugData);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardHeaderComponent, i3.NbIconComponent, i4.HouseholdBindingComponent, i5.TestDropdownComponent, i6.SimpleDropdownTestComponent, i1.JsonPipe],\n      styles: [\".demo-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #495057;\\n  font-weight: 600;\\n}\\n\\n.result-display[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  padding: 1rem;\\n  font-size: 0.875rem;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.custom-selected-display[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding: 1rem;\\n  background-color: #fff;\\n  border: 1px solid #28a745;\\n  border-radius: 0.375rem;\\n}\\n\\n.custom-selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n\\n.selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.selected-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #28a745;\\n  font-size: 0.875rem;\\n}\\n\\n.custom-selected-content[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e9ecef;\\n  padding-top: 0.75rem;\\n}\\n\\n.selected-items-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.selected-item-chip[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #d4edda;\\n  color: #155724;\\n  border: 1px solid #c3e6cb;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #155724;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.15s ease;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #c3e6cb;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.control-panel[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 6px;\\n  margin-bottom: 16px;\\n}\\n\\n.control-panel[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin: 0;\\n}\\n\\n.control-panel[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  padding: 6px 12px;\\n  font-size: 14px;\\n}\\n\\n.control-panel[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "apiLoading<PERSON><PERSON><PERSON>", "ɵɵlistener", "HouseholdBindingDemoComponent_div_32_span_10_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "removeFromSelection2", "ɵɵelement", "ɵɵtextInterpolate1", "HouseholdBindingDemoComponent_div_32_Template_button_click_6_listener", "_r2", "clearSelection2", "ɵɵtemplate", "HouseholdBindingDemoComponent_div_32_span_10_Template", "selectedHouseholds2", "length", "ɵɵproperty", "ɵɵpipeBind1", "selectedHouseholds1", "appliedBuildCaseId", "selectedHouseholdsApi", "selectedItems", "selectedItemsApi", "selectedItemsCustom", "selectedItemsFloor", "selectedItems2", "debugData", "HouseholdBindingDemoComponent", "constructor", "selectedHouseholds3", "selectedHouseholds4", "selectedHouseholdsFloor", "testBuildCaseId", "customBuildingData", "code", "building", "floor", "isSelected", "isDisabled", "Array", "from", "_", "i", "String", "padStart", "Math", "floorTestData", "unit", "fromCharCode", "toString", "ngOnInit", "onSelectionChange", "console", "log", "onSelectionChange2", "onApiSelectionChange", "onCustomSelectionChange", "onFloorSelectionChange", "applyBuildCaseId", "setTimeout", "alert", "clearBuildCaseId", "debugInfo", "testBuildingSelect", "householdCode", "filter", "item", "selectors", "decls", "vars", "consts", "template", "HouseholdBindingDemoComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener", "HouseholdBindingDemoComponent_Template_input_ngModelChange_15_listener", "HouseholdBindingDemoComponent_Template_button_click_16_listener", "HouseholdBindingDemoComponent_Template_button_click_18_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_21_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_21_listener", "HouseholdBindingDemoComponent_div_22_Template", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_31_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_31_listener", "HouseholdBindingDemoComponent_div_32_Template", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_36_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_53_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_53_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_62_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_62_listener", "HouseholdBindingDemoComponent_div_63_Template", "HouseholdBindingDemoComponent_div_64_Template", "HouseholdBindingDemoComponent_div_65_Template", "HouseholdBindingDemoComponent_div_66_Template", "HouseholdBindingDemoComponent_div_67_Template", "HouseholdBindingDemoComponent_div_68_Template", "HouseholdBindingDemoComponent_div_69_Template", "HouseholdBindingDemoComponent_Template_button_click_91_listener", "HouseholdBindingDemoComponent_Template_button_click_93_listener", "HouseholdBindingDemoComponent_div_95_Template", "ɵɵtwoWayProperty", "join"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding-demo.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { HouseholdItem, BuildingData } from './household-binding.component';\r\n\r\n@Component({\r\n  selector: 'app-household-binding-demo',\r\n  template: `\r\n    <nb-card>\r\n      <nb-card-header>\r\n        <h4>戶別綁定元件示例</h4>\r\n      </nb-card-header>\r\n      <nb-card-body>        <div class=\"demo-section\">\r\n          <h5>基本使用 (Mock 資料)</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds1\"\r\n            placeholder=\"請選擇戶別\"\r\n            [maxSelections]=\"20\"\r\n            (selectionChange)=\"onSelectionChange($event)\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>使用 API 資料 (建案ID: {{testBuildCaseId}})</h5>\r\n          <div class=\"control-panel\">\r\n            <label for=\"buildCaseId\">建案ID：</label>\r\n            <input\r\n              id=\"buildCaseId\"\r\n              type=\"number\"\r\n              [(ngModel)]=\"testBuildCaseId\"\r\n              class=\"form-control\"\r\n              style=\"width: 150px; display: inline-block; margin: 0 10px;\"\r\n              placeholder=\"請輸入建案ID\">\r\n            <button type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"applyBuildCaseId()\">\r\n              套用\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-secondary btn-sm ms-2\" (click)=\"clearBuildCaseId()\">\r\n              清除\r\n            </button>\r\n          </div>\r\n          <div class=\"mt-3\">\r\n            <app-household-binding\r\n              [(ngModel)]=\"selectedHouseholdsApi\"\r\n              placeholder=\"請選擇戶別\"\r\n              [buildCaseId]=\"appliedBuildCaseId\"\r\n              [maxSelections]=\"15\"\r\n              (selectionChange)=\"onApiSelectionChange($event)\">\r\n            </app-household-binding>\r\n          </div>\r\n          <div *ngIf=\"apiLoadingStatus\" class=\"mt-2 alert alert-info\">\r\n            <small>{{apiLoadingStatus}}</small>\r\n          </div>\r\n        </div><div class=\"demo-section\">\r\n          <h5>不顯示已選擇區域</h5>\r\n          <div class=\"alert alert-info\" style=\"margin-bottom: 1rem; padding: 0.75rem; background-color: #d1ecf1; border: 1px solid #b6d4db; border-radius: 0.375rem;\">\r\n            <small style=\"color: #0c5460;\">\r\n              <strong>智能排除：</strong>此元件會自動排除「基本使用」元件已選擇的戶別，避免重複選擇。已排除的戶別會顯示為灰色且帶有刪除線和紅色 ✕ 標記。\r\n            </small>\r\n          </div>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds2\"\r\n            placeholder=\"請選擇戶別\"\r\n            [showSelectedArea]=\"false\"\r\n            [maxSelections]=\"10\"\r\n            [excludedHouseholds]=\"selectedHouseholds1\"\r\n            (selectionChange)=\"onSelectionChange2($event)\">\r\n          </app-household-binding>\r\n\r\n          <!-- 自定義已選擇項目顯示 -->\r\n          <div *ngIf=\"selectedHouseholds2.length > 0\" class=\"custom-selected-display\">\r\n            <div class=\"custom-selected-header\">\r\n              <div class=\"selected-info\">\r\n                <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n                <span class=\"selected-title\">已選擇戶別 ({{selectedHouseholds2.length}})</span>\r\n              </div>\r\n              <button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearSelection2()\">\r\n                清空\r\n              </button>\r\n            </div>\r\n            <div class=\"custom-selected-content\">\r\n              <div class=\"selected-items-grid\">\r\n                <span *ngFor=\"let householdCode of selectedHouseholds2\" class=\"selected-item-chip\">\r\n                  {{householdCode}}\r\n                  <button type=\"button\" class=\"remove-chip-btn\" (click)=\"removeFromSelection2(householdCode)\">\r\n                    <nb-icon icon=\"close-outline\"></nb-icon>\r\n                  </button>\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>禁用搜尋和批次選擇</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds3\"\r\n            placeholder=\"請選擇戶別\"\r\n            [allowSearch]=\"false\"\r\n            [allowBatchSelect]=\"false\">\r\n          </app-household-binding>\r\n        </div>        <div class=\"demo-section\">\r\n          <h5>自定義戶別資料 (測試樓層功能)</h5>\r\n          <div class=\"alert alert-info\" style=\"margin-bottom: 1rem; padding: 0.75rem; background-color: #d1ecf1; border: 1px solid #b6d4db; border-radius: 0.375rem;\">\r\n            <small style=\"color: #0c5460;\">\r\n              <strong>樓層功能測試：</strong>此元件包含多個樓層的戶別資料。您可以測試：\r\n              <br>• 選擇棟別後查看樓層篩選器\r\n              <br>• 點擊樓層按鈕進行篩選\r\n              <br>• 戶別按鈕會顯示樓層資訊\r\n              <br>• 「總統套房」有2個樓層（50F, 51F），「景觀樓層」有10個樓層（30F-39F）\r\n            </small>\r\n          </div>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds4\"\r\n            placeholder=\"請選擇戶別\"\r\n            [buildingData]=\"customBuildingData\"\r\n            (selectionChange)=\"onCustomSelectionChange($event)\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>樓層功能測試 - 多樓層建築</h5>\r\n          <div class=\"alert alert-warning\" style=\"margin-bottom: 1rem; padding: 0.75rem; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 0.375rem;\">\r\n            <small style=\"color: #856404;\">\r\n              <strong>高樓測試：</strong>此建築有15個樓層，每樓層4戶，總共60戶。測試樓層篩選的效果。\r\n            </small>\r\n          </div>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholdsFloor\"\r\n            placeholder=\"請選擇戶別\"\r\n            [buildingData]=\"floorTestData\"\r\n            [maxSelections]=\"10\"\r\n            (selectionChange)=\"onFloorSelectionChange($event)\">\r\n          </app-household-binding>\r\n        </div><div class=\"demo-section\" *ngIf=\"selectedHouseholds1.length > 0\">\r\n          <h5>選擇結果 (基本使用 - Mock 資料)</h5>\r\n          <pre class=\"result-display\">{{ selectedHouseholds1 | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedHouseholdsApi.length > 0\">\r\n          <h5>選擇結果 (API 資料 - 建案ID: {{appliedBuildCaseId}})</h5>\r\n          <pre class=\"result-display\">{{ selectedHouseholdsApi | json }}</pre>\r\n        </div>        <div class=\"demo-section\" *ngIf=\"selectedItems.length > 0\">\r\n          <h5>詳細選擇項目 (基本使用 - Mock 資料)</h5>\r\n          <pre class=\"result-display\">{{ selectedItems | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedItemsApi.length > 0\">\r\n          <h5>詳細選擇項目 (API 資料 - 建案ID: {{appliedBuildCaseId}})</h5>\r\n          <pre class=\"result-display\">{{ selectedItemsApi | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedItemsCustom.length > 0\">\r\n          <h5>詳細選擇項目 (自定義戶別資料 - 含樓層資訊)</h5>\r\n          <pre class=\"result-display\">{{ selectedItemsCustom | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedItemsFloor.length > 0\">\r\n          <h5>詳細選擇項目 (樓層測試 - 高樓建築)</h5>\r\n          <pre class=\"result-display\">{{ selectedItemsFloor | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedItems2.length > 0\">\r\n          <h5>詳細選擇項目 (不顯示已選擇區域)</h5>\r\n          <pre class=\"result-display\">{{ selectedItems2 | json }}</pre>\r\n        </div><div class=\"demo-section\">\r\n          <h5>比較兩種顯示模式</h5>\r\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;\">\r\n            <div style=\"padding: 1rem; border: 1px solid #007bff; border-radius: 0.375rem; background-color: #f8f9fa;\">\r\n              <h6 style=\"color: #007bff; margin-bottom: 0.5rem;\">基本使用 (顯示已選擇區域)</h6>\r\n              <p style=\"font-size: 0.875rem; margin-bottom: 0.5rem;\">已選擇: {{selectedHouseholds1.length}} 個戶別</p>\r\n              <div style=\"font-size: 0.75rem; color: #6c757d;\">\r\n                {{selectedHouseholds1.join(', ') || '尚未選擇'}}\r\n              </div>\r\n            </div>\r\n            <div style=\"padding: 1rem; border: 1px solid #28a745; border-radius: 0.375rem; background-color: #f8f9fa;\">\r\n              <h6 style=\"color: #28a745; margin-bottom: 0.5rem;\">不顯示已選擇區域 (自定義顯示)</h6>\r\n              <p style=\"font-size: 0.875rem; margin-bottom: 0.5rem;\">已選擇: {{selectedHouseholds2.length}} 個戶別</p>\r\n              <div style=\"font-size: 0.75rem; color: #6c757d;\">\r\n                {{selectedHouseholds2.join(', ') || '尚未選擇'}}\r\n              </div>\r\n            </div>          </div>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>除錯資訊</h5>\r\n          <button type=\"button\" class=\"btn btn-secondary btn-sm me-2\" (click)=\"debugInfo()\">\r\n            檢查資料\r\n          </button>\r\n          <button type=\"button\" class=\"btn btn-info btn-sm me-2\" (click)=\"testBuildingSelect()\">\r\n            測試選擇 A棟\r\n          </button>\r\n          <div *ngIf=\"debugData\" class=\"mt-2\">\r\n            <small class=\"text-muted\">資料狀態：</small>\r\n            <pre class=\"result-display\">{{ debugData | json }}</pre>\r\n          </div>\r\n        </div><div class=\"demo-section\">\r\n          <h5>測試簡化下拉選單</h5>\r\n          <app-test-dropdown></app-test-dropdown>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>極簡下拉選單測試</h5>\r\n          <app-simple-dropdown-test></app-simple-dropdown-test>\r\n        </div>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  `, styles: [`\r\n    .demo-section {\r\n      margin-bottom: 2rem;\r\n      padding: 1rem;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 0.375rem;\r\n      background-color: #f8f9fa;\r\n    }\r\n\r\n    .demo-section h5 {\r\n      margin-bottom: 1rem;\r\n      color: #495057;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .result-display {\r\n      background-color: #fff;\r\n      border: 1px solid #ced4da;\r\n      border-radius: 0.25rem;\r\n      padding: 1rem;\r\n      font-size: 0.875rem;\r\n      max-height: 200px;\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .custom-selected-display {\r\n      margin-top: 1rem;\r\n      padding: 1rem;\r\n      background-color: #fff;\r\n      border: 1px solid #28a745;\r\n      border-radius: 0.375rem;\r\n    }\r\n\r\n    .custom-selected-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 0.75rem;\r\n    }\r\n\r\n    .selected-info {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n    }\r\n\r\n    .selected-title {\r\n      font-weight: 500;\r\n      color: #28a745;\r\n      font-size: 0.875rem;\r\n    }\r\n\r\n    .custom-selected-content {\r\n      border-top: 1px solid #e9ecef;\r\n      padding-top: 0.75rem;\r\n    }\r\n\r\n    .selected-items-grid {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 0.5rem;\r\n    }\r\n\r\n    .selected-item-chip {\r\n      display: inline-flex;\r\n      align-items: center;\r\n      gap: 0.25rem;\r\n      padding: 0.375rem 0.75rem;\r\n      background-color: #d4edda;\r\n      color: #155724;\r\n      border: 1px solid #c3e6cb;\r\n      border-radius: 1rem;\r\n      font-size: 0.75rem;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .remove-chip-btn {\r\n      background: none;\r\n      border: none;\r\n      padding: 0;\r\n      margin: 0;\r\n      cursor: pointer;\r\n      color: #155724;\r\n      border-radius: 50%;\r\n      width: 1rem;\r\n      height: 1rem;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      transition: background-color 0.15s ease;\r\n    }\r\n\r\n    .remove-chip-btn:hover {\r\n      background-color: #c3e6cb;\r\n    }    .remove-chip-btn nb-icon {\r\n      font-size: 0.75rem;\r\n    }\r\n\r\n    .control-panel {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n      padding: 12px;\r\n      background-color: #fff;\r\n      border: 1px solid #ced4da;\r\n      border-radius: 6px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .control-panel label {\r\n      font-weight: 500;\r\n      color: #495057;\r\n      margin: 0;\r\n    }\r\n\r\n    .control-panel .form-control {\r\n      border: 1px solid #ced4da;\r\n      border-radius: 4px;\r\n      padding: 6px 12px;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .control-panel .btn {\r\n      white-space: nowrap;\r\n    }\r\n  `]\r\n})\r\nexport class HouseholdBindingDemoComponent implements OnInit {\r\n  selectedHouseholds1: string[] = [];\r\n  selectedHouseholds2: string[] = [];\r\n  selectedHouseholds3: string[] = [];\r\n  selectedHouseholds4: string[] = [];\r\n  selectedHouseholdsApi: string[] = []; // 新增：API 測試用\r\n  selectedHouseholdsFloor: string[] = []; // 新增：樓層測試用\r\n  selectedItems: HouseholdItem[] = [];\r\n  selectedItems2: HouseholdItem[] = [];\r\n  selectedItemsApi: HouseholdItem[] = []; // 新增：API 測試用\r\n  selectedItemsCustom: HouseholdItem[] = []; // 新增：自定義測試用\r\n  selectedItemsFloor: HouseholdItem[] = []; // 新增：樓層測試用\r\n  debugData: any = null;\r\n  \r\n  // 新增：API 測試相關屬性\r\n  testBuildCaseId: number = 1; // 預設建案ID\r\n  appliedBuildCaseId: number | null = null; // 實際套用的建案ID\r\n  apiLoadingStatus: string = '';\r\n  customBuildingData: BuildingData = {\r\n    '總統套房': [\r\n      { code: 'P001', building: '總統套房', floor: '50F', isSelected: false, isDisabled: false },\r\n      { code: 'P002', building: '總統套房', floor: '51F', isSelected: false, isDisabled: false }\r\n    ],\r\n    '景觀樓層': Array.from({ length: 20 }, (_, i) => ({\r\n      code: `V${String(i + 1).padStart(3, '0')}`,\r\n      building: '景觀樓層',\r\n      floor: `${30 + Math.floor(i / 2)}F`,\r\n      isSelected: false,\r\n      isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\r\n    }))\r\n  };\r\n\r\n  // 新增：樓層測試資料 - 高樓建築\r\n  floorTestData: BuildingData = {\r\n    '摩天大樓': Array.from({ length: 60 }, (_, i) => {\r\n      const floor = Math.floor(i / 4) + 1; // 每4戶一層\r\n      const unit = String.fromCharCode(65 + (i % 4)); // A, B, C, D\r\n      return {\r\n        code: `${floor.toString().padStart(2, '0')}${unit}`,\r\n        building: '摩天大樓',\r\n        floor: `${floor}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      };\r\n    })\r\n  };\r\n\r\n  ngOnInit() {\r\n    // 初始化一些預選的戶別\r\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\r\n  }\r\n  onSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItems = selectedItems;\r\n    console.log('Selection changed:', selectedItems);\r\n  } onSelectionChange2(selectedItems: HouseholdItem[]) {\r\n    this.selectedItems2 = selectedItems;\r\n    console.log('Selection 2 changed:', selectedItems);\r\n  }\r\n\r\n  onApiSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItemsApi = selectedItems;\r\n    console.log('API Selection changed:', selectedItems);\r\n  }\r\n\r\n  // 新增：自定義選擇變更處理\r\n  onCustomSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItemsCustom = selectedItems;\r\n    console.log('Custom Selection changed:', selectedItems);\r\n  }\r\n\r\n  // 新增：樓層測試選擇變更處理\r\n  onFloorSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItemsFloor = selectedItems;\r\n    console.log('Floor Selection changed:', selectedItems);\r\n  }\r\n\r\n  // 新增：API 測試相關方法\r\n  applyBuildCaseId() {\r\n    if (this.testBuildCaseId && this.testBuildCaseId > 0) {\r\n      this.appliedBuildCaseId = this.testBuildCaseId;\r\n      this.selectedHouseholdsApi = []; // 清空之前的選擇\r\n      this.selectedItemsApi = [];\r\n      this.apiLoadingStatus = `正在載入建案 ${this.testBuildCaseId} 的戶別資料...`;\r\n\r\n      // 模擬載入狀態更新\r\n      setTimeout(() => {\r\n        this.apiLoadingStatus = '';\r\n      }, 2000);\r\n\r\n      console.log('Applied build case ID:', this.appliedBuildCaseId);\r\n    } else {\r\n      alert('請輸入有效的建案ID');\r\n    }\r\n  }\r\n\r\n  clearBuildCaseId() {\r\n    this.appliedBuildCaseId = null;\r\n    this.selectedHouseholdsApi = [];\r\n    this.selectedItemsApi = [];\r\n    this.apiLoadingStatus = '';\r\n    console.log('Cleared build case ID');\r\n  } debugInfo() {\r\n    this.debugData = {\r\n      selectedHouseholds1: this.selectedHouseholds1,\r\n      selectedHouseholds2: this.selectedHouseholds2,\r\n      selectedHouseholds3: this.selectedHouseholds3,\r\n      selectedHouseholds4: this.selectedHouseholds4,\r\n      selectedHouseholdsApi: this.selectedHouseholdsApi,\r\n      customBuildingData: this.customBuildingData,\r\n      selectedItems: this.selectedItems,\r\n      selectedItems2: this.selectedItems2,\r\n      selectedItemsApi: this.selectedItemsApi,\r\n      appliedBuildCaseId: this.appliedBuildCaseId,\r\n      testBuildCaseId: this.testBuildCaseId\r\n    };\r\n    console.log('Debug info:', this.debugData);\r\n  }\r\n\r\n  testBuildingSelect() {\r\n    console.log('Testing building selection...');\r\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\r\n    // 主要是為了觸發除錯訊息\r\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\r\n  }\r\n\r\n  clearSelection2() {\r\n    this.selectedHouseholds2 = [];\r\n    this.selectedItems2 = [];\r\n  }\r\n\r\n  removeFromSelection2(householdCode: string) {\r\n    this.selectedHouseholds2 = this.selectedHouseholds2.filter(code => code !== householdCode);\r\n    this.selectedItems2 = this.selectedItems2.filter(item => item.code !== householdCode);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;IAgDYA,EADF,CAAAC,cAAA,cAA4D,YACnD;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC7BF,EAD6B,CAAAG,YAAA,EAAQ,EAC/B;;;;IADGH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAoB;;;;;;IA+BvBP,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA4F;IAA9CD,EAAA,CAAAQ,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,gBAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAU,oBAAA,CAAAN,gBAAA,CAAmC;IAAA,EAAC;IACzFV,EAAA,CAAAiB,SAAA,kBAAwC;IAE5CjB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;IAJLH,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAkB,kBAAA,MAAAR,gBAAA,MACA;;;;;;IAZJV,EAFJ,CAAAC,cAAA,cAA4E,cACtC,cACP;IACzBD,EAAA,CAAAiB,SAAA,kBAA2E;IAC3EjB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IACrEF,EADqE,CAAAG,YAAA,EAAO,EACtE;IACNH,EAAA,CAAAC,cAAA,iBAA2F;IAA5BD,EAAA,CAAAQ,UAAA,mBAAAW,sEAAA;MAAAnB,EAAA,CAAAW,aAAA,CAAAS,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAST,MAAA,CAAAe,eAAA,EAAiB;IAAA,EAAC;IACxFrB,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IAEJH,EADF,CAAAC,cAAA,cAAqC,cACF;IAC/BD,EAAA,CAAAsB,UAAA,KAAAC,qDAAA,mBAAmF;IAQzFvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAhB6BH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkB,kBAAA,qCAAAZ,MAAA,CAAAkB,mBAAA,CAAAC,MAAA,MAAsC;IAQnCzB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA0B,UAAA,YAAApB,MAAA,CAAAkB,mBAAA,CAAsB;;;;;IAqD5DxB,EADI,CAAAC,cAAA,aAAiE,SACjE;IAAAD,EAAA,CAAAE,MAAA,8EAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;;IAC9DF,EAD8D,CAAAG,YAAA,EAAM,EAC9D;;;;IADwBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAAsB,mBAAA,EAAgC;;;;;IAI5D5B,EADF,CAAAC,cAAA,aAAmE,SAC7D;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAChEF,EADgE,CAAAG,YAAA,EAAM,EAChE;;;;IAFAH,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAkB,kBAAA,kEAAAZ,MAAA,CAAAuB,kBAAA,MAA4C;IACpB7B,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAAwB,qBAAA,EAAkC;;;;;IAE9D9B,EADY,CAAAC,cAAA,aAA2D,SACnE;IAAAD,EAAA,CAAAE,MAAA,0FAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IACxDF,EADwD,CAAAG,YAAA,EAAM,EACxD;;;;IADwBH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAAyB,aAAA,EAA0B;;;;;IAItD/B,EADF,CAAAC,cAAA,aAA8D,SACxD;IAAAD,EAAA,CAAAE,MAAA,GAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;;IAC3DF,EAD2D,CAAAG,YAAA,EAAM,EAC3D;;;;IAFAH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAkB,kBAAA,8EAAAZ,MAAA,CAAAuB,kBAAA,MAA8C;IACtB7B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAA0B,gBAAA,EAA6B;;;;;IAIzDhC,EADF,CAAAC,cAAA,aAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,yHAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;;IAC9DF,EAD8D,CAAAG,YAAA,EAAM,EAC9D;;;;IADwBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAA2B,mBAAA,EAAgC;;;;;IAI5DjC,EADF,CAAAC,cAAA,aAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,iGAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA+B;;IAC7DF,EAD6D,CAAAG,YAAA,EAAM,EAC7D;;;;IADwBH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAA4B,kBAAA,EAA+B;;;;;IAI3DlC,EADF,CAAAC,cAAA,aAA4D,SACtD;IAAAD,EAAA,CAAAE,MAAA,8FAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;;IACzDF,EADyD,CAAAG,YAAA,EAAM,EACzD;;;;IADwBH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAA6B,cAAA,EAA2B;;;;;IA6BrDnC,EADF,CAAAC,cAAA,cAAoC,gBACR;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;;IACpDF,EADoD,CAAAG,YAAA,EAAM,EACpD;;;;IADwBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAA2B,WAAA,OAAArB,MAAA,CAAA8B,SAAA,EAAsB;;;AA4I9D,OAAM,MAAOC,6BAA6B;EAxU1CC,YAAA;IAyUE,KAAAV,mBAAmB,GAAa,EAAE;IAClC,KAAAJ,mBAAmB,GAAa,EAAE;IAClC,KAAAe,mBAAmB,GAAa,EAAE;IAClC,KAAAC,mBAAmB,GAAa,EAAE;IAClC,KAAAV,qBAAqB,GAAa,EAAE,CAAC,CAAC;IACtC,KAAAW,uBAAuB,GAAa,EAAE,CAAC,CAAC;IACxC,KAAAV,aAAa,GAAoB,EAAE;IACnC,KAAAI,cAAc,GAAoB,EAAE;IACpC,KAAAH,gBAAgB,GAAoB,EAAE,CAAC,CAAC;IACxC,KAAAC,mBAAmB,GAAoB,EAAE,CAAC,CAAC;IAC3C,KAAAC,kBAAkB,GAAoB,EAAE,CAAC,CAAC;IAC1C,KAAAE,SAAS,GAAQ,IAAI;IAErB;IACA,KAAAM,eAAe,GAAW,CAAC,CAAC,CAAC;IAC7B,KAAAb,kBAAkB,GAAkB,IAAI,CAAC,CAAC;IAC1C,KAAAtB,gBAAgB,GAAW,EAAE;IAC7B,KAAAoC,kBAAkB,GAAiB;MACjC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,EACtF;QAAEJ,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,CACvF;MACD,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEzB,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC0B,CAAC,EAAEC,CAAC,MAAM;QAC5CR,IAAI,EAAE,IAAIS,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CT,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,GAAG,EAAE,GAAGS,IAAI,CAACT,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAG;QACnCL,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAEI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;OACzB,CAAC;KACH;IAED;IACA,KAAAI,aAAa,GAAiB;MAC5B,MAAM,EAAEP,KAAK,CAACC,IAAI,CAAC;QAAEzB,MAAM,EAAE;MAAE,CAAE,EAAE,CAAC0B,CAAC,EAAEC,CAAC,KAAI;QAC1C,MAAMN,KAAK,GAAGS,IAAI,CAACT,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACrC,MAAMK,IAAI,GAAGJ,MAAM,CAACK,YAAY,CAAC,EAAE,GAAIN,CAAC,GAAG,CAAE,CAAC,CAAC,CAAC;QAChD,OAAO;UACLR,IAAI,EAAE,GAAGE,KAAK,CAACa,QAAQ,EAAE,CAACL,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGG,IAAI,EAAE;UACnDZ,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,GAAGA,KAAK,GAAG;UAClBC,UAAU,EAAE,KAAK;UACjBC,UAAU,EAAE;SACb;MACH,CAAC;KACF;;EAEDY,QAAQA,CAAA;IACN;IACA,IAAI,CAAChC,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrD;EACAiC,iBAAiBA,CAAC9B,aAA8B;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC+B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEhC,aAAa,CAAC;EAClD;EAAEiC,kBAAkBA,CAACjC,aAA8B;IACjD,IAAI,CAACI,cAAc,GAAGJ,aAAa;IACnC+B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhC,aAAa,CAAC;EACpD;EAEAkC,oBAAoBA,CAAClC,aAA8B;IACjD,IAAI,CAACC,gBAAgB,GAAGD,aAAa;IACrC+B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhC,aAAa,CAAC;EACtD;EAEA;EACAmC,uBAAuBA,CAACnC,aAA8B;IACpD,IAAI,CAACE,mBAAmB,GAAGF,aAAa;IACxC+B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhC,aAAa,CAAC;EACzD;EAEA;EACAoC,sBAAsBA,CAACpC,aAA8B;IACnD,IAAI,CAACG,kBAAkB,GAAGH,aAAa;IACvC+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhC,aAAa,CAAC;EACxD;EAEA;EACAqC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC1B,eAAe,IAAI,IAAI,CAACA,eAAe,GAAG,CAAC,EAAE;MACpD,IAAI,CAACb,kBAAkB,GAAG,IAAI,CAACa,eAAe;MAC9C,IAAI,CAACZ,qBAAqB,GAAG,EAAE,CAAC,CAAC;MACjC,IAAI,CAACE,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACzB,gBAAgB,GAAG,UAAU,IAAI,CAACmC,eAAe,WAAW;MAEjE;MACA2B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9D,gBAAgB,GAAG,EAAE;MAC5B,CAAC,EAAE,IAAI,CAAC;MAERuD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAClC,kBAAkB,CAAC;IAChE,CAAC,MAAM;MACLyC,KAAK,CAAC,YAAY,CAAC;IACrB;EACF;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC1C,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACE,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACzB,gBAAgB,GAAG,EAAE;IAC1BuD,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAAES,SAASA,CAAA;IACT,IAAI,CAACpC,SAAS,GAAG;MACfR,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CJ,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7Ce,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CV,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;MACjDa,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CZ,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCI,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCH,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3Ca,eAAe,EAAE,IAAI,CAACA;KACvB;IACDoB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC3B,SAAS,CAAC;EAC5C;EAEAqC,kBAAkBA,CAAA;IAChBX,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C;IACA;IACAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC7C;EAEA1C,eAAeA,CAAA;IACb,IAAI,CAACG,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACW,cAAc,GAAG,EAAE;EAC1B;EAEAnB,oBAAoBA,CAAC0D,aAAqB;IACxC,IAAI,CAAClD,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACmD,MAAM,CAAC/B,IAAI,IAAIA,IAAI,KAAK8B,aAAa,CAAC;IAC1F,IAAI,CAACvC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACwC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChC,IAAI,KAAK8B,aAAa,CAAC;EACvF;;;uCArIWrC,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAnUlCnF,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;UAAAD,EAAA,CAAAE,MAAA,uDAAQ;UACdF,EADc,CAAAG,YAAA,EAAK,EACF;UAEbH,EADJ,CAAAC,cAAA,mBAAc,aAAkC,SACxC;UAAAD,EAAA,CAAAE,MAAA,mDAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,+BAIgD;UAH9CD,EAAA,CAAAqF,gBAAA,2BAAAC,sFAAAC,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAAxD,mBAAA,EAAA2D,MAAA,MAAAH,GAAA,CAAAxD,mBAAA,GAAA2D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAGjCvF,EAAA,CAAAQ,UAAA,6BAAAiF,wFAAAF,MAAA;YAAA,OAAmBH,GAAA,CAAAvB,iBAAA,CAAA0B,MAAA,CAAyB;UAAA,EAAC;UAEjDvF,EADE,CAAAG,YAAA,EAAwB,EACpB;UAGJH,EADF,CAAAC,cAAA,aAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5CH,EADF,CAAAC,cAAA,cAA2B,gBACA;UAAAD,EAAA,CAAAE,MAAA,4BAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,gBAMwB;UAHtBD,EAAA,CAAAqF,gBAAA,2BAAAK,uEAAAH,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAA1C,eAAA,EAAA6C,MAAA,MAAAH,GAAA,CAAA1C,eAAA,GAAA6C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAH/BvF,EAAA,CAAAG,YAAA,EAMwB;UACxBH,EAAA,CAAAC,cAAA,iBAAkF;UAA7BD,EAAA,CAAAQ,UAAA,mBAAAmF,gEAAA;YAAA,OAASP,GAAA,CAAAhB,gBAAA,EAAkB;UAAA,EAAC;UAC/EpE,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAyF;UAA7BD,EAAA,CAAAQ,UAAA,mBAAAoF,gEAAA;YAAA,OAASR,GAAA,CAAAb,gBAAA,EAAkB;UAAA,EAAC;UACtFvE,EAAA,CAAAE,MAAA,sBACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UAEJH,EADF,CAAAC,cAAA,cAAkB,gCAMmC;UAJjDD,EAAA,CAAAqF,gBAAA,2BAAAQ,uFAAAN,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAAtD,qBAAA,EAAAyD,MAAA,MAAAH,GAAA,CAAAtD,qBAAA,GAAAyD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UAInCvF,EAAA,CAAAQ,UAAA,6BAAAsF,yFAAAP,MAAA;YAAA,OAAmBH,GAAA,CAAAnB,oBAAA,CAAAsB,MAAA,CAA4B;UAAA,EAAC;UAEpDvF,EADE,CAAAG,YAAA,EAAwB,EACpB;UACNH,EAAA,CAAAsB,UAAA,KAAAyE,6CAAA,iBAA4D;UAG9D/F,EAAA,CAAAG,YAAA,EAAM;UACJH,EADI,CAAAC,cAAA,cAA0B,UAC1B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGbH,EAFJ,CAAAC,cAAA,eAA4J,iBAC3H,cACrB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAAAH,EAAA,CAAAE,MAAA,qVACxB;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UACNH,EAAA,CAAAC,cAAA,iCAMiD;UAL/CD,EAAA,CAAAqF,gBAAA,2BAAAW,uFAAAT,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAA5D,mBAAA,EAAA+D,MAAA,MAAAH,GAAA,CAAA5D,mBAAA,GAAA+D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAKjCvF,EAAA,CAAAQ,UAAA,6BAAAyF,yFAAAV,MAAA;YAAA,OAAmBH,GAAA,CAAApB,kBAAA,CAAAuB,MAAA,CAA0B;UAAA,EAAC;UAChDvF,EAAA,CAAAG,YAAA,EAAwB;UAGxBH,EAAA,CAAAsB,UAAA,KAAA4E,6CAAA,mBAA4E;UAqB9ElG,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,8DAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,iCAI6B;UAH3BD,EAAA,CAAAqF,gBAAA,2BAAAc,uFAAAZ,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAA7C,mBAAA,EAAAgD,MAAA,MAAAH,GAAA,CAAA7C,mBAAA,GAAAgD,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAKrCvF,EADE,CAAAG,YAAA,EAAwB,EACpB;UACJH,EADY,CAAAC,cAAA,cAA0B,UAClC;UAAAD,EAAA,CAAAE,MAAA,yFAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrBH,EAFJ,CAAAC,cAAA,eAA4J,iBAC3H,cACrB;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAAAH,EAAA,CAAAE,MAAA,uIACxB;UAAAF,EAAA,CAAAiB,SAAA,UAAI;UAAAjB,EAAA,CAAAE,MAAA,wFACJ;UAAAF,EAAA,CAAAiB,SAAA,UAAI;UAAAjB,EAAA,CAAAE,MAAA,4EACJ;UAAAF,EAAA,CAAAiB,SAAA,UAAI;UAAAjB,EAAA,CAAAE,MAAA,kFACJ;UAAAF,EAAA,CAAAiB,SAAA,UAAI;UAAAjB,EAAA,CAAAE,MAAA,wLACN;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UACNH,EAAA,CAAAC,cAAA,iCAIsD;UAHpDD,EAAA,CAAAqF,gBAAA,2BAAAe,uFAAAb,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAA5C,mBAAA,EAAA+C,MAAA,MAAAH,GAAA,CAAA5C,mBAAA,GAAA+C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAGjCvF,EAAA,CAAAQ,UAAA,6BAAA6F,yFAAAd,MAAA;YAAA,OAAmBH,GAAA,CAAAlB,uBAAA,CAAAqB,MAAA,CAA+B;UAAA,EAAC;UAEvDvF,EADE,CAAAG,YAAA,EAAwB,EACpB;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,6EAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGnBH,EAFJ,CAAAC,cAAA,eAA+J,iBAC9H,cACrB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAAAH,EAAA,CAAAE,MAAA,gLACxB;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UACNH,EAAA,CAAAC,cAAA,iCAKqD;UAJnDD,EAAA,CAAAqF,gBAAA,2BAAAiB,uFAAAf,MAAA;YAAAvF,EAAA,CAAAwF,kBAAA,CAAAJ,GAAA,CAAA3C,uBAAA,EAAA8C,MAAA,MAAAH,GAAA,CAAA3C,uBAAA,GAAA8C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UAIrCvF,EAAA,CAAAQ,UAAA,6BAAA+F,yFAAAhB,MAAA;YAAA,OAAmBH,GAAA,CAAAjB,sBAAA,CAAAoB,MAAA,CAA8B;UAAA,EAAC;UAEtDvF,EADE,CAAAG,YAAA,EAAwB,EACpB;UA4BNH,EA5BM,CAAAsB,UAAA,KAAAkF,6CAAA,kBAAiE,KAAAC,6CAAA,kBAKJ,KAAAC,6CAAA,kBAGM,KAAAC,6CAAA,kBAKX,KAAAC,6CAAA,kBAKG,KAAAC,6CAAA,kBAKD,KAAAC,6CAAA,kBAKJ;UAI1D9G,EADI,CAAAC,cAAA,cAA0B,UAC1B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGbH,EAFJ,CAAAC,cAAA,eAAuE,eACsC,cACtD;UAAAD,EAAA,CAAAE,MAAA,6EAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtEH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClGH,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,IACF;UACFF,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAA2G,cACtD;UAAAD,EAAA,CAAAE,MAAA,yFAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClGH,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,IACF;UAENF,EAFM,CAAAG,YAAA,EAAM,EACF,EAAgB,EACpB;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,kBAAkF;UAAtBD,EAAA,CAAAQ,UAAA,mBAAAuG,gEAAA;YAAA,OAAS3B,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAC/ExE,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsF;UAA/BD,EAAA,CAAAQ,UAAA,mBAAAwG,gEAAA;YAAA,OAAS5B,GAAA,CAAAX,kBAAA,EAAoB;UAAA,EAAC;UACnFzE,EAAA,CAAAE,MAAA,0CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAsB,UAAA,KAAA2F,6CAAA,kBAAoC;UAItCjH,EAAA,CAAAG,YAAA,EAAM;UACJH,EADI,CAAAC,cAAA,cAA0B,UAC1B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAiB,SAAA,yBAAuC;UACzCjB,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAA0B,WACpB;UAAAD,EAAA,CAAAE,MAAA,yDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAiB,SAAA,iCAAqD;UAG3DjB,EAFI,CAAAG,YAAA,EAAM,EACO,EACP;;;UA9LFH,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAAxD,mBAAA,CAAiC;UAEjC5B,EAAA,CAAA0B,UAAA,qBAAoB;UAMlB1B,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAkB,kBAAA,oDAAAkE,GAAA,CAAA1C,eAAA,MAAqC;UAMrC1C,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAA1C,eAAA,CAA6B;UAa7B1C,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAAtD,qBAAA,CAAmC;UAGnC9B,EADA,CAAA0B,UAAA,gBAAA0D,GAAA,CAAAvD,kBAAA,CAAkC,qBACd;UAIlB7B,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAA7E,gBAAA,CAAsB;UAW1BP,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAA5D,mBAAA,CAAiC;UAIjCxB,EAFA,CAAA0B,UAAA,2BAA0B,qBACN,uBAAA0D,GAAA,CAAAxD,mBAAA,CACsB;UAKtC5B,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAA5D,mBAAA,CAAAC,MAAA,KAAoC;UA0BxCzB,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAA7C,mBAAA,CAAiC;UAGjCvC,EADA,CAAA0B,UAAA,sBAAqB,2BACK;UAc1B1B,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAA5C,mBAAA,CAAiC;UAEjCxC,EAAA,CAAA0B,UAAA,iBAAA0D,GAAA,CAAAzC,kBAAA,CAAmC;UAanC3C,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAkH,gBAAA,YAAA9B,GAAA,CAAA3C,uBAAA,CAAqC;UAGrCzC,EADA,CAAA0B,UAAA,iBAAA0D,GAAA,CAAA5B,aAAA,CAA8B,qBACV;UAGSxD,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAxD,mBAAA,CAAAH,MAAA,KAAoC;UAK1CzB,EAAA,CAAAI,SAAA,EAAsC;UAAtCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAtD,qBAAA,CAAAL,MAAA,KAAsC;UAGxBzB,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAArD,aAAA,CAAAN,MAAA,KAA8B;UAK5CzB,EAAA,CAAAI,SAAA,EAAiC;UAAjCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAApD,gBAAA,CAAAP,MAAA,KAAiC;UAKjCzB,EAAA,CAAAI,SAAA,EAAoC;UAApCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAnD,mBAAA,CAAAR,MAAA,KAAoC;UAKpCzB,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAlD,kBAAA,CAAAT,MAAA,KAAmC;UAKnCzB,EAAA,CAAAI,SAAA,EAA+B;UAA/BJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAjD,cAAA,CAAAV,MAAA,KAA+B;UAQGzB,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAkB,kBAAA,yBAAAkE,GAAA,CAAAxD,mBAAA,CAAAH,MAAA,wBAAuC;UAE5FzB,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAkB,kBAAA,MAAAkE,GAAA,CAAAxD,mBAAA,CAAAuF,IAAA,0CACF;UAIuDnH,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAkB,kBAAA,yBAAAkE,GAAA,CAAA5D,mBAAA,CAAAC,MAAA,wBAAuC;UAE5FzB,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAkB,kBAAA,MAAAkE,GAAA,CAAA5D,mBAAA,CAAA2F,IAAA,0CACF;UAYEnH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA0B,UAAA,SAAA0D,GAAA,CAAAhD,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}