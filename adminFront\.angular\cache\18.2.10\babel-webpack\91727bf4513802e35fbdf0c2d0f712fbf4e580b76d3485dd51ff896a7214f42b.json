{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPlanGetPlanListPost$Json } from '../fn/plan/api-plan-get-plan-list-post-json';\nimport { apiPlanGetPlanListPost$Plain } from '../fn/plan/api-plan-get-plan-list-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let PlanService = /*#__PURE__*/(() => {\n  class PlanService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiPlanGetPlanListPost()` */\n    static {\n      this.ApiPlanGetPlanListPostPath = '/api/Plan/GetPlanList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPlanGetPlanListPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiPlanGetPlanListPost$Plain$Response(params, context) {\n      return apiPlanGetPlanListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiPlanGetPlanListPost$Plain(params, context) {\n      return this.apiPlanGetPlanListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiPlanGetPlanListPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiPlanGetPlanListPost$Json$Response(params, context) {\n      return apiPlanGetPlanListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiPlanGetPlanListPost$Json(params, context) {\n      return this.apiPlanGetPlanListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function PlanService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PlanService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PlanService,\n        factory: PlanService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PlanService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}