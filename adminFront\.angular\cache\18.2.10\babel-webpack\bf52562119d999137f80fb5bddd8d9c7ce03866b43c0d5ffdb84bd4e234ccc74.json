{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFinalDocumentCreateFinalDocPost$Json } from '../fn/final-document/api-final-document-create-final-doc-post-json';\nimport { apiFinalDocumentCreateFinalDocPost$Plain } from '../fn/final-document/api-final-document-create-final-doc-post-plain';\nimport { apiFinalDocumentGetFinalDocAfterPost$Json } from '../fn/final-document/api-final-document-get-final-doc-after-post-json';\nimport { apiFinalDocumentGetFinalDocAfterPost$Plain } from '../fn/final-document/api-final-document-get-final-doc-after-post-plain';\nimport { apiFinalDocumentGetFinalDocBeforePost$Json } from '../fn/final-document/api-final-document-get-final-doc-before-post-json';\nimport { apiFinalDocumentGetFinalDocBeforePost$Plain } from '../fn/final-document/api-final-document-get-final-doc-before-post-plain';\nimport { apiFinalDocumentGetListFinalDocByHousePost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-json';\nimport { apiFinalDocumentGetListFinalDocByHousePost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-plain';\nimport { apiFinalDocumentGetListFinalDocPost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-post-json';\nimport { apiFinalDocumentGetListFinalDocPost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-post-plain';\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Json } from '../fn/final-document/api-final-document-get-list-special-change-available-post-json';\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Plain } from '../fn/final-document/api-final-document-get-list-special-change-available-post-plain';\nimport { apiFinalDocumentUpdateSignPost$Json } from '../fn/final-document/api-final-document-update-sign-post-json';\nimport { apiFinalDocumentUpdateSignPost$Plain } from '../fn/final-document/api-final-document-update-sign-post-plain';\nimport { apiFinalDocumentUploadFinalDocPost$Json } from '../fn/final-document/api-final-document-upload-final-doc-post-json';\nimport { apiFinalDocumentUploadFinalDocPost$Plain } from '../fn/final-document/api-final-document-upload-final-doc-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let FinalDocumentService = /*#__PURE__*/(() => {\n  class FinalDocumentService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiFinalDocumentGetFinalDocBeforePost()` */\n    static {\n      this.ApiFinalDocumentGetFinalDocBeforePostPath = '/api/FinalDocument/GetFinalDocBefore';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params, context) {\n      return apiFinalDocumentGetFinalDocBeforePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocBeforePost$Plain(params, context) {\n      return this.apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocBeforePost$Json$Response(params, context) {\n      return apiFinalDocumentGetFinalDocBeforePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocBeforePost$Json(params, context) {\n      return this.apiFinalDocumentGetFinalDocBeforePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentUpdateSignPost()` */\n    static {\n      this.ApiFinalDocumentUpdateSignPostPath = '/api/FinalDocument/UpdateSign';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentUpdateSignPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentUpdateSignPost$Plain$Response(params, context) {\n      return apiFinalDocumentUpdateSignPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentUpdateSignPost$Plain(params, context) {\n      return this.apiFinalDocumentUpdateSignPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentUpdateSignPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentUpdateSignPost$Json$Response(params, context) {\n      return apiFinalDocumentUpdateSignPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentUpdateSignPost$Json(params, context) {\n      return this.apiFinalDocumentUpdateSignPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentGetFinalDocAfterPost()` */\n    static {\n      this.ApiFinalDocumentGetFinalDocAfterPostPath = '/api/FinalDocument/GetFinalDocAfter';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params, context) {\n      return apiFinalDocumentGetFinalDocAfterPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocAfterPost$Plain(params, context) {\n      return this.apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocAfterPost$Json$Response(params, context) {\n      return apiFinalDocumentGetFinalDocAfterPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetFinalDocAfterPost$Json(params, context) {\n      return this.apiFinalDocumentGetFinalDocAfterPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentGetListFinalDocPost()` */\n    static {\n      this.ApiFinalDocumentGetListFinalDocPostPath = '/api/FinalDocument/GetListFinalDoc';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocPost$Plain$Response(params, context) {\n      return apiFinalDocumentGetListFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocPost$Plain(params, context) {\n      return this.apiFinalDocumentGetListFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocPost$Json$Response(params, context) {\n      return apiFinalDocumentGetListFinalDocPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocPost$Json(params, context) {\n      return this.apiFinalDocumentGetListFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentCreateFinalDocPost()` */\n    static {\n      this.ApiFinalDocumentCreateFinalDocPostPath = '/api/FinalDocument/CreateFinalDoc';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentCreateFinalDocPost$Plain$Response(params, context) {\n      return apiFinalDocumentCreateFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentCreateFinalDocPost$Plain(params, context) {\n      return this.apiFinalDocumentCreateFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentCreateFinalDocPost$Json$Response(params, context) {\n      return apiFinalDocumentCreateFinalDocPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentCreateFinalDocPost$Json(params, context) {\n      return this.apiFinalDocumentCreateFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentGetListSpecialChangeAvailablePost()` */\n    static {\n      this.ApiFinalDocumentGetListSpecialChangeAvailablePostPath = '/api/FinalDocument/GetListSpecialChangeAvailable';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params, context) {\n      return apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(params, context) {\n      return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params, context) {\n      return apiFinalDocumentGetListSpecialChangeAvailablePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListSpecialChangeAvailablePost$Json(params, context) {\n      return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentGetListFinalDocByHousePost()` */\n    static {\n      this.ApiFinalDocumentGetListFinalDocByHousePostPath = '/api/FinalDocument/GetListFinalDocByHouse';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params, context) {\n      return apiFinalDocumentGetListFinalDocByHousePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocByHousePost$Plain(params, context) {\n      return this.apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params, context) {\n      return apiFinalDocumentGetListFinalDocByHousePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFinalDocumentGetListFinalDocByHousePost$Json(params, context) {\n      return this.apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFinalDocumentUploadFinalDocPost()` */\n    static {\n      this.ApiFinalDocumentUploadFinalDocPostPath = '/api/FinalDocument/UploadFinalDoc';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiFinalDocumentUploadFinalDocPost$Plain$Response(params, context) {\n      return apiFinalDocumentUploadFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiFinalDocumentUploadFinalDocPost$Plain(params, context) {\n      return this.apiFinalDocumentUploadFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiFinalDocumentUploadFinalDocPost$Json$Response(params, context) {\n      return apiFinalDocumentUploadFinalDocPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiFinalDocumentUploadFinalDocPost$Json(params, context) {\n      return this.apiFinalDocumentUploadFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function FinalDocumentService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FinalDocumentService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FinalDocumentService,\n        factory: FinalDocumentService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FinalDocumentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}