{"ast": null, "code": "import { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor } from '@angular/common';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction FinaldochouseManagementComponent_tr_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 21)(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 24)(6, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_tr_30_Template_button_click_6_listener() {\n      const data_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openPdfInNewTab(data_r2));\n    });\n    i0.ɵɵtext(7, \"\\u9023\\u7D50\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r2.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r2.CSignDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !data_r2.CFileAfter && !data_r2.CFileBefore);\n  }\n}\nexport let FinaldochouseManagementComponent = /*#__PURE__*/(() => {\n  class FinaldochouseManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, pettern, router, route, destroyref, _eventService, location) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this.finalDocumentService = finalDocumentService;\n      this.pettern = pettern;\n      this.router = router;\n      this.route = route;\n      this.destroyref = destroyref;\n      this._eventService = _eventService;\n      this.location = location;\n      this.calendarOptions = {\n        plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n        locale: 'zh-tw',\n        headerToolbar: {\n          left: 'prev',\n          center: 'title',\n          right: 'next'\n        }\n      };\n      // request\n      this.getListFinalDocRequest = {};\n      // response\n      this.listFinalDoc = [];\n      this.maxDate = new Date();\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id1');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          const idParam2 = params.get('id2');\n          const id2 = idParam2 ? +idParam2 : 0;\n          this.currentHouseID = id2;\n          this.getList();\n        }\n      });\n    }\n    openPdfInNewTab(data) {\n      if (data) {\n        if (data.CSignDate && data.CSign) {\n          console.log(data.CFileAfter);\n          window.open(data.CFileAfter, '_blank');\n        } else {\n          console.log(data.CFileBefore);\n          window.open(data.CFileBefore, '_blank');\n        }\n      }\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    getList() {\n      this.getListFinalDocRequest.PageSize = this.pageSize;\n      this.getListFinalDocRequest.PageIndex = this.pageIndex;\n      if (this.currentHouseID != 0) {\n        this.getListFinalDocRequest.CHouseID = this.currentHouseID;\n        this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({\n          body: this.getListFinalDocRequest\n        }).pipe().subscribe(res => {\n          if (res.StatusCode == 0) {\n            if (res.Entries) {\n              this.listFinalDoc = res.Entries;\n              this.totalRecords = res.TotalItems;\n              if (this.listFinalDoc) {\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\n                  if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\n                }\n              }\n            }\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function FinaldochouseManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FinaldochouseManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.FinalDocumentService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i8.ActivatedRoute), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.Location));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FinaldochouseManagementComponent,\n        selectors: [[\"app-finaldochouse-management\"]],\n        viewQuery: function FinaldochouseManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 10,\n        consts: [[\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"flex\", \"form-group\", \"col-12\", \"col-md-9\", \"text-right\"], [\"for\", \"date-select1\", 1, \"mr-3\", \"mt-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\", \"maxDate\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\", \"mt-2\"], [1, \"form-group\", \"col-12\", \"col-md-3\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-4\"], [\"scope\", \"col\", 1, \"col-3\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-5\"], [1, \"col-4\"], [1, \"col-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\", \"disabled\"]],\n        template: function FinaldochouseManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\", 1)(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"span\", 5);\n            i0.ɵɵtext(8, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"p-calendar\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateStart, $event) || (ctx.getListFinalDocRequest.CDateStart = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"span\", 7);\n            i0.ɵɵtext(11, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"p-calendar\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_12_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateEnd, $event) || (ctx.getListFinalDocRequest.CDateEnd = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_14_listener() {\n              return ctx.getList();\n            });\n            i0.ɵɵelement(15, \"i\", 10);\n            i0.ɵɵtext(16, \"\\u67E5\\u8A62\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(17, \"nb-card-body\", 1)(18, \"div\", 2)(19, \"div\", 11)(20, \"table\", 12)(21, \"thead\")(22, \"tr\", 13)(23, \"th\", 14);\n            i0.ɵɵtext(24, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 15);\n            i0.ɵɵtext(26, \"\\u5BA2\\u6236\\u7C3D\\u540D\\u6642\\u9593\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 16);\n            i0.ɵɵtext(28, \"\\u9023\\u7D50\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(29, \"tbody\");\n            i0.ɵɵtemplate(30, FinaldochouseManagementComponent_tr_30_Template, 8, 3, \"tr\", 17);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"ngx-pagination\", 18);\n            i0.ɵɵtwoWayListener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_31_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_31_listener() {\n              return ctx.getList();\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 19)(34, \"button\", 20);\n            i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_34_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵtext(35, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateStart);\n            i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateEnd);\n            i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n            i0.ɵɵadvance(18);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n            i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, FormsModule, i11.NgControlStatus, i11.NgModel, NbSelectModule, NbOptionModule, NgFor, PaginationComponent, NbCheckboxModule, FullCalendarModule, CalendarModule, i12.Calendar]\n      });\n    }\n  }\n  return FinaldochouseManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}