{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserService } from './users.service';\nimport { ElectricityService } from './electricity.service';\nimport { SmartTableService } from './smart-table.service';\nimport { UserActivityService } from './user-activity.service';\nimport { OrdersChartService } from './orders-chart.service';\nimport { ProfitChartService } from './profit-chart.service';\nimport { TrafficListService } from './traffic-list.service';\nimport { PeriodsService } from './periods.service';\n//import { EarningService } from './earning.service';\nimport { OrdersProfitChartService } from './orders-profit-chart.service';\nimport { TrafficBarService } from './traffic-bar.service';\nimport { ProfitBarAnimationChartService } from './profit-bar-animation-chart.service';\nimport { TemperatureHumidityService } from './temperature-humidity.service';\nimport { SolarService } from './solar.service';\nimport { TrafficChartService } from './traffic-chart.service';\nimport { StatsBarService } from './stats-bar.service';\nimport { CountryOrderService } from './country-order.service';\nimport { StatsProgressBarService } from './stats-progress-bar.service';\nimport { VisitorsAnalyticsService } from './visitors-analytics.service';\nimport { SecurityCamerasService } from './security-cameras.service';\nimport * as i0 from \"@angular/core\";\nconst SERVICES = [UserService, ElectricityService, SmartTableService, UserActivityService, OrdersChartService, ProfitChartService, TrafficListService, PeriodsService,\n//EarningService,\nOrdersProfitChartService, TrafficBarService, ProfitBarAnimationChartService, TemperatureHumidityService, SolarService, TrafficChartService, StatsBarService, CountryOrderService, StatsProgressBarService, VisitorsAnalyticsService, SecurityCamerasService];\nexport class MockDataModule {\n  static forRoot() {\n    return {\n      ngModule: MockDataModule,\n      providers: [...SERVICES]\n    };\n  }\n  static {\n    this.ɵfac = function MockDataModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MockDataModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MockDataModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [...SERVICES],\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MockDataModule, {\n    imports: [CommonModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UserService", "ElectricityService", "SmartTableService", "UserActivityService", "OrdersChartService", "ProfitChartService", "TrafficListService", "PeriodsService", "OrdersProfitChartService", "TrafficBarService", "ProfitBarAnimationChartService", "TemperatureHumidityService", "SolarService", "TrafficChartService", "StatsBarService", "CountryOrderService", "StatsProgressBarService", "VisitorsAnalyticsService", "SecurityCamerasService", "SERVICES", "MockDataModule", "forRoot", "ngModule", "providers", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\mock-data.module.ts"], "sourcesContent": ["import { NgModule, ModuleWithProviders } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { UserService } from './users.service';\r\nimport { ElectricityService } from './electricity.service';\r\nimport { SmartTableService } from './smart-table.service';\r\nimport { UserActivityService } from './user-activity.service';\r\nimport { OrdersChartService } from './orders-chart.service';\r\nimport { ProfitChartService } from './profit-chart.service';\r\nimport { TrafficListService } from './traffic-list.service';\r\nimport { PeriodsService } from './periods.service';\r\n//import { EarningService } from './earning.service';\r\nimport { OrdersProfitChartService } from './orders-profit-chart.service';\r\nimport { TrafficBarService } from './traffic-bar.service';\r\nimport { ProfitBarAnimationChartService } from './profit-bar-animation-chart.service';\r\nimport { TemperatureHumidityService } from './temperature-humidity.service';\r\nimport { SolarService } from './solar.service';\r\nimport { TrafficChartService } from './traffic-chart.service';\r\nimport { StatsBarService } from './stats-bar.service';\r\nimport { CountryOrderService } from './country-order.service';\r\nimport { StatsProgressBarService } from './stats-progress-bar.service';\r\nimport { VisitorsAnalyticsService } from './visitors-analytics.service';\r\nimport { SecurityCamerasService } from './security-cameras.service';\r\n\r\nconst SERVICES = [\r\n  UserService,\r\n  ElectricityService,\r\n  SmartTableService,\r\n  UserActivityService,\r\n  OrdersChartService,\r\n  ProfitChartService,\r\n  TrafficListService,\r\n  PeriodsService,\r\n  //EarningService,\r\n  OrdersProfitChartService,\r\n  TrafficBarService,\r\n  ProfitBarAnimationChartService,\r\n  TemperatureHumidityService,\r\n  SolarService,\r\n  TrafficChartService,\r\n  StatsBarService,\r\n  CountryOrderService,\r\n  StatsProgressBarService,\r\n  VisitorsAnalyticsService,\r\n  SecurityCamerasService,\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n  ],\r\n  providers: [\r\n    ...SERVICES,\r\n  ],\r\n})\r\nexport class MockDataModule {\r\n  static forRoot(): ModuleWithProviders<MockDataModule> {\r\n    return {\r\n      ngModule: MockDataModule,\r\n      providers: [\r\n        ...SERVICES,\r\n      ],\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;AAClD;AACA,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,8BAA8B,QAAQ,sCAAsC;AACrF,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,sBAAsB,QAAQ,4BAA4B;;AAEnE,MAAMC,QAAQ,GAAG,CACfnB,WAAW,EACXC,kBAAkB,EAClBC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,kBAAkB,EAClBC,kBAAkB,EAClBC,cAAc;AACd;AACAC,wBAAwB,EACxBC,iBAAiB,EACjBC,8BAA8B,EAC9BC,0BAA0B,EAC1BC,YAAY,EACZC,mBAAmB,EACnBC,eAAe,EACfC,mBAAmB,EACnBC,uBAAuB,EACvBC,wBAAwB,EACxBC,sBAAsB,CACvB;AAUD,OAAM,MAAOE,cAAc;EACzB,OAAOC,OAAOA,CAAA;IACZ,OAAO;MACLC,QAAQ,EAAEF,cAAc;MACxBG,SAAS,EAAE,CACT,GAAGJ,QAAQ;KAEd;EACH;;;uCARWC,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAJd,CACT,GAAGD,QAAQ,CACZ;MAAAK,OAAA,GAJCzB,YAAY;IAAA;EAAA;;;2EAMHqB,cAAc;IAAAI,OAAA,GANvBzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}