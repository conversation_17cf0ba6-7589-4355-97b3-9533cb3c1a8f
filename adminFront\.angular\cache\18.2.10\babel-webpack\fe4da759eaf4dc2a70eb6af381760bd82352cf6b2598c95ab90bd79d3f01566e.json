{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseServiceAPI } from '../base-service';\nimport { apiBaseGetFunctionPost$Json } from '../fn/base/api-base-get-function-post-json';\nimport { apiBaseGetFunctionPost$Plain } from '../fn/base/api-base-get-function-post-plain';\nimport { apiBaseGetStatusPost$Json } from '../fn/base/api-base-get-status-post-json';\nimport { apiBaseGetStatusPost$Plain } from '../fn/base/api-base-get-status-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class BaseService extends BaseServiceAPI {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiBaseGetStatusPost()` */\n  static {\n    this.ApiBaseGetStatusPostPath = '/api/Base/GetStatus';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseGetStatusPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetStatusPost$Plain$Response(params, context) {\n    return apiBaseGetStatusPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseGetStatusPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetStatusPost$Plain(params, context) {\n    return this.apiBaseGetStatusPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseGetStatusPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetStatusPost$Json$Response(params, context) {\n    return apiBaseGetStatusPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseGetStatusPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetStatusPost$Json(params, context) {\n    return this.apiBaseGetStatusPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiBaseGetFunctionPost()` */\n  static {\n    this.ApiBaseGetFunctionPostPath = '/api/Base/GetFunction';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseGetFunctionPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetFunctionPost$Plain$Response(params, context) {\n    return apiBaseGetFunctionPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseGetFunctionPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetFunctionPost$Plain(params, context) {\n    return this.apiBaseGetFunctionPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiBaseGetFunctionPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetFunctionPost$Json$Response(params, context) {\n    return apiBaseGetFunctionPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiBaseGetFunctionPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiBaseGetFunctionPost$Json(params, context) {\n    return this.apiBaseGetFunctionPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function BaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BaseService,\n      factory: BaseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseServiceAPI", "apiBaseGetFunctionPost$Json", "apiBaseGetFunctionPost$Plain", "apiBaseGetStatusPost$Json", "apiBaseGetStatusPost$Plain", "BaseService", "constructor", "config", "http", "ApiBaseGetStatusPostPath", "apiBaseGetStatusPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiBaseGetStatusPost$Json$Response", "ApiBaseGetFunctionPostPath", "apiBaseGetFunctionPost$Plain$Response", "apiBaseGetFunctionPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\base.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseServiceAPI } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiBaseGetFunctionPost$Json } from '../fn/base/api-base-get-function-post-json';\r\nimport { ApiBaseGetFunctionPost$Json$Params } from '../fn/base/api-base-get-function-post-json';\r\nimport { apiBaseGetFunctionPost$Plain } from '../fn/base/api-base-get-function-post-plain';\r\nimport { ApiBaseGetFunctionPost$Plain$Params } from '../fn/base/api-base-get-function-post-plain';\r\nimport { apiBaseGetStatusPost$Json } from '../fn/base/api-base-get-status-post-json';\r\nimport { ApiBaseGetStatusPost$Json$Params } from '../fn/base/api-base-get-status-post-json';\r\nimport { apiBaseGetStatusPost$Plain } from '../fn/base/api-base-get-status-post-plain';\r\nimport { ApiBaseGetStatusPost$Plain$Params } from '../fn/base/api-base-get-status-post-plain';\r\nimport { EnumResponseListResponseBase } from '../models/enum-response-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class BaseService extends BaseServiceAPI {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiBaseGetStatusPost()` */\r\n  static readonly ApiBaseGetStatusPostPath = '/api/Base/GetStatus';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseGetStatusPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetStatusPost$Plain$Response(params?: ApiBaseGetStatusPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseGetStatusPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseGetStatusPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetStatusPost$Plain(params?: ApiBaseGetStatusPost$Plain$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseGetStatusPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseGetStatusPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetStatusPost$Json$Response(params?: ApiBaseGetStatusPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseGetStatusPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseGetStatusPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetStatusPost$Json(params?: ApiBaseGetStatusPost$Json$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseGetStatusPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiBaseGetFunctionPost()` */\r\n  static readonly ApiBaseGetFunctionPostPath = '/api/Base/GetFunction';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseGetFunctionPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetFunctionPost$Plain$Response(params?: ApiBaseGetFunctionPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseGetFunctionPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseGetFunctionPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetFunctionPost$Plain(params?: ApiBaseGetFunctionPost$Plain$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseGetFunctionPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiBaseGetFunctionPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetFunctionPost$Json$Response(params?: ApiBaseGetFunctionPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<EnumResponseListResponseBase>> {\r\n    return apiBaseGetFunctionPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiBaseGetFunctionPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiBaseGetFunctionPost$Json(params?: ApiBaseGetFunctionPost$Json$Params, context?: HttpContext): Observable<EnumResponseListResponseBase> {\r\n    return this.apiBaseGetFunctionPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<EnumResponseListResponseBase>): EnumResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,cAAc,QAAQ,iBAAiB;AAIhD,SAASC,2BAA2B,QAAQ,4CAA4C;AAExF,SAASC,4BAA4B,QAAQ,6CAA6C;AAE1F,SAASC,yBAAyB,QAAQ,0CAA0C;AAEpF,SAASC,0BAA0B,QAAQ,2CAA2C;;;;AAKtF,OAAM,MAAOC,WAAY,SAAQL,cAAc;EAC7CM,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,wBAAwB,GAAG,qBAAqB;EAAC;EAEjE;;;;;;EAMAC,mCAAmCA,CAACC,MAA0C,EAAEC,OAAqB;IACnG,OAAOR,0BAA0B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAR,0BAA0BA,CAACO,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAACF,mCAAmC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnEf,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAC,kCAAkCA,CAACN,MAAyC,EAAEC,OAAqB;IACjG,OAAOT,yBAAyB,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5E;EAEA;;;;;;EAMAT,yBAAyBA,CAACQ,MAAyC,EAAEC,OAAqB;IACxF,OAAO,IAAI,CAACK,kCAAkC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClEf,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAE,0BAA0B,GAAG,uBAAuB;EAAC;EAErE;;;;;;EAMAC,qCAAqCA,CAACR,MAA4C,EAAEC,OAAqB;IACvG,OAAOV,4BAA4B,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAV,4BAA4BA,CAACS,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACO,qCAAqC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrEf,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAI,oCAAoCA,CAACT,MAA2C,EAAEC,OAAqB;IACrG,OAAOX,2BAA2B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAX,2BAA2BA,CAACU,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAACQ,oCAAoC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpEf,GAAG,CAAEgB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;;;uCAjGWX,WAAW,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXrB,WAAW;MAAAsB,OAAA,EAAXtB,WAAW,CAAAuB,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}