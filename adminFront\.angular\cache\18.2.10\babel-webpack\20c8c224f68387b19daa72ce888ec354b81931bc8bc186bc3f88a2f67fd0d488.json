{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Ukrainian [uk]\n//! author : zemlanin : https://github.com/zemlanin\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function plural(word, num) {\n    var forms = word.split('_');\n    return num % 10 === 1 && num % 100 !== 11 ? forms[0] : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20) ? forms[1] : forms[2];\n  }\n  function relativeTimeWithPlural(number, withoutSuffix, key) {\n    var format = {\n      ss: withoutSuffix ? 'секунда_секунди_секунд' : 'секунду_секунди_секунд',\n      mm: withoutSuffix ? 'хвилина_хвилини_хвилин' : 'хвилину_хвилини_хвилин',\n      hh: withoutSuffix ? 'година_години_годин' : 'годину_години_годин',\n      dd: 'день_дні_днів',\n      MM: 'місяць_місяці_місяців',\n      yy: 'рік_роки_років'\n    };\n    if (key === 'm') {\n      return withoutSuffix ? 'хвилина' : 'хвилину';\n    } else if (key === 'h') {\n      return withoutSuffix ? 'година' : 'годину';\n    } else {\n      return number + ' ' + plural(format[key], +number);\n    }\n  }\n  function weekdaysCaseReplace(m, format) {\n    var weekdays = {\n        nominative: 'неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота'.split('_'),\n        accusative: 'неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу'.split('_'),\n        genitive: 'неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи'.split('_')\n      },\n      nounCase;\n    if (m === true) {\n      return weekdays['nominative'].slice(1, 7).concat(weekdays['nominative'].slice(0, 1));\n    }\n    if (!m) {\n      return weekdays['nominative'];\n    }\n    nounCase = /(\\[[ВвУу]\\]) ?dddd/.test(format) ? 'accusative' : /\\[?(?:минулої|наступної)? ?\\] ?dddd/.test(format) ? 'genitive' : 'nominative';\n    return weekdays[nounCase][m.day()];\n  }\n  function processHoursFunction(str) {\n    return function () {\n      return str + 'о' + (this.hours() === 11 ? 'б' : '') + '] LT';\n    };\n  }\n  var uk = moment.defineLocale('uk', {\n    months: {\n      format: 'січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня'.split('_'),\n      standalone: 'січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень'.split('_')\n    },\n    monthsShort: 'січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд'.split('_'),\n    weekdays: weekdaysCaseReplace,\n    weekdaysShort: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY р.',\n      LLL: 'D MMMM YYYY р., HH:mm',\n      LLLL: 'dddd, D MMMM YYYY р., HH:mm'\n    },\n    calendar: {\n      sameDay: processHoursFunction('[Сьогодні '),\n      nextDay: processHoursFunction('[Завтра '),\n      lastDay: processHoursFunction('[Вчора '),\n      nextWeek: processHoursFunction('[У] dddd ['),\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n          case 3:\n          case 5:\n          case 6:\n            return processHoursFunction('[Минулої] dddd [').call(this);\n          case 1:\n          case 2:\n          case 4:\n            return processHoursFunction('[Минулого] dddd [').call(this);\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'за %s',\n      past: '%s тому',\n      s: 'декілька секунд',\n      ss: relativeTimeWithPlural,\n      m: relativeTimeWithPlural,\n      mm: relativeTimeWithPlural,\n      h: 'годину',\n      hh: relativeTimeWithPlural,\n      d: 'день',\n      dd: relativeTimeWithPlural,\n      M: 'місяць',\n      MM: relativeTimeWithPlural,\n      y: 'рік',\n      yy: relativeTimeWithPlural\n    },\n    // M. E.: those two are virtually unused but a user might want to implement them for his/her website for some reason\n    meridiemParse: /ночі|ранку|дня|вечора/,\n    isPM: function (input) {\n      return /^(дня|вечора)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ночі';\n      } else if (hour < 12) {\n        return 'ранку';\n      } else if (hour < 17) {\n        return 'дня';\n      } else {\n        return 'вечора';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(й|го)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'M':\n        case 'd':\n        case 'DDD':\n        case 'w':\n        case 'W':\n          return number + '-й';\n        case 'D':\n          return number + '-го';\n        default:\n          return number;\n      }\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 7th is the first week of the year.\n    }\n  });\n  return uk;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "plural", "word", "num", "forms", "split", "relativeTimeWithPlural", "number", "withoutSuffix", "key", "format", "ss", "mm", "hh", "dd", "MM", "yy", "weekdaysCaseReplace", "m", "weekdays", "nominative", "accusative", "genitive", "nounCase", "slice", "concat", "test", "day", "processHoursFunction", "str", "hours", "uk", "defineLocale", "months", "standalone", "monthsShort", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "lastDay", "nextWeek", "lastWeek", "call", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "h", "d", "M", "y", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "dayOfMonthOrdinalParse", "ordinal", "period", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/uk.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Ukrainian [uk]\n//! author : zemlanin : https://github.com/zemlanin\n//! Author : <PERSON><PERSON><PERSON> : https://github.com/Oire\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function plural(word, num) {\n        var forms = word.split('_');\n        return num % 10 === 1 && num % 100 !== 11\n            ? forms[0]\n            : num % 10 >= 2 && num % 10 <= 4 && (num % 100 < 10 || num % 100 >= 20)\n            ? forms[1]\n            : forms[2];\n    }\n    function relativeTimeWithPlural(number, withoutSuffix, key) {\n        var format = {\n            ss: withoutSuffix ? 'секунда_секунди_секунд' : 'секунду_секунди_секунд',\n            mm: withoutSuffix ? 'хвилина_хвилини_хвилин' : 'хвилину_хвилини_хвилин',\n            hh: withoutSuffix ? 'година_години_годин' : 'годину_години_годин',\n            dd: 'день_дні_днів',\n            MM: 'місяць_місяці_місяців',\n            yy: 'рік_роки_років',\n        };\n        if (key === 'm') {\n            return withoutSuffix ? 'хвилина' : 'хвилину';\n        } else if (key === 'h') {\n            return withoutSuffix ? 'година' : 'годину';\n        } else {\n            return number + ' ' + plural(format[key], +number);\n        }\n    }\n    function weekdaysCaseReplace(m, format) {\n        var weekdays = {\n                nominative:\n                    'неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота'.split(\n                        '_'\n                    ),\n                accusative:\n                    'неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу'.split(\n                        '_'\n                    ),\n                genitive:\n                    'неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи'.split(\n                        '_'\n                    ),\n            },\n            nounCase;\n\n        if (m === true) {\n            return weekdays['nominative']\n                .slice(1, 7)\n                .concat(weekdays['nominative'].slice(0, 1));\n        }\n        if (!m) {\n            return weekdays['nominative'];\n        }\n\n        nounCase = /(\\[[ВвУу]\\]) ?dddd/.test(format)\n            ? 'accusative'\n            : /\\[?(?:минулої|наступної)? ?\\] ?dddd/.test(format)\n            ? 'genitive'\n            : 'nominative';\n        return weekdays[nounCase][m.day()];\n    }\n    function processHoursFunction(str) {\n        return function () {\n            return str + 'о' + (this.hours() === 11 ? 'б' : '') + '] LT';\n        };\n    }\n\n    var uk = moment.defineLocale('uk', {\n        months: {\n            format: 'січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня'.split(\n                '_'\n            ),\n            standalone:\n                'січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень'.split(\n                    '_'\n                ),\n        },\n        monthsShort: 'січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд'.split(\n            '_'\n        ),\n        weekdays: weekdaysCaseReplace,\n        weekdaysShort: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n        weekdaysMin: 'нд_пн_вт_ср_чт_пт_сб'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D MMMM YYYY р.',\n            LLL: 'D MMMM YYYY р., HH:mm',\n            LLLL: 'dddd, D MMMM YYYY р., HH:mm',\n        },\n        calendar: {\n            sameDay: processHoursFunction('[Сьогодні '),\n            nextDay: processHoursFunction('[Завтра '),\n            lastDay: processHoursFunction('[Вчора '),\n            nextWeek: processHoursFunction('[У] dddd ['),\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                    case 3:\n                    case 5:\n                    case 6:\n                        return processHoursFunction('[Минулої] dddd [').call(this);\n                    case 1:\n                    case 2:\n                    case 4:\n                        return processHoursFunction('[Минулого] dddd [').call(this);\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'за %s',\n            past: '%s тому',\n            s: 'декілька секунд',\n            ss: relativeTimeWithPlural,\n            m: relativeTimeWithPlural,\n            mm: relativeTimeWithPlural,\n            h: 'годину',\n            hh: relativeTimeWithPlural,\n            d: 'день',\n            dd: relativeTimeWithPlural,\n            M: 'місяць',\n            MM: relativeTimeWithPlural,\n            y: 'рік',\n            yy: relativeTimeWithPlural,\n        },\n        // M. E.: those two are virtually unused but a user might want to implement them for his/her website for some reason\n        meridiemParse: /ночі|ранку|дня|вечора/,\n        isPM: function (input) {\n            return /^(дня|вечора)$/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 4) {\n                return 'ночі';\n            } else if (hour < 12) {\n                return 'ранку';\n            } else if (hour < 17) {\n                return 'дня';\n            } else {\n                return 'вечора';\n            }\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}-(й|го)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'M':\n                case 'd':\n                case 'DDD':\n                case 'w':\n                case 'W':\n                    return number + '-й';\n                case 'D':\n                    return number + '-го';\n                default:\n                    return number;\n            }\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 7, // The week that contains Jan 7th is the first week of the year.\n        },\n    });\n\n    return uk;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,MAAMA,CAACC,IAAI,EAAEC,GAAG,EAAE;IACvB,IAAIC,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAC3B,OAAOF,GAAG,GAAG,EAAE,KAAK,CAAC,IAAIA,GAAG,GAAG,GAAG,KAAK,EAAE,GACnCC,KAAK,CAAC,CAAC,CAAC,GACRD,GAAG,GAAG,EAAE,IAAI,CAAC,IAAIA,GAAG,GAAG,EAAE,IAAI,CAAC,KAAKA,GAAG,GAAG,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC,GACrEC,KAAK,CAAC,CAAC,CAAC,GACRA,KAAK,CAAC,CAAC,CAAC;EAClB;EACA,SAASE,sBAAsBA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAE;IACxD,IAAIC,MAAM,GAAG;MACTC,EAAE,EAAEH,aAAa,GAAG,wBAAwB,GAAG,wBAAwB;MACvEI,EAAE,EAAEJ,aAAa,GAAG,wBAAwB,GAAG,wBAAwB;MACvEK,EAAE,EAAEL,aAAa,GAAG,qBAAqB,GAAG,qBAAqB;MACjEM,EAAE,EAAE,eAAe;MACnBC,EAAE,EAAE,uBAAuB;MAC3BC,EAAE,EAAE;IACR,CAAC;IACD,IAAIP,GAAG,KAAK,GAAG,EAAE;MACb,OAAOD,aAAa,GAAG,SAAS,GAAG,SAAS;IAChD,CAAC,MAAM,IAAIC,GAAG,KAAK,GAAG,EAAE;MACpB,OAAOD,aAAa,GAAG,QAAQ,GAAG,QAAQ;IAC9C,CAAC,MAAM;MACH,OAAOD,MAAM,GAAG,GAAG,GAAGN,MAAM,CAACS,MAAM,CAACD,GAAG,CAAC,EAAE,CAACF,MAAM,CAAC;IACtD;EACJ;EACA,SAASU,mBAAmBA,CAACC,CAAC,EAAER,MAAM,EAAE;IACpC,IAAIS,QAAQ,GAAG;QACPC,UAAU,EACN,yDAAyD,CAACf,KAAK,CAC3D,GACJ,CAAC;QACLgB,UAAU,EACN,yDAAyD,CAAChB,KAAK,CAC3D,GACJ,CAAC;QACLiB,QAAQ,EACJ,2DAA2D,CAACjB,KAAK,CAC7D,GACJ;MACR,CAAC;MACDkB,QAAQ;IAEZ,IAAIL,CAAC,KAAK,IAAI,EAAE;MACZ,OAAOC,QAAQ,CAAC,YAAY,CAAC,CACxBK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXC,MAAM,CAACN,QAAQ,CAAC,YAAY,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnD;IACA,IAAI,CAACN,CAAC,EAAE;MACJ,OAAOC,QAAQ,CAAC,YAAY,CAAC;IACjC;IAEAI,QAAQ,GAAG,oBAAoB,CAACG,IAAI,CAAChB,MAAM,CAAC,GACtC,YAAY,GACZ,qCAAqC,CAACgB,IAAI,CAAChB,MAAM,CAAC,GAClD,UAAU,GACV,YAAY;IAClB,OAAOS,QAAQ,CAACI,QAAQ,CAAC,CAACL,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC;EACtC;EACA,SAASC,oBAAoBA,CAACC,GAAG,EAAE;IAC/B,OAAO,YAAY;MACf,OAAOA,GAAG,GAAG,GAAG,IAAI,IAAI,CAACC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM;IAChE,CAAC;EACL;EAEA,IAAIC,EAAE,GAAG/B,MAAM,CAACgC,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE;MACJvB,MAAM,EAAE,wFAAwF,CAACL,KAAK,CAClG,GACJ,CAAC;MACD6B,UAAU,EACN,gGAAgG,CAAC7B,KAAK,CAClG,GACJ;IACR,CAAC;IACD8B,WAAW,EAAE,wDAAwD,CAAC9B,KAAK,CACvE,GACJ,CAAC;IACDc,QAAQ,EAAEF,mBAAmB;IAC7BmB,aAAa,EAAE,sBAAsB,CAAC/B,KAAK,CAAC,GAAG,CAAC;IAChDgC,WAAW,EAAE,sBAAsB,CAAChC,KAAK,CAAC,GAAG,CAAC;IAC9CiC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,gBAAgB;MACpBC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAElB,oBAAoB,CAAC,YAAY,CAAC;MAC3CmB,OAAO,EAAEnB,oBAAoB,CAAC,UAAU,CAAC;MACzCoB,OAAO,EAAEpB,oBAAoB,CAAC,SAAS,CAAC;MACxCqB,QAAQ,EAAErB,oBAAoB,CAAC,YAAY,CAAC;MAC5CsB,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACvB,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAOC,oBAAoB,CAAC,kBAAkB,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;UAC9D,KAAK,CAAC;UACN,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAOvB,oBAAoB,CAAC,mBAAmB,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;QACnE;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,iBAAiB;MACpB7C,EAAE,EAAEL,sBAAsB;MAC1BY,CAAC,EAAEZ,sBAAsB;MACzBM,EAAE,EAAEN,sBAAsB;MAC1BmD,CAAC,EAAE,QAAQ;MACX5C,EAAE,EAAEP,sBAAsB;MAC1BoD,CAAC,EAAE,MAAM;MACT5C,EAAE,EAAER,sBAAsB;MAC1BqD,CAAC,EAAE,QAAQ;MACX5C,EAAE,EAAET,sBAAsB;MAC1BsD,CAAC,EAAE,KAAK;MACR5C,EAAE,EAAEV;IACR,CAAC;IACD;IACAuD,aAAa,EAAE,uBAAuB;IACtCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,gBAAgB,CAACrC,IAAI,CAACqC,KAAK,CAAC;IACvC,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,MAAM;MACjB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,KAAK;MAChB,CAAC,MAAM;QACH,OAAO,QAAQ;MACnB;IACJ,CAAC;IACDG,sBAAsB,EAAE,gBAAgB;IACxCC,OAAO,EAAE,SAAAA,CAAU9D,MAAM,EAAE+D,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;QACV,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAO/D,MAAM,GAAG,IAAI;QACxB,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,KAAK;QACzB;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDgE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO1C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}