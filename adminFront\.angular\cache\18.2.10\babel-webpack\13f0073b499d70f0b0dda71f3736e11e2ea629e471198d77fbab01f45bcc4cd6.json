{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getDaysInMonth from \"../getDaysInMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} month - the month of the new date\n * @returns {Date} the new date with the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport default function setMonth(dirtyDate, dirtyMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var month = toInteger(dirtyMonth);\n  var year = date.getFullYear();\n  var day = date.getDate();\n  var dateWithDesiredMonth = new Date(0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  var daysInMonth = getDaysInMonth(dateWithDesiredMonth);\n  // Set the last day of the new month\n  // if the original date was the last day of the longer month\n  date.setMonth(month, Math.min(day, daysInMonth));\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}