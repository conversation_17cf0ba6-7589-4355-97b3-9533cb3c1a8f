{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app-routing.module';\nimport { provideNoopAnimations } from '@angular/platform-browser/animations';\nimport { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { ApiModule } from '../services/api/api.module';\nimport { environment } from '../environments/environment';\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), {\n    provide: HTTP_INTERCEPTORS,\n    useClass: TokenInterceptor,\n    multi: true\n  }, importProvidersFrom(ApiModule.forRoot({\n    rootUrl: environment.BASE_URL_API\n  })), provideNoopAnimations()]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "routes", "provideNoopAnimations", "HTTP_INTERCEPTORS", "provideHttpClient", "withInterceptorsFromDi", "ApiModule", "environment", "TokenInterceptor", "appConfig", "providers", "provide", "useClass", "multi", "forRoot", "rootUrl", "BASE_URL_API"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\n\r\nimport { routes } from './app-routing.module';\r\nimport { provideNoopAnimations } from '@angular/platform-browser/animations';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { AppModule } from './app.module';\r\nimport { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\r\nimport { ApiModule } from '../services/api/api.module';\r\nimport { environment } from '../environments/environment';\r\nimport { TokenInterceptor } from './shared/auth/token.interceptor';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideRouter(routes),\r\n    provideHttpClient(\r\n      withInterceptorsFromDi()\r\n    ),\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: TokenInterceptor,\r\n      multi: true\r\n    },\r\n    importProvidersFrom(\r\n      ApiModule.forRoot({ rootUrl: environment.BASE_URL_API })\r\n    ),\r\n    provideNoopAnimations(),\r\n  ],\r\n};\r\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAE/C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,qBAAqB,QAAQ,sCAAsC;AAG5E,SAASC,iBAAiB,EAAEC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AACnG,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,gBAAgB,QAAQ,iCAAiC;AAElE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTV,aAAa,CAACC,MAAM,CAAC,EACrBG,iBAAiB,CACfC,sBAAsB,EAAE,CACzB,EACD;IACEM,OAAO,EAAER,iBAAiB;IAC1BS,QAAQ,EAAEJ,gBAAgB;IAC1BK,KAAK,EAAE;GACR,EACDd,mBAAmB,CACjBO,SAAS,CAACQ,OAAO,CAAC;IAAEC,OAAO,EAAER,WAAW,CAACS;EAAY,CAAE,CAAC,CACzD,EACDd,qBAAqB,EAAE;CAE1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}