{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetHourListPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHourListPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetHourListPost$Plain.PATH = '/api/House/GetHourList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetHourListPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-hour-list-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetHourListResponeListResponseBase } from '../../models/get-hour-list-respone-list-response-base';\r\nimport { HouseGetHourListArgs } from '../../models/house-get-hour-list-args';\r\n\r\nexport interface ApiHouseGetHourListPost$Plain$Params {\r\n      body?: HouseGetHourListArgs\r\n}\r\n\r\nexport function apiHouseGetHourListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetHourListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHourListPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetHourListResponeListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetHourListPost$Plain.PATH = '/api/House/GetHourList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,6BAA6BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA6C,EAAEC,OAAqB;EACnJ,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,6BAA6B,CAACM,IAAI,EAAE,MAAM,CAAC;EAClF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2D;EACpE,CAAC,CAAC,CACH;AACH;AAEAb,6BAA6B,CAACM,IAAI,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}