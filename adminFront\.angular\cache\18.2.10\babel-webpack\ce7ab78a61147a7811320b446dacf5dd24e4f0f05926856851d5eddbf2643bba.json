{"ast": null, "code": "import { BaseComponent } from '../base/baseComponent';\nimport { EventEmitter } from '@angular/core';\nimport { NbTagModule, NbAutocompleteModule, NbOptionModule } from '@nebular/theme';\nimport { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, AsyncPipe } from '@angular/common';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"autoInput\"];\nfunction TagInputDirectiveComponent_nb_tag_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nb-tag\", 7);\n  }\n  if (rf & 2) {\n    const tag_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"text\", tag_r2);\n  }\n}\nfunction TagInputDirectiveComponent_nb_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3, \" \");\n  }\n}\nexport let TagInputDirectiveComponent = /*#__PURE__*/(() => {\n  class TagInputDirectiveComponent extends BaseComponent {\n    constructor(\n    // private tagService: TagService,\n    allow) {\n      super(allow);\n      this.allow = allow;\n      this.TagItems = [];\n      this.OutputTagItems = new EventEmitter();\n      this.options = [];\n      this.isAutocomplete = false;\n      this.request = new ShareRequest();\n      this.tagValue = '';\n    }\n    ngOnInit() {}\n    // 標籤刪除\n    onTagRemove(tagToRemove) {\n      this.TagItems = this.TagItems.filter(t => t !== tagToRemove.text);\n      this.OutputTagItems.emit(this.TagItems);\n    }\n    // 標籤新增\n    onTagAdd({\n      value,\n      input\n    }) {\n      if (value) {\n        this.TagItems.push(value);\n        this.OutputTagItems.emit(this.TagItems);\n      }\n      input.nativeElement.value = '';\n    }\n    onTagAddByFocus() {\n      if (!this.isNullOrEmpty(this.tagValue)) {\n        this.TagItems.push(this.tagValue);\n        this.OutputTagItems.emit(this.TagItems);\n        this.tagValue = '';\n      }\n    }\n    filter(value) {\n      const filterValue = value.toLowerCase();\n      return this.options.filter(optionValue => optionValue.toLowerCase().includes(filterValue));\n    }\n    getFilteredOptions(value) {\n      return of(value).pipe(map(filterString => this.filter(filterString)));\n    }\n    onAutocompleteChange() {\n      if (this.isAutocomplete === false) {\n        this.isAutocomplete = true;\n        setTimeout(() => {\n          this.getAutocomplete();\n        }, 1000);\n      }\n    }\n    getAutocomplete() {\n      // this.request.CTagId = this.TagId;\n      // this.request.CTagName = this.TagName;\n      // this.request.CName = this.tagValue;\n      // this.tagService.getAutocompleteByTagValueList(this.request).subscribe(res => {\n      //   this.options = [...res.Entries!.map(x => x.CTagValue!)];\n      //   this.filteredOptions$ = this.getFilteredOptions(this.input.nativeElement.value);\n      //   this.isAutocomplete = false;\n      // });\n    }\n    onAutocompleteSelectionChange($event) {\n      this.filteredOptions$ = this.getFilteredOptions($event);\n      this.OutputTagItems.emit(this.TagItems);\n    }\n    static {\n      this.ɵfac = function TagInputDirectiveComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || TagInputDirectiveComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TagInputDirectiveComponent,\n        selectors: [[\"ngx-tag-input-directive\"]],\n        viewQuery: function TagInputDirectiveComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n          }\n        },\n        inputs: {\n          TagItems: \"TagItems\",\n          TagId: \"TagId\",\n          TagName: \"TagName\"\n        },\n        outputs: {\n          OutputTagItems: \"OutputTagItems\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 8,\n        vars: 6,\n        consts: [[\"autoInput\", \"\"], [\"auto\", \"\"], [3, \"tagRemove\"], [\"removable\", \"\", 3, \"text\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbTagInput\", \"\", \"fullWidth\", \"\", 3, \"tagAdd\", \"input\", \"ngModelChange\", \"focusout\", \"nbAutocomplete\", \"ngModel\"], [3, \"selectedChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"removable\", \"\", 3, \"text\"], [3, \"value\"]],\n        template: function TagInputDirectiveComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-tag-list\", 2);\n            i0.ɵɵlistener(\"tagRemove\", function TagInputDirectiveComponent_Template_nb_tag_list_tagRemove_0_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTagRemove($event));\n            });\n            i0.ɵɵtemplate(1, TagInputDirectiveComponent_nb_tag_1_Template, 1, 1, \"nb-tag\", 3);\n            i0.ɵɵelementStart(2, \"input\", 4, 0);\n            i0.ɵɵlistener(\"tagAdd\", function TagInputDirectiveComponent_Template_input_tagAdd_2_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTagAdd($event));\n            })(\"input\", function TagInputDirectiveComponent_Template_input_input_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAutocompleteChange());\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function TagInputDirectiveComponent_Template_input_ngModelChange_2_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.tagValue, $event) || (ctx.tagValue = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"focusout\", function TagInputDirectiveComponent_Template_input_focusout_2_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onTagAddByFocus());\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(4, \"nb-autocomplete\", 5, 1);\n            i0.ɵɵlistener(\"selectedChange\", function TagInputDirectiveComponent_Template_nb_autocomplete_selectedChange_4_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onAutocompleteSelectionChange($event));\n            });\n            i0.ɵɵtemplate(6, TagInputDirectiveComponent_nb_option_6_Template, 2, 2, \"nb-option\", 6);\n            i0.ɵɵpipe(7, \"async\");\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            const auto_r4 = i0.ɵɵreference(5);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.TagItems);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"nbAutocomplete\", auto_r4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tagValue);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(7, 4, ctx.filteredOptions$));\n          }\n        },\n        dependencies: [NbTagModule, i2.NbTagComponent, i2.NbTagListComponent, i2.NbTagInputDirective, NgFor, NbAutocompleteModule, i2.NbAutocompleteComponent, i2.NbAutocompleteDirective, i2.NbOptionComponent, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, NbOptionModule, AsyncPipe]\n      });\n    }\n  }\n  return TagInputDirectiveComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}