{"ast": null, "code": "import { of as observableOf, BehaviorSubject } from 'rxjs';\nimport { takeWhile } from 'rxjs/operators';\nimport { NbLayoutDirection } from '@nebular/theme';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport class StateService {\n  constructor(directionService) {\n    this.layouts = [{\n      name: 'One Column',\n      icon: 'nb-layout-default',\n      id: 'one-column',\n      selected: true\n    }, {\n      name: 'Two Column',\n      icon: 'nb-layout-two-column',\n      id: 'two-column'\n    }, {\n      name: 'Center Column',\n      icon: 'nb-layout-centre',\n      id: 'center-column'\n    }];\n    this.sidebars = [{\n      name: 'Sidebar at layout start',\n      icon: 'nb-layout-sidebar-left',\n      id: 'start',\n      selected: true\n    }, {\n      name: 'Sidebar at layout end',\n      icon: 'nb-layout-sidebar-right',\n      id: 'end'\n    }];\n    this.layoutState$ = new BehaviorSubject(this.layouts[0]);\n    this.sidebarState$ = new BehaviorSubject(this.sidebars[0]);\n    this.alive = true;\n    directionService.onDirectionChange().pipe(takeWhile(() => this.alive)).subscribe(direction => this.updateSidebarIcons(direction));\n    this.updateSidebarIcons(directionService.getDirection());\n  }\n  ngOnDestroy() {\n    this.alive = false;\n  }\n  updateSidebarIcons(direction) {\n    const [startSidebar, endSidebar] = this.sidebars;\n    const isLtr = direction === NbLayoutDirection.LTR;\n    const startIconClass = isLtr ? 'nb-layout-sidebar-left' : 'nb-layout-sidebar-right';\n    const endIconClass = isLtr ? 'nb-layout-sidebar-right' : 'nb-layout-sidebar-left';\n    startSidebar.icon = startIconClass;\n    endSidebar.icon = endIconClass;\n  }\n  setLayoutState(state) {\n    this.layoutState$.next(state);\n  }\n  getLayoutStates() {\n    return observableOf(this.layouts);\n  }\n  onLayoutState() {\n    return this.layoutState$.asObservable();\n  }\n  setSidebarState(state) {\n    this.sidebarState$.next(state);\n  }\n  getSidebarStates() {\n    return observableOf(this.sidebars);\n  }\n  onSidebarState() {\n    return this.sidebarState$.asObservable();\n  }\n  static {\n    this.ɵfac = function StateService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StateService)(i0.ɵɵinject(i1.NbLayoutDirectionService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StateService,\n      factory: StateService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "BehaviorSubject", "<PERSON><PERSON><PERSON><PERSON>", "NbLayoutDirection", "StateService", "constructor", "directionService", "layouts", "name", "icon", "id", "selected", "sidebars", "layoutState$", "sidebarState$", "alive", "onDirectionChange", "pipe", "subscribe", "direction", "updateSidebarIcons", "getDirection", "ngOnDestroy", "startSidebar", "endSidebar", "isLtr", "LTR", "startIconClass", "endIconClass", "setLayoutState", "state", "next", "getLayoutStates", "onLayoutState", "asObservable", "setSidebarState", "getSidebarStates", "onSidebarState", "i0", "ɵɵinject", "i1", "NbLayoutDirectionService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\utils\\state.service.ts"], "sourcesContent": ["import { Injectable, OnD<PERSON>roy } from '@angular/core';\r\nimport { of as observableOf,  Observable,  BehaviorSubject } from 'rxjs';\r\nimport { takeWhile } from 'rxjs/operators';\r\n\r\nimport { NbLayoutDirectionService, NbLayoutDirection } from '@nebular/theme';\r\n\r\n@Injectable()\r\nexport class StateService implements OnDestroy {\r\n\r\n  protected layouts: any = [\r\n    {\r\n      name: 'One Column',\r\n      icon: 'nb-layout-default',\r\n      id: 'one-column',\r\n      selected: true,\r\n    },\r\n    {\r\n      name: 'Two Column',\r\n      icon: 'nb-layout-two-column',\r\n      id: 'two-column',\r\n    },\r\n    {\r\n      name: 'Center Column',\r\n      icon: 'nb-layout-centre',\r\n      id: 'center-column',\r\n    },\r\n  ];\r\n\r\n  protected sidebars: any = [\r\n    {\r\n      name: 'Sidebar at layout start',\r\n      icon: 'nb-layout-sidebar-left',\r\n      id: 'start',\r\n      selected: true,\r\n    },\r\n    {\r\n      name: 'Sidebar at layout end',\r\n      icon: 'nb-layout-sidebar-right',\r\n      id: 'end',\r\n    },\r\n  ];\r\n\r\n  protected layoutState$ = new BehaviorSubject(this.layouts[0]);\r\n  protected sidebarState$ = new BehaviorSubject(this.sidebars[0]);\r\n\r\n  alive = true;\r\n\r\n  constructor(directionService: NbLayoutDirectionService) {\r\n    directionService.onDirectionChange()\r\n      .pipe(takeWhile(() => this.alive))\r\n      .subscribe(direction => this.updateSidebarIcons(direction));\r\n\r\n    this.updateSidebarIcons(directionService.getDirection());\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.alive = false;\r\n  }\r\n\r\n  private updateSidebarIcons(direction: NbLayoutDirection) {\r\n    const [ startSidebar, endSidebar ] = this.sidebars;\r\n    const isLtr = direction === NbLayoutDirection.LTR;\r\n    const startIconClass = isLtr ? 'nb-layout-sidebar-left' : 'nb-layout-sidebar-right';\r\n    const endIconClass = isLtr ? 'nb-layout-sidebar-right' : 'nb-layout-sidebar-left';\r\n    startSidebar.icon = startIconClass;\r\n    endSidebar.icon = endIconClass;\r\n  }\r\n\r\n  setLayoutState(state: any): any {\r\n    this.layoutState$.next(state);\r\n  }\r\n\r\n  getLayoutStates(): Observable<any[]> {\r\n    return observableOf(this.layouts);\r\n  }\r\n\r\n  onLayoutState(): Observable<any> {\r\n    return this.layoutState$.asObservable();\r\n  }\r\n\r\n  setSidebarState(state: any): any {\r\n    this.sidebarState$.next(state);\r\n  }\r\n\r\n  getSidebarStates(): Observable<any[]> {\r\n    return observableOf(this.sidebars);\r\n  }\r\n\r\n  onSidebarState(): Observable<any> {\r\n    return this.sidebarState$.asObservable();\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,EAAgBC,eAAe,QAAQ,MAAM;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAAmCC,iBAAiB,QAAQ,gBAAgB;;;AAG5E,OAAM,MAAOC,YAAY;EAwCvBC,YAAYC,gBAA0C;IAtC5C,KAAAC,OAAO,GAAQ,CACvB;MACEC,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,mBAAmB;MACzBC,EAAE,EAAE,YAAY;MAChBC,QAAQ,EAAE;KACX,EACD;MACEH,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE,sBAAsB;MAC5BC,EAAE,EAAE;KACL,EACD;MACEF,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,kBAAkB;MACxBC,EAAE,EAAE;KACL,CACF;IAES,KAAAE,QAAQ,GAAQ,CACxB;MACEJ,IAAI,EAAE,yBAAyB;MAC/BC,IAAI,EAAE,wBAAwB;MAC9BC,EAAE,EAAE,OAAO;MACXC,QAAQ,EAAE;KACX,EACD;MACEH,IAAI,EAAE,uBAAuB;MAC7BC,IAAI,EAAE,yBAAyB;MAC/BC,EAAE,EAAE;KACL,CACF;IAES,KAAAG,YAAY,GAAG,IAAIZ,eAAe,CAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC;IACnD,KAAAO,aAAa,GAAG,IAAIb,eAAe,CAAC,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/D,KAAAG,KAAK,GAAG,IAAI;IAGVT,gBAAgB,CAACU,iBAAiB,EAAE,CACjCC,IAAI,CAACf,SAAS,CAAC,MAAM,IAAI,CAACa,KAAK,CAAC,CAAC,CACjCG,SAAS,CAACC,SAAS,IAAI,IAAI,CAACC,kBAAkB,CAACD,SAAS,CAAC,CAAC;IAE7D,IAAI,CAACC,kBAAkB,CAACd,gBAAgB,CAACe,YAAY,EAAE,CAAC;EAC1D;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,KAAK,GAAG,KAAK;EACpB;EAEQK,kBAAkBA,CAACD,SAA4B;IACrD,MAAM,CAAEI,YAAY,EAAEC,UAAU,CAAE,GAAG,IAAI,CAACZ,QAAQ;IAClD,MAAMa,KAAK,GAAGN,SAAS,KAAKhB,iBAAiB,CAACuB,GAAG;IACjD,MAAMC,cAAc,GAAGF,KAAK,GAAG,wBAAwB,GAAG,yBAAyB;IACnF,MAAMG,YAAY,GAAGH,KAAK,GAAG,yBAAyB,GAAG,wBAAwB;IACjFF,YAAY,CAACd,IAAI,GAAGkB,cAAc;IAClCH,UAAU,CAACf,IAAI,GAAGmB,YAAY;EAChC;EAEAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACjB,YAAY,CAACkB,IAAI,CAACD,KAAK,CAAC;EAC/B;EAEAE,eAAeA,CAAA;IACb,OAAOhC,YAAY,CAAC,IAAI,CAACO,OAAO,CAAC;EACnC;EAEA0B,aAAaA,CAAA;IACX,OAAO,IAAI,CAACpB,YAAY,CAACqB,YAAY,EAAE;EACzC;EAEAC,eAAeA,CAACL,KAAU;IACxB,IAAI,CAAChB,aAAa,CAACiB,IAAI,CAACD,KAAK,CAAC;EAChC;EAEAM,gBAAgBA,CAAA;IACd,OAAOpC,YAAY,CAAC,IAAI,CAACY,QAAQ,CAAC;EACpC;EAEAyB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACvB,aAAa,CAACoB,YAAY,EAAE;EAC1C;;;uCAnFW9B,YAAY,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,wBAAA;IAAA;EAAA;;;aAAZrC,YAAY;MAAAsC,OAAA,EAAZtC,YAAY,CAAAuC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}