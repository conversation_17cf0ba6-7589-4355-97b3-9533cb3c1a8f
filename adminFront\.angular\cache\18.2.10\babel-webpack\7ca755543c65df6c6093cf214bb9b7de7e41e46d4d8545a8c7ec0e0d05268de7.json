{"ast": null, "code": "import { decodeJwtPayload } from '@nebular/auth';\nimport { FormsModule } from '@angular/forms';\nimport { NbLayoutModule, NbCardModule, NbInputModule, NbButtonModule } from '@nebular/theme';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/helper/allowHelper\";\nimport * as i6 from \"src/app/shared/helper/petternHelper\";\nimport * as i7 from \"@nebular/theme\";\nimport * as i8 from \"@angular/forms\";\nexport class LoginComponent {\n  constructor(router, userService, message, valid, allow, pettern) {\n    this.router = router;\n    this.userService = userService;\n    this.message = message;\n    this.valid = valid;\n    this.allow = allow;\n    this.pettern = pettern;\n    this.account = \"\";\n    this.password = \"\";\n  }\n  ngOnInit() {}\n  login() {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.userService.apiUserUserLoginPost$Json({\n      body: {\n        Account: this.account,\n        Password: this.password\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        const jwt = decodeJwtPayload(res.Entries);\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.TOKEN, res.Entries);\n        LocalStorageService.AddLocalStorage(STORAGE_KEY.BUID, jwt.BuId);\n        this.message.showSucessMSG('登入成功');\n        this.router.navigateByUrl('home');\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[帳號]', this.account);\n    this.valid.pattern('[帳號]', this.account, this.pettern.AccountPettern);\n    this.valid.required('[密碼]', this.password);\n    this.valid.pattern('[密碼]', this.password, this.pettern.PasswordPettern);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.AllowHelper), i0.ɵɵdirectiveInject(i6.PetternHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"ngx-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 17,\n      vars: 2,\n      consts: [[\"center\", \"\"], [1, \"form-group\"], [\"for\", \"exampleInputEmail1\", 1, \"label\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"exampleInputEmail1\", \"placeholder\", \"\\u5E33\\u865F\", \"name\", \"account\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"exampleInputPassword1\", 1, \"label\"], [\"type\", \"password\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"exampleInputPassword1\", \"placeholder\", \"\\u5BC6\\u78BC\", \"name\", \"password\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", \"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-layout\", 0)(1, \"nb-layout-column\")(2, \"nb-card\")(3, \"nb-card-header\");\n          i0.ɵɵtext(4, \"\\u767B\\u5165\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"form\")(7, \"div\", 1)(8, \"label\", 2);\n          i0.ɵɵtext(9, \"\\u5E33\\u865F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"input\", 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.account, $event) || (ctx.account = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 1)(12, \"label\", 4);\n          i0.ɵɵtext(13, \"\\u5BC6\\u78BC\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.password, $event) || (ctx.password = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_15_listener() {\n            return ctx.login();\n          });\n          i0.ɵɵtext(16, \" \\u767B\\u5165 \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.account);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.password);\n        }\n      },\n      dependencies: [NbLayoutModule, i7.NbLayoutComponent, i7.NbLayoutColumnComponent, NbCardModule, i7.NbCardComponent, i7.NbCardBodyComponent, i7.NbCardHeaderComponent, FormsModule, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.NgModel, i8.NgForm, NbInputModule, i7.NbInputDirective, NbButtonModule, i7.NbButtonComponent]\n    });\n  }\n}", "map": {"version": 3, "names": ["decodeJwtPayload", "FormsModule", "NbLayoutModule", "NbCardModule", "NbInputModule", "NbButtonModule", "LocalStorageService", "STORAGE_KEY", "LoginComponent", "constructor", "router", "userService", "message", "valid", "allow", "pettern", "account", "password", "ngOnInit", "login", "validation", "errorMessages", "length", "showErrorMSGs", "apiUserUserLoginPost$Json", "body", "Account", "Password", "subscribe", "res", "StatusCode", "jwt", "Entries", "AddLocalStorage", "TOKEN", "BUID", "BuId", "showSucessMSG", "navigateByUrl", "showErrorMSG", "Message", "clear", "required", "pattern", "Account<PERSON><PERSON><PERSON>", "PasswordPettern", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "UserService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "AllowHelper", "i6", "PetternHelper", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "LoginComponent_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "LoginComponent_Template_input_ngModelChange_14_listener", "ɵɵlistener", "LoginComponent_Template_button_click_15_listener", "ɵɵadvance", "ɵɵtwoWayProperty", "i7", "NbLayoutComponent", "NbLayoutColumnComponent", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "i8", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "NgModel", "NgForm", "NbInputDirective", "NbButtonComponent"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\login\\login.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\login\\login.component.html"], "sourcesContent": ["\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbLayoutModule, NbCardModule, NbInputModule, NbButtonModule } from '@nebular/theme';\r\nimport { UserService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\n\r\n@Component({\r\n  selector: 'ngx-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    NbLayoutModule,\r\n    NbCardModule,\r\n    FormsModule,\r\n    NbInputModule,\r\n    NbButtonModule,\r\n  ],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n\r\n  account: string = \"\"\r\n  password: string = \"\"\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    protected allow: AllowHelper,\r\n    private pettern: PetternHelper,\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n  login() {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    this.userService.apiUserUserLoginPost$Json({\r\n      body: {\r\n        Account: this.account,\r\n        Password: this.password\r\n      }\r\n    }).subscribe(\r\n      res => {\r\n        if (res.StatusCode === 0) {\r\n          const jwt = decodeJwtPayload(res.Entries!);\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.TOKEN, res.Entries!);\r\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.BUID, jwt.BuId);\r\n          this.message.showSucessMSG('登入成功');\r\n          this.router.navigateByUrl('home');\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      },\r\n    );\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[帳號]', this.account);\r\n    this.valid.pattern('[帳號]', this.account, this.pettern.AccountPettern);\r\n    this.valid.required('[密碼]', this.password);\r\n    this.valid.pattern('[密碼]', this.password, this.pettern.PasswordPettern);\r\n  }\r\n}\r\n", "<nb-layout center>\r\n  <nb-layout-column>\r\n    <nb-card>\r\n      <nb-card-header>登入</nb-card-header>\r\n      <nb-card-body>\r\n        <form>\r\n          <div class=\"form-group\">\r\n            <label for=\"exampleInputEmail1\" class=\"label\">帳號</label>\r\n            <input\r\n              type=\"text\"\r\n              nbInput\r\n              fullWidth\r\n              id=\"exampleInputEmail1\"\r\n              placeholder=\"帳號\"\r\n              [(ngModel)]=\"account\"\r\n              name=\"account\"\r\n            />\r\n          </div>\r\n          <div class=\"form-group\">\r\n            <label for=\"exampleInputPassword1\" class=\"label\">密碼</label>\r\n            <input\r\n              type=\"password\"\r\n              nbInput\r\n              fullWidth\r\n              id=\"exampleInputPassword1\"\r\n              placeholder=\"密碼\"\r\n              [(ngModel)]=\"password\"\r\n              name=\"password\"\r\n            />\r\n          </div>\r\n          <button type=\"submit\" nbButton status=\"primary\" (click)=\"login()\">\r\n            登入\r\n          </button>\r\n        </form>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  </nb-layout-column>\r\n</nb-layout>\r\n"], "mappings": "AAIA,SAASA,gBAAgB,QAAQ,eAAe;AAChD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,gBAAgB;AAM5F,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;;;;;;;;;;AAe9D,OAAM,MAAOC,cAAc;EAKzBC,YACUC,MAAc,EACdC,WAAwB,EACxBC,OAAuB,EACvBC,KAAuB,EACrBC,KAAkB,EACpBC,OAAsB;IALtB,KAAAL,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACH,KAAAC,KAAK,GAALA,KAAK;IACP,KAAAC,OAAO,GAAPA,OAAO;IATjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,QAAQ,GAAW,EAAE;EAWrB;EAEAC,QAAQA,CAAA,GACR;EACAC,KAAKA,CAAA;IACH,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,IAAI,CAACP,KAAK,CAACQ,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACV,OAAO,CAACW,aAAa,CAAC,IAAI,CAACV,KAAK,CAACQ,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACV,WAAW,CAACa,yBAAyB,CAAC;MACzCC,IAAI,EAAE;QACJC,OAAO,EAAE,IAAI,CAACV,OAAO;QACrBW,QAAQ,EAAE,IAAI,CAACV;;KAElB,CAAC,CAACW,SAAS,CACVC,GAAG,IAAG;MACJ,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,MAAMC,GAAG,GAAG/B,gBAAgB,CAAC6B,GAAG,CAACG,OAAQ,CAAC;QAC1C1B,mBAAmB,CAAC2B,eAAe,CAAC1B,WAAW,CAAC2B,KAAK,EAAEL,GAAG,CAACG,OAAQ,CAAC;QACpE1B,mBAAmB,CAAC2B,eAAe,CAAC1B,WAAW,CAAC4B,IAAI,EAAEJ,GAAG,CAACK,IAAI,CAAC;QAC/D,IAAI,CAACxB,OAAO,CAACyB,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC3B,MAAM,CAAC4B,aAAa,CAAC,MAAM,CAAC;MACnC,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAAC2B,YAAY,CAACV,GAAG,CAACW,OAAQ,CAAC;MACzC;IACF,CAAC,CACF;EACH;EAEApB,UAAUA,CAAA;IACR,IAAI,CAACP,KAAK,CAAC4B,KAAK,EAAE;IAClB,IAAI,CAAC5B,KAAK,CAAC6B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1B,OAAO,CAAC;IACzC,IAAI,CAACH,KAAK,CAAC8B,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC3B,OAAO,EAAE,IAAI,CAACD,OAAO,CAAC6B,cAAc,CAAC;IACrE,IAAI,CAAC/B,KAAK,CAAC6B,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACzB,QAAQ,CAAC;IAC1C,IAAI,CAACJ,KAAK,CAAC8B,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC1B,QAAQ,EAAE,IAAI,CAACF,OAAO,CAAC8B,eAAe,CAAC;EACzE;;;uCAnDWrC,cAAc,EAAAsC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAX,EAAA,CAAAC,iBAAA,CAAAW,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAdnD,cAAc;MAAAoD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhB,EAAA,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBrBvB,EAHN,CAAAyB,cAAA,mBAAkB,uBACE,cACP,qBACS;UAAAzB,EAAA,CAAA0B,MAAA,mBAAE;UAAA1B,EAAA,CAAA2B,YAAA,EAAiB;UAI7B3B,EAHN,CAAAyB,cAAA,mBAAc,WACN,aACoB,eACwB;UAAAzB,EAAA,CAAA0B,MAAA,mBAAE;UAAA1B,EAAA,CAAA2B,YAAA,EAAQ;UACxD3B,EAAA,CAAAyB,cAAA,gBAQE;UAFAzB,EAAA,CAAA4B,gBAAA,2BAAAC,wDAAAC,MAAA;YAAA9B,EAAA,CAAA+B,kBAAA,CAAAP,GAAA,CAAAtD,OAAA,EAAA4D,MAAA,MAAAN,GAAA,CAAAtD,OAAA,GAAA4D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAGzB9B,EATE,CAAA2B,YAAA,EAQE,EACE;UAEJ3B,EADF,CAAAyB,cAAA,cAAwB,gBAC2B;UAAAzB,EAAA,CAAA0B,MAAA,oBAAE;UAAA1B,EAAA,CAAA2B,YAAA,EAAQ;UAC3D3B,EAAA,CAAAyB,cAAA,gBAQE;UAFAzB,EAAA,CAAA4B,gBAAA,2BAAAI,wDAAAF,MAAA;YAAA9B,EAAA,CAAA+B,kBAAA,CAAAP,GAAA,CAAArD,QAAA,EAAA2D,MAAA,MAAAN,GAAA,CAAArD,QAAA,GAAA2D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsB;UAG1B9B,EATE,CAAA2B,YAAA,EAQE,EACE;UACN3B,EAAA,CAAAyB,cAAA,iBAAkE;UAAlBzB,EAAA,CAAAiC,UAAA,mBAAAC,iDAAA;YAAA,OAASV,GAAA,CAAAnD,KAAA,EAAO;UAAA,EAAC;UAC/D2B,EAAA,CAAA0B,MAAA,sBACF;UAKV1B,EALU,CAAA2B,YAAA,EAAS,EACJ,EACM,EACP,EACO,EACT;;;UAvBE3B,EAAA,CAAAmC,SAAA,IAAqB;UAArBnC,EAAA,CAAAoC,gBAAA,YAAAZ,GAAA,CAAAtD,OAAA,CAAqB;UAYrB8B,EAAA,CAAAmC,SAAA,GAAsB;UAAtBnC,EAAA,CAAAoC,gBAAA,YAAAZ,GAAA,CAAArD,QAAA,CAAsB;;;qBDLhCf,cAAc,EAAAiF,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,uBAAA,EACdlF,YAAY,EAAAgF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,mBAAA,EAAAJ,EAAA,CAAAK,qBAAA,EACZvF,WAAW,EAAAwF,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,OAAA,EAAAL,EAAA,CAAAM,MAAA,EACX3F,aAAa,EAAA+E,EAAA,CAAAa,gBAAA,EACb3F,cAAc,EAAA8E,EAAA,CAAAc,iBAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}