{"ast": null, "code": "import { BaseComponent, getUniqueDomId, getDateMeta, buildNavLinkAttrs, ContentContainer, getDayClassNames, formatDayString, createFormatter, EventContainer, getSegAnchorAttrs, isMultiDayRange, buildSegTimeText, DateComponent, memoize, ViewContainer, Scroller, NowTimer, sortEventSegs, getSegMeta, sliceEventStore, intersectRanges, startOfDay, addDays, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, Fragment } from '@fullcalendar/core/preact.js';\nclass ListViewHeaderRow extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      textId: getUniqueDomId()\n    };\n  }\n  render() {\n    let {\n      theme,\n      dateEnv,\n      options,\n      viewApi\n    } = this.context;\n    let {\n      cellId,\n      dayDate,\n      todayRange\n    } = this.props;\n    let {\n      textId\n    } = this.state;\n    let dayMeta = getDateMeta(dayDate, todayRange);\n    // will ever be falsy?\n    let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : '';\n    // will ever be falsy? also, BAD NAME \"alt\"\n    let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : '';\n    let renderProps = Object.assign({\n      date: dateEnv.toDate(dayDate),\n      view: viewApi,\n      textId,\n      text,\n      sideText,\n      navLinkAttrs: buildNavLinkAttrs(this.context, dayDate),\n      sideNavLinkAttrs: buildNavLinkAttrs(this.context, dayDate, 'day', false)\n    }, dayMeta);\n    // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n    return createElement(ContentContainer, {\n      elTag: \"tr\",\n      elClasses: ['fc-list-day', ...getDayClassNames(dayMeta, theme)],\n      elAttrs: {\n        'data-date': formatDayString(dayDate)\n      },\n      renderProps: renderProps,\n      generatorName: \"dayHeaderContent\",\n      customGenerator: options.dayHeaderContent,\n      defaultGenerator: renderInnerContent,\n      classNameGenerator: options.dayHeaderClassNames,\n      didMount: options.dayHeaderDidMount,\n      willUnmount: options.dayHeaderWillUnmount\n    }, InnerContent =>\n    // TODO: force-hide top border based on :first-child\n    createElement(\"th\", {\n      scope: \"colgroup\",\n      colSpan: 3,\n      id: cellId,\n      \"aria-labelledby\": textId\n    }, createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-list-day-cushion', theme.getClass('tableCellShaded')]\n    })));\n  }\n}\nfunction renderInnerContent(props) {\n  return createElement(Fragment, null, props.text && createElement(\"a\", Object.assign({\n    id: props.textId,\n    className: \"fc-list-day-text\"\n  }, props.navLinkAttrs), props.text), props.sideText && ( /* not keyboard tabbable */createElement(\"a\", Object.assign({\n    \"aria-hidden\": true,\n    className: \"fc-list-day-side-text\"\n  }, props.sideNavLinkAttrs), props.sideText)));\n}\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: 'short'\n});\nclass ListViewEventRow extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg,\n      timeHeaderId,\n      eventHeaderId,\n      dateHeaderId\n    } = props;\n    let timeFormat = options.eventTimeFormat || DEFAULT_TIME_FORMAT;\n    return createElement(EventContainer, Object.assign({}, props, {\n      elTag: \"tr\",\n      elClasses: ['fc-list-event', seg.eventRange.def.url && 'fc-event-forced-url'],\n      defaultGenerator: () => renderEventInnerContent(seg, context) /* weird */,\n      seg: seg,\n      timeText: \"\",\n      disableDragging: true,\n      disableResizing: true\n    }), (InnerContent, eventContentArg) => createElement(Fragment, null, buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId), createElement(\"td\", {\n      \"aria-hidden\": true,\n      className: \"fc-list-event-graphic\"\n    }, createElement(\"span\", {\n      className: \"fc-list-event-dot\",\n      style: {\n        borderColor: eventContentArg.borderColor || eventContentArg.backgroundColor\n      }\n    })), createElement(InnerContent, {\n      elTag: \"td\",\n      elClasses: ['fc-list-event-title'],\n      elAttrs: {\n        headers: `${eventHeaderId} ${dateHeaderId}`\n      }\n    })));\n  }\n}\nfunction renderEventInnerContent(seg, context) {\n  let interactiveAttrs = getSegAnchorAttrs(seg, context);\n  return createElement(\"a\", Object.assign({}, interactiveAttrs), seg.eventRange.def.title);\n}\nfunction buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId) {\n  let {\n    options\n  } = context;\n  if (options.displayEventTime !== false) {\n    let eventDef = seg.eventRange.def;\n    let eventInstance = seg.eventRange.instance;\n    let doAllDay = false;\n    let timeText;\n    if (eventDef.allDay) {\n      doAllDay = true;\n    } else if (isMultiDayRange(seg.eventRange.range)) {\n      // TODO: use (!isStart || !isEnd) instead?\n      if (seg.isStart) {\n        timeText = buildSegTimeText(seg, timeFormat, context, null, null, eventInstance.range.start, seg.end);\n      } else if (seg.isEnd) {\n        timeText = buildSegTimeText(seg, timeFormat, context, null, null, seg.start, eventInstance.range.end);\n      } else {\n        doAllDay = true;\n      }\n    } else {\n      timeText = buildSegTimeText(seg, timeFormat, context);\n    }\n    if (doAllDay) {\n      let renderProps = {\n        text: context.options.allDayText,\n        view: context.viewApi\n      };\n      return createElement(ContentContainer, {\n        elTag: \"td\",\n        elClasses: ['fc-list-event-time'],\n        elAttrs: {\n          headers: `${timeHeaderId} ${dateHeaderId}`\n        },\n        renderProps: renderProps,\n        generatorName: \"allDayContent\",\n        customGenerator: options.allDayContent,\n        defaultGenerator: renderAllDayInner,\n        classNameGenerator: options.allDayClassNames,\n        didMount: options.allDayDidMount,\n        willUnmount: options.allDayWillUnmount\n      });\n    }\n    return createElement(\"td\", {\n      className: \"fc-list-event-time\"\n    }, timeText);\n  }\n  return null;\n}\nfunction renderAllDayInner(renderProps) {\n  return renderProps.text;\n}\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nclass ListView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.computeDateVars = memoize(computeDateVars);\n    this.eventStoreToSegs = memoize(this._eventStoreToSegs);\n    this.state = {\n      timeHeaderId: getUniqueDomId(),\n      eventHeaderId: getUniqueDomId(),\n      dateHeaderIdRoot: getUniqueDomId()\n    };\n    this.setRootEl = rootEl => {\n      if (rootEl) {\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl\n        });\n      } else {\n        this.context.unregisterInteractiveComponent(this);\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      dayDates,\n      dayRanges\n    } = this.computeDateVars(props.dateProfile);\n    let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges);\n    return createElement(ViewContainer, {\n      elRef: this.setRootEl,\n      elClasses: ['fc-list', context.theme.getClass('table'), context.options.stickyHeaderDates !== false ? 'fc-list-sticky' : ''],\n      viewSpec: context.viewSpec\n    }, createElement(Scroller, {\n      liquid: !props.isHeightAuto,\n      overflowX: props.isHeightAuto ? 'visible' : 'hidden',\n      overflowY: props.isHeightAuto ? 'visible' : 'auto'\n    }, eventSegs.length > 0 ? this.renderSegList(eventSegs, dayDates) : this.renderEmptyMessage()));\n  }\n  renderEmptyMessage() {\n    let {\n      options,\n      viewApi\n    } = this.context;\n    let renderProps = {\n      text: options.noEventsText,\n      view: viewApi\n    };\n    return createElement(ContentContainer, {\n      elTag: \"div\",\n      elClasses: ['fc-list-empty'],\n      renderProps: renderProps,\n      generatorName: \"noEventsContent\",\n      customGenerator: options.noEventsContent,\n      defaultGenerator: renderNoEventsInner,\n      classNameGenerator: options.noEventsClassNames,\n      didMount: options.noEventsDidMount,\n      willUnmount: options.noEventsWillUnmount\n    }, InnerContent => createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-list-empty-cushion']\n    }));\n  }\n  renderSegList(allSegs, dayDates) {\n    let {\n      theme,\n      options\n    } = this.context;\n    let {\n      timeHeaderId,\n      eventHeaderId,\n      dateHeaderIdRoot\n    } = this.state;\n    let segsByDay = groupSegsByDay(allSegs); // sparse array\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => {\n      let innerNodes = [];\n      for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n        let daySegs = segsByDay[dayIndex];\n        if (daySegs) {\n          // sparse array, so might be undefined\n          let dayStr = formatDayString(dayDates[dayIndex]);\n          let dateHeaderId = dateHeaderIdRoot + '-' + dayStr;\n          // append a day header\n          innerNodes.push(createElement(ListViewHeaderRow, {\n            key: dayStr,\n            cellId: dateHeaderId,\n            dayDate: dayDates[dayIndex],\n            todayRange: todayRange\n          }));\n          daySegs = sortEventSegs(daySegs, options.eventOrder);\n          for (let seg of daySegs) {\n            innerNodes.push(createElement(ListViewEventRow, Object.assign({\n              key: dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */,\n              seg: seg,\n              isDragging: false,\n              isResizing: false,\n              isDateSelecting: false,\n              isSelected: false,\n              timeHeaderId: timeHeaderId,\n              eventHeaderId: eventHeaderId,\n              dateHeaderId: dateHeaderId\n            }, getSegMeta(seg, todayRange, nowDate))));\n          }\n        }\n      }\n      return createElement(\"table\", {\n        className: 'fc-list-table ' + theme.getClass('table')\n      }, createElement(\"thead\", null, createElement(\"tr\", null, createElement(\"th\", {\n        scope: \"col\",\n        id: timeHeaderId\n      }, options.timeHint), createElement(\"th\", {\n        scope: \"col\",\n        \"aria-hidden\": true\n      }), createElement(\"th\", {\n        scope: \"col\",\n        id: eventHeaderId\n      }, options.eventHint))), createElement(\"tbody\", null, innerNodes));\n    });\n  }\n  _eventStoreToSegs(eventStore, eventUiBases, dayRanges) {\n    return this.eventRangesToSegs(sliceEventStore(eventStore, eventUiBases, this.props.dateProfile.activeRange, this.context.options.nextDayThreshold).fg, dayRanges);\n  }\n  eventRangesToSegs(eventRanges, dayRanges) {\n    let segs = [];\n    for (let eventRange of eventRanges) {\n      segs.push(...this.eventRangeToSegs(eventRange, dayRanges));\n    }\n    return segs;\n  }\n  eventRangeToSegs(eventRange, dayRanges) {\n    let {\n      dateEnv\n    } = this.context;\n    let {\n      nextDayThreshold\n    } = this.context.options;\n    let range = eventRange.range;\n    let allDay = eventRange.def.allDay;\n    let dayIndex;\n    let segRange;\n    let seg;\n    let segs = [];\n    for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n      segRange = intersectRanges(range, dayRanges[dayIndex]);\n      if (segRange) {\n        seg = {\n          component: this,\n          eventRange,\n          start: segRange.start,\n          end: segRange.end,\n          isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n          dayIndex\n        };\n        segs.push(seg);\n        // detect when range won't go fully into the next day,\n        // and mutate the latest seg to the be the end.\n        if (!seg.isEnd && !allDay && dayIndex + 1 < dayRanges.length && range.end < dateEnv.add(dayRanges[dayIndex + 1].start, nextDayThreshold)) {\n          seg.end = range.end;\n          seg.isEnd = true;\n          break;\n        }\n      }\n    }\n    return segs;\n  }\n}\nfunction renderNoEventsInner(renderProps) {\n  return renderProps.text;\n}\nfunction computeDateVars(dateProfile) {\n  let dayStart = startOfDay(dateProfile.renderRange.start);\n  let viewEnd = dateProfile.renderRange.end;\n  let dayDates = [];\n  let dayRanges = [];\n  while (dayStart < viewEnd) {\n    dayDates.push(dayStart);\n    dayRanges.push({\n      start: dayStart,\n      end: addDays(dayStart, 1)\n    });\n    dayStart = addDays(dayStart, 1);\n  }\n  return {\n    dayDates,\n    dayRanges\n  };\n}\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs) {\n  let segsByDay = []; // sparse array\n  let i;\n  let seg;\n  for (i = 0; i < segs.length; i += 1) {\n    seg = segs[i];\n    (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = [])).push(seg);\n  }\n  return segsByDay;\n}\nvar css_248z = \":root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:\\\"\\\";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}\";\ninjectStyles(css_248z);\nexport { ListView };", "map": {"version": 3, "names": ["BaseComponent", "getUniqueDomId", "getDateMeta", "buildNavLinkAttrs", "ContentContainer", "getDayClassNames", "formatDayString", "createFormatter", "EventContainer", "getSegAnchorAttrs", "isMultiDayRange", "buildSegTimeText", "DateComponent", "memoize", "ViewContainer", "<PERSON><PERSON><PERSON>", "NowTimer", "sortEventSegs", "getSegMeta", "sliceEventStore", "intersectRanges", "startOfDay", "addDays", "injectStyles", "createElement", "Fragment", "ListViewHeaderRow", "constructor", "arguments", "state", "textId", "render", "theme", "dateEnv", "options", "viewApi", "context", "cellId", "dayDate", "todayRange", "props", "dayMeta", "text", "listDayFormat", "format", "sideText", "listDaySideFormat", "renderProps", "Object", "assign", "date", "toDate", "view", "navLinkAttrs", "sideNavLinkAttrs", "elTag", "elClasses", "elAttrs", "generatorName", "customGenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultGenerator", "renderInnerContent", "classNameGenerator", "dayHeaderClassNames", "didMount", "dayHeaderDidMount", "will<PERSON>n<PERSON>", "dayHeaderWillUnmount", "InnerContent", "scope", "colSpan", "id", "getClass", "className", "DEFAULT_TIME_FORMAT", "hour", "minute", "meridiem", "ListViewEventRow", "seg", "timeHeaderId", "eventHeaderId", "dateHeaderId", "timeFormat", "eventTimeFormat", "eventRange", "def", "url", "renderEventInnerContent", "timeText", "disableDragging", "disableResizing", "eventContentArg", "buildTimeContent", "style", "borderColor", "backgroundColor", "headers", "interactiveAttrs", "title", "displayEventTime", "eventDef", "eventInstance", "instance", "doAllDay", "allDay", "range", "isStart", "start", "end", "isEnd", "allDayText", "allDayContent", "renderAllDayInner", "allDayClassNames", "allDayDidMount", "allDayWillUnmount", "ListView", "computeDateVars", "eventStoreToSegs", "_eventStoreToSegs", "dateHeaderIdRoot", "setRootEl", "rootEl", "registerInteractiveComponent", "el", "unregisterInteractiveComponent", "dayDates", "<PERSON><PERSON><PERSON><PERSON>", "dateProfile", "eventSegs", "eventStore", "eventUiBases", "elRef", "stickyHeaderDates", "viewSpec", "liquid", "isHeightAuto", "overflowX", "overflowY", "length", "renderSegList", "renderEmptyMessage", "noEventsText", "noEventsContent", "renderNoEventsInner", "noEventsClassNames", "noEventsDidMount", "noEventsWillUnmount", "allSegs", "segsByDay", "groupSegsByDay", "unit", "nowDate", "innerNodes", "dayIndex", "daySegs", "dayStr", "push", "key", "eventOrder", "instanceId", "isDragging", "isResizing", "isDateSelecting", "isSelected", "timeHint", "eventHint", "eventRangesToSegs", "activeRange", "nextDayThreshold", "fg", "event<PERSON>ang<PERSON>", "segs", "eventRangeToSegs", "seg<PERSON><PERSON><PERSON>", "component", "valueOf", "add", "dayStart", "renderRange", "viewEnd", "i", "css_248z"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@fullcalendar/list/internal.js"], "sourcesContent": ["import { BaseComponent, getUniqueDomId, getDateMeta, buildNavLinkAttrs, ContentContainer, getDayClassNames, formatDayString, createFormatter, EventContainer, getSegAnchorAttrs, isMultiDayRange, buildSegTimeText, DateComponent, memoize, ViewContainer, Scroller, NowTimer, sortEventSegs, getSegMeta, sliceEventStore, intersectRanges, startOfDay, addDays, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, Fragment } from '@fullcalendar/core/preact.js';\n\nclass ListViewHeaderRow extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.state = {\n            textId: getUniqueDomId(),\n        };\n    }\n    render() {\n        let { theme, dateEnv, options, viewApi } = this.context;\n        let { cellId, dayDate, todayRange } = this.props;\n        let { textId } = this.state;\n        let dayMeta = getDateMeta(dayDate, todayRange);\n        // will ever be falsy?\n        let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : '';\n        // will ever be falsy? also, BAD NAME \"alt\"\n        let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : '';\n        let renderProps = Object.assign({ date: dateEnv.toDate(dayDate), view: viewApi, textId,\n            text,\n            sideText, navLinkAttrs: buildNavLinkAttrs(this.context, dayDate), sideNavLinkAttrs: buildNavLinkAttrs(this.context, dayDate, 'day', false) }, dayMeta);\n        // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n        return (createElement(ContentContainer, { elTag: \"tr\", elClasses: [\n                'fc-list-day',\n                ...getDayClassNames(dayMeta, theme),\n            ], elAttrs: {\n                'data-date': formatDayString(dayDate),\n            }, renderProps: renderProps, generatorName: \"dayHeaderContent\", customGenerator: options.dayHeaderContent, defaultGenerator: renderInnerContent, classNameGenerator: options.dayHeaderClassNames, didMount: options.dayHeaderDidMount, willUnmount: options.dayHeaderWillUnmount }, (InnerContent) => ( // TODO: force-hide top border based on :first-child\n        createElement(\"th\", { scope: \"colgroup\", colSpan: 3, id: cellId, \"aria-labelledby\": textId },\n            createElement(InnerContent, { elTag: \"div\", elClasses: [\n                    'fc-list-day-cushion',\n                    theme.getClass('tableCellShaded'),\n                ] })))));\n    }\n}\nfunction renderInnerContent(props) {\n    return (createElement(Fragment, null,\n        props.text && (createElement(\"a\", Object.assign({ id: props.textId, className: \"fc-list-day-text\" }, props.navLinkAttrs), props.text)),\n        props.sideText && ( /* not keyboard tabbable */createElement(\"a\", Object.assign({ \"aria-hidden\": true, className: \"fc-list-day-side-text\" }, props.sideNavLinkAttrs), props.sideText))));\n}\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    meridiem: 'short',\n});\nclass ListViewEventRow extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg, timeHeaderId, eventHeaderId, dateHeaderId } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TIME_FORMAT;\n        return (createElement(EventContainer, Object.assign({}, props, { elTag: \"tr\", elClasses: [\n                'fc-list-event',\n                seg.eventRange.def.url && 'fc-event-forced-url',\n            ], defaultGenerator: () => renderEventInnerContent(seg, context) /* weird */, seg: seg, timeText: \"\", disableDragging: true, disableResizing: true }), (InnerContent, eventContentArg) => (createElement(Fragment, null,\n            buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId),\n            createElement(\"td\", { \"aria-hidden\": true, className: \"fc-list-event-graphic\" },\n                createElement(\"span\", { className: \"fc-list-event-dot\", style: {\n                        borderColor: eventContentArg.borderColor || eventContentArg.backgroundColor,\n                    } })),\n            createElement(InnerContent, { elTag: \"td\", elClasses: ['fc-list-event-title'], elAttrs: { headers: `${eventHeaderId} ${dateHeaderId}` } })))));\n    }\n}\nfunction renderEventInnerContent(seg, context) {\n    let interactiveAttrs = getSegAnchorAttrs(seg, context);\n    return (createElement(\"a\", Object.assign({}, interactiveAttrs), seg.eventRange.def.title));\n}\nfunction buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId) {\n    let { options } = context;\n    if (options.displayEventTime !== false) {\n        let eventDef = seg.eventRange.def;\n        let eventInstance = seg.eventRange.instance;\n        let doAllDay = false;\n        let timeText;\n        if (eventDef.allDay) {\n            doAllDay = true;\n        }\n        else if (isMultiDayRange(seg.eventRange.range)) { // TODO: use (!isStart || !isEnd) instead?\n            if (seg.isStart) {\n                timeText = buildSegTimeText(seg, timeFormat, context, null, null, eventInstance.range.start, seg.end);\n            }\n            else if (seg.isEnd) {\n                timeText = buildSegTimeText(seg, timeFormat, context, null, null, seg.start, eventInstance.range.end);\n            }\n            else {\n                doAllDay = true;\n            }\n        }\n        else {\n            timeText = buildSegTimeText(seg, timeFormat, context);\n        }\n        if (doAllDay) {\n            let renderProps = {\n                text: context.options.allDayText,\n                view: context.viewApi,\n            };\n            return (createElement(ContentContainer, { elTag: \"td\", elClasses: ['fc-list-event-time'], elAttrs: {\n                    headers: `${timeHeaderId} ${dateHeaderId}`,\n                }, renderProps: renderProps, generatorName: \"allDayContent\", customGenerator: options.allDayContent, defaultGenerator: renderAllDayInner, classNameGenerator: options.allDayClassNames, didMount: options.allDayDidMount, willUnmount: options.allDayWillUnmount }));\n        }\n        return (createElement(\"td\", { className: \"fc-list-event-time\" }, timeText));\n    }\n    return null;\n}\nfunction renderAllDayInner(renderProps) {\n    return renderProps.text;\n}\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nclass ListView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.computeDateVars = memoize(computeDateVars);\n        this.eventStoreToSegs = memoize(this._eventStoreToSegs);\n        this.state = {\n            timeHeaderId: getUniqueDomId(),\n            eventHeaderId: getUniqueDomId(),\n            dateHeaderIdRoot: getUniqueDomId(),\n        };\n        this.setRootEl = (rootEl) => {\n            if (rootEl) {\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                });\n            }\n            else {\n                this.context.unregisterInteractiveComponent(this);\n            }\n        };\n    }\n    render() {\n        let { props, context } = this;\n        let { dayDates, dayRanges } = this.computeDateVars(props.dateProfile);\n        let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges);\n        return (createElement(ViewContainer, { elRef: this.setRootEl, elClasses: [\n                'fc-list',\n                context.theme.getClass('table'),\n                context.options.stickyHeaderDates !== false ?\n                    'fc-list-sticky' :\n                    '',\n            ], viewSpec: context.viewSpec },\n            createElement(Scroller, { liquid: !props.isHeightAuto, overflowX: props.isHeightAuto ? 'visible' : 'hidden', overflowY: props.isHeightAuto ? 'visible' : 'auto' }, eventSegs.length > 0 ?\n                this.renderSegList(eventSegs, dayDates) :\n                this.renderEmptyMessage())));\n    }\n    renderEmptyMessage() {\n        let { options, viewApi } = this.context;\n        let renderProps = {\n            text: options.noEventsText,\n            view: viewApi,\n        };\n        return (createElement(ContentContainer, { elTag: \"div\", elClasses: ['fc-list-empty'], renderProps: renderProps, generatorName: \"noEventsContent\", customGenerator: options.noEventsContent, defaultGenerator: renderNoEventsInner, classNameGenerator: options.noEventsClassNames, didMount: options.noEventsDidMount, willUnmount: options.noEventsWillUnmount }, (InnerContent) => (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-list-empty-cushion'] }))));\n    }\n    renderSegList(allSegs, dayDates) {\n        let { theme, options } = this.context;\n        let { timeHeaderId, eventHeaderId, dateHeaderIdRoot } = this.state;\n        let segsByDay = groupSegsByDay(allSegs); // sparse array\n        return (createElement(NowTimer, { unit: \"day\" }, (nowDate, todayRange) => {\n            let innerNodes = [];\n            for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n                let daySegs = segsByDay[dayIndex];\n                if (daySegs) { // sparse array, so might be undefined\n                    let dayStr = formatDayString(dayDates[dayIndex]);\n                    let dateHeaderId = dateHeaderIdRoot + '-' + dayStr;\n                    // append a day header\n                    innerNodes.push(createElement(ListViewHeaderRow, { key: dayStr, cellId: dateHeaderId, dayDate: dayDates[dayIndex], todayRange: todayRange }));\n                    daySegs = sortEventSegs(daySegs, options.eventOrder);\n                    for (let seg of daySegs) {\n                        innerNodes.push(createElement(ListViewEventRow, Object.assign({ key: dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */, seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: false, timeHeaderId: timeHeaderId, eventHeaderId: eventHeaderId, dateHeaderId: dateHeaderId }, getSegMeta(seg, todayRange, nowDate))));\n                    }\n                }\n            }\n            return (createElement(\"table\", { className: 'fc-list-table ' + theme.getClass('table') },\n                createElement(\"thead\", null,\n                    createElement(\"tr\", null,\n                        createElement(\"th\", { scope: \"col\", id: timeHeaderId }, options.timeHint),\n                        createElement(\"th\", { scope: \"col\", \"aria-hidden\": true }),\n                        createElement(\"th\", { scope: \"col\", id: eventHeaderId }, options.eventHint))),\n                createElement(\"tbody\", null, innerNodes)));\n        }));\n    }\n    _eventStoreToSegs(eventStore, eventUiBases, dayRanges) {\n        return this.eventRangesToSegs(sliceEventStore(eventStore, eventUiBases, this.props.dateProfile.activeRange, this.context.options.nextDayThreshold).fg, dayRanges);\n    }\n    eventRangesToSegs(eventRanges, dayRanges) {\n        let segs = [];\n        for (let eventRange of eventRanges) {\n            segs.push(...this.eventRangeToSegs(eventRange, dayRanges));\n        }\n        return segs;\n    }\n    eventRangeToSegs(eventRange, dayRanges) {\n        let { dateEnv } = this.context;\n        let { nextDayThreshold } = this.context.options;\n        let range = eventRange.range;\n        let allDay = eventRange.def.allDay;\n        let dayIndex;\n        let segRange;\n        let seg;\n        let segs = [];\n        for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n            segRange = intersectRanges(range, dayRanges[dayIndex]);\n            if (segRange) {\n                seg = {\n                    component: this,\n                    eventRange,\n                    start: segRange.start,\n                    end: segRange.end,\n                    isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n                    isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n                    dayIndex,\n                };\n                segs.push(seg);\n                // detect when range won't go fully into the next day,\n                // and mutate the latest seg to the be the end.\n                if (!seg.isEnd && !allDay &&\n                    dayIndex + 1 < dayRanges.length &&\n                    range.end <\n                        dateEnv.add(dayRanges[dayIndex + 1].start, nextDayThreshold)) {\n                    seg.end = range.end;\n                    seg.isEnd = true;\n                    break;\n                }\n            }\n        }\n        return segs;\n    }\n}\nfunction renderNoEventsInner(renderProps) {\n    return renderProps.text;\n}\nfunction computeDateVars(dateProfile) {\n    let dayStart = startOfDay(dateProfile.renderRange.start);\n    let viewEnd = dateProfile.renderRange.end;\n    let dayDates = [];\n    let dayRanges = [];\n    while (dayStart < viewEnd) {\n        dayDates.push(dayStart);\n        dayRanges.push({\n            start: dayStart,\n            end: addDays(dayStart, 1),\n        });\n        dayStart = addDays(dayStart, 1);\n    }\n    return { dayDates, dayRanges };\n}\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs) {\n    let segsByDay = []; // sparse array\n    let i;\n    let seg;\n    for (i = 0; i < segs.length; i += 1) {\n        seg = segs[i];\n        (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = []))\n            .push(seg);\n    }\n    return segsByDay;\n}\n\nvar css_248z = \":root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:\\\"\\\";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}\";\ninjectStyles(css_248z);\n\nexport { ListView };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,EAAEC,eAAe,EAAEC,eAAe,EAAEC,UAAU,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gCAAgC;AACrZ,SAASC,aAAa,EAAEC,QAAQ,QAAQ,8BAA8B;AAEtE,MAAMC,iBAAiB,SAAS1B,aAAa,CAAC;EAC1C2B,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,KAAK,GAAG;MACTC,MAAM,EAAE7B,cAAc,CAAC;IAC3B,CAAC;EACL;EACA8B,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEC,KAAK;MAAEC,OAAO;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACC,OAAO;IACvD,IAAI;MAAEC,MAAM;MAAEC,OAAO;MAAEC;IAAW,CAAC,GAAG,IAAI,CAACC,KAAK;IAChD,IAAI;MAAEV;IAAO,CAAC,GAAG,IAAI,CAACD,KAAK;IAC3B,IAAIY,OAAO,GAAGvC,WAAW,CAACoC,OAAO,EAAEC,UAAU,CAAC;IAC9C;IACA,IAAIG,IAAI,GAAGR,OAAO,CAACS,aAAa,GAAGV,OAAO,CAACW,MAAM,CAACN,OAAO,EAAEJ,OAAO,CAACS,aAAa,CAAC,GAAG,EAAE;IACtF;IACA,IAAIE,QAAQ,GAAGX,OAAO,CAACY,iBAAiB,GAAGb,OAAO,CAACW,MAAM,CAACN,OAAO,EAAEJ,OAAO,CAACY,iBAAiB,CAAC,GAAG,EAAE;IAClG,IAAIC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;MAAEC,IAAI,EAAEjB,OAAO,CAACkB,MAAM,CAACb,OAAO,CAAC;MAAEc,IAAI,EAAEjB,OAAO;MAAEL,MAAM;MAClFY,IAAI;MACJG,QAAQ;MAAEQ,YAAY,EAAElD,iBAAiB,CAAC,IAAI,CAACiC,OAAO,EAAEE,OAAO,CAAC;MAAEgB,gBAAgB,EAAEnD,iBAAiB,CAAC,IAAI,CAACiC,OAAO,EAAEE,OAAO,EAAE,KAAK,EAAE,KAAK;IAAE,CAAC,EAAEG,OAAO,CAAC;IAC1J;IACA,OAAQjB,aAAa,CAACpB,gBAAgB,EAAE;MAAEmD,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,CAC1D,aAAa,EACb,GAAGnD,gBAAgB,CAACoC,OAAO,EAAET,KAAK,CAAC,CACtC;MAAEyB,OAAO,EAAE;QACR,WAAW,EAAEnD,eAAe,CAACgC,OAAO;MACxC,CAAC;MAAES,WAAW,EAAEA,WAAW;MAAEW,aAAa,EAAE,kBAAkB;MAAEC,eAAe,EAAEzB,OAAO,CAAC0B,gBAAgB;MAAEC,gBAAgB,EAAEC,kBAAkB;MAAEC,kBAAkB,EAAE7B,OAAO,CAAC8B,mBAAmB;MAAEC,QAAQ,EAAE/B,OAAO,CAACgC,iBAAiB;MAAEC,WAAW,EAAEjC,OAAO,CAACkC;IAAqB,CAAC,EAAGC,YAAY;IAAO;IAC5S7C,aAAa,CAAC,IAAI,EAAE;MAAE8C,KAAK,EAAE,UAAU;MAAEC,OAAO,EAAE,CAAC;MAAEC,EAAE,EAAEnC,MAAM;MAAE,iBAAiB,EAAEP;IAAO,CAAC,EACxFN,aAAa,CAAC6C,YAAY,EAAE;MAAEd,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAC/C,qBAAqB,EACrBxB,KAAK,CAACyC,QAAQ,CAAC,iBAAiB,CAAC;IACnC,CAAC,CAAC,CAAE,CAAC;EACnB;AACJ;AACA,SAASX,kBAAkBA,CAACtB,KAAK,EAAE;EAC/B,OAAQhB,aAAa,CAACC,QAAQ,EAAE,IAAI,EAChCe,KAAK,CAACE,IAAI,IAAKlB,aAAa,CAAC,GAAG,EAAEwB,MAAM,CAACC,MAAM,CAAC;IAAEuB,EAAE,EAAEhC,KAAK,CAACV,MAAM;IAAE4C,SAAS,EAAE;EAAmB,CAAC,EAAElC,KAAK,CAACa,YAAY,CAAC,EAAEb,KAAK,CAACE,IAAI,CAAE,EACtIF,KAAK,CAACK,QAAQ,MAAM,2BAA2BrB,aAAa,CAAC,GAAG,EAAEwB,MAAM,CAACC,MAAM,CAAC;IAAE,aAAa,EAAE,IAAI;IAAEyB,SAAS,EAAE;EAAwB,CAAC,EAAElC,KAAK,CAACc,gBAAgB,CAAC,EAAEd,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;AAC/L;AAEA,MAAM8B,mBAAmB,GAAGpE,eAAe,CAAC;EACxCqE,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,gBAAgB,SAAS/E,aAAa,CAAC;EACzC+B,MAAMA,CAAA,EAAG;IACL,IAAI;MAAES,KAAK;MAAEJ;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAEF;IAAQ,CAAC,GAAGE,OAAO;IACzB,IAAI;MAAE4C,GAAG;MAAEC,YAAY;MAAEC,aAAa;MAAEC;IAAa,CAAC,GAAG3C,KAAK;IAC9D,IAAI4C,UAAU,GAAGlD,OAAO,CAACmD,eAAe,IAAIV,mBAAmB;IAC/D,OAAQnD,aAAa,CAAChB,cAAc,EAAEwC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,KAAK,EAAE;MAAEe,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,CACjF,eAAe,EACfwB,GAAG,CAACM,UAAU,CAACC,GAAG,CAACC,GAAG,IAAI,qBAAqB,CAClD;MAAE3B,gBAAgB,EAAEA,CAAA,KAAM4B,uBAAuB,CAACT,GAAG,EAAE5C,OAAO,CAAC,CAAC;MAAa4C,GAAG,EAAEA,GAAG;MAAEU,QAAQ,EAAE,EAAE;MAAEC,eAAe,EAAE,IAAI;MAAEC,eAAe,EAAE;IAAK,CAAC,CAAC,EAAE,CAACvB,YAAY,EAAEwB,eAAe,KAAMrE,aAAa,CAACC,QAAQ,EAAE,IAAI,EACvNqE,gBAAgB,CAACd,GAAG,EAAEI,UAAU,EAAEhD,OAAO,EAAE6C,YAAY,EAAEE,YAAY,CAAC,EACtE3D,aAAa,CAAC,IAAI,EAAE;MAAE,aAAa,EAAE,IAAI;MAAEkD,SAAS,EAAE;IAAwB,CAAC,EAC3ElD,aAAa,CAAC,MAAM,EAAE;MAAEkD,SAAS,EAAE,mBAAmB;MAAEqB,KAAK,EAAE;QACvDC,WAAW,EAAEH,eAAe,CAACG,WAAW,IAAIH,eAAe,CAACI;MAChE;IAAE,CAAC,CAAC,CAAC,EACbzE,aAAa,CAAC6C,YAAY,EAAE;MAAEd,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE,CAAC,qBAAqB,CAAC;MAAEC,OAAO,EAAE;QAAEyC,OAAO,EAAE,GAAGhB,aAAa,IAAIC,YAAY;MAAG;IAAE,CAAC,CAAC,CAAE,CAAC;EACrJ;AACJ;AACA,SAASM,uBAAuBA,CAACT,GAAG,EAAE5C,OAAO,EAAE;EAC3C,IAAI+D,gBAAgB,GAAG1F,iBAAiB,CAACuE,GAAG,EAAE5C,OAAO,CAAC;EACtD,OAAQZ,aAAa,CAAC,GAAG,EAAEwB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEkD,gBAAgB,CAAC,EAAEnB,GAAG,CAACM,UAAU,CAACC,GAAG,CAACa,KAAK,CAAC;AAC7F;AACA,SAASN,gBAAgBA,CAACd,GAAG,EAAEI,UAAU,EAAEhD,OAAO,EAAE6C,YAAY,EAAEE,YAAY,EAAE;EAC5E,IAAI;IAAEjD;EAAQ,CAAC,GAAGE,OAAO;EACzB,IAAIF,OAAO,CAACmE,gBAAgB,KAAK,KAAK,EAAE;IACpC,IAAIC,QAAQ,GAAGtB,GAAG,CAACM,UAAU,CAACC,GAAG;IACjC,IAAIgB,aAAa,GAAGvB,GAAG,CAACM,UAAU,CAACkB,QAAQ;IAC3C,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIf,QAAQ;IACZ,IAAIY,QAAQ,CAACI,MAAM,EAAE;MACjBD,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAI/F,eAAe,CAACsE,GAAG,CAACM,UAAU,CAACqB,KAAK,CAAC,EAAE;MAAE;MAC9C,IAAI3B,GAAG,CAAC4B,OAAO,EAAE;QACblB,QAAQ,GAAG/E,gBAAgB,CAACqE,GAAG,EAAEI,UAAU,EAAEhD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEmE,aAAa,CAACI,KAAK,CAACE,KAAK,EAAE7B,GAAG,CAAC8B,GAAG,CAAC;MACzG,CAAC,MACI,IAAI9B,GAAG,CAAC+B,KAAK,EAAE;QAChBrB,QAAQ,GAAG/E,gBAAgB,CAACqE,GAAG,EAAEI,UAAU,EAAEhD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE4C,GAAG,CAAC6B,KAAK,EAAEN,aAAa,CAACI,KAAK,CAACG,GAAG,CAAC;MACzG,CAAC,MACI;QACDL,QAAQ,GAAG,IAAI;MACnB;IACJ,CAAC,MACI;MACDf,QAAQ,GAAG/E,gBAAgB,CAACqE,GAAG,EAAEI,UAAU,EAAEhD,OAAO,CAAC;IACzD;IACA,IAAIqE,QAAQ,EAAE;MACV,IAAI1D,WAAW,GAAG;QACdL,IAAI,EAAEN,OAAO,CAACF,OAAO,CAAC8E,UAAU;QAChC5D,IAAI,EAAEhB,OAAO,CAACD;MAClB,CAAC;MACD,OAAQX,aAAa,CAACpB,gBAAgB,EAAE;QAAEmD,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE,CAAC,oBAAoB,CAAC;QAAEC,OAAO,EAAE;UAC3FyC,OAAO,EAAE,GAAGjB,YAAY,IAAIE,YAAY;QAC5C,CAAC;QAAEpC,WAAW,EAAEA,WAAW;QAAEW,aAAa,EAAE,eAAe;QAAEC,eAAe,EAAEzB,OAAO,CAAC+E,aAAa;QAAEpD,gBAAgB,EAAEqD,iBAAiB;QAAEnD,kBAAkB,EAAE7B,OAAO,CAACiF,gBAAgB;QAAElD,QAAQ,EAAE/B,OAAO,CAACkF,cAAc;QAAEjD,WAAW,EAAEjC,OAAO,CAACmF;MAAkB,CAAC,CAAC;IAC3Q;IACA,OAAQ7F,aAAa,CAAC,IAAI,EAAE;MAAEkD,SAAS,EAAE;IAAqB,CAAC,EAAEgB,QAAQ,CAAC;EAC9E;EACA,OAAO,IAAI;AACf;AACA,SAASwB,iBAAiBA,CAACnE,WAAW,EAAE;EACpC,OAAOA,WAAW,CAACL,IAAI;AAC3B;;AAEA;AACA;AACA;AACA,MAAM4E,QAAQ,SAAS1G,aAAa,CAAC;EACjCe,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC2F,eAAe,GAAG1G,OAAO,CAAC0G,eAAe,CAAC;IAC/C,IAAI,CAACC,gBAAgB,GAAG3G,OAAO,CAAC,IAAI,CAAC4G,iBAAiB,CAAC;IACvD,IAAI,CAAC5F,KAAK,GAAG;MACToD,YAAY,EAAEhF,cAAc,CAAC,CAAC;MAC9BiF,aAAa,EAAEjF,cAAc,CAAC,CAAC;MAC/ByH,gBAAgB,EAAEzH,cAAc,CAAC;IACrC,CAAC;IACD,IAAI,CAAC0H,SAAS,GAAIC,MAAM,IAAK;MACzB,IAAIA,MAAM,EAAE;QACR,IAAI,CAACxF,OAAO,CAACyF,4BAA4B,CAAC,IAAI,EAAE;UAC5CC,EAAE,EAAEF;QACR,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACxF,OAAO,CAAC2F,8BAA8B,CAAC,IAAI,CAAC;MACrD;IACJ,CAAC;EACL;EACAhG,MAAMA,CAAA,EAAG;IACL,IAAI;MAAES,KAAK;MAAEJ;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAE4F,QAAQ;MAAEC;IAAU,CAAC,GAAG,IAAI,CAACV,eAAe,CAAC/E,KAAK,CAAC0F,WAAW,CAAC;IACrE,IAAIC,SAAS,GAAG,IAAI,CAACX,gBAAgB,CAAChF,KAAK,CAAC4F,UAAU,EAAE5F,KAAK,CAAC6F,YAAY,EAAEJ,SAAS,CAAC;IACtF,OAAQzG,aAAa,CAACV,aAAa,EAAE;MAAEwH,KAAK,EAAE,IAAI,CAACX,SAAS;MAAEnE,SAAS,EAAE,CACjE,SAAS,EACTpB,OAAO,CAACJ,KAAK,CAACyC,QAAQ,CAAC,OAAO,CAAC,EAC/BrC,OAAO,CAACF,OAAO,CAACqG,iBAAiB,KAAK,KAAK,GACvC,gBAAgB,GAChB,EAAE,CACT;MAAEC,QAAQ,EAAEpG,OAAO,CAACoG;IAAS,CAAC,EAC/BhH,aAAa,CAACT,QAAQ,EAAE;MAAE0H,MAAM,EAAE,CAACjG,KAAK,CAACkG,YAAY;MAAEC,SAAS,EAAEnG,KAAK,CAACkG,YAAY,GAAG,SAAS,GAAG,QAAQ;MAAEE,SAAS,EAAEpG,KAAK,CAACkG,YAAY,GAAG,SAAS,GAAG;IAAO,CAAC,EAAEP,SAAS,CAACU,MAAM,GAAG,CAAC,GACnL,IAAI,CAACC,aAAa,CAACX,SAAS,EAAEH,QAAQ,CAAC,GACvC,IAAI,CAACe,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACvC;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI;MAAE7G,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACC,OAAO;IACvC,IAAIW,WAAW,GAAG;MACdL,IAAI,EAAER,OAAO,CAAC8G,YAAY;MAC1B5F,IAAI,EAAEjB;IACV,CAAC;IACD,OAAQX,aAAa,CAACpB,gBAAgB,EAAE;MAAEmD,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAAC,eAAe,CAAC;MAAET,WAAW,EAAEA,WAAW;MAAEW,aAAa,EAAE,iBAAiB;MAAEC,eAAe,EAAEzB,OAAO,CAAC+G,eAAe;MAAEpF,gBAAgB,EAAEqF,mBAAmB;MAAEnF,kBAAkB,EAAE7B,OAAO,CAACiH,kBAAkB;MAAElF,QAAQ,EAAE/B,OAAO,CAACkH,gBAAgB;MAAEjF,WAAW,EAAEjC,OAAO,CAACmH;IAAoB,CAAC,EAAGhF,YAAY,IAAM7C,aAAa,CAAC6C,YAAY,EAAE;MAAEd,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAAC,uBAAuB;IAAE,CAAC,CAAE,CAAC;EAC/c;EACAsF,aAAaA,CAACQ,OAAO,EAAEtB,QAAQ,EAAE;IAC7B,IAAI;MAAEhG,KAAK;MAAEE;IAAQ,CAAC,GAAG,IAAI,CAACE,OAAO;IACrC,IAAI;MAAE6C,YAAY;MAAEC,aAAa;MAAEwC;IAAiB,CAAC,GAAG,IAAI,CAAC7F,KAAK;IAClE,IAAI0H,SAAS,GAAGC,cAAc,CAACF,OAAO,CAAC,CAAC,CAAC;IACzC,OAAQ9H,aAAa,CAACR,QAAQ,EAAE;MAAEyI,IAAI,EAAE;IAAM,CAAC,EAAE,CAACC,OAAO,EAAEnH,UAAU,KAAK;MACtE,IAAIoH,UAAU,GAAG,EAAE;MACnB,KAAK,IAAIC,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAGL,SAAS,CAACV,MAAM,EAAEe,QAAQ,IAAI,CAAC,EAAE;QAC/D,IAAIC,OAAO,GAAGN,SAAS,CAACK,QAAQ,CAAC;QACjC,IAAIC,OAAO,EAAE;UAAE;UACX,IAAIC,MAAM,GAAGxJ,eAAe,CAAC0H,QAAQ,CAAC4B,QAAQ,CAAC,CAAC;UAChD,IAAIzE,YAAY,GAAGuC,gBAAgB,GAAG,GAAG,GAAGoC,MAAM;UAClD;UACAH,UAAU,CAACI,IAAI,CAACvI,aAAa,CAACE,iBAAiB,EAAE;YAAEsI,GAAG,EAAEF,MAAM;YAAEzH,MAAM,EAAE8C,YAAY;YAAE7C,OAAO,EAAE0F,QAAQ,CAAC4B,QAAQ,CAAC;YAAErH,UAAU,EAAEA;UAAW,CAAC,CAAC,CAAC;UAC7IsH,OAAO,GAAG5I,aAAa,CAAC4I,OAAO,EAAE3H,OAAO,CAAC+H,UAAU,CAAC;UACpD,KAAK,IAAIjF,GAAG,IAAI6E,OAAO,EAAE;YACrBF,UAAU,CAACI,IAAI,CAACvI,aAAa,CAACuD,gBAAgB,EAAE/B,MAAM,CAACC,MAAM,CAAC;cAAE+G,GAAG,EAAEF,MAAM,GAAG,GAAG,GAAG9E,GAAG,CAACM,UAAU,CAACkB,QAAQ,CAAC0D,UAAU,CAAC;cAA2ClF,GAAG,EAAEA,GAAG;cAAEmF,UAAU,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAEC,eAAe,EAAE,KAAK;cAAEC,UAAU,EAAE,KAAK;cAAErF,YAAY,EAAEA,YAAY;cAAEC,aAAa,EAAEA,aAAa;cAAEC,YAAY,EAAEA;YAAa,CAAC,EAAEjE,UAAU,CAAC8D,GAAG,EAAEzC,UAAU,EAAEmH,OAAO,CAAC,CAAC,CAAC,CAAC;UAChY;QACJ;MACJ;MACA,OAAQlI,aAAa,CAAC,OAAO,EAAE;QAAEkD,SAAS,EAAE,gBAAgB,GAAG1C,KAAK,CAACyC,QAAQ,CAAC,OAAO;MAAE,CAAC,EACpFjD,aAAa,CAAC,OAAO,EAAE,IAAI,EACvBA,aAAa,CAAC,IAAI,EAAE,IAAI,EACpBA,aAAa,CAAC,IAAI,EAAE;QAAE8C,KAAK,EAAE,KAAK;QAAEE,EAAE,EAAES;MAAa,CAAC,EAAE/C,OAAO,CAACqI,QAAQ,CAAC,EACzE/I,aAAa,CAAC,IAAI,EAAE;QAAE8C,KAAK,EAAE,KAAK;QAAE,aAAa,EAAE;MAAK,CAAC,CAAC,EAC1D9C,aAAa,CAAC,IAAI,EAAE;QAAE8C,KAAK,EAAE,KAAK;QAAEE,EAAE,EAAEU;MAAc,CAAC,EAAEhD,OAAO,CAACsI,SAAS,CAAC,CAAC,CAAC,EACrFhJ,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEmI,UAAU,CAAC,CAAC;IACjD,CAAC,CAAC;EACN;EACAlC,iBAAiBA,CAACW,UAAU,EAAEC,YAAY,EAAEJ,SAAS,EAAE;IACnD,OAAO,IAAI,CAACwC,iBAAiB,CAACtJ,eAAe,CAACiH,UAAU,EAAEC,YAAY,EAAE,IAAI,CAAC7F,KAAK,CAAC0F,WAAW,CAACwC,WAAW,EAAE,IAAI,CAACtI,OAAO,CAACF,OAAO,CAACyI,gBAAgB,CAAC,CAACC,EAAE,EAAE3C,SAAS,CAAC;EACrK;EACAwC,iBAAiBA,CAACI,WAAW,EAAE5C,SAAS,EAAE;IACtC,IAAI6C,IAAI,GAAG,EAAE;IACb,KAAK,IAAIxF,UAAU,IAAIuF,WAAW,EAAE;MAChCC,IAAI,CAACf,IAAI,CAAC,GAAG,IAAI,CAACgB,gBAAgB,CAACzF,UAAU,EAAE2C,SAAS,CAAC,CAAC;IAC9D;IACA,OAAO6C,IAAI;EACf;EACAC,gBAAgBA,CAACzF,UAAU,EAAE2C,SAAS,EAAE;IACpC,IAAI;MAAEhG;IAAQ,CAAC,GAAG,IAAI,CAACG,OAAO;IAC9B,IAAI;MAAEuI;IAAiB,CAAC,GAAG,IAAI,CAACvI,OAAO,CAACF,OAAO;IAC/C,IAAIyE,KAAK,GAAGrB,UAAU,CAACqB,KAAK;IAC5B,IAAID,MAAM,GAAGpB,UAAU,CAACC,GAAG,CAACmB,MAAM;IAClC,IAAIkD,QAAQ;IACZ,IAAIoB,QAAQ;IACZ,IAAIhG,GAAG;IACP,IAAI8F,IAAI,GAAG,EAAE;IACb,KAAKlB,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG3B,SAAS,CAACY,MAAM,EAAEe,QAAQ,IAAI,CAAC,EAAE;MAC3DoB,QAAQ,GAAG5J,eAAe,CAACuF,KAAK,EAAEsB,SAAS,CAAC2B,QAAQ,CAAC,CAAC;MACtD,IAAIoB,QAAQ,EAAE;QACVhG,GAAG,GAAG;UACFiG,SAAS,EAAE,IAAI;UACf3F,UAAU;UACVuB,KAAK,EAAEmE,QAAQ,CAACnE,KAAK;UACrBC,GAAG,EAAEkE,QAAQ,CAAClE,GAAG;UACjBF,OAAO,EAAEtB,UAAU,CAACsB,OAAO,IAAIoE,QAAQ,CAACnE,KAAK,CAACqE,OAAO,CAAC,CAAC,KAAKvE,KAAK,CAACE,KAAK,CAACqE,OAAO,CAAC,CAAC;UACjFnE,KAAK,EAAEzB,UAAU,CAACyB,KAAK,IAAIiE,QAAQ,CAAClE,GAAG,CAACoE,OAAO,CAAC,CAAC,KAAKvE,KAAK,CAACG,GAAG,CAACoE,OAAO,CAAC,CAAC;UACzEtB;QACJ,CAAC;QACDkB,IAAI,CAACf,IAAI,CAAC/E,GAAG,CAAC;QACd;QACA;QACA,IAAI,CAACA,GAAG,CAAC+B,KAAK,IAAI,CAACL,MAAM,IACrBkD,QAAQ,GAAG,CAAC,GAAG3B,SAAS,CAACY,MAAM,IAC/BlC,KAAK,CAACG,GAAG,GACL7E,OAAO,CAACkJ,GAAG,CAAClD,SAAS,CAAC2B,QAAQ,GAAG,CAAC,CAAC,CAAC/C,KAAK,EAAE8D,gBAAgB,CAAC,EAAE;UAClE3F,GAAG,CAAC8B,GAAG,GAAGH,KAAK,CAACG,GAAG;UACnB9B,GAAG,CAAC+B,KAAK,GAAG,IAAI;UAChB;QACJ;MACJ;IACJ;IACA,OAAO+D,IAAI;EACf;AACJ;AACA,SAAS5B,mBAAmBA,CAACnG,WAAW,EAAE;EACtC,OAAOA,WAAW,CAACL,IAAI;AAC3B;AACA,SAAS6E,eAAeA,CAACW,WAAW,EAAE;EAClC,IAAIkD,QAAQ,GAAG/J,UAAU,CAAC6G,WAAW,CAACmD,WAAW,CAACxE,KAAK,CAAC;EACxD,IAAIyE,OAAO,GAAGpD,WAAW,CAACmD,WAAW,CAACvE,GAAG;EACzC,IAAIkB,QAAQ,GAAG,EAAE;EACjB,IAAIC,SAAS,GAAG,EAAE;EAClB,OAAOmD,QAAQ,GAAGE,OAAO,EAAE;IACvBtD,QAAQ,CAAC+B,IAAI,CAACqB,QAAQ,CAAC;IACvBnD,SAAS,CAAC8B,IAAI,CAAC;MACXlD,KAAK,EAAEuE,QAAQ;MACftE,GAAG,EAAExF,OAAO,CAAC8J,QAAQ,EAAE,CAAC;IAC5B,CAAC,CAAC;IACFA,QAAQ,GAAG9J,OAAO,CAAC8J,QAAQ,EAAE,CAAC,CAAC;EACnC;EACA,OAAO;IAAEpD,QAAQ;IAAEC;EAAU,CAAC;AAClC;AACA;AACA,SAASuB,cAAcA,CAACsB,IAAI,EAAE;EAC1B,IAAIvB,SAAS,GAAG,EAAE,CAAC,CAAC;EACpB,IAAIgC,CAAC;EACL,IAAIvG,GAAG;EACP,KAAKuG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACjC,MAAM,EAAE0C,CAAC,IAAI,CAAC,EAAE;IACjCvG,GAAG,GAAG8F,IAAI,CAACS,CAAC,CAAC;IACb,CAAChC,SAAS,CAACvE,GAAG,CAAC4E,QAAQ,CAAC,KAAKL,SAAS,CAACvE,GAAG,CAAC4E,QAAQ,CAAC,GAAG,EAAE,CAAC,EACrDG,IAAI,CAAC/E,GAAG,CAAC;EAClB;EACA,OAAOuE,SAAS;AACpB;AAEA,IAAIiC,QAAQ,GAAG,iuDAAiuD;AAChvDjK,YAAY,CAACiK,QAAQ,CAAC;AAEtB,SAASlE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}