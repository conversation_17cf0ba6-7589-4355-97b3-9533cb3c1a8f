{"ast": null, "code": "import isSameYear from \"../isSameYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport default function isThisYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameYear(dirtyDate, Date.now());\n}", "map": {"version": 3, "names": ["isSameYear", "requiredArgs", "isThisYear", "dirtyDate", "arguments", "Date", "now"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/isThisYear/index.js"], "sourcesContent": ["import isSameYear from \"../isSameYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is in this year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport default function isThisYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  return isSameYear(dirtyDate, Date.now());\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC5CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,UAAU,CAACG,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}