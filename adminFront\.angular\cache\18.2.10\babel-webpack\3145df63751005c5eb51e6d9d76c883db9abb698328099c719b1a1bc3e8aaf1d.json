{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/@core/service/notice.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../@theme/directives/label.directive\";\nimport * as i13 from \"../../../@theme/pipes/date-format.pipe\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = () => [0, 1, 2];\nconst _c2 = a0 => ({\n  \"!text-red\": a0\n});\nfunction NoticeManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E\\u6A94\\u6848 \");\n    i0.ɵɵelement(2, \"i\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r8));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener() {\n      const item_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r8 == null ? null : item_r8.CFile));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_18_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_18_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r8 == null ? null : item_r8.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CHouseHold == null ? null : item_r8.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c1).includes(item_r8.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r8.CExamineStatus] : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r8 == null ? null : item_r8.CApproveDate, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      const dialog_r5 = i0.ɵɵreference(19);\n      return i0.ɵɵresetView(ctx_r3.openModel(dialog_r5, item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onDelete(item_r12));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 24)(1, \"td\")(2, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener() {\n      const item_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r12 == null ? null : item_r12.CFile));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtemplate(12, NoticeManagementComponent_ng_container_16_tr_38_button_12_Template, 2, 0, \"button\", 26)(13, NoticeManagementComponent_ng_container_16_tr_38_button_13_Template, 2, 0, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r12 == null ? null : item_r12.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r12.CHouseHold == null ? null : item_r12.CHouseHold.join(\"\\u3001\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r12.CExamineStatus != null && i0.ɵɵpureFunction0(9, _c1).includes(item_r12.CExamineStatus) ? ctx_r3.cExamineStatusOption[item_r12.CExamineStatus] : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 6, item_r12 == null ? null : item_r12.CApproveDate, \"yyyy-MM-dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDelete);\n  }\n}\nfunction NoticeManagementComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"h4\", 17);\n    i0.ɵɵtext(3, \"\\u5730\\u4E3B\\u6236 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"table\", 18)(5, \"thead\")(6, \"tr\", 19)(7, \"th\", 20);\n    i0.ɵɵtext(8, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 20);\n    i0.ɵɵtext(10, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 20);\n    i0.ɵɵtext(12, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 20);\n    i0.ɵɵtext(14, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 20);\n    i0.ɵɵtext(16, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"tbody\");\n    i0.ɵɵtemplate(18, NoticeManagementComponent_ng_container_16_tr_18_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 22)(20, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndex, $event) || (ctx_r3.pageIndex = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"h4\", 17);\n    i0.ɵɵtext(23, \"\\u92B7\\u552E\\u6236\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"table\", 18)(25, \"thead\")(26, \"tr\", 19)(27, \"th\", 20);\n    i0.ɵɵtext(28, \"\\u6A94\\u6848\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 20);\n    i0.ɵɵtext(30, \"\\u9069\\u7528\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"th\", 20);\n    i0.ɵɵtext(32, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"th\", 20);\n    i0.ɵɵtext(34, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 20);\n    i0.ɵɵtext(36, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"tbody\");\n    i0.ɵɵtemplate(38, NoticeManagementComponent_ng_container_16_tr_38_Template, 14, 10, \"tr\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 22)(40, \"ngb-pagination\", 23);\n    i0.ɵɵtwoWayListener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pageIndexSales, $event) || (ctx_r3.pageIndexSales = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.pageChangedSales($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListLandLords);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndex);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSize)(\"collectionSize\", ctx_r3.totalRecords);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listSpecialNoticeFile == null ? null : ctx_r3.listSpecialNoticeFile.ListSales);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r3.pageIndexSales);\n    i0.ɵɵproperty(\"pageSize\", ctx_r3.pageSizeSales)(\"collectionSize\", ctx_r3.totalRecordsSales);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r16.label, \" \");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.fileName);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 60)(1, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(ctx_r3.saveSpecialNoticeFile.CFileUrl));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.imageUrl ? \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.saveSpecialNoticeFile.CFileUrl, \" \");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"nb-checkbox\", 64);\n    i0.ɵɵlistener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const idx_r20 = i0.ɵɵrestoreView(_r19).index;\n      const ctx_r3 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r20));\n    });\n    i0.ɵɵelementStart(2, \"span\", 65);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const idx_r20 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r20))(\"disabled\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template, 4, 2, \"th\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList2D[0]);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"nb-checkbox\", 64);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const house_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r24.CIsSelect, $event) || (house_r24.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r24 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", house_r24.CIsSelect);\n    i0.ɵɵproperty(\"disabled\", !house_r24.CHouseHold || !house_r24.CIsEnable || !(ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value === house_r24.CHouseType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, house_r24.CID));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r24.CHouseHold || \"null\", \" - \", house_r24.CFloor, \"\");\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 64);\n    i0.ɵɵlistener(\"checkedChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const row_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, row_r22));\n    });\n    i0.ɵɵelementStart(3, \"span\", 65);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template, 5, 7, \"td\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r22 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(row_r22))(\"disabled\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r22);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"table\", 62)(2, \"thead\");\n    i0.ɵɵtemplate(3, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template, 3, 1, \"tr\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template, 6, 3, \"tr\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList2D.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList2D);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r26 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r26.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r26.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getActionName(row_r26.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r26.CExamineNote);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 49)(2, \"label\", 68);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 62)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template, 10, 6, \"tr\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.saveSpecialNoticeFile.tblExamineLogs);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_nb_card_body_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 32)(1, \"div\", 33)(2, \"label\", 34);\n    i0.ɵɵtext(3, \"\\u6A94\\u6848\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.selectedCNoticeType, $event) || (ctx_r3.saveSpecialNoticeFile.selectedCNoticeType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onStatusChange($event));\n    });\n    i0.ɵɵtemplate(5, NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"div\", 37)(8, \"label\", 38);\n    i0.ɵɵtext(9, \"\\u4E0A\\u50B3\\u6A94\\u6848 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 39);\n    i0.ɵɵtext(11, \"\\u53EA\\u63A5\\u53D7pdf\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"input\", 41);\n    i0.ɵɵlistener(\"change\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_input_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"label\", 42);\n    i0.ɵɵelement(15, \"i\", 43);\n    i0.ɵɵtext(16, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template, 5, 1, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template, 3, 2, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"label\", 47);\n    i0.ɵɵtext(21, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template, 6, 2, \"div\", 48);\n    i0.ɵɵelementStart(23, \"div\", 49)(24, \"label\", 50);\n    i0.ɵɵtext(25, \"\\u9001\\u5BE9\\u5099\\u8A3B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.saveSpecialNoticeFile.CExamineNote, $event) || (ctx_r3.saveSpecialNoticeFile.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 52)(28, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ref_r25 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r25));\n    });\n    i0.ɵɵtext(29, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ref_r25 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSaveSpecialNoticeFile(ref_r25));\n    });\n    i0.ɵɵtext(31, \" \\u5132\\u5B58\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template, 17, 1, \"div\", 55);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.selectedCNoticeType);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.cNoticeTypeOptions);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.saveSpecialNoticeFile && (ctx_r3.saveSpecialNoticeFile == null ? null : ctx_r3.saveSpecialNoticeFile.CFileUrl));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isHouseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.saveSpecialNoticeFile.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isNew);\n  }\n}\nfunction NoticeManagementComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 30)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NoticeManagementComponent_ng_template_18_nb_card_body_3_Template, 33, 9, \"nb-card-body\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.isNew ? \"\\u65B0\\u589E\\u6A94\\u6848-\\u5730\\u4E3B\\u6236\" : \"\\u7DE8\\u8F2F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isHouseList);\n  }\n}\nexport class NoticeManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _utilityService, _buildCaseService, _specialNoticeFileService, _regularNoticeFileService, _houseService, _specialChangeCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._utilityService = _utilityService;\n    this._buildCaseService = _buildCaseService;\n    this._specialNoticeFileService = _specialNoticeFileService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._houseService = _houseService;\n    this._specialChangeCustomService = _specialChangeCustomService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.pageFirstSales = 1;\n    this.pageSizeSales = 10;\n    this.pageIndexSales = 1;\n    this.totalRecordsSales = 0;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: 1\n    }, {\n      label: '銷售戶',\n      value: 2\n    }];\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.isHouseList = false;\n    this.cExamineStatusOption = ['待審核', '已通過', '已駁回'];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getUserBuildCase();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  pageChangedSales(newPage) {\n    this.pageIndexSales = newPage;\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = {\n            CName: file.name,\n            CFile: e.target?.result?.toString().split(',')[1],\n            Cimg: file.name.includes('pdf') ? file : file,\n            CFileUpload: file,\n            CFileType: EnumFileType.PDF\n          };\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  onChangeBuildCase() {\n    if (this.selectedCBuildCase.value) {\n      this.getSpecialNoticeFileHouseHoldList();\n      this.getSpecialNoticeFileList();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.selectedCBuildCase = this.userBuildCaseOptions[0];\n        if (this.selectedCBuildCase.value) {\n          this.getSpecialNoticeFileHouseHoldList();\n          this.getSpecialNoticeFileList();\n        }\n      }\n    })).subscribe();\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    if (this.selectedCBuildCase.value) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.selectedCBuildCase.value,\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseListEnable = [...rest];\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []));\n          } else {\n            this.houseList2D = this.groupByFloor([...rest]);\n          }\n          this.isHouseList = true;\n        }\n      });\n    }\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length && this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      if (index < floorData.length && this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n      item.CIsSelect = checked;\n    }\n  }\n  getSpecialNoticeFileList() {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedCBuildCase.value,\n        PageIndexLandLord: this.pageIndex,\n        PageIndexSales: this.pageIndexSales,\n        PageSizeLandLord: this.pageSize,\n        PageSizeSales: this.pageSizeSales\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFile = res.Entries;\n        this.totalRecords = res.Entries.TotalListLandLords || 0;\n        this.totalRecordsSales = res.Entries.TotalListSales || 0;\n      }\n    });\n  }\n  onStatusChange(newStatus) {}\n  getSpecialNoticeFileHouseHoldList() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.selectedCBuildCase.value\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFileHouseHold = res.Entries;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  updateCIsEnable(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        if (selectedHouses.has(key)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  getSpecialNoticeFileById(item, ref) {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\n      body: item.CSpecialNoticeFileId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.saveSpecialNoticeFile = {\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\n          CExamineNote: data.CExamineNote,\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter(i => i.CIsSelect)\n        };\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\n  }\n  openModel(ref, item) {\n    this.isHouseList = false;\n    this.isNew = true;\n    this.clearImage();\n    this.saveSpecialNoticeFile = {\n      CNoticeType: 1,\n      CBuildCaseId: undefined,\n      CFile: undefined,\n      CHouse: [],\n      CSpecialNoticeFileId: undefined,\n      CIsSelectAll: false,\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\n      CExamineNote: '',\n      tblSpecialNoticeFileHouses: undefined\n    };\n    if (item) {\n      this.isNew = false;\n      this.getSpecialNoticeFileById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      this.dialogService.open(ref);\n    }\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  onDelete(item) {\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\n        body: item.CSpecialNoticeFileId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.onChangeBuildCase();\n        }\n      });\n    }\n    return item;\n  }\n  getTrueKeys(inputDict) {\n    const trueKeys = [];\n    for (const key in inputDict) {\n      if (inputDict[key]) {\n        trueKeys.push(key);\n      }\n    }\n    return trueKeys;\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveSpecialNoticeFile(ref) {\n    const param = {\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\n      CBuildCaseId: this.selectedCBuildCase.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CHouse: this.flattenAndFilter(this.houseList2D),\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\n    };\n    this.validation(param.CHouse);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getSpecialNoticeFileList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(CHouse) {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.required('[檔案]', '');\n    }\n    if (!(CHouse.length > 0)) {\n      this.valid.required('[適用戶別]', '');\n    }\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  static {\n    this.ɵfac = function NoticeManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NoticeManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.SpecialNoticeFileService), i0.ɵɵdirectiveInject(i6.RegularNoticeFileService), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i7.NoticeServiceCustom));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NoticeManagementComponent,\n      selectors: [[\"ngx-notice-management\"]],\n      viewQuery: function NoticeManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 4,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"table-responsive\", \"mt-4\"], [1, \"text-xl\", \"font-bold\"], [1, \"table\", \"table-striped\", \"border\", \"mt-3\", 2, \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-center\", \"my-3\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"text-center\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\"], [\"for\", \"remark\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"Select Status\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-baseline\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", \"baseLabel\", \"\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"75px\"], [2, \"font-size\", \"10px\", \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"items-start\", \"space-y-4\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\"], [\"for\", \"fileInput\", 1, \"cursor-pointer\", \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"class\", \"text-sm ml-4\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"class\", \"table-responsive mt-1\", 4, \"ngIf\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"d-flex\", \"justify-content-center\", \"min-w-[90px]\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\", \"ml-4\", 3, \"ngClass\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngFor\", \"ngForOf\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\"], [1, \"font-bold\", 3, \"ngClass\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"]],\n      template: function NoticeManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCBuildCase, $event) || (ctx.selectedCBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function NoticeManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, NoticeManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 9);\n          i0.ɵɵtemplate(15, NoticeManagementComponent_button_15_Template, 3, 0, \"button\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(16, NoticeManagementComponent_ng_container_16_Template, 41, 8, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"nb-card-footer\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, NoticeManagementComponent_ng_template_18_Template, 4, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listSpecialNoticeFile);\n        }\n      },\n      dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, i13.DateFormatPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsZ0xBQWdMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EnumFileType", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "NoticeManagementComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "openModel", "ɵɵelement", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template_button_click_0_listener", "_r9", "item_r8", "$implicit", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template_button_click_0_listener", "_r10", "onDelete", "NoticeManagementComponent_ng_container_16_tr_18_Template_a_click_2_listener", "_r7", "openPdfInNewTab", "CFile", "ɵɵtemplate", "NoticeManagementComponent_ng_container_16_tr_18_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_18_button_13_Template", "ɵɵtextInterpolate", "CFileName", "CHouseHold", "join", "CExamineStatus", "ɵɵpureFunction0", "_c1", "includes", "cExamineStatusOption", "ɵɵpipeBind2", "CApproveDate", "isUpdate", "isDelete", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template_button_click_0_listener", "_r13", "item_r12", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template_button_click_0_listener", "_r14", "NoticeManagementComponent_ng_container_16_tr_38_Template_a_click_2_listener", "_r11", "NoticeManagementComponent_ng_container_16_tr_38_button_12_Template", "NoticeManagementComponent_ng_container_16_tr_38_button_13_Template", "ɵɵelementContainerStart", "NoticeManagementComponent_ng_container_16_tr_18_Template", "ɵɵtwoWayListener", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_20_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "pageIndex", "pageChanged", "NoticeManagementComponent_ng_container_16_tr_38_Template", "NoticeManagementComponent_ng_container_16_Template_ngb_pagination_pageChange_40_listener", "pageIndexSales", "pageChangedSales", "listSpecialNoticeFile", "ListLandLords", "ɵɵtwoWayProperty", "pageSize", "totalRecords", "ListSales", "pageSizeSales", "totalRecordsSales", "status_r16", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template_button_click_3_listener", "_r17", "clearImage", "fileName", "NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template_a_click_1_listener", "_r18", "saveSpecialNoticeFile", "CFileUrl", "imageUrl", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener", "idx_r20", "_r19", "index", "enableAllAtIndex", "isCheckAllColumnChecked", "selectedCNoticeType", "value", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_th_2_Template", "houseList2D", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener", "house_r24", "_r23", "CIsSelect", "CIsEnable", "CHouseType", "ɵɵpureFunction1", "_c2", "CID", "ɵɵtextInterpolate2", "CFloor", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template_nb_checkbox_checkedChange_2_listener", "row_r22", "_r21", "enableAllRow", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_td_5_Template", "isCheckAllRowChecked", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_3_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_tr_5_Template", "length", "ɵɵpipeBind1", "row_r26", "CCreateDt", "CCreator", "getActionName", "CAction", "CExamineNote", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_tr_16_Template", "tblExamineLogs", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_nb_select_ngModelChange_4_listener", "_r15", "onStatusChange", "NoticeManagementComponent_ng_template_18_nb_card_body_3_nb_option_5_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_input_change_13_listener", "onFileSelected", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_17_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_span_18_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_22_Template", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_textarea_ngModelChange_26_listener", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_28_listener", "ref_r25", "dialogRef", "onClose", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template_button_click_30_listener", "onSaveSpecialNoticeFile", "NoticeManagementComponent_ng_template_18_nb_card_body_3_div_32_Template", "isNew", "cNoticeTypeOptions", "isHouseList", "NoticeManagementComponent_ng_template_18_nb_card_body_3_Template", "NoticeManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_utilityService", "_buildCaseService", "_specialNoticeFileService", "_regularNoticeFileService", "_houseService", "_specialChangeCustomService", "pageFirst", "pageFirstSales", "buildCaseOptions", "typeContentManagementLandowner", "CFormType", "CNoticeType", "undefined", "ngOnInit", "getUserBuildCase", "newPage", "event", "file", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "name", "reader", "FileReader", "onload", "e", "CName", "result", "toString", "split", "Cimg", "CFileUpload", "CFileType", "PDF", "fileInput", "nativeElement", "readAsDataURL", "onChangeBuildCase", "selectedCBuildCase", "getSpecialNoticeFileHouseHoldList", "getSpecialNoticeFileList", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "res", "Entries", "StatusCode", "userBuildCaseOptions", "map", "CBuildCaseName", "cID", "subscribe", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "filter", "floor", "push", "floorIndex", "indexOf", "CHouseID", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "rest", "houseListEnable", "CSpecialNoticeFileId", "addCIsSelectToA", "tblSpecialNoticeFileHouses", "row", "every", "item", "Error", "floorData", "checked", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "CBuildCaseId", "PageIndexLandLord", "PageIndexSales", "PageSizeLandLord", "PageSizeSales", "TotalListLandLords", "TotalListSales", "newStatus", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "listSpecialNoticeFileHouseHold", "createArrayObjectFromArray", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelected", "getItemByValue", "options", "updateCIsEnable", "houseSpecialNoticeFile", "selectedHouses", "floorArray", "key", "has", "A", "B", "mapB", "Map", "get", "getSpecialNoticeFileById", "ref", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "data", "tblSpecialNoticeFile", "i", "open", "window", "BASE_WITHOUT_FILEROOT", "CHouse", "CIsSelectAll", "removeBase64Prefix", "base64String", "prefixIndex", "substring", "confirm", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "showSucessMSG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputDict", "true<PERSON>eys", "flattenAndFilter", "flattened", "house", "param", "validation", "errorMessages", "showErrorMSGs", "SaveSpecialNoticeFile", "close", "Message", "clear", "required", "actionID", "textR", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "UtilityService", "i6", "BuildCaseService", "SpecialNoticeFileService", "RegularNoticeFileService", "HouseService", "i7", "NoticeServiceCustom", "selectors", "viewQuery", "NoticeManagementComponent_Query", "rf", "ctx", "NoticeManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "NoticeManagementComponent_Template_nb_select_selected<PERSON>hange_11_listener", "NoticeManagementComponent_nb_option_12_Template", "NoticeManagementComponent_button_15_Template", "NoticeManagementComponent_ng_container_16_Template", "NoticeManagementComponent_ng_template_18_Template", "ɵɵtemplateRefExtractor", "isCreate", "i8", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i10", "NgbPagination", "i11", "BreadcrumbComponent", "i12", "BaseLabelDirective", "i13", "DateFormatPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListRes, GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, TblExamineLog } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { NoticeServiceCustom } from 'src/app/@core/service/notice.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\nexport interface SaveSpecialNoticeFileCus {\r\n  CFileUrl?: string\r\n  CNoticeType?: number\r\n  CBuildCaseId?: number\r\n  CFile?: Blob\r\n  CHouse?: Array<string>\r\n  CSpecialNoticeFileId?: number\r\n  CIsSelectAll?: boolean\r\n  selectedCNoticeType?: any\r\n  CExamineNote?: string | null;\r\n  tblExamineLogs?: TblExamineLog[],\r\n  tblSpecialNoticeFileHouses: any\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notice-management',\r\n  templateUrl: './notice-management.component.html',\r\n  styleUrls: ['./notice-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, DatePipe, LabelInOptionsPipe],\r\n})\r\n\r\nexport class NoticeManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _utilityService: UtilityService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialNoticeFileService: SpecialNoticeFileService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _houseService: HouseService,\r\n    private _specialChangeCustomService: NoticeServiceCustom\r\n  ) { super(_allow) }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  selectedCBuildCase: any\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  pageFirstSales = 1;\r\n  pageSizeSales = 10;\r\n  pageIndexSales = 1;\r\n  totalRecordsSales = 0;\r\n\r\n\r\n  saveSpecialNoticeFile: SaveSpecialNoticeFileCus\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  cNoticeTypeOptions: any[] = [\r\n    { label: '地主戶', value: 1 },\r\n    { label: '銷售戶', value: 2 }\r\n  ]\r\n  seletectedNoticeType: any\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n  userBuildCaseOptions: any\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n  pageChangedSales(newPage: number) {\r\n    this.pageIndexSales = newPage;\r\n  }\r\n\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = {\r\n            CName: file.name,\r\n            CFile: e.target?.result?.toString().split(',')[1],\r\n            Cimg: file.name.includes('pdf') ? file : file,\r\n            CFileUpload: file,\r\n            CFileType: EnumFileType.PDF,\r\n          };\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n  userBuildCaseSelected: any\r\n\r\n  onChangeBuildCase() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this.getSpecialNoticeFileHouseHoldList()\r\n      this.getSpecialNoticeFileList()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.selectedCBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.selectedCBuildCase.value) {\r\n            this.getSpecialNoticeFileHouseHoldList()\r\n            this.getSpecialNoticeFileList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  groupByFloor(customerData: HouseList[]): HouseList[][] {\r\n\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n  houseListEnable: GetHouseListRes[]\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.selectedCBuildCase.value, CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length && (this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType)) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      if (index < floorData.length && (this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType)) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n      item.CIsSelect = checked;\r\n    }\r\n  }\r\n\r\n\r\n  listSpecialNoticeFile: GetSpecialNoticeFileListRes\r\n\r\n  getSpecialNoticeFileList() {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedCBuildCase.value,\r\n        PageIndexLandLord: this.pageIndex,\r\n        PageIndexSales: this.pageIndexSales,\r\n        PageSizeLandLord: this.pageSize,\r\n        PageSizeSales: this.pageSizeSales,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFile = res.Entries\r\n        this.totalRecords = res.Entries.TotalListLandLords || 0\r\n        this.totalRecordsSales = res.Entries.TotalListSales || 0\r\n      }\r\n    })\r\n  }\r\n  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]\r\n\r\n  houseHoldList: string[]\r\n\r\n  onStatusChange(newStatus: any) {\r\n  }\r\n\r\n  getSpecialNoticeFileHouseHoldList() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.selectedCBuildCase.value\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFileHouseHold = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  cExamineStatusOption = ['待審核', '已通過', '已駁回']\r\n\r\n  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        const key = `${item.CHouseHold}-${item.CFloor}`;\r\n        if (selectedHouses.has(key)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n\r\n  getSpecialNoticeFileById(item: any, ref: any) {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\r\n      body: item.CSpecialNoticeFileId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.saveSpecialNoticeFile = {\r\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\r\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\r\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\r\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\r\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\r\n          CExamineNote: data.CExamineNote,\r\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect)\r\n        }\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.clearImage()\r\n    this.saveSpecialNoticeFile = {\r\n      CNoticeType: 1,\r\n      CBuildCaseId: undefined,\r\n      CFile: undefined,\r\n      CHouse: [],\r\n      CSpecialNoticeFileId: undefined,\r\n      CIsSelectAll: false,\r\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\r\n      CExamineNote: '',\r\n      tblSpecialNoticeFileHouses: undefined\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getSpecialNoticeFileById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  onDelete(item: any) {\r\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\r\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\r\n        body: item.CSpecialNoticeFileId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.onChangeBuildCase()\r\n        }\r\n      })\r\n    }\r\n    return item\r\n  }\r\n\r\n  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {\r\n    const trueKeys: string[] = [];\r\n    for (const key in inputDict) {\r\n      if (inputDict[key]) {\r\n        trueKeys.push(key);\r\n      }\r\n    }\r\n    return trueKeys;\r\n  }\r\n\r\n  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {\r\n    const flattened: HouseSpecialNoticeFile[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  onSaveSpecialNoticeFile(ref: any) {\r\n    const param = {\r\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\r\n      CBuildCaseId: this.selectedCBuildCase.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CHouse: this.flattenAndFilter(this.houseList2D),\r\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\r\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\r\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\r\n    }\r\n\r\n    this.validation(param.CHouse)\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getSpecialNoticeFileList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation(CHouse: any[]) {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.required('[檔案]', '')\r\n    }\r\n    if (!(CHouse.length > 0)) {\r\n      this.valid.required('[適用戶別]', '')\r\n    }\r\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n}\r\n\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedCBuildCase\" class=\"col-9\"\r\n            (selectedChange)=\"onChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"openModel(dialog)\" *ngIf=\"isCreate\">\r\n            新增檔案 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"listSpecialNoticeFile\">\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">地主戶 </h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListLandLords ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>{{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}</td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy/MM/dd HH:mm:ss' }}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" *ngIf=\"isUpdate\"\r\n                  (click)=\"openModel(dialog, item)\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n          (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n      <div class=\"table-responsive mt-4\">\r\n        <h4 class=\"text-xl font-bold\">銷售戶</h4>\r\n        <table class=\"table table-striped border mt-3\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n              <th scope=\"col\" class=\"col-1 \">檔案名稱</th>\r\n              <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n              <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n              <th scope=\"col\" class=\"col-1\">審核日期</th>\r\n              <th scope=\"col\" class=\"col-1\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of listSpecialNoticeFile?.ListSales ; let i = index\" class=\"text-center\">\r\n              <td>\r\n                <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CFileName}}</a>\r\n              </td>\r\n              <td>{{ item.CHouseHold?.join('、')}}</td>\r\n              <td>\r\n                {{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?\r\n                cExamineStatusOption[item.CExamineStatus] : '' }}\r\n              </td>\r\n              <td>{{ item?.CApproveDate | date:'yyyy-MM-dd HH:mm:ss'}}</td>\r\n              <td>\r\n                <button class=\"btn btn-outline-success btn-sm text-left m-[2px]\" (click)=\"openModel(dialog, item)\" *ngIf=\"isUpdate\">\r\n                  編輯\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm m-[2px]\" *ngIf=\"isDelete\" (click)=\"onDelete(item)\">\r\n                  刪除\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"flex justify-center my-3\">\r\n        <ngb-pagination [(page)]=\"pageIndexSales\" [pageSize]=\"pageSizeSales\" [collectionSize]=\"totalRecordsSales\"\r\n          (pageChange)=\"pageChangedSales($event)\" aria-label=\"Pagination\">\r\n        </ngb-pagination>\r\n      </div>\r\n    </ng-container>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header> {{isNew ? '新增檔案-地主戶': '編輯' }}\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\" *ngIf=\"isHouseList\">\r\n      <div class=\"form-group d-flex\">\r\n        <label for=\"remark\" style=\"min-width:75px\" class=\"required-field mr-4\" baseLabel>檔案類型</label>\r\n        <nb-select placeholder=\"Select Status\" [(ngModel)]=\"saveSpecialNoticeFile.selectedCNoticeType\" class=\"w-full\"\r\n          [disabled]=\"!isNew\" (ngModelChange)=\"onStatusChange($event)\">\r\n          <nb-option *ngFor=\"let status of cNoticeTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-baseline\">\r\n        <div class=\"d-flex flex-col mr-3\">\r\n          <label for=\"file\" class=\"required-field  mb-0\" style=\"min-width:75px\" baseLabel>上傳檔案\r\n          </label>\r\n          <span style=\"font-size: 10px; color:red;\">只接受pdf</span>\r\n        </div>\r\n        <div class=\"flex flex-col items-start space-y-4\">\r\n          <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n            style=\"display: none\" (change)=\"onFileSelected($event)\">\r\n          <label for=\"fileInput\"\r\n            class=\"cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n            <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n          </label>\r\n          <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n            <span class=\"text-gray-600\">{{ fileName }}</span>\r\n            <button type=\"button\" (click)=\"clearImage()\" class=\"text-red-500 hover:text-red-700\">\r\n              <i class=\"fa-solid fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <span class=\"text-sm ml-4\" *ngIf=\"saveSpecialNoticeFile && saveSpecialNoticeFile?.CFileUrl\"\r\n          [ngClass]=\"imageUrl ? 'hidden':''\">\r\n          <a (click)=\"openPdfInNewTab(saveSpecialNoticeFile.CFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n            {{saveSpecialNoticeFile.CFileUrl}}\r\n          </a>\r\n        </span>\r\n      </div>\r\n      <div class=\"form-group d-flex mb-0\">\r\n        <label for=\"houseList2D\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">適用戶別</label>\r\n      </div>\r\n      <div class=\"table-responsive mt-1\" *ngIf=\"isHouseList\">\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr *ngIf=\"houseList2D.length\">\r\n              <th></th>\r\n              <th *ngFor=\"let house of houseList2D[0]; let idx = index;\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\" [disabled]=\"saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of houseList2D\">\r\n              <td>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\" [disabled]=\"saveSpecialNoticeFile.selectedCNoticeType.value== 1 ? true : false\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </td>\r\n              <td *ngFor=\"let house of row\">\r\n                <!-- *ngIf=\"saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType\" -->\r\n                <ng-container>\r\n                  <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelect\"\r\n                    [disabled]=\"!house.CHouseHold || !house.CIsEnable || !(saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType)\">\r\n                    <span class=\"font-bold\" [ngClass]=\"{ '!text-red': house.CID }\"> {{ house.CHouseHold || 'null' }} -\r\n                      {{ house.CFloor }}</span>\r\n                  </nb-checkbox>\r\n                </ng-container>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"status\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">送審備註</label>\r\n        <textarea nbInput [(ngModel)]=\"saveSpecialNoticeFile.CExamineNote\" [rows]=\"4\"\r\n          class=\"resize-none !max-w-full w-full\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center min-w-[90px]\">\r\n        <button class=\"btn btn-danger btn-sm mr-4\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm min-w-[90px]\" (click)=\"onSaveSpecialNoticeFile(ref)\">\r\n          儲存</button>\r\n      </div>\r\n      <div class=\"w-full\" *ngIf=\"!isNew\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"status\" baseLabel class=\"mr-3\" style=\"min-width:75px\">審核歷程</label>\r\n        </div>\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr>\r\n              <th>時間</th>\r\n              <th>使用者</th>\r\n              <th>動作</th>\r\n              <th>說明</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of saveSpecialNoticeFile.tblExamineLogs\">\r\n              <td>{{row.CCreateDt | dateFormat}}</td>\r\n              <td>{{row.CCreator}}</td>\r\n              <td>{{getActionName(row.CAction)}}</td>\r\n              <td>{{row.CExamineNote}}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AAQxD,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,kCAAkC;AAG/D,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;;;ICH9CC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;;IAMFR,EAAA,CAAAC,cAAA,iBAAsF;IAA7CD,EAAA,CAAAS,UAAA,mBAAAC,qEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,CAAiB;IAAA,EAAC;IAClEf,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAmB,SAAA,YAA2B;IAClCnB,EAAA,CAAAG,YAAA,EAAS;;;;;;IA4BHH,EAAA,CAAAC,cAAA,iBACoC;IAAlCD,EAAA,CAAAS,UAAA,mBAAAW,2FAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAO,OAAA,CAAuB;IAAA,EAAC;IACjCtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAe,2FAAA;MAAAxB,EAAA,CAAAW,aAAA,CAAAc,IAAA;MAAA,MAAAH,OAAA,GAAAtB,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAJ,OAAA,CAAc;IAAA,EAAC;IAC7FtB,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAFJ,CAAAC,cAAA,aAAkG,SAC5F,YAC6E;IAAvCD,EAAA,CAAAS,UAAA,mBAAAkB,4EAAA;MAAA,MAAAL,OAAA,GAAAtB,EAAA,CAAAW,aAAA,CAAAiB,GAAA,EAAAL,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAP,OAAA,kBAAAA,OAAA,CAAAQ,KAAA,CAA4B;IAAA,EAAC;IAAC9B,EAAA,CAAAE,MAAA,GAAoB;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACpG;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAC+C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,UAAI;IAKFD,EAJA,CAAA+B,UAAA,KAAAC,kEAAA,qBACoC,KAAAC,kEAAA,qBAG4D;IAIpGjC,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAf8EH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,kBAAAA,OAAA,CAAAa,SAAA,CAAoB;IAEjGnC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,CAAAc,UAAA,kBAAAd,OAAA,CAAAc,UAAA,CAAAC,IAAA,WAA+B;IAC/BrC,EAAA,CAAAM,SAAA,GAC+C;IAD/CN,EAAA,CAAAkC,iBAAA,CAAAZ,OAAA,CAAAgB,cAAA,YAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAnB,OAAA,CAAAgB,cAAA,IAAAzB,MAAA,CAAA6B,oBAAA,CAAApB,OAAA,CAAAgB,cAAA,OAC+C;IAC/CtC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAA2C,WAAA,QAAArB,OAAA,kBAAAA,OAAA,CAAAsB,YAAA,yBAAqD;IAEW5C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;IAIzB7C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAiC,QAAA,CAAc;;;;;;IAqCrE9C,EAAA,CAAAC,cAAA,iBAAoH;IAAnDD,EAAA,CAAAS,UAAA,mBAAAsC,2FAAA;MAAA/C,EAAA,CAAAW,aAAA,CAAAqC,IAAA;MAAA,MAAAC,QAAA,GAAAjD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,MAAAC,SAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,SAAA,EAAAkC,QAAA,CAAuB;IAAA,EAAC;IAChGjD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAAzBD,EAAA,CAAAS,UAAA,mBAAAyC,2FAAA;MAAAlD,EAAA,CAAAW,aAAA,CAAAwC,IAAA;MAAA,MAAAF,QAAA,GAAAjD,EAAA,CAAAc,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAa,QAAA,CAAAuB,QAAA,CAAc;IAAA,EAAC;IAC7FjD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAdTH,EAFJ,CAAAC,cAAA,aAA8F,SACxF,YAC6E;IAAvCD,EAAA,CAAAS,UAAA,mBAAA2C,4EAAA;MAAA,MAAAH,QAAA,GAAAjD,EAAA,CAAAW,aAAA,CAAA0C,IAAA,EAAA9B,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAoB,QAAA,kBAAAA,QAAA,CAAAnB,KAAA,CAA4B;IAAA,EAAC;IAAC9B,EAAA,CAAAE,MAAA,GAAoB;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACpG;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAA+B,UAAA,KAAAuB,kEAAA,qBAAoH,KAAAC,kEAAA,qBAGpB;IAIpGvD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAhB8EH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAe,QAAA,kBAAAA,QAAA,CAAAd,SAAA,CAAoB;IAEjGnC,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAkC,iBAAA,CAAAe,QAAA,CAAAb,UAAA,kBAAAa,QAAA,CAAAb,UAAA,CAAAC,IAAA,WAA+B;IAEjCrC,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAO,kBAAA,MAAA0C,QAAA,CAAAX,cAAA,YAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAQ,QAAA,CAAAX,cAAA,IAAAzB,MAAA,CAAA6B,oBAAA,CAAAO,QAAA,CAAAX,cAAA,YAEF;IACItC,EAAA,CAAAM,SAAA,GAAoD;IAApDN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAA2C,WAAA,QAAAM,QAAA,kBAAAA,QAAA,CAAAL,YAAA,yBAAoD;IAE8C5C,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAgC,QAAA,CAAc;IAG3D7C,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAiC,QAAA,CAAc;;;;;;IAnEjF9C,EAAA,CAAAwD,uBAAA,GAA4C;IAExCxD,EADF,CAAAC,cAAA,cAAmC,aACH;IAAAD,EAAA,CAAAE,MAAA,0BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIjCH,EAHN,CAAAC,cAAA,gBAAiF,YACxE,aACoE,aACxC;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAA0B,wDAAA,mBAAkG;IAoBxGzD,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAEyB;IAD7CD,EAAA,CAAA0D,gBAAA,wBAAAC,yFAAAC,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAkD,SAAA,EAAAH,MAAA,MAAA/C,MAAA,CAAAkD,SAAA,GAAAH,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAoB;IAClC5D,EAAA,CAAAS,UAAA,wBAAAkD,yFAAAC,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAmD,WAAA,CAAAJ,MAAA,CAAmB;IAAA,EAAC;IAEtC5D,EADE,CAAAG,YAAA,EAAiB,EACb;IAEJH,EADF,CAAAC,cAAA,eAAmC,cACH;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAIhCH,EAHN,CAAAC,cAAA,iBAAiF,aACxE,cACoE,cACxC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAAkC,wDAAA,mBAA8F;IAqBpGjE,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAEJH,EADF,CAAAC,cAAA,eAAsC,0BAE8B;IADlDD,EAAA,CAAA0D,gBAAA,wBAAAQ,yFAAAN,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAsD,cAAA,EAAAP,MAAA,MAAA/C,MAAA,CAAAsD,cAAA,GAAAP,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IACvC5D,EAAA,CAAAS,UAAA,wBAAAyD,yFAAAN,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAcJ,MAAA,CAAAuD,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAE3C5D,EADE,CAAAG,YAAA,EAAiB,EACb;;;;;IAjEqBH,EAAA,CAAAM,SAAA,IAA0C;IAA1CN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAwD,qBAAA,kBAAAxD,MAAA,CAAAwD,qBAAA,CAAAC,aAAA,CAA0C;IAsBnDtE,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAuE,gBAAA,SAAA1D,MAAA,CAAAkD,SAAA,CAAoB;IAAuB/D,EAAtB,CAAAI,UAAA,aAAAS,MAAA,CAAA2D,QAAA,CAAqB,mBAAA3D,MAAA,CAAA4D,YAAA,CAAgC;IAiBjEzE,EAAA,CAAAM,SAAA,IAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAwD,qBAAA,kBAAAxD,MAAA,CAAAwD,qBAAA,CAAAK,SAAA,CAAsC;IAuB/C1E,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAuE,gBAAA,SAAA1D,MAAA,CAAAsD,cAAA,CAAyB;IAA4BnE,EAA3B,CAAAI,UAAA,aAAAS,MAAA,CAAA8D,aAAA,CAA0B,mBAAA9D,MAAA,CAAA+D,iBAAA,CAAqC;;;;;IAsBvG5E,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAyE,UAAA,CAAgB;IACnE7E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsE,UAAA,CAAArE,KAAA,MACF;;;;;;IAiBER,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAqF;IAA/DD,EAAA,CAAAS,UAAA,mBAAAqE,gGAAA;MAAA9E,EAAA,CAAAW,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAmE,UAAA,EAAY;IAAA,EAAC;IAC1ChF,EAAA,CAAAmB,SAAA,YAAiC;IAErCnB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJwBH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAkC,iBAAA,CAAArB,MAAA,CAAAoE,QAAA,CAAc;;;;;;IAQ5CjF,EAFF,CAAAC,cAAA,eACqC,YAC+D;IAA/FD,EAAA,CAAAS,UAAA,mBAAAyE,4FAAA;MAAAlF,EAAA,CAAAW,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgB,eAAA,CAAAhB,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,CAA+C;IAAA,EAAC;IAC1DrF,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACC;;;;IAJLH,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAyE,QAAA,iBAAkC;IAEhCtF,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,MACF;;;;;;IAYMrF,EADF,CAAAC,cAAA,SAA2D,sBAEmE;IADhGD,EAAA,CAAAS,UAAA,2BAAA8E,uHAAA3B,MAAA;MAAA,MAAA4B,OAAA,GAAAxF,EAAA,CAAAW,aAAA,CAAA8E,IAAA,EAAAC,KAAA;MAAA,MAAA7E,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAA8E,gBAAA,CAAA/B,MAAA,EAAA4B,OAAA,CAA6B;IAAA,EAAC;IAEzExF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;;;;;IAHDH,EAAA,CAAAM,SAAA,EAAwC;IAACN,EAAzC,CAAAI,UAAA,YAAAS,MAAA,CAAA+E,uBAAA,CAAAJ,OAAA,EAAwC,aAAA3E,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,qBAAiF;;;;;IAJ/H9F,EAAA,CAAAC,cAAA,SAA+B;IAC7BD,EAAA,CAAAmB,SAAA,SAAS;IACTnB,EAAA,CAAA+B,UAAA,IAAAgE,iFAAA,iBAA2D;IAM7D/F,EAAA,CAAAG,YAAA,EAAK;;;;IANmBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAmF,WAAA,IAAmB;;;;;;IAgBzChG,EAAA,CAAAC,cAAA,SAA8B;IAE5BD,EAAA,CAAAwD,uBAAA,GAAc;IACZxD,EAAA,CAAAC,cAAA,sBACgI;IADpGD,EAAA,CAAA0D,gBAAA,2BAAAuC,uHAAArC,MAAA;MAAA,MAAAsC,SAAA,GAAAlG,EAAA,CAAAW,aAAA,CAAAwF,IAAA,EAAA5E,SAAA;MAAAvB,EAAA,CAAA8D,kBAAA,CAAAoC,SAAA,CAAAE,SAAA,EAAAxC,MAAA,MAAAsC,SAAA,CAAAE,SAAA,GAAAxC,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAA6B;IAEvD5D,EAAA,CAAAC,cAAA,eAA+D;IAACD,EAAA,CAAAE,MAAA,GAC5C;IACtBF,EADsB,CAAAG,YAAA,EAAO,EACf;;IAElBH,EAAA,CAAAG,YAAA,EAAK;;;;;IAN2BH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuE,gBAAA,YAAA2B,SAAA,CAAAE,SAAA,CAA6B;IACvDpG,EAAA,CAAAI,UAAA,cAAA8F,SAAA,CAAA9D,UAAA,KAAA8D,SAAA,CAAAG,SAAA,MAAAxF,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,KAAAI,SAAA,CAAAI,UAAA,EAA6H;IACrGtG,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuG,eAAA,IAAAC,GAAA,EAAAN,SAAA,CAAAO,GAAA,EAAsC;IAAEzG,EAAA,CAAAM,SAAA,EAC5C;IAD4CN,EAAA,CAAA0G,kBAAA,MAAAR,SAAA,CAAA9D,UAAA,mBAAA8D,SAAA,CAAAS,MAAA,KAC5C;;;;;;IAXxB3G,EAFJ,CAAAC,cAAA,SAAoC,SAC9B,sBAE2H;IAA3HD,EAAA,CAAAS,UAAA,2BAAAmG,kHAAAhD,MAAA;MAAA,MAAAiD,OAAA,GAAA7G,EAAA,CAAAW,aAAA,CAAAmG,IAAA,EAAAvF,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAAkG,YAAA,CAAAnD,MAAA,EAAAiD,OAAA,CAAwB;IAAA,EAAC;IAC1C7G,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;IACLH,EAAA,CAAA+B,UAAA,IAAAiF,iFAAA,iBAA8B;IAUhChH,EAAA,CAAAG,YAAA,EAAK;;;;;IAf2BH,EAAA,CAAAM,SAAA,GAAqC;IACpBN,EADjB,CAAAI,UAAA,YAAAS,MAAA,CAAAoG,oBAAA,CAAAJ,OAAA,EAAqC,aAAAhG,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAAC,KAAA,qBAC2D;IAIxG9F,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAAyG,OAAA,CAAM;;;;;IAnBhC7G,EAFJ,CAAAC,cAAA,cAAuD,gBACiB,YAC7D;IACLD,EAAA,CAAA+B,UAAA,IAAAmF,4EAAA,iBAA+B;IASjClH,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAA+B,UAAA,IAAAoF,4EAAA,iBAAoC;IAoB1CnH,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IA/BKH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAmF,WAAA,CAAAoB,MAAA,CAAwB;IAWTpH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAmF,WAAA,CAAc;;;;;IAiDhChG,EADF,CAAAC,cAAA,SAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;;IAJCH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAkC,iBAAA,CAAAlC,EAAA,CAAAqH,WAAA,OAAAC,OAAA,CAAAC,SAAA,EAA8B;IAC9BvH,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAkC,iBAAA,CAAAoF,OAAA,CAAAE,QAAA,CAAgB;IAChBxH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAkC,iBAAA,CAAArB,MAAA,CAAA4G,aAAA,CAAAH,OAAA,CAAAI,OAAA,EAA8B;IAC9B1H,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAkC,iBAAA,CAAAoF,OAAA,CAAAK,YAAA,CAAoB;;;;;IAhB5B3H,EAFJ,CAAAC,cAAA,cAAmC,cACiB,gBACkB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;IAIAH,EAHN,CAAAC,cAAA,gBAAsE,YAC7D,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA+B,UAAA,KAAA6F,6EAAA,kBAA6D;IAQnE5H,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IARoBH,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAuE,qBAAA,CAAAyC,cAAA,CAAuC;;;;;;IApG/D7H,EAFJ,CAAAC,cAAA,uBAA+C,cACd,gBACoD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBAC+D;IADxBD,EAAA,CAAA0D,gBAAA,2BAAAoE,oGAAAlE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,EAAAjC,MAAA,MAAA/C,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,GAAAjC,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAuD;IACxE5D,EAAA,CAAAS,UAAA,2BAAAqH,oGAAAlE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBJ,MAAA,CAAAmH,cAAA,CAAApE,MAAA,CAAsB;IAAA,EAAC;IAC5D5D,EAAA,CAAA+B,UAAA,IAAAkG,4EAAA,uBAAsE;IAI1EjI,EADE,CAAAG,YAAA,EAAY,EACR;IAGFH,EAFJ,CAAAC,cAAA,cAAoD,cAChB,gBACgD;IAAAD,EAAA,CAAAE,MAAA,gCAChF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAA0C;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAClDF,EADkD,CAAAG,YAAA,EAAO,EACnD;IAEJH,EADF,CAAAC,cAAA,eAAiD,iBAEW;IAAlCD,EAAA,CAAAS,UAAA,oBAAAyH,0FAAAtE,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUJ,MAAA,CAAAsH,cAAA,CAAAvE,MAAA,CAAsB;IAAA,EAAC;IADzD5D,EAAA,CAAAG,YAAA,EAC0D;IAC1DH,EAAA,CAAAC,cAAA,iBAC8F;IAC5FD,EAAA,CAAAmB,SAAA,aAA+C;IAACnB,EAAA,CAAAE,MAAA,sBAClD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAA+B,UAAA,KAAAqG,uEAAA,kBAA0D;IAM5DpI,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA+B,UAAA,KAAAsG,wEAAA,mBACqC;IAKvCrI,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACoD;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAC5FF,EAD4F,CAAAG,YAAA,EAAQ,EAC9F;IACNH,EAAA,CAAA+B,UAAA,KAAAuG,uEAAA,kBAAuD;IAoCrDtI,EADF,CAAAC,cAAA,eAAkD,iBACiC;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7FH,EAAA,CAAAC,cAAA,oBACyC;IADvBD,EAAA,CAAA0D,gBAAA,2BAAA6E,oGAAA3E,MAAA;MAAA5D,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8D,kBAAA,CAAAjD,MAAA,CAAAuE,qBAAA,CAAAuC,YAAA,EAAA/D,MAAA,MAAA/C,MAAA,CAAAuE,qBAAA,CAAAuC,YAAA,GAAA/D,MAAA;MAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;IAAA,EAAgD;IAEpE5D,EAD2C,CAAAG,YAAA,EAAW,EAChD;IAGJH,EADF,CAAAC,cAAA,eAAwD,kBACY;IAAvBD,EAAA,CAAAS,UAAA,mBAAA+H,0FAAA;MAAAxI,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAU,OAAA,GAAAzI,EAAA,CAAAc,aAAA,GAAA4H,SAAA;MAAA,MAAA7H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAA8H,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAC/DzI,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAvCD,EAAA,CAAAS,UAAA,mBAAAmI,0FAAA;MAAA5I,EAAA,CAAAW,aAAA,CAAAoH,IAAA;MAAA,MAAAU,OAAA,GAAAzI,EAAA,CAAAc,aAAA,GAAA4H,SAAA;MAAA,MAAA7H,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASJ,MAAA,CAAAgI,uBAAA,CAAAJ,OAAA,CAA4B;IAAA,EAAC;IACxFzI,EAAA,CAAAE,MAAA,qBAAE;IACNF,EADM,CAAAG,YAAA,EAAS,EACT;IACNH,EAAA,CAAA+B,UAAA,KAAA+G,uEAAA,mBAAmC;IAuBrC9I,EAAA,CAAAG,YAAA,EAAe;;;;IA5G4BH,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAuE,gBAAA,YAAA1D,MAAA,CAAAuE,qBAAA,CAAAS,mBAAA,CAAuD;IAC5F7F,EAAA,CAAAI,UAAA,cAAAS,MAAA,CAAAkI,KAAA,CAAmB;IACW/I,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,YAAAS,MAAA,CAAAmI,kBAAA,CAAqB;IAkBThJ,EAAA,CAAAM,SAAA,IAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAoE,QAAA,CAAc;IAO9BjF,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAuE,qBAAA,KAAAvE,MAAA,CAAAuE,qBAAA,kBAAAvE,MAAA,CAAAuE,qBAAA,CAAAC,QAAA,EAA8D;IAUxDrF,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAoI,WAAA,CAAiB;IAqCjCjJ,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAuE,gBAAA,YAAA1D,MAAA,CAAAuE,qBAAA,CAAAuC,YAAA,CAAgD;IAAC3H,EAAA,CAAAI,UAAA,WAAU;IAW1DJ,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,UAAAS,MAAA,CAAAkI,KAAA,CAAY;;;;;IA1FnC/I,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IAACD,EAAA,CAAAE,MAAA,GACjB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAA+B,UAAA,IAAAmH,gEAAA,4BAA+C;IAgHjDlJ,EAAA,CAAAG,YAAA,EAAU;;;;IAlHSH,EAAA,CAAAM,SAAA,GACjB;IADiBN,EAAA,CAAAO,kBAAA,MAAAM,MAAA,CAAAkI,KAAA,uEACjB;IAC4B/I,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAS,MAAA,CAAAoI,WAAA,CAAiB;;;ADxEjD,OAAM,MAAOE,yBAA0B,SAAQtJ,aAAa;EAC1DuJ,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,yBAAmD,EACnDC,yBAAmD,EACnDC,aAA2B,EAC3BC,2BAAgD;IACtD,KAAK,CAACT,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAS5B,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAvF,QAAQ,GAAG,EAAE;IACb,KAAAT,SAAS,GAAG,CAAC;IACb,KAAAU,YAAY,GAAG,CAAC;IACzB,KAAAuF,cAAc,GAAG,CAAC;IAClB,KAAArF,aAAa,GAAG,EAAE;IAClB,KAAAR,cAAc,GAAG,CAAC;IAClB,KAAAS,iBAAiB,GAAG,CAAC;IAKrB,KAAAqF,gBAAgB,GAAU,CAAC;MAAEzJ,KAAK,EAAE,IAAI;MAAEsF,KAAK,EAAE;IAAE,CAAE,CAAC;IAEtD,KAAAkD,kBAAkB,GAAU,CAC1B;MAAExI,KAAK,EAAE,KAAK;MAAEsF,KAAK,EAAE;IAAC,CAAE,EAC1B;MAAEtF,KAAK,EAAE,KAAK;MAAEsF,KAAK,EAAE;IAAC,CAAE,CAC3B;IAGD,KAAAoE,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAcD,KAAAnF,QAAQ,GAAkB,IAAI;IAC9B,KAAAK,QAAQ,GAAQ+E,SAAS;IAgGzB,KAAApB,WAAW,GAAG,KAAK;IA0HnB,KAAAvG,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAmD5C,KAAAqG,KAAK,GAAG,IAAI;EA3TM;EAETuB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAgCAvG,WAAWA,CAACwG,OAAe;IACzB,IAAI,CAACzG,SAAS,GAAGyG,OAAO;EAC1B;EAEApG,gBAAgBA,CAACoG,OAAe;IAC9B,IAAI,CAACrG,cAAc,GAAGqG,OAAO;EAC/B;EAOArC,cAAcA,CAACsC,KAAU;IACvB,MAAMC,IAAI,GAASD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACxB,OAAO,CAACyB,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IACA,IAAIN,IAAI,EAAE;MACR,MAAMO,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACxI,QAAQ,CAACiI,IAAI,CAACK,IAAI,CAAC,EAAE;QACpC,IAAI,CAAC9F,QAAQ,GAAGyF,IAAI,CAACQ,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,IAAI,CAAChG,QAAQ,GAAG;YACdiG,KAAK,EAAEb,IAAI,CAACQ,IAAI;YAChBpJ,KAAK,EAAEwJ,CAAC,CAACX,MAAM,EAAEa,MAAM,EAAEC,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAEjB,IAAI,CAACQ,IAAI,CAACzI,QAAQ,CAAC,KAAK,CAAC,GAAGiI,IAAI,GAAGA,IAAI;YAC7CkB,WAAW,EAAElB,IAAI;YACjBmB,SAAS,EAAE/L,YAAY,CAACgM;WACzB;UACD,IAAI,IAAI,CAACC,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAClG,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACDqF,MAAM,CAACc,aAAa,CAACvB,IAAI,CAAC;MAC5B;IACF;EACF;EAGA1F,UAAUA,CAAA;IACR,IAAI,IAAI,CAACM,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACL,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAAC8G,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAClG,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAGAoG,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,kBAAkB,CAACrG,KAAK,EAAE;MACjC,IAAI,CAACsG,iCAAiC,EAAE;MACxC,IAAI,CAACC,wBAAwB,EAAE;IACjC;EACF;EAEA9B,gBAAgBA,CAAA;IACd,IAAI,CAACb,iBAAiB,CAAC4C,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACC,IAAI,CAC7E7M,GAAG,CAAC8M,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAACJ,GAAG,IAAG;UAChD,OAAO;YACLjM,KAAK,EAAEiM,GAAG,CAACK,cAAc;YACzBhH,KAAK,EAAE2G,GAAG,CAACM;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACS,oBAAoB,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAACT,kBAAkB,CAACrG,KAAK,EAAE;UACjC,IAAI,CAACsG,iCAAiC,EAAE;UACxC,IAAI,CAACC,wBAAwB,EAAE;QACjC;MACF;IACF,CAAC,CAAC,CACH,CAACW,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,YAAyB;IAEpC,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACL,GAAG,CAACW,QAAQ,IAAIA,QAAQ,CAAC7G,MAAM,CAAC,CAAC8G,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACQ,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMH,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGR,YAAY,CAACS,OAAO,CAACL,QAAQ,CAAC7G,MAAgB,CAAC;MAClE,IAAIiH,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBT,WAAW,CAACS,UAAU,CAAC,CAACD,IAAI,CAAC;UAC3BvH,SAAS,EAAEoH,QAAQ,EAAEpH,SAAS,IAAI,KAAK;UACvC0H,QAAQ,EAAEN,QAAQ,CAAC/G,GAAG;UACtBH,UAAU,EAAEkH,QAAQ,CAAClH,UAAU;UAC/BK,MAAM,EAAE6G,QAAQ,CAAC7G,MAAM;UACvBvE,UAAU,EAAEoL,QAAQ,CAACpL,UAAU;UAC/BiE,SAAS,EAAEmH,QAAQ,CAACnH;SACrB,CAAC;MACJ;IACF;IACA,OAAO8G,WAAW;EACpB;EAIAY,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACxH,MAAM,IAAI,CAAC,KAAKuH,CAAC,CAACvH,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAyH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjC,kBAAkB,CAACrG,KAAK,EAAE;MACjC,IAAI,CAAC+D,aAAa,CAACwE,6BAA6B,CAAC;QAC/C9B,IAAI,EAAE;UACJ+B,YAAY,EAAE,IAAI,CAACnC,kBAAkB,CAACrG,KAAK;UAAEyI,OAAO,EAAE;;OAEzD,CAAC,CAACvB,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM6B,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAACtB,GAAG,CAACC,OAAO,CAAC;UACpD,IAAI,CAAC+B,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;UAChC,IAAI,IAAI,CAACpJ,qBAAqB,CAACsJ,oBAAoB,EAAE;YACnD,IAAI,CAAC1I,WAAW,GAAG,IAAI,CAACiH,YAAY,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,GAAG,IAAI,CAACF,eAAe,CAAC,EAAE,IAAI,CAACrJ,qBAAqB,CAACwJ,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACxJ,qBAAqB,CAACwJ,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;UAChN,CAAC,MAAM;YACL,IAAI,CAAC5I,WAAW,GAAG,IAAI,CAACiH,YAAY,CAAC,CAAC,GAAGuB,IAAI,CAAC,CAAC;UACjD;UACA,IAAI,CAACvF,WAAW,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;IACJ;EACF;EAEAhC,oBAAoBA,CAAC4H,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAAC3I,SAAS,CAAC;EACjE;EAEAR,uBAAuBA,CAACF,KAAa;IACnC,IAAI,IAAI,CAACuD,WAAW,EAAE;MACpB,IAAIvD,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACM,WAAW,CAAC,CAAC,CAAC,CAACoB,MAAM,EAAE;QACpD,MAAM,IAAI4H,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACjJ,WAAW,EAAE;QACxC,IAAIN,KAAK,IAAIuJ,SAAS,CAAC7H,MAAM,IAAI,CAAC6H,SAAS,CAACvJ,KAAK,CAAC,CAACU,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAT,gBAAgBA,CAACuJ,OAAgB,EAAExJ,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIsJ,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACjJ,WAAW,EAAE;MACxC,IAAIN,KAAK,GAAGuJ,SAAS,CAAC7H,MAAM,IAAK,IAAI,CAAChC,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,KAAKmJ,SAAS,CAACvJ,KAAK,CAAC,CAACY,UAAW,EAAE;QAAE;QACxH2I,SAAS,CAACvJ,KAAK,CAAC,CAACU,SAAS,GAAG8I,OAAO;MACtC;IACF;EACF;EAEAnI,YAAYA,CAACmI,OAAgB,EAAEL,GAAU;IACvC,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtB,IAAInJ,KAAK,GAAGuJ,SAAS,CAAC7H,MAAM,IAAK,IAAI,CAAChC,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,KAAKmJ,SAAS,CAACvJ,KAAK,CAAC,CAACY,UAAW,EAAE;QAAE;QACxH2I,SAAS,CAACvJ,KAAK,CAAC,CAACU,SAAS,GAAG8I,OAAO;MACtC;MACAH,IAAI,CAAC3I,SAAS,GAAG8I,OAAO;IAC1B;EACF;EAKA7C,wBAAwBA,CAAA;IACtB,IAAI,CAAC1C,yBAAyB,CAACwF,qDAAqD,CAAC;MACnF5C,IAAI,EAAE;QACJ6C,YAAY,EAAE,IAAI,CAACjD,kBAAkB,CAACrG,KAAK;QAC3CuJ,iBAAiB,EAAE,IAAI,CAACtL,SAAS;QACjCuL,cAAc,EAAE,IAAI,CAACnL,cAAc;QACnCoL,gBAAgB,EAAE,IAAI,CAAC/K,QAAQ;QAC/BgL,aAAa,EAAE,IAAI,CAAC7K;;KAEvB,CAAC,CAACqI,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACtI,qBAAqB,GAAGoI,GAAG,CAACC,OAAO;QACxC,IAAI,CAACjI,YAAY,GAAGgI,GAAG,CAACC,OAAO,CAAC+C,kBAAkB,IAAI,CAAC;QACvD,IAAI,CAAC7K,iBAAiB,GAAG6H,GAAG,CAACC,OAAO,CAACgD,cAAc,IAAI,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAKA1H,cAAcA,CAAC2H,SAAc,GAC7B;EAEAvD,iCAAiCA,CAAA;IAC/B,IAAI,CAACxC,yBAAyB,CAACgG,8DAA8D,CAAC;MAC5FrD,IAAI,EAAE,IAAI,CAACJ,kBAAkB,CAACrG;KAC/B,CAAC,CAACkH,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACkD,8BAA8B,GAAGpD,GAAG,CAACC,OAAO;MACnD;IACF,CAAC,CAAC;EACJ;EAEAoD,0BAA0BA,CAAC5B,CAAW,EAAEC,CAA8H;IACpK,MAAM4B,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMhB,IAAI,IAAIb,CAAC,EAAE;MACpB,MAAM8B,YAAY,GAAG7B,CAAC,CAAC8B,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKpB,IAAI,IAAImB,KAAK,CAACE,WAAW,CAAC;MACpFL,CAAC,CAAChB,IAAI,CAAC,GAAG,CAAC,CAACiB,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV;EAGAM,cAAcA,CAACvK,KAAU,EAAEwK,OAAc;IACvC,KAAK,MAAMvB,IAAI,IAAIuB,OAAO,EAAE;MAC1B,IAAIvB,IAAI,CAACjJ,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOiJ,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAIAwB,eAAeA,CAACvK,WAA0B,EAAEwK,sBAAgD;IAC1F,MAAMC,cAAc,GAAG,IAAIlD,GAAG,CAACiD,sBAAsB,CAAC3D,GAAG,CAACkC,IAAI,IAAI,GAAGA,IAAI,CAAC3M,UAAU,IAAI2M,IAAI,CAACpI,MAAM,EAAE,CAAC,CAAC;IACvG,OAAOX,WAAW,CAAC6G,GAAG,CAAC6D,UAAU,IAAG;MAClC,OAAOA,UAAU,CAAC7D,GAAG,CAACkC,IAAI,IAAG;QAC3B,MAAM4B,GAAG,GAAG,GAAG5B,IAAI,CAAC3M,UAAU,IAAI2M,IAAI,CAACpI,MAAM,EAAE;QAC/C,IAAI8J,cAAc,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UAC3B5B,IAAI,CAAC3I,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACL2I,IAAI,CAAC3I,SAAS,GAAG,KAAK;QACxB;QACA,OAAO2I,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAJ,eAAeA,CAACkC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAACjE,GAAG,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,CAAC3M,UAAU,IAAI2M,IAAI,CAACpI,MAAM,EAAE,EAAEoI,IAAI,CAAC3I,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOyK,CAAC,CAAChE,GAAG,CAACkC,IAAI,IAAG;MAClB,MAAM4B,GAAG,GAAG,GAAG5B,IAAI,CAAC3M,UAAU,IAAI2M,IAAI,CAACpI,MAAM,EAAE;MAC/C,OAAO;QACL,GAAGoI,IAAI;QACP3I,SAAS,EAAE2K,IAAI,CAACH,GAAG,CAACD,GAAG,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACN,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAGAO,wBAAwBA,CAACnC,IAAS,EAAEoC,GAAQ;IAC1C,IAAI,CAACxH,yBAAyB,CAACyH,qDAAqD,CAAC;MACnF7E,IAAI,EAAEwC,IAAI,CAACL;KACZ,CAAC,CAAC1B,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAM0E,IAAI,GAAG5E,GAAG,CAACC,OAAO;QACxB,IAAI,CAACtH,qBAAqB,GAAG;UAC3BC,QAAQ,EAAEgM,IAAI,CAACC,oBAAoB,EAAEjM,QAAQ,IAAIgF,SAAS;UAC1DD,WAAW,EAAEiH,IAAI,CAACC,oBAAoB,EAAElH,WAAW;UACnDgF,YAAY,EAAEiC,IAAI,CAACC,oBAAoB,EAAElC,YAAY;UACrDV,oBAAoB,EAAE2C,IAAI,CAACC,oBAAoB,EAAE5C,oBAAoB;UACrE7I,mBAAmB,EAAEwL,IAAI,CAACC,oBAAoB,EAAElH,WAAW,GAAG,IAAI,CAACiG,cAAc,CAACgB,IAAI,CAACC,oBAAoB,EAAElH,WAAW,EAAE,IAAI,CAACpB,kBAAkB,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC;UAC/KnB,cAAc,EAAEwJ,IAAI,CAACxJ,cAAc,GAAGwJ,IAAI,CAACxJ,cAAc,GAAGwC,SAAS;UACrE1C,YAAY,EAAE0J,IAAI,CAAC1J,YAAY;UAC/BiH,0BAA0B,EAAEyC,IAAI,CAACzC,0BAA0B,EAAEnB,MAAM,CAAE8D,CAAM,IAAKA,CAAC,CAACnL,SAAS;SAC5F;QACD,IAAI,CAACgI,YAAY,EAAE;QACnB,IAAI,CAAC9E,aAAa,CAACkI,IAAI,CAACL,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAtP,eAAeA,CAACwD,QAAc;IAC5B,IAAIA,QAAQ,EAAEoM,MAAM,CAACD,IAAI,CAACzR,WAAW,CAAC2R,qBAAqB,GAAGrM,QAAQ,EAAE,QAAQ,CAAC;EACnF;EAEAnE,SAASA,CAACiQ,GAAQ,EAAEpC,IAAU;IAC5B,IAAI,CAAC9F,WAAW,GAAG,KAAK;IACxB,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC/D,UAAU,EAAE;IACjB,IAAI,CAACI,qBAAqB,GAAG;MAC3BgF,WAAW,EAAE,CAAC;MACdgF,YAAY,EAAE/E,SAAS;MACvBvI,KAAK,EAAEuI,SAAS;MAChBsH,MAAM,EAAE,EAAE;MACVjD,oBAAoB,EAAErE,SAAS;MAC/BuH,YAAY,EAAE,KAAK;MACnB/L,mBAAmB,EAAE,IAAI,CAACmD,kBAAkB,CAAC,CAAC,CAAC;MAC/CrB,YAAY,EAAE,EAAE;MAChBiH,0BAA0B,EAAEvE;KAC7B;IAED,IAAI0E,IAAI,EAAE;MACR,IAAI,CAAChG,KAAK,GAAG,KAAK;MAClB,IAAI,CAACmI,wBAAwB,CAACnC,IAAI,EAAEoC,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACpI,KAAK,GAAG,IAAI;MACjB,IAAI,CAACqF,YAAY,EAAE;MACnB,IAAI,CAAC9E,aAAa,CAACkI,IAAI,CAACL,GAAG,CAAC;IAC9B;EACF;EAEAU,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACjE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAIkE,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACE,SAAS,CAACD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEApQ,QAAQA,CAACqN,IAAS;IAChB,IAAI0C,MAAM,CAACQ,OAAO,CAAC,WAAWlD,IAAI,CAACL,oBAAoB,IAAI,CAAC,EAAE;MAC5D,IAAI,CAAC/E,yBAAyB,CAACuI,oDAAoD,CAAC;QAClF3F,IAAI,EAAEwC,IAAI,CAACL;OACZ,CAAC,CAAC1B,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACpD,OAAO,CAAC4I,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACjG,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ;IACA,OAAO6C,IAAI;EACb;EAEAqD,WAAWA,CAACC,SAAqC;IAC/C,MAAMC,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAM3B,GAAG,IAAI0B,SAAS,EAAE;MAC3B,IAAIA,SAAS,CAAC1B,GAAG,CAAC,EAAE;QAClB2B,QAAQ,CAAC3E,IAAI,CAACgD,GAAG,CAAC;MACpB;IACF;IACA,OAAO2B,QAAQ;EACjB;EAEAC,gBAAgBA,CAAClB,IAAa;IAC5B,MAAMmB,SAAS,GAA6B,EAAE;IAC9C,KAAK,MAAMvD,SAAS,IAAIoC,IAAI,EAAE;MAC5B,KAAK,MAAMoB,KAAK,IAAIxD,SAAS,EAAE;QAC7B,IAAIwD,KAAK,CAACrM,SAAS,IAAIqM,KAAK,CAACpM,SAAS,IAAIoM,KAAK,CAACnM,UAAU,KAAK,IAAI,CAAClB,qBAAqB,CAACS,mBAAmB,CAACC,KAAK,EAAE;UACnH0M,SAAS,CAAC7E,IAAI,CAAC;YACbG,QAAQ,EAAE2E,KAAK,CAAC3E,QAAQ;YACxB1H,SAAS,EAAEqM,KAAK,CAACrM;WAClB,CAAC;QACJ;MACF;IACF;IACA,OAAOoM,SAAS;EAClB;EAEA3J,uBAAuBA,CAACsI,GAAQ;IAC9B,MAAMuB,KAAK,GAAG;MACZtI,WAAW,EAAE,IAAI,CAAChF,qBAAqB,CAACS,mBAAmB,CAACC,KAAK;MACjEsJ,YAAY,EAAE,IAAI,CAACjD,kBAAkB,CAACrG,KAAK;MAC3ChE,KAAK,EAAE,IAAI,CAACwD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACsG,WAAW,GAAGvB,SAAS;MAC5DsH,MAAM,EAAE,IAAI,CAACY,gBAAgB,CAAC,IAAI,CAACvM,WAAW,CAAC;MAC/C0I,oBAAoB,EAAE,IAAI,CAACtJ,qBAAqB,CAACsJ,oBAAoB,IAAIrE,SAAS;MAClFuH,YAAY,EAAE,IAAI,CAACxM,qBAAqB,CAACwM,YAAY,IAAI,KAAK;MAC9DjK,YAAY,EAAE,IAAI,CAACvC,qBAAqB,CAACuC;KAC1C;IAED,IAAI,CAACgL,UAAU,CAACD,KAAK,CAACf,MAAM,CAAC;IAE7B,IAAI,IAAI,CAACnI,KAAK,CAACoJ,aAAa,CAACxL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACmC,OAAO,CAACsJ,aAAa,CAAC,IAAI,CAACrJ,KAAK,CAACoJ,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC9I,2BAA2B,CAACgJ,qBAAqB,CAACJ,KAAK,CAAC,CAAC1F,SAAS,CAACP,GAAG,IAAG;MAC5E,IAAIA,GAAG,IAAIA,GAAG,CAACF,IAAK,IAAIE,GAAG,CAACF,IAAI,CAACI,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAACpD,OAAO,CAAC4I,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnN,UAAU,EAAE;QACjB,IAAI,CAACqH,wBAAwB,EAAE;QAC/B8E,GAAG,CAAC4B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACxJ,OAAO,CAACyB,YAAY,CAACyB,GAAG,IAAIA,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACF,IAAI,CAACyG,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAGArK,OAAOA,CAACwI,GAAQ;IACdA,GAAG,CAAC4B,KAAK,EAAE;EACb;EAGAJ,UAAUA,CAAChB,MAAa;IACtB,IAAI,CAACnI,KAAK,CAACyJ,KAAK,EAAE;IAClB,IAAI,IAAI,CAAClK,KAAK,IAAI,CAAC,IAAI,CAACzD,QAAQ,EAAE;MAChC,IAAI,CAACkE,KAAK,CAAC0J,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;IACjC;IACA,IAAI,EAAEvB,MAAM,CAACvK,MAAM,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACoC,KAAK,CAAC0J,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnC;IACA,IAAI,CAAC1J,KAAK,CAAC0J,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9N,qBAAqB,CAACuC,YAAY,CAAC;EACxE;EAEAF,aAAaA,CAAC0L,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAI9I,SAAS,EAAE;MACzB,QAAQ8I,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;;;uCAvdWjK,yBAAyB,EAAAnJ,EAAA,CAAAqT,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvT,EAAA,CAAAqT,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzT,EAAA,CAAAqT,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA3T,EAAA,CAAAqT,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA7T,EAAA,CAAAqT,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA/T,EAAA,CAAAqT,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAjU,EAAA,CAAAqT,iBAAA,CAAAW,EAAA,CAAAE,wBAAA,GAAAlU,EAAA,CAAAqT,iBAAA,CAAAW,EAAA,CAAAG,wBAAA,GAAAnU,EAAA,CAAAqT,iBAAA,CAAAW,EAAA,CAAAI,YAAA,GAAApU,EAAA,CAAAqT,iBAAA,CAAAgB,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAzBnL,yBAAyB;MAAAoL,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC/CpC1U,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACyC;UADbD,EAAA,CAAA0D,gBAAA,2BAAAkR,uEAAAhR,MAAA;YAAA5D,EAAA,CAAAW,aAAA,CAAAkU,GAAA;YAAA7U,EAAA,CAAA8D,kBAAA,CAAA6Q,GAAA,CAAAxI,kBAAA,EAAAvI,MAAA,MAAA+Q,GAAA,CAAAxI,kBAAA,GAAAvI,MAAA;YAAA,OAAA5D,EAAA,CAAAiB,WAAA,CAAA2C,MAAA;UAAA,EAAgC;UAC1D5D,EAAA,CAAAS,UAAA,4BAAAqU,wEAAA;YAAA9U,EAAA,CAAAW,aAAA,CAAAkU,GAAA;YAAA,OAAA7U,EAAA,CAAAiB,WAAA,CAAkB0T,GAAA,CAAAzI,iBAAA,EAAmB;UAAA,EAAC;UACtClM,EAAA,CAAA+B,UAAA,KAAAgT,+CAAA,uBAAoE;UAK1E/U,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,cAC2B;UAC7CD,EAAA,CAAA+B,UAAA,KAAAiT,4CAAA,qBAAsF;UAK5FhV,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAENH,EAAA,CAAA+B,UAAA,KAAAkT,kDAAA,4BAA4C;UAiF9CjV,EAAA,CAAAG,YAAA,EAAe;UACfH,EAAA,CAAAmB,SAAA,0BAEiB;UACnBnB,EAAA,CAAAG,YAAA,EAAU;UAIVH,EAAA,CAAA+B,UAAA,KAAAmT,iDAAA,gCAAAlV,EAAA,CAAAmV,sBAAA,CAAoD;;;UA1GdnV,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAuE,gBAAA,YAAAoQ,GAAA,CAAAxI,kBAAA,CAAgC;UAE9BnM,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAuU,GAAA,CAAA/H,oBAAA,CAAuB;UAQiB5M,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAuU,GAAA,CAAAS,QAAA,CAAc;UAO3EpV,EAAA,CAAAM,SAAA,EAA2B;UAA3BN,EAAA,CAAAI,UAAA,SAAAuU,GAAA,CAAAtQ,qBAAA,CAA2B;;;qBDkBlC3E,YAAY,EAAA2V,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,QAAA,EAAE7V,YAAY,EAAA8V,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAArC,EAAA,CAAAsC,eAAA,EAAAtC,EAAA,CAAAuC,mBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EAAAxC,EAAA,CAAAyC,qBAAA,EAAAzC,EAAA,CAAA0C,mBAAA,EAAA1C,EAAA,CAAA2C,gBAAA,EAAA3C,EAAA,CAAA4C,iBAAA,EAAA5C,EAAA,CAAA6C,iBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}