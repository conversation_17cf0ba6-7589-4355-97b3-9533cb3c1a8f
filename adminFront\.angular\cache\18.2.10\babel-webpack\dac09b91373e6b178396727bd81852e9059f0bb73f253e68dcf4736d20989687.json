{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { NgbPagination } from '@ng-bootstrap/ng-bootstrap';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nfunction PaginationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"ngb-pagination\", 4);\n    i0.ɵɵtwoWayListener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.Page, $event) || (ctx_r1.Page = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"pageChange\", function PaginationComponent_div_0_Template_ngb_pagination_pageChange_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pageChange());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"collectionSize\", ctx_r1.CollectionSize);\n    i0.ɵɵtwoWayProperty(\"page\", ctx_r1.Page);\n    i0.ɵɵproperty(\"pageSize\", ctx_r1.PageSize)(\"maxSize\", 5)(\"boundaryLinks\", true);\n  }\n}\nfunction PaginationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" TotalRecords\\uFF1A\", ctx_r1.CollectionSize, \"\\n\");\n  }\n}\nfunction PaginationComponent_p_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 6);\n    i0.ɵɵtext(1, \"\\u7121\\u4EFB\\u4F55\\u8CC7\\u6599.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let PaginationComponent = /*#__PURE__*/(() => {\n  class PaginationComponent {\n    constructor() {\n      this.PageChange = new EventEmitter();\n      this.PageSize = 0;\n      this.CollectionSize = 0;\n      this.PageSizeChange = new EventEmitter();\n      this.CollectionSizeChange = new EventEmitter();\n    }\n    ngOnInit() {}\n    pageChange() {\n      this.PageChange.emit(this.Page);\n    }\n    static {\n      this.ɵfac = function PaginationComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PaginationComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PaginationComponent,\n        selectors: [[\"ngx-pagination\"]],\n        inputs: {\n          Page: \"Page\",\n          PageSize: \"PageSize\",\n          CollectionSize: \"CollectionSize\"\n        },\n        outputs: {\n          PageChange: \"PageChange\",\n          PageSizeChange: \"PageSizeChange\",\n          CollectionSizeChange: \"CollectionSizeChange\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 3,\n        consts: [[\"class\", \"d-flex justify-content-center p-2\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center text-danger fw-bold\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"p-2\"], [3, \"pageChange\", \"collectionSize\", \"page\", \"pageSize\", \"maxSize\", \"boundaryLinks\"], [1, \"text-center\"], [1, \"text-center\", \"text-danger\", \"fw-bold\"]],\n        template: function PaginationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, PaginationComponent_div_0_Template, 2, 5, \"div\", 0)(1, PaginationComponent_div_1_Template, 2, 1, \"div\", 1)(2, PaginationComponent_p_2_Template, 2, 0, \"p\", 2);\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.CollectionSize === 0);\n          }\n        },\n        dependencies: [NgIf, NgbPagination]\n      });\n    }\n  }\n  return PaginationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}