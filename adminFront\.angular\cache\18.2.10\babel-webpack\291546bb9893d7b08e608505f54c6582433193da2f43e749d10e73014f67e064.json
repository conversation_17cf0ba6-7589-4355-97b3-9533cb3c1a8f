{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRequirementGetListPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRequirementGetListPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRequirementGetListPost$Json.PATH = '/api/Requirement/GetList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRequirementGetListPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\requirement\\api-requirement-get-list-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetListRequirementRequest } from '../../models/get-list-requirement-request';\r\nimport { GetRequirementListResponseBase } from '../../models/get-requirement-list-response-base';\r\n\r\nexport interface ApiRequirementGetListPost$Json$Params {\r\n      body?: GetListRequirementRequest\r\n}\r\n\r\nexport function apiRequirementGetListPost$Json(http: HttpClient, rootUrl: string, params?: ApiRequirementGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRequirementListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRequirementGetListPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetRequirementListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRequirementGetListPost$Json.PATH = '/api/Requirement/GetList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,8BAA8BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA8C,EAAEC,OAAqB;EACrJ,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,8BAA8B,CAACM,IAAI,EAAE,MAAM,CAAC;EACnF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAuD;EAChE,CAAC,CAAC,CACH;AACH;AAEAb,8BAA8B,CAACM,IAAI,GAAG,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}