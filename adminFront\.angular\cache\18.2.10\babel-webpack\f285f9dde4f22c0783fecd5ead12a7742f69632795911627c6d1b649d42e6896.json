{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseAnyDigitsSigned } from \"../utils.js\";\nexport var TimestampSecondsParser = /*#__PURE__*/function (_Parser) {\n  _inherits(TimestampSecondsParser, _Parser);\n  var _super = _createSuper(TimestampSecondsParser);\n  function TimestampSecondsParser() {\n    var _this;\n    _classCallCheck(this, TimestampSecondsParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 40);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", '*');\n    return _this;\n  }\n  _createClass(TimestampSecondsParser, [{\n    key: \"parse\",\n    value: function parse(dateString) {\n      return parseAnyDigitsSigned(dateString);\n    }\n  }, {\n    key: \"set\",\n    value: function set(_date, _flags, value) {\n      return [new Date(value * 1000), {\n        timestampIsSet: true\n      }];\n    }\n  }]);\n  return TimestampSecondsParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}