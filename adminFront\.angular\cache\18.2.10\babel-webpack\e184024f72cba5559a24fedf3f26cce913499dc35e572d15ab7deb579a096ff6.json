{"ast": null, "code": "import { m as mergeProps, g as guid, i as isArraysEqual, T as Theme, a as mapHash, B as BaseComponent, V as ViewContextType, C as ContentContainer, b as buildViewClassNames, c as greatestDurationDenominator, d as createDuration, e as BASE_OPTION_DEFAULTS, f as arrayToHash, h as filterHash, j as buildEventSourceRefiners, p as parseEventSource, k as formatWithOrdinals, u as unpromisify, l as buildRangeApiWithTimeZone, n as identity, r as requestJson, s as subtractDurations, o as intersectRanges, q as startOfDay, t as addDays, v as hashValuesToArray, w as buildEventApis, D as DelayedRunner, x as createFormatter, y as diffWholeDays, z as memoize, A as memoizeObjArg, E as isPropsEqual, F as Emitter, G as getInitialDate, H as rangeContainsMarker, I as createEmptyEventStore, J as reduceCurrentDate, K as reduceEventStore, L as rezoneEventStoreDates, M as mergeRawOptions, N as BASE_OPTION_REFINERS, O as CALENDAR_LISTENER_REFINERS, P as CALENDAR_OPTION_REFINERS, Q as COMPLEX_OPTION_COMPARATORS, R as VIEW_OPTION_REFINERS, S as DateEnv, U as DateProfileGenerator, W as createEventUi, X as parseBusinessHours, Y as setRef, Z as Interaction, _ as getElSeg, $ as elementClosest, a0 as EventImpl, a1 as listenBySelector, a2 as listenToHoverBySelector, a3 as PureComponent, a4 as buildViewContext, a5 as getUniqueDomId, a6 as parseInteractionSettings, a7 as interactionSettingsStore, a8 as getNow, a9 as CalendarImpl, aa as flushSync, ab as CalendarRoot, ac as RenderId, ad as ensureElHasStyles, ae as applyStyleProp, af as sliceEventStore } from './internal-common.js';\nexport { ag as JsonRequestError } from './internal-common.js';\nimport { createElement, createRef, Fragment, render } from 'preact';\nimport 'preact/compat';\nconst globalLocales = [];\nconst MINIMAL_RAW_EN_LOCALE = {\n  code: 'en',\n  week: {\n    dow: 0,\n    doy: 4 // 4 days need to be within the year to be considered the first week\n  },\n  direction: 'ltr',\n  buttonText: {\n    prev: 'prev',\n    next: 'next',\n    prevYear: 'prev year',\n    nextYear: 'next year',\n    year: 'year',\n    today: 'today',\n    month: 'month',\n    week: 'week',\n    day: 'day',\n    list: 'list'\n  },\n  weekText: 'W',\n  weekTextLong: 'Week',\n  closeHint: 'Close',\n  timeHint: 'Time',\n  eventHint: 'Event',\n  allDayText: 'all-day',\n  moreLinkText: 'more',\n  noEventsText: 'No events to display'\n};\nconst RAW_EN_LOCALE = Object.assign(Object.assign({}, MINIMAL_RAW_EN_LOCALE), {\n  // Includes things we don't want other locales to inherit,\n  // things that derive from other translatable strings.\n  buttonHints: {\n    prev: 'Previous $0',\n    next: 'Next $0',\n    today(buttonText, unit) {\n      return unit === 'day' ? 'Today' : `This ${buttonText}`;\n    }\n  },\n  viewHint: '$0 view',\n  navLinkHint: 'Go to $0',\n  moreLinkHint(eventCnt) {\n    return `Show ${eventCnt} more event${eventCnt === 1 ? '' : 's'}`;\n  }\n});\nfunction organizeRawLocales(explicitRawLocales) {\n  let defaultCode = explicitRawLocales.length > 0 ? explicitRawLocales[0].code : 'en';\n  let allRawLocales = globalLocales.concat(explicitRawLocales);\n  let rawLocaleMap = {\n    en: RAW_EN_LOCALE\n  };\n  for (let rawLocale of allRawLocales) {\n    rawLocaleMap[rawLocale.code] = rawLocale;\n  }\n  return {\n    map: rawLocaleMap,\n    defaultCode\n  };\n}\nfunction buildLocale(inputSingular, available) {\n  if (typeof inputSingular === 'object' && !Array.isArray(inputSingular)) {\n    return parseLocale(inputSingular.code, [inputSingular.code], inputSingular);\n  }\n  return queryLocale(inputSingular, available);\n}\nfunction queryLocale(codeArg, available) {\n  let codes = [].concat(codeArg || []); // will convert to array\n  let raw = queryRawLocale(codes, available) || RAW_EN_LOCALE;\n  return parseLocale(codeArg, codes, raw);\n}\nfunction queryRawLocale(codes, available) {\n  for (let i = 0; i < codes.length; i += 1) {\n    let parts = codes[i].toLocaleLowerCase().split('-');\n    for (let j = parts.length; j > 0; j -= 1) {\n      let simpleId = parts.slice(0, j).join('-');\n      if (available[simpleId]) {\n        return available[simpleId];\n      }\n    }\n  }\n  return null;\n}\nfunction parseLocale(codeArg, codes, raw) {\n  let merged = mergeProps([MINIMAL_RAW_EN_LOCALE, raw], ['buttonText']);\n  delete merged.code; // don't want this part of the options\n  let {\n    week\n  } = merged;\n  delete merged.week;\n  return {\n    codeArg,\n    codes,\n    week,\n    simpleNumberFormat: new Intl.NumberFormat(codeArg),\n    options: merged\n  };\n}\n\n// TODO: easier way to add new hooks? need to update a million things\nfunction createPlugin(input) {\n  return {\n    id: guid(),\n    name: input.name,\n    premiumReleaseDate: input.premiumReleaseDate ? new Date(input.premiumReleaseDate) : undefined,\n    deps: input.deps || [],\n    reducers: input.reducers || [],\n    isLoadingFuncs: input.isLoadingFuncs || [],\n    contextInit: [].concat(input.contextInit || []),\n    eventRefiners: input.eventRefiners || {},\n    eventDefMemberAdders: input.eventDefMemberAdders || [],\n    eventSourceRefiners: input.eventSourceRefiners || {},\n    isDraggableTransformers: input.isDraggableTransformers || [],\n    eventDragMutationMassagers: input.eventDragMutationMassagers || [],\n    eventDefMutationAppliers: input.eventDefMutationAppliers || [],\n    dateSelectionTransformers: input.dateSelectionTransformers || [],\n    datePointTransforms: input.datePointTransforms || [],\n    dateSpanTransforms: input.dateSpanTransforms || [],\n    views: input.views || {},\n    viewPropsTransformers: input.viewPropsTransformers || [],\n    isPropsValid: input.isPropsValid || null,\n    externalDefTransforms: input.externalDefTransforms || [],\n    viewContainerAppends: input.viewContainerAppends || [],\n    eventDropTransformers: input.eventDropTransformers || [],\n    componentInteractions: input.componentInteractions || [],\n    calendarInteractions: input.calendarInteractions || [],\n    themeClasses: input.themeClasses || {},\n    eventSourceDefs: input.eventSourceDefs || [],\n    cmdFormatter: input.cmdFormatter,\n    recurringTypes: input.recurringTypes || [],\n    namedTimeZonedImpl: input.namedTimeZonedImpl,\n    initialView: input.initialView || '',\n    elementDraggingImpl: input.elementDraggingImpl,\n    optionChangeHandlers: input.optionChangeHandlers || {},\n    scrollGridImpl: input.scrollGridImpl || null,\n    listenerRefiners: input.listenerRefiners || {},\n    optionRefiners: input.optionRefiners || {},\n    propSetHandlers: input.propSetHandlers || {}\n  };\n}\nfunction buildPluginHooks(pluginDefs, globalDefs) {\n  let currentPluginIds = {};\n  let hooks = {\n    premiumReleaseDate: undefined,\n    reducers: [],\n    isLoadingFuncs: [],\n    contextInit: [],\n    eventRefiners: {},\n    eventDefMemberAdders: [],\n    eventSourceRefiners: {},\n    isDraggableTransformers: [],\n    eventDragMutationMassagers: [],\n    eventDefMutationAppliers: [],\n    dateSelectionTransformers: [],\n    datePointTransforms: [],\n    dateSpanTransforms: [],\n    views: {},\n    viewPropsTransformers: [],\n    isPropsValid: null,\n    externalDefTransforms: [],\n    viewContainerAppends: [],\n    eventDropTransformers: [],\n    componentInteractions: [],\n    calendarInteractions: [],\n    themeClasses: {},\n    eventSourceDefs: [],\n    cmdFormatter: null,\n    recurringTypes: [],\n    namedTimeZonedImpl: null,\n    initialView: '',\n    elementDraggingImpl: null,\n    optionChangeHandlers: {},\n    scrollGridImpl: null,\n    listenerRefiners: {},\n    optionRefiners: {},\n    propSetHandlers: {}\n  };\n  function addDefs(defs) {\n    for (let def of defs) {\n      const pluginName = def.name;\n      const currentId = currentPluginIds[pluginName];\n      if (currentId === undefined) {\n        currentPluginIds[pluginName] = def.id;\n        addDefs(def.deps);\n        hooks = combineHooks(hooks, def);\n      } else if (currentId !== def.id) {\n        // different ID than the one already added\n        console.warn(`Duplicate plugin '${pluginName}'`);\n      }\n    }\n  }\n  if (pluginDefs) {\n    addDefs(pluginDefs);\n  }\n  addDefs(globalDefs);\n  return hooks;\n}\nfunction buildBuildPluginHooks() {\n  let currentOverrideDefs = [];\n  let currentGlobalDefs = [];\n  let currentHooks;\n  return (overrideDefs, globalDefs) => {\n    if (!currentHooks || !isArraysEqual(overrideDefs, currentOverrideDefs) || !isArraysEqual(globalDefs, currentGlobalDefs)) {\n      currentHooks = buildPluginHooks(overrideDefs, globalDefs);\n    }\n    currentOverrideDefs = overrideDefs;\n    currentGlobalDefs = globalDefs;\n    return currentHooks;\n  };\n}\nfunction combineHooks(hooks0, hooks1) {\n  return {\n    premiumReleaseDate: compareOptionalDates(hooks0.premiumReleaseDate, hooks1.premiumReleaseDate),\n    reducers: hooks0.reducers.concat(hooks1.reducers),\n    isLoadingFuncs: hooks0.isLoadingFuncs.concat(hooks1.isLoadingFuncs),\n    contextInit: hooks0.contextInit.concat(hooks1.contextInit),\n    eventRefiners: Object.assign(Object.assign({}, hooks0.eventRefiners), hooks1.eventRefiners),\n    eventDefMemberAdders: hooks0.eventDefMemberAdders.concat(hooks1.eventDefMemberAdders),\n    eventSourceRefiners: Object.assign(Object.assign({}, hooks0.eventSourceRefiners), hooks1.eventSourceRefiners),\n    isDraggableTransformers: hooks0.isDraggableTransformers.concat(hooks1.isDraggableTransformers),\n    eventDragMutationMassagers: hooks0.eventDragMutationMassagers.concat(hooks1.eventDragMutationMassagers),\n    eventDefMutationAppliers: hooks0.eventDefMutationAppliers.concat(hooks1.eventDefMutationAppliers),\n    dateSelectionTransformers: hooks0.dateSelectionTransformers.concat(hooks1.dateSelectionTransformers),\n    datePointTransforms: hooks0.datePointTransforms.concat(hooks1.datePointTransforms),\n    dateSpanTransforms: hooks0.dateSpanTransforms.concat(hooks1.dateSpanTransforms),\n    views: Object.assign(Object.assign({}, hooks0.views), hooks1.views),\n    viewPropsTransformers: hooks0.viewPropsTransformers.concat(hooks1.viewPropsTransformers),\n    isPropsValid: hooks1.isPropsValid || hooks0.isPropsValid,\n    externalDefTransforms: hooks0.externalDefTransforms.concat(hooks1.externalDefTransforms),\n    viewContainerAppends: hooks0.viewContainerAppends.concat(hooks1.viewContainerAppends),\n    eventDropTransformers: hooks0.eventDropTransformers.concat(hooks1.eventDropTransformers),\n    calendarInteractions: hooks0.calendarInteractions.concat(hooks1.calendarInteractions),\n    componentInteractions: hooks0.componentInteractions.concat(hooks1.componentInteractions),\n    themeClasses: Object.assign(Object.assign({}, hooks0.themeClasses), hooks1.themeClasses),\n    eventSourceDefs: hooks0.eventSourceDefs.concat(hooks1.eventSourceDefs),\n    cmdFormatter: hooks1.cmdFormatter || hooks0.cmdFormatter,\n    recurringTypes: hooks0.recurringTypes.concat(hooks1.recurringTypes),\n    namedTimeZonedImpl: hooks1.namedTimeZonedImpl || hooks0.namedTimeZonedImpl,\n    initialView: hooks0.initialView || hooks1.initialView,\n    elementDraggingImpl: hooks0.elementDraggingImpl || hooks1.elementDraggingImpl,\n    optionChangeHandlers: Object.assign(Object.assign({}, hooks0.optionChangeHandlers), hooks1.optionChangeHandlers),\n    scrollGridImpl: hooks1.scrollGridImpl || hooks0.scrollGridImpl,\n    listenerRefiners: Object.assign(Object.assign({}, hooks0.listenerRefiners), hooks1.listenerRefiners),\n    optionRefiners: Object.assign(Object.assign({}, hooks0.optionRefiners), hooks1.optionRefiners),\n    propSetHandlers: Object.assign(Object.assign({}, hooks0.propSetHandlers), hooks1.propSetHandlers)\n  };\n}\nfunction compareOptionalDates(date0, date1) {\n  if (date0 === undefined) {\n    return date1;\n  }\n  if (date1 === undefined) {\n    return date0;\n  }\n  return new Date(Math.max(date0.valueOf(), date1.valueOf()));\n}\nclass StandardTheme extends Theme {}\nStandardTheme.prototype.classes = {\n  root: 'fc-theme-standard',\n  tableCellShaded: 'fc-cell-shaded',\n  buttonGroup: 'fc-button-group',\n  button: 'fc-button fc-button-primary',\n  buttonActive: 'fc-button-active'\n};\nStandardTheme.prototype.baseIconClass = 'fc-icon';\nStandardTheme.prototype.iconClasses = {\n  close: 'fc-icon-x',\n  prev: 'fc-icon-chevron-left',\n  next: 'fc-icon-chevron-right',\n  prevYear: 'fc-icon-chevrons-left',\n  nextYear: 'fc-icon-chevrons-right'\n};\nStandardTheme.prototype.rtlIconClasses = {\n  prev: 'fc-icon-chevron-right',\n  next: 'fc-icon-chevron-left',\n  prevYear: 'fc-icon-chevrons-right',\n  nextYear: 'fc-icon-chevrons-left'\n};\nStandardTheme.prototype.iconOverrideOption = 'buttonIcons'; // TODO: make TS-friendly\nStandardTheme.prototype.iconOverrideCustomButtonOption = 'icon';\nStandardTheme.prototype.iconOverridePrefix = 'fc-icon-';\nfunction compileViewDefs(defaultConfigs, overrideConfigs) {\n  let hash = {};\n  let viewType;\n  for (viewType in defaultConfigs) {\n    ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  }\n  for (viewType in overrideConfigs) {\n    ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  }\n  return hash;\n}\nfunction ensureViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n  if (hash[viewType]) {\n    return hash[viewType];\n  }\n  let viewDef = buildViewDef(viewType, hash, defaultConfigs, overrideConfigs);\n  if (viewDef) {\n    hash[viewType] = viewDef;\n  }\n  return viewDef;\n}\nfunction buildViewDef(viewType, hash, defaultConfigs, overrideConfigs) {\n  let defaultConfig = defaultConfigs[viewType];\n  let overrideConfig = overrideConfigs[viewType];\n  let queryProp = name => defaultConfig && defaultConfig[name] !== null ? defaultConfig[name] : overrideConfig && overrideConfig[name] !== null ? overrideConfig[name] : null;\n  let theComponent = queryProp('component');\n  let superType = queryProp('superType');\n  let superDef = null;\n  if (superType) {\n    if (superType === viewType) {\n      throw new Error('Can\\'t have a custom view type that references itself');\n    }\n    superDef = ensureViewDef(superType, hash, defaultConfigs, overrideConfigs);\n  }\n  if (!theComponent && superDef) {\n    theComponent = superDef.component;\n  }\n  if (!theComponent) {\n    return null; // don't throw a warning, might be settings for a single-unit view\n  }\n  return {\n    type: viewType,\n    component: theComponent,\n    defaults: Object.assign(Object.assign({}, superDef ? superDef.defaults : {}), defaultConfig ? defaultConfig.rawOptions : {}),\n    overrides: Object.assign(Object.assign({}, superDef ? superDef.overrides : {}), overrideConfig ? overrideConfig.rawOptions : {})\n  };\n}\nfunction parseViewConfigs(inputs) {\n  return mapHash(inputs, parseViewConfig);\n}\nfunction parseViewConfig(input) {\n  let rawOptions = typeof input === 'function' ? {\n    component: input\n  } : input;\n  let {\n    component\n  } = rawOptions;\n  if (rawOptions.content) {\n    // TODO: remove content/classNames/didMount/etc from options?\n    component = createViewHookComponent(rawOptions);\n  } else if (component && !(component.prototype instanceof BaseComponent)) {\n    // WHY?: people were using `component` property for `content`\n    // TODO: converge on one setting name\n    component = createViewHookComponent(Object.assign(Object.assign({}, rawOptions), {\n      content: component\n    }));\n  }\n  return {\n    superType: rawOptions.type,\n    component: component,\n    rawOptions // includes type and component too :(\n  };\n}\nfunction createViewHookComponent(options) {\n  return viewProps => createElement(ViewContextType.Consumer, null, context => createElement(ContentContainer, {\n    elTag: \"div\",\n    elClasses: buildViewClassNames(context.viewSpec),\n    renderProps: Object.assign(Object.assign({}, viewProps), {\n      nextDayThreshold: context.options.nextDayThreshold\n    }),\n    generatorName: undefined,\n    customGenerator: options.content,\n    classNameGenerator: options.classNames,\n    didMount: options.didMount,\n    willUnmount: options.willUnmount\n  }));\n}\nfunction buildViewSpecs(defaultInputs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n  let defaultConfigs = parseViewConfigs(defaultInputs);\n  let overrideConfigs = parseViewConfigs(optionOverrides.views);\n  let viewDefs = compileViewDefs(defaultConfigs, overrideConfigs);\n  return mapHash(viewDefs, viewDef => buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults));\n}\nfunction buildViewSpec(viewDef, overrideConfigs, optionOverrides, dynamicOptionOverrides, localeDefaults) {\n  let durationInput = viewDef.overrides.duration || viewDef.defaults.duration || dynamicOptionOverrides.duration || optionOverrides.duration;\n  let duration = null;\n  let durationUnit = '';\n  let singleUnit = '';\n  let singleUnitOverrides = {};\n  if (durationInput) {\n    duration = createDurationCached(durationInput);\n    if (duration) {\n      // valid?\n      let denom = greatestDurationDenominator(duration);\n      durationUnit = denom.unit;\n      if (denom.value === 1) {\n        singleUnit = durationUnit;\n        singleUnitOverrides = overrideConfigs[durationUnit] ? overrideConfigs[durationUnit].rawOptions : {};\n      }\n    }\n  }\n  let queryButtonText = optionsSubset => {\n    let buttonTextMap = optionsSubset.buttonText || {};\n    let buttonTextKey = viewDef.defaults.buttonTextKey;\n    if (buttonTextKey != null && buttonTextMap[buttonTextKey] != null) {\n      return buttonTextMap[buttonTextKey];\n    }\n    if (buttonTextMap[viewDef.type] != null) {\n      return buttonTextMap[viewDef.type];\n    }\n    if (buttonTextMap[singleUnit] != null) {\n      return buttonTextMap[singleUnit];\n    }\n    return null;\n  };\n  let queryButtonTitle = optionsSubset => {\n    let buttonHints = optionsSubset.buttonHints || {};\n    let buttonKey = viewDef.defaults.buttonTextKey; // use same key as text\n    if (buttonKey != null && buttonHints[buttonKey] != null) {\n      return buttonHints[buttonKey];\n    }\n    if (buttonHints[viewDef.type] != null) {\n      return buttonHints[viewDef.type];\n    }\n    if (buttonHints[singleUnit] != null) {\n      return buttonHints[singleUnit];\n    }\n    return null;\n  };\n  return {\n    type: viewDef.type,\n    component: viewDef.component,\n    duration,\n    durationUnit,\n    singleUnit,\n    optionDefaults: viewDef.defaults,\n    optionOverrides: Object.assign(Object.assign({}, singleUnitOverrides), viewDef.overrides),\n    buttonTextOverride: queryButtonText(dynamicOptionOverrides) || queryButtonText(optionOverrides) ||\n    // constructor-specified buttonText lookup hash takes precedence\n    viewDef.overrides.buttonText,\n    buttonTextDefault: queryButtonText(localeDefaults) || viewDef.defaults.buttonText || queryButtonText(BASE_OPTION_DEFAULTS) || viewDef.type,\n    // not DRY\n    buttonTitleOverride: queryButtonTitle(dynamicOptionOverrides) || queryButtonTitle(optionOverrides) || viewDef.overrides.buttonHint,\n    buttonTitleDefault: queryButtonTitle(localeDefaults) || viewDef.defaults.buttonHint || queryButtonTitle(BASE_OPTION_DEFAULTS)\n    // will eventually fall back to buttonText\n  };\n}\n// hack to get memoization working\nlet durationInputMap = {};\nfunction createDurationCached(durationInput) {\n  let json = JSON.stringify(durationInput);\n  let res = durationInputMap[json];\n  if (res === undefined) {\n    res = createDuration(durationInput);\n    durationInputMap[json] = res;\n  }\n  return res;\n}\nfunction reduceViewType(viewType, action) {\n  switch (action.type) {\n    case 'CHANGE_VIEW_TYPE':\n      viewType = action.viewType;\n  }\n  return viewType;\n}\nfunction reduceDynamicOptionOverrides(dynamicOptionOverrides, action) {\n  switch (action.type) {\n    case 'SET_OPTION':\n      return Object.assign(Object.assign({}, dynamicOptionOverrides), {\n        [action.optionName]: action.rawOptionValue\n      });\n    default:\n      return dynamicOptionOverrides;\n  }\n}\nfunction reduceDateProfile(currentDateProfile, action, currentDate, dateProfileGenerator) {\n  let dp;\n  switch (action.type) {\n    case 'CHANGE_VIEW_TYPE':\n      return dateProfileGenerator.build(action.dateMarker || currentDate);\n    case 'CHANGE_DATE':\n      return dateProfileGenerator.build(action.dateMarker);\n    case 'PREV':\n      dp = dateProfileGenerator.buildPrev(currentDateProfile, currentDate);\n      if (dp.isValid) {\n        return dp;\n      }\n      break;\n    case 'NEXT':\n      dp = dateProfileGenerator.buildNext(currentDateProfile, currentDate);\n      if (dp.isValid) {\n        return dp;\n      }\n      break;\n  }\n  return currentDateProfile;\n}\nfunction initEventSources(calendarOptions, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null;\n  return addSources({}, parseInitialSources(calendarOptions, context), activeRange, context);\n}\nfunction reduceEventSources(eventSources, action, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n  switch (action.type) {\n    case 'ADD_EVENT_SOURCES':\n      // already parsed\n      return addSources(eventSources, action.sources, activeRange, context);\n    case 'REMOVE_EVENT_SOURCE':\n      return removeSource(eventSources, action.sourceId);\n    case 'PREV': // TODO: how do we track all actions that affect dateProfile :(\n    case 'NEXT':\n    case 'CHANGE_DATE':\n    case 'CHANGE_VIEW_TYPE':\n      if (dateProfile) {\n        return fetchDirtySources(eventSources, activeRange, context);\n      }\n      return eventSources;\n    case 'FETCH_EVENT_SOURCES':\n      return fetchSourcesByIds(eventSources, action.sourceIds ?\n      // why no type?\n      arrayToHash(action.sourceIds) : excludeStaticSources(eventSources, context), activeRange, action.isRefetch || false, context);\n    case 'RECEIVE_EVENTS':\n    case 'RECEIVE_EVENT_ERROR':\n      return receiveResponse(eventSources, action.sourceId, action.fetchId, action.fetchRange);\n    case 'REMOVE_ALL_EVENT_SOURCES':\n      return {};\n    default:\n      return eventSources;\n  }\n}\nfunction reduceEventSourcesNewTimeZone(eventSources, dateProfile, context) {\n  let activeRange = dateProfile ? dateProfile.activeRange : null; // need this check?\n  return fetchSourcesByIds(eventSources, excludeStaticSources(eventSources, context), activeRange, true, context);\n}\nfunction computeEventSourcesLoading(eventSources) {\n  for (let sourceId in eventSources) {\n    if (eventSources[sourceId].isFetching) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction addSources(eventSourceHash, sources, fetchRange, context) {\n  let hash = {};\n  for (let source of sources) {\n    hash[source.sourceId] = source;\n  }\n  if (fetchRange) {\n    hash = fetchDirtySources(hash, fetchRange, context);\n  }\n  return Object.assign(Object.assign({}, eventSourceHash), hash);\n}\nfunction removeSource(eventSourceHash, sourceId) {\n  return filterHash(eventSourceHash, eventSource => eventSource.sourceId !== sourceId);\n}\nfunction fetchDirtySources(sourceHash, fetchRange, context) {\n  return fetchSourcesByIds(sourceHash, filterHash(sourceHash, eventSource => isSourceDirty(eventSource, fetchRange, context)), fetchRange, false, context);\n}\nfunction isSourceDirty(eventSource, fetchRange, context) {\n  if (!doesSourceNeedRange(eventSource, context)) {\n    return !eventSource.latestFetchId;\n  }\n  return !context.options.lazyFetching || !eventSource.fetchRange || eventSource.isFetching ||\n  // always cancel outdated in-progress fetches\n  fetchRange.start < eventSource.fetchRange.start || fetchRange.end > eventSource.fetchRange.end;\n}\nfunction fetchSourcesByIds(prevSources, sourceIdHash, fetchRange, isRefetch, context) {\n  let nextSources = {};\n  for (let sourceId in prevSources) {\n    let source = prevSources[sourceId];\n    if (sourceIdHash[sourceId]) {\n      nextSources[sourceId] = fetchSource(source, fetchRange, isRefetch, context);\n    } else {\n      nextSources[sourceId] = source;\n    }\n  }\n  return nextSources;\n}\nfunction fetchSource(eventSource, fetchRange, isRefetch, context) {\n  let {\n    options,\n    calendarApi\n  } = context;\n  let sourceDef = context.pluginHooks.eventSourceDefs[eventSource.sourceDefId];\n  let fetchId = guid();\n  sourceDef.fetch({\n    eventSource,\n    range: fetchRange,\n    isRefetch,\n    context\n  }, res => {\n    let {\n      rawEvents\n    } = res;\n    if (options.eventSourceSuccess) {\n      rawEvents = options.eventSourceSuccess.call(calendarApi, rawEvents, res.response) || rawEvents;\n    }\n    if (eventSource.success) {\n      rawEvents = eventSource.success.call(calendarApi, rawEvents, res.response) || rawEvents;\n    }\n    context.dispatch({\n      type: 'RECEIVE_EVENTS',\n      sourceId: eventSource.sourceId,\n      fetchId,\n      fetchRange,\n      rawEvents\n    });\n  }, error => {\n    let errorHandled = false;\n    if (options.eventSourceFailure) {\n      options.eventSourceFailure.call(calendarApi, error);\n      errorHandled = true;\n    }\n    if (eventSource.failure) {\n      eventSource.failure(error);\n      errorHandled = true;\n    }\n    if (!errorHandled) {\n      console.warn(error.message, error);\n    }\n    context.dispatch({\n      type: 'RECEIVE_EVENT_ERROR',\n      sourceId: eventSource.sourceId,\n      fetchId,\n      fetchRange,\n      error\n    });\n  });\n  return Object.assign(Object.assign({}, eventSource), {\n    isFetching: true,\n    latestFetchId: fetchId\n  });\n}\nfunction receiveResponse(sourceHash, sourceId, fetchId, fetchRange) {\n  let eventSource = sourceHash[sourceId];\n  if (eventSource &&\n  // not already removed\n  fetchId === eventSource.latestFetchId) {\n    return Object.assign(Object.assign({}, sourceHash), {\n      [sourceId]: Object.assign(Object.assign({}, eventSource), {\n        isFetching: false,\n        fetchRange\n      })\n    });\n  }\n  return sourceHash;\n}\nfunction excludeStaticSources(eventSources, context) {\n  return filterHash(eventSources, eventSource => doesSourceNeedRange(eventSource, context));\n}\nfunction parseInitialSources(rawOptions, context) {\n  let refiners = buildEventSourceRefiners(context);\n  let rawSources = [].concat(rawOptions.eventSources || []);\n  let sources = []; // parsed\n  if (rawOptions.initialEvents) {\n    rawSources.unshift(rawOptions.initialEvents);\n  }\n  if (rawOptions.events) {\n    rawSources.unshift(rawOptions.events);\n  }\n  for (let rawSource of rawSources) {\n    let source = parseEventSource(rawSource, context, refiners);\n    if (source) {\n      sources.push(source);\n    }\n  }\n  return sources;\n}\nfunction doesSourceNeedRange(eventSource, context) {\n  let defs = context.pluginHooks.eventSourceDefs;\n  return !defs[eventSource.sourceDefId].ignoreRange;\n}\nfunction reduceDateSelection(currentSelection, action) {\n  switch (action.type) {\n    case 'UNSELECT_DATES':\n      return null;\n    case 'SELECT_DATES':\n      return action.selection;\n    default:\n      return currentSelection;\n  }\n}\nfunction reduceSelectedEvent(currentInstanceId, action) {\n  switch (action.type) {\n    case 'UNSELECT_EVENT':\n      return '';\n    case 'SELECT_EVENT':\n      return action.eventInstanceId;\n    default:\n      return currentInstanceId;\n  }\n}\nfunction reduceEventDrag(currentDrag, action) {\n  let newDrag;\n  switch (action.type) {\n    case 'UNSET_EVENT_DRAG':\n      return null;\n    case 'SET_EVENT_DRAG':\n      newDrag = action.state;\n      return {\n        affectedEvents: newDrag.affectedEvents,\n        mutatedEvents: newDrag.mutatedEvents,\n        isEvent: newDrag.isEvent\n      };\n    default:\n      return currentDrag;\n  }\n}\nfunction reduceEventResize(currentResize, action) {\n  let newResize;\n  switch (action.type) {\n    case 'UNSET_EVENT_RESIZE':\n      return null;\n    case 'SET_EVENT_RESIZE':\n      newResize = action.state;\n      return {\n        affectedEvents: newResize.affectedEvents,\n        mutatedEvents: newResize.mutatedEvents,\n        isEvent: newResize.isEvent\n      };\n    default:\n      return currentResize;\n  }\n}\nfunction parseToolbars(calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n  let header = calendarOptions.headerToolbar ? parseToolbar(calendarOptions.headerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n  let footer = calendarOptions.footerToolbar ? parseToolbar(calendarOptions.footerToolbar, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) : null;\n  return {\n    header,\n    footer\n  };\n}\nfunction parseToolbar(sectionStrHash, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi) {\n  let sectionWidgets = {};\n  let viewsWithButtons = [];\n  let hasTitle = false;\n  for (let sectionName in sectionStrHash) {\n    let sectionStr = sectionStrHash[sectionName];\n    let sectionRes = parseSection(sectionStr, calendarOptions, calendarOptionOverrides, theme, viewSpecs, calendarApi);\n    sectionWidgets[sectionName] = sectionRes.widgets;\n    viewsWithButtons.push(...sectionRes.viewsWithButtons);\n    hasTitle = hasTitle || sectionRes.hasTitle;\n  }\n  return {\n    sectionWidgets,\n    viewsWithButtons,\n    hasTitle\n  };\n}\n/*\nBAD: querying icons and text here. should be done at render time\n*/\nfunction parseSection(sectionStr, calendarOptions,\n// defaults+overrides, then refined\ncalendarOptionOverrides,\n// overrides only!, unrefined :(\ntheme, viewSpecs, calendarApi) {\n  let isRtl = calendarOptions.direction === 'rtl';\n  let calendarCustomButtons = calendarOptions.customButtons || {};\n  let calendarButtonTextOverrides = calendarOptionOverrides.buttonText || {};\n  let calendarButtonText = calendarOptions.buttonText || {};\n  let calendarButtonHintOverrides = calendarOptionOverrides.buttonHints || {};\n  let calendarButtonHints = calendarOptions.buttonHints || {};\n  let sectionSubstrs = sectionStr ? sectionStr.split(' ') : [];\n  let viewsWithButtons = [];\n  let hasTitle = false;\n  let widgets = sectionSubstrs.map(buttonGroupStr => buttonGroupStr.split(',').map(buttonName => {\n    if (buttonName === 'title') {\n      hasTitle = true;\n      return {\n        buttonName\n      };\n    }\n    let customButtonProps;\n    let viewSpec;\n    let buttonClick;\n    let buttonIcon; // only one of these will be set\n    let buttonText; // \"\n    let buttonHint;\n    // ^ for the title=\"\" attribute, for accessibility\n    if (customButtonProps = calendarCustomButtons[buttonName]) {\n      buttonClick = ev => {\n        if (customButtonProps.click) {\n          customButtonProps.click.call(ev.target, ev, ev.target); // TODO: use Calendar this context?\n        }\n      };\n      (buttonIcon = theme.getCustomButtonIconClass(customButtonProps)) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = customButtonProps.text);\n      buttonHint = customButtonProps.hint || customButtonProps.text;\n    } else if (viewSpec = viewSpecs[buttonName]) {\n      viewsWithButtons.push(buttonName);\n      buttonClick = () => {\n        calendarApi.changeView(buttonName);\n      };\n      (buttonText = viewSpec.buttonTextOverride) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = viewSpec.buttonTextDefault);\n      let textFallback = viewSpec.buttonTextOverride || viewSpec.buttonTextDefault;\n      buttonHint = formatWithOrdinals(viewSpec.buttonTitleOverride || viewSpec.buttonTitleDefault || calendarOptions.viewHint, [textFallback, buttonName],\n      // view-name = buttonName\n      textFallback);\n    } else if (calendarApi[buttonName]) {\n      // a calendarApi method\n      buttonClick = () => {\n        calendarApi[buttonName]();\n      };\n      (buttonText = calendarButtonTextOverrides[buttonName]) || (buttonIcon = theme.getIconClass(buttonName, isRtl)) || (buttonText = calendarButtonText[buttonName]); // everything else is considered default\n      if (buttonName === 'prevYear' || buttonName === 'nextYear') {\n        let prevOrNext = buttonName === 'prevYear' ? 'prev' : 'next';\n        buttonHint = formatWithOrdinals(calendarButtonHintOverrides[prevOrNext] || calendarButtonHints[prevOrNext], [calendarButtonText.year || 'year', 'year'], calendarButtonText[buttonName]);\n      } else {\n        buttonHint = navUnit => formatWithOrdinals(calendarButtonHintOverrides[buttonName] || calendarButtonHints[buttonName], [calendarButtonText[navUnit] || navUnit, navUnit], calendarButtonText[buttonName]);\n      }\n    }\n    return {\n      buttonName,\n      buttonClick,\n      buttonIcon,\n      buttonText,\n      buttonHint\n    };\n  }));\n  return {\n    widgets,\n    viewsWithButtons,\n    hasTitle\n  };\n}\n\n// always represents the current view. otherwise, it'd need to change value every time date changes\nclass ViewImpl {\n  constructor(type, getCurrentData, dateEnv) {\n    this.type = type;\n    this.getCurrentData = getCurrentData;\n    this.dateEnv = dateEnv;\n  }\n  get calendar() {\n    return this.getCurrentData().calendarApi;\n  }\n  get title() {\n    return this.getCurrentData().viewTitle;\n  }\n  get activeStart() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start);\n  }\n  get activeEnd() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end);\n  }\n  get currentStart() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start);\n  }\n  get currentEnd() {\n    return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end);\n  }\n  getOption(name) {\n    return this.getCurrentData().options[name]; // are the view-specific options\n  }\n}\nlet eventSourceDef$2 = {\n  ignoreRange: true,\n  parseMeta(refined) {\n    if (Array.isArray(refined.events)) {\n      return refined.events;\n    }\n    return null;\n  },\n  fetch(arg, successCallback) {\n    successCallback({\n      rawEvents: arg.eventSource.meta\n    });\n  }\n};\nconst arrayEventSourcePlugin = createPlugin({\n  name: 'array-event-source',\n  eventSourceDefs: [eventSourceDef$2]\n});\nlet eventSourceDef$1 = {\n  parseMeta(refined) {\n    if (typeof refined.events === 'function') {\n      return refined.events;\n    }\n    return null;\n  },\n  fetch(arg, successCallback, errorCallback) {\n    const {\n      dateEnv\n    } = arg.context;\n    const func = arg.eventSource.meta;\n    unpromisify(func.bind(null, buildRangeApiWithTimeZone(arg.range, dateEnv)), rawEvents => successCallback({\n      rawEvents\n    }), errorCallback);\n  }\n};\nconst funcEventSourcePlugin = createPlugin({\n  name: 'func-event-source',\n  eventSourceDefs: [eventSourceDef$1]\n});\nconst JSON_FEED_EVENT_SOURCE_REFINERS = {\n  method: String,\n  extraParams: identity,\n  startParam: String,\n  endParam: String,\n  timeZoneParam: String\n};\nlet eventSourceDef = {\n  parseMeta(refined) {\n    if (refined.url && (refined.format === 'json' || !refined.format)) {\n      return {\n        url: refined.url,\n        format: 'json',\n        method: (refined.method || 'GET').toUpperCase(),\n        extraParams: refined.extraParams,\n        startParam: refined.startParam,\n        endParam: refined.endParam,\n        timeZoneParam: refined.timeZoneParam\n      };\n    }\n    return null;\n  },\n  fetch(arg, successCallback, errorCallback) {\n    const {\n      meta\n    } = arg.eventSource;\n    const requestParams = buildRequestParams(meta, arg.range, arg.context);\n    requestJson(meta.method, meta.url, requestParams).then(([rawEvents, response]) => {\n      successCallback({\n        rawEvents,\n        response\n      });\n    }, errorCallback);\n  }\n};\nconst jsonFeedEventSourcePlugin = createPlugin({\n  name: 'json-event-source',\n  eventSourceRefiners: JSON_FEED_EVENT_SOURCE_REFINERS,\n  eventSourceDefs: [eventSourceDef]\n});\nfunction buildRequestParams(meta, range, context) {\n  let {\n    dateEnv,\n    options\n  } = context;\n  let startParam;\n  let endParam;\n  let timeZoneParam;\n  let customRequestParams;\n  let params = {};\n  startParam = meta.startParam;\n  if (startParam == null) {\n    startParam = options.startParam;\n  }\n  endParam = meta.endParam;\n  if (endParam == null) {\n    endParam = options.endParam;\n  }\n  timeZoneParam = meta.timeZoneParam;\n  if (timeZoneParam == null) {\n    timeZoneParam = options.timeZoneParam;\n  }\n  // retrieve any outbound GET/POST data from the options\n  if (typeof meta.extraParams === 'function') {\n    // supplied as a function that returns a key/value object\n    customRequestParams = meta.extraParams();\n  } else {\n    // probably supplied as a straight key/value object\n    customRequestParams = meta.extraParams || {};\n  }\n  Object.assign(params, customRequestParams);\n  params[startParam] = dateEnv.formatIso(range.start);\n  params[endParam] = dateEnv.formatIso(range.end);\n  if (dateEnv.timeZone !== 'local') {\n    params[timeZoneParam] = dateEnv.timeZone;\n  }\n  return params;\n}\nconst SIMPLE_RECURRING_REFINERS = {\n  daysOfWeek: identity,\n  startTime: createDuration,\n  endTime: createDuration,\n  duration: createDuration,\n  startRecur: identity,\n  endRecur: identity\n};\nlet recurring = {\n  parse(refined, dateEnv) {\n    if (refined.daysOfWeek || refined.startTime || refined.endTime || refined.startRecur || refined.endRecur) {\n      let recurringData = {\n        daysOfWeek: refined.daysOfWeek || null,\n        startTime: refined.startTime || null,\n        endTime: refined.endTime || null,\n        startRecur: refined.startRecur ? dateEnv.createMarker(refined.startRecur) : null,\n        endRecur: refined.endRecur ? dateEnv.createMarker(refined.endRecur) : null\n      };\n      let duration;\n      if (refined.duration) {\n        duration = refined.duration;\n      }\n      if (!duration && refined.startTime && refined.endTime) {\n        duration = subtractDurations(refined.endTime, refined.startTime);\n      }\n      return {\n        allDayGuess: Boolean(!refined.startTime && !refined.endTime),\n        duration,\n        typeData: recurringData // doesn't need endTime anymore but oh well\n      };\n    }\n    return null;\n  },\n  expand(typeData, framingRange, dateEnv) {\n    let clippedFramingRange = intersectRanges(framingRange, {\n      start: typeData.startRecur,\n      end: typeData.endRecur\n    });\n    if (clippedFramingRange) {\n      return expandRanges(typeData.daysOfWeek, typeData.startTime, clippedFramingRange, dateEnv);\n    }\n    return [];\n  }\n};\nconst simpleRecurringEventsPlugin = createPlugin({\n  name: 'simple-recurring-event',\n  recurringTypes: [recurring],\n  eventRefiners: SIMPLE_RECURRING_REFINERS\n});\nfunction expandRanges(daysOfWeek, startTime, framingRange, dateEnv) {\n  let dowHash = daysOfWeek ? arrayToHash(daysOfWeek) : null;\n  let dayMarker = startOfDay(framingRange.start);\n  let endMarker = framingRange.end;\n  let instanceStarts = [];\n  while (dayMarker < endMarker) {\n    let instanceStart;\n    // if everyday, or this particular day-of-week\n    if (!dowHash || dowHash[dayMarker.getUTCDay()]) {\n      if (startTime) {\n        instanceStart = dateEnv.add(dayMarker, startTime);\n      } else {\n        instanceStart = dayMarker;\n      }\n      instanceStarts.push(instanceStart);\n    }\n    dayMarker = addDays(dayMarker, 1);\n  }\n  return instanceStarts;\n}\nconst changeHandlerPlugin = createPlugin({\n  name: 'change-handler',\n  optionChangeHandlers: {\n    events(events, context) {\n      handleEventSources([events], context);\n    },\n    eventSources: handleEventSources\n  }\n});\n/*\nBUG: if `event` was supplied, all previously-given `eventSources` will be wiped out\n*/\nfunction handleEventSources(inputs, context) {\n  let unfoundSources = hashValuesToArray(context.getCurrentData().eventSources);\n  if (unfoundSources.length === 1 && inputs.length === 1 && Array.isArray(unfoundSources[0]._raw) && Array.isArray(inputs[0])) {\n    context.dispatch({\n      type: 'RESET_RAW_EVENTS',\n      sourceId: unfoundSources[0].sourceId,\n      rawEvents: inputs[0]\n    });\n    return;\n  }\n  let newInputs = [];\n  for (let input of inputs) {\n    let inputFound = false;\n    for (let i = 0; i < unfoundSources.length; i += 1) {\n      if (unfoundSources[i]._raw === input) {\n        unfoundSources.splice(i, 1); // delete\n        inputFound = true;\n        break;\n      }\n    }\n    if (!inputFound) {\n      newInputs.push(input);\n    }\n  }\n  for (let unfoundSource of unfoundSources) {\n    context.dispatch({\n      type: 'REMOVE_EVENT_SOURCE',\n      sourceId: unfoundSource.sourceId\n    });\n  }\n  for (let newInput of newInputs) {\n    context.calendarApi.addEventSource(newInput);\n  }\n}\nfunction handleDateProfile(dateProfile, context) {\n  context.emitter.trigger('datesSet', Object.assign(Object.assign({}, buildRangeApiWithTimeZone(dateProfile.activeRange, context.dateEnv)), {\n    view: context.viewApi\n  }));\n}\nfunction handleEventStore(eventStore, context) {\n  let {\n    emitter\n  } = context;\n  if (emitter.hasHandlers('eventsSet')) {\n    emitter.trigger('eventsSet', buildEventApis(eventStore, context));\n  }\n}\n\n/*\nthis array is exposed on the root namespace so that UMD plugins can add to it.\nsee the rollup-bundles script.\n*/\nconst globalPlugins = [arrayEventSourcePlugin, funcEventSourcePlugin, jsonFeedEventSourcePlugin, simpleRecurringEventsPlugin, changeHandlerPlugin, createPlugin({\n  name: 'misc',\n  isLoadingFuncs: [state => computeEventSourcesLoading(state.eventSources)],\n  propSetHandlers: {\n    dateProfile: handleDateProfile,\n    eventStore: handleEventStore\n  }\n})];\nclass TaskRunner {\n  constructor(runTaskOption, drainedOption) {\n    this.runTaskOption = runTaskOption;\n    this.drainedOption = drainedOption;\n    this.queue = [];\n    this.delayedRunner = new DelayedRunner(this.drain.bind(this));\n  }\n  request(task, delay) {\n    this.queue.push(task);\n    this.delayedRunner.request(delay);\n  }\n  pause(scope) {\n    this.delayedRunner.pause(scope);\n  }\n  resume(scope, force) {\n    this.delayedRunner.resume(scope, force);\n  }\n  drain() {\n    let {\n      queue\n    } = this;\n    while (queue.length) {\n      let completedTasks = [];\n      let task;\n      while (task = queue.shift()) {\n        this.runTask(task);\n        completedTasks.push(task);\n      }\n      this.drained(completedTasks);\n    } // keep going, in case new tasks were added in the drained handler\n  }\n  runTask(task) {\n    if (this.runTaskOption) {\n      this.runTaskOption(task);\n    }\n  }\n  drained(completedTasks) {\n    if (this.drainedOption) {\n      this.drainedOption(completedTasks);\n    }\n  }\n}\n\n// Computes what the title at the top of the calendarApi should be for this view\nfunction buildTitle(dateProfile, viewOptions, dateEnv) {\n  let range;\n  // for views that span a large unit of time, show the proper interval, ignoring stray days before and after\n  if (/^(year|month)$/.test(dateProfile.currentRangeUnit)) {\n    range = dateProfile.currentRange;\n  } else {\n    // for day units or smaller, use the actual day range\n    range = dateProfile.activeRange;\n  }\n  return dateEnv.formatRange(range.start, range.end, createFormatter(viewOptions.titleFormat || buildTitleFormat(dateProfile)), {\n    isEndExclusive: dateProfile.isRangeAllDay,\n    defaultSeparator: viewOptions.titleRangeSeparator\n  });\n}\n// Generates the format string that should be used to generate the title for the current date range.\n// Attempts to compute the most appropriate format if not explicitly specified with `titleFormat`.\nfunction buildTitleFormat(dateProfile) {\n  let {\n    currentRangeUnit\n  } = dateProfile;\n  if (currentRangeUnit === 'year') {\n    return {\n      year: 'numeric'\n    };\n  }\n  if (currentRangeUnit === 'month') {\n    return {\n      year: 'numeric',\n      month: 'long'\n    }; // like \"September 2014\"\n  }\n  let days = diffWholeDays(dateProfile.currentRange.start, dateProfile.currentRange.end);\n  if (days !== null && days > 1) {\n    // multi-day range. shorter, like \"Sep 9 - 10 2014\"\n    return {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    };\n  }\n  // one day. longer, like \"September 9 2014\"\n  return {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  };\n}\n\n// in future refactor, do the redux-style function(state=initial) for initial-state\n// also, whatever is happening in constructor, have it happen in action queue too\nclass CalendarDataManager {\n  constructor(props) {\n    this.computeCurrentViewData = memoize(this._computeCurrentViewData);\n    this.organizeRawLocales = memoize(organizeRawLocales);\n    this.buildLocale = memoize(buildLocale);\n    this.buildPluginHooks = buildBuildPluginHooks();\n    this.buildDateEnv = memoize(buildDateEnv$1);\n    this.buildTheme = memoize(buildTheme);\n    this.parseToolbars = memoize(parseToolbars);\n    this.buildViewSpecs = memoize(buildViewSpecs);\n    this.buildDateProfileGenerator = memoizeObjArg(buildDateProfileGenerator);\n    this.buildViewApi = memoize(buildViewApi);\n    this.buildViewUiProps = memoizeObjArg(buildViewUiProps);\n    this.buildEventUiBySource = memoize(buildEventUiBySource, isPropsEqual);\n    this.buildEventUiBases = memoize(buildEventUiBases);\n    this.parseContextBusinessHours = memoizeObjArg(parseContextBusinessHours);\n    this.buildTitle = memoize(buildTitle);\n    this.emitter = new Emitter();\n    this.actionRunner = new TaskRunner(this._handleAction.bind(this), this.updateData.bind(this));\n    this.currentCalendarOptionsInput = {};\n    this.currentCalendarOptionsRefined = {};\n    this.currentViewOptionsInput = {};\n    this.currentViewOptionsRefined = {};\n    this.currentCalendarOptionsRefiners = {};\n    this.optionsForRefining = [];\n    this.optionsForHandling = [];\n    this.getCurrentData = () => this.data;\n    this.dispatch = action => {\n      this.actionRunner.request(action); // protects against recursive calls to _handleAction\n    };\n    this.props = props;\n    this.actionRunner.pause();\n    let dynamicOptionOverrides = {};\n    let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n    let currentViewType = optionsData.calendarOptions.initialView || optionsData.pluginHooks.initialView;\n    let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n    // wire things up\n    // TODO: not DRY\n    props.calendarApi.currentDataManager = this;\n    this.emitter.setThisContext(props.calendarApi);\n    this.emitter.setOptions(currentViewData.options);\n    let currentDate = getInitialDate(optionsData.calendarOptions, optionsData.dateEnv);\n    let dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n    if (!rangeContainsMarker(dateProfile.activeRange, currentDate)) {\n      currentDate = dateProfile.currentRange.start;\n    }\n    let calendarContext = {\n      dateEnv: optionsData.dateEnv,\n      options: optionsData.calendarOptions,\n      pluginHooks: optionsData.pluginHooks,\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter: this.emitter,\n      getCurrentData: this.getCurrentData\n    };\n    // needs to be after setThisContext\n    for (let callback of optionsData.pluginHooks.contextInit) {\n      callback(calendarContext);\n    }\n    // NOT DRY\n    let eventSources = initEventSources(optionsData.calendarOptions, dateProfile, calendarContext);\n    let initialState = {\n      dynamicOptionOverrides,\n      currentViewType,\n      currentDate,\n      dateProfile,\n      businessHours: this.parseContextBusinessHours(calendarContext),\n      eventSources,\n      eventUiBases: {},\n      eventStore: createEmptyEventStore(),\n      renderableEventStore: createEmptyEventStore(),\n      dateSelection: null,\n      eventSelection: '',\n      eventDrag: null,\n      eventResize: null,\n      selectionConfig: this.buildViewUiProps(calendarContext).selectionConfig\n    };\n    let contextAndState = Object.assign(Object.assign({}, calendarContext), initialState);\n    for (let reducer of optionsData.pluginHooks.reducers) {\n      Object.assign(initialState, reducer(null, null, contextAndState));\n    }\n    if (computeIsLoading(initialState, calendarContext)) {\n      this.emitter.trigger('loading', true); // NOT DRY\n    }\n    this.state = initialState;\n    this.updateData();\n    this.actionRunner.resume();\n  }\n  resetOptions(optionOverrides, changedOptionNames) {\n    let {\n      props\n    } = this;\n    if (changedOptionNames === undefined) {\n      props.optionOverrides = optionOverrides;\n    } else {\n      props.optionOverrides = Object.assign(Object.assign({}, props.optionOverrides || {}), optionOverrides);\n      this.optionsForRefining.push(...changedOptionNames);\n    }\n    if (changedOptionNames === undefined || changedOptionNames.length) {\n      this.actionRunner.request({\n        type: 'NOTHING'\n      });\n    }\n  }\n  _handleAction(action) {\n    let {\n      props,\n      state,\n      emitter\n    } = this;\n    let dynamicOptionOverrides = reduceDynamicOptionOverrides(state.dynamicOptionOverrides, action);\n    let optionsData = this.computeOptionsData(props.optionOverrides, dynamicOptionOverrides, props.calendarApi);\n    let currentViewType = reduceViewType(state.currentViewType, action);\n    let currentViewData = this.computeCurrentViewData(currentViewType, optionsData, props.optionOverrides, dynamicOptionOverrides);\n    // wire things up\n    // TODO: not DRY\n    props.calendarApi.currentDataManager = this;\n    emitter.setThisContext(props.calendarApi);\n    emitter.setOptions(currentViewData.options);\n    let calendarContext = {\n      dateEnv: optionsData.dateEnv,\n      options: optionsData.calendarOptions,\n      pluginHooks: optionsData.pluginHooks,\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter,\n      getCurrentData: this.getCurrentData\n    };\n    let {\n      currentDate,\n      dateProfile\n    } = state;\n    if (this.data && this.data.dateProfileGenerator !== currentViewData.dateProfileGenerator) {\n      // hack\n      dateProfile = currentViewData.dateProfileGenerator.build(currentDate);\n    }\n    currentDate = reduceCurrentDate(currentDate, action);\n    dateProfile = reduceDateProfile(dateProfile, action, currentDate, currentViewData.dateProfileGenerator);\n    if (action.type === 'PREV' ||\n    // TODO: move this logic into DateProfileGenerator\n    action.type === 'NEXT' ||\n    // \"\n    !rangeContainsMarker(dateProfile.currentRange, currentDate)) {\n      currentDate = dateProfile.currentRange.start;\n    }\n    let eventSources = reduceEventSources(state.eventSources, action, dateProfile, calendarContext);\n    let eventStore = reduceEventStore(state.eventStore, action, eventSources, dateProfile, calendarContext);\n    let isEventsLoading = computeEventSourcesLoading(eventSources); // BAD. also called in this func in computeIsLoading\n    let renderableEventStore = isEventsLoading && !currentViewData.options.progressiveEventRendering ? state.renderableEventStore || eventStore :\n    // try from previous state\n    eventStore;\n    let {\n      eventUiSingleBase,\n      selectionConfig\n    } = this.buildViewUiProps(calendarContext); // will memoize obj\n    let eventUiBySource = this.buildEventUiBySource(eventSources);\n    let eventUiBases = this.buildEventUiBases(renderableEventStore.defs, eventUiSingleBase, eventUiBySource);\n    let newState = {\n      dynamicOptionOverrides,\n      currentViewType,\n      currentDate,\n      dateProfile,\n      eventSources,\n      eventStore,\n      renderableEventStore,\n      selectionConfig,\n      eventUiBases,\n      businessHours: this.parseContextBusinessHours(calendarContext),\n      dateSelection: reduceDateSelection(state.dateSelection, action),\n      eventSelection: reduceSelectedEvent(state.eventSelection, action),\n      eventDrag: reduceEventDrag(state.eventDrag, action),\n      eventResize: reduceEventResize(state.eventResize, action)\n    };\n    let contextAndState = Object.assign(Object.assign({}, calendarContext), newState);\n    for (let reducer of optionsData.pluginHooks.reducers) {\n      Object.assign(newState, reducer(state, action, contextAndState)); // give the OLD state, for old value\n    }\n    let wasLoading = computeIsLoading(state, calendarContext);\n    let isLoading = computeIsLoading(newState, calendarContext);\n    // TODO: use propSetHandlers in plugin system\n    if (!wasLoading && isLoading) {\n      emitter.trigger('loading', true);\n    } else if (wasLoading && !isLoading) {\n      emitter.trigger('loading', false);\n    }\n    this.state = newState;\n    if (props.onAction) {\n      props.onAction(action);\n    }\n  }\n  updateData() {\n    let {\n      props,\n      state\n    } = this;\n    let oldData = this.data;\n    let optionsData = this.computeOptionsData(props.optionOverrides, state.dynamicOptionOverrides, props.calendarApi);\n    let currentViewData = this.computeCurrentViewData(state.currentViewType, optionsData, props.optionOverrides, state.dynamicOptionOverrides);\n    let data = this.data = Object.assign(Object.assign(Object.assign({\n      viewTitle: this.buildTitle(state.dateProfile, currentViewData.options, optionsData.dateEnv),\n      calendarApi: props.calendarApi,\n      dispatch: this.dispatch,\n      emitter: this.emitter,\n      getCurrentData: this.getCurrentData\n    }, optionsData), currentViewData), state);\n    let changeHandlers = optionsData.pluginHooks.optionChangeHandlers;\n    let oldCalendarOptions = oldData && oldData.calendarOptions;\n    let newCalendarOptions = optionsData.calendarOptions;\n    if (oldCalendarOptions && oldCalendarOptions !== newCalendarOptions) {\n      if (oldCalendarOptions.timeZone !== newCalendarOptions.timeZone) {\n        // hack\n        state.eventSources = data.eventSources = reduceEventSourcesNewTimeZone(data.eventSources, state.dateProfile, data);\n        state.eventStore = data.eventStore = rezoneEventStoreDates(data.eventStore, oldData.dateEnv, data.dateEnv);\n        state.renderableEventStore = data.renderableEventStore = rezoneEventStoreDates(data.renderableEventStore, oldData.dateEnv, data.dateEnv);\n      }\n      for (let optionName in changeHandlers) {\n        if (this.optionsForHandling.indexOf(optionName) !== -1 || oldCalendarOptions[optionName] !== newCalendarOptions[optionName]) {\n          changeHandlers[optionName](newCalendarOptions[optionName], data);\n        }\n      }\n    }\n    this.optionsForHandling = [];\n    if (props.onData) {\n      props.onData(data);\n    }\n  }\n  computeOptionsData(optionOverrides, dynamicOptionOverrides, calendarApi) {\n    // TODO: blacklist options that are handled by optionChangeHandlers\n    if (!this.optionsForRefining.length && optionOverrides === this.stableOptionOverrides && dynamicOptionOverrides === this.stableDynamicOptionOverrides) {\n      return this.stableCalendarOptionsData;\n    }\n    let {\n      refinedOptions,\n      pluginHooks,\n      localeDefaults,\n      availableLocaleData,\n      extra\n    } = this.processRawCalendarOptions(optionOverrides, dynamicOptionOverrides);\n    warnUnknownOptions(extra);\n    let dateEnv = this.buildDateEnv(refinedOptions.timeZone, refinedOptions.locale, refinedOptions.weekNumberCalculation, refinedOptions.firstDay, refinedOptions.weekText, pluginHooks, availableLocaleData, refinedOptions.defaultRangeSeparator);\n    let viewSpecs = this.buildViewSpecs(pluginHooks.views, this.stableOptionOverrides, this.stableDynamicOptionOverrides, localeDefaults);\n    let theme = this.buildTheme(refinedOptions, pluginHooks);\n    let toolbarConfig = this.parseToolbars(refinedOptions, this.stableOptionOverrides, theme, viewSpecs, calendarApi);\n    return this.stableCalendarOptionsData = {\n      calendarOptions: refinedOptions,\n      pluginHooks,\n      dateEnv,\n      viewSpecs,\n      theme,\n      toolbarConfig,\n      localeDefaults,\n      availableRawLocales: availableLocaleData.map\n    };\n  }\n  // always called from behind a memoizer\n  processRawCalendarOptions(optionOverrides, dynamicOptionOverrides) {\n    let {\n      locales,\n      locale\n    } = mergeRawOptions([BASE_OPTION_DEFAULTS, optionOverrides, dynamicOptionOverrides]);\n    let availableLocaleData = this.organizeRawLocales(locales);\n    let availableRawLocales = availableLocaleData.map;\n    let localeDefaults = this.buildLocale(locale || availableLocaleData.defaultCode, availableRawLocales).options;\n    let pluginHooks = this.buildPluginHooks(optionOverrides.plugins || [], globalPlugins);\n    let refiners = this.currentCalendarOptionsRefiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n    let extra = {};\n    let raw = mergeRawOptions([BASE_OPTION_DEFAULTS, localeDefaults, optionOverrides, dynamicOptionOverrides]);\n    let refined = {};\n    let currentRaw = this.currentCalendarOptionsInput;\n    let currentRefined = this.currentCalendarOptionsRefined;\n    let anyChanges = false;\n    for (let optionName in raw) {\n      if (this.optionsForRefining.indexOf(optionName) === -1 && (raw[optionName] === currentRaw[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && optionName in currentRaw && COMPLEX_OPTION_COMPARATORS[optionName](currentRaw[optionName], raw[optionName]))) {\n        refined[optionName] = currentRefined[optionName];\n      } else if (refiners[optionName]) {\n        refined[optionName] = refiners[optionName](raw[optionName]);\n        anyChanges = true;\n      } else {\n        extra[optionName] = currentRaw[optionName];\n      }\n    }\n    if (anyChanges) {\n      this.currentCalendarOptionsInput = raw;\n      this.currentCalendarOptionsRefined = refined;\n      this.stableOptionOverrides = optionOverrides;\n      this.stableDynamicOptionOverrides = dynamicOptionOverrides;\n    }\n    this.optionsForHandling.push(...this.optionsForRefining);\n    this.optionsForRefining = [];\n    return {\n      rawOptions: this.currentCalendarOptionsInput,\n      refinedOptions: this.currentCalendarOptionsRefined,\n      pluginHooks,\n      availableLocaleData,\n      localeDefaults,\n      extra\n    };\n  }\n  _computeCurrentViewData(viewType, optionsData, optionOverrides, dynamicOptionOverrides) {\n    let viewSpec = optionsData.viewSpecs[viewType];\n    if (!viewSpec) {\n      throw new Error(`viewType \"${viewType}\" is not available. Please make sure you've loaded all neccessary plugins`);\n    }\n    let {\n      refinedOptions,\n      extra\n    } = this.processRawViewOptions(viewSpec, optionsData.pluginHooks, optionsData.localeDefaults, optionOverrides, dynamicOptionOverrides);\n    warnUnknownOptions(extra);\n    let dateProfileGenerator = this.buildDateProfileGenerator({\n      dateProfileGeneratorClass: viewSpec.optionDefaults.dateProfileGeneratorClass,\n      duration: viewSpec.duration,\n      durationUnit: viewSpec.durationUnit,\n      usesMinMaxTime: viewSpec.optionDefaults.usesMinMaxTime,\n      dateEnv: optionsData.dateEnv,\n      calendarApi: this.props.calendarApi,\n      slotMinTime: refinedOptions.slotMinTime,\n      slotMaxTime: refinedOptions.slotMaxTime,\n      showNonCurrentDates: refinedOptions.showNonCurrentDates,\n      dayCount: refinedOptions.dayCount,\n      dateAlignment: refinedOptions.dateAlignment,\n      dateIncrement: refinedOptions.dateIncrement,\n      hiddenDays: refinedOptions.hiddenDays,\n      weekends: refinedOptions.weekends,\n      nowInput: refinedOptions.now,\n      validRangeInput: refinedOptions.validRange,\n      visibleRangeInput: refinedOptions.visibleRange,\n      fixedWeekCount: refinedOptions.fixedWeekCount\n    });\n    let viewApi = this.buildViewApi(viewType, this.getCurrentData, optionsData.dateEnv);\n    return {\n      viewSpec,\n      options: refinedOptions,\n      dateProfileGenerator,\n      viewApi\n    };\n  }\n  processRawViewOptions(viewSpec, pluginHooks, localeDefaults, optionOverrides, dynamicOptionOverrides) {\n    let raw = mergeRawOptions([BASE_OPTION_DEFAULTS, viewSpec.optionDefaults, localeDefaults, optionOverrides, viewSpec.optionOverrides, dynamicOptionOverrides]);\n    let refiners = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, BASE_OPTION_REFINERS), CALENDAR_LISTENER_REFINERS), CALENDAR_OPTION_REFINERS), VIEW_OPTION_REFINERS), pluginHooks.listenerRefiners), pluginHooks.optionRefiners);\n    let refined = {};\n    let currentRaw = this.currentViewOptionsInput;\n    let currentRefined = this.currentViewOptionsRefined;\n    let anyChanges = false;\n    let extra = {};\n    for (let optionName in raw) {\n      if (raw[optionName] === currentRaw[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], currentRaw[optionName])) {\n        refined[optionName] = currentRefined[optionName];\n      } else {\n        if (raw[optionName] === this.currentCalendarOptionsInput[optionName] || COMPLEX_OPTION_COMPARATORS[optionName] && COMPLEX_OPTION_COMPARATORS[optionName](raw[optionName], this.currentCalendarOptionsInput[optionName])) {\n          if (optionName in this.currentCalendarOptionsRefined) {\n            // might be an \"extra\" prop\n            refined[optionName] = this.currentCalendarOptionsRefined[optionName];\n          }\n        } else if (refiners[optionName]) {\n          refined[optionName] = refiners[optionName](raw[optionName]);\n        } else {\n          extra[optionName] = raw[optionName];\n        }\n        anyChanges = true;\n      }\n    }\n    if (anyChanges) {\n      this.currentViewOptionsInput = raw;\n      this.currentViewOptionsRefined = refined;\n    }\n    return {\n      rawOptions: this.currentViewOptionsInput,\n      refinedOptions: this.currentViewOptionsRefined,\n      extra\n    };\n  }\n}\nfunction buildDateEnv$1(timeZone, explicitLocale, weekNumberCalculation, firstDay, weekText, pluginHooks, availableLocaleData, defaultSeparator) {\n  let locale = buildLocale(explicitLocale || availableLocaleData.defaultCode, availableLocaleData.map);\n  return new DateEnv({\n    calendarSystem: 'gregory',\n    timeZone,\n    namedTimeZoneImpl: pluginHooks.namedTimeZonedImpl,\n    locale,\n    weekNumberCalculation,\n    firstDay,\n    weekText,\n    cmdFormatter: pluginHooks.cmdFormatter,\n    defaultSeparator\n  });\n}\nfunction buildTheme(options, pluginHooks) {\n  let ThemeClass = pluginHooks.themeClasses[options.themeSystem] || StandardTheme;\n  return new ThemeClass(options);\n}\nfunction buildDateProfileGenerator(props) {\n  let DateProfileGeneratorClass = props.dateProfileGeneratorClass || DateProfileGenerator;\n  return new DateProfileGeneratorClass(props);\n}\nfunction buildViewApi(type, getCurrentData, dateEnv) {\n  return new ViewImpl(type, getCurrentData, dateEnv);\n}\nfunction buildEventUiBySource(eventSources) {\n  return mapHash(eventSources, eventSource => eventSource.ui);\n}\nfunction buildEventUiBases(eventDefs, eventUiSingleBase, eventUiBySource) {\n  let eventUiBases = {\n    '': eventUiSingleBase\n  };\n  for (let defId in eventDefs) {\n    let def = eventDefs[defId];\n    if (def.sourceId && eventUiBySource[def.sourceId]) {\n      eventUiBases[defId] = eventUiBySource[def.sourceId];\n    }\n  }\n  return eventUiBases;\n}\nfunction buildViewUiProps(calendarContext) {\n  let {\n    options\n  } = calendarContext;\n  return {\n    eventUiSingleBase: createEventUi({\n      display: options.eventDisplay,\n      editable: options.editable,\n      startEditable: options.eventStartEditable,\n      durationEditable: options.eventDurationEditable,\n      constraint: options.eventConstraint,\n      overlap: typeof options.eventOverlap === 'boolean' ? options.eventOverlap : undefined,\n      allow: options.eventAllow,\n      backgroundColor: options.eventBackgroundColor,\n      borderColor: options.eventBorderColor,\n      textColor: options.eventTextColor,\n      color: options.eventColor\n      // classNames: options.eventClassNames // render hook will handle this\n    }, calendarContext),\n    selectionConfig: createEventUi({\n      constraint: options.selectConstraint,\n      overlap: typeof options.selectOverlap === 'boolean' ? options.selectOverlap : undefined,\n      allow: options.selectAllow\n    }, calendarContext)\n  };\n}\nfunction computeIsLoading(state, context) {\n  for (let isLoadingFunc of context.pluginHooks.isLoadingFuncs) {\n    if (isLoadingFunc(state)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction parseContextBusinessHours(calendarContext) {\n  return parseBusinessHours(calendarContext.options.businessHours, calendarContext);\n}\nfunction warnUnknownOptions(options, viewName) {\n  for (let optionName in options) {\n    console.warn(`Unknown option '${optionName}'` + (viewName ? ` for view '${viewName}'` : ''));\n  }\n}\nclass ToolbarSection extends BaseComponent {\n  render() {\n    let children = this.props.widgetGroups.map(widgetGroup => this.renderWidgetGroup(widgetGroup));\n    return createElement('div', {\n      className: 'fc-toolbar-chunk'\n    }, ...children);\n  }\n  renderWidgetGroup(widgetGroup) {\n    let {\n      props\n    } = this;\n    let {\n      theme\n    } = this.context;\n    let children = [];\n    let isOnlyButtons = true;\n    for (let widget of widgetGroup) {\n      let {\n        buttonName,\n        buttonClick,\n        buttonText,\n        buttonIcon,\n        buttonHint\n      } = widget;\n      if (buttonName === 'title') {\n        isOnlyButtons = false;\n        children.push(createElement(\"h2\", {\n          className: \"fc-toolbar-title\",\n          id: props.titleId\n        }, props.title));\n      } else {\n        let isPressed = buttonName === props.activeButton;\n        let isDisabled = !props.isTodayEnabled && buttonName === 'today' || !props.isPrevEnabled && buttonName === 'prev' || !props.isNextEnabled && buttonName === 'next';\n        let buttonClasses = [`fc-${buttonName}-button`, theme.getClass('button')];\n        if (isPressed) {\n          buttonClasses.push(theme.getClass('buttonActive'));\n        }\n        children.push(createElement(\"button\", {\n          type: \"button\",\n          title: typeof buttonHint === 'function' ? buttonHint(props.navUnit) : buttonHint,\n          disabled: isDisabled,\n          \"aria-pressed\": isPressed,\n          className: buttonClasses.join(' '),\n          onClick: buttonClick\n        }, buttonText || (buttonIcon ? createElement(\"span\", {\n          className: buttonIcon,\n          role: \"img\"\n        }) : '')));\n      }\n    }\n    if (children.length > 1) {\n      let groupClassName = isOnlyButtons && theme.getClass('buttonGroup') || '';\n      return createElement('div', {\n        className: groupClassName\n      }, ...children);\n    }\n    return children[0];\n  }\n}\nclass Toolbar extends BaseComponent {\n  render() {\n    let {\n      model,\n      extraClassName\n    } = this.props;\n    let forceLtr = false;\n    let startContent;\n    let endContent;\n    let sectionWidgets = model.sectionWidgets;\n    let centerContent = sectionWidgets.center;\n    if (sectionWidgets.left) {\n      forceLtr = true;\n      startContent = sectionWidgets.left;\n    } else {\n      startContent = sectionWidgets.start;\n    }\n    if (sectionWidgets.right) {\n      forceLtr = true;\n      endContent = sectionWidgets.right;\n    } else {\n      endContent = sectionWidgets.end;\n    }\n    let classNames = [extraClassName || '', 'fc-toolbar', forceLtr ? 'fc-toolbar-ltr' : ''];\n    return createElement(\"div\", {\n      className: classNames.join(' ')\n    }, this.renderSection('start', startContent || []), this.renderSection('center', centerContent || []), this.renderSection('end', endContent || []));\n  }\n  renderSection(key, widgetGroups) {\n    let {\n      props\n    } = this;\n    return createElement(ToolbarSection, {\n      key: key,\n      widgetGroups: widgetGroups,\n      title: props.title,\n      navUnit: props.navUnit,\n      activeButton: props.activeButton,\n      isTodayEnabled: props.isTodayEnabled,\n      isPrevEnabled: props.isPrevEnabled,\n      isNextEnabled: props.isNextEnabled,\n      titleId: props.titleId\n    });\n  }\n}\nclass ViewHarness extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      availableWidth: null\n    };\n    this.handleEl = el => {\n      this.el = el;\n      setRef(this.props.elRef, el);\n      this.updateAvailableWidth();\n    };\n    this.handleResize = () => {\n      this.updateAvailableWidth();\n    };\n  }\n  render() {\n    let {\n      props,\n      state\n    } = this;\n    let {\n      aspectRatio\n    } = props;\n    let classNames = ['fc-view-harness', aspectRatio || props.liquid || props.height ? 'fc-view-harness-active' // harness controls the height\n    : 'fc-view-harness-passive' // let the view do the height\n    ];\n    let height = '';\n    let paddingBottom = '';\n    if (aspectRatio) {\n      if (state.availableWidth !== null) {\n        height = state.availableWidth / aspectRatio;\n      } else {\n        // while waiting to know availableWidth, we can't set height to *zero*\n        // because will cause lots of unnecessary scrollbars within scrollgrid.\n        // BETTER: don't start rendering ANYTHING yet until we know container width\n        // NOTE: why not always use paddingBottom? Causes height oscillation (issue 5606)\n        paddingBottom = `${1 / aspectRatio * 100}%`;\n      }\n    } else {\n      height = props.height || '';\n    }\n    return createElement(\"div\", {\n      \"aria-labelledby\": props.labeledById,\n      ref: this.handleEl,\n      className: classNames.join(' '),\n      style: {\n        height,\n        paddingBottom\n      }\n    }, props.children);\n  }\n  componentDidMount() {\n    this.context.addResizeHandler(this.handleResize);\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleResize);\n  }\n  updateAvailableWidth() {\n    if (this.el &&\n    // needed. but why?\n    this.props.aspectRatio // aspectRatio is the only height setting that needs availableWidth\n    ) {\n      this.setState({\n        availableWidth: this.el.offsetWidth\n      });\n    }\n  }\n}\n\n/*\nDetects when the user clicks on an event within a DateComponent\n*/\nclass EventClicking extends Interaction {\n  constructor(settings) {\n    super(settings);\n    this.handleSegClick = (ev, segEl) => {\n      let {\n        component\n      } = this;\n      let {\n        context\n      } = component;\n      let seg = getElSeg(segEl);\n      if (seg &&\n      // might be the <div> surrounding the more link\n      component.isValidSegDownEl(ev.target)) {\n        // our way to simulate a link click for elements that can't be <a> tags\n        // grab before trigger fired in case trigger trashes DOM thru rerendering\n        let hasUrlContainer = elementClosest(ev.target, '.fc-event-forced-url');\n        let url = hasUrlContainer ? hasUrlContainer.querySelector('a[href]').href : '';\n        context.emitter.trigger('eventClick', {\n          el: segEl,\n          event: new EventImpl(component.context, seg.eventRange.def, seg.eventRange.instance),\n          jsEvent: ev,\n          view: context.viewApi\n        });\n        if (url && !ev.defaultPrevented) {\n          window.location.href = url;\n        }\n      }\n    };\n    this.destroy = listenBySelector(settings.el, 'click', '.fc-event',\n    // on both fg and bg events\n    this.handleSegClick);\n  }\n}\n\n/*\nTriggers events and adds/removes core classNames when the user's pointer\nenters/leaves event-elements of a component.\n*/\nclass EventHovering extends Interaction {\n  constructor(settings) {\n    super(settings);\n    // for simulating an eventMouseLeave when the event el is destroyed while mouse is over it\n    this.handleEventElRemove = el => {\n      if (el === this.currentSegEl) {\n        this.handleSegLeave(null, this.currentSegEl);\n      }\n    };\n    this.handleSegEnter = (ev, segEl) => {\n      if (getElSeg(segEl)) {\n        // TODO: better way to make sure not hovering over more+ link or its wrapper\n        this.currentSegEl = segEl;\n        this.triggerEvent('eventMouseEnter', ev, segEl);\n      }\n    };\n    this.handleSegLeave = (ev, segEl) => {\n      if (this.currentSegEl) {\n        this.currentSegEl = null;\n        this.triggerEvent('eventMouseLeave', ev, segEl);\n      }\n    };\n    this.removeHoverListeners = listenToHoverBySelector(settings.el, '.fc-event',\n    // on both fg and bg events\n    this.handleSegEnter, this.handleSegLeave);\n  }\n  destroy() {\n    this.removeHoverListeners();\n  }\n  triggerEvent(publicEvName, ev, segEl) {\n    let {\n      component\n    } = this;\n    let {\n      context\n    } = component;\n    let seg = getElSeg(segEl);\n    if (!ev || component.isValidSegDownEl(ev.target)) {\n      context.emitter.trigger(publicEvName, {\n        el: segEl,\n        event: new EventImpl(context, seg.eventRange.def, seg.eventRange.instance),\n        jsEvent: ev,\n        view: context.viewApi\n      });\n    }\n  }\n}\nclass CalendarContent extends PureComponent {\n  constructor() {\n    super(...arguments);\n    this.buildViewContext = memoize(buildViewContext);\n    this.buildViewPropTransformers = memoize(buildViewPropTransformers);\n    this.buildToolbarProps = memoize(buildToolbarProps);\n    this.headerRef = createRef();\n    this.footerRef = createRef();\n    this.interactionsStore = {};\n    // eslint-disable-next-line\n    this.state = {\n      viewLabelId: getUniqueDomId()\n    };\n    // Component Registration\n    // -----------------------------------------------------------------------------------------------------------------\n    this.registerInteractiveComponent = (component, settingsInput) => {\n      let settings = parseInteractionSettings(component, settingsInput);\n      let DEFAULT_INTERACTIONS = [EventClicking, EventHovering];\n      let interactionClasses = DEFAULT_INTERACTIONS.concat(this.props.pluginHooks.componentInteractions);\n      let interactions = interactionClasses.map(TheInteractionClass => new TheInteractionClass(settings));\n      this.interactionsStore[component.uid] = interactions;\n      interactionSettingsStore[component.uid] = settings;\n    };\n    this.unregisterInteractiveComponent = component => {\n      let listeners = this.interactionsStore[component.uid];\n      if (listeners) {\n        for (let listener of listeners) {\n          listener.destroy();\n        }\n        delete this.interactionsStore[component.uid];\n      }\n      delete interactionSettingsStore[component.uid];\n    };\n    // Resizing\n    // -----------------------------------------------------------------------------------------------------------------\n    this.resizeRunner = new DelayedRunner(() => {\n      this.props.emitter.trigger('_resize', true); // should window resizes be considered \"forced\" ?\n      this.props.emitter.trigger('windowResize', {\n        view: this.props.viewApi\n      });\n    });\n    this.handleWindowResize = ev => {\n      let {\n        options\n      } = this.props;\n      if (options.handleWindowResize && ev.target === window // avoid jqui events\n      ) {\n        this.resizeRunner.request(options.windowResizeDelay);\n      }\n    };\n  }\n  /*\n  renders INSIDE of an outer div\n  */\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      toolbarConfig,\n      options\n    } = props;\n    let toolbarProps = this.buildToolbarProps(props.viewSpec, props.dateProfile, props.dateProfileGenerator, props.currentDate, getNow(props.options.now, props.dateEnv),\n    // TODO: use NowTimer????\n    props.viewTitle);\n    let viewVGrow = false;\n    let viewHeight = '';\n    let viewAspectRatio;\n    if (props.isHeightAuto || props.forPrint) {\n      viewHeight = '';\n    } else if (options.height != null) {\n      viewVGrow = true;\n    } else if (options.contentHeight != null) {\n      viewHeight = options.contentHeight;\n    } else {\n      viewAspectRatio = Math.max(options.aspectRatio, 0.5); // prevent from getting too tall\n    }\n    let viewContext = this.buildViewContext(props.viewSpec, props.viewApi, props.options, props.dateProfileGenerator, props.dateEnv, props.theme, props.pluginHooks, props.dispatch, props.getCurrentData, props.emitter, props.calendarApi, this.registerInteractiveComponent, this.unregisterInteractiveComponent);\n    let viewLabelId = toolbarConfig.header && toolbarConfig.header.hasTitle ? this.state.viewLabelId : undefined;\n    return createElement(ViewContextType.Provider, {\n      value: viewContext\n    }, toolbarConfig.header && createElement(Toolbar, Object.assign({\n      ref: this.headerRef,\n      extraClassName: \"fc-header-toolbar\",\n      model: toolbarConfig.header,\n      titleId: viewLabelId\n    }, toolbarProps)), createElement(ViewHarness, {\n      liquid: viewVGrow,\n      height: viewHeight,\n      aspectRatio: viewAspectRatio,\n      labeledById: viewLabelId\n    }, this.renderView(props), this.buildAppendContent()), toolbarConfig.footer && createElement(Toolbar, Object.assign({\n      ref: this.footerRef,\n      extraClassName: \"fc-footer-toolbar\",\n      model: toolbarConfig.footer,\n      titleId: \"\"\n    }, toolbarProps)));\n  }\n  componentDidMount() {\n    let {\n      props\n    } = this;\n    this.calendarInteractions = props.pluginHooks.calendarInteractions.map(CalendarInteractionClass => new CalendarInteractionClass(props));\n    window.addEventListener('resize', this.handleWindowResize);\n    let {\n      propSetHandlers\n    } = props.pluginHooks;\n    for (let propName in propSetHandlers) {\n      propSetHandlers[propName](props[propName], props);\n    }\n  }\n  componentDidUpdate(prevProps) {\n    let {\n      props\n    } = this;\n    let {\n      propSetHandlers\n    } = props.pluginHooks;\n    for (let propName in propSetHandlers) {\n      if (props[propName] !== prevProps[propName]) {\n        propSetHandlers[propName](props[propName], props);\n      }\n    }\n  }\n  componentWillUnmount() {\n    window.removeEventListener('resize', this.handleWindowResize);\n    this.resizeRunner.clear();\n    for (let interaction of this.calendarInteractions) {\n      interaction.destroy();\n    }\n    this.props.emitter.trigger('_unmount');\n  }\n  buildAppendContent() {\n    let {\n      props\n    } = this;\n    let children = props.pluginHooks.viewContainerAppends.map(buildAppendContent => buildAppendContent(props));\n    return createElement(Fragment, {}, ...children);\n  }\n  renderView(props) {\n    let {\n      pluginHooks\n    } = props;\n    let {\n      viewSpec\n    } = props;\n    let viewProps = {\n      dateProfile: props.dateProfile,\n      businessHours: props.businessHours,\n      eventStore: props.renderableEventStore,\n      eventUiBases: props.eventUiBases,\n      dateSelection: props.dateSelection,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      isHeightAuto: props.isHeightAuto,\n      forPrint: props.forPrint\n    };\n    let transformers = this.buildViewPropTransformers(pluginHooks.viewPropsTransformers);\n    for (let transformer of transformers) {\n      Object.assign(viewProps, transformer.transform(viewProps, props));\n    }\n    let ViewComponent = viewSpec.component;\n    return createElement(ViewComponent, Object.assign({}, viewProps));\n  }\n}\nfunction buildToolbarProps(viewSpec, dateProfile, dateProfileGenerator, currentDate, now, title) {\n  // don't force any date-profiles to valid date profiles (the `false`) so that we can tell if it's invalid\n  let todayInfo = dateProfileGenerator.build(now, undefined, false); // TODO: need `undefined` or else INFINITE LOOP for some reason\n  let prevInfo = dateProfileGenerator.buildPrev(dateProfile, currentDate, false);\n  let nextInfo = dateProfileGenerator.buildNext(dateProfile, currentDate, false);\n  return {\n    title,\n    activeButton: viewSpec.type,\n    navUnit: viewSpec.singleUnit,\n    isTodayEnabled: todayInfo.isValid && !rangeContainsMarker(dateProfile.currentRange, now),\n    isPrevEnabled: prevInfo.isValid,\n    isNextEnabled: nextInfo.isValid\n  };\n}\n// Plugin\n// -----------------------------------------------------------------------------------------------------------------\nfunction buildViewPropTransformers(theClasses) {\n  return theClasses.map(TheClass => new TheClass());\n}\nclass Calendar extends CalendarImpl {\n  constructor(el, optionOverrides = {}) {\n    super();\n    this.isRendering = false;\n    this.isRendered = false;\n    this.currentClassNames = [];\n    this.customContentRenderId = 0;\n    this.handleAction = action => {\n      // actions we know we want to render immediately\n      switch (action.type) {\n        case 'SET_EVENT_DRAG':\n        case 'SET_EVENT_RESIZE':\n          this.renderRunner.tryDrain();\n      }\n    };\n    this.handleData = data => {\n      this.currentData = data;\n      this.renderRunner.request(data.calendarOptions.rerenderDelay);\n    };\n    this.handleRenderRequest = () => {\n      if (this.isRendering) {\n        this.isRendered = true;\n        let {\n          currentData\n        } = this;\n        flushSync(() => {\n          render(createElement(CalendarRoot, {\n            options: currentData.calendarOptions,\n            theme: currentData.theme,\n            emitter: currentData.emitter\n          }, (classNames, height, isHeightAuto, forPrint) => {\n            this.setClassNames(classNames);\n            this.setHeight(height);\n            return createElement(RenderId.Provider, {\n              value: this.customContentRenderId\n            }, createElement(CalendarContent, Object.assign({\n              isHeightAuto: isHeightAuto,\n              forPrint: forPrint\n            }, currentData)));\n          }), this.el);\n        });\n      } else if (this.isRendered) {\n        this.isRendered = false;\n        render(null, this.el);\n        this.setClassNames([]);\n        this.setHeight('');\n      }\n    };\n    ensureElHasStyles(el);\n    this.el = el;\n    this.renderRunner = new DelayedRunner(this.handleRenderRequest);\n    new CalendarDataManager({\n      optionOverrides,\n      calendarApi: this,\n      onAction: this.handleAction,\n      onData: this.handleData\n    });\n  }\n  render() {\n    let wasRendering = this.isRendering;\n    if (!wasRendering) {\n      this.isRendering = true;\n    } else {\n      this.customContentRenderId += 1;\n    }\n    this.renderRunner.request();\n    if (wasRendering) {\n      this.updateSize();\n    }\n  }\n  destroy() {\n    if (this.isRendering) {\n      this.isRendering = false;\n      this.renderRunner.request();\n    }\n  }\n  updateSize() {\n    flushSync(() => {\n      super.updateSize();\n    });\n  }\n  batchRendering(func) {\n    this.renderRunner.pause('batchRendering');\n    func();\n    this.renderRunner.resume('batchRendering');\n  }\n  pauseRendering() {\n    this.renderRunner.pause('pauseRendering');\n  }\n  resumeRendering() {\n    this.renderRunner.resume('pauseRendering', true);\n  }\n  resetOptions(optionOverrides, changedOptionNames) {\n    this.currentDataManager.resetOptions(optionOverrides, changedOptionNames);\n  }\n  setClassNames(classNames) {\n    if (!isArraysEqual(classNames, this.currentClassNames)) {\n      let {\n        classList\n      } = this.el;\n      for (let className of this.currentClassNames) {\n        classList.remove(className);\n      }\n      for (let className of classNames) {\n        classList.add(className);\n      }\n      this.currentClassNames = classNames;\n    }\n  }\n  setHeight(height) {\n    applyStyleProp(this.el, 'height', height);\n  }\n}\nfunction formatDate(dateInput, options = {}) {\n  let dateEnv = buildDateEnv(options);\n  let formatter = createFormatter(options);\n  let dateMeta = dateEnv.createMarkerMeta(dateInput);\n  if (!dateMeta) {\n    // TODO: warning?\n    return '';\n  }\n  return dateEnv.format(dateMeta.marker, formatter, {\n    forcedTzo: dateMeta.forcedTzo\n  });\n}\nfunction formatRange(startInput, endInput, options) {\n  let dateEnv = buildDateEnv(typeof options === 'object' && options ? options : {}); // pass in if non-null object\n  let formatter = createFormatter(options);\n  let startMeta = dateEnv.createMarkerMeta(startInput);\n  let endMeta = dateEnv.createMarkerMeta(endInput);\n  if (!startMeta || !endMeta) {\n    // TODO: warning?\n    return '';\n  }\n  return dateEnv.formatRange(startMeta.marker, endMeta.marker, formatter, {\n    forcedStartTzo: startMeta.forcedTzo,\n    forcedEndTzo: endMeta.forcedTzo,\n    isEndExclusive: options.isEndExclusive,\n    defaultSeparator: BASE_OPTION_DEFAULTS.defaultRangeSeparator\n  });\n}\n// TODO: more DRY and optimized\nfunction buildDateEnv(settings) {\n  let locale = buildLocale(settings.locale || 'en', organizeRawLocales([]).map); // TODO: don't hardcode 'en' everywhere\n  return new DateEnv(Object.assign(Object.assign({\n    timeZone: BASE_OPTION_DEFAULTS.timeZone,\n    calendarSystem: 'gregory'\n  }, settings), {\n    locale\n  }));\n}\n\n// HELPERS\n/*\nif nextDayThreshold is specified, slicing is done in an all-day fashion.\nyou can get nextDayThreshold from context.nextDayThreshold\n*/\nfunction sliceEvents(props, allDay) {\n  return sliceEventStore(props.eventStore, props.eventUiBases, props.dateProfile.activeRange, allDay ? props.nextDayThreshold : null).fg;\n}\nconst version = '6.1.15';\nexport { Calendar, createPlugin, formatDate, formatRange, globalLocales, globalPlugins, sliceEvents, version };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}