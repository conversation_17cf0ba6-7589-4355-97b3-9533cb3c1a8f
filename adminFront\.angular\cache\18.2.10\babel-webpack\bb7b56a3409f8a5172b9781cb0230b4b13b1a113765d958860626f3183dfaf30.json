{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction TemplateViewerComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_15_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"input\", 28);\n    i0.ɵɵlistener(\"change\", function TemplateViewerComponent_div_15_div_14_Template_input_change_1_listener($event) {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      return i0.ɵɵresetView(item_r5.selected = $event.target.checked);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"name\", \"item\", i_r6, \"\");\n    i0.ɵɵproperty(\"checked\", item_r5.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r5.CRequirement, \" (\", item_r5.CGroupName, \") \");\n  }\n}\nfunction TemplateViewerComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"form\", 21);\n    i0.ɵɵlistener(\"ngSubmit\", function TemplateViewerComponent_div_15_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createTemplate());\n    });\n    i0.ɵɵelementStart(2, \"div\", 22)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"input\", 23);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_15_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplateName, $event) || (ctx_r1.newTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 22)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_div_15_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newTemplateDesc, $event) || (ctx_r1.newTemplateDesc = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 22)(11, \"label\");\n    i0.ɵɵtext(12, \"\\u9078\\u64C7\\u8981\\u5B58\\u6210\\u6A21\\u677F\\u7684\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 25);\n    i0.ɵɵtemplate(14, TemplateViewerComponent_div_15_div_14_Template, 3, 5, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 26);\n    i0.ɵɵtext(16, \"\\u5132\\u5B58\\u6A21\\u677F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_15_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showCreateTemplate = false);\n    });\n    i0.ɵɵtext(18, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplateName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newTemplateDesc);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.sharedData);\n  }\n}\nfunction TemplateViewerComponent_tr_27_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_27_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const tpl_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r8.TemplateID && ctx_r1.onDeleteTemplate(tpl_r8.TemplateID));\n    });\n    i0.ɵɵtext(1, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_27_Template_button_click_6_listener() {\n      const tpl_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r8));\n    });\n    i0.ɵɵtext(7, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, TemplateViewerComponent_tr_27_button_8_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tpl_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r8.TemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r8.Description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", tpl_r8.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 32);\n    i0.ɵɵtext(2, \"\\u66AB\\u7121\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_div_29_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", detail_r11.FieldName, \": \", detail_r11.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_29_li_4_Template, 2, 2, \"li\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_29_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵtext(6, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n  }\n  ngOnInit() {\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 新增模板\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\",\n        templateDetails: \"templateDetails\",\n        sharedData: \"sharedData\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\",\n        saveTemplate: \"saveTemplate\",\n        deleteTemplate: \"deleteTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 30,\n      vars: 6,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"mb-3 p-2 border rounded bg-light\", 4, \"ngIf\"], [1, \"template-list\"], [1, \"table\", \"table-bordered\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"class\", \"mt-3 p-2 border rounded bg-white\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"mb-3\", \"p-2\", \"border\", \"rounded\", \"bg-light\"], [3, \"ngSubmit\"], [1, \"form-group\", \"mb-2\"], [\"type\", \"text\", \"name\", \"templateName\", \"required\", \"\", \"maxlength\", \"30\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"templateDesc\", \"maxlength\", \"100\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [2, \"max-height\", \"120px\", \"overflow\", \"auto\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"mr-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\", \"name\"], [1, \"btn\", \"btn-info\", \"btn-sm\", \"mr-1\", 3, \"click\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"], [1, \"mt-3\", \"p-2\", \"border\", \"rounded\", \"bg-white\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_10_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, TemplateViewerComponent_button_14_Template, 2, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(15, TemplateViewerComponent_div_15_Template, 19, 3, \"div\", 13);\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"table\", 15)(18, \"thead\")(19, \"tr\")(20, \"th\");\n          i0.ɵɵtext(21, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\");\n          i0.ɵɵtext(23, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\");\n          i0.ɵɵtext(25, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"tbody\");\n          i0.ɵɵtemplate(27, TemplateViewerComponent_tr_27_Template, 9, 3, \"tr\", 16)(28, TemplateViewerComponent_tr_28_Template, 3, 0, \"tr\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(29, TemplateViewerComponent_div_29_Template, 7, 2, \"div\", 18);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showCreateTemplate);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.templates || ctx.templates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MaxLengthValidator, i2.NgModel, i2.NgForm],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_14_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "TemplateViewerComponent_div_15_div_14_Template_input_change_1_listener", "$event", "item_r5", "_r4", "$implicit", "selected", "target", "checked", "ɵɵtext", "ɵɵadvance", "ɵɵpropertyInterpolate1", "i_r6", "ɵɵproperty", "ɵɵtextInterpolate2", "CRequirement", "CGroupName", "TemplateViewerComponent_div_15_Template_form_ngSubmit_1_listener", "_r3", "createTemplate", "ɵɵtwoWayListener", "TemplateViewerComponent_div_15_Template_input_ngModelChange_5_listener", "ɵɵtwoWayBindingSet", "newTemplateName", "TemplateViewerComponent_div_15_Template_input_ngModelChange_9_listener", "newTemplateDesc", "ɵɵtemplate", "TemplateViewerComponent_div_15_div_14_Template", "TemplateViewerComponent_div_15_Template_button_click_17_listener", "showCreateTemplate", "ɵɵtwoWayProperty", "sharedData", "TemplateViewerComponent_tr_27_button_8_Template_button_click_0_listener", "_r9", "tpl_r8", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_27_Template_button_click_6_listener", "_r7", "onSelectTemplate", "TemplateViewerComponent_tr_27_button_8_Template", "ɵɵtextInterpolate", "TemplateName", "Description", "detail_r11", "FieldName", "FieldValue", "TemplateViewerComponent_div_29_li_4_Template", "TemplateViewerComponent_div_29_Template_button_click_5_listener", "_r10", "closeTemplateDetail", "ɵɵtextInterpolate1", "selectedTemplate", "currentTemplateDetails", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "searchKeyword", "filteredTemplates", "ngOnInit", "updateFilteredTemplates", "ngOnChanges", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "x", "length", "alert", "details", "map", "RefID", "ID", "CRequirementID", "ModuleType", "emit", "for<PERSON>ach", "onAddTemplate", "templateID", "confirm", "d", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "TemplateViewerComponent_Template_input_ngModelChange_10_listener", "TemplateViewerComponent_Template_input_input_10_listener", "TemplateViewerComponent_Template_input_keyup_enter_10_listener", "TemplateViewerComponent_Template_button_click_12_listener", "TemplateViewerComponent_button_14_Template", "TemplateViewerComponent_div_15_Template", "TemplateViewerComponent_tr_27_Template", "TemplateViewerComponent_tr_28_Template", "TemplateViewerComponent_div_29_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  ngOnInit() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({ template, details });\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 新增模板\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n      <h5 class=\"mb-0\">模板管理</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 建立模板區塊 -->\r\n  <div *ngIf=\"showCreateTemplate\" class=\"mb-3 p-2 border rounded bg-light\">\r\n    <form (ngSubmit)=\"createTemplate()\">\r\n      <div class=\"form-group mb-2\">\r\n        <label>模板名稱</label>\r\n        <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplateName\" name=\"templateName\" required\r\n          maxlength=\"30\">\r\n      </div>\r\n      <div class=\"form-group mb-2\">\r\n        <label>模板描述</label>\r\n        <input type=\"text\" class=\"form-control\" [(ngModel)]=\"newTemplateDesc\" name=\"templateDesc\" maxlength=\"100\">\r\n      </div>\r\n      <div class=\"form-group mb-2\">\r\n        <label>選擇要存成模板的資料</label>\r\n        <div style=\"max-height:120px;overflow:auto;\">\r\n          <div *ngFor=\"let item of sharedData; let i = index\">\r\n            <input type=\"checkbox\" [checked]=\"item.selected\" (change)=\"item.selected = $any($event.target).checked\"\r\n              name=\"item{{i}}\"> {{item.CRequirement}}\r\n            ({{item.CGroupName}})\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button class=\"btn btn-primary btn-sm mr-2\" type=\"submit\">儲存模板</button>\r\n      <button class=\"btn btn-secondary btn-sm\" type=\"button\" (click)=\"showCreateTemplate = false\">取消</button>\r\n    </form>\r\n  </div>\r\n\r\n  <div class=\"template-list\">\r\n    <table class=\"table table-bordered table-hover\">\r\n      <thead>\r\n        <tr>\r\n          <th>模板名稱</th>\r\n          <th>描述</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let tpl of templates\">\r\n          <td>{{ tpl.TemplateName }}</td>\r\n          <td>{{ tpl.Description }}</td>\r\n          <td>\r\n            <button class=\"btn btn-info btn-sm mr-1\" (click)=\"onSelectTemplate(tpl)\">查看</button>\r\n            <button class=\"btn btn-danger btn-sm\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n              *ngIf=\"tpl.TemplateID\">刪除</button>\r\n          </td>\r\n        </tr>\r\n        <tr *ngIf=\"!templates || templates.length === 0\">\r\n          <td colspan=\"3\" class=\"text-center\">暫無模板</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n  </div>\r\n\r\n  <!-- 查看模板細節 -->\r\n  <div *ngIf=\"selectedTemplate\" class=\"mt-3 p-2 border rounded bg-white\">\r\n    <h6>模板細節：{{selectedTemplate!.TemplateName}}</h6>\r\n    <ul>\r\n      <li *ngFor=\"let detail of currentTemplateDetails\">\r\n        {{detail.FieldName}}: {{detail.FieldValue}}\r\n      </li>\r\n    </ul>\r\n    <button class=\"btn btn-secondary btn-sm\" (click)=\"closeTemplateDetail()\">關閉</button>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;ICgBlCC,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAsBPX,EADF,CAAAC,cAAA,UAAoD,gBAE/B;IAD8BD,EAAA,CAAAE,UAAA,oBAAAU,uEAAAC,MAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA,EAAAC,SAAA;MAAA,OAAAhB,EAAA,CAAAQ,WAAA,CAAAM,OAAA,CAAAG,QAAA,GAAAJ,MAAA,CAAAK,MAAA,CAAAC,OAAA;IAAA,EAAsD;IAAvGnB,EAAA,CAAAW,YAAA,EACmB;IAACX,EAAA,CAAAoB,MAAA,GAEtB;IAAApB,EAAA,CAAAW,YAAA,EAAM;;;;;IAFFX,EAAA,CAAAqB,SAAA,EAAgB;IAAhBrB,EAAA,CAAAsB,sBAAA,iBAAAC,IAAA,KAAgB;IADKvB,EAAA,CAAAwB,UAAA,YAAAV,OAAA,CAAAG,QAAA,CAAyB;IAC5BjB,EAAA,CAAAqB,SAAA,EAEtB;IAFsBrB,EAAA,CAAAyB,kBAAA,MAAAX,OAAA,CAAAY,YAAA,QAAAZ,OAAA,CAAAa,UAAA,OAEtB;;;;;;IAjBN3B,EADF,CAAAC,cAAA,cAAyE,eACnC;IAA9BD,EAAA,CAAAE,UAAA,sBAAA0B,iEAAA;MAAA5B,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IAE/B9B,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAoB,MAAA,+BAAI;IAAApB,EAAA,CAAAW,YAAA,EAAQ;IACnBX,EAAA,CAAAC,cAAA,gBACiB;IADuBD,EAAA,CAAA+B,gBAAA,2BAAAC,uEAAAnB,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAA4B,eAAA,EAAArB,MAAA,MAAAP,MAAA,CAAA4B,eAAA,GAAArB,MAAA;MAAA,OAAAb,EAAA,CAAAQ,WAAA,CAAAK,MAAA;IAAA,EAA6B;IAEvEb,EAFE,CAAAW,YAAA,EACiB,EACb;IAEJX,EADF,CAAAC,cAAA,cAA6B,YACpB;IAAAD,EAAA,CAAAoB,MAAA,+BAAI;IAAApB,EAAA,CAAAW,YAAA,EAAQ;IACnBX,EAAA,CAAAC,cAAA,gBAA0G;IAAlED,EAAA,CAAA+B,gBAAA,2BAAAI,uEAAAtB,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAiC,kBAAA,CAAA3B,MAAA,CAAA8B,eAAA,EAAAvB,MAAA,MAAAP,MAAA,CAAA8B,eAAA,GAAAvB,MAAA;MAAA,OAAAb,EAAA,CAAAQ,WAAA,CAAAK,MAAA;IAAA,EAA6B;IACvEb,EADE,CAAAW,YAAA,EAA0G,EACtG;IAEJX,EADF,CAAAC,cAAA,eAA6B,aACpB;IAAAD,EAAA,CAAAoB,MAAA,oEAAU;IAAApB,EAAA,CAAAW,YAAA,EAAQ;IACzBX,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAqC,UAAA,KAAAC,8CAAA,kBAAoD;IAMxDtC,EADE,CAAAW,YAAA,EAAM,EACF;IACNX,EAAA,CAAAC,cAAA,kBAA0D;IAAAD,EAAA,CAAAoB,MAAA,gCAAI;IAAApB,EAAA,CAAAW,YAAA,EAAS;IACvEX,EAAA,CAAAC,cAAA,kBAA4F;IAArCD,EAAA,CAAAE,UAAA,mBAAAqC,iEAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAyB,GAAA;MAAA,MAAAvB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAkC,kBAAA,GAA8B,KAAK;IAAA,EAAC;IAACxC,EAAA,CAAAoB,MAAA,oBAAE;IAElGpB,EAFkG,CAAAW,YAAA,EAAS,EAClG,EACH;;;;IApBwCX,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAyC,gBAAA,YAAAnC,MAAA,CAAA4B,eAAA,CAA6B;IAK7BlC,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAyC,gBAAA,YAAAnC,MAAA,CAAA8B,eAAA,CAA6B;IAK7CpC,EAAA,CAAAqB,SAAA,GAAe;IAAfrB,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAoC,UAAA,CAAe;;;;;;IA2BnC1C,EAAA,CAAAC,cAAA,iBACyB;IADaD,EAAA,CAAAE,UAAA,mBAAAyC,wEAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAwC,GAAA;MAAA,MAAAC,MAAA,GAAA7C,EAAA,CAAAO,aAAA,GAAAS,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAqC,MAAA,CAAAC,UAAA,IAA2BxC,MAAA,CAAAyC,gBAAA,CAAAF,MAAA,CAAAC,UAAA,CAAgC;IAAA,EAAC;IACzE9C,EAAA,CAAAoB,MAAA,mBAAE;IAAApB,EAAA,CAAAW,YAAA,EAAS;;;;;;IALtCX,EADF,CAAAC,cAAA,SAAkC,SAC5B;IAAAD,EAAA,CAAAoB,MAAA,GAAsB;IAAApB,EAAA,CAAAW,YAAA,EAAK;IAC/BX,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAoB,MAAA,GAAqB;IAAApB,EAAA,CAAAW,YAAA,EAAK;IAE5BX,EADF,CAAAC,cAAA,SAAI,iBACuE;IAAhCD,EAAA,CAAAE,UAAA,mBAAA8C,+DAAA;MAAA,MAAAH,MAAA,GAAA7C,EAAA,CAAAI,aAAA,CAAA6C,GAAA,EAAAjC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,gBAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IAAC7C,EAAA,CAAAoB,MAAA,mBAAE;IAAApB,EAAA,CAAAW,YAAA,EAAS;IACpFX,EAAA,CAAAqC,UAAA,IAAAc,+CAAA,qBACyB;IAE7BnD,EADE,CAAAW,YAAA,EAAK,EACF;;;;IAPCX,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAoD,iBAAA,CAAAP,MAAA,CAAAQ,YAAA,CAAsB;IACtBrD,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAoD,iBAAA,CAAAP,MAAA,CAAAS,WAAA,CAAqB;IAIpBtD,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAwB,UAAA,SAAAqB,MAAA,CAAAC,UAAA,CAAoB;;;;;IAIzB9C,EADF,CAAAC,cAAA,SAAiD,aACX;IAAAD,EAAA,CAAAoB,MAAA,+BAAI;IAC1CpB,EAD0C,CAAAW,YAAA,EAAK,EAC1C;;;;;IASPX,EAAA,CAAAC,cAAA,SAAkD;IAChDD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAyB,kBAAA,MAAA8B,UAAA,CAAAC,SAAA,QAAAD,UAAA,CAAAE,UAAA,MACF;;;;;;IAJFzD,EADF,CAAAC,cAAA,cAAuE,SACjE;IAAAD,EAAA,CAAAoB,MAAA,GAAuC;IAAApB,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAqC,UAAA,IAAAqB,4CAAA,iBAAkD;IAGpD1D,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,iBAAyE;IAAhCD,EAAA,CAAAE,UAAA,mBAAAyD,gEAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,mBAAA,EAAqB;IAAA,EAAC;IAAC7D,EAAA,CAAAoB,MAAA,mBAAE;IAC7EpB,EAD6E,CAAAW,YAAA,EAAS,EAChF;;;;IAPAX,EAAA,CAAAqB,SAAA,GAAuC;IAAvCrB,EAAA,CAAA8D,kBAAA,mCAAAxD,MAAA,CAAAyD,gBAAA,CAAAV,YAAA,KAAuC;IAElBrD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAA0D,sBAAA,CAAyB;;;ADxEtD,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAA1B,UAAU,GAAU,EAAE;IACrB,KAAA2B,WAAW,GAAG,IAAIxE,YAAY,EAAY;IAC1C,KAAAyE,cAAc,GAAG,IAAIzE,YAAY,EAAY;IAC7C,KAAA0E,YAAY,GAAG,IAAI1E,YAAY,EAAqD;IACpF,KAAA2E,cAAc,GAAG,IAAI3E,YAAY,EAAU;IAErD,KAAA2C,kBAAkB,GAAG,KAAK;IAC1B,KAAAN,eAAe,GAAG,EAAE;IACpB,KAAAE,eAAe,GAAG,EAAE;IACpB,KAAA2B,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAU,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;;EAElCC,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACH,aAAa,CAACK,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACJ,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACP,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMY,OAAO,GAAG,IAAI,CAACN,aAAa,CAACO,WAAW,EAAE;MAChD,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACP,SAAS,CAACc,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAAC7B,YAAY,CAAC2B,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAC5B,WAAW,IAAI4B,QAAQ,CAAC5B,WAAW,CAAC0B,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACR,uBAAuB,EAAE;EAChC;EAEA;EACAnE,WAAWA,CAAA;IACT,IAAI,CAACgE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACG,uBAAuB,EAAE;EAChC;EAEA;EACA9C,cAAcA,CAAA;IACZ,MAAMb,QAAQ,GAAG,CAAC,IAAI,CAACyB,UAAU,IAAI,EAAE,EAAEuC,MAAM,CAACI,CAAC,IAAIA,CAAC,CAACpE,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACiB,eAAe,IAAIjB,QAAQ,CAACqE,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAML,QAAQ,GAAa;MACzB7B,YAAY,EAAE,IAAI,CAACnB,eAAe;MAClCoB,WAAW,EAAE,IAAI,CAAClB;KACnB;IACD;IACA,MAAMoD,OAAO,GAAqBvE,QAAQ,CAACwE,GAAG,CAACJ,CAAC,KAAK;MACnDvC,UAAU,EAAE,CAAC;MAAE;MACf4C,KAAK,EAAEL,CAAC,CAACM,EAAE,IAAIN,CAAC,CAACO,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAER,CAAC,CAACQ,UAAU,IAAI,aAAa;MACzCrC,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAE4B,CAAC,CAAC3D;KACf,CAAC,CAAC;IACH,IAAI,CAAC6C,YAAY,CAACuB,IAAI,CAAC;MAAEZ,QAAQ;MAAEM;IAAO,CAAE,CAAC;IAC7C,IAAI,CAAChD,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACN,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACM,UAAU,IAAI,EAAE,EAAEqD,OAAO,CAACV,CAAC,IAAIA,CAAC,CAACpE,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACA+E,aAAaA,CAAA;IACX,IAAI,CAAC3B,WAAW,CAACyB,IAAI,EAAE;EACzB;EAEA;EACA5C,gBAAgBA,CAACgC,QAAkB;IACjC,IAAI,CAACnB,gBAAgB,GAAGmB,QAAQ;IAChC,IAAI,CAACZ,cAAc,CAACwB,IAAI,CAACZ,QAAQ,CAAC;EACpC;EAEA;EACAnC,gBAAgBA,CAACkD,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC1B,cAAc,CAACsB,IAAI,CAACG,UAAU,CAAC;IACtC;EACF;EAEA;EACApC,mBAAmBA,CAAA;IACjB,IAAI,CAACE,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACK,eAAe,CAACa,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAACrD,UAAU,KAAK,IAAI,CAACiB,gBAAiB,CAACjB,UAAU,CAAC;EAC7F;;;uCAzGWmB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAmC,SAAA;MAAAC,MAAA;QAAAlC,SAAA;QAAAC,eAAA;QAAA1B,UAAA;MAAA;MAAA4D,OAAA;QAAAjC,WAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAA+B,UAAA;MAAAC,QAAA,GAAAxG,EAAA,CAAAyG,oBAAA,EAAAzG,EAAA,CAAA0G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA3B,QAAA,WAAA4B,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR9B/G,EAHN,CAAAC,cAAA,aAAmC,aACG,aACkC,YACjD;UAAAD,EAAA,CAAAoB,MAAA,+BAAI;UAAApB,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAA+G,yDAAA;YAAA,OAASD,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAC9DhG,EAAA,CAAAU,SAAA,WAAgC;UAAAV,EAAA,CAAAoB,MAAA,oBAClC;UACFpB,EADE,CAAAW,YAAA,EAAS,EACL;UAKFX,EAFJ,CAAAC,cAAA,aAAmC,aACR,gBAE2B;UADiBD,EAAA,CAAA+B,gBAAA,2BAAAmF,iEAAArG,MAAA;YAAAb,EAAA,CAAAiC,kBAAA,CAAA+E,GAAA,CAAAvC,aAAA,EAAA5D,MAAA,MAAAmG,GAAA,CAAAvC,aAAA,GAAA5D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvEb,EAArB,CAAAE,UAAA,mBAAAiH,yDAAA;YAAA,OAASH,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC,yBAAAgC,+DAAA;YAAA,OAAgBJ,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UADjDpF,EAAA,CAAAW,YAAA,EACkD;UAEhDX,EADF,CAAAC,cAAA,cAAgC,kBAC+C;UAArBD,EAAA,CAAAE,UAAA,mBAAAmH,0DAAA;YAAA,OAASL,GAAA,CAAA5B,QAAA,EAAU;UAAA,EAAC;UAC1EpF,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAqC,UAAA,KAAAiF,0CAAA,qBAAsG;UAM9GtH,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAGNX,EAAA,CAAAqC,UAAA,KAAAkF,uCAAA,mBAAyE;UA8BjEvH,EAJR,CAAAC,cAAA,eAA2B,iBACuB,aACvC,UACD,UACE;UAAAD,EAAA,CAAAoB,MAAA,gCAAI;UAAApB,EAAA,CAAAW,YAAA,EAAK;UACbX,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAoB,MAAA,oBAAE;UAAApB,EAAA,CAAAW,YAAA,EAAK;UACXX,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAoB,MAAA,oBAAE;UAEVpB,EAFU,CAAAW,YAAA,EAAK,EACR,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UAULD,EATA,CAAAqC,UAAA,KAAAmF,sCAAA,iBAAkC,KAAAC,sCAAA,iBASe;UAKvDzH,EAFI,CAAAW,YAAA,EAAQ,EACF,EACJ;UAGNX,EAAA,CAAAqC,UAAA,KAAAqF,uCAAA,kBAAuE;UASzE1H,EAAA,CAAAW,YAAA,EAAM;;;UA7EqEX,EAAA,CAAAqB,SAAA,IAA2B;UAA3BrB,EAAA,CAAAyC,gBAAA,YAAAuE,GAAA,CAAAvC,aAAA,CAA2B;UAMXzE,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAwB,UAAA,SAAAwF,GAAA,CAAAvC,aAAA,CAAmB;UAStGzE,EAAA,CAAAqB,SAAA,EAAwB;UAAxBrB,EAAA,CAAAwB,UAAA,SAAAwF,GAAA,CAAAxE,kBAAA,CAAwB;UAoCJxC,EAAA,CAAAqB,SAAA,IAAY;UAAZrB,EAAA,CAAAwB,UAAA,YAAAwF,GAAA,CAAA7C,SAAA,CAAY;UAS3BnE,EAAA,CAAAqB,SAAA,EAA0C;UAA1CrB,EAAA,CAAAwB,UAAA,UAAAwF,GAAA,CAAA7C,SAAA,IAAA6C,GAAA,CAAA7C,SAAA,CAAAmB,MAAA,OAA0C;UAQ/CtF,EAAA,CAAAqB,SAAA,EAAsB;UAAtBrB,EAAA,CAAAwB,UAAA,SAAAwF,GAAA,CAAAjD,gBAAA,CAAsB;;;qBDvElBjE,YAAY,EAAA6H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9H,WAAW,EAAA+H,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}