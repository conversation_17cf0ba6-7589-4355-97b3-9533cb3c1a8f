{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiPictureGetPicturelListPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiPictureGetPicturelListPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiPictureGetPicturelListPost$Plain.PATH = '/api/Picture/GetPictureList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiPictureGetPicturelListPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\picture\\api-picture-get-picturel-list-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetPictureListRequest } from '../../models/get-picture-list-request';\r\nimport { GetPictureListResponseListResponseBase } from '../../models/get-picture-list-response-list-response-base';\r\n\r\nexport interface ApiPictureGetPicturelListPost$Plain$Params {\r\n  body?: GetPictureListRequest\r\n}\r\n\r\nexport function apiPictureGetPicturelListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiPictureGetPicturelListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetPictureListResponseListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiPictureGetPicturelListPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetPictureListResponseListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiPictureGetPicturelListPost$Plain.PATH = '/api/Picture/GetPictureList';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,mCAAmCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAmD,EAAEC,OAAqB;EAC/J,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,mCAAmC,CAACM,IAAI,EAAE,MAAM,CAAC;EACxF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA+D;EACxE,CAAC,CAAC,CACH;AACH;AAEAb,mCAAmC,CAACM,IAAI,GAAG,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}