{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseFileSaveBuildCaseFilePost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveBuildCaseFilePost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseFileSaveBuildCaseFilePost$Json.PATH = '/api/BuildCaseFile/SaveBuildCaseFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseFileSaveBuildCaseFilePost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\build-case-file\\api-build-case-file-save-build-case-file-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params {\r\n      body?: {\r\n'CBuildCaseID'?: number;\r\n'CBuildCaseFileID'?: number;\r\n'CStatus'?: number;\r\n'CFile'?: Blob;\r\n'CCategoryName'?: string;\r\n'CSubmitRemark'?: string;\r\n}\r\n}\r\n\r\nexport function apiBuildCaseFileSaveBuildCaseFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileSaveBuildCaseFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveBuildCaseFilePost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseFileSaveBuildCaseFilePost$Json.PATH = '/api/BuildCaseFile/SaveBuildCaseFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAetD,OAAM,SAAUC,0CAA0CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA0D,EAAEC,OAAqB;EAC7K,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,0CAA0C,CAACM,IAAI,EAAE,MAAM,CAAC;EAC/F,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,0CAA0C,CAACM,IAAI,GAAG,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}