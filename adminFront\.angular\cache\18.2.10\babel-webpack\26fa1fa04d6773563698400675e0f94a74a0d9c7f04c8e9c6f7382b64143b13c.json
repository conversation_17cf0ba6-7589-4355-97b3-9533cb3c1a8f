{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component, Input } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { switchMap, tap } from 'rxjs';\nimport { BaseComponent } from '../components/base/baseComponent';\nlet ApproveWaitingComponent = class ApproveWaitingComponent extends BaseComponent {\n  constructor(allow, _buildCaseService, _specialChangeService, _router, _eventService) {\n    super(allow);\n    this.allow = allow;\n    this._buildCaseService = _buildCaseService;\n    this._specialChangeService = _specialChangeService;\n    this._router = _router;\n    this._eventService = _eventService;\n    this.CType = -1;\n    this.CDateStart = \"\";\n    this.CDateEnd = \"\";\n    this.isReadOnly = false;\n    this.TYPE_WAITING_APPROVE = [{\n      value: -1,\n      name: '全部'\n    }, {\n      value: 1,\n      name: '洽談紀錄'\n    }, {\n      value: 2,\n      name: '客變確認圖說'\n    }, {\n      value: 3,\n      name: '建案公佈欄文件'\n    }, {\n      value: 4,\n      name: '客變原則'\n    }, {\n      value: 5,\n      name: '標準圖說'\n    }];\n    this.listUserBuildCases = [];\n    this.listApprovalWaitingList = [];\n    this.queryCName = \"\";\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.buildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.listUserBuildCases.unshift({\n          CBuildCaseName: \"全部\",\n          cID: -1\n        });\n        if (!this.buildCaseId) {\n          this.buildCaseId = this.listUserBuildCases[0].cID;\n        }\n      }\n    }), switchMap(() => this.getListApproval(1))).subscribe();\n  }\n  getListApproval(pageIndex) {\n    return this._specialChangeService.apiSpecialChangeGetApproveWaitingListPost$Json({\n      body: {\n        CBuilCaseID: this.buildCaseId,\n        PageIndex: pageIndex,\n        PageSize: this.pageSize,\n        CDateEnd: this.CDateEnd == \"\" ? null : new Date(this.CDateEnd).toISOString(),\n        CDateStart: this.CDateEnd == \"\" ? null : new Date(this.CDateStart).toISOString(),\n        CType: +this.CType,\n        CName: this.queryCName\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listApprovalWaitingList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  searchList() {\n    this.getListApproval(1).subscribe();\n  }\n  edit(id) {\n    this._router.navigate([`/pages/approve-waiting/${id.CBuildCaseId}/${id.CID}`], {\n      queryParams: {\n        type: id.CType\n      }\n    });\n  }\n  getListPageChange(pageIndex) {\n    this.getListApproval(pageIndex).subscribe();\n  }\n  ngOnChanges() {\n    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.\n    //Add '${implements OnChanges}' to the class.\n    console.log(this.type);\n    if (this.type > 0) {\n      this.CType = this.type;\n      this.isReadOnly = true;\n      this.searchList();\n    }\n  }\n};\n__decorate([Input()], ApproveWaitingComponent.prototype, \"type\", void 0);\nApproveWaitingComponent = __decorate([Component({\n  selector: 'app-approve-waiting',\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbDatepickerModule],\n  templateUrl: './approve-waiting.component.html',\n  styleUrls: ['./approve-waiting.component.scss']\n})], ApproveWaitingComponent);\nexport { ApproveWaitingComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "Input", "SharedModule", "NbDatepickerModule", "switchMap", "tap", "BaseComponent", "ApproveWaitingComponent", "constructor", "allow", "_buildCaseService", "_specialChangeService", "_router", "_eventService", "CType", "CDateStart", "CDateEnd", "isReadOnly", "TYPE_WAITING_APPROVE", "value", "name", "listUserBuildCases", "listApprovalWaitingList", "queryCName", "receive", "pipe", "res", "action", "payload", "buildCaseId", "subscribe", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "StatusCode", "Entries", "unshift", "CBuildCaseName", "cID", "getListApproval", "pageIndex", "apiSpecialChangeGetApproveWaitingListPost$Json", "body", "CBuilCaseID", "PageIndex", "PageSize", "pageSize", "Date", "toISOString", "CName", "totalRecords", "TotalItems", "searchList", "edit", "id", "navigate", "CBuildCaseId", "CID", "queryParams", "type", "getListPageChange", "ngOnChanges", "console", "log", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\approve-waiting.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, Input, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { BuildCaseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { ApproveWaitingArgs, ApproveWaitingRes, BuildCaseGetListReponse } from 'src/services/api/models';\r\nimport { NbDatepickerModule } from '@nebular/theme';\r\nimport { mergeMap, switchMap, tap } from 'rxjs';\r\nimport { DEFAULT_DATE } from 'src/app/shared/constant/constant';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { Router } from '@angular/router';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'app-approve-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    NbDatepickerModule\r\n  ],\r\n  templateUrl: './approve-waiting.component.html',\r\n  styleUrls: ['./approve-waiting.component.scss']\r\n})\r\nexport class ApproveWaitingComponent extends BaseComponent implements OnInit {\r\n  @Input() type: number;\r\n  CType: number = -1\r\n  CDateStart: string = \"\"\r\n  CDateEnd: string = \"\"\r\n  isReadOnly: boolean = false;\r\n  TYPE_WAITING_APPROVE = [\r\n    {\r\n      value: -1,\r\n      name: '全部',\r\n    },\r\n    {\r\n      value: 1,\r\n      name: '洽談紀錄'\r\n    },\r\n    {\r\n      value: 2,\r\n      name: '客變確認圖說'\r\n    },\r\n    {\r\n      value: 3,\r\n      name: '建案公佈欄文件'\r\n    },\r\n    {\r\n      value: 4,\r\n      name: '客變原則'\r\n    },\r\n    {\r\n      value: 5,\r\n      name: '標準圖說'\r\n    }\r\n  ]\r\n\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n  listApprovalWaitingList: ApproveWaitingRes[] = []\r\n\r\n  buildCaseId: number\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _router: Router,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(allow);\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.buildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.listUserBuildCases.unshift({\r\n              CBuildCaseName: \"全部\",\r\n              cID: -1,\r\n            })\r\n            if (!this.buildCaseId) {\r\n              this.buildCaseId = this.listUserBuildCases[0].cID!\r\n            }\r\n          }\r\n        }),\r\n        switchMap(() => this.getListApproval(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  queryCName: string = \"\"\r\n  getListApproval(pageIndex: number) {\r\n    return this._specialChangeService.apiSpecialChangeGetApproveWaitingListPost$Json({\r\n      body: {\r\n        CBuilCaseID: this.buildCaseId,\r\n        PageIndex: pageIndex,\r\n        PageSize: this.pageSize,\r\n        CDateEnd: this.CDateEnd == \"\" ? null : new Date(this.CDateEnd).toISOString(),\r\n        CDateStart: this.CDateEnd == \"\" ? null : new Date(this.CDateStart).toISOString(),\r\n        CType: +this.CType,\r\n        CName : this.queryCName\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.listApprovalWaitingList = res.Entries!\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  searchList() {\r\n    this.getListApproval(1).subscribe()\r\n  }\r\n\r\n  edit(id: ApproveWaitingRes) {\r\n    this._router.navigate([`/pages/approve-waiting/${id.CBuildCaseId!}/${id.CID}`], {\r\n      queryParams: {\r\n        type: id.CType\r\n      }\r\n    })\r\n  }\r\n\r\n  getListPageChange(pageIndex: number) {\r\n    this.getListApproval(pageIndex).subscribe()\r\n  }\r\n  ngOnChanges(): void {\r\n    //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.\r\n    //Add '${implements OnChanges}' to the class.\r\n    console.log(this.type);\r\n    if (this.type > 0) {\r\n      this.CType = this.type;\r\n      this.isReadOnly = true;\r\n      this.searchList();\r\n    }\r\n\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,KAAK,QAAgB,eAAe;AACxD,SAASC,YAAY,QAAQ,6BAA6B;AAG1D,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAAmBC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAE/C,SAASC,aAAa,QAAQ,kCAAkC;AAgBzD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAwB,SAAQD,aAAa;EAsCxDE,YACqBC,KAAkB,EAC7BC,iBAAmC,EACnCC,qBAA2C,EAC3CC,OAAe,EACfC,aAA2B;IAEnC,KAAK,CAACJ,KAAK,CAAC;IANO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,aAAa,GAAbA,aAAa;IAzCvB,KAAAC,KAAK,GAAW,CAAC,CAAC;IAClB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,oBAAoB,GAAG,CACrB;MACEC,KAAK,EAAE,CAAC,CAAC;MACTC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE;KACP,CACF;IAED,KAAAC,kBAAkB,GAA8B,EAAE;IAClD,KAAAC,uBAAuB,GAAwB,EAAE;IA4CjD,KAAAC,UAAU,GAAW,EAAE;IAhCrB,IAAI,CAACV,aAAa,CAACW,OAAO,EAAE,CAACC,IAAI,CAC/BpB,GAAG,CAAEqB,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACC,WAAW,GAAGH,GAAG,CAACE,OAAO;MAChC;IACF,CAAC,CAAC,CACH,CAACE,SAAS,EAAE;EACf;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACtB,iBAAiB,CAACuB,qCAAqC,CAAC,EAAE,CAAC,CAC7DR,IAAI,CACHpB,GAAG,CAACqB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACQ,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACb,kBAAkB,GAAGK,GAAG,CAACS,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACd,kBAAkB,CAACe,OAAO,CAAC;UAC9BC,cAAc,EAAE,IAAI;UACpBC,GAAG,EAAE,CAAC;SACP,CAAC;QACF,IAAI,CAAC,IAAI,CAACT,WAAW,EAAE;UACrB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAACiB,GAAI;QACpD;MACF;IACF,CAAC,CAAC,EACFlC,SAAS,CAAC,MAAM,IAAI,CAACmC,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACT,SAAS,EAAE;EACjB;EAGAS,eAAeA,CAACC,SAAiB;IAC/B,OAAO,IAAI,CAAC7B,qBAAqB,CAAC8B,8CAA8C,CAAC;MAC/EC,IAAI,EAAE;QACJC,WAAW,EAAE,IAAI,CAACd,WAAW;QAC7Be,SAAS,EAAEJ,SAAS;QACpBK,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvB9B,QAAQ,EAAE,IAAI,CAACA,QAAQ,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI+B,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAAC,CAACgC,WAAW,EAAE;QAC5EjC,UAAU,EAAE,IAAI,CAACC,QAAQ,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI+B,IAAI,CAAC,IAAI,CAAChC,UAAU,CAAC,CAACiC,WAAW,EAAE;QAChFlC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK;QAClBmC,KAAK,EAAG,IAAI,CAAC1B;;KAEhB,CAAC,CAACE,IAAI,CACLpB,GAAG,CAACqB,GAAG,IAAG;MACR,IAAIA,GAAG,CAACQ,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACZ,uBAAuB,GAAGI,GAAG,CAACS,OAAQ;QAC3C,IAAI,CAACe,YAAY,GAAGxB,GAAG,CAACyB,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACb,eAAe,CAAC,CAAC,CAAC,CAACT,SAAS,EAAE;EACrC;EAEAuB,IAAIA,CAACC,EAAqB;IACxB,IAAI,CAAC1C,OAAO,CAAC2C,QAAQ,CAAC,CAAC,0BAA0BD,EAAE,CAACE,YAAa,IAAIF,EAAE,CAACG,GAAG,EAAE,CAAC,EAAE;MAC9EC,WAAW,EAAE;QACXC,IAAI,EAAEL,EAAE,CAACxC;;KAEZ,CAAC;EACJ;EAEA8C,iBAAiBA,CAACpB,SAAiB;IACjC,IAAI,CAACD,eAAe,CAACC,SAAS,CAAC,CAACV,SAAS,EAAE;EAC7C;EACA+B,WAAWA,CAAA;IACT;IACA;IACAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,IAAI,CAAC;IACtB,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,EAAE;MACjB,IAAI,CAAC7C,KAAK,GAAG,IAAI,CAAC6C,IAAI;MACtB,IAAI,CAAC1C,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmC,UAAU,EAAE;IACnB;EAEF;CACD;AA7HUY,UAAA,EAAR/D,KAAK,EAAE,C,oDAAc;AADXM,uBAAuB,GAAAyD,UAAA,EAXnChE,SAAS,CAAC;EACTiE,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPpE,YAAY,EACZG,YAAY,EACZC,kBAAkB,CACnB;EACDiE,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACW9D,uBAAuB,CA8HnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}