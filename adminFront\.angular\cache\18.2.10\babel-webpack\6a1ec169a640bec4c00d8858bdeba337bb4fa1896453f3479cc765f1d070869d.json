{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiReviewSaveReviewPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiReviewSaveReviewPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiReviewSaveReviewPost$Json.PATH = '/api/Review/SaveReview';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiReviewSaveReviewPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\review\\api-review-save-review-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { HouseReview } from '../../models/house-review';\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiReviewSaveReviewPost$Json$Params {\r\n      body?: {\r\n'CBuildCaseId'?: number;\r\n'CReviewId'?: number;\r\n'CReviewType'?: number;\r\n'CReviewName'?: string;\r\n'CSort'?: number;\r\n'CStatus'?: number;\r\n'CFile'?: Blob;\r\n'CExamineNote'?: string;\r\n'HouseReviews'?: Array<HouseReview>;\r\n}\r\n}\r\n\r\nexport function apiReviewSaveReviewPost$Json(http: HttpClient, rootUrl: string, params?: ApiReviewSaveReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiReviewSaveReviewPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiReviewSaveReviewPost$Json.PATH = '/api/Review/SaveReview';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAmBtD,OAAM,SAAUC,4BAA4BA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA4C,EAAEC,OAAqB;EACjJ,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,4BAA4B,CAACM,IAAI,EAAE,MAAM,CAAC;EACjF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,4BAA4B,CAACM,IAAI,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}