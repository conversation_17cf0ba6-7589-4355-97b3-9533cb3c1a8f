{"ast": null, "code": "import addLeadingZeros from \"../../addLeadingZeros/index.js\";\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\nvar formatters = {\n  // Year\n  y: function y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    var signedYear = date.getUTCFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    var year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);\n  },\n  // Month\n  M: function M(date, token) {\n    var month = date.getUTCMonth();\n    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d: function d(date, token) {\n    return addLeadingZeros(date.getUTCDate(), token.length);\n  },\n  // AM or PM\n  a: function a(date, token) {\n    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return dayPeriodEnumValue.toUpperCase();\n      case 'aaa':\n        return dayPeriodEnumValue;\n      case 'aaaaa':\n        return dayPeriodEnumValue[0];\n      case 'aaaa':\n      default:\n        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';\n    }\n  },\n  // Hour [1-12]\n  h: function h(date, token) {\n    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H: function H(date, token) {\n    return addLeadingZeros(date.getUTCHours(), token.length);\n  },\n  // Minute\n  m: function m(date, token) {\n    return addLeadingZeros(date.getUTCMinutes(), token.length);\n  },\n  // Second\n  s: function s(date, token) {\n    return addLeadingZeros(date.getUTCSeconds(), token.length);\n  },\n  // Fraction of second\n  S: function S(date, token) {\n    var numberOfDigits = token.length;\n    var milliseconds = date.getUTCMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};\nexport default formatters;", "map": {"version": 3, "names": ["addLeadingZeros", "formatters", "y", "date", "token", "signedYear", "getUTCFullYear", "year", "length", "M", "month", "getUTCMonth", "String", "d", "getUTCDate", "a", "dayPeriodEnumValue", "getUTCHours", "toUpperCase", "h", "H", "m", "getUTCMinutes", "s", "getUTCSeconds", "S", "numberOfDigits", "milliseconds", "getUTCMilliseconds", "fractionalSeconds", "Math", "floor", "pow"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/_lib/format/lightFormatters/index.js"], "sourcesContent": ["import addLeadingZeros from \"../../addLeadingZeros/index.js\";\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\nvar formatters = {\n  // Year\n  y: function y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    var signedYear = date.getUTCFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    var year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === 'yy' ? year % 100 : year, token.length);\n  },\n  // Month\n  M: function M(date, token) {\n    var month = date.getUTCMonth();\n    return token === 'M' ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d: function d(date, token) {\n    return addLeadingZeros(date.getUTCDate(), token.length);\n  },\n  // AM or PM\n  a: function a(date, token) {\n    var dayPeriodEnumValue = date.getUTCHours() / 12 >= 1 ? 'pm' : 'am';\n    switch (token) {\n      case 'a':\n      case 'aa':\n        return dayPeriodEnumValue.toUpperCase();\n      case 'aaa':\n        return dayPeriodEnumValue;\n      case 'aaaaa':\n        return dayPeriodEnumValue[0];\n      case 'aaaa':\n      default:\n        return dayPeriodEnumValue === 'am' ? 'a.m.' : 'p.m.';\n    }\n  },\n  // Hour [1-12]\n  h: function h(date, token) {\n    return addLeadingZeros(date.getUTCHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H: function H(date, token) {\n    return addLeadingZeros(date.getUTCHours(), token.length);\n  },\n  // Minute\n  m: function m(date, token) {\n    return addLeadingZeros(date.getUTCMinutes(), token.length);\n  },\n  // Second\n  s: function s(date, token) {\n    return addLeadingZeros(date.getUTCSeconds(), token.length);\n  },\n  // Fraction of second\n  S: function S(date, token) {\n    var numberOfDigits = token.length;\n    var milliseconds = date.getUTCMilliseconds();\n    var fractionalSeconds = Math.floor(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};\nexport default formatters;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,gCAAgC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG;EACf;EACAC,CAAC,EAAE,SAASA,CAACA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIC,UAAU,GAAGF,IAAI,CAACG,cAAc,CAAC,CAAC;IACtC;IACA,IAAIC,IAAI,GAAGF,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU;IACvD,OAAOL,eAAe,CAACI,KAAK,KAAK,IAAI,GAAGG,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAEH,KAAK,CAACI,MAAM,CAAC;EAC1E,CAAC;EACD;EACAC,CAAC,EAAE,SAASA,CAACA,CAACN,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIM,KAAK,GAAGP,IAAI,CAACQ,WAAW,CAAC,CAAC;IAC9B,OAAOP,KAAK,KAAK,GAAG,GAAGQ,MAAM,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGV,eAAe,CAACU,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAC1E,CAAC;EACD;EACAG,CAAC,EAAE,SAASA,CAACA,CAACV,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOJ,eAAe,CAACG,IAAI,CAACW,UAAU,CAAC,CAAC,EAAEV,KAAK,CAACI,MAAM,CAAC;EACzD,CAAC;EACD;EACAO,CAAC,EAAE,SAASA,CAACA,CAACZ,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIY,kBAAkB,GAAGb,IAAI,CAACc,WAAW,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IACnE,QAAQb,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOY,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACzC,KAAK,KAAK;QACR,OAAOF,kBAAkB;MAC3B,KAAK,OAAO;QACV,OAAOA,kBAAkB,CAAC,CAAC,CAAC;MAC9B,KAAK,MAAM;MACX;QACE,OAAOA,kBAAkB,KAAK,IAAI,GAAG,MAAM,GAAG,MAAM;IACxD;EACF,CAAC;EACD;EACAG,CAAC,EAAE,SAASA,CAACA,CAAChB,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOJ,eAAe,CAACG,IAAI,CAACc,WAAW,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAEb,KAAK,CAACI,MAAM,CAAC;EACrE,CAAC;EACD;EACAY,CAAC,EAAE,SAASA,CAACA,CAACjB,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOJ,eAAe,CAACG,IAAI,CAACc,WAAW,CAAC,CAAC,EAAEb,KAAK,CAACI,MAAM,CAAC;EAC1D,CAAC;EACD;EACAa,CAAC,EAAE,SAASA,CAACA,CAAClB,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOJ,eAAe,CAACG,IAAI,CAACmB,aAAa,CAAC,CAAC,EAAElB,KAAK,CAACI,MAAM,CAAC;EAC5D,CAAC;EACD;EACAe,CAAC,EAAE,SAASA,CAACA,CAACpB,IAAI,EAAEC,KAAK,EAAE;IACzB,OAAOJ,eAAe,CAACG,IAAI,CAACqB,aAAa,CAAC,CAAC,EAAEpB,KAAK,CAACI,MAAM,CAAC;EAC5D,CAAC;EACD;EACAiB,CAAC,EAAE,SAASA,CAACA,CAACtB,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIsB,cAAc,GAAGtB,KAAK,CAACI,MAAM;IACjC,IAAImB,YAAY,GAAGxB,IAAI,CAACyB,kBAAkB,CAAC,CAAC;IAC5C,IAAIC,iBAAiB,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,GAAGG,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEN,cAAc,GAAG,CAAC,CAAC,CAAC;IACnF,OAAO1B,eAAe,CAAC6B,iBAAiB,EAAEzB,KAAK,CAACI,MAAM,CAAC;EACzD;AACF,CAAC;AACD,eAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}