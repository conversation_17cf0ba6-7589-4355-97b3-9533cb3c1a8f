{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let BaseLabelDirective = /*#__PURE__*/(() => {\n  class BaseLabelDirective {\n    constructor(el, rennder) {\n      this.el = el;\n      this.rennder = rennder;\n    }\n    ngOnInit() {\n      this.rennder.addClass(this.el.nativeElement, 'mr-2');\n      this.rennder.addClass(this.el.nativeElement, 'label');\n    }\n    static {\n      this.ɵfac = function BaseLabelDirective_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BaseLabelDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n      };\n    }\n    static {\n      this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n        type: BaseLabelDirective,\n        selectors: [[\"\", \"baseLabel\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return BaseLabelDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}