{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { delay, shareReplay, debounceTime } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class LayoutService {\n  constructor() {\n    this.layoutSize$ = new Subject();\n    this.layoutSizeChange$ = this.layoutSize$.pipe(shareReplay({\n      refCount: true\n    }));\n  }\n  changeLayoutSize() {\n    this.layoutSize$.next(null);\n  }\n  onChangeLayoutSize() {\n    return this.layoutSizeChange$.pipe(delay(1));\n  }\n  onSafeChangeLayoutSize() {\n    return this.layoutSizeChange$.pipe(debounceTime(350));\n  }\n  static {\n    this.ɵfac = function LayoutService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LayoutService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "delay", "shareReplay", "debounceTime", "LayoutService", "constructor", "layoutSize$", "layoutSizeChange$", "pipe", "refCount", "changeLayoutSize", "next", "onChangeLayoutSize", "onSafeChangeLayoutSize", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\utils\\layout.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, Subject } from 'rxjs';\r\nimport { delay, shareReplay, debounceTime } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class LayoutService {\r\n\r\n  protected layoutSize$ = new Subject();\r\n  protected layoutSizeChange$ = this.layoutSize$.pipe(\r\n    shareReplay({ refCount: true }),\r\n  );\r\n\r\n  changeLayoutSize() {\r\n    this.layoutSize$.next(null);\r\n  }\r\n\r\n  onChangeLayoutSize(): Observable<any> {\r\n    return this.layoutSizeChange$.pipe(delay(1));\r\n  }\r\n\r\n  onSafeChangeLayoutSize(): Observable<any> {\r\n    return this.layoutSizeChange$.pipe(\r\n      debounceTime(350),\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,OAAO,QAAQ,MAAM;AAC1C,SAASC,KAAK,EAAEC,WAAW,EAAEC,YAAY,QAAQ,gBAAgB;;AAGjE,OAAM,MAAOC,aAAa;EAD1BC,YAAA;IAGY,KAAAC,WAAW,GAAG,IAAIN,OAAO,EAAE;IAC3B,KAAAO,iBAAiB,GAAG,IAAI,CAACD,WAAW,CAACE,IAAI,CACjDN,WAAW,CAAC;MAAEO,QAAQ,EAAE;IAAI,CAAE,CAAC,CAChC;;EAEDC,gBAAgBA,CAAA;IACd,IAAI,CAACJ,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC;EAC7B;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACL,iBAAiB,CAACC,IAAI,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEAY,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACN,iBAAiB,CAACC,IAAI,CAChCL,YAAY,CAAC,GAAG,CAAC,CAClB;EACH;;;uCAnBWC,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAU,OAAA,EAAbV,aAAa,CAAAW;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}