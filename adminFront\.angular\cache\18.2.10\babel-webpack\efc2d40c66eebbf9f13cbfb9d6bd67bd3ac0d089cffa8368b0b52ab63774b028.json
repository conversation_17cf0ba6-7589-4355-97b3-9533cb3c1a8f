{"ast": null, "code": "//! moment.js locale configuration\n//! locale : <PERSON><PERSON> (East Timor) [tet]\n//! author : <PERSON> : https://github.com/joshbrooks\n//! author : <PERSON><PERSON> : https://github.com/marobo\n//! author : <PERSON> : https://github.com/soniasimoes\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var tet = moment.defineLocale('tet', {\n    months: 'Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru'.split('_'),\n    monthsShort: 'Jan_Fev_Mar_<PERSON>r_<PERSON>_<PERSON>_Jul_Ago_Set_Out_Nov_Dez'.split('_'),\n    weekdays: 'Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu'.split('_'),\n    weekdaysShort: 'Dom_Seg_Ters_Kua_Kint_Sest_Sab'.split('_'),\n    weekdaysMin: 'Do_Seg_Te_Ku_Ki_Ses_Sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Ohin iha] LT',\n      nextDay: '[Aban iha] LT',\n      nextWeek: 'dddd [iha] LT',\n      lastDay: '[Horiseik iha] LT',\n      lastWeek: 'dddd [semana kotuk] [iha] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'iha %s',\n      past: '%s liuba',\n      s: 'segundu balun',\n      ss: 'segundu %d',\n      m: 'minutu ida',\n      mm: 'minutu %d',\n      h: 'oras ida',\n      hh: 'oras %d',\n      d: 'loron ida',\n      dd: 'loron %d',\n      M: 'fulan ida',\n      MM: 'fulan %d',\n      y: 'tinan ida',\n      yy: 'tinan %d'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return tet;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}