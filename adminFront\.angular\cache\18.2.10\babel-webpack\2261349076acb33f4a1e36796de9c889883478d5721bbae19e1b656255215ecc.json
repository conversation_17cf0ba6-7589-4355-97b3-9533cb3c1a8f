{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetChangePreOrderPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetChangePreOrderPost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetChangePreOrderPost$Json.PATH = '/api/House/GetChangePreOrder';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetChangePreOrderPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-change-pre-order-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetChangePreOrderArgs } from '../../models/get-change-pre-order-args';\r\nimport { GetChangePreOrderResponeResponseBase } from '../../models/get-change-pre-order-respone-response-base';\r\n\r\nexport interface ApiHouseGetChangePreOrderPost$Json$Params {\r\n      body?: GetChangePreOrderArgs\r\n}\r\n\r\nexport function apiHouseGetChangePreOrderPost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseGetChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetChangePreOrderResponeResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetChangePreOrderPost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetChangePreOrderResponeResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetChangePreOrderPost$Json.PATH = '/api/House/GetChangePreOrder';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,kCAAkCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAkD,EAAEC,OAAqB;EAC7J,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,kCAAkC,CAACM,IAAI,EAAE,MAAM,CAAC;EACvF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEP;EAAO,CAAE,CAAC,CACjE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA6D;EACtE,CAAC,CAAC,CACH;AACH;AAEAb,kCAAkC,CAACM,IAAI,GAAG,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}