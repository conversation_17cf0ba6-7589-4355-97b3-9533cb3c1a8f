{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nvar days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport default function formatRFC7231(dirtyDate) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var dayName = days[originalDate.getUTCDay()];\n  var dayOfMonth = addLeadingZeros(originalDate.getUTCDate(), 2);\n  var monthName = months[originalDate.getUTCMonth()];\n  var year = originalDate.getUTCFullYear();\n  var hour = addLeadingZeros(originalDate.getUTCHours(), 2);\n  var minute = addLeadingZeros(originalDate.getUTCMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getUTCSeconds(), 2);\n\n  // Result variables.\n  return \"\".concat(dayName, \", \").concat(dayOfMonth, \" \").concat(monthName, \" \").concat(year, \" \").concat(hour, \":\").concat(minute, \":\").concat(second, \" GMT\");\n}", "map": {"version": 3, "names": ["toDate", "<PERSON><PERSON><PERSON><PERSON>", "addLeadingZeros", "days", "months", "formatRFC7231", "dirtyDate", "arguments", "length", "TypeError", "concat", "originalDate", "RangeError", "day<PERSON><PERSON>", "getUTCDay", "dayOfMonth", "getUTCDate", "monthName", "getUTCMonth", "year", "getUTCFullYear", "hour", "getUTCHours", "minute", "getUTCMinutes", "second", "getUTCSeconds"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/formatRFC7231/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nvar days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nvar months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n\n/**\n * @name formatRFC7231\n * @category Common Helpers\n * @summary Format the date according to the RFC 7231 standard (https://tools.ietf.org/html/rfc7231#section-*******).\n *\n * @description\n * Return the formatted date string in RFC 7231 format.\n * The result will always be in UTC timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {String} the formatted date string\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 7231 format:\n * const result = formatRFC7231(new Date(2019, 8, 18, 19, 0, 52))\n * //=> 'Wed, 18 Sep 2019 19:00:52 GMT'\n */\nexport default function formatRFC7231(dirtyDate) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 arguments required, but only \".concat(arguments.length, \" present\"));\n  }\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n  var dayName = days[originalDate.getUTCDay()];\n  var dayOfMonth = addLeadingZeros(originalDate.getUTCDate(), 2);\n  var monthName = months[originalDate.getUTCMonth()];\n  var year = originalDate.getUTCFullYear();\n  var hour = addLeadingZeros(originalDate.getUTCHours(), 2);\n  var minute = addLeadingZeros(originalDate.getUTCMinutes(), 2);\n  var second = addLeadingZeros(originalDate.getUTCSeconds(), 2);\n\n  // Result variables.\n  return \"\".concat(dayName, \", \").concat(dayOfMonth, \" \").concat(monthName, \" \").concat(year, \" \").concat(hour, \":\").concat(minute, \":\").concat(second, \" GMT\");\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,IAAIC,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AAC5D,IAAIC,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;AAEjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC/C,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,iCAAiC,CAACC,MAAM,CAACH,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC,CAAC;EAC7F;EACA,IAAIG,YAAY,GAAGX,MAAM,CAACM,SAAS,CAAC;EACpC,IAAI,CAACL,OAAO,CAACU,YAAY,CAAC,EAAE;IAC1B,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EACA,IAAIC,OAAO,GAAGV,IAAI,CAACQ,YAAY,CAACG,SAAS,CAAC,CAAC,CAAC;EAC5C,IAAIC,UAAU,GAAGb,eAAe,CAACS,YAAY,CAACK,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9D,IAAIC,SAAS,GAAGb,MAAM,CAACO,YAAY,CAACO,WAAW,CAAC,CAAC,CAAC;EAClD,IAAIC,IAAI,GAAGR,YAAY,CAACS,cAAc,CAAC,CAAC;EACxC,IAAIC,IAAI,GAAGnB,eAAe,CAACS,YAAY,CAACW,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;EACzD,IAAIC,MAAM,GAAGrB,eAAe,CAACS,YAAY,CAACa,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7D,IAAIC,MAAM,GAAGvB,eAAe,CAACS,YAAY,CAACe,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;;EAE7D;EACA,OAAO,EAAE,CAAChB,MAAM,CAACG,OAAO,EAAE,IAAI,CAAC,CAACH,MAAM,CAACK,UAAU,EAAE,GAAG,CAAC,CAACL,MAAM,CAACO,SAAS,EAAE,GAAG,CAAC,CAACP,MAAM,CAACS,IAAI,EAAE,GAAG,CAAC,CAACT,MAAM,CAACW,IAAI,EAAE,GAAG,CAAC,CAACX,MAAM,CAACa,MAAM,EAAE,GAAG,CAAC,CAACb,MAAM,CAACe,MAAM,EAAE,MAAM,CAAC;AAC/J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}