{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getHours\n * @category Hour Helpers\n * @summary Get the hours of the given date.\n *\n * @description\n * Get the hours of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the hours of 29 February 2012 11:45:00:\n * const result = getHours(new Date(2012, 1, 29, 11, 45))\n * //=> 11\n */\nexport default function getHours(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var hours = date.getHours();\n  return hours;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}