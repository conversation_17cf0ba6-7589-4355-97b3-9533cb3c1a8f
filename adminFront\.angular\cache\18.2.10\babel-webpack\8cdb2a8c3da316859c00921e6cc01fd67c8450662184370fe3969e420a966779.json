{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../image-modal/image-modal.component\";\nexport class ImageGalleryComponent {\n  constructor() {\n    this.images = [];\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square';\n    this.containerClass = '';\n    this.currentIndex = 0;\n    this.isModalVisible = false;\n  }\n  ngOnInit() {\n    this.currentIndex = 0;\n  }\n  ngOnDestroy() {\n    // 確保模態關閉時移除body class\n    if (this.isModalVisible) {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  onImageClick(event) {\n    this.currentIndex = event.index;\n    this.openModal();\n  }\n  onCarouselIndexChange(index) {\n    this.currentIndex = index;\n  }\n  onModalIndexChange(index) {\n    this.currentIndex = index;\n  }\n  openModal() {\n    this.isModalVisible = true;\n    document.body.classList.add('modal-open');\n  }\n  closeModal() {\n    this.isModalVisible = false;\n    document.body.classList.remove('modal-open');\n  }\n  static {\n    this.ɵfac = function ImageGalleryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageGalleryComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageGalleryComponent,\n      selectors: [[\"app-image-gallery\"]],\n      inputs: {\n        images: \"images\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        aspectRatio: \"aspectRatio\",\n        containerClass: \"containerClass\"\n      },\n      decls: 3,\n      vars: 10,\n      consts: [[1, \"image-gallery\"], [3, \"imageClick\", \"indexChange\", \"images\", \"currentIndex\", \"showThumbnails\", \"showCounter\", \"showNavigation\", \"aspectRatio\", \"containerClass\"], [3, \"close\", \"indexChange\", \"images\", \"currentIndex\", \"isVisible\"]],\n      template: function ImageGalleryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-image-carousel\", 1);\n          i0.ɵɵlistener(\"imageClick\", function ImageGalleryComponent_Template_app_image_carousel_imageClick_1_listener($event) {\n            return ctx.onImageClick($event);\n          })(\"indexChange\", function ImageGalleryComponent_Template_app_image_carousel_indexChange_1_listener($event) {\n            return ctx.onCarouselIndexChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"app-image-modal\", 2);\n          i0.ɵɵlistener(\"close\", function ImageGalleryComponent_Template_app_image_modal_close_2_listener() {\n            return ctx.closeModal();\n          })(\"indexChange\", function ImageGalleryComponent_Template_app_image_modal_indexChange_2_listener($event) {\n            return ctx.onModalIndexChange($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.images)(\"currentIndex\", ctx.currentIndex)(\"showThumbnails\", ctx.showThumbnails)(\"showCounter\", ctx.showCounter)(\"showNavigation\", ctx.showNavigation)(\"aspectRatio\", ctx.aspectRatio)(\"containerClass\", ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.images)(\"currentIndex\", ctx.currentIndex)(\"isVisible\", ctx.isModalVisible);\n        }\n      },\n      dependencies: [i1.ImageModalComponent],\n      styles: [\".image-gallery[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImltYWdlLWdhbGxlcnkuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDRSxXQUFBO0FBQUYiLCJmaWxlIjoiaW1hZ2UtZ2FsbGVyeS5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi8vIOmAmeWAi+e1hOS7tuS4u+imgeS9nOeCuuWuueWZqO+8jOaoo+W8j+eUseWtkOe1hOS7tuiZleeQhlxyXG4uaW1hZ2UtZ2FsbGVyeSB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29tcG9uZW50cy9pbWFnZS1nYWxsZXJ5L2ltYWdlLWdhbGxlcnkuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDRSxXQUFBO0FBQUY7QUFDQSx3WkFBd1oiLCJzb3VyY2VzQ29udGVudCI6WyIvLyDDqcKAwpnDpcKAwovDp8K1woTDpMK7wrbDpMK4wrvDqMKmwoHDpMK9wpzDp8KCwrrDpcKuwrnDpcKZwqjDr8K8wozDpsKowqPDpcK8wo/Dp8KUwrHDpcKtwpDDp8K1woTDpMK7wrbDqMKZwpXDp8KQwoZcclxuLmltYWdlLWdhbGxlcnkge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ImageGalleryComponent", "constructor", "images", "showThumbnails", "showCounter", "showNavigation", "aspectRatio", "containerClass", "currentIndex", "isModalVisible", "ngOnInit", "ngOnDestroy", "document", "body", "classList", "remove", "onImageClick", "event", "index", "openModal", "onCarouselIndexChange", "onModalIndexChange", "add", "closeModal", "selectors", "inputs", "decls", "vars", "consts", "template", "ImageGalleryComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "ImageGalleryComponent_Template_app_image_carousel_imageClick_1_listener", "$event", "ImageGalleryComponent_Template_app_image_carousel_indexChange_1_listener", "ɵɵelementEnd", "ImageGalleryComponent_Template_app_image_modal_close_2_listener", "ImageGalleryComponent_Template_app_image_modal_indexChange_2_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-gallery\\image-gallery.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-gallery\\image-gallery.component.html"], "sourcesContent": ["import { Component, Input, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { ImageData } from '../image-carousel/image-carousel.component';\r\n\r\n@Component({\r\n  selector: 'app-image-gallery',\r\n  templateUrl: './image-gallery.component.html',\r\n  styleUrls: ['./image-gallery.component.scss']\r\n})\r\nexport class ImageGalleryComponent implements OnInit, OnDestroy {\r\n  @Input() images: ImageData[] = [];\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() aspectRatio: string = 'aspect-square';\r\n  @Input() containerClass: string = '';\r\n\r\n  currentIndex: number = 0;\r\n  isModalVisible: boolean = false;\r\n\r\n  ngOnInit() {\r\n    this.currentIndex = 0;\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // 確保模態關閉時移除body class\r\n    if (this.isModalVisible) {\r\n      document.body.classList.remove('modal-open');\r\n    }\r\n  }\r\n\r\n  onImageClick(event: { image: ImageData, index: number }): void {\r\n    this.currentIndex = event.index;\r\n    this.openModal();\r\n  }\r\n\r\n  onCarouselIndexChange(index: number): void {\r\n    this.currentIndex = index;\r\n  }\r\n\r\n  onModalIndexChange(index: number): void {\r\n    this.currentIndex = index;\r\n  }\r\n\r\n  openModal(): void {\r\n    this.isModalVisible = true;\r\n    document.body.classList.add('modal-open');\r\n  }\r\n\r\n  closeModal(): void {\r\n    this.isModalVisible = false;\r\n    document.body.classList.remove('modal-open');\r\n  }\r\n}\r\n", "<div class=\"image-gallery\">\r\n  <!-- 圖片輪播組件 -->\r\n  <app-image-carousel\r\n    [images]=\"images\"\r\n    [currentIndex]=\"currentIndex\"\r\n    [showThumbnails]=\"showThumbnails\"\r\n    [showCounter]=\"showCounter\"\r\n    [showNavigation]=\"showNavigation\"\r\n    [aspectRatio]=\"aspectRatio\"\r\n    [containerClass]=\"containerClass\"\r\n    (imageClick)=\"onImageClick($event)\"\r\n    (indexChange)=\"onCarouselIndexChange($event)\">\r\n  </app-image-carousel>\r\n\r\n  <!-- 圖片放大模態 -->\r\n  <app-image-modal\r\n    [images]=\"images\"\r\n    [currentIndex]=\"currentIndex\"\r\n    [isVisible]=\"isModalVisible\"\r\n    (close)=\"closeModal()\"\r\n    (indexChange)=\"onModalIndexChange($event)\">\r\n  </app-image-modal>\r\n</div>\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,qBAAqB;EALlCC,YAAA;IAMW,KAAAC,MAAM,GAAgB,EAAE;IACxB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAW,eAAe;IACrC,KAAAC,cAAc,GAAW,EAAE;IAEpC,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,cAAc,GAAY,KAAK;;EAE/BC,QAAQA,CAAA;IACN,IAAI,CAACF,YAAY,GAAG,CAAC;EACvB;EAEAG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACF,cAAc,EAAE;MACvBG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAC9C;EACF;EAEAC,YAAYA,CAACC,KAA0C;IACrD,IAAI,CAACT,YAAY,GAAGS,KAAK,CAACC,KAAK;IAC/B,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,qBAAqBA,CAACF,KAAa;IACjC,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC3B;EAEAG,kBAAkBA,CAACH,KAAa;IAC9B,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC3B;EAEAC,SAASA,CAAA;IACP,IAAI,CAACV,cAAc,GAAG,IAAI;IAC1BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACQ,GAAG,CAAC,YAAY,CAAC;EAC3C;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACd,cAAc,GAAG,KAAK;IAC3BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;EAC9C;;;uCA3CWf,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAwB,SAAA;MAAAC,MAAA;QAAAvB,MAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,cAAA;MAAA;MAAAmB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNhCE,EAFF,CAAAC,cAAA,aAA2B,4BAWuB;UAA9CD,EADA,CAAAE,UAAA,wBAAAC,wEAAAC,MAAA;YAAA,OAAcL,GAAA,CAAAhB,YAAA,CAAAqB,MAAA,CAAoB;UAAA,EAAC,yBAAAC,yEAAAD,MAAA;YAAA,OACpBL,GAAA,CAAAZ,qBAAA,CAAAiB,MAAA,CAA6B;UAAA,EAAC;UAC/CJ,EAAA,CAAAM,YAAA,EAAqB;UAGrBN,EAAA,CAAAC,cAAA,yBAK6C;UAA3CD,EADA,CAAAE,UAAA,mBAAAK,gEAAA;YAAA,OAASR,GAAA,CAAAT,UAAA,EAAY;UAAA,EAAC,yBAAAkB,sEAAAJ,MAAA;YAAA,OACPL,GAAA,CAAAX,kBAAA,CAAAgB,MAAA,CAA0B;UAAA,EAAC;UAE9CJ,EADE,CAAAM,YAAA,EAAkB,EACd;;;UAnBFN,EAAA,CAAAS,SAAA,EAAiB;UAMjBT,EANA,CAAAU,UAAA,WAAAX,GAAA,CAAA9B,MAAA,CAAiB,iBAAA8B,GAAA,CAAAxB,YAAA,CACY,mBAAAwB,GAAA,CAAA7B,cAAA,CACI,gBAAA6B,GAAA,CAAA5B,WAAA,CACN,mBAAA4B,GAAA,CAAA3B,cAAA,CACM,gBAAA2B,GAAA,CAAA1B,WAAA,CACN,mBAAA0B,GAAA,CAAAzB,cAAA,CACM;UAOjC0B,EAAA,CAAAS,SAAA,EAAiB;UAEjBT,EAFA,CAAAU,UAAA,WAAAX,GAAA,CAAA9B,MAAA,CAAiB,iBAAA8B,GAAA,CAAAxB,YAAA,CACY,cAAAwB,GAAA,CAAAvB,cAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}