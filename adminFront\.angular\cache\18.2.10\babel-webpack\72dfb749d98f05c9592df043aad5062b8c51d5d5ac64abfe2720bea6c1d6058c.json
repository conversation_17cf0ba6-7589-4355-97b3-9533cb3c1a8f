{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ImageCarouselComponent } from '../image-carousel/image-carousel.component';\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\nimport * as i0 from \"@angular/core\";\nexport class ImageGalleryComponent {\n  constructor() {\n    this.images = [];\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.aspectRatio = 'aspect-square';\n    this.containerClass = '';\n    this.currentIndex = 0;\n    this.isModalVisible = false;\n  }\n  ngOnInit() {\n    this.currentIndex = 0;\n  }\n  ngOnDestroy() {\n    // 確保模態關閉時移除body class\n    if (this.isModalVisible) {\n      document.body.classList.remove('modal-open');\n    }\n  }\n  onImageClick(event) {\n    this.currentIndex = event.index;\n    this.openModal();\n  }\n  onCarouselIndexChange(index) {\n    this.currentIndex = index;\n  }\n  onModalIndexChange(index) {\n    this.currentIndex = index;\n  }\n  openModal() {\n    this.isModalVisible = true;\n    document.body.classList.add('modal-open');\n  }\n  closeModal() {\n    this.isModalVisible = false;\n    document.body.classList.remove('modal-open');\n  }\n  static {\n    this.ɵfac = function ImageGalleryComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageGalleryComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageGalleryComponent,\n      selectors: [[\"app-image-gallery\"]],\n      inputs: {\n        images: \"images\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        aspectRatio: \"aspectRatio\",\n        containerClass: \"containerClass\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 10,\n      consts: [[1, \"image-gallery\"], [3, \"imageClick\", \"indexChange\", \"images\", \"currentIndex\", \"showThumbnails\", \"showCounter\", \"showNavigation\", \"aspectRatio\", \"containerClass\"], [3, \"close\", \"indexChange\", \"images\", \"currentIndex\", \"isVisible\"]],\n      template: function ImageGalleryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-image-carousel\", 1);\n          i0.ɵɵlistener(\"imageClick\", function ImageGalleryComponent_Template_app_image_carousel_imageClick_1_listener($event) {\n            return ctx.onImageClick($event);\n          })(\"indexChange\", function ImageGalleryComponent_Template_app_image_carousel_indexChange_1_listener($event) {\n            return ctx.onCarouselIndexChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"app-image-modal\", 2);\n          i0.ɵɵlistener(\"close\", function ImageGalleryComponent_Template_app_image_modal_close_2_listener() {\n            return ctx.closeModal();\n          })(\"indexChange\", function ImageGalleryComponent_Template_app_image_modal_indexChange_2_listener($event) {\n            return ctx.onModalIndexChange($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.images)(\"currentIndex\", ctx.currentIndex)(\"showThumbnails\", ctx.showThumbnails)(\"showCounter\", ctx.showCounter)(\"showNavigation\", ctx.showNavigation)(\"aspectRatio\", ctx.aspectRatio)(\"containerClass\", ctx.containerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"images\", ctx.images)(\"currentIndex\", ctx.currentIndex)(\"isVisible\", ctx.isModalVisible);\n        }\n      },\n      dependencies: [CommonModule, ImageCarouselComponent, ImageModalComponent],\n      styles: [\".image-gallery[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImltYWdlLWdhbGxlcnkuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDSSxXQUFBO0FBQUoiLCJmaWxlIjoiaW1hZ2UtZ2FsbGVyeS5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi8vIOmAmeWAi+e1hOS7tuS4u+imgeS9nOeCuuWuueWZqO+8jOaoo+W8j+eUseWtkOe1hOS7tuiZleeQhlxyXG4uaW1hZ2UtZ2FsbGVyeSB7XHJcbiAgICB3aWR0aDogMTAwJTtcclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29tcG9uZW50cy9pbWFnZS1nYWxsZXJ5L2ltYWdlLWdhbGxlcnkuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDSSxXQUFBO0FBQUo7QUFDQSw0WkFBNFoiLCJzb3VyY2VzQ29udGVudCI6WyIvLyDDqcKAwpnDpcKAwovDp8K1woTDpMK7wrbDpMK4wrvDqMKmwoHDpMK9wpzDp8KCwrrDpcKuwrnDpcKZwqjDr8K8wozDpsKowqPDpcK8wo/Dp8KUwrHDpcKtwpDDp8K1woTDpMK7wrbDqMKZwpXDp8KQwoZcclxuLmltYWdlLWdhbGxlcnkge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ImageCarouselComponent", "ImageModalComponent", "ImageGalleryComponent", "constructor", "images", "showThumbnails", "showCounter", "showNavigation", "aspectRatio", "containerClass", "currentIndex", "isModalVisible", "ngOnInit", "ngOnDestroy", "document", "body", "classList", "remove", "onImageClick", "event", "index", "openModal", "onCarouselIndexChange", "onModalIndexChange", "add", "closeModal", "selectors", "inputs", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ImageGalleryComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ImageGalleryComponent_Template_app_image_carousel_imageClick_1_listener", "$event", "ImageGalleryComponent_Template_app_image_carousel_indexChange_1_listener", "ɵɵelementEnd", "ImageGalleryComponent_Template_app_image_modal_close_2_listener", "ImageGalleryComponent_Template_app_image_modal_indexChange_2_listener", "ɵɵadvance", "ɵɵproperty", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-gallery\\image-gallery.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\image-gallery\\image-gallery.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ImageData } from '../image-carousel/image-carousel.component';\r\nimport { ImageCarouselComponent } from '../image-carousel/image-carousel.component';\r\nimport { ImageModalComponent } from '../image-modal/image-modal.component';\r\n\r\n@Component({\r\n    selector: 'app-image-gallery',\r\n    templateUrl: './image-gallery.component.html',\r\n    styleUrls: ['./image-gallery.component.scss'],\r\n    standalone: true,\r\n    imports: [CommonModule, ImageCarouselComponent, ImageModalComponent]\r\n})\r\nexport class ImageGalleryComponent implements OnInit, OnDestroy {\r\n    @Input() images: ImageData[] = [];\r\n    @Input() showThumbnails: boolean = true;\r\n    @Input() showCounter: boolean = true;\r\n    @Input() showNavigation: boolean = true;\r\n    @Input() aspectRatio: string = 'aspect-square';\r\n    @Input() containerClass: string = '';\r\n\r\n    currentIndex: number = 0;\r\n    isModalVisible: boolean = false;\r\n\r\n    ngOnInit() {\r\n        this.currentIndex = 0;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        // 確保模態關閉時移除body class\r\n        if (this.isModalVisible) {\r\n            document.body.classList.remove('modal-open');\r\n        }\r\n    }\r\n\r\n    onImageClick(event: { image: ImageData, index: number }): void {\r\n        this.currentIndex = event.index;\r\n        this.openModal();\r\n    }\r\n\r\n    onCarouselIndexChange(index: number): void {\r\n        this.currentIndex = index;\r\n    }\r\n\r\n    onModalIndexChange(index: number): void {\r\n        this.currentIndex = index;\r\n    }\r\n\r\n    openModal(): void {\r\n        this.isModalVisible = true;\r\n        document.body.classList.add('modal-open');\r\n    }\r\n\r\n    closeModal(): void {\r\n        this.isModalVisible = false;\r\n        document.body.classList.remove('modal-open');\r\n    }\r\n}\r\n", "<div class=\"image-gallery\">\r\n  <!-- 圖片輪播組件 -->\r\n  <app-image-carousel [images]=\"images\" [currentIndex]=\"currentIndex\" [showThumbnails]=\"showThumbnails\"\r\n    [showCounter]=\"showCounter\" [showNavigation]=\"showNavigation\" [aspectRatio]=\"aspectRatio\"\r\n    [containerClass]=\"containerClass\" (imageClick)=\"onImageClick($event)\" (indexChange)=\"onCarouselIndexChange($event)\">\r\n  </app-image-carousel>\r\n\r\n  <!-- 圖片放大模態 -->\r\n  <app-image-modal [images]=\"images\" [currentIndex]=\"currentIndex\" [isVisible]=\"isModalVisible\" (close)=\"closeModal()\"\r\n    (indexChange)=\"onModalIndexChange($event)\">\r\n  </app-image-modal>\r\n</div>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,mBAAmB,QAAQ,sCAAsC;;AAS1E,OAAM,MAAOC,qBAAqB;EAPlCC,YAAA;IAQa,KAAAC,MAAM,GAAgB,EAAE;IACxB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAW,eAAe;IACrC,KAAAC,cAAc,GAAW,EAAE;IAEpC,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,cAAc,GAAY,KAAK;;EAE/BC,QAAQA,CAAA;IACJ,IAAI,CAACF,YAAY,GAAG,CAAC;EACzB;EAEAG,WAAWA,CAAA;IACP;IACA,IAAI,IAAI,CAACF,cAAc,EAAE;MACrBG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAChD;EACJ;EAEAC,YAAYA,CAACC,KAA0C;IACnD,IAAI,CAACT,YAAY,GAAGS,KAAK,CAACC,KAAK;IAC/B,IAAI,CAACC,SAAS,EAAE;EACpB;EAEAC,qBAAqBA,CAACF,KAAa;IAC/B,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC7B;EAEAG,kBAAkBA,CAACH,KAAa;IAC5B,IAAI,CAACV,YAAY,GAAGU,KAAK;EAC7B;EAEAC,SAASA,CAAA;IACL,IAAI,CAACV,cAAc,GAAG,IAAI;IAC1BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACQ,GAAG,CAAC,YAAY,CAAC;EAC7C;EAEAC,UAAUA,CAAA;IACN,IAAI,CAACd,cAAc,GAAG,KAAK;IAC3BG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;EAChD;;;uCA3CSf,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAwB,SAAA;MAAAC,MAAA;QAAAvB,MAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,cAAA;MAAA;MAAAmB,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXhCP,EAFF,CAAAS,cAAA,aAA2B,4BAI6F;UAA9CT,EAApC,CAAAU,UAAA,wBAAAC,wEAAAC,MAAA;YAAA,OAAcJ,GAAA,CAAApB,YAAA,CAAAwB,MAAA,CAAoB;UAAA,EAAC,yBAAAC,yEAAAD,MAAA;YAAA,OAAgBJ,GAAA,CAAAhB,qBAAA,CAAAoB,MAAA,CAA6B;UAAA,EAAC;UACrHZ,EAAA,CAAAc,YAAA,EAAqB;UAGrBd,EAAA,CAAAS,cAAA,yBAC6C;UAA3CT,EAD4F,CAAAU,UAAA,mBAAAK,gEAAA;YAAA,OAASP,GAAA,CAAAb,UAAA,EAAY;UAAA,EAAC,yBAAAqB,sEAAAJ,MAAA;YAAA,OACnGJ,GAAA,CAAAf,kBAAA,CAAAmB,MAAA,CAA0B;UAAA,EAAC;UAE9CZ,EADE,CAAAc,YAAA,EAAkB,EACd;;;UATgBd,EAAA,CAAAiB,SAAA,EAAiB;UAEnCjB,EAFkB,CAAAkB,UAAA,WAAAV,GAAA,CAAAlC,MAAA,CAAiB,iBAAAkC,GAAA,CAAA5B,YAAA,CAA8B,mBAAA4B,GAAA,CAAAjC,cAAA,CAAkC,gBAAAiC,GAAA,CAAAhC,WAAA,CACxE,mBAAAgC,GAAA,CAAA/B,cAAA,CAAkC,gBAAA+B,GAAA,CAAA9B,WAAA,CAA4B,mBAAA8B,GAAA,CAAA7B,cAAA,CACxD;UAIlBqB,EAAA,CAAAiB,SAAA,EAAiB;UAA+BjB,EAAhD,CAAAkB,UAAA,WAAAV,GAAA,CAAAlC,MAAA,CAAiB,iBAAAkC,GAAA,CAAA5B,YAAA,CAA8B,cAAA4B,GAAA,CAAA3B,cAAA,CAA6B;;;qBDGjFZ,YAAY,EAAEC,sBAAsB,EAAEC,mBAAmB;MAAAgD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}