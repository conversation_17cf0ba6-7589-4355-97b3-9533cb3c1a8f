{"ast": null, "code": "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\nexport default function endOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  date.setMilliseconds(date.getMilliseconds() - 1);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}