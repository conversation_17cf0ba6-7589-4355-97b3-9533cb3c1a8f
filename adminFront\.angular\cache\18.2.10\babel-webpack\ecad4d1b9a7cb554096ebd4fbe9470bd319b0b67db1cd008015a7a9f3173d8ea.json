{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\models\\quotation.model.ts"], "sourcesContent": ["export interface QuotationItem {\r\n  cQuotationID?: number;\r\n  cHouseID: number;\r\n  cItemName: string;\r\n  cUnitPrice: number;\r\n  cCount: number;\r\n  cStatus?: number;\r\n  cIsDefault: boolean;\r\n}\r\n\r\nexport interface QuotationRequest {\r\n  houseId: number;\r\n  items: QuotationItem[];\r\n}\r\n\r\nexport interface QuotationResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: QuotationItem[];\r\n}\r\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}