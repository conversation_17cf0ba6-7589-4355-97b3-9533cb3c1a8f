{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class CapitalizePipe {\n  transform(input) {\n    return input && input.length ? input.charAt(0).toUpperCase() + input.slice(1).toLowerCase() : input;\n  }\n  static {\n    this.ɵfac = function CapitalizePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CapitalizePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"ngxCapitalize\",\n      type: CapitalizePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["CapitalizePipe", "transform", "input", "length", "char<PERSON>t", "toUpperCase", "slice", "toLowerCase", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\capitalize.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'ngxCapitalize',\r\n    standalone: true\r\n})\r\nexport class CapitalizePipe implements PipeTransform {\r\n\r\n  transform(input: string): string {\r\n    return input && input.length\r\n      ? (input.charAt(0).toUpperCase() + input.slice(1).toLowerCase())\r\n      : input;\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,cAAc;EAEzBC,SAASA,CAACC,KAAa;IACrB,OAAOA,KAAK,IAAIA,KAAK,CAACC,MAAM,GACvBD,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAC7DL,KAAK;EACX;;;uCANWF,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAQ,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}