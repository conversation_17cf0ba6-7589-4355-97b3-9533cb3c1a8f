{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiInfoPictureUploadListInfoPicturePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiInfoPictureUploadListInfoPicturePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiInfoPictureUploadListInfoPicturePost$Plain.PATH = '/api/InfoPicture/UploadListInfoPicture';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiInfoPictureUploadListInfoPicturePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\info-picture\\api-info-picture-upload-list-info-picture-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { UploadFileResponseResponseBase } from '../../models/upload-file-response-response-base';\r\n\r\nexport interface ApiInfoPictureUploadListInfoPicturePost$Plain$Params {\r\n      body?: {\r\n'CBuildCaseId'?: number;\r\n'CPath'?: string;\r\n'CFile'?: Array<Blob>;\r\n}\r\n}\r\n\r\nexport function apiInfoPictureUploadListInfoPicturePost$Plain(http: HttpClient, rootUrl: string, params?: ApiInfoPictureUploadListInfoPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiInfoPictureUploadListInfoPicturePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<UploadFileResponseResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiInfoPictureUploadListInfoPicturePost$Plain.PATH = '/api/InfoPicture/UploadListInfoPicture';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAYtD,OAAM,SAAUC,6CAA6CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA6D,EAAEC,OAAqB;EACnL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,6CAA6C,CAACM,IAAI,EAAE,MAAM,CAAC;EAClG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAuD;EAChE,CAAC,CAAC,CACH;AACH;AAEAb,6CAA6C,CAACM,IAAI,GAAG,wCAAwC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}