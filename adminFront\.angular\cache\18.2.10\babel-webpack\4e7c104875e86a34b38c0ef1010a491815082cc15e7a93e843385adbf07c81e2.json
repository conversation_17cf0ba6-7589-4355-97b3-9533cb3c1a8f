{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = value;\n          // 將戶別名稱轉換為 houseId（用於內部邏輯）\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.isHouseIdSelected(houseId);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n    } else {\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseIds, houseId];\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\n    const filteredHouseholdItems = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId && !this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n        unselectedFilteredIds.push(household.houseId);\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    for (const household of buildingHouseholds) {\n      if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n        unselectedBuildingIds.push(household.houseId);\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\n    // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      console.log('useHouseNameMode - 回傳戶別名稱陣列:', this.selectedHouseNames);\n      this.onChange([...this.selectedHouseNames]);\n      this.houseNameChange.emit([...this.selectedHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n    this.onTouched();\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    return this.selectedHouseIds.includes(houseId);\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseIds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return 0;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：將戶別名稱陣列轉換為 houseId 陣列\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    for (const houseName of houseNames) {\n      const houseId = this.getHouseIdByHouseName(houseName);\n      if (houseId !== null) {\n        houseIds.push(houseId);\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n    return houseIds;\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    for (const houseId of houseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n    return houseNames;\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseIds\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"useHouseNameMode\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseNameChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseIds", "useHouseNameMode", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseIds", "selectedHouseNames", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "console", "log", "length", "firstItem", "convertHouseNamesToIds", "error", "convertIdsToHouseNames", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "houseName", "HouseName", "code", "Building", "floor", "Floor", "houseId", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "onBuildingSelect", "updateFloorsForBuilding", "onBuildingClick", "households", "filteredItems", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "isHouseIdExcluded", "isHouseIdSelected", "newSelection", "id", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "filteredHouseholdItems", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuildingIds", "isHouseholdExcluded", "onUnselectAllBuilding", "buildingHouseIds", "undefined", "onClearAll", "emit", "houseIds", "selectedItems", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Set", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "hasNoSearchResults", "filtered", "getFilteredHouseholdsCount", "getHouseholdUniqueId", "toString", "getHouseholdByHouseId", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "houseNames", "householdInfo", "startsWith", "__decorate", "static", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation\r\n  private onChange = (value: number[] | string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { }  writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n      this.selectedHouseNames = [];\r\n    } else {\r\n      const firstItem = value[0];\r\n      \r\n      if (this.useHouseNameMode) {\r\n        // useHouseNameMode: 期望接收戶別名稱陣列\r\n        if (typeof firstItem === 'string') {\r\n          this.selectedHouseNames = value as string[];\r\n          // 將戶別名稱轉換為 houseId（用於內部邏輯）\r\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\r\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列');\r\n        } else {\r\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\r\n          this.selectedHouseNames = [];\r\n          this.selectedHouseIds = [];\r\n        }\r\n      } else {\r\n        // 一般模式: 期望接收 houseId 陣列\r\n        if (typeof firstItem === 'number') {\r\n          this.selectedHouseIds = value as number[];\r\n          // 將 houseId 轉換為戶別名稱（用於顯示）\r\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n          console.log('一般模式: 使用傳入的 houseId 陣列');\r\n        } else if (typeof firstItem === 'string') {\r\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\r\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\r\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\r\n          return;\r\n        } else {\r\n          console.error('writeValue 收到未知格式的資料:', value);\r\n          this.selectedHouseIds = [];\r\n          this.selectedHouseNames = [];\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\r\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    const filteredItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\r\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.isHouseIdSelected(houseId);\r\n    let newSelection: number[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseIds, houseId];\r\n    }\r\n\r\n    this.selectedHouseIds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    console.log('onSelectAllFiltered called');\r\n    console.log('selectedBuilding:', this.selectedBuilding);\r\n    console.log('selectedFloor:', this.selectedFloor);\r\n    console.log('searchTerm:', this.searchTerm);\r\n\r\n    if (!this.selectedBuilding) {\r\n      console.log('No building selected');\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 直接過濾戶別物件，而不是使用 filteredHouseholds 字串陣列\r\n    const filteredHouseholdItems = households.filter(h => {\r\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n\r\n    if (filteredHouseholdItems.length === 0) {\r\n      console.log('No filtered households found');\r\n      return;\r\n    }\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const household of filteredHouseholdItems) {\r\n      if (household.houseId &&\r\n        !this.isHouseIdSelected(household.houseId) &&\r\n        !this.isHouseIdExcluded(household.houseId)) {\r\n        unselectedFilteredIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\r\n    } else {\r\n      console.log('No households to add');\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n    for (const household of buildingHouseholds) {\r\n      if (household.houseId &&\r\n        !this.selectedHouseIds.includes(household.houseId) &&\r\n        !this.isHouseholdExcluded(household.houseId)) {\r\n        unselectedBuildingIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots); if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.emitChanges();\r\n  }  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\r\n    \r\n    // 根據模式決定要回傳的資料格式\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 回傳戶別名稱陣列\r\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n      console.log('useHouseNameMode - 回傳戶別名稱陣列:', this.selectedHouseNames);\r\n      this.onChange([...this.selectedHouseNames]);\r\n      this.houseNameChange.emit([...this.selectedHouseNames]);\r\n    } else {\r\n      // 一般模式: 回傳 houseId 陣列\r\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\r\n      this.onChange([...this.selectedHouseIds]);\r\n      \r\n      // 回傳 houseId 陣列\r\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\r\n      console.log('House IDs to emit:', houseIds);\r\n      this.houseIdChange.emit(houseIds);\r\n    }\r\n    \r\n    this.onTouched();\r\n\r\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n  }toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n  isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\r\n  } isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseIds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return 0;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 新增：將戶別名稱陣列轉換為 houseId 陣列\r\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\r\n    const houseIds: number[] = [];\r\n    for (const houseName of houseNames) {\r\n      const houseId = this.getHouseIdByHouseName(houseName);\r\n      if (houseId !== null) {\r\n        houseIds.push(houseId);\r\n      } else {\r\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\r\n      }\r\n    }\r\n    return houseIds;\r\n  }\r\n\r\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列\r\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\r\n    const houseNames: string[] = [];\r\n    for (const houseId of houseIds) {\r\n      const householdInfo = this.getHouseholdInfoById(houseId);\r\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\r\n        houseNames.push(householdInfo.houseName);\r\n      } else {\r\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\r\n      }\r\n    }\r\n    return houseNames;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA8BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA5Bd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAAW,KAAAC,gBAAgB,GAAY,IAAI;IAC1E,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAInB,YAAY,EAAmB;IACrD,KAAAoB,aAAa,GAAG,IAAIpB,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAqB,eAAe,GAAG,IAAIrB,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAsB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IAAG,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACtC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IACpB,KAAAC,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAAGC,UAAUA,CAACF,KAAY;IAC5BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAChB,gBAAgB,CAAC;IAErH,IAAI,CAACgB,KAAK,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACb,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMa,SAAS,GAAGN,KAAK,CAAC,CAAC,CAAC;MAE1B,IAAI,IAAI,CAAChB,gBAAgB,EAAE;QACzB;QACA,IAAI,OAAOsB,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACb,kBAAkB,GAAGO,KAAiB;UAC3C;UACA,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACe,sBAAsB,CAAC,IAAI,CAACd,kBAAkB,CAAC;UAC5EU,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,MAAM;UACLD,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAE,OAAOF,SAAS,CAAC;UACtE,IAAI,CAACb,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACD,gBAAgB,GAAG,EAAE;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAOc,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACd,gBAAgB,GAAGQ,KAAiB;UACzC;UACA,IAAI,CAACP,kBAAkB,GAAG,IAAI,CAACgB,sBAAsB,CAAC,IAAI,CAACjB,gBAAgB,CAAC;UAC5EW,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC,CAAC,MAAM,IAAI,OAAOE,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAH,OAAO,CAACK,KAAK,CAAC,oCAAoC,CAAC;UACnDL,OAAO,CAACK,KAAK,CAAC,2CAA2C,CAAC;UAC1D;QACF,CAAC,MAAM;UACLL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAER,KAAK,CAAC;UAC7C,IAAI,CAACR,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACC,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IAEAU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACZ,gBAAgB,CAAC;IAC9DW,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACX,kBAAkB,CAAC;IAClE,IAAI,CAACiB,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACb,QAAQ,GAAGa,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACX,SAAS,GAAGW,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACtC,QAAQ,GAAGsC,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACzC,WAAW,EAAE;MAC9C;MACA,IAAI,CAACuC,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACzB,SAAS,GAAG0B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1C,YAAY,IAAI,EAAE,CAAC;MACrDwB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzB,YAAY,CAAC;MACvD,IAAI,CAAC2C,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrB,gBAAgB,CAAC;IACnE;EACF;EAAUkC,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAACtC,YAAY,IAAIyC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1C,YAAY,CAAC,CAAC0B,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACX,SAAS,GAAG0B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1C,YAAY,CAAC;MAC/CwB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACV,SAAS,CAAC;MAChF,IAAI,CAACgB,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAAChC,WAAW,EAAE;MAC3B;MACA,IAAI,CAAC6C,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC7B,SAAS,GAAG,EAAE;MACnBS,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQmB,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC7C,WAAW,EAAE;IAEvB,IAAI,CAACoB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACzB,kBAAkB,CAACmD,WAAW,CAAC,IAAI,CAAC9C,WAAW,CAAC,CAAC+C,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBxB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuB,QAAQ,CAAC;QACtC,IAAI,CAAChD,YAAY,GAAG,IAAI,CAACiD,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAACnC,SAAS,GAAG0B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1C,YAAY,CAAC;QAC/C,IAAI,CAAC+B,wBAAwB,EAAE;QAC/B,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC1B,GAAG,CAAC0D,aAAa,EAAE;MAC1B,CAAC;MAAEtB,KAAK,EAAGA,KAAK,IAAI;QAClBL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAC7B,YAAY,GAAG,EAAE;QACtB,IAAI,CAACe,SAAS,GAAG,EAAE;QACnB,IAAI,CAACI,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC1B,GAAG,CAAC0D,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACG,OAAiC;IACxE,MAAMpD,YAAY,GAAiB,EAAE;IAErCyC,MAAM,CAACW,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDvD,YAAY,CAACsD,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,SAAS,EAAED,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,IAAI;QAC3DN,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACI,QAAQ,IAAIP,QAAQ;QACtDQ,KAAK,EAAEL,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACM,KAAK;QACjCC,OAAO,EAAEP,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjB9B,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOpC,YAAY;EACrB;EAAU+B,wBAAwBA,CAAA;IAChC,MAAMoC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACtD,gBAAgB,CAACwC,OAAO,CAACW,OAAO,IAAG;MACtC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;QACrC,MAAMqD,IAAI,GAAG,IAAI,CAACpE,YAAY,CAACsD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACP,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC9C,kBAAkB,GAAGiD,OAAO;EACnC;EAAEK,gBAAgBA,CAAClB,QAAgB;IACjC9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6B,QAAQ,CAAC;IAC3C,IAAI,CAAC5C,gBAAgB,GAAG4C,QAAQ;IAChC,IAAI,CAAC1C,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC8D,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC9B,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;IACzE;IACA,IAAI,CAACjC,GAAG,CAAC0D,aAAa,EAAE;EAC1B;EAEAuB,eAAeA,CAACpB,QAAgB;IAC9B9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE6B,QAAQ,CAAC;EACxD;EAAEX,wBAAwBA,CAAA;IACxBnB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACf,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACO,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAM0D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjEc,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkD,UAAU,CAACjD,MAAM,CAAC;IAEpE;IACA,MAAMkD,aAAa,GAAGD,UAAU,CAACE,MAAM,CAACP,CAAC,IAAG;MAC1C;MACA,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAAClE,aAAa,IAAI0D,CAAC,CAACR,KAAK,KAAK,IAAI,CAAClD,aAAa;MACxE;MACA,MAAMmE,WAAW,GAAG,CAAC,IAAI,CAACpE,UAAU,IAAI2D,CAAC,CAACZ,SAAS,CAACsB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtE,UAAU,CAACqE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC9D,kBAAkB,GAAG2D,aAAa,CAACpB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;IAE7DlC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;IAC1EF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmD,aAAa,CAACpB,GAAG,CAACc,CAAC,IAAI,GAAGA,CAAC,CAACZ,SAAS,IAAIY,CAAC,CAACR,KAAK,QAAQQ,CAAC,CAACN,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAkB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACxE,UAAU,GAAGwE,KAAK,CAACC,MAAM,CAAC/D,KAAK;IACpC,IAAI,CAACsB,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACd,UAAU,CAAC;EACtD;EACA0E,WAAWA,CAAA;IACT,IAAI,CAAC1E,UAAU,GAAG,EAAE;IACpB,IAAI,CAACgC,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACA6D,iBAAiBA,CAACtB,OAA2B;IAC3CxC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEuC,OAAO,CAAC;IAC9DxC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACZ,gBAAgB,CAAC;IAE/D,IAAI,CAACmD,OAAO,EAAE;MACZxC,OAAO,CAACC,GAAG,CAAC,gBAAgBuC,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACuB,iBAAiB,CAACvB,OAAO,CAAC,EAAE;MACnCxC,OAAO,CAACC,GAAG,CAAC,SAASuC,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA,MAAME,UAAU,GAAG,IAAI,CAACsB,iBAAiB,CAACxB,OAAO,CAAC;IAClD,IAAIyB,YAAsB;IAE1B,IAAIvB,UAAU,EAAE;MACduB,YAAY,GAAG,IAAI,CAAC5E,gBAAgB,CAACgE,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAK1B,OAAO,CAAC;IACnE,CAAC,MAAM;MACL,IAAI,IAAI,CAACnE,aAAa,IAAI,IAAI,CAACgB,gBAAgB,CAACa,MAAM,IAAI,IAAI,CAAC7B,aAAa,EAAE;QAC5E2B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAgE,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC5E,gBAAgB,EAAEmD,OAAO,CAAC;IACpD;IAEA,IAAI,CAACnD,gBAAgB,GAAG4E,YAAY;IACpC,IAAI,CAACE,WAAW,EAAE;EACpB;EACAC,iBAAiBA,CAAC5B,OAAe;IAC/B,IAAI,CAACnD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACgE,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAK1B,OAAO,CAAC;IAC1E,IAAI,CAAC2B,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnBrE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACf,gBAAgB,CAAC;IACvDc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACb,aAAa,CAAC;IACjDY,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACd,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1Bc,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA,MAAMkD,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjEc,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkD,UAAU,CAACjD,MAAM,CAAC;IAEpE;IACA,MAAMoE,sBAAsB,GAAGnB,UAAU,CAACE,MAAM,CAACP,CAAC,IAAG;MACnD;MACA,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAAClE,aAAa,IAAI0D,CAAC,CAACR,KAAK,KAAK,IAAI,CAAClD,aAAa;MACxE;MACA,MAAMmE,WAAW,GAAG,CAAC,IAAI,CAACpE,UAAU,IAAI2D,CAAC,CAACZ,SAAS,CAACsB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtE,UAAU,CAACqE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEFvD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqE,sBAAsB,CAACtC,GAAG,CAACc,CAAC,IAAI,GAAGA,CAAC,CAACZ,SAAS,IAAIY,CAAC,CAACR,KAAK,QAAQQ,CAAC,CAACN,OAAO,GAAG,CAAC,CAAC;IAExH,IAAI8B,sBAAsB,CAACpE,MAAM,KAAK,CAAC,EAAE;MACvCF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAMsE,YAAY,GAAG,IAAI,CAAClF,gBAAgB,CAACa,MAAM;IACjD,MAAMsE,UAAU,GAAG,IAAI,CAACnG,aAAa,IAAIoG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIN,sBAAsB,EAAE;MAC9C,IAAIM,SAAS,CAACpC,OAAO,IACnB,CAAC,IAAI,CAACwB,iBAAiB,CAACY,SAAS,CAACpC,OAAO,CAAC,IAC1C,CAAC,IAAI,CAACuB,iBAAiB,CAACa,SAAS,CAACpC,OAAO,CAAC,EAAE;QAC5CmC,qBAAqB,CAAC5B,IAAI,CAAC6B,SAAS,CAACpC,OAAO,CAAC;MAC/C;IACF;IAEAxC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0E,qBAAqB,CAAC;IAE9D;IACA,MAAME,KAAK,GAAGF,qBAAqB,CAACG,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAE5D,IAAIG,KAAK,CAAC3E,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACb,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGwF,KAAK,CAAC;MAC5D,IAAI,CAACV,WAAW,EAAE;MAClBnE,OAAO,CAACC,GAAG,CAAC,aAAa4E,KAAK,CAAC3E,MAAM,WAAW,EAAE2E,KAAK,CAAC;IAC1D,CAAC,MAAM;MACL7E,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAE8E,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC7F,gBAAgB,EAAE;IAE5B;IACA,MAAM8F,kBAAkB,GAAG,IAAI,CAACxG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAMqF,YAAY,GAAG,IAAI,CAAClF,gBAAgB,CAACa,MAAM;IACjD,MAAMsE,UAAU,GAAG,IAAI,CAACnG,aAAa,IAAIoG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMU,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAML,SAAS,IAAII,kBAAkB,EAAE;MAC1C,IAAIJ,SAAS,CAACpC,OAAO,IACnB,CAAC,IAAI,CAACnD,gBAAgB,CAACoE,QAAQ,CAACmB,SAAS,CAACpC,OAAO,CAAC,IAClD,CAAC,IAAI,CAAC0C,mBAAmB,CAACN,SAAS,CAACpC,OAAO,CAAC,EAAE;QAC9CyC,qBAAqB,CAAClC,IAAI,CAAC6B,SAAS,CAACpC,OAAO,CAAC;MAC/C;IACF;IAEA;IACA,MAAMqC,KAAK,GAAGI,qBAAqB,CAACH,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAAE,IAAIG,KAAK,CAAC3E,MAAM,GAAG,CAAC,EAAE;MAClF,IAAI,CAACb,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGwF,KAAK,CAAC;MAC5D,IAAI,CAACV,WAAW,EAAE;MAClBnE,OAAO,CAACC,GAAG,CAAC,aAAa4E,KAAK,CAAC3E,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAiF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACjG,gBAAgB,EAAE;IAE5B,MAAM8F,kBAAkB,GAAG,IAAI,CAACxG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAMkG,gBAAgB,GAAGJ,kBAAkB,CAAChD,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACN,OAAO,CAAC,CAACa,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAa;IAC1G,IAAI,CAAChG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACgE,MAAM,CAACa,EAAE,IAAI,CAACkB,gBAAgB,CAAC3B,QAAQ,CAACS,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAmB,UAAUA,CAAA;IACR,IAAI,CAACjG,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC8E,WAAW,EAAE;EACpB;EAAWA,WAAWA,CAAA;IACpB,IAAI,CAAC5D,wBAAwB,EAAE;IAC/BP,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACZ,gBAAgB,CAAC;IAE1E;IACA,IAAI,IAAI,CAACR,gBAAgB,EAAE;MACzB;MACA,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACgB,sBAAsB,CAAC,IAAI,CAACjB,gBAAgB,CAAC;MAC5EW,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACX,kBAAkB,CAAC;MACpE,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACN,kBAAkB,CAAC,CAAC;MAC3C,IAAI,CAACN,eAAe,CAACuG,IAAI,CAAC,CAAC,GAAG,IAAI,CAACjG,kBAAkB,CAAC,CAAC;IACzD,CAAC,MAAM;MACL;MACAU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACZ,gBAAgB,CAAC;MAC3D,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC;MAEzC;MACA,MAAMmG,QAAQ,GAAG,IAAI,CAACnG,gBAAgB,CAACgE,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAC;MACrErF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEuF,QAAQ,CAAC;MAC3C,IAAI,CAACzG,aAAa,CAACwG,IAAI,CAACC,QAAQ,CAAC;IACnC;IAEA,IAAI,CAAC1F,SAAS,EAAE;IAEhB;IACA,MAAM2F,aAAa,GAAG,IAAI,CAACpG,gBAAgB,CAAC2C,GAAG,CAACQ,OAAO,IAAG;MACxD,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;QACrC,MAAMqD,IAAI,GAAG,IAAI,CAACpE,YAAY,CAACsD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACS,MAAM,CAACT,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD5C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwF,aAAa,CAAC;IACrD,IAAI,CAAC3G,eAAe,CAACyG,IAAI,CAACE,aAAa,CAAC;EAC1C;EAACC,cAAcA,CAAA;IACb,IAAI,CAAC,IAAI,CAACpH,QAAQ,EAAE;MAClB,IAAI,CAACqH,UAAU,EAAE;MACjB3F,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACV,SAAS,CAAC;IACrD;EACF;EAEAoG,UAAUA,CAAA;IACR,IAAI,CAACxH,aAAa,CAACyH,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAEFC,mBAAmBA,CAAC3D,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACnD,gBAAgB,CAACoE,QAAQ,CAACjB,OAAO,CAAC;EAChD;EAEA0C,mBAAmBA,CAAC1C,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAAC5D,gBAAgB,CAAC6E,QAAQ,CAACjB,OAAO,CAAC;EAChD;EAEA4D,mBAAmBA,CAAC5D,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAAC0C,mBAAmB,CAAC1C,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC6D,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAAC3D,OAAO,CAAE;EACjE;EACA6D,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAChI,aAAa,IAAI,IAAI,CAACgB,gBAAgB,CAACa,MAAM,GAAG,IAAI,CAAC7B,aAAa;EACjF;EAAEiI,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACpH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM8F,kBAAkB,GAAG,IAAI,CAACxG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAChEmE,MAAM,CAACP,CAAC,IAAI,CAACA,CAAC,CAAClC,UAAU,IAAIkC,CAAC,CAACN,OAAO,KAAK6C,SAAS,CAAC;IACxD,OAAOL,kBAAkB,CAAC9E,MAAM,GAAG,CAAC,IAClC8E,kBAAkB,CAACuB,KAAK,CAAC3B,SAAS,IAAIA,SAAS,CAACpC,OAAO,IAAI,IAAI,CAACnD,gBAAgB,CAACoE,QAAQ,CAACmB,SAAS,CAACpC,OAAO,CAAC,CAAC;EACjH;EAEAgE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACtH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM8F,kBAAkB,GAAG,IAAI,CAACxG,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAO8F,kBAAkB,CAACyB,IAAI,CAAC7B,SAAS,IAAIA,SAAS,CAACpC,OAAO,IAAI,IAAI,CAACnD,gBAAgB,CAACoE,QAAQ,CAACmB,SAAS,CAACpC,OAAO,CAAC,CAAC;EACrH;EAAEkE,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAAChH,kBAAkB;EAChC;EAEAiH,gBAAgBA,CAAC7E,QAAgB;IAC/B,OAAO,IAAI,CAACtD,YAAY,CAACsD,QAAQ,CAAC,EAAE5B,MAAM,IAAI,CAAC;EACjD;EAEA0G,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACvH,gBAAgB,CAACa,MAAM;EACrC;EAEA;EACA2G,2BAA2BA,CAAC/E,QAAgB;IAC1C,OAAO,IAAI,CAACpC,kBAAkB,CAACoC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAgF,mBAAmBA,CAAChF,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACpC,kBAAkB,CAACoC,QAAQ,CAAC,IAAI,IAAI,CAACpC,kBAAkB,CAACoC,QAAQ,CAAC,CAAC5B,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ+C,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC/D,gBAAgB,EAAE;MAC1B,IAAI,CAACM,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAM2D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM6H,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElC7D,UAAU,CAACtB,OAAO,CAAC+C,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACtC,KAAK,EAAE;QACnByE,QAAQ,CAACE,GAAG,CAACrC,SAAS,CAACtC,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC9C,MAAM,GAAG0H,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEF1H,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACf,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAC;EACjF;EAEA;EACAmI,aAAaA,CAACrF,KAAa;IACzBtC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqC,KAAK,CAAC;IACrC,IAAI,CAAClD,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKkD,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACnB,wBAAwB,EAAE;IAC/B,IAAI,CAAClD,GAAG,CAAC0D,aAAa,EAAE;EAC1B;EAEA;EACAiG,aAAaA,CAACtF,KAAa;IACzB,IAAI,CAAC,IAAI,CAACpD,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMiE,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOiE,UAAU,CAACE,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACR,KAAK,KAAKA,KAAK,CAAC,CAACpC,MAAM;EACzD;EACA;EACA2H,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAC5I,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMiE,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM0F,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK4F,aAAa,CAAC;IACrE,OAAOlD,SAAS,EAAEtC,KAAK,IAAI,EAAE;EAC/B;EACA;EACAyF,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAMhG,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;MACrC,MAAM4D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACsD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM8C,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK4F,aAAa,CAAC;MACrE,IAAIlD,SAAS,EAAE;QACb,OAAO;UACL1C,SAAS,EAAE0C,SAAS,CAAC1C,SAAS;UAC9BI,KAAK,EAAEsC,SAAS,CAACtC,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE4F,aAAa;MAAExF,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACA0F,oBAAoBA,CAACxF,OAAe;IAClC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;MACrC,MAAM4D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACsD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM8C,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIoC,SAAS,EAAE;QACb,OAAO;UACL1C,SAAS,EAAE0C,SAAS,CAAC1C,SAAS;UAC9BI,KAAK,EAAEsC,SAAS,CAACtC,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE,MAAMM,OAAO,EAAE;MAAEF,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACA2F,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC9I,UAAU,IAAI,CAAC,IAAI,CAACD,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAEA,MAAMgJ,QAAQ,GAAG,IAAI,CAAC1J,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAACmE,MAAM,CAACP,CAAC,IAAG;MACnE,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAAClE,aAAa,IAAI0D,CAAC,CAACR,KAAK,KAAK,IAAI,CAAClD,aAAa;MACxE,MAAMmE,WAAW,GAAGT,CAAC,CAACZ,SAAS,CAACsB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtE,UAAU,CAACqE,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO2E,QAAQ,CAAChI,MAAM,KAAK,CAAC;EAC9B;EAEA;EACAiI,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjJ,gBAAgB,IAAI,CAAC,IAAI,CAACV,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,EAAE;MACvE,OAAO,CAAC;IACV;IAEA,MAAMgJ,QAAQ,GAAG,IAAI,CAAC1J,YAAY,CAAC,IAAI,CAACU,gBAAgB,CAAC,CAACmE,MAAM,CAACP,CAAC,IAAG;MACnE,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAAClE,aAAa,IAAI0D,CAAC,CAACR,KAAK,KAAK,IAAI,CAAClD,aAAa;MACxE,MAAMmE,WAAW,GAAG,CAAC,IAAI,CAACpE,UAAU,IAAI2D,CAAC,CAACZ,SAAS,CAACsB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtE,UAAU,CAACqE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO2E,QAAQ,CAAChI,MAAM;EACxB;EAEA;EACAkI,oBAAoBA,CAACxD,SAAwB;IAC3C,OAAOA,SAAS,CAACpC,OAAO,GAAGoC,SAAS,CAACpC,OAAO,CAAC6F,QAAQ,EAAE,GAAG,GAAGzD,SAAS,CAAC1C,SAAS,IAAI0C,SAAS,CAACtC,KAAK,EAAE;EACvG;EACA;EACQgG,qBAAqBA,CAAC9F,OAAe;IAC3C,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;MACrC,MAAM4D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACsD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM8C,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIoC,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EACA;EACQ2D,qBAAqBA,CAACrG,SAAiB;IAC7C,MAAMsG,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAM1G,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;MACrC,MAAM4D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACsD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM2G,OAAO,GAAGtF,UAAU,CAACE,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;MACjEuG,OAAO,CAAC5G,OAAO,CAAC+C,SAAS,IAAG;QAC1B4D,kBAAkB,CAACzF,IAAI,CAAC;UAAEjB,QAAQ;UAAE8C;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA5E,OAAO,CAACC,GAAG,CAAC,iBAAiBiC,SAAS,QAAQ,EAAEsG,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACtI,MAAM,KAAK,CAAC,EAAE;MACnCF,OAAO,CAAC0I,IAAI,CAAC,kBAAkBxG,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAIsG,kBAAkB,CAACtI,MAAM,GAAG,CAAC,EAAE;MACjCF,OAAO,CAAC0I,IAAI,CAAC,aAAaxG,SAAS,IAAI,EAAEsG,kBAAkB,CAACxG,GAAG,CAAC2G,CAAC,IAAI,GAAGA,CAAC,CAAC7G,QAAQ,IAAI6G,CAAC,CAAC/D,SAAS,CAACtC,KAAK,EAAE,CAAC,CAAC;MAC3GtC,OAAO,CAAC0I,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAAC1G,QAAQ,IAAI0G,kBAAkB,CAAC,CAAC,CAAC,CAAC5D,SAAS,CAACtC,KAAK,EAAE,CAAC;IACvG;IAEA,MAAMsG,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAAChE,SAAS,CAACpC,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQwB,iBAAiBA,CAACxB,OAAe;IACvC,OAAO,IAAI,CAACnD,gBAAgB,CAACoE,QAAQ,CAACjB,OAAO,CAAC;EAChD;EAEA;EACQuB,iBAAiBA,CAACvB,OAAe;IACvC,OAAO,IAAI,CAAC5D,gBAAgB,CAAC6E,QAAQ,CAACjB,OAAO,CAAC;EAChD;EACA;EACAqG,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMhH,QAAQ,IAAI,IAAI,CAACvC,SAAS,EAAE;MACrC,MAAM4D,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACsD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM8C,SAAS,GAAGzB,UAAU,CAACN,IAAI,CAACC,CAAC,IAAI,IAAI,CAACsF,oBAAoB,CAACtF,CAAC,CAAC,KAAKgG,QAAQ,CAAC;MACjF,IAAIlE,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACQxE,sBAAsBA,CAAC2I,UAAoB;IACjD,MAAMvD,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAMtD,SAAS,IAAI6G,UAAU,EAAE;MAClC,MAAMvG,OAAO,GAAG,IAAI,CAAC+F,qBAAqB,CAACrG,SAAS,CAAC;MACrD,IAAIM,OAAO,KAAK,IAAI,EAAE;QACpBgD,QAAQ,CAACzC,IAAI,CAACP,OAAO,CAAC;MACxB,CAAC,MAAM;QACLxC,OAAO,CAAC0I,IAAI,CAAC,aAAaxG,SAAS,eAAe,CAAC;MACrD;IACF;IACA,OAAOsD,QAAQ;EACjB;EAEA;EACQlF,sBAAsBA,CAACkF,QAAkB;IAC/C,MAAMuD,UAAU,GAAa,EAAE;IAC/B,KAAK,MAAMvG,OAAO,IAAIgD,QAAQ,EAAE;MAC9B,MAAMwD,aAAa,GAAG,IAAI,CAAChB,oBAAoB,CAACxF,OAAO,CAAC;MACxD,IAAIwG,aAAa,CAAC9G,SAAS,IAAI,CAAC8G,aAAa,CAAC9G,SAAS,CAAC+G,UAAU,CAAC,KAAK,CAAC,EAAE;QACzEF,UAAU,CAAChG,IAAI,CAACiG,aAAa,CAAC9G,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLlC,OAAO,CAAC0I,IAAI,CAAC,gBAAgBlG,OAAO,UAAU,CAAC;MACjD;IACF;IACA,OAAOuG,UAAU;EACnB;CACD;AAhpBkDG,UAAA,EAAhDrL,SAAS,CAAC,iBAAiB,EAAE;EAAEsL,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAC3ED,UAAA,EAARzL,KAAK,EAAE,C,6DAA+B;AAC9ByL,UAAA,EAARzL,KAAK,EAAE,C,+DAAqC;AACpCyL,UAAA,EAARzL,KAAK,EAAE,C,0DAA2B;AAC1ByL,UAAA,EAARzL,KAAK,EAAE,C,6DAAmC;AAClCyL,UAAA,EAARzL,KAAK,EAAE,C,8DAAiC;AAAUyL,UAAA,EAARzL,KAAK,EAAE,C,kEAAkC;AAC3EyL,UAAA,EAARzL,KAAK,EAAE,C,6DAA6B;AAC5ByL,UAAA,EAARzL,KAAK,EAAE,C,kEAAkC;AACjCyL,UAAA,EAARzL,KAAK,EAAE,C,kEAAiC;AAChCyL,UAAA,EAARzL,KAAK,EAAE,C,kEAAmC;AAEjCyL,UAAA,EAATxL,MAAM,EAAE,C,iEAAuD;AACtDwL,UAAA,EAATxL,MAAM,EAAE,C,+DAA8C;AAC7CwL,UAAA,EAATxL,MAAM,EAAE,C,iEAAgD;AAd9CK,yBAAyB,GAAAmL,UAAA,EAZrC1L,SAAS,CAAC;EACT4L,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAE1L,iBAAiB;IAC1B2L,WAAW,EAAE7L,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxD2L,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACW3L,yBAAyB,CAipBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}