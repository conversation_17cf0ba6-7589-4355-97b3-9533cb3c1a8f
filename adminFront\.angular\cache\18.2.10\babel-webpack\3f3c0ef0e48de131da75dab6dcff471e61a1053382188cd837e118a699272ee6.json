{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { SecurityCamerasData } from '../data/security-cameras';\nimport * as i0 from \"@angular/core\";\nexport let SecurityCamerasService = /*#__PURE__*/(() => {\n  class SecurityCamerasService extends SecurityCamerasData {\n    constructor() {\n      super(...arguments);\n      this.cameras = [{\n        title: 'Camera #1',\n        source: 'assets/images/camera1.jpg'\n      }, {\n        title: 'Camera #2',\n        source: 'assets/images/camera2.jpg'\n      }, {\n        title: 'Camera #3',\n        source: 'assets/images/camera3.jpg'\n      }, {\n        title: 'Camera #4',\n        source: 'assets/images/camera4.jpg'\n      }];\n    }\n    getCamerasData() {\n      return observableOf(this.cameras);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵSecurityCamerasService_BaseFactory;\n        return function SecurityCamerasService_Factory(__ngFactoryType__) {\n          return (ɵSecurityCamerasService_BaseFactory || (ɵSecurityCamerasService_BaseFactory = i0.ɵɵgetInheritedFactory(SecurityCamerasService)))(__ngFactoryType__ || SecurityCamerasService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SecurityCamerasService,\n        factory: SecurityCamerasService.ɵfac\n      });\n    }\n  }\n  return SecurityCamerasService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}