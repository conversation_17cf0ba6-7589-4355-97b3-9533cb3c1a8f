{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { finalize, mergeMap, tap } from 'rxjs';\nimport * as XLSX from 'xlsx';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction BuildingMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction BuildingMaterialComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.search());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_31_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u55AE\\u7B46\\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext();\n      const inputFile_r8 = i0.ɵɵreference(34);\n      return i0.ɵɵresetView(inputFile_r8.click());\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u532F\\u5165 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_button_35_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.exportExelMaterialList());\n    });\n    i0.ɵɵtext(1, \" \\u4E0B\\u8F09\\u7BC4\\u4F8B\\u6A94\\u6848 \");\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_th_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 22);\n    i0.ɵɵtext(1, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tr_60_td_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.CPrice);\n  }\n}\nfunction BuildingMaterialComponent_tr_60_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_tr_60_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r6 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r3.onSelectedMaterial(item_r10, dialog_r6));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuildingMaterialComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, BuildingMaterialComponent_tr_60_td_17_Template, 2, 1, \"td\", 36);\n    i0.ɵɵelementStart(18, \"td\", 37);\n    i0.ɵɵtemplate(19, BuildingMaterialComponent_tr_60_button_19_Template, 2, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CImageCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CInfoImageCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CDescription);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r10.CShowPrice == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction BuildingMaterialComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 40)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u5EFA\\u6750\\u7BA1\\u7406 > \\u65B0\\u589E\\u5EFA\\u6750 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 41)(4, \"h5\", 42);\n    i0.ɵɵtext(5, \"\\u8ACB\\u8F38\\u5165\\u4E0B\\u65B9\\u5167\\u5BB9\\u65B0\\u589E\\u5EFA\\u6750\\uFF0C\\u8ACB\\u7559\\u610F\\u5EFA\\u6750\\u5716\\u7247\\u7DE8\\u865F\\u4E0D\\u53EF\\u8207\\u8A72\\u5EFA\\u6848\\u5167\\u5176\\u4ED6\\u7DE8\\u865F\\u91CD\\u8907\\u3002\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 43)(7, \"div\", 44)(8, \"label\", 45);\n    i0.ɵɵtext(9, \"\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CName, $event) || (ctx_r3.selectedMaterial.CName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 47)(12, \"label\", 45);\n    i0.ɵɵtext(13, \"\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPart, $event) || (ctx_r3.selectedMaterial.CPart = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 47)(16, \"label\", 45);\n    i0.ɵɵtext(17, \"\\u4F4D\\u7F6E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CLocation, $event) || (ctx_r3.selectedMaterial.CLocation = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 47)(20, \"label\", 45);\n    i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CSelectName, $event) || (ctx_r3.selectedMaterial.CSelectName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 47)(24, \"label\", 45);\n    i0.ɵɵtext(25, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CImageCode, $event) || (ctx_r3.selectedMaterial.CImageCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 47)(28, \"label\", 48);\n    i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"textarea\", 49);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_textarea_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CDescription, $event) || (ctx_r3.selectedMaterial.CDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 47)(32, \"label\", 48);\n    i0.ɵɵtext(33, \"\\u50F9\\u683C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_ng_template_63_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.selectedMaterial.CPrice, $event) || (ctx_r3.selectedMaterial.CPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"nb-card-footer\", 27)(36, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_63_Template_button_click_36_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClose(ref_r13));\n    });\n    i0.ɵɵtext(37, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function BuildingMaterialComponent_ng_template_63_Template_button_click_38_listener() {\n      const ref_r13 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSubmit(ref_r13));\n    });\n    i0.ɵɵtext(39, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPart);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CLocation);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CSelectName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CImageCode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CDescription);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.selectedMaterial.CPrice);\n  }\n}\nexport let BuildingMaterialComponent = /*#__PURE__*/(() => {\n  class BuildingMaterialComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _buildCaseService, _materialService, _utilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._buildCaseService = _buildCaseService;\n      this._materialService = _materialService;\n      this._utilityService = _utilityService;\n      this.isNew = true;\n      this.listBuildCases = [];\n      this.materialOptions = [{\n        value: null,\n        label: '全部'\n      }, {\n        value: false,\n        label: '方案'\n      }, {\n        value: true,\n        label: '選樣'\n      }];\n      this.materialOptionsId = null;\n      this.CSelectName = \"\";\n      this.CImageCode = \"\";\n      this.CInfoImageCode = \"\";\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.listBuildCases = res.Entries?.length ? res.Entries : [];\n          this.selectedBuildCaseId = this.listBuildCases[0].cID;\n        }\n      }), mergeMap(() => this.getMaterialList())).subscribe();\n    }\n    getMaterialList(pageIndex = 1) {\n      return this._materialService.apiMaterialGetMaterialListPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPlanUse: this.materialOptionsId,\n          CSelectName: this.CSelectName,\n          CImageCode: this.CImageCode,\n          PageSize: this.pageSize,\n          PageIndex: pageIndex,\n          CInfoImageCode: this.CInfoImageCode\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.materialList = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    search() {\n      this.getMaterialList().subscribe();\n    }\n    pageChanged(pageIndex) {\n      this.getMaterialList(pageIndex).subscribe();\n    }\n    exportExelMaterialList() {\n      this._materialService.apiMaterialExportExcelMaterialListPost$Json({\n        body: this.selectedBuildCaseId\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries.FileByte) {\n            this._utilityService.downloadExcelFile(res.Entries.FileByte, '建材管理');\n          }\n        }\n      });\n    }\n    addNew(ref) {\n      this.isNew = true;\n      this.selectedMaterial = {};\n      this.dialogService.open(ref);\n    }\n    onSelectedMaterial(data, ref) {\n      this.isNew = false;\n      this.selectedMaterial = {\n        ...data\n      };\n      this.dialogService.open(ref);\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[名稱]', this.selectedMaterial.CName);\n      this.valid.required('[項目]', this.selectedMaterial.CPart);\n      this.valid.required('[位置]', this.selectedMaterial.CLocation);\n      this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName);\n      this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode);\n      this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30);\n      this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30);\n      this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30);\n      this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30);\n      this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30);\n    }\n    onSubmit(ref) {\n      console.log('selectedMaterial', this.selectedMaterial);\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._materialService.apiMaterialSaveMaterialAdminPost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CImageCode: this.selectedMaterial.CImageCode,\n          CName: this.selectedMaterial.CName,\n          CPart: this.selectedMaterial.CPart,\n          CLocation: this.selectedMaterial.CLocation,\n          CSelectName: this.selectedMaterial.CSelectName,\n          CDescription: this.selectedMaterial.CDescription,\n          CMaterialId: this.isNew ? null : this.selectedMaterial.CId,\n          CPrice: this.selectedMaterial.CPrice\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), mergeMap(() => this.getMaterialList()), finalize(() => ref.close())).subscribe();\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    detectFileExcel(event) {\n      const target = event.target;\n      const reader = new FileReader();\n      reader.readAsBinaryString(target.files[0]);\n      reader.onload = e => {\n        const binarystr = e.target.result;\n        const wb = XLSX.read(binarystr, {\n          type: 'binary'\n        });\n        const wsname = wb.SheetNames[0];\n        const ws = wb.Sheets[wsname];\n        let isValidFile = true;\n        const data = XLSX.utils.sheet_to_json(ws);\n        if (data && data.length > 0) {\n          data.forEach(x => {\n            if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] && (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {\n              isValidFile = false;\n            }\n          });\n          if (!isValidFile) {\n            this.message.showErrorMSG(\"导入文件时出现错误\");\n          } else {\n            this._materialService.apiMaterialImportExcelMaterialListPost$Json({\n              body: {\n                CBuildCaseId: this.selectedBuildCaseId,\n                CFile: target.files[0]\n              }\n            }).pipe(tap(res => {\n              if (res.StatusCode == 0) {\n                this.message.showSucessMSG(\"執行成功\");\n              } else {\n                this.message.showErrorMSG(res.Message);\n              }\n            }), mergeMap(() => this.getMaterialList(1))).subscribe();\n          }\n        } else {\n          this.message.showErrorMSG(\"文件中没有数据\");\n        }\n        event.target.value = null;\n      };\n    }\n    static {\n      this.ɵfac = function BuildingMaterialComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BuildingMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.MaterialService), i0.ɵɵdirectiveInject(i6.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: BuildingMaterialComponent,\n        selectors: [[\"ngx-building-material\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 65,\n        vars: 14,\n        consts: [[\"inputFile\", \"\"], [\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\", \"w-[22%]\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info mr-2 text-white\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"accept\", \".xls, .xlsx\", 1, \"hidden\", 3, \"change\"], [\"class\", \"btn btn-success ml-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1200px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-3\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-info\", \"mr-2\", \"text-white\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"btn\", \"btn-success\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-file-download\"], [4, \"ngIf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"w-[700px]\"], [1, \"px-4\"], [1, \"text-base\"], [1, \"w-full\", \"mt-3\"], [1, \"flex\", \"items-center\"], [1, \"required-field\", \"w-[150px]\"], [\"type\", \"text\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"w-[150px]\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [\"type\", \"number\", \"nbInput\", \"\", \"maxlength\", \"30\", 1, \"w-full\", \"!max-w-full\", \"p-2\", \"rounded\", \"text-[13px]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"]],\n        template: function BuildingMaterialComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n            i0.ɵɵtext(5, \" \\u53EF\\u8A2D\\u5B9A\\u55AE\\u7B46\\u6216\\u6279\\u6B21\\u532F\\u5165\\u8A2D\\u5B9A\\u5404\\u5340\\u57DF\\u53CA\\u65B9\\u6848\\u5C0D\\u61C9\\u4E4B\\u5EFA\\u6750\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 8);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(12, BuildingMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 6)(15, \"label\", 7);\n            i0.ɵɵtext(16, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"input\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CSelectName, $event) || (ctx.CSelectName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 6)(20, \"label\", 7);\n            i0.ɵɵtext(21, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"input\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_22_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CImageCode, $event) || (ctx.CImageCode = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 5)(24, \"div\", 6)(25, \"label\", 7);\n            i0.ɵɵtext(26, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function BuildingMaterialComponent_Template_input_ngModelChange_27_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CInfoImageCode, $event) || (ctx.CInfoImageCode = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14);\n            i0.ɵɵtemplate(30, BuildingMaterialComponent_button_30_Template, 3, 0, \"button\", 15)(31, BuildingMaterialComponent_button_31_Template, 3, 0, \"button\", 16)(32, BuildingMaterialComponent_button_32_Template, 2, 0, \"button\", 16);\n            i0.ɵɵelementStart(33, \"input\", 17, 0);\n            i0.ɵɵlistener(\"change\", function BuildingMaterialComponent_Template_input_change_33_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.detectFileExcel($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(35, BuildingMaterialComponent_button_35_Template, 3, 0, \"button\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"div\", 19)(37, \"table\", 20)(38, \"thead\")(39, \"tr\", 21)(40, \"th\", 22);\n            i0.ɵɵtext(41, \"\\u9805\\u6B21\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\", 22);\n            i0.ɵɵtext(43, \"\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\", 22);\n            i0.ɵɵtext(45, \"\\u9805\\u76EE\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\", 22);\n            i0.ɵɵtext(47, \"\\u4F4D\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"th\", 22);\n            i0.ɵɵtext(49, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"th\", 22);\n            i0.ɵɵtext(51, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"th\", 22);\n            i0.ɵɵtext(53, \"\\u793A\\u610F\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"th\", 23);\n            i0.ɵɵtext(55, \"\\u5EFA\\u6750\\u8AAA\\u660E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(56, BuildingMaterialComponent_th_56_Template, 2, 0, \"th\", 24);\n            i0.ɵɵelementStart(57, \"th\", 25);\n            i0.ɵɵtext(58, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"tbody\");\n            i0.ɵɵtemplate(60, BuildingMaterialComponent_tr_60_Template, 20, 10, \"tr\", 26);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(61, \"nb-card-footer\", 27)(62, \"ngx-pagination\", 28);\n            i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_CollectionSizeChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageSizeChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function BuildingMaterialComponent_Template_ngx_pagination_PageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(63, BuildingMaterialComponent_ng_template_63_Template, 40, 8, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CSelectName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CImageCode);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CInfoImageCode);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isExcelImport);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isExcelExport);\n            i0.ɵɵadvance(21);\n            i0.ɵɵproperty(\"ngIf\", ctx.materialList[0].CShowPrice == true);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.materialList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n          }\n        },\n        dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i8.DefaultValueAccessor, i8.NumberValueAccessor, i8.NgControlStatus, i8.MaxLengthValidator, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent]\n      });\n    }\n  }\n  return BuildingMaterialComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}