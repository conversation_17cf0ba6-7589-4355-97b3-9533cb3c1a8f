{"ast": null, "code": "/**\n * @license Angular v18.2.9\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵAnimationGroupPlayer, NoopAnimationPlayer, AUTO_STYLE, ɵPRE_STYLE, sequence, AnimationMetadataType, style } from '@angular/animations';\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError, Injectable } from '@angular/core';\nconst LINE_START = '\\n - ';\nfunction invalidTimingValue(exp) {\n  return new ɵRuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value \"${exp}\" is invalid.`);\n}\nfunction negativeStepValue() {\n  return new ɵRuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');\n}\nfunction negativeDelayValue() {\n  return new ɵRuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');\n}\nfunction invalidStyleParams(varName) {\n  return new ɵRuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode && `Unable to resolve the local animation param ${varName} in the given list of values`);\n}\nfunction invalidParamValue(varName) {\n  return new ɵRuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);\n}\nfunction invalidNodeType(nodeType) {\n  return new ɵRuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);\n}\nfunction invalidCssUnitValue(userProvidedProperty, value) {\n  return new ɵRuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n}\nfunction invalidTrigger() {\n  return new ɵRuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode && \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\");\n}\nfunction invalidDefinition() {\n  return new ɵRuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');\n}\nfunction invalidState(metadataName, missingSubs) {\n  return new ɵRuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode && `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);\n}\nfunction invalidStyleValue(value) {\n  return new ɵRuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);\n}\nfunction invalidProperty(prop) {\n  return new ɵRuntimeError(3009 /* RuntimeErrorCode.INVALID_PROPERTY */, ngDevMode && `The provided animation property \"${prop}\" is not a supported CSS property for animations`);\n}\nfunction invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {\n  return new ɵRuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode && `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`);\n}\nfunction invalidKeyframes() {\n  return new ɵRuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);\n}\nfunction invalidOffset() {\n  return new ɵRuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);\n}\nfunction keyframeOffsetsOutOfOrder() {\n  return new ɵRuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);\n}\nfunction keyframesMissingOffsets() {\n  return new ɵRuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);\n}\nfunction invalidStagger() {\n  return new ɵRuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);\n}\nfunction invalidQuery(selector) {\n  return new ɵRuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode && `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`);\n}\nfunction invalidExpression(expr) {\n  return new ɵRuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression \"${expr}\" is not supported`);\n}\nfunction invalidTransitionAlias(alias) {\n  return new ɵRuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value \"${alias}\" is not supported`);\n}\nfunction validationFailed(errors) {\n  return new ɵRuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction buildingFailed(errors) {\n  return new ɵRuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction triggerBuildFailed(name, errors) {\n  return new ɵRuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode && `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.map(err => err.message).join('\\n - ')}`);\n}\nfunction animationFailed(errors) {\n  return new ɵRuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode && `Unable to animate due to the following errors:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\nfunction registerFailed(errors) {\n  return new ɵRuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode && `Unable to build the animation due to the following errors: ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingOrDestroyedAnimation() {\n  return new ɵRuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && \"The requested animation doesn't exist or has already been destroyed\");\n}\nfunction createAnimationFailed(errors) {\n  return new ɵRuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode && `Unable to create the animation due to the following errors:${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingPlayer(id) {\n  return new ɵRuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);\n}\nfunction missingTrigger(phase, name) {\n  return new ɵRuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode && `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n}\nfunction missingEvent(name) {\n  return new ɵRuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode && `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n}\nfunction unsupportedTriggerEvent(phase, name) {\n  return new ɵRuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode && `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n}\nfunction unregisteredTrigger(name) {\n  return new ɵRuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`);\n}\nfunction triggerTransitionsFailed(errors) {\n  return new ɵRuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode && `Unable to process animations due to the following failed trigger transitions\\n ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction triggerParsingFailed(name, errors) {\n  return new ɵRuntimeError(3403 /* RuntimeErrorCode.TRIGGER_PARSING_FAILED */, ngDevMode && `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\nfunction transitionFailed(name, errors) {\n  return new ɵRuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\\n ${errors.map(err => err.message).join('\\n- ')}`);\n}\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nconst ANIMATABLE_PROP_SET = /*#__PURE__*/new Set(['-moz-outline-radius', '-moz-outline-radius-bottomleft', '-moz-outline-radius-bottomright', '-moz-outline-radius-topleft', '-moz-outline-radius-topright', '-ms-grid-columns', '-ms-grid-rows', '-webkit-line-clamp', '-webkit-text-fill-color', '-webkit-text-stroke', '-webkit-text-stroke-color', 'accent-color', 'all', 'backdrop-filter', 'background', 'background-color', 'background-position', 'background-size', 'block-size', 'border', 'border-block-end', 'border-block-end-color', 'border-block-end-width', 'border-block-start', 'border-block-start-color', 'border-block-start-width', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-width', 'border-color', 'border-end-end-radius', 'border-end-start-radius', 'border-image-outset', 'border-image-slice', 'border-image-width', 'border-inline-end', 'border-inline-end-color', 'border-inline-end-width', 'border-inline-start', 'border-inline-start-color', 'border-inline-start-width', 'border-left', 'border-left-color', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-width', 'border-start-end-radius', 'border-start-start-radius', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-width', 'border-width', 'bottom', 'box-shadow', 'caret-color', 'clip', 'clip-path', 'color', 'column-count', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-width', 'column-width', 'columns', 'filter', 'flex', 'flex-basis', 'flex-grow', 'flex-shrink', 'font', 'font-size', 'font-size-adjust', 'font-stretch', 'font-variation-settings', 'font-weight', 'gap', 'grid-column-gap', 'grid-gap', 'grid-row-gap', 'grid-template-columns', 'grid-template-rows', 'height', 'inline-size', 'input-security', 'inset', 'inset-block', 'inset-block-end', 'inset-block-start', 'inset-inline', 'inset-inline-end', 'inset-inline-start', 'left', 'letter-spacing', 'line-clamp', 'line-height', 'margin', 'margin-block-end', 'margin-block-start', 'margin-bottom', 'margin-inline-end', 'margin-inline-start', 'margin-left', 'margin-right', 'margin-top', 'mask', 'mask-border', 'mask-position', 'mask-size', 'max-block-size', 'max-height', 'max-inline-size', 'max-lines', 'max-width', 'min-block-size', 'min-height', 'min-inline-size', 'min-width', 'object-position', 'offset', 'offset-anchor', 'offset-distance', 'offset-path', 'offset-position', 'offset-rotate', 'opacity', 'order', 'outline', 'outline-color', 'outline-offset', 'outline-width', 'padding', 'padding-block-end', 'padding-block-start', 'padding-bottom', 'padding-inline-end', 'padding-inline-start', 'padding-left', 'padding-right', 'padding-top', 'perspective', 'perspective-origin', 'right', 'rotate', 'row-gap', 'scale', 'scroll-margin', 'scroll-margin-block', 'scroll-margin-block-end', 'scroll-margin-block-start', 'scroll-margin-bottom', 'scroll-margin-inline', 'scroll-margin-inline-end', 'scroll-margin-inline-start', 'scroll-margin-left', 'scroll-margin-right', 'scroll-margin-top', 'scroll-padding', 'scroll-padding-block', 'scroll-padding-block-end', 'scroll-padding-block-start', 'scroll-padding-bottom', 'scroll-padding-inline', 'scroll-padding-inline-end', 'scroll-padding-inline-start', 'scroll-padding-left', 'scroll-padding-right', 'scroll-padding-top', 'scroll-snap-coordinate', 'scroll-snap-destination', 'scrollbar-color', 'shape-image-threshold', 'shape-margin', 'shape-outside', 'tab-size', 'text-decoration', 'text-decoration-color', 'text-decoration-thickness', 'text-emphasis', 'text-emphasis-color', 'text-indent', 'text-shadow', 'text-underline-offset', 'top', 'transform', 'transform-origin', 'translate', 'vertical-align', 'visibility', 'width', 'word-spacing', 'z-index', 'zoom']);\nfunction optimizeGroupPlayer(players) {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new ɵAnimationGroupPlayer(players);\n  }\n}\nfunction normalizeKeyframes$1(normalizer, keyframes, preStyles = new Map(), postStyles = new Map()) {\n  const errors = [];\n  const normalizedKeyframes = [];\n  let previousOffset = -1;\n  let previousKeyframe = null;\n  keyframes.forEach(kf => {\n    const offset = kf.get('offset');\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe = isSameOffset && previousKeyframe || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case ɵPRE_STYLE:\n            normalizedValue = preStyles.get(prop);\n            break;\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop);\n            break;\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n            break;\n        }\n      }\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n  return normalizedKeyframes;\n}\nfunction listenOnPlayer(player, eventName, event, callback) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\nfunction copyAnimationEvent(e, phaseName, player) {\n  const totalTime = player.totalTime;\n  const disabled = player.disabled ? true : false;\n  const event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n  const data = e['_data'];\n  if (data != null) {\n    event['_data'] = data;\n  }\n  return event;\n}\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0, disabled) {\n  return {\n    element,\n    triggerName,\n    fromState,\n    toState,\n    phaseName,\n    totalTime,\n    disabled: !!disabled\n  };\n}\nfunction getOrSetDefaultValue(map, key, defaultValue) {\n  let value = map.get(key);\n  if (!value) {\n    map.set(key, value = defaultValue);\n  }\n  return value;\n}\nfunction parseTimelineCommand(command) {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\nconst documentElement = /* @__PURE__ */(() => typeof document === 'undefined' ? null : document.documentElement)();\nfunction getParentElement(element) {\n  const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n  if (parent === documentElement) {\n    return null;\n  }\n  return parent;\n}\nfunction containsVendorPrefix(prop) {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet _CACHED_BODY = null;\nlet _IS_WEBKIT = false;\nfunction validateStyleProperty(prop) {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY.style ? 'WebkitAppearance' in _CACHED_BODY.style : false;\n  }\n  let result = true;\n  if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY.style;\n    }\n  }\n  return result;\n}\nfunction validateWebAnimatableStyleProperty(prop) {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\nfunction getBodyNode() {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\nfunction containsElement(elm1, elm2) {\n  while (elm2) {\n    if (elm2 === elm1) {\n      return true;\n    }\n    elm2 = getParentElement(elm2);\n  }\n  return false;\n}\nfunction invokeQuery(element, selector, multi) {\n  if (multi) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  const elem = element.querySelector(selector);\n  return elem ? [elem] : [];\n}\nfunction hypenatePropsKeys(original) {\n  const newMap = new Map();\n  original.forEach((val, prop) => {\n    const newProp = prop.replace(/([a-z])([A-Z])/g, '$1-$2');\n    newMap.set(newProp, val);\n  });\n  return newMap;\n}\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nlet NoopAnimationDriver = /*#__PURE__*/(() => {\n  class NoopAnimationDriver {\n    /**\n     * @returns Whether `prop` is a valid CSS property\n     */\n    validateStyleProperty(prop) {\n      return validateStyleProperty(prop);\n    }\n    /**\n     *\n     * @returns Whether elm1 contains elm2.\n     */\n    containsElement(elm1, elm2) {\n      return containsElement(elm1, elm2);\n    }\n    /**\n     * @returns Rhe parent of the given element or `null` if the element is the `document`\n     */\n    getParentElement(element) {\n      return getParentElement(element);\n    }\n    /**\n     * @returns The result of the query selector on the element. The array will contain up to 1 item\n     *     if `multi` is  `false`.\n     */\n    query(element, selector, multi) {\n      return invokeQuery(element, selector, multi);\n    }\n    /**\n     * @returns The `defaultValue` or empty string\n     */\n    computeStyle(element, prop, defaultValue) {\n      return defaultValue || '';\n    }\n    /**\n     * @returns An `NoopAnimationPlayer`\n     */\n    animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n      return new NoopAnimationPlayer(duration, delay);\n    }\n    static {\n      this.ɵfac = function NoopAnimationDriver_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || NoopAnimationDriver)();\n      };\n    }\n    static {\n      this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n        token: NoopAnimationDriver,\n        factory: NoopAnimationDriver.ɵfac\n      });\n    }\n  }\n  return NoopAnimationDriver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @publicApi\n */\nclass AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static {\n    this.NOOP = /*#__PURE__*/new NoopAnimationDriver();\n  }\n}\nclass AnimationStyleNormalizer {}\nclass NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return propertyName;\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    return value;\n  }\n}\nconst ONE_SECOND = 1000;\nconst SUBSTITUTION_EXPR_START = '{{';\nconst SUBSTITUTION_EXPR_END = '}}';\nconst ENTER_CLASSNAME = 'ng-enter';\nconst LEAVE_CLASSNAME = 'ng-leave';\nconst NG_TRIGGER_CLASSNAME = 'ng-trigger';\nconst NG_TRIGGER_SELECTOR = '.ng-trigger';\nconst NG_ANIMATING_CLASSNAME = 'ng-animating';\nconst NG_ANIMATING_SELECTOR = '.ng-animating';\nfunction resolveTimingValue(value) {\n  if (typeof value == 'number') return value;\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\nfunction _convertTimeValueToMS(value, unit) {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:\n      // ms or something else\n      return value;\n  }\n}\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n  return timings.hasOwnProperty('duration') ? timings : parseTimeExpression(timings, errors, allowNegativeValues);\n}\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration;\n  let delay = 0;\n  let easing = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {\n        duration: 0,\n        delay: 0,\n        easing: ''\n      };\n    }\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction normalizeKeyframes(keyframes) {\n  if (!keyframes.length) {\n    return [];\n  }\n  if (keyframes[0] instanceof Map) {\n    return keyframes;\n  }\n  return keyframes.map(kf => new Map(Object.entries(kf)));\n}\nfunction normalizeStyles(styles) {\n  return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\nfunction setStyles(element, styles, formerStyles) {\n  styles.forEach((val, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    if (formerStyles && !formerStyles.has(prop)) {\n      formerStyles.set(prop, element.style[camelProp]);\n    }\n    element.style[camelProp] = val;\n  });\n}\nfunction eraseStyles(element, styles) {\n  styles.forEach((_, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    element.style[camelProp] = '';\n  });\n}\nfunction normalizeAnimationEntry(steps) {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps;\n}\nfunction validateStyleParams(value, options, errors) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\nconst PARAM_REGEX = /*#__PURE__*/new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\nfunction extractStyleParams(value) {\n  let params = [];\n  if (typeof value === 'string') {\n    let match;\n    while (match = PARAM_REGEX.exec(value)) {\n      params.push(match[1]);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\nfunction interpolateParams(value, params, errors) {\n  const original = `${value}`;\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\nfunction camelCaseToDashCase(input) {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n  return duration === 0 || delay === 0;\n}\nfunction balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe.set(prop, val);\n    });\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach(prop => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n  return keyframes;\n}\nfunction visitDslNode(visitor, node, context) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\nfunction computeStyle(element, prop) {\n  return window.getComputedStyle(element)[prop];\n}\nconst DIMENSIONAL_PROP_SET = /*#__PURE__*/new Set(['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight', 'left', 'top', 'bottom', 'right', 'fontSize', 'outlineWidth', 'outlineOffset', 'paddingTop', 'paddingLeft', 'paddingBottom', 'paddingRight', 'marginTop', 'marginLeft', 'marginBottom', 'marginRight', 'borderRadius', 'borderWidth', 'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth', 'textIndent', 'perspective']);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return dashCaseToCamelCase(propertyName);\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    let unit = '';\n    const strVal = value.toString().trim();\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\nfunction createListOfWarnings(warnings) {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings.filter(Boolean).map(warning => warning).join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n  (typeof ngDevMode === 'undefined' || ngDevMode) && console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n  (typeof ngDevMode === 'undefined' || ngDevMode) && console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n  (typeof ngDevMode === 'undefined' || ngDevMode) && console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction triggerParsingWarnings(name, warnings) {\n  (typeof ngDevMode === 'undefined' || ngDevMode) && console.warn(`Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n  const expressions = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue.split(/\\s*,\\s*/).forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(transitionValue);\n  }\n  return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\nfunction parseAnimationAlias(alias, errors) {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = /*#__PURE__*/new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n  return (fromState, toState) => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n    return lhsMatch && rhsMatch;\n  };\n}\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /*#__PURE__*/new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n  constructor(_driver) {\n    this._driver = _driver;\n  }\n  build(metadata, errors, warnings) {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = visitDslNode(this, normalizeAnimationEntry(metadata), context);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [...context.unsupportedCSSPropertiesFound.keys()]);\n      }\n    }\n    return ast;\n  }\n  _resetContextStyleTimingState(context) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n  visitTrigger(metadata, context) {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states = [];\n    const transitions = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null\n    };\n  }\n  visitState(metadata, context) {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = metadata.options && metadata.options.params || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set();\n      const params = astParams || {};\n      styleAst.styles.forEach(style => {\n        if (style instanceof Map) {\n          style.forEach(value => {\n            extractStyleParams(value).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {\n        params: astParams\n      } : null\n    };\n  }\n  visitTransition(metadata, context) {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitSequence(metadata, context) {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitGroup(metadata, context) {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimate(metadata, context) {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst;\n    let styleMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n  visitStyle(metadata, context) {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n  _makeStyleAst(metadata, context) {\n    const styles = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n    let containsDynamicStyles = false;\n    let collectedEasing = null;\n    styles.forEach(styleData => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing');\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null\n    };\n  }\n  _validateStyleAst(ast, context) {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n    ast.styles.forEach(tuple => {\n      if (typeof tuple === 'string') return;\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime && endTime <= collectedEntry.endTime) {\n            context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n            updateCollectedStyle = false;\n          }\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {\n            startTime,\n            endTime\n          });\n        }\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n  visitKeyframes(metadata, context) {\n    const ast = {\n      type: AnimationMetadataType.Keyframes,\n      styles: [],\n      options: null\n    };\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n    const MAX_KEYFRAME_OFFSET = 1;\n    let totalKeyframesWithOffsets = 0;\n    const offsets = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset = 0;\n    const keyframes = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? i == limit ? 1 : generatedOffset * i : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n      ast.styles.push(kf);\n    });\n    return ast;\n  }\n  visitReference(metadata, context) {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateChild(metadata, context) {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateRef(metadata, context) {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitQuery(metadata, context) {\n    const parentSelector = context.currentQuerySelector;\n    const options = metadata.options || {};\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length ? parentSelector + ' ' + selector : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitStagger(metadata, context) {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings = metadata.timings === 'full' ? {\n      duration: 0,\n      delay: 0,\n      easing: 'full'\n    } : resolveTiming(metadata.timings, context.errors, true);\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null\n    };\n  }\n}\nfunction normalizeSelector(selector) {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR).replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.slice(1)).replace(/:animating/g, NG_ANIMATING_SELECTOR);\n  return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n  return obj ? {\n    ...obj\n  } : null;\n}\nclass AnimationAstBuilderContext {\n  constructor(errors) {\n    this.errors = errors;\n    this.queryCount = 0;\n    this.depCount = 0;\n    this.currentTransition = null;\n    this.currentQuery = null;\n    this.currentQuerySelector = null;\n    this.currentAnimateTimings = null;\n    this.currentTime = 0;\n    this.collectedStyles = new Map();\n    this.options = null;\n    this.unsupportedCSSPropertiesFound = new Set();\n  }\n}\nfunction consumeOffset(styles) {\n  if (typeof styles == 'string') return null;\n  let offset = null;\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset'));\n    obj.delete('offset');\n  }\n  return offset;\n}\nfunction constructTimingAst(value, errors) {\n  if (value.hasOwnProperty('duration')) {\n    return value;\n  }\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n  const strValue = value;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '');\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast;\n  }\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n  if (options) {\n    options = {\n      ...options\n    };\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']);\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n  return {\n    type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline\n  };\n}\nclass ElementInstructionMap {\n  constructor() {\n    this._map = new Map();\n  }\n  get(element) {\n    return this._map.get(element) || [];\n  }\n  append(element, instructions) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n  has(element) {\n    return this._map.has(element);\n  }\n  clear() {\n    this._map.clear();\n  }\n}\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /*#__PURE__*/new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /*#__PURE__*/new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n  buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n    visitDslNode(this, ast, context);\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n  visitTrigger(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitState(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitTransition(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitAnimateChild(ast, context) {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitAnimateRef(ast, context) {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n  _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue = typeof animationDelay === 'number' ? animationDelay : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n  _visitSubInstructions(instructions, context, options) {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n    return furthestTime;\n  }\n  visitReference(ast, context) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n  visitSequence(ast, context) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx));\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitGroup(ast, context) {\n    const innerTimelines = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n  _visitTiming(ast, context) {\n    if (ast.dynamic) {\n      const strValue = ast.strValue;\n      const timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {\n        duration: ast.duration,\n        delay: ast.delay,\n        easing: ast.easing\n      };\n    }\n  }\n  visitAnimate(ast, context) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style, context);\n      timeline.applyStylesToKeyframe();\n    }\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n  visitStyle(ast, context) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings;\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n    const easing = timings && timings.easing || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n    context.previousNode = ast;\n  }\n  visitKeyframes(ast, context) {\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const startTime = context.currentTimeline.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n    ast.styles.forEach(step => {\n      const offset = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n  visitQuery(ast, context) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = ast.options || {};\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    if (delay && (context.previousNode.type === AnimationMetadataType.Style || startTime == 0 && context.currentTimeline.hasCurrentStyleProperties())) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n      visitDslNode(this, ast.animation, innerContext);\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n    context.previousNode = ast;\n  }\n  visitStagger(ast, context) {\n    const parentContext = context.parentContext;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime = tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n  constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n    this._driver = _driver;\n    this.element = element;\n    this.subInstructions = subInstructions;\n    this._enterClassName = _enterClassName;\n    this._leaveClassName = _leaveClassName;\n    this.errors = errors;\n    this.timelines = timelines;\n    this.parentContext = null;\n    this.currentAnimateTimings = null;\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.subContextCount = 0;\n    this.options = {};\n    this.currentQueryIndex = 0;\n    this.currentQueryTotal = 0;\n    this.currentStaggerTime = 0;\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n  get params() {\n    return this.options.params;\n  }\n  updateOptions(options, skipIfExists) {\n    if (!options) return;\n    const newOptions = options;\n    let optionsToUpdate = this.options;\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n    }\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate = optionsToUpdate.params;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n  _copyOptions() {\n    const options = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params = options['params'] = {};\n        Object.keys(oldParams).forEach(name => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n  createSubContext(options = null, element, newTime) {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n  transformIntoNewTimeline(newTime) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n  appendInstructionToTimeline(instruction, duration, delay) {\n    const updatedTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n  incrementTime(time) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n  delayNextStep(delay) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n  invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n    let results = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\nclass TimelineBuilder {\n  constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n    this._driver = _driver;\n    this.element = element;\n    this.startTime = startTime;\n    this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n    this.duration = 0;\n    this.easing = null;\n    this._previousKeyframe = new Map();\n    this._currentKeyframe = new Map();\n    this._keyframes = new Map();\n    this._styleSummary = new Map();\n    this._localTimelineStyles = new Map();\n    this._pendingStyles = new Map();\n    this._backFill = new Map();\n    this._currentEmptyStepKeyframe = null;\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map();\n    }\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n  containsAnimation() {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n  hasCurrentStyleProperties() {\n    return this._currentKeyframe.size > 0;\n  }\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n  delayNextStep(delay) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n  fork(element, currentTime) {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n  _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration);\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n  forwardTime(time) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n  _updateStyle(prop, value) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {\n      time: this.currentTime,\n      value\n    });\n  }\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n  applyEmptyStep(easing) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n  setStyles(input, easing, errors, options) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = options && options.params || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n  get properties() {\n    const properties = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n  mergeTimelineCollectedStyles(timeline) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n  buildKeyframes() {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set();\n    const postStyleProps = new Set();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n    let finalKeyframes = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === ɵPRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n    const preProps = [...preStyleProps.values()];\n    const postProps = [...postStyleProps.values()];\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n    return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n  }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n  constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n    super(driver, element, timings.delay);\n    this.keyframes = keyframes;\n    this.preStyleProps = preStyleProps;\n    this.postStyleProps = postStyleProps;\n    this._stretchStartingKeyframe = _stretchStartingKeyframe;\n    this.timings = {\n      duration: timings.duration,\n      delay: timings.delay,\n      easing: timings.easing\n    };\n  }\n  containsAnimation() {\n    return this.keyframes.length > 1;\n  }\n  buildKeyframes() {\n    let keyframes = this.keyframes;\n    let {\n      delay,\n      duration,\n      easing\n    } = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n               delay=1000, duration=1000, keyframes = 0 .5 1\n               turns into\n               delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset');\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n      keyframes = newKeyframes;\n    }\n    return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n  }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n  const styles = new Map();\n  let allProperties;\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n  return {\n    type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n  constructor(_triggerName, ast, _stateStyles) {\n    this._triggerName = _triggerName;\n    this.ast = ast;\n    this._stateStyles = _stateStyles;\n  }\n  match(currentState, nextState, element, params) {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n  buildStyles(stateName, params, errors) {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n  build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n    const errors = [];\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n    const queriedElements = new Set();\n    const preStyleMap = new Map();\n    const postStyleMap = new Map();\n    const isRemoval = nextState === 'void';\n    const animationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay\n    };\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n    let totalTime = 0;\n    timelines.forEach(tl => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n    if (errors.length) {\n      return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n      tl.preStyleProps.forEach(prop => preProps.add(prop));\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n      tl.postStyleProps.forEach(prop => postProps.add(prop));\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n    return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n  }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n  const allowedNonAnimatableProps = new Set([\n  // 'easing' is a utility/synthetic prop we use to represent\n  // easing functions, it represents a property of the animation\n  // which is not animatable but different values can be used\n  // in different steps\n  'easing']);\n  const invalidNonAnimatableProps = new Set();\n  timelines.forEach(({\n    keyframes\n  }) => {\n    const nonAnimatablePropsInitialValues = new Map();\n    keyframes.forEach(keyframe => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` + ' not animatable properties: ' + Array.from(invalidNonAnimatableProps).join(', ') + '\\n' + '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n  }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n  const result = {\n    ...defaults\n  };\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\nclass AnimationStateStyles {\n  constructor(styles, defaultParams, normalizer) {\n    this.styles = styles;\n    this.defaultParams = defaultParams;\n    this.normalizer = normalizer;\n  }\n  buildStyles(params, errors) {\n    const finalStyles = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\nfunction buildTrigger(name, ast, normalizer) {\n  return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n  constructor(name, ast, _normalizer) {\n    this.name = name;\n    this.ast = ast;\n    this._normalizer = _normalizer;\n    this.transitionFactories = [];\n    this.states = new Map();\n    ast.states.forEach(ast => {\n      const defaultParams = ast.options && ast.options.params || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);\n  }\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n  matchTransition(currentState, nextState, element, params) {\n    const entry = this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n  matchStyles(currentState, params, errors) {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n  const matchers = [(fromState, toState) => true];\n  const animation = {\n    type: AnimationMetadataType.Sequence,\n    steps: [],\n    options: null\n  };\n  const transition = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1));\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2));\n  }\n}\nconst EMPTY_INSTRUCTION_MAP = /*#__PURE__*/new ElementInstructionMap();\nclass TimelineAnimationEngine {\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._animations = new Map();\n    this._playersById = new Map();\n    this.players = [];\n  }\n  register(id, metadata) {\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (warnings.length) {\n        warnRegister(warnings);\n      }\n      this._animations.set(id, ast);\n    }\n  }\n  _buildPlayer(i, preStyles, postStyles) {\n    const element = i.element;\n    const keyframes = normalizeKeyframes$1(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n  create(id, element, options = {}) {\n    const errors = [];\n    const ast = this._animations.get(id);\n    let instructions;\n    const autoStylesMap = new Map();\n    if (ast) {\n      instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n        inst.postStyleProps.forEach(prop => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n    this.players.push(player);\n    return player;\n  }\n  destroy(id) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n  _getPlayer(id) {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n  listen(id, element, eventName, callback) {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n  command(id, element, command, args) {\n    if (command == 'register') {\n      this.register(id, args[0]);\n      return;\n    }\n    if (command == 'create') {\n      const options = args[0] || {};\n      this.create(id, element, options);\n      return;\n    }\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0]));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n  get params() {\n    return this.options.params;\n  }\n  constructor(input, namespaceId = '') {\n    this.namespaceId = namespaceId;\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {\n        value,\n        ...options\n      } = input;\n      this.options = options;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n  absorbOptions(options) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = /*#__PURE__*/new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n  constructor(id, hostElement, _engine) {\n    this.id = id;\n    this.hostElement = hostElement;\n    this._engine = _engine;\n    this.players = [];\n    this._triggers = new Map();\n    this._queue = [];\n    this._elementListeners = new Map();\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n  listen(element, name, phase, callback) {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {\n      name,\n      phase,\n      callback\n    };\n    listeners.push(data);\n    const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n  register(name, ast) {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n  _getTrigger(name) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n  trigger(element, triggerName, value, defaultToFallback = true) {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = new Map());\n    }\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n    triggersWithStates.set(triggerName, toState);\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n    const isRemoval = toState.value === VOID_VALUE;\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n    const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n    let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition\n    });\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n    this.players.push(player);\n    playersOnElement.push(player);\n    return player;\n  }\n  deregister(name) {\n    this._triggers.delete(name);\n    this._engine.statesByElement.forEach(stateMap => stateMap.delete(name));\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(element, listeners.filter(entry => {\n        return entry.name != name;\n      }));\n    });\n  }\n  clearElementCache(element) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n  _signalRemovalForInnerTriggers(rootElement, context) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() => elements.forEach(elm => this.clearElementCache(elm)));\n  }\n  triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map();\n    if (triggerStates) {\n      const players = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  prepareLeaveAnimationListeners(element) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n        const trigger = this._triggers.get(triggerName);\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n  removeNode(element, context) {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n  insertNode(element, parent) {\n    addClass(element, this._hostClassName);\n  }\n  drainQueuedTransitions(microtaskId) {\n    const instructions = [];\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach(listener => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            baseEvent['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n    this._queue = [];\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n  destroy(context) {\n    this.players.forEach(p => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\nclass TransitionAnimationEngine {\n  /** @internal */\n  _onRemovalComplete(element, context) {\n    this.onRemovalComplete(element, context);\n  }\n  constructor(bodyNode, driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this.driver = driver;\n    this._normalizer = _normalizer;\n    this.players = [];\n    this.newHostElements = new Map();\n    this.playersByElement = new Map();\n    this.playersByQueriedElement = new Map();\n    this.statesByElement = new Map();\n    this.disabledNodes = new Set();\n    this.totalAnimations = 0;\n    this.totalQueuedPlayers = 0;\n    this._namespaceLookup = {};\n    this._namespaceList = [];\n    this._flushFns = [];\n    this._whenQuietFns = [];\n    this.namespacesByHostElement = new Map();\n    this.collectedEnterElements = [];\n    this.collectedLeaveElements = [];\n    // this method is designed to be overridden by the code that uses this engine\n    this.onRemovalComplete = (element, context) => {};\n  }\n  get queuedPlayers() {\n    const players = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n  createNamespace(namespaceId, hostElement) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n  _balanceNamespaceList(ns, hostElement) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n  register(namespaceId, hostElement) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n  registerTrigger(namespaceId, name, trigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n  destroy(namespaceId, context) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n  _fetchNamespace(id) {\n    return this._namespaceLookup[id];\n  }\n  fetchNamespacesByElement(element) {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n  trigger(namespaceId, element, name, value) {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n  insertNode(namespaceId, element, parent, insertBefore) {\n    if (!isElementNode(element)) return;\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n  collectEnterElement(element) {\n    this.collectedEnterElements.push(element);\n  }\n  markElementAsDisabled(element, value) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n  removeNode(namespaceId, element, context) {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n  markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues\n    };\n  }\n  listen(namespaceId, element, name, phase, callback) {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n  _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n    return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n  destroyInnerAnimations(containerElement) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n    if (this.playersByQueriedElement.size == 0) return;\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n  destroyActiveAnimationsForElement(element) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n  finishActiveQueriedAnimationOnElement(element) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n  whenRenderingDone() {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n  processLeaveNode(element) {\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n  flush(microtaskId = -1) {\n    let players = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n    if (this._namespaceList.length && (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach(fn => fn());\n        });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n  reportError(errors) {\n    throw triggerTransitionsFailed(errors);\n  }\n  _flushAnimations(cleanupFns, microtaskId) {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers = [];\n    const skippedPlayersMap = new Map();\n    const queuedInstructions = [];\n    const queriedElements = new Map();\n    const allPreStyleElements = new Map();\n    const allPostStyleElements = new Map();\n    const disabledElementsSet = new Set();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    const allLeaveNodes = [];\n    const mergedLeaveNodes = new Set();\n    const leaveNodesWithoutAnimations = new Set();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG];\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n    const leaveNodeMapIds = new Map();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      allLeaveNodes.forEach(element => {\n        this.processLeaveNode(element);\n      });\n    });\n    const allPlayers = [];\n    const erroneousTransitions = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG];\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (details.previousTriggersValues && details.previousTriggersValues.has(entry.triggerName)) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName);\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName);\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n            player.destroy();\n            return;\n          }\n        }\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element);\n        const enterClassName = enterNodeMapIds.get(element);\n        const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines = [];\n        instruction.timelines.forEach(tl => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n        subTimelines.append(element, instruction.timelines);\n        const tuple = {\n          instruction,\n          player,\n          element\n        };\n        queuedInstructions.push(tuple);\n        instruction.queriedElements.forEach(element => getOrSetDefaultValue(queriedElements, element, []).push(player));\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal = allPreStyleElements.get(element);\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set());\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal = allPostStyleElements.get(element);\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set());\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n    if (erroneousTransitions.length) {\n      const errors = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n      });\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n    const allPreviousPlayersMap = new Map();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, ɵPRE_STYLE);\n    });\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n    const rootPlayers = [];\n    const subPlayers = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {\n        element,\n        player,\n        instruction\n      } = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd = [];\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n        const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n        player.setRealPlayer(innerPlayer);\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach(player => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG];\n      removeClass(element, LEAVE_CLASSNAME);\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n      let players = [];\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      const activePlayers = players.filter(p => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n    return rootPlayers;\n  }\n  afterFlush(callback) {\n    this._flushFns.push(callback);\n  }\n  afterFlushAnimationsDone(callback) {\n    this._whenQuietFns.push(callback);\n  }\n  _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n    let players = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n  _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer();\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n  _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers = [];\n    const allConsumedElements = new Set();\n    const allSubElements = new Set();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map(p => p.getRealPlayer())).filter(p => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p;\n        return pp.element ? pp.element === element : false;\n      });\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes$1(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n      return player;\n    });\n    allQueriedPlayers.forEach(player => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(element => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n    return player;\n  }\n  _buildPlayer(instruction, keyframes, previousPlayers) {\n    if (keyframes.length > 0) {\n      return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n    }\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\nclass TransitionAnimationPlayer {\n  constructor(namespaceId, triggerName, element) {\n    this.namespaceId = namespaceId;\n    this.triggerName = triggerName;\n    this.element = element;\n    this._player = new NoopAnimationPlayer();\n    this._containsRealPlayer = false;\n    this._queuedCallbacks = new Map();\n    this.destroyed = false;\n    this.parentPlayer = null;\n    this.markedForDestroy = false;\n    this.disabled = false;\n    this.queued = true;\n    this.totalTime = 0;\n  }\n  setRealPlayer(player) {\n    if (this._containsRealPlayer) return;\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    this.queued = false;\n  }\n  getRealPlayer() {\n    return this._player;\n  }\n  overrideTotalTime(totalTime) {\n    this.totalTime = totalTime;\n  }\n  syncPlayerEvents(player) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n  _queueEvent(name, callback) {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n  onDone(fn) {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n  onStart(fn) {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n  onDestroy(fn) {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n  init() {\n    this._player.init();\n  }\n  hasStarted() {\n    return this.queued ? false : this._player.hasStarted();\n  }\n  play() {\n    !this.queued && this._player.play();\n  }\n  pause() {\n    !this.queued && this._player.pause();\n  }\n  restart() {\n    !this.queued && this._player.restart();\n  }\n  finish() {\n    this._player.finish();\n  }\n  destroy() {\n    this.destroyed = true;\n    this._player.destroy();\n  }\n  reset() {\n    !this.queued && this._player.reset();\n  }\n  setPosition(p) {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n  getPosition() {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\nfunction isElementNode(node) {\n  return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n  return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n  const cloakVals = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n  const failedElements = [];\n  elementPropsMap.forEach((props, element) => {\n    const styles = new Map();\n    props.forEach(prop => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n  return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n  const rootMap = new Map();\n  roots.forEach(root => rootMap.set(root, []));\n  if (nodes.length == 0) return rootMap;\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map();\n  function getRoot(node) {\n    if (!node) return NULL_NODE;\n    let root = localRootMap.get(node);\n    if (root) return root;\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n    localRootMap.set(node, root);\n    return root;\n  }\n  nodes.forEach(node => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root).push(node);\n    }\n  });\n  return rootMap;\n}\nfunction addClass(element, className) {\n  element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n  element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n  const finalPlayers = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof ɵAnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\nfunction objEquals(a, b) {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach(data => preEntry.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n  allPostStyleElements.delete(element);\n  return true;\n}\nclass AnimationEngine {\n  constructor(doc, _driver, _normalizer) {\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._triggerCache = {};\n    // this method is designed to be overridden by the code that uses this engine\n    this.onRemovalComplete = (element, context) => {};\n    this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n    this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n  }\n  registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors = [];\n      const warnings = [];\n      const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (warnings.length) {\n        warnTriggerBuild(name, warnings);\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n  register(namespaceId, hostElement) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n  destroy(namespaceId, context) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n  onInsert(namespaceId, element, parent, insertBefore) {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n  onRemove(namespaceId, element, context) {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n  disableAnimations(element, disable) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n  process(namespaceId, element, property, value) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value;\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n  listen(namespaceId, element, eventName, eventPhase, callback) {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n  flush(microtaskId = -1) {\n    this._transitionEngine.flush(microtaskId);\n  }\n  get players() {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n  whenRenderingDone() {\n    return this._transitionEngine.whenRenderingDone();\n  }\n  afterFlushAnimationsDone(cb) {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n  let startStyles = null;\n  let endStyles = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nclass SpecialCasedStyles {\n  static {\n    this.initialStylesByElement = /*#__PURE__*/new WeakMap();\n  }\n  constructor(_element, _startStyles, _endStyles) {\n    this._element = _element;\n    this._startStyles = _startStyles;\n    this._endStyles = _endStyles;\n    this._state = 0 /* SpecialCasedStylesState.Pending */;\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = new Map());\n    }\n    this._initialStyles = initialStyles;\n  }\n  start() {\n    if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  finish() {\n    this.start();\n    if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  destroy() {\n    this.finish();\n    if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n    }\n  }\n}\nfunction filterNonAnimatableStyles(styles) {\n  let result = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\nfunction isNonAnimatableStyle(prop) {\n  return prop === 'display' || prop === 'position';\n}\nclass WebAnimationsPlayer {\n  constructor(element, keyframes, options, _specialStyles) {\n    this.element = element;\n    this.keyframes = keyframes;\n    this.options = options;\n    this._specialStyles = _specialStyles;\n    this._onDoneFns = [];\n    this._onStartFns = [];\n    this._onDestroyFns = [];\n    this._initialized = false;\n    this._finished = false;\n    this._started = false;\n    this._destroyed = false;\n    // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n    // and are used to reset the fns to their original values upon reset()\n    // (since the _onStartFns and _onDoneFns get deleted after they are called)\n    this._originalOnDoneFns = [];\n    this._originalOnStartFns = [];\n    this.time = 0;\n    this.parentPlayer = null;\n    this.currentSnapshot = new Map();\n    this._duration = options['duration'];\n    this._delay = options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  init() {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n  _buildPlayer() {\n    if (this._initialized) return;\n    this._initialized = true;\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n  _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n  _convertKeyframesToObject(keyframes) {\n    const kfs = [];\n    keyframes.forEach(frame => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n  /** @internal */\n  _triggerWebAnimation(element, keyframes, options) {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  play() {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n  pause() {\n    this.init();\n    this.domPlayer.pause();\n  }\n  finish() {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n  reset() {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n  restart() {\n    this.reset();\n    this.play();\n  }\n  hasStarted() {\n    return this._started;\n  }\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  setPosition(p) {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n  getPosition() {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n  get totalTime() {\n    return this._delay + this._duration;\n  }\n  beforeDestroy() {\n    const styles = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\nclass WebAnimationsDriver {\n  validateStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n  validateAnimatableStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  computeStyle(element, prop, defaultValue) {\n    return computeStyle(element, prop);\n  }\n  animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions = {\n      duration,\n      delay,\n      fill\n    };\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n    const previousStyles = new Map();\n    const previousWebAnimationPlayers = previousPlayers.filter(player => player instanceof WebAnimationsPlayer);\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n    let _keyframes = normalizeKeyframes(keyframes).map(styles => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\nfunction createEngine(type, doc) {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n  }\n  return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\nclass Animation {\n  constructor(_driver, input) {\n    this._driver = _driver;\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (warnings.length) {\n      warnValidation(warnings);\n    }\n    this._animationAst = ast;\n  }\n  buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : destinationStyles;\n    const errors = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n    // We need to explicitly type this property because of an api-extractor bug\n    // See https://github.com/microsoft/rushstack/issues/4390\n    this.ɵtype = 0 /* AnimationRendererType.Regular */;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n    // doesn't have a parent. There appears to be some animation-specific downstream logic that\n    // depends on the null check happening before the animation engine. This check keeps the old\n    // behavior while allowing `core` to not have to check for the parent element anymore.\n    if (this.parentNode(oldChild)) {\n      this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass AnimationRendererFactory {\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    this._currentId = 0;\n    this._microtaskId = 1;\n    this._animationCallbacksBuffer = [];\n    this._rendererCache = new Map();\n    this._cdRecurDepth = 0;\n    engine.onRemovalComplete = (element, delegate) => {\n      delegate?.removeChild(null, element);\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n}\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };\n//# sourceMappingURL=browser.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}