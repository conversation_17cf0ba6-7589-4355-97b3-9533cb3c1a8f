{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_3_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 39);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 29)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_3_img_3_Template, 2, 3, \"img\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 41)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00A0 \");\n    i0.ɵɵelementStart(6, \"input\", 42);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_input_blur_6_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r7, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template_button_click_7_listener() {\n      const picture_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r8.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(8, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"img\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const picture_r8 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", picture_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", picture_r8.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 29)(2, \"div\", 46);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_img_3_Template, 2, 3, \"img\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 50);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 49);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 23)(2, \"label\", 32);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 33);\n    i0.ɵɵtemplate(5, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_label_5_Template, 2, 1, \"label\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵtemplate(3, DetailContentManagementSalesAccountComponent_ng_container_8_div_3_Template, 4, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14)(7, \"label\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"input\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_10_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"div\", 14)(13, \"label\", 18);\n    i0.ɵɵtext(14, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_ngModelChange_15_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"div\", 14)(18, \"label\", 20);\n    i0.ɵɵtext(19, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"nb-select\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_ngModelChange_20_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_nb_select_selectedChange_20_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(21, DetailContentManagementSalesAccountComponent_ng_container_8_nb_option_21_Template, 2, 2, \"nb-option\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 23)(23, \"div\", 24)(24, \"div\", 25)(25, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r5 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(inputFile_r5.click());\n    });\n    i0.ɵɵtext(26, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"input\", 28, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_8_Template_input_change_28_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 29)(31, \"table\", 30)(32, \"tbody\");\n    i0.ɵɵtemplate(33, DetailContentManagementSalesAccountComponent_ng_container_8_ng_container_33_Template, 11, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, DetailContentManagementSalesAccountComponent_ng_container_8_div_34_Template, 4, 2, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 9)(36, \"div\", 23)(37, \"label\", 32);\n    i0.ɵɵtext(38, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33);\n    i0.ɵɵtemplate(40, DetailContentManagementSalesAccountComponent_ng_container_8_label_40_Template, 3, 2, \"label\", 34)(41, DetailContentManagementSalesAccountComponent_ng_container_8_label_41_Template, 3, 3, \"label\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_8_div_42_Template, 6, 1, \"div\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_11_0;\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CFirstMatrialUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_7_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_7_0 !== undefined ? tmp_7_0 : false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3 || ((tmp_9_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_9_0 !== undefined ? tmp_9_0 : false));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport let DetailContentManagementSalesAccountComponent = /*#__PURE__*/(() => {\n  class DetailContentManagementSalesAccountComponent extends BaseComponent {\n    constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.route = route;\n      this.message = message;\n      this._formItemService = _formItemService;\n      this._regularNoticeFileService = _regularNoticeFileService;\n      this._utilityService = _utilityService;\n      this.valid = valid;\n      this.location = location;\n      this._materialService = _materialService;\n      this._eventService = _eventService;\n      this.typeContentManagementSalesAccount = {\n        CFormType: 2,\n        CNoticeType: 2\n      };\n      this.CUiTypeOptions = [{\n        value: 1,\n        label: '建材選色'\n      }, {\n        value: 2,\n        label: '群組選樣_選色'\n      }, {\n        value: 3,\n        label: '建材選樣'\n      }];\n      this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n      this.selectedItems = {};\n      this.selectedRemarkType = {};\n      this.isNew = true;\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          if (this.buildCaseId) {\n            this.getListRegularNoticeFileHouseHold();\n          }\n        }\n      });\n    }\n    getItemByValue(value, options) {\n      for (const item of options) {\n        if (item.value === value) {\n          return item;\n        }\n      }\n      return null;\n    }\n    detectFiles(event, formItemReq_) {\n      const file = event.target.files[0];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          if (formItemReq_.listPictures.length > 0) {\n            formItemReq_.listPictures[0] = {\n              id: new Date().getTime(),\n              name: file.name.split('.')[0],\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            };\n          } else {\n            formItemReq_.listPictures.push({\n              id: new Date().getTime(),\n              name: file.name.split('.')[0],\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n          }\n          event.target.value = null;\n        };\n      }\n    }\n    removeImage(pictureId, formItemReq_) {\n      if (formItemReq_.listPictures.length) {\n        formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n      }\n    }\n    renameFile(event, index, formItemReq_) {\n      var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n      var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n        type: formItemReq_.listPictures[index].CFile.type\n      });\n      formItemReq_.listPictures[index].CFile = newFile;\n    }\n    onCheckAllChange(checked, formItemReq_) {\n      formItemReq_.allSelected = checked;\n      this.houseHoldList.forEach(item => {\n        formItemReq_.selectedItems[item] = checked;\n      });\n    }\n    onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n      if (checked) {\n        formItemReq_.selectedItems[item] = checked;\n        formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n      } else {\n        formItemReq_.allSelected = false;\n      }\n    }\n    onCheckboxRemarkChange(checked, item, formItemReq_) {\n      formItemReq_.selectedRemarkType[item] = checked;\n    }\n    createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n      const remarkObject = {};\n      for (const option of CRemarkTypeOptions) {\n        remarkObject[option] = false;\n      }\n      const remarkTypes = CRemarkType.split('-');\n      for (const type of remarkTypes) {\n        if (CRemarkTypeOptions.includes(type)) {\n          remarkObject[type] = true;\n        }\n      }\n      return remarkObject;\n    }\n    mergeItems(items) {\n      const map = new Map();\n      items.forEach(item => {\n        const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n        if (map.has(key)) {\n          const existing = map.get(key);\n          existing.count += 1;\n        } else {\n          map.set(key, {\n            item: {\n              ...item\n            },\n            count: 1\n          });\n        }\n      });\n      return Array.from(map.values()).map(({\n        item,\n        count\n      }) => ({\n        ...item,\n        CTotalAnswer: count\n      }));\n    }\n    getMaterialList() {\n      this._materialService.apiMaterialGetMaterialListPost$Json({\n        body: {\n          CBuildCaseId: this.buildCaseId,\n          CPagi: false\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.map(o => {\n            return {\n              CDesignFileUrl: null,\n              CFormItemHouseHold: null,\n              CFormId: null,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: null,\n              CTotalAnswer: 0,\n              CRequireAnswer: 1,\n              CUiType: 0,\n              selectedItems: {},\n              selectedRemarkType: this.selectedRemarkType,\n              allSelected: false,\n              listPictures: [],\n              selectedCUiType: this.CUiTypeOptions[0],\n              CFirstMatrialUrl: o.CPicture\n            };\n          });\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n        }\n      })).subscribe();\n    }\n    getListFormItem() {\n      this._formItemService.apiFormItemGetListFormItemPost$Json({\n        body: {\n          CBuildCaseId: this.buildCaseId,\n          CFormType: this.typeContentManagementSalesAccount.CFormType,\n          CIsPaging: false\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.listFormItem = res.Entries;\n          this.isNew = res.Entries.formItems ? false : true;\n          if (res.Entries.formItems) {\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n            this.arrListFormItemReq = res.Entries.formItems.map(o => {\n              return {\n                CFormId: this.listFormItem.CFormId,\n                CDesignFileUrl: o.CDesignFileUrl,\n                CFirstMatrialUrl: o.CFirstMatrialUrl,\n                CFile: o.CFile,\n                CFormItemHouseHold: o.CFormItemHouseHold,\n                CFormItemId: o.CFormItemId,\n                CLocation: o.CLocation,\n                CName: o.CName,\n                CPart: o.CPart,\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n                CRemarkType: o.CRemarkType,\n                CTotalAnswer: o.CTotalAnswer,\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n                CUiType: o.CUiType,\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                  ...this.selectedItems\n                },\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                  ...this.selectedRemarkType\n                },\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n                listPictures: [],\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n              };\n            });\n          } else {\n            this.getMaterialList();\n          }\n        }\n      })).subscribe();\n    }\n    changeSelectCUiType(formItemReq) {\n      if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n        formItemReq.CRequireAnswer = 1;\n      }\n    }\n    getHouseHoldListByNoticeType(data) {\n      for (let item of data) {\n        if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n          return item.CHouseHoldList;\n        }\n      }\n      return [];\n    }\n    getKeysWithTrueValue(obj) {\n      return Object.keys(obj).filter(key => obj[key]);\n    }\n    getKeysWithTrueValueJoined(obj) {\n      return Object.keys(obj).filter(key => obj[key]).join('-');\n    }\n    getCRemarkType(selectedCUiType, selectedRemarkType) {\n      if (selectedCUiType && selectedCUiType.value == 3) {\n        return this.getKeysWithTrueValueJoined(selectedRemarkType);\n      }\n    }\n    getStringAfterComma(inputString) {\n      const parts = inputString.split(',');\n      if (parts.length > 1) {\n        return parts[1];\n      } else return \"\";\n    }\n    formatFile(listPictures) {\n      if (listPictures && listPictures.length > 0) {\n        return {\n          Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n          FileExtension: listPictures[0].extension || null,\n          FileName: listPictures[0].CFile.name || listPictures[0].name || null\n        };\n      } else return undefined;\n    }\n    validation() {\n      this.valid.clear();\n      let hasInvalidCUiType = false;\n      let hasInvalidCRequireAnswer = false;\n      let hasInvalidItemName = false;\n      for (const item of this.saveListFormItemReq) {\n        if (!hasInvalidCUiType && !item.CUiType) {\n          hasInvalidCUiType = true;\n        }\n        if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n          hasInvalidCRequireAnswer = true;\n        }\n        if (item.CTotalAnswer && item.CRequireAnswer) {\n          if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n            this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n          }\n        }\n        if (!hasInvalidItemName && !item.CItemName) {\n          hasInvalidItemName = true;\n        }\n      }\n      if (hasInvalidCUiType) {\n        this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n      }\n      if (hasInvalidCRequireAnswer) {\n        this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n      }\n      if (hasInvalidItemName) {\n        this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n      }\n    }\n    onSubmit() {\n      this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n        return {\n          CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n          CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n          CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n          CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n          CFormID: this.isNew ? null : this.listFormItem.CFormId,\n          CName: e.CName,\n          CPart: e.CPart,\n          CLocation: e.CLocation,\n          CItemName: e.CItemName,\n          //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n          CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n          CTotalAnswer: e.CTotalAnswer,\n          CRequireAnswer: e.CRequireAnswer,\n          CUiType: e.selectedCUiType.value\n        };\n      });\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      if (this.isNew) {\n        this.createListFormItem();\n      } else {\n        this.saveListFormItem();\n      }\n    }\n    saveListFormItem() {\n      this._formItemService.apiFormItemSaveListFormItemPost$Json({\n        body: this.saveListFormItemReq\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      });\n    }\n    createListFormItem() {\n      this.creatListFormItem = {\n        CBuildCaseId: this.buildCaseId,\n        CFormItem: this.saveListFormItemReq || null,\n        CFormType: this.typeContentManagementSalesAccount.CFormType\n      };\n      this._formItemService.apiFormItemCreateListFormItemPost$Json({\n        body: this.creatListFormItem\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          // this.getListFormItem()\n          this.goBack();\n        }\n      });\n    }\n    createArrayObjectFromArray(a, b) {\n      const c = {};\n      for (const item of a) {\n        const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n        c[item] = !!matchingItem;\n      }\n      return c;\n    } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n    getListRegularNoticeFileHouseHold() {\n      this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n        body: this.buildCaseId\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n          this.getListFormItem();\n        }\n      })).subscribe();\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    static {\n      this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DetailContentManagementSalesAccountComponent,\n        selectors: [[\"ngx-detail-content-management-sales-account\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 14,\n        vars: 1,\n        consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"font-bold\", \"text-lg\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"flex\", \"flex-wrap\", \"w-full\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"pb-2\"], [\"class\", \"flex flex-col items-center\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-1/2\", \"px-2\"], [1, \"w-full\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"w-1/2\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-1/2\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EDA\\u623F\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"w-1/2\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"w-1/2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"w-1/2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-1/2\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\"], [1, \"btn\", \"btn-info\", \"h-fit\", 3, \"click\", \"disabled\"], [1, \"w-full\", 3, \"ngClass\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"w-full text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"buildingName\", 1, \"label\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex flex-wrap w-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\"], [\"class\", \"h-[140px] w-[300px]\", 3, \"src\", 4, \"ngIf\"], [1, \"h-[140px]\", \"w-[300px]\", 3, \"src\"], [3, \"value\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"h-32\", \"w-32\", 3, \"src\"], [1, \"w-full\", \"text-center\", 3, \"ngClass\"], [1, \"w-full\", \"justify-items-end\"], [\"class\", \" h-28 w-40\", 3, \"src\", 4, \"ngIf\"], [1, \"h-28\", \"w-40\", 3, \"src\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n        template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\");\n            i0.ɵɵelement(4, \"h1\", 2);\n            i0.ɵɵelementStart(5, \"div\", 3)(6, \"h4\", 4);\n            i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_8_Template, 43, 18, \"ng-container\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"nb-card-footer\", 6)(10, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_10_listener() {\n              return ctx.goBack();\n            });\n            i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 8);\n            i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_12_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          }\n        },\n        dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule]\n      });\n    }\n  }\n  return DetailContentManagementSalesAccountComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}