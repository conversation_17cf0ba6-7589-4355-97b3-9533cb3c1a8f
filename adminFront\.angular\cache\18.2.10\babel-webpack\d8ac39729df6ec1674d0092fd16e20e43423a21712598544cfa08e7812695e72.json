{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { OrdersProfitChartData } from '../data/orders-profit-chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../data/orders-chart\";\nimport * as i2 from \"../data/profit-chart\";\nexport class OrdersProfitChartService extends OrdersProfitChartData {\n  constructor(ordersChartService, profitChartService) {\n    super();\n    this.ordersChartService = ordersChartService;\n    this.profitChartService = profitChartService;\n    this.summary = [{\n      title: 'Marketplace',\n      value: 3654\n    }, {\n      title: 'Last Month',\n      value: 946\n    }, {\n      title: 'Last Week',\n      value: 654\n    }, {\n      title: 'Today',\n      value: 230\n    }];\n  }\n  getOrderProfitChartSummary() {\n    return observableOf(this.summary);\n  }\n  getOrdersChartData(period) {\n    return observableOf(this.ordersChartService.getOrdersChartData(period));\n  }\n  getProfitChartData(period) {\n    return observableOf(this.profitChartService.getProfitChartData(period));\n  }\n  static {\n    this.ɵfac = function OrdersProfitChartService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OrdersProfitChartService)(i0.ɵɵinject(i1.OrdersChartData), i0.ɵɵinject(i2.ProfitChartData));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OrdersProfitChartService,\n      factory: OrdersProfitChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "OrdersProfitChartData", "OrdersProfitChartService", "constructor", "ordersChartService", "profitChartService", "summary", "title", "value", "getOrderProfitChartSummary", "getOrdersChartData", "period", "getProfitChartData", "i0", "ɵɵinject", "i1", "OrdersChartData", "i2", "ProfitChartData", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\mock\\orders-profit-chart.service.ts"], "sourcesContent": ["import { of as observableOf,  Observable } from 'rxjs';\r\nimport { Injectable } from '@angular/core';\r\nimport { OrdersChart, OrdersChartData } from '../data/orders-chart';\r\nimport { OrderProfitChartSummary, OrdersProfitChartData } from '../data/orders-profit-chart';\r\nimport { ProfitChart, ProfitChartData } from '../data/profit-chart';\r\n\r\n@Injectable()\r\nexport class OrdersProfitChartService extends OrdersProfitChartData {\r\n\r\n  private summary = [\r\n    {\r\n      title: 'Marketplace',\r\n      value: 3654,\r\n    },\r\n    {\r\n      title: 'Last Month',\r\n      value: 946,\r\n    },\r\n    {\r\n      title: 'Last Week',\r\n      value: 654,\r\n    },\r\n    {\r\n      title: 'Today',\r\n      value: 230,\r\n    },\r\n  ];\r\n\r\n  constructor(private ordersChartService: OrdersChartData,\r\n              private profitChartService: ProfitChartData) {\r\n    super();\r\n  }\r\n\r\n  getOrderProfitChartSummary(): Observable<OrderProfitChartSummary[]> {\r\n    return observableOf(this.summary);\r\n  }\r\n\r\n  getOrdersChartData(period: string): Observable<OrdersChart> {\r\n    return observableOf(this.ordersChartService.getOrdersChartData(period));\r\n  }\r\n\r\n  getProfitChartData(period: string): Observable<ProfitChart> {\r\n    return observableOf(this.profitChartService.getProfitChartData(period));\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AAGtD,SAAkCC,qBAAqB,QAAQ,6BAA6B;;;;AAI5F,OAAM,MAAOC,wBAAyB,SAAQD,qBAAqB;EAqBjEE,YAAoBC,kBAAmC,EACnCC,kBAAmC;IACrD,KAAK,EAAE;IAFW,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IApB9B,KAAAC,OAAO,GAAG,CAChB;MACEC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,WAAW;MAClBC,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE;KACR,CACF;EAKD;EAEAC,0BAA0BA,CAAA;IACxB,OAAOT,YAAY,CAAC,IAAI,CAACM,OAAO,CAAC;EACnC;EAEAI,kBAAkBA,CAACC,MAAc;IAC/B,OAAOX,YAAY,CAAC,IAAI,CAACI,kBAAkB,CAACM,kBAAkB,CAACC,MAAM,CAAC,CAAC;EACzE;EAEAC,kBAAkBA,CAACD,MAAc;IAC/B,OAAOX,YAAY,CAAC,IAAI,CAACK,kBAAkB,CAACO,kBAAkB,CAACD,MAAM,CAAC,CAAC;EACzE;;;uCApCWT,wBAAwB,EAAAW,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAxBhB,wBAAwB;MAAAiB,OAAA,EAAxBjB,wBAAwB,CAAAkB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}