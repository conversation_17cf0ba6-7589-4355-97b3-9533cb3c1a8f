{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseAgreeDisclaimerPost$Json } from '../fn/house/api-house-agree-disclaimer-post-json';\nimport { apiHouseAgreeDisclaimerPost$Plain } from '../fn/house/api-house-agree-disclaimer-post-plain';\nimport { apiHouseCancelChangePreOrderPost$Json } from '../fn/house/api-house-cancel-change-pre-order-post-json';\nimport { apiHouseCancelChangePreOrderPost$Plain } from '../fn/house/api-house-cancel-change-pre-order-post-plain';\nimport { apiHouseChangePreOrderPost$Json } from '../fn/house/api-house-change-pre-order-post-json';\nimport { apiHouseChangePreOrderPost$Plain } from '../fn/house/api-house-change-pre-order-post-plain';\nimport { apiHouseCheckIsReviewPost$Json } from '../fn/house/api-house-check-is-review-post-json';\nimport { apiHouseCheckIsReviewPost$Plain } from '../fn/house/api-house-check-is-review-post-plain';\nimport { apiHouseCreateAppointmentPost$Json } from '../fn/house/api-house-create-appointment-post-json';\nimport { apiHouseCreateAppointmentPost$Plain } from '../fn/house/api-house-create-appointment-post-plain';\nimport { apiHouseDeleteAppointmentPost$Json } from '../fn/house/api-house-delete-appointment-post-json';\nimport { apiHouseDeleteAppointmentPost$Plain } from '../fn/house/api-house-delete-appointment-post-plain';\nimport { apiHouseDeleteRegularPicturePost$Json } from '../fn/house/api-house-delete-regular-picture-post-json';\nimport { apiHouseDeleteRegularPicturePost$Plain } from '../fn/house/api-house-delete-regular-picture-post-plain';\nimport { apiHouseEditAppointmentPost$Json } from '../fn/house/api-house-edit-appointment-post-json';\nimport { apiHouseEditAppointmentPost$Plain } from '../fn/house/api-house-edit-appointment-post-plain';\nimport { apiHouseEditHouseInfoPost$Json } from '../fn/house/api-house-edit-house-info-post-json';\nimport { apiHouseEditHouseInfoPost$Plain } from '../fn/house/api-house-edit-house-info-post-plain';\nimport { apiHouseEditHousePost$Json } from '../fn/house/api-house-edit-house-post-json';\nimport { apiHouseEditHousePost$Plain } from '../fn/house/api-house-edit-house-post-plain';\nimport { apiHouseEditHouseRegularPicPost$Json } from '../fn/house/api-house-edit-house-regular-pic-post-json';\nimport { apiHouseEditHouseRegularPicPost$Plain } from '../fn/house/api-house-edit-house-regular-pic-post-plain';\nimport { apiHouseEditListHousePost$Json } from '../fn/house/api-house-edit-list-house-post-json';\nimport { apiHouseEditListHousePost$Plain } from '../fn/house/api-house-edit-list-house-post-plain';\nimport { apiHouseExportExcelListAppointmentsPost$Json } from '../fn/house/api-house-export-excel-list-appointments-post-json';\nimport { apiHouseExportExcelListAppointmentsPost$Plain } from '../fn/house/api-house-export-excel-list-appointments-post-plain';\nimport { apiHouseExportHousePost$Json } from '../fn/house/api-house-export-house-post-json';\nimport { apiHouseExportHousePost$Plain } from '../fn/house/api-house-export-house-post-plain';\nimport { apiHouseGetAppointmentByIdPost$Json } from '../fn/house/api-house-get-appointment-by-id-post-json';\nimport { apiHouseGetAppointmentByIdPost$Plain } from '../fn/house/api-house-get-appointment-by-id-post-plain';\nimport { apiHouseGetChangeDatePost$Json } from '../fn/house/api-house-get-change-date-post-json';\nimport { apiHouseGetChangeDatePost$Plain } from '../fn/house/api-house-get-change-date-post-plain';\nimport { apiHouseGetChangePreOrderPost$Json } from '../fn/house/api-house-get-change-pre-order-post-json';\nimport { apiHouseGetChangePreOrderPost$Plain } from '../fn/house/api-house-get-change-pre-order-post-plain';\nimport { apiHouseGetHourListAppointmentPost$Json } from '../fn/house/api-house-get-hour-list-appointment-post-json';\nimport { apiHouseGetHourListAppointmentPost$Plain } from '../fn/house/api-house-get-hour-list-appointment-post-plain';\nimport { apiHouseGetHourListPost$Json } from '../fn/house/api-house-get-hour-list-post-json';\nimport { apiHouseGetHourListPost$Plain } from '../fn/house/api-house-get-hour-list-post-plain';\nimport { apiHouseGetHouseByIdPost$Json } from '../fn/house/api-house-get-house-by-id-post-json';\nimport { apiHouseGetHouseByIdPost$Plain } from '../fn/house/api-house-get-house-by-id-post-plain';\nimport { apiHouseGetHouseChangeDatePost$Json } from '../fn/house/api-house-get-house-change-date-post-json';\nimport { apiHouseGetHouseChangeDatePost$Plain } from '../fn/house/api-house-get-house-change-date-post-plain';\nimport { apiHouseGetHouseInfoPost$Json } from '../fn/house/api-house-get-house-info-post-json';\nimport { apiHouseGetHouseInfoPost$Plain } from '../fn/house/api-house-get-house-info-post-plain';\nimport { apiHouseGetHouseListPost$Json } from '../fn/house/api-house-get-house-list-post-json';\nimport { apiHouseGetHouseListPost$Plain } from '../fn/house/api-house-get-house-list-post-plain';\nimport { apiHouseGetHouseRegularPicturePost$Json } from '../fn/house/api-house-get-house-regular-picture-post-json';\nimport { apiHouseGetHouseRegularPicturePost$Plain } from '../fn/house/api-house-get-house-regular-picture-post-plain';\nimport { apiHouseGetHouseRequirementPost$Json } from '../fn/house/api-house-get-house-requirement-post-json';\nimport { apiHouseGetHouseRequirementPost$Plain } from '../fn/house/api-house-get-house-requirement-post-plain';\nimport { apiHouseGetHouseReviewPost$Json } from '../fn/house/api-house-get-house-review-post-json';\nimport { apiHouseGetHouseReviewPost$Plain } from '../fn/house/api-house-get-house-review-post-plain';\nimport { apiHouseGetListAppointmentsPost$Json } from '../fn/house/api-house-get-list-appointments-post-json';\nimport { apiHouseGetListAppointmentsPost$Plain } from '../fn/house/api-house-get-list-appointments-post-plain';\nimport { apiHouseGetListBuildingPost$Json } from '../fn/house/api-house-get-list-building-post-json';\nimport { apiHouseGetListBuildingPost$Plain } from '../fn/house/api-house-get-list-building-post-plain';\nimport { apiHouseGetListHouseHoldPost$Json } from '../fn/house/api-house-get-list-house-hold-post-json';\nimport { apiHouseGetListHouseHoldPost$Plain } from '../fn/house/api-house-get-list-house-hold-post-plain';\nimport { apiHouseGetListHouseRegularPicPost$Json } from '../fn/house/api-house-get-list-house-regular-pic-post-json';\nimport { apiHouseGetListHouseRegularPicPost$Plain } from '../fn/house/api-house-get-list-house-regular-pic-post-plain';\nimport { apiHouseGetListProgressPost$Json } from '../fn/house/api-house-get-list-progress-post-json';\nimport { apiHouseGetListProgressPost$Plain } from '../fn/house/api-house-get-list-progress-post-plain';\nimport { apiHouseGetMilestonePost$Json } from '../fn/house/api-house-get-milestone-post-json';\nimport { apiHouseGetMilestonePost$Plain } from '../fn/house/api-house-get-milestone-post-plain';\nimport { apiHouseGetRegularNoticeFilePost$Json } from '../fn/house/api-house-get-regular-notice-file-post-json';\nimport { apiHouseGetRegularNoticeFilePost$Plain } from '../fn/house/api-house-get-regular-notice-file-post-plain';\nimport { apiHouseGetSpecialNoticeFilePost$Json } from '../fn/house/api-house-get-special-notice-file-post-json';\nimport { apiHouseGetSpecialNoticeFilePost$Plain } from '../fn/house/api-house-get-special-notice-file-post-plain';\nimport { apiHouseHouseLoginStep2Post$Json } from '../fn/house/api-house-house-login-step-2-post-json';\nimport { apiHouseHouseLoginStep2Post$Plain } from '../fn/house/api-house-house-login-step-2-post-plain';\nimport { apiHouseImportHousePost$Json } from '../fn/house/api-house-import-house-post-json';\nimport { apiHouseImportHousePost$Plain } from '../fn/house/api-house-import-house-post-plain';\nimport { apiHouseLoginPost$Json } from '../fn/house/api-house-login-post-json';\nimport { apiHouseLoginPost$Plain } from '../fn/house/api-house-login-post-plain';\nimport { apiHouseSaveHouseChangeDatePost$Json } from '../fn/house/api-house-save-house-change-date-post-json';\nimport { apiHouseSaveHouseChangeDatePost$Plain } from '../fn/house/api-house-save-house-change-date-post-plain';\nimport { apiHouseSaveMilestonePost$Json } from '../fn/house/api-house-save-milestone-post-json';\nimport { apiHouseSaveMilestonePost$Plain } from '../fn/house/api-house-save-milestone-post-plain';\nimport { apiHouseUpdateHouseRequirementPost$Json } from '../fn/house/api-house-update-house-requirement-post-json';\nimport { apiHouseUpdateHouseRequirementPost$Plain } from '../fn/house/api-house-update-house-requirement-post-plain';\nimport { apiHouseUpdateHouseReviewPost$Json } from '../fn/house/api-house-update-house-review-post-json';\nimport { apiHouseUpdateHouseReviewPost$Plain } from '../fn/house/api-house-update-house-review-post-plain';\nimport { apiHouseUploadRegularPicPost$Json } from '../fn/house/api-house-upload-regular-pic-post-json';\nimport { apiHouseUploadRegularPicPost$Plain } from '../fn/house/api-house-upload-regular-pic-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class HouseService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiHouseLoginPost()` */\n  static {\n    this.ApiHouseLoginPostPath = '/api/House/Login';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseLoginPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseLoginPost$Plain$Response(params, context) {\n    return apiHouseLoginPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseLoginPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseLoginPost$Plain(params, context) {\n    return this.apiHouseLoginPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseLoginPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseLoginPost$Json$Response(params, context) {\n    return apiHouseLoginPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseLoginPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseLoginPost$Json(params, context) {\n    return this.apiHouseLoginPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetChangeDatePost()` */\n  static {\n    this.ApiHouseGetChangeDatePostPath = '/api/House/GetChangeDate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetChangeDatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangeDatePost$Plain$Response(params, context) {\n    return apiHouseGetChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangeDatePost$Plain(params, context) {\n    return this.apiHouseGetChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetChangeDatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangeDatePost$Json$Response(params, context) {\n    return apiHouseGetChangeDatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangeDatePost$Json(params, context) {\n    return this.apiHouseGetChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseChangePreOrderPost()` */\n  static {\n    this.ApiHouseChangePreOrderPostPath = '/api/House/ChangePreOrder';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseChangePreOrderPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseChangePreOrderPost$Plain$Response(params, context) {\n    return apiHouseChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseChangePreOrderPost$Plain(params, context) {\n    return this.apiHouseChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseChangePreOrderPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseChangePreOrderPost$Json$Response(params, context) {\n    return apiHouseChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseChangePreOrderPost$Json(params, context) {\n    return this.apiHouseChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHourListPost()` */\n  static {\n    this.ApiHouseGetHourListPostPath = '/api/House/GetHourList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHourListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListPost$Plain$Response(params, context) {\n    return apiHouseGetHourListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHourListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListPost$Plain(params, context) {\n    return this.apiHouseGetHourListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHourListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListPost$Json$Response(params, context) {\n    return apiHouseGetHourListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHourListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListPost$Json(params, context) {\n    return this.apiHouseGetHourListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseCancelChangePreOrderPost()` */\n  static {\n    this.ApiHouseCancelChangePreOrderPostPath = '/api/House/CancelChangePreOrder';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCancelChangePreOrderPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCancelChangePreOrderPost$Plain$Response(params, context) {\n    return apiHouseCancelChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCancelChangePreOrderPost$Plain(params, context) {\n    return this.apiHouseCancelChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCancelChangePreOrderPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCancelChangePreOrderPost$Json$Response(params, context) {\n    return apiHouseCancelChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCancelChangePreOrderPost$Json(params, context) {\n    return this.apiHouseCancelChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseRegularPicturePost()` */\n  static {\n    this.ApiHouseGetHouseRegularPicturePostPath = '/api/House/GetHouseRegularPicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRegularPicturePost$Plain$Response(params, context) {\n    return apiHouseGetHouseRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRegularPicturePost$Plain(params, context) {\n    return this.apiHouseGetHouseRegularPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRegularPicturePost$Json$Response(params, context) {\n    return apiHouseGetHouseRegularPicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRegularPicturePost$Json(params, context) {\n    return this.apiHouseGetHouseRegularPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseHouseLoginStep2Post()` */\n  static {\n    this.ApiHouseHouseLoginStep2PostPath = '/api/House/HouseLoginStep2';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHouseLoginStep2Post$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHouseLoginStep2Post$Plain$Response(params, context) {\n    return apiHouseHouseLoginStep2Post$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHouseLoginStep2Post$Plain(params, context) {\n    return this.apiHouseHouseLoginStep2Post$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHouseLoginStep2Post$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHouseLoginStep2Post$Json$Response(params, context) {\n    return apiHouseHouseLoginStep2Post$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHouseLoginStep2Post$Json(params, context) {\n    return this.apiHouseHouseLoginStep2Post$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseAgreeDisclaimerPost()` */\n  static {\n    this.ApiHouseAgreeDisclaimerPostPath = '/api/House/AgreeDisclaimer';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseAgreeDisclaimerPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseAgreeDisclaimerPost$Plain$Response(params, context) {\n    return apiHouseAgreeDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseAgreeDisclaimerPost$Plain(params, context) {\n    return this.apiHouseAgreeDisclaimerPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseAgreeDisclaimerPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseAgreeDisclaimerPost$Json$Response(params, context) {\n    return apiHouseAgreeDisclaimerPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseAgreeDisclaimerPost$Json(params, context) {\n    return this.apiHouseAgreeDisclaimerPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetRegularNoticeFilePost()` */\n  static {\n    this.ApiHouseGetRegularNoticeFilePostPath = '/api/House/GetRegularNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetRegularNoticeFilePost$Plain$Response(params, context) {\n    return apiHouseGetRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetRegularNoticeFilePost$Plain(params, context) {\n    return this.apiHouseGetRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetRegularNoticeFilePost$Json$Response(params, context) {\n    return apiHouseGetRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetRegularNoticeFilePost$Json(params, context) {\n    return this.apiHouseGetRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetSpecialNoticeFilePost()` */\n  static {\n    this.ApiHouseGetSpecialNoticeFilePostPath = '/api/House/GetSpecialNoticeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetSpecialNoticeFilePost$Plain$Response(params, context) {\n    return apiHouseGetSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetSpecialNoticeFilePost$Plain(params, context) {\n    return this.apiHouseGetSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetSpecialNoticeFilePost$Json$Response(params, context) {\n    return apiHouseGetSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetSpecialNoticeFilePost$Json(params, context) {\n    return this.apiHouseGetSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetChangePreOrderPost()` */\n  static {\n    this.ApiHouseGetChangePreOrderPostPath = '/api/House/GetChangePreOrder';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetChangePreOrderPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangePreOrderPost$Plain$Response(params, context) {\n    return apiHouseGetChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangePreOrderPost$Plain(params, context) {\n    return this.apiHouseGetChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetChangePreOrderPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangePreOrderPost$Json$Response(params, context) {\n    return apiHouseGetChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetChangePreOrderPost$Json(params, context) {\n    return this.apiHouseGetChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseReviewPost()` */\n  static {\n    this.ApiHouseGetHouseReviewPostPath = '/api/House/GetHouseReview';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseReviewPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseReviewPost$Plain$Response(params, context) {\n    return apiHouseGetHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseReviewPost$Plain(params, context) {\n    return this.apiHouseGetHouseReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseReviewPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseReviewPost$Json$Response(params, context) {\n    return apiHouseGetHouseReviewPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseReviewPost$Json(params, context) {\n    return this.apiHouseGetHouseReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseUpdateHouseReviewPost()` */\n  static {\n    this.ApiHouseUpdateHouseReviewPostPath = '/api/House/UpdateHouseReview';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUpdateHouseReviewPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseReviewPost$Plain$Response(params, context) {\n    return apiHouseUpdateHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseReviewPost$Plain(params, context) {\n    return this.apiHouseUpdateHouseReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUpdateHouseReviewPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseReviewPost$Json$Response(params, context) {\n    return apiHouseUpdateHouseReviewPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseReviewPost$Json(params, context) {\n    return this.apiHouseUpdateHouseReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseRequirementPost()` */\n  static {\n    this.ApiHouseGetHouseRequirementPostPath = '/api/House/GetHouseRequirement';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseRequirementPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRequirementPost$Plain$Response(params, context) {\n    return apiHouseGetHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRequirementPost$Plain(params, context) {\n    return this.apiHouseGetHouseRequirementPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseRequirementPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRequirementPost$Json$Response(params, context) {\n    return apiHouseGetHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseRequirementPost$Json(params, context) {\n    return this.apiHouseGetHouseRequirementPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseUpdateHouseRequirementPost()` */\n  static {\n    this.ApiHouseUpdateHouseRequirementPostPath = '/api/House/UpdateHouseRequirement';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseRequirementPost$Plain$Response(params, context) {\n    return apiHouseUpdateHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseRequirementPost$Plain(params, context) {\n    return this.apiHouseUpdateHouseRequirementPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseRequirementPost$Json$Response(params, context) {\n    return apiHouseUpdateHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUpdateHouseRequirementPost$Json(params, context) {\n    return this.apiHouseUpdateHouseRequirementPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseCheckIsReviewPost()` */\n  static {\n    this.ApiHouseCheckIsReviewPostPath = '/api/House/CheckIsReview';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCheckIsReviewPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseCheckIsReviewPost$Plain$Response(params, context) {\n    return apiHouseCheckIsReviewPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseCheckIsReviewPost$Plain(params, context) {\n    return this.apiHouseCheckIsReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCheckIsReviewPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseCheckIsReviewPost$Json$Response(params, context) {\n    return apiHouseCheckIsReviewPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseCheckIsReviewPost$Json(params, context) {\n    return this.apiHouseCheckIsReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseInfoPost()` */\n  static {\n    this.ApiHouseGetHouseInfoPostPath = '/api/House/GetHouseInfo';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseInfoPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseInfoPost$Plain$Response(params, context) {\n    return apiHouseGetHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseInfoPost$Plain(params, context) {\n    return this.apiHouseGetHouseInfoPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseInfoPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseInfoPost$Json$Response(params, context) {\n    return apiHouseGetHouseInfoPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetHouseInfoPost$Json(params, context) {\n    return this.apiHouseGetHouseInfoPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseEditHouseInfoPost()` */\n  static {\n    this.ApiHouseEditHouseInfoPostPath = '/api/House/EditHouseInfo';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHouseInfoPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseInfoPost$Plain$Response(params, context) {\n    return apiHouseEditHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseInfoPost$Plain(params, context) {\n    return this.apiHouseEditHouseInfoPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHouseInfoPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseInfoPost$Json$Response(params, context) {\n    return apiHouseEditHouseInfoPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseInfoPost$Json(params, context) {\n    return this.apiHouseEditHouseInfoPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetMilestonePost()` */\n  static {\n    this.ApiHouseGetMilestonePostPath = '/api/House/GetMilestone';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetMilestonePost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetMilestonePost$Plain$Response(params, context) {\n    return apiHouseGetMilestonePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetMilestonePost$Plain(params, context) {\n    return this.apiHouseGetMilestonePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetMilestonePost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetMilestonePost$Json$Response(params, context) {\n    return apiHouseGetMilestonePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseGetMilestonePost$Json(params, context) {\n    return this.apiHouseGetMilestonePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseSaveMilestonePost()` */\n  static {\n    this.ApiHouseSaveMilestonePostPath = '/api/House/SaveMilestone';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseSaveMilestonePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveMilestonePost$Plain$Response(params, context) {\n    return apiHouseSaveMilestonePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveMilestonePost$Plain(params, context) {\n    return this.apiHouseSaveMilestonePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseSaveMilestonePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveMilestonePost$Json$Response(params, context) {\n    return apiHouseSaveMilestonePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveMilestonePost$Json(params, context) {\n    return this.apiHouseSaveMilestonePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetListBuildingPost()` */\n  static {\n    this.ApiHouseGetListBuildingPostPath = '/api/House/GetListBuilding';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListBuildingPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListBuildingPost$Plain$Response(params, context) {\n    return apiHouseGetListBuildingPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListBuildingPost$Plain(params, context) {\n    return this.apiHouseGetListBuildingPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListBuildingPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListBuildingPost$Json$Response(params, context) {\n    return apiHouseGetListBuildingPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListBuildingPost$Json(params, context) {\n    return this.apiHouseGetListBuildingPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetListHouseHoldPost()` */\n  static {\n    this.ApiHouseGetListHouseHoldPostPath = '/api/House/GetListHouseHold';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListHouseHoldPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseHoldPost$Plain$Response(params, context) {\n    return apiHouseGetListHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseHoldPost$Plain(params, context) {\n    return this.apiHouseGetListHouseHoldPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListHouseHoldPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseHoldPost$Json$Response(params, context) {\n    return apiHouseGetListHouseHoldPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseHoldPost$Json(params, context) {\n    return this.apiHouseGetListHouseHoldPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetListProgressPost()` */\n  static {\n    this.ApiHouseGetListProgressPostPath = '/api/House/GetListProgress';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListProgressPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListProgressPost$Plain$Response(params, context) {\n    return apiHouseGetListProgressPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListProgressPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListProgressPost$Plain(params, context) {\n    return this.apiHouseGetListProgressPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListProgressPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListProgressPost$Json$Response(params, context) {\n    return apiHouseGetListProgressPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListProgressPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListProgressPost$Json(params, context) {\n    return this.apiHouseGetListProgressPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseListPost()` */\n  static {\n    this.ApiHouseGetHouseListPostPath = '/api/House/GetHouseList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseListPost$Plain$Response(params, context) {\n    return apiHouseGetHouseListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseListPost$Plain(params, context) {\n    return this.apiHouseGetHouseListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseListPost$Json$Response(params, context) {\n    return apiHouseGetHouseListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseListPost$Json(params, context) {\n    return this.apiHouseGetHouseListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseByIdPost()` */\n  static {\n    this.ApiHouseGetHouseByIdPostPath = '/api/House/GetHouseByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseByIdPost$Plain$Response(params, context) {\n    return apiHouseGetHouseByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseByIdPost$Plain(params, context) {\n    return this.apiHouseGetHouseByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseByIdPost$Json$Response(params, context) {\n    return apiHouseGetHouseByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseByIdPost$Json(params, context) {\n    return this.apiHouseGetHouseByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseEditHousePost()` */\n  static {\n    this.ApiHouseEditHousePostPath = '/api/House/EditHouse';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHousePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHousePost$Plain$Response(params, context) {\n    return apiHouseEditHousePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHousePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHousePost$Plain(params, context) {\n    return this.apiHouseEditHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHousePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHousePost$Json$Response(params, context) {\n    return apiHouseEditHousePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHousePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHousePost$Json(params, context) {\n    return this.apiHouseEditHousePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseEditListHousePost()` */\n  static {\n    this.ApiHouseEditListHousePostPath = '/api/House/EditListHouse';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditListHousePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditListHousePost$Plain$Response(params, context) {\n    return apiHouseEditListHousePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditListHousePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditListHousePost$Plain(params, context) {\n    return this.apiHouseEditListHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditListHousePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditListHousePost$Json$Response(params, context) {\n    return apiHouseEditListHousePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditListHousePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditListHousePost$Json(params, context) {\n    return this.apiHouseEditListHousePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetListHouseRegularPicPost()` */\n  static {\n    this.ApiHouseGetListHouseRegularPicPostPath = '/api/House/GetListHouseRegularPic';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseRegularPicPost$Plain$Response(params, context) {\n    return apiHouseGetListHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseRegularPicPost$Plain(params, context) {\n    return this.apiHouseGetListHouseRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseRegularPicPost$Json$Response(params, context) {\n    return apiHouseGetListHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListHouseRegularPicPost$Json(params, context) {\n    return this.apiHouseGetListHouseRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseEditHouseRegularPicPost()` */\n  static {\n    this.ApiHouseEditHouseRegularPicPostPath = '/api/House/EditHouseRegularPic';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHouseRegularPicPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseRegularPicPost$Plain$Response(params, context) {\n    return apiHouseEditHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseRegularPicPost$Plain(params, context) {\n    return this.apiHouseEditHouseRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditHouseRegularPicPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseRegularPicPost$Json$Response(params, context) {\n    return apiHouseEditHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditHouseRegularPicPost$Json(params, context) {\n    return this.apiHouseEditHouseRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseDeleteRegularPicturePost()` */\n  static {\n    this.ApiHouseDeleteRegularPicturePostPath = '/api/House/DeleteRegularPicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseDeleteRegularPicturePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteRegularPicturePost$Plain$Response(params, context) {\n    return apiHouseDeleteRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteRegularPicturePost$Plain(params, context) {\n    return this.apiHouseDeleteRegularPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseDeleteRegularPicturePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteRegularPicturePost$Json$Response(params, context) {\n    return apiHouseDeleteRegularPicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteRegularPicturePost$Json(params, context) {\n    return this.apiHouseDeleteRegularPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseUploadRegularPicPost()` */\n  static {\n    this.ApiHouseUploadRegularPicPostPath = '/api/House/UploadRegularPic';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUploadRegularPicPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUploadRegularPicPost$Plain$Response(params, context) {\n    return apiHouseUploadRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUploadRegularPicPost$Plain(params, context) {\n    return this.apiHouseUploadRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseUploadRegularPicPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUploadRegularPicPost$Json$Response(params, context) {\n    return apiHouseUploadRegularPicPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseUploadRegularPicPost$Json(params, context) {\n    return this.apiHouseUploadRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetListAppointmentsPost()` */\n  static {\n    this.ApiHouseGetListAppointmentsPostPath = '/api/House/GetListAppointments';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListAppointmentsPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListAppointmentsPost$Plain$Response(params, context) {\n    return apiHouseGetListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListAppointmentsPost$Plain(params, context) {\n    return this.apiHouseGetListAppointmentsPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetListAppointmentsPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListAppointmentsPost$Json$Response(params, context) {\n    return apiHouseGetListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetListAppointmentsPost$Json(params, context) {\n    return this.apiHouseGetListAppointmentsPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetAppointmentByIdPost()` */\n  static {\n    this.ApiHouseGetAppointmentByIdPostPath = '/api/House/GetAppointmentByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetAppointmentByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetAppointmentByIdPost$Plain$Response(params, context) {\n    return apiHouseGetAppointmentByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetAppointmentByIdPost$Plain(params, context) {\n    return this.apiHouseGetAppointmentByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetAppointmentByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetAppointmentByIdPost$Json$Response(params, context) {\n    return apiHouseGetAppointmentByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetAppointmentByIdPost$Json(params, context) {\n    return this.apiHouseGetAppointmentByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseEditAppointmentPost()` */\n  static {\n    this.ApiHouseEditAppointmentPostPath = '/api/House/EditAppointment';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditAppointmentPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditAppointmentPost$Plain$Response(params, context) {\n    return apiHouseEditAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditAppointmentPost$Plain(params, context) {\n    return this.apiHouseEditAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseEditAppointmentPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditAppointmentPost$Json$Response(params, context) {\n    return apiHouseEditAppointmentPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseEditAppointmentPost$Json(params, context) {\n    return this.apiHouseEditAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseDeleteAppointmentPost()` */\n  static {\n    this.ApiHouseDeleteAppointmentPostPath = '/api/House/DeleteAppointment';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseDeleteAppointmentPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteAppointmentPost$Plain$Response(params, context) {\n    return apiHouseDeleteAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteAppointmentPost$Plain(params, context) {\n    return this.apiHouseDeleteAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseDeleteAppointmentPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteAppointmentPost$Json$Response(params, context) {\n    return apiHouseDeleteAppointmentPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseDeleteAppointmentPost$Json(params, context) {\n    return this.apiHouseDeleteAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseCreateAppointmentPost()` */\n  static {\n    this.ApiHouseCreateAppointmentPostPath = '/api/House/CreateAppointment';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCreateAppointmentPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCreateAppointmentPost$Plain$Response(params, context) {\n    return apiHouseCreateAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCreateAppointmentPost$Plain(params, context) {\n    return this.apiHouseCreateAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseCreateAppointmentPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCreateAppointmentPost$Json$Response(params, context) {\n    return apiHouseCreateAppointmentPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseCreateAppointmentPost$Json(params, context) {\n    return this.apiHouseCreateAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHourListAppointmentPost()` */\n  static {\n    this.ApiHouseGetHourListAppointmentPostPath = '/api/House/GetHourListAppointment';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHourListAppointmentPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListAppointmentPost$Plain$Response(params, context) {\n    return apiHouseGetHourListAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListAppointmentPost$Plain(params, context) {\n    return this.apiHouseGetHourListAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHourListAppointmentPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListAppointmentPost$Json$Response(params, context) {\n    return apiHouseGetHourListAppointmentPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHourListAppointmentPost$Json(params, context) {\n    return this.apiHouseGetHourListAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseExportExcelListAppointmentsPost()` */\n  static {\n    this.ApiHouseExportExcelListAppointmentsPostPath = '/api/House/ExportExcelListAppointments';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseExportExcelListAppointmentsPost$Plain$Response(params, context) {\n    return apiHouseExportExcelListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseExportExcelListAppointmentsPost$Plain(params, context) {\n    return this.apiHouseExportExcelListAppointmentsPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseExportExcelListAppointmentsPost$Json$Response(params, context) {\n    return apiHouseExportExcelListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseExportExcelListAppointmentsPost$Json(params, context) {\n    return this.apiHouseExportExcelListAppointmentsPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseGetHouseChangeDatePost()` */\n  static {\n    this.ApiHouseGetHouseChangeDatePostPath = '/api/House/GetHouseChangeDate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseChangeDatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseChangeDatePost$Plain$Response(params, context) {\n    return apiHouseGetHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseChangeDatePost$Plain(params, context) {\n    return this.apiHouseGetHouseChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseGetHouseChangeDatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseChangeDatePost$Json$Response(params, context) {\n    return apiHouseGetHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseGetHouseChangeDatePost$Json(params, context) {\n    return this.apiHouseGetHouseChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseSaveHouseChangeDatePost()` */\n  static {\n    this.ApiHouseSaveHouseChangeDatePostPath = '/api/House/SaveHouseChangeDate';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveHouseChangeDatePost$Plain$Response(params, context) {\n    return apiHouseSaveHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveHouseChangeDatePost$Plain(params, context) {\n    return this.apiHouseSaveHouseChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveHouseChangeDatePost$Json$Response(params, context) {\n    return apiHouseSaveHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseSaveHouseChangeDatePost$Json(params, context) {\n    return this.apiHouseSaveHouseChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseExportHousePost()` */\n  static {\n    this.ApiHouseExportHousePostPath = '/api/House/ExportHouse';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseExportHousePost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseExportHousePost$Plain$Response(params, context) {\n    return apiHouseExportHousePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseExportHousePost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseExportHousePost$Plain(params, context) {\n    return this.apiHouseExportHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseExportHousePost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseExportHousePost$Json$Response(params, context) {\n    return apiHouseExportHousePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseExportHousePost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiHouseExportHousePost$Json(params, context) {\n    return this.apiHouseExportHousePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiHouseImportHousePost()` */\n  static {\n    this.ApiHouseImportHousePostPath = '/api/House/ImportHouse';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseImportHousePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiHouseImportHousePost$Plain$Response(params, context) {\n    return apiHouseImportHousePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseImportHousePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiHouseImportHousePost$Plain(params, context) {\n    return this.apiHouseImportHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseImportHousePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiHouseImportHousePost$Json$Response(params, context) {\n    return apiHouseImportHousePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseImportHousePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiHouseImportHousePost$Json(params, context) {\n    return this.apiHouseImportHousePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function HouseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HouseService,\n      factory: HouseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiHouseAgreeDisclaimerPost$Json", "apiHouseAgreeDisclaimerPost$Plain", "apiHouseCancelChangePreOrderPost$Json", "apiHouseCancelChangePreOrderPost$Plain", "apiHouseChangePreOrderPost$Json", "apiHouseChangePreOrderPost$Plain", "apiHouseCheckIsReviewPost$Json", "apiHouseCheckIsReviewPost$Plain", "apiHouseCreateAppointmentPost$Json", "apiHouseCreateAppointmentPost$Plain", "apiHouseDeleteAppointmentPost$Json", "apiHouseDeleteAppointmentPost$Plain", "apiHouseDeleteRegularPicturePost$Json", "apiHouseDeleteRegularPicturePost$Plain", "apiHouseEditAppointmentPost$Json", "apiHouseEditAppointmentPost$Plain", "apiHouseEditHouseInfoPost$Json", "apiHouseEditHouseInfoPost$Plain", "apiHouseEditHousePost$Json", "apiHouseEditHousePost$Plain", "apiHouseEditHouseRegularPicPost$Json", "apiHouseEditHouseRegularPicPost$Plain", "apiHouseEditListHousePost$Json", "apiHouseEditListHousePost$Plain", "apiHouseExportExcelListAppointmentsPost$Json", "apiHouseExportExcelListAppointmentsPost$Plain", "apiHouseExportHousePost$Json", "apiHouseExportHousePost$Plain", "apiHouseGetAppointmentByIdPost$Json", "apiHouseGetAppointmentByIdPost$Plain", "apiHouseGetChangeDatePost$Json", "apiHouseGetChangeDatePost$Plain", "apiHouseGetChangePreOrderPost$Json", "apiHouseGetChangePreOrderPost$Plain", "apiHouseGetHourListAppointmentPost$Json", "apiHouseGetHourListAppointmentPost$Plain", "apiHouseGetHourListPost$Json", "apiHouseGetHourListPost$Plain", "apiHouseGetHouseByIdPost$Json", "apiHouseGetHouseByIdPost$Plain", "apiHouseGetHouseChangeDatePost$Json", "apiHouseGetHouseChangeDatePost$Plain", "apiHouseGetHouseInfoPost$Json", "apiHouseGetHouseInfoPost$Plain", "apiHouseGetHouseListPost$Json", "apiHouseGetHouseListPost$Plain", "apiHouseGetHouseRegularPicturePost$Json", "apiHouseGetHouseRegularPicturePost$Plain", "apiHouseGetHouseRequirementPost$Json", "apiHouseGetHouseRequirementPost$Plain", "apiHouseGetHouseReviewPost$Json", "apiHouseGetHouseReviewPost$Plain", "apiHouseGetListAppointmentsPost$Json", "apiHouseGetListAppointmentsPost$Plain", "apiHouseGetListBuildingPost$Json", "apiHouseGetListBuildingPost$Plain", "apiHouseGetListHouseHoldPost$Json", "apiHouseGetListHouseHoldPost$Plain", "apiHouseGetListHouseRegularPicPost$Json", "apiHouseGetListHouseRegularPicPost$Plain", "apiHouseGetListProgressPost$Json", "apiHouseGetListProgressPost$Plain", "apiHouseGetMilestonePost$Json", "apiHouseGetMilestonePost$Plain", "apiHouseGetRegularNoticeFilePost$Json", "apiHouseGetRegularNoticeFilePost$Plain", "apiHouseGetSpecialNoticeFilePost$Json", "apiHouseGetSpecialNoticeFilePost$Plain", "apiHouseHouseLoginStep2Post$Json", "apiHouseHouseLoginStep2Post$Plain", "apiHouseImportHousePost$Json", "apiHouseImportHousePost$Plain", "apiHouseLoginPost$Json", "apiHouseLoginPost$Plain", "apiHouseSaveHouseChangeDatePost$Json", "apiHouseSaveHouseChangeDatePost$Plain", "apiHouseSaveMilestonePost$Json", "apiHouseSaveMilestonePost$Plain", "apiHouseUpdateHouseRequirementPost$Json", "apiHouseUpdateHouseRequirementPost$Plain", "apiHouseUpdateHouseReviewPost$Json", "apiHouseUpdateHouseReviewPost$Plain", "apiHouseUploadRegularPicPost$Json", "apiHouseUploadRegularPicPost$Plain", "HouseService", "constructor", "config", "http", "ApiHouseLoginPostPath", "apiHouseLoginPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiHouseLoginPost$Json$Response", "ApiHouseGetChangeDatePostPath", "apiHouseGetChangeDatePost$Plain$Response", "apiHouseGetChangeDatePost$Json$Response", "ApiHouseChangePreOrderPostPath", "apiHouseChangePreOrderPost$Plain$Response", "apiHouseChangePreOrderPost$Json$Response", "ApiHouseGetHourListPostPath", "apiHouseGetHourListPost$Plain$Response", "apiHouseGetHourListPost$Json$Response", "ApiHouseCancelChangePreOrderPostPath", "apiHouseCancelChangePreOrderPost$Plain$Response", "apiHouseCancelChangePreOrderPost$Json$Response", "ApiHouseGetHouseRegularPicturePostPath", "apiHouseGetHouseRegularPicturePost$Plain$Response", "apiHouseGetHouseRegularPicturePost$Json$Response", "ApiHouseHouseLoginStep2PostPath", "apiHouseHouseLoginStep2Post$Plain$Response", "apiHouseHouseLoginStep2Post$Json$Response", "ApiHouseAgreeDisclaimerPostPath", "apiHouseAgreeDisclaimerPost$Plain$Response", "apiHouseAgreeDisclaimerPost$Json$Response", "ApiHouseGetRegularNoticeFilePostPath", "apiHouseGetRegularNoticeFilePost$Plain$Response", "apiHouseGetRegularNoticeFilePost$Json$Response", "ApiHouseGetSpecialNoticeFilePostPath", "apiHouseGetSpecialNoticeFilePost$Plain$Response", "apiHouseGetSpecialNoticeFilePost$Json$Response", "ApiHouseGetChangePreOrderPostPath", "apiHouseGetChangePreOrderPost$Plain$Response", "apiHouseGetChangePreOrderPost$Json$Response", "ApiHouseGetHouseReviewPostPath", "apiHouseGetHouseReviewPost$Plain$Response", "apiHouseGetHouseReviewPost$Json$Response", "ApiHouseUpdateHouseReviewPostPath", "apiHouseUpdateHouseReviewPost$Plain$Response", "apiHouseUpdateHouseReviewPost$Json$Response", "ApiHouseGetHouseRequirementPostPath", "apiHouseGetHouseRequirementPost$Plain$Response", "apiHouseGetHouseRequirementPost$Json$Response", "ApiHouseUpdateHouseRequirementPostPath", "apiHouseUpdateHouseRequirementPost$Plain$Response", "apiHouseUpdateHouseRequirementPost$Json$Response", "ApiHouseCheckIsReviewPostPath", "apiHouseCheckIsReviewPost$Plain$Response", "apiHouseCheckIsReviewPost$Json$Response", "ApiHouseGetHouseInfoPostPath", "apiHouseGetHouseInfoPost$Plain$Response", "apiHouseGetHouseInfoPost$Json$Response", "ApiHouseEditHouseInfoPostPath", "apiHouseEditHouseInfoPost$Plain$Response", "apiHouseEditHouseInfoPost$Json$Response", "ApiHouseGetMilestonePostPath", "apiHouseGetMilestonePost$Plain$Response", "apiHouseGetMilestonePost$Json$Response", "ApiHouseSaveMilestonePostPath", "apiHouseSaveMilestonePost$Plain$Response", "apiHouseSaveMilestonePost$Json$Response", "ApiHouseGetListBuildingPostPath", "apiHouseGetListBuildingPost$Plain$Response", "apiHouseGetListBuildingPost$Json$Response", "ApiHouseGetListHouseHoldPostPath", "apiHouseGetListHouseHoldPost$Plain$Response", "apiHouseGetListHouseHoldPost$Json$Response", "ApiHouseGetListProgressPostPath", "apiHouseGetListProgressPost$Plain$Response", "apiHouseGetListProgressPost$Json$Response", "ApiHouseGetHouseListPostPath", "apiHouseGetHouseListPost$Plain$Response", "apiHouseGetHouseListPost$Json$Response", "ApiHouseGetHouseByIdPostPath", "apiHouseGetHouseByIdPost$Plain$Response", "apiHouseGetHouseByIdPost$Json$Response", "ApiHouseEditHousePostPath", "apiHouseEditHousePost$Plain$Response", "apiHouseEditHousePost$Json$Response", "ApiHouseEditListHousePostPath", "apiHouseEditListHousePost$Plain$Response", "apiHouseEditListHousePost$Json$Response", "ApiHouseGetListHouseRegularPicPostPath", "apiHouseGetListHouseRegularPicPost$Plain$Response", "apiHouseGetListHouseRegularPicPost$Json$Response", "ApiHouseEditHouseRegularPicPostPath", "apiHouseEditHouseRegularPicPost$Plain$Response", "apiHouseEditHouseRegularPicPost$Json$Response", "ApiHouseDeleteRegularPicturePostPath", "apiHouseDeleteRegularPicturePost$Plain$Response", "apiHouseDeleteRegularPicturePost$Json$Response", "ApiHouseUploadRegularPicPostPath", "apiHouseUploadRegularPicPost$Plain$Response", "apiHouseUploadRegularPicPost$Json$Response", "ApiHouseGetListAppointmentsPostPath", "apiHouseGetListAppointmentsPost$Plain$Response", "apiHouseGetListAppointmentsPost$Json$Response", "ApiHouseGetAppointmentByIdPostPath", "apiHouseGetAppointmentByIdPost$Plain$Response", "apiHouseGetAppointmentByIdPost$Json$Response", "ApiHouseEditAppointmentPostPath", "apiHouseEditAppointmentPost$Plain$Response", "apiHouseEditAppointmentPost$Json$Response", "ApiHouseDeleteAppointmentPostPath", "apiHouseDeleteAppointmentPost$Plain$Response", "apiHouseDeleteAppointmentPost$Json$Response", "ApiHouseCreateAppointmentPostPath", "apiHouseCreateAppointmentPost$Plain$Response", "apiHouseCreateAppointmentPost$Json$Response", "ApiHouseGetHourListAppointmentPostPath", "apiHouseGetHourListAppointmentPost$Plain$Response", "apiHouseGetHourListAppointmentPost$Json$Response", "ApiHouseExportExcelListAppointmentsPostPath", "apiHouseExportExcelListAppointmentsPost$Plain$Response", "apiHouseExportExcelListAppointmentsPost$Json$Response", "ApiHouseGetHouseChangeDatePostPath", "apiHouseGetHouseChangeDatePost$Plain$Response", "apiHouseGetHouseChangeDatePost$Json$Response", "ApiHouseSaveHouseChangeDatePostPath", "apiHouseSaveHouseChangeDatePost$Plain$Response", "apiHouseSaveHouseChangeDatePost$Json$Response", "ApiHouseExportHousePostPath", "apiHouseExportHousePost$Plain$Response", "apiHouseExportHousePost$Json$Response", "ApiHouseImportHousePostPath", "apiHouseImportHousePost$Plain$Response", "apiHouseImportHousePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\house.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiHouseAgreeDisclaimerPost$Json } from '../fn/house/api-house-agree-disclaimer-post-json';\r\nimport { ApiHouseAgreeDisclaimerPost$Json$Params } from '../fn/house/api-house-agree-disclaimer-post-json';\r\nimport { apiHouseAgreeDisclaimerPost$Plain } from '../fn/house/api-house-agree-disclaimer-post-plain';\r\nimport { ApiHouseAgreeDisclaimerPost$Plain$Params } from '../fn/house/api-house-agree-disclaimer-post-plain';\r\nimport { apiHouseCancelChangePreOrderPost$Json } from '../fn/house/api-house-cancel-change-pre-order-post-json';\r\nimport { ApiHouseCancelChangePreOrderPost$Json$Params } from '../fn/house/api-house-cancel-change-pre-order-post-json';\r\nimport { apiHouseCancelChangePreOrderPost$Plain } from '../fn/house/api-house-cancel-change-pre-order-post-plain';\r\nimport { ApiHouseCancelChangePreOrderPost$Plain$Params } from '../fn/house/api-house-cancel-change-pre-order-post-plain';\r\nimport { apiHouseChangePreOrderPost$Json } from '../fn/house/api-house-change-pre-order-post-json';\r\nimport { ApiHouseChangePreOrderPost$Json$Params } from '../fn/house/api-house-change-pre-order-post-json';\r\nimport { apiHouseChangePreOrderPost$Plain } from '../fn/house/api-house-change-pre-order-post-plain';\r\nimport { ApiHouseChangePreOrderPost$Plain$Params } from '../fn/house/api-house-change-pre-order-post-plain';\r\nimport { apiHouseCheckIsReviewPost$Json } from '../fn/house/api-house-check-is-review-post-json';\r\nimport { ApiHouseCheckIsReviewPost$Json$Params } from '../fn/house/api-house-check-is-review-post-json';\r\nimport { apiHouseCheckIsReviewPost$Plain } from '../fn/house/api-house-check-is-review-post-plain';\r\nimport { ApiHouseCheckIsReviewPost$Plain$Params } from '../fn/house/api-house-check-is-review-post-plain';\r\nimport { apiHouseCreateAppointmentPost$Json } from '../fn/house/api-house-create-appointment-post-json';\r\nimport { ApiHouseCreateAppointmentPost$Json$Params } from '../fn/house/api-house-create-appointment-post-json';\r\nimport { apiHouseCreateAppointmentPost$Plain } from '../fn/house/api-house-create-appointment-post-plain';\r\nimport { ApiHouseCreateAppointmentPost$Plain$Params } from '../fn/house/api-house-create-appointment-post-plain';\r\nimport { apiHouseDeleteAppointmentPost$Json } from '../fn/house/api-house-delete-appointment-post-json';\r\nimport { ApiHouseDeleteAppointmentPost$Json$Params } from '../fn/house/api-house-delete-appointment-post-json';\r\nimport { apiHouseDeleteAppointmentPost$Plain } from '../fn/house/api-house-delete-appointment-post-plain';\r\nimport { ApiHouseDeleteAppointmentPost$Plain$Params } from '../fn/house/api-house-delete-appointment-post-plain';\r\nimport { apiHouseDeleteRegularPicturePost$Json } from '../fn/house/api-house-delete-regular-picture-post-json';\r\nimport { ApiHouseDeleteRegularPicturePost$Json$Params } from '../fn/house/api-house-delete-regular-picture-post-json';\r\nimport { apiHouseDeleteRegularPicturePost$Plain } from '../fn/house/api-house-delete-regular-picture-post-plain';\r\nimport { ApiHouseDeleteRegularPicturePost$Plain$Params } from '../fn/house/api-house-delete-regular-picture-post-plain';\r\nimport { apiHouseEditAppointmentPost$Json } from '../fn/house/api-house-edit-appointment-post-json';\r\nimport { ApiHouseEditAppointmentPost$Json$Params } from '../fn/house/api-house-edit-appointment-post-json';\r\nimport { apiHouseEditAppointmentPost$Plain } from '../fn/house/api-house-edit-appointment-post-plain';\r\nimport { ApiHouseEditAppointmentPost$Plain$Params } from '../fn/house/api-house-edit-appointment-post-plain';\r\nimport { apiHouseEditHouseInfoPost$Json } from '../fn/house/api-house-edit-house-info-post-json';\r\nimport { ApiHouseEditHouseInfoPost$Json$Params } from '../fn/house/api-house-edit-house-info-post-json';\r\nimport { apiHouseEditHouseInfoPost$Plain } from '../fn/house/api-house-edit-house-info-post-plain';\r\nimport { ApiHouseEditHouseInfoPost$Plain$Params } from '../fn/house/api-house-edit-house-info-post-plain';\r\nimport { apiHouseEditHousePost$Json } from '../fn/house/api-house-edit-house-post-json';\r\nimport { ApiHouseEditHousePost$Json$Params } from '../fn/house/api-house-edit-house-post-json';\r\nimport { apiHouseEditHousePost$Plain } from '../fn/house/api-house-edit-house-post-plain';\r\nimport { ApiHouseEditHousePost$Plain$Params } from '../fn/house/api-house-edit-house-post-plain';\r\nimport { apiHouseEditHouseRegularPicPost$Json } from '../fn/house/api-house-edit-house-regular-pic-post-json';\r\nimport { ApiHouseEditHouseRegularPicPost$Json$Params } from '../fn/house/api-house-edit-house-regular-pic-post-json';\r\nimport { apiHouseEditHouseRegularPicPost$Plain } from '../fn/house/api-house-edit-house-regular-pic-post-plain';\r\nimport { ApiHouseEditHouseRegularPicPost$Plain$Params } from '../fn/house/api-house-edit-house-regular-pic-post-plain';\r\nimport { apiHouseEditListHousePost$Json } from '../fn/house/api-house-edit-list-house-post-json';\r\nimport { ApiHouseEditListHousePost$Json$Params } from '../fn/house/api-house-edit-list-house-post-json';\r\nimport { apiHouseEditListHousePost$Plain } from '../fn/house/api-house-edit-list-house-post-plain';\r\nimport { ApiHouseEditListHousePost$Plain$Params } from '../fn/house/api-house-edit-list-house-post-plain';\r\nimport { apiHouseExportExcelListAppointmentsPost$Json } from '../fn/house/api-house-export-excel-list-appointments-post-json';\r\nimport { ApiHouseExportExcelListAppointmentsPost$Json$Params } from '../fn/house/api-house-export-excel-list-appointments-post-json';\r\nimport { apiHouseExportExcelListAppointmentsPost$Plain } from '../fn/house/api-house-export-excel-list-appointments-post-plain';\r\nimport { ApiHouseExportExcelListAppointmentsPost$Plain$Params } from '../fn/house/api-house-export-excel-list-appointments-post-plain';\r\nimport { apiHouseExportHousePost$Json } from '../fn/house/api-house-export-house-post-json';\r\nimport { ApiHouseExportHousePost$Json$Params } from '../fn/house/api-house-export-house-post-json';\r\nimport { apiHouseExportHousePost$Plain } from '../fn/house/api-house-export-house-post-plain';\r\nimport { ApiHouseExportHousePost$Plain$Params } from '../fn/house/api-house-export-house-post-plain';\r\nimport { apiHouseGetAppointmentByIdPost$Json } from '../fn/house/api-house-get-appointment-by-id-post-json';\r\nimport { ApiHouseGetAppointmentByIdPost$Json$Params } from '../fn/house/api-house-get-appointment-by-id-post-json';\r\nimport { apiHouseGetAppointmentByIdPost$Plain } from '../fn/house/api-house-get-appointment-by-id-post-plain';\r\nimport { ApiHouseGetAppointmentByIdPost$Plain$Params } from '../fn/house/api-house-get-appointment-by-id-post-plain';\r\nimport { apiHouseGetChangeDatePost$Json } from '../fn/house/api-house-get-change-date-post-json';\r\nimport { ApiHouseGetChangeDatePost$Json$Params } from '../fn/house/api-house-get-change-date-post-json';\r\nimport { apiHouseGetChangeDatePost$Plain } from '../fn/house/api-house-get-change-date-post-plain';\r\nimport { ApiHouseGetChangeDatePost$Plain$Params } from '../fn/house/api-house-get-change-date-post-plain';\r\nimport { apiHouseGetChangePreOrderPost$Json } from '../fn/house/api-house-get-change-pre-order-post-json';\r\nimport { ApiHouseGetChangePreOrderPost$Json$Params } from '../fn/house/api-house-get-change-pre-order-post-json';\r\nimport { apiHouseGetChangePreOrderPost$Plain } from '../fn/house/api-house-get-change-pre-order-post-plain';\r\nimport { ApiHouseGetChangePreOrderPost$Plain$Params } from '../fn/house/api-house-get-change-pre-order-post-plain';\r\nimport { apiHouseGetHourListAppointmentPost$Json } from '../fn/house/api-house-get-hour-list-appointment-post-json';\r\nimport { ApiHouseGetHourListAppointmentPost$Json$Params } from '../fn/house/api-house-get-hour-list-appointment-post-json';\r\nimport { apiHouseGetHourListAppointmentPost$Plain } from '../fn/house/api-house-get-hour-list-appointment-post-plain';\r\nimport { ApiHouseGetHourListAppointmentPost$Plain$Params } from '../fn/house/api-house-get-hour-list-appointment-post-plain';\r\nimport { apiHouseGetHourListPost$Json } from '../fn/house/api-house-get-hour-list-post-json';\r\nimport { ApiHouseGetHourListPost$Json$Params } from '../fn/house/api-house-get-hour-list-post-json';\r\nimport { apiHouseGetHourListPost$Plain } from '../fn/house/api-house-get-hour-list-post-plain';\r\nimport { ApiHouseGetHourListPost$Plain$Params } from '../fn/house/api-house-get-hour-list-post-plain';\r\nimport { apiHouseGetHouseByIdPost$Json } from '../fn/house/api-house-get-house-by-id-post-json';\r\nimport { ApiHouseGetHouseByIdPost$Json$Params } from '../fn/house/api-house-get-house-by-id-post-json';\r\nimport { apiHouseGetHouseByIdPost$Plain } from '../fn/house/api-house-get-house-by-id-post-plain';\r\nimport { ApiHouseGetHouseByIdPost$Plain$Params } from '../fn/house/api-house-get-house-by-id-post-plain';\r\nimport { apiHouseGetHouseChangeDatePost$Json } from '../fn/house/api-house-get-house-change-date-post-json';\r\nimport { ApiHouseGetHouseChangeDatePost$Json$Params } from '../fn/house/api-house-get-house-change-date-post-json';\r\nimport { apiHouseGetHouseChangeDatePost$Plain } from '../fn/house/api-house-get-house-change-date-post-plain';\r\nimport { ApiHouseGetHouseChangeDatePost$Plain$Params } from '../fn/house/api-house-get-house-change-date-post-plain';\r\nimport { apiHouseGetHouseInfoPost$Json } from '../fn/house/api-house-get-house-info-post-json';\r\nimport { ApiHouseGetHouseInfoPost$Json$Params } from '../fn/house/api-house-get-house-info-post-json';\r\nimport { apiHouseGetHouseInfoPost$Plain } from '../fn/house/api-house-get-house-info-post-plain';\r\nimport { ApiHouseGetHouseInfoPost$Plain$Params } from '../fn/house/api-house-get-house-info-post-plain';\r\nimport { apiHouseGetHouseListPost$Json } from '../fn/house/api-house-get-house-list-post-json';\r\nimport { ApiHouseGetHouseListPost$Json$Params } from '../fn/house/api-house-get-house-list-post-json';\r\nimport { apiHouseGetHouseListPost$Plain } from '../fn/house/api-house-get-house-list-post-plain';\r\nimport { ApiHouseGetHouseListPost$Plain$Params } from '../fn/house/api-house-get-house-list-post-plain';\r\nimport { apiHouseGetHouseRegularPicturePost$Json } from '../fn/house/api-house-get-house-regular-picture-post-json';\r\nimport { ApiHouseGetHouseRegularPicturePost$Json$Params } from '../fn/house/api-house-get-house-regular-picture-post-json';\r\nimport { apiHouseGetHouseRegularPicturePost$Plain } from '../fn/house/api-house-get-house-regular-picture-post-plain';\r\nimport { ApiHouseGetHouseRegularPicturePost$Plain$Params } from '../fn/house/api-house-get-house-regular-picture-post-plain';\r\nimport { apiHouseGetHouseRequirementPost$Json } from '../fn/house/api-house-get-house-requirement-post-json';\r\nimport { ApiHouseGetHouseRequirementPost$Json$Params } from '../fn/house/api-house-get-house-requirement-post-json';\r\nimport { apiHouseGetHouseRequirementPost$Plain } from '../fn/house/api-house-get-house-requirement-post-plain';\r\nimport { ApiHouseGetHouseRequirementPost$Plain$Params } from '../fn/house/api-house-get-house-requirement-post-plain';\r\nimport { apiHouseGetHouseReviewPost$Json } from '../fn/house/api-house-get-house-review-post-json';\r\nimport { ApiHouseGetHouseReviewPost$Json$Params } from '../fn/house/api-house-get-house-review-post-json';\r\nimport { apiHouseGetHouseReviewPost$Plain } from '../fn/house/api-house-get-house-review-post-plain';\r\nimport { ApiHouseGetHouseReviewPost$Plain$Params } from '../fn/house/api-house-get-house-review-post-plain';\r\nimport { apiHouseGetListAppointmentsPost$Json } from '../fn/house/api-house-get-list-appointments-post-json';\r\nimport { ApiHouseGetListAppointmentsPost$Json$Params } from '../fn/house/api-house-get-list-appointments-post-json';\r\nimport { apiHouseGetListAppointmentsPost$Plain } from '../fn/house/api-house-get-list-appointments-post-plain';\r\nimport { ApiHouseGetListAppointmentsPost$Plain$Params } from '../fn/house/api-house-get-list-appointments-post-plain';\r\nimport { apiHouseGetListBuildingPost$Json } from '../fn/house/api-house-get-list-building-post-json';\r\nimport { ApiHouseGetListBuildingPost$Json$Params } from '../fn/house/api-house-get-list-building-post-json';\r\nimport { apiHouseGetListBuildingPost$Plain } from '../fn/house/api-house-get-list-building-post-plain';\r\nimport { ApiHouseGetListBuildingPost$Plain$Params } from '../fn/house/api-house-get-list-building-post-plain';\r\nimport { apiHouseGetListHouseHoldPost$Json } from '../fn/house/api-house-get-list-house-hold-post-json';\r\nimport { ApiHouseGetListHouseHoldPost$Json$Params } from '../fn/house/api-house-get-list-house-hold-post-json';\r\nimport { apiHouseGetListHouseHoldPost$Plain } from '../fn/house/api-house-get-list-house-hold-post-plain';\r\nimport { ApiHouseGetListHouseHoldPost$Plain$Params } from '../fn/house/api-house-get-list-house-hold-post-plain';\r\nimport { apiHouseGetListHouseRegularPicPost$Json } from '../fn/house/api-house-get-list-house-regular-pic-post-json';\r\nimport { ApiHouseGetListHouseRegularPicPost$Json$Params } from '../fn/house/api-house-get-list-house-regular-pic-post-json';\r\nimport { apiHouseGetListHouseRegularPicPost$Plain } from '../fn/house/api-house-get-list-house-regular-pic-post-plain';\r\nimport { ApiHouseGetListHouseRegularPicPost$Plain$Params } from '../fn/house/api-house-get-list-house-regular-pic-post-plain';\r\nimport { apiHouseGetListProgressPost$Json } from '../fn/house/api-house-get-list-progress-post-json';\r\nimport { ApiHouseGetListProgressPost$Json$Params } from '../fn/house/api-house-get-list-progress-post-json';\r\nimport { apiHouseGetListProgressPost$Plain } from '../fn/house/api-house-get-list-progress-post-plain';\r\nimport { ApiHouseGetListProgressPost$Plain$Params } from '../fn/house/api-house-get-list-progress-post-plain';\r\nimport { apiHouseGetMilestonePost$Json } from '../fn/house/api-house-get-milestone-post-json';\r\nimport { ApiHouseGetMilestonePost$Json$Params } from '../fn/house/api-house-get-milestone-post-json';\r\nimport { apiHouseGetMilestonePost$Plain } from '../fn/house/api-house-get-milestone-post-plain';\r\nimport { ApiHouseGetMilestonePost$Plain$Params } from '../fn/house/api-house-get-milestone-post-plain';\r\nimport { apiHouseGetRegularNoticeFilePost$Json } from '../fn/house/api-house-get-regular-notice-file-post-json';\r\nimport { ApiHouseGetRegularNoticeFilePost$Json$Params } from '../fn/house/api-house-get-regular-notice-file-post-json';\r\nimport { apiHouseGetRegularNoticeFilePost$Plain } from '../fn/house/api-house-get-regular-notice-file-post-plain';\r\nimport { ApiHouseGetRegularNoticeFilePost$Plain$Params } from '../fn/house/api-house-get-regular-notice-file-post-plain';\r\nimport { apiHouseGetSpecialNoticeFilePost$Json } from '../fn/house/api-house-get-special-notice-file-post-json';\r\nimport { ApiHouseGetSpecialNoticeFilePost$Json$Params } from '../fn/house/api-house-get-special-notice-file-post-json';\r\nimport { apiHouseGetSpecialNoticeFilePost$Plain } from '../fn/house/api-house-get-special-notice-file-post-plain';\r\nimport { ApiHouseGetSpecialNoticeFilePost$Plain$Params } from '../fn/house/api-house-get-special-notice-file-post-plain';\r\nimport { apiHouseHouseLoginStep2Post$Json } from '../fn/house/api-house-house-login-step-2-post-json';\r\nimport { ApiHouseHouseLoginStep2Post$Json$Params } from '../fn/house/api-house-house-login-step-2-post-json';\r\nimport { apiHouseHouseLoginStep2Post$Plain } from '../fn/house/api-house-house-login-step-2-post-plain';\r\nimport { ApiHouseHouseLoginStep2Post$Plain$Params } from '../fn/house/api-house-house-login-step-2-post-plain';\r\nimport { apiHouseImportHousePost$Json } from '../fn/house/api-house-import-house-post-json';\r\nimport { ApiHouseImportHousePost$Json$Params } from '../fn/house/api-house-import-house-post-json';\r\nimport { apiHouseImportHousePost$Plain } from '../fn/house/api-house-import-house-post-plain';\r\nimport { ApiHouseImportHousePost$Plain$Params } from '../fn/house/api-house-import-house-post-plain';\r\nimport { apiHouseLoginPost$Json } from '../fn/house/api-house-login-post-json';\r\nimport { ApiHouseLoginPost$Json$Params } from '../fn/house/api-house-login-post-json';\r\nimport { apiHouseLoginPost$Plain } from '../fn/house/api-house-login-post-plain';\r\nimport { ApiHouseLoginPost$Plain$Params } from '../fn/house/api-house-login-post-plain';\r\nimport { apiHouseSaveHouseChangeDatePost$Json } from '../fn/house/api-house-save-house-change-date-post-json';\r\nimport { ApiHouseSaveHouseChangeDatePost$Json$Params } from '../fn/house/api-house-save-house-change-date-post-json';\r\nimport { apiHouseSaveHouseChangeDatePost$Plain } from '../fn/house/api-house-save-house-change-date-post-plain';\r\nimport { ApiHouseSaveHouseChangeDatePost$Plain$Params } from '../fn/house/api-house-save-house-change-date-post-plain';\r\nimport { apiHouseSaveMilestonePost$Json } from '../fn/house/api-house-save-milestone-post-json';\r\nimport { ApiHouseSaveMilestonePost$Json$Params } from '../fn/house/api-house-save-milestone-post-json';\r\nimport { apiHouseSaveMilestonePost$Plain } from '../fn/house/api-house-save-milestone-post-plain';\r\nimport { ApiHouseSaveMilestonePost$Plain$Params } from '../fn/house/api-house-save-milestone-post-plain';\r\nimport { apiHouseUpdateHouseRequirementPost$Json } from '../fn/house/api-house-update-house-requirement-post-json';\r\nimport { ApiHouseUpdateHouseRequirementPost$Json$Params } from '../fn/house/api-house-update-house-requirement-post-json';\r\nimport { apiHouseUpdateHouseRequirementPost$Plain } from '../fn/house/api-house-update-house-requirement-post-plain';\r\nimport { ApiHouseUpdateHouseRequirementPost$Plain$Params } from '../fn/house/api-house-update-house-requirement-post-plain';\r\nimport { apiHouseUpdateHouseReviewPost$Json } from '../fn/house/api-house-update-house-review-post-json';\r\nimport { ApiHouseUpdateHouseReviewPost$Json$Params } from '../fn/house/api-house-update-house-review-post-json';\r\nimport { apiHouseUpdateHouseReviewPost$Plain } from '../fn/house/api-house-update-house-review-post-plain';\r\nimport { ApiHouseUpdateHouseReviewPost$Plain$Params } from '../fn/house/api-house-update-house-review-post-plain';\r\nimport { apiHouseUploadRegularPicPost$Json } from '../fn/house/api-house-upload-regular-pic-post-json';\r\nimport { ApiHouseUploadRegularPicPost$Json$Params } from '../fn/house/api-house-upload-regular-pic-post-json';\r\nimport { apiHouseUploadRegularPicPost$Plain } from '../fn/house/api-house-upload-regular-pic-post-plain';\r\nimport { ApiHouseUploadRegularPicPost$Plain$Params } from '../fn/house/api-house-upload-regular-pic-post-plain';\r\nimport { BooleanResponseBase } from '../models/boolean-response-base';\r\nimport { ByteArrayResponseBase } from '../models/byte-array-response-base';\r\nimport { ExportExcelResponseResponseBase } from '../models/export-excel-response-response-base';\r\nimport { GetAppoinmentResListResponseBase } from '../models/get-appoinment-res-list-response-base';\r\nimport { GetAppoinmentResResponseBase } from '../models/get-appoinment-res-response-base';\r\nimport { GetChangePreOrderResponeResponseBase } from '../models/get-change-pre-order-respone-response-base';\r\nimport { GetHourListResponeListResponseBase } from '../models/get-hour-list-respone-list-response-base';\r\nimport { GetHouseChangeDateResListResponseBase } from '../models/get-house-change-date-res-list-response-base';\r\nimport { GetHouseListResListResponseBase } from '../models/get-house-list-res-list-response-base';\r\nimport { GetHouseReviewListResponseBase } from '../models/get-house-review-list-response-base';\r\nimport { GetListHouseRegularPicResListResponseBase } from '../models/get-list-house-regular-pic-res-list-response-base';\r\nimport { GetMilestoneResResponseBase } from '../models/get-milestone-res-response-base';\r\nimport { HouseGetChangeDateResponeResponseBase } from '../models/house-get-change-date-respone-response-base';\r\nimport { HouseLoginResponseResponseBase } from '../models/house-login-response-response-base';\r\nimport { HouseRegularPicResponseBase } from '../models/house-regular-pic-response-base';\r\nimport { HouseRequirementResListResponseBase } from '../models/house-requirement-res-list-response-base';\r\nimport { StringListResponseBase } from '../models/string-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\nimport { TblHouseResponseBase } from '../models/tbl-house-response-base';\r\nimport { TblRegularNoticeFileResponseBase } from '../models/tbl-regular-notice-file-response-base';\r\nimport { TblSpecialNoticeFileResponseBase } from '../models/tbl-special-notice-file-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class HouseService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiHouseLoginPost()` */\r\n  static readonly ApiHouseLoginPostPath = '/api/House/Login';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseLoginPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseLoginPost$Plain$Response(params?: ApiHouseLoginPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseLoginResponseResponseBase>> {\r\n    return apiHouseLoginPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseLoginPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseLoginPost$Plain(params?: ApiHouseLoginPost$Plain$Params, context?: HttpContext): Observable<HouseLoginResponseResponseBase> {\r\n    return this.apiHouseLoginPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseLoginResponseResponseBase>): HouseLoginResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseLoginPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseLoginPost$Json$Response(params?: ApiHouseLoginPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseLoginResponseResponseBase>> {\r\n    return apiHouseLoginPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseLoginPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseLoginPost$Json(params?: ApiHouseLoginPost$Json$Params, context?: HttpContext): Observable<HouseLoginResponseResponseBase> {\r\n    return this.apiHouseLoginPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseLoginResponseResponseBase>): HouseLoginResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetChangeDatePost()` */\r\n  static readonly ApiHouseGetChangeDatePostPath = '/api/House/GetChangeDate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetChangeDatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangeDatePost$Plain$Response(params?: ApiHouseGetChangeDatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseGetChangeDateResponeResponseBase>> {\r\n    return apiHouseGetChangeDatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangeDatePost$Plain(params?: ApiHouseGetChangeDatePost$Plain$Params, context?: HttpContext): Observable<HouseGetChangeDateResponeResponseBase> {\r\n    return this.apiHouseGetChangeDatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseGetChangeDateResponeResponseBase>): HouseGetChangeDateResponeResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetChangeDatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangeDatePost$Json$Response(params?: ApiHouseGetChangeDatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseGetChangeDateResponeResponseBase>> {\r\n    return apiHouseGetChangeDatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangeDatePost$Json(params?: ApiHouseGetChangeDatePost$Json$Params, context?: HttpContext): Observable<HouseGetChangeDateResponeResponseBase> {\r\n    return this.apiHouseGetChangeDatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseGetChangeDateResponeResponseBase>): HouseGetChangeDateResponeResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseChangePreOrderPost()` */\r\n  static readonly ApiHouseChangePreOrderPostPath = '/api/House/ChangePreOrder';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseChangePreOrderPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseChangePreOrderPost$Plain$Response(params?: ApiHouseChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseChangePreOrderPost$Plain(params?: ApiHouseChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseChangePreOrderPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseChangePreOrderPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseChangePreOrderPost$Json$Response(params?: ApiHouseChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseChangePreOrderPost$Json(params?: ApiHouseChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseChangePreOrderPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHourListPost()` */\r\n  static readonly ApiHouseGetHourListPostPath = '/api/House/GetHourList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHourListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListPost$Plain$Response(params?: ApiHouseGetHourListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n    return apiHouseGetHourListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHourListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListPost$Plain(params?: ApiHouseGetHourListPost$Plain$Params, context?: HttpContext): Observable<GetHourListResponeListResponseBase> {\r\n    return this.apiHouseGetHourListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHourListResponeListResponseBase>): GetHourListResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHourListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListPost$Json$Response(params?: ApiHouseGetHourListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n    return apiHouseGetHourListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHourListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListPost$Json(params?: ApiHouseGetHourListPost$Json$Params, context?: HttpContext): Observable<GetHourListResponeListResponseBase> {\r\n    return this.apiHouseGetHourListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHourListResponeListResponseBase>): GetHourListResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseCancelChangePreOrderPost()` */\r\n  static readonly ApiHouseCancelChangePreOrderPostPath = '/api/House/CancelChangePreOrder';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCancelChangePreOrderPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCancelChangePreOrderPost$Plain$Response(params?: ApiHouseCancelChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseCancelChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCancelChangePreOrderPost$Plain(params?: ApiHouseCancelChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseCancelChangePreOrderPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCancelChangePreOrderPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCancelChangePreOrderPost$Json$Response(params?: ApiHouseCancelChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseCancelChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCancelChangePreOrderPost$Json(params?: ApiHouseCancelChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseCancelChangePreOrderPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseRegularPicturePost()` */\r\n  static readonly ApiHouseGetHouseRegularPicturePostPath = '/api/House/GetHouseRegularPicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRegularPicturePost$Plain$Response(params?: ApiHouseGetHouseRegularPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseRegularPicResponseBase>> {\r\n    return apiHouseGetHouseRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRegularPicturePost$Plain(params?: ApiHouseGetHouseRegularPicturePost$Plain$Params, context?: HttpContext): Observable<HouseRegularPicResponseBase> {\r\n    return this.apiHouseGetHouseRegularPicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseRegularPicResponseBase>): HouseRegularPicResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRegularPicturePost$Json$Response(params?: ApiHouseGetHouseRegularPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseRegularPicResponseBase>> {\r\n    return apiHouseGetHouseRegularPicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRegularPicturePost$Json(params?: ApiHouseGetHouseRegularPicturePost$Json$Params, context?: HttpContext): Observable<HouseRegularPicResponseBase> {\r\n    return this.apiHouseGetHouseRegularPicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseRegularPicResponseBase>): HouseRegularPicResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseHouseLoginStep2Post()` */\r\n  static readonly ApiHouseHouseLoginStep2PostPath = '/api/House/HouseLoginStep2';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHouseLoginStep2Post$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHouseLoginStep2Post$Plain$Response(params?: ApiHouseHouseLoginStep2Post$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseHouseLoginStep2Post$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHouseLoginStep2Post$Plain(params?: ApiHouseHouseLoginStep2Post$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseHouseLoginStep2Post$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHouseLoginStep2Post$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHouseLoginStep2Post$Json$Response(params?: ApiHouseHouseLoginStep2Post$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseHouseLoginStep2Post$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHouseLoginStep2Post$Json(params?: ApiHouseHouseLoginStep2Post$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseHouseLoginStep2Post$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseAgreeDisclaimerPost()` */\r\n  static readonly ApiHouseAgreeDisclaimerPostPath = '/api/House/AgreeDisclaimer';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseAgreeDisclaimerPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseAgreeDisclaimerPost$Plain$Response(params?: ApiHouseAgreeDisclaimerPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseAgreeDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseAgreeDisclaimerPost$Plain(params?: ApiHouseAgreeDisclaimerPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseAgreeDisclaimerPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseAgreeDisclaimerPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseAgreeDisclaimerPost$Json$Response(params?: ApiHouseAgreeDisclaimerPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseAgreeDisclaimerPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseAgreeDisclaimerPost$Json(params?: ApiHouseAgreeDisclaimerPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseAgreeDisclaimerPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetRegularNoticeFilePost()` */\r\n  static readonly ApiHouseGetRegularNoticeFilePostPath = '/api/House/GetRegularNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetRegularNoticeFilePost$Plain$Response(params?: ApiHouseGetRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblRegularNoticeFileResponseBase>> {\r\n    return apiHouseGetRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetRegularNoticeFilePost$Plain(params?: ApiHouseGetRegularNoticeFilePost$Plain$Params, context?: HttpContext): Observable<TblRegularNoticeFileResponseBase> {\r\n    return this.apiHouseGetRegularNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblRegularNoticeFileResponseBase>): TblRegularNoticeFileResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetRegularNoticeFilePost$Json$Response(params?: ApiHouseGetRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblRegularNoticeFileResponseBase>> {\r\n    return apiHouseGetRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetRegularNoticeFilePost$Json(params?: ApiHouseGetRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<TblRegularNoticeFileResponseBase> {\r\n    return this.apiHouseGetRegularNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblRegularNoticeFileResponseBase>): TblRegularNoticeFileResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetSpecialNoticeFilePost()` */\r\n  static readonly ApiHouseGetSpecialNoticeFilePostPath = '/api/House/GetSpecialNoticeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetSpecialNoticeFilePost$Plain$Response(params?: ApiHouseGetSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblSpecialNoticeFileResponseBase>> {\r\n    return apiHouseGetSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetSpecialNoticeFilePost$Plain(params?: ApiHouseGetSpecialNoticeFilePost$Plain$Params, context?: HttpContext): Observable<TblSpecialNoticeFileResponseBase> {\r\n    return this.apiHouseGetSpecialNoticeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblSpecialNoticeFileResponseBase>): TblSpecialNoticeFileResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetSpecialNoticeFilePost$Json$Response(params?: ApiHouseGetSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblSpecialNoticeFileResponseBase>> {\r\n    return apiHouseGetSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetSpecialNoticeFilePost$Json(params?: ApiHouseGetSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<TblSpecialNoticeFileResponseBase> {\r\n    return this.apiHouseGetSpecialNoticeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblSpecialNoticeFileResponseBase>): TblSpecialNoticeFileResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetChangePreOrderPost()` */\r\n  static readonly ApiHouseGetChangePreOrderPostPath = '/api/House/GetChangePreOrder';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetChangePreOrderPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangePreOrderPost$Plain$Response(params?: ApiHouseGetChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetChangePreOrderResponeResponseBase>> {\r\n    return apiHouseGetChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangePreOrderPost$Plain(params?: ApiHouseGetChangePreOrderPost$Plain$Params, context?: HttpContext): Observable<GetChangePreOrderResponeResponseBase> {\r\n    return this.apiHouseGetChangePreOrderPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetChangePreOrderResponeResponseBase>): GetChangePreOrderResponeResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetChangePreOrderPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangePreOrderPost$Json$Response(params?: ApiHouseGetChangePreOrderPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetChangePreOrderResponeResponseBase>> {\r\n    return apiHouseGetChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetChangePreOrderPost$Json(params?: ApiHouseGetChangePreOrderPost$Json$Params, context?: HttpContext): Observable<GetChangePreOrderResponeResponseBase> {\r\n    return this.apiHouseGetChangePreOrderPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetChangePreOrderResponeResponseBase>): GetChangePreOrderResponeResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseReviewPost()` */\r\n  static readonly ApiHouseGetHouseReviewPostPath = '/api/House/GetHouseReview';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseReviewPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseReviewPost$Plain$Response(params?: ApiHouseGetHouseReviewPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseReviewListResponseBase>> {\r\n    return apiHouseGetHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseReviewPost$Plain(params?: ApiHouseGetHouseReviewPost$Plain$Params, context?: HttpContext): Observable<GetHouseReviewListResponseBase> {\r\n    return this.apiHouseGetHouseReviewPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseReviewListResponseBase>): GetHouseReviewListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseReviewPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseReviewPost$Json$Response(params?: ApiHouseGetHouseReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseReviewListResponseBase>> {\r\n    return apiHouseGetHouseReviewPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseReviewPost$Json(params?: ApiHouseGetHouseReviewPost$Json$Params, context?: HttpContext): Observable<GetHouseReviewListResponseBase> {\r\n    return this.apiHouseGetHouseReviewPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseReviewListResponseBase>): GetHouseReviewListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseUpdateHouseReviewPost()` */\r\n  static readonly ApiHouseUpdateHouseReviewPostPath = '/api/House/UpdateHouseReview';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUpdateHouseReviewPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseReviewPost$Plain$Response(params?: ApiHouseUpdateHouseReviewPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUpdateHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseReviewPost$Plain(params?: ApiHouseUpdateHouseReviewPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUpdateHouseReviewPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUpdateHouseReviewPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseReviewPost$Json$Response(params?: ApiHouseUpdateHouseReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUpdateHouseReviewPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseReviewPost$Json(params?: ApiHouseUpdateHouseReviewPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUpdateHouseReviewPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseRequirementPost()` */\r\n  static readonly ApiHouseGetHouseRequirementPostPath = '/api/House/GetHouseRequirement';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseRequirementPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRequirementPost$Plain$Response(params?: ApiHouseGetHouseRequirementPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseRequirementResListResponseBase>> {\r\n    return apiHouseGetHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRequirementPost$Plain(params?: ApiHouseGetHouseRequirementPost$Plain$Params, context?: HttpContext): Observable<HouseRequirementResListResponseBase> {\r\n    return this.apiHouseGetHouseRequirementPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseRequirementResListResponseBase>): HouseRequirementResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseRequirementPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRequirementPost$Json$Response(params?: ApiHouseGetHouseRequirementPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HouseRequirementResListResponseBase>> {\r\n    return apiHouseGetHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseRequirementPost$Json(params?: ApiHouseGetHouseRequirementPost$Json$Params, context?: HttpContext): Observable<HouseRequirementResListResponseBase> {\r\n    return this.apiHouseGetHouseRequirementPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HouseRequirementResListResponseBase>): HouseRequirementResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseUpdateHouseRequirementPost()` */\r\n  static readonly ApiHouseUpdateHouseRequirementPostPath = '/api/House/UpdateHouseRequirement';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseRequirementPost$Plain$Response(params?: ApiHouseUpdateHouseRequirementPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUpdateHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseRequirementPost$Plain(params?: ApiHouseUpdateHouseRequirementPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUpdateHouseRequirementPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseRequirementPost$Json$Response(params?: ApiHouseUpdateHouseRequirementPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUpdateHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUpdateHouseRequirementPost$Json(params?: ApiHouseUpdateHouseRequirementPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUpdateHouseRequirementPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseCheckIsReviewPost()` */\r\n  static readonly ApiHouseCheckIsReviewPostPath = '/api/House/CheckIsReview';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCheckIsReviewPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseCheckIsReviewPost$Plain$Response(params?: ApiHouseCheckIsReviewPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {\r\n    return apiHouseCheckIsReviewPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseCheckIsReviewPost$Plain(params?: ApiHouseCheckIsReviewPost$Plain$Params, context?: HttpContext): Observable<BooleanResponseBase> {\r\n    return this.apiHouseCheckIsReviewPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCheckIsReviewPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseCheckIsReviewPost$Json$Response(params?: ApiHouseCheckIsReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<BooleanResponseBase>> {\r\n    return apiHouseCheckIsReviewPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseCheckIsReviewPost$Json(params?: ApiHouseCheckIsReviewPost$Json$Params, context?: HttpContext): Observable<BooleanResponseBase> {\r\n    return this.apiHouseCheckIsReviewPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<BooleanResponseBase>): BooleanResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseInfoPost()` */\r\n  static readonly ApiHouseGetHouseInfoPostPath = '/api/House/GetHouseInfo';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseInfoPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseInfoPost$Plain$Response(params?: ApiHouseGetHouseInfoPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHouseResponseBase>> {\r\n    return apiHouseGetHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseInfoPost$Plain(params?: ApiHouseGetHouseInfoPost$Plain$Params, context?: HttpContext): Observable<TblHouseResponseBase> {\r\n    return this.apiHouseGetHouseInfoPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblHouseResponseBase>): TblHouseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseInfoPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseInfoPost$Json$Response(params?: ApiHouseGetHouseInfoPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHouseResponseBase>> {\r\n    return apiHouseGetHouseInfoPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetHouseInfoPost$Json(params?: ApiHouseGetHouseInfoPost$Json$Params, context?: HttpContext): Observable<TblHouseResponseBase> {\r\n    return this.apiHouseGetHouseInfoPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblHouseResponseBase>): TblHouseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseEditHouseInfoPost()` */\r\n  static readonly ApiHouseEditHouseInfoPostPath = '/api/House/EditHouseInfo';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHouseInfoPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseInfoPost$Plain$Response(params?: ApiHouseEditHouseInfoPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseInfoPost$Plain(params?: ApiHouseEditHouseInfoPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHouseInfoPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHouseInfoPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseInfoPost$Json$Response(params?: ApiHouseEditHouseInfoPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHouseInfoPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseInfoPost$Json(params?: ApiHouseEditHouseInfoPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHouseInfoPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetMilestonePost()` */\r\n  static readonly ApiHouseGetMilestonePostPath = '/api/House/GetMilestone';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetMilestonePost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetMilestonePost$Plain$Response(params?: ApiHouseGetMilestonePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMilestoneResResponseBase>> {\r\n    return apiHouseGetMilestonePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetMilestonePost$Plain(params?: ApiHouseGetMilestonePost$Plain$Params, context?: HttpContext): Observable<GetMilestoneResResponseBase> {\r\n    return this.apiHouseGetMilestonePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMilestoneResResponseBase>): GetMilestoneResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetMilestonePost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetMilestonePost$Json$Response(params?: ApiHouseGetMilestonePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetMilestoneResResponseBase>> {\r\n    return apiHouseGetMilestonePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseGetMilestonePost$Json(params?: ApiHouseGetMilestonePost$Json$Params, context?: HttpContext): Observable<GetMilestoneResResponseBase> {\r\n    return this.apiHouseGetMilestonePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetMilestoneResResponseBase>): GetMilestoneResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseSaveMilestonePost()` */\r\n  static readonly ApiHouseSaveMilestonePostPath = '/api/House/SaveMilestone';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseSaveMilestonePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveMilestonePost$Plain$Response(params?: ApiHouseSaveMilestonePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseSaveMilestonePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveMilestonePost$Plain(params?: ApiHouseSaveMilestonePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseSaveMilestonePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseSaveMilestonePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveMilestonePost$Json$Response(params?: ApiHouseSaveMilestonePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseSaveMilestonePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveMilestonePost$Json(params?: ApiHouseSaveMilestonePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseSaveMilestonePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetListBuildingPost()` */\r\n  static readonly ApiHouseGetListBuildingPostPath = '/api/House/GetListBuilding';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListBuildingPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListBuildingPost$Plain$Response(params?: ApiHouseGetListBuildingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListBuildingPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListBuildingPost$Plain(params?: ApiHouseGetListBuildingPost$Plain$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListBuildingPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListBuildingPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListBuildingPost$Json$Response(params?: ApiHouseGetListBuildingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListBuildingPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListBuildingPost$Json(params?: ApiHouseGetListBuildingPost$Json$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListBuildingPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetListHouseHoldPost()` */\r\n  static readonly ApiHouseGetListHouseHoldPostPath = '/api/House/GetListHouseHold';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListHouseHoldPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseHoldPost$Plain$Response(params?: ApiHouseGetListHouseHoldPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseHoldPost$Plain(params?: ApiHouseGetListHouseHoldPost$Plain$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListHouseHoldPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListHouseHoldPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseHoldPost$Json$Response(params?: ApiHouseGetListHouseHoldPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListHouseHoldPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseHoldPost$Json(params?: ApiHouseGetListHouseHoldPost$Json$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListHouseHoldPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetListProgressPost()` */\r\n  static readonly ApiHouseGetListProgressPostPath = '/api/House/GetListProgress';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListProgressPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListProgressPost$Plain$Response(params?: ApiHouseGetListProgressPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListProgressPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListProgressPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListProgressPost$Plain(params?: ApiHouseGetListProgressPost$Plain$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListProgressPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListProgressPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListProgressPost$Json$Response(params?: ApiHouseGetListProgressPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringListResponseBase>> {\r\n    return apiHouseGetListProgressPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListProgressPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListProgressPost$Json(params?: ApiHouseGetListProgressPost$Json$Params, context?: HttpContext): Observable<StringListResponseBase> {\r\n    return this.apiHouseGetListProgressPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringListResponseBase>): StringListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseListPost()` */\r\n  static readonly ApiHouseGetHouseListPostPath = '/api/House/GetHouseList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseListPost$Plain$Response(params?: ApiHouseGetHouseListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseListResListResponseBase>> {\r\n    return apiHouseGetHouseListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseListPost$Plain(params?: ApiHouseGetHouseListPost$Plain$Params, context?: HttpContext): Observable<GetHouseListResListResponseBase> {\r\n    return this.apiHouseGetHouseListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseListResListResponseBase>): GetHouseListResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseListPost$Json$Response(params?: ApiHouseGetHouseListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseListResListResponseBase>> {\r\n    return apiHouseGetHouseListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseListPost$Json(params?: ApiHouseGetHouseListPost$Json$Params, context?: HttpContext): Observable<GetHouseListResListResponseBase> {\r\n    return this.apiHouseGetHouseListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseListResListResponseBase>): GetHouseListResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseByIdPost()` */\r\n  static readonly ApiHouseGetHouseByIdPostPath = '/api/House/GetHouseByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseByIdPost$Plain$Response(params?: ApiHouseGetHouseByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHouseResponseBase>> {\r\n    return apiHouseGetHouseByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseByIdPost$Plain(params?: ApiHouseGetHouseByIdPost$Plain$Params, context?: HttpContext): Observable<TblHouseResponseBase> {\r\n    return this.apiHouseGetHouseByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblHouseResponseBase>): TblHouseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseByIdPost$Json$Response(params?: ApiHouseGetHouseByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblHouseResponseBase>> {\r\n    return apiHouseGetHouseByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseByIdPost$Json(params?: ApiHouseGetHouseByIdPost$Json$Params, context?: HttpContext): Observable<TblHouseResponseBase> {\r\n    return this.apiHouseGetHouseByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblHouseResponseBase>): TblHouseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseEditHousePost()` */\r\n  static readonly ApiHouseEditHousePostPath = '/api/House/EditHouse';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHousePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHousePost$Plain$Response(params?: ApiHouseEditHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHousePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHousePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHousePost$Plain(params?: ApiHouseEditHousePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHousePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHousePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHousePost$Json$Response(params?: ApiHouseEditHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHousePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHousePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHousePost$Json(params?: ApiHouseEditHousePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHousePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseEditListHousePost()` */\r\n  static readonly ApiHouseEditListHousePostPath = '/api/House/EditListHouse';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditListHousePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditListHousePost$Plain$Response(params?: ApiHouseEditListHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditListHousePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditListHousePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditListHousePost$Plain(params?: ApiHouseEditListHousePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditListHousePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditListHousePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditListHousePost$Json$Response(params?: ApiHouseEditListHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditListHousePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditListHousePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditListHousePost$Json(params?: ApiHouseEditListHousePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditListHousePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetListHouseRegularPicPost()` */\r\n  static readonly ApiHouseGetListHouseRegularPicPostPath = '/api/House/GetListHouseRegularPic';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseRegularPicPost$Plain$Response(params?: ApiHouseGetListHouseRegularPicPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListHouseRegularPicResListResponseBase>> {\r\n    return apiHouseGetListHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseRegularPicPost$Plain(params?: ApiHouseGetListHouseRegularPicPost$Plain$Params, context?: HttpContext): Observable<GetListHouseRegularPicResListResponseBase> {\r\n    return this.apiHouseGetListHouseRegularPicPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListHouseRegularPicResListResponseBase>): GetListHouseRegularPicResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseRegularPicPost$Json$Response(params?: ApiHouseGetListHouseRegularPicPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListHouseRegularPicResListResponseBase>> {\r\n    return apiHouseGetListHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListHouseRegularPicPost$Json(params?: ApiHouseGetListHouseRegularPicPost$Json$Params, context?: HttpContext): Observable<GetListHouseRegularPicResListResponseBase> {\r\n    return this.apiHouseGetListHouseRegularPicPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListHouseRegularPicResListResponseBase>): GetListHouseRegularPicResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseEditHouseRegularPicPost()` */\r\n  static readonly ApiHouseEditHouseRegularPicPostPath = '/api/House/EditHouseRegularPic';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHouseRegularPicPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseRegularPicPost$Plain$Response(params?: ApiHouseEditHouseRegularPicPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseRegularPicPost$Plain(params?: ApiHouseEditHouseRegularPicPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHouseRegularPicPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditHouseRegularPicPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseRegularPicPost$Json$Response(params?: ApiHouseEditHouseRegularPicPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditHouseRegularPicPost$Json(params?: ApiHouseEditHouseRegularPicPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditHouseRegularPicPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseDeleteRegularPicturePost()` */\r\n  static readonly ApiHouseDeleteRegularPicturePostPath = '/api/House/DeleteRegularPicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseDeleteRegularPicturePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteRegularPicturePost$Plain$Response(params?: ApiHouseDeleteRegularPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseDeleteRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteRegularPicturePost$Plain(params?: ApiHouseDeleteRegularPicturePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseDeleteRegularPicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseDeleteRegularPicturePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteRegularPicturePost$Json$Response(params?: ApiHouseDeleteRegularPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseDeleteRegularPicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteRegularPicturePost$Json(params?: ApiHouseDeleteRegularPicturePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseDeleteRegularPicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseUploadRegularPicPost()` */\r\n  static readonly ApiHouseUploadRegularPicPostPath = '/api/House/UploadRegularPic';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUploadRegularPicPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUploadRegularPicPost$Plain$Response(params?: ApiHouseUploadRegularPicPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUploadRegularPicPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUploadRegularPicPost$Plain(params?: ApiHouseUploadRegularPicPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUploadRegularPicPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseUploadRegularPicPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUploadRegularPicPost$Json$Response(params?: ApiHouseUploadRegularPicPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseUploadRegularPicPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseUploadRegularPicPost$Json(params?: ApiHouseUploadRegularPicPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseUploadRegularPicPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetListAppointmentsPost()` */\r\n  static readonly ApiHouseGetListAppointmentsPostPath = '/api/House/GetListAppointments';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListAppointmentsPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListAppointmentsPost$Plain$Response(params?: ApiHouseGetListAppointmentsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetAppoinmentResListResponseBase>> {\r\n    return apiHouseGetListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListAppointmentsPost$Plain(params?: ApiHouseGetListAppointmentsPost$Plain$Params, context?: HttpContext): Observable<GetAppoinmentResListResponseBase> {\r\n    return this.apiHouseGetListAppointmentsPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetAppoinmentResListResponseBase>): GetAppoinmentResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetListAppointmentsPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListAppointmentsPost$Json$Response(params?: ApiHouseGetListAppointmentsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetAppoinmentResListResponseBase>> {\r\n    return apiHouseGetListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetListAppointmentsPost$Json(params?: ApiHouseGetListAppointmentsPost$Json$Params, context?: HttpContext): Observable<GetAppoinmentResListResponseBase> {\r\n    return this.apiHouseGetListAppointmentsPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetAppoinmentResListResponseBase>): GetAppoinmentResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetAppointmentByIdPost()` */\r\n  static readonly ApiHouseGetAppointmentByIdPostPath = '/api/House/GetAppointmentByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetAppointmentByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetAppointmentByIdPost$Plain$Response(params?: ApiHouseGetAppointmentByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetAppoinmentResResponseBase>> {\r\n    return apiHouseGetAppointmentByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetAppointmentByIdPost$Plain(params?: ApiHouseGetAppointmentByIdPost$Plain$Params, context?: HttpContext): Observable<GetAppoinmentResResponseBase> {\r\n    return this.apiHouseGetAppointmentByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetAppoinmentResResponseBase>): GetAppoinmentResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetAppointmentByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetAppointmentByIdPost$Json$Response(params?: ApiHouseGetAppointmentByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetAppoinmentResResponseBase>> {\r\n    return apiHouseGetAppointmentByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetAppointmentByIdPost$Json(params?: ApiHouseGetAppointmentByIdPost$Json$Params, context?: HttpContext): Observable<GetAppoinmentResResponseBase> {\r\n    return this.apiHouseGetAppointmentByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetAppoinmentResResponseBase>): GetAppoinmentResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseEditAppointmentPost()` */\r\n  static readonly ApiHouseEditAppointmentPostPath = '/api/House/EditAppointment';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditAppointmentPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditAppointmentPost$Plain$Response(params?: ApiHouseEditAppointmentPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditAppointmentPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditAppointmentPost$Plain(params?: ApiHouseEditAppointmentPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditAppointmentPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseEditAppointmentPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditAppointmentPost$Json$Response(params?: ApiHouseEditAppointmentPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseEditAppointmentPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseEditAppointmentPost$Json(params?: ApiHouseEditAppointmentPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseEditAppointmentPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseDeleteAppointmentPost()` */\r\n  static readonly ApiHouseDeleteAppointmentPostPath = '/api/House/DeleteAppointment';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseDeleteAppointmentPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteAppointmentPost$Plain$Response(params?: ApiHouseDeleteAppointmentPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseDeleteAppointmentPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteAppointmentPost$Plain(params?: ApiHouseDeleteAppointmentPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseDeleteAppointmentPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseDeleteAppointmentPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteAppointmentPost$Json$Response(params?: ApiHouseDeleteAppointmentPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseDeleteAppointmentPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseDeleteAppointmentPost$Json(params?: ApiHouseDeleteAppointmentPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseDeleteAppointmentPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseCreateAppointmentPost()` */\r\n  static readonly ApiHouseCreateAppointmentPostPath = '/api/House/CreateAppointment';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCreateAppointmentPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCreateAppointmentPost$Plain$Response(params?: ApiHouseCreateAppointmentPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseCreateAppointmentPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCreateAppointmentPost$Plain(params?: ApiHouseCreateAppointmentPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseCreateAppointmentPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseCreateAppointmentPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCreateAppointmentPost$Json$Response(params?: ApiHouseCreateAppointmentPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseCreateAppointmentPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseCreateAppointmentPost$Json(params?: ApiHouseCreateAppointmentPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseCreateAppointmentPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHourListAppointmentPost()` */\r\n  static readonly ApiHouseGetHourListAppointmentPostPath = '/api/House/GetHourListAppointment';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHourListAppointmentPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListAppointmentPost$Plain$Response(params?: ApiHouseGetHourListAppointmentPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n    return apiHouseGetHourListAppointmentPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListAppointmentPost$Plain(params?: ApiHouseGetHourListAppointmentPost$Plain$Params, context?: HttpContext): Observable<GetHourListResponeListResponseBase> {\r\n    return this.apiHouseGetHourListAppointmentPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHourListResponeListResponseBase>): GetHourListResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHourListAppointmentPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListAppointmentPost$Json$Response(params?: ApiHouseGetHourListAppointmentPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n    return apiHouseGetHourListAppointmentPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHourListAppointmentPost$Json(params?: ApiHouseGetHourListAppointmentPost$Json$Params, context?: HttpContext): Observable<GetHourListResponeListResponseBase> {\r\n    return this.apiHouseGetHourListAppointmentPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHourListResponeListResponseBase>): GetHourListResponeListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseExportExcelListAppointmentsPost()` */\r\n  static readonly ApiHouseExportExcelListAppointmentsPostPath = '/api/House/ExportExcelListAppointments';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseExportExcelListAppointmentsPost$Plain$Response(params?: ApiHouseExportExcelListAppointmentsPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelResponseResponseBase>> {\r\n    return apiHouseExportExcelListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseExportExcelListAppointmentsPost$Plain(params?: ApiHouseExportExcelListAppointmentsPost$Plain$Params, context?: HttpContext): Observable<ExportExcelResponseResponseBase> {\r\n    return this.apiHouseExportExcelListAppointmentsPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelResponseResponseBase>): ExportExcelResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseExportExcelListAppointmentsPost$Json$Response(params?: ApiHouseExportExcelListAppointmentsPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelResponseResponseBase>> {\r\n    return apiHouseExportExcelListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseExportExcelListAppointmentsPost$Json(params?: ApiHouseExportExcelListAppointmentsPost$Json$Params, context?: HttpContext): Observable<ExportExcelResponseResponseBase> {\r\n    return this.apiHouseExportExcelListAppointmentsPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ExportExcelResponseResponseBase>): ExportExcelResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseGetHouseChangeDatePost()` */\r\n  static readonly ApiHouseGetHouseChangeDatePostPath = '/api/House/GetHouseChangeDate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseChangeDatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseChangeDatePost$Plain$Response(params?: ApiHouseGetHouseChangeDatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseChangeDateResListResponseBase>> {\r\n    return apiHouseGetHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseChangeDatePost$Plain(params?: ApiHouseGetHouseChangeDatePost$Plain$Params, context?: HttpContext): Observable<GetHouseChangeDateResListResponseBase> {\r\n    return this.apiHouseGetHouseChangeDatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseChangeDateResListResponseBase>): GetHouseChangeDateResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseGetHouseChangeDatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseChangeDatePost$Json$Response(params?: ApiHouseGetHouseChangeDatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseChangeDateResListResponseBase>> {\r\n    return apiHouseGetHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseGetHouseChangeDatePost$Json(params?: ApiHouseGetHouseChangeDatePost$Json$Params, context?: HttpContext): Observable<GetHouseChangeDateResListResponseBase> {\r\n    return this.apiHouseGetHouseChangeDatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetHouseChangeDateResListResponseBase>): GetHouseChangeDateResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseSaveHouseChangeDatePost()` */\r\n  static readonly ApiHouseSaveHouseChangeDatePostPath = '/api/House/SaveHouseChangeDate';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveHouseChangeDatePost$Plain$Response(params?: ApiHouseSaveHouseChangeDatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseSaveHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveHouseChangeDatePost$Plain(params?: ApiHouseSaveHouseChangeDatePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseSaveHouseChangeDatePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveHouseChangeDatePost$Json$Response(params?: ApiHouseSaveHouseChangeDatePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseSaveHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseSaveHouseChangeDatePost$Json(params?: ApiHouseSaveHouseChangeDatePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseSaveHouseChangeDatePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseExportHousePost()` */\r\n  static readonly ApiHouseExportHousePostPath = '/api/House/ExportHouse';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseExportHousePost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseExportHousePost$Plain$Response(params?: ApiHouseExportHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ByteArrayResponseBase>> {\r\n    return apiHouseExportHousePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseExportHousePost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseExportHousePost$Plain(params?: ApiHouseExportHousePost$Plain$Params, context?: HttpContext): Observable<ByteArrayResponseBase> {\r\n    return this.apiHouseExportHousePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ByteArrayResponseBase>): ByteArrayResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseExportHousePost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseExportHousePost$Json$Response(params?: ApiHouseExportHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ByteArrayResponseBase>> {\r\n    return apiHouseExportHousePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseExportHousePost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiHouseExportHousePost$Json(params?: ApiHouseExportHousePost$Json$Params, context?: HttpContext): Observable<ByteArrayResponseBase> {\r\n    return this.apiHouseExportHousePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ByteArrayResponseBase>): ByteArrayResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiHouseImportHousePost()` */\r\n  static readonly ApiHouseImportHousePostPath = '/api/House/ImportHouse';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseImportHousePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiHouseImportHousePost$Plain$Response(params?: ApiHouseImportHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseImportHousePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseImportHousePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiHouseImportHousePost$Plain(params?: ApiHouseImportHousePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseImportHousePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseImportHousePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiHouseImportHousePost$Json$Response(params?: ApiHouseImportHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseImportHousePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseImportHousePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiHouseImportHousePost$Json(params?: ApiHouseImportHousePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseImportHousePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,gCAAgC,QAAQ,kDAAkD;AAEnG,SAASC,iCAAiC,QAAQ,mDAAmD;AAErG,SAASC,qCAAqC,QAAQ,yDAAyD;AAE/G,SAASC,sCAAsC,QAAQ,0DAA0D;AAEjH,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,gCAAgC,QAAQ,mDAAmD;AAEpG,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,kCAAkC,QAAQ,oDAAoD;AAEvG,SAASC,mCAAmC,QAAQ,qDAAqD;AAEzG,SAASC,kCAAkC,QAAQ,oDAAoD;AAEvG,SAASC,mCAAmC,QAAQ,qDAAqD;AAEzG,SAASC,qCAAqC,QAAQ,wDAAwD;AAE9G,SAASC,sCAAsC,QAAQ,yDAAyD;AAEhH,SAASC,gCAAgC,QAAQ,kDAAkD;AAEnG,SAASC,iCAAiC,QAAQ,mDAAmD;AAErG,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,0BAA0B,QAAQ,4CAA4C;AAEvF,SAASC,2BAA2B,QAAQ,6CAA6C;AAEzF,SAASC,oCAAoC,QAAQ,wDAAwD;AAE7G,SAASC,qCAAqC,QAAQ,yDAAyD;AAE/G,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,4CAA4C,QAAQ,gEAAgE;AAE7H,SAASC,6CAA6C,QAAQ,iEAAiE;AAE/H,SAASC,4BAA4B,QAAQ,8CAA8C;AAE3F,SAASC,6BAA6B,QAAQ,+CAA+C;AAE7F,SAASC,mCAAmC,QAAQ,uDAAuD;AAE3G,SAASC,oCAAoC,QAAQ,wDAAwD;AAE7G,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,kCAAkC,QAAQ,sDAAsD;AAEzG,SAASC,mCAAmC,QAAQ,uDAAuD;AAE3G,SAASC,uCAAuC,QAAQ,2DAA2D;AAEnH,SAASC,wCAAwC,QAAQ,4DAA4D;AAErH,SAASC,4BAA4B,QAAQ,+CAA+C;AAE5F,SAASC,6BAA6B,QAAQ,gDAAgD;AAE9F,SAASC,6BAA6B,QAAQ,iDAAiD;AAE/F,SAASC,8BAA8B,QAAQ,kDAAkD;AAEjG,SAASC,mCAAmC,QAAQ,uDAAuD;AAE3G,SAASC,oCAAoC,QAAQ,wDAAwD;AAE7G,SAASC,6BAA6B,QAAQ,gDAAgD;AAE9F,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,6BAA6B,QAAQ,gDAAgD;AAE9F,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,uCAAuC,QAAQ,2DAA2D;AAEnH,SAASC,wCAAwC,QAAQ,4DAA4D;AAErH,SAASC,oCAAoC,QAAQ,uDAAuD;AAE5G,SAASC,qCAAqC,QAAQ,wDAAwD;AAE9G,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,gCAAgC,QAAQ,mDAAmD;AAEpG,SAASC,oCAAoC,QAAQ,uDAAuD;AAE5G,SAASC,qCAAqC,QAAQ,wDAAwD;AAE9G,SAASC,gCAAgC,QAAQ,mDAAmD;AAEpG,SAASC,iCAAiC,QAAQ,oDAAoD;AAEtG,SAASC,iCAAiC,QAAQ,qDAAqD;AAEvG,SAASC,kCAAkC,QAAQ,sDAAsD;AAEzG,SAASC,uCAAuC,QAAQ,4DAA4D;AAEpH,SAASC,wCAAwC,QAAQ,6DAA6D;AAEtH,SAASC,gCAAgC,QAAQ,mDAAmD;AAEpG,SAASC,iCAAiC,QAAQ,oDAAoD;AAEtG,SAASC,6BAA6B,QAAQ,+CAA+C;AAE7F,SAASC,8BAA8B,QAAQ,gDAAgD;AAE/F,SAASC,qCAAqC,QAAQ,yDAAyD;AAE/G,SAASC,sCAAsC,QAAQ,0DAA0D;AAEjH,SAASC,qCAAqC,QAAQ,yDAAyD;AAE/G,SAASC,sCAAsC,QAAQ,0DAA0D;AAEjH,SAASC,gCAAgC,QAAQ,oDAAoD;AAErG,SAASC,iCAAiC,QAAQ,qDAAqD;AAEvG,SAASC,4BAA4B,QAAQ,8CAA8C;AAE3F,SAASC,6BAA6B,QAAQ,+CAA+C;AAE7F,SAASC,sBAAsB,QAAQ,uCAAuC;AAE9E,SAASC,uBAAuB,QAAQ,wCAAwC;AAEhF,SAASC,oCAAoC,QAAQ,wDAAwD;AAE7G,SAASC,qCAAqC,QAAQ,yDAAyD;AAE/G,SAASC,8BAA8B,QAAQ,gDAAgD;AAE/F,SAASC,+BAA+B,QAAQ,iDAAiD;AAEjG,SAASC,uCAAuC,QAAQ,0DAA0D;AAElH,SAASC,wCAAwC,QAAQ,2DAA2D;AAEpH,SAASC,kCAAkC,QAAQ,qDAAqD;AAExG,SAASC,mCAAmC,QAAQ,sDAAsD;AAE1G,SAASC,iCAAiC,QAAQ,oDAAoD;AAEtG,SAASC,kCAAkC,QAAQ,qDAAqD;;;;AAyBxG,OAAM,MAAOC,YAAa,SAAQrF,WAAW;EAC3CsF,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,qBAAqB,GAAG,kBAAkB;EAAC;EAE3D;;;;;;EAMAC,gCAAgCA,CAACC,MAAuC,EAAEC,OAAqB;IAC7F,OAAOlB,uBAAuB,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1E;EAEA;;;;;;EAMAlB,uBAAuBA,CAACiB,MAAuC,EAAEC,OAAqB;IACpF,OAAO,IAAI,CAACF,gCAAgC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChE/F,GAAG,CAAEgG,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAC,+BAA+BA,CAACN,MAAsC,EAAEC,OAAqB;IAC3F,OAAOnB,sBAAsB,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzE;EAEA;;;;;;EAMAnB,sBAAsBA,CAACkB,MAAsC,EAAEC,OAAqB;IAClF,OAAO,IAAI,CAACK,+BAA+B,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/D/F,GAAG,CAAEgG,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAE,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAACR,MAA+C,EAAEC,OAAqB;IAC7G,OAAO5D,+BAA+B,CAAC,IAAI,CAACwD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMA5D,+BAA+BA,CAAC2D,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACO,wCAAwC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;;;;;EAMAI,uCAAuCA,CAACT,MAA8C,EAAEC,OAAqB;IAC3G,OAAO7D,8BAA8B,CAAC,IAAI,CAACyD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMA7D,8BAA8BA,CAAC4D,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACQ,uCAAuC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;IACgB,KAAAK,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,yCAAyCA,CAACX,MAAgD,EAAEC,OAAqB;IAC/G,OAAOtF,gCAAgC,CAAC,IAAI,CAACkF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAtF,gCAAgCA,CAACqF,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACU,yCAAyC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,wCAAwCA,CAACZ,MAA+C,EAAEC,OAAqB;IAC7G,OAAOvF,+BAA+B,CAAC,IAAI,CAACmF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAvF,+BAA+BA,CAACsF,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACW,wCAAwC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACd,MAA6C,EAAEC,OAAqB;IACzG,OAAOtD,6BAA6B,CAAC,IAAI,CAACkD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAtD,6BAA6BA,CAACqD,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACa,sCAAsC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;;;;;EAMAU,qCAAqCA,CAACf,MAA4C,EAAEC,OAAqB;IACvG,OAAOvD,4BAA4B,CAAC,IAAI,CAACmD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAvD,4BAA4BA,CAACsD,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACc,qCAAqC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrE/F,GAAG,CAAEgG,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;IACgB,KAAAW,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAACjB,MAAsD,EAAEC,OAAqB;IAC3H,OAAOxF,sCAAsC,CAAC,IAAI,CAACoF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAxF,sCAAsCA,CAACuF,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACgB,+CAA+C,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,8CAA8CA,CAAClB,MAAqD,EAAEC,OAAqB;IACzH,OAAOzF,qCAAqC,CAAC,IAAI,CAACqF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAzF,qCAAqCA,CAACwF,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACiB,8CAA8C,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAc,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAACpB,MAAwD,EAAEC,OAAqB;IAC/H,OAAO5C,wCAAwC,CAAC,IAAI,CAACwC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMA5C,wCAAwCA,CAAC2C,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACmB,iDAAiD,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF/F,GAAG,CAAEgG,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;;;;;EAMAgB,gDAAgDA,CAACrB,MAAuD,EAAEC,OAAqB;IAC7H,OAAO7C,uCAAuC,CAAC,IAAI,CAACyC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMA7C,uCAAuCA,CAAC4C,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACoB,gDAAgD,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF/F,GAAG,CAAEgG,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;IACgB,KAAAiB,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACvB,MAAiD,EAAEC,OAAqB;IACjH,OAAOtB,iCAAiC,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAtB,iCAAiCA,CAACqB,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACsB,0CAA0C,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAmB,yCAAyCA,CAACxB,MAAgD,EAAEC,OAAqB;IAC/G,OAAOvB,gCAAgC,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAvB,gCAAgCA,CAACsB,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACuB,yCAAyC,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAoB,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAAC1B,MAAiD,EAAEC,OAAqB;IACjH,OAAO1F,iCAAiC,CAAC,IAAI,CAACsF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMA1F,iCAAiCA,CAACyF,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACyB,0CAA0C,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAsB,yCAAyCA,CAAC3B,MAAgD,EAAEC,OAAqB;IAC/G,OAAO3F,gCAAgC,CAAC,IAAI,CAACuF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMA3F,gCAAgCA,CAAC0F,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAAC0B,yCAAyC,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAuB,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAAC7B,MAAsD,EAAEC,OAAqB;IAC3H,OAAO1B,sCAAsC,CAAC,IAAI,CAACsB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMA1B,sCAAsCA,CAACyB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAAC4B,+CAA+C,CAAC7B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAyB,8CAA8CA,CAAC9B,MAAqD,EAAEC,OAAqB;IACzH,OAAO3B,qCAAqC,CAAC,IAAI,CAACuB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMA3B,qCAAqCA,CAAC0B,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAAC6B,8CAA8C,CAAC9B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAA0B,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAAChC,MAAsD,EAAEC,OAAqB;IAC3H,OAAOxB,sCAAsC,CAAC,IAAI,CAACoB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAxB,sCAAsCA,CAACuB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAAC+B,+CAA+C,CAAChC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMA4B,8CAA8CA,CAACjC,MAAqD,EAAEC,OAAqB;IACzH,OAAOzB,qCAAqC,CAAC,IAAI,CAACqB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAzB,qCAAqCA,CAACwB,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACgC,8CAA8C,CAACjC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAA6B,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACnC,MAAmD,EAAEC,OAAqB;IACrH,OAAO1D,mCAAmC,CAAC,IAAI,CAACsD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMA1D,mCAAmCA,CAACyD,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACkC,4CAA4C,CAACnC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;;;;;EAMA+B,2CAA2CA,CAACpC,MAAkD,EAAEC,OAAqB;IACnH,OAAO3D,kCAAkC,CAAC,IAAI,CAACuD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMA3D,kCAAkCA,CAAC0D,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACmC,2CAA2C,CAACpC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAA2D,IAA2CA,CAAC,CAACC,IAAI,CAAC,CACnH;EACH;EAEA;;IACgB,KAAAgC,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,yCAAyCA,CAACtC,MAAgD,EAAEC,OAAqB;IAC/G,OAAOxC,gCAAgC,CAAC,IAAI,CAACoC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAxC,gCAAgCA,CAACuC,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACqC,yCAAyC,CAACtC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAkC,wCAAwCA,CAACvC,MAA+C,EAAEC,OAAqB;IAC7G,OAAOzC,+BAA+B,CAAC,IAAI,CAACqC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAzC,+BAA+BA,CAACwC,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACsC,wCAAwC,CAACvC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAmC,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACzC,MAAmD,EAAEC,OAAqB;IACrH,OAAOV,mCAAmC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAV,mCAAmCA,CAACS,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACwC,4CAA4C,CAACzC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAqC,2CAA2CA,CAAC1C,MAAkD,EAAEC,OAAqB;IACnH,OAAOX,kCAAkC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAX,kCAAkCA,CAACU,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACyC,2CAA2C,CAAC1C,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAsC,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAAC5C,MAAqD,EAAEC,OAAqB;IACzH,OAAO1C,qCAAqC,CAAC,IAAI,CAACsC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMA1C,qCAAqCA,CAACyC,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAAC2C,8CAA8C,CAAC5C,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;;;;;EAMAwC,6CAA6CA,CAAC7C,MAAoD,EAAEC,OAAqB;IACvH,OAAO3C,oCAAoC,CAAC,IAAI,CAACuC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMA3C,oCAAoCA,CAAC0C,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAAC4C,6CAA6C,CAAC7C,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAA0D,IAA0CA,CAAC,CAACC,IAAI,CAAC,CACjH;EACH;EAEA;;IACgB,KAAAyC,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAAC/C,MAAwD,EAAEC,OAAqB;IAC/H,OAAOZ,wCAAwC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAZ,wCAAwCA,CAACW,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAAC8C,iDAAiD,CAAC/C,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA2C,gDAAgDA,CAAChD,MAAuD,EAAEC,OAAqB;IAC7H,OAAOb,uCAAuC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAb,uCAAuCA,CAACY,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAAC+C,gDAAgD,CAAChD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA4C,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAAClD,MAA+C,EAAEC,OAAqB;IAC7G,OAAOpF,+BAA+B,CAAC,IAAI,CAACgF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMApF,+BAA+BA,CAACmF,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACiD,wCAAwC,CAAClD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAA0C,IAA0BA,CAAC,CAACC,IAAI,CAAC,CACjF;EACH;EAEA;;;;;;EAMA8C,uCAAuCA,CAACnD,MAA8C,EAAEC,OAAqB;IAC3G,OAAOrF,8BAA8B,CAAC,IAAI,CAACiF,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMArF,8BAA8BA,CAACoF,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACkD,uCAAuC,CAACnD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAA0C,IAA0BA,CAAC,CAACC,IAAI,CAAC,CACjF;EACH;EAEA;;IACgB,KAAA+C,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAACrD,MAA8C,EAAEC,OAAqB;IAC3G,OAAOhD,8BAA8B,CAAC,IAAI,CAAC4C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAhD,8BAA8BA,CAAC+C,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACoD,uCAAuC,CAACrD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAA2C,IAA2BA,CAAC,CAACC,IAAI,CAAC,CACnF;EACH;EAEA;;;;;;EAMAiD,sCAAsCA,CAACtD,MAA6C,EAAEC,OAAqB;IACzG,OAAOjD,6BAA6B,CAAC,IAAI,CAAC6C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAjD,6BAA6BA,CAACgD,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACqD,sCAAsC,CAACtD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAA2C,IAA2BA,CAAC,CAACC,IAAI,CAAC,CACnF;EACH;EAEA;;IACgB,KAAAkD,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAACxD,MAA+C,EAAEC,OAAqB;IAC7G,OAAO1E,+BAA+B,CAAC,IAAI,CAACsE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMA1E,+BAA+BA,CAACyE,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACuD,wCAAwC,CAACxD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAoD,uCAAuCA,CAACzD,MAA8C,EAAEC,OAAqB;IAC3G,OAAO3E,8BAA8B,CAAC,IAAI,CAACuE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMA3E,8BAA8BA,CAAC0E,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACwD,uCAAuC,CAACzD,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAqD,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAAC3D,MAA8C,EAAEC,OAAqB;IAC3G,OAAO5B,8BAA8B,CAAC,IAAI,CAACwB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMA5B,8BAA8BA,CAAC2B,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAAC0D,uCAAuC,CAAC3D,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;;;;;EAMAuD,sCAAsCA,CAAC5D,MAA6C,EAAEC,OAAqB;IACzG,OAAO7B,6BAA6B,CAAC,IAAI,CAACyB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMA7B,6BAA6BA,CAAC4B,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAAC2D,sCAAsC,CAAC5D,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAAkD,IAAkCA,CAAC,CAACC,IAAI,CAAC,CACjG;EACH;EAEA;;IACgB,KAAAwD,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAAC9D,MAA+C,EAAEC,OAAqB;IAC7G,OAAOd,+BAA+B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAd,+BAA+BA,CAACa,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAAC6D,wCAAwC,CAAC9D,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA0D,uCAAuCA,CAAC/D,MAA8C,EAAEC,OAAqB;IAC3G,OAAOf,8BAA8B,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAf,8BAA8BA,CAACc,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAAC8D,uCAAuC,CAAC/D,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA2D,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACjE,MAAiD,EAAEC,OAAqB;IACjH,OAAOpC,iCAAiC,CAAC,IAAI,CAACgC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMApC,iCAAiCA,CAACmC,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACgE,0CAA0C,CAACjE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;;;;;EAMA6D,yCAAyCA,CAAClE,MAAgD,EAAEC,OAAqB;IAC/G,OAAOrC,gCAAgC,CAAC,IAAI,CAACiC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMArC,gCAAgCA,CAACoC,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACiE,yCAAyC,CAAClE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;IACgB,KAAA8D,gCAAgC,GAAG,6BAA6B;EAAC;EAEjF;;;;;;EAMAC,2CAA2CA,CAACpE,MAAkD,EAAEC,OAAqB;IACnH,OAAOlC,kCAAkC,CAAC,IAAI,CAAC8B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAlC,kCAAkCA,CAACiC,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACmE,2CAA2C,CAACpE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;;;;;EAMAgE,0CAA0CA,CAACrE,MAAiD,EAAEC,OAAqB;IACjH,OAAOnC,iCAAiC,CAAC,IAAI,CAAC+B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAnC,iCAAiCA,CAACkC,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACoE,0CAA0C,CAACrE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;IACgB,KAAAiE,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACvE,MAAiD,EAAEC,OAAqB;IACjH,OAAO9B,iCAAiC,CAAC,IAAI,CAAC0B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMA9B,iCAAiCA,CAAC6B,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACsE,0CAA0C,CAACvE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;;;;;EAMAmE,yCAAyCA,CAACxE,MAAgD,EAAEC,OAAqB;IAC/G,OAAO/B,gCAAgC,CAAC,IAAI,CAAC2B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMA/B,gCAAgCA,CAAC8B,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACuE,yCAAyC,CAACxE,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAA6C,IAA6BA,CAAC,CAACC,IAAI,CAAC,CACvF;EACH;EAEA;;IACgB,KAAAoE,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAAC1E,MAA8C,EAAEC,OAAqB;IAC3G,OAAO9C,8BAA8B,CAAC,IAAI,CAAC0C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMA9C,8BAA8BA,CAAC6C,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACyE,uCAAuC,CAAC1E,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;;;;;EAMAsE,sCAAsCA,CAAC3E,MAA6C,EAAEC,OAAqB;IACzG,OAAO/C,6BAA6B,CAAC,IAAI,CAAC2C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMA/C,6BAA6BA,CAAC8C,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAAC0E,sCAAsC,CAAC3E,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;IACgB,KAAAuE,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,uCAAuCA,CAAC7E,MAA8C,EAAEC,OAAqB;IAC3G,OAAOpD,8BAA8B,CAAC,IAAI,CAACgD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMApD,8BAA8BA,CAACmD,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAAC4E,uCAAuC,CAAC7E,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAA2C,IAA2BA,CAAC,CAACC,IAAI,CAAC,CACnF;EACH;EAEA;;;;;;EAMAyE,sCAAsCA,CAAC9E,MAA6C,EAAEC,OAAqB;IACzG,OAAOrD,6BAA6B,CAAC,IAAI,CAACiD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMArD,6BAA6BA,CAACoD,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAAC6E,sCAAsC,CAAC9E,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAA2C,IAA2BA,CAAC,CAACC,IAAI,CAAC,CACnF;EACH;EAEA;;IACgB,KAAA0E,yBAAyB,GAAG,sBAAsB;EAAC;EAEnE;;;;;;EAMAC,oCAAoCA,CAAChF,MAA2C,EAAEC,OAAqB;IACrG,OAAOxE,2BAA2B,CAAC,IAAI,CAACoE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAxE,2BAA2BA,CAACuE,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAAC+E,oCAAoC,CAAChF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA4E,mCAAmCA,CAACjF,MAA0C,EAAEC,OAAqB;IACnG,OAAOzE,0BAA0B,CAAC,IAAI,CAACqE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7E;EAEA;;;;;;EAMAzE,0BAA0BA,CAACwE,MAA0C,EAAEC,OAAqB;IAC1F,OAAO,IAAI,CAACgF,mCAAmC,CAACjF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA6E,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAACnF,MAA+C,EAAEC,OAAqB;IAC7G,OAAOpE,+BAA+B,CAAC,IAAI,CAACgE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMApE,+BAA+BA,CAACmE,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACkF,wCAAwC,CAACnF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA+E,uCAAuCA,CAACpF,MAA8C,EAAEC,OAAqB;IAC3G,OAAOrE,8BAA8B,CAAC,IAAI,CAACiE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMArE,8BAA8BA,CAACoE,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACmF,uCAAuC,CAACpF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAgF,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAACtF,MAAwD,EAAEC,OAAqB;IAC/H,OAAOhC,wCAAwC,CAAC,IAAI,CAAC4B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAhC,wCAAwCA,CAAC+B,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACqF,iDAAiD,CAACtF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF/F,GAAG,CAAEgG,CAAgE,IAAgDA,CAAC,CAACC,IAAI,CAAC,CAC7H;EACH;EAEA;;;;;;EAMAkF,gDAAgDA,CAACvF,MAAuD,EAAEC,OAAqB;IAC7H,OAAOjC,uCAAuC,CAAC,IAAI,CAAC6B,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAjC,uCAAuCA,CAACgC,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACsF,gDAAgD,CAACvF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF/F,GAAG,CAAEgG,CAAgE,IAAgDA,CAAC,CAACC,IAAI,CAAC,CAC7H;EACH;EAEA;;IACgB,KAAAmF,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAACzF,MAAqD,EAAEC,OAAqB;IACzH,OAAOtE,qCAAqC,CAAC,IAAI,CAACkE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAtE,qCAAqCA,CAACqE,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACwF,8CAA8C,CAACzF,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAqF,6CAA6CA,CAAC1F,MAAoD,EAAEC,OAAqB;IACvH,OAAOvE,oCAAoC,CAAC,IAAI,CAACmE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAvE,oCAAoCA,CAACsE,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACyF,6CAA6C,CAAC1F,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAsF,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,+CAA+CA,CAAC5F,MAAsD,EAAEC,OAAqB;IAC3H,OAAO9E,sCAAsC,CAAC,IAAI,CAAC0E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMA9E,sCAAsCA,CAAC6E,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAAC2F,+CAA+C,CAAC5F,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAwF,8CAA8CA,CAAC7F,MAAqD,EAAEC,OAAqB;IACzH,OAAO/E,qCAAqC,CAAC,IAAI,CAAC2E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMA/E,qCAAqCA,CAAC8E,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAAC4F,8CAA8C,CAAC7F,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAyF,gCAAgC,GAAG,6BAA6B;EAAC;EAEjF;;;;;;EAMAC,2CAA2CA,CAAC/F,MAAkD,EAAEC,OAAqB;IACnH,OAAOR,kCAAkC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAR,kCAAkCA,CAACO,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAAC8F,2CAA2C,CAAC/F,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA2F,0CAA0CA,CAAChG,MAAiD,EAAEC,OAAqB;IACjH,OAAOT,iCAAiC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAT,iCAAiCA,CAACQ,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAAC+F,0CAA0C,CAAChG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA4F,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAAClG,MAAqD,EAAEC,OAAqB;IACzH,OAAOtC,qCAAqC,CAAC,IAAI,CAACkC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAtC,qCAAqCA,CAACqC,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACiG,8CAA8C,CAAClG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMA8F,6CAA6CA,CAACnG,MAAoD,EAAEC,OAAqB;IACvH,OAAOvC,oCAAoC,CAAC,IAAI,CAACmC,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAvC,oCAAoCA,CAACsC,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACkG,6CAA6C,CAACnG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAA+F,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACrG,MAAoD,EAAEC,OAAqB;IACvH,OAAO9D,oCAAoC,CAAC,IAAI,CAAC0D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMA9D,oCAAoCA,CAAC6D,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACoG,6CAA6C,CAACrG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAiG,4CAA4CA,CAACtG,MAAmD,EAAEC,OAAqB;IACrH,OAAO/D,mCAAmC,CAAC,IAAI,CAAC2D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMA/D,mCAAmCA,CAAC8D,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACqG,4CAA4C,CAACtG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAkG,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACxG,MAAiD,EAAEC,OAAqB;IACjH,OAAO5E,iCAAiC,CAAC,IAAI,CAACwE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMA5E,iCAAiCA,CAAC2E,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACuG,0CAA0C,CAACxG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAoG,yCAAyCA,CAACzG,MAAgD,EAAEC,OAAqB;IAC/G,OAAO7E,gCAAgC,CAAC,IAAI,CAACyE,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMA7E,gCAAgCA,CAAC4E,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACwG,yCAAyC,CAACzG,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAqG,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAAC3G,MAAmD,EAAEC,OAAqB;IACrH,OAAOhF,mCAAmC,CAAC,IAAI,CAAC4E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAhF,mCAAmCA,CAAC+E,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAAC0G,4CAA4C,CAAC3G,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAuG,2CAA2CA,CAAC5G,MAAkD,EAAEC,OAAqB;IACnH,OAAOjF,kCAAkC,CAAC,IAAI,CAAC6E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAjF,kCAAkCA,CAACgF,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAAC2G,2CAA2C,CAAC5G,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAwG,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAAC9G,MAAmD,EAAEC,OAAqB;IACrH,OAAOlF,mCAAmC,CAAC,IAAI,CAAC8E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAlF,mCAAmCA,CAACiF,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAAC6G,4CAA4C,CAAC9G,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA0G,2CAA2CA,CAAC/G,MAAkD,EAAEC,OAAqB;IACnH,OAAOnF,kCAAkC,CAAC,IAAI,CAAC+E,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAnF,kCAAkCA,CAACkF,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAAC8G,2CAA2C,CAAC/G,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAA2G,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAACjH,MAAwD,EAAEC,OAAqB;IAC/H,OAAOxD,wCAAwC,CAAC,IAAI,CAACoD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAxD,wCAAwCA,CAACuD,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACgH,iDAAiD,CAACjH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF/F,GAAG,CAAEgG,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;;;;;EAMA6G,gDAAgDA,CAAClH,MAAuD,EAAEC,OAAqB;IAC7H,OAAOzD,uCAAuC,CAAC,IAAI,CAACqD,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAzD,uCAAuCA,CAACwD,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACiH,gDAAgD,CAAClH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF/F,GAAG,CAAEgG,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;IACgB,KAAA8G,2CAA2C,GAAG,wCAAwC;EAAC;EAEvG;;;;;;EAMAC,sDAAsDA,CAACpH,MAA6D,EAAEC,OAAqB;IACzI,OAAOlE,6CAA6C,CAAC,IAAI,CAAC8D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAlE,6CAA6CA,CAACiE,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACmH,sDAAsD,CAACpH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtF/F,GAAG,CAAEgG,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;;;;;EAMAgH,qDAAqDA,CAACrH,MAA4D,EAAEC,OAAqB;IACvI,OAAOnE,4CAA4C,CAAC,IAAI,CAAC+D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAnE,4CAA4CA,CAACkE,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACoH,qDAAqD,CAACrH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrF/F,GAAG,CAAEgG,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;IACgB,KAAAiH,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACvH,MAAoD,EAAEC,OAAqB;IACvH,OAAOlD,oCAAoC,CAAC,IAAI,CAAC8C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAlD,oCAAoCA,CAACiD,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACsH,6CAA6C,CAACvH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;;;;;EAMAmH,4CAA4CA,CAACxH,MAAmD,EAAEC,OAAqB;IACrH,OAAOnD,mCAAmC,CAAC,IAAI,CAAC+C,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAnD,mCAAmCA,CAACkD,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACuH,4CAA4C,CAACxH,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E/F,GAAG,CAAEgG,CAA4D,IAA4CA,CAAC,CAACC,IAAI,CAAC,CACrH;EACH;EAEA;;IACgB,KAAAoH,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAAC1H,MAAqD,EAAEC,OAAqB;IACzH,OAAOhB,qCAAqC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAhB,qCAAqCA,CAACe,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACyH,8CAA8C,CAAC1H,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAsH,6CAA6CA,CAAC3H,MAAoD,EAAEC,OAAqB;IACvH,OAAOjB,oCAAoC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAjB,oCAAoCA,CAACgB,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAAC0H,6CAA6C,CAAC3H,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAuH,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAAC7H,MAA6C,EAAEC,OAAqB;IACzG,OAAOhE,6BAA6B,CAAC,IAAI,CAAC4D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAhE,6BAA6BA,CAAC+D,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAAC4H,sCAAsC,CAAC7H,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAA4C,IAA4BA,CAAC,CAACC,IAAI,CAAC,CACrF;EACH;EAEA;;;;;;EAMAyH,qCAAqCA,CAAC9H,MAA4C,EAAEC,OAAqB;IACvG,OAAOjE,4BAA4B,CAAC,IAAI,CAAC6D,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAjE,4BAA4BA,CAACgE,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAAC6H,qCAAqC,CAAC9H,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrE/F,GAAG,CAAEgG,CAA4C,IAA4BA,CAAC,CAACC,IAAI,CAAC,CACrF;EACH;EAEA;;IACgB,KAAA0H,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAAChI,MAA6C,EAAEC,OAAqB;IACzG,OAAOpB,6BAA6B,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMApB,6BAA6BA,CAACmB,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAAC+H,sCAAsC,CAAChI,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMA4H,qCAAqCA,CAACjI,MAA4C,EAAEC,OAAqB;IACvG,OAAOrB,4BAA4B,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMArB,4BAA4BA,CAACoB,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACgI,qCAAqC,CAACjI,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrE/F,GAAG,CAAEgG,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCAz7DWX,YAAY,EAAAwI,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAZ7I,YAAY;MAAA8I,OAAA,EAAZ9I,YAAY,CAAA+I,IAAA;MAAAC,UAAA,EADC;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}