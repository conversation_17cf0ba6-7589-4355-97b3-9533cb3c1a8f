{"ast": null, "code": "export var EnumHouseProgress;\n(function (EnumHouseProgress) {\n  EnumHouseProgress[EnumHouseProgress[\"\\u5C1A\\u672A\\u958B\\u59CB\"] = 0] = \"\\u5C1A\\u672A\\u958B\\u59CB\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u5DF2\\u95B1\\u8B80\\u64CD\\u4F5C\\u8AAA\\u660E\"] = 1] = \"\\u5DF2\\u95B1\\u8B80\\u64CD\\u4F5C\\u8AAA\\u660E\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u9078\\u6A23\\u5B8C\\u6210\"] = 2] = \"\\u9078\\u6A23\\u5B8C\\u6210\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u5B8C\\u6210\"] = 3] = \"\\u5B8C\\u6210\";\n})(EnumHouseProgress || (EnumHouseProgress = {}));", "map": {"version": 3, "names": ["EnumHouseProgress"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\enum\\enumHouseProgress.ts"], "sourcesContent": ["export enum EnumHouseProgress {\r\n  尚未開始 = 0,\r\n  已閱讀操作說明 = 1,\r\n  選樣完成 = 2,\r\n  完成 = 3\r\n}\r\n"], "mappings": "AAAA,WAAYA,iBAKX;AALD,WAAYA,iBAAiB;EAC3BA,iBAAA,CAAAA,iBAAA,8DAAQ;EACRA,iBAAA,CAAAA,iBAAA,kGAAW;EACXA,iBAAA,CAAAA,iBAAA,8DAAQ;EACRA,iBAAA,CAAAA,iBAAA,sCAAM;AACR,CAAC,EALWA,iBAAiB,KAAjBA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}