{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport class MessageService {\n  showErrorMSG(content, canDuplicates = false) {\n    this.toastrService.show(content, '錯誤', {\n      status: 'danger',\n      preventDuplicates: !canDuplicates\n    });\n  }\n  showSucessMSG(content, canDuplicates = false) {\n    this.toastrService.show(content, '成功', {\n      status: 'success',\n      preventDuplicates: !canDuplicates\n    });\n  }\n  showErrorMSGs(content, canDuplicates = false) {\n    this.toastrService.show(content.map(x => x).join('\\n'), '錯誤', {\n      status: 'danger',\n      preventDuplicates: !canDuplicates\n    });\n  }\n  constructor(toastrService) {\n    this.toastrService = toastrService;\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MessageService)(i0.ɵɵinject(i1.NbToastrService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["MessageService", "showErrorMSG", "content", "canDuplicates", "toastrService", "show", "status", "preventDuplicates", "showSucessMSG", "showErrorMSGs", "map", "x", "join", "constructor", "i0", "ɵɵinject", "i1", "NbToastrService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\services\\message.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { NbToastrService } from '@nebular/theme';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class MessageService {\r\n  showErrorMSG(content: string, canDuplicates: boolean = false) {\r\n    this.toastrService.show(\r\n      content,\r\n      '錯誤', { status: 'danger', preventDuplicates: !canDuplicates });\r\n  }\r\n  showSucessMSG(content: string, canDuplicates: boolean = false) {\r\n    this.toastrService.show(\r\n      content,\r\n      '成功', { status: 'success', preventDuplicates: !canDuplicates });\r\n  }\r\n  showErrorMSGs(content: string[], canDuplicates: boolean = false) {\r\n    this.toastrService.show(\r\n      content.map(x => x).join('\\n'),\r\n      '錯誤', { status: 'danger', preventDuplicates: !canDuplicates });\r\n  }\r\n  constructor(\r\n    private toastrService: NbToastrService,\r\n  ) {\r\n\r\n  }\r\n}\r\n"], "mappings": ";;AAMA,OAAM,MAAOA,cAAc;EACzBC,YAAYA,CAACC,OAAe,EAAEC,aAAA,GAAyB,KAAK;IAC1D,IAAI,CAACC,aAAa,CAACC,IAAI,CACrBH,OAAO,EACP,IAAI,EAAE;MAAEI,MAAM,EAAE,QAAQ;MAAEC,iBAAiB,EAAE,CAACJ;IAAa,CAAE,CAAC;EAClE;EACAK,aAAaA,CAACN,OAAe,EAAEC,aAAA,GAAyB,KAAK;IAC3D,IAAI,CAACC,aAAa,CAACC,IAAI,CACrBH,OAAO,EACP,IAAI,EAAE;MAAEI,MAAM,EAAE,SAAS;MAAEC,iBAAiB,EAAE,CAACJ;IAAa,CAAE,CAAC;EACnE;EACAM,aAAaA,CAACP,OAAiB,EAAEC,aAAA,GAAyB,KAAK;IAC7D,IAAI,CAACC,aAAa,CAACC,IAAI,CACrBH,OAAO,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAC9B,IAAI,EAAE;MAAEN,MAAM,EAAE,QAAQ;MAAEC,iBAAiB,EAAE,CAACJ;IAAa,CAAE,CAAC;EAClE;EACAU,YACUT,aAA8B;IAA9B,KAAAA,aAAa,GAAbA,aAAa;EAGvB;;;uCApBWJ,cAAc,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA;IAAA;EAAA;;;aAAdjB,cAAc;MAAAkB,OAAA,EAAdlB,cAAc,CAAAmB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}