{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, untracked, afterRender, afterNextRender, ElementRef, EnvironmentInjector, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, inject, Directive, NgZone, EventEmitter, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const body = this._document.body;\n    const viewport = this._viewportRuler.getViewportSize();\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    this._detach = () => {\n      this.disable();\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    this._document = document;\n  }\n  static {\n    this.ɵfac = function ScrollStrategyOptions_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollStrategyOptions,\n      factory: ScrollStrategyOptions.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.ScrollDispatcher\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    this.disposeOnNavigation = false;\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  constructor( /** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static {\n    this.ɵfac = function BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BaseOverlayDispatcher,\n      factory: BaseOverlayDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n      this._isAttached = false;\n    }\n  }\n  static {\n    this.ɵfac = function OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayKeyboardDispatcher,\n      factory: OverlayKeyboardDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform, /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    this._clickListener = event => {\n      const target = _getEventTarget(event);\n      // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n      // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n      this._pointerDownEventTarget = null;\n      // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n      const overlays = this._attachedOverlays.slice();\n      // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        }\n        // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n        if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n          break;\n        }\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      }\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n  static {\n    this.ɵfac = function OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayOutsideClickDispatcher,\n      factory: OverlayOutsideClickDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: i0.NgZone,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\n\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  static {\n    this.ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayContainer,\n      factory: OverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    this._outsidePointerEvents = new Subject();\n    this._renders = new Subject();\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() => afterRender(() => {\n      this._renders.next();\n    }, {\n      injector: this._injector\n    }));\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._disposeBackdrop(this._backdropElement);\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n    if (this._animationsDisabled) {\n      this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropElement, this._host);\n    // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n    this._backdropElement.addEventListener('click', this._backdropClickHandler);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n    if (!backdropToDetach) {\n      return;\n    }\n    if (this._animationsDisabled) {\n      this._disposeBackdrop(backdropToDetach);\n      return;\n    }\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    });\n    // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n    backdropToDetach.style.pointerEvents = 'none';\n    // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove();\n      // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._alignItems = '';\n    this._xPosition = '';\n    this._xOffset = '';\n    this._width = '';\n    this._height = '';\n    this._isDisposed = false;\n  }\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n  static {\n    this.ɵfac = function OverlayPositionBuilder_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OverlayPositionBuilder,\n      factory: OverlayPositionBuilder.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.ViewportRuler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: OverlayContainer\n  }], null);\n})();\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  constructor( /** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsModuleType = _animationsModuleType;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector));\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n  static {\n    this.ɵfac = function Overlay_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher), i0.ɵɵinject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Overlay,\n      factory: Overlay.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: ScrollStrategyOptions\n  }, {\n    type: OverlayContainer\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: OverlayPositionBuilder\n  }, {\n    type: OverlayKeyboardDispatcher\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i5.Directionality\n  }, {\n    type: i6.Location\n  }, {\n    type: OverlayOutsideClickDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  constructor( /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function CdkOverlayOrigin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkOverlayOrigin,\n      selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n      exportAs: [\"cdkOverlayOrigin\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    this._disposeOnNavigation = false;\n    this._ngZone = inject(NgZone);\n    /** Margin between the overlay and the viewport edges. */\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    this.disableClose = false;\n    /** Whether or not the overlay should attach a backdrop. */\n    this.hasBackdrop = false;\n    /** Whether or not the overlay should be locked when scrolling. */\n    this.lockPosition = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    this.flexibleDimensions = false;\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    this.growAfterOpen = false;\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    this.push = false;\n    /** Event emitted when the backdrop is clicked. */\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this._detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n  }\n  static {\n    this.ɵfac = function CdkConnectedOverlay_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkConnectedOverlay,\n      selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n      inputs: {\n        origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n        positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n        positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n        offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n        offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n        width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n        height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n        minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n        minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n        backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n        panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n        viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n        scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n        open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n        disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n        transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n        hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n        lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n        flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n        growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n        push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n        disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n      },\n      outputs: {\n        backdropClick: \"backdropClick\",\n        positionChange: \"positionChange\",\n        attach: \"attach\",\n        detach: \"detach\",\n        overlayKeydown: \"overlayKeydown\",\n        overlayOutsideClick: \"overlayOutsideClick\"\n      },\n      exportAs: [\"cdkConnectedOverlay\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay',\n      standalone: true\n    }]\n  }], () => [{\n    type: Overlay\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: i5.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static {\n    this.ɵfac = function OverlayModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverlayModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OverlayModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n      imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n  _createContainer() {\n    super._createContainer();\n    this._adjustParentForFullscreenChange();\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n      this._document.addEventListener(eventName, fn);\n      this._fullScreenListener = fn;\n    }\n  }\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n  static {\n    this.ɵfac = function FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FullscreenOverlayContainer,\n      factory: FullscreenOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };", "map": {"version": 3, "names": ["i1", "ScrollingModule", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "i6", "DOCUMENT", "i0", "Injectable", "Inject", "Optional", "untracked", "afterRender", "afterNextRender", "ElementRef", "EnvironmentInjector", "ApplicationRef", "ANIMATION_MODULE_TYPE", "InjectionToken", "inject", "Directive", "NgZone", "EventEmitter", "booleanAttribute", "Input", "Output", "NgModule", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "i1$1", "supportsScrollBehavior", "_getEventTarget", "_isTestEnvironment", "filter", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "i5", "BidiModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "Subject", "Subscription", "merge", "ESCAPE", "hasModifierKey", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_previousHTMLStyles", "top", "left", "_isEnabled", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "ScrollStrategyOptions_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "OverlayConfig", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "BaseOverlayDispatcher_Factory", "OverlayKeyboardDispatcher", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "keydownEvents", "next", "_isAttached", "runOutsideAngular", "addEventListener", "removeEventListener", "OverlayKeyboardDispatcher_Factory", "OverlayOutsideClickDispatcher", "_platform", "_cursorStyleIsSet", "_pointerDownListener", "_pointerDownEventTarget", "_clickListener", "target", "slice", "_outsidePointerEvents", "containsPierceShadowDom", "outsidePointerEvents", "_addEventListeners", "IOS", "_cursorOriginalV<PERSON>ue", "cursor", "OverlayOutsideClickDispatcher_Factory", "Platform", "parent", "child", "supportsShadowRoot", "ShadowRoot", "current", "host", "parentNode", "OverlayContainer", "_containerElement", "getContainerElement", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "OverlayContainer_Factory", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_injector", "_backdropElement", "_backdropClick", "_attachments", "_detachments", "_locationChanges", "EMPTY", "_backdropClickHandler", "_backdropTransitionendHandler", "_disposeBackdrop", "_renders", "_scrollStrategy", "_positionStrategy", "positionStrategy", "_afterRenderRef", "injector", "backdropElement", "hostElement", "portal", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "_afterNextRenderRef", "destroy", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "dispose", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenEmpty", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "backdropToDetach", "_backdropTimeout", "setTimeout", "cssClasses", "isAdd", "c", "subscription", "children", "backdrop", "clearTimeout", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "positions", "_preferredPositions", "connectedTo", "_overlayContainer", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_viewportMargin", "_scrollables", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_appliedPanelClasses", "position<PERSON><PERSON>es", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "scrollVisibility", "_getScrollVisibility", "_lastScrollVisibility", "compareScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "styles", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "for<PERSON>ach", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "a", "b", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "OverlayPositionBuilder_Factory", "nextUniqueId", "Overlay", "scrollStrategies", "_componentFactoryResolver", "_positionBuilder", "_directionality", "_animationsModuleType", "create", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "get", "id", "_appRef", "Overlay_Factory", "ComponentFactoryResolver", "Injector", "Directionality", "Location", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "CdkOverlayOrigin_Factory", "ɵɵdirectiveInject", "ɵdir", "ɵɵdefineDirective", "selectors", "exportAs", "standalone", "CdkConnectedOverlay", "_position", "_updatePositionStrategy", "_disposeOnNavigation", "_overlay", "templateRef", "viewContainerRef", "scrollStrategyFactory", "_dir", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "viewportMargin", "open", "disableClose", "lockPosition", "positionChange", "overlayKeydown", "overlayOutsideClick", "_templatePortal", "_scrollStrategyFactory", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_get<PERSON><PERSON>in<PERSON><PERSON>", "_createPositionStrategy", "currentPosition", "_get<PERSON><PERSON>in", "transformOriginSelector", "CdkConnectedOverlay_Factory", "TemplateRef", "ViewContainerRef", "inputs", "outputs", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "alias", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "imports", "exports", "FullscreenOverlayContainer", "platform", "_fullScreenEventName", "_fullScreenListener", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "fullscreenElement", "getFullscreenElement", "fn", "eventName", "_getEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "FullscreenOverlayContainer_Factory"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@angular/cdk/fesm2022/overlay.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, untracked, afterRender, afterNextRender, ElementRef, EnvironmentInjector, ApplicationRef, ANIMATION_MODULE_TYPE, InjectionToken, inject, Directive, NgZone, EventEmitter, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._previousHTMLStyles = { top: '', left: '' };\n        this._isEnabled = false;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const body = this._document.body;\n        const viewport = this._viewportRuler.getViewportSize();\n        return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n        this._scrollSubscription = null;\n        /** Detaches the overlay ref and disables the scroll strategy. */\n        this._detach = () => {\n            this.disable();\n            if (this._overlayRef.hasAttached()) {\n                this._ngZone.run(() => this._overlayRef.detach());\n            }\n        };\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n        this._scrollSubscription = null;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        /** Do nothing on scroll. */\n        this.noop = () => new NoopScrollStrategy();\n        /**\n         * Close the overlay as soon as the user scrolls.\n         * @param config Configuration to be used inside the scroll strategy.\n         */\n        this.close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n        /** Block scrolling. */\n        this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n        /**\n         * Update the overlay's position on scroll.\n         * @param config Configuration to be used inside the scroll strategy.\n         * Allows debouncing the reposition calls.\n         */\n        this.reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n        this._document = document;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ScrollStrategyOptions, deps: [{ token: i1.ScrollDispatcher }, { token: i1.ViewportRuler }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.ScrollDispatcher }, { type: i1.ViewportRuler }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    constructor(config) {\n        /** Strategy to be used when handling scroll events while the overlay is open. */\n        this.scrollStrategy = new NoopScrollStrategy();\n        /** Custom class to add to the overlay pane. */\n        this.panelClass = '';\n        /** Whether the overlay has a backdrop. */\n        this.hasBackdrop = false;\n        /** Custom class to add to the backdrop */\n        this.backdropClass = 'cdk-overlay-dark-backdrop';\n        /**\n         * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.disposeOnNavigation = false;\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    constructor(document) {\n        /** Currently attached overlays in the order they were attached. */\n        this._attachedOverlays = [];\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: BaseOverlayDispatcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    constructor(document, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._ngZone = _ngZone;\n        /** Keyboard event listener that will be attached to the body. */\n        this._keydownListener = (event) => {\n            const overlays = this._attachedOverlays;\n            for (let i = overlays.length - 1; i > -1; i--) {\n                // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n                // We want to target the most recent overlay, rather than trying to match where the event came\n                // from, because some components might open an overlay, but keep focus on a trigger element\n                // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n                // because we don't want overlays that don't handle keyboard events to block the ones below\n                // them that do.\n                if (overlays[i]._keydownEvents.observers.length > 0) {\n                    const keydownEvents = overlays[i]._keydownEvents;\n                    /** @breaking-change 14.0.0 _ngZone will be required. */\n                    if (this._ngZone) {\n                        this._ngZone.run(() => keydownEvents.next(event));\n                    }\n                    else {\n                        keydownEvents.next(event);\n                    }\n                    break;\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n            }\n            else {\n                this._document.body.addEventListener('keydown', this._keydownListener);\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._document.body.removeEventListener('keydown', this._keydownListener);\n            this._isAttached = false;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: [{ token: DOCUMENT }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }] });\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    constructor(document, _platform, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._cursorStyleIsSet = false;\n        /** Store pointerdown event target to track origin of click. */\n        this._pointerDownListener = (event) => {\n            this._pointerDownEventTarget = _getEventTarget(event);\n        };\n        /** Click event listener that will be attached to the body propagate phase. */\n        this._clickListener = (event) => {\n            const target = _getEventTarget(event);\n            // In case of a click event, we want to check the origin of the click\n            // (e.g. in case where a user starts a click inside the overlay and\n            // releases the click outside of it).\n            // This is done by using the event target of the preceding pointerdown event.\n            // Every click event caused by a pointer device has a preceding pointerdown\n            // event, unless the click was programmatically triggered (e.g. in a unit test).\n            const origin = event.type === 'click' && this._pointerDownEventTarget\n                ? this._pointerDownEventTarget\n                : target;\n            // Reset the stored pointerdown event target, to avoid having it interfere\n            // in subsequent events.\n            this._pointerDownEventTarget = null;\n            // We copy the array because the original may be modified asynchronously if the\n            // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n            // the for loop.\n            const overlays = this._attachedOverlays.slice();\n            // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n            // We want to target all overlays for which the click could be considered as outside click.\n            // As soon as we reach an overlay for which the click is not outside click we break off\n            // the loop.\n            for (let i = overlays.length - 1; i > -1; i--) {\n                const overlayRef = overlays[i];\n                if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                    continue;\n                }\n                // If it's a click inside the overlay, just break - we should do nothing\n                // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n                // and proceed with the next overlay\n                if (containsPierceShadowDom(overlayRef.overlayElement, target) ||\n                    containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n                    break;\n                }\n                const outsidePointerEvents = overlayRef._outsidePointerEvents;\n                /** @breaking-change 14.0.0 _ngZone will be required. */\n                if (this._ngZone) {\n                    this._ngZone.run(() => outsidePointerEvents.next(event));\n                }\n                else {\n                    outsidePointerEvents.next(event);\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n            }\n            else {\n                this._addEventListeners(body);\n            }\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            const body = this._document.body;\n            body.removeEventListener('pointerdown', this._pointerDownListener, true);\n            body.removeEventListener('click', this._clickListener, true);\n            body.removeEventListener('auxclick', this._clickListener, true);\n            body.removeEventListener('contextmenu', this._clickListener, true);\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    _addEventListeners(body) {\n        body.addEventListener('pointerdown', this._pointerDownListener, true);\n        body.addEventListener('click', this._clickListener, true);\n        body.addEventListener('auxclick', this._clickListener, true);\n        body.addEventListener('contextmenu', this._clickListener, true);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }] });\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n    const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n    let current = child;\n    while (current) {\n        if (current === parent) {\n            return true;\n        }\n        current =\n            supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n    }\n    return false;\n}\n\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    constructor(document, _platform) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayContainer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }] });\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._injector = _injector;\n        this._backdropElement = null;\n        this._backdropClick = new Subject();\n        this._attachments = new Subject();\n        this._detachments = new Subject();\n        this._locationChanges = Subscription.EMPTY;\n        this._backdropClickHandler = (event) => this._backdropClick.next(event);\n        this._backdropTransitionendHandler = (event) => {\n            this._disposeBackdrop(event.target);\n        };\n        /** Stream of keydown events dispatched to this overlay. */\n        this._keydownEvents = new Subject();\n        /** Stream of mouse outside events dispatched to this overlay. */\n        this._outsidePointerEvents = new Subject();\n        this._renders = new Subject();\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n        // Users could open the overlay from an `effect`, in which case we need to\n        // run the `afterRender` as `untracked`. We don't recommend that users do\n        // this, but we also don't want to break users who are doing it.\n        this._afterRenderRef = untracked(() => afterRender(() => {\n            this._renders.next();\n        }, { injector: this._injector }));\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropElement;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // We need to clean this up ourselves, because we're passing in an\n        // `EnvironmentInjector` below which won't ever be destroyed.\n        // Otherwise it causes some callbacks to be retained (see #29696).\n        this._afterNextRenderRef?.destroy();\n        // Update the position once the overlay is fully rendered before attempting to position it,\n        // as the position may depend on the size of the rendered content.\n        this._afterNextRenderRef = afterNextRender(() => {\n            // The overlay could've been detached before the callback executed.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        }, { injector: this._injector });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenEmpty();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._disposeBackdrop(this._backdropElement);\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._afterNextRenderRef?.destroy();\n        this._previousHostParent = this._pane = this._host = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n        this._afterRenderRef.destroy();\n        this._renders.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropElement = this._document.createElement('div');\n        this._backdropElement.classList.add('cdk-overlay-backdrop');\n        if (this._animationsDisabled) {\n            this._backdropElement.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropElement, this._host);\n        // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n        // action desired when such a click occurs (usually closing the overlay).\n        this._backdropElement.addEventListener('click', this._backdropClickHandler);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    if (this._backdropElement) {\n                        this._backdropElement.classList.add(showingClass);\n                    }\n                });\n            });\n        }\n        else {\n            this._backdropElement.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        const backdropToDetach = this._backdropElement;\n        if (!backdropToDetach) {\n            return;\n        }\n        if (this._animationsDisabled) {\n            this._disposeBackdrop(backdropToDetach);\n            return;\n        }\n        backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n        this._ngZone.runOutsideAngular(() => {\n            backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n        });\n        // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n        // In this case we make it unclickable and we try to remove it after a delay.\n        backdropToDetach.style.pointerEvents = 'none';\n        // Run this outside the Angular zone because there's nothing that Angular cares about.\n        // If it were to run inside the Angular zone, every test that used Overlay would have to be\n        // either async or fakeAsync.\n        this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n            this._disposeBackdrop(backdropToDetach);\n        }, 500));\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenEmpty() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._renders\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        if (scrollStrategy) {\n            scrollStrategy.disable();\n            if (scrollStrategy.detach) {\n                scrollStrategy.detach();\n            }\n        }\n    }\n    /** Removes a backdrop element from the DOM. */\n    _disposeBackdrop(backdrop) {\n        if (backdrop) {\n            backdrop.removeEventListener('click', this._backdropClickHandler);\n            backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n            backdrop.remove();\n            // It is possible that a new portal has been attached to this overlay since we started\n            // removing the backdrop. If that is the case, only clear the backdrop reference if it\n            // is still the same instance that we started to remove.\n            if (this._backdropElement === backdrop) {\n                this._backdropElement = null;\n            }\n        }\n        if (this._backdropTimeout) {\n            clearTimeout(this._backdropTimeout);\n            this._backdropTimeout = undefined;\n        }\n    }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n        this._lastBoundingBoxSize = { width: 0, height: 0 };\n        /** Whether the overlay was pushed in a previous positioning. */\n        this._isPushed = false;\n        /** Whether the overlay can be pushed on-screen on the initial open. */\n        this._canPush = true;\n        /** Whether the overlay can grow via flexible width/height after the initial open. */\n        this._growAfterOpen = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this._hasFlexibleDimensions = true;\n        /** Whether the overlay position is locked. */\n        this._positionLocked = false;\n        /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n        this._viewportMargin = 0;\n        /** The Scrollable containers used to check scrollable view properties on position change. */\n        this._scrollables = [];\n        /** Ordered list of preferred positions, from most to least desirable. */\n        this._preferredPositions = [];\n        /** Subject that emits whenever the position changes. */\n        this._positionChanges = new Subject();\n        /** Subscription to viewport size changes. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Default offset for the overlay along the x axis. */\n        this._offsetX = 0;\n        /** Default offset for the overlay along the y axis. */\n        this._offsetY = 0;\n        /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n        this._appliedPanelClasses = [];\n        /** Observable sequence of position changes. */\n        this.positionChanges = this._positionChanges;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollVisibility = this._getScrollVisibility();\n            // We're recalculating on scroll, but we only want to emit if anything\n            // changed since downstream code might be hitting the `NgZone`.\n            if (position !== this._lastPosition ||\n                !this._lastScrollVisibility ||\n                !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n                const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n                this._positionChanges.next(changeEvent);\n            }\n            this._lastScrollVisibility = scrollVisibility;\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin * 2;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the DOMRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n    if (a === b) {\n        return true;\n    }\n    return (a.isOriginClipped === b.isOriginClipped &&\n        a.isOriginOutsideView === b.isOriginOutsideView &&\n        a.isOverlayClipped === b.isOverlayClipped &&\n        a.isOverlayOutsideView === b.isOverlayOutsideView);\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    constructor() {\n        this._cssPosition = 'static';\n        this._topOffset = '';\n        this._bottomOffset = '';\n        this._alignItems = '';\n        this._xPosition = '';\n        this._xOffset = '';\n        this._width = '';\n        this._height = '';\n        this._isDisposed = false;\n    }\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n    }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayPositionBuilder, deps: [{ token: i1.ViewportRuler }, { token: DOCUMENT }, { token: i1$1.Platform }, { token: OverlayContainer }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: OverlayContainer }] });\n\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher, _animationsModuleType) {\n        this.scrollStrategies = scrollStrategies;\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._positionBuilder = _positionBuilder;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._injector = _injector;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._directionality = _directionality;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsModuleType = _animationsModuleType;\n    }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector));\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = `cdk-overlay-${nextUniqueId++}`;\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: Overlay, deps: [{ token: ScrollStrategyOptions }, { token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: OverlayPositionBuilder }, { token: OverlayKeyboardDispatcher }, { token: i0.Injector }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i5.Directionality }, { token: i6.Location }, { token: OverlayOutsideClickDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: Overlay, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: ScrollStrategyOptions }, { type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: OverlayPositionBuilder }, { type: OverlayKeyboardDispatcher }, { type: i0.Injector }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i5.Directionality }, { type: i6.Location }, { type: OverlayOutsideClickDispatcher }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }, {\n                    type: Optional\n                }] }] });\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkOverlayOrigin, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n    get disposeOnNavigation() {\n        return this._disposeOnNavigation;\n    }\n    set disposeOnNavigation(value) {\n        this._disposeOnNavigation = value;\n    }\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n        this._overlay = _overlay;\n        this._dir = _dir;\n        this._backdropSubscription = Subscription.EMPTY;\n        this._attachSubscription = Subscription.EMPTY;\n        this._detachSubscription = Subscription.EMPTY;\n        this._positionSubscription = Subscription.EMPTY;\n        this._disposeOnNavigation = false;\n        this._ngZone = inject(NgZone);\n        /** Margin between the overlay and the viewport edges. */\n        this.viewportMargin = 0;\n        /** Whether the overlay is open. */\n        this.open = false;\n        /** Whether the overlay can be closed by user interaction. */\n        this.disableClose = false;\n        /** Whether or not the overlay should attach a backdrop. */\n        this.hasBackdrop = false;\n        /** Whether or not the overlay should be locked when scrolling. */\n        this.lockPosition = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this.flexibleDimensions = false;\n        /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n        this.growAfterOpen = false;\n        /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n        this.push = false;\n        /** Event emitted when the backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Event emitted when the position has changed. */\n        this.positionChange = new EventEmitter();\n        /** Event emitted when the overlay has been attached. */\n        this.attach = new EventEmitter();\n        /** Event emitted when the overlay has been detached. */\n        this.detach = new EventEmitter();\n        /** Emits when there are keyboard events that are targeted at the overlay. */\n        this.overlayKeydown = new EventEmitter();\n        /** Emits when there are mouse outside click events that are targeted at the overlay. */\n        this.overlayOutsideClick = new EventEmitter();\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n        }\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this._attachOverlay() : this._detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this._detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            const origin = this._getOriginElement();\n            const target = _getEventTarget(event);\n            if (!origin || (origin !== target && !origin.contains(target))) {\n                this.overlayOutsideClick.next(event);\n            }\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir,\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n            disposeOnNavigation: this.disposeOnNavigation,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    _getOriginElement() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef.nativeElement;\n        }\n        if (this.origin instanceof ElementRef) {\n            return this.origin.nativeElement;\n        }\n        if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n            return this.origin;\n        }\n        return null;\n    }\n    /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n    _attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this._ngZone.run(() => this.positionChange.emit(position));\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n    }\n    /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n    _detachOverlay() {\n        if (this._overlayRef) {\n            this._overlayRef.detach();\n        }\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkConnectedOverlay, deps: [{ token: Overlay }, { token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.2.0-next.2\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute], push: [\"cdkConnectedOverlayPush\", \"push\", booleanAttribute], disposeOnNavigation: [\"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: Overlay }, { type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute }]\n            }], lockPosition: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute }]\n            }], flexibleDimensions: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute }]\n            }], growAfterOpen: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute }]\n            }], push: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayPush', transform: booleanAttribute }]\n            }], disposeOnNavigation: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute }]\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass OverlayModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    constructor(_document, platform) {\n        super(_document, platform);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._fullScreenEventName && this._fullScreenListener) {\n            this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n        }\n    }\n    _createContainer() {\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n    }\n    _adjustParentForFullscreenChange() {\n        if (!this._containerElement) {\n            return;\n        }\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n    }\n    _addFullscreenChangeListener(fn) {\n        const eventName = this._getEventName();\n        if (eventName) {\n            if (this._fullScreenListener) {\n                this._document.removeEventListener(eventName, this._fullScreenListener);\n            }\n            this._document.addEventListener(eventName, fn);\n            this._fullScreenListener = fn;\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FullscreenOverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, STANDARD_DROPDOWN_ADJACENT_POSITIONS, STANDARD_DROPDOWN_BELOW_POSITIONS, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,wBAAwB;AAC5C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,wBAAwB;AACvF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjR,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,uBAAuB;AACxE,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,SAASC,sBAAsB,EAAEC,eAAe,EAAEC,kBAAkB,QAAQ,uBAAuB;AACnG,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAE9D,MAAMC,uBAAuB,GAAGhB,sBAAsB,CAAC,CAAC;AACxD;AACA;AACA;AACA,MAAMiB,mBAAmB,CAAC;EACtBC,WAAWA,CAACC,cAAc,EAAEC,QAAQ,EAAE;IAClC,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACE,mBAAmB,GAAG;MAAEC,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IAChD,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAGL,QAAQ;EAC7B;EACA;EACAM,MAAMA,CAAA,EAAG,CAAE;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACJ,SAAS,CAACK,eAAe;MAC3C,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACZ,cAAc,CAACa,yBAAyB,CAAC,CAAC;MAC9E;MACA,IAAI,CAACX,mBAAmB,CAACE,IAAI,GAAGM,IAAI,CAACI,KAAK,CAACV,IAAI,IAAI,EAAE;MACrD,IAAI,CAACF,mBAAmB,CAACC,GAAG,GAAGO,IAAI,CAACI,KAAK,CAACX,GAAG,IAAI,EAAE;MACnD;MACA;MACAO,IAAI,CAACI,KAAK,CAACV,IAAI,GAAG1B,mBAAmB,CAAC,CAAC,IAAI,CAACkC,uBAAuB,CAACR,IAAI,CAAC;MACzEM,IAAI,CAACI,KAAK,CAACX,GAAG,GAAGzB,mBAAmB,CAAC,CAAC,IAAI,CAACkC,uBAAuB,CAACT,GAAG,CAAC;MACvEO,IAAI,CAACK,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACX,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAY,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACZ,UAAU,EAAE;MACjB,MAAMa,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;MAC3C,MAAMQ,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAK;MAC5B,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAAClB,UAAU,GAAG,KAAK;MACvBe,SAAS,CAAChB,IAAI,GAAG,IAAI,CAACF,mBAAmB,CAACE,IAAI;MAC9CgB,SAAS,CAACjB,GAAG,GAAG,IAAI,CAACD,mBAAmB,CAACC,GAAG;MAC5Ce,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI5B,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACf,uBAAuB,CAACR,IAAI,EAAE,IAAI,CAACQ,uBAAuB,CAACT,GAAG,CAAC;MAClF,IAAIN,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAf,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,MAAMS,IAAI,GAAG,IAAI,CAACZ,SAAS,CAACK,eAAe;IAC3C,IAAIO,IAAI,CAACH,SAAS,CAACa,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACvB,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMc,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;IAChC,MAAMU,QAAQ,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,eAAe,CAAC,CAAC;IACtD,OAAOX,IAAI,CAACY,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIb,IAAI,CAACc,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACnF;AACJ;;AAEA;AACA;AACA;AACA,SAASC,wCAAwCA,CAAA,EAAG;EAChD,OAAOC,KAAK,CAAC,4CAA4C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBtC,WAAWA,CAACuC,iBAAiB,EAAEC,OAAO,EAAEvC,cAAc,EAAEwC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACvC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACzB,OAAO,CAAC,CAAC;MACd,IAAI,IAAI,CAAC0B,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;QAChC,IAAI,CAACL,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;MACrD;IACJ,CAAC;EACL;EACA;EACAvC,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACiC,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACX,iBAAiB,CAACY,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAACnE,MAAM,CAACoE,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACT,WAAW,CAACU,cAAc,CAACzB,QAAQ,CAACwB,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACf,OAAO,IAAI,IAAI,CAACA,OAAO,CAACgB,SAAS,IAAI,IAAI,CAAChB,OAAO,CAACgB,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACzD,cAAc,CAACa,yBAAyB,CAAC,CAAC,CAACV,GAAG;MACjF,IAAI,CAACsC,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC,CAACV,GAAG;QAC1E,IAAIyD,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACF,sBAAsB,CAAC,GAAG,IAAI,CAACjB,OAAO,CAACgB,SAAS,EAAE;UACjF,IAAI,CAACd,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD,IAAI,CAACC,WAAW,CAACmB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACrB,mBAAmB,GAAGQ,MAAM,CAACS,SAAS,CAAC,IAAI,CAAChB,OAAO,CAAC;IAC7D;EACJ;EACA;EACAzB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMqB,kBAAkB,CAAC;EACrB;EACAxD,MAAMA,CAAA,EAAG,CAAE;EACX;EACAS,OAAOA,CAAA,EAAG,CAAE;EACZ;EACAV,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,4BAA4BA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAAClE,GAAG;IACzD,MAAMqE,YAAY,GAAGN,OAAO,CAAC/D,GAAG,GAAGkE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACjE,IAAI;IACxD,MAAMuE,YAAY,GAAGT,OAAO,CAAC9D,IAAI,GAAGiE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAAC/D,GAAG,GAAG0E,mBAAmB,CAAC1E,GAAG;IAC1D,MAAM4E,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAAC9D,IAAI,GAAGyE,mBAAmB,CAACzE,IAAI;IAC3D,MAAM6E,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BnF,WAAWA,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACnC;EACA;EACAlC,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,KAAK,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMb,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACQ,WAAW,GAAGI,UAAU;EACjC;EACA;EACAvC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACiC,mBAAmB,EAAE;MAC3B,MAAM0C,QAAQ,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAAC3C,mBAAmB,GAAG,IAAI,CAACH,iBAAiB,CAACY,QAAQ,CAACiC,QAAQ,CAAC,CAACzB,SAAS,CAAC,MAAM;QACjF,IAAI,CAACf,WAAW,CAACmB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,IAAI,CAACtB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC3C,WAAW,CAACU,cAAc,CAACkC,qBAAqB,CAAC,CAAC;UAC3E,MAAM;YAAErD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAChC,cAAc,CAAC8B,eAAe,CAAC,CAAC;UAC/D;UACA;UACA,MAAM0D,WAAW,GAAG,CAAC;YAAEtD,KAAK;YAAEF,MAAM;YAAEuC,MAAM,EAAEvC,MAAM;YAAE0C,KAAK,EAAExC,KAAK;YAAE/B,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAI6D,4BAA4B,CAACqB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAACvE,OAAO,CAAC,CAAC;YACd,IAAI,CAACsB,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA7B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACsB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACtB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAK,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,qBAAqB,CAAC;EACxB1F,WAAWA,CAACuC,iBAAiB,EAAEtC,cAAc,EAAEuC,OAAO,EAAEtC,QAAQ,EAAE;IAC9D,IAAI,CAACqC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACtC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACuC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACmD,IAAI,GAAG,MAAM,IAAI1B,kBAAkB,CAAC,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC2B,KAAK,GAAIC,MAAM,IAAK,IAAIvD,mBAAmB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACvC,cAAc,EAAE4F,MAAM,CAAC;IACnH;IACA,IAAI,CAACC,KAAK,GAAG,MAAM,IAAI/F,mBAAmB,CAAC,IAAI,CAACE,cAAc,EAAE,IAAI,CAACM,SAAS,CAAC;IAC/E;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwF,UAAU,GAAIF,MAAM,IAAK,IAAIV,wBAAwB,CAAC,IAAI,CAAC5C,iBAAiB,EAAE,IAAI,CAACtC,cAAc,EAAE,IAAI,CAACuC,OAAO,EAAEqD,MAAM,CAAC;IAC7H,IAAI,CAACtF,SAAS,GAAGL,QAAQ;EAC7B;EACA;IAAS,IAAI,CAAC8F,IAAI,YAAAC,8BAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA+FR,qBAAqB,EAA/BnI,EAAE,CAAA4I,QAAA,CAA+CnJ,EAAE,CAACG,gBAAgB,GAApEI,EAAE,CAAA4I,QAAA,CAA+EnJ,EAAE,CAACI,aAAa,GAAjGG,EAAE,CAAA4I,QAAA,CAA4G5I,EAAE,CAACc,MAAM,GAAvHd,EAAE,CAAA4I,QAAA,CAAkI7I,QAAQ;IAAA,CAA6C;EAAE;EAClS;IAAS,IAAI,CAAC8I,KAAK,kBADoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EACYZ,qBAAqB;MAAAa,OAAA,EAArBb,qBAAqB,CAAAM,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvK;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAH2G1F,EAAE,CAAAkJ,iBAAA,CAGXf,qBAAqB,EAAc,CAAC;IAC1HgB,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAE1J,EAAE,CAACG;EAAiB,CAAC,EAAE;IAAEuJ,IAAI,EAAE1J,EAAE,CAACI;EAAc,CAAC,EAAE;IAAEsJ,IAAI,EAAEnJ,EAAE,CAACc;EAAO,CAAC,EAAE;IAAEqI,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/HH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,MAAMwJ,aAAa,CAAC;EAChB9G,WAAWA,CAAC6F,MAAM,EAAE;IAChB;IACA,IAAI,CAACkB,cAAc,GAAG,IAAI9C,kBAAkB,CAAC,CAAC;IAC9C;IACA,IAAI,CAAC+C,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,aAAa,GAAG,2BAA2B;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAItB,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAMuB,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACzB,MAAM,CAAC;MACtC,KAAK,MAAM0B,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAIvB,MAAM,CAAC0B,GAAG,CAAC,KAAKX,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACW,GAAG,CAAC,GAAG1B,MAAM,CAAC0B,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA,MAAMC,sBAAsB,CAAC;EACzBxH,WAAWA,CAACyH,MAAM,EAAEC,OAAO,EAC3B;EACAC,OAAO,EACP;EACAC,OAAO,EACP;EACAZ,UAAU,EAAE;IACR,IAAI,CAACW,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACZ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACa,OAAO,GAAGJ,MAAM,CAACI,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGL,MAAM,CAACK,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACK,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;AAE1B;AACA,MAAMC,8BAA8B,CAAC;EACjClI,WAAWA,CAAA,CACX;EACAmI,cAAc,EACd;EACAC,wBAAwB,EAAE;IACtB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAMlG,KAAK,CAAC,8BAA8BiG,QAAQ,KAAKC,KAAK,KAAK,GAC7D,uCAAuC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAMlG,KAAK,CAAC,8BAA8BiG,QAAQ,KAAKC,KAAK,KAAK,GAC7D,sCAAsC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxBzI,WAAWA,CAACE,QAAQ,EAAE;IAClB;IACA,IAAI,CAACwI,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACnI,SAAS,GAAGL,QAAQ;EAC7B;EACAyI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC5F,MAAM,CAAC,CAAC;EACjB;EACA;EACA9B,GAAGA,CAAC+B,UAAU,EAAE;IACZ;IACA,IAAI,CAACtB,MAAM,CAACsB,UAAU,CAAC;IACvB,IAAI,CAAC0F,iBAAiB,CAACE,IAAI,CAAC5F,UAAU,CAAC;EAC3C;EACA;EACAtB,MAAMA,CAACsB,UAAU,EAAE;IACf,MAAM6F,KAAK,GAAG,IAAI,CAACH,iBAAiB,CAACI,OAAO,CAAC9F,UAAU,CAAC;IACxD,IAAI6F,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACH,iBAAiB,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACH,iBAAiB,CAACM,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACjG,MAAM,CAAC,CAAC;IACjB;EACJ;EACA;IAAS,IAAI,CAACiD,IAAI,YAAAiD,8BAAA/C,iBAAA;MAAA,YAAAA,iBAAA,IAA+FuC,qBAAqB,EAhK/BlL,EAAE,CAAA4I,QAAA,CAgK+C7I,QAAQ;IAAA,CAA6C;EAAE;EAC/M;IAAS,IAAI,CAAC8I,KAAK,kBAjKoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAiKYmC,qBAAqB;MAAAlC,OAAA,EAArBkC,qBAAqB,CAAAzC,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvK;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAnK2G1F,EAAE,CAAAkJ,iBAAA,CAmKXgC,qBAAqB,EAAc,CAAC;IAC1H/B,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAM4L,yBAAyB,SAAST,qBAAqB,CAAC;EAC1DzI,WAAWA,CAACE,QAAQ,EACpB;EACAsC,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAACsC,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAC2G,gBAAgB,GAAIC,KAAK,IAAK;MAC/B,MAAMC,QAAQ,GAAG,IAAI,CAACX,iBAAiB;MACvC,KAAK,IAAIY,CAAC,GAAGD,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;UACjD,MAAMS,aAAa,GAAGJ,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc;UAChD;UACA,IAAI,IAAI,CAAC/G,OAAO,EAAE;YACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAM2G,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC,CAAC;UACrD,CAAC,MACI;YACDK,aAAa,CAACC,IAAI,CAACN,KAAK,CAAC;UAC7B;UACA;QACJ;MACJ;IACJ,CAAC;EACL;EACA;EACAnI,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAAC2G,WAAW,EAAE;MACnB;MACA,IAAI,IAAI,CAACnH,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACoH,iBAAiB,CAAC,MAAM,IAAI,CAACrJ,SAAS,CAACa,IAAI,CAACyI,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC,CAAC;MAChH,CAAC,MACI;QACD,IAAI,CAAC5I,SAAS,CAACa,IAAI,CAACyI,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC;MAC1E;MACA,IAAI,CAACQ,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA5G,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC4G,WAAW,EAAE;MAClB,IAAI,CAACpJ,SAAS,CAACa,IAAI,CAAC0I,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACX,gBAAgB,CAAC;MACzE,IAAI,CAACQ,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;IAAS,IAAI,CAAC3D,IAAI,YAAA+D,kCAAA7D,iBAAA;MAAA,YAAAA,iBAAA,IAA+FgD,yBAAyB,EApOnC3L,EAAE,CAAA4I,QAAA,CAoOmD7I,QAAQ,GApO7DC,EAAE,CAAA4I,QAAA,CAoOwE5I,EAAE,CAACc,MAAM;IAAA,CAA6D;EAAE;EACzP;IAAS,IAAI,CAAC+H,KAAK,kBArOoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAqOY4C,yBAAyB;MAAA3C,OAAA,EAAzB2C,yBAAyB,CAAAlD,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3K;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAvO2G1F,EAAE,CAAAkJ,iBAAA,CAuOXyC,yBAAyB,EAAc,CAAC;IAC9HxC,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAEnJ,EAAE,CAACc,MAAM;IAAEwI,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA,MAAMsM,6BAA6B,SAASvB,qBAAqB,CAAC;EAC9DzI,WAAWA,CAACE,QAAQ,EAAE+J,SAAS,EAC/B;EACAzH,OAAO,EAAE;IACL,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAAC+J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACzH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0H,iBAAiB,GAAG,KAAK;IAC9B;IACA,IAAI,CAACC,oBAAoB,GAAIf,KAAK,IAAK;MACnC,IAAI,CAACgB,uBAAuB,GAAGrL,eAAe,CAACqK,KAAK,CAAC;IACzD,CAAC;IACD;IACA,IAAI,CAACiB,cAAc,GAAIjB,KAAK,IAAK;MAC7B,MAAMkB,MAAM,GAAGvL,eAAe,CAACqK,KAAK,CAAC;MACrC;MACA;MACA;MACA;MACA;MACA;MACA,MAAM3B,MAAM,GAAG2B,KAAK,CAAC1C,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC0D,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BE,MAAM;MACZ;MACA;MACA,IAAI,CAACF,uBAAuB,GAAG,IAAI;MACnC;MACA;MACA;MACA,MAAMf,QAAQ,GAAG,IAAI,CAACX,iBAAiB,CAAC6B,KAAK,CAAC,CAAC;MAC/C;MACA;MACA;MACA;MACA,KAAK,IAAIjB,CAAC,GAAGD,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAEM,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMtG,UAAU,GAAGqG,QAAQ,CAACC,CAAC,CAAC;QAC9B,IAAItG,UAAU,CAACwH,qBAAqB,CAAChB,SAAS,CAACR,MAAM,GAAG,CAAC,IAAI,CAAChG,UAAU,CAACH,WAAW,CAAC,CAAC,EAAE;UACpF;QACJ;QACA;QACA;QACA;QACA,IAAI4H,uBAAuB,CAACzH,UAAU,CAACM,cAAc,EAAEgH,MAAM,CAAC,IAC1DG,uBAAuB,CAACzH,UAAU,CAACM,cAAc,EAAEmE,MAAM,CAAC,EAAE;UAC5D;QACJ;QACA,MAAMiD,oBAAoB,GAAG1H,UAAU,CAACwH,qBAAqB;QAC7D;QACA,IAAI,IAAI,CAAChI,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACM,GAAG,CAAC,MAAM4H,oBAAoB,CAAChB,IAAI,CAACN,KAAK,CAAC,CAAC;QAC5D,CAAC,MACI;UACDsB,oBAAoB,CAAChB,IAAI,CAACN,KAAK,CAAC;QACpC;MACJ;IACJ,CAAC;EACL;EACA;EACAnI,GAAGA,CAAC+B,UAAU,EAAE;IACZ,KAAK,CAAC/B,GAAG,CAAC+B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC2G,WAAW,EAAE;MACnB,MAAMvI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChC;MACA,IAAI,IAAI,CAACoB,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACoH,iBAAiB,CAAC,MAAM,IAAI,CAACe,kBAAkB,CAACvJ,IAAI,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAACuJ,kBAAkB,CAACvJ,IAAI,CAAC;MACjC;MACA;MACA;MACA,IAAI,IAAI,CAAC6I,SAAS,CAACW,GAAG,IAAI,CAAC,IAAI,CAACV,iBAAiB,EAAE;QAC/C,IAAI,CAACW,oBAAoB,GAAGzJ,IAAI,CAACL,KAAK,CAAC+J,MAAM;QAC7C1J,IAAI,CAACL,KAAK,CAAC+J,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACZ,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAACP,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACA5G,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC4G,WAAW,EAAE;MAClB,MAAMvI,IAAI,GAAG,IAAI,CAACb,SAAS,CAACa,IAAI;MAChCA,IAAI,CAAC0I,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACK,oBAAoB,EAAE,IAAI,CAAC;MACxE/I,IAAI,CAAC0I,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAC5DjJ,IAAI,CAAC0I,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAC/DjJ,IAAI,CAAC0I,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACO,cAAc,EAAE,IAAI,CAAC;MAClE,IAAI,IAAI,CAACJ,SAAS,CAACW,GAAG,IAAI,IAAI,CAACV,iBAAiB,EAAE;QAC9C9I,IAAI,CAACL,KAAK,CAAC+J,MAAM,GAAG,IAAI,CAACD,oBAAoB;QAC7C,IAAI,CAACX,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAACP,WAAW,GAAG,KAAK;IAC5B;EACJ;EACAgB,kBAAkBA,CAACvJ,IAAI,EAAE;IACrBA,IAAI,CAACyI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACM,oBAAoB,EAAE,IAAI,CAAC;IACrE/I,IAAI,CAACyI,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;IACzDjJ,IAAI,CAACyI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;IAC5DjJ,IAAI,CAACyI,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACQ,cAAc,EAAE,IAAI,CAAC;EACnE;EACA;IAAS,IAAI,CAACrE,IAAI,YAAA+E,sCAAA7E,iBAAA;MAAA,YAAAA,iBAAA,IAA+F8D,6BAA6B,EAjWvCzM,EAAE,CAAA4I,QAAA,CAiWuD7I,QAAQ,GAjWjEC,EAAE,CAAA4I,QAAA,CAiW4EtH,IAAI,CAACmM,QAAQ,GAjW3FzN,EAAE,CAAA4I,QAAA,CAiWsG5I,EAAE,CAACc,MAAM;IAAA,CAA6D;EAAE;EACvR;IAAS,IAAI,CAAC+H,KAAK,kBAlWoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAkWY0D,6BAA6B;MAAAzD,OAAA,EAA7ByD,6BAA6B,CAAAhE,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/K;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KApW2G1F,EAAE,CAAAkJ,iBAAA,CAoWXuD,6BAA6B,EAAc,CAAC;IAClItD,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAE7H,IAAI,CAACmM;EAAS,CAAC,EAAE;IAAEtE,IAAI,EAAEnJ,EAAE,CAACc,MAAM;IAAEwI,UAAU,EAAE,CAAC;MAC3DH,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA,SAAS+M,uBAAuBA,CAACQ,MAAM,EAAEC,KAAK,EAAE;EAC5C,MAAMC,kBAAkB,GAAG,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU;EAC1E,IAAIC,OAAO,GAAGH,KAAK;EACnB,OAAOG,OAAO,EAAE;IACZ,IAAIA,OAAO,KAAKJ,MAAM,EAAE;MACpB,OAAO,IAAI;IACf;IACAI,OAAO,GACHF,kBAAkB,IAAIE,OAAO,YAAYD,UAAU,GAAGC,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,UAAU;EAC/F;EACA,OAAO,KAAK;AAChB;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnBxL,WAAWA,CAACE,QAAQ,EAAE+J,SAAS,EAAE;IAC7B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC1J,SAAS,GAAGL,QAAQ;EAC7B;EACAyI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8C,iBAAiB,EAAE/J,MAAM,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgK,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACD,iBAAiB,EAAE;MACzB,IAAI,CAACE,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACF,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAAA,EAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAAC3B,SAAS,CAAC4B,SAAS,IAAI7M,kBAAkB,CAAC,CAAC,EAAE;MAClD,MAAM8M,0BAA0B,GAAG,IAAI,CAACvL,SAAS,CAACwL,gBAAgB,CAAC,IAAIH,cAAc,uBAAuB,GAAG,IAAIA,cAAc,mBAAmB,CAAC;MACrJ;MACA;MACA,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,0BAA0B,CAAC9C,MAAM,EAAEM,CAAC,EAAE,EAAE;QACxDwC,0BAA0B,CAACxC,CAAC,CAAC,CAAC5H,MAAM,CAAC,CAAC;MAC1C;IACJ;IACA,MAAMsK,SAAS,GAAG,IAAI,CAACzL,SAAS,CAAC0L,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAAChL,SAAS,CAACC,GAAG,CAAC2K,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI5M,kBAAkB,CAAC,CAAC,EAAE;MACtBgN,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAACjC,SAAS,CAAC4B,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAAC3L,SAAS,CAACa,IAAI,CAAC+K,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACP,iBAAiB,GAAGO,SAAS;EACtC;EACA;IAAS,IAAI,CAAChG,IAAI,YAAAoG,yBAAAlG,iBAAA;MAAA,YAAAA,iBAAA,IAA+FsF,gBAAgB,EArb1BjO,EAAE,CAAA4I,QAAA,CAqb0C7I,QAAQ,GArbpDC,EAAE,CAAA4I,QAAA,CAqb+DtH,IAAI,CAACmM,QAAQ;IAAA,CAA6C;EAAE;EACpO;IAAS,IAAI,CAAC5E,KAAK,kBAtboF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAsbYkF,gBAAgB;MAAAjF,OAAA,EAAhBiF,gBAAgB,CAAAxF,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAxb2G1F,EAAE,CAAAkJ,iBAAA,CAwbX+E,gBAAgB,EAAc,CAAC;IACrH9E,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAE7H,IAAI,CAACmM;EAAS,CAAC,CAAC;AAAA;;AAE9C;AACA;AACA;AACA;AACA,MAAMqB,UAAU,CAAC;EACbrM,WAAWA,CAACsM,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAE/J,OAAO,EAAED,OAAO,EAAEiK,mBAAmB,EAAElM,SAAS,EAAEmM,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAEC,SAAS,EAAE;IACnK,IAAI,CAACP,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC/J,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACiK,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAClM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACmM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,cAAc,GAAG,IAAItN,OAAO,CAAC,CAAC;IACnC,IAAI,CAACuN,YAAY,GAAG,IAAIvN,OAAO,CAAC,CAAC;IACjC,IAAI,CAACwN,YAAY,GAAG,IAAIxN,OAAO,CAAC,CAAC;IACjC,IAAI,CAACyN,gBAAgB,GAAGxN,YAAY,CAACyN,KAAK;IAC1C,IAAI,CAACC,qBAAqB,GAAIhE,KAAK,IAAK,IAAI,CAAC2D,cAAc,CAACrD,IAAI,CAACN,KAAK,CAAC;IACvE,IAAI,CAACiE,6BAA6B,GAAIjE,KAAK,IAAK;MAC5C,IAAI,CAACkE,gBAAgB,CAAClE,KAAK,CAACkB,MAAM,CAAC;IACvC,CAAC;IACD;IACA,IAAI,CAACf,cAAc,GAAG,IAAI9J,OAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAAC+K,qBAAqB,GAAG,IAAI/K,OAAO,CAAC,CAAC;IAC1C,IAAI,CAAC8N,QAAQ,GAAG,IAAI9N,OAAO,CAAC,CAAC;IAC7B,IAAIgD,OAAO,CAACsE,cAAc,EAAE;MACxB,IAAI,CAACyG,eAAe,GAAG/K,OAAO,CAACsE,cAAc;MAC7C,IAAI,CAACyG,eAAe,CAAChN,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACiN,iBAAiB,GAAGhL,OAAO,CAACiL,gBAAgB;IACjD;IACA;IACA;IACA,IAAI,CAACC,eAAe,GAAGhQ,SAAS,CAAC,MAAMC,WAAW,CAAC,MAAM;MACrD,IAAI,CAAC2P,QAAQ,CAAC7D,IAAI,CAAC,CAAC;IACxB,CAAC,EAAE;MAAEkE,QAAQ,EAAE,IAAI,CAACf;IAAU,CAAC,CAAC,CAAC;EACrC;EACA;EACA,IAAIvJ,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACkJ,KAAK;EACrB;EACA;EACA,IAAIqB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACf,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIgB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI/L,MAAMA,CAACuN,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACxB,KAAK,CAACyB,aAAa,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAAC9B,WAAW,CAAC,IAAI,CAACI,KAAK,CAAC;IACpD;IACA,MAAM2B,YAAY,GAAG,IAAI,CAAC5B,aAAa,CAAC9L,MAAM,CAACuN,MAAM,CAAC;IACtD,IAAI,IAAI,CAACN,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACjN,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAAC2N,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACb,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC/M,MAAM,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI,CAAC6N,mBAAmB,EAAEC,OAAO,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACD,mBAAmB,GAAGzQ,eAAe,CAAC,MAAM;MAC7C;MACA,IAAI,IAAI,CAACgF,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACkB,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE;MAAE6J,QAAQ,EAAE,IAAI,CAACf;IAAU,CAAC,CAAC;IAChC;IACA,IAAI,CAAC2B,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAAC/L,OAAO,CAACwE,WAAW,EAAE;MAC1B,IAAI,CAACwH,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAAChM,OAAO,CAACuE,UAAU,EAAE;MACzB,IAAI,CAAC0H,cAAc,CAAC,IAAI,CAAClC,KAAK,EAAE,IAAI,CAAC/J,OAAO,CAACuE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAACgG,YAAY,CAACtD,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC+C,mBAAmB,CAACxL,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAACwB,OAAO,CAAC0E,mBAAmB,EAAE;MAClC,IAAI,CAAC+F,gBAAgB,GAAG,IAAI,CAACR,SAAS,CAAC/I,SAAS,CAAC,MAAM,IAAI,CAACgL,OAAO,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAAChC,uBAAuB,CAAC1L,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,OAAOiN,YAAY,EAAEU,SAAS,KAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAV,YAAY,CAACU,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAAC/L,WAAW,CAAC,CAAC,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACL,OAAO,CAACoH,iBAAiB,CAAC,MAAMiF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAChM,MAAM,CAAC,CAAC,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAOmL,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACInL,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAACmM,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACR,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAACf,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC1K,MAAM,EAAE;MACzD,IAAI,CAAC0K,iBAAiB,CAAC1K,MAAM,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACyK,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACtM,OAAO,CAAC,CAAC;IAClC;IACA,MAAM+N,gBAAgB,GAAG,IAAI,CAAC3C,aAAa,CAACvJ,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAACkK,YAAY,CAACvD,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC+C,mBAAmB,CAAC/K,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAACwN,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAChC,gBAAgB,CAAClJ,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC2I,uBAAuB,CAACjL,MAAM,CAAC,IAAI,CAAC;IACzC,OAAOuN,gBAAgB;EAC3B;EACA;EACAN,OAAOA,CAAA,EAAG;IACN,MAAMQ,UAAU,GAAG,IAAI,CAACtM,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC4K,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACkB,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACS,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC9B,gBAAgB,CAAC,IAAI,CAACR,gBAAgB,CAAC;IAC5C,IAAI,CAACI,gBAAgB,CAAClJ,WAAW,CAAC,CAAC;IACnC,IAAI,CAACyI,mBAAmB,CAAC/K,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAAC4K,aAAa,CAACqC,OAAO,CAAC,CAAC;IAC5B,IAAI,CAAC3B,YAAY,CAACqC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACtC,cAAc,CAACsC,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAC9F,cAAc,CAAC8F,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAAC7E,qBAAqB,CAAC6E,QAAQ,CAAC,CAAC;IACrC,IAAI,CAAC1C,uBAAuB,CAACjL,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI,CAAC6K,KAAK,EAAE7K,MAAM,CAAC,CAAC;IACpB,IAAI,CAAC4M,mBAAmB,EAAEC,OAAO,CAAC,CAAC;IACnC,IAAI,CAACN,mBAAmB,GAAG,IAAI,CAACzB,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI;IACzD,IAAI4C,UAAU,EAAE;MACZ,IAAI,CAAClC,YAAY,CAACvD,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAACuD,YAAY,CAACoC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC1B,eAAe,CAACY,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAChB,QAAQ,CAAC8B,QAAQ,CAAC,CAAC;EAC5B;EACA;EACAxM,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACyJ,aAAa,CAACzJ,WAAW,CAAC,CAAC;EAC3C;EACA;EACAyM,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvC,cAAc;EAC9B;EACA;EACAwC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvC,YAAY;EAC5B;EACA;EACAwC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACvC,YAAY;EAC5B;EACA;EACAxD,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACF,cAAc;EAC9B;EACA;EACAmB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,qBAAqB;EACrC;EACA;EACAiF,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChN,OAAO;EACvB;EACA;EACAsB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC0J,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACiC,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;EACAC,sBAAsBA,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAACnC,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACkB,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAAClB,iBAAiB,GAAGmC,QAAQ;IACjC,IAAI,IAAI,CAAC/M,WAAW,CAAC,CAAC,EAAE;MACpB+M,QAAQ,CAACpP,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAACuD,cAAc,CAAC,CAAC;IACzB;EACJ;EACA;EACA8L,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAACrN,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAGqN;IAAW,CAAC;IACjD,IAAI,CAAC1B,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA2B,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAACvN,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAEwN,SAAS,EAAED;IAAI,CAAC;IAClD,IAAI,CAAC3B,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA6B,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACZ,IAAI,CAACkC,cAAc,CAAC,IAAI,CAAClC,KAAK,EAAE2D,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAAC3D,KAAK,EAAE;MACZ,IAAI,CAACkC,cAAc,CAAC,IAAI,CAAClC,KAAK,EAAE2D,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,MAAMJ,SAAS,GAAG,IAAI,CAACxN,OAAO,CAACwN,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC1H,KAAK;EACtE;EACA;EACA+H,oBAAoBA,CAACV,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACpC,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAAC4B,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC5B,eAAe,GAAGoC,QAAQ;IAC/B,IAAI,IAAI,CAAC/M,WAAW,CAAC,CAAC,EAAE;MACpB+M,QAAQ,CAACpP,MAAM,CAAC,IAAI,CAAC;MACrBoP,QAAQ,CAACnP,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;EACA4N,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC9B,KAAK,CAACL,YAAY,CAAC,KAAK,EAAE,IAAI,CAACmE,YAAY,CAAC,CAAC,CAAC;EACvD;EACA;EACAjC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC5B,KAAK,EAAE;MACb;IACJ;IACA,MAAMzL,KAAK,GAAG,IAAI,CAACyL,KAAK,CAACzL,KAAK;IAC9BA,KAAK,CAACoB,KAAK,GAAGxD,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAACN,KAAK,CAAC;IACrDpB,KAAK,CAACkB,MAAM,GAAGtD,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAACR,MAAM,CAAC;IACvDlB,KAAK,CAACwP,QAAQ,GAAG5R,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAAC8N,QAAQ,CAAC;IAC3DxP,KAAK,CAACyP,SAAS,GAAG7R,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAAC+N,SAAS,CAAC;IAC7DzP,KAAK,CAAC0P,QAAQ,GAAG9R,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAACgO,QAAQ,CAAC;IAC3D1P,KAAK,CAAC2P,SAAS,GAAG/R,mBAAmB,CAAC,IAAI,CAAC8D,OAAO,CAACiO,SAAS,CAAC;EACjE;EACA;EACAlC,oBAAoBA,CAACmC,aAAa,EAAE;IAChC,IAAI,CAACnE,KAAK,CAACzL,KAAK,CAAC6P,aAAa,GAAGD,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACAlC,eAAeA,CAAA,EAAG;IACd,MAAMoC,YAAY,GAAG,8BAA8B;IACnD,IAAI,CAAC/D,gBAAgB,GAAG,IAAI,CAACvM,SAAS,CAAC0L,aAAa,CAAC,KAAK,CAAC;IAC3D,IAAI,CAACa,gBAAgB,CAAC9L,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAC3D,IAAI,IAAI,CAAC2L,mBAAmB,EAAE;MAC1B,IAAI,CAACE,gBAAgB,CAAC9L,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAC9E;IACA,IAAI,IAAI,CAACwB,OAAO,CAACyE,aAAa,EAAE;MAC5B,IAAI,CAACwH,cAAc,CAAC,IAAI,CAAC5B,gBAAgB,EAAE,IAAI,CAACrK,OAAO,CAACyE,aAAa,EAAE,IAAI,CAAC;IAChF;IACA;IACA;IACA,IAAI,CAACqF,KAAK,CAACyB,aAAa,CAAC8C,YAAY,CAAC,IAAI,CAAChE,gBAAgB,EAAE,IAAI,CAACP,KAAK,CAAC;IACxE;IACA;IACA,IAAI,CAACO,gBAAgB,CAACjD,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACuD,qBAAqB,CAAC;IAC3E;IACA,IAAI,CAAC,IAAI,CAACR,mBAAmB,IAAI,OAAOmE,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAACvO,OAAO,CAACoH,iBAAiB,CAAC,MAAM;QACjCmH,qBAAqB,CAAC,MAAM;UACxB,IAAI,IAAI,CAACjE,gBAAgB,EAAE;YACvB,IAAI,CAACA,gBAAgB,CAAC9L,SAAS,CAACC,GAAG,CAAC4P,YAAY,CAAC;UACrD;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC/D,gBAAgB,CAAC9L,SAAS,CAACC,GAAG,CAAC4P,YAAY,CAAC;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5B,KAAK,CAACyE,WAAW,EAAE;MACxB,IAAI,CAACzE,KAAK,CAAChB,UAAU,CAACY,WAAW,CAAC,IAAI,CAACI,KAAK,CAAC;IACjD;EACJ;EACA;EACAyC,cAAcA,CAAA,EAAG;IACb,MAAMiC,gBAAgB,GAAG,IAAI,CAACnE,gBAAgB;IAC9C,IAAI,CAACmE,gBAAgB,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACrE,mBAAmB,EAAE;MAC1B,IAAI,CAACU,gBAAgB,CAAC2D,gBAAgB,CAAC;MACvC;IACJ;IACAA,gBAAgB,CAACjQ,SAAS,CAACU,MAAM,CAAC,8BAA8B,CAAC;IACjE,IAAI,CAACc,OAAO,CAACoH,iBAAiB,CAAC,MAAM;MACjCqH,gBAAgB,CAACpH,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACwD,6BAA6B,CAAC;IAC1F,CAAC,CAAC;IACF;IACA;IACA4D,gBAAgB,CAAClQ,KAAK,CAAC6P,aAAa,GAAG,MAAM;IAC7C;IACA;IACA;IACA,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAAC1O,OAAO,CAACoH,iBAAiB,CAAC,MAAMuH,UAAU,CAAC,MAAM;MAC1E,IAAI,CAAC7D,gBAAgB,CAAC2D,gBAAgB,CAAC;IAC3C,CAAC,EAAE,GAAG,CAAC,CAAC;EACZ;EACA;EACAvC,cAAcA,CAACvK,OAAO,EAAEiN,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMlB,OAAO,GAAGvR,WAAW,CAACwS,UAAU,IAAI,EAAE,CAAC,CAACnS,MAAM,CAACqS,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAInB,OAAO,CAACnH,MAAM,EAAE;MAChBqI,KAAK,GAAGlN,OAAO,CAACnD,SAAS,CAACC,GAAG,CAAC,GAAGkP,OAAO,CAAC,GAAGhM,OAAO,CAACnD,SAAS,CAACU,MAAM,CAAC,GAAGyO,OAAO,CAAC;IACpF;EACJ;EACA;EACAjB,uBAAuBA,CAAA,EAAG;IACtB;IACA;IACA;IACA,IAAI,CAAC1M,OAAO,CAACoH,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAM2H,YAAY,GAAG,IAAI,CAAChE,QAAQ,CAC7BnK,IAAI,CAAClE,SAAS,CAACS,KAAK,CAAC,IAAI,CAACqN,YAAY,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAC5DtJ,SAAS,CAAC,MAAM;QACjB;QACA;QACA,IAAI,CAAC,IAAI,CAAC6I,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAACgF,QAAQ,CAACxI,MAAM,KAAK,CAAC,EAAE;UAChE,IAAI,IAAI,CAACwD,KAAK,IAAI,IAAI,CAAC/J,OAAO,CAACuE,UAAU,EAAE;YACvC,IAAI,CAAC0H,cAAc,CAAC,IAAI,CAAClC,KAAK,EAAE,IAAI,CAAC/J,OAAO,CAACuE,UAAU,EAAE,KAAK,CAAC;UACnE;UACA,IAAI,IAAI,CAACuF,KAAK,IAAI,IAAI,CAACA,KAAK,CAACyB,aAAa,EAAE;YACxC,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAAC1B,KAAK,CAACyB,aAAa;YACnD,IAAI,CAACzB,KAAK,CAAC7K,MAAM,CAAC,CAAC;UACvB;UACA6P,YAAY,CAACvN,WAAW,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAoL,sBAAsBA,CAAA,EAAG;IACrB,MAAMrI,cAAc,GAAG,IAAI,CAACyG,eAAe;IAC3C,IAAIzG,cAAc,EAAE;MAChBA,cAAc,CAAC7F,OAAO,CAAC,CAAC;MACxB,IAAI6F,cAAc,CAAChE,MAAM,EAAE;QACvBgE,cAAc,CAAChE,MAAM,CAAC,CAAC;MAC3B;IACJ;EACJ;EACA;EACAuK,gBAAgBA,CAACmE,QAAQ,EAAE;IACvB,IAAIA,QAAQ,EAAE;MACVA,QAAQ,CAAC3H,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACsD,qBAAqB,CAAC;MACjEqE,QAAQ,CAAC3H,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACuD,6BAA6B,CAAC;MACjFoE,QAAQ,CAAC/P,MAAM,CAAC,CAAC;MACjB;MACA;MACA;MACA,IAAI,IAAI,CAACoL,gBAAgB,KAAK2E,QAAQ,EAAE;QACpC,IAAI,CAAC3E,gBAAgB,GAAG,IAAI;MAChC;IACJ;IACA,IAAI,IAAI,CAACoE,gBAAgB,EAAE;MACvBQ,YAAY,CAAC,IAAI,CAACR,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGtK,SAAS;IACrC;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAM+K,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EACpC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,mBAAmB;EACnC;EACA/R,WAAWA,CAACgS,WAAW,EAAE/R,cAAc,EAAEM,SAAS,EAAE0J,SAAS,EAAEgI,iBAAiB,EAAE;IAC9E,IAAI,CAAChS,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgI,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,oBAAoB,GAAG;MAAE/P,KAAK,EAAE,CAAC;MAAEF,MAAM,EAAE;IAAE,CAAC;IACnD;IACA,IAAI,CAACkQ,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B;IACA,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB;IACA,IAAI,CAACV,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACW,gBAAgB,GAAG,IAAIjT,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACkT,mBAAmB,GAAGjT,YAAY,CAACyN,KAAK;IAC7C;IACA,IAAI,CAACyF,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACL,gBAAgB;IAC5C,IAAI,CAACM,SAAS,CAAChB,WAAW,CAAC;EAC/B;EACA;EACAxR,MAAMA,CAACwC,UAAU,EAAE;IACf,IAAI,IAAI,CAACJ,WAAW,IAChBI,UAAU,KAAK,IAAI,CAACJ,WAAW,KAC9B,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMZ,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAAC4Q,kBAAkB,CAAC,CAAC;IACzBjQ,UAAU,CAAC8K,WAAW,CAAC9M,SAAS,CAACC,GAAG,CAAC0Q,gBAAgB,CAAC;IACtD,IAAI,CAAC/O,WAAW,GAAGI,UAAU;IAC7B,IAAI,CAACkQ,YAAY,GAAGlQ,UAAU,CAAC8K,WAAW;IAC1C,IAAI,CAACtB,KAAK,GAAGxJ,UAAU,CAACM,cAAc;IACtC,IAAI,CAAC6P,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACV,mBAAmB,CAAC3O,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC2O,mBAAmB,GAAG,IAAI,CAAC1S,cAAc,CAACqT,MAAM,CAAC,CAAC,CAAC3P,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAACyP,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC1D,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACyD,WAAW,IAAI,CAAC,IAAI,CAAClJ,SAAS,CAAC4B,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACuH,gBAAgB,IAAI,IAAI,CAACb,eAAe,IAAI,IAAI,CAACc,aAAa,EAAE;MACtE,IAAI,CAACE,mBAAmB,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IACpD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACvH,KAAK,CAAChH,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACwO,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAACvG,mBAAmB,CAAC,CAAC,CAAClG,qBAAqB,CAAC,CAAC;IAC1F,MAAMyO,UAAU,GAAG,IAAI,CAACJ,WAAW;IACnC,MAAMtO,WAAW,GAAG,IAAI,CAACwO,YAAY;IACrC,MAAMG,YAAY,GAAG,IAAI,CAACP,aAAa;IACvC,MAAMQ,aAAa,GAAG,IAAI,CAACH,cAAc;IACzC;IACA,MAAMI,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACvC,mBAAmB,EAAE;MACtC;MACA,IAAIwC,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAEhP,WAAW,EAAE+O,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAElP,WAAW,EAAE2O,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAAC1C,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC2C,cAAc,CAACR,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACQ,6BAA6B,CAACJ,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAACxL,IAAI,CAAC;UACdoM,QAAQ,EAAEV,GAAG;UACb7M,MAAM,EAAE8M,WAAW;UACnBhP,WAAW;UACX0P,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACX,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,EAAE;QACvEd,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAES,QAAQ,EAAEV,GAAG;UAAE/O;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAI6O,YAAY,CAACpL,MAAM,EAAE;MACrB,IAAIoM,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAIlB,YAAY,EAAE;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAAC9S,KAAK,GAAGmT,GAAG,CAACL,eAAe,CAAChT,MAAM,IAAIqT,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACnD,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC2C,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAAC3N,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC2K,QAAQ,EAAE;MACf;MACA,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC2C,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACO,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;EAChE;EACAxR,MAAMA,CAAA,EAAG;IACL,IAAI,CAACyQ,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACH,aAAa,GAAG,IAAI;IACzB,IAAI,CAACoC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC9C,mBAAmB,CAAC3O,WAAW,CAAC,CAAC;EAC1C;EACA;EACA2K,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwE,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACD,YAAY,EAAE;MACnBwC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACnS,KAAK,EAAE;QAClCX,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRsE,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACVvC,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACTwT,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACpJ,KAAK,EAAE;MACZ,IAAI,CAACiH,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAC7Q,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACkL,WAAW,CAAC9M,SAAS,CAACU,MAAM,CAACiQ,gBAAgB,CAAC;IACnE;IACA,IAAI,CAAC5O,MAAM,CAAC,CAAC;IACb,IAAI,CAAC2P,gBAAgB,CAACrD,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACzM,WAAW,GAAG,IAAI,CAACsQ,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACII,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACJ,WAAW,IAAI,CAAC,IAAI,CAAClJ,SAAS,CAAC4B,SAAS,EAAE;MAC/C;IACJ;IACA,MAAMgK,YAAY,GAAG,IAAI,CAACxC,aAAa;IACvC,IAAIwC,YAAY,EAAE;MACd,IAAI,CAAChC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACxC,IAAI,CAACC,YAAY,GAAG,IAAI,CAACvH,KAAK,CAAChH,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAACmO,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACpD,IAAI,CAACI,cAAc,GAAG,IAAI,CAAC/B,iBAAiB,CAACvG,mBAAmB,CAAC,CAAC,CAAClG,qBAAqB,CAAC,CAAC;MAC1F,MAAM+O,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACX,WAAW,EAAE,IAAI,CAACG,cAAc,EAAE6B,YAAY,CAAC;MAC7F,IAAI,CAACf,cAAc,CAACe,YAAY,EAAEtB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC7E,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIoG,wBAAwBA,CAACC,WAAW,EAAE;IAClC,IAAI,CAACtD,YAAY,GAAGsD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAAClE,SAAS,EAAE;IACrB,IAAI,CAACC,mBAAmB,GAAGD,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAAChJ,OAAO,CAAC,IAAI,CAACuK,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIgD,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC1D,eAAe,GAAG0D,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAC,sBAAsBA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAAC9D,sBAAsB,GAAG8D,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAC,iBAAiBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACjE,cAAc,GAAGiE,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAC,QAAQA,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACpE,QAAQ,GAAGoE,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkBA,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAACnE,eAAe,GAAGmE,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1D,SAASA,CAACvL,MAAM,EAAE;IACd,IAAI,CAACkP,OAAO,GAAGlP,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACImP,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACjE,QAAQ,GAAGiE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAI,CAAChE,QAAQ,GAAGgE,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAACC,wBAAwB,GAAGD,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIxC,eAAeA,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAI4C,CAAC;IACL,IAAI5C,GAAG,CAACzM,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAqP,CAAC,GAAGjD,UAAU,CAAC5T,IAAI,GAAG4T,UAAU,CAAC9R,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAMgV,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGnD,UAAU,CAACtP,KAAK,GAAGsP,UAAU,CAAC5T,IAAI;MACjE,MAAMgX,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GAAGnD,UAAU,CAAC5T,IAAI,GAAG4T,UAAU,CAACtP,KAAK;MAC/DuS,CAAC,GAAG5C,GAAG,CAACzM,OAAO,IAAI,OAAO,GAAGsP,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAIlD,aAAa,CAAC9T,IAAI,GAAG,CAAC,EAAE;MACxB6W,CAAC,IAAI/C,aAAa,CAAC9T,IAAI;IAC3B;IACA,IAAIiX,CAAC;IACL,IAAIhD,GAAG,CAACxM,OAAO,IAAI,QAAQ,EAAE;MACzBwP,CAAC,GAAGrD,UAAU,CAAC7T,GAAG,GAAG6T,UAAU,CAAChS,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACDqV,CAAC,GAAGhD,GAAG,CAACxM,OAAO,IAAI,KAAK,GAAGmM,UAAU,CAAC7T,GAAG,GAAG6T,UAAU,CAACzP,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI2P,aAAa,CAAC/T,GAAG,GAAG,CAAC,EAAE;MACvBkX,CAAC,IAAInD,aAAa,CAAC/T,GAAG;IAC1B;IACA,OAAO;MAAE8W,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACI5C,gBAAgBA,CAACH,WAAW,EAAEhP,WAAW,EAAE+O,GAAG,EAAE;IAC5C;IACA;IACA,IAAIiD,aAAa;IACjB,IAAIjD,GAAG,CAACvM,QAAQ,IAAI,QAAQ,EAAE;MAC1BwP,aAAa,GAAG,CAAChS,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAImS,GAAG,CAACvM,QAAQ,KAAK,OAAO,EAAE;MAC/BwP,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC7R,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACDoV,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC7R,WAAW,CAACpD,KAAK;IAC1D;IACA,IAAIqV,aAAa;IACjB,IAAIlD,GAAG,CAACtM,QAAQ,IAAI,QAAQ,EAAE;MAC1BwP,aAAa,GAAG,CAACjS,WAAW,CAACtD,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACDuV,aAAa,GAAGlD,GAAG,CAACtM,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAACzC,WAAW,CAACtD,MAAM;IACnE;IACA;IACA,OAAO;MACHiV,CAAC,EAAE3C,WAAW,CAAC2C,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAE/C,WAAW,CAAC+C,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACA5C,cAAcA,CAAC6C,KAAK,EAAEC,cAAc,EAAE5V,QAAQ,EAAEkT,QAAQ,EAAE;IACtD;IACA;IACA,MAAMtN,OAAO,GAAGiQ,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAI9P,OAAO,GAAG,IAAI,CAACiQ,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIpN,OAAO,GAAG,IAAI,CAACgQ,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAIrN,OAAO,EAAE;MACTuP,CAAC,IAAIvP,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACT0P,CAAC,IAAI1P,OAAO;IAChB;IACA;IACA,IAAIiQ,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAGxP,OAAO,CAACvF,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAI4V,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAG5P,OAAO,CAACzF,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAIgW,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACxQ,OAAO,CAACvF,KAAK,EAAE0V,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAACxQ,OAAO,CAACzF,MAAM,EAAE8V,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAI7C,WAAW,GAAG8C,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACHhD,WAAW;MACXN,0BAA0B,EAAEnN,OAAO,CAACvF,KAAK,GAAGuF,OAAO,CAACzF,MAAM,KAAKkT,WAAW;MAC1EiD,wBAAwB,EAAED,aAAa,KAAKzQ,OAAO,CAACzF,MAAM;MAC1DoW,0BAA0B,EAAEJ,YAAY,IAAIvQ,OAAO,CAACvF;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4S,6BAA6BA,CAACO,GAAG,EAAEmC,KAAK,EAAE3V,QAAQ,EAAE;IAChD,IAAI,IAAI,CAACwQ,sBAAsB,EAAE;MAC7B,MAAMgG,eAAe,GAAGxW,QAAQ,CAAC0C,MAAM,GAAGiT,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAGzW,QAAQ,CAAC6C,KAAK,GAAG8S,KAAK,CAACP,CAAC;MAC/C,MAAM1G,SAAS,GAAGgI,aAAa,CAAC,IAAI,CAAC5V,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACe,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAGiI,aAAa,CAAC,IAAI,CAAC5V,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACc,QAAQ,CAAC;MACrE,MAAMkI,WAAW,GAAGnD,GAAG,CAAC8C,wBAAwB,IAAK5H,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI8H,eAAgB;MACvG,MAAMI,aAAa,GAAGpD,GAAG,CAAC+C,0BAA0B,IAAK9H,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAIgI,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,KAAK,EAAElB,cAAc,EAAE9T,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAAC6R,mBAAmB,IAAI,IAAI,CAAClD,eAAe,EAAE;MAClD,OAAO;QACH2E,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACzB,mBAAmB,CAACyB,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC7B,mBAAmB,CAAC6B;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAM5P,OAAO,GAAGiQ,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAM5V,QAAQ,GAAG,IAAI,CAAC6R,aAAa;IACnC;IACA;IACA,MAAMkF,aAAa,GAAGhV,IAAI,CAACiV,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAGxP,OAAO,CAACvF,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAM4W,cAAc,GAAGlV,IAAI,CAACiV,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAG5P,OAAO,CAACzF,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAM+W,WAAW,GAAGnV,IAAI,CAACiV,GAAG,CAAChX,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAGwY,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAGpV,IAAI,CAACiV,GAAG,CAAChX,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAGuY,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAIzR,OAAO,CAACvF,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjC+W,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC1E,eAAe,GAAG1Q,QAAQ,CAACzB,IAAI,GAAGuD,cAAc,CAACvD,IAAI,GAAGuY,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAIxP,OAAO,CAACzF,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnCkX,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAG1Q,QAAQ,CAAC1B,GAAG,GAAGwD,cAAc,CAACxD,GAAG,GAAGwY,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAAC7B,mBAAmB,GAAG;MAAEyB,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIrE,cAAcA,CAACE,QAAQ,EAAET,WAAW,EAAE;IAClC,IAAI,CAAC6E,mBAAmB,CAACpE,QAAQ,CAAC;IAClC,IAAI,CAACqE,wBAAwB,CAAC9E,WAAW,EAAES,QAAQ,CAAC;IACpD,IAAI,CAACsE,qBAAqB,CAAC/E,WAAW,EAAES,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAAChO,UAAU,EAAE;MACrB,IAAI,CAACuS,gBAAgB,CAACvE,QAAQ,CAAChO,UAAU,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC0L,gBAAgB,CAAClJ,SAAS,CAACR,MAAM,EAAE;MACxC,MAAMwQ,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACpD;MACA;MACA,IAAIzE,QAAQ,KAAK,IAAI,CAAC3B,aAAa,IAC/B,CAAC,IAAI,CAACqG,qBAAqB,IAC3B,CAACC,uBAAuB,CAAC,IAAI,CAACD,qBAAqB,EAAEF,gBAAgB,CAAC,EAAE;QACxE,MAAMI,WAAW,GAAG,IAAI1R,8BAA8B,CAAC8M,QAAQ,EAAEwE,gBAAgB,CAAC;QAClF,IAAI,CAAC9G,gBAAgB,CAAChJ,IAAI,CAACkQ,WAAW,CAAC;MAC3C;MACA,IAAI,CAACF,qBAAqB,GAAGF,gBAAgB;IACjD;IACA;IACA,IAAI,CAACnG,aAAa,GAAG2B,QAAQ;IAC7B,IAAI,CAAC5B,gBAAgB,GAAG,KAAK;EACjC;EACA;EACAgG,mBAAmBA,CAACpE,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACiC,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAM4C,QAAQ,GAAG,IAAI,CAAC3G,YAAY,CAACnH,gBAAgB,CAAC,IAAI,CAACkL,wBAAwB,CAAC;IAClF,IAAI6C,OAAO;IACX,IAAIC,OAAO,GAAG/E,QAAQ,CAAChN,QAAQ;IAC/B,IAAIgN,QAAQ,CAACjN,QAAQ,KAAK,QAAQ,EAAE;MAChC+R,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAAC1C,MAAM,CAAC,CAAC,EAAE;MACpB0C,OAAO,GAAG9E,QAAQ,CAACjN,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACD+R,OAAO,GAAG9E,QAAQ,CAACjN,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuQ,QAAQ,CAAC7Q,MAAM,EAAEM,CAAC,EAAE,EAAE;MACtCuQ,QAAQ,CAACvQ,CAAC,CAAC,CAACvI,KAAK,CAACiZ,eAAe,GAAG,GAAGF,OAAO,IAAIC,OAAO,EAAE;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI7E,yBAAyBA,CAACzN,MAAM,EAAEuN,QAAQ,EAAE;IACxC,MAAMlT,QAAQ,GAAG,IAAI,CAAC6R,aAAa;IACnC,MAAMsG,KAAK,GAAG,IAAI,CAAC7C,MAAM,CAAC,CAAC;IAC3B,IAAInV,MAAM,EAAE7B,GAAG,EAAEoE,MAAM;IACvB,IAAIwQ,QAAQ,CAAChN,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACA5H,GAAG,GAAGqH,MAAM,CAAC6P,CAAC;MACdrV,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAG7B,GAAG,GAAG,IAAI,CAACoS,eAAe;IACzD,CAAC,MACI,IAAIwC,QAAQ,CAAChN,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACAxD,MAAM,GAAG1C,QAAQ,CAACG,MAAM,GAAGwF,MAAM,CAAC6P,CAAC,GAAG,IAAI,CAAC9E,eAAe,GAAG,CAAC;MAC9DvQ,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGuC,MAAM,GAAG,IAAI,CAACgO,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAM0H,8BAA8B,GAAGrW,IAAI,CAACsW,GAAG,CAACrY,QAAQ,CAAC0C,MAAM,GAAGiD,MAAM,CAAC6P,CAAC,GAAGxV,QAAQ,CAAC1B,GAAG,EAAEqH,MAAM,CAAC6P,CAAC,CAAC;MACpG,MAAM8C,cAAc,GAAG,IAAI,CAAClI,oBAAoB,CAACjQ,MAAM;MACvDA,MAAM,GAAGiY,8BAA8B,GAAG,CAAC;MAC3C9Z,GAAG,GAAGqH,MAAM,CAAC6P,CAAC,GAAG4C,8BAA8B;MAC/C,IAAIjY,MAAM,GAAGmY,cAAc,IAAI,CAAC,IAAI,CAAChH,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QAC3EjS,GAAG,GAAGqH,MAAM,CAAC6P,CAAC,GAAG8C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAMC,4BAA4B,GAAIrF,QAAQ,CAACjN,QAAQ,KAAK,OAAO,IAAI,CAACkS,KAAK,IAAMjF,QAAQ,CAACjN,QAAQ,KAAK,KAAK,IAAIkS,KAAM;IACxH;IACA,MAAMK,2BAA2B,GAAItF,QAAQ,CAACjN,QAAQ,KAAK,KAAK,IAAI,CAACkS,KAAK,IAAMjF,QAAQ,CAACjN,QAAQ,KAAK,OAAO,IAAIkS,KAAM;IACvH,IAAI9X,KAAK,EAAE9B,IAAI,EAAEsE,KAAK;IACtB,IAAI2V,2BAA2B,EAAE;MAC7B3V,KAAK,GAAG7C,QAAQ,CAACK,KAAK,GAAGsF,MAAM,CAACyP,CAAC,GAAG,IAAI,CAAC1E,eAAe,GAAG,CAAC;MAC5DrQ,KAAK,GAAGsF,MAAM,CAACyP,CAAC,GAAG,IAAI,CAAC1E,eAAe;IAC3C,CAAC,MACI,IAAI6H,4BAA4B,EAAE;MACnCha,IAAI,GAAGoH,MAAM,CAACyP,CAAC;MACf/U,KAAK,GAAGL,QAAQ,CAAC6C,KAAK,GAAG8C,MAAM,CAACyP,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMgD,8BAA8B,GAAGrW,IAAI,CAACsW,GAAG,CAACrY,QAAQ,CAAC6C,KAAK,GAAG8C,MAAM,CAACyP,CAAC,GAAGpV,QAAQ,CAACzB,IAAI,EAAEoH,MAAM,CAACyP,CAAC,CAAC;MACpG,MAAMqD,aAAa,GAAG,IAAI,CAACrI,oBAAoB,CAAC/P,KAAK;MACrDA,KAAK,GAAG+X,8BAA8B,GAAG,CAAC;MAC1C7Z,IAAI,GAAGoH,MAAM,CAACyP,CAAC,GAAGgD,8BAA8B;MAChD,IAAI/X,KAAK,GAAGoY,aAAa,IAAI,CAAC,IAAI,CAACnH,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;QACzEhS,IAAI,GAAGoH,MAAM,CAACyP,CAAC,GAAGqD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAEna,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEmE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAExC,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqX,qBAAqBA,CAAC7R,MAAM,EAAEuN,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACzN,MAAM,EAAEuN,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;MAChD4C,eAAe,CAAChT,MAAM,GAAG4B,IAAI,CAACsW,GAAG,CAAClF,eAAe,CAAChT,MAAM,EAAE,IAAI,CAACiQ,oBAAoB,CAACjQ,MAAM,CAAC;MAC3FgT,eAAe,CAAC9S,KAAK,GAAG0B,IAAI,CAACsW,GAAG,CAAClF,eAAe,CAAC9S,KAAK,EAAE,IAAI,CAAC+P,oBAAoB,CAAC/P,KAAK,CAAC;IAC5F;IACA,MAAMqY,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC1BD,MAAM,CAACpa,GAAG,GAAGoa,MAAM,CAACna,IAAI,GAAG,GAAG;MAC9Bma,MAAM,CAAChW,MAAM,GAAGgW,MAAM,CAAC7V,KAAK,GAAG6V,MAAM,CAAC9J,SAAS,GAAG8J,MAAM,CAAC/J,QAAQ,GAAG,EAAE;MACtE+J,MAAM,CAACrY,KAAK,GAAGqY,MAAM,CAACvY,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAMyO,SAAS,GAAG,IAAI,CAAC9N,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACiB,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAAC7N,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACgB,QAAQ;MACtD+J,MAAM,CAACvY,MAAM,GAAGtD,mBAAmB,CAACsW,eAAe,CAAChT,MAAM,CAAC;MAC3DuY,MAAM,CAACpa,GAAG,GAAGzB,mBAAmB,CAACsW,eAAe,CAAC7U,GAAG,CAAC;MACrDoa,MAAM,CAAChW,MAAM,GAAG7F,mBAAmB,CAACsW,eAAe,CAACzQ,MAAM,CAAC;MAC3DgW,MAAM,CAACrY,KAAK,GAAGxD,mBAAmB,CAACsW,eAAe,CAAC9S,KAAK,CAAC;MACzDqY,MAAM,CAACna,IAAI,GAAG1B,mBAAmB,CAACsW,eAAe,CAAC5U,IAAI,CAAC;MACvDma,MAAM,CAAC7V,KAAK,GAAGhG,mBAAmB,CAACsW,eAAe,CAACtQ,KAAK,CAAC;MACzD;MACA,IAAIqQ,QAAQ,CAACjN,QAAQ,KAAK,QAAQ,EAAE;QAChCyS,MAAM,CAAC7E,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACD6E,MAAM,CAAC7E,UAAU,GAAGX,QAAQ,CAACjN,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAIiN,QAAQ,CAAChN,QAAQ,KAAK,QAAQ,EAAE;QAChCwS,MAAM,CAAC5E,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACD4E,MAAM,CAAC5E,cAAc,GAAGZ,QAAQ,CAAChN,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAI0I,SAAS,EAAE;QACX8J,MAAM,CAAC9J,SAAS,GAAG/R,mBAAmB,CAAC+R,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACV+J,MAAM,CAAC/J,QAAQ,GAAG9R,mBAAmB,CAAC8R,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAACyB,oBAAoB,GAAG+C,eAAe;IAC3CS,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACnS,KAAK,EAAEyZ,MAAM,CAAC;EACjD;EACA;EACA9G,uBAAuBA,CAAA,EAAG;IACtBgC,YAAY,CAAC,IAAI,CAACxC,YAAY,CAACnS,KAAK,EAAE;MAClCX,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTsE,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACXvC,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACTwT,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACAnC,0BAA0BA,CAAA,EAAG;IACzBiC,YAAY,CAAC,IAAI,CAAClJ,KAAK,CAACzL,KAAK,EAAE;MAC3BX,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRmE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACTqQ,QAAQ,EAAE,EAAE;MACZ0F,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACArB,wBAAwBA,CAAC9E,WAAW,EAAES,QAAQ,EAAE;IAC5C,MAAMwF,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMG,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAACtI,sBAAsB;IACzD,MAAMzM,MAAM,GAAG,IAAI,CAACjD,WAAW,CAAC6M,SAAS,CAAC,CAAC;IAC3C,IAAIkL,gBAAgB,EAAE;MAClB,MAAM/W,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC;MACtE4U,YAAY,CAAC8E,MAAM,EAAE,IAAI,CAACK,iBAAiB,CAAC7F,QAAQ,EAAET,WAAW,EAAE3Q,cAAc,CAAC,CAAC;MACnF8R,YAAY,CAAC8E,MAAM,EAAE,IAAI,CAACM,iBAAiB,CAAC9F,QAAQ,EAAET,WAAW,EAAE3Q,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACD4W,MAAM,CAACxF,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI+F,eAAe,GAAG,EAAE;IACxB,IAAIpT,OAAO,GAAG,IAAI,CAACiQ,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIpN,OAAO,GAAG,IAAI,CAACgQ,UAAU,CAAC5C,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIrN,OAAO,EAAE;MACToT,eAAe,IAAI,cAAcpT,OAAO,MAAM;IAClD;IACA,IAAIC,OAAO,EAAE;MACTmT,eAAe,IAAI,cAAcnT,OAAO,KAAK;IACjD;IACA4S,MAAM,CAACE,SAAS,GAAGK,eAAe,CAACC,IAAI,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAInV,MAAM,CAAC6K,SAAS,EAAE;MAClB,IAAIiK,gBAAgB,EAAE;QAClBH,MAAM,CAAC9J,SAAS,GAAG/R,mBAAmB,CAACkH,MAAM,CAAC6K,SAAS,CAAC;MAC5D,CAAC,MACI,IAAIkK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC9J,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAI7K,MAAM,CAAC4K,QAAQ,EAAE;MACjB,IAAIkK,gBAAgB,EAAE;QAClBH,MAAM,CAAC/J,QAAQ,GAAG9R,mBAAmB,CAACkH,MAAM,CAAC4K,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAImK,qBAAqB,EAAE;QAC5BJ,MAAM,CAAC/J,QAAQ,GAAG,EAAE;MACxB;IACJ;IACAiF,YAAY,CAAC,IAAI,CAAClJ,KAAK,CAACzL,KAAK,EAAEyZ,MAAM,CAAC;EAC1C;EACA;EACAK,iBAAiBA,CAAC7F,QAAQ,EAAET,WAAW,EAAE3Q,cAAc,EAAE;IACrD;IACA;IACA,IAAI4W,MAAM,GAAG;MAAEpa,GAAG,EAAE,EAAE;MAAEoE,MAAM,EAAE;IAAG,CAAC;IACpC,IAAIiQ,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAEnQ,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAIoR,QAAQ,CAAChN,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAMiT,cAAc,GAAG,IAAI,CAAC1a,SAAS,CAACK,eAAe,CAACsa,YAAY;MAClEV,MAAM,CAAChW,MAAM,GAAG,GAAGyW,cAAc,IAAIxG,YAAY,CAAC6C,CAAC,GAAG,IAAI,CAACvD,YAAY,CAAC9R,MAAM,CAAC,IAAI;IACvF,CAAC,MACI;MACDuY,MAAM,CAACpa,GAAG,GAAGzB,mBAAmB,CAAC8V,YAAY,CAAC6C,CAAC,CAAC;IACpD;IACA,OAAOkD,MAAM;EACjB;EACA;EACAM,iBAAiBA,CAAC9F,QAAQ,EAAET,WAAW,EAAE3Q,cAAc,EAAE;IACrD;IACA;IACA,IAAI4W,MAAM,GAAG;MAAEna,IAAI,EAAE,EAAE;MAAEsE,KAAK,EAAE;IAAG,CAAC;IACpC,IAAI8P,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACR,YAAY,EAAEiB,QAAQ,CAAC;IAClF,IAAI,IAAI,CAAC7C,SAAS,EAAE;MAChBsC,YAAY,GAAG,IAAI,CAACkE,oBAAoB,CAAClE,YAAY,EAAE,IAAI,CAACV,YAAY,EAAEnQ,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAIuX,uBAAuB;IAC3B,IAAI,IAAI,CAAC/D,MAAM,CAAC,CAAC,EAAE;MACf+D,uBAAuB,GAAGnG,QAAQ,CAACjN,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACDoT,uBAAuB,GAAGnG,QAAQ,CAACjN,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAIoT,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAAC7a,SAAS,CAACK,eAAe,CAACya,WAAW;MAChEb,MAAM,CAAC7V,KAAK,GAAG,GAAGyW,aAAa,IAAI3G,YAAY,CAACyC,CAAC,GAAG,IAAI,CAACnD,YAAY,CAAC5R,KAAK,CAAC,IAAI;IACpF,CAAC,MACI;MACDqY,MAAM,CAACna,IAAI,GAAG1B,mBAAmB,CAAC8V,YAAY,CAACyC,CAAC,CAAC;IACrD;IACA,OAAOsD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIf,oBAAoBA,CAAA,EAAG;IACnB;IACA,MAAM6B,YAAY,GAAG,IAAI,CAACxH,cAAc,CAAC,CAAC;IAC1C,MAAMyH,aAAa,GAAG,IAAI,CAAC/O,KAAK,CAAChH,qBAAqB,CAAC,CAAC;IACxD;IACA;IACA;IACA,MAAMgW,qBAAqB,GAAG,IAAI,CAAC/I,YAAY,CAACgJ,GAAG,CAACpY,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAACgC,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO;MACHkW,eAAe,EAAE7W,2BAA2B,CAACyW,YAAY,EAAEE,qBAAqB,CAAC;MACjFG,mBAAmB,EAAEzX,4BAA4B,CAACoX,YAAY,EAAEE,qBAAqB,CAAC;MACtFI,gBAAgB,EAAE/W,2BAA2B,CAAC0W,aAAa,EAAEC,qBAAqB,CAAC;MACnFK,oBAAoB,EAAE3X,4BAA4B,CAACqX,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACAtD,kBAAkBA,CAAClP,MAAM,EAAE,GAAG8S,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAGnY,IAAI,CAACiV,GAAG,CAACmD,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAEjT,MAAM,CAAC;EACd;EACA;EACA4K,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAMzR,KAAK,GAAG,IAAI,CAAC5B,SAAS,CAACK,eAAe,CAACya,WAAW;IACxD,MAAMpZ,MAAM,GAAG,IAAI,CAAC1B,SAAS,CAACK,eAAe,CAACsa,YAAY;IAC1D,MAAMtX,cAAc,GAAG,IAAI,CAAC3D,cAAc,CAACa,yBAAyB,CAAC,CAAC;IACtE,OAAO;MACHV,GAAG,EAAEwD,cAAc,CAACxD,GAAG,GAAG,IAAI,CAACoS,eAAe;MAC9CnS,IAAI,EAAEuD,cAAc,CAACvD,IAAI,GAAG,IAAI,CAACmS,eAAe;MAChD7N,KAAK,EAAEf,cAAc,CAACvD,IAAI,GAAG8B,KAAK,GAAG,IAAI,CAACqQ,eAAe;MACzDhO,MAAM,EAAEZ,cAAc,CAACxD,GAAG,GAAG6B,MAAM,GAAG,IAAI,CAACuQ,eAAe;MAC1DrQ,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACqQ,eAAe;MACvCvQ,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACuQ;IAC9B,CAAC;EACL;EACA;EACA4E,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACxU,WAAW,CAACyN,YAAY,CAAC,CAAC,KAAK,KAAK;EACpD;EACA;EACAoK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAACnI,sBAAsB,IAAI,IAAI,CAACH,SAAS;EACzD;EACA;EACAyF,UAAUA,CAAC5C,QAAQ,EAAEkH,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAOlH,QAAQ,CAACrN,OAAO,IAAI,IAAI,GAAG,IAAI,CAACiL,QAAQ,GAAGoC,QAAQ,CAACrN,OAAO;IACtE;IACA,OAAOqN,QAAQ,CAACpN,OAAO,IAAI,IAAI,GAAG,IAAI,CAACiL,QAAQ,GAAGmC,QAAQ,CAACpN,OAAO;EACtE;EACA;EACAqL,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAOhQ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAAC8O,mBAAmB,CAAC/I,MAAM,EAAE;QAClC,MAAM3G,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAAC0P,mBAAmB,CAACoK,OAAO,CAACC,IAAI,IAAI;QACrC5T,0BAA0B,CAAC,SAAS,EAAE4T,IAAI,CAACvU,OAAO,CAAC;QACnDQ,wBAAwB,CAAC,SAAS,EAAE+T,IAAI,CAACtU,OAAO,CAAC;QACjDU,0BAA0B,CAAC,UAAU,EAAE4T,IAAI,CAACrU,QAAQ,CAAC;QACrDM,wBAAwB,CAAC,UAAU,EAAE+T,IAAI,CAACpU,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACAuR,gBAAgBA,CAACnI,UAAU,EAAE;IACzB,IAAI,IAAI,CAAC5E,KAAK,EAAE;MACZ5N,WAAW,CAACwS,UAAU,CAAC,CAAC+K,OAAO,CAACE,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACvJ,oBAAoB,CAAChK,OAAO,CAACuT,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACvJ,oBAAoB,CAAClK,IAAI,CAACyT,QAAQ,CAAC;UACxC,IAAI,CAAC7P,KAAK,CAACxL,SAAS,CAACC,GAAG,CAACob,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA7I,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAChH,KAAK,EAAE;MACZ,IAAI,CAACsG,oBAAoB,CAACqJ,OAAO,CAACE,QAAQ,IAAI;QAC1C,IAAI,CAAC7P,KAAK,CAACxL,SAAS,CAACU,MAAM,CAAC2a,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACvJ,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACAgB,cAAcA,CAAA,EAAG;IACb,MAAMrM,MAAM,GAAG,IAAI,CAACkP,OAAO;IAC3B,IAAIlP,MAAM,YAAY3J,UAAU,EAAE;MAC9B,OAAO2J,MAAM,CAACjE,aAAa,CAACgC,qBAAqB,CAAC,CAAC;IACvD;IACA;IACA,IAAIiC,MAAM,YAAY6U,OAAO,EAAE;MAC3B,OAAO7U,MAAM,CAACjC,qBAAqB,CAAC,CAAC;IACzC;IACA,MAAMrD,KAAK,GAAGsF,MAAM,CAACtF,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAGwF,MAAM,CAACxF,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACH7B,GAAG,EAAEqH,MAAM,CAAC6P,CAAC;MACb9S,MAAM,EAAEiD,MAAM,CAAC6P,CAAC,GAAGrV,MAAM;MACzB5B,IAAI,EAAEoH,MAAM,CAACyP,CAAC;MACdvS,KAAK,EAAE8C,MAAM,CAACyP,CAAC,GAAG/U,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASuT,YAAYA,CAAC6G,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIjV,GAAG,IAAIiV,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAAClV,GAAG,CAAC,EAAE;MAC5BgV,WAAW,CAAChV,GAAG,CAAC,GAAGiV,MAAM,CAACjV,GAAG,CAAC;IAClC;EACJ;EACA,OAAOgV,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAAS/D,aAAaA,CAACkE,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAACnU,KAAK,EAAEoU,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAChL,cAAc,CAAC;IAClD,OAAO,CAAC+K,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAACtU,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOmU,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/E,4BAA4BA,CAACmF,UAAU,EAAE;EAC9C,OAAO;IACH1c,GAAG,EAAEyD,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAAC1c,GAAG,CAAC;IAC/BuE,KAAK,EAAEd,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAACnY,KAAK,CAAC;IACnCH,MAAM,EAAEX,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAACtY,MAAM,CAAC;IACrCnE,IAAI,EAAEwD,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAACzc,IAAI,CAAC;IACjC8B,KAAK,EAAE0B,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAAC3a,KAAK,CAAC;IACnCF,MAAM,EAAE4B,IAAI,CAACkZ,KAAK,CAACD,UAAU,CAAC7a,MAAM;EACxC,CAAC;AACL;AACA;AACA,SAAS0X,uBAAuBA,CAACqD,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,OAAQD,CAAC,CAACtB,eAAe,KAAKuB,CAAC,CAACvB,eAAe,IAC3CsB,CAAC,CAACrB,mBAAmB,KAAKsB,CAAC,CAACtB,mBAAmB,IAC/CqB,CAAC,CAACpB,gBAAgB,KAAKqB,CAAC,CAACrB,gBAAgB,IACzCoB,CAAC,CAACnB,oBAAoB,KAAKoB,CAAC,CAACpB,oBAAoB;AACzD;AACA,MAAMqB,iCAAiC,GAAG,CACtC;EAAErV,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAMmV,oCAAoC,GAAG,CACzC;EAAEtV,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA,MAAMoV,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBrd,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsd,YAAY,GAAG,QAAQ;IAC5B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC1K,WAAW,GAAG,KAAK;EAC5B;EACA3S,MAAMA,CAACwC,UAAU,EAAE;IACf,MAAM6C,MAAM,GAAG7C,UAAU,CAACyM,SAAS,CAAC,CAAC;IACrC,IAAI,CAAC7M,WAAW,GAAGI,UAAU;IAC7B,IAAI,IAAI,CAAC4a,MAAM,IAAI,CAAC/X,MAAM,CAAC1D,KAAK,EAAE;MAC9Ba,UAAU,CAAC6M,UAAU,CAAC;QAAE1N,KAAK,EAAE,IAAI,CAACyb;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAAChY,MAAM,CAAC5D,MAAM,EAAE;MAChCe,UAAU,CAAC6M,UAAU,CAAC;QAAE5N,MAAM,EAAE,IAAI,CAAC4b;MAAQ,CAAC,CAAC;IACnD;IACA7a,UAAU,CAAC8K,WAAW,CAAC9M,SAAS,CAACC,GAAG,CAACmc,YAAY,CAAC;IAClD,IAAI,CAACjK,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACI/S,GAAGA,CAACmI,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACiV,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,UAAU,GAAGhV,KAAK;IACvB,IAAI,CAACkV,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIpd,IAAIA,CAACkI,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAACoV,QAAQ,GAAGpV,KAAK;IACrB,IAAI,CAACmV,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIlZ,MAAMA,CAAC+D,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAACgV,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAGjV,KAAK;IAC1B,IAAI,CAACkV,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI9Y,KAAKA,CAAC4D,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACoV,QAAQ,GAAGpV,KAAK;IACrB,IAAI,CAACmV,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI9E,KAAKA,CAACrQ,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACoV,QAAQ,GAAGpV,KAAK;IACrB,IAAI,CAACmV,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACvV,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACoV,QAAQ,GAAGpV,KAAK;IACrB,IAAI,CAACmV,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvb,KAAKA,CAACoG,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAAC3F,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACiN,UAAU,CAAC;QAAE1N,KAAK,EAAEoG;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACqV,MAAM,GAAGrV,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACItG,MAAMA,CAACsG,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAAC3F,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACiN,UAAU,CAAC;QAAE5N,MAAM,EAAEsG;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACsV,OAAO,GAAGtV,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwV,kBAAkBA,CAAClH,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAACxW,IAAI,CAACwW,MAAM,CAAC;IACjB,IAAI,CAAC6G,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,gBAAgBA,CAACnH,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAACzW,GAAG,CAACyW,MAAM,CAAC;IAChB,IAAI,CAAC4G,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI/N,KAAKA,CAAA,EAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC9M,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACtD;IACJ;IACA,MAAM2X,MAAM,GAAG,IAAI,CAAC5X,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAMkd,YAAY,GAAG,IAAI,CAACrb,WAAW,CAACkL,WAAW,CAAC/M,KAAK;IACvD,MAAM8E,MAAM,GAAG,IAAI,CAACjD,WAAW,CAAC6M,SAAS,CAAC,CAAC;IAC3C,MAAM;MAAEtN,KAAK;MAAEF,MAAM;MAAEwO,QAAQ;MAAEC;IAAU,CAAC,GAAG7K,MAAM;IACrD,MAAMqY,yBAAyB,GAAG,CAAC/b,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAACsO,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAM0N,uBAAuB,GAAG,CAAClc,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAACyO,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAM0N,SAAS,GAAG,IAAI,CAACV,UAAU;IACjC,MAAMW,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC7B,MAAM1D,KAAK,GAAG,IAAI,CAACrX,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACQ,SAAS,KAAK,KAAK;IAC9D,IAAIqO,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAI3I,cAAc,GAAG,EAAE;IACvB,IAAIsI,yBAAyB,EAAE;MAC3BtI,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAIwI,SAAS,KAAK,QAAQ,EAAE;MAC7BxI,cAAc,GAAG,QAAQ;MACzB,IAAIqE,KAAK,EAAE;QACPsE,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAIpE,KAAK,EAAE;MACZ,IAAImE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7CxI,cAAc,GAAG,UAAU;QAC3B0I,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrDxI,cAAc,GAAG,YAAY;QAC7B2I,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpDxI,cAAc,GAAG,YAAY;MAC7B0I,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnDxI,cAAc,GAAG,UAAU;MAC3B2I,WAAW,GAAGF,OAAO;IACzB;IACA7D,MAAM,CAACxF,QAAQ,GAAG,IAAI,CAACsI,YAAY;IACnC9C,MAAM,CAAC8D,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChE9D,MAAM,CAACgE,SAAS,GAAGL,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACZ,UAAU;IAClE/C,MAAM,CAACiE,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxChD,MAAM,CAAC+D,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAACrI,cAAc,GAAGA,cAAc;IAC5CqI,YAAY,CAACtI,UAAU,GAAGwI,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACV,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACI9O,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACwE,WAAW,IAAI,CAAC,IAAI,CAACvQ,WAAW,EAAE;MACvC;IACJ;IACA,MAAM4X,MAAM,GAAG,IAAI,CAAC5X,WAAW,CAACU,cAAc,CAACvC,KAAK;IACpD,MAAMkK,MAAM,GAAG,IAAI,CAACrI,WAAW,CAACkL,WAAW;IAC3C,MAAMmQ,YAAY,GAAGhT,MAAM,CAAClK,KAAK;IACjCkK,MAAM,CAACjK,SAAS,CAACU,MAAM,CAAC0b,YAAY,CAAC;IACrCa,YAAY,CAACrI,cAAc,GACvBqI,YAAY,CAACtI,UAAU,GACnB6E,MAAM,CAACgE,SAAS,GACZhE,MAAM,CAACiE,YAAY,GACfjE,MAAM,CAAC8D,UAAU,GACb9D,MAAM,CAAC+D,WAAW,GACd/D,MAAM,CAACxF,QAAQ,GACX,EAAE;IAC9B,IAAI,CAACpS,WAAW,GAAG,IAAI;IACvB,IAAI,CAACuQ,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMuL,sBAAsB,CAAC;EACzB1e,WAAWA,CAACC,cAAc,EAAEM,SAAS,EAAE0J,SAAS,EAAEgI,iBAAiB,EAAE;IACjE,IAAI,CAAChS,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0J,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgI,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;EACI0M,MAAMA,CAAA,EAAG;IACL,OAAO,IAAItB,sBAAsB,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIuB,mBAAmBA,CAACnX,MAAM,EAAE;IACxB,OAAO,IAAIoK,iCAAiC,CAACpK,MAAM,EAAE,IAAI,CAACxH,cAAc,EAAE,IAAI,CAACM,SAAS,EAAE,IAAI,CAAC0J,SAAS,EAAE,IAAI,CAACgI,iBAAiB,CAAC;EACrI;EACA;IAAS,IAAI,CAACjM,IAAI,YAAA6Y,+BAAA3Y,iBAAA;MAAA,YAAAA,iBAAA,IAA+FwY,sBAAsB,EAxkEhCnhB,EAAE,CAAA4I,QAAA,CAwkEgDnJ,EAAE,CAACI,aAAa,GAxkElEG,EAAE,CAAA4I,QAAA,CAwkE6E7I,QAAQ,GAxkEvFC,EAAE,CAAA4I,QAAA,CAwkEkGtH,IAAI,CAACmM,QAAQ,GAxkEjHzN,EAAE,CAAA4I,QAAA,CAwkE4HqF,gBAAgB;IAAA,CAA6C;EAAE;EACpS;IAAS,IAAI,CAACpF,KAAK,kBAzkEoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAykEYoY,sBAAsB;MAAAnY,OAAA,EAAtBmY,sBAAsB,CAAA1Y,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxK;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KA3kE2G1F,EAAE,CAAAkJ,iBAAA,CA2kEXiY,sBAAsB,EAAc,CAAC;IAC3HhY,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAE1J,EAAE,CAACI;EAAc,CAAC,EAAE;IAAEsJ,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC3EH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAE7H,IAAI,CAACmM;EAAS,CAAC,EAAE;IAAEtE,IAAI,EAAE8E;EAAiB,CAAC,CAAC;AAAA;;AAE1E;AACA,IAAIsT,YAAY,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACV/e,WAAWA,CAAA,CACX;EACAgf,gBAAgB,EAAE/M,iBAAiB,EAAEgN,yBAAyB,EAAEC,gBAAgB,EAAEzS,mBAAmB,EAAEI,SAAS,EAAErK,OAAO,EAAEjC,SAAS,EAAE4e,eAAe,EAAEzS,SAAS,EAAEC,uBAAuB,EAAEyS,qBAAqB,EAAE;IAC9M,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/M,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACgN,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACzS,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACI,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC4e,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACzS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACyS,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;AACJ;AACA;AACA;AACA;EACIC,MAAMA,CAACxZ,MAAM,EAAE;IACX,MAAMyF,IAAI,GAAG,IAAI,CAACgU,kBAAkB,CAAC,CAAC;IACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAAClU,IAAI,CAAC;IAC1C,MAAMmU,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;IACnD,MAAMI,aAAa,GAAG,IAAI7Y,aAAa,CAACjB,MAAM,CAAC;IAC/C8Z,aAAa,CAAC1P,SAAS,GAAG0P,aAAa,CAAC1P,SAAS,IAAI,IAAI,CAACkP,eAAe,CAAC5W,KAAK;IAC/E,OAAO,IAAI8D,UAAU,CAACoT,YAAY,EAAEnU,IAAI,EAAEiU,IAAI,EAAEI,aAAa,EAAE,IAAI,CAACnd,OAAO,EAAE,IAAI,CAACiK,mBAAmB,EAAE,IAAI,CAAClM,SAAS,EAAE,IAAI,CAACmM,SAAS,EAAE,IAAI,CAACC,uBAAuB,EAAE,IAAI,CAACyS,qBAAqB,KAAK,gBAAgB,EAAE,IAAI,CAACvS,SAAS,CAAC+S,GAAG,CAAC7hB,mBAAmB,CAAC,CAAC;EAClQ;EACA;AACJ;AACA;AACA;AACA;EACIiX,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACkK,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;EACIM,kBAAkBA,CAAClU,IAAI,EAAE;IACrB,MAAMiU,IAAI,GAAG,IAAI,CAAChf,SAAS,CAAC0L,aAAa,CAAC,KAAK,CAAC;IAChDsT,IAAI,CAACM,EAAE,GAAG,eAAef,YAAY,EAAE,EAAE;IACzCS,IAAI,CAACve,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtCqK,IAAI,CAACa,WAAW,CAACoT,IAAI,CAAC;IACtB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkBA,CAAA,EAAG;IACjB,MAAMhU,IAAI,GAAG,IAAI,CAAC/K,SAAS,CAAC0L,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAACgG,iBAAiB,CAACvG,mBAAmB,CAAC,CAAC,CAACS,WAAW,CAACb,IAAI,CAAC;IAC9D,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIoU,mBAAmBA,CAACH,IAAI,EAAE;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAACO,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACjT,SAAS,CAAC+S,GAAG,CAAC5hB,cAAc,CAAC;IACrD;IACA,OAAO,IAAIsB,eAAe,CAACigB,IAAI,EAAE,IAAI,CAACN,yBAAyB,EAAE,IAAI,CAACa,OAAO,EAAE,IAAI,CAACjT,SAAS,EAAE,IAAI,CAACtM,SAAS,CAAC;EAClH;EACA;IAAS,IAAI,CAACyF,IAAI,YAAA+Z,gBAAA7Z,iBAAA;MAAA,YAAAA,iBAAA,IAA+F6Y,OAAO,EAvqEjBxhB,EAAE,CAAA4I,QAAA,CAuqEiCT,qBAAqB,GAvqExDnI,EAAE,CAAA4I,QAAA,CAuqEmEqF,gBAAgB,GAvqErFjO,EAAE,CAAA4I,QAAA,CAuqEgG5I,EAAE,CAACyiB,wBAAwB,GAvqE7HziB,EAAE,CAAA4I,QAAA,CAuqEwIuY,sBAAsB,GAvqEhKnhB,EAAE,CAAA4I,QAAA,CAuqE2K+C,yBAAyB,GAvqEtM3L,EAAE,CAAA4I,QAAA,CAuqEiN5I,EAAE,CAAC0iB,QAAQ,GAvqE9N1iB,EAAE,CAAA4I,QAAA,CAuqEyO5I,EAAE,CAACc,MAAM,GAvqEpPd,EAAE,CAAA4I,QAAA,CAuqE+P7I,QAAQ,GAvqEzQC,EAAE,CAAA4I,QAAA,CAuqEoR/G,EAAE,CAAC8gB,cAAc,GAvqEvS3iB,EAAE,CAAA4I,QAAA,CAuqEkT9I,EAAE,CAAC8iB,QAAQ,GAvqE/T5iB,EAAE,CAAA4I,QAAA,CAuqE0U6D,6BAA6B,GAvqEzWzM,EAAE,CAAA4I,QAAA,CAuqEoXlI,qBAAqB;IAAA,CAA6D;EAAE;EACjjB;IAAS,IAAI,CAACmI,KAAK,kBAxqEoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAwqEYyY,OAAO;MAAAxY,OAAA,EAAPwY,OAAO,CAAA/Y,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACzJ;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KA1qE2G1F,EAAE,CAAAkJ,iBAAA,CA0qEXsY,OAAO,EAAc,CAAC;IAC5GrY,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEhB;EAAsB,CAAC,EAAE;IAAEgB,IAAI,EAAE8E;EAAiB,CAAC,EAAE;IAAE9E,IAAI,EAAEnJ,EAAE,CAACyiB;EAAyB,CAAC,EAAE;IAAEtZ,IAAI,EAAEgY;EAAuB,CAAC,EAAE;IAAEhY,IAAI,EAAEwC;EAA0B,CAAC,EAAE;IAAExC,IAAI,EAAEnJ,EAAE,CAAC0iB;EAAS,CAAC,EAAE;IAAEvZ,IAAI,EAAEnJ,EAAE,CAACc;EAAO,CAAC,EAAE;IAAEqI,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtQH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAEtH,EAAE,CAAC8gB;EAAe,CAAC,EAAE;IAAExZ,IAAI,EAAErJ,EAAE,CAAC8iB;EAAS,CAAC,EAAE;IAAEzZ,IAAI,EAAEsD;EAA8B,CAAC,EAAE;IAAEtD,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/HH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAAC1I,qBAAqB;IAChC,CAAC,EAAE;MACCyI,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,MAAM0iB,mBAAmB,GAAG,CACxB;EACIvY,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAMqY,qCAAqC,GAAG,IAAIniB,cAAc,CAAC,uCAAuC,EAAE;EACtGsI,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMmB,OAAO,GAAGvJ,MAAM,CAAC4gB,OAAO,CAAC;IAC/B,OAAO,MAAMrX,OAAO,CAACsX,gBAAgB,CAACjZ,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMua,gBAAgB,CAAC;EACnBtgB,WAAWA,CAAA,CACX;EACAugB,UAAU,EAAE;IACR,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACva,IAAI,YAAAwa,yBAAAta,iBAAA;MAAA,YAAAA,iBAAA,IAA+Foa,gBAAgB,EApuE1B/iB,EAAE,CAAAkjB,iBAAA,CAouE0CljB,EAAE,CAACO,UAAU;IAAA,CAA4C;EAAE;EAC9M;IAAS,IAAI,CAAC4iB,IAAI,kBAruEqFnjB,EAAE,CAAAojB,iBAAA;MAAAja,IAAA,EAquEJ4Z,gBAAgB;MAAAM,SAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA,EAA6I;EAAE;AACxQ;AACA;EAAA,QAAA7d,SAAA,oBAAAA,SAAA,KAvuE2G1F,EAAE,CAAAkJ,iBAAA,CAuuEX6Z,gBAAgB,EAAc,CAAC;IACrH5Z,IAAI,EAAEtI,SAAS;IACfuI,IAAI,EAAE,CAAC;MACCqQ,QAAQ,EAAE,4DAA4D;MACtE6J,QAAQ,EAAE,kBAAkB;MAC5BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpa,IAAI,EAAEnJ,EAAE,CAACO;EAAW,CAAC,CAAC;AAAA;AAC3D;AACA;AACA;AACA;AACA,MAAMijB,mBAAmB,CAAC;EACtB;EACA,IAAIpZ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACiL,QAAQ;EACxB;EACA,IAAIjL,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACiL,QAAQ,GAAGjL,OAAO;IACvB,IAAI,IAAI,CAACqZ,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAIpZ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACiL,QAAQ;EACxB;EACA,IAAIjL,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACiL,QAAQ,GAAGjL,OAAO;IACvB,IAAI,IAAI,CAACoZ,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAI7Z,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAAC+Z,oBAAoB;EACpC;EACA,IAAI/Z,mBAAmBA,CAACoB,KAAK,EAAE;IAC3B,IAAI,CAAC2Y,oBAAoB,GAAG3Y,KAAK;EACrC;EACA;EACAvI,WAAWA,CAACmhB,QAAQ,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,qBAAqB,EAAEC,IAAI,EAAE;IAC9E,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,qBAAqB,GAAG9hB,YAAY,CAACyN,KAAK;IAC/C,IAAI,CAACsU,mBAAmB,GAAG/hB,YAAY,CAACyN,KAAK;IAC7C,IAAI,CAACuU,mBAAmB,GAAGhiB,YAAY,CAACyN,KAAK;IAC7C,IAAI,CAACwU,qBAAqB,GAAGjiB,YAAY,CAACyN,KAAK;IAC/C,IAAI,CAAC+T,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAAC1e,OAAO,GAAGrE,MAAM,CAACE,MAAM,CAAC;IAC7B;IACA,IAAI,CAACujB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAAC7a,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAAC8a,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAAC3L,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAAC1N,IAAI,GAAG,KAAK;IACjB;IACA,IAAI,CAAC0G,aAAa,GAAG,IAAIhR,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAAC0jB,cAAc,GAAG,IAAI1jB,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAACkC,MAAM,GAAG,IAAIlC,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACyE,MAAM,GAAG,IAAIzE,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC2jB,cAAc,GAAG,IAAI3jB,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAAC4jB,mBAAmB,GAAG,IAAI5jB,YAAY,CAAC,CAAC;IAC7C,IAAI,CAAC6jB,eAAe,GAAG,IAAI5iB,cAAc,CAAC6hB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAACe,sBAAsB,GAAGd,qBAAqB;IACnD,IAAI,CAACva,cAAc,GAAG,IAAI,CAACqb,sBAAsB,CAAC,CAAC;EACvD;EACA;EACA,IAAIpf,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,WAAW;EAC3B;EACA;EACA,IAAIoN,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACuR,IAAI,GAAG,IAAI,CAACA,IAAI,CAAChZ,KAAK,GAAG,KAAK;EAC9C;EACAI,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8Y,mBAAmB,CAACzd,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC0d,mBAAmB,CAAC1d,WAAW,CAAC,CAAC;IACtC,IAAI,CAACwd,qBAAqB,CAACxd,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC2d,qBAAqB,CAAC3d,WAAW,CAAC,CAAC;IACxC,IAAI,IAAI,CAACpB,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC+L,OAAO,CAAC,CAAC;IAC9B;EACJ;EACA0T,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACtB,SAAS,EAAE;MAChB,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACD,SAAS,CAAC;MAC5C,IAAI,CAACpe,WAAW,CAACiN,UAAU,CAAC;QACxB1N,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBoO,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBtO,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBuO,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAI8R,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACT,IAAI,EAAE;QAChC,IAAI,CAACb,SAAS,CAACtR,KAAK,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI4S,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACT,IAAI,GAAG,IAAI,CAACU,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAC7D;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC3Q,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC9I,MAAM,EAAE;MAC3C,IAAI,CAAC8I,SAAS,GAAGsO,mBAAmB;IACxC;IACA,MAAMpd,UAAU,GAAI,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACue,QAAQ,CAAC9B,MAAM,CAAC,IAAI,CAACqD,YAAY,CAAC,CAAC,CAAE;IACjF,IAAI,CAACjB,mBAAmB,GAAGze,UAAU,CAACuM,WAAW,CAAC,CAAC,CAAC5L,SAAS,CAAC,MAAM,IAAI,CAACnD,MAAM,CAACmiB,IAAI,CAAC,CAAC,CAAC;IACvF,IAAI,CAACjB,mBAAmB,GAAG1e,UAAU,CAACwM,WAAW,CAAC,CAAC,CAAC7L,SAAS,CAAC,MAAM,IAAI,CAACZ,MAAM,CAAC4f,IAAI,CAAC,CAAC,CAAC;IACvF3f,UAAU,CAACyG,aAAa,CAAC,CAAC,CAAC9F,SAAS,CAAEyF,KAAK,IAAK;MAC5C,IAAI,CAAC6Y,cAAc,CAACvY,IAAI,CAACN,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACwZ,OAAO,KAAKhjB,MAAM,IAAI,CAAC,IAAI,CAACkiB,YAAY,IAAI,CAACjiB,cAAc,CAACuJ,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACyZ,cAAc,CAAC,CAAC;QACtB,IAAI,CAACL,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,IAAI,CAAC5f,WAAW,CAAC8H,oBAAoB,CAAC,CAAC,CAAC/G,SAAS,CAAEyF,KAAK,IAAK;MACzD,MAAM3B,MAAM,GAAG,IAAI,CAACqb,iBAAiB,CAAC,CAAC;MACvC,MAAMxY,MAAM,GAAGvL,eAAe,CAACqK,KAAK,CAAC;MACrC,IAAI,CAAC3B,MAAM,IAAKA,MAAM,KAAK6C,MAAM,IAAI,CAAC7C,MAAM,CAAC5F,QAAQ,CAACyI,MAAM,CAAE,EAAE;QAC5D,IAAI,CAAC4X,mBAAmB,CAACxY,IAAI,CAACN,KAAK,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;EACAsZ,YAAYA,CAAA,EAAG;IACX,MAAMhV,gBAAgB,GAAI,IAAI,CAACsT,SAAS,GACpC,IAAI,CAACtT,gBAAgB,IAAI,IAAI,CAACqV,uBAAuB,CAAC,CAAE;IAC5D,MAAMpD,aAAa,GAAG,IAAI7Y,aAAa,CAAC;MACpCmJ,SAAS,EAAE,IAAI,CAACsR,IAAI;MACpB7T,gBAAgB;MAChB3G,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCE,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BE,mBAAmB,EAAE,IAAI,CAACA;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAAChF,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChCwd,aAAa,CAACxd,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClC0d,aAAa,CAAC1d,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAACsO,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtCoP,aAAa,CAACpP,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxCmP,aAAa,CAACnP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACtJ,aAAa,EAAE;MACpByY,aAAa,CAACzY,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjB2Y,aAAa,CAAC3Y,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAO2Y,aAAa;EACxB;EACA;EACAsB,uBAAuBA,CAACvT,gBAAgB,EAAE;IACtC,MAAMoE,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC2J,GAAG,CAACuH,eAAe,KAAK;MACrDnb,OAAO,EAAEmb,eAAe,CAACnb,OAAO;MAChCC,OAAO,EAAEkb,eAAe,CAAClb,OAAO;MAChCC,QAAQ,EAAEib,eAAe,CAACjb,QAAQ;MAClCC,QAAQ,EAAEgb,eAAe,CAAChb,QAAQ;MAClCL,OAAO,EAAEqb,eAAe,CAACrb,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAEob,eAAe,CAACpb,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDZ,UAAU,EAAEgc,eAAe,CAAChc,UAAU,IAAIJ;IAC9C,CAAC,CAAC,CAAC;IACH,OAAO8G,gBAAgB,CAClBsF,SAAS,CAAC,IAAI,CAACiQ,UAAU,CAAC,CAAC,CAAC,CAC5BjN,aAAa,CAAClE,SAAS,CAAC,CACxBqE,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CG,QAAQ,CAAC,IAAI,CAAC3N,IAAI,CAAC,CACnByN,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCL,kBAAkB,CAAC,IAAI,CAAC2L,cAAc,CAAC,CACvCnL,kBAAkB,CAAC,IAAI,CAACsL,YAAY,CAAC,CACrChL,qBAAqB,CAAC,IAAI,CAACmM,uBAAuB,CAAC;EAC5D;EACA;EACAH,uBAAuBA,CAAA,EAAG;IACtB,MAAMnT,QAAQ,GAAG,IAAI,CAACuR,QAAQ,CAACnM,QAAQ,CAAC,CAAC,CAAC4J,mBAAmB,CAAC,IAAI,CAACqE,UAAU,CAAC,CAAC,CAAC;IAChF,IAAI,CAAChC,uBAAuB,CAACrR,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACAqT,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACxb,MAAM,YAAY6Y,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC7Y,MAAM,CAAC8Y,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAAC9Y,MAAM;IACtB;EACJ;EACAqb,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACrb,MAAM,YAAY6Y,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAAC7Y,MAAM,CAAC8Y,UAAU,CAAC/c,aAAa;IAC/C;IACA,IAAI,IAAI,CAACiE,MAAM,YAAY3J,UAAU,EAAE;MACnC,OAAO,IAAI,CAAC2J,MAAM,CAACjE,aAAa;IACpC;IACA,IAAI,OAAO8Y,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC7U,MAAM,YAAY6U,OAAO,EAAE;MAClE,OAAO,IAAI,CAAC7U,MAAM;IACtB;IACA,OAAO,IAAI;EACf;EACA;EACA8a,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC3f,WAAW,EAAE;MACnB,IAAI,CAAC6f,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAAC7f,WAAW,CAAC6M,SAAS,CAAC,CAAC,CAACxI,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAACrE,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;MACjC,IAAI,CAACD,WAAW,CAACpC,MAAM,CAAC,IAAI,CAAC2hB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAAClb,WAAW,EAAE;MAClB,IAAI,CAACua,qBAAqB,GAAG,IAAI,CAAC5e,WAAW,CAAC0M,aAAa,CAAC,CAAC,CAAC3L,SAAS,CAACyF,KAAK,IAAI;QAC7E,IAAI,CAACkG,aAAa,CAACqT,IAAI,CAACvZ,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACoY,qBAAqB,CAACxd,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAAC2d,qBAAqB,CAAC3d,WAAW,CAAC,CAAC;IACxC;IACA;IACA,IAAI,IAAI,CAACge,cAAc,CAACxY,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAAC2Y,qBAAqB,GAAG,IAAI,CAACX,SAAS,CAACjO,eAAe,CACtD3P,IAAI,CAACjE,SAAS,CAAC,MAAM,IAAI,CAAC6iB,cAAc,CAACxY,SAAS,CAACR,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/DrF,SAAS,CAACqR,QAAQ,IAAI;QACvB,IAAI,CAACxS,OAAO,CAACM,GAAG,CAAC,MAAM,IAAI,CAACkf,cAAc,CAACW,IAAI,CAAC3N,QAAQ,CAAC,CAAC;QAC1D,IAAI,IAAI,CAACgN,cAAc,CAACxY,SAAS,CAACR,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAAC2Y,qBAAqB,CAAC3d,WAAW,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAwe,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC5f,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACG,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAACye,qBAAqB,CAACxd,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC2d,qBAAqB,CAAC3d,WAAW,CAAC,CAAC;EAC5C;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAmd,4BAAAjd,iBAAA;MAAA,YAAAA,iBAAA,IAA+F6a,mBAAmB,EA1+E7BxjB,EAAE,CAAAkjB,iBAAA,CA0+E6C1B,OAAO,GA1+EtDxhB,EAAE,CAAAkjB,iBAAA,CA0+EiEljB,EAAE,CAAC6lB,WAAW,GA1+EjF7lB,EAAE,CAAAkjB,iBAAA,CA0+E4FljB,EAAE,CAAC8lB,gBAAgB,GA1+EjH9lB,EAAE,CAAAkjB,iBAAA,CA0+E4HJ,qCAAqC,GA1+EnK9iB,EAAE,CAAAkjB,iBAAA,CA0+E8KrhB,EAAE,CAAC8gB,cAAc;IAAA,CAA4D;EAAE;EACtW;IAAS,IAAI,CAACQ,IAAI,kBA3+EqFnjB,EAAE,CAAAojB,iBAAA;MAAAja,IAAA,EA2+EJqa,mBAAmB;MAAAH,SAAA;MAAA0C,MAAA;QAAA7b,MAAA;QAAAqK,SAAA;QAAApE,gBAAA;QAAA/F,OAAA;QAAAC,OAAA;QAAAzF,KAAA;QAAAF,MAAA;QAAAsO,QAAA;QAAAC,SAAA;QAAAtJ,aAAA;QAAAF,UAAA;QAAA4a,cAAA;QAAA7a,cAAA;QAAA8a,IAAA;QAAAC,YAAA;QAAAoB,uBAAA;QAAAjc,WAAA,uDAAmoC1I,gBAAgB;QAAAwjB,YAAA,yDAAqExjB,gBAAgB;QAAA6X,kBAAA,qEAAuF7X,gBAAgB;QAAA+X,aAAA,2DAAwE/X,gBAAgB;QAAAqK,IAAA,yCAA6CrK,gBAAgB;QAAA4I,mBAAA,uEAA0F5I,gBAAgB;MAAA;MAAAglB,OAAA;QAAAjU,aAAA;QAAA0S,cAAA;QAAAxhB,MAAA;QAAAuC,MAAA;QAAAkf,cAAA;QAAAC,mBAAA;MAAA;MAAArB,QAAA;MAAAC,UAAA;MAAA0C,QAAA,GA3+E/lDjmB,EAAE,CAAAkmB,wBAAA,EAAFlmB,EAAE,CAAAmmB,oBAAA;IAAA,EA2+E02D;EAAE;AACz9D;AACA;EAAA,QAAAzgB,SAAA,oBAAAA,SAAA,KA7+E2G1F,EAAE,CAAAkJ,iBAAA,CA6+EXsa,mBAAmB,EAAc,CAAC;IACxHra,IAAI,EAAEtI,SAAS;IACfuI,IAAI,EAAE,CAAC;MACCqQ,QAAQ,EAAE,qEAAqE;MAC/E6J,QAAQ,EAAE,qBAAqB;MAC/BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpa,IAAI,EAAEqY;EAAQ,CAAC,EAAE;IAAErY,IAAI,EAAEnJ,EAAE,CAAC6lB;EAAY,CAAC,EAAE;IAAE1c,IAAI,EAAEnJ,EAAE,CAAC8lB;EAAiB,CAAC,EAAE;IAAE3c,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC3HH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAAC0Z,qCAAqC;IAChD,CAAC;EAAE,CAAC,EAAE;IAAE3Z,IAAI,EAAEtH,EAAE,CAAC8gB,cAAc;IAAErZ,UAAU,EAAE,CAAC;MAC1CH,IAAI,EAAEhJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE+J,MAAM,EAAE,CAAC;MAClCf,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEmL,SAAS,EAAE,CAAC;MACZpL,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAE+G,gBAAgB,EAAE,CAAC;MACnBhH,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEgB,OAAO,EAAE,CAAC;MACVjB,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEiB,OAAO,EAAE,CAAC;MACVlB,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAExE,KAAK,EAAE,CAAC;MACRuE,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE1E,MAAM,EAAE,CAAC;MACTyE,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE4J,QAAQ,EAAE,CAAC;MACX7J,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAE6J,SAAS,EAAE,CAAC;MACZ9J,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEO,aAAa,EAAE,CAAC;MAChBR,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEK,UAAU,EAAE,CAAC;MACbN,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEib,cAAc,EAAE,CAAC;MACjBlb,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEI,cAAc,EAAE,CAAC;MACjBL,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEkb,IAAI,EAAE,CAAC;MACPnb,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEmb,YAAY,EAAE,CAAC;MACfpb,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAEuc,uBAAuB,EAAE,CAAC;MAC1Bxc,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEM,WAAW,EAAE,CAAC;MACdP,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,gCAAgC;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IACnF,CAAC,CAAC;IAAEwjB,YAAY,EAAE,CAAC;MACfrb,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,iCAAiC;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IACpF,CAAC,CAAC;IAAE6X,kBAAkB,EAAE,CAAC;MACrB1P,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,uCAAuC;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IAC1F,CAAC,CAAC;IAAE+X,aAAa,EAAE,CAAC;MAChB5P,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,kCAAkC;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IACrF,CAAC,CAAC;IAAEqK,IAAI,EAAE,CAAC;MACPlC,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,yBAAyB;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IAC5E,CAAC,CAAC;IAAE4I,mBAAmB,EAAE,CAAC;MACtBT,IAAI,EAAElI,KAAK;MACXmI,IAAI,EAAE,CAAC;QAAEgd,KAAK,EAAE,wCAAwC;QAAEjJ,SAAS,EAAEnc;MAAiB,CAAC;IAC3F,CAAC,CAAC;IAAE+Q,aAAa,EAAE,CAAC;MAChB5I,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEujB,cAAc,EAAE,CAAC;MACjBtb,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAE+B,MAAM,EAAE,CAAC;MACTkG,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEsE,MAAM,EAAE,CAAC;MACT2D,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEwjB,cAAc,EAAE,CAAC;MACjBvb,IAAI,EAAEjI;IACV,CAAC,CAAC;IAAEyjB,mBAAmB,EAAE,CAAC;MACtBxb,IAAI,EAAEjI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASmlB,sDAAsDA,CAAClc,OAAO,EAAE;EACrE,OAAO,MAAMA,OAAO,CAACsX,gBAAgB,CAACjZ,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAM8d,8CAA8C,GAAG;EACnDC,OAAO,EAAEzD,qCAAqC;EAC9C0D,IAAI,EAAE,CAAChF,OAAO,CAAC;EACfiF,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,aAAa,CAAC;EAChB;IAAS,IAAI,CAACje,IAAI,YAAAke,sBAAAhe,iBAAA;MAAA,YAAAA,iBAAA,IAA+F+d,aAAa;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACE,IAAI,kBArlFqF5mB,EAAE,CAAA6mB,gBAAA;MAAA1d,IAAA,EAqlFSud;IAAa,EAAmK;EAAE;EACpS;IAAS,IAAI,CAACI,IAAI,kBAtlFqF9mB,EAAE,CAAA+mB,gBAAA;MAAAC,SAAA,EAslFmC,CAACxF,OAAO,EAAE8E,8CAA8C,CAAC;MAAAW,OAAA,GAAYnlB,UAAU,EAAEG,YAAY,EAAEvC,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AACrR;AACA;EAAA,QAAAgG,SAAA,oBAAAA,SAAA,KAxlF2G1F,EAAE,CAAAkJ,iBAAA,CAwlFXwd,aAAa,EAAc,CAAC;IAClHvd,IAAI,EAAEhI,QAAQ;IACdiI,IAAI,EAAE,CAAC;MACC6d,OAAO,EAAE,CAACnlB,UAAU,EAAEG,YAAY,EAAEvC,eAAe,EAAE8jB,mBAAmB,EAAET,gBAAgB,CAAC;MAC3FmE,OAAO,EAAE,CAAC1D,mBAAmB,EAAET,gBAAgB,EAAErjB,eAAe,CAAC;MACjEsnB,SAAS,EAAE,CAACxF,OAAO,EAAE8E,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,0BAA0B,SAASlZ,gBAAgB,CAAC;EACtDxL,WAAWA,CAACO,SAAS,EAAEokB,QAAQ,EAAE;IAC7B,KAAK,CAACpkB,SAAS,EAAEokB,QAAQ,CAAC;EAC9B;EACAhc,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,IAAI,CAACic,oBAAoB,IAAI,IAAI,CAACC,mBAAmB,EAAE;MACvD,IAAI,CAACtkB,SAAS,CAACuJ,mBAAmB,CAAC,IAAI,CAAC8a,oBAAoB,EAAE,IAAI,CAACC,mBAAmB,CAAC;IAC3F;EACJ;EACAlZ,gBAAgBA,CAAA,EAAG;IACf,KAAK,CAACA,gBAAgB,CAAC,CAAC;IACxB,IAAI,CAACmZ,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAACC,4BAA4B,CAAC,MAAM,IAAI,CAACD,gCAAgC,CAAC,CAAC,CAAC;EACpF;EACAA,gCAAgCA,CAAA,EAAG;IAC/B,IAAI,CAAC,IAAI,CAACrZ,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAMuZ,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACrD,MAAMha,MAAM,GAAG+Z,iBAAiB,IAAI,IAAI,CAACzkB,SAAS,CAACa,IAAI;IACvD6J,MAAM,CAACkB,WAAW,CAAC,IAAI,CAACV,iBAAiB,CAAC;EAC9C;EACAsZ,4BAA4BA,CAACG,EAAE,EAAE;IAC7B,MAAMC,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACtC,IAAID,SAAS,EAAE;MACX,IAAI,IAAI,CAACN,mBAAmB,EAAE;QAC1B,IAAI,CAACtkB,SAAS,CAACuJ,mBAAmB,CAACqb,SAAS,EAAE,IAAI,CAACN,mBAAmB,CAAC;MAC3E;MACA,IAAI,CAACtkB,SAAS,CAACsJ,gBAAgB,CAACsb,SAAS,EAAED,EAAE,CAAC;MAC9C,IAAI,CAACL,mBAAmB,GAAGK,EAAE;IACjC;EACJ;EACAE,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACR,oBAAoB,EAAE;MAC5B,MAAMrkB,SAAS,GAAG,IAAI,CAACA,SAAS;MAChC,IAAIA,SAAS,CAAC8kB,iBAAiB,EAAE;QAC7B,IAAI,CAACT,oBAAoB,GAAG,kBAAkB;MAClD,CAAC,MACI,IAAIrkB,SAAS,CAAC+kB,uBAAuB,EAAE;QACxC,IAAI,CAACV,oBAAoB,GAAG,wBAAwB;MACxD,CAAC,MACI,IAAIrkB,SAAS,CAACglB,oBAAoB,EAAE;QACrC,IAAI,CAACX,oBAAoB,GAAG,qBAAqB;MACrD,CAAC,MACI,IAAIrkB,SAAS,CAACilB,mBAAmB,EAAE;QACpC,IAAI,CAACZ,oBAAoB,GAAG,oBAAoB;MACpD;IACJ;IACA,OAAO,IAAI,CAACA,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;EACIK,oBAAoBA,CAAA,EAAG;IACnB,MAAM1kB,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,OAAQA,SAAS,CAACykB,iBAAiB,IAC/BzkB,SAAS,CAACklB,uBAAuB,IACjCllB,SAAS,CAACmlB,oBAAoB,IAC9BnlB,SAAS,CAAColB,mBAAmB,IAC7B,IAAI;EACZ;EACA;IAAS,IAAI,CAAC3f,IAAI,YAAA4f,mCAAA1f,iBAAA;MAAA,YAAAA,iBAAA,IAA+Fwe,0BAA0B,EAvqFpCnnB,EAAE,CAAA4I,QAAA,CAuqFoD7I,QAAQ,GAvqF9DC,EAAE,CAAA4I,QAAA,CAuqFyEtH,IAAI,CAACmM,QAAQ;IAAA,CAA6C;EAAE;EAC9O;IAAS,IAAI,CAAC5E,KAAK,kBAxqFoF7I,EAAE,CAAA8I,kBAAA;MAAAC,KAAA,EAwqFYoe,0BAA0B;MAAAne,OAAA,EAA1Bme,0BAA0B,CAAA1e,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5K;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KA1qF2G1F,EAAE,CAAAkJ,iBAAA,CA0qFXie,0BAA0B,EAAc,CAAC;IAC/Hhe,IAAI,EAAElJ,UAAU;IAChBmJ,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEjJ,MAAM;MACZkJ,IAAI,EAAE,CAACrJ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoJ,IAAI,EAAE7H,IAAI,CAACmM;EAAS,CAAC,CAAC;AAAA;;AAE9C;AACA;AACA;;AAEA,SAASjL,mBAAmB,EAAEghB,mBAAmB,EAAET,gBAAgB,EAAEhe,mBAAmB,EAAE4F,8BAA8B,EAAEV,sBAAsB,EAAEqK,iCAAiC,EAAE6S,0BAA0B,EAAErH,sBAAsB,EAAEpZ,kBAAkB,EAAE8a,OAAO,EAAEjY,aAAa,EAAE0E,gBAAgB,EAAEtC,yBAAyB,EAAE+a,aAAa,EAAEja,6BAA6B,EAAE0U,sBAAsB,EAAErS,UAAU,EAAElH,wBAAwB,EAAEgY,oCAAoC,EAAED,iCAAiC,EAAExX,qBAAqB,EAAEuC,mBAAmB,EAAEO,0BAA0B,EAAEH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}