{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nfunction StandardHousePlanComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r2.label, \" \");\n  }\n}\nfunction StandardHousePlanComponent_tr_29_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i_r4.CHouseHold, \" \\u3001 \");\n  }\n}\nfunction StandardHousePlanComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, StandardHousePlanComponent_tr_29_span_4_Template, 2, 1, \"span\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CFileName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r5.CHouse);\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ref_r8 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.submitEditHouseRegularPic(ref_r8));\n    });\n    i0.ɵɵtext(1, \"\\u4E0A\\u50B3\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_div_6_label_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 32)(1, \"nb-checkbox\", 34);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      i0.ɵɵtwoWayBindingSet(i_r13.CIsSelect, $event) || (i_r13.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const idx_r14 = i0.ɵɵnextContext().index;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.checkItem($event, idx_r14, i_r13));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", i_r13.CIsSelect);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r13.CHouseHold || \"null\");\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"label\", 30);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\")(5, \"label\", 31);\n    i0.ɵɵtext(6, \" \\u9069\\u7528\\u6236\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 32)(8, \"span\", 33);\n    i0.ɵɵtext(9, \"\\u9069\\u7528\\u6236\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-checkbox\", 34);\n    i0.ɵɵlistener(\"checkedChange\", function StandardHousePlanComponent_ng_template_32_div_6_Template_nb_checkbox_checkedChange_10_listener($event) {\n      const houseRegularPic_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.checkAll($event, houseRegularPic_r11));\n    });\n    i0.ɵɵtext(11, \"\\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 35);\n    i0.ɵɵtemplate(13, StandardHousePlanComponent_ng_template_32_div_6_label_13_Template, 4, 2, \"label\", 36);\n    i0.ɵɵelementStart(14, \"div\", 37)(15, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_div_6_Template_button_click_15_listener() {\n      const houseRegularPic_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onDeleteHouseRegularPic(houseRegularPic_r11));\n    });\n    i0.ɵɵtext(16, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const houseRegularPic_r11 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", houseRegularPic_r11.CFileName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"checked\", ctx_r8.isAllChecked(houseRegularPic_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", houseRegularPic_r11.CHouse);\n  }\n}\nfunction StandardHousePlanComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 21)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406\\u300B\\u8A2D\\u5B9A\\u6236\\u578B\\u6A19\\u6E96\\u5716\\u300B\\u4FEE\\u6539 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 22)(4, \"div\", 23);\n    i0.ɵɵtemplate(5, StandardHousePlanComponent_ng_template_32_button_5_Template, 2, 0, \"button\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, StandardHousePlanComponent_ng_template_32_div_6_Template, 17, 3, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-card-footer\", 18)(8, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_Template_button_click_8_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onClose(ref_r8));\n    });\n    i0.ɵɵtext(9, \"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_ng_template_32_Template_button_click_10_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r6).dialogRef;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onClose(ref_r8));\n    });\n    i0.ɵɵtext(11, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.listHouseRegularPic.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.listHouseRegularPic);\n  }\n}\nexport class StandardHousePlanComponent extends BaseComponent {\n  constructor(_allow, dialogService, _houseService, route, message) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        this.selectedBuilding = this.buildingSelectedOptions[0];\n        this.getListHouseRegularPic();\n      }\n    });\n  }\n  getListHouseRegularPic() {\n    let param = {\n      CBuildCaseID: this.buildCaseId,\n      CBuildingName: this.selectedBuilding.value,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (!this.selectedBuilding.value) {\n      delete param.CBuildingName;\n    }\n    this._houseService.apiHouseGetListHouseRegularPicPost$Json({\n      body: param\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listHouseRegularPic = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  checkAll(checked, houseRegularPic) {\n    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\n      houseRegularPic.CHouse.forEach(item => item.CIsSelect = checked);\n    }\n    if (checked) {\n      this.listHouseRegularPic.forEach((element, index) => {\n        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\n          if (element.CHouse && Array.isArray(element.CHouse)) {\n            element.CHouse.forEach((item, o) => {\n              item.CIsSelect = false;\n            });\n          }\n        }\n      });\n    }\n  }\n  checkItem(checked, idx, i) {\n    if (checked) {\n      this.listHouseRegularPic.forEach((element, index) => {\n        if (index !== idx) {\n          if (element.CHouse && Array.isArray(element.CHouse)) {\n            element.CHouse.forEach((item, o) => {\n              if (item.CHouseID === i.CHouseID) {\n                item.CIsSelect = false;\n              }\n            });\n          }\n        }\n      });\n    }\n  }\n  isAllChecked(houseRegularPic) {\n    return houseRegularPic.CHouse.every(item => item.CIsSelect);\n  }\n  extractSelectedHouses(data) {\n    const result = [];\n    for (const item of data) {\n      for (const house of item.CHouse) {\n        if (house.CIsSelect) {\n          result.push({\n            CHouseID: house.CHouseID,\n            CRegularPictureID: item.CRegularPictureID\n          });\n        }\n      }\n    }\n    return result;\n  }\n  submitEditHouseRegularPic(ref) {\n    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic);\n    this._houseService.apiHouseEditHouseRegularPicPost$Json({\n      body: {\n        CHousePic: bodyHouseRegularPic\n      }\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onDeleteHouseRegularPic(houseRegularPic) {\n    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\n      this._houseService.apiHouseDeleteRegularPicturePost$Json({\n        body: {\n          CRegularPictureID: houseRegularPic.CRegularPictureID\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getListHouseRegularPic();\n        }\n      });\n    }\n  }\n  clear() {\n    this.selectedBuilding = this.buildingSelectedOptions[0];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        this.getListBuilding();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListHouseRegularPic();\n  }\n  onOpen(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  static {\n    this.ɵfac = function StandardHousePlanComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StandardHousePlanComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StandardHousePlanComponent,\n      selectors: [[\"ngx-standard-house-plan\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 34,\n      vars: 6,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [2, \"width\", \"700px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"d-flex\", \"justify-end\"], [\"class\", \"btn btn-primary mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"bg-white p-4 rounded shadow m-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [1, \"bg-white\", \"p-4\", \"rounded\", \"shadow\", \"m-2\"], [1, \"mb-2\"], [\"for\", \"standard-drawing\", 1, \"block\", \"text-gray-700\", \"font-bold\", \"mb-2\"], [\"for\", \"applicable-models\", 1, \"block\", \"text-gray-700\", \"font-bold\", \"mb-2\"], [1, \"inline-flex\", \"items-center\", \"mr-4\"], [1, \"mr-2\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"flex\", \"flex-wrap\"], [\"class\", \"inline-flex items-center mr-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"text-right\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-dark\", \"py-2\", \"px-4\", \"btn-sm\", \"ml-6\", 3, \"click\"], [1, \"ml-2\"]],\n      template: function StandardHousePlanComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"div\", 5)(8, \"label\", 6);\n          i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function StandardHousePlanComponent_Template_nb_select_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(11, StandardHousePlanComponent_nb_option_11_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 4)(13, \"div\", 9)(14, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.clear());\n          });\n          i0.ɵɵtext(15, \" \\u6E05\\u9664 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getListHouseRegularPic());\n          });\n          i0.ɵɵtext(17, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function StandardHousePlanComponent_Template_button_click_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const dialog_r3 = i0.ɵɵreference(33);\n            return i0.ɵɵresetView(ctx.onOpen(dialog_r3));\n          });\n          i0.ɵɵtext(19, \" \\u68DF\\u5225 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"table\", 14)(22, \"thead\")(23, \"tr\", 15)(24, \"th\", 16);\n          i0.ɵɵtext(25, \"\\u6A94\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 16);\n          i0.ɵɵtext(27, \"\\u9069\\u7528\\u6236\\u578B \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"tbody\");\n          i0.ɵɵtemplate(29, StandardHousePlanComponent_tr_29_Template, 5, 2, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"nb-card-footer\", 18)(31, \"ngb-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"pageChange\", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(32, StandardHousePlanComponent_ng_template_32_Template, 12, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listHouseRegularPic);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzdGFuZGFyZC1ob3VzZS1wbGFuLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvc3RhbmRhcmQtaG91c2UtcGxhbi9zdGFuZGFyZC1ob3VzZS1wbGFuLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvTEFBb0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "building_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "i_r4", "CHouseHold", "ɵɵtemplate", "StandardHousePlanComponent_tr_29_span_4_Template", "ɵɵtextInterpolate", "item_r5", "CFileName", "CHouse", "ɵɵlistener", "StandardHousePlanComponent_ng_template_32_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ref_r8", "ɵɵnextContext", "dialogRef", "ctx_r8", "ɵɵresetView", "submitEditHouseRegularPic", "ɵɵtwoWayListener", "StandardHousePlanComponent_ng_template_32_div_6_label_13_Template_nb_checkbox_checkedChange_1_listener", "$event", "i_r13", "_r12", "$implicit", "ɵɵtwoWayBindingSet", "CIsSelect", "idx_r14", "index", "checkItem", "ɵɵtwoWayProperty", "StandardHousePlanComponent_ng_template_32_div_6_Template_nb_checkbox_checkedChange_10_listener", "houseRegularPic_r11", "_r10", "checkAll", "StandardHousePlanComponent_ng_template_32_div_6_label_13_Template", "StandardHousePlanComponent_ng_template_32_div_6_Template_button_click_15_listener", "onDeleteHouseRegularPic", "isAllChecked", "StandardHousePlanComponent_ng_template_32_button_5_Template", "StandardHousePlanComponent_ng_template_32_div_6_Template", "StandardHousePlanComponent_ng_template_32_Template_button_click_8_listener", "_r6", "onClose", "StandardHousePlanComponent_ng_template_32_Template_button_click_10_listener", "listHouseRegularPic", "length", "StandardHousePlanComponent", "constructor", "_allow", "dialogService", "_houseService", "route", "message", "buildingSelectedOptions", "value", "getListBuilding", "apiHouseGetListBuildingPost$Json", "body", "CBuildCaseID", "buildCaseId", "subscribe", "res", "Entries", "StatusCode", "map", "e", "selectedBuilding", "getListHouseRegularPic", "param", "CBuildingName", "PageIndex", "pageIndex", "PageSize", "pageSize", "apiHouseGetListHouseRegularPicPost$Json", "totalRecords", "TotalItems", "checked", "houseRegularPic", "for<PERSON>ach", "item", "element", "CRegularPictureID", "Array", "isArray", "o", "idx", "i", "CHouseID", "every", "extractSelectedHouses", "data", "result", "house", "push", "ref", "bodyHouseRegularPic", "apiHouseEditHouseRegularPicPost$Json", "CHousePic", "showSucessMSG", "close", "window", "confirm", "apiHouseDeleteRegularPicturePost$Json", "clear", "ngOnInit", "paramMap", "params", "idParam", "get", "id", "pageChanged", "newPage", "onOpen", "open", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "HouseService", "i4", "ActivatedRoute", "i5", "MessageService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "StandardHousePlanComponent_Template", "rf", "ctx", "ɵɵelement", "StandardHousePlanComponent_Template_nb_select_ngModelChange_10_listener", "_r1", "StandardHousePlanComponent_nb_option_11_Template", "StandardHousePlanComponent_Template_button_click_14_listener", "StandardHousePlanComponent_Template_button_click_16_listener", "StandardHousePlanComponent_Template_button_click_18_listener", "dialog_r3", "ɵɵreference", "StandardHousePlanComponent_tr_29_Template", "StandardHousePlanComponent_Template_ngb_pagination_pageChange_31_listener", "StandardHousePlanComponent_ng_template_32_Template", "ɵɵtemplateRefExtractor"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\standard-house-plan\\standard-house-plan.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\standard-house-plan\\standard-house-plan.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { EditHouseRegularPicture, GetListHouseRegularPicRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-standard-house-plan',\r\n  templateUrl: './standard-house-plan.component.html',\r\n  styleUrls: ['./standard-house-plan.component.scss'],\r\n})\r\n\r\nexport class StandardHousePlanComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n\r\n  selectedBuilding: any\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        this.selectedBuilding = this.buildingSelectedOptions[0]\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n\r\n  listHouseRegularPic: GetListHouseRegularPicRes[]\r\n\r\n\r\n  getListHouseRegularPic() {\r\n    let param = {\r\n      CBuildCaseID: this.buildCaseId,\r\n      CBuildingName: this.selectedBuilding.value,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (!this.selectedBuilding.value) {\r\n      delete param.CBuildingName\r\n    }\r\n    this._houseService.apiHouseGetListHouseRegularPicPost$Json({\r\n      body: param\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listHouseRegularPic = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems!\r\n      }\r\n    })\r\n  }\r\n\r\n  checkAll(checked: boolean, houseRegularPic: any) {\r\n    if (houseRegularPic.CHouse && houseRegularPic.CHouse.length > 0) {\r\n      houseRegularPic.CHouse.forEach((item: { CIsSelect: boolean; }) => (item.CIsSelect = checked));\r\n    }\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (element.CRegularPictureID !== houseRegularPic.CRegularPictureID) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              item.CIsSelect = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  checkItem(checked: boolean, idx: any, i: any) {\r\n    if (checked) {\r\n      this.listHouseRegularPic.forEach((element, index) => {\r\n        if (index !== idx) {\r\n          if (element.CHouse && Array.isArray(element.CHouse)) {\r\n            element.CHouse.forEach((item, o) => {\r\n              if (item.CHouseID === i.CHouseID) {\r\n                item.CIsSelect = false\r\n              }\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isAllChecked(houseRegularPic: any): boolean {\r\n    return houseRegularPic.CHouse.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n\r\n  extractSelectedHouses(data: any[]): EditHouseRegularPicture[] {\r\n    const result: EditHouseRegularPicture[] = [];\r\n    for (const item of data) {\r\n      for (const house of item.CHouse) {\r\n        if (house.CIsSelect) {\r\n          result.push({\r\n            CHouseID: house.CHouseID,\r\n            CRegularPictureID: item.CRegularPictureID\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n  submitEditHouseRegularPic(ref: any) {\r\n    let bodyHouseRegularPic = this.extractSelectedHouses(this.listHouseRegularPic)\r\n    this._houseService.apiHouseEditHouseRegularPicPost$Json( { body: {CHousePic : bodyHouseRegularPic}\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  onDeleteHouseRegularPic(houseRegularPic: any) {\r\n    if (window.confirm(`確定要刪除【項目${houseRegularPic.CFileName}】?`)) {\r\n    this._houseService.apiHouseDeleteRegularPicturePost$Json({body : { CRegularPictureID: houseRegularPic.CRegularPictureID}}).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListHouseRegularPic()\r\n      }\r\n    })\r\n  }\r\n  }\r\n\r\n  clear() {\r\n    this.selectedBuilding = this.buildingSelectedOptions[0]\r\n  }\r\n\r\n\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n      }\r\n    });\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListHouseRegularPic()\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n}\r\n", "<!-- 2.1.6 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"selectedBuilding\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"getListHouseRegularPic()\">\r\n            查詢\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"onOpen(dialog)\">\r\n            棟別\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">檔案</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶型 </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listHouseRegularPic ; let i = index\">\r\n            <td>{{ item.CFileName}}</td>\r\n            <td>\r\n            <span *ngFor=\"let i of item.CHouse\">\r\n              {{i.CHouseHold}} 、\r\n\r\n            </span>\r\n          </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:700px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理》設定戶型標準圖》修改\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"d-flex justify-end\">\r\n        <button class=\"btn btn-primary mx-2\" *ngIf=\"listHouseRegularPic.length\" (click)=\"submitEditHouseRegularPic(ref)\">上傳檔案</button>\r\n      </div>\r\n      <div class=\"bg-white p-4 rounded shadow m-2\" \r\n        *ngFor=\"let houseRegularPic of listHouseRegularPic; let idx = index\">\r\n        <div class=\"mb-2\">\r\n          <label for=\"standard-drawing\" class=\"block text-gray-700 font-bold mb-2\">\r\n            {{houseRegularPic.CFileName}}\r\n          </label>\r\n        </div>\r\n\r\n        <div>\r\n          <label for=\"applicable-models\" class=\"block text-gray-700 font-bold mb-2\">\r\n            適用戶型\r\n          </label>\r\n\r\n          <label class=\"inline-flex items-center mr-4\">\r\n            <span class=\"mr-2\">適用戶型 </span>\r\n            <nb-checkbox status=\"basic\" (checkedChange)=\"checkAll($event, houseRegularPic)\" [checked]=\"isAllChecked(houseRegularPic)\">全選\r\n            </nb-checkbox>\r\n          </label>\r\n          <div class=\"flex flex-wrap\">\r\n            <label class=\"inline-flex items-center mr-4\" *ngFor=\"let i of houseRegularPic.CHouse\">\r\n              <nb-checkbox status=\"basic\" [(checked)]=\"i.CIsSelect\" (checkedChange)=\"checkItem($event, idx, i)\">\r\n              </nb-checkbox>\r\n              <span class=\"ml-2\">{{i.CHouseHold || 'null'}}</span>\r\n            </label>\r\n            <div class=\"w-full text-right\">\r\n              <button type=\"button\" class=\"btn btn-outline-dark py-2 px-4 btn-sm ml-6 \" (click)=\"onDeleteHouseRegularPic(houseRegularPic)\">\r\n                刪除\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onClose(ref)\">返回上一頁</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onClose(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAIA,SAASA,aAAa,QAAQ,qCAAqC;;;;;;;;;ICQvDC,EAAA,CAAAC,cAAA,oBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAC5EL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,WAAA,CAAAG,KAAA,MACF;;;;;IAgCAR,EAAA,CAAAC,cAAA,WAAoC;IAClCD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFLH,EAAA,CAAAM,SAAA,EAEF;IAFEN,EAAA,CAAAO,kBAAA,MAAAE,IAAA,CAAAC,UAAA,aAEF;;;;;IALAV,EADF,CAAAC,cAAA,SAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IACJD,EAAA,CAAAW,UAAA,IAAAC,gDAAA,mBAAoC;IAKtCZ,EADA,CAAAG,YAAA,EAAK,EACA;;;;IAPCH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAa,iBAAA,CAAAC,OAAA,CAAAC,SAAA,CAAmB;IAEHf,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAU,OAAA,CAAAE,MAAA,CAAc;;;;;;IAyBtChB,EAAA,CAAAC,cAAA,iBAAiH;IAAzCD,EAAA,CAAAiB,UAAA,mBAAAC,oFAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASD,MAAA,CAAAE,yBAAA,CAAAL,MAAA,CAA8B;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAsBxHH,EADF,CAAAC,cAAA,gBAAsF,sBACc;IAAtED,EAAA,CAAA2B,gBAAA,2BAAAC,uGAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAY,IAAA,EAAAC,SAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAAH,KAAA,CAAAI,SAAA,EAAAL,MAAA,MAAAC,KAAA,CAAAI,SAAA,GAAAL,MAAA;MAAA,OAAA7B,EAAA,CAAAyB,WAAA,CAAAI,MAAA;IAAA,EAAyB;IAAC7B,EAAA,CAAAiB,UAAA,2BAAAW,uGAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA9B,EAAA,CAAAmB,aAAA,CAAAY,IAAA,EAAAC,SAAA;MAAA,MAAAG,OAAA,GAAAnC,EAAA,CAAAsB,aAAA,GAAAc,KAAA;MAAA,MAAAZ,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBD,MAAA,CAAAa,SAAA,CAAAR,MAAA,EAAAM,OAAA,EAAAL,KAAA,CAAyB;IAAA,EAAC;IACjG9B,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAC9C;;;;IAHsBH,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAsC,gBAAA,YAAAR,KAAA,CAAAI,SAAA,CAAyB;IAElClC,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAa,iBAAA,CAAAiB,KAAA,CAAApB,UAAA,WAA0B;;;;;;IAnBjDV,EAHJ,CAAAC,cAAA,cACuE,cACnD,gBACyD;IACvED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;IAGJH,EADF,CAAAC,cAAA,UAAK,gBACuE;IACxED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAGNH,EADF,CAAAC,cAAA,gBAA6C,eACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,uBAA0H;IAA9FD,EAAA,CAAAiB,UAAA,2BAAAsB,+FAAAV,MAAA;MAAA,MAAAW,mBAAA,GAAAxC,EAAA,CAAAmB,aAAA,CAAAsB,IAAA,EAAAT,SAAA;MAAA,MAAAR,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBD,MAAA,CAAAkB,QAAA,CAAAb,MAAA,EAAAW,mBAAA,CAAiC;IAAA,EAAC;IAA2CxC,EAAA,CAAAE,MAAA,qBAC1H;IACFF,EADE,CAAAG,YAAA,EAAc,EACR;IACRH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAW,UAAA,KAAAgC,iEAAA,oBAAsF;IAMpF3C,EADF,CAAAC,cAAA,eAA+B,kBACgG;IAAnDD,EAAA,CAAAiB,UAAA,mBAAA2B,kFAAA;MAAA,MAAAJ,mBAAA,GAAAxC,EAAA,CAAAmB,aAAA,CAAAsB,IAAA,EAAAT,SAAA;MAAA,MAAAR,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASD,MAAA,CAAAqB,uBAAA,CAAAL,mBAAA,CAAwC;IAAA,EAAC;IAC1HxC,EAAA,CAAAE,MAAA,sBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;;IA3BAH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAiC,mBAAA,CAAAzB,SAAA,MACF;IAUkFf,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAI,UAAA,YAAAoB,MAAA,CAAAsB,YAAA,CAAAN,mBAAA,EAAyC;IAI9DxC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,YAAAoC,mBAAA,CAAAxB,MAAA,CAAyB;;;;;;IA1B5FhB,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,mGACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEfH,EADF,CAAAC,cAAA,uBAA2B,cACO;IAC9BD,EAAA,CAAAW,UAAA,IAAAoC,2DAAA,qBAAiH;IACnH/C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,IAAAqC,wDAAA,mBACuE;IA+BzEhD,EAAA,CAAAG,YAAA,EAAe;IAEbH,EADF,CAAAC,cAAA,yBAAsD,iBACe;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAgC,2EAAA;MAAA,MAAA5B,MAAA,GAAArB,EAAA,CAAAmB,aAAA,CAAA+B,GAAA,EAAA3B,SAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASD,MAAA,CAAA2B,OAAA,CAAA9B,MAAA,CAAY;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjFH,EAAA,CAAAC,cAAA,kBAAmE;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAmC,4EAAA;MAAA,MAAA/B,MAAA,GAAArB,EAAA,CAAAmB,aAAA,CAAA+B,GAAA,EAAA3B,SAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASD,MAAA,CAAA2B,OAAA,CAAA9B,MAAA,CAAY;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,oBAAE;IAEzEF,EAFyE,CAAAG,YAAA,EAAS,EAC/D,EACT;;;;IAvCkCH,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAoB,MAAA,CAAA6B,mBAAA,CAAAC,MAAA,CAAgC;IAG1CtD,EAAA,CAAAM,SAAA,EAAwB;IAAxBN,EAAA,CAAAI,UAAA,YAAAoB,MAAA,CAAA6B,mBAAA,CAAwB;;;ADnD5D,OAAM,MAAOE,0BAA2B,SAAQxD,aAAa;EAC3DyD,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,aAA2B,EAC3BC,KAAqB,EACrBC,OAAuB;IAE/B,KAAK,CAACJ,MAAM,CAAC;IANL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IAOjB,KAAAC,uBAAuB,GAAU,CAC/B;MACEC,KAAK,EAAE,EAAE;MAAEvD,KAAK,EAAE;KACnB,CACF;EARD;EAUAwD,eAAeA,CAAA;IACb,IAAI,CAACL,aAAa,CAACM,gCAAgC,CAAC;MAClDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACV,uBAAuB,GAAG,CAAC;UAC9BC,KAAK,EAAE,EAAE;UAAEvD,KAAK,EAAE;SACnB,EAAE,GAAG8D,GAAG,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEX,KAAK,EAAEW,CAAC;YAAElE,KAAK,EAAEkE;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACb,uBAAuB,CAAC,CAAC,CAAC;QACvD,IAAI,CAACc,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC;EACJ;EAKAA,sBAAsBA,CAAA;IACpB,IAAIC,KAAK,GAAG;MACVV,YAAY,EAAE,IAAI,CAACC,WAAW;MAC9BU,aAAa,EAAE,IAAI,CAACH,gBAAgB,CAACZ,KAAK;MAC1CgB,SAAS,EAAE,IAAI,CAACC,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACC;KAChB;IACD,IAAI,CAAC,IAAI,CAACP,gBAAgB,CAACZ,KAAK,EAAE;MAChC,OAAOc,KAAK,CAACC,aAAa;IAC5B;IACA,IAAI,CAACnB,aAAa,CAACwB,uCAAuC,CAAC;MACzDjB,IAAI,EAAEW;KACP,CAAC,CAACR,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnB,mBAAmB,GAAGiB,GAAG,CAACC,OAAQ,IAAI,EAAE;QAC7C,IAAI,CAACa,YAAY,GAAGd,GAAG,CAACe,UAAW;MACrC;IACF,CAAC,CAAC;EACJ;EAEA3C,QAAQA,CAAC4C,OAAgB,EAAEC,eAAoB;IAC7C,IAAIA,eAAe,CAACvE,MAAM,IAAIuE,eAAe,CAACvE,MAAM,CAACsC,MAAM,GAAG,CAAC,EAAE;MAC/DiC,eAAe,CAACvE,MAAM,CAACwE,OAAO,CAAEC,IAA6B,IAAMA,IAAI,CAACvD,SAAS,GAAGoD,OAAQ,CAAC;IAC/F;IACA,IAAIA,OAAO,EAAE;MACX,IAAI,CAACjC,mBAAmB,CAACmC,OAAO,CAAC,CAACE,OAAO,EAAEtD,KAAK,KAAI;QAClD,IAAIsD,OAAO,CAACC,iBAAiB,KAAKJ,eAAe,CAACI,iBAAiB,EAAE;UACnE,IAAID,OAAO,CAAC1E,MAAM,IAAI4E,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC1E,MAAM,CAAC,EAAE;YACnD0E,OAAO,CAAC1E,MAAM,CAACwE,OAAO,CAAC,CAACC,IAAI,EAAEK,CAAC,KAAI;cACjCL,IAAI,CAACvD,SAAS,GAAG,KAAK;YACxB,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAG,SAASA,CAACiD,OAAgB,EAAES,GAAQ,EAAEC,CAAM;IAC1C,IAAIV,OAAO,EAAE;MACX,IAAI,CAACjC,mBAAmB,CAACmC,OAAO,CAAC,CAACE,OAAO,EAAEtD,KAAK,KAAI;QAClD,IAAIA,KAAK,KAAK2D,GAAG,EAAE;UACjB,IAAIL,OAAO,CAAC1E,MAAM,IAAI4E,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC1E,MAAM,CAAC,EAAE;YACnD0E,OAAO,CAAC1E,MAAM,CAACwE,OAAO,CAAC,CAACC,IAAI,EAAEK,CAAC,KAAI;cACjC,IAAIL,IAAI,CAACQ,QAAQ,KAAKD,CAAC,CAACC,QAAQ,EAAE;gBAChCR,IAAI,CAACvD,SAAS,GAAG,KAAK;cACxB;YACF,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAY,YAAYA,CAACyC,eAAoB;IAC/B,OAAOA,eAAe,CAACvE,MAAM,CAACkF,KAAK,CAAET,IAAyB,IAAKA,IAAI,CAACvD,SAAS,CAAC;EACpF;EAGAiE,qBAAqBA,CAACC,IAAW;IAC/B,MAAMC,MAAM,GAA8B,EAAE;IAC5C,KAAK,MAAMZ,IAAI,IAAIW,IAAI,EAAE;MACvB,KAAK,MAAME,KAAK,IAAIb,IAAI,CAACzE,MAAM,EAAE;QAC/B,IAAIsF,KAAK,CAACpE,SAAS,EAAE;UACnBmE,MAAM,CAACE,IAAI,CAAC;YACVN,QAAQ,EAAEK,KAAK,CAACL,QAAQ;YACxBN,iBAAiB,EAAEF,IAAI,CAACE;WACzB,CAAC;QACJ;MACF;IACF;IACA,OAAOU,MAAM;EACf;EAEA3E,yBAAyBA,CAAC8E,GAAQ;IAChC,IAAIC,mBAAmB,GAAG,IAAI,CAACN,qBAAqB,CAAC,IAAI,CAAC9C,mBAAmB,CAAC;IAC9E,IAAI,CAACM,aAAa,CAAC+C,oCAAoC,CAAE;MAAExC,IAAI,EAAE;QAACyC,SAAS,EAAGF;MAAmB;KAChG,CAAC,CAACpC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACX,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;QAClCJ,GAAG,CAACK,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAhE,uBAAuBA,CAAC0C,eAAoB;IAC1C,IAAIuB,MAAM,CAACC,OAAO,CAAC,WAAWxB,eAAe,CAACxE,SAAS,IAAI,CAAC,EAAE;MAC9D,IAAI,CAAC4C,aAAa,CAACqD,qCAAqC,CAAC;QAAC9C,IAAI,EAAG;UAAEyB,iBAAiB,EAAEJ,eAAe,CAACI;QAAiB;MAAC,CAAC,CAAC,CAACtB,SAAS,CAACC,GAAG,IAAG;QACzI,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACX,OAAO,CAAC+C,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAChC,sBAAsB,EAAE;QAC/B;MACF,CAAC,CAAC;IACJ;EACA;EAEAqC,KAAKA,CAAA;IACH,IAAI,CAACtC,gBAAgB,GAAG,IAAI,CAACb,uBAAuB,CAAC,CAAC,CAAC;EACzD;EAKSoD,QAAQA,CAAA;IACf,IAAI,CAACtD,KAAK,CAACuD,QAAQ,CAAC9C,SAAS,CAAC+C,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACjD,WAAW,GAAGmD,EAAE;QACrB,IAAI,CAACvD,eAAe,EAAE;MACxB;IACF,CAAC,CAAC;EACJ;EAEAwD,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACzC,SAAS,GAAGyC,OAAO;IACxB,IAAI,CAAC7C,sBAAsB,EAAE;EAC/B;EAGA8C,MAAMA,CAAClB,GAAQ;IACb,IAAI,CAAC9C,aAAa,CAACiE,IAAI,CAACnB,GAAG,CAAC;EAC9B;EAEArD,OAAOA,CAACqD,GAAQ;IACdA,GAAG,CAACK,KAAK,EAAE;EACb;;;uCArKWtD,0BAA0B,EAAAvD,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAhI,EAAA,CAAA4H,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAlI,EAAA,CAAA4H,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAApI,EAAA,CAAA4H,iBAAA,CAAAS,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAA1B/E,0BAA0B;MAAAgF,SAAA;MAAAC,QAAA,GAAAxI,EAAA,CAAAyI,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrBrC/I,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAiJ,SAAA,qBAAiC;UACnCjJ,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAiJ,SAAA,YAA0C;UAIpCjJ,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBAA0E;UAA9CD,EAAA,CAAA2B,gBAAA,2BAAAuH,wEAAArH,MAAA;YAAA7B,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAAnJ,EAAA,CAAAiC,kBAAA,CAAA+G,GAAA,CAAArE,gBAAA,EAAA9C,MAAA,MAAAmH,GAAA,CAAArE,gBAAA,GAAA9C,MAAA;YAAA,OAAA7B,EAAA,CAAAyB,WAAA,CAAAI,MAAA;UAAA,EAA8B;UACxD7B,EAAA,CAAAW,UAAA,KAAAyI,gDAAA,uBAA+E;UAMrFpJ,EAHI,CAAAG,YAAA,EAAY,EACR,EAEF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC2B,kBACO;UAAlBD,EAAA,CAAAiB,UAAA,mBAAAoI,6DAAA;YAAArJ,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAA,OAAAnJ,EAAA,CAAAyB,WAAA,CAASuH,GAAA,CAAA/B,KAAA,EAAO;UAAA,EAAC;UACjDjH,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA0E;UAAnCD,EAAA,CAAAiB,UAAA,mBAAAqI,6DAAA;YAAAtJ,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAA,OAAAnJ,EAAA,CAAAyB,WAAA,CAASuH,GAAA,CAAApE,sBAAA,EAAwB;UAAA,EAAC;UACvE5E,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsD;UAAzBD,EAAA,CAAAiB,UAAA,mBAAAsI,6DAAA;YAAAvJ,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAA,MAAAK,SAAA,GAAAxJ,EAAA,CAAAyJ,WAAA;YAAA,OAAAzJ,EAAA,CAAAyB,WAAA,CAASuH,GAAA,CAAAtB,MAAA,CAAA8B,SAAA,CAAc;UAAA,EAAC;UACnDxJ,EAAA,CAAAE,MAAA,sBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAW,UAAA,KAAA+I,yCAAA,iBAA6D;UAYrE1J,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAA2B,gBAAA,wBAAAgI,0EAAA9H,MAAA;YAAA7B,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAAnJ,EAAA,CAAAiC,kBAAA,CAAA+G,GAAA,CAAAhE,SAAA,EAAAnD,MAAA,MAAAmH,GAAA,CAAAhE,SAAA,GAAAnD,MAAA;YAAA,OAAA7B,EAAA,CAAAyB,WAAA,CAAAI,MAAA;UAAA,EAAoB;UAClC7B,EAAA,CAAAiB,UAAA,wBAAA0I,0EAAA9H,MAAA;YAAA7B,EAAA,CAAAmB,aAAA,CAAAgI,GAAA;YAAA,OAAAnJ,EAAA,CAAAyB,WAAA,CAAcuH,GAAA,CAAAxB,WAAA,CAAA3F,MAAA,CAAmB;UAAA,EAAC;UAGxC7B,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAGVH,EAAA,CAAAW,UAAA,KAAAiJ,kDAAA,iCAAA5J,EAAA,CAAA6J,sBAAA,CAAoD;;;UArDd7J,EAAA,CAAAM,SAAA,IAA8B;UAA9BN,EAAA,CAAAsC,gBAAA,YAAA0G,GAAA,CAAArE,gBAAA,CAA8B;UACxB3E,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAAlF,uBAAA,CAA0B;UA+BvC9D,EAAA,CAAAM,SAAA,IAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA4I,GAAA,CAAA3F,mBAAA,CAAyB;UAcpCrD,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAsC,gBAAA,SAAA0G,GAAA,CAAAhE,SAAA,CAAoB;UAAuBhF,EAAtB,CAAAI,UAAA,aAAA4I,GAAA,CAAA9D,QAAA,CAAqB,mBAAA8D,GAAA,CAAA5D,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}