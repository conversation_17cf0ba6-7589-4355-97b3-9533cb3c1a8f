{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var C_enc = C.enc;\n    var Utf8 = C_enc.Utf8;\n    var C_algo = C.algo;\n\n    /**\n     * HMAC algorithm.\n     */\n    var HMAC = C_algo.HMAC = Base.extend({\n      /**\n       * Initializes a newly created HMAC.\n       *\n       * @param {Hasher} hasher The hash algorithm to use.\n       * @param {WordArray|string} key The secret key.\n       *\n       * @example\n       *\n       *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);\n       */\n      init: function (hasher, key) {\n        // Init hasher\n        hasher = this._hasher = new hasher.init();\n\n        // Convert string to WordArray, else assume WordArray already\n        if (typeof key == 'string') {\n          key = Utf8.parse(key);\n        }\n\n        // Shortcuts\n        var hasherBlockSize = hasher.blockSize;\n        var hasherBlockSizeBytes = hasherBlockSize * 4;\n\n        // Allow arbitrary length keys\n        if (key.sigBytes > hasherBlockSizeBytes) {\n          key = hasher.finalize(key);\n        }\n\n        // Clamp excess bits\n        key.clamp();\n\n        // Clone key for inner and outer pads\n        var oKey = this._oKey = key.clone();\n        var iKey = this._iKey = key.clone();\n\n        // Shortcuts\n        var oKeyWords = oKey.words;\n        var iKeyWords = iKey.words;\n\n        // XOR keys with pad constants\n        for (var i = 0; i < hasherBlockSize; i++) {\n          oKeyWords[i] ^= 0x5c5c5c5c;\n          iKeyWords[i] ^= 0x36363636;\n        }\n        oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes;\n\n        // Set initial values\n        this.reset();\n      },\n      /**\n       * Resets this HMAC to its initial state.\n       *\n       * @example\n       *\n       *     hmacHasher.reset();\n       */\n      reset: function () {\n        // Shortcut\n        var hasher = this._hasher;\n\n        // Reset\n        hasher.reset();\n        hasher.update(this._iKey);\n      },\n      /**\n       * Updates this HMAC with a message.\n       *\n       * @param {WordArray|string} messageUpdate The message to append.\n       *\n       * @return {HMAC} This HMAC instance.\n       *\n       * @example\n       *\n       *     hmacHasher.update('message');\n       *     hmacHasher.update(wordArray);\n       */\n      update: function (messageUpdate) {\n        this._hasher.update(messageUpdate);\n\n        // Chainable\n        return this;\n      },\n      /**\n       * Finalizes the HMAC computation.\n       * Note that the finalize operation is effectively a destructive, read-once operation.\n       *\n       * @param {WordArray|string} messageUpdate (Optional) A final message update.\n       *\n       * @return {WordArray} The HMAC.\n       *\n       * @example\n       *\n       *     var hmac = hmacHasher.finalize();\n       *     var hmac = hmacHasher.finalize('message');\n       *     var hmac = hmacHasher.finalize(wordArray);\n       */\n      finalize: function (messageUpdate) {\n        // Shortcut\n        var hasher = this._hasher;\n\n        // Compute HMAC\n        var innerHash = hasher.finalize(messageUpdate);\n        hasher.reset();\n        var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));\n        return hmac;\n      }\n    });\n  })();\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_lib", "lib", "Base", "C_enc", "enc", "Utf8", "C_algo", "algo", "HMAC", "extend", "init", "hasher", "key", "_hasher", "parse", "hasherBlockSize", "blockSize", "hasherBlockSizeBytes", "sigBytes", "finalize", "clamp", "o<PERSON><PERSON>", "_o<PERSON>ey", "clone", "i<PERSON>ey", "_i<PERSON><PERSON>", "oKeyWords", "words", "iKeyWords", "i", "reset", "update", "messageUpdate", "innerHash", "hmac", "concat"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/crypto-js/hmac.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var C_enc = C.enc;\n\t    var Utf8 = C_enc.Utf8;\n\t    var C_algo = C.algo;\n\n\t    /**\n\t     * HMAC algorithm.\n\t     */\n\t    var HMAC = C_algo.HMAC = Base.extend({\n\t        /**\n\t         * Initializes a newly created HMAC.\n\t         *\n\t         * @param {Hasher} hasher The hash algorithm to use.\n\t         * @param {WordArray|string} key The secret key.\n\t         *\n\t         * @example\n\t         *\n\t         *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);\n\t         */\n\t        init: function (hasher, key) {\n\t            // Init hasher\n\t            hasher = this._hasher = new hasher.init();\n\n\t            // Convert string to WordArray, else assume WordArray already\n\t            if (typeof key == 'string') {\n\t                key = Utf8.parse(key);\n\t            }\n\n\t            // Shortcuts\n\t            var hasherBlockSize = hasher.blockSize;\n\t            var hasherBlockSizeBytes = hasherBlockSize * 4;\n\n\t            // Allow arbitrary length keys\n\t            if (key.sigBytes > hasherBlockSizeBytes) {\n\t                key = hasher.finalize(key);\n\t            }\n\n\t            // Clamp excess bits\n\t            key.clamp();\n\n\t            // Clone key for inner and outer pads\n\t            var oKey = this._oKey = key.clone();\n\t            var iKey = this._iKey = key.clone();\n\n\t            // Shortcuts\n\t            var oKeyWords = oKey.words;\n\t            var iKeyWords = iKey.words;\n\n\t            // XOR keys with pad constants\n\t            for (var i = 0; i < hasherBlockSize; i++) {\n\t                oKeyWords[i] ^= 0x5c5c5c5c;\n\t                iKeyWords[i] ^= 0x36363636;\n\t            }\n\t            oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes;\n\n\t            // Set initial values\n\t            this.reset();\n\t        },\n\n\t        /**\n\t         * Resets this HMAC to its initial state.\n\t         *\n\t         * @example\n\t         *\n\t         *     hmacHasher.reset();\n\t         */\n\t        reset: function () {\n\t            // Shortcut\n\t            var hasher = this._hasher;\n\n\t            // Reset\n\t            hasher.reset();\n\t            hasher.update(this._iKey);\n\t        },\n\n\t        /**\n\t         * Updates this HMAC with a message.\n\t         *\n\t         * @param {WordArray|string} messageUpdate The message to append.\n\t         *\n\t         * @return {HMAC} This HMAC instance.\n\t         *\n\t         * @example\n\t         *\n\t         *     hmacHasher.update('message');\n\t         *     hmacHasher.update(wordArray);\n\t         */\n\t        update: function (messageUpdate) {\n\t            this._hasher.update(messageUpdate);\n\n\t            // Chainable\n\t            return this;\n\t        },\n\n\t        /**\n\t         * Finalizes the HMAC computation.\n\t         * Note that the finalize operation is effectively a destructive, read-once operation.\n\t         *\n\t         * @param {WordArray|string} messageUpdate (Optional) A final message update.\n\t         *\n\t         * @return {WordArray} The HMAC.\n\t         *\n\t         * @example\n\t         *\n\t         *     var hmac = hmacHasher.finalize();\n\t         *     var hmac = hmacHasher.finalize('message');\n\t         *     var hmac = hmacHasher.finalize(wordArray);\n\t         */\n\t        finalize: function (messageUpdate) {\n\t            // Shortcut\n\t            var hasher = this._hasher;\n\n\t            // Compute HMAC\n\t            var innerHash = hasher.finalize(messageUpdate);\n\t            hasher.reset();\n\t            var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));\n\n\t            return hmac;\n\t        }\n\t    });\n\t}());\n\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAE;EAC1B,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACtD,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAEJ,OAAO,CAAC;EAC5B,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,KAAK,GAAGJ,CAAC,CAACK,GAAG;IACjB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,MAAM,GAAGP,CAAC,CAACQ,IAAI;;IAEnB;AACL;AACA;IACK,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAI,GAAGN,IAAI,CAACO,MAAM,CAAC;MACjC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,IAAI,EAAE,SAAAA,CAAUC,MAAM,EAAEC,GAAG,EAAE;QACzB;QACAD,MAAM,GAAG,IAAI,CAACE,OAAO,GAAG,IAAIF,MAAM,CAACD,IAAI,CAAC,CAAC;;QAEzC;QACA,IAAI,OAAOE,GAAG,IAAI,QAAQ,EAAE;UACxBA,GAAG,GAAGP,IAAI,CAACS,KAAK,CAACF,GAAG,CAAC;QACzB;;QAEA;QACA,IAAIG,eAAe,GAAGJ,MAAM,CAACK,SAAS;QACtC,IAAIC,oBAAoB,GAAGF,eAAe,GAAG,CAAC;;QAE9C;QACA,IAAIH,GAAG,CAACM,QAAQ,GAAGD,oBAAoB,EAAE;UACrCL,GAAG,GAAGD,MAAM,CAACQ,QAAQ,CAACP,GAAG,CAAC;QAC9B;;QAEA;QACAA,GAAG,CAACQ,KAAK,CAAC,CAAC;;QAEX;QACA,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK,GAAGV,GAAG,CAACW,KAAK,CAAC,CAAC;QACnC,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK,GAAGb,GAAG,CAACW,KAAK,CAAC,CAAC;;QAEnC;QACA,IAAIG,SAAS,GAAGL,IAAI,CAACM,KAAK;QAC1B,IAAIC,SAAS,GAAGJ,IAAI,CAACG,KAAK;;QAE1B;QACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,eAAe,EAAEc,CAAC,EAAE,EAAE;UACtCH,SAAS,CAACG,CAAC,CAAC,IAAI,UAAU;UAC1BD,SAAS,CAACC,CAAC,CAAC,IAAI,UAAU;QAC9B;QACAR,IAAI,CAACH,QAAQ,GAAGM,IAAI,CAACN,QAAQ,GAAGD,oBAAoB;;QAEpD;QACA,IAAI,CAACa,KAAK,CAAC,CAAC;MAChB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;MACSA,KAAK,EAAE,SAAAA,CAAA,EAAY;QACf;QACA,IAAInB,MAAM,GAAG,IAAI,CAACE,OAAO;;QAEzB;QACAF,MAAM,CAACmB,KAAK,CAAC,CAAC;QACdnB,MAAM,CAACoB,MAAM,CAAC,IAAI,CAACN,KAAK,CAAC;MAC7B,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSM,MAAM,EAAE,SAAAA,CAAUC,aAAa,EAAE;QAC7B,IAAI,CAACnB,OAAO,CAACkB,MAAM,CAACC,aAAa,CAAC;;QAElC;QACA,OAAO,IAAI;MACf,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSb,QAAQ,EAAE,SAAAA,CAAUa,aAAa,EAAE;QAC/B;QACA,IAAIrB,MAAM,GAAG,IAAI,CAACE,OAAO;;QAEzB;QACA,IAAIoB,SAAS,GAAGtB,MAAM,CAACQ,QAAQ,CAACa,aAAa,CAAC;QAC9CrB,MAAM,CAACmB,KAAK,CAAC,CAAC;QACd,IAAII,IAAI,GAAGvB,MAAM,CAACQ,QAAQ,CAAC,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC,CAAC,CAACY,MAAM,CAACF,SAAS,CAAC,CAAC;QAEhE,OAAOC,IAAI;MACf;IACJ,CAAC,CAAC;EACN,CAAC,EAAC,CAAC;AAGJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}