{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularNoticeFileSaveRegularNoticeFilePost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH, 'post');\n  if (params) {\n    rb.query('BuId', params.BuId, {});\n    rb.query('UserId', params.UserId, {});\n    rb.query('UserCode', params.UserCode, {});\n    rb.query('UserName', params.UserName, {});\n    rb.query('CUserType', params.CUserType, {});\n    rb.query('CreateTime', params.CreateTime, {});\n    rb.query('ExpTime', params.ExpTime, {});\n    rb.query('LoginId', params.LoginId, {});\n    rb.query('IsLimit', params.IsLimit, {});\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH = '/api/RegularNoticeFile/SaveRegularNoticeFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularNoticeFileSaveRegularNoticeFilePost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "query", "BuId", "UserId", "UserCode", "UserName", "CUserType", "CreateTime", "ExpTime", "LoginId", "IsLimit", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\regular-notice-file\\api-regular-notice-file-save-regular-notice-file-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiRegularNoticeFileSaveRegularNoticeFilePost$Json$Params {\r\n  BuId?: string;\r\n  UserId?: string;\r\n  UserCode?: string;\r\n  UserName?: string;\r\n  CUserType?: number;\r\n  CreateTime?: string;\r\n  ExpTime?: string;\r\n  LoginId?: string;\r\n  IsLimit?: boolean;\r\n      body?: {\r\n'CNoticeType'?: number;\r\n'CBuildCaseId'?: number;\r\n'CFile'?: Blob;\r\n'CHouseHold'?: Array<string>;\r\n'CRegularNoticeFileId'?: number;\r\n'CIsSelectAll'?: boolean;\r\n}\r\n}\r\n\r\nexport function apiRegularNoticeFileSaveRegularNoticeFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiRegularNoticeFileSaveRegularNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH, 'post');\r\n  if (params) {\r\n    rb.query('BuId', params.BuId, {});\r\n    rb.query('UserId', params.UserId, {});\r\n    rb.query('UserCode', params.UserCode, {});\r\n    rb.query('UserName', params.UserName, {});\r\n    rb.query('CUserType', params.CUserType, {});\r\n    rb.query('CreateTime', params.CreateTime, {});\r\n    rb.query('ExpTime', params.ExpTime, {});\r\n    rb.query('LoginId', params.LoginId, {});\r\n    rb.query('IsLimit', params.IsLimit, {});\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularNoticeFileSaveRegularNoticeFilePost$Json.PATH = '/api/RegularNoticeFile/SaveRegularNoticeFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAwBtD,OAAM,SAAUC,kDAAkDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAkE,EAAEC,OAAqB;EAC7L,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,kDAAkD,CAACM,IAAI,EAAE,MAAM,CAAC;EACvG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,KAAK,CAAC,MAAM,EAAEJ,MAAM,CAACK,IAAI,EAAE,EAAE,CAAC;IACjCH,EAAE,CAACE,KAAK,CAAC,QAAQ,EAAEJ,MAAM,CAACM,MAAM,EAAE,EAAE,CAAC;IACrCJ,EAAE,CAACE,KAAK,CAAC,UAAU,EAAEJ,MAAM,CAACO,QAAQ,EAAE,EAAE,CAAC;IACzCL,EAAE,CAACE,KAAK,CAAC,UAAU,EAAEJ,MAAM,CAACQ,QAAQ,EAAE,EAAE,CAAC;IACzCN,EAAE,CAACE,KAAK,CAAC,WAAW,EAAEJ,MAAM,CAACS,SAAS,EAAE,EAAE,CAAC;IAC3CP,EAAE,CAACE,KAAK,CAAC,YAAY,EAAEJ,MAAM,CAACU,UAAU,EAAE,EAAE,CAAC;IAC7CR,EAAE,CAACE,KAAK,CAAC,SAAS,EAAEJ,MAAM,CAACW,OAAO,EAAE,EAAE,CAAC;IACvCT,EAAE,CAACE,KAAK,CAAC,SAAS,EAAEJ,MAAM,CAACY,OAAO,EAAE,EAAE,CAAC;IACvCV,EAAE,CAACE,KAAK,CAAC,SAAS,EAAEJ,MAAM,CAACa,OAAO,EAAE,EAAE,CAAC;IACvCX,EAAE,CAACY,IAAI,CAACd,MAAM,CAACc,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAOhB,IAAI,CAACiB,OAAO,CACjBb,EAAE,CAACc,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEjB;EAAO,CAAE,CAAC,CACjE,CAACkB,IAAI,CACJzB,MAAM,CAAE0B,CAAM,IAA6BA,CAAC,YAAY3B,YAAY,CAAC,EACrEE,GAAG,CAAEyB,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAvB,kDAAkD,CAACM,IAAI,GAAG,8CAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}