{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const houseId_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getHouseholdInfoById(houseId_r5).floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template, 2, 1, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener() {\n      const houseId_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onRemoveHousehold(houseId_r5));\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const houseId_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseholdInfoById(houseId_r5).houseName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.getHouseholdInfoById(houseId_r5).floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 7, 3, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r6, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getBuildingSelectedHouseIds(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasBuildingSelected(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.displayText.selectedPrefix, \" (\", ctx_r2.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 27);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r2.getSelectedCount() + \" \" + ctx_r2.displayText.selectedCount : ctx_r2.placeholder || ctx_r2.displayText.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"nb-icon\", 54);\n    i0.ɵɵelementStart(3, \"p\", 55);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener() {\n      const building_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onBuildingSelect(building_r9));\n    });\n    i0.ɵɵelementStart(1, \"span\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedBuilding === building_r9 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r2.selectedBuilding === building_r9 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBuildingCount(building_r9), \"\\u6236 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵelement(1, \"nb-icon\", 77);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵelement(1, \"nb-icon\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_15_button_5_Template, 2, 0, \"button\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.displayText.noResults, \" \\\"\", ctx_r2.searchTerm, \"\\\" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchTerm, $event) || (ctx_r2.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_8_div_13_div_16_div_4_Template, 2, 2, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵproperty(\"placeholder\", ctx_r2.displayText.searchPlaceholder);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm && ctx_r2.hasNoSearchResults());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_17_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_17_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_17_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_17_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_17_button_8_Template_button_click_0_listener() {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(floor_r15));\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 100);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 101);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const floor_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r2.selectedFloor === floor_r15 ? \"#fff\" : \"#495057\")(\"border\", ctx_r2.selectedFloor === floor_r15 ? \"2px solid #007bff\" : \"1px solid #dee2e6\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", floor_r15, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.getFloorCount(floor_r15), \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92);\n    i0.ɵɵelement(2, \"nb-icon\", 93);\n    i0.ɵɵelementStart(3, \"span\", 94);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_17_span_5_Template, 2, 1, \"span\", 66)(6, HouseholdBindingComponent_ng_template_8_div_13_div_17_button_6_Template, 2, 0, \"button\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 96);\n    i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_div_13_div_17_button_8_Template, 5, 8, \"button\", 97);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"nb-icon\", 103);\n    i0.ɵɵelementStart(2, \"p\", 55);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 110);\n    i0.ɵɵelement(1, \"nb-icon\", 111);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"rgba(255,255,255,0.9)\" : \"#28a745\")(\"color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#007bff\" : \"#fff\")(\"border\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"1px solid rgba(0,123,255,0.3)\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", household_r17.floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 106);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_Template_button_click_1_listener() {\n      const household_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onHouseholdToggle(household_r17.houseId));\n    });\n    i0.ɵɵelementStart(2, \"span\", 107);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_span_4_Template, 3, 7, \"span\", 108)(5, HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_div_5_Template, 2, 0, \"div\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const household_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"#fff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"#6c757d\" : \"#495057\")(\"border\", ctx_r2.isHouseholdSelected(household_r17.houseId) ? \"2px solid #007bff\" : ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"1px solid #dee2e6\" : \"1px solid #ced4da\")(\"opacity\", ctx_r2.isHouseholdDisabled(household_r17.houseId) ? \"0.6\" : \"1\")(\"cursor\", ctx_r2.isHouseholdDisabled(household_r17.houseId) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isHouseholdDisabled(household_r17.houseId));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r2.isHouseholdExcluded(household_r17.houseId) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", household_r17.houseName, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && household_r17.floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHouseholdExcluded(household_r17.houseId));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_Template, 6, 16, \"ng-container\", 105);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getUniqueHouseholdsForDisplay());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102);\n    i0.ɵɵelement(1, \"nb-icon\", 113);\n    i0.ɵɵelementStart(2, \"p\", 55);\n    i0.ɵɵtext(3, \"\\u6B64\\u68DF\\u5225\\u6C92\\u6709\\u53EF\\u7528\\u7684\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57)(2, \"div\", 58)(3, \"h6\", 59);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 60);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_13_button_6_Template, 5, 6, \"button\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 62)(8, \"div\", 58)(9, \"div\", 63)(10, \"div\", 30)(11, \"h6\", 64);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HouseholdBindingComponent_ng_template_8_div_13_span_13_Template, 3, 1, \"span\", 65)(14, HouseholdBindingComponent_ng_template_8_div_13_span_14_Template, 3, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_ng_template_8_div_13_div_15_Template, 6, 8, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, HouseholdBindingComponent_ng_template_8_div_13_div_16_Template, 5, 3, \"div\", 68)(17, HouseholdBindingComponent_ng_template_8_div_13_div_17_Template, 9, 3, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 70);\n    i0.ɵɵtemplate(19, HouseholdBindingComponent_ng_template_8_div_13_div_19_Template, 4, 0, \"div\", 71)(20, HouseholdBindingComponent_ng_template_8_div_13_div_20_Template, 2, 1, \"div\", 72)(21, HouseholdBindingComponent_ng_template_8_div_13_div_21_Template, 4, 0, \"div\", 71);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.displayText.unitSelection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowBatchSelect && ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedBuilding && ctx_r2.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.buildingData[ctx_r2.selectedBuilding] && ctx_r2.buildingData[ctx_r2.selectedBuilding].length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && (!ctx_r2.buildingData[ctx_r2.selectedBuilding] || ctx_r2.buildingData[ctx_r2.selectedBuilding].length === 0) && !ctx_r2.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 114);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 118);\n    i0.ɵɵelement(1, \"nb-icon\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"nb-icon\", 115);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 116);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_25_span_6_Template, 3, 1, \"span\", 117);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.useHouseNameMode && ctx_r2.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r2.searchTerm, \"\\\" (\", ctx_r2.getFilteredHouseholdsCount(), \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 121);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 123);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\")(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"nb-icon\", 31);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 34);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_12_Template, 5, 0, \"div\", 35)(13, HouseholdBindingComponent_ng_template_8_div_13_Template, 22, 10, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 37)(15, \"div\", 38)(16, \"div\", 39)(17, \"div\", 40);\n    i0.ɵɵelement(18, \"nb-icon\", 41);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_8_div_24_Template, 7, 1, \"div\", 42)(25, HouseholdBindingComponent_ng_template_8_div_25_Template, 7, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_ng_template_8_div_26_Template, 2, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_ng_template_8_button_29_Template, 3, 0, \"button\", 47)(30, HouseholdBindingComponent_ng_template_8_button_30_Template, 3, 0, \"button\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 46)(32, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 51);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.displayText.selectUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r2.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedHouseIds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.getSelectedCount(), \") \");\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, dialogService) {\n    this.cdr = cdr;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 建案ID（用於識別）\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.useHouseNameMode = false; // 新增：使用戶別名稱模式\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.houseNameChange = new EventEmitter(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.selectedHouseNames = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態  // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n      this.selectedHouseNames = [];\n    } else {\n      const firstItem = value[0];\n      if (this.useHouseNameMode) {\n        // useHouseNameMode: 期望接收戶別名稱陣列\n        if (typeof firstItem === 'string') {\n          this.selectedHouseNames = [...new Set(value)]; // 去除重複的戶別名稱\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\n        } else {\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\n          this.selectedHouseNames = [];\n          this.selectedHouseIds = [];\n        }\n      } else {\n        // 一般模式: 期望接收 houseId 陣列\n        if (typeof firstItem === 'number') {\n          this.selectedHouseIds = value;\n          // 將 houseId 轉換為戶別名稱（用於顯示）\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n          console.log('一般模式: 使用傳入的 houseId 陣列');\n        } else if (typeof firstItem === 'string') {\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\n          return;\n        } else {\n          console.error('writeValue 收到未知格式的資料:', value);\n          this.selectedHouseIds = [];\n          this.selectedHouseNames = [];\n        }\n      }\n    }\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n    if (changes['useHouseNameMode']) {\n      // 當 useHouseNameMode 變更時，重新同步選擇狀態\n      console.log('useHouseNameMode changed to:', this.useHouseNameMode);\n      // 在 useHouseNameMode 切換時，確保樓層選擇被重置\n      if (this.useHouseNameMode) {\n        this.selectedFloor = '';\n      }\n    }\n  }\n  initializeData() {\n    // 使用傳入的 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else {\n      // 沒有 buildingData，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData provided');\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor, 'useHouseNameMode:', this.useHouseNameMode);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    const filteredItems = households.filter(h => {\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    // 取得被點擊戶別的名稱\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\n    if (!clickedHousehold) {\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\n      return;\n    }\n    let newSelection;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 處理同名戶別的邏輯\n      const houseName = clickedHousehold.houseName;\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      // 檢查是否有任何同名戶別已被選中\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      if (hasAnySelected) {\n        // 如果有同名戶別被選中，移除所有同名戶別\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\n      } else {\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      const isSelected = this.isHouseIdSelected(houseId);\n      if (isSelected) {\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n      } else {\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n          console.log('已達到最大選擇數量');\n          return;\n        }\n        newSelection = [...this.selectedHouseIds, houseId];\n      }\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    console.log('onSelectAllFiltered called');\n    console.log('selectedBuilding:', this.selectedBuilding);\n    console.log('selectedFloor:', this.selectedFloor);\n    console.log('searchTerm:', this.searchTerm);\n    if (!this.selectedBuilding) {\n      console.log('No building selected');\n      return;\n    }\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\n    if (filteredHouseholdItems.length === 0) {\n      console.log('No filtered households found');\n      return;\n    }\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const household of filteredHouseholdItems) {\n      if (household.houseId) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        } else {\n          // 一般模式: 原有邏輯\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\n            unselectedFilteredIds.push(household.houseId);\n          }\n        }\n      }\n    }\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\n    } else {\n      console.log('No households to add');\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 只選擇唯一的戶別名稱\n      const processedHouseNames = new Set();\n      for (const household of buildingHouseholds) {\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\n          processedHouseNames.add(household.houseName);\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\n          if (!hasAnySelected && !hasAnyExcluded) {\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\n          }\n        }\n      }\n    } else {\n      // 一般模式: 原有邏輯\n      for (const household of buildingHouseholds) {\n        if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n          unselectedBuildingIds.push(household.houseId);\n        }\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds); // 根據模式決定要回傳的資料格式\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\n      this.onChange([...uniqueHouseNames]);\n      this.houseNameChange.emit([...uniqueHouseNames]);\n    } else {\n      // 一般模式: 回傳 houseId 陣列\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\n      this.onChange([...this.selectedHouseIds]);\n      // 回傳 houseId 陣列\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\n      console.log('House IDs to emit:', houseIds);\n      this.houseIdChange.emit(houseIds);\n    }\n    this.onTouched();\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\n      const household = this.getHouseholdByHouseId(houseId);\n      if (household) {\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\n      }\n      return false;\n    } else {\n      // 一般模式: 直接檢查 houseId\n      return this.selectedHouseIds.includes(houseId);\n    }\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const households = this.buildingData[building] || [];\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\n      return uniqueHouseNames.size;\n    } else {\n      // 一般模式: 返回總戶別數量\n      return this.buildingData[building]?.length || 0;\n    }\n  }\n  getSelectedCount() {\n    if (this.useHouseNameMode) {\n      // useHouseNameMode: 返回唯一戶別名稱的數量\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\n      return uniqueHouseNames.length;\n    } else {\n      // 一般模式: 返回實際選中的戶別數量\n      return this.selectedHouseIds.length;\n    }\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    return this.getUniqueHouseholdsForDisplay().length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\n  getAllHouseIdsByHouseName(houseName) {\n    const houseIds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        if (household.houseId) {\n          houseIds.push(household.houseId);\n        }\n      });\n    }\n    return houseIds;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  } // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\n  convertHouseNamesToIds(houseNames) {\n    const houseIds = [];\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\n    for (const houseName of uniqueHouseNames) {\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\n      if (matchingHouseIds.length > 0) {\n        if (this.useHouseNameMode) {\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\n          houseIds.push(matchingHouseIds[0]);\n          if (matchingHouseIds.length > 1) {\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\n          }\n        } else {\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\n          houseIds.push(...matchingHouseIds);\n          if (matchingHouseIds.length > 1) {\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\n          }\n        }\n      } else {\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\n      }\n    }\n    // 去除重複的 houseId\n    return [...new Set(houseIds)];\n  }\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\n  convertIdsToHouseNames(houseIds) {\n    const houseNames = [];\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\n    for (const houseId of uniqueHouseIds) {\n      const householdInfo = this.getHouseholdInfoById(houseId);\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\n        houseNames.push(householdInfo.houseName);\n      } else {\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\n      }\n    }\n    // 去除重複的戶別名稱\n    return [...new Set(houseNames)];\n  }\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\n  getUniqueHouseholdsForDisplay() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return [];\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    if (!this.useHouseNameMode) {\n      // 一般模式：返回所有戶別\n      return households.filter(h => {\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n        return floorMatch && searchMatch;\n      });\n    }\n    // useHouseNameMode：只返回唯一的戶別名稱\n    const uniqueHouseNames = new Set();\n    const uniqueHouseholds = [];\n    for (const household of households) {\n      // 搜尋篩選\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\n        uniqueHouseNames.add(household.houseName);\n        uniqueHouseholds.push(household);\n      }\n    }\n    console.log('Unique households for display:', uniqueHouseholds.map(h => h.houseName));\n    return uniqueHouseholds;\n  }\n  // 新增：動態獲取文案的getter方法\n  get displayText() {\n    return {\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型代碼...' : '搜尋戶別代碼...',\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶別',\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可用的戶型' : '此棟別沒有可用的戶別'\n    };\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseIds: \"excludedHouseIds\",\n        useHouseNameMode: \"useHouseNameMode\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        houseIdChange: \"houseIdChange\",\n        houseNameChange: \"houseNameChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 6,\n      consts: [[\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [1, \"household-info\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"household-floor\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"display: flex; align-items: center; gap: 8px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [2, \"width\", \"300px\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"style\", \"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"home-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"3px 8px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"4px\", \"font-size\", \"0.7rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\", \"placeholder\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"1rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"600\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"6px 10px\", \"border-radius\", \"3px\", \"font-size\", \"0.75rem\", \"font-weight\", \"500\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"3px\", \"font-size\", \"0.7rem\"], [2, \"font-size\", \"0.7rem\", \"opacity\", \"0.7\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"8px 6px\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"55px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"align-items\", \"center\", \"gap\", \"3px\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"600\", \"line-height\", \"1.2\", \"font-size\", \"0.85rem\"], [\"style\", \"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\", 3, \"background-color\", \"color\", \"border\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.7rem\", \"font-weight\", \"600\", \"padding\", \"2px 6px\", \"border-radius\", \"3px\", \"display\", \"inline-flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"min-width\", \"22px\"], [\"icon\", \"layers-outline\", 2, \"margin-right\", \"2px\", \"font-size\", \"0.6rem\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"background-color\", \"#007bff\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\"], [\"style\", \"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\", 4, \"ngIf\"], [2, \"background-color\", \"#28a745\", \"color\", \"white\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"font-weight\", \"600\", \"margin-left\", \"4px\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 4, \"div\", 2);\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleDropdown());\n          });\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 6)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_Template, 37, 12, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseIds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i1.NbCardComponent, i1.NbCardBodyComponent, i1.NbCardFooterComponent, i1.NbCardHeaderComponent, i1.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 500;\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n  flex: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 0.25rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed !important;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.15s ease;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.flat-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.flat-badge.building[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n.flat-badge.floor[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.flat-badge.search[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  color: #6c757d;\\n}\\n.flat-badge.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  border: 1px solid #bbdefb;\\n}\\n\\n.flat-household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 4px;\\n  border: 1px solid #bbdefb;\\n  transition: background-color 0.2s ease;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  color: #ffffff;\\n  background-color: #28a745;\\n  padding: 0.15rem 0.4rem;\\n  border-radius: 4px;\\n  min-width: fit-content;\\n  text-align: center;\\n  letter-spacing: 0.02em;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 4px;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #ffffff;\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.flat-household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n\\n.flat-button[_ngcontent-%COMP%] {\\n  border: 1px solid;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n}\\n.flat-button.primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button.primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.primary.selected[_ngcontent-%COMP%] {\\n  background-color: #0056b3;\\n  border-color: #0056b3;\\n}\\n.flat-button.success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border-color: #28a745;\\n}\\n.flat-button.success[_ngcontent-%COMP%]:hover {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.success.selected[_ngcontent-%COMP%] {\\n  background-color: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.flat-button.light[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  border-color: #ced4da;\\n}\\n.flat-button.light[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.flat-button.light.selected[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-color: #007bff;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.flat-button[_ngcontent-%COMP%]:disabled:hover {\\n  opacity: 0.6;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.25rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    color: #bbdefb;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n    color: #90caf9;\\n    background-color: rgba(0, 0, 0, 0.2);\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n    background: rgba(0, 0, 0, 0.3);\\n    border-color: #6c757d;\\n    color: #bbdefb;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n    background-color: #f44336;\\n    border-color: #f44336;\\n    color: white;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "getHouseholdInfoById", "houseId_r5", "floor", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵtextInterpolate", "houseName", "ɵɵproperty", "useHouseNameMode", "disabled", "ɵɵelementContainerStart", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r6", "getBuildingSelectedHouseIds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r2", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "ɵɵtextInterpolate2", "displayText", "selectedPrefix", "getSelectedCount", "buildings", "selectedCount", "placeholder", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener", "building_r9", "_r8", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_ng_template_8_div_13_div_15_button_5_Template_button_click_0_listener", "_r11", "onUnselectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_button_click_1_listener", "_r10", "onSelectAllFiltered", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_15_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "noResults", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_ng_template_8_div_13_div_16_Template_input_ngModelChange_2_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_ng_template_8_div_13_div_16_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_ng_template_8_div_13_div_16_div_4_Template", "ɵɵtwoWayProperty", "searchPlaceholder", "hasNoSearchResults", "HouseholdBindingComponent_ng_template_8_div_13_div_17_button_6_Template_button_click_0_listener", "_r13", "onFloorSelect", "HouseholdBindingComponent_ng_template_8_div_13_div_17_button_8_Template_button_click_0_listener", "floor_r15", "_r14", "getFloorCount", "HouseholdBindingComponent_ng_template_8_div_13_div_17_span_5_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_17_button_6_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_17_button_8_Template", "floors", "isHouseholdSelected", "household_r17", "houseId", "HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_Template_button_click_1_listener", "_r16", "onHouseholdToggle", "HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_span_4_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_div_5_Template", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_ng_template_8_div_13_div_20_ng_container_1_Template", "getUniqueHouseholdsForDisplay", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_13_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_14_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_16_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_17_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_19_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_21_Template", "unitSelection", "allowBatchSelect", "buildingData", "length", "allowSearch", "maxSelections", "HouseholdBindingComponent_ng_template_8_div_25_span_6_Template", "getFilteredHouseholdsCount", "HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener", "_r18", "HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener", "_r19", "resetSearch", "HouseholdBindingComponent_ng_template_8_div_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_Template", "HouseholdBindingComponent_ng_template_8_div_24_Template", "HouseholdBindingComponent_ng_template_8_div_25_Template", "HouseholdBindingComponent_ng_template_8_div_26_Template", "HouseholdBindingComponent_ng_template_8_button_29_Template", "HouseholdBindingComponent_ng_template_8_button_30_Template", "HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener", "ref_r20", "_r7", "dialogRef", "close", "HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener", "selectUnit", "isLoading", "selectedHouseIds", "HouseholdBindingComponent", "constructor", "cdr", "dialogService", "buildCaseId", "showSelectedArea", "excludedHouseIds", "selectionChange", "houseIdChange", "houseNameChange", "isOpen", "selectedHouseNames", "filteredHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "console", "log", "firstItem", "Set", "convertHouseNamesToIds", "error", "convertIdsToHouseNames", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "building", "item", "find", "h", "push", "updateFloorsForBuilding", "detectChanges", "onBuildingClick", "households", "filteredItems", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "map", "event", "target", "isHouseIdExcluded", "clickedHousehold", "getHouseholdByHouseId", "newSelection", "allMatchingHouseIds", "getAllHouseIdsByHouseName", "hasAnySelected", "some", "id", "isSelected", "isHouseIdSelected", "emitChanges", "filteredHouseholdItems", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "household", "hasAnyExcluded", "toAdd", "slice", "buildingHouseholds", "unselectedBuildingIds", "processedHouseNames", "has", "add", "buildingHouseIds", "undefined", "uniqueHouseNames", "emit", "houseIds", "selectedItems", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isAllBuildingSelected", "every", "getSelectedByBuilding", "size", "floorSet", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "filtered", "getHouseholdUniqueId", "toString", "getHouseIdByHouseName", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "houseNames", "matchingHouseIds", "uniqueHouseIds", "householdInfo", "startsWith", "uniqueHouseholds", "unitType", "noAvailable", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "NbDialogService", "selectors", "viewQuery", "HouseholdBindingComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "_r1", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseType?: number; // 新增：戶別類型\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 建案ID（用於識別）\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n  @Input() useHouseNameMode: boolean = false; // 新增：使用戶別名稱模式\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  @Output() houseNameChange = new EventEmitter<string[]>(); // 新增：useHouseNameMode 時回傳戶別名稱陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = ''; selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  selectedHouseNames: string[] = []; // 新增：useHouseNameMode 時使用的戶別名稱陣列\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態  // ControlValueAccessor implementation\r\n  private onChange = (value: number[] | string[]) => { };\r\n  private onTouched = () => { }; constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private dialogService: NbDialogService\r\n  ) { } writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0], 'useHouseNameMode:', this.useHouseNameMode);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n      this.selectedHouseNames = [];\r\n    } else {\r\n      const firstItem = value[0]; if (this.useHouseNameMode) {\r\n        // useHouseNameMode: 期望接收戶別名稱陣列\r\n        if (typeof firstItem === 'string') {\r\n          this.selectedHouseNames = [...new Set(value as string[])]; // 去除重複的戶別名稱\r\n          // 將戶別名稱轉換為 houseId（用於內部邏輯），會自動處理重複名稱\r\n          this.selectedHouseIds = this.convertHouseNamesToIds(this.selectedHouseNames);\r\n          console.log('useHouseNameMode: 使用傳入的戶別名稱陣列（已去重）');\r\n        } else {\r\n          console.error('useHouseNameMode 期望接收 string[] 但收到:', typeof firstItem);\r\n          this.selectedHouseNames = [];\r\n          this.selectedHouseIds = [];\r\n        }\r\n      } else {\r\n        // 一般模式: 期望接收 houseId 陣列\r\n        if (typeof firstItem === 'number') {\r\n          this.selectedHouseIds = value as number[];\r\n          // 將 houseId 轉換為戶別名稱（用於顯示）\r\n          this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n          console.log('一般模式: 使用傳入的 houseId 陣列');\r\n        } else if (typeof firstItem === 'string') {\r\n          // 向下相容：如果收到字串但不在 useHouseNameMode，發出警告\r\n          console.error('⚠️ 警告：一般模式下收到戶別名稱陣列而不是 houseId 陣列！');\r\n          console.error('⚠️ 建議父元件改用 houseId 陣列或啟用 useHouseNameMode');\r\n          return;\r\n        } else {\r\n          console.error('writeValue 收到未知格式的資料:', value);\r\n          this.selectedHouseIds = [];\r\n          this.selectedHouseNames = [];\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\r\n    console.log('selectedHouseNames set to:', this.selectedHouseNames);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n  registerOnChange(fn: (value: number[] | string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  } ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n    if (changes['useHouseNameMode']) {\r\n      // 當 useHouseNameMode 變更時，重新同步選擇狀態\r\n      console.log('useHouseNameMode changed to:', this.useHouseNameMode);\r\n      // 在 useHouseNameMode 切換時，確保樓層選擇被重置\r\n      if (this.useHouseNameMode) {\r\n        this.selectedFloor = '';\r\n      }\r\n    }\r\n  } private initializeData() {\r\n    // 使用傳入的 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else {\r\n      // 沒有 buildingData，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData provided');\r\n    }\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor, 'useHouseNameMode:', this.useHouseNameMode);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    const filteredItems = households.filter(h => {\r\n      // 樓層篩選：在 useHouseNameMode 時跳過樓層篩選，否則按原邏輯篩選\r\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\r\n      // 搜尋篩選：戶別代碼包含搜尋詞\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    // 只提取戶別名稱用於UI顯示（但onSelectAllFiltered會直接使用物件）\r\n    this.filteredHouseholds = filteredItems.map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n    console.log('Filtered items details:', filteredItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  } onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    // 取得被點擊戶別的名稱\r\n    const clickedHousehold = this.getHouseholdByHouseId(houseId);\r\n    if (!clickedHousehold) {\r\n      console.log(`找不到 houseId ${houseId} 對應的戶別資訊`);\r\n      return;\r\n    }\r\n\r\n    let newSelection: number[];\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 處理同名戶別的邏輯\r\n      const houseName = clickedHousehold.houseName;\r\n      const allMatchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\r\n\r\n      // 檢查是否有任何同名戶別已被選中\r\n      const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n\r\n      if (hasAnySelected) {\r\n        // 如果有同名戶別被選中，移除所有同名戶別\r\n        newSelection = this.selectedHouseIds.filter(id => !allMatchingHouseIds.includes(id));\r\n        console.log(`useHouseNameMode: 移除所有同名戶別 \"${houseName}\":`, allMatchingHouseIds);\r\n      } else {\r\n        // 如果沒有同名戶別被選中，只添加第一個（通常是當前點擊的）\r\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n          console.log('已達到最大選擇數量');\r\n          return;\r\n        }\r\n        newSelection = [...this.selectedHouseIds, allMatchingHouseIds[0]];\r\n        console.log(`useHouseNameMode: 添加戶別 \"${houseName}\" 的第一個項目:`, allMatchingHouseIds[0]);\r\n      }\r\n    } else {\r\n      // 一般模式: 原有邏輯\r\n      const isSelected = this.isHouseIdSelected(houseId);\r\n\r\n      if (isSelected) {\r\n        newSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n      } else {\r\n        if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n          console.log('已達到最大選擇數量');\r\n          return;\r\n        }\r\n        newSelection = [...this.selectedHouseIds, houseId];\r\n      }\r\n    }\r\n\r\n    this.selectedHouseIds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    console.log('onSelectAllFiltered called');\r\n    console.log('selectedBuilding:', this.selectedBuilding);\r\n    console.log('selectedFloor:', this.selectedFloor);\r\n    console.log('searchTerm:', this.searchTerm);\r\n\r\n    if (!this.selectedBuilding) {\r\n      console.log('No building selected');\r\n      return;\r\n    }\r\n\r\n    // 使用 getUniqueHouseholdsForDisplay 方法來獲取要處理的戶別列表\r\n    const filteredHouseholdItems = this.getUniqueHouseholdsForDisplay();\r\n\r\n    console.log('Filtered household items:', filteredHouseholdItems.map(h => `${h.houseName}-${h.floor} (ID:${h.houseId})`));\r\n\r\n    if (filteredHouseholdItems.length === 0) {\r\n      console.log('No filtered households found');\r\n      return;\r\n    }\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const household of filteredHouseholdItems) {\r\n      if (household.houseId) {\r\n        if (this.useHouseNameMode) {\r\n          // useHouseNameMode: 檢查是否有任何同名戶別已被選擇\r\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseIdExcluded(id));\r\n\r\n          if (!hasAnySelected && !hasAnyExcluded) {\r\n            unselectedFilteredIds.push(allMatchingHouseIds[0]); // 只選擇第一個\r\n          }\r\n        } else {\r\n          // 一般模式: 原有邏輯\r\n          if (!this.isHouseIdSelected(household.houseId) && !this.isHouseIdExcluded(household.houseId)) {\r\n            unselectedFilteredIds.push(household.houseId);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    console.log('Unselected filtered IDs:', unselectedFilteredIds);\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別，IDs:`, toAdd);\r\n    } else {\r\n      console.log('No households to add');\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 只選擇唯一的戶別名稱\r\n      const processedHouseNames = new Set<string>();\r\n\r\n      for (const household of buildingHouseholds) {\r\n        if (household.houseId && household.houseName && !processedHouseNames.has(household.houseName)) {\r\n          processedHouseNames.add(household.houseName);\r\n\r\n          const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n          const hasAnySelected = allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n          const hasAnyExcluded = allMatchingHouseIds.some(id => this.isHouseholdExcluded(id));\r\n\r\n          if (!hasAnySelected && !hasAnyExcluded) {\r\n            unselectedBuildingIds.push(allMatchingHouseIds[0]); // 只選擇第一個\r\n          }\r\n        }\r\n      }\r\n    } else {\r\n      // 一般模式: 原有邏輯\r\n      for (const household of buildingHouseholds) {\r\n        if (household.houseId &&\r\n          !this.selectedHouseIds.includes(household.houseId) &&\r\n          !this.isHouseholdExcluded(household.houseId)) {\r\n          unselectedBuildingIds.push(household.houseId);\r\n        }\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.emitChanges();\r\n  } private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);    // 根據模式決定要回傳的資料格式\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 回傳戶別名稱陣列（去重複）\r\n      this.selectedHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n      const uniqueHouseNames = [...new Set(this.selectedHouseNames)]; // 確保去重複\r\n      console.log('useHouseNameMode - 回傳戶別名稱陣列（已去重）:', uniqueHouseNames);\r\n      this.onChange([...uniqueHouseNames]);\r\n      this.houseNameChange.emit([...uniqueHouseNames]);\r\n    } else {\r\n      // 一般模式: 回傳 houseId 陣列\r\n      console.log('一般模式 - 回傳 houseId 陣列:', this.selectedHouseIds);\r\n      this.onChange([...this.selectedHouseIds]);\r\n\r\n      // 回傳 houseId 陣列\r\n      const houseIds = this.selectedHouseIds.filter(id => id !== undefined);\r\n      console.log('House IDs to emit:', houseIds);\r\n      this.houseIdChange.emit(houseIds);\r\n    }\r\n\r\n    this.onTouched();\r\n\r\n    // 無論哪種模式都回傳完整的 HouseholdItem 陣列（向下相容）\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  } isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 檢查是否有任何同名戶別被選中\r\n      const household = this.getHouseholdByHouseId(houseId);\r\n      if (household) {\r\n        const allMatchingHouseIds = this.getAllHouseIdsByHouseName(household.houseName);\r\n        return allMatchingHouseIds.some(id => this.selectedHouseIds.includes(id));\r\n      }\r\n      return false;\r\n    } else {\r\n      // 一般模式: 直接檢查 houseId\r\n      return this.selectedHouseIds.includes(houseId);\r\n    }\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\r\n  } isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n  getBuildingCount(building: string): number {\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 返回唯一戶別名稱的數量\r\n      const households = this.buildingData[building] || [];\r\n      const uniqueHouseNames = new Set(households.map(h => h.houseName));\r\n      return uniqueHouseNames.size;\r\n    } else {\r\n      // 一般模式: 返回總戶別數量\r\n      return this.buildingData[building]?.length || 0;\r\n    }\r\n  }\r\n  getSelectedCount(): number {\r\n    if (this.useHouseNameMode) {\r\n      // useHouseNameMode: 返回唯一戶別名稱的數量\r\n      const uniqueHouseNames = this.convertIdsToHouseNames(this.selectedHouseIds);\r\n      return uniqueHouseNames.length;\r\n    } else {\r\n      // 一般模式: 返回實際選中的戶別數量\r\n      return this.selectedHouseIds.length;\r\n    }\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    } const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = this.useHouseNameMode || !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    return this.getUniqueHouseholdsForDisplay().length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 根據 houseName 查找所有對應的 houseId（處理重複名稱）\r\n  private getAllHouseIdsByHouseName(houseName: string): number[] {\r\n    const houseIds: number[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        if (household.houseId) {\r\n          houseIds.push(household.houseId);\r\n        }\r\n      });\r\n    }\r\n\r\n    return houseIds;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }  // 新增：將戶別名稱陣列轉換為 houseId 陣列（在 useHouseNameMode 下只選擇第一個匹配項）\r\n  private convertHouseNamesToIds(houseNames: string[]): number[] {\r\n    const houseIds: number[] = [];\r\n    const uniqueHouseNames = [...new Set(houseNames)]; // 去除重複的戶別名稱\r\n\r\n    for (const houseName of uniqueHouseNames) {\r\n      const matchingHouseIds = this.getAllHouseIdsByHouseName(houseName);\r\n      if (matchingHouseIds.length > 0) {\r\n        if (this.useHouseNameMode) {\r\n          // useHouseNameMode: 只選擇第一個匹配的 houseId，避免多個同名戶別都被選中\r\n          houseIds.push(matchingHouseIds[0]);\r\n          if (matchingHouseIds.length > 1) {\r\n            console.log(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，在 useHouseNameMode 下只選擇第一個:`, matchingHouseIds[0]);\r\n          }\r\n        } else {\r\n          // 一般模式: 將所有對應的 houseId 都加入（保持原有邏輯）\r\n          houseIds.push(...matchingHouseIds);\r\n          if (matchingHouseIds.length > 1) {\r\n            console.warn(`戶別名稱 \"${houseName}\" 有 ${matchingHouseIds.length} 個重複項目，已全部加入選擇:`, matchingHouseIds);\r\n          }\r\n        }\r\n      } else {\r\n        console.warn(`無法找到戶別名稱 \"${houseName}\" 對應的 houseId`);\r\n      }\r\n    }\r\n\r\n    // 去除重複的 houseId\r\n    return [...new Set(houseIds)];\r\n  }\r\n  // 新增：將 houseId 陣列轉換為戶別名稱陣列（去重複）\r\n  private convertIdsToHouseNames(houseIds: number[]): string[] {\r\n    const houseNames: string[] = [];\r\n    const uniqueHouseIds = [...new Set(houseIds)]; // 去除重複的 houseId\r\n\r\n    for (const houseId of uniqueHouseIds) {\r\n      const householdInfo = this.getHouseholdInfoById(houseId);\r\n      if (householdInfo.houseName && !householdInfo.houseName.startsWith('ID:')) {\r\n        houseNames.push(householdInfo.houseName);\r\n      } else {\r\n        console.warn(`無法找到 houseId ${houseId} 對應的戶別名稱`);\r\n      }\r\n    }\r\n\r\n    // 去除重複的戶別名稱\r\n    return [...new Set(houseNames)];\r\n  }\r\n\r\n  // 新增：取得去重複的戶別列表（用於 useHouseNameMode 顯示）\r\n  getUniqueHouseholdsForDisplay(): HouseholdItem[] {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return [];\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    if (!this.useHouseNameMode) {\r\n      // 一般模式：返回所有戶別\r\n      return households.filter(h => {\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      });\r\n    }\r\n\r\n    // useHouseNameMode：只返回唯一的戶別名稱\r\n    const uniqueHouseNames = new Set<string>();\r\n    const uniqueHouseholds: HouseholdItem[] = [];\r\n\r\n    for (const household of households) {\r\n      // 搜尋篩選\r\n      const searchMatch = !this.searchTerm || household.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n\r\n      if (searchMatch && !uniqueHouseNames.has(household.houseName)) {\r\n        uniqueHouseNames.add(household.houseName);\r\n        uniqueHouseholds.push(household);\r\n      }\r\n    }\r\n\r\n    console.log('Unique households for display:', uniqueHouseholds.map(h => h.houseName));\r\n    return uniqueHouseholds;\r\n  }\r\n\r\n  // 新增：動態獲取文案的getter方法\r\n  get displayText() {\r\n    return {\r\n      unitType: this.useHouseNameMode ? '戶型' : '戶別',\r\n      placeholder: this.useHouseNameMode ? '請選擇戶型' : '請選擇戶別',\r\n      selectedPrefix: this.useHouseNameMode ? '已選擇戶型' : '已選擇戶別',\r\n      selectUnit: this.useHouseNameMode ? '選擇戶型' : '選擇戶別',\r\n      unitSelection: this.useHouseNameMode ? '戶型選擇' : '戶別選擇',\r\n      searchPlaceholder: this.useHouseNameMode ? '搜尋戶型代碼...' : '搜尋戶別代碼...',\r\n      selectedCount: this.useHouseNameMode ? '個戶型' : '個戶別',\r\n      noResults: this.useHouseNameMode ? '找不到符合的戶型' : '找不到符合的戶別',\r\n      noAvailable: this.useHouseNameMode ? '此棟別沒有可用的戶型' : '此棟別沒有可用的戶別'\r\n    };\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseIds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">{{displayText.selectedPrefix}} ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let houseId of getBuildingSelectedHouseIds(building)\" class=\"household-tag\">\r\n              <div class=\"household-info\">\r\n                <span class=\"household-code\">{{getHouseholdInfoById(houseId).houseName}}</span>\r\n                <span *ngIf=\"!useHouseNameMode && getHouseholdInfoById(houseId).floor\" class=\"household-floor\">\r\n                  {{getHouseholdInfoById(houseId).floor}}\r\n                </span>\r\n              </div>\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(houseId)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div> <!-- 選擇器 -->\r\n  <div class=\"selector-container\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' ' + displayText.selectedCount : (placeholder || displayText.placeholder)}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"home-outline\" class=\"chevron-icon\"></nb-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- 戶別選擇對話框 -->\r\n<ng-template #householdDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 95vw; max-width: 1200px; max-height: 90vh;\">\r\n    <nb-card-header>\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n        <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n          <nb-icon icon=\"home-outline\" style=\"color: #007bff; font-size: 1.5rem;\"></nb-icon>\r\n          <span style=\"font-weight: 500; color: #495057; font-size: 1.25rem;\">{{displayText.selectUnit}}</span>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n        </div>\r\n        <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body style=\"padding: 0; overflow: hidden;\">\r\n      <!-- 載入狀態 -->\r\n      <div *ngIf=\"isLoading\"\r\n        style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n        <div style=\"text-align: center; color: #6c757d;\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n          <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要內容區域 -->\r\n      <div *ngIf=\"!isLoading\" style=\"display: flex; height: 60vh; min-height: 400px;\">\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div\r\n          style=\"width: 300px; border-right: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n          </div>\r\n          <div style=\"flex: 1; overflow-y: auto;\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n              <span\r\n                style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">\r\n                {{getBuildingCount(building)}}戶\r\n              </span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n                <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 600; color: #495057;\">{{displayText.unitSelection}}</h6> <span\r\n                  *ngIf=\"selectedBuilding\"\r\n                  style=\"background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  <nb-icon icon=\"home-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{selectedBuilding}}\r\n                </span>\r\n                <span *ngIf=\"!useHouseNameMode && selectedFloor\"\r\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{selectedFloor}}\r\n                </span>\r\n              </div>\r\n              <div\r\n                *ngIf=\"allowBatchSelect && selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n                style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" [placeholder]=\"displayText.searchPlaceholder\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\"\r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && hasNoSearchResults()\"\r\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                {{displayText.noResults}} \"{{searchTerm}}\"\r\n              </div>\r\n            </div> <!-- 樓層篩選器 -->\r\n            <div *ngIf=\"!useHouseNameMode && selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 1rem;\"></nb-icon>\r\n                <span style=\"font-size: 0.875rem; font-weight: 600; color: #495057;\">樓層篩選:</span>\r\n                <span *ngIf=\"selectedFloor\"\r\n                  style=\"background-color: #28a745; color: white; padding: 3px 8px; border-radius: 3px; font-size: 0.75rem; font-weight: 600;\">\r\n                  {{selectedFloor}}\r\n                </span>\r\n                <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\r\n                  style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                  清除篩選\r\n                </button>\r\n              </div>\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\r\n                  [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                  [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                  [style.border]=\"selectedFloor === floor ? '2px solid #007bff' : '1px solid #dee2e6'\"\r\n                  style=\"padding: 6px 10px; border-radius: 3px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"margin-right: 3px; font-size: 0.7rem;\"></nb-icon>\r\n                  {{floor}} <span style=\"font-size: 0.7rem; opacity: 0.7;\">({{getFloorCount(floor)}})</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && buildingData[selectedBuilding] && buildingData[selectedBuilding].length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n              <ng-container *ngFor=\"let household of getUniqueHouseholdsForDisplay()\"> <button type=\"button\"\r\n                  (click)=\"onHouseholdToggle(household.houseId)\" [disabled]=\"isHouseholdDisabled(household.houseId)\"\r\n                  [style.background-color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : (isHouseholdExcluded(household.houseId) ? '#f8f9fa' : '#fff')\"\r\n                  [style.color]=\"isHouseholdSelected(household.houseId) ? '#fff' : (isHouseholdExcluded(household.houseId) ? '#6c757d' : '#495057')\"\r\n                  [style.border]=\"isHouseholdSelected(household.houseId) ? '2px solid #007bff' : (isHouseholdExcluded(household.houseId) ? '1px solid #dee2e6' : '1px solid #ced4da')\"\r\n                  [style.opacity]=\"isHouseholdDisabled(household.houseId) ? '0.6' : '1'\"\r\n                  [style.cursor]=\"isHouseholdDisabled(household.houseId) ? 'not-allowed' : 'pointer'\"\r\n                  style=\"padding: 8px 6px; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 55px; position: relative; display: flex; flex-direction: column; justify-content: center; align-items: center; gap: 3px;\">\r\n                  <span [style.text-decoration]=\"isHouseholdExcluded(household.houseId) ? 'line-through' : 'none'\"\r\n                    style=\"font-weight: 600; line-height: 1.2; font-size: 0.85rem;\">\r\n                    {{household.houseName}}\r\n                  </span> <span *ngIf=\"!useHouseNameMode && household.floor\"\r\n                    [style.background-color]=\"isHouseholdSelected(household.houseId) ? 'rgba(255,255,255,0.9)' : '#28a745'\"\r\n                    [style.color]=\"isHouseholdSelected(household.houseId) ? '#007bff' : '#fff'\"\r\n                    [style.border]=\"isHouseholdSelected(household.houseId) ? '1px solid rgba(0,123,255,0.3)' : 'none'\"\r\n                    style=\"font-size: 0.7rem; font-weight: 600; padding: 2px 6px; border-radius: 3px; display: inline-flex; align-items: center; justify-content: center; min-width: 22px;\">\r\n                    <nb-icon icon=\"layers-outline\" style=\"margin-right: 2px; font-size: 0.6rem;\"></nb-icon>\r\n                    {{household.floor}}\r\n                  </span>\r\n                  <div *ngIf=\"isHouseholdExcluded(household.houseId)\"\r\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                    ✕\r\n                  </div>\r\n                </button>\r\n              </ng-container>\r\n            </div>\r\n            <div\r\n              *ngIf=\"selectedBuilding && (!buildingData[selectedBuilding] || buildingData[selectedBuilding].length === 0) && !searchTerm\"\r\n              style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">此棟別沒有可用的戶別</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n      <!-- 統計資訊行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n        <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n          <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n            <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n          </div>\r\n          <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n          </div>\r\n          <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span>當前棟別: </span>\r\n            <span\r\n              style=\"background-color: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600;\">\r\n              {{selectedBuilding}}\r\n            </span> <span *ngIf=\"!useHouseNameMode && selectedFloor\"\r\n              style=\"background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 600; margin-left: 4px;\">\r\n              <nb-icon icon=\"layers-outline\" style=\"margin-right: 4px; font-size: 0.7rem;\"></nb-icon>\r\n              {{selectedFloor}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"searchTerm\"\r\n          style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px;\">\r\n          搜尋: \"{{searchTerm}}\" ({{getFilteredHouseholdsCount()}} 個結果)\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作按鈕行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" *ngIf=\"selectedHouseIds.length > 0\" (click)=\"onClearAll()\"\r\n            style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n            清空全部\r\n          </button>\r\n          <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n            style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n            重置搜尋\r\n          </button>\r\n        </div>\r\n\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n            取消\r\n          </button>\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n            確定選擇 ({{getSelectedCount()}})\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAAmD,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;ICmBxDC,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAC,KAAA,MACF;;;;;;IAHAT,EAFJ,CAAAC,cAAA,eAA0F,cAC5D,eACG;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAU,UAAA,IAAAC,2EAAA,mBAA+F;IAGjGX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAoG;IAArCD,EAAA,CAAAY,UAAA,mBAAAC,6FAAA;MAAA,MAAAL,UAAA,GAAAR,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,UAAA,CAA0B;IAAA,EAAC;IACjGR,EAAA,CAAAoB,SAAA,kBAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;;IAR0BH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAc,SAAA,CAA2C;IACjEtB,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAkB,gBAAA,IAAAlB,MAAA,CAAAC,oBAAA,CAAAC,UAAA,EAAAC,KAAA,CAA8D;IAI9BT,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAmB,QAAA,CAAqB;;;;;IAVpEzB,EAAA,CAAA0B,uBAAA,GAAoD;IAClD1B,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAU,UAAA,IAAAiB,oEAAA,mBAA0F;IAW5F3B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,kBAAA,KAAAuB,WAAA,MAAa;IAEb5B,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAuB,2BAAA,CAAAD,WAAA,EAAwC;;;;;IAJxE5B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,UAAA,IAAAoB,6DAAA,0BAAoD;IAgBtD9B,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBWH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpD5B,EAFJ,CAAAC,cAAA,aAA8F,aAC/D,cACA;IACzBD,EAAA,CAAAoB,SAAA,kBAA8D;IAC9DpB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IACtFF,EADsF,CAAAG,YAAA,EAAO,EACvF;IACNH,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAoB,iEAAA;MAAAhC,EAAA,CAAAc,aAAA,CAAAmB,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4B,UAAA,EAAY;IAAA,EAAC;IACtGlC,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,UAAA,IAAAyB,8CAAA,kBAA+D;IAmBnEnC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1B6BH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAoC,kBAAA,KAAA9B,MAAA,CAAA+B,WAAA,CAAAC,cAAA,QAAAhC,MAAA,CAAAiC,gBAAA,QAAuD;IAE1BvC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAmB,QAAA,CAAqB;IAKvDzB,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAkC,SAAA,CAAY;;;;;IAyBpCxC,EAAA,CAAA0B,uBAAA,GAAgC;IAC9B1B,EAAA,CAAAoB,SAAA,kBAAsD;IACtDpB,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAA0B,uBAAA,GAAiC;IAC/B1B,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAiC,gBAAA,iCAAAjC,MAAA,CAAAiC,gBAAA,WAAAjC,MAAA,CAAA+B,WAAA,CAAAI,aAAA,GAAAnC,MAAA,CAAAoC,WAAA,IAAApC,MAAA,CAAA+B,WAAA,CAAAK,WAAA,MACF;;;;;IAyBA1C,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAoB,SAAA,kBAAmG;IACnGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAG,YAAA,EAAI,EACrD,EACF;;;;;;IAWAH,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAY,UAAA,mBAAA+B,yFAAA;MAAA,MAAAC,WAAA,GAAA5C,EAAA,CAAAc,aAAA,CAAA+B,GAAA,EAAA7B,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAwC,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3F5C,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACA;;;;;IAPPH,EADA,CAAA+C,WAAA,qBAAAzC,MAAA,CAAA0C,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAtC,MAAA,CAAA0C,gBAAA,KAAAJ,WAAA,iDACe;IAEnD5C,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAqB,iBAAA,CAAAuB,WAAA,CAAY;IAG1D5C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA2C,gBAAA,CAAAL,WAAA,aACF;;;;;IAUmH5C,EAAA,CAAAC,cAAA,eAEc;IAC7HD,EAAA,CAAAoB,SAAA,kBAAqF;IACrFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0C,gBAAA,MACF;;;;;IACAhD,EAAA,CAAAC,cAAA,eAC+H;IAC7HD,EAAA,CAAAoB,SAAA,kBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4C,aAAA,MACF;;;;;;IAeAlD,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAY,UAAA,mBAAAuC,gGAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAsC,IAAA;MAAA,MAAA9C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA+C,qBAAA,EAAuB;IAAA,EAAC;IAEtFrD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAHF,CAAAC,cAAA,cAEmC,iBAGqG;IAFlFD,EAAA,CAAAY,UAAA,mBAAA0C,uFAAA;MAAAtD,EAAA,CAAAc,aAAA,CAAAyC,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAkD,mBAAA,EAAqB;IAAA,EAAC;IAGjFxD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAY,UAAA,mBAAA6C,uFAAA;MAAAzD,EAAA,CAAAc,aAAA,CAAAyC,IAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoD,mBAAA,EAAqB;IAAA,EAAC;IAGjF1D,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,IAAAiD,uEAAA,qBACsI;IAGxI3D,EAAA,CAAAG,YAAA,EAAM;;;;IAbFH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAA+C,WAAA,YAAAzC,MAAA,CAAAsD,aAAA,iBAA+C;IAD3B5D,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAsD,aAAA,GAA6B;IAMjD5D,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA+C,WAAA,YAAAzC,MAAA,CAAAsD,aAAA,iBAA+C;IAD3B5D,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAsD,aAAA,GAA6B;IAGjD5D,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAA0C,gBAAA,MACF;IACuBhD,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAuD,sBAAA,GAA8B;;;;;IAevD7D,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoC,kBAAA,MAAA9B,MAAA,CAAA+B,WAAA,CAAAyB,SAAA,SAAAxD,MAAA,CAAAyD,UAAA,QACF;;;;;;IARE/D,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAAgE,gBAAA,2BAAAC,8FAAAC,MAAA;MAAAlE,EAAA,CAAAc,aAAA,CAAAqD,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAoE,kBAAA,CAAA9D,MAAA,CAAAyD,UAAA,EAAAG,MAAA,MAAA5D,MAAA,CAAAyD,UAAA,GAAAG,MAAA;MAAA,OAAAlE,EAAA,CAAAkB,WAAA,CAAAgD,MAAA;IAAA,EAAwB;IAAClE,EAAA,CAAAY,UAAA,mBAAAyD,sFAAAH,MAAA;MAAAlE,EAAA,CAAAc,aAAA,CAAAqD,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5ElE,EAAA,CAAAG,YAAA,EACsI;IACtIH,EAAA,CAAAoB,SAAA,kBACiI;IACnIpB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,IAAA6D,oEAAA,kBAC+D;IAGjEvE,EAAA,CAAAG,YAAA,EAAM;;;;IATiBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAwE,gBAAA,YAAAlE,MAAA,CAAAyD,UAAA,CAAwB;IAAkC/D,EAAA,CAAAuB,UAAA,gBAAAjB,MAAA,CAAA+B,WAAA,CAAAoC,iBAAA,CAA6C;IAKtHzE,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyD,UAAA,IAAAzD,MAAA,CAAAoE,kBAAA,GAAwC;;;;;IAS5C1E,EAAA,CAAAC,cAAA,eAC+H;IAC7HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4C,aAAA,MACF;;;;;;IACAlD,EAAA,CAAAC,cAAA,iBAC2H;IAD/ED,EAAA,CAAAY,UAAA,mBAAA+D,gGAAA;MAAA3E,EAAA,CAAAc,aAAA,CAAA8D,IAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAuE,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErE7E,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAIyJ;IAJtGD,EAAA,CAAAY,UAAA,mBAAAkE,gGAAA;MAAA,MAAAC,SAAA,GAAA/E,EAAA,CAAAc,aAAA,CAAAkE,IAAA,EAAAhE,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAuE,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/E/E,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAC,cAAA,gBAA+C;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACnF;;;;;IAJPH,EAFA,CAAA+C,WAAA,qBAAAzC,MAAA,CAAA4C,aAAA,KAAA6B,SAAA,yBAA0E,UAAAzE,MAAA,CAAA4C,aAAA,KAAA6B,SAAA,sBACd,WAAAzE,MAAA,CAAA4C,aAAA,KAAA6B,SAAA,6CACwB;IAGpF/E,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAK,kBAAA,MAAA0E,SAAA,MAAU;IAA+C/E,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA2E,aAAA,CAAAF,SAAA,OAA0B;;;;;IAnBvF/E,EADF,CAAAC,cAAA,cAAkG,cACjB;IAC7ED,EAAA,CAAAoB,SAAA,kBAAkF;IAClFpB,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKjFH,EAJA,CAAAU,UAAA,IAAAwE,qEAAA,mBAC+H,IAAAC,uEAAA,qBAIJ;IAG7HnF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,UAAA,IAAA0E,uEAAA,qBAIyJ;IAK7JpF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnBKH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA4C,aAAA,CAAmB;IAIHlD,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA4C,aAAA,CAAmB;IAMFlD,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA+E,MAAA,CAAS;;;;;IAcrDrF,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAoB,SAAA,mBAAkG;IAClGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;;;;;IAcQH,EAAA,CAAAC,cAAA,gBAIkK;IACxKD,EAAA,CAAAoB,SAAA,mBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAJLH,EAFA,CAAA+C,WAAA,qBAAAzC,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,wCAAuG,UAAAlF,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,uBAC5B,WAAAlF,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,6CACuB;IAGlGxF,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkF,aAAA,CAAA9E,KAAA,MACF;;;;;IACAT,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAtBVH,EAAA,CAAA0B,uBAAA,GAAwE;IAAC1B,EAAA,CAAAC,cAAA,kBAOkL;IANvPD,EAAA,CAAAY,UAAA,mBAAA6E,sGAAA;MAAA,MAAAF,aAAA,GAAAvF,EAAA,CAAAc,aAAA,CAAA4E,IAAA,EAAA1E,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAqF,iBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAoC;IAAA,EAAC;IAO9CxF,EAAA,CAAAC,cAAA,gBACkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAQPH,EARQ,CAAAU,UAAA,IAAAkF,oFAAA,oBAIkK,IAAAC,mFAAA,mBAK4C;IAGxN7F,EAAA,CAAAG,YAAA,EAAS;;;;;;IArBPH,EAAA,CAAAI,SAAA,EAA6I;IAI7IJ,EAJA,CAAA+C,WAAA,qBAAAzC,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,gBAAAlF,MAAA,CAAAwF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,uBAA6I,UAAAlF,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,aAAAlF,MAAA,CAAAwF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,0BACX,WAAAlF,MAAA,CAAAgF,mBAAA,CAAAC,aAAA,CAAAC,OAAA,0BAAAlF,MAAA,CAAAwF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,8CACkC,YAAAlF,MAAA,CAAAyF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,gBAC9F,WAAAlF,MAAA,CAAAyF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,8BACa;IALpCxF,EAAA,CAAAuB,UAAA,aAAAjB,MAAA,CAAAyF,mBAAA,CAAAR,aAAA,CAAAC,OAAA,EAAmD;IAO5FxF,EAAA,CAAAI,SAAA,EAA0F;IAA1FJ,EAAA,CAAA+C,WAAA,oBAAAzC,MAAA,CAAAwF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,4BAA0F;IAE9FxF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkF,aAAA,CAAAjE,SAAA,MACF;IAAetB,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAkB,gBAAA,IAAA+D,aAAA,CAAA9E,KAAA,CAA0C;IAQnDT,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAwF,mBAAA,CAAAP,aAAA,CAAAC,OAAA,EAA4C;;;;;IArBxDxF,EAAA,CAAAC,cAAA,eACgG;IAC9FD,EAAA,CAAAU,UAAA,IAAAsF,6EAAA,6BAAwE;IAyB1EhG,EAAA,CAAAG,YAAA,EAAM;;;;IAzBgCH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAA2F,6BAAA,GAAkC;;;;;IA0BxEjG,EAAA,CAAAC,cAAA,eAEkE;IAChED,EAAA,CAAAoB,SAAA,mBAA0G;IAC1GpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;;;;;IAnINH,EALN,CAAAC,cAAA,cAAgF,cAG6C,cACtD,aACa;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACpFF,EADoF,CAAAG,YAAA,EAAK,EACnF;IACNH,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAU,UAAA,IAAAwF,gEAAA,qBAG4L;IAQhMlG,EADE,CAAAG,YAAA,EAAM,EACF;IAOEH,EAJR,CAAAC,cAAA,cAA6D,cACQ,cACoC,eACxC,cACqB;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAMhHH,EANiH,CAAAU,UAAA,KAAAyF,+DAAA,mBAEc,KAAAC,+DAAA,mBAKA;IAIjIpG,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAA2F,8DAAA,kBAEmC;IAgBrCrG,EAAA,CAAAG,YAAA,EAAM;IAeNH,EAZA,CAAAU,UAAA,KAAA4F,8DAAA,kBAAsE,KAAAC,8DAAA,kBAY4B;IAwBpGvG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuD;IAiCrDD,EAhCA,CAAAU,UAAA,KAAA8F,8DAAA,kBAA+F,KAAAC,8DAAA,kBAKC,KAAAC,8DAAA,kBA6B9B;IAMxE1G,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAnI6BH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAuB,UAAA,YAAAjB,MAAA,CAAAkC,SAAA,CAAY;IAkByCxC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA+B,WAAA,CAAAsE,aAAA,CAA6B;IACxG3G,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA0C,gBAAA,CAAsB;IAKlBhD,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAkB,gBAAA,IAAAlB,MAAA,CAAA4C,aAAA,CAAwC;IAO9ClD,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAsG,gBAAA,IAAAtG,MAAA,CAAA0C,gBAAA,IAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,KAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,EAAA8D,MAAA,KAAyH;IAoBxH9G,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyG,WAAA,IAAAzG,MAAA,CAAA0C,gBAAA,CAAqC;IAYrChD,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAkB,gBAAA,IAAAlB,MAAA,CAAA0C,gBAAA,IAAA1C,MAAA,CAAA+E,MAAA,CAAAyB,MAAA,KAAgE;IA4BhE9G,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAA0C,gBAAA,CAAuB;IAIvBhD,EAAA,CAAAI,SAAA,EAAqG;IAArGJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA0C,gBAAA,IAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,KAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,EAAA8D,MAAA,KAAqG;IA6BxG9G,EAAA,CAAAI,SAAA,EAAyH;IAAzHJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA0C,gBAAA,MAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,KAAA1C,MAAA,CAAAuG,YAAA,CAAAvG,MAAA,CAAA0C,gBAAA,EAAA8D,MAAA,YAAAxG,MAAA,CAAAyD,UAAA,CAAyH;;;;;IAkB9H/D,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAoB,SAAA,mBAAuE;IACvEpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADiBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA0G,aAAA,CAAiB;;;;;IAQ9BhH,EAAA,CAAAC,cAAA,gBACyI;IAC/ID,EAAA,CAAAoB,SAAA,kBAAuF;IACvFpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4C,aAAA,MACF;;;;;IAVFlD,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,SAAA,mBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnBH,EAAA,CAAAC,cAAA,gBAC+H;IAC7HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAU,UAAA,IAAAuG,8DAAA,oBACyI;IAInJjH,EAAA,CAAAG,YAAA,EAAM;;;;IANFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0C,gBAAA,MACF;IAAehD,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAkB,gBAAA,IAAAlB,MAAA,CAAA4C,aAAA,CAAwC;;;;;IAO3DlD,EAAA,CAAAC,cAAA,eAC+G;IAC7GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAoC,kBAAA,sBAAA9B,MAAA,CAAAyD,UAAA,UAAAzD,MAAA,CAAA4G,0BAAA,4BACF;;;;;;IAMElH,EAAA,CAAAC,cAAA,kBACsL;IAD5HD,EAAA,CAAAY,UAAA,mBAAAuG,mFAAA;MAAAnH,EAAA,CAAAc,aAAA,CAAAsG,IAAA;MAAA,MAAA9G,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4B,UAAA,EAAY;IAAA,EAAC;IAE9ElC,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAY,UAAA,mBAAAyG,mFAAA;MAAArH,EAAA,CAAAc,aAAA,CAAAwG,IAAA;MAAA,MAAAhH,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAiH,WAAA,EAAa;IAAA,EAAC;IAE7EvH,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7MXH,EAHN,CAAAC,cAAA,kBAAmE,qBACjD,cACmE,cACpB;IACzDD,EAAA,CAAAoB,SAAA,kBAAkF;IAClFpB,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrGH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAG,YAAA,EAAO,EAChF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EACjF,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAoD;IAWlDD,EATA,CAAAU,UAAA,KAAA8G,uDAAA,kBACmG,KAAAC,uDAAA,oBAQnB;IA4IlFzH,EAAA,CAAAG,YAAA,EAAe;IAMTH,EAJN,CAAAC,cAAA,0BAAiG,eAEO,eACH,eACpC;IACzDD,EAAA,CAAAoB,SAAA,mBAA2E;IAC3EpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAKNH,EAJA,CAAAU,UAAA,KAAAgH,uDAAA,kBAAiF,KAAAC,uDAAA,kBAIG;IAYtF3H,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAAkH,uDAAA,kBAC+G;IAGjH5H,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAU,UAAA,KAAAmH,0DAAA,qBACsL,KAAAC,0DAAA,qBAKA;IAIxL9H,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAY,UAAA,mBAAAmH,0EAAA;MAAA,MAAAC,OAAA,GAAAhI,EAAA,CAAAc,aAAA,CAAAmH,GAAA,EAAAC,SAAA;MAAA,OAAAlI,EAAA,CAAAkB,WAAA,CAAS8G,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCnI,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAY,UAAA,mBAAAwH,0EAAA;MAAA,MAAAJ,OAAA,GAAAhI,EAAA,CAAAc,aAAA,CAAAmH,GAAA,EAAAC,SAAA;MAAA,OAAAlI,EAAA,CAAAkB,WAAA,CAAS8G,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCnI,EAAA,CAAAoB,SAAA,mBAA4C;IAC5CpB,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;;IA3NkEH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA+B,WAAA,CAAAgG,UAAA,CAA0B;IAC3CrI,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkC,SAAA,CAAAsE,MAAA,yBAA0B;IAE5B9G,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAAiC,gBAAA,OAA2B;IAM1EvC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgI,SAAA,CAAe;IASftI,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAgI,SAAA,CAAgB;IAoJGtI,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAiC,gBAAA,GAAsB;IAErCvC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA0G,aAAA,CAAmB;IAInBhH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAA0C,gBAAA,CAAsB;IAaxBhD,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyD,UAAA,CAAgB;IASG/D,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAiI,gBAAA,CAAAzB,MAAA,KAAiC;IAKjC9G,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAyG,WAAA,IAAAzG,MAAA,CAAAyD,UAAA,CAA+B;IAepD/D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAAiC,gBAAA,SACF;;;ADnPV,OAAM,MAAOiG,yBAAyB;EA0BLC,YACrBC,GAAsB,EACtBC,aAA8B;IAD9B,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,aAAa,GAAbA,aAAa;IA1Bd,KAAAjG,WAAW,GAAW,OAAO;IAC7B,KAAAsE,aAAa,GAAkB,IAAI;IACnC,KAAAvF,QAAQ,GAAY,KAAK;IACzB,KAAAmH,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAA/B,YAAY,GAAiB,EAAE;IAAW,KAAAgC,gBAAgB,GAAY,IAAI;IAC1E,KAAA9B,WAAW,GAAY,IAAI;IAC3B,KAAAH,gBAAgB,GAAY,IAAI;IAChC,KAAAkC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAtH,gBAAgB,GAAY,KAAK,CAAC,CAAC;IAElC,KAAAuH,eAAe,GAAG,IAAIlJ,YAAY,EAAmB;IACrD,KAAAmJ,aAAa,GAAG,IAAInJ,YAAY,EAAY,CAAC,CAAC;IAC9C,KAAAoJ,eAAe,GAAG,IAAIpJ,YAAY,EAAY,CAAC,CAAC;IAC1D,KAAAqJ,MAAM,GAAG,KAAK;IACd,KAAAlG,gBAAgB,GAAG,EAAE;IACrB,KAAAe,UAAU,GAAG,EAAE;IAAE,KAAAb,aAAa,GAAG,EAAE,CAAC,CAAC;IACrC,KAAAqF,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAY,kBAAkB,GAAa,EAAE,CAAC,CAAC;IACnC,KAAA3G,SAAS,GAAa,EAAE;IACxB,KAAA6C,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAA+D,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAf,SAAS,GAAY,KAAK,CAAC,CAAC;IACpB,KAAAgB,QAAQ,GAAIC,KAA0B,IAAI,CAAG,CAAC;IAC9C,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAGzB;EAAEC,UAAUA,CAACF,KAAY;IAC3BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAC/H,gBAAgB,CAAC;IAErH,IAAI,CAAC+H,KAAK,IAAIA,KAAK,CAACzC,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACyB,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAACY,kBAAkB,GAAG,EAAE;IAC9B,CAAC,MAAM;MACL,MAAMS,SAAS,GAAGL,KAAK,CAAC,CAAC,CAAC;MAAE,IAAI,IAAI,CAAC/H,gBAAgB,EAAE;QACrD;QACA,IAAI,OAAOoI,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACT,kBAAkB,GAAG,CAAC,GAAG,IAAIU,GAAG,CAACN,KAAiB,CAAC,CAAC,CAAC,CAAC;UAC3D;UACA,IAAI,CAAChB,gBAAgB,GAAG,IAAI,CAACuB,sBAAsB,CAAC,IAAI,CAACX,kBAAkB,CAAC;UAC5EO,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACnD,CAAC,MAAM;UACLD,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAE,OAAOH,SAAS,CAAC;UACtE,IAAI,CAACT,kBAAkB,GAAG,EAAE;UAC5B,IAAI,CAACZ,gBAAgB,GAAG,EAAE;QAC5B;MACF,CAAC,MAAM;QACL;QACA,IAAI,OAAOqB,SAAS,KAAK,QAAQ,EAAE;UACjC,IAAI,CAACrB,gBAAgB,GAAGgB,KAAiB;UACzC;UACA,IAAI,CAACJ,kBAAkB,GAAG,IAAI,CAACa,sBAAsB,CAAC,IAAI,CAACzB,gBAAgB,CAAC;UAC5EmB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACvC,CAAC,MAAM,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;UACxC;UACAF,OAAO,CAACK,KAAK,CAAC,oCAAoC,CAAC;UACnDL,OAAO,CAACK,KAAK,CAAC,2CAA2C,CAAC;UAC1D;QACF,CAAC,MAAM;UACLL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAER,KAAK,CAAC;UAC7C,IAAI,CAAChB,gBAAgB,GAAG,EAAE;UAC1B,IAAI,CAACY,kBAAkB,GAAG,EAAE;QAC9B;MACF;IACF;IAEAO,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;IAC9DmB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,kBAAkB,CAAC;IAClE,IAAI,CAACc,wBAAwB,EAAE;EACjC;EACAC,gBAAgBA,CAACC,EAAwC;IACvD,IAAI,CAACb,QAAQ,GAAGa,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACX,SAAS,GAAGW,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAC7I,QAAQ,GAAG6I,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAAEC,WAAWA,CAACC,OAAsB;IAClC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAAClI,SAAS,GAAGmI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,YAAY,IAAI,EAAE,CAAC;MACrD6C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC9C,YAAY,CAAC;MACvD,IAAI,CAACgE,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACb,gBAAgB,CAAC;IACnE;IACA,IAAI4B,OAAO,CAAC,kBAAkB,CAAC,EAAE;MAC/B;MACAhB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACnI,gBAAgB,CAAC;MAClE;MACA,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAAC0B,aAAa,GAAG,EAAE;MACzB;IACF;EACF;EAAUsH,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAAC3D,YAAY,IAAI8D,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,YAAY,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAClE,IAAI,CAACtE,SAAS,GAAGmI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/D,YAAY,CAAC;MAC/C6C,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACnH,SAAS,CAAC;MAChF,IAAI,CAACyH,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAACzH,SAAS,GAAG,EAAE;MACnBkH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF;EAAUM,wBAAwBA,CAAA;IAChC,MAAMa,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACvC,gBAAgB,CAACwC,OAAO,CAACvF,OAAO,IAAG;MACtC,KAAK,MAAMwF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;QACrC,MAAMyI,IAAI,GAAG,IAAI,CAACpE,YAAY,CAACmE,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3F,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIyF,IAAI,EAAE;UACR,IAAI,CAACH,OAAO,CAACE,QAAQ,CAAC,EAAEF,OAAO,CAACE,QAAQ,CAAC,GAAG,EAAE;UAC9CF,OAAO,CAACE,QAAQ,CAAC,CAACI,IAAI,CAAC5F,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC6D,kBAAkB,GAAGyB,OAAO;EACnC;EAAEhI,gBAAgBA,CAACkI,QAAgB;IACjCtB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;IAC3C,IAAI,CAAChI,gBAAgB,GAAGgI,QAAQ;IAChC,IAAI,CAAC9H,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACa,UAAU,GAAG,EAAE;IACpB,IAAI,CAACsH,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAACR,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACP,kBAAkB,CAACtC,MAAM,CAAC;IACzE;IACA,IAAI,CAAC4B,GAAG,CAAC4C,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACP,QAAgB;IAC9BtB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEqB,QAAQ,CAAC;EACxD;EAAEH,wBAAwBA,CAAA;IACxBnB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC3G,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,EAAE,mBAAmB,EAAE,IAAI,CAAC1B,gBAAgB,CAAC;IAC1J,IAAI,CAAC,IAAI,CAACwB,gBAAgB,EAAE;MAC1B,IAAI,CAACoG,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMoC,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACjE0G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE6B,UAAU,CAAC1E,MAAM,CAAC;IAEpE;IACA,MAAM2E,aAAa,GAAGD,UAAU,CAACE,MAAM,CAACP,CAAC,IAAG;MAC1C;MACA,MAAMQ,UAAU,GAAG,IAAI,CAACnK,gBAAgB,IAAI,CAAC,IAAI,CAAC0B,aAAa,IAAIiI,CAAC,CAAC1K,KAAK,KAAK,IAAI,CAACyC,aAAa;MACjG;MACA,MAAM0I,WAAW,GAAG,CAAC,IAAI,CAAC7H,UAAU,IAAIoH,CAAC,CAAC7J,SAAS,CAACuK,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/H,UAAU,CAAC8H,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF;IACA,IAAI,CAACxC,kBAAkB,GAAGqC,aAAa,CAACM,GAAG,CAACZ,CAAC,IAAIA,CAAC,CAAC7J,SAAS,CAAC;IAE7DoI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACP,kBAAkB,CAACtC,MAAM,CAAC;IAC1E4C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE8B,aAAa,CAACM,GAAG,CAACZ,CAAC,IAAI,GAAGA,CAAC,CAAC7J,SAAS,IAAI6J,CAAC,CAAC1K,KAAK,QAAQ0K,CAAC,CAAC3F,OAAO,GAAG,CAAC,CAAC;EAC/G;EAEAlB,cAAcA,CAAC0H,KAAU;IACvB,IAAI,CAACjI,UAAU,GAAGiI,KAAK,CAACC,MAAM,CAAC1C,KAAK;IACpC,IAAI,CAACsB,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5F,UAAU,CAAC;EACtD;EACAwD,WAAWA,CAAA;IACT,IAAI,CAACxD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC8G,wBAAwB,EAAE;IAC/BnB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EAAEhE,iBAAiBA,CAACH,OAA2B;IAC7CkE,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEnE,OAAO,CAAC;IAC9DkE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;IAE/D,IAAI,CAAC/C,OAAO,EAAE;MACZkE,OAAO,CAACC,GAAG,CAAC,gBAAgBnE,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAAC0G,iBAAiB,CAAC1G,OAAO,CAAC,EAAE;MACnCkE,OAAO,CAACC,GAAG,CAAC,SAASnE,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA;IACA,MAAM2G,gBAAgB,GAAG,IAAI,CAACC,qBAAqB,CAAC5G,OAAO,CAAC;IAC5D,IAAI,CAAC2G,gBAAgB,EAAE;MACrBzC,OAAO,CAACC,GAAG,CAAC,eAAenE,OAAO,UAAU,CAAC;MAC7C;IACF;IAEA,IAAI6G,YAAsB;IAE1B,IAAI,IAAI,CAAC7K,gBAAgB,EAAE;MACzB;MACA,MAAMF,SAAS,GAAG6K,gBAAgB,CAAC7K,SAAS;MAC5C,MAAMgL,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACjL,SAAS,CAAC;MAErE;MACA,MAAMkL,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACuD,QAAQ,CAACY,EAAE,CAAC,CAAC;MAEzF,IAAIF,cAAc,EAAE;QAClB;QACAH,YAAY,GAAG,IAAI,CAAC9D,gBAAgB,CAACmD,MAAM,CAACgB,EAAE,IAAI,CAACJ,mBAAmB,CAACR,QAAQ,CAACY,EAAE,CAAC,CAAC;QACpFhD,OAAO,CAACC,GAAG,CAAC,+BAA+BrI,SAAS,IAAI,EAAEgL,mBAAmB,CAAC;MAChF,CAAC,MAAM;QACL;QACA,IAAI,IAAI,CAACtF,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACzB,MAAM,IAAI,IAAI,CAACE,aAAa,EAAE;UAC5E0C,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACA0C,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC9D,gBAAgB,EAAE+D,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACjE5C,OAAO,CAACC,GAAG,CAAC,2BAA2BrI,SAAS,WAAW,EAAEgL,mBAAmB,CAAC,CAAC,CAAC,CAAC;MACtF;IACF,CAAC,MAAM;MACL;MACA,MAAMK,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAACpH,OAAO,CAAC;MAElD,IAAImH,UAAU,EAAE;QACdN,YAAY,GAAG,IAAI,CAAC9D,gBAAgB,CAACmD,MAAM,CAACgB,EAAE,IAAIA,EAAE,KAAKlH,OAAO,CAAC;MACnE,CAAC,MAAM;QACL,IAAI,IAAI,CAACwB,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACzB,MAAM,IAAI,IAAI,CAACE,aAAa,EAAE;UAC5E0C,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;UACxB;QACF;QACA0C,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC9D,gBAAgB,EAAE/C,OAAO,CAAC;MACpD;IACF;IAEA,IAAI,CAAC+C,gBAAgB,GAAG8D,YAAY;IACpC,IAAI,CAACQ,WAAW,EAAE;EACpB;EACA1L,iBAAiBA,CAACqE,OAAe;IAC/B,IAAI,CAAC+C,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmD,MAAM,CAACgB,EAAE,IAAIA,EAAE,KAAKlH,OAAO,CAAC;IAC1E,IAAI,CAACqH,WAAW,EAAE;EACpB;EAAErJ,mBAAmBA,CAAA;IACnBkG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC3G,gBAAgB,CAAC;IACvD0G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACzG,aAAa,CAAC;IACjDwG,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC5F,UAAU,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACf,gBAAgB,EAAE;MAC1B0G,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA;IACA,MAAMmD,sBAAsB,GAAG,IAAI,CAAC7G,6BAA6B,EAAE;IAEnEyD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmD,sBAAsB,CAACf,GAAG,CAACZ,CAAC,IAAI,GAAGA,CAAC,CAAC7J,SAAS,IAAI6J,CAAC,CAAC1K,KAAK,QAAQ0K,CAAC,CAAC3F,OAAO,GAAG,CAAC,CAAC;IAExH,IAAIsH,sBAAsB,CAAChG,MAAM,KAAK,CAAC,EAAE;MACvC4C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;IACF;IAEA;IACA,MAAMoD,YAAY,GAAG,IAAI,CAACxE,gBAAgB,CAACzB,MAAM;IACjD,MAAMkG,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIN,sBAAsB,EAAE;MAC9C,IAAIM,SAAS,CAAC5H,OAAO,EAAE;QACrB,IAAI,IAAI,CAAChE,gBAAgB,EAAE;UACzB;UACA,MAAM8K,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAAC9L,SAAS,CAAC;UAC/E,MAAMkL,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACuD,QAAQ,CAACY,EAAE,CAAC,CAAC;UACzF,MAAMW,cAAc,GAAGf,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACR,iBAAiB,CAACQ,EAAE,CAAC,CAAC;UAEjF,IAAI,CAACF,cAAc,IAAI,CAACa,cAAc,EAAE;YACtCF,qBAAqB,CAAC/B,IAAI,CAACkB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAAC,IAAI,CAACM,iBAAiB,CAACQ,SAAS,CAAC5H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC0G,iBAAiB,CAACkB,SAAS,CAAC5H,OAAO,CAAC,EAAE;YAC5F2H,qBAAqB,CAAC/B,IAAI,CAACgC,SAAS,CAAC5H,OAAO,CAAC;UAC/C;QACF;MACF;IACF;IAEAkE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwD,qBAAqB,CAAC;IAE9D;IACA,MAAMG,KAAK,GAAGH,qBAAqB,CAACI,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACxG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACyB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAG+E,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClBnD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAACxG,MAAM,WAAW,EAAEwG,KAAK,CAAC;IAC1D,CAAC,MAAM;MACL5D,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF;EAAEjG,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAMwK,kBAAkB,GAAG,IAAI,CAAC3G,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAM+J,YAAY,GAAG,IAAI,CAACxE,gBAAgB,CAACzB,MAAM;IACjD,MAAMkG,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMU,qBAAqB,GAAa,EAAE;IAE1C,IAAI,IAAI,CAACjM,gBAAgB,EAAE;MACzB;MACA,MAAMkM,mBAAmB,GAAG,IAAI7D,GAAG,EAAU;MAE7C,KAAK,MAAMuD,SAAS,IAAII,kBAAkB,EAAE;QAC1C,IAAIJ,SAAS,CAAC5H,OAAO,IAAI4H,SAAS,CAAC9L,SAAS,IAAI,CAACoM,mBAAmB,CAACC,GAAG,CAACP,SAAS,CAAC9L,SAAS,CAAC,EAAE;UAC7FoM,mBAAmB,CAACE,GAAG,CAACR,SAAS,CAAC9L,SAAS,CAAC;UAE5C,MAAMgL,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAAC9L,SAAS,CAAC;UAC/E,MAAMkL,cAAc,GAAGF,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACuD,QAAQ,CAACY,EAAE,CAAC,CAAC;UACzF,MAAMW,cAAc,GAAGf,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAAC5G,mBAAmB,CAAC4G,EAAE,CAAC,CAAC;UAEnF,IAAI,CAACF,cAAc,IAAI,CAACa,cAAc,EAAE;YACtCI,qBAAqB,CAACrC,IAAI,CAACkB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD;QACF;MACF;IACF,CAAC,MAAM;MACL;MACA,KAAK,MAAMc,SAAS,IAAII,kBAAkB,EAAE;QAC1C,IAAIJ,SAAS,CAAC5H,OAAO,IACnB,CAAC,IAAI,CAAC+C,gBAAgB,CAACuD,QAAQ,CAACsB,SAAS,CAAC5H,OAAO,CAAC,IAClD,CAAC,IAAI,CAACM,mBAAmB,CAACsH,SAAS,CAAC5H,OAAO,CAAC,EAAE;UAC9CiI,qBAAqB,CAACrC,IAAI,CAACgC,SAAS,CAAC5H,OAAO,CAAC;QAC/C;MACF;IACF;IAEA;IACA,MAAM8H,KAAK,GAAGG,qBAAqB,CAACF,KAAK,CAAC,CAAC,EAAEL,cAAc,CAAC;IAE5D,IAAII,KAAK,CAACxG,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACyB,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAG+E,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClBnD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAACxG,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAzD,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAMwK,kBAAkB,GAAG,IAAI,CAAC3G,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAM6K,gBAAgB,GAAGL,kBAAkB,CAACzB,GAAG,CAACZ,CAAC,IAAIA,CAAC,CAAC3F,OAAO,CAAC,CAACkG,MAAM,CAACgB,EAAE,IAAIA,EAAE,KAAKoB,SAAS,CAAa;IAC1G,IAAI,CAACvF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmD,MAAM,CAACgB,EAAE,IAAI,CAACmB,gBAAgB,CAAC/B,QAAQ,CAACY,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACG,WAAW,EAAE;EACpB;EAEA3K,UAAUA,CAAA;IACR,IAAI,CAACqG,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACsE,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAAC5C,wBAAwB,EAAE;IAC/BP,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAI;IAC/E,IAAI,IAAI,CAAC/G,gBAAgB,EAAE;MACzB;MACA,IAAI,CAAC2H,kBAAkB,GAAG,IAAI,CAACa,sBAAsB,CAAC,IAAI,CAACzB,gBAAgB,CAAC;MAC5E,MAAMwF,gBAAgB,GAAG,CAAC,GAAG,IAAIlE,GAAG,CAAC,IAAI,CAACV,kBAAkB,CAAC,CAAC,CAAC,CAAC;MAChEO,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoE,gBAAgB,CAAC;MAClE,IAAI,CAACzE,QAAQ,CAAC,CAAC,GAAGyE,gBAAgB,CAAC,CAAC;MACpC,IAAI,CAAC9E,eAAe,CAAC+E,IAAI,CAAC,CAAC,GAAGD,gBAAgB,CAAC,CAAC;IAClD,CAAC,MAAM;MACL;MACArE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACpB,gBAAgB,CAAC;MAC3D,IAAI,CAACe,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACf,gBAAgB,CAAC,CAAC;MAEzC;MACA,MAAM0F,QAAQ,GAAG,IAAI,CAAC1F,gBAAgB,CAACmD,MAAM,CAACgB,EAAE,IAAIA,EAAE,KAAKoB,SAAS,CAAC;MACrEpE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsE,QAAQ,CAAC;MAC3C,IAAI,CAACjF,aAAa,CAACgF,IAAI,CAACC,QAAQ,CAAC;IACnC;IAEA,IAAI,CAACzE,SAAS,EAAE;IAEhB;IACA,MAAM0E,aAAa,GAAG,IAAI,CAAC3F,gBAAgB,CAACwD,GAAG,CAACvG,OAAO,IAAG;MACxD,KAAK,MAAMwF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;QACrC,MAAMyI,IAAI,GAAG,IAAI,CAACpE,YAAY,CAACmE,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3F,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAIyF,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACS,MAAM,CAACT,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnDvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuE,aAAa,CAAC;IACrD,IAAI,CAACnF,eAAe,CAACiF,IAAI,CAACE,aAAa,CAAC;EAC1C;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC1M,QAAQ,EAAE;MAClB,IAAI,CAAC2M,UAAU,EAAE;MACjB1E,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnH,SAAS,CAAC;IACrD;EACF;EAEA4L,UAAUA,CAAA;IACR,IAAI,CAACzF,aAAa,CAAC0F,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EACArJ,mBAAmBA,CAACE,OAA2B;IAC/C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B,IAAI,IAAI,CAAChE,gBAAgB,EAAE;MACzB;MACA,MAAM4L,SAAS,GAAG,IAAI,CAAChB,qBAAqB,CAAC5G,OAAO,CAAC;MACrD,IAAI4H,SAAS,EAAE;QACb,MAAMd,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACa,SAAS,CAAC9L,SAAS,CAAC;QAC/E,OAAOgL,mBAAmB,CAACG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACnE,gBAAgB,CAACuD,QAAQ,CAACY,EAAE,CAAC,CAAC;MAC3E;MACA,OAAO,KAAK;IACd,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACnE,gBAAgB,CAACuD,QAAQ,CAACtG,OAAO,CAAC;IAChD;EACF;EAEAM,mBAAmBA,CAACN,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACsD,gBAAgB,CAACgD,QAAQ,CAACtG,OAAO,CAAC;EAChD;EAEAO,mBAAmBA,CAACP,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACM,mBAAmB,CAACN,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC5B,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC0B,mBAAmB,CAACE,OAAO,CAAE;EACjE;EACA5B,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACoD,aAAa,IAAI,IAAI,CAACuB,gBAAgB,CAACzB,MAAM,GAAG,IAAI,CAACE,aAAa;EACjF;EAAE4H,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC5L,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwK,kBAAkB,GAAG,IAAI,CAAC3G,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,CAChE0I,MAAM,CAACP,CAAC,IAAI,CAACA,CAAC,CAACb,UAAU,IAAIa,CAAC,CAAC3F,OAAO,KAAKsI,SAAS,CAAC;IACxD,OAAON,kBAAkB,CAAC1G,MAAM,GAAG,CAAC,IAClC0G,kBAAkB,CAACqB,KAAK,CAACzB,SAAS,IAAIA,SAAS,CAAC5H,OAAO,IAAI,IAAI,CAAC+C,gBAAgB,CAACuD,QAAQ,CAACsB,SAAS,CAAC5H,OAAO,CAAC,CAAC;EACjH;EAEA3B,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMwK,kBAAkB,GAAG,IAAI,CAAC3G,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAOwK,kBAAkB,CAACf,IAAI,CAACW,SAAS,IAAIA,SAAS,CAAC5H,OAAO,IAAI,IAAI,CAAC+C,gBAAgB,CAACuD,QAAQ,CAACsB,SAAS,CAAC5H,OAAO,CAAC,CAAC;EACrH;EAAEsJ,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAACzF,kBAAkB;EAChC;EACApG,gBAAgBA,CAAC+H,QAAgB;IAC/B,IAAI,IAAI,CAACxJ,gBAAgB,EAAE;MACzB;MACA,MAAMgK,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM+C,gBAAgB,GAAG,IAAIlE,GAAG,CAAC2B,UAAU,CAACO,GAAG,CAACZ,CAAC,IAAIA,CAAC,CAAC7J,SAAS,CAAC,CAAC;MAClE,OAAOyM,gBAAgB,CAACgB,IAAI;IAC9B,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAAClI,YAAY,CAACmE,QAAQ,CAAC,EAAElE,MAAM,IAAI,CAAC;IACjD;EACF;EACAvE,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACzB;MACA,MAAMuM,gBAAgB,GAAG,IAAI,CAAC/D,sBAAsB,CAAC,IAAI,CAACzB,gBAAgB,CAAC;MAC3E,OAAOwF,gBAAgB,CAACjH,MAAM;IAChC,CAAC,MAAM;MACL;MACA,OAAO,IAAI,CAACyB,gBAAgB,CAACzB,MAAM;IACrC;EACF;EAEA;EACAjF,2BAA2BA,CAACmJ,QAAgB;IAC1C,OAAO,IAAI,CAAC3B,kBAAkB,CAAC2B,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAjJ,mBAAmBA,CAACiJ,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAC3B,kBAAkB,CAAC2B,QAAQ,CAAC,IAAI,IAAI,CAAC3B,kBAAkB,CAAC2B,QAAQ,CAAC,CAAClE,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQuE,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACrI,gBAAgB,EAAE;MAC1B,IAAI,CAACqC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMmG,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMgM,QAAQ,GAAG,IAAInF,GAAG,EAAU;IAElC2B,UAAU,CAACT,OAAO,CAACqC,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAC3M,KAAK,EAAE;QACnBuO,QAAQ,CAACpB,GAAG,CAACR,SAAS,CAAC3M,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC4E,MAAM,GAAG4J,KAAK,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEF/F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC3G,gBAAgB,EAAE,IAAI,CAACqC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAACpE,KAAa;IACzBiJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElJ,KAAK,CAAC;IACrC,IAAI,CAACyC,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKzC,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACoK,wBAAwB,EAAE;IAC/B,IAAI,CAACnC,GAAG,CAAC4C,aAAa,EAAE;EAC1B;EAEA;EACArG,aAAaA,CAACxE,KAAa;IACzB,IAAI,CAAC,IAAI,CAACuC,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMwI,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOwI,UAAU,CAACE,MAAM,CAACP,CAAC,IAAIA,CAAC,CAAC1K,KAAK,KAAKA,KAAK,CAAC,CAACqG,MAAM;EACzD;EACA;EACA4I,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAC3M,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMwI,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMoK,SAAS,GAAG5B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7J,SAAS,KAAKqO,aAAa,CAAC;IACrE,OAAOvC,SAAS,EAAE3M,KAAK,IAAI,EAAE;EAC/B;EACA;EACAmP,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAM3E,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMoC,SAAS,GAAG5B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7J,SAAS,KAAKqO,aAAa,CAAC;MACrE,IAAIvC,SAAS,EAAE;QACb,OAAO;UACL9L,SAAS,EAAE8L,SAAS,CAAC9L,SAAS;UAC9Bb,KAAK,EAAE2M,SAAS,CAAC3M,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEa,SAAS,EAAEqO,aAAa;MAAElP,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAF,oBAAoBA,CAACiF,OAAe;IAClC,KAAK,MAAMwF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMoC,SAAS,GAAG5B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3F,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI4H,SAAS,EAAE;QACb,OAAO;UACL9L,SAAS,EAAE8L,SAAS,CAAC9L,SAAS;UAC9Bb,KAAK,EAAE2M,SAAS,CAAC3M,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEa,SAAS,EAAE,MAAMkE,OAAO,EAAE;MAAE/E,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACAiE,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACX,UAAU,IAAI,CAAC,IAAI,CAACf,gBAAgB,IAAI,CAAC,IAAI,CAAC6D,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAAE,MAAM6M,QAAQ,GAAG,IAAI,CAAChJ,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,CAAC0I,MAAM,CAACP,CAAC,IAAG;MACrE,MAAMQ,UAAU,GAAG,IAAI,CAACnK,gBAAgB,IAAI,CAAC,IAAI,CAAC0B,aAAa,IAAIiI,CAAC,CAAC1K,KAAK,KAAK,IAAI,CAACyC,aAAa;MACjG,MAAM0I,WAAW,GAAGT,CAAC,CAAC7J,SAAS,CAACuK,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/H,UAAU,CAAC8H,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAOiE,QAAQ,CAAC/I,MAAM,KAAK,CAAC;EAC9B;EACA;EACAI,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAACjB,6BAA6B,EAAE,CAACa,MAAM;EACpD;EAEA;EACAgJ,oBAAoBA,CAAC1C,SAAwB;IAC3C,OAAOA,SAAS,CAAC5H,OAAO,GAAG4H,SAAS,CAAC5H,OAAO,CAACuK,QAAQ,EAAE,GAAG,GAAG3C,SAAS,CAAC9L,SAAS,IAAI8L,SAAS,CAAC3M,KAAK,EAAE;EACvG;EACA;EACQ2L,qBAAqBA,CAAC5G,OAAe;IAC3C,KAAK,MAAMwF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMoC,SAAS,GAAG5B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3F,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAI4H,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACK4C,qBAAqBA,CAAC1O,SAAiB;IAC7C,MAAM2O,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAMjF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkF,OAAO,GAAG1E,UAAU,CAACE,MAAM,CAACP,CAAC,IAAIA,CAAC,CAAC7J,SAAS,KAAKA,SAAS,CAAC;MACjE4O,OAAO,CAACnF,OAAO,CAACqC,SAAS,IAAG;QAC1B6C,kBAAkB,CAAC7E,IAAI,CAAC;UAAEJ,QAAQ;UAAEoC;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA1D,OAAO,CAACC,GAAG,CAAC,iBAAiBrI,SAAS,QAAQ,EAAE2O,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAACnJ,MAAM,KAAK,CAAC,EAAE;MACnC4C,OAAO,CAACyG,IAAI,CAAC,kBAAkB7O,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAI2O,kBAAkB,CAACnJ,MAAM,GAAG,CAAC,EAAE;MACjC4C,OAAO,CAACyG,IAAI,CAAC,aAAa7O,SAAS,IAAI,EAAE2O,kBAAkB,CAAClE,GAAG,CAACqE,CAAC,IAAI,GAAGA,CAAC,CAACpF,QAAQ,IAAIoF,CAAC,CAAChD,SAAS,CAAC3M,KAAK,EAAE,CAAC,CAAC;MAC3GiJ,OAAO,CAACyG,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAACjF,QAAQ,IAAIiF,kBAAkB,CAAC,CAAC,CAAC,CAAC7C,SAAS,CAAC3M,KAAK,EAAE,CAAC;IACvG;IAEA,MAAM4P,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAACjD,SAAS,CAAC5H,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQ+G,yBAAyBA,CAACjL,SAAiB;IACjD,MAAM2M,QAAQ,GAAa,EAAE;IAE7B;IACA,KAAK,MAAMjD,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkF,OAAO,GAAG1E,UAAU,CAACE,MAAM,CAACP,CAAC,IAAIA,CAAC,CAAC7J,SAAS,KAAKA,SAAS,CAAC;MACjE4O,OAAO,CAACnF,OAAO,CAACqC,SAAS,IAAG;QAC1B,IAAIA,SAAS,CAAC5H,OAAO,EAAE;UACrByI,QAAQ,CAAC7C,IAAI,CAACgC,SAAS,CAAC5H,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IAEA,OAAOyI,QAAQ;EACjB;EAEA;EACQrB,iBAAiBA,CAACpH,OAAe;IACvC,OAAO,IAAI,CAAC+C,gBAAgB,CAACuD,QAAQ,CAACtG,OAAO,CAAC;EAChD;EAEA;EACQ0G,iBAAiBA,CAAC1G,OAAe;IACvC,OAAO,IAAI,CAACsD,gBAAgB,CAACgD,QAAQ,CAACtG,OAAO,CAAC;EAChD;EACA;EACA8K,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAMvF,QAAQ,IAAI,IAAI,CAACxI,SAAS,EAAE;MACrC,MAAMgJ,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAACmE,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMoC,SAAS,GAAG5B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAI,IAAI,CAAC2E,oBAAoB,CAAC3E,CAAC,CAAC,KAAKoF,QAAQ,CAAC;MACjF,IAAInD,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb,CAAC,CAAE;EACKtD,sBAAsBA,CAAC0G,UAAoB;IACjD,MAAMvC,QAAQ,GAAa,EAAE;IAC7B,MAAMF,gBAAgB,GAAG,CAAC,GAAG,IAAIlE,GAAG,CAAC2G,UAAU,CAAC,CAAC,CAAC,CAAC;IAEnD,KAAK,MAAMlP,SAAS,IAAIyM,gBAAgB,EAAE;MACxC,MAAM0C,gBAAgB,GAAG,IAAI,CAAClE,yBAAyB,CAACjL,SAAS,CAAC;MAClE,IAAImP,gBAAgB,CAAC3J,MAAM,GAAG,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACtF,gBAAgB,EAAE;UACzB;UACAyM,QAAQ,CAAC7C,IAAI,CAACqF,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAClC,IAAIA,gBAAgB,CAAC3J,MAAM,GAAG,CAAC,EAAE;YAC/B4C,OAAO,CAACC,GAAG,CAAC,SAASrI,SAAS,OAAOmP,gBAAgB,CAAC3J,MAAM,oCAAoC,EAAE2J,gBAAgB,CAAC,CAAC,CAAC,CAAC;UACxH;QACF,CAAC,MAAM;UACL;UACAxC,QAAQ,CAAC7C,IAAI,CAAC,GAAGqF,gBAAgB,CAAC;UAClC,IAAIA,gBAAgB,CAAC3J,MAAM,GAAG,CAAC,EAAE;YAC/B4C,OAAO,CAACyG,IAAI,CAAC,SAAS7O,SAAS,OAAOmP,gBAAgB,CAAC3J,MAAM,iBAAiB,EAAE2J,gBAAgB,CAAC;UACnG;QACF;MACF,CAAC,MAAM;QACL/G,OAAO,CAACyG,IAAI,CAAC,aAAa7O,SAAS,eAAe,CAAC;MACrD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAIuI,GAAG,CAACoE,QAAQ,CAAC,CAAC;EAC/B;EACA;EACQjE,sBAAsBA,CAACiE,QAAkB;IAC/C,MAAMuC,UAAU,GAAa,EAAE;IAC/B,MAAME,cAAc,GAAG,CAAC,GAAG,IAAI7G,GAAG,CAACoE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/C,KAAK,MAAMzI,OAAO,IAAIkL,cAAc,EAAE;MACpC,MAAMC,aAAa,GAAG,IAAI,CAACpQ,oBAAoB,CAACiF,OAAO,CAAC;MACxD,IAAImL,aAAa,CAACrP,SAAS,IAAI,CAACqP,aAAa,CAACrP,SAAS,CAACsP,UAAU,CAAC,KAAK,CAAC,EAAE;QACzEJ,UAAU,CAACpF,IAAI,CAACuF,aAAa,CAACrP,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLoI,OAAO,CAACyG,IAAI,CAAC,gBAAgB3K,OAAO,UAAU,CAAC;MACjD;IACF;IAEA;IACA,OAAO,CAAC,GAAG,IAAIqE,GAAG,CAAC2G,UAAU,CAAC,CAAC;EACjC;EAEA;EACAvK,6BAA6BA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACjD,gBAAgB,IAAI,CAAC,IAAI,CAAC6D,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,EAAE;MACvE,OAAO,EAAE;IACX;IAEA,MAAMwI,UAAU,GAAG,IAAI,CAAC3E,YAAY,CAAC,IAAI,CAAC7D,gBAAgB,CAAC,IAAI,EAAE;IAEjE,IAAI,CAAC,IAAI,CAACxB,gBAAgB,EAAE;MAC1B;MACA,OAAOgK,UAAU,CAACE,MAAM,CAACP,CAAC,IAAG;QAC3B,MAAMQ,UAAU,GAAG,CAAC,IAAI,CAACzI,aAAa,IAAIiI,CAAC,CAAC1K,KAAK,KAAK,IAAI,CAACyC,aAAa;QACxE,MAAM0I,WAAW,GAAG,CAAC,IAAI,CAAC7H,UAAU,IAAIoH,CAAC,CAAC7J,SAAS,CAACuK,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/H,UAAU,CAAC8H,WAAW,EAAE,CAAC;QACzG,OAAOF,UAAU,IAAIC,WAAW;MAClC,CAAC,CAAC;IACJ;IAEA;IACA,MAAMmC,gBAAgB,GAAG,IAAIlE,GAAG,EAAU;IAC1C,MAAMgH,gBAAgB,GAAoB,EAAE;IAE5C,KAAK,MAAMzD,SAAS,IAAI5B,UAAU,EAAE;MAClC;MACA,MAAMI,WAAW,GAAG,CAAC,IAAI,CAAC7H,UAAU,IAAIqJ,SAAS,CAAC9L,SAAS,CAACuK,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/H,UAAU,CAAC8H,WAAW,EAAE,CAAC;MAEjH,IAAID,WAAW,IAAI,CAACmC,gBAAgB,CAACJ,GAAG,CAACP,SAAS,CAAC9L,SAAS,CAAC,EAAE;QAC7DyM,gBAAgB,CAACH,GAAG,CAACR,SAAS,CAAC9L,SAAS,CAAC;QACzCuP,gBAAgB,CAACzF,IAAI,CAACgC,SAAS,CAAC;MAClC;IACF;IAEA1D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkH,gBAAgB,CAAC9E,GAAG,CAACZ,CAAC,IAAIA,CAAC,CAAC7J,SAAS,CAAC,CAAC;IACrF,OAAOuP,gBAAgB;EACzB;EAEA;EACA,IAAIxO,WAAWA,CAAA;IACb,OAAO;MACLyO,QAAQ,EAAE,IAAI,CAACtP,gBAAgB,GAAG,IAAI,GAAG,IAAI;MAC7CkB,WAAW,EAAE,IAAI,CAAClB,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACtDc,cAAc,EAAE,IAAI,CAACd,gBAAgB,GAAG,OAAO,GAAG,OAAO;MACzD6G,UAAU,EAAE,IAAI,CAAC7G,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACnDmF,aAAa,EAAE,IAAI,CAACnF,gBAAgB,GAAG,MAAM,GAAG,MAAM;MACtDiD,iBAAiB,EAAE,IAAI,CAACjD,gBAAgB,GAAG,WAAW,GAAG,WAAW;MACpEiB,aAAa,EAAE,IAAI,CAACjB,gBAAgB,GAAG,KAAK,GAAG,KAAK;MACpDsC,SAAS,EAAE,IAAI,CAACtC,gBAAgB,GAAG,UAAU,GAAG,UAAU;MAC1DuP,WAAW,EAAE,IAAI,CAACvP,gBAAgB,GAAG,YAAY,GAAG;KACrD;EACH;;;uCA3vBWgH,yBAAyB,EAAAxI,EAAA,CAAAgR,iBAAA,CAAAhR,EAAA,CAAAiR,iBAAA,GAAAjR,EAAA,CAAAgR,iBAAA,CAAAE,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzB3I,yBAAyB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;uCARzB,CACT;QACEE,OAAO,EAAE1R,iBAAiB;QAC1B2R,WAAW,EAAE5R,UAAU,CAAC,MAAM0I,yBAAyB,CAAC;QACxDmJ,KAAK,EAAE;OACR,CACF,GAAA3R,EAAA,CAAA4R,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC5BHvR,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAU,UAAA,IAAAwR,wCAAA,kBAA8F;UAgC5FlS,EADF,CAAAC,cAAA,aAAgC,gBAGmL;UAD5KD,EAAA,CAAAY,UAAA,mBAAAuR,2DAAA;YAAAnS,EAAA,CAAAc,aAAA,CAAAsR,GAAA;YAAA,OAAApS,EAAA,CAAAkB,WAAA,CAASsQ,GAAA,CAAArD,cAAA,EAAgB;UAAA,EAAC;UAE7DnO,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAU,UAAA,IAAA2R,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnCtS,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAoB,SAAA,iBAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAU,UAAA,IAAA6R,gDAAA,kCAAAvS,EAAA,CAAAwS,sBAAA,CAA6D;;;UAlDrDxS,EAAA,CAAAI,SAAA,EAAqD;UAArDJ,EAAA,CAAAuB,UAAA,SAAAiQ,GAAA,CAAA3I,gBAAA,IAAA2I,GAAA,CAAAjJ,gBAAA,CAAAzB,MAAA,KAAqD;UAgCX9G,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAyS,WAAA,aAAAjB,GAAA,CAAA/P,QAAA,IAAA+P,GAAA,CAAAlJ,SAAA,CAAwC;UACpFtI,EAAA,CAAAuB,UAAA,aAAAiQ,GAAA,CAAA/P,QAAA,IAAA+P,GAAA,CAAAlJ,SAAA,CAAkC;UAGjBtI,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAuB,UAAA,SAAAiQ,GAAA,CAAAlJ,SAAA,CAAe;UAIftI,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAuB,UAAA,UAAAiQ,GAAA,CAAAlJ,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}