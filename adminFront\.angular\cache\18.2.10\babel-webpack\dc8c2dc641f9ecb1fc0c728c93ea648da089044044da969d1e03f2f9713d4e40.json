{"ast": null, "code": "import { signal } from '@angular/core';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { mergeMap, tap } from 'rxjs';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { CommonModule } from '@angular/common';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@nebular/theme\";\nimport * as i7 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i8 from \"@fullcalendar/angular\";\nimport * as i9 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction AvailableTimeSlotComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.CBuildCaseName, \"\");\n  }\n}\nfunction AvailableTimeSlotComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AvailableTimeSlotComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.searchPreOrderSetting());\n    });\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵtext(2, \"\\u67E5\\u8A62 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailableTimeSlotComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AvailableTimeSlotComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.gotoEdit());\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailableTimeSlotComponent_ng_template_26_Template(rf, ctx) {}\nexport class AvailableTimeSlotComponent extends BaseComponent {\n  constructor(_allow, changeDetector, preOderSettingService, buildCaseService, router) {\n    super(_allow);\n    this._allow = _allow;\n    this.changeDetector = changeDetector;\n    this.preOderSettingService = preOderSettingService;\n    this.buildCaseService = buildCaseService;\n    this.router = router;\n    this.listEvent = [];\n    this.calendarOptions = signal({\n      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n      locale: 'zh-tw',\n      headerToolbar: {\n        left: 'prev',\n        center: 'title',\n        right: 'next'\n      },\n      views: {\n        timeGridWeekCustom: {\n          type: 'timeGridWeek',\n          slotMinTime: \"09:00:00\",\n          slotMaxTime: \"22:00:00\",\n          slotDuration: {\n            hour: 1\n          },\n          dayHeaders: true,\n          allDaySlot: false,\n          titleFormat: arg => {\n            return moment(arg.start).format(\"yyyy/MM/DD\") + \" ~ \" + moment(arg.end).format(\"yyyy/MM/DD\");\n          },\n          // titleFormat: {\n          //   year: 'numeric',\n          //   month: 'numeric',\n          //   day: 'numeric'\n          // },\n          // titleRangeSeparator: ' ~ ',\n          slotLabelFormat: {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          }\n        }\n      },\n      height: 383,\n      initialView: 'timeGridWeekCustom',\n      weekends: true,\n      editable: false,\n      selectable: false,\n      selectMirror: false,\n      dayMaxEvents: true,\n      datesSet: arg => {\n        if (this.paramSave && this.paramSave.minDate) {\n          arg.start = new Date(this.paramSave.minDate);\n          arg.start.setHours(1);\n          arg.end.setDate(arg.start.getDate() + 7);\n        }\n        this.dateFrom = arg.start;\n        this.dateTo = arg.end;\n        this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe();\n      }\n    });\n    this.getPreOderSettingRes = [];\n    this.listBuildCases = [];\n    this.selectedBuildCaseId = 0;\n    this.paramSave = null;\n    this.currentEvents = signal([]);\n    if (!!LocalStorageService.GetLocalStorage('paramSave')) {\n      this.paramSave = JSON.parse(LocalStorageService.GetLocalStorage('paramSave'));\n      LocalStorageService.RemoveLocalStorage('paramSave');\n    } else {\n      this.paramSave = null;\n    }\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  ngAfterViewInit() {\n    this.calendarApi = this.calendarComponent.getApi();\n  }\n  // ngOnDestroy(): void {\n  //   LocalStorageService.RemoveLocalStorage('paramSave')\n  // }\n  getListBuildCase() {\n    if (this.paramSave && this.paramSave.minDate) {\n      this.calendarOptions.mutate(options => {\n        options.initialDate = moment(new Date(this.paramSave.minDate)).format('YYYY-MM-DD');\n      });\n    }\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.paramSave ? parseInt(this.paramSave.buildCaseId) : res.Entries[0].cID;\n      }\n    }), mergeMap(() => this.getPreOrderSetting(this.dateFrom, this.dateTo))).subscribe();\n  }\n  getPreOrderSetting(CDateStart, CDateEnd) {\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.selectedBuildCaseId,\n        CDate: null,\n        CDateEnd: CDateEnd.toISOString(),\n        CDateStart: CDateStart.toISOString(),\n        CStatus: null\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.getPreOderSettingRes = res.Entries ?? [];\n        this.initEvent();\n        this.paramSave = null;\n      }\n    }));\n  }\n  searchPreOrderSetting() {\n    this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe();\n    if (this.dateFrom) {\n      this.calendarApi.gotoDate(moment(this.dateFrom).format('YYYY-MM-DD'));\n    }\n  }\n  initEvent() {\n    this.listEvent = [];\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n      this.getPreOderSettingRes.forEach(e => {\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n          let date = e.CDate ? new Date(e.CDate) : undefined;\n          date = date ? new Date(date.setMinutes(0)) : date;\n          date = date ? new Date(date.setSeconds(0)) : date;\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)).getTime() : 0;\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)).getTime() : 0;\n          this.listEvent.push({\n            start: startDate,\n            end: endDate,\n            display: \"background\"\n          });\n        }\n      });\n    }\n    this.calendarOptions.mutate(options => {\n      options.events = this.listEvent;\n    });\n  }\n  gotoEdit() {\n    let paramInfo = {\n      CBuildCaseID: this.selectedBuildCaseId,\n      CDateEnd: this.dateTo.toISOString(),\n      CDateStart: this.dateFrom.toISOString()\n    };\n    LocalStorageService.AddLocalStorage('paramInfo', JSON.stringify(paramInfo));\n    this.router.navigateByUrl(`/pages/available-time-slot/${this.selectedBuildCaseId}`);\n  }\n  static {\n    this.ɵfac = function AvailableTimeSlotComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AvailableTimeSlotComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.PreOrderSettingService), i0.ɵɵdirectiveInject(i2.BuildCaseService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AvailableTimeSlotComponent,\n      selectors: [[\"app-available-time-slot\"]],\n      viewQuery: function AvailableTimeSlotComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 28,\n      vars: 9,\n      consts: [[\"calendar\", \"\"], [\"eventContent\", \"\"], [\"accent\", \"success\"], [1, \"col-12\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"w-full\"], [\"for\", \"name\"], [\"name\", \"templates\", 1, \"ml-3\", \"w-full\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"w-full\"], [\"for\", \"date-select1\", 1, \"mr-3\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\"], [1, \"mt-3\", \"flex\", \"justify-end\", \"items-end\", \"w-full\"], [1, \"mr-3\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [3, \"options\"], [3, \"value\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function AvailableTimeSlotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"span\", 6);\n          i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"selectedChange\", function AvailableTimeSlotComponent_Template_nb_select_selectedChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(10, AvailableTimeSlotComponent_nb_option_10_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"span\", 10);\n          i0.ɵɵtext(13, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p-calendar\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.dateFrom, $event) || (ctx.dateFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 12);\n          i0.ɵɵtext(16, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p-calendar\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.dateTo, $event) || (ctx.dateTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14);\n          i0.ɵɵtemplate(20, AvailableTimeSlotComponent_button_20_Template, 3, 0, \"button\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\");\n          i0.ɵɵtemplate(22, AvailableTimeSlotComponent_button_22_Template, 2, 0, \"button\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"nb-card-body\")(24, \"full-calendar\", 17, 0);\n          i0.ɵɵtemplate(26, AvailableTimeSlotComponent_ng_template_26_Template, 0, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"selected\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dateFrom);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dateTo);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUpdate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"options\", ctx.calendarOptions);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.NgControlStatus, i5.NgModel, i6.NbCardComponent, i6.NbCardBodyComponent, i6.NbCardHeaderComponent, i6.NbSelectComponent, i6.NbOptionComponent, i7.BreadcrumbComponent, FullCalendarModule, i8.FullCalendarComponent, CalendarModule, i9.Calendar],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJhdmFpbGFibGUtdGltZS1zbG90LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVzZXJ2YXRpb24tdGltZS1tYW5hZ2VtZW50L2F2YWlsYWJsZS10aW1lLXNsb3QvYXZhaWxhYmxlLXRpbWUtc2xvdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esb0xBQW9MIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["signal", "interactionPlugin", "dayGridPlugin", "timeGridPlugin", "listPlugin", "bootstrapPlugin", "mergeMap", "tap", "LocalStorageService", "CommonModule", "FullCalendarModule", "CalendarModule", "moment", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "AvailableTimeSlotComponent_button_20_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "searchPreOrderSetting", "ɵɵelement", "AvailableTimeSlotComponent_button_22_Template_button_click_0_listener", "_r5", "gotoEdit", "AvailableTimeSlotComponent", "constructor", "_allow", "changeDetector", "preOderSettingService", "buildCaseService", "router", "listEvent", "calendarOptions", "plugins", "locale", "headerToolbar", "left", "center", "right", "views", "timeGridWeekCustom", "type", "slotMinTime", "slotMaxTime", "slotDuration", "hour", "dayHeaders", "allDaySlot", "titleFormat", "arg", "start", "format", "end", "slotLabelFormat", "minute", "hour12", "height", "initialView", "weekends", "editable", "selectable", "selectMirror", "dayMaxEvents", "datesSet", "paramSave", "minDate", "Date", "setHours", "setDate", "getDate", "dateFrom", "dateTo", "getPreOrderSetting", "subscribe", "getPreOderSettingRes", "listBuildCases", "selectedBuildCaseId", "currentEvents", "GetLocalStorage", "JSON", "parse", "RemoveLocalStorage", "ngOnInit", "getListBuildCase", "ngAfterViewInit", "calendarApi", "calendarComponent", "getApi", "mutate", "options", "initialDate", "apiBuildCaseGetBuildCaseListPost$Json", "pipe", "res", "StatusCode", "Entries", "parseInt", "buildCaseId", "CDateStart", "CDateEnd", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "CDate", "toISOString", "CStatus", "initEvent", "gotoDate", "length", "for<PERSON>ach", "e", "CHour", "date", "undefined", "setMinutes", "setSeconds", "startDate", "getTime", "endDate", "push", "display", "events", "paramInfo", "AddLocalStorage", "stringify", "navigateByUrl", "ɵɵdirectiveInject", "i1", "AllowHelper", "ChangeDetectorRef", "i2", "PreOrderSettingService", "BuildCaseService", "i3", "Router", "selectors", "viewQuery", "AvailableTimeSlotComponent_Query", "rf", "ctx", "ɵɵtwoWayListener", "AvailableTimeSlotComponent_Template_nb_select_selectedChange_9_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "ɵɵtemplate", "AvailableTimeSlotComponent_nb_option_10_Template", "AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_14_listener", "AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_17_listener", "AvailableTimeSlotComponent_button_20_Template", "AvailableTimeSlotComponent_button_22_Template", "AvailableTimeSlotComponent_ng_template_26_Template", "ɵɵtemplateRefExtractor", "ɵɵtwoWayProperty", "isUpdate", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "NgControlStatus", "NgModel", "i6", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i7", "BreadcrumbComponent", "i8", "FullCalendarComponent", "i9", "Calendar", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\available-time-slot.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\available-time-slot.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild, signal } from '@angular/core';\r\nimport { Calendar, CalendarOptions, EventApi } from 'fullcalendar';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\r\nimport { BuildCaseService, PreOrderSettingService } from 'src/services/api/services';\r\nimport { mergeMap, tap } from 'rxjs';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BuildCaseGetListReponse, GetPreOrderSettingResponse } from 'src/services/api/models';\r\nimport { end } from '@popperjs/core';\r\nimport { Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { CalendarModule } from 'primeng/calendar'\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n@Component({\r\n  selector: 'app-available-time-slot',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    FullCalendarModule,\r\n    CalendarModule\r\n  ],\r\n  templateUrl: './available-time-slot.component.html',\r\n  styleUrls: ['./available-time-slot.component.scss']\r\n})\r\nexport class AvailableTimeSlotComponent extends BaseComponent implements OnInit {\r\n  listEvent: any[] = []\r\n  @ViewChild('calendar') calendarComponent: FullCalendarComponent;\r\n  calendarApi: Calendar;\r\n\r\n  calendarOptions = signal<CalendarOptions>({\r\n    plugins: [\r\n      interactionPlugin,\r\n      dayGridPlugin,\r\n      timeGridPlugin,\r\n      listPlugin,\r\n      timeGridPlugin,\r\n      bootstrapPlugin\r\n    ],\r\n    locale: 'zh-tw',\r\n    headerToolbar: {\r\n      left: 'prev',\r\n      center: 'title',\r\n      right: 'next'\r\n    },\r\n    views: {\r\n      timeGridWeekCustom: {\r\n        type: 'timeGridWeek',\r\n        slotMinTime: \"09:00:00\",\r\n        slotMaxTime: \"22:00:00\",\r\n        slotDuration: { hour: 1 },\r\n        dayHeaders: true,\r\n        allDaySlot: false,\r\n        titleFormat: (arg) => {\r\n          return moment(arg.start).format(\"yyyy/MM/DD\") + \" ~ \" + moment(arg.end).format(\"yyyy/MM/DD\")\r\n        },\r\n        // titleFormat: {\r\n        //   year: 'numeric',\r\n        //   month: 'numeric',\r\n        //   day: 'numeric'\r\n        // },\r\n        // titleRangeSeparator: ' ~ ',\r\n        slotLabelFormat: {\r\n          hour: '2-digit',\r\n          minute: '2-digit',\r\n          hour12: false\r\n        }\r\n      },\r\n    },\r\n    height: 383,\r\n    initialView: 'timeGridWeekCustom',\r\n    weekends: true,\r\n    editable: false,\r\n    selectable: false,\r\n    selectMirror: false,\r\n    dayMaxEvents: true,\r\n    datesSet: (arg) => {\r\n      if (this.paramSave && this.paramSave.minDate) {\r\n        arg.start = new Date(this.paramSave.minDate)\r\n        arg.start.setHours(1)\r\n        arg.end.setDate(arg.start.getDate() + 7)\r\n      }\r\n      this.dateFrom = arg.start;\r\n      this.dateTo = arg.end;\r\n\r\n      this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe()\r\n    },\r\n  });\r\n\r\n  getPreOderSettingRes = [] as GetPreOrderSettingResponse[]\r\n  listBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number | undefined = 0\r\n  dateFrom: Date\r\n  dateTo: Date\r\n  paramSave: any = null\r\n\r\n  currentEvents = signal<EventApi[]>([]);\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private changeDetector: ChangeDetectorRef,\r\n    private preOderSettingService: PreOrderSettingService,\r\n    private buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n  ) {\r\n    super(_allow)\r\n    if (!!LocalStorageService.GetLocalStorage('paramSave')) {\r\n      this.paramSave = JSON.parse(LocalStorageService.GetLocalStorage('paramSave'))\r\n      LocalStorageService.RemoveLocalStorage('paramSave')\r\n    } else {\r\n      this.paramSave = null;\r\n    }\r\n\r\n  }\r\n\r\n  override ngOnInit() {\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.calendarApi = this.calendarComponent.getApi();\r\n  }\r\n\r\n  // ngOnDestroy(): void {\r\n  //   LocalStorageService.RemoveLocalStorage('paramSave')\r\n  // }\r\n\r\n  getListBuildCase() {\r\n    if (this.paramSave && this.paramSave.minDate) {\r\n      this.calendarOptions.mutate((options) => {\r\n        options.initialDate = moment(new Date(this.paramSave.minDate)).format('YYYY-MM-DD')\r\n      });\r\n    }\r\n    this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.listBuildCases = res.Entries! ?? []\r\n          this.selectedBuildCaseId = this.paramSave ? parseInt(this.paramSave.buildCaseId) : res.Entries![0].cID\r\n        }\r\n      }),\r\n      mergeMap(() => this.getPreOrderSetting(this.dateFrom, this.dateTo))\r\n    ).subscribe()\r\n  }\r\n\r\n  getPreOrderSetting(CDateStart: Date, CDateEnd: Date) {\r\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.selectedBuildCaseId,\r\n        CDate: null,\r\n        CDateEnd: CDateEnd.toISOString(),\r\n        CDateStart: CDateStart.toISOString(),\r\n        CStatus: null\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.getPreOderSettingRes = res.Entries! ?? []\r\n          this.initEvent()\r\n          this.paramSave = null;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  searchPreOrderSetting() {\r\n    this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe()\r\n    if (this.dateFrom) {\r\n      this.calendarApi.gotoDate(moment(this.dateFrom).format('YYYY-MM-DD'));\r\n    }\r\n  }\r\n\r\n  initEvent() {\r\n    this.listEvent = []\r\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\r\n      this.getPreOderSettingRes.forEach(e => {\r\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\r\n          let date = e.CDate ? new Date(e.CDate) : undefined;\r\n          date = date ? new Date(date.setMinutes(0)) : date\r\n          date = date ? new Date(date.setSeconds(0)) : date\r\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)).getTime() : 0\r\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)).getTime() : 0\r\n          this.listEvent.push({\r\n            start: startDate,\r\n            end: endDate,\r\n            display: \"background\",\r\n          })\r\n        }\r\n      })\r\n    }\r\n    this.calendarOptions.mutate((options) => {\r\n      options.events = this.listEvent\r\n    });\r\n  }\r\n\r\n  gotoEdit() {\r\n    let paramInfo = {\r\n      CBuildCaseID: this.selectedBuildCaseId,\r\n      CDateEnd: this.dateTo.toISOString(),\r\n      CDateStart: this.dateFrom.toISOString(),\r\n    }\r\n    LocalStorageService.AddLocalStorage('paramInfo', JSON.stringify(paramInfo))\r\n    this.router.navigateByUrl(`/pages/available-time-slot/${this.selectedBuildCaseId}`)\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"col-12\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center w-full\">\r\n          <span for=\"name\">\r\n            建案\r\n          </span>\r\n          <nb-select class=\"ml-3 w-full\" name=\"templates\" [(selected)]=\"selectedBuildCaseId\">\r\n            <nb-option *ngFor=\"let option of listBuildCases\" [value]=\"option.cID\">\r\n              {{ option.CBuildCaseName }}</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"flex items-center justify-end w-full\">\r\n          <span for=\"date-select1\" class=\"mr-3\">\r\n            建立時間\r\n          </span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\" [(ngModel)]=\"dateFrom\"></p-calendar>\r\n          <span for=\"date-select1\" class=\"mr-1 ml-1\">~</span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\" [(ngModel)]=\"dateTo\"></p-calendar>\r\n        </div>\r\n      </div>\r\n      <div class=\"mt-3 flex justify-end items-end w-full\">\r\n        <div class=\"mr-3\">\r\n          <button class=\"btn btn-info\" *ngIf=\"isUpdate\" (click)=\"searchPreOrderSetting()\">\r\n            <i class=\"fas fa-search mr-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n        <div>\r\n          <button class=\"btn btn-success\" *ngIf=\"isUpdate\" (click)=\"gotoEdit()\">編輯</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-body>\r\n    <full-calendar #calendar [options]='calendarOptions'>\r\n      <ng-template #eventContent let-arg>\r\n        <!-- <b>{{ arg.timeText }}</b>\r\n        <i>{{ arg.event.title }}</i> -->\r\n      </ng-template>\r\n    </full-calendar>\r\n  </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAA0DA,MAAM,QAAQ,eAAe;AAGvF,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AAErD,SAASC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAEpC,SAASC,mBAAmB,QAAQ,+CAA+C;AAKnF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAAgCC,kBAAkB,QAAQ,uBAAuB;AACjF,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;ICTvDC,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADQH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,GAAA,CAAoB;IACnEN,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,cAAA,KAA2B;;;;;;IAc/BT,EAAA,CAAAC,cAAA,iBAAgF;IAAlCD,EAAA,CAAAU,UAAA,mBAAAC,sEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,qBAAA,EAAuB;IAAA,EAAC;IAC7EjB,EAAA,CAAAkB,SAAA,YAAkC;IAAAlB,EAAA,CAAAE,MAAA,oBACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAAsE;IAArBD,EAAA,CAAAU,UAAA,mBAAAS,sEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAO,QAAA,EAAU;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;ADG3F,OAAM,MAAOmB,0BAA2B,SAAQvB,aAAa;EAyE3DwB,YACUC,MAAmB,EACnBC,cAAiC,EACjCC,qBAA6C,EAC7CC,gBAAkC,EAClCC,MAAc;IAEtB,KAAK,CAACJ,MAAM,CAAC;IANL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IA7EhB,KAAAC,SAAS,GAAU,EAAE;IAIrB,KAAAC,eAAe,GAAG7C,MAAM,CAAkB;MACxC8C,OAAO,EAAE,CACP7C,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVD,cAAc,EACdE,eAAe,CAChB;MACD0C,MAAM,EAAE,OAAO;MACfC,aAAa,EAAE;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;OACR;MACDC,KAAK,EAAE;QACLC,kBAAkB,EAAE;UAClBC,IAAI,EAAE,cAAc;UACpBC,WAAW,EAAE,UAAU;UACvBC,WAAW,EAAE,UAAU;UACvBC,YAAY,EAAE;YAAEC,IAAI,EAAE;UAAC,CAAE;UACzBC,UAAU,EAAE,IAAI;UAChBC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAGC,GAAG,IAAI;YACnB,OAAOlD,MAAM,CAACkD,GAAG,CAACC,KAAK,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,GAAGpD,MAAM,CAACkD,GAAG,CAACG,GAAG,CAAC,CAACD,MAAM,CAAC,YAAY,CAAC;UAC9F,CAAC;UACD;UACA;UACA;UACA;UACA;UACA;UACAE,eAAe,EAAE;YACfR,IAAI,EAAE,SAAS;YACfS,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;;;OAGb;MACDC,MAAM,EAAE,GAAG;MACXC,WAAW,EAAE,oBAAoB;MACjCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAGd,GAAG,IAAI;QAChB,IAAI,IAAI,CAACe,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,OAAO,EAAE;UAC5ChB,GAAG,CAACC,KAAK,GAAG,IAAIgB,IAAI,CAAC,IAAI,CAACF,SAAS,CAACC,OAAO,CAAC;UAC5ChB,GAAG,CAACC,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC;UACrBlB,GAAG,CAACG,GAAG,CAACgB,OAAO,CAACnB,GAAG,CAACC,KAAK,CAACmB,OAAO,EAAE,GAAG,CAAC,CAAC;QAC1C;QACA,IAAI,CAACC,QAAQ,GAAGrB,GAAG,CAACC,KAAK;QACzB,IAAI,CAACqB,MAAM,GAAGtB,GAAG,CAACG,GAAG;QAErB,IAAI,CAACoB,kBAAkB,CAAC,IAAI,CAACF,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC,CAACE,SAAS,EAAE;MACjE;KACD,CAAC;IAEF,KAAAC,oBAAoB,GAAG,EAAkC;IACzD,KAAAC,cAAc,GAA8B,EAAE;IAC9C,KAAAC,mBAAmB,GAAuB,CAAC;IAG3C,KAAAZ,SAAS,GAAQ,IAAI;IAErB,KAAAa,aAAa,GAAG1F,MAAM,CAAa,EAAE,CAAC;IAUpC,IAAI,CAAC,CAACQ,mBAAmB,CAACmF,eAAe,CAAC,WAAW,CAAC,EAAE;MACtD,IAAI,CAACd,SAAS,GAAGe,IAAI,CAACC,KAAK,CAACrF,mBAAmB,CAACmF,eAAe,CAAC,WAAW,CAAC,CAAC;MAC7EnF,mBAAmB,CAACsF,kBAAkB,CAAC,WAAW,CAAC;IACrD,CAAC,MAAM;MACL,IAAI,CAACjB,SAAS,GAAG,IAAI;IACvB;EAEF;EAESkB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACC,MAAM,EAAE;EACpD;EAEA;EACA;EACA;EAEAJ,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACC,OAAO,EAAE;MAC5C,IAAI,CAACjC,eAAe,CAACwD,MAAM,CAAEC,OAAO,IAAI;QACtCA,OAAO,CAACC,WAAW,GAAG3F,MAAM,CAAC,IAAImE,IAAI,CAAC,IAAI,CAACF,SAAS,CAACC,OAAO,CAAC,CAAC,CAACd,MAAM,CAAC,YAAY,CAAC;MACrF,CAAC,CAAC;IACJ;IACA,IAAI,CAACtB,gBAAgB,CAAC8D,qCAAqC,EAAE,CAACC,IAAI,CAChElG,GAAG,CAACmG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnB,cAAc,GAAGkB,GAAG,CAACE,OAAQ,IAAI,EAAE;QACxC,IAAI,CAACnB,mBAAmB,GAAG,IAAI,CAACZ,SAAS,GAAGgC,QAAQ,CAAC,IAAI,CAAChC,SAAS,CAACiC,WAAW,CAAC,GAAGJ,GAAG,CAACE,OAAQ,CAAC,CAAC,CAAC,CAACvF,GAAG;MACxG;IACF,CAAC,CAAC,EACFf,QAAQ,CAAC,MAAM,IAAI,CAAC+E,kBAAkB,CAAC,IAAI,CAACF,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,CACpE,CAACE,SAAS,EAAE;EACf;EAEAD,kBAAkBA,CAAC0B,UAAgB,EAAEC,QAAc;IACjD,OAAO,IAAI,CAACvE,qBAAqB,CAACwE,6CAA6C,CAAC;MAC9EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC1B,mBAAmB;QACtC2B,KAAK,EAAE,IAAI;QACXJ,QAAQ,EAAEA,QAAQ,CAACK,WAAW,EAAE;QAChCN,UAAU,EAAEA,UAAU,CAACM,WAAW,EAAE;QACpCC,OAAO,EAAE;;KAEZ,CAAC,CAACb,IAAI,CACLlG,GAAG,CAACmG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACpB,oBAAoB,GAAGmB,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC9C,IAAI,CAACW,SAAS,EAAE;QAChB,IAAI,CAAC1C,SAAS,GAAG,IAAI;MACvB;IACF,CAAC,CAAC,CACH;EACH;EAEA7C,qBAAqBA,CAAA;IACnB,IAAI,CAACqD,kBAAkB,CAAC,IAAI,CAACF,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC,CAACE,SAAS,EAAE;IAC/D,IAAI,IAAI,CAACH,QAAQ,EAAE;MACjB,IAAI,CAACe,WAAW,CAACsB,QAAQ,CAAC5G,MAAM,CAAC,IAAI,CAACuE,QAAQ,CAAC,CAACnB,MAAM,CAAC,YAAY,CAAC,CAAC;IACvE;EACF;EAEAuD,SAASA,CAAA;IACP,IAAI,CAAC3E,SAAS,GAAG,EAAE;IACnB,IAAI,IAAI,CAAC2C,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACkC,MAAM,GAAG,CAAC,EAAE;MACrE,IAAI,CAAClC,oBAAoB,CAACmC,OAAO,CAACC,CAAC,IAAG;QACpC,IAAIA,CAAC,CAACC,KAAK,IAAID,CAAC,CAACC,KAAK,GAAG,CAAC,IAAID,CAAC,CAACC,KAAK,GAAG,EAAE,EAAE;UAC1C,IAAIC,IAAI,GAAGF,CAAC,CAACP,KAAK,GAAG,IAAIrC,IAAI,CAAC4C,CAAC,CAACP,KAAK,CAAC,GAAGU,SAAS;UAClDD,IAAI,GAAGA,IAAI,GAAG,IAAI9C,IAAI,CAAC8C,IAAI,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI;UACjDA,IAAI,GAAGA,IAAI,GAAG,IAAI9C,IAAI,CAAC8C,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI;UACjD,IAAII,SAAS,GAAGJ,IAAI,IAAIF,CAAC,CAACC,KAAK,GAAG,IAAI7C,IAAI,CAAC8C,IAAI,CAAC7C,QAAQ,CAAC2C,CAAC,CAACC,KAAK,CAAC,CAAC,CAACM,OAAO,EAAE,GAAG,CAAC;UAChF,IAAIC,OAAO,GAAGN,IAAI,IAAIF,CAAC,CAACC,KAAK,GAAG,IAAI7C,IAAI,CAAC8C,IAAI,CAAC7C,QAAQ,CAAC2C,CAAC,CAACC,KAAK,GAAG,CAAC,CAAC,CAAC,CAACM,OAAO,EAAE,GAAG,CAAC;UAClF,IAAI,CAACtF,SAAS,CAACwF,IAAI,CAAC;YAClBrE,KAAK,EAAEkE,SAAS;YAChBhE,GAAG,EAAEkE,OAAO;YACZE,OAAO,EAAE;WACV,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAACxF,eAAe,CAACwD,MAAM,CAAEC,OAAO,IAAI;MACtCA,OAAO,CAACgC,MAAM,GAAG,IAAI,CAAC1F,SAAS;IACjC,CAAC,CAAC;EACJ;EAEAR,QAAQA,CAAA;IACN,IAAImG,SAAS,GAAG;MACdpB,YAAY,EAAE,IAAI,CAAC1B,mBAAmB;MACtCuB,QAAQ,EAAE,IAAI,CAAC5B,MAAM,CAACiC,WAAW,EAAE;MACnCN,UAAU,EAAE,IAAI,CAAC5B,QAAQ,CAACkC,WAAW;KACtC;IACD7G,mBAAmB,CAACgI,eAAe,CAAC,WAAW,EAAE5C,IAAI,CAAC6C,SAAS,CAACF,SAAS,CAAC,CAAC;IAC3E,IAAI,CAAC5F,MAAM,CAAC+F,aAAa,CAAC,8BAA8B,IAAI,CAACjD,mBAAmB,EAAE,CAAC;EACrF;;;uCAjLWpD,0BAA0B,EAAAtB,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAA5H,EAAA,CAAA+H,iBAAA,GAAA/H,EAAA,CAAA4H,iBAAA,CAAAI,EAAA,CAAAC,sBAAA,GAAAjI,EAAA,CAAA4H,iBAAA,CAAAI,EAAA,CAAAE,gBAAA,GAAAlI,EAAA,CAAA4H,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA1B9G,0BAA0B;MAAA+G,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UClCrCxI,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkB,SAAA,qBAAiC;UACnClB,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,mBAAc,aACQ,aAC6B,aACP,cACnB;UACfD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,mBAAmF;UAAnCD,EAAA,CAAA0I,gBAAA,4BAAAC,wEAAAC,MAAA;YAAA5I,EAAA,CAAAY,aAAA,CAAAiI,GAAA;YAAA7I,EAAA,CAAA8I,kBAAA,CAAAL,GAAA,CAAA/D,mBAAA,EAAAkE,MAAA,MAAAH,GAAA,CAAA/D,mBAAA,GAAAkE,MAAA;YAAA,OAAA5I,EAAA,CAAAgB,WAAA,CAAA4H,MAAA;UAAA,EAAkC;UAChF5I,EAAA,CAAA+I,UAAA,KAAAC,gDAAA,uBAAsE;UAG1EhJ,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,cAAkD,gBACV;UACpCD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,sBAAiG;UAAvBD,EAAA,CAAA0I,gBAAA,2BAAAO,yEAAAL,MAAA;YAAA5I,EAAA,CAAAY,aAAA,CAAAiI,GAAA;YAAA7I,EAAA,CAAA8I,kBAAA,CAAAL,GAAA,CAAArE,QAAA,EAAAwE,MAAA,MAAAH,GAAA,CAAArE,QAAA,GAAAwE,MAAA;YAAA,OAAA5I,EAAA,CAAAgB,WAAA,CAAA4H,MAAA;UAAA,EAAsB;UAAC5I,EAAA,CAAAG,YAAA,EAAa;UAC9GH,EAAA,CAAAC,cAAA,gBAA2C;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnDH,EAAA,CAAAC,cAAA,sBAA+F;UAArBD,EAAA,CAAA0I,gBAAA,2BAAAQ,yEAAAN,MAAA;YAAA5I,EAAA,CAAAY,aAAA,CAAAiI,GAAA;YAAA7I,EAAA,CAAA8I,kBAAA,CAAAL,GAAA,CAAApE,MAAA,EAAAuE,MAAA,MAAAH,GAAA,CAAApE,MAAA,GAAAuE,MAAA;YAAA,OAAA5I,EAAA,CAAAgB,WAAA,CAAA4H,MAAA;UAAA,EAAoB;UAElG5I,EAFmG,CAAAG,YAAA,EAAa,EACxG,EACF;UAEJH,EADF,CAAAC,cAAA,eAAoD,eAChC;UAChBD,EAAA,CAAA+I,UAAA,KAAAI,6CAAA,qBAAgF;UAGlFnJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAK;UACHD,EAAA,CAAA+I,UAAA,KAAAK,6CAAA,qBAAsE;UAI9EpJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAEbH,EADF,CAAAC,cAAA,oBAAc,4BACyC;UACnDD,EAAA,CAAA+I,UAAA,KAAAM,kDAAA,gCAAArJ,EAAA,CAAAsJ,sBAAA,CAAmC;UAMzCtJ,EAFI,CAAAG,YAAA,EAAgB,EACH,EACP;;;UAlCgDH,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAuJ,gBAAA,aAAAd,GAAA,CAAA/D,mBAAA,CAAkC;UAClD1E,EAAA,CAAAO,SAAA,EAAiB;UAAjBP,EAAA,CAAAI,UAAA,YAAAqI,GAAA,CAAAhE,cAAA,CAAiB;UAQrCzE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAA2CJ,EAAA,CAAAuJ,gBAAA,YAAAd,GAAA,CAAArE,QAAA,CAAsB;UAEpFpE,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAA2CJ,EAAA,CAAAuJ,gBAAA,YAAAd,GAAA,CAAApE,MAAA,CAAoB;UAKhErE,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAqI,GAAA,CAAAe,QAAA,CAAc;UAKXxJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAqI,GAAA,CAAAe,QAAA,CAAc;UAM5BxJ,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAI,UAAA,YAAAqI,GAAA,CAAA3G,eAAA,CAA2B;;;qBDXpDpC,YAAY,EAAA+J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7J,YAAY,EAAA8J,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EACZ3K,kBAAkB,EAAA4K,EAAA,CAAAC,qBAAA,EAClB5K,cAAc,EAAA6K,EAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}