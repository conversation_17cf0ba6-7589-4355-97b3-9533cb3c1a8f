{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport default function getMilliseconds(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = date.getMilliseconds();\n  return milliseconds;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "getMilliseconds", "dirtyDate", "arguments", "date", "milliseconds"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/getMilliseconds/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nexport default function getMilliseconds(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = date.getMilliseconds();\n  return milliseconds;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAE;EACjDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,YAAY,GAAGD,IAAI,CAACH,eAAe,CAAC,CAAC;EACzC,OAAOI,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}