{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { throwError } from 'rxjs';\n// import { AccountService } from '../services/account.service';\nimport { catchError, map } from 'rxjs/operators';\nimport { LocalStorageService } from '../services/local-storage.service';\nimport { STORAGE_KEY } from '../constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/message.service\";\nimport * as i3 from \"ngx-spinner\";\nexport class TokenInterceptor {\n  constructor(router, messageService, spinner) {\n    this.router = router;\n    this.messageService = messageService;\n    this.spinner = spinner;\n    this.LoadingQue = [];\n  }\n  intercept(request, next) {\n    const token = LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN);\n    if (token !== null && token !== undefined && token !== '') {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)}`,\n          BuId: 'BU00000'\n        }\n      });\n    }\n    if (request.url.indexOf('GetAutocompleteList') === -1 && request.url.indexOf('GetAutocompleteByTagValueList') === -1) {\n      this.spinner.show();\n      this.LoadingQue.push(request);\n    }\n    return next.handle(request).pipe(map(event => {\n      if (event instanceof HttpResponse) {\n        this.LoadingQue.pop();\n        if (this.LoadingQue.length == 0) {\n          this.spinner.hide();\n        }\n        if (event.body.StatusCode > 0) {\n          this.messageService.showErrorMSG(event.body.Message);\n        }\n      }\n      return event;\n    }), catchError(error => {\n      if (error.status === 401) {\n        // this.toastMessage.showErrorMSG('請先登入');\n        this.router.navigateByUrl('login');\n      }\n      if (error.status === 403) {\n        // this.toastMessage.showErrorMSG('請先登入');\n        this.messageService.showErrorMSG(\"權限不足\");\n        this.router.navigateByUrl('home');\n      }\n      return throwError(error);\n    }));\n  }\n  static {\n    this.ɵfac = function TokenInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TokenInterceptor)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.MessageService), i0.ɵɵinject(i3.NgxSpinnerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TokenInterceptor,\n      factory: TokenInterceptor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpResponse", "throwError", "catchError", "map", "LocalStorageService", "STORAGE_KEY", "TokenInterceptor", "constructor", "router", "messageService", "spinner", "LoadingQue", "intercept", "request", "next", "token", "GetLocalStorage", "TOKEN", "undefined", "clone", "setHeaders", "Authorization", "BuId", "url", "indexOf", "show", "push", "handle", "pipe", "event", "pop", "length", "hide", "body", "StatusCode", "showErrorMSG", "Message", "error", "status", "navigateByUrl", "i0", "ɵɵinject", "i1", "Router", "i2", "MessageService", "i3", "NgxSpinnerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\auth\\token.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  HttpRequest,\r\n  HttpEvent,\r\n  HttpInterceptor,\r\n  HttpResponse,\r\n  HttpErrorResponse,\r\n  HttpHandler,\r\n} from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\n// import { AccountService } from '../services/account.service';\r\nimport { catchError, map } from 'rxjs/operators';\r\nimport { Router } from '@angular/router';\r\nimport { LocalStorageService } from '../services/local-storage.service';\r\nimport { STORAGE_KEY } from '../constant/constant';\r\nimport { MessageService } from '../services/message.service';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class TokenInterceptor implements HttpInterceptor {\r\n  constructor(\r\n    private router: Router,\r\n    private messageService: MessageService,\r\n    private spinner: NgxSpinnerService,\r\n  ) { }\r\n  LoadingQue = [] as HttpRequest<any>[];\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,\r\n  ): Observable<HttpEvent<any>> {\r\n\r\n    const token = LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN);\r\n\r\n    if (token !== null && token !== undefined && token !== '') {\r\n      request = request.clone(\r\n        ({\r\n          setHeaders: {\r\n            Authorization: `Bearer ${LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN)}`,\r\n            BuId: 'BU00000'\r\n          },\r\n        }),\r\n      );\r\n    }\r\n\r\n    if (request.url.indexOf('GetAutocompleteList') === -1\r\n      && request.url.indexOf('GetAutocompleteByTagValueList') === -1\r\n    ) {\r\n      this.spinner.show();\r\n      this.LoadingQue.push(request);\r\n    }\r\n\r\n    return next.handle(request).pipe(\r\n      map((event: HttpEvent<any>) => {\r\n        if (event instanceof HttpResponse) {\r\n          this.LoadingQue.pop();\r\n          if (this.LoadingQue.length == 0) {\r\n            this.spinner.hide();\r\n          }\r\n\r\n          if (event.body.StatusCode > 0) {\r\n            this.messageService.showErrorMSG(event.body.Message);\r\n          }\r\n        }\r\n        return event;\r\n      })\r\n      ,\r\n      catchError((error: HttpErrorResponse) => {\r\n        if (error.status === 401) {\r\n          // this.toastMessage.showErrorMSG('請先登入');\r\n          this.router.navigateByUrl('login');\r\n        }\r\n        if (error.status === 403) {\r\n          // this.toastMessage.showErrorMSG('請先登入');\r\n          this.messageService.showErrorMSG(\"權限不足\")\r\n          this.router.navigateByUrl('home');\r\n        }\r\n        return throwError(error);\r\n      }),\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAIEA,YAAY,QAGP,sBAAsB;AAC7B,SAAqBC,UAAU,QAAQ,MAAM;AAC7C;AACA,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAEhD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,WAAW,QAAQ,sBAAsB;;;;;AAKlD,OAAM,MAAOC,gBAAgB;EAC3BC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,OAA0B;IAF1B,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,OAAO,GAAPA,OAAO;IAEjB,KAAAC,UAAU,GAAG,EAAwB;EADjC;EAEJC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAGjB,MAAMC,KAAK,GAAGX,mBAAmB,CAACY,eAAe,CAACX,WAAW,CAACY,KAAK,CAAC;IAEpE,IAAIF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAK,EAAE,EAAE;MACzDF,OAAO,GAAGA,OAAO,CAACM,KAAK,CACpB;QACCC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUjB,mBAAmB,CAACY,eAAe,CAACX,WAAW,CAACY,KAAK,CAAC,EAAE;UACjFK,IAAI,EAAE;;OAER,CACH;IACH;IAEA,IAAIT,OAAO,CAACU,GAAG,CAACC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,IAChDX,OAAO,CAACU,GAAG,CAACC,OAAO,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC,EAC9D;MACA,IAAI,CAACd,OAAO,CAACe,IAAI,EAAE;MACnB,IAAI,CAACd,UAAU,CAACe,IAAI,CAACb,OAAO,CAAC;IAC/B;IAEA,OAAOC,IAAI,CAACa,MAAM,CAACd,OAAO,CAAC,CAACe,IAAI,CAC9BzB,GAAG,CAAE0B,KAAqB,IAAI;MAC5B,IAAIA,KAAK,YAAY7B,YAAY,EAAE;QACjC,IAAI,CAACW,UAAU,CAACmB,GAAG,EAAE;QACrB,IAAI,IAAI,CAACnB,UAAU,CAACoB,MAAM,IAAI,CAAC,EAAE;UAC/B,IAAI,CAACrB,OAAO,CAACsB,IAAI,EAAE;QACrB;QAEA,IAAIH,KAAK,CAACI,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE;UAC7B,IAAI,CAACzB,cAAc,CAAC0B,YAAY,CAACN,KAAK,CAACI,IAAI,CAACG,OAAO,CAAC;QACtD;MACF;MACA,OAAOP,KAAK;IACd,CAAC,CAAC,EAEF3B,UAAU,CAAEmC,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB;QACA,IAAI,CAAC9B,MAAM,CAAC+B,aAAa,CAAC,OAAO,CAAC;MACpC;MACA,IAAIF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB;QACA,IAAI,CAAC7B,cAAc,CAAC0B,YAAY,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC3B,MAAM,CAAC+B,aAAa,CAAC,MAAM,CAAC;MACnC;MACA,OAAOtC,UAAU,CAACoC,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;;;uCA5DW/B,gBAAgB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;aAAhBzC,gBAAgB;MAAA0C,OAAA,EAAhB1C,gBAAgB,CAAA2C,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}