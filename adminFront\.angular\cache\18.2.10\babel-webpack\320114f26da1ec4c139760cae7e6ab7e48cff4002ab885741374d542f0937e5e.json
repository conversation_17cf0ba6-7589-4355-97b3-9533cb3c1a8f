{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as JSZip from 'jszip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nfunction PictureMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction PictureMaterialComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nfunction PictureMaterialComponent_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.batchDelete());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedCount === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u522A\\u9664 (\", ctx_r4.selectedCount, \") \");\n  }\n}\nfunction PictureMaterialComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r7));\n    });\n    i0.ɵɵtext(1, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_th_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 21)(1, \"nb-checkbox\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function PictureMaterialComponent_th_27_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSelectAll($event));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.selectAll);\n  }\n}\nfunction PictureMaterialComponent_tr_38_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"nb-checkbox\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function PictureMaterialComponent_tr_38_td_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.toggleSelectItem(item_r10.CId));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.isItemSelected(item_r10.CId));\n  }\n}\nfunction PictureMaterialComponent_tr_38_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_38_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.addNew(dialog_r7, item_r10));\n    });\n    i0.ɵɵtext(1, \"\\u9810\\u89BD\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_38_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_38_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const item_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(42);\n      return i0.ɵɵresetView(ctx_r4.changePicture(dialog_r7, item_r10));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, PictureMaterialComponent_tr_38_td_1_Template, 2, 1, \"td\", 29);\n    i0.ɵɵelementStart(2, \"td\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 30);\n    i0.ɵɵtemplate(12, PictureMaterialComponent_tr_38_button_12_Template, 2, 0, \"button\", 31)(13, PictureMaterialComponent_tr_38_button_13_Template, 2, 0, \"button\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead && ctx_r4.images.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r10 == null ? null : item_r10.CPictureCode, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getCategoryLabel(ctx_r4.selectedCategory));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 7, item_r10 == null ? null : item_r10.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isRead);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r4.imageCounter, \") \");\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"p\", 48)(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5716\\u7247\\u540D\\u7A31:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 48)(6, \"strong\");\n    i0.ɵɵtext(7, \"\\u66F4\\u65B0\\u6642\\u9593:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentImageInfo.CPictureCode, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(9, 2, ctx_r4.currentImageInfo.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"), \"\");\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.previousImage());\n    });\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵtext(3, \" \\u4E0A\\u4E00\\u5F35 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 52);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.nextImage());\n    });\n    i0.ɵɵtext(7, \" \\u4E0B\\u4E00\\u5F35 \");\n    i0.ɵɵelement(8, \"i\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.imageCounter);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template_div_click_0_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.goToImage(i_r16));\n    });\n    i0.ɵɵelement(1, \"img\", 58);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const img_r17 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"active\", i_r16 === ctx_r4.currentImageIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 3, img_r17.CBase64 || img_r17.CFile), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtemplate(2, PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template, 3, 5, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.currentPreviewImages);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"div\", 42)(2, \"div\", 43);\n    i0.ɵɵelement(3, \"img\", 44);\n    i0.ɵɵpipe(4, \"base64Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, PictureMaterialComponent_ng_template_41_div_6_div_5_Template, 10, 5, \"div\", 45)(6, PictureMaterialComponent_ng_template_41_div_6_div_6_Template, 9, 1, \"div\", 46)(7, PictureMaterialComponent_ng_template_41_div_6_div_7_Template, 3, 1, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(4, 4, ctx_r4.currentImageShowing), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentImageInfo);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentPreviewImages.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.currentPreviewImages.length > 1);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 67);\n    i0.ɵɵlistener(\"blur\", function PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_input_blur_2_listener($event) {\n      const i_r22 = i0.ɵɵrestoreView(_r21).index;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.renameFile($event, i_r22));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 68);\n    i0.ɵɵelement(4, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 30)(6, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_button_click_6_listener() {\n      const picture_r23 = i0.ɵɵrestoreView(_r21).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.removeImage(picture_r23.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r23 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r23.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r23.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"h6\");\n    i0.ɵɵtext(2, \"\\u4E0A\\u50B3\\u65B9\\u5F0F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const inputFile_r19 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(inputFile_r19.click());\n    });\n    i0.ɵɵtext(4, \"\\u55AE\\u5F35\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const zipInputFile_r20 = i0.ɵɵreference(10);\n      return i0.ɵɵresetView(zipInputFile_r20.click());\n    });\n    i0.ɵɵtext(6, \"ZIP \\u6279\\u91CF\\u532F\\u5165\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"input\", 62, 2);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63, 3);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 64)(12, \"table\", 65)(13, \"thead\")(14, \"tr\", 19)(15, \"th\", 66);\n    i0.ɵɵtext(16, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 21);\n    i0.ɵɵtext(18, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"th\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template, 8, 2, \"tr\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-info mr-2\" : ctx_r4.listPictures.length < 1 ? \"btn btn-info mr-2\" : \"btn btn-info disable mr-2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(!ctx_r4.isEdit ? \"btn btn-success\" : \"btn btn-success disable\");\n    i0.ɵɵproperty(\"disabled\", ctx_r4.isEdit);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.listPictures);\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ref_r24 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.uploadImage(ref_r24));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 33)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PictureMaterialComponent_ng_template_41_span_4_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\", 35);\n    i0.ɵɵtemplate(6, PictureMaterialComponent_ng_template_41_div_6_Template, 8, 6, \"div\", 36)(7, PictureMaterialComponent_ng_template_41_ng_template_7_Template, 22, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"nb-card-footer\")(10, \"div\", 37)(11, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_41_Template_button_click_11_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r13).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      ref_r24.close();\n      return i0.ɵɵresetView(ctx_r4.resetPreviewState());\n    });\n    i0.ɵɵtext(12, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, PictureMaterialComponent_ng_template_41_button_13_Template, 2, 0, \"button\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r26 = i0.ɵɵreference(8);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.isPreviewMode ? \"\\u5716\\u7247\\u9810\\u89BD\" : (ctx_r4.selectedCategory === ctx_r4.PictureCategory.BUILDING_MATERIAL ? \"\\u5EFA\\u6750\\u5716\\u7247\" : \"\\u793A\\u610F\\u5716\\u7247\") + \"\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isPreviewMode && ctx_r4.currentPreviewImages.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isPreviewMode)(\"ngIfElse\", upload_r26);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isPreviewMode);\n  }\n}\n// 圖片類別枚舉\nvar PictureCategory;\n(function (PictureCategory) {\n  PictureCategory[PictureCategory[\"NONE\"] = 0] = \"NONE\";\n  PictureCategory[PictureCategory[\"BUILDING_MATERIAL\"] = 1] = \"BUILDING_MATERIAL\";\n  PictureCategory[PictureCategory[\"SCHEMATIC\"] = 2] = \"SCHEMATIC\";\n  PictureCategory[PictureCategory[\"ACCESSORIES\"] = 3] = \"ACCESSORIES\"; // 飾品圖片\n})(PictureCategory || (PictureCategory = {}));\nexport class PictureMaterialComponent extends BaseComponent {\n  get selectedCount() {\n    return this.selectedItems.size;\n  }\n  // 獲取類別標籤的方法\n  getCategoryLabel(category) {\n    const option = this.categoryOptions.find(opt => opt.value === category);\n    return option ? option.label : '未知類別';\n  }\n  constructor(_allow, dialogService, valid, _pictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.selectedCategory = PictureCategory.NONE;\n    this.isCategorySelected = false; // 追蹤用戶是否已經明確選擇類別\n    // 批次選擇相關屬性\n    this.selectedItems = new Set(); // 選中的項目 ID\n    this.selectAll = false; // 全選狀態\n    this.currentImageShowing = \"\";\n    // 輪播預覽相關屬性\n    this.currentPreviewImages = [];\n    this.currentImageIndex = 0;\n    this.isPreviewMode = false;\n    this.listPictures = [];\n    this.isEdit = false;\n    // 類別選項\n    this.categoryOptions = [{\n      value: PictureCategory.BUILDING_MATERIAL,\n      label: '建材圖片'\n    }, {\n      value: PictureCategory.SCHEMATIC,\n      label: '示意圖片'\n    }];\n    // 讓模板可以使用 enum\n    this.PictureCategory = PictureCategory;\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getPicturelList(1))).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    // 重置選擇狀態\n    this.selectedItems.clear();\n    this.selectAll = false;\n    if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n          this.updateSelectAllState();\n        }\n      }));\n    } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId,\n          cPictureType: this.selectedCategory\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n          this.updateSelectAllState();\n        }\n      }));\n    } else {\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\n      this.images = [];\n      this.totalRecords = 0;\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: 1,\n          PageSize: 1,\n          CBuildCaseId: -1,\n          // 使用無效 ID 確保返回空結果\n          cPictureType: PictureCategory.NONE\n        }\n      }).pipe(tap(() => {\n        this.images = [];\n        this.totalRecords = 0;\n      }));\n    }\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\n    if (!item && !this.isCategorySelected) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    this.listPictures = [];\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      // 預覽模式 - 顯示當前類別的所有圖片並支持輪播\n      this.isPreviewMode = true;\n      this.currentPreviewImages = [...this.images];\n      this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);\n      if (this.currentImageIndex === -1) {\n        this.currentImageIndex = 0;\n      }\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    } else {\n      // 上傳模式\n      this.isPreviewMode = false;\n      this.currentPreviewImages = [];\n      this.currentImageIndex = 0;\n      this.currentImageShowing = \"\";\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    // 檢查是否已選擇類別\n    if (!this.selectedCategory) {\n      this.message.showErrorMSG('請先選擇圖片類別');\n      return;\n    }\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation(CFile) {\n    this.valid.clear();\n    const nameSet = new Set();\n    for (const item of CFile) {\n      if (nameSet.has(item.name)) {\n        this.valid.addErrorMessage('檔名不可重複');\n        return;\n      }\n      nameSet.add(item.name);\n    }\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        // 檢查是否為 ZIP 檔案\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\n          this.processZipFile(file);\n        } else {\n          this.processSingleImageFile(file);\n        }\n      }\n    }\n    // Reset input file to be able to select the old file again\n    event.target.value = null;\n  }\n  processZipFile(zipFile) {\n    const zip = new JSZip();\n    zip.loadAsync(zipFile).then(contents => {\n      const imageFiles = [];\n      contents.forEach((relativePath, file) => {\n        // 只處理圖片檔案，跳過資料夾\n        if (!file.dir && this.isImageFile(relativePath)) {\n          imageFiles.push(file.async('blob').then(blob => {\n            // 只取檔案名稱，移除資料夾路徑\n            const fileName = relativePath.split('/').pop() || relativePath.split('\\\\').pop() || relativePath;\n            // 建立 File 物件\n            const imageFile = new File([blob], fileName, {\n              type: this.getImageMimeType(relativePath)\n            });\n            return this.processImageFileFromZip(imageFile, fileName);\n          }));\n        }\n      });\n      // 處理所有圖片檔案\n      Promise.all(imageFiles).then(() => {\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\n      }).catch(error => {\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\n      });\n    }).catch(error => {\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\n    });\n  }\n  processSingleImageFile(file) {\n    let reader = new FileReader();\n    reader.readAsDataURL(file);\n    reader.onload = () => {\n      let base64Str = reader.result;\n      if (!base64Str) {\n        return;\n      }\n      this.addImageToList(file, base64Str);\n    };\n  }\n  processImageFileFromZip(file, originalPath) {\n    return new Promise((resolve, reject) => {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          reject('無法讀取圖片檔案');\n          return;\n        }\n        this.addImageToList(file, base64Str, originalPath);\n        resolve();\n      };\n      reader.onerror = () => {\n        reject('讀取圖片檔案時發生錯誤');\n      };\n    });\n  }\n  addImageToList(file, base64Str, originalPath) {\n    // Get name file ( no extension)\n    let fileName = originalPath || file.name;\n    // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑\n    if (originalPath) {\n      fileName = originalPath.split('/').pop() || originalPath.split('\\\\').pop() || originalPath;\n    }\n    const fileNameWithoutExtension = fileName.split('.')[0];\n    // Find files with duplicate names\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n    if (existingFileIndex !== -1) {\n      // If name is duplicate, update file data\n      this.listPictures[existingFileIndex] = {\n        ...this.listPictures[existingFileIndex],\n        data: base64Str,\n        CFile: file,\n        extension: this._utilityService.getFileExtension(fileName)\n      };\n    } else {\n      // If not duplicate, add new file\n      this.listPictures.push({\n        id: new Date().getTime() + Math.random(),\n        name: fileNameWithoutExtension,\n        data: base64Str,\n        extension: this._utilityService.getFileExtension(fileName),\n        CFile: file\n      });\n    }\n  }\n  isImageFile(fileName) {\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    return imageExtensions.includes(extension);\n  }\n  getImageMimeType(fileName) {\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\n    switch (extension) {\n      case '.jpg':\n      case '.jpeg':\n        return 'image/jpeg';\n      case '.png':\n        return 'image/png';\n      case '.gif':\n        return 'image/gif';\n      case '.bmp':\n        return 'image/bmp';\n      case '.webp':\n        return 'image/webp';\n      default:\n        return 'image/jpeg';\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      const CFile = this.listPictures.map(x => x.CFile);\n      this.validation(CFile);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      } // 統一使用 PictureService 進行上傳\n      const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? \"picture\" : \"infoPicture\",\n          CFile: CFile,\n          cPictureType: this.selectedCategory\n        }\n      });\n      uploadRequest.pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n        this.resetPreviewState();\n      }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        // 統一使用 PictureService 進行更新\n        const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({\n          body: {\n            CBuildCaseID: this.selectedBuildCaseId,\n            CPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        });\n        updateRequest.pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n          this.resetPreviewState();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  // 批次選擇相關方法\n  toggleSelectItem(itemId) {\n    if (this.selectedItems.has(itemId)) {\n      this.selectedItems.delete(itemId);\n    } else {\n      this.selectedItems.add(itemId);\n    }\n    this.updateSelectAllState();\n  }\n  toggleSelectAll(checked) {\n    this.selectAll = checked;\n    if (checked) {\n      // 全選當前頁面的所有項目\n      this.images.forEach(item => {\n        if (item.CId) {\n          this.selectedItems.add(item.CId);\n        }\n      });\n    } else {\n      // 取消全選 - 只移除當前頁面的項目\n      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n      currentPageIds.forEach(id => this.selectedItems.delete(id));\n    }\n  }\n  updateSelectAllState() {\n    const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined);\n    this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));\n  }\n  // 批次刪除方法\n  batchDelete() {\n    if (this.selectedItems.size === 0) {\n      this.message.showErrorMSG('請選擇要刪除的項目');\n      return;\n    }\n    const selectedIds = Array.from(this.selectedItems);\n    // 顯示確認對話框\n    if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {\n      this._pictureService.apiPictureDeletePicturePost$Json({\n        body: selectedIds\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);\n          // 清空選擇狀態\n          this.selectedItems.clear();\n          this.selectAll = false;\n        } else {\n          this.message.showErrorMSG(res.Message || '刪除失敗');\n        }\n      }), concatMap(res => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))).subscribe();\n    }\n  }\n  // 檢查項目是否被選中\n  isItemSelected(itemId) {\n    return this.selectedItems.has(itemId);\n  }\n  // 輪播預覽相關方法\n  getCurrentPreviewImage() {\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n      return '';\n    }\n    const currentImage = this.currentPreviewImages[this.currentImageIndex];\n    // 優先使用 CBase64，如果沒有則使用 CFile\n    return currentImage.CBase64 || currentImage.CFile || '';\n  }\n  previousImage() {\n    if (this.currentPreviewImages.length === 0) return;\n    this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;\n    this.currentImageShowing = this.getCurrentPreviewImage();\n  }\n  nextImage() {\n    if (this.currentPreviewImages.length === 0) return;\n    this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;\n    this.currentImageShowing = this.getCurrentPreviewImage();\n  }\n  goToImage(index) {\n    if (index >= 0 && index < this.currentPreviewImages.length) {\n      this.currentImageIndex = index;\n      this.currentImageShowing = this.getCurrentPreviewImage();\n    }\n  }\n  get currentImageInfo() {\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\n      return null;\n    }\n    return this.currentPreviewImages[this.currentImageIndex];\n  }\n  get imageCounter() {\n    if (this.currentPreviewImages.length === 0) return '';\n    return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;\n  }\n  // 重置預覽狀態\n  resetPreviewState() {\n    this.isPreviewMode = false;\n    this.currentPreviewImages = [];\n    this.currentImageIndex = 0;\n    this.currentImageShowing = \"\";\n  }\n  // 鍵盤導航支持\n  handleKeyboardEvent(event) {\n    if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        event.preventDefault();\n        this.previousImage();\n        break;\n      case 'ArrowRight':\n        event.preventDefault();\n        this.nextImage();\n        break;\n      case 'Escape':\n        event.preventDefault();\n        // 可以在這裡添加關閉對話框的邏輯\n        break;\n    }\n  }\n  static {\n    this.ɵfac = function PictureMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PictureMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PictureMaterialComponent,\n      selectors: [[\"ngx-picture-material\"]],\n      hostBindings: function PictureMaterialComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function PictureMaterialComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 43,\n      vars: 11,\n      consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"zipInputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"category\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5716\\u7247\\u985E\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-danger mr-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", \"class\", \"col-1\", 4, \"ngIf\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [3, \"ngModelChange\", \"ngModel\"], [4, \"ngIf\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [\"class\", \"ml-2 text-muted\", 4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"ml-2\", \"text-muted\"], [1, \"w-full\", \"h-auto\"], [1, \"preview-container\", \"position-relative\", 2, \"min-height\", \"400px\"], [1, \"text-center\", \"mb-3\"], [1, \"fit-size\", 2, \"max-height\", \"400px\", \"max-width\", \"100%\", \"object-fit\", \"contain\", 3, \"src\"], [\"class\", \"text-center mb-3\", 4, \"ngIf\"], [\"class\", \"carousel-controls d-flex justify-content-between align-items-center\", 4, \"ngIf\"], [\"class\", \"thumbnail-nav mt-3\", 4, \"ngIf\"], [1, \"mb-1\"], [1, \"carousel-controls\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"nb-chevron-left\"], [1, \"text-muted\"], [1, \"nb-chevron-right\"], [1, \"thumbnail-nav\", \"mt-3\"], [1, \"d-flex\", \"flex-wrap\", \"justify-content-center\"], [\"class\", \"thumbnail-item m-1\", \"style\", \"cursor: pointer; border: 2px solid transparent; padding: 2px;\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"thumbnail-item\", \"m-1\", 2, \"cursor\", \"pointer\", \"border\", \"2px solid transparent\", \"padding\", \"2px\", 3, \"click\"], [2, \"width\", \"60px\", \"height\", \"60px\", \"object-fit\", \"cover\", \"border-radius\", \"4px\", 3, \"src\"], [1, \"mb-3\"], [3, \"click\"], [3, \"click\", \"disabled\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [\"type\", \"file\", \"accept\", \".zip,application/zip\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function PictureMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 4)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 5);\n          i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"label\", 9);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedChange($event));\n          });\n          i0.ɵɵtemplate(12, PictureMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"label\", 12);\n          i0.ɵɵtext(16, \"\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCategory, $event) || (ctx.selectedCategory = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.categoryChanged($event));\n          });\n          i0.ɵɵtemplate(18, PictureMaterialComponent_nb_option_18_Template, 2, 2, \"nb-option\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 7)(20, \"div\", 14);\n          i0.ɵɵtemplate(21, PictureMaterialComponent_button_21_Template, 2, 2, \"button\", 15)(22, PictureMaterialComponent_button_22_Template, 2, 0, \"button\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"table\", 18)(25, \"thead\")(26, \"tr\", 19);\n          i0.ɵɵtemplate(27, PictureMaterialComponent_th_27_Template, 3, 1, \"th\", 20);\n          i0.ɵɵelementStart(28, \"th\", 21);\n          i0.ɵɵtext(29, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 21);\n          i0.ɵɵtext(31, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"th\", 21);\n          i0.ɵɵtext(33, \"\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"th\", 21);\n          i0.ɵɵtext(35, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"th\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"tbody\");\n          i0.ɵɵtemplate(38, PictureMaterialComponent_tr_38_Template, 14, 10, \"tr\", 22);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(39, \"nb-card-footer\", 23)(40, \"ngx-pagination\", 24);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(41, PictureMaterialComponent_ng_template_41_Template, 14, 5, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.categoryOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead && ctx.images.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead && ctx.images.length > 0);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.images);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, Base64ImagePipe],\n      styles: [\".disable[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n\\n.preview-container[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%] {\\n  max-height: 120px;\\n  overflow-y: auto;\\n}\\n.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff !important;\\n  transform: scale(1.05);\\n}\\n.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item.active[_ngcontent-%COMP%] {\\n  border-color: #27ae60 !important;\\n  box-shadow: 0 0 8px rgba(39, 174, 96, 0.3);\\n}\\n.preview-container[_ngcontent-%COMP%]   .thumbnail-nav[_ngcontent-%COMP%]   .thumbnail-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.fit-size[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBpY3R1cmUtbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0Esb0JBQUE7QUFDRjs7QUFJRTtFQUNFLGdCQUFBO0FBREo7QUFJRTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7QUFGSjtBQUlJO0VBQ0UseUJBQUE7QUFGTjtBQUlNO0VBQ0UsZ0NBQUE7RUFDQSxzQkFBQTtBQUZSO0FBS007RUFDRSxnQ0FBQTtFQUNBLDBDQUFBO0FBSFI7QUFNTTtFQUNFLHlCQUFBO0FBSlI7O0FBV0E7RUFDRSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0Esd0NBQUE7QUFSRiIsImZpbGUiOiJwaWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmRpc2FibGUge1xyXG4gIG9wYWNpdHk6IC41O1xyXG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xyXG59XHJcblxyXG4vLyDpoJDopr3ovKrmkq3mqKPlvI9cclxuLnByZXZpZXctY29udGFpbmVyIHtcclxuICAuY2Fyb3VzZWwtY29udHJvbHMge1xyXG4gICAgbWFyZ2luLXRvcDogMjBweDtcclxuICB9XHJcblxyXG4gIC50aHVtYm5haWwtbmF2IHtcclxuICAgIG1heC1oZWlnaHQ6IDEyMHB4O1xyXG4gICAgb3ZlcmZsb3cteTogYXV0bztcclxuXHJcbiAgICAudGh1bWJuYWlsLWl0ZW0ge1xyXG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMDA3YmZmICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogIzI3YWU2MCAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgzOSwgMTc0LCA5NiwgMC4zKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgaW1nIHtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyDlnJbniYfpoa/npLrmqKPlvI9cclxuLmZpdC1zaXplIHtcclxuICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiBhdXRvO1xyXG4gIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvcGljdHVyZS1tYXRlcmlhbC9waWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLG9CQUFBO0FBQ0Y7O0FBSUU7RUFDRSxnQkFBQTtBQURKO0FBSUU7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0FBRko7QUFJSTtFQUNFLHlCQUFBO0FBRk47QUFJTTtFQUNFLGdDQUFBO0VBQ0Esc0JBQUE7QUFGUjtBQUtNO0VBQ0UsZ0NBQUE7RUFDQSwwQ0FBQTtBQUhSO0FBTU07RUFDRSx5QkFBQTtBQUpSOztBQVdBO0VBQ0UsZUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLHdDQUFBO0FBUkY7QUFDQSxnb0RBQWdvRCIsInNvdXJjZXNDb250ZW50IjpbIi5kaXNhYmxlIHtcclxuICBvcGFjaXR5OiAuNTtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxufVxyXG5cclxuLy8gw6nCoMKQw6jCpsK9w6jCvMKqw6bCksKtw6bCqMKjw6XCvMKPXHJcbi5wcmV2aWV3LWNvbnRhaW5lciB7XHJcbiAgLmNhcm91c2VsLWNvbnRyb2xzIHtcclxuICAgIG1hcmdpbi10b3A6IDIwcHg7XHJcbiAgfVxyXG5cclxuICAudGh1bWJuYWlsLW5hdiB7XHJcbiAgICBtYXgtaGVpZ2h0OiAxMjBweDtcclxuICAgIG92ZXJmbG93LXk6IGF1dG87XHJcblxyXG4gICAgLnRodW1ibmFpbC1pdGVtIHtcclxuICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJvcmRlci1jb2xvcjogIzAwN2JmZiAhaW1wb3J0YW50O1xyXG4gICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgICYuYWN0aXZlIHtcclxuICAgICAgICBib3JkZXItY29sb3I6ICMyN2FlNjAgIWltcG9ydGFudDtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDAgOHB4IHJnYmEoMzksIDE3NCwgOTYsIDAuMyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGltZyB7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gw6XCnMKWw6fCicKHw6nCocKvw6fCpMK6w6bCqMKjw6XCvMKPXHJcbi5maXQtc2l6ZSB7XHJcbiAgbWF4LXdpZHRoOiAxMDAlO1xyXG4gIGhlaWdodDogYXV0bztcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "of", "tap", "Base64ImagePipe", "SharedModule", "BaseComponent", "JSZip", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "option_r3", "value", "label", "ɵɵlistener", "PictureMaterialComponent_button_21_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "batchDelete", "selectedCount", "PictureMaterialComponent_button_22_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "addNew", "PictureMaterialComponent_th_27_Template_nb_checkbox_ngModelChange_1_listener", "$event", "_r8", "toggleSelectAll", "selectAll", "PictureMaterialComponent_tr_38_td_1_Template_nb_checkbox_ngModelChange_1_listener", "_r9", "item_r10", "$implicit", "toggleSelectItem", "CId", "isItemSelected", "PictureMaterialComponent_tr_38_button_12_Template_button_click_0_listener", "_r11", "PictureMaterialComponent_tr_38_button_13_Template_button_click_0_listener", "_r12", "changePicture", "ɵɵtemplate", "PictureMaterialComponent_tr_38_td_1_Template", "PictureMaterialComponent_tr_38_button_12_Template", "PictureMaterialComponent_tr_38_button_13_Template", "isRead", "images", "length", "ɵɵtextInterpolate", "CPictureCode", "getCategoryLabel", "selectedCate<PERSON><PERSON>", "ɵɵpipeBind2", "CUpdateDT", "imageCounter", "currentImageInfo", "PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_1_listener", "_r14", "previousImage", "ɵɵelement", "PictureMaterialComponent_ng_template_41_div_6_div_6_Template_button_click_6_listener", "nextImage", "PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template_div_click_0_listener", "i_r16", "_r15", "index", "goToImage", "ɵɵclassProp", "currentImageIndex", "ɵɵpipeBind1", "img_r17", "CBase64", "CFile", "ɵɵsanitizeUrl", "PictureMaterialComponent_ng_template_41_div_6_div_7_div_2_Template", "currentPreviewImages", "PictureMaterialComponent_ng_template_41_div_6_div_5_Template", "PictureMaterialComponent_ng_template_41_div_6_div_6_Template", "PictureMaterialComponent_ng_template_41_div_6_div_7_Template", "currentImageShowing", "PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_input_blur_2_listener", "i_r22", "_r21", "renameFile", "PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template_button_click_6_listener", "picture_r23", "removeImage", "id", "name", "data", "PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_3_listener", "_r18", "inputFile_r19", "click", "PictureMaterialComponent_ng_template_41_ng_template_7_Template_button_click_5_listener", "zipInputFile_r20", "PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_7_listener", "detectFiles", "PictureMaterialComponent_ng_template_41_ng_template_7_Template_input_change_9_listener", "PictureMaterialComponent_ng_template_41_ng_template_7_tr_21_Template", "ɵɵclassMap", "isEdit", "listPictures", "PictureMaterialComponent_ng_template_41_button_13_Template_button_click_0_listener", "_r25", "ref_r24", "dialogRef", "uploadImage", "PictureMaterialComponent_ng_template_41_span_4_Template", "PictureMaterialComponent_ng_template_41_div_6_Template", "PictureMaterialComponent_ng_template_41_ng_template_7_Template", "ɵɵtemplateRefExtractor", "PictureMaterialComponent_ng_template_41_Template_button_click_11_listener", "_r13", "close", "resetPreviewState", "PictureMaterialComponent_ng_template_41_button_13_Template", "isPreviewMode", "PictureCategory", "BUILDING_MATERIAL", "upload_r26", "PictureMaterialComponent", "selectedItems", "size", "category", "option", "categoryOptions", "find", "opt", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_buildCaseService", "message", "_utilityService", "listUserBuildCases", "NONE", "isCategorySelected", "Set", "SCHEMATIC", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getPicturelList", "subscribe", "openPdfInNewTab", "CFileUrl", "openFileInNewTab", "pageIndex", "clear", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "cPictureType", "totalRecords", "TotalItems", "updateSelectAllState", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "categoryChanged", "ref", "item", "showErrorMSG", "open", "findIndex", "img", "getCurrentPreviewImage", "currentEditItem", "validation", "nameSet", "has", "addErrorMessage", "add", "onSubmit", "event", "target", "files", "file", "type", "toLowerCase", "endsWith", "processZipFile", "processSingleImageFile", "zipFile", "zip", "loadAsync", "then", "contents", "imageFiles", "for<PERSON>ach", "relativePath", "dir", "isImageFile", "push", "async", "blob", "fileName", "split", "pop", "imageFile", "File", "getImageMimeType", "processImageFileFromZip", "Promise", "all", "showSucessMSG", "catch", "error", "console", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "addImageToList", "originalPath", "resolve", "reject", "onerror", "fileNameWithoutExtension", "existingFileIndex", "picture", "extension", "getFileExtension", "Date", "getTime", "Math", "random", "imageExtensions", "substring", "lastIndexOf", "includes", "pictureId", "filter", "x", "map", "errorMessages", "showErrorMSGs", "uploadRequest", "apiPictureUploadListPicturePost$Json", "CPath", "Message", "updateRequest", "apiPictureUpdatePicturePost$Json", "CBuildCaseID", "CPictureID", "slice", "newFile", "itemId", "delete", "checked", "currentPageIds", "undefined", "every", "selectedIds", "Array", "from", "confirm", "apiPictureDeletePicturePost$Json", "currentImage", "handleKeyboardEvent", "key", "preventDefault", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "PictureService", "BuildCaseService", "i5", "MessageService", "i6", "UtilityService", "selectors", "hostBindings", "PictureMaterialComponent_HostBindings", "rf", "ctx", "PictureMaterialComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ɵɵtwoWayListener", "PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ɵɵtwoWayBindingSet", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "PictureMaterialComponent_nb_option_12_Template", "PictureMaterialComponent_Template_nb_select_ngModelChange_17_listener", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_17_listener", "PictureMaterialComponent_nb_option_18_Template", "PictureMaterialComponent_button_21_Template", "PictureMaterialComponent_button_22_Template", "PictureMaterialComponent_th_27_Template", "PictureMaterialComponent_tr_38_Template", "PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_40_listener", "PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_40_listener", "PictureMaterialComponent_Template_ngx_pagination_PageChange_40_listener", "PictureMaterialComponent_ng_template_41_Template", "ɵɵtwoWayProperty", "isCreate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.html"], "sourcesContent": ["import { Component, OnInit, HostListener } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse, GetInfoPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { Base64ImagePipe } from 'src/app/@theme/pipes/base64-image.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport * as JSZip from 'jszip';\r\n\r\n// 圖片類別枚舉\r\nenum PictureCategory {\r\n  NONE = 0,           // 未選擇\r\n  BUILDING_MATERIAL = 1,  // 建材圖片\r\n  SCHEMATIC = 2,       // 示意圖片\r\n  ACCESSORIES = 3       // 飾品圖片\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true, imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe,\r\n    Base64ImagePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n  images: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n  selectedBuildCaseId: number\r\n  selectedCategory: PictureCategory = PictureCategory.NONE;\r\n  isCategorySelected: boolean = false; // 追蹤用戶是否已經明確選擇類別\r\n\r\n  // 批次選擇相關屬性\r\n  selectedItems: Set<number> = new Set<number>(); // 選中的項目 ID\r\n  selectAll: boolean = false; // 全選狀態\r\n  get selectedCount(): number {\r\n    return this.selectedItems.size;\r\n  }\r\n  currentImageShowing: string = \"\"\r\n\r\n  // 輪播預覽相關屬性\r\n  currentPreviewImages: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  currentImageIndex: number = 0;\r\n  isPreviewMode: boolean = false;\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: PictureCategory.BUILDING_MATERIAL, label: '建材圖片' },\r\n    { value: PictureCategory.SCHEMATIC, label: '示意圖片' }\r\n  ]\r\n\r\n  // 讓模板可以使用 enum\r\n  PictureCategory = PictureCategory;\r\n\r\n  // 獲取類別標籤的方法\r\n  getCategoryLabel(category: number): string {\r\n    const option = this.categoryOptions.find(opt => opt.value === category);\r\n    return option ? option.label : '未知類別';\r\n  }\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  } getPicturelList(pageIndex: number) {\r\n    // 重置選擇狀態\r\n    this.selectedItems.clear();\r\n    this.selectAll = false;\r\n\r\n    if (this.selectedCategory === PictureCategory.BUILDING_MATERIAL) {\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n            this.updateSelectAllState();\r\n          }\r\n        })\r\n      )\r\n    } else if (this.selectedCategory === PictureCategory.SCHEMATIC) {\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n            this.updateSelectAllState();\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      // 如果沒有選擇類別，清空數據並返回預設的空 observable\r\n      this.images = [];\r\n      this.totalRecords = 0;\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: 1,\r\n          PageSize: 1,\r\n          CBuildCaseId: -1, // 使用無效 ID 確保返回空結果\r\n          cPictureType: PictureCategory.NONE\r\n        }\r\n      }).pipe(\r\n        tap(() => {\r\n          this.images = [];\r\n          this.totalRecords = 0;\r\n        })\r\n      )\r\n    }\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  } categoryChanged(category: PictureCategory) {\r\n    this.selectedCategory = category;\r\n    this.isCategorySelected = true; // 標記用戶已經選擇了類別\r\n    this.getPicturelList(1).subscribe();\r\n  } addNew(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 如果是新增圖片（沒有傳入 item），則檢查是否已選擇類別\r\n    if (!item && !this.isCategorySelected) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    this.listPictures = []\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n\r\n    if (!!item) {\r\n      // 預覽模式 - 顯示當前類別的所有圖片並支持輪播\r\n      this.isPreviewMode = true;\r\n      this.currentPreviewImages = [...this.images];\r\n      this.currentImageIndex = this.images.findIndex(img => img.CId === item.CId);\r\n      if (this.currentImageIndex === -1) {\r\n        this.currentImageIndex = 0;\r\n      }\r\n      this.currentImageShowing = this.getCurrentPreviewImage();\r\n    } else {\r\n      // 上傳模式\r\n      this.isPreviewMode = false;\r\n      this.currentPreviewImages = [];\r\n      this.currentImageIndex = 0;\r\n      this.currentImageShowing = \"\";\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n  changePicture(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    // 檢查是否已選擇類別\r\n    if (!this.selectedCategory) {\r\n      this.message.showErrorMSG('請先選擇圖片類別');\r\n      return;\r\n    }\r\n\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation(CFile: any) {\r\n    this.valid.clear();\r\n    const nameSet = new Set();\r\n    for (const item of CFile) {\r\n      if (nameSet.has(item.name)) {\r\n        this.valid.addErrorMessage('檔名不可重複')\r\n        return;\r\n      }\r\n      nameSet.add(item.name);\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        // 檢查是否為 ZIP 檔案\r\n        if (file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')) {\r\n          this.processZipFile(file);\r\n        } else {\r\n          this.processSingleImageFile(file);\r\n        }\r\n      }\r\n    }\r\n    // Reset input file to be able to select the old file again\r\n    event.target.value = null;\r\n  }\r\n\r\n  processZipFile(zipFile: File) {\r\n    const zip = new JSZip();\r\n\r\n    zip.loadAsync(zipFile).then((contents) => {\r\n      const imageFiles: Promise<any>[] = []; contents.forEach((relativePath, file) => {\r\n        // 只處理圖片檔案，跳過資料夾\r\n        if (!file.dir && this.isImageFile(relativePath)) {\r\n          imageFiles.push(\r\n            file.async('blob').then((blob) => {\r\n              // 只取檔案名稱，移除資料夾路徑\r\n              const fileName = relativePath.split('/').pop() || relativePath.split('\\\\').pop() || relativePath;\r\n              // 建立 File 物件\r\n              const imageFile = new File([blob], fileName, { type: this.getImageMimeType(relativePath) });\r\n              return this.processImageFileFromZip(imageFile, fileName);\r\n            })\r\n          );\r\n        }\r\n      });\r\n\r\n      // 處理所有圖片檔案\r\n      Promise.all(imageFiles).then(() => {\r\n        this.message.showSucessMSG(`成功從 ZIP 檔案中匯入 ${imageFiles.length} 張圖片`);\r\n      }).catch((error) => {\r\n        console.error('處理 ZIP 檔案中的圖片時發生錯誤:', error);\r\n        this.message.showErrorMSG('處理 ZIP 檔案中的圖片時發生錯誤');\r\n      });\r\n\r\n    }).catch((error) => {\r\n      console.error('讀取 ZIP 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('無法讀取 ZIP 檔案，請確認檔案格式正確');\r\n    });\r\n  }\r\n\r\n  processSingleImageFile(file: File) {\r\n    let reader = new FileReader();\r\n    reader.readAsDataURL(file);\r\n    reader.onload = () => {\r\n      let base64Str: string = reader.result as string;\r\n      if (!base64Str) {\r\n        return;\r\n      }\r\n      this.addImageToList(file, base64Str);\r\n    };\r\n  }\r\n\r\n  processImageFileFromZip(file: File, originalPath: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          reject('無法讀取圖片檔案');\r\n          return;\r\n        }\r\n        this.addImageToList(file, base64Str, originalPath);\r\n        resolve();\r\n      };\r\n      reader.onerror = () => {\r\n        reject('讀取圖片檔案時發生錯誤');\r\n      };\r\n    });\r\n  }\r\n  addImageToList(file: File, base64Str: string, originalPath?: string) {\r\n    // Get name file ( no extension)\r\n    let fileName = originalPath || file.name;\r\n\r\n    // 如果是從 ZIP 檔案來的，只取檔案名稱，移除資料夾路徑\r\n    if (originalPath) {\r\n      fileName = originalPath.split('/').pop() || originalPath.split('\\\\').pop() || originalPath;\r\n    }\r\n\r\n    const fileNameWithoutExtension = fileName.split('.')[0];\r\n\r\n    // Find files with duplicate names\r\n    const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n    if (existingFileIndex !== -1) {\r\n      // If name is duplicate, update file data\r\n      this.listPictures[existingFileIndex] = {\r\n        ...this.listPictures[existingFileIndex],\r\n        data: base64Str,\r\n        CFile: file,\r\n        extension: this._utilityService.getFileExtension(fileName)\r\n      };\r\n    } else {\r\n      // If not duplicate, add new file\r\n      this.listPictures.push({\r\n        id: new Date().getTime() + Math.random(),\r\n        name: fileNameWithoutExtension,\r\n        data: base64Str,\r\n        extension: this._utilityService.getFileExtension(fileName),\r\n        CFile: file\r\n      });\r\n    }\r\n  }\r\n\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    return imageExtensions.includes(extension);\r\n  }\r\n\r\n  getImageMimeType(fileName: string): string {\r\n    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));\r\n    switch (extension) {\r\n      case '.jpg':\r\n      case '.jpeg':\r\n        return 'image/jpeg';\r\n      case '.png':\r\n        return 'image/png';\r\n      case '.gif':\r\n        return 'image/gif';\r\n      case '.bmp':\r\n        return 'image/bmp';\r\n      case '.webp':\r\n        return 'image/webp';\r\n      default:\r\n        return 'image/jpeg';\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n  uploadImage(ref: any) {\r\n    if (!this.isEdit) {\r\n      const CFile = this.listPictures.map(x => x.CFile)\r\n      this.validation(CFile)\r\n      if (this.valid.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this.valid.errorMessages);\r\n        return\r\n      }      // 統一使用 PictureService 進行上傳\r\n      const uploadRequest = this._pictureService.apiPictureUploadListPicturePost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CPath: this.selectedCategory === PictureCategory.BUILDING_MATERIAL ? \"picture\" : \"infoPicture\",\r\n          CFile: CFile,\r\n          cPictureType: this.selectedCategory\r\n        }\r\n      }); uploadRequest.pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n          this.resetPreviewState();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else {\r\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\r\n        // 統一使用 PictureService 進行更新\r\n        const updateRequest = this._pictureService.apiPictureUpdatePicturePost$Json({\r\n          body: {\r\n            CBuildCaseID: this.selectedBuildCaseId,\r\n            CPictureID: this.currentEditItem,\r\n            CFile: this.listPictures[0].CFile\r\n          }\r\n        }); updateRequest.pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n            this.resetPreviewState();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n  // 批次選擇相關方法\r\n  toggleSelectItem(itemId: number) {\r\n    if (this.selectedItems.has(itemId)) {\r\n      this.selectedItems.delete(itemId);\r\n    } else {\r\n      this.selectedItems.add(itemId);\r\n    }\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  toggleSelectAll(checked: boolean) {\r\n    this.selectAll = checked;\r\n    if (checked) {\r\n      // 全選當前頁面的所有項目\r\n      this.images.forEach(item => {\r\n        if (item.CId) {\r\n          this.selectedItems.add(item.CId);\r\n        }\r\n      });\r\n    } else {\r\n      // 取消全選 - 只移除當前頁面的項目\r\n      const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];\r\n      currentPageIds.forEach(id => this.selectedItems.delete(id));\r\n    }\r\n  }\r\n\r\n  updateSelectAllState() {\r\n    const currentPageIds = this.images.map(item => item.CId).filter(id => id !== undefined) as number[];\r\n    this.selectAll = currentPageIds.length > 0 && currentPageIds.every(id => this.selectedItems.has(id));\r\n  }\r\n  // 批次刪除方法\r\n  batchDelete() {\r\n    if (this.selectedItems.size === 0) {\r\n      this.message.showErrorMSG('請選擇要刪除的項目');\r\n      return;\r\n    }\r\n\r\n    const selectedIds = Array.from(this.selectedItems);\r\n\r\n    // 顯示確認對話框\r\n    if (confirm(`確定要刪除選中的 ${selectedIds.length} 個項目嗎？`)) {\r\n      this._pictureService.apiPictureDeletePicturePost$Json({\r\n        body: selectedIds\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(`成功刪除 ${selectedIds.length} 個項目`);\r\n            // 清空選擇狀態\r\n            this.selectedItems.clear();\r\n            this.selectAll = false;\r\n          } else {\r\n            this.message.showErrorMSG(res.Message || '刪除失敗');\r\n          }\r\n        }),\r\n        concatMap((res) => res.StatusCode === 0 ? this.getPicturelList(this.pageIndex) : of(null))\r\n      ).subscribe();\r\n    }\r\n  }\r\n  // 檢查項目是否被選中\r\n  isItemSelected(itemId: number): boolean {\r\n    return this.selectedItems.has(itemId);\r\n  }\r\n  // 輪播預覽相關方法\r\n  getCurrentPreviewImage(): string {\r\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\r\n      return '';\r\n    }\r\n    const currentImage = this.currentPreviewImages[this.currentImageIndex];\r\n    // 優先使用 CBase64，如果沒有則使用 CFile\r\n    return currentImage.CBase64 || currentImage.CFile || '';\r\n  }\r\n\r\n  previousImage(): void {\r\n    if (this.currentPreviewImages.length === 0) return;\r\n    this.currentImageIndex = this.currentImageIndex > 0 ? this.currentImageIndex - 1 : this.currentPreviewImages.length - 1;\r\n    this.currentImageShowing = this.getCurrentPreviewImage();\r\n  }\r\n  nextImage(): void {\r\n    if (this.currentPreviewImages.length === 0) return;\r\n    this.currentImageIndex = this.currentImageIndex < this.currentPreviewImages.length - 1 ? this.currentImageIndex + 1 : 0;\r\n    this.currentImageShowing = this.getCurrentPreviewImage();\r\n  }\r\n\r\n  goToImage(index: number): void {\r\n    if (index >= 0 && index < this.currentPreviewImages.length) {\r\n      this.currentImageIndex = index;\r\n      this.currentImageShowing = this.getCurrentPreviewImage();\r\n    }\r\n  }\r\n\r\n  get currentImageInfo() {\r\n    if (this.currentPreviewImages.length === 0 || this.currentImageIndex < 0 || this.currentImageIndex >= this.currentPreviewImages.length) {\r\n      return null;\r\n    }\r\n    return this.currentPreviewImages[this.currentImageIndex];\r\n  }\r\n  get imageCounter(): string {\r\n    if (this.currentPreviewImages.length === 0) return '';\r\n    return `${this.currentImageIndex + 1} / ${this.currentPreviewImages.length}`;\r\n  }\r\n  // 重置預覽狀態\r\n  resetPreviewState(): void {\r\n    this.isPreviewMode = false;\r\n    this.currentPreviewImages = [];\r\n    this.currentImageIndex = 0;\r\n    this.currentImageShowing = \"\";\r\n  }\r\n\r\n  // 鍵盤導航支持\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeyboardEvent(event: KeyboardEvent): void {\r\n    if (!this.isPreviewMode || this.currentPreviewImages.length <= 1) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.previousImage();\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.nextImage();\r\n        break;\r\n      case 'Escape':\r\n        event.preventDefault();\r\n        // 可以在這裡添加關閉對話框的邏輯\r\n        break;\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可設定上傳建材示意圖片，上傳前請將圖片檔案改為建材圖片檔名。</h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" (selectedChange)=\"selectedChange($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listUserBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"category\" class=\"label mr-2\">類別</label>\r\n          <nb-select placeholder=\"圖片類別\" [(ngModel)]=\"selectedCategory\" (selectedChange)=\"categoryChanged($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let option of categoryOptions\" [value]=\"option.value\">\r\n              {{ option.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"batchDelete()\" [disabled]=\"selectedCount === 0\"\r\n            *ngIf=\"isRead && images.length > 0\">\r\n            批次刪除 ({{selectedCount}})\r\n          </button>\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            圖片上傳</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\" *ngIf=\"isRead && images.length > 0\">\r\n              <nb-checkbox [ngModel]=\"selectAll\" (ngModelChange)=\"toggleSelectAll($event)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </th>\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">類別</th>\r\n            <th scope=\"col\" class=\"col-1\">最新圖片上傳時間</th>\r\n            <th scope=\"col\" class=\"col-1\"></th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of images ; let i = index\">\r\n            <td *ngIf=\"isRead && images.length > 0\">\r\n              <nb-checkbox [ngModel]=\"isItemSelected(item.CId!)\" (ngModelChange)=\"toggleSelectItem(item.CId!)\">\r\n              </nb-checkbox>\r\n            </td>\r\n            <td>{{ item.CId}}</td>\r\n            <td>\r\n              <!-- <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CName}}</a> -->\r\n              {{ item?.CPictureCode}}\r\n            </td>\r\n            <td>{{ getCategoryLabel(selectedCategory) }}</td>\r\n            <td>{{ item?.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"addNew(dialog, item)\">預覽</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"changePicture(dialog, item)\">改變圖片</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\"> <nb-card style=\"height: 100%; overflow: auto; width: 700px;\"\r\n    class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        {{isPreviewMode ? '圖片預覽' : (selectedCategory === PictureCategory.BUILDING_MATERIAL ? '建材圖片' : '示意圖片') + '上傳'}}\r\n      </span>\r\n      <span *ngIf=\"isPreviewMode && currentPreviewImages.length > 1\" class=\"ml-2 text-muted\">\r\n        ({{imageCounter}})\r\n      </span>\r\n    </nb-card-header> <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\" *ngIf=\"isPreviewMode; else upload\">\r\n        <!-- 輪播預覽區域 -->\r\n        <div class=\"preview-container position-relative\" style=\"min-height: 400px;\"> <!-- 圖片顯示區 -->\r\n          <div class=\"text-center mb-3\">\r\n            <img class=\"fit-size\" [src]=\"currentImageShowing | base64Image \"\r\n              style=\"max-height: 400px; max-width: 100%; object-fit: contain;\">\r\n          </div>\r\n\r\n          <!-- 圖片信息 -->\r\n          <div class=\"text-center mb-3\" *ngIf=\"currentImageInfo\">\r\n            <p class=\"mb-1\"><strong>圖片名稱:</strong> {{currentImageInfo.CPictureCode}}</p>\r\n            <p class=\"mb-1\"><strong>更新時間:</strong> {{currentImageInfo.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</p>\r\n          </div>\r\n\r\n          <!-- 輪播控制按鈕 -->\r\n          <div class=\"carousel-controls d-flex justify-content-between align-items-center\"\r\n            *ngIf=\"currentPreviewImages.length > 1\">\r\n            <button class=\"btn btn-outline-primary btn-sm\" (click)=\"previousImage()\">\r\n              <i class=\"nb-chevron-left\"></i> 上一張\r\n            </button>\r\n\r\n            <span class=\"text-muted\">{{imageCounter}}</span>\r\n\r\n            <button class=\"btn btn-outline-primary btn-sm\" (click)=\"nextImage()\">\r\n              下一張 <i class=\"nb-chevron-right\"></i>\r\n            </button>\r\n          </div>\r\n          <!-- 縮圖導航 -->\r\n          <div class=\"thumbnail-nav mt-3\" *ngIf=\"currentPreviewImages.length > 1\">\r\n            <div class=\"d-flex flex-wrap justify-content-center\">\r\n              <div *ngFor=\"let img of currentPreviewImages; let i = index\" class=\"thumbnail-item m-1\"\r\n                [class.active]=\"i === currentImageIndex\" (click)=\"goToImage(i)\"\r\n                style=\"cursor: pointer; border: 2px solid transparent; padding: 2px;\">\r\n                <img [src]=\"(img.CBase64 || img.CFile) | base64Image \"\r\n                  style=\"width: 60px; height: 60px; object-fit: cover; border-radius: 4px;\">\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ng-template #upload>\r\n        <div class=\"mb-3\">\r\n          <h6>上傳方式：</h6>\r\n          <button\r\n            [class]=\"!isEdit ? 'btn btn-info mr-2' : listPictures.length < 1 ? 'btn btn-info mr-2' : 'btn btn-info disable mr-2'\"\r\n            (click)=\"inputFile.click()\">單張圖片上傳</button>\r\n          <button [class]=\"!isEdit ? 'btn btn-success' : 'btn btn-success disable'\" (click)=\"zipInputFile.click()\"\r\n            [disabled]=\"isEdit\">ZIP 批量匯入</button>\r\n        </div>\r\n        <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\"\r\n          accept=\"image/png, image/gif, image/jpeg\" multiple>\r\n        <input #zipInputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\" accept=\".zip,application/zip\">\r\n        <div class=\"mt-3 w-full flex flex-col\">\r\n          <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr style=\"background-color: #27ae60; color: white;\">\r\n                <th scope=\"col\" class=\"col-4\">文件名</th>\r\n                <th scope=\"col\" class=\"col-1\">檢視</th>\r\n                <th scope=\"col\" class=\"col-1\"></th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let picture of listPictures; let i = index\">\r\n                <td>\r\n                  <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                    (blur)=\"renameFile($event, i)\">\r\n                </td>\r\n                <td class=\"w-[100px] h-auto\">\r\n                  <img class=\"fit-size\" [src]=\"picture.data\">\r\n                </td>\r\n                <td class=\"text-center w-32\">\r\n                  <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeImage(picture.id)\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-template>\r\n    </nb-card-body> <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close(); resetPreviewState()\">取消</button>\r\n        <button *ngIf=\"!isPreviewMode\" class=\"btn btn-success\" (click)=\"uploadImage(ref)\">儲存</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAEnD,SAASC,eAAe,QAAQ,wCAAwC;AAGxE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,OAAO,KAAKC,KAAK,MAAM,OAAO;;;;;;;;;;;;;;ICFlBC,EAAA,CAAAC,cAAA,oBAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IAC7EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;IASAT,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAC,KAAA,CAAsB;IACtEX,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAE,KAAA,MACF;;;;;;IAMFZ,EAAA,CAAAC,cAAA,iBACsC;IADFD,EAAA,CAAAa,UAAA,mBAAAC,oEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAEzDpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHmDH,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAI,aAAA,OAAgC;IAE1FrB,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,gCAAAS,MAAA,CAAAI,aAAA,OACF;;;;;;IACArB,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAa,UAAA,mBAAAS,oEAAA;MAAAtB,EAAA,CAAAe,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAM,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAS,MAAA,CAAAF,SAAA,CAAc;IAAA,EAAC;IACnDxB,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAUXH,EADF,CAAAC,cAAA,aAAkE,sBACa;IAA1CD,EAAA,CAAAa,UAAA,2BAAAc,6EAAAC,MAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAiBF,MAAA,CAAAa,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IAC1E5B,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACX;;;;IAHUH,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAc,SAAA,CAAqB;;;;;;IAclC/B,EADF,CAAAC,cAAA,SAAwC,sBAC2D;IAA9CD,EAAA,CAAAa,UAAA,2BAAAmB,kFAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAkB,GAAA;MAAA,MAAAC,QAAA,GAAAlC,EAAA,CAAAkB,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAiBF,MAAA,CAAAmB,gBAAA,CAAAF,QAAA,CAAAG,GAAA,CAA2B;IAAA,EAAC;IAElGrC,EADE,CAAAG,YAAA,EAAc,EACX;;;;;IAFUH,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAqB,cAAA,CAAAJ,QAAA,CAAAG,GAAA,EAAqC;;;;;;IAWlDrC,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAa,UAAA,mBAAA0B,0EAAA;MAAAvC,EAAA,CAAAe,aAAA,CAAAyB,IAAA;MAAA,MAAAN,QAAA,GAAAlC,EAAA,CAAAkB,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAM,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAS,MAAA,CAAAF,SAAA,EAAAU,QAAA,CAAoB;IAAA,EAAC;IAAClC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBACwC;IAAtCD,EAAA,CAAAa,UAAA,mBAAA4B,0EAAA;MAAAzC,EAAA,CAAAe,aAAA,CAAA2B,IAAA;MAAA,MAAAR,QAAA,GAAAlC,EAAA,CAAAkB,aAAA,GAAAiB,SAAA;MAAA,MAAAlB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,MAAAM,SAAA,GAAAxB,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA0B,aAAA,CAAAnB,SAAA,EAAAU,QAAA,CAA2B;IAAA,EAAC;IAAClC,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAhBzDH,EAAA,CAAAC,cAAA,SAAgD;IAC9CD,EAAA,CAAA4C,UAAA,IAAAC,4CAAA,iBAAwC;IAIxC7C,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAEFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA4C,UAAA,KAAAE,iDAAA,qBACiC,KAAAC,iDAAA,qBAEO;IAE5C/C,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAjBEH,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,MAAA,IAAA/B,MAAA,CAAAgC,MAAA,CAAAC,MAAA,KAAiC;IAIlClD,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAmD,iBAAA,CAAAjB,QAAA,CAAAG,GAAA,CAAa;IAGfrC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA0B,QAAA,kBAAAA,QAAA,CAAAkB,YAAA,MACF;IACIpD,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAmD,iBAAA,CAAAlC,MAAA,CAAAoC,gBAAA,CAAApC,MAAA,CAAAqC,gBAAA,EAAwC;IACxCtD,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAmD,iBAAA,CAAAnD,EAAA,CAAAuD,WAAA,QAAArB,QAAA,kBAAAA,QAAA,CAAAsB,SAAA,yBAAkD;IAEAxD,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,MAAA,CAAY;IAEZhD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA+B,MAAA,CAAY;;;;;IAqBxEhD,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,OAAAS,MAAA,CAAAwC,YAAA,OACF;;;;;IAYsBzD,EADlB,CAAAC,cAAA,cAAuD,YACrC,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAhB,CAAAC,cAAA,YAAgB,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAA4D;;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACnG;;;;IAFmCH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAQ,kBAAA,MAAAS,MAAA,CAAAyC,gBAAA,CAAAN,YAAA,KAAiC;IACjCpD,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAAuD,WAAA,OAAAtC,MAAA,CAAAyC,gBAAA,CAAAF,SAAA,6BAA4D;;;;;;IAMnGxD,EAFF,CAAAC,cAAA,cAC0C,iBACiC;IAA1BD,EAAA,CAAAa,UAAA,mBAAA8C,qFAAA;MAAA3D,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA4C,aAAA,EAAe;IAAA,EAAC;IACtE7D,EAAA,CAAA8D,SAAA,YAA+B;IAAC9D,EAAA,CAAAE,MAAA,2BAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEhDH,EAAA,CAAAC,cAAA,iBAAqE;IAAtBD,EAAA,CAAAa,UAAA,mBAAAkD,qFAAA;MAAA/D,EAAA,CAAAe,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA+C,SAAA,EAAW;IAAA,EAAC;IAClEhE,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAA8D,SAAA,YAAgC;IAExC9D,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAmD,iBAAA,CAAAlC,MAAA,CAAAwC,YAAA,CAAgB;;;;;;IASvCzD,EAAA,CAAAC,cAAA,cAEwE;IAD7BD,EAAA,CAAAa,UAAA,mBAAAoD,wFAAA;MAAA,MAAAC,KAAA,GAAAlE,EAAA,CAAAe,aAAA,CAAAoD,IAAA,EAAAC,KAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAoD,SAAA,CAAAH,KAAA,CAAY;IAAA,EAAC;IAE/DlE,EAAA,CAAA8D,SAAA,cAC4E;;IAC9E9D,EAAA,CAAAG,YAAA,EAAM;;;;;;IAJJH,EAAA,CAAAsE,WAAA,WAAAJ,KAAA,KAAAjD,MAAA,CAAAsD,iBAAA,CAAwC;IAEnCvE,EAAA,CAAAO,SAAA,EAAiD;IAAjDP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAAwE,WAAA,OAAAC,OAAA,CAAAC,OAAA,IAAAD,OAAA,CAAAE,KAAA,GAAA3E,EAAA,CAAA4E,aAAA,CAAiD;;;;;IAJ1D5E,EADF,CAAAC,cAAA,cAAwE,cACjB;IACnDD,EAAA,CAAA4C,UAAA,IAAAiC,kEAAA,kBAEwE;IAK5E7E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAPmBH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAA6D,oBAAA,CAAyB;;;;;IA3BlD9E,EAHJ,CAAAC,cAAA,cAA8D,cAEgB,cAC5C;IAC5BD,EAAA,CAAA8D,SAAA,cACmE;;IACrE9D,EAAA,CAAAG,YAAA,EAAM;IAsBNH,EAnBA,CAAA4C,UAAA,IAAAmC,4DAAA,mBAAuD,IAAAC,4DAAA,kBAOb,IAAAC,4DAAA,kBAY8B;IAW5EjF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnCsBH,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAAwE,WAAA,OAAAvD,MAAA,CAAAiE,mBAAA,GAAAlF,EAAA,CAAA4E,aAAA,CAA0C;IAKnC5E,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAyC,gBAAA,CAAsB;IAOlD1D,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6D,oBAAA,CAAA5B,MAAA,KAAqC;IAYPlD,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAA6D,oBAAA,CAAA5B,MAAA,KAAqC;;;;;;IAqC9DlD,EAFJ,CAAAC,cAAA,SAAwD,SAClD,gBAE+B;IAA/BD,EAAA,CAAAa,UAAA,kBAAAsE,2FAAAvD,MAAA;MAAA,MAAAwD,KAAA,GAAApF,EAAA,CAAAe,aAAA,CAAAsE,IAAA,EAAAjB,KAAA;MAAA,MAAAnD,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAQF,MAAA,CAAAqE,UAAA,CAAA1D,MAAA,EAAAwD,KAAA,CAAqB;IAAA,EAAC;IAClCpF,EAFE,CAAAG,YAAA,EACiC,EAC9B;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAA8D,SAAA,cAA2C;IAC7C9D,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA6B,iBACyD;IAAlCD,EAAA,CAAAa,UAAA,mBAAA0E,6FAAA;MAAA,MAAAC,WAAA,GAAAxF,EAAA,CAAAe,aAAA,CAAAsE,IAAA,EAAAlD,SAAA;MAAA,MAAAlB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAwE,WAAA,CAAAD,WAAA,CAAAE,EAAA,CAAuB;IAAA,EAAC;IAAC1F,EAAA,CAAAE,MAAA,mBAAE;IAE1FF,EAF0F,CAAAG,YAAA,EAAS,EAC5F,EACF;;;;IATwDH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,UAAAoF,WAAA,CAAAG,IAAA,CAAsB;IAIzD3F,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,QAAAoF,WAAA,CAAAI,IAAA,EAAA5F,EAAA,CAAA4E,aAAA,CAAoB;;;;;;IA1BlD5E,EADF,CAAAC,cAAA,cAAkB,SACZ;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,iBAE8B;IAA5BD,EAAA,CAAAa,UAAA,mBAAAgF,uFAAA;MAAA7F,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAAC,aAAA,GAAA/F,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAmB,WAAA,CAAS4E,aAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAChG,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,iBACsB;IADoDD,EAAA,CAAAa,UAAA,mBAAAoF,uFAAA;MAAAjG,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAAI,gBAAA,GAAAlG,EAAA,CAAAyB,WAAA;MAAA,OAAAzB,EAAA,CAAAmB,WAAA,CAAS+E,gBAAA,CAAAF,KAAA,EAAoB;IAAA,EAAC;IAClFhG,EAAA,CAAAE,MAAA,mCAAQ;IAChCF,EADgC,CAAAG,YAAA,EAAS,EACnC;IACNH,EAAA,CAAAC,cAAA,mBACqD;IADRD,EAAA,CAAAa,UAAA,oBAAAsF,uFAAAvE,MAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAUF,MAAA,CAAAmF,WAAA,CAAAxE,MAAA,CAAmB;IAAA,EAAC;IAA3E5B,EAAA,CAAAG,YAAA,EACqD;IACrDH,EAAA,CAAAC,cAAA,mBAA6G;IAA7DD,EAAA,CAAAa,UAAA,oBAAAwF,uFAAAzE,MAAA;MAAA5B,EAAA,CAAAe,aAAA,CAAA+E,IAAA;MAAA,MAAA7E,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAUF,MAAA,CAAAmF,WAAA,CAAAxE,MAAA,CAAmB;IAAA,EAAC;IAA9E5B,EAAA,CAAAG,YAAA,EAA6G;IAKrGH,EAJR,CAAAC,cAAA,eAAuC,iBACuC,aACnE,cACgD,cACrB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAA8D,SAAA,cAAmC;IAEvC9D,EADE,CAAAG,YAAA,EAAK,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4C,UAAA,KAAA0D,oEAAA,iBAAwD;IAc9DtG,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAhCFH,EAAA,CAAAO,SAAA,GAAqH;IAArHP,EAAA,CAAAuG,UAAA,EAAAtF,MAAA,CAAAuF,MAAA,yBAAAvF,MAAA,CAAAwF,YAAA,CAAAvD,MAAA,yDAAqH;IAE/GlD,EAAA,CAAAO,SAAA,GAAiE;IAAjEP,EAAA,CAAAuG,UAAA,EAAAtF,MAAA,CAAAuF,MAAA,iDAAiE;IACvExG,EAAA,CAAAI,UAAA,aAAAa,MAAA,CAAAuF,MAAA,CAAmB;IAeOxG,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAa,MAAA,CAAAwF,YAAA,CAAiB;;;;;;IAmB/CzG,EAAA,CAAAC,cAAA,iBAAkF;IAA3BD,EAAA,CAAAa,UAAA,mBAAA6F,mFAAA;MAAA1G,EAAA,CAAAe,aAAA,CAAA4F,IAAA;MAAA,MAAAC,OAAA,GAAA5G,EAAA,CAAAkB,aAAA,GAAA2F,SAAA;MAAA,MAAA5F,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAA6F,WAAA,CAAAF,OAAA,CAAgB;IAAA,EAAC;IAAC5G,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAzF/FH,EAH6C,CAAAC,cAAA,kBACvB,qBACR,WACR;IACJD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAA4C,UAAA,IAAAmE,uDAAA,mBAAuF;IAGzF/G,EAAA,CAAAG,YAAA,EAAiB;IAACH,EAAA,CAAAC,cAAA,uBAAwC;IA0CxDD,EAzCA,CAAA4C,UAAA,IAAAoE,sDAAA,kBAA8D,IAAAC,8DAAA,iCAAAjH,EAAA,CAAAkH,sBAAA,CAyCzC;IAsCvBlH,EAAA,CAAAG,YAAA,EAAe;IAEXH,EAFY,CAAAC,cAAA,qBAAgB,eACgB,kBACmC;IAA3CD,EAAA,CAAAa,UAAA,mBAAAsG,0EAAA;MAAA,MAAAP,OAAA,GAAA5G,EAAA,CAAAe,aAAA,CAAAqG,IAAA,EAAAP,SAAA;MAAA,MAAA5F,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAS0F,OAAA,CAAAS,KAAA,EAAW;MAAA,OAAArH,EAAA,CAAAmB,WAAA,CAAEF,MAAA,CAAAqG,iBAAA,EAAmB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1FH,EAAA,CAAA4C,UAAA,KAAA2E,0DAAA,qBAAkF;IAGxFvH,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;;IA3FJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAS,MAAA,CAAAuG,aAAA,iCAAAvG,MAAA,CAAAqC,gBAAA,KAAArC,MAAA,CAAAwG,eAAA,CAAAC,iBAAA,kFACF;IACO1H,EAAA,CAAAO,SAAA,EAAsD;IAAtDP,EAAA,CAAAI,UAAA,SAAAa,MAAA,CAAAuG,aAAA,IAAAvG,MAAA,CAAA6D,oBAAA,CAAA5B,MAAA,KAAsD;IAIjClD,EAAA,CAAAO,SAAA,GAAqB;IAAAP,EAArB,CAAAI,UAAA,SAAAa,MAAA,CAAAuG,aAAA,CAAqB,aAAAG,UAAA,CAAW;IAkFjD3H,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,UAAAa,MAAA,CAAAuG,aAAA,CAAoB;;;ADpKrC;AACA,IAAKC,eAKJ;AALD,WAAKA,eAAe;EAClBA,eAAA,CAAAA,eAAA,sBAAQ;EACRA,eAAA,CAAAA,eAAA,gDAAqB;EACrBA,eAAA,CAAAA,eAAA,gCAAa;EACbA,eAAA,CAAAA,eAAA,oCAAe,EAAO;AACxB,CAAC,EALIA,eAAe,KAAfA,eAAe;AAmBpB,OAAM,MAAOG,wBAAyB,SAAQ9H,aAAa;EAUzD,IAAIuB,aAAaA,CAAA;IACf,OAAO,IAAI,CAACwG,aAAa,CAACC,IAAI;EAChC;EAsBA;EACAzE,gBAAgBA,CAAC0E,QAAgB;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACxH,KAAK,KAAKoH,QAAQ,CAAC;IACvE,OAAOC,MAAM,GAAGA,MAAM,CAACpH,KAAK,GAAG,MAAM;EACvC;EACAwH,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IA7CzB,KAAA1F,MAAM,GAA4D,EAAE;IACpE,KAAA2F,kBAAkB,GAA8B,EAAE;IAElD,KAAAtF,gBAAgB,GAAoBmE,eAAe,CAACoB,IAAI;IACxD,KAAAC,kBAAkB,GAAY,KAAK,CAAC,CAAC;IAErC;IACA,KAAAjB,aAAa,GAAgB,IAAIkB,GAAG,EAAU,CAAC,CAAC;IAChD,KAAAhH,SAAS,GAAY,KAAK,CAAC,CAAC;IAI5B,KAAAmD,mBAAmB,GAAW,EAAE;IAEhC;IACA,KAAAJ,oBAAoB,GAA4D,EAAE;IAClF,KAAAP,iBAAiB,GAAW,CAAC;IAC7B,KAAAiD,aAAa,GAAY,KAAK;IAE9B,KAAAf,YAAY,GAAU,EAAE;IAExB,KAAAD,MAAM,GAAY,KAAK;IAGvB;IACA,KAAAyB,eAAe,GAAG,CAChB;MAAEtH,KAAK,EAAE8G,eAAe,CAACC,iBAAiB;MAAE9G,KAAK,EAAE;IAAM,CAAE,EAC3D;MAAED,KAAK,EAAE8G,eAAe,CAACuB,SAAS;MAAEpI,KAAK,EAAE;IAAM,CAAE,CACpD;IAED;IACA,KAAA6G,eAAe,GAAGA,eAAe;EAiBjC;EAESwB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACT,iBAAiB,CAACU,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACHzJ,GAAG,CAAC0J,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACV,kBAAkB,GAAGS,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC,CAAC,CAACtI,GAAI;MAC5D;IACF,CAAC,CAAC,EACFb,SAAS,CAAC,MAAM,IAAI,CAACgK,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACC,SAAS,EAAE;EACjB;EAEAC,eAAeA,CAACC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACjB,eAAe,CAACkB,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EAAEH,eAAeA,CAACK,SAAiB;IACjC;IACA,IAAI,CAACjC,aAAa,CAACkC,KAAK,EAAE;IAC1B,IAAI,CAAChI,SAAS,GAAG,KAAK;IAEtB,IAAI,IAAI,CAACuB,gBAAgB,KAAKmE,eAAe,CAACC,iBAAiB,EAAE;MAC/D,OAAO,IAAI,CAACc,eAAe,CAACwB,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEJ,SAAS;UACpBK,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACb,mBAAmB;UACtCc,YAAY,EAAE,IAAI,CAAChH;;OAEtB,CAAC,CAAC8F,IAAI,CACLzJ,GAAG,CAAC0J,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACrG,MAAM,GAAGoG,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACgB,YAAY,GAAGlB,GAAG,CAACmB,UAAW;UACnC,IAAI,CAACC,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM,IAAI,IAAI,CAACnH,gBAAgB,KAAKmE,eAAe,CAACuB,SAAS,EAAE;MAC9D,OAAO,IAAI,CAACR,eAAe,CAACwB,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEJ,SAAS;UACpBK,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACb,mBAAmB;UACtCc,YAAY,EAAE,IAAI,CAAChH;;OAEtB,CAAC,CAAC8F,IAAI,CACLzJ,GAAG,CAAC0J,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACrG,MAAM,GAAGoG,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACgB,YAAY,GAAGlB,GAAG,CAACmB,UAAW;UACnC,IAAI,CAACC,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAACxH,MAAM,GAAG,EAAE;MAChB,IAAI,CAACsH,YAAY,GAAG,CAAC;MACrB,OAAO,IAAI,CAAC/B,eAAe,CAACwB,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,CAAC;UACXE,YAAY,EAAE,CAAC,CAAC;UAAE;UAClBC,YAAY,EAAE7C,eAAe,CAACoB;;OAEjC,CAAC,CAACO,IAAI,CACLzJ,GAAG,CAAC,MAAK;QACP,IAAI,CAACsD,MAAM,GAAG,EAAE;QAChB,IAAI,CAACsH,YAAY,GAAG,CAAC;MACvB,CAAC,CAAC,CACH;IACH;EACF;EAEAG,WAAWA,CAACZ,SAAiB;IAC3B;IACA,IAAI,CAACL,eAAe,CAACK,SAAS,CAAC,CAACJ,SAAS,EAAE;EAC7C;EACAiB,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAACpB,mBAAmB,GAAGoB,WAAW;IACtC,IAAI,CAACnB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAAEmB,eAAeA,CAAC9C,QAAyB;IACzC,IAAI,CAACzE,gBAAgB,GAAGyE,QAAQ;IAChC,IAAI,CAACe,kBAAkB,GAAG,IAAI,CAAC,CAAC;IAChC,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAAEhI,MAAMA,CAACoJ,GAAQ,EAAEC,IAA0D;IAC3E;IACA,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACjC,kBAAkB,EAAE;MACrC,IAAI,CAACJ,OAAO,CAACsC,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAACvE,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC6B,aAAa,CAAC2C,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAI,CAACtE,MAAM,GAAG,KAAK;IAEnB,IAAI,CAAC,CAACuE,IAAI,EAAE;MACV;MACA,IAAI,CAACvD,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC1C,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAC7B,MAAM,CAAC;MAC5C,IAAI,CAACsB,iBAAiB,GAAG,IAAI,CAACtB,MAAM,CAACiI,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAC9I,GAAG,KAAK0I,IAAI,CAAC1I,GAAG,CAAC;MAC3E,IAAI,IAAI,CAACkC,iBAAiB,KAAK,CAAC,CAAC,EAAE;QACjC,IAAI,CAACA,iBAAiB,GAAG,CAAC;MAC5B;MACA,IAAI,CAACW,mBAAmB,GAAG,IAAI,CAACkG,sBAAsB,EAAE;IAC1D,CAAC,MAAM;MACL;MACA,IAAI,CAAC5D,aAAa,GAAG,KAAK;MAC1B,IAAI,CAAC1C,oBAAoB,GAAG,EAAE;MAC9B,IAAI,CAACP,iBAAiB,GAAG,CAAC;MAC1B,IAAI,CAACW,mBAAmB,GAAG,EAAE;MAC7B,IAAI,CAACuB,YAAY,GAAG,EAAE;IACxB;EACF;EACA9D,aAAaA,CAACmI,GAAQ,EAAEC,IAA0D;IAChF;IACA,IAAI,CAAC,IAAI,CAACzH,gBAAgB,EAAE;MAC1B,IAAI,CAACoF,OAAO,CAACsC,YAAY,CAAC,UAAU,CAAC;MACrC;IACF;IAEA,IAAI,CAAC,CAACD,IAAI,IAAIA,IAAI,CAAC1I,GAAG,EAAE;MACtB,IAAI,CAACiG,aAAa,CAAC2C,IAAI,CAACH,GAAG,CAAC;MAC5B,IAAI,CAACtE,MAAM,GAAG,IAAI;MAClB,IAAI,CAAC6E,eAAe,GAAGN,IAAI,CAAC1I,GAAG;MAC/B,IAAI,CAACoE,YAAY,GAAG,EAAE;IACxB;EACF;EAEA6E,UAAUA,CAAC3G,KAAU;IACnB,IAAI,CAAC4D,KAAK,CAACwB,KAAK,EAAE;IAClB,MAAMwB,OAAO,GAAG,IAAIxC,GAAG,EAAE;IACzB,KAAK,MAAMgC,IAAI,IAAIpG,KAAK,EAAE;MACxB,IAAI4G,OAAO,CAACC,GAAG,CAACT,IAAI,CAACpF,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC4C,KAAK,CAACkD,eAAe,CAAC,QAAQ,CAAC;QACpC;MACF;MACAF,OAAO,CAACG,GAAG,CAACX,IAAI,CAACpF,IAAI,CAAC;IACxB;EACF;EAEAgG,QAAQA,CAACb,GAAQ,GACjB;EACA1E,WAAWA,CAACwF,KAAU;IACpB,KAAK,IAAIxH,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGwH,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC5I,MAAM,EAAEkB,KAAK,EAAE,EAAE;MAC9D,MAAM2H,IAAI,GAAGH,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC1H,KAAK,CAAC;MACtC,IAAI2H,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAACC,IAAI,KAAK,iBAAiB,IAAID,IAAI,CAACpG,IAAI,CAACsG,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC/E,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC;QAC3B,CAAC,MAAM;UACL,IAAI,CAACK,sBAAsB,CAACL,IAAI,CAAC;QACnC;MACF;IACF;IACA;IACAH,KAAK,CAACC,MAAM,CAAClL,KAAK,GAAG,IAAI;EAC3B;EAEAwL,cAAcA,CAACE,OAAa;IAC1B,MAAMC,GAAG,GAAG,IAAIvM,KAAK,EAAE;IAEvBuM,GAAG,CAACC,SAAS,CAACF,OAAO,CAAC,CAACG,IAAI,CAAEC,QAAQ,IAAI;MACvC,MAAMC,UAAU,GAAmB,EAAE;MAAED,QAAQ,CAACE,OAAO,CAAC,CAACC,YAAY,EAAEb,IAAI,KAAI;QAC7E;QACA,IAAI,CAACA,IAAI,CAACc,GAAG,IAAI,IAAI,CAACC,WAAW,CAACF,YAAY,CAAC,EAAE;UAC/CF,UAAU,CAACK,IAAI,CACbhB,IAAI,CAACiB,KAAK,CAAC,MAAM,CAAC,CAACR,IAAI,CAAES,IAAI,IAAI;YAC/B;YACA,MAAMC,QAAQ,GAAGN,YAAY,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIR,YAAY,CAACO,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,IAAIR,YAAY;YAChG;YACA,MAAMS,SAAS,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAEC,QAAQ,EAAE;cAAElB,IAAI,EAAE,IAAI,CAACuB,gBAAgB,CAACX,YAAY;YAAC,CAAE,CAAC;YAC3F,OAAO,IAAI,CAACY,uBAAuB,CAACH,SAAS,EAAEH,QAAQ,CAAC;UAC1D,CAAC,CAAC,CACH;QACH;MACF,CAAC,CAAC;MAEF;MACAO,OAAO,CAACC,GAAG,CAAChB,UAAU,CAAC,CAACF,IAAI,CAAC,MAAK;QAChC,IAAI,CAAC9D,OAAO,CAACiF,aAAa,CAAC,iBAAiBjB,UAAU,CAACxJ,MAAM,MAAM,CAAC;MACtE,CAAC,CAAC,CAAC0K,KAAK,CAAEC,KAAK,IAAI;QACjBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,IAAI,CAACnF,OAAO,CAACsC,YAAY,CAAC,oBAAoB,CAAC;MACjD,CAAC,CAAC;IAEJ,CAAC,CAAC,CAAC4C,KAAK,CAAEC,KAAK,IAAI;MACjBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACnF,OAAO,CAACsC,YAAY,CAAC,uBAAuB,CAAC;IACpD,CAAC,CAAC;EACJ;EAEAoB,sBAAsBA,CAACL,IAAU;IAC/B,IAAIgC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC7BD,MAAM,CAACE,aAAa,CAAClC,IAAI,CAAC;IAC1BgC,MAAM,CAACG,MAAM,GAAG,MAAK;MACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;MAC/C,IAAI,CAACD,SAAS,EAAE;QACd;MACF;MACA,IAAI,CAACE,cAAc,CAACtC,IAAI,EAAEoC,SAAS,CAAC;IACtC,CAAC;EACH;EAEAX,uBAAuBA,CAACzB,IAAU,EAAEuC,YAAoB;IACtD,OAAO,IAAIb,OAAO,CAAC,CAACc,OAAO,EAAEC,MAAM,KAAI;MACrC,IAAIT,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAAClC,IAAI,CAAC;MAC1BgC,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACdK,MAAM,CAAC,UAAU,CAAC;UAClB;QACF;QACA,IAAI,CAACH,cAAc,CAACtC,IAAI,EAAEoC,SAAS,EAAEG,YAAY,CAAC;QAClDC,OAAO,EAAE;MACX,CAAC;MACDR,MAAM,CAACU,OAAO,GAAG,MAAK;QACpBD,MAAM,CAAC,aAAa,CAAC;MACvB,CAAC;IACH,CAAC,CAAC;EACJ;EACAH,cAAcA,CAACtC,IAAU,EAAEoC,SAAiB,EAAEG,YAAqB;IACjE;IACA,IAAIpB,QAAQ,GAAGoB,YAAY,IAAIvC,IAAI,CAACpG,IAAI;IAExC;IACA,IAAI2I,YAAY,EAAE;MAChBpB,QAAQ,GAAGoB,YAAY,CAACnB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,IAAIkB,YAAY,CAACnB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE,IAAIkB,YAAY;IAC5F;IAEA,MAAMI,wBAAwB,GAAGxB,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvD;IACA,MAAMwB,iBAAiB,GAAG,IAAI,CAAClI,YAAY,CAACyE,SAAS,CAAC0D,OAAO,IAAIA,OAAO,CAACjJ,IAAI,KAAK+I,wBAAwB,CAAC;IAC3G,IAAIC,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAAClI,YAAY,CAACkI,iBAAiB,CAAC,GAAG;QACrC,GAAG,IAAI,CAAClI,YAAY,CAACkI,iBAAiB,CAAC;QACvC/I,IAAI,EAAEuI,SAAS;QACfxJ,KAAK,EAAEoH,IAAI;QACX8C,SAAS,EAAE,IAAI,CAAClG,eAAe,CAACmG,gBAAgB,CAAC5B,QAAQ;OAC1D;IACH,CAAC,MAAM;MACL;MACA,IAAI,CAACzG,YAAY,CAACsG,IAAI,CAAC;QACrBrH,EAAE,EAAE,IAAIqJ,IAAI,EAAE,CAACC,OAAO,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE;QACxCvJ,IAAI,EAAE+I,wBAAwB;QAC9B9I,IAAI,EAAEuI,SAAS;QACfU,SAAS,EAAE,IAAI,CAAClG,eAAe,CAACmG,gBAAgB,CAAC5B,QAAQ,CAAC;QAC1DvI,KAAK,EAAEoH;OACR,CAAC;IACJ;EACF;EAEAe,WAAWA,CAACI,QAAgB;IAC1B,MAAMiC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IAC1E,MAAMN,SAAS,GAAG3B,QAAQ,CAACjB,WAAW,EAAE,CAACmD,SAAS,CAAClC,QAAQ,CAACmC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,OAAOF,eAAe,CAACG,QAAQ,CAACT,SAAS,CAAC;EAC5C;EAEAtB,gBAAgBA,CAACL,QAAgB;IAC/B,MAAM2B,SAAS,GAAG3B,QAAQ,CAACjB,WAAW,EAAE,CAACmD,SAAS,CAAClC,QAAQ,CAACmC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC7E,QAAQR,SAAS;MACf,KAAK,MAAM;MACX,KAAK,OAAO;QACV,OAAO,YAAY;MACrB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,MAAM;QACT,OAAO,WAAW;MACpB,KAAK,OAAO;QACV,OAAO,YAAY;MACrB;QACE,OAAO,YAAY;IACvB;EACF;EAEApJ,WAAWA,CAAC8J,SAAiB;IAC3B,IAAI,CAAC9I,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC+I,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/J,EAAE,IAAI6J,SAAS,CAAC;EACtE;EACAzI,WAAWA,CAACgE,GAAQ;IAClB,IAAI,CAAC,IAAI,CAACtE,MAAM,EAAE;MAChB,MAAM7B,KAAK,GAAG,IAAI,CAAC8B,YAAY,CAACiJ,GAAG,CAACD,CAAC,IAAIA,CAAC,CAAC9K,KAAK,CAAC;MACjD,IAAI,CAAC2G,UAAU,CAAC3G,KAAK,CAAC;MACtB,IAAI,IAAI,CAAC4D,KAAK,CAACoH,aAAa,CAACzM,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACwF,OAAO,CAACkH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;QACpD;MACF,CAAC,CAAM;MACP,MAAME,aAAa,GAAG,IAAI,CAACrH,eAAe,CAACsH,oCAAoC,CAAC;QAC9E7F,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACb,mBAAmB;UACtCuG,KAAK,EAAE,IAAI,CAACzM,gBAAgB,KAAKmE,eAAe,CAACC,iBAAiB,GAAG,SAAS,GAAG,aAAa;UAC9F/C,KAAK,EAAEA,KAAK;UACZ2F,YAAY,EAAE,IAAI,CAAChH;;OAEtB,CAAC;MAAEuM,aAAa,CAACzG,IAAI,CACpBzJ,GAAG,CAAC0J,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACZ,OAAO,CAACiF,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAClH,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACiC,OAAO,CAACsC,YAAY,CAAC3B,GAAG,CAAC2G,OAAQ,CAAC;QACzC;QACAlF,GAAG,CAACzD,KAAK,EAAE;QACX,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,CAAC,EACF7H,SAAS,CAAE4J,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG/J,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACgK,SAAS,EAAE;IACf,CAAC,MACI;MACH,IAAI,IAAI,CAACjD,YAAY,CAACvD,MAAM,GAAG,CAAC,IAAI,IAAI,CAACuD,YAAY,CAAC,CAAC,CAAC,CAAC9B,KAAK,EAAE;QAC9D;QACA,MAAMsL,aAAa,GAAG,IAAI,CAACzH,eAAe,CAAC0H,gCAAgC,CAAC;UAC1EjG,IAAI,EAAE;YACJkG,YAAY,EAAE,IAAI,CAAC3G,mBAAmB;YACtC4G,UAAU,EAAE,IAAI,CAAC/E,eAAe;YAChC1G,KAAK,EAAE,IAAI,CAAC8B,YAAY,CAAC,CAAC,CAAC,CAAC9B;;SAE/B,CAAC;QAAEsL,aAAa,CAAC7G,IAAI,CACpBzJ,GAAG,CAAC0J,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACZ,OAAO,CAACiF,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAAClH,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACiC,OAAO,CAACsC,YAAY,CAAC3B,GAAG,CAAC2G,OAAQ,CAAC;UACzC;UACAlF,GAAG,CAACzD,KAAK,EAAE;UACX,IAAI,CAACC,iBAAiB,EAAE;QAC1B,CAAC,CAAC,EACF7H,SAAS,CAAE4J,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG/J,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACgK,SAAS,EAAE;MACf;IACF;EACF;EAEApE,UAAUA,CAACsG,KAAU,EAAExH,KAAa;IAClC,IAAI6I,IAAI,GAAG,IAAI,CAACxG,YAAY,CAACrC,KAAK,CAAC,CAACO,KAAK,CAAC0L,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5J,YAAY,CAACrC,KAAK,CAAC,CAACO,KAAK,CAACmD,IAAI,EAAE,IAAI,CAACrB,YAAY,CAACrC,KAAK,CAAC,CAACO,KAAK,CAACqH,IAAI,CAAC;IAC5H,IAAIsE,OAAO,GAAG,IAAIhD,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACC,MAAM,CAAClL,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC8F,YAAY,CAACrC,KAAK,CAAC,CAACyK,SAAS,EAAE,EAAE;MAAE7C,IAAI,EAAE,IAAI,CAACvF,YAAY,CAACrC,KAAK,CAAC,CAACO,KAAK,CAACqH;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAACvF,YAAY,CAACrC,KAAK,CAAC,CAACO,KAAK,GAAG2L,OAAO;EAC1C;EACA;EACAlO,gBAAgBA,CAACmO,MAAc;IAC7B,IAAI,IAAI,CAAC1I,aAAa,CAAC2D,GAAG,CAAC+E,MAAM,CAAC,EAAE;MAClC,IAAI,CAAC1I,aAAa,CAAC2I,MAAM,CAACD,MAAM,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAAC1I,aAAa,CAAC6D,GAAG,CAAC6E,MAAM,CAAC;IAChC;IACA,IAAI,CAAC9F,oBAAoB,EAAE;EAC7B;EAEA3I,eAAeA,CAAC2O,OAAgB;IAC9B,IAAI,CAAC1O,SAAS,GAAG0O,OAAO;IACxB,IAAIA,OAAO,EAAE;MACX;MACA,IAAI,CAACxN,MAAM,CAAC0J,OAAO,CAAC5B,IAAI,IAAG;QACzB,IAAIA,IAAI,CAAC1I,GAAG,EAAE;UACZ,IAAI,CAACwF,aAAa,CAAC6D,GAAG,CAACX,IAAI,CAAC1I,GAAG,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMqO,cAAc,GAAG,IAAI,CAACzN,MAAM,CAACyM,GAAG,CAAC3E,IAAI,IAAIA,IAAI,CAAC1I,GAAG,CAAC,CAACmN,MAAM,CAAC9J,EAAE,IAAIA,EAAE,KAAKiL,SAAS,CAAa;MACnGD,cAAc,CAAC/D,OAAO,CAACjH,EAAE,IAAI,IAAI,CAACmC,aAAa,CAAC2I,MAAM,CAAC9K,EAAE,CAAC,CAAC;IAC7D;EACF;EAEA+E,oBAAoBA,CAAA;IAClB,MAAMiG,cAAc,GAAG,IAAI,CAACzN,MAAM,CAACyM,GAAG,CAAC3E,IAAI,IAAIA,IAAI,CAAC1I,GAAG,CAAC,CAACmN,MAAM,CAAC9J,EAAE,IAAIA,EAAE,KAAKiL,SAAS,CAAa;IACnG,IAAI,CAAC5O,SAAS,GAAG2O,cAAc,CAACxN,MAAM,GAAG,CAAC,IAAIwN,cAAc,CAACE,KAAK,CAAClL,EAAE,IAAI,IAAI,CAACmC,aAAa,CAAC2D,GAAG,CAAC9F,EAAE,CAAC,CAAC;EACtG;EACA;EACAtE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACyG,aAAa,CAACC,IAAI,KAAK,CAAC,EAAE;MACjC,IAAI,CAACY,OAAO,CAACsC,YAAY,CAAC,WAAW,CAAC;MACtC;IACF;IAEA,MAAM6F,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAClJ,aAAa,CAAC;IAElD;IACA,IAAImJ,OAAO,CAAC,YAAYH,WAAW,CAAC3N,MAAM,QAAQ,CAAC,EAAE;MACnD,IAAI,CAACsF,eAAe,CAACyI,gCAAgC,CAAC;QACpDhH,IAAI,EAAE4G;OACP,CAAC,CAACzH,IAAI,CACLzJ,GAAG,CAAC0J,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACZ,OAAO,CAACiF,aAAa,CAAC,QAAQkD,WAAW,CAAC3N,MAAM,MAAM,CAAC;UAC5D;UACA,IAAI,CAAC2E,aAAa,CAACkC,KAAK,EAAE;UAC1B,IAAI,CAAChI,SAAS,GAAG,KAAK;QACxB,CAAC,MAAM;UACL,IAAI,CAAC2G,OAAO,CAACsC,YAAY,CAAC3B,GAAG,CAAC2G,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC,EACFvQ,SAAS,CAAE4J,GAAG,IAAKA,GAAG,CAACC,UAAU,KAAK,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,IAAI,CAACK,SAAS,CAAC,GAAGpK,EAAE,CAAC,IAAI,CAAC,CAAC,CAC3F,CAACgK,SAAS,EAAE;IACf;EACF;EACA;EACApH,cAAcA,CAACiO,MAAc;IAC3B,OAAO,IAAI,CAAC1I,aAAa,CAAC2D,GAAG,CAAC+E,MAAM,CAAC;EACvC;EACA;EACAnF,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACtG,oBAAoB,CAAC5B,MAAM,KAAK,CAAC,IAAI,IAAI,CAACqB,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACO,oBAAoB,CAAC5B,MAAM,EAAE;MACtI,OAAO,EAAE;IACX;IACA,MAAMgO,YAAY,GAAG,IAAI,CAACpM,oBAAoB,CAAC,IAAI,CAACP,iBAAiB,CAAC;IACtE;IACA,OAAO2M,YAAY,CAACxM,OAAO,IAAIwM,YAAY,CAACvM,KAAK,IAAI,EAAE;EACzD;EAEAd,aAAaA,CAAA;IACX,IAAI,IAAI,CAACiB,oBAAoB,CAAC5B,MAAM,KAAK,CAAC,EAAE;IAC5C,IAAI,CAACqB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACA,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACO,oBAAoB,CAAC5B,MAAM,GAAG,CAAC;IACvH,IAAI,CAACgC,mBAAmB,GAAG,IAAI,CAACkG,sBAAsB,EAAE;EAC1D;EACApH,SAASA,CAAA;IACP,IAAI,IAAI,CAACc,oBAAoB,CAAC5B,MAAM,KAAK,CAAC,EAAE;IAC5C,IAAI,CAACqB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACO,oBAAoB,CAAC5B,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqB,iBAAiB,GAAG,CAAC,GAAG,CAAC;IACvH,IAAI,CAACW,mBAAmB,GAAG,IAAI,CAACkG,sBAAsB,EAAE;EAC1D;EAEA/G,SAASA,CAACD,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACU,oBAAoB,CAAC5B,MAAM,EAAE;MAC1D,IAAI,CAACqB,iBAAiB,GAAGH,KAAK;MAC9B,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACkG,sBAAsB,EAAE;IAC1D;EACF;EAEA,IAAI1H,gBAAgBA,CAAA;IAClB,IAAI,IAAI,CAACoB,oBAAoB,CAAC5B,MAAM,KAAK,CAAC,IAAI,IAAI,CAACqB,iBAAiB,GAAG,CAAC,IAAI,IAAI,CAACA,iBAAiB,IAAI,IAAI,CAACO,oBAAoB,CAAC5B,MAAM,EAAE;MACtI,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAAC4B,oBAAoB,CAAC,IAAI,CAACP,iBAAiB,CAAC;EAC1D;EACA,IAAId,YAAYA,CAAA;IACd,IAAI,IAAI,CAACqB,oBAAoB,CAAC5B,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACrD,OAAO,GAAG,IAAI,CAACqB,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAACO,oBAAoB,CAAC5B,MAAM,EAAE;EAC9E;EACA;EACAoE,iBAAiBA,CAAA;IACf,IAAI,CAACE,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC1C,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACP,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACW,mBAAmB,GAAG,EAAE;EAC/B;EAEA;EAEAiM,mBAAmBA,CAACvF,KAAoB;IACtC,IAAI,CAAC,IAAI,CAACpE,aAAa,IAAI,IAAI,CAAC1C,oBAAoB,CAAC5B,MAAM,IAAI,CAAC,EAAE;IAElE,QAAQ0I,KAAK,CAACwF,GAAG;MACf,KAAK,WAAW;QACdxF,KAAK,CAACyF,cAAc,EAAE;QACtB,IAAI,CAACxN,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf+H,KAAK,CAACyF,cAAc,EAAE;QACtB,IAAI,CAACrN,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX4H,KAAK,CAACyF,cAAc,EAAE;QACtB;QACA;IACJ;EACF;;;uCAhhBWzJ,wBAAwB,EAAA5H,EAAA,CAAAsR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxR,EAAA,CAAAsR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1R,EAAA,CAAAsR,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA5R,EAAA,CAAAsR,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9R,EAAA,CAAAsR,iBAAA,CAAAO,EAAA,CAAAE,gBAAA,GAAA/R,EAAA,CAAAsR,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAjS,EAAA,CAAAsR,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxBvK,wBAAwB;MAAAwK,SAAA;MAAAC,YAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAxBvS,EAAA,CAAAa,UAAA,qBAAA4R,oDAAA7Q,MAAA;YAAA,OAAA4Q,GAAA,CAAArB,mBAAA,CAAAvP,MAAA,CAA2B;UAAA,UAAA5B,EAAA,CAAA0S,iBAAA,CAAH;;;;;;;;;;;UCnCnC1S,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAA8D,SAAA,qBAAiC;UACnC9D,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2LAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIlEH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,qBACiB;UADWD,EAAA,CAAA2S,gBAAA,2BAAAC,sEAAAhR,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA7S,EAAA,CAAA8S,kBAAA,CAAAN,GAAA,CAAAhJ,mBAAA,EAAA5H,MAAA,MAAA4Q,GAAA,CAAAhJ,mBAAA,GAAA5H,MAAA;YAAA,OAAA5B,EAAA,CAAAmB,WAAA,CAAAS,MAAA;UAAA,EAAiC;UAAC5B,EAAA,CAAAa,UAAA,4BAAAkS,uEAAAnR,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA,OAAA7S,EAAA,CAAAmB,WAAA,CAAkBqR,GAAA,CAAA7H,cAAA,CAAA/I,MAAA,CAAsB;UAAA,EAAC;UAErG5B,EAAA,CAAA4C,UAAA,KAAAoQ,8CAAA,wBAAgF;UAKtFhT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACd;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,qBACiB;UADaD,EAAA,CAAA2S,gBAAA,2BAAAM,sEAAArR,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA7S,EAAA,CAAA8S,kBAAA,CAAAN,GAAA,CAAAlP,gBAAA,EAAA1B,MAAA,MAAA4Q,GAAA,CAAAlP,gBAAA,GAAA1B,MAAA;YAAA,OAAA5B,EAAA,CAAAmB,WAAA,CAAAS,MAAA;UAAA,EAA8B;UAAC5B,EAAA,CAAAa,UAAA,4BAAAqS,uEAAAtR,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA,OAAA7S,EAAA,CAAAmB,WAAA,CAAkBqR,GAAA,CAAA3H,eAAA,CAAAjJ,MAAA,CAAuB;UAAA,EAAC;UAErG5B,EAAA,CAAA4C,UAAA,KAAAuQ,8CAAA,wBAAyE;UAK/EnT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAK7CD,EAJA,CAAA4C,UAAA,KAAAwQ,2CAAA,qBACsC,KAAAC,2CAAA,qBAGiC;UAI7ErT,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKAH,EAHN,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD;UACnDD,EAAA,CAAA4C,UAAA,KAAA0Q,uCAAA,iBAAkE;UAKlEtT,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAA8D,SAAA,cAAmC;UAEvC9D,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA4C,UAAA,KAAA2Q,uCAAA,mBAAgD;UAsBxDvT,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA2S,gBAAA,kCAAAa,kFAAA5R,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA7S,EAAA,CAAA8S,kBAAA,CAAAN,GAAA,CAAAjI,YAAA,EAAA3I,MAAA,MAAA4Q,GAAA,CAAAjI,YAAA,GAAA3I,MAAA;YAAA,OAAA5B,EAAA,CAAAmB,WAAA,CAAAS,MAAA;UAAA,EAAiC,4BAAA6R,4EAAA7R,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA7S,EAAA,CAAA8S,kBAAA,CAAAN,GAAA,CAAApI,QAAA,EAAAxI,MAAA,MAAA4Q,GAAA,CAAApI,QAAA,GAAAxI,MAAA;YAAA,OAAA5B,EAAA,CAAAmB,WAAA,CAAAS,MAAA;UAAA,EAAwB,wBAAA8R,wEAAA9R,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA7S,EAAA,CAAA8S,kBAAA,CAAAN,GAAA,CAAA1I,SAAA,EAAAlI,MAAA,MAAA4Q,GAAA,CAAA1I,SAAA,GAAAlI,MAAA;YAAA,OAAA5B,EAAA,CAAAmB,WAAA,CAAAS,MAAA;UAAA,EAAqB;UAC5F5B,EAAA,CAAAa,UAAA,wBAAA6S,wEAAA9R,MAAA;YAAA5B,EAAA,CAAAe,aAAA,CAAA8R,GAAA;YAAA,OAAA7S,EAAA,CAAAmB,WAAA,CAAcqR,GAAA,CAAA9H,WAAA,CAAA9I,MAAA,CAAmB;UAAA,EAAC;UAGxC5B,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA4C,UAAA,KAAA+Q,gDAAA,iCAAA3T,EAAA,CAAAkH,sBAAA,CAAkD;;;UA9EZlH,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAA4T,gBAAA,YAAApB,GAAA,CAAAhJ,mBAAA,CAAiC;UAE1BxJ,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAA5J,kBAAA,CAAqB;UAS1B5I,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAA4T,gBAAA,YAAApB,GAAA,CAAAlP,gBAAA,CAA8B;UAE5BtD,EAAA,CAAAO,SAAA,EAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAvK,eAAA,CAAkB;UAS/CjI,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAI,UAAA,SAAAoS,GAAA,CAAAxP,MAAA,IAAAwP,GAAA,CAAAvP,MAAA,CAAAC,MAAA,KAAiC;UAGmBlD,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAoS,GAAA,CAAAqB,QAAA,CAAc;UAUpC7T,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAI,UAAA,SAAAoS,GAAA,CAAAxP,MAAA,IAAAwP,GAAA,CAAAvP,MAAA,CAAAC,MAAA,KAAiC;UAa7ClD,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAoS,GAAA,CAAAvP,MAAA,CAAY;UAwBvBjD,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAA4T,gBAAA,mBAAApB,GAAA,CAAAjI,YAAA,CAAiC,aAAAiI,GAAA,CAAApI,QAAA,CAAwB,SAAAoI,GAAA,CAAA1I,SAAA,CAAqB;;;qBDrD9FtK,YAAY,EAAAsU,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZpU,YAAY,EAAAqU,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,mBAAA,EAAAhD,EAAA,CAAAiD,gBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAEZpV,eAAe;MAAAqV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}