{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i16 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_67_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r11 = i0.ɵɵreference(105);\n      return i0.ɵɵresetView(ctx_r9.openModel(dialogHouseholdMain_r11));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_99_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r15 = i0.ɵɵreference(103);\n      return i0.ɵɵresetView(ctx_r9.openModelDetail(dialogUpdateHousehold_r15, item_r14));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 50);\n    i0.ɵɵtemplate(18, HouseholdManagementComponent_tr_99_button_18_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementStart(19, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_19_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(20, \" \\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_21_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(22, \" \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_23_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r9.searchQuery.CBuildCaseSelected.cID, item_r14.CID));\n    });\n    i0.ɵɵtext(24, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_25_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.resetSecureKey(item_r14));\n    });\n    i0.ɵɵtext(26, \" \\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_99_Template_button_click_27_listener() {\n      const item_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      const dialogQuotation_r16 = i0.ɵɵreference(107);\n      return i0.ɵɵresetView(ctx_r9.openQuotation(dialogQuotation_r16, item_r14));\n    });\n    i0.ɵɵtext(28, \" \\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r14 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r14.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r14.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r14.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r14.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r14.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r14.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CSignStatus === 0 || item_r14.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r14.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r19);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r19.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 88);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CPayStatusSelected, $event) || (ctx_r9.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r24);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r24.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"label\", 90);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CProgressSelected, $event) || (ctx_r9.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 58)(1, \"div\", 59)(2, \"label\", 60);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CBuildCaseSelected, $event) || (ctx_r9.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"label\", 62);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CHousehold, $event) || (ctx_r9.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 59)(11, \"label\", 64);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CFloor, $event) || (ctx_r9.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"label\", 66);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CCustomerName, $event) || (ctx_r9.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 59)(19, \"label\", 68);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CNationalId, $event) || (ctx_r9.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"label\", 70);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CMail, $event) || (ctx_r9.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 59)(27, \"label\", 72);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CPhone, $event) || (ctx_r9.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 59)(31, \"label\", 74);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.detailSelected.CHouseTypeSelected, $event) || (ctx_r9.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template, 5, 2, \"div\", 76)(36, HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template, 5, 2, \"div\", 76);\n    i0.ɵɵelementStart(37, \"div\", 59)(38, \"label\", 77);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CIsChange, $event) || (ctx_r9.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 59)(43, \"label\", 79);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 78);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.CIsEnable, $event) || (ctx_r9.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 80)(48, \"label\", 81);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 82)(51, \"nb-form-field\", 83);\n    i0.ɵɵelement(52, \"nb-icon\", 84);\n    i0.ɵɵelementStart(53, \"input\", 85);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.changeStartDate, $event) || (ctx_r9.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 86, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 83);\n    i0.ɵɵelement(57, \"nb-icon\", 84);\n    i0.ɵɵelementStart(58, \"input\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseDetail.changeEndDate, $event) || (ctx_r9.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 86, 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r25 = i0.ɵɵreference(55);\n    const EndDate_r26 = i0.ɵɵreference(60);\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r9.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r9.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_102_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ref_r27 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onSubmitDetail(ref_r27));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 55);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 47)(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_102_Template_button_click_3_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r17).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r27));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_102_button_5_Template, 2, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_104_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_104_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ref_r30 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.addHouseHoldMain(ref_r30));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 58)(4, \"div\", 59)(5, \"label\", 93);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CBuildingName, $event) || (ctx_r9.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 59)(9, \"label\", 95);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CHouseHoldCount, $event) || (ctx_r9.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 59)(13, \"label\", 97);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.houseHoldMain.CFloor, $event) || (ctx_r9.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 47)(17, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_104_Template_button_click_17_listener() {\n      const ref_r30 = i0.ɵɵrestoreView(_r29).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r30));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_104_button_19_Template, 2, 0, \"button\", 100);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 127)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.addQuotationItem());\n    });\n    i0.ɵɵtext(2, \" + \\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.loadDefaultItems());\n    });\n    i0.ɵɵtext(5, \" \\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.loadRegularItems());\n    });\n    i0.ɵɵtext(7, \" \\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 129);\n    i0.ɵɵelement(1, \"i\", 130);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_23_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 138);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_tr_23_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const i_r37 = i0.ɵɵnextContext().index;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.removeQuotationItem(i_r37));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_23_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 139);\n    i0.ɵɵelement(1, \"i\", 140);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 131);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_2_listener($event) {\n      const item_r35 = i0.ɵɵrestoreView(_r34).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r35.cItemName, $event) || (item_r35.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 132);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_4_listener($event) {\n      const item_r35 = i0.ɵɵrestoreView(_r34).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r35.cUnitPrice, $event) || (item_r35.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 133);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_6_listener($event) {\n      const item_r35 = i0.ɵɵrestoreView(_r34).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r35.cCount, $event) || (item_r35.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_6_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 134);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 135);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, HouseholdManagementComponent_ng_template_106_tr_23_button_13_Template, 2, 0, \"button\", 136)(14, HouseholdManagementComponent_ng_template_106_tr_23_span_14_Template, 2, 0, \"span\", 137);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r35 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r9.isQuotationEditable || item_r35.CQuotationItemType === 1 || item_r35.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r35.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable || item_r35.CQuotationItemType === 1 || item_r35.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r35.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable || item_r35.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r35.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable || item_r35.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatCurrency(item_r35.cUnitPrice * item_r35.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r35.CQuotationItemType === 1)(\"badge-info\", item_r35.CQuotationItemType === 3)(\"badge-secondary\", item_r35.CQuotationItemType !== 1 && item_r35.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getQuotationTypeText(item_r35.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 141);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 142)(1, \"div\", 143)(2, \"label\", 144);\n    i0.ɵɵtext(3, \"\\u8CBB\\u7528\\u540D\\u7A31\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 145);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.additionalFeeName, $event) || (ctx_r9.additionalFeeName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.updateAdditionalFee());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 146)(6, \"label\", 144);\n    i0.ɵɵtext(7, \"\\u767E\\u5206\\u6BD4\\uFF08%\\uFF09\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 147);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r9.additionalFeePercentage, $event) || (ctx_r9.additionalFeePercentage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.updateAdditionalFee());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 148)(10, \"label\", 144);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 149);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.additionalFeeName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.additionalFeePercentage);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r9.additionalFeeName, \"\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.formatCurrency(ctx_r9.additionalFeeAmount));\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_button_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 150);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_button_42_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 151);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 152);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.lockQuotation(ref_r40));\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_button_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_button_47_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.saveQuotation(ref_r40));\n    });\n    i0.ɵɵtext(1, \" \\u5132\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 102)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\");\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_106_div_4_Template, 8, 0, \"div\", 103)(5, HouseholdManagementComponent_ng_template_106_div_5_Template, 5, 0, \"div\", 104);\n    i0.ɵɵelementStart(6, \"div\", 105)(7, \"table\", 106)(8, \"thead\")(9, \"tr\")(10, \"th\", 107);\n    i0.ɵɵtext(11, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 108);\n    i0.ɵɵtext(13, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 109);\n    i0.ɵɵtext(15, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 108);\n    i0.ɵɵtext(17, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 109);\n    i0.ɵɵtext(19, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"th\", 109);\n    i0.ɵɵtext(21, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"tbody\");\n    i0.ɵɵtemplate(23, HouseholdManagementComponent_ng_template_106_tr_23_Template, 15, 18, \"tr\", 46)(24, HouseholdManagementComponent_ng_template_106_tr_24_Template, 3, 0, \"tr\", 110);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 111)(26, \"div\", 112)(27, \"h5\", 113);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 114)(30, \"div\", 115)(31, \"nb-checkbox\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_Template_nb_checkbox_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r9 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r9.enableAdditionalFee, $event) || (ctx_r9.enableAdditionalFee = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_106_Template_nb_checkbox_ngModelChange_31_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.updateAdditionalFee());\n    });\n    i0.ɵɵtext(32, \" \\u555F\\u7528\\u984D\\u5916\\u8CBB\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, HouseholdManagementComponent_ng_template_106_div_33_Template, 14, 6, \"div\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 118)(35, \"h4\", 119);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"nb-card-footer\", 120)(38, \"div\")(39, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.printQuotation());\n    });\n    i0.ɵɵelement(40, \"i\", 122);\n    i0.ɵɵtext(41, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, HouseholdManagementComponent_ng_template_106_button_42_Template, 3, 0, \"button\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\")(44, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_106_Template_button_click_44_listener() {\n      const ref_r40 = i0.ɵɵrestoreView(_r32).dialogRef;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onClose(ref_r40));\n    });\n    i0.ɵɵtext(45, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(46, HouseholdManagementComponent_ng_template_106_button_46_Template, 2, 1, \"button\", 125)(47, HouseholdManagementComponent_ng_template_106_button_47_Template, 2, 1, \"button\", 126);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r9.currentHouse == null ? null : ctx_r9.currentHouse.CHouseHold, \" (\", ctx_r9.currentHouse == null ? null : ctx_r9.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.quotationItems.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u5C0F\\u8A08: \", ctx_r9.formatCurrency(ctx_r9.totalAmount), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r9.enableAdditionalFee);\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.enableAdditionalFee);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u7E3D\\u91D1\\u984D: \", ctx_r9.formatCurrency(ctx_r9.finalTotalAmount), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r9.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isQuotationEditable);\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.quotationStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n      quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    // 報價單相關\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    // 新增：百分比費用設定\n    this.additionalFeeName = '營業稅'; // 預設名稱\n    this.additionalFeePercentage = 5; // 預設5%\n    this.additionalFeeAmount = 0; // 百分比費用金額\n    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n    this.enableAdditionalFee = false; // 是否啟用百分比費用\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.isQuotationEditable = true; // 報價單是否可編輯\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    // if (this.searchQuery.CBuildingNameSelected) {\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\n    // }\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      _this.isQuotationEditable = true; // 預設可編輯\n      // 重置百分比費用設定\n      _this.additionalFeeName = '營業稅';\n      _this.additionalFeePercentage = 5;\n      _this.additionalFeeAmount = 0;\n      _this.finalTotalAmount = 0;\n      _this.enableAdditionalFee = false;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationID || 0;\n          // 根據 cQuotationStatus 決定是否可編輯\n          if (response.Entries.CQuotationStatus === 2) {\n            // 2: 已報價\n            _this.isQuotationEditable = false;\n          } else {\n            _this.isQuotationEditable = true;\n          }\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n              cQuotationStatus: entry.CQuotationStatus\n            }));\n            _this.calculateTotal();\n          } else {}\n        } else {}\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 產生新報價單\n  createNewQuotation() {\n    this.currentQuotationId = 0;\n    this.quotationItems = [];\n    this.isQuotationEditable = true;\n    this.totalAmount = 0;\n    this.finalTotalAmount = 0;\n    this.additionalFeeAmount = 0;\n    this.enableAdditionalFee = false;\n    // 顯示成功訊息\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      CQuotationItemType: CQuotationItemType.自定義\n    });\n  }\n  // 載入客變需求\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.客變需求 // 客變需求\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入客變需求成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n        }\n      } catch (error) {\n        console.error('載入客變需求錯誤:', error);\n        _this2.message.showErrorMSG('載入客變需求失敗');\n      }\n    })();\n  }\n  // 載入選樣資料\n  loadRegularItems() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.currentHouse?.CID) {\n          _this3.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this3.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this3.currentHouse.CID\n        };\n        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n        if (response?.success && response.data) {\n          const regularItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this3.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.選樣 // 選樣資料\n          }));\n          _this3.quotationItems.push(...regularItems);\n          _this3.calculateTotal();\n          _this3.message.showSucessMSG('載入選樣資料成功');\n        } else {\n          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n        }\n      } catch (error) {\n        console.error('載入選樣資料錯誤:', error);\n        _this3.message.showErrorMSG('載入選樣資料失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n    this.calculateFinalTotal();\n  }\n  // 計算百分比費用和最終總金額\n  calculateFinalTotal() {\n    if (this.enableAdditionalFee) {\n      this.additionalFeeAmount = Math.round(this.totalAmount * (this.additionalFeePercentage / 100));\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n    } else {\n      this.additionalFeeAmount = 0;\n      this.finalTotalAmount = this.totalAmount;\n    }\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 更新百分比費用\n  updateAdditionalFee() {\n    this.calculateFinalTotal();\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.quotationItems.length === 0) {\n        _this4.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位 (調整：允許單價和數量為負數)\n      const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this4.currentHouse.CID,\n          items: _this4.quotationItems,\n          quotationId: _this4.currentQuotationId // 傳遞當前的報價單ID\n        };\n        const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this4.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this4.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this5.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  // 列印報價單\n  printQuotation() {\n    if (this.quotationItems.length === 0) {\n      this.message.showErrorMSG('沒有可列印的報價項目');\n      return;\n    }\n    try {\n      // 建立列印內容\n      const printContent = this.generatePrintContent();\n      // 建立新的視窗進行列印\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n      if (printWindow) {\n        printWindow.document.open();\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        // 等待內容載入完成後列印\n        printWindow.onload = function () {\n          setTimeout(() => {\n            printWindow.print();\n            // 列印後不自動關閉視窗，讓使用者可以預覽\n          }, 500);\n        };\n      } else {\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n      }\n    } catch (error) {\n      console.error('列印報價單錯誤:', error);\n      this.message.showErrorMSG('列印報價單時發生錯誤');\n    }\n  }\n  // 產生列印內容\n  generatePrintContent() {\n    const currentDate = new Date().toLocaleDateString('zh-TW');\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n    let itemsHtml = '';\n    this.quotationItems.forEach((item, index) => {\n      const subtotal = item.cUnitPrice * item.cCount;\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n      itemsHtml += `\n        <tr>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${index + 1}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px;\">${item.cItemName}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(item.cUnitPrice)}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${item.cCount}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(subtotal)}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${quotationType}</td>\n        </tr>\n      `;\n    });\n    return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>報價單列印</title>\n        <style>\n          body {\n            font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;\n            margin: 20px;\n            font-size: 14px;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 30px;\n          }\n          .header h1 {\n            margin: 0;\n            font-size: 24px;\n            color: #333;\n          }\n          .info-section {\n            margin-bottom: 20px;\n            border-bottom: 1px solid #ddd;\n            padding-bottom: 15px;\n          }\n          .info-row {\n            display: flex;\n            margin-bottom: 8px;\n          }\n          .info-label {\n            font-weight: bold;\n            width: 100px;\n            flex-shrink: 0;\n          }\n          .info-value {\n            flex: 1;\n          }\n          table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n          }\n          th {\n            background-color: #27ae60;\n            color: white;\n            border: 1px solid #ddd;\n            padding: 10px 8px;\n            text-align: center;\n            font-weight: bold;\n          }\n          td {\n            border: 1px solid #ddd;\n            padding: 8px;\n          }\n          .total-section {\n            text-align: right;\n            margin-top: 20px;\n            padding-top: 15px;\n            border-top: 2px solid #27ae60;\n          }\n          .subtotal {\n            font-size: 14px;\n            margin-bottom: 5px;\n            color: #666;\n          }\n          .additional-fee {\n            font-size: 14px;\n            margin-bottom: 10px;\n            color: #666;\n          }\n          .total-amount {\n            font-size: 18px;\n            font-weight: bold;\n            color: #27ae60;\n            border-top: 1px solid #ddd;\n            padding-top: 10px;\n          }\n          .footer {\n            margin-top: 40px;\n            text-align: center;\n            font-size: 12px;\n            color: #666;\n          }\n          .signature-section {\n            margin-top: 40px;\n            page-break-inside: avoid;\n          }\n          .signature-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 30px;\n          }\n          .signature-box {\n            width: 45%;\n            text-align: center;\n          }\n          .signature-label {\n            font-weight: bold;\n            margin-bottom: 40px;\n            font-size: 16px;\n          }\n          .signature-line {\n            border-bottom: 2px solid #000;\n            height: 60px;\n            margin-bottom: 10px;\n            position: relative;\n          }\n          .signature-date {\n            font-size: 14px;\n            margin-top: 15px;\n          }\n          .signature-notes {\n            margin-top: 30px;\n            padding: 15px;\n            background-color: #f9f9f9;\n            border-left: 4px solid #27ae60;\n          }\n          .signature-notes p {\n            margin: 0 0 10px 0;\n            font-weight: bold;\n          }\n          .signature-notes ul {\n            margin: 0;\n            padding-left: 20px;\n          }\n          .signature-notes li {\n            margin-bottom: 5px;\n            line-height: 1.4;\n          }\n          @media print {\n            body { margin: 0; }\n            .header { page-break-inside: avoid; }\n            .signature-section { page-break-inside: avoid; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>報價單</h1>\n        </div>\n\n        <div class=\"info-section\">\n          <div class=\"info-row\">\n            <span class=\"info-label\">建案名稱：</span>\n            <span class=\"info-value\">${buildCaseName}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">戶別：</span>\n            <span class=\"info-value\">${this.currentHouse?.CHouseHold || ''}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">樓層：</span>\n            <span class=\"info-value\">${this.currentHouse?.CFloor || ''}樓</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">客戶姓名：</span>\n            <span class=\"info-value\">${this.currentHouse?.CCustomerName || ''}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">列印日期：</span>\n            <span class=\"info-value\">${currentDate}</span>\n          </div>\n        </div>\n\n        <table>\n          <thead>\n            <tr>\n              <th width=\"8%\">序號</th>\n              <th width=\"35%\">項目名稱</th>\n              <th width=\"15%\">單價 (元)</th>\n              <th width=\"10%\">數量</th>\n              <th width=\"20%\">小計 (元)</th>\n              <th width=\"12%\">類型</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${itemsHtml}\n          </tbody>\n        </table>\n\n        <div class=\"total-section\">\n          <div class=\"subtotal\">\n            小計：${this.formatCurrency(this.totalAmount)}\n          </div>\n          ${this.enableAdditionalFee ? `\n          <div class=\"additional-fee\">\n            ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n          </div>\n          ` : ''}\n          <div class=\"total-amount\">\n            總金額：${this.formatCurrency(this.finalTotalAmount)}\n          </div>\n        </div>\n\n        <div class=\"signature-section\">\n          <div class=\"signature-row\">\n            <div class=\"signature-box\">\n              <div class=\"signature-label\">客戶簽名：</div>\n              <div class=\"signature-line\"></div>\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\n            </div>\n            <div class=\"signature-box\">\n              <div class=\"signature-label\">業務簽名：</div>\n              <div class=\"signature-line\"></div>\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\n            </div>\n          </div>\n          <div class=\"signature-notes\">\n            <p><strong>注意事項：</strong></p>\n            <ul>\n              <li>此報價單有效期限為30天，逾期需重新報價</li>\n              <li>報價內容若有異動，請重新確認</li>\n              <li>簽名確認後即視為同意此報價內容</li>\n            </ul>\n          </div>\n        </div>\n\n        <div class=\"footer\">\n          此報價單由系統自動產生，列印時間：${new Date().toLocaleString('zh-TW')}\n        </div>\n      </body>\n      </html>\n    `;\n  }\n  // 鎖定報價單\n  lockQuotation(ref) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.quotationItems.length === 0) {\n        _this6.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      if (!_this6.currentQuotationId) {\n        _this6.message.showErrorMSG('無效的報價單ID');\n        return;\n      }\n      try {\n        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n        if (response.success) {\n          _this6.message.showSucessMSG('報價單已成功鎖定');\n          console.log('報價單鎖定成功:', {\n            quotationId: _this6.currentQuotationId,\n            message: response.message\n          });\n        } else {\n          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n          console.error('報價單鎖定失敗:', response.message);\n        }\n        ref.close();\n      } catch (error) {\n        _this6.message.showErrorMSG('報價單鎖定失敗');\n        console.error('鎖定報價單錯誤:', error);\n      }\n    })();\n  }\n  // 取得報價類型文字\n  getQuotationTypeText(quotationType) {\n    switch (quotationType) {\n      case CQuotationItemType.客變需求:\n        return '客變需求';\n      case CQuotationItemType.自定義:\n        return '自定義';\n      case CQuotationItemType.選樣:\n        return '選樣';\n      default:\n        return '未知';\n    }\n  }\n  getQuotationStatusText(status) {\n    switch (status) {\n      case EnumQuotationStatus.待報價:\n        return '待報價';\n      case EnumQuotationStatus.已報價:\n        return '已報價';\n      case EnumQuotationStatus.已簽回:\n        return '已簽回';\n      default:\n        return '未知';\n    }\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 108,\n      vars: 21,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"width\", \"30%\"], [\"width\", \"20%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"text-right\", \"mt-4\"], [1, \"mb-3\"], [1, \"text-secondary\"], [1, \"mb-3\", \"p-3\", \"border\", \"rounded\", \"bg-light\"], [1, \"mb-2\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"row align-items-center\", 4, \"ngIf\"], [1, \"border-top\", \"pt-2\"], [1, \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"6\", 1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-4\"], [1, \"form-label\", \"mb-1\"], [\"type\", \"text\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u71DF\\u696D\\u7A05\\u3001\\u670D\\u52D9\\u8CBB\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"col-md-3\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"100\", \"step\", \"0.1\", 1, \"form-control\", \"form-control-sm\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"col-md-5\"], [1, \"text-info\", \"fw-bold\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"label\", 14);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 16)(21, \"label\", 17);\n          i0.ɵɵtext(22, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-form-field\", 18)(24, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"label\", 20);\n          i0.ɵɵtext(26, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 21)(28, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10)(32, \"label\", 23);\n          i0.ɵɵtext(33, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10)(38, \"label\", 25);\n          i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 26);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 10)(44, \"label\", 27);\n          i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10)(50, \"label\", 29);\n          i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-select\", 30);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 10)(56, \"label\", 31);\n          i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nb-select\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 33)(62, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_62_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(63, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(64, \"i\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 36)(66, \"div\", 33);\n          i0.ɵɵtemplate(67, HouseholdManagementComponent_button_67_Template, 2, 0, \"button\", 37);\n          i0.ɵɵelementStart(68, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_68_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵtext(69, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_70_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵtext(71, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"input\", 39, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_72_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_74_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵtext(75, \" \\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(76, \"div\", 41)(77, \"table\", 42)(78, \"thead\")(79, \"tr\", 43)(80, \"th\", 44);\n          i0.ɵɵtext(81, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"th\", 44);\n          i0.ɵɵtext(83, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"th\", 44);\n          i0.ɵɵtext(85, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"th\", 44);\n          i0.ɵɵtext(87, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\", 44);\n          i0.ɵɵtext(89, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\", 44);\n          i0.ɵɵtext(91, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\", 44);\n          i0.ɵɵtext(93, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"th\", 44);\n          i0.ɵɵtext(95, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"th\", 45);\n          i0.ɵɵtext(97, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(98, \"tbody\");\n          i0.ɵɵtemplate(99, HouseholdManagementComponent_tr_99_Template, 29, 12, \"tr\", 46);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(100, \"nb-card-footer\", 47)(101, \"ngb-pagination\", 48);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(102, HouseholdManagementComponent_ng_template_102_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(104, HouseholdManagementComponent_ng_template_104_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(106, HouseholdManagementComponent_ng_template_106_Template, 48, 15, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i12.NgForOf, i12.NgIf, SharedModule, i13.DefaultValueAccessor, i13.NumberValueAccessor, i13.NgControlStatus, i13.MaxLengthValidator, i13.MinValidator, i13.MaxValidator, i13.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i14.NgbPagination, i15.BreadcrumbComponent, i16.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJob3VzZWhvbGQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvaG91c2Vob2xkLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9MQUFvTCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "CQuotationItemType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "ɵɵlistener", "HouseholdManagementComponent_button_67_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r9", "ɵɵnextContext", "dialogHouseholdMain_r11", "ɵɵreference", "ɵɵresetView", "openModel", "HouseholdManagementComponent_tr_99_button_18_Template_button_click_0_listener", "_r13", "item_r14", "$implicit", "dialogUpdateHousehold_r15", "openModelDetail", "ɵɵtemplate", "HouseholdManagementComponent_tr_99_button_18_Template", "HouseholdManagementComponent_tr_99_Template_button_click_19_listener", "_r12", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_99_Template_button_click_21_listener", "HouseholdManagementComponent_tr_99_Template_button_click_23_listener", "HouseholdManagementComponent_tr_99_Template_button_click_25_listener", "resetSecureKey", "HouseholdManagementComponent_tr_99_Template_button_click_27_listener", "dialogQuotation_r16", "openQuotation", "ɵɵtextInterpolate", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "CIsEnable", "isUpdate", "status_r19", "status_r20", "status_r22", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "$event", "_r21", "ɵɵtwoWayBindingSet", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_nb_option_4_Template", "ɵɵtwoWayProperty", "options", "payStatusOptions", "status_r24", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r23", "CProgressSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r18", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "ɵɵelement", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r25", "EndDate_r26", "HouseholdManagementComponent_ng_template_102_button_5_Template_button_click_0_listener", "_r28", "ref_r27", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_102_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_102_Template_button_click_3_listener", "_r17", "onClose", "HouseholdManagementComponent_ng_template_102_button_5_Template", "isCreate", "HouseholdManagementComponent_ng_template_104_button_19_Template_button_click_0_listener", "_r31", "ref_r30", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_7_listener", "_r29", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_104_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_104_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_104_button_19_Template", "HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_1_listener", "_r33", "addQuotationItem", "HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_4_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_106_div_4_Template_button_click_6_listener", "loadRegularItems", "HouseholdManagementComponent_ng_template_106_tr_23_button_13_Template_button_click_0_listener", "_r36", "i_r37", "index", "removeQuotationItem", "HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_2_listener", "item_r35", "_r34", "cItemName", "HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_4_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_106_tr_23_Template_input_ngModelChange_6_listener", "cCount", "HouseholdManagementComponent_ng_template_106_tr_23_button_13_Template", "HouseholdManagementComponent_ng_template_106_tr_23_span_14_Template", "ɵɵclassProp", "isQuotationEditable", "formatCurrency", "getQuotationTypeText", "HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_4_listener", "_r38", "additionalFeeName", "updateAdditionalFee", "HouseholdManagementComponent_ng_template_106_div_33_Template_input_ngModelChange_8_listener", "additionalFeePercentage", "additionalFeeAmount", "HouseholdManagementComponent_ng_template_106_button_42_Template_button_click_0_listener", "_r39", "createNewQuotation", "HouseholdManagementComponent_ng_template_106_button_46_Template_button_click_0_listener", "_r41", "ref_r40", "lockQuotation", "quotationItems", "length", "HouseholdManagementComponent_ng_template_106_button_47_Template_button_click_0_listener", "_r42", "saveQuotation", "HouseholdManagementComponent_ng_template_106_div_4_Template", "HouseholdManagementComponent_ng_template_106_div_5_Template", "HouseholdManagementComponent_ng_template_106_tr_23_Template", "HouseholdManagementComponent_ng_template_106_tr_24_Template", "HouseholdManagementComponent_ng_template_106_Template_nb_checkbox_ngModelChange_31_listener", "_r32", "enableAdditionalFee", "HouseholdManagementComponent_ng_template_106_div_33_Template", "HouseholdManagementComponent_ng_template_106_Template_button_click_39_listener", "printQuotation", "HouseholdManagementComponent_ng_template_106_button_42_Template", "HouseholdManagementComponent_ng_template_106_Template_button_click_44_listener", "HouseholdManagementComponent_ng_template_106_button_46_Template", "HouseholdManagementComponent_ng_template_106_button_47_Template", "currentHouse", "totalAmount", "finalTotalAmount", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "quotationStatusOptions", "getEnumOptions", "initDetail", "CHouseID", "CNationalID", "CProgress", "currentQuotationId", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "x", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "CBuildingNameSelected", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationID", "CQuotationStatus", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CItemName", "CUnitPrice", "CCount", "cStatus", "自定義", "cQuotationStatus", "error", "console", "context", "closeOnBackdropClick", "push", "_this2", "request", "success", "data", "defaultItems", "客變需求", "_this3", "regularItems", "選樣", "splice", "reduce", "sum", "calculateFinalTotal", "Math", "round", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "_this4", "invalidItems", "filter", "trim", "items", "quotationId", "exportQuotation", "_this5", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printContent", "generatePrintContent", "printWindow", "write", "onload", "print", "currentDate", "toLocaleDateString", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "toLocaleString", "_this6", "log", "getQuotationStatusText", "status", "待報價", "已報價", "已簽回", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "HouseService", "HouseHoldMainService", "BuildCaseService", "i7", "PetternHelper", "i8", "Router", "i9", "EventService", "i10", "UtilityService", "i11", "QuotationService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_button_click_62_listener", "HouseholdManagementComponent_button_67_Template", "HouseholdManagementComponent_Template_button_click_68_listener", "HouseholdManagementComponent_Template_button_click_70_listener", "HouseholdManagementComponent_Template_input_change_72_listener", "HouseholdManagementComponent_Template_button_click_74_listener", "HouseholdManagementComponent_tr_99_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_101_listener", "HouseholdManagementComponent_ng_template_102_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_104_Template", "HouseholdManagementComponent_ng_template_106_Template", "i12", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i13", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i14", "NgbPagination", "i15", "BreadcrumbComponent", "i16", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 預設名稱\r\n  additionalFeePercentage: number = 5;   // 預設5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = false;  // 是否啟用百分比費用\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n\r\n    // if (this.searchQuery.CBuildingNameSelected) {\r\n    //   this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value\r\n    // }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = false;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationID || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = false;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義\r\n    });\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求 // 客變需求\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣 // 選樣資料\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額\r\n  calculateFinalTotal() {\r\n    if (this.enableAdditionalFee) {\r\n      this.additionalFeeAmount = Math.round(this.totalAmount * (this.additionalFeePercentage / 100));\r\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n    } else {\r\n      this.additionalFeeAmount = 0;\r\n      this.finalTotalAmount = this.totalAmount;\r\n    }\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 更新百分比費用\r\n  updateAdditionalFee() {\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId // 傳遞當前的報價單ID\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      itemsHtml += `\r\n        <tr>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${index + 1}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px;\">${item.cItemName}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${item.cCount}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(subtotal)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${quotationType}</td>\r\n        </tr>\r\n      `;\r\n    });\r\n\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <title>報價單列印</title>\r\n        <style>\r\n          body {\r\n            font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;\r\n            margin: 20px;\r\n            font-size: 14px;\r\n          }\r\n          .header {\r\n            text-align: center;\r\n            margin-bottom: 30px;\r\n          }\r\n          .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            color: #333;\r\n          }\r\n          .info-section {\r\n            margin-bottom: 20px;\r\n            border-bottom: 1px solid #ddd;\r\n            padding-bottom: 15px;\r\n          }\r\n          .info-row {\r\n            display: flex;\r\n            margin-bottom: 8px;\r\n          }\r\n          .info-label {\r\n            font-weight: bold;\r\n            width: 100px;\r\n            flex-shrink: 0;\r\n          }\r\n          .info-value {\r\n            flex: 1;\r\n          }\r\n          table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-bottom: 20px;\r\n          }\r\n          th {\r\n            background-color: #27ae60;\r\n            color: white;\r\n            border: 1px solid #ddd;\r\n            padding: 10px 8px;\r\n            text-align: center;\r\n            font-weight: bold;\r\n          }\r\n          td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n          .total-section {\r\n            text-align: right;\r\n            margin-top: 20px;\r\n            padding-top: 15px;\r\n            border-top: 2px solid #27ae60;\r\n          }\r\n          .subtotal {\r\n            font-size: 14px;\r\n            margin-bottom: 5px;\r\n            color: #666;\r\n          }\r\n          .additional-fee {\r\n            font-size: 14px;\r\n            margin-bottom: 10px;\r\n            color: #666;\r\n          }\r\n          .total-amount {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #27ae60;\r\n            border-top: 1px solid #ddd;\r\n            padding-top: 10px;\r\n          }\r\n          .footer {\r\n            margin-top: 40px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #666;\r\n          }\r\n          .signature-section {\r\n            margin-top: 40px;\r\n            page-break-inside: avoid;\r\n          }\r\n          .signature-row {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            margin-bottom: 30px;\r\n          }\r\n          .signature-box {\r\n            width: 45%;\r\n            text-align: center;\r\n          }\r\n          .signature-label {\r\n            font-weight: bold;\r\n            margin-bottom: 40px;\r\n            font-size: 16px;\r\n          }\r\n          .signature-line {\r\n            border-bottom: 2px solid #000;\r\n            height: 60px;\r\n            margin-bottom: 10px;\r\n            position: relative;\r\n          }\r\n          .signature-date {\r\n            font-size: 14px;\r\n            margin-top: 15px;\r\n          }\r\n          .signature-notes {\r\n            margin-top: 30px;\r\n            padding: 15px;\r\n            background-color: #f9f9f9;\r\n            border-left: 4px solid #27ae60;\r\n          }\r\n          .signature-notes p {\r\n            margin: 0 0 10px 0;\r\n            font-weight: bold;\r\n          }\r\n          .signature-notes ul {\r\n            margin: 0;\r\n            padding-left: 20px;\r\n          }\r\n          .signature-notes li {\r\n            margin-bottom: 5px;\r\n            line-height: 1.4;\r\n          }\r\n          @media print {\r\n            body { margin: 0; }\r\n            .header { page-break-inside: avoid; }\r\n            .signature-section { page-break-inside: avoid; }\r\n          }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"header\">\r\n          <h1>報價單</h1>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">建案名稱：</span>\r\n            <span class=\"info-value\">${buildCaseName}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">戶別：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CHouseHold || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">樓層：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CFloor || ''}樓</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">客戶姓名：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CCustomerName || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">列印日期：</span>\r\n            <span class=\"info-value\">${currentDate}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th width=\"8%\">序號</th>\r\n              <th width=\"35%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"10%\">數量</th>\r\n              <th width=\"20%\">小計 (元)</th>\r\n              <th width=\"12%\">類型</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            ${itemsHtml}\r\n          </tbody>\r\n        </table>\r\n\r\n        <div class=\"total-section\">\r\n          <div class=\"subtotal\">\r\n            小計：${this.formatCurrency(this.totalAmount)}\r\n          </div>\r\n          ${this.enableAdditionalFee ? `\r\n          <div class=\"additional-fee\">\r\n            ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n          </div>\r\n          ` : ''}\r\n          <div class=\"total-amount\">\r\n            總金額：${this.formatCurrency(this.finalTotalAmount)}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"signature-section\">\r\n          <div class=\"signature-row\">\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">客戶簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">業務簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"signature-notes\">\r\n            <p><strong>注意事項：</strong></p>\r\n            <ul>\r\n              <li>此報價單有效期限為30天，逾期需重新報價</li>\r\n              <li>報價內容若有異動，請重新確認</li>\r\n              <li>簽名確認後即視為同意此報價內容</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"footer\">\r\n          此報價單由系統自動產生，列印時間：${new Date().toLocaleString('zh-TW')}\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1200px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 只有報價單可編輯時才顯示操作按鈕 -->\r\n      <div *ngIf=\"isQuotationEditable\" class=\"mb-4 d-flex justify-content-between\">\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"addQuotationItem()\">\r\n          + 新增自定義項目\r\n        </button>\r\n        <div>\r\n          <button class=\"btn btn-secondary btn-sm me-2\" (click)=\"loadDefaultItems()\">\r\n            載入客變需求\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"loadRegularItems()\">\r\n            載入選樣資料\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 報價單已鎖定時的提示 -->\r\n      <div *ngIf=\"!isQuotationEditable\" class=\"mb-4 alert alert-warning\">\r\n        <i class=\"fas fa-lock me-2\"></i>\r\n        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-bordered\">\r\n          <thead>\r\n            <tr>\r\n              <th width=\"30%\">項目名稱</th>\r\n              <th width=\"20%\">單價 (元)</th>\r\n              <th width=\"10%\">數量</th>\r\n              <th width=\"20%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.CQuotationItemType === 1\"\r\n                  [class.badge-info]=\"item.CQuotationItemType === 3\"\r\n                  [class.badge-secondary]=\"item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3\">\r\n                  {{ getQuotationTypeText(item.CQuotationItemType) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button *ngIf=\"isQuotationEditable\" class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  刪除\r\n                </button>\r\n                <span *ngIf=\"!isQuotationEditable\" class=\"text-muted\">\r\n                  <i class=\"fas fa-lock\"></i>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"6\" class=\"text-center text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <div class=\"text-right mt-4\">\r\n        <div class=\"mb-3\">\r\n          <h5 class=\"text-secondary\">小計: {{ formatCurrency(totalAmount) }}</h5>\r\n        </div>\r\n\r\n        <!-- 百分比費用設定 -->\r\n        <div class=\"mb-3 p-3 border rounded bg-light\">\r\n          <div class=\"mb-2\">\r\n            <nb-checkbox [(ngModel)]=\"enableAdditionalFee\" (ngModelChange)=\"updateAdditionalFee()\"\r\n              [disabled]=\"!isQuotationEditable\">\r\n              啟用額外費用\r\n            </nb-checkbox>\r\n          </div>\r\n          <div class=\"row align-items-center\" *ngIf=\"enableAdditionalFee\">\r\n            <div class=\"col-md-4\">\r\n              <label class=\"form-label mb-1\">費用名稱：</label>\r\n              <input type=\"text\" class=\"form-control form-control-sm\" [(ngModel)]=\"additionalFeeName\"\r\n                (ngModelChange)=\"updateAdditionalFee()\" placeholder=\"例如：營業稅、服務費\" [disabled]=\"!isQuotationEditable\">\r\n            </div>\r\n            <div class=\"col-md-3\">\r\n              <label class=\"form-label mb-1\">百分比（%）：</label>\r\n              <input type=\"number\" class=\"form-control form-control-sm\" [(ngModel)]=\"additionalFeePercentage\"\r\n                (ngModelChange)=\"updateAdditionalFee()\" min=\"0\" max=\"100\" step=\"0.1\" [disabled]=\"!isQuotationEditable\">\r\n            </div>\r\n            <div class=\"col-md-5\">\r\n              <label class=\"form-label mb-1\">{{ additionalFeeName }}：</label>\r\n              <div class=\"text-info fw-bold\">{{ formatCurrency(additionalFeeAmount) }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"border-top pt-2\">\r\n          <h4 class=\"text-primary fw-bold\">總金額: {{ formatCurrency(finalTotalAmount) }}</h4>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <button class=\"btn btn-outline-info btn-sm me-2\" (click)=\"printQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\" title=\"列印報價單\">\r\n          <i class=\"fas fa-print me-1\"></i> 列印報價單\r\n        </button>\r\n        <!-- 報價單已鎖定時才顯示 -->\r\n        <button *ngIf=\"!isQuotationEditable\" class=\"btn btn-outline-success btn-sm me-2\" (click)=\"createNewQuotation()\"\r\n          title=\"產生新報價單\">\r\n          <i class=\"fas fa-plus me-1\"></i> 產生新報價單\r\n        </button>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-warning m-2\" (click)=\"lockQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          鎖定報價單\r\n        </button>\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          儲存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;AAEpF,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAwBC,kBAAkB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;ICdtEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAuCAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IAC7DX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC5Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC7Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC9Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;;IAeFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAgB,UAAA,mBAAAC,wEAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAC,uBAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGtB,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAqDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAgB,UAAA,mBAAAU,8EAAA;MAAA1B,EAAA,CAAAkB,aAAA,CAAAS,IAAA;MAAA,MAAAC,QAAA,GAAA5B,EAAA,CAAAqB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAS,yBAAA,GAAA9B,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAW,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAnBXH,EAFF,CAAAC,cAAA,SAAmD,SAE7C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAgC,UAAA,KAAAC,qDAAA,qBACyD;IAGzDjC,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAgB,UAAA,mBAAAkB,qEAAA;MAAA,MAAAN,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHxC,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAgB,UAAA,mBAAAyB,qEAAA;MAAA,MAAAb,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHxC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoH;IAAlHD,EAAA,CAAAgB,UAAA,mBAAA0B,qEAAA;MAAA,MAAAd,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgB,4BAAA,CAA6B,0BAA0B,EAAAhB,MAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IACjHxC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA/BD,EAAA,CAAAgB,UAAA,mBAAA2B,qEAAA;MAAA,MAAAf,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAwB,cAAA,CAAAhB,QAAA,CAAoB;IAAA,EAAC;IACnF5B,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA/CD,EAAA,CAAAgB,UAAA,mBAAA6B,qEAAA;MAAA,MAAAjB,QAAA,GAAA5B,EAAA,CAAAkB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,MAAAyB,mBAAA,GAAA9C,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA2B,aAAA,CAAAD,mBAAA,EAAAlB,QAAA,CAAoC;IAAA,EAAC;IACnG5B,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAvCCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAqB,UAAA,CAAoB;IACpBjD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;IAElBlD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAmD,kBAAA,MAAAvB,QAAA,CAAAwB,UAAA,yCAAAxB,QAAA,CAAAwB,UAAA,wCAEF;IACIpD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAAyB,SAAA,6BAAAzB,QAAA,CAAAyB,SAAA,iCAA4E;IAC5ErD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA0B,aAAA,CAAuB;IAEzBtD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAuD,kBAAA,MAAA3B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,8CAGF;IACIxD,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA6B,WAAA,UAAA7B,QAAA,CAAA6B,WAAA,uDAA0E;IAC1EzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAgD,iBAAA,CAAApB,QAAA,CAAA8B,SAAA,mCAAiC;IAE1B1D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuC,QAAA,CAAc;;;;;IA6C3B3D,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAwD,UAAA,CAAgB;IACrE5D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqD,UAAA,CAAApD,cAAA,MACF;;;;;IAoDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAyD,UAAA,CAAgB;IACzE7D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsD,UAAA,CAAAnD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA0D,UAAA,CAAgB;IACzE9D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuD,UAAA,CAAApD,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAkD,gBACqC;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6F;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAC,+GAAAC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAgD,IAAA;MAAA,MAAA9C,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,EAAAJ,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,GAAAJ,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,IAAAsC,uFAAA,wBAA4E;IAIhFtE,EADE,CAAAG,YAAA,EAAY,EACR;;;;IAL0BH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAAC,kBAAA,CAA+C;IAC7CrE,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzDzE,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAsE,UAAA,CAAgB;IACxE1E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAmE,UAAA,CAAAhE,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAiD,gBACqC;IAClFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA0F;IAA9DD,EAAA,CAAA+D,gBAAA,2BAAAY,+GAAAV,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA0D,IAAA;MAAA,MAAAxD,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,EAAAZ,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,GAAAZ,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA8C;IACxEjE,EAAA,CAAAgC,UAAA,IAAA8C,uFAAA,wBAA2E;IAI/E9E,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALwBH,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAAS,iBAAA,CAA8C;IAC1C7E,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1D/E,EAFJ,CAAAC,cAAA,uBAA+C,cACrB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAA+D,gBAAA,2BAAAiB,wGAAAf,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,EAAA2B,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,GAAA2B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,IAAAkD,gFAAA,wBAAwE;IAI5ElF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAA+D,gBAAA,2BAAAoB,oGAAAlB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAC,UAAA,EAAApB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAC,UAAA,GAAApB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAoC;IACnGjE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAA+D,gBAAA,2BAAAuB,qGAAArB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,EAAAe,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,GAAAe,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAgC;IAE/FjE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAA+D,gBAAA,2BAAAwB,qGAAAtB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAI,aAAA,EAAAvB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAI,aAAA,GAAAvB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAuC;IACtGjE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmB;IAD2CD,EAAA,CAAA+D,gBAAA,2BAAA0B,qGAAAxB,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAM,WAAA,EAAAzB,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAM,WAAA,GAAAzB,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAqC;IAErGjE,EAFE,CAAAG,YAAA,EACmB,EACf;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAA+D,gBAAA,2BAAA4B,qGAAA1B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,EAAA3B,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,GAAA3B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+B;IAC9FjE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAA+D,gBAAA,2BAAA8B,qGAAA5B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAU,MAAA,EAAA7B,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAU,MAAA,GAAA7B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAgC;IAC/FjE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAA+D,gBAAA,2BAAAgC,yGAAA9B,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,EAAA/B,MAAA,MAAA7C,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,GAAA/B,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+C;IAC3EjE,EAAA,CAAAgC,UAAA,KAAAiE,iFAAA,wBAA4E;IAIhFjG,EADE,CAAAG,YAAA,EAAY,EACR;IAYNH,EAVA,CAAAgC,UAAA,KAAAkE,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/CnG,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAqC,2GAAAnC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,EAAAY,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,GAAAY,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAmC;IAACjE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAA+D,gBAAA,2BAAAsC,2GAAApC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,EAAAO,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,GAAAO,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAmC;IAACjE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAAsG,SAAA,mBAAoD;IACpDtG,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAA+D,gBAAA,2BAAAwC,qGAAAtC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,EAAAvC,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,GAAAvC,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyC;IADhEjE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAAsG,SAAA,4BAA8D;IAChEtG,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAAsG,SAAA,mBAAoD;IACpDtG,EAAA,CAAAC,cAAA,iBAC+D;IAAxCD,EAAA,CAAA+D,gBAAA,2BAAA0C,qGAAAxC,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+D,IAAA;MAAA,MAAA7D,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,EAAAzC,MAAA,MAAA7C,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,GAAAzC,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAuC;IAD9DjE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAAsG,SAAA,4BAA4D;IAIpEtG,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAA9B,kBAAA,CAA+C;IAC7CtC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAuF,oBAAA,CAAuB;IAUM3G,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAC,UAAA,CAAoC;IAOpCrF,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAlC,MAAA,CAAgC;IAQhClD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAI,aAAA,CAAuC;IAOtCxF,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAM,WAAA,CAAqC;IAQtC1F,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAQ,KAAA,CAA+B;IAM/B5F,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAU,MAAA,CAAgC;IAO/D9F,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgD,cAAA,CAAA4B,kBAAA,CAA+C;IAC7ChG,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAAoD,OAAA,CAAAoC,gBAAA,CAA2B;IAMpC5G,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAyF,iBAAA,CAAuB;IAUvB7G,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA0F,gBAAA,CAAsB;IAejB9G,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAA/B,SAAA,CAAmC;IAQnCrD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAA1B,SAAA,CAAmC;IAWQ1D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAA2G,aAAA,CAA0B;IACtE/G,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAoB,eAAA,CAAyC;IAKCxG,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAA4G,WAAA,CAAwB;IAClEhH,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAgE,WAAA,CAAAsB,aAAA,CAAuC;;;;;;IAQpE1G,EAAA,CAAAC,cAAA,iBAAqG;IAA9BD,EAAA,CAAAgB,UAAA,mBAAAiG,uFAAA;MAAAjH,EAAA,CAAAkB,aAAA,CAAAgG,IAAA;MAAA,MAAAC,OAAA,GAAAnH,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAiG,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhIpHH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAgC,UAAA,IAAAsF,oEAAA,6BAA+C;IA4H7CtH,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAuG,8EAAA;MAAA,MAAAJ,OAAA,GAAAnH,EAAA,CAAAkB,aAAA,CAAAsG,IAAA,EAAAJ,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IAACnH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrFH,EAAA,CAAAgC,UAAA,IAAA0F,8DAAA,qBAAqG;IAEzG1H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IA/HoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAgE,WAAA,CAAiB;IA6HYpF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuG,QAAA,CAAc;;;;;;IA8BrE3H,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAgB,UAAA,mBAAA4G,wFAAA;MAAA5H,EAAA,CAAAkB,aAAA,CAAA2G,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA2G,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAAC9H,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvB9FH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAuG;IAA5CD,EAAA,CAAA+D,gBAAA,2BAAAiE,qFAAA/D,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAC,aAAA,EAAAlE,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAC,aAAA,GAAAlE,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyC;IACtGjE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+G;IAA9CD,EAAA,CAAA+D,gBAAA,2BAAAqE,sFAAAnE,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAG,eAAA,EAAApE,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAG,eAAA,GAAApE,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA2C;IAC9GjE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAqG;IAArCD,EAAA,CAAA+D,gBAAA,2BAAAuE,sFAAArE,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA+G,IAAA;MAAA,MAAA7G,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,EAAAe,MAAA,MAAA7C,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,GAAAe,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAkC;IAEtGjE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,kBACQ;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAuH,+EAAA;MAAA,MAAAT,OAAA,GAAA9H,EAAA,CAAAkB,aAAA,CAAA+G,IAAA,EAAAb,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IAAC9H,EAAA,CAAAE,MAAA,IAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9EH,EAAA,CAAAgC,UAAA,KAAAwG,+DAAA,sBAAiF;IAErFxI,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAjBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAC,aAAA,CAAyC;IAKnCnI,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAG,eAAA,CAA2C;IAK5CrI,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAA8G,aAAA,CAAAhF,MAAA,CAAkC;IAIxClD,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAgD,iBAAA,gBAAS;IACpChD,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAuG,QAAA,CAAc;;;;;;IAc7C3H,EADF,CAAAC,cAAA,eAA6E,iBACV;IAA7BD,EAAA,CAAAgB,UAAA,mBAAAyH,oFAAA;MAAAzI,EAAA,CAAAkB,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAuH,gBAAA,EAAkB;IAAA,EAAC;IAC9D3I,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,UAAK,kBACwE;IAA7BD,EAAA,CAAAgB,UAAA,mBAAA4H,oFAAA;MAAA5I,EAAA,CAAAkB,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAyH,gBAAA,EAAkB;IAAA,EAAC;IACxE7I,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAsE;IAA7BD,EAAA,CAAAgB,UAAA,mBAAA8H,oFAAA;MAAA9I,EAAA,CAAAkB,aAAA,CAAAwH,IAAA;MAAA,MAAAtH,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA2H,gBAAA,EAAkB;IAAA,EAAC;IACnE/I,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAsG,SAAA,aAAgC;IAChCtG,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iNAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAyCIH,EAAA,CAAAC,cAAA,kBAAmG;IAAjCD,EAAA,CAAAgB,UAAA,mBAAAgI,8FAAA;MAAAhJ,EAAA,CAAAkB,aAAA,CAAA+H,IAAA;MAAA,MAAAC,KAAA,GAAAlJ,EAAA,CAAAqB,aAAA,GAAA8H,KAAA;MAAA,MAAA/H,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAgI,mBAAA,CAAAF,KAAA,CAAsB;IAAA,EAAC;IAChGlJ,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAsG,SAAA,aAA2B;IAC7BtG,EAAA,CAAAG,YAAA,EAAO;;;;;;IA7BPH,EAFJ,CAAAC,cAAA,SAAuD,SACjD,iBAI0G;IAHjFD,EAAA,CAAA+D,gBAAA,2BAAAsF,2FAAApF,MAAA;MAAA,MAAAqF,QAAA,GAAAtJ,EAAA,CAAAkB,aAAA,CAAAqI,IAAA,EAAA1H,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAmF,QAAA,CAAAE,SAAA,EAAAvF,MAAA,MAAAqF,QAAA,CAAAE,SAAA,GAAAvF,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA4B;IAIzDjE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEsG;IAD3ED,EAAA,CAAA+D,gBAAA,2BAAA0F,2FAAAxF,MAAA;MAAA,MAAAqF,QAAA,GAAAtJ,EAAA,CAAAkB,aAAA,CAAAqI,IAAA,EAAA1H,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAmF,QAAA,CAAAI,UAAA,EAAAzF,MAAA,MAAAqF,QAAA,CAAAI,UAAA,GAAAzF,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA6B;IAACjE,EAAA,CAAAgB,UAAA,2BAAAyI,2FAAA;MAAAzJ,EAAA,CAAAkB,aAAA,CAAAqI,IAAA;MAAA,MAAAnI,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAAuI,cAAA,EAAgB;IAAA,EAAC;IAE/F3J,EAFE,CAAAG,YAAA,EACwG,EACrG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAE+E;IADpDD,EAAA,CAAA+D,gBAAA,2BAAA6F,2FAAA3F,MAAA;MAAA,MAAAqF,QAAA,GAAAtJ,EAAA,CAAAkB,aAAA,CAAAqI,IAAA,EAAA1H,SAAA;MAAA7B,EAAA,CAAAmE,kBAAA,CAAAmF,QAAA,CAAAO,MAAA,EAAA5F,MAAA,MAAAqF,QAAA,CAAAO,MAAA,GAAA5F,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAyB;IAACjE,EAAA,CAAAgB,UAAA,2BAAA4I,2FAAA;MAAA5J,EAAA,CAAAkB,aAAA,CAAAqI,IAAA;MAAA,MAAAnI,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAAuI,cAAA,EAAgB;IAAA,EAAC;IAE3F3J,EAFE,CAAAG,YAAA,EACiF,EAC9E;IACLH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAGyF;IACzFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAgC,UAAA,KAAA8H,qEAAA,sBAAmG,KAAAC,mEAAA,oBAG7C;IAI1D/J,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IA5BCH,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAAgK,WAAA,cAAA5I,MAAA,CAAA6I,mBAAA,IAAAX,QAAA,CAAAvJ,kBAAA,UAAAuJ,QAAA,CAAAvJ,kBAAA,OAAyG;IAHhFC,EAAA,CAAAuE,gBAAA,YAAA+E,QAAA,CAAAE,SAAA,CAA4B;IACrDxJ,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,IAAAX,QAAA,CAAAvJ,kBAAA,UAAAuJ,QAAA,CAAAvJ,kBAAA,OAAmG;IAKxEC,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAuE,gBAAA,YAAA+E,QAAA,CAAAI,UAAA,CAA6B;IACrB1J,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,IAAAX,QAAA,CAAAvJ,kBAAA,OAAkE;IAG1EC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAuE,gBAAA,YAAA+E,QAAA,CAAAO,MAAA,CAAyB;IACxC7J,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,IAAAX,QAAA,CAAAvJ,kBAAA,OAAkE;IAGhFC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAa,MAAA,CAAA8I,cAAA,CAAAZ,QAAA,CAAAI,UAAA,GAAAJ,QAAA,CAAAO,MAAA,OACF;IAEsB7J,EAAA,CAAAM,SAAA,GAAqD;IAEvEN,EAFkB,CAAAgK,WAAA,kBAAAV,QAAA,CAAAvJ,kBAAA,OAAqD,eAAAuJ,QAAA,CAAAvJ,kBAAA,OACrB,oBAAAuJ,QAAA,CAAAvJ,kBAAA,UAAAuJ,QAAA,CAAAvJ,kBAAA,OACsC;IACxFC,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAa,MAAA,CAAA+I,oBAAA,CAAAb,QAAA,CAAAvJ,kBAAA,OACF;IAGSC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA6I,mBAAA,CAAyB;IAG3BjK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAgB,MAAA,CAAA6I,mBAAA,CAA0B;;;;;IAMnCjK,EADF,CAAAC,cAAA,SAAwC,cACc;IAClDD,EAAA,CAAAE,MAAA,iLACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IAoBHH,EAFJ,CAAAC,cAAA,eAAgE,eACxC,iBACW;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5CH,EAAA,CAAAC,cAAA,iBACqG;IAD7CD,EAAA,CAAA+D,gBAAA,2BAAAqG,4FAAAnG,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAmJ,IAAA;MAAA,MAAAjJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAkJ,iBAAA,EAAArG,MAAA,MAAA7C,MAAA,CAAAkJ,iBAAA,GAAArG,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAA+B;IACrFjE,EAAA,CAAAgB,UAAA,2BAAAoJ,4FAAA;MAAApK,EAAA,CAAAkB,aAAA,CAAAmJ,IAAA;MAAA,MAAAjJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAAmJ,mBAAA,EAAqB;IAAA,EAAC;IAC3CvK,EAFE,CAAAG,YAAA,EACqG,EACjG;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACW;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9CH,EAAA,CAAAC,cAAA,iBACyG;IAD/CD,EAAA,CAAA+D,gBAAA,2BAAAyG,4FAAAvG,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAAmJ,IAAA;MAAA,MAAAjJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAqJ,uBAAA,EAAAxG,MAAA,MAAA7C,MAAA,CAAAqJ,uBAAA,GAAAxG,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAqC;IAC7FjE,EAAA,CAAAgB,UAAA,2BAAAwJ,4FAAA;MAAAxK,EAAA,CAAAkB,aAAA,CAAAmJ,IAAA;MAAA,MAAAjJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAAmJ,mBAAA,EAAqB;IAAA,EAAC;IAC3CvK,EAFE,CAAAG,YAAA,EACyG,EACrG;IAEJH,EADF,CAAAC,cAAA,eAAsB,kBACW;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAE5EF,EAF4E,CAAAG,YAAA,EAAM,EAC1E,EACF;;;;IAZsDH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAkJ,iBAAA,CAA+B;IACpBtK,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,CAAiC;IAI1CjK,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAqJ,uBAAA,CAAqC;IACxBzK,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,CAAiC;IAGzEjK,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAO,kBAAA,KAAAa,MAAA,CAAAkJ,iBAAA,WAAwB;IACxBtK,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAgD,iBAAA,CAAA5B,MAAA,CAAA8I,cAAA,CAAA9I,MAAA,CAAAsJ,mBAAA,EAAyC;;;;;;IAiB9E1K,EAAA,CAAAC,cAAA,kBACiB;IADgED,EAAA,CAAAgB,UAAA,mBAAA2J,wFAAA;MAAA3K,EAAA,CAAAkB,aAAA,CAAA0J,IAAA;MAAA,MAAAxJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAyJ,kBAAA,EAAoB;IAAA,EAAC;IAE7G7K,EAAA,CAAAsG,SAAA,aAAgC;IAACtG,EAAA,CAAAE,MAAA,6CACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IASTH,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAgB,UAAA,mBAAA8J,wFAAA;MAAA9K,EAAA,CAAAkB,aAAA,CAAA6J,IAAA;MAAA,MAAAC,OAAA,GAAAhL,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA6J,aAAA,CAAAD,OAAA,CAAkB;IAAA,EAAC;IAE1FhL,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAgB,MAAA,CAAA8J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IAG1CnL,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAgB,UAAA,mBAAAoK,wFAAA;MAAApL,EAAA,CAAAkB,aAAA,CAAAmK,IAAA;MAAA,MAAAL,OAAA,GAAAhL,EAAA,CAAAqB,aAAA,GAAA+F,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAkK,aAAA,CAAAN,OAAA,CAAkB;IAAA,EAAC;IAE1FhL,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAgB,MAAA,CAAA8J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IA7I9CnL,EADF,CAAAC,cAAA,mBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,mBAAc;IAiBZD,EAfA,CAAAgC,UAAA,IAAAuJ,2DAAA,mBAA6E,IAAAC,2DAAA,mBAeV;IAS3DxL,EAJR,CAAAC,cAAA,eAA8B,iBACQ,YAC3B,SACD,eACc;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEtBF,EAFsB,CAAAG,YAAA,EAAK,EACpB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IAmCLD,EAlCA,CAAAgC,UAAA,KAAAyJ,2DAAA,mBAAuD,KAAAC,2DAAA,kBAkCf;IAO9C1L,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAIFH,EAFJ,CAAAC,cAAA,gBAA6B,gBACT,eACW;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAClEF,EADkE,CAAAG,YAAA,EAAK,EACjE;IAKFH,EAFJ,CAAAC,cAAA,gBAA8C,gBAC1B,wBAEoB;IADvBD,EAAA,CAAA+D,gBAAA,2BAAA4H,4FAAA1H,MAAA;MAAAjE,EAAA,CAAAkB,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAArB,EAAA,CAAAmE,kBAAA,CAAA/C,MAAA,CAAAyK,mBAAA,EAAA5H,MAAA,MAAA7C,MAAA,CAAAyK,mBAAA,GAAA5H,MAAA;MAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;IAAA,EAAiC;IAACjE,EAAA,CAAAgB,UAAA,2BAAA2K,4FAAA;MAAA3L,EAAA,CAAAkB,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAAiBJ,MAAA,CAAAmJ,mBAAA,EAAqB;IAAA,EAAC;IAEpFvK,EAAA,CAAAE,MAAA,8CACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IACNH,EAAA,CAAAgC,UAAA,KAAA8J,4DAAA,oBAAgE;IAgBlE9L,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,gBAA6B,eACM;IAAAD,EAAA,CAAAE,MAAA,IAA2C;IAGlFF,EAHkF,CAAAG,YAAA,EAAK,EAC7E,EACF,EACO;IAGXH,EAFJ,CAAAC,cAAA,2BAAuD,WAChD,mBAEsD;IADRD,EAAA,CAAAgB,UAAA,mBAAA+K,+EAAA;MAAA/L,EAAA,CAAAkB,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAA4K,cAAA,EAAgB;IAAA,EAAC;IAEzEhM,EAAA,CAAAsG,SAAA,cAAiC;IAACtG,EAAA,CAAAE,MAAA,wCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAgC,UAAA,KAAAiK,+DAAA,sBACiB;IAOnBjM,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,mBACkE;IAAvBD,EAAA,CAAAgB,UAAA,mBAAAkL,+EAAA;MAAA,MAAAlB,OAAA,GAAAhL,EAAA,CAAAkB,aAAA,CAAA0K,IAAA,EAAAxE,SAAA;MAAA,MAAAhG,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAwB,WAAA,CAASJ,MAAA,CAAAqG,OAAA,CAAAuD,OAAA,CAAY;IAAA,EAAC;IAAChL,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMhFH,EAJA,CAAAgC,UAAA,KAAAmK,+DAAA,sBAC2C,KAAAC,+DAAA,sBAIA;IAKjDpM,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;IAjJNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAmD,kBAAA,2BAAA/B,MAAA,CAAAiL,YAAA,kBAAAjL,MAAA,CAAAiL,YAAA,CAAApJ,UAAA,QAAA7B,MAAA,CAAAiL,YAAA,kBAAAjL,MAAA,CAAAiL,YAAA,CAAAnJ,MAAA,aACF;IAGQlD,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA6I,mBAAA,CAAyB;IAezBjK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAgB,MAAA,CAAA6I,mBAAA,CAA0B;IAkBLjK,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAgB,MAAA,CAAA8J,cAAA,CAAmB;IAkCnClL,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA8J,cAAA,CAAAC,MAAA,OAAiC;IAWbnL,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,kBAAA,mBAAAa,MAAA,CAAA8I,cAAA,CAAA9I,MAAA,CAAAkL,WAAA,MAAqC;IAMjDtM,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAuE,gBAAA,YAAAnD,MAAA,CAAAyK,mBAAA,CAAiC;IAC5C7L,EAAA,CAAAI,UAAA,cAAAgB,MAAA,CAAA6I,mBAAA,CAAiC;IAIAjK,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAAyK,mBAAA,CAAyB;IAmB7B7L,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAO,kBAAA,yBAAAa,MAAA,CAAA8I,cAAA,CAAA9I,MAAA,CAAAmL,gBAAA,MAA2C;IAO5EvM,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAgB,MAAA,CAAA8J,cAAA,CAAAC,MAAA,OAAwC;IAIjCnL,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAgB,MAAA,CAAA6I,mBAAA,CAA0B;IAY1BjK,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA6I,mBAAA,CAAyB;IAIzBjK,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAgB,MAAA,CAAA6I,mBAAA,CAAyB;;;ADld1C,OAAM,MAAOuC,4BAA6B,SAAQtN,aAAa;EAE7DuN,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZpN,KAAK,EAAE;KACR,EACD;MACEmN,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBpN,KAAK,EAAE;KACR,CACF;IAED,KAAAqN,gBAAgB,GAAG,CACjB;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVpN,KAAK,EAAE;KACR,EACD;MACEmN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbpN,KAAK,EAAE;KACR,EACD;MACEmN,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBpN,KAAK,EAAE;KACR,CACF;IAKD,KAAAsN,gBAAgB,GAAU,CAAC;MAAEtN,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAI,gBAAgB,GAAU,CAAC;MAAEvN,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA9I,eAAe,GAAU,CAAC;MAAErE,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAjH,gBAAgB,GAAU,CAAC;MAAElG,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAApJ,gBAAgB,GAAU,CAAC;MAAE/D,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAK,iBAAiB,GAAU,CAAC;MAAExN,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAAM,sBAAsB,GAAU,CAAC;MAAEzN,KAAK,EAAE,IAAI;MAAEmN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAArJ,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAAC4H,UAAU,CAACyB,cAAc,CAAC5O,iBAAiB,CAAC;MAClEiF,gBAAgB,EAAE,IAAI,CAACkI,UAAU,CAACyB,cAAc,CAAC1O,aAAa,CAAC;MAC/DkH,gBAAgB,EAAE,IAAI,CAAC+F,UAAU,CAACyB,cAAc,CAAC3O,aAAa,CAAC;MAC/D0O,sBAAsB,EAAE,IAAI,CAACxB,UAAU,CAACyB,cAAc,CAACxO,mBAAmB;KAC3E;IAGD,KAAAyO,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACX1I,KAAK,EAAE,EAAE;MACTvC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbE,SAAS,EAAE,KAAK;MAChB8B,aAAa,EAAE,EAAE;MACjB+I,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbpL,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACd6C,MAAM,EAAE;KACT;IACD;IACA,KAAAoF,cAAc,GAAoB,EAAE;IACpC,KAAAoB,WAAW,GAAW,CAAC;IACvB;IACA,KAAAhC,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAG,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAAC,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAA6B,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAAV,mBAAmB,GAAY,KAAK,CAAC,CAAE;IACvC,KAAAQ,YAAY,GAAQ,IAAI;IACxB,KAAAoC,kBAAkB,GAAW,CAAC;IAC9B,KAAAxE,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAuHrC,KAAAyE,YAAY,GAAgB,IAAI;IAwKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEd,KAAK,EAAE,EAAE;MAAEnN,KAAK,EAAE;KACnB,CACF;IA3XC,IAAI,CAAC0M,aAAa,CAACwB,OAAO,EAAE,CAACC,IAAI,CAC/BzP,GAAG,CAAE0P,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAACzB,eAAe,GAAGuB,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAmFSC,QAAQA,CAAA;IACf,IAAI,CAACnK,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAAC4H,UAAU,CAACyB,cAAc,CAAC5O,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACoH,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC+F,UAAU,CAACyB,cAAc,CAAC3O,aAAa,CAAC,CACjD;IACD,IAAI,CAACgF,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACkI,UAAU,CAACyB,cAAc,CAAC1O,aAAa,CAAC,CACjD;IACD,IAAI,CAACwO,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACvB,UAAU,CAACyB,cAAc,CAACzO,cAAc,CAAC,CAClD;IACD,IAAI,CAACwO,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAACxB,UAAU,CAACyB,cAAc,CAACxO,mBAAmB,CAAC,CACvD;IAED,IAAIC,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,IAAI,IACtEvP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAIC,SAAS,IAC5ExP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC3P,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,CAAC;MACjG,IAAI,CAAC/M,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAmN,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,GAC7G,IAAI,CAACpB,gBAAgB,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACG,kBAAkB,CAAC5B,KAAK,CAAC,GACpF,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5BjI,kBAAkB,EAAEsJ,eAAe,CAACtJ,kBAAkB,IAAI,IAAI,IAAIsJ,eAAe,CAACtJ,kBAAkB,IAAIqJ,SAAS,GAC7G,IAAI,CAACzI,gBAAgB,CAAC8I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACtJ,kBAAkB,CAAC6H,KAAK,CAAC,GACpF,IAAI,CAACjH,gBAAgB,CAAC,CAAC,CAAC;QAC5BvC,kBAAkB,EAAEiL,eAAe,CAACjL,kBAAkB,IAAI,IAAI,IAAIiL,eAAe,CAACjL,kBAAkB,IAAIgL,SAAS,GAC7G,IAAI,CAAC5K,gBAAgB,CAACiL,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACjL,kBAAkB,CAACwJ,KAAK,CAAC,GACpF,IAAI,CAACpJ,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAEyK,eAAe,CAACzK,iBAAiB,IAAI,IAAI,IAAIyK,eAAe,CAACzK,iBAAiB,IAAIwK,SAAS,GAC1G,IAAI,CAACtK,eAAe,CAAC2K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACzK,iBAAiB,CAACgJ,KAAK,CAAC,GAClF,IAAI,CAAC9I,eAAe,CAAC,CAAC,CAAC;QAC3B6K,mBAAmB,EAAEN,eAAe,CAACM,mBAAmB,IAAI,IAAI,IAAIN,eAAe,CAACM,mBAAmB,IAAIP,SAAS,GAChH,IAAI,CAACnB,iBAAiB,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACM,mBAAmB,CAAC/B,KAAK,CAAC,GACtF,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC;QAC7B2B,wBAAwB,EAAEP,eAAe,CAACO,wBAAwB,IAAI,IAAI,IAAIP,eAAe,CAACO,wBAAwB,IAAIR,SAAS,GAC/H,IAAI,CAAClB,sBAAsB,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACO,wBAAwB,CAAChC,KAAK,CAAC,GAChG,IAAI,CAACM,sBAAsB,CAAC,CAAC,CAAC;QAClC2B,gBAAgB,EAAER,eAAe,CAACQ,gBAAgB,IAAI,IAAI,IAAIR,eAAe,CAACQ,gBAAgB,IAAIT,SAAS,GACvG,IAAI,CAACtB,gBAAgB,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACQ,gBAAgB,CAACjC,KAAK,CAAC,GAClF,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;QAC5BgC,KAAK,EAAET,eAAe,CAACS,KAAK,IAAI,IAAI,IAAIT,eAAe,CAACS,KAAK,IAAIV,SAAS,GACtEC,eAAe,CAACS,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEV,eAAe,CAACU,GAAG,IAAI,IAAI,IAAIV,eAAe,CAACU,GAAG,IAAIX,SAAS,GAChEC,eAAe,CAACU,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAAC3N,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB2N,qBAAqB,EAAE,IAAI,CAACtB,uBAAuB,CAAC,CAAC,CAAC;QACtDc,kBAAkB,EAAE,IAAI,CAACxB,gBAAgB,CAAC,CAAC,CAAC;QAC5CjI,kBAAkB,EAAE,IAAI,CAACY,gBAAgB,CAAC,CAAC,CAAC;QAC5CvC,kBAAkB,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1C6K,mBAAmB,EAAE,IAAI,CAAC1B,iBAAiB,CAAC,CAAC,CAAC;QAC9C2B,wBAAwB,EAAE,IAAI,CAAC1B,sBAAsB,CAAC,CAAC,CAAC;QACxD2B,gBAAgB,EAAE,IAAI,CAAC/B,gBAAgB,CAAC,CAAC,CAAC;QAC1CgC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACE,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChB9N,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAyN,KAAK,EAAE,IAAI,CAAC1N,WAAW,CAAC0N,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAAC3N,WAAW,CAAC2N,GAAG;MACzBP,kBAAkB,EAAE,IAAI,CAACpN,WAAW,CAACoN,kBAAkB;MACvDzJ,kBAAkB,EAAE,IAAI,CAAC3D,WAAW,CAAC2D,kBAAkB;MACvD8J,gBAAgB,EAAE,IAAI,CAACzN,WAAW,CAACyN,gBAAgB;MACnDzL,kBAAkB,EAAE,IAAI,CAAChC,WAAW,CAACgC,kBAAkB;MACvDQ,iBAAiB,EAAE,IAAI,CAACxC,WAAW,CAACwC,iBAAiB;MACrD+K,mBAAmB,EAAE,IAAI,CAACvN,WAAW,CAACuN,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAACxN,WAAW,CAACwN;KAC5C;IACDhQ,mBAAmB,CAACwQ,iBAAiB,CAACvQ,WAAW,CAACsP,YAAY,EAAEG,IAAI,CAACe,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACtB,SAAS,EAAE;EACjC;EAEAuB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC/C,SAAS,GAAG+C,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACtB,SAAS,EAAE;EACjC;EAEAyB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrO,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAACwK,aAAa,CAAC4D,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACvO,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAAC0M,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAACzD,gBAAgB,CAAC0D,iBAAiB,CACrCjC,GAAG,CAAC+B,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAAChE,OAAO,CAACmE,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACvG,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACuD,YAAY,GAAG8C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACjD,YAAY,EAAE;MACrB,MAAMkD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACpD,YAAY,CAAC;MAC3C,IAAI,CAAC3B,aAAa,CAACgF,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJpB,YAAY,EAAE,IAAI,CAACvO,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrD0P,KAAK,EAAE,IAAI,CAACvD;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACgC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACjE,OAAO,CAACqF,aAAa,CAACpD,GAAG,CAACmC,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACtB,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACpC,OAAO,CAACmE,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAkB,gBAAgBA,CAAA;IACd,IAAI,CAACpF,aAAa,CAACqF,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAEpB,YAAY,EAAE,IAAI,CAACvO,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAAC0M,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC7C,gBAAgB,GAAG,CAAC;UACvBJ,KAAK,EAAE,EAAE;UAAEnN,KAAK,EAAE;SACnB,EAAE,GAAGoO,GAAG,CAAC+B,OAAO,CAACwB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEzE,KAAK,EAAEyE,CAAC;YAAE5R,KAAK,EAAE4R;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIzS,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,IAAI,IACtEvP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAIC,SAAS,IAC5ExP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC3P,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,EAAE;YACjG,IAAIlG,KAAK,GAAG,IAAI,CAAC8E,gBAAgB,CAACsE,SAAS,CAAE5C,CAAM,IAAKA,CAAC,CAAC9B,KAAK,IAAIyB,eAAe,CAACG,kBAAkB,CAAC5B,KAAK,CAAC;YAC5G,IAAI,CAACxL,WAAW,CAACoN,kBAAkB,GAAG,IAAI,CAACxB,gBAAgB,CAAC9E,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAAC9G,WAAW,CAACoN,kBAAkB,GAAG,IAAI,CAACxB,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKAuE,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAACvO,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrDmQ,SAAS,EAAE,IAAI,CAAChF,SAAS;MACzBiF,QAAQ,EAAE,IAAI,CAAClF;KAChB;IAED;IACA;IACA;IACA,IAAI,IAAI,CAACpL,WAAW,CAAC0N,KAAK,IAAI,IAAI,CAAC1N,WAAW,CAAC2N,GAAG,EAAE;MAClD,IAAI,CAACyC,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE1C,KAAK,EAAE,IAAI,CAAC1N,WAAW,CAAC0N,KAAK;QAAEC,GAAG,EAAE,IAAI,CAAC3N,WAAW,CAAC2N;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAAC3N,WAAW,CAACoN,kBAAkB,EAAE;MACvC,IAAI,CAACgD,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAACoN,kBAAkB,CAAC5B,KAAK;IAC5E;IACA,IAAI,IAAI,CAACxL,WAAW,CAAC2D,kBAAkB,CAAC6H,KAAK,EAAE;MAC7C,IAAI,CAAC4E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAAC2D,kBAAkB,CAAC6H,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAACxL,WAAW,CAACyN,gBAAgB,CAACjC,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC4E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAACyN,gBAAgB,CAACjC,KAAK;IACzE;IACA,IAAI,IAAI,CAACxL,WAAW,CAACgC,kBAAkB,CAACwJ,KAAK,EAAE;MAC7C,IAAI,CAAC4E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAACgC,kBAAkB,CAACwJ,KAAK;IAC5E;IACA,IAAI,IAAI,CAACxL,WAAW,CAACwC,iBAAiB,CAACgJ,KAAK,EAAE;MAC5C,IAAI,CAAC4E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAACwC,iBAAiB,CAACgJ,KAAK;IAC1E;IACA,IAAI,IAAI,CAACxL,WAAW,CAACuN,mBAAmB,CAAC/B,KAAK,EAAE;MAC9C,IAAI,CAAC4E,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACpQ,WAAW,CAACuN,mBAAmB,CAAC/B,KAAK;IAC9E;IAEA,OAAO,IAAI,CAAC4E,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC9P,MAAM,IAAI,CAAC,KAAK6P,CAAC,CAAC7P,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAqN,YAAYA,CAAA;IACV,OAAO,IAAI,CAACxD,aAAa,CAACkG,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACQ,WAAW;KACvB,CAAC,CAAC3D,IAAI,CACLzP,GAAG,CAAC0P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,SAAS,GAAGpE,GAAG,CAAC+B,OAAO;QAC5B,IAAI,CAAClD,YAAY,GAAGmB,GAAG,CAACqE,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACjB,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,YAAY,EAAE,CAACtB,SAAS,EAAE;EACjC;EACAiB,gBAAgBA,CAAA;IACd,IAAI,CAACjD,iBAAiB,CAACoG,6CAA6C,CAAC;MACnErB,IAAI,EAAE;QACJsB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAAC1E,IAAI,CACLzP,GAAG,CAAC0P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACnK,oBAAoB,GAAGmI,GAAG,CAAC+B,OAAO,EAAE1F,MAAM,GAAG2D,GAAG,CAAC+B,OAAO,CAACwB,GAAG,CAACvD,GAAG,IAAG;UACtE,OAAO;YACLtO,cAAc,EAAEsO,GAAG,CAACtO,cAAc;YAClC+B,GAAG,EAAEuM,GAAG,CAACvM;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAI1C,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,IAAI,IACtEvP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAIC,SAAS,IAC5ExP,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC3P,mBAAmB,CAACsP,iBAAiB,CAACrP,WAAW,CAACsP,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAAChN,kBAAkB,IAAI,IAAI,IAAIgN,eAAe,CAAChN,kBAAkB,IAAI+M,SAAS,EAAE;YACjG,IAAIlG,KAAK,GAAG,IAAI,CAACxC,oBAAoB,CAAC4L,SAAS,CAAE5C,CAAM,IAAKA,CAAC,CAACpN,GAAG,IAAI+M,eAAe,CAAChN,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAACwC,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAAC9G,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACtE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACqE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACFvH,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAAC+S,gBAAgB,EAAE;MACvBqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACjD,YAAY,EAAE,CAACtB,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAwE,YAAYA,CAACjR,GAAQ,EAAEkR,GAAQ;IAC7B,IAAI,CAACtP,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC2I,aAAa,CAAC4G,6BAA6B,CAAC;MAC/C3B,IAAI,EAAE;QAAE1D,QAAQ,EAAE9L;MAAG;KACtB,CAAC,CAACyM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1L,WAAW,GAAG;UACjB,GAAG0J,GAAG,CAAC+B,OAAO;UACdrK,eAAe,EAAEsI,GAAG,CAAC+B,OAAO,CAAC+C,gBAAgB,GAAG,IAAIC,IAAI,CAAC/E,GAAG,CAAC+B,OAAO,CAAC+C,gBAAgB,CAAC,GAAGvE,SAAS;UAClG3I,aAAa,EAAEoI,GAAG,CAAC+B,OAAO,CAACiD,cAAc,GAAG,IAAID,IAAI,CAAC/E,GAAG,CAAC+B,OAAO,CAACiD,cAAc,CAAC,GAAGzE;SACpF;QAED,IAAIP,GAAG,CAAC+B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,CAAC3P,cAAc,CAAC9B,kBAAkB,GAAG,IAAI,CAAC0R,eAAe,CAAC,IAAI,CAACrN,oBAAoB,EAAE,KAAK,EAAEmI,GAAG,CAAC+B,OAAO,CAACkD,YAAY,CAAC;QAC3H;QACA,IAAI,CAAC3P,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAAC2P,eAAe,CAAC,IAAI,CAACxP,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAEqK,GAAG,CAAC+B,OAAO,CAACrN,UAAU,CAAC;QAC7H,IAAIsL,GAAG,CAAC+B,OAAO,CAACzN,UAAU,EAAE;UAC1B,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACgO,eAAe,CAAC,IAAI,CAACxP,OAAO,CAACoC,gBAAgB,EAAE,OAAO,EAAEkI,GAAG,CAAC+B,OAAO,CAACzN,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACxB,OAAO,CAACoC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACxC,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACmP,eAAe,CAAC,IAAI,CAACxP,OAAO,CAACO,eAAe,EAAE,OAAO,EAAE+J,GAAG,CAAC+B,OAAO,CAACrC,SAAS,CAAC;QAE1H,IAAIM,GAAG,CAAC+B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAAC7L,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAC0I,YAAY,GAAG9B,GAAG,CAAC+B,OAAO,CAACkD,YAAY;UAC5D;QACF;QACA,IAAI,CAACnH,aAAa,CAACqH,IAAI,CAACP,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAM,eAAeA,CAACE,KAAY,EAAEpG,GAAW,EAAED,KAAU;IACnD,OAAOqG,KAAK,CAACxE,IAAI,CAACyE,IAAI,IAAIA,IAAI,CAACrG,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGA9L,eAAeA,CAAC2R,GAAQ,EAAES,IAAS;IACjC,IAAI,CAACV,YAAY,CAACU,IAAI,CAAC3R,GAAG,EAAEkR,GAAG,CAAC;EAClC;EAEAjS,SAASA,CAACiS,GAAQ;IAChB,IAAI,CAACxL,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBjF,MAAM,EAAEmM,SAAS;MACjBhH,eAAe,EAAEgH;KAClB;IACD,IAAI,CAACzC,aAAa,CAACqH,IAAI,CAACP,GAAG,CAAC;EAC9B;EAKAU,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO/U,MAAM,CAAC+U,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAjN,cAAcA,CAACqM,GAAQ;IACrB,IAAI,CAACtO,WAAW,CAACwO,gBAAgB,GAAG,IAAI,CAACxO,WAAW,CAACoB,eAAe,GAAG,IAAI,CAAC4N,UAAU,CAAC,IAAI,CAAChP,WAAW,CAACoB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACpB,WAAW,CAAC0O,cAAc,GAAG,IAAI,CAAC1O,WAAW,CAACsB,aAAa,GAAG,IAAI,CAAC0N,UAAU,CAAC,IAAI,CAAChP,WAAW,CAACsB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAAC6N,kBAAkB,GAAG;MACxB/O,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CvC,UAAU,EAAE,IAAI,CAACmC,WAAW,CAACC,UAAU;MACvCiJ,QAAQ,EAAE,IAAI,CAAClJ,WAAW,CAACoP,GAAG;MAC9BpR,UAAU,EAAE,IAAI,CAACgB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC5B,cAAc,CAAC4B,kBAAkB,CAAC6H,KAAK,GAAG,IAAI;MACxGxK,SAAS,EAAE,IAAI,CAAC+B,WAAW,CAAC/B,SAAS;MACrCK,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B2I,WAAW,EAAE,IAAI,CAACnJ,WAAW,CAACM,WAAW;MACzClC,UAAU,EAAE,IAAI,CAACY,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAACwJ,KAAK,GAAG,IAAI;MACxG/H,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B0I,SAAS,EAAE,IAAI,CAACpK,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACT,cAAc,CAACS,iBAAiB,CAACgJ,KAAK,GAAG,IAAI;MACrG+F,gBAAgB,EAAE,IAAI,CAACxO,WAAW,CAACwO,gBAAgB;MACnDE,cAAc,EAAE,IAAI,CAAC1O,WAAW,CAAC0O;KAClC;IACH,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC3H,KAAK,CAAC4H,aAAa,CAACvJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0B,OAAO,CAAC8H,aAAa,CAAC,IAAI,CAAC7H,KAAK,CAAC4H,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC3H,aAAa,CAAC6H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE,IAAI,CAACuC;KACZ,CAAC,CAAC1F,IAAI,CACLzP,GAAG,CAAC0P,GAAG,IAAG;MACR,IAAIA,GAAG,CAACgC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjE,OAAO,CAACqF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAChI,OAAO,CAACmE,YAAY,CAAClC,GAAG,CAACmC,OAAQ,CAAC;QACvCyC,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF1V,SAAS,CAAC,MAAM,IAAI,CAACoR,YAAY,EAAE,CAAC,CACrC,CAACtB,SAAS,EAAE;EACf;EAGA6F,QAAQA,CAACpB,GAAQ;IACf,IAAIqB,OAAO,GAAkB;MAC3BvP,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CvC,UAAU,EAAE,IAAI,CAACmC,WAAW,CAACC,UAAU;MACvCiJ,QAAQ,EAAE,IAAI,CAAClJ,WAAW,CAACoP,GAAG;MAC9BpR,UAAU,EAAE,IAAI,CAACgC,WAAW,CAAChC,UAAU;MACvCC,SAAS,EAAE,IAAI,CAAC+B,WAAW,CAAC/B,SAAS;MACrCK,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7B2I,WAAW,EAAE,IAAI,CAACnJ,WAAW,CAACM,WAAW;MACzClC,UAAU,EAAE,IAAI,CAAC4B,WAAW,CAAC5B,UAAU;MACvCsC,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/B0I,SAAS,EAAE,IAAI,CAACpJ,WAAW,CAACoJ;KAC7B;IACD,IAAI,CAACzB,aAAa,CAAC6H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE+C;KACP,CAAC,CAAC9F,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACgC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjE,OAAO,CAACqF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEApN,OAAOA,CAACiM,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAAC7S,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAAC4K,MAAM,CAACiI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEA/S,4BAA4BA,CAAC6S,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACnI,MAAM,CAACiI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEA1S,cAAcA,CAACuR,IAAS;IACtB,IAAIoB,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACxI,aAAa,CAACyI,oCAAoC,CAAC;QACtDxD,IAAI,EAAEmC,IAAI,CAAC3R;OACZ,CAAC,CAACyM,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACgC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACjE,OAAO,CAACqF,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAuC,UAAUA,CAAA;IACR,IAAI,CAAC3H,KAAK,CAAC2I,KAAK,EAAE;IAClB,IAAI,CAAC3I,KAAK,CAAC4I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtQ,WAAW,CAACoP,GAAG,CAAC;IACnD,IAAI,CAAC1H,KAAK,CAAC4I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnB,kBAAkB,CAACtR,UAAU,CAAC;IACjE,IAAI,CAAC6J,KAAK,CAAC6I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACpB,kBAAkB,CAACtR,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAAC6J,KAAK,CAAC4I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACtQ,WAAW,CAAClC,MAAM,CAAC;IACpD,IAAI,CAAC4J,KAAK,CAAC6I,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAAC/O,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACsH,KAAK,CAAC8I,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACrB,kBAAkB,CAAC3O,KAAK,EAAE,IAAI,CAACsH,OAAO,CAAC2I,WAAW,CAAC;IACrF,IAAI,CAAC/I,KAAK,CAACgJ,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACvB,kBAAkB,CAACzO,MAAM,CAAC;IAClE,IAAI,CAACgH,KAAK,CAAC4I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnB,kBAAkB,CAAC/F,SAAS,CAAC;IAC9D,IAAI,CAAC1B,KAAK,CAAC4I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtR,cAAc,CAAC4B,kBAAkB,CAAC6H,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAAC4I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtR,cAAc,CAACC,kBAAkB,CAACwJ,KAAK,CAAC;IAC3E,IAAI,IAAI,CAACzI,WAAW,CAACwO,gBAAgB,EAAE;MACrC,IAAI,CAAC9G,KAAK,CAAC4I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACtQ,WAAW,CAAC0O,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAAC1O,WAAW,CAAC0O,cAAc,EAAE;MACnC,IAAI,CAAChH,KAAK,CAAC4I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACtQ,WAAW,CAACwO,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAC9G,KAAK,CAACiJ,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC3Q,WAAW,CAACwO,gBAAgB,GAAG,IAAI,CAACxO,WAAW,CAACwO,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACxO,WAAW,CAAC0O,cAAc,GAAG,IAAI,CAAC1O,WAAW,CAAC0O,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAAClJ,KAAK,CAAC2I,KAAK,EAAE;IAClB,IAAI,CAAC3I,KAAK,CAAC4I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxN,aAAa,CAAC0I,YAAY,CAAC;IAC5D,IAAI,CAAC9D,KAAK,CAAC4I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxN,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAAC2E,KAAK,CAAC6I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzN,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAAC2E,KAAK,CAACmJ,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC/N,aAAa,CAAChF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAC4J,KAAK,CAACmJ,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC/N,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAAC2L,GAAQ;IACvB,IAAI,CAACxL,aAAa,CAAC0I,YAAY,GAAG,IAAI,CAACvO,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACyT,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAAClJ,KAAK,CAAC4H,aAAa,CAACvJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC0B,OAAO,CAAC8H,aAAa,CAAC,IAAI,CAAC7H,KAAK,CAAC4H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC1H,qBAAqB,CAACkJ,yCAAyC,CAAC;MACnElE,IAAI,EAAE,IAAI,CAAC9J;KACZ,CAAC,CAAC2G,IAAI,CACLzP,GAAG,CAAC0P,GAAG,IAAG;MACR,IAAIA,GAAG,CAACgC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjE,OAAO,CAACqF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF1V,SAAS,CAAC,MAAM,IAAI,CAACoR,YAAY,EAAE,CAAC,CACrC,CAACtB,SAAS,EAAE;EACf,CAAC,CAAE;EACGlM,aAAaA,CAACoT,MAAW,EAAEhC,IAAS;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAAC/J,YAAY,GAAG8H,IAAI;MACxBiC,KAAI,CAAClL,cAAc,GAAG,EAAE;MACxBkL,KAAI,CAAC9J,WAAW,GAAG,CAAC;MACpB8J,KAAI,CAAC3H,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7B2H,KAAI,CAACnM,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACAmM,KAAI,CAAC9L,iBAAiB,GAAG,KAAK;MAC9B8L,KAAI,CAAC3L,uBAAuB,GAAG,CAAC;MAChC2L,KAAI,CAAC1L,mBAAmB,GAAG,CAAC;MAC5B0L,KAAI,CAAC7J,gBAAgB,GAAG,CAAC;MACzB6J,KAAI,CAACvK,mBAAmB,GAAG,KAAK;MAEhC;MACA,IAAI;QACF,MAAMyK,QAAQ,SAASF,KAAI,CAAC9I,gBAAgB,CAACiJ,qBAAqB,CAACpC,IAAI,CAAC3R,GAAG,CAAC,CAACgU,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAACxF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAC7D;UACAuF,KAAI,CAAC3H,kBAAkB,GAAG6H,QAAQ,CAACzF,OAAO,CAAC4F,YAAY,IAAI,CAAC;UAC5D;UACA,IAAIH,QAAQ,CAACzF,OAAO,CAAC6F,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7CN,KAAI,CAACnM,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACLmM,KAAI,CAACnM,mBAAmB,GAAG,IAAI;UACjC;UACA;UACA,IAAIqM,QAAQ,CAACzF,OAAO,CAAC8F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACzF,OAAO,CAAC8F,KAAK,CAAC,EAAE;YACnE;YACAP,KAAI,CAAClL,cAAc,GAAGoL,QAAQ,CAACzF,OAAO,CAAC8F,KAAK,CAACtE,GAAG,CAAEyE,KAAU,KAAM;cAChEC,QAAQ,EAAET,QAAQ,CAACzF,OAAO,CAACvC,QAAQ,IAAI6F,IAAI,CAAC3R,GAAG;cAC/CwU,YAAY,EAAEV,QAAQ,CAACzF,OAAO,CAAC4F,YAAY;cAC3CjN,SAAS,EAAEsN,KAAK,CAACG,SAAS,IAAI,EAAE;cAChCvN,UAAU,EAAEoN,KAAK,CAACI,UAAU,IAAI,CAAC;cACjCrN,MAAM,EAAEiN,KAAK,CAACK,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAEN,KAAK,CAACvD,OAAO,IAAI,CAAC;cAC3BxT,kBAAkB,EAAE+W,KAAK,CAAC/W,kBAAkB,IAAI+W,KAAK,CAAC/W,kBAAkB,GAAG,CAAC,GAAG+W,KAAK,CAAC/W,kBAAkB,GAAGA,kBAAkB,CAACsX,GAAG;cAChIC,gBAAgB,EAAER,KAAK,CAACJ;aACzB,CAAC,CAAC;YACHN,KAAI,CAACzM,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAO4N,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAnB,KAAI,CAACxJ,aAAa,CAACqH,IAAI,CAACkC,MAAM,EAAE;QAC9BsB,OAAO,EAAEtD,IAAI;QACbuD,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACA7M,kBAAkBA,CAAA;IAChB,IAAI,CAAC4D,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACvD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACjB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACqC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAAC7B,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACmB,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,CAACgB,OAAO,CAACqF,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACAvJ,gBAAgBA,CAAA;IACd,IAAI,CAACuC,cAAc,CAACyM,IAAI,CAAC;MACvBZ,QAAQ,EAAE,IAAI,CAAC1K,YAAY,EAAE7J,GAAG,IAAI,CAAC;MACrCgH,SAAS,EAAE,EAAE;MACbE,UAAU,EAAE,CAAC;MACbG,MAAM,EAAE,CAAC;MACTuN,OAAO,EAAE,CAAC;MACVrX,kBAAkB,EAAEA,kBAAkB,CAACsX;KACxC,CAAC;EACJ;EACA;EACMxO,gBAAgBA,CAAA;IAAA,IAAA+O,MAAA;IAAA,OAAAvB,iBAAA;MACpB,IAAI;QACF,IAAI,CAACuB,MAAI,CAACvL,YAAY,EAAE7J,GAAG,EAAE;UAC3BoV,MAAI,CAAC/K,OAAO,CAACmE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM6G,OAAO,GAAG;UACdjH,YAAY,EAAEgH,MAAI,CAACvL,YAAY,CAACuE,YAAY,IAAI,CAAC;UACjDtC,QAAQ,EAAEsJ,MAAI,CAACvL,YAAY,CAAC7J;SAC7B;QAED,MAAM8T,QAAQ,SAASsB,MAAI,CAACtK,gBAAgB,CAACzE,gBAAgB,CAACgP,OAAO,CAAC,CAACrB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEwB,OAAO,IAAIxB,QAAQ,CAACyB,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAG1B,QAAQ,CAACyB,IAAI,CAAC1F,GAAG,CAAE1C,CAAM,KAAM;YAClDqH,YAAY,EAAErH,CAAC,CAAC8G,YAAY;YAC5BM,QAAQ,EAAEa,MAAI,CAACvL,YAAY,EAAE7J,GAAG;YAChCgH,SAAS,EAAEmG,CAAC,CAACsH,SAAS;YACtBvN,UAAU,EAAEiG,CAAC,CAACuH,UAAU;YACxBrN,MAAM,EAAE8F,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC4D,OAAO;YAClBxT,kBAAkB,EAAEA,kBAAkB,CAACkY,IAAI,CAAC;WAC7C,CAAC,CAAC;UACHL,MAAI,CAAC1M,cAAc,CAACyM,IAAI,CAAC,GAAGK,YAAY,CAAC;UACzCJ,MAAI,CAACjO,cAAc,EAAE;UACrBiO,MAAI,CAAC/K,OAAO,CAACqF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL0F,MAAI,CAAC/K,OAAO,CAACmE,YAAY,CAACsF,QAAQ,EAAEzJ,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO0K,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCK,MAAI,CAAC/K,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACMjI,gBAAgBA,CAAA;IAAA,IAAAmP,MAAA;IAAA,OAAA7B,iBAAA;MACpB,IAAI;QACF,IAAI,CAAC6B,MAAI,CAAC7L,YAAY,EAAE7J,GAAG,EAAE;UAC3B0V,MAAI,CAACrL,OAAO,CAACmE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM6G,OAAO,GAAG;UACdjH,YAAY,EAAEsH,MAAI,CAAC7L,YAAY,CAACuE,YAAY,IAAI,CAAC;UACjDtC,QAAQ,EAAE4J,MAAI,CAAC7L,YAAY,CAAC7J;SAC7B;QAED,MAAM8T,QAAQ,SAAS4B,MAAI,CAAC5K,gBAAgB,CAACvE,gBAAgB,CAAC8O,OAAO,CAAC,CAACrB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAEwB,OAAO,IAAIxB,QAAQ,CAACyB,IAAI,EAAE;UACtC,MAAMI,YAAY,GAAG7B,QAAQ,CAACyB,IAAI,CAAC1F,GAAG,CAAE1C,CAAM,KAAM;YAClDqH,YAAY,EAAErH,CAAC,CAAC8G,YAAY;YAC5BM,QAAQ,EAAEmB,MAAI,CAAC7L,YAAY,EAAE7J,GAAG;YAChCgH,SAAS,EAAEmG,CAAC,CAACsH,SAAS;YACtBvN,UAAU,EAAEiG,CAAC,CAACuH,UAAU;YACxBrN,MAAM,EAAE8F,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC4D,OAAO;YAClBxT,kBAAkB,EAAEA,kBAAkB,CAACqY,EAAE,CAAC;WAC3C,CAAC,CAAC;UACHF,MAAI,CAAChN,cAAc,CAACyM,IAAI,CAAC,GAAGQ,YAAY,CAAC;UACzCD,MAAI,CAACvO,cAAc,EAAE;UACrBuO,MAAI,CAACrL,OAAO,CAACqF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLgG,MAAI,CAACrL,OAAO,CAACmE,YAAY,CAACsF,QAAQ,EAAEzJ,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO0K,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCW,MAAI,CAACrL,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACA5H,mBAAmBA,CAACD,KAAa;IAC/B,MAAMgL,IAAI,GAAG,IAAI,CAACjJ,cAAc,CAAC/B,KAAK,CAAC;IACvC,IAAI,CAAC+B,cAAc,CAACmN,MAAM,CAAClP,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAAC2C,WAAW,GAAG,IAAI,CAACpB,cAAc,CAACoN,MAAM,CAAC,CAACC,GAAG,EAAEpE,IAAI,KAAI;MAC1D,OAAOoE,GAAG,GAAIpE,IAAI,CAACzK,UAAU,GAAGyK,IAAI,CAACtK,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAAC2O,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC3M,mBAAmB,EAAE;MAC5B,IAAI,CAACnB,mBAAmB,GAAG+N,IAAI,CAACC,KAAK,CAAC,IAAI,CAACpM,WAAW,IAAI,IAAI,CAAC7B,uBAAuB,GAAG,GAAG,CAAC,CAAC;MAC9F,IAAI,CAAC8B,gBAAgB,GAAG,IAAI,CAACD,WAAW,GAAG,IAAI,CAAC5B,mBAAmB;IACrE,CAAC,MAAM;MACL,IAAI,CAACA,mBAAmB,GAAG,CAAC;MAC5B,IAAI,CAAC6B,gBAAgB,GAAG,IAAI,CAACD,WAAW;IAC1C;EACF;EAEA;EACApC,cAAcA,CAACyO,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAAC1E,MAAM,CAACqE,MAAM,CAAC;EACnB;EAEA;EACApO,mBAAmBA,CAAA;IACjB,IAAI,CAACiO,mBAAmB,EAAE;EAC5B;EAEA;EACMlN,aAAaA,CAACoI,GAAQ;IAAA,IAAAuF,MAAA;IAAA,OAAA5C,iBAAA;MAC1B,IAAI4C,MAAI,CAAC/N,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC8N,MAAI,CAACpM,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAMkI,YAAY,GAAGD,MAAI,CAAC/N,cAAc,CAACiO,MAAM,CAAChF,IAAI,IAClD,CAACA,IAAI,CAAC3K,SAAS,CAAC4P,IAAI,EAAE,CACvB;MAED,IAAIF,YAAY,CAAC/N,MAAM,GAAG,CAAC,EAAE;QAC3B8N,MAAI,CAACpM,OAAO,CAACmE,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAAE,IAAI;QACJ,MAAM6G,OAAO,GAAG;UACdvC,OAAO,EAAE2D,MAAI,CAAC5M,YAAY,CAAC7J,GAAG;UAC9B6W,KAAK,EAAEJ,MAAI,CAAC/N,cAAc;UAC1BoO,WAAW,EAAEL,MAAI,CAACxK,kBAAkB,CAAC;SACtC;QAED,MAAM6H,QAAQ,SAAS2C,MAAI,CAAC3L,gBAAgB,CAAChC,aAAa,CAACuM,OAAO,CAAC,CAACrB,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAEwB,OAAO,EAAE;UACrBmB,MAAI,CAACpM,OAAO,CAACqF,aAAa,CAAC,SAAS,CAAC;UACrCwB,GAAG,CAACmB,KAAK,EAAE;QACb,CAAC,MAAM;UACLoE,MAAI,CAACpM,OAAO,CAACmE,YAAY,CAACsF,QAAQ,EAAEzJ,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAO0K,KAAK,EAAE;QACd0B,MAAI,CAACpM,OAAO,CAACmE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMuI,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnD,iBAAA;MACnB,IAAI;QACF,MAAMoD,IAAI,SAA2BD,MAAI,CAAClM,gBAAgB,CAACiM,eAAe,CAACC,MAAI,CAACnN,YAAY,CAAC7J,GAAG,CAAC,CAACgU,SAAS,EAAE;QAC7G,IAAIiD,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAACnN,YAAY,CAACpJ,UAAU,IAAIuW,MAAI,CAACnN,YAAY,CAACnJ,MAAM,OAAO;UACtF4W,IAAI,CAACzI,KAAK,EAAE;UACZsI,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAAC3M,OAAO,CAACmE,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;QACdiC,MAAI,CAAC3M,OAAO,CAACmE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACAhF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACd,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC0B,OAAO,CAACmE,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAMoJ,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAGX,MAAM,CAAC1F,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAIqG,WAAW,EAAE;QACfA,WAAW,CAACP,QAAQ,CAAC9F,IAAI,EAAE;QAC3BqG,WAAW,CAACP,QAAQ,CAACQ,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACP,QAAQ,CAAClF,KAAK,EAAE;QAE5B;QACAyF,WAAW,CAACE,MAAM,GAAG;UACnBhH,UAAU,CAAC,MAAK;YACd8G,WAAW,CAACG,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAC5N,OAAO,CAACmE,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAC1K,OAAO,CAACmE,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQqJ,oBAAoBA,CAAA;IAC1B,MAAMK,WAAW,GAAG,IAAI7G,IAAI,EAAE,CAAC8G,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMC,aAAa,GAAG,IAAI,CAACvY,WAAW,CAACC,kBAAkB,EAAE9B,cAAc,IAAI,EAAE;IAE/E,IAAIqa,SAAS,GAAG,EAAE;IAClB,IAAI,CAAC3P,cAAc,CAAC4P,OAAO,CAAC,CAAC3G,IAAI,EAAEhL,KAAK,KAAI;MAC1C,MAAM4R,QAAQ,GAAG5G,IAAI,CAACzK,UAAU,GAAGyK,IAAI,CAACtK,MAAM;MAC9C,MAAMmR,aAAa,GAAG,IAAI,CAAC7Q,oBAAoB,CAACgK,IAAI,CAACpU,kBAAkB,CAAC;MACxE8a,SAAS,IAAI;;kFAE+D1R,KAAK,GAAG,CAAC;8DAC7BgL,IAAI,CAAC3K,SAAS;iFACK,IAAI,CAACU,cAAc,CAACiK,IAAI,CAACzK,UAAU,CAAC;kFACnCyK,IAAI,CAACtK,MAAM;iFACZ,IAAI,CAACK,cAAc,CAAC6Q,QAAQ,CAAC;kFAC5BC,aAAa;;OAExF;IACH,CAAC,CAAC;IAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAiJ4BJ,aAAa;;;;uCAIb,IAAI,CAACvO,YAAY,EAAEpJ,UAAU,IAAI,EAAE;;;;uCAInC,IAAI,CAACoJ,YAAY,EAAEnJ,MAAM,IAAI,EAAE;;;;uCAI/B,IAAI,CAACmJ,YAAY,EAAE7G,aAAa,IAAI,EAAE;;;;uCAItCkV,WAAW;;;;;;;;;;;;;;;;cAgBpCG,SAAS;;;;;;iBAMN,IAAI,CAAC3Q,cAAc,CAAC,IAAI,CAACoC,WAAW,CAAC;;YAE1C,IAAI,CAACT,mBAAmB,GAAG;;cAEzB,IAAI,CAACvB,iBAAiB,KAAK,IAAI,CAACG,uBAAuB,MAAM,IAAI,CAACP,cAAc,CAAC,IAAI,CAACQ,mBAAmB,CAAC;;WAE7G,GAAG,EAAE;;kBAEE,IAAI,CAACR,cAAc,CAAC,IAAI,CAACqC,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BA4B/B,IAAIsH,IAAI,EAAE,CAACoH,cAAc,CAAC,OAAO,CAAC;;;;KAI1D;EACH;EAEA;EACMhQ,aAAaA,CAACyI,GAAQ;IAAA,IAAAwH,MAAA;IAAA,OAAA7E,iBAAA;MAC1B,IAAI6E,MAAI,CAAChQ,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC+P,MAAI,CAACrO,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI,CAACkK,MAAI,CAACzM,kBAAkB,EAAE;QAC5ByM,MAAI,CAACrO,OAAO,CAACmE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAMsF,QAAQ,SAAS4E,MAAI,CAAC5N,gBAAgB,CAACrC,aAAa,CAACiQ,MAAI,CAACzM,kBAAkB,CAAC,CAAC+H,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAACwB,OAAO,EAAE;UACpBoD,MAAI,CAACrO,OAAO,CAACqF,aAAa,CAAC,UAAU,CAAC;UACtCsF,OAAO,CAAC2D,GAAG,CAAC,UAAU,EAAE;YACtB7B,WAAW,EAAE4B,MAAI,CAACzM,kBAAkB;YACpC5B,OAAO,EAAEyJ,QAAQ,CAACzJ;WACnB,CAAC;QACJ,CAAC,MAAM;UACLqO,MAAI,CAACrO,OAAO,CAACmE,YAAY,CAACsF,QAAQ,CAACzJ,OAAO,IAAI,SAAS,CAAC;UACxD2K,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEjB,QAAQ,CAACzJ,OAAO,CAAC;QAC7C;QAEA6G,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,CAAC,OAAO0C,KAAK,EAAE;QACd2D,MAAI,CAACrO,OAAO,CAACmE,YAAY,CAAC,SAAS,CAAC;QACpCwG,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACApN,oBAAoBA,CAAC6Q,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAKjb,kBAAkB,CAACkY,IAAI;QAC1B,OAAO,MAAM;MACf,KAAKlY,kBAAkB,CAACsX,GAAG;QACzB,OAAO,KAAK;MACd,KAAKtX,kBAAkB,CAACqY,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAgD,sBAAsBA,CAACC,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAKzb,mBAAmB,CAAC0b,GAAG;QAC1B,OAAO,KAAK;MACd,KAAK1b,mBAAmB,CAAC2b,GAAG;QAC1B,OAAO,KAAK;MACd,KAAK3b,mBAAmB,CAAC4b,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCA5sCWhP,4BAA4B,EAAAxM,EAAA,CAAAyb,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3b,EAAA,CAAAyb,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA7b,EAAA,CAAAyb,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA/b,EAAA,CAAAyb,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAjc,EAAA,CAAAyb,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAnc,EAAA,CAAAyb,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAArc,EAAA,CAAAyb,iBAAA,CAAAW,EAAA,CAAAE,oBAAA,GAAAtc,EAAA,CAAAyb,iBAAA,CAAAW,EAAA,CAAAG,gBAAA,GAAAvc,EAAA,CAAAyb,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAAzc,EAAA,CAAAyb,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAA3c,EAAA,CAAAyb,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAA7c,EAAA,CAAAyb,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAA/c,EAAA,CAAAyb,iBAAA,CAAAuB,GAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA5BzQ,4BAA4B;MAAA0Q,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC7DvCrd,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAsG,SAAA,qBAAiC;UACnCtG,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAA+D,gBAAA,2BAAAwZ,0EAAAtZ,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAC,kBAAA,EAAA2B,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAC,kBAAA,GAAA2B,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACtEjE,EAAA,CAAAgB,UAAA,4BAAAyc,2EAAA;YAAAzd,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAkB8b,GAAA,CAAAlK,0BAAA,EAA4B;UAAA,EAAC;UAC/CpT,EAAA,CAAAgC,UAAA,KAAA0b,kDAAA,wBAAoE;UAK1E1d,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAA4Z,0EAAA1Z,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAA2D,kBAAA,EAAA/B,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAA2D,kBAAA,GAAA/B,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACrDjE,EAAA,CAAAgC,UAAA,KAAA4b,kDAAA,wBAAgE;UAKtE5d,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAA+D,gBAAA,2BAAA8Z,sEAAA5Z,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAA0N,KAAA,EAAA9L,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAA0N,KAAA,GAAA9L,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA+B;UAC5FjE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAA+D,gBAAA,2BAAA+Z,sEAAA7Z,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAA2N,GAAA,EAAA/L,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAA2N,GAAA,GAAA/L,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA6B;UAGtFjE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAENH,EAAA,CAAAsG,SAAA,cAWM;UAIFtG,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAAga,0EAAA9Z,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAoN,kBAAA,EAAAxL,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAoN,kBAAA,GAAAxL,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACtEjE,EAAA,CAAAgC,UAAA,KAAAgc,kDAAA,wBAAgE;UAKtEhe,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAA+D,gBAAA,2BAAAka,0EAAAha,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAgC,kBAAA,EAAAJ,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAgC,kBAAA,GAAAJ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA4C;UACxEjE,EAAA,CAAAgC,UAAA,KAAAkc,kDAAA,wBAAgE;UAKtEle,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAA+D,gBAAA,2BAAAoa,0EAAAla,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAwC,iBAAA,EAAAZ,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAwC,iBAAA,GAAAZ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA2C;UACrEjE,EAAA,CAAAgC,UAAA,KAAAoc,kDAAA,wBAA+D;UAKrEpe,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAA+D,gBAAA,2BAAAsa,0EAAApa,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAyN,gBAAA,EAAA7L,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAyN,gBAAA,GAAA7L,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA0C;UACpEjE,EAAA,CAAAgC,UAAA,KAAAsc,kDAAA,wBAAgE;UAKtEte,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAA+D,gBAAA,2BAAAwa,0EAAAta,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAAjb,WAAA,CAAAuN,mBAAA,EAAA3L,MAAA,MAAAqZ,GAAA,CAAAjb,WAAA,CAAAuN,mBAAA,GAAA3L,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAA6C;UACzEjE,EAAA,CAAAgC,UAAA,KAAAwc,kDAAA,wBAAiE;UAKvExe,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAKFH,EAHJ,CAAAC,cAAA,cAAsB,eAC2B,kBAEiB;UAArBD,EAAA,CAAAgB,UAAA,mBAAAyd,+DAAA;YAAAze,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAS8b,GAAA,CAAAnN,QAAA,EAAU;UAAA,EAAC;UAC3DnQ,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAsG,SAAA,aAA6B;UAGtCtG,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAEJH,EADF,CAAAC,cAAA,eAAuB,eAC0B;UAC7CD,EAAA,CAAAgC,UAAA,KAAA0c,+CAAA,qBAAmG;UAGnG1e,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAgB,UAAA,mBAAA2d,+DAAA;YAAA3e,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAS8b,GAAA,CAAAtI,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClFhV,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAgB,UAAA,mBAAA4d,+DAAA;YAAA5e,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAS8b,GAAA,CAAA5M,WAAA,EAAa;UAAA,EAAC;UAC9D1Q,EAAA,CAAAE,MAAA,oDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAgB,UAAA,oBAAA6d,+DAAA5a,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAU8b,GAAA,CAAAhM,cAAA,CAAArN,MAAA,CAAsB;UAAA,EAAC;UAApFjE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAgB,UAAA,mBAAA8d,+DAAA;YAAA9e,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAS8b,GAAA,CAAApM,gBAAA,EAAkB;UAAA,EAAC;UAC9DlR,EAAA,CAAAE,MAAA,gEACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAOEH,EALR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cAErB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAgC,UAAA,KAAA+c,2CAAA,mBAAmD;UA6C3D/e,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAA+D,gBAAA,wBAAAib,6EAAA/a,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAAxd,EAAA,CAAAmE,kBAAA,CAAAmZ,GAAA,CAAA5P,SAAA,EAAAzJ,MAAA,MAAAqZ,GAAA,CAAA5P,SAAA,GAAAzJ,MAAA;YAAA,OAAAjE,EAAA,CAAAwB,WAAA,CAAAyC,MAAA;UAAA,EAAoB;UAClCjE,EAAA,CAAAgB,UAAA,wBAAAge,6EAAA/a,MAAA;YAAAjE,EAAA,CAAAkB,aAAA,CAAAsc,GAAA;YAAA,OAAAxd,EAAA,CAAAwB,WAAA,CAAc8b,GAAA,CAAA9M,WAAA,CAAAvM,MAAA,CAAmB;UAAA,EAAC;UAGxCjE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAuKVH,EArKA,CAAAgC,UAAA,MAAAid,qDAAA,gCAAAjf,EAAA,CAAAkf,sBAAA,CAAmE,MAAAC,qDAAA,iCAAAnf,EAAA,CAAAkf,sBAAA,CAsIF,MAAAE,qDAAA,kCAAApf,EAAA,CAAAkf,sBAAA,CA+BJ;;;UAxXvBlf,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAC,kBAAA,CAA4C;UAE1CtC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAA3W,oBAAA,CAAuB;UAS1C3G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAA2D,kBAAA,CAA4C;UACzBhG,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAA1W,gBAAA,CAAmB;UAYY5G,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAA0N,KAAA,CAA+B;UAKvC/P,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAA2N,GAAA,CAA6B;UAuBtDhQ,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAoN,kBAAA,CAA4C;UAC1CzP,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAArP,gBAAA,CAAmB;UAYnBjO,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAgC,kBAAA,CAA4C;UAC5CrE,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAA7Y,gBAAA,CAAmB;UAYrBzE,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAwC,iBAAA,CAA2C;UACzC7E,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAAvY,eAAA,CAAkB;UAWpB/E,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAyN,gBAAA,CAA0C;UACxC9P,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAAvP,gBAAA,CAAmB;UAYnB/N,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAAuE,gBAAA,YAAA+Y,GAAA,CAAAjb,WAAA,CAAAuN,mBAAA,CAA6C;UAC7C5P,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAApP,iBAAA,CAAoB;UAiBRlO,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAkd,GAAA,CAAA3V,QAAA,CAAc;UAqCnC3H,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAkd,GAAA,CAAApK,SAAA,CAAe;UA+C1BlT,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAuE,gBAAA,SAAA+Y,GAAA,CAAA5P,SAAA,CAAoB;UAAuB1N,EAAtB,CAAAI,UAAA,aAAAkd,GAAA,CAAA7P,QAAA,CAAqB,mBAAA6P,GAAA,CAAA3P,YAAA,CAAgC;;;qBD5JlF3O,YAAY,EAAAqgB,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAExgB,YAAY,EAAAygB,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,YAAA,EAAAN,GAAA,CAAAO,OAAA,EAAAjE,EAAA,CAAAkE,eAAA,EAAAlE,EAAA,CAAAmE,mBAAA,EAAAnE,EAAA,CAAAoE,qBAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAArE,EAAA,CAAAsE,mBAAA,EAAAtE,EAAA,CAAAuE,gBAAA,EAAAvE,EAAA,CAAAwE,iBAAA,EAAAxE,EAAA,CAAAyE,iBAAA,EAAAzE,EAAA,CAAA0E,oBAAA,EAAA1E,EAAA,CAAA2E,iBAAA,EAAA3E,EAAA,CAAA4E,eAAA,EAAA5E,EAAA,CAAA6E,qBAAA,EAAA7E,EAAA,CAAA8E,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAEjiB,kBAAkB,EAAEI,mBAAmB;MAAA8hB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}