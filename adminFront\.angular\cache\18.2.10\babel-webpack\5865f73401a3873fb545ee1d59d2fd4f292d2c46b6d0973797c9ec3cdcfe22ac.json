{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nlet DetailContentManagementSalesAccountComponent = class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      // 初始化模態框狀態\n      formItemReq.isModalOpen = true;\n      formItemReq.imageLoading = true;\n      formItemReq.showNavButtons = false;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n      // 設置焦點到模態框以支持鍵盤導航\n      setTimeout(() => {\n        const modalElement = document.querySelector('[tabindex=\"0\"]');\n        if (modalElement) {\n          modalElement.focus();\n        }\n      }, 100);\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n};\nDetailContentManagementSalesAccountComponent = __decorate([Component({\n  selector: 'ngx-detail-content-management-sales-account',\n  templateUrl: './detail-content-management-sales-account.component.html',\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe]\n})], DetailContentManagementSalesAccountComponent);\nexport { DetailContentManagementSalesAccountComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "NbCheckboxModule", "tap", "BaseFilePipe", "SharedModule", "BaseComponent", "Base64ImagePipe", "EnumHouseType", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "label", "value", "地主戶", "銷售戶", "CUiTypeOptions", "CRemarkTypeOptions", "selectedItems", "selectedRemarkType", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "detectFiles", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "listPictures", "length", "Date", "getTime", "name", "split", "data", "extension", "getFileExtension", "CFile", "push", "removeImage", "pictureId", "filter", "x", "renameFile", "index", "blob", "slice", "size", "type", "newFile", "File", "nextImage", "formItemReq", "CMatrialUrl", "currentImageIndex", "prevImage", "getCurrentImage", "undefined", "openImageModal", "imageIndex", "isModalOpen", "imageLoading", "showNavButtons", "setTimeout", "modalElement", "querySelector", "focus", "closeImageModal", "nextImageModal", "prevImageModal", "onKeydown", "key", "preventDefault", "onCheckAllChange", "checked", "allSelected", "houseHoldList", "for<PERSON>ach", "onCheckboxHouseHoldListChange", "every", "onCheckboxRemarkChange", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "CLocation", "CName", "<PERSON>art", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CDesignFileUrl", "CFormItemHouseHold", "CFormId", "CItemName", "CRequireAnswer", "CUiType", "selectedCUiType", "CSelectPicture", "x1", "CBase64", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "listFormItem", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "changeSelectCUiType", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, GetMaterialListResponse, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n  imageLoading?: boolean; // 圖片加載狀態\r\n  showNavButtons?: boolean; // 顯示導航按鈕\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number; override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      // 初始化模態框狀態\r\n      formItemReq.isModalOpen = true;\r\n      formItemReq.imageLoading = true;\r\n      formItemReq.showNavButtons = false;\r\n      \r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n      \r\n      // 設置焦點到模態框以支持鍵盤導航\r\n      setTimeout(() => {\r\n        const modalElement = document.querySelector('[tabindex=\"0\"]') as HTMLElement;\r\n        if (modalElement) {\r\n          modalElement.focus();\r\n        }\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64) : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAE1B,SAASC,YAAY,QAAQ,qCAAqC;AAGlE,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAE3E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;AAqC1D,IAAMC,4CAA4C,GAAlD,MAAMA,4CAA6C,SAAQH,aAAa;EAC7EI,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAElB,aAAa,CAACmB;IAAG,CAAE,EAC1C;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAElB,aAAa,CAACoB;IAAG,CAAE,CAC3C;IAeD,KAAAC,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EACD;MACEC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,EAAE;MACDC,KAAK,EAAE,CAAC;MAAED,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAK,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2CjC,KAAAC,aAAa,GAA+B,EAAE;IAC9C,KAAAC,kBAAkB,GAA+B,EAAE;IAgOnD,KAAAC,KAAK,GAAY,IAAI;EA7SrB;EAUA;EACA,IAAIC,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACX,kBAAkB,CAACY,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACT,KAAK,KAAK,IAAI,CAACL,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOY,MAAM,GAAG,QAAQA,MAAM,CAACV,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAY,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACT,KAAK,KAAKY,UAAU,CAAC,EAAE;MACvE,IAAI,CAACjB,iCAAiC,CAACE,WAAW,GAAGe,UAAU;IACjE;EACF;EAY8BE,QAAQA,CAAA;IACpC,IAAI,CAAC5B,KAAK,CAAC6B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QAErB,IAAI,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAAClC,OAAO,CAACsC,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACxC,KAAK,CAACyC,WAAW,CAACX,SAAS,CAACW,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAAChB,cAAc,CAACiB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAClC,KAAU,EAAEmC,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAACpC,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOoC,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQAC,WAAWA,CAACC,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAACU,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCX,YAAY,CAACU,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7B7B,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAClE,eAAe,CAACmE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAACU,YAAY,CAACU,IAAI,CAAC;YAC7BvC,EAAE,EAAE,IAAI+B,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBC,IAAI,EAAEb,IAAI,CAACa,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BC,IAAI,EAAER,SAAS;YACfS,SAAS,EAAE,IAAI,CAAClE,eAAe,CAACmE,gBAAgB,CAACjB,IAAI,CAACa,IAAI,CAAC;YAC3DK,KAAK,EAAElB;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACzC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA4D,WAAWA,CAACC,SAAiB,EAAEtB,YAAiB;IAC9C,IAAIA,YAAY,CAACU,YAAY,CAACC,MAAM,EAAE;MACpCX,YAAY,CAACU,YAAY,GAAGV,YAAY,CAACU,YAAY,CAACa,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC3C,EAAE,IAAIyC,SAAS,CAAC;IAC7F;EACF;EACAG,UAAUA,CAAC1B,KAAU,EAAE2B,KAAa,EAAE1B,YAAiB;IACrD,IAAI2B,IAAI,GAAG3B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACS,KAAK,CAAC,CAAC,EAAE5B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACU,IAAI,EAAE7B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG5B,KAAK,CAACG,MAAM,CAACzC,KAAK,GAAG,GAAG,GAAGuC,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACT,SAAS,EAAE,EAAE;MAAEa,IAAI,EAAE9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,CAACW;IAAI,CAAE,CAAC;IACjK9B,YAAY,CAACU,YAAY,CAACgB,KAAK,CAAC,CAACP,KAAK,GAAGY,OAAO;EAClD;EAEA;EACAE,SAASA,CAACC,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAG,CAACF,WAAW,CAACE,iBAAiB,GAAG,CAAC,IAAIF,WAAW,CAACC,WAAW,CAACxB,MAAM;IACtG;EACF;EAEA0B,SAASA,CAACH,WAAgB;IACxB,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjEuB,WAAW,CAACE,iBAAiB,GAAGF,WAAW,CAACE,iBAAiB,KAAK,CAAC,GAC/DF,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,GAClCuB,WAAW,CAACE,iBAAiB,GAAG,CAAC;IACvC;EACF;EACAE,eAAeA,CAACJ,WAAgB;IAC9B,IAAIA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,IAAIuB,WAAW,CAACE,iBAAiB,KAAKG,SAAS,EAAE;MAChH,OAAOL,WAAW,CAACC,WAAW,CAACD,WAAW,CAACE,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EACA;EACAI,cAAcA,CAACN,WAAgB,EAAEO,UAAmB;IAClD,IAAIP,WAAW,CAACC,WAAW,IAAID,WAAW,CAACC,WAAW,CAACxB,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI8B,UAAU,KAAKF,SAAS,EAAE;QAC5BL,WAAW,CAACE,iBAAiB,GAAGK,UAAU;MAC5C;MACA;MACAP,WAAW,CAACQ,WAAW,GAAG,IAAI;MAC9BR,WAAW,CAACS,YAAY,GAAG,IAAI;MAC/BT,WAAW,CAACU,cAAc,GAAG,KAAK;MAElC;MACArD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;MAEvC;MACAmD,UAAU,CAAC,MAAK;QACd,MAAMC,YAAY,GAAGvD,QAAQ,CAACwD,aAAa,CAAC,gBAAgB,CAAgB;QAC5E,IAAID,YAAY,EAAE;UAChBA,YAAY,CAACE,KAAK,EAAE;QACtB;MACF,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEAC,eAAeA,CAACf,WAAgB;IAC9BA,WAAW,CAACQ,WAAW,GAAG,KAAK;IAC/B;IACAnD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACAwD,cAAcA,CAAChB,WAAgB;IAC7B,IAAI,CAACD,SAAS,CAACC,WAAW,CAAC;EAC7B;EAEAiB,cAAcA,CAACjB,WAAgB;IAC7B,IAAI,CAACG,SAAS,CAACH,WAAW,CAAC;EAC7B;EAEA;EACAkB,SAASA,CAACrD,KAAoB,EAAEmC,WAAgB;IAC9C,IAAIA,WAAW,CAACQ,WAAW,EAAE;MAC3B,QAAQ3C,KAAK,CAACsD,GAAG;QACf,KAAK,WAAW;UACdtD,KAAK,CAACuD,cAAc,EAAE;UACtB,IAAI,CAACH,cAAc,CAACjB,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACfnC,KAAK,CAACuD,cAAc,EAAE;UACtB,IAAI,CAACJ,cAAc,CAAChB,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACXnC,KAAK,CAACuD,cAAc,EAAE;UACtB,IAAI,CAACL,eAAe,CAACf,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEAqB,gBAAgBA,CAACC,OAAgB,EAAExD,YAAiB;IAClDA,YAAY,CAACyD,WAAW,GAAGD,OAAO;IAClC,IAAI,CAACE,aAAa,CAACC,OAAO,CAAC9D,IAAI,IAAG;MAChCG,YAAY,CAAClC,aAAa,CAAC+B,IAAI,CAAC,GAAG2D,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEAI,6BAA6BA,CAACJ,OAAgB,EAAE3D,IAAY,EAAEG,YAAiB;IAC7E,IAAIwD,OAAO,EAAE;MACXxD,YAAY,CAAClC,aAAa,CAAC+B,IAAI,CAAC,GAAG2D,OAAO;MAC1CxD,YAAY,CAACyD,WAAW,GAAG,IAAI,CAACC,aAAa,CAACG,KAAK,CAAChE,IAAI,IAAIG,YAAY,CAAClC,aAAa,CAAC+B,IAAI,CAAC,IAAI2D,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLxD,YAAY,CAACyD,WAAW,GAAG,KAAK;IAClC;EACF;EAIAK,sBAAsBA,CAACN,OAAgB,EAAE3D,IAAY,EAAEG,YAAiB;IACtEA,YAAY,CAACjC,kBAAkB,CAAC8B,IAAI,CAAC,GAAG2D,OAAO;EACjD;EAEAO,kBAAkBA,CAAClG,kBAA4B,EAAEmG,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAM/F,MAAM,IAAIL,kBAAkB,EAAE;MACvCoG,YAAY,CAAC/F,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMgG,WAAW,GAAGF,WAAW,CAACjD,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMe,IAAI,IAAIoC,WAAW,EAAE;MAC9B,IAAIrG,kBAAkB,CAACsG,QAAQ,CAACrC,IAAI,CAAC,EAAE;QACrCmC,YAAY,CAACnC,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOmC,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACV,OAAO,CAAC9D,IAAI,IAAG;MACnB,MAAMwD,GAAG,GAAG,GAAGxD,IAAI,CAAC2E,SAAS,IAAI3E,IAAI,CAAC4E,KAAK,IAAI5E,IAAI,CAAC6E,KAAK,EAAE;MAC3D,IAAIJ,GAAG,CAACK,GAAG,CAACtB,GAAG,CAAC,EAAE;QAChB,MAAMuB,QAAQ,GAAGN,GAAG,CAAC1F,GAAG,CAACyE,GAAG,CAAE;QAC9BuB,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLP,GAAG,CAACQ,GAAG,CAACzB,GAAG,EAAE;UAAExD,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEgF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACV,GAAG,CAACW,MAAM,EAAE,CAAC,CAACX,GAAG,CAAC,CAAC;MAAEzE,IAAI;MAAEgF;IAAK,CAAE,MAAM;MACxD,GAAGhF,IAAI;MACPqF,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAACjI,gBAAgB,CAACkI,mCAAmC,CAAC;MACxD5F,IAAI,EAAE;QACJ6F,YAAY,EAAE,IAAI,CAACvG,WAAW;QAC9BwG,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLrJ,GAAG,CAACsJ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAAChC,aAAa,CAACC,OAAO,CAAC9D,IAAI,IAAI,IAAI,CAAC/B,aAAa,CAAC+B,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAChC,kBAAkB,CAAC8F,OAAO,CAAC9D,IAAI,IAAI,IAAI,CAAC9B,kBAAkB,CAAC8B,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC8F,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACnB,GAAG,CAAEsB,CAA0B,IAAI;UACvE,OAAO;YACLC,cAAc,EAAE,IAAI;YACpBC,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbvB,SAAS,EAAEoB,CAAC,CAACpB,SAAS;YACtBC,KAAK,EAAEmB,CAAC,CAACnB,KAAK;YACdC,KAAK,EAAEkB,CAAC,CAAClB,KAAK;YACdsB,SAAS,EAAE,GAAGJ,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAAClB,KAAK,IAAIkB,CAAC,CAACpB,SAAS,EAAE;YACjDR,WAAW,EAAE,IAAI;YACjBkB,YAAY,EAAE,CAAC;YACfe,cAAc,EAAE,CAAC;YACjBC,OAAO,EAAE,CAAC;YAAEpI,aAAa,EAAE,EAAE;YAC7BC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3C0F,WAAW,EAAE,KAAK;YAClB/C,YAAY,EAAE,EAAE;YAAEyF,eAAe,EAAE,IAAI,CAACvI,cAAc,CAAC,CAAC,CAAC;YACzDwE,iBAAiB,EAAE,CAAC;YACpBM,WAAW,EAAE,KAAK;YAClBP,WAAW,EAAEyD,CAAC,CAACQ,cAAc,GAAGR,CAAC,CAACQ,cAAc,CAAC9B,GAAG,CAAC+B,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,GAAG;WAC1E;QACH,CAAC,CAAC;QACF,IAAI,CAACX,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACvB,UAAU,CAAC,IAAI,CAACuB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAClH,SAAS,EAAE;EACf;EAKA8H,eAAeA,CAAA;IACb,IAAI,CAAC1J,gBAAgB,CAAC2J,mCAAmC,CAAC;MACxDhH,IAAI,EAAE;QACJ6F,YAAY,EAAE,IAAI,CAACvG,WAAW;QAC9BzB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DoJ,SAAS,EAAE;;KAEd,CAAC,CAAClB,IAAI,CACLrJ,GAAG,CAACsJ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACgB,YAAY,GAAGlB,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACzH,KAAK,GAAGwH,GAAG,CAACC,OAAO,CAACkB,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAInB,GAAG,CAACC,OAAO,CAACkB,SAAS,EAAE;UACzB,IAAI,CAACjD,aAAa,CAACC,OAAO,CAAC9D,IAAI,IAAI,IAAI,CAAC/B,aAAa,CAAC+B,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAChC,kBAAkB,CAAC8F,OAAO,CAAC9D,IAAI,IAAI,IAAI,CAAC9B,kBAAkB,CAAC8B,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC8F,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACkB,SAAS,CAACrC,GAAG,CAAEsB,CAAM,IAAI;YAC7D,OAAO;cACLG,OAAO,EAAE,IAAI,CAACW,YAAY,CAACX,OAAO;cAClCF,cAAc,EAAED,CAAC,CAACC,cAAc;cAChC1D,WAAW,EAAEyD,CAAC,CAACzD,WAAW,KAAKyD,CAAC,CAACgB,gBAAgB,GAAG,CAAChB,CAAC,CAACgB,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EzF,KAAK,EAAEyE,CAAC,CAACzE,KAAK;cACd2E,kBAAkB,EAAEF,CAAC,CAACE,kBAAkB;cACxCe,WAAW,EAAEjB,CAAC,CAACiB,WAAW;cAC1BrC,SAAS,EAAEoB,CAAC,CAACpB,SAAS;cACtBC,KAAK,EAAEmB,CAAC,CAACnB,KAAK;cACdC,KAAK,EAAEkB,CAAC,CAAClB,KAAK;cACdsB,SAAS,EAAEJ,CAAC,CAACI,SAAS,GAAGJ,CAAC,CAACI,SAAS,GAAG,GAAGJ,CAAC,CAACnB,KAAK,IAAImB,CAAC,CAAClB,KAAK,IAAIkB,CAAC,CAACpB,SAAS,EAAE;cAC7ER,WAAW,EAAE4B,CAAC,CAAC5B,WAAW;cAC1BkB,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5Be,cAAc,EAAEL,CAAC,CAACM,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGN,CAAC,CAACK,cAAc;cACtDC,OAAO,EAAEN,CAAC,CAACM,OAAO;cAClBpI,aAAa,EAAE8H,CAAC,CAACkB,qBAAqB,CAACnG,MAAM,GAAG,IAAI,CAACoG,0BAA0B,CAAC,IAAI,CAACrD,aAAa,EAAEkC,CAAC,CAACkB,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAChJ;cAAa,CAAE;cAAEC,kBAAkB,EAAE6H,CAAC,CAAC5B,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAClG,kBAAkB,EAAE+H,CAAC,CAAC5B,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACjG;cAAkB,CAAE;cAC9R0F,WAAW,EAAEmC,CAAC,CAACkB,qBAAqB,CAACnG,MAAM,KAAK,IAAI,CAAC+C,aAAa,CAAC/C,MAAM;cACzED,YAAY,EAAE,EAAE;cAAEyF,eAAe,EAAEP,CAAC,CAACM,OAAO,GAAG,IAAI,CAACvG,cAAc,CAACiG,CAAC,CAACM,OAAO,EAAE,IAAI,CAACtI,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HwE,iBAAiB,EAAE,CAAC;cACpBM,WAAW,EAAE;aACd;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACyC,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC1G,SAAS,EAAE;EACf;EAEAuI,mBAAmBA,CAAC9E,WAAgB;IAClC,IAAIA,WAAW,CAACiE,eAAe,IAAIjE,WAAW,CAACiE,eAAe,CAAC1I,KAAK,KAAK,CAAC,EAAE;MAC1EyE,WAAW,CAAC+D,cAAc,GAAG,CAAC;IAChC;EACF;EACAgB,4BAA4BA,CAACjG,IAAW;IACtC,KAAK,IAAInB,IAAI,IAAImB,IAAI,EAAE;MACrB,IAAInB,IAAI,CAACvC,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOuC,IAAI,CAACqH,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC7F,MAAM,CAAC8B,GAAG,IAAI+D,GAAG,CAAC/D,GAAG,CAAC,CAAC;EACjD;EAEAkE,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB7F,MAAM,CAAC8B,GAAG,IAAI+D,GAAG,CAAC/D,GAAG,CAAC,CAAC,CACvBmE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACtB,eAAoB,EAAEpI,kBAAuB;IAC1D,IAAIoI,eAAe,IAAIA,eAAe,CAAC1I,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC8J,0BAA0B,CAACxJ,kBAAkB,CAAC;IAC5D;EACF;EAEA2J,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAAC5G,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI6G,KAAK,CAACjH,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOiH,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAACnH,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLmH,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAChH,YAAY,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,IAAI;QACpE+G,aAAa,EAAErH,YAAY,CAAC,CAAC,CAAC,CAACO,SAAS,IAAI,IAAI;QAChD+G,QAAQ,EAAEtH,YAAY,CAAC,CAAC,CAAC,CAACS,KAAK,CAACL,IAAI,IAAIJ,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOyB,SAAS;EAEzB;EAGA0F,UAAUA,CAAA;IACR,IAAI,CAACjL,KAAK,CAACkL,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMxI,IAAI,IAAI,IAAI,CAACyI,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAACtI,IAAI,CAACqG,OAAQ,EAAE;QACzCiC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAACvI,IAAI,CAACoG,cAAe,EAAE;QACvDmC,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAIvI,IAAI,CAACqF,YAAY,IAAIrF,IAAI,CAACoG,cAAc,EAAE;QAC5C,IAAIpG,IAAI,CAACoG,cAAc,GAAGpG,IAAI,CAACqF,YAAY,IAAIrF,IAAI,CAACoG,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACjJ,KAAK,CAACuL,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG1I,IAAI,CAACqF,YAAY,GAAG,KAAKrF,IAAI,CAACmG,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACqC,kBAAkB,IAAK,CAACxI,IAAI,CAACmG,SAAU,EAAE;QAC5CqC,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACnL,KAAK,CAACuL,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACpL,KAAK,CAACuL,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACrL,KAAK,CAACuL,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC3C,kBAAkB,CAACrB,GAAG,CAAEmE,CAAM,IAAI;MAChE,OAAO;QACL5C,cAAc,EAAE4C,CAAC,CAAC5C,cAAc,GAAG4C,CAAC,CAAC5C,cAAc,GAAG,IAAI;QAC1D1E,KAAK,EAAEsH,CAAC,CAAC/H,YAAY,GAAG,IAAI,CAACmH,UAAU,CAACY,CAAC,CAAC/H,YAAY,CAAC,GAAG6B,SAAS;QACnEuD,kBAAkB,EAAE,IAAI,CAACqB,oBAAoB,CAACsB,CAAC,CAAC3K,aAAa,CAAC;QAC9D+I,WAAW,EAAE4B,CAAC,CAAC5B,WAAW,GAAG4B,CAAC,CAAC5B,WAAW,GAAG,IAAI;QACjD6B,OAAO,EAAE,IAAI,CAAC1K,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC0I,YAAY,CAACX,OAAO;QACtDtB,KAAK,EAAEgE,CAAC,CAAChE,KAAK;QACdC,KAAK,EAAE+D,CAAC,CAAC/D,KAAK;QACdF,SAAS,EAAEiE,CAAC,CAACjE,SAAS;QACtBwB,SAAS,EAAEyC,CAAC,CAACzC,SAAS;QAAE;QACxBhC,WAAW,EAAEyE,CAAC,CAACtC,eAAe,CAAC1I,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACgK,cAAc,CAACgB,CAAC,CAACtC,eAAe,EAAEsC,CAAC,CAAC1K,kBAAkB,CAAC,IAAI,IAAI;QACxHmH,YAAY,EAAEuD,CAAC,CAACvD,YAAY;QAC5Be,cAAc,EAAEwC,CAAC,CAACxC,cAAc;QAChCC,OAAO,EAAEuC,CAAC,CAACtC,eAAe,CAAC1I;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACwK,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjL,KAAK,CAAC2L,aAAa,CAAChI,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC/D,OAAO,CAACgM,aAAa,CAAC,IAAI,CAAC5L,KAAK,CAAC2L,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC3K,KAAK,EAAE;MACd,IAAI,CAAC6K,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACjM,gBAAgB,CAACkM,oCAAoC,CAAC;MACzDvJ,IAAI,EAAE,IAAI,CAAC8I;KACZ,CAAC,CAAC7J,SAAS,CAAC+G,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9I,OAAO,CAACoM,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAC7J,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIA0J,kBAAkBA,CAAA;IAChB,IAAI,CAACI,iBAAiB,GAAG;MACvB5D,YAAY,EAAE,IAAI,CAACvG,WAAW;MAC9BoK,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3CjL,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACsM,sCAAsC,CAAC;MAC3D3J,IAAI,EAAE,IAAI,CAACyJ;KACZ,CAAC,CAACxK,SAAS,CAAC+G,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9I,OAAO,CAACoM,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAAC7J,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA4H,0BAA0BA,CAACqC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMzJ,IAAI,IAAIuJ,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAAClL,IAAI,CAACqL,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK5J,IAAI,IAAI2J,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAACzJ,IAAI,CAAC,GAAG,CAAC,CAAC0J,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFvK,iCAAiCA,CAAA;IAC/B,IAAI,CAACjC,yBAAyB,CAAC6M,8DAA8D,CAAC;MAC5FnK,IAAI,EAAE,IAAI,CAACV;KACZ,CAAC,CAACyG,IAAI,CACLrJ,GAAG,CAACsJ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChC,aAAa,GAAG,IAAI,CAACuD,4BAA4B,CAACzB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACc,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC9H,SAAS,EAAE;EACf;EACAU,MAAMA,CAAA;IACJ,IAAI,CAAChC,aAAa,CAACiE,IAAI,CAAC;MACtBwI,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC/K;KACf,CAAC;IACF,IAAI,CAAC7B,QAAQ,CAAC6M,IAAI,EAAE;EACtB;CACD;AAjiBYtN,4CAA4C,GAAAuN,UAAA,EARxDhO,SAAS,CAAC;EACTiO,QAAQ,EAAE,6CAA6C;EACvDC,WAAW,EAAE,0DAA0D;EACvEC,SAAS,EAAE,CAAC,0DAA0D,CAAC;EACvEC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpO,YAAY,EAAEI,YAAY,EAAEH,gBAAgB,EAAEE,YAAY,EAAEG,eAAe;CACtF,CAAC,C,EAEWE,4CAA4C,CAiiBxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}