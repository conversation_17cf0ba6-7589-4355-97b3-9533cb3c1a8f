{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_3_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 39);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 29)(2, \"div\", 13);\n    i0.ɵɵtemplate(3, DetailContentManagementLandownerComponent_ng_container_8_div_3_img_3_Template, 2, 3, \"img\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2 == null ? null : formItemReq_r2.CFirstMatrialUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\")(2, \"td\", 41)(3, \"label\");\n    i0.ɵɵtext(4, \"\\u6587\\u4EF6\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00A0 \");\n    i0.ɵɵelementStart(6, \"input\", 42);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template_input_blur_6_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.renameFile($event, i_r7, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template_button_click_7_listener() {\n      const picture_r8 = i0.ɵɵrestoreView(_r6).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeImage(picture_r8.id, formItemReq_r2));\n    });\n    i0.ɵɵtext(8, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"img\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const picture_r8 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", picture_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", picture_r8.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_34_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n    i0.ɵɵpipe(1, \"addBaseFile\");\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, formItemReq_r2.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 29)(2, \"div\", 46);\n    i0.ɵɵtemplate(3, DetailContentManagementLandownerComponent_ng_container_8_div_34_img_3_Template, 2, 3, \"img\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"hidden\" : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_label_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 50);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.allSelected, $event) || (formItemReq_r2.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckAllChange($event, formItemReq_r2));\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_label_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 49)(1, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedItems[item_r11], $event) || (formItemReq_r2.selectedItems[item_r11] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxHouseHoldListChange($event, item_r11, formItemReq_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedItems[item_r11]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r11, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 51);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedRemarkType[remark_r13], $event) || (formItemReq_r2.selectedRemarkType[remark_r13] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const remark_r13 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxRemarkChange($event, remark_r13, formItemReq_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r13 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r2.selectedRemarkType[remark_r13]);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", remark_r13, \" \");\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 49);\n    i0.ɵɵtemplate(1, DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template, 2, 3, \"nb-checkbox\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedRemarkType);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 23)(2, \"label\", 32);\n    i0.ɵɵtext(3, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 33);\n    i0.ɵɵtemplate(5, DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_Template, 2, 1, \"label\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementLandownerComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵtemplate(3, DetailContentManagementLandownerComponent_ng_container_8_div_3_Template, 4, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14)(7, \"label\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"input\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_8_Template_input_ngModelChange_10_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CItemName, $event) || (formItemReq_r2.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"div\", 14)(13, \"label\", 18);\n    i0.ɵɵtext(14, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_8_Template_input_ngModelChange_15_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.CRequireAnswer, $event) || (formItemReq_r2.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"div\", 14)(18, \"label\", 20);\n    i0.ɵɵtext(19, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"nb-select\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementLandownerComponent_ng_container_8_Template_nb_select_ngModelChange_20_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r2.selectedCUiType, $event) || (formItemReq_r2.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementLandownerComponent_ng_container_8_Template_nb_select_selectedChange_20_listener() {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeSelectCUiType(formItemReq_r2));\n    });\n    i0.ɵɵtemplate(21, DetailContentManagementLandownerComponent_ng_container_8_nb_option_21_Template, 2, 2, \"nb-option\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 23)(23, \"div\", 24)(24, \"div\", 25)(25, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_ng_container_8_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r5 = i0.ɵɵreference(29);\n      return i0.ɵɵresetView(inputFile_r5.click());\n    });\n    i0.ɵɵtext(26, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"input\", 28, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementLandownerComponent_ng_container_8_Template_input_change_28_listener($event) {\n      const formItemReq_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event, formItemReq_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 29)(31, \"table\", 30)(32, \"tbody\");\n    i0.ɵɵtemplate(33, DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template, 11, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(34, DetailContentManagementLandownerComponent_ng_container_8_div_34_Template, 4, 2, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 9)(36, \"div\", 23)(37, \"label\", 32);\n    i0.ɵɵtext(38, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 33);\n    i0.ɵɵtemplate(40, DetailContentManagementLandownerComponent_ng_container_8_label_40_Template, 3, 2, \"label\", 34)(41, DetailContentManagementLandownerComponent_ng_container_8_label_41_Template, 3, 3, \"label\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, DetailContentManagementLandownerComponent_ng_container_8_div_42_Template, 6, 1, \"div\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_11_0;\n    const formItemReq_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CFirstMatrialUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r2.CName, \"-\", formItemReq_r2.CPart, \"-\", formItemReq_r2.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_7_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_7_0 !== undefined ? tmp_7_0 : false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r2.selectedCUiType.value === 3 || ((tmp_9_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_9_0 !== undefined ? tmp_9_0 : false));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r2.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_11_0 = ctx_r2.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.CUiTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", formItemReq_r2.listPictures.length ? \"\" : \"hidden\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r2.listPictures);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.CDesignFileUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseHoldList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r2.selectedCUiType.value === 3);\n  }\n}\nexport class DetailContentManagementLandownerComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId) {\n          this.getListRegularNoticeFileHouseHold();\n        }\n      }\n    });\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0]\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementLandowner.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  // formatParam() {\n  //   const result: SaveListFormItemReq[] =\n  //   })\n  //   return result\n  // }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        // ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementLandowner.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementLandownerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementLandownerComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementLandownerComponent,\n      selectors: [[\"ngx-detail-content-management-landowner\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 1,\n      consts: [[\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"font-bold\", \"text-lg\", \"pb-3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"flex\", \"flex-wrap\", \"w-full\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\", \"pb-2\"], [\"class\", \"flex flex-col items-center\", 4, \"ngIf\"], [1, \"w-full\", \"md:w-1/2\", \"px-2\"], [1, \"w-full\"], [1, \"form-group\", \"flex\", \"items-center\", \"w-full\"], [\"for\", \"CItemName\", 1, \"label\", \"w-1/2\", \"text-base\"], [1, \"input-group\", \"items-center\", \"w-1/2\", \"px-0\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5EDA\\u623F\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cRequireAnswer\", 1, \"label\", \"w-1/2\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u5FC5\\u586B\\u6578\\u91CF\", 1, \"w-1/2\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"buildingName\", 1, \"label\", \"w-1/2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-1/2\", \"px-0\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"w-full\", \"md:w-1/4\", \"px-2\"], [1, \"w-full\", \"flex\", \"flex-col\"], [1, \"flex\", \"justify-end\", \"w-full\"], [1, \"btn\", \"btn-info\", \"h-fit\", 3, \"click\", \"disabled\"], [1, \"w-full\", 3, \"ngClass\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"class\", \"w-full text-center\", 3, \"ngClass\", 4, \"ngIf\"], [\"for\", \"buildingName\", 1, \"label\"], [1, \"w-full\", \"md:w-3/4\", \"px-2\"], [\"class\", \"mr-2\", 4, \"ngIf\"], [\"class\", \"mr-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"flex flex-wrap w-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"items-center\"], [\"class\", \"h-[140px] w-[300px]\", 3, \"src\", 4, \"ngIf\"], [1, \"h-[140px]\", \"w-[300px]\", 3, \"src\"], [3, \"value\"], [1, \"align-middle\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"h-28\", \"w-40\", 3, \"src\"], [1, \"w-full\", \"text-center\", 3, \"ngClass\"], [1, \"w-full\", \"justify-items-end\"], [\"class\", \"h-32 w-32\", 3, \"src\", 4, \"ngIf\"], [1, \"h-32\", \"w-32\", 3, \"src\"], [1, \"mr-2\"], [3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checkedChange\", \"checked\", \"disabled\"], [\"value\", \"item\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"]],\n      template: function DetailContentManagementLandownerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 2);\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"h4\", 4);\n          i0.ɵɵtext(7, \"\\u985E\\u578B-\\u7368\\u7ACB\\u9078\\u6A23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, DetailContentManagementLandownerComponent_ng_container_8_Template, 43, 18, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"nb-card-footer\", 6)(10, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_10_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(11, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementLandownerComponent_Template_button_click_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(13, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgClass, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci9kZXRhaWwtY29udGVudC1tYW5hZ2VtZW50LWxhbmRvd25lci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0Esd01BQXdNIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbCheckboxModule", "BaseComponent", "tap", "EEvent", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "formItemReq_r2", "CFirstMatrialUrl", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "DetailContentManagementLandownerComponent_ng_container_8_div_3_img_3_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtext", "case_r4", "ɵɵtextInterpolate1", "label", "ɵɵelementContainerStart", "ɵɵlistener", "DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template_input_blur_6_listener", "$event", "i_r7", "ɵɵrestoreView", "_r6", "index", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "renameFile", "DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template_button_click_7_listener", "picture_r8", "removeImage", "id", "name", "data", "CDesignFileUrl", "DetailContentManagementLandownerComponent_ng_container_8_div_34_img_3_Template", "listPictures", "length", "ɵɵtwoWayListener", "DetailContentManagementLandownerComponent_ng_container_8_label_40_Template_nb_checkbox_checkedChange_1_listener", "_r9", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "listFormItem", "CIsLock", "DetailContentManagementLandownerComponent_ng_container_8_label_41_Template_nb_checkbox_checkedChange_1_listener", "item_r11", "_r10", "selectedItems", "onCheckboxHouseHoldListChange", "DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r12", "remark_r13", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_nb_checkbox_1_Template", "DetailContentManagementLandownerComponent_ng_container_8_div_42_label_5_Template", "CRemarkTypeOptions", "DetailContentManagementLandownerComponent_ng_container_8_div_3_Template", "DetailContentManagementLandownerComponent_ng_container_8_Template_input_ngModelChange_10_listener", "_r1", "CItemName", "DetailContentManagementLandownerComponent_ng_container_8_Template_input_ngModelChange_15_listener", "CRequireAnswer", "DetailContentManagementLandownerComponent_ng_container_8_Template_nb_select_ngModelChange_20_listener", "selectedCUiType", "DetailContentManagementLandownerComponent_ng_container_8_Template_nb_select_selectedChange_20_listener", "changeSelectCUiType", "DetailContentManagementLandownerComponent_ng_container_8_nb_option_21_Template", "DetailContentManagementLandownerComponent_ng_container_8_Template_button_click_25_listener", "inputFile_r5", "ɵɵreference", "click", "DetailContentManagementLandownerComponent_ng_container_8_Template_input_change_28_listener", "detectFiles", "DetailContentManagementLandownerComponent_ng_container_8_ng_container_33_Template", "DetailContentManagementLandownerComponent_ng_container_8_div_34_Template", "DetailContentManagementLandownerComponent_ng_container_8_label_40_Template", "DetailContentManagementLandownerComponent_ng_container_8_label_41_Template", "DetailContentManagementLandownerComponent_ng_container_8_div_42_Template", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "tmp_7_0", "undefined", "value", "tmp_9_0", "tmp_11_0", "CUiTypeOptions", "houseHoldList", "DetailContentManagementLandownerComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementLandowner", "CFormType", "CNoticeType", "isNew", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "getItemByValue", "options", "item", "checked", "formItemReq_", "for<PERSON>ach", "event", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "every", "createRemarkObject", "CRemarkType", "remarkObject", "option", "remarkTypes", "includes", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "body", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "map", "o", "CFormItemHouseHold", "CFormId", "CTotalAnswer", "CUiType", "mergeItems", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "formItemReq", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "key", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "items", "Map", "has", "existing", "count", "set", "Array", "from", "values", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "goBack", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementLandownerComponent_Template", "rf", "ctx", "DetailContentManagementLandownerComponent_ng_container_8_Template", "DetailContentManagementLandownerComponent_Template_button_click_10_listener", "DetailContentManagementLandownerComponent_Template_button_click_12_listener", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\content-management-landowner\\detail-content-management-landowner\\detail-content-management-landowner.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[]\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-landowner',\r\n  templateUrl: './detail-content-management-landowner.component.html',\r\n  styleUrls: ['./detail-content-management-landowner.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe],\r\n\r\n})\r\n\r\nexport class DetailContentManagementLandownerComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }\r\n  ]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number\r\n\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        if (this.buildCaseId) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n          this.arrListFormItemReq = res.Entries.map((o: any) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0,\r\n              selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [],\r\n              selectedCUiType: this.CUiTypeOptions[0]\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementLandowner.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false)\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false)\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType===3 ? 1: o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems },\r\n                selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length == this.houseHoldList.length,\r\n                listPictures: [],\r\n                selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0]\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if(formItemReq.selectedCUiType && formItemReq.selectedCUiType.value ===3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementLandowner.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n  // formatParam() {\r\n  //   const result: SaveListFormItemReq[] =\r\n  //   })\r\n  //   return result\r\n  // }\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false\r\n    let hasInvalidCRequireAnswer = false\r\n    let hasInvalidItemName = false;\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName,// ? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementLandowner.CFormType,\r\n    }\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n        <h4 class=\"font-bold text-lg pb-3\">類型-獨立選樣</h4>\r\n        <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n          <div class=\"flex flex-wrap w-full\">\r\n\r\n            <div class=\"w-full md:w-1/4 px-2 pb-2\">\r\n              <div class=\"flex flex-col items-center\" *ngIf=\"formItemReq.CFirstMatrialUrl\">\r\n                <div class=\"mt-3 w-full flex flex-col\">\r\n                  <div class=\"w-full\">\r\n                    <img *ngIf=\"formItemReq?.CFirstMatrialUrl\" class=\"h-[140px] w-[300px]\"\r\n                      [src]=\"formItemReq?.CFirstMatrialUrl | addBaseFile\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n  \r\n            <div class=\"w-full md:w-1/2 px-2\">\r\n              <div class=\"w-full\">\r\n                <div class=\"form-group flex items-center w-full\">\r\n                  <label for=\"CItemName\" class=\"label w-1/2 text-base\">\r\n                    {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                  </label>\r\n                  <div class=\"input-group items-center w-1/2 px-0\">\r\n                    <input type=\"text\" class=\"w-full\" nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"廚房\"\r\n                      [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"w-full\">\r\n                <div class=\"form-group flex items-center w-full\">\r\n                  <label for=\"cRequireAnswer\" class=\"label w-1/2\">必填數量</label>\r\n                  <input type=\"number\" class=\"w-1/2\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                    [disabled]=\"formItemReq.selectedCUiType.value === 3 || listFormItem.CIsLock ?? false\" />\r\n                </div>\r\n              </div>\r\n              <div class=\"w-full\">\r\n                <div class=\"form-group flex items-center w-full\">\r\n                  <label for=\"buildingName\" class=\"label w-1/2\">前台UI類型</label>\r\n                  <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\" class=\"w-1/2 px-0\"\r\n                    (selectedChange)=\"changeSelectCUiType(formItemReq)\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                    <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                      {{ case.label }}\r\n                    </nb-option>\r\n                  </nb-select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n  \r\n            <div class=\"w-full md:w-1/4 px-2\">\r\n              <div class=\"w-full flex flex-col\">\r\n                <div class=\"flex justify-end w-full\">\r\n                  <button class=\"btn btn-info h-fit\" [disabled]=\"listFormItem.CIsLock\"\r\n                    (click)=\"inputFile.click()\">上傳概念設計圖</button>\r\n                </div>\r\n                <div class=\"w-full\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n                  <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                    accept=\"image/png, image/gif, image/jpeg\">\r\n                  <div class=\"mt-3 w-full flex flex-col\">\r\n                    <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n                      <tbody>\r\n                        <ng-container *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                          <tr>\r\n                            <td class=\"align-middle\">\r\n                              <label>文件名 </label> &nbsp;\r\n                              <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                                (blur)=\"renameFile($event, i, formItemReq)\">\r\n                              <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                                (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                            </td>\r\n                            <td>\r\n                              <img class=\"h-28 w-40\" [src]=\"picture.data\">\r\n                            </td>\r\n                          </tr>\r\n                        </ng-container>\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n  \r\n                <div class=\"w-full text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n                  [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n                  <div class=\"mt-3 w-full flex flex-col\">\r\n                    <div class=\"w-full justify-items-end\">\r\n                      <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"h-32 w-32\"\r\n                        [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n  \r\n          <div class=\"flex flex-wrap w-full\">\r\n            <div class=\"w-full md:w-1/4 px-2\"><label for=\"buildingName\" class=\"label\">適用戶別</label></div>\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                  全選\r\n                </nb-checkbox>\r\n              </label>\r\n              <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n                <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\" [disabled]=\"listFormItem.CIsLock\"\r\n                  (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                  {{ item }}\r\n                </nb-checkbox>\r\n              </label>\r\n            </div>\r\n          </div>\r\n  \r\n          <div class=\"flex flex-wrap w-full\" *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n            <div class=\"w-full md:w-1/4 px-2\">\r\n              <label for=\"buildingName\" class=\"label\">備註選項</label>\r\n            </div>\r\n            <div class=\"w-full md:w-3/4 px-2\">\r\n              <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n                <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                  [disabled]=\"listFormItem.CIsLock\" value=\"item\"\r\n                  (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\">\r\n                  {{ remark }}\r\n                </nb-checkbox>\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n\r\n\r\n\r\n      <!-- <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"CItemName\" class=\"label col-4 text-base\">\r\n              {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n            </label>\r\n            <div class=\"input-group items-center w-full col-8 px-0\">\r\n              <input type=\"text\" class=\"w-full col-12\" nbInput [(ngModel)]=\"formItemReq.CItemName\"\r\n                [placeholder]=\"formItemReq.CItemName\" [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-info\" (click)=\"inputFile.click()\" [disabled]=\"listFormItem.CIsLock\">上傳概念設計圖</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" [ngClass]=\"formItemReq.listPictures.length ? '':'hidden'\">\r\n          <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n            accept=\"image/png, image/gif, image/jpeg\" [disabled]=\"listFormItem.CIsLock\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n              <tbody>\r\n                <tr *ngFor=\"let picture of formItemReq.listPictures; let i = index\">\r\n                  <td class=\"align-middle\">\r\n                    <label>文件名 </label> &nbsp;\r\n                    <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                      (blur)=\"renameFile($event, i, formItemReq)\">\r\n                  </td>\r\n                  <td class=\"w-[100px] h-auto\">\r\n                    <img class=\"fit-size\" [src]=\"picture.data\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                    <button class=\"btn btn-outline-danger btn-sm m-1\"\r\n                      (click)=\"removeImage(picture.id, formItemReq)\">删除</button>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12 text-center\" *ngIf=\"formItemReq.CDesignFileUrl\"\r\n          [ngClass]=\"formItemReq.listPictures.length ? 'hidden':''\">\r\n          <div class=\"mt-3 w-full flex flex-col\">\r\n            <table class=\"table table-striped border\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"align-middle\">\r\n                  </td>\r\n                  <td class=\"w-[80px] h-auto\">\r\n                    <img *ngIf=\"formItemReq.CDesignFileUrl\" class=\"w-14 h-14\"\r\n                      [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                  </td>\r\n                  <td class=\"text-center w-32 align-middle\">\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cRequireAnswer\" class=\"label col-4\">必填數量 </label>\r\n            <input type=\"number\" class=\"col-8\" nbInput placeholder=\"必填數量\" [(ngModel)]=\"formItemReq.CRequireAnswer \"\r\n              [disabled]=\"formItemReq.selectedCUiType.value===3||listFormItem.CIsLock ?? false\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n\r\n        </div>\r\n\r\n        <div class=\"col-md-9\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-4\">前台UI類型</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n              (selectedChange)=\"changeSelectCUiType(formItemReq)\" class=\"col-8 px-0\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n              <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                {{ case.label }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-3\"></div>\r\n        <div class=\"col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">適用戶別 </label>\r\n            <label class=\"mr-2\" *ngIf=\"houseHoldList.length\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckAllChange($event, formItemReq)\">\r\n                全選\r\n              </nb-checkbox>\r\n            </label>\r\n            <label *ngFor=\"let item of houseHoldList\" class=\"mr-2\">\r\n              <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\" [disabled]=\"listFormItem.CIsLock\"\r\n                (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\">\r\n                {{ item }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-12\" *ngIf=\"formItemReq.selectedCUiType.value===3\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">備註選項</label>\r\n            <label *ngFor=\"let remark of CRemarkTypeOptions\" class=\"mr-2\">\r\n              <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\" [(checked)]=\"formItemReq.selectedRemarkType[remark]\"\r\n                value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\"\r\n                [disabled]=\"listFormItem.CIsLock\">\r\n                {{ remark }}\r\n              </nb-checkbox>\r\n            </label>\r\n          </div>\r\n        </div>\r\n      </ng-container> -->\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <button class=\"btn btn-secondary mx-2\" (click)=\"goBack()\">\r\n      取消\r\n    </button>\r\n    <button class=\"btn btn-info\" (click)=\"onSubmit()\">\r\n      儲存\r\n    </button>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,aAAa,QAAQ,wCAAwC;AAMtE,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;;;;ICCxDC,EAAA,CAAAC,SAAA,cACsD;;;;;IAApDD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,cAAA,kBAAAA,cAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAmD;;;;;IAFvDN,EAFJ,CAAAO,cAAA,cAA6E,cACpC,cACjB;IAClBP,EAAA,CAAAQ,UAAA,IAAAC,6EAAA,kBACsD;IAG5DT,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;IAJMV,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,kBAAAA,cAAA,CAAAC,gBAAA,CAAmC;;;;;IA+BzCL,EAAA,CAAAO,cAAA,oBAA8D;IAC5DP,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAY;;;;IAFmCV,EAAA,CAAAE,UAAA,UAAAW,OAAA,CAAc;IAC3Db,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAD,OAAA,CAAAE,KAAA,MACF;;;;;;IAkBIf,EAAA,CAAAgB,uBAAA,GAA8E;IAGxEhB,EAFJ,CAAAO,cAAA,SAAI,aACuB,YAChB;IAAAP,EAAA,CAAAY,MAAA,0BAAI;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAACV,EAAA,CAAAY,MAAA,eACpB;IAAAZ,EAAA,CAAAO,cAAA,gBAC8C;IAA5CP,EAAA,CAAAiB,UAAA,kBAAAC,wGAAAC,MAAA;MAAA,MAAAC,IAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAnB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAQD,MAAA,CAAAE,UAAA,CAAAT,MAAA,EAAAC,IAAA,EAAAhB,cAAA,CAAkC;IAAA,EAAC;IAD7CJ,EAAA,CAAAU,YAAA,EAC8C;IAC9CV,EAAA,CAAAO,cAAA,iBACiD;IAA/CP,EAAA,CAAAiB,UAAA,mBAAAY,0GAAA;MAAA,MAAAC,UAAA,GAAA9B,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAG,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAASD,MAAA,CAAAK,WAAA,CAAAD,UAAA,CAAAE,EAAA,EAAA5B,cAAA,CAAoC;IAAA,EAAC;IAACJ,EAAA,CAAAY,MAAA,mBAAE;IACrDZ,EADqD,CAAAU,YAAA,EAAS,EACzD;IACLV,EAAA,CAAAO,cAAA,SAAI;IACFP,EAAA,CAAAC,SAAA,eAA4C;IAEhDD,EADE,CAAAU,YAAA,EAAK,EACF;;;;;IARwDV,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAE,UAAA,UAAA4B,UAAA,CAAAG,IAAA,CAAsB;IAMxDjC,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAE,UAAA,QAAA4B,UAAA,CAAAI,IAAA,EAAAlC,EAAA,CAAAM,aAAA,CAAoB;;;;;IAanDN,EAAA,CAAAC,SAAA,cACmD;;;;;IAAjDD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,cAAA,CAAA+B,cAAA,GAAAnC,EAAA,CAAAM,aAAA,CAAgD;;;;;IAFpDN,EAHJ,CAAAO,cAAA,cAC4D,cACnB,cACC;IACpCP,EAAA,CAAAQ,UAAA,IAAA4B,8EAAA,kBACmD;IAGzDpC,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;;;;IAPJV,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAAAC,MAAA,iBAAyD;IAG/CtC,EAAA,CAAAW,SAAA,GAAgC;IAAhCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAA+B,cAAA,CAAgC;;;;;;IAa5CnC,EADF,CAAAO,cAAA,gBAAiD,sBAEW;IAD7CP,EAAA,CAAAuC,gBAAA,2BAAAC,gHAAArB,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAoB,GAAA;MAAA,MAAArC,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAuC,WAAA,EAAAxB,MAAA,MAAAf,cAAA,CAAAuC,WAAA,GAAAxB,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAChDnB,EAAA,CAAAiB,UAAA,2BAAAuB,gHAAArB,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAoB,GAAA;MAAA,MAAArC,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAAkB,gBAAA,CAAAzB,MAAA,EAAAf,cAAA,CAAqC;IAAA,EAAC;IACvDJ,EAAA,CAAAY,MAAA,qBACF;IACFZ,EADE,CAAAU,YAAA,EAAc,EACR;;;;;IAJOV,EAAA,CAAAW,SAAA,EAAqC;IAArCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAuC,WAAA,CAAqC;IAAC3C,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;;;;;;IAMpF/C,EADF,CAAAO,cAAA,gBAAuD,sBAEwB;IADhEP,EAAA,CAAAuC,gBAAA,2BAAAS,gHAAA7B,MAAA;MAAA,MAAA8B,QAAA,GAAAjD,EAAA,CAAAqB,aAAA,CAAA6B,IAAA,EAAAzB,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA+C,aAAA,CAAAF,QAAA,GAAA9B,MAAA,MAAAf,cAAA,CAAA+C,aAAA,CAAAF,QAAA,IAAA9B,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAA6C;IACxDnB,EAAA,CAAAiB,UAAA,2BAAA+B,gHAAA7B,MAAA;MAAA,MAAA8B,QAAA,GAAAjD,EAAA,CAAAqB,aAAA,CAAA6B,IAAA,EAAAzB,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAA0B,6BAAA,CAAAjC,MAAA,EAAA8B,QAAA,EAAA7C,cAAA,CAAwD;IAAA,EAAC;IAC1EJ,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAU,YAAA,EAAc,EACR;;;;;;IAJOV,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA+C,aAAA,CAAAF,QAAA,EAA6C;IAAcjD,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAEvG/C,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAmC,QAAA,MACF;;;;;;IAWAjD,EAAA,CAAAO,cAAA,sBAEwE;IAFpBP,EAAA,CAAAuC,gBAAA,2BAAAc,oIAAAlC,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,IAAAC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,GAAApC,MAAA,MAAAf,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,IAAApC,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAEtGnB,EAAA,CAAAiB,UAAA,2BAAAoC,oIAAAlC,MAAA;MAAAnB,EAAA,CAAAqB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,UAAA,GAAAvD,EAAA,CAAAwB,aAAA,GAAAC,SAAA;MAAA,MAAArB,cAAA,GAAAJ,EAAA,CAAAwB,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAiBD,MAAA,CAAA+B,sBAAA,CAAAtC,MAAA,EAAAoC,UAAA,EAAAnD,cAAA,CAAmD;IAAA,EAAC;IACrEJ,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAc;;;;;;IAJsCV,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAoD,kBAAA,CAAAD,UAAA,EAAoD;IACtGvD,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAEjC/C,EAAA,CAAAW,SAAA,EACF;IADEX,EAAA,CAAAc,kBAAA,MAAAyC,UAAA,MACF;;;;;IALFvD,EAAA,CAAAO,cAAA,gBAA8D;IAC5DP,EAAA,CAAAQ,UAAA,IAAAkD,8FAAA,0BAEwE;IAG1E1D,EAAA,CAAAU,YAAA,EAAQ;;;;IALQV,EAAA,CAAAW,SAAA,EAAoC;IAApCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAoD,kBAAA,CAAoC;;;;;IAJpDxD,EAFJ,CAAAO,cAAA,aAAmF,cAC/C,gBACQ;IAAAP,EAAA,CAAAY,MAAA,+BAAI;IAC9CZ,EAD8C,CAAAU,YAAA,EAAQ,EAChD;IACNV,EAAA,CAAAO,cAAA,cAAkC;IAChCP,EAAA,CAAAQ,UAAA,IAAAmD,gFAAA,oBAA8D;IAQlE3D,EADE,CAAAU,YAAA,EAAM,EACF;;;;IARwBV,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAkC,kBAAA,CAAqB;;;;;;IAjHrD5D,EAAA,CAAAgB,uBAAA,GAA8E;IAG1EhB,EAFF,CAAAO,cAAA,aAAmC,cAEM;IACrCP,EAAA,CAAAQ,UAAA,IAAAqD,uEAAA,kBAA6E;IAQ/E7D,EAAA,CAAAU,YAAA,EAAM;IAKAV,EAHN,CAAAO,cAAA,cAAkC,cACZ,cAC+B,gBACM;IACnDP,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAENV,EADF,CAAAO,cAAA,cAAiD,iBAEA;IADLP,EAAA,CAAAuC,gBAAA,2BAAAuB,kGAAA3C,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA4D,SAAA,EAAA7C,MAAA,MAAAf,cAAA,CAAA4D,SAAA,GAAA7C,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAInFnB,EAJM,CAAAU,YAAA,EAC+C,EAC3C,EACF,EACF;IAGFV,EAFJ,CAAAO,cAAA,eAAoB,eAC+B,iBACC;IAAAP,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAC5DV,EAAA,CAAAO,cAAA,iBAC0F;IAD5BP,EAAA,CAAAuC,gBAAA,2BAAA0B,kGAAA9C,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAA8D,cAAA,EAAA/C,MAAA,MAAAf,cAAA,CAAA8D,cAAA,GAAA/C,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAG1GnB,EAHI,CAAAU,YAAA,EAC0F,EACtF,EACF;IAGFV,EAFJ,CAAAO,cAAA,eAAoB,eAC+B,iBACD;IAAAP,EAAA,CAAAY,MAAA,kCAAM;IAAAZ,EAAA,CAAAU,YAAA,EAAQ;IAC5DV,EAAA,CAAAO,cAAA,qBACiG;IADrEP,EAAA,CAAAuC,gBAAA,2BAAA4B,sGAAAhD,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAAzB,EAAA,CAAA0C,kBAAA,CAAAtC,cAAA,CAAAgE,eAAA,EAAAjD,MAAA,MAAAf,cAAA,CAAAgE,eAAA,GAAAjD,MAAA;MAAA,OAAAnB,EAAA,CAAA2B,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACnEnB,EAAA,CAAAiB,UAAA,4BAAAoD,uGAAA;MAAA,MAAAjE,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAkBD,MAAA,CAAA4C,mBAAA,CAAAlE,cAAA,CAAgC;IAAA,EAAC;IACnDJ,EAAA,CAAAQ,UAAA,KAAA+D,8EAAA,wBAA8D;IAMtEvE,EAHM,CAAAU,YAAA,EAAY,EACR,EACF,EACF;IAKAV,EAHN,CAAAO,cAAA,eAAkC,eACE,eACK,kBAEL;IAA5BP,EAAA,CAAAiB,UAAA,mBAAAuD,2FAAA;MAAAxE,EAAA,CAAAqB,aAAA,CAAA0C,GAAA;MAAA,MAAAU,YAAA,GAAAzE,EAAA,CAAA0E,WAAA;MAAA,OAAA1E,EAAA,CAAA2B,WAAA,CAAS8C,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IAAC3E,EAAA,CAAAY,MAAA,kDAAO;IACvCZ,EADuC,CAAAU,YAAA,EAAS,EAC1C;IAEJV,EADF,CAAAO,cAAA,eAA8E,oBAEhC;IADCP,EAAA,CAAAiB,UAAA,oBAAA2D,2FAAAzD,MAAA;MAAA,MAAAf,cAAA,GAAAJ,EAAA,CAAAqB,aAAA,CAAA0C,GAAA,EAAAtC,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAA2B,WAAA,CAAUD,MAAA,CAAAmD,WAAA,CAAA1D,MAAA,EAAAf,cAAA,CAAgC;IAAA,EAAC;IAAxFJ,EAAA,CAAAU,YAAA,EAC4C;IAGxCV,EAFJ,CAAAO,cAAA,eAAuC,iBACuC,aACnE;IACLP,EAAA,CAAAQ,UAAA,KAAAsE,iFAAA,2BAA8E;IAiBtF9E,EAHM,CAAAU,YAAA,EAAQ,EACF,EACJ,EACF;IAENV,EAAA,CAAAQ,UAAA,KAAAuE,wEAAA,kBAC4D;IAUlE/E,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;IAG8BV,EADpC,CAAAO,cAAA,cAAmC,eACC,iBAAwC;IAAAP,EAAA,CAAAY,MAAA,gCAAI;IAAQZ,EAAR,CAAAU,YAAA,EAAQ,EAAM;IAC5FV,EAAA,CAAAO,cAAA,eAAkC;IAOhCP,EANA,CAAAQ,UAAA,KAAAwE,0EAAA,oBAAiD,KAAAC,0EAAA,oBAMM;IAO3DjF,EADE,CAAAU,YAAA,EAAM,EACF;IAENV,EAAA,CAAAQ,UAAA,KAAA0E,wEAAA,kBAAmF;;;;;;;;;IAxGtClF,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAC,gBAAA,CAAkC;IAcrEL,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmF,kBAAA,MAAA/E,cAAA,CAAAgF,KAAA,OAAAhF,cAAA,CAAAiF,KAAA,OAAAjF,cAAA,CAAAkF,SAAA,MACF;IAE4CtF,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA4D,SAAA,CAAmC;IAC3EhE,EAAA,CAAAE,UAAA,cAAAqF,OAAA,GAAA7D,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAAwC,OAAA,KAAAC,SAAA,GAAAD,OAAA,SAA0C;IAOgBvF,EAAA,CAAAW,SAAA,GAAwC;IAAxCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAA8D,cAAA,CAAwC;IACpGlE,EAAA,CAAAE,UAAA,aAAAE,cAAA,CAAAgE,eAAA,CAAAqB,KAAA,YAAAC,OAAA,GAAAhE,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAA2C,OAAA,KAAAF,SAAA,GAAAE,OAAA,UAAqF;IAM3D1F,EAAA,CAAAW,SAAA,GAAyC;IAAzCX,EAAA,CAAA6C,gBAAA,YAAAzC,cAAA,CAAAgE,eAAA,CAAyC;IACfpE,EAAA,CAAAE,UAAA,cAAAyF,QAAA,GAAAjE,MAAA,CAAAoB,YAAA,CAAAC,OAAA,cAAA4C,QAAA,KAAAH,SAAA,GAAAG,QAAA,SAA0C;IAClE3F,EAAA,CAAAW,SAAA,EAAiB;IAAjBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAkE,cAAA,CAAiB;IAWZ5F,EAAA,CAAAW,SAAA,GAAiC;IAAjCX,EAAA,CAAAE,UAAA,aAAAwB,MAAA,CAAAoB,YAAA,CAAAC,OAAA,CAAiC;IAGlD/C,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAAAC,MAAA,iBAAyD;IAMnCtC,EAAA,CAAAW,SAAA,GAA6B;IAA7BX,EAAA,CAAAE,UAAA,YAAAE,cAAA,CAAAiC,YAAA,CAA6B;IAmBtCrC,EAAA,CAAAW,SAAA,EAAgC;IAAhCX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAA+B,cAAA,CAAgC;IAgB9CnC,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAE,UAAA,SAAAwB,MAAA,CAAAmE,aAAA,CAAAvD,MAAA,CAA0B;IAMvBtC,EAAA,CAAAW,SAAA,EAAgB;IAAhBX,EAAA,CAAAE,UAAA,YAAAwB,MAAA,CAAAmE,aAAA,CAAgB;IASR7F,EAAA,CAAAW,SAAA,EAA6C;IAA7CX,EAAA,CAAAE,UAAA,SAAAE,cAAA,CAAAgE,eAAA,CAAAqB,KAAA,OAA6C;;;ADtE3F,OAAM,MAAOK,yCAA0C,SAAQjG,aAAa;EAC1EkG,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAKvB,KAAAC,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD,KAAAhB,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,EACD;MACE0E,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,EAAE;MACD0E,KAAK,EAAE,CAAC;MAAE1E,KAAK,EAAE;KAClB,CACF;IACD,KAAA6C,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2BjC,KAAAT,aAAa,GAA+B,EAAE;IAC9C,KAAAK,kBAAkB,GAA+B,EAAE;IA2HnD,KAAAqD,KAAK,GAAY,IAAI;EAvKrB;EAmBSC,QAAQA,CAAA;IACf,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMnF,EAAE,GAAGkF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGpF,EAAE;QACrB,IAAI,IAAI,CAACoF,WAAW,EAAE;UACpB,IAAI,CAACC,iCAAiC,EAAE;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAAC7B,KAAU,EAAE8B,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC/B,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO+B,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAMA5E,gBAAgBA,CAAC6E,OAAgB,EAAEC,YAAiB;IAClDA,YAAY,CAAC/E,WAAW,GAAG8E,OAAO;IAClC,IAAI,CAAC5B,aAAa,CAAC8B,OAAO,CAACH,IAAI,IAAG;MAChCE,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,GAAGC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAGA5C,WAAWA,CAAC+C,KAAU,EAAEF,YAAiB;IACvC,MAAMG,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIV,YAAY,CAACrF,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;UACxCoF,YAAY,CAACrF,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BL,EAAE,EAAE,IAAIsG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBtG,IAAI,EAAE4F,IAAI,CAAC5F,IAAI,CAACuG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BtG,IAAI,EAAEkG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACpC,eAAe,CAACqC,gBAAgB,CAACb,IAAI,CAAC5F,IAAI,CAAC;YAC3D0G,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLH,YAAY,CAACrF,YAAY,CAACuG,IAAI,CAAC;YAC7B5G,EAAE,EAAE,IAAIsG,IAAI,EAAE,CAACC,OAAO,EAAE;YACxBtG,IAAI,EAAE4F,IAAI,CAAC5F,IAAI,CAACuG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BtG,IAAI,EAAEkG,SAAS;YACfK,SAAS,EAAE,IAAI,CAACpC,eAAe,CAACqC,gBAAgB,CAACb,IAAI,CAAC5F,IAAI,CAAC;YAC3D0G,KAAK,EAAEd;WACR,CAAC;QACJ;QACAD,KAAK,CAACE,MAAM,CAACrC,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEA1D,WAAWA,CAAC8G,SAAiB,EAAEnB,YAAiB;IAC9C,IAAIA,YAAY,CAACrF,YAAY,CAACC,MAAM,EAAE;MACpCoF,YAAY,CAACrF,YAAY,GAAGqF,YAAY,CAACrF,YAAY,CAACyG,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC/G,EAAE,IAAI6G,SAAS,CAAC;IAC7F;EACF;EAEAjH,UAAUA,CAACgG,KAAU,EAAErG,KAAa,EAAEmG,YAAiB;IACrD,IAAIsB,IAAI,GAAGtB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACoH,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEvB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACoH,KAAK,CAACO,IAAI,EAAExB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACoH,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGpB,KAAK,CAACE,MAAM,CAACrC,KAAK,GAAG,GAAG,GAAGiC,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACkH,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEzB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACoH,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKzB,YAAY,CAACrF,YAAY,CAACd,KAAK,CAAC,CAACoH,KAAK,GAAGS,OAAO;EAClD;EAEAhG,6BAA6BA,CAACqE,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IAC7E,IAAID,OAAO,EAAE;MACXC,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,GAAGC,OAAO;MAC1CC,YAAY,CAAC/E,WAAW,GAAG,IAAI,CAACkD,aAAa,CAACyD,KAAK,CAAC9B,IAAI,IAAIE,YAAY,CAACvE,aAAa,CAACqE,IAAI,CAAC,IAAIC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACLC,YAAY,CAAC/E,WAAW,GAAG,KAAK;IAClC;EACF;EAIAc,sBAAsBA,CAACgE,OAAgB,EAAED,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAAClE,kBAAkB,CAACgE,IAAI,CAAC,GAAGC,OAAO;EACjD;EAEA8B,kBAAkBA,CAAC3F,kBAA4B,EAAE4F,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAMC,MAAM,IAAI9F,kBAAkB,EAAE;MACvC6F,YAAY,CAACC,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMC,WAAW,GAAGH,WAAW,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIQ,WAAW,EAAE;MAC9B,IAAI/F,kBAAkB,CAACgG,QAAQ,CAACT,IAAI,CAAC,EAAE;QACrCM,YAAY,CAACN,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOM,YAAY;EACrB;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACrD,gBAAgB,CAACsD,mCAAmC,CAAC;MACxDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9B6C,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLpK,GAAG,CAACqK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACxE,aAAa,CAAC8B,OAAO,CAACH,IAAI,IAAI,IAAI,CAACrE,aAAa,CAACqE,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAC5D,kBAAkB,CAAC+D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAChE,kBAAkB,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;QAC9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAAEC,CAAM,IAAI;UACnD,OAAO;YACLrI,cAAc,EAAE,IAAI;YACpBsI,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACbpF,SAAS,EAAEkF,CAAC,CAAClF,SAAS;YACtBF,KAAK,EAAEoF,CAAC,CAACpF,KAAK;YACdC,KAAK,EAAEmF,CAAC,CAACnF,KAAK;YACdrB,SAAS,EAAE,GAAGwG,CAAC,CAACpF,KAAK,IAAIoF,CAAC,CAACnF,KAAK,IAAImF,CAAC,CAAClF,SAAS,EAAE;YACjDkE,WAAW,EAAE,IAAI;YACjBmB,YAAY,EAAE,CAAC;YACfzG,cAAc,EAAE,CAAC;YACjB0G,OAAO,EAAE,CAAC;YACVzH,aAAa,EAAE,EAAE;YACjBK,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cb,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAChB+B,eAAe,EAAE,IAAI,CAACwB,cAAc,CAAC,CAAC;WACvC;QACH,CAAC,CAAC;QACF,IAAI,CAAC0E,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACO,UAAU,CAAC,IAAI,CAACP,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAACtD,SAAS,EAAE;EACf;EAKA8D,eAAeA,CAAA;IACb,IAAI,CAAC3E,gBAAgB,CAAC4E,mCAAmC,CAAC;MACxDhB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAAC5C,WAAW;QAC9BT,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC,SAAS;QACxDqE,SAAS,EAAE;;KAEd,CAAC,CAACd,IAAI,CACLpK,GAAG,CAACqK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvH,YAAY,GAAGqH,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACvD,KAAK,GAAGsD,GAAG,CAACC,OAAO,CAACa,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAId,GAAG,CAACC,OAAO,CAACa,SAAS,EAAE;UAEzB,IAAI,CAACpF,aAAa,CAAC8B,OAAO,CAACH,IAAI,IAAI,IAAI,CAACrE,aAAa,CAACqE,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAC5D,kBAAkB,CAAC+D,OAAO,CAACH,IAAI,IAAI,IAAI,CAAChE,kBAAkB,CAACgE,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC8C,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACa,SAAS,CAACV,GAAG,CAAEC,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAC5H,YAAY,CAAC4H,OAAO;cAClCvI,cAAc,EAAEqI,CAAC,CAACrI,cAAc;cAChCwG,KAAK,EAAE6B,CAAC,CAAC7B,KAAK;cACd8B,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCS,WAAW,EAAEV,CAAC,CAACU,WAAW;cAC1B5F,SAAS,EAAEkF,CAAC,CAAClF,SAAS;cACtBF,KAAK,EAAEoF,CAAC,CAACpF,KAAK;cACdC,KAAK,EAAEmF,CAAC,CAACnF,KAAK;cACdrB,SAAS,EAAEwG,CAAC,CAACxG,SAAS,GAAGwG,CAAC,CAACxG,SAAS,GAAG,GAAGwG,CAAC,CAACpF,KAAK,IAAIoF,CAAC,CAACnF,KAAK,IAAImF,CAAC,CAAClF,SAAS,EAAE;cAC7EkE,WAAW,EAAEgB,CAAC,CAAChB,WAAW;cAC1BmB,YAAY,EAAEH,CAAC,CAACG,YAAY;cAC5BzG,cAAc,EAAEsG,CAAC,CAACI,OAAO,KAAG,CAAC,GAAG,CAAC,GAAEJ,CAAC,CAACtG,cAAc;cACnD0G,OAAO,EAAEJ,CAAC,CAACI,OAAO;cAClBzH,aAAa,EAAEqH,CAAC,CAACW,qBAAqB,CAAC7I,MAAM,GAAG,IAAI,CAAC8I,0BAA0B,CAAC,IAAI,CAACvF,aAAa,EAAE2E,CAAC,CAACW,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAChI;cAAa,CAAE;cACxJK,kBAAkB,EAAEgH,CAAC,CAAChB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAAC3F,kBAAkB,EAAE4G,CAAC,CAAChB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAChG;cAAkB,CAAE;cACpIb,WAAW,EAAE6H,CAAC,CAACW,qBAAqB,CAAC7I,MAAM,IAAI,IAAI,CAACuD,aAAa,CAACvD,MAAM;cACxED,YAAY,EAAE,EAAE;cAChB+B,eAAe,EAAEoG,CAAC,CAACI,OAAO,GAAG,IAAI,CAACtD,cAAc,CAACkD,CAAC,CAACI,OAAO,EAAE,IAAI,CAAChF,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;aACzG;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACiE,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAAC7C,SAAS,EAAE;EACf;EAEA1C,mBAAmBA,CAAC+G,WAAgB;IAClC,IAAGA,WAAW,CAACjH,eAAe,IAAIiH,WAAW,CAACjH,eAAe,CAACqB,KAAK,KAAI,CAAC,EAAE;MACxE4F,WAAW,CAACnH,cAAc,GAAG,CAAC;IAChC;EACF;EAEAoH,4BAA4BA,CAACpJ,IAAW;IACtC,KAAK,IAAIsF,IAAI,IAAItF,IAAI,EAAE;MACrB,IAAIsF,IAAI,CAACZ,WAAW,KAAK,IAAI,CAACF,8BAA8B,CAACE,WAAW,EAAE;QACxE,OAAOY,IAAI,CAAC+D,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC3C,MAAM,CAAC8C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC;EACjD;EAEAC,0BAA0BA,CAACJ,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB3C,MAAM,CAAC8C,GAAG,IAAIH,GAAG,CAACG,GAAG,CAAC,CAAC,CACvBE,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAAC3H,eAAoB,EAAEZ,kBAAuB;IAC1D,IAAIY,eAAe,IAAIA,eAAe,CAACqB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAACoG,0BAA0B,CAACrI,kBAAkB,CAAC;IAC5D;EACF;EAEAwI,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACzD,KAAK,CAAC,GAAG,CAAC;IACpC,IAAI0D,KAAK,CAAC5J,MAAM,GAAG,CAAC,EAAE;MACpB,OAAO4J,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAC9J,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACL8J,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC3J,YAAY,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,IAAI,IAAI;QACpEmK,aAAa,EAAEhK,YAAY,CAAC,CAAC,CAAC,CAACoG,SAAS,IAAI,IAAI;QAChD6D,QAAQ,EAAEjK,YAAY,CAAC,CAAC,CAAC,CAACsG,KAAK,CAAC1G,IAAI,IAAII,YAAY,CAAC,CAAC,CAAC,CAACJ,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOuD,SAAS;EAEzB;EAEA;EACA;EACA;EACA;EACA;EACAqF,UAAUA,CAAC0B,KAAoC;IAC7C,MAAMhC,GAAG,GAAG,IAAIiC,GAAG,EAAgE;IAEnFD,KAAK,CAAC5E,OAAO,CAACH,IAAI,IAAG;MACnB,MAAMoE,GAAG,GAAG,GAAGpE,IAAI,CAAClC,SAAS,IAAIkC,IAAI,CAACpC,KAAK,IAAIoC,IAAI,CAACnC,KAAK,EAAE;MAC3D,IAAIkF,GAAG,CAACkC,GAAG,CAACb,GAAG,CAAC,EAAE;QAChB,MAAMc,QAAQ,GAAGnC,GAAG,CAACpD,GAAG,CAACyE,GAAG,CAAE;QAC9Bc,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLpC,GAAG,CAACqC,GAAG,CAAChB,GAAG,EAAE;UAAEpE,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAEmF,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACvC,GAAG,CAACwC,MAAM,EAAE,CAAC,CAACxC,GAAG,CAAC,CAAC;MAAE/C,IAAI;MAAEmF;IAAK,CAAE,MAAM;MACxD,GAAGnF,IAAI;MACPmD,YAAY,EAAEgC;KACf,CAAC,CAAC;EACL;EAGAK,UAAUA,CAAA;IACR,IAAI,CAAC1G,KAAK,CAAC2G,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAC9B,KAAK,MAAM5F,IAAI,IAAI,IAAI,CAAC6F,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC1F,IAAI,CAACoD,OAAQ,EAAE;QACzCsC,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC3F,IAAI,CAACtD,cAAe,EAAE;QACvDiJ,wBAAwB,GAAG,IAAI;MACjC;MAEA,IAAI3F,IAAI,CAACmD,YAAY,IAAInD,IAAI,CAACtD,cAAc,EAAE;QAC5C,IAAIsD,IAAI,CAACtD,cAAc,GAAGsD,IAAI,CAACmD,YAAY,EAAE;UAC3C,IAAI,CAACrE,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAG9F,IAAI,CAACmD,YAAY,GAAG,KAAKnD,IAAI,CAACxD,SAAS,IAAI,CAAC;QAC7F;MACF;MACA,IAAI,CAACoJ,kBAAkB,IAAK,CAAC5F,IAAI,CAACxD,SAAU,EAAE;QAC5CoJ,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAAC5G,KAAK,CAACgH,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAAC7G,KAAK,CAACgH,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAAC9G,KAAK,CAACgH,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAAC/C,kBAAkB,CAACC,GAAG,CAAEiD,CAAM,IAAI;MAChE,OAAO;QACLrL,cAAc,EAAEqL,CAAC,CAACrL,cAAc,GAAGqL,CAAC,CAACrL,cAAc,GAAG,IAAI;QAC1DwG,KAAK,EAAE6E,CAAC,CAACnL,YAAY,GAAG,IAAI,CAAC8J,UAAU,CAACqB,CAAC,CAACnL,YAAY,CAAC,GAAGmD,SAAS;QACnEiF,kBAAkB,EAAE,IAAI,CAACe,oBAAoB,CAACgC,CAAC,CAACrK,aAAa,CAAC;QAC9D+H,WAAW,EAAEsC,CAAC,CAACtC,WAAW,GAAGsC,CAAC,CAACtC,WAAW,GAAG,IAAI;QACjDuC,OAAO,EAAE,IAAI,CAAC5G,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC/D,YAAY,CAAC4H,OAAO;QACtDtF,KAAK,EAAEoI,CAAC,CAACpI,KAAK;QACdC,KAAK,EAAEmI,CAAC,CAACnI,KAAK;QACdC,SAAS,EAAEkI,CAAC,CAAClI,SAAS;QACtBtB,SAAS,EAAEwJ,CAAC,CAACxJ,SAAS;QAAC;QACvBwF,WAAW,EAAEgE,CAAC,CAACpJ,eAAe,CAACqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACsG,cAAc,CAACyB,CAAC,CAACpJ,eAAe,EAAEoJ,CAAC,CAAChK,kBAAkB,CAAC,IAAI,IAAI;QACxHmH,YAAY,EAAE6C,CAAC,CAAC7C,YAAY;QAC5BzG,cAAc,EAAEsJ,CAAC,CAACtJ,cAAc;QAChC0G,OAAO,EAAE4C,CAAC,CAACpJ,eAAe,CAACqB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACuH,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC1G,KAAK,CAACoH,aAAa,CAACpL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC4D,OAAO,CAACyH,aAAa,CAAC,IAAI,CAACrH,KAAK,CAACoH,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAC7G,KAAK,EAAE;MACd,IAAI,CAAC+G,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1H,gBAAgB,CAAC2H,oCAAoC,CAAC;MACzD/D,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAACrG,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAJ,kBAAkBA,CAAA;IAChB,IAAI,CAACK,iBAAiB,GAAG;MACvBjE,YAAY,EAAE,IAAI,CAAC5C,WAAW;MAC9B8G,SAAS,EAAE,IAAI,CAACb,mBAAmB,IAAI,IAAI;MAC3C1G,SAAS,EAAE,IAAI,CAACD,8BAA8B,CAACC;KAChD;IACD,IAAI,CAACR,gBAAgB,CAACgI,sCAAsC,CAAC;MAC3DpE,IAAI,EAAE,IAAI,CAACkE;KACZ,CAAC,CAACjH,SAAS,CAACmD,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnE,OAAO,CAAC6H,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA5C,0BAA0BA,CAACgD,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAM9G,IAAI,IAAI4G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKlH,IAAI,IAAIiH,KAAK,CAACE,SAAS,CAAC;MAClFL,CAAC,CAAC9G,IAAI,CAAC,GAAG,CAAC,CAAC+G,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKFjH,iCAAiCA,CAAA;IAC/B,IAAI,CAACjB,yBAAyB,CAACwI,8DAA8D,CAAC;MAC5F7E,IAAI,EAAE,IAAI,CAAC3C;KACZ,CAAC,CAAC8C,IAAI,CACLpK,GAAG,CAACqK,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACxE,aAAa,GAAG,IAAI,CAACyF,4BAA4B,CAACnB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACU,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAAC9D,SAAS,EAAE;EACf;EACAgH,MAAMA,CAAA;IACJ,IAAI,CAACvH,aAAa,CAACmC,IAAI,CAAC;MACtBiG,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC1H;KACf,CAAC;IACF,IAAI,CAACb,QAAQ,CAACwI,IAAI,EAAE;EACtB;;;uCAjbWjJ,yCAAyC,EAAA9F,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtP,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAxP,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAAzP,EAAA,CAAAgP,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAgP,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA7P,EAAA,CAAAgP,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA/P,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAAhQ,EAAA,CAAAgP,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAzCpK,yCAAyC;MAAAqK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAArQ,EAAA,CAAAsQ,0BAAA,EAAAtQ,EAAA,CAAAuQ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7CpD7Q,EADF,CAAAO,cAAA,iBAA0B,qBACR;UACdP,EAAA,CAAAC,SAAA,qBAAiC;UACnCD,EAAA,CAAAU,YAAA,EAAiB;UACjBV,EAAA,CAAAO,cAAA,mBAAc;UACZP,EAAA,CAAAC,SAAA,YAA0C;UAEtCD,EADJ,CAAAO,cAAA,aAA8B,YACS;UAAAP,EAAA,CAAAY,MAAA,4CAAO;UAAAZ,EAAA,CAAAU,YAAA,EAAK;UAC/CV,EAAA,CAAAQ,UAAA,IAAAuQ,iEAAA,4BAA8E;UAgPpF/Q,EADE,CAAAU,YAAA,EAAM,EACO;UAEbV,EADF,CAAAO,cAAA,wBAAsD,iBACM;UAAnBP,EAAA,CAAAiB,UAAA,mBAAA+P,4EAAA;YAAA,OAASF,GAAA,CAAA9C,MAAA,EAAQ;UAAA,EAAC;UACvDhO,EAAA,CAAAY,MAAA,sBACF;UAAAZ,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAO,cAAA,iBAAkD;UAArBP,EAAA,CAAAiB,UAAA,mBAAAgQ,4EAAA;YAAA,OAASH,GAAA,CAAAvD,QAAA,EAAU;UAAA,EAAC;UAC/CvN,EAAA,CAAAY,MAAA,sBACF;UAEJZ,EAFI,CAAAU,YAAA,EAAS,EACM,EACT;;;UAzPoCV,EAAA,CAAAW,SAAA,GAAuB;UAAvBX,EAAA,CAAAE,UAAA,YAAA4Q,GAAA,CAAAxG,kBAAA,CAAuB;;;qBDkCzD3K,YAAY,EAAAmQ,EAAA,CAAAoB,OAAA,EAAApB,EAAA,CAAAqB,OAAA,EAAArB,EAAA,CAAAsB,IAAA,EAAE1R,YAAY,EAAA2R,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAE1S,gBAAgB;MAAA2S,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}