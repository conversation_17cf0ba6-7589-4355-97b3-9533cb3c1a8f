{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpRequest, HttpParams, HttpHeaders } from '@angular/common/http';\n/**\n * Custom parameter codec to correctly handle the plus sign in parameter\n * values. See https://github.com/angular/angular/issues/18261\n */\nclass ParameterCodec {\n  encodeKey(key) {\n    return encodeURIComponent(key);\n  }\n  encodeValue(value) {\n    return encodeURIComponent(value);\n  }\n  decode<PERSON>ey(key) {\n    return decodeURIComponent(key);\n  }\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nconst ParameterCodecInstance = new ParameterCodec();\n/**\n * Base class for a parameter\n */\nclass Parameter {\n  constructor(name, value, options, defaultStyle, defaultExplode) {\n    this.name = name;\n    this.value = value;\n    this.options = options;\n    this.options = options || {};\n    if (this.options.style === null || this.options.style === undefined) {\n      this.options.style = defaultStyle;\n    }\n    if (this.options.explode === null || this.options.explode === undefined) {\n      this.options.explode = defaultExplode;\n    }\n  }\n  serializeValue(value, separator = ',') {\n    if (value === null || value === undefined) {\n      return '';\n    } else if (value instanceof Array) {\n      return value.map(v => this.serializeValue(v).split(separator).join(encodeURIComponent(separator))).join(separator);\n    } else if (typeof value === 'object') {\n      const array = [];\n      for (const key of Object.keys(value)) {\n        let propVal = value[key];\n        if (propVal !== null && propVal !== undefined) {\n          propVal = this.serializeValue(propVal).split(separator).join(encodeURIComponent(separator));\n          if (this.options.explode) {\n            array.push(`${key}=${propVal}`);\n          } else {\n            array.push(key);\n            array.push(propVal);\n          }\n        }\n      }\n      return array.join(separator);\n    } else {\n      return String(value);\n    }\n  }\n}\n/**\n * A parameter in the operation path\n */\nclass PathParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'simple', false);\n  }\n  append(path) {\n    let value = this.value;\n    if (value === null || value === undefined) {\n      value = '';\n    }\n    let prefix = this.options.style === 'label' ? '.' : '';\n    let separator = this.options.explode ? prefix === '' ? ',' : prefix : ',';\n    let alreadySerialized = false;\n    if (this.options.style === 'matrix') {\n      // The parameter name is just used as prefix, except in some cases...\n      prefix = `;${this.name}=`;\n      if (this.options.explode && typeof value === 'object') {\n        prefix = ';';\n        if (value instanceof Array) {\n          // For arrays we have to repeat the name for each element\n          value = value.map(v => `${this.name}=${this.serializeValue(v, ';')}`);\n          value = value.join(';');\n          alreadySerialized = true;\n        } else {\n          // For objects we have to put each the key / value pairs\n          value = this.serializeValue(value, ';');\n          alreadySerialized = true;\n        }\n      }\n    }\n    value = prefix + (alreadySerialized ? value : this.serializeValue(value, separator));\n    // Replace both the plain variable and the corresponding variant taking in the prefix and explode into account\n    path = path.replace(`{${this.name}}`, value);\n    path = path.replace(`{${prefix}${this.name}${this.options.explode ? '*' : ''}}`, value);\n    return path;\n  }\n  // @ts-ignore\n  serializeValue(value, separator = ',') {\n    var result = typeof value === 'string' ? encodeURIComponent(value) : super.serializeValue(value, separator);\n    result = result.replace(/%3D/g, '=');\n    result = result.replace(/%3B/g, ';');\n    result = result.replace(/%2C/g, ',');\n    return result;\n  }\n}\n/**\n * A parameter in the query\n */\nclass QueryParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'form', true);\n  }\n  append(params) {\n    if (this.value instanceof Array) {\n      // Array serialization\n      if (this.options.explode) {\n        for (const v of this.value) {\n          params = params.append(this.name, this.serializeValue(v));\n        }\n      } else {\n        const separator = this.options.style === 'spaceDelimited' ? ' ' : this.options.style === 'pipeDelimited' ? '|' : ',';\n        return params.append(this.name, this.serializeValue(this.value, separator));\n      }\n    } else if (this.value !== null && typeof this.value === 'object') {\n      // Object serialization\n      if (this.options.style === 'deepObject') {\n        // Append a parameter for each key, in the form `name[key]`\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            params = params.append(`${this.name}[${key}]`, this.serializeValue(propVal));\n          }\n        }\n      } else if (this.options.explode) {\n        // Append a parameter for each key without using the parameter name\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            params = params.append(key, this.serializeValue(propVal));\n          }\n        }\n      } else {\n        // Append a single parameter whose values are a comma-separated list of key,value,key,value...\n        const array = [];\n        for (const key of Object.keys(this.value)) {\n          const propVal = this.value[key];\n          if (propVal !== null && propVal !== undefined) {\n            array.push(key);\n            array.push(propVal);\n          }\n        }\n        params = params.append(this.name, this.serializeValue(array));\n      }\n    } else if (this.value !== null && this.value !== undefined) {\n      // Plain value\n      params = params.append(this.name, this.serializeValue(this.value));\n    }\n    return params;\n  }\n}\n/**\n * A parameter in the HTTP request header\n */\nclass HeaderParameter extends Parameter {\n  constructor(name, value, options) {\n    super(name, value, options, 'simple', false);\n  }\n  append(headers) {\n    if (this.value !== null && this.value !== undefined) {\n      if (this.value instanceof Array) {\n        for (const v of this.value) {\n          headers = headers.append(this.name, this.serializeValue(v));\n        }\n      } else {\n        headers = headers.append(this.name, this.serializeValue(this.value));\n      }\n    }\n    return headers;\n  }\n}\n/**\n * Helper to build http requests from parameters\n */\nexport class RequestBuilder {\n  constructor(rootUrl, operationPath, method) {\n    this.rootUrl = rootUrl;\n    this.operationPath = operationPath;\n    this.method = method;\n    this._path = new Map();\n    this._query = new Map();\n    this._header = new Map();\n  }\n  /**\n   * Sets a path parameter\n   */\n  path(name, value, options) {\n    this._path.set(name, new PathParameter(name, value, options || {}));\n  }\n  /**\n   * Sets a query parameter\n   */\n  query(name, value, options) {\n    this._query.set(name, new QueryParameter(name, value, options || {}));\n  }\n  /**\n   * Sets a header parameter\n   */\n  header(name, value, options) {\n    this._header.set(name, new HeaderParameter(name, value, options || {}));\n  }\n  /**\n   * Sets the body content, along with the content type\n   */\n  body(value, contentType = 'application/json') {\n    if (value instanceof Blob) {\n      this._bodyContentType = value.type;\n    } else {\n      this._bodyContentType = contentType;\n    }\n    if (this._bodyContentType === 'application/x-www-form-urlencoded' && value !== null && typeof value === 'object') {\n      // Handle URL-encoded data\n      const pairs = [];\n      for (const key of Object.keys(value)) {\n        let val = value[key];\n        if (!(val instanceof Array)) {\n          val = [val];\n        }\n        for (const v of val) {\n          const formValue = this.formDataValue(v);\n          if (formValue !== null) {\n            pairs.push([key, formValue]);\n          }\n        }\n      }\n      this._bodyContent = pairs.map(p => `${encodeURIComponent(p[0])}=${encodeURIComponent(p[1])}`).join('&');\n    } else if (this._bodyContentType === 'multipart/form-data') {\n      // Handle multipart form data\n      const formData = new FormData();\n      if (value !== null && value !== undefined) {\n        for (const key of Object.keys(value)) {\n          const val = value[key];\n          if (val instanceof Array) {\n            for (const v of val) {\n              const toAppend = this.formDataValue(v);\n              if (toAppend !== null) {\n                formData.append(key, toAppend);\n              }\n            }\n          } else {\n            const toAppend = this.formDataValue(val);\n            if (toAppend !== null) {\n              formData.set(key, toAppend);\n            }\n          }\n        }\n      }\n      this._bodyContent = formData;\n    } else {\n      // The body is the plain content\n      this._bodyContent = value;\n    }\n  }\n  formDataValue(value) {\n    if (value === null || value === undefined) {\n      return null;\n    }\n    if (value instanceof Blob) {\n      return value;\n    }\n    if (typeof value === 'object') {\n      return new Blob([JSON.stringify(value)], {\n        type: 'application/json'\n      });\n    }\n    return String(value);\n  }\n  /**\n   * Builds the request with the current set parameters\n   */\n  build(options) {\n    options = options || {};\n    // Path parameters\n    let path = this.operationPath;\n    for (const pathParam of this._path.values()) {\n      path = pathParam.append(path);\n    }\n    const url = this.rootUrl + path;\n    // Query parameters\n    let httpParams = new HttpParams({\n      encoder: ParameterCodecInstance\n    });\n    for (const queryParam of this._query.values()) {\n      httpParams = queryParam.append(httpParams);\n    }\n    // Header parameters\n    let httpHeaders = new HttpHeaders();\n    if (options.accept) {\n      httpHeaders = httpHeaders.append('Accept', options.accept);\n    }\n    for (const headerParam of this._header.values()) {\n      httpHeaders = headerParam.append(httpHeaders);\n    }\n    // Request content headers\n    if (this._bodyContentType && !(this._bodyContent instanceof FormData)) {\n      httpHeaders = httpHeaders.set('Content-Type', this._bodyContentType);\n    }\n    // Perform the request\n    return new HttpRequest(this.method.toUpperCase(), url, this._bodyContent, {\n      params: httpParams,\n      headers: httpHeaders,\n      responseType: options.responseType,\n      reportProgress: options.reportProgress,\n      context: options.context\n    });\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}