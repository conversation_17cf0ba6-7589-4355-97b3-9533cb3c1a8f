{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ISO/IEC 9797-1 Padding Method 2.\n   */\n  CryptoJS.pad.Iso97971 = {\n    pad: function (data, blockSize) {\n      // Add 0x80 byte\n      data.concat(CryptoJS.lib.WordArray.create([0x80000000], 1));\n\n      // Zero pad the rest\n      CryptoJS.pad.ZeroPadding.pad(data, blockSize);\n    },\n    unpad: function (data) {\n      // Remove zero padding\n      CryptoJS.pad.ZeroPadding.unpad(data);\n\n      // Remove one more byte -- the 0x80 byte\n      data.sigBytes--;\n    }\n  };\n  return CryptoJS.pad.Iso97971;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "pad", "Iso97971", "data", "blockSize", "concat", "lib", "WordArray", "create", "ZeroPadding", "unpad", "sigBytes"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/pad-iso97971.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * ISO/IEC 9797-1 Padding Method 2.\n\t */\n\tCryptoJS.pad.Iso97971 = {\n\t    pad: function (data, blockSize) {\n\t        // Add 0x80 byte\n\t        data.concat(CryptoJS.lib.WordArray.create([0x80000000], 1));\n\n\t        // Zero pad the rest\n\t        CryptoJS.pad.ZeroPadding.pad(data, blockSize);\n\t    },\n\n\t    unpad: function (data) {\n\t        // Remove zero padding\n\t        CryptoJS.pad.ZeroPadding.unpad(data);\n\n\t        // Remove one more byte -- the 0x80 byte\n\t        data.sigBytes--;\n\t    }\n\t};\n\n\n\treturn CryptoJS.pad.Iso97971;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAG,CAACC,QAAQ,GAAG;IACpBD,GAAG,EAAE,SAAAA,CAAUE,IAAI,EAAEC,SAAS,EAAE;MAC5B;MACAD,IAAI,CAACE,MAAM,CAACL,QAAQ,CAACM,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE3D;MACAR,QAAQ,CAACC,GAAG,CAACQ,WAAW,CAACR,GAAG,CAACE,IAAI,EAAEC,SAAS,CAAC;IACjD,CAAC;IAEDM,KAAK,EAAE,SAAAA,CAAUP,IAAI,EAAE;MACnB;MACAH,QAAQ,CAACC,GAAG,CAACQ,WAAW,CAACC,KAAK,CAACP,IAAI,CAAC;;MAEpC;MACAA,IAAI,CAACQ,QAAQ,EAAE;IACnB;EACJ,CAAC;EAGD,OAAOX,QAAQ,CAACC,GAAG,CAACC,QAAQ;AAE7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}