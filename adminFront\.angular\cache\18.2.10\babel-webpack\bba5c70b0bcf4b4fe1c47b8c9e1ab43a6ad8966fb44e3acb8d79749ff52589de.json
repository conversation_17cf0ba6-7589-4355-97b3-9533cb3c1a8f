{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction TemplateViewerComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 24);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"small\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u627E\\u5230 \", ctx_r1.filteredTemplates.length, \" \\u500B\\u7B26\\u5408\\u300C\", ctx_r1.searchKeyword, \"\\u300D\\u7684\\u6A21\\u677F \");\n  }\n}\nfunction TemplateViewerComponent_tr_28_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_28_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const tpl_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(tpl_r4.TemplateID && ctx_r1.onDeleteTemplate(tpl_r4.TemplateID));\n    });\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateViewerComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\")(5, \"span\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\", 27)(8, \"div\", 28)(9, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_28_Template_button_click_9_listener() {\n      const tpl_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectTemplate(tpl_r4));\n    });\n    i0.ɵɵelement(10, \"i\", 30);\n    i0.ɵɵtext(11, \" \\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, TemplateViewerComponent_tr_28_button_12_Template, 3, 0, \"button\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r4.TemplateName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tpl_r4.Description || \"\\u7121\\u63CF\\u8FF0\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", tpl_r4.TemplateID);\n  }\n}\nfunction TemplateViewerComponent_tr_29_small_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"small\", 26);\n    i0.ɵɵtext(1, \" \\u8ACB\\u5617\\u8A66\\u5176\\u4ED6\\u95DC\\u9375\\u5B57\\u6216 \");\n    i0.ɵɵelementStart(2, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_29_small_6_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSearch());\n    });\n    i0.ɵɵtext(3, \"\\u6E05\\u9664\\u641C\\u5C0B\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateViewerComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 34)(2, \"div\", 35);\n    i0.ɵɵelement(3, \"i\", 36);\n    i0.ɵɵelementStart(4, \"p\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TemplateViewerComponent_tr_29_small_6_Template, 4, 0, \"small\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.searchKeyword ? \"\\u627E\\u4E0D\\u5230\\u7B26\\u5408\\u689D\\u4EF6\\u7684\\u6A21\\u677F\" : \"\\u66AB\\u7121\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchKeyword);\n  }\n}\nfunction TemplateViewerComponent_div_30_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const detail_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", detail_r8.FieldName, \": \", detail_r8.FieldValue, \" \");\n  }\n}\nfunction TemplateViewerComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, TemplateViewerComponent_div_30_li_4_Template, 2, 2, \"li\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_div_30_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeTemplateDetail());\n    });\n    i0.ɵɵtext(6, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u6A21\\u677F\\u7D30\\u7BC0\\uFF1A\", ctx_r1.selectedTemplate.TemplateName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.currentTemplateDetails);\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n  }\n  ngOnInit() {\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 新增模板\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\",\n        templateDetails: \"templateDetails\",\n        sharedData: \"sharedData\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\",\n        saveTemplate: \"saveTemplate\",\n        deleteTemplate: \"deleteTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 31,\n      vars: 7,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"search-container\", \"mb-3\"], [1, \"input-group\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31\\u6216\\u63CF\\u8FF0...\", 1, \"form-control\", 3, \"ngModelChange\", \"input\", \"keyup.enter\", \"ngModel\"], [1, \"input-group-append\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"class\", \"btn btn-outline-secondary\", \"type\", \"button\", 3, \"click\", 4, \"ngIf\"], [1, \"template-list\"], [\"class\", \"search-results-info mb-2\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-hover\"], [1, \"thead-light\"], [\"width\", \"30%\"], [\"width\", \"50%\"], [\"width\", \"20%\", 1, \"text-center\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [\"class\", \"mt-3 p-2 border rounded bg-white\", 4, \"ngIf\"], [1, \"fas\", \"fa-times\"], [1, \"search-results-info\", \"mb-2\"], [1, \"text-muted\"], [1, \"text-center\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"title\", \"\\u67E5\\u770B\\u8A73\\u60C5\", 1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-danger\", \"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"\\u522A\\u9664\\u6A21\\u677F\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"3\", 1, \"text-center\", \"py-4\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\", \"fa-2x\", \"text-muted\", \"mb-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [\"href\", \"javascript:void(0)\", 3, \"click\"], [1, \"mt-3\", \"p-2\", \"border\", \"rounded\", \"bg-white\"], [4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\", 3);\n          i0.ɵɵtext(4, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_5_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateViewerComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function TemplateViewerComponent_Template_input_input_10_listener() {\n            return ctx.onSearch();\n          })(\"keyup.enter\", function TemplateViewerComponent_Template_input_keyup_enter_10_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(14, TemplateViewerComponent_button_14_Template, 2, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(15, \"div\", 13);\n          i0.ɵɵtemplate(16, TemplateViewerComponent_div_16_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementStart(17, \"div\", 15)(18, \"table\", 16)(19, \"thead\", 17)(20, \"tr\")(21, \"th\", 18);\n          i0.ɵɵtext(22, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"th\", 19);\n          i0.ɵɵtext(24, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 20);\n          i0.ɵɵtext(26, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"tbody\");\n          i0.ɵɵtemplate(28, TemplateViewerComponent_tr_28_Template, 13, 3, \"tr\", 21)(29, TemplateViewerComponent_tr_29_Template, 7, 2, \"tr\", 22);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(30, TemplateViewerComponent_div_30_Template, 7, 2, \"div\", 23);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchKeyword);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredTemplates)(\"ngForTrackBy\", ctx.trackByTemplateId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.filteredTemplates || ctx.filteredTemplates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTemplate);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateViewerComponent_button_14_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "filteredTemplates", "length", "searchKeyword", "TemplateViewerComponent_tr_28_button_12_Template_button_click_0_listener", "_r5", "tpl_r4", "$implicit", "TemplateID", "onDeleteTemplate", "TemplateViewerComponent_tr_28_Template_button_click_9_listener", "_r3", "onSelectTemplate", "ɵɵtemplate", "TemplateViewerComponent_tr_28_button_12_Template", "ɵɵtextInterpolate", "TemplateName", "Description", "ɵɵproperty", "TemplateViewerComponent_tr_29_small_6_Template_a_click_2_listener", "_r6", "TemplateViewerComponent_tr_29_small_6_Template", "ɵɵtextInterpolate1", "detail_r8", "FieldName", "FieldValue", "TemplateViewerComponent_div_30_li_4_Template", "TemplateViewerComponent_div_30_Template_button_click_5_listener", "_r7", "closeTemplateDetail", "selectedTemplate", "currentTemplateDetails", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "sharedData", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "showCreateTemplate", "newTemplateName", "newTemplateDesc", "ngOnInit", "updateFilteredTemplates", "ngOnChanges", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "createTemplate", "selected", "x", "alert", "details", "map", "RefID", "ID", "CRequirementID", "ModuleType", "CRequirement", "emit", "for<PERSON>ach", "onAddTemplate", "templateID", "confirm", "d", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_5_listener", "ɵɵtwoWayListener", "TemplateViewerComponent_Template_input_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "TemplateViewerComponent_Template_input_input_10_listener", "TemplateViewerComponent_Template_input_keyup_enter_10_listener", "TemplateViewerComponent_Template_button_click_12_listener", "TemplateViewerComponent_button_14_Template", "TemplateViewerComponent_div_16_Template", "TemplateViewerComponent_tr_28_Template", "TemplateViewerComponent_tr_29_Template", "TemplateViewerComponent_div_30_Template", "ɵɵtwoWayProperty", "trackByTemplateId", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  ngOnInit() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({ template, details });\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 新增模板\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header\">\r\n    <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n      <h5 class=\"mb-0\">模板管理</h5>\r\n      <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n        <i class=\"fas fa-plus mr-1\"></i>新增\r\n      </button>\r\n    </div>\r\n\r\n    <!-- 搜尋功能 -->\r\n    <div class=\"search-container mb-3\">\r\n      <div class=\"input-group\">\r\n        <input type=\"text\" class=\"form-control\" placeholder=\"搜尋模板名稱或描述...\" [(ngModel)]=\"searchKeyword\"\r\n          (input)=\"onSearch()\" (keyup.enter)=\"onSearch()\">\r\n        <div class=\"input-group-append\">\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"clearSearch()\" *ngIf=\"searchKeyword\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n\r\n\r\n  <div class=\"template-list\">\r\n    <!-- 搜尋結果統計 -->\r\n    <div class=\"search-results-info mb-2\" *ngIf=\"searchKeyword\">\r\n      <small class=\"text-muted\">\r\n        找到 {{ filteredTemplates.length }} 個符合「{{ searchKeyword }}」的模板\r\n      </small>\r\n    </div>\r\n\r\n    <div class=\"table-responsive\">\r\n      <table class=\"table table-striped table-hover\">\r\n        <thead class=\"thead-light\">\r\n          <tr>\r\n            <th width=\"30%\">模板名稱</th>\r\n            <th width=\"50%\">描述</th>\r\n            <th width=\"20%\" class=\"text-center\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let tpl of filteredTemplates; trackBy: trackByTemplateId\">\r\n            <td>\r\n              <strong>{{ tpl.TemplateName }}</strong>\r\n            </td>\r\n            <td>\r\n              <span class=\"text-muted\">{{ tpl.Description || '無描述' }}</span>\r\n            </td>\r\n            <td class=\"text-center\">\r\n              <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n                <button class=\"btn btn-info\" (click)=\"onSelectTemplate(tpl)\" title=\"查看詳情\">\r\n                  <i class=\"fas fa-eye\"></i> 查看\r\n                </button>\r\n                <button class=\"btn btn-danger\" (click)=\"tpl.TemplateID && onDeleteTemplate(tpl.TemplateID)\"\r\n                  *ngIf=\"tpl.TemplateID\" title=\"刪除模板\">\r\n                  <i class=\"fas fa-trash\"></i> 刪除\r\n                </button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"!filteredTemplates || filteredTemplates.length === 0\">\r\n            <td colspan=\"3\" class=\"text-center py-4\">\r\n              <div class=\"empty-state\">\r\n                <i class=\"fas fa-folder-open fa-2x text-muted mb-2\"></i>\r\n                <p class=\"text-muted mb-0\">\r\n                  {{ searchKeyword ? '找不到符合條件的模板' : '暫無模板' }}\r\n                </p>\r\n                <small class=\"text-muted\" *ngIf=\"searchKeyword\">\r\n                  請嘗試其他關鍵字或 <a href=\"javascript:void(0)\" (click)=\"clearSearch()\">清除搜尋</a>\r\n                </small>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 查看模板細節 -->\r\n  <div *ngIf=\"selectedTemplate\" class=\"mt-3 p-2 border rounded bg-white\">\r\n    <h6>模板細節：{{selectedTemplate!.TemplateName}}</h6>\r\n    <ul>\r\n      <li *ngFor=\"let detail of currentTemplateDetails\">\r\n        {{detail.FieldName}}: {{detail.FieldValue}}\r\n      </li>\r\n    </ul>\r\n    <button class=\"btn btn-secondary btn-sm\" (click)=\"closeTemplateDetail()\">關閉</button>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;ICgBlCC,EAAA,CAAAC,cAAA,iBAAsG;IAA9CD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IAWbX,EADF,CAAAC,cAAA,cAA4D,gBAChC;IACxBD,EAAA,CAAAY,MAAA,GACF;IACFZ,EADE,CAAAW,YAAA,EAAQ,EACJ;;;;IAFFX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,mBAAAR,MAAA,CAAAS,iBAAA,CAAAC,MAAA,+BAAAV,MAAA,CAAAW,aAAA,8BACF;;;;;;IAyBUjB,EAAA,CAAAC,cAAA,iBACsC;IADPD,EAAA,CAAAE,UAAA,mBAAAgB,yEAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAO,aAAA,GAAAc,SAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAE,UAAA,IAA2BhB,MAAA,CAAAiB,gBAAA,CAAAH,MAAA,CAAAE,UAAA,CAAgC;IAAA,EAAC;IAEzFtB,EAAA,CAAAU,SAAA,YAA4B;IAACV,EAAA,CAAAY,MAAA,qBAC/B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;;;;;;IAbXX,EAFJ,CAAAC,cAAA,SAAsE,SAChE,aACM;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAChCZ,EADgC,CAAAW,YAAA,EAAS,EACpC;IAEHX,EADF,CAAAC,cAAA,SAAI,eACuB;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IACzDZ,EADyD,CAAAW,YAAA,EAAO,EAC3D;IAGDX,EAFJ,CAAAC,cAAA,aAAwB,cAC2B,iBAC2B;IAA7CD,EAAA,CAAAE,UAAA,mBAAAsB,+DAAA;MAAA,MAAAJ,MAAA,GAAApB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAJ,SAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoB,gBAAA,CAAAN,MAAA,CAAqB;IAAA,EAAC;IAC1DpB,EAAA,CAAAU,SAAA,aAA0B;IAACV,EAAA,CAAAY,MAAA,sBAC7B;IAAAZ,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAA2B,UAAA,KAAAC,gDAAA,qBACsC;IAK5C5B,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAhBOX,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAA6B,iBAAA,CAAAT,MAAA,CAAAU,YAAA,CAAsB;IAGL9B,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA6B,iBAAA,CAAAT,MAAA,CAAAW,WAAA,yBAA8B;IAQlD/B,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAgC,UAAA,SAAAZ,MAAA,CAAAE,UAAA,CAAoB;;;;;;IAavBtB,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAY,MAAA,+DAAU;IAAAZ,EAAA,CAAAC,cAAA,YAAqD;IAAxBD,EAAA,CAAAE,UAAA,mBAAA+B,kEAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACT,EAAA,CAAAY,MAAA,+BAAI;IACrEZ,EADqE,CAAAW,YAAA,EAAI,EACjE;;;;;IAPVX,EAFJ,CAAAC,cAAA,SAAiE,aACtB,cACd;IACvBD,EAAA,CAAAU,SAAA,YAAwD;IACxDV,EAAA,CAAAC,cAAA,YAA2B;IACzBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAA2B,UAAA,IAAAQ,8CAAA,oBAAgD;IAKtDnC,EAFI,CAAAW,YAAA,EAAM,EACH,EACF;;;;IAPGX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAoC,kBAAA,MAAA9B,MAAA,CAAAW,aAAA,oGACF;IAC2BjB,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAgC,UAAA,SAAA1B,MAAA,CAAAW,aAAA,CAAmB;;;;;IAexDjB,EAAA,CAAAC,cAAA,SAAkD;IAChDD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAc,kBAAA,MAAAuB,SAAA,CAAAC,SAAA,QAAAD,SAAA,CAAAE,UAAA,MACF;;;;;;IAJFvC,EADF,CAAAC,cAAA,cAAuE,SACjE;IAAAD,EAAA,CAAAY,MAAA,GAAuC;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAA2B,UAAA,IAAAa,4CAAA,iBAAkD;IAGpDxC,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,iBAAyE;IAAhCD,EAAA,CAAAE,UAAA,mBAAAuC,gEAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,mBAAA,EAAqB;IAAA,EAAC;IAAC3C,EAAA,CAAAY,MAAA,mBAAE;IAC7EZ,EAD6E,CAAAW,YAAA,EAAS,EAChF;;;;IAPAX,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAoC,kBAAA,mCAAA9B,MAAA,CAAAsC,gBAAA,CAAAd,YAAA,KAAuC;IAElB9B,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAgC,UAAA,YAAA1B,MAAA,CAAAuC,sBAAA,CAAyB;;;AD5EtD,OAAM,MAAOC,uBAAuB;EAPpCC,YAAA;IAQW,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,UAAU,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAG,IAAItD,YAAY,EAAY;IAC1C,KAAAuD,cAAc,GAAG,IAAIvD,YAAY,EAAY;IAC7C,KAAAwD,YAAY,GAAG,IAAIxD,YAAY,EAAqD;IACpF,KAAAyD,cAAc,GAAG,IAAIzD,YAAY,EAAU;IAErD,KAAA0D,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAb,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAA3B,aAAa,GAAG,EAAE;IAClB,KAAAF,iBAAiB,GAAe,EAAE;;EAElC2C,QAAQA,CAAA;IACN,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAA,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC1C,aAAa,CAAC4C,IAAI,EAAE,EAAE;MAC9B,IAAI,CAAC9C,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACiC,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMc,OAAO,GAAG,IAAI,CAAC7C,aAAa,CAAC8C,WAAW,EAAE;MAChD,IAAI,CAAChD,iBAAiB,GAAG,IAAI,CAACiC,SAAS,CAACgB,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACnC,YAAY,CAACiC,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAAClC,WAAW,IAAIkC,QAAQ,CAAClC,WAAW,CAACgC,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACR,uBAAuB,EAAE;EAChC;EAEA;EACAlD,WAAWA,CAAA;IACT,IAAI,CAACQ,aAAa,GAAG,EAAE;IACvB,IAAI,CAAC0C,uBAAuB,EAAE;EAChC;EAEA;EACAS,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACnB,UAAU,IAAI,EAAE,EAAEc,MAAM,CAACM,CAAC,IAAIA,CAAC,CAACD,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACb,eAAe,IAAIa,QAAQ,CAACrD,MAAM,KAAK,CAAC,EAAE;MAClDuD,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMN,QAAQ,GAAa;MACzBnC,YAAY,EAAE,IAAI,CAAC0B,eAAe;MAClCzB,WAAW,EAAE,IAAI,CAAC0B;KACnB;IACD;IACA,MAAMe,OAAO,GAAqBH,QAAQ,CAACI,GAAG,CAACH,CAAC,KAAK;MACnDhD,UAAU,EAAE,CAAC;MAAE;MACfoD,KAAK,EAAEJ,CAAC,CAACK,EAAE,IAAIL,CAAC,CAACM,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAEP,CAAC,CAACO,UAAU,IAAI,aAAa;MACzCvC,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAE+B,CAAC,CAACQ;KACf,CAAC,CAAC;IACH,IAAI,CAACzB,YAAY,CAAC0B,IAAI,CAAC;MAAEd,QAAQ;MAAEO;IAAO,CAAE,CAAC;IAC7C,IAAI,CAACjB,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACP,UAAU,IAAI,EAAE,EAAE8B,OAAO,CAACV,CAAC,IAAIA,CAAC,CAACD,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACAY,aAAaA,CAAA;IACX,IAAI,CAAC9B,WAAW,CAAC4B,IAAI,EAAE;EACzB;EAEA;EACArD,gBAAgBA,CAACuC,QAAkB;IACjC,IAAI,CAACrB,gBAAgB,GAAGqB,QAAQ;IAChC,IAAI,CAACb,cAAc,CAAC2B,IAAI,CAACd,QAAQ,CAAC;EACpC;EAEA;EACA1C,gBAAgBA,CAAC2D,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC7B,cAAc,CAACyB,IAAI,CAACG,UAAU,CAAC;IACtC;EACF;EAEA;EACAvC,mBAAmBA,CAAA;IACjB,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIC,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACK,eAAe,CAACe,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAAC9D,UAAU,KAAK,IAAI,CAACsB,gBAAiB,CAACtB,UAAU,CAAC;EAC7F;;;uCAzGWwB,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAuC,SAAA;MAAAC,MAAA;QAAAtC,SAAA;QAAAC,eAAA;QAAAC,UAAA;MAAA;MAAAqC,OAAA;QAAApC,WAAA;QAAAC,cAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAkC,UAAA;MAAAC,QAAA,GAAAzF,EAAA,CAAA0F,oBAAA,EAAA1F,EAAA,CAAA2F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA7B,QAAA,WAAA8B,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR9BhG,EAHN,CAAAC,cAAA,aAAmC,aACG,aACkC,YACjD;UAAAD,EAAA,CAAAY,MAAA,+BAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UAC1BX,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAE,UAAA,mBAAAgG,yDAAA;YAAA,OAASD,GAAA,CAAAhB,aAAA,EAAe;UAAA,EAAC;UAC9DjF,EAAA,CAAAU,SAAA,WAAgC;UAAAV,EAAA,CAAAY,MAAA,oBAClC;UACFZ,EADE,CAAAW,YAAA,EAAS,EACL;UAKFX,EAFJ,CAAAC,cAAA,aAAmC,aACR,gBAE2B;UADiBD,EAAA,CAAAmG,gBAAA,2BAAAC,iEAAAC,MAAA;YAAArG,EAAA,CAAAsG,kBAAA,CAAAL,GAAA,CAAAhF,aAAA,EAAAoF,MAAA,MAAAJ,GAAA,CAAAhF,aAAA,GAAAoF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UACvErG,EAArB,CAAAE,UAAA,mBAAAqG,yDAAA;YAAA,OAASN,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC,yBAAAqC,+DAAA;YAAA,OAAgBP,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UADjDnE,EAAA,CAAAW,YAAA,EACkD;UAEhDX,EADF,CAAAC,cAAA,cAAgC,kBAC+C;UAArBD,EAAA,CAAAE,UAAA,mBAAAuG,0DAAA;YAAA,OAASR,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAC1EnE,EAAA,CAAAU,SAAA,aAA6B;UAC/BV,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAA2B,UAAA,KAAA+E,0CAAA,qBAAsG;UAM9G1G,EAHM,CAAAW,YAAA,EAAM,EACF,EACF,EACF;UAINX,EAAA,CAAAC,cAAA,eAA2B;UAEzBD,EAAA,CAAA2B,UAAA,KAAAgF,uCAAA,kBAA4D;UAUpD3G,EAJR,CAAAC,cAAA,eAA8B,iBACmB,iBAClB,UACrB,cACc;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACzBX,EAAA,CAAAC,cAAA,cAAgB;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACvBX,EAAA,CAAAC,cAAA,cAAoC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAE1CZ,EAF0C,CAAAW,YAAA,EAAK,EACxC,EACC;UACRX,EAAA,CAAAC,cAAA,aAAO;UAoBLD,EAnBA,CAAA2B,UAAA,KAAAiF,sCAAA,kBAAsE,KAAAC,sCAAA,iBAmBL;UAgBzE7G,EAHM,CAAAW,YAAA,EAAQ,EACF,EACJ,EACF;UAGNX,EAAA,CAAA2B,UAAA,KAAAmF,uCAAA,kBAAuE;UASzE9G,EAAA,CAAAW,YAAA,EAAM;;;UAjFqEX,EAAA,CAAAa,SAAA,IAA2B;UAA3Bb,EAAA,CAAA+G,gBAAA,YAAAd,GAAA,CAAAhF,aAAA,CAA2B;UAMXjB,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAAgC,UAAA,SAAAiE,GAAA,CAAAhF,aAAA,CAAmB;UAYnEjB,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAAgC,UAAA,SAAAiE,GAAA,CAAAhF,aAAA,CAAmB;UAgBhCjB,EAAA,CAAAa,SAAA,IAAsB;UAAAb,EAAtB,CAAAgC,UAAA,YAAAiE,GAAA,CAAAlF,iBAAA,CAAsB,iBAAAkF,GAAA,CAAAe,iBAAA,CAA0B;UAmB/DhH,EAAA,CAAAa,SAAA,EAA0D;UAA1Db,EAAA,CAAAgC,UAAA,UAAAiE,GAAA,CAAAlF,iBAAA,IAAAkF,GAAA,CAAAlF,iBAAA,CAAAC,MAAA,OAA0D;UAmBjEhB,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAgC,UAAA,SAAAiE,GAAA,CAAArD,gBAAA,CAAsB;;;qBD3ElB9C,YAAY,EAAAmH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpH,WAAW,EAAAqH,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}