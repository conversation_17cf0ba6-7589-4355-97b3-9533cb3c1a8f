# 模板查看器分頁功能說明

## 功能概述

為模板查看器組件添加了完整的分頁功能，支持處理大量資料的模板列表和模板詳情。

## 實現的功能

### 1. 模板列表分頁
- **每頁顯示**: 10個模板
- **分頁控制**: 上一頁/下一頁按鈕 + 頁碼導航
- **分頁資訊**: 顯示當前頁範圍和總數量
- **搜尋整合**: 搜尋結果也支持分頁

### 2. 模板詳情分頁
- **每頁顯示**: 5個詳情項目
- **分頁控制**: 上一頁/下一頁按鈕 + 頁碼導航
- **項目編號**: 自動計算跨頁的連續編號
- **頁面資訊**: 顯示當前頁/總頁數

## 技術實現

### 分頁數據結構
```typescript
templatePagination = {
  currentPage: 1,      // 當前頁碼
  pageSize: 10,        // 每頁顯示數量
  totalItems: 0,       // 總項目數
  totalPages: 0        // 總頁數
};

detailPagination = {
  currentPage: 1,
  pageSize: 5,
  totalItems: 0,
  totalPages: 0
};
```

### 核心方法
- `updateTemplatePagination()`: 更新模板分頁資訊
- `updatePaginatedTemplates()`: 更新當前頁顯示的模板
- `goToTemplatePage(page)`: 跳轉到指定頁面
- `getTemplatePageNumbers()`: 獲取頁碼導航數組

### API 整合準備
分頁功能已準備好與後端API整合：

```typescript
// 模板列表API (支持分頁)
GET /api/templates?moduleType={type}&page={page}&pageSize={size}&search={keyword}

// 模板詳情API (支持分頁)  
GET /api/templates/{id}/details?page={page}&pageSize={size}
```

## 用戶體驗優化

### 1. 視覺設計
- 簡潔的分頁控制器設計
- 清晰的分頁資訊顯示
- 響應式的按鈕狀態

### 2. 交互優化
- 禁用狀態的邊界處理
- 頁碼範圍智能顯示（當前頁±2）
- 平滑的頁面切換

### 3. 性能優化
- 只渲染當前頁數據
- TrackBy函數優化ngFor性能
- 智能的分頁計算

## 測試數據
為了測試分頁功能，組件現在生成：
- 25個模板（觸發模板列表分頁）
- 每個模板3-8個詳情項目（觸發詳情分頁）

## 使用方式

### 模板列表分頁
用戶可以：
1. 使用頁碼按鈕跳轉到特定頁面
2. 使用上一頁/下一頁按鈕導航
3. 查看當前頁範圍和總數量資訊

### 模板詳情分頁
當查看模板詳情時：
1. 自動重置到第一頁
2. 顯示當前頁/總頁數
3. 支持詳情項目的分頁瀏覽

## 後續擴展

### 可配置的分頁大小
```typescript
// 可以添加用戶自定義每頁顯示數量
pageSizeOptions = [5, 10, 20, 50];
```

### 跳轉到指定頁面
```typescript
// 可以添加直接輸入頁碼跳轉功能
goToPageInput: number;
```

### 無限滾動
```typescript
// 可以替換為無限滾動模式
infiniteScrollMode: boolean = false;
```
