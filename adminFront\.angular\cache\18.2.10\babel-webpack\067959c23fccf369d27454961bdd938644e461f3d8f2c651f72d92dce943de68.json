{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nexport class UtilityService {\n  constructor() {\n    this.BASE_FILE = environment.BASE_FILE;\n  }\n  isAbsoluteUrl(url) {\n    return /^(?:[a-z]+:)?\\/\\//i.test(url);\n  }\n  openFileNewTab(CFileUrl) {\n    if (CFileUrl) {\n      if (this.isAbsoluteUrl(CFileUrl)) {\n        window.open(CFileUrl, '_blank');\n      } else {\n        window.open(CFileUrl, '_blank');\n      }\n    }\n  }\n  openFileInNewTab(CFileUrl) {\n    if (CFileUrl) window.open(`${this.BASE_FILE}${CFileUrl}`, '_blank');\n  }\n  downloadFileFromUrl(filePath, fileName) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${this.BASE_FILE}${filePath}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  getFileNameFromUrl(url) {\n    const parts = url.split('/');\n    const fileName = parts.pop();\n    return fileName;\n  }\n  downloadFileFullUrl(filePath, fileName) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', `${filePath}`, true);\n    xhr.responseType = 'blob';\n    xhr.onload = () => {\n      if (xhr.status === 200) {\n        const url = window.URL.createObjectURL(xhr.response);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n      }\n    };\n    xhr.send();\n  }\n  downloadExcelFile(fileByte, fileName) {\n    const base64Data = fileByte;\n    const byteCharacters = atob(base64Data);\n    const byteNumbers = new Array(byteCharacters.length);\n    for (let i = 0; i < byteCharacters.length; i++) {\n      byteNumbers[i] = byteCharacters.charCodeAt(i);\n    }\n    const byteArray = new Uint8Array(byteNumbers);\n    const blob = new Blob([byteArray], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.setAttribute('download', `${fileName}` + '.xlsx');\n    document.body.appendChild(link);\n    link.click();\n    URL.revokeObjectURL(url);\n    document.body.removeChild(link);\n  }\n  base64ToBlob(base64, contentType = '') {\n    const base64Data = base64.split(',')[1] || base64;\n    // Decode base64 string\n    const byteCharacters = atob(base64Data);\n    // Create a byte array with length equal to the number of bytes in the string\n    const byteArrays = [];\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\n      const slice = byteCharacters.slice(offset, offset + 512);\n      const byteNumbers = new Array(slice.length);\n      for (let i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    return new Blob(byteArrays, {\n      type: contentType\n    });\n  }\n  getFileExtension(filename) {\n    var ext = /^.+\\.([^.]+)$/.exec(filename);\n    return ext == null ? '' : ext[1];\n  }\n  htmltoText(html) {\n    let text = html;\n    text = text.replace(/\\n/gi, \"\");\n    text = text.replace(/<style([\\s\\S]*?)<\\/style>/gi, \"\");\n    text = text.replace(/<script([\\s\\S]*?)<\\/script>/gi, \"\");\n    text = text.replace(/<a.*?href=\"(.*?)[\\?\\\"].*?>(.*?)<\\/a.*?>/gi, \" $2 $1 \");\n    text = text.replace(/<\\/div>/gi, \"\\n\\n\");\n    text = text.replace(/<\\/li>/gi, \"\\n\");\n    text = text.replace(/<li.*?>/gi, \"  *  \");\n    text = text.replace(/<\\/ul>/gi, \"\\n\\n\");\n    text = text.replace(/<\\/p>/gi, \"\\n\\n\");\n    text = text.replace(/<br\\s*[\\/]?>/gi, \"\\n\");\n    text = text.replace(/<[^>]+>/gi, \"\");\n    text = text.replace(/^\\s*/gim, \"\");\n    text = text.replace(/ ,/gi, \",\");\n    text = text.replace(/ +/gi, \" \");\n    text = text.replace(/\\n+/gi, \"\\n\\n\");\n    return text;\n  }\n  static {\n    this.ɵfac = function UtilityService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || UtilityService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilityService,\n      factory: UtilityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "UtilityService", "constructor", "BASE_FILE", "isAbsoluteUrl", "url", "test", "openFileNewTab", "CFileUrl", "window", "open", "openFileInNewTab", "downloadFileFromUrl", "filePath", "fileName", "xhr", "XMLHttpRequest", "responseType", "onload", "status", "URL", "createObjectURL", "response", "link", "document", "createElement", "href", "download", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "send", "getFileNameFromUrl", "parts", "split", "pop", "downloadFileFullUrl", "downloadExcelFile", "fileByte", "base64Data", "byteCharacters", "atob", "byteNumbers", "Array", "length", "i", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "type", "setAttribute", "base64ToBlob", "base64", "contentType", "byteArrays", "offset", "slice", "push", "getFileExtension", "filename", "ext", "exec", "htmltoText", "html", "text", "replace", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\services\\utility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { environment } from '../../../environments/environment';\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilityService {\r\n  readonly BASE_FILE = environment.BASE_FILE;\r\n\r\n  constructor() { }\r\n\r\n  isAbsoluteUrl(url: string): boolean {\r\n    return /^(?:[a-z]+:)?\\/\\//i.test(url);\r\n  }\r\n\r\n  openFileNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      if (this.isAbsoluteUrl(CFileUrl)) {\r\n        window.open(CFileUrl, '_blank');\r\n      } else {\r\n        window.open(CFileUrl, '_blank');\r\n      }\r\n    }\r\n  }\r\n\r\n  openFileInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) window.open(`${this.BASE_FILE}${CFileUrl}`, '_blank');\r\n  }\r\n\r\n  downloadFileFromUrl(filePath: string, fileName: string) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${this.BASE_FILE}${filePath}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = fileName;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  getFileNameFromUrl(url: string) {\r\n    const parts = url.split('/');\r\n    const fileName = parts.pop();\r\n    return fileName;\r\n  }\r\n\r\n  downloadFileFullUrl(filePath: string, fileName: string) {\r\n    const xhr = new XMLHttpRequest();\r\n    xhr.open('GET', `${filePath}`\r\n      , true);\r\n    xhr.responseType = 'blob';\r\n    xhr.onload = () => {\r\n      if (xhr.status === 200) {\r\n        const url = window.URL.createObjectURL(xhr.response);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = fileName!;\r\n        link.style.display = 'none';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n        window.URL.revokeObjectURL(url);\r\n      }\r\n    };\r\n    xhr.send();\r\n  }\r\n\r\n  downloadExcelFile(fileByte: any, fileName: string) {\r\n    const base64Data = fileByte;\r\n    const byteCharacters = atob(base64Data);\r\n    const byteNumbers = new Array(byteCharacters.length);\r\n    for (let i = 0; i < byteCharacters.length; i++) {\r\n      byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n    }\r\n    const byteArray = new Uint8Array(byteNumbers);\r\n    const blob = new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.setAttribute('download', `${fileName}` + '.xlsx');\r\n    document.body.appendChild(link);\r\n    link.click();\r\n\r\n    URL.revokeObjectURL(url);\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  base64ToBlob(base64: string, contentType: string = ''): Blob {\r\n    const base64Data = base64.split(',')[1] || base64;\r\n    // Decode base64 string\r\n    const byteCharacters = atob(base64Data);\r\n    // Create a byte array with length equal to the number of bytes in the string\r\n    const byteArrays: Uint8Array[] = [];\r\n    for (let offset = 0; offset < byteCharacters.length; offset += 512) {\r\n      const slice = byteCharacters.slice(offset, offset + 512);\r\n      const byteNumbers = new Array(slice.length);\r\n      for (let i = 0; i < slice.length; i++) {\r\n        byteNumbers[i] = slice.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      byteArrays.push(byteArray);\r\n    }\r\n    return new Blob(byteArrays, { type: contentType });\r\n  }\r\n\r\n  getFileExtension(filename: string) {\r\n    var ext = /^.+\\.([^.]+)$/.exec(filename);\r\n    return ext == null ? '' : ext[1];\r\n  }\r\n\r\n  htmltoText(html: string) {\r\n    let text = html;\r\n    text = text.replace(/\\n/gi, \"\");\r\n    text = text.replace(/<style([\\s\\S]*?)<\\/style>/gi, \"\");\r\n    text = text.replace(/<script([\\s\\S]*?)<\\/script>/gi, \"\");\r\n    text = text.replace(/<a.*?href=\"(.*?)[\\?\\\"].*?>(.*?)<\\/a.*?>/gi, \" $2 $1 \");\r\n    text = text.replace(/<\\/div>/gi, \"\\n\\n\");\r\n    text = text.replace(/<\\/li>/gi, \"\\n\");\r\n    text = text.replace(/<li.*?>/gi, \"  *  \");\r\n    text = text.replace(/<\\/ul>/gi, \"\\n\\n\");\r\n    text = text.replace(/<\\/p>/gi, \"\\n\\n\");\r\n    text = text.replace(/<br\\s*[\\/]?>/gi, \"\\n\");\r\n    text = text.replace(/<[^>]+>/gi, \"\");\r\n    text = text.replace(/^\\s*/gim, \"\");\r\n    text = text.replace(/ ,/gi, \",\");\r\n    text = text.replace(/ +/gi, \" \");\r\n    text = text.replace(/\\n+/gi, \"\\n\\n\");\r\n    return text;\r\n  };\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,mCAAmC;;AAI/D,OAAM,MAAOC,cAAc;EAGzBC,YAAA;IAFS,KAAAC,SAAS,GAAGH,WAAW,CAACG,SAAS;EAE1B;EAEhBC,aAAaA,CAACC,GAAW;IACvB,OAAO,oBAAoB,CAACC,IAAI,CAACD,GAAG,CAAC;EACvC;EAEAE,cAAcA,CAACC,QAAc;IAC3B,IAAIA,QAAQ,EAAE;MACZ,IAAI,IAAI,CAACJ,aAAa,CAACI,QAAQ,CAAC,EAAE;QAChCC,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;MACjC,CAAC,MAAM;QACLC,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;MACjC;IACF;EACF;EAEAG,gBAAgBA,CAACH,QAAc;IAC7B,IAAIA,QAAQ,EAAEC,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACP,SAAS,GAAGK,QAAQ,EAAE,EAAE,QAAQ,CAAC;EACrE;EAEAI,mBAAmBA,CAACC,QAAgB,EAAEC,QAAgB;IACpD,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACL,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAACP,SAAS,GAAGU,QAAQ,EAAE,EAC1C,IAAI,CAAC;IACTE,GAAG,CAACE,YAAY,GAAG,MAAM;IACzBF,GAAG,CAACG,MAAM,GAAG,MAAK;MAChB,IAAIH,GAAG,CAACI,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMd,GAAG,GAAGI,MAAM,CAACW,GAAG,CAACC,eAAe,CAACN,GAAG,CAACO,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGrB,GAAG;QACfkB,IAAI,CAACI,QAAQ,GAAGb,QAAQ;QACxBS,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,EAAE;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;QAC/Bd,MAAM,CAACW,GAAG,CAACc,eAAe,CAAC7B,GAAG,CAAC;MACjC;IACF,CAAC;IACDU,GAAG,CAACoB,IAAI,EAAE;EACZ;EAEAC,kBAAkBA,CAAC/B,GAAW;IAC5B,MAAMgC,KAAK,GAAGhC,GAAG,CAACiC,KAAK,CAAC,GAAG,CAAC;IAC5B,MAAMxB,QAAQ,GAAGuB,KAAK,CAACE,GAAG,EAAE;IAC5B,OAAOzB,QAAQ;EACjB;EAEA0B,mBAAmBA,CAAC3B,QAAgB,EAAEC,QAAgB;IACpD,MAAMC,GAAG,GAAG,IAAIC,cAAc,EAAE;IAChCD,GAAG,CAACL,IAAI,CAAC,KAAK,EAAE,GAAGG,QAAQ,EAAE,EACzB,IAAI,CAAC;IACTE,GAAG,CAACE,YAAY,GAAG,MAAM;IACzBF,GAAG,CAACG,MAAM,GAAG,MAAK;MAChB,IAAIH,GAAG,CAACI,MAAM,KAAK,GAAG,EAAE;QACtB,MAAMd,GAAG,GAAGI,MAAM,CAACW,GAAG,CAACC,eAAe,CAACN,GAAG,CAACO,QAAQ,CAAC;QACpD,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGrB,GAAG;QACfkB,IAAI,CAACI,QAAQ,GAAGb,QAAS;QACzBS,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,EAAE;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;QAC/Bd,MAAM,CAACW,GAAG,CAACc,eAAe,CAAC7B,GAAG,CAAC;MACjC;IACF,CAAC;IACDU,GAAG,CAACoB,IAAI,EAAE;EACZ;EAEAM,iBAAiBA,CAACC,QAAa,EAAE5B,QAAgB;IAC/C,MAAM6B,UAAU,GAAGD,QAAQ;IAC3B,MAAME,cAAc,GAAGC,IAAI,CAACF,UAAU,CAAC;IACvC,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACI,MAAM,CAAC;IACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC9CH,WAAW,CAACG,CAAC,CAAC,GAAGL,cAAc,CAACM,UAAU,CAACD,CAAC,CAAC;IAC/C;IACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;IAC7C,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAmE,CAAE,CAAC;IACjH,MAAMlD,GAAG,GAAGe,GAAG,CAACC,eAAe,CAACgC,IAAI,CAAC;IACrC,MAAM9B,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGrB,GAAG;IACfkB,IAAI,CAACiC,YAAY,CAAC,UAAU,EAAE,GAAG1C,QAAQ,EAAE,GAAG,OAAO,CAAC;IACtDU,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,EAAE;IAEZZ,GAAG,CAACc,eAAe,CAAC7B,GAAG,CAAC;IACxBmB,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC;EAEAkC,YAAYA,CAACC,MAAc,EAAEC,WAAA,GAAsB,EAAE;IACnD,MAAMhB,UAAU,GAAGe,MAAM,CAACpB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIoB,MAAM;IACjD;IACA,MAAMd,cAAc,GAAGC,IAAI,CAACF,UAAU,CAAC;IACvC;IACA,MAAMiB,UAAU,GAAiB,EAAE;IACnC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGjB,cAAc,CAACI,MAAM,EAAEa,MAAM,IAAI,GAAG,EAAE;MAClE,MAAMC,KAAK,GAAGlB,cAAc,CAACkB,KAAK,CAACD,MAAM,EAAEA,MAAM,GAAG,GAAG,CAAC;MACxD,MAAMf,WAAW,GAAG,IAAIC,KAAK,CAACe,KAAK,CAACd,MAAM,CAAC;MAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,KAAK,CAACd,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrCH,WAAW,CAACG,CAAC,CAAC,GAAGa,KAAK,CAACZ,UAAU,CAACD,CAAC,CAAC;MACtC;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;MAC7Cc,UAAU,CAACG,IAAI,CAACZ,SAAS,CAAC;IAC5B;IACA,OAAO,IAAIG,IAAI,CAACM,UAAU,EAAE;MAAEL,IAAI,EAAEI;IAAW,CAAE,CAAC;EACpD;EAEAK,gBAAgBA,CAACC,QAAgB;IAC/B,IAAIC,GAAG,GAAG,eAAe,CAACC,IAAI,CAACF,QAAQ,CAAC;IACxC,OAAOC,GAAG,IAAI,IAAI,GAAG,EAAE,GAAGA,GAAG,CAAC,CAAC,CAAC;EAClC;EAEAE,UAAUA,CAACC,IAAY;IACrB,IAAIC,IAAI,GAAGD,IAAI;IACfC,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IAC/BD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;IACtDD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,+BAA+B,EAAE,EAAE,CAAC;IACxDD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,2CAA2C,EAAE,SAAS,CAAC;IAC3ED,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IACxCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;IACrCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC;IACzCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;IACvCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;IACtCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC;IAC3CD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;IACpCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAClCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAChCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAChCD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;IACpC,OAAOD,IAAI;EACb;;;uCApIWrE,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAAuE,OAAA,EAAdvE,cAAc,CAAAwE,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}