{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Greek [el]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mehiel\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  function isFunction(input) {\n    return typeof Function !== 'undefined' && input instanceof Function || Object.prototype.toString.call(input) === '[object Function]';\n  }\n  var el = moment.defineLocale('el', {\n    monthsNominativeEl: 'Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος'.split('_'),\n    monthsGenitiveEl: 'Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου'.split('_'),\n    months: function (momentToFormat, format) {\n      if (!momentToFormat) {\n        return this._monthsNominativeEl;\n      } else if (typeof format === 'string' && /D/.test(format.substring(0, format.indexOf('MMMM')))) {\n        // if there is a day number before 'MMMM'\n        return this._monthsGenitiveEl[momentToFormat.month()];\n      } else {\n        return this._monthsNominativeEl[momentToFormat.month()];\n      }\n    },\n    monthsShort: 'Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ'.split('_'),\n    weekdays: 'Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο'.split('_'),\n    weekdaysShort: 'Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ'.split('_'),\n    weekdaysMin: 'Κυ_Δε_Τρ_Τε_Πε_Πα_Σα'.split('_'),\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? 'μμ' : 'ΜΜ';\n      } else {\n        return isLower ? 'πμ' : 'ΠΜ';\n      }\n    },\n    isPM: function (input) {\n      return (input + '').toLowerCase()[0] === 'μ';\n    },\n    meridiemParse: /[ΠΜ]\\.?Μ?\\.?/i,\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY h:mm A',\n      LLLL: 'dddd, D MMMM YYYY h:mm A'\n    },\n    calendarEl: {\n      sameDay: '[Σήμερα {}] LT',\n      nextDay: '[Αύριο {}] LT',\n      nextWeek: 'dddd [{}] LT',\n      lastDay: '[Χθες {}] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 6:\n            return '[το προηγούμενο] dddd [{}] LT';\n          default:\n            return '[την προηγούμενη] dddd [{}] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    calendar: function (key, mom) {\n      var output = this._calendarEl[key],\n        hours = mom && mom.hours();\n      if (isFunction(output)) {\n        output = output.apply(mom);\n      }\n      return output.replace('{}', hours % 12 === 1 ? 'στη' : 'στις');\n    },\n    relativeTime: {\n      future: 'σε %s',\n      past: '%s πριν',\n      s: 'λίγα δευτερόλεπτα',\n      ss: '%d δευτερόλεπτα',\n      m: 'ένα λεπτό',\n      mm: '%d λεπτά',\n      h: 'μία ώρα',\n      hh: '%d ώρες',\n      d: 'μία μέρα',\n      dd: '%d μέρες',\n      M: 'ένας μήνας',\n      MM: '%d μήνες',\n      y: 'ένας χρόνος',\n      yy: '%d χρόνια'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}η/,\n    ordinal: '%dη',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4st is the first week of the year.\n    }\n  });\n  return el;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "isFunction", "input", "Function", "Object", "prototype", "toString", "call", "el", "defineLocale", "monthsNominativeEl", "split", "monthsGenitiveEl", "months", "momentToFormat", "format", "_monthsNominativeEl", "test", "substring", "indexOf", "_monthsGenitiveEl", "month", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "meridiem", "hours", "minutes", "isLower", "isPM", "toLowerCase", "meridiemParse", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendarEl", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "day", "same<PERSON><PERSON><PERSON>", "calendar", "key", "mom", "output", "_calendarEl", "apply", "replace", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/el.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Greek [el]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/mehiel\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    function isFunction(input) {\n        return (\n            (typeof Function !== 'undefined' && input instanceof Function) ||\n            Object.prototype.toString.call(input) === '[object Function]'\n        );\n    }\n\n    var el = moment.defineLocale('el', {\n        monthsNominativeEl:\n            'Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος'.split(\n                '_'\n            ),\n        monthsGenitiveEl:\n            'Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου'.split(\n                '_'\n            ),\n        months: function (momentToFormat, format) {\n            if (!momentToFormat) {\n                return this._monthsNominativeEl;\n            } else if (\n                typeof format === 'string' &&\n                /D/.test(format.substring(0, format.indexOf('MMMM')))\n            ) {\n                // if there is a day number before 'MMMM'\n                return this._monthsGenitiveEl[momentToFormat.month()];\n            } else {\n                return this._monthsNominativeEl[momentToFormat.month()];\n            }\n        },\n        monthsShort: 'Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ'.split('_'),\n        weekdays: 'Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο'.split(\n            '_'\n        ),\n        weekdaysShort: 'Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ'.split('_'),\n        weekdaysMin: 'Κυ_Δε_Τρ_Τε_Πε_Πα_Σα'.split('_'),\n        meridiem: function (hours, minutes, isLower) {\n            if (hours > 11) {\n                return isLower ? 'μμ' : 'ΜΜ';\n            } else {\n                return isLower ? 'πμ' : 'ΠΜ';\n            }\n        },\n        isPM: function (input) {\n            return (input + '').toLowerCase()[0] === 'μ';\n        },\n        meridiemParse: /[ΠΜ]\\.?Μ?\\.?/i,\n        longDateFormat: {\n            LT: 'h:mm A',\n            LTS: 'h:mm:ss A',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY h:mm A',\n            LLLL: 'dddd, D MMMM YYYY h:mm A',\n        },\n        calendarEl: {\n            sameDay: '[Σήμερα {}] LT',\n            nextDay: '[Αύριο {}] LT',\n            nextWeek: 'dddd [{}] LT',\n            lastDay: '[Χθες {}] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 6:\n                        return '[το προηγούμενο] dddd [{}] LT';\n                    default:\n                        return '[την προηγούμενη] dddd [{}] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        calendar: function (key, mom) {\n            var output = this._calendarEl[key],\n                hours = mom && mom.hours();\n            if (isFunction(output)) {\n                output = output.apply(mom);\n            }\n            return output.replace('{}', hours % 12 === 1 ? 'στη' : 'στις');\n        },\n        relativeTime: {\n            future: 'σε %s',\n            past: '%s πριν',\n            s: 'λίγα δευτερόλεπτα',\n            ss: '%d δευτερόλεπτα',\n            m: 'ένα λεπτό',\n            mm: '%d λεπτά',\n            h: 'μία ώρα',\n            hh: '%d ώρες',\n            d: 'μία μέρα',\n            dd: '%d μέρες',\n            M: 'ένας μήνας',\n            MM: '%d μήνες',\n            y: 'ένας χρόνος',\n            yy: '%d χρόνια',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}η/,\n        ordinal: '%dη',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4st is the first week of the year.\n        },\n    });\n\n    return el;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,SAASC,UAAUA,CAACC,KAAK,EAAE;IACvB,OACK,OAAOC,QAAQ,KAAK,WAAW,IAAID,KAAK,YAAYC,QAAQ,IAC7DC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACL,KAAK,CAAC,KAAK,mBAAmB;EAErE;EAEA,IAAIM,EAAE,GAAGR,MAAM,CAACS,YAAY,CAAC,IAAI,EAAE;IAC/BC,kBAAkB,EACd,oHAAoH,CAACC,KAAK,CACtH,GACJ,CAAC;IACLC,gBAAgB,EACZ,oHAAoH,CAACD,KAAK,CACtH,GACJ,CAAC;IACLE,MAAM,EAAE,SAAAA,CAAUC,cAAc,EAAEC,MAAM,EAAE;MACtC,IAAI,CAACD,cAAc,EAAE;QACjB,OAAO,IAAI,CAACE,mBAAmB;MACnC,CAAC,MAAM,IACH,OAAOD,MAAM,KAAK,QAAQ,IAC1B,GAAG,CAACE,IAAI,CAACF,MAAM,CAACG,SAAS,CAAC,CAAC,EAAEH,MAAM,CAACI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EACvD;QACE;QACA,OAAO,IAAI,CAACC,iBAAiB,CAACN,cAAc,CAACO,KAAK,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACH,OAAO,IAAI,CAACL,mBAAmB,CAACF,cAAc,CAACO,KAAK,CAAC,CAAC,CAAC;MAC3D;IACJ,CAAC;IACDC,WAAW,EAAE,mDAAmD,CAACX,KAAK,CAAC,GAAG,CAAC;IAC3EY,QAAQ,EAAE,wDAAwD,CAACZ,KAAK,CACpE,GACJ,CAAC;IACDa,aAAa,EAAE,6BAA6B,CAACb,KAAK,CAAC,GAAG,CAAC;IACvDc,WAAW,EAAE,sBAAsB,CAACd,KAAK,CAAC,GAAG,CAAC;IAC9Ce,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,IAAI,GAAG,IAAI;MAChC;IACJ,CAAC;IACDC,IAAI,EAAE,SAAAA,CAAU5B,KAAK,EAAE;MACnB,OAAO,CAACA,KAAK,GAAG,EAAE,EAAE6B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;IAChD,CAAC;IACDC,aAAa,EAAE,eAAe;IAC9BC,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,oBAAoB;MACzBC,IAAI,EAAE;IACV,CAAC;IACDC,UAAU,EAAE;MACRC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,cAAc;MACxBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,+BAA+B;UAC1C;YACI,OAAO,gCAAgC;QAC/C;MACJ,CAAC;MACDC,QAAQ,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAE;MAC1B,IAAIC,MAAM,GAAG,IAAI,CAACC,WAAW,CAACH,GAAG,CAAC;QAC9BtB,KAAK,GAAGuB,GAAG,IAAIA,GAAG,CAACvB,KAAK,CAAC,CAAC;MAC9B,IAAI1B,UAAU,CAACkD,MAAM,CAAC,EAAE;QACpBA,MAAM,GAAGA,MAAM,CAACE,KAAK,CAACH,GAAG,CAAC;MAC9B;MACA,OAAOC,MAAM,CAACG,OAAO,CAAC,IAAI,EAAE3B,KAAK,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC;IAClE,CAAC;IACD4B,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,mBAAmB;MACtBC,EAAE,EAAE,iBAAiB;MACrBC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,UAAU;IAClCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOlE,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}