{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Tajik [tg]\n//! author : <PERSON><PERSON>. : https://github.com/orif-jr\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var suffixes = {\n    0: '-ум',\n    1: '-ум',\n    2: '-юм',\n    3: '-юм',\n    4: '-ум',\n    5: '-ум',\n    6: '-ум',\n    7: '-ум',\n    8: '-ум',\n    9: '-ум',\n    10: '-ум',\n    12: '-ум',\n    13: '-ум',\n    20: '-ум',\n    30: '-юм',\n    40: '-ум',\n    50: '-ум',\n    60: '-ум',\n    70: '-ум',\n    80: '-ум',\n    90: '-ум',\n    100: '-ум'\n  };\n  var tg = moment.defineLocale('tg', {\n    months: {\n      format: 'январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри'.split('_'),\n      standalone: 'январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр'.split('_')\n    },\n    monthsShort: 'янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек'.split('_'),\n    weekdays: 'якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе'.split('_'),\n    weekdaysShort: 'яшб_дшб_сшб_чшб_пшб_ҷум_шнб'.split('_'),\n    weekdaysMin: 'яш_дш_сш_чш_пш_ҷм_шб'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Имрӯз соати] LT',\n      nextDay: '[Фардо соати] LT',\n      lastDay: '[Дирӯз соати] LT',\n      nextWeek: 'dddd[и] [ҳафтаи оянда соати] LT',\n      lastWeek: 'dddd[и] [ҳафтаи гузашта соати] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'баъди %s',\n      past: '%s пеш',\n      s: 'якчанд сония',\n      m: 'як дақиқа',\n      mm: '%d дақиқа',\n      h: 'як соат',\n      hh: '%d соат',\n      d: 'як рӯз',\n      dd: '%d рӯз',\n      M: 'як моҳ',\n      MM: '%d моҳ',\n      y: 'як сол',\n      yy: '%d сол'\n    },\n    meridiemParse: /шаб|субҳ|рӯз|бегоҳ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'шаб') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'субҳ') {\n        return hour;\n      } else if (meridiem === 'рӯз') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'бегоҳ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'шаб';\n      } else if (hour < 11) {\n        return 'субҳ';\n      } else if (hour < 16) {\n        return 'рӯз';\n      } else if (hour < 19) {\n        return 'бегоҳ';\n      } else {\n        return 'шаб';\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}-(ум|юм)/,\n    ordinal: function (number) {\n      var a = number % 10,\n        b = number >= 100 ? 100 : null;\n      return number + (suffixes[number] || suffixes[a] || suffixes[b]);\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 7 // The week that contains Jan 1th is the first week of the year.\n    }\n  });\n  return tg;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}