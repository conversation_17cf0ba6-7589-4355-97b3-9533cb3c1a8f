{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { TemperatureHumidityData } from '../data/temperature-humidity';\nimport * as i0 from \"@angular/core\";\nexport class TemperatureHumidityService extends TemperatureHumidityData {\n  constructor() {\n    super(...arguments);\n    this.temperatureDate = {\n      value: 24,\n      min: 12,\n      max: 30\n    };\n    this.humidityDate = {\n      value: 87,\n      min: 0,\n      max: 100\n    };\n  }\n  getTemperatureData() {\n    return observableOf(this.temperatureDate);\n  }\n  getHumidityData() {\n    return observableOf(this.humidityDate);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵTemperatureHumidityService_BaseFactory;\n      return function TemperatureHumidityService_Factory(__ngFactoryType__) {\n        return (ɵTemperatureHumidityService_BaseFactory || (ɵTemperatureHumidityService_BaseFactory = i0.ɵɵgetInheritedFactory(TemperatureHumidityService)))(__ngFactoryType__ || TemperatureHumidityService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TemperatureHumidityService,\n      factory: TemperatureHumidityService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "TemperatureHumidityData", "TemperatureHumidityService", "constructor", "temperatureDate", "value", "min", "max", "humidityDate", "getTemperatureData", "getHumidityData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\temperature-humidity.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { TemperatureHumidityData, Temperature } from '../data/temperature-humidity';\r\n\r\n@Injectable()\r\nexport class TemperatureHumidityService extends TemperatureHumidityData {\r\n\r\n  private temperatureDate: Temperature = {\r\n    value: 24,\r\n    min: 12,\r\n    max: 30,\r\n  };\r\n\r\n  private humidityDate: Temperature = {\r\n    value: 87,\r\n    min: 0,\r\n    max: 100,\r\n  };\r\n\r\n  getTemperatureData(): Observable<Temperature> {\r\n    return observableOf(this.temperatureDate);\r\n  }\r\n\r\n  getHumidityData(): Observable<Temperature> {\r\n    return observableOf(this.humidityDate);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AACtD,SAASC,uBAAuB,QAAqB,8BAA8B;;AAGnF,OAAM,MAAOC,0BAA2B,SAAQD,uBAAuB;EADvEE,YAAA;;IAGU,KAAAC,eAAe,GAAgB;MACrCC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,EAAE;MACPC,GAAG,EAAE;KACN;IAEO,KAAAC,YAAY,GAAgB;MAClCH,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE;KACN;;EAEDE,kBAAkBA,CAAA;IAChB,OAAOT,YAAY,CAAC,IAAI,CAACI,eAAe,CAAC;EAC3C;EAEAM,eAAeA,CAAA;IACb,OAAOV,YAAY,CAAC,IAAI,CAACQ,YAAY,CAAC;EACxC;;;;;+HApBWN,0BAA0B,IAAAS,iBAAA,IAA1BT,0BAA0B;MAAA;IAAA;EAAA;;;aAA1BA,0BAA0B;MAAAU,OAAA,EAA1BV,0BAA0B,CAAAW;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}