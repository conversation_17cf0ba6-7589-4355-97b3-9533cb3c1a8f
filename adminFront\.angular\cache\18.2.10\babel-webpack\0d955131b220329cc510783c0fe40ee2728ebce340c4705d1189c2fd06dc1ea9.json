{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { map } from 'rxjs/operators';\nlet QuotationService = class QuotationService {\n  constructor(apiQuotationService) {\n    this.apiQuotationService = apiQuotationService;\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單\n  saveQuotation(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  }\n  // 刪除報價單\n  deleteQuotation(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({\n      body: request\n    });\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      cQuotationID: quotationId,\n      cHouseID: item.cHouseID,\n      cItemName: item.cItemName,\n      cUnitPrice: item.cUnitPrice,\n      cCount: item.cCount,\n      cStatus: item.cStatus || 1,\n      cVersion: item.cVersion || 1,\n      cIsDeafult: item.cIsDefault\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.deleteQuotation(quotationId).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: []\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n};\nQuotationService = __decorate([Injectable({\n  providedIn: 'root'\n})], QuotationService);\nexport { QuotationService };", "map": {"version": 3, "names": ["Injectable", "map", "QuotationService", "constructor", "apiQuotationService", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotation", "quotation", "apiQuotationSaveDataPost$Json", "deleteQuotation", "apiQuotationDeleteDataPost$Json", "getDefaultQuotationItems", "pageIndex", "pageSize", "pipe", "response", "success", "statusCode", "message", "data", "entries", "updateQuotationItem", "item", "saveData", "cItemName", "cUnitPrice", "cCount", "cStatus", "cVersion", "cIs<PERSON><PERSON><PERSON>t", "cIsDefault", "StatusCode", "Message", "deleteQuotationItem", "exportQuotation", "Error", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport { \r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n\r\n  constructor(private apiQuotationService: ApiQuotationService) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n\r\n  // 儲存報價單\r\n  saveQuotation(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }\r\n  // 刪除報價單\r\n  deleteQuotation(quotationId: number): Observable<any> {\r\n    const request: DeleteQuotationRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({ body: request });\r\n  }\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = { \r\n      pageIndex: 0, \r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      cQuotationID: quotationId,\r\n      cHouseID: item.cHouseID,\r\n      cItemName: item.cItemName,\r\n      cUnitPrice: item.cUnitPrice,\r\n      cCount: item.cCount,\r\n      cStatus: item.cStatus || 1,\r\n      cVersion: item.cVersion || 1,\r\n      cIsDeafult: item.cIsDefault,\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.deleteQuotation(quotationId).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,GAAG,QAAQ,gBAAgB;AAc7B,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAE3BC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;EAAyB;EAChE;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACF,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACN,mBAAmB,CAACQ,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EAEA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACV,mBAAmB,CAACY,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EAEA;EACAW,aAAaA,CAACC,SAA4B;IACxC,OAAO,IAAI,CAACd,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF;EACA;EACAE,eAAeA,CAACV,WAAmB;IACjC,MAAMJ,OAAO,GAA2B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACrE,OAAO,IAAI,CAACN,mBAAmB,CAACiB,+BAA+B,CAAC;MAAEb,IAAI,EAAEF;IAAO,CAAE,CAAC;EACpF;EACA;EACAgB,wBAAwBA,CAAA;IACtB;IACA,MAAMhB,OAAO,GAA4B;MACvCiB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAACpB,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACmB,IAAI,CAClFxB,GAAG,CAACyB,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACE,UAAU,KAAK,CAAC;QAAE;QACpCC,OAAO,EAAEH,QAAQ,CAACG,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAEJ,QAAQ,CAACK,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH;EACA;EACAC,mBAAmBA,CAACtB,WAAmB,EAAEuB,IAAmB;IAC1D,MAAMC,QAAQ,GAAsB;MAClCvB,YAAY,EAAED,WAAW;MACzBK,QAAQ,EAAEkB,IAAI,CAAClB,QAAQ;MACvBoB,SAAS,EAAEF,IAAI,CAACE,SAAS;MACzBC,UAAU,EAAEH,IAAI,CAACG,UAAU;MAC3BC,MAAM,EAAEJ,IAAI,CAACI,MAAM;MACnBC,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,CAAC;MAC1BC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,CAAC;MAC5BC,UAAU,EAAEP,IAAI,CAACQ;KAClB;IACD,OAAO,IAAI,CAACrC,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAE0B;IAAQ,CAAE,CAAC,CAACT,IAAI,CACpFxB,GAAG,CAACyB,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACgB,UAAU,KAAK,CAAC;QAAE;QACpCb,OAAO,EAAEH,QAAQ,CAACiB,OAAO,IAAI,EAAE;QAC/Bb,IAAI,EAAE,CAACG,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAW,mBAAmBA,CAAClC,WAAmB;IACrC,OAAO,IAAI,CAACU,eAAe,CAACV,WAAW,CAAC,CAACe,IAAI,CAC3CxB,GAAG,CAACyB,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACgB,UAAU,KAAK,CAAC;QAAE;QACpCb,OAAO,EAAEH,QAAQ,CAACiB,OAAO,IAAI,EAAE;QAC/Bb,IAAI,EAAE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAe,eAAeA,CAAC/B,OAAe;IAC7B;IACA;IACA,MAAM,IAAIgC,KAAK,CAAC,mEAAmE,CAAC;EACtF;CACD;AAzFY5C,gBAAgB,GAAA6C,UAAA,EAH5B/C,UAAU,CAAC;EACVgD,UAAU,EAAE;CACb,CAAC,C,EACW9C,gBAAgB,CAyF5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}