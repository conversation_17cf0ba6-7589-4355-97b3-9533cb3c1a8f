{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\nexport default function hoursToSeconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * secondsInHour);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}