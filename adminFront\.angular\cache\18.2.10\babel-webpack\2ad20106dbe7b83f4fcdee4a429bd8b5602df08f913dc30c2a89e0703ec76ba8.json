{"ast": null, "code": "import { DateComponent, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, renderScrollShim, createFormatter, BaseComponent, StandardEvent, buildSegTimeText, EventContainer, getSegAnchorAttrs, memoize, MoreLinkContainer, getSegMeta, getUniqueDomId, setRef, DayCellContainer, WeekNumberContainer, buildNavLinkAttrs, hasCustomDayCellContent, addMs, intersectRanges, addDays, SegHierarchy, buildEntryKey, intersectSpans, RefMap, sortEventSegs, isPropsEqual, buildEventRangeKey, BgEvent, renderFill, PositionCache, NowTimer, formatIsoMonthStr, formatDayString, Slicer, DayHeader, DaySeriesModel, DayTableModel, DateProfileGenerator, addWeeks, diffWeeks, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createRef, createElement, Fragment } from '@fullcalendar/core/preact.js';\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.headerElRef = createRef();\n  }\n  renderSimpleLayout(headerRowContent, bodyContent) {\n    let {\n      props,\n      context\n    } = this;\n    let sections = [];\n    let stickyHeaderDates = getStickyHeaderDates(context.options);\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunk: {\n        content: bodyContent\n      }\n    });\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(SimpleScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      collapsibleWidth: props.forPrint,\n      cols: [] /* TODO: make optional? */,\n      sections: sections\n    }));\n  }\n  renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation');\n    }\n    let {\n      props,\n      context\n    } = this;\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n    let sections = [];\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunks: [{\n          key: 'main',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }]\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      chunks: [{\n        key: 'main',\n        content: bodyContent\n      }]\n    });\n    if (stickyFooterScrollbar) {\n      sections.push({\n        type: 'footer',\n        key: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'main',\n          content: renderScrollShim\n        }]\n      });\n    }\n    return createElement(ViewContainer, {\n      elClasses: ['fc-daygrid'],\n      viewSpec: context.viewSpec\n    }, createElement(ScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      forPrint: props.forPrint,\n      collapsibleWidth: props.forPrint,\n      colGroups: [{\n        cols: [{\n          span: colCnt,\n          minWidth: dayMinWidth\n        }]\n      }],\n      sections: sections\n    }));\n  }\n}\nfunction splitSegsByRow(segs, rowCnt) {\n  let byRow = [];\n  for (let i = 0; i < rowCnt; i += 1) {\n    byRow[i] = [];\n  }\n  for (let seg of segs) {\n    byRow[seg.row].push(seg);\n  }\n  return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n  let byCol = [];\n  for (let i = 0; i < colCnt; i += 1) {\n    byCol[i] = [];\n  }\n  for (let seg of segs) {\n    byCol[seg.firstCol].push(seg);\n  }\n  return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n  let byRow = [];\n  if (!ui) {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = null;\n    }\n  } else {\n    for (let i = 0; i < rowCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: []\n      };\n    }\n    for (let seg of ui.segs) {\n      byRow[seg.row].segs.push(seg);\n    }\n  }\n  return byRow;\n}\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'narrow'\n});\nfunction hasListItemDisplay(seg) {\n  let {\n    display\n  } = seg.eventRange.ui;\n  return display === 'list-item' || display === 'auto' && !seg.eventRange.def.allDay && seg.firstCol === seg.lastCol &&\n  // can't be multi-day\n  seg.isStart &&\n  // \"\n  seg.isEnd // \"\n  ;\n}\nclass TableBlockEvent extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    return createElement(StandardEvent, Object.assign({}, props, {\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'],\n      defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT,\n      defaultDisplayEventEnd: props.defaultDisplayEventEnd,\n      disableResizing: !props.seg.eventRange.def.allDay\n    }));\n  }\n}\nclass TableListItemEvent extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg\n    } = props;\n    let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n    let timeText = buildSegTimeText(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n    return createElement(EventContainer, Object.assign({}, props, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'],\n      elAttrs: getSegAnchorAttrs(props.seg, context),\n      defaultGenerator: renderInnerContent,\n      timeText: timeText,\n      isResizing: false,\n      isDateSelecting: false\n    }));\n  }\n}\nfunction renderInnerContent(renderProps) {\n  return createElement(Fragment, null, createElement(\"div\", {\n    className: \"fc-daygrid-event-dot\",\n    style: {\n      borderColor: renderProps.borderColor || renderProps.backgroundColor\n    }\n  }), renderProps.timeText && createElement(\"div\", {\n    className: \"fc-event-time\"\n  }, renderProps.timeText), createElement(\"div\", {\n    className: \"fc-event-title\"\n  }, renderProps.event.title || createElement(Fragment, null, \"\\u00A0\")));\n}\nclass TableCellMoreLink extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.compileSegs = memoize(compileSegs);\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      allSegs,\n      invisibleSegs\n    } = this.compileSegs(props.singlePlacements);\n    return createElement(MoreLinkContainer, {\n      elClasses: ['fc-daygrid-more-link'],\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      allDayDate: props.allDayDate,\n      moreCnt: props.moreCnt,\n      allSegs: allSegs,\n      hiddenSegs: invisibleSegs,\n      alignmentElRef: props.alignmentElRef,\n      alignGridTop: props.alignGridTop,\n      extraDateSpan: props.extraDateSpan,\n      popoverContent: () => {\n        let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) || (props.eventResize ? props.eventResize.affectedInstances : null) || {};\n        return createElement(Fragment, null, allSegs.map(seg => {\n          let instanceId = seg.eventRange.instance.instanceId;\n          return createElement(\"div\", {\n            className: \"fc-daygrid-event-harness\",\n            key: instanceId,\n            style: {\n              visibility: isForcedInvisible[instanceId] ? 'hidden' : ''\n            }\n          }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))) : createElement(TableBlockEvent, Object.assign({\n            seg: seg,\n            isDragging: false,\n            isResizing: false,\n            isDateSelecting: false,\n            isSelected: instanceId === props.eventSelection,\n            defaultDisplayEventEnd: false\n          }, getSegMeta(seg, props.todayRange))));\n        }));\n      }\n    });\n  }\n}\nfunction compileSegs(singlePlacements) {\n  let allSegs = [];\n  let invisibleSegs = [];\n  for (let placement of singlePlacements) {\n    allSegs.push(placement.seg);\n    if (!placement.isVisible) {\n      invisibleSegs.push(placement.seg);\n    }\n  }\n  return {\n    allSegs,\n    invisibleSegs\n  };\n}\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({\n  week: 'narrow'\n});\nclass TableCell extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.rootElRef = createRef();\n    this.state = {\n      dayNumberId: getUniqueDomId()\n    };\n    this.handleRootEl = el => {\n      setRef(this.rootElRef, el);\n      setRef(this.props.elRef, el);\n    };\n  }\n  render() {\n    let {\n      context,\n      props,\n      state,\n      rootElRef\n    } = this;\n    let {\n      options,\n      dateEnv\n    } = context;\n    let {\n      date,\n      dateProfile\n    } = props;\n    // TODO: memoize this?\n    const isMonthStart = props.showDayNumber && shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n    return createElement(DayCellContainer, {\n      elTag: \"td\",\n      elRef: this.handleRootEl,\n      elClasses: ['fc-daygrid-day', ...(props.extraClassNames || [])],\n      elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), props.showDayNumber ? {\n        'aria-labelledby': state.dayNumberId\n      } : {}), {\n        role: 'gridcell'\n      }),\n      defaultGenerator: renderTopInner,\n      date: date,\n      dateProfile: dateProfile,\n      todayRange: props.todayRange,\n      showDayNumber: props.showDayNumber,\n      isMonthStart: isMonthStart,\n      extraRenderProps: props.extraRenderProps\n    }, (InnerContent, renderProps) => createElement(\"div\", {\n      ref: props.innerElRef,\n      className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\",\n      style: {\n        minHeight: props.minHeight\n      }\n    }, props.showWeekNumber && createElement(WeekNumberContainer, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-week-number'],\n      elAttrs: buildNavLinkAttrs(context, date, 'week'),\n      date: date,\n      defaultFormat: DEFAULT_WEEK_NUM_FORMAT\n    }), !renderProps.isDisabled && (props.showDayNumber || hasCustomDayCellContent(options) || props.forceDayTop) ? createElement(\"div\", {\n      className: \"fc-daygrid-day-top\"\n    }, createElement(InnerContent, {\n      elTag: \"a\",\n      elClasses: ['fc-daygrid-day-number', isMonthStart && 'fc-daygrid-month-start'],\n      elAttrs: Object.assign(Object.assign({}, buildNavLinkAttrs(context, date)), {\n        id: state.dayNumberId\n      })\n    })) : props.showDayNumber ?\n    // for creating correct amount of space (see issue #7162)\n    createElement(\"div\", {\n      className: \"fc-daygrid-day-top\",\n      style: {\n        visibility: 'hidden'\n      }\n    }, createElement(\"a\", {\n      className: \"fc-daygrid-day-number\"\n    }, \"\\u00A0\")) : undefined, createElement(\"div\", {\n      className: \"fc-daygrid-day-events\",\n      ref: props.fgContentElRef\n    }, props.fgContent, createElement(\"div\", {\n      className: \"fc-daygrid-day-bottom\",\n      style: {\n        marginTop: props.moreMarginTop\n      }\n    }, createElement(TableCellMoreLink, {\n      allDayDate: date,\n      singlePlacements: props.singlePlacements,\n      moreCnt: props.moreCnt,\n      alignmentElRef: rootElRef,\n      alignGridTop: !props.showDayNumber,\n      extraDateSpan: props.extraDateSpan,\n      dateProfile: props.dateProfile,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      todayRange: props.todayRange\n    }))), createElement(\"div\", {\n      className: \"fc-daygrid-day-bg\"\n    }, props.bgContent)));\n  }\n}\nfunction renderTopInner(props) {\n  return props.dayNumberText || createElement(Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n  const {\n    start: currentStart,\n    end: currentEnd\n  } = currentRange;\n  const currentEndIncl = addMs(currentEnd, -1);\n  const currentFirstYear = dateEnv.getYear(currentStart);\n  const currentFirstMonth = dateEnv.getMonth(currentStart);\n  const currentLastYear = dateEnv.getYear(currentEndIncl);\n  const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n  // spans more than one month?\n  return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) && Boolean(\n  // first date in current view?\n  date.valueOf() === currentStart.valueOf() ||\n  // a month-start that's within the current range?\n  dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf());\n}\nfunction generateSegKey(seg) {\n  return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n  return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs,\n// assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n  let hierarchy = new DayGridSegHierarchy(segEntry => {\n    // TODO: more DRY with generateSegUid\n    let segUid = segs[segEntry.index].eventRange.instance.instanceId + ':' + segEntry.span.start + ':' + (segEntry.span.end - 1);\n    // if no thickness known, assume 1 (if 0, so small it always fits)\n    return segHeights[segUid] || 1;\n  });\n  hierarchy.allowReslicing = true;\n  hierarchy.strictOrder = strictOrder;\n  if (dayMaxEvents === true || dayMaxEventRows === true) {\n    hierarchy.maxCoord = maxContentHeight;\n    hierarchy.hiddenConsumes = true;\n  } else if (typeof dayMaxEvents === 'number') {\n    hierarchy.maxStackCnt = dayMaxEvents;\n  } else if (typeof dayMaxEventRows === 'number') {\n    hierarchy.maxStackCnt = dayMaxEventRows;\n    hierarchy.hiddenConsumes = true;\n  }\n  // create segInputs only for segs with known heights\n  let segInputs = [];\n  let unknownHeightSegs = [];\n  for (let i = 0; i < segs.length; i += 1) {\n    let seg = segs[i];\n    let segUid = generateSegUid(seg);\n    let eventHeight = segHeights[segUid];\n    if (eventHeight != null) {\n      segInputs.push({\n        index: i,\n        span: {\n          start: seg.firstCol,\n          end: seg.lastCol + 1\n        }\n      });\n    } else {\n      unknownHeightSegs.push(seg);\n    }\n  }\n  let hiddenEntries = hierarchy.addSegs(segInputs);\n  let segRects = hierarchy.toRects();\n  let {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  } = placeRects(segRects, segs, cells);\n  let moreCnts = [];\n  let moreMarginTops = [];\n  // add segs with unknown heights\n  for (let seg of unknownHeightSegs) {\n    multiColPlacements[seg.firstCol].push({\n      seg,\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // add the hidden entries\n  for (let col = 0; col < cells.length; col += 1) {\n    moreCnts.push(0);\n  }\n  for (let hiddenEntry of hiddenEntries) {\n    let seg = segs[hiddenEntry.index];\n    let hiddenSpan = hiddenEntry.span;\n    multiColPlacements[hiddenSpan.start].push({\n      seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n      isVisible: false,\n      isAbsolute: true,\n      absoluteTop: 0,\n      marginTop: 0\n    });\n    for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n      moreCnts[col] += 1;\n      singleColPlacements[col].push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: false,\n        isAbsolute: false,\n        absoluteTop: 0,\n        marginTop: 0\n      });\n    }\n  }\n  // deal with leftover margins\n  for (let col = 0; col < cells.length; col += 1) {\n    moreMarginTops.push(leftoverMargins[col]);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    moreCnts,\n    moreMarginTops\n  };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n  let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n  let singleColPlacements = [];\n  let multiColPlacements = [];\n  let leftoverMargins = [];\n  for (let col = 0; col < cells.length; col += 1) {\n    let rects = rectsByEachCol[col];\n    // compute all static segs in singlePlacements\n    let singlePlacements = [];\n    let currentHeight = 0;\n    let currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      singlePlacements.push({\n        seg: resliceSeg(seg, col, col + 1, cells),\n        isVisible: true,\n        isAbsolute: false,\n        absoluteTop: rect.levelCoord,\n        marginTop: rect.levelCoord - currentHeight\n      });\n      currentHeight = rect.levelCoord + rect.thickness;\n    }\n    // compute mixed static/absolute segs in multiPlacements\n    let multiPlacements = [];\n    currentHeight = 0;\n    currentMarginTop = 0;\n    for (let rect of rects) {\n      let seg = segs[rect.index];\n      let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n      let isFirstCol = rect.span.start === col;\n      currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n      currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n      if (isAbsolute) {\n        currentMarginTop += rect.thickness;\n        if (isFirstCol) {\n          multiPlacements.push({\n            seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n            isVisible: true,\n            isAbsolute: true,\n            absoluteTop: rect.levelCoord,\n            marginTop: 0\n          });\n        }\n      } else if (isFirstCol) {\n        multiPlacements.push({\n          seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n          isVisible: true,\n          isAbsolute: false,\n          absoluteTop: rect.levelCoord,\n          marginTop: currentMarginTop // claim the margin\n        });\n        currentMarginTop = 0;\n      }\n    }\n    singleColPlacements.push(singlePlacements);\n    multiColPlacements.push(multiPlacements);\n    leftoverMargins.push(currentMarginTop);\n  }\n  return {\n    singleColPlacements,\n    multiColPlacements,\n    leftoverMargins\n  };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n  let rectsByEachCol = [];\n  for (let col = 0; col < colCnt; col += 1) {\n    rectsByEachCol.push([]);\n  }\n  for (let rect of rects) {\n    for (let col = rect.span.start; col < rect.span.end; col += 1) {\n      rectsByEachCol[col].push(rect);\n    }\n  }\n  return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n  if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n    return seg;\n  }\n  let eventRange = seg.eventRange;\n  let origRange = eventRange.range;\n  let slicedRange = intersectRanges(origRange, {\n    start: cells[spanStart].date,\n    end: addDays(cells[spanEnd - 1].date, 1)\n  });\n  return Object.assign(Object.assign({}, seg), {\n    firstCol: spanStart,\n    lastCol: spanEnd - 1,\n    eventRange: {\n      def: eventRange.def,\n      ui: Object.assign(Object.assign({}, eventRange.ui), {\n        durationEditable: false\n      }),\n      instance: eventRange.instance,\n      range: slicedRange\n    },\n    isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(),\n    isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf()\n  });\n}\nclass DayGridSegHierarchy extends SegHierarchy {\n  constructor() {\n    super(...arguments);\n    // config\n    this.hiddenConsumes = false;\n    // allows us to keep hidden entries in the hierarchy so they take up space\n    this.forceHidden = {};\n  }\n  addSegs(segInputs) {\n    const hiddenSegs = super.addSegs(segInputs);\n    const {\n      entriesByLevel\n    } = this;\n    const excludeHidden = entry => !this.forceHidden[buildEntryKey(entry)];\n    // remove the forced-hidden segs\n    for (let level = 0; level < entriesByLevel.length; level += 1) {\n      entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n    }\n    return hiddenSegs;\n  }\n  handleInvalidInsertion(insertion, entry, hiddenEntries) {\n    const {\n      entriesByLevel,\n      forceHidden\n    } = this;\n    const {\n      touchingEntry,\n      touchingLevel,\n      touchingLateral\n    } = insertion;\n    // the entry that the new insertion is touching must be hidden\n    if (this.hiddenConsumes && touchingEntry) {\n      const touchingEntryId = buildEntryKey(touchingEntry);\n      if (!forceHidden[touchingEntryId]) {\n        if (this.allowReslicing) {\n          // split up the touchingEntry, reinsert it\n          const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), {\n            span: intersectSpans(touchingEntry.span, entry.span)\n          });\n          // reinsert the area that turned into a \"more\" link (so no other entries try to\n          // occupy the space) but mark it forced-hidden\n          const hiddenEntryId = buildEntryKey(hiddenEntry);\n          forceHidden[hiddenEntryId] = true;\n          entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n          hiddenEntries.push(hiddenEntry);\n          this.splitEntry(touchingEntry, entry, hiddenEntries);\n        } else {\n          forceHidden[touchingEntryId] = true;\n          hiddenEntries.push(touchingEntry);\n        }\n      }\n    }\n    // will try to reslice...\n    super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n  }\n}\nclass TableRow extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.cellElRefs = new RefMap(); // the <td>\n    this.frameElRefs = new RefMap(); // the fc-daygrid-day-frame\n    this.fgElRefs = new RefMap(); // the fc-daygrid-day-events\n    this.segHarnessRefs = new RefMap(); // indexed by \"instanceId:firstCol\"\n    this.rootElRef = createRef();\n    this.state = {\n      framePositions: null,\n      maxContentHeight: null,\n      segHeights: {}\n    };\n    this.handleResize = isForced => {\n      if (isForced) {\n        this.updateSizing(true); // isExternal=true\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      state,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let colCnt = props.cells.length;\n    let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n    let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n    let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n    let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n    let {\n      singleColPlacements,\n      multiColPlacements,\n      moreCnts,\n      moreMarginTops\n    } = computeFgSegPlacement(sortEventSegs(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n    let isForcedInvisible =\n    // TODO: messy way to compute this\n    props.eventDrag && props.eventDrag.affectedInstances || props.eventResize && props.eventResize.affectedInstances || {};\n    return createElement(\"tr\", {\n      ref: this.rootElRef,\n      role: \"row\"\n    }, props.renderIntro && props.renderIntro(), props.cells.map((cell, col) => {\n      let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n      let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n      return createElement(TableCell, {\n        key: cell.key,\n        elRef: this.cellElRefs.createRef(cell.key),\n        innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */,\n        dateProfile: props.dateProfile,\n        date: cell.date,\n        showDayNumber: props.showDayNumbers,\n        showWeekNumber: props.showWeekNumbers && col === 0,\n        forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */,\n        todayRange: props.todayRange,\n        eventSelection: props.eventSelection,\n        eventDrag: props.eventDrag,\n        eventResize: props.eventResize,\n        extraRenderProps: cell.extraRenderProps,\n        extraDataAttrs: cell.extraDataAttrs,\n        extraClassNames: cell.extraClassNames,\n        extraDateSpan: cell.extraDateSpan,\n        moreCnt: moreCnts[col],\n        moreMarginTop: moreMarginTops[col],\n        singlePlacements: singleColPlacements[col],\n        fgContentElRef: this.fgElRefs.createRef(cell.key),\n        fgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, createElement(Fragment, null, normalFgNodes), createElement(Fragment, null, mirrorFgNodes)),\n        bgContent:\n        // Fragment scopes the keys\n        createElement(Fragment, null, this.renderFillSegs(highlightSegsByCol[col], 'highlight'), this.renderFillSegs(businessHoursByCol[col], 'non-business'), this.renderFillSegs(bgEventSegsByCol[col], 'bg-event')),\n        minHeight: props.cellMinHeight\n      });\n    }));\n  }\n  componentDidMount() {\n    this.updateSizing(true);\n    this.context.addResizeHandler(this.handleResize);\n  }\n  componentDidUpdate(prevProps, prevState) {\n    let currentProps = this.props;\n    this.updateSizing(!isPropsEqual(prevProps, currentProps));\n  }\n  componentWillUnmount() {\n    this.context.removeResizeHandler(this.handleResize);\n  }\n  getHighlightSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventDrag && props.eventDrag.segs.length) {\n      // messy check\n      return props.eventDrag.segs;\n    }\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return props.dateSelectionSegs;\n  }\n  getMirrorSegs() {\n    let {\n      props\n    } = this;\n    if (props.eventResize && props.eventResize.segs.length) {\n      // messy check\n      return props.eventResize.segs;\n    }\n    return [];\n  }\n  renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n    let {\n      context\n    } = this;\n    let {\n      eventSelection\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n    let isMirror = isDragging || isResizing || isDateSelecting;\n    let nodes = [];\n    if (framePositions) {\n      for (let placement of segPlacements) {\n        let {\n          seg\n        } = placement;\n        let {\n          instanceId\n        } = seg.eventRange.instance;\n        let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n        let isAbsolute = placement.isAbsolute;\n        let left = '';\n        let right = '';\n        if (isAbsolute) {\n          if (context.isRtl) {\n            right = 0;\n            left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n          } else {\n            left = 0;\n            right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n          }\n        }\n        /*\n        known bug: events that are force to be list-item but span multiple days still take up space in later columns\n        todo: in print view, for multi-day events, don't display title within non-start/end segs\n        */\n        nodes.push(createElement(\"div\", {\n          className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''),\n          key: generateSegKey(seg),\n          ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)),\n          style: {\n            visibility: isVisible ? '' : 'hidden',\n            marginTop: isAbsolute ? '' : placement.marginTop,\n            top: isAbsolute ? placement.absoluteTop : '',\n            left,\n            right\n          }\n        }, hasListItemDisplay(seg) ? createElement(TableListItemEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange))) : createElement(TableBlockEvent, Object.assign({\n          seg: seg,\n          isDragging: isDragging,\n          isResizing: isResizing,\n          isDateSelecting: isDateSelecting,\n          isSelected: instanceId === eventSelection,\n          defaultDisplayEventEnd: defaultDisplayEventEnd\n        }, getSegMeta(seg, todayRange)))));\n      }\n    }\n    return nodes;\n  }\n  renderFillSegs(segs, fillType) {\n    let {\n      isRtl\n    } = this.context;\n    let {\n      todayRange\n    } = this.props;\n    let {\n      framePositions\n    } = this.state;\n    let nodes = [];\n    if (framePositions) {\n      for (let seg of segs) {\n        let leftRightCss = isRtl ? {\n          right: 0,\n          left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol]\n        } : {\n          left: 0,\n          right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol]\n        };\n        nodes.push(createElement(\"div\", {\n          key: buildEventRangeKey(seg.eventRange),\n          className: \"fc-daygrid-bg-harness\",\n          style: leftRightCss\n        }, fillType === 'bg-event' ? createElement(BgEvent, Object.assign({\n          seg: seg\n        }, getSegMeta(seg, todayRange))) : renderFill(fillType)));\n      }\n    }\n    return createElement(Fragment, {}, ...nodes);\n  }\n  updateSizing(isExternalSizingChange) {\n    let {\n      props,\n      state,\n      frameElRefs\n    } = this;\n    if (!props.forPrint && props.clientWidth !== null // positioning ready?\n    ) {\n      if (isExternalSizingChange) {\n        let frameEls = props.cells.map(cell => frameElRefs.currentMap[cell.key]);\n        if (frameEls.length) {\n          let originEl = this.rootElRef.current;\n          let newPositionCache = new PositionCache(originEl, frameEls, true,\n          // isHorizontal\n          false);\n          if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n            this.setState({\n              framePositions: new PositionCache(originEl, frameEls, true,\n              // isHorizontal\n              false)\n            });\n          }\n        }\n      }\n      const oldSegHeights = this.state.segHeights;\n      const newSegHeights = this.querySegHeights();\n      const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n      this.safeSetState({\n        // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n        // Essentially, once you compute an element's height, never null-out.\n        // TODO: always display all events, as visibility:hidden?\n        segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n        maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null\n      });\n    }\n  }\n  querySegHeights() {\n    let segElMap = this.segHarnessRefs.currentMap;\n    let segHeights = {};\n    // get the max height amongst instance segs\n    for (let segUid in segElMap) {\n      let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n      segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n    }\n    return segHeights;\n  }\n  computeMaxContentHeight() {\n    let firstKey = this.props.cells[0].key;\n    let cellEl = this.cellElRefs.currentMap[firstKey];\n    let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n    return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n  }\n  getCellEls() {\n    let elMap = this.cellElRefs.currentMap;\n    return this.props.cells.map(cell => elMap[cell.key]);\n  }\n}\nTableRow.addStateEquality({\n  segHeights: isPropsEqual\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n  if (!mirrorSegs.length) {\n    return [];\n  }\n  let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n  return mirrorSegs.map(seg => ({\n    seg,\n    isVisible: true,\n    isAbsolute: true,\n    absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n    marginTop: 0\n  }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n  let topsByInstanceId = {};\n  for (let placements of colPlacements) {\n    for (let placement of placements) {\n      topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n    }\n  }\n  return topsByInstanceId;\n}\nclass TableRows extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.splitBusinessHourSegs = memoize(splitSegsByRow);\n    this.splitBgEventSegs = memoize(splitSegsByRow);\n    this.splitFgEventSegs = memoize(splitSegsByRow);\n    this.splitDateSelectionSegs = memoize(splitSegsByRow);\n    this.splitEventDrag = memoize(splitInteractionByRow);\n    this.splitEventResize = memoize(splitInteractionByRow);\n    this.rowRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let rowCnt = props.cells.length;\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n    let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n    // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n    // choose 7 because a month view will have max 6 rows\n    let cellMinHeight = rowCnt >= 7 && props.clientWidth ? props.clientWidth / context.options.aspectRatio / 6 : null;\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => createElement(Fragment, null, props.cells.map((cells, row) => createElement(TableRow, {\n      ref: this.rowRefs.createRef(row),\n      key: cells.length ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */ : row // in case there are no cells (like when resource view is loading)\n      ,\n      showDayNumbers: rowCnt > 1,\n      showWeekNumbers: props.showWeekNumbers,\n      todayRange: todayRange,\n      dateProfile: props.dateProfile,\n      cells: cells,\n      renderIntro: props.renderRowIntro,\n      businessHourSegs: businessHourSegsByRow[row],\n      eventSelection: props.eventSelection,\n      bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */,\n      fgEventSegs: fgEventSegsByRow[row],\n      dateSelectionSegs: dateSelectionSegsByRow[row],\n      eventDrag: eventDragByRow[row],\n      eventResize: eventResizeByRow[row],\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      cellMinHeight: cellMinHeight,\n      forPrint: props.forPrint\n    }))));\n  }\n  componentDidMount() {\n    this.registerInteractiveComponent();\n  }\n  componentDidUpdate() {\n    // for if started with zero cells\n    this.registerInteractiveComponent();\n  }\n  registerInteractiveComponent() {\n    if (!this.rootEl) {\n      // HACK: need a daygrid wrapper parent to do positioning\n      // NOTE: a daygrid resource view w/o resources can have zero cells\n      const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n      const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n      if (rootEl) {\n        this.rootEl = rootEl;\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl,\n          isHitComboAllowed: this.props.isHitComboAllowed\n        });\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.rootEl) {\n      this.context.unregisterInteractiveComponent(this);\n      this.rootEl = null;\n    }\n  }\n  // Hit System\n  // ----------------------------------------------------------------------------------------------------\n  prepareHits() {\n    this.rowPositions = new PositionCache(this.rootEl, this.rowRefs.collect().map(rowObj => rowObj.getCellEls()[0]),\n    // first cell el in each row. TODO: not optimal\n    false, true);\n    this.colPositions = new PositionCache(this.rootEl, this.rowRefs.currentMap[0].getCellEls(),\n    // cell els in first row\n    true,\n    // horizontal\n    false);\n  }\n  queryHit(positionLeft, positionTop) {\n    let {\n      colPositions,\n      rowPositions\n    } = this;\n    let col = colPositions.leftToIndex(positionLeft);\n    let row = rowPositions.topToIndex(positionTop);\n    if (row != null && col != null) {\n      let cell = this.props.cells[row][col];\n      return {\n        dateProfile: this.props.dateProfile,\n        dateSpan: Object.assign({\n          range: this.getCellRange(row, col),\n          allDay: true\n        }, cell.extraDateSpan),\n        dayEl: this.getCellEl(row, col),\n        rect: {\n          left: colPositions.lefts[col],\n          right: colPositions.rights[col],\n          top: rowPositions.tops[row],\n          bottom: rowPositions.bottoms[row]\n        },\n        layer: 0\n      };\n    }\n    return null;\n  }\n  getCellEl(row, col) {\n    return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n  }\n  getCellRange(row, col) {\n    let start = this.props.cells[row][col].date;\n    let end = addDays(start, 1);\n    return {\n      start,\n      end\n    };\n  }\n}\nfunction isSegAllDay(seg) {\n  return seg.eventRange.def.allDay;\n}\nclass Table extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.elRef = createRef();\n    this.needsScrollReset = false;\n  }\n  render() {\n    let {\n      props\n    } = this;\n    let {\n      dayMaxEventRows,\n      dayMaxEvents,\n      expandRows\n    } = props;\n    let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n    // if rows can't expand to fill fixed height, can't do balanced-height event limit\n    // TODO: best place to normalize these options?\n    if (limitViaBalanced && !expandRows) {\n      limitViaBalanced = false;\n      dayMaxEventRows = null;\n      dayMaxEvents = null;\n    }\n    let classNames = ['fc-daygrid-body', limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced', expandRows ? '' : 'fc-daygrid-body-natural' // will height of one row depend on the others?\n    ];\n    return createElement(\"div\", {\n      ref: this.elRef,\n      className: classNames.join(' '),\n      style: {\n        // these props are important to give this wrapper correct dimensions for interactions\n        // TODO: if we set it here, can we avoid giving to inner tables?\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth\n      }\n    }, createElement(\"table\", {\n      role: \"presentation\",\n      className: \"fc-scrollgrid-sync-table\",\n      style: {\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth,\n        height: expandRows ? props.clientHeight : ''\n      }\n    }, props.colGroupNode, createElement(\"tbody\", {\n      role: \"presentation\"\n    }, createElement(TableRows, {\n      dateProfile: props.dateProfile,\n      cells: props.cells,\n      renderRowIntro: props.renderRowIntro,\n      showWeekNumbers: props.showWeekNumbers,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      businessHourSegs: props.businessHourSegs,\n      bgEventSegs: props.bgEventSegs,\n      fgEventSegs: props.fgEventSegs,\n      dateSelectionSegs: props.dateSelectionSegs,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      dayMaxEvents: dayMaxEvents,\n      dayMaxEventRows: dayMaxEventRows,\n      forPrint: props.forPrint,\n      isHitComboAllowed: props.isHitComboAllowed\n    }))));\n  }\n  componentDidMount() {\n    this.requestScrollReset();\n  }\n  componentDidUpdate(prevProps) {\n    if (prevProps.dateProfile !== this.props.dateProfile) {\n      this.requestScrollReset();\n    } else {\n      this.flushScrollReset();\n    }\n  }\n  requestScrollReset() {\n    this.needsScrollReset = true;\n    this.flushScrollReset();\n  }\n  flushScrollReset() {\n    if (this.needsScrollReset && this.props.clientWidth // sizes computed?\n    ) {\n      const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n      if (subjectEl) {\n        const originEl = subjectEl.closest('.fc-daygrid-body');\n        const scrollEl = originEl.closest('.fc-scroller');\n        const scrollTop = subjectEl.getBoundingClientRect().top - originEl.getBoundingClientRect().top;\n        scrollEl.scrollTop = scrollTop ? scrollTop + 1 : 0; // overcome border\n      }\n      this.needsScrollReset = false;\n    }\n  }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n  let el;\n  if (dateProfile.currentRangeUnit.match(/year|month/)) {\n    el = containerEl.querySelector(`[data-date=\"${formatIsoMonthStr(dateProfile.currentDate)}-01\"]`);\n    // even if view is month-based, first-of-month might be hidden...\n  }\n  if (!el) {\n    el = containerEl.querySelector(`[data-date=\"${formatDayString(dateProfile.currentDate)}\"]`);\n    // could still be hidden if an interior-view hidden day\n  }\n  return el;\n}\nclass DayTableSlicer extends Slicer {\n  constructor() {\n    super(...arguments);\n    this.forceDayIfListItem = true;\n  }\n  sliceRange(dateRange, dayTableModel) {\n    return dayTableModel.sliceRange(dateRange);\n  }\n}\nclass DayTable extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.slicer = new DayTableSlicer();\n    this.tableRef = createRef();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    return createElement(Table, Object.assign({\n      ref: this.tableRef\n    }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), {\n      dateProfile: props.dateProfile,\n      cells: props.dayTableModel.cells,\n      colGroupNode: props.colGroupNode,\n      tableMinWidth: props.tableMinWidth,\n      renderRowIntro: props.renderRowIntro,\n      dayMaxEvents: props.dayMaxEvents,\n      dayMaxEventRows: props.dayMaxEventRows,\n      showWeekNumbers: props.showWeekNumbers,\n      expandRows: props.expandRows,\n      headerAlignElRef: props.headerAlignElRef,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      forPrint: props.forPrint\n    }));\n  }\n}\nclass DayTableView extends TableView {\n  constructor() {\n    super(...arguments);\n    this.buildDayTableModel = memoize(buildDayTableModel);\n    this.headerRef = createRef();\n    this.tableRef = createRef();\n    // can't override any lifecycle methods from parent\n  }\n  render() {\n    let {\n      options,\n      dateProfileGenerator\n    } = this.context;\n    let {\n      props\n    } = this;\n    let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n    let headerContent = options.dayHeaders && createElement(DayHeader, {\n      ref: this.headerRef,\n      dateProfile: props.dateProfile,\n      dates: dayTableModel.headerDates,\n      datesRepDistinctDays: dayTableModel.rowCnt === 1\n    });\n    let bodyContent = contentArg => createElement(DayTable, {\n      ref: this.tableRef,\n      dateProfile: props.dateProfile,\n      dayTableModel: dayTableModel,\n      businessHours: props.businessHours,\n      dateSelection: props.dateSelection,\n      eventStore: props.eventStore,\n      eventUiBases: props.eventUiBases,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      nextDayThreshold: options.nextDayThreshold,\n      colGroupNode: contentArg.tableColGroupNode,\n      tableMinWidth: contentArg.tableMinWidth,\n      dayMaxEvents: options.dayMaxEvents,\n      dayMaxEventRows: options.dayMaxEventRows,\n      showWeekNumbers: options.weekNumbers,\n      expandRows: !props.isHeightAuto,\n      headerAlignElRef: this.headerElRef,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      forPrint: props.forPrint\n    });\n    return options.dayMinWidth ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth) : this.renderSimpleLayout(headerContent, bodyContent);\n  }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n  return new DayTableModel(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\nclass TableDateProfileGenerator extends DateProfileGenerator {\n  // Computes the date range that will be rendered\n  buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n    let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n    let {\n      props\n    } = this;\n    return buildDayTableRenderRange({\n      currentRange: renderRange,\n      snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n      fixedWeekCount: props.fixedWeekCount,\n      dateEnv: props.dateEnv\n    });\n  }\n}\nfunction buildDayTableRenderRange(props) {\n  let {\n    dateEnv,\n    currentRange\n  } = props;\n  let {\n    start,\n    end\n  } = currentRange;\n  let endOfWeek;\n  // year and month views should be aligned with weeks. this is already done for week\n  if (props.snapToWeek) {\n    start = dateEnv.startOfWeek(start);\n    // make end-of-week if not already\n    endOfWeek = dateEnv.startOfWeek(end);\n    if (endOfWeek.valueOf() !== end.valueOf()) {\n      end = addWeeks(endOfWeek, 1);\n    }\n  }\n  // ensure 6 weeks\n  if (props.fixedWeekCount) {\n    // TODO: instead of these date-math gymnastics (for multimonth view),\n    // compute dateprofiles of all months, then use start of first and end of last.\n    let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth(addDays(currentRange.end, -1)));\n    let rowCnt = Math.ceil(\n    // could be partial weeks due to hiddenDays\n    diffWeeks(lastMonthRenderStart, end));\n    end = addWeeks(end, 6 - rowCnt);\n  }\n  return {\n    start,\n    end\n  };\n}\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\ninjectStyles(css_248z);\nexport { DayTableView as DayGridView, DayTable, DayTableSlicer, Table, TableDateProfileGenerator, TableRows, TableView, buildDayTableModel, buildDayTableRenderRange };", "map": {"version": 3, "names": ["DateComponent", "getStickyHeaderDates", "ViewContainer", "SimpleScrollGrid", "getStickyFooterScrollbar", "renderScrollShim", "createFormatter", "BaseComponent", "StandardEvent", "buildSegTimeText", "EventContainer", "getSegAnchorAttrs", "memoize", "MoreLinkContainer", "getSegMeta", "getUniqueDomId", "setRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WeekNumberContainer", "buildNavLinkAttrs", "hasCustomDayCellContent", "addMs", "intersectRanges", "addDays", "SegHierarchy", "buildEntryKey", "intersectSpans", "RefMap", "sortEventSegs", "isPropsEqual", "buildEventRangeKey", "BgEvent", "renderFill", "PositionCache", "NowTimer", "formatIsoMonthStr", "formatDayString", "<PERSON>licer", "<PERSON><PERSON><PERSON><PERSON>", "DaySeriesModel", "DayTableModel", "DateProfileGenerator", "addWeeks", "diffWeeks", "injectStyles", "createRef", "createElement", "Fragment", "TableView", "constructor", "arguments", "headerElRef", "renderSimpleLayout", "headerRowContent", "bodyContent", "props", "context", "sections", "stickyHeaderDates", "options", "push", "type", "key", "isSticky", "chunk", "elRef", "tableClassName", "row<PERSON><PERSON>nt", "liquid", "content", "elClasses", "viewSpec", "isHeightAuto", "forPrint", "collapsibleWidth", "cols", "renderHScrollLayout", "colCnt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScrollGrid", "pluginHooks", "scrollGridImpl", "Error", "stickyFooterScrollbar", "chunks", "colGroups", "span", "min<PERSON><PERSON><PERSON>", "splitSegsByRow", "segs", "rowCnt", "byRow", "i", "seg", "row", "splitSegsByFirstCol", "byCol", "firstCol", "splitInteractionByRow", "ui", "affectedInstances", "isEvent", "DEFAULT_TABLE_EVENT_TIME_FORMAT", "hour", "minute", "omitZeroMinute", "meridiem", "hasListItemDisplay", "display", "eventRange", "def", "allDay", "lastCol", "isStart", "isEnd", "TableBlockEvent", "render", "Object", "assign", "defaultTimeFormat", "defaultDisplayEventEnd", "disableResizing", "TableListItemEvent", "timeFormat", "eventTimeFormat", "timeText", "elTag", "elAttrs", "defaultGenerator", "renderInnerContent", "isResizing", "isDateSelecting", "renderProps", "className", "style", "borderColor", "backgroundColor", "event", "title", "TableCellMoreLink", "compileSegs", "allSegs", "invisibleSegs", "singlePlacements", "dateProfile", "todayRange", "allDayDate", "moreCnt", "hiddenSegs", "alignmentElRef", "alignGridTop", "extraDateSpan", "popoverContent", "isForcedInvisible", "eventDrag", "eventResize", "map", "instanceId", "instance", "visibility", "isDragging", "isSelected", "eventSelection", "placement", "isVisible", "DEFAULT_WEEK_NUM_FORMAT", "week", "TableCell", "rootElRef", "state", "dayNumberId", "handleRootEl", "el", "dateEnv", "date", "isMonthStart", "showDayNumber", "shouldDisplayMonthStart", "currentRange", "extraClassNames", "extraDataAttrs", "role", "renderTopInner", "extraRenderProps", "InnerContent", "ref", "innerElRef", "minHeight", "showWeekNumber", "defaultFormat", "isDisabled", "forceDayTop", "id", "undefined", "fgContentElRef", "fgC<PERSON>nt", "marginTop", "moreMarginTop", "bg<PERSON><PERSON>nt", "dayNumberText", "start", "currentStart", "end", "currentEnd", "currentEndIncl", "currentFirstYear", "getYear", "currentFirstMonth", "getMonth", "currentLastYear", "currentLastMonth", "Boolean", "valueOf", "getDay", "generateSegKey", "generateSegUid", "computeFgSegPlacement", "dayMaxEvents", "dayMaxEventRows", "strictOrder", "segHeights", "maxContentHeight", "cells", "hierarchy", "DayGridSegHierarchy", "segEntry", "segUid", "index", "allowReslicing", "maxCoord", "hiddenConsumes", "maxStackCnt", "segInputs", "unknownHeightSegs", "length", "eventHeight", "hiddenEntries", "addSegs", "segRects", "toRects", "singleColPlacements", "multiColPlacements", "leftover<PERSON><PERSON><PERSON>", "placeRects", "moreCnts", "moreMarginTops", "isAbsolute", "absoluteTop", "col", "resliceSeg", "hiddenEntry", "hiddenSpan", "allRects", "rectsByEachCol", "groupRectsByEachCol", "rects", "currentHeight", "currentMarginTop", "rect", "levelCoord", "thickness", "multiPlacements", "isFirstCol", "spanStart", "spanEnd", "origRange", "range", "slicedRange", "durationEditable", "forceHidden", "entriesByLevel", "excludeHidden", "entry", "level", "filter", "handleInvalidInsertion", "insertion", "touchingEntry", "touchingLevel", "touchingLateral", "touchingEntryId", "hiddenEntryId", "splitEntry", "TableRow", "cellElRefs", "frameElRefs", "fgElRefs", "segHarnessRefs", "framePositions", "handleResize", "isForced", "updateSizing", "businessHoursByCol", "businessHourSegs", "bgEventSegsByCol", "bgEventSegs", "highlightSegsByCol", "getHighlightSegs", "mirrorSegsByCol", "getMirrorSegs", "fgEventSegs", "eventOrder", "eventOrderStrict", "renderIntro", "cell", "normalFgNodes", "renderFgSegs", "mirrorFgNodes", "buildMirrorPlacements", "showDayNumbers", "showWeekNumbers", "renderFillSegs", "cellMinHeight", "componentDidMount", "addResizeHandler", "componentDidUpdate", "prevProps", "prevState", "currentProps", "componentWillUnmount", "removeResizeHandler", "dateSelectionSegs", "segPlacements", "is<PERSON><PERSON><PERSON><PERSON>", "nodes", "left", "right", "isRtl", "lefts", "rights", "top", "fillType", "leftRightCss", "isExternalSizingChange", "clientWidth", "frameEls", "currentMap", "originEl", "current", "newPositionCache", "similarTo", "setState", "oldSegHeights", "newSegHeights", "querySegHeights", "limitByContentHeight", "safeSetState", "computeMaxContentHeight", "segElMap", "height", "Math", "round", "getBoundingClientRect", "max", "firstKey", "cellEl", "fcContainerEl", "bottom", "getCellEls", "elMap", "addStateEquality", "mirrorSegs", "colPlacements", "topsByInstanceId", "buildAbsoluteTopHash", "placements", "TableRows", "splitBusinessHourSegs", "splitBgEventSegs", "splitFgEventSegs", "splitDateSelectionSegs", "splitEventDrag", "splitEventResize", "rowRefs", "businessHourSegsByRow", "bgEventSegsByRow", "fgEventSegsByRow", "dateSelectionSegsByRow", "eventDragByRow", "eventResizeByRow", "aspectRatio", "unit", "nowDate", "toISOString", "renderRowIntro", "isSegAllDay", "clientHeight", "registerInteractiveComponent", "rootEl", "firstCellEl", "closest", "isHitComboAllowed", "unregisterInteractiveComponent", "prepareHits", "rowPositions", "collect", "row<PERSON><PERSON><PERSON>", "colPositions", "queryHit", "positionLeft", "positionTop", "leftToIndex", "topToIndex", "dateSpan", "getCellRange", "dayEl", "getCellEl", "tops", "bottoms", "layer", "Table", "needsScrollReset", "expandRows", "limitViaBalanced", "classNames", "join", "width", "tableMin<PERSON>idth", "colGroupNode", "requestScrollReset", "flushScrollReset", "subjectEl", "getScrollSubjectEl", "scrollEl", "scrollTop", "containerEl", "currentRangeUnit", "match", "querySelector", "currentDate", "DayTableSlicer", "forceDayIfListItem", "sliceRange", "date<PERSON><PERSON><PERSON>", "dayTableModel", "DayTable", "slicer", "tableRef", "sliceProps", "nextDayThreshold", "headerAlignElRef", "DayTableView", "buildDayTableModel", "headerRef", "dateProfileGenerator", "headerContent", "dayHeaders", "dates", "headerDates", "datesRepDistinctDays", "contentArg", "businessHours", "dateSelection", "eventStore", "eventUiBases", "tableColGroupNode", "weekNumbers", "daySeries", "renderRange", "test", "TableDateProfileGenerator", "buildRenderRange", "isRangeAllDay", "buildDayTableRenderRange", "snapToWeek", "fixedWeekCount", "endOfWeek", "startOfWeek", "lastMonthRenderStart", "startOfMonth", "ceil", "css_248z", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@fullcalendar/daygrid/internal.js"], "sourcesContent": ["import { DateComponent, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, renderScrollShim, createFormatter, BaseComponent, StandardEvent, buildSegTimeText, EventContainer, getSegAnchorAttrs, memoize, MoreLinkContainer, getSegMeta, getUniqueDomId, setRef, DayCellContainer, WeekNumberContainer, buildNavLinkAttrs, hasCustomDayCellContent, addMs, intersectRanges, addDays, SegHierarchy, buildEntryKey, intersectSpans, RefMap, sortEventSegs, isPropsEqual, buildEventRangeKey, BgEvent, renderFill, PositionCache, NowTimer, formatIsoMonthStr, formatDayString, Slicer, DayHeader, DaySeriesModel, DayTableModel, DateProfileGenerator, addWeeks, diffWeeks, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createRef, createElement, Fragment } from '@fullcalendar/core/preact.js';\n\n/* An abstract class for the daygrid views, as well as month view. Renders one or more rows of day cells.\n----------------------------------------------------------------------------------------------------------------------*/\n// It is a manager for a Table subcomponent, which does most of the heavy lifting.\n// It is responsible for managing width/height.\nclass TableView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.headerElRef = createRef();\n    }\n    renderSimpleLayout(headerRowContent, bodyContent) {\n        let { props, context } = this;\n        let sections = [];\n        let stickyHeaderDates = getStickyHeaderDates(context.options);\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunk: {\n                    elRef: this.headerElRef,\n                    tableClassName: 'fc-col-header',\n                    rowContent: headerRowContent,\n                },\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunk: { content: bodyContent },\n        });\n        return (createElement(ViewContainer, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            createElement(SimpleScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, collapsibleWidth: props.forPrint, cols: [] /* TODO: make optional? */, sections: sections })));\n    }\n    renderHScrollLayout(headerRowContent, bodyContent, colCnt, dayMinWidth) {\n        let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n        if (!ScrollGrid) {\n            throw new Error('No ScrollGrid implementation');\n        }\n        let { props, context } = this;\n        let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n        let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n        let sections = [];\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunks: [{\n                        key: 'main',\n                        elRef: this.headerElRef,\n                        tableClassName: 'fc-col-header',\n                        rowContent: headerRowContent,\n                    }],\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            chunks: [{\n                    key: 'main',\n                    content: bodyContent,\n                }],\n        });\n        if (stickyFooterScrollbar) {\n            sections.push({\n                type: 'footer',\n                key: 'footer',\n                isSticky: true,\n                chunks: [{\n                        key: 'main',\n                        content: renderScrollShim,\n                    }],\n            });\n        }\n        return (createElement(ViewContainer, { elClasses: ['fc-daygrid'], viewSpec: context.viewSpec },\n            createElement(ScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, forPrint: props.forPrint, collapsibleWidth: props.forPrint, colGroups: [{ cols: [{ span: colCnt, minWidth: dayMinWidth }] }], sections: sections })));\n    }\n}\n\nfunction splitSegsByRow(segs, rowCnt) {\n    let byRow = [];\n    for (let i = 0; i < rowCnt; i += 1) {\n        byRow[i] = [];\n    }\n    for (let seg of segs) {\n        byRow[seg.row].push(seg);\n    }\n    return byRow;\n}\nfunction splitSegsByFirstCol(segs, colCnt) {\n    let byCol = [];\n    for (let i = 0; i < colCnt; i += 1) {\n        byCol[i] = [];\n    }\n    for (let seg of segs) {\n        byCol[seg.firstCol].push(seg);\n    }\n    return byCol;\n}\nfunction splitInteractionByRow(ui, rowCnt) {\n    let byRow = [];\n    if (!ui) {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = null;\n        }\n    }\n    else {\n        for (let i = 0; i < rowCnt; i += 1) {\n            byRow[i] = {\n                affectedInstances: ui.affectedInstances,\n                isEvent: ui.isEvent,\n                segs: [],\n            };\n        }\n        for (let seg of ui.segs) {\n            byRow[seg.row].segs.push(seg);\n        }\n    }\n    return byRow;\n}\n\nconst DEFAULT_TABLE_EVENT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    omitZeroMinute: true,\n    meridiem: 'narrow',\n});\nfunction hasListItemDisplay(seg) {\n    let { display } = seg.eventRange.ui;\n    return display === 'list-item' || (display === 'auto' &&\n        !seg.eventRange.def.allDay &&\n        seg.firstCol === seg.lastCol && // can't be multi-day\n        seg.isStart && // \"\n        seg.isEnd // \"\n    );\n}\n\nclass TableBlockEvent extends BaseComponent {\n    render() {\n        let { props } = this;\n        return (createElement(StandardEvent, Object.assign({}, props, { elClasses: ['fc-daygrid-event', 'fc-daygrid-block-event', 'fc-h-event'], defaultTimeFormat: DEFAULT_TABLE_EVENT_TIME_FORMAT, defaultDisplayEventEnd: props.defaultDisplayEventEnd, disableResizing: !props.seg.eventRange.def.allDay })));\n    }\n}\n\nclass TableListItemEvent extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { seg } = props;\n        let timeFormat = options.eventTimeFormat || DEFAULT_TABLE_EVENT_TIME_FORMAT;\n        let timeText = buildSegTimeText(seg, timeFormat, context, true, props.defaultDisplayEventEnd);\n        return (createElement(EventContainer, Object.assign({}, props, { elTag: \"a\", elClasses: ['fc-daygrid-event', 'fc-daygrid-dot-event'], elAttrs: getSegAnchorAttrs(props.seg, context), defaultGenerator: renderInnerContent, timeText: timeText, isResizing: false, isDateSelecting: false })));\n    }\n}\nfunction renderInnerContent(renderProps) {\n    return (createElement(Fragment, null,\n        createElement(\"div\", { className: \"fc-daygrid-event-dot\", style: { borderColor: renderProps.borderColor || renderProps.backgroundColor } }),\n        renderProps.timeText && (createElement(\"div\", { className: \"fc-event-time\" }, renderProps.timeText)),\n        createElement(\"div\", { className: \"fc-event-title\" }, renderProps.event.title || createElement(Fragment, null, \"\\u00A0\"))));\n}\n\nclass TableCellMoreLink extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.compileSegs = memoize(compileSegs);\n    }\n    render() {\n        let { props } = this;\n        let { allSegs, invisibleSegs } = this.compileSegs(props.singlePlacements);\n        return (createElement(MoreLinkContainer, { elClasses: ['fc-daygrid-more-link'], dateProfile: props.dateProfile, todayRange: props.todayRange, allDayDate: props.allDayDate, moreCnt: props.moreCnt, allSegs: allSegs, hiddenSegs: invisibleSegs, alignmentElRef: props.alignmentElRef, alignGridTop: props.alignGridTop, extraDateSpan: props.extraDateSpan, popoverContent: () => {\n                let isForcedInvisible = (props.eventDrag ? props.eventDrag.affectedInstances : null) ||\n                    (props.eventResize ? props.eventResize.affectedInstances : null) ||\n                    {};\n                return (createElement(Fragment, null, allSegs.map((seg) => {\n                    let instanceId = seg.eventRange.instance.instanceId;\n                    return (createElement(\"div\", { className: \"fc-daygrid-event-harness\", key: instanceId, style: {\n                            visibility: isForcedInvisible[instanceId] ? 'hidden' : '',\n                        } }, hasListItemDisplay(seg) ? (createElement(TableListItemEvent, Object.assign({ seg: seg, isDragging: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, getSegMeta(seg, props.todayRange)))) : (createElement(TableBlockEvent, Object.assign({ seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: instanceId === props.eventSelection, defaultDisplayEventEnd: false }, getSegMeta(seg, props.todayRange))))));\n                })));\n            } }));\n    }\n}\nfunction compileSegs(singlePlacements) {\n    let allSegs = [];\n    let invisibleSegs = [];\n    for (let placement of singlePlacements) {\n        allSegs.push(placement.seg);\n        if (!placement.isVisible) {\n            invisibleSegs.push(placement.seg);\n        }\n    }\n    return { allSegs, invisibleSegs };\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'narrow' });\nclass TableCell extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.rootElRef = createRef();\n        this.state = {\n            dayNumberId: getUniqueDomId(),\n        };\n        this.handleRootEl = (el) => {\n            setRef(this.rootElRef, el);\n            setRef(this.props.elRef, el);\n        };\n    }\n    render() {\n        let { context, props, state, rootElRef } = this;\n        let { options, dateEnv } = context;\n        let { date, dateProfile } = props;\n        // TODO: memoize this?\n        const isMonthStart = props.showDayNumber &&\n            shouldDisplayMonthStart(date, dateProfile.currentRange, dateEnv);\n        return (createElement(DayCellContainer, { elTag: \"td\", elRef: this.handleRootEl, elClasses: [\n                'fc-daygrid-day',\n                ...(props.extraClassNames || []),\n            ], elAttrs: Object.assign(Object.assign(Object.assign({}, props.extraDataAttrs), (props.showDayNumber ? { 'aria-labelledby': state.dayNumberId } : {})), { role: 'gridcell' }), defaultGenerator: renderTopInner, date: date, dateProfile: dateProfile, todayRange: props.todayRange, showDayNumber: props.showDayNumber, isMonthStart: isMonthStart, extraRenderProps: props.extraRenderProps }, (InnerContent, renderProps) => (createElement(\"div\", { ref: props.innerElRef, className: \"fc-daygrid-day-frame fc-scrollgrid-sync-inner\", style: { minHeight: props.minHeight } },\n            props.showWeekNumber && (createElement(WeekNumberContainer, { elTag: \"a\", elClasses: ['fc-daygrid-week-number'], elAttrs: buildNavLinkAttrs(context, date, 'week'), date: date, defaultFormat: DEFAULT_WEEK_NUM_FORMAT })),\n            !renderProps.isDisabled &&\n                (props.showDayNumber || hasCustomDayCellContent(options) || props.forceDayTop) ? (createElement(\"div\", { className: \"fc-daygrid-day-top\" },\n                createElement(InnerContent, { elTag: \"a\", elClasses: [\n                        'fc-daygrid-day-number',\n                        isMonthStart && 'fc-daygrid-month-start',\n                    ], elAttrs: Object.assign(Object.assign({}, buildNavLinkAttrs(context, date)), { id: state.dayNumberId }) }))) : props.showDayNumber ? (\n            // for creating correct amount of space (see issue #7162)\n            createElement(\"div\", { className: \"fc-daygrid-day-top\", style: { visibility: 'hidden' } },\n                createElement(\"a\", { className: \"fc-daygrid-day-number\" }, \"\\u00A0\"))) : undefined,\n            createElement(\"div\", { className: \"fc-daygrid-day-events\", ref: props.fgContentElRef },\n                props.fgContent,\n                createElement(\"div\", { className: \"fc-daygrid-day-bottom\", style: { marginTop: props.moreMarginTop } },\n                    createElement(TableCellMoreLink, { allDayDate: date, singlePlacements: props.singlePlacements, moreCnt: props.moreCnt, alignmentElRef: rootElRef, alignGridTop: !props.showDayNumber, extraDateSpan: props.extraDateSpan, dateProfile: props.dateProfile, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, todayRange: props.todayRange }))),\n            createElement(\"div\", { className: \"fc-daygrid-day-bg\" }, props.bgContent)))));\n    }\n}\nfunction renderTopInner(props) {\n    return props.dayNumberText || createElement(Fragment, null, \"\\u00A0\");\n}\nfunction shouldDisplayMonthStart(date, currentRange, dateEnv) {\n    const { start: currentStart, end: currentEnd } = currentRange;\n    const currentEndIncl = addMs(currentEnd, -1);\n    const currentFirstYear = dateEnv.getYear(currentStart);\n    const currentFirstMonth = dateEnv.getMonth(currentStart);\n    const currentLastYear = dateEnv.getYear(currentEndIncl);\n    const currentLastMonth = dateEnv.getMonth(currentEndIncl);\n    // spans more than one month?\n    return !(currentFirstYear === currentLastYear && currentFirstMonth === currentLastMonth) &&\n        Boolean(\n        // first date in current view?\n        date.valueOf() === currentStart.valueOf() ||\n            // a month-start that's within the current range?\n            (dateEnv.getDay(date) === 1 && date.valueOf() < currentEnd.valueOf()));\n}\n\nfunction generateSegKey(seg) {\n    return seg.eventRange.instance.instanceId + ':' + seg.firstCol;\n}\nfunction generateSegUid(seg) {\n    return generateSegKey(seg) + ':' + seg.lastCol;\n}\nfunction computeFgSegPlacement(segs, // assumed already sorted\ndayMaxEvents, dayMaxEventRows, strictOrder, segHeights, maxContentHeight, cells) {\n    let hierarchy = new DayGridSegHierarchy((segEntry) => {\n        // TODO: more DRY with generateSegUid\n        let segUid = segs[segEntry.index].eventRange.instance.instanceId +\n            ':' + segEntry.span.start +\n            ':' + (segEntry.span.end - 1);\n        // if no thickness known, assume 1 (if 0, so small it always fits)\n        return segHeights[segUid] || 1;\n    });\n    hierarchy.allowReslicing = true;\n    hierarchy.strictOrder = strictOrder;\n    if (dayMaxEvents === true || dayMaxEventRows === true) {\n        hierarchy.maxCoord = maxContentHeight;\n        hierarchy.hiddenConsumes = true;\n    }\n    else if (typeof dayMaxEvents === 'number') {\n        hierarchy.maxStackCnt = dayMaxEvents;\n    }\n    else if (typeof dayMaxEventRows === 'number') {\n        hierarchy.maxStackCnt = dayMaxEventRows;\n        hierarchy.hiddenConsumes = true;\n    }\n    // create segInputs only for segs with known heights\n    let segInputs = [];\n    let unknownHeightSegs = [];\n    for (let i = 0; i < segs.length; i += 1) {\n        let seg = segs[i];\n        let segUid = generateSegUid(seg);\n        let eventHeight = segHeights[segUid];\n        if (eventHeight != null) {\n            segInputs.push({\n                index: i,\n                span: {\n                    start: seg.firstCol,\n                    end: seg.lastCol + 1,\n                },\n            });\n        }\n        else {\n            unknownHeightSegs.push(seg);\n        }\n    }\n    let hiddenEntries = hierarchy.addSegs(segInputs);\n    let segRects = hierarchy.toRects();\n    let { singleColPlacements, multiColPlacements, leftoverMargins } = placeRects(segRects, segs, cells);\n    let moreCnts = [];\n    let moreMarginTops = [];\n    // add segs with unknown heights\n    for (let seg of unknownHeightSegs) {\n        multiColPlacements[seg.firstCol].push({\n            seg,\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = seg.firstCol; col <= seg.lastCol; col += 1) {\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // add the hidden entries\n    for (let col = 0; col < cells.length; col += 1) {\n        moreCnts.push(0);\n    }\n    for (let hiddenEntry of hiddenEntries) {\n        let seg = segs[hiddenEntry.index];\n        let hiddenSpan = hiddenEntry.span;\n        multiColPlacements[hiddenSpan.start].push({\n            seg: resliceSeg(seg, hiddenSpan.start, hiddenSpan.end, cells),\n            isVisible: false,\n            isAbsolute: true,\n            absoluteTop: 0,\n            marginTop: 0,\n        });\n        for (let col = hiddenSpan.start; col < hiddenSpan.end; col += 1) {\n            moreCnts[col] += 1;\n            singleColPlacements[col].push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: false,\n                isAbsolute: false,\n                absoluteTop: 0,\n                marginTop: 0,\n            });\n        }\n    }\n    // deal with leftover margins\n    for (let col = 0; col < cells.length; col += 1) {\n        moreMarginTops.push(leftoverMargins[col]);\n    }\n    return { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops };\n}\n// rects ordered by top coord, then left\nfunction placeRects(allRects, segs, cells) {\n    let rectsByEachCol = groupRectsByEachCol(allRects, cells.length);\n    let singleColPlacements = [];\n    let multiColPlacements = [];\n    let leftoverMargins = [];\n    for (let col = 0; col < cells.length; col += 1) {\n        let rects = rectsByEachCol[col];\n        // compute all static segs in singlePlacements\n        let singlePlacements = [];\n        let currentHeight = 0;\n        let currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            singlePlacements.push({\n                seg: resliceSeg(seg, col, col + 1, cells),\n                isVisible: true,\n                isAbsolute: false,\n                absoluteTop: rect.levelCoord,\n                marginTop: rect.levelCoord - currentHeight,\n            });\n            currentHeight = rect.levelCoord + rect.thickness;\n        }\n        // compute mixed static/absolute segs in multiPlacements\n        let multiPlacements = [];\n        currentHeight = 0;\n        currentMarginTop = 0;\n        for (let rect of rects) {\n            let seg = segs[rect.index];\n            let isAbsolute = rect.span.end - rect.span.start > 1; // multi-column?\n            let isFirstCol = rect.span.start === col;\n            currentMarginTop += rect.levelCoord - currentHeight; // amount of space since bottom of previous seg\n            currentHeight = rect.levelCoord + rect.thickness; // height will now be bottom of current seg\n            if (isAbsolute) {\n                currentMarginTop += rect.thickness;\n                if (isFirstCol) {\n                    multiPlacements.push({\n                        seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                        isVisible: true,\n                        isAbsolute: true,\n                        absoluteTop: rect.levelCoord,\n                        marginTop: 0,\n                    });\n                }\n            }\n            else if (isFirstCol) {\n                multiPlacements.push({\n                    seg: resliceSeg(seg, rect.span.start, rect.span.end, cells),\n                    isVisible: true,\n                    isAbsolute: false,\n                    absoluteTop: rect.levelCoord,\n                    marginTop: currentMarginTop, // claim the margin\n                });\n                currentMarginTop = 0;\n            }\n        }\n        singleColPlacements.push(singlePlacements);\n        multiColPlacements.push(multiPlacements);\n        leftoverMargins.push(currentMarginTop);\n    }\n    return { singleColPlacements, multiColPlacements, leftoverMargins };\n}\nfunction groupRectsByEachCol(rects, colCnt) {\n    let rectsByEachCol = [];\n    for (let col = 0; col < colCnt; col += 1) {\n        rectsByEachCol.push([]);\n    }\n    for (let rect of rects) {\n        for (let col = rect.span.start; col < rect.span.end; col += 1) {\n            rectsByEachCol[col].push(rect);\n        }\n    }\n    return rectsByEachCol;\n}\nfunction resliceSeg(seg, spanStart, spanEnd, cells) {\n    if (seg.firstCol === spanStart && seg.lastCol === spanEnd - 1) {\n        return seg;\n    }\n    let eventRange = seg.eventRange;\n    let origRange = eventRange.range;\n    let slicedRange = intersectRanges(origRange, {\n        start: cells[spanStart].date,\n        end: addDays(cells[spanEnd - 1].date, 1),\n    });\n    return Object.assign(Object.assign({}, seg), { firstCol: spanStart, lastCol: spanEnd - 1, eventRange: {\n            def: eventRange.def,\n            ui: Object.assign(Object.assign({}, eventRange.ui), { durationEditable: false }),\n            instance: eventRange.instance,\n            range: slicedRange,\n        }, isStart: seg.isStart && slicedRange.start.valueOf() === origRange.start.valueOf(), isEnd: seg.isEnd && slicedRange.end.valueOf() === origRange.end.valueOf() });\n}\nclass DayGridSegHierarchy extends SegHierarchy {\n    constructor() {\n        super(...arguments);\n        // config\n        this.hiddenConsumes = false;\n        // allows us to keep hidden entries in the hierarchy so they take up space\n        this.forceHidden = {};\n    }\n    addSegs(segInputs) {\n        const hiddenSegs = super.addSegs(segInputs);\n        const { entriesByLevel } = this;\n        const excludeHidden = (entry) => !this.forceHidden[buildEntryKey(entry)];\n        // remove the forced-hidden segs\n        for (let level = 0; level < entriesByLevel.length; level += 1) {\n            entriesByLevel[level] = entriesByLevel[level].filter(excludeHidden);\n        }\n        return hiddenSegs;\n    }\n    handleInvalidInsertion(insertion, entry, hiddenEntries) {\n        const { entriesByLevel, forceHidden } = this;\n        const { touchingEntry, touchingLevel, touchingLateral } = insertion;\n        // the entry that the new insertion is touching must be hidden\n        if (this.hiddenConsumes && touchingEntry) {\n            const touchingEntryId = buildEntryKey(touchingEntry);\n            if (!forceHidden[touchingEntryId]) {\n                if (this.allowReslicing) {\n                    // split up the touchingEntry, reinsert it\n                    const hiddenEntry = Object.assign(Object.assign({}, touchingEntry), { span: intersectSpans(touchingEntry.span, entry.span) });\n                    // reinsert the area that turned into a \"more\" link (so no other entries try to\n                    // occupy the space) but mark it forced-hidden\n                    const hiddenEntryId = buildEntryKey(hiddenEntry);\n                    forceHidden[hiddenEntryId] = true;\n                    entriesByLevel[touchingLevel][touchingLateral] = hiddenEntry;\n                    hiddenEntries.push(hiddenEntry);\n                    this.splitEntry(touchingEntry, entry, hiddenEntries);\n                }\n                else {\n                    forceHidden[touchingEntryId] = true;\n                    hiddenEntries.push(touchingEntry);\n                }\n            }\n        }\n        // will try to reslice...\n        super.handleInvalidInsertion(insertion, entry, hiddenEntries);\n    }\n}\n\nclass TableRow extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.cellElRefs = new RefMap(); // the <td>\n        this.frameElRefs = new RefMap(); // the fc-daygrid-day-frame\n        this.fgElRefs = new RefMap(); // the fc-daygrid-day-events\n        this.segHarnessRefs = new RefMap(); // indexed by \"instanceId:firstCol\"\n        this.rootElRef = createRef();\n        this.state = {\n            framePositions: null,\n            maxContentHeight: null,\n            segHeights: {},\n        };\n        this.handleResize = (isForced) => {\n            if (isForced) {\n                this.updateSizing(true); // isExternal=true\n            }\n        };\n    }\n    render() {\n        let { props, state, context } = this;\n        let { options } = context;\n        let colCnt = props.cells.length;\n        let businessHoursByCol = splitSegsByFirstCol(props.businessHourSegs, colCnt);\n        let bgEventSegsByCol = splitSegsByFirstCol(props.bgEventSegs, colCnt);\n        let highlightSegsByCol = splitSegsByFirstCol(this.getHighlightSegs(), colCnt);\n        let mirrorSegsByCol = splitSegsByFirstCol(this.getMirrorSegs(), colCnt);\n        let { singleColPlacements, multiColPlacements, moreCnts, moreMarginTops } = computeFgSegPlacement(sortEventSegs(props.fgEventSegs, options.eventOrder), props.dayMaxEvents, props.dayMaxEventRows, options.eventOrderStrict, state.segHeights, state.maxContentHeight, props.cells);\n        let isForcedInvisible = // TODO: messy way to compute this\n         (props.eventDrag && props.eventDrag.affectedInstances) ||\n            (props.eventResize && props.eventResize.affectedInstances) ||\n            {};\n        return (createElement(\"tr\", { ref: this.rootElRef, role: \"row\" },\n            props.renderIntro && props.renderIntro(),\n            props.cells.map((cell, col) => {\n                let normalFgNodes = this.renderFgSegs(col, props.forPrint ? singleColPlacements[col] : multiColPlacements[col], props.todayRange, isForcedInvisible);\n                let mirrorFgNodes = this.renderFgSegs(col, buildMirrorPlacements(mirrorSegsByCol[col], multiColPlacements), props.todayRange, {}, Boolean(props.eventDrag), Boolean(props.eventResize), false);\n                return (createElement(TableCell, { key: cell.key, elRef: this.cellElRefs.createRef(cell.key), innerElRef: this.frameElRefs.createRef(cell.key) /* FF <td> problem, but okay to use for left/right. TODO: rename prop */, dateProfile: props.dateProfile, date: cell.date, showDayNumber: props.showDayNumbers, showWeekNumber: props.showWeekNumbers && col === 0, forceDayTop: props.showWeekNumbers /* even displaying weeknum for row, not necessarily day */, todayRange: props.todayRange, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, extraRenderProps: cell.extraRenderProps, extraDataAttrs: cell.extraDataAttrs, extraClassNames: cell.extraClassNames, extraDateSpan: cell.extraDateSpan, moreCnt: moreCnts[col], moreMarginTop: moreMarginTops[col], singlePlacements: singleColPlacements[col], fgContentElRef: this.fgElRefs.createRef(cell.key), fgContent: ( // Fragment scopes the keys\n                    createElement(Fragment, null,\n                        createElement(Fragment, null, normalFgNodes),\n                        createElement(Fragment, null, mirrorFgNodes))), bgContent: ( // Fragment scopes the keys\n                    createElement(Fragment, null,\n                        this.renderFillSegs(highlightSegsByCol[col], 'highlight'),\n                        this.renderFillSegs(businessHoursByCol[col], 'non-business'),\n                        this.renderFillSegs(bgEventSegsByCol[col], 'bg-event'))), minHeight: props.cellMinHeight }));\n            })));\n    }\n    componentDidMount() {\n        this.updateSizing(true);\n        this.context.addResizeHandler(this.handleResize);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        let currentProps = this.props;\n        this.updateSizing(!isPropsEqual(prevProps, currentProps));\n    }\n    componentWillUnmount() {\n        this.context.removeResizeHandler(this.handleResize);\n    }\n    getHighlightSegs() {\n        let { props } = this;\n        if (props.eventDrag && props.eventDrag.segs.length) { // messy check\n            return props.eventDrag.segs;\n        }\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return props.dateSelectionSegs;\n    }\n    getMirrorSegs() {\n        let { props } = this;\n        if (props.eventResize && props.eventResize.segs.length) { // messy check\n            return props.eventResize.segs;\n        }\n        return [];\n    }\n    renderFgSegs(col, segPlacements, todayRange, isForcedInvisible, isDragging, isResizing, isDateSelecting) {\n        let { context } = this;\n        let { eventSelection } = this.props;\n        let { framePositions } = this.state;\n        let defaultDisplayEventEnd = this.props.cells.length === 1; // colCnt === 1\n        let isMirror = isDragging || isResizing || isDateSelecting;\n        let nodes = [];\n        if (framePositions) {\n            for (let placement of segPlacements) {\n                let { seg } = placement;\n                let { instanceId } = seg.eventRange.instance;\n                let isVisible = placement.isVisible && !isForcedInvisible[instanceId];\n                let isAbsolute = placement.isAbsolute;\n                let left = '';\n                let right = '';\n                if (isAbsolute) {\n                    if (context.isRtl) {\n                        right = 0;\n                        left = framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol];\n                    }\n                    else {\n                        left = 0;\n                        right = framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol];\n                    }\n                }\n                /*\n                known bug: events that are force to be list-item but span multiple days still take up space in later columns\n                todo: in print view, for multi-day events, don't display title within non-start/end segs\n                */\n                nodes.push(createElement(\"div\", { className: 'fc-daygrid-event-harness' + (isAbsolute ? ' fc-daygrid-event-harness-abs' : ''), key: generateSegKey(seg), ref: isMirror ? null : this.segHarnessRefs.createRef(generateSegUid(seg)), style: {\n                        visibility: isVisible ? '' : 'hidden',\n                        marginTop: isAbsolute ? '' : placement.marginTop,\n                        top: isAbsolute ? placement.absoluteTop : '',\n                        left,\n                        right,\n                    } }, hasListItemDisplay(seg) ? (createElement(TableListItemEvent, Object.assign({ seg: seg, isDragging: isDragging, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, getSegMeta(seg, todayRange)))) : (createElement(TableBlockEvent, Object.assign({ seg: seg, isDragging: isDragging, isResizing: isResizing, isDateSelecting: isDateSelecting, isSelected: instanceId === eventSelection, defaultDisplayEventEnd: defaultDisplayEventEnd }, getSegMeta(seg, todayRange))))));\n            }\n        }\n        return nodes;\n    }\n    renderFillSegs(segs, fillType) {\n        let { isRtl } = this.context;\n        let { todayRange } = this.props;\n        let { framePositions } = this.state;\n        let nodes = [];\n        if (framePositions) {\n            for (let seg of segs) {\n                let leftRightCss = isRtl ? {\n                    right: 0,\n                    left: framePositions.lefts[seg.lastCol] - framePositions.lefts[seg.firstCol],\n                } : {\n                    left: 0,\n                    right: framePositions.rights[seg.firstCol] - framePositions.rights[seg.lastCol],\n                };\n                nodes.push(createElement(\"div\", { key: buildEventRangeKey(seg.eventRange), className: \"fc-daygrid-bg-harness\", style: leftRightCss }, fillType === 'bg-event' ?\n                    createElement(BgEvent, Object.assign({ seg: seg }, getSegMeta(seg, todayRange))) :\n                    renderFill(fillType)));\n            }\n        }\n        return createElement(Fragment, {}, ...nodes);\n    }\n    updateSizing(isExternalSizingChange) {\n        let { props, state, frameElRefs } = this;\n        if (!props.forPrint &&\n            props.clientWidth !== null // positioning ready?\n        ) {\n            if (isExternalSizingChange) {\n                let frameEls = props.cells.map((cell) => frameElRefs.currentMap[cell.key]);\n                if (frameEls.length) {\n                    let originEl = this.rootElRef.current;\n                    let newPositionCache = new PositionCache(originEl, frameEls, true, // isHorizontal\n                    false);\n                    if (!state.framePositions || !state.framePositions.similarTo(newPositionCache)) {\n                        this.setState({\n                            framePositions: new PositionCache(originEl, frameEls, true, // isHorizontal\n                            false),\n                        });\n                    }\n                }\n            }\n            const oldSegHeights = this.state.segHeights;\n            const newSegHeights = this.querySegHeights();\n            const limitByContentHeight = props.dayMaxEvents === true || props.dayMaxEventRows === true;\n            this.safeSetState({\n                // HACK to prevent oscillations of events being shown/hidden from max-event-rows\n                // Essentially, once you compute an element's height, never null-out.\n                // TODO: always display all events, as visibility:hidden?\n                segHeights: Object.assign(Object.assign({}, oldSegHeights), newSegHeights),\n                maxContentHeight: limitByContentHeight ? this.computeMaxContentHeight() : null,\n            });\n        }\n    }\n    querySegHeights() {\n        let segElMap = this.segHarnessRefs.currentMap;\n        let segHeights = {};\n        // get the max height amongst instance segs\n        for (let segUid in segElMap) {\n            let height = Math.round(segElMap[segUid].getBoundingClientRect().height);\n            segHeights[segUid] = Math.max(segHeights[segUid] || 0, height);\n        }\n        return segHeights;\n    }\n    computeMaxContentHeight() {\n        let firstKey = this.props.cells[0].key;\n        let cellEl = this.cellElRefs.currentMap[firstKey];\n        let fcContainerEl = this.fgElRefs.currentMap[firstKey];\n        return cellEl.getBoundingClientRect().bottom - fcContainerEl.getBoundingClientRect().top;\n    }\n    getCellEls() {\n        let elMap = this.cellElRefs.currentMap;\n        return this.props.cells.map((cell) => elMap[cell.key]);\n    }\n}\nTableRow.addStateEquality({\n    segHeights: isPropsEqual,\n});\nfunction buildMirrorPlacements(mirrorSegs, colPlacements) {\n    if (!mirrorSegs.length) {\n        return [];\n    }\n    let topsByInstanceId = buildAbsoluteTopHash(colPlacements); // TODO: cache this at first render?\n    return mirrorSegs.map((seg) => ({\n        seg,\n        isVisible: true,\n        isAbsolute: true,\n        absoluteTop: topsByInstanceId[seg.eventRange.instance.instanceId],\n        marginTop: 0,\n    }));\n}\nfunction buildAbsoluteTopHash(colPlacements) {\n    let topsByInstanceId = {};\n    for (let placements of colPlacements) {\n        for (let placement of placements) {\n            topsByInstanceId[placement.seg.eventRange.instance.instanceId] = placement.absoluteTop;\n        }\n    }\n    return topsByInstanceId;\n}\n\nclass TableRows extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.splitBusinessHourSegs = memoize(splitSegsByRow);\n        this.splitBgEventSegs = memoize(splitSegsByRow);\n        this.splitFgEventSegs = memoize(splitSegsByRow);\n        this.splitDateSelectionSegs = memoize(splitSegsByRow);\n        this.splitEventDrag = memoize(splitInteractionByRow);\n        this.splitEventResize = memoize(splitInteractionByRow);\n        this.rowRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        let rowCnt = props.cells.length;\n        let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, rowCnt);\n        let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, rowCnt);\n        let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, rowCnt);\n        let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, rowCnt);\n        let eventDragByRow = this.splitEventDrag(props.eventDrag, rowCnt);\n        let eventResizeByRow = this.splitEventResize(props.eventResize, rowCnt);\n        // for DayGrid view with many rows, force a min-height on cells so doesn't appear squished\n        // choose 7 because a month view will have max 6 rows\n        let cellMinHeight = (rowCnt >= 7 && props.clientWidth) ?\n            props.clientWidth / context.options.aspectRatio / 6 :\n            null;\n        return (createElement(NowTimer, { unit: \"day\" }, (nowDate, todayRange) => (createElement(Fragment, null, props.cells.map((cells, row) => (createElement(TableRow, { ref: this.rowRefs.createRef(row), key: cells.length\n                ? cells[0].date.toISOString() /* best? or put key on cell? or use diff formatter? */\n                : row // in case there are no cells (like when resource view is loading)\n            , showDayNumbers: rowCnt > 1, showWeekNumbers: props.showWeekNumbers, todayRange: todayRange, dateProfile: props.dateProfile, cells: cells, renderIntro: props.renderRowIntro, businessHourSegs: businessHourSegsByRow[row], eventSelection: props.eventSelection, bgEventSegs: bgEventSegsByRow[row].filter(isSegAllDay) /* hack */, fgEventSegs: fgEventSegsByRow[row], dateSelectionSegs: dateSelectionSegsByRow[row], eventDrag: eventDragByRow[row], eventResize: eventResizeByRow[row], dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, clientWidth: props.clientWidth, clientHeight: props.clientHeight, cellMinHeight: cellMinHeight, forPrint: props.forPrint })))))));\n    }\n    componentDidMount() {\n        this.registerInteractiveComponent();\n    }\n    componentDidUpdate() {\n        // for if started with zero cells\n        this.registerInteractiveComponent();\n    }\n    registerInteractiveComponent() {\n        if (!this.rootEl) {\n            // HACK: need a daygrid wrapper parent to do positioning\n            // NOTE: a daygrid resource view w/o resources can have zero cells\n            const firstCellEl = this.rowRefs.currentMap[0].getCellEls()[0];\n            const rootEl = firstCellEl ? firstCellEl.closest('.fc-daygrid-body') : null;\n            if (rootEl) {\n                this.rootEl = rootEl;\n                this.context.registerInteractiveComponent(this, {\n                    el: rootEl,\n                    isHitComboAllowed: this.props.isHitComboAllowed,\n                });\n            }\n        }\n    }\n    componentWillUnmount() {\n        if (this.rootEl) {\n            this.context.unregisterInteractiveComponent(this);\n            this.rootEl = null;\n        }\n    }\n    // Hit System\n    // ----------------------------------------------------------------------------------------------------\n    prepareHits() {\n        this.rowPositions = new PositionCache(this.rootEl, this.rowRefs.collect().map((rowObj) => rowObj.getCellEls()[0]), // first cell el in each row. TODO: not optimal\n        false, true);\n        this.colPositions = new PositionCache(this.rootEl, this.rowRefs.currentMap[0].getCellEls(), // cell els in first row\n        true, // horizontal\n        false);\n    }\n    queryHit(positionLeft, positionTop) {\n        let { colPositions, rowPositions } = this;\n        let col = colPositions.leftToIndex(positionLeft);\n        let row = rowPositions.topToIndex(positionTop);\n        if (row != null && col != null) {\n            let cell = this.props.cells[row][col];\n            return {\n                dateProfile: this.props.dateProfile,\n                dateSpan: Object.assign({ range: this.getCellRange(row, col), allDay: true }, cell.extraDateSpan),\n                dayEl: this.getCellEl(row, col),\n                rect: {\n                    left: colPositions.lefts[col],\n                    right: colPositions.rights[col],\n                    top: rowPositions.tops[row],\n                    bottom: rowPositions.bottoms[row],\n                },\n                layer: 0,\n            };\n        }\n        return null;\n    }\n    getCellEl(row, col) {\n        return this.rowRefs.currentMap[row].getCellEls()[col]; // TODO: not optimal\n    }\n    getCellRange(row, col) {\n        let start = this.props.cells[row][col].date;\n        let end = addDays(start, 1);\n        return { start, end };\n    }\n}\nfunction isSegAllDay(seg) {\n    return seg.eventRange.def.allDay;\n}\n\nclass Table extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.elRef = createRef();\n        this.needsScrollReset = false;\n    }\n    render() {\n        let { props } = this;\n        let { dayMaxEventRows, dayMaxEvents, expandRows } = props;\n        let limitViaBalanced = dayMaxEvents === true || dayMaxEventRows === true;\n        // if rows can't expand to fill fixed height, can't do balanced-height event limit\n        // TODO: best place to normalize these options?\n        if (limitViaBalanced && !expandRows) {\n            limitViaBalanced = false;\n            dayMaxEventRows = null;\n            dayMaxEvents = null;\n        }\n        let classNames = [\n            'fc-daygrid-body',\n            limitViaBalanced ? 'fc-daygrid-body-balanced' : 'fc-daygrid-body-unbalanced',\n            expandRows ? '' : 'fc-daygrid-body-natural', // will height of one row depend on the others?\n        ];\n        return (createElement(\"div\", { ref: this.elRef, className: classNames.join(' '), style: {\n                // these props are important to give this wrapper correct dimensions for interactions\n                // TODO: if we set it here, can we avoid giving to inner tables?\n                width: props.clientWidth,\n                minWidth: props.tableMinWidth,\n            } },\n            createElement(\"table\", { role: \"presentation\", className: \"fc-scrollgrid-sync-table\", style: {\n                    width: props.clientWidth,\n                    minWidth: props.tableMinWidth,\n                    height: expandRows ? props.clientHeight : '',\n                } },\n                props.colGroupNode,\n                createElement(\"tbody\", { role: \"presentation\" },\n                    createElement(TableRows, { dateProfile: props.dateProfile, cells: props.cells, renderRowIntro: props.renderRowIntro, showWeekNumbers: props.showWeekNumbers, clientWidth: props.clientWidth, clientHeight: props.clientHeight, businessHourSegs: props.businessHourSegs, bgEventSegs: props.bgEventSegs, fgEventSegs: props.fgEventSegs, dateSelectionSegs: props.dateSelectionSegs, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, dayMaxEvents: dayMaxEvents, dayMaxEventRows: dayMaxEventRows, forPrint: props.forPrint, isHitComboAllowed: props.isHitComboAllowed })))));\n    }\n    componentDidMount() {\n        this.requestScrollReset();\n    }\n    componentDidUpdate(prevProps) {\n        if (prevProps.dateProfile !== this.props.dateProfile) {\n            this.requestScrollReset();\n        }\n        else {\n            this.flushScrollReset();\n        }\n    }\n    requestScrollReset() {\n        this.needsScrollReset = true;\n        this.flushScrollReset();\n    }\n    flushScrollReset() {\n        if (this.needsScrollReset &&\n            this.props.clientWidth // sizes computed?\n        ) {\n            const subjectEl = getScrollSubjectEl(this.elRef.current, this.props.dateProfile);\n            if (subjectEl) {\n                const originEl = subjectEl.closest('.fc-daygrid-body');\n                const scrollEl = originEl.closest('.fc-scroller');\n                const scrollTop = subjectEl.getBoundingClientRect().top -\n                    originEl.getBoundingClientRect().top;\n                scrollEl.scrollTop = scrollTop ? (scrollTop + 1) : 0; // overcome border\n            }\n            this.needsScrollReset = false;\n        }\n    }\n}\nfunction getScrollSubjectEl(containerEl, dateProfile) {\n    let el;\n    if (dateProfile.currentRangeUnit.match(/year|month/)) {\n        el = containerEl.querySelector(`[data-date=\"${formatIsoMonthStr(dateProfile.currentDate)}-01\"]`);\n        // even if view is month-based, first-of-month might be hidden...\n    }\n    if (!el) {\n        el = containerEl.querySelector(`[data-date=\"${formatDayString(dateProfile.currentDate)}\"]`);\n        // could still be hidden if an interior-view hidden day\n    }\n    return el;\n}\n\nclass DayTableSlicer extends Slicer {\n    constructor() {\n        super(...arguments);\n        this.forceDayIfListItem = true;\n    }\n    sliceRange(dateRange, dayTableModel) {\n        return dayTableModel.sliceRange(dateRange);\n    }\n}\n\nclass DayTable extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.slicer = new DayTableSlicer();\n        this.tableRef = createRef();\n    }\n    render() {\n        let { props, context } = this;\n        return (createElement(Table, Object.assign({ ref: this.tableRef }, this.slicer.sliceProps(props, props.dateProfile, props.nextDayThreshold, context, props.dayTableModel), { dateProfile: props.dateProfile, cells: props.dayTableModel.cells, colGroupNode: props.colGroupNode, tableMinWidth: props.tableMinWidth, renderRowIntro: props.renderRowIntro, dayMaxEvents: props.dayMaxEvents, dayMaxEventRows: props.dayMaxEventRows, showWeekNumbers: props.showWeekNumbers, expandRows: props.expandRows, headerAlignElRef: props.headerAlignElRef, clientWidth: props.clientWidth, clientHeight: props.clientHeight, forPrint: props.forPrint })));\n    }\n}\n\nclass DayTableView extends TableView {\n    constructor() {\n        super(...arguments);\n        this.buildDayTableModel = memoize(buildDayTableModel);\n        this.headerRef = createRef();\n        this.tableRef = createRef();\n        // can't override any lifecycle methods from parent\n    }\n    render() {\n        let { options, dateProfileGenerator } = this.context;\n        let { props } = this;\n        let dayTableModel = this.buildDayTableModel(props.dateProfile, dateProfileGenerator);\n        let headerContent = options.dayHeaders && (createElement(DayHeader, { ref: this.headerRef, dateProfile: props.dateProfile, dates: dayTableModel.headerDates, datesRepDistinctDays: dayTableModel.rowCnt === 1 }));\n        let bodyContent = (contentArg) => (createElement(DayTable, { ref: this.tableRef, dateProfile: props.dateProfile, dayTableModel: dayTableModel, businessHours: props.businessHours, dateSelection: props.dateSelection, eventStore: props.eventStore, eventUiBases: props.eventUiBases, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, nextDayThreshold: options.nextDayThreshold, colGroupNode: contentArg.tableColGroupNode, tableMinWidth: contentArg.tableMinWidth, dayMaxEvents: options.dayMaxEvents, dayMaxEventRows: options.dayMaxEventRows, showWeekNumbers: options.weekNumbers, expandRows: !props.isHeightAuto, headerAlignElRef: this.headerElRef, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, forPrint: props.forPrint }));\n        return options.dayMinWidth\n            ? this.renderHScrollLayout(headerContent, bodyContent, dayTableModel.colCnt, options.dayMinWidth)\n            : this.renderSimpleLayout(headerContent, bodyContent);\n    }\n}\nfunction buildDayTableModel(dateProfile, dateProfileGenerator) {\n    let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n    return new DayTableModel(daySeries, /year|month|week/.test(dateProfile.currentRangeUnit));\n}\n\nclass TableDateProfileGenerator extends DateProfileGenerator {\n    // Computes the date range that will be rendered\n    buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay) {\n        let renderRange = super.buildRenderRange(currentRange, currentRangeUnit, isRangeAllDay);\n        let { props } = this;\n        return buildDayTableRenderRange({\n            currentRange: renderRange,\n            snapToWeek: /^(year|month)$/.test(currentRangeUnit),\n            fixedWeekCount: props.fixedWeekCount,\n            dateEnv: props.dateEnv,\n        });\n    }\n}\nfunction buildDayTableRenderRange(props) {\n    let { dateEnv, currentRange } = props;\n    let { start, end } = currentRange;\n    let endOfWeek;\n    // year and month views should be aligned with weeks. this is already done for week\n    if (props.snapToWeek) {\n        start = dateEnv.startOfWeek(start);\n        // make end-of-week if not already\n        endOfWeek = dateEnv.startOfWeek(end);\n        if (endOfWeek.valueOf() !== end.valueOf()) {\n            end = addWeeks(endOfWeek, 1);\n        }\n    }\n    // ensure 6 weeks\n    if (props.fixedWeekCount) {\n        // TODO: instead of these date-math gymnastics (for multimonth view),\n        // compute dateprofiles of all months, then use start of first and end of last.\n        let lastMonthRenderStart = dateEnv.startOfWeek(dateEnv.startOfMonth(addDays(currentRange.end, -1)));\n        let rowCnt = Math.ceil(// could be partial weeks due to hiddenDays\n        diffWeeks(lastMonthRenderStart, end));\n        end = addWeeks(end, 6 - rowCnt);\n    }\n    return { start, end };\n}\n\nvar css_248z = \":root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:\\\"\\\";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}\";\ninjectStyles(css_248z);\n\nexport { DayTableView as DayGridView, DayTable, DayTableSlicer, Table, TableDateProfileGenerator, TableRows, TableView, buildDayTableModel, buildDayTableRenderRange };\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,OAAO,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,MAAM,EAAEC,aAAa,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gCAAgC;AAC5tB,SAASC,SAAS,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,8BAA8B;;AAEjF;AACA;AACA;AACA;AACA,MAAMC,SAAS,SAAShD,aAAa,CAAC;EAClCiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,WAAW,GAAGN,SAAS,CAAC,CAAC;EAClC;EACAO,kBAAkBA,CAACC,gBAAgB,EAAEC,WAAW,EAAE;IAC9C,IAAI;MAAEC,KAAK;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,iBAAiB,GAAGzD,oBAAoB,CAACuD,OAAO,CAACG,OAAO,CAAC;IAC7D,IAAIN,gBAAgB,EAAE;MAClBI,QAAQ,CAACG,IAAI,CAAC;QACVC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,QAAQ;QACbC,QAAQ,EAAEL,iBAAiB;QAC3BM,KAAK,EAAE;UACHC,KAAK,EAAE,IAAI,CAACd,WAAW;UACvBe,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAEd;QAChB;MACJ,CAAC,CAAC;IACN;IACAI,QAAQ,CAACG,IAAI,CAAC;MACVC,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,MAAM;MACXM,MAAM,EAAE,IAAI;MACZJ,KAAK,EAAE;QAAEK,OAAO,EAAEf;MAAY;IAClC,CAAC,CAAC;IACF,OAAQR,aAAa,CAAC5C,aAAa,EAAE;MAAEoE,SAAS,EAAE,CAAC,YAAY,CAAC;MAAEC,QAAQ,EAAEf,OAAO,CAACe;IAAS,CAAC,EAC1FzB,aAAa,CAAC3C,gBAAgB,EAAE;MAAEiE,MAAM,EAAE,CAACb,KAAK,CAACiB,YAAY,IAAI,CAACjB,KAAK,CAACkB,QAAQ;MAAEC,gBAAgB,EAAEnB,KAAK,CAACkB,QAAQ;MAAEE,IAAI,EAAE,EAAE,CAAC;MAA4BlB,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAAC;EACvL;EACAmB,mBAAmBA,CAACvB,gBAAgB,EAAEC,WAAW,EAAEuB,MAAM,EAAEC,WAAW,EAAE;IACpE,IAAIC,UAAU,GAAG,IAAI,CAACvB,OAAO,CAACwB,WAAW,CAACC,cAAc;IACxD,IAAI,CAACF,UAAU,EAAE;MACb,MAAM,IAAIG,KAAK,CAAC,8BAA8B,CAAC;IACnD;IACA,IAAI;MAAE3B,KAAK;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAIE,iBAAiB,GAAG,CAACH,KAAK,CAACkB,QAAQ,IAAIxE,oBAAoB,CAACuD,OAAO,CAACG,OAAO,CAAC;IAChF,IAAIwB,qBAAqB,GAAG,CAAC5B,KAAK,CAACkB,QAAQ,IAAIrE,wBAAwB,CAACoD,OAAO,CAACG,OAAO,CAAC;IACxF,IAAIF,QAAQ,GAAG,EAAE;IACjB,IAAIJ,gBAAgB,EAAE;MAClBI,QAAQ,CAACG,IAAI,CAAC;QACVC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,QAAQ;QACbC,QAAQ,EAAEL,iBAAiB;QAC3B0B,MAAM,EAAE,CAAC;UACDtB,GAAG,EAAE,MAAM;UACXG,KAAK,EAAE,IAAI,CAACd,WAAW;UACvBe,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAEd;QAChB,CAAC;MACT,CAAC,CAAC;IACN;IACAI,QAAQ,CAACG,IAAI,CAAC;MACVC,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,MAAM;MACXM,MAAM,EAAE,IAAI;MACZgB,MAAM,EAAE,CAAC;QACDtB,GAAG,EAAE,MAAM;QACXO,OAAO,EAAEf;MACb,CAAC;IACT,CAAC,CAAC;IACF,IAAI6B,qBAAqB,EAAE;MACvB1B,QAAQ,CAACG,IAAI,CAAC;QACVC,IAAI,EAAE,QAAQ;QACdC,GAAG,EAAE,QAAQ;QACbC,QAAQ,EAAE,IAAI;QACdqB,MAAM,EAAE,CAAC;UACDtB,GAAG,EAAE,MAAM;UACXO,OAAO,EAAEhE;QACb,CAAC;MACT,CAAC,CAAC;IACN;IACA,OAAQyC,aAAa,CAAC5C,aAAa,EAAE;MAAEoE,SAAS,EAAE,CAAC,YAAY,CAAC;MAAEC,QAAQ,EAAEf,OAAO,CAACe;IAAS,CAAC,EAC1FzB,aAAa,CAACiC,UAAU,EAAE;MAAEX,MAAM,EAAE,CAACb,KAAK,CAACiB,YAAY,IAAI,CAACjB,KAAK,CAACkB,QAAQ;MAAEA,QAAQ,EAAElB,KAAK,CAACkB,QAAQ;MAAEC,gBAAgB,EAAEnB,KAAK,CAACkB,QAAQ;MAAEY,SAAS,EAAE,CAAC;QAAEV,IAAI,EAAE,CAAC;UAAEW,IAAI,EAAET,MAAM;UAAEU,QAAQ,EAAET;QAAY,CAAC;MAAE,CAAC,CAAC;MAAErB,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAAC;EACxO;AACJ;AAEA,SAAS+B,cAAcA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAClC,IAAIC,KAAK,GAAG,EAAE;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;IAChCD,KAAK,CAACC,CAAC,CAAC,GAAG,EAAE;EACjB;EACA,KAAK,IAAIC,GAAG,IAAIJ,IAAI,EAAE;IAClBE,KAAK,CAACE,GAAG,CAACC,GAAG,CAAC,CAAClC,IAAI,CAACiC,GAAG,CAAC;EAC5B;EACA,OAAOF,KAAK;AAChB;AACA,SAASI,mBAAmBA,CAACN,IAAI,EAAEZ,MAAM,EAAE;EACvC,IAAImB,KAAK,GAAG,EAAE;EACd,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,MAAM,EAAEe,CAAC,IAAI,CAAC,EAAE;IAChCI,KAAK,CAACJ,CAAC,CAAC,GAAG,EAAE;EACjB;EACA,KAAK,IAAIC,GAAG,IAAIJ,IAAI,EAAE;IAClBO,KAAK,CAACH,GAAG,CAACI,QAAQ,CAAC,CAACrC,IAAI,CAACiC,GAAG,CAAC;EACjC;EACA,OAAOG,KAAK;AAChB;AACA,SAASE,qBAAqBA,CAACC,EAAE,EAAET,MAAM,EAAE;EACvC,IAAIC,KAAK,GAAG,EAAE;EACd,IAAI,CAACQ,EAAE,EAAE;IACL,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MAChCD,KAAK,CAACC,CAAC,CAAC,GAAG,IAAI;IACnB;EACJ,CAAC,MACI;IACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;MAChCD,KAAK,CAACC,CAAC,CAAC,GAAG;QACPQ,iBAAiB,EAAED,EAAE,CAACC,iBAAiB;QACvCC,OAAO,EAAEF,EAAE,CAACE,OAAO;QACnBZ,IAAI,EAAE;MACV,CAAC;IACL;IACA,KAAK,IAAII,GAAG,IAAIM,EAAE,CAACV,IAAI,EAAE;MACrBE,KAAK,CAACE,GAAG,CAACC,GAAG,CAAC,CAACL,IAAI,CAAC7B,IAAI,CAACiC,GAAG,CAAC;IACjC;EACJ;EACA,OAAOF,KAAK;AAChB;AAEA,MAAMW,+BAA+B,GAAGhG,eAAe,CAAC;EACpDiG,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,SAASC,kBAAkBA,CAACd,GAAG,EAAE;EAC7B,IAAI;IAAEe;EAAQ,CAAC,GAAGf,GAAG,CAACgB,UAAU,CAACV,EAAE;EACnC,OAAOS,OAAO,KAAK,WAAW,IAAKA,OAAO,KAAK,MAAM,IACjD,CAACf,GAAG,CAACgB,UAAU,CAACC,GAAG,CAACC,MAAM,IAC1BlB,GAAG,CAACI,QAAQ,KAAKJ,GAAG,CAACmB,OAAO;EAAI;EAChCnB,GAAG,CAACoB,OAAO;EAAI;EACfpB,GAAG,CAACqB,KAAK,CAAC;EACb;AACL;AAEA,MAAMC,eAAe,SAAS5G,aAAa,CAAC;EACxC6G,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D;IAAM,CAAC,GAAG,IAAI;IACpB,OAAQT,aAAa,CAACtC,aAAa,EAAE6G,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/D,KAAK,EAAE;MAAEe,SAAS,EAAE,CAAC,kBAAkB,EAAE,wBAAwB,EAAE,YAAY,CAAC;MAAEiD,iBAAiB,EAAEjB,+BAA+B;MAAEkB,sBAAsB,EAAEjE,KAAK,CAACiE,sBAAsB;MAAEC,eAAe,EAAE,CAAClE,KAAK,CAACsC,GAAG,CAACgB,UAAU,CAACC,GAAG,CAACC;IAAO,CAAC,CAAC,CAAC;EAC5S;AACJ;AAEA,MAAMW,kBAAkB,SAASnH,aAAa,CAAC;EAC3C6G,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D,KAAK;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAEG;IAAQ,CAAC,GAAGH,OAAO;IACzB,IAAI;MAAEqC;IAAI,CAAC,GAAGtC,KAAK;IACnB,IAAIoE,UAAU,GAAGhE,OAAO,CAACiE,eAAe,IAAItB,+BAA+B;IAC3E,IAAIuB,QAAQ,GAAGpH,gBAAgB,CAACoF,GAAG,EAAE8B,UAAU,EAAEnE,OAAO,EAAE,IAAI,EAAED,KAAK,CAACiE,sBAAsB,CAAC;IAC7F,OAAQ1E,aAAa,CAACpC,cAAc,EAAE2G,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/D,KAAK,EAAE;MAAEuE,KAAK,EAAE,GAAG;MAAExD,SAAS,EAAE,CAAC,kBAAkB,EAAE,sBAAsB,CAAC;MAAEyD,OAAO,EAAEpH,iBAAiB,CAAC4C,KAAK,CAACsC,GAAG,EAAErC,OAAO,CAAC;MAAEwE,gBAAgB,EAAEC,kBAAkB;MAAEJ,QAAQ,EAAEA,QAAQ;MAAEK,UAAU,EAAE,KAAK;MAAEC,eAAe,EAAE;IAAM,CAAC,CAAC,CAAC;EACjS;AACJ;AACA,SAASF,kBAAkBA,CAACG,WAAW,EAAE;EACrC,OAAQtF,aAAa,CAACC,QAAQ,EAAE,IAAI,EAChCD,aAAa,CAAC,KAAK,EAAE;IAAEuF,SAAS,EAAE,sBAAsB;IAAEC,KAAK,EAAE;MAAEC,WAAW,EAAEH,WAAW,CAACG,WAAW,IAAIH,WAAW,CAACI;IAAgB;EAAE,CAAC,CAAC,EAC3IJ,WAAW,CAACP,QAAQ,IAAK/E,aAAa,CAAC,KAAK,EAAE;IAAEuF,SAAS,EAAE;EAAgB,CAAC,EAAED,WAAW,CAACP,QAAQ,CAAE,EACpG/E,aAAa,CAAC,KAAK,EAAE;IAAEuF,SAAS,EAAE;EAAiB,CAAC,EAAED,WAAW,CAACK,KAAK,CAACC,KAAK,IAAI5F,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClI;AAEA,MAAM4F,iBAAiB,SAASpI,aAAa,CAAC;EAC1C0C,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC0F,WAAW,GAAGhI,OAAO,CAACgI,WAAW,CAAC;EAC3C;EACAxB,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAEsF,OAAO;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACF,WAAW,CAACrF,KAAK,CAACwF,gBAAgB,CAAC;IACzE,OAAQjG,aAAa,CAACjC,iBAAiB,EAAE;MAAEyD,SAAS,EAAE,CAAC,sBAAsB,CAAC;MAAE0E,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAEC,UAAU,EAAE1F,KAAK,CAAC0F,UAAU;MAAEC,UAAU,EAAE3F,KAAK,CAAC2F,UAAU;MAAEC,OAAO,EAAE5F,KAAK,CAAC4F,OAAO;MAAEN,OAAO,EAAEA,OAAO;MAAEO,UAAU,EAAEN,aAAa;MAAEO,cAAc,EAAE9F,KAAK,CAAC8F,cAAc;MAAEC,YAAY,EAAE/F,KAAK,CAAC+F,YAAY;MAAEC,aAAa,EAAEhG,KAAK,CAACgG,aAAa;MAAEC,cAAc,EAAEA,CAAA,KAAM;QAC3W,IAAIC,iBAAiB,GAAG,CAAClG,KAAK,CAACmG,SAAS,GAAGnG,KAAK,CAACmG,SAAS,CAACtD,iBAAiB,GAAG,IAAI,MAC9E7C,KAAK,CAACoG,WAAW,GAAGpG,KAAK,CAACoG,WAAW,CAACvD,iBAAiB,GAAG,IAAI,CAAC,IAChE,CAAC,CAAC;QACN,OAAQtD,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE8F,OAAO,CAACe,GAAG,CAAE/D,GAAG,IAAK;UACvD,IAAIgE,UAAU,GAAGhE,GAAG,CAACgB,UAAU,CAACiD,QAAQ,CAACD,UAAU;UACnD,OAAQ/G,aAAa,CAAC,KAAK,EAAE;YAAEuF,SAAS,EAAE,0BAA0B;YAAEvE,GAAG,EAAE+F,UAAU;YAAEvB,KAAK,EAAE;cACtFyB,UAAU,EAAEN,iBAAiB,CAACI,UAAU,CAAC,GAAG,QAAQ,GAAG;YAC3D;UAAE,CAAC,EAAElD,kBAAkB,CAACd,GAAG,CAAC,GAAI/C,aAAa,CAAC4E,kBAAkB,EAAEL,MAAM,CAACC,MAAM,CAAC;YAAEzB,GAAG,EAAEA,GAAG;YAAEmE,UAAU,EAAE,KAAK;YAAEC,UAAU,EAAEJ,UAAU,KAAKtG,KAAK,CAAC2G,cAAc;YAAE1C,sBAAsB,EAAE;UAAM,CAAC,EAAE1G,UAAU,CAAC+E,GAAG,EAAEtC,KAAK,CAAC0F,UAAU,CAAC,CAAC,CAAC,GAAKnG,aAAa,CAACqE,eAAe,EAAEE,MAAM,CAACC,MAAM,CAAC;YAAEzB,GAAG,EAAEA,GAAG;YAAEmE,UAAU,EAAE,KAAK;YAAE9B,UAAU,EAAE,KAAK;YAAEC,eAAe,EAAE,KAAK;YAAE8B,UAAU,EAAEJ,UAAU,KAAKtG,KAAK,CAAC2G,cAAc;YAAE1C,sBAAsB,EAAE;UAAM,CAAC,EAAE1G,UAAU,CAAC+E,GAAG,EAAEtC,KAAK,CAAC0F,UAAU,CAAC,CAAC,CAAE,CAAC;QAC/d,CAAC,CAAC,CAAC;MACP;IAAE,CAAC,CAAC;EACZ;AACJ;AACA,SAASL,WAAWA,CAACG,gBAAgB,EAAE;EACnC,IAAIF,OAAO,GAAG,EAAE;EAChB,IAAIC,aAAa,GAAG,EAAE;EACtB,KAAK,IAAIqB,SAAS,IAAIpB,gBAAgB,EAAE;IACpCF,OAAO,CAACjF,IAAI,CAACuG,SAAS,CAACtE,GAAG,CAAC;IAC3B,IAAI,CAACsE,SAAS,CAACC,SAAS,EAAE;MACtBtB,aAAa,CAAClF,IAAI,CAACuG,SAAS,CAACtE,GAAG,CAAC;IACrC;EACJ;EACA,OAAO;IAAEgD,OAAO;IAAEC;EAAc,CAAC;AACrC;AAEA,MAAMuB,uBAAuB,GAAG/J,eAAe,CAAC;EAAEgK,IAAI,EAAE;AAAS,CAAC,CAAC;AACnE,MAAMC,SAAS,SAASvK,aAAa,CAAC;EAClCiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACsH,SAAS,GAAG3H,SAAS,CAAC,CAAC;IAC5B,IAAI,CAAC4H,KAAK,GAAG;MACTC,WAAW,EAAE3J,cAAc,CAAC;IAChC,CAAC;IACD,IAAI,CAAC4J,YAAY,GAAIC,EAAE,IAAK;MACxB5J,MAAM,CAAC,IAAI,CAACwJ,SAAS,EAAEI,EAAE,CAAC;MAC1B5J,MAAM,CAAC,IAAI,CAACuC,KAAK,CAACU,KAAK,EAAE2G,EAAE,CAAC;IAChC,CAAC;EACL;EACAxD,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE5D,OAAO;MAAED,KAAK;MAAEkH,KAAK;MAAED;IAAU,CAAC,GAAG,IAAI;IAC/C,IAAI;MAAE7G,OAAO;MAAEkH;IAAQ,CAAC,GAAGrH,OAAO;IAClC,IAAI;MAAEsH,IAAI;MAAE9B;IAAY,CAAC,GAAGzF,KAAK;IACjC;IACA,MAAMwH,YAAY,GAAGxH,KAAK,CAACyH,aAAa,IACpCC,uBAAuB,CAACH,IAAI,EAAE9B,WAAW,CAACkC,YAAY,EAAEL,OAAO,CAAC;IACpE,OAAQ/H,aAAa,CAAC7B,gBAAgB,EAAE;MAAE6G,KAAK,EAAE,IAAI;MAAE7D,KAAK,EAAE,IAAI,CAAC0G,YAAY;MAAErG,SAAS,EAAE,CACpF,gBAAgB,EAChB,IAAIf,KAAK,CAAC4H,eAAe,IAAI,EAAE,CAAC,CACnC;MAAEpD,OAAO,EAAEV,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/D,KAAK,CAAC6H,cAAc,CAAC,EAAG7H,KAAK,CAACyH,aAAa,GAAG;QAAE,iBAAiB,EAAEP,KAAK,CAACC;MAAY,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE;QAAEW,IAAI,EAAE;MAAW,CAAC,CAAC;MAAErD,gBAAgB,EAAEsD,cAAc;MAAER,IAAI,EAAEA,IAAI;MAAE9B,WAAW,EAAEA,WAAW;MAAEC,UAAU,EAAE1F,KAAK,CAAC0F,UAAU;MAAE+B,aAAa,EAAEzH,KAAK,CAACyH,aAAa;MAAED,YAAY,EAAEA,YAAY;MAAEQ,gBAAgB,EAAEhI,KAAK,CAACgI;IAAiB,CAAC,EAAE,CAACC,YAAY,EAAEpD,WAAW,KAAMtF,aAAa,CAAC,KAAK,EAAE;MAAE2I,GAAG,EAAElI,KAAK,CAACmI,UAAU;MAAErD,SAAS,EAAE,+CAA+C;MAAEC,KAAK,EAAE;QAAEqD,SAAS,EAAEpI,KAAK,CAACoI;MAAU;IAAE,CAAC,EACnjBpI,KAAK,CAACqI,cAAc,IAAK9I,aAAa,CAAC5B,mBAAmB,EAAE;MAAE4G,KAAK,EAAE,GAAG;MAAExD,SAAS,EAAE,CAAC,wBAAwB,CAAC;MAAEyD,OAAO,EAAE5G,iBAAiB,CAACqC,OAAO,EAAEsH,IAAI,EAAE,MAAM,CAAC;MAAEA,IAAI,EAAEA,IAAI;MAAEe,aAAa,EAAExB;IAAwB,CAAC,CAAE,EAC1N,CAACjC,WAAW,CAAC0D,UAAU,KAClBvI,KAAK,CAACyH,aAAa,IAAI5J,uBAAuB,CAACuC,OAAO,CAAC,IAAIJ,KAAK,CAACwI,WAAW,CAAC,GAAIjJ,aAAa,CAAC,KAAK,EAAE;MAAEuF,SAAS,EAAE;IAAqB,CAAC,EAC1IvF,aAAa,CAAC0I,YAAY,EAAE;MAAE1D,KAAK,EAAE,GAAG;MAAExD,SAAS,EAAE,CAC7C,uBAAuB,EACvByG,YAAY,IAAI,wBAAwB,CAC3C;MAAEhD,OAAO,EAAEV,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnG,iBAAiB,CAACqC,OAAO,EAAEsH,IAAI,CAAC,CAAC,EAAE;QAAEkB,EAAE,EAAEvB,KAAK,CAACC;MAAY,CAAC;IAAE,CAAC,CAAC,CAAC,GAAInH,KAAK,CAACyH,aAAa;IAC5I;IACAlI,aAAa,CAAC,KAAK,EAAE;MAAEuF,SAAS,EAAE,oBAAoB;MAAEC,KAAK,EAAE;QAAEyB,UAAU,EAAE;MAAS;IAAE,CAAC,EACrFjH,aAAa,CAAC,GAAG,EAAE;MAAEuF,SAAS,EAAE;IAAwB,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAI4D,SAAS,EACtFnJ,aAAa,CAAC,KAAK,EAAE;MAAEuF,SAAS,EAAE,uBAAuB;MAAEoD,GAAG,EAAElI,KAAK,CAAC2I;IAAe,CAAC,EAClF3I,KAAK,CAAC4I,SAAS,EACfrJ,aAAa,CAAC,KAAK,EAAE;MAAEuF,SAAS,EAAE,uBAAuB;MAAEC,KAAK,EAAE;QAAE8D,SAAS,EAAE7I,KAAK,CAAC8I;MAAc;IAAE,CAAC,EAClGvJ,aAAa,CAAC6F,iBAAiB,EAAE;MAAEO,UAAU,EAAE4B,IAAI;MAAE/B,gBAAgB,EAAExF,KAAK,CAACwF,gBAAgB;MAAEI,OAAO,EAAE5F,KAAK,CAAC4F,OAAO;MAAEE,cAAc,EAAEmB,SAAS;MAAElB,YAAY,EAAE,CAAC/F,KAAK,CAACyH,aAAa;MAAEzB,aAAa,EAAEhG,KAAK,CAACgG,aAAa;MAAEP,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAEkB,cAAc,EAAE3G,KAAK,CAAC2G,cAAc;MAAER,SAAS,EAAEnG,KAAK,CAACmG,SAAS;MAAEC,WAAW,EAAEpG,KAAK,CAACoG,WAAW;MAAEV,UAAU,EAAE1F,KAAK,CAAC0F;IAAW,CAAC,CAAC,CAAC,CAAC,EACrYnG,aAAa,CAAC,KAAK,EAAE;MAAEuF,SAAS,EAAE;IAAoB,CAAC,EAAE9E,KAAK,CAAC+I,SAAS,CAAC,CAAE,CAAC;EACpF;AACJ;AACA,SAAShB,cAAcA,CAAC/H,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACgJ,aAAa,IAAIzJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC;AACzE;AACA,SAASkI,uBAAuBA,CAACH,IAAI,EAAEI,YAAY,EAAEL,OAAO,EAAE;EAC1D,MAAM;IAAE2B,KAAK,EAAEC,YAAY;IAAEC,GAAG,EAAEC;EAAW,CAAC,GAAGzB,YAAY;EAC7D,MAAM0B,cAAc,GAAGvL,KAAK,CAACsL,UAAU,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAME,gBAAgB,GAAGhC,OAAO,CAACiC,OAAO,CAACL,YAAY,CAAC;EACtD,MAAMM,iBAAiB,GAAGlC,OAAO,CAACmC,QAAQ,CAACP,YAAY,CAAC;EACxD,MAAMQ,eAAe,GAAGpC,OAAO,CAACiC,OAAO,CAACF,cAAc,CAAC;EACvD,MAAMM,gBAAgB,GAAGrC,OAAO,CAACmC,QAAQ,CAACJ,cAAc,CAAC;EACzD;EACA,OAAO,EAAEC,gBAAgB,KAAKI,eAAe,IAAIF,iBAAiB,KAAKG,gBAAgB,CAAC,IACpFC,OAAO;EACP;EACArC,IAAI,CAACsC,OAAO,CAAC,CAAC,KAAKX,YAAY,CAACW,OAAO,CAAC,CAAC;EACrC;EACCvC,OAAO,CAACwC,MAAM,CAACvC,IAAI,CAAC,KAAK,CAAC,IAAIA,IAAI,CAACsC,OAAO,CAAC,CAAC,GAAGT,UAAU,CAACS,OAAO,CAAC,CAAE,CAAC;AAClF;AAEA,SAASE,cAAcA,CAACzH,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACgB,UAAU,CAACiD,QAAQ,CAACD,UAAU,GAAG,GAAG,GAAGhE,GAAG,CAACI,QAAQ;AAClE;AACA,SAASsH,cAAcA,CAAC1H,GAAG,EAAE;EACzB,OAAOyH,cAAc,CAACzH,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,CAACmB,OAAO;AAClD;AACA,SAASwG,qBAAqBA,CAAC/H,IAAI;AAAE;AACrCgI,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAE;EAC7E,IAAIC,SAAS,GAAG,IAAIC,mBAAmB,CAAEC,QAAQ,IAAK;IAClD;IACA,IAAIC,MAAM,GAAGzI,IAAI,CAACwI,QAAQ,CAACE,KAAK,CAAC,CAACtH,UAAU,CAACiD,QAAQ,CAACD,UAAU,GAC5D,GAAG,GAAGoE,QAAQ,CAAC3I,IAAI,CAACkH,KAAK,GACzB,GAAG,IAAIyB,QAAQ,CAAC3I,IAAI,CAACoH,GAAG,GAAG,CAAC,CAAC;IACjC;IACA,OAAOkB,UAAU,CAACM,MAAM,CAAC,IAAI,CAAC;EAClC,CAAC,CAAC;EACFH,SAAS,CAACK,cAAc,GAAG,IAAI;EAC/BL,SAAS,CAACJ,WAAW,GAAGA,WAAW;EACnC,IAAIF,YAAY,KAAK,IAAI,IAAIC,eAAe,KAAK,IAAI,EAAE;IACnDK,SAAS,CAACM,QAAQ,GAAGR,gBAAgB;IACrCE,SAAS,CAACO,cAAc,GAAG,IAAI;EACnC,CAAC,MACI,IAAI,OAAOb,YAAY,KAAK,QAAQ,EAAE;IACvCM,SAAS,CAACQ,WAAW,GAAGd,YAAY;EACxC,CAAC,MACI,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;IAC1CK,SAAS,CAACQ,WAAW,GAAGb,eAAe;IACvCK,SAAS,CAACO,cAAc,GAAG,IAAI;EACnC;EACA;EACA,IAAIE,SAAS,GAAG,EAAE;EAClB,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAI7I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACiJ,MAAM,EAAE9I,CAAC,IAAI,CAAC,EAAE;IACrC,IAAIC,GAAG,GAAGJ,IAAI,CAACG,CAAC,CAAC;IACjB,IAAIsI,MAAM,GAAGX,cAAc,CAAC1H,GAAG,CAAC;IAChC,IAAI8I,WAAW,GAAGf,UAAU,CAACM,MAAM,CAAC;IACpC,IAAIS,WAAW,IAAI,IAAI,EAAE;MACrBH,SAAS,CAAC5K,IAAI,CAAC;QACXuK,KAAK,EAAEvI,CAAC;QACRN,IAAI,EAAE;UACFkH,KAAK,EAAE3G,GAAG,CAACI,QAAQ;UACnByG,GAAG,EAAE7G,GAAG,CAACmB,OAAO,GAAG;QACvB;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACDyH,iBAAiB,CAAC7K,IAAI,CAACiC,GAAG,CAAC;IAC/B;EACJ;EACA,IAAI+I,aAAa,GAAGb,SAAS,CAACc,OAAO,CAACL,SAAS,CAAC;EAChD,IAAIM,QAAQ,GAAGf,SAAS,CAACgB,OAAO,CAAC,CAAC;EAClC,IAAI;IAAEC,mBAAmB;IAAEC,kBAAkB;IAAEC;EAAgB,CAAC,GAAGC,UAAU,CAACL,QAAQ,EAAErJ,IAAI,EAAEqI,KAAK,CAAC;EACpG,IAAIsB,QAAQ,GAAG,EAAE;EACjB,IAAIC,cAAc,GAAG,EAAE;EACvB;EACA,KAAK,IAAIxJ,GAAG,IAAI4I,iBAAiB,EAAE;IAC/BQ,kBAAkB,CAACpJ,GAAG,CAACI,QAAQ,CAAC,CAACrC,IAAI,CAAC;MAClCiC,GAAG;MACHuE,SAAS,EAAE,KAAK;MAChBkF,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MACdnD,SAAS,EAAE;IACf,CAAC,CAAC;IACF,KAAK,IAAIoD,GAAG,GAAG3J,GAAG,CAACI,QAAQ,EAAEuJ,GAAG,IAAI3J,GAAG,CAACmB,OAAO,EAAEwI,GAAG,IAAI,CAAC,EAAE;MACvDR,mBAAmB,CAACQ,GAAG,CAAC,CAAC5L,IAAI,CAAC;QAC1BiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAE2J,GAAG,EAAEA,GAAG,GAAG,CAAC,EAAE1B,KAAK,CAAC;QACzC1D,SAAS,EAAE,KAAK;QAChBkF,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,CAAC;QACdnD,SAAS,EAAE;MACf,CAAC,CAAC;IACN;EACJ;EACA;EACA,KAAK,IAAIoD,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG1B,KAAK,CAACY,MAAM,EAAEc,GAAG,IAAI,CAAC,EAAE;IAC5CJ,QAAQ,CAACxL,IAAI,CAAC,CAAC,CAAC;EACpB;EACA,KAAK,IAAI8L,WAAW,IAAId,aAAa,EAAE;IACnC,IAAI/I,GAAG,GAAGJ,IAAI,CAACiK,WAAW,CAACvB,KAAK,CAAC;IACjC,IAAIwB,UAAU,GAAGD,WAAW,CAACpK,IAAI;IACjC2J,kBAAkB,CAACU,UAAU,CAACnD,KAAK,CAAC,CAAC5I,IAAI,CAAC;MACtCiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAE8J,UAAU,CAACnD,KAAK,EAAEmD,UAAU,CAACjD,GAAG,EAAEoB,KAAK,CAAC;MAC7D1D,SAAS,EAAE,KAAK;MAChBkF,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,CAAC;MACdnD,SAAS,EAAE;IACf,CAAC,CAAC;IACF,KAAK,IAAIoD,GAAG,GAAGG,UAAU,CAACnD,KAAK,EAAEgD,GAAG,GAAGG,UAAU,CAACjD,GAAG,EAAE8C,GAAG,IAAI,CAAC,EAAE;MAC7DJ,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClBR,mBAAmB,CAACQ,GAAG,CAAC,CAAC5L,IAAI,CAAC;QAC1BiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAE2J,GAAG,EAAEA,GAAG,GAAG,CAAC,EAAE1B,KAAK,CAAC;QACzC1D,SAAS,EAAE,KAAK;QAChBkF,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,CAAC;QACdnD,SAAS,EAAE;MACf,CAAC,CAAC;IACN;EACJ;EACA;EACA,KAAK,IAAIoD,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG1B,KAAK,CAACY,MAAM,EAAEc,GAAG,IAAI,CAAC,EAAE;IAC5CH,cAAc,CAACzL,IAAI,CAACsL,eAAe,CAACM,GAAG,CAAC,CAAC;EAC7C;EACA,OAAO;IAAER,mBAAmB;IAAEC,kBAAkB;IAAEG,QAAQ;IAAEC;EAAe,CAAC;AAChF;AACA;AACA,SAASF,UAAUA,CAACS,QAAQ,EAAEnK,IAAI,EAAEqI,KAAK,EAAE;EACvC,IAAI+B,cAAc,GAAGC,mBAAmB,CAACF,QAAQ,EAAE9B,KAAK,CAACY,MAAM,CAAC;EAChE,IAAIM,mBAAmB,GAAG,EAAE;EAC5B,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIC,eAAe,GAAG,EAAE;EACxB,KAAK,IAAIM,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG1B,KAAK,CAACY,MAAM,EAAEc,GAAG,IAAI,CAAC,EAAE;IAC5C,IAAIO,KAAK,GAAGF,cAAc,CAACL,GAAG,CAAC;IAC/B;IACA,IAAIzG,gBAAgB,GAAG,EAAE;IACzB,IAAIiH,aAAa,GAAG,CAAC;IACrB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,KAAK,IAAIC,IAAI,IAAIH,KAAK,EAAE;MACpB,IAAIlK,GAAG,GAAGJ,IAAI,CAACyK,IAAI,CAAC/B,KAAK,CAAC;MAC1BpF,gBAAgB,CAACnF,IAAI,CAAC;QAClBiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAE2J,GAAG,EAAEA,GAAG,GAAG,CAAC,EAAE1B,KAAK,CAAC;QACzC1D,SAAS,EAAE,IAAI;QACfkF,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAEW,IAAI,CAACC,UAAU;QAC5B/D,SAAS,EAAE8D,IAAI,CAACC,UAAU,GAAGH;MACjC,CAAC,CAAC;MACFA,aAAa,GAAGE,IAAI,CAACC,UAAU,GAAGD,IAAI,CAACE,SAAS;IACpD;IACA;IACA,IAAIC,eAAe,GAAG,EAAE;IACxBL,aAAa,GAAG,CAAC;IACjBC,gBAAgB,GAAG,CAAC;IACpB,KAAK,IAAIC,IAAI,IAAIH,KAAK,EAAE;MACpB,IAAIlK,GAAG,GAAGJ,IAAI,CAACyK,IAAI,CAAC/B,KAAK,CAAC;MAC1B,IAAImB,UAAU,GAAGY,IAAI,CAAC5K,IAAI,CAACoH,GAAG,GAAGwD,IAAI,CAAC5K,IAAI,CAACkH,KAAK,GAAG,CAAC,CAAC,CAAC;MACtD,IAAI8D,UAAU,GAAGJ,IAAI,CAAC5K,IAAI,CAACkH,KAAK,KAAKgD,GAAG;MACxCS,gBAAgB,IAAIC,IAAI,CAACC,UAAU,GAAGH,aAAa,CAAC,CAAC;MACrDA,aAAa,GAAGE,IAAI,CAACC,UAAU,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;MAClD,IAAId,UAAU,EAAE;QACZW,gBAAgB,IAAIC,IAAI,CAACE,SAAS;QAClC,IAAIE,UAAU,EAAE;UACZD,eAAe,CAACzM,IAAI,CAAC;YACjBiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAEqK,IAAI,CAAC5K,IAAI,CAACkH,KAAK,EAAE0D,IAAI,CAAC5K,IAAI,CAACoH,GAAG,EAAEoB,KAAK,CAAC;YAC3D1D,SAAS,EAAE,IAAI;YACfkF,UAAU,EAAE,IAAI;YAChBC,WAAW,EAAEW,IAAI,CAACC,UAAU;YAC5B/D,SAAS,EAAE;UACf,CAAC,CAAC;QACN;MACJ,CAAC,MACI,IAAIkE,UAAU,EAAE;QACjBD,eAAe,CAACzM,IAAI,CAAC;UACjBiC,GAAG,EAAE4J,UAAU,CAAC5J,GAAG,EAAEqK,IAAI,CAAC5K,IAAI,CAACkH,KAAK,EAAE0D,IAAI,CAAC5K,IAAI,CAACoH,GAAG,EAAEoB,KAAK,CAAC;UAC3D1D,SAAS,EAAE,IAAI;UACfkF,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAEW,IAAI,CAACC,UAAU;UAC5B/D,SAAS,EAAE6D,gBAAgB,CAAE;QACjC,CAAC,CAAC;QACFA,gBAAgB,GAAG,CAAC;MACxB;IACJ;IACAjB,mBAAmB,CAACpL,IAAI,CAACmF,gBAAgB,CAAC;IAC1CkG,kBAAkB,CAACrL,IAAI,CAACyM,eAAe,CAAC;IACxCnB,eAAe,CAACtL,IAAI,CAACqM,gBAAgB,CAAC;EAC1C;EACA,OAAO;IAAEjB,mBAAmB;IAAEC,kBAAkB;IAAEC;EAAgB,CAAC;AACvE;AACA,SAASY,mBAAmBA,CAACC,KAAK,EAAElL,MAAM,EAAE;EACxC,IAAIgL,cAAc,GAAG,EAAE;EACvB,KAAK,IAAIL,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG3K,MAAM,EAAE2K,GAAG,IAAI,CAAC,EAAE;IACtCK,cAAc,CAACjM,IAAI,CAAC,EAAE,CAAC;EAC3B;EACA,KAAK,IAAIsM,IAAI,IAAIH,KAAK,EAAE;IACpB,KAAK,IAAIP,GAAG,GAAGU,IAAI,CAAC5K,IAAI,CAACkH,KAAK,EAAEgD,GAAG,GAAGU,IAAI,CAAC5K,IAAI,CAACoH,GAAG,EAAE8C,GAAG,IAAI,CAAC,EAAE;MAC3DK,cAAc,CAACL,GAAG,CAAC,CAAC5L,IAAI,CAACsM,IAAI,CAAC;IAClC;EACJ;EACA,OAAOL,cAAc;AACzB;AACA,SAASJ,UAAUA,CAAC5J,GAAG,EAAE0K,SAAS,EAAEC,OAAO,EAAE1C,KAAK,EAAE;EAChD,IAAIjI,GAAG,CAACI,QAAQ,KAAKsK,SAAS,IAAI1K,GAAG,CAACmB,OAAO,KAAKwJ,OAAO,GAAG,CAAC,EAAE;IAC3D,OAAO3K,GAAG;EACd;EACA,IAAIgB,UAAU,GAAGhB,GAAG,CAACgB,UAAU;EAC/B,IAAI4J,SAAS,GAAG5J,UAAU,CAAC6J,KAAK;EAChC,IAAIC,WAAW,GAAGrP,eAAe,CAACmP,SAAS,EAAE;IACzCjE,KAAK,EAAEsB,KAAK,CAACyC,SAAS,CAAC,CAACzF,IAAI;IAC5B4B,GAAG,EAAEnL,OAAO,CAACuM,KAAK,CAAC0C,OAAO,GAAG,CAAC,CAAC,CAAC1F,IAAI,EAAE,CAAC;EAC3C,CAAC,CAAC;EACF,OAAOzD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzB,GAAG,CAAC,EAAE;IAAEI,QAAQ,EAAEsK,SAAS;IAAEvJ,OAAO,EAAEwJ,OAAO,GAAG,CAAC;IAAE3J,UAAU,EAAE;MAC9FC,GAAG,EAAED,UAAU,CAACC,GAAG;MACnBX,EAAE,EAAEkB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,UAAU,CAACV,EAAE,CAAC,EAAE;QAAEyK,gBAAgB,EAAE;MAAM,CAAC,CAAC;MAChF9G,QAAQ,EAAEjD,UAAU,CAACiD,QAAQ;MAC7B4G,KAAK,EAAEC;IACX,CAAC;IAAE1J,OAAO,EAAEpB,GAAG,CAACoB,OAAO,IAAI0J,WAAW,CAACnE,KAAK,CAACY,OAAO,CAAC,CAAC,KAAKqD,SAAS,CAACjE,KAAK,CAACY,OAAO,CAAC,CAAC;IAAElG,KAAK,EAAErB,GAAG,CAACqB,KAAK,IAAIyJ,WAAW,CAACjE,GAAG,CAACU,OAAO,CAAC,CAAC,KAAKqD,SAAS,CAAC/D,GAAG,CAACU,OAAO,CAAC;EAAE,CAAC,CAAC;AAC1K;AACA,MAAMY,mBAAmB,SAASxM,YAAY,CAAC;EAC3CyB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB;IACA,IAAI,CAACoL,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACuC,WAAW,GAAG,CAAC,CAAC;EACzB;EACAhC,OAAOA,CAACL,SAAS,EAAE;IACf,MAAMpF,UAAU,GAAG,KAAK,CAACyF,OAAO,CAACL,SAAS,CAAC;IAC3C,MAAM;MAAEsC;IAAe,CAAC,GAAG,IAAI;IAC/B,MAAMC,aAAa,GAAIC,KAAK,IAAK,CAAC,IAAI,CAACH,WAAW,CAACpP,aAAa,CAACuP,KAAK,CAAC,CAAC;IACxE;IACA,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,cAAc,CAACpC,MAAM,EAAEuC,KAAK,IAAI,CAAC,EAAE;MAC3DH,cAAc,CAACG,KAAK,CAAC,GAAGH,cAAc,CAACG,KAAK,CAAC,CAACC,MAAM,CAACH,aAAa,CAAC;IACvE;IACA,OAAO3H,UAAU;EACrB;EACA+H,sBAAsBA,CAACC,SAAS,EAAEJ,KAAK,EAAEpC,aAAa,EAAE;IACpD,MAAM;MAAEkC,cAAc;MAAED;IAAY,CAAC,GAAG,IAAI;IAC5C,MAAM;MAAEQ,aAAa;MAAEC,aAAa;MAAEC;IAAgB,CAAC,GAAGH,SAAS;IACnE;IACA,IAAI,IAAI,CAAC9C,cAAc,IAAI+C,aAAa,EAAE;MACtC,MAAMG,eAAe,GAAG/P,aAAa,CAAC4P,aAAa,CAAC;MACpD,IAAI,CAACR,WAAW,CAACW,eAAe,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACpD,cAAc,EAAE;UACrB;UACA,MAAMsB,WAAW,GAAGrI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+J,aAAa,CAAC,EAAE;YAAE/L,IAAI,EAAE5D,cAAc,CAAC2P,aAAa,CAAC/L,IAAI,EAAE0L,KAAK,CAAC1L,IAAI;UAAE,CAAC,CAAC;UAC7H;UACA;UACA,MAAMmM,aAAa,GAAGhQ,aAAa,CAACiO,WAAW,CAAC;UAChDmB,WAAW,CAACY,aAAa,CAAC,GAAG,IAAI;UACjCX,cAAc,CAACQ,aAAa,CAAC,CAACC,eAAe,CAAC,GAAG7B,WAAW;UAC5Dd,aAAa,CAAChL,IAAI,CAAC8L,WAAW,CAAC;UAC/B,IAAI,CAACgC,UAAU,CAACL,aAAa,EAAEL,KAAK,EAAEpC,aAAa,CAAC;QACxD,CAAC,MACI;UACDiC,WAAW,CAACW,eAAe,CAAC,GAAG,IAAI;UACnC5C,aAAa,CAAChL,IAAI,CAACyN,aAAa,CAAC;QACrC;MACJ;IACJ;IACA;IACA,KAAK,CAACF,sBAAsB,CAACC,SAAS,EAAEJ,KAAK,EAAEpC,aAAa,CAAC;EACjE;AACJ;AAEA,MAAM+C,QAAQ,SAAS3R,aAAa,CAAC;EACjCiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC0O,UAAU,GAAG,IAAIjQ,MAAM,CAAC,CAAC,CAAC,CAAC;IAChC,IAAI,CAACkQ,WAAW,GAAG,IAAIlQ,MAAM,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,CAACmQ,QAAQ,GAAG,IAAInQ,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACoQ,cAAc,GAAG,IAAIpQ,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC6I,SAAS,GAAG3H,SAAS,CAAC,CAAC;IAC5B,IAAI,CAAC4H,KAAK,GAAG;MACTuH,cAAc,EAAE,IAAI;MACpBnE,gBAAgB,EAAE,IAAI;MACtBD,UAAU,EAAE,CAAC;IACjB,CAAC;IACD,IAAI,CAACqE,YAAY,GAAIC,QAAQ,IAAK;MAC9B,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7B;IACJ,CAAC;EACL;EACA/K,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D,KAAK;MAAEkH,KAAK;MAAEjH;IAAQ,CAAC,GAAG,IAAI;IACpC,IAAI;MAAEG;IAAQ,CAAC,GAAGH,OAAO;IACzB,IAAIqB,MAAM,GAAGtB,KAAK,CAACuK,KAAK,CAACY,MAAM;IAC/B,IAAI0D,kBAAkB,GAAGrM,mBAAmB,CAACxC,KAAK,CAAC8O,gBAAgB,EAAExN,MAAM,CAAC;IAC5E,IAAIyN,gBAAgB,GAAGvM,mBAAmB,CAACxC,KAAK,CAACgP,WAAW,EAAE1N,MAAM,CAAC;IACrE,IAAI2N,kBAAkB,GAAGzM,mBAAmB,CAAC,IAAI,CAAC0M,gBAAgB,CAAC,CAAC,EAAE5N,MAAM,CAAC;IAC7E,IAAI6N,eAAe,GAAG3M,mBAAmB,CAAC,IAAI,CAAC4M,aAAa,CAAC,CAAC,EAAE9N,MAAM,CAAC;IACvE,IAAI;MAAEmK,mBAAmB;MAAEC,kBAAkB;MAAEG,QAAQ;MAAEC;IAAe,CAAC,GAAG7B,qBAAqB,CAAC5L,aAAa,CAAC2B,KAAK,CAACqP,WAAW,EAAEjP,OAAO,CAACkP,UAAU,CAAC,EAAEtP,KAAK,CAACkK,YAAY,EAAElK,KAAK,CAACmK,eAAe,EAAE/J,OAAO,CAACmP,gBAAgB,EAAErI,KAAK,CAACmD,UAAU,EAAEnD,KAAK,CAACoD,gBAAgB,EAAEtK,KAAK,CAACuK,KAAK,CAAC;IACnR,IAAIrE,iBAAiB;IAAG;IACtBlG,KAAK,CAACmG,SAAS,IAAInG,KAAK,CAACmG,SAAS,CAACtD,iBAAiB,IACjD7C,KAAK,CAACoG,WAAW,IAAIpG,KAAK,CAACoG,WAAW,CAACvD,iBAAkB,IAC1D,CAAC,CAAC;IACN,OAAQtD,aAAa,CAAC,IAAI,EAAE;MAAE2I,GAAG,EAAE,IAAI,CAACjB,SAAS;MAAEa,IAAI,EAAE;IAAM,CAAC,EAC5D9H,KAAK,CAACwP,WAAW,IAAIxP,KAAK,CAACwP,WAAW,CAAC,CAAC,EACxCxP,KAAK,CAACuK,KAAK,CAAClE,GAAG,CAAC,CAACoJ,IAAI,EAAExD,GAAG,KAAK;MAC3B,IAAIyD,aAAa,GAAG,IAAI,CAACC,YAAY,CAAC1D,GAAG,EAAEjM,KAAK,CAACkB,QAAQ,GAAGuK,mBAAmB,CAACQ,GAAG,CAAC,GAAGP,kBAAkB,CAACO,GAAG,CAAC,EAAEjM,KAAK,CAAC0F,UAAU,EAAEQ,iBAAiB,CAAC;MACpJ,IAAI0J,aAAa,GAAG,IAAI,CAACD,YAAY,CAAC1D,GAAG,EAAE4D,qBAAqB,CAACV,eAAe,CAAClD,GAAG,CAAC,EAAEP,kBAAkB,CAAC,EAAE1L,KAAK,CAAC0F,UAAU,EAAE,CAAC,CAAC,EAAEkE,OAAO,CAAC5J,KAAK,CAACmG,SAAS,CAAC,EAAEyD,OAAO,CAAC5J,KAAK,CAACoG,WAAW,CAAC,EAAE,KAAK,CAAC;MAC9L,OAAQ7G,aAAa,CAACyH,SAAS,EAAE;QAAEzG,GAAG,EAAEkP,IAAI,CAAClP,GAAG;QAAEG,KAAK,EAAE,IAAI,CAAC2N,UAAU,CAAC/O,SAAS,CAACmQ,IAAI,CAAClP,GAAG,CAAC;QAAE4H,UAAU,EAAE,IAAI,CAACmG,WAAW,CAAChP,SAAS,CAACmQ,IAAI,CAAClP,GAAG,CAAC,CAAC;QAA0EkF,WAAW,EAAEzF,KAAK,CAACyF,WAAW;QAAE8B,IAAI,EAAEkI,IAAI,CAAClI,IAAI;QAAEE,aAAa,EAAEzH,KAAK,CAAC8P,cAAc;QAAEzH,cAAc,EAAErI,KAAK,CAAC+P,eAAe,IAAI9D,GAAG,KAAK,CAAC;QAAEzD,WAAW,EAAExI,KAAK,CAAC+P,eAAe,CAAC;QAA4DrK,UAAU,EAAE1F,KAAK,CAAC0F,UAAU;QAAEiB,cAAc,EAAE3G,KAAK,CAAC2G,cAAc;QAAER,SAAS,EAAEnG,KAAK,CAACmG,SAAS;QAAEC,WAAW,EAAEpG,KAAK,CAACoG,WAAW;QAAE4B,gBAAgB,EAAEyH,IAAI,CAACzH,gBAAgB;QAAEH,cAAc,EAAE4H,IAAI,CAAC5H,cAAc;QAAED,eAAe,EAAE6H,IAAI,CAAC7H,eAAe;QAAE5B,aAAa,EAAEyJ,IAAI,CAACzJ,aAAa;QAAEJ,OAAO,EAAEiG,QAAQ,CAACI,GAAG,CAAC;QAAEnD,aAAa,EAAEgD,cAAc,CAACG,GAAG,CAAC;QAAEzG,gBAAgB,EAAEiG,mBAAmB,CAACQ,GAAG,CAAC;QAAEtD,cAAc,EAAE,IAAI,CAAC4F,QAAQ,CAACjP,SAAS,CAACmQ,IAAI,CAAClP,GAAG,CAAC;QAAEqI,SAAS;QAAI;QAC93BrJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EACxBD,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAEkQ,aAAa,CAAC,EAC5CnQ,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAEoQ,aAAa,CAAC,CAAE;QAAE7G,SAAS;QAAI;QACjExJ,aAAa,CAACC,QAAQ,EAAE,IAAI,EACxB,IAAI,CAACwQ,cAAc,CAACf,kBAAkB,CAAChD,GAAG,CAAC,EAAE,WAAW,CAAC,EACzD,IAAI,CAAC+D,cAAc,CAACnB,kBAAkB,CAAC5C,GAAG,CAAC,EAAE,cAAc,CAAC,EAC5D,IAAI,CAAC+D,cAAc,CAACjB,gBAAgB,CAAC9C,GAAG,CAAC,EAAE,UAAU,CAAC,CAAE;QAAE7D,SAAS,EAAEpI,KAAK,CAACiQ;MAAc,CAAC,CAAC;IACvG,CAAC,CAAC,CAAC;EACX;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtB,YAAY,CAAC,IAAI,CAAC;IACvB,IAAI,CAAC3O,OAAO,CAACkQ,gBAAgB,CAAC,IAAI,CAACzB,YAAY,CAAC;EACpD;EACA0B,kBAAkBA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACrC,IAAIC,YAAY,GAAG,IAAI,CAACvQ,KAAK;IAC7B,IAAI,CAAC4O,YAAY,CAAC,CAACtQ,YAAY,CAAC+R,SAAS,EAAEE,YAAY,CAAC,CAAC;EAC7D;EACAC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACvQ,OAAO,CAACwQ,mBAAmB,CAAC,IAAI,CAAC/B,YAAY,CAAC;EACvD;EACAQ,gBAAgBA,CAAA,EAAG;IACf,IAAI;MAAElP;IAAM,CAAC,GAAG,IAAI;IACpB,IAAIA,KAAK,CAACmG,SAAS,IAAInG,KAAK,CAACmG,SAAS,CAACjE,IAAI,CAACiJ,MAAM,EAAE;MAAE;MAClD,OAAOnL,KAAK,CAACmG,SAAS,CAACjE,IAAI;IAC/B;IACA,IAAIlC,KAAK,CAACoG,WAAW,IAAIpG,KAAK,CAACoG,WAAW,CAAClE,IAAI,CAACiJ,MAAM,EAAE;MAAE;MACtD,OAAOnL,KAAK,CAACoG,WAAW,CAAClE,IAAI;IACjC;IACA,OAAOlC,KAAK,CAAC0Q,iBAAiB;EAClC;EACAtB,aAAaA,CAAA,EAAG;IACZ,IAAI;MAAEpP;IAAM,CAAC,GAAG,IAAI;IACpB,IAAIA,KAAK,CAACoG,WAAW,IAAIpG,KAAK,CAACoG,WAAW,CAAClE,IAAI,CAACiJ,MAAM,EAAE;MAAE;MACtD,OAAOnL,KAAK,CAACoG,WAAW,CAAClE,IAAI;IACjC;IACA,OAAO,EAAE;EACb;EACAyN,YAAYA,CAAC1D,GAAG,EAAE0E,aAAa,EAAEjL,UAAU,EAAEQ,iBAAiB,EAAEO,UAAU,EAAE9B,UAAU,EAAEC,eAAe,EAAE;IACrG,IAAI;MAAE3E;IAAQ,CAAC,GAAG,IAAI;IACtB,IAAI;MAAE0G;IAAe,CAAC,GAAG,IAAI,CAAC3G,KAAK;IACnC,IAAI;MAAEyO;IAAe,CAAC,GAAG,IAAI,CAACvH,KAAK;IACnC,IAAIjD,sBAAsB,GAAG,IAAI,CAACjE,KAAK,CAACuK,KAAK,CAACY,MAAM,KAAK,CAAC,CAAC,CAAC;IAC5D,IAAIyF,QAAQ,GAAGnK,UAAU,IAAI9B,UAAU,IAAIC,eAAe;IAC1D,IAAIiM,KAAK,GAAG,EAAE;IACd,IAAIpC,cAAc,EAAE;MAChB,KAAK,IAAI7H,SAAS,IAAI+J,aAAa,EAAE;QACjC,IAAI;UAAErO;QAAI,CAAC,GAAGsE,SAAS;QACvB,IAAI;UAAEN;QAAW,CAAC,GAAGhE,GAAG,CAACgB,UAAU,CAACiD,QAAQ;QAC5C,IAAIM,SAAS,GAAGD,SAAS,CAACC,SAAS,IAAI,CAACX,iBAAiB,CAACI,UAAU,CAAC;QACrE,IAAIyF,UAAU,GAAGnF,SAAS,CAACmF,UAAU;QACrC,IAAI+E,IAAI,GAAG,EAAE;QACb,IAAIC,KAAK,GAAG,EAAE;QACd,IAAIhF,UAAU,EAAE;UACZ,IAAI9L,OAAO,CAAC+Q,KAAK,EAAE;YACfD,KAAK,GAAG,CAAC;YACTD,IAAI,GAAGrC,cAAc,CAACwC,KAAK,CAAC3O,GAAG,CAACmB,OAAO,CAAC,GAAGgL,cAAc,CAACwC,KAAK,CAAC3O,GAAG,CAACI,QAAQ,CAAC;UACjF,CAAC,MACI;YACDoO,IAAI,GAAG,CAAC;YACRC,KAAK,GAAGtC,cAAc,CAACyC,MAAM,CAAC5O,GAAG,CAACI,QAAQ,CAAC,GAAG+L,cAAc,CAACyC,MAAM,CAAC5O,GAAG,CAACmB,OAAO,CAAC;UACpF;QACJ;QACA;AAChB;AACA;AACA;QACgBoN,KAAK,CAACxQ,IAAI,CAACd,aAAa,CAAC,KAAK,EAAE;UAAEuF,SAAS,EAAE,0BAA0B,IAAIiH,UAAU,GAAG,+BAA+B,GAAG,EAAE,CAAC;UAAExL,GAAG,EAAEwJ,cAAc,CAACzH,GAAG,CAAC;UAAE4F,GAAG,EAAE0I,QAAQ,GAAG,IAAI,GAAG,IAAI,CAACpC,cAAc,CAAClP,SAAS,CAAC0K,cAAc,CAAC1H,GAAG,CAAC,CAAC;UAAEyC,KAAK,EAAE;YACnOyB,UAAU,EAAEK,SAAS,GAAG,EAAE,GAAG,QAAQ;YACrCgC,SAAS,EAAEkD,UAAU,GAAG,EAAE,GAAGnF,SAAS,CAACiC,SAAS;YAChDsI,GAAG,EAAEpF,UAAU,GAAGnF,SAAS,CAACoF,WAAW,GAAG,EAAE;YAC5C8E,IAAI;YACJC;UACJ;QAAE,CAAC,EAAE3N,kBAAkB,CAACd,GAAG,CAAC,GAAI/C,aAAa,CAAC4E,kBAAkB,EAAEL,MAAM,CAACC,MAAM,CAAC;UAAEzB,GAAG,EAAEA,GAAG;UAAEmE,UAAU,EAAEA,UAAU;UAAEC,UAAU,EAAEJ,UAAU,KAAKK,cAAc;UAAE1C,sBAAsB,EAAEA;QAAuB,CAAC,EAAE1G,UAAU,CAAC+E,GAAG,EAAEoD,UAAU,CAAC,CAAC,CAAC,GAAKnG,aAAa,CAACqE,eAAe,EAAEE,MAAM,CAACC,MAAM,CAAC;UAAEzB,GAAG,EAAEA,GAAG;UAAEmE,UAAU,EAAEA,UAAU;UAAE9B,UAAU,EAAEA,UAAU;UAAEC,eAAe,EAAEA,eAAe;UAAE8B,UAAU,EAAEJ,UAAU,KAAKK,cAAc;UAAE1C,sBAAsB,EAAEA;QAAuB,CAAC,EAAE1G,UAAU,CAAC+E,GAAG,EAAEoD,UAAU,CAAC,CAAC,CAAE,CAAC,CAAC;MACngB;IACJ;IACA,OAAOmL,KAAK;EAChB;EACAb,cAAcA,CAAC9N,IAAI,EAAEkP,QAAQ,EAAE;IAC3B,IAAI;MAAEJ;IAAM,CAAC,GAAG,IAAI,CAAC/Q,OAAO;IAC5B,IAAI;MAAEyF;IAAW,CAAC,GAAG,IAAI,CAAC1F,KAAK;IAC/B,IAAI;MAAEyO;IAAe,CAAC,GAAG,IAAI,CAACvH,KAAK;IACnC,IAAI2J,KAAK,GAAG,EAAE;IACd,IAAIpC,cAAc,EAAE;MAChB,KAAK,IAAInM,GAAG,IAAIJ,IAAI,EAAE;QAClB,IAAImP,YAAY,GAAGL,KAAK,GAAG;UACvBD,KAAK,EAAE,CAAC;UACRD,IAAI,EAAErC,cAAc,CAACwC,KAAK,CAAC3O,GAAG,CAACmB,OAAO,CAAC,GAAGgL,cAAc,CAACwC,KAAK,CAAC3O,GAAG,CAACI,QAAQ;QAC/E,CAAC,GAAG;UACAoO,IAAI,EAAE,CAAC;UACPC,KAAK,EAAEtC,cAAc,CAACyC,MAAM,CAAC5O,GAAG,CAACI,QAAQ,CAAC,GAAG+L,cAAc,CAACyC,MAAM,CAAC5O,GAAG,CAACmB,OAAO;QAClF,CAAC;QACDoN,KAAK,CAACxQ,IAAI,CAACd,aAAa,CAAC,KAAK,EAAE;UAAEgB,GAAG,EAAEhC,kBAAkB,CAAC+D,GAAG,CAACgB,UAAU,CAAC;UAAEwB,SAAS,EAAE,uBAAuB;UAAEC,KAAK,EAAEsM;QAAa,CAAC,EAAED,QAAQ,KAAK,UAAU,GACzJ7R,aAAa,CAACf,OAAO,EAAEsF,MAAM,CAACC,MAAM,CAAC;UAAEzB,GAAG,EAAEA;QAAI,CAAC,EAAE/E,UAAU,CAAC+E,GAAG,EAAEoD,UAAU,CAAC,CAAC,CAAC,GAChFjH,UAAU,CAAC2S,QAAQ,CAAC,CAAC,CAAC;MAC9B;IACJ;IACA,OAAO7R,aAAa,CAACC,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAGqR,KAAK,CAAC;EAChD;EACAjC,YAAYA,CAAC0C,sBAAsB,EAAE;IACjC,IAAI;MAAEtR,KAAK;MAAEkH,KAAK;MAAEoH;IAAY,CAAC,GAAG,IAAI;IACxC,IAAI,CAACtO,KAAK,CAACkB,QAAQ,IACflB,KAAK,CAACuR,WAAW,KAAK,IAAI,CAAC;IAAA,EAC7B;MACE,IAAID,sBAAsB,EAAE;QACxB,IAAIE,QAAQ,GAAGxR,KAAK,CAACuK,KAAK,CAAClE,GAAG,CAAEoJ,IAAI,IAAKnB,WAAW,CAACmD,UAAU,CAAChC,IAAI,CAAClP,GAAG,CAAC,CAAC;QAC1E,IAAIiR,QAAQ,CAACrG,MAAM,EAAE;UACjB,IAAIuG,QAAQ,GAAG,IAAI,CAACzK,SAAS,CAAC0K,OAAO;UACrC,IAAIC,gBAAgB,GAAG,IAAIlT,aAAa,CAACgT,QAAQ,EAAEF,QAAQ,EAAE,IAAI;UAAE;UACnE,KAAK,CAAC;UACN,IAAI,CAACtK,KAAK,CAACuH,cAAc,IAAI,CAACvH,KAAK,CAACuH,cAAc,CAACoD,SAAS,CAACD,gBAAgB,CAAC,EAAE;YAC5E,IAAI,CAACE,QAAQ,CAAC;cACVrD,cAAc,EAAE,IAAI/P,aAAa,CAACgT,QAAQ,EAAEF,QAAQ,EAAE,IAAI;cAAE;cAC5D,KAAK;YACT,CAAC,CAAC;UACN;QACJ;MACJ;MACA,MAAMO,aAAa,GAAG,IAAI,CAAC7K,KAAK,CAACmD,UAAU;MAC3C,MAAM2H,aAAa,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAC5C,MAAMC,oBAAoB,GAAGlS,KAAK,CAACkK,YAAY,KAAK,IAAI,IAAIlK,KAAK,CAACmK,eAAe,KAAK,IAAI;MAC1F,IAAI,CAACgI,YAAY,CAAC;QACd;QACA;QACA;QACA9H,UAAU,EAAEvG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgO,aAAa,CAAC,EAAEC,aAAa,CAAC;QAC1E1H,gBAAgB,EAAE4H,oBAAoB,GAAG,IAAI,CAACE,uBAAuB,CAAC,CAAC,GAAG;MAC9E,CAAC,CAAC;IACN;EACJ;EACAH,eAAeA,CAAA,EAAG;IACd,IAAII,QAAQ,GAAG,IAAI,CAAC7D,cAAc,CAACiD,UAAU;IAC7C,IAAIpH,UAAU,GAAG,CAAC,CAAC;IACnB;IACA,KAAK,IAAIM,MAAM,IAAI0H,QAAQ,EAAE;MACzB,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC1H,MAAM,CAAC,CAAC8H,qBAAqB,CAAC,CAAC,CAACH,MAAM,CAAC;MACxEjI,UAAU,CAACM,MAAM,CAAC,GAAG4H,IAAI,CAACG,GAAG,CAACrI,UAAU,CAACM,MAAM,CAAC,IAAI,CAAC,EAAE2H,MAAM,CAAC;IAClE;IACA,OAAOjI,UAAU;EACrB;EACA+H,uBAAuBA,CAAA,EAAG;IACtB,IAAIO,QAAQ,GAAG,IAAI,CAAC3S,KAAK,CAACuK,KAAK,CAAC,CAAC,CAAC,CAAChK,GAAG;IACtC,IAAIqS,MAAM,GAAG,IAAI,CAACvE,UAAU,CAACoD,UAAU,CAACkB,QAAQ,CAAC;IACjD,IAAIE,aAAa,GAAG,IAAI,CAACtE,QAAQ,CAACkD,UAAU,CAACkB,QAAQ,CAAC;IACtD,OAAOC,MAAM,CAACH,qBAAqB,CAAC,CAAC,CAACK,MAAM,GAAGD,aAAa,CAACJ,qBAAqB,CAAC,CAAC,CAACtB,GAAG;EAC5F;EACA4B,UAAUA,CAAA,EAAG;IACT,IAAIC,KAAK,GAAG,IAAI,CAAC3E,UAAU,CAACoD,UAAU;IACtC,OAAO,IAAI,CAACzR,KAAK,CAACuK,KAAK,CAAClE,GAAG,CAAEoJ,IAAI,IAAKuD,KAAK,CAACvD,IAAI,CAAClP,GAAG,CAAC,CAAC;EAC1D;AACJ;AACA6N,QAAQ,CAAC6E,gBAAgB,CAAC;EACtB5I,UAAU,EAAE/L;AAChB,CAAC,CAAC;AACF,SAASuR,qBAAqBA,CAACqD,UAAU,EAAEC,aAAa,EAAE;EACtD,IAAI,CAACD,UAAU,CAAC/H,MAAM,EAAE;IACpB,OAAO,EAAE;EACb;EACA,IAAIiI,gBAAgB,GAAGC,oBAAoB,CAACF,aAAa,CAAC,CAAC,CAAC;EAC5D,OAAOD,UAAU,CAAC7M,GAAG,CAAE/D,GAAG,KAAM;IAC5BA,GAAG;IACHuE,SAAS,EAAE,IAAI;IACfkF,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAEoH,gBAAgB,CAAC9Q,GAAG,CAACgB,UAAU,CAACiD,QAAQ,CAACD,UAAU,CAAC;IACjEuC,SAAS,EAAE;EACf,CAAC,CAAC,CAAC;AACP;AACA,SAASwK,oBAAoBA,CAACF,aAAa,EAAE;EACzC,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzB,KAAK,IAAIE,UAAU,IAAIH,aAAa,EAAE;IAClC,KAAK,IAAIvM,SAAS,IAAI0M,UAAU,EAAE;MAC9BF,gBAAgB,CAACxM,SAAS,CAACtE,GAAG,CAACgB,UAAU,CAACiD,QAAQ,CAACD,UAAU,CAAC,GAAGM,SAAS,CAACoF,WAAW;IAC1F;EACJ;EACA,OAAOoH,gBAAgB;AAC3B;AAEA,MAAMG,SAAS,SAAS9W,aAAa,CAAC;EAClCiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC6T,qBAAqB,GAAGnW,OAAO,CAAC4E,cAAc,CAAC;IACpD,IAAI,CAACwR,gBAAgB,GAAGpW,OAAO,CAAC4E,cAAc,CAAC;IAC/C,IAAI,CAACyR,gBAAgB,GAAGrW,OAAO,CAAC4E,cAAc,CAAC;IAC/C,IAAI,CAAC0R,sBAAsB,GAAGtW,OAAO,CAAC4E,cAAc,CAAC;IACrD,IAAI,CAAC2R,cAAc,GAAGvW,OAAO,CAACsF,qBAAqB,CAAC;IACpD,IAAI,CAACkR,gBAAgB,GAAGxW,OAAO,CAACsF,qBAAqB,CAAC;IACtD,IAAI,CAACmR,OAAO,GAAG,IAAI1V,MAAM,CAAC,CAAC;EAC/B;EACAyF,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D,KAAK;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAIkC,MAAM,GAAGnC,KAAK,CAACuK,KAAK,CAACY,MAAM;IAC/B,IAAI4I,qBAAqB,GAAG,IAAI,CAACP,qBAAqB,CAACxT,KAAK,CAAC8O,gBAAgB,EAAE3M,MAAM,CAAC;IACtF,IAAI6R,gBAAgB,GAAG,IAAI,CAACP,gBAAgB,CAACzT,KAAK,CAACgP,WAAW,EAAE7M,MAAM,CAAC;IACvE,IAAI8R,gBAAgB,GAAG,IAAI,CAACP,gBAAgB,CAAC1T,KAAK,CAACqP,WAAW,EAAElN,MAAM,CAAC;IACvE,IAAI+R,sBAAsB,GAAG,IAAI,CAACP,sBAAsB,CAAC3T,KAAK,CAAC0Q,iBAAiB,EAAEvO,MAAM,CAAC;IACzF,IAAIgS,cAAc,GAAG,IAAI,CAACP,cAAc,CAAC5T,KAAK,CAACmG,SAAS,EAAEhE,MAAM,CAAC;IACjE,IAAIiS,gBAAgB,GAAG,IAAI,CAACP,gBAAgB,CAAC7T,KAAK,CAACoG,WAAW,EAAEjE,MAAM,CAAC;IACvE;IACA;IACA,IAAI8N,aAAa,GAAI9N,MAAM,IAAI,CAAC,IAAInC,KAAK,CAACuR,WAAW,GACjDvR,KAAK,CAACuR,WAAW,GAAGtR,OAAO,CAACG,OAAO,CAACiU,WAAW,GAAG,CAAC,GACnD,IAAI;IACR,OAAQ9U,aAAa,CAACZ,QAAQ,EAAE;MAAE2V,IAAI,EAAE;IAAM,CAAC,EAAE,CAACC,OAAO,EAAE7O,UAAU,KAAMnG,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAEQ,KAAK,CAACuK,KAAK,CAAClE,GAAG,CAAC,CAACkE,KAAK,EAAEhI,GAAG,KAAMhD,aAAa,CAAC6O,QAAQ,EAAE;MAAElG,GAAG,EAAE,IAAI,CAAC4L,OAAO,CAACxU,SAAS,CAACiD,GAAG,CAAC;MAAEhC,GAAG,EAAEgK,KAAK,CAACY,MAAM,GAC7MZ,KAAK,CAAC,CAAC,CAAC,CAAChD,IAAI,CAACiN,WAAW,CAAC,CAAC,CAAC,yDAC5BjS,GAAG,CAAC;MAAA;MACRuN,cAAc,EAAE3N,MAAM,GAAG,CAAC;MAAE4N,eAAe,EAAE/P,KAAK,CAAC+P,eAAe;MAAErK,UAAU,EAAEA,UAAU;MAAED,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAE8E,KAAK,EAAEA,KAAK;MAAEiF,WAAW,EAAExP,KAAK,CAACyU,cAAc;MAAE3F,gBAAgB,EAAEiF,qBAAqB,CAACxR,GAAG,CAAC;MAAEoE,cAAc,EAAE3G,KAAK,CAAC2G,cAAc;MAAEqI,WAAW,EAAEgF,gBAAgB,CAACzR,GAAG,CAAC,CAACoL,MAAM,CAAC+G,WAAW,CAAC,CAAC;MAAYrF,WAAW,EAAE4E,gBAAgB,CAAC1R,GAAG,CAAC;MAAEmO,iBAAiB,EAAEwD,sBAAsB,CAAC3R,GAAG,CAAC;MAAE4D,SAAS,EAAEgO,cAAc,CAAC5R,GAAG,CAAC;MAAE6D,WAAW,EAAEgO,gBAAgB,CAAC7R,GAAG,CAAC;MAAE2H,YAAY,EAAElK,KAAK,CAACkK,YAAY;MAAEC,eAAe,EAAEnK,KAAK,CAACmK,eAAe;MAAEoH,WAAW,EAAEvR,KAAK,CAACuR,WAAW;MAAEoD,YAAY,EAAE3U,KAAK,CAAC2U,YAAY;MAAE1E,aAAa,EAAEA,aAAa;MAAE/O,QAAQ,EAAElB,KAAK,CAACkB;IAAS,CAAC,CAAE,CAAC,CAAE,CAAC;EAChrB;EACAgP,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC0E,4BAA4B,CAAC,CAAC;EACvC;EACAxE,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,CAACwE,4BAA4B,CAAC,CAAC;EACvC;EACAA,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACd;MACA;MACA,MAAMC,WAAW,GAAG,IAAI,CAAChB,OAAO,CAACrC,UAAU,CAAC,CAAC,CAAC,CAACsB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D,MAAM8B,MAAM,GAAGC,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;MAC3E,IAAIF,MAAM,EAAE;QACR,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAAC5U,OAAO,CAAC2U,4BAA4B,CAAC,IAAI,EAAE;UAC5CvN,EAAE,EAAEwN,MAAM;UACVG,iBAAiB,EAAE,IAAI,CAAChV,KAAK,CAACgV;QAClC,CAAC,CAAC;MACN;IACJ;EACJ;EACAxE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACqE,MAAM,EAAE;MACb,IAAI,CAAC5U,OAAO,CAACgV,8BAA8B,CAAC,IAAI,CAAC;MACjD,IAAI,CAACJ,MAAM,GAAG,IAAI;IACtB;EACJ;EACA;EACA;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,YAAY,GAAG,IAAIzW,aAAa,CAAC,IAAI,CAACmW,MAAM,EAAE,IAAI,CAACf,OAAO,CAACsB,OAAO,CAAC,CAAC,CAAC/O,GAAG,CAAEgP,MAAM,IAAKA,MAAM,CAACtC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAE;IACnH,KAAK,EAAE,IAAI,CAAC;IACZ,IAAI,CAACuC,YAAY,GAAG,IAAI5W,aAAa,CAAC,IAAI,CAACmW,MAAM,EAAE,IAAI,CAACf,OAAO,CAACrC,UAAU,CAAC,CAAC,CAAC,CAACsB,UAAU,CAAC,CAAC;IAAE;IAC5F,IAAI;IAAE;IACN,KAAK,CAAC;EACV;EACAwC,QAAQA,CAACC,YAAY,EAAEC,WAAW,EAAE;IAChC,IAAI;MAAEH,YAAY;MAAEH;IAAa,CAAC,GAAG,IAAI;IACzC,IAAIlJ,GAAG,GAAGqJ,YAAY,CAACI,WAAW,CAACF,YAAY,CAAC;IAChD,IAAIjT,GAAG,GAAG4S,YAAY,CAACQ,UAAU,CAACF,WAAW,CAAC;IAC9C,IAAIlT,GAAG,IAAI,IAAI,IAAI0J,GAAG,IAAI,IAAI,EAAE;MAC5B,IAAIwD,IAAI,GAAG,IAAI,CAACzP,KAAK,CAACuK,KAAK,CAAChI,GAAG,CAAC,CAAC0J,GAAG,CAAC;MACrC,OAAO;QACHxG,WAAW,EAAE,IAAI,CAACzF,KAAK,CAACyF,WAAW;QACnCmQ,QAAQ,EAAE9R,MAAM,CAACC,MAAM,CAAC;UAAEoJ,KAAK,EAAE,IAAI,CAAC0I,YAAY,CAACtT,GAAG,EAAE0J,GAAG,CAAC;UAAEzI,MAAM,EAAE;QAAK,CAAC,EAAEiM,IAAI,CAACzJ,aAAa,CAAC;QACjG8P,KAAK,EAAE,IAAI,CAACC,SAAS,CAACxT,GAAG,EAAE0J,GAAG,CAAC;QAC/BU,IAAI,EAAE;UACFmE,IAAI,EAAEwE,YAAY,CAACrE,KAAK,CAAChF,GAAG,CAAC;UAC7B8E,KAAK,EAAEuE,YAAY,CAACpE,MAAM,CAACjF,GAAG,CAAC;UAC/BkF,GAAG,EAAEgE,YAAY,CAACa,IAAI,CAACzT,GAAG,CAAC;UAC3BuQ,MAAM,EAAEqC,YAAY,CAACc,OAAO,CAAC1T,GAAG;QACpC,CAAC;QACD2T,KAAK,EAAE;MACX,CAAC;IACL;IACA,OAAO,IAAI;EACf;EACAH,SAASA,CAACxT,GAAG,EAAE0J,GAAG,EAAE;IAChB,OAAO,IAAI,CAAC6H,OAAO,CAACrC,UAAU,CAAClP,GAAG,CAAC,CAACwQ,UAAU,CAAC,CAAC,CAAC9G,GAAG,CAAC,CAAC,CAAC;EAC3D;EACA4J,YAAYA,CAACtT,GAAG,EAAE0J,GAAG,EAAE;IACnB,IAAIhD,KAAK,GAAG,IAAI,CAACjJ,KAAK,CAACuK,KAAK,CAAChI,GAAG,CAAC,CAAC0J,GAAG,CAAC,CAAC1E,IAAI;IAC3C,IAAI4B,GAAG,GAAGnL,OAAO,CAACiL,KAAK,EAAE,CAAC,CAAC;IAC3B,OAAO;MAAEA,KAAK;MAAEE;IAAI,CAAC;EACzB;AACJ;AACA,SAASuL,WAAWA,CAACpS,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACgB,UAAU,CAACC,GAAG,CAACC,MAAM;AACpC;AAEA,MAAM2S,KAAK,SAAS1Z,aAAa,CAAC;EAC9BiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACe,KAAK,GAAGpB,SAAS,CAAC,CAAC;IACxB,IAAI,CAAC8W,gBAAgB,GAAG,KAAK;EACjC;EACAvS,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAEmK,eAAe;MAAED,YAAY;MAAEmM;IAAW,CAAC,GAAGrW,KAAK;IACzD,IAAIsW,gBAAgB,GAAGpM,YAAY,KAAK,IAAI,IAAIC,eAAe,KAAK,IAAI;IACxE;IACA;IACA,IAAImM,gBAAgB,IAAI,CAACD,UAAU,EAAE;MACjCC,gBAAgB,GAAG,KAAK;MACxBnM,eAAe,GAAG,IAAI;MACtBD,YAAY,GAAG,IAAI;IACvB;IACA,IAAIqM,UAAU,GAAG,CACb,iBAAiB,EACjBD,gBAAgB,GAAG,0BAA0B,GAAG,4BAA4B,EAC5ED,UAAU,GAAG,EAAE,GAAG,yBAAyB,CAAE;IAAA,CAChD;IACD,OAAQ9W,aAAa,CAAC,KAAK,EAAE;MAAE2I,GAAG,EAAE,IAAI,CAACxH,KAAK;MAAEoE,SAAS,EAAEyR,UAAU,CAACC,IAAI,CAAC,GAAG,CAAC;MAAEzR,KAAK,EAAE;QAChF;QACA;QACA0R,KAAK,EAAEzW,KAAK,CAACuR,WAAW;QACxBvP,QAAQ,EAAEhC,KAAK,CAAC0W;MACpB;IAAE,CAAC,EACHnX,aAAa,CAAC,OAAO,EAAE;MAAEuI,IAAI,EAAE,cAAc;MAAEhD,SAAS,EAAE,0BAA0B;MAAEC,KAAK,EAAE;QACrF0R,KAAK,EAAEzW,KAAK,CAACuR,WAAW;QACxBvP,QAAQ,EAAEhC,KAAK,CAAC0W,aAAa;QAC7BpE,MAAM,EAAE+D,UAAU,GAAGrW,KAAK,CAAC2U,YAAY,GAAG;MAC9C;IAAE,CAAC,EACH3U,KAAK,CAAC2W,YAAY,EAClBpX,aAAa,CAAC,OAAO,EAAE;MAAEuI,IAAI,EAAE;IAAe,CAAC,EAC3CvI,aAAa,CAACgU,SAAS,EAAE;MAAE9N,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAE8E,KAAK,EAAEvK,KAAK,CAACuK,KAAK;MAAEkK,cAAc,EAAEzU,KAAK,CAACyU,cAAc;MAAE1E,eAAe,EAAE/P,KAAK,CAAC+P,eAAe;MAAEwB,WAAW,EAAEvR,KAAK,CAACuR,WAAW;MAAEoD,YAAY,EAAE3U,KAAK,CAAC2U,YAAY;MAAE7F,gBAAgB,EAAE9O,KAAK,CAAC8O,gBAAgB;MAAEE,WAAW,EAAEhP,KAAK,CAACgP,WAAW;MAAEK,WAAW,EAAErP,KAAK,CAACqP,WAAW;MAAEqB,iBAAiB,EAAE1Q,KAAK,CAAC0Q,iBAAiB;MAAE/J,cAAc,EAAE3G,KAAK,CAAC2G,cAAc;MAAER,SAAS,EAAEnG,KAAK,CAACmG,SAAS;MAAEC,WAAW,EAAEpG,KAAK,CAACoG,WAAW;MAAE8D,YAAY,EAAEA,YAAY;MAAEC,eAAe,EAAEA,eAAe;MAAEjJ,QAAQ,EAAElB,KAAK,CAACkB,QAAQ;MAAE8T,iBAAiB,EAAEhV,KAAK,CAACgV;IAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/mB;EACA9E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC0G,kBAAkB,CAAC,CAAC;EAC7B;EACAxG,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,IAAIA,SAAS,CAAC5K,WAAW,KAAK,IAAI,CAACzF,KAAK,CAACyF,WAAW,EAAE;MAClD,IAAI,CAACmR,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACR,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACS,gBAAgB,CAAC,CAAC;EAC3B;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACT,gBAAgB,IACrB,IAAI,CAACpW,KAAK,CAACuR,WAAW,CAAC;IAAA,EACzB;MACE,MAAMuF,SAAS,GAAGC,kBAAkB,CAAC,IAAI,CAACrW,KAAK,CAACiR,OAAO,EAAE,IAAI,CAAC3R,KAAK,CAACyF,WAAW,CAAC;MAChF,IAAIqR,SAAS,EAAE;QACX,MAAMpF,QAAQ,GAAGoF,SAAS,CAAC/B,OAAO,CAAC,kBAAkB,CAAC;QACtD,MAAMiC,QAAQ,GAAGtF,QAAQ,CAACqD,OAAO,CAAC,cAAc,CAAC;QACjD,MAAMkC,SAAS,GAAGH,SAAS,CAACrE,qBAAqB,CAAC,CAAC,CAACtB,GAAG,GACnDO,QAAQ,CAACe,qBAAqB,CAAC,CAAC,CAACtB,GAAG;QACxC6F,QAAQ,CAACC,SAAS,GAAGA,SAAS,GAAIA,SAAS,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC;MAC1D;MACA,IAAI,CAACb,gBAAgB,GAAG,KAAK;IACjC;EACJ;AACJ;AACA,SAASW,kBAAkBA,CAACG,WAAW,EAAEzR,WAAW,EAAE;EAClD,IAAI4B,EAAE;EACN,IAAI5B,WAAW,CAAC0R,gBAAgB,CAACC,KAAK,CAAC,YAAY,CAAC,EAAE;IAClD/P,EAAE,GAAG6P,WAAW,CAACG,aAAa,CAAC,eAAezY,iBAAiB,CAAC6G,WAAW,CAAC6R,WAAW,CAAC,OAAO,CAAC;IAChG;EACJ;EACA,IAAI,CAACjQ,EAAE,EAAE;IACLA,EAAE,GAAG6P,WAAW,CAACG,aAAa,CAAC,eAAexY,eAAe,CAAC4G,WAAW,CAAC6R,WAAW,CAAC,IAAI,CAAC;IAC3F;EACJ;EACA,OAAOjQ,EAAE;AACb;AAEA,MAAMkQ,cAAc,SAASzY,MAAM,CAAC;EAChCY,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC6X,kBAAkB,GAAG,IAAI;EAClC;EACAC,UAAUA,CAACC,SAAS,EAAEC,aAAa,EAAE;IACjC,OAAOA,aAAa,CAACF,UAAU,CAACC,SAAS,CAAC;EAC9C;AACJ;AAEA,MAAME,QAAQ,SAASnb,aAAa,CAAC;EACjCiD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACkY,MAAM,GAAG,IAAIN,cAAc,CAAC,CAAC;IAClC,IAAI,CAACO,QAAQ,GAAGxY,SAAS,CAAC,CAAC;EAC/B;EACAuE,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE7D,KAAK;MAAEC;IAAQ,CAAC,GAAG,IAAI;IAC7B,OAAQV,aAAa,CAAC4W,KAAK,EAAErS,MAAM,CAACC,MAAM,CAAC;MAAEmE,GAAG,EAAE,IAAI,CAAC4P;IAAS,CAAC,EAAE,IAAI,CAACD,MAAM,CAACE,UAAU,CAAC/X,KAAK,EAAEA,KAAK,CAACyF,WAAW,EAAEzF,KAAK,CAACgY,gBAAgB,EAAE/X,OAAO,EAAED,KAAK,CAAC2X,aAAa,CAAC,EAAE;MAAElS,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAE8E,KAAK,EAAEvK,KAAK,CAAC2X,aAAa,CAACpN,KAAK;MAAEoM,YAAY,EAAE3W,KAAK,CAAC2W,YAAY;MAAED,aAAa,EAAE1W,KAAK,CAAC0W,aAAa;MAAEjC,cAAc,EAAEzU,KAAK,CAACyU,cAAc;MAAEvK,YAAY,EAAElK,KAAK,CAACkK,YAAY;MAAEC,eAAe,EAAEnK,KAAK,CAACmK,eAAe;MAAE4F,eAAe,EAAE/P,KAAK,CAAC+P,eAAe;MAAEsG,UAAU,EAAErW,KAAK,CAACqW,UAAU;MAAE4B,gBAAgB,EAAEjY,KAAK,CAACiY,gBAAgB;MAAE1G,WAAW,EAAEvR,KAAK,CAACuR,WAAW;MAAEoD,YAAY,EAAE3U,KAAK,CAAC2U,YAAY;MAAEzT,QAAQ,EAAElB,KAAK,CAACkB;IAAS,CAAC,CAAC,CAAC;EACvnB;AACJ;AAEA,MAAMgX,YAAY,SAASzY,SAAS,CAAC;EACjCC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACwY,kBAAkB,GAAG9a,OAAO,CAAC8a,kBAAkB,CAAC;IACrD,IAAI,CAACC,SAAS,GAAG9Y,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACwY,QAAQ,GAAGxY,SAAS,CAAC,CAAC;IAC3B;EACJ;EACAuE,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEzD,OAAO;MAAEiY;IAAqB,CAAC,GAAG,IAAI,CAACpY,OAAO;IACpD,IAAI;MAAED;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI2X,aAAa,GAAG,IAAI,CAACQ,kBAAkB,CAACnY,KAAK,CAACyF,WAAW,EAAE4S,oBAAoB,CAAC;IACpF,IAAIC,aAAa,GAAGlY,OAAO,CAACmY,UAAU,IAAKhZ,aAAa,CAACR,SAAS,EAAE;MAAEmJ,GAAG,EAAE,IAAI,CAACkQ,SAAS;MAAE3S,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAE+S,KAAK,EAAEb,aAAa,CAACc,WAAW;MAAEC,oBAAoB,EAAEf,aAAa,CAACxV,MAAM,KAAK;IAAE,CAAC,CAAE;IACjN,IAAIpC,WAAW,GAAI4Y,UAAU,IAAMpZ,aAAa,CAACqY,QAAQ,EAAE;MAAE1P,GAAG,EAAE,IAAI,CAAC4P,QAAQ;MAAErS,WAAW,EAAEzF,KAAK,CAACyF,WAAW;MAAEkS,aAAa,EAAEA,aAAa;MAAEiB,aAAa,EAAE5Y,KAAK,CAAC4Y,aAAa;MAAEC,aAAa,EAAE7Y,KAAK,CAAC6Y,aAAa;MAAEC,UAAU,EAAE9Y,KAAK,CAAC8Y,UAAU;MAAEC,YAAY,EAAE/Y,KAAK,CAAC+Y,YAAY;MAAEpS,cAAc,EAAE3G,KAAK,CAAC2G,cAAc;MAAER,SAAS,EAAEnG,KAAK,CAACmG,SAAS;MAAEC,WAAW,EAAEpG,KAAK,CAACoG,WAAW;MAAE4R,gBAAgB,EAAE5X,OAAO,CAAC4X,gBAAgB;MAAErB,YAAY,EAAEgC,UAAU,CAACK,iBAAiB;MAAEtC,aAAa,EAAEiC,UAAU,CAACjC,aAAa;MAAExM,YAAY,EAAE9J,OAAO,CAAC8J,YAAY;MAAEC,eAAe,EAAE/J,OAAO,CAAC+J,eAAe;MAAE4F,eAAe,EAAE3P,OAAO,CAAC6Y,WAAW;MAAE5C,UAAU,EAAE,CAACrW,KAAK,CAACiB,YAAY;MAAEgX,gBAAgB,EAAE,IAAI,CAACrY,WAAW;MAAE2R,WAAW,EAAEoH,UAAU,CAACpH,WAAW;MAAEoD,YAAY,EAAEgE,UAAU,CAAChE,YAAY;MAAEzT,QAAQ,EAAElB,KAAK,CAACkB;IAAS,CAAC,CAAE;IAC3xB,OAAOd,OAAO,CAACmB,WAAW,GACpB,IAAI,CAACF,mBAAmB,CAACiX,aAAa,EAAEvY,WAAW,EAAE4X,aAAa,CAACrW,MAAM,EAAElB,OAAO,CAACmB,WAAW,CAAC,GAC/F,IAAI,CAAC1B,kBAAkB,CAACyY,aAAa,EAAEvY,WAAW,CAAC;EAC7D;AACJ;AACA,SAASoY,kBAAkBA,CAAC1S,WAAW,EAAE4S,oBAAoB,EAAE;EAC3D,IAAIa,SAAS,GAAG,IAAIla,cAAc,CAACyG,WAAW,CAAC0T,WAAW,EAAEd,oBAAoB,CAAC;EACjF,OAAO,IAAIpZ,aAAa,CAACia,SAAS,EAAE,iBAAiB,CAACE,IAAI,CAAC3T,WAAW,CAAC0R,gBAAgB,CAAC,CAAC;AAC7F;AAEA,MAAMkC,yBAAyB,SAASna,oBAAoB,CAAC;EACzD;EACAoa,gBAAgBA,CAAC3R,YAAY,EAAEwP,gBAAgB,EAAEoC,aAAa,EAAE;IAC5D,IAAIJ,WAAW,GAAG,KAAK,CAACG,gBAAgB,CAAC3R,YAAY,EAAEwP,gBAAgB,EAAEoC,aAAa,CAAC;IACvF,IAAI;MAAEvZ;IAAM,CAAC,GAAG,IAAI;IACpB,OAAOwZ,wBAAwB,CAAC;MAC5B7R,YAAY,EAAEwR,WAAW;MACzBM,UAAU,EAAE,gBAAgB,CAACL,IAAI,CAACjC,gBAAgB,CAAC;MACnDuC,cAAc,EAAE1Z,KAAK,CAAC0Z,cAAc;MACpCpS,OAAO,EAAEtH,KAAK,CAACsH;IACnB,CAAC,CAAC;EACN;AACJ;AACA,SAASkS,wBAAwBA,CAACxZ,KAAK,EAAE;EACrC,IAAI;IAAEsH,OAAO;IAAEK;EAAa,CAAC,GAAG3H,KAAK;EACrC,IAAI;IAAEiJ,KAAK;IAAEE;EAAI,CAAC,GAAGxB,YAAY;EACjC,IAAIgS,SAAS;EACb;EACA,IAAI3Z,KAAK,CAACyZ,UAAU,EAAE;IAClBxQ,KAAK,GAAG3B,OAAO,CAACsS,WAAW,CAAC3Q,KAAK,CAAC;IAClC;IACA0Q,SAAS,GAAGrS,OAAO,CAACsS,WAAW,CAACzQ,GAAG,CAAC;IACpC,IAAIwQ,SAAS,CAAC9P,OAAO,CAAC,CAAC,KAAKV,GAAG,CAACU,OAAO,CAAC,CAAC,EAAE;MACvCV,GAAG,GAAGhK,QAAQ,CAACwa,SAAS,EAAE,CAAC,CAAC;IAChC;EACJ;EACA;EACA,IAAI3Z,KAAK,CAAC0Z,cAAc,EAAE;IACtB;IACA;IACA,IAAIG,oBAAoB,GAAGvS,OAAO,CAACsS,WAAW,CAACtS,OAAO,CAACwS,YAAY,CAAC9b,OAAO,CAAC2J,YAAY,CAACwB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnG,IAAIhH,MAAM,GAAGoQ,IAAI,CAACwH,IAAI;IAAC;IACvB3a,SAAS,CAACya,oBAAoB,EAAE1Q,GAAG,CAAC,CAAC;IACrCA,GAAG,GAAGhK,QAAQ,CAACgK,GAAG,EAAE,CAAC,GAAGhH,MAAM,CAAC;EACnC;EACA,OAAO;IAAE8G,KAAK;IAAEE;EAAI,CAAC;AACzB;AAEA,IAAI6Q,QAAQ,GAAG,s8GAAs8G;AACr9G3a,YAAY,CAAC2a,QAAQ,CAAC;AAEtB,SAAS9B,YAAY,IAAI+B,WAAW,EAAErC,QAAQ,EAAEL,cAAc,EAAEpB,KAAK,EAAEkD,yBAAyB,EAAE9F,SAAS,EAAE9T,SAAS,EAAE0Y,kBAAkB,EAAEqB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}