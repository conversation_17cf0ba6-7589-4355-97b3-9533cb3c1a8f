{"ast": null, "code": "import { BaseComponent, getUniqueDomId, getDateMeta, buildNavLinkAttrs, ContentContainer, getDayClassNames, formatDayString, createFormatter, EventContainer, getSegAnchorAttrs, isMultiDayRange, buildSegTimeText, DateComponent, memoize, ViewContainer, Scroller, NowTimer, sortEventSegs, getSegMeta, sliceEventStore, intersectRanges, startOfDay, addDays, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, Fragment } from '@fullcalendar/core/preact.js';\nclass ListViewHeaderRow extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.state = {\n      textId: getUniqueDomId()\n    };\n  }\n  render() {\n    let {\n      theme,\n      dateEnv,\n      options,\n      viewApi\n    } = this.context;\n    let {\n      cellId,\n      dayDate,\n      todayRange\n    } = this.props;\n    let {\n      textId\n    } = this.state;\n    let dayMeta = getDateMeta(dayDate, todayRange);\n    // will ever be falsy?\n    let text = options.listDayFormat ? dateEnv.format(dayDate, options.listDayFormat) : '';\n    // will ever be falsy? also, BAD NAME \"alt\"\n    let sideText = options.listDaySideFormat ? dateEnv.format(dayDate, options.listDaySideFormat) : '';\n    let renderProps = Object.assign({\n      date: dateEnv.toDate(dayDate),\n      view: viewApi,\n      textId,\n      text,\n      sideText,\n      navLinkAttrs: buildNavLinkAttrs(this.context, dayDate),\n      sideNavLinkAttrs: buildNavLinkAttrs(this.context, dayDate, 'day', false)\n    }, dayMeta);\n    // TODO: make a reusable HOC for dayHeader (used in daygrid/timegrid too)\n    return createElement(ContentContainer, {\n      elTag: \"tr\",\n      elClasses: ['fc-list-day', ...getDayClassNames(dayMeta, theme)],\n      elAttrs: {\n        'data-date': formatDayString(dayDate)\n      },\n      renderProps: renderProps,\n      generatorName: \"dayHeaderContent\",\n      customGenerator: options.dayHeaderContent,\n      defaultGenerator: renderInnerContent,\n      classNameGenerator: options.dayHeaderClassNames,\n      didMount: options.dayHeaderDidMount,\n      willUnmount: options.dayHeaderWillUnmount\n    }, InnerContent =>\n    // TODO: force-hide top border based on :first-child\n    createElement(\"th\", {\n      scope: \"colgroup\",\n      colSpan: 3,\n      id: cellId,\n      \"aria-labelledby\": textId\n    }, createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-list-day-cushion', theme.getClass('tableCellShaded')]\n    })));\n  }\n}\nfunction renderInnerContent(props) {\n  return createElement(Fragment, null, props.text && createElement(\"a\", Object.assign({\n    id: props.textId,\n    className: \"fc-list-day-text\"\n  }, props.navLinkAttrs), props.text), props.sideText && ( /* not keyboard tabbable */createElement(\"a\", Object.assign({\n    \"aria-hidden\": true,\n    className: \"fc-list-day-side-text\"\n  }, props.sideNavLinkAttrs), props.sideText)));\n}\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: 'short'\n});\nclass ListViewEventRow extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      seg,\n      timeHeaderId,\n      eventHeaderId,\n      dateHeaderId\n    } = props;\n    let timeFormat = options.eventTimeFormat || DEFAULT_TIME_FORMAT;\n    return createElement(EventContainer, Object.assign({}, props, {\n      elTag: \"tr\",\n      elClasses: ['fc-list-event', seg.eventRange.def.url && 'fc-event-forced-url'],\n      defaultGenerator: () => renderEventInnerContent(seg, context) /* weird */,\n      seg: seg,\n      timeText: \"\",\n      disableDragging: true,\n      disableResizing: true\n    }), (InnerContent, eventContentArg) => createElement(Fragment, null, buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId), createElement(\"td\", {\n      \"aria-hidden\": true,\n      className: \"fc-list-event-graphic\"\n    }, createElement(\"span\", {\n      className: \"fc-list-event-dot\",\n      style: {\n        borderColor: eventContentArg.borderColor || eventContentArg.backgroundColor\n      }\n    })), createElement(InnerContent, {\n      elTag: \"td\",\n      elClasses: ['fc-list-event-title'],\n      elAttrs: {\n        headers: `${eventHeaderId} ${dateHeaderId}`\n      }\n    })));\n  }\n}\nfunction renderEventInnerContent(seg, context) {\n  let interactiveAttrs = getSegAnchorAttrs(seg, context);\n  return createElement(\"a\", Object.assign({}, interactiveAttrs), seg.eventRange.def.title);\n}\nfunction buildTimeContent(seg, timeFormat, context, timeHeaderId, dateHeaderId) {\n  let {\n    options\n  } = context;\n  if (options.displayEventTime !== false) {\n    let eventDef = seg.eventRange.def;\n    let eventInstance = seg.eventRange.instance;\n    let doAllDay = false;\n    let timeText;\n    if (eventDef.allDay) {\n      doAllDay = true;\n    } else if (isMultiDayRange(seg.eventRange.range)) {\n      // TODO: use (!isStart || !isEnd) instead?\n      if (seg.isStart) {\n        timeText = buildSegTimeText(seg, timeFormat, context, null, null, eventInstance.range.start, seg.end);\n      } else if (seg.isEnd) {\n        timeText = buildSegTimeText(seg, timeFormat, context, null, null, seg.start, eventInstance.range.end);\n      } else {\n        doAllDay = true;\n      }\n    } else {\n      timeText = buildSegTimeText(seg, timeFormat, context);\n    }\n    if (doAllDay) {\n      let renderProps = {\n        text: context.options.allDayText,\n        view: context.viewApi\n      };\n      return createElement(ContentContainer, {\n        elTag: \"td\",\n        elClasses: ['fc-list-event-time'],\n        elAttrs: {\n          headers: `${timeHeaderId} ${dateHeaderId}`\n        },\n        renderProps: renderProps,\n        generatorName: \"allDayContent\",\n        customGenerator: options.allDayContent,\n        defaultGenerator: renderAllDayInner,\n        classNameGenerator: options.allDayClassNames,\n        didMount: options.allDayDidMount,\n        willUnmount: options.allDayWillUnmount\n      });\n    }\n    return createElement(\"td\", {\n      className: \"fc-list-event-time\"\n    }, timeText);\n  }\n  return null;\n}\nfunction renderAllDayInner(renderProps) {\n  return renderProps.text;\n}\n\n/*\nResponsible for the scroller, and forwarding event-related actions into the \"grid\".\n*/\nclass ListView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.computeDateVars = memoize(computeDateVars);\n    this.eventStoreToSegs = memoize(this._eventStoreToSegs);\n    this.state = {\n      timeHeaderId: getUniqueDomId(),\n      eventHeaderId: getUniqueDomId(),\n      dateHeaderIdRoot: getUniqueDomId()\n    };\n    this.setRootEl = rootEl => {\n      if (rootEl) {\n        this.context.registerInteractiveComponent(this, {\n          el: rootEl\n        });\n      } else {\n        this.context.unregisterInteractiveComponent(this);\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      dayDates,\n      dayRanges\n    } = this.computeDateVars(props.dateProfile);\n    let eventSegs = this.eventStoreToSegs(props.eventStore, props.eventUiBases, dayRanges);\n    return createElement(ViewContainer, {\n      elRef: this.setRootEl,\n      elClasses: ['fc-list', context.theme.getClass('table'), context.options.stickyHeaderDates !== false ? 'fc-list-sticky' : ''],\n      viewSpec: context.viewSpec\n    }, createElement(Scroller, {\n      liquid: !props.isHeightAuto,\n      overflowX: props.isHeightAuto ? 'visible' : 'hidden',\n      overflowY: props.isHeightAuto ? 'visible' : 'auto'\n    }, eventSegs.length > 0 ? this.renderSegList(eventSegs, dayDates) : this.renderEmptyMessage()));\n  }\n  renderEmptyMessage() {\n    let {\n      options,\n      viewApi\n    } = this.context;\n    let renderProps = {\n      text: options.noEventsText,\n      view: viewApi\n    };\n    return createElement(ContentContainer, {\n      elTag: \"div\",\n      elClasses: ['fc-list-empty'],\n      renderProps: renderProps,\n      generatorName: \"noEventsContent\",\n      customGenerator: options.noEventsContent,\n      defaultGenerator: renderNoEventsInner,\n      classNameGenerator: options.noEventsClassNames,\n      didMount: options.noEventsDidMount,\n      willUnmount: options.noEventsWillUnmount\n    }, InnerContent => createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-list-empty-cushion']\n    }));\n  }\n  renderSegList(allSegs, dayDates) {\n    let {\n      theme,\n      options\n    } = this.context;\n    let {\n      timeHeaderId,\n      eventHeaderId,\n      dateHeaderIdRoot\n    } = this.state;\n    let segsByDay = groupSegsByDay(allSegs); // sparse array\n    return createElement(NowTimer, {\n      unit: \"day\"\n    }, (nowDate, todayRange) => {\n      let innerNodes = [];\n      for (let dayIndex = 0; dayIndex < segsByDay.length; dayIndex += 1) {\n        let daySegs = segsByDay[dayIndex];\n        if (daySegs) {\n          // sparse array, so might be undefined\n          let dayStr = formatDayString(dayDates[dayIndex]);\n          let dateHeaderId = dateHeaderIdRoot + '-' + dayStr;\n          // append a day header\n          innerNodes.push(createElement(ListViewHeaderRow, {\n            key: dayStr,\n            cellId: dateHeaderId,\n            dayDate: dayDates[dayIndex],\n            todayRange: todayRange\n          }));\n          daySegs = sortEventSegs(daySegs, options.eventOrder);\n          for (let seg of daySegs) {\n            innerNodes.push(createElement(ListViewEventRow, Object.assign({\n              key: dayStr + ':' + seg.eventRange.instance.instanceId /* are multiple segs for an instanceId */,\n              seg: seg,\n              isDragging: false,\n              isResizing: false,\n              isDateSelecting: false,\n              isSelected: false,\n              timeHeaderId: timeHeaderId,\n              eventHeaderId: eventHeaderId,\n              dateHeaderId: dateHeaderId\n            }, getSegMeta(seg, todayRange, nowDate))));\n          }\n        }\n      }\n      return createElement(\"table\", {\n        className: 'fc-list-table ' + theme.getClass('table')\n      }, createElement(\"thead\", null, createElement(\"tr\", null, createElement(\"th\", {\n        scope: \"col\",\n        id: timeHeaderId\n      }, options.timeHint), createElement(\"th\", {\n        scope: \"col\",\n        \"aria-hidden\": true\n      }), createElement(\"th\", {\n        scope: \"col\",\n        id: eventHeaderId\n      }, options.eventHint))), createElement(\"tbody\", null, innerNodes));\n    });\n  }\n  _eventStoreToSegs(eventStore, eventUiBases, dayRanges) {\n    return this.eventRangesToSegs(sliceEventStore(eventStore, eventUiBases, this.props.dateProfile.activeRange, this.context.options.nextDayThreshold).fg, dayRanges);\n  }\n  eventRangesToSegs(eventRanges, dayRanges) {\n    let segs = [];\n    for (let eventRange of eventRanges) {\n      segs.push(...this.eventRangeToSegs(eventRange, dayRanges));\n    }\n    return segs;\n  }\n  eventRangeToSegs(eventRange, dayRanges) {\n    let {\n      dateEnv\n    } = this.context;\n    let {\n      nextDayThreshold\n    } = this.context.options;\n    let range = eventRange.range;\n    let allDay = eventRange.def.allDay;\n    let dayIndex;\n    let segRange;\n    let seg;\n    let segs = [];\n    for (dayIndex = 0; dayIndex < dayRanges.length; dayIndex += 1) {\n      segRange = intersectRanges(range, dayRanges[dayIndex]);\n      if (segRange) {\n        seg = {\n          component: this,\n          eventRange,\n          start: segRange.start,\n          end: segRange.end,\n          isStart: eventRange.isStart && segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: eventRange.isEnd && segRange.end.valueOf() === range.end.valueOf(),\n          dayIndex\n        };\n        segs.push(seg);\n        // detect when range won't go fully into the next day,\n        // and mutate the latest seg to the be the end.\n        if (!seg.isEnd && !allDay && dayIndex + 1 < dayRanges.length && range.end < dateEnv.add(dayRanges[dayIndex + 1].start, nextDayThreshold)) {\n          seg.end = range.end;\n          seg.isEnd = true;\n          break;\n        }\n      }\n    }\n    return segs;\n  }\n}\nfunction renderNoEventsInner(renderProps) {\n  return renderProps.text;\n}\nfunction computeDateVars(dateProfile) {\n  let dayStart = startOfDay(dateProfile.renderRange.start);\n  let viewEnd = dateProfile.renderRange.end;\n  let dayDates = [];\n  let dayRanges = [];\n  while (dayStart < viewEnd) {\n    dayDates.push(dayStart);\n    dayRanges.push({\n      start: dayStart,\n      end: addDays(dayStart, 1)\n    });\n    dayStart = addDays(dayStart, 1);\n  }\n  return {\n    dayDates,\n    dayRanges\n  };\n}\n// Returns a sparse array of arrays, segs grouped by their dayIndex\nfunction groupSegsByDay(segs) {\n  let segsByDay = []; // sparse array\n  let i;\n  let seg;\n  for (i = 0; i < segs.length; i += 1) {\n    seg = segs[i];\n    (segsByDay[seg.dayIndex] || (segsByDay[seg.dayIndex] = [])).push(seg);\n  }\n  return segsByDay;\n}\nvar css_248z = \":root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:\\\"\\\";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}\";\ninjectStyles(css_248z);\nexport { ListView };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}