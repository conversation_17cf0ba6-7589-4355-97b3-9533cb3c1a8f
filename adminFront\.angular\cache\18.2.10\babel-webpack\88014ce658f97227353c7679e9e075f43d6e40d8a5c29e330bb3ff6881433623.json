{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n  const hasConfig = typeof config === 'object';\n  return new Promise((resolve, reject) => {\n    let _hasValue = false;\n    let _value;\n    source.subscribe({\n      next: value => {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: () => {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "lastValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "_hasValue", "_value", "subscribe", "next", "value", "error", "complete", "defaultValue"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/rxjs/dist/esm/internal/lastValueFrom.js"], "sourcesContent": ["import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n    const hasConfig = typeof config === 'object';\n    return new Promise((resolve, reject) => {\n        let _hasValue = false;\n        let _value;\n        source.subscribe({\n            next: (value) => {\n                _value = value;\n                _hasValue = true;\n            },\n            error: reject,\n            complete: () => {\n                if (_hasValue) {\n                    resolve(_value);\n                }\n                else if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC1C,MAAMC,SAAS,GAAG,OAAOD,MAAM,KAAK,QAAQ;EAC5C,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACpC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,MAAM;IACVP,MAAM,CAACQ,SAAS,CAAC;MACbC,IAAI,EAAGC,KAAK,IAAK;QACbH,MAAM,GAAGG,KAAK;QACdJ,SAAS,GAAG,IAAI;MACpB,CAAC;MACDK,KAAK,EAAEN,MAAM;MACbO,QAAQ,EAAEA,CAAA,KAAM;QACZ,IAAIN,SAAS,EAAE;UACXF,OAAO,CAACG,MAAM,CAAC;QACnB,CAAC,MACI,IAAIL,SAAS,EAAE;UAChBE,OAAO,CAACH,MAAM,CAACY,YAAY,CAAC;QAChC,CAAC,MACI;UACDR,MAAM,CAAC,IAAIP,UAAU,CAAC,CAAC,CAAC;QAC5B;MACJ;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}