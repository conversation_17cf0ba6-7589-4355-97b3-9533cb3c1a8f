{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport * as i1 from '@nebular/theme';\nimport { NbSvgIcon } from '@nebular/theme';\nimport { icons } from 'eva-icons';\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbEvaSvgIcon extends NbSvgIcon {\n  constructor(name, content) {\n    super(name, '');\n    this.name = name;\n    this.content = content;\n  }\n  getContent(options) {\n    return this.content.toSvg({\n      width: '100%',\n      height: '100%',\n      fill: 'currentColor',\n      ...options\n    });\n  }\n}\nclass NbEvaIconsModule {\n  constructor(iconLibrary) {\n    this.NAME = 'eva';\n    iconLibrary.registerSvgPack(this.NAME, this.createIcons());\n    iconLibrary.setDefaultPack(this.NAME);\n  }\n  createIcons() {\n    return Object.entries(icons).map(([name, icon]) => {\n      return [name, new NbEvaSvgIcon(name, icon)];\n    }).reduce((newIcons, [name, icon]) => {\n      newIcons[name] = icon;\n      return newIcons;\n    }, {});\n  }\n  static {\n    this.ɵfac = function NbEvaIconsModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NbEvaIconsModule)(i0.ɵɵinject(i1.NbIconLibraries));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NbEvaIconsModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NbEvaIconsModule, [{\n    type: NgModule,\n    args: [{}]\n  }], () => [{\n    type: i1.NbIconLibraries\n  }], null);\n})();\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NbEvaIconsModule, NbEvaSvgIcon };", "map": {"version": 3, "names": ["i0", "NgModule", "i1", "NbSvgIcon", "icons", "NbEvaSvgIcon", "constructor", "name", "content", "get<PERSON>ontent", "options", "toSvg", "width", "height", "fill", "NbEvaIconsModule", "iconLibrary", "NAME", "registerSvgPack", "createIcons", "setDefaultPack", "Object", "entries", "map", "icon", "reduce", "newIcons", "ɵfac", "NbEvaIconsModule_Factory", "__ngFactoryType__", "ɵɵinject", "NbIconLibraries", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/@nebular/eva-icons/fesm2022/nebular-eva-icons.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport * as i1 from '@nebular/theme';\nimport { NbSvgIcon } from '@nebular/theme';\nimport { icons } from 'eva-icons';\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\nclass NbEvaSvgIcon extends NbSvgIcon {\n    constructor(name, content) {\n        super(name, '');\n        this.name = name;\n        this.content = content;\n    }\n    getContent(options) {\n        return this.content.toSvg({\n            width: '100%',\n            height: '100%',\n            fill: 'currentColor',\n            ...options,\n        });\n    }\n}\nclass NbEvaIconsModule {\n    constructor(iconLibrary) {\n        this.NAME = 'eva';\n        iconLibrary.registerSvgPack(this.NAME, this.createIcons());\n        iconLibrary.setDefaultPack(this.NAME);\n    }\n    createIcons() {\n        return Object\n            .entries(icons)\n            .map(([name, icon]) => {\n            return [name, new NbEvaSvgIcon(name, icon)];\n        })\n            .reduce((newIcons, [name, icon]) => {\n            newIcons[name] = icon;\n            return newIcons;\n        }, {});\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbEvaIconsModule, deps: [{ token: i1.NbIconLibraries }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.1.3\", ngImport: i0, type: NbEvaIconsModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbEvaIconsModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.1.3\", ngImport: i0, type: NbEvaIconsModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }], ctorParameters: () => [{ type: i1.NbIconLibraries }] });\n\n/*\n * @license\n * Copyright Akveo. All Rights Reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NbEvaIconsModule, NbEvaSvgIcon };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,QAAQ,eAAe;AACxC,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,KAAK,QAAQ,WAAW;;AAEjC;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAASF,SAAS,CAAC;EACjCG,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;IACvB,KAAK,CAACD,IAAI,EAAE,EAAE,CAAC;IACf,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAC,UAAUA,CAACC,OAAO,EAAE;IAChB,OAAO,IAAI,CAACF,OAAO,CAACG,KAAK,CAAC;MACtBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,cAAc;MACpB,GAAGJ;IACP,CAAC,CAAC;EACN;AACJ;AACA,MAAMK,gBAAgB,CAAC;EACnBT,WAAWA,CAACU,WAAW,EAAE;IACrB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjBD,WAAW,CAACE,eAAe,CAAC,IAAI,CAACD,IAAI,EAAE,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC;IAC1DH,WAAW,CAACI,cAAc,CAAC,IAAI,CAACH,IAAI,CAAC;EACzC;EACAE,WAAWA,CAAA,EAAG;IACV,OAAOE,MAAM,CACRC,OAAO,CAAClB,KAAK,CAAC,CACdmB,GAAG,CAAC,CAAC,CAAChB,IAAI,EAAEiB,IAAI,CAAC,KAAK;MACvB,OAAO,CAACjB,IAAI,EAAE,IAAIF,YAAY,CAACE,IAAI,EAAEiB,IAAI,CAAC,CAAC;IAC/C,CAAC,CAAC,CACGC,MAAM,CAAC,CAACC,QAAQ,EAAE,CAACnB,IAAI,EAAEiB,IAAI,CAAC,KAAK;MACpCE,QAAQ,CAACnB,IAAI,CAAC,GAAGiB,IAAI;MACrB,OAAOE,QAAQ;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAwFd,gBAAgB,EAA1Bf,EAAE,CAAA8B,QAAA,CAA0C5B,EAAE,CAAC6B,eAAe;IAAA,CAA2C;EAAE;EAC3M;IAAS,IAAI,CAACC,IAAI,kBAD8EhC,EAAE,CAAAiC,gBAAA;MAAAC,IAAA,EACSnB;IAAgB,EAAG;EAAE;EAChI;IAAS,IAAI,CAACoB,IAAI,kBAF8EnC,EAAE,CAAAoC,gBAAA,IAE4B;EAAE;AACpI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAJoGrC,EAAE,CAAAsC,iBAAA,CAIXvB,gBAAgB,EAAc,CAAC;IAC9GmB,IAAI,EAAEjC,QAAQ;IACdsC,IAAI,EAAE,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEL,IAAI,EAAEhC,EAAE,CAAC6B;EAAgB,CAAC,CAAC;AAAA;;AAEhE;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAShB,gBAAgB,EAAEV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}