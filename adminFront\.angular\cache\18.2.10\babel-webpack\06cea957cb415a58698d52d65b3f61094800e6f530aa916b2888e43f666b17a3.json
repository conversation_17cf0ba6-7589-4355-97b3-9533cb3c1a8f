{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"./household-binding.component\";\nimport * as i5 from \"./test-dropdown.component\";\nimport * as i6 from \"./simple-dropdown-test.component\";\nfunction HouseholdBindingDemoComponent_div_13_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_div_13_span_10_Template_button_click_2_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFromSelection2(householdCode_r4));\n    });\n    i0.ɵɵelement(3, \"nb-icon\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r4, \" \");\n  }\n}\nfunction HouseholdBindingDemoComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"nb-icon\", 20);\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_div_13_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearSelection2());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 23)(9, \"div\", 24);\n    i0.ɵɵtemplate(10, HouseholdBindingDemoComponent_div_13_span_10_Template, 4, 1, \"span\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.selectedHouseholds2.length, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedHouseholds2);\n  }\n}\nfunction HouseholdBindingDemoComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u9078\\u64C7\\u7D50\\u679C (selectedHouseholds1)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r1.selectedHouseholds1));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u57FA\\u672C\\u4F7F\\u7528)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r1.selectedItems));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n    i0.ɵɵtext(2, \"\\u8A73\\u7D30\\u9078\\u64C7\\u9805\\u76EE (\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r1.selectedItems2));\n  }\n}\nfunction HouseholdBindingDemoComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"small\", 31);\n    i0.ɵɵtext(2, \"\\u8CC7\\u6599\\u72C0\\u614B\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"pre\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"json\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(5, 1, ctx_r1.debugData));\n  }\n}\nexport class HouseholdBindingDemoComponent {\n  constructor() {\n    this.selectedHouseholds1 = [];\n    this.selectedHouseholds2 = [];\n    this.selectedHouseholds3 = [];\n    this.selectedHouseholds4 = [];\n    this.selectedItems = [];\n    this.selectedItems2 = [];\n    this.debugData = null;\n    this.customBuildingData = {\n      '總統套房': [{\n        code: 'P001',\n        building: '總統套房',\n        floor: '50F',\n        isSelected: false,\n        isDisabled: false\n      }, {\n        code: 'P002',\n        building: '總統套房',\n        floor: '51F',\n        isSelected: false,\n        isDisabled: false\n      }],\n      '景觀樓層': Array.from({\n        length: 20\n      }, (_, i) => ({\n        code: `V${String(i + 1).padStart(3, '0')}`,\n        building: '景觀樓層',\n        floor: `${30 + Math.floor(i / 2)}F`,\n        isSelected: false,\n        isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\n      }))\n    };\n  }\n  ngOnInit() {\n    // 初始化一些預選的戶別\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\n  }\n  onSelectionChange(selectedItems) {\n    this.selectedItems = selectedItems;\n    console.log('Selection changed:', selectedItems);\n  }\n  onSelectionChange2(selectedItems) {\n    this.selectedItems2 = selectedItems;\n    console.log('Selection 2 changed:', selectedItems);\n  }\n  debugInfo() {\n    this.debugData = {\n      selectedHouseholds1: this.selectedHouseholds1,\n      selectedHouseholds2: this.selectedHouseholds2,\n      selectedHouseholds3: this.selectedHouseholds3,\n      selectedHouseholds4: this.selectedHouseholds4,\n      customBuildingData: this.customBuildingData,\n      selectedItems: this.selectedItems,\n      selectedItems2: this.selectedItems2\n    };\n    console.log('Debug info:', this.debugData);\n  }\n  testBuildingSelect() {\n    console.log('Testing building selection...');\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\n    // 主要是為了觸發除錯訊息\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\n  }\n  clearSelection2() {\n    this.selectedHouseholds2 = [];\n    this.selectedItems2 = [];\n  }\n  removeFromSelection2(householdCode) {\n    this.selectedHouseholds2 = this.selectedHouseholds2.filter(code => code !== householdCode);\n    this.selectedItems2 = this.selectedItems2.filter(item => item.code !== householdCode);\n  }\n  static {\n    this.ɵfac = function HouseholdBindingDemoComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingDemoComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingDemoComponent,\n      selectors: [[\"app-household-binding-demo\"]],\n      decls: 59,\n      vars: 19,\n      consts: [[1, \"demo-section\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"maxSelections\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"selectionChange\", \"ngModel\", \"showSelectedArea\", \"maxSelections\"], [\"class\", \"custom-selected-display\", 4, \"ngIf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"allowSearch\", \"allowBatchSelect\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u6236\\u5225\", 3, \"ngModelChange\", \"ngModel\", \"buildingData\"], [\"class\", \"demo-section\", 4, \"ngIf\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"1fr 1fr\", \"gap\", \"1rem\"], [2, \"padding\", \"1rem\", \"border\", \"1px solid #007bff\", \"border-radius\", \"0.375rem\", \"background-color\", \"#f8f9fa\"], [2, \"color\", \"#007bff\", \"margin-bottom\", \"0.5rem\"], [2, \"font-size\", \"0.875rem\", \"margin-bottom\", \"0.5rem\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"1rem\", \"border\", \"1px solid #28a745\", \"border-radius\", \"0.375rem\", \"background-color\", \"#f8f9fa\"], [2, \"color\", \"#28a745\", \"margin-bottom\", \"0.5rem\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"custom-selected-display\"], [1, \"custom-selected-header\"], [1, \"selected-info\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [1, \"selected-title\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"custom-selected-content\"], [1, \"selected-items-grid\"], [\"class\", \"selected-item-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"selected-item-chip\"], [\"type\", \"button\", 1, \"remove-chip-btn\", 3, \"click\"], [\"icon\", \"close-outline\"], [1, \"result-display\"], [1, \"mt-2\"], [1, \"text-muted\"]],\n      template: function HouseholdBindingDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-header\")(2, \"h4\");\n          i0.ɵɵtext(3, \"\\u6236\\u5225\\u7D81\\u5B9A\\u5143\\u4EF6\\u793A\\u4F8B\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"nb-card-body\")(5, \"div\", 0)(6, \"h5\");\n          i0.ɵɵtext(7, \"\\u57FA\\u672C\\u4F7F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"app-household-binding\", 1);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds1, $event) || (ctx.selectedHouseholds1 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener($event) {\n            return ctx.onSelectionChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 0)(10, \"h5\");\n          i0.ɵɵtext(11, \"\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"app-household-binding\", 2);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds2, $event) || (ctx.selectedHouseholds2 = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_12_listener($event) {\n            return ctx.onSelectionChange2($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, HouseholdBindingDemoComponent_div_13_Template, 11, 2, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 0)(15, \"h5\");\n          i0.ɵɵtext(16, \"\\u7981\\u7528\\u641C\\u5C0B\\u548C\\u6279\\u6B21\\u9078\\u64C7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"app-household-binding\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds3, $event) || (ctx.selectedHouseholds3 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 0)(19, \"h5\");\n          i0.ɵɵtext(20, \"\\u81EA\\u5B9A\\u7FA9\\u6236\\u5225\\u8CC7\\u6599\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"app-household-binding\", 5);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedHouseholds4, $event) || (ctx.selectedHouseholds4 = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(22, HouseholdBindingDemoComponent_div_22_Template, 6, 3, \"div\", 6)(23, HouseholdBindingDemoComponent_div_23_Template, 6, 3, \"div\", 6)(24, HouseholdBindingDemoComponent_div_24_Template, 6, 3, \"div\", 6);\n          i0.ɵɵelementStart(25, \"div\", 0)(26, \"h5\");\n          i0.ɵɵtext(27, \"\\u6BD4\\u8F03\\u5169\\u7A2E\\u986F\\u793A\\u6A21\\u5F0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 7)(29, \"div\", 8)(30, \"h6\", 9);\n          i0.ɵɵtext(31, \"\\u57FA\\u672C\\u4F7F\\u7528 (\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\", 10);\n          i0.ɵɵtext(33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 11);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 12)(37, \"h6\", 13);\n          i0.ɵɵtext(38, \"\\u4E0D\\u986F\\u793A\\u5DF2\\u9078\\u64C7\\u5340\\u57DF (\\u81EA\\u5B9A\\u7FA9\\u986F\\u793A)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"p\", 10);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 11);\n          i0.ɵɵtext(42);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(43, \"div\", 0)(44, \"h5\");\n          i0.ɵɵtext(45, \"\\u9664\\u932F\\u8CC7\\u8A0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_46_listener() {\n            return ctx.debugInfo();\n          });\n          i0.ɵɵtext(47, \" \\u6AA2\\u67E5\\u8CC7\\u6599 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingDemoComponent_Template_button_click_48_listener() {\n            return ctx.testBuildingSelect();\n          });\n          i0.ɵɵtext(49, \" \\u6E2C\\u8A66\\u9078\\u64C7 A\\u68DF \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, HouseholdBindingDemoComponent_div_50_Template, 6, 3, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 0)(52, \"h5\");\n          i0.ɵɵtext(53, \"\\u6E2C\\u8A66\\u7C21\\u5316\\u4E0B\\u62C9\\u9078\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"app-test-dropdown\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 0)(56, \"h5\");\n          i0.ɵɵtext(57, \"\\u6975\\u7C21\\u4E0B\\u62C9\\u9078\\u55AE\\u6E2C\\u8A66\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"app-simple-dropdown-test\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds1);\n          i0.ɵɵproperty(\"maxSelections\", 20);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds2);\n          i0.ɵɵproperty(\"showSelectedArea\", false)(\"maxSelections\", 10);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds2.length > 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds3);\n          i0.ɵɵproperty(\"allowSearch\", false)(\"allowBatchSelect\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedHouseholds4);\n          i0.ɵɵproperty(\"buildingData\", ctx.customBuildingData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedHouseholds1.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedItems2.length > 0);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx.selectedHouseholds1.length, \" \\u500B\\u6236\\u5225\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedHouseholds1.join(\", \") || \"\\u5C1A\\u672A\\u9078\\u64C7\", \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx.selectedHouseholds2.length, \" \\u500B\\u6236\\u5225\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedHouseholds2.join(\", \") || \"\\u5C1A\\u672A\\u9078\\u64C7\", \" \");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.debugData);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardHeaderComponent, i3.NbIconComponent, i4.HouseholdBindingComponent, i5.TestDropdownComponent, i6.SimpleDropdownTestComponent, i1.JsonPipe],\n      styles: [\".demo-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n  background-color: #f8f9fa;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #495057;\\n  font-weight: 600;\\n}\\n\\n.result-display[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  padding: 1rem;\\n  font-size: 0.875rem;\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n\\n.custom-selected-display[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  padding: 1rem;\\n  background-color: #fff;\\n  border: 1px solid #28a745;\\n  border-radius: 0.375rem;\\n}\\n\\n.custom-selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n\\n.selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.selected-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #28a745;\\n  font-size: 0.875rem;\\n}\\n\\n.custom-selected-content[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e9ecef;\\n  padding-top: 0.75rem;\\n}\\n\\n.selected-items-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n\\n.selected-item-chip[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #d4edda;\\n  color: #155724;\\n  border: 1px solid #c3e6cb;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #155724;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: background-color 0.15s ease;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #c3e6cb;\\n}\\n\\n.remove-chip-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "HouseholdBindingDemoComponent_div_13_span_10_Template_button_click_2_listener", "householdCode_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "removeFromSelection2", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "HouseholdBindingDemoComponent_div_13_Template_button_click_6_listener", "_r1", "clearSelection2", "ɵɵtemplate", "HouseholdBindingDemoComponent_div_13_span_10_Template", "selectedHouseholds2", "length", "ɵɵproperty", "ɵɵtextInterpolate", "ɵɵpipeBind1", "selectedHouseholds1", "selectedItems", "selectedItems2", "debugData", "HouseholdBindingDemoComponent", "constructor", "selectedHouseholds3", "selectedHouseholds4", "customBuildingData", "code", "building", "floor", "isSelected", "isDisabled", "Array", "from", "_", "i", "String", "padStart", "Math", "ngOnInit", "onSelectionChange", "console", "log", "onSelectionChange2", "debugInfo", "testBuildingSelect", "householdCode", "filter", "item", "selectors", "decls", "vars", "consts", "template", "HouseholdBindingDemoComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_8_listener", "$event", "ɵɵtwoWayBindingSet", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_8_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_12_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_selectionChange_12_listener", "HouseholdBindingDemoComponent_div_13_Template", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_17_listener", "HouseholdBindingDemoComponent_Template_app_household_binding_ngModelChange_21_listener", "HouseholdBindingDemoComponent_div_22_Template", "HouseholdBindingDemoComponent_div_23_Template", "HouseholdBindingDemoComponent_div_24_Template", "HouseholdBindingDemoComponent_Template_button_click_46_listener", "HouseholdBindingDemoComponent_Template_button_click_48_listener", "HouseholdBindingDemoComponent_div_50_Template", "ɵɵtwoWayProperty", "join"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding-demo.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { HouseholdItem, BuildingData } from './household-binding.component';\r\n\r\n@Component({\r\n  selector: 'app-household-binding-demo',\r\n  template: `\r\n    <nb-card>\r\n      <nb-card-header>\r\n        <h4>戶別綁定元件示例</h4>\r\n      </nb-card-header>\r\n      <nb-card-body>        <div class=\"demo-section\">\r\n          <h5>基本使用</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds1\"\r\n            placeholder=\"請選擇戶別\"\r\n            [maxSelections]=\"20\"\r\n            (selectionChange)=\"onSelectionChange($event)\">\r\n          </app-household-binding>\r\n        </div>        <div class=\"demo-section\">\r\n          <h5>不顯示已選擇區域</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds2\"\r\n            placeholder=\"請選擇戶別\"\r\n            [showSelectedArea]=\"false\"\r\n            [maxSelections]=\"10\"\r\n            (selectionChange)=\"onSelectionChange2($event)\">\r\n          </app-household-binding>\r\n          \r\n          <!-- 自定義已選擇項目顯示 -->\r\n          <div *ngIf=\"selectedHouseholds2.length > 0\" class=\"custom-selected-display\">\r\n            <div class=\"custom-selected-header\">\r\n              <div class=\"selected-info\">\r\n                <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n                <span class=\"selected-title\">已選擇戶別 ({{selectedHouseholds2.length}})</span>\r\n              </div>\r\n              <button type=\"button\" class=\"btn btn-outline-secondary btn-sm\" (click)=\"clearSelection2()\">\r\n                清空\r\n              </button>\r\n            </div>\r\n            <div class=\"custom-selected-content\">\r\n              <div class=\"selected-items-grid\">\r\n                <span *ngFor=\"let householdCode of selectedHouseholds2\" class=\"selected-item-chip\">\r\n                  {{householdCode}}\r\n                  <button type=\"button\" class=\"remove-chip-btn\" (click)=\"removeFromSelection2(householdCode)\">\r\n                    <nb-icon icon=\"close-outline\"></nb-icon>\r\n                  </button>\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>禁用搜尋和批次選擇</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds3\"\r\n            placeholder=\"請選擇戶別\"\r\n            [allowSearch]=\"false\"\r\n            [allowBatchSelect]=\"false\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>自定義戶別資料</h5>\r\n          <app-household-binding\r\n            [(ngModel)]=\"selectedHouseholds4\"\r\n            placeholder=\"請選擇戶別\"\r\n            [buildingData]=\"customBuildingData\">\r\n          </app-household-binding>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedHouseholds1.length > 0\">\r\n          <h5>選擇結果 (selectedHouseholds1)</h5>\r\n          <pre class=\"result-display\">{{ selectedHouseholds1 | json }}</pre>\r\n        </div>        <div class=\"demo-section\" *ngIf=\"selectedItems.length > 0\">\r\n          <h5>詳細選擇項目 (基本使用)</h5>\r\n          <pre class=\"result-display\">{{ selectedItems | json }}</pre>\r\n        </div>\r\n\r\n        <div class=\"demo-section\" *ngIf=\"selectedItems2.length > 0\">\r\n          <h5>詳細選擇項目 (不顯示已選擇區域)</h5>\r\n          <pre class=\"result-display\">{{ selectedItems2 | json }}</pre>\r\n        </div>        <div class=\"demo-section\">\r\n          <h5>比較兩種顯示模式</h5>\r\n          <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;\">\r\n            <div style=\"padding: 1rem; border: 1px solid #007bff; border-radius: 0.375rem; background-color: #f8f9fa;\">\r\n              <h6 style=\"color: #007bff; margin-bottom: 0.5rem;\">基本使用 (顯示已選擇區域)</h6>\r\n              <p style=\"font-size: 0.875rem; margin-bottom: 0.5rem;\">已選擇: {{selectedHouseholds1.length}} 個戶別</p>\r\n              <div style=\"font-size: 0.75rem; color: #6c757d;\">\r\n                {{selectedHouseholds1.join(', ') || '尚未選擇'}}\r\n              </div>\r\n            </div>\r\n            <div style=\"padding: 1rem; border: 1px solid #28a745; border-radius: 0.375rem; background-color: #f8f9fa;\">\r\n              <h6 style=\"color: #28a745; margin-bottom: 0.5rem;\">不顯示已選擇區域 (自定義顯示)</h6>\r\n              <p style=\"font-size: 0.875rem; margin-bottom: 0.5rem;\">已選擇: {{selectedHouseholds2.length}} 個戶別</p>\r\n              <div style=\"font-size: 0.75rem; color: #6c757d;\">\r\n                {{selectedHouseholds2.join(', ') || '尚未選擇'}}\r\n              </div>\r\n            </div>          </div>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>除錯資訊</h5>\r\n          <button type=\"button\" class=\"btn btn-secondary btn-sm me-2\" (click)=\"debugInfo()\">\r\n            檢查資料\r\n          </button>\r\n          <button type=\"button\" class=\"btn btn-info btn-sm me-2\" (click)=\"testBuildingSelect()\">\r\n            測試選擇 A棟\r\n          </button>\r\n          <div *ngIf=\"debugData\" class=\"mt-2\">\r\n            <small class=\"text-muted\">資料狀態：</small>\r\n            <pre class=\"result-display\">{{ debugData | json }}</pre>\r\n          </div>\r\n        </div><div class=\"demo-section\">\r\n          <h5>測試簡化下拉選單</h5>\r\n          <app-test-dropdown></app-test-dropdown>\r\n        </div>\r\n\r\n        <div class=\"demo-section\">\r\n          <h5>極簡下拉選單測試</h5>\r\n          <app-simple-dropdown-test></app-simple-dropdown-test>\r\n        </div>\r\n      </nb-card-body>\r\n    </nb-card>\r\n  `,  styles: [`\r\n    .demo-section {\r\n      margin-bottom: 2rem;\r\n      padding: 1rem;\r\n      border: 1px solid #e9ecef;\r\n      border-radius: 0.375rem;\r\n      background-color: #f8f9fa;\r\n    }\r\n\r\n    .demo-section h5 {\r\n      margin-bottom: 1rem;\r\n      color: #495057;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .result-display {\r\n      background-color: #fff;\r\n      border: 1px solid #ced4da;\r\n      border-radius: 0.25rem;\r\n      padding: 1rem;\r\n      font-size: 0.875rem;\r\n      max-height: 200px;\r\n      overflow-y: auto;\r\n    }\r\n\r\n    .custom-selected-display {\r\n      margin-top: 1rem;\r\n      padding: 1rem;\r\n      background-color: #fff;\r\n      border: 1px solid #28a745;\r\n      border-radius: 0.375rem;\r\n    }\r\n\r\n    .custom-selected-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 0.75rem;\r\n    }\r\n\r\n    .selected-info {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 0.5rem;\r\n    }\r\n\r\n    .selected-title {\r\n      font-weight: 500;\r\n      color: #28a745;\r\n      font-size: 0.875rem;\r\n    }\r\n\r\n    .custom-selected-content {\r\n      border-top: 1px solid #e9ecef;\r\n      padding-top: 0.75rem;\r\n    }\r\n\r\n    .selected-items-grid {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 0.5rem;\r\n    }\r\n\r\n    .selected-item-chip {\r\n      display: inline-flex;\r\n      align-items: center;\r\n      gap: 0.25rem;\r\n      padding: 0.375rem 0.75rem;\r\n      background-color: #d4edda;\r\n      color: #155724;\r\n      border: 1px solid #c3e6cb;\r\n      border-radius: 1rem;\r\n      font-size: 0.75rem;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .remove-chip-btn {\r\n      background: none;\r\n      border: none;\r\n      padding: 0;\r\n      margin: 0;\r\n      cursor: pointer;\r\n      color: #155724;\r\n      border-radius: 50%;\r\n      width: 1rem;\r\n      height: 1rem;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      transition: background-color 0.15s ease;\r\n    }\r\n\r\n    .remove-chip-btn:hover {\r\n      background-color: #c3e6cb;\r\n    }\r\n\r\n    .remove-chip-btn nb-icon {\r\n      font-size: 0.75rem;\r\n    }\r\n  `]\r\n})\r\nexport class HouseholdBindingDemoComponent implements OnInit {\r\n  selectedHouseholds1: string[] = [];\r\n  selectedHouseholds2: string[] = [];\r\n  selectedHouseholds3: string[] = [];\r\n  selectedHouseholds4: string[] = [];\r\n  selectedItems: HouseholdItem[] = [];\r\n  selectedItems2: HouseholdItem[] = [];\r\n  debugData: any = null;\r\n\r\n  customBuildingData: BuildingData = {\r\n    '總統套房': [\r\n      { code: 'P001', building: '總統套房', floor: '50F', isSelected: false, isDisabled: false },\r\n      { code: 'P002', building: '總統套房', floor: '51F', isSelected: false, isDisabled: false }\r\n    ],\r\n    '景觀樓層': Array.from({ length: 20 }, (_, i) => ({\r\n      code: `V${String(i + 1).padStart(3, '0')}`,\r\n      building: '景觀樓層',\r\n      floor: `${30 + Math.floor(i / 2)}F`,\r\n      isSelected: false,\r\n      isDisabled: i % 5 === 0 // 每五個禁用一個作為示例\r\n    }))\r\n  };\r\n\r\n  ngOnInit() {\r\n    // 初始化一些預選的戶別\r\n    this.selectedHouseholds1 = ['A001', 'A002', 'B001'];\r\n  }\r\n  onSelectionChange(selectedItems: HouseholdItem[]) {\r\n    this.selectedItems = selectedItems;\r\n    console.log('Selection changed:', selectedItems);\r\n  }\r\n  onSelectionChange2(selectedItems: HouseholdItem[]) {\r\n    this.selectedItems2 = selectedItems;\r\n    console.log('Selection 2 changed:', selectedItems);\r\n  }  debugInfo() {\r\n    this.debugData = {\r\n      selectedHouseholds1: this.selectedHouseholds1,\r\n      selectedHouseholds2: this.selectedHouseholds2,\r\n      selectedHouseholds3: this.selectedHouseholds3,\r\n      selectedHouseholds4: this.selectedHouseholds4,\r\n      customBuildingData: this.customBuildingData,\r\n      selectedItems: this.selectedItems,\r\n      selectedItems2: this.selectedItems2\r\n    };\r\n    console.log('Debug info:', this.debugData);\r\n  }\r\n\r\n  testBuildingSelect() {\r\n    console.log('Testing building selection...');\r\n    // 這個方法只是為了測試，實際上我們無法直接呼叫子元件的方法\r\n    // 主要是為了觸發除錯訊息\r\n    console.log('請在下拉選單中點擊 A棟，然後查看 Console 訊息');\r\n  }\r\n\r\n  clearSelection2() {\r\n    this.selectedHouseholds2 = [];\r\n    this.selectedItems2 = [];\r\n  }\r\n\r\n  removeFromSelection2(householdCode: string) {\r\n    this.selectedHouseholds2 = this.selectedHouseholds2.filter(code => code !== householdCode);\r\n    this.selectedItems2 = this.selectedItems2.filter(item => item.code !== householdCode);\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;IAyCgBA,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,iBAA4F;IAA9CD,EAAA,CAAAG,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,gBAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,oBAAA,CAAAP,gBAAA,CAAmC;IAAA,EAAC;IACzFL,EAAA,CAAAa,SAAA,kBAAwC;IAE5Cb,EADE,CAAAc,YAAA,EAAS,EACJ;;;;IAJLd,EAAA,CAAAe,SAAA,EACA;IADAf,EAAA,CAAAgB,kBAAA,MAAAX,gBAAA,MACA;;;;;;IAZJL,EAFJ,CAAAC,cAAA,cAA4E,cACtC,cACP;IACzBD,EAAA,CAAAa,SAAA,kBAA2E;IAC3Eb,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IACrEF,EADqE,CAAAc,YAAA,EAAO,EACtE;IACNd,EAAA,CAAAC,cAAA,iBAA2F;IAA5BD,EAAA,CAAAG,UAAA,mBAAAc,sEAAA;MAAAjB,EAAA,CAAAM,aAAA,CAAAY,GAAA;MAAA,MAAAT,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAU,eAAA,EAAiB;IAAA,EAAC;IACxFnB,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAc,YAAA,EAAS,EACL;IAEJd,EADF,CAAAC,cAAA,cAAqC,cACF;IAC/BD,EAAA,CAAAoB,UAAA,KAAAC,qDAAA,mBAAmF;IAQzFrB,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;IAhB6Bd,EAAA,CAAAe,SAAA,GAAsC;IAAtCf,EAAA,CAAAgB,kBAAA,qCAAAP,MAAA,CAAAa,mBAAA,CAAAC,MAAA,MAAsC;IAQnCvB,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAwB,UAAA,YAAAf,MAAA,CAAAa,mBAAA,CAAsB;;;;;IA+B5DtB,EADF,CAAAC,cAAA,aAAiE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,qDAA0B;IAAAF,EAAA,CAAAc,YAAA,EAAK;IACnCd,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;;IAC9DF,EAD8D,CAAAc,YAAA,EAAM,EAC9D;;;;IADwBd,EAAA,CAAAe,SAAA,GAAgC;IAAhCf,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA0B,WAAA,OAAAjB,MAAA,CAAAkB,mBAAA,EAAgC;;;;;IAE5D3B,EADY,CAAAC,cAAA,aAA2D,SACnE;IAAAD,EAAA,CAAAE,MAAA,sEAAa;IAAAF,EAAA,CAAAc,YAAA,EAAK;IACtBd,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;;IACxDF,EADwD,CAAAc,YAAA,EAAM,EACxD;;;;IADwBd,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA0B,WAAA,OAAAjB,MAAA,CAAAmB,aAAA,EAA0B;;;;;IAItD5B,EADF,CAAAC,cAAA,aAA4D,SACtD;IAAAD,EAAA,CAAAE,MAAA,8FAAiB;IAAAF,EAAA,CAAAc,YAAA,EAAK;IAC1Bd,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;;IACzDF,EADyD,CAAAc,YAAA,EAAM,EACzD;;;;IADwBd,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA0B,WAAA,OAAAjB,MAAA,CAAAoB,cAAA,EAA2B;;;;;IA6BrD7B,EADF,CAAAC,cAAA,cAAoC,gBACR;IAAAD,EAAA,CAAAE,MAAA,qCAAK;IAAAF,EAAA,CAAAc,YAAA,EAAQ;IACvCd,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;;IACpDF,EADoD,CAAAc,YAAA,EAAM,EACpD;;;;IADwBd,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA0B,WAAA,OAAAjB,MAAA,CAAAqB,SAAA,EAAsB;;;AAkH9D,OAAM,MAAOC,6BAA6B;EA9N1CC,YAAA;IA+NE,KAAAL,mBAAmB,GAAa,EAAE;IAClC,KAAAL,mBAAmB,GAAa,EAAE;IAClC,KAAAW,mBAAmB,GAAa,EAAE;IAClC,KAAAC,mBAAmB,GAAa,EAAE;IAClC,KAAAN,aAAa,GAAoB,EAAE;IACnC,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,SAAS,GAAQ,IAAI;IAErB,KAAAK,kBAAkB,GAAiB;MACjC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,EACtF;QAAEJ,IAAI,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,CACvF;MACD,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEnB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACoB,CAAC,EAAEC,CAAC,MAAM;QAC5CR,IAAI,EAAE,IAAIS,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QAC1CT,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,GAAG,EAAE,GAAGS,IAAI,CAACT,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,GAAG;QACnCL,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAEI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;OACzB,CAAC;KACH;;EAEDI,QAAQA,CAAA;IACN;IACA,IAAI,CAACrB,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrD;EACAsB,iBAAiBA,CAACrB,aAA8B;IAC9C,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClCsB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEvB,aAAa,CAAC;EAClD;EACAwB,kBAAkBA,CAACxB,aAA8B;IAC/C,IAAI,CAACC,cAAc,GAAGD,aAAa;IACnCsB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEvB,aAAa,CAAC;EACpD;EAAGyB,SAASA,CAAA;IACV,IAAI,CAACvB,SAAS,GAAG;MACfH,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CL,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CW,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;MAC7CC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CP,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,cAAc,EAAE,IAAI,CAACA;KACtB;IACDqB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACrB,SAAS,CAAC;EAC5C;EAEAwB,kBAAkBA,CAAA;IAChBJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C;IACA;IACAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC7C;EAEAhC,eAAeA,CAAA;IACb,IAAI,CAACG,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACO,cAAc,GAAG,EAAE;EAC1B;EAEAjB,oBAAoBA,CAAC2C,aAAqB;IACxC,IAAI,CAACjC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACkC,MAAM,CAACpB,IAAI,IAAIA,IAAI,KAAKmB,aAAa,CAAC;IAC1F,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC2B,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACrB,IAAI,KAAKmB,aAAa,CAAC;EACvF;;;uCA9DWxB,6BAA6B;IAAA;EAAA;;;YAA7BA,6BAA6B;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzNlChE,EAFJ,CAAAC,cAAA,cAAS,qBACS,SACV;UAAAD,EAAA,CAAAE,MAAA,uDAAQ;UACdF,EADc,CAAAc,YAAA,EAAK,EACF;UAEbd,EADJ,CAAAC,cAAA,mBAAc,aAAkC,SACxC;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACbd,EAAA,CAAAC,cAAA,+BAIgD;UAH9CD,EAAA,CAAAkE,gBAAA,2BAAAC,sFAAAC,MAAA;YAAApE,EAAA,CAAAqE,kBAAA,CAAAJ,GAAA,CAAAtC,mBAAA,EAAAyC,MAAA,MAAAH,GAAA,CAAAtC,mBAAA,GAAAyC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAGjCpE,EAAA,CAAAG,UAAA,6BAAAmE,wFAAAF,MAAA;YAAA,OAAmBH,GAAA,CAAAhB,iBAAA,CAAAmB,MAAA,CAAyB;UAAA,EAAC;UAEjDpE,EADE,CAAAc,YAAA,EAAwB,EACpB;UACJd,EADY,CAAAC,cAAA,aAA0B,UAClC;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACjBd,EAAA,CAAAC,cAAA,gCAKiD;UAJ/CD,EAAA,CAAAkE,gBAAA,2BAAAK,uFAAAH,MAAA;YAAApE,EAAA,CAAAqE,kBAAA,CAAAJ,GAAA,CAAA3C,mBAAA,EAAA8C,MAAA,MAAAH,GAAA,CAAA3C,mBAAA,GAAA8C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAIjCpE,EAAA,CAAAG,UAAA,6BAAAqE,yFAAAJ,MAAA;YAAA,OAAmBH,GAAA,CAAAb,kBAAA,CAAAgB,MAAA,CAA0B;UAAA,EAAC;UAChDpE,EAAA,CAAAc,YAAA,EAAwB;UAGxBd,EAAA,CAAAoB,UAAA,KAAAqD,6CAAA,kBAA4E;UAqB9EzE,EAAA,CAAAc,YAAA,EAAM;UAGJd,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,8DAAS;UAAAF,EAAA,CAAAc,YAAA,EAAK;UAClBd,EAAA,CAAAC,cAAA,gCAI6B;UAH3BD,EAAA,CAAAkE,gBAAA,2BAAAQ,uFAAAN,MAAA;YAAApE,EAAA,CAAAqE,kBAAA,CAAAJ,GAAA,CAAAhC,mBAAA,EAAAmC,MAAA,MAAAH,GAAA,CAAAhC,mBAAA,GAAAmC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAKrCpE,EADE,CAAAc,YAAA,EAAwB,EACpB;UAGJd,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,kDAAO;UAAAF,EAAA,CAAAc,YAAA,EAAK;UAChBd,EAAA,CAAAC,cAAA,gCAGsC;UAFpCD,EAAA,CAAAkE,gBAAA,2BAAAS,uFAAAP,MAAA;YAAApE,EAAA,CAAAqE,kBAAA,CAAAJ,GAAA,CAAA/B,mBAAA,EAAAkC,MAAA,MAAAH,GAAA,CAAA/B,mBAAA,GAAAkC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAiC;UAIrCpE,EADE,CAAAc,YAAA,EAAwB,EACpB;UAUNd,EARA,CAAAoB,UAAA,KAAAwD,6CAAA,iBAAiE,KAAAC,6CAAA,iBAGQ,KAAAC,6CAAA,iBAKb;UAI1D9E,EADY,CAAAC,cAAA,cAA0B,UAClC;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAc,YAAA,EAAK;UAGbd,EAFJ,CAAAC,cAAA,cAAuE,cACsC,aACtD;UAAAD,EAAA,CAAAE,MAAA,6EAAc;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACtEd,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAAAF,EAAA,CAAAc,YAAA,EAAI;UAClGd,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,IACF;UACFF,EADE,CAAAc,YAAA,EAAM,EACF;UAEJd,EADF,CAAAC,cAAA,eAA2G,cACtD;UAAAD,EAAA,CAAAE,MAAA,yFAAgB;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACxEd,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAAAF,EAAA,CAAAc,YAAA,EAAI;UAClGd,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,IACF;UAENF,EAFM,CAAAc,YAAA,EAAM,EACF,EAAgB,EACpB;UAGJd,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACbd,EAAA,CAAAC,cAAA,kBAAkF;UAAtBD,EAAA,CAAAG,UAAA,mBAAA4E,gEAAA;YAAA,OAASd,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAC/ErD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAAsF;UAA/BD,EAAA,CAAAG,UAAA,mBAAA6E,gEAAA;YAAA,OAASf,GAAA,CAAAX,kBAAA,EAAoB;UAAA,EAAC;UACnFtD,EAAA,CAAAE,MAAA,0CACF;UAAAF,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAoB,UAAA,KAAA6D,6CAAA,kBAAoC;UAItCjF,EAAA,CAAAc,YAAA,EAAM;UACJd,EADI,CAAAC,cAAA,cAA0B,UAC1B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACjBd,EAAA,CAAAa,SAAA,yBAAuC;UACzCb,EAAA,CAAAc,YAAA,EAAM;UAGJd,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAc,YAAA,EAAK;UACjBd,EAAA,CAAAa,SAAA,gCAAqD;UAG3Db,EAFI,CAAAc,YAAA,EAAM,EACO,EACP;;;UA9GFd,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAkF,gBAAA,YAAAjB,GAAA,CAAAtC,mBAAA,CAAiC;UAEjC3B,EAAA,CAAAwB,UAAA,qBAAoB;UAMpBxB,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAkF,gBAAA,YAAAjB,GAAA,CAAA3C,mBAAA,CAAiC;UAGjCtB,EADA,CAAAwB,UAAA,2BAA0B,qBACN;UAKhBxB,EAAA,CAAAe,SAAA,EAAoC;UAApCf,EAAA,CAAAwB,UAAA,SAAAyC,GAAA,CAAA3C,mBAAA,CAAAC,MAAA,KAAoC;UA0BxCvB,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAkF,gBAAA,YAAAjB,GAAA,CAAAhC,mBAAA,CAAiC;UAGjCjC,EADA,CAAAwB,UAAA,sBAAqB,2BACK;UAO1BxB,EAAA,CAAAe,SAAA,GAAiC;UAAjCf,EAAA,CAAAkF,gBAAA,YAAAjB,GAAA,CAAA/B,mBAAA,CAAiC;UAEjClC,EAAA,CAAAwB,UAAA,iBAAAyC,GAAA,CAAA9B,kBAAA,CAAmC;UAIZnC,EAAA,CAAAe,SAAA,EAAoC;UAApCf,EAAA,CAAAwB,UAAA,SAAAyC,GAAA,CAAAtC,mBAAA,CAAAJ,MAAA,KAAoC;UAGtBvB,EAAA,CAAAe,SAAA,EAA8B;UAA9Bf,EAAA,CAAAwB,UAAA,SAAAyC,GAAA,CAAArC,aAAA,CAAAL,MAAA,KAA8B;UAK5CvB,EAAA,CAAAe,SAAA,EAA+B;UAA/Bf,EAAA,CAAAwB,UAAA,SAAAyC,GAAA,CAAApC,cAAA,CAAAN,MAAA,KAA+B;UAQGvB,EAAA,CAAAe,SAAA,GAAuC;UAAvCf,EAAA,CAAAgB,kBAAA,yBAAAiD,GAAA,CAAAtC,mBAAA,CAAAJ,MAAA,wBAAuC;UAE5FvB,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAgB,kBAAA,MAAAiD,GAAA,CAAAtC,mBAAA,CAAAwD,IAAA,0CACF;UAIuDnF,EAAA,CAAAe,SAAA,GAAuC;UAAvCf,EAAA,CAAAgB,kBAAA,yBAAAiD,GAAA,CAAA3C,mBAAA,CAAAC,MAAA,wBAAuC;UAE5FvB,EAAA,CAAAe,SAAA,GACF;UADEf,EAAA,CAAAgB,kBAAA,MAAAiD,GAAA,CAAA3C,mBAAA,CAAA6D,IAAA,0CACF;UAYEnF,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAwB,UAAA,SAAAyC,GAAA,CAAAnC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}