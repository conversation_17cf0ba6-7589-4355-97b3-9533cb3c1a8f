{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic (Morocco) [ar-ma]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/ElFadiliY\n//! author : <PERSON><PERSON> Said : https://github.com/abdelsaid\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var arMa = moment.defineLocale('ar-ma', {\n    months: 'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split('_'),\n    monthsShort: 'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split('_'),\n    weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[اليوم على الساعة] LT',\n      nextDay: '[غدا على الساعة] LT',\n      nextWeek: 'dddd [على الساعة] LT',\n      lastDay: '[أمس على الساعة] LT',\n      lastWeek: 'dddd [على الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'في %s',\n      past: 'منذ %s',\n      s: 'ثوان',\n      ss: '%d ثانية',\n      m: 'دقيقة',\n      mm: '%d دقائق',\n      h: 'ساعة',\n      hh: '%d ساعات',\n      d: 'يوم',\n      dd: '%d أيام',\n      M: 'شهر',\n      MM: '%d أشهر',\n      y: 'سنة',\n      yy: '%d سنوات'\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return arMa;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}