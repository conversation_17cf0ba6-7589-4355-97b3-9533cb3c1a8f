{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory();\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([], factory);\n  } else {\n    // Global (browser)\n    root.CryptoJS = factory();\n  }\n})(this, function () {\n  /*globals window, global, require*/\n\n  /**\n   * CryptoJS core components.\n   */\n  var CryptoJS = CryptoJS || function (Math, undefined) {\n    var crypto;\n\n    // Native crypto from window (Browser)\n    if (typeof window !== 'undefined' && window.crypto) {\n      crypto = window.crypto;\n    }\n\n    // Native crypto in web worker (Browser)\n    if (typeof self !== 'undefined' && self.crypto) {\n      crypto = self.crypto;\n    }\n\n    // Native crypto from worker\n    if (typeof globalThis !== 'undefined' && globalThis.crypto) {\n      crypto = globalThis.crypto;\n    }\n\n    // Native (experimental IE 11) crypto from window (Browser)\n    if (!crypto && typeof window !== 'undefined' && window.msCrypto) {\n      crypto = window.msCrypto;\n    }\n\n    // Native crypto from global (NodeJS)\n    if (!crypto && typeof global !== 'undefined' && global.crypto) {\n      crypto = global.crypto;\n    }\n\n    // Native crypto import via require (NodeJS)\n    if (!crypto && typeof require === 'function') {\n      try {\n        crypto = require('crypto');\n      } catch (err) {}\n    }\n\n    /*\n     * Cryptographically secure pseudorandom number generator\n     *\n     * As Math.random() is cryptographically not safe to use\n     */\n    var cryptoSecureRandomInt = function () {\n      if (crypto) {\n        // Use getRandomValues method (Browser)\n        if (typeof crypto.getRandomValues === 'function') {\n          try {\n            return crypto.getRandomValues(new Uint32Array(1))[0];\n          } catch (err) {}\n        }\n\n        // Use randomBytes method (NodeJS)\n        if (typeof crypto.randomBytes === 'function') {\n          try {\n            return crypto.randomBytes(4).readInt32LE();\n          } catch (err) {}\n        }\n      }\n      throw new Error('Native crypto module could not be used to get secure random number.');\n    };\n\n    /*\n     * Local polyfill of Object.create\n      */\n    var create = Object.create || function () {\n      function F() {}\n      return function (obj) {\n        var subtype;\n        F.prototype = obj;\n        subtype = new F();\n        F.prototype = null;\n        return subtype;\n      };\n    }();\n\n    /**\n     * CryptoJS namespace.\n     */\n    var C = {};\n\n    /**\n     * Library namespace.\n     */\n    var C_lib = C.lib = {};\n\n    /**\n     * Base object for prototypal inheritance.\n     */\n    var Base = C_lib.Base = function () {\n      return {\n        /**\n         * Creates a new object that inherits from this object.\n         *\n         * @param {Object} overrides Properties to copy into the new object.\n         *\n         * @return {Object} The new object.\n         *\n         * @static\n         *\n         * @example\n         *\n         *     var MyType = CryptoJS.lib.Base.extend({\n         *         field: 'value',\n         *\n         *         method: function () {\n         *         }\n         *     });\n         */\n        extend: function (overrides) {\n          // Spawn\n          var subtype = create(this);\n\n          // Augment\n          if (overrides) {\n            subtype.mixIn(overrides);\n          }\n\n          // Create default initializer\n          if (!subtype.hasOwnProperty('init') || this.init === subtype.init) {\n            subtype.init = function () {\n              subtype.$super.init.apply(this, arguments);\n            };\n          }\n\n          // Initializer's prototype is the subtype object\n          subtype.init.prototype = subtype;\n\n          // Reference supertype\n          subtype.$super = this;\n          return subtype;\n        },\n        /**\n         * Extends this object and runs the init method.\n         * Arguments to create() will be passed to init().\n         *\n         * @return {Object} The new object.\n         *\n         * @static\n         *\n         * @example\n         *\n         *     var instance = MyType.create();\n         */\n        create: function () {\n          var instance = this.extend();\n          instance.init.apply(instance, arguments);\n          return instance;\n        },\n        /**\n         * Initializes a newly created object.\n         * Override this method to add some logic when your objects are created.\n         *\n         * @example\n         *\n         *     var MyType = CryptoJS.lib.Base.extend({\n         *         init: function () {\n         *             // ...\n         *         }\n         *     });\n         */\n        init: function () {},\n        /**\n         * Copies properties into this object.\n         *\n         * @param {Object} properties The properties to mix in.\n         *\n         * @example\n         *\n         *     MyType.mixIn({\n         *         field: 'value'\n         *     });\n         */\n        mixIn: function (properties) {\n          for (var propertyName in properties) {\n            if (properties.hasOwnProperty(propertyName)) {\n              this[propertyName] = properties[propertyName];\n            }\n          }\n\n          // IE won't copy toString using the loop above\n          if (properties.hasOwnProperty('toString')) {\n            this.toString = properties.toString;\n          }\n        },\n        /**\n         * Creates a copy of this object.\n         *\n         * @return {Object} The clone.\n         *\n         * @example\n         *\n         *     var clone = instance.clone();\n         */\n        clone: function () {\n          return this.init.prototype.extend(this);\n        }\n      };\n    }();\n\n    /**\n     * An array of 32-bit words.\n     *\n     * @property {Array} words The array of 32-bit words.\n     * @property {number} sigBytes The number of significant bytes in this word array.\n     */\n    var WordArray = C_lib.WordArray = Base.extend({\n      /**\n       * Initializes a newly created word array.\n       *\n       * @param {Array} words (Optional) An array of 32-bit words.\n       * @param {number} sigBytes (Optional) The number of significant bytes in the words.\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.lib.WordArray.create();\n       *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607]);\n       *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607], 6);\n       */\n      init: function (words, sigBytes) {\n        words = this.words = words || [];\n        if (sigBytes != undefined) {\n          this.sigBytes = sigBytes;\n        } else {\n          this.sigBytes = words.length * 4;\n        }\n      },\n      /**\n       * Converts this word array to a string.\n       *\n       * @param {Encoder} encoder (Optional) The encoding strategy to use. Default: CryptoJS.enc.Hex\n       *\n       * @return {string} The stringified word array.\n       *\n       * @example\n       *\n       *     var string = wordArray + '';\n       *     var string = wordArray.toString();\n       *     var string = wordArray.toString(CryptoJS.enc.Utf8);\n       */\n      toString: function (encoder) {\n        return (encoder || Hex).stringify(this);\n      },\n      /**\n       * Concatenates a word array to this word array.\n       *\n       * @param {WordArray} wordArray The word array to append.\n       *\n       * @return {WordArray} This word array.\n       *\n       * @example\n       *\n       *     wordArray1.concat(wordArray2);\n       */\n      concat: function (wordArray) {\n        // Shortcuts\n        var thisWords = this.words;\n        var thatWords = wordArray.words;\n        var thisSigBytes = this.sigBytes;\n        var thatSigBytes = wordArray.sigBytes;\n\n        // Clamp excess bits\n        this.clamp();\n\n        // Concat\n        if (thisSigBytes % 4) {\n          // Copy one byte at a time\n          for (var i = 0; i < thatSigBytes; i++) {\n            var thatByte = thatWords[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n            thisWords[thisSigBytes + i >>> 2] |= thatByte << 24 - (thisSigBytes + i) % 4 * 8;\n          }\n        } else {\n          // Copy one word at a time\n          for (var j = 0; j < thatSigBytes; j += 4) {\n            thisWords[thisSigBytes + j >>> 2] = thatWords[j >>> 2];\n          }\n        }\n        this.sigBytes += thatSigBytes;\n\n        // Chainable\n        return this;\n      },\n      /**\n       * Removes insignificant bits.\n       *\n       * @example\n       *\n       *     wordArray.clamp();\n       */\n      clamp: function () {\n        // Shortcuts\n        var words = this.words;\n        var sigBytes = this.sigBytes;\n\n        // Clamp\n        words[sigBytes >>> 2] &= 0xffffffff << 32 - sigBytes % 4 * 8;\n        words.length = Math.ceil(sigBytes / 4);\n      },\n      /**\n       * Creates a copy of this word array.\n       *\n       * @return {WordArray} The clone.\n       *\n       * @example\n       *\n       *     var clone = wordArray.clone();\n       */\n      clone: function () {\n        var clone = Base.clone.call(this);\n        clone.words = this.words.slice(0);\n        return clone;\n      },\n      /**\n       * Creates a word array filled with random bytes.\n       *\n       * @param {number} nBytes The number of random bytes to generate.\n       *\n       * @return {WordArray} The random word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.lib.WordArray.random(16);\n       */\n      random: function (nBytes) {\n        var words = [];\n        for (var i = 0; i < nBytes; i += 4) {\n          words.push(cryptoSecureRandomInt());\n        }\n        return new WordArray.init(words, nBytes);\n      }\n    });\n\n    /**\n     * Encoder namespace.\n     */\n    var C_enc = C.enc = {};\n\n    /**\n     * Hex encoding strategy.\n     */\n    var Hex = C_enc.Hex = {\n      /**\n       * Converts a word array to a hex string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The hex string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var hexString = CryptoJS.enc.Hex.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var hexChars = [];\n        for (var i = 0; i < sigBytes; i++) {\n          var bite = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n          hexChars.push((bite >>> 4).toString(16));\n          hexChars.push((bite & 0x0f).toString(16));\n        }\n        return hexChars.join('');\n      },\n      /**\n       * Converts a hex string to a word array.\n       *\n       * @param {string} hexStr The hex string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Hex.parse(hexString);\n       */\n      parse: function (hexStr) {\n        // Shortcut\n        var hexStrLength = hexStr.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < hexStrLength; i += 2) {\n          words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << 24 - i % 8 * 4;\n        }\n        return new WordArray.init(words, hexStrLength / 2);\n      }\n    };\n\n    /**\n     * Latin1 encoding strategy.\n     */\n    var Latin1 = C_enc.Latin1 = {\n      /**\n       * Converts a word array to a Latin1 string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The Latin1 string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var latin1String = CryptoJS.enc.Latin1.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        // Shortcuts\n        var words = wordArray.words;\n        var sigBytes = wordArray.sigBytes;\n\n        // Convert\n        var latin1Chars = [];\n        for (var i = 0; i < sigBytes; i++) {\n          var bite = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n          latin1Chars.push(String.fromCharCode(bite));\n        }\n        return latin1Chars.join('');\n      },\n      /**\n       * Converts a Latin1 string to a word array.\n       *\n       * @param {string} latin1Str The Latin1 string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Latin1.parse(latin1String);\n       */\n      parse: function (latin1Str) {\n        // Shortcut\n        var latin1StrLength = latin1Str.length;\n\n        // Convert\n        var words = [];\n        for (var i = 0; i < latin1StrLength; i++) {\n          words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << 24 - i % 4 * 8;\n        }\n        return new WordArray.init(words, latin1StrLength);\n      }\n    };\n\n    /**\n     * UTF-8 encoding strategy.\n     */\n    var Utf8 = C_enc.Utf8 = {\n      /**\n       * Converts a word array to a UTF-8 string.\n       *\n       * @param {WordArray} wordArray The word array.\n       *\n       * @return {string} The UTF-8 string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var utf8String = CryptoJS.enc.Utf8.stringify(wordArray);\n       */\n      stringify: function (wordArray) {\n        try {\n          return decodeURIComponent(escape(Latin1.stringify(wordArray)));\n        } catch (e) {\n          throw new Error('Malformed UTF-8 data');\n        }\n      },\n      /**\n       * Converts a UTF-8 string to a word array.\n       *\n       * @param {string} utf8Str The UTF-8 string.\n       *\n       * @return {WordArray} The word array.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var wordArray = CryptoJS.enc.Utf8.parse(utf8String);\n       */\n      parse: function (utf8Str) {\n        return Latin1.parse(unescape(encodeURIComponent(utf8Str)));\n      }\n    };\n\n    /**\n     * Abstract buffered block algorithm template.\n     *\n     * The property blockSize must be implemented in a concrete subtype.\n     *\n     * @property {number} _minBufferSize The number of blocks that should be kept unprocessed in the buffer. Default: 0\n     */\n    var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm = Base.extend({\n      /**\n       * Resets this block algorithm's data buffer to its initial state.\n       *\n       * @example\n       *\n       *     bufferedBlockAlgorithm.reset();\n       */\n      reset: function () {\n        // Initial values\n        this._data = new WordArray.init();\n        this._nDataBytes = 0;\n      },\n      /**\n       * Adds new data to this block algorithm's buffer.\n       *\n       * @param {WordArray|string} data The data to append. Strings are converted to a WordArray using UTF-8.\n       *\n       * @example\n       *\n       *     bufferedBlockAlgorithm._append('data');\n       *     bufferedBlockAlgorithm._append(wordArray);\n       */\n      _append: function (data) {\n        // Convert string to WordArray, else assume WordArray already\n        if (typeof data == 'string') {\n          data = Utf8.parse(data);\n        }\n\n        // Append\n        this._data.concat(data);\n        this._nDataBytes += data.sigBytes;\n      },\n      /**\n       * Processes available data blocks.\n       *\n       * This method invokes _doProcessBlock(offset), which must be implemented by a concrete subtype.\n       *\n       * @param {boolean} doFlush Whether all blocks and partial blocks should be processed.\n       *\n       * @return {WordArray} The processed data.\n       *\n       * @example\n       *\n       *     var processedData = bufferedBlockAlgorithm._process();\n       *     var processedData = bufferedBlockAlgorithm._process(!!'flush');\n       */\n      _process: function (doFlush) {\n        var processedWords;\n\n        // Shortcuts\n        var data = this._data;\n        var dataWords = data.words;\n        var dataSigBytes = data.sigBytes;\n        var blockSize = this.blockSize;\n        var blockSizeBytes = blockSize * 4;\n\n        // Count blocks ready\n        var nBlocksReady = dataSigBytes / blockSizeBytes;\n        if (doFlush) {\n          // Round up to include partial blocks\n          nBlocksReady = Math.ceil(nBlocksReady);\n        } else {\n          // Round down to include only full blocks,\n          // less the number of blocks that must remain in the buffer\n          nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);\n        }\n\n        // Count words ready\n        var nWordsReady = nBlocksReady * blockSize;\n\n        // Count bytes ready\n        var nBytesReady = Math.min(nWordsReady * 4, dataSigBytes);\n\n        // Process blocks\n        if (nWordsReady) {\n          for (var offset = 0; offset < nWordsReady; offset += blockSize) {\n            // Perform concrete-algorithm logic\n            this._doProcessBlock(dataWords, offset);\n          }\n\n          // Remove processed words\n          processedWords = dataWords.splice(0, nWordsReady);\n          data.sigBytes -= nBytesReady;\n        }\n\n        // Return processed words\n        return new WordArray.init(processedWords, nBytesReady);\n      },\n      /**\n       * Creates a copy of this object.\n       *\n       * @return {Object} The clone.\n       *\n       * @example\n       *\n       *     var clone = bufferedBlockAlgorithm.clone();\n       */\n      clone: function () {\n        var clone = Base.clone.call(this);\n        clone._data = this._data.clone();\n        return clone;\n      },\n      _minBufferSize: 0\n    });\n\n    /**\n     * Abstract hasher template.\n     *\n     * @property {number} blockSize The number of 32-bit words this hasher operates on. Default: 16 (512 bits)\n     */\n    var Hasher = C_lib.Hasher = BufferedBlockAlgorithm.extend({\n      /**\n       * Configuration options.\n       */\n      cfg: Base.extend(),\n      /**\n       * Initializes a newly created hasher.\n       *\n       * @param {Object} cfg (Optional) The configuration options to use for this hash computation.\n       *\n       * @example\n       *\n       *     var hasher = CryptoJS.algo.SHA256.create();\n       */\n      init: function (cfg) {\n        // Apply config defaults\n        this.cfg = this.cfg.extend(cfg);\n\n        // Set initial values\n        this.reset();\n      },\n      /**\n       * Resets this hasher to its initial state.\n       *\n       * @example\n       *\n       *     hasher.reset();\n       */\n      reset: function () {\n        // Reset data buffer\n        BufferedBlockAlgorithm.reset.call(this);\n\n        // Perform concrete-hasher logic\n        this._doReset();\n      },\n      /**\n       * Updates this hasher with a message.\n       *\n       * @param {WordArray|string} messageUpdate The message to append.\n       *\n       * @return {Hasher} This hasher.\n       *\n       * @example\n       *\n       *     hasher.update('message');\n       *     hasher.update(wordArray);\n       */\n      update: function (messageUpdate) {\n        // Append\n        this._append(messageUpdate);\n\n        // Update the hash\n        this._process();\n\n        // Chainable\n        return this;\n      },\n      /**\n       * Finalizes the hash computation.\n       * Note that the finalize operation is effectively a destructive, read-once operation.\n       *\n       * @param {WordArray|string} messageUpdate (Optional) A final message update.\n       *\n       * @return {WordArray} The hash.\n       *\n       * @example\n       *\n       *     var hash = hasher.finalize();\n       *     var hash = hasher.finalize('message');\n       *     var hash = hasher.finalize(wordArray);\n       */\n      finalize: function (messageUpdate) {\n        // Final message update\n        if (messageUpdate) {\n          this._append(messageUpdate);\n        }\n\n        // Perform concrete-hasher logic\n        var hash = this._doFinalize();\n        return hash;\n      },\n      blockSize: 512 / 32,\n      /**\n       * Creates a shortcut function to a hasher's object interface.\n       *\n       * @param {Hasher} hasher The hasher to create a helper for.\n       *\n       * @return {Function} The shortcut function.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var SHA256 = CryptoJS.lib.Hasher._createHelper(CryptoJS.algo.SHA256);\n       */\n      _createHelper: function (hasher) {\n        return function (message, cfg) {\n          return new hasher.init(cfg).finalize(message);\n        };\n      },\n      /**\n       * Creates a shortcut function to the HMAC's object interface.\n       *\n       * @param {Hasher} hasher The hasher to use in this HMAC helper.\n       *\n       * @return {Function} The shortcut function.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var HmacSHA256 = CryptoJS.lib.Hasher._createHmacHelper(CryptoJS.algo.SHA256);\n       */\n      _createHmacHelper: function (hasher) {\n        return function (message, key) {\n          return new C_algo.HMAC.init(hasher, key).finalize(message);\n        };\n      }\n    });\n\n    /**\n     * Algorithm namespace.\n     */\n    var C_algo = C.algo = {};\n    return C;\n  }(Math);\n  return CryptoJS;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}