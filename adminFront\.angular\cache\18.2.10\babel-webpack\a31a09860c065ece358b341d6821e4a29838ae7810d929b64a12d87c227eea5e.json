{"ast": null, "code": "import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nvar defaultFormat = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds'];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param {Duration} duration - the duration to format\n * @param {Object} [options] - an object with options.\n * @param {string[]} [options.format=['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']] - the array of units to format\n * @param {boolean} [options.zero=false] - should zeros be included in the output?\n * @param {string} [options.delimiter=' '] - delimiter string\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {string} the formatted date string\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport default function formatDuration(duration, options) {\n  var _ref, _options$locale, _options$format, _options$zero, _options$delimiter;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var format = (_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : defaultFormat;\n  var zero = (_options$zero = options === null || options === void 0 ? void 0 : options.zero) !== null && _options$zero !== void 0 ? _options$zero : false;\n  var delimiter = (_options$delimiter = options === null || options === void 0 ? void 0 : options.delimiter) !== null && _options$delimiter !== void 0 ? _options$delimiter : ' ';\n  if (!locale.formatDistance) {\n    return '';\n  }\n  var result = format.reduce(function (acc, unit) {\n    var token = \"x\".concat(unit.replace(/(^.)/, function (m) {\n      return m.toUpperCase();\n    }));\n    var value = duration[unit];\n    if (typeof value === 'number' && (zero || duration[unit])) {\n      return acc.concat(locale.formatDistance(token, value));\n    }\n    return acc;\n  }, []).join(delimiter);\n  return result;\n}", "map": {"version": 3, "names": ["getDefaultOptions", "defaultLocale", "defaultFormat", "formatDuration", "duration", "options", "_ref", "_options$locale", "_options$format", "_options$zero", "_options$delimiter", "arguments", "length", "TypeError", "concat", "defaultOptions", "locale", "format", "zero", "delimiter", "formatDistance", "result", "reduce", "acc", "unit", "token", "replace", "m", "toUpperCase", "value", "join"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/formatDuration/index.js"], "sourcesContent": ["import { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\nimport defaultLocale from \"../_lib/defaultLocale/index.js\";\nvar defaultFormat = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds'];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param {Duration} duration - the duration to format\n * @param {Object} [options] - an object with options.\n * @param {string[]} [options.format=['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']] - the array of units to format\n * @param {boolean} [options.zero=false] - should zeros be included in the output?\n * @param {string} [options.delimiter=' '] - delimiter string\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {string} the formatted date string\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport default function formatDuration(duration, options) {\n  var _ref, _options$locale, _options$format, _options$zero, _options$delimiter;\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n  var defaultOptions = getDefaultOptions();\n  var locale = (_ref = (_options$locale = options === null || options === void 0 ? void 0 : options.locale) !== null && _options$locale !== void 0 ? _options$locale : defaultOptions.locale) !== null && _ref !== void 0 ? _ref : defaultLocale;\n  var format = (_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : defaultFormat;\n  var zero = (_options$zero = options === null || options === void 0 ? void 0 : options.zero) !== null && _options$zero !== void 0 ? _options$zero : false;\n  var delimiter = (_options$delimiter = options === null || options === void 0 ? void 0 : options.delimiter) !== null && _options$delimiter !== void 0 ? _options$delimiter : ' ';\n  if (!locale.formatDistance) {\n    return '';\n  }\n  var result = format.reduce(function (acc, unit) {\n    var token = \"x\".concat(unit.replace(/(^.)/, function (m) {\n      return m.toUpperCase();\n    }));\n    var value = duration[unit];\n    if (typeof value === 'number' && (zero || duration[unit])) {\n      return acc.concat(locale.formatDistance(token, value));\n    }\n    return acc;\n  }, []).join(delimiter);\n  return result;\n}"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AACnE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,IAAIC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;;AAEvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACxD,IAAIC,IAAI,EAAEC,eAAe,EAAEC,eAAe,EAAEC,aAAa,EAAEC,kBAAkB;EAC7E,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,gCAAgC,CAACC,MAAM,CAACH,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC,CAAC;EAC5F;EACA,IAAIG,cAAc,GAAGf,iBAAiB,CAAC,CAAC;EACxC,IAAIgB,MAAM,GAAG,CAACV,IAAI,GAAG,CAACC,eAAe,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,MAAM,MAAM,IAAI,IAAIT,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGQ,cAAc,CAACC,MAAM,MAAM,IAAI,IAAIV,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGL,aAAa;EAC9O,IAAIgB,MAAM,GAAG,CAACT,eAAe,GAAGH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIT,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAGN,aAAa;EAC1K,IAAIgB,IAAI,GAAG,CAACT,aAAa,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACa,IAAI,MAAM,IAAI,IAAIT,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,KAAK;EACxJ,IAAIU,SAAS,GAAG,CAACT,kBAAkB,GAAGL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACc,SAAS,MAAM,IAAI,IAAIT,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,GAAG;EAC/K,IAAI,CAACM,MAAM,CAACI,cAAc,EAAE;IAC1B,OAAO,EAAE;EACX;EACA,IAAIC,MAAM,GAAGJ,MAAM,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IAC9C,IAAIC,KAAK,GAAG,GAAG,CAACX,MAAM,CAACU,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;MACvD,OAAOA,CAAC,CAACC,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAIC,KAAK,GAAGzB,QAAQ,CAACoB,IAAI,CAAC;IAC1B,IAAI,OAAOK,KAAK,KAAK,QAAQ,KAAKX,IAAI,IAAId,QAAQ,CAACoB,IAAI,CAAC,CAAC,EAAE;MACzD,OAAOD,GAAG,CAACT,MAAM,CAACE,MAAM,CAACI,cAAc,CAACK,KAAK,EAAEI,KAAK,CAAC,CAAC;IACxD;IACA,OAAON,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC,CAACO,IAAI,CAACX,SAAS,CAAC;EACtB,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}