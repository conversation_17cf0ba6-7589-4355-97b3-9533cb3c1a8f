{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./x64-core\"), require(\"./sha512\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./x64-core\", \"./sha512\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_x64 = C.x64;\n    var X64Word = C_x64.Word;\n    var X64WordArray = C_x64.WordArray;\n    var C_algo = C.algo;\n    var SHA512 = C_algo.SHA512;\n\n    /**\n     * SHA-384 hash algorithm.\n     */\n    var SHA384 = C_algo.SHA384 = SHA512.extend({\n      _doReset: function () {\n        this._hash = new X64WordArray.init([new X64Word.init(0xcbbb9d5d, 0xc1059ed8), new X64Word.init(0x629a292a, 0x367cd507), new X64Word.init(0x9159015a, 0x3070dd17), new X64Word.init(0x152fecd8, 0xf70e5939), new X64Word.init(0x67332667, 0xffc00b31), new X64Word.init(0x8eb44a87, 0x68581511), new X64Word.init(0xdb0c2e0d, 0x64f98fa7), new X64Word.init(0x47b5481d, 0xbefa4fa4)]);\n      },\n      _doFinalize: function () {\n        var hash = SHA512._doFinalize.call(this);\n        hash.sigBytes -= 16;\n        return hash;\n      }\n    });\n\n    /**\n     * Shortcut function to the hasher's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     *\n     * @return {WordArray} The hash.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hash = CryptoJS.SHA384('message');\n     *     var hash = CryptoJS.SHA384(wordArray);\n     */\n    C.SHA384 = SHA512._createHelper(SHA384);\n\n    /**\n     * Shortcut function to the HMAC's object interface.\n     *\n     * @param {WordArray|string} message The message to hash.\n     * @param {WordArray|string} key The secret key.\n     *\n     * @return {WordArray} The HMAC.\n     *\n     * @static\n     *\n     * @example\n     *\n     *     var hmac = CryptoJS.HmacSHA384(message, key);\n     */\n    C.HmacSHA384 = SHA512._createHmacHelper(SHA384);\n  })();\n  return CryptoJS.SHA384;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "C", "C_x64", "x64", "X64Word", "Word", "X64WordArray", "WordArray", "C_algo", "algo", "SHA512", "SHA384", "extend", "_doReset", "_hash", "init", "_doFinalize", "hash", "call", "sigBytes", "_createHelper", "HmacSHA384", "_createHmacHelper"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/crypto-js/sha384.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./x64-core\"), require(\"./sha512\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./x64-core\", \"./sha512\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_x64 = C.x64;\n\t    var X64Word = C_x64.Word;\n\t    var X64WordArray = C_x64.WordArray;\n\t    var C_algo = C.algo;\n\t    var SHA512 = C_algo.SHA512;\n\n\t    /**\n\t     * SHA-384 hash algorithm.\n\t     */\n\t    var SHA384 = C_algo.SHA384 = SHA512.extend({\n\t        _doReset: function () {\n\t            this._hash = new X64WordArray.init([\n\t                new X64Word.init(0xcbbb9d5d, 0xc1059ed8), new X64Word.init(0x629a292a, 0x367cd507),\n\t                new X64Word.init(0x9159015a, 0x3070dd17), new X64Word.init(0x152fecd8, 0xf70e5939),\n\t                new X64Word.init(0x67332667, 0xffc00b31), new X64Word.init(0x8eb44a87, 0x68581511),\n\t                new X64Word.init(0xdb0c2e0d, 0x64f98fa7), new X64Word.init(0x47b5481d, 0xbefa4fa4)\n\t            ]);\n\t        },\n\n\t        _doFinalize: function () {\n\t            var hash = SHA512._doFinalize.call(this);\n\n\t            hash.sigBytes -= 16;\n\n\t            return hash;\n\t        }\n\t    });\n\n\t    /**\n\t     * Shortcut function to the hasher's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     *\n\t     * @return {WordArray} The hash.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hash = CryptoJS.SHA384('message');\n\t     *     var hash = CryptoJS.SHA384(wordArray);\n\t     */\n\t    C.SHA384 = SHA512._createHelper(SHA384);\n\n\t    /**\n\t     * Shortcut function to the HMAC's object interface.\n\t     *\n\t     * @param {WordArray|string} message The message to hash.\n\t     * @param {WordArray|string} key The secret key.\n\t     *\n\t     * @return {WordArray} The HMAC.\n\t     *\n\t     * @static\n\t     *\n\t     * @example\n\t     *\n\t     *     var hmac = CryptoJS.HmacSHA384(message, key);\n\t     */\n\t    C.HmacSHA384 = SHA512._createHmacHelper(SHA384);\n\t}());\n\n\n\treturn CryptoJS.SHA384;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,EAAEA,OAAO,CAAC,UAAU,CAAC,CAAC;EAClG,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,EAAEL,OAAO,CAAC;EACtD,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAIC,CAAC,GAAGD,QAAQ;IAChB,IAAIE,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,OAAO,GAAGF,KAAK,CAACG,IAAI;IACxB,IAAIC,YAAY,GAAGJ,KAAK,CAACK,SAAS;IAClC,IAAIC,MAAM,GAAGP,CAAC,CAACQ,IAAI;IACnB,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;;IAE1B;AACL;AACA;IACK,IAAIC,MAAM,GAAGH,MAAM,CAACG,MAAM,GAAGD,MAAM,CAACE,MAAM,CAAC;MACvCC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,IAAI,CAACC,KAAK,GAAG,IAAIR,YAAY,CAACS,IAAI,CAAC,CAC/B,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAClF,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAClF,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAClF,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,IAAIX,OAAO,CAACW,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CACrF,CAAC;MACN,CAAC;MAEDC,WAAW,EAAE,SAAAA,CAAA,EAAY;QACrB,IAAIC,IAAI,GAAGP,MAAM,CAACM,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC;QAExCD,IAAI,CAACE,QAAQ,IAAI,EAAE;QAEnB,OAAOF,IAAI;MACf;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKhB,CAAC,CAACU,MAAM,GAAGD,MAAM,CAACU,aAAa,CAACT,MAAM,CAAC;;IAEvC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACKV,CAAC,CAACoB,UAAU,GAAGX,MAAM,CAACY,iBAAiB,CAACX,MAAM,CAAC;EACnD,CAAC,EAAC,CAAC;EAGH,OAAOX,QAAQ,CAACW,MAAM;AAEvB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}