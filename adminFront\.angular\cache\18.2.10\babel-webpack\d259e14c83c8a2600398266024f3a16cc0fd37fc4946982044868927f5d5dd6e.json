{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor() {\n    this.availableData = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\n    this.moduleType = 'Requirement'; // 模組類型，用於區分資料來源\n    this.selectTemplate = new EventEmitter();\n    this.close = new EventEmitter(); // 關閉事件\n    // 內部管理的模板資料\n    this.templates = [];\n    this.templateDetails = [];\n    this.selectedTemplate = null;\n    // 查詢功能\n    this.searchKeyword = '';\n    this.filteredTemplates = [];\n    // 新增模板表單\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  ngOnInit() {\n    this.loadTemplates();\n    this.updateFilteredTemplates();\n  }\n  ngOnChanges() {\n    this.updateFilteredTemplates();\n  }\n  // TODO: 替換為實際的API調用\n  loadTemplates() {\n    // 模擬API調用 - 載入模板列表\n    console.log('載入模板 API 調用:', {\n      moduleType: this.moduleType\n    });\n    this.templates = [{\n      TemplateID: 1,\n      TemplateName: '需求模板A',\n      Description: '包含基本工程項目的模板'\n    }, {\n      TemplateID: 2,\n      TemplateName: '需求模板B',\n      Description: '包含進階工程項目的模板'\n    }];\n    // 載入對應模組類型的模板詳情\n    this.templateDetails = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: this.moduleType,\n      FieldName: this.getFieldName({}),\n      FieldValue: '工程項目C'\n    }];\n  }\n  // 更新過濾後的模板列表\n  updateFilteredTemplates() {\n    if (!this.searchKeyword.trim()) {\n      this.filteredTemplates = [...this.templates];\n    } else {\n      const keyword = this.searchKeyword.toLowerCase();\n      this.filteredTemplates = this.templates.filter(template => template.TemplateName.toLowerCase().includes(keyword) || template.Description && template.Description.toLowerCase().includes(keyword));\n    }\n  }\n  // 搜尋模板\n  onSearch() {\n    this.updateFilteredTemplates();\n  }\n  // 清除搜尋\n  clearSearch() {\n    this.searchKeyword = '';\n    this.updateFilteredTemplates();\n  }\n  // 顯示新增模板表單\n  onAddTemplate() {\n    this.showAddForm = true;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n    // 重置可選資料的選擇狀態\n    this.availableData.forEach(item => item.selected = false);\n  }\n  // 取消新增模板\n  cancelAddTemplate() {\n    this.showAddForm = false;\n    this.newTemplate = {\n      name: '',\n      description: '',\n      selectedItems: []\n    };\n  }\n  // 儲存新模板\n  saveNewTemplate() {\n    if (!this.newTemplate.name.trim()) {\n      alert('請輸入模板名稱');\n      return;\n    }\n    const selectedItems = this.availableData.filter(item => item.selected);\n    if (selectedItems.length === 0) {\n      alert('請至少選擇一個項目');\n      return;\n    }\n    // TODO: 替換為實際的API調用\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\n  }\n  // TODO: 替換為實際的API調用\n  createTemplate(name, description, selectedItems) {\n    // 模擬API調用 - 創建模板\n    console.log('創建模板 API 調用:', {\n      templateName: name,\n      description: description,\n      moduleType: this.moduleType,\n      selectedItems: selectedItems.map(item => ({\n        refId: this.getRefId(item),\n        fieldName: this.getFieldName(item),\n        fieldValue: this.getFieldValue(item)\n      }))\n    });\n    // 生成新的模板ID (實際應由後端API返回)\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\n    // 創建新模板\n    const newTemplate = {\n      TemplateID: newId,\n      TemplateName: name.trim(),\n      Description: description.trim()\n    };\n    // 添加到模板列表\n    this.templates.push(newTemplate);\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\n    selectedItems.forEach((item, index) => {\n      const detail = {\n        TemplateDetailID: this.templateDetails.length + index + 1,\n        TemplateID: newId,\n        RefID: this.getRefId(item),\n        // 關聯主檔ID\n        ModuleType: this.moduleType,\n        // 模組類型，用於區分資料來源\n        FieldName: this.getFieldName(item),\n        // 欄位名稱\n        FieldValue: this.getFieldValue(item) // 欄位值\n      };\n      this.templateDetails.push(detail);\n    });\n    // 更新過濾列表\n    this.updateFilteredTemplates();\n    // 關閉表單\n    this.showAddForm = false;\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\n  }\n  // 獲取關聯主檔ID的輔助方法\n  getRefId(item) {\n    return item.CRequirementID || item.ID || item.id || 0;\n  }\n  // 獲取欄位名稱的輔助方法\n  getFieldName(_item) {\n    // 根據模組類型決定欄位名稱\n    switch (this.moduleType) {\n      case 'Requirement':\n        return 'CRequirement';\n      default:\n        return 'name';\n    }\n  }\n  // 獲取欄位值的輔助方法\n  getFieldValue(item) {\n    return item.CRequirement || item.name || item.title || '';\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 關閉模板查看器\n  onClose() {\n    this.close.emit();\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      // TODO: 替換為實際的API調用\n      this.deleteTemplateById(templateID);\n    }\n  }\n  // TODO: 替換為實際的API調用\n  deleteTemplateById(templateID) {\n    // 模擬API調用 - 刪除模板\n    console.log('刪除模板 API 調用:', {\n      templateID: templateID,\n      moduleType: this.moduleType\n    });\n    // 刪除模板和相關詳情\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\n    this.updateFilteredTemplates();\n    // 如果當前查看的模板被刪除，關閉詳情\n    if (this.selectedTemplate?.TemplateID === templateID) {\n      this.selectedTemplate = null;\n    }\n    alert('模板已刪除');\n  }\n  /*\n   * API 設計說明：\n   *\n   * 1. 載入模板列表 API:\n   *    GET /api/templates?moduleType={moduleType}\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\n   *\n   * 2. 創建模板 API:\n   *    POST /api/templates\n   *    請求體: {\n   *      templateName: string,\n   *      description: string,\n   *      moduleType: string,\n   *      details: [{\n   *        refId: number,        // 關聯主檔ID\n   *        fieldName: string,    // 欄位名稱\n   *        fieldValue: string    // 欄位值\n   *      }]\n   *    }\n   *\n   * 3. 刪除模板 API:\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\n   *\n   * 資料庫設計重點：\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\n   */\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n  // TrackBy函數用於優化ngFor性能\n  trackByTemplateId(index, template) {\n    return template.TemplateID || index;\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"availableData\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"moduleType\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"close\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss'],\n  standalone: true,\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule]\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "CommonModule", "FormsModule", "NbCardModule", "NbButtonModule", "TemplateViewerComponent", "constructor", "availableData", "moduleType", "selectTemplate", "close", "templates", "templateDetails", "selectedTemplate", "searchKeyword", "filteredTemplates", "showAddForm", "newTemplate", "name", "description", "selectedItems", "ngOnInit", "loadTemplates", "updateFilteredTemplates", "ngOnChanges", "console", "log", "TemplateID", "TemplateName", "Description", "TemplateDetailID", "RefID", "ModuleType", "FieldName", "getFieldName", "FieldValue", "trim", "keyword", "toLowerCase", "filter", "template", "includes", "onSearch", "clearSearch", "onAddTemplate", "for<PERSON>ach", "item", "selected", "cancelAddTemplate", "saveNewTemplate", "alert", "length", "createTemplate", "templateName", "map", "refId", "getRefId", "fieldName", "fieldValue", "getFieldValue", "newId", "Math", "max", "t", "push", "index", "detail", "CRequirementID", "ID", "id", "_item", "CRequirement", "title", "onSelectTemplate", "emit", "onClose", "onDeleteTemplate", "templateID", "confirm", "deleteTemplateById", "d", "closeTemplateDetail", "currentTemplateDetails", "trackByTemplateId", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output, OnInit, OnChanges } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbCardModule, NbButtonModule } from '@nebular/theme';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NbCardModule, NbButtonModule],\r\n})\r\nexport class TemplateViewerComponent implements OnInit, OnChanges {\r\n  @Input() availableData: any[] = []; // 父元件傳入的可選資料 (包含關聯主檔ID和名稱)\r\n  @Input() moduleType: string = 'Requirement'; // 模組類型，用於區分資料來源\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() close = new EventEmitter<void>(); // 關閉事件\r\n\r\n  // 內部管理的模板資料\r\n  templates: Template[] = [];\r\n  templateDetails: TemplateDetail[] = [];\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 查詢功能\r\n  searchKeyword = '';\r\n  filteredTemplates: Template[] = [];\r\n\r\n  // 新增模板表單\r\n  showAddForm = false;\r\n  newTemplate = {\r\n    name: '',\r\n    description: '',\r\n    selectedItems: [] as any[]\r\n  };\r\n\r\n  ngOnInit() {\r\n    this.loadTemplates();\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  loadTemplates() {\r\n    // 模擬API調用 - 載入模板列表\r\n    console.log('載入模板 API 調用:', { moduleType: this.moduleType });\r\n\r\n    this.templates = [\r\n      { TemplateID: 1, TemplateName: '需求模板A', Description: '包含基本工程項目的模板' },\r\n      { TemplateID: 2, TemplateName: '需求模板B', Description: '包含進階工程項目的模板' }\r\n    ];\r\n\r\n    // 載入對應模組類型的模板詳情\r\n    this.templateDetails = [\r\n      {\r\n        TemplateDetailID: 1,\r\n        TemplateID: 1,\r\n        RefID: 101,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目A'\r\n      },\r\n      {\r\n        TemplateDetailID: 2,\r\n        TemplateID: 1,\r\n        RefID: 102,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目B'\r\n      },\r\n      {\r\n        TemplateDetailID: 3,\r\n        TemplateID: 2,\r\n        RefID: 201,\r\n        ModuleType: this.moduleType,\r\n        FieldName: this.getFieldName({}),\r\n        FieldValue: '工程項目C'\r\n      }\r\n    ];\r\n  }\r\n\r\n  // 更新過濾後的模板列表\r\n  updateFilteredTemplates() {\r\n    if (!this.searchKeyword.trim()) {\r\n      this.filteredTemplates = [...this.templates];\r\n    } else {\r\n      const keyword = this.searchKeyword.toLowerCase();\r\n      this.filteredTemplates = this.templates.filter(template =>\r\n        template.TemplateName.toLowerCase().includes(keyword) ||\r\n        (template.Description && template.Description.toLowerCase().includes(keyword))\r\n      );\r\n    }\r\n  }\r\n\r\n  // 搜尋模板\r\n  onSearch() {\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n  // 清除搜尋\r\n  clearSearch() {\r\n    this.searchKeyword = '';\r\n    this.updateFilteredTemplates();\r\n  }\r\n\r\n\r\n\r\n  // 顯示新增模板表單\r\n  onAddTemplate() {\r\n    this.showAddForm = true;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n    // 重置可選資料的選擇狀態\r\n    this.availableData.forEach(item => item.selected = false);\r\n  }\r\n\r\n  // 取消新增模板\r\n  cancelAddTemplate() {\r\n    this.showAddForm = false;\r\n    this.newTemplate = {\r\n      name: '',\r\n      description: '',\r\n      selectedItems: []\r\n    };\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate() {\r\n    if (!this.newTemplate.name.trim()) {\r\n      alert('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    const selectedItems = this.availableData.filter(item => item.selected);\r\n    if (selectedItems.length === 0) {\r\n      alert('請至少選擇一個項目');\r\n      return;\r\n    }\r\n\r\n    // TODO: 替換為實際的API調用\r\n    this.createTemplate(this.newTemplate.name, this.newTemplate.description, selectedItems);\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  createTemplate(name: string, description: string, selectedItems: any[]) {\r\n    // 模擬API調用 - 創建模板\r\n    console.log('創建模板 API 調用:', {\r\n      templateName: name,\r\n      description: description,\r\n      moduleType: this.moduleType,\r\n      selectedItems: selectedItems.map(item => ({\r\n        refId: this.getRefId(item),\r\n        fieldName: this.getFieldName(item),\r\n        fieldValue: this.getFieldValue(item)\r\n      }))\r\n    });\r\n\r\n    // 生成新的模板ID (實際應由後端API返回)\r\n    const newId = Math.max(...this.templates.map(t => t.TemplateID || 0), 0) + 1;\r\n\r\n    // 創建新模板\r\n    const newTemplate: Template = {\r\n      TemplateID: newId,\r\n      TemplateName: name.trim(),\r\n      Description: description.trim()\r\n    };\r\n\r\n    // 添加到模板列表\r\n    this.templates.push(newTemplate);\r\n\r\n    // 創建模板詳情 - 根據選中的項目創建詳情記錄\r\n    selectedItems.forEach((item, index) => {\r\n      const detail: TemplateDetail = {\r\n        TemplateDetailID: this.templateDetails.length + index + 1,\r\n        TemplateID: newId,\r\n        RefID: this.getRefId(item), // 關聯主檔ID\r\n        ModuleType: this.moduleType, // 模組類型，用於區分資料來源\r\n        FieldName: this.getFieldName(item), // 欄位名稱\r\n        FieldValue: this.getFieldValue(item) // 欄位值\r\n      };\r\n      this.templateDetails.push(detail);\r\n    });\r\n\r\n    // 更新過濾列表\r\n    this.updateFilteredTemplates();\r\n\r\n    // 關閉表單\r\n    this.showAddForm = false;\r\n    alert(`模板 \"${name}\" 已成功創建！包含 ${selectedItems.length} 個項目`);\r\n  }\r\n\r\n  // 獲取關聯主檔ID的輔助方法\r\n  private getRefId(item: any): number {\r\n    return item.CRequirementID || item.ID || item.id || 0;\r\n  }\r\n\r\n  // 獲取欄位名稱的輔助方法\r\n  private getFieldName(_item?: any): string {\r\n    // 根據模組類型決定欄位名稱\r\n    switch (this.moduleType) {\r\n      case 'Requirement':\r\n        return 'CRequirement';\r\n      default:\r\n        return 'name';\r\n    }\r\n  }\r\n\r\n  // 獲取欄位值的輔助方法\r\n  private getFieldValue(item: any): string {\r\n    return item.CRequirement || item.name || item.title || '';\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 關閉模板查看器\r\n  onClose() {\r\n    this.close.emit();\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      // TODO: 替換為實際的API調用\r\n      this.deleteTemplateById(templateID);\r\n    }\r\n  }\r\n\r\n  // TODO: 替換為實際的API調用\r\n  deleteTemplateById(templateID: number) {\r\n    // 模擬API調用 - 刪除模板\r\n    console.log('刪除模板 API 調用:', {\r\n      templateID: templateID,\r\n      moduleType: this.moduleType\r\n    });\r\n\r\n    // 刪除模板和相關詳情\r\n    this.templates = this.templates.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetails = this.templateDetails.filter(d => d.TemplateID !== templateID);\r\n    this.updateFilteredTemplates();\r\n\r\n    // 如果當前查看的模板被刪除，關閉詳情\r\n    if (this.selectedTemplate?.TemplateID === templateID) {\r\n      this.selectedTemplate = null;\r\n    }\r\n\r\n    alert('模板已刪除');\r\n  }\r\n\r\n  /*\r\n   * API 設計說明：\r\n   *\r\n   * 1. 載入模板列表 API:\r\n   *    GET /api/templates?moduleType={moduleType}\r\n   *    返回: { templates: Template[], templateDetails: TemplateDetail[] }\r\n   *\r\n   * 2. 創建模板 API:\r\n   *    POST /api/templates\r\n   *    請求體: {\r\n   *      templateName: string,\r\n   *      description: string,\r\n   *      moduleType: string,\r\n   *      details: [{\r\n   *        refId: number,        // 關聯主檔ID\r\n   *        fieldName: string,    // 欄位名稱\r\n   *        fieldValue: string    // 欄位值\r\n   *      }]\r\n   *    }\r\n   *\r\n   * 3. 刪除模板 API:\r\n   *    DELETE /api/templates/{templateId}?moduleType={moduleType}\r\n   *\r\n   * 資料庫設計重點：\r\n   * - template 表：存放模板基本資訊 (TemplateID, TemplateName, Description)\r\n   * - templatedetail 表：存放模板詳情 (TemplateDetailID, TemplateID, RefID, ModuleType, FieldName, FieldValue)\r\n   * - ModuleType 欄位用於區分不同模組的資料 (如: 'Requirement', 'Product', 'Order' 等)\r\n   * - RefID 存放關聯主檔的ID，配合 ModuleType 可以找到對應的原始資料\r\n   */\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n\r\n  // TrackBy函數用於優化ngFor性能\r\n  trackByTemplateId(index: number, template: Template): number {\r\n    return template.TemplateID || index;\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAA2B,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,QAAQ,gBAAgB;AAStD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAA7BC,YAAA;IACI,KAAAC,aAAa,GAAU,EAAE,CAAC,CAAC;IAC3B,KAAAC,UAAU,GAAW,aAAa,CAAC,CAAC;IACnC,KAAAC,cAAc,GAAG,IAAIX,YAAY,EAAY;IAC7C,KAAAY,KAAK,GAAG,IAAIZ,YAAY,EAAQ,CAAC,CAAC;IAE5C;IACA,KAAAa,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,gBAAgB,GAAoB,IAAI;IAExC;IACA,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAe,EAAE;IAElC;IACA,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,WAAW,GAAG;MACZC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;EA+QH;EA7QEC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,uBAAuB,EAAE;EAChC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACD,uBAAuB,EAAE;EAChC;EAEA;EACAD,aAAaA,CAAA;IACX;IACAG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAAElB,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE,CAAC;IAE5D,IAAI,CAACG,SAAS,GAAG,CACf;MAAEgB,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,EACpE;MAAEF,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE,OAAO;MAAEC,WAAW,EAAE;IAAa,CAAE,CACrE;IAED;IACA,IAAI,CAACjB,eAAe,GAAG,CACrB;MACEkB,gBAAgB,EAAE,CAAC;MACnBH,UAAU,EAAE,CAAC;MACbI,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACxB,UAAU;MAC3ByB,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE,CAAC;MAChCC,UAAU,EAAE;KACb,EACD;MACEL,gBAAgB,EAAE,CAAC;MACnBH,UAAU,EAAE,CAAC;MACbI,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACxB,UAAU;MAC3ByB,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE,CAAC;MAChCC,UAAU,EAAE;KACb,EACD;MACEL,gBAAgB,EAAE,CAAC;MACnBH,UAAU,EAAE,CAAC;MACbI,KAAK,EAAE,GAAG;MACVC,UAAU,EAAE,IAAI,CAACxB,UAAU;MAC3ByB,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE,CAAC;MAChCC,UAAU,EAAE;KACb,CACF;EACH;EAEA;EACAZ,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACT,aAAa,CAACsB,IAAI,EAAE,EAAE;MAC9B,IAAI,CAACrB,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACJ,SAAS,CAAC;IAC9C,CAAC,MAAM;MACL,MAAM0B,OAAO,GAAG,IAAI,CAACvB,aAAa,CAACwB,WAAW,EAAE;MAChD,IAAI,CAACvB,iBAAiB,GAAG,IAAI,CAACJ,SAAS,CAAC4B,MAAM,CAACC,QAAQ,IACrDA,QAAQ,CAACZ,YAAY,CAACU,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACpDG,QAAQ,CAACX,WAAW,IAAIW,QAAQ,CAACX,WAAW,CAACS,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAE,CAC/E;IACH;EACF;EAEA;EACAK,QAAQA,CAAA;IACN,IAAI,CAACnB,uBAAuB,EAAE;EAChC;EAEA;EACAoB,WAAWA,CAAA;IACT,IAAI,CAAC7B,aAAa,GAAG,EAAE;IACvB,IAAI,CAACS,uBAAuB,EAAE;EAChC;EAIA;EACAqB,aAAaA,CAAA;IACX,IAAI,CAAC5B,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;IACD;IACA,IAAI,CAACb,aAAa,CAACsC,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;EAC3D;EAEA;EACAC,iBAAiBA,CAAA;IACf,IAAI,CAAChC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE;KAChB;EACH;EAEA;EACA6B,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAChC,WAAW,CAACC,IAAI,CAACkB,IAAI,EAAE,EAAE;MACjCc,KAAK,CAAC,SAAS,CAAC;MAChB;IACF;IAEA,MAAM9B,aAAa,GAAG,IAAI,CAACb,aAAa,CAACgC,MAAM,CAACO,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC;IACtE,IAAI3B,aAAa,CAAC+B,MAAM,KAAK,CAAC,EAAE;MAC9BD,KAAK,CAAC,WAAW,CAAC;MAClB;IACF;IAEA;IACA,IAAI,CAACE,cAAc,CAAC,IAAI,CAACnC,WAAW,CAACC,IAAI,EAAE,IAAI,CAACD,WAAW,CAACE,WAAW,EAAEC,aAAa,CAAC;EACzF;EAEA;EACAgC,cAAcA,CAAClC,IAAY,EAAEC,WAAmB,EAAEC,aAAoB;IACpE;IACAK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1B2B,YAAY,EAAEnC,IAAI;MAClBC,WAAW,EAAEA,WAAW;MACxBX,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BY,aAAa,EAAEA,aAAa,CAACkC,GAAG,CAACR,IAAI,KAAK;QACxCS,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACV,IAAI,CAAC;QAC1BW,SAAS,EAAE,IAAI,CAACvB,YAAY,CAACY,IAAI,CAAC;QAClCY,UAAU,EAAE,IAAI,CAACC,aAAa,CAACb,IAAI;OACpC,CAAC;KACH,CAAC;IAEF;IACA,MAAMc,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACnD,SAAS,CAAC2C,GAAG,CAACS,CAAC,IAAIA,CAAC,CAACpC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAE5E;IACA,MAAMV,WAAW,GAAa;MAC5BU,UAAU,EAAEiC,KAAK;MACjBhC,YAAY,EAAEV,IAAI,CAACkB,IAAI,EAAE;MACzBP,WAAW,EAAEV,WAAW,CAACiB,IAAI;KAC9B;IAED;IACA,IAAI,CAACzB,SAAS,CAACqD,IAAI,CAAC/C,WAAW,CAAC;IAEhC;IACAG,aAAa,CAACyB,OAAO,CAAC,CAACC,IAAI,EAAEmB,KAAK,KAAI;MACpC,MAAMC,MAAM,GAAmB;QAC7BpC,gBAAgB,EAAE,IAAI,CAAClB,eAAe,CAACuC,MAAM,GAAGc,KAAK,GAAG,CAAC;QACzDtC,UAAU,EAAEiC,KAAK;QACjB7B,KAAK,EAAE,IAAI,CAACyB,QAAQ,CAACV,IAAI,CAAC;QAAE;QAC5Bd,UAAU,EAAE,IAAI,CAACxB,UAAU;QAAE;QAC7ByB,SAAS,EAAE,IAAI,CAACC,YAAY,CAACY,IAAI,CAAC;QAAE;QACpCX,UAAU,EAAE,IAAI,CAACwB,aAAa,CAACb,IAAI,CAAC,CAAC;OACtC;MACD,IAAI,CAAClC,eAAe,CAACoD,IAAI,CAACE,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3C,uBAAuB,EAAE;IAE9B;IACA,IAAI,CAACP,WAAW,GAAG,KAAK;IACxBkC,KAAK,CAAC,OAAOhC,IAAI,cAAcE,aAAa,CAAC+B,MAAM,MAAM,CAAC;EAC5D;EAEA;EACQK,QAAQA,CAACV,IAAS;IACxB,OAAOA,IAAI,CAACqB,cAAc,IAAIrB,IAAI,CAACsB,EAAE,IAAItB,IAAI,CAACuB,EAAE,IAAI,CAAC;EACvD;EAEA;EACQnC,YAAYA,CAACoC,KAAW;IAC9B;IACA,QAAQ,IAAI,CAAC9D,UAAU;MACrB,KAAK,aAAa;QAChB,OAAO,cAAc;MACvB;QACE,OAAO,MAAM;IACjB;EACF;EAEA;EACQmD,aAAaA,CAACb,IAAS;IAC7B,OAAOA,IAAI,CAACyB,YAAY,IAAIzB,IAAI,CAAC5B,IAAI,IAAI4B,IAAI,CAAC0B,KAAK,IAAI,EAAE;EAC3D;EAEA;EACAC,gBAAgBA,CAACjC,QAAkB;IACjC,IAAI,CAAC3B,gBAAgB,GAAG2B,QAAQ;IAChC,IAAI,CAAC/B,cAAc,CAACiE,IAAI,CAAClC,QAAQ,CAAC;EACpC;EAEA;EACAmC,OAAOA,CAAA;IACL,IAAI,CAACjE,KAAK,CAACgE,IAAI,EAAE;EACnB;EAEA;EACAE,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB;MACA,IAAI,CAACC,kBAAkB,CAACF,UAAU,CAAC;IACrC;EACF;EAEA;EACAE,kBAAkBA,CAACF,UAAkB;IACnC;IACApD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1BmD,UAAU,EAAEA,UAAU;MACtBrE,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,CAACG,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC4B,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAACpC,UAAU,KAAKkD,UAAU,CAAC;IACxE,IAAI,CAACjE,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC2B,MAAM,CAACyC,CAAC,IAAIA,CAAC,CAACrD,UAAU,KAAKkD,UAAU,CAAC;IACpF,IAAI,CAACtD,uBAAuB,EAAE;IAE9B;IACA,IAAI,IAAI,CAACV,gBAAgB,EAAEc,UAAU,KAAKkD,UAAU,EAAE;MACpD,IAAI,CAAChE,gBAAgB,GAAG,IAAI;IAC9B;IAEAqC,KAAK,CAAC,OAAO,CAAC;EAChB;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA;EACA+B,mBAAmBA,CAAA;IACjB,IAAI,CAACpE,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAIqE,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACrE,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACD,eAAe,CAAC2B,MAAM,CAACyC,CAAC,IAAIA,CAAC,CAACrD,UAAU,KAAK,IAAI,CAACd,gBAAiB,CAACc,UAAU,CAAC;EAC7F;EAEA;EACAwD,iBAAiBA,CAAClB,KAAa,EAAEzB,QAAkB;IACjD,OAAOA,QAAQ,CAACb,UAAU,IAAIsC,KAAK;EACrC;CACD;AAnSUmB,UAAA,EAARrF,KAAK,EAAE,C,6DAA2B;AAC1BqF,UAAA,EAARrF,KAAK,EAAE,C,0DAAoC;AAClCqF,UAAA,EAATpF,MAAM,EAAE,C,8DAA+C;AAC9CoF,UAAA,EAATpF,MAAM,EAAE,C,qDAAkC;AAJhCK,uBAAuB,GAAA+E,UAAA,EAPnCvF,SAAS,CAAC;EACTwF,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxF,YAAY,EAAEC,WAAW,EAAEC,YAAY,EAAEC,cAAc;CAClE,CAAC,C,EACWC,uBAAuB,CAoSnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}