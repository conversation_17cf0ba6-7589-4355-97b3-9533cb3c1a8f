{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Base64ImagePipe {\n  transform(value, imageType = 'png') {\n    if (!value) {\n      return '';\n    }\n    // 如果已經包含前綴，直接返回\n    if (value.startsWith('data:image/')) {\n      return value;\n    }\n    // 如果是 HTTP/HTTPS URL，直接返回\n    if (value.startsWith('http://') || value.startsWith('https://')) {\n      return value;\n    }\n    // 添加 Base64 前綴\n    return `data:image/${imageType};base64,${value}`;\n  }\n  static {\n    this.ɵfac = function Base64ImagePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Base64ImagePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"base64Image\",\n      type: Base64ImagePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["Base64ImagePipe", "transform", "value", "imageType", "startsWith", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\base64-image.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'base64Image',\r\n  standalone: true\r\n})\r\nexport class Base64ImagePipe implements PipeTransform {\r\n  transform(value: string | null | undefined, imageType: string = 'png'): string {\r\n    if (!value) {\r\n      return '';\r\n    }\r\n\r\n    // 如果已經包含前綴，直接返回\r\n    if (value.startsWith('data:image/')) {\r\n      return value;\r\n    }\r\n\r\n    // 如果是 HTTP/HTTPS URL，直接返回\r\n    if (value.startsWith('http://') || value.startsWith('https://')) {\r\n      return value;\r\n    }\r\n\r\n    // 添加 Base64 前綴\r\n    return `data:image/${imageType};base64,${value}`;\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,eAAe;EAC1BC,SAASA,CAACC,KAAgC,EAAEC,SAAA,GAAoB,KAAK;IACnE,IAAI,CAACD,KAAK,EAAE;MACV,OAAO,EAAE;IACX;IAEA;IACA,IAAIA,KAAK,CAACE,UAAU,CAAC,aAAa,CAAC,EAAE;MACnC,OAAOF,KAAK;IACd;IAEA;IACA,IAAIA,KAAK,CAACE,UAAU,CAAC,SAAS,CAAC,IAAIF,KAAK,CAACE,UAAU,CAAC,UAAU,CAAC,EAAE;MAC/D,OAAOF,KAAK;IACd;IAEA;IACA,OAAO,cAAcC,SAAS,WAAWD,KAAK,EAAE;EAClD;;;uCAlBWF,eAAe;IAAA;EAAA;;;;YAAfA,eAAe;MAAAK,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}