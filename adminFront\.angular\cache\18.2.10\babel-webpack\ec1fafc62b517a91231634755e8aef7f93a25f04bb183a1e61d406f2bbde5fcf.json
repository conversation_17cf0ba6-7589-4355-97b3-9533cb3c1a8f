{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"primeng/calendar\";\nimport * as i10 from \"../../../@theme/pipes/specialChangeSource.pipe\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = () => [];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_27_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 17)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"specialChangeSource\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 18);\n    i0.ɵɵtemplate(13, CustomerChangePictureComponent_tr_27_button_13_Template, 2, 0, \"button\", 19);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 6, item_r6.CSource));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ref_r8 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r8));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 21)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 22)(4, \"div\", 23)(5, \"label\", 24, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 25);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"label\", 26);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 23)(14, \"label\", 28);\n    i0.ɵɵtext(15, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"app-file-upload\", 29);\n    i0.ɵɵlistener(\"multiFileSelected\", function CustomerChangePictureComponent_ng_template_34_Template_app_file_upload_multiFileSelected_16_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onMultiFileSelected($event));\n    })(\"nameAutoFilled\", function CustomerChangePictureComponent_ng_template_34_Template_app_file_upload_nameAutoFilled_16_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onNameAutoFilled($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 30)(18, \"label\", 31);\n    i0.ɵɵtext(19, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"textarea\", 32);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_34_Template_textarea_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_34_Template_button_click_22_listener() {\n      const ref_r8 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r8));\n    });\n    i0.ɵɵtext(23, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, CustomerChangePictureComponent_ng_template_34_button_24_Template, 2, 0, \"button\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"config\", ctx_r2.fileUploadConfig)(\"fileList\", ctx_r2.imageUrlList)(\"existingFiles\", ctx_r2.isEdit && ctx_r2.SpecialChange && ctx_r2.SpecialChange.CFileRes ? ctx_r2.SpecialChange.CFileRes : i0.ɵɵpureFunction0(16, _c1));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.fileUploadConfig = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\n      label: '',\n      helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\n      required: false,\n      disabled: false,\n      autoFillName: true,\n      buttonText: '選擇檔案',\n      buttonIcon: 'fa fa-upload',\n      maxFileSize: 10,\n      multiple: true,\n      showPreview: true\n    };\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n    // 動態更新配置\n    this.updateFileUploadConfig();\n  }\n  updateFileUploadConfig() {\n    this.fileUploadConfig = {\n      ...this.fileUploadConfig,\n      disabled: this.isEdit && this.SpecialChange?.CIsApprove === null\n    };\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.updateFileUploadConfig();\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.updateFileUploadConfig();\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  onMultiFileSelected(files) {\n    this.imageUrlList = files;\n  }\n  onNameAutoFilled(name) {\n    if (!this.formSpecialChange.CDrawingName) {\n      this.formSpecialChange.CDrawingName = name;\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n  static {\n    this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerChangePictureComponent,\n      selectors: [[\"ngx-customer-change-picture\"]],\n      viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 36,\n      vars: 6,\n      consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"labelMinWidth\", \"0px\", 3, \"multiFileSelected\", \"nameAutoFilled\", \"config\", \"fileList\", \"existingFiles\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function CustomerChangePictureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"table\", 9)(12, \"thead\")(13, \"tr\", 10)(14, \"th\", 11);\n          i0.ɵɵtext(15, \"\\u4F86\\u6E90\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"th\", 11);\n          i0.ɵɵtext(17, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"th\", 11);\n          i0.ɵɵtext(19, \"\\u5716\\u9762\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"th\", 11);\n          i0.ɵɵtext(21, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 11);\n          i0.ɵɵtext(23, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 11);\n          i0.ɵɵtext(25, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"tbody\");\n          i0.ɵɵtemplate(27, CustomerChangePictureComponent_tr_27_Template, 14, 8, \"tr\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"nb-card-footer\", 13)(29, \"ngb-pagination\", 14);\n          i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"nb-card-footer\")(31, \"div\", 13)(32, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_32_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(33, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(34, CustomerChangePictureComponent_ng_template_34_Template, 25, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u6D3D\\u8AC7\\u7D00\\u9304\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i9.Calendar, i10.SpecialChangeSourcePipe],\n      styles: [\"#icondisplay {\\n  width: 318px;\\n}\\n\\n  [id^=pn_id_] {\\n  z-index: 10;\\n}\\n\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .file-type-label[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(4px);\\n          backdrop-filter: blur(4px);\\n  background-color: rgba(0, 0, 0, 0.7);\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out;\\n}\\n.file-type-container[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.pdf-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n.cad-icon[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.cad-icon[_ngcontent-%COMP%]:hover {\\n  transform: rotate(15deg);\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksWUFBQTtBQUNKOztBQUVBO0VBQ0ksV0FBQTtBQUNKOztBQUlJO0VBQ0ksZ0NBQUE7QUFEUjtBQUdRO0VBQ0ksMkJBQUE7QUFEWjtBQUtJO0VBQ0ksa0NBQUE7VUFBQSwwQkFBQTtFQUNBLG9DQUFBO0FBSFI7QUFNSTtFQUNJLGdDQUFBO0FBSlI7QUFNUTtFQUNJLHFCQUFBO0FBSlo7O0FBVUE7RUFDSSw0QkFBQTtBQVBKOztBQVVBO0VBRUk7SUFFSSxVQUFBO0VBVE47RUFZRTtJQUNJLFlBQUE7RUFWTjtBQUNGO0FBY0E7RUFDSSwrQkFBQTtBQVpKO0FBY0k7RUFDSSx3QkFBQTtBQVpSIiwiZmlsZSI6ImN1c3RvbWVyLWNoYW5nZS1waWN0dXJlLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwICNpY29uZGlzcGxheSB7XHJcbiAgICB3aWR0aDogMzE4cHg7XHJcbn1cclxuXHJcbjo6bmctZGVlcCBbaWRePVwicG5faWRfXCJdIHtcclxuICAgIHotaW5kZXg6IDEwO1xyXG59XHJcblxyXG4vLyDmqpTmoYjpoZ7lnovpoa/npLrmqKPlvI9cclxuLmZpbGUtdHlwZS1jb250YWluZXIge1xyXG4gICAgLmZpbGUtaXRlbSB7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5maWxlLXR5cGUtbGFiZWwge1xyXG4gICAgICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cig0cHgpO1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC43KTtcclxuICAgIH1cclxuXHJcbiAgICAucmVtb3ZlLWJ0biB7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbi1vdXQ7XHJcblxyXG4gICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4vLyBQREbmlofku7blnJbmqJnli5XnlatcclxuLnBkZi1pY29uIHtcclxuICAgIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XHJcbn1cclxuXHJcbkBrZXlmcmFtZXMgcHVsc2Uge1xyXG5cclxuICAgIDAlLFxyXG4gICAgMTAwJSB7XHJcbiAgICAgICAgb3BhY2l0eTogMTtcclxuICAgIH1cclxuXHJcbiAgICA1MCUge1xyXG4gICAgICAgIG9wYWNpdHk6IDAuNztcclxuICAgIH1cclxufVxyXG5cclxuLy8gQ0FE5paH5Lu25ZyW5qiZ5peL6L2J5pWI5p6cXHJcbi5jYWQtaWNvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDE1ZGVnKTtcclxuICAgIH1cclxufVxyXG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUvY3VzdG9tZXItY2hhbmdlLXBpY3R1cmUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxZQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0FBQ0o7O0FBSUk7RUFDSSxnQ0FBQTtBQURSO0FBR1E7RUFDSSwyQkFBQTtBQURaO0FBS0k7RUFDSSxrQ0FBQTtVQUFBLDBCQUFBO0VBQ0Esb0NBQUE7QUFIUjtBQU1JO0VBQ0ksZ0NBQUE7QUFKUjtBQU1RO0VBQ0kscUJBQUE7QUFKWjs7QUFVQTtFQUNJLDRCQUFBO0FBUEo7O0FBVUE7RUFFSTtJQUVJLFVBQUE7RUFUTjtFQVlFO0lBQ0ksWUFBQTtFQVZOO0FBQ0Y7QUFjQTtFQUNJLCtCQUFBO0FBWko7QUFjSTtFQUNJLHdCQUFBO0FBWlI7QUFFQSxnOURBQWc5RCIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAjaWNvbmRpc3BsYXkge1xyXG4gICAgd2lkdGg6IDMxOHB4O1xyXG59XHJcblxyXG46Om5nLWRlZXAgW2lkXj1cInBuX2lkX1wiXSB7XHJcbiAgICB6LWluZGV4OiAxMDtcclxufVxyXG5cclxuLy8gw6bCqsKUw6bCocKIw6nCocKew6XCnsKLw6nCocKvw6fCpMK6w6bCqMKjw6XCvMKPXHJcbi5maWxlLXR5cGUtY29udGFpbmVyIHtcclxuICAgIC5maWxlLWl0ZW0ge1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuZmlsZS10eXBlLWxhYmVsIHtcclxuICAgICAgICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoNHB4KTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNyk7XHJcbiAgICB9XHJcblxyXG4gICAgLnJlbW92ZS1idG4ge1xyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW4tb3V0O1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuLy8gUERGw6bClsKHw6TCu8K2w6XCnMKWw6bCqMKZw6XCi8KVw6fClcKrXHJcbi5wZGYtaWNvbiB7XHJcbiAgICBhbmltYXRpb246IHB1bHNlIDJzIGluZmluaXRlO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcclxuXHJcbiAgICAwJSxcclxuICAgIDEwMCUge1xyXG4gICAgICAgIG9wYWNpdHk6IDE7XHJcbiAgICB9XHJcblxyXG4gICAgNTAlIHtcclxuICAgICAgICBvcGFjaXR5OiAwLjc7XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIENBRMOmwpbCh8OkwrvCtsOlwpzClsOmwqjCmcOmwpfCi8Oowr3CicOmwpXCiMOmwp7CnFxyXG4uY2FkLWljb24ge1xyXG4gICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZTtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxNWRlZyk7XHJcbiAgICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["moment", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "CustomerChangePictureComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogUploadDrawing_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "CustomerChangePictureComponent_tr_27_button_13_Template_button_click_0_listener", "_r5", "item_r6", "$implicit", "onEdit", "ɵɵtemplate", "CustomerChangePictureComponent_tr_27_button_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "CSource", "formatDate", "CChangeDate", "CDrawingName", "CCreateDT", "CIsApprove", "ɵɵproperty", "isUpdate", "CustomerChangePictureComponent_ng_template_34_button_24_Template_button_click_0_listener", "_r9", "ref_r8", "dialogRef", "onSaveSpecialChange", "ɵɵtwoWayListener", "CustomerChangePictureComponent_ng_template_34_Template_p_calendar_ngModelChange_8_listener", "$event", "_r7", "ɵɵtwoWayBindingSet", "formSpecialChange", "CustomerChangePictureComponent_ng_template_34_Template_input_ngModelChange_12_listener", "CustomerChangePictureComponent_ng_template_34_Template_app_file_upload_multiFileSelected_16_listener", "onMultiFileSelected", "CustomerChangePictureComponent_ng_template_34_Template_app_file_upload_nameAutoFilled_16_listener", "onNameAutoFilled", "CustomerChangePictureComponent_ng_template_34_Template_textarea_ngModelChange_20_listener", "CApproveRemark", "CustomerChangePictureComponent_ng_template_34_Template_button_click_22_listener", "onClose", "CustomerChangePictureComponent_ng_template_34_button_24_Template", "ɵɵtextInterpolate2", "house", "CHousehold", "CFloor", "ɵɵtwoWayProperty", "isEdit", "fileUploadConfig", "imageUrlList", "SpecialChange", "CFileRes", "ɵɵpureFunction0", "_c1", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "label", "helpText", "required", "disabled", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "statusOptions", "value", "key", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "updateFileUploadConfig", "openPdfInNewTab", "data", "CFile", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "houseTitle", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "CBuildCaseID", "SpecialChangeFiles", "Date", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "specialChange", "files", "name", "clear", "format", "deleteDataFields", "array", "item", "result", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "event", "index", "blob", "slice", "size", "type", "newFile", "File", "target", "extension", "goBack", "push", "action", "payload", "back", "openNewTab", "url", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "SpecialChangeService", "HouseService", "i5", "ActivatedRoute", "i6", "MessageService", "i7", "Location", "i8", "EventService", "selectors", "viewQuery", "CustomerChangePictureComponent_Query", "rf", "ctx", "CustomerChangePictureComponent_button_9_Template", "CustomerChangePictureComponent_tr_27_Template", "CustomerChangePictureComponent_Template_ngb_pagination_pageChange_29_listener", "_r1", "CustomerChangePictureComponent_Template_button_click_32_listener", "CustomerChangePictureComponent_ng_template_34_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate1", "isCreate"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { MultiFileUploadResult, FileUploadConfig } from '../../components/file-upload/file-upload.component';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: MultiFileUploadResult[] = [];\r\n  isEdit = false;\r\n\r\n  fileUploadConfig: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png|dwg|dxf/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf, .dwg, .dxf',\r\n    label: '',\r\n    helpText: '支援格式：圖片 (JPG, JPEG)、PDF、CAD (DWG, DXF)',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: true,\r\n    buttonText: '選擇檔案',\r\n    buttonIcon: 'fa fa-upload',\r\n    maxFileSize: 10,\r\n    multiple: true,\r\n    showPreview: true\r\n  };\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n  override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n\r\n    // 動態更新配置\r\n    this.updateFileUploadConfig();\r\n  }\r\n\r\n  updateFileUploadConfig() {\r\n    this.fileUploadConfig = {\r\n      ...this.fileUploadConfig,\r\n      disabled: this.isEdit && this.SpecialChange?.CIsApprove === null\r\n    };\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false;\r\n    this.updateFileUploadConfig();\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref);\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true;\r\n    this.updateFileUploadConfig();\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\r\n  }\r\n\r\n  onMultiFileSelected(files: MultiFileUploadResult[]) {\r\n    this.imageUrlList = files;\r\n  }\r\n\r\n  onNameAutoFilled(name: string) {\r\n    if (!this.formSpecialChange.CDrawingName) {\r\n      this.formSpecialChange.CDrawingName = name;\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    戶別管理 > 洽談紀錄上傳 > {{houseTitle}}\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此上傳與該戶別客戶討論的客戶圖面，審核通過後客戶就可以在前台檢視該圖面。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialogUploadDrawing)\" *ngIf=\"isCreate\">\r\n            上傳圖面</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\" class=\"text-center\">\r\n            <th scope=\"col\" class=\"col-1\">來源</th>\r\n            <th scope=\"col\" class=\"col-1\">討論日期</th>\r\n            <th scope=\"col\" class=\"col-1\">圖面名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">上傳日期</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of listSpecialChange ; let i = index\" class=\"text-center\">\r\n            <td>{{ item.CSource| specialChangeSource }}</td>\r\n            <td>{{ formatDate(item.CChangeDate)}}</td>\r\n            <td>{{ item.CDrawingName}}</td>\r\n            <td>{{formatDate(item.CCreateDT)}}</td>\r\n            <td>{{ item.CIsApprove == null ? '待審核' : ( item.CIsApprove ? \"通過\" : \"駁回\")}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isUpdate\"\r\n                (click)=\"onEdit(dialogUploadDrawing, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\" (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialogUploadDrawing let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"min-width:600px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 > 洽談紀錄上傳 > {{house.CHousehold}} &nbsp; {{house.CFloor}}F\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"CChangeDate\" #CChangeDate class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          討論日期\r\n        </label>\r\n        <p-calendar [appendTo]=\"'CChangeDate'\" placeholder=\"年/月/日\" [iconDisplay]=\"'input'\" [showIcon]=\"true\"\r\n          inputId=\"icondisplay\" dateFormat=\"yy/mm/dd\" [(ngModel)]=\"formSpecialChange.CChangeDate\" [disabled]=\"isEdit\"\r\n          [showButtonBar]=\"true\" class=\"!w-[400px]\"></p-calendar>\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cDrawingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          圖面名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"圖面名稱\" [(ngModel)]=\"formSpecialChange.CDrawingName\"\r\n          [disabled]=\"isEdit\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\" mr-4\" style=\"min-width:75px\" baseLabel>\r\n          選樣結果\r\n        </label>\r\n        <app-file-upload [config]=\"fileUploadConfig\" [fileList]=\"imageUrlList\"\r\n          [existingFiles]=\"isEdit && SpecialChange && SpecialChange.CFileRes ? SpecialChange.CFileRes : []\"\r\n          (multiFileSelected)=\"onMultiFileSelected($event)\" (nameAutoFilled)=\"onNameAutoFilled($event)\"\r\n          labelMinWidth=\"0px\">\r\n        </app-file-upload>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cApproveRemark\" baseLabel class=\"required-field align-self-start mr-4\"\r\n          style=\"min-width:75px\">審核說明</label>\r\n        <textarea name=\"remark\" id=\"cApproveRemark\" rows=\"5\" nbInput style=\"resize: none;\" class=\"w-full\"\r\n          [disabled]=\"isEdit\" class=\"w-full\" [(ngModel)]=\"formSpecialChange.CApproveRemark\"></textarea>\r\n      </div>\r\n\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <button class=\"btn btn-success m-2\" *ngIf=\"!isEdit\" (click)=\"onSaveSpecialChange(ref)\">送出審核</button>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AASA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;ICAlEC,EAAA,CAAAC,cAAA,iBAAoF;IAAvDD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,sBAAA,CAA2B;IAAA,EAAC;IAChER,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAyBXb,EAAA,CAAAC,cAAA,iBAC8C;IAA5CD,EAAA,CAAAE,UAAA,mBAAAY,gFAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAW,GAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAO,aAAA,GAAAU,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,sBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAY,MAAA,CAAAV,sBAAA,EAAAQ,OAAA,CAAiC;IAAA,EAAC;IAAChB,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;IAP3Db,EADF,CAAAC,cAAA,aAA+E,SACzE;IAAAD,EAAA,CAAAY,MAAA,GAAuC;;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChDb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAiC;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC1Cb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAAsB;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAC/Bb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAY,MAAA,GAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAY,MAAA,IAAuE;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IAChFb,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAAC,uDAAA,qBAC8C;IAElDpB,EADE,CAAAa,YAAA,EAAK,EACF;;;;;IATCb,EAAA,CAAAqB,SAAA,GAAuC;IAAvCrB,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAuB,WAAA,OAAAP,OAAA,CAAAQ,OAAA,EAAuC;IACvCxB,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAU,WAAA,EAAiC;IACjC1B,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAW,YAAA,CAAsB;IACtB3B,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,MAAA,CAAAmB,UAAA,CAAAT,OAAA,CAAAY,SAAA,EAA8B;IAC9B5B,EAAA,CAAAqB,SAAA,GAAuE;IAAvErB,EAAA,CAAAsB,iBAAA,CAAAN,OAAA,CAAAa,UAAA,kCAAAb,OAAA,CAAAa,UAAA,mCAAuE;IAErB7B,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyB,QAAA,CAAc;;;;;;IAgExE/B,EAAA,CAAAC,cAAA,iBAAuF;IAAnCD,EAAA,CAAAE,UAAA,mBAAA8B,yFAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA;MAAA,MAAAC,MAAA,GAAAlC,EAAA,CAAAO,aAAA,GAAA4B,SAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA8B,mBAAA,CAAAF,MAAA,CAAwB;IAAA,EAAC;IAAClC,EAAA,CAAAY,MAAA,+BAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAS;;;;;;IAvCxGb,EADF,CAAAC,cAAA,kBAAmD,qBACjC;IACdD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAa,YAAA,EAAiB;IAIbb,EAHJ,CAAAC,cAAA,uBAA2B,cAED,mBAC6E;IACjGD,EAAA,CAAAY,MAAA,iCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,qBAE4C;IADED,EAAA,CAAAqC,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAAoC,iBAAA,CAAAhB,WAAA,EAAAa,MAAA,MAAAjC,MAAA,CAAAoC,iBAAA,CAAAhB,WAAA,GAAAa,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAA2C;IAE3FvC,EAD8C,CAAAa,YAAA,EAAa,EACrD;IAEJb,EADF,CAAAC,cAAA,cAAwB,iBACiE;IACrFD,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,iBACwB;IADqCD,EAAA,CAAAqC,gBAAA,2BAAAM,uFAAAJ,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAAoC,iBAAA,CAAAf,YAAA,EAAAY,MAAA,MAAAjC,MAAA,CAAAoC,iBAAA,CAAAf,YAAA,GAAAY,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAA4C;IAE3GvC,EAFE,CAAAa,YAAA,EACwB,EACpB;IAEJb,EADF,CAAAC,cAAA,eAAwB,iBACgD;IACpED,EAAA,CAAAY,MAAA,kCACF;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACRb,EAAA,CAAAC,cAAA,2BAGsB;IAD8BD,EAAlD,CAAAE,UAAA,+BAAA0C,qGAAAL,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAqBJ,MAAA,CAAAuC,mBAAA,CAAAN,MAAA,CAA2B;IAAA,EAAC,4BAAAO,kGAAAP,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAmBJ,MAAA,CAAAyC,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAGjGvC,EADE,CAAAa,YAAA,EAAkB,EACd;IAEJb,EADF,CAAAC,cAAA,eAAkD,iBAEvB;IAAAD,EAAA,CAAAY,MAAA,gCAAI;IAAAZ,EAAA,CAAAa,YAAA,EAAQ;IACrCb,EAAA,CAAAC,cAAA,oBACoF;IAA/CD,EAAA,CAAAqC,gBAAA,2BAAAW,0FAAAT,MAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAyC,kBAAA,CAAAnC,MAAA,CAAAoC,iBAAA,CAAAO,cAAA,EAAAV,MAAA,MAAAjC,MAAA,CAAAoC,iBAAA,CAAAO,cAAA,GAAAV,MAAA;MAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;IAAA,EAA8C;IACrFvC,EADsF,CAAAa,YAAA,EAAW,EAC3F;IAGJb,EADF,CAAAC,cAAA,eAA2C,kBAC4B;IAAvBD,EAAA,CAAAE,UAAA,mBAAAgD,gFAAA;MAAA,MAAAhB,MAAA,GAAAlC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAL,SAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA6C,OAAA,CAAAjB,MAAA,CAAY;IAAA,EAAC;IAAClC,EAAA,CAAAY,MAAA,oBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChFb,EAAA,CAAAmB,UAAA,KAAAiC,gEAAA,qBAAuF;IAG7FpD,EAFI,CAAAa,YAAA,EAAM,EACO,EACP;;;;IAzCNb,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAqD,kBAAA,wEAAA/C,MAAA,CAAAgD,KAAA,CAAAC,UAAA,cAAAjD,MAAA,CAAAgD,KAAA,CAAAE,MAAA,OACF;IAOgBxD,EAAA,CAAAqB,SAAA,GAA0B;IAA6CrB,EAAvE,CAAA8B,UAAA,2BAA0B,wBAA4C,kBAAkB;IACtD9B,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAoC,iBAAA,CAAAhB,WAAA,CAA2C;IACvF1B,EADwF,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,MAAA,CAAmB,uBACrF;IAMqC1D,EAAA,CAAAqB,SAAA,GAA4C;IAA5CrB,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAoC,iBAAA,CAAAf,YAAA,CAA4C;IACvG3B,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,MAAA,CAAmB;IAMJ1D,EAAA,CAAAqB,SAAA,GAA2B;IAC1CrB,EADe,CAAA8B,UAAA,WAAAxB,MAAA,CAAAqD,gBAAA,CAA2B,aAAArD,MAAA,CAAAsD,YAAA,CAA0B,kBAAAtD,MAAA,CAAAoD,MAAA,IAAApD,MAAA,CAAAuD,aAAA,IAAAvD,MAAA,CAAAuD,aAAA,CAAAC,QAAA,GAAAxD,MAAA,CAAAuD,aAAA,CAAAC,QAAA,GAAA9D,EAAA,CAAA+D,eAAA,KAAAC,GAAA,EAC6B;IASjGhE,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAA8B,UAAA,aAAAxB,MAAA,CAAAoD,MAAA,CAAmB;IAAgB1D,EAAA,CAAAyD,gBAAA,YAAAnD,MAAA,CAAAoC,iBAAA,CAAAO,cAAA,CAA8C;IAK9CjD,EAAA,CAAAqB,SAAA,GAAa;IAAbrB,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAoD,MAAA,CAAa;;;AD3E1D,OAAM,MAAOO,8BAA+B,SAAQnE,aAAa;EAC/DoE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAGvB,KAAAf,YAAY,GAA4B,EAAE;IAC1C,KAAAF,MAAM,GAAG,KAAK;IAEd,KAAAC,gBAAgB,GAAqB;MACnCiB,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAC1IC,iBAAiB,EAAE,2BAA2B;MAC9CC,eAAe,EAAE,oDAAoD;MACrEC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,wCAAwC;MAClDC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,cAAc;MAC1BC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;KACd;IACD,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZZ,KAAK,EAAE;KACR,EACD;MACEW,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBZ,KAAK,EAAE;KACR,CACF;IAEQ,KAAAa,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IA8LzB,KAAAC,YAAY,GAAU,EAAE;EAlON;EA4CTC,QAAQA,CAAA;IACf,IAAI,CAACzB,KAAK,CAAC0B,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACnD,gBAAgB,GAAG;MACtB,GAAG,IAAI,CAACA,gBAAgB;MACxBuB,QAAQ,EAAE,IAAI,CAACxB,MAAM,IAAI,IAAI,CAACG,aAAa,EAAEhC,UAAU,KAAK;KAC7D;EACH;EAGAkF,eAAeA,CAACC,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAAClD,QAAQ,CAACmD,KAAK,EAAEC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAClD,QAAQ,CAACmD,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAL,oBAAoBA,CAAA;IAClB,IAAI,CAACtC,qBAAqB,CAAC8C,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACX,OAAO;QACtBY,SAAS,EAAE,IAAI,CAACzB,SAAS;QACzB0B,QAAQ,EAAE,IAAI,CAAC3B;;KAElB,CAAC,CAACM,SAAS,CAACsB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAAC5B,YAAY,GAAG0B,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAb,YAAYA,CAAA;IACV,IAAI,CAACtC,aAAa,CAACuD,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACpB;;KAElB,CAAC,CAACR,SAAS,CAACsB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACtE,KAAK,GAAGmE,GAAG,CAACE,OAAO;QACxB,IAAI,CAACK,UAAU,GAAG,GAAG,IAAI,CAAC1E,KAAK,CAACC,UAAU,IAAI,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAyE,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAAC7D,qBAAqB,CAAC8D,6CAA6C,CAAC;MAAEf,IAAI,EAAEc;IAAgB,CAAE,CAAC,CAAChC,SAAS,CAACsB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAAC/D,aAAa,GAAG4D,GAAG,CAACE,OAAO;QAChC,IAAI,CAACjF,iBAAiB,GAAG;UACvBO,cAAc,EAAE,IAAI,CAACY,aAAa,CAACZ,cAAc;UACjDoF,YAAY,EAAE,IAAI,CAAC7B,WAAW;UAC9B7E,YAAY,EAAE,IAAI,CAACkC,aAAa,CAAClC,YAAY;UAC7CoG,QAAQ,EAAE,IAAI,CAACpB,OAAO;UACtB2B,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAACzE,aAAa,CAACnC,WAAW,EAAE;UAClC,IAAI,CAACgB,iBAAiB,CAAChB,WAAW,GAAG,IAAI6G,IAAI,CAAC,IAAI,CAAC1E,aAAa,CAACnC,WAAW,CAAC;QAC/E;QACA,IAAI,CAAC0C,aAAa,CAAC+C,IAAI,CAACe,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIA9F,mBAAmBA,CAAC8F,GAAQ;IAC1B,IAAI,CAACM,UAAU,EAAE;IACjB,IAAI,IAAI,CAACnE,KAAK,CAACoE,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACjE,OAAO,CAACkE,aAAa,CAAC,IAAI,CAACtE,KAAK,CAACoE,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACnE,qBAAqB,CAACsE,0CAA0C,CAAC;MAAEvB,IAAI,EAAE,IAAI,CAACwB,WAAW;IAAE,CAAE,CAAC,CAAC1C,SAAS,CAACsB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACnD,OAAO,CAACqE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAClC,oBAAoB,EAAE;QAC3BsB,GAAG,CAACa,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACnD,SAAS,GAAGmD,OAAO;IACxB,IAAI,CAACrC,oBAAoB,EAAE;EAC7B;EAEAjG,MAAMA,CAACuH,GAAQ;IACb,IAAI,CAACtE,YAAY,GAAG,EAAE;IACtB,IAAI,CAACF,MAAM,GAAG,KAAK;IACnB,IAAI,CAACoD,sBAAsB,EAAE;IAC7B,IAAI,CAACpE,iBAAiB,GAAG;MACvBO,cAAc,EAAE,EAAE;MAClBoF,YAAY,EAAE,IAAI,CAAC7B,WAAW;MAC9B9E,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBoG,QAAQ,EAAE,IAAI,CAACpB,OAAO;MACtB2B,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAClE,aAAa,CAAC+C,IAAI,CAACe,GAAG,CAAC;EAC9B;EAEAhH,MAAMA,CAACgH,GAAQ,EAAEgB,aAAkB;IACjC,IAAI,CAACtF,YAAY,GAAG,EAAE;IACtB,IAAI,CAACF,MAAM,GAAG,IAAI;IAClB,IAAI,CAACoD,sBAAsB,EAAE;IAC7B,IAAI,CAACmB,oBAAoB,CAACC,GAAG,EAAEgB,aAAa,CAACf,gBAAgB,CAAC;EAChE;EAEAtF,mBAAmBA,CAACsG,KAA8B;IAChD,IAAI,CAACvF,YAAY,GAAGuF,KAAK;EAC3B;EAEApG,gBAAgBA,CAACqG,IAAY;IAC3B,IAAI,CAAC,IAAI,CAAC1G,iBAAiB,CAACf,YAAY,EAAE;MACxC,IAAI,CAACe,iBAAiB,CAACf,YAAY,GAAGyH,IAAI;IAC5C;EACF;EAEAZ,UAAUA,CAAA;IACR,IAAI,CAACnE,KAAK,CAACgF,KAAK,EAAE;IAClB,IAAI,CAAChF,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAAChB,WAAW,CAAC;IACjE,IAAI,CAAC2C,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAACf,YAAY,CAAC;IAClE,IAAI,CAAC0C,KAAK,CAACY,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACvC,iBAAiB,CAACO,cAAc,CAAC;EACtE;EAEAxB,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7B,MAAM,CAAC6B,WAAW,CAAC,CAAC4H,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAACzC,IAAI;IAClB;IACA,OAAOwC,KAAK;EACd;EAEAX,WAAWA,CAAA;IACT,MAAMa,MAAM,GAAG;MACb,GAAG,IAAI,CAAChH,iBAAiB;MACzB4F,kBAAkB,EAAE,IAAI,CAAC1E;KAC1B;IACD,IAAI,CAAC2F,gBAAgB,CAACG,MAAM,CAACpB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAAC5F,iBAAiB,CAAChB,WAAW,EAAE;MACtCgI,MAAM,CAAChI,WAAW,GAAG,IAAI,CAACD,UAAU,CAAC,IAAI,CAACiB,iBAAiB,CAAChB,WAAW,CAAC;IAC1E;IACA,OAAOgI,MAAM;EACf;EAEAvG,OAAOA,CAAC+E,GAAQ;IACdA,GAAG,CAACa,KAAK,EAAE;EACb;EACAY,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAIAI,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAACjE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,EAAE,IAAI0D,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAAClC,GAAQ,GACpB;EAEAmC,UAAUA,CAACC,KAAU,EAAEC,KAAa;IAClC,IAAIC,IAAI,GAAG,IAAI,CAACxE,YAAY,CAACuE,KAAK,CAAC,CAACtD,KAAK,CAACwD,KAAK,CAAC,CAAC,EAAE,IAAI,CAACzE,YAAY,CAACuE,KAAK,CAAC,CAACtD,KAAK,CAACyD,IAAI,EAAE,IAAI,CAAC1E,YAAY,CAACuE,KAAK,CAAC,CAACtD,KAAK,CAAC0D,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGF,KAAK,CAACQ,MAAM,CAACpF,KAAK,GAAG,GAAG,GAAG,IAAI,CAACM,YAAY,CAACuE,KAAK,CAAC,CAACQ,SAAS,EAAE,EAAE;MAAEJ,IAAI,EAAE,IAAI,CAAC3E,YAAY,CAACuE,KAAK,CAAC,CAACtD,KAAK,CAAC0D;IAAI,CAAE,CAAC;IACjJ,IAAI,CAAC3E,YAAY,CAACuE,KAAK,CAAC,CAACtD,KAAK,GAAG2D,OAAO;EAC1C;EAEAI,MAAMA,CAAA;IACJ,IAAI,CAACrG,aAAa,CAACsG,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3E;KACf,CAAC;IACF,IAAI,CAAC9B,QAAQ,CAAC0G,IAAI,EAAE;EACtB;EAEAC,UAAUA,CAACC,GAAQ;IACjB,IAAIA,GAAG,EAAEpE,MAAM,CAACC,IAAI,CAACmE,GAAG,EAAE,QAAQ,CAAC;EACrC;;;uCAvQWrH,8BAA8B,EAAAjE,EAAA,CAAAuL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzL,EAAA,CAAAuL,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA3L,EAAA,CAAAuL,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA7L,EAAA,CAAAuL,iBAAA,CAAAO,EAAA,CAAAC,oBAAA,GAAA/L,EAAA,CAAAuL,iBAAA,CAAAO,EAAA,CAAAE,YAAA,GAAAhM,EAAA,CAAAuL,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAlM,EAAA,CAAAuL,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAApM,EAAA,CAAAuL,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAtM,EAAA,CAAAuL,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BvI,8BAA8B;MAAAwI,SAAA;MAAAC,SAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCzBzC5M,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAY,MAAA,iPAAuC;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UAK7Eb,EAHJ,CAAAC,cAAA,aAA8B,aAEL,aAC0B;UAC7CD,EAAA,CAAAmB,UAAA,IAAA2L,gDAAA,oBAAoF;UAI1F9M,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAMEb,EAJR,CAAAC,cAAA,cAAmC,gBAC+D,aACvF,cACoE,cACzC;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACrCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,gCAAI;UAAAZ,EAAA,CAAAa,YAAA,EAAK;UACvCb,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAY,MAAA,oBAAE;UAEpCZ,EAFoC,CAAAa,YAAA,EAAK,EAClC,EACC;UACRb,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAmB,UAAA,KAAA4L,6CAAA,kBAA+E;UAcvF/M,EAHM,CAAAa,YAAA,EAAQ,EACF,EACJ,EACO;UAEbb,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAqC,gBAAA,wBAAA2K,8EAAAzK,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAA6M,GAAA;YAAAjN,EAAA,CAAAyC,kBAAA,CAAAoK,GAAA,CAAA/G,SAAA,EAAAvD,MAAA,MAAAsK,GAAA,CAAA/G,SAAA,GAAAvD,MAAA;YAAA,OAAAvC,EAAA,CAAAU,WAAA,CAAA6B,MAAA;UAAA,EAAoB;UAClCvC,EAAA,CAAAE,UAAA,wBAAA8M,8EAAAzK,MAAA;YAAAvC,EAAA,CAAAI,aAAA,CAAA6M,GAAA;YAAA,OAAAjN,EAAA,CAAAU,WAAA,CAAcmM,GAAA,CAAA7D,WAAA,CAAAzG,MAAA,CAAmB;UAAA,EAAC;UAEtCvC,EADE,CAAAa,YAAA,EAAiB,EACF;UAGbb,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACmB;UAAnBD,EAAA,CAAAE,UAAA,mBAAAgN,iEAAA;YAAAlN,EAAA,CAAAI,aAAA,CAAA6M,GAAA;YAAA,OAAAjN,EAAA,CAAAU,WAAA,CAASmM,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UACzDhL,EAAA,CAAAY,MAAA,wCACF;UAGNZ,EAHM,CAAAa,YAAA,EAAS,EACL,EACS,EACT;UAGVb,EAAA,CAAAmB,UAAA,KAAAgM,sDAAA,kCAAAnN,EAAA,CAAAoN,sBAAA,CAAiE;;;UA1D7DpN,EAAA,CAAAqB,SAAA,GACF;UADErB,EAAA,CAAAqN,kBAAA,wEAAAR,GAAA,CAAA7E,UAAA,MACF;UAQ4EhI,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAA8B,UAAA,SAAA+K,GAAA,CAAAS,QAAA,CAAc;UAmB7DtN,EAAA,CAAAqB,SAAA,IAAuB;UAAvBrB,EAAA,CAAA8B,UAAA,YAAA+K,GAAA,CAAAhF,iBAAA,CAAuB;UAgBlC7H,EAAA,CAAAqB,SAAA,GAAoB;UAApBrB,EAAA,CAAAyD,gBAAA,SAAAoJ,GAAA,CAAA/G,SAAA,CAAoB;UAAuB9F,EAAtB,CAAA8B,UAAA,aAAA+K,GAAA,CAAAhH,QAAA,CAAqB,mBAAAgH,GAAA,CAAA9G,YAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}