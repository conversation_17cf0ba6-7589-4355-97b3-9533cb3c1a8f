{"ast": null, "code": "import { signal } from '@angular/core';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { mergeMap, tap } from 'rxjs';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { CommonModule } from '@angular/common';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport { CalendarModule } from 'primeng/calendar';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/shared/services/event.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@nebular/theme\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"@fullcalendar/angular\";\nimport * as i10 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction AvailableTimeSlotComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.CBuildCaseName, \"\");\n  }\n}\nfunction AvailableTimeSlotComponent_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AvailableTimeSlotComponent_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.searchPreOrderSetting());\n    });\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵtext(2, \"\\u67E5\\u8A62 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailableTimeSlotComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AvailableTimeSlotComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.gotoEdit());\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailableTimeSlotComponent_ng_template_26_Template(rf, ctx) {}\nexport let AvailableTimeSlotComponent = /*#__PURE__*/(() => {\n  class AvailableTimeSlotComponent extends BaseComponent {\n    constructor(_allow, changeDetector, preOderSettingService, buildCaseService, router, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.changeDetector = changeDetector;\n      this.preOderSettingService = preOderSettingService;\n      this.buildCaseService = buildCaseService;\n      this.router = router;\n      this._eventService = _eventService;\n      this.listEvent = [];\n      this.calendarOptions = {\n        plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n        locale: 'zh-tw',\n        headerToolbar: {\n          left: 'prev',\n          center: 'title',\n          right: 'next'\n        },\n        views: {\n          timeGridWeekCustom: {\n            type: 'timeGridWeek',\n            slotMinTime: \"09:00:00\",\n            slotMaxTime: \"22:00:00\",\n            slotDuration: {\n              hour: 2\n            },\n            dayHeaders: true,\n            allDaySlot: false,\n            titleFormat: arg => {\n              return moment(arg.start).format(\"yyyy/MM/DD\") + \" ~ \" + moment(arg.end).format(\"yyyy/MM/DD\");\n            },\n            // titleFormat: {\n            //   year: 'numeric',\n            //   month: 'numeric',\n            //   day: 'numeric'\n            // },\n            // titleRangeSeparator: ' ~ ',\n            slotLabelFormat: {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            }\n          }\n        },\n        height: 383,\n        initialView: 'timeGridWeekCustom',\n        weekends: true,\n        editable: false,\n        selectable: false,\n        selectMirror: false,\n        dayMaxEvents: true,\n        datesSet: arg => {\n          if (this.paramSave && this.paramSave.minDate) {\n            arg.start = new Date(this.paramSave.minDate);\n            arg.start.setHours(1);\n            arg.end.setDate(arg.start.getDate() + 7);\n          }\n          this.dateFrom = arg.start;\n          this.dateTo = arg.end;\n          this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe();\n        }\n      };\n      this.getPreOderSettingRes = [];\n      this.listBuildCases = [];\n      this.selectedBuildCaseId = 0;\n      this.paramSave = null;\n      this.currentEvents = signal([]);\n      this.tempBuildCaseID = -1;\n      if (!!LocalStorageService.GetLocalStorage('paramSave')) {\n        this.paramSave = JSON.parse(LocalStorageService.GetLocalStorage('paramSave'));\n        LocalStorageService.RemoveLocalStorage('paramSave');\n      } else {\n        this.paramSave = null;\n      }\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    ngAfterViewInit() {\n      this.calendarApi = this.calendarComponent.getApi();\n    }\n    // ngOnDestroy(): void {\n    //   LocalStorageService.RemoveLocalStorage('paramSave')\n    // }\n    getListBuildCase() {\n      if (this.paramSave && this.paramSave.minDate) {\n        this.calendarOptions = {\n          ...this.calendarOptions,\n          // Spread the existing options to avoid mutation\n          initialDate: moment(new Date(this.paramSave.minDate)).format('YYYY-MM-DD')\n        };\n      }\n      this.buildCaseService.apiBuildCaseGetBuildCaseListPost$Json().pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.listBuildCases = res.Entries ?? [];\n          this.selectedBuildCaseId = this.paramSave ? parseInt(this.paramSave.buildCaseId) : res.Entries[0].cID;\n        }\n        if (this.tempBuildCaseID != -1) {\n          let index = this.listBuildCases.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.selectedBuildCaseId = this.listBuildCases[index].cID;\n        } else {\n          this.selectedBuildCaseId = this.listBuildCases[0].cID;\n        }\n      }), mergeMap(() => this.getPreOrderSetting(this.dateFrom, this.dateTo))).subscribe();\n    }\n    getPreOrderSetting(CDateStart, CDateEnd) {\n      return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n        body: {\n          CBuildCaseID: this.selectedBuildCaseId,\n          CDate: null,\n          CDateEnd: CDateEnd.toISOString(),\n          CDateStart: CDateStart.toISOString(),\n          CStatus: null\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.getPreOderSettingRes = res.Entries ?? [];\n          this.initEvent();\n          this.paramSave = null;\n        }\n      }));\n    }\n    searchPreOrderSetting() {\n      this.getPreOrderSetting(this.dateFrom, this.dateTo).subscribe();\n      if (this.dateFrom) {\n        this.calendarApi.gotoDate(moment(this.dateFrom).format('YYYY-MM-DD'));\n      }\n    }\n    initEvent() {\n      this.listEvent = [];\n      if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n        this.getPreOderSettingRes.forEach(e => {\n          if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n            let date = e.CDate ? new Date(e.CDate) : undefined;\n            date = date ? new Date(date.setMinutes(0)) : date;\n            date = date ? new Date(date.setSeconds(0)) : date;\n            let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)).getTime() : 0;\n            let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)).getTime() : 0;\n            this.listEvent.push({\n              start: startDate,\n              end: endDate,\n              display: \"background\"\n            });\n          }\n        });\n      }\n      this.calendarOptions = {\n        ...this.calendarOptions,\n        // Spread the existing options to avoid mutation\n        events: this.listEvent // Update events property with new data\n      };\n    }\n    gotoEdit() {\n      let paramInfo = {\n        CBuildCaseID: this.selectedBuildCaseId,\n        CDateEnd: this.dateTo.toISOString(),\n        CDateStart: this.dateFrom.toISOString()\n      };\n      LocalStorageService.AddLocalStorage('paramInfo', JSON.stringify(paramInfo));\n      this.router.navigateByUrl(`/pages/available-time-slot/${this.selectedBuildCaseId}`);\n    }\n    static {\n      this.ɵfac = function AvailableTimeSlotComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AvailableTimeSlotComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.PreOrderSettingService), i0.ɵɵdirectiveInject(i2.BuildCaseService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AvailableTimeSlotComponent,\n        selectors: [[\"app-available-time-slot\"]],\n        viewQuery: function AvailableTimeSlotComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 28,\n        vars: 9,\n        consts: [[\"calendar\", \"\"], [\"eventContent\", \"\"], [\"accent\", \"success\"], [1, \"col-12\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"w-full\"], [\"for\", \"name\"], [\"name\", \"templates\", 1, \"ml-3\", \"w-full\", 3, \"selectedChange\", \"selected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-end\", \"w-full\"], [\"for\", \"date-select1\", 1, \"mr-3\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\"], [1, \"mt-3\", \"flex\", \"justify-end\", \"items-end\", \"w-full\"], [1, \"mr-3\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [3, \"options\"], [3, \"value\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n        template: function AvailableTimeSlotComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5)(7, \"span\", 6);\n            i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"selectedChange\", function AvailableTimeSlotComponent_Template_nb_select_selectedChange_9_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(10, AvailableTimeSlotComponent_nb_option_10_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 9)(12, \"span\", 10);\n            i0.ɵɵtext(13, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"p-calendar\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dateFrom, $event) || (ctx.dateFrom = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"span\", 12);\n            i0.ɵɵtext(16, \"~\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"p-calendar\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AvailableTimeSlotComponent_Template_p_calendar_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.dateTo, $event) || (ctx.dateTo = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14);\n            i0.ɵɵtemplate(20, AvailableTimeSlotComponent_button_20_Template, 3, 0, \"button\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\");\n            i0.ɵɵtemplate(22, AvailableTimeSlotComponent_button_22_Template, 2, 0, \"button\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(23, \"nb-card-body\")(24, \"full-calendar\", 17, 0);\n            i0.ɵɵtemplate(26, AvailableTimeSlotComponent_ng_template_26_Template, 0, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"selected\", ctx.selectedBuildCaseId);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.listBuildCases);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dateFrom);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"appendTo\", \"body\");\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.dateTo);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUpdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.isUpdate);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"options\", ctx.calendarOptions);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, SharedModule, i6.NgControlStatus, i6.NgModel, i7.NbCardComponent, i7.NbCardBodyComponent, i7.NbCardHeaderComponent, i7.NbSelectComponent, i7.NbOptionComponent, i8.BreadcrumbComponent, FullCalendarModule, i9.FullCalendarComponent, CalendarModule, i10.Calendar],\n        styles: [\".fc .fc-timegrid-slot{height:2.5em}\"]\n      });\n    }\n  }\n  return AvailableTimeSlotComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}