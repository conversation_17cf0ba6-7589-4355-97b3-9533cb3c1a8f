{"ast": null, "code": "import { ApiConfiguration } from './api-configuration';\nimport { BaseFunctionService } from './services/base-function.service';\nimport { BuildCaseService } from './services/build-case.service';\nimport { BuildCaseFileService } from './services/build-case-file.service';\nimport { BuildCaseMailService } from './services/build-case-mail.service';\nimport { FinalDocumentService } from './services/final-document.service';\nimport { FormItemService } from './services/form-item.service';\nimport { HouseService } from './services/house.service';\nimport { HouseHoldDetailService } from './services/house-hold-detail.service';\nimport { HouseHoldMainService } from './services/house-hold-main.service';\nimport { InfoPictureService } from './services/info-picture.service';\nimport { MaterialService } from './services/material.service';\nimport { PictureService } from './services/picture.service';\nimport { PlanService } from './services/plan.service';\nimport { PreOrderSettingService } from './services/pre-order-setting.service';\nimport { RegularChangeItemService } from './services/regular-change-item.service';\nimport { RegularNoticeFileService } from './services/regular-notice-file.service';\nimport { RequirementService } from './services/requirement.service';\nimport { ReviewService } from './services/review.service';\nimport { SpecialChangeService } from './services/special-change.service';\nimport { SpecialNoticeFileService } from './services/special-notice-file.service';\nimport { UserService } from './services/user.service';\nimport { UserGroupService } from './services/user-group.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\n/**\n * Module that provides all services and configuration.\n */\nexport let ApiModule = /*#__PURE__*/(() => {\n  class ApiModule {\n    static forRoot(params) {\n      return {\n        ngModule: ApiModule,\n        providers: [{\n          provide: ApiConfiguration,\n          useValue: params\n        }]\n      };\n    }\n    constructor(parentModule, http) {\n      if (parentModule) {\n        throw new Error('ApiModule is already loaded. Import in your base AppModule only.');\n      }\n      if (!http) {\n        throw new Error('You need to import the HttpClientModule in your AppModule! \\n' + 'See also https://github.com/angular/angular/issues/20575');\n      }\n    }\n    static {\n      this.ɵfac = function ApiModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApiModule)(i0.ɵɵinject(ApiModule, 12), i0.ɵɵinject(i1.HttpClient, 8));\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: ApiModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        providers: [BaseFunctionService, BuildCaseService, BuildCaseFileService, BuildCaseMailService, FinalDocumentService, FormItemService, HouseService, HouseHoldDetailService, HouseHoldMainService, InfoPictureService, MaterialService, PictureService, PlanService, PreOrderSettingService, RegularChangeItemService, RegularNoticeFileService, RequirementService, ReviewService, SpecialChangeService, SpecialNoticeFileService, UserService, UserGroupService, ApiConfiguration]\n      });\n    }\n  }\n  return ApiModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}