{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetSpecialNoticeFilePost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetSpecialNoticeFilePost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetSpecialNoticeFilePost$Json.PATH = '/api/House/GetSpecialNoticeFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetSpecialNoticeFilePost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-special-notice-file-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { TblSpecialNoticeFileResponseBase } from '../../models/tbl-special-notice-file-response-base';\r\n\r\nexport interface ApiHouseGetSpecialNoticeFilePost$Json$Params {\r\n}\r\n\r\nexport function apiHouseGetSpecialNoticeFilePost$Json(http: HttpClient, rootUrl: string, params?: ApiHouseGetSpecialNoticeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblSpecialNoticeFileResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetSpecialNoticeFilePost$Json.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<TblSpecialNoticeFileResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetSpecialNoticeFilePost$Json.PATH = '/api/House/GetSpecialNoticeFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,qCAAqCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAqD,EAAEC,OAAqB;EACnK,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,qCAAqC,CAACM,IAAI,EAAE,MAAM,CAAC;EAC1F,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN;EAAO,CAAE,CAAC,CACjE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAyD;EAClE,CAAC,CAAC,CACH;AACH;AAEAZ,qCAAqC,CAACM,IAAI,GAAG,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}