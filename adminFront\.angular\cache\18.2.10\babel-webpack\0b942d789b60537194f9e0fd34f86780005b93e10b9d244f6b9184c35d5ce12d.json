{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\nexport default function eachYearOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setMonth(0, 1);\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setFullYear(currentDate.getFullYear() + 1);\n  }\n  return dates;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "eachYearOfInterval", "dirtyInterval", "arguments", "interval", "startDate", "start", "endDate", "end", "endTime", "getTime", "RangeError", "dates", "currentDate", "setHours", "setMonth", "push", "setFullYear", "getFullYear"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/eachYearOfInterval/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachYearOfInterval\n * @category Interval Helpers\n * @summary Return the array of yearly timestamps within the specified time interval.\n *\n * @description\n * Return the array of yearly timestamps within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of yearly timestamps from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each year between 6 February 2014 and 10 August 2017:\n * const result = eachYearOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2017, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Thu Jan 01 2015 00:00:00,\n * //   Fri Jan 01 2016 00:00:00,\n * //   Sun Jan 01 2017 00:00:00\n * // ]\n */\nexport default function eachYearOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n\n  // Throw an exception if start date is after end date or if any date is `Invalid Date`\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var dates = [];\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setMonth(0, 1);\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setFullYear(currentDate.getFullYear() + 1);\n  }\n  return dates;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAE;EACxDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGF,aAAa,IAAI,CAAC,CAAC;EAClC,IAAIG,SAAS,GAAGN,MAAM,CAACK,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGR,MAAM,CAACK,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;;EAE/B;EACA,IAAI,EAAEL,SAAS,CAACK,OAAO,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;IACrC,MAAM,IAAIE,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EACA,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,WAAW,GAAGR,SAAS;EAC3BQ,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChCD,WAAW,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOF,WAAW,CAACH,OAAO,CAAC,CAAC,IAAID,OAAO,EAAE;IACvCG,KAAK,CAACI,IAAI,CAACjB,MAAM,CAACc,WAAW,CAAC,CAAC;IAC/BA,WAAW,CAACI,WAAW,CAACJ,WAAW,CAACK,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;EACxD;EACA,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}