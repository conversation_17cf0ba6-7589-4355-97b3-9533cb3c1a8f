{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"householdDialog\"];\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getHouseholdInfo(householdCode_r5).floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"div\", 21)(2, \"span\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template, 2, 1, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener() {\n      const householdCode_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onRemoveHousehold(householdCode_r5));\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(householdCode_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getHouseholdInfo(householdCode_r5).floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 7, 3, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r6, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getBuildingSelectedHouseholds(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.hasBuildingSelected(building_r6));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"nb-icon\", 11);\n    i0.ɵɵelementStart(4, \"span\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r2.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 27);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r2.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r2.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵelement(2, \"nb-icon\", 53);\n    i0.ɵɵelementStart(3, \"p\", 54);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener() {\n      const building_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onBuildingSelect(building_r9));\n    });\n    i0.ɵɵelementStart(1, \"span\", 72);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedBuilding === building_r9 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r2.selectedBuilding === building_r9 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getBuildingCount(building_r9), \"\\u6236 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r2.selectedFloor, \"\");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template, 2, 0, \"button\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r2.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r2.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r2.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"div\", 82)(2, \"input\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchTerm, $event) || (ctx_r2.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template, 2, 1, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm && ctx_r2.filteredHouseholds.length === 0);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template_button_click_0_listener() {\n      const floor_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onFloorSelect(floor_r15));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r2.selectedFloor === floor_r15 ? \"#fff\" : \"#495057\")(\"border-color\", ctx_r2.selectedFloor === floor_r15 ? \"#007bff\" : \"#ced4da\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r15, \" (\", ctx_r2.getFloorCount(floor_r15), \") \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"div\", 88);\n    i0.ɵɵelement(2, \"nb-icon\", 89);\n    i0.ɵɵelementStart(3, \"span\", 90);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template, 2, 0, \"button\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 92);\n    i0.ɵɵtemplate(7, HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template, 2, 8, \"button\", 93);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.floors);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"nb-icon\", 97);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 104);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getHouseholdFloor(householdCode_r17), \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_Template_button_click_0_listener() {\n      const householdCode_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.onHouseholdToggle(householdCode_r17));\n    });\n    i0.ɵɵelementStart(1, \"span\", 101);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_span_3_Template, 2, 1, \"span\", 102)(4, HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_div_4_Template, 2, 0, \"div\", 103);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.isHouseholdSelected(householdCode_r17) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(householdCode_r17) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r2.isHouseholdSelected(householdCode_r17) ? \"#fff\" : ctx_r2.isHouseholdExcluded(householdCode_r17) ? \"#6c757d\" : \"#495057\")(\"border-color\", ctx_r2.isHouseholdSelected(householdCode_r17) ? \"#007bff\" : ctx_r2.isHouseholdExcluded(householdCode_r17) ? \"#dee2e6\" : \"#ced4da\")(\"opacity\", ctx_r2.isHouseholdDisabled(householdCode_r17) ? \"0.6\" : \"1\")(\"cursor\", ctx_r2.isHouseholdDisabled(householdCode_r17) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isHouseholdDisabled(householdCode_r17));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r2.isHouseholdExcluded(householdCode_r17) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r17, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getHouseholdFloor(householdCode_r17));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isHouseholdExcluded(householdCode_r17));\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_Template, 5, 16, \"button\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96);\n    i0.ɵɵelement(1, \"nb-icon\", 106);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"\\u6B64\\u68DF\\u5225\\u6C92\\u6709\\u53EF\\u7528\\u7684\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 57)(3, \"h6\", 58);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 59);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_13_button_6_Template, 5, 6, \"button\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 61)(8, \"div\", 57)(9, \"div\", 62)(10, \"h6\", 58);\n    i0.ɵɵtext(11, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_13_span_12_Template, 2, 1, \"span\", 63)(13, HouseholdBindingComponent_ng_template_8_div_13_span_13_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, HouseholdBindingComponent_ng_template_8_div_13_div_14_Template, 6, 8, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_ng_template_8_div_13_div_15_Template, 5, 2, \"div\", 66)(16, HouseholdBindingComponent_ng_template_8_div_13_div_16_Template, 8, 2, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 68);\n    i0.ɵɵtemplate(18, HouseholdBindingComponent_ng_template_8_div_13_div_18_Template, 4, 0, \"div\", 69)(19, HouseholdBindingComponent_ng_template_8_div_13_div_19_Template, 2, 1, \"div\", 70)(20, HouseholdBindingComponent_ng_template_8_div_13_div_20_Template, 4, 0, \"div\", 69);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowBatchSelect && ctx_r2.selectedBuilding && ctx_r2.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding && ctx_r2.filteredHouseholds.length === 0 && !ctx_r2.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 107);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 110);\n    i0.ɵɵelement(1, \"nb-icon\", 111);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"nb-icon\", 108);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_ng_template_8_div_25_span_6_Template, 3, 1, \"span\", 109);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r2.searchTerm, \"\\\" (\", ctx_r2.filteredHouseholds.length, \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 114);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 116);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 28)(1, \"nb-card-header\")(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelement(4, \"nb-icon\", 31);\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 33);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"nb-card-body\", 34);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_ng_template_8_div_12_Template, 5, 0, \"div\", 35)(13, HouseholdBindingComponent_ng_template_8_div_13_Template, 21, 9, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-card-footer\", 37)(15, \"div\", 38)(16, \"div\", 39)(17, \"div\", 40);\n    i0.ɵɵelement(18, \"nb-icon\", 41);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_ng_template_8_div_24_Template, 7, 1, \"div\", 42)(25, HouseholdBindingComponent_ng_template_8_div_25_Template, 7, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_ng_template_8_div_26_Template, 2, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 44)(28, \"div\", 45);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_ng_template_8_button_29_Template, 3, 0, \"button\", 46)(30, HouseholdBindingComponent_ng_template_8_button_30_Template, 3, 0, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 45)(32, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener() {\n      const ref_r20 = i0.ɵɵrestoreView(_r7).dialogRef;\n      return i0.ɵɵresetView(ref_r20.close());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 50);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r2.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r2.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.allowSearch && ctx_r2.searchTerm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \\u78BA\\u5B9A\\u9078\\u64C7 (\", ctx_r2.getSelectedCount(), \") \");\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.floors = []; // 新增：當前棟別的樓層列表\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    if (this.buildCaseId) {\n      // 使用API載入資料\n      this.loadBuildingDataFromApi();\n    } else {\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n        this.buildingData = this.generateMockData();\n      }\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with buildings:', this.buildings);\n      this.updateSelectedByBuilding();\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // 如果API載入失敗，使用mock資料作為備援\n        this.buildingData = this.generateMockData();\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.code);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n    // 新增：回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    this.houseIdChange.emit(houseIds);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棟別的樓層列表\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棟別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.code === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.code === householdCode);\n      if (household) {\n        return {\n          code: household.code,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      code: householdCode,\n      floor: ''\n    };\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService), i0.ɵɵdirectiveInject(i2.NbDialogService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      viewQuery: function HouseholdBindingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.householdDialog = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseholds: \"excludedHouseholds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        houseIdChange: \"houseIdChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 6,\n      consts: [[\"householdDialog\", \"\"], [1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"home-outline\", 1, \"chevron-icon\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [1, \"household-info\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"household-floor\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"width\", \"95vw\", \"max-width\", \"1200px\", \"max-height\", \"90vh\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\", \"font-size\", \"1.5rem\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\", \"font-size\", \"1.25rem\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"padding\", \"0\", \"overflow\", \"hidden\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [\"style\", \"display: flex; height: 60vh; min-height: 400px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"display\", \"flex\", \"height\", \"60vh\", \"min-height\", \"400px\"], [2, \"width\", \"300px\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"flex\", \"1\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"color: #28a745; font-size: 0.75rem;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"color\", \"#28a745\", \"font-size\", \"0.75rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border-color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"cursor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"6px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"40px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"500\", \"line-height\", \"1.2\"], [\"style\", \"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.6rem\", \"opacity\", \"0.8\", \"margin-top\", \"2px\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [\"style\", \"color: #28a745; margin-left: 8px;\", 4, \"ngIf\"], [2, \"color\", \"#28a745\", \"margin-left\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#28a745\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"12px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 2);\n          i0.ɵɵelementStart(2, \"div\", 3)(3, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleDropdown());\n          });\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 6)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_ng_template_8_Template, 37, 11, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  opacity: 0.8;\\n  font-weight: 400;\\n  color: #1565c0;\\n  background-color: rgba(255, 255, 255, 0.3);\\n  padding: 0.1rem 0.25rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.8);\\n  border: 1px solid #90caf9;\\n  padding: 0.1rem;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #0d47a1;\\n  border-radius: 50%;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f44336;\\n  border-color: #f44336;\\n  color: white;\\n  transform: scale(1.1);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: rgba(255, 255, 255, 0.8);\\n  border-color: #90caf9;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "getHouseholdInfo", "householdCode_r5", "floor", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵtextInterpolate", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r6", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r2", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template_button_click_0_listener", "building_r9", "_r8", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template_button_click_0_listener", "_r11", "onUnselectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_1_listener", "_r10", "onSelectAllFiltered", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_ng_template_8_div_13_div_14_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_ngModelChange_2_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_ng_template_8_div_13_div_15_div_4_Template", "ɵɵtwoWayProperty", "filteredHouseholds", "length", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template_button_click_0_listener", "_r13", "onFloorSelect", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template_button_click_0_listener", "floor_r15", "_r14", "ɵɵtextInterpolate2", "getFloorCount", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_5_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_16_button_7_Template", "floors", "getHouseholdFloor", "householdCode_r17", "HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_Template_button_click_0_listener", "_r16", "onHouseholdToggle", "HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_span_3_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_div_4_Template", "isHouseholdSelected", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_ng_template_8_div_13_div_19_button_1_Template", "HouseholdBindingComponent_ng_template_8_div_13_button_6_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_span_13_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_14_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_15_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_16_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_18_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_19_Template", "HouseholdBindingComponent_ng_template_8_div_13_div_20_Template", "allowBatchSelect", "allowSearch", "maxSelections", "HouseholdBindingComponent_ng_template_8_div_25_span_6_Template", "HouseholdBindingComponent_ng_template_8_button_29_Template_button_click_0_listener", "_r18", "HouseholdBindingComponent_ng_template_8_button_30_Template_button_click_0_listener", "_r19", "resetSearch", "HouseholdBindingComponent_ng_template_8_div_12_Template", "HouseholdBindingComponent_ng_template_8_div_13_Template", "HouseholdBindingComponent_ng_template_8_div_24_Template", "HouseholdBindingComponent_ng_template_8_div_25_Template", "HouseholdBindingComponent_ng_template_8_div_26_Template", "HouseholdBindingComponent_ng_template_8_button_29_Template", "HouseholdBindingComponent_ng_template_8_button_30_Template", "HouseholdBindingComponent_ng_template_8_Template_button_click_32_listener", "ref_r20", "_r7", "dialogRef", "close", "HouseholdBindingComponent_ng_template_8_Template_button_click_34_listener", "isLoading", "selectedHouseholds", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "buildCaseId", "buildingData", "showSelectedArea", "excludedHouseholds", "selectionChange", "houseIdChange", "isOpen", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "console", "log", "loadBuildingDataFromApi", "generateMockData", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "code", "HouseName", "Building", "Floor", "houseId", "HouseId", "houseName", "isSelected", "grouped", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "codes", "Math", "parseInt", "slice", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "event", "target", "householdCode", "newSelection", "emitChanges", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "buildingHouseholds", "unselectedBuilding", "selectedItems", "emit", "houseIds", "id", "undefined", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "floorSet", "Set", "household", "add", "sort", "a", "b", "numA", "replace", "numB", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "i2", "NbDialogService", "selectors", "viewQuery", "HouseholdBindingComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "_r1", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_ng_template_8_Template", "ɵɵtemplateRefExtractor", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseName?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n  \r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棟別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  }\r\n\r\n  private initializeData() {\r\n    if (this.buildCaseId) {\r\n      // 使用API載入資料\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\r\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n        this.buildingData = this.generateMockData();\r\n      }\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with buildings:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // 如果API載入失敗，使用mock資料作為備援\r\n        this.buildingData = this.generateMockData();\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: HouseItem[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.code);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 新增：回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    this.houseIdChange.emit(houseIds);\r\n  }  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棟別的樓層列表\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棟別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.code === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { code: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.code === householdCode);\r\n      if (household) {\r\n        return {\r\n          code: household.code,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { code: householdCode, floor: '' };\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>\r\n          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              <div class=\"household-info\">\r\n                <span class=\"household-code\">{{householdCode}}</span>\r\n                <span *ngIf=\"getHouseholdInfo(householdCode).floor\" class=\"household-floor\">\r\n                  {{getHouseholdInfo(householdCode).floor}}\r\n                </span>\r\n              </div>\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div>  <!-- 選擇器 -->\r\n  <div class=\"selector-container\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"home-outline\" class=\"chevron-icon\"></nb-icon>\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- 戶別選擇對話框 -->\r\n<ng-template #householdDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 95vw; max-width: 1200px; max-height: 90vh;\">\r\n    <nb-card-header>\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n        <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n          <nb-icon icon=\"home-outline\" style=\"color: #007bff; font-size: 1.5rem;\"></nb-icon>\r\n          <span style=\"font-weight: 500; color: #495057; font-size: 1.25rem;\">選擇戶別</span>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n        </div>\r\n        <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n      </div>\r\n    </nb-card-header>\r\n    \r\n    <nb-card-body style=\"padding: 0; overflow: hidden;\">\r\n      <!-- 載入狀態 -->\r\n      <div *ngIf=\"isLoading\" style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n        <div style=\"text-align: center; color: #6c757d;\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n          <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要內容區域 -->\r\n      <div *ngIf=\"!isLoading\" style=\"display: flex; height: 60vh; min-height: 400px;\">\r\n        <!-- 棟別選擇側邊欄 -->\r\n        <div style=\"width: 300px; border-right: 1px solid #e9ecef; background-color: #f8f9fa; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n          </div>\r\n          <div style=\"flex: 1; overflow-y: auto;\">\r\n            <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n              [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n              [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n              style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n              <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n              <span style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">\r\n                {{getBuildingCount(building)}}戶\r\n              </span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 戶別選擇主區域 -->\r\n        <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n          <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n            <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                戶別選擇\r\n                <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n                <span *ngIf=\"selectedFloor\" style=\"color: #28a745; font-size: 0.75rem;\"> - {{selectedFloor}}</span>\r\n              </h6>\r\n              <div *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\" style=\"display: flex; gap: 4px;\">\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選當前\r\n                </button>\r\n                <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                  [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  全選{{selectedBuilding}}\r\n                </button>\r\n                <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                  style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                  清除\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 搜尋框 -->\r\n            <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n              <div style=\"position: relative;\">\r\n                <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                  style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                <nb-icon icon=\"search-outline\"\r\n                  style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n              </div>\r\n              <div *ngIf=\"searchTerm && filteredHouseholds.length === 0\"\r\n                style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                找不到符合 \"{{searchTerm}}\" 的戶別\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 樓層篩選器 -->\r\n            <div *ngIf=\"selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n              <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                <span style=\"font-size: 0.875rem; font-weight: 500; color: #495057;\">樓層篩選:</span>\r\n                <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\r\n                  style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                  清除篩選\r\n                </button>\r\n              </div>\r\n              <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\r\n                  [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                  [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                  [style.border-color]=\"selectedFloor === floor ? '#007bff' : '#ced4da'\"\r\n                  style=\"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                  {{floor}} ({{getFloorCount(floor)}})\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 戶別網格或空狀態 -->\r\n          <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n            <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\"\r\n              style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n              <button *ngFor=\"let householdCode of filteredHouseholds\" type=\"button\"\r\n                (click)=\"onHouseholdToggle(householdCode)\" [disabled]=\"isHouseholdDisabled(householdCode)\"\r\n                [style.background-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#f8f9fa' : '#fff')\"\r\n                [style.color]=\"isHouseholdSelected(householdCode) ? '#fff' : (isHouseholdExcluded(householdCode) ? '#6c757d' : '#495057')\"\r\n                [style.border-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#dee2e6' : '#ced4da')\"\r\n                [style.opacity]=\"isHouseholdDisabled(householdCode) ? '0.6' : '1'\"\r\n                [style.cursor]=\"isHouseholdDisabled(householdCode) ? 'not-allowed' : 'pointer'\"\r\n                style=\"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\">\r\n                <span [style.text-decoration]=\"isHouseholdExcluded(householdCode) ? 'line-through' : 'none'\"\r\n                  style=\"font-weight: 500; line-height: 1.2;\">\r\n                  {{householdCode}}\r\n                </span>\r\n                <span *ngIf=\"getHouseholdFloor(householdCode)\"\r\n                  style=\"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\">\r\n                  {{getHouseholdFloor(householdCode)}}\r\n                </span>\r\n                <div *ngIf=\"isHouseholdExcluded(householdCode)\"\r\n                  style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                  ✕\r\n                </div>\r\n              </button>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding && filteredHouseholds.length === 0 && !searchTerm\"\r\n              style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n              <p style=\"margin: 0; font-size: 0.875rem;\">此棟別沒有可用的戶別</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    \r\n    <nb-card-footer style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n      <!-- 統計資訊行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n        <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n          <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n            <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n          </div>\r\n          <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n            <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n          </div>\r\n          <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span>當前棟別: <strong>{{selectedBuilding}}</strong></span>\r\n            <span *ngIf=\"selectedFloor\" style=\"color: #28a745; margin-left: 8px;\">\r\n              <nb-icon icon=\"layers-outline\" style=\"color: #28a745;\"></nb-icon>\r\n              {{selectedFloor}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"searchTerm\"\r\n          style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\">\r\n          搜尋: \"{{searchTerm}}\" ({{filteredHouseholds.length}} 個結果)\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作按鈕行 -->\r\n      <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" *ngIf=\"selectedHouseholds.length > 0\" (click)=\"onClearAll()\"\r\n            style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n            清空全部\r\n          </button>\r\n          <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n            style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n            重置搜尋\r\n          </button>\r\n        </div>\r\n\r\n        <div style=\"display: flex; gap: 8px;\">\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n            取消\r\n          </button>\r\n          <button type=\"button\" (click)=\"ref.close()\"\r\n            style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n            <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n            確定選擇 ({{getSelectedCount()}})\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAAmD,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;;ICmBxDC,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,MACF;;;;;;IAHAT,EAFJ,CAAAC,cAAA,eAAkG,cACpE,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrDH,EAAA,CAAAU,UAAA,IAAAC,2EAAA,mBAA4E;IAG9EX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAY,UAAA,mBAAAC,6FAAA;MAAA,MAAAL,gBAAA,GAAAR,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,gBAAA,CAAgC;IAAA,EAAC;IACvGR,EAAA,CAAAoB,SAAA,kBAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;;IAR0BH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAb,gBAAA,CAAiB;IACvCR,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,CAA2C;IAIXT,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,QAAA,CAAqB;;;;;IAVpEvB,EAAA,CAAAwB,uBAAA,GAAoD;IAClDxB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAU,UAAA,IAAAe,oEAAA,mBAAkG;IAWpGzB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAbsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,kBAAA,KAAAqB,WAAA,MAAa;IAEP1B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAqB,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAJhF1B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,UAAA,IAAAkB,6DAAA,0BAAoD;IAgBtD5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAhBWH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAuB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpD1B,EAFJ,CAAAC,cAAA,aAAgG,aACjE,cACA;IACzBD,EAAA,CAAAoB,SAAA,kBAA8D;IAC9DpB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IACNH,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAkB,iEAAA;MAAA9B,EAAA,CAAAc,aAAA,CAAAiB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0B,UAAA,EAAY;IAAA,EAAC;IACtGhC,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,UAAA,IAAAuB,8CAAA,kBAA+D;IAmBnEjC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1B6BH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,qCAAAC,MAAA,CAAA4B,gBAAA,QAA8B;IAEDlC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,QAAA,CAAqB;IAKvDvB,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA6B,SAAA,CAAY;;;;;IAyBpCnC,EAAA,CAAAwB,uBAAA,GAAgC;IAC9BxB,EAAA,CAAAoB,SAAA,kBAAsD;IACtDpB,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAwB,uBAAA,GAAiC;IAC/BxB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4B,gBAAA,iCAAA5B,MAAA,CAAA4B,gBAAA,6BAAA5B,MAAA,CAAA8B,WAAA,MACF;;;;;IAwBApC,EADF,CAAAC,cAAA,cAAwH,cACrE;IAC/CD,EAAA,CAAAoB,SAAA,kBAAmG;IACnGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAG,YAAA,EAAI,EACrD,EACF;;;;;;IAUAH,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAY,UAAA,mBAAAyB,yFAAA;MAAA,MAAAC,WAAA,GAAAtC,EAAA,CAAAc,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAkC,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FtC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eAAoH;IAClHD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACA;;;;;IANPH,EADA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAoC,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAhC,MAAA,CAAAoC,gBAAA,KAAAJ,WAAA,iDACe;IAEnDtC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAqB,iBAAA,CAAAiB,WAAA,CAAY;IAE1DtC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAqC,gBAAA,CAAAL,WAAA,aACF;;;;;IAWEtC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAoC,gBAAA,MAAsB;;;;;IAC7E1C,EAAA,CAAAC,cAAA,eAAwE;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,QAAAC,MAAA,CAAAsC,aAAA,KAAmB;;;;;;IAa5F5C,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAY,UAAA,mBAAAiC,gGAAA;MAAA7C,EAAA,CAAAc,aAAA,CAAAgC,IAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAyC,qBAAA,EAAuB;IAAA,EAAC;IAEtF/C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EADF,CAAAC,cAAA,cAAoH,iBAGoB;IAFlFD,EAAA,CAAAY,UAAA,mBAAAoC,uFAAA;MAAAhD,EAAA,CAAAc,aAAA,CAAAmC,IAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4C,mBAAA,EAAqB;IAAA,EAAC;IAGjFlD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAY,UAAA,mBAAAuC,uFAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAmC,IAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA8C,mBAAA,EAAqB;IAAA,EAAC;IAGjFpD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,IAAA2C,uEAAA,qBACsI;IAGxIrD,EAAA,CAAAG,YAAA,EAAM;;;;IAbFH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAAgD,aAAA,iBAA+C;IAD3BtD,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAAgD,aAAA,GAA6B;IAMjDtD,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAAgD,aAAA,iBAA+C;IAD3BtD,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAAgD,aAAA,GAA6B;IAGjDtD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAoC,gBAAA,MACF;IACuB1C,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiD,sBAAA,GAA8B;;;;;IAevDvD,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,uCAAAC,MAAA,CAAAkD,UAAA,2BACF;;;;;;IARExD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAAyD,gBAAA,2BAAAC,8FAAAC,MAAA;MAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA6D,kBAAA,CAAAvD,MAAA,CAAAkD,UAAA,EAAAG,MAAA,MAAArD,MAAA,CAAAkD,UAAA,GAAAG,MAAA;MAAA,OAAA3D,EAAA,CAAAkB,WAAA,CAAAyC,MAAA;IAAA,EAAwB;IAAC3D,EAAA,CAAAY,UAAA,mBAAAkD,sFAAAH,MAAA;MAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAyD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5E3D,EAAA,CAAAG,YAAA,EACsI;IACtIH,EAAA,CAAAoB,SAAA,kBACiI;IACnIpB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,IAAAsD,oEAAA,kBAC+D;IAGjEhE,EAAA,CAAAG,YAAA,EAAM;;;;IATiBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAiE,gBAAA,YAAA3D,MAAA,CAAAkD,UAAA,CAAwB;IAKvCxD,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAkD,UAAA,IAAAlD,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,OAAmD;;;;;;IAWvDnE,EAAA,CAAAC,cAAA,iBAC2H;IAD/ED,EAAA,CAAAY,UAAA,mBAAAwD,gGAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErEtE,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAIyJ;IAJtGD,EAAA,CAAAY,UAAA,mBAAA2D,gGAAA;MAAA,MAAAC,SAAA,GAAAxE,EAAA,CAAAc,aAAA,CAAA2D,IAAA,EAAAzD,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/ExE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAFA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,yBAA0E,UAAAlE,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,sBACd,iBAAAlE,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,yBACU;IAEtExE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,MAAAF,SAAA,QAAAlE,MAAA,CAAAqE,aAAA,CAAAH,SAAA,QACF;;;;;IAfFxE,EADF,CAAAC,cAAA,cAA6E,cACI;IAC7ED,EAAA,CAAAoB,SAAA,kBAAsF;IACtFpB,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,IAAAkE,uEAAA,qBAC2H;IAG7H5E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,UAAA,IAAAmE,uEAAA,qBAIyJ;IAI7J7E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdqBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;IAMF5C,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAwE,MAAA,CAAS;;;;;IAarD9E,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAoB,SAAA,kBAAkG;IAClGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;;;;;IAeFH,EAAA,CAAAC,cAAA,gBAC4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAyE,iBAAA,CAAAC,iBAAA,OACF;;;;;IACAhF,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAnBRH,EAAA,CAAAC,cAAA,kBAO6O;IAN3OD,EAAA,CAAAY,UAAA,mBAAAqE,gGAAA;MAAA,MAAAD,iBAAA,GAAAhF,EAAA,CAAAc,aAAA,CAAAoE,IAAA,EAAAlE,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6E,iBAAA,CAAAH,iBAAA,CAAgC;IAAA,EAAC;IAO1ChF,EAAA,CAAAC,cAAA,gBAC8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKPH,EAJA,CAAAU,UAAA,IAAA0E,8EAAA,oBAC4D,IAAAC,6EAAA,mBAI0J;IAGxNrF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdPH,EAJA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,gBAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,uBAAqI,UAAA1E,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,aAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,0BACX,iBAAA1E,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,gBAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,0BACU,YAAA1E,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,gBAClE,WAAA1E,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,8BACa;IALpChF,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,EAA+C;IAOpFhF,EAAA,CAAAI,SAAA,EAAsF;IAAtFJ,EAAA,CAAAyC,WAAA,oBAAAnC,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,4BAAsF;IAE1FhF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA2E,iBAAA,MACF;IACOhF,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAyE,iBAAA,CAAAC,iBAAA,EAAsC;IAIvChF,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,EAAwC;;;;;IAlBlDhF,EAAA,CAAAC,cAAA,cACgG;IAC9FD,EAAA,CAAAU,UAAA,IAAA+E,uEAAA,sBAO6O;IAc/OzF,EAAA,CAAAG,YAAA,EAAM;;;;IArB8BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA4D,kBAAA,CAAqB;;;;;IAsBzDlE,EAAA,CAAAC,cAAA,cACkE;IAChED,EAAA,CAAAoB,SAAA,mBAA0G;IAC1GpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;;;;;IAhHNH,EAJN,CAAAC,cAAA,cAAgF,cAEgD,cACzD,aACa;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACpFF,EADoF,CAAAG,YAAA,EAAK,EACnF;IACNH,EAAA,CAAAC,cAAA,cAAwC;IACtCD,EAAA,CAAAU,UAAA,IAAAgF,gEAAA,qBAG4L;IAOhM1F,EADE,CAAAG,YAAA,EAAM,EACF;IAMAH,EAHN,CAAAC,cAAA,cAA6D,cACQ,cACoC,cACrB;IAC5ED,EAAA,CAAAE,MAAA,kCACA;IACAF,EADA,CAAAU,UAAA,KAAAiF,+DAAA,mBAAuD,KAAAC,+DAAA,mBACiB;IAC1E5F,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,UAAA,KAAAmF,8DAAA,kBAAoH;IAgBtH7F,EAAA,CAAAG,YAAA,EAAM;IAiBNH,EAdA,CAAAU,UAAA,KAAAoF,8DAAA,kBAAsE,KAAAC,8DAAA,kBAcO;IAmB/E/F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuD;IA6BrDD,EA5BA,CAAAU,UAAA,KAAAsF,8DAAA,kBAA+F,KAAAC,8DAAA,kBAKC,KAAAC,8DAAA,kBAwB9B;IAMxElG,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAhH6BH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA6B,SAAA,CAAY;IAkB9BnC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,CAAsB;IACtB1C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;IAEtB5C,EAAA,CAAAI,SAAA,EAA2E;IAA3EJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA6F,gBAAA,IAAA7F,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,KAA2E;IAmB7EnE,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA8F,WAAA,IAAA9F,MAAA,CAAAoC,gBAAA,CAAqC;IAcrC1C,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAAwE,MAAA,CAAAX,MAAA,KAA2C;IAuB3CnE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAsB,UAAA,UAAAhB,MAAA,CAAAoC,gBAAA,CAAuB;IAIvB1C,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,KAAuD;IAwBvDnE,EAAA,CAAAI,SAAA,EAAwE;IAAxEJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,WAAA7D,MAAA,CAAAkD,UAAA,CAAwE;;;;;IAkBhFxD,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAoB,SAAA,mBAAuE;IACvEpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADiBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA+F,aAAA,CAAiB;;;;;IAKtCrG,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAoB,SAAA,mBAAiE;IACjEpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsC,aAAA,MACF;;;;;IANF5C,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,SAAA,mBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAASF,EAAT,CAAAG,YAAA,EAAS,EAAO;IACxDH,EAAA,CAAAU,UAAA,IAAA4F,8DAAA,oBAAsE;IAIxEtG,EAAA,CAAAG,YAAA,EAAM;;;;IALgBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAoC,gBAAA,CAAoB;IACjC1C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;;;;;IAM9B5C,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,sBAAApE,MAAA,CAAAkD,UAAA,UAAAlD,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,0BACF;;;;;;IAMEnE,EAAA,CAAAC,cAAA,kBACsL;IAD1HD,EAAA,CAAAY,UAAA,mBAAA2F,mFAAA;MAAAvG,EAAA,CAAAc,aAAA,CAAA0F,IAAA;MAAA,MAAAlG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0B,UAAA,EAAY;IAAA,EAAC;IAEhFhC,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAY,UAAA,mBAAA6F,mFAAA;MAAAzG,EAAA,CAAAc,aAAA,CAAA4F,IAAA;MAAA,MAAApG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAqG,WAAA,EAAa;IAAA,EAAC;IAE7E3G,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IApLXH,EAHN,CAAAC,cAAA,kBAAmE,qBACjD,cACmE,cACpB;IACzDD,EAAA,CAAAoB,SAAA,kBAAkF;IAClFpB,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAG,YAAA,EAAO,EAChF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EACjF,EACS;IAEjBH,EAAA,CAAAC,cAAA,wBAAoD;IAUlDD,EARA,CAAAU,UAAA,KAAAkG,uDAAA,kBAAwH,KAAAC,uDAAA,mBAQxC;IAwHlF7G,EAAA,CAAAG,YAAA,EAAe;IAMTH,EAJN,CAAAC,cAAA,0BAAiG,eAEO,eACH,eACpC;IACzDD,EAAA,CAAAoB,SAAA,mBAA2E;IAC3EpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAKNH,EAJA,CAAAU,UAAA,KAAAoG,uDAAA,kBAAiF,KAAAC,uDAAA,kBAIG;IAQtF/G,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAAsG,uDAAA,kBACgH;IAGlHhH,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAU,UAAA,KAAAuG,0DAAA,qBACsL,KAAAC,0DAAA,qBAKA;IAIxLlH,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAY,UAAA,mBAAAuG,0EAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAc,aAAA,CAAAuG,GAAA,EAAAC,SAAA;MAAA,OAAAtH,EAAA,CAAAkB,WAAA,CAASkG,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCvH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAY,UAAA,mBAAA4G,0EAAA;MAAA,MAAAJ,OAAA,GAAApH,EAAA,CAAAc,aAAA,CAAAuG,GAAA,EAAAC,SAAA;MAAA,OAAAtH,EAAA,CAAAkB,WAAA,CAASkG,OAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAEzCvH,EAAA,CAAAoB,SAAA,mBAA4C;IAC5CpB,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;;IAjMiDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,SAAA,CAAAgC,MAAA,yBAA0B;IAE5BnE,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAA4B,gBAAA,OAA2B;IAM1ElC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAmH,SAAA,CAAe;IAQfzH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,UAAAhB,MAAA,CAAAmH,SAAA,CAAgB;IAgIGzH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA4B,gBAAA,GAAsB;IAErClC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA+F,aAAA,CAAmB;IAInBrG,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,CAAsB;IASxB1C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAkD,UAAA,CAAgB;IASGxD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoH,kBAAA,CAAAvD,MAAA,KAAmC;IAKnCnE,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA8F,WAAA,IAAA9F,MAAA,CAAAkD,UAAA,CAA+B;IAepDxD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,gCAAAC,MAAA,CAAA4B,gBAAA,SACF;;;ADnNV,OAAM,MAAOyF,yBAAyB;EA6BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA7Bd,KAAA3F,WAAW,GAAW,OAAO;IAC7B,KAAAiE,aAAa,GAAkB,IAAI;IACnC,KAAA9E,QAAQ,GAAY,KAAK;IACzB,KAAAyG,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAA9B,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAAgC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIvI,YAAY,EAAmB;IACrD,KAAAwI,aAAa,GAAG,IAAIxI,YAAY,EAAY,CAAC,CAAC;IACxD,KAAAyI,MAAM,GAAG,KAAK;IACd,KAAA5F,gBAAgB,GAAG,EAAE;IACrB,KAAAc,UAAU,GAAG,EAAE;IACf,KAAAZ,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAA8E,kBAAkB,GAAa,EAAE;IACjC,KAAAvF,SAAS,GAAa,EAAE;IACxB,KAAA2C,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAZ,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAqE,kBAAkB,GAAqC,EAAE;IACzD,KAAAd,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAe,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACf,kBAAkB,GAAGe,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAC1H,QAAQ,GAAG0H,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACrB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACmB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAAClH,SAAS,GAAGmH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;MAC/C,IAAI,CAACuB,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACvB,kBAAkB,CAAC;IACtE;EACF;EAEQgB,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACnB,WAAW,EAAE;MACpB;MACA,IAAI,CAAC2B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAAC1B,YAAY,IAAIqB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC,CAAC9D,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAC8D,YAAY,GAAG,IAAI,CAAC2B,gBAAgB,EAAE;MAC7C;MACA,IAAI,CAACzH,SAAS,GAAGmH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;MAC/CwB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACvH,SAAS,CAAC;MACpE,IAAI,CAACyG,wBAAwB,EAAE;IACjC;EACF;EAEQe,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC3B,WAAW,EAAE;IAEvB,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,kBAAkB,CAAC+B,WAAW,CAAC,IAAI,CAAC7B,WAAW,CAAC,CAAC8B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,QAAQ,CAAC;QACtC,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACgC,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC/H,SAAS,GAAGmH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;QAC/C,IAAI,CAACW,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACsC,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAACnC,YAAY,GAAG,IAAI,CAAC2B,gBAAgB,EAAE;QAC3C,IAAI,CAACzH,SAAS,GAAGmH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC;QAC/C,IAAI,CAACW,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACI,GAAG,CAACsC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,gCAAgCA,CAACI,OAAuC;IAC9E,MAAMpC,YAAY,GAAiB,EAAE;IAErCqB,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDvC,YAAY,CAACsC,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,IAAI,EAAED,KAAK,CAACE,SAAS;QACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;QACxBpK,KAAK,EAAEiK,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACE,SAAS;QAC1BM,UAAU,EAAE,KAAK;QACjBjC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOhB,YAAY;EACrB;EAEQW,wBAAwBA,CAAA;IAC9B,MAAMuC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACzD,kBAAkB,CAAC4C,OAAO,CAACK,IAAI,IAAG;MACrC,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpI,SAAS,EAAE;QACrC,MAAMiJ,IAAI,GAAG,IAAI,CAACnD,YAAY,CAACsC,QAAQ,CAAC,EAAEc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIS,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACZ,QAAQ,CAAC,EAAEY,OAAO,CAACZ,QAAQ,CAAC,GAAG,EAAE;UAC9CY,OAAO,CAACZ,QAAQ,CAAC,CAACgB,IAAI,CAACZ,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACpC,kBAAkB,GAAG4C,OAAO;EACnC;EACQvB,gBAAgBA,CAAA;IACtB;IACA,MAAM4B,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEvH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACwH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEvH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACwH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEvH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACwH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEvH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACwH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEvH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACwH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAM7D,YAAY,GAAiB,EAAE;IACrCqB,MAAM,CAACe,OAAO,CAACmB,cAAc,CAAC,CAAClB,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEwB,KAAK,CAAC,KAAI;MAC3D9D,YAAY,CAACsC,QAAQ,CAAC,GAAGwB,KAAK,CAACtB,GAAG,CAACE,IAAI,KAAK;QAC1CA,IAAI;QACJJ,QAAQ;QACR9J,KAAK,EAAE,GAAGuL,IAAI,CAACvL,KAAK,CAAC,CAACwL,QAAQ,CAACtB,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DhB,UAAU,EAAE,KAAK;QACjBjC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOhB,YAAY;EACrB;EAAEzF,gBAAgBA,CAAC+H,QAAgB;IACjCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,QAAQ,CAAC;IAC3C,IAAI,CAAC7H,gBAAgB,GAAG6H,QAAQ;IAChC,IAAI,CAAC3H,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACY,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC2I,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC3C,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACxF,kBAAkB,CAACC,MAAM,CAAC;IACzE;IACA,IAAI,CAAC0D,GAAG,CAACsC,aAAa,EAAE;EAC1B;EAEAiC,eAAeA,CAAC7B,QAAgB;IAC9Bd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,QAAQ,CAAC;EACxD;EAAEf,wBAAwBA,CAAA;IACxBC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAChH,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACwB,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMmI,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,IAAI,EAAE;IACjE+G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,UAAU,CAAClI,MAAM,CAAC;IAEpE;IACA,IAAI,CAACD,kBAAkB,GAAGmI,UAAU,CACjCC,MAAM,CAAChB,CAAC,IAAG;MACV;MACA,MAAMiB,UAAU,GAAG,CAAC,IAAI,CAAC3J,aAAa,IAAI0I,CAAC,CAAC7K,KAAK,KAAK,IAAI,CAACmC,aAAa;MACxE;MACA,MAAM4J,WAAW,GAAGlB,CAAC,CAACX,IAAI,CAAC8B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClJ,UAAU,CAACiJ,WAAW,EAAE,CAAC;MAChF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACD/B,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC;IAEnBlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACxF,kBAAkB,CAACC,MAAM,CAAC;EAC5E;EAEAJ,cAAcA,CAAC4I,KAAU;IACvB,IAAI,CAACnJ,UAAU,GAAGmJ,KAAK,CAACC,MAAM,CAACnE,KAAK;IACpC,IAAI,CAACe,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAClG,UAAU,CAAC;EACtD;EAEAmD,WAAWA,CAAA;IACT,IAAI,CAACnD,UAAU,GAAG,EAAE;IACpB,IAAI,CAACgG,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACAvE,iBAAiBA,CAAC0H,aAAqB;IACrC;IACA,IAAI,IAAI,CAACtH,mBAAmB,CAACsH,aAAa,CAAC,EAAE;MAC3CpD,OAAO,CAACC,GAAG,CAAC,MAAMmD,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAM3B,UAAU,GAAG,IAAI,CAACxD,kBAAkB,CAACgF,QAAQ,CAACG,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAI5B,UAAU,EAAE;MACd4B,YAAY,GAAG,IAAI,CAACpF,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACxG,aAAa,IAAI,IAAI,CAACqB,kBAAkB,CAACvD,MAAM,IAAI,IAAI,CAACkC,aAAa,EAAE;QAC9EoD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAoD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpF,kBAAkB,EAAEmF,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACnF,kBAAkB,GAAGoF,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA5L,iBAAiBA,CAAC0L,aAAqB;IACrC,IAAI,CAACnF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EAAE7J,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACwB,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAM6I,YAAY,GAAG,IAAI,CAACtF,kBAAkB,CAACvD,MAAM;IACnD,MAAM8I,UAAU,GAAG,IAAI,CAAC5G,aAAa,IAAI6G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAClJ,kBAAkB,CAACoI,MAAM,CAAC3B,IAAI,IAC5D,CAAC,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACpF,mBAAmB,CAACoF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM0C,KAAK,GAAGD,kBAAkB,CAAClB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAClJ,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACuD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG2F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBtD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAAClJ,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAf,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAM4K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,EAAE+H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAE3F;IACA,MAAMqC,YAAY,GAAG,IAAI,CAACtF,kBAAkB,CAACvD,MAAM;IACnD,MAAM8I,UAAU,GAAG,IAAI,CAAC5G,aAAa,IAAI6G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMO,kBAAkB,GAAGD,kBAAkB,CAAChB,MAAM,CAAC3B,IAAI,IACvD,CAAC,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACpF,mBAAmB,CAACoF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM0C,KAAK,GAAGE,kBAAkB,CAACrB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAClJ,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACuD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG2F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBtD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAAClJ,MAAM,MAAM,CAAC;IAC9C;EACF;EACApB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAM4K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,EAAE+H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAACjD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAI,CAACgC,kBAAkB,CAACZ,QAAQ,CAACpB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACyB,WAAW,EAAE;EACpB;EACA/K,UAAUA,CAAA;IACR,IAAI,CAAC0F,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACqF,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACnE,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACgB,SAAS,EAAE;IAEhB,MAAM8E,aAAa,GAAG,IAAI,CAAC9F,kBAAkB,CAAC+C,GAAG,CAACE,IAAI,IAAG;MACvD,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpI,SAAS,EAAE;QACrC,MAAMiJ,IAAI,GAAG,IAAI,CAACnD,YAAY,CAACsC,QAAQ,CAAC,EAAEc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIS,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACkB,MAAM,CAAClB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAAChD,eAAe,CAACqF,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAAC/C,GAAG,CAACW,IAAI,IAAIA,IAAI,CAACL,OAAQ,CAAC,CAACuB,MAAM,CAACqB,EAAE,IAAIA,EAAE,KAAKC,SAAS,CAAC;IACxF,IAAI,CAACvF,aAAa,CAACoF,IAAI,CAACC,QAAQ,CAAC;EACnC;EAAGG,cAAcA,CAAA;IACf,IAAI,CAAC,IAAI,CAACtM,QAAQ,EAAE;MAClB,IAAI,CAACuM,UAAU,EAAE;MACjBrE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACvH,SAAS,CAAC;IACrD;EACF;EAEA2L,UAAUA,CAAA;IACR,IAAI,CAAC/F,aAAa,CAACgG,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAGF/I,mBAAmBA,CAACuH,aAAqB;IACvC,OAAO,IAAI,CAACnF,kBAAkB,CAACgF,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEAtH,mBAAmBA,CAACsH,aAAqB;IACvC,OAAO,IAAI,CAAC1E,kBAAkB,CAACuE,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEArH,mBAAmBA,CAACqH,aAAqB;IACvC,OAAO,IAAI,CAACtH,mBAAmB,CAACsH,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACvJ,aAAa,EAAE,IAAI,CAAC,IAAI,CAACgC,mBAAmB,CAACuH,aAAa,CAAE;EACvE;EAEAvJ,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAC+C,aAAa,IAAI,IAAI,CAACqB,kBAAkB,CAACvD,MAAM,GAAG,IAAI,CAACkC,aAAa;EACnF;EAEAiI,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC5L,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM4K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,CAChE4J,MAAM,CAAChB,CAAC,IAAI,CAACA,CAAC,CAACrC,UAAU,CAAC,CAC1BwB,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC;IACnB,OAAO2C,kBAAkB,CAACnJ,MAAM,GAAG,CAAC,IAClCmJ,kBAAkB,CAACiB,KAAK,CAAC5D,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,CAAC;EAC5E;EACApH,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM4K,kBAAkB,GAAG,IAAI,CAACrF,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,EAAE+H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAO2C,kBAAkB,CAACkB,IAAI,CAAC7D,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,CAAC;EAChF;EACA8D,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAClG,kBAAkB;EAChC;EAEA5F,gBAAgBA,CAAC4H,QAAgB;IAC/B,OAAO,IAAI,CAACtC,YAAY,CAACsC,QAAQ,CAAC,EAAEpG,MAAM,IAAI,CAAC;EACjD;EAEAjC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACwF,kBAAkB,CAACvD,MAAM;EACvC;EAEA;EACAxC,6BAA6BA,CAAC4I,QAAgB;IAC5C,OAAO,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA1I,mBAAmBA,CAAC0I,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,CAACpG,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQgI,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACzJ,gBAAgB,EAAE;MAC1B,IAAI,CAACoC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMuH,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMgM,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElCtC,UAAU,CAAC/B,OAAO,CAACsE,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACnO,KAAK,EAAE;QACnBiO,QAAQ,CAACG,GAAG,CAACD,SAAS,CAACnO,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACqE,MAAM,GAAG2G,KAAK,CAACC,IAAI,CAACgD,QAAQ,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGhD,QAAQ,CAAC8C,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGlD,QAAQ,CAAC+C,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOD,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEF1F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAChH,gBAAgB,EAAE,IAAI,CAACoC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAAC7D,KAAa;IACzBgJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEjJ,KAAK,CAAC;IACrC,IAAI,CAACmC,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKnC,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAAC+I,wBAAwB,EAAE;IAC/B,IAAI,CAAC3B,GAAG,CAACsC,aAAa,EAAE;EAC1B;EAEA;EACAxF,aAAaA,CAAClE,KAAa;IACzB,IAAI,CAAC,IAAI,CAACiC,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAM2J,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAO2J,UAAU,CAACC,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAAC7K,KAAK,KAAKA,KAAK,CAAC,CAAC0D,MAAM;EACzD;EAEA;EACAY,iBAAiBA,CAAC8H,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACnK,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAM2J,UAAU,GAAG,IAAI,CAACpE,YAAY,CAAC,IAAI,CAACvF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMkM,SAAS,GAAGvC,UAAU,CAAChB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKkC,aAAa,CAAC;IAChE,OAAO+B,SAAS,EAAEnO,KAAK,IAAI,EAAE;EAC/B;EAEA;EACAF,gBAAgBA,CAACsM,aAAqB;IACpC,KAAK,MAAMtC,QAAQ,IAAI,IAAI,CAACpI,SAAS,EAAE;MACrC,MAAMkK,UAAU,GAAG,IAAI,CAACpE,YAAY,CAACsC,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMqE,SAAS,GAAGvC,UAAU,CAAChB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKkC,aAAa,CAAC;MAChE,IAAI+B,SAAS,EAAE;QACb,OAAO;UACLjE,IAAI,EAAEiE,SAAS,CAACjE,IAAI;UACpBlK,KAAK,EAAEmO,SAAS,CAACnO,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEkK,IAAI,EAAEkC,aAAa;MAAEpM,KAAK,EAAE;IAAE,CAAE;EAC3C;;;uCArcWkH,yBAAyB,EAAA3H,EAAA,CAAAoP,iBAAA,CAAApP,EAAA,CAAAqP,iBAAA,GAAArP,EAAA,CAAAoP,iBAAA,CAAAE,EAAA,CAAAC,kBAAA,GAAAvP,EAAA,CAAAoP,iBAAA,CAAAI,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAzB9H,yBAAyB;MAAA+H,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;uCARzB,CACT;QACEE,OAAO,EAAEhQ,iBAAiB;QAC1BiQ,WAAW,EAAElQ,UAAU,CAAC,MAAM6H,yBAAyB,CAAC;QACxDsI,KAAK,EAAE;OACR,CACF,GAAAjQ,EAAA,CAAAkQ,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnCH7P,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAU,UAAA,IAAA8P,wCAAA,kBAAgG;UAgC9FxQ,EADF,CAAAC,cAAA,aAAgC,gBAGmL;UAD5KD,EAAA,CAAAY,UAAA,mBAAA6P,2DAAA;YAAAzQ,EAAA,CAAAc,aAAA,CAAA4P,GAAA;YAAA,OAAA1Q,EAAA,CAAAkB,WAAA,CAAS4O,GAAA,CAAAjC,cAAA,EAAgB;UAAA,EAAC;UAE7D7N,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAU,UAAA,IAAAiQ,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnC5Q,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAoB,SAAA,iBAA4D;UAGlEpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGNH,EAAA,CAAAU,UAAA,IAAAmQ,gDAAA,kCAAA7Q,EAAA,CAAA8Q,sBAAA,CAA6D;;;UAlDrD9Q,EAAA,CAAAI,SAAA,EAAuD;UAAvDJ,EAAA,CAAAsB,UAAA,SAAAwO,GAAA,CAAA5H,gBAAA,IAAA4H,GAAA,CAAApI,kBAAA,CAAAvD,MAAA,KAAuD;UAgCbnE,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAA+Q,WAAA,aAAAjB,GAAA,CAAAvO,QAAA,IAAAuO,GAAA,CAAArI,SAAA,CAAwC;UACpFzH,EAAA,CAAAsB,UAAA,aAAAwO,GAAA,CAAAvO,QAAA,IAAAuO,GAAA,CAAArI,SAAA,CAAkC;UAGjBzH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAsB,UAAA,SAAAwO,GAAA,CAAArI,SAAA,CAAe;UAIfzH,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAsB,UAAA,UAAAwO,GAAA,CAAArI,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}