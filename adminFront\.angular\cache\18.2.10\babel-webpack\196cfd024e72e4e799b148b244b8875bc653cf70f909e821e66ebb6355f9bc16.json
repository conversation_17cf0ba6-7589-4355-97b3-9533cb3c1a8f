{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nlet TemplateViewerComponent = class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.templateDetails = [];\n    this.sharedData = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n    this.saveTemplate = new EventEmitter();\n    this.deleteTemplate = new EventEmitter();\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    this.selectedTemplate = null;\n  }\n  // 建立模板\n  createTemplate() {\n    const selected = (this.sharedData || []).filter(x => x.selected);\n    if (!this.newTemplateName || selected.length === 0) {\n      alert('請輸入模板名稱並選擇資料');\n      return;\n    }\n    const template = {\n      TemplateName: this.newTemplateName,\n      Description: this.newTemplateDesc\n    };\n    // details 依據選擇資料組成\n    const details = selected.map(x => ({\n      TemplateID: 0,\n      // 新增時由後端補上\n      RefID: x.ID || x.CRequirementID || 0,\n      ModuleType: x.ModuleType || 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: x.CRequirement\n    }));\n    this.saveTemplate.emit({\n      template,\n      details\n    });\n    this.showCreateTemplate = false;\n    this.newTemplateName = '';\n    this.newTemplateDesc = '';\n    (this.sharedData || []).forEach(x => x.selected = false);\n  }\n  // 新增模板\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  // 查看模板\n  onSelectTemplate(template) {\n    this.selectedTemplate = template;\n    this.selectTemplate.emit(template);\n  }\n  // 刪除模板\n  onDeleteTemplate(templateID) {\n    if (confirm('確定刪除此模板？')) {\n      this.deleteTemplate.emit(templateID);\n    }\n  }\n  // 關閉模板詳情\n  closeTemplateDetail() {\n    this.selectedTemplate = null;\n  }\n  // 取得當前選中模板的詳情\n  get currentTemplateDetails() {\n    if (!this.selectedTemplate) {\n      return [];\n    }\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate.TemplateID);\n  }\n};\n__decorate([Input()], TemplateViewerComponent.prototype, \"templates\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"templateDetails\", void 0);\n__decorate([Input()], TemplateViewerComponent.prototype, \"sharedData\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"addTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"selectTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"saveTemplate\", void 0);\n__decorate([Output()], TemplateViewerComponent.prototype, \"deleteTemplate\", void 0);\nTemplateViewerComponent = __decorate([Component({\n  selector: 'app-template-viewer',\n  templateUrl: './template-viewer.component.html',\n  styleUrls: ['./template-viewer.component.scss'],\n  standalone: true,\n  imports: [FormsModule]\n})], TemplateViewerComponent);\nexport { TemplateViewerComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "FormsModule", "TemplateViewerComponent", "constructor", "templates", "templateDetails", "sharedData", "addTemplate", "selectTemplate", "saveTemplate", "deleteTemplate", "showCreateTemplate", "newTemplateName", "newTemplateDesc", "selectedTemplate", "createTemplate", "selected", "filter", "x", "length", "alert", "template", "TemplateName", "Description", "details", "map", "TemplateID", "RefID", "ID", "CRequirementID", "ModuleType", "FieldName", "FieldValue", "CRequirement", "emit", "for<PERSON>ach", "onAddTemplate", "onSelectTemplate", "onDeleteTemplate", "templateID", "confirm", "closeTemplateDetail", "currentTemplateDetails", "d", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss'],\r\n  standalone: true,\r\n  imports: [FormsModule],\r\n})\r\nexport class TemplateViewerComponent {\r\n  @Input() templates: Template[] = [];\r\n  @Input() templateDetails: TemplateDetail[] = [];\r\n  @Input() sharedData: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<Template>();\r\n  @Output() selectTemplate = new EventEmitter<Template>();\r\n  @Output() saveTemplate = new EventEmitter<{ template: Template, details: TemplateDetail[] }>();\r\n  @Output() deleteTemplate = new EventEmitter<number>();\r\n\r\n  showCreateTemplate = false;\r\n  newTemplateName = '';\r\n  newTemplateDesc = '';\r\n  selectedTemplate: Template | null = null;\r\n\r\n  // 建立模板\r\n  createTemplate() {\r\n    const selected = (this.sharedData || []).filter(x => x.selected);\r\n    if (!this.newTemplateName || selected.length === 0) {\r\n      alert('請輸入模板名稱並選擇資料');\r\n      return;\r\n    }\r\n    const template: Template = {\r\n      TemplateName: this.newTemplateName,\r\n      Description: this.newTemplateDesc\r\n    };\r\n    // details 依據選擇資料組成\r\n    const details: TemplateDetail[] = selected.map(x => ({\r\n      TemplateID: 0, // 新增時由後端補上\r\n      RefID: x.ID || x.CRequirementID || 0,\r\n      ModuleType: x.ModuleType || 'Requirement',\r\n      FieldName: 'CRequirement',\r\n      FieldValue: x.CRequirement\r\n    }));\r\n    this.saveTemplate.emit({ template, details });\r\n    this.showCreateTemplate = false;\r\n    this.newTemplateName = '';\r\n    this.newTemplateDesc = '';\r\n    (this.sharedData || []).forEach(x => x.selected = false);\r\n  }\r\n\r\n  // 新增模板\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  // 查看模板\r\n  onSelectTemplate(template: Template) {\r\n    this.selectedTemplate = template;\r\n    this.selectTemplate.emit(template);\r\n  }\r\n\r\n  // 刪除模板\r\n  onDeleteTemplate(templateID: number) {\r\n    if (confirm('確定刪除此模板？')) {\r\n      this.deleteTemplate.emit(templateID);\r\n    }\r\n  }\r\n\r\n  // 關閉模板詳情\r\n  closeTemplateDetail() {\r\n    this.selectedTemplate = null;\r\n  }\r\n\r\n  // 取得當前選中模板的詳情\r\n  get currentTemplateDetails(): TemplateDetail[] {\r\n    if (!this.selectedTemplate) {\r\n      return [];\r\n    }\r\n    return this.templateDetails.filter(d => d.TemplateID === this.selectedTemplate!.TemplateID);\r\n  }\r\n}\r\n\r\n// DB 對應型別\r\nexport interface Template {\r\n  TemplateID?: number;\r\n  TemplateName: string;\r\n  Description?: string;\r\n}\r\nexport interface TemplateDetail {\r\n  TemplateDetailID?: number;\r\n  TemplateID: number;\r\n  RefID: number; // 關聯主檔ID\r\n  ModuleType: string;\r\n  FieldName: string;\r\n  FieldValue: string;\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,QAAQ,eAAe;AACtE,SAASC,WAAW,QAAQ,gBAAgB;AASrC,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAA7BC,YAAA;IACI,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,eAAe,GAAqB,EAAE;IACtC,KAAAC,UAAU,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAG,IAAIT,YAAY,EAAY;IAC1C,KAAAU,cAAc,GAAG,IAAIV,YAAY,EAAY;IAC7C,KAAAW,YAAY,GAAG,IAAIX,YAAY,EAAqD;IACpF,KAAAY,cAAc,GAAG,IAAIZ,YAAY,EAAU;IAErD,KAAAa,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,gBAAgB,GAAoB,IAAI;EA0D1C;EAxDE;EACAC,cAAcA,CAAA;IACZ,MAAMC,QAAQ,GAAG,CAAC,IAAI,CAACV,UAAU,IAAI,EAAE,EAAEW,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,QAAQ,CAAC;IAChE,IAAI,CAAC,IAAI,CAACJ,eAAe,IAAII,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;MAClDC,KAAK,CAAC,cAAc,CAAC;MACrB;IACF;IACA,MAAMC,QAAQ,GAAa;MACzBC,YAAY,EAAE,IAAI,CAACV,eAAe;MAClCW,WAAW,EAAE,IAAI,CAACV;KACnB;IACD;IACA,MAAMW,OAAO,GAAqBR,QAAQ,CAACS,GAAG,CAACP,CAAC,KAAK;MACnDQ,UAAU,EAAE,CAAC;MAAE;MACfC,KAAK,EAAET,CAAC,CAACU,EAAE,IAAIV,CAAC,CAACW,cAAc,IAAI,CAAC;MACpCC,UAAU,EAAEZ,CAAC,CAACY,UAAU,IAAI,aAAa;MACzCC,SAAS,EAAE,cAAc;MACzBC,UAAU,EAAEd,CAAC,CAACe;KACf,CAAC,CAAC;IACH,IAAI,CAACxB,YAAY,CAACyB,IAAI,CAAC;MAAEb,QAAQ;MAAEG;IAAO,CAAE,CAAC;IAC7C,IAAI,CAACb,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,CAAC,IAAI,CAACP,UAAU,IAAI,EAAE,EAAE6B,OAAO,CAACjB,CAAC,IAAIA,CAAC,CAACF,QAAQ,GAAG,KAAK,CAAC;EAC1D;EAEA;EACAoB,aAAaA,CAAA;IACX,IAAI,CAAC7B,WAAW,CAAC2B,IAAI,EAAE;EACzB;EAEA;EACAG,gBAAgBA,CAAChB,QAAkB;IACjC,IAAI,CAACP,gBAAgB,GAAGO,QAAQ;IAChC,IAAI,CAACb,cAAc,CAAC0B,IAAI,CAACb,QAAQ,CAAC;EACpC;EAEA;EACAiB,gBAAgBA,CAACC,UAAkB;IACjC,IAAIC,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAAC9B,cAAc,CAACwB,IAAI,CAACK,UAAU,CAAC;IACtC;EACF;EAEA;EACAE,mBAAmBA,CAAA;IACjB,IAAI,CAAC3B,gBAAgB,GAAG,IAAI;EAC9B;EAEA;EACA,IAAI4B,sBAAsBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC5B,gBAAgB,EAAE;MAC1B,OAAO,EAAE;IACX;IACA,OAAO,IAAI,CAACT,eAAe,CAACY,MAAM,CAAC0B,CAAC,IAAIA,CAAC,CAACjB,UAAU,KAAK,IAAI,CAACZ,gBAAiB,CAACY,UAAU,CAAC;EAC7F;CACD;AArEUkB,UAAA,EAAR7C,KAAK,EAAE,C,yDAA4B;AAC3B6C,UAAA,EAAR7C,KAAK,EAAE,C,+DAAwC;AACvC6C,UAAA,EAAR7C,KAAK,EAAE,C,0DAAwB;AACtB6C,UAAA,EAAT5C,MAAM,EAAE,C,2DAA4C;AAC3C4C,UAAA,EAAT5C,MAAM,EAAE,C,8DAA+C;AAC9C4C,UAAA,EAAT5C,MAAM,EAAE,C,4DAAsF;AACrF4C,UAAA,EAAT5C,MAAM,EAAE,C,8DAA6C;AAP3CE,uBAAuB,GAAA0C,UAAA,EAPnC/C,SAAS,CAAC;EACTgD,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,kCAAkC,CAAC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChD,WAAW;CACtB,CAAC,C,EACWC,uBAAuB,CAsEnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}