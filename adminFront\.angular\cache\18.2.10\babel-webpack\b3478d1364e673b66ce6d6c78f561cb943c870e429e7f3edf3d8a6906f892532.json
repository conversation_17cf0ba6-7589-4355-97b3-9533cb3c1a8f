{"ast": null, "code": "import { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as moment from 'moment';\nimport { CommonModule } from '@angular/common';\nimport { CalendarModule, CalendarView } from 'angular-calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { BehaviorSubject, concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@nebular/theme\";\nimport * as i6 from \"src/app/shared/services/event.service\";\nimport * as i7 from \"angular-calendar\";\nfunction EditAvailableTimeSlotComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogConfirm_r4 = i0.ɵɵreference(32);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogConfirm_r4));\n    });\n    i0.ɵɵtext(1, \" \\u8907\\u88FD\\u4E0A\\u4E00\\u5468\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, segment_r5.date, \"HH:mm\"));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵelementStart(2, \"input\", 25);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_27_div_2_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const segment_r5 = i0.ɵɵnextContext().segment;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheck($event, segment_r5.date));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const segment_r5 = i0.ɵɵnextContext().segment;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"className\", i0.ɵɵpipeBind1(1, 4, ctx_r2.listEvent$) && ctx_r2.isCellEmpty(segment_r5.date) ? \"bg-event\" : \"bg-inherit\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", segment_r5.date.toISOString())(\"checked\", i0.ɵɵpipeBind1(3, 6, ctx_r2.listEvent$) && ctx_r2.isCellEmpty(segment_r5.date))(\"disabled\", i0.ɵɵpipeBind1(4, 8, ctx_r2.listEvent$) && ctx_r2.checkDisable(segment_r5.date));\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, EditAvailableTimeSlotComponent_ng_template_27_span_1_Template, 3, 4, \"span\", 22)(2, EditAvailableTimeSlotComponent_ng_template_27_div_2_Template, 5, 10, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const isTimeLabel_r7 = ctx.isTimeLabel;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", isTimeLabel_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !isTimeLabel_r7);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 26);\n  }\n}\nfunction EditAvailableTimeSlotComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\", 27);\n    i0.ɵɵtext(2, \" \\u8ACB\\u78BA\\u8A8D\\u662F\\u5426\\u8A2D\\u5B9A\\u70BA\\u4E0A\\u5468\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-footer\", 28)(4, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_4_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      return i0.ɵɵresetView(ref_r9.close());\n    });\n    i0.ɵɵtext(5, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_6_listener() {\n      const ref_r9 = i0.ɵɵrestoreView(_r8).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.confirmCopy(ref_r9));\n    });\n    i0.ɵɵtext(7, \"\\u78BA\\u8A8D\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class EditAvailableTimeSlotComponent {\n  constructor(changeDetector, preOderSettingService, route, _router, messageService, _location, dialogService, _eventService) {\n    this.changeDetector = changeDetector;\n    this.preOderSettingService = preOderSettingService;\n    this.route = route;\n    this._router = _router;\n    this.messageService = messageService;\n    this._location = _location;\n    this.dialogService = dialogService;\n    this._eventService = _eventService;\n    this.locale = 'zh';\n    this.getPreOderSettingRes = [];\n    this.activeDayIsOpen = true;\n    this.savePreOrderSetting = [];\n    this.listEvent$ = new BehaviorSubject([]);\n    this.view = CalendarView.Week;\n    this.viewDate = new Date();\n    this.paramInfo = null;\n    this.listDate = [];\n    this.test = 0;\n    this.buildCaseId = this.route.snapshot.paramMap.get('id');\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'));\n  }\n  ngOnInit() {\n    if (this.paramInfo) {\n      this.viewDate = new Date(this.paramInfo.CDateStart);\n    }\n    this.getPreOrderSetting().subscribe();\n  }\n  getPreOrderSetting() {\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : [];\n        this.initEvent();\n      }\n    }));\n  }\n  initEvent() {\n    let temp = [];\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n      this.getPreOderSettingRes.forEach(e => {\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n          let date = e.CDate ? new Date(e.CDate) : undefined;\n          date = date ? new Date(date.setMinutes(0)) : date;\n          date = date ? new Date(date.setSeconds(0)) : date;\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0;\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0;\n          temp.push({\n            id: e.CID,\n            CBuildCaseId: this.buildCaseId,\n            CDate: e.CDate,\n            CHour: e.CHour,\n            CIsDelete: false,\n            isChange: false,\n            start: startDate,\n            end: endDate,\n            display: \"background\",\n            IsOld: true\n          });\n        }\n      });\n    }\n    this.listEvent$.next(temp);\n  }\n  handleDateSelect(selectInfo) {\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\n      let temp = [...this.listEvent$.value];\n      const calendarApi = selectInfo.view.calendar;\n      calendarApi.unselect(); // clear date selection\n      calendarApi.addEvent({\n        start: selectInfo.startStr,\n        end: selectInfo.endStr,\n        display: 'background'\n      });\n      temp.push({\n        CBuildCaseId: this.buildCaseId,\n        CDate: selectInfo.startStr,\n        CHour: selectInfo.start.getHours(),\n        CIsDelete: false,\n        isChange: true,\n        start: selectInfo.start,\n        end: selectInfo.end\n      });\n      this.listEvent$.next(temp);\n    }\n  }\n  save() {\n    this.savedData(true).subscribe(res => {\n      this.backToList();\n    });\n  }\n  backToList() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this._location.back();\n  }\n  // Function to check if the cell is empty\n  isCellEmpty(day) {\n    const startHour = day.getHours();\n    let check = false;\n    this.listEvent$.value.some(event => {\n      // console.log(event);\n      if (event.CHour === startHour && new Date(event.start).getDate() === day.getDate() && new Date(event.start).getMonth() === day.getMonth() && new Date(event.start).getFullYear() === day.getFullYear() && !event.CIsDelete) check = true;\n    });\n    return check;\n  }\n  checkDisable(day) {\n    if (day.getTime() < new Date().getTime()) {\n      return true;\n    }\n    return false;\n  }\n  handleEventClick(clickInfo) {\n    let eventIndex = this.listEvent$.value.findIndex(x => typeof x.start === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime() && typeof x.end === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime());\n    if (eventIndex !== -1) {\n      this.listEvent$.value[eventIndex].CIsDelete = true;\n      this.listEvent$.value[eventIndex].isChange = true;\n      clickInfo.event.remove();\n    }\n  }\n  onCheck(e, segment) {\n    let eventIndex = this.listEvent$.value.findIndex(x => x.start.getFullYear() === segment.getFullYear() && x.start.getMonth() === segment.getMonth() && x.start.getDate() === segment.getDate() && x.CHour === segment.getHours());\n    // click empty checkbox\n    if (eventIndex === -1) {\n      // create new event\n      if (e.target.checked) {\n        let temp = [...this.listEvent$.value];\n        temp.push({\n          CBuildCaseId: this.buildCaseId,\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\n          CHour: segment.getHours(),\n          CIsDelete: false,\n          isChange: true,\n          start: segment,\n          IsOld: false\n        });\n        this.listEvent$.next(temp);\n      }\n    } else {\n      // unchecked checkbox\n      if (e.target.checked) {\n        this.listEvent$.value[eventIndex].CIsDelete = false;\n        this.listEvent$.value[eventIndex].isChange = true;\n      } else {\n        this.listEvent$.value[eventIndex].CIsDelete = true;\n        this.listEvent$.value[eventIndex].isChange = true;\n      }\n    }\n  }\n  addNew(ref) {\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\n      this.dialogService.open(ref);\n    } else {\n      this.getPreOderSettingRes = this.handleDataWeek().dataFromPrevious;\n      const updatedEvents = [...this.listEvent$.value];\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\n        updatedEvents.push({\n          CBuildCaseId: e.CBuildCaseId,\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete,\n          isChange: true,\n          start: this.add_weeks(e.CDate, 1),\n          IsOld: false\n        });\n      });\n      this.listEvent$.next(updatedEvents);\n      this.changeDetector.markForCheck();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  confirmCopy(ref) {\n    this.savedData(false).subscribe(() => this.onClose(ref));\n  }\n  handleDataWeek() {\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1);\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1);\n    let dataFromPrevious = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime() && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime());\n    let dataFromCurrent = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime() && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime());\n    return {\n      dataFromCurrent,\n      dataFromPrevious\n    };\n  }\n  handleShowButton() {\n    var result = new Date(this.viewDate);\n    result.setDate(result.getDate() + 7);\n    if (result.getTime() < Date.now()) {\n      return false;\n    }\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\n      return false;\n    }\n    return true;\n  }\n  savedData(needToFilter) {\n    if (needToFilter) {\n      this.savePreOrderSetting = this.listEvent$.value.filter(x => x.isChange === true).map(e => {\n        this.listDate.push(new Date(e.CDate));\n        return {\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: e.CDate,\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        };\n      });\n    } else {\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete\n          });\n        } else {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: true\n          });\n        }\n      });\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\n        this.savePreOrderSetting.push({\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        });\n      });\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\n        this.listDate.push(new Date(x.CDate));\n      });\n    }\n    if (this.savePreOrderSetting.length == 0) {\n      this.backToList();\n      return of();\n    }\n    let minDate = new Date(Math.min.apply(null, this.listDate));\n    let paramSave = {\n      buildCaseId: this.buildCaseId,\n      minDate: minDate\n    };\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\n      body: {\n        CSavePreOrderSetting: this.savePreOrderSetting\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.messageService.showSucessMSG('Save Successfully!');\n        this.savePreOrderSetting = [];\n        this.listDate = [];\n        this.listEvent$.value.forEach(x => {\n          if (x.isChange) {\n            let id = document.getElementById(x.start.toISOString());\n            setTimeout(() => {\n              id?.click();\n            }, 200);\n          }\n        });\n        LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave));\n      }\n    }), concatMap(() => this.getPreOrderSetting()));\n  }\n  add_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + n * 7));\n  }\n  sub_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - n * 7));\n  }\n  get_date(dt) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\n  }\n  fromDateToString(date) {\n    date = new Date(+date);\n    date.setTime(date.getTime() - date.getTimezoneOffset() * 60000);\n    let dateAsString = date.toISOString().substr(0, 19);\n    return dateAsString;\n  }\n  static {\n    this.ɵfac = function EditAvailableTimeSlotComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditAvailableTimeSlotComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PreOrderSettingService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.NbDialogService), i0.ɵɵdirectiveInject(i6.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditAvailableTimeSlotComponent,\n      selectors: [[\"app-edit-available-time-slot\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 33,\n      vars: 19,\n      consts: [[\"hourSegmentTemplate\", \"\"], [\"currentTimeMarkerTemplate\", \"\"], [\"dialogConfirm\", \"\"], [\"accent\", \"success\"], [1, \"w-full\", \"flex\", \"justify-end\", \"mb-4\"], [\"class\", \"text-white p-3 bg-[#169bd5] cursor-pointer rounded-md\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"text-left\", \"mb-4\"], [1, \"col-md-4\"], [1, \"btn-group\"], [\"mwlCalendarPreviousView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-left\"], [1, \"col-md-4\", \"text-center\"], [1, \"col-md-4\", \"text-right\"], [\"mwlCalendarNextView\", \"\", 1, \"btn\", \"btn-success\", 3, \"viewDateChange\", \"view\", \"viewDate\"], [1, \"fa\", \"fa-chevron-right\"], [3, \"hourDuration\", \"hourSegments\", \"viewDate\", \"locale\", \"dayStartHour\", \"dayEndHour\", \"hourSegmentTemplate\", \"currentTimeMarkerTemplate\", \"eventSnapSize\"], [1, \"row\"], [1, \"col-md-12\", \"text-center\"], [1, \"btn\", \"btn-secondary\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-1\", 3, \"click\"], [1, \"text-white\", \"p-3\", \"bg-[#169bd5]\", \"cursor-pointer\", \"rounded-md\", 3, \"click\"], [1, \"hour-segment\", \"text-center\", \"border\", 2, \"height\", \"100%\"], [4, \"ngIf\"], [\"style\", \"height: 100%;\", 3, \"className\", 4, \"ngIf\"], [2, \"height\", \"100%\", 3, \"className\"], [\"type\", \"checkbox\", 1, \"align-middle\", 3, \"click\", \"id\", \"checked\", \"disabled\"], [2, \"display\", \"none\"], [1, \"px-4\"], [1, \"flex\", \"justify-center\", \"w-full\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n      template: function EditAvailableTimeSlotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 4);\n          i0.ɵɵtemplate(5, EditAvailableTimeSlotComponent_div_5_Template, 2, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9);\n          i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelement(10, \"i\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 11)(12, \"h2\");\n          i0.ɵɵtext(13);\n          i0.ɵɵpipe(14, \"calendarDate\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 8)(17, \"div\", 13);\n          i0.ɵɵtwoWayListener(\"viewDateChange\", function EditAvailableTimeSlotComponent_Template_div_viewDateChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.viewDate, $event) || (ctx.viewDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelement(18, \"i\", 14);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(19, \"mwl-calendar-week-view\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-card-body\")(21, \"div\", 16)(22, \"div\", 17)(23, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.backToList());\n          });\n          i0.ɵɵtext(24, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function EditAvailableTimeSlotComponent_Template_button_click_25_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.save());\n          });\n          i0.ɵɵtext(26, \"\\u5132\\u5B58\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(27, EditAvailableTimeSlotComponent_ng_template_27_Template, 3, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(29, EditAvailableTimeSlotComponent_ng_template_29_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(31, EditAvailableTimeSlotComponent_ng_template_31_Template, 8, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const hourSegmentTemplate_r10 = i0.ɵɵreference(28);\n          const currentTimeMarkerTemplate_r11 = i0.ɵɵreference(30);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.handleShowButton());\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"view\", ctx.view);\n          i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(14, 15, ctx.viewDate, ctx.view + \"ViewTitle\", \"zh\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"view\", ctx.view);\n          i0.ɵɵtwoWayProperty(\"viewDate\", ctx.viewDate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hourDuration\", 60)(\"hourSegments\", 1)(\"viewDate\", ctx.viewDate)(\"locale\", ctx.locale)(\"dayStartHour\", 9)(\"dayEndHour\", 21)(\"hourSegmentTemplate\", hourSegmentTemplate_r10)(\"currentTimeMarkerTemplate\", currentTimeMarkerTemplate_r11)(\"eventSnapSize\", 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgIf, i4.AsyncPipe, i4.DatePipe, SharedModule, i5.NbCardComponent, i5.NbCardBodyComponent, i5.NbCardFooterComponent, i5.NbCardHeaderComponent, CalendarModule, i7.ɵCalendarPreviousViewDirective, i7.ɵCalendarNextViewDirective, i7.ɵCalendarDatePipe, i7.CalendarWeekViewComponent, DialogModule],\n      styles: [\".empty-cell[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.empty-cell[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: blue;\\n  text-decoration: underline;\\n  cursor: pointer;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n  box-shadow: none;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:after {\\n  box-shadow: none;\\n  border: 1px solid black;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:before {\\n  color: transparent !important;\\n  border: 2px solid black;\\n}\\n\\ninput[type=checkbox][_ngcontent-%COMP%]:checked:before {\\n  color: black !important;\\n}\\n\\n.bg-inherit[_ngcontent-%COMP%] {\\n  background-color: inherit !important;\\n}\\n\\n.bg-event[_ngcontent-%COMP%] {\\n  background-color: #89d0ff !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVkaXQtYXZhaWxhYmxlLXRpbWUtc2xvdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxlQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0VBQ0EsWUFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNJLDZCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNDLHVCQUFBO0FBQ0Q7O0FBRUE7RUFDSSxvQ0FBQTtBQUNKOztBQUVBO0VBQ0ksb0NBQUE7QUFDSiIsImZpbGUiOiJlZGl0LWF2YWlsYWJsZS10aW1lLXNsb3QuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIuZW1wdHktY2VsbCB7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbn1cclxuXHJcbi5lbXB0eS1jZWxsIGEge1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgdG9wOiA1MCU7XHJcbiAgICBsZWZ0OiA1MCU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcclxuICAgIGNvbG9yOiBibHVlO1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdIHtcclxuICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgaGVpZ2h0OiAyMHB4O1xyXG4gICAgY29sb3I6ICNmZmZmZmZjYztcclxuICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmFmdGVyIHtcclxuICAgIGJveC1zaGFkb3c6IG5vbmU7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCBibGFjaztcclxufVxyXG5cclxuaW5wdXRbdHlwZT1jaGVja2JveF06YmVmb3JlIHtcclxuICAgIGNvbG9yOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50O1xyXG4gICAgYm9yZGVyOiAycHggc29saWQgYmxhY2s7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmNoZWNrZWQ6YmVmb3JlIHtcclxuXHRjb2xvcjogYmxhY2sgIWltcG9ydGFudDtcclxufVxyXG5cclxuLmJnLWluaGVyaXR7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiBpbmhlcml0ICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5iZy1ldmVudCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjODlkMGZmICFpbXBvcnRhbnQ7XHJcbn0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVzZXJ2YXRpb24tdGltZS1tYW5hZ2VtZW50L2F2YWlsYWJsZS10aW1lLXNsb3QvZWRpdC1hdmFpbGFibGUtdGltZS1zbG90L2VkaXQtYXZhaWxhYmxlLXRpbWUtc2xvdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxrQkFBQTtFQUNBLFFBQUE7RUFDQSxTQUFBO0VBQ0EsZ0NBQUE7RUFDQSxXQUFBO0VBQ0EsMEJBQUE7RUFDQSxlQUFBO0FBQ0o7O0FBRUE7RUFDSSxXQUFBO0VBQ0EsWUFBQTtFQUNBLCtCQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNJLDZCQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFFQTtFQUNDLHVCQUFBO0FBQ0Q7O0FBRUE7RUFDSSxvQ0FBQTtBQUNKOztBQUVBO0VBQ0ksb0NBQUE7QUFDSjtBQUNBLHdzREFBd3NEIiwic291cmNlc0NvbnRlbnQiOlsiLmVtcHR5LWNlbGwge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcblxyXG4uZW1wdHktY2VsbCBhIHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogNTAlO1xyXG4gICAgbGVmdDogNTAlO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XHJcbiAgICBjb2xvcjogYmx1ZTtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XSB7XHJcbiAgICB3aWR0aDogMjBweDtcclxuICAgIGhlaWdodDogMjBweDtcclxuICAgIGNvbG9yOiAjZmZmZmZmY2M7XHJcbiAgICBib3gtc2hhZG93OiBub25lO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XTphZnRlciB7XHJcbiAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgYmxhY2s7XHJcbn1cclxuXHJcbmlucHV0W3R5cGU9Y2hlY2tib3hdOmJlZm9yZSB7XHJcbiAgICBjb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDtcclxuICAgIGJvcmRlcjogMnB4IHNvbGlkIGJsYWNrO1xyXG59XHJcblxyXG5pbnB1dFt0eXBlPWNoZWNrYm94XTpjaGVja2VkOmJlZm9yZSB7XHJcblx0Y29sb3I6IGJsYWNrICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5iZy1pbmhlcml0e1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogaW5oZXJpdCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uYmctZXZlbnQge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzg5ZDBmZiAhaW1wb3J0YW50O1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["LocalStorageService", "moment", "CommonModule", "CalendarModule", "CalendarView", "DialogModule", "BehaviorSubject", "concatMap", "of", "tap", "SharedModule", "EEvent", "i0", "ɵɵelementStart", "ɵɵlistener", "EditAvailableTimeSlotComponent_div_5_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "dialogConfirm_r4", "ɵɵreference", "ɵɵresetView", "addNew", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "segment_r5", "date", "EditAvailableTimeSlotComponent_ng_template_27_div_2_Template_input_click_2_listener", "$event", "_r6", "segment", "onCheck", "ɵɵproperty", "ɵɵpipeBind1", "listEvent$", "isCellEmpty", "toISOString", "checkDisable", "ɵɵtemplate", "EditAvailableTimeSlotComponent_ng_template_27_span_1_Template", "EditAvailableTimeSlotComponent_ng_template_27_div_2_Template", "isTimeLabel_r7", "ɵɵelement", "EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_4_listener", "ref_r9", "_r8", "dialogRef", "close", "EditAvailableTimeSlotComponent_ng_template_31_Template_button_click_6_listener", "confirmCopy", "EditAvailableTimeSlotComponent", "constructor", "changeDetector", "preOderSettingService", "route", "_router", "messageService", "_location", "dialogService", "_eventService", "locale", "getPreOderSettingRes", "activeDayIsOpen", "savePreOrderSetting", "view", "Week", "viewDate", "Date", "paramInfo", "listDate", "test", "buildCaseId", "snapshot", "paramMap", "get", "JSON", "parse", "GetLocalStorage", "ngOnInit", "CDateStart", "getPreOrderSetting", "subscribe", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "pipe", "res", "StatusCode", "Entries", "filter", "x", "CHour", "initEvent", "temp", "length", "for<PERSON>ach", "e", "CDate", "undefined", "setMinutes", "setSeconds", "startDate", "setHours", "endDate", "push", "id", "CID", "CBuildCaseId", "CIsDelete", "isChange", "start", "end", "display", "IsOld", "next", "handleDateSelect", "selectInfo", "getDate", "getHours", "value", "calendarApi", "calendar", "unselect", "addEvent", "startStr", "endStr", "save", "savedData", "backToList", "action", "payload", "back", "day", "startHour", "check", "some", "event", "getMonth", "getFullYear", "getTime", "handleEventClick", "clickInfo", "eventIndex", "findIndex", "remove", "target", "checked", "format", "ref", "handleDataWeek", "dataFromCurrent", "open", "dataFromPrevious", "updatedEvents", "fromDateToString", "add_weeks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClose", "firstDateofLastWeek", "sub_weeks", "lastDateofCurrentWeek", "handleShowButton", "result", "setDate", "now", "needToFilter", "map", "parseInt", "get_date", "minDate", "Math", "min", "apply", "paramSave", "apiPreOrderSettingSavePreOrderSettingPost$Json", "CSavePreOrderSetting", "showSucessMSG", "document", "getElementById", "setTimeout", "click", "AddLocalStorage", "stringify", "dt", "n", "setTime", "getTimezoneOffset", "dateAsString", "substr", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "PreOrderSettingService", "i2", "ActivatedRoute", "Router", "i3", "MessageService", "i4", "Location", "i5", "NbDialogService", "i6", "EventService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EditAvailableTimeSlotComponent_Template", "rf", "ctx", "EditAvailableTimeSlotComponent_div_5_Template", "ɵɵtwoWayListener", "EditAvailableTimeSlotComponent_Template_div_viewDateChange_9_listener", "_r1", "ɵɵtwoWayBindingSet", "EditAvailableTimeSlotComponent_Template_div_viewDateChange_17_listener", "EditAvailableTimeSlotComponent_Template_button_click_23_listener", "EditAvailableTimeSlotComponent_Template_button_click_25_listener", "EditAvailableTimeSlotComponent_ng_template_27_Template", "ɵɵtemplateRefExtractor", "EditAvailableTimeSlotComponent_ng_template_29_Template", "EditAvailableTimeSlotComponent_ng_template_31_Template", "ɵɵtwoWayProperty", "ɵɵpipeBind3", "hourSegmentTemplate_r10", "currentTimeMarkerTemplate_r11", "NgIf", "AsyncPipe", "DatePipe", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "i7", "ɵCalendarPreviousViewDirective", "ɵCalendarNextViewDirective", "ɵCalendarDatePipe", "CalendarWeekViewComponent", "styles", "changeDetection"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\edit-available-time-slot\\edit-available-time-slot.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\edit-available-time-slot\\edit-available-time-slot.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';\r\nimport { PreOrderSettingService } from 'src/services/api/services';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { GetPreOrderSettingResponse, PreOrderSetting } from 'src/services/api/models';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { CalendarModule, CalendarView } from 'angular-calendar';\r\nimport { DialogModule } from 'primeng/dialog'\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BehaviorSubject, concatMap, mergeMap, of, tap } from 'rxjs';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'app-edit-available-time-slot',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    CalendarModule,\r\n    DialogModule\r\n  ],\r\n  templateUrl: './edit-available-time-slot.component.html',\r\n  styleUrls: ['./edit-available-time-slot.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class EditAvailableTimeSlotComponent implements OnInit {\r\n  locale: string = 'zh'\r\n  buildCaseId: any | null\r\n  getPreOderSettingRes = [] as GetPreOrderSettingResponse[]\r\n  activeDayIsOpen: boolean = true;\r\n  savePreOrderSetting = [] as PreOrderSetting[]\r\n\r\n  listEvent$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);\r\n\r\n  view: CalendarView = CalendarView.Week;\r\n  viewDate: Date = new Date();\r\n  paramInfo: any = null\r\n  listDate: any = []\r\n  test = 0;\r\n\r\n  constructor(\r\n    private changeDetector: ChangeDetectorRef,\r\n    private preOderSettingService: PreOrderSettingService,\r\n    private route: ActivatedRoute,\r\n    private _router: Router,\r\n    private messageService: MessageService,\r\n    private _location: Location,\r\n    private dialogService: NbDialogService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    this.buildCaseId = this.route.snapshot.paramMap.get('id')\r\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'))\r\n  }\r\n\r\n  ngOnInit() {\r\n    if (this.paramInfo) {\r\n      this.viewDate = new Date(this.paramInfo.CDateStart)\r\n    }\r\n    this.getPreOrderSetting().subscribe()\r\n  }\r\n\r\n  getPreOrderSetting() {\r\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : []\r\n          this.initEvent()\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  initEvent() {\r\n    let temp : any[] = [];\r\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\r\n      this.getPreOderSettingRes.forEach(e => {\r\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\r\n          let date = e.CDate ? new Date(e.CDate) : undefined;\r\n          date = date ? new Date(date.setMinutes(0)) : date\r\n          date = date ? new Date(date.setSeconds(0)) : date\r\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0\r\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0\r\n          temp.push({\r\n            id: e.CID,\r\n            CBuildCaseId: this.buildCaseId,\r\n            CDate: e.CDate,\r\n            CHour: e.CHour,\r\n            CIsDelete: false,\r\n            isChange: false,\r\n            start: startDate,\r\n            end: endDate,\r\n            display: \"background\",\r\n            IsOld: true\r\n          })\r\n        }\r\n      })\r\n    }\r\n    this.listEvent$.next(temp);\r\n  }\r\n\r\n  handleDateSelect(selectInfo: any) {\r\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\r\n      let temp = [...this.listEvent$.value];\r\n      const calendarApi = selectInfo.view.calendar;\r\n\r\n      calendarApi.unselect(); // clear date selection\r\n\r\n      calendarApi.addEvent({\r\n        start: selectInfo.startStr,\r\n        end: selectInfo.endStr,\r\n        display: 'background',\r\n      });\r\n\r\n      temp.push({\r\n        CBuildCaseId: this.buildCaseId,\r\n        CDate: selectInfo.startStr,\r\n        CHour: selectInfo.start.getHours(),\r\n        CIsDelete: false,\r\n        isChange: true,\r\n        start: selectInfo.start,\r\n        end: selectInfo.end,\r\n      })\r\n      this.listEvent$.next(temp);\r\n    }\r\n  }\r\n\r\n  save() {\r\n    this.savedData(true).subscribe(res => {\r\n      this.backToList()\r\n    })\r\n  }\r\n\r\n  backToList() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  // Function to check if the cell is empty\r\n  isCellEmpty(day: Date): boolean {\r\n    const startHour = day.getHours();\r\n    let check = false;\r\n    this.listEvent$.value.some(event => {\r\n      // console.log(event);\r\n      if (event.CHour === startHour\r\n        && new Date(event.start).getDate() === day.getDate()\r\n        && new Date(event.start).getMonth() === day.getMonth()\r\n        && new Date(event.start).getFullYear() === day.getFullYear()\r\n        && !event.CIsDelete\r\n      )\r\n        check = true;\r\n    });\r\n    return check;\r\n  }\r\n\r\n  checkDisable(day: Date) {\r\n    if (day.getTime() < new Date().getTime()) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  handleEventClick(clickInfo: any) {\r\n    let eventIndex = this.listEvent$.value.findIndex(x => typeof (x.start) === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime()\r\n      && typeof (x.end) === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime())\r\n    if (eventIndex !== -1) {\r\n      this.listEvent$.value[eventIndex].CIsDelete = true\r\n      this.listEvent$.value[eventIndex].isChange = true\r\n      clickInfo.event.remove();\r\n    }\r\n  }\r\n\r\n  onCheck(e: any, segment: Date) {\r\n    let eventIndex = this.listEvent$.value.findIndex(x => x.start.getFullYear() === segment.getFullYear()\r\n      && x.start.getMonth() === segment.getMonth()\r\n      && x.start.getDate() === segment.getDate()\r\n      && x.CHour === segment.getHours())\r\n\r\n    // click empty checkbox\r\n    if (eventIndex === -1) {\r\n      // create new event\r\n      if (e.target.checked) {\r\n        let temp = [...this.listEvent$.value]\r\n        temp.push({\r\n          CBuildCaseId: this.buildCaseId,\r\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\r\n          CHour: segment.getHours(),\r\n          CIsDelete: false,\r\n          isChange: true,\r\n          start: segment,\r\n          IsOld: false\r\n        })\r\n        this.listEvent$.next(temp);\r\n      }\r\n    } else { // unchecked checkbox\r\n      if (e.target.checked) {\r\n        this.listEvent$.value[eventIndex].CIsDelete = false\r\n        this.listEvent$.value[eventIndex].isChange = true\r\n      } else {\r\n        this.listEvent$.value[eventIndex].CIsDelete = true\r\n        this.listEvent$.value[eventIndex].isChange = true\r\n      }\r\n    }\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\r\n      this.dialogService.open(ref);\r\n    } else {\r\n      this.getPreOderSettingRes = this.handleDataWeek().dataFromPrevious;\r\n      const updatedEvents = [...this.listEvent$.value];\r\n      this.handleDataWeek().dataFromPrevious.forEach((e) => {\r\n        updatedEvents.push({\r\n          CBuildCaseId: e.CBuildCaseId,\r\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete,\r\n          isChange: true,\r\n          start: this.add_weeks(e.CDate, 1),\r\n          IsOld: false\r\n        });\r\n      });\r\n      this.listEvent$.next(updatedEvents);\r\n      this.changeDetector.markForCheck();\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close()\r\n  }\r\n\r\n  confirmCopy(ref: any) {\r\n    this.savedData(false).subscribe(() => this.onClose(ref))\r\n  }\r\n\r\n  handleDataWeek() {\r\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1)\r\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1)\r\n\r\n    let dataFromPrevious = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime()\r\n      && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime())\r\n\r\n    let dataFromCurrent = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime()\r\n      && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime())\r\n\r\n    return { dataFromCurrent, dataFromPrevious }\r\n  }\r\n\r\n  handleShowButton() {\r\n    var result = new Date(this.viewDate);\r\n    result.setDate(result.getDate() + 7);\r\n    if (result.getTime() < Date.now()) {\r\n      return false;\r\n    }\r\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\r\n      return false;\r\n    }\r\n    return true\r\n  }\r\n\r\n  savedData(needToFilter: boolean) {\r\n    if (needToFilter) {\r\n      this.savePreOrderSetting = this.listEvent$.value.filter(x => x.isChange === true).map(e => {\r\n        this.listDate.push(new Date(e.CDate))\r\n        return {\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: e.CDate,\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        }\r\n      })\r\n    } else {\r\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\r\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: e.CIsDelete\r\n          })\r\n        } else {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: true\r\n          })\r\n        }\r\n      })\r\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\r\n        this.savePreOrderSetting.push({\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        })\r\n      })\r\n\r\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\r\n        this.listDate.push(new Date(x.CDate!))\r\n      })\r\n    }\r\n    if (this.savePreOrderSetting.length == 0) {\r\n      this.backToList()\r\n      return of();\r\n    }\r\n    let minDate = new Date(Math.min.apply(null, this.listDate))\r\n    let paramSave = {\r\n      buildCaseId: this.buildCaseId,\r\n      minDate: minDate\r\n    }\r\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\r\n      body: {\r\n        CSavePreOrderSetting: this.savePreOrderSetting\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.messageService.showSucessMSG('Save Successfully!')\r\n          this.savePreOrderSetting = []\r\n          this.listDate = []\r\n          this.listEvent$.value.forEach(x => {\r\n            if (x.isChange) {\r\n              let id = document.getElementById(x.start.toISOString())\r\n              setTimeout(() => {\r\n                id?.click()\r\n              }, 200);\r\n            }\r\n          });\r\n          LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave))\r\n        }\r\n      }),\r\n      concatMap(() => this.getPreOrderSetting()),\r\n    )\r\n  }\r\n\r\n  add_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + (n * 7)));\r\n  }\r\n\r\n  sub_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - (n * 7)));\r\n  }\r\n\r\n  get_date(dt: Date) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\r\n  }\r\n\r\n  fromDateToString(date: any) {\r\n    date = new Date(+date);\r\n    date.setTime(date.getTime() - (date.getTimezoneOffset() * 60000));\r\n    let dateAsString = date.toISOString().substr(0, 19);\r\n    return dateAsString;\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"w-full flex justify-end mb-4\">\r\n      <div *ngIf=\"handleShowButton()\" class=\"text-white p-3 bg-[#169bd5] cursor-pointer rounded-md\"\r\n        (click)=\"addNew(dialogConfirm)\">\r\n        複製上一周設定\r\n      </div>\r\n    </div>\r\n    <div class=\"row text-left mb-4\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"btn-group\">\r\n          <div class=\"btn btn-success\" mwlCalendarPreviousView [view]=\"view\" [(viewDate)]=\"viewDate\">\r\n            <i class=\"fa fa-chevron-left\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-4 text-center\">\r\n        <h2>{{ viewDate | calendarDate:(view + 'ViewTitle'):'zh' }}</h2>\r\n      </div>\r\n      <div class=\"col-md-4 text-right\">\r\n        <div class=\"btn-group\">\r\n          <div class=\"btn btn-success\" mwlCalendarNextView [view]=\"view\" [(viewDate)]=\"viewDate\">\r\n            <i class=\"fa fa-chevron-right\"></i>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <mwl-calendar-week-view [hourDuration]=\"60\" [hourSegments]=\"1\" [viewDate]=\"viewDate\" [locale]=\"locale\"\r\n      [dayStartHour]=\"9\" [dayEndHour]=\"21\" [hourSegmentTemplate]=\"hourSegmentTemplate\"\r\n      [currentTimeMarkerTemplate]=\"currentTimeMarkerTemplate\" [eventSnapSize]=\"0\">\r\n    </mwl-calendar-week-view>\r\n  </nb-card-body>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-12 text-center\">\r\n        <button class=\"btn btn-secondary m-1\" (click)=\"backToList()\">取消</button>\r\n        <button class=\"btn btn-success m-1\" (click)=\"save()\">儲存</button>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<ng-template #hourSegmentTemplate let-isTimeLabel=\"isTimeLabel\" let-segment=\"segment\">\r\n  <div class=\"hour-segment text-center border\" style=\"height: 100%;\">\r\n    <span *ngIf=\"isTimeLabel\">{{ segment.date | date: 'HH:mm' }}</span>\r\n    <div *ngIf=\"!isTimeLabel\" [className]=\"(listEvent$ | async) && isCellEmpty(segment.date) ? 'bg-event' : 'bg-inherit'\" style=\"height: 100%;\">\r\n      <input [id]=\"segment.date.toISOString()\" class=\"align-middle\" type=\"checkbox\" [checked]=\"(listEvent$ | async) && isCellEmpty(segment.date)\"\r\n        [disabled]=\"(listEvent$ | async) && checkDisable(segment.date)\" (click)=\"onCheck($event, segment.date)\" />\r\n    </div>\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #currentTimeMarkerTemplate>\r\n  <div style=\"display: none;\">\r\n  </div>\r\n</ng-template>\r\n\r\n<ng-template #dialogConfirm let-dialog let-ref=\"dialogRef\">\r\n  <nb-card>\r\n    <nb-card-body class=\"px-4\">\r\n      請確認是否設定為上周時段設定\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"flex justify-center w-full\">\r\n      <button class=\"btn btn-outline-secondary m-2\" (click)=\"ref.close()\">取消</button>\r\n      <button class=\"btn btn-success m-2\" (click)=\"confirmCopy(ref)\">確認</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,+CAA+C;AAInF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AAC/D,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,eAAe,EAAEC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACpE,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;ICPtEC,EAAA,CAAAC,cAAA,cACkC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAC,mEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,gBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,gBAAA,CAAqB;IAAA,EAAC;IAC/BR,EAAA,CAAAY,MAAA,mDACF;IAAAZ,EAAA,CAAAa,YAAA,EAAM;;;;;IAsCRb,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAY,MAAA,GAAkC;;IAAAZ,EAAA,CAAAa,YAAA,EAAO;;;;IAAzCb,EAAA,CAAAc,SAAA,EAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,OAAAC,UAAA,CAAAC,IAAA,WAAkC;;;;;;IAC5DlB,EAAA,CAAAC,cAAA,cAA4I;;IAC1ID,EAAA,CAAAC,cAAA,gBAC4G;;;IAA1CD,EAAA,CAAAE,UAAA,mBAAAiB,oFAAAC,MAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAJ,UAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAe,OAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAiB,OAAA,CAAAH,MAAA,EAAAH,UAAA,CAAAC,IAAA,CAA6B;IAAA,EAAC;IAC3GlB,EAFE,CAAAa,YAAA,EAC4G,EACxG;;;;;IAHoBb,EAAA,CAAAwB,UAAA,cAAAxB,EAAA,CAAAyB,WAAA,OAAAnB,MAAA,CAAAoB,UAAA,KAAApB,MAAA,CAAAqB,WAAA,CAAAV,UAAA,CAAAC,IAAA,8BAA2F;IAC5GlB,EAAA,CAAAc,SAAA,GAAiC;IACtCd,EADK,CAAAwB,UAAA,OAAAP,UAAA,CAAAC,IAAA,CAAAU,WAAA,GAAiC,YAAA5B,EAAA,CAAAyB,WAAA,OAAAnB,MAAA,CAAAoB,UAAA,KAAApB,MAAA,CAAAqB,WAAA,CAAAV,UAAA,CAAAC,IAAA,EAAmG,aAAAlB,EAAA,CAAAyB,WAAA,OAAAnB,MAAA,CAAAoB,UAAA,KAAApB,MAAA,CAAAuB,YAAA,CAAAZ,UAAA,CAAAC,IAAA,EAC1E;;;;;IAJrElB,EAAA,CAAAC,cAAA,cAAmE;IAEjED,EADA,CAAA8B,UAAA,IAAAC,6DAAA,mBAA0B,IAAAC,4DAAA,mBACkH;IAI9IhC,EAAA,CAAAa,YAAA,EAAM;;;;IALGb,EAAA,CAAAc,SAAA,EAAiB;IAAjBd,EAAA,CAAAwB,UAAA,SAAAS,cAAA,CAAiB;IAClBjC,EAAA,CAAAc,SAAA,EAAkB;IAAlBd,EAAA,CAAAwB,UAAA,UAAAS,cAAA,CAAkB;;;;;IAQ1BjC,EAAA,CAAAkC,SAAA,cACM;;;;;;IAKJlC,EADF,CAAAC,cAAA,cAAS,uBACoB;IACzBD,EAAA,CAAAY,MAAA,6FACF;IAAAZ,EAAA,CAAAa,YAAA,EAAe;IAEbb,EADF,CAAAC,cAAA,yBAAmD,iBACmB;IAAtBD,EAAA,CAAAE,UAAA,mBAAAiC,+EAAA;MAAA,MAAAC,MAAA,GAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,OAAAtC,EAAA,CAAAU,WAAA,CAAS0B,MAAA,CAAAG,KAAA,EAAW;IAAA,EAAC;IAACvC,EAAA,CAAAY,MAAA,mBAAE;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAC/Eb,EAAA,CAAAC,cAAA,iBAA+D;IAA3BD,EAAA,CAAAE,UAAA,mBAAAsC,+EAAA;MAAA,MAAAJ,MAAA,GAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA,EAAAC,SAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAmC,WAAA,CAAAL,MAAA,CAAgB;IAAA,EAAC;IAACpC,EAAA,CAAAY,MAAA,mBAAE;IAErEZ,EAFqE,CAAAa,YAAA,EAAS,EAC3D,EACT;;;ADzCZ,OAAM,MAAO6B,8BAA8B;EAezCC,YACUC,cAAiC,EACjCC,qBAA6C,EAC7CC,KAAqB,EACrBC,OAAe,EACfC,cAA8B,EAC9BC,SAAmB,EACnBC,aAA8B,EAC9BC,aAA2B;IAP3B,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAtBvB,KAAAC,MAAM,GAAW,IAAI;IAErB,KAAAC,oBAAoB,GAAG,EAAkC;IACzD,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,mBAAmB,GAAG,EAAuB;IAE7C,KAAA7B,UAAU,GAA2B,IAAIhC,eAAe,CAAQ,EAAE,CAAC;IAEnE,KAAA8D,IAAI,GAAiBhE,YAAY,CAACiE,IAAI;IACtC,KAAAC,QAAQ,GAAS,IAAIC,IAAI,EAAE;IAC3B,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,IAAI,GAAG,CAAC;IAYN,IAAI,CAACC,WAAW,GAAG,IAAI,CAACjB,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACzD,IAAI,CAACN,SAAS,GAAGO,IAAI,CAACC,KAAK,CAAChF,mBAAmB,CAACiF,eAAe,CAAC,WAAW,CAAC,CAAC;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,EAAE;MAClB,IAAI,CAACF,QAAQ,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACC,SAAS,CAACW,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,kBAAkB,EAAE,CAACC,SAAS,EAAE;EACvC;EAEAD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC3B,qBAAqB,CAAC6B,6CAA6C,CAAC;MAC9EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACb;;KAEtB,CAAC,CAACc,IAAI,CACLhF,GAAG,CAACiF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1B,oBAAoB,GAAGyB,GAAG,CAACE,OAAO,GAAGF,GAAG,CAACE,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAACC,KAAK,GAAG,CAAC,IAAID,CAAC,CAACC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;QACpH,IAAI,CAACC,SAAS,EAAE;MAClB;IACF,CAAC,CAAC,CACH;EACH;EAEAA,SAASA,CAAA;IACP,IAAIC,IAAI,GAAW,EAAE;IACrB,IAAI,IAAI,CAAChC,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACiC,MAAM,GAAG,CAAC,EAAE;MACrE,IAAI,CAACjC,oBAAoB,CAACkC,OAAO,CAACC,CAAC,IAAG;QACpC,IAAIA,CAAC,CAACL,KAAK,IAAIK,CAAC,CAACL,KAAK,GAAG,CAAC,IAAIK,CAAC,CAACL,KAAK,GAAG,EAAE,EAAE;UAC1C,IAAIjE,IAAI,GAAGsE,CAAC,CAACC,KAAK,GAAG,IAAI9B,IAAI,CAAC6B,CAAC,CAACC,KAAK,CAAC,GAAGC,SAAS;UAClDxE,IAAI,GAAGA,IAAI,GAAG,IAAIyC,IAAI,CAACzC,IAAI,CAACyE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGzE,IAAI;UACjDA,IAAI,GAAGA,IAAI,GAAG,IAAIyC,IAAI,CAACzC,IAAI,CAAC0E,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG1E,IAAI;UACjD,IAAI2E,SAAS,GAAG3E,IAAI,IAAIsE,CAAC,CAACL,KAAK,GAAG,IAAIxB,IAAI,CAACzC,IAAI,CAAC4E,QAAQ,CAACN,CAAC,CAACL,KAAK,CAAC,CAAC,GAAG,CAAC;UACtE,IAAIY,OAAO,GAAG7E,IAAI,IAAIsE,CAAC,CAACL,KAAK,GAAG,IAAIxB,IAAI,CAACzC,IAAI,CAAC4E,QAAQ,CAACN,CAAC,CAACL,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;UACxEE,IAAI,CAACW,IAAI,CAAC;YACRC,EAAE,EAAET,CAAC,CAACU,GAAG;YACTC,YAAY,EAAE,IAAI,CAACpC,WAAW;YAC9B0B,KAAK,EAAED,CAAC,CAACC,KAAK;YACdN,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdiB,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAET,SAAS;YAChBU,GAAG,EAAER,OAAO;YACZS,OAAO,EAAE,YAAY;YACrBC,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAAC/E,UAAU,CAACgF,IAAI,CAACrB,IAAI,CAAC;EAC5B;EAEAsB,gBAAgBA,CAACC,UAAe;IAC9B,IAAIA,UAAU,CAACN,KAAK,CAACO,OAAO,EAAE,KAAKD,UAAU,CAACL,GAAG,CAACM,OAAO,EAAE,IAAID,UAAU,CAACL,GAAG,CAACO,QAAQ,EAAE,GAAGF,UAAU,CAACN,KAAK,CAACQ,QAAQ,EAAE,KAAK,CAAC,EAAE;MAC5H,IAAIzB,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC3D,UAAU,CAACqF,KAAK,CAAC;MACrC,MAAMC,WAAW,GAAGJ,UAAU,CAACpD,IAAI,CAACyD,QAAQ;MAE5CD,WAAW,CAACE,QAAQ,EAAE,CAAC,CAAC;MAExBF,WAAW,CAACG,QAAQ,CAAC;QACnBb,KAAK,EAAEM,UAAU,CAACQ,QAAQ;QAC1Bb,GAAG,EAAEK,UAAU,CAACS,MAAM;QACtBb,OAAO,EAAE;OACV,CAAC;MAEFnB,IAAI,CAACW,IAAI,CAAC;QACRG,YAAY,EAAE,IAAI,CAACpC,WAAW;QAC9B0B,KAAK,EAAEmB,UAAU,CAACQ,QAAQ;QAC1BjC,KAAK,EAAEyB,UAAU,CAACN,KAAK,CAACQ,QAAQ,EAAE;QAClCV,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAEM,UAAU,CAACN,KAAK;QACvBC,GAAG,EAAEK,UAAU,CAACL;OACjB,CAAC;MACF,IAAI,CAAC7E,UAAU,CAACgF,IAAI,CAACrB,IAAI,CAAC;IAC5B;EACF;EAEAiC,IAAIA,CAAA;IACF,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC9C,SAAS,CAACK,GAAG,IAAG;MACnC,IAAI,CAAC0C,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,CAACrE,aAAa,CAAC6C,IAAI,CAAC;MACtByB,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3D;KACf,CAAC;IACF,IAAI,CAACd,SAAS,CAAC0E,IAAI,EAAE;EACvB;EAEA;EACAhG,WAAWA,CAACiG,GAAS;IACnB,MAAMC,SAAS,GAAGD,GAAG,CAACd,QAAQ,EAAE;IAChC,IAAIgB,KAAK,GAAG,KAAK;IACjB,IAAI,CAACpG,UAAU,CAACqF,KAAK,CAACgB,IAAI,CAACC,KAAK,IAAG;MACjC;MACA,IAAIA,KAAK,CAAC7C,KAAK,KAAK0C,SAAS,IACxB,IAAIlE,IAAI,CAACqE,KAAK,CAAC1B,KAAK,CAAC,CAACO,OAAO,EAAE,KAAKe,GAAG,CAACf,OAAO,EAAE,IACjD,IAAIlD,IAAI,CAACqE,KAAK,CAAC1B,KAAK,CAAC,CAAC2B,QAAQ,EAAE,KAAKL,GAAG,CAACK,QAAQ,EAAE,IACnD,IAAItE,IAAI,CAACqE,KAAK,CAAC1B,KAAK,CAAC,CAAC4B,WAAW,EAAE,KAAKN,GAAG,CAACM,WAAW,EAAE,IACzD,CAACF,KAAK,CAAC5B,SAAS,EAEnB0B,KAAK,GAAG,IAAI;IAChB,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;EAEAjG,YAAYA,CAAC+F,GAAS;IACpB,IAAIA,GAAG,CAACO,OAAO,EAAE,GAAG,IAAIxE,IAAI,EAAE,CAACwE,OAAO,EAAE,EAAE;MACxC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAC,gBAAgBA,CAACC,SAAc;IAC7B,IAAIC,UAAU,GAAG,IAAI,CAAC5G,UAAU,CAACqF,KAAK,CAACwB,SAAS,CAACrD,CAAC,IAAI,OAAQA,CAAC,CAACoB,KAAM,KAAK,QAAQ,GAAGpB,CAAC,CAACoB,KAAK,KAAK+B,SAAS,CAACL,KAAK,CAAC1B,KAAK,CAAC6B,OAAO,EAAE,GAAGjD,CAAC,CAACoB,KAAK,CAAC6B,OAAO,EAAE,KAAKE,SAAS,CAACL,KAAK,CAAC1B,KAAK,CAAC6B,OAAO,EAAE,IACpL,OAAQjD,CAAC,CAACqB,GAAI,KAAK,QAAQ,GAAGrB,CAAC,CAACqB,GAAG,KAAK8B,SAAS,CAACL,KAAK,CAACzB,GAAG,CAAC4B,OAAO,EAAE,GAAGjD,CAAC,CAACqB,GAAG,CAAC4B,OAAO,EAAE,KAAKE,SAAS,CAACL,KAAK,CAACzB,GAAG,CAAC4B,OAAO,EAAE,CAAC;IAC/H,IAAIG,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC5G,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAAClC,SAAS,GAAG,IAAI;MAClD,IAAI,CAAC1E,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAACjC,QAAQ,GAAG,IAAI;MACjDgC,SAAS,CAACL,KAAK,CAACQ,MAAM,EAAE;IAC1B;EACF;EAEAjH,OAAOA,CAACiE,CAAM,EAAElE,OAAa;IAC3B,IAAIgH,UAAU,GAAG,IAAI,CAAC5G,UAAU,CAACqF,KAAK,CAACwB,SAAS,CAACrD,CAAC,IAAIA,CAAC,CAACoB,KAAK,CAAC4B,WAAW,EAAE,KAAK5G,OAAO,CAAC4G,WAAW,EAAE,IAChGhD,CAAC,CAACoB,KAAK,CAAC2B,QAAQ,EAAE,KAAK3G,OAAO,CAAC2G,QAAQ,EAAE,IACzC/C,CAAC,CAACoB,KAAK,CAACO,OAAO,EAAE,KAAKvF,OAAO,CAACuF,OAAO,EAAE,IACvC3B,CAAC,CAACC,KAAK,KAAK7D,OAAO,CAACwF,QAAQ,EAAE,CAAC;IAEpC;IACA,IAAIwB,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB;MACA,IAAI9C,CAAC,CAACiD,MAAM,CAACC,OAAO,EAAE;QACpB,IAAIrD,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC3D,UAAU,CAACqF,KAAK,CAAC;QACrC1B,IAAI,CAACW,IAAI,CAAC;UACRG,YAAY,EAAE,IAAI,CAACpC,WAAW;UAC9B0B,KAAK,EAAEpG,MAAM,CAACiC,OAAO,CAAC,CAACqH,MAAM,CAAC,qBAAqB,CAAC;UACpDxD,KAAK,EAAE7D,OAAO,CAACwF,QAAQ,EAAE;UACzBV,SAAS,EAAE,KAAK;UAChBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAEhF,OAAO;UACdmF,KAAK,EAAE;SACR,CAAC;QACF,IAAI,CAAC/E,UAAU,CAACgF,IAAI,CAACrB,IAAI,CAAC;MAC5B;IACF,CAAC,MAAM;MAAE;MACP,IAAIG,CAAC,CAACiD,MAAM,CAACC,OAAO,EAAE;QACpB,IAAI,CAAChH,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAAClC,SAAS,GAAG,KAAK;QACnD,IAAI,CAAC1E,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAACjC,QAAQ,GAAG,IAAI;MACnD,CAAC,MAAM;QACL,IAAI,CAAC3E,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAAClC,SAAS,GAAG,IAAI;QAClD,IAAI,CAAC1E,UAAU,CAACqF,KAAK,CAACuB,UAAU,CAAC,CAACjC,QAAQ,GAAG,IAAI;MACnD;IACF;EACF;EAEA1F,MAAMA,CAACiI,GAAQ;IACb,IAAI,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,CAACxD,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACpC,aAAa,CAAC6F,IAAI,CAACH,GAAG,CAAC;IAC9B,CAAC,MAAM;MACL,IAAI,CAACvF,oBAAoB,GAAG,IAAI,CAACwF,cAAc,EAAE,CAACG,gBAAgB;MAClE,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACvH,UAAU,CAACqF,KAAK,CAAC;MAChD,IAAI,CAAC8B,cAAc,EAAE,CAACG,gBAAgB,CAACzD,OAAO,CAAEC,CAAC,IAAI;QACnDyD,aAAa,CAACjD,IAAI,CAAC;UACjBG,YAAY,EAAEX,CAAC,CAACW,YAAY;UAC5BV,KAAK,EAAE,IAAI,CAACyD,gBAAgB,CAAC,IAAI,CAACC,SAAS,CAAC3D,CAAC,CAACC,KAAK,EAAE,CAAC,CAAC,CAAC;UACxDN,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdiB,SAAS,EAAEZ,CAAC,CAACY,SAAS;UACtBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE,IAAI,CAAC6C,SAAS,CAAC3D,CAAC,CAACC,KAAK,EAAE,CAAC,CAAC;UACjCgB,KAAK,EAAE;SACR,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAAC/E,UAAU,CAACgF,IAAI,CAACuC,aAAa,CAAC;MACnC,IAAI,CAACrG,cAAc,CAACwG,YAAY,EAAE;IACpC;EACF;EAEAC,OAAOA,CAACT,GAAQ;IACdA,GAAG,CAACrG,KAAK,EAAE;EACb;EAEAE,WAAWA,CAACmG,GAAQ;IAClB,IAAI,CAACrB,SAAS,CAAC,KAAK,CAAC,CAAC9C,SAAS,CAAC,MAAM,IAAI,CAAC4E,OAAO,CAACT,GAAG,CAAC,CAAC;EAC1D;EAEAC,cAAcA,CAAA;IACZ,IAAIS,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7F,QAAQ,EAAE,CAAC,CAAC;IAC1D,IAAI8F,qBAAqB,GAAG,IAAI,CAACL,SAAS,CAAC,IAAI,CAACzF,QAAQ,EAAE,CAAC,CAAC;IAE5D,IAAIsF,gBAAgB,GAAG,IAAI,CAACtH,UAAU,CAACqF,KAAK,CAAC9B,MAAM,CAACC,CAAC,IAAI,IAAIvB,IAAI,CAACuB,CAAC,CAACO,KAAK,CAAC,CAAC0C,OAAO,EAAE,IAAI,IAAIxE,IAAI,CAAC2F,mBAAmB,CAAC,CAACnB,OAAO,EAAE,IAC1H,IAAIxE,IAAI,CAACuB,CAAC,CAACO,KAAK,CAAC,CAAC0C,OAAO,EAAE,IAAI,IAAIxE,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAACyE,OAAO,EAAE,CAAC;IAEtE,IAAIW,eAAe,GAAG,IAAI,CAACpH,UAAU,CAACqF,KAAK,CAAC9B,MAAM,CAACC,CAAC,IAAI,IAAIvB,IAAI,CAACuB,CAAC,CAACO,KAAK,CAAC,CAAC0C,OAAO,EAAE,IAAI,IAAIxE,IAAI,CAAC6F,qBAAqB,CAAC,CAACrB,OAAO,EAAE,IAC3H,IAAIxE,IAAI,CAACuB,CAAC,CAACO,KAAK,CAAC,CAAC0C,OAAO,EAAE,IAAI,IAAIxE,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAACyE,OAAO,EAAE,CAAC;IAEtE,OAAO;MAAEW,eAAe;MAAEE;IAAgB,CAAE;EAC9C;EAEAS,gBAAgBA,CAAA;IACd,IAAIC,MAAM,GAAG,IAAI/F,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC;IACpCgG,MAAM,CAACC,OAAO,CAACD,MAAM,CAAC7C,OAAO,EAAE,GAAG,CAAC,CAAC;IACpC,IAAI6C,MAAM,CAACvB,OAAO,EAAE,GAAGxE,IAAI,CAACiG,GAAG,EAAE,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACf,cAAc,EAAE,CAACC,eAAe,CAACxD,MAAM,IAAI,CAAC,IAAI,IAAI,CAACuD,cAAc,EAAE,CAACG,gBAAgB,CAAC1D,MAAM,IAAI,CAAC,EAAE;MAC3G,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEAiC,SAASA,CAACsC,YAAqB;IAC7B,IAAIA,YAAY,EAAE;MAChB,IAAI,CAACtG,mBAAmB,GAAG,IAAI,CAAC7B,UAAU,CAACqF,KAAK,CAAC9B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACmB,QAAQ,KAAK,IAAI,CAAC,CAACyD,GAAG,CAACtE,CAAC,IAAG;QACxF,IAAI,CAAC3B,QAAQ,CAACmC,IAAI,CAAC,IAAIrC,IAAI,CAAC6B,CAAC,CAACC,KAAK,CAAC,CAAC;QACrC,OAAO;UACLb,YAAY,EAAEmF,QAAQ,CAACvE,CAAC,CAACW,YAAY,CAAC;UACtCV,KAAK,EAAED,CAAC,CAACC,KAAK;UACdN,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdiB,SAAS,EAAEZ,CAAC,CAACY;SACd;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACyC,cAAc,EAAE,CAACC,eAAe,CAACvD,OAAO,CAACC,CAAC,IAAG;QAChD,IAAI,IAAI,CAACqD,cAAc,EAAE,CAACG,gBAAgB,CAACjB,IAAI,CAAC7C,CAAC,IAAI,IAAIvB,IAAI,CAAC6B,CAAC,CAACC,KAAK,CAAC,CAACoB,OAAO,EAAE,IAAI,IAAI,CAACsC,SAAS,CAACjE,CAAC,CAACO,KAAK,EAAE,CAAC,CAAC,CAACoB,OAAO,EAAE,IAAI3B,CAAC,CAACC,KAAK,KAAKK,CAAC,CAACL,KAAK,CAAC,EAAE;UAChJ,IAAI,CAAC5B,mBAAmB,CAACyC,IAAI,CAAC;YAC5BpB,YAAY,EAAEmF,QAAQ,CAACvE,CAAC,CAACW,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAACyD,gBAAgB,CAAC,IAAI,CAACc,QAAQ,CAACxE,CAAC,CAACC,KAAK,CAAC,CAAC;YACpDN,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdiB,SAAS,EAAEZ,CAAC,CAACY;WACd,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC7C,mBAAmB,CAACyC,IAAI,CAAC;YAC5BpB,YAAY,EAAEmF,QAAQ,CAACvE,CAAC,CAACW,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAACyD,gBAAgB,CAAC,IAAI,CAACc,QAAQ,CAACxE,CAAC,CAACC,KAAK,CAAC,CAAC;YACpDN,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdiB,SAAS,EAAE;WACZ,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAACyC,cAAc,EAAE,CAACG,gBAAgB,CAACzD,OAAO,CAACC,CAAC,IAAG;QACjD,IAAI,CAACjC,mBAAmB,CAACyC,IAAI,CAAC;UAC5BpB,YAAY,EAAEmF,QAAQ,CAACvE,CAAC,CAACW,YAAY,CAAC;UACtCV,KAAK,EAAE,IAAI,CAACyD,gBAAgB,CAAC,IAAI,CAACC,SAAS,CAAC3D,CAAC,CAACC,KAAK,EAAE,CAAC,CAAC,CAAC;UACxDN,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdiB,SAAS,EAAEZ,CAAC,CAACY;SACd,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAAC7C,mBAAmB,CAAC0B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACkB,SAAS,CAAC,CAACb,OAAO,CAACL,CAAC,IAAG;QAC7D,IAAI,CAACrB,QAAQ,CAACmC,IAAI,CAAC,IAAIrC,IAAI,CAACuB,CAAC,CAACO,KAAM,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAAClC,mBAAmB,CAAC+B,MAAM,IAAI,CAAC,EAAE;MACxC,IAAI,CAACkC,UAAU,EAAE;MACjB,OAAO5H,EAAE,EAAE;IACb;IACA,IAAIqK,OAAO,GAAG,IAAItG,IAAI,CAACuG,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAACvG,QAAQ,CAAC,CAAC;IAC3D,IAAIwG,SAAS,GAAG;MACdtG,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BkG,OAAO,EAAEA;KACV;IACD,OAAO,IAAI,CAACpH,qBAAqB,CAACyH,8CAA8C,CAAC;MAC/E3F,IAAI,EAAE;QACJ4F,oBAAoB,EAAE,IAAI,CAAChH;;KAE9B,CAAC,CAACsB,IAAI,CACLhF,GAAG,CAACiF,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC/B,cAAc,CAACwH,aAAa,CAAC,oBAAoB,CAAC;QACvD,IAAI,CAACjH,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACM,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACnC,UAAU,CAACqF,KAAK,CAACxB,OAAO,CAACL,CAAC,IAAG;UAChC,IAAIA,CAAC,CAACmB,QAAQ,EAAE;YACd,IAAIJ,EAAE,GAAGwE,QAAQ,CAACC,cAAc,CAACxF,CAAC,CAACoB,KAAK,CAAC1E,WAAW,EAAE,CAAC;YACvD+I,UAAU,CAAC,MAAK;cACd1E,EAAE,EAAE2E,KAAK,EAAE;YACb,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,CAAC;QACFxL,mBAAmB,CAACyL,eAAe,CAAC,WAAW,EAAE1G,IAAI,CAAC2G,SAAS,CAACT,SAAS,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC,EACF1K,SAAS,CAAC,MAAM,IAAI,CAAC6E,kBAAkB,EAAE,CAAC,CAC3C;EACH;EAEA2E,SAASA,CAAC4B,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAIrH,IAAI,CAAC,IAAIA,IAAI,CAACoH,EAAE,CAAC,CAACpB,OAAO,CAAC,IAAIhG,IAAI,CAACoH,EAAE,CAAC,CAAClE,OAAO,EAAE,GAAImE,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEAzB,SAASA,CAACwB,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAIrH,IAAI,CAAC,IAAIA,IAAI,CAACoH,EAAE,CAAC,CAACpB,OAAO,CAAC,IAAIhG,IAAI,CAACoH,EAAE,CAAC,CAAClE,OAAO,EAAE,GAAImE,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEAhB,QAAQA,CAACe,EAAQ;IACf,OAAO,IAAIpH,IAAI,CAAC,IAAIA,IAAI,CAACoH,EAAE,CAAC,CAACpB,OAAO,CAAC,IAAIhG,IAAI,CAACoH,EAAE,CAAC,CAAClE,OAAO,EAAE,CAAC,CAAC;EAC/D;EAEAqC,gBAAgBA,CAAChI,IAAS;IACxBA,IAAI,GAAG,IAAIyC,IAAI,CAAC,CAACzC,IAAI,CAAC;IACtBA,IAAI,CAAC+J,OAAO,CAAC/J,IAAI,CAACiH,OAAO,EAAE,GAAIjH,IAAI,CAACgK,iBAAiB,EAAE,GAAG,KAAM,CAAC;IACjE,IAAIC,YAAY,GAAGjK,IAAI,CAACU,WAAW,EAAE,CAACwJ,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IACnD,OAAOD,YAAY;EACrB;;;uCA/UWzI,8BAA8B,EAAA1C,EAAA,CAAAqL,iBAAA,CAAArL,EAAA,CAAAsL,iBAAA,GAAAtL,EAAA,CAAAqL,iBAAA,CAAAE,EAAA,CAAAC,sBAAA,GAAAxL,EAAA,CAAAqL,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA1L,EAAA,CAAAqL,iBAAA,CAAAI,EAAA,CAAAE,MAAA,GAAA3L,EAAA,CAAAqL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7L,EAAA,CAAAqL,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAA/L,EAAA,CAAAqL,iBAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAjM,EAAA,CAAAqL,iBAAA,CAAAa,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BzJ,8BAA8B;MAAA0J,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtM,EAAA,CAAAuM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3BzC7M,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkC,SAAA,qBAAiC;UACnClC,EAAA,CAAAa,YAAA,EAAiB;UAEfb,EADF,CAAAC,cAAA,mBAAc,aAC8B;UACxCD,EAAA,CAAA8B,UAAA,IAAAiL,6CAAA,iBACkC;UAGpC/M,EAAA,CAAAa,YAAA,EAAM;UAIAb,EAHN,CAAAC,cAAA,aAAgC,aACR,aACG,aACsE;UAAxBD,EAAA,CAAAgN,gBAAA,4BAAAC,sEAAA7L,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAA8M,GAAA;YAAAlN,EAAA,CAAAmN,kBAAA,CAAAL,GAAA,CAAApJ,QAAA,EAAAtC,MAAA,MAAA0L,GAAA,CAAApJ,QAAA,GAAAtC,MAAA;YAAA,OAAApB,EAAA,CAAAU,WAAA,CAAAU,MAAA;UAAA,EAAuB;UACxFpB,EAAA,CAAAkC,SAAA,aAAkC;UAGxClC,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;UAEJb,EADF,CAAAC,cAAA,eAAkC,UAC5B;UAAAD,EAAA,CAAAY,MAAA,IAAuD;;UAC7DZ,EAD6D,CAAAa,YAAA,EAAK,EAC5D;UAGFb,EAFJ,CAAAC,cAAA,eAAiC,cACR,eACkE;UAAxBD,EAAA,CAAAgN,gBAAA,4BAAAI,uEAAAhM,MAAA;YAAApB,EAAA,CAAAI,aAAA,CAAA8M,GAAA;YAAAlN,EAAA,CAAAmN,kBAAA,CAAAL,GAAA,CAAApJ,QAAA,EAAAtC,MAAA,MAAA0L,GAAA,CAAApJ,QAAA,GAAAtC,MAAA;YAAA,OAAApB,EAAA,CAAAU,WAAA,CAAAU,MAAA;UAAA,EAAuB;UACpFpB,EAAA,CAAAkC,SAAA,aAAmC;UAI3ClC,EAHM,CAAAa,YAAA,EAAM,EACF,EACF,EACF;UACNb,EAAA,CAAAkC,SAAA,kCAGyB;UAC3BlC,EAAA,CAAAa,YAAA,EAAe;UAITb,EAHN,CAAAC,cAAA,oBAAc,eACK,eACoB,kBAC4B;UAAvBD,EAAA,CAAAE,UAAA,mBAAAmN,iEAAA;YAAArN,EAAA,CAAAI,aAAA,CAAA8M,GAAA;YAAA,OAAAlN,EAAA,CAAAU,WAAA,CAASoM,GAAA,CAAAtF,UAAA,EAAY;UAAA,EAAC;UAACxH,EAAA,CAAAY,MAAA,oBAAE;UAAAZ,EAAA,CAAAa,YAAA,EAAS;UACxEb,EAAA,CAAAC,cAAA,kBAAqD;UAAjBD,EAAA,CAAAE,UAAA,mBAAAoN,iEAAA;YAAAtN,EAAA,CAAAI,aAAA,CAAA8M,GAAA;YAAA,OAAAlN,EAAA,CAAAU,WAAA,CAASoM,GAAA,CAAAxF,IAAA,EAAM;UAAA,EAAC;UAACtH,EAAA,CAAAY,MAAA,oBAAE;UAI/DZ,EAJ+D,CAAAa,YAAA,EAAS,EAC5D,EACF,EACO,EACP;UAiBVb,EAfA,CAAA8B,UAAA,KAAAyL,sDAAA,gCAAAvN,EAAA,CAAAwN,sBAAA,CAAsF,KAAAC,sDAAA,gCAAAzN,EAAA,CAAAwN,sBAAA,CAU9C,KAAAE,sDAAA,gCAAA1N,EAAA,CAAAwN,sBAAA,CAKmB;;;;;UAtD/CxN,EAAA,CAAAc,SAAA,GAAwB;UAAxBd,EAAA,CAAAwB,UAAA,SAAAsL,GAAA,CAAArD,gBAAA,GAAwB;UAQ2BzJ,EAAA,CAAAc,SAAA,GAAa;UAAbd,EAAA,CAAAwB,UAAA,SAAAsL,GAAA,CAAAtJ,IAAA,CAAa;UAACxD,EAAA,CAAA2N,gBAAA,aAAAb,GAAA,CAAApJ,QAAA,CAAuB;UAMxF1D,EAAA,CAAAc,SAAA,GAAuD;UAAvDd,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAA4N,WAAA,SAAAd,GAAA,CAAApJ,QAAA,EAAAoJ,GAAA,CAAAtJ,IAAA,sBAAuD;UAIRxD,EAAA,CAAAc,SAAA,GAAa;UAAbd,EAAA,CAAAwB,UAAA,SAAAsL,GAAA,CAAAtJ,IAAA,CAAa;UAACxD,EAAA,CAAA2N,gBAAA,aAAAb,GAAA,CAAApJ,QAAA,CAAuB;UAMpE1D,EAAA,CAAAc,SAAA,GAAmB;UAEed,EAFlC,CAAAwB,UAAA,oBAAmB,mBAAmB,aAAAsL,GAAA,CAAApJ,QAAA,CAAsB,WAAAoJ,GAAA,CAAA1J,MAAA,CAAkB,mBAClF,kBAAkB,wBAAAyK,uBAAA,CAA4C,8BAAAC,6BAAA,CACzB,oBAAoB;;;qBDb7ExO,YAAY,EAAAwM,EAAA,CAAAiC,IAAA,EAAAjC,EAAA,CAAAkC,SAAA,EAAAlC,EAAA,CAAAmC,QAAA,EACZnO,YAAY,EAAAkM,EAAA,CAAAkC,eAAA,EAAAlC,EAAA,CAAAmC,mBAAA,EAAAnC,EAAA,CAAAoC,qBAAA,EAAApC,EAAA,CAAAqC,qBAAA,EACZ9O,cAAc,EAAA+O,EAAA,CAAAC,8BAAA,EAAAD,EAAA,CAAAE,0BAAA,EAAAF,EAAA,CAAAG,iBAAA,EAAAH,EAAA,CAAAI,yBAAA,EACdjP,YAAY;MAAAkP,MAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}