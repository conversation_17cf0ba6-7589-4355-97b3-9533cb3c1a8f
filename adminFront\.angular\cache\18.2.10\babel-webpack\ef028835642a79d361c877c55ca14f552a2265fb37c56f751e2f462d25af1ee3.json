{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetHouseProgressPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHouseProgressPost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetHouseProgressPost$Json.PATH = '/api/House/GetHouseProgress';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}