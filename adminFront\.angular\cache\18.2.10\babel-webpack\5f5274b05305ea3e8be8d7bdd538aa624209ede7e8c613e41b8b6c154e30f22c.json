{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Cipher Feedback block mode.\n   */\n  CryptoJS.mode.CFB = function () {\n    var CFB = CryptoJS.lib.BlockCipherMode.extend();\n    CFB.Encryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n        // Remember this block to use with next block\n        this._prevBlock = words.slice(offset, offset + blockSize);\n      }\n    });\n    CFB.Decryptor = CFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n\n        // Remember this block to use with next block\n        var thisBlock = words.slice(offset, offset + blockSize);\n        generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n        // This block becomes the previous block\n        this._prevBlock = thisBlock;\n      }\n    });\n    function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {\n      var keystream;\n\n      // Shortcut\n      var iv = this._iv;\n\n      // Generate keystream\n      if (iv) {\n        keystream = iv.slice(0);\n\n        // Remove IV for subsequent blocks\n        this._iv = undefined;\n      } else {\n        keystream = this._prevBlock;\n      }\n      cipher.encryptBlock(keystream, 0);\n\n      // Encrypt\n      for (var i = 0; i < blockSize; i++) {\n        words[offset + i] ^= keystream[i];\n      }\n    }\n    return CFB;\n  }();\n  return CryptoJS.mode.CFB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "CFB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "generateKeystreamAndEncrypt", "call", "_prevBlock", "slice", "Decryptor", "thisBlock", "keystream", "iv", "_iv", "undefined", "encryptBlock", "i"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/mode-cfb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Cipher Feedback block mode.\n\t */\n\tCryptoJS.mode.CFB = (function () {\n\t    var CFB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    CFB.Encryptor = CFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher;\n\t            var blockSize = cipher.blockSize;\n\n\t            generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n\t            // Remember this block to use with next block\n\t            this._prevBlock = words.slice(offset, offset + blockSize);\n\t        }\n\t    });\n\n\t    CFB.Decryptor = CFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher;\n\t            var blockSize = cipher.blockSize;\n\n\t            // Remember this block to use with next block\n\t            var thisBlock = words.slice(offset, offset + blockSize);\n\n\t            generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);\n\n\t            // This block becomes the previous block\n\t            this._prevBlock = thisBlock;\n\t        }\n\t    });\n\n\t    function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {\n\t        var keystream;\n\n\t        // Shortcut\n\t        var iv = this._iv;\n\n\t        // Generate keystream\n\t        if (iv) {\n\t            keystream = iv.slice(0);\n\n\t            // Remove IV for subsequent blocks\n\t            this._iv = undefined;\n\t        } else {\n\t            keystream = this._prevBlock;\n\t        }\n\t        cipher.encryptBlock(keystream, 0);\n\n\t        // Encrypt\n\t        for (var i = 0; i < blockSize; i++) {\n\t            words[offset + i] ^= keystream[i];\n\t        }\n\t    }\n\n\t    return CFB;\n\t}());\n\n\n\treturn CryptoJS.mode.CFB;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAI,CAACC,GAAG,GAAI,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAG,CAACC,eAAe,CAACC,MAAM,CAAC,CAAC;IAE/CH,GAAG,CAACI,SAAS,GAAGJ,GAAG,CAACG,MAAM,CAAC;MACvBE,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC;QACA,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;QACzB,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS;QAEhCC,2BAA2B,CAACC,IAAI,CAAC,IAAI,EAAEN,KAAK,EAAEC,MAAM,EAAEG,SAAS,EAAEF,MAAM,CAAC;;QAExE;QACA,IAAI,CAACK,UAAU,GAAGP,KAAK,CAACQ,KAAK,CAACP,MAAM,EAAEA,MAAM,GAAGG,SAAS,CAAC;MAC7D;IACJ,CAAC,CAAC;IAEFV,GAAG,CAACe,SAAS,GAAGf,GAAG,CAACG,MAAM,CAAC;MACvBE,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC;QACA,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;QACzB,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS;;QAEhC;QACA,IAAIM,SAAS,GAAGV,KAAK,CAACQ,KAAK,CAACP,MAAM,EAAEA,MAAM,GAAGG,SAAS,CAAC;QAEvDC,2BAA2B,CAACC,IAAI,CAAC,IAAI,EAAEN,KAAK,EAAEC,MAAM,EAAEG,SAAS,EAAEF,MAAM,CAAC;;QAExE;QACA,IAAI,CAACK,UAAU,GAAGG,SAAS;MAC/B;IACJ,CAAC,CAAC;IAEF,SAASL,2BAA2BA,CAACL,KAAK,EAAEC,MAAM,EAAEG,SAAS,EAAEF,MAAM,EAAE;MACnE,IAAIS,SAAS;;MAEb;MACA,IAAIC,EAAE,GAAG,IAAI,CAACC,GAAG;;MAEjB;MACA,IAAID,EAAE,EAAE;QACJD,SAAS,GAAGC,EAAE,CAACJ,KAAK,CAAC,CAAC,CAAC;;QAEvB;QACA,IAAI,CAACK,GAAG,GAAGC,SAAS;MACxB,CAAC,MAAM;QACHH,SAAS,GAAG,IAAI,CAACJ,UAAU;MAC/B;MACAL,MAAM,CAACa,YAAY,CAACJ,SAAS,EAAE,CAAC,CAAC;;MAEjC;MACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,SAAS,EAAEY,CAAC,EAAE,EAAE;QAChChB,KAAK,CAACC,MAAM,GAAGe,CAAC,CAAC,IAAIL,SAAS,CAACK,CAAC,CAAC;MACrC;IACJ;IAEA,OAAOtB,GAAG;EACd,CAAC,CAAC,CAAE;EAGJ,OAAOF,QAAQ,CAACC,IAAI,CAACC,GAAG;AAEzB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}