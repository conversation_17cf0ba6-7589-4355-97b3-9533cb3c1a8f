{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Kurdish [ku]\n//! author : <PERSON><PERSON> : https://github.com/ShahramMebashar\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '١',\n      2: '٢',\n      3: '٣',\n      4: '٤',\n      5: '٥',\n      6: '٦',\n      7: '٧',\n      8: '٨',\n      9: '٩',\n      0: '٠'\n    },\n    numberMap = {\n      '١': '1',\n      '٢': '2',\n      '٣': '3',\n      '٤': '4',\n      '٥': '5',\n      '٦': '6',\n      '٧': '7',\n      '٨': '8',\n      '٩': '9',\n      '٠': '0'\n    },\n    months = ['کانونی دووەم', 'شوبات', 'ئازار', 'نیسان', 'ئایار', 'حوزەیران', 'تەمموز', 'ئاب', 'ئەیلوول', 'تشرینی یەكەم', 'تشرینی دووەم', 'كانونی یەکەم'];\n  var ku = moment.defineLocale('ku', {\n    months: months,\n    monthsShort: months,\n    weekdays: 'یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌'.split('_'),\n    weekdaysShort: 'یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌'.split('_'),\n    weekdaysMin: 'ی_د_س_چ_پ_ه_ش'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ئێواره‌|به‌یانی/,\n    isPM: function (input) {\n      return /ئێواره‌/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'به‌یانی';\n      } else {\n        return 'ئێواره‌';\n      }\n    },\n    calendar: {\n      sameDay: '[ئه‌مرۆ كاتژمێر] LT',\n      nextDay: '[به‌یانی كاتژمێر] LT',\n      nextWeek: 'dddd [كاتژمێر] LT',\n      lastDay: '[دوێنێ كاتژمێر] LT',\n      lastWeek: 'dddd [كاتژمێر] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'له‌ %s',\n      past: '%s',\n      s: 'چه‌ند چركه‌یه‌ك',\n      ss: 'چركه‌ %d',\n      m: 'یه‌ك خوله‌ك',\n      mm: '%d خوله‌ك',\n      h: 'یه‌ك كاتژمێر',\n      hh: '%d كاتژمێر',\n      d: 'یه‌ك ڕۆژ',\n      dd: '%d ڕۆژ',\n      M: 'یه‌ك مانگ',\n      MM: '%d مانگ',\n      y: 'یه‌ك ساڵ',\n      yy: '%d ساڵ'\n    },\n    preparse: function (string) {\n      return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return ku;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "months", "ku", "defineLocale", "monthsShort", "weekdays", "split", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "preparse", "string", "replace", "match", "postformat", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/ku.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Kurdish [ku]\n//! author : <PERSON><PERSON> : https://github.com/ShahramMebashar\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '١',\n            2: '٢',\n            3: '٣',\n            4: '٤',\n            5: '٥',\n            6: '٦',\n            7: '٧',\n            8: '٨',\n            9: '٩',\n            0: '٠',\n        },\n        numberMap = {\n            '١': '1',\n            '٢': '2',\n            '٣': '3',\n            '٤': '4',\n            '٥': '5',\n            '٦': '6',\n            '٧': '7',\n            '٨': '8',\n            '٩': '9',\n            '٠': '0',\n        },\n        months = [\n            'کانونی دووەم',\n            'شوبات',\n            'ئازار',\n            'نیسان',\n            'ئایار',\n            'حوزەیران',\n            'تەمموز',\n            'ئاب',\n            'ئەیلوول',\n            'تشرینی یەكەم',\n            'تشرینی دووەم',\n            'كانونی یەکەم',\n        ];\n\n    var ku = moment.defineLocale('ku', {\n        months: months,\n        monthsShort: months,\n        weekdays:\n            'یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌'.split(\n                '_'\n            ),\n        weekdaysShort:\n            'یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌'.split('_'),\n        weekdaysMin: 'ی_د_س_چ_پ_ه_ش'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ئێواره‌|به‌یانی/,\n        isPM: function (input) {\n            return /ئێواره‌/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'به‌یانی';\n            } else {\n                return 'ئێواره‌';\n            }\n        },\n        calendar: {\n            sameDay: '[ئه‌مرۆ كاتژمێر] LT',\n            nextDay: '[به‌یانی كاتژمێر] LT',\n            nextWeek: 'dddd [كاتژمێر] LT',\n            lastDay: '[دوێنێ كاتژمێر] LT',\n            lastWeek: 'dddd [كاتژمێر] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'له‌ %s',\n            past: '%s',\n            s: 'چه‌ند چركه‌یه‌ك',\n            ss: 'چركه‌ %d',\n            m: 'یه‌ك خوله‌ك',\n            mm: '%d خوله‌ك',\n            h: 'یه‌ك كاتژمێر',\n            hh: '%d كاتژمێر',\n            d: 'یه‌ك ڕۆژ',\n            dd: '%d ڕۆژ',\n            M: 'یه‌ك مانگ',\n            MM: '%d مانگ',\n            y: 'یه‌ك ساڵ',\n            yy: '%d ساڵ',\n        },\n        preparse: function (string) {\n            return string\n                .replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n                    return numberMap[match];\n                })\n                .replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string\n                .replace(/\\d/g, function (match) {\n                    return symbolMap[match];\n                })\n                .replace(/,/g, '،');\n        },\n        week: {\n            dow: 6, // Saturday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return ku;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;IACDC,MAAM,GAAG,CACL,cAAc,EACd,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,EACL,SAAS,EACT,cAAc,EACd,cAAc,EACd,cAAc,CACjB;EAEL,IAAIC,EAAE,GAAGJ,MAAM,CAACK,YAAY,CAAC,IAAI,EAAE;IAC/BF,MAAM,EAAEA,MAAM;IACdG,WAAW,EAAEH,MAAM;IACnBI,QAAQ,EACJ,yEAAyE,CAACC,KAAK,CAC3E,GACJ,CAAC;IACLC,aAAa,EACT,0DAA0D,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,WAAW,EAAE,eAAe,CAACF,KAAK,CAAC,GAAG,CAAC;IACvCG,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,iBAAiB;IAChCC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,SAAS,CAACC,IAAI,CAACD,KAAK,CAAC;IAChC,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,SAAS;MACpB,CAAC,MAAM;QACH,OAAO,SAAS;MACpB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,qBAAqB;MAC9BC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,mBAAmB;MAC7BC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,IAAI;MACVC,CAAC,EAAE,iBAAiB;MACpBC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,cAAc;MACjBC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,QAAQ;MACZC,CAAC,EAAE,WAAW;MACdC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CACRC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACvC,OAAOlD,SAAS,CAACkD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDE,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CACRC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC7B,OAAOnD,SAAS,CAACmD,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACDG,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOpD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}