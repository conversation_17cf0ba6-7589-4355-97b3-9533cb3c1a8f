{"ast": null, "code": "//! moment.js locale configuration\n//! locale : English (Singapore) [en-sg]\n//! author : <PERSON>-<PERSON> : https://github.com/techdimension\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var enSg = moment.defineLocale('en-sg', {\n    months: 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_'),\n    monthsShort: 'Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec'.split('_'),\n    weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),\n    weekdaysShort: 'Sun_Mon_Tue_Wed_Thu_Fri_Sat'.split('_'),\n    weekdaysMin: 'Su_Mo_Tu_We_Th_Fr_Sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Today at] LT',\n      nextDay: '[Tomorrow at] LT',\n      nextWeek: 'dddd [at] LT',\n      lastDay: '[Yesterday at] LT',\n      lastWeek: '[Last] dddd [at] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'in %s',\n      past: '%s ago',\n      s: 'a few seconds',\n      ss: '%d seconds',\n      m: 'a minute',\n      mm: '%d minutes',\n      h: 'an hour',\n      hh: '%d hours',\n      d: 'a day',\n      dd: '%d days',\n      M: 'a month',\n      MM: '%d months',\n      y: 'a year',\n      yy: '%d years'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(st|nd|rd|th)/,\n    ordinal: function (number) {\n      var b = number % 10,\n        output = ~~(number % 100 / 10) === 1 ? 'th' : b === 1 ? 'st' : b === 2 ? 'nd' : b === 3 ? 'rd' : 'th';\n      return number + output;\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return enSg;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}