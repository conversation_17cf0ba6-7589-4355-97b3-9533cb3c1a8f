{"ast": null, "code": "export class CountryOrderData {}", "map": {"version": 3, "names": ["CountryOrderData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\country-order.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport abstract class CountryOrderData {\r\n  abstract getCountriesCategories(): Observable<string[]>;\r\n  abstract getCountriesCategoriesData(country: string): Observable<number[]>;\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAgBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}