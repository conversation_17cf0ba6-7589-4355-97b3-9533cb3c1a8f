{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./evpkdf\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./evpkdf\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Cipher core components.\n   */\n  CryptoJS.lib.Cipher || function (undefined) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm;\n    var C_enc = C.enc;\n    var Utf8 = C_enc.Utf8;\n    var Base64 = C_enc.Base64;\n    var C_algo = C.algo;\n    var EvpKDF = C_algo.EvpKDF;\n\n    /**\n     * Abstract base cipher template.\n     *\n     * @property {number} keySize This cipher's key size. Default: 4 (128 bits)\n     * @property {number} ivSize This cipher's IV size. Default: 4 (128 bits)\n     * @property {number} _ENC_XFORM_MODE A constant representing encryption mode.\n     * @property {number} _DEC_XFORM_MODE A constant representing decryption mode.\n     */\n    var Cipher = C_lib.Cipher = BufferedBlockAlgorithm.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {WordArray} iv The IV to use for this operation.\n       */\n      cfg: Base.extend(),\n      /**\n       * Creates this cipher in encryption mode.\n       *\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {Cipher} A cipher instance.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.createEncryptor(keyWordArray, { iv: ivWordArray });\n       */\n      createEncryptor: function (key, cfg) {\n        return this.create(this._ENC_XFORM_MODE, key, cfg);\n      },\n      /**\n       * Creates this cipher in decryption mode.\n       *\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {Cipher} A cipher instance.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.createDecryptor(keyWordArray, { iv: ivWordArray });\n       */\n      createDecryptor: function (key, cfg) {\n        return this.create(this._DEC_XFORM_MODE, key, cfg);\n      },\n      /**\n       * Initializes a newly created cipher.\n       *\n       * @param {number} xformMode Either the encryption or decryption transormation mode constant.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.create(CryptoJS.algo.AES._ENC_XFORM_MODE, keyWordArray, { iv: ivWordArray });\n       */\n      init: function (xformMode, key, cfg) {\n        // Apply config defaults\n        this.cfg = this.cfg.extend(cfg);\n\n        // Store transform mode and key\n        this._xformMode = xformMode;\n        this._key = key;\n\n        // Set initial values\n        this.reset();\n      },\n      /**\n       * Resets this cipher to its initial state.\n       *\n       * @example\n       *\n       *     cipher.reset();\n       */\n      reset: function () {\n        // Reset data buffer\n        BufferedBlockAlgorithm.reset.call(this);\n\n        // Perform concrete-cipher logic\n        this._doReset();\n      },\n      /**\n       * Adds data to be encrypted or decrypted.\n       *\n       * @param {WordArray|string} dataUpdate The data to encrypt or decrypt.\n       *\n       * @return {WordArray} The data after processing.\n       *\n       * @example\n       *\n       *     var encrypted = cipher.process('data');\n       *     var encrypted = cipher.process(wordArray);\n       */\n      process: function (dataUpdate) {\n        // Append\n        this._append(dataUpdate);\n\n        // Process available blocks\n        return this._process();\n      },\n      /**\n       * Finalizes the encryption or decryption process.\n       * Note that the finalize operation is effectively a destructive, read-once operation.\n       *\n       * @param {WordArray|string} dataUpdate The final data to encrypt or decrypt.\n       *\n       * @return {WordArray} The data after final processing.\n       *\n       * @example\n       *\n       *     var encrypted = cipher.finalize();\n       *     var encrypted = cipher.finalize('data');\n       *     var encrypted = cipher.finalize(wordArray);\n       */\n      finalize: function (dataUpdate) {\n        // Final data update\n        if (dataUpdate) {\n          this._append(dataUpdate);\n        }\n\n        // Perform concrete-cipher logic\n        var finalProcessedData = this._doFinalize();\n        return finalProcessedData;\n      },\n      keySize: 128 / 32,\n      ivSize: 128 / 32,\n      _ENC_XFORM_MODE: 1,\n      _DEC_XFORM_MODE: 2,\n      /**\n       * Creates shortcut functions to a cipher's object interface.\n       *\n       * @param {Cipher} cipher The cipher to create a helper for.\n       *\n       * @return {Object} An object with encrypt and decrypt shortcut functions.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var AES = CryptoJS.lib.Cipher._createHelper(CryptoJS.algo.AES);\n       */\n      _createHelper: function () {\n        function selectCipherStrategy(key) {\n          if (typeof key == 'string') {\n            return PasswordBasedCipher;\n          } else {\n            return SerializableCipher;\n          }\n        }\n        return function (cipher) {\n          return {\n            encrypt: function (message, key, cfg) {\n              return selectCipherStrategy(key).encrypt(cipher, message, key, cfg);\n            },\n            decrypt: function (ciphertext, key, cfg) {\n              return selectCipherStrategy(key).decrypt(cipher, ciphertext, key, cfg);\n            }\n          };\n        };\n      }()\n    });\n\n    /**\n     * Abstract base stream cipher template.\n     *\n     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 1 (32 bits)\n     */\n    var StreamCipher = C_lib.StreamCipher = Cipher.extend({\n      _doFinalize: function () {\n        // Process partial blocks\n        var finalProcessedBlocks = this._process(!!'flush');\n        return finalProcessedBlocks;\n      },\n      blockSize: 1\n    });\n\n    /**\n     * Mode namespace.\n     */\n    var C_mode = C.mode = {};\n\n    /**\n     * Abstract base block cipher mode template.\n     */\n    var BlockCipherMode = C_lib.BlockCipherMode = Base.extend({\n      /**\n       * Creates this mode for encryption.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.createEncryptor(cipher, iv.words);\n       */\n      createEncryptor: function (cipher, iv) {\n        return this.Encryptor.create(cipher, iv);\n      },\n      /**\n       * Creates this mode for decryption.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.createDecryptor(cipher, iv.words);\n       */\n      createDecryptor: function (cipher, iv) {\n        return this.Decryptor.create(cipher, iv);\n      },\n      /**\n       * Initializes a newly created mode.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.Encryptor.create(cipher, iv.words);\n       */\n      init: function (cipher, iv) {\n        this._cipher = cipher;\n        this._iv = iv;\n      }\n    });\n\n    /**\n     * Cipher Block Chaining mode.\n     */\n    var CBC = C_mode.CBC = function () {\n      /**\n       * Abstract base CBC mode.\n       */\n      var CBC = BlockCipherMode.extend();\n\n      /**\n       * CBC encryptor.\n       */\n      CBC.Encryptor = CBC.extend({\n        /**\n         * Processes the data block at offset.\n         *\n         * @param {Array} words The data words to operate on.\n         * @param {number} offset The offset where the block starts.\n         *\n         * @example\n         *\n         *     mode.processBlock(data.words, offset);\n         */\n        processBlock: function (words, offset) {\n          // Shortcuts\n          var cipher = this._cipher;\n          var blockSize = cipher.blockSize;\n\n          // XOR and encrypt\n          xorBlock.call(this, words, offset, blockSize);\n          cipher.encryptBlock(words, offset);\n\n          // Remember this block to use with next block\n          this._prevBlock = words.slice(offset, offset + blockSize);\n        }\n      });\n\n      /**\n       * CBC decryptor.\n       */\n      CBC.Decryptor = CBC.extend({\n        /**\n         * Processes the data block at offset.\n         *\n         * @param {Array} words The data words to operate on.\n         * @param {number} offset The offset where the block starts.\n         *\n         * @example\n         *\n         *     mode.processBlock(data.words, offset);\n         */\n        processBlock: function (words, offset) {\n          // Shortcuts\n          var cipher = this._cipher;\n          var blockSize = cipher.blockSize;\n\n          // Remember this block to use with next block\n          var thisBlock = words.slice(offset, offset + blockSize);\n\n          // Decrypt and XOR\n          cipher.decryptBlock(words, offset);\n          xorBlock.call(this, words, offset, blockSize);\n\n          // This block becomes the previous block\n          this._prevBlock = thisBlock;\n        }\n      });\n      function xorBlock(words, offset, blockSize) {\n        var block;\n\n        // Shortcut\n        var iv = this._iv;\n\n        // Choose mixing block\n        if (iv) {\n          block = iv;\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        } else {\n          block = this._prevBlock;\n        }\n\n        // XOR blocks\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= block[i];\n        }\n      }\n      return CBC;\n    }();\n\n    /**\n     * Padding namespace.\n     */\n    var C_pad = C.pad = {};\n\n    /**\n     * PKCS #5/7 padding strategy.\n     */\n    var Pkcs7 = C_pad.Pkcs7 = {\n      /**\n       * Pads data using the algorithm defined in PKCS #5/7.\n       *\n       * @param {WordArray} data The data to pad.\n       * @param {number} blockSize The multiple that the data should be padded to.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     CryptoJS.pad.Pkcs7.pad(wordArray, 4);\n       */\n      pad: function (data, blockSize) {\n        // Shortcut\n        var blockSizeBytes = blockSize * 4;\n\n        // Count padding bytes\n        var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n        // Create padding word\n        var paddingWord = nPaddingBytes << 24 | nPaddingBytes << 16 | nPaddingBytes << 8 | nPaddingBytes;\n\n        // Create padding\n        var paddingWords = [];\n        for (var i = 0; i < nPaddingBytes; i += 4) {\n          paddingWords.push(paddingWord);\n        }\n        var padding = WordArray.create(paddingWords, nPaddingBytes);\n\n        // Add padding\n        data.concat(padding);\n      },\n      /**\n       * Unpads data that had been padded using the algorithm defined in PKCS #5/7.\n       *\n       * @param {WordArray} data The data to unpad.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     CryptoJS.pad.Pkcs7.unpad(wordArray);\n       */\n      unpad: function (data) {\n        // Get number of padding bytes from last byte\n        var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n        // Remove padding\n        data.sigBytes -= nPaddingBytes;\n      }\n    };\n\n    /**\n     * Abstract base block cipher template.\n     *\n     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 4 (128 bits)\n     */\n    var BlockCipher = C_lib.BlockCipher = Cipher.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {Mode} mode The block mode to use. Default: CBC\n       * @property {Padding} padding The padding strategy to use. Default: Pkcs7\n       */\n      cfg: Cipher.cfg.extend({\n        mode: CBC,\n        padding: Pkcs7\n      }),\n      reset: function () {\n        var modeCreator;\n\n        // Reset cipher\n        Cipher.reset.call(this);\n\n        // Shortcuts\n        var cfg = this.cfg;\n        var iv = cfg.iv;\n        var mode = cfg.mode;\n\n        // Reset block mode\n        if (this._xformMode == this._ENC_XFORM_MODE) {\n          modeCreator = mode.createEncryptor;\n        } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{\n            modeCreator = mode.createDecryptor;\n            // Keep at least one block in the buffer for unpadding\n            this._minBufferSize = 1;\n          }\n        if (this._mode && this._mode.__creator == modeCreator) {\n          this._mode.init(this, iv && iv.words);\n        } else {\n          this._mode = modeCreator.call(mode, this, iv && iv.words);\n          this._mode.__creator = modeCreator;\n        }\n      },\n      _doProcessBlock: function (words, offset) {\n        this._mode.processBlock(words, offset);\n      },\n      _doFinalize: function () {\n        var finalProcessedBlocks;\n\n        // Shortcut\n        var padding = this.cfg.padding;\n\n        // Finalize\n        if (this._xformMode == this._ENC_XFORM_MODE) {\n          // Pad data\n          padding.pad(this._data, this.blockSize);\n\n          // Process final blocks\n          finalProcessedBlocks = this._process(!!'flush');\n        } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{\n            // Process final blocks\n            finalProcessedBlocks = this._process(!!'flush');\n\n            // Unpad data\n            padding.unpad(finalProcessedBlocks);\n          }\n        return finalProcessedBlocks;\n      },\n      blockSize: 128 / 32\n    });\n\n    /**\n     * A collection of cipher parameters.\n     *\n     * @property {WordArray} ciphertext The raw ciphertext.\n     * @property {WordArray} key The key to this ciphertext.\n     * @property {WordArray} iv The IV used in the ciphering operation.\n     * @property {WordArray} salt The salt used with a key derivation function.\n     * @property {Cipher} algorithm The cipher algorithm.\n     * @property {Mode} mode The block mode used in the ciphering operation.\n     * @property {Padding} padding The padding scheme used in the ciphering operation.\n     * @property {number} blockSize The block size of the cipher.\n     * @property {Format} formatter The default formatting strategy to convert this cipher params object to a string.\n     */\n    var CipherParams = C_lib.CipherParams = Base.extend({\n      /**\n       * Initializes a newly created cipher params object.\n       *\n       * @param {Object} cipherParams An object with any of the possible cipher parameters.\n       *\n       * @example\n       *\n       *     var cipherParams = CryptoJS.lib.CipherParams.create({\n       *         ciphertext: ciphertextWordArray,\n       *         key: keyWordArray,\n       *         iv: ivWordArray,\n       *         salt: saltWordArray,\n       *         algorithm: CryptoJS.algo.AES,\n       *         mode: CryptoJS.mode.CBC,\n       *         padding: CryptoJS.pad.PKCS7,\n       *         blockSize: 4,\n       *         formatter: CryptoJS.format.OpenSSL\n       *     });\n       */\n      init: function (cipherParams) {\n        this.mixIn(cipherParams);\n      },\n      /**\n       * Converts this cipher params object to a string.\n       *\n       * @param {Format} formatter (Optional) The formatting strategy to use.\n       *\n       * @return {string} The stringified cipher params.\n       *\n       * @throws Error If neither the formatter nor the default formatter is set.\n       *\n       * @example\n       *\n       *     var string = cipherParams + '';\n       *     var string = cipherParams.toString();\n       *     var string = cipherParams.toString(CryptoJS.format.OpenSSL);\n       */\n      toString: function (formatter) {\n        return (formatter || this.formatter).stringify(this);\n      }\n    });\n\n    /**\n     * Format namespace.\n     */\n    var C_format = C.format = {};\n\n    /**\n     * OpenSSL formatting strategy.\n     */\n    var OpenSSLFormatter = C_format.OpenSSL = {\n      /**\n       * Converts a cipher params object to an OpenSSL-compatible string.\n       *\n       * @param {CipherParams} cipherParams The cipher params object.\n       *\n       * @return {string} The OpenSSL-compatible string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var openSSLString = CryptoJS.format.OpenSSL.stringify(cipherParams);\n       */\n      stringify: function (cipherParams) {\n        var wordArray;\n\n        // Shortcuts\n        var ciphertext = cipherParams.ciphertext;\n        var salt = cipherParams.salt;\n\n        // Format\n        if (salt) {\n          wordArray = WordArray.create([0x53616c74, 0x65645f5f]).concat(salt).concat(ciphertext);\n        } else {\n          wordArray = ciphertext;\n        }\n        return wordArray.toString(Base64);\n      },\n      /**\n       * Converts an OpenSSL-compatible string to a cipher params object.\n       *\n       * @param {string} openSSLStr The OpenSSL-compatible string.\n       *\n       * @return {CipherParams} The cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipherParams = CryptoJS.format.OpenSSL.parse(openSSLString);\n       */\n      parse: function (openSSLStr) {\n        var salt;\n\n        // Parse base64\n        var ciphertext = Base64.parse(openSSLStr);\n\n        // Shortcut\n        var ciphertextWords = ciphertext.words;\n\n        // Test for salt\n        if (ciphertextWords[0] == 0x53616c74 && ciphertextWords[1] == 0x65645f5f) {\n          // Extract salt\n          salt = WordArray.create(ciphertextWords.slice(2, 4));\n\n          // Remove salt from ciphertext\n          ciphertextWords.splice(0, 4);\n          ciphertext.sigBytes -= 16;\n        }\n        return CipherParams.create({\n          ciphertext: ciphertext,\n          salt: salt\n        });\n      }\n    };\n\n    /**\n     * A cipher wrapper that returns ciphertext as a serializable cipher params object.\n     */\n    var SerializableCipher = C_lib.SerializableCipher = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {Formatter} format The formatting strategy to convert cipher param objects to and from a string. Default: OpenSSL\n       */\n      cfg: Base.extend({\n        format: OpenSSLFormatter\n      }),\n      /**\n       * Encrypts a message.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {WordArray|string} message The message to encrypt.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {CipherParams} A cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key);\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv });\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       */\n      encrypt: function (cipher, message, key, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Encrypt\n        var encryptor = cipher.createEncryptor(key, cfg);\n        var ciphertext = encryptor.finalize(message);\n\n        // Shortcut\n        var cipherCfg = encryptor.cfg;\n\n        // Create and return serializable cipher params\n        return CipherParams.create({\n          ciphertext: ciphertext,\n          key: key,\n          iv: cipherCfg.iv,\n          algorithm: cipher,\n          mode: cipherCfg.mode,\n          padding: cipherCfg.padding,\n          blockSize: cipher.blockSize,\n          formatter: cfg.format\n        });\n      },\n      /**\n       * Decrypts serialized ciphertext.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {WordArray} The plaintext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       */\n      decrypt: function (cipher, ciphertext, key, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, cfg.format);\n\n        // Decrypt\n        var plaintext = cipher.createDecryptor(key, cfg).finalize(ciphertext.ciphertext);\n        return plaintext;\n      },\n      /**\n       * Converts serialized ciphertext to CipherParams,\n       * else assumed CipherParams already and returns ciphertext unchanged.\n       *\n       * @param {CipherParams|string} ciphertext The ciphertext.\n       * @param {Formatter} format The formatting strategy to use to parse serialized ciphertext.\n       *\n       * @return {CipherParams} The unserialized ciphertext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);\n       */\n      _parse: function (ciphertext, format) {\n        if (typeof ciphertext == 'string') {\n          return format.parse(ciphertext, this);\n        } else {\n          return ciphertext;\n        }\n      }\n    });\n\n    /**\n     * Key derivation function namespace.\n     */\n    var C_kdf = C.kdf = {};\n\n    /**\n     * OpenSSL key derivation function.\n     */\n    var OpenSSLKdf = C_kdf.OpenSSL = {\n      /**\n       * Derives a key and IV from a password.\n       *\n       * @param {string} password The password to derive from.\n       * @param {number} keySize The size in words of the key to generate.\n       * @param {number} ivSize The size in words of the IV to generate.\n       * @param {WordArray|string} salt (Optional) A 64-bit salt to use. If omitted, a salt will be generated randomly.\n       *\n       * @return {CipherParams} A cipher params object with the key, IV, and salt.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32);\n       *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32, 'saltsalt');\n       */\n      execute: function (password, keySize, ivSize, salt, hasher) {\n        // Generate random salt\n        if (!salt) {\n          salt = WordArray.random(64 / 8);\n        }\n\n        // Derive key and IV\n        if (!hasher) {\n          var key = EvpKDF.create({\n            keySize: keySize + ivSize\n          }).compute(password, salt);\n        } else {\n          var key = EvpKDF.create({\n            keySize: keySize + ivSize,\n            hasher: hasher\n          }).compute(password, salt);\n        }\n\n        // Separate key and IV\n        var iv = WordArray.create(key.words.slice(keySize), ivSize * 4);\n        key.sigBytes = keySize * 4;\n\n        // Return params\n        return CipherParams.create({\n          key: key,\n          iv: iv,\n          salt: salt\n        });\n      }\n    };\n\n    /**\n     * A serializable cipher wrapper that derives the key from a password,\n     * and returns ciphertext as a serializable cipher params object.\n     */\n    var PasswordBasedCipher = C_lib.PasswordBasedCipher = SerializableCipher.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {KDF} kdf The key derivation function to use to generate a key and IV from a password. Default: OpenSSL\n       */\n      cfg: SerializableCipher.cfg.extend({\n        kdf: OpenSSLKdf\n      }),\n      /**\n       * Encrypts a message using a password.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {WordArray|string} message The message to encrypt.\n       * @param {string} password The password.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {CipherParams} A cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password');\n       *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password', { format: CryptoJS.format.OpenSSL });\n       */\n      encrypt: function (cipher, message, password, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Derive key and other params\n        var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, cfg.salt, cfg.hasher);\n\n        // Add IV to config\n        cfg.iv = derivedParams.iv;\n\n        // Encrypt\n        var ciphertext = SerializableCipher.encrypt.call(this, cipher, message, derivedParams.key, cfg);\n\n        // Mix in derived params\n        ciphertext.mixIn(derivedParams);\n        return ciphertext;\n      },\n      /**\n       * Decrypts serialized ciphertext using a password.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n       * @param {string} password The password.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {WordArray} The plaintext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, 'password', { format: CryptoJS.format.OpenSSL });\n       *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, 'password', { format: CryptoJS.format.OpenSSL });\n       */\n      decrypt: function (cipher, ciphertext, password, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, cfg.format);\n\n        // Derive key and other params\n        var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, ciphertext.salt, cfg.hasher);\n\n        // Add IV to config\n        cfg.iv = derivedParams.iv;\n\n        // Decrypt\n        var plaintext = SerializableCipher.decrypt.call(this, cipher, ciphertext, derivedParams.key, cfg);\n        return plaintext;\n      }\n    });\n  }();\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}