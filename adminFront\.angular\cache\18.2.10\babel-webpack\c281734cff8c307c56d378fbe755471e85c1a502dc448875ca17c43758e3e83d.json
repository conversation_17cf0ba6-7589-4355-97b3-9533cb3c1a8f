{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, isSignal, effect, InjectionToken, afterNextRender, Injector, booleanAttribute, Directive, Input, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform, _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, isObservable, of, BehaviorSubject } from 'rxjs';\nimport { A, Z, ZERO, NINE, hasModifierKey, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceObservable } from '@angular/cdk/coercion/private';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  constructor(_document,\n  /**\n   * @deprecated To be turned into a required parameter.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    this._platform = _platform;\n    /** Map of all registered message elements that have been placed into the document. */\n    this._messageRegistry = new Map();\n    /** Container for all registered messages. */\n    this._messagesContainer = null;\n    /** Unique ID for the service. */\n    this._id = `${nextId++}`;\n    this._document = _document;\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    // @breaking-change 14.0.0 Remove null check for `_platform`.\n    if (this._platform && !this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static {\n    this.ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AriaDescriber,\n      factory: AriaDescriber.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.Platform\n  }], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  constructor(initialItems, config) {\n    this._letterKeyStream = new Subject();\n    this._items = [];\n    this._selectedItemIndex = -1;\n    /** Buffer for the letters that the user has pressed */\n    this._pressedLetters = [];\n    this._selectedItem = new Subject();\n    this.selectedItem = this._selectedItem;\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  constructor(_items, injector) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = null;\n    this._wrap = false;\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    this._pageUpAndDown = {\n      enabled: false,\n      delta: 10\n    };\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    this._skipPredicateFn = item => item.disabled;\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem;\n    this.updateActiveItem(item);\n    if (this._activeItem !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem;\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem = activeItem == null ? null : activeItem;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    if (this._activeItem) {\n      const newIndex = newItems.indexOf(this._activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    /** The index of the currently active (focused) item. */\n    this._activeItemIndex = -1;\n    /** The currently active (focused) item. */\n    this._activeItem = null;\n    /** Whether or not we activate the item when it's focused. */\n    this._shouldActivationFollowFocus = false;\n    /**\n     * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n     * Right arrow are switched.\n     */\n    this._horizontalOrientation = 'ltr';\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager.\n     *\n     * The default value for this doesn't skip any elements in order to keep tree items focusable\n     * when disabled. This aligns with ARIA guidelines:\n     * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n     */\n    this._skipPredicateFn = _item => false;\n    /** Function to determine equivalent items. */\n    this._trackByFn = item => item;\n    /** Synchronous cache of the items to manage. */\n    this._items = [];\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._hasInitialFocused = false;\n    /** Stream that emits any time the focused item changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/** @docs-private */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/** @docs-private */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  constructor() {\n    this._isNoopTreeKeyManager = true;\n    // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n    // implementation that does not emit to streams.\n    this.change = new Subject();\n  }\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static {\n    this.ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InteractivityChecker,\n      factory: InteractivityChecker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    this._hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n    this._enabled = true;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._injector = inject(Injector);\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static {\n    this.ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapFactory,\n      factory: FocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  constructor(_elementRef, _focusTrapFactory,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 13.0.0\n   */\n  _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    this._previouslyFocusedElement = null;\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static {\n    this.ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTrapFocus,\n      selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n      inputs: {\n        enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n        autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n      },\n      exportAs: [\"cdkTrapFocus\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusTrapFactory\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusTrapManager)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapManager,\n      factory: FocusTrapManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._focusTrapManager = _focusTrapManager;\n    this._injector = inject(Injector);\n    this._document = _document;\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static {\n    this.ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigurableFocusTrapFactory,\n      factory: ConfigurableFocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: FocusTrapManager\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_TRAP_INERT_STRATEGY]\n    }]\n  }], null);\n})();\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  constructor(_platform, ngZone, document, options) {\n    this._platform = _platform;\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    this._mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    this._modality = new BehaviorSubject(null);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    this._lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    this._onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n      this._modality.next('keyboard');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      }\n      // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n        return;\n      }\n      // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n      this._lastTouchMs = Date.now();\n      this._modality.next('touch');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (_platform.isBrowser) {\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    if (this._platform.isBrowser) {\n      document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n      document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n      document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n    }\n  }\n  static {\n    this.ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InputModalityDetector,\n      factory: InputModalityDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: i0.NgZone\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n    }]\n  }], null);\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  constructor(elementToken, _ngZone, _document, _defaultOptions) {\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions;\n    // We inject the live element and document as `any` because the constructor signature cannot\n    // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n    // a class decorator causes TypeScript to preserve the constructor signature types.\n    this._document = _document;\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LiveAnnouncer,\n      factory: LiveAnnouncer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n    this._elementRef = _elementRef;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._contentObserver = _contentObserver;\n    this._ngZone = _ngZone;\n    this._politeness = 'polite';\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAriaLive,\n      selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n      inputs: {\n        politeness: [0, \"cdkAriaLive\", \"politeness\"],\n        duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n      },\n      exportAs: [\"cdkAriaLive\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: LiveAnnouncer\n  }, {\n    type: i1$1.ContentObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  constructor(_ngZone, _platform, _inputModalityDetector, /** @breaking-change 11.0.0 make document required */\n  document, options) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._inputModalityDetector = _inputModalityDetector;\n    /** The focus origin that the next focus event is a result of. */\n    this._origin = null;\n    /** Whether the window has just been focused. */\n    this._windowFocused = false;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    this._originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    this._elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    this._monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    this._rootNodeFocusListenerCount = new Map();\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n    };\n    /** Subject for stopping our InputModalityDetector subscription. */\n    this._stopInputModalityDetector = new Subject();\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event);\n      // We need to walk up the ancestor chain in order to support `checkChildren`.\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    };\n    this._document = document;\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusMonitor,\n      factory: FocusMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.Platform\n  }, {\n    type: InputModalityDetector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  constructor(_elementRef, _focusMonitor) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._focusOrigin = null;\n    this.cdkFocusChange = new EventEmitter();\n  }\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMonitorFocus,\n      selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n      outputs: {\n        cdkFocusChange: \"cdkFocusChange\"\n      },\n      exportAs: [\"cdkMonitorFocus\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusMonitor\n  }], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  constructor(_platform, document) {\n    this._platform = _platform;\n    this._document = document;\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HighContrastModeDetector,\n      factory: HighContrastModeDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nclass A11yModule {\n  constructor(highContrastModeDetector) {\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n  }\n  static {\n    this.ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: A11yModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ObserversModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [{\n    type: HighContrastModeDetector\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, TREE_KEY_MANAGER_FACTORY, TREE_KEY_MANAGER_FACTORY_PROVIDER, TreeKeyManager, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "APP_ID", "Injectable", "Inject", "QueryList", "isSignal", "effect", "InjectionToken", "afterNextRender", "Injector", "booleanAttribute", "Directive", "Input", "Optional", "EventEmitter", "Output", "NgModule", "i1", "Platform", "_getFocusedElementPierceShadowDom", "normalizePassiveListenerOptions", "_getEventTarget", "_getShadowRoot", "Subject", "Subscription", "isObservable", "of", "BehaviorSubject", "A", "Z", "ZERO", "NINE", "hasModifierKey", "PAGE_DOWN", "PAGE_UP", "END", "HOME", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "ALT", "CONTROL", "MAC_META", "META", "SHIFT", "tap", "debounceTime", "filter", "map", "take", "skip", "distinctUntilChanged", "takeUntil", "coerceObservable", "i1$1", "ObserversModule", "coerceElement", "BreakpointObserver", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "trim", "some", "existingId", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "val", "length", "removeAttribute", "attrValue", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "constructor", "_document", "_platform", "_messageRegistry", "Map", "_messagesContainer", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "i", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "AriaDescriber_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "serviceId", "DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS", "Typeahead", "initialItems", "config", "_letterKeyStream", "_items", "_selectedItemIndex", "_pressedLetters", "_selectedItem", "selectedItem", "typeAheadInterval", "debounceInterval", "skipPredicate", "_skipPredicateFn", "item", "get<PERSON><PERSON><PERSON>", "Error", "setItems", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destroy", "complete", "setCurrentSelectedItemIndex", "index", "items", "handle<PERSON>ey", "event", "keyCode", "next", "toLocaleUpperCase", "String", "fromCharCode", "isTyping", "reset", "pipe", "letter", "subscribe", "inputString", "ListKeyManager", "injector", "_activeItemIndex", "_activeItem", "_wrap", "_typeaheadSubscription", "EMPTY", "_vertical", "_allowedModifierKeys", "_homeAndEnd", "_pageUpAndDown", "enabled", "delta", "disabled", "tabOut", "change", "_itemChangesSubscription", "changes", "newItems", "_itemsChanged", "toArray", "_effectRef", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "keys", "withTypeAhead", "_getItemsArray", "unsubscribe", "_typeahead", "setActiveItem", "cancelTypeahead", "withHomeAndEnd", "withPageUpDown", "previousActiveItem", "updateActiveItem", "onKeydown", "modifiers", "isModifierAllowed", "every", "modifier", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "targetIndex", "_setActiveItemByIndex", "itemsLength", "preventDefault", "activeItemIndex", "activeItem", "_setActiveItemByDelta", "itemArray", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "newIndex", "ActiveDescendantKeyManager", "setInactiveStyles", "setActiveStyles", "FocusKeyManager", "arguments", "_origin", "setFocusOrigin", "origin", "focus", "TreeKeyManager", "_initializeFocus", "_hasInitialFocused", "activeIndex", "_isItemDisabled", "makeFocusable", "unfocus", "focusItem", "_shouldActivationFollowFocus", "_horizontalOrientation", "_item", "_trackByFn", "_updateActiveItemIndex", "shouldActivationFollowFocus", "horizontalOrientation", "trackBy", "typeAheadDebounceInterval", "_setTypeAhead", "_focusNextItem", "_focusPreviousItem", "_collapseCurrentItem", "_expandCurrentItem", "_focusFirstItem", "_focusLastItem", "_activateCurrentItem", "_expandAllItemsAtCurrentItemLevel", "getActiveItemIndex", "getActiveItem", "_findNextAvailableItemIndex", "_findPreviousAvailableItemIndex", "itemOrIndex", "options", "emitChangeEvent", "findIndex", "startingIndex", "_isCurrentItemExpanded", "collapse", "parent", "getParent", "expand", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "<PERSON><PERSON><PERSON><PERSON>", "find", "child", "isExpanded", "isDisabled", "itemsToExpand", "activate", "TREE_KEY_MANAGER_FACTORY", "TREE_KEY_MANAGER", "TREE_KEY_MANAGER_FACTORY_PROVIDER", "provide", "useFactory", "NoopTreeKeyManager", "_isNoopTreeKeyManager", "NOOP_TREE_KEY_MANAGER_FACTORY", "NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "isPotentiallyFocusable", "InteractivityChecker_Factory", "window", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_enabled", "value", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "_element", "_checker", "_ngZone", "deferAnchors", "_injector", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "attachAnchors", "startAnchor", "endAnchor", "removeEventListener", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "has<PERSON>tta<PERSON>", "root", "tabbable<PERSON><PERSON><PERSON>", "anchor", "isEnabled", "toggleAnchors", "fn", "setTimeout", "FocusTrapFactory", "create", "deferCaptureElements", "FocusTrapFactory_Factory", "NgZone", "CdkTrapFocus", "focusTrap", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "platform", "nativeElement", "ngAfterContentInit", "autoCapture", "_captureFocus", "ngDoCheck", "ngOnChanges", "autoCaptureChange", "firstChange", "CdkTrapFocus_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "selector", "alias", "transform", "ConfigurableFocusTrap", "_focusTrapManager", "register", "deregister", "_inertStrategy", "defer", "_enable", "preventFocus", "_disable", "allowFocus", "EventListenerFocusTrapInertStrategy", "_listener", "e", "_trapFocus", "target", "focusTrapRoot", "contains", "closest", "activeElement", "FOCUS_TRAP_INERT_STRATEGY", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "FocusTrapManager_Factory", "ConfigurableFocusTrapFactory", "configObject", "ConfigurableFocusTrapFactory_Factory", "isFakeMousedownFromScreenReader", "buttons", "detail", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "mostRecentModality", "_modality", "ngZone", "document", "_mostRecentTarget", "_lastTouchMs", "_onKeydown", "_options", "_onMousedown", "Date", "now", "_onTouchstart", "modalityDetected", "modalityChanged", "InputModalityDetector_Factory", "Document", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "uniqueIds", "LiveAnnouncer", "elementToken", "_defaultOptions", "_liveElement", "_createLiveElement", "announce", "defaultOptions", "politeness", "duration", "clearTimeout", "_previousTimeout", "_exposeAnnouncerToModals", "_currentPromise", "_currentResolve", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "modals", "modal", "ariaOwns", "LiveAnnouncer_Factory", "CdkAriaLive", "_politeness", "_subscription", "_contentObserver", "observe", "elementText", "_previousAnnouncedText", "_liveAnnouncer", "CdkAriaLive_Factory", "ContentObserver", "FocusMonitorDetectionMode", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_inputModalityDetector", "_windowFocused", "_originFromTouchInteraction", "_elementInfo", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_windowFocusListener", "_windowFocusTimeoutId", "_stopInputModalityDetector", "_rootNodeFocusAndBlurListener", "parentElement", "_onFocus", "_onBlur", "_detectionMode", "detectionMode", "IMMEDIATE", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "rootNode", "_getDocument", "cachedInfo", "subject", "info", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "_removeGlobalListeners", "focusVia", "focusedElement", "_getClosestElementsInfo", "for<PERSON>ach", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "_info", "_getWindow", "doc", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isLastInteractionFromInputLabel", "EVENTUAL", "toggle", "isFromInteraction", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "run", "rootNodeFocusListeners", "modality", "results", "mostRecentTarget", "labels", "FocusMonitor_Factory", "CdkMonitorFocus", "_focusMonitor", "_focus<PERSON><PERSON>in", "cdkFocusChange", "<PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "_monitorSubscription", "emit", "CdkMonitorFocus_Factory", "outputs", "HighContrastMode", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_breakpointSubscription", "_hasCheckedHighContrastMode", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "NONE", "testElement", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "WHITE_ON_BLACK", "BLACK_ON_WHITE", "bodyClasses", "mode", "HighContrastModeDetector_Factory", "A11yModule", "highContrastModeDetector", "A11yModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@angular/cdk/fesm2022/a11y.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, isSignal, effect, InjectionToken, afterNextRender, Injector, booleanAttribute, Directive, Input, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform, _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, isObservable, of, BehaviorSubject } from 'rxjs';\nimport { A, Z, ZERO, NINE, hasModifierKey, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceObservable } from '@angular/cdk/coercion/private';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    if (ids.some(existingId => existingId.trim() === id)) {\n        return;\n    }\n    ids.push(id);\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    const filteredIds = ids.filter(val => val !== id);\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    const attrValue = el.getAttribute(attr);\n    return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    constructor(_document, \n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        this._platform = _platform;\n        /** Map of all registered message elements that have been placed into the document. */\n        this._messageRegistry = new Map();\n        /** Container for all registered messages. */\n        this._messagesContainer = null;\n        /** Unique ID for the service. */\n        this._id = `${nextId++}`;\n        this._document = _document;\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        // @breaking-change 14.0.0 Remove null check for `_platform`.\n        if (this._platform && !this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: AriaDescriber, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: AriaDescriber, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }] });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n    constructor(initialItems, config) {\n        this._letterKeyStream = new Subject();\n        this._items = [];\n        this._selectedItemIndex = -1;\n        /** Buffer for the letters that the user has pressed */\n        this._pressedLetters = [];\n        this._selectedItem = new Subject();\n        this.selectedItem = this._selectedItem;\n        const typeAheadInterval = typeof config?.debounceInterval === 'number'\n            ? config.debounceInterval\n            : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n        if (config?.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            initialItems.length &&\n            initialItems.some(item => typeof item.getLabel !== 'function')) {\n            throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this.setItems(initialItems);\n        this._setupKeyHandler(typeAheadInterval);\n    }\n    destroy() {\n        this._pressedLetters = [];\n        this._letterKeyStream.complete();\n        this._selectedItem.complete();\n    }\n    setCurrentSelectedItemIndex(index) {\n        this._selectedItemIndex = index;\n    }\n    setItems(items) {\n        this._items = items;\n    }\n    handleKey(event) {\n        const keyCode = event.keyCode;\n        // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n        // otherwise fall back to resolving alphanumeric characters via the keyCode.\n        if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n        }\n        else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n        }\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Resets the currently stored sequence of typed letters. */\n    reset() {\n        this._pressedLetters = [];\n    }\n    _setupKeyHandler(typeAheadInterval) {\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase()))\n            .subscribe(inputString => {\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < this._items.length + 1; i++) {\n                const index = (this._selectedItemIndex + i) % this._items.length;\n                const item = this._items[index];\n                if (!this._skipPredicateFn?.(item) &&\n                    item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n                    this._selectedItem.next(item);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n    }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    constructor(_items, injector) {\n        this._items = _items;\n        this._activeItemIndex = -1;\n        this._activeItem = null;\n        this._wrap = false;\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._vertical = true;\n        this._allowedModifierKeys = [];\n        this._homeAndEnd = false;\n        this._pageUpAndDown = { enabled: false, delta: 10 };\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager. By default, disabled items are skipped.\n         */\n        this._skipPredicateFn = (item) => item.disabled;\n        /**\n         * Stream that emits any time the TAB key is pressed, so components can react\n         * when focus is shifted off of the list.\n         */\n        this.tabOut = new Subject();\n        /** Stream that emits whenever the active item of the list manager changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => this._itemsChanged(newItems.toArray()));\n        }\n        else if (isSignal(_items)) {\n            if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('ListKeyManager constructed with a signal must receive an injector');\n            }\n            this._effectRef = effect(() => this._itemsChanged(_items()), { injector });\n        }\n    }\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const items = this._getItemsArray();\n            if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n                throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n            }\n        }\n        this._typeaheadSubscription.unsubscribe();\n        const items = this._getItemsArray();\n        this._typeahead = new Typeahead(items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.setActiveItem(item);\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._typeahead?.reset();\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem;\n        this.updateActiveItem(item);\n        if (this._activeItem !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    this._typeahead?.handleKey(event);\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem;\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return !!this._typeahead && this._typeahead.isTyping();\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem = activeItem == null ? null : activeItem;\n        this._activeItemIndex = index;\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._effectRef?.destroy();\n        this._typeahead?.destroy();\n        this.tabOut.complete();\n        this.change.complete();\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        if (isSignal(this._items)) {\n            return this._items();\n        }\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n    /** Callback for when the items have changed. */\n    _itemsChanged(newItems) {\n        this._typeahead?.setItems(newItems);\n        if (this._activeItem) {\n            const newIndex = newItems.indexOf(this._activeItem);\n            if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                this._activeItemIndex = newIndex;\n                this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n            }\n        }\n    }\n}\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\nclass FocusKeyManager extends ListKeyManager {\n    constructor() {\n        super(...arguments);\n        this._origin = 'program';\n    }\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n    _initializeFocus() {\n        if (this._hasInitialFocused || this._items.length === 0) {\n            return;\n        }\n        let activeIndex = 0;\n        for (let i = 0; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n                activeIndex = i;\n                break;\n            }\n        }\n        const activeItem = this._items[activeIndex];\n        // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n        // capture the focus since the user isn't interacting with it. See #29628.\n        if (activeItem.makeFocusable) {\n            this._activeItem?.unfocus();\n            this._activeItemIndex = activeIndex;\n            this._activeItem = activeItem;\n            this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n            activeItem.makeFocusable();\n        }\n        else {\n            // Backwards compatibility for items that don't implement `makeFocusable`.\n            this.focusItem(activeIndex);\n        }\n        this._hasInitialFocused = true;\n    }\n    /**\n     *\n     * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n     * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n     * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n     * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n     * default interval of 200ms.\n     */\n    constructor(items, config) {\n        /** The index of the currently active (focused) item. */\n        this._activeItemIndex = -1;\n        /** The currently active (focused) item. */\n        this._activeItem = null;\n        /** Whether or not we activate the item when it's focused. */\n        this._shouldActivationFollowFocus = false;\n        /**\n         * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n         * Right arrow are switched.\n         */\n        this._horizontalOrientation = 'ltr';\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager.\n         *\n         * The default value for this doesn't skip any elements in order to keep tree items focusable\n         * when disabled. This aligns with ARIA guidelines:\n         * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n         */\n        this._skipPredicateFn = (_item) => false;\n        /** Function to determine equivalent items. */\n        this._trackByFn = (item) => item;\n        /** Synchronous cache of the items to manage. */\n        this._items = [];\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._hasInitialFocused = false;\n        /** Stream that emits any time the focused item changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array or Observable because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (items instanceof QueryList) {\n            this._items = items.toArray();\n            items.changes.subscribe((newItems) => {\n                this._items = newItems.toArray();\n                this._typeahead?.setItems(this._items);\n                this._updateActiveItemIndex(this._items);\n                this._initializeFocus();\n            });\n        }\n        else if (isObservable(items)) {\n            items.subscribe(newItems => {\n                this._items = newItems;\n                this._typeahead?.setItems(newItems);\n                this._updateActiveItemIndex(newItems);\n                this._initializeFocus();\n            });\n        }\n        else {\n            this._items = items;\n            this._initializeFocus();\n        }\n        if (typeof config.shouldActivationFollowFocus === 'boolean') {\n            this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n        }\n        if (config.horizontalOrientation) {\n            this._horizontalOrientation = config.horizontalOrientation;\n        }\n        if (config.skipPredicate) {\n            this._skipPredicateFn = config.skipPredicate;\n        }\n        if (config.trackBy) {\n            this._trackByFn = config.trackBy;\n        }\n        if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n            this._setTypeAhead(config.typeAheadDebounceInterval);\n        }\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._typeahead?.destroy();\n        this.change.complete();\n    }\n    /**\n     * Handles a keyboard event on the tree.\n     * @param event Keyboard event that represents the user interaction with the tree.\n     */\n    onKeydown(event) {\n        const key = event.key;\n        switch (key) {\n            case 'Tab':\n                // Return early here, in order to allow Tab to actually tab out of the tree\n                return;\n            case 'ArrowDown':\n                this._focusNextItem();\n                break;\n            case 'ArrowUp':\n                this._focusPreviousItem();\n                break;\n            case 'ArrowRight':\n                this._horizontalOrientation === 'rtl'\n                    ? this._collapseCurrentItem()\n                    : this._expandCurrentItem();\n                break;\n            case 'ArrowLeft':\n                this._horizontalOrientation === 'rtl'\n                    ? this._expandCurrentItem()\n                    : this._collapseCurrentItem();\n                break;\n            case 'Home':\n                this._focusFirstItem();\n                break;\n            case 'End':\n                this._focusLastItem();\n                break;\n            case 'Enter':\n            case ' ':\n                this._activateCurrentItem();\n                break;\n            default:\n                if (event.key === '*') {\n                    this._expandAllItemsAtCurrentItemLevel();\n                    break;\n                }\n                this._typeahead?.handleKey(event);\n                // Return here, in order to avoid preventing the default action of non-navigational\n                // keys or resetting the buffer of pressed letters.\n                return;\n        }\n        // Reset the typeahead since the user has used a navigational key.\n        this._typeahead?.reset();\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    getActiveItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The currently active item. */\n    getActiveItem() {\n        return this._activeItem;\n    }\n    /** Focus the first available item. */\n    _focusFirstItem() {\n        this.focusItem(this._findNextAvailableItemIndex(-1));\n    }\n    /** Focus the last available item. */\n    _focusLastItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n    }\n    /** Focus the next available item. */\n    _focusNextItem() {\n        this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n    }\n    /** Focus the previous available item. */\n    _focusPreviousItem() {\n        this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n    }\n    focusItem(itemOrIndex, options = {}) {\n        // Set default options\n        options.emitChangeEvent ??= true;\n        let index = typeof itemOrIndex === 'number'\n            ? itemOrIndex\n            : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n        if (index < 0 || index >= this._items.length) {\n            return;\n        }\n        const activeItem = this._items[index];\n        // If we're just setting the same item, don't re-call activate or focus\n        if (this._activeItem !== null &&\n            this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n            return;\n        }\n        const previousActiveItem = this._activeItem;\n        this._activeItem = activeItem ?? null;\n        this._activeItemIndex = index;\n        this._typeahead?.setCurrentSelectedItemIndex(index);\n        this._activeItem?.focus();\n        previousActiveItem?.unfocus();\n        if (options.emitChangeEvent) {\n            this.change.next(this._activeItem);\n        }\n        if (this._shouldActivationFollowFocus) {\n            this._activateCurrentItem();\n        }\n    }\n    _updateActiveItemIndex(newItems) {\n        const activeItem = this._activeItem;\n        if (!activeItem) {\n            return;\n        }\n        const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n        if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n            this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n        }\n    }\n    _setTypeAhead(debounceInterval) {\n        this._typeahead = new Typeahead(this._items, {\n            debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n            skipPredicate: item => this._skipPredicateFn(item),\n        });\n        this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n            this.focusItem(item);\n        });\n    }\n    _findNextAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex + 1; i < this._items.length; i++) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    _findPreviousAvailableItemIndex(startingIndex) {\n        for (let i = startingIndex - 1; i >= 0; i--) {\n            if (!this._skipPredicateFn(this._items[i])) {\n                return i;\n            }\n        }\n        return startingIndex;\n    }\n    /**\n     * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n     */\n    _collapseCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (this._isCurrentItemExpanded()) {\n            this._activeItem.collapse();\n        }\n        else {\n            const parent = this._activeItem.getParent();\n            if (!parent || this._skipPredicateFn(parent)) {\n                return;\n            }\n            this.focusItem(parent);\n        }\n    }\n    /**\n     * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n     */\n    _expandCurrentItem() {\n        if (!this._activeItem) {\n            return;\n        }\n        if (!this._isCurrentItemExpanded()) {\n            this._activeItem.expand();\n        }\n        else {\n            coerceObservable(this._activeItem.getChildren())\n                .pipe(take(1))\n                .subscribe(children => {\n                const firstChild = children.find(child => !this._skipPredicateFn(child));\n                if (!firstChild) {\n                    return;\n                }\n                this.focusItem(firstChild);\n            });\n        }\n    }\n    _isCurrentItemExpanded() {\n        if (!this._activeItem) {\n            return false;\n        }\n        return typeof this._activeItem.isExpanded === 'boolean'\n            ? this._activeItem.isExpanded\n            : this._activeItem.isExpanded();\n    }\n    _isItemDisabled(item) {\n        return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n    }\n    /** For all items that are the same level as the current item, we expand those items. */\n    _expandAllItemsAtCurrentItemLevel() {\n        if (!this._activeItem) {\n            return;\n        }\n        const parent = this._activeItem.getParent();\n        let itemsToExpand;\n        if (!parent) {\n            itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n        }\n        else {\n            itemsToExpand = coerceObservable(parent.getChildren());\n        }\n        itemsToExpand.pipe(take(1)).subscribe(items => {\n            for (const item of items) {\n                item.expand();\n            }\n        });\n    }\n    _activateCurrentItem() {\n        this._activeItem?.activate();\n    }\n}\n/** @docs-private */\nfunction TREE_KEY_MANAGER_FACTORY() {\n    return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n    providedIn: 'root',\n    factory: TREE_KEY_MANAGER_FACTORY,\n});\n/** @docs-private */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: TREE_KEY_MANAGER_FACTORY,\n};\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n    constructor() {\n        this._isNoopTreeKeyManager = true;\n        // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n        // implementation that does not emit to streams.\n        this.change = new Subject();\n    }\n    destroy() {\n        this.change.complete();\n    }\n    onKeydown() {\n        // noop\n    }\n    getActiveItemIndex() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    getActiveItem() {\n        // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n        // the active item.\n        return null;\n    }\n    focusItem() {\n        // noop\n    }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n    return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n    provide: TREE_KEY_MANAGER,\n    useFactory: NOOP_TREE_KEY_MANAGER_FACTORY,\n};\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InteractivityChecker, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }] });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false, \n    /** @breaking-change 20.0.0 param to become required */\n    _injector) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._injector = _injector;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        // TODO: remove this conditional when injector is required in the constructor.\n        if (this._injector) {\n            afterNextRender(fn, { injector: this._injector });\n        }\n        else {\n            setTimeout(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._injector = inject(Injector);\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap?.enabled || false;\n    }\n    set enabled(value) {\n        if (this.focusTrap) {\n            this.focusTrap.enabled = value;\n        }\n    }\n    constructor(_elementRef, _focusTrapFactory, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        const platform = inject(Platform);\n        if (platform.isBrowser) {\n            this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n        }\n    }\n    ngOnDestroy() {\n        this.focusTrap?.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap?.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (this.focusTrap && !this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap?.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap?.focusInitialElementWhenReady();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkTrapFocus, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"18.2.0-next.2\", type: CdkTrapFocus, isStandalone: true, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\", booleanAttribute], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocus', transform: booleanAttribute }]\n            }], autoCapture: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute }]\n            }] } });\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n        super(_element, _checker, _ngZone, _document, config.defer, injector);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    constructor() {\n        /** Focus event handler. */\n        this._listener = null;\n    }\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._focusTrapManager = _focusTrapManager;\n        this._injector = inject(Injector);\n        this._document = _document;\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: FocusTrapManager }, { token: DOCUMENT }, { token: FOCUS_TRAP_INERT_STRATEGY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: FocusTrapManager }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_TRAP_INERT_STRATEGY]\n                }] }] });\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n    // `event.detail` is zero depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `detail` works on Chrome, but fails on Firefox.\n    return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    constructor(_platform, ngZone, document, options) {\n        this._platform = _platform;\n        /**\n         * The most recently detected input modality event target. Is null if no input modality has been\n         * detected or if the associated event target is null for some unknown reason.\n         */\n        this._mostRecentTarget = null;\n        /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n        this._modality = new BehaviorSubject(null);\n        /**\n         * The timestamp of the last touch input modality. Used to determine whether mousedown events\n         * should be attributed to mouse or touch.\n         */\n        this._lastTouchMs = 0;\n        /**\n         * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n         * bound.\n         */\n        this._onKeydown = (event) => {\n            // If this is one of the keys we should ignore, then ignore it and don't update the input\n            // modality to keyboard.\n            if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n                return;\n            }\n            this._modality.next('keyboard');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onMousedown = (event) => {\n            // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n            // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n            // after the previous touch event.\n            if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n                return;\n            }\n            // Fake mousedown events are fired by some screen readers when controls are activated by the\n            // screen reader. Attribute them to keyboard input modality.\n            this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onTouchstart = (event) => {\n            // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n            // events are fired. Again, attribute to keyboard input modality.\n            if (isFakeTouchstartFromScreenReader(event)) {\n                this._modality.next('keyboard');\n                return;\n            }\n            // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n            // triggered via mouse vs touch.\n            this._lastTouchMs = Date.now();\n            this._modality.next('touch');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (_platform.isBrowser) {\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n                document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n                document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n            });\n        }\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        if (this._platform.isBrowser) {\n            document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n            document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n            document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InputModalityDetector, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT }, { token: INPUT_MODALITY_DETECTOR_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: i0.NgZone }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n                }] }] });\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        // We inject the live element and document as `any` because the constructor signature cannot\n        // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n        // a class decorator causes TypeScript to preserve the constructor signature types.\n        this._document = _document;\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        if (this._liveElement.id) {\n            this._exposeAnnouncerToModals(this._liveElement.id);\n        }\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                // For some reason in tests this can be undefined\n                // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n                this._currentResolve?.();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `SnakBarContainer` and other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: LiveAnnouncer, deps: [{ token: LIVE_ANNOUNCER_ELEMENT_TOKEN, optional: true }, { token: i0.NgZone }, { token: DOCUMENT }, { token: LIVE_ANNOUNCER_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n                }] }] });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n        this._elementRef = _elementRef;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._contentObserver = _contentObserver;\n        this._ngZone = _ngZone;\n        this._politeness = 'polite';\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkAriaLive, deps: [{ token: i0.ElementRef }, { token: LiveAnnouncer }, { token: i1$1.ContentObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkAriaLive, isStandalone: true, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: LiveAnnouncer }, { type: i1$1.ContentObserver }, { type: i0.NgZone }], propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n    /**\n     * Any mousedown, keydown, or touchstart event that happened in the previous\n     * tick or the current tick will be used to assign a focus event's origin (to\n     * either mouse, keyboard, or touch). This is the default option.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n    /**\n     * A focus event's origin is always attributed to the last corresponding\n     * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, \n    /** @breaking-change 11.0.0 make document required */\n    document, options) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._inputModalityDetector = _inputModalityDetector;\n        /** The focus origin that the next focus event is a result of. */\n        this._origin = null;\n        /** Whether the window has just been focused. */\n        this._windowFocused = false;\n        /**\n         * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n         * focus events to touch interactions requires special logic.\n         */\n        this._originFromTouchInteraction = false;\n        /** Map of elements being monitored to their info. */\n        this._elementInfo = new Map();\n        /** The number of elements currently being monitored. */\n        this._monitoredElementCount = 0;\n        /**\n         * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n         * as well as the number of monitored elements that they contain. We have to treat focus/blur\n         * handlers differently from the rest of the events, because the browser won't emit events\n         * to the document when focus moves inside of a shadow root.\n         */\n        this._rootNodeFocusListenerCount = new Map();\n        /**\n         * Event listener for `focus` events on the window.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._windowFocusListener = () => {\n            // Make a note of when the window regains focus, so we can\n            // restore the origin info for the focused element.\n            this._windowFocused = true;\n            this._windowFocusTimeoutId = window.setTimeout(() => (this._windowFocused = false));\n        };\n        /** Subject for stopping our InputModalityDetector subscription. */\n        this._stopInputModalityDetector = new Subject();\n        /**\n         * Event listener for `focus` and 'blur' events on the document.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._rootNodeFocusAndBlurListener = (event) => {\n            const target = _getEventTarget(event);\n            // We need to walk up the ancestor chain in order to support `checkChildren`.\n            for (let element = target; element; element = element.parentElement) {\n                if (event.type === 'focus') {\n                    this._onFocus(event, element);\n                }\n                else {\n                    this._onBlur(event, element);\n                }\n            }\n        };\n        this._document = document;\n        this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n    }\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            // Note: we don't want the observable to emit at all so we don't pass any parameters.\n            return of();\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusMonitor, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: InputModalityDetector }, { token: DOCUMENT, optional: true }, { token: FOCUS_MONITOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusMonitor, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i1.Platform }, { type: InputModalityDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n                }] }] });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._focusOrigin = null;\n        this.cdkFocusChange = new EventEmitter();\n    }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkMonitorFocus, deps: [{ token: i0.ElementRef }, { token: FocusMonitor }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkMonitorFocus, isStandalone: true, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: FocusMonitor }], propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n    HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n    HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n    HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    constructor(_platform, document) {\n        this._platform = _platform;\n        this._document = document;\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return HighContrastMode.NONE;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return HighContrastMode.WHITE_ON_BLACK;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return HighContrastMode.BLACK_ON_WHITE;\n        }\n        return HighContrastMode.NONE;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === HighContrastMode.BLACK_ON_WHITE) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: HighContrastModeDetector, deps: [{ token: i1.Platform }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\nclass A11yModule {\n    constructor(highContrastModeDetector) {\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: A11yModule, deps: [{ token: HighContrastModeDetector }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: A11yModule, imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: A11yModule, imports: [ObserversModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: () => [{ type: HighContrastModeDetector }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, TREE_KEY_MANAGER, TREE_KEY_MANAGER_FACTORY, TREE_KEY_MANAGER_FACTORY_PROVIDER, TreeKeyManager, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACxN,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,QAAQ,EAAEC,iCAAiC,EAAEC,+BAA+B,EAAEC,eAAe,EAAEC,cAAc,QAAQ,uBAAuB;AACrJ,SAASC,OAAO,EAAEC,YAAY,EAAEC,YAAY,EAAEC,EAAE,EAAEC,eAAe,QAAQ,MAAM;AAC/E,SAASC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;AAChM,SAASC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC5G,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,kBAAkB,QAAQ,qBAAqB;;AAExD;AACA,MAAMC,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACvC,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,IAAIF,GAAG,CAACG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACF,IAAI,CAAC,CAAC,KAAKH,EAAE,CAAC,EAAE;IAClD;EACJ;EACAC,GAAG,CAACK,IAAI,CAACN,EAAE,CAAC;EACZF,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEE,GAAG,CAACO,IAAI,CAACZ,YAAY,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAACX,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC1C,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,MAAMO,WAAW,GAAGT,GAAG,CAAChB,MAAM,CAAC0B,GAAG,IAAIA,GAAG,KAAKX,EAAE,CAAC;EACjD,IAAIU,WAAW,CAACE,MAAM,EAAE;IACpBd,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEW,WAAW,CAACF,IAAI,CAACZ,YAAY,CAAC,CAAC;EACzD,CAAC,MACI;IACDE,EAAE,CAACe,eAAe,CAACd,IAAI,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACJ,EAAE,EAAEC,IAAI,EAAE;EACnC;EACA,MAAMe,SAAS,GAAGhB,EAAE,CAACiB,YAAY,CAAChB,IAAI,CAAC;EACvC,OAAOe,SAAS,EAAEE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,mCAAmC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D;AACA,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,SAAS;EACrB;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACC,GAAG,GAAG,GAAGR,MAAM,EAAE,EAAE;IACxB,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,GAAG,GAAG5F,MAAM,CAACC,MAAM,CAAC,GAAG,GAAG,GAAGmF,MAAM,EAAE;EAC9C;EACAS,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACH,WAAW,EAAEC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;MAC7B;MACAK,YAAY,CAACL,OAAO,EAAE,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAI,CAACH,gBAAgB,CAACY,GAAG,CAACH,GAAG,EAAE;QAAEI,cAAc,EAAEP,OAAO;QAAEQ,cAAc,EAAE;MAAE,CAAC,CAAC;IAClF,CAAC,MACI,IAAI,CAAC,IAAI,CAACd,gBAAgB,CAACe,GAAG,CAACN,GAAG,CAAC,EAAE;MACtC,IAAI,CAACO,qBAAqB,CAACV,OAAO,EAAEC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACtD,IAAI,CAACS,oBAAoB,CAACb,WAAW,EAAEI,GAAG,CAAC;IAC/C;EACJ;EACAU,iBAAiBA,CAACd,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAC1C,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,CAACc,cAAc,CAACf,WAAW,CAAC,EAAE;MAC/C;IACJ;IACA,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACrD,IAAI,CAACY,uBAAuB,CAAChB,WAAW,EAAEI,GAAG,CAAC;IAClD;IACA;IACA;IACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMgB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;MACxD,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAc,KAAK,CAAC,EAAE;QAC7D,IAAI,CAACU,qBAAqB,CAACf,GAAG,CAAC;MACnC;IACJ;IACA,IAAI,IAAI,CAACP,kBAAkB,EAAEuB,UAAU,CAACtC,MAAM,KAAK,CAAC,EAAE;MAClD,IAAI,CAACe,kBAAkB,CAACwB,MAAM,CAAC,CAAC;MAChC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,MAAMC,iBAAiB,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,gBAAgB,CAAC,IAAInC,8BAA8B,KAAK,IAAI,CAACS,GAAG,IAAI,CAAC;IAC9G,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC/C,IAAI,CAACC,iCAAiC,CAACH,iBAAiB,CAACE,CAAC,CAAC,CAAC;MAC5DF,iBAAiB,CAACE,CAAC,CAAC,CAAC1C,eAAe,CAACM,8BAA8B,CAAC;IACxE;IACA,IAAI,CAACQ,kBAAkB,EAAEwB,MAAM,CAAC,CAAC;IACjC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACF,gBAAgB,CAACgC,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIhB,qBAAqBA,CAACV,OAAO,EAAEC,IAAI,EAAE;IACjC,MAAMM,cAAc,GAAG,IAAI,CAACf,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC1DtB,YAAY,CAACE,cAAc,EAAE,IAAI,CAACV,GAAG,CAAC;IACtCU,cAAc,CAACqB,WAAW,GAAG5B,OAAO;IACpC,IAAIC,IAAI,EAAE;MACNM,cAAc,CAAC/B,YAAY,CAAC,MAAM,EAAEyB,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC4B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACjC,kBAAkB,CAACkC,WAAW,CAACvB,cAAc,CAAC;IACnD,IAAI,CAACb,gBAAgB,CAACY,GAAG,CAACF,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC,EAAE;MAAEM,cAAc;MAAEC,cAAc,EAAE;IAAE,CAAC,CAAC;EAC3F;EACA;EACAU,qBAAqBA,CAACf,GAAG,EAAE;IACvB,IAAI,CAACT,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC,EAAEI,cAAc,EAAEa,MAAM,CAAC,CAAC;IACxD,IAAI,CAAC1B,gBAAgB,CAACqC,MAAM,CAAC5B,GAAG,CAAC;EACrC;EACA;EACA0B,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACjC,kBAAkB,EAAE;MACzB;IACJ;IACA,MAAMoC,kBAAkB,GAAG,mCAAmC;IAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACzC,SAAS,CAAC+B,gBAAgB,CAAC,IAAIS,kBAAkB,qBAAqB,CAAC;IACrG,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,gBAAgB,CAACpD,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC9C;MACA;MACA;MACA;MACAS,gBAAgB,CAACT,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACA,MAAMc,iBAAiB,GAAG,IAAI,CAAC1C,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC7D;IACA;IACA;IACA;IACAO,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;IAC7C;IACA;IACAF,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAACN,kBAAkB,CAAC;IACnDE,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACtD;IACA,IAAI,IAAI,CAAC7C,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC8C,SAAS,EAAE;MAC7CL,iBAAiB,CAAC1D,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IACxD;IACA,IAAI,CAACgB,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACI,iBAAiB,CAAC;IAClD,IAAI,CAACtC,kBAAkB,GAAGsC,iBAAiB;EAC/C;EACA;EACAT,iCAAiCA,CAACgB,OAAO,EAAE;IACvC;IACA,MAAMC,oBAAoB,GAAGvE,mBAAmB,CAACsE,OAAO,EAAE,kBAAkB,CAAC,CAACvF,MAAM,CAACe,EAAE,IAAIA,EAAE,CAAC0E,OAAO,CAACxD,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACtIsD,OAAO,CAACjE,YAAY,CAAC,kBAAkB,EAAEkE,oBAAoB,CAACjE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACImC,oBAAoBA,CAAC6B,OAAO,EAAEtC,GAAG,EAAE;IAC/B,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD;IACA;IACArC,mBAAmB,CAAC2E,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAACtC,EAAE,CAAC;IACrFwE,OAAO,CAACjE,YAAY,CAACY,8BAA8B,EAAE,IAAI,CAACS,GAAG,CAAC;IAC9DmB,iBAAiB,CAACR,cAAc,EAAE;EACtC;EACA;AACJ;AACA;AACA;EACIO,uBAAuBA,CAAC0B,OAAO,EAAEtC,GAAG,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxDa,iBAAiB,CAACR,cAAc,EAAE;IAClC9B,sBAAsB,CAAC+D,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAACtC,EAAE,CAAC;IACxFwE,OAAO,CAAC3D,eAAe,CAACM,8BAA8B,CAAC;EAC3D;EACA;EACAuB,4BAA4BA,CAAC8B,OAAO,EAAEtC,GAAG,EAAE;IACvC,MAAMyC,YAAY,GAAGzE,mBAAmB,CAACsE,OAAO,EAAE,kBAAkB,CAAC;IACrE,MAAMzB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD,MAAM0C,SAAS,GAAG7B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAc,CAACtC,EAAE;IAC1E,OAAO,CAAC,CAAC4E,SAAS,IAAID,YAAY,CAACD,OAAO,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA;EACA3C,eAAeA,CAACuC,OAAO,EAAEzC,OAAO,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACc,cAAc,CAAC2B,OAAO,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIzC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,MAAM8C,cAAc,GAAG9C,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,GAAGA,OAAO,EAAE,CAAC5B,IAAI,CAAC,CAAC;IACjE,MAAM2E,SAAS,GAAGN,OAAO,CAACzD,YAAY,CAAC,YAAY,CAAC;IACpD;IACA;IACA,OAAO8D,cAAc,GAAG,CAACC,SAAS,IAAIA,SAAS,CAAC3E,IAAI,CAAC,CAAC,KAAK0E,cAAc,GAAG,KAAK;EACrF;EACA;EACAhC,cAAcA,CAAC2B,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY;EAC3D;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA+F9D,aAAa,EAAvBtF,EAAE,CAAAqJ,QAAA,CAAuCtJ,QAAQ,GAAjDC,EAAE,CAAAqJ,QAAA,CAA4DnI,EAAE,CAACC,QAAQ;IAAA,CAA6C;EAAE;EAC/N;IAAS,IAAI,CAACmI,KAAK,kBADoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EACYlE,aAAa;MAAAmE,OAAA,EAAbnE,aAAa,CAAA4D,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH2G3J,EAAE,CAAA4J,iBAAA,CAGXtE,aAAa,EAAc,CAAC;IAClHuE,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAE3I,EAAE,CAACC;EAAS,CAAC,CAAC;AAAA;AAC5C;AACA,SAASiF,MAAMA,CAACJ,OAAO,EAAEC,IAAI,EAAE;EAC3B,OAAO,OAAOD,OAAO,KAAK,QAAQ,GAAG,GAAGC,IAAI,IAAI,EAAE,IAAID,OAAO,EAAE,GAAGA,OAAO;AAC7E;AACA;AACA,SAASK,YAAYA,CAACoC,OAAO,EAAEwB,SAAS,EAAE;EACtC,IAAI,CAACxB,OAAO,CAACxE,EAAE,EAAE;IACbwE,OAAO,CAACxE,EAAE,GAAG,GAAGkB,yBAAyB,IAAI8E,SAAS,IAAI5E,MAAM,EAAE,EAAE;EACxE;AACJ;AAEA,MAAM6E,sCAAsC,GAAG,GAAG;AAClD;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ5E,WAAWA,CAAC6E,YAAY,EAAEC,MAAM,EAAE;IAC9B,IAAI,CAACC,gBAAgB,GAAG,IAAI9I,OAAO,CAAC,CAAC;IACrC,IAAI,CAAC+I,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAC5B;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,aAAa,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAClC,IAAI,CAACmJ,YAAY,GAAG,IAAI,CAACD,aAAa;IACtC,MAAME,iBAAiB,GAAG,OAAOP,MAAM,EAAEQ,gBAAgB,KAAK,QAAQ,GAChER,MAAM,CAACQ,gBAAgB,GACvBX,sCAAsC;IAC5C,IAAIG,MAAM,EAAES,aAAa,EAAE;MACvB,IAAI,CAACC,gBAAgB,GAAGV,MAAM,CAACS,aAAa;IAChD;IACA,IAAI,CAAC,OAAOnB,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CS,YAAY,CAACvF,MAAM,IACnBuF,YAAY,CAAC/F,IAAI,CAAC2G,IAAI,IAAI,OAAOA,IAAI,CAACC,QAAQ,KAAK,UAAU,CAAC,EAAE;MAChE,MAAM,IAAIC,KAAK,CAAC,0EAA0E,CAAC;IAC/F;IACA,IAAI,CAACC,QAAQ,CAACf,YAAY,CAAC;IAC3B,IAAI,CAACgB,gBAAgB,CAACR,iBAAiB,CAAC;EAC5C;EACAS,OAAOA,CAAA,EAAG;IACN,IAAI,CAACZ,eAAe,GAAG,EAAE;IACzB,IAAI,CAACH,gBAAgB,CAACgB,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACZ,aAAa,CAACY,QAAQ,CAAC,CAAC;EACjC;EACAC,2BAA2BA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAAChB,kBAAkB,GAAGgB,KAAK;EACnC;EACAL,QAAQA,CAACM,KAAK,EAAE;IACZ,IAAI,CAAClB,MAAM,GAAGkB,KAAK;EACvB;EACAC,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B;IACA;IACA,IAAID,KAAK,CAACxF,GAAG,IAAIwF,KAAK,CAACxF,GAAG,CAACtB,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAACyF,gBAAgB,CAACuB,IAAI,CAACF,KAAK,CAACxF,GAAG,CAAC2F,iBAAiB,CAAC,CAAC,CAAC;IAC7D,CAAC,MACI,IAAKF,OAAO,IAAI/J,CAAC,IAAI+J,OAAO,IAAI9J,CAAC,IAAM8J,OAAO,IAAI7J,IAAI,IAAI6J,OAAO,IAAI5J,IAAK,EAAE;MAC7E,IAAI,CAACsI,gBAAgB,CAACuB,IAAI,CAACE,MAAM,CAACC,YAAY,CAACJ,OAAO,CAAC,CAAC;IAC5D;EACJ;EACA;EACAK,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,eAAe,CAAC5F,MAAM,GAAG,CAAC;EAC1C;EACA;EACAqH,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACzB,eAAe,GAAG,EAAE;EAC7B;EACAW,gBAAgBA,CAACR,iBAAiB,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAACN,gBAAgB,CAChB6B,IAAI,CAACnJ,GAAG,CAACoJ,MAAM,IAAI,IAAI,CAAC3B,eAAe,CAAClG,IAAI,CAAC6H,MAAM,CAAC,CAAC,EAAEnJ,YAAY,CAAC2H,iBAAiB,CAAC,EAAE1H,MAAM,CAAC,MAAM,IAAI,CAACuH,eAAe,CAAC5F,MAAM,GAAG,CAAC,CAAC,EAAE1B,GAAG,CAAC,MAAM,IAAI,CAACsH,eAAe,CAAChG,IAAI,CAAC,EAAE,CAAC,CAACqH,iBAAiB,CAAC,CAAC,CAAC,CAAC,CACpMO,SAAS,CAACC,WAAW,IAAI;MAC1B;MACA;MACA,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+C,MAAM,CAAC1F,MAAM,GAAG,CAAC,EAAE2C,CAAC,EAAE,EAAE;QAC7C,MAAMgE,KAAK,GAAG,CAAC,IAAI,CAAChB,kBAAkB,GAAGhD,CAAC,IAAI,IAAI,CAAC+C,MAAM,CAAC1F,MAAM;QAChE,MAAMmG,IAAI,GAAG,IAAI,CAACT,MAAM,CAACiB,KAAK,CAAC;QAC/B,IAAI,CAAC,IAAI,CAACT,gBAAgB,GAAGC,IAAI,CAAC,IAC9BA,IAAI,CAACC,QAAQ,GAAG,CAAC,CAACa,iBAAiB,CAAC,CAAC,CAAC1H,IAAI,CAAC,CAAC,CAACuE,OAAO,CAAC2D,WAAW,CAAC,KAAK,CAAC,EAAE;UACzE,IAAI,CAAC5B,aAAa,CAACmB,IAAI,CAACb,IAAI,CAAC;UAC7B;QACJ;MACJ;MACA,IAAI,CAACP,eAAe,GAAG,EAAE;IAC7B,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM8B,cAAc,CAAC;EACjBhH,WAAWA,CAACgF,MAAM,EAAEiC,QAAQ,EAAE;IAC1B,IAAI,CAACjC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,sBAAsB,GAAGnL,YAAY,CAACoL,KAAK;IAChD,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACpC,gBAAgB,GAAIC,IAAI,IAAKA,IAAI,CAACoC,QAAQ;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAI7L,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC8L,MAAM,GAAG,IAAI9L,OAAO,CAAC,CAAC;IAC3B;IACA;IACA;IACA,IAAI+I,MAAM,YAAYlK,SAAS,EAAE;MAC7B,IAAI,CAACkN,wBAAwB,GAAGhD,MAAM,CAACiD,OAAO,CAACnB,SAAS,CAAEoB,QAAQ,IAAK,IAAI,CAACC,aAAa,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC,MACI,IAAIrN,QAAQ,CAACiK,MAAM,CAAC,EAAE;MACvB,IAAI,CAACiC,QAAQ,KAAK,OAAO7C,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC9D,MAAM,IAAIuB,KAAK,CAAC,mEAAmE,CAAC;MACxF;MACA,IAAI,CAAC0C,UAAU,GAAGrN,MAAM,CAAC,MAAM,IAAI,CAACmN,aAAa,CAACnD,MAAM,CAAC,CAAC,CAAC,EAAE;QAAEiC;MAAS,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI1B,aAAaA,CAAC+C,SAAS,EAAE;IACrB,IAAI,CAAC9C,gBAAgB,GAAG8C,SAAS;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,GAAG,IAAI,EAAE;IACxB,IAAI,CAACpB,KAAK,GAAGoB,UAAU;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,uBAAuBA,CAACd,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAACJ,SAAS,GAAGI,OAAO;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIe,yBAAyBA,CAACC,SAAS,EAAE;IACjC,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACC,IAAI,EAAE;IAC1B,IAAI,CAACtB,oBAAoB,GAAGsB,IAAI;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACzD,gBAAgB,GAAG,GAAG,EAAE;IAClC,IAAI,OAAOlB,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAM8B,KAAK,GAAG,IAAI,CAAC8C,cAAc,CAAC,CAAC;MACnC,IAAI9C,KAAK,CAAC5G,MAAM,GAAG,CAAC,IAAI4G,KAAK,CAACpH,IAAI,CAAC2G,IAAI,IAAI,OAAOA,IAAI,CAACC,QAAQ,KAAK,UAAU,CAAC,EAAE;QAC7E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;MAC/F;IACJ;IACA,IAAI,CAAC0B,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IACzC,MAAM/C,KAAK,GAAG,IAAI,CAAC8C,cAAc,CAAC,CAAC;IACnC,IAAI,CAACE,UAAU,GAAG,IAAItE,SAAS,CAACsB,KAAK,EAAE;MACnCZ,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGd,SAAS;MACrFe,aAAa,EAAEE,IAAI,IAAI,IAAI,CAACD,gBAAgB,CAACC,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAAC4B,sBAAsB,GAAG,IAAI,CAAC6B,UAAU,CAAC9D,YAAY,CAAC0B,SAAS,CAACrB,IAAI,IAAI;MACzE,IAAI,CAAC0D,aAAa,CAAC1D,IAAI,CAAC;IAC5B,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACA2D,eAAeA,CAAA,EAAG;IACd,IAAI,CAACF,UAAU,EAAEvC,KAAK,CAAC,CAAC;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI0C,cAAcA,CAAC1B,OAAO,GAAG,IAAI,EAAE;IAC3B,IAAI,CAACF,WAAW,GAAGE,OAAO;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2B,cAAcA,CAAC3B,OAAO,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACvC,IAAI,CAACF,cAAc,GAAG;MAAEC,OAAO;MAAEC;IAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACAuB,aAAaA,CAAC1D,IAAI,EAAE;IAChB,MAAM8D,kBAAkB,GAAG,IAAI,CAACpC,WAAW;IAC3C,IAAI,CAACqC,gBAAgB,CAAC/D,IAAI,CAAC;IAC3B,IAAI,IAAI,CAAC0B,WAAW,KAAKoC,kBAAkB,EAAE;MACzC,IAAI,CAACxB,MAAM,CAACzB,IAAI,CAAC,IAAI,CAACY,gBAAgB,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;EACIuC,SAASA,CAACrD,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B,MAAMqD,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAC9D,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAI;MAClD,OAAO,CAACzD,KAAK,CAACyD,QAAQ,CAAC,IAAI,IAAI,CAACrC,oBAAoB,CAACpE,OAAO,CAACyG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF,QAAQxD,OAAO;MACX,KAAKlJ,GAAG;QACJ,IAAI,CAAC2K,MAAM,CAACxB,IAAI,CAAC,CAAC;QAClB;MACJ,KAAKpJ,UAAU;QACX,IAAI,IAAI,CAACqK,SAAS,IAAIoC,iBAAiB,EAAE;UACrC,IAAI,CAACG,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK7M,QAAQ;QACT,IAAI,IAAI,CAACsK,SAAS,IAAIoC,iBAAiB,EAAE;UACrC,IAAI,CAACI,qBAAqB,CAAC,CAAC;UAC5B;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK/M,WAAW;QACZ,IAAI,IAAI,CAAC4L,WAAW,IAAIe,iBAAiB,EAAE;UACvC,IAAI,CAACf,WAAW,KAAK,KAAK,GAAG,IAAI,CAACmB,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK/M,UAAU;QACX,IAAI,IAAI,CAAC6L,WAAW,IAAIe,iBAAiB,EAAE;UACvC,IAAI,CAACf,WAAW,KAAK,KAAK,GAAG,IAAI,CAACkB,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKjN,IAAI;QACL,IAAI,IAAI,CAAC2K,WAAW,IAAIkC,iBAAiB,EAAE;UACvC,IAAI,CAACK,kBAAkB,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKnN,GAAG;QACJ,IAAI,IAAI,CAAC4K,WAAW,IAAIkC,iBAAiB,EAAE;UACvC,IAAI,CAACM,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKrN,OAAO;QACR,IAAI,IAAI,CAAC8K,cAAc,CAACC,OAAO,IAAIgC,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACQ,cAAc,CAACE,KAAK;UACrE,IAAI,CAACuC,qBAAqB,CAACD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;UAChE;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKvN,SAAS;QACV,IAAI,IAAI,CAAC+K,cAAc,CAACC,OAAO,IAAIgC,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACQ,cAAc,CAACE,KAAK;UACrE,MAAMwC,WAAW,GAAG,IAAI,CAACpB,cAAc,CAAC,CAAC,CAAC1J,MAAM;UAChD,IAAI,CAAC6K,qBAAqB,CAACD,WAAW,GAAGE,WAAW,GAAGF,WAAW,GAAGE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzF;QACJ,CAAC,MACI;UACD;QACJ;MACJ;QACI,IAAIT,iBAAiB,IAAIjN,cAAc,CAAC0J,KAAK,EAAE,UAAU,CAAC,EAAE;UACxD,IAAI,CAAC8C,UAAU,EAAE/C,SAAS,CAACC,KAAK,CAAC;QACrC;QACA;QACA;QACA;IACR;IACA,IAAI,CAAC8C,UAAU,EAAEvC,KAAK,CAAC,CAAC;IACxBP,KAAK,CAACiE,cAAc,CAAC,CAAC;EAC1B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpD,gBAAgB;EAChC;EACA;EACA,IAAIqD,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACpD,WAAW;EAC3B;EACA;EACAT,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,CAAC,IAAI,CAACwC,UAAU,IAAI,IAAI,CAACA,UAAU,CAACxC,QAAQ,CAAC,CAAC;EAC1D;EACA;EACAsD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACG,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAACnB,cAAc,CAAC,CAAC,CAAC1J,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE;EACA;EACAwK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC5C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC8C,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACQ,qBAAqB,CAAC,CAAC,CAAC;EACzF;EACA;EACAT,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC7C,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAACE,KAAK,GACjC,IAAI,CAAC6C,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACxC;EACAhB,gBAAgBA,CAAC/D,IAAI,EAAE;IACnB,MAAMgF,SAAS,GAAG,IAAI,CAACzB,cAAc,CAAC,CAAC;IACvC,MAAM/C,KAAK,GAAG,OAAOR,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGgF,SAAS,CAACrH,OAAO,CAACqC,IAAI,CAAC;IACvE,MAAM8E,UAAU,GAAGE,SAAS,CAACxE,KAAK,CAAC;IACnC;IACA,IAAI,CAACkB,WAAW,GAAGoD,UAAU,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU;IACzD,IAAI,CAACrD,gBAAgB,GAAGjB,KAAK;IAC7B,IAAI,CAACiD,UAAU,EAAElD,2BAA2B,CAACC,KAAK,CAAC;EACvD;EACA;EACAH,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuB,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IACzC,IAAI,CAACjB,wBAAwB,EAAEiB,WAAW,CAAC,CAAC;IAC5C,IAAI,CAACZ,UAAU,EAAEvC,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACoD,UAAU,EAAEpD,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACgC,MAAM,CAAC/B,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACgC,MAAM,CAAChC,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIyE,qBAAqBA,CAAC5C,KAAK,EAAE;IACzB,IAAI,CAACR,KAAK,GAAG,IAAI,CAACsD,oBAAoB,CAAC9C,KAAK,CAAC,GAAG,IAAI,CAAC+C,uBAAuB,CAAC/C,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACI8C,oBAAoBA,CAAC9C,KAAK,EAAE;IACxB,MAAM1B,KAAK,GAAG,IAAI,CAAC8C,cAAc,CAAC,CAAC;IACnC,KAAK,IAAI/G,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIiE,KAAK,CAAC5G,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACpC,MAAMgE,KAAK,GAAG,CAAC,IAAI,CAACiB,gBAAgB,GAAGU,KAAK,GAAG3F,CAAC,GAAGiE,KAAK,CAAC5G,MAAM,IAAI4G,KAAK,CAAC5G,MAAM;MAC/E,MAAMmG,IAAI,GAAGS,KAAK,CAACD,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAACT,gBAAgB,CAACC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC0D,aAAa,CAAClD,KAAK,CAAC;QACzB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0E,uBAAuBA,CAAC/C,KAAK,EAAE;IAC3B,IAAI,CAACuC,qBAAqB,CAAC,IAAI,CAACjD,gBAAgB,GAAGU,KAAK,EAAEA,KAAK,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACIuC,qBAAqBA,CAAClE,KAAK,EAAE2E,aAAa,EAAE;IACxC,MAAM1E,KAAK,GAAG,IAAI,CAAC8C,cAAc,CAAC,CAAC;IACnC,IAAI,CAAC9C,KAAK,CAACD,KAAK,CAAC,EAAE;MACf;IACJ;IACA,OAAO,IAAI,CAACT,gBAAgB,CAACU,KAAK,CAACD,KAAK,CAAC,CAAC,EAAE;MACxCA,KAAK,IAAI2E,aAAa;MACtB,IAAI,CAAC1E,KAAK,CAACD,KAAK,CAAC,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAACkD,aAAa,CAAClD,KAAK,CAAC;EAC7B;EACA;EACA+C,cAAcA,CAAA,EAAG;IACb,IAAIjO,QAAQ,CAAC,IAAI,CAACiK,MAAM,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,MAAM,CAAC,CAAC;IACxB;IACA,OAAO,IAAI,CAACA,MAAM,YAAYlK,SAAS,GAAG,IAAI,CAACkK,MAAM,CAACoD,OAAO,CAAC,CAAC,GAAG,IAAI,CAACpD,MAAM;EACjF;EACA;EACAmD,aAAaA,CAACD,QAAQ,EAAE;IACpB,IAAI,CAACgB,UAAU,EAAEtD,QAAQ,CAACsC,QAAQ,CAAC;IACnC,IAAI,IAAI,CAACf,WAAW,EAAE;MAClB,MAAM0D,QAAQ,GAAG3C,QAAQ,CAAC9E,OAAO,CAAC,IAAI,CAAC+D,WAAW,CAAC;MACnD,IAAI0D,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAAC3D,gBAAgB,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAG2D,QAAQ;QAChC,IAAI,CAAC3B,UAAU,EAAElD,2BAA2B,CAAC6E,QAAQ,CAAC;MAC1D;IACJ;EACJ;AACJ;AAEA,MAAMC,0BAA0B,SAAS9D,cAAc,CAAC;EACpDmC,aAAaA,CAAClD,KAAK,EAAE;IACjB,IAAI,IAAI,CAACsE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACQ,iBAAiB,CAAC,CAAC;IACvC;IACA,KAAK,CAAC5B,aAAa,CAAClD,KAAK,CAAC;IAC1B,IAAI,IAAI,CAACsE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACS,eAAe,CAAC,CAAC;IACrC;EACJ;AACJ;AAEA,MAAMC,eAAe,SAASjE,cAAc,CAAC;EACzChH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGkL,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,SAAS;EAC5B;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAACF,OAAO,GAAGE,MAAM;IACrB,OAAO,IAAI;EACf;EACAlC,aAAaA,CAAC1D,IAAI,EAAE;IAChB,KAAK,CAAC0D,aAAa,CAAC1D,IAAI,CAAC;IACzB,IAAI,IAAI,CAAC8E,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACe,KAAK,CAAC,IAAI,CAACH,OAAO,CAAC;IACvC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,cAAc,CAAC;EACjBC,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACC,kBAAkB,IAAI,IAAI,CAACzG,MAAM,CAAC1F,MAAM,KAAK,CAAC,EAAE;MACrD;IACJ;IACA,IAAIoM,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC+C,MAAM,CAAC1F,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACuD,gBAAgB,CAAC,IAAI,CAACR,MAAM,CAAC/C,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0J,eAAe,CAAC,IAAI,CAAC3G,MAAM,CAAC/C,CAAC,CAAC,CAAC,EAAE;QACjFyJ,WAAW,GAAGzJ,CAAC;QACf;MACJ;IACJ;IACA,MAAMsI,UAAU,GAAG,IAAI,CAACvF,MAAM,CAAC0G,WAAW,CAAC;IAC3C;IACA;IACA,IAAInB,UAAU,CAACqB,aAAa,EAAE;MAC1B,IAAI,CAACzE,WAAW,EAAE0E,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC3E,gBAAgB,GAAGwE,WAAW;MACnC,IAAI,CAACvE,WAAW,GAAGoD,UAAU;MAC7B,IAAI,CAACrB,UAAU,EAAElD,2BAA2B,CAAC0F,WAAW,CAAC;MACzDnB,UAAU,CAACqB,aAAa,CAAC,CAAC;IAC9B,CAAC,MACI;MACD;MACA,IAAI,CAACE,SAAS,CAACJ,WAAW,CAAC;IAC/B;IACA,IAAI,CAACD,kBAAkB,GAAG,IAAI;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIzL,WAAWA,CAACkG,KAAK,EAAEpB,MAAM,EAAE;IACvB;IACA,IAAI,CAACoC,gBAAgB,GAAG,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAAC4E,4BAA4B,GAAG,KAAK;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACxG,gBAAgB,GAAIyG,KAAK,IAAK,KAAK;IACxC;IACA,IAAI,CAACC,UAAU,GAAIzG,IAAI,IAAKA,IAAI;IAChC;IACA,IAAI,CAACT,MAAM,GAAG,EAAE;IAChB,IAAI,CAACqC,sBAAsB,GAAGnL,YAAY,CAACoL,KAAK;IAChD,IAAI,CAACmE,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAAC1D,MAAM,GAAG,IAAI9L,OAAO,CAAC,CAAC;IAC3B;IACA;IACA;IACA,IAAIiK,KAAK,YAAYpL,SAAS,EAAE;MAC5B,IAAI,CAACkK,MAAM,GAAGkB,KAAK,CAACkC,OAAO,CAAC,CAAC;MAC7BlC,KAAK,CAAC+B,OAAO,CAACnB,SAAS,CAAEoB,QAAQ,IAAK;QAClC,IAAI,CAAClD,MAAM,GAAGkD,QAAQ,CAACE,OAAO,CAAC,CAAC;QAChC,IAAI,CAACc,UAAU,EAAEtD,QAAQ,CAAC,IAAI,CAACZ,MAAM,CAAC;QACtC,IAAI,CAACmH,sBAAsB,CAAC,IAAI,CAACnH,MAAM,CAAC;QACxC,IAAI,CAACwG,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI,IAAIrP,YAAY,CAAC+J,KAAK,CAAC,EAAE;MAC1BA,KAAK,CAACY,SAAS,CAACoB,QAAQ,IAAI;QACxB,IAAI,CAAClD,MAAM,GAAGkD,QAAQ;QACtB,IAAI,CAACgB,UAAU,EAAEtD,QAAQ,CAACsC,QAAQ,CAAC;QACnC,IAAI,CAACiE,sBAAsB,CAACjE,QAAQ,CAAC;QACrC,IAAI,CAACsD,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACxG,MAAM,GAAGkB,KAAK;MACnB,IAAI,CAACsF,gBAAgB,CAAC,CAAC;IAC3B;IACA,IAAI,OAAO1G,MAAM,CAACsH,2BAA2B,KAAK,SAAS,EAAE;MACzD,IAAI,CAACL,4BAA4B,GAAGjH,MAAM,CAACsH,2BAA2B;IAC1E;IACA,IAAItH,MAAM,CAACuH,qBAAqB,EAAE;MAC9B,IAAI,CAACL,sBAAsB,GAAGlH,MAAM,CAACuH,qBAAqB;IAC9D;IACA,IAAIvH,MAAM,CAACS,aAAa,EAAE;MACtB,IAAI,CAACC,gBAAgB,GAAGV,MAAM,CAACS,aAAa;IAChD;IACA,IAAIT,MAAM,CAACwH,OAAO,EAAE;MAChB,IAAI,CAACJ,UAAU,GAAGpH,MAAM,CAACwH,OAAO;IACpC;IACA,IAAI,OAAOxH,MAAM,CAACyH,yBAAyB,KAAK,WAAW,EAAE;MACzD,IAAI,CAACC,aAAa,CAAC1H,MAAM,CAACyH,yBAAyB,CAAC;IACxD;EACJ;EACA;EACAzG,OAAOA,CAAA,EAAG;IACN,IAAI,CAACuB,sBAAsB,CAAC4B,WAAW,CAAC,CAAC;IACzC,IAAI,CAACC,UAAU,EAAEpD,OAAO,CAAC,CAAC;IAC1B,IAAI,CAACiC,MAAM,CAAChC,QAAQ,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACI0D,SAASA,CAACrD,KAAK,EAAE;IACb,MAAMxF,GAAG,GAAGwF,KAAK,CAACxF,GAAG;IACrB,QAAQA,GAAG;MACP,KAAK,KAAK;QACN;QACA;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC6L,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,SAAS;QACV,IAAI,CAACC,kBAAkB,CAAC,CAAC;QACzB;MACJ,KAAK,YAAY;QACb,IAAI,CAACV,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAACW,oBAAoB,CAAC,CAAC,GAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAC/B;MACJ,KAAK,WAAW;QACZ,IAAI,CAACZ,sBAAsB,KAAK,KAAK,GAC/B,IAAI,CAACY,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACD,oBAAoB,CAAC,CAAC;QACjC;MACJ,KAAK,MAAM;QACP,IAAI,CAACE,eAAe,CAAC,CAAC;QACtB;MACJ,KAAK,KAAK;QACN,IAAI,CAACC,cAAc,CAAC,CAAC;QACrB;MACJ,KAAK,OAAO;MACZ,KAAK,GAAG;QACJ,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC3B;MACJ;QACI,IAAI3G,KAAK,CAACxF,GAAG,KAAK,GAAG,EAAE;UACnB,IAAI,CAACoM,iCAAiC,CAAC,CAAC;UACxC;QACJ;QACA,IAAI,CAAC9D,UAAU,EAAE/C,SAAS,CAACC,KAAK,CAAC;QACjC;QACA;QACA;IACR;IACA;IACA,IAAI,CAAC8C,UAAU,EAAEvC,KAAK,CAAC,CAAC;IACxBP,KAAK,CAACiE,cAAc,CAAC,CAAC;EAC1B;EACA;EACA4C,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/F,gBAAgB;EAChC;EACA;EACAgG,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/F,WAAW;EAC3B;EACA;EACA0F,eAAeA,CAAA,EAAG;IACd,IAAI,CAACf,SAAS,CAAC,IAAI,CAACqB,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD;EACA;EACAL,cAAcA,CAAA,EAAG;IACb,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACsB,+BAA+B,CAAC,IAAI,CAACpI,MAAM,CAAC1F,MAAM,CAAC,CAAC;EAC5E;EACA;EACAmN,cAAcA,CAAA,EAAG;IACb,IAAI,CAACX,SAAS,CAAC,IAAI,CAACqB,2BAA2B,CAAC,IAAI,CAACjG,gBAAgB,CAAC,CAAC;EAC3E;EACA;EACAwF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACsB,+BAA+B,CAAC,IAAI,CAAClG,gBAAgB,CAAC,CAAC;EAC/E;EACA4E,SAASA,CAACuB,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC;IACAA,OAAO,CAACC,eAAe,KAAK,IAAI;IAChC,IAAItH,KAAK,GAAG,OAAOoH,WAAW,KAAK,QAAQ,GACrCA,WAAW,GACX,IAAI,CAACrI,MAAM,CAACwI,SAAS,CAAC/H,IAAI,IAAI,IAAI,CAACyG,UAAU,CAACzG,IAAI,CAAC,KAAK,IAAI,CAACyG,UAAU,CAACmB,WAAW,CAAC,CAAC;IAC3F,IAAIpH,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACjB,MAAM,CAAC1F,MAAM,EAAE;MAC1C;IACJ;IACA,MAAMiL,UAAU,GAAG,IAAI,CAACvF,MAAM,CAACiB,KAAK,CAAC;IACrC;IACA,IAAI,IAAI,CAACkB,WAAW,KAAK,IAAI,IACzB,IAAI,CAAC+E,UAAU,CAAC3B,UAAU,CAAC,KAAK,IAAI,CAAC2B,UAAU,CAAC,IAAI,CAAC/E,WAAW,CAAC,EAAE;MACnE;IACJ;IACA,MAAMoC,kBAAkB,GAAG,IAAI,CAACpC,WAAW;IAC3C,IAAI,CAACA,WAAW,GAAGoD,UAAU,IAAI,IAAI;IACrC,IAAI,CAACrD,gBAAgB,GAAGjB,KAAK;IAC7B,IAAI,CAACiD,UAAU,EAAElD,2BAA2B,CAACC,KAAK,CAAC;IACnD,IAAI,CAACkB,WAAW,EAAEmE,KAAK,CAAC,CAAC;IACzB/B,kBAAkB,EAAEsC,OAAO,CAAC,CAAC;IAC7B,IAAIyB,OAAO,CAACC,eAAe,EAAE;MACzB,IAAI,CAACxF,MAAM,CAACzB,IAAI,CAAC,IAAI,CAACa,WAAW,CAAC;IACtC;IACA,IAAI,IAAI,CAAC4E,4BAA4B,EAAE;MACnC,IAAI,CAACgB,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACAZ,sBAAsBA,CAACjE,QAAQ,EAAE;IAC7B,MAAMqC,UAAU,GAAG,IAAI,CAACpD,WAAW;IACnC,IAAI,CAACoD,UAAU,EAAE;MACb;IACJ;IACA,MAAMM,QAAQ,GAAG3C,QAAQ,CAACsF,SAAS,CAAC/H,IAAI,IAAI,IAAI,CAACyG,UAAU,CAACzG,IAAI,CAAC,KAAK,IAAI,CAACyG,UAAU,CAAC3B,UAAU,CAAC,CAAC;IAClG,IAAIM,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAAC3D,gBAAgB,EAAE;MACrD,IAAI,CAACA,gBAAgB,GAAG2D,QAAQ;MAChC,IAAI,CAAC3B,UAAU,EAAElD,2BAA2B,CAAC6E,QAAQ,CAAC;IAC1D;EACJ;EACA2B,aAAaA,CAAClH,gBAAgB,EAAE;IAC5B,IAAI,CAAC4D,UAAU,GAAG,IAAItE,SAAS,CAAC,IAAI,CAACI,MAAM,EAAE;MACzCM,gBAAgB,EAAE,OAAOA,gBAAgB,KAAK,QAAQ,GAAGA,gBAAgB,GAAGd,SAAS;MACrFe,aAAa,EAAEE,IAAI,IAAI,IAAI,CAACD,gBAAgB,CAACC,IAAI;IACrD,CAAC,CAAC;IACF,IAAI,CAAC4B,sBAAsB,GAAG,IAAI,CAAC6B,UAAU,CAAC9D,YAAY,CAAC0B,SAAS,CAACrB,IAAI,IAAI;MACzE,IAAI,CAACqG,SAAS,CAACrG,IAAI,CAAC;IACxB,CAAC,CAAC;EACN;EACA0H,2BAA2BA,CAACM,aAAa,EAAE;IACvC,KAAK,IAAIxL,CAAC,GAAGwL,aAAa,GAAG,CAAC,EAAExL,CAAC,GAAG,IAAI,CAAC+C,MAAM,CAAC1F,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACzD,IAAI,CAAC,IAAI,CAACuD,gBAAgB,CAAC,IAAI,CAACR,MAAM,CAAC/C,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAOwL,aAAa;EACxB;EACAL,+BAA+BA,CAACK,aAAa,EAAE;IAC3C,KAAK,IAAIxL,CAAC,GAAGwL,aAAa,GAAG,CAAC,EAAExL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAI,CAAC,IAAI,CAACuD,gBAAgB,CAAC,IAAI,CAACR,MAAM,CAAC/C,CAAC,CAAC,CAAC,EAAE;QACxC,OAAOA,CAAC;MACZ;IACJ;IACA,OAAOwL,aAAa;EACxB;EACA;AACJ;AACA;EACId,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACxF,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,IAAI,CAACuG,sBAAsB,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACvG,WAAW,CAACwG,QAAQ,CAAC,CAAC;IAC/B,CAAC,MACI;MACD,MAAMC,MAAM,GAAG,IAAI,CAACzG,WAAW,CAAC0G,SAAS,CAAC,CAAC;MAC3C,IAAI,CAACD,MAAM,IAAI,IAAI,CAACpI,gBAAgB,CAACoI,MAAM,CAAC,EAAE;QAC1C;MACJ;MACA,IAAI,CAAC9B,SAAS,CAAC8B,MAAM,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;EACIhB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACzF,WAAW,EAAE;MACnB;IACJ;IACA,IAAI,CAAC,IAAI,CAACuG,sBAAsB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACvG,WAAW,CAAC2G,MAAM,CAAC,CAAC;IAC7B,CAAC,MACI;MACD7P,gBAAgB,CAAC,IAAI,CAACkJ,WAAW,CAAC4G,WAAW,CAAC,CAAC,CAAC,CAC3CnH,IAAI,CAAC/I,IAAI,CAAC,CAAC,CAAC,CAAC,CACbiJ,SAAS,CAACkH,QAAQ,IAAI;QACvB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,KAAK,IAAI,CAAC,IAAI,CAAC3I,gBAAgB,CAAC2I,KAAK,CAAC,CAAC;QACxE,IAAI,CAACF,UAAU,EAAE;UACb;QACJ;QACA,IAAI,CAACnC,SAAS,CAACmC,UAAU,CAAC;MAC9B,CAAC,CAAC;IACN;EACJ;EACAP,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACvG,WAAW,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,OAAO,OAAO,IAAI,CAACA,WAAW,CAACiH,UAAU,KAAK,SAAS,GACjD,IAAI,CAACjH,WAAW,CAACiH,UAAU,GAC3B,IAAI,CAACjH,WAAW,CAACiH,UAAU,CAAC,CAAC;EACvC;EACAzC,eAAeA,CAAClG,IAAI,EAAE;IAClB,OAAO,OAAOA,IAAI,CAAC4I,UAAU,KAAK,SAAS,GAAG5I,IAAI,CAAC4I,UAAU,GAAG5I,IAAI,CAAC4I,UAAU,GAAG,CAAC;EACvF;EACA;EACArB,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC,IAAI,CAAC7F,WAAW,EAAE;MACnB;IACJ;IACA,MAAMyG,MAAM,GAAG,IAAI,CAACzG,WAAW,CAAC0G,SAAS,CAAC,CAAC;IAC3C,IAAIS,aAAa;IACjB,IAAI,CAACV,MAAM,EAAE;MACTU,aAAa,GAAGlS,EAAE,CAAC,IAAI,CAAC4I,MAAM,CAACrH,MAAM,CAAC8H,IAAI,IAAIA,IAAI,CAACoI,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;IAC7E,CAAC,MACI;MACDS,aAAa,GAAGrQ,gBAAgB,CAAC2P,MAAM,CAACG,WAAW,CAAC,CAAC,CAAC;IAC1D;IACAO,aAAa,CAAC1H,IAAI,CAAC/I,IAAI,CAAC,CAAC,CAAC,CAAC,CAACiJ,SAAS,CAACZ,KAAK,IAAI;MAC3C,KAAK,MAAMT,IAAI,IAAIS,KAAK,EAAE;QACtBT,IAAI,CAACqI,MAAM,CAAC,CAAC;MACjB;IACJ,CAAC,CAAC;EACN;EACAf,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC5F,WAAW,EAAEoH,QAAQ,CAAC,CAAC;EAChC;AACJ;AACA;AACA,SAASC,wBAAwBA,CAAA,EAAG;EAChC,OAAO,CAACtI,KAAK,EAAEoH,OAAO,KAAK,IAAI/B,cAAc,CAACrF,KAAK,EAAEoH,OAAO,CAAC;AACjE;AACA;AACA,MAAMmB,gBAAgB,GAAG,IAAIxT,cAAc,CAAC,kBAAkB,EAAE;EAC5DkJ,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEsK;AACb,CAAC,CAAC;AACF;AACA,MAAME,iCAAiC,GAAG;EACtCC,OAAO,EAAEF,gBAAgB;EACzBG,UAAU,EAAEJ;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,kBAAkB,CAAC;EACrB7O,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8O,qBAAqB,GAAG,IAAI;IACjC;IACA;IACA,IAAI,CAAC/G,MAAM,GAAG,IAAI9L,OAAO,CAAC,CAAC;EAC/B;EACA6J,OAAOA,CAAA,EAAG;IACN,IAAI,CAACiC,MAAM,CAAChC,QAAQ,CAAC,CAAC;EAC1B;EACA0D,SAASA,CAAA,EAAG;IACR;EAAA;EAEJwD,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,OAAO,IAAI;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,OAAO,IAAI;EACf;EACApB,SAASA,CAAA,EAAG;IACR;EAAA;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,6BAA6BA,CAAA,EAAG;EACrC,OAAO,MAAM,IAAIF,kBAAkB,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,sCAAsC,GAAG;EAC3CL,OAAO,EAAEF,gBAAgB;EACzBG,UAAU,EAAEG;AAChB,CAAC;;AAED;AACA;AACA;AACA,MAAME,iBAAiB,CAAC;EACpBjP,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACkP,gBAAgB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBnP,WAAWA,CAACE,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACImO,UAAUA,CAACnL,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAACkM,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACnM,OAAO,EAAE;IACf,OAAOoM,WAAW,CAACpM,OAAO,CAAC,IAAIqM,gBAAgB,CAACrM,OAAO,CAAC,CAACL,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2M,UAAUA,CAACtM,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMyM,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACzM,OAAO,CAAC,CAAC;IACxD,IAAIuM,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAG3M,OAAO,CAAC2M,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAAC1M,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAACkM,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAAC3P,SAAS,CAAC8P,MAAM,IAAI,IAAI,CAAC9P,SAAS,CAAC+P,GAAG,IAAI,CAACC,wBAAwB,CAAChN,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAI2M,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAAC3M,OAAO,CAACkM,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC7P,SAAS,CAACiQ,OAAO,IAAIjN,OAAO,CAACkM,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAOlM,OAAO,CAACkN,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACnN,OAAO,EAAE4B,MAAM,EAAE;IACzB;IACA;IACA,OAAQwL,sBAAsB,CAACpN,OAAO,CAAC,IACnC,CAAC,IAAI,CAACmL,UAAU,CAACnL,OAAO,CAAC,KACxB4B,MAAM,EAAEoK,gBAAgB,IAAI,IAAI,CAACG,SAAS,CAACnM,OAAO,CAAC,CAAC;EAC7D;EACA;IAAS,IAAI,CAACS,IAAI,YAAA4M,6BAAA1M,iBAAA;MAAA,YAAAA,iBAAA,IAA+FsL,oBAAoB,EA3gC9B1U,EAAE,CAAAqJ,QAAA,CA2gC8CnI,EAAE,CAACC,QAAQ;IAAA,CAA6C;EAAE;EACjN;IAAS,IAAI,CAACmI,KAAK,kBA5gCoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EA4gCYkL,oBAAoB;MAAAjL,OAAA,EAApBiL,oBAAoB,CAAAxL,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACtK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9gC2G3J,EAAE,CAAA4J,iBAAA,CA8gCX8K,oBAAoB,EAAc,CAAC;IACzH7K,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE3I,EAAE,CAACC;EAAS,CAAC,CAAC;AAAA;AACzD;AACA;AACA;AACA;AACA;AACA,SAAS8T,eAAeA,CAACc,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAACf,YAAY;EAC9B,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASH,WAAWA,CAACpM,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACuN,WAAW,IACzBvN,OAAO,CAACwN,YAAY,IACnB,OAAOxN,OAAO,CAACyN,cAAc,KAAK,UAAU,IAAIzN,OAAO,CAACyN,cAAc,CAAC,CAAC,CAACrR,MAAO,CAAC;AAC1F;AACA;AACA,SAASsR,mBAAmBA,CAAC1N,OAAO,EAAE;EAClC,IAAI2M,QAAQ,GAAG3M,OAAO,CAAC2M,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAQD,QAAQ,KAAK,OAAO,IACxBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAASgB,aAAaA,CAAC3N,OAAO,EAAE;EAC5B,OAAO4N,cAAc,CAAC5N,OAAO,CAAC,IAAIA,OAAO,CAACoB,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAASyM,gBAAgBA,CAAC7N,OAAO,EAAE;EAC/B,OAAO8N,eAAe,CAAC9N,OAAO,CAAC,IAAIA,OAAO,CAACkM,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAAS0B,cAAcA,CAAC5N,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAAC2M,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAASkB,eAAeA,CAAC9N,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAAC2M,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASmB,gBAAgBA,CAAC/N,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAACkM,YAAY,CAAC,UAAU,CAAC,IAAIlM,OAAO,CAACkN,QAAQ,KAAK5L,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAI4L,QAAQ,GAAGlN,OAAO,CAACzD,YAAY,CAAC,UAAU,CAAC;EAC/C,OAAO,CAAC,EAAE2Q,QAAQ,IAAI,CAACc,KAAK,CAACC,QAAQ,CAACf,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASR,gBAAgBA,CAAC1M,OAAO,EAAE;EAC/B,IAAI,CAAC+N,gBAAgB,CAAC/N,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAMkN,QAAQ,GAAGe,QAAQ,CAACjO,OAAO,CAACzD,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOyR,KAAK,CAACd,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASF,wBAAwBA,CAAChN,OAAO,EAAE;EACvC,IAAI2M,QAAQ,GAAG3M,OAAO,CAAC2M,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIsB,SAAS,GAAGvB,QAAQ,KAAK,OAAO,IAAI3M,OAAO,CAACoB,IAAI;EACpD,OAAQ8M,SAAS,KAAK,MAAM,IACxBA,SAAS,KAAK,UAAU,IACxBvB,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA;AACA;AACA;AACA,SAASS,sBAAsBA,CAACpN,OAAO,EAAE;EACrC;EACA,IAAI2N,aAAa,CAAC3N,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAQ0N,mBAAmB,CAAC1N,OAAO,CAAC,IAChC6N,gBAAgB,CAAC7N,OAAO,CAAC,IACzBA,OAAO,CAACkM,YAAY,CAAC,iBAAiB,CAAC,IACvC6B,gBAAgB,CAAC/N,OAAO,CAAC;AACjC;AACA;AACA,SAASyM,SAASA,CAAC0B,IAAI,EAAE;EACrB;EACA,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAKf,MAAM;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,SAAS,CAAC;EACZ;EACA,IAAI7J,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8J,QAAQ;EACxB;EACA,IAAI9J,OAAOA,CAAC+J,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACC,YAAY,CAAC;MACpD,IAAI,CAACE,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACE,UAAU,CAAC;IACtD;EACJ;EACA5R,WAAWA,CAAC8R,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/R,SAAS,EAAEgS,YAAY,GAAG,KAAK,EACxE;EACAC,SAAS,EAAE;IACP,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC/R,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiS,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,MAAM,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAG,MAAM,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC/D,IAAI,CAACd,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACQ,YAAY,EAAE;MACf,IAAI,CAACO,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACA1M,OAAOA,CAAA,EAAG;IACN,MAAM2M,WAAW,GAAG,IAAI,CAACd,YAAY;IACrC,MAAMe,SAAS,GAAG,IAAI,CAACd,UAAU;IACjC,IAAIa,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACP,mBAAmB,CAAC;MAClEK,WAAW,CAAC5Q,MAAM,CAAC,CAAC;IACxB;IACA,IAAI6Q,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,iBAAiB,CAAC;MAC9DI,SAAS,CAAC7Q,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAAC8P,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACO,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACH,OAAO,CAACY,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAACjB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACkB,aAAa,CAAC,CAAC;QACxC,IAAI,CAAClB,YAAY,CAACmB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACV,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACR,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACiB,aAAa,CAAC,CAAC;QACtC,IAAI,CAACjB,UAAU,CAACkB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACR,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACR,QAAQ,CAACiB,UAAU,EAAE;MAC1B,IAAI,CAACjB,QAAQ,CAACiB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACrB,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAACiB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACpB,UAAU,EAAE,IAAI,CAACE,QAAQ,CAACmB,WAAW,CAAC;MACjF,IAAI,CAACd,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIe,4BAA4BA,CAAC5F,OAAO,EAAE;IAClC,OAAO,IAAI6F,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAAChG,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiG,kCAAkCA,CAACjG,OAAO,EAAE;IACxC,OAAO,IAAI6F,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACb,yBAAyB,CAACjF,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkG,iCAAiCA,CAAClG,OAAO,EAAE;IACvC,OAAO,IAAI6F,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACf,wBAAwB,CAAC/E,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACImG,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC7B,QAAQ,CAAC9P,gBAAgB,CAAC,qBAAqB0R,KAAK,KAAK,GAAG,kBAAkBA,KAAK,KAAK,GAAG,cAAcA,KAAK,GAAG,CAAC;IACvI,IAAI,OAAOtP,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0R,OAAO,CAACrU,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACrC;QACA,IAAI0R,OAAO,CAAC1R,CAAC,CAAC,CAACmN,YAAY,CAAC,aAAasE,KAAK,EAAE,CAAC,EAAE;UAC/CE,OAAO,CAACC,IAAI,CAAC,gDAAgDH,KAAK,KAAK,GACnE,sBAAsBA,KAAK,4BAA4B,GACvD,qCAAqC,EAAEC,OAAO,CAAC1R,CAAC,CAAC,CAAC;QAC1D,CAAC,MACI,IAAI0R,OAAO,CAAC1R,CAAC,CAAC,CAACmN,YAAY,CAAC,oBAAoBsE,KAAK,EAAE,CAAC,EAAE;UAC3DE,OAAO,CAACC,IAAI,CAAC,uDAAuDH,KAAK,KAAK,GAC1E,sBAAsBA,KAAK,sCAAsC,GACjE,2BAA2B,EAAEC,OAAO,CAAC1R,CAAC,CAAC,CAAC;QAChD;MACJ;IACJ;IACA,IAAIyR,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAACrU,MAAM,GAAGqU,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACG,wBAAwB,CAAC,IAAI,CAAChC,QAAQ,CAAC;IACrF;IACA,OAAO6B,OAAO,CAACrU,MAAM,GACfqU,OAAO,CAACA,OAAO,CAACrU,MAAM,GAAG,CAAC,CAAC,GAC3B,IAAI,CAACyU,uBAAuB,CAAC,IAAI,CAACjC,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIwB,mBAAmBA,CAAChG,OAAO,EAAE;IACzB;IACA,MAAM0G,iBAAiB,GAAG,IAAI,CAAClC,QAAQ,CAACmC,aAAa,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;IACpG,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAI,CAAC,OAAO5P,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C4P,iBAAiB,CAAC5E,YAAY,CAAC,mBAAmB,CAAC,EAAE;QACrDwE,OAAO,CAACC,IAAI,CAAC,yDAAyD,GAClE,0DAA0D,GAC1D,0BAA0B,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,OAAO5P,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,CAAC,IAAI,CAAC2N,QAAQ,CAAC1B,WAAW,CAAC2D,iBAAiB,CAAC,EAAE;QAC/CJ,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEG,iBAAiB,CAAC;MAC7F;MACA,IAAI,CAAC,IAAI,CAACjC,QAAQ,CAAC1B,WAAW,CAAC2D,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,EAAE5I,KAAK,CAACgC,OAAO,CAAC;QAC9B,OAAO,CAAC,CAAC4G,cAAc;MAC3B;MACAF,iBAAiB,CAAC1I,KAAK,CAACgC,OAAO,CAAC;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACiF,yBAAyB,CAACjF,OAAO,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIiF,yBAAyBA,CAACjF,OAAO,EAAE;IAC/B,MAAM0G,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAC1I,KAAK,CAACgC,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAAC0G,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACI3B,wBAAwBA,CAAC/E,OAAO,EAAE;IAC9B,MAAM0G,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAC1I,KAAK,CAACgC,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAAC0G,iBAAiB;EAC9B;EACA;AACJ;AACA;EACIG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChC,YAAY;EAC5B;EACA;EACA2B,wBAAwBA,CAACM,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACrC,QAAQ,CAAC1B,WAAW,CAAC+D,IAAI,CAAC,IAAI,IAAI,CAACrC,QAAQ,CAACvC,UAAU,CAAC4E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA,MAAMpG,QAAQ,GAAGoG,IAAI,CAACpG,QAAQ;IAC9B,KAAK,IAAI/L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+L,QAAQ,CAAC1O,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACtC,MAAMoS,aAAa,GAAGrG,QAAQ,CAAC/L,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAACoQ,wBAAwB,CAAC9F,QAAQ,CAAC/L,CAAC,CAAC,CAAC,GAC1C,IAAI;MACV,IAAIoS,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAN,uBAAuBA,CAACK,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACrC,QAAQ,CAAC1B,WAAW,CAAC+D,IAAI,CAAC,IAAI,IAAI,CAACrC,QAAQ,CAACvC,UAAU,CAAC4E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,MAAMpG,QAAQ,GAAGoG,IAAI,CAACpG,QAAQ;IAC9B,KAAK,IAAI/L,CAAC,GAAG+L,QAAQ,CAAC1O,MAAM,GAAG,CAAC,EAAE2C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMoS,aAAa,GAAGrG,QAAQ,CAAC/L,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAACqQ,uBAAuB,CAAC/F,QAAQ,CAAC/L,CAAC,CAAC,CAAC,GACzC,IAAI;MACV,IAAIoS,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAxB,aAAaA,CAAA,EAAG;IACZ,MAAMyB,MAAM,GAAG,IAAI,CAACrU,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACyP,qBAAqB,CAAC,IAAI,CAACJ,QAAQ,EAAE6C,MAAM,CAAC;IACjDA,MAAM,CAACxR,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CuR,MAAM,CAACxR,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CuR,MAAM,CAACrV,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOqV,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIzC,qBAAqBA,CAAC0C,SAAS,EAAED,MAAM,EAAE;IACrC;IACA;IACAC,SAAS,GAAGD,MAAM,CAACrV,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGqV,MAAM,CAAC/U,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIiV,aAAaA,CAAC7M,OAAO,EAAE;IACnB,IAAI,IAAI,CAACgK,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAAClK,OAAO,EAAE,IAAI,CAACgK,YAAY,CAAC;MACtD,IAAI,CAACE,qBAAqB,CAAClK,OAAO,EAAE,IAAI,CAACiK,UAAU,CAAC;IACxD;EACJ;EACA;EACAyB,gBAAgBA,CAACoB,EAAE,EAAE;IACjB;IACA,IAAI,IAAI,CAACvC,SAAS,EAAE;MAChBhX,eAAe,CAACuZ,EAAE,EAAE;QAAExN,QAAQ,EAAE,IAAI,CAACiL;MAAU,CAAC,CAAC;IACrD,CAAC,MACI;MACDwC,UAAU,CAACD,EAAE,CAAC;IAClB;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EACnB3U,WAAWA,CAAC+R,QAAQ,EAAEC,OAAO,EAAE/R,SAAS,EAAE;IACtC,IAAI,CAAC8R,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,SAAS,GAAGxX,MAAM,CAACS,QAAQ,CAAC;IACjC,IAAI,CAAC8E,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI2U,MAAMA,CAAC1R,OAAO,EAAE2R,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIrD,SAAS,CAACtO,OAAO,EAAE,IAAI,CAAC6O,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC/R,SAAS,EAAE4U,oBAAoB,EAAE,IAAI,CAAC3C,SAAS,CAAC;EACpH;EACA;IAAS,IAAI,CAACvO,IAAI,YAAAmR,yBAAAjR,iBAAA;MAAA,YAAAA,iBAAA,IAA+F8Q,gBAAgB,EAr5C1Bla,EAAE,CAAAqJ,QAAA,CAq5C0CqL,oBAAoB,GAr5ChE1U,EAAE,CAAAqJ,QAAA,CAq5C2ErJ,EAAE,CAACsa,MAAM,GAr5CtFta,EAAE,CAAAqJ,QAAA,CAq5CiGtJ,QAAQ;IAAA,CAA6C;EAAE;EACjQ;IAAS,IAAI,CAACuJ,KAAK,kBAt5CoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EAs5CY0Q,gBAAgB;MAAAzQ,OAAA,EAAhByQ,gBAAgB,CAAAhR,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAx5C2G3J,EAAE,CAAA4J,iBAAA,CAw5CXsQ,gBAAgB,EAAc,CAAC;IACrHrQ,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE6K;EAAqB,CAAC,EAAE;IAAE7K,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,EAAE;IAAEzQ,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACpGH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA,MAAMwa,YAAY,CAAC;EACf;EACA,IAAIrN,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsN,SAAS,EAAEtN,OAAO,IAAI,KAAK;EAC3C;EACA,IAAIA,OAAOA,CAAC+J,KAAK,EAAE;IACf,IAAI,IAAI,CAACuD,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACtN,OAAO,GAAG+J,KAAK;IAClC;EACJ;EACA1R,WAAWA,CAACkV,WAAW,EAAEC,iBAAiB;EAC1C;AACJ;AACA;AACA;EACIlV,SAAS,EAAE;IACP,IAAI,CAACiV,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,MAAMC,QAAQ,GAAG3a,MAAM,CAACkB,QAAQ,CAAC;IACjC,IAAIyZ,QAAQ,CAACrS,SAAS,EAAE;MACpB,IAAI,CAACiS,SAAS,GAAG,IAAI,CAACE,iBAAiB,CAACP,MAAM,CAAC,IAAI,CAACM,WAAW,CAACI,aAAa,EAAE,IAAI,CAAC;IACxF;EACJ;EACAxT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmT,SAAS,EAAEnP,OAAO,CAAC,CAAC;IACzB;IACA;IACA,IAAI,IAAI,CAACsP,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAAC9J,KAAK,CAAC,CAAC;MACtC,IAAI,CAAC8J,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,SAAS,EAAEzC,aAAa,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACgD,WAAW,EAAE;MAClB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACd,WAAW,CAAC,CAAC,EAAE;MACjD,IAAI,CAACc,SAAS,CAACzC,aAAa,CAAC,CAAC;IAClC;EACJ;EACAmD,WAAWA,CAAC1N,OAAO,EAAE;IACjB,MAAM2N,iBAAiB,GAAG3N,OAAO,CAAC,aAAa,CAAC;IAChD,IAAI2N,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WAAW,IAC9B,IAAI,CAACL,WAAW,IAChB,IAAI,CAACP,SAAS,EAAEd,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACsB,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACL,yBAAyB,GAAGvZ,iCAAiC,CAAC,CAAC;IACpE,IAAI,CAACoZ,SAAS,EAAE/B,4BAA4B,CAAC,CAAC;EAClD;EACA;IAAS,IAAI,CAACvP,IAAI,YAAAmS,qBAAAjS,iBAAA;MAAA,YAAAA,iBAAA,IAA+FmR,YAAY,EA19CtBva,EAAE,CAAAsb,iBAAA,CA09CsCtb,EAAE,CAACub,UAAU,GA19CrDvb,EAAE,CAAAsb,iBAAA,CA09CgEpB,gBAAgB,GA19ClFla,EAAE,CAAAsb,iBAAA,CA09C6Fvb,QAAQ;IAAA,CAA4C;EAAE;EAC5P;IAAS,IAAI,CAACyb,IAAI,kBA39CqFxb,EAAE,CAAAyb,iBAAA;MAAA5R,IAAA,EA29CJ0Q,YAAY;MAAAmB,SAAA;MAAAC,MAAA;QAAAzO,OAAA,iCAAiGvM,gBAAgB;QAAAoa,WAAA,gDAA2Dpa,gBAAgB;MAAA;MAAAib,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA39CtM9b,EAAE,CAAA+b,wBAAA,EAAF/b,EAAE,CAAAgc,oBAAA;IAAA,EA29CyQ;EAAE;AACxX;AACA;EAAA,QAAArS,SAAA,oBAAAA,SAAA,KA79C2G3J,EAAE,CAAA4J,iBAAA,CA69CX2Q,YAAY,EAAc,CAAC;IACjH1Q,IAAI,EAAEjJ,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCmS,QAAQ,EAAE,gBAAgB;MAC1BL,QAAQ,EAAE,cAAc;MACxBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhS,IAAI,EAAE7J,EAAE,CAACub;EAAW,CAAC,EAAE;IAAE1R,IAAI,EAAEqQ;EAAiB,CAAC,EAAE;IAAErQ,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACpGH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEmN,OAAO,EAAE,CAAC;MACnCrD,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEoS,KAAK,EAAE,cAAc;QAAEC,SAAS,EAAExb;MAAiB,CAAC;IACjE,CAAC,CAAC;IAAEoa,WAAW,EAAE,CAAC;MACdlR,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC;QAAEoS,KAAK,EAAE,yBAAyB;QAAEC,SAAS,EAAExb;MAAiB,CAAC;IAC5E,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyb,qBAAqB,SAASrF,SAAS,CAAC;EAC1C;EACA,IAAI7J,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC8J,QAAQ;EACxB;EACA,IAAI9J,OAAOA,CAAC+J,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACqF,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACD,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IAC3C;EACJ;EACAhX,WAAWA,CAAC8R,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/R,SAAS,EAAE6W,iBAAiB,EAAEG,cAAc,EAAEnS,MAAM,EAAEmC,QAAQ,EAAE;IACrG,KAAK,CAAC6K,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE/R,SAAS,EAAE6E,MAAM,CAACoS,KAAK,EAAEjQ,QAAQ,CAAC;IACrE,IAAI,CAAC6P,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACH,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACzC;EACA;EACAjR,OAAOA,CAAA,EAAG;IACN,IAAI,CAACgR,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IACvC,KAAK,CAAClR,OAAO,CAAC,CAAC;EACnB;EACA;EACAqR,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,cAAc,CAACG,YAAY,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC5C,aAAa,CAAC,IAAI,CAAC;EAC5B;EACA;EACA6C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,cAAc,CAACK,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC9C,aAAa,CAAC,KAAK,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM+C,mCAAmC,CAAC;EACtCvX,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACwX,SAAS,GAAG,IAAI;EACzB;EACA;EACAJ,YAAYA,CAACnC,SAAS,EAAE;IACpB;IACA,IAAI,IAAI,CAACuC,SAAS,EAAE;MAChBvC,SAAS,CAAChV,SAAS,CAAC0S,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAAC;IAC1E;IACA,IAAI,CAACA,SAAS,GAAIC,CAAC,IAAK,IAAI,CAACC,UAAU,CAACzC,SAAS,EAAEwC,CAAC,CAAC;IACrDxC,SAAS,CAACjD,OAAO,CAACY,iBAAiB,CAAC,MAAM;MACtCqC,SAAS,CAAChV,SAAS,CAAC6S,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC0E,SAAS,EAAE,IAAI,CAAC;IACvE,CAAC,CAAC;EACN;EACA;EACAF,UAAUA,CAACrC,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAACuC,SAAS,EAAE;MACjB;IACJ;IACAvC,SAAS,CAAChV,SAAS,CAAC0S,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAAC;IACtE,IAAI,CAACA,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAACzC,SAAS,EAAE7O,KAAK,EAAE;IACzB,MAAMuR,MAAM,GAAGvR,KAAK,CAACuR,MAAM;IAC3B,MAAMC,aAAa,GAAG3C,SAAS,CAACnD,QAAQ;IACxC;IACA;IACA,IAAI6F,MAAM,IAAI,CAACC,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,OAAO,GAAG,sBAAsB,CAAC,EAAE;MACxF;MACA;MACA;MACApD,UAAU,CAAC,MAAM;QACb;QACA,IAAIO,SAAS,CAACtN,OAAO,IAAI,CAACiQ,aAAa,CAACC,QAAQ,CAAC5C,SAAS,CAAChV,SAAS,CAAC8X,aAAa,CAAC,EAAE;UACjF9C,SAAS,CAAC1C,yBAAyB,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA,MAAMyF,yBAAyB,GAAG,IAAI/c,cAAc,CAAC,2BAA2B,CAAC;;AAEjF;AACA,MAAMgd,gBAAgB,CAAC;EACnBjY,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACkY,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;EACInB,QAAQA,CAAC9B,SAAS,EAAE;IAChB;IACA,IAAI,CAACiD,eAAe,GAAG,IAAI,CAACA,eAAe,CAACva,MAAM,CAACwa,EAAE,IAAIA,EAAE,KAAKlD,SAAS,CAAC;IAC1E,IAAImD,KAAK,GAAG,IAAI,CAACF,eAAe;IAChC,IAAIE,KAAK,CAAC9Y,MAAM,EAAE;MACd8Y,KAAK,CAACA,KAAK,CAAC9Y,MAAM,GAAG,CAAC,CAAC,CAAC+X,QAAQ,CAAC,CAAC;IACtC;IACAe,KAAK,CAACpZ,IAAI,CAACiW,SAAS,CAAC;IACrBA,SAAS,CAACkC,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIH,UAAUA,CAAC/B,SAAS,EAAE;IAClBA,SAAS,CAACoC,QAAQ,CAAC,CAAC;IACpB,MAAMe,KAAK,GAAG,IAAI,CAACF,eAAe;IAClC,MAAMjW,CAAC,GAAGmW,KAAK,CAAChV,OAAO,CAAC6R,SAAS,CAAC;IAClC,IAAIhT,CAAC,KAAK,CAAC,CAAC,EAAE;MACVmW,KAAK,CAACC,MAAM,CAACpW,CAAC,EAAE,CAAC,CAAC;MAClB,IAAImW,KAAK,CAAC9Y,MAAM,EAAE;QACd8Y,KAAK,CAACA,KAAK,CAAC9Y,MAAM,GAAG,CAAC,CAAC,CAAC6X,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;IAAS,IAAI,CAACxT,IAAI,YAAA2U,yBAAAzU,iBAAA;MAAA,YAAAA,iBAAA,IAA+FoU,gBAAgB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAAClU,KAAK,kBAxnDoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EAwnDYgU,gBAAgB;MAAA/T,OAAA,EAAhB+T,gBAAgB,CAAAtU,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1nD2G3J,EAAE,CAAA4J,iBAAA,CA0nDX4T,gBAAgB,EAAc,CAAC;IACrH3T,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMoU,4BAA4B,CAAC;EAC/BvY,WAAWA,CAAC+R,QAAQ,EAAEC,OAAO,EAAE8E,iBAAiB,EAAE7W,SAAS,EAAEgX,cAAc,EAAE;IACzE,IAAI,CAAClF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8E,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC5E,SAAS,GAAGxX,MAAM,CAACS,QAAQ,CAAC;IACjC,IAAI,CAAC8E,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACgX,cAAc,GAAGA,cAAc,IAAI,IAAIM,mCAAmC,CAAC,CAAC;EACrF;EACA3C,MAAMA,CAAC1R,OAAO,EAAE4B,MAAM,GAAG;IAAEoS,KAAK,EAAE;EAAM,CAAC,EAAE;IACvC,IAAIsB,YAAY;IAChB,IAAI,OAAO1T,MAAM,KAAK,SAAS,EAAE;MAC7B0T,YAAY,GAAG;QAAEtB,KAAK,EAAEpS;MAAO,CAAC;IACpC,CAAC,MACI;MACD0T,YAAY,GAAG1T,MAAM;IACzB;IACA,OAAO,IAAI+R,qBAAqB,CAAC3T,OAAO,EAAE,IAAI,CAAC6O,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC/R,SAAS,EAAE,IAAI,CAAC6W,iBAAiB,EAAE,IAAI,CAACG,cAAc,EAAEuB,YAAY,EAAE,IAAI,CAACtG,SAAS,CAAC;EACrK;EACA;IAAS,IAAI,CAACvO,IAAI,YAAA8U,qCAAA5U,iBAAA;MAAA,YAAAA,iBAAA,IAA+F0U,4BAA4B,EAppDtC9d,EAAE,CAAAqJ,QAAA,CAopDsDqL,oBAAoB,GAppD5E1U,EAAE,CAAAqJ,QAAA,CAopDuFrJ,EAAE,CAACsa,MAAM,GAppDlGta,EAAE,CAAAqJ,QAAA,CAopD6GmU,gBAAgB,GAppD/Hxd,EAAE,CAAAqJ,QAAA,CAopD0ItJ,QAAQ,GAppDpJC,EAAE,CAAAqJ,QAAA,CAopD+JkU,yBAAyB;IAAA,CAA6D;EAAE;EAChW;IAAS,IAAI,CAACjU,KAAK,kBArpDoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EAqpDYsU,4BAA4B;MAAArU,OAAA,EAA5BqU,4BAA4B,CAAA5U,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvpD2G3J,EAAE,CAAA4J,iBAAA,CAupDXkU,4BAA4B,EAAc,CAAC;IACjIjU,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE6K;EAAqB,CAAC,EAAE;IAAE7K,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,EAAE;IAAEzQ,IAAI,EAAE2T;EAAiB,CAAC,EAAE;IAAE3T,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAChIH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAACyT,yBAAyB;IACpC,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,SAASU,+BAA+BA,CAACtS,KAAK,EAAE;EAC5C;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK,CAACuS,OAAO,KAAK,CAAC,IAAIvS,KAAK,CAACwS,MAAM,KAAK,CAAC;AACpD;AACA;AACA,SAASC,gCAAgCA,CAACzS,KAAK,EAAE;EAC7C,MAAM0S,KAAK,GAAI1S,KAAK,CAAC2S,OAAO,IAAI3S,KAAK,CAAC2S,OAAO,CAAC,CAAC,CAAC,IAAM3S,KAAK,CAAC4S,cAAc,IAAI5S,KAAK,CAAC4S,cAAc,CAAC,CAAC,CAAE;EACtG;EACA;EACA;EACA;EACA,OAAQ,CAAC,CAACF,KAAK,IACXA,KAAK,CAACG,UAAU,KAAK,CAAC,CAAC,KACtBH,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIJ,KAAK,CAACI,OAAO,KAAK,CAAC,CAAC,KAC7CJ,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIL,KAAK,CAACK,OAAO,KAAK,CAAC,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAIne,cAAc,CAAC,qCAAqC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoe,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAAClc,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+b,eAAe,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAG1d,+BAA+B,CAAC;EACjE2d,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,SAAS,CAACnI,KAAK;EAC/B;EACA1R,WAAWA,CAACE,SAAS,EAAE4Z,MAAM,EAAEC,QAAQ,EAAEzM,OAAO,EAAE;IAC9C,IAAI,CAACpN,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAAC8Z,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACH,SAAS,GAAG,IAAIxd,eAAe,CAAC,IAAI,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC4d,YAAY,GAAG,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAI9T,KAAK,IAAK;MACzB;MACA;MACA,IAAI,IAAI,CAAC+T,QAAQ,EAAEb,UAAU,EAAExa,IAAI,CAACuH,OAAO,IAAIA,OAAO,KAAKD,KAAK,CAACC,OAAO,CAAC,EAAE;QACvE;MACJ;MACA,IAAI,CAACwT,SAAS,CAACvT,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAI,CAAC0T,iBAAiB,GAAGje,eAAe,CAACqK,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACgU,YAAY,GAAIhU,KAAK,IAAK;MAC3B;MACA;MACA;MACA,IAAIiU,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACL,YAAY,GAAGV,eAAe,EAAE;QAClD;MACJ;MACA;MACA;MACA,IAAI,CAACM,SAAS,CAACvT,IAAI,CAACoS,+BAA+B,CAACtS,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;MAClF,IAAI,CAAC4T,iBAAiB,GAAGje,eAAe,CAACqK,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACmU,aAAa,GAAInU,KAAK,IAAK;MAC5B;MACA;MACA,IAAIyS,gCAAgC,CAACzS,KAAK,CAAC,EAAE;QACzC,IAAI,CAACyT,SAAS,CAACvT,IAAI,CAAC,UAAU,CAAC;QAC/B;MACJ;MACA;MACA;MACA,IAAI,CAAC2T,YAAY,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACT,SAAS,CAACvT,IAAI,CAAC,OAAO,CAAC;MAC5B,IAAI,CAAC0T,iBAAiB,GAAGje,eAAe,CAACqK,KAAK,CAAC;IACnD,CAAC;IACD,IAAI,CAAC+T,QAAQ,GAAG;MACZ,GAAGd,uCAAuC;MAC1C,GAAG/L;IACP,CAAC;IACD;IACA,IAAI,CAACkN,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAACjT,IAAI,CAAC9I,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC2c,eAAe,GAAG,IAAI,CAACD,gBAAgB,CAAC5T,IAAI,CAAC7I,oBAAoB,CAAC,CAAC,CAAC;IACzE;IACA;IACA,IAAImC,SAAS,CAAC8C,SAAS,EAAE;MACrB8W,MAAM,CAAClH,iBAAiB,CAAC,MAAM;QAC3BmH,QAAQ,CAACjH,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACoH,UAAU,EAAEV,4BAA4B,CAAC;QACnFO,QAAQ,CAACjH,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACsH,YAAY,EAAEZ,4BAA4B,CAAC;QACvFO,QAAQ,CAACjH,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACyH,aAAa,EAAEf,4BAA4B,CAAC;MAC7F,CAAC,CAAC;IACN;EACJ;EACA1X,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+X,SAAS,CAAC9T,QAAQ,CAAC,CAAC;IACzB,IAAI,IAAI,CAAC7F,SAAS,CAAC8C,SAAS,EAAE;MAC1B+W,QAAQ,CAACpH,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACuH,UAAU,EAAEV,4BAA4B,CAAC;MACtFO,QAAQ,CAACpH,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACyH,YAAY,EAAEZ,4BAA4B,CAAC;MAC1FO,QAAQ,CAACpH,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC4H,aAAa,EAAEf,4BAA4B,CAAC;IAChG;EACJ;EACA;IAAS,IAAI,CAAC7V,IAAI,YAAA+W,8BAAA7W,iBAAA;MAAA,YAAAA,iBAAA,IAA+F8V,qBAAqB,EA10D/Blf,EAAE,CAAAqJ,QAAA,CA00D+CnI,EAAE,CAACC,QAAQ,GA10D5DnB,EAAE,CAAAqJ,QAAA,CA00DuErJ,EAAE,CAACsa,MAAM,GA10DlFta,EAAE,CAAAqJ,QAAA,CA00D6FtJ,QAAQ,GA10DvGC,EAAE,CAAAqJ,QAAA,CA00DkHsV,+BAA+B;IAAA,CAA6D;EAAE;EACzT;IAAS,IAAI,CAACrV,KAAK,kBA30DoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EA20DY0V,qBAAqB;MAAAzV,OAAA,EAArByV,qBAAqB,CAAAhW,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA70D2G3J,EAAE,CAAA4J,iBAAA,CA60DXsV,qBAAqB,EAAc,CAAC;IAC1HrV,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE3I,EAAE,CAACC;EAAS,CAAC,EAAE;IAAE0I,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,EAAE;IAAEzQ,IAAI,EAAEqW,QAAQ;IAAElW,UAAU,EAAE,CAAC;MAC1FH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC6U,+BAA+B;IAC1C,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMwB,4BAA4B,GAAG,IAAI3f,cAAc,CAAC,sBAAsB,EAAE;EAC5EkJ,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAE2W;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO,IAAI;AACf;AACA;AACA,MAAMC,8BAA8B,GAAG,IAAI7f,cAAc,CAAC,gCAAgC,CAAC;AAE3F,IAAI8f,SAAS,GAAG,CAAC;AACjB,MAAMC,aAAa,CAAC;EAChBhb,WAAWA,CAACib,YAAY,EAAEjJ,OAAO,EAAE/R,SAAS,EAAEib,eAAe,EAAE;IAC3D,IAAI,CAAClJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkJ,eAAe,GAAGA,eAAe;IACtC;IACA;IACA;IACA,IAAI,CAACjb,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkb,YAAY,GAAGF,YAAY,IAAI,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACjE;EACAC,QAAQA,CAAC5a,OAAO,EAAE,GAAG8D,IAAI,EAAE;IACvB,MAAM+W,cAAc,GAAG,IAAI,CAACJ,eAAe;IAC3C,IAAIK,UAAU;IACd,IAAIC,QAAQ;IACZ,IAAIjX,IAAI,CAACjF,MAAM,KAAK,CAAC,IAAI,OAAOiF,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClDiX,QAAQ,GAAGjX,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD,CAACgX,UAAU,EAAEC,QAAQ,CAAC,GAAGjX,IAAI;IACjC;IACA,IAAI,CAACpC,KAAK,CAAC,CAAC;IACZsZ,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACH,UAAU,EAAE;MACbA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAU,GAAGD,cAAc,CAACC,UAAU,GAAG,QAAQ;IAC1F;IACA,IAAIC,QAAQ,IAAI,IAAI,IAAIF,cAAc,EAAE;MACpCE,QAAQ,GAAGF,cAAc,CAACE,QAAQ;IACtC;IACA;IACA,IAAI,CAACL,YAAY,CAAClc,YAAY,CAAC,WAAW,EAAEsc,UAAU,CAAC;IACvD,IAAI,IAAI,CAACJ,YAAY,CAACzc,EAAE,EAAE;MACtB,IAAI,CAACid,wBAAwB,CAAC,IAAI,CAACR,YAAY,CAACzc,EAAE,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACsT,OAAO,CAACY,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACgJ,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAIzI,OAAO,CAACC,OAAO,IAAK,IAAI,CAACyI,eAAe,GAAGzI,OAAQ,CAAC;MACnF;MACAqI,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGhH,UAAU,CAAC,MAAM;QACrC,IAAI,CAACyG,YAAY,CAAC9Y,WAAW,GAAG5B,OAAO;QACvC,IAAI,OAAO+a,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAI,CAACE,gBAAgB,GAAGhH,UAAU,CAAC,MAAM,IAAI,CAACvS,KAAK,CAAC,CAAC,EAAEqZ,QAAQ,CAAC;QACpE;QACA;QACA;QACA,IAAI,CAACK,eAAe,GAAG,CAAC;QACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGrX,SAAS;MAC3D,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,IAAI,CAACoX,eAAe;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIzZ,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACgZ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC9Y,WAAW,GAAG,EAAE;IACtC;EACJ;EACAP,WAAWA,CAAA,EAAG;IACV2Z,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACP,YAAY,EAAEtZ,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACsZ,YAAY,GAAG,IAAI;IACxB,IAAI,CAACU,eAAe,GAAG,CAAC;IACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGrX,SAAS;EAC3D;EACA4W,kBAAkBA,CAAA,EAAG;IACjB,MAAMU,YAAY,GAAG,4BAA4B;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAAC9b,SAAS,CAAC+b,sBAAsB,CAACF,YAAY,CAAC;IAC5E,MAAMG,MAAM,GAAG,IAAI,CAAChc,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD;IACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Z,gBAAgB,CAACzc,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC9C8Z,gBAAgB,CAAC9Z,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACAoa,MAAM,CAACnZ,SAAS,CAACC,GAAG,CAAC+Y,YAAY,CAAC;IAClCG,MAAM,CAACnZ,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CkZ,MAAM,CAAChd,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1Cgd,MAAM,CAAChd,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC1Cgd,MAAM,CAACvd,EAAE,GAAG,sBAAsBqc,SAAS,EAAE,EAAE;IAC/C,IAAI,CAAC9a,SAAS,CAACgD,IAAI,CAACV,WAAW,CAAC0Z,MAAM,CAAC;IACvC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIN,wBAAwBA,CAACjd,EAAE,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwd,MAAM,GAAG,IAAI,CAACjc,SAAS,CAAC+B,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGia,MAAM,CAAC5c,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACpC,MAAMka,KAAK,GAAGD,MAAM,CAACja,CAAC,CAAC;MACvB,MAAMma,QAAQ,GAAGD,KAAK,CAAC1c,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAAC2c,QAAQ,EAAE;QACXD,KAAK,CAACld,YAAY,CAAC,WAAW,EAAEP,EAAE,CAAC;MACvC,CAAC,MACI,IAAI0d,QAAQ,CAAChZ,OAAO,CAAC1E,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCyd,KAAK,CAACld,YAAY,CAAC,WAAW,EAAEmd,QAAQ,GAAG,GAAG,GAAG1d,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;IAAS,IAAI,CAACiF,IAAI,YAAA0Y,sBAAAxY,iBAAA;MAAA,YAAAA,iBAAA,IAA+FmX,aAAa,EAx9DvBvgB,EAAE,CAAAqJ,QAAA,CAw9DuC8W,4BAA4B,MAx9DrEngB,EAAE,CAAAqJ,QAAA,CAw9DgGrJ,EAAE,CAACsa,MAAM,GAx9D3Gta,EAAE,CAAAqJ,QAAA,CAw9DsHtJ,QAAQ,GAx9DhIC,EAAE,CAAAqJ,QAAA,CAw9D2IgX,8BAA8B;IAAA,CAA6D;EAAE;EACjV;IAAS,IAAI,CAAC/W,KAAK,kBAz9DoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EAy9DY+W,aAAa;MAAA9W,OAAA,EAAb8W,aAAa,CAAArX,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA39D2G3J,EAAE,CAAA4J,iBAAA,CA29DX2W,aAAa,EAAc,CAAC;IAClH1W,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAACqW,4BAA4B;IACvC,CAAC;EAAE,CAAC,EAAE;IAAEtW,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,EAAE;IAAEzQ,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAACuW,8BAA8B;IACzC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,MAAMwB,WAAW,CAAC;EACd;EACA,IAAIf,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACgB,WAAW;EAC3B;EACA,IAAIhB,UAAUA,CAAC7J,KAAK,EAAE;IAClB,IAAI,CAAC6K,WAAW,GAAG7K,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,QAAQ;IAC9E,IAAI,IAAI,CAAC6K,WAAW,KAAK,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACvT,WAAW,CAAC,CAAC;QAChC,IAAI,CAACuT,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MAC1B,IAAI,CAACA,aAAa,GAAG,IAAI,CAACxK,OAAO,CAACY,iBAAiB,CAAC,MAAM;QACtD,OAAO,IAAI,CAAC6J,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAACxH,WAAW,CAAC,CAACpO,SAAS,CAAC,MAAM;UACnE;UACA,MAAM6V,WAAW,GAAG,IAAI,CAACzH,WAAW,CAACI,aAAa,CAACjT,WAAW;UAC9D;UACA;UACA,IAAIsa,WAAW,KAAK,IAAI,CAACC,sBAAsB,EAAE;YAC7C,IAAI,CAACC,cAAc,CAACxB,QAAQ,CAACsB,WAAW,EAAE,IAAI,CAACJ,WAAW,EAAE,IAAI,CAACf,QAAQ,CAAC;YAC1E,IAAI,CAACoB,sBAAsB,GAAGD,WAAW;UAC7C;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA3c,WAAWA,CAACkV,WAAW,EAAE2H,cAAc,EAAEJ,gBAAgB,EAAEzK,OAAO,EAAE;IAChE,IAAI,CAACkD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC2H,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACzK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuK,WAAW,GAAG,QAAQ;EAC/B;EACAza,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC0a,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACvT,WAAW,CAAC,CAAC;IACpC;EACJ;EACA;IAAS,IAAI,CAACtF,IAAI,YAAAmZ,oBAAAjZ,iBAAA;MAAA,YAAAA,iBAAA,IAA+FyY,WAAW,EAxhErB7hB,EAAE,CAAAsb,iBAAA,CAwhEqCtb,EAAE,CAACub,UAAU,GAxhEpDvb,EAAE,CAAAsb,iBAAA,CAwhE+DiF,aAAa,GAxhE9EvgB,EAAE,CAAAsb,iBAAA,CAwhEyF7X,IAAI,CAAC6e,eAAe,GAxhE/GtiB,EAAE,CAAAsb,iBAAA,CAwhE0Htb,EAAE,CAACsa,MAAM;IAAA,CAA4C;EAAE;EAC1R;IAAS,IAAI,CAACkB,IAAI,kBAzhEqFxb,EAAE,CAAAyb,iBAAA;MAAA5R,IAAA,EAyhEJgY,WAAW;MAAAnG,SAAA;MAAAC,MAAA;QAAAmF,UAAA;QAAAC,QAAA;MAAA;MAAAnF,QAAA;MAAAC,UAAA;IAAA,EAAiM;EAAE;AACvT;AACA;EAAA,QAAAlS,SAAA,oBAAAA,SAAA,KA3hE2G3J,EAAE,CAAA4J,iBAAA,CA2hEXiY,WAAW,EAAc,CAAC;IAChHhY,IAAI,EAAEjJ,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCmS,QAAQ,EAAE,eAAe;MACzBL,QAAQ,EAAE,aAAa;MACvBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhS,IAAI,EAAE7J,EAAE,CAACub;EAAW,CAAC,EAAE;IAAE1R,IAAI,EAAE0W;EAAc,CAAC,EAAE;IAAE1W,IAAI,EAAEpG,IAAI,CAAC6e;EAAgB,CAAC,EAAE;IAAEzY,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,CAAC,EAAkB;IAAEwG,UAAU,EAAE,CAAC;MAC1JjX,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEiX,QAAQ,EAAE,CAAC;MACXlX,IAAI,EAAEhJ,KAAK;MACXiJ,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIyY,yBAAyB;AAC7B,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACnF;AACJ;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACrF,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE;AACA,MAAMC,6BAA6B,GAAG,IAAIhiB,cAAc,CAAC,mCAAmC,CAAC;AAC7F;AACA;AACA;AACA;AACA,MAAMiiB,2BAA2B,GAAGphB,+BAA+B,CAAC;EAChE2d,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMyD,YAAY,CAAC;EACfnd,WAAWA,CAACgS,OAAO,EAAE9R,SAAS,EAAEkd,sBAAsB,EACtD;EACArD,QAAQ,EAAEzM,OAAO,EAAE;IACf,IAAI,CAAC0E,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9R,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkd,sBAAsB,GAAGA,sBAAsB;IACpD;IACA,IAAI,CAACjS,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACkS,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;IACA,IAAI,CAACC,YAAY,GAAG,IAAInd,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACod,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,IAAIrd,GAAG,CAAC,CAAC;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACsd,oBAAoB,GAAG,MAAM;MAC9B;MACA;MACA,IAAI,CAACL,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACM,qBAAqB,GAAGnN,MAAM,CAACkE,UAAU,CAAC,MAAO,IAAI,CAAC2I,cAAc,GAAG,KAAM,CAAC;IACvF,CAAC;IACD;IACA,IAAI,CAACO,0BAA0B,GAAG,IAAI3hB,OAAO,CAAC,CAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAAC4hB,6BAA6B,GAAIzX,KAAK,IAAK;MAC5C,MAAMuR,MAAM,GAAG5b,eAAe,CAACqK,KAAK,CAAC;MACrC;MACA,KAAK,IAAIlD,OAAO,GAAGyU,MAAM,EAAEzU,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAAC4a,aAAa,EAAE;QACjE,IAAI1X,KAAK,CAAC9B,IAAI,KAAK,OAAO,EAAE;UACxB,IAAI,CAACyZ,QAAQ,CAAC3X,KAAK,EAAElD,OAAO,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAAC8a,OAAO,CAAC5X,KAAK,EAAElD,OAAO,CAAC;QAChC;MACJ;IACJ,CAAC;IACD,IAAI,CAACjD,SAAS,GAAG8Z,QAAQ;IACzB,IAAI,CAACkE,cAAc,GAAG3Q,OAAO,EAAE4Q,aAAa,IAAIlB,yBAAyB,CAACmB,SAAS;EACvF;EACAC,OAAOA,CAAClb,OAAO,EAAEmb,aAAa,GAAG,KAAK,EAAE;IACpC,MAAM/I,aAAa,GAAGlX,aAAa,CAAC8E,OAAO,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,IAAIsS,aAAa,CAAC7R,QAAQ,KAAK,CAAC,EAAE;MAC3D;MACA,OAAOrH,EAAE,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA,MAAMkiB,QAAQ,GAAGtiB,cAAc,CAACsZ,aAAa,CAAC,IAAI,IAAI,CAACiJ,YAAY,CAAC,CAAC;IACrE,MAAMC,UAAU,GAAG,IAAI,CAACjB,YAAY,CAAC7b,GAAG,CAAC4T,aAAa,CAAC;IACvD;IACA,IAAIkJ,UAAU,EAAE;MACZ,IAAIH,aAAa,EAAE;QACf;QACA;QACA;QACAG,UAAU,CAACH,aAAa,GAAG,IAAI;MACnC;MACA,OAAOG,UAAU,CAACC,OAAO;IAC7B;IACA;IACA,MAAMC,IAAI,GAAG;MACTL,aAAa,EAAEA,aAAa;MAC5BI,OAAO,EAAE,IAAIxiB,OAAO,CAAC,CAAC;MACtBqiB;IACJ,CAAC;IACD,IAAI,CAACf,YAAY,CAACxc,GAAG,CAACuU,aAAa,EAAEoJ,IAAI,CAAC;IAC1C,IAAI,CAACC,wBAAwB,CAACD,IAAI,CAAC;IACnC,OAAOA,IAAI,CAACD,OAAO;EACvB;EACAG,cAAcA,CAAC1b,OAAO,EAAE;IACpB,MAAMoS,aAAa,GAAGlX,aAAa,CAAC8E,OAAO,CAAC;IAC5C,MAAM2b,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC7b,GAAG,CAAC4T,aAAa,CAAC;IACxD,IAAIuJ,WAAW,EAAE;MACbA,WAAW,CAACJ,OAAO,CAAC1Y,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAAC+Y,WAAW,CAACxJ,aAAa,CAAC;MAC/B,IAAI,CAACiI,YAAY,CAAC/a,MAAM,CAAC8S,aAAa,CAAC;MACvC,IAAI,CAACyJ,sBAAsB,CAACF,WAAW,CAAC;IAC5C;EACJ;EACAG,QAAQA,CAAC9b,OAAO,EAAEmI,MAAM,EAAEiC,OAAO,EAAE;IAC/B,MAAMgI,aAAa,GAAGlX,aAAa,CAAC8E,OAAO,CAAC;IAC5C,MAAM+b,cAAc,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC,CAACxG,aAAa;IACxD;IACA;IACA;IACA,IAAIzC,aAAa,KAAK2J,cAAc,EAAE;MAClC,IAAI,CAACC,uBAAuB,CAAC5J,aAAa,CAAC,CAAC6J,OAAO,CAAC,CAAC,CAACC,cAAc,EAAEV,IAAI,CAAC,KAAK,IAAI,CAACW,cAAc,CAACD,cAAc,EAAE/T,MAAM,EAAEqT,IAAI,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACY,UAAU,CAACjU,MAAM,CAAC;MACvB;MACA,IAAI,OAAOiK,aAAa,CAAChK,KAAK,KAAK,UAAU,EAAE;QAC3CgK,aAAa,CAAChK,KAAK,CAACgC,OAAO,CAAC;MAChC;IACJ;EACJ;EACAxL,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyb,YAAY,CAAC4B,OAAO,CAAC,CAACI,KAAK,EAAErc,OAAO,KAAK,IAAI,CAAC0b,cAAc,CAAC1b,OAAO,CAAC,CAAC;EAC/E;EACA;EACAqb,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACte,SAAS,IAAI8Z,QAAQ;EACrC;EACA;EACAyF,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAAClB,YAAY,CAAC,CAAC;IAC/B,OAAOkB,GAAG,CAAClO,WAAW,IAAIf,MAAM;EACpC;EACAkP,eAAeA,CAACC,gBAAgB,EAAE;IAC9B,IAAI,IAAI,CAACxU,OAAO,EAAE;MACd;MACA;MACA,IAAI,IAAI,CAACmS,2BAA2B,EAAE;QAClC,OAAO,IAAI,CAACsC,0BAA0B,CAACD,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;MAClF,CAAC,MACI;QACD,OAAO,IAAI,CAACxU,OAAO;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACkS,cAAc,IAAI,IAAI,CAACwC,gBAAgB,EAAE;MAC9C,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIF,gBAAgB,IAAI,IAAI,CAACG,gCAAgC,CAACH,gBAAgB,CAAC,EAAE;MAC7E,OAAO,OAAO;IAClB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,0BAA0BA,CAACD,gBAAgB,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAAC1B,cAAc,KAAKjB,yBAAyB,CAAC+C,QAAQ,IAC9D,CAAC,CAACJ,gBAAgB,EAAE9H,QAAQ,CAAC,IAAI,CAACuF,sBAAsB,CAACpD,iBAAiB,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI8E,WAAWA,CAAC5b,OAAO,EAAEmI,MAAM,EAAE;IACzBnI,OAAO,CAACJ,SAAS,CAACkd,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC3U,MAAM,CAAC;IACjDnI,OAAO,CAACJ,SAAS,CAACkd,MAAM,CAAC,mBAAmB,EAAE3U,MAAM,KAAK,OAAO,CAAC;IACjEnI,OAAO,CAACJ,SAAS,CAACkd,MAAM,CAAC,sBAAsB,EAAE3U,MAAM,KAAK,UAAU,CAAC;IACvEnI,OAAO,CAACJ,SAAS,CAACkd,MAAM,CAAC,mBAAmB,EAAE3U,MAAM,KAAK,OAAO,CAAC;IACjEnI,OAAO,CAACJ,SAAS,CAACkd,MAAM,CAAC,qBAAqB,EAAE3U,MAAM,KAAK,SAAS,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiU,UAAUA,CAACjU,MAAM,EAAE4U,iBAAiB,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACjO,OAAO,CAACY,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACzH,OAAO,GAAGE,MAAM;MACrB,IAAI,CAACiS,2BAA2B,GAAGjS,MAAM,KAAK,OAAO,IAAI4U,iBAAiB;MAC1E;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAChC,cAAc,KAAKjB,yBAAyB,CAACmB,SAAS,EAAE;QAC7D1C,YAAY,CAAC,IAAI,CAACyE,gBAAgB,CAAC;QACnC,MAAMC,EAAE,GAAG,IAAI,CAAC7C,2BAA2B,GAAG/D,eAAe,GAAG,CAAC;QACjE,IAAI,CAAC2G,gBAAgB,GAAGxL,UAAU,CAAC,MAAO,IAAI,CAACvJ,OAAO,GAAG,IAAK,EAAEgV,EAAE,CAAC;MACvE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIpC,QAAQA,CAAC3X,KAAK,EAAElD,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM2b,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC7b,GAAG,CAACwB,OAAO,CAAC;IAClD,MAAMyc,gBAAgB,GAAG5jB,eAAe,CAACqK,KAAK,CAAC;IAC/C,IAAI,CAACyY,WAAW,IAAK,CAACA,WAAW,CAACR,aAAa,IAAInb,OAAO,KAAKyc,gBAAiB,EAAE;MAC9E;IACJ;IACA,IAAI,CAACN,cAAc,CAACnc,OAAO,EAAE,IAAI,CAACwc,eAAe,CAACC,gBAAgB,CAAC,EAAEd,WAAW,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;EACIb,OAAOA,CAAC5X,KAAK,EAAElD,OAAO,EAAE;IACpB;IACA;IACA,MAAM2b,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC7b,GAAG,CAACwB,OAAO,CAAC;IAClD,IAAI,CAAC2b,WAAW,IACXA,WAAW,CAACR,aAAa,IACtBjY,KAAK,CAACga,aAAa,YAAYC,IAAI,IACnCnd,OAAO,CAAC2U,QAAQ,CAACzR,KAAK,CAACga,aAAa,CAAE,EAAE;MAC5C;IACJ;IACA,IAAI,CAACtB,WAAW,CAAC5b,OAAO,CAAC;IACzB,IAAI,CAACod,WAAW,CAACzB,WAAW,EAAE,IAAI,CAAC;EACvC;EACAyB,WAAWA,CAAC5B,IAAI,EAAErT,MAAM,EAAE;IACtB,IAAIqT,IAAI,CAACD,OAAO,CAAC8B,SAAS,CAACjhB,MAAM,EAAE;MAC/B,IAAI,CAAC0S,OAAO,CAACwO,GAAG,CAAC,MAAM9B,IAAI,CAACD,OAAO,CAACnY,IAAI,CAAC+E,MAAM,CAAC,CAAC;IACrD;EACJ;EACAsT,wBAAwBA,CAACE,WAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAAC3e,SAAS,CAAC8C,SAAS,EAAE;MAC3B;IACJ;IACA,MAAMsb,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,MAAMmC,sBAAsB,GAAG,IAAI,CAAChD,2BAA2B,CAAC/b,GAAG,CAAC4c,QAAQ,CAAC,IAAI,CAAC;IAClF,IAAI,CAACmC,sBAAsB,EAAE;MACzB,IAAI,CAACzO,OAAO,CAACY,iBAAiB,CAAC,MAAM;QACjC0L,QAAQ,CAACxL,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC+K,6BAA6B,EAAEX,2BAA2B,CAAC;QACnGoB,QAAQ,CAACxL,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC+K,6BAA6B,EAAEX,2BAA2B,CAAC;MACtG,CAAC,CAAC;IACN;IACA,IAAI,CAACO,2BAA2B,CAAC1c,GAAG,CAACud,QAAQ,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,EAAE,IAAI,CAACjD,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAACxL,OAAO,CAACY,iBAAiB,CAAC,MAAM;QACjC,MAAMpC,MAAM,GAAG,IAAI,CAACgP,UAAU,CAAC,CAAC;QAChChP,MAAM,CAACsC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC4K,oBAAoB,CAAC;MAC/D,CAAC,CAAC;MACF;MACA,IAAI,CAACN,sBAAsB,CAAC5C,gBAAgB,CACvC5T,IAAI,CAAC5I,SAAS,CAAC,IAAI,CAAC4f,0BAA0B,CAAC,CAAC,CAChD9W,SAAS,CAAC4Z,QAAQ,IAAI;QACvB,IAAI,CAACpB,UAAU,CAACoB,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACA3B,sBAAsBA,CAACF,WAAW,EAAE;IAChC,MAAMP,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,IAAI,IAAI,CAACb,2BAA2B,CAACvc,GAAG,CAACod,QAAQ,CAAC,EAAE;MAChD,MAAMmC,sBAAsB,GAAG,IAAI,CAAChD,2BAA2B,CAAC/b,GAAG,CAAC4c,QAAQ,CAAC;MAC7E,IAAImC,sBAAsB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAAChD,2BAA2B,CAAC1c,GAAG,CAACud,QAAQ,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;MAC9E,CAAC,MACI;QACDnC,QAAQ,CAAC3L,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACkL,6BAA6B,EAAEX,2BAA2B,CAAC;QACtGoB,QAAQ,CAAC3L,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACkL,6BAA6B,EAAEX,2BAA2B,CAAC;QACrG,IAAI,CAACO,2BAA2B,CAACjb,MAAM,CAAC8b,QAAQ,CAAC;MACrD;IACJ;IACA;IACA,IAAI,CAAC,GAAE,IAAI,CAACd,sBAAsB,EAAE;MAChC,MAAMhN,MAAM,GAAG,IAAI,CAACgP,UAAU,CAAC,CAAC;MAChChP,MAAM,CAACmC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC+K,oBAAoB,CAAC;MAC9D;MACA,IAAI,CAACE,0BAA0B,CAACtX,IAAI,CAAC,CAAC;MACtC;MACAmV,YAAY,CAAC,IAAI,CAACkC,qBAAqB,CAAC;MACxClC,YAAY,CAAC,IAAI,CAACyE,gBAAgB,CAAC;IACvC;EACJ;EACA;EACAb,cAAcA,CAACnc,OAAO,EAAEmI,MAAM,EAAEwT,WAAW,EAAE;IACzC,IAAI,CAACC,WAAW,CAAC5b,OAAO,EAAEmI,MAAM,CAAC;IACjC,IAAI,CAACiV,WAAW,CAACzB,WAAW,EAAExT,MAAM,CAAC;IACrC,IAAI,CAACwU,gBAAgB,GAAGxU,MAAM;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI6T,uBAAuBA,CAAChc,OAAO,EAAE;IAC7B,MAAMyd,OAAO,GAAG,EAAE;IAClB,IAAI,CAACpD,YAAY,CAAC4B,OAAO,CAAC,CAACT,IAAI,EAAEU,cAAc,KAAK;MAChD,IAAIA,cAAc,KAAKlc,OAAO,IAAKwb,IAAI,CAACL,aAAa,IAAIe,cAAc,CAACvH,QAAQ,CAAC3U,OAAO,CAAE,EAAE;QACxFyd,OAAO,CAAC3hB,IAAI,CAAC,CAACogB,cAAc,EAAEV,IAAI,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOiC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIb,gCAAgCA,CAACH,gBAAgB,EAAE;IAC/C,MAAM;MAAE3F,iBAAiB,EAAE4G,gBAAgB;MAAEhH;IAAmB,CAAC,GAAG,IAAI,CAACwD,sBAAsB;IAC/F;IACA;IACA;IACA,IAAIxD,kBAAkB,KAAK,OAAO,IAC9B,CAACgH,gBAAgB,IACjBA,gBAAgB,KAAKjB,gBAAgB,IACpCA,gBAAgB,CAAC9P,QAAQ,KAAK,OAAO,IAAI8P,gBAAgB,CAAC9P,QAAQ,KAAK,UAAW,IACnF8P,gBAAgB,CAAC9X,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMgZ,MAAM,GAAGlB,gBAAgB,CAACkB,MAAM;IACtC,IAAIA,MAAM,EAAE;MACR,KAAK,IAAI5e,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4e,MAAM,CAACvhB,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACpC,IAAI4e,MAAM,CAAC5e,CAAC,CAAC,CAAC4V,QAAQ,CAAC+I,gBAAgB,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAACjd,IAAI,YAAAmd,qBAAAjd,iBAAA;MAAA,YAAAA,iBAAA,IAA+FsZ,YAAY,EA56EtB1iB,EAAE,CAAAqJ,QAAA,CA46EsCrJ,EAAE,CAACsa,MAAM,GA56EjDta,EAAE,CAAAqJ,QAAA,CA46E4DnI,EAAE,CAACC,QAAQ,GA56EzEnB,EAAE,CAAAqJ,QAAA,CA46EoF6V,qBAAqB,GA56E3Glf,EAAE,CAAAqJ,QAAA,CA46EsHtJ,QAAQ,MA56EhIC,EAAE,CAAAqJ,QAAA,CA46E2JmZ,6BAA6B;IAAA,CAA6D;EAAE;EAChW;IAAS,IAAI,CAAClZ,KAAK,kBA76EoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EA66EYkZ,YAAY;MAAAjZ,OAAA,EAAZiZ,YAAY,CAAAxZ,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/6E2G3J,EAAE,CAAA4J,iBAAA,CA+6EX8Y,YAAY,EAAc,CAAC;IACjH7Y,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE7J,EAAE,CAACsa;EAAO,CAAC,EAAE;IAAEzQ,IAAI,EAAE3I,EAAE,CAACC;EAAS,CAAC,EAAE;IAAE0I,IAAI,EAAEqV;EAAsB,CAAC,EAAE;IAAErV,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC5HH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE/I;IACV,CAAC,EAAE;MACC+I,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC0Y,6BAA6B;IACxC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8D,eAAe,CAAC;EAClB/gB,WAAWA,CAACkV,WAAW,EAAE8L,aAAa,EAAE;IACpC,IAAI,CAAC9L,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC8L,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,GAAG,IAAI1lB,YAAY,CAAC,CAAC;EAC5C;EACA,IAAI2lB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAG,eAAeA,CAAA,EAAG;IACd,MAAMle,OAAO,GAAG,IAAI,CAACgS,WAAW,CAACI,aAAa;IAC9C,IAAI,CAAC+L,oBAAoB,GAAG,IAAI,CAACL,aAAa,CACzC5C,OAAO,CAAClb,OAAO,EAAEA,OAAO,CAACO,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAACkM,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAC1FtI,SAAS,CAACuE,MAAM,IAAI;MACrB,IAAI,CAAC4V,YAAY,GAAG5V,MAAM;MAC1B,IAAI,CAAC6V,cAAc,CAACI,IAAI,CAACjW,MAAM,CAAC;IACpC,CAAC,CAAC;EACN;EACAvJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkf,aAAa,CAACpC,cAAc,CAAC,IAAI,CAAC1J,WAAW,CAAC;IACnD,IAAI,IAAI,CAACmM,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACpY,WAAW,CAAC,CAAC;IAC3C;EACJ;EACA;IAAS,IAAI,CAACtF,IAAI,YAAA4d,wBAAA1d,iBAAA;MAAA,YAAAA,iBAAA,IAA+Fkd,eAAe,EA/9EzBtmB,EAAE,CAAAsb,iBAAA,CA+9EyCtb,EAAE,CAACub,UAAU,GA/9ExDvb,EAAE,CAAAsb,iBAAA,CA+9EmEoH,YAAY;IAAA,CAA4C;EAAE;EACtO;IAAS,IAAI,CAAClH,IAAI,kBAh+EqFxb,EAAE,CAAAyb,iBAAA;MAAA5R,IAAA,EAg+EJyc,eAAe;MAAA5K,SAAA;MAAAqL,OAAA;QAAAN,cAAA;MAAA;MAAA7K,QAAA;MAAAC,UAAA;IAAA,EAAmL;EAAE;AAC7S;AACA;EAAA,QAAAlS,SAAA,oBAAAA,SAAA,KAl+E2G3J,EAAE,CAAA4J,iBAAA,CAk+EX0c,eAAe,EAAc,CAAC;IACpHzc,IAAI,EAAEjJ,SAAS;IACfkJ,IAAI,EAAE,CAAC;MACCmS,QAAQ,EAAE,oDAAoD;MAC9DL,QAAQ,EAAE,iBAAiB;MAC3BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhS,IAAI,EAAE7J,EAAE,CAACub;EAAW,CAAC,EAAE;IAAE1R,IAAI,EAAE6Y;EAAa,CAAC,CAAC,EAAkB;IAAE+D,cAAc,EAAE,CAAC;MACxG5c,IAAI,EAAE7I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIgmB,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC3EA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AAC/E,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,mCAAmC,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3B7hB,WAAWA,CAACE,SAAS,EAAE6Z,QAAQ,EAAE;IAC7B,IAAI,CAAC7Z,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,SAAS,GAAG8Z,QAAQ;IACzB,IAAI,CAAC+H,uBAAuB,GAAGpnB,MAAM,CAAC2D,kBAAkB,CAAC,CACpDqe,OAAO,CAAC,yBAAyB,CAAC,CAClC5V,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACib,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,GAAG,KAAK;QACxC,IAAI,CAACC,oCAAoC,CAAC,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAAC/hB,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAOye,gBAAgB,CAACS,IAAI;IAChC;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACliB,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IACvD+f,WAAW,CAACvf,KAAK,CAACwf,eAAe,GAAG,YAAY;IAChDD,WAAW,CAACvf,KAAK,CAACyf,QAAQ,GAAG,UAAU;IACvC,IAAI,CAACpiB,SAAS,CAACgD,IAAI,CAACV,WAAW,CAAC4f,WAAW,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAACriB,SAAS,CAACsR,WAAW,IAAIf,MAAM;IAC3D,MAAM+R,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAAC/S,gBAAgB,GACjE+S,cAAc,CAAC/S,gBAAgB,CAAC4S,WAAW,CAAC,GAC5C,IAAI;IACV,MAAMK,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAe,IAAK,EAAE,EAAEK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAChGN,WAAW,CAACtgB,MAAM,CAAC,CAAC;IACpB,QAAQ2gB,aAAa;MACjB;MACA,KAAK,YAAY;MACjB;MACA,KAAK,eAAe;MACpB,KAAK,eAAe;QAChB,OAAOf,gBAAgB,CAACiB,cAAc;MAC1C;MACA,KAAK,kBAAkB;MACvB;MACA,KAAK,kBAAkB;QACnB,OAAOjB,gBAAgB,CAACkB,cAAc;IAC9C;IACA,OAAOlB,gBAAgB,CAACS,IAAI;EAChC;EACApgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACggB,uBAAuB,CAAC7Y,WAAW,CAAC,CAAC;EAC9C;EACA;EACA+Y,oCAAoCA,CAAA,EAAG;IACnC,IAAI,CAAC,IAAI,CAACD,2BAA2B,IAAI,IAAI,CAAC7hB,SAAS,CAAC8C,SAAS,IAAI,IAAI,CAAC/C,SAAS,CAACgD,IAAI,EAAE;MACtF,MAAM2f,WAAW,GAAG,IAAI,CAAC3iB,SAAS,CAACgD,IAAI,CAACH,SAAS;MACjD8f,WAAW,CAAC/gB,MAAM,CAAC+f,mCAAmC,EAAEF,wBAAwB,EAAEC,wBAAwB,CAAC;MAC3G,IAAI,CAACI,2BAA2B,GAAG,IAAI;MACvC,MAAMc,IAAI,GAAG,IAAI,CAACZ,mBAAmB,CAAC,CAAC;MACvC,IAAIY,IAAI,KAAKpB,gBAAgB,CAACkB,cAAc,EAAE;QAC1CC,WAAW,CAAC7f,GAAG,CAAC6e,mCAAmC,EAAEF,wBAAwB,CAAC;MAClF,CAAC,MACI,IAAImB,IAAI,KAAKpB,gBAAgB,CAACiB,cAAc,EAAE;QAC/CE,WAAW,CAAC7f,GAAG,CAAC6e,mCAAmC,EAAED,wBAAwB,CAAC;MAClF;IACJ;EACJ;EACA;IAAS,IAAI,CAAChe,IAAI,YAAAmf,iCAAAjf,iBAAA;MAAA,YAAAA,iBAAA,IAA+Fge,wBAAwB,EAzkFlCpnB,EAAE,CAAAqJ,QAAA,CAykFkDnI,EAAE,CAACC,QAAQ,GAzkF/DnB,EAAE,CAAAqJ,QAAA,CAykF0EtJ,QAAQ;IAAA,CAA6C;EAAE;EAC1O;IAAS,IAAI,CAACuJ,KAAK,kBA1kFoFtJ,EAAE,CAAAuJ,kBAAA;MAAAC,KAAA,EA0kFY4d,wBAAwB;MAAA3d,OAAA,EAAxB2d,wBAAwB,CAAAle,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1K;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5kF2G3J,EAAE,CAAA4J,iBAAA,CA4kFXwd,wBAAwB,EAAc,CAAC;IAC7Hvd,IAAI,EAAE1J,UAAU;IAChB2J,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAE3I,EAAE,CAACC;EAAS,CAAC,EAAE;IAAE0I,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtEH,IAAI,EAAEzJ,MAAM;MACZ0J,IAAI,EAAE,CAAC/J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMuoB,UAAU,CAAC;EACb/iB,WAAWA,CAACgjB,wBAAwB,EAAE;IAClCA,wBAAwB,CAAChB,oCAAoC,CAAC,CAAC;EACnE;EACA;IAAS,IAAI,CAACre,IAAI,YAAAsf,mBAAApf,iBAAA;MAAA,YAAAA,iBAAA,IAA+Fkf,UAAU,EAxlFpBtoB,EAAE,CAAAqJ,QAAA,CAwlFoC+d,wBAAwB;IAAA,CAA2C;EAAE;EAClN;IAAS,IAAI,CAACqB,IAAI,kBAzlFqFzoB,EAAE,CAAA0oB,gBAAA;MAAA7e,IAAA,EAylFSye;IAAU,EAAkI;EAAE;EAChQ;IAAS,IAAI,CAACK,IAAI,kBA1lFqF3oB,EAAE,CAAA4oB,gBAAA;MAAAC,OAAA,GA0lF+BnlB,eAAe;IAAA,EAAI;EAAE;AACjK;AACA;EAAA,QAAAiG,SAAA,oBAAAA,SAAA,KA5lF2G3J,EAAE,CAAA4J,iBAAA,CA4lFX0e,UAAU,EAAc,CAAC;IAC/Gze,IAAI,EAAE5I,QAAQ;IACd6I,IAAI,EAAE,CAAC;MACC+e,OAAO,EAAE,CAACnlB,eAAe,EAAEme,WAAW,EAAEtH,YAAY,EAAE+L,eAAe,CAAC;MACtEwC,OAAO,EAAE,CAACjH,WAAW,EAAEtH,YAAY,EAAE+L,eAAe;IACxD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzc,IAAI,EAAEud;EAAyB,CAAC,CAAC;AAAA;;AAEtE;AACA;AACA;;AAEA,SAASkB,UAAU,EAAEjY,0BAA0B,EAAE/K,aAAa,EAAEF,8BAA8B,EAAED,yBAAyB,EAAE0c,WAAW,EAAEyE,eAAe,EAAE/L,YAAY,EAAE6B,qBAAqB,EAAE0B,4BAA4B,EAAEhB,mCAAmC,EAAE0F,6BAA6B,EAAEjF,yBAAyB,EAAE/M,eAAe,EAAEkS,YAAY,EAAEH,yBAAyB,EAAExL,SAAS,EAAEmD,gBAAgB,EAAE8M,gBAAgB,EAAEI,wBAAwB,EAAExI,uCAAuC,EAAED,+BAA+B,EAAEO,qBAAqB,EAAExK,oBAAoB,EAAEF,iBAAiB,EAAE6L,8BAA8B,EAAEF,4BAA4B,EAAEC,oCAAoC,EAAE7T,cAAc,EAAEgU,aAAa,EAAErb,qBAAqB,EAAEoP,6BAA6B,EAAEC,sCAAsC,EAAEH,kBAAkB,EAAEJ,gBAAgB,EAAED,wBAAwB,EAAEE,iCAAiC,EAAEnD,cAAc,EAAEhN,mBAAmB,EAAEK,mBAAmB,EAAE8Z,+BAA+B,EAAEG,gCAAgC,EAAE1Z,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}