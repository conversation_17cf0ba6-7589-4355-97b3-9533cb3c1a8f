{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = a0 => ({\n  \"opacity-50 cursor-not-allowed\": a0\n});\nfunction ModifyHouseholdComponent_nb_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r1.label, \" \");\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const house_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r3.CHouseHold, $event) || (house_r3.CHouseHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵelementStart(3, \"span\", 40);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_3_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const house_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.clearForm(house_r3));\n    });\n    i0.ɵɵtext(4, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 41);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      const house_r3 = ctx_r4.$implicit;\n      const idx_r6 = ctx_r4.index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onUpdateAllCol(house_r3, idx_r6));\n    });\n    i0.ɵɵtext(6, \" \\u6279\\u6B21\\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const house_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r3.CHouseHold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, house_r3.CHouseHold === \"\"));\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"span\", 43);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const house_r3 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(house_r3.isEdit = !house_r3.isEdit);\n    });\n    i0.ɵɵtext(2, \"\\u6279\\u6B21\\u4FEE\\u6539\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_3_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtemplate(1, ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template, 7, 4, \"div\", 36)(2, ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template, 3, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", house_r3.isEdit === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", house_r3.isEdit !== true);\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33)(1, \"th\")(2, \"span\", 34);\n    i0.ɵɵtext(3, \"\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, ModifyHouseholdComponent_div_37_tr_3_th_4_Template, 3, 2, \"th\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseListItem);\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\")(1, \"p\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r8[0].CFloor);\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(house_r9.CHouseHold || \"null\");\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const house_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r9.CHouseHold, $event) || (house_r9.CHouseHold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵelementStart(3, \"span\", 49);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const house_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.closeEditItem(house_r9));\n    });\n    i0.ɵɵtext(4, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 50);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const house_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onUpdateItem(house_r9));\n    });\n    i0.ɵɵtext(6, \" \\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r9.CHouseHold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, house_r9.CHouseHold === \"\"));\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"span\", 51);\n    i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const house_r9 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onEditItem(house_r9));\n    });\n    i0.ɵɵtext(2, \"\\u4FEE\\u6539\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 46);\n    i0.ɵɵtemplate(1, ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template, 2, 1, \"p\", 47)(2, ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template, 7, 4, \"div\", 36)(3, ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template, 3, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngClass\", !house_r9.CIsEnable ? \"bg-slate-400\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", house_r9.CIsEnable && house_r9.isEdit === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", house_r9.CIsEnable && house_r9.isEdit !== true);\n  }\n}\nfunction ModifyHouseholdComponent_div_37_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33);\n    i0.ɵɵtemplate(1, ModifyHouseholdComponent_div_37_tr_5_td_1_Template, 3, 1, \"td\", 44)(2, ModifyHouseholdComponent_div_37_tr_5_td_2_Template, 4, 4, \"td\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", row_r8.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", row_r8);\n  }\n}\nfunction ModifyHouseholdComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"table\", 30)(2, \"thead\");\n    i0.ɵɵtemplate(3, ModifyHouseholdComponent_div_37_tr_3_Template, 5, 1, \"tr\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ModifyHouseholdComponent_div_37_tr_5_Template, 3, 2, \"tr\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseList.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseList);\n  }\n}\nexport class ModifyHouseholdComponent extends BaseComponent {\n  constructor(_allow, dialogService, _houseService, route, location, message, router) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.location = location;\n    this.message = message;\n    this.router = router;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.isHouseList = false;\n  }\n  getListBuilding() {\n    this._houseService.apiHouseGetListBuildingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.buildingSelectedOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n      }\n    });\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    // Get all unique floor numbers (handling potential nulls)\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    // Create an empty array for each unique floor\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    // Place each customer in the correct floor array\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor); // Find the index of the customer's floor in the uniqueFloors array\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push(customer);\n      } // Add customer to the corresponding array in groupedData\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    this.isHouseList = false;\n    if (this.buildCaseId) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.buildCaseId,\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\n          CFloor: {\n            CFrom: this.searchQuery.CFrom,\n            CTo: this.searchQuery.CTo\n          },\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseList = this.groupByFloor(rest);\n          this.houseListItem = this.houseList[0].map(item => {\n            return {\n              CHouseHold: \"\",\n              isEdit: false\n            };\n          });\n          this.houseList.forEach(element => {\n            if (element.CIsEnable) {\n              element['isEdit'] = false;\n            }\n          });\n          this.isHouseList = true;\n        }\n      });\n    }\n  }\n  goBack() {\n    this.location.back();\n  }\n  onSubmit() {\n    let bodyParam = this.houseList.flat().map(item => {\n      return {\n        CIsEnable: item.CIsEnable,\n        CHouseID: item.CID,\n        CHouseHold: item.CHouseHold\n      };\n    });\n    this._houseService.apiHouseEditListHousePost$Json({\n      body: {\n        mode: 2,\n        Args: bodyParam.filter(e => e.CIsEnable)\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseList();\n      }\n    });\n  }\n  onEditItem(house) {\n    house.isEdit = !house.isEdit;\n    this.currentCHouseHold = house.CHouseHold;\n  }\n  closeEditItem(house) {\n    house.isEdit = !house.isEdit;\n    if (this.currentCHouseHold) {\n      house.CHouseHold = this.currentCHouseHold;\n    }\n  }\n  onUpdateItem(item) {\n    this._houseService.apiHouseEditListHousePost$Json({\n      body: {\n        mode: 2,\n        Args: [{\n          CIsEnable: item.CIsEnable,\n          CHouseID: item.CID,\n          CHouseHold: item.CHouseHold\n        }]\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseList();\n      }\n    });\n  }\n  clearForm(house) {\n    house.isEdit = !house.isEdit;\n    house.CHouseHold = '';\n  }\n  onUpdateAllCol(item, index) {\n    if (item.CHouseHold === '') return;\n    let param = [];\n    this.houseList.forEach(element => {\n      if (element[index].CIsEnable) {\n        param.push({\n          CHouseHold: item.CHouseHold,\n          CHouseID: element[index].CID,\n          CIsEnable: element[index].CIsEnable\n        });\n      }\n    });\n    this._houseService.apiHouseEditListHousePost$Json({\n      body: {\n        mode: 2,\n        Args: param\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseList();\n      }\n    });\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\n      CFrom: 1,\n      CTo: 100\n    };\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        this.getListBuilding();\n        this.getHouseList();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  onOpen(ref) {\n    this.dialogService.open(ref);\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  clear() {\n    this.searchQuery = {\n      CFrom: 1,\n      CTo: 100,\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\n    };\n  }\n  onNavigateWithId(type) {\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId]);\n  }\n  static {\n    this.ɵfac = function ModifyHouseholdComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ModifyHouseholdComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.HouseService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Location), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyHouseholdComponent,\n      selectors: [[\"ngx-modify-household\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 47,\n      vars: 5,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-5\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"number\", \"id\", \"search\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-3\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"mx-2\", 3, \"click\"], [\"class\", \"table-responsive mt-4\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"inline\"], [1, \"d-flex\", \"justify-content-center\", \"w-full\"], [3, \"value\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-bordered\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\"], [1, \"block\", \"w-8\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"font-bold float-left w-32\", 4, \"ngIf\"], [\"class\", \"font-bold w-max\", 4, \"ngIf\"], [1, \"font-bold\", \"float-left\", \"w-32\"], [\"type\", \"text\", \"id\", \"CHouseHold\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"font-normal\", \"text-blue-400\", \"underline\", \"inline-block\", \"w-[40%]\", \"hover:underline\", \"cursor-pointer\", 3, \"click\"], [1, \"font-normal\", \"text-blue-400\", \"underline\", \"inline-block\", \"w-[60%]\", \"hover:underline\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"font-bold\", \"w-max\"], [1, \"font-normal\", \"text-blue-400\", \"hover:underline\", \"underline\", \"cursor-pointer\", \"block\", \"min-w-3\", 3, \"click\"], [4, \"ngIf\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"class\", \"font-bold\", 4, \"ngIf\"], [1, \"font-bold\"], [1, \"font-normal\", \"text-blue-400\", \"underline\", \"inline-block\", \"w-[50%]\", \"hover:underline\", \"cursor-pointer\", 3, \"click\"], [1, \"font-normal\", \"text-blue-400\", \"underline\", \"inline-block\", \"w-[50%]\", \"hover:underline\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"font-normal\", \"text-blue-400\", \"hover:underline\", \"underline\", \"cursor-pointer\", 3, \"click\"]],\n      template: function ModifyHouseholdComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\");\n          i0.ɵɵelement(4, \"h1\", 1);\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n          i0.ɵɵtext(9, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseholdComponent_Template_nb_select_ngModelChange_10_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(11, ModifyHouseholdComponent_nb_option_11_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"label\", 10);\n          i0.ɵɵtext(15, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"nb-form-field\", 11)(17, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseholdComponent_Template_input_ngModelChange_17_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"label\", 13);\n          i0.ɵɵtext(19, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"nb-form-field\", 14)(21, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ModifyHouseholdComponent_Template_input_ngModelChange_21_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_24_listener() {\n            return ctx.clear();\n          });\n          i0.ɵɵtext(25, \" \\u6E05\\u9664 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_26_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(27, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(28, \"i\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 21)(30, \"div\", 17)(31, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_31_listener() {\n            return ctx.onNavigateWithId(\"modify-floor-plan\");\n          });\n          i0.ɵɵtext(32, \" 1.\\u8ABF\\u6574\\u6236\\u578B\\u7D44\\u6210 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_33_listener() {\n            return ctx.onNavigateWithId(\"modify-household\");\n          });\n          i0.ɵɵtext(34, \" 2.\\u4FEE\\u6539\\u6236\\u578B\\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_35_listener() {\n            return ctx.onNavigateWithId(\"modify-house-type\");\n          });\n          i0.ɵɵtext(36, \" 3.\\u8A2D\\u5B9A\\u5730\\u4E3B\\u6236 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(37, ModifyHouseholdComponent_div_37_Template, 6, 2, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"nb-card-footer\", 25)(39, \"div\", 26)(40, \"div\", 27)(41, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_41_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(42, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_43_listener() {\n            return ctx.getHouseList();\n          });\n          i0.ɵɵtext(44, \" \\u53D6\\u6D88 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function ModifyHouseholdComponent_Template_button_click_45_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.isHouseList);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i8.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtb2RpZnktaG91c2Vob2xkLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvbW9kaWZ5LWhvdXNlaG9sZC9tb2RpZnktaG91c2Vob2xkLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "building_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵtwoWayListener", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_input_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "house_r3", "ɵɵnextContext", "$implicit", "ɵɵtwoWayBindingSet", "CHouseHold", "ɵɵresetView", "ɵɵelement", "ɵɵlistener", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_3_listener", "ctx_r3", "clearForm", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template_span_click_5_listener", "ctx_r4", "idx_r6", "index", "onUpdateAllCol", "ɵɵtwoWayProperty", "ɵɵpureFunction1", "_c0", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template_span_click_1_listener", "_r7", "isEdit", "ɵɵtemplate", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_1_Template", "ModifyHouseholdComponent_div_37_tr_3_th_4_div_2_Template", "ModifyHouseholdComponent_div_37_tr_3_th_4_Template", "houseListItem", "ɵɵtextInterpolate", "row_r8", "CFloor", "house_r9", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_input_ngModelChange_1_listener", "_r10", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_3_listener", "closeEditItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template_span_click_5_listener", "onUpdateItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template_span_click_1_listener", "_r11", "onEditItem", "ModifyHouseholdComponent_div_37_tr_5_td_2_p_1_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_2_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_div_3_Template", "CIsEnable", "ModifyHouseholdComponent_div_37_tr_5_td_1_Template", "ModifyHouseholdComponent_div_37_tr_5_td_2_Template", "length", "ModifyHouseholdComponent_div_37_tr_3_Template", "ModifyHouseholdComponent_div_37_tr_5_Template", "houseList", "ModifyHouseholdComponent", "constructor", "_allow", "dialogService", "_houseService", "route", "location", "message", "router", "buildingSelectedOptions", "value", "isHouseList", "getListBuilding", "apiHouseGetListBuildingPost$Json", "body", "CBuildCaseID", "buildCaseId", "subscribe", "res", "Entries", "StatusCode", "map", "e", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "filter", "floor", "push", "floorIndex", "indexOf", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildingName", "searchQuery", "CBuildingNameSelected", "CFrom", "CTo", "CIsPagi", "rest", "item", "for<PERSON>ach", "element", "goBack", "back", "onSubmit", "bodyParam", "flat", "CHouseID", "CID", "apiHouseEditListHousePost$Json", "mode", "<PERSON><PERSON><PERSON>", "showSucessMSG", "house", "currentCHouseHold", "param", "ngOnInit", "paramMap", "params", "idParam", "get", "id", "pageChanged", "newPage", "pageIndex", "onOpen", "ref", "open", "onClose", "close", "clear", "onNavigateWithId", "type", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "HouseService", "i4", "ActivatedRoute", "i5", "Location", "i6", "MessageService", "Router", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ModifyHouseholdComponent_Template", "rf", "ctx", "ModifyHouseholdComponent_Template_nb_select_ngModelChange_10_listener", "ModifyHouseholdComponent_nb_option_11_Template", "ModifyHouseholdComponent_Template_input_ngModelChange_17_listener", "ModifyHouseholdComponent_Template_input_ngModelChange_21_listener", "ModifyHouseholdComponent_Template_button_click_24_listener", "ModifyHouseholdComponent_Template_button_click_26_listener", "ModifyHouseholdComponent_Template_button_click_31_listener", "ModifyHouseholdComponent_Template_button_click_33_listener", "ModifyHouseholdComponent_Template_button_click_35_listener", "ModifyHouseholdComponent_div_37_Template", "ModifyHouseholdComponent_Template_button_click_41_listener", "ModifyHouseholdComponent_Template_button_click_43_listener", "ModifyHouseholdComponent_Template_button_click_45_listener"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-household\\modify-household.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\modify-household\\modify-household.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { HouseService } from 'src/services/api/services';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { GetHouseListRes } from 'src/services/api/models';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\ninterface HouseListWithEdit extends GetHouseListRes {\r\n  isEdit?: boolean | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-modify-household',\r\n  templateUrl: './modify-household.component.html',\r\n  styleUrls: ['./modify-household.component.scss'],\r\n})\r\n\r\nexport class ModifyHouseholdComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _houseService: HouseService,\r\n    private route: ActivatedRoute,\r\n    private location: Location,\r\n    private message: MessageService,\r\n    private router: Router\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  buildCaseId: number\r\n  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }]\r\n\r\n  getListBuilding() {\r\n    this._houseService.apiHouseGetListBuildingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.buildingSelectedOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  houseList: HouseListWithEdit[][]\r\n\r\n  houseListItem: any\r\n\r\n\r\n  groupByFloor(customerData: GetHouseListRes[]): GetHouseListRes[][] {\r\n    const groupedData: GetHouseListRes[][] = [];\r\n    // Get all unique floor numbers (handling potential nulls)\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    // Create an empty array for each unique floor\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    // Place each customer in the correct floor array\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number); // Find the index of the customer's floor in the uniqueFloors array\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push(customer);\r\n      } // Add customer to the corresponding array in groupedData\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    this.isHouseList = false\r\n    if (this.buildCaseId) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.buildCaseId,\r\n          CBuildingName: this.searchQuery.CBuildingNameSelected.value || null,\r\n          CFloor: {\r\n            CFrom: this.searchQuery.CFrom,\r\n            CTo: this.searchQuery.CTo,\r\n          },\r\n          CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseList = this.groupByFloor(rest)\r\n          this.houseListItem = this.houseList[0].map((item: GetHouseListRes) => {\r\n            return {\r\n              CHouseHold: \"\",\r\n              isEdit: false\r\n            }\r\n          })\r\n          this.houseList.forEach((element: any) => {\r\n            if (element.CIsEnable) {\r\n              element['isEdit'] = false\r\n            }\r\n          });\r\n          this.isHouseList = true\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  goBack() { this.location.back() }\r\n\r\n  onSubmit() {\r\n    let bodyParam = this.houseList.flat().map((item: any) => {\r\n      return {\r\n        CIsEnable: item.CIsEnable,\r\n        CHouseID: item.CID,\r\n        CHouseHold: item.CHouseHold\r\n      };\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: bodyParam.filter((e: any) => e.CIsEnable)\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n  currentCHouseHold: any\r\n\r\n  onEditItem(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    this.currentCHouseHold = house.CHouseHold\r\n  }\r\n\r\n  closeEditItem(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    if (this.currentCHouseHold) {\r\n      house.CHouseHold = this.currentCHouseHold\r\n    }\r\n  }\r\n\r\n  onUpdateItem(item: any) {\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: [{\r\n          CIsEnable: item.CIsEnable,\r\n          CHouseID: item.CID,\r\n          CHouseHold: item.CHouseHold\r\n        }]\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  clearForm(house: any) {\r\n    house.isEdit = !house.isEdit\r\n    house.CHouseHold = ''\r\n  }\r\n\r\n  onUpdateAllCol(item: any, index: number) {\r\n    if (item.CHouseHold === '') return\r\n    let param: any[] = []\r\n    this.houseList.forEach((element: any) => {\r\n      if (element[index].CIsEnable) {\r\n        param.push({\r\n          CHouseHold: item.CHouseHold,\r\n          CHouseID: element[index].CID,\r\n          CIsEnable: element[index].CIsEnable\r\n        })\r\n      }\r\n    });\r\n\r\n    this._houseService.apiHouseEditListHousePost$Json({\r\n      body: {\r\n        mode: 2,\r\n        Args: param\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseList()\r\n      }\r\n    })\r\n  }\r\n\r\n  searchQuery: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n      CFrom: 1,\r\n      CTo: 100\r\n    }\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        this.getListBuilding()\r\n        this.getHouseList()\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n\r\n  onOpen(ref: any) {\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  clear() {\r\n    this.searchQuery = {\r\n      CFrom: 1,\r\n      CTo: 100,\r\n      CBuildingNameSelected: this.buildingSelectedOptions[0]\r\n    }\r\n  }\r\n\r\n  onNavigateWithId(type: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, this.buildCaseId])\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\"></h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-4\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">棟別</label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"w-full\">\r\n            <nb-option *ngFor=\"let building of buildingSelectedOptions\" [value]=\"building\">\r\n              {{ building.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n\r\n      </div>\r\n      <div class=\"col-md-5\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"number\" id=\"search\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-3\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-secondary mx-2\" (click)=\"clear()\">\r\n            清除\r\n          </button>\r\n          <button class=\"btn btn-secondary\" (click)=\"getHouseList()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-floor-plan')\">\r\n            1.調整戶型組成\r\n          </button>\r\n          <button class=\"btn btn-primary mx-2\" (click)=\"onNavigateWithId('modify-household')\">\r\n            2.修改戶型名稱\r\n          </button>\r\n          <button class=\"btn btn-primary\" (click)=\"onNavigateWithId('modify-house-type')\">\r\n            3.設定地主戶\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\" *ngIf=\"isHouseList\">\r\n      <table class=\"table table-bordered\" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr *ngIf=\"houseList.length\" class=\"text-center\">\r\n            <th><span class=\"block w-8\">樓層</span></th>\r\n            <th *ngFor=\"let house of houseListItem; let idx = index;\">\r\n              <div *ngIf=\"house.isEdit === true\" class=\"font-bold float-left w-32\">\r\n                <input type=\"text\" id=\"CHouseHold\" nbInput class=\"w-full\" [(ngModel)]=\"house.CHouseHold\"><br>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[40%] hover:underline cursor-pointer\"\r\n                  (click)=\"clearForm(house)\">\r\n                  取消\r\n                </span>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[60%] hover:underline cursor-pointer\"\r\n                  [ngClass]=\"{'opacity-50 cursor-not-allowed': house.CHouseHold === ''}\"\r\n                  (click)=\"onUpdateAllCol(house, idx)\">\r\n                  批次儲存\r\n                </span>\r\n              </div>\r\n              <div *ngIf=\"house.isEdit !== true\" class=\"font-bold w-max\">\r\n                <span class=\"font-normal text-blue-400 hover:underline underline cursor-pointer block min-w-3\"\r\n                  (click)=\"house.isEdit = !house.isEdit\">批次修改名稱 </span>\r\n              </div>\r\n            </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let row of houseList\" class=\"text-center\">\r\n            <td *ngIf=\"row.length\">\r\n              <p>{{row[0].CFloor}}</p>\r\n            </td>\r\n            <td *ngFor=\"let house of row\" [ngClass]=\"!house.CIsEnable ? 'bg-slate-400' : ''\">\r\n              <p *ngIf=\"!house.isEdit\" class=\"font-bold\">{{ house.CHouseHold || 'null' }}</p>\r\n              <div *ngIf=\"house.CIsEnable && house.isEdit === true\" class=\"font-bold float-left w-32\">\r\n                <input type=\"text\" id=\"CHouseHold\" nbInput class=\"w-full\" [(ngModel)]=\"house.CHouseHold\"><br>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[50%] hover:underline cursor-pointer\"\r\n                  (click)=\"closeEditItem(house)\">\r\n                  取消\r\n                </span>\r\n                <span class=\"font-normal text-blue-400 underline inline-block w-[50%] hover:underline cursor-pointer\"\r\n                  [ngClass]=\"{'opacity-50 cursor-not-allowed': house.CHouseHold === ''}\" (click)=\"onUpdateItem(house)\">\r\n                  儲存\r\n                </span>\r\n              </div>\r\n              <div *ngIf=\"house.CIsEnable  && house.isEdit !== true\" class=\"font-bold\">\r\n                <span class=\"font-normal text-blue-400 hover:underline underline cursor-pointer\"\r\n                  (click)=\"onEditItem(house)\">修改名稱 </span>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <div class=\"inline\">\r\n      <div class=\"d-flex justify-content-center w-full\">\r\n        <button class=\"btn btn-primary\" (click)=\"goBack()\">\r\n          返回上一頁\r\n        </button>\r\n        <button class=\"btn btn-primary mx-2\" (click)=\"getHouseList()\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-primary\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AAIA,SAASA,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;;ICOvDC,EAAA,CAAAC,cAAA,oBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFgDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAC5EL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,WAAA,CAAAG,KAAA,MACF;;;;;;IAkDIR,EADF,CAAAC,cAAA,cAAqE,gBACsB;IAA/BD,EAAA,CAAAS,gBAAA,2BAAAC,wFAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAH,QAAA,CAAAI,UAAA,EAAAP,MAAA,MAAAG,QAAA,CAAAI,UAAA,GAAAP,MAAA;MAAA,OAAAX,EAAA,CAAAmB,WAAA,CAAAR,MAAA;IAAA,EAA8B;IAAxFX,EAAA,CAAAG,YAAA,EAAyF;IAAAH,EAAA,CAAAoB,SAAA,SAAI;IAC7FpB,EAAA,CAAAC,cAAA,eAC6B;IAA3BD,EAAA,CAAAqB,UAAA,mBAAAC,+EAAA;MAAAtB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,QAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAmB,WAAA,CAASI,MAAA,CAAAC,SAAA,CAAAV,QAAA,CAAgB;IAAA,EAAC;IAC1Bd,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAEuC;IAArCD,EAAA,CAAAqB,UAAA,mBAAAI,+EAAA;MAAAzB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAA1B,EAAA,CAAAe,aAAA;MAAA,MAAAD,QAAA,GAAAY,MAAA,CAAAV,SAAA;MAAA,MAAAW,MAAA,GAAAD,MAAA,CAAAE,KAAA;MAAA,MAAAL,MAAA,GAAAvB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAmB,WAAA,CAASI,MAAA,CAAAM,cAAA,CAAAf,QAAA,EAAAa,MAAA,CAA0B;IAAA,EAAC;IACpC3B,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAVsDH,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAA8B,gBAAA,YAAAhB,QAAA,CAAAI,UAAA,CAA8B;IAMtFlB,EAAA,CAAAM,SAAA,GAAsE;IAAtEN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAAlB,QAAA,CAAAI,UAAA,SAAsE;;;;;;IAMxElB,EADF,CAAAC,cAAA,cAA2D,eAEhB;IAAvCD,EAAA,CAAAqB,UAAA,mBAAAY,+EAAA;MAAAjC,EAAA,CAAAY,aAAA,CAAAsB,GAAA;MAAA,MAAApB,QAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAAAL,QAAA,CAAAqB,MAAA,IAAArB,QAAA,CAAAqB,MAAA;IAAA,EAAsC;IAACnC,EAAA,CAAAE,MAAA,4CAAO;IAClDF,EADkD,CAAAG,YAAA,EAAO,EACnD;;;;;IAhBRH,EAAA,CAAAC,cAAA,SAA0D;IAaxDD,EAZA,CAAAoC,UAAA,IAAAC,wDAAA,kBAAqE,IAAAC,wDAAA,kBAYV;IAI7DtC,EAAA,CAAAG,YAAA,EAAK;;;;IAhBGH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAAU,QAAA,CAAAqB,MAAA,UAA2B;IAY3BnC,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAAU,QAAA,CAAAqB,MAAA,UAA2B;;;;;IAd/BnC,EADN,CAAAC,cAAA,aAAiD,SAC3C,eAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAK;IAC1CH,EAAA,CAAAoC,UAAA,IAAAG,kDAAA,iBAA0D;IAkB5DvC,EAAA,CAAAG,YAAA,EAAK;;;;IAlBmBH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAiB,aAAA,CAAkB;;;;;IAuBtCxC,EADF,CAAAC,cAAA,SAAuB,QAClB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACtBF,EADsB,CAAAG,YAAA,EAAI,EACrB;;;;IADAH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAyC,iBAAA,CAAAC,MAAA,IAAAC,MAAA,CAAiB;;;;;IAGpB3C,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAApCH,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAyC,iBAAA,CAAAG,QAAA,CAAA1B,UAAA,WAAgC;;;;;;IAEzElB,EADF,CAAAC,cAAA,cAAwF,gBACG;IAA/BD,EAAA,CAAAS,gBAAA,2BAAAoC,wFAAAlC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAkC,IAAA;MAAA,MAAAF,QAAA,GAAA5C,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAA2B,QAAA,CAAA1B,UAAA,EAAAP,MAAA,MAAAiC,QAAA,CAAA1B,UAAA,GAAAP,MAAA;MAAA,OAAAX,EAAA,CAAAmB,WAAA,CAAAR,MAAA;IAAA,EAA8B;IAAxFX,EAAA,CAAAG,YAAA,EAAyF;IAAAH,EAAA,CAAAoB,SAAA,SAAI;IAC7FpB,EAAA,CAAAC,cAAA,eACiC;IAA/BD,EAAA,CAAAqB,UAAA,mBAAA0B,+EAAA;MAAA/C,EAAA,CAAAY,aAAA,CAAAkC,IAAA;MAAA,MAAAF,QAAA,GAAA5C,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAmB,WAAA,CAASI,MAAA,CAAAyB,aAAA,CAAAJ,QAAA,CAAoB;IAAA,EAAC;IAC9B5C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eACuG;IAA9BD,EAAA,CAAAqB,UAAA,mBAAA4B,+EAAA;MAAAjD,EAAA,CAAAY,aAAA,CAAAkC,IAAA;MAAA,MAAAF,QAAA,GAAA5C,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAmB,WAAA,CAASI,MAAA,CAAA2B,YAAA,CAAAN,QAAA,CAAmB;IAAA,EAAC;IACpG5C,EAAA,CAAAE,MAAA,qBACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IATsDH,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAA8B,gBAAA,YAAAc,QAAA,CAAA1B,UAAA,CAA8B;IAMtFlB,EAAA,CAAAM,SAAA,GAAsE;IAAtEN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAAY,QAAA,CAAA1B,UAAA,SAAsE;;;;;;IAKxElB,EADF,CAAAC,cAAA,cAAyE,eAEzC;IAA5BD,EAAA,CAAAqB,UAAA,mBAAA8B,+EAAA;MAAAnD,EAAA,CAAAY,aAAA,CAAAwC,IAAA;MAAA,MAAAR,QAAA,GAAA5C,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAmB,WAAA,CAASI,MAAA,CAAA8B,UAAA,CAAAT,QAAA,CAAiB;IAAA,EAAC;IAAC5C,EAAA,CAAAE,MAAA,gCAAK;IACrCF,EADqC,CAAAG,YAAA,EAAO,EACtC;;;;;IAhBRH,EAAA,CAAAC,cAAA,aAAiF;IAa/ED,EAZA,CAAAoC,UAAA,IAAAkB,sDAAA,gBAA2C,IAAAC,wDAAA,kBAC6C,IAAAC,wDAAA,kBAWf;IAI3ExD,EAAA,CAAAG,YAAA,EAAK;;;;IAjByBH,EAAA,CAAAI,UAAA,aAAAwC,QAAA,CAAAa,SAAA,uBAAkD;IAC1EzD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAI,UAAA,UAAAwC,QAAA,CAAAT,MAAA,CAAmB;IACjBnC,EAAA,CAAAM,SAAA,EAA8C;IAA9CN,EAAA,CAAAI,UAAA,SAAAwC,QAAA,CAAAa,SAAA,IAAAb,QAAA,CAAAT,MAAA,UAA8C;IAW9CnC,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAI,UAAA,SAAAwC,QAAA,CAAAa,SAAA,IAAAb,QAAA,CAAAT,MAAA,UAA+C;;;;;IAjBzDnC,EAAA,CAAAC,cAAA,aAAsD;IAIpDD,EAHA,CAAAoC,UAAA,IAAAsB,kDAAA,iBAAuB,IAAAC,kDAAA,iBAG0D;IAkBnF3D,EAAA,CAAAG,YAAA,EAAK;;;;IArBEH,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAI,UAAA,SAAAsC,MAAA,CAAAkB,MAAA,CAAgB;IAGC5D,EAAA,CAAAM,SAAA,EAAM;IAANN,EAAA,CAAAI,UAAA,YAAAsC,MAAA,CAAM;;;;;IA5BhC1C,EAFJ,CAAAC,cAAA,cAAuD,gBACmC,YAC/E;IACLD,EAAA,CAAAoC,UAAA,IAAAyB,6CAAA,iBAAiD;IAqBnD7D,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAAoC,UAAA,IAAA0B,6CAAA,iBAAsD;IAyB5D9D,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAhDKH,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAmB,MAAA,CAAAwC,SAAA,CAAAH,MAAA,CAAsB;IAuBP5D,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAI,UAAA,YAAAmB,MAAA,CAAAwC,SAAA,CAAY;;;ADvD1C,OAAM,MAAOC,wBAAyB,SAAQjE,aAAa;EACzDkE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,aAA2B,EAC3BC,KAAqB,EACrBC,QAAkB,EAClBC,OAAuB,EACvBC,MAAc;IAEtB,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IAMhB,KAAAC,uBAAuB,GAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAElE,KAAK,EAAE;IAAI,CAAE,CAAC;IA4C7D,KAAAmE,WAAW,GAAG,KAAK;EA/CnB;EAKAC,eAAeA,CAAA;IACb,IAAI,CAACR,aAAa,CAACS,gCAAgC,CAAC;MAClDC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACX,uBAAuB,GAAG,CAAC;UAC9BC,KAAK,EAAE,EAAE;UAAElE,KAAK,EAAE;SACnB,EAAE,GAAG0E,GAAG,CAACC,OAAO,CAACE,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAEZ,KAAK,EAAEY,CAAC;YAAE9E,KAAK,EAAE8E;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ;EAQAC,YAAYA,CAACC,YAA+B;IAC1C,MAAMC,WAAW,GAAwB,EAAE;IAC3C;IACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACH,GAAG,CAACS,QAAQ,IAAIA,QAAQ,CAACnD,MAAM,CAAC,CAACoD,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF;IACA,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACQ,IAAI,CAAC,EAAE,CAAC;IACtB;IACA;IACA,KAAK,MAAMH,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGR,YAAY,CAACS,OAAO,CAACL,QAAQ,CAACnD,MAAgB,CAAC,CAAC,CAAC;MACpE,IAAIuD,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBT,WAAW,CAACS,UAAU,CAAC,CAACD,IAAI,CAACH,QAAQ,CAAC;MACxC,CAAC,CAAC;IACJ;IACA,OAAOL,WAAW;EACpB;EAIAW,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC7D,MAAM,IAAI,CAAC,KAAK4D,CAAC,CAAC5D,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA8D,YAAYA,CAAA;IACV,IAAI,CAAC9B,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAACK,WAAW,EAAE;MACpB,IAAI,CAACZ,aAAa,CAACsC,6BAA6B,CAAC;QAC/C5B,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI,CAACC,WAAW;UAC9B2B,aAAa,EAAE,IAAI,CAACC,WAAW,CAACC,qBAAqB,CAACnC,KAAK,IAAI,IAAI;UACnE/B,MAAM,EAAE;YACNmE,KAAK,EAAE,IAAI,CAACF,WAAW,CAACE,KAAK;YAC7BC,GAAG,EAAE,IAAI,CAACH,WAAW,CAACG;WACvB;UACDC,OAAO,EAAE;;OAEZ,CAAC,CAAC/B,SAAS,CAACC,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACtC,MAAM6B,IAAI,GAAG,IAAI,CAACb,qBAAqB,CAAClB,GAAG,CAACC,OAAO,CAAC;UACpD,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACwB,YAAY,CAAC0B,IAAI,CAAC;UACxC,IAAI,CAACzE,aAAa,GAAG,IAAI,CAACuB,SAAS,CAAC,CAAC,CAAC,CAACsB,GAAG,CAAE6B,IAAqB,IAAI;YACnE,OAAO;cACLhG,UAAU,EAAE,EAAE;cACdiB,MAAM,EAAE;aACT;UACH,CAAC,CAAC;UACF,IAAI,CAAC4B,SAAS,CAACoD,OAAO,CAAEC,OAAY,IAAI;YACtC,IAAIA,OAAO,CAAC3D,SAAS,EAAE;cACrB2D,OAAO,CAAC,QAAQ,CAAC,GAAG,KAAK;YAC3B;UACF,CAAC,CAAC;UACF,IAAI,CAACzC,WAAW,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;IACJ;EACF;EAEA0C,MAAMA,CAAA;IAAK,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,EAAE;EAAC;EAEhCC,QAAQA,CAAA;IACN,IAAIC,SAAS,GAAG,IAAI,CAACzD,SAAS,CAAC0D,IAAI,EAAE,CAACpC,GAAG,CAAE6B,IAAS,IAAI;MACtD,OAAO;QACLzD,SAAS,EAAEyD,IAAI,CAACzD,SAAS;QACzBiE,QAAQ,EAAER,IAAI,CAACS,GAAG;QAClBzG,UAAU,EAAEgG,IAAI,CAAChG;OAClB;IACH,CAAC,CAAC;IAEF,IAAI,CAACkD,aAAa,CAACwD,8BAA8B,CAAC;MAChD9C,IAAI,EAAE;QACJ+C,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEN,SAAS,CAACzB,MAAM,CAAET,CAAM,IAAKA,CAAC,CAAC7B,SAAS;;KAEjD,CAAC,CAACwB,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACb,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtB,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGApD,UAAUA,CAAC2E,KAAU;IACnBA,KAAK,CAAC7F,MAAM,GAAG,CAAC6F,KAAK,CAAC7F,MAAM;IAC5B,IAAI,CAAC8F,iBAAiB,GAAGD,KAAK,CAAC9G,UAAU;EAC3C;EAEA8B,aAAaA,CAACgF,KAAU;IACtBA,KAAK,CAAC7F,MAAM,GAAG,CAAC6F,KAAK,CAAC7F,MAAM;IAC5B,IAAI,IAAI,CAAC8F,iBAAiB,EAAE;MAC1BD,KAAK,CAAC9G,UAAU,GAAG,IAAI,CAAC+G,iBAAiB;IAC3C;EACF;EAEA/E,YAAYA,CAACgE,IAAS;IACpB,IAAI,CAAC9C,aAAa,CAACwD,8BAA8B,CAAC;MAChD9C,IAAI,EAAE;QACJ+C,IAAI,EAAE,CAAC;QACPC,IAAI,EAAE,CAAC;UACLrE,SAAS,EAAEyD,IAAI,CAACzD,SAAS;UACzBiE,QAAQ,EAAER,IAAI,CAACS,GAAG;UAClBzG,UAAU,EAAEgG,IAAI,CAAChG;SAClB;;KAEJ,CAAC,CAAC+D,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACb,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtB,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAEAjF,SAASA,CAACwG,KAAU;IAClBA,KAAK,CAAC7F,MAAM,GAAG,CAAC6F,KAAK,CAAC7F,MAAM;IAC5B6F,KAAK,CAAC9G,UAAU,GAAG,EAAE;EACvB;EAEAW,cAAcA,CAACqF,IAAS,EAAEtF,KAAa;IACrC,IAAIsF,IAAI,CAAChG,UAAU,KAAK,EAAE,EAAE;IAC5B,IAAIgH,KAAK,GAAU,EAAE;IACrB,IAAI,CAACnE,SAAS,CAACoD,OAAO,CAAEC,OAAY,IAAI;MACtC,IAAIA,OAAO,CAACxF,KAAK,CAAC,CAAC6B,SAAS,EAAE;QAC5ByE,KAAK,CAACjC,IAAI,CAAC;UACT/E,UAAU,EAAEgG,IAAI,CAAChG,UAAU;UAC3BwG,QAAQ,EAAEN,OAAO,CAACxF,KAAK,CAAC,CAAC+F,GAAG;UAC5BlE,SAAS,EAAE2D,OAAO,CAACxF,KAAK,CAAC,CAAC6B;SAC3B,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACW,aAAa,CAACwD,8BAA8B,CAAC;MAChD9C,IAAI,EAAE;QACJ+C,IAAI,EAAE,CAAC;QACPC,IAAI,EAAEI;;KAET,CAAC,CAACjD,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACb,OAAO,CAACwD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACtB,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAIS0B,QAAQA,CAAA;IACf,IAAI,CAACvB,WAAW,GAAG;MACjBC,qBAAqB,EAAE,IAAI,CAACpC,uBAAuB,CAAC,CAAC,CAAC;MACtDqC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE;KACN;IACD,IAAI,CAAC1C,KAAK,CAAC+D,QAAQ,CAACnD,SAAS,CAACoD,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACtD,WAAW,GAAGwD,EAAE;QACrB,IAAI,CAAC5D,eAAe,EAAE;QACtB,IAAI,CAAC6B,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EAEJ;EAEAgC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACC,SAAS,GAAGD,OAAO;EAC1B;EAGAE,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC1E,aAAa,CAAC2E,IAAI,CAACD,GAAG,CAAC;EAC9B;EAEAE,OAAOA,CAACF,GAAQ;IACdA,GAAG,CAACG,KAAK,EAAE;EACb;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACrC,WAAW,GAAG;MACjBE,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,GAAG;MACRF,qBAAqB,EAAE,IAAI,CAACpC,uBAAuB,CAAC,CAAC;KACtD;EACH;EAEAyE,gBAAgBA,CAACC,IAAS;IACxB,IAAI,CAAC3E,MAAM,CAAC4E,QAAQ,CAAC,CAAC,+BAA+BD,IAAI,EAAE,EAAE,IAAI,CAACnE,WAAW,CAAC,CAAC;EACjF;;;uCAnOWhB,wBAAwB,EAAAhE,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAC,QAAA,GAAA/J,EAAA,CAAAqJ,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAM,MAAA;IAAA;EAAA;;;YAAxBlG,wBAAwB;MAAAmG,SAAA;MAAAC,QAAA,GAAApK,EAAA,CAAAqK,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BnC3K,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,mBAAc;UACZD,EAAA,CAAAoB,SAAA,YAA0C;UAIpCpB,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBAA2F;UAA/DD,EAAA,CAAAS,gBAAA,2BAAAoK,sEAAAlK,MAAA;YAAAX,EAAA,CAAAiB,kBAAA,CAAA2J,GAAA,CAAAhE,WAAA,CAAAC,qBAAA,EAAAlG,MAAA,MAAAiK,GAAA,CAAAhE,WAAA,CAAAC,qBAAA,GAAAlG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UACzEX,EAAA,CAAAoC,UAAA,KAAA0I,8CAAA,uBAA+E;UAMrF9K,EAHI,CAAAG,YAAA,EAAY,EACR,EAEF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACoE;UAAhCD,EAAA,CAAAS,gBAAA,2BAAAsK,kEAAApK,MAAA;YAAAX,EAAA,CAAAiB,kBAAA,CAAA2J,GAAA,CAAAhE,WAAA,CAAAE,KAAA,EAAAnG,MAAA,MAAAiK,GAAA,CAAAhE,WAAA,CAAAE,KAAA,GAAAnG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+B;UAC/FX,EADE,CAAAG,YAAA,EAA8F,EAChF;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBAC4D;UAA9BD,EAAA,CAAAS,gBAAA,2BAAAuK,kEAAArK,MAAA;YAAAX,EAAA,CAAAiB,kBAAA,CAAA2J,GAAA,CAAAhE,WAAA,CAAAG,GAAA,EAAApG,MAAA,MAAAiK,GAAA,CAAAhE,WAAA,CAAAG,GAAA,GAAApG,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6B;UAG3FX,EAHM,CAAAG,YAAA,EAAsF,EACxE,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC2B,kBACY;UAAlBD,EAAA,CAAAqB,UAAA,mBAAA4J,2DAAA;YAAA,OAASL,GAAA,CAAA3B,KAAA,EAAO;UAAA,EAAC;UACtDjJ,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA2D;UAAzBD,EAAA,CAAAqB,UAAA,mBAAA6J,2DAAA;YAAA,OAASN,GAAA,CAAAnE,YAAA,EAAc;UAAA,EAAC;UACxDzG,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAoB,SAAA,aAA6B;UAGtCpB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuB,eAC0B,kBACmC;UAAhDD,EAAA,CAAAqB,UAAA,mBAAA8J,2DAAA;YAAA,OAASP,GAAA,CAAA1B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7ElJ,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoF;UAA/CD,EAAA,CAAAqB,UAAA,mBAAA+J,2DAAA;YAAA,OAASR,GAAA,CAAA1B,gBAAA,CAAiB,kBAAkB,CAAC;UAAA,EAAC;UACjFlJ,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgF;UAAhDD,EAAA,CAAAqB,UAAA,mBAAAgK,2DAAA;YAAA,OAAST,GAAA,CAAA1B,gBAAA,CAAiB,mBAAmB,CAAC;UAAA,EAAC;UAC7ElJ,EAAA,CAAAE,MAAA,0CACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UACNH,EAAA,CAAAoC,UAAA,KAAAkJ,wCAAA,kBAAuD;UAoDzDtL,EAAA,CAAAG,YAAA,EAAe;UAITH,EAHN,CAAAC,cAAA,0BAAsD,eAChC,eACgC,kBACG;UAAnBD,EAAA,CAAAqB,UAAA,mBAAAkK,2DAAA;YAAA,OAASX,GAAA,CAAAvD,MAAA,EAAQ;UAAA,EAAC;UAChDrH,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA8D;UAAzBD,EAAA,CAAAqB,UAAA,mBAAAmK,2DAAA;YAAA,OAASZ,GAAA,CAAAnE,YAAA,EAAc;UAAA,EAAC;UAC3DzG,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAqD;UAArBD,EAAA,CAAAqB,UAAA,mBAAAoK,2DAAA;YAAA,OAASb,GAAA,CAAArD,QAAA,EAAU;UAAA,EAAC;UAClDvH,EAAA,CAAAE,MAAA,sBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACS,EACT;;;UAlH4BH,EAAA,CAAAM,SAAA,IAA+C;UAA/CN,EAAA,CAAA8B,gBAAA,YAAA8I,GAAA,CAAAhE,WAAA,CAAAC,qBAAA,CAA+C;UACzC7G,EAAA,CAAAM,SAAA,EAA0B;UAA1BN,EAAA,CAAAI,UAAA,YAAAwK,GAAA,CAAAnG,uBAAA,CAA0B;UAYIzE,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA8B,gBAAA,YAAA8I,GAAA,CAAAhE,WAAA,CAAAE,KAAA,CAA+B;UAKrC9G,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAA8B,gBAAA,YAAA8I,GAAA,CAAAhE,WAAA,CAAAG,GAAA,CAA6B;UA4BzD/G,EAAA,CAAAM,SAAA,IAAiB;UAAjBN,EAAA,CAAAI,UAAA,SAAAwK,GAAA,CAAAjG,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}