{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nlet HouseholdManagementComponent = class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.quotationStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n      quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    // 報價單相關\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    // 新增：百分比費用設定\n    this.additionalFeeName = '營業稅'; // 預設名稱\n    this.additionalFeePercentage = 5; // 預設5%\n    this.additionalFeeAmount = 0; // 百分比費用金額\n    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n    this.enableAdditionalFee = false; // 是否啟用百分比費用\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.isQuotationEditable = true; // 報價單是否可編輯\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    if (this.searchQuery.CQuotationStatusSelected.value) {\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      _this.isQuotationEditable = true; // 預設可編輯\n      // 重置百分比費用設定\n      _this.additionalFeeName = '營業稅';\n      _this.additionalFeePercentage = 5;\n      _this.additionalFeeAmount = 0;\n      _this.finalTotalAmount = 0;\n      _this.enableAdditionalFee = false;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationID || 0;\n          // 根據 cQuotationStatus 決定是否可編輯\n          if (response.Entries.CQuotationStatus === 2) {\n            // 2: 已報價\n            _this.isQuotationEditable = false;\n          } else {\n            _this.isQuotationEditable = true;\n          }\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n              cQuotationStatus: entry.CQuotationStatus\n            }));\n            _this.calculateTotal();\n          } else {}\n        } else {}\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 產生新報價單\n  createNewQuotation() {\n    this.currentQuotationId = 0;\n    this.quotationItems = [];\n    this.isQuotationEditable = true;\n    this.totalAmount = 0;\n    this.finalTotalAmount = 0;\n    this.additionalFeeAmount = 0;\n    this.enableAdditionalFee = false;\n    // 顯示成功訊息\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      CQuotationItemType: CQuotationItemType.自定義\n    });\n  }\n  // 載入客變需求\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.客變需求 // 客變需求\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入客變需求成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n        }\n      } catch (error) {\n        console.error('載入客變需求錯誤:', error);\n        _this2.message.showErrorMSG('載入客變需求失敗');\n      }\n    })();\n  }\n  // 載入選樣資料\n  loadRegularItems() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.currentHouse?.CID) {\n          _this3.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this3.currentHouse.CBuildCaseID || 0,\n          CHouseID: _this3.currentHouse.CID\n        };\n        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n        if (response?.success && response.data) {\n          const regularItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this3.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.選樣 // 選樣資料\n          }));\n          _this3.quotationItems.push(...regularItems);\n          _this3.calculateTotal();\n          _this3.message.showSucessMSG('載入選樣資料成功');\n        } else {\n          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n        }\n      } catch (error) {\n        console.error('載入選樣資料錯誤:', error);\n        _this3.message.showErrorMSG('載入選樣資料失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n    this.calculateFinalTotal();\n  }\n  // 計算百分比費用和最終總金額\n  calculateFinalTotal() {\n    if (this.enableAdditionalFee) {\n      this.additionalFeeAmount = Math.round(this.totalAmount * (this.additionalFeePercentage / 100));\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n    } else {\n      this.additionalFeeAmount = 0;\n      this.finalTotalAmount = this.totalAmount;\n    }\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 更新百分比費用\n  updateAdditionalFee() {\n    this.calculateFinalTotal();\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.quotationItems.length === 0) {\n        _this4.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位 (調整：允許單價和數量為負數)\n      const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this4.currentHouse.CID,\n          items: _this4.quotationItems,\n          quotationId: _this4.currentQuotationId // 傳遞當前的報價單ID\n        };\n        const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this4.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this4.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this5.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  // 列印報價單\n  printQuotation() {\n    if (this.quotationItems.length === 0) {\n      this.message.showErrorMSG('沒有可列印的報價項目');\n      return;\n    }\n    try {\n      // 建立列印內容\n      const printContent = this.generatePrintContent();\n      // 建立新的視窗進行列印\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n      if (printWindow) {\n        printWindow.document.open();\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        // 等待內容載入完成後列印\n        printWindow.onload = function () {\n          setTimeout(() => {\n            printWindow.print();\n            // 列印後不自動關閉視窗，讓使用者可以預覽\n          }, 500);\n        };\n      } else {\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n      }\n    } catch (error) {\n      console.error('列印報價單錯誤:', error);\n      this.message.showErrorMSG('列印報價單時發生錯誤');\n    }\n  }\n  // 產生列印內容\n  generatePrintContent() {\n    const currentDate = new Date().toLocaleDateString('zh-TW');\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n    let itemsHtml = '';\n    this.quotationItems.forEach((item, index) => {\n      const subtotal = item.cUnitPrice * item.cCount;\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n      itemsHtml += `\n        <tr>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${index + 1}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px;\">${item.cItemName}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(item.cUnitPrice)}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${item.cCount}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(subtotal)}</td>\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${quotationType}</td>\n        </tr>\n      `;\n    });\n    return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>報價單列印</title>\n        <style>\n          body {\n            font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;\n            margin: 20px;\n            font-size: 14px;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 30px;\n          }\n          .header h1 {\n            margin: 0;\n            font-size: 24px;\n            color: #333;\n          }\n          .info-section {\n            margin-bottom: 20px;\n            border-bottom: 1px solid #ddd;\n            padding-bottom: 15px;\n          }\n          .info-row {\n            display: flex;\n            margin-bottom: 8px;\n          }\n          .info-label {\n            font-weight: bold;\n            width: 100px;\n            flex-shrink: 0;\n          }\n          .info-value {\n            flex: 1;\n          }\n          table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n          }\n          th {\n            background-color: #27ae60;\n            color: white;\n            border: 1px solid #ddd;\n            padding: 10px 8px;\n            text-align: center;\n            font-weight: bold;\n          }\n          td {\n            border: 1px solid #ddd;\n            padding: 8px;\n          }\n          .total-section {\n            text-align: right;\n            margin-top: 20px;\n            padding-top: 15px;\n            border-top: 2px solid #27ae60;\n          }\n          .subtotal {\n            font-size: 14px;\n            margin-bottom: 5px;\n            color: #666;\n          }\n          .additional-fee {\n            font-size: 14px;\n            margin-bottom: 10px;\n            color: #666;\n          }\n          .total-amount {\n            font-size: 18px;\n            font-weight: bold;\n            color: #27ae60;\n            border-top: 1px solid #ddd;\n            padding-top: 10px;\n          }\n          .footer {\n            margin-top: 40px;\n            text-align: center;\n            font-size: 12px;\n            color: #666;\n          }\n          .signature-section {\n            margin-top: 40px;\n            page-break-inside: avoid;\n          }\n          .signature-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 30px;\n          }\n          .signature-box {\n            width: 45%;\n            text-align: center;\n          }\n          .signature-label {\n            font-weight: bold;\n            margin-bottom: 40px;\n            font-size: 16px;\n          }\n          .signature-line {\n            border-bottom: 2px solid #000;\n            height: 60px;\n            margin-bottom: 10px;\n            position: relative;\n          }\n          .signature-date {\n            font-size: 14px;\n            margin-top: 15px;\n          }\n          .signature-notes {\n            margin-top: 30px;\n            padding: 15px;\n            background-color: #f9f9f9;\n            border-left: 4px solid #27ae60;\n          }\n          .signature-notes p {\n            margin: 0 0 10px 0;\n            font-weight: bold;\n          }\n          .signature-notes ul {\n            margin: 0;\n            padding-left: 20px;\n          }\n          .signature-notes li {\n            margin-bottom: 5px;\n            line-height: 1.4;\n          }\n          @media print {\n            body { margin: 0; }\n            .header { page-break-inside: avoid; }\n            .signature-section { page-break-inside: avoid; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>報價單</h1>\n        </div>\n\n        <div class=\"info-section\">\n          <div class=\"info-row\">\n            <span class=\"info-label\">建案名稱：</span>\n            <span class=\"info-value\">${buildCaseName}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">戶別：</span>\n            <span class=\"info-value\">${this.currentHouse?.CHouseHold || ''}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">樓層：</span>\n            <span class=\"info-value\">${this.currentHouse?.CFloor || ''}樓</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">客戶姓名：</span>\n            <span class=\"info-value\">${this.currentHouse?.CCustomerName || ''}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"info-label\">列印日期：</span>\n            <span class=\"info-value\">${currentDate}</span>\n          </div>\n        </div>\n\n        <table>\n          <thead>\n            <tr>\n              <th width=\"8%\">序號</th>\n              <th width=\"35%\">項目名稱</th>\n              <th width=\"15%\">單價 (元)</th>\n              <th width=\"10%\">數量</th>\n              <th width=\"20%\">小計 (元)</th>\n              <th width=\"12%\">類型</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${itemsHtml}\n          </tbody>\n        </table>\n\n        <div class=\"total-section\">\n          <div class=\"subtotal\">\n            小計：${this.formatCurrency(this.totalAmount)}\n          </div>\n          ${this.enableAdditionalFee ? `\n          <div class=\"additional-fee\">\n            ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n          </div>\n          ` : ''}\n          <div class=\"total-amount\">\n            總金額：${this.formatCurrency(this.finalTotalAmount)}\n          </div>\n        </div>\n\n        <div class=\"signature-section\">\n          <div class=\"signature-row\">\n            <div class=\"signature-box\">\n              <div class=\"signature-label\">客戶簽名：</div>\n              <div class=\"signature-line\"></div>\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\n            </div>\n            <div class=\"signature-box\">\n              <div class=\"signature-label\">業務簽名：</div>\n              <div class=\"signature-line\"></div>\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\n            </div>\n          </div>\n          <div class=\"signature-notes\">\n            <p><strong>注意事項：</strong></p>\n            <ul>\n              <li>此報價單有效期限為30天，逾期需重新報價</li>\n              <li>報價內容若有異動，請重新確認</li>\n              <li>簽名確認後即視為同意此報價內容</li>\n            </ul>\n          </div>\n        </div>\n\n        <div class=\"footer\">\n          此報價單由系統自動產生，列印時間：${new Date().toLocaleString('zh-TW')}\n        </div>\n      </body>\n      </html>\n    `;\n  }\n  // 鎖定報價單\n  lockQuotation(ref) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.quotationItems.length === 0) {\n        _this6.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      if (!_this6.currentQuotationId) {\n        _this6.message.showErrorMSG('無效的報價單ID');\n        return;\n      }\n      try {\n        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n        if (response.success) {\n          _this6.message.showSucessMSG('報價單已成功鎖定');\n          console.log('報價單鎖定成功:', {\n            quotationId: _this6.currentQuotationId,\n            message: response.message\n          });\n        } else {\n          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n          console.error('報價單鎖定失敗:', response.message);\n        }\n        ref.close();\n      } catch (error) {\n        _this6.message.showErrorMSG('報價單鎖定失敗');\n        console.error('鎖定報價單錯誤:', error);\n      }\n    })();\n  }\n  // 取得報價類型文字\n  getQuotationTypeText(quotationType) {\n    switch (quotationType) {\n      case CQuotationItemType.客變需求:\n        return '客變需求';\n      case CQuotationItemType.自定義:\n        return '自定義';\n      case CQuotationItemType.選樣:\n        return '選樣';\n      default:\n        return '未知';\n    }\n  }\n  getQuotationStatusText(status) {\n    switch (status) {\n      case EnumQuotationStatus.待報價:\n        return '待報價';\n      case EnumQuotationStatus.已報價:\n        return '已報價';\n      case EnumQuotationStatus.已簽回:\n        return '已簽回';\n      default:\n        return '未知';\n    }\n  }\n};\n__decorate([ViewChild('fileInput')], HouseholdManagementComponent.prototype, \"fileInput\", void 0);\nHouseholdManagementComponent = __decorate([Component({\n  selector: 'ngx-household-management',\n  templateUrl: './household-management.component.html',\n  styleUrls: ['./household-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule]\n})], HouseholdManagementComponent);\nexport { HouseholdManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "SharedModule", "CommonModule", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "CQuotationItemType", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "label", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "progressOptions", "houseTypeOptions", "payStatusOptions", "signStatusOptions", "quotationStatusOptions", "options", "getEnumOptions", "initDetail", "CHouseID", "CMail", "CIsChange", "CPayStatus", "CIsEnable", "CCustomerName", "CNationalID", "CProgress", "CHouseType", "CHouseHold", "CPhone", "quotationItems", "totalAmount", "additionalFeeName", "additionalFeePercentage", "additionalFeeAmount", "finalTotalAmount", "enableAdditionalFee", "currentHouse", "currentQuotationId", "isQuotationEditable", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "searchQuery", "CBuildCaseSelected", "CHouseHoldSelected", "find", "x", "CHouseTypeSelected", "CPayStatusSelected", "CProgressSelected", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "cID", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "length", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "index", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "CFloor", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "userBuildCaseOptions", "CBuildCaseName", "setTimeout", "getHouseById", "CID", "ref", "detailSelected", "apiHouseGetHouseByIdPost$Json", "houseDetail", "changeStartDate", "CChangeStartDate", "Date", "changeEndDate", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "houseHoldMain", "open", "array", "item", "openModelDetail", "openModel", "CBuildingName", "CHouseHoldCount", "formatDate", "CChangeDate", "format", "onSubmitDetail", "editHouseArgsParam", "CHousehold", "CId", "CNationalId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onClose", "onNavidateId", "type", "id", "idURL", "navigate", "onNavidateBuildCaseIdHouseId", "buildCaseId", "houseId", "resetSecureKey", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "addHouseHoldMain", "apiHouseHoldMainAddHouseHoldMainPost$Json", "openQuotation", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationID", "CQuotationStatus", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "cItemName", "CItemName", "cUnitPrice", "CUnitPrice", "cCount", "CCount", "cStatus", "自定義", "cQuotationStatus", "calculateTotal", "error", "console", "context", "closeOnBackdropClick", "createNewQuotation", "addQuotationItem", "push", "loadDefaultItems", "_this2", "request", "success", "data", "defaultItems", "客變需求", "loadRegularItems", "_this3", "regularItems", "選樣", "removeQuotationItem", "splice", "reduce", "sum", "calculateFinalTotal", "Math", "round", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "updateAdditionalFee", "saveQuotation", "_this4", "invalidItems", "filter", "trim", "items", "quotationId", "exportQuotation", "_this5", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printQuotation", "printContent", "generatePrintContent", "printWindow", "write", "onload", "print", "currentDate", "toLocaleDateString", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "getQuotationTypeText", "toLocaleString", "lockQuotation", "_this6", "log", "getQuotationStatusText", "status", "待報價", "已報價", "已簽回", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseList<PERSON>rgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 預設名稱\r\n  additionalFeePercentage: number = 5;   // 預設5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = false;  // 是否啟用百分比費用\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CQuotationStatusSelected.value) {\r\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = false;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationID || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = false;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義\r\n    });\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求 // 客變需求\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.currentHouse.CBuildCaseID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣 // 選樣資料\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額\r\n  calculateFinalTotal() {\r\n    if (this.enableAdditionalFee) {\r\n      this.additionalFeeAmount = Math.round(this.totalAmount * (this.additionalFeePercentage / 100));\r\n      this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n    } else {\r\n      this.additionalFeeAmount = 0;\r\n      this.finalTotalAmount = this.totalAmount;\r\n    }\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 更新百分比費用\r\n  updateAdditionalFee() {\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId // 傳遞當前的報價單ID\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      itemsHtml += `\r\n        <tr>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${index + 1}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px;\">${item.cItemName}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${item.cCount}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: right;\">${this.formatCurrency(subtotal)}</td>\r\n          <td style=\"border: 1px solid #ddd; padding: 8px; text-align: center;\">${quotationType}</td>\r\n        </tr>\r\n      `;\r\n    });\r\n\r\n    return `\r\n      <!DOCTYPE html>\r\n      <html>\r\n      <head>\r\n        <meta charset=\"utf-8\">\r\n        <title>報價單列印</title>\r\n        <style>\r\n          body {\r\n            font-family: 'Microsoft JhengHei', '微軟正黑體', Arial, sans-serif;\r\n            margin: 20px;\r\n            font-size: 14px;\r\n          }\r\n          .header {\r\n            text-align: center;\r\n            margin-bottom: 30px;\r\n          }\r\n          .header h1 {\r\n            margin: 0;\r\n            font-size: 24px;\r\n            color: #333;\r\n          }\r\n          .info-section {\r\n            margin-bottom: 20px;\r\n            border-bottom: 1px solid #ddd;\r\n            padding-bottom: 15px;\r\n          }\r\n          .info-row {\r\n            display: flex;\r\n            margin-bottom: 8px;\r\n          }\r\n          .info-label {\r\n            font-weight: bold;\r\n            width: 100px;\r\n            flex-shrink: 0;\r\n          }\r\n          .info-value {\r\n            flex: 1;\r\n          }\r\n          table {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin-bottom: 20px;\r\n          }\r\n          th {\r\n            background-color: #27ae60;\r\n            color: white;\r\n            border: 1px solid #ddd;\r\n            padding: 10px 8px;\r\n            text-align: center;\r\n            font-weight: bold;\r\n          }\r\n          td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n          .total-section {\r\n            text-align: right;\r\n            margin-top: 20px;\r\n            padding-top: 15px;\r\n            border-top: 2px solid #27ae60;\r\n          }\r\n          .subtotal {\r\n            font-size: 14px;\r\n            margin-bottom: 5px;\r\n            color: #666;\r\n          }\r\n          .additional-fee {\r\n            font-size: 14px;\r\n            margin-bottom: 10px;\r\n            color: #666;\r\n          }\r\n          .total-amount {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #27ae60;\r\n            border-top: 1px solid #ddd;\r\n            padding-top: 10px;\r\n          }\r\n          .footer {\r\n            margin-top: 40px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #666;\r\n          }\r\n          .signature-section {\r\n            margin-top: 40px;\r\n            page-break-inside: avoid;\r\n          }\r\n          .signature-row {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            margin-bottom: 30px;\r\n          }\r\n          .signature-box {\r\n            width: 45%;\r\n            text-align: center;\r\n          }\r\n          .signature-label {\r\n            font-weight: bold;\r\n            margin-bottom: 40px;\r\n            font-size: 16px;\r\n          }\r\n          .signature-line {\r\n            border-bottom: 2px solid #000;\r\n            height: 60px;\r\n            margin-bottom: 10px;\r\n            position: relative;\r\n          }\r\n          .signature-date {\r\n            font-size: 14px;\r\n            margin-top: 15px;\r\n          }\r\n          .signature-notes {\r\n            margin-top: 30px;\r\n            padding: 15px;\r\n            background-color: #f9f9f9;\r\n            border-left: 4px solid #27ae60;\r\n          }\r\n          .signature-notes p {\r\n            margin: 0 0 10px 0;\r\n            font-weight: bold;\r\n          }\r\n          .signature-notes ul {\r\n            margin: 0;\r\n            padding-left: 20px;\r\n          }\r\n          .signature-notes li {\r\n            margin-bottom: 5px;\r\n            line-height: 1.4;\r\n          }\r\n          @media print {\r\n            body { margin: 0; }\r\n            .header { page-break-inside: avoid; }\r\n            .signature-section { page-break-inside: avoid; }\r\n          }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"header\">\r\n          <h1>報價單</h1>\r\n        </div>\r\n\r\n        <div class=\"info-section\">\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">建案名稱：</span>\r\n            <span class=\"info-value\">${buildCaseName}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">戶別：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CHouseHold || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">樓層：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CFloor || ''}樓</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">客戶姓名：</span>\r\n            <span class=\"info-value\">${this.currentHouse?.CCustomerName || ''}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"info-label\">列印日期：</span>\r\n            <span class=\"info-value\">${currentDate}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th width=\"8%\">序號</th>\r\n              <th width=\"35%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"10%\">數量</th>\r\n              <th width=\"20%\">小計 (元)</th>\r\n              <th width=\"12%\">類型</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            ${itemsHtml}\r\n          </tbody>\r\n        </table>\r\n\r\n        <div class=\"total-section\">\r\n          <div class=\"subtotal\">\r\n            小計：${this.formatCurrency(this.totalAmount)}\r\n          </div>\r\n          ${this.enableAdditionalFee ? `\r\n          <div class=\"additional-fee\">\r\n            ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n          </div>\r\n          ` : ''}\r\n          <div class=\"total-amount\">\r\n            總金額：${this.formatCurrency(this.finalTotalAmount)}\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"signature-section\">\r\n          <div class=\"signature-row\">\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">客戶簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n            <div class=\"signature-box\">\r\n              <div class=\"signature-label\">業務簽名：</div>\r\n              <div class=\"signature-line\"></div>\r\n              <div class=\"signature-date\">日期：_____年_____月_____日</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"signature-notes\">\r\n            <p><strong>注意事項：</strong></p>\r\n            <ul>\r\n              <li>此報價單有效期限為30天，逾期需重新報價</li>\r\n              <li>報價內容若有異動，請重新確認</li>\r\n              <li>簽名確認後即視為同意此報價內容</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"footer\">\r\n          此報價單由系統自動產生，列印時間：${new Date().toLocaleString('zh-TW')}\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n  }\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAwBC,kBAAkB,QAAQ,gCAAgC;AAoC3E,IAAMC,4BAA4B,GAAlC,MAAMA,4BAA6B,SAAQb,aAAa;EAE7Dc,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAED,KAAAC,gBAAgB,GAAG,CACjB;MACEH,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;KACR,CACF;IAKD,KAAAE,gBAAgB,GAAU,CAAC;MAAEF,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAK,gBAAgB,GAAU,CAAC;MAAEH,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAM,eAAe,GAAU,CAAC;MAAEJ,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAAO,gBAAgB,GAAU,CAAC;MAAEL,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAQ,gBAAgB,GAAU,CAAC;MAAEN,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAS,iBAAiB,GAAU,CAAC;MAAEP,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAAU,sBAAsB,GAAU,CAAC;MAAER,KAAK,EAAE,IAAI;MAAEF,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAAW,OAAO,GAAG;MACRL,eAAe,EAAE,IAAI,CAACxB,UAAU,CAAC8B,cAAc,CAACzC,iBAAiB,CAAC;MAClEqC,gBAAgB,EAAE,IAAI,CAAC1B,UAAU,CAAC8B,cAAc,CAACvC,aAAa,CAAC;MAC/DkC,gBAAgB,EAAE,IAAI,CAACzB,UAAU,CAAC8B,cAAc,CAACxC,aAAa,CAAC;MAC/DsC,sBAAsB,EAAE,IAAI,CAAC5B,UAAU,CAAC8B,cAAc,CAACrC,mBAAmB;KAC3E;IAGD,KAAAsC,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;KACT;IACD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,WAAW,GAAW,CAAC;IACvB;IACA,KAAAC,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAC,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAAC,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAAC,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAAC,mBAAmB,GAAY,KAAK,CAAC,CAAE;IACvC,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAuHrC,KAAAC,YAAY,GAAgB,IAAI;IAuKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEpC,KAAK,EAAE,EAAE;MAAEE,KAAK,EAAE;KACnB,CACF;IA1XC,IAAI,CAACX,aAAa,CAAC8C,OAAO,EAAE,CAACC,IAAI,CAC/BtE,GAAG,CAAEuE,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAC/C,eAAe,GAAG6C,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAmFSC,QAAQA,CAAA;IACf,IAAI,CAACrC,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAACxB,UAAU,CAAC8B,cAAc,CAACzC,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACoC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAACzB,UAAU,CAAC8B,cAAc,CAACxC,aAAa,CAAC,CACjD;IACD,IAAI,CAACoC,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC1B,UAAU,CAAC8B,cAAc,CAACvC,aAAa,CAAC,CACjD;IACD,IAAI,CAACoC,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAAC3B,UAAU,CAAC8B,cAAc,CAACtC,cAAc,CAAC,CAClD;IACD,IAAI,CAACoC,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAAC5B,UAAU,CAAC8B,cAAc,CAACrC,mBAAmB,CAAC,CACvD;IAED,IAAIC,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,IAAI,IACtErE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAIC,SAAS,IAC5EtE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,CAAC;MACjG,IAAI,CAACK,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAC,kBAAkB,EAAEL,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,GAC7G,IAAI,CAACzC,gBAAgB,CAACgD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACK,kBAAkB,CAACpD,KAAK,CAAC,GACpF,IAAI,CAACK,gBAAgB,CAAC,CAAC,CAAC;QAC5BkD,kBAAkB,EAAER,eAAe,CAACQ,kBAAkB,IAAI,IAAI,IAAIR,eAAe,CAACQ,kBAAkB,IAAIT,SAAS,GAC7G,IAAI,CAACvC,gBAAgB,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACQ,kBAAkB,CAACvD,KAAK,CAAC,GACpF,IAAI,CAACO,gBAAgB,CAAC,CAAC,CAAC;QAC5BiD,kBAAkB,EAAET,eAAe,CAACS,kBAAkB,IAAI,IAAI,IAAIT,eAAe,CAACS,kBAAkB,IAAIV,SAAS,GAC7G,IAAI,CAACtC,gBAAgB,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACS,kBAAkB,CAACxD,KAAK,CAAC,GACpF,IAAI,CAACQ,gBAAgB,CAAC,CAAC,CAAC;QAC5BiD,iBAAiB,EAAEV,eAAe,CAACU,iBAAiB,IAAI,IAAI,IAAIV,eAAe,CAACU,iBAAiB,IAAIX,SAAS,GAC1G,IAAI,CAACxC,eAAe,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACU,iBAAiB,CAACzD,KAAK,CAAC,GAClF,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC;QAC3BoD,mBAAmB,EAAEX,eAAe,CAACW,mBAAmB,IAAI,IAAI,IAAIX,eAAe,CAACW,mBAAmB,IAAIZ,SAAS,GAChH,IAAI,CAACrC,iBAAiB,CAAC4C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACW,mBAAmB,CAAC1D,KAAK,CAAC,GACtF,IAAI,CAACS,iBAAiB,CAAC,CAAC,CAAC;QAC7BkD,wBAAwB,EAAEZ,eAAe,CAACY,wBAAwB,IAAI,IAAI,IAAIZ,eAAe,CAACY,wBAAwB,IAAIb,SAAS,GAC/H,IAAI,CAACpC,sBAAsB,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACY,wBAAwB,CAAC3D,KAAK,CAAC,GAChG,IAAI,CAACU,sBAAsB,CAAC,CAAC,CAAC;QAClCkD,gBAAgB,EAAEb,eAAe,CAACa,gBAAgB,IAAI,IAAI,IAAIb,eAAe,CAACa,gBAAgB,IAAId,SAAS,GACvG,IAAI,CAAC3C,gBAAgB,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACa,gBAAgB,CAAC5D,KAAK,CAAC,GAClF,IAAI,CAACG,gBAAgB,CAAC,CAAC,CAAC;QAC5B0D,KAAK,EAAEd,eAAe,CAACc,KAAK,IAAI,IAAI,IAAId,eAAe,CAACc,KAAK,IAAIf,SAAS,GACtEC,eAAe,CAACc,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEf,eAAe,CAACe,GAAG,IAAI,IAAI,IAAIf,eAAe,CAACe,GAAG,IAAIhB,SAAS,GAChEC,eAAe,CAACe,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAACZ,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACAC,kBAAkB,EAAE,IAAI,CAAC/C,gBAAgB,CAAC,CAAC,CAAC;QAC5CkD,kBAAkB,EAAE,IAAI,CAAChD,gBAAgB,CAAC,CAAC,CAAC;QAC5CiD,kBAAkB,EAAE,IAAI,CAAChD,gBAAgB,CAAC,CAAC,CAAC;QAC5CiD,iBAAiB,EAAE,IAAI,CAACnD,eAAe,CAAC,CAAC,CAAC;QAC1CoD,mBAAmB,EAAE,IAAI,CAACjD,iBAAiB,CAAC,CAAC,CAAC;QAC9CkD,wBAAwB,EAAE,IAAI,CAACjD,sBAAsB,CAAC,CAAC,CAAC;QACxDkD,gBAAgB,EAAE,IAAI,CAACzD,gBAAgB,CAAC,CAAC,CAAC;QAC1C0D,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChBd,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAU,KAAK,EAAE,IAAI,CAACX,WAAW,CAACW,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAACZ,WAAW,CAACY,GAAG;MACzBV,kBAAkB,EAAE,IAAI,CAACF,WAAW,CAACE,kBAAkB;MACvDG,kBAAkB,EAAE,IAAI,CAACL,WAAW,CAACK,kBAAkB;MACvDK,gBAAgB,EAAE,IAAI,CAACV,WAAW,CAACU,gBAAgB;MACnDJ,kBAAkB,EAAE,IAAI,CAACN,WAAW,CAACM,kBAAkB;MACvDC,iBAAiB,EAAE,IAAI,CAACP,WAAW,CAACO,iBAAiB;MACrDC,mBAAmB,EAAE,IAAI,CAACR,WAAW,CAACQ,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAACT,WAAW,CAACS;KAC5C;IACDnF,mBAAmB,CAAC0F,iBAAiB,CAACzF,WAAW,CAACoE,YAAY,EAAEG,IAAI,CAACmB,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA2B,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACzE,SAAS,GAAGyE,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EAEA6B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EAAE;MAC3C,IAAI,CAACtF,aAAa,CAACuF,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;OACnD,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAACpF,gBAAgB,CAACqF,iBAAiB,CACrCtC,GAAG,CAACoC,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAAC3F,OAAO,CAAC8F,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAACtD,YAAY,GAAGmD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACE,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAACvD,YAAY,EAAE;MACrB,MAAMwD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC1D,YAAY,CAAC;MAC3C,IAAI,CAACjD,aAAa,CAAC4G,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJrB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;UACrDwB,KAAK,EAAE,IAAI,CAAC7D;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAC5F,OAAO,CAACiH,aAAa,CAAC1D,GAAG,CAACwC,OAAQ,CAAC;UACxC,IAAI,CAACX,YAAY,EAAE,CAAC1B,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAAC1D,OAAO,CAAC8F,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAmB,gBAAgBA,CAAA;IACd,IAAI,CAAChH,aAAa,CAACiH,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAErB,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB;MAAG;KAC9D,CAAC,CAAC9B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvE,gBAAgB,GAAG,CAAC;UACvBL,KAAK,EAAE,EAAE;UAAEE,KAAK,EAAE;SACnB,EAAE,GAAGqC,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAErG,KAAK,EAAEqG,CAAC;YAAEnG,KAAK,EAAEmG;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI7H,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,IAAI,IACtErE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAIC,SAAS,IAC5EtE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACK,kBAAkB,IAAI,IAAI,IAAIL,eAAe,CAACK,kBAAkB,IAAIN,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAACjG,gBAAgB,CAACkG,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAACtD,KAAK,IAAI+C,eAAe,CAACK,kBAAkB,CAACpD,KAAK,CAAC;YAC5G,IAAI,CAACkD,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAAC/C,gBAAgB,CAACiG,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACE,kBAAkB,GAAG,IAAI,CAAC/C,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKAmG,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB/B,YAAY,EAAE,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG;MACrDkC,SAAS,EAAE,IAAI,CAAC7G,SAAS;MACzB8G,QAAQ,EAAE,IAAI,CAAC/G;KAChB;IACD,IAAI,IAAI,CAACsD,WAAW,CAACW,KAAK,IAAI,IAAI,CAACX,WAAW,CAACY,GAAG,EAAE;MAClD,IAAI,CAAC2C,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAE5C,KAAK,EAAE,IAAI,CAACX,WAAW,CAACW,KAAK;QAAEC,GAAG,EAAE,IAAI,CAACZ,WAAW,CAACY;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAACZ,WAAW,CAACE,kBAAkB,EAAE;MACvC,IAAI,CAACqD,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACE,kBAAkB,CAACpD,KAAK;IAC5E;IACA,IAAI,IAAI,CAACkD,WAAW,CAACK,kBAAkB,CAACvD,KAAK,EAAE;MAC7C,IAAI,CAACyG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACK,kBAAkB,CAACvD,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAACkD,WAAW,CAACU,gBAAgB,CAAC5D,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAACyG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACU,gBAAgB,CAAC5D,KAAK;IACzE;IACA,IAAI,IAAI,CAACkD,WAAW,CAACM,kBAAkB,CAACxD,KAAK,EAAE;MAC7C,IAAI,CAACyG,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACM,kBAAkB,CAACxD,KAAK;IAC5E;IACA,IAAI,IAAI,CAACkD,WAAW,CAACO,iBAAiB,CAACzD,KAAK,EAAE;MAC5C,IAAI,CAACyG,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACO,iBAAiB,CAACzD,KAAK;IAC1E;IACA,IAAI,IAAI,CAACkD,WAAW,CAACQ,mBAAmB,CAAC1D,KAAK,EAAE;MAC9C,IAAI,CAACyG,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACQ,mBAAmB,CAAC1D,KAAK;IAC9E;IACA,IAAI,IAAI,CAACkD,WAAW,CAACS,wBAAwB,CAAC3D,KAAK,EAAE;MACnD,IAAI,CAACyG,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACvD,WAAW,CAACS,wBAAwB,CAAC3D,KAAK;IACxF;IAEA,OAAO,IAAI,CAACyG,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,MAAM,IAAI,CAAC,KAAKF,CAAC,CAACE,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA7C,YAAYA,CAAA;IACV,OAAO,IAAI,CAAClF,aAAa,CAACgI,6BAA6B,CAAC;MACtDnB,IAAI,EAAE,IAAI,CAACS,WAAW;KACvB,CAAC,CAAClE,IAAI,CACLtE,GAAG,CAACuE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACuC,SAAS,GAAG5E,GAAG,CAACoC,OAAO;QAC5B,IAAI,CAAC7E,YAAY,GAAGyC,GAAG,CAAC6E,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACnB,gBAAgB,EAAE;IACvB,IAAI,CAAC9B,YAAY,EAAE,CAAC1B,SAAS,EAAE;EACjC;EACAqB,gBAAgBA,CAAA;IACd,IAAI,CAAC3E,iBAAiB,CAACkI,6CAA6C,CAAC;MACnEvB,IAAI,EAAE;QACJwB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAAClF,IAAI,CACLtE,GAAG,CAACuE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC6C,oBAAoB,GAAGlF,GAAG,CAACoC,OAAO,EAAEc,MAAM,GAAGlD,GAAG,CAACoC,OAAO,CAACyB,GAAG,CAAC7D,GAAG,IAAG;UACtE,OAAO;YACLmF,cAAc,EAAEnF,GAAG,CAACmF,cAAc;YAClClD,GAAG,EAAEjC,GAAG,CAACiC;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAIhG,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,IAAI,IACtErE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAIC,SAAS,IAC5EtE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACzE,mBAAmB,CAACoE,iBAAiB,CAACnE,WAAW,CAACoE,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACI,kBAAkB,IAAI,IAAI,IAAIJ,eAAe,CAACI,kBAAkB,IAAIL,SAAS,EAAE;YACjG,IAAIwD,KAAK,GAAG,IAAI,CAACmB,oBAAoB,CAAClB,SAAS,CAAEjD,CAAM,IAAKA,CAAC,CAACkB,GAAG,IAAIzB,eAAe,CAACI,kBAAkB,CAACqB,GAAG,CAAC;YAC5G,IAAI,CAACtB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAACnB,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAACpD,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACvE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACsE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACFzJ,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAACkI,gBAAgB,EAAE;MACvByB,UAAU,CAAC,MAAK;QACd,IAAI,CAACvD,YAAY,EAAE,CAAC1B,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAkF,YAAYA,CAACC,GAAQ,EAAEC,GAAQ;IAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC7I,aAAa,CAAC8I,6BAA6B,CAAC;MAC/CjC,IAAI,EAAE;QAAEjF,QAAQ,EAAE+G;MAAG;KACtB,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACqD,WAAW,GAAG;UACjB,GAAG1F,GAAG,CAACoC,OAAO;UACduD,eAAe,EAAE3F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,GAAG,IAAIC,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAACwD,gBAAgB,CAAC,GAAGrF,SAAS;UAClGuF,aAAa,EAAE9F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,GAAG,IAAIF,IAAI,CAAC7F,GAAG,CAACoC,OAAO,CAAC2D,cAAc,CAAC,GAAGxF;SACpF;QAED,IAAIP,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,CAACR,cAAc,CAAC5E,kBAAkB,GAAG,IAAI,CAACqF,eAAe,CAAC,IAAI,CAACf,oBAAoB,EAAE,KAAK,EAAElF,GAAG,CAACoC,OAAO,CAAC4D,YAAY,CAAC;QAC3H;QACA,IAAI,CAACR,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACgF,eAAe,CAAC,IAAI,CAAC7H,OAAO,CAACH,gBAAgB,EAAE,OAAO,EAAE+B,GAAG,CAACoC,OAAO,CAAC1D,UAAU,CAAC;QAC7H,IAAIsB,GAAG,CAACoC,OAAO,CAACrD,UAAU,EAAE;UAC1B,IAAI,CAACyG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACiF,eAAe,CAAC,IAAI,CAAC7H,OAAO,CAACJ,gBAAgB,EAAE,OAAO,EAAEgC,GAAG,CAACoC,OAAO,CAACrD,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACyG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAAC5C,OAAO,CAACJ,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACwH,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAAC+E,eAAe,CAAC,IAAI,CAAC7H,OAAO,CAACL,eAAe,EAAE,OAAO,EAAEiC,GAAG,CAACoC,OAAO,CAACtD,SAAS,CAAC;QAE1H,IAAIkB,GAAG,CAACoC,OAAO,CAAC4D,YAAY,EAAE;UAC5B,IAAI,IAAI,CAACE,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAAC/D,YAAY,GAAGnC,GAAG,CAACoC,OAAO,CAAC4D,YAAY;UAC5D;QACF;QACA,IAAI,CAACxJ,aAAa,CAAC2J,IAAI,CAACZ,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAU,eAAeA,CAACG,KAAY,EAAE1I,GAAW,EAAED,KAAU;IACnD,OAAO2I,KAAK,CAACtF,IAAI,CAACuF,IAAI,IAAIA,IAAI,CAAC3I,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGA6I,eAAeA,CAACf,GAAQ,EAAEc,IAAS;IACjC,IAAI,CAAChB,YAAY,CAACgB,IAAI,CAACf,GAAG,EAAEC,GAAG,CAAC;EAClC;EAEAgB,SAASA,CAAChB,GAAQ;IAChB,IAAI,CAACW,aAAa,GAAG;MACnBM,aAAa,EAAE,EAAE;MACjB9B,MAAM,EAAEnE,SAAS;MACjBkG,eAAe,EAAElG;KAClB;IACD,IAAI,CAAC/D,aAAa,CAAC2J,IAAI,CAACZ,GAAG,CAAC;EAC9B;EAKAmB,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOhL,MAAM,CAACgL,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,cAAcA,CAACtB,GAAQ;IACrB,IAAI,CAACG,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACC,eAAe,GAAG,IAAI,CAACe,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACC,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACD,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACI,aAAa,GAAG,IAAI,CAACY,UAAU,CAAC,IAAI,CAAChB,WAAW,CAACI,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAACgB,kBAAkB,GAAG;MACxBlI,aAAa,EAAE,IAAI,CAAC8G,WAAW,CAAC9G,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAAC0G,WAAW,CAACqB,UAAU;MACvCxI,QAAQ,EAAE,IAAI,CAACmH,WAAW,CAACsB,GAAG;MAC9BjI,UAAU,EAAE,IAAI,CAACyG,cAAc,CAACxE,kBAAkB,GAAG,IAAI,CAACwE,cAAc,CAACxE,kBAAkB,CAACvD,KAAK,GAAG,IAAI;MACxGgB,SAAS,EAAE,IAAI,CAACiH,WAAW,CAACjH,SAAS;MACrCE,SAAS,EAAE,IAAI,CAAC+G,WAAW,CAAC/G,SAAS;MACrCH,KAAK,EAAE,IAAI,CAACkH,WAAW,CAAClH,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAAC6G,WAAW,CAACuB,WAAW;MACzCvI,UAAU,EAAE,IAAI,CAAC8G,cAAc,CAACvE,kBAAkB,GAAG,IAAI,CAACuE,cAAc,CAACvE,kBAAkB,CAACxD,KAAK,GAAG,IAAI;MACxGwB,MAAM,EAAE,IAAI,CAACyG,WAAW,CAACzG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAAC0G,cAAc,CAACtE,iBAAiB,GAAG,IAAI,CAACsE,cAAc,CAACtE,iBAAiB,CAACzD,KAAK,GAAG,IAAI;MACrGmI,gBAAgB,EAAE,IAAI,CAACF,WAAW,CAACE,gBAAgB;MACnDG,cAAc,EAAE,IAAI,CAACL,WAAW,CAACK;KAClC;IACH,IAAI,CAACmB,UAAU,EAAE;IACjB,IAAI,IAAI,CAACxK,KAAK,CAACyK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACzG,OAAO,CAAC2K,aAAa,CAAC,IAAI,CAAC1K,KAAK,CAACyK,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAACxK,aAAa,CAAC0K,0BAA0B,CAAC;MAC5C7D,IAAI,EAAE,IAAI,CAACsD;KACZ,CAAC,CAAC/G,IAAI,CACLtE,GAAG,CAACuE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC5F,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAC7K,OAAO,CAAC8F,YAAY,CAACvC,GAAG,CAACwC,OAAQ,CAAC;QACvC+C,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF9L,SAAS,CAAC,MAAM,IAAI,CAACqG,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf;EAGAoH,QAAQA,CAAChC,GAAQ;IACf,IAAIiC,OAAO,GAAkB;MAC3B5I,aAAa,EAAE,IAAI,CAAC8G,WAAW,CAAC9G,aAAa;MAC7CI,UAAU,EAAE,IAAI,CAAC0G,WAAW,CAACqB,UAAU;MACvCxI,QAAQ,EAAE,IAAI,CAACmH,WAAW,CAACsB,GAAG;MAC9BjI,UAAU,EAAE,IAAI,CAAC2G,WAAW,CAAC3G,UAAU;MACvCN,SAAS,EAAE,IAAI,CAACiH,WAAW,CAACjH,SAAS;MACrCE,SAAS,EAAE,IAAI,CAAC+G,WAAW,CAAC/G,SAAS;MACrCH,KAAK,EAAE,IAAI,CAACkH,WAAW,CAAClH,KAAK;MAC7BK,WAAW,EAAE,IAAI,CAAC6G,WAAW,CAACuB,WAAW;MACzCvI,UAAU,EAAE,IAAI,CAACgH,WAAW,CAAChH,UAAU;MACvCO,MAAM,EAAE,IAAI,CAACyG,WAAW,CAACzG,MAAM;MAC/BH,SAAS,EAAE,IAAI,CAAC4G,WAAW,CAAC5G;KAC7B;IACD,IAAI,CAACnC,aAAa,CAAC0K,0BAA0B,CAAC;MAC5C7D,IAAI,EAAEgE;KACP,CAAC,CAACrH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC5F,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAG,OAAOA,CAAClC,GAAQ;IACdA,GAAG,CAAC+B,KAAK,EAAE;EACb;EAEAI,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACjH,WAAW,CAACC,kBAAkB,CAACqB,GAAG;IAC/D,IAAI,CAAClF,MAAM,CAAC+K,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEAE,4BAA4BA,CAACJ,IAAS,EAAEK,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAAClL,MAAM,CAAC+K,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEK,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEAC,cAAcA,CAAC7B,IAAS;IACtB,IAAI8B,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACxL,aAAa,CAACyL,oCAAoC,CAAC;QACtD5E,IAAI,EAAE6C,IAAI,CAACf;OACZ,CAAC,CAACnF,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACqC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAC5F,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAwD,UAAUA,CAAA;IACR,IAAI,CAACxK,KAAK,CAAC2L,KAAK,EAAE;IAClB,IAAI,CAAC3L,KAAK,CAAC4L,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC5C,WAAW,CAACsB,GAAG,CAAC;IACnD,IAAI,CAACtK,KAAK,CAAC4L,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxB,kBAAkB,CAAC9H,UAAU,CAAC;IACjE,IAAI,CAACtC,KAAK,CAAC6L,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACzB,kBAAkB,CAAC9H,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAACtC,KAAK,CAAC4L,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC5C,WAAW,CAAChB,MAAM,CAAC;IACpD,IAAI,CAAChI,KAAK,CAAC6L,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACzB,kBAAkB,CAAClI,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAAClC,KAAK,CAAC8L,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC1B,kBAAkB,CAACtI,KAAK,EAAE,IAAI,CAAC1B,OAAO,CAAC2L,WAAW,CAAC;IACrF,IAAI,CAAC/L,KAAK,CAACgM,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC5B,kBAAkB,CAAC7H,MAAM,CAAC;IAClE,IAAI,CAACvC,KAAK,CAAC4L,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxB,kBAAkB,CAAChI,SAAS,CAAC;IAC9D,IAAI,CAACpC,KAAK,CAAC4L,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACxE,kBAAkB,CAACvD,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAAC4L,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9C,cAAc,CAACvE,kBAAkB,CAACxD,KAAK,CAAC;IAC3E,IAAI,IAAI,CAACiI,WAAW,CAACE,gBAAgB,EAAE;MACrC,IAAI,CAAClJ,KAAK,CAAC4L,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACK,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACL,WAAW,CAACK,cAAc,EAAE;MACnC,IAAI,CAACrJ,KAAK,CAAC4L,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC5C,WAAW,CAACE,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAClJ,KAAK,CAACiM,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACjD,WAAW,CAACE,gBAAgB,GAAG,IAAI,CAACF,WAAW,CAACE,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACF,WAAW,CAACK,cAAc,GAAG,IAAI,CAACL,WAAW,CAACK,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEA6C,uBAAuBA,CAAA;IACrB,IAAI,CAAClM,KAAK,CAAC2L,KAAK,EAAE;IAClB,IAAI,CAAC3L,KAAK,CAAC4L,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAAC/D,YAAY,CAAC;IAC5D,IAAI,CAACzF,KAAK,CAAC4L,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACpC,aAAa,CAACM,aAAa,CAAC;IAC7D,IAAI,CAAC9J,KAAK,CAAC6L,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACrC,aAAa,CAACM,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAAC9J,KAAK,CAACmM,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC3C,aAAa,CAACxB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAChI,KAAK,CAACmM,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC3C,aAAa,CAACO,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAqC,gBAAgBA,CAACvD,GAAQ;IACvB,IAAI,CAACW,aAAa,CAAC/D,YAAY,GAAG,IAAI,CAACxB,WAAW,CAACC,kBAAkB,CAACqB,GAAG,EACvE,IAAI,CAAC2G,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAAClM,KAAK,CAACyK,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACzG,OAAO,CAAC2K,aAAa,CAAC,IAAI,CAAC1K,KAAK,CAACyK,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACvK,qBAAqB,CAACmM,yCAAyC,CAAC;MACnEvF,IAAI,EAAE,IAAI,CAAC0C;KACZ,CAAC,CAACnG,IAAI,CACLtE,GAAG,CAACuE,GAAG,IAAG;MACR,IAAIA,GAAG,CAACqC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC5F,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QAClC6B,GAAG,CAAC+B,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF9L,SAAS,CAAC,MAAM,IAAI,CAACqG,YAAY,EAAE,CAAC,CACrC,CAAC1B,SAAS,EAAE;EACf,CAAC,CAAE;EACG6I,aAAaA,CAACC,MAAW,EAAE5C,IAAS;IAAA,IAAA6C,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAACzJ,YAAY,GAAG4G,IAAI;MACxB6C,KAAI,CAAChK,cAAc,GAAG,EAAE;MACxBgK,KAAI,CAAC/J,WAAW,GAAG,CAAC;MACpB+J,KAAI,CAACxJ,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7BwJ,KAAI,CAACvJ,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACAuJ,KAAI,CAAC9J,iBAAiB,GAAG,KAAK;MAC9B8J,KAAI,CAAC7J,uBAAuB,GAAG,CAAC;MAChC6J,KAAI,CAAC5J,mBAAmB,GAAG,CAAC;MAC5B4J,KAAI,CAAC3J,gBAAgB,GAAG,CAAC;MACzB2J,KAAI,CAAC1J,mBAAmB,GAAG,KAAK;MAEhC;MACA,IAAI;QACF,MAAM4J,QAAQ,SAASF,KAAI,CAAChM,gBAAgB,CAACmM,qBAAqB,CAAChD,IAAI,CAACf,GAAG,CAAC,CAACgE,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAAC/G,UAAU,KAAK,CAAC,IAAI+G,QAAQ,CAAChH,OAAO,EAAE;UAC7D;UACA8G,KAAI,CAACxJ,kBAAkB,GAAG0J,QAAQ,CAAChH,OAAO,CAACmH,YAAY,IAAI,CAAC;UAC5D;UACA,IAAIH,QAAQ,CAAChH,OAAO,CAACoH,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7CN,KAAI,CAACvJ,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACLuJ,KAAI,CAACvJ,mBAAmB,GAAG,IAAI;UACjC;UACA;UACA,IAAIyJ,QAAQ,CAAChH,OAAO,CAACqH,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAChH,OAAO,CAACqH,KAAK,CAAC,EAAE;YACnE;YACAP,KAAI,CAAChK,cAAc,GAAGkK,QAAQ,CAAChH,OAAO,CAACqH,KAAK,CAAC5F,GAAG,CAAE+F,KAAU,KAAM;cAChEC,QAAQ,EAAET,QAAQ,CAAChH,OAAO,CAAC7D,QAAQ,IAAI8H,IAAI,CAACf,GAAG;cAC/CwE,YAAY,EAAEV,QAAQ,CAAChH,OAAO,CAACmH,YAAY;cAC3CQ,SAAS,EAAEH,KAAK,CAACI,SAAS,IAAI,EAAE;cAChCC,UAAU,EAAEL,KAAK,CAACM,UAAU,IAAI,CAAC;cACjCC,MAAM,EAAEP,KAAK,CAACQ,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAET,KAAK,CAAC3E,OAAO,IAAI,CAAC;cAC3B9I,kBAAkB,EAAEyN,KAAK,CAACzN,kBAAkB,IAAIyN,KAAK,CAACzN,kBAAkB,GAAG,CAAC,GAAGyN,KAAK,CAACzN,kBAAkB,GAAGA,kBAAkB,CAACmO,GAAG;cAChIC,gBAAgB,EAAEX,KAAK,CAACJ;aACzB,CAAC,CAAC;YACHN,KAAI,CAACsB,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAvB,KAAI,CAAC1M,aAAa,CAAC2J,IAAI,CAAC8C,MAAM,EAAE;QAC9B0B,OAAO,EAAEtE,IAAI;QACbuE,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAACnL,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACR,cAAc,GAAG,EAAE;IACxB,IAAI,CAACS,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACR,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACE,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,CAAC/C,OAAO,CAACiH,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACAoH,gBAAgBA,CAAA;IACd,IAAI,CAAC5L,cAAc,CAAC6L,IAAI,CAAC;MACvBlB,QAAQ,EAAE,IAAI,CAACpK,YAAY,EAAE6F,GAAG,IAAI,CAAC;MACrCyE,SAAS,EAAE,EAAE;MACbE,UAAU,EAAE,CAAC;MACbE,MAAM,EAAE,CAAC;MACTE,OAAO,EAAE,CAAC;MACVlO,kBAAkB,EAAEA,kBAAkB,CAACmO;KACxC,CAAC;EACJ;EACA;EACMU,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MACpB,IAAI;QACF,IAAI,CAAC8B,MAAI,CAACxL,YAAY,EAAE6F,GAAG,EAAE;UAC3B2F,MAAI,CAACxO,OAAO,CAAC8F,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM2I,OAAO,GAAG;UACd/I,YAAY,EAAE8I,MAAI,CAACxL,YAAY,CAAC0C,YAAY,IAAI,CAAC;UACjD5D,QAAQ,EAAE0M,MAAI,CAACxL,YAAY,CAAC6F;SAC7B;QAED,MAAM8D,QAAQ,SAAS6B,MAAI,CAAC/N,gBAAgB,CAAC8N,gBAAgB,CAACE,OAAO,CAAC,CAAC5B,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE+B,OAAO,IAAI/B,QAAQ,CAACgC,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAGjC,QAAQ,CAACgC,IAAI,CAACvH,GAAG,CAAE9C,CAAM,KAAM;YAClD+I,YAAY,EAAE/I,CAAC,CAACwI,YAAY;YAC5BM,QAAQ,EAAEoB,MAAI,CAACxL,YAAY,EAAE6F,GAAG;YAChCyE,SAAS,EAAEhJ,CAAC,CAACiJ,SAAS;YACtBC,UAAU,EAAElJ,CAAC,CAACmJ,UAAU;YACxBC,MAAM,EAAEpJ,CAAC,CAACqJ,MAAM;YAChBC,OAAO,EAAEtJ,CAAC,CAACkE,OAAO;YAClB9I,kBAAkB,EAAEA,kBAAkB,CAACmP,IAAI,CAAC;WAC7C,CAAC,CAAC;UACHL,MAAI,CAAC/L,cAAc,CAAC6L,IAAI,CAAC,GAAGM,YAAY,CAAC;UACzCJ,MAAI,CAACT,cAAc,EAAE;UACrBS,MAAI,CAACxO,OAAO,CAACiH,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLuH,MAAI,CAACxO,OAAO,CAAC8F,YAAY,CAAC6G,QAAQ,EAAE3M,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOgO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCQ,MAAI,CAACxO,OAAO,CAAC8F,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACMgJ,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MACpB,IAAI;QACF,IAAI,CAACqC,MAAI,CAAC/L,YAAY,EAAE6F,GAAG,EAAE;UAC3BkG,MAAI,CAAC/O,OAAO,CAAC8F,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAM2I,OAAO,GAAG;UACd/I,YAAY,EAAEqJ,MAAI,CAAC/L,YAAY,CAAC0C,YAAY,IAAI,CAAC;UACjD5D,QAAQ,EAAEiN,MAAI,CAAC/L,YAAY,CAAC6F;SAC7B;QAED,MAAM8D,QAAQ,SAASoC,MAAI,CAACtO,gBAAgB,CAACqO,gBAAgB,CAACL,OAAO,CAAC,CAAC5B,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE+B,OAAO,IAAI/B,QAAQ,CAACgC,IAAI,EAAE;UACtC,MAAMK,YAAY,GAAGrC,QAAQ,CAACgC,IAAI,CAACvH,GAAG,CAAE9C,CAAM,KAAM;YAClD+I,YAAY,EAAE/I,CAAC,CAACwI,YAAY;YAC5BM,QAAQ,EAAE2B,MAAI,CAAC/L,YAAY,EAAE6F,GAAG;YAChCyE,SAAS,EAAEhJ,CAAC,CAACiJ,SAAS;YACtBC,UAAU,EAAElJ,CAAC,CAACmJ,UAAU;YACxBC,MAAM,EAAEpJ,CAAC,CAACqJ,MAAM;YAChBC,OAAO,EAAEtJ,CAAC,CAACkE,OAAO;YAClB9I,kBAAkB,EAAEA,kBAAkB,CAACuP,EAAE,CAAC;WAC3C,CAAC,CAAC;UACHF,MAAI,CAACtM,cAAc,CAAC6L,IAAI,CAAC,GAAGU,YAAY,CAAC;UACzCD,MAAI,CAAChB,cAAc,EAAE;UACrBgB,MAAI,CAAC/O,OAAO,CAACiH,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL8H,MAAI,CAAC/O,OAAO,CAAC8F,YAAY,CAAC6G,QAAQ,EAAE3M,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAOgO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCe,MAAI,CAAC/O,OAAO,CAAC8F,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACAoJ,mBAAmBA,CAAC5H,KAAa;IAC/B,MAAMsC,IAAI,GAAG,IAAI,CAACnH,cAAc,CAAC6E,KAAK,CAAC;IACvC,IAAI,CAAC7E,cAAc,CAAC0M,MAAM,CAAC7H,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACyG,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACrL,WAAW,GAAG,IAAI,CAACD,cAAc,CAAC2M,MAAM,CAAC,CAACC,GAAG,EAAEzF,IAAI,KAAI;MAC1D,OAAOyF,GAAG,GAAIzF,IAAI,CAAC4D,UAAU,GAAG5D,IAAI,CAAC8D,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAAC4B,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAACvM,mBAAmB,EAAE;MAC5B,IAAI,CAACF,mBAAmB,GAAG0M,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC9M,WAAW,IAAI,IAAI,CAACE,uBAAuB,GAAG,GAAG,CAAC,CAAC;MAC9F,IAAI,CAACE,gBAAgB,GAAG,IAAI,CAACJ,WAAW,GAAG,IAAI,CAACG,mBAAmB;IACrE,CAAC,MAAM;MACL,IAAI,CAACA,mBAAmB,GAAG,CAAC;MAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACJ,WAAW;IAC1C;EACF;EAEA;EACA+M,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAAC5F,MAAM,CAACuF,MAAM,CAAC;EACnB;EAEA;EACAM,mBAAmBA,CAAA;IACjB,IAAI,CAACV,mBAAmB,EAAE;EAC5B;EAEA;EACMW,aAAaA,CAACnH,GAAQ;IAAA,IAAAoH,MAAA;IAAA,OAAAxD,iBAAA;MAC1B,IAAIwD,MAAI,CAACzN,cAAc,CAACgE,MAAM,KAAK,CAAC,EAAE;QACpCyJ,MAAI,CAAClQ,OAAO,CAAC8F,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAMqK,YAAY,GAAGD,MAAI,CAACzN,cAAc,CAAC2N,MAAM,CAACxG,IAAI,IAClD,CAACA,IAAI,CAAC0D,SAAS,CAAC+C,IAAI,EAAE,CACvB;MAED,IAAIF,YAAY,CAAC1J,MAAM,GAAG,CAAC,EAAE;QAC3ByJ,MAAI,CAAClQ,OAAO,CAAC8F,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAAE,IAAI;QACJ,MAAM2I,OAAO,GAAG;UACdjD,OAAO,EAAE0E,MAAI,CAAClN,YAAY,CAAC6F,GAAG;UAC9ByH,KAAK,EAAEJ,MAAI,CAACzN,cAAc;UAC1B8N,WAAW,EAAEL,MAAI,CAACjN,kBAAkB,CAAC;SACtC;QAED,MAAM0J,QAAQ,SAASuD,MAAI,CAACzP,gBAAgB,CAACwP,aAAa,CAACxB,OAAO,CAAC,CAAC5B,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAE+B,OAAO,EAAE;UACrBwB,MAAI,CAAClQ,OAAO,CAACiH,aAAa,CAAC,SAAS,CAAC;UACrC6B,GAAG,CAAC+B,KAAK,EAAE;QACb,CAAC,MAAM;UACLqF,MAAI,CAAClQ,OAAO,CAAC8F,YAAY,CAAC6G,QAAQ,EAAE3M,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAOgO,KAAK,EAAE;QACdkC,MAAI,CAAClQ,OAAO,CAAC8F,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACM0K,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/D,iBAAA;MACnB,IAAI;QACF,MAAMgE,IAAI,SAA2BD,MAAI,CAAChQ,gBAAgB,CAAC+P,eAAe,CAACC,MAAI,CAACzN,YAAY,CAAC6F,GAAG,CAAC,CAACgE,SAAS,EAAE;QAC7G,IAAI6D,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAACzN,YAAY,CAACT,UAAU,IAAIkO,MAAI,CAACzN,YAAY,CAACiF,MAAM,OAAO;UACtF8I,IAAI,CAAC5K,KAAK,EAAE;UACZyK,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAACzQ,OAAO,CAAC8F,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOkI,KAAK,EAAE;QACdyC,MAAI,CAACzQ,OAAO,CAAC8F,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACAuL,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5O,cAAc,CAACgE,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAACzG,OAAO,CAAC8F,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAMwL,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAGZ,MAAM,CAAClH,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAI8H,WAAW,EAAE;QACfA,WAAW,CAACR,QAAQ,CAACtH,IAAI,EAAE;QAC3B8H,WAAW,CAACR,QAAQ,CAACS,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACR,QAAQ,CAACnG,KAAK,EAAE;QAE5B;QACA2G,WAAW,CAACE,MAAM,GAAG;UACnB/I,UAAU,CAAC,MAAK;YACd6I,WAAW,CAACG,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAC3R,OAAO,CAAC8F,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAOkI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAChO,OAAO,CAAC8F,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQyL,oBAAoBA,CAAA;IAC1B,MAAMK,WAAW,GAAG,IAAIxI,IAAI,EAAE,CAACyI,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMC,aAAa,GAAG,IAAI,CAAC5N,WAAW,CAACC,kBAAkB,EAAEuE,cAAc,IAAI,EAAE;IAE/E,IAAIqJ,SAAS,GAAG,EAAE;IAClB,IAAI,CAACtP,cAAc,CAACuP,OAAO,CAAC,CAACpI,IAAI,EAAEtC,KAAK,KAAI;MAC1C,MAAM2K,QAAQ,GAAGrI,IAAI,CAAC4D,UAAU,GAAG5D,IAAI,CAAC8D,MAAM;MAC9C,MAAMwE,aAAa,GAAG,IAAI,CAACC,oBAAoB,CAACvI,IAAI,CAAClK,kBAAkB,CAAC;MACxEqS,SAAS,IAAI;;kFAE+DzK,KAAK,GAAG,CAAC;8DAC7BsC,IAAI,CAAC0D,SAAS;iFACK,IAAI,CAACmC,cAAc,CAAC7F,IAAI,CAAC4D,UAAU,CAAC;kFACnC5D,IAAI,CAAC8D,MAAM;iFACZ,IAAI,CAAC+B,cAAc,CAACwC,QAAQ,CAAC;kFAC5BC,aAAa;;OAExF;IACH,CAAC,CAAC;IAEF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAiJ4BJ,aAAa;;;;uCAIb,IAAI,CAAC9O,YAAY,EAAET,UAAU,IAAI,EAAE;;;;uCAInC,IAAI,CAACS,YAAY,EAAEiF,MAAM,IAAI,EAAE;;;;uCAI/B,IAAI,CAACjF,YAAY,EAAEb,aAAa,IAAI,EAAE;;;;uCAItCyP,WAAW;;;;;;;;;;;;;;;;cAgBpCG,SAAS;;;;;;iBAMN,IAAI,CAACtC,cAAc,CAAC,IAAI,CAAC/M,WAAW,CAAC;;YAE1C,IAAI,CAACK,mBAAmB,GAAG;;cAEzB,IAAI,CAACJ,iBAAiB,KAAK,IAAI,CAACC,uBAAuB,MAAM,IAAI,CAAC6M,cAAc,CAAC,IAAI,CAAC5M,mBAAmB,CAAC;;WAE7G,GAAG,EAAE;;kBAEE,IAAI,CAAC4M,cAAc,CAAC,IAAI,CAAC3M,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;6BA4B/B,IAAIsG,IAAI,EAAE,CAACgJ,cAAc,CAAC,OAAO,CAAC;;;;KAI1D;EACH;EAEA;EACMC,aAAaA,CAACvJ,GAAQ;IAAA,IAAAwJ,MAAA;IAAA,OAAA5F,iBAAA;MAC1B,IAAI4F,MAAI,CAAC7P,cAAc,CAACgE,MAAM,KAAK,CAAC,EAAE;QACpC6L,MAAI,CAACtS,OAAO,CAAC8F,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI,CAACwM,MAAI,CAACrP,kBAAkB,EAAE;QAC5BqP,MAAI,CAACtS,OAAO,CAAC8F,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAM6G,QAAQ,SAAS2F,MAAI,CAAC7R,gBAAgB,CAAC4R,aAAa,CAACC,MAAI,CAACrP,kBAAkB,CAAC,CAAC4J,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAAC+B,OAAO,EAAE;UACpB4D,MAAI,CAACtS,OAAO,CAACiH,aAAa,CAAC,UAAU,CAAC;UACtCgH,OAAO,CAACsE,GAAG,CAAC,UAAU,EAAE;YACtBhC,WAAW,EAAE+B,MAAI,CAACrP,kBAAkB;YACpCjD,OAAO,EAAE2M,QAAQ,CAAC3M;WACnB,CAAC;QACJ,CAAC,MAAM;UACLsS,MAAI,CAACtS,OAAO,CAAC8F,YAAY,CAAC6G,QAAQ,CAAC3M,OAAO,IAAI,SAAS,CAAC;UACxDiO,OAAO,CAACD,KAAK,CAAC,UAAU,EAAErB,QAAQ,CAAC3M,OAAO,CAAC;QAC7C;QAEA8I,GAAG,CAAC+B,KAAK,EAAE;MACb,CAAC,CAAC,OAAOmD,KAAK,EAAE;QACdsE,MAAI,CAACtS,OAAO,CAAC8F,YAAY,CAAC,SAAS,CAAC;QACpCmI,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACAmE,oBAAoBA,CAACD,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAKxS,kBAAkB,CAACmP,IAAI;QAC1B,OAAO,MAAM;MACf,KAAKnP,kBAAkB,CAACmO,GAAG;QACzB,OAAO,KAAK;MACd,KAAKnO,kBAAkB,CAACuP,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEAuD,sBAAsBA,CAACC,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAKlT,mBAAmB,CAACmT,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKnT,mBAAmB,CAACoT,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKpT,mBAAmB,CAACqT,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;CACD;AA1+ByBC,UAAA,EAAvBnU,SAAS,CAAC,WAAW,CAAC,C,8DAAyC;AAlOrDiB,4BAA4B,GAAAkT,UAAA,EARxCpU,SAAS,CAAC;EACTqU,QAAQ,EAAE,0BAA0B;EACpCC,WAAW,EAAE,uCAAuC;EACpDC,SAAS,EAAE,CAAC,uCAAuC,CAAC;EACpDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtU,YAAY,EAAED,YAAY,EAAEE,kBAAkB,EAAEI,mBAAmB;CAC9E,CAAC,C,EAEWU,4BAA4B,CA4sCxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}