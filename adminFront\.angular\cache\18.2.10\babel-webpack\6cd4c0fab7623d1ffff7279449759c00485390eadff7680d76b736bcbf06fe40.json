{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nlet NoticeManagementComponent = class NoticeManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _utilityService, _buildCaseService, _specialNoticeFileService, _regularNoticeFileService, _houseService, _specialChangeCustomService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._utilityService = _utilityService;\n    this._buildCaseService = _buildCaseService;\n    this._specialNoticeFileService = _specialNoticeFileService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._houseService = _houseService;\n    this._specialChangeCustomService = _specialChangeCustomService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.pageFirstSales = 1;\n    this.pageSizeSales = 10;\n    this.pageIndexSales = 1;\n    this.totalRecordsSales = 0;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: 1\n    }, {\n      label: '銷售戶',\n      value: 2\n    }];\n    this.typeContentManagementLandowner = {\n      CFormType: 1,\n      CNoticeType: 1\n    };\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.isHouseList = false;\n    this.cExamineStatusOption = ['待審核', '已通過', '已駁回'];\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.getUserBuildCase();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n  }\n  pageChangedSales(newPage) {\n    this.pageIndexSales = newPage;\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = {\n            CName: file.name,\n            CFile: e.target?.result?.toString().split(',')[1],\n            Cimg: file.name.includes('pdf') ? file : file,\n            CFileUpload: file,\n            CFileType: EnumFileType.PDF\n          };\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  onChangeBuildCase() {\n    if (this.selectedCBuildCase.value) {\n      this.getSpecialNoticeFileHouseHoldList();\n      this.getSpecialNoticeFileList();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.selectedCBuildCase = this.userBuildCaseOptions[0];\n        if (this.selectedCBuildCase.value) {\n          this.getSpecialNoticeFileHouseHoldList();\n          this.getSpecialNoticeFileList();\n        }\n      }\n    })).subscribe();\n  }\n  groupByFloor(customerData) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    if (this.selectedCBuildCase.value) {\n      this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.selectedCBuildCase.value,\n          CIsPagi: false\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseListEnable = [...rest];\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []));\n          } else {\n            this.houseList2D = this.groupByFloor([...rest]);\n          }\n          this.isHouseList = true;\n          if (this.saveSpecialNoticeFile) {\n            this.houseList2D = this.updateCIsClick(this.houseList2D, this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses);\n          }\n        }\n      });\n    }\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length && this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      if (this.saveSpecialNoticeFile.selectedCNoticeType.value === item.CHouseType) {\n        // Check if index is valid for this floor\n        item.CIsSelect = checked;\n      }\n    }\n  }\n  getSpecialNoticeFileList() {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\n      body: {\n        CBuildCaseId: this.selectedCBuildCase.value,\n        PageIndexLandLord: this.pageIndex,\n        PageIndexSales: this.pageIndexSales,\n        PageSizeLandLord: this.pageSize,\n        PageSizeSales: this.pageSizeSales\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFile = res.Entries;\n        this.totalRecords = res.Entries.TotalListLandLords || 0;\n        this.totalRecordsSales = res.Entries.TotalListSales || 0;\n      }\n    });\n  }\n  onStatusChange(newStatus) {}\n  getSpecialNoticeFileHouseHoldList() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.selectedCBuildCase.value\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listSpecialNoticeFileHouseHold = res.Entries;\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  updateCIsEnable(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        if (selectedHouses.has(key)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  getSpecialNoticeFileById(item, ref) {\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\n      body: item.CSpecialNoticeFileId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.saveSpecialNoticeFile = {\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\n          CExamineNote: data.CExamineNote,\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter(i => i.CIsSelect),\n          CExamineStauts: data.CExamineStauts\n        };\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\n  }\n  openModel(ref, item) {\n    this.isHouseList = false;\n    this.isNew = true;\n    this.clearImage();\n    this.saveSpecialNoticeFile = {\n      CNoticeType: 1,\n      CBuildCaseId: undefined,\n      CFile: undefined,\n      CHouse: [],\n      CSpecialNoticeFileId: undefined,\n      CIsSelectAll: false,\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\n      CExamineNote: '',\n      tblSpecialNoticeFileHouses: undefined\n    };\n    if (item) {\n      this.isNew = false;\n      this.getSpecialNoticeFileById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      this.dialogService.open(ref);\n    }\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  onDelete(item) {\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\n        body: item.CSpecialNoticeFileId\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.onChangeBuildCase();\n        }\n      });\n    }\n    return item;\n  }\n  getTrueKeys(inputDict) {\n    const trueKeys = [];\n    for (const key in inputDict) {\n      if (inputDict[key]) {\n        trueKeys.push(key);\n      }\n    }\n    return trueKeys;\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveSpecialNoticeFile(ref) {\n    const param = {\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\n      CBuildCaseId: this.selectedCBuildCase.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CHouse: this.flattenAndFilter(this.houseList2D),\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\n    };\n    this.validation(param.CHouse);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getSpecialNoticeFileList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(CHouse) {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.required('[檔案]', '');\n    }\n    if (!(CHouse.length > 0)) {\n      this.valid.required('[適用戶別]', '');\n    }\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote);\n  }\n  getActionName(actionID) {\n    let textR = \"\";\n    if (actionID != undefined) {\n      switch (actionID) {\n        case 1:\n          textR = \"傳送\";\n          break;\n        case 2:\n          textR = \"通過\";\n          break;\n        case 3:\n          textR = \"駁回\";\n          break;\n        default:\n          break;\n      }\n    }\n    return textR;\n  }\n  updateCIsClick(houseList2D, houseSpecialNoticeFile) {\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\n    return houseList2D.map(floorArray => {\n      return floorArray.map(item => {\n        if (selectedHouses.includes(item.CHouseID)) {\n          item.CIsSelect = true;\n        } else {\n          item.CIsSelect = false;\n        }\n        return item;\n      });\n    });\n  }\n};\n__decorate([ViewChild('fileInput')], NoticeManagementComponent.prototype, \"fileInput\", void 0);\nNoticeManagementComponent = __decorate([Component({\n  selector: 'ngx-notice-management',\n  templateUrl: './notice-management.component.html',\n  styleUrls: ['./notice-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, TypeMailPipe, DatePipe, LabelInOptionsPipe]\n})], NoticeManagementComponent);\nexport { NoticeManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "DatePipe", "LabelInOptionsPipe", "TypeMailPipe", "tap", "SharedModule", "BaseComponent", "EnumFileType", "environment", "NoticeManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_utilityService", "_buildCaseService", "_specialNoticeFileService", "_regularNoticeFileService", "_houseService", "_specialChangeCustomService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "pageFirstSales", "pageSizeSales", "pageIndexSales", "totalRecordsSales", "buildCaseOptions", "label", "value", "cNoticeTypeOptions", "typeContentManagementLandowner", "CFormType", "CNoticeType", "fileName", "imageUrl", "undefined", "isHouseList", "cExamineStatusOption", "isNew", "ngOnInit", "getUserBuildCase", "pageChanged", "newPage", "pageChangedSales", "onFileSelected", "event", "file", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "includes", "name", "reader", "FileReader", "onload", "e", "CName", "CFile", "result", "toString", "split", "Cimg", "CFileUpload", "CFileType", "PDF", "fileInput", "nativeElement", "readAsDataURL", "clearImage", "onChangeBuildCase", "selectedCBuildCase", "getSpecialNoticeFileHouseHoldList", "getSpecialNoticeFileList", "apiBuildCaseGetUserBuildCasePost$Json", "body", "pipe", "res", "Entries", "StatusCode", "userBuildCaseOptions", "map", "CBuildCaseName", "cID", "subscribe", "groupByFloor", "customerData", "groupedData", "uniqueFloors", "Array", "from", "Set", "customer", "CFloor", "filter", "floor", "push", "floorIndex", "indexOf", "CIsSelect", "CHouseID", "CID", "CHouseType", "CHouseHold", "CIsEnable", "sortByFloorDescending", "arr", "sort", "a", "b", "getHouseList", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "rest", "houseListEnable", "saveSpecialNoticeFile", "CSpecialNoticeFileId", "houseList2D", "addCIsSelectToA", "tblSpecialNoticeFileHouses", "updateCIsClick", "isCheckAllRowChecked", "row", "every", "item", "isCheckAllColumnChecked", "index", "length", "Error", "floorData", "enableAllAtIndex", "checked", "selectedCNoticeType", "enableAllRow", "apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json", "CBuildCaseId", "PageIndexLandLord", "PageIndexSales", "PageSizeLandLord", "PageSizeSales", "listSpecialNoticeFile", "TotalListLandLords", "TotalListSales", "onStatusChange", "newStatus", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "listSpecialNoticeFileHouseHold", "createArrayObjectFromArray", "c", "matchingItem", "find", "bItem", "CHousehold", "CIsSelected", "getItemByValue", "options", "updateCIsEnable", "houseSpecialNoticeFile", "selectedHouses", "floorArray", "key", "has", "A", "B", "mapB", "Map", "get", "getSpecialNoticeFileById", "ref", "apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json", "data", "CFileUrl", "tblSpecialNoticeFile", "tblExamineLogs", "CExamineNote", "i", "CExamineStauts", "open", "openPdfInNewTab", "window", "BASE_WITHOUT_FILEROOT", "openModel", "CHouse", "CIsSelectAll", "removeBase64Prefix", "base64String", "prefixIndex", "substring", "onDelete", "confirm", "apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json", "showSucessMSG", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputDict", "true<PERSON>eys", "flattenAndFilter", "flattened", "house", "onSaveSpecialNoticeFile", "param", "validation", "errorMessages", "showErrorMSGs", "SaveSpecialNoticeFile", "close", "Message", "onClose", "clear", "required", "getActionName", "actionID", "textR", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\notice-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule, DatePipe } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, RegularNoticeFileService, SpecialNoticeFileService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListRes, GetListHouseHoldRes, GetSpecialNoticeFileListRes, HouseSpecialNoticeFile, TblExamineLog } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { NoticeServiceCustom } from 'src/app/@core/service/notice.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\nexport interface SaveSpecialNoticeFileCus {\r\n  CFileUrl?: string\r\n  CNoticeType?: number\r\n  CBuildCaseId?: number\r\n  CFile?: Blob\r\n  CHouse?: Array<string>\r\n  CSpecialNoticeFileId?: number\r\n  CIsSelectAll?: boolean\r\n  selectedCNoticeType?: any\r\n  CExamineNote?: string | null;\r\n  tblExamineLogs?: TblExamineLog[],\r\n  tblSpecialNoticeFileHouses: any\r\n  CExamineStauts?: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-notice-management',\r\n  templateUrl: './notice-management.component.html',\r\n  styleUrls: ['./notice-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, DatePipe, LabelInOptionsPipe],\r\n})\r\n\r\nexport class NoticeManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _utilityService: UtilityService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _specialNoticeFileService: SpecialNoticeFileService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _houseService: HouseService,\r\n    private _specialChangeCustomService: NoticeServiceCustom\r\n  ) { super(_allow) }\r\n\r\n  override ngOnInit(): void {\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  selectedCBuildCase: any\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  pageFirstSales = 1;\r\n  pageSizeSales = 10;\r\n  pageIndexSales = 1;\r\n  totalRecordsSales = 0;\r\n\r\n\r\n  saveSpecialNoticeFile: SaveSpecialNoticeFileCus\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  cNoticeTypeOptions: any[] = [\r\n    { label: '地主戶', value: 1 },\r\n    { label: '銷售戶', value: 2 }\r\n  ]\r\n  seletectedNoticeType: any\r\n\r\n  typeContentManagementLandowner = {\r\n    CFormType: 1,\r\n    CNoticeType: 1\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n  userBuildCaseOptions: any\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n  }\r\n\r\n  pageChangedSales(newPage: number) {\r\n    this.pageIndexSales = newPage;\r\n  }\r\n\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = {\r\n            CName: file.name,\r\n            CFile: e.target?.result?.toString().split(',')[1],\r\n            Cimg: file.name.includes('pdf') ? file : file,\r\n            CFileUpload: file,\r\n            CFileType: EnumFileType.PDF,\r\n          };\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n  userBuildCaseSelected: any\r\n\r\n  onChangeBuildCase() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this.getSpecialNoticeFileHouseHoldList()\r\n      this.getSpecialNoticeFileList()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.selectedCBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.selectedCBuildCase.value) {\r\n            this.getSpecialNoticeFileHouseHoldList()\r\n            this.getSpecialNoticeFileList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  groupByFloor(customerData: HouseList[]): HouseList[][] {\r\n\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n  isHouseList = false\r\n  houseListEnable: GetHouseListRes[]\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    if (this.selectedCBuildCase.value) {\r\n      this._houseService.apiHouseGetHouseListPost$Json({\r\n        body: {\r\n          CBuildCaseID: this.selectedCBuildCase.value, CIsPagi: false\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.saveSpecialNoticeFile.CSpecialNoticeFileId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses ? [...this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true;\r\n          if (this.saveSpecialNoticeFile) {\r\n            this.houseList2D = this.updateCIsClick(this.houseList2D, this.saveSpecialNoticeFile.tblSpecialNoticeFileHouses)\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length && (this.saveSpecialNoticeFile.selectedCNoticeType.value === floorData[index].CHouseType)) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: HouseList[]) {\r\n    for (const item of row) {\r\n      if ((this.saveSpecialNoticeFile.selectedCNoticeType.value === item.CHouseType)) { // Check if index is valid for this floor\r\n        item.CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  listSpecialNoticeFile: GetSpecialNoticeFileListRes\r\n\r\n  getSpecialNoticeFileList() {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedCBuildCase.value,\r\n        PageIndexLandLord: this.pageIndex,\r\n        PageIndexSales: this.pageIndexSales,\r\n        PageSizeLandLord: this.pageSize,\r\n        PageSizeSales: this.pageSizeSales,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFile = res.Entries\r\n        this.totalRecords = res.Entries.TotalListLandLords || 0\r\n        this.totalRecordsSales = res.Entries.TotalListSales || 0\r\n      }\r\n    })\r\n  }\r\n  listSpecialNoticeFileHouseHold: GetListHouseHoldRes[]\r\n\r\n  houseHoldList: string[]\r\n\r\n  onStatusChange(newStatus: any) {\r\n  }\r\n\r\n  getSpecialNoticeFileHouseHoldList() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.selectedCBuildCase.value\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialNoticeFileHouseHold = res.Entries\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CSpecialNoticeFileHouseholdId: number, CSpecialNoticeFileId: number, CHousehold: string, CIsSelected: boolean }[] | any[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelected);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  }\r\n\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  cExamineStatusOption = ['待審核', '已通過', '已駁回']\r\n\r\n  updateCIsEnable(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = new Set(houseSpecialNoticeFile.map(item => `${item.CHouseHold}-${item.CFloor}`));\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        const key = `${item.CHouseHold}-${item.CFloor}`;\r\n        if (selectedHouses.has(key)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n\r\n  getSpecialNoticeFileById(item: any, ref: any) {\r\n    this._specialNoticeFileService.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json({\r\n      body: item.CSpecialNoticeFileId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.saveSpecialNoticeFile = {\r\n          CFileUrl: data.tblSpecialNoticeFile?.CFileUrl || undefined,\r\n          CNoticeType: data.tblSpecialNoticeFile?.CNoticeType,\r\n          CBuildCaseId: data.tblSpecialNoticeFile?.CBuildCaseId,\r\n          CSpecialNoticeFileId: data.tblSpecialNoticeFile?.CSpecialNoticeFileId,\r\n          selectedCNoticeType: data.tblSpecialNoticeFile?.CNoticeType ? this.getItemByValue(data.tblSpecialNoticeFile?.CNoticeType, this.cNoticeTypeOptions) : this.cNoticeTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs ? data.tblExamineLogs : undefined,\r\n          CExamineNote: data.CExamineNote,\r\n          tblSpecialNoticeFileHouses: data.tblSpecialNoticeFileHouses?.filter((i: any) => i.CIsSelect),\r\n          CExamineStauts: data.CExamineStauts\r\n        }\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) window.open(environment.BASE_WITHOUT_FILEROOT + CFileUrl, '_blank');\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.clearImage()\r\n    this.saveSpecialNoticeFile = {\r\n      CNoticeType: 1,\r\n      CBuildCaseId: undefined,\r\n      CFile: undefined,\r\n      CHouse: [],\r\n      CSpecialNoticeFileId: undefined,\r\n      CIsSelectAll: false,\r\n      selectedCNoticeType: this.cNoticeTypeOptions[0],\r\n      CExamineNote: '',\r\n      tblSpecialNoticeFileHouses: undefined\r\n    }\r\n\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getSpecialNoticeFileById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  onDelete(item: any) {\r\n    if (window.confirm(`確定要刪除【項目${item.CSpecialNoticeFileId}】?`)) {\r\n      this._specialNoticeFileService.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json({\r\n        body: item.CSpecialNoticeFileId\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.onChangeBuildCase()\r\n        }\r\n      })\r\n    }\r\n    return item\r\n  }\r\n\r\n  getTrueKeys(inputDict: { [key: string]: boolean }): string[] {\r\n    const trueKeys: string[] = [];\r\n    for (const key in inputDict) {\r\n      if (inputDict[key]) {\r\n        trueKeys.push(key);\r\n      }\r\n    }\r\n    return trueKeys;\r\n  }\r\n\r\n  flattenAndFilter(data: any[][]): HouseSpecialNoticeFile[] {\r\n    const flattened: HouseSpecialNoticeFile[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable && house.CHouseType === this.saveSpecialNoticeFile.selectedCNoticeType.value) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  onSaveSpecialNoticeFile(ref: any) {\r\n    const param = {\r\n      CNoticeType: this.saveSpecialNoticeFile.selectedCNoticeType.value,\r\n      CBuildCaseId: this.selectedCBuildCase.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CHouse: this.flattenAndFilter(this.houseList2D),\r\n      CSpecialNoticeFileId: this.saveSpecialNoticeFile.CSpecialNoticeFileId || undefined,\r\n      CIsSelectAll: this.saveSpecialNoticeFile.CIsSelectAll ?? false,\r\n      CExamineNote: this.saveSpecialNoticeFile.CExamineNote\r\n    }\r\n\r\n    this.validation(param.CHouse)\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeCustomService.SaveSpecialNoticeFile(param).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getSpecialNoticeFileList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation(CHouse: any[]) {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.required('[檔案]', '')\r\n    }\r\n    if (!(CHouse.length > 0)) {\r\n      this.valid.required('[適用戶別]', '')\r\n    }\r\n    this.valid.required('[送審說明]', this.saveSpecialNoticeFile.CExamineNote)\r\n  }\r\n\r\n  getActionName(actionID: number | undefined) {\r\n    let textR = \"\";\r\n    if (actionID != undefined) {\r\n      switch (actionID) {\r\n        case 1:\r\n          textR = \"傳送\";\r\n          break;\r\n        case 2:\r\n          textR = \"通過\";\r\n          break;\r\n        case 3:\r\n          textR = \"駁回\";\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    return textR;\r\n  }\r\n\r\n\r\n  updateCIsClick(houseList2D: HouseList[][], houseSpecialNoticeFile: HouseSpecialNoticeFile[]): HouseList[][] {\r\n    const selectedHouses = houseSpecialNoticeFile.map(item => item.CHouseID);\r\n    return houseList2D.map(floorArray => {\r\n      return floorArray.map(item => {\r\n        if (selectedHouses.includes(item.CHouseID)) {\r\n          item.CIsSelect = true;\r\n        } else {\r\n          item.CIsSelect = false;\r\n        }\r\n        return item;\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAKxD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,mCAAmC;AAGpF,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,kCAAkC;AAG/D,SAASC,WAAW,QAAQ,8BAA8B;AAkCnD,IAAMC,yBAAyB,GAA/B,MAAMA,yBAA0B,SAAQH,aAAa;EAC1DI,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,yBAAmD,EACnDC,yBAAmD,EACnDC,aAA2B,EAC3BC,2BAAgD;IACtD,KAAK,CAACT,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,2BAA2B,GAA3BA,2BAA2B;IAS5B,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IACzB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IAKrB,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAEtD,KAAAC,kBAAkB,GAAU,CAC1B;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,EAC1B;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAC,CAAE,CAC3B;IAGD,KAAAE,8BAA8B,GAAG;MAC/BC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IAcD,KAAAC,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,QAAQ,GAAQC,SAAS;IAgGzB,KAAAC,WAAW,GAAG,KAAK;IA4HnB,KAAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAoD5C,KAAAC,KAAK,GAAG,IAAI;EA9TM;EAETC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAgCAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACtB,SAAS,GAAGsB,OAAO;EAC1B;EAEAC,gBAAgBA,CAACD,OAAe;IAC9B,IAAI,CAAClB,cAAc,GAAGkB,OAAO;EAC/B;EAOAE,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAASD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MAC9B,IAAI,CAACzC,OAAO,CAAC0C,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IACA,IAAIN,IAAI,EAAE;MACR,MAAMO,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACC,QAAQ,CAACR,IAAI,CAACK,IAAI,CAAC,EAAE;QACpC,IAAI,CAAClB,QAAQ,GAAGa,IAAI,CAACS,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,IAAI,CAACzB,QAAQ,GAAG;YACd0B,KAAK,EAAEd,IAAI,CAACS,IAAI;YAChBM,KAAK,EAAEF,CAAC,CAACZ,MAAM,EAAEe,MAAM,EAAEC,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAEnB,IAAI,CAACS,IAAI,CAACD,QAAQ,CAAC,KAAK,CAAC,GAAGR,IAAI,GAAGA,IAAI;YAC7CoB,WAAW,EAAEpB,IAAI;YACjBqB,SAAS,EAAE/D,YAAY,CAACgE;WACzB;UACD,IAAI,IAAI,CAACC,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAC1C,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACD4B,MAAM,CAACe,aAAa,CAACzB,IAAI,CAAC;MAC5B;IACF;EACF;EAGA0B,UAAUA,CAAA;IACR,IAAI,IAAI,CAACtC,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACoC,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAAC1C,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAGA6C,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACC,kBAAkB,CAAC9C,KAAK,EAAE;MACjC,IAAI,CAAC+C,iCAAiC,EAAE;MACxC,IAAI,CAACC,wBAAwB,EAAE;IACjC;EACF;EAEApC,gBAAgBA,CAAA;IACd,IAAI,CAAC3B,iBAAiB,CAACgE,qCAAqC,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAE,CAAC,CAACC,IAAI,CAC7E9E,GAAG,CAAC+E,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACC,oBAAoB,GAAGH,GAAG,CAACC,OAAO,CAACG,GAAG,CAACJ,GAAG,IAAG;UAChD,OAAO;YACLrD,KAAK,EAAEqD,GAAG,CAACK,cAAc;YACzBzD,KAAK,EAAEoD,GAAG,CAACM;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACZ,kBAAkB,GAAG,IAAI,CAACS,oBAAoB,CAAC,CAAC,CAAC;QACtD,IAAI,IAAI,CAACT,kBAAkB,CAAC9C,KAAK,EAAE;UACjC,IAAI,CAAC+C,iCAAiC,EAAE;UACxC,IAAI,CAACC,wBAAwB,EAAE;QACjC;MACF;IACF,CAAC,CAAC,CACH,CAACW,SAAS,EAAE;EACf;EAEAC,YAAYA,CAACC,YAAyB;IAEpC,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCL,YAAY,CAACL,GAAG,CAACW,QAAQ,IAAIA,QAAQ,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIP,YAAY,EAAE;MAChCD,WAAW,CAACS,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMJ,QAAQ,IAAIN,YAAY,EAAE;MACnC,MAAMW,UAAU,GAAGT,YAAY,CAACU,OAAO,CAACN,QAAQ,CAACC,MAAgB,CAAC;MAClE,IAAII,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBV,WAAW,CAACU,UAAU,CAAC,CAACD,IAAI,CAAC;UAC3BG,SAAS,EAAEP,QAAQ,EAAEO,SAAS,IAAI,KAAK;UACvCC,QAAQ,EAAER,QAAQ,CAACS,GAAG;UACtBC,UAAU,EAAEV,QAAQ,CAACU,UAAU;UAC/BT,MAAM,EAAED,QAAQ,CAACC,MAAM;UACvBU,UAAU,EAAEX,QAAQ,CAACW,UAAU;UAC/BC,SAAS,EAAEZ,QAAQ,CAACY;SACrB,CAAC;MACJ;IACF;IACA,OAAOjB,WAAW;EACpB;EAIAkB,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAChB,MAAM,IAAI,CAAC,KAAKe,CAAC,CAACf,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAiB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACvC,kBAAkB,CAAC9C,KAAK,EAAE;MACjC,IAAI,CAACZ,aAAa,CAACkG,6BAA6B,CAAC;QAC/CpC,IAAI,EAAE;UACJqC,YAAY,EAAE,IAAI,CAACzC,kBAAkB,CAAC9C,KAAK;UAAEwF,OAAO,EAAE;;OAEzD,CAAC,CAAC7B,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;UACtC,MAAMmC,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAAC5B,GAAG,CAACC,OAAO,CAAC;UACpD,IAAI,CAACqC,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;UAChC,IAAI,IAAI,CAACE,qBAAqB,CAACC,oBAAoB,EAAE;YACnD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACjC,YAAY,CAAC,IAAI,CAACkC,eAAe,CAAC,CAAC,GAAG,IAAI,CAACJ,eAAe,CAAC,EAAE,IAAI,CAACC,qBAAqB,CAACI,0BAA0B,GAAG,CAAC,GAAG,IAAI,CAACJ,qBAAqB,CAACI,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;UAChN,CAAC,MAAM;YACL,IAAI,CAACF,WAAW,GAAG,IAAI,CAACjC,YAAY,CAAC,CAAC,GAAG6B,IAAI,CAAC,CAAC;UACjD;UACA,IAAI,CAACjF,WAAW,GAAG,IAAI;UACvB,IAAI,IAAI,CAACmF,qBAAqB,EAAE;YAC9B,IAAI,CAACE,WAAW,GAAG,IAAI,CAACG,cAAc,CAAC,IAAI,CAACH,WAAW,EAAE,IAAI,CAACF,qBAAqB,CAACI,0BAA0B,CAAC;UACjH;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAE,oBAAoBA,CAACC,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAAC1B,SAAS,CAAC;EACjE;EAEA2B,uBAAuBA,CAACC,KAAa;IACnC,IAAI,IAAI,CAAC9F,WAAW,EAAE;MACpB,IAAI8F,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACT,WAAW,CAAC,CAAC,CAAC,CAACU,MAAM,EAAE;QACpD,MAAM,IAAIC,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACZ,WAAW,EAAE;QACxC,IAAIS,KAAK,IAAIG,SAAS,CAACF,MAAM,IAAI,CAACE,SAAS,CAACH,KAAK,CAAC,CAAC5B,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAgC,gBAAgBA,CAACC,OAAgB,EAAEL,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAACZ,WAAW,EAAE;MACxC,IAAIS,KAAK,GAAGG,SAAS,CAACF,MAAM,IAAK,IAAI,CAACZ,qBAAqB,CAACiB,mBAAmB,CAAC5G,KAAK,KAAKyG,SAAS,CAACH,KAAK,CAAC,CAACzB,UAAW,EAAE;QAAE;QACxH4B,SAAS,CAACH,KAAK,CAAC,CAAC5B,SAAS,GAAGiC,OAAO;MACtC;IACF;EACF;EAEAE,YAAYA,CAACF,OAAgB,EAAET,GAAgB;IAC7C,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtB,IAAK,IAAI,CAACP,qBAAqB,CAACiB,mBAAmB,CAAC5G,KAAK,KAAKoG,IAAI,CAACvB,UAAU,EAAG;QAAE;QAChFuB,IAAI,CAAC1B,SAAS,GAAGiC,OAAO;MAC1B;IACF;EACF;EAKA3D,wBAAwBA,CAAA;IACtB,IAAI,CAAC9D,yBAAyB,CAAC4H,qDAAqD,CAAC;MACnF5D,IAAI,EAAE;QACJ6D,YAAY,EAAE,IAAI,CAACjE,kBAAkB,CAAC9C,KAAK;QAC3CgH,iBAAiB,EAAE,IAAI,CAACxH,SAAS;QACjCyH,cAAc,EAAE,IAAI,CAACrH,cAAc;QACnCsH,gBAAgB,EAAE,IAAI,CAAC3H,QAAQ;QAC/B4H,aAAa,EAAE,IAAI,CAACxH;;KAEvB,CAAC,CAACgE,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC8D,qBAAqB,GAAGhE,GAAG,CAACC,OAAO;QACxC,IAAI,CAAC5D,YAAY,GAAG2D,GAAG,CAACC,OAAO,CAACgE,kBAAkB,IAAI,CAAC;QACvD,IAAI,CAACxH,iBAAiB,GAAGuD,GAAG,CAACC,OAAO,CAACiE,cAAc,IAAI,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;EAKAC,cAAcA,CAACC,SAAc,GAC7B;EAEAzE,iCAAiCA,CAAA;IAC/B,IAAI,CAAC5D,yBAAyB,CAACsI,8DAA8D,CAAC;MAC5FvE,IAAI,EAAE,IAAI,CAACJ,kBAAkB,CAAC9C;KAC/B,CAAC,CAAC2D,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoE,8BAA8B,GAAGtE,GAAG,CAACC,OAAO;MACnD;IACF,CAAC,CAAC;EACJ;EAEAsE,0BAA0BA,CAACxC,CAAW,EAAEC,CAA8H;IACpK,MAAMwC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMxB,IAAI,IAAIjB,CAAC,EAAE;MACpB,MAAM0C,YAAY,GAAGzC,CAAC,CAAC0C,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK5B,IAAI,IAAI2B,KAAK,CAACE,WAAW,CAAC;MACpFL,CAAC,CAACxB,IAAI,CAAC,GAAG,CAAC,CAACyB,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV;EAGAM,cAAcA,CAAClI,KAAU,EAAEmI,OAAc;IACvC,KAAK,MAAM/B,IAAI,IAAI+B,OAAO,EAAE;MAC1B,IAAI/B,IAAI,CAACpG,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOoG,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAIAgC,eAAeA,CAACvC,WAA0B,EAAEwC,sBAAgD;IAC1F,MAAMC,cAAc,GAAG,IAAIpE,GAAG,CAACmE,sBAAsB,CAAC7E,GAAG,CAAC4C,IAAI,IAAI,GAAGA,IAAI,CAACtB,UAAU,IAAIsB,IAAI,CAAChC,MAAM,EAAE,CAAC,CAAC;IACvG,OAAOyB,WAAW,CAACrC,GAAG,CAAC+E,UAAU,IAAG;MAClC,OAAOA,UAAU,CAAC/E,GAAG,CAAC4C,IAAI,IAAG;QAC3B,MAAMoC,GAAG,GAAG,GAAGpC,IAAI,CAACtB,UAAU,IAAIsB,IAAI,CAAChC,MAAM,EAAE;QAC/C,IAAIkE,cAAc,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;UAC3BpC,IAAI,CAAC1B,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACL0B,IAAI,CAAC1B,SAAS,GAAG,KAAK;QACxB;QACA,OAAO0B,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAN,eAAeA,CAAC4C,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAACnF,GAAG,CAAC4C,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACtB,UAAU,IAAIsB,IAAI,CAAChC,MAAM,EAAE,EAAEgC,IAAI,CAAC1B,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOgE,CAAC,CAAClF,GAAG,CAAC4C,IAAI,IAAG;MAClB,MAAMoC,GAAG,GAAG,GAAGpC,IAAI,CAACtB,UAAU,IAAIsB,IAAI,CAAChC,MAAM,EAAE;MAC/C,OAAO;QACL,GAAGgC,IAAI;QACP1B,SAAS,EAAEkE,IAAI,CAACH,GAAG,CAACD,GAAG,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACN,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAGAO,wBAAwBA,CAAC3C,IAAS,EAAE4C,GAAQ;IAC1C,IAAI,CAAC9J,yBAAyB,CAAC+J,qDAAqD,CAAC;MACnF/F,IAAI,EAAEkD,IAAI,CAACR;KACZ,CAAC,CAACjC,SAAS,CAACP,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAM4F,IAAI,GAAG9F,GAAG,CAACC,OAAO;QACxB,IAAI,CAACsC,qBAAqB,GAAG;UAC3BwD,QAAQ,EAAED,IAAI,CAACE,oBAAoB,EAAED,QAAQ,IAAI5I,SAAS;UAC1DH,WAAW,EAAE8I,IAAI,CAACE,oBAAoB,EAAEhJ,WAAW;UACnD2G,YAAY,EAAEmC,IAAI,CAACE,oBAAoB,EAAErC,YAAY;UACrDnB,oBAAoB,EAAEsD,IAAI,CAACE,oBAAoB,EAAExD,oBAAoB;UACrEgB,mBAAmB,EAAEsC,IAAI,CAACE,oBAAoB,EAAEhJ,WAAW,GAAG,IAAI,CAAC8H,cAAc,CAACgB,IAAI,CAACE,oBAAoB,EAAEhJ,WAAW,EAAE,IAAI,CAACH,kBAAkB,CAAC,GAAG,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAAC;UAC/KoJ,cAAc,EAAEH,IAAI,CAACG,cAAc,GAAGH,IAAI,CAACG,cAAc,GAAG9I,SAAS;UACrE+I,YAAY,EAAEJ,IAAI,CAACI,YAAY;UAC/BvD,0BAA0B,EAAEmD,IAAI,CAACnD,0BAA0B,EAAE1B,MAAM,CAAEkF,CAAM,IAAKA,CAAC,CAAC7E,SAAS,CAAC;UAC5F8E,cAAc,EAAEN,IAAI,CAACM;SACtB;QACD,IAAI,CAACnE,YAAY,EAAE;QACnB,IAAI,CAACxG,aAAa,CAAC4K,IAAI,CAACT,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAU,eAAeA,CAACP,QAAc;IAC5B,IAAIA,QAAQ,EAAEQ,MAAM,CAACF,IAAI,CAAChL,WAAW,CAACmL,qBAAqB,GAAGT,QAAQ,EAAE,QAAQ,CAAC;EACnF;EAEAU,SAASA,CAACb,GAAQ,EAAE5C,IAAU;IAC5B,IAAI,CAAC5F,WAAW,GAAG,KAAK;IACxB,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACkC,UAAU,EAAE;IACjB,IAAI,CAAC+C,qBAAqB,GAAG;MAC3BvF,WAAW,EAAE,CAAC;MACd2G,YAAY,EAAExG,SAAS;MACvB0B,KAAK,EAAE1B,SAAS;MAChBuJ,MAAM,EAAE,EAAE;MACVlE,oBAAoB,EAAErF,SAAS;MAC/BwJ,YAAY,EAAE,KAAK;MACnBnD,mBAAmB,EAAE,IAAI,CAAC3G,kBAAkB,CAAC,CAAC,CAAC;MAC/CqJ,YAAY,EAAE,EAAE;MAChBvD,0BAA0B,EAAExF;KAC7B;IAED,IAAI6F,IAAI,EAAE;MACR,IAAI,CAAC1F,KAAK,GAAG,KAAK;MAClB,IAAI,CAACqI,wBAAwB,CAAC3C,IAAI,EAAE4C,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACtI,KAAK,GAAG,IAAI;MACjB,IAAI,CAAC2E,YAAY,EAAE;MACnB,IAAI,CAACxG,aAAa,CAAC4K,IAAI,CAACT,GAAG,CAAC;IAC9B;EACF;EAEAgB,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACxF,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAIyF,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACE,SAAS,CAACD,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAG,QAAQA,CAAChE,IAAS;IAChB,IAAIuD,MAAM,CAACU,OAAO,CAAC,WAAWjE,IAAI,CAACR,oBAAoB,IAAI,CAAC,EAAE;MAC5D,IAAI,CAAC1G,yBAAyB,CAACoL,oDAAoD,CAAC;QAClFpH,IAAI,EAAEkD,IAAI,CAACR;OACZ,CAAC,CAACjC,SAAS,CAACP,GAAG,IAAG;QACjB,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACxE,OAAO,CAACyL,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAAC1H,iBAAiB,EAAE;QAC1B;MACF,CAAC,CAAC;IACJ;IACA,OAAOuD,IAAI;EACb;EAEAoE,WAAWA,CAACC,SAAqC;IAC/C,MAAMC,QAAQ,GAAa,EAAE;IAC7B,KAAK,MAAMlC,GAAG,IAAIiC,SAAS,EAAE;MAC3B,IAAIA,SAAS,CAACjC,GAAG,CAAC,EAAE;QAClBkC,QAAQ,CAACnG,IAAI,CAACiE,GAAG,CAAC;MACpB;IACF;IACA,OAAOkC,QAAQ;EACjB;EAEAC,gBAAgBA,CAACzB,IAAa;IAC5B,MAAM0B,SAAS,GAA6B,EAAE;IAC9C,KAAK,MAAMnE,SAAS,IAAIyC,IAAI,EAAE;MAC5B,KAAK,MAAM2B,KAAK,IAAIpE,SAAS,EAAE;QAC7B,IAAIoE,KAAK,CAACnG,SAAS,IAAImG,KAAK,CAAC9F,SAAS,IAAI8F,KAAK,CAAChG,UAAU,KAAK,IAAI,CAACc,qBAAqB,CAACiB,mBAAmB,CAAC5G,KAAK,EAAE;UACnH4K,SAAS,CAACrG,IAAI,CAAC;YACbI,QAAQ,EAAEkG,KAAK,CAAClG,QAAQ;YACxBD,SAAS,EAAEmG,KAAK,CAACnG;WAClB,CAAC;QACJ;MACF;IACF;IACA,OAAOkG,SAAS;EAClB;EAEAE,uBAAuBA,CAAC9B,GAAQ;IAC9B,MAAM+B,KAAK,GAAG;MACZ3K,WAAW,EAAE,IAAI,CAACuF,qBAAqB,CAACiB,mBAAmB,CAAC5G,KAAK;MACjE+G,YAAY,EAAE,IAAI,CAACjE,kBAAkB,CAAC9C,KAAK;MAC3CiC,KAAK,EAAE,IAAI,CAAC3B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACgC,WAAW,GAAG/B,SAAS;MAC5DuJ,MAAM,EAAE,IAAI,CAACa,gBAAgB,CAAC,IAAI,CAAC9E,WAAW,CAAC;MAC/CD,oBAAoB,EAAE,IAAI,CAACD,qBAAqB,CAACC,oBAAoB,IAAIrF,SAAS;MAClFwJ,YAAY,EAAE,IAAI,CAACpE,qBAAqB,CAACoE,YAAY,IAAI,KAAK;MAC9DT,YAAY,EAAE,IAAI,CAAC3D,qBAAqB,CAAC2D;KAC1C;IAED,IAAI,CAAC0B,UAAU,CAACD,KAAK,CAACjB,MAAM,CAAC;IAE7B,IAAI,IAAI,CAAC/K,KAAK,CAACkM,aAAa,CAAC1E,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACzH,OAAO,CAACoM,aAAa,CAAC,IAAI,CAACnM,KAAK,CAACkM,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC5L,2BAA2B,CAAC8L,qBAAqB,CAACJ,KAAK,CAAC,CAACpH,SAAS,CAACP,GAAG,IAAG;MAC5E,IAAIA,GAAG,IAAIA,GAAG,CAACF,IAAK,IAAIE,GAAG,CAACF,IAAI,CAACI,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAACxE,OAAO,CAACyL,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC3H,UAAU,EAAE;QACjB,IAAI,CAACI,wBAAwB,EAAE;QAC/BgG,GAAG,CAACoC,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACtM,OAAO,CAAC0C,YAAY,CAAC4B,GAAG,IAAIA,GAAG,CAACF,IAAI,IAAIE,GAAG,CAACF,IAAI,CAACmI,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAGAC,OAAOA,CAACtC,GAAQ;IACdA,GAAG,CAACoC,KAAK,EAAE;EACb;EAGAJ,UAAUA,CAAClB,MAAa;IACtB,IAAI,CAAC/K,KAAK,CAACwM,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC7K,KAAK,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;MAChC,IAAI,CAACvB,KAAK,CAACyM,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;IACjC;IACA,IAAI,EAAE1B,MAAM,CAACvD,MAAM,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACxH,KAAK,CAACyM,QAAQ,CAAC,QAAQ,EAAE,EAAE,CAAC;IACnC;IACA,IAAI,CAACzM,KAAK,CAACyM,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC7F,qBAAqB,CAAC2D,YAAY,CAAC;EACxE;EAEAmC,aAAaA,CAACC,QAA4B;IACxC,IAAIC,KAAK,GAAG,EAAE;IACd,IAAID,QAAQ,IAAInL,SAAS,EAAE;MACzB,QAAQmL,QAAQ;QACd,KAAK,CAAC;UACJC,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF,KAAK,CAAC;UACJA,KAAK,GAAG,IAAI;UACZ;QACF;UACE;MACJ;IACF;IACA,OAAOA,KAAK;EACd;EAGA3F,cAAcA,CAACH,WAA0B,EAAEwC,sBAAgD;IACzF,MAAMC,cAAc,GAAGD,sBAAsB,CAAC7E,GAAG,CAAC4C,IAAI,IAAIA,IAAI,CAACzB,QAAQ,CAAC;IACxE,OAAOkB,WAAW,CAACrC,GAAG,CAAC+E,UAAU,IAAG;MAClC,OAAOA,UAAU,CAAC/E,GAAG,CAAC4C,IAAI,IAAG;QAC3B,IAAIkC,cAAc,CAAC5G,QAAQ,CAAC0E,IAAI,CAACzB,QAAQ,CAAC,EAAE;UAC1CyB,IAAI,CAAC1B,SAAS,GAAG,IAAI;QACvB,CAAC,MAAM;UACL0B,IAAI,CAAC1B,SAAS,GAAG,KAAK;QACxB;QACA,OAAO0B,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;CACD;AAlbyBwF,UAAA,EAAvB5N,SAAS,CAAC,WAAW,CAAC,C,2DAAwB;AAxDpCU,yBAAyB,GAAAkN,UAAA,EARrC7N,SAAS,CAAC;EACT8N,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChO,YAAY,EAAEK,YAAY,EAAEF,YAAY,EAAEF,QAAQ,EAAEC,kBAAkB;CACjF,CAAC,C,EAEWO,yBAAyB,CA0erC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}