{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseFileSaveBuildCaseFilePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveBuildCaseFilePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'multipart/form-data');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseFileSaveBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/SaveBuildCaseFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseFileSaveBuildCaseFilePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\build-case-file\\api-build-case-file-save-build-case-file-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { StringResponseBase } from '../../models/string-response-base';\r\n\r\nexport interface ApiBuildCaseFileSaveBuildCaseFilePost$Plain$Params {\r\n      body?: {\r\n'CBuildCaseID'?: number;\r\n'CBuildCaseFileID'?: number;\r\n'CStatus'?: number;\r\n'CFile'?: Blob;\r\n'CCategoryName'?: string;\r\n'CSubmitRemark'?: string;\r\n}\r\n}\r\n\r\nexport function apiBuildCaseFileSaveBuildCaseFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseFileSaveBuildCaseFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseFileSaveBuildCaseFilePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'multipart/form-data');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<StringResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseFileSaveBuildCaseFilePost$Plain.PATH = '/api/BuildCaseFile/SaveBuildCaseFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAetD,OAAM,SAAUC,2CAA2CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA2D,EAAEC,OAAqB;EAC/K,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,2CAA2C,CAACM,IAAI,EAAE,MAAM,CAAC;EAChG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,qBAAqB,CAAC;EAC7C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2C;EACpD,CAAC,CAAC,CACH;AACH;AAEAb,2CAA2C,CAACM,IAAI,GAAG,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}