import { Component, OnInit, TemplateRef, } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NbDialogService } from '@nebular/theme';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { BuildCaseService, MaterialService } from 'src/services/api/services';
import { BuildCaseGetListReponse, SaveMaterialArgs, GetMaterialListResponse } from 'src/services/api/models';
import { finalize, mergeMap, tap } from 'rxjs';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { UtilityService } from 'src/app/shared/services/utility.service';
import * as XLSX from 'xlsx';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';

@Component({
  selector: 'ngx-building-material',
  templateUrl: './building-material.component.html',
  styleUrls: ['./building-material.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule],
})

export class BuildingMaterialComponent extends BaseComponent implements OnInit {
  isNew = true

  materialList: GetMaterialListResponse[]
  selectedMaterial: GetMaterialListResponse

  listBuildCases: BuildCaseGetListReponse[] = []
  selectedBuildCaseId: number

  materialOptions = [
    {
      value: null,
      label: '全部',
    },
    {
      value: false,
      label: '方案',
    },
    {
      value: true,
      label: '選樣',
    }
  ]
  materialOptionsId = null
  CSelectName: string = ""
  CImageCode: string = ""
  CInfoImageCode: string = ""
  ShowPrice: boolean = false
  currentImageShowing: string = ""
  filterMapping: boolean = false
  CIsMapping: boolean = true

  constructor(
    private _allow: AllowHelper,
    private dialogService: NbDialogService,
    private message: MessageService,
    private valid: ValidationHelper,
    private _buildCaseService: BuildCaseService,
    private _materialService: MaterialService,
    private _utilityService: UtilityService
  ) {
    super(_allow)
  }

  override ngOnInit(): void {
    this.getListBuildCase()
  }

  getListBuildCase() {
    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({
      body: {
        CIsPagi: false,
        CStatus: 1,
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode == 0) {
            this.listBuildCases = res.Entries?.length ? res.Entries : []
            this.selectedBuildCaseId = this.listBuildCases[0].cID!
          }
        }),
        mergeMap(() => this.getMaterialList())
      ).subscribe()
  }

  getMaterialList(pageIndex: number = 1) {
    return this._materialService.apiMaterialGetMaterialListPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CPlanUse: this.materialOptionsId,
        CSelectName: this.CSelectName,
        CImageCode: this.CImageCode,
        PageSize: this.pageSize,
        PageIndex: pageIndex,
        CInfoImageCode: this.CInfoImageCode,
        CIsMapping: this.CIsMapping
      }
    }).pipe(
      tap(res => {
        if (res.StatusCode == 0) {
          this.materialList = res.Entries! ?? []
          this.totalRecords = res.TotalItems!

          if(this.materialList.length > 0) {
            this.ShowPrice = this.materialList[0].CShowPrice!;
          }
        }
      })
    )
  }

  search() {
    this.getMaterialList().subscribe()
  }

  pageChanged(pageIndex: number) {
    this.getMaterialList(pageIndex).subscribe()
  }

  exportExelMaterialList() {
    this._materialService.apiMaterialExportExcelMaterialListPost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }

  exportExelMaterialTemplate() {
    this._materialService.apiMaterialExportExcelMaterialTemplatePost$Json({
      body: this.selectedBuildCaseId
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        if (res.Entries!.FileByte) {
          this._utilityService.downloadExcelFile(res.Entries!.FileByte, '建材管理')
        }
      }
    })
  }

  addNew(ref: any) {
    this.isNew = true
    this.selectedMaterial = {}
    this.dialogService.open(ref)
  }

  onSelectedMaterial(data: GetMaterialListResponse, ref: any) {
    this.isNew = false
    this.selectedMaterial = { ...data }
    this.dialogService.open(ref)
  }

  validation() {
    this.valid.clear();
    this.valid.required('[名稱]', this.selectedMaterial.CName)
    this.valid.required('[項目]', this.selectedMaterial.CPart)
    this.valid.required('[位置]', this.selectedMaterial.CLocation)
    this.valid.required('[建材選項名稱]', this.selectedMaterial.CSelectName)
    this.valid.required('[建材圖片編號]', this.selectedMaterial.CImageCode)
    this.valid.isStringMaxLength('[名稱]', this.selectedMaterial.CName, 30)
    this.valid.isStringMaxLength('[項目]', this.selectedMaterial.CPart, 30)
    this.valid.isStringMaxLength('[位置]', this.selectedMaterial.CLocation, 30)
    this.valid.isStringMaxLength('[建材選項名稱]', this.selectedMaterial.CSelectName, 30)
    this.valid.isStringMaxLength('[建材圖片編號]', this.selectedMaterial.CImageCode, 30)
  }

  onSubmit(ref: any) {
    this.validation()
    if (this.valid.errorMessages.length > 0) {
      this.message.showErrorMSGs(this.valid.errorMessages);
      return
    }
    this._materialService.apiMaterialSaveMaterialAdminPost$Json({
      body: {
        CBuildCaseId: this.selectedBuildCaseId,
        CImageCode: this.selectedMaterial.CImageCode,
        CName: this.selectedMaterial.CName,
        CPart: this.selectedMaterial.CPart,
        CLocation: this.selectedMaterial.CLocation,
        CSelectName: this.selectedMaterial.CSelectName,
        CDescription: this.selectedMaterial.CDescription,
        CMaterialId: this.isNew ? null : this.selectedMaterial.CId!,
        CPrice: this.selectedMaterial.CPrice
      }
    })
      .pipe(
        tap(res => {
          if (res.StatusCode === 0) {
            this.message.showSucessMSG("執行成功");
          } else {
            this.message.showErrorMSG(res.Message!);
          }
        }),
        mergeMap(() => this.getMaterialList()),
        finalize(() => ref.close())
      ).subscribe()
  }

  onClose(ref: any) {
    ref.close();
  }

  detectFileExcel(event: any) {
    const target: DataTransfer = <DataTransfer>(event.target);
    const reader: FileReader = new FileReader();
    reader.readAsBinaryString(target.files[0]);
    reader.onload = (e: any) => {
      const binarystr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(binarystr, { type: 'binary' });

      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      let isValidFile: boolean = true;
      const data = XLSX.utils.sheet_to_json(ws);
      if (data && data.length > 0) {
        data.forEach((x: any) => {
          if (!(x['名稱'] && x['項目'] && x['位置'] && x['建材選項名稱'] &&
            (x['建材圖片檔名'] || x['建材說明'] || x['示意圖片檔名']))) {
            isValidFile = false;
          }
        })

        if (!isValidFile) {
          this.message.showErrorMSG("导入文件时出现错误")
        } else {
          this._materialService.apiMaterialImportExcelMaterialListPost$Json({
            body: {
              CBuildCaseId: this.selectedBuildCaseId,
              CFile: target.files[0]
            }
          }).pipe(
            tap(res => {
              if (res.StatusCode == 0) {
                this.message.showSucessMSG("執行成功")
              } else {
                this.message.showErrorMSG(res.Message!)
              }
            }),
            mergeMap(() => this.getMaterialList(1))
          ).subscribe();
        }
      } else {
        this.message.showErrorMSG("匯入的檔案內容為空，請檢查檔案並重新上傳。")
      }
      event.target.value = null;
    };
  }

  showImage(imageUrl: string, dialog: TemplateRef<any>){
    this.currentImageShowing = imageUrl;
    this.dialogService.open(dialog);
  }

  changeFilter(){
    if(this.filterMapping){
      this.CIsMapping = false;
      this.getMaterialList().subscribe();
    }
    else{
      this.CIsMapping = true;
      this.getMaterialList().subscribe();
    }
  }
}
