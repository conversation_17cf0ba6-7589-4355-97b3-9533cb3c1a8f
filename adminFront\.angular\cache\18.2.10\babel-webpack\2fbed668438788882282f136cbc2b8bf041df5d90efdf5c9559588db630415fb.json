{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nlet EditSettingTimePeriodComponent = class EditSettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, route, _location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.route = route;\n    this._location = _location;\n    this._eventService = _eventService;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    this.isHouseList = false;\n    this.buildCaseId = this.route.snapshot.paramMap.get('id');\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.buildCaseId,\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getHouseChangeDate();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      console.log({\n        item\n      });\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit() {\n    const bodyReq = this.convertData();\n    this.validation(bodyReq);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.goBack();\n      }\n    });\n  }\n  convertData() {\n    return this.convertedHouseArray.flat().filter(item => item.isChecked && item.CHouseId !== null) // Lọc các item theo điều kiện\n    .map(item => ({\n      ...item,\n      CChangeStartDate: this.formatDate(this.searchQuery.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.searchQuery.CChangeEndDate)\n    }));\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Khởi tạo dictionary để gom nhóm các phần tử theo CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // Nếu CFloor chưa có trong dictionary thì khởi tạo danh sách rỗng\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate,\n          isChecked: false\n        });\n      });\n    });\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  getHouseChangeDate() {\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (this.houseChangeDates) {\n          this.getFloorsAndHouseholds(this.houseChangeDates);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(this.houseChangeDates);\n          console.log('convertedHouseArray', this.convertedHouseArray);\n        }\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(bodyReq) {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.searchQuery.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.searchQuery.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.searchQuery.CChangeStartDate, this.searchQuery.CChangeEndDate);\n    if (bodyReq && bodyReq.length) {\n      return;\n    } else {\n      this.valid.required('[ 勾選適用戶型 ]', '');\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this._location.back();\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.isChecked || !item.CHouseId);\n  }\n  isCheckAllColumnChecked(index) {\n    if (index < 0 || index >= this.convertedHouseArray[0].length) {\n      throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n    }\n    for (const floorData of this.convertedHouseArray) {\n      if (floorData[index].CHouseId) {\n        if (index >= floorData.length || !floorData[index].isChecked) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.convertedHouseArray) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        if (floorData[index].CHouseId) {\n          floorData[index].isChecked = checked;\n        }\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.isChecked = checked;\n    }\n  }\n};\nEditSettingTimePeriodComponent = __decorate([Component({\n  selector: 'ngx-edit-setting-time-period',\n  templateUrl: './edit-setting-time-period.component.html',\n  styleUrls: ['./edit-setting-time-period.component.scss'],\n  standalone: true,\n  providers: [],\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe]\n})], EditSettingTimePeriodComponent);\nexport { EditSettingTimePeriodComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "NbDatepickerModule", "NbDateFnsDateModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "EditSettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "route", "_location", "_eventService", "buildCaseOptions", "label", "value", "isStatus", "isHouseList", "buildCaseId", "snapshot", "paramMap", "get", "selectedHouseChangeDate", "CChangeStartDate", "CChangeEndDate", "CFloor", "undefined", "CHouseHold", "CHouseId", "ngOnInit", "searchQuery", "CBuildCaseSelected", "getHouseChangeDate", "openModel", "ref", "item", "console", "log", "Date", "open", "formatDate", "CChangeDate", "format", "onSubmit", "bodyReq", "convertData", "validation", "errorMessages", "length", "showErrorMSGs", "apiHouseSaveHouseChangeDatePost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "goBack", "convertedHouseArray", "flat", "filter", "isChecked", "map", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "Entries", "houseChangeDates", "onClose", "close", "clear", "required", "checkStartBeforeEnd", "action", "payload", "back", "isCheckAllRowChecked", "row", "every", "isCheckAllColumnChecked", "index", "Error", "floorData", "enableAllAtIndex", "checked", "enableAllRow", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "providers", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\edit-setting-time-period\\edit-setting-time-period.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string;\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  isChecked?: boolean\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-edit-setting-time-period',\r\n  templateUrl: './edit-setting-time-period.component.html',\r\n  styleUrls: ['./edit-setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class EditSettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private route: ActivatedRoute,\r\n    private _location: Location,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n    this.buildCaseId = this.route.snapshot.paramMap.get('id')\r\n\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n  }\r\n  buildCaseId: any | null\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.buildCaseId,\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getHouseChangeDate()\r\n  }\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      console.log({ item });\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit() {\r\n    const bodyReq = this.convertData()\r\n    this.validation(bodyReq)\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  convertData() {\r\n    return this.convertedHouseArray.flat()\r\n      .filter(item => item.isChecked && item.CHouseId !== null) // Lọc các item theo điều kiện\r\n      .map(item => ({\r\n        ...item,\r\n        CChangeStartDate: this.formatDate(this.searchQuery.CChangeStartDate),\r\n        CChangeEndDate: this.formatDate(this.searchQuery.CChangeEndDate)\r\n      }));\r\n\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Khởi tạo dictionary để gom nhóm các phần tử theo CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // Nếu CFloor chưa có trong dictionary thì khởi tạo danh sách rỗng\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Thêm phần tử vào danh sách của CFloor tương ứng\r\n          CHouseHold: household.CHouseHold,\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate,\r\n          isChecked: false\r\n        });\r\n      });\r\n    });\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (this.houseChangeDates) {\r\n          this.getFloorsAndHouseholds(this.houseChangeDates)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(this.houseChangeDates)\r\n          console.log('convertedHouseArray', this.convertedHouseArray);\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation(bodyReq?: any) {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.searchQuery.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.searchQuery.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.searchQuery.CChangeStartDate, this.searchQuery.CChangeEndDate)\r\n    if (bodyReq && bodyReq.length) {\r\n      return\r\n    } else {\r\n      this.valid.required('[ 勾選適用戶型 ]', '')\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { isChecked: any; CHouseId: any }) => item.isChecked || !item.CHouseId);\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (index < 0 || index >= this.convertedHouseArray[0].length) {\r\n      throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n    }\r\n    for (const floorData of this.convertedHouseArray) {\r\n      if (floorData[index].CHouseId) {\r\n        if (index >= floorData.length || !floorData[index].isChecked) {\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n    return true;\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.convertedHouseArray) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        if (floorData[index].CHouseId) {\r\n          floorData[index].isChecked = checked;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean | any, row: any[]) {\r\n    for (const item of row) {\r\n      item.isChecked = checked;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAMvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAgCpE,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQD,aAAa;EAC/DE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,KAAqB,EACrBC,SAAmB,EACnBC,aAA2B;IAEnC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IA2BvB,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAC,QAAQ,GAAY,IAAI;IAkKxB,KAAAC,WAAW,GAAG,KAAK;IA9LjB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAGzD,IAAI,CAACC,uBAAuB,GAAG;MAC7BC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAEC,SAAS;MACjBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAEF;KACX;EACH;EAyBSG,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACb,WAAW;MACpCK,gBAAgB,EAAEG,SAAS;MAC3BF,cAAc,EAAEE;KACjB;IACD,IAAI,CAACM,kBAAkB,EAAE;EAC3B;EAGAC,SAASA,CAACC,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACP,QAAQ,EAAE;MACjBQ,OAAO,CAACC,GAAG,CAAC;QAAEF;MAAI,CAAE,CAAC;MACrB,IAAI,CAACb,uBAAuB,GAAG;QAC7B,GAAGa,IAAI;QACPZ,gBAAgB,EAAEY,IAAI,CAACZ,gBAAgB,GAAG,IAAIe,IAAI,CAACH,IAAI,CAACZ,gBAAgB,CAAC,GAAGG,SAAS;QACrFF,cAAc,EAAEW,IAAI,CAACX,cAAc,GAAG,IAAIc,IAAI,CAACH,IAAI,CAACX,cAAc,CAAC,GAAGE;OACvE;MACD,IAAI,CAACrB,aAAa,CAACkC,IAAI,CAACL,GAAG,CAAC;IAC9B;EACF;EAEAM,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO1C,MAAM,CAAC0C,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE;IAClC,IAAI,CAACC,UAAU,CAACF,OAAO,CAAC;IACxB,IAAI,IAAI,CAACrC,KAAK,CAACwC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC1C,OAAO,CAAC2C,aAAa,CAAC,IAAI,CAAC1C,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACvC,aAAa,CAAC0C,oCAAoC,CAAC;MACtDC,IAAI,EAAEP;KACP,CAAC,CAACQ,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAChD,OAAO,CAACiD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAX,WAAWA,CAAA;IACT,OAAO,IAAI,CAACY,mBAAmB,CAACC,IAAI,EAAE,CACnCC,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACyB,SAAS,IAAIzB,IAAI,CAACP,QAAQ,KAAK,IAAI,CAAC,CAAC;IAAA,CACzDiC,GAAG,CAAC1B,IAAI,KAAK;MACZ,GAAGA,IAAI;MACPZ,gBAAgB,EAAE,IAAI,CAACiB,UAAU,CAAC,IAAI,CAACV,WAAW,CAACP,gBAAgB,CAAC;MACpEC,cAAc,EAAE,IAAI,CAACgB,UAAU,CAAC,IAAI,CAACV,WAAW,CAACN,cAAc;KAChE,CAAC,CAAC;EAEP;EAEAsC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAAC3C,MAAM;QAC1B,IAAI,CAACuC,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpB3C,UAAU,EAAEuC,SAAS,CAACvC,UAAU;UAChCC,QAAQ,EAAEwC,KAAK,CAACxC,QAAQ;UACxBH,MAAM,EAAE2C,KAAK,CAAC3C,MAAM;UACpBF,gBAAgB,EAAE6C,KAAK,CAAC7C,gBAAgB;UACxCC,cAAc,EAAE4C,KAAK,CAAC5C,cAAc;UACpCoC,SAAS,EAAE;SACZ,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACW,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAACV,GAAG,CAAEQ,KAAU,IAAI;MAChE,OAAO,IAAI,CAACO,UAAU,CAACf,GAAG,CAAEK,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACQ,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAACnD,UAAU,KAAKuC,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdzC,UAAU,EAAEuC,SAAS;UACrBtC,QAAQ,EAAE,IAAI;UACdH,MAAM,EAAE4C,KAAK;UACb9C,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOmD,MAAM;EACf;EAEAI,sBAAsBA,CAAChB,GAAU;IAC/B,MAAMiB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ClB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAACvC,UAAU,CAAC;MACvCuC,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCY,SAAS,CAACG,GAAG,CAACf,KAAK,CAAC3C,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC8C,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAlD,kBAAkBA,CAAA;IAChB,IAAI,CAACxB,aAAa,CAAC8E,mCAAmC,CAAC;MACrDnC,IAAI,EAAE;QACJoC,YAAY,EAAE,IAAI,CAACrE;;KAEtB,CAAC,CAACkC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACmC,OAAO,IAAInC,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACmC,gBAAgB,GAAGpC,GAAG,CAACmC,OAAO,GAAGnC,GAAG,CAACmC,OAAO,GAAG,EAAE;QACtD,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACzB,IAAI,CAACV,sBAAsB,CAAC,IAAI,CAACU,gBAAgB,CAAC;UAClD,IAAI,CAAChC,mBAAmB,GAAG,IAAI,CAACK,8BAA8B,CAAC,IAAI,CAAC2B,gBAAgB,CAAC;UACrFrD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACoB,mBAAmB,CAAC;QAC9D;MACF;IACF,CAAC,CAAC;EACJ;EAEAiC,OAAOA,CAACxD,GAAQ;IACdA,GAAG,CAACyD,KAAK,EAAE;EACb;EAEA7C,UAAUA,CAACF,OAAa;IACtB,IAAI,CAACrC,KAAK,CAACqF,KAAK,EAAE;IAClB,IAAI,CAACrF,KAAK,CAACsF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC/D,WAAW,CAACP,gBAAgB,CAAC;IAClE,IAAI,CAAChB,KAAK,CAACsF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC/D,WAAW,CAACN,cAAc,CAAC;IAChE,IAAI,CAACjB,KAAK,CAACuF,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAChE,WAAW,CAACP,gBAAgB,EAAE,IAAI,CAACO,WAAW,CAACN,cAAc,CAAC;IAC5G,IAAIoB,OAAO,IAAIA,OAAO,CAACI,MAAM,EAAE;MAC7B;IACF,CAAC,MAAM;MACL,IAAI,CAACzC,KAAK,CAACsF,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;IACvC;EACF;EAEArC,MAAMA,CAAA;IACJ,IAAI,CAAC5C,aAAa,CAAC0D,IAAI,CAAC;MACtByB,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC9E;KACf,CAAC;IACF,IAAI,CAACP,SAAS,CAACsF,IAAI,EAAE;EACvB;EAEAC,oBAAoBA,CAACC,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEjE,IAAuC,IAAKA,IAAI,CAACyB,SAAS,IAAI,CAACzB,IAAI,CAACP,QAAQ,CAAC;EACjG;EAIAyE,uBAAuBA,CAACC,KAAa;IACnC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAAC7C,mBAAmB,CAAC,CAAC,CAAC,CAACT,MAAM,EAAE;MAC5D,MAAM,IAAIuD,KAAK,CAAC,8DAA8D,CAAC;IACjF;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC/C,mBAAmB,EAAE;MAChD,IAAI+C,SAAS,CAACF,KAAK,CAAC,CAAC1E,QAAQ,EAAE;QAC7B,IAAI0E,KAAK,IAAIE,SAAS,CAACxD,MAAM,IAAI,CAACwD,SAAS,CAACF,KAAK,CAAC,CAAC1C,SAAS,EAAE;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEA6C,gBAAgBA,CAACC,OAAgB,EAAEJ,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC/C,mBAAmB,EAAE;MAChD,IAAI6C,KAAK,GAAGE,SAAS,CAACxD,MAAM,EAAE;QAAE;QAC9B,IAAIwD,SAAS,CAACF,KAAK,CAAC,CAAC1E,QAAQ,EAAE;UAC7B4E,SAAS,CAACF,KAAK,CAAC,CAAC1C,SAAS,GAAG8C,OAAO;QACtC;MACF;IACF;EACF;EAEAC,YAAYA,CAACD,OAAsB,EAAEP,GAAU;IAC7C,KAAK,MAAMhE,IAAI,IAAIgE,GAAG,EAAE;MACtBhE,IAAI,CAACyB,SAAS,GAAG8C,OAAO;IAC1B;EACF;CACD;AA7OYxG,8BAA8B,GAAA0G,UAAA,EAb1ClH,SAAS,CAAC;EACTmH,QAAQ,EAAE,8BAA8B;EACxCC,WAAW,EAAE,2CAA2C;EACxDC,SAAS,EAAE,CAAC,2CAA2C,CAAC;EACxDC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,CACPvH,YAAY,EAAEK,YAAY,EAC1BJ,kBAAkB,EAAEC,mBAAmB,EACvCC,qBAAqB;CAExB,CAAC,C,EAEWI,8BAA8B,CA6O1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}