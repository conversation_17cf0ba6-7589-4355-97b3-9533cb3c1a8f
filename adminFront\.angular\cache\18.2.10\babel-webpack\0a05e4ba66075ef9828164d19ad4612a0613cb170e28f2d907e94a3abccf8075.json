{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet ApproveWaiting2Component = class ApproveWaiting2Component {};\nApproveWaiting2Component = __decorate([Component({\n  selector: 'app-approve-waiting2',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting2.component.html',\n  styleUrl: './approve-waiting2.component.css'\n})], ApproveWaiting2Component);\nexport { ApproveWaiting2Component };", "map": {"version": 3, "names": ["Component", "ApproveWaiting2Component", "__decorate", "selector", "standalone", "imports", "ApproveWaitingComponent", "templateUrl", "styleUrl"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting2\\approve-waiting2.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n  selector: 'app-approve-waiting2',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting2.component.html',\n  styleUrl: './approve-waiting2.component.css',\n})\nexport class ApproveWaiting2Component { }\n"], "mappings": ";AAAA,SAAkCA,SAAS,QAAQ,eAAe;AAS3D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAAI;AAA5BA,wBAAwB,GAAAC,UAAA,EAPpCF,SAAS,CAAC;EACTG,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACC,uBAAuB,CAAC;EAClCC,WAAW,EAAE,mCAAmC;EAChDC,QAAQ,EAAE;CACX,CAAC,C,EACWP,wBAAwB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}