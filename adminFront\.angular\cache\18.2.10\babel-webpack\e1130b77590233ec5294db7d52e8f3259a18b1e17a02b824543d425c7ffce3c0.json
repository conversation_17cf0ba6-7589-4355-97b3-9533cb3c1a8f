{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ISO/IEC 9797-1 Padding Method 2.\n   */\n  CryptoJS.pad.Iso97971 = {\n    pad: function (data, blockSize) {\n      // Add 0x80 byte\n      data.concat(CryptoJS.lib.WordArray.create([0x80000000], 1));\n\n      // Zero pad the rest\n      CryptoJS.pad.ZeroPadding.pad(data, blockSize);\n    },\n    unpad: function (data) {\n      // Remove zero padding\n      CryptoJS.pad.ZeroPadding.unpad(data);\n\n      // Remove one more byte -- the 0x80 byte\n      data.sigBytes--;\n    }\n  };\n  return CryptoJS.pad.Iso97971;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}