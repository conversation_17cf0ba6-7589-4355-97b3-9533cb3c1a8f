{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../../components/breadcrumb/breadcrumb.component\";\nfunction EditSettingTimePeriodComponent_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 22)(2, \"nb-checkbox\", 23);\n    i0.ɵɵlistener(\"checkedChange\", function EditSettingTimePeriodComponent_ng_container_38_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const idx_r3 = i0.ɵɵrestoreView(_r2).index;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.enableAllAtIndex($event, idx_r3));\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const idx_r3 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllColumnChecked(idx_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.CHouseHold);\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 23);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const itm_r9 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(itm_r9.isChecked, $event) || (itm_r9.isChecked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const itm_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtwoWayProperty(\"checked\", itm_r9.isChecked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", itm_r9.CHouseHold, \"-\", itm_r9.CFloor, \"F \");\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" \\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵtemplate(1, EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template, 3, 3, \"nb-checkbox\", 24)(2, EditSettingTimePeriodComponent_tr_40_td_5_span_2_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const itm_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", itm_r9.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !itm_r9.CHouseId);\n  }\n}\nfunction EditSettingTimePeriodComponent_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 23);\n    i0.ɵɵlistener(\"checkedChange\", function EditSettingTimePeriodComponent_tr_40_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.enableAllRow($event, item_r7));\n    });\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, EditSettingTimePeriodComponent_tr_40_td_5_Template, 3, 2, \"td\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r3.isCheckAllRowChecked(item_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r7[0].CFloor, \"F \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", item_r7);\n  }\n}\nexport class EditSettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, route, _location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.route = route;\n    this._location = _location;\n    this._eventService = _eventService;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    this.isHouseList = false;\n    this.buildCaseId = this.route.snapshot.paramMap.get('id');\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.buildCaseId,\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getHouseChangeDate();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      console.log({\n        item\n      });\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit() {\n    const bodyReq = this.convertData();\n    this.validation(bodyReq);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.goBack();\n      }\n    });\n  }\n  convertData() {\n    return this.convertedHouseArray.flat().filter(item => item.isChecked && item.CHouseId !== null) // Lọc các item theo điều kiện\n    .map(item => ({\n      ...item,\n      CChangeStartDate: this.formatDate(this.searchQuery.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.searchQuery.CChangeEndDate)\n    }));\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Khởi tạo dictionary để gom nhóm các phần tử theo CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // Nếu CFloor chưa có trong dictionary thì khởi tạo danh sách rỗng\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate,\n          isChecked: false\n        });\n      });\n    });\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  getHouseChangeDate() {\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (this.houseChangeDates) {\n          this.getFloorsAndHouseholds(this.houseChangeDates);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(this.houseChangeDates);\n          console.log('convertedHouseArray', this.convertedHouseArray);\n        }\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation(bodyReq) {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.searchQuery.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.searchQuery.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.searchQuery.CChangeStartDate, this.searchQuery.CChangeEndDate);\n    if (bodyReq && bodyReq.length) {\n      return;\n    } else {\n      this.valid.required('[ 勾選適用戶型 ]', '');\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this._location.back();\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.isChecked || !item.CHouseId);\n  }\n  isCheckAllColumnChecked(index) {\n    if (index < 0 || index >= this.convertedHouseArray[0].length) {\n      throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n    }\n    for (const floorData of this.convertedHouseArray) {\n      if (floorData[index].CHouseId) {\n        if (index >= floorData.length || !floorData[index].isChecked) {\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.convertedHouseArray) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        if (floorData[index].CHouseId) {\n          floorData[index].isChecked = checked;\n        }\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.isChecked = checked;\n    }\n  }\n  static {\n    this.ɵfac = function EditSettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditSettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditSettingTimePeriodComponent,\n      selectors: [[\"ngx-edit-setting-time-period\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 47,\n      vars: 6,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"StartDate\", 1, \"label\", \"col-3\"], [1, \"text-red-600\"], [1, \"ml-3\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"for\", \"EndDate\", 1, \"label\", \"col-1\", \"text-center\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-center\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"min-w-[100px]\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"status\", \"basic\", 3, \"checked\", \"checkedChange\", 4, \"ngIf\"], [4, \"ngIf\"]],\n      template: function EditSettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 3);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"div\", 6)(9, \"label\", 7);\n          i0.ɵɵtext(10, \"1. \\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n          i0.ɵɵelementStart(11, \"span\", 8);\n          i0.ɵɵtext(12, \"*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" : \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"nb-form-field\", 9);\n          i0.ɵɵelement(15, \"nb-icon\", 10);\n          i0.ɵɵelementStart(16, \"input\", 11);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EditSettingTimePeriodComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"nb-datepicker\", 12, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 13);\n          i0.ɵɵtext(20, \" ~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\");\n          i0.ɵɵelement(22, \"nb-icon\", 10);\n          i0.ɵɵelementStart(23, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function EditSettingTimePeriodComponent_Template_input_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"nb-datepicker\", 12, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 5)(27, \"div\", 6)(28, \"label\", 15);\n          i0.ɵɵtext(29, \"2. \\u52FE\\u9078\\u9069\\u7528\\u6236\\u578B \");\n          i0.ɵɵelementStart(30, \"span\", 8);\n          i0.ɵɵtext(31, \"*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" : \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"div\", 16)(34, \"table\", 17)(35, \"thead\")(36, \"tr\");\n          i0.ɵɵelement(37, \"th\");\n          i0.ɵɵtemplate(38, EditSettingTimePeriodComponent_ng_container_38_Template, 5, 2, \"ng-container\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"tbody\");\n          i0.ɵɵtemplate(40, EditSettingTimePeriodComponent_tr_40_Template, 6, 3, \"tr\", 18);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"nb-card-footer\")(42, \"div\", 19)(43, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function EditSettingTimePeriodComponent_Template_button_click_43_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵtext(44, \" \\u8FD4\\u56DE\\u4E0A\\u9801 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function EditSettingTimePeriodComponent_Template_button_click_45_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵtext(46, \" \\u5132\\u5B58 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const StartDate_r10 = i0.ɵɵreference(18);\n          const EndDate_r11 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r10);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseChangeDates);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.convertedHouseArray);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJlZGl0LXNldHRpbmctdGltZS1wZXJpb2QuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvc2V0dGluZy10aW1lLXBlcmlvZC9lZGl0LXNldHRpbmctdGltZS1wZXJpb2QvZWRpdC1zZXR0aW5nLXRpbWUtcGVyaW9kLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0TEFBNEwiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbDatepickerModule", "NbDateFnsDateModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "EditSettingTimePeriodComponent_ng_container_38_Template_nb_checkbox_checkedChange_2_listener", "$event", "idx_r3", "ɵɵrestoreView", "_r2", "index", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "enableAllAtIndex", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "isCheckAllColumnChecked", "ɵɵtextInterpolate", "item_r5", "CHouseHold", "ɵɵtwoWayListener", "EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r8", "itm_r9", "$implicit", "ɵɵtwoWayBindingSet", "isChecked", "ɵɵtwoWayProperty", "ɵɵtextInterpolate2", "CFloor", "ɵɵtemplate", "EditSettingTimePeriodComponent_tr_40_td_5_nb_checkbox_1_Template", "EditSettingTimePeriodComponent_tr_40_td_5_span_2_Template", "CHouseId", "EditSettingTimePeriodComponent_tr_40_Template_nb_checkbox_checkedChange_2_listener", "item_r7", "_r6", "enableAllRow", "EditSettingTimePeriodComponent_tr_40_td_5_Template", "isCheckAllRowChecked", "ɵɵtextInterpolate1", "EditSettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "route", "_location", "_eventService", "buildCaseOptions", "label", "value", "isStatus", "isHouseList", "buildCaseId", "snapshot", "paramMap", "get", "selectedHouseChangeDate", "CChangeStartDate", "CChangeEndDate", "undefined", "ngOnInit", "searchQuery", "CBuildCaseSelected", "getHouseChangeDate", "openModel", "ref", "item", "console", "log", "Date", "open", "formatDate", "CChangeDate", "format", "onSubmit", "bodyReq", "convertData", "validation", "errorMessages", "length", "showErrorMSGs", "apiHouseSaveHouseChangeDatePost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "goBack", "convertedHouseArray", "flat", "filter", "map", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "Entries", "houseChangeDates", "onClose", "close", "clear", "required", "checkStartBeforeEnd", "action", "payload", "back", "row", "every", "Error", "floorData", "checked", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "ActivatedRoute", "i7", "Location", "i8", "EventService", "selectors", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "EditSettingTimePeriodComponent_Template", "rf", "ctx", "ɵɵelement", "EditSettingTimePeriodComponent_Template_input_ngModelChange_16_listener", "_r1", "EditSettingTimePeriodComponent_Template_input_ngModelChange_23_listener", "EditSettingTimePeriodComponent_ng_container_38_Template", "EditSettingTimePeriodComponent_tr_40_Template", "EditSettingTimePeriodComponent_Template_button_click_43_listener", "EditSettingTimePeriodComponent_Template_button_click_45_listener", "StartDate_r10", "EndDate_r11", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\edit-setting-time-period\\edit-setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\edit-setting-time-period\\edit-setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string;\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  isChecked?: boolean\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-edit-setting-time-period',\r\n  templateUrl: './edit-setting-time-period.component.html',\r\n  styleUrls: ['./edit-setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class EditSettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private route: ActivatedRoute,\r\n    private _location: Location,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n    this.buildCaseId = this.route.snapshot.paramMap.get('id')\r\n\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n  }\r\n  buildCaseId: any | null\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.buildCaseId,\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getHouseChangeDate()\r\n  }\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      console.log({ item });\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit() {\r\n    const bodyReq = this.convertData()\r\n    this.validation(bodyReq)\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  convertData() {\r\n    return this.convertedHouseArray.flat()\r\n      .filter(item => item.isChecked && item.CHouseId !== null) // Lọc các item theo điều kiện\r\n      .map(item => ({\r\n        ...item,\r\n        CChangeStartDate: this.formatDate(this.searchQuery.CChangeStartDate),\r\n        CChangeEndDate: this.formatDate(this.searchQuery.CChangeEndDate)\r\n      }));\r\n\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Khởi tạo dictionary để gom nhóm các phần tử theo CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // Nếu CFloor chưa có trong dictionary thì khởi tạo danh sách rỗng\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Thêm phần tử vào danh sách của CFloor tương ứng\r\n          CHouseHold: household.CHouseHold,\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate,\r\n          isChecked: false\r\n        });\r\n      });\r\n    });\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (this.houseChangeDates) {\r\n          this.getFloorsAndHouseholds(this.houseChangeDates)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(this.houseChangeDates)\r\n          console.log('convertedHouseArray', this.convertedHouseArray);\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation(bodyReq?: any) {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.searchQuery.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.searchQuery.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.searchQuery.CChangeStartDate, this.searchQuery.CChangeEndDate)\r\n    if (bodyReq && bodyReq.length) {\r\n      return\r\n    } else {\r\n      this.valid.required('[ 勾選適用戶型 ]', '')\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { isChecked: any; CHouseId: any }) => item.isChecked || !item.CHouseId);\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (index < 0 || index >= this.convertedHouseArray[0].length) {\r\n      throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n    }\r\n    for (const floorData of this.convertedHouseArray) {\r\n      if (floorData[index].CHouseId) {\r\n        if (index >= floorData.length || !floorData[index].isChecked) {\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n    return true;\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.convertedHouseArray) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        if (floorData[index].CHouseId) {\r\n          floorData[index].isChecked = checked;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean | any, row: any[]) {\r\n    for (const item of row) {\r\n      item.isChecked = checked;\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-12\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"StartDate\" class=\"label col-3\">1. 開放時間起訖 <span class=\"text-red-600\">*</span> :\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon> \r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"StartDate\" class=\"w-full col-4\"\r\n              [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <label for=\"EndDate\" class=\"label col-1 text-center\"> ~\r\n          </label>\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon> \r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"EndDate\" class=\"w-full col-4\"\r\n              [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-12\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">2. 勾選適用戶型 <span class=\"text-red-600\">*</span> :\r\n          </label>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr>\r\n            <th></th>\r\n            <ng-container *ngFor=\"let item of houseChangeDates ; let idx = index\">\r\n              <th class=\"min-w-[100px]\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span>{{item.CHouseHold}}</span>\r\n                </nb-checkbox>\r\n              </th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of convertedHouseArray ; let i = index\">\r\n            <td>\r\n              <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(item)\" (checkedChange)=\"enableAllRow($event,item)\">\r\n                <span>\r\n                  {{ item[0].CFloor}}F\r\n                </span>\r\n              </nb-checkbox>\r\n              </td>\r\n            <td *ngFor=\"let itm of item ; let i = index\">\r\n              <nb-checkbox *ngIf=\"itm.CHouseId\" status=\"basic\" [(checked)]=\"itm.isChecked\">\r\n                <span>\r\n                  {{itm.CHouseHold}}-{{ itm.CFloor}}F\r\n                </span>\r\n              </nb-checkbox>\r\n              <span *ngIf=\"!itm.CHouseId\">\r\n                未設定\r\n              </span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n      <div class=\"d-flex justify-center w-full\">\r\n        <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"goBack()\">\r\n          返回上頁\r\n        </button>\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"onSubmit()\">\r\n          儲存\r\n        </button>\r\n      </div>\r\n  </nb-card-footer>\r\n</nb-card>"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AAQvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;;;IC0BhEC,EAAA,CAAAC,uBAAA,GAAsE;IAElED,EADF,CAAAE,cAAA,aAA0B,sBAEmB;IADfF,EAAA,CAAAG,UAAA,2BAAAC,6FAAAC,MAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAiBF,MAAA,CAAAG,gBAAA,CAAAR,MAAA,EAAAC,MAAA,CAA6B;IAAA,EAAC;IAEzEN,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAc,MAAA,GAAmB;IAE7Bd,EAF6B,CAAAe,YAAA,EAAO,EACpB,EACX;;;;;;;IAHDf,EAAA,CAAAgB,SAAA,GAAwC;IAAxChB,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAAQ,uBAAA,CAAAZ,MAAA,EAAwC;IAClCN,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAmB,iBAAA,CAAAC,OAAA,CAAAC,UAAA,CAAmB;;;;;;IAgB7BrB,EAAA,CAAAE,cAAA,sBAA6E;IAA5BF,EAAA,CAAAsB,gBAAA,2BAAAC,sGAAAlB,MAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAW,aAAA,GAAAe,SAAA;MAAA1B,EAAA,CAAA2B,kBAAA,CAAAF,MAAA,CAAAG,SAAA,EAAAvB,MAAA,MAAAoB,MAAA,CAAAG,SAAA,GAAAvB,MAAA;MAAA,OAAAL,EAAA,CAAAY,WAAA,CAAAP,MAAA;IAAA,EAA2B;IAC1EL,EAAA,CAAAE,cAAA,WAAM;IACJF,EAAA,CAAAc,MAAA,GACF;IACFd,EADE,CAAAe,YAAA,EAAO,EACK;;;;IAJmCf,EAAA,CAAA6B,gBAAA,YAAAJ,MAAA,CAAAG,SAAA,CAA2B;IAExE5B,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAA8B,kBAAA,MAAAL,MAAA,CAAAJ,UAAA,OAAAI,MAAA,CAAAM,MAAA,OACF;;;;;IAEF/B,EAAA,CAAAE,cAAA,WAA4B;IAC1BF,EAAA,CAAAc,MAAA,2BACF;IAAAd,EAAA,CAAAe,YAAA,EAAO;;;;;IARTf,EAAA,CAAAE,cAAA,SAA6C;IAM3CF,EALA,CAAAgC,UAAA,IAAAC,gEAAA,0BAA6E,IAAAC,yDAAA,mBAKjD;IAG9BlC,EAAA,CAAAe,YAAA,EAAK;;;;IARWf,EAAA,CAAAgB,SAAA,EAAkB;IAAlBhB,EAAA,CAAAiB,UAAA,SAAAQ,MAAA,CAAAU,QAAA,CAAkB;IAKzBnC,EAAA,CAAAgB,SAAA,EAAmB;IAAnBhB,EAAA,CAAAiB,UAAA,UAAAQ,MAAA,CAAAU,QAAA,CAAmB;;;;;;IAZ1BnC,EAFJ,CAAAE,cAAA,SAA6D,SACvD,sBAC6G;IAA5CF,EAAA,CAAAG,UAAA,2BAAAiC,mFAAA/B,MAAA;MAAA,MAAAgC,OAAA,GAAArC,EAAA,CAAAO,aAAA,CAAA+B,GAAA,EAAAZ,SAAA;MAAA,MAAAhB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAiBF,MAAA,CAAA6B,YAAA,CAAAlC,MAAA,EAAAgC,OAAA,CAAyB;IAAA,EAAC;IAC5GrC,EAAA,CAAAE,cAAA,WAAM;IACJF,EAAA,CAAAc,MAAA,GACF;IAEFd,EAFE,CAAAe,YAAA,EAAO,EACK,EACT;IACPf,EAAA,CAAAgC,UAAA,IAAAQ,kDAAA,iBAA6C;IAU/CxC,EAAA,CAAAe,YAAA,EAAK;;;;;IAhB2Bf,EAAA,CAAAgB,SAAA,GAAsC;IAAtChB,EAAA,CAAAiB,UAAA,YAAAP,MAAA,CAAA+B,oBAAA,CAAAJ,OAAA,EAAsC;IAE9DrC,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAA0C,kBAAA,MAAAL,OAAA,IAAAN,MAAA,OACF;IAGgB/B,EAAA,CAAAgB,SAAA,EAAU;IAAVhB,EAAA,CAAAiB,UAAA,YAAAoB,OAAA,CAAU;;;ADd1C,OAAM,MAAOM,8BAA+B,SAAQ7C,aAAa;EAC/D8C,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,KAAqB,EACrBC,SAAmB,EACnBC,aAA2B;IAEnC,KAAK,CAACR,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IA2BvB,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAC,QAAQ,GAAY,IAAI;IAkKxB,KAAAC,WAAW,GAAG,KAAK;IA9LjB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAGzD,IAAI,CAACC,uBAAuB,GAAG;MAC7BC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBlC,MAAM,EAAEmC,SAAS;MACjB7C,UAAU,EAAE,EAAE;MACdc,QAAQ,EAAE+B;KACX;EACH;EAyBSC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACV,WAAW;MACpCK,gBAAgB,EAAEE,SAAS;MAC3BD,cAAc,EAAEC;KACjB;IACD,IAAI,CAACI,kBAAkB,EAAE;EAC3B;EAGAC,SAASA,CAACC,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACtC,QAAQ,EAAE;MACjBuC,OAAO,CAACC,GAAG,CAAC;QAAEF;MAAI,CAAE,CAAC;MACrB,IAAI,CAACV,uBAAuB,GAAG;QAC7B,GAAGU,IAAI;QACPT,gBAAgB,EAAES,IAAI,CAACT,gBAAgB,GAAG,IAAIY,IAAI,CAACH,IAAI,CAACT,gBAAgB,CAAC,GAAGE,SAAS;QACrFD,cAAc,EAAEQ,IAAI,CAACR,cAAc,GAAG,IAAIW,IAAI,CAACH,IAAI,CAACR,cAAc,CAAC,GAAGC;OACvE;MACD,IAAI,CAACpB,aAAa,CAAC+B,IAAI,CAACL,GAAG,CAAC;IAC9B;EACF;EAEAM,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOnF,MAAM,CAACmF,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE;IAClC,IAAI,CAACC,UAAU,CAACF,OAAO,CAAC;IACxB,IAAI,IAAI,CAAClC,KAAK,CAACqC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACvC,OAAO,CAACwC,aAAa,CAAC,IAAI,CAACvC,KAAK,CAACqC,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACpC,aAAa,CAACuC,oCAAoC,CAAC;MACtDC,IAAI,EAAEP;KACP,CAAC,CAACQ,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC7C,OAAO,CAAC8C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAX,WAAWA,CAAA;IACT,OAAO,IAAI,CAACY,mBAAmB,CAACC,IAAI,EAAE,CACnCC,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAAC7C,SAAS,IAAI6C,IAAI,CAACtC,QAAQ,KAAK,IAAI,CAAC,CAAC;IAAA,CACzD+D,GAAG,CAACzB,IAAI,KAAK;MACZ,GAAGA,IAAI;MACPT,gBAAgB,EAAE,IAAI,CAACc,UAAU,CAAC,IAAI,CAACV,WAAW,CAACJ,gBAAgB,CAAC;MACpEC,cAAc,EAAE,IAAI,CAACa,UAAU,CAAC,IAAI,CAACV,WAAW,CAACH,cAAc;KAChE,CAAC,CAAC;EAEP;EAEAkC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAAC1E,MAAM;QAC1B,IAAI,CAACsE,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBtF,UAAU,EAAEkF,SAAS,CAAClF,UAAU;UAChCc,QAAQ,EAAEsE,KAAK,CAACtE,QAAQ;UACxBJ,MAAM,EAAE0E,KAAK,CAAC1E,MAAM;UACpBiC,gBAAgB,EAAEyC,KAAK,CAACzC,gBAAgB;UACxCC,cAAc,EAAEwC,KAAK,CAACxC,cAAc;UACpCrC,SAAS,EAAE;SACZ,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACgF,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAACV,GAAG,CAAEQ,KAAU,IAAI;MAChE,OAAO,IAAI,CAACO,UAAU,CAACf,GAAG,CAAEK,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACQ,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC9F,UAAU,KAAKkF,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdpF,UAAU,EAAEkF,SAAS;UACrBpE,QAAQ,EAAE,IAAI;UACdJ,MAAM,EAAE2E,KAAK;UACb1C,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO+C,MAAM;EACf;EAEAI,sBAAsBA,CAAChB,GAAU;IAC/B,MAAMiB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ClB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAAClF,UAAU,CAAC;MACvCkF,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCY,SAAS,CAACG,GAAG,CAACf,KAAK,CAAC1E,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC6E,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAjD,kBAAkBA,CAAA;IAChB,IAAI,CAACrB,aAAa,CAAC0E,mCAAmC,CAAC;MACrDlC,IAAI,EAAE;QACJmC,YAAY,EAAE,IAAI,CAACjE;;KAEtB,CAAC,CAAC+B,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACkC,OAAO,IAAIlC,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACkC,gBAAgB,GAAGnC,GAAG,CAACkC,OAAO,GAAGlC,GAAG,CAACkC,OAAO,GAAG,EAAE;QACtD,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACzB,IAAI,CAACV,sBAAsB,CAAC,IAAI,CAACU,gBAAgB,CAAC;UAClD,IAAI,CAAC/B,mBAAmB,GAAG,IAAI,CAACI,8BAA8B,CAAC,IAAI,CAAC2B,gBAAgB,CAAC;UACrFpD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACoB,mBAAmB,CAAC;QAC9D;MACF;IACF,CAAC,CAAC;EACJ;EAEAgC,OAAOA,CAACvD,GAAQ;IACdA,GAAG,CAACwD,KAAK,EAAE;EACb;EAEA5C,UAAUA,CAACF,OAAa;IACtB,IAAI,CAAClC,KAAK,CAACiF,KAAK,EAAE;IAClB,IAAI,CAACjF,KAAK,CAACkF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9D,WAAW,CAACJ,gBAAgB,CAAC;IAClE,IAAI,CAAChB,KAAK,CAACkF,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC9D,WAAW,CAACH,cAAc,CAAC;IAChE,IAAI,CAACjB,KAAK,CAACmF,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC/D,WAAW,CAACJ,gBAAgB,EAAE,IAAI,CAACI,WAAW,CAACH,cAAc,CAAC;IAC5G,IAAIiB,OAAO,IAAIA,OAAO,CAACI,MAAM,EAAE;MAC7B;IACF,CAAC,MAAM;MACL,IAAI,CAACtC,KAAK,CAACkF,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC;IACvC;EACF;EAEApC,MAAMA,CAAA;IACJ,IAAI,CAACzC,aAAa,CAACsD,IAAI,CAAC;MACtByB,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC1E;KACf,CAAC;IACF,IAAI,CAACP,SAAS,CAACkF,IAAI,EAAE;EACvB;EAEA7F,oBAAoBA,CAAC8F,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAE/D,IAAuC,IAAKA,IAAI,CAAC7C,SAAS,IAAI,CAAC6C,IAAI,CAACtC,QAAQ,CAAC;EACjG;EAIAjB,uBAAuBA,CAACT,KAAa;IACnC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACsF,mBAAmB,CAAC,CAAC,CAAC,CAACT,MAAM,EAAE;MAC5D,MAAM,IAAImD,KAAK,CAAC,8DAA8D,CAAC;IACjF;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC3C,mBAAmB,EAAE;MAChD,IAAI2C,SAAS,CAACjI,KAAK,CAAC,CAAC0B,QAAQ,EAAE;QAC7B,IAAI1B,KAAK,IAAIiI,SAAS,CAACpD,MAAM,IAAI,CAACoD,SAAS,CAACjI,KAAK,CAAC,CAACmB,SAAS,EAAE;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEAf,gBAAgBA,CAAC8H,OAAgB,EAAElI,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIgI,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAC3C,mBAAmB,EAAE;MAChD,IAAItF,KAAK,GAAGiI,SAAS,CAACpD,MAAM,EAAE;QAAE;QAC9B,IAAIoD,SAAS,CAACjI,KAAK,CAAC,CAAC0B,QAAQ,EAAE;UAC7BuG,SAAS,CAACjI,KAAK,CAAC,CAACmB,SAAS,GAAG+G,OAAO;QACtC;MACF;IACF;EACF;EAEApG,YAAYA,CAACoG,OAAsB,EAAEJ,GAAU;IAC7C,KAAK,MAAM9D,IAAI,IAAI8D,GAAG,EAAE;MACtB9D,IAAI,CAAC7C,SAAS,GAAG+G,OAAO;IAC1B;EACF;;;uCA5OWhG,8BAA8B,EAAA3C,EAAA,CAAA4I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAA4I,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAhJ,EAAA,CAAA4I,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlJ,EAAA,CAAA4I,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAApJ,EAAA,CAAA4I,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAtJ,EAAA,CAAA4I,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAvJ,EAAA,CAAA4I,iBAAA,CAAAY,EAAA,CAAAC,cAAA,GAAAzJ,EAAA,CAAA4I,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAA3J,EAAA,CAAA4I,iBAAA,CAAAgB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA9BlH,8BAA8B;MAAAmH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhK,EAAA,CAAAiK,kBAAA,CAR9B,EAAE,GAAAjK,EAAA,CAAAkK,0BAAA,EAAAlK,EAAA,CAAAmK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpCbzK,EADF,CAAAE,cAAA,iBAA0B,qBACR;UACdF,EAAA,CAAA2K,SAAA,qBAAiC;UACnC3K,EAAA,CAAAe,YAAA,EAAiB;UAEff,EADF,CAAAE,cAAA,mBAAc,YACyB;UAAAF,EAAA,CAAAc,MAAA,gRAA6C;UAAAd,EAAA,CAAAe,YAAA,EAAK;UAIjFf,EAHN,CAAAE,cAAA,aAA8B,aACL,aAC6B,eACL;UAAAF,EAAA,CAAAc,MAAA,gDAAU;UAAAd,EAAA,CAAAE,cAAA,eAA2B;UAAAF,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAe,YAAA,EAAO;UAACf,EAAA,CAAAc,MAAA,WACzF;UAAAd,EAAA,CAAAe,YAAA,EAAQ;UACRf,EAAA,CAAAE,cAAA,wBAA4B;UAC1BF,EAAA,CAAA2K,SAAA,mBAAoD;UACpD3K,EAAA,CAAAE,cAAA,iBAC6C;UAA3CF,EAAA,CAAAsB,gBAAA,2BAAAsJ,wEAAAvK,MAAA;YAAAL,EAAA,CAAAO,aAAA,CAAAsK,GAAA;YAAA7K,EAAA,CAAA2B,kBAAA,CAAA+I,GAAA,CAAAtG,WAAA,CAAAJ,gBAAA,EAAA3D,MAAA,MAAAqK,GAAA,CAAAtG,WAAA,CAAAJ,gBAAA,GAAA3D,MAAA;YAAA,OAAAL,EAAA,CAAAY,WAAA,CAAAP,MAAA;UAAA,EAA0C;UAD5CL,EAAA,CAAAe,YAAA,EAC6C;UAC7Cf,EAAA,CAAA2K,SAAA,4BAA8D;UAChE3K,EAAA,CAAAe,YAAA,EAAgB;UAChBf,EAAA,CAAAE,cAAA,iBAAqD;UAACF,EAAA,CAAAc,MAAA,WACtD;UAAAd,EAAA,CAAAe,YAAA,EAAQ;UACRf,EAAA,CAAAE,cAAA,qBAAe;UACbF,EAAA,CAAA2K,SAAA,mBAAoD;UACpD3K,EAAA,CAAAE,cAAA,iBAC2C;UAAzCF,EAAA,CAAAsB,gBAAA,2BAAAwJ,wEAAAzK,MAAA;YAAAL,EAAA,CAAAO,aAAA,CAAAsK,GAAA;YAAA7K,EAAA,CAAA2B,kBAAA,CAAA+I,GAAA,CAAAtG,WAAA,CAAAH,cAAA,EAAA5D,MAAA,MAAAqK,GAAA,CAAAtG,WAAA,CAAAH,cAAA,GAAA5D,MAAA;YAAA,OAAAL,EAAA,CAAAY,WAAA,CAAAP,MAAA;UAAA,EAAwC;UAD1CL,EAAA,CAAAe,YAAA,EAC2C;UAC3Cf,EAAA,CAAA2K,SAAA,4BAA4D;UAGlE3K,EAFI,CAAAe,YAAA,EAAgB,EACZ,EACF;UAGFf,EAFJ,CAAAE,cAAA,cAAuB,cAC6B,iBACJ;UAAAF,EAAA,CAAAc,MAAA,gDAAU;UAAAd,EAAA,CAAAE,cAAA,eAA2B;UAAAF,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAe,YAAA,EAAO;UAACf,EAAA,CAAAc,MAAA,WAC1F;UAGNd,EAHM,CAAAe,YAAA,EAAQ,EACJ,EACF,EACF;UAKAf,EAHN,CAAAE,cAAA,eAAmC,iBAC4C,aACpE,UACD;UACFF,EAAA,CAAA2K,SAAA,UAAS;UACT3K,EAAA,CAAAgC,UAAA,KAAA+I,uDAAA,2BAAsE;UAS1E/K,EADE,CAAAe,YAAA,EAAK,EACC;UACRf,EAAA,CAAAE,cAAA,aAAO;UACLF,EAAA,CAAAgC,UAAA,KAAAgJ,6CAAA,iBAA6D;UAsBrEhL,EAHM,CAAAe,YAAA,EAAQ,EACF,EACJ,EACO;UAGTf,EAFN,CAAAE,cAAA,sBAAgB,eAC8B,kBACyB;UAAnBF,EAAA,CAAAG,UAAA,mBAAA8K,iEAAA;YAAAjL,EAAA,CAAAO,aAAA,CAAAsK,GAAA;YAAA,OAAA7K,EAAA,CAAAY,WAAA,CAAS8J,GAAA,CAAA5E,MAAA,EAAQ;UAAA,EAAC;UAC9D9F,EAAA,CAAAc,MAAA,kCACF;UAAAd,EAAA,CAAAe,YAAA,EAAS;UACTf,EAAA,CAAAE,cAAA,kBAAyD;UAArBF,EAAA,CAAAG,UAAA,mBAAA+K,iEAAA;YAAAlL,EAAA,CAAAO,aAAA,CAAAsK,GAAA;YAAA,OAAA7K,EAAA,CAAAY,WAAA,CAAS8J,GAAA,CAAAzF,QAAA,EAAU;UAAA,EAAC;UACtDjF,EAAA,CAAAc,MAAA,sBACF;UAGRd,EAHQ,CAAAe,YAAA,EAAS,EACL,EACO,EACT;;;;;UAvEgEf,EAAA,CAAAgB,SAAA,IAA0B;UAA1BhB,EAAA,CAAAiB,UAAA,iBAAAkK,aAAA,CAA0B;UACtFnL,EAAA,CAAA6B,gBAAA,YAAA6I,GAAA,CAAAtG,WAAA,CAAAJ,gBAAA,CAA0C;UAOgBhE,EAAA,CAAAgB,SAAA,GAAwB;UAAxBhB,EAAA,CAAAiB,UAAA,iBAAAmK,WAAA,CAAwB;UAClFpL,EAAA,CAAA6B,gBAAA,YAAA6I,GAAA,CAAAtG,WAAA,CAAAH,cAAA,CAAwC;UAkBXjE,EAAA,CAAAgB,SAAA,IAAsB;UAAtBhB,EAAA,CAAAiB,UAAA,YAAAyJ,GAAA,CAAA5C,gBAAA,CAAsB;UAWlC9H,EAAA,CAAAgB,SAAA,GAAyB;UAAzBhB,EAAA,CAAAiB,UAAA,YAAAyJ,GAAA,CAAA3E,mBAAA,CAAyB;;;qBDZpDtG,YAAY,EAAAiK,EAAA,CAAA2B,OAAA,EAAA3B,EAAA,CAAA4B,IAAA,EAAEzL,YAAY,EAAA0L,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,mBAAA,EAAAhD,EAAA,CAAAiD,gBAAA,EAAAjD,EAAA,CAAAkD,oBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAnD,EAAA,CAAAoD,eAAA,EAAApD,EAAA,CAAAqD,qBAAA,EAAArD,EAAA,CAAAsD,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1B7M,kBAAkB,EAAEC,mBAAmB;MAAA6M,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}