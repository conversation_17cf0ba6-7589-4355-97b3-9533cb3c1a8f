{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Json } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-json';\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Plain } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class HouseHoldDetailService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiHouseHoldDetailGetHouseHoldDetailListPost()` */\n  static {\n    this.ApiHouseHoldDetailGetHouseHoldDetailListPostPath = '/api/HouseHoldDetail/GetHouseHoldDetailList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params, context) {\n    return apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(params, context) {\n    return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params, context) {\n    return apiHouseHoldDetailGetHouseHoldDetailListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Json(params, context) {\n    return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function HouseHoldDetailService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseHoldDetailService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HouseHoldDetailService,\n      factory: HouseHoldDetailService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiHouseHoldDetailGetHouseHoldDetailListPost$Json", "apiHouseHoldDetailGetHouseHoldDetailListPost$Plain", "HouseHoldDetailService", "constructor", "config", "http", "ApiHouseHoldDetailGetHouseHoldDetailListPostPath", "apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\house-hold-detail.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Json } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-json';\r\nimport { ApiHouseHoldDetailGetHouseHoldDetailListPost$Json$Params } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-json';\r\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Plain } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-plain';\r\nimport { ApiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Params } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-plain';\r\nimport { HoldNameReponseListResponseBase } from '../models/hold-name-reponse-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class HouseHoldDetailService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiHouseHoldDetailGetHouseHoldDetailListPost()` */\r\n  static readonly ApiHouseHoldDetailGetHouseHoldDetailListPostPath = '/api/HouseHoldDetail/GetHouseHoldDetailList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params?: ApiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<HoldNameReponseListResponseBase>> {\r\n    return apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(params?: ApiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Params, context?: HttpContext): Observable<HoldNameReponseListResponseBase> {\r\n    return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HoldNameReponseListResponseBase>): HoldNameReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params?: ApiHouseHoldDetailGetHouseHoldDetailListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<HoldNameReponseListResponseBase>> {\r\n    return apiHouseHoldDetailGetHouseHoldDetailListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldDetailGetHouseHoldDetailListPost$Json(params?: ApiHouseHoldDetailGetHouseHoldDetailListPost$Json$Params, context?: HttpContext): Observable<HoldNameReponseListResponseBase> {\r\n    return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<HoldNameReponseListResponseBase>): HoldNameReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,iDAAiD,QAAQ,oFAAoF;AAEtJ,SAASC,kDAAkD,QAAQ,qFAAqF;;;;AAKxJ,OAAM,MAAOC,sBAAuB,SAAQH,WAAW;EACrDI,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,gDAAgD,GAAG,6CAA6C;EAAC;EAEjH;;;;;;EAMAC,2DAA2DA,CAACC,MAAkE,EAAEC,OAAqB;IACnJ,OAAOR,kDAAkD,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrG;EAEA;;;;;;EAMAR,kDAAkDA,CAACO,MAAkE,EAAEC,OAAqB;IAC1I,OAAO,IAAI,CAACF,2DAA2D,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3Fb,GAAG,CAAEc,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;;;;;EAMAC,0DAA0DA,CAACN,MAAiE,EAAEC,OAAqB;IACjJ,OAAOT,iDAAiD,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpG;EAEA;;;;;;EAMAT,iDAAiDA,CAACQ,MAAiE,EAAEC,OAAqB;IACxI,OAAO,IAAI,CAACK,0DAA0D,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1Fb,GAAG,CAAEc,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;;;uCAlDWX,sBAAsB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAtBlB,sBAAsB;MAAAmB,OAAA,EAAtBnB,sBAAsB,CAAAoB,IAAA;MAAAC,UAAA,EADT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}