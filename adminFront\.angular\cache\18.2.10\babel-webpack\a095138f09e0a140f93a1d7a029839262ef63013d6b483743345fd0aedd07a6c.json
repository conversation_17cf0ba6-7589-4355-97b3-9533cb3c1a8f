{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./@core/utils/analytics.service\";\nimport * as i2 from \"./@core/utils/seo.service\";\nimport * as i3 from \"@angular/router\";\nexport class AppComponent {\n  constructor(analytics, seoService) {\n    this.analytics = analytics;\n    this.seoService = seoService;\n  }\n  ngOnInit() {\n    this.analytics.trackPageViews();\n    this.seoService.trackCanonicalChanges();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AnalyticsService), i0.ɵɵdirectiveInject(i2.SeoService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"ngx-app\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i3.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "analytics", "seoService", "ngOnInit", "trackPageViews", "trackCanonicalChanges", "i0", "ɵɵdirectiveInject", "i1", "AnalyticsService", "i2", "SeoService", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AnalyticsService } from './@core/utils/analytics.service';\r\nimport { SeoService } from './@core/utils/seo.service';\r\n\r\n@Component({\r\n  selector: 'ngx-app',\r\n  template: '<router-outlet></router-outlet>',\r\n})\r\nexport class AppComponent implements OnInit {\r\n\r\n  constructor(private analytics: AnalyticsService, private seoService: SeoService) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.analytics.trackPageViews();\r\n    this.seoService.trackCanonicalChanges();\r\n  }\r\n}\r\n"], "mappings": ";;;;AAQA,OAAM,MAAOA,YAAY;EAEvBC,YAAoBC,SAA2B,EAAUC,UAAsB;IAA3D,KAAAD,SAAS,GAATA,SAAS;IAA4B,KAAAC,UAAU,GAAVA,UAAU;EACnE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACF,SAAS,CAACG,cAAc,EAAE;IAC/B,IAAI,CAACF,UAAU,CAACG,qBAAqB,EAAE;EACzC;;;uCARWN,YAAY,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAZZ,YAAY;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAFZX,EAAA,CAAAa,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}