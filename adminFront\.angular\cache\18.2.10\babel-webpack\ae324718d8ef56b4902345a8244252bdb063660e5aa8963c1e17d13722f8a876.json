{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nexport class HomeComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"ngx-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"home works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [RouterModule]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterModule", "HomeComponent", "constructor", "ngOnInit", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n@Component({\r\n    selector: 'ngx-home',\r\n    templateUrl: './home.component.html',\r\n    styleUrls: ['./home.component.scss'],\r\n    standalone: true,\r\n    imports: [ RouterModule ]\r\n})\r\nexport class HomeComponent implements OnInit {\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<p>home works!</p>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAS9C,OAAM,MAAOC,aAAa;EAExBC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GACR;;;uCALWF,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV1BN,EAAA,CAAAQ,cAAA,QAAG;UAAAR,EAAA,CAAAS,MAAA,kBAAW;UAAAT,EAAA,CAAAU,YAAA,EAAI;;;qBDQHjB,YAAY;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}