{"ast": null, "code": "import * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/shared/services/message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../@theme/directives/label.directive\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"fileInput\"];\nfunction CustomerChangePictureComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.addNew(dialogUploadDrawing_r4));\n    });\n    i0.ɵɵtext(1, \" \\u4E0A\\u50B3\\u5716\\u9762\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_tr_25_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const item_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialogUploadDrawing_r4 = i0.ɵɵreference(33);\n      return i0.ɵɵresetView(ctx_r2.onEdit(dialogUploadDrawing_r4, item_r6));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_tr_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 18)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 19);\n    i0.ɵɵtemplate(10, CustomerChangePictureComponent_tr_25_button_10_Template, 2, 0, \"button\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CChangeDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CDrawingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.formatDate(item_r6.CCreateDT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r6.CIsApprove == null ? \"\\u5F85\\u5BE9\\u6838\" : item_r6.CIsApprove ? \"\\u901A\\u904E\" : \"\\u99C1\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext();\n      const inputFile_r9 = i0.ɵɵreference(18);\n      return i0.ɵɵresetView(inputFile_r9.click());\n    });\n    i0.ɵɵtext(1, \"\\u9078\\u64C7\\u6A94\\u6848\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 46);\n  }\n  if (rf & 2) {\n    const file_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r11.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 47);\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_21_div_1_img_1_Template, 1, 1, \"img\", 41)(2, CustomerChangePictureComponent_ng_template_32_div_21_div_1_span_2_Template, 2, 0, \"span\", 42);\n    i0.ɵɵelementStart(3, \"p\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 44);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template_span_click_5_listener() {\n      const i_r12 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r12));\n    });\n    i0.ɵɵelement(6, \"i\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isImage(file_r11.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isImage(file_r11.CFileType));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r11.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_21_div_1_Template, 7, 3, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.imageUrlList);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 50);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r14.CFile));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r14.CFile, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 51);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const file_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.openNewTab(file_r14.CFile));\n    });\n    i0.ɵɵtext(1, \"PDF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_img_1_Template, 1, 1, \"img\", 48)(2, CustomerChangePictureComponent_ng_template_32_div_22_div_1_span_2_Template, 2, 0, \"span\", 49);\n    i0.ɵɵelementStart(3, \"p\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isPDFString(file_r14.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isPDFString(file_r14.CFile));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r14.CFileName);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, CustomerChangePictureComponent_ng_template_32_div_22_div_1_Template, 5, 3, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.SpecialChange.CFileRes);\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ref_r16 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSaveSpecialChange(ref_r16));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\\u5BE9\\u6838\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerChangePictureComponent_ng_template_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 22)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 23)(4, \"div\", 24)(5, \"label\", 25, 1);\n    i0.ɵɵtext(7, \" \\u8A0E\\u8AD6\\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-calendar\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_p_calendar_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CChangeDate, $event) || (ctx_r2.formSpecialChange.CChangeDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 24)(10, \"label\", 27);\n    i0.ɵɵtext(11, \" \\u5716\\u9762\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CDrawingName, $event) || (ctx_r2.formSpecialChange.CDrawingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 24)(14, \"label\", 29);\n    i0.ɵɵtext(15, \" \\u9078\\u6A23\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, CustomerChangePictureComponent_ng_template_32_button_16_Template, 2, 0, \"button\", 8);\n    i0.ɵɵelementStart(17, \"input\", 30, 2);\n    i0.ɵɵlistener(\"change\", function CustomerChangePictureComponent_ng_template_32_Template_input_change_17_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.detectFiles($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 31);\n    i0.ɵɵelement(20, \"label\", 32);\n    i0.ɵɵtemplate(21, CustomerChangePictureComponent_ng_template_32_div_21_Template, 2, 1, \"div\", 33)(22, CustomerChangePictureComponent_ng_template_32_div_22_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 31)(24, \"label\", 34);\n    i0.ɵɵtext(25, \"\\u5BE9\\u6838\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"textarea\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CustomerChangePictureComponent_ng_template_32_Template_textarea_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.formSpecialChange.CApproveRemark, $event) || (ctx_r2.formSpecialChange.CApproveRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 14)(28, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_ng_template_32_Template_button_click_28_listener() {\n      const ref_r16 = i0.ɵɵrestoreView(_r7).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r16));\n    });\n    i0.ɵɵtext(29, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, CustomerChangePictureComponent_ng_template_32_button_30_Template, 2, 0, \"button\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u5BA2\\u8B8A\\u5716\\u4E0A\\u50B3 > \", ctx_r2.house.CHousehold, \" \\u00A0 \", ctx_r2.house.CFloor, \"F \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"appendTo\", \"CChangeDate\")(\"iconDisplay\", \"input\")(\"showIcon\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CChangeDate);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit)(\"showButtonBar\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CDrawingName);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r2.isEdit && ctx_r2.SpecialChange.CIsApprove === null));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEdit);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isEdit);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.formSpecialChange.CApproveRemark);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEdit);\n  }\n}\nexport let CustomerChangePictureComponent = /*#__PURE__*/(() => {\n  class CustomerChangePictureComponent extends BaseComponent {\n    constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.valid = valid;\n      this._specialChangeService = _specialChangeService;\n      this._houseService = _houseService;\n      this.route = route;\n      this.message = message;\n      this.location = location;\n      this._eventService = _eventService;\n      this.imageUrlList = [];\n      this.isEdit = false;\n      this.statusOptions = [{\n        value: 0,\n        key: 'allow',\n        label: '允許'\n      }, {\n        value: 1,\n        key: 'not allowed',\n        label: '不允許'\n      }];\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.listPictures = [];\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        if (params) {\n          const idParam = params.get('id1');\n          const id = idParam ? +idParam : 0;\n          this.buildCaseId = id;\n          const idParam2 = params.get('id2');\n          const id2 = idParam2 ? +idParam2 : 0;\n          this.houseId = id2;\n          this.getListSpecialChange();\n          this.getHouseById();\n        }\n      });\n    }\n    openPdfInNewTab(data) {\n      if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n    }\n    getListSpecialChange() {\n      this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n        body: {\n          CHouseId: this.houseId,\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize\n        }\n      }).subscribe(res => {\n        if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n          this.listSpecialChange = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      });\n    }\n    getHouseById() {\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: this.houseId\n        }\n      }).subscribe(res => {\n        if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n          this.house = res.Entries;\n          this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n        }\n      });\n    }\n    getSpecialChangeById(ref, CSpecialChangeID) {\n      this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n        body: CSpecialChangeID\n      }).subscribe(res => {\n        if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n          this.SpecialChange = res.Entries;\n          this.formSpecialChange = {\n            CApproveRemark: this.SpecialChange.CApproveRemark,\n            CBuildCaseID: this.buildCaseId,\n            CDrawingName: this.SpecialChange.CDrawingName,\n            CHouseID: this.houseId,\n            SpecialChangeFiles: null\n          };\n          if (this.SpecialChange.CChangeDate) {\n            this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n          }\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    onSaveSpecialChange(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n        body: this.formatParam()\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.getListSpecialChange();\n          ref.close();\n        }\n      });\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListSpecialChange();\n    }\n    addNew(ref) {\n      this.imageUrlList = [];\n      this.isEdit = false;\n      this.formSpecialChange = {\n        CApproveRemark: '',\n        CBuildCaseID: this.buildCaseId,\n        CChangeDate: '',\n        CDrawingName: '',\n        CHouseID: this.houseId,\n        SpecialChangeFiles: null\n      };\n      this.dialogService.open(ref);\n    }\n    onEdit(ref, specialChange) {\n      this.imageUrlList = [];\n      this.isEdit = true;\n      this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n      this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n      this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DD');\n      }\n      return '';\n    }\n    deleteDataFields(array) {\n      for (const item of array) {\n        delete item.data;\n      }\n      return array;\n    }\n    formatParam() {\n      const result = {\n        ...this.formSpecialChange,\n        SpecialChangeFiles: this.imageUrlList\n      };\n      this.deleteDataFields(result.SpecialChangeFiles);\n      if (this.formSpecialChange.CChangeDate) {\n        result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n      }\n      return result;\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    removeBase64Prefix(base64String) {\n      const prefixIndex = base64String.indexOf(\",\");\n      if (prefixIndex !== -1) {\n        return base64String.substring(prefixIndex + 1);\n      }\n      return base64String;\n    }\n    detectFiles(event) {\n      const files = event.target.files;\n      if (files && files.length > 0) {\n        const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n        const fileRegex = /pdf|jpg|jpeg|png/i;\n        for (let i = 0; i < files.length; i++) {\n          const file = files[i];\n          if (!fileRegex.test(file.type)) {\n            this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n          }\n          if (allowedTypes.includes(file.type)) {\n            const reader = new FileReader();\n            reader.onload = e => {\n              const fileType = file.type.startsWith('image/') ? 2 : 1;\n              this.imageUrlList.push({\n                data: e.target.result,\n                CFileBlood: this.removeBase64Prefix(e.target.result),\n                CFileName: file.name,\n                CFileType: fileType\n              });\n              if (this.imageUrlList.length === files.length) {\n                console.log('this.imageUrlList', this.imageUrlList);\n                if (this.fileInput) {\n                  this.fileInput.nativeElement.value = null;\n                }\n              }\n            };\n            reader.readAsDataURL(file);\n          }\n        }\n      }\n    }\n    isPDFString(str) {\n      if (str) {\n        return str.toLowerCase().endsWith(\".pdf\");\n      }\n      return false;\n    }\n    isImage(fileType) {\n      return fileType === 2;\n    }\n    isPdf(extension) {\n      return extension.toLowerCase() === 'pdf';\n    }\n    removeFile(index) {\n      this.imageUrlList.splice(index, 1);\n    }\n    removeImage(pictureId) {\n      this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n    }\n    uploadImage(ref) {}\n    renameFile(event, index) {\n      var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n      var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n        type: this.listPictures[index].CFile.type\n      });\n      this.listPictures[index].CFile = newFile;\n    }\n    goBack() {\n      this._eventService.push({\n        action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n        payload: this.buildCaseId\n      });\n      this.location.back();\n    }\n    openNewTab(url) {\n      if (url) window.open(url, \"_blank\");\n    }\n    static {\n      this.ɵfac = function CustomerChangePictureComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CustomerChangePictureComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.SpecialChangeService), i0.ɵɵdirectiveInject(i4.HouseService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MessageService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerChangePictureComponent,\n        selectors: [[\"ngx-customer-change-picture\"]],\n        viewQuery: function CustomerChangePictureComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        features: [i0.ɵɵInheritDefinitionFeature],\n        decls: 34,\n        vars: 6,\n        consts: [[\"dialogUploadDrawing\", \"\"], [\"CChangeDate\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [1, \"text-center\", 2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"text-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"min-width\", \"600px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"CChangeDate\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"inputId\", \"icondisplay\", \"dateFormat\", \"yy/mm/dd\", 1, \"!w-[400px]\", 3, \"ngModelChange\", \"appendTo\", \"iconDisplay\", \"showIcon\", \"ngModel\", \"disabled\", \"showButtonBar\"], [\"for\", \"cDrawingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5716\\u9762\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"baseLabel\", \"\", 1, \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"class\", \"flex flex-wrap mt-2\", 4, \"ngIf\"], [\"for\", \"cApproveRemark\", \"baseLabel\", \"\", 1, \"required-field\", \"align-self-start\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"name\", \"remark\", \"id\", \"cApproveRemark\", \"rows\", \"5\", \"nbInput\", \"\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-success m-2\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\"], [\"class\", \"relative w-24 h-24 mr-2 mb-2 border\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-24\", \"h-24\", \"mr-2\", \"mb-2\", \"border\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-4\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\"], [1, \"absolute\", \"top-0\", \"right-0\", \"cursor-pointer\", \"bg-white\", \"rounded-full\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-red-600\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\"], [\"class\", \"w-full h-full object-contain cursor-pointer\", 3, \"src\", \"click\", 4, \"ngIf\"], [\"class\", \"absolute inset-0 flex items-center justify-center cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-contain\", \"cursor-pointer\", 3, \"click\", \"src\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\", \"cursor-pointer\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"m-2\", 3, \"click\"]],\n        template: function CustomerChangePictureComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u4E0A\\u50B3\\u8207\\u8A72\\u6236\\u5225\\u5BA2\\u6236\\u8A0E\\u8AD6\\u7684\\u5BA2\\u6236\\u5716\\u9762\\uFF0C\\u5BE9\\u6838\\u901A\\u904E\\u5F8C\\u5BA2\\u6236\\u5C31\\u53EF\\u4EE5\\u5728\\u524D\\u53F0\\u6AA2\\u8996\\u8A72\\u5716\\u9762\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n            i0.ɵɵtemplate(9, CustomerChangePictureComponent_button_9_Template, 2, 0, \"button\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(10, \"div\", 9)(11, \"table\", 10)(12, \"thead\")(13, \"tr\", 11)(14, \"th\", 12);\n            i0.ɵɵtext(15, \"\\u8A0E\\u8AD6\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"th\", 12);\n            i0.ɵɵtext(17, \"\\u5716\\u9762\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"th\", 12);\n            i0.ɵɵtext(19, \"\\u4E0A\\u50B3\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"th\", 12);\n            i0.ɵɵtext(21, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"th\", 12);\n            i0.ɵɵtext(23, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(24, \"tbody\");\n            i0.ɵɵtemplate(25, CustomerChangePictureComponent_tr_25_Template, 11, 5, \"tr\", 13);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(26, \"nb-card-footer\", 14)(27, \"ngb-pagination\", 15);\n            i0.ɵɵtwoWayListener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function CustomerChangePictureComponent_Template_ngb_pagination_pageChange_27_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"nb-card-footer\")(29, \"div\", 14)(30, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function CustomerChangePictureComponent_Template_button_click_30_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.goBack());\n            });\n            i0.ɵɵtext(31, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(32, CustomerChangePictureComponent_ng_template_32_Template, 31, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \\u6236\\u5225\\u7BA1\\u7406 > \\u5BA2\\u8B8A\\u5716\\u4E0A\\u50B3 > \", ctx.houseTitle, \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.listSpecialChange);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [i7.NgForOf, i7.NgIf, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i10.NgbPagination, i11.BaseLabelDirective, i12.Calendar],\n        styles: [\"#icondisplay{width:318px}  [id^=pn_id_]{z-index:10}\"]\n      });\n    }\n  }\n  return CustomerChangePictureComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}