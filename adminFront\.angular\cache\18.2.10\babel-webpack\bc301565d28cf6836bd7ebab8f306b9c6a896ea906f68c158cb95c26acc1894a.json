{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFinalDocumentCreateFinalDocPost$Json } from '../fn/final-document/api-final-document-create-final-doc-post-json';\nimport { apiFinalDocumentCreateFinalDocPost$Plain } from '../fn/final-document/api-final-document-create-final-doc-post-plain';\nimport { apiFinalDocumentGetFinalDocAfterPost$Json } from '../fn/final-document/api-final-document-get-final-doc-after-post-json';\nimport { apiFinalDocumentGetFinalDocAfterPost$Plain } from '../fn/final-document/api-final-document-get-final-doc-after-post-plain';\nimport { apiFinalDocumentGetFinalDocBeforePost$Json } from '../fn/final-document/api-final-document-get-final-doc-before-post-json';\nimport { apiFinalDocumentGetFinalDocBeforePost$Plain } from '../fn/final-document/api-final-document-get-final-doc-before-post-plain';\nimport { apiFinalDocumentGetListFinalDocByHousePost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-json';\nimport { apiFinalDocumentGetListFinalDocByHousePost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-plain';\nimport { apiFinalDocumentGetListFinalDocPost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-post-json';\nimport { apiFinalDocumentGetListFinalDocPost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-post-plain';\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Json } from '../fn/final-document/api-final-document-get-list-special-change-available-post-json';\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Plain } from '../fn/final-document/api-final-document-get-list-special-change-available-post-plain';\nimport { apiFinalDocumentUpdateSignPost$Json } from '../fn/final-document/api-final-document-update-sign-post-json';\nimport { apiFinalDocumentUpdateSignPost$Plain } from '../fn/final-document/api-final-document-update-sign-post-plain';\nimport { apiFinalDocumentUploadFinalDocPost$Json } from '../fn/final-document/api-final-document-upload-final-doc-post-json';\nimport { apiFinalDocumentUploadFinalDocPost$Plain } from '../fn/final-document/api-final-document-upload-final-doc-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class FinalDocumentService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiFinalDocumentGetFinalDocBeforePost()` */\n  static {\n    this.ApiFinalDocumentGetFinalDocBeforePostPath = '/api/FinalDocument/GetFinalDocBefore';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params, context) {\n    return apiFinalDocumentGetFinalDocBeforePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocBeforePost$Plain(params, context) {\n    return this.apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocBeforePost$Json$Response(params, context) {\n    return apiFinalDocumentGetFinalDocBeforePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocBeforePost$Json(params, context) {\n    return this.apiFinalDocumentGetFinalDocBeforePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentUpdateSignPost()` */\n  static {\n    this.ApiFinalDocumentUpdateSignPostPath = '/api/FinalDocument/UpdateSign';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentUpdateSignPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentUpdateSignPost$Plain$Response(params, context) {\n    return apiFinalDocumentUpdateSignPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentUpdateSignPost$Plain(params, context) {\n    return this.apiFinalDocumentUpdateSignPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentUpdateSignPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentUpdateSignPost$Json$Response(params, context) {\n    return apiFinalDocumentUpdateSignPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentUpdateSignPost$Json(params, context) {\n    return this.apiFinalDocumentUpdateSignPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentGetFinalDocAfterPost()` */\n  static {\n    this.ApiFinalDocumentGetFinalDocAfterPostPath = '/api/FinalDocument/GetFinalDocAfter';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params, context) {\n    return apiFinalDocumentGetFinalDocAfterPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocAfterPost$Plain(params, context) {\n    return this.apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocAfterPost$Json$Response(params, context) {\n    return apiFinalDocumentGetFinalDocAfterPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetFinalDocAfterPost$Json(params, context) {\n    return this.apiFinalDocumentGetFinalDocAfterPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentGetListFinalDocPost()` */\n  static {\n    this.ApiFinalDocumentGetListFinalDocPostPath = '/api/FinalDocument/GetListFinalDoc';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocPost$Plain$Response(params, context) {\n    return apiFinalDocumentGetListFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocPost$Plain(params, context) {\n    return this.apiFinalDocumentGetListFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocPost$Json$Response(params, context) {\n    return apiFinalDocumentGetListFinalDocPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocPost$Json(params, context) {\n    return this.apiFinalDocumentGetListFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentCreateFinalDocPost()` */\n  static {\n    this.ApiFinalDocumentCreateFinalDocPostPath = '/api/FinalDocument/CreateFinalDoc';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentCreateFinalDocPost$Plain$Response(params, context) {\n    return apiFinalDocumentCreateFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentCreateFinalDocPost$Plain(params, context) {\n    return this.apiFinalDocumentCreateFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentCreateFinalDocPost$Json$Response(params, context) {\n    return apiFinalDocumentCreateFinalDocPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentCreateFinalDocPost$Json(params, context) {\n    return this.apiFinalDocumentCreateFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentGetListSpecialChangeAvailablePost()` */\n  static {\n    this.ApiFinalDocumentGetListSpecialChangeAvailablePostPath = '/api/FinalDocument/GetListSpecialChangeAvailable';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params, context) {\n    return apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(params, context) {\n    return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params, context) {\n    return apiFinalDocumentGetListSpecialChangeAvailablePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Json(params, context) {\n    return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentGetListFinalDocByHousePost()` */\n  static {\n    this.ApiFinalDocumentGetListFinalDocByHousePostPath = '/api/FinalDocument/GetListFinalDocByHouse';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params, context) {\n    return apiFinalDocumentGetListFinalDocByHousePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocByHousePost$Plain(params, context) {\n    return this.apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params, context) {\n    return apiFinalDocumentGetListFinalDocByHousePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFinalDocumentGetListFinalDocByHousePost$Json(params, context) {\n    return this.apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFinalDocumentUploadFinalDocPost()` */\n  static {\n    this.ApiFinalDocumentUploadFinalDocPostPath = '/api/FinalDocument/UploadFinalDoc';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiFinalDocumentUploadFinalDocPost$Plain$Response(params, context) {\n    return apiFinalDocumentUploadFinalDocPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiFinalDocumentUploadFinalDocPost$Plain(params, context) {\n    return this.apiFinalDocumentUploadFinalDocPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiFinalDocumentUploadFinalDocPost$Json$Response(params, context) {\n    return apiFinalDocumentUploadFinalDocPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiFinalDocumentUploadFinalDocPost$Json(params, context) {\n    return this.apiFinalDocumentUploadFinalDocPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function FinalDocumentService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FinalDocumentService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FinalDocumentService,\n      factory: FinalDocumentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiFinalDocumentCreateFinalDocPost$Json", "apiFinalDocumentCreateFinalDocPost$Plain", "apiFinalDocumentGetFinalDocAfterPost$Json", "apiFinalDocumentGetFinalDocAfterPost$Plain", "apiFinalDocumentGetFinalDocBeforePost$Json", "apiFinalDocumentGetFinalDocBeforePost$Plain", "apiFinalDocumentGetListFinalDocByHousePost$Json", "apiFinalDocumentGetListFinalDocByHousePost$Plain", "apiFinalDocumentGetListFinalDocPost$Json", "apiFinalDocumentGetListFinalDocPost$Plain", "apiFinalDocumentGetListSpecialChangeAvailablePost$Json", "apiFinalDocumentGetListSpecialChangeAvailablePost$Plain", "apiFinalDocumentUpdateSignPost$Json", "apiFinalDocumentUpdateSignPost$Plain", "apiFinalDocumentUploadFinalDocPost$Json", "apiFinalDocumentUploadFinalDocPost$Plain", "FinalDocumentService", "constructor", "config", "http", "ApiFinalDocumentGetFinalDocBeforePostPath", "apiFinalDocumentGetFinalDocBeforePost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiFinalDocumentGetFinalDocBeforePost$Json$Response", "ApiFinalDocumentUpdateSignPostPath", "apiFinalDocumentUpdateSignPost$Plain$Response", "apiFinalDocumentUpdateSignPost$Json$Response", "ApiFinalDocumentGetFinalDocAfterPostPath", "apiFinalDocumentGetFinalDocAfterPost$Plain$Response", "apiFinalDocumentGetFinalDocAfterPost$Json$Response", "ApiFinalDocumentGetListFinalDocPostPath", "apiFinalDocumentGetListFinalDocPost$Plain$Response", "apiFinalDocumentGetListFinalDocPost$Json$Response", "ApiFinalDocumentCreateFinalDocPostPath", "apiFinalDocumentCreateFinalDocPost$Plain$Response", "apiFinalDocumentCreateFinalDocPost$Json$Response", "ApiFinalDocumentGetListSpecialChangeAvailablePostPath", "apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response", "apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response", "ApiFinalDocumentGetListFinalDocByHousePostPath", "apiFinalDocumentGetListFinalDocByHousePost$Plain$Response", "apiFinalDocumentGetListFinalDocByHousePost$Json$Response", "ApiFinalDocumentUploadFinalDocPostPath", "apiFinalDocumentUploadFinalDocPost$Plain$Response", "apiFinalDocumentUploadFinalDocPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\final-document.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiFinalDocumentCreateFinalDocPost$Json } from '../fn/final-document/api-final-document-create-final-doc-post-json';\r\nimport { ApiFinalDocumentCreateFinalDocPost$Json$Params } from '../fn/final-document/api-final-document-create-final-doc-post-json';\r\nimport { apiFinalDocumentCreateFinalDocPost$Plain } from '../fn/final-document/api-final-document-create-final-doc-post-plain';\r\nimport { ApiFinalDocumentCreateFinalDocPost$Plain$Params } from '../fn/final-document/api-final-document-create-final-doc-post-plain';\r\nimport { apiFinalDocumentGetFinalDocAfterPost$Json } from '../fn/final-document/api-final-document-get-final-doc-after-post-json';\r\nimport { ApiFinalDocumentGetFinalDocAfterPost$Json$Params } from '../fn/final-document/api-final-document-get-final-doc-after-post-json';\r\nimport { apiFinalDocumentGetFinalDocAfterPost$Plain } from '../fn/final-document/api-final-document-get-final-doc-after-post-plain';\r\nimport { ApiFinalDocumentGetFinalDocAfterPost$Plain$Params } from '../fn/final-document/api-final-document-get-final-doc-after-post-plain';\r\nimport { apiFinalDocumentGetFinalDocBeforePost$Json } from '../fn/final-document/api-final-document-get-final-doc-before-post-json';\r\nimport { ApiFinalDocumentGetFinalDocBeforePost$Json$Params } from '../fn/final-document/api-final-document-get-final-doc-before-post-json';\r\nimport { apiFinalDocumentGetFinalDocBeforePost$Plain } from '../fn/final-document/api-final-document-get-final-doc-before-post-plain';\r\nimport { ApiFinalDocumentGetFinalDocBeforePost$Plain$Params } from '../fn/final-document/api-final-document-get-final-doc-before-post-plain';\r\nimport { apiFinalDocumentGetListFinalDocByHousePost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-json';\r\nimport { ApiFinalDocumentGetListFinalDocByHousePost$Json$Params } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-json';\r\nimport { apiFinalDocumentGetListFinalDocByHousePost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-plain';\r\nimport { ApiFinalDocumentGetListFinalDocByHousePost$Plain$Params } from '../fn/final-document/api-final-document-get-list-final-doc-by-house-post-plain';\r\nimport { apiFinalDocumentGetListFinalDocPost$Json } from '../fn/final-document/api-final-document-get-list-final-doc-post-json';\r\nimport { ApiFinalDocumentGetListFinalDocPost$Json$Params } from '../fn/final-document/api-final-document-get-list-final-doc-post-json';\r\nimport { apiFinalDocumentGetListFinalDocPost$Plain } from '../fn/final-document/api-final-document-get-list-final-doc-post-plain';\r\nimport { ApiFinalDocumentGetListFinalDocPost$Plain$Params } from '../fn/final-document/api-final-document-get-list-final-doc-post-plain';\r\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Json } from '../fn/final-document/api-final-document-get-list-special-change-available-post-json';\r\nimport { ApiFinalDocumentGetListSpecialChangeAvailablePost$Json$Params } from '../fn/final-document/api-final-document-get-list-special-change-available-post-json';\r\nimport { apiFinalDocumentGetListSpecialChangeAvailablePost$Plain } from '../fn/final-document/api-final-document-get-list-special-change-available-post-plain';\r\nimport { ApiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Params } from '../fn/final-document/api-final-document-get-list-special-change-available-post-plain';\r\nimport { apiFinalDocumentUpdateSignPost$Json } from '../fn/final-document/api-final-document-update-sign-post-json';\r\nimport { ApiFinalDocumentUpdateSignPost$Json$Params } from '../fn/final-document/api-final-document-update-sign-post-json';\r\nimport { apiFinalDocumentUpdateSignPost$Plain } from '../fn/final-document/api-final-document-update-sign-post-plain';\r\nimport { ApiFinalDocumentUpdateSignPost$Plain$Params } from '../fn/final-document/api-final-document-update-sign-post-plain';\r\nimport { apiFinalDocumentUploadFinalDocPost$Json } from '../fn/final-document/api-final-document-upload-final-doc-post-json';\r\nimport { ApiFinalDocumentUploadFinalDocPost$Json$Params } from '../fn/final-document/api-final-document-upload-final-doc-post-json';\r\nimport { apiFinalDocumentUploadFinalDocPost$Plain } from '../fn/final-document/api-final-document-upload-final-doc-post-plain';\r\nimport { ApiFinalDocumentUploadFinalDocPost$Plain$Params } from '../fn/final-document/api-final-document-upload-final-doc-post-plain';\r\nimport { GetFinalDocResListResponseBase } from '../models/get-final-doc-res-list-response-base';\r\nimport { GetListFinalDocResListResponseBase } from '../models/get-list-final-doc-res-list-response-base';\r\nimport { SpecialChangeAvailableResListResponseBase } from '../models/special-change-available-res-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\nimport { TblFinalDocumentListResponseBase } from '../models/tbl-final-document-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class FinalDocumentService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentGetFinalDocBeforePost()` */\r\n  static readonly ApiFinalDocumentGetFinalDocBeforePostPath = '/api/FinalDocument/GetFinalDocBefore';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params?: ApiFinalDocumentGetFinalDocBeforePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetFinalDocBeforePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocBeforePost$Plain(params?: ApiFinalDocumentGetFinalDocBeforePost$Plain$Params, context?: HttpContext): Observable<GetFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetFinalDocBeforePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetFinalDocResListResponseBase>): GetFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetFinalDocBeforePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocBeforePost$Json$Response(params?: ApiFinalDocumentGetFinalDocBeforePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetFinalDocBeforePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocBeforePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocBeforePost$Json(params?: ApiFinalDocumentGetFinalDocBeforePost$Json$Params, context?: HttpContext): Observable<GetFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetFinalDocBeforePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetFinalDocResListResponseBase>): GetFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentUpdateSignPost()` */\r\n  static readonly ApiFinalDocumentUpdateSignPostPath = '/api/FinalDocument/UpdateSign';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentUpdateSignPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentUpdateSignPost$Plain$Response(params?: ApiFinalDocumentUpdateSignPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentUpdateSignPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentUpdateSignPost$Plain(params?: ApiFinalDocumentUpdateSignPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentUpdateSignPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentUpdateSignPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentUpdateSignPost$Json$Response(params?: ApiFinalDocumentUpdateSignPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentUpdateSignPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentUpdateSignPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentUpdateSignPost$Json(params?: ApiFinalDocumentUpdateSignPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentUpdateSignPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentGetFinalDocAfterPost()` */\r\n  static readonly ApiFinalDocumentGetFinalDocAfterPostPath = '/api/FinalDocument/GetFinalDocAfter';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params?: ApiFinalDocumentGetFinalDocAfterPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetFinalDocAfterPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocAfterPost$Plain(params?: ApiFinalDocumentGetFinalDocAfterPost$Plain$Params, context?: HttpContext): Observable<GetFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetFinalDocAfterPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetFinalDocResListResponseBase>): GetFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetFinalDocAfterPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocAfterPost$Json$Response(params?: ApiFinalDocumentGetFinalDocAfterPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetFinalDocAfterPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetFinalDocAfterPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetFinalDocAfterPost$Json(params?: ApiFinalDocumentGetFinalDocAfterPost$Json$Params, context?: HttpContext): Observable<GetFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetFinalDocAfterPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetFinalDocResListResponseBase>): GetFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentGetListFinalDocPost()` */\r\n  static readonly ApiFinalDocumentGetListFinalDocPostPath = '/api/FinalDocument/GetListFinalDoc';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocPost$Plain$Response(params?: ApiFinalDocumentGetListFinalDocPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetListFinalDocPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocPost$Plain(params?: ApiFinalDocumentGetListFinalDocPost$Plain$Params, context?: HttpContext): Observable<GetListFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetListFinalDocPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListFinalDocResListResponseBase>): GetListFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocPost$Json$Response(params?: ApiFinalDocumentGetListFinalDocPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListFinalDocResListResponseBase>> {\r\n    return apiFinalDocumentGetListFinalDocPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocPost$Json(params?: ApiFinalDocumentGetListFinalDocPost$Json$Params, context?: HttpContext): Observable<GetListFinalDocResListResponseBase> {\r\n    return this.apiFinalDocumentGetListFinalDocPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListFinalDocResListResponseBase>): GetListFinalDocResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentCreateFinalDocPost()` */\r\n  static readonly ApiFinalDocumentCreateFinalDocPostPath = '/api/FinalDocument/CreateFinalDoc';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentCreateFinalDocPost$Plain$Response(params?: ApiFinalDocumentCreateFinalDocPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentCreateFinalDocPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentCreateFinalDocPost$Plain(params?: ApiFinalDocumentCreateFinalDocPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentCreateFinalDocPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentCreateFinalDocPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentCreateFinalDocPost$Json$Response(params?: ApiFinalDocumentCreateFinalDocPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentCreateFinalDocPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentCreateFinalDocPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentCreateFinalDocPost$Json(params?: ApiFinalDocumentCreateFinalDocPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentCreateFinalDocPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentGetListSpecialChangeAvailablePost()` */\r\n  static readonly ApiFinalDocumentGetListSpecialChangeAvailablePostPath = '/api/FinalDocument/GetListSpecialChangeAvailable';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params?: ApiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeAvailableResListResponseBase>> {\r\n    return apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Plain(params?: ApiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Params, context?: HttpContext): Observable<SpecialChangeAvailableResListResponseBase> {\r\n    return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeAvailableResListResponseBase>): SpecialChangeAvailableResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListSpecialChangeAvailablePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params?: ApiFinalDocumentGetListSpecialChangeAvailablePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeAvailableResListResponseBase>> {\r\n    return apiFinalDocumentGetListSpecialChangeAvailablePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListSpecialChangeAvailablePost$Json(params?: ApiFinalDocumentGetListSpecialChangeAvailablePost$Json$Params, context?: HttpContext): Observable<SpecialChangeAvailableResListResponseBase> {\r\n    return this.apiFinalDocumentGetListSpecialChangeAvailablePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeAvailableResListResponseBase>): SpecialChangeAvailableResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentGetListFinalDocByHousePost()` */\r\n  static readonly ApiFinalDocumentGetListFinalDocByHousePostPath = '/api/FinalDocument/GetListFinalDocByHouse';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params?: ApiFinalDocumentGetListFinalDocByHousePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<TblFinalDocumentListResponseBase>> {\r\n    return apiFinalDocumentGetListFinalDocByHousePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocByHousePost$Plain(params?: ApiFinalDocumentGetListFinalDocByHousePost$Plain$Params, context?: HttpContext): Observable<TblFinalDocumentListResponseBase> {\r\n    return this.apiFinalDocumentGetListFinalDocByHousePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblFinalDocumentListResponseBase>): TblFinalDocumentListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentGetListFinalDocByHousePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params?: ApiFinalDocumentGetListFinalDocByHousePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<TblFinalDocumentListResponseBase>> {\r\n    return apiFinalDocumentGetListFinalDocByHousePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentGetListFinalDocByHousePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFinalDocumentGetListFinalDocByHousePost$Json(params?: ApiFinalDocumentGetListFinalDocByHousePost$Json$Params, context?: HttpContext): Observable<TblFinalDocumentListResponseBase> {\r\n    return this.apiFinalDocumentGetListFinalDocByHousePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<TblFinalDocumentListResponseBase>): TblFinalDocumentListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFinalDocumentUploadFinalDocPost()` */\r\n  static readonly ApiFinalDocumentUploadFinalDocPostPath = '/api/FinalDocument/UploadFinalDoc';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiFinalDocumentUploadFinalDocPost$Plain$Response(params?: ApiFinalDocumentUploadFinalDocPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentUploadFinalDocPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiFinalDocumentUploadFinalDocPost$Plain(params?: ApiFinalDocumentUploadFinalDocPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentUploadFinalDocPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFinalDocumentUploadFinalDocPost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiFinalDocumentUploadFinalDocPost$Json$Response(params?: ApiFinalDocumentUploadFinalDocPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFinalDocumentUploadFinalDocPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFinalDocumentUploadFinalDocPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiFinalDocumentUploadFinalDocPost$Json(params?: ApiFinalDocumentUploadFinalDocPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFinalDocumentUploadFinalDocPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,uCAAuC,QAAQ,oEAAoE;AAE5H,SAASC,wCAAwC,QAAQ,qEAAqE;AAE9H,SAASC,yCAAyC,QAAQ,uEAAuE;AAEjI,SAASC,0CAA0C,QAAQ,wEAAwE;AAEnI,SAASC,0CAA0C,QAAQ,wEAAwE;AAEnI,SAASC,2CAA2C,QAAQ,yEAAyE;AAErI,SAASC,+CAA+C,QAAQ,+EAA+E;AAE/I,SAASC,gDAAgD,QAAQ,gFAAgF;AAEjJ,SAASC,wCAAwC,QAAQ,sEAAsE;AAE/H,SAASC,yCAAyC,QAAQ,uEAAuE;AAEjI,SAASC,sDAAsD,QAAQ,qFAAqF;AAE5J,SAASC,uDAAuD,QAAQ,sFAAsF;AAE9J,SAASC,mCAAmC,QAAQ,+DAA+D;AAEnH,SAASC,oCAAoC,QAAQ,gEAAgE;AAErH,SAASC,uCAAuC,QAAQ,oEAAoE;AAE5H,SAASC,wCAAwC,QAAQ,qEAAqE;;;;AAS9H,OAAM,MAAOC,oBAAqB,SAAQjB,WAAW;EACnDkB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,yCAAyC,GAAG,sCAAsC;EAAC;EAEnG;;;;;;EAMAC,oDAAoDA,CAACC,MAA2D,EAAEC,OAAqB;IACrI,OAAOlB,2CAA2C,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAlB,2CAA2CA,CAACiB,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACF,oDAAoD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpF3B,GAAG,CAAE4B,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAC,mDAAmDA,CAACN,MAA0D,EAAEC,OAAqB;IACnI,OAAOnB,0CAA0C,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAnB,0CAA0CA,CAACkB,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACK,mDAAmD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnF3B,GAAG,CAAE4B,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAE,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACR,MAAoD,EAAEC,OAAqB;IACvH,OAAOV,oCAAoC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAV,oCAAoCA,CAACS,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACO,6CAA6C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7E3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,4CAA4CA,CAACT,MAAmD,EAAEC,OAAqB;IACrH,OAAOX,mCAAmC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAX,mCAAmCA,CAACU,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACQ,4CAA4C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5E3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,wCAAwC,GAAG,qCAAqC;EAAC;EAEjG;;;;;;EAMAC,mDAAmDA,CAACX,MAA0D,EAAEC,OAAqB;IACnI,OAAOpB,0CAA0C,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMApB,0CAA0CA,CAACmB,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACU,mDAAmD,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnF3B,GAAG,CAAE4B,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAO,kDAAkDA,CAACZ,MAAyD,EAAEC,OAAqB;IACjI,OAAOrB,yCAAyC,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMArB,yCAAyCA,CAACoB,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACW,kDAAkD,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClF3B,GAAG,CAAE4B,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAQ,uCAAuC,GAAG,oCAAoC;EAAC;EAE/F;;;;;;EAMAC,kDAAkDA,CAACd,MAAyD,EAAEC,OAAqB;IACjI,OAAOd,yCAAyC,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAd,yCAAyCA,CAACa,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACa,kDAAkD,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClF3B,GAAG,CAAE4B,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;;;;;EAMAU,iDAAiDA,CAACf,MAAwD,EAAEC,OAAqB;IAC/H,OAAOf,wCAAwC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAf,wCAAwCA,CAACc,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACc,iDAAiD,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF3B,GAAG,CAAE4B,CAAyD,IAAyCA,CAAC,CAACC,IAAI,CAAC,CAC/G;EACH;EAEA;;IACgB,KAAAW,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAACjB,MAAwD,EAAEC,OAAqB;IAC/H,OAAOtB,wCAAwC,CAAC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAtB,wCAAwCA,CAACqB,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACgB,iDAAiD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,gDAAgDA,CAAClB,MAAuD,EAAEC,OAAqB;IAC7H,OAAOvB,uCAAuC,CAAC,IAAI,CAACmB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAvB,uCAAuCA,CAACsB,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACiB,gDAAgD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAc,qDAAqD,GAAG,kDAAkD;EAAC;EAE3H;;;;;;EAMAC,gEAAgEA,CAACpB,MAAuE,EAAEC,OAAqB;IAC7J,OAAOZ,uDAAuD,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1G;EAEA;;;;;;EAMAZ,uDAAuDA,CAACW,MAAuE,EAAEC,OAAqB;IACpJ,OAAO,IAAI,CAACmB,gEAAgE,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChG3B,GAAG,CAAE4B,CAAgE,IAAgDA,CAAC,CAACC,IAAI,CAAC,CAC7H;EACH;EAEA;;;;;;EAMAgB,+DAA+DA,CAACrB,MAAsE,EAAEC,OAAqB;IAC3J,OAAOb,sDAAsD,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzG;EAEA;;;;;;EAMAb,sDAAsDA,CAACY,MAAsE,EAAEC,OAAqB;IAClJ,OAAO,IAAI,CAACoB,+DAA+D,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/F3B,GAAG,CAAE4B,CAAgE,IAAgDA,CAAC,CAACC,IAAI,CAAC,CAC7H;EACH;EAEA;;IACgB,KAAAiB,8CAA8C,GAAG,2CAA2C;EAAC;EAE7G;;;;;;EAMAC,yDAAyDA,CAACvB,MAAgE,EAAEC,OAAqB;IAC/I,OAAOhB,gDAAgD,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnG;EAEA;;;;;;EAMAhB,gDAAgDA,CAACe,MAAgE,EAAEC,OAAqB;IACtI,OAAO,IAAI,CAACsB,yDAAyD,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzF3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAmB,wDAAwDA,CAACxB,MAA+D,EAAEC,OAAqB;IAC7I,OAAOjB,+CAA+C,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClG;EAEA;;;;;;EAMAjB,+CAA+CA,CAACgB,MAA+D,EAAEC,OAAqB;IACpI,OAAO,IAAI,CAACuB,wDAAwD,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxF3B,GAAG,CAAE4B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAoB,sCAAsC,GAAG,mCAAmC;EAAC;EAE7F;;;;;;EAMAC,iDAAiDA,CAAC1B,MAAwD,EAAEC,OAAqB;IAC/H,OAAOR,wCAAwC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC3F;EAEA;;;;;;EAMAR,wCAAwCA,CAACO,MAAwD,EAAEC,OAAqB;IACtH,OAAO,IAAI,CAACyB,iDAAiD,CAAC1B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACjF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAsB,gDAAgDA,CAAC3B,MAAuD,EAAEC,OAAqB;IAC7H,OAAOT,uCAAuC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAT,uCAAuCA,CAACQ,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAAC0B,gDAAgD,CAAC3B,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChF3B,GAAG,CAAE4B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA3XWX,oBAAoB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBvC,oBAAoB;MAAAwC,OAAA,EAApBxC,oBAAoB,CAAAyC,IAAA;MAAAC,UAAA,EADP;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}