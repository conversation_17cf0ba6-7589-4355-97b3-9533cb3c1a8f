{"ast": null, "code": "import { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor } from '@angular/common';\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as moment from 'moment';\nimport { FullCalendarModule } from '@fullcalendar/angular';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\nimport { CalendarModule } from 'primeng/calendar';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"primeng/calendar\";\nconst _c0 = [\"calendar\"];\nfunction FinaldochouseManagementComponent_tr_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 21)(1, \"td\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 24)(6, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_tr_30_Template_button_click_6_listener() {\n      const data_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openPdfInNewTab(data_r2));\n    });\n    i0.ɵɵtext(7, \"\\u9023\\u7D50\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const data_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r2.CDocumentName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r2.CSignDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !data_r2.CFileAfter && !data_r2.CFileBefore);\n  }\n}\nexport class FinaldochouseManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, finalDocumentService, pettern, router, route, destroyref, _eventService, location) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.finalDocumentService = finalDocumentService;\n    this.pettern = pettern;\n    this.router = router;\n    this.route = route;\n    this.destroyref = destroyref;\n    this._eventService = _eventService;\n    this.location = location;\n    this.calendarOptions = {\n      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, timeGridPlugin, bootstrapPlugin],\n      locale: 'zh-tw',\n      headerToolbar: {\n        left: 'prev',\n        center: 'title',\n        right: 'next'\n      }\n    };\n    // request\n    this.getListFinalDocRequest = {};\n    // response\n    this.listFinalDoc = [];\n    this.maxDate = new Date();\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.currentHouseID = id2;\n        this.getList();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data) {\n      if (data.CSignDate && data.CSign) {\n        console.log(data.CFileAfter);\n        window.open(data.CFileAfter, '_blank');\n      } else {\n        console.log(data.CFileBefore);\n        window.open(data.CFileBefore, '_blank');\n      }\n    }\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  getList() {\n    this.getListFinalDocRequest.PageSize = this.pageSize;\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\n    if (this.currentHouseID != 0) {\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({\n        body: this.getListFinalDocRequest\n      }).pipe().subscribe(res => {\n        if (res.StatusCode == 0) {\n          if (res.Entries) {\n            this.listFinalDoc = res.Entries;\n            this.totalRecords = res.TotalItems;\n            if (this.listFinalDoc) {\n              for (let i = 0; i < this.listFinalDoc.length; i++) {\n                if (this.listFinalDoc[i].CSignDate) this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\n              }\n            }\n          }\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function FinaldochouseManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FinaldochouseManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.FinalDocumentService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i8.ActivatedRoute), i0.ɵɵdirectiveInject(i0.DestroyRef), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.Location));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FinaldochouseManagementComponent,\n      selectors: [[\"app-finaldochouse-management\"]],\n      viewQuery: function FinaldochouseManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 36,\n      vars: 10,\n      consts: [[\"accent\", \"success\"], [1, \"bg-white\"], [1, \"col-12\"], [1, \"row\"], [1, \"flex\", \"form-group\", \"col-12\", \"col-md-9\", \"text-right\"], [\"for\", \"date-select1\", 1, \"mr-3\", \"mt-2\"], [\"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", \"dateFormat\", \"yy/mm/dd\", 3, \"ngModelChange\", \"appendTo\", \"ngModel\", \"maxDate\"], [\"for\", \"date-select1\", 1, \"mr-1\", \"ml-1\", \"mt-2\"], [1, \"form-group\", \"col-12\", \"col-md-3\", \"text-right\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-5\"], [\"scope\", \"col\", 1, \"col-4\"], [\"scope\", \"col\", 1, \"col-3\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"d-flex\"], [1, \"col-5\"], [1, \"col-4\"], [1, \"col-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\", \"disabled\"]],\n      template: function FinaldochouseManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 1)(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"span\", 5);\n          i0.ɵɵtext(8, \" \\u5EFA\\u7ACB\\u6642\\u9593 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p-calendar\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateStart, $event) || (ctx.getListFinalDocRequest.CDateStart = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p-calendar\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_12_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.getListFinalDocRequest.CDateEnd, $event) || (ctx.getListFinalDocRequest.CDateEnd = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_14_listener() {\n            return ctx.getList();\n          });\n          i0.ɵɵelement(15, \"i\", 10);\n          i0.ɵɵtext(16, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(17, \"nb-card-body\", 1)(18, \"div\", 2)(19, \"div\", 11)(20, \"table\", 12)(21, \"thead\")(22, \"tr\", 13)(23, \"th\", 14);\n          i0.ɵɵtext(24, \"\\u6587\\u4EF6\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"th\", 15);\n          i0.ɵɵtext(26, \"\\u5BA2\\u6236\\u7C3D\\u540D\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"th\", 16);\n          i0.ɵɵtext(28, \"\\u9023\\u7D50\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"tbody\");\n          i0.ɵɵtemplate(30, FinaldochouseManagementComponent_tr_30_Template, 8, 3, \"tr\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"ngx-pagination\", 18);\n          i0.ɵɵtwoWayListener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"PageChange\", function FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_31_listener() {\n            return ctx.getList();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 19)(34, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function FinaldochouseManagementComponent_Template_button_click_34_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(35, \" \\u8FD4\\u56DE\\u4E0A\\u4E00\\u9801 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateStart);\n          i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"appendTo\", \"body\");\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListFinalDocRequest.CDateEnd);\n          i0.ɵɵproperty(\"maxDate\", ctx.maxDate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.listFinalDoc);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, FormsModule, i11.NgControlStatus, i11.NgModel, NbSelectModule, NbOptionModule, NgFor, PaginationComponent, NbCheckboxModule, FullCalendarModule, CalendarModule, i12.Calendar],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJmaW5hbGRvY2hvdXNlLW1hbmFnZW1lbnQuY29tcG9uZW50LnNjc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvaG91c2Vob2xkLW1hbmFnZW1lbnQvZmluYWxkb2Nob3VzZS1tYW5hZ2VtZW50L2ZpbmFsZG9jaG91c2UtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNExBQTRMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "PaginationComponent", "BaseComponent", "moment", "FullCalendarModule", "interactionPlugin", "dayGridPlugin", "timeGridPlugin", "listPlugin", "bootstrapPlugin", "CalendarModule", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "FinaldochouseManagementComponent_tr_30_Template_button_click_6_listener", "data_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openPdfInNewTab", "ɵɵadvance", "ɵɵtextInterpolate", "CDocumentName", "CSignDate", "ɵɵproperty", "CFileAfter", "CFileBefore", "FinaldochouseManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "finalDocumentService", "pettern", "router", "route", "destroyref", "_eventService", "location", "calendarOptions", "plugins", "locale", "headerToolbar", "left", "center", "right", "getListFinalDocRequest", "listFinalDoc", "maxDate", "Date", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "currentHouseID", "getList", "data", "CSign", "console", "log", "window", "open", "goBack", "push", "action", "payload", "back", "PageSize", "pageSize", "PageIndex", "pageIndex", "CHouseID", "apiFinalDocumentGetListFinalDocByHousePost$Json", "body", "pipe", "res", "StatusCode", "Entries", "totalRecords", "TotalItems", "i", "length", "format", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "FinalDocumentService", "i7", "PetternHelper", "i8", "Router", "ActivatedRoute", "DestroyRef", "i9", "EventService", "i10", "Location", "selectors", "viewQuery", "FinaldochouseManagementComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵtwoWayListener", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_9_listener", "$event", "ɵɵtwoWayBindingSet", "CDateStart", "FinaldochouseManagementComponent_Template_p_calendar_ngModelChange_12_listener", "CDateEnd", "FinaldochouseManagementComponent_Template_button_click_14_listener", "ɵɵtemplate", "FinaldochouseManagementComponent_tr_30_Template", "FinaldochouseManagementComponent_Template_ngx_pagination_PageChange_31_listener", "FinaldochouseManagementComponent_Template_button_click_34_listener", "ɵɵtwoWayProperty", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "i11", "NgControlStatus", "NgModel", "i12", "Calendar", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\finaldochouse-management\\finaldochouse-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\household-management\\finaldochouse-management\\finaldochouse-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule } from '@nebular/theme';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { FinalDocumentService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { GetFinalDocListByHouse, TblFinalDocument } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { Calendar, CalendarOptions } from 'fullcalendar';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport bootstrapPlugin from '@fullcalendar/bootstrap';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Location } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-finaldochouse-management',\r\n  standalone: true,\r\n  imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    NbCheckboxModule,\r\n    FullCalendarModule,\r\n    CalendarModule\r\n  ],\r\n  templateUrl: './finaldochouse-management.component.html',\r\n  styleUrl: './finaldochouse-management.component.scss'\r\n})\r\nexport class FinaldochouseManagementComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('calendar') calendarComponent: FullCalendarComponent;\r\n  calendarApi: Calendar;\r\n  maxDate!: Date;\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    plugins: [\r\n      interactionPlugin,\r\n      dayGridPlugin,\r\n      timeGridPlugin,\r\n      listPlugin,\r\n      timeGridPlugin,\r\n      bootstrapPlugin\r\n    ],\r\n    locale: 'zh-tw',\r\n    headerToolbar: {\r\n      left: 'prev',\r\n      center: 'title',\r\n      right: 'next'\r\n    },\r\n  };\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private finalDocumentService: FinalDocumentService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private destroyref: DestroyRef,\r\n    private _eventService: EventService,\r\n    private location: Location,\r\n  ) {\r\n    super(_allow);\r\n    this.maxDate = new Date();\r\n  }\r\n\r\n  currentHouseID: number;\r\n  buildCaseId: number;\r\n  // request\r\n  getListFinalDocRequest: GetFinalDocListByHouse = {};\r\n\r\n  // response\r\n  listFinalDoc: TblFinalDocument[] = [];\r\n\r\n  override ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id;\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.currentHouseID = id2;\r\n        this.getList();\r\n      }\r\n    });\r\n  }\r\n\r\n  openPdfInNewTab(data: TblFinalDocument) {\r\n    if (data) {\r\n      if (data.CSignDate && data.CSign) {\r\n        console.log(data.CFileAfter);\r\n        window.open(data.CFileAfter!, '_blank');\r\n      }\r\n      else {\r\n        console.log(data.CFileBefore);\r\n        window.open(data.CFileBefore!, '_blank');\r\n      }\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n\r\n  getList() {\r\n    this.getListFinalDocRequest.PageSize = this.pageSize;\r\n    this.getListFinalDocRequest.PageIndex = this.pageIndex;\r\n    if (this.currentHouseID != 0) {\r\n      this.getListFinalDocRequest.CHouseID = this.currentHouseID;\r\n      this.finalDocumentService.apiFinalDocumentGetListFinalDocByHousePost$Json({ body: this.getListFinalDocRequest })\r\n        .pipe()\r\n        .subscribe(res => {\r\n          if (res.StatusCode == 0) {\r\n            if (res.Entries) {\r\n              this.listFinalDoc = res.Entries;\r\n              this.totalRecords = res.TotalItems!;\r\n              if (this.listFinalDoc) {\r\n                for (let i = 0; i < this.listFinalDoc.length; i++) {\r\n                  if (this.listFinalDoc[i].CSignDate)\r\n                    this.listFinalDoc[i].CSignDate = moment(this.listFinalDoc[i].CSignDate).format(\"yyyy/MM/DD H:mm:ss\");\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"flex form-group col-12 col-md-9 text-right\">\r\n          <span for=\"date-select1\" class=\"mr-3 mt-2\">\r\n            建立時間\r\n          </span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateStart\"\r\n            [maxDate]=\"maxDate\"></p-calendar>\r\n          <span for=\"date-select1\" class=\"mr-1 ml-1 mt-2\">~</span>\r\n          <p-calendar [appendTo]=\"'body'\" placeholder='年/月/日' dateFormat=\"yy/mm/dd\"\r\n            [(ngModel)]=\"getListFinalDocRequest.CDateEnd\"\r\n            [maxDate]=\"maxDate\"></p-calendar>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-3 text-right\">\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <nb-card-body class=\"bg-white\">\r\n    <div class=\"col-12\">\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n              <th scope=\"col\" class=\"col-5\">文件名稱</th>\r\n              <th scope=\"col\" class=\"col-4\">客戶簽名時間</th>\r\n              <th scope=\"col\" class=\"col-3\">連結</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let data of listFinalDoc; let i = index\" class=\"d-flex\">\r\n              <td class=\"col-5\">{{ data.CDocumentName }}</td>\r\n              <td class=\"col-4\">{{ data.CSignDate }}</td>\r\n              <td class=\"col-3\">\r\n                <button class=\"btn btn-outline-primary btn-sm m-1\" [disabled]=\"!data.CFileAfter && !data.CFileBefore\"\r\n                (click)=\"openPdfInNewTab(data)\">連結</button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n        (PageChange)=\"getList()\">\r\n      </ngx-pagination>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer>\r\n    <div class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm\"  (click)=\"goBack()\">\r\n        返回上一頁\r\n      </button>\r\n    </div>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/H,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAc,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,kDAAkD;AAQtF,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAgCC,kBAAkB,QAAQ,uBAAuB;AAEjF,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,SAASC,cAAc,QAAQ,kBAAkB;AAEjD,SAAuBC,MAAM,QAAQ,uCAAuC;;;;;;;;;;;;;;;;;;ICc9DC,EADF,CAAAC,cAAA,aAAoE,aAChD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzCH,EADF,CAAAC,cAAA,aAAkB,iBAEgB;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,OAAA,CAAqB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,mBAAE;IAEtCF,EAFsC,CAAAG,YAAA,EAAS,EACxC,EACF;;;;IANeH,EAAA,CAAAc,SAAA,GAAwB;IAAxBd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAU,aAAA,CAAwB;IACxBhB,EAAA,CAAAc,SAAA,GAAoB;IAApBd,EAAA,CAAAe,iBAAA,CAAAT,OAAA,CAAAW,SAAA,CAAoB;IAEejB,EAAA,CAAAc,SAAA,GAAkD;IAAlDd,EAAA,CAAAkB,UAAA,cAAAZ,OAAA,CAAAa,UAAA,KAAAb,OAAA,CAAAc,WAAA,CAAkD;;;ADMrH,OAAM,MAAOC,gCAAiC,SAAQ/B,aAAa;EAsBjEgC,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,oBAA0C,EAC1CC,OAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,UAAsB,EACtBC,aAA2B,EAC3BC,QAAkB;IAE1B,KAAK,CAACX,MAAM,CAAC;IAbL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IA7BlB,KAAAC,eAAe,GAAoB;MACjCC,OAAO,EAAE,CACP3C,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVD,cAAc,EACdE,eAAe,CAChB;MACDwC,MAAM,EAAE,OAAO;MACfC,aAAa,EAAE;QACbC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;;KAEV;IAsBD;IACA,KAAAC,sBAAsB,GAA2B,EAAE;IAEnD;IACA,KAAAC,YAAY,GAAuB,EAAE;IATnC,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE;EAC3B;EAUSC,QAAQA,CAAA;IACf,IAAI,CAACf,KAAK,CAACgB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,cAAc,GAAGD,GAAG;QACzB,IAAI,CAACE,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACJ;EAEA5C,eAAeA,CAAC6C,IAAsB;IACpC,IAAIA,IAAI,EAAE;MACR,IAAIA,IAAI,CAACzC,SAAS,IAAIyC,IAAI,CAACC,KAAK,EAAE;QAChCC,OAAO,CAACC,GAAG,CAACH,IAAI,CAACvC,UAAU,CAAC;QAC5B2C,MAAM,CAACC,IAAI,CAACL,IAAI,CAACvC,UAAW,EAAE,QAAQ,CAAC;MACzC,CAAC,MACI;QACHyC,OAAO,CAACC,GAAG,CAACH,IAAI,CAACtC,WAAW,CAAC;QAC7B0C,MAAM,CAACC,IAAI,CAACL,IAAI,CAACtC,WAAY,EAAE,QAAQ,CAAC;MAC1C;IACF;EACF;EAEA4C,MAAMA,CAAA;IACJ,IAAI,CAAC/B,aAAa,CAACgC,IAAI,CAAC;MACtBC,MAAM;MACNC,OAAO,EAAE,IAAI,CAACd;KACf,CAAC;IACF,IAAI,CAACnB,QAAQ,CAACkC,IAAI,EAAE;EACtB;EAEAX,OAAOA,CAAA;IACL,IAAI,CAACf,sBAAsB,CAAC2B,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACpD,IAAI,CAAC5B,sBAAsB,CAAC6B,SAAS,GAAG,IAAI,CAACC,SAAS;IACtD,IAAI,IAAI,CAAChB,cAAc,IAAI,CAAC,EAAE;MAC5B,IAAI,CAACd,sBAAsB,CAAC+B,QAAQ,GAAG,IAAI,CAACjB,cAAc;MAC1D,IAAI,CAAC5B,oBAAoB,CAAC8C,+CAA+C,CAAC;QAAEC,IAAI,EAAE,IAAI,CAACjC;MAAsB,CAAE,CAAC,CAC7GkC,IAAI,EAAE,CACN5B,SAAS,CAAC6B,GAAG,IAAG;QACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAID,GAAG,CAACE,OAAO,EAAE;YACf,IAAI,CAACpC,YAAY,GAAGkC,GAAG,CAACE,OAAO;YAC/B,IAAI,CAACC,YAAY,GAAGH,GAAG,CAACI,UAAW;YACnC,IAAI,IAAI,CAACtC,YAAY,EAAE;cACrB,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvC,YAAY,CAACwC,MAAM,EAAED,CAAC,EAAE,EAAE;gBACjD,IAAI,IAAI,CAACvC,YAAY,CAACuC,CAAC,CAAC,CAACjE,SAAS,EAChC,IAAI,CAAC0B,YAAY,CAACuC,CAAC,CAAC,CAACjE,SAAS,GAAG1B,MAAM,CAAC,IAAI,CAACoD,YAAY,CAACuC,CAAC,CAAC,CAACjE,SAAS,CAAC,CAACmE,MAAM,CAAC,oBAAoB,CAAC;cACxG;YACF;UACF;QACF;MACF,CAAC,CAAC;IACN;EACF;;;uCAzGW/D,gCAAgC,EAAArB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA3F,EAAA,CAAAqF,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7F,EAAA,CAAAqF,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA/F,EAAA,CAAAqF,iBAAA,CAAAW,EAAA,CAAAC,oBAAA,GAAAjG,EAAA,CAAAqF,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAnG,EAAA,CAAAqF,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAArG,EAAA,CAAAqF,iBAAA,CAAAe,EAAA,CAAAE,cAAA,GAAAtG,EAAA,CAAAqF,iBAAA,CAAArF,EAAA,CAAAuG,UAAA,GAAAvG,EAAA,CAAAqF,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAAzG,EAAA,CAAAqF,iBAAA,CAAAqB,GAAA,CAAAC,QAAA;IAAA;EAAA;;;YAAhCtF,gCAAgC;MAAAuF,SAAA;MAAAC,SAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UC/C3C/G,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAiH,SAAA,qBAAiC;UACnCjH,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,sBAA+B,aACT,aACD,aACyC,cACX;UACzCD,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,oBAEsB;UADpBD,EAAA,CAAAkH,gBAAA,2BAAAC,8EAAAC,MAAA;YAAApH,EAAA,CAAAqH,kBAAA,CAAAL,GAAA,CAAAtE,sBAAA,CAAA4E,UAAA,EAAAF,MAAA,MAAAJ,GAAA,CAAAtE,sBAAA,CAAA4E,UAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA+C;UAC3BpH,EAAA,CAAAG,YAAA,EAAa;UACnCH,EAAA,CAAAC,cAAA,eAAgD;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxDH,EAAA,CAAAC,cAAA,qBAEsB;UADpBD,EAAA,CAAAkH,gBAAA,2BAAAK,+EAAAH,MAAA;YAAApH,EAAA,CAAAqH,kBAAA,CAAAL,GAAA,CAAAtE,sBAAA,CAAA8E,QAAA,EAAAJ,MAAA,MAAAJ,GAAA,CAAAtE,sBAAA,CAAA8E,QAAA,GAAAJ,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA6C;UAEjDpH,EADwB,CAAAG,YAAA,EAAa,EAC/B;UAEJH,EADF,CAAAC,cAAA,cAAmD,iBACK;UAApBD,EAAA,CAAAI,UAAA,mBAAAqH,mEAAA;YAAA,OAAST,GAAA,CAAAvD,OAAA,EAAS;UAAA,EAAC;UAACzD,EAAA,CAAAiH,SAAA,aAAkC;UAAAjH,EAAA,CAAAE,MAAA,oBAAE;UAIlGF,EAJkG,CAAAG,YAAA,EAAS,EAC/F,EACF,EACF,EACO;UAQHH,EANZ,CAAAC,cAAA,uBAA+B,cACT,eACY,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA0H,UAAA,KAAAC,+CAAA,iBAAoE;UAU1E3H,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAkH,gBAAA,wBAAAU,gFAAAR,MAAA;YAAApH,EAAA,CAAAqH,kBAAA,CAAAL,GAAA,CAAAxC,SAAA,EAAA4C,MAAA,MAAAJ,GAAA,CAAAxC,SAAA,GAAA4C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAClEpH,EAAA,CAAAI,UAAA,wBAAAwH,gFAAA;YAAA,OAAcZ,GAAA,CAAAvD,OAAA,EAAS;UAAA,EAAC;UAG9BzD,EAFI,CAAAG,YAAA,EAAiB,EACb,EACO;UAGXH,EAFJ,CAAAC,cAAA,sBAAgB,eAC6B,kBACoB;UAAnBD,EAAA,CAAAI,UAAA,mBAAAyH,mEAAA;YAAA,OAASb,GAAA,CAAAhD,MAAA,EAAQ;UAAA,EAAC;UAC1DhE,EAAA,CAAAE,MAAA,wCACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;UAlDYH,EAAA,CAAAc,SAAA,GAAmB;UAAnBd,EAAA,CAAAkB,UAAA,oBAAmB;UAC7BlB,EAAA,CAAA8H,gBAAA,YAAAd,GAAA,CAAAtE,sBAAA,CAAA4E,UAAA,CAA+C;UAC/CtH,EAAA,CAAAkB,UAAA,YAAA8F,GAAA,CAAApE,OAAA,CAAmB;UAET5C,EAAA,CAAAc,SAAA,GAAmB;UAAnBd,EAAA,CAAAkB,UAAA,oBAAmB;UAC7BlB,EAAA,CAAA8H,gBAAA,YAAAd,GAAA,CAAAtE,sBAAA,CAAA8E,QAAA,CAA6C;UAC7CxH,EAAA,CAAAkB,UAAA,YAAA8F,GAAA,CAAApE,OAAA,CAAmB;UAqBE5C,EAAA,CAAAc,SAAA,IAAiB;UAAjBd,EAAA,CAAAkB,UAAA,YAAA8F,GAAA,CAAArE,YAAA,CAAiB;UAW5B3C,EAAA,CAAAc,SAAA,EAA+B;UAA/Bd,EAAA,CAAAkB,UAAA,mBAAA8F,GAAA,CAAAhC,YAAA,CAA+B;UAAChF,EAAA,CAAA8H,gBAAA,SAAAd,GAAA,CAAAxC,SAAA,CAAoB;UAACxE,EAAA,CAAAkB,UAAA,aAAA8F,GAAA,CAAA1C,QAAA,CAAqB;;;qBDjB5FzF,YAAY,EAAA6G,EAAA,CAAAqC,eAAA,EAAArC,EAAA,CAAAsC,mBAAA,EAAAtC,EAAA,CAAAuC,qBAAA,EAAAvC,EAAA,CAAAwC,qBAAA,EACZhJ,mBAAmB,EACnBH,aAAa,EACbI,WAAW,EAAAgJ,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,OAAA,EACXpJ,cAAc,EACdD,cAAc,EAEdI,KAAK,EACLC,mBAAmB,EACnBP,gBAAgB,EAChBU,kBAAkB,EAClBM,cAAc,EAAAwI,GAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}