{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param {number} weeks - number of weeks to be converted\n *\n * @returns {number} the number of weeks converted in days\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport default function weeksToDays(weeks) {\n  requiredArgs(1, arguments);\n  return Math.floor(weeks * daysInWeek);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}