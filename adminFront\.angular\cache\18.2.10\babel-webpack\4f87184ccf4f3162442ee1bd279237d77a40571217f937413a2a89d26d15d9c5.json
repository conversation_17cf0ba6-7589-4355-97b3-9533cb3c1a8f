{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { CQuotationItemType } from 'src/app/models/quotation.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"src/app/services/quotation.service\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/forms\";\nimport * as i14 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i15 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i16 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_74_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(114);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(112);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 53);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_108_button_20_Template, 2, 0, \"button\", 54);\n    i0.ɵɵelementStart(21, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_21_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(22, \" \\u6D3D\\u8AC7\\u7D00\\u9304 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_23_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(24, \" \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_25_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(26, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_27_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.resetSecureKey(item_r15));\n    });\n    i0.ɵɵtext(28, \" \\u91CD\\u7F6E\\u5BC6\\u78BC \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_108_Template_button_click_29_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogQuotation_r17 = i0.ɵɵreference(116);\n      return i0.ɵɵresetView(ctx_r10.openQuotation(dialogQuotation_r17, item_r15));\n    });\n    i0.ɵɵtext(30, \" \\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.getQuotationStatusText(item_r15.CQuotationStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"label\", 91);\n    i0.ɵɵtext(2, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r25);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r25.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"label\", 93);\n    i0.ɵɵtext(2, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 94);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 61)(1, \"div\", 62)(2, \"label\", 63);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 62)(7, \"label\", 65);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 62)(11, \"label\", 67);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 62)(15, \"label\", 69);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 62)(19, \"label\", 71);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 62)(23, \"label\", 73);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 74);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 62)(27, \"label\", 75);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 62)(31, \"label\", 77);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 78);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template, 5, 2, \"div\", 79)(36, HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template, 5, 2, \"div\", 79);\n    i0.ɵɵelementStart(37, \"div\", 62)(38, \"label\", 80);\n    i0.ɵɵtext(39, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(41, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 62)(43, \"label\", 82);\n    i0.ɵɵtext(44, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(46, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 83)(48, \"label\", 84);\n    i0.ɵɵtext(49, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 85)(51, \"nb-form-field\", 86);\n    i0.ɵɵelement(52, \"nb-icon\", 87);\n    i0.ɵɵelementStart(53, \"input\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(54, \"nb-datepicker\", 89, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"nb-form-field\", 86);\n    i0.ɵɵelement(57, \"nb-icon\", 87);\n    i0.ɵɵelementStart(58, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"nb-datepicker\", 89, 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r26 = i0.ɵɵreference(55);\n    const EndDate_r27 = i0.ɵɵreference(60);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangePayStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isChangeProgress);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r27);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ref_r28 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r28));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 57);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template, 61, 18, \"nb-card-body\", 58);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 50)(3, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r28));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_111_button_5_Template, 2, 0, \"button\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ref_r31 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r31));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 57)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 61)(4, \"div\", 62)(5, \"label\", 96);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 62)(9, \"label\", 98);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 62)(13, \"label\", 100);\n    i0.ɵɵtext(14, \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 101);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 50)(17, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener() {\n      const ref_r31 = i0.ɵɵrestoreView(_r30).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r31));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_113_button_19_Template, 2, 0, \"button\", 103);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 160)(1, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.addQuotationItem());\n    });\n    i0.ɵɵtext(2, \" + \\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"button\", 161);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadDefaultItems());\n    });\n    i0.ɵɵtext(5, \" \\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.loadRegularItems());\n    });\n    i0.ɵɵtext(7, \" \\u8F09\\u5165\\u9078\\u6A23\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 162);\n    i0.ɵɵelement(1, \"i\", 163);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" - \\u6B64\\u5831\\u50F9\\u55AE\\u5DF2\\u9396\\u5B9A\\uFF0C\\u7121\\u6CD5\\u9032\\u884C\\u4FEE\\u6539\\u3002\\u60A8\\u53EF\\u4EE5\\u5217\\u5370\\u6B64\\u5831\\u50F9\\u55AE\\u6216\\u7522\\u751F\\u65B0\\u7684\\u5831\\u50F9\\u55AE\\u3002 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_64_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_tr_64_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const i_r38 = i0.ɵɵnextContext().index;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.removeQuotationItem(i_r38));\n    });\n    i0.ɵɵtext(1, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_64_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 173);\n    i0.ɵɵelement(1, \"i\", 174);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 164);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_2_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cItemName, $event) || (item_r36.cItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"input\", 165);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_4_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnitPrice, $event) || (item_r36.cUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\")(6, \"input\", 166);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_6_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cUnit, $event) || (item_r36.cUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 167);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_8_listener($event) {\n      const item_r36 = i0.ɵɵrestoreView(_r35).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r36.cCount, $event) || (item_r36.cCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_8_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.calculateTotal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 168);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 169);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtemplate(15, HouseholdManagementComponent_ng_template_115_tr_64_button_15_Template, 2, 0, \"button\", 170)(16, HouseholdManagementComponent_ng_template_115_tr_64_span_16_Template, 2, 0, \"span\", 171);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r36 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cItemName);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnitPrice);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-light\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cUnit);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 1 || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r36.cCount);\n    i0.ɵɵproperty(\"disabled\", !ctx_r10.isQuotationEditable || item_r36.CQuotationItemType === 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.formatCurrency(item_r36.cUnitPrice * item_r36.cCount), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"badge-primary\", item_r36.CQuotationItemType === 1)(\"badge-info\", item_r36.CQuotationItemType === 3)(\"badge-secondary\", item_r36.CQuotationItemType !== 1 && item_r36.CQuotationItemType !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getQuotationTypeText(item_r36.CQuotationItemType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_tr_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 175);\n    i0.ɵɵtext(2, \" \\u8ACB\\u9EDE\\u64CA\\u300C\\u65B0\\u589E\\u81EA\\u5B9A\\u7FA9\\u9805\\u76EE\\u300D\\u6216\\u300C\\u8F09\\u5165\\u5BA2\\u8B8A\\u9700\\u6C42\\u300D\\u958B\\u59CB\\u5EFA\\u7ACB\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 176);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_102_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.createNewQuotation());\n    });\n    i0.ɵɵelement(1, \"i\", 177);\n    i0.ɵɵtext(2, \" \\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 178);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_106_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.lockQuotation(ref_r40));\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_button_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 179);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_button_107_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ref_r40 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveQuotation(ref_r40));\n    });\n    i0.ɵɵtext(1, \" \\u5132\\u5B58\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_115_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 105)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 106)(5, \"div\", 107)(6, \"div\", 9)(7, \"div\", 108)(8, \"div\", 109);\n    i0.ɵɵelement(9, \"i\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 111)(11, \"div\", 112);\n    i0.ɵɵtext(12, \"\\u5831\\u50F9\\u55AE\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 113);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 108)(17, \"div\", 114);\n    i0.ɵɵelement(18, \"i\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 111)(20, \"div\", 112);\n    i0.ɵɵtext(21, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 113)(23, \"span\", 116);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 9)(26, \"div\", 108)(27, \"div\", 117);\n    i0.ɵɵelement(28, \"i\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 111)(30, \"div\", 112);\n    i0.ɵɵtext(31, \"\\u5EFA\\u7ACB\\u65E5\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 113);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 9)(35, \"div\", 119)(36, \"div\", 120);\n    i0.ɵɵelement(37, \"i\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 111)(39, \"div\", 112);\n    i0.ɵɵtext(40, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 122);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(43, HouseholdManagementComponent_ng_template_115_div_43_Template, 8, 0, \"div\", 123)(44, HouseholdManagementComponent_ng_template_115_div_44_Template, 5, 0, \"div\", 124);\n    i0.ɵɵelementStart(45, \"div\", 125)(46, \"table\", 126)(47, \"thead\")(48, \"tr\")(49, \"th\", 127);\n    i0.ɵɵtext(50, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"th\", 128);\n    i0.ɵɵtext(52, \"\\u55AE\\u50F9 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"th\", 129);\n    i0.ɵɵtext(54, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"th\", 129);\n    i0.ɵɵtext(56, \"\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"th\", 130);\n    i0.ɵɵtext(58, \"\\u5C0F\\u8A08 (\\u5143)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"th\", 131);\n    i0.ɵɵtext(60, \"\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"th\", 131);\n    i0.ɵɵtext(62, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"tbody\");\n    i0.ɵɵtemplate(64, HouseholdManagementComponent_ng_template_115_tr_64_Template, 17, 22, \"tr\", 49)(65, HouseholdManagementComponent_ng_template_115_tr_65_Template, 3, 0, \"tr\", 132);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(66, \"div\", 133)(67, \"div\", 134)(68, \"div\", 135)(69, \"div\", 136)(70, \"span\", 137);\n    i0.ɵɵtext(71, \"\\u5C0F\\u8A08\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"span\", 138);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(74, \"div\", 139)(75, \"div\", 140)(76, \"div\", 141);\n    i0.ɵɵelement(77, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"div\")(79, \"span\", 143);\n    i0.ɵɵtext(80, \"\\u71DF\\u696D\\u7A05\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"span\", 144);\n    i0.ɵɵtext(82, \"5%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 145);\n    i0.ɵɵelement(84, \"i\", 146);\n    i0.ɵɵtext(85, \" \\u56FA\\u5B9A\\u70BA\\u5C0F\\u8A08\\u91D1\\u984D\\u76845% \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(86, \"div\", 147)(87, \"div\", 148);\n    i0.ɵɵtext(88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(89, \"div\", 149);\n    i0.ɵɵtext(90, \"\\u542B\\u7A05\\u91D1\\u984D\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(91, \"hr\", 150);\n    i0.ɵɵelementStart(92, \"div\", 151)(93, \"span\", 138);\n    i0.ɵɵtext(94, \"\\u7E3D\\u91D1\\u984D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(95, \"span\", 152);\n    i0.ɵɵtext(96);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(97, \"nb-card-footer\", 153)(98, \"div\")(99, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_99_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.printQuotation());\n    });\n    i0.ɵɵelement(100, \"i\", 155);\n    i0.ɵɵtext(101, \" \\u5217\\u5370\\u5831\\u50F9\\u55AE \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(102, HouseholdManagementComponent_ng_template_115_button_102_Template, 3, 0, \"button\", 156);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"div\")(104, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_115_Template_button_click_104_listener() {\n      const ref_r40 = i0.ɵɵrestoreView(_r33).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r40));\n    });\n    i0.ɵɵtext(105, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(106, HouseholdManagementComponent_ng_template_115_button_106_Template, 2, 1, \"button\", 158)(107, HouseholdManagementComponent_ng_template_115_button_107_Template, 2, 1, \"button\", 159);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \\u5831\\u50F9\\u55AE - \", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CHouseHold, \" (\", ctx_r10.currentHouse == null ? null : ctx_r10.currentHouse.CFloor, \"\\u6A13) \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\"Q\", ctx_r10.currentQuotationId || \"202507031009304\", \"28\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵclassProp(\"status-quoted\", !ctx_r10.isQuotationEditable)(\"status-draft\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.isQuotationEditable ? \"\\u8349\\u7A3F\" : \"\\u5DF2\\u5831\\u50F9\", \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r10.getCurrentDate());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"NT$ \", ctx_r10.formatNumber(ctx_r10.finalTotalAmount), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.quotationItems);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.totalAmount));\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.additionalFeeAmount));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r10.formatCurrency(ctx_r10.finalTotalAmount));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.quotationItems.length === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isQuotationEditable);\n  }\n}\nexport class HouseholdManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService, quotationService) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._houseHoldMainService = _houseHoldMainService;\n    this._buildCaseService = _buildCaseService;\n    this.pettern = pettern;\n    this.router = router;\n    this._eventService = _eventService;\n    this._ultilityService = _ultilityService;\n    this.quotationService = quotationService;\n    this.tempBuildCaseID = -1;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.cIsEnableOptions = [{\n      value: null,\n      key: 'all',\n      label: '全部'\n    }, {\n      value: true,\n      key: 'enable',\n      label: '啟用'\n    }, {\n      value: false,\n      key: 'deactivate',\n      label: '停用'\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.houseHoldOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.progressOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.houseTypeOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.payStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.signStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.quotationStatusOptions = [{\n      label: '全部',\n      value: -1\n    }];\n    this.options = {\n      progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n      payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n      houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\n      quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus)\n    };\n    this.initDetail = {\n      CHouseID: 0,\n      CMail: \"\",\n      CIsChange: false,\n      CPayStatus: 0,\n      CIsEnable: false,\n      CCustomerName: \"\",\n      CNationalID: \"\",\n      CProgress: \"\",\n      CHouseType: 0,\n      CHouseHold: \"\",\n      CPhone: \"\"\n    };\n    // 報價單相關\n    this.quotationItems = [];\n    this.totalAmount = 0;\n    // 新增：百分比費用設定\n    this.additionalFeeName = '營業稅'; // 固定名稱\n    this.additionalFeePercentage = 5; // 固定5%\n    this.additionalFeeAmount = 0; // 百分比費用金額\n    this.finalTotalAmount = 0; // 最終總金額（含百分比費用）\n    this.enableAdditionalFee = true; // 固定啟用營業稅\n    this.currentHouse = null;\n    this.currentQuotationId = 0;\n    this.isQuotationEditable = true; // 報價單是否可編輯\n    this.selectedFile = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n    this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n    this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n    this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    this.quotationStatusOptions = [...this.quotationStatusOptions, ...this.enumHelper.getEnumOptions(EnumQuotationStatus)];\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\n        //   : this.buildingSelectedOptions[0],\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value) : this.houseHoldOptions[0],\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value) : this.houseTypeOptions[0],\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value) : this.payStatusOptions[0],\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value) : this.progressOptions[0],\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value) : this.signStatusOptions[0],\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value) : this.quotationStatusOptions[0],\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value) : this.cIsEnableOptions[0],\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined ? previous_search.CFrom : '',\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined ? previous_search.CTo : ''\n      };\n    } else {\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n    }\n    this.getListBuildCase();\n  }\n  onSearch() {\n    let sessionSave = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\n      CFrom: this.searchQuery.CFrom,\n      CTo: this.searchQuery.CTo,\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\n      CProgressSelected: this.searchQuery.CProgressSelected,\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\n    };\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\n    this.getHouseList().subscribe();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getHouseList().subscribe();\n  }\n  exportHouse() {\n    if (this.searchQuery.CBuildCaseSelected.cID) {\n      this._houseService.apiHouseExportHousePost$Json({\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  triggerFileInput() {\n    this.fileInput.nativeElement.click();\n  }\n  onFileSelected(event) {\n    const input = event.target;\n    if (input.files && input.files.length > 0) {\n      this.selectedFile = input.files[0];\n      this.importExcel();\n    }\n  }\n  importExcel() {\n    if (this.selectedFile) {\n      const formData = new FormData();\n      formData.append('CFile', this.selectedFile);\n      this._houseService.apiHouseImportHousePost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n          CFile: this.selectedFile\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(res.Message);\n          this.getHouseList().subscribe();\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n  }\n  getListHouseHold() {\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldOptions = [{\n          value: '',\n          label: '全部'\n        }, ...res.Entries.map(e => {\n          return {\n            value: e,\n            label: e\n          };\n        })];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\n            let index = this.houseHoldOptions.findIndex(x => x.value == previous_search.CHouseHoldSelected.value);\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index];\n          } else {\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n          }\n        }\n      }\n    });\n  }\n  formatQuery() {\n    this.bodyRequest = {\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n      this.bodyRequest['CFloor'] = {\n        CFrom: this.searchQuery.CFrom,\n        CTo: this.searchQuery.CTo\n      };\n    }\n    if (this.searchQuery.CHouseHoldSelected) {\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n    }\n    if (this.searchQuery.CHouseTypeSelected.value) {\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n    }\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n    }\n    if (this.searchQuery.CPayStatusSelected.value) {\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n    }\n    if (this.searchQuery.CProgressSelected.value) {\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n    }\n    if (this.searchQuery.CSignStatusSelected.value) {\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n    }\n    if (this.searchQuery.CQuotationStatusSelected.value) {\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value;\n    }\n    return this.bodyRequest;\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: this.formatQuery()\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  onSelectionChangeBuildCase() {\n    // this.getListBuilding()\n    this.getListHouseHold();\n    this.getHouseList().subscribe();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\n      body: {\n        CIsPagi: false,\n        CStatus: 1\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        }) : [];\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\n          if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == previous_search.CBuildCaseSelected.cID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n      }\n    }), tap(() => {\n      // this.getListBuilding()\n      this.getListHouseHold();\n      setTimeout(() => {\n        this.getHouseList().subscribe();\n      }, 500);\n    })).subscribe();\n  }\n  getHouseById(CID, ref) {\n    this.detailSelected = {};\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: CID\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseDetail = {\n          ...res.Entries,\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n        };\n        if (res.Entries.CBuildCaseId) {\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n        }\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n        if (res.Entries.CHouseType) {\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n        } else {\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n        }\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n        if (res.Entries.CBuildCaseId) {\n          if (this.houseHoldMain) {\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n          }\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  findItemInArray(array, key, value) {\n    return array.find(item => item[key] === value);\n  }\n  openModelDetail(ref, item) {\n    this.getHouseById(item.CID, ref);\n  }\n  openModel(ref) {\n    this.houseHoldMain = {\n      CBuildingName: '',\n      CFloor: undefined,\n      CHouseHoldCount: undefined\n    };\n    this.dialogService.open(ref);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmitDetail(ref) {\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n      CChangeStartDate: this.houseDetail.CChangeStartDate,\n      CChangeEndDate: this.houseDetail.CChangeEndDate\n    };\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseService.apiHouseEditHousePost$Json({\n      body: this.editHouseArgsParam\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res.Message);\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  }\n  onSubmit(ref) {\n    let bodyReq = {\n      CCustomerName: this.houseDetail.CCustomerName,\n      CHouseHold: this.houseDetail.CHousehold,\n      CHouseID: this.houseDetail.CId,\n      CHouseType: this.houseDetail.CHouseType,\n      CIsChange: this.houseDetail.CIsChange,\n      CIsEnable: this.houseDetail.CIsEnable,\n      CMail: this.houseDetail.CMail,\n      CNationalID: this.houseDetail.CNationalId,\n      CPayStatus: this.houseDetail.CPayStatus,\n      CPhone: this.houseDetail.CPhone,\n      CProgress: this.houseDetail.CProgress\n    };\n    this._houseService.apiHouseEditHousePost$Json({\n      body: bodyReq\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  onNavidateId(type, id) {\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n    this.router.navigate([`/pages/household-management/${type}`, idURL]);\n  }\n  onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n  }\n  resetSecureKey(item) {\n    if (confirm(\"您想重設密碼嗎？\")) {\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\n        body: item.CID\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        }\n      });\n    }\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.houseDetail.CId);\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n    this.valid.required('[樓層]', this.houseDetail.CFloor);\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n    // if (this.editHouseArgsParam.CNationalID) {\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\n    // }\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n    if (this.houseDetail.CChangeStartDate) {\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n    }\n    if (this.houseDetail.CChangeEndDate) {\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n    }\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n  }\n  validationHouseHoldMain() {\n    this.valid.clear();\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n  }\n  addHouseHoldMain(ref) {\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n      body: this.houseHoldMain\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        ref.close();\n      }\n    }), concatMap(() => this.getHouseList())).subscribe();\n  } // 開啟報價單對話框\n  openQuotation(dialog, item) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.currentHouse = item;\n      _this.quotationItems = [];\n      _this.totalAmount = 0;\n      _this.currentQuotationId = 0; // 重置報價單ID\n      _this.isQuotationEditable = true; // 預設可編輯\n      // 重置百分比費用設定（固定營業稅5%）\n      _this.additionalFeeName = '營業稅';\n      _this.additionalFeePercentage = 5;\n      _this.additionalFeeAmount = 0;\n      _this.finalTotalAmount = 0;\n      _this.enableAdditionalFee = true;\n      // 載入現有報價資料\n      try {\n        const response = yield _this.quotationService.getQuotationByHouseId(item.CID).toPromise();\n        if (response && response.StatusCode === 0 && response.Entries) {\n          // 保存當前的報價單ID\n          _this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\n          // 根據 cQuotationStatus 決定是否可編輯\n          if (response.Entries.CQuotationStatus === 2) {\n            // 2: 已報價\n            _this.isQuotationEditable = false;\n          } else {\n            _this.isQuotationEditable = true;\n          }\n          // 載入額外費用設定（固定營業稅5%，不從後端載入）\n          _this.enableAdditionalFee = true;\n          _this.additionalFeeName = '營業稅';\n          _this.additionalFeePercentage = 5;\n          // 檢查 Entries 是否有 Items 陣列\n          if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\n            // 將 API 回傳的資料轉換為 QuotationItem 格式\n            _this.quotationItems = response.Entries.Items.map(entry => ({\n              cHouseID: response.Entries.CHouseID || item.CID,\n              cQuotationID: response.Entries.CQuotationID,\n              cItemName: entry.CItemName || '',\n              cUnit: entry.CUnit || '',\n              cUnitPrice: entry.CUnitPrice || 0,\n              cCount: entry.CCount || 1,\n              cStatus: entry.CStatus || 1,\n              CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\n              cRemark: entry.CRemark || '',\n              cQuotationStatus: entry.CQuotationStatus\n            }));\n            _this.calculateTotal();\n          } else {}\n        } else {}\n      } catch (error) {\n        console.error('載入報價資料失敗:', error);\n      }\n      _this.dialogService.open(dialog, {\n        context: item,\n        closeOnBackdropClick: false\n      });\n    })();\n  }\n  // 產生新報價單\n  createNewQuotation() {\n    this.currentQuotationId = 0;\n    this.quotationItems = [];\n    this.isQuotationEditable = true;\n    this.totalAmount = 0;\n    this.finalTotalAmount = 0;\n    this.additionalFeeAmount = 0;\n    this.enableAdditionalFee = true;\n    // 顯示成功訊息\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\n  }\n  // 新增自定義報價項目\n  addQuotationItem() {\n    this.quotationItems.push({\n      cHouseID: this.currentHouse?.CID || 0,\n      cItemName: '',\n      cUnit: '',\n      cUnitPrice: 0,\n      cCount: 1,\n      cStatus: 1,\n      CQuotationItemType: CQuotationItemType.自定義,\n      cRemark: ''\n    });\n  }\n  // 載入客變需求\n  loadDefaultItems() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.currentHouse?.CID) {\n          _this2.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this2.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this2.currentHouse.CID\n        };\n        const response = yield _this2.quotationService.loadDefaultItems(request).toPromise();\n        if (response?.success && response.data) {\n          const defaultItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this2.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnit: x.CUnit || '',\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.客變需求,\n            cRemark: x.CRemark\n          }));\n          _this2.quotationItems.push(...defaultItems);\n          _this2.calculateTotal();\n          _this2.message.showSucessMSG('載入客變需求成功');\n        } else {\n          _this2.message.showErrorMSG(response?.message || '載入客變需求失敗');\n        }\n      } catch (error) {\n        console.error('載入客變需求錯誤:', error);\n        _this2.message.showErrorMSG('載入客變需求失敗');\n      }\n    })();\n  }\n  // 載入選樣資料\n  loadRegularItems() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.currentHouse?.CID) {\n          _this3.message.showErrorMSG('請先選擇戶別');\n          return;\n        }\n        const request = {\n          CBuildCaseID: _this3.searchQuery?.CBuildCaseSelected?.cID || 0,\n          CHouseID: _this3.currentHouse.CID\n        };\n        const response = yield _this3.quotationService.loadRegularItems(request).toPromise();\n        if (response?.success && response.data) {\n          const regularItems = response.data.map(x => ({\n            cQuotationID: x.CQuotationID,\n            cHouseID: _this3.currentHouse?.CID,\n            cItemName: x.CItemName,\n            cUnit: x.CUnit || '',\n            cUnitPrice: x.CUnitPrice,\n            cCount: x.CCount,\n            cStatus: x.CStatus,\n            CQuotationItemType: CQuotationItemType.選樣,\n            // 選樣資料\n            cRemark: x.CRemark || ''\n          }));\n          _this3.quotationItems.push(...regularItems);\n          _this3.calculateTotal();\n          _this3.message.showSucessMSG('載入選樣資料成功');\n        } else {\n          _this3.message.showErrorMSG(response?.message || '載入選樣資料失敗');\n        }\n      } catch (error) {\n        console.error('載入選樣資料錯誤:', error);\n        _this3.message.showErrorMSG('載入選樣資料失敗');\n      }\n    })();\n  }\n  // 移除報價項目\n  removeQuotationItem(index) {\n    const item = this.quotationItems[index];\n    this.quotationItems.splice(index, 1);\n    this.calculateTotal();\n  }\n  // 計算總金額\n  calculateTotal() {\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\n      return sum + item.cUnitPrice * item.cCount;\n    }, 0);\n    this.calculateFinalTotal();\n  }\n  // 計算百分比費用和最終總金額（固定營業稅5%）\n  calculateFinalTotal() {\n    // 固定計算營業稅5%\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\n  }\n  // 格式化金額\n  formatCurrency(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      style: 'currency',\n      currency: 'TWD',\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 格式化數字（不含貨幣符號）\n  formatNumber(amount) {\n    return new Intl.NumberFormat('zh-TW', {\n      minimumFractionDigits: 0\n    }).format(amount);\n  }\n  // 取得當前日期\n  getCurrentDate() {\n    return new Date().toLocaleDateString('zh-TW', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit'\n    });\n  }\n  // 儲存報價單\n  saveQuotation(ref) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.quotationItems.length === 0) {\n        _this4.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      // 驗證必填欄位 (調整：允許單價和數量為負數)\n      const invalidItems = _this4.quotationItems.filter(item => !item.cItemName.trim());\n      if (invalidItems.length > 0) {\n        _this4.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\n        return;\n      }\n      try {\n        const request = {\n          houseId: _this4.currentHouse.CID,\n          items: _this4.quotationItems,\n          quotationId: _this4.currentQuotationId,\n          // 傳遞當前的報價單ID\n          // 額外費用相關欄位\n          cShowOther: _this4.enableAdditionalFee,\n          // 啟用額外費用\n          cOtherName: _this4.additionalFeeName,\n          // 額外費用名稱\n          cOtherPercent: _this4.additionalFeePercentage // 額外費用百分比\n        };\n        const response = yield _this4.quotationService.saveQuotation(request).toPromise();\n        if (response?.success) {\n          _this4.message.showSucessMSG('報價單儲存成功');\n          ref.close();\n        } else {\n          _this4.message.showErrorMSG(response?.message || '儲存失敗');\n        }\n      } catch (error) {\n        _this4.message.showErrorMSG('報價單儲存失敗');\n      }\n    })();\n  }\n  // 匯出報價單\n  exportQuotation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield _this5.quotationService.exportQuotation(_this5.currentHouse.CID).toPromise();\n        if (blob) {\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `報價單_${_this5.currentHouse.CHouseHold}_${_this5.currentHouse.CFloor}樓.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n        } else {\n          _this5.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\n        }\n      } catch (error) {\n        _this5.message.showErrorMSG('匯出報價單失敗');\n      }\n    })();\n  }\n  // 列印報價單\n  printQuotation() {\n    if (this.quotationItems.length === 0) {\n      this.message.showErrorMSG('沒有可列印的報價項目');\n      return;\n    }\n    try {\n      // 建立列印內容\n      const printContent = this.generatePrintContent();\n      // 建立新的視窗進行列印\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\n      if (printWindow) {\n        printWindow.document.open();\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        // 等待內容載入完成後列印\n        printWindow.onload = function () {\n          setTimeout(() => {\n            printWindow.print();\n            // 列印後不自動關閉視窗，讓使用者可以預覽\n          }, 500);\n        };\n      } else {\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\n      }\n    } catch (error) {\n      console.error('列印報價單錯誤:', error);\n      this.message.showErrorMSG('列印報價單時發生錯誤');\n    }\n  }\n  // 產生列印內容\n  generatePrintContent() {\n    // 使用導入的模板\n    const template = QUOTATION_TEMPLATE;\n    // 準備數據\n    const currentDate = new Date().toLocaleDateString('zh-TW');\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\n    // 生成項目HTML\n    let itemsHtml = '';\n    this.quotationItems.forEach((item, index) => {\n      const subtotal = item.cUnitPrice * item.cCount;\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\n      const unit = item.cUnit || '';\n      itemsHtml += `\n          <tr>\n            <td class=\"text-center\">${index + 1}</td>\n            <td>${item.cItemName}</td>\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\n            <td class=\"text-center\">${unit}</td>\n            <td class=\"text-center\">${item.cCount}</td>\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\n            <td class=\"text-center\">${quotationType}</td>\n          </tr>\n        `;\n    });\n    // 生成額外費用HTML\n    const additionalFeeHtml = this.enableAdditionalFee ? `\n        <div class=\"additional-fee\">\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\n        </div>\n      ` : '';\n    // 替換模板中的占位符\n    const html = template.replace(/{{buildCaseName}}/g, buildCaseName).replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '').replace(/{{floor}}/g, this.currentHouse?.CFloor || '').replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '').replace(/{{printDate}}/g, currentDate).replace(/{{itemsHtml}}/g, itemsHtml).replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount)).replace(/{{additionalFeeHtml}}/g, additionalFeeHtml).replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount)).replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\n    return html;\n  }\n  // 鎖定報價單\n  lockQuotation(ref) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (_this6.quotationItems.length === 0) {\n        _this6.message.showErrorMSG('請先新增報價項目');\n        return;\n      }\n      if (!_this6.currentQuotationId) {\n        _this6.message.showErrorMSG('無效的報價單ID');\n        return;\n      }\n      try {\n        const response = yield _this6.quotationService.lockQuotation(_this6.currentQuotationId).toPromise();\n        if (response.success) {\n          _this6.message.showSucessMSG('報價單已成功鎖定');\n          console.log('報價單鎖定成功:', {\n            quotationId: _this6.currentQuotationId,\n            message: response.message\n          });\n        } else {\n          _this6.message.showErrorMSG(response.message || '報價單鎖定失敗');\n          console.error('報價單鎖定失敗:', response.message);\n        }\n        ref.close();\n      } catch (error) {\n        _this6.message.showErrorMSG('報價單鎖定失敗');\n        console.error('鎖定報價單錯誤:', error);\n      }\n    })();\n  }\n  // 取得報價類型文字\n  getQuotationTypeText(quotationType) {\n    switch (quotationType) {\n      case CQuotationItemType.客變需求:\n        return '客變需求';\n      case CQuotationItemType.自定義:\n        return '自定義';\n      case CQuotationItemType.選樣:\n        return '選樣';\n      default:\n        return '未知';\n    }\n  }\n  getQuotationStatusText(status) {\n    switch (status) {\n      case EnumQuotationStatus.待報價:\n        return '待報價';\n      case EnumQuotationStatus.已報價:\n        return '已報價';\n      case EnumQuotationStatus.已簽回:\n        return '已簽回';\n      default:\n        return '未知';\n    }\n  }\n  static {\n    this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService), i0.ɵɵdirectiveInject(i11.QuotationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdManagementComponent,\n      selectors: [[\"ngx-household-management\"]],\n      viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 117,\n      vars: 23,\n      consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"dialogQuotation\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cQuotationStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", \"maxlength\", \"20\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"class\", \"form-group\", 4, \"ngIf\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68E0\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [2, \"width\", \"1200px\", \"max-height\", \"95vh\"], [1, \"quotation-info-section\", \"mb-4\"], [1, \"row\", \"g-3\"], [1, \"info-block\"], [1, \"info-icon\"], [1, \"fas\", \"fa-file-invoice\"], [1, \"info-content\"], [1, \"info-label\"], [1, \"info-value\"], [1, \"info-icon\", \"status-icon\"], [1, \"fas\", \"fa-info-circle\"], [1, \"status-badge\"], [1, \"info-icon\", \"date-icon\"], [1, \"fas\", \"fa-calendar\"], [1, \"info-block\", \"amount-block\"], [1, \"info-icon\", \"amount-icon\"], [1, \"fas\", \"fa-dollar-sign\"], [1, \"info-value\", \"amount-value\"], [\"class\", \"mb-4 d-flex justify-content-between\", 4, \"ngIf\"], [\"class\", \"mb-4 alert alert-warning\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-bordered\"], [\"width\", \"25%\"], [\"width\", \"15%\"], [\"width\", \"8%\"], [\"width\", \"18%\"], [\"width\", \"10%\"], [4, \"ngIf\"], [1, \"mt-4\"], [1, \"card\", \"border-0\", \"shadow-sm\"], [1, \"card-body\", \"p-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"h6\", \"mb-0\", \"text-muted\"], [1, \"h5\", \"mb-0\", \"text-dark\", \"fw-bold\"], [1, \"tax-section\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\", \"p-3\", \"bg-light\", \"rounded\"], [1, \"d-flex\", \"align-items-center\"], [1, \"tax-icon-wrapper\", \"me-3\"], [1, \"fas\", \"fa-receipt\", \"text-info\"], [1, \"fw-medium\", \"text-dark\"], [1, \"tax-percentage\", \"ms-1\", \"badge\", \"bg-info\", \"text-white\"], [1, \"small\", \"text-muted\", \"mt-1\"], [1, \"fas\", \"fa-info-circle\", \"me-1\"], [1, \"text-end\"], [1, \"tax-amount\", \"h6\", \"mb-0\", \"text-info\", \"fw-bold\"], [1, \"small\", \"text-muted\"], [1, \"my-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"h4\", \"mb-0\", \"text-primary\", \"fw-bold\"], [1, \"d-flex\", \"justify-content-between\"], [\"title\", \"\\u5217\\u5370\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-print\", \"me-1\"], [\"class\", \"btn btn-outline-success btn-sm me-2\", \"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", 3, \"click\"], [\"class\", \"btn btn-warning m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary m-2\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"mb-4\", \"d-flex\", \"justify-content-between\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"mb-4\", \"alert\", \"alert-warning\"], [1, \"fas\", \"fa-lock\", \"me-2\"], [\"type\", \"text\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"min\", \"0\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"type\", \"number\", \"nbInput\", \"\", \"step\", \"0.01\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"text-right\"], [1, \"badge\"], [\"class\", \"btn btn-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"text-muted\"], [1, \"fas\", \"fa-lock\"], [\"colspan\", \"7\", 1, \"text-center\", \"text-muted\", \"py-4\"], [\"title\", \"\\u7522\\u751F\\u65B0\\u5831\\u50F9\\u55AE\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"btn\", \"btn-warning\", \"m-2\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", \"m-2\", 3, \"click\", \"disabled\"]],\n      template: function HouseholdManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 6)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 7);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"div\", 10)(9, \"label\", 11);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"label\", 14);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 16)(21, \"label\", 17);\n          i0.ɵɵtext(22, \"\\u6A13 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"nb-form-field\", 18)(24, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"label\", 20);\n          i0.ɵɵtext(26, \"~ \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-form-field\", 21)(28, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(29, \"div\", 9);\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"div\", 10)(32, \"label\", 23);\n          i0.ɵɵtext(33, \" \\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 24);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(35, HouseholdManagementComponent_nb_option_35_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 9)(37, \"div\", 10)(38, \"label\", 25);\n          i0.ɵɵtext(39, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"nb-select\", 26);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(41, HouseholdManagementComponent_nb_option_41_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"div\", 9)(43, \"div\", 10)(44, \"label\", 27);\n          i0.ɵɵtext(45, \" \\u9032\\u5EA6 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(47, HouseholdManagementComponent_nb_option_47_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 9)(49, \"div\", 10)(50, \"label\", 29);\n          i0.ɵɵtext(51, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"nb-select\", 30);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(53, HouseholdManagementComponent_nb_option_53_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 9)(55, \"div\", 10)(56, \"label\", 31);\n          i0.ɵɵtext(57, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"nb-select\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(59, HouseholdManagementComponent_nb_option_59_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"div\", 10)(62, \"label\", 33);\n          i0.ɵɵtext(63, \" \\u5831\\u50F9\\u55AE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"nb-select\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CQuotationStatusSelected, $event) || (ctx.searchQuery.CQuotationStatusSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(65, HouseholdManagementComponent_nb_option_65_Template, 2, 2, \"nb-option\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(66, \"div\", 9);\n          i0.ɵɵelementStart(67, \"div\", 35)(68, \"div\", 36)(69, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_69_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵtext(70, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(71, \"i\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 39);\n          i0.ɵɵtemplate(74, HouseholdManagementComponent_button_74_Template, 2, 0, \"button\", 40);\n          i0.ɵɵelementStart(75, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n          });\n          i0.ɵɵtext(76, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_77_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.exportHouse());\n          });\n          i0.ɵɵtext(78, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"input\", 42, 0);\n          i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_79_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_81_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerFileInput());\n          });\n          i0.ɵɵtext(82, \" \\u532F\\u5165\\u66F4\\u65B0\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(83, \"div\", 44)(84, \"table\", 45)(85, \"thead\")(86, \"tr\", 46)(87, \"th\", 47);\n          i0.ɵɵtext(88, \"\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"th\", 47);\n          i0.ɵɵtext(90, \"\\u6A13\\u5C64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 47);\n          i0.ɵɵtext(92, \"\\u6236\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 47);\n          i0.ɵɵtext(94, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 47);\n          i0.ɵɵtext(96, \"\\u9032\\u5EA6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 47);\n          i0.ɵɵtext(98, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 47);\n          i0.ɵɵtext(100, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 47);\n          i0.ɵɵtext(102, \"\\u5831\\u50F9\\u55AE\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"th\", 47);\n          i0.ɵɵtext(104, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"th\", 48);\n          i0.ɵɵtext(106, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"tbody\");\n          i0.ɵɵtemplate(108, HouseholdManagementComponent_tr_108_Template, 31, 13, \"tr\", 49);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(109, \"nb-card-footer\", 50)(110, \"ngb-pagination\", 51);\n          i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(111, HouseholdManagementComponent_ng_template_111_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(113, HouseholdManagementComponent_ng_template_113_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(115, HouseholdManagementComponent_ng_template_115_Template, 108, 21, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CQuotationStatusSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.quotationStatusOptions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i12.NgForOf, i12.NgIf, SharedModule, i13.DefaultValueAccessor, i13.NumberValueAccessor, i13.NgControlStatus, i13.MaxLengthValidator, i13.MinValidator, i13.MaxValidator, i13.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i14.NgbPagination, i15.BreadcrumbComponent, i16.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;\\n}\\n\\n.tax-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;\\n  border: 1px solid #dee2e6;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.tax-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(to bottom, #0dcaf0, #0aa2c0);\\n  transition: width 0.3s ease;\\n}\\n.tax-section[_ngcontent-%COMP%]:hover::before {\\n  width: 6px;\\n}\\n\\n.tax-icon-wrapper[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #0dcaf0, #0aa2c0);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 2px 8px rgba(13, 202, 240, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: white !important;\\n}\\n.tax-icon-wrapper[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(13, 202, 240, 0.4);\\n}\\n\\n.tax-percentage[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n.tax-amount[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.tax-amount[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  color: #0aa2c0 !important;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #0d6efd !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\nhr[_ngcontent-%COMP%] {\\n  border-top: 2px solid #dee2e6;\\n  opacity: 0.5;\\n}\\n\\n.h5[_ngcontent-%COMP%], \\n.h6[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.text-primary.fw-bold[_ngcontent-%COMP%] {\\n  text-shadow: 0 1px 2px rgba(13, 110, 253, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.text-primary.fw-bold[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  text-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);\\n}\\n\\n.fa-info-circle[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.fa-info-circle[_ngcontent-%COMP%]:hover {\\n  opacity: 1;\\n}\\n\\n.quotation-info-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  border: 1px solid #e9ecef;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.info-block[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  background: white;\\n  border-radius: 10px;\\n  padding: 1rem;\\n  border: 1px solid #e9ecef;\\n  transition: all 0.3s ease;\\n  height: 100%;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\\n}\\n.info-block[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border-color: #dee2e6;\\n}\\n.info-block.amount-block[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);\\n  border-color: #ffc107;\\n}\\n.info-block.amount-block[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #fff3c4 0%, #ffffff 100%);\\n  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);\\n}\\n\\n.info-icon[_ngcontent-%COMP%] {\\n  width: 45px;\\n  height: 45px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 1rem;\\n  flex-shrink: 0;\\n  background: linear-gradient(135deg, #6c757d, #495057);\\n  color: white;\\n  transition: all 0.3s ease;\\n}\\n.info-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.info-icon.status-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8, #138496);\\n}\\n.info-icon.date-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745, #1e7e34);\\n}\\n.info-icon.amount-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107, #e0a800);\\n  color: #212529 !important;\\n}\\n.info-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n.info-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.info-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-bottom: 0.25rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.info-value[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #212529;\\n  word-break: break-all;\\n}\\n.info-value.amount-value[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #ffc107;\\n  font-weight: 700;\\n  text-shadow: 0 1px 2px rgba(255, 193, 7, 0.2);\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.status-badge.status-draft[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #17a2b8, #138496);\\n  color: white;\\n}\\n.status-badge.status-quoted[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745, #1e7e34);\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .card-body[_ngcontent-%COMP%] {\\n    padding: 1.5rem !important;\\n  }\\n  .h4[_ngcontent-%COMP%], \\n   .h5[_ngcontent-%COMP%], \\n   .h6[_ngcontent-%COMP%] {\\n    font-size: 1rem !important;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .tax-icon-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .tax-section[_ngcontent-%COMP%] {\\n    padding: 1rem !important;\\n  }\\n  .tax-section[_ngcontent-%COMP%]::before {\\n    width: 3px;\\n  }\\n  .tax-section[_ngcontent-%COMP%]:hover::before {\\n    width: 4px;\\n  }\\n  .quotation-info-section[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .info-block[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    margin-bottom: 0.75rem;\\n  }\\n  .info-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-right: 0.75rem;\\n  }\\n  .info-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .info-value[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .info-value.amount-value[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "QUOTATION_TEMPLATE", "NbDatepickerModule", "BaseComponent", "concatMap", "tap", "NbDateFnsDateModule", "moment", "EEvent", "EnumHouseProgress", "EnumHouseType", "EnumPayStatus", "EnumSignStatus", "EnumQuotationStatus", "LocalStorageService", "STORAGE_KEY", "CQuotationItemType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "case_r3", "label", "case_r4", "case_r5", "case_r6", "case_r7", "case_r8", "case_r9", "ɵɵlistener", "HouseholdManagementComponent_button_74_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r10", "ɵɵnextContext", "dialogHouseholdMain_r12", "ɵɵreference", "ɵɵresetView", "openModel", "HouseholdManagementComponent_tr_108_button_20_Template_button_click_0_listener", "_r14", "item_r15", "$implicit", "dialogUpdateHousehold_r16", "openModelDetail", "ɵɵtemplate", "HouseholdManagementComponent_tr_108_button_20_Template", "HouseholdManagementComponent_tr_108_Template_button_click_21_listener", "_r13", "onNavidateBuildCaseIdHouseId", "searchQuery", "CBuildCaseSelected", "cID", "CID", "HouseholdManagementComponent_tr_108_Template_button_click_23_listener", "HouseholdManagementComponent_tr_108_Template_button_click_25_listener", "HouseholdManagementComponent_tr_108_Template_button_click_27_listener", "resetSecureKey", "HouseholdManagementComponent_tr_108_Template_button_click_29_listener", "dialogQuotation_r17", "openQuotation", "ɵɵtextInterpolate", "CHouseHold", "CFloor", "ɵɵtextInterpolate2", "CHouseType", "CIsChange", "CProgressName", "ɵɵtextInterpolate3", "CPayStatus", "CSignStatus", "getQuotationStatusText", "CQuotationStatus", "CIsEnable", "isUpdate", "status_r20", "status_r21", "status_r23", "ɵɵtwoWayListener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template_nb_select_ngModelChange_3_listener", "$event", "_r22", "ɵɵtwoWayBindingSet", "detailSelected", "CPayStatusSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_nb_option_4_Template", "ɵɵtwoWayProperty", "options", "payStatusOptions", "status_r25", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template_nb_select_ngModelChange_3_listener", "_r24", "CProgressSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_nb_option_4_Template", "progressOptions", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_4_listener", "_r19", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_5_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_9_listener", "houseDetail", "CHousehold", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_13_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_17_listener", "CCustomerName", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_21_listener", "CNationalId", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_25_listener", "CMail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_29_listener", "CPhone", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_select_ngModelChange_33_listener", "CHouseTypeSelected", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_nb_option_34_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_35_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_div_36_Template", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_40_listener", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_nb_checkbox_checkedChange_45_listener", "ɵɵelement", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_53_listener", "changeStartDate", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template_input_ngModelChange_58_listener", "changeEndDate", "userBuildCaseOptions", "houseTypeOptions", "isChangePayStatus", "isChangeProgress", "StartDate_r26", "EndDate_r27", "HouseholdManagementComponent_ng_template_111_button_5_Template_button_click_0_listener", "_r29", "ref_r28", "dialogRef", "onSubmitDetail", "HouseholdManagementComponent_ng_template_111_nb_card_body_1_Template", "HouseholdManagementComponent_ng_template_111_Template_button_click_3_listener", "_r18", "onClose", "HouseholdManagementComponent_ng_template_111_button_5_Template", "isCreate", "HouseholdManagementComponent_ng_template_113_button_19_Template_button_click_0_listener", "_r32", "ref_r31", "addHouseHoldMain", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_7_listener", "_r30", "houseHoldMain", "CBuildingName", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_11_listener", "CHouseHoldCount", "HouseholdManagementComponent_ng_template_113_Template_input_ngModelChange_15_listener", "HouseholdManagementComponent_ng_template_113_Template_button_click_17_listener", "HouseholdManagementComponent_ng_template_113_button_19_Template", "HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_1_listener", "_r34", "addQuotationItem", "HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_4_listener", "loadDefaultItems", "HouseholdManagementComponent_ng_template_115_div_43_Template_button_click_6_listener", "loadRegularItems", "HouseholdManagementComponent_ng_template_115_tr_64_button_15_Template_button_click_0_listener", "_r37", "i_r38", "index", "removeQuotationItem", "HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_2_listener", "item_r36", "_r35", "cItemName", "HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_4_listener", "cUnitPrice", "calculateTotal", "HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_6_listener", "cUnit", "HouseholdManagementComponent_ng_template_115_tr_64_Template_input_ngModelChange_8_listener", "cCount", "HouseholdManagementComponent_ng_template_115_tr_64_button_15_Template", "HouseholdManagementComponent_ng_template_115_tr_64_span_16_Template", "ɵɵclassProp", "isQuotationEditable", "formatCurrency", "getQuotationTypeText", "HouseholdManagementComponent_ng_template_115_button_102_Template_button_click_0_listener", "_r39", "createNewQuotation", "HouseholdManagementComponent_ng_template_115_button_106_Template_button_click_0_listener", "_r41", "ref_r40", "lockQuotation", "quotationItems", "length", "HouseholdManagementComponent_ng_template_115_button_107_Template_button_click_0_listener", "_r42", "saveQuotation", "HouseholdManagementComponent_ng_template_115_div_43_Template", "HouseholdManagementComponent_ng_template_115_div_44_Template", "HouseholdManagementComponent_ng_template_115_tr_64_Template", "HouseholdManagementComponent_ng_template_115_tr_65_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_99_listener", "_r33", "printQuotation", "HouseholdManagementComponent_ng_template_115_button_102_Template", "HouseholdManagementComponent_ng_template_115_Template_button_click_104_listener", "HouseholdManagementComponent_ng_template_115_button_106_Template", "HouseholdManagementComponent_ng_template_115_button_107_Template", "currentHouse", "currentQuotationId", "getCurrentDate", "formatNumber", "finalTotalAmount", "totalAmount", "additionalFeeAmount", "HouseholdManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "_houseService", "_houseHoldMainService", "_buildCaseService", "pettern", "router", "_eventService", "_ultilityService", "quotationService", "tempBuildCaseID", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "value", "key", "cIsEnableOptions", "buildCaseOptions", "houseHoldOptions", "signStatusOptions", "quotationStatusOptions", "getEnumOptions", "initDetail", "CHouseID", "CNationalID", "CProgress", "additionalFeeName", "additionalFeePercentage", "enableAdditionalFee", "selectedFile", "buildingSelectedOptions", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "GetSessionStorage", "HOUSE_SEARCH", "undefined", "previous_search", "JSON", "parse", "CHouseHoldSelected", "find", "x", "CSignStatusSelected", "CQuotationStatusSelected", "CIsEnableSeleted", "CFrom", "CTo", "getListBuildCase", "onSearch", "sessionSave", "AddSessionStorage", "stringify", "getHouseList", "pageChanged", "newPage", "exportHouse", "apiHouseExportHousePost$Json", "CBuildCaseID", "Entries", "StatusCode", "downloadExcelFile", "showErrorMSG", "Message", "triggerFileInput", "fileInput", "nativeElement", "click", "onFileSelected", "event", "input", "target", "files", "importExcel", "formData", "FormData", "append", "apiHouseImportHousePost$Json", "body", "CFile", "showSucessMSG", "getListHouseHold", "apiHouseGetListHouseHoldPost$Json", "map", "e", "findIndex", "formatQuery", "bodyRequest", "PageIndex", "PageSize", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "houseList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetAllBuildCaseForSelectPost$Json", "CIsPagi", "CStatus", "setTimeout", "getHouseById", "ref", "apiHouseGetHouseByIdPost$Json", "CChangeStartDate", "Date", "CChangeEndDate", "CBuildCaseId", "findItemInArray", "open", "array", "item", "formatDate", "CChangeDate", "format", "editHouseArgsParam", "CId", "validation", "errorMessages", "showErrorMSGs", "apiHouseEditHousePost$Json", "close", "onSubmit", "bodyReq", "onNavidateId", "type", "id", "idURL", "navigate", "buildCaseId", "houseId", "confirm", "apiHouseResetHouseSecureKeyPost$Json", "clear", "required", "isStringMaxLength", "pattern", "MailPettern", "isPhoneNumber", "checkStartBeforeEnd", "validationHouseHoldMain", "isNaturalNumberInRange", "apiHouseHoldMainAddHouseHoldMainPost$Json", "dialog", "_this", "_asyncToGenerator", "response", "getQuotationByHouseId", "to<PERSON>romise", "CQuotationVersionId", "Items", "Array", "isArray", "entry", "cHouseID", "cQuotationID", "CQuotationID", "CItemName", "CUnit", "CUnitPrice", "CCount", "cStatus", "自定義", "cRemark", "CRemark", "cQuotationStatus", "error", "console", "context", "closeOnBackdropClick", "push", "_this2", "request", "success", "data", "defaultItems", "客變需求", "_this3", "regularItems", "選樣", "splice", "reduce", "sum", "calculateFinalTotal", "Math", "round", "amount", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "toLocaleDateString", "year", "month", "day", "_this4", "invalidItems", "filter", "trim", "items", "quotationId", "cShowOther", "cOtherName", "cOtherPercent", "exportQuotation", "_this5", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "revokeObjectURL", "printContent", "generatePrintContent", "printWindow", "write", "onload", "print", "template", "currentDate", "buildCaseName", "itemsHtml", "for<PERSON>ach", "subtotal", "quotationType", "unit", "additionalFeeHtml", "html", "replace", "toLocaleString", "_this6", "log", "status", "待報價", "已報價", "已簽回", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "HouseService", "HouseHoldMainService", "BuildCaseService", "i7", "PetternHelper", "i8", "Router", "i9", "EventService", "i10", "UtilityService", "i11", "QuotationService", "selectors", "viewQuery", "HouseholdManagementComponent_Query", "rf", "ctx", "HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "HouseholdManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "HouseholdManagementComponent_nb_option_12_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener", "HouseholdManagementComponent_nb_option_18_Template", "HouseholdManagementComponent_Template_input_ngModelChange_24_listener", "HouseholdManagementComponent_Template_input_ngModelChange_28_listener", "HouseholdManagementComponent_Template_nb_select_ngModelChange_34_listener", "HouseholdManagementComponent_nb_option_35_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_40_listener", "HouseholdManagementComponent_nb_option_41_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_46_listener", "HouseholdManagementComponent_nb_option_47_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_52_listener", "HouseholdManagementComponent_nb_option_53_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_58_listener", "HouseholdManagementComponent_nb_option_59_Template", "HouseholdManagementComponent_Template_nb_select_ngModelChange_64_listener", "HouseholdManagementComponent_nb_option_65_Template", "HouseholdManagementComponent_Template_button_click_69_listener", "HouseholdManagementComponent_button_74_Template", "HouseholdManagementComponent_Template_button_click_75_listener", "HouseholdManagementComponent_Template_button_click_77_listener", "HouseholdManagementComponent_Template_input_change_79_listener", "HouseholdManagementComponent_Template_button_click_81_listener", "HouseholdManagementComponent_tr_108_Template", "HouseholdManagementComponent_Template_ngb_pagination_pageChange_110_listener", "HouseholdManagementComponent_ng_template_111_Template", "ɵɵtemplateRefExtractor", "HouseholdManagementComponent_ng_template_113_Template", "HouseholdManagementComponent_ng_template_115_Template", "i12", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i13", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i14", "NgbPagination", "i15", "BreadcrumbComponent", "i16", "BaseLabelDirective", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\household-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { QUOTATION_TEMPLATE } from 'src/assets/template/quotation-template';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseHoldMainService, HouseService } from 'src/services/api/services';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\n// import { TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { AddHouseHoldMain, EditHouseArgs, GetHouseListArgs, GetHouseListRes, TblHouse } from 'src/services/api/models';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\r\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\r\nimport { EnumQuotationStatus } from 'src/app/shared/enum/enumQuotationStatus';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { QuotationItem, CQuotationItemType } from 'src/app/models/quotation.model';\r\nimport { QuotationService } from 'src/app/services/quotation.service';\r\n\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n  key?: string\r\n}\r\n\r\n// interface HouseDetailExtension {\r\n//   changeStartDate: string;\r\n//   changeEndDate: string;\r\n// }\r\nexport interface SearchQuery {\r\n  CBuildCaseSelected?: any | null;\r\n  CHouseTypeSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CHouseHoldSelected?: any | null;\r\n  CPayStatusSelected?: any | null;\r\n  CProgressSelected?: any | null;\r\n  CSignStatusSelected?: any | null;\r\n  CQuotationStatusSelected?: any | null;\r\n  CIsEnableSeleted?: any | null;\r\n  CFrom?: any | null;\r\n  CTo?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-household-management',\r\n  templateUrl: './household-management.component.html',\r\n  styleUrls: ['./household-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbDatepickerModule, NbDateFnsDateModule,],\r\n})\r\n\r\nexport class HouseholdManagementComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseID: number = -1\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _houseHoldMainService: HouseHoldMainService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private _eventService: EventService,\r\n    private _ultilityService: UtilityService,\r\n    private quotationService: QuotationService\r\n  ) {\r\n    super(_allow)\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  cIsEnableOptions = [\r\n    {\r\n      value: null,\r\n      key: 'all',\r\n      label: '全部',\r\n    },\r\n    {\r\n      value: true,\r\n      key: 'enable',\r\n      label: '啟用',\r\n    },\r\n    {\r\n      value: false,\r\n      key: 'deactivate',\r\n      label: '停用',\r\n    }\r\n  ]\r\n\r\n  searchQuery: SearchQuery\r\n  detailSelected: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n  houseHoldOptions: any[] = [{ label: '全部', value: '' }]\r\n  progressOptions: any[] = [{ label: '全部', value: -1 }]\r\n  houseTypeOptions: any[] = [{ label: '全部', value: -1 }]\r\n  payStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  signStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n  quotationStatusOptions: any[] = [{ label: '全部', value: -1 }]\r\n\r\n  options = {\r\n    progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\r\n    payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\r\n    houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType),\r\n    quotationStatusOptions: this.enumHelper.getEnumOptions(EnumQuotationStatus),\r\n  }\r\n\r\n  userBuildCaseOptions: any\r\n  initDetail = {\r\n    CHouseID: 0,\r\n    CMail: \"\",\r\n    CIsChange: false,\r\n    CPayStatus: 0,\r\n    CIsEnable: false,\r\n    CCustomerName: \"\",\r\n    CNationalID: \"\",\r\n    CProgress: \"\",\r\n    CHouseType: 0,\r\n    CHouseHold: \"\",\r\n    CPhone: \"\"\r\n  }\r\n  // 報價單相關\r\n  quotationItems: QuotationItem[] = [];\r\n  totalAmount: number = 0;\r\n  // 新增：百分比費用設定\r\n  additionalFeeName: string = '營業稅';  // 固定名稱\r\n  additionalFeePercentage: number = 5;   // 固定5%\r\n  additionalFeeAmount: number = 0;       // 百分比費用金額\r\n  finalTotalAmount: number = 0;          // 最終總金額（含百分比費用）\r\n  enableAdditionalFee: boolean = true;   // 固定啟用營業稅\r\n  currentHouse: any = null;\r\n  currentQuotationId: number = 0;\r\n  isQuotationEditable: boolean = true; // 報價單是否可編輯\r\n\r\n  override ngOnInit(): void {\r\n    this.progressOptions = [\r\n      ...this.progressOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseProgress)\r\n    ]\r\n    this.houseTypeOptions = [\r\n      ...this.houseTypeOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumHouseType)\r\n    ]\r\n    this.payStatusOptions = [\r\n      ...this.payStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumPayStatus)\r\n    ]\r\n    this.signStatusOptions = [\r\n      ...this.signStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumSignStatus)\r\n    ]\r\n    this.quotationStatusOptions = [\r\n      ...this.quotationStatusOptions,\r\n      ...this.enumHelper.getEnumOptions(EnumQuotationStatus)\r\n    ]\r\n\r\n    if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n      && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n      let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined\r\n        //   ? this.buildingSelectedOptions.find(x => x.value == previous_search.CBuildingNameSelected.value)\r\n        //   : this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined\r\n          ? this.houseHoldOptions.find(x => x.value == previous_search.CHouseHoldSelected.value)\r\n          : this.houseHoldOptions[0],\r\n        CHouseTypeSelected: previous_search.CHouseTypeSelected != null && previous_search.CHouseTypeSelected != undefined\r\n          ? this.houseTypeOptions.find(x => x.value == previous_search.CHouseTypeSelected.value)\r\n          : this.houseTypeOptions[0],\r\n        CPayStatusSelected: previous_search.CPayStatusSelected != null && previous_search.CPayStatusSelected != undefined\r\n          ? this.payStatusOptions.find(x => x.value == previous_search.CPayStatusSelected.value)\r\n          : this.payStatusOptions[0],\r\n        CProgressSelected: previous_search.CProgressSelected != null && previous_search.CProgressSelected != undefined\r\n          ? this.progressOptions.find(x => x.value == previous_search.CProgressSelected.value)\r\n          : this.progressOptions[0],\r\n        CSignStatusSelected: previous_search.CSignStatusSelected != null && previous_search.CSignStatusSelected != undefined\r\n          ? this.signStatusOptions.find(x => x.value == previous_search.CSignStatusSelected.value)\r\n          : this.signStatusOptions[0],\r\n        CQuotationStatusSelected: previous_search.CQuotationStatusSelected != null && previous_search.CQuotationStatusSelected != undefined\r\n          ? this.quotationStatusOptions.find(x => x.value == previous_search.CQuotationStatusSelected.value)\r\n          : this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: previous_search.CIsEnableSeleted != null && previous_search.CIsEnableSeleted != undefined\r\n          ? this.cIsEnableOptions.find(x => x.value == previous_search.CIsEnableSeleted.value)\r\n          : this.cIsEnableOptions[0],\r\n        CFrom: previous_search.CFrom != null && previous_search.CFrom != undefined\r\n          ? previous_search.CFrom\r\n          : '',\r\n        CTo: previous_search.CTo != null && previous_search.CTo != undefined\r\n          ? previous_search.CTo\r\n          : ''\r\n      }\r\n    }\r\n    else {\r\n      this.searchQuery = {\r\n        CBuildCaseSelected: null,\r\n        // CBuildingNameSelected: this.buildingSelectedOptions[0],\r\n        CHouseHoldSelected: this.houseHoldOptions[0],\r\n        CHouseTypeSelected: this.houseTypeOptions[0],\r\n        CPayStatusSelected: this.payStatusOptions[0],\r\n        CProgressSelected: this.progressOptions[0],\r\n        CSignStatusSelected: this.signStatusOptions[0],\r\n        CQuotationStatusSelected: this.quotationStatusOptions[0],\r\n        CIsEnableSeleted: this.cIsEnableOptions[0],\r\n        CFrom: '',\r\n        CTo: ''\r\n      }\r\n    }\r\n    this.getListBuildCase()\r\n  }\r\n\r\n  onSearch() {\r\n    let sessionSave = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      // CBuildingNameSelected: this.searchQuery.CBuildingNameSelected,\r\n      CFrom: this.searchQuery.CFrom,\r\n      CTo: this.searchQuery.CTo,\r\n      CHouseHoldSelected: this.searchQuery.CHouseHoldSelected,\r\n      CHouseTypeSelected: this.searchQuery.CHouseTypeSelected,\r\n      CIsEnableSeleted: this.searchQuery.CIsEnableSeleted,\r\n      CPayStatusSelected: this.searchQuery.CPayStatusSelected,\r\n      CProgressSelected: this.searchQuery.CProgressSelected,\r\n      CSignStatusSelected: this.searchQuery.CSignStatusSelected,\r\n      CQuotationStatusSelected: this.searchQuery.CQuotationStatusSelected\r\n    }\r\n    LocalStorageService.AddSessionStorage(STORAGE_KEY.HOUSE_SEARCH, JSON.stringify(sessionSave));\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getHouseList().subscribe()\r\n  }\r\n\r\n  exportHouse() {\r\n    if (this.searchQuery.CBuildCaseSelected.cID) {\r\n      this._houseService.apiHouseExportHousePost$Json({\r\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n      }).subscribe(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this._ultilityService.downloadExcelFile(\r\n            res.Entries, '戶別資訊範本'\r\n          )\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  selectedFile: File | null = null;\r\n  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;\r\n\r\n\r\n  triggerFileInput(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  onFileSelected(event: Event): void {\r\n    const input = event.target as HTMLInputElement;\r\n    if (input.files && input.files.length > 0) {\r\n      this.selectedFile = input.files[0];\r\n      this.importExcel();\r\n    }\r\n  }\r\n\r\n  importExcel(): void {\r\n    if (this.selectedFile) {\r\n      const formData = new FormData();\r\n      formData.append('CFile', this.selectedFile);\r\n      this._houseService.apiHouseImportHousePost$Json({\r\n        body: {\r\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n          CFile: this.selectedFile\r\n        }\r\n      }).subscribe(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(res.Message!);\r\n          this.getHouseList().subscribe()\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n  getListHouseHold() {\r\n    this._houseService.apiHouseGetListHouseHoldPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseHoldOptions = [{\r\n          value: '', label: '全部'\r\n        }, ...res.Entries.map(e => {\r\n          return { value: e, label: e }\r\n        })]\r\n        if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n          && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n          let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n          if (previous_search.CHouseHoldSelected != null && previous_search.CHouseHoldSelected != undefined) {\r\n            let index = this.houseHoldOptions.findIndex((x: any) => x.value == previous_search.CHouseHoldSelected.value)\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[index]\r\n          } else {\r\n            this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0]\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  houseList: any\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  formatQuery() {\r\n    this.bodyRequest = {\r\n      CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    }\r\n    if (this.searchQuery.CFrom && this.searchQuery.CTo) {\r\n      this.bodyRequest['CFloor'] = { CFrom: this.searchQuery.CFrom, CTo: this.searchQuery.CTo }\r\n    }\r\n    if (this.searchQuery.CHouseHoldSelected) {\r\n      this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value\r\n    }\r\n    if (this.searchQuery.CHouseTypeSelected.value) {\r\n      this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value\r\n    }\r\n    if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\r\n      this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value\r\n    }\r\n    if (this.searchQuery.CPayStatusSelected.value) {\r\n      this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CProgressSelected.value) {\r\n      this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value\r\n    }\r\n    if (this.searchQuery.CSignStatusSelected.value) {\r\n      this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value\r\n    }\r\n    if (this.searchQuery.CQuotationStatusSelected.value) {\r\n      this.bodyRequest['CQuotationStatus'] = this.searchQuery.CQuotationStatusSelected.value\r\n    }\r\n\r\n    return this.bodyRequest\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: this.formatQuery()\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseList = res.Entries;\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n\r\n  userBuildCaseSelected: any\r\n  onSelectionChangeBuildCase() {\r\n    // this.getListBuilding()\r\n    this.getListHouseHold()\r\n    this.getHouseList().subscribe()\r\n  }\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetAllBuildCaseForSelectPost$Json({\r\n      body: {\r\n        CIsPagi: false,\r\n        CStatus: 1,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            }\r\n          }) : []\r\n\r\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n            && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n            if (previous_search.CBuildCaseSelected != null && previous_search.CBuildCaseSelected != undefined) {\r\n              let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == previous_search.CBuildCaseSelected.cID)\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index]\r\n            } else {\r\n              this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n            }\r\n          }\r\n          else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0]\r\n          }\r\n        }\r\n      }),\r\n      tap(() => {\r\n        // this.getListBuilding()\r\n        this.getListHouseHold()\r\n        setTimeout(() => {\r\n          this.getHouseList().subscribe();\r\n        }, 500)\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  buildingSelected: any\r\n\r\n  buildingSelectedOptions: any[] = [\r\n    {\r\n      value: '', label: '全部'\r\n    }\r\n  ]\r\n\r\n  // getListBuilding() {\r\n  //   this._houseService.apiHouseGetListBuildingPost$Json({\r\n  //     body: {\r\n  //       CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\r\n  //     }\r\n  //   }).subscribe(res => {\r\n  //     if (res.Entries && res.StatusCode == 0) {\r\n  //       this.buildingSelectedOptions = [{\r\n  //         value: '', label: '全部'\r\n  //       }, ...res.Entries.map(e => {\r\n  //         return { value: e, label: e }\r\n  //       })]\r\n  //       if (LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != null\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != undefined\r\n  //         && LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH) != \"\") {\r\n  //         let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.HOUSE_SEARCH));\r\n  //         if (previous_search.CBuildingNameSelected != null && previous_search.CBuildingNameSelected != undefined) {\r\n  //           let index = this.buildingSelectedOptions.findIndex((x: any) => x.value == previous_search.CBuildingNameSelected.value)\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[index]\r\n  //         } else {\r\n  //           this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //         }\r\n  //       }\r\n  //       else {\r\n  //         this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0]\r\n  //       }\r\n  //     }\r\n  //   })\r\n  // }\r\n\r\n  houseDetail: TblHouse & {\r\n    changeStartDate?: any;\r\n    changeEndDate?: any\r\n  }\r\n\r\n  getHouseById(CID: any, ref: any) {\r\n    this.detailSelected = {}\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: { CHouseID: CID }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseDetail = {\r\n          ...res.Entries,\r\n          changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\r\n          changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined,\r\n        }\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId)\r\n        }\r\n        this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus)\r\n        if (res.Entries.CHouseType) {\r\n          this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType)\r\n        } else {\r\n          this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1]\r\n        }\r\n        this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress)\r\n\r\n        if (res.Entries.CBuildCaseId) {\r\n          if (this.houseHoldMain) {\r\n            this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId\r\n          }\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n\r\n    })\r\n  }\r\n\r\n\r\n  findItemInArray(array: any[], key: string, value: any) {\r\n    return array.find(item => item[key] === value);\r\n  }\r\n\r\n\r\n  openModelDetail(ref: any, item: any) {\r\n    this.getHouseById(item.CID, ref)\r\n  }\r\n\r\n  openModel(ref: any) {\r\n    this.houseHoldMain = {\r\n      CBuildingName: '',\r\n      CFloor: undefined,\r\n      CHouseHoldCount: undefined\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  editHouseArgsParam: EditHouseArgs\r\n\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmitDetail(ref: any) {\r\n    this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '',\r\n      this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '',\r\n\r\n      this.editHouseArgsParam = {\r\n        CCustomerName: this.houseDetail.CCustomerName,\r\n        CHouseHold: this.houseDetail.CHousehold,\r\n        CHouseID: this.houseDetail.CId,\r\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\r\n        CIsChange: this.houseDetail.CIsChange,\r\n        CIsEnable: this.houseDetail.CIsEnable,\r\n        CMail: this.houseDetail.CMail,\r\n        CNationalID: this.houseDetail.CNationalId,\r\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\r\n        CPhone: this.houseDetail.CPhone,\r\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\r\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\r\n        CChangeEndDate: this.houseDetail.CChangeEndDate\r\n      }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: this.editHouseArgsParam\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!);\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  onSubmit(ref: any) {\r\n    let bodyReq: EditHouseArgs = {\r\n      CCustomerName: this.houseDetail.CCustomerName,\r\n      CHouseHold: this.houseDetail.CHousehold,\r\n      CHouseID: this.houseDetail.CId,\r\n      CHouseType: this.houseDetail.CHouseType,\r\n      CIsChange: this.houseDetail.CIsChange,\r\n      CIsEnable: this.houseDetail.CIsEnable,\r\n      CMail: this.houseDetail.CMail,\r\n      CNationalID: this.houseDetail.CNationalId,\r\n      CPayStatus: this.houseDetail.CPayStatus,\r\n      CPhone: this.houseDetail.CPhone,\r\n      CProgress: this.houseDetail.CProgress,\r\n    }\r\n    this._houseService.apiHouseEditHousePost$Json({\r\n      body: bodyReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        ref.close();\r\n      }\r\n    })\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  onNavidateId(type: any, id?: any) {\r\n    const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID\r\n    this.router.navigate([`/pages/household-management/${type}`, idURL])\r\n  }\r\n\r\n  onNavidateBuildCaseIdHouseId(type: any, buildCaseId: any, houseId: any) {\r\n    this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId])\r\n  }\r\n\r\n  resetSecureKey(item: any) {\r\n    if (confirm(\"您想重設密碼嗎？\")) {\r\n      this._houseService.apiHouseResetHouseSecureKeyPost$Json({\r\n        body: item.CID\r\n      }).subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        }\r\n      })\r\n    }\r\n  }\r\n\r\n  houseHoldMain: AddHouseHoldMain\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.houseDetail.CId)\r\n    this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold)\r\n    this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50)\r\n    this.valid.required('[樓層]', this.houseDetail.CFloor)\r\n    this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50)\r\n    // if (this.editHouseArgsParam.CNationalID) {\r\n    //   this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID)\r\n    // }\r\n    this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern)\r\n    this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone)\r\n    this.valid.required('[進度]', this.editHouseArgsParam.CProgress)\r\n    this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value)\r\n    this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value)\r\n    if (this.houseDetail.CChangeStartDate) {\r\n      this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate)\r\n    }\r\n    if (this.houseDetail.CChangeEndDate) {\r\n      this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate)\r\n    }\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '')\r\n  }\r\n\r\n  validationHouseHoldMain() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID)\r\n    this.valid.required('[棟別]', this.houseHoldMain.CBuildingName)\r\n    this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10)\r\n    this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100)\r\n    this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100)\r\n  }\r\n\r\n\r\n  addHouseHoldMain(ref: any) {\r\n    this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID,\r\n      this.validationHouseHoldMain()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\r\n      body: this.houseHoldMain\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          ref.close();\r\n        }\r\n      }),\r\n      concatMap(() => this.getHouseList())\r\n    ).subscribe();\r\n  }  // 開啟報價單對話框\r\n  async openQuotation(dialog: any, item: any) {\r\n    this.currentHouse = item;\r\n    this.quotationItems = [];\r\n    this.totalAmount = 0;\r\n    this.currentQuotationId = 0; // 重置報價單ID\r\n    this.isQuotationEditable = true; // 預設可編輯\r\n    // 重置百分比費用設定（固定營業稅5%）\r\n    this.additionalFeeName = '營業稅';\r\n    this.additionalFeePercentage = 5;\r\n    this.additionalFeeAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 載入現有報價資料\r\n    try {\r\n      const response = await this.quotationService.getQuotationByHouseId(item.CID).toPromise();\r\n\r\n      if (response && response.StatusCode === 0 && response.Entries) {\r\n        // 保存當前的報價單ID\r\n        this.currentQuotationId = response.Entries.CQuotationVersionId || 0;\r\n        // 根據 cQuotationStatus 決定是否可編輯\r\n        if (response.Entries.CQuotationStatus === 2) { // 2: 已報價\r\n          this.isQuotationEditable = false;\r\n        } else {\r\n          this.isQuotationEditable = true;\r\n        }\r\n\r\n        // 載入額外費用設定（固定營業稅5%，不從後端載入）\r\n        this.enableAdditionalFee = true;\r\n        this.additionalFeeName = '營業稅';\r\n        this.additionalFeePercentage = 5;\r\n\r\n        // 檢查 Entries 是否有 Items 陣列\r\n        if (response.Entries.Items && Array.isArray(response.Entries.Items)) {\r\n          // 將 API 回傳的資料轉換為 QuotationItem 格式\r\n          this.quotationItems = response.Entries.Items.map((entry: any) => ({\r\n            cHouseID: response.Entries.CHouseID || item.CID,\r\n            cQuotationID: response.Entries.CQuotationID,\r\n            cItemName: entry.CItemName || '',\r\n            cUnit: entry.CUnit || '',\r\n            cUnitPrice: entry.CUnitPrice || 0,\r\n            cCount: entry.CCount || 1,\r\n            cStatus: entry.CStatus || 1,\r\n            CQuotationItemType: entry.CQuotationItemType && entry.CQuotationItemType > 0 ? entry.CQuotationItemType : CQuotationItemType.自定義,\r\n            cRemark: entry.CRemark || '',\r\n            cQuotationStatus: entry.CQuotationStatus\r\n          }));\r\n          this.calculateTotal();\r\n        } else {\r\n\r\n        }\r\n      } else {\r\n\r\n      }\r\n    } catch (error) {\r\n      console.error('載入報價資料失敗:', error);\r\n    }\r\n\r\n    this.dialogService.open(dialog, {\r\n      context: item,\r\n      closeOnBackdropClick: false\r\n    });\r\n  }\r\n\r\n  // 產生新報價單\r\n  createNewQuotation() {\r\n    this.currentQuotationId = 0;\r\n    this.quotationItems = [];\r\n    this.isQuotationEditable = true;\r\n    this.totalAmount = 0;\r\n    this.finalTotalAmount = 0;\r\n    this.additionalFeeAmount = 0;\r\n    this.enableAdditionalFee = true;\r\n\r\n    // 顯示成功訊息\r\n    this.message.showSucessMSG('已產生新報價單，可開始編輯');\r\n  }\r\n  // 新增自定義報價項目\r\n  addQuotationItem() {\r\n    this.quotationItems.push({\r\n      cHouseID: this.currentHouse?.CID || 0,\r\n      cItemName: '',\r\n      cUnit: '',\r\n      cUnitPrice: 0,\r\n      cCount: 1,\r\n      cStatus: 1,\r\n      CQuotationItemType: CQuotationItemType.自定義,\r\n      cRemark: ''\r\n    });\r\n  }\r\n  // 載入客變需求\r\n  async loadDefaultItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadDefaultItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const defaultItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.客變需求,\r\n          cRemark: x.CRemark\r\n        }));\r\n        this.quotationItems.push(...defaultItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入客變需求成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入客變需求失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入客變需求錯誤:', error);\r\n      this.message.showErrorMSG('載入客變需求失敗');\r\n    }\r\n  }\r\n\r\n  // 載入選樣資料\r\n  async loadRegularItems() {\r\n    try {\r\n      if (!this.currentHouse?.CID) {\r\n        this.message.showErrorMSG('請先選擇戶別');\r\n        return;\r\n      }\r\n\r\n      const request = {\r\n        CBuildCaseID: this.searchQuery?.CBuildCaseSelected?.cID || 0,\r\n        CHouseID: this.currentHouse.CID\r\n      };\r\n\r\n      const response = await this.quotationService.loadRegularItems(request).toPromise();\r\n      if (response?.success && response.data) {\r\n        const regularItems = response.data.map((x: any) => ({\r\n          cQuotationID: x.CQuotationID,\r\n          cHouseID: this.currentHouse?.CID,\r\n          cItemName: x.CItemName,\r\n          cUnit: x.CUnit || '',\r\n          cUnitPrice: x.CUnitPrice,\r\n          cCount: x.CCount,\r\n          cStatus: x.CStatus,\r\n          CQuotationItemType: CQuotationItemType.選樣, // 選樣資料\r\n          cRemark: x.CRemark || ''\r\n        }));\r\n        this.quotationItems.push(...regularItems);\r\n        this.calculateTotal();\r\n        this.message.showSucessMSG('載入選樣資料成功');\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '載入選樣資料失敗');\r\n      }\r\n    } catch (error) {\r\n      console.error('載入選樣資料錯誤:', error);\r\n      this.message.showErrorMSG('載入選樣資料失敗');\r\n    }\r\n  }\r\n\r\n  // 移除報價項目\r\n  removeQuotationItem(index: number) {\r\n    const item = this.quotationItems[index];\r\n    this.quotationItems.splice(index, 1);\r\n    this.calculateTotal();\r\n  }\r\n\r\n  // 計算總金額\r\n  calculateTotal() {\r\n    this.totalAmount = this.quotationItems.reduce((sum, item) => {\r\n      return sum + (item.cUnitPrice * item.cCount);\r\n    }, 0);\r\n    this.calculateFinalTotal();\r\n  }\r\n\r\n  // 計算百分比費用和最終總金額（固定營業稅5%）\r\n  calculateFinalTotal() {\r\n    // 固定計算營業稅5%\r\n    this.additionalFeeAmount = Math.round(this.totalAmount * 0.05);\r\n    this.finalTotalAmount = this.totalAmount + this.additionalFeeAmount;\r\n  }\r\n\r\n  // 格式化金額\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      style: 'currency',\r\n      currency: 'TWD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 格式化數字（不含貨幣符號）\r\n  formatNumber(amount: number): string {\r\n    return new Intl.NumberFormat('zh-TW', {\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  }\r\n\r\n  // 取得當前日期\r\n  getCurrentDate(): string {\r\n    return new Date().toLocaleDateString('zh-TW', {\r\n      year: 'numeric',\r\n      month: '2-digit',\r\n      day: '2-digit'\r\n    });\r\n  }\r\n\r\n\r\n\r\n  // 儲存報價單\r\n  async saveQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    // 驗證必填欄位 (調整：允許單價和數量為負數)\r\n    const invalidItems = this.quotationItems.filter(item =>\r\n      !item.cItemName.trim()\r\n    );\r\n\r\n    if (invalidItems.length > 0) {\r\n      this.message.showErrorMSG('請確認所有項目名稱都已正確填寫');\r\n      return;\r\n    } try {\r\n      const request = {\r\n        houseId: this.currentHouse.CID,\r\n        items: this.quotationItems,\r\n        quotationId: this.currentQuotationId, // 傳遞當前的報價單ID\r\n        // 額外費用相關欄位\r\n        cShowOther: this.enableAdditionalFee, // 啟用額外費用\r\n        cOtherName: this.additionalFeeName,   // 額外費用名稱\r\n        cOtherPercent: this.additionalFeePercentage // 額外費用百分比\r\n      };\r\n\r\n      const response = await this.quotationService.saveQuotation(request).toPromise();\r\n      if (response?.success) {\r\n        this.message.showSucessMSG('報價單儲存成功');\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(response?.message || '儲存失敗');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單儲存失敗');\r\n    }\r\n  }\r\n\r\n  // 匯出報價單\r\n  async exportQuotation() {\r\n    try {\r\n      const blob: Blob | undefined = await this.quotationService.exportQuotation(this.currentHouse.CID).toPromise();\r\n      if (blob) {\r\n        const url = window.URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = `報價單_${this.currentHouse.CHouseHold}_${this.currentHouse.CFloor}樓.pdf`;\r\n        link.click();\r\n        window.URL.revokeObjectURL(url);\r\n      } else {\r\n        this.message.showErrorMSG('匯出報價單失敗：未收到檔案資料');\r\n      }\r\n    } catch (error) {\r\n      this.message.showErrorMSG('匯出報價單失敗');\r\n    }\r\n  }\r\n\r\n  // 列印報價單\r\n  printQuotation() {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('沒有可列印的報價項目');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // 建立列印內容\r\n      const printContent = this.generatePrintContent();\r\n\r\n      // 建立新的視窗進行列印\r\n      const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');\r\n      if (printWindow) {\r\n        printWindow.document.open();\r\n        printWindow.document.write(printContent);\r\n        printWindow.document.close();\r\n\r\n        // 等待內容載入完成後列印\r\n        printWindow.onload = function () {\r\n          setTimeout(() => {\r\n            printWindow.print();\r\n            // 列印後不自動關閉視窗，讓使用者可以預覽\r\n          }, 500);\r\n        };\r\n      } else {\r\n        this.message.showErrorMSG('無法開啟列印視窗，請檢查瀏覽器是否阻擋彈出視窗');\r\n      }\r\n    } catch (error) {\r\n      console.error('列印報價單錯誤:', error);\r\n      this.message.showErrorMSG('列印報價單時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 產生列印內容\r\n  private generatePrintContent(): string {\r\n    // 使用導入的模板\r\n    const template = QUOTATION_TEMPLATE;\r\n\r\n    // 準備數據\r\n    const currentDate = new Date().toLocaleDateString('zh-TW');\r\n    const buildCaseName = this.searchQuery.CBuildCaseSelected?.CBuildCaseName || '';\r\n\r\n    // 生成項目HTML\r\n    let itemsHtml = '';\r\n    this.quotationItems.forEach((item, index) => {\r\n      const subtotal = item.cUnitPrice * item.cCount;\r\n      const quotationType = this.getQuotationTypeText(item.CQuotationItemType);\r\n      const unit = item.cUnit || '';\r\n      itemsHtml += `\r\n          <tr>\r\n            <td class=\"text-center\">${index + 1}</td>\r\n            <td>${item.cItemName}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(item.cUnitPrice)}</td>\r\n            <td class=\"text-center\">${unit}</td>\r\n            <td class=\"text-center\">${item.cCount}</td>\r\n            <td class=\"text-right\">${this.formatCurrency(subtotal)}</td>\r\n            <td class=\"text-center\">${quotationType}</td>\r\n          </tr>\r\n        `;\r\n    });\r\n\r\n    // 生成額外費用HTML\r\n    const additionalFeeHtml = this.enableAdditionalFee ? `\r\n        <div class=\"additional-fee\">\r\n          ${this.additionalFeeName} (${this.additionalFeePercentage}%)：${this.formatCurrency(this.additionalFeeAmount)}\r\n        </div>\r\n      ` : '';\r\n\r\n    // 替換模板中的占位符\r\n    const html = template\r\n      .replace(/{{buildCaseName}}/g, buildCaseName)\r\n      .replace(/{{houseHold}}/g, this.currentHouse?.CHouseHold || '')\r\n      .replace(/{{floor}}/g, this.currentHouse?.CFloor || '')\r\n      .replace(/{{customerName}}/g, this.currentHouse?.CCustomerName || '')\r\n      .replace(/{{printDate}}/g, currentDate)\r\n      .replace(/{{itemsHtml}}/g, itemsHtml)\r\n      .replace(/{{subtotalAmount}}/g, this.formatCurrency(this.totalAmount))\r\n      .replace(/{{additionalFeeHtml}}/g, additionalFeeHtml)\r\n      .replace(/{{totalAmount}}/g, this.formatCurrency(this.finalTotalAmount))\r\n      .replace(/{{printDateTime}}/g, new Date().toLocaleString('zh-TW'));\r\n\r\n    return html;\r\n  }\r\n\r\n\r\n  // 鎖定報價單\r\n  async lockQuotation(ref: any) {\r\n    if (this.quotationItems.length === 0) {\r\n      this.message.showErrorMSG('請先新增報價項目');\r\n      return;\r\n    }\r\n\r\n    if (!this.currentQuotationId) {\r\n      this.message.showErrorMSG('無效的報價單ID');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await this.quotationService.lockQuotation(this.currentQuotationId).toPromise();\r\n\r\n      if (response.success) {\r\n        this.message.showSucessMSG('報價單已成功鎖定');\r\n        console.log('報價單鎖定成功:', {\r\n          quotationId: this.currentQuotationId,\r\n          message: response.message\r\n        });\r\n      } else {\r\n        this.message.showErrorMSG(response.message || '報價單鎖定失敗');\r\n        console.error('報價單鎖定失敗:', response.message);\r\n      }\r\n\r\n      ref.close();\r\n    } catch (error) {\r\n      this.message.showErrorMSG('報價單鎖定失敗');\r\n      console.error('鎖定報價單錯誤:', error);\r\n    }\r\n  }\r\n\r\n  // 取得報價類型文字\r\n  getQuotationTypeText(quotationType: CQuotationItemType): string {\r\n    switch (quotationType) {\r\n      case CQuotationItemType.客變需求:\r\n        return '客變需求';\r\n      case CQuotationItemType.自定義:\r\n        return '自定義';\r\n      case CQuotationItemType.選樣:\r\n        return '選樣';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  getQuotationStatusText(status: number): string {\r\n    switch (status) {\r\n      case EnumQuotationStatus.待報價:\r\n        return '待報價';\r\n      case EnumQuotationStatus.已報價:\r\n        return '已報價';\r\n      case EnumQuotationStatus.已簽回:\r\n        return '已簽回';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHouseType\" class=\"label col-3\">類型</label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CHouseTypeSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseTypeOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"cFloorFrom\" class=\"label col-3\">樓\r\n          </label>\r\n          <nb-form-field class=\"ml-3\">\r\n            <input type=\"text\" id=\"CFrom\" nbInput class=\"w-full col-4\" [(ngModel)]=\"searchQuery.CFrom\">\r\n          </nb-form-field>\r\n          <label for=\"cFloorTo\" class=\"label col-1\">~\r\n          </label>\r\n          <nb-form-field class=\"mr-3\">\r\n            <input type=\"text\" id=\"CTo\" nbInput class=\"w-full\" [(ngModel)]=\"searchQuery.CTo\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">\r\n            棟別\r\n          </label>\r\n          <nb-select [(ngModel)]=\"searchQuery.CBuildingNameSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildingSelectedOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div> -->\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cHousehold\" class=\"label col-3\">\r\n            戶型\r\n          </label>\r\n          <nb-select placeholder=\"戶型\" [(ngModel)]=\"searchQuery.CHouseHoldSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of houseHoldOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cPayStatus\" class=\"label col-3\">\r\n            繳款狀態\r\n          </label>\r\n          <nb-select placeholder=\"繳款狀態\" [(ngModel)]=\"searchQuery.CPayStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of payStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cProgress\" class=\"label col-3\">\r\n            進度\r\n          </label>\r\n          <nb-select placeholder=\"進度\" [(ngModel)]=\"searchQuery.CProgressSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of progressOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.CIsEnableSeleted\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of cIsEnableOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cSignStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.CSignStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of signStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cQuotationStatus\" class=\"label col-3\">\r\n            報價單狀態\r\n          </label>\r\n          <nb-select placeholder=\"報價單狀態\" [(ngModel)]=\"searchQuery.CQuotationStatusSelected\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of quotationStatusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢按鈕移到這裡，放在搜尋條件的右下角 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialogHouseholdMain)\">\r\n            批次新增戶別資料\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('modify-floor-plan')\">\r\n            修改樓層戶型\r\n          </button>\r\n          <!-- <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"onNavidateId('standard-house-plan')\">\r\n            3.設定戶型標準圖\r\n          </button> -->\r\n          <button class=\"btn btn-info mx-1 btn-sm\" (click)=\"exportHouse()\">\r\n            匯出戶別明細檔\r\n          </button>\r\n          <input type=\"file\" #fileInput style=\"display:none\" (change)=\"onFileSelected($event)\" accept=\".xlsx, .xls\" />\r\n          <button class=\"btn btn-info btn-sm\" (click)=\"triggerFileInput()\">\r\n            匯入更新戶別明細檔\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <!-- <th scope=\"col\" class=\"col-1\">棟別</th> -->\r\n            <th scope=\"col\" class=\"col-1\">戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">樓層</th>\r\n            <th scope=\"col\" class=\"col-1\">戶別</th>\r\n            <th scope=\"col\" class=\"col-1\">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">進度</th>\r\n            <th scope=\"col\" class=\"col-1\">繳款狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">簽回狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">報價單狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-4\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of houseList ; let i = index\">\r\n            <!-- <td>{{ item.CBuildingName}}</td> -->\r\n            <td>{{ item.CHouseHold}}</td>\r\n            <td>{{ item.CFloor}}</td>\r\n            <td>\r\n              {{ item.CHouseType === 2 ? '銷售戶' : ''}}\r\n              {{item.CHouseType === 1 ? '地主戶' : ''}}\r\n            </td>\r\n            <td>{{ item.CIsChange === true ? '客變' : (item.CIsChange === false ? '標準' :'') }}</td>\r\n            <td>{{ item.CProgressName}}</td>\r\n            <td>\r\n              {{item.CPayStatus === 0 ? '未付款': ''}}\r\n              {{item.CPayStatus === 1 ? '已付款': ''}}\r\n              {{item.CPayStatus === 2 ? '無須付款': ''}}\r\n            </td>\r\n            <td>{{ (item.CSignStatus === 0 || item.CSignStatus == null) ? '未簽回' : '已簽回' }}</td>\r\n            <td>{{ getQuotationStatusText(item.CQuotationStatus) }}</td>\r\n            <td>{{ item.CIsEnable ? '啟用' : '停用'}}</td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-success btn-sm text-left m-[2px]\"\r\n                (click)=\"openModelDetail(dialogUpdateHousehold, item)\">\r\n                編輯\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('customer-change-picture', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                洽談紀錄\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('sample-selection-result', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                客變確認圖說\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\"\r\n                (click)=\"onNavidateBuildCaseIdHouseId('finaldochouse_management', searchQuery.CBuildCaseSelected.cID, item.CID )\">\r\n                簽署文件歷程\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"resetSecureKey(item)\">\r\n                重置密碼\r\n              </button>\r\n              <button class=\"btn btn-outline-success btn-sm m-[2px]\" (click)=\"openQuotation(dialogQuotation, item)\">\r\n                報價單\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <!-- <nb-card-header>\r\n    </nb-card-header> -->\r\n    <nb-card-body class=\"px-4\" *ngIf=\"houseDetail\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildCaseId\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          建案名稱\r\n        </label>\r\n        <nb-select placeholder=\"建案名稱\" [(ngModel)]=\"detailSelected.CBuildCaseSelected\" class=\"w-full\" disabled=\"true\">\r\n          <nb-option *ngFor=\"let status of userBuildCaseOptions\" [value]=\"status\">\r\n            {{ status.CBuildCaseName }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHousehold\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶型名稱\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"戶型名稱\" [(ngModel)]=\"houseDetail.CHousehold\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"樓層\" [(ngModel)]=\"houseDetail.CFloor\" min=\"1\" max=\"100\"\r\n          disabled=\"true\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cCustomerName\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          客戶姓名\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"客戶姓名\" [(ngModel)]=\"houseDetail.CCustomerName\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cNationalId\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          身分證字號\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"身分證字號\" [(ngModel)]=\"houseDetail.CNationalId\"\r\n          maxlength=\"20\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cMail\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          電子郵件\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"電子郵件\" [(ngModel)]=\"houseDetail.CMail\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"cPhone\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          聯絡電話\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"聯絡電話\" [(ngModel)]=\"houseDetail.CPhone\" />\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cHouseType\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          戶別類型\r\n        </label>\r\n        <nb-select placeholder=\"戶別類型\" [(ngModel)]=\"detailSelected.CHouseTypeSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.houseTypeOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\" *ngIf=\"isChangePayStatus\">\r\n        <label for=\"cPayStatus\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          付款狀態\r\n        </label>\r\n        <nb-select placeholder=\"付款狀態\" [(ngModel)]=\"detailSelected.CPayStatusSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.payStatusOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group\" *ngIf=\"isChangeProgress\">\r\n        <label for=\"cProgress\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          進度\r\n        </label>\r\n        <nb-select placeholder=\"進度\" [(ngModel)]=\"detailSelected.CProgressSelected\" class=\"w-full\">\r\n          <nb-option *ngFor=\"let status of options.progressOptions\" [value]=\"status\">\r\n            {{ status.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsChange\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否客變\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsChange\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group\">\r\n        <label for=\"cIsEnable\" class=\"mr-4\" style=\"min-width:75px\" baseLabel>\r\n          是否啟用\r\n        </label>\r\n        <nb-checkbox status=\"basic\" [(checked)]=\"houseDetail.CIsEnable\">是\r\n        </nb-checkbox>\r\n      </div>\r\n\r\n      <div class=\"form-group flex flex-row\">\r\n        <label for=\"cIsEnable\" class=\"mr-4 content-center\" style=\"min-width:75px\" baseLabel>\r\n          客變時段\r\n        </label>\r\n        <div class=\"max-w-xs flex flex-row\">\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"StartDate\"\r\n              class=\"w-[42%] mr-2\" [(ngModel)]=\"houseDetail.changeStartDate\">\r\n            <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <nb-form-field class=\"w-1/2\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"yyyy-mm-dd\" [nbDatepicker]=\"EndDate\"\r\n              class=\"w-[42%] ml-2\" [(ngModel)]=\"houseDetail.changeEndDate\">\r\n            <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-outline-secondary m-2 px-8\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary m-2 bg-[#169BD5] px-8\" *ngIf=\"isCreate\" (click)=\"onSubmitDetail(ref)\">送出</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogHouseholdMain let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      戶別管理 》批次新增戶別資料\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group\">\r\n        <label for=\"cBuildingName\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>\r\n          棟別\r\n        </label>\r\n        <input type=\"text\" class=\"w-full\" nbInput placeholder=\"棟別\" [(ngModel)]=\"houseHoldMain.CBuildingName\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CHouseHoldCount\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>當層最多戶數\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"當層最多戶數\" [(ngModel)]=\"houseHoldMain.CHouseHoldCount\" />\r\n      </div>\r\n      <div class=\"form-group\">\r\n        <label for=\"CFloor\" class=\"required-field mr-4\" style=\"min-width:75px\" baseLabel>本棠總樓層\r\n        </label>\r\n        <input type=\"number\" class=\"w-full\" nbInput placeholder=\"本棠總樓層\" [(ngModel)]=\"houseHoldMain.CFloor\" />\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-primary mr-4\" (click)=\"onClose(ref)\">{{ '關閉'}}</button>\r\n      <button class=\"btn btn-primary\" *ngIf=\"isCreate\" (click)=\"addHouseHoldMain(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 報價單對話框 -->\r\n<ng-template #dialogQuotation let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:1200px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      報價單 - {{ currentHouse?.CHouseHold }} ({{ currentHouse?.CFloor }}樓)\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 報價單資訊區塊 -->\r\n      <div class=\"quotation-info-section mb-4\">\r\n        <div class=\"row g-3\">\r\n          <!-- 報價單號碼 -->\r\n          <div class=\"col-md-6\">\r\n            <div class=\"info-block\">\r\n              <div class=\"info-icon\">\r\n                <i class=\"fas fa-file-invoice\"></i>\r\n              </div>\r\n              <div class=\"info-content\">\r\n                <div class=\"info-label\">報價單號</div>\r\n                <div class=\"info-value\">Q{{ currentQuotationId || '202507031009304' }}28</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 狀態 -->\r\n          <div class=\"col-md-6\">\r\n            <div class=\"info-block\">\r\n              <div class=\"info-icon status-icon\">\r\n                <i class=\"fas fa-info-circle\"></i>\r\n              </div>\r\n              <div class=\"info-content\">\r\n                <div class=\"info-label\">狀態</div>\r\n                <div class=\"info-value\">\r\n                  <span class=\"status-badge\" [class.status-quoted]=\"!isQuotationEditable\"\r\n                    [class.status-draft]=\"isQuotationEditable\">\r\n                    {{ isQuotationEditable ? '草稿' : '已報價' }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 建立日期 -->\r\n          <div class=\"col-md-6\">\r\n            <div class=\"info-block\">\r\n              <div class=\"info-icon date-icon\">\r\n                <i class=\"fas fa-calendar\"></i>\r\n              </div>\r\n              <div class=\"info-content\">\r\n                <div class=\"info-label\">建立日期</div>\r\n                <div class=\"info-value\">{{ getCurrentDate() }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 總金額 -->\r\n          <div class=\"col-md-6\">\r\n            <div class=\"info-block amount-block\">\r\n              <div class=\"info-icon amount-icon\">\r\n                <i class=\"fas fa-dollar-sign\"></i>\r\n              </div>\r\n              <div class=\"info-content\">\r\n                <div class=\"info-label\">總金額</div>\r\n                <div class=\"info-value amount-value\">NT$ {{ formatNumber(finalTotalAmount) }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 只有報價單可編輯時才顯示操作按鈕 -->\r\n      <div *ngIf=\"isQuotationEditable\" class=\"mb-4 d-flex justify-content-between\">\r\n        <button class=\"btn btn-info btn-sm\" (click)=\"addQuotationItem()\">\r\n          + 新增自定義項目\r\n        </button>\r\n        <div>\r\n          <button class=\"btn btn-secondary btn-sm me-2\" (click)=\"loadDefaultItems()\">\r\n            載入客變需求\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"loadRegularItems()\">\r\n            載入選樣資料\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 報價單已鎖定時的提示 -->\r\n      <div *ngIf=\"!isQuotationEditable\" class=\"mb-4 alert alert-warning\">\r\n        <i class=\"fas fa-lock me-2\"></i>\r\n        <strong>報價單已鎖定</strong> - 此報價單已鎖定，無法進行修改。您可以列印此報價單或產生新的報價單。\r\n      </div>\r\n\r\n      <div class=\"table-responsive\">\r\n        <table class=\"table table-bordered\">\r\n          <thead>\r\n            <tr>\r\n              <th width=\"25%\">項目名稱</th>\r\n              <th width=\"15%\">單價 (元)</th>\r\n              <th width=\"8%\">單位</th>\r\n              <th width=\"8%\">數量</th>\r\n              <th width=\"18%\">小計 (元)</th>\r\n              <th width=\"10%\">類型</th>\r\n              <th width=\"10%\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let item of quotationItems; let i = index\">\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cItemName\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cUnitPrice\" (ngModelChange)=\"calculateTotal()\"\r\n                  class=\"w-full\" min=\"0\" step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"text\" nbInput [(ngModel)]=\"item.cUnit\"\r\n                  [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\"\r\n                  class=\"w-full\" placeholder=\"單位\"\r\n                  [class.bg-light]=\"!isQuotationEditable || item.CQuotationItemType === 1 || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td>\r\n                <input type=\"number\" nbInput [(ngModel)]=\"item.cCount\" (ngModelChange)=\"calculateTotal()\" class=\"w-full\"\r\n                  step=\"0.01\" [disabled]=\"!isQuotationEditable || item.CQuotationItemType === 3\">\r\n              </td>\r\n              <td class=\"text-right\">\r\n                {{ formatCurrency(item.cUnitPrice * item.cCount) }}\r\n              </td>\r\n              <td>\r\n                <span class=\"badge\" [class.badge-primary]=\"item.CQuotationItemType === 1\"\r\n                  [class.badge-info]=\"item.CQuotationItemType === 3\"\r\n                  [class.badge-secondary]=\"item.CQuotationItemType !== 1 && item.CQuotationItemType !== 3\">\r\n                  {{ getQuotationTypeText(item.CQuotationItemType) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button *ngIf=\"isQuotationEditable\" class=\"btn btn-danger btn-sm\" (click)=\"removeQuotationItem(i)\">\r\n                  刪除\r\n                </button>\r\n                <span *ngIf=\"!isQuotationEditable\" class=\"text-muted\">\r\n                  <i class=\"fas fa-lock\"></i>\r\n                </span>\r\n              </td>\r\n            </tr>\r\n            <tr *ngIf=\"quotationItems.length === 0\">\r\n              <td colspan=\"7\" class=\"text-center text-muted py-4\">\r\n                請點擊「新增自定義項目」或「載入客變需求」開始建立報價單\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 金額計算區塊 -->\r\n      <div class=\"mt-4\">\r\n        <div class=\"card border-0 shadow-sm\">\r\n          <div class=\"card-body p-4\">\r\n            <!-- 小計 -->\r\n            <div class=\"d-flex justify-content-between align-items-center mb-3\">\r\n              <span class=\"h6 mb-0 text-muted\">小計</span>\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">{{ formatCurrency(totalAmount) }}</span>\r\n            </div>\r\n\r\n            <!-- 營業稅 -->\r\n            <div class=\"tax-section d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded\">\r\n              <div class=\"d-flex align-items-center\">\r\n                <div class=\"tax-icon-wrapper me-3\">\r\n                  <i class=\"fas fa-receipt text-info\"></i>\r\n                </div>\r\n                <div>\r\n                  <span class=\"fw-medium text-dark\">營業稅</span>\r\n                  <span class=\"tax-percentage ms-1 badge bg-info text-white\">5%</span>\r\n                  <div class=\"small text-muted mt-1\">\r\n                    <i class=\"fas fa-info-circle me-1\"></i>\r\n                    固定為小計金額的5%\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"text-end\">\r\n                <div class=\"tax-amount h6 mb-0 text-info fw-bold\">{{ formatCurrency(additionalFeeAmount) }}</div>\r\n                <div class=\"small text-muted\">含稅金額</div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 分隔線 -->\r\n            <hr class=\"my-3\">\r\n\r\n            <!-- 總金額 -->\r\n            <div class=\"d-flex justify-content-between align-items-center\">\r\n              <span class=\"h5 mb-0 text-dark fw-bold\">總金額</span>\r\n              <span class=\"h4 mb-0 text-primary fw-bold\">{{ formatCurrency(finalTotalAmount) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-between\">\r\n      <div>\r\n        <button class=\"btn btn-outline-info btn-sm me-2\" (click)=\"printQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\" title=\"列印報價單\">\r\n          <i class=\"fas fa-print me-1\"></i> 列印報價單\r\n        </button>\r\n        <!-- 報價單已鎖定時才顯示 -->\r\n        <button *ngIf=\"!isQuotationEditable\" class=\"btn btn-outline-success btn-sm me-2\" (click)=\"createNewQuotation()\"\r\n          title=\"產生新報價單\">\r\n          <i class=\"fas fa-plus me-1\"></i> 產生新報價單\r\n        </button>\r\n        <!-- <button class=\"btn btn-outline-info btn-sm\" (click)=\"exportQuotation()\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          匯出報價單\r\n        </button> -->\r\n      </div>\r\n      <div>\r\n        <button class=\"btn btn-outline-secondary m-2\" (click)=\"onClose(ref)\">取消</button>\r\n        <!-- 只有在報價單可編輯時才顯示鎖定和儲存按鈕 -->\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-warning m-2\" (click)=\"lockQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          鎖定報價單\r\n        </button>\r\n        <button *ngIf=\"isQuotationEditable\" class=\"btn btn-primary m-2\" (click)=\"saveQuotation(ref)\"\r\n          [disabled]=\"quotationItems.length === 0\">\r\n          儲存報價單\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AACA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,wCAAwC;AAC3E,SAASC,kBAAkB,QAAyB,gBAAgB;AAOpE,SAASC,aAAa,QAAQ,kCAAkC;AAGhE,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,MAAM,QAA8B,uCAAuC;AAEpF,SAASC,iBAAiB,QAAQ,uCAAuC;AAEzE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAAwBC,kBAAkB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;ICftEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IAC7DT,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAC,KAAA,MACF;;;;;IAuCAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IAC7DX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAD,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAQ,OAAA,CAAc;IAC7DZ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAK,OAAA,CAAAF,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAS,OAAA,CAAc;IAC5Db,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAM,OAAA,CAAAH,KAAA,MACF;;;;;IAUAV,EAAA,CAAAC,cAAA,oBAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAU,OAAA,CAAc;IAC7Dd,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAO,OAAA,CAAAJ,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAW,OAAA,CAAc;IAC9Df,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAQ,OAAA,CAAAL,KAAA,MACF;;;;;IAWAV,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAY,OAAA,CAAc;IACnEhB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,OAAA,CAAAN,KAAA,MACF;;;;;;IAoBFV,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAC,uBAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAK,SAAA,CAAAH,uBAAA,CAA8B;IAAA,EAAC;IAChGvB,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuDLH,EAAA,CAAAC,cAAA,iBACyD;IAAvDD,EAAA,CAAAiB,UAAA,mBAAAU,+EAAA;MAAA3B,EAAA,CAAAmB,aAAA,CAAAS,IAAA;MAAA,MAAAC,QAAA,GAAA7B,EAAA,CAAAsB,aAAA,GAAAQ,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAS,yBAAA,GAAA/B,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAW,eAAA,CAAAD,yBAAA,EAAAF,QAAA,CAA4C;IAAA,EAAC;IACtD7B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IApBXH,EAFF,CAAAC,cAAA,SAAmD,SAE7C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GAEF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA4E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrFH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IAGF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0E;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnFH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAiC,UAAA,KAAAC,sDAAA,qBACyD;IAGzDlC,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAkB,sEAAA;MAAA,MAAAN,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHzC,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmH;IAAjHD,EAAA,CAAAiB,UAAA,mBAAAyB,sEAAA;MAAA,MAAAb,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,yBAAyB,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IAChHzC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoH;IAAlHD,EAAA,CAAAiB,UAAA,mBAAA0B,sEAAA;MAAA,MAAAd,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAgB,4BAAA,CAA6B,0BAA0B,EAAAhB,OAAA,CAAAiB,WAAA,CAAAC,kBAAA,CAAAC,GAAA,EAAAX,QAAA,CAAAY,GAAA,CAAgD;IAAA,EAAC;IACjHzC,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsF;IAA/BD,EAAA,CAAAiB,UAAA,mBAAA2B,sEAAA;MAAA,MAAAf,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAwB,cAAA,CAAAhB,QAAA,CAAoB;IAAA,EAAC;IACnF7B,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA/CD,EAAA,CAAAiB,UAAA,mBAAA6B,sEAAA;MAAA,MAAAjB,QAAA,GAAA7B,EAAA,CAAAmB,aAAA,CAAAiB,IAAA,EAAAN,SAAA;MAAA,MAAAT,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,MAAAyB,mBAAA,GAAA/C,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA2B,aAAA,CAAAD,mBAAA,EAAAlB,QAAA,CAAoC;IAAA,EAAC;IACnG7B,EAAA,CAAAE,MAAA,4BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IAxCCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAqB,UAAA,CAAoB;IACpBlD,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAsB,MAAA,CAAgB;IAElBnD,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAoD,kBAAA,MAAAvB,QAAA,CAAAwB,UAAA,yCAAAxB,QAAA,CAAAwB,UAAA,wCAEF;IACIrD,EAAA,CAAAM,SAAA,GAA4E;IAA5EN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAyB,SAAA,6BAAAzB,QAAA,CAAAyB,SAAA,iCAA4E;IAC5EtD,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAA0B,aAAA,CAAuB;IAEzBvD,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAwD,kBAAA,MAAA3B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,yCAAA5B,QAAA,CAAA4B,UAAA,8CAGF;IACIzD,EAAA,CAAAM,SAAA,GAA0E;IAA1EN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAA6B,WAAA,UAAA7B,QAAA,CAAA6B,WAAA,uDAA0E;IAC1E1D,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAsC,sBAAA,CAAA9B,QAAA,CAAA+B,gBAAA,EAAmD;IACnD5D,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAiD,iBAAA,CAAApB,QAAA,CAAAgC,SAAA,mCAAiC;IAE1B7D,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyC,QAAA,CAAc;;;;;IA6C3B9D,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAA2D,UAAA,CAAgB;IACrE/D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAwD,UAAA,CAAAvD,cAAA,MACF;;;;;IAoDAR,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA4D,UAAA,CAAgB;IACzEhE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAyD,UAAA,CAAAtD,KAAA,MACF;;;;;IASAV,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAA6D,UAAA,CAAgB;IACzEjE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAA0D,UAAA,CAAAvD,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAkD,gBACqC;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6F;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAC,+GAAAC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,EAAAJ,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,GAAAJ,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,IAAAwC,uFAAA,wBAA4E;IAIhFzE,EADE,CAAAG,YAAA,EAAY,EACR;;;;IAL0BH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAC,kBAAA,CAA+C;IAC7CxE,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAUzD5E,EAAA,CAAAC,cAAA,oBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAyE,UAAA,CAAgB;IACxE7E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAsE,UAAA,CAAAnE,KAAA,MACF;;;;;;IANFV,EADF,CAAAC,cAAA,cAAiD,gBACqC;IAClFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA0F;IAA9DD,EAAA,CAAAkE,gBAAA,2BAAAY,+GAAAV,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,EAAAZ,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,GAAAZ,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA8C;IACxEpE,EAAA,CAAAiC,UAAA,IAAAgD,uFAAA,wBAA2E;IAI/EjF,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALwBH,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAS,iBAAA,CAA8C;IAC1ChF,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAO,eAAA,CAA0B;;;;;;IA/E1DlF,EAFJ,CAAAC,cAAA,uBAA+C,cACrB,gBACiE;IACrFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBAA6G;IAA/ED,EAAA,CAAAkE,gBAAA,2BAAAiB,wGAAAf,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,EAAA6B,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,GAAA6B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,IAAAoD,gFAAA,wBAAwE;IAI5ErF,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,cAAwB,gBAC+D;IACnFD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAoG;IAAvCD,EAAA,CAAAkE,gBAAA,2BAAAoB,oGAAAlB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAC,UAAA,EAAApB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAC,UAAA,GAAApB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAoC;IACnGpE,EADE,CAAAG,YAAA,EAAoG,EAChG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2D;IAC/ED,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACoB;IADyCD,EAAA,CAAAkE,gBAAA,2BAAAuB,qGAAArB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAApC,MAAA,EAAAiB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAApC,MAAA,GAAAiB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAE/FpE,EAFE,CAAAG,YAAA,EACoB,EAChB;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACmD;IACvED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAuG;IAA1CD,EAAA,CAAAkE,gBAAA,2BAAAwB,qGAAAtB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAI,aAAA,EAAAvB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAI,aAAA,GAAAvB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAuC;IACtGpE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBACiD;IACrED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBACmB;IAD2CD,EAAA,CAAAkE,gBAAA,2BAAA0B,qGAAAxB,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAM,WAAA,EAAAzB,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAM,WAAA,GAAAzB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAqC;IAErGpE,EAFE,CAAAG,YAAA,EACmB,EACf;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC2C;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+F;IAAlCD,EAAA,CAAAkE,gBAAA,2BAAA4B,qGAAA1B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,EAAA3B,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,GAAA3B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+B;IAC9FpE,EADE,CAAAG,YAAA,EAA+F,EAC3F;IAEJH,EADF,CAAAC,cAAA,eAAwB,iBAC4C;IAChED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAAgG;IAAnCD,EAAA,CAAAkE,gBAAA,2BAAA8B,qGAAA5B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAU,MAAA,EAAA7B,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAU,MAAA,GAAA7B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAgC;IAC/FpE,EADE,CAAAG,YAAA,EAAgG,EAC5F;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+D;IACnFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAA6F;IAA/DD,EAAA,CAAAkE,gBAAA,2BAAAgC,yGAAA9B,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,EAAA/B,MAAA,MAAA/C,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,GAAA/B,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA+C;IAC3EpE,EAAA,CAAAiC,UAAA,KAAAmE,iFAAA,wBAA4E;IAIhFpG,EADE,CAAAG,YAAA,EAAY,EACR;IAYNH,EAVA,CAAAiC,UAAA,KAAAoE,2EAAA,kBAAkD,KAAAC,2EAAA,kBAUD;IAY/CtG,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAqC,2GAAAnC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,EAAAc,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,GAAAc,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAmC;IAACpE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAwB,iBAC+C;IACnED,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,uBAAgE;IAApCD,EAAA,CAAAkE,gBAAA,2BAAAsC,2GAAApC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,EAAAO,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,GAAAO,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAmC;IAACpE,EAAA,CAAAE,MAAA,eAChE;IACFF,EADE,CAAAG,YAAA,EAAc,EACV;IAGJH,EADF,CAAAC,cAAA,eAAsC,iBACgD;IAClFD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAoC,yBACL;IAC3BD,EAAA,CAAAyG,SAAA,mBAAoD;IACpDzG,EAAA,CAAAC,cAAA,iBACiE;IAA1CD,EAAA,CAAAkE,gBAAA,2BAAAwC,qGAAAtC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,EAAAvC,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,GAAAvC,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyC;IADhEpE,EAAA,CAAAG,YAAA,EACiE;IACjEH,EAAA,CAAAyG,SAAA,4BAA8D;IAChEzG,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,yBAA6B;IAC3BD,EAAA,CAAAyG,SAAA,mBAAoD;IACpDzG,EAAA,CAAAC,cAAA,iBAC+D;IAAxCD,EAAA,CAAAkE,gBAAA,2BAAA0C,qGAAAxC,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,EAAAzC,MAAA,MAAA/C,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,GAAAzC,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAuC;IAD9DpE,EAAA,CAAAG,YAAA,EAC+D;IAC/DH,EAAA,CAAAyG,SAAA,4BAA4D;IAIpEzG,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACO;;;;;;IArHmBH,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAAhC,kBAAA,CAA+C;IAC7CvC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAyF,oBAAA,CAAuB;IAUM9G,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAC,UAAA,CAAoC;IAOpCxF,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAApC,MAAA,CAAgC;IAQhCnD,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAI,aAAA,CAAuC;IAOtC3F,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAM,WAAA,CAAqC;IAQtC7F,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAQ,KAAA,CAA+B;IAM/B/F,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAU,MAAA,CAAgC;IAO/DjG,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkD,cAAA,CAAA4B,kBAAA,CAA+C;IAC7CnG,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAsD,OAAA,CAAAoC,gBAAA,CAA2B;IAMpC/G,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA2F,iBAAA,CAAuB;IAUvBhH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA4F,gBAAA,CAAsB;IAejBjH,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAjC,SAAA,CAAmC;IAQnCtD,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAA1B,SAAA,CAAmC;IAWQ7D,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,iBAAA8G,aAAA,CAA0B;IACtElH,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAoB,eAAA,CAAyC;IAKC3G,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,iBAAA+G,WAAA,CAAwB;IAClEnH,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAkE,WAAA,CAAAsB,aAAA,CAAuC;;;;;;IAQpE7G,EAAA,CAAAC,cAAA,iBAAqG;IAA9BD,EAAA,CAAAiB,UAAA,mBAAAmG,uFAAA;MAAApH,EAAA,CAAAmB,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAmG,cAAA,CAAAF,OAAA,CAAmB;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAhIpHH,EAAA,CAAAC,cAAA,kBAA+C;IAG7CD,EAAA,CAAAiC,UAAA,IAAAwF,oEAAA,6BAA+C;IA4H7CzH,EADF,CAAAC,cAAA,yBAAsD,iBACsB;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyG,8EAAA;MAAA,MAAAJ,OAAA,GAAAtH,EAAA,CAAAmB,aAAA,CAAAwG,IAAA,EAAAJ,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAN,OAAA,CAAY;IAAA,EAAC;IAACtH,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrFH,EAAA,CAAAiC,UAAA,IAAA4F,8DAAA,qBAAqG;IAEzG7H,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IA/HoBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAkE,WAAA,CAAiB;IA6HYvF,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IA8BrE9H,EAAA,CAAAC,cAAA,kBAAiF;IAAhCD,EAAA,CAAAiB,UAAA,mBAAA8G,wFAAA;MAAA/H,EAAA,CAAAmB,aAAA,CAAA6G,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6G,gBAAA,CAAAD,OAAA,CAAqB;IAAA,EAAC;IAACjI,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAvB9FH,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,wFACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,uBAA2B,cACD,gBACkE;IACtFD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAuG;IAA5CD,EAAA,CAAAkE,gBAAA,2BAAAiE,qFAAA/D,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,EAAAlE,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAC,aAAA,GAAAlE,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyC;IACtGpE,EADE,CAAAG,YAAA,EAAuG,EACnG;IAEJH,EADF,CAAAC,cAAA,cAAwB,gBACoE;IAAAD,EAAA,CAAAE,MAAA,6CAC1F;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAA+G;IAA9CD,EAAA,CAAAkE,gBAAA,2BAAAqE,sFAAAnE,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,EAAApE,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAG,eAAA,GAAApE,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA2C;IAC9GpE,EADE,CAAAG,YAAA,EAA+G,EAC3G;IAEJH,EADF,CAAAC,cAAA,eAAwB,kBAC2D;IAAAD,EAAA,CAAAE,MAAA,uCACjF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,kBAAqG;IAArCD,EAAA,CAAAkE,gBAAA,2BAAAuE,sFAAArE,MAAA;MAAApE,EAAA,CAAAmB,aAAA,CAAAiH,IAAA;MAAA,MAAA/G,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAAtB,EAAA,CAAAsE,kBAAA,CAAAjD,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,EAAAiB,MAAA,MAAA/C,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,GAAAiB,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAkC;IAEtGpE,EAFI,CAAAG,YAAA,EAAqG,EACjG,EACO;IAEbH,EADF,CAAAC,cAAA,0BAAsD,mBACQ;IAAvBD,EAAA,CAAAiB,UAAA,mBAAAyH,+EAAA;MAAA,MAAAT,OAAA,GAAAjI,EAAA,CAAAmB,aAAA,CAAAiH,IAAA,EAAAb,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAK,OAAA,CAAY;IAAA,EAAC;IAACjI,EAAA,CAAAE,MAAA,IAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9EH,EAAA,CAAAiC,UAAA,KAAA0G,+DAAA,sBAAiF;IAErF3I,EADE,CAAAG,YAAA,EAAiB,EACT;;;;IAjBuDH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAC,aAAA,CAAyC;IAKnCtI,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAG,eAAA,CAA2C;IAK5CxI,EAAA,CAAAM,SAAA,GAAkC;IAAlCN,EAAA,CAAA0E,gBAAA,YAAArD,OAAA,CAAAgH,aAAA,CAAAlF,MAAA,CAAkC;IAIxCnD,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAiD,iBAAA,gBAAS;IACpCjD,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAyG,QAAA,CAAc;;;;;;IA4E7C9H,EADF,CAAAC,cAAA,eAA6E,iBACV;IAA7BD,EAAA,CAAAiB,UAAA,mBAAA2H,qFAAA;MAAA5I,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAyH,gBAAA,EAAkB;IAAA,EAAC;IAC9D9I,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,UAAK,kBACwE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAA8H,qFAAA;MAAA/I,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA2H,gBAAA,EAAkB;IAAA,EAAC;IACxEhJ,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAsE;IAA7BD,EAAA,CAAAiB,UAAA,mBAAAgI,qFAAA;MAAAjJ,EAAA,CAAAmB,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA6H,gBAAA,EAAkB;IAAA,EAAC;IACnElJ,EAAA,CAAAE,MAAA,6CACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAGNH,EAAA,CAAAC,cAAA,eAAmE;IACjED,EAAA,CAAAyG,SAAA,aAAgC;IAChCzG,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,iNAC1B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAgDIH,EAAA,CAAAC,cAAA,kBAAmG;IAAjCD,EAAA,CAAAiB,UAAA,mBAAAkI,8FAAA;MAAAnJ,EAAA,CAAAmB,aAAA,CAAAiI,IAAA;MAAA,MAAAC,KAAA,GAAArJ,EAAA,CAAAsB,aAAA,GAAAgI,KAAA;MAAA,MAAAjI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAkI,mBAAA,CAAAF,KAAA,CAAsB;IAAA,EAAC;IAChGrJ,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACTH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAyG,SAAA,aAA2B;IAC7BzG,EAAA,CAAAG,YAAA,EAAO;;;;;;IAnCPH,EAFJ,CAAAC,cAAA,SAAuD,SACjD,iBAI0G;IAHjFD,EAAA,CAAAkE,gBAAA,2BAAAsF,2FAAApF,MAAA;MAAA,MAAAqF,QAAA,GAAAzJ,EAAA,CAAAmB,aAAA,CAAAuI,IAAA,EAAA5H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAmF,QAAA,CAAAE,SAAA,EAAAvF,MAAA,MAAAqF,QAAA,CAAAE,SAAA,GAAAvF,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA4B;IAIzDpE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAEsG;IAD3ED,EAAA,CAAAkE,gBAAA,2BAAA0F,2FAAAxF,MAAA;MAAA,MAAAqF,QAAA,GAAAzJ,EAAA,CAAAmB,aAAA,CAAAuI,IAAA,EAAA5H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAmF,QAAA,CAAAI,UAAA,EAAAzF,MAAA,MAAAqF,QAAA,CAAAI,UAAA,GAAAzF,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAA6B;IAACpE,EAAA,CAAAiB,UAAA,2BAAA2I,2FAAA;MAAA5J,EAAA,CAAAmB,aAAA,CAAAuI,IAAA;MAAA,MAAArI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAAyI,cAAA,EAAgB;IAAA,EAAC;IAE/F9J,EAFE,CAAAG,YAAA,EACwG,EACrG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAI0G;IAHjFD,EAAA,CAAAkE,gBAAA,2BAAA6F,2FAAA3F,MAAA;MAAA,MAAAqF,QAAA,GAAAzJ,EAAA,CAAAmB,aAAA,CAAAuI,IAAA,EAAA5H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAmF,QAAA,CAAAO,KAAA,EAAA5F,MAAA,MAAAqF,QAAA,CAAAO,KAAA,GAAA5F,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAwB;IAIrDpE,EAJE,CAAAG,YAAA,EAG4G,EACzG;IAEHH,EADF,CAAAC,cAAA,SAAI,iBAE+E;IADpDD,EAAA,CAAAkE,gBAAA,2BAAA+F,2FAAA7F,MAAA;MAAA,MAAAqF,QAAA,GAAAzJ,EAAA,CAAAmB,aAAA,CAAAuI,IAAA,EAAA5H,SAAA;MAAA9B,EAAA,CAAAsE,kBAAA,CAAAmF,QAAA,CAAAS,MAAA,EAAA9F,MAAA,MAAAqF,QAAA,CAAAS,MAAA,GAAA9F,MAAA;MAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;IAAA,EAAyB;IAACpE,EAAA,CAAAiB,UAAA,2BAAAgJ,2FAAA;MAAAjK,EAAA,CAAAmB,aAAA,CAAAuI,IAAA;MAAA,MAAArI,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAAiBJ,OAAA,CAAAyI,cAAA,EAAgB;IAAA,EAAC;IAE3F9J,EAFE,CAAAG,YAAA,EACiF,EAC9E;IACLH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,iBAGyF;IACzFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAAkI,qEAAA,sBAAmG,KAAAC,mEAAA,oBAG7C;IAI1DpK,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAlCCH,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAAqK,WAAA,cAAAhJ,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,UAAA0J,QAAA,CAAA1J,kBAAA,OAAyG;IAHhFC,EAAA,CAAA0E,gBAAA,YAAA+E,QAAA,CAAAE,SAAA,CAA4B;IACrD3J,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,UAAA0J,QAAA,CAAA1J,kBAAA,OAAmG;IAKxEC,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAA0E,gBAAA,YAAA+E,QAAA,CAAAI,UAAA,CAA6B;IACrB7J,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,OAAkE;IAMrGC,EAAA,CAAAM,SAAA,GAAyG;IAAzGN,EAAA,CAAAqK,WAAA,cAAAhJ,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,UAAA0J,QAAA,CAAA1J,kBAAA,OAAyG;IAHhFC,EAAA,CAAA0E,gBAAA,YAAA+E,QAAA,CAAAO,KAAA,CAAwB;IACjDhK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,UAAA0J,QAAA,CAAA1J,kBAAA,OAAmG;IAKxEC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA0E,gBAAA,YAAA+E,QAAA,CAAAS,MAAA,CAAyB;IACxClK,EAAA,CAAAI,UAAA,cAAAiB,OAAA,CAAAiJ,mBAAA,IAAAb,QAAA,CAAA1J,kBAAA,OAAkE;IAGhFC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAkJ,cAAA,CAAAd,QAAA,CAAAI,UAAA,GAAAJ,QAAA,CAAAS,MAAA,OACF;IAEsBlK,EAAA,CAAAM,SAAA,GAAqD;IAEvEN,EAFkB,CAAAqK,WAAA,kBAAAZ,QAAA,CAAA1J,kBAAA,OAAqD,eAAA0J,QAAA,CAAA1J,kBAAA,OACrB,oBAAA0J,QAAA,CAAA1J,kBAAA,UAAA0J,QAAA,CAAA1J,kBAAA,OACsC;IACxFC,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAmJ,oBAAA,CAAAf,QAAA,CAAA1J,kBAAA,OACF;IAGSC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAiJ,mBAAA,CAAyB;IAG3BtK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAiJ,mBAAA,CAA0B;;;;;IAMnCtK,EADF,CAAAC,cAAA,SAAwC,cACc;IAClDD,EAAA,CAAAE,MAAA,iLACF;IACFF,EADE,CAAAG,YAAA,EAAK,EACF;;;;;;IAuDTH,EAAA,CAAAC,cAAA,kBACiB;IADgED,EAAA,CAAAiB,UAAA,mBAAAwJ,yFAAA;MAAAzK,EAAA,CAAAmB,aAAA,CAAAuJ,IAAA;MAAA,MAAArJ,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAsJ,kBAAA,EAAoB;IAAA,EAAC;IAE7G3K,EAAA,CAAAyG,SAAA,aAAgC;IAACzG,EAAA,CAAAE,MAAA,6CACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IASTH,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAA2J,yFAAA;MAAA5K,EAAA,CAAAmB,aAAA,CAAA0J,IAAA;MAAA,MAAAC,OAAA,GAAA9K,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA0J,aAAA,CAAAD,OAAA,CAAkB;IAAA,EAAC;IAE1F9K,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA2J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IAG1CjL,EAAA,CAAAC,cAAA,kBAC2C;IADqBD,EAAA,CAAAiB,UAAA,mBAAAiK,yFAAA;MAAAlL,EAAA,CAAAmB,aAAA,CAAAgK,IAAA;MAAA,MAAAL,OAAA,GAAA9K,EAAA,CAAAsB,aAAA,GAAAiG,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAA+J,aAAA,CAAAN,OAAA,CAAkB;IAAA,EAAC;IAE1F9K,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA2J,cAAA,CAAAC,MAAA,OAAwC;;;;;;IAzN9CjL,EADF,CAAAC,cAAA,mBAAgD,qBAC9B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAQPH,EAPV,CAAAC,cAAA,mBAAc,eAE6B,eAClB,aAEG,eACI,eACC;IACrBD,EAAA,CAAAyG,SAAA,aAAmC;IACrCzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACA;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClCH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAG9EF,EAH8E,CAAAG,YAAA,EAAM,EAC1E,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAsB,gBACI,gBACa;IACjCD,EAAA,CAAAyG,SAAA,cAAkC;IACpCzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACA;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE9BH,EADF,CAAAC,cAAA,gBAAwB,iBAEuB;IAC3CD,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAO,EACH,EACF,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAsB,gBACI,gBACW;IAC/BD,EAAA,CAAAyG,SAAA,cAA+B;IACjCzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACA;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClCH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAGpDF,EAHoD,CAAAG,YAAA,EAAM,EAChD,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAsB,gBACiB,gBACA;IACjCD,EAAA,CAAAyG,SAAA,cAAkC;IACpCzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA0B,gBACA;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjCH,EAAA,CAAAC,cAAA,gBAAqC;IAAAD,EAAA,CAAAE,MAAA,IAAwC;IAKvFF,EALuF,CAAAG,YAAA,EAAM,EAC/E,EACF,EACF,EACF,EACF;IAkBNH,EAfA,CAAAiC,UAAA,KAAAoJ,4DAAA,mBAA6E,KAAAC,4DAAA,mBAeV;IAS3DtL,EAJR,CAAAC,cAAA,gBAA8B,kBACQ,aAC3B,UACD,eACc;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,6BAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,eAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEtBF,EAFsB,CAAAG,YAAA,EAAK,EACpB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IAyCLD,EAxCA,CAAAiC,UAAA,KAAAsJ,2DAAA,mBAAuD,KAAAC,2DAAA,kBAwCf;IAO9CxL,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAQEH,EALR,CAAAC,cAAA,gBAAkB,gBACqB,gBACR,gBAE2C,iBACjC;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,iBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAC3EF,EAD2E,CAAAG,YAAA,EAAO,EAC5E;IAKFH,EAFJ,CAAAC,cAAA,gBAAqG,gBAC5D,gBACF;IACjCD,EAAA,CAAAyG,SAAA,cAAwC;IAC1CzG,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,WAAK,iBAC+B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,iBAA2D;IAAAD,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpEH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAyG,SAAA,cAAuC;IACvCzG,EAAA,CAAAE,MAAA,4DACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,gBAAsB,gBAC8B;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjGH,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAEtCF,EAFsC,CAAAG,YAAA,EAAM,EACpC,EACF;IAGNH,EAAA,CAAAyG,SAAA,eAAiB;IAIfzG,EADF,CAAAC,cAAA,gBAA+D,iBACrB;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAK3FF,EAL2F,CAAAG,YAAA,EAAO,EACpF,EACF,EACF,EACF,EACO;IAGXH,EAFJ,CAAAC,cAAA,2BAAuD,WAChD,mBAEsD;IADRD,EAAA,CAAAiB,UAAA,mBAAAwK,+EAAA;MAAAzL,EAAA,CAAAmB,aAAA,CAAAuK,IAAA;MAAA,MAAArK,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAsK,cAAA,EAAgB;IAAA,EAAC;IAEzE3L,EAAA,CAAAyG,SAAA,eAAiC;IAACzG,EAAA,CAAAE,MAAA,yCACpC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAiC,UAAA,MAAA2J,gEAAA,sBACiB;IAOnB5L,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,YAAK,oBACkE;IAAvBD,EAAA,CAAAiB,UAAA,mBAAA4K,gFAAA;MAAA,MAAAf,OAAA,GAAA9K,EAAA,CAAAmB,aAAA,CAAAuK,IAAA,EAAAnE,SAAA;MAAA,MAAAlG,OAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAyB,WAAA,CAASJ,OAAA,CAAAuG,OAAA,CAAAkD,OAAA,CAAY;IAAA,EAAC;IAAC9K,EAAA,CAAAE,MAAA,qBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMhFH,EAJA,CAAAiC,UAAA,MAAA6J,gEAAA,sBAC2C,MAAAC,gEAAA,sBAIA;IAKjD/L,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;IA7NNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAoD,kBAAA,2BAAA/B,OAAA,CAAA2K,YAAA,kBAAA3K,OAAA,CAAA2K,YAAA,CAAA9I,UAAA,QAAA7B,OAAA,CAAA2K,YAAA,kBAAA3K,OAAA,CAAA2K,YAAA,CAAA7I,MAAA,aACF;IAaoCnD,EAAA,CAAAM,SAAA,IAAgD;IAAhDN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAA4K,kBAAA,4BAAgD;IAc3CjM,EAAA,CAAAM,SAAA,GAA4C;IACrEN,EADyB,CAAAqK,WAAA,mBAAAhJ,OAAA,CAAAiJ,mBAAA,CAA4C,iBAAAjJ,OAAA,CAAAiJ,mBAAA,CAC3B;IAC1CtK,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAc,OAAA,CAAAiJ,mBAAA,8CACF;IAcsBtK,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAA6K,cAAA,GAAsB;IAaTlM,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAO,kBAAA,SAAAc,OAAA,CAAA8K,YAAA,CAAA9K,OAAA,CAAA+K,gBAAA,MAAwC;IAQjFpM,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAiJ,mBAAA,CAAyB;IAezBtK,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAiJ,mBAAA,CAA0B;IAmBLtK,EAAA,CAAAM,SAAA,IAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAA2J,cAAA,CAAmB;IAwCnChL,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAA2J,cAAA,CAAAC,MAAA,OAAiC;IAgBIjL,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAkJ,cAAA,CAAAlJ,OAAA,CAAAgL,WAAA,EAAiC;IAmBrBrM,EAAA,CAAAM,SAAA,IAAyC;IAAzCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAkJ,cAAA,CAAAlJ,OAAA,CAAAiL,mBAAA,EAAyC;IAWlDtM,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAAiD,iBAAA,CAAA5B,OAAA,CAAAkJ,cAAA,CAAAlJ,OAAA,CAAA+K,gBAAA,EAAsC;IASrFpM,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAiB,OAAA,CAAA2J,cAAA,CAAAC,MAAA,OAAwC;IAIjCjL,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,UAAAiB,OAAA,CAAAiJ,mBAAA,CAA0B;IAY1BtK,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAiJ,mBAAA,CAAyB;IAIzBtK,EAAA,CAAAM,SAAA,EAAyB;IAAzBN,EAAA,CAAAI,UAAA,SAAAiB,OAAA,CAAAiJ,mBAAA,CAAyB;;;ADjjB1C,OAAM,MAAOiC,4BAA6B,SAAQrN,aAAa;EAE7DsN,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,qBAA2C,EAC3CC,iBAAmC,EACnCC,OAAsB,EACtBC,MAAc,EACdC,aAA2B,EAC3BC,gBAAgC,EAChCC,gBAAkC;IAE1C,KAAK,CAACZ,MAAM,CAAC;IAdL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0BnB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZnN,KAAK,EAAE;KACR,EACD;MACEkN,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBnN,KAAK,EAAE;KACR,CACF;IAED,KAAAoN,gBAAgB,GAAG,CACjB;MACEF,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,KAAK;MACVnN,KAAK,EAAE;KACR,EACD;MACEkN,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,QAAQ;MACbnN,KAAK,EAAE;KACR,EACD;MACEkN,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,YAAY;MACjBnN,KAAK,EAAE;KACR,CACF;IAKD,KAAAqN,gBAAgB,GAAU,CAAC;MAAErN,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAAI,gBAAgB,GAAU,CAAC;MAAEtN,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE;IAAE,CAAE,CAAC;IACtD,KAAA1I,eAAe,GAAU,CAAC;MAAExE,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACrD,KAAA7G,gBAAgB,GAAU,CAAC;MAAErG,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAhJ,gBAAgB,GAAU,CAAC;MAAElE,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACtD,KAAAK,iBAAiB,GAAU,CAAC;MAAEvN,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IACvD,KAAAM,sBAAsB,GAAU,CAAC;MAAExN,KAAK,EAAE,IAAI;MAAEkN,KAAK,EAAE,CAAC;IAAC,CAAE,CAAC;IAE5D,KAAAjJ,OAAO,GAAG;MACRO,eAAe,EAAE,IAAI,CAACwH,UAAU,CAACyB,cAAc,CAAC3O,iBAAiB,CAAC;MAClEoF,gBAAgB,EAAE,IAAI,CAAC8H,UAAU,CAACyB,cAAc,CAACzO,aAAa,CAAC;MAC/DqH,gBAAgB,EAAE,IAAI,CAAC2F,UAAU,CAACyB,cAAc,CAAC1O,aAAa,CAAC;MAC/DyO,sBAAsB,EAAE,IAAI,CAACxB,UAAU,CAACyB,cAAc,CAACvO,mBAAmB;KAC3E;IAGD,KAAAwO,UAAU,GAAG;MACXC,QAAQ,EAAE,CAAC;MACXtI,KAAK,EAAE,EAAE;MACTzC,SAAS,EAAE,KAAK;MAChBG,UAAU,EAAE,CAAC;MACbI,SAAS,EAAE,KAAK;MAChB8B,aAAa,EAAE,EAAE;MACjB2I,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACblL,UAAU,EAAE,CAAC;MACbH,UAAU,EAAE,EAAE;MACd+C,MAAM,EAAE;KACT;IACD;IACA,KAAA+E,cAAc,GAAoB,EAAE;IACpC,KAAAqB,WAAW,GAAW,CAAC;IACvB;IACA,KAAAmC,iBAAiB,GAAW,KAAK,CAAC,CAAE;IACpC,KAAAC,uBAAuB,GAAW,CAAC,CAAC,CAAG;IACvC,KAAAnC,mBAAmB,GAAW,CAAC,CAAC,CAAO;IACvC,KAAAF,gBAAgB,GAAW,CAAC,CAAC,CAAU;IACvC,KAAAsC,mBAAmB,GAAY,IAAI,CAAC,CAAG;IACvC,KAAA1C,YAAY,GAAQ,IAAI;IACxB,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAA3B,mBAAmB,GAAY,IAAI,CAAC,CAAC;IAuHrC,KAAAqE,YAAY,GAAgB,IAAI;IAuKhC,KAAAC,uBAAuB,GAAU,CAC/B;MACEhB,KAAK,EAAE,EAAE;MAAElN,KAAK,EAAE;KACnB,CACF;IA1XC,IAAI,CAACyM,aAAa,CAAC0B,OAAO,EAAE,CAACC,IAAI,CAC/B1P,GAAG,CAAE2P,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAC3B,eAAe,GAAGyB,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAmFSC,QAAQA,CAAA;IACf,IAAI,CAACjK,eAAe,GAAG,CACrB,GAAG,IAAI,CAACA,eAAe,EACvB,GAAG,IAAI,CAACwH,UAAU,CAACyB,cAAc,CAAC3O,iBAAiB,CAAC,CACrD;IACD,IAAI,CAACuH,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC2F,UAAU,CAACyB,cAAc,CAAC1O,aAAa,CAAC,CACjD;IACD,IAAI,CAACmF,gBAAgB,GAAG,CACtB,GAAG,IAAI,CAACA,gBAAgB,EACxB,GAAG,IAAI,CAAC8H,UAAU,CAACyB,cAAc,CAACzO,aAAa,CAAC,CACjD;IACD,IAAI,CAACuO,iBAAiB,GAAG,CACvB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG,IAAI,CAACvB,UAAU,CAACyB,cAAc,CAACxO,cAAc,CAAC,CAClD;IACD,IAAI,CAACuO,sBAAsB,GAAG,CAC5B,GAAG,IAAI,CAACA,sBAAsB,EAC9B,GAAG,IAAI,CAACxB,UAAU,CAACyB,cAAc,CAACvO,mBAAmB,CAAC,CACvD;IAED,IAAIC,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,IAAI,IACtExP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAIC,SAAS,IAC5EzP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,EAAE,EAAE;MAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC5P,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,CAAC;MACjG,IAAI,CAAC/M,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACA;QACA;QACAmN,kBAAkB,EAAEH,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,GAC7G,IAAI,CAACtB,gBAAgB,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACG,kBAAkB,CAAC9B,KAAK,CAAC,GACpF,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5B7H,kBAAkB,EAAEoJ,eAAe,CAACpJ,kBAAkB,IAAI,IAAI,IAAIoJ,eAAe,CAACpJ,kBAAkB,IAAImJ,SAAS,GAC7G,IAAI,CAACvI,gBAAgB,CAAC4I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACpJ,kBAAkB,CAACyH,KAAK,CAAC,GACpF,IAAI,CAAC7G,gBAAgB,CAAC,CAAC,CAAC;QAC5BvC,kBAAkB,EAAE+K,eAAe,CAAC/K,kBAAkB,IAAI,IAAI,IAAI+K,eAAe,CAAC/K,kBAAkB,IAAI8K,SAAS,GAC7G,IAAI,CAAC1K,gBAAgB,CAAC+K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAAC/K,kBAAkB,CAACoJ,KAAK,CAAC,GACpF,IAAI,CAAChJ,gBAAgB,CAAC,CAAC,CAAC;QAC5BI,iBAAiB,EAAEuK,eAAe,CAACvK,iBAAiB,IAAI,IAAI,IAAIuK,eAAe,CAACvK,iBAAiB,IAAIsK,SAAS,GAC1G,IAAI,CAACpK,eAAe,CAACyK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACvK,iBAAiB,CAAC4I,KAAK,CAAC,GAClF,IAAI,CAAC1I,eAAe,CAAC,CAAC,CAAC;QAC3B2K,mBAAmB,EAAEN,eAAe,CAACM,mBAAmB,IAAI,IAAI,IAAIN,eAAe,CAACM,mBAAmB,IAAIP,SAAS,GAChH,IAAI,CAACrB,iBAAiB,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACM,mBAAmB,CAACjC,KAAK,CAAC,GACtF,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC;QAC7B6B,wBAAwB,EAAEP,eAAe,CAACO,wBAAwB,IAAI,IAAI,IAAIP,eAAe,CAACO,wBAAwB,IAAIR,SAAS,GAC/H,IAAI,CAACpB,sBAAsB,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACO,wBAAwB,CAAClC,KAAK,CAAC,GAChG,IAAI,CAACM,sBAAsB,CAAC,CAAC,CAAC;QAClC6B,gBAAgB,EAAER,eAAe,CAACQ,gBAAgB,IAAI,IAAI,IAAIR,eAAe,CAACQ,gBAAgB,IAAIT,SAAS,GACvG,IAAI,CAACxB,gBAAgB,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACQ,gBAAgB,CAACnC,KAAK,CAAC,GAClF,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;QAC5BkC,KAAK,EAAET,eAAe,CAACS,KAAK,IAAI,IAAI,IAAIT,eAAe,CAACS,KAAK,IAAIV,SAAS,GACtEC,eAAe,CAACS,KAAK,GACrB,EAAE;QACNC,GAAG,EAAEV,eAAe,CAACU,GAAG,IAAI,IAAI,IAAIV,eAAe,CAACU,GAAG,IAAIX,SAAS,GAChEC,eAAe,CAACU,GAAG,GACnB;OACL;IACH,CAAC,MACI;MACH,IAAI,CAAC3N,WAAW,GAAG;QACjBC,kBAAkB,EAAE,IAAI;QACxB;QACAmN,kBAAkB,EAAE,IAAI,CAAC1B,gBAAgB,CAAC,CAAC,CAAC;QAC5C7H,kBAAkB,EAAE,IAAI,CAACY,gBAAgB,CAAC,CAAC,CAAC;QAC5CvC,kBAAkB,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,CAAC;QAC5CI,iBAAiB,EAAE,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC;QAC1C2K,mBAAmB,EAAE,IAAI,CAAC5B,iBAAiB,CAAC,CAAC,CAAC;QAC9C6B,wBAAwB,EAAE,IAAI,CAAC5B,sBAAsB,CAAC,CAAC,CAAC;QACxD6B,gBAAgB,EAAE,IAAI,CAACjC,gBAAgB,CAAC,CAAC,CAAC;QAC1CkC,KAAK,EAAE,EAAE;QACTC,GAAG,EAAE;OACN;IACH;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAIC,WAAW,GAAG;MAChB7N,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvD;MACAyN,KAAK,EAAE,IAAI,CAAC1N,WAAW,CAAC0N,KAAK;MAC7BC,GAAG,EAAE,IAAI,CAAC3N,WAAW,CAAC2N,GAAG;MACzBP,kBAAkB,EAAE,IAAI,CAACpN,WAAW,CAACoN,kBAAkB;MACvDvJ,kBAAkB,EAAE,IAAI,CAAC7D,WAAW,CAAC6D,kBAAkB;MACvD4J,gBAAgB,EAAE,IAAI,CAACzN,WAAW,CAACyN,gBAAgB;MACnDvL,kBAAkB,EAAE,IAAI,CAAClC,WAAW,CAACkC,kBAAkB;MACvDQ,iBAAiB,EAAE,IAAI,CAAC1C,WAAW,CAAC0C,iBAAiB;MACrD6K,mBAAmB,EAAE,IAAI,CAACvN,WAAW,CAACuN,mBAAmB;MACzDC,wBAAwB,EAAE,IAAI,CAACxN,WAAW,CAACwN;KAC5C;IACDjQ,mBAAmB,CAACwQ,iBAAiB,CAACvQ,WAAW,CAACuP,YAAY,EAAEG,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC;IAC5F,IAAI,CAACG,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAsB,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAChD,SAAS,GAAGgD,OAAO;IACxB,IAAI,CAACF,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EAEAwB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpO,WAAW,CAACC,kBAAkB,CAACC,GAAG,EAAE;MAC3C,IAAI,CAACsK,aAAa,CAAC6D,4BAA4B,CAAC;QAC9CC,YAAY,EAAE,IAAI,CAACtO,WAAW,CAACC,kBAAkB,CAACC;OACnD,CAAC,CAAC0M,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC1D,gBAAgB,CAAC2D,iBAAiB,CACrChC,GAAG,CAAC8B,OAAO,EAAE,QAAQ,CACtB;QACH,CAAC,MAAM;UACL,IAAI,CAACjE,OAAO,CAACoE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAMAC,gBAAgBA,CAAA;IACd,IAAI,CAACC,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEAC,cAAcA,CAACC,KAAY;IACzB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAA0B;IAC9C,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAACzG,MAAM,GAAG,CAAC,EAAE;MACzC,IAAI,CAAC0D,YAAY,GAAG6C,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC;MAClC,IAAI,CAACC,WAAW,EAAE;IACpB;EACF;EAEAA,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChD,YAAY,EAAE;MACrB,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,IAAI,CAACnD,YAAY,CAAC;MAC3C,IAAI,CAAC7B,aAAa,CAACiF,4BAA4B,CAAC;QAC9CC,IAAI,EAAE;UACJpB,YAAY,EAAE,IAAI,CAACtO,WAAW,CAACC,kBAAkB,CAACC,GAAG;UACrDyP,KAAK,EAAE,IAAI,CAACtD;;OAEf,CAAC,CAACO,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAAClE,OAAO,CAACsF,aAAa,CAACnD,GAAG,CAACkC,OAAQ,CAAC;UACxC,IAAI,CAACV,YAAY,EAAE,CAACrB,SAAS,EAAE;QACjC,CAAC,MAAM;UACL,IAAI,CAACtC,OAAO,CAACoE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC;IACJ;EACF;EAGAkB,gBAAgBA,CAAA;IACd,IAAI,CAACrF,aAAa,CAACsF,iCAAiC,CAAC;MACnDJ,IAAI,EAAE;QAAEpB,YAAY,EAAE,IAAI,CAACtO,WAAW,CAACC,kBAAkB,CAACC;MAAG;KAC9D,CAAC,CAAC0M,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC9C,gBAAgB,GAAG,CAAC;UACvBJ,KAAK,EAAE,EAAE;UAAElN,KAAK,EAAE;SACnB,EAAE,GAAGqO,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACC,CAAC,IAAG;UACxB,OAAO;YAAE1E,KAAK,EAAE0E,CAAC;YAAE5R,KAAK,EAAE4R;UAAC,CAAE;QAC/B,CAAC,CAAC,CAAC;QACH,IAAIzS,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,IAAI,IACtExP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAIC,SAAS,IAC5EzP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC5P,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAACG,kBAAkB,IAAI,IAAI,IAAIH,eAAe,CAACG,kBAAkB,IAAIJ,SAAS,EAAE;YACjG,IAAIhG,KAAK,GAAG,IAAI,CAAC0E,gBAAgB,CAACuE,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAAChC,KAAK,IAAI2B,eAAe,CAACG,kBAAkB,CAAC9B,KAAK,CAAC;YAC5G,IAAI,CAACtL,WAAW,CAACoN,kBAAkB,GAAG,IAAI,CAAC1B,gBAAgB,CAAC1E,KAAK,CAAC;UACpE,CAAC,MAAM;YACL,IAAI,CAAChH,WAAW,CAACoN,kBAAkB,GAAG,IAAI,CAAC1B,gBAAgB,CAAC,CAAC,CAAC;UAChE;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAKAwE,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,GAAG;MACjB7B,YAAY,EAAE,IAAI,CAACtO,WAAW,CAACC,kBAAkB,CAACC,GAAG;MACrDkQ,SAAS,EAAE,IAAI,CAACjF,SAAS;MACzBkF,QAAQ,EAAE,IAAI,CAACnF;KAChB;IACD,IAAI,IAAI,CAAClL,WAAW,CAAC0N,KAAK,IAAI,IAAI,CAAC1N,WAAW,CAAC2N,GAAG,EAAE;MAClD,IAAI,CAACwC,WAAW,CAAC,QAAQ,CAAC,GAAG;QAAEzC,KAAK,EAAE,IAAI,CAAC1N,WAAW,CAAC0N,KAAK;QAAEC,GAAG,EAAE,IAAI,CAAC3N,WAAW,CAAC2N;MAAG,CAAE;IAC3F;IACA,IAAI,IAAI,CAAC3N,WAAW,CAACoN,kBAAkB,EAAE;MACvC,IAAI,CAAC+C,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAACoN,kBAAkB,CAAC9B,KAAK;IAC5E;IACA,IAAI,IAAI,CAACtL,WAAW,CAAC6D,kBAAkB,CAACyH,KAAK,EAAE;MAC7C,IAAI,CAAC6E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAAC6D,kBAAkB,CAACyH,KAAK;IAC5E;IACA,IAAI,OAAO,IAAI,CAACtL,WAAW,CAACyN,gBAAgB,CAACnC,KAAK,KAAK,SAAS,EAAE;MAChE,IAAI,CAAC6E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAACyN,gBAAgB,CAACnC,KAAK;IACzE;IACA,IAAI,IAAI,CAACtL,WAAW,CAACkC,kBAAkB,CAACoJ,KAAK,EAAE;MAC7C,IAAI,CAAC6E,WAAW,CAAC,YAAY,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAACkC,kBAAkB,CAACoJ,KAAK;IAC5E;IACA,IAAI,IAAI,CAACtL,WAAW,CAAC0C,iBAAiB,CAAC4I,KAAK,EAAE;MAC5C,IAAI,CAAC6E,WAAW,CAAC,WAAW,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAAC0C,iBAAiB,CAAC4I,KAAK;IAC1E;IACA,IAAI,IAAI,CAACtL,WAAW,CAACuN,mBAAmB,CAACjC,KAAK,EAAE;MAC9C,IAAI,CAAC6E,WAAW,CAAC,aAAa,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAACuN,mBAAmB,CAACjC,KAAK;IAC9E;IACA,IAAI,IAAI,CAACtL,WAAW,CAACwN,wBAAwB,CAAClC,KAAK,EAAE;MACnD,IAAI,CAAC6E,WAAW,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAACnQ,WAAW,CAACwN,wBAAwB,CAAClC,KAAK;IACxF;IAEA,OAAO,IAAI,CAAC6E,WAAW;EACzB;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAAC7P,MAAM,IAAI,CAAC,KAAK4P,CAAC,CAAC5P,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAoN,YAAYA,CAAA;IACV,OAAO,IAAI,CAACzD,aAAa,CAACmG,6BAA6B,CAAC;MACtDjB,IAAI,EAAE,IAAI,CAACQ,WAAW;KACvB,CAAC,CAAC1D,IAAI,CACL1P,GAAG,CAAC2P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACoC,SAAS,GAAGnE,GAAG,CAAC8B,OAAO;QAC5B,IAAI,CAACnD,YAAY,GAAGqB,GAAG,CAACoE,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAIAC,0BAA0BA,CAAA;IACxB;IACA,IAAI,CAACjB,gBAAgB,EAAE;IACvB,IAAI,CAAC5B,YAAY,EAAE,CAACrB,SAAS,EAAE;EACjC;EACAgB,gBAAgBA,CAAA;IACd,IAAI,CAAClD,iBAAiB,CAACqG,6CAA6C,CAAC;MACnErB,IAAI,EAAE;QACJsB,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;;KAEZ,CAAC,CAACzE,IAAI,CACL1P,GAAG,CAAC2P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAChK,oBAAoB,GAAGiI,GAAG,CAAC8B,OAAO,EAAE5F,MAAM,GAAG8D,GAAG,CAAC8B,OAAO,CAACwB,GAAG,CAACtD,GAAG,IAAG;UACtE,OAAO;YACLvO,cAAc,EAAEuO,GAAG,CAACvO,cAAc;YAClCgC,GAAG,EAAEuM,GAAG,CAACvM;WACV;QACH,CAAC,CAAC,GAAG,EAAE;QAEP,IAAI3C,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,IAAI,IACtExP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAIC,SAAS,IAC5EzP,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,IAAI,EAAE,EAAE;UAC1E,IAAIE,eAAe,GAAGC,IAAI,CAACC,KAAK,CAAC5P,mBAAmB,CAACuP,iBAAiB,CAACtP,WAAW,CAACuP,YAAY,CAAC,CAAC;UACjG,IAAIE,eAAe,CAAChN,kBAAkB,IAAI,IAAI,IAAIgN,eAAe,CAAChN,kBAAkB,IAAI+M,SAAS,EAAE;YACjG,IAAIhG,KAAK,GAAG,IAAI,CAACxC,oBAAoB,CAACyL,SAAS,CAAE3C,CAAM,IAAKA,CAAC,CAACpN,GAAG,IAAI+M,eAAe,CAAChN,kBAAkB,CAACC,GAAG,CAAC;YAC5G,IAAI,CAACF,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAACwC,KAAK,CAAC;UACxE,CAAC,MAAM;YACL,IAAI,CAAChH,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAAC,CAAC,CAAC;UACpE;QACF,CAAC,MACI;UACH,IAAI,CAACxE,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuE,oBAAoB,CAAC,CAAC,CAAC;QACpE;MACF;IACF,CAAC,CAAC,EACF1H,GAAG,CAAC,MAAK;MACP;MACA,IAAI,CAAC+S,gBAAgB,EAAE;MACvBqB,UAAU,CAAC,MAAK;QACd,IAAI,CAACjD,YAAY,EAAE,CAACrB,SAAS,EAAE;MACjC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,CACH,CAACA,SAAS,EAAE;EACf;EA6CAuE,YAAYA,CAAChR,GAAQ,EAAEiR,GAAQ;IAC7B,IAAI,CAACnP,cAAc,GAAG,EAAE;IACxB,IAAI,CAACuI,aAAa,CAAC6G,6BAA6B,CAAC;MAC/C3B,IAAI,EAAE;QAAE3D,QAAQ,EAAE5L;MAAG;KACtB,CAAC,CAACyM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,OAAO,IAAI9B,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACvL,WAAW,GAAG;UACjB,GAAGwJ,GAAG,CAAC8B,OAAO;UACdlK,eAAe,EAAEoI,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,GAAG,IAAIC,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAAC+C,gBAAgB,CAAC,GAAGtE,SAAS;UAClGzI,aAAa,EAAEkI,GAAG,CAAC8B,OAAO,CAACiD,cAAc,GAAG,IAAID,IAAI,CAAC9E,GAAG,CAAC8B,OAAO,CAACiD,cAAc,CAAC,GAAGxE;SACpF;QAED,IAAIP,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,CAACxP,cAAc,CAAChC,kBAAkB,GAAG,IAAI,CAACyR,eAAe,CAAC,IAAI,CAAClN,oBAAoB,EAAE,KAAK,EAAEiI,GAAG,CAAC8B,OAAO,CAACkD,YAAY,CAAC;QAC3H;QACA,IAAI,CAACxP,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACwP,eAAe,CAAC,IAAI,CAACrP,OAAO,CAACC,gBAAgB,EAAE,OAAO,EAAEmK,GAAG,CAAC8B,OAAO,CAACpN,UAAU,CAAC;QAC7H,IAAIsL,GAAG,CAAC8B,OAAO,CAACxN,UAAU,EAAE;UAC1B,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC6N,eAAe,CAAC,IAAI,CAACrP,OAAO,CAACoC,gBAAgB,EAAE,OAAO,EAAEgI,GAAG,CAAC8B,OAAO,CAACxN,UAAU,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAACxB,OAAO,CAACoC,gBAAgB,CAAC,CAAC,CAAC;QAC3E;QACA,IAAI,CAACxC,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACgP,eAAe,CAAC,IAAI,CAACrP,OAAO,CAACO,eAAe,EAAE,OAAO,EAAE6J,GAAG,CAAC8B,OAAO,CAACtC,SAAS,CAAC;QAE1H,IAAIQ,GAAG,CAAC8B,OAAO,CAACkD,YAAY,EAAE;UAC5B,IAAI,IAAI,CAAC1L,aAAa,EAAE;YACtB,IAAI,CAACA,aAAa,CAACuI,YAAY,GAAG7B,GAAG,CAAC8B,OAAO,CAACkD,YAAY;UAC5D;QACF;QACA,IAAI,CAACpH,aAAa,CAACsH,IAAI,CAACP,GAAG,CAAC;MAC9B;IAEF,CAAC,CAAC;EACJ;EAGAM,eAAeA,CAACE,KAAY,EAAErG,GAAW,EAAED,KAAU;IACnD,OAAOsG,KAAK,CAACvE,IAAI,CAACwE,IAAI,IAAIA,IAAI,CAACtG,GAAG,CAAC,KAAKD,KAAK,CAAC;EAChD;EAGA5L,eAAeA,CAAC0R,GAAQ,EAAES,IAAS;IACjC,IAAI,CAACV,YAAY,CAACU,IAAI,CAAC1R,GAAG,EAAEiR,GAAG,CAAC;EAClC;EAEAhS,SAASA,CAACgS,GAAQ;IAChB,IAAI,CAACrL,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBnF,MAAM,EAAEmM,SAAS;MACjB9G,eAAe,EAAE8G;KAClB;IACD,IAAI,CAAC3C,aAAa,CAACsH,IAAI,CAACP,GAAG,CAAC;EAC9B;EAKAU,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO/U,MAAM,CAAC+U,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA9M,cAAcA,CAACkM,GAAQ;IACrB,IAAI,CAACnO,WAAW,CAACqO,gBAAgB,GAAG,IAAI,CAACrO,WAAW,CAACoB,eAAe,GAAG,IAAI,CAACyN,UAAU,CAAC,IAAI,CAAC7O,WAAW,CAACoB,eAAe,CAAC,GAAG,EAAE,EAC3H,IAAI,CAACpB,WAAW,CAACuO,cAAc,GAAG,IAAI,CAACvO,WAAW,CAACsB,aAAa,GAAG,IAAI,CAACuN,UAAU,CAAC,IAAI,CAAC7O,WAAW,CAACsB,aAAa,CAAC,GAAG,EAAE,EAEvH,IAAI,CAAC0N,kBAAkB,GAAG;MACxB5O,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvC6I,QAAQ,EAAE,IAAI,CAAC9I,WAAW,CAACiP,GAAG;MAC9BnR,UAAU,EAAE,IAAI,CAACkB,cAAc,CAAC4B,kBAAkB,GAAG,IAAI,CAAC5B,cAAc,CAAC4B,kBAAkB,CAACyH,KAAK,GAAG,IAAI;MACxGtK,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7BuI,WAAW,EAAE,IAAI,CAAC/I,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAACc,cAAc,CAACC,kBAAkB,GAAG,IAAI,CAACD,cAAc,CAACC,kBAAkB,CAACoJ,KAAK,GAAG,IAAI;MACxG3H,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/BsI,SAAS,EAAE,IAAI,CAAChK,cAAc,CAACS,iBAAiB,GAAG,IAAI,CAACT,cAAc,CAACS,iBAAiB,CAAC4I,KAAK,GAAG,IAAI;MACrGgG,gBAAgB,EAAE,IAAI,CAACrO,WAAW,CAACqO,gBAAgB;MACnDE,cAAc,EAAE,IAAI,CAACvO,WAAW,CAACuO;KAClC;IACH,IAAI,CAACW,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5H,KAAK,CAAC6H,aAAa,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2B,OAAO,CAAC+H,aAAa,CAAC,IAAI,CAAC9H,KAAK,CAAC6H,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC5H,aAAa,CAAC8H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE,IAAI,CAACuC;KACZ,CAAC,CAACzF,IAAI,CACL1P,GAAG,CAAC2P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClE,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAACjI,OAAO,CAACoE,YAAY,CAACjC,GAAG,CAACkC,OAAQ,CAAC;QACvCyC,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF1V,SAAS,CAAC,MAAM,IAAI,CAACoR,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf;EAGA4F,QAAQA,CAACpB,GAAQ;IACf,IAAIqB,OAAO,GAAkB;MAC3BpP,aAAa,EAAE,IAAI,CAACJ,WAAW,CAACI,aAAa;MAC7CzC,UAAU,EAAE,IAAI,CAACqC,WAAW,CAACC,UAAU;MACvC6I,QAAQ,EAAE,IAAI,CAAC9I,WAAW,CAACiP,GAAG;MAC9BnR,UAAU,EAAE,IAAI,CAACkC,WAAW,CAAClC,UAAU;MACvCC,SAAS,EAAE,IAAI,CAACiC,WAAW,CAACjC,SAAS;MACrCO,SAAS,EAAE,IAAI,CAAC0B,WAAW,CAAC1B,SAAS;MACrCkC,KAAK,EAAE,IAAI,CAACR,WAAW,CAACQ,KAAK;MAC7BuI,WAAW,EAAE,IAAI,CAAC/I,WAAW,CAACM,WAAW;MACzCpC,UAAU,EAAE,IAAI,CAAC8B,WAAW,CAAC9B,UAAU;MACvCwC,MAAM,EAAE,IAAI,CAACV,WAAW,CAACU,MAAM;MAC/BsI,SAAS,EAAE,IAAI,CAAChJ,WAAW,CAACgJ;KAC7B;IACD,IAAI,CAACzB,aAAa,CAAC8H,0BAA0B,CAAC;MAC5C5C,IAAI,EAAE+C;KACP,CAAC,CAAC7F,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClE,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EAEJ;EAEAjN,OAAOA,CAAC8L,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAG,YAAYA,CAACC,IAAS,EAAEC,EAAQ;IAC9B,MAAMC,KAAK,GAAGD,EAAE,GAAGA,EAAE,GAAG,IAAI,CAAC5S,WAAW,CAACC,kBAAkB,CAACC,GAAG;IAC/D,IAAI,CAAC0K,MAAM,CAACkI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;EACtE;EAEA9S,4BAA4BA,CAAC4S,IAAS,EAAEI,WAAgB,EAAEC,OAAY;IACpE,IAAI,CAACpI,MAAM,CAACkI,QAAQ,CAAC,CAAC,+BAA+BH,IAAI,EAAE,EAAEI,WAAW,EAAEC,OAAO,CAAC,CAAC;EACrF;EAEAzS,cAAcA,CAACsR,IAAS;IACtB,IAAIoB,OAAO,CAAC,UAAU,CAAC,EAAE;MACvB,IAAI,CAACzI,aAAa,CAAC0I,oCAAoC,CAAC;QACtDxD,IAAI,EAAEmC,IAAI,CAAC1R;OACZ,CAAC,CAACyM,SAAS,CAACH,GAAG,IAAG;QACjB,IAAIA,GAAG,CAAC+B,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAAClE,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;QACpC;MACF,CAAC,CAAC;IACJ;EACF;EAIAuC,UAAUA,CAAA;IACR,IAAI,CAAC5H,KAAK,CAAC4I,KAAK,EAAE;IAClB,IAAI,CAAC5I,KAAK,CAAC6I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnQ,WAAW,CAACiP,GAAG,CAAC;IACnD,IAAI,CAAC3H,KAAK,CAAC6I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnB,kBAAkB,CAACrR,UAAU,CAAC;IACjE,IAAI,CAAC2J,KAAK,CAAC8I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACpB,kBAAkB,CAACrR,UAAU,EAAE,EAAE,CAAC;IAC5E,IAAI,CAAC2J,KAAK,CAAC6I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnQ,WAAW,CAACpC,MAAM,CAAC;IACpD,IAAI,CAAC0J,KAAK,CAAC8I,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACpB,kBAAkB,CAAC5O,aAAa,EAAE,EAAE,CAAC;IACjF;IACA;IACA;IACA,IAAI,CAACkH,KAAK,CAAC+I,OAAO,CAAC,QAAQ,EAAE,IAAI,CAACrB,kBAAkB,CAACxO,KAAK,EAAE,IAAI,CAACkH,OAAO,CAAC4I,WAAW,CAAC;IACrF,IAAI,CAAChJ,KAAK,CAACiJ,aAAa,CAAC,QAAQ,EAAE,IAAI,CAACvB,kBAAkB,CAACtO,MAAM,CAAC;IAClE,IAAI,CAAC4G,KAAK,CAAC6I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACnB,kBAAkB,CAAChG,SAAS,CAAC;IAC9D,IAAI,CAAC1B,KAAK,CAAC6I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnR,cAAc,CAAC4B,kBAAkB,CAACyH,KAAK,CAAC;IAC3E,IAAI,CAACf,KAAK,CAAC6I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACnR,cAAc,CAACC,kBAAkB,CAACoJ,KAAK,CAAC;IAC3E,IAAI,IAAI,CAACrI,WAAW,CAACqO,gBAAgB,EAAE;MACrC,IAAI,CAAC/G,KAAK,CAAC6I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnQ,WAAW,CAACuO,cAAc,CAAC;IAClE;IACA,IAAI,IAAI,CAACvO,WAAW,CAACuO,cAAc,EAAE;MACnC,IAAI,CAACjH,KAAK,CAAC6I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnQ,WAAW,CAACqO,gBAAgB,CAAC;IACpE;IACA,IAAI,CAAC/G,KAAK,CAACkJ,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACxQ,WAAW,CAACqO,gBAAgB,GAAG,IAAI,CAACrO,WAAW,CAACqO,gBAAgB,GAAG,EAAE,EAAE,IAAI,CAACrO,WAAW,CAACuO,cAAc,GAAG,IAAI,CAACvO,WAAW,CAACuO,cAAc,GAAG,EAAE,CAAC;EAC9L;EAEAkC,uBAAuBA,CAAA;IACrB,IAAI,CAACnJ,KAAK,CAAC4I,KAAK,EAAE;IAClB,IAAI,CAAC5I,KAAK,CAAC6I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACrN,aAAa,CAACuI,YAAY,CAAC;IAC5D,IAAI,CAAC/D,KAAK,CAAC6I,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACrN,aAAa,CAACC,aAAa,CAAC;IAC7D,IAAI,CAACuE,KAAK,CAAC8I,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAACtN,aAAa,CAACC,aAAa,EAAE,EAAE,CAAC;IAC1E,IAAI,CAACuE,KAAK,CAACoJ,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC5N,aAAa,CAAClF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAC0J,KAAK,CAACoJ,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC5N,aAAa,CAACG,eAAe,EAAE,CAAC,EAAE,GAAG,CAAC;EAC1F;EAGAN,gBAAgBA,CAACwL,GAAQ;IACvB,IAAI,CAACrL,aAAa,CAACuI,YAAY,GAAG,IAAI,CAACtO,WAAW,CAACC,kBAAkB,CAACC,GAAG,EACvE,IAAI,CAACwT,uBAAuB,EAAE;IAChC,IAAI,IAAI,CAACnJ,KAAK,CAAC6H,aAAa,CAACzJ,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2B,OAAO,CAAC+H,aAAa,CAAC,IAAI,CAAC9H,KAAK,CAAC6H,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAAC3H,qBAAqB,CAACmJ,yCAAyC,CAAC;MACnElE,IAAI,EAAE,IAAI,CAAC3J;KACZ,CAAC,CAACyG,IAAI,CACL1P,GAAG,CAAC2P,GAAG,IAAG;MACR,IAAIA,GAAG,CAAC+B,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAClE,OAAO,CAACsF,aAAa,CAAC,MAAM,CAAC;QAClCwB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC,EACF1V,SAAS,CAAC,MAAM,IAAI,CAACoR,YAAY,EAAE,CAAC,CACrC,CAACrB,SAAS,EAAE;EACf,CAAC,CAAE;EACGlM,aAAaA,CAACmT,MAAW,EAAEhC,IAAS;IAAA,IAAAiC,KAAA;IAAA,OAAAC,iBAAA;MACxCD,KAAI,CAACpK,YAAY,GAAGmI,IAAI;MACxBiC,KAAI,CAACpL,cAAc,GAAG,EAAE;MACxBoL,KAAI,CAAC/J,WAAW,GAAG,CAAC;MACpB+J,KAAI,CAACnK,kBAAkB,GAAG,CAAC,CAAC,CAAC;MAC7BmK,KAAI,CAAC9L,mBAAmB,GAAG,IAAI,CAAC,CAAC;MACjC;MACA8L,KAAI,CAAC5H,iBAAiB,GAAG,KAAK;MAC9B4H,KAAI,CAAC3H,uBAAuB,GAAG,CAAC;MAChC2H,KAAI,CAAC9J,mBAAmB,GAAG,CAAC;MAC5B8J,KAAI,CAAChK,gBAAgB,GAAG,CAAC;MACzBgK,KAAI,CAAC1H,mBAAmB,GAAG,IAAI;MAE/B;MACA,IAAI;QACF,MAAM4H,QAAQ,SAASF,KAAI,CAAC/I,gBAAgB,CAACkJ,qBAAqB,CAACpC,IAAI,CAAC1R,GAAG,CAAC,CAAC+T,SAAS,EAAE;QAExF,IAAIF,QAAQ,IAAIA,QAAQ,CAACxF,UAAU,KAAK,CAAC,IAAIwF,QAAQ,CAACzF,OAAO,EAAE;UAC7D;UACAuF,KAAI,CAACnK,kBAAkB,GAAGqK,QAAQ,CAACzF,OAAO,CAAC4F,mBAAmB,IAAI,CAAC;UACnE;UACA,IAAIH,QAAQ,CAACzF,OAAO,CAACjN,gBAAgB,KAAK,CAAC,EAAE;YAAE;YAC7CwS,KAAI,CAAC9L,mBAAmB,GAAG,KAAK;UAClC,CAAC,MAAM;YACL8L,KAAI,CAAC9L,mBAAmB,GAAG,IAAI;UACjC;UAEA;UACA8L,KAAI,CAAC1H,mBAAmB,GAAG,IAAI;UAC/B0H,KAAI,CAAC5H,iBAAiB,GAAG,KAAK;UAC9B4H,KAAI,CAAC3H,uBAAuB,GAAG,CAAC;UAEhC;UACA,IAAI6H,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAAC,EAAE;YACnE;YACAN,KAAI,CAACpL,cAAc,GAAGsL,QAAQ,CAACzF,OAAO,CAAC6F,KAAK,CAACrE,GAAG,CAAEwE,KAAU,KAAM;cAChEC,QAAQ,EAAER,QAAQ,CAACzF,OAAO,CAACxC,QAAQ,IAAI8F,IAAI,CAAC1R,GAAG;cAC/CsU,YAAY,EAAET,QAAQ,CAACzF,OAAO,CAACmG,YAAY;cAC3CrN,SAAS,EAAEkN,KAAK,CAACI,SAAS,IAAI,EAAE;cAChCjN,KAAK,EAAE6M,KAAK,CAACK,KAAK,IAAI,EAAE;cACxBrN,UAAU,EAAEgN,KAAK,CAACM,UAAU,IAAI,CAAC;cACjCjN,MAAM,EAAE2M,KAAK,CAACO,MAAM,IAAI,CAAC;cACzBC,OAAO,EAAER,KAAK,CAACtD,OAAO,IAAI,CAAC;cAC3BxT,kBAAkB,EAAE8W,KAAK,CAAC9W,kBAAkB,IAAI8W,KAAK,CAAC9W,kBAAkB,GAAG,CAAC,GAAG8W,KAAK,CAAC9W,kBAAkB,GAAGA,kBAAkB,CAACuX,GAAG;cAChIC,OAAO,EAAEV,KAAK,CAACW,OAAO,IAAI,EAAE;cAC5BC,gBAAgB,EAAEZ,KAAK,CAACjT;aACzB,CAAC,CAAC;YACHwS,KAAI,CAACtM,cAAc,EAAE;UACvB,CAAC,MAAM,CAEP;QACF,CAAC,MAAM,CAEP;MACF,CAAC,CAAC,OAAO4N,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;MAEAtB,KAAI,CAACzJ,aAAa,CAACsH,IAAI,CAACkC,MAAM,EAAE;QAC9ByB,OAAO,EAAEzD,IAAI;QACb0D,oBAAoB,EAAE;OACvB,CAAC;IAAC;EACL;EAEA;EACAlN,kBAAkBA,CAAA;IAChB,IAAI,CAACsB,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACjB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACV,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC+B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACD,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACE,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACoC,mBAAmB,GAAG,IAAI;IAE/B;IACA,IAAI,CAAC9B,OAAO,CAACsF,aAAa,CAAC,eAAe,CAAC;EAC7C;EACA;EACApJ,gBAAgBA,CAAA;IACd,IAAI,CAACkC,cAAc,CAAC8M,IAAI,CAAC;MACvBhB,QAAQ,EAAE,IAAI,CAAC9K,YAAY,EAAEvJ,GAAG,IAAI,CAAC;MACrCkH,SAAS,EAAE,EAAE;MACbK,KAAK,EAAE,EAAE;MACTH,UAAU,EAAE,CAAC;MACbK,MAAM,EAAE,CAAC;MACTmN,OAAO,EAAE,CAAC;MACVtX,kBAAkB,EAAEA,kBAAkB,CAACuX,GAAG;MAC1CC,OAAO,EAAE;KACV,CAAC;EACJ;EACA;EACMvO,gBAAgBA,CAAA;IAAA,IAAA+O,MAAA;IAAA,OAAA1B,iBAAA;MACpB,IAAI;QACF,IAAI,CAAC0B,MAAI,CAAC/L,YAAY,EAAEvJ,GAAG,EAAE;UAC3BsV,MAAI,CAACnL,OAAO,CAACoE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAMgH,OAAO,GAAG;UACdpH,YAAY,EAAEmH,MAAI,CAACzV,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5D6L,QAAQ,EAAE0J,MAAI,CAAC/L,YAAY,CAACvJ;SAC7B;QAED,MAAM6T,QAAQ,SAASyB,MAAI,CAAC1K,gBAAgB,CAACrE,gBAAgB,CAACgP,OAAO,CAAC,CAACxB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE2B,OAAO,IAAI3B,QAAQ,CAAC4B,IAAI,EAAE;UACtC,MAAMC,YAAY,GAAG7B,QAAQ,CAAC4B,IAAI,CAAC7F,GAAG,CAAEzC,CAAM,KAAM;YAClDmH,YAAY,EAAEnH,CAAC,CAACoH,YAAY;YAC5BF,QAAQ,EAAEiB,MAAI,CAAC/L,YAAY,EAAEvJ,GAAG;YAChCkH,SAAS,EAAEiG,CAAC,CAACqH,SAAS;YACtBjN,KAAK,EAAE4F,CAAC,CAACsH,KAAK,IAAI,EAAE;YACpBrN,UAAU,EAAE+F,CAAC,CAACuH,UAAU;YACxBjN,MAAM,EAAE0F,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC2D,OAAO;YAClBxT,kBAAkB,EAAEA,kBAAkB,CAACqY,IAAI;YAC3Cb,OAAO,EAAE3H,CAAC,CAAC4H;WACZ,CAAC,CAAC;UACHO,MAAI,CAAC/M,cAAc,CAAC8M,IAAI,CAAC,GAAGK,YAAY,CAAC;UACzCJ,MAAI,CAACjO,cAAc,EAAE;UACrBiO,MAAI,CAACnL,OAAO,CAACsF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACL6F,MAAI,CAACnL,OAAO,CAACoE,YAAY,CAACsF,QAAQ,EAAE1J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO8K,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCK,MAAI,CAACnL,OAAO,CAACoE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACM9H,gBAAgBA,CAAA;IAAA,IAAAmP,MAAA;IAAA,OAAAhC,iBAAA;MACpB,IAAI;QACF,IAAI,CAACgC,MAAI,CAACrM,YAAY,EAAEvJ,GAAG,EAAE;UAC3B4V,MAAI,CAACzL,OAAO,CAACoE,YAAY,CAAC,QAAQ,CAAC;UACnC;QACF;QAEA,MAAMgH,OAAO,GAAG;UACdpH,YAAY,EAAEyH,MAAI,CAAC/V,WAAW,EAAEC,kBAAkB,EAAEC,GAAG,IAAI,CAAC;UAC5D6L,QAAQ,EAAEgK,MAAI,CAACrM,YAAY,CAACvJ;SAC7B;QAED,MAAM6T,QAAQ,SAAS+B,MAAI,CAAChL,gBAAgB,CAACnE,gBAAgB,CAAC8O,OAAO,CAAC,CAACxB,SAAS,EAAE;QAClF,IAAIF,QAAQ,EAAE2B,OAAO,IAAI3B,QAAQ,CAAC4B,IAAI,EAAE;UACtC,MAAMI,YAAY,GAAGhC,QAAQ,CAAC4B,IAAI,CAAC7F,GAAG,CAAEzC,CAAM,KAAM;YAClDmH,YAAY,EAAEnH,CAAC,CAACoH,YAAY;YAC5BF,QAAQ,EAAEuB,MAAI,CAACrM,YAAY,EAAEvJ,GAAG;YAChCkH,SAAS,EAAEiG,CAAC,CAACqH,SAAS;YACtBjN,KAAK,EAAE4F,CAAC,CAACsH,KAAK,IAAI,EAAE;YACpBrN,UAAU,EAAE+F,CAAC,CAACuH,UAAU;YACxBjN,MAAM,EAAE0F,CAAC,CAACwH,MAAM;YAChBC,OAAO,EAAEzH,CAAC,CAAC2D,OAAO;YAClBxT,kBAAkB,EAAEA,kBAAkB,CAACwY,EAAE;YAAE;YAC3ChB,OAAO,EAAE3H,CAAC,CAAC4H,OAAO,IAAI;WACvB,CAAC,CAAC;UACHa,MAAI,CAACrN,cAAc,CAAC8M,IAAI,CAAC,GAAGQ,YAAY,CAAC;UACzCD,MAAI,CAACvO,cAAc,EAAE;UACrBuO,MAAI,CAACzL,OAAO,CAACsF,aAAa,CAAC,UAAU,CAAC;QACxC,CAAC,MAAM;UACLmG,MAAI,CAACzL,OAAO,CAACoE,YAAY,CAACsF,QAAQ,EAAE1J,OAAO,IAAI,UAAU,CAAC;QAC5D;MACF,CAAC,CAAC,OAAO8K,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCW,MAAI,CAACzL,OAAO,CAACoE,YAAY,CAAC,UAAU,CAAC;MACvC;IAAC;EACH;EAEA;EACAzH,mBAAmBA,CAACD,KAAa;IAC/B,MAAM6K,IAAI,GAAG,IAAI,CAACnJ,cAAc,CAAC1B,KAAK,CAAC;IACvC,IAAI,CAAC0B,cAAc,CAACwN,MAAM,CAAClP,KAAK,EAAE,CAAC,CAAC;IACpC,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;EACAA,cAAcA,CAAA;IACZ,IAAI,CAACuC,WAAW,GAAG,IAAI,CAACrB,cAAc,CAACyN,MAAM,CAAC,CAACC,GAAG,EAAEvE,IAAI,KAAI;MAC1D,OAAOuE,GAAG,GAAIvE,IAAI,CAACtK,UAAU,GAAGsK,IAAI,CAACjK,MAAO;IAC9C,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAACyO,mBAAmB,EAAE;EAC5B;EAEA;EACAA,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACrM,mBAAmB,GAAGsM,IAAI,CAACC,KAAK,CAAC,IAAI,CAACxM,WAAW,GAAG,IAAI,CAAC;IAC9D,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,mBAAmB;EACrE;EAEA;EACA/B,cAAcA,CAACuO,MAAc;IAC3B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAAC7E,MAAM,CAACwE,MAAM,CAAC;EACnB;EAEA;EACA3M,YAAYA,CAAC2M,MAAc;IACzB,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCG,qBAAqB,EAAE;KACxB,CAAC,CAAC7E,MAAM,CAACwE,MAAM,CAAC;EACnB;EAEA;EACA5M,cAAcA,CAAA;IACZ,OAAO,IAAI2H,IAAI,EAAE,CAACuF,kBAAkB,CAAC,OAAO,EAAE;MAC5CC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE;KACN,CAAC;EACJ;EAIA;EACMnO,aAAaA,CAACsI,GAAQ;IAAA,IAAA8F,MAAA;IAAA,OAAAnD,iBAAA;MAC1B,IAAImD,MAAI,CAACxO,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpCuO,MAAI,CAAC5M,OAAO,CAACoE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA;MACA,MAAMyI,YAAY,GAAGD,MAAI,CAACxO,cAAc,CAAC0O,MAAM,CAACvF,IAAI,IAClD,CAACA,IAAI,CAACxK,SAAS,CAACgQ,IAAI,EAAE,CACvB;MAED,IAAIF,YAAY,CAACxO,MAAM,GAAG,CAAC,EAAE;QAC3BuO,MAAI,CAAC5M,OAAO,CAACoE,YAAY,CAAC,iBAAiB,CAAC;QAC5C;MACF;MAAE,IAAI;QACJ,MAAMgH,OAAO,GAAG;UACd1C,OAAO,EAAEkE,MAAI,CAACxN,YAAY,CAACvJ,GAAG;UAC9BmX,KAAK,EAAEJ,MAAI,CAACxO,cAAc;UAC1B6O,WAAW,EAAEL,MAAI,CAACvN,kBAAkB;UAAE;UACtC;UACA6N,UAAU,EAAEN,MAAI,CAAC9K,mBAAmB;UAAE;UACtCqL,UAAU,EAAEP,MAAI,CAAChL,iBAAiB;UAAI;UACtCwL,aAAa,EAAER,MAAI,CAAC/K,uBAAuB,CAAC;SAC7C;QAED,MAAM6H,QAAQ,SAASkD,MAAI,CAACnM,gBAAgB,CAACjC,aAAa,CAAC4M,OAAO,CAAC,CAACxB,SAAS,EAAE;QAC/E,IAAIF,QAAQ,EAAE2B,OAAO,EAAE;UACrBuB,MAAI,CAAC5M,OAAO,CAACsF,aAAa,CAAC,SAAS,CAAC;UACrCwB,GAAG,CAACmB,KAAK,EAAE;QACb,CAAC,MAAM;UACL2E,MAAI,CAAC5M,OAAO,CAACoE,YAAY,CAACsF,QAAQ,EAAE1J,OAAO,IAAI,MAAM,CAAC;QACxD;MACF,CAAC,CAAC,OAAO8K,KAAK,EAAE;QACd8B,MAAI,CAAC5M,OAAO,CAACoE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACMiJ,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACnB,IAAI;QACF,MAAM8D,IAAI,SAA2BD,MAAI,CAAC7M,gBAAgB,CAAC4M,eAAe,CAACC,MAAI,CAAClO,YAAY,CAACvJ,GAAG,CAAC,CAAC+T,SAAS,EAAE;QAC7G,IAAI2D,IAAI,EAAE;UACR,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;UACfI,IAAI,CAACI,QAAQ,GAAG,OAAOV,MAAI,CAAClO,YAAY,CAAC9I,UAAU,IAAIgX,MAAI,CAAClO,YAAY,CAAC7I,MAAM,OAAO;UACtFqX,IAAI,CAACnJ,KAAK,EAAE;UACZgJ,MAAM,CAACC,GAAG,CAACO,eAAe,CAACT,GAAG,CAAC;QACjC,CAAC,MAAM;UACLF,MAAI,CAACtN,OAAO,CAACoE,YAAY,CAAC,iBAAiB,CAAC;QAC9C;MACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;QACdwC,MAAI,CAACtN,OAAO,CAACoE,YAAY,CAAC,SAAS,CAAC;MACtC;IAAC;EACH;EAEA;EACArF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACX,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;MACpC,IAAI,CAAC2B,OAAO,CAACoE,YAAY,CAAC,YAAY,CAAC;MACvC;IACF;IAEA,IAAI;MACF;MACA,MAAM8J,YAAY,GAAG,IAAI,CAACC,oBAAoB,EAAE;MAEhD;MACA,MAAMC,WAAW,GAAGX,MAAM,CAACpG,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,mDAAmD,CAAC;MAClG,IAAI+G,WAAW,EAAE;QACfA,WAAW,CAACP,QAAQ,CAACxG,IAAI,EAAE;QAC3B+G,WAAW,CAACP,QAAQ,CAACQ,KAAK,CAACH,YAAY,CAAC;QACxCE,WAAW,CAACP,QAAQ,CAAC5F,KAAK,EAAE;QAE5B;QACAmG,WAAW,CAACE,MAAM,GAAG;UACnB1H,UAAU,CAAC,MAAK;YACdwH,WAAW,CAACG,KAAK,EAAE;YACnB;UACF,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAACvO,OAAO,CAACoE,YAAY,CAAC,yBAAyB,CAAC;MACtD;IACF,CAAC,CAAC,OAAO0G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAAC9K,OAAO,CAACoE,YAAY,CAAC,YAAY,CAAC;IACzC;EACF;EAEA;EACQ+J,oBAAoBA,CAAA;IAC1B;IACA,MAAMK,QAAQ,GAAGpc,kBAAkB;IAEnC;IACA,MAAMqc,WAAW,GAAG,IAAIxH,IAAI,EAAE,CAACuF,kBAAkB,CAAC,OAAO,CAAC;IAC1D,MAAMkC,aAAa,GAAG,IAAI,CAAChZ,WAAW,CAACC,kBAAkB,EAAE/B,cAAc,IAAI,EAAE;IAE/E;IACA,IAAI+a,SAAS,GAAG,EAAE;IAClB,IAAI,CAACvQ,cAAc,CAACwQ,OAAO,CAAC,CAACrH,IAAI,EAAE7K,KAAK,KAAI;MAC1C,MAAMmS,QAAQ,GAAGtH,IAAI,CAACtK,UAAU,GAAGsK,IAAI,CAACjK,MAAM;MAC9C,MAAMwR,aAAa,GAAG,IAAI,CAAClR,oBAAoB,CAAC2J,IAAI,CAACpU,kBAAkB,CAAC;MACxE,MAAM4b,IAAI,GAAGxH,IAAI,CAACnK,KAAK,IAAI,EAAE;MAC7BuR,SAAS,IAAI;;sCAEmBjS,KAAK,GAAG,CAAC;kBAC7B6K,IAAI,CAACxK,SAAS;qCACK,IAAI,CAACY,cAAc,CAAC4J,IAAI,CAACtK,UAAU,CAAC;sCACnC8R,IAAI;sCACJxH,IAAI,CAACjK,MAAM;qCACZ,IAAI,CAACK,cAAc,CAACkR,QAAQ,CAAC;sCAC5BC,aAAa;;SAE1C;IACL,CAAC,CAAC;IAEF;IACA,MAAME,iBAAiB,GAAG,IAAI,CAAClN,mBAAmB,GAAG;;YAE7C,IAAI,CAACF,iBAAiB,KAAK,IAAI,CAACC,uBAAuB,MAAM,IAAI,CAAClE,cAAc,CAAC,IAAI,CAAC+B,mBAAmB,CAAC;;OAE/G,GAAG,EAAE;IAER;IACA,MAAMuP,IAAI,GAAGT,QAAQ,CAClBU,OAAO,CAAC,oBAAoB,EAAER,aAAa,CAAC,CAC5CQ,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC9P,YAAY,EAAE9I,UAAU,IAAI,EAAE,CAAC,CAC9D4Y,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC9P,YAAY,EAAE7I,MAAM,IAAI,EAAE,CAAC,CACtD2Y,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC9P,YAAY,EAAErG,aAAa,IAAI,EAAE,CAAC,CACpEmW,OAAO,CAAC,gBAAgB,EAAET,WAAW,CAAC,CACtCS,OAAO,CAAC,gBAAgB,EAAEP,SAAS,CAAC,CACpCO,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAACvR,cAAc,CAAC,IAAI,CAAC8B,WAAW,CAAC,CAAC,CACrEyP,OAAO,CAAC,wBAAwB,EAAEF,iBAAiB,CAAC,CACpDE,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAACvR,cAAc,CAAC,IAAI,CAAC6B,gBAAgB,CAAC,CAAC,CACvE0P,OAAO,CAAC,oBAAoB,EAAE,IAAIjI,IAAI,EAAE,CAACkI,cAAc,CAAC,OAAO,CAAC,CAAC;IAEpE,OAAOF,IAAI;EACb;EAGA;EACM9Q,aAAaA,CAAC2I,GAAQ;IAAA,IAAAsI,MAAA;IAAA,OAAA3F,iBAAA;MAC1B,IAAI2F,MAAI,CAAChR,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC+Q,MAAI,CAACpP,OAAO,CAACoE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI,CAACgL,MAAI,CAAC/P,kBAAkB,EAAE;QAC5B+P,MAAI,CAACpP,OAAO,CAACoE,YAAY,CAAC,UAAU,CAAC;QACrC;MACF;MAEA,IAAI;QACF,MAAMsF,QAAQ,SAAS0F,MAAI,CAAC3O,gBAAgB,CAACtC,aAAa,CAACiR,MAAI,CAAC/P,kBAAkB,CAAC,CAACuK,SAAS,EAAE;QAE/F,IAAIF,QAAQ,CAAC2B,OAAO,EAAE;UACpB+D,MAAI,CAACpP,OAAO,CAACsF,aAAa,CAAC,UAAU,CAAC;UACtCyF,OAAO,CAACsE,GAAG,CAAC,UAAU,EAAE;YACtBpC,WAAW,EAAEmC,MAAI,CAAC/P,kBAAkB;YACpCW,OAAO,EAAE0J,QAAQ,CAAC1J;WACnB,CAAC;QACJ,CAAC,MAAM;UACLoP,MAAI,CAACpP,OAAO,CAACoE,YAAY,CAACsF,QAAQ,CAAC1J,OAAO,IAAI,SAAS,CAAC;UACxD+K,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEpB,QAAQ,CAAC1J,OAAO,CAAC;QAC7C;QAEA8G,GAAG,CAACmB,KAAK,EAAE;MACb,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdsE,MAAI,CAACpP,OAAO,CAACoE,YAAY,CAAC,SAAS,CAAC;QACpC2G,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IAAC;EACH;EAEA;EACAlN,oBAAoBA,CAACkR,aAAiC;IACpD,QAAQA,aAAa;MACnB,KAAK3b,kBAAkB,CAACqY,IAAI;QAC1B,OAAO,MAAM;MACf,KAAKrY,kBAAkB,CAACuX,GAAG;QACzB,OAAO,KAAK;MACd,KAAKvX,kBAAkB,CAACwY,EAAE;QACxB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EAEA5U,sBAAsBA,CAACuY,MAAc;IACnC,QAAQA,MAAM;MACZ,KAAKtc,mBAAmB,CAACuc,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKvc,mBAAmB,CAACwc,GAAG;QAC1B,OAAO,KAAK;MACd,KAAKxc,mBAAmB,CAACyc,GAAG;QAC1B,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;;;uCAniCW9P,4BAA4B,EAAAvM,EAAA,CAAAsc,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxc,EAAA,CAAAsc,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA1c,EAAA,CAAAsc,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5c,EAAA,CAAAsc,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9c,EAAA,CAAAsc,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAAhd,EAAA,CAAAsc,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAld,EAAA,CAAAsc,iBAAA,CAAAW,EAAA,CAAAE,oBAAA,GAAAnd,EAAA,CAAAsc,iBAAA,CAAAW,EAAA,CAAAG,gBAAA,GAAApd,EAAA,CAAAsc,iBAAA,CAAAe,EAAA,CAAAC,aAAA,GAAAtd,EAAA,CAAAsc,iBAAA,CAAAiB,EAAA,CAAAC,MAAA,GAAAxd,EAAA,CAAAsc,iBAAA,CAAAmB,EAAA,CAAAC,YAAA,GAAA1d,EAAA,CAAAsc,iBAAA,CAAAqB,GAAA,CAAAC,cAAA,GAAA5d,EAAA,CAAAsc,iBAAA,CAAAuB,GAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAA5BvR,4BAA4B;MAAAwR,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC9DvCle,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAyG,SAAA,qBAAiC;UACnCzG,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIjFH,EAHN,CAAAC,cAAA,aAA8B,aACN,cACqC,gBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBACkD;UADtBD,EAAA,CAAAkE,gBAAA,2BAAAka,0EAAAha,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAC,kBAAA,EAAA6B,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAC,kBAAA,GAAA6B,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACtEpE,EAAA,CAAAiB,UAAA,4BAAAqd,2EAAA;YAAAte,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAkB0c,GAAA,CAAA/K,0BAAA,EAA4B;UAAA,EAAC;UAC/CpT,EAAA,CAAAiC,UAAA,KAAAsc,kDAAA,wBAAoE;UAK1Eve,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,qBAAsE;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAAsa,0EAAApa,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAA6D,kBAAA,EAAA/B,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAA6D,kBAAA,GAAA/B,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACrDpE,EAAA,CAAAiC,UAAA,KAAAwc,kDAAA,wBAAgE;UAKtEze,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,eAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACiE;UAAhCD,EAAA,CAAAkE,gBAAA,2BAAAwa,sEAAAta,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAA0N,KAAA,EAAA5L,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAA0N,KAAA,GAAA5L,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA+B;UAC5FpE,EADE,CAAAG,YAAA,EAA2F,EAC7E;UAChBH,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAE,MAAA,UAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,yBAA4B,iBACuD;UAA9BD,EAAA,CAAAkE,gBAAA,2BAAAya,sEAAAva,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAA2N,GAAA,EAAA7L,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAA2N,GAAA,GAAA7L,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA6B;UAGtFpE,EAHM,CAAAG,YAAA,EAAiF,EACnE,EACZ,EACF;UAENH,EAAA,CAAAyG,SAAA,cAWM;UAIFzG,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAA0a,0EAAAxa,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAoN,kBAAA,EAAAtL,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAoN,kBAAA,GAAAtL,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACtEpE,EAAA,CAAAiC,UAAA,KAAA4c,kDAAA,wBAAgE;UAKtE7e,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACX;UAC1CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAyF;UAA3DD,EAAA,CAAAkE,gBAAA,2BAAA4a,0EAAA1a,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAkC,kBAAA,EAAAJ,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAkC,kBAAA,GAAAJ,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA4C;UACxEpE,EAAA,CAAAiC,UAAA,KAAA8c,kDAAA,wBAAgE;UAKtE/e,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACZ;UACzCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAsF;UAA1DD,EAAA,CAAAkE,gBAAA,2BAAA8a,0EAAA5a,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAA0C,iBAAA,EAAAZ,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAA0C,iBAAA,GAAAZ,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA2C;UACrEpE,EAAA,CAAAiC,UAAA,KAAAgd,kDAAA,wBAA+D;UAKrEjf,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAqF;UAAzDD,EAAA,CAAAkE,gBAAA,2BAAAgb,0EAAA9a,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAyN,gBAAA,EAAA3L,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAyN,gBAAA,GAAA3L,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA0C;UACpEpE,EAAA,CAAAiC,UAAA,KAAAkd,kDAAA,wBAAgE;UAKtEnf,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA0F;UAA5DD,EAAA,CAAAkE,gBAAA,2BAAAkb,0EAAAhb,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAuN,mBAAA,EAAAzL,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAuN,mBAAA,GAAAzL,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAA6C;UACzEpE,EAAA,CAAAiC,UAAA,KAAAod,kDAAA,wBAAiE;UAKvErf,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,eACqC,iBACL;UAChDD,EAAA,CAAAE,MAAA,wCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAgG;UAAjED,EAAA,CAAAkE,gBAAA,2BAAAob,0EAAAlb,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA7b,WAAA,CAAAwN,wBAAA,EAAA1L,MAAA,MAAA+Z,GAAA,CAAA7b,WAAA,CAAAwN,wBAAA,GAAA1L,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAAkD;UAC/EpE,EAAA,CAAAiC,UAAA,KAAAsd,kDAAA,wBAAsE;UAK5Evf,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAENH,EAAA,CAAAyG,SAAA,cAEM;UAKFzG,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACO;UAArBD,EAAA,CAAAiB,UAAA,mBAAAue,+DAAA;YAAAxf,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAS0c,GAAA,CAAAhO,QAAA,EAAU;UAAA,EAAC;UAC3DnQ,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAyG,SAAA,aAA6B;UAGtCzG,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGJH,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAAiC,UAAA,KAAAwd,+CAAA,qBAAmG;UAGnGzf,EAAA,CAAAC,cAAA,kBAAqF;UAA5CD,EAAA,CAAAiB,UAAA,mBAAAye,+DAAA;YAAA1f,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAS0c,GAAA,CAAAnJ,YAAA,CAAa,mBAAmB,CAAC;UAAA,EAAC;UAClFhV,EAAA,CAAAE,MAAA,8CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,kBAAiE;UAAxBD,EAAA,CAAAiB,UAAA,mBAAA0e,+DAAA;YAAA3f,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAS0c,GAAA,CAAAzN,WAAA,EAAa;UAAA,EAAC;UAC9D1Q,EAAA,CAAAE,MAAA,oDACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,oBAA4G;UAAzDD,EAAA,CAAAiB,UAAA,oBAAA2e,+DAAAxb,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAU0c,GAAA,CAAA7M,cAAA,CAAAlN,MAAA,CAAsB;UAAA,EAAC;UAApFpE,EAAA,CAAAG,YAAA,EAA4G;UAC5GH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAiB,UAAA,mBAAA4e,+DAAA;YAAA7f,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAS0c,GAAA,CAAAjN,gBAAA,EAAkB;UAAA,EAAC;UAC9DlR,EAAA,CAAAE,MAAA,gEACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAOEH,EALR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cAErB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,uCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAEpCF,EAFoC,CAAAG,YAAA,EAAK,EAClC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAiC,UAAA,MAAA6d,4CAAA,mBAAmD;UA8C3D9f,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,2BAAsD,2BAES;UAD7CD,EAAA,CAAAkE,gBAAA,wBAAA6b,6EAAA3b,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAAre,EAAA,CAAAsE,kBAAA,CAAA6Z,GAAA,CAAA1Q,SAAA,EAAArJ,MAAA,MAAA+Z,GAAA,CAAA1Q,SAAA,GAAArJ,MAAA;YAAA,OAAApE,EAAA,CAAAyB,WAAA,CAAA2C,MAAA;UAAA,EAAoB;UAClCpE,EAAA,CAAAiB,UAAA,wBAAA8e,6EAAA3b,MAAA;YAAApE,EAAA,CAAAmB,aAAA,CAAAkd,GAAA;YAAA,OAAAre,EAAA,CAAAyB,WAAA,CAAc0c,GAAA,CAAA3N,WAAA,CAAApM,MAAA,CAAmB;UAAA,EAAC;UAGxCpE,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAuKVH,EArKA,CAAAiC,UAAA,MAAA+d,qDAAA,gCAAAhgB,EAAA,CAAAigB,sBAAA,CAAmE,MAAAC,qDAAA,iCAAAlgB,EAAA,CAAAigB,sBAAA,CAsIF,MAAAE,qDAAA,mCAAAngB,EAAA,CAAAigB,sBAAA,CA+BJ;;;UA5YvBjgB,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAC,kBAAA,CAA4C;UAE1CvC,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAArX,oBAAA,CAAuB;UAS1C9G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAA6D,kBAAA,CAA4C;UACzBnG,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAApX,gBAAA,CAAmB;UAYY/G,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAA0N,KAAA,CAA+B;UAKvChQ,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAA2N,GAAA,CAA6B;UAuBtDjQ,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAoN,kBAAA,CAA4C;UAC1C1P,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAnQ,gBAAA,CAAmB;UAYnBhO,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAkC,kBAAA,CAA4C;UAC5CxE,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAvZ,gBAAA,CAAmB;UAYrB5E,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAA0C,iBAAA,CAA2C;UACzChF,EAAA,CAAAM,SAAA,EAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAjZ,eAAA,CAAkB;UAWpBlF,EAAA,CAAAM,SAAA,GAA0C;UAA1CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAyN,gBAAA,CAA0C;UACxC/P,EAAA,CAAAM,SAAA,EAAmB;UAAnBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAArQ,gBAAA,CAAmB;UAYnB9N,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAuN,mBAAA,CAA6C;UAC7C7P,EAAA,CAAAM,SAAA,EAAoB;UAApBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAlQ,iBAAA,CAAoB;UAYnBjO,EAAA,CAAAM,SAAA,GAAkD;UAAlDN,EAAA,CAAA0E,gBAAA,YAAAyZ,GAAA,CAAA7b,WAAA,CAAAwN,wBAAA,CAAkD;UACnD9P,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAjQ,sBAAA,CAAyB;UAsBblO,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,SAAA+d,GAAA,CAAArW,QAAA,CAAc;UAsCnC9H,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA+d,GAAA,CAAAjL,SAAA,CAAe;UAgD1BlT,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA0E,gBAAA,SAAAyZ,GAAA,CAAA1Q,SAAA,CAAoB;UAAuBzN,EAAtB,CAAAI,UAAA,aAAA+d,GAAA,CAAA3Q,QAAA,CAAqB,mBAAA2Q,GAAA,CAAAzQ,YAAA,CAAgC;;;qBD/KlF3O,YAAY,EAAAqhB,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAExhB,YAAY,EAAAyhB,GAAA,CAAAC,oBAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,kBAAA,EAAAJ,GAAA,CAAAK,YAAA,EAAAL,GAAA,CAAAM,YAAA,EAAAN,GAAA,CAAAO,OAAA,EAAAnE,EAAA,CAAAoE,eAAA,EAAApE,EAAA,CAAAqE,mBAAA,EAAArE,EAAA,CAAAsE,qBAAA,EAAAtE,EAAA,CAAAuE,qBAAA,EAAAvE,EAAA,CAAAwE,mBAAA,EAAAxE,EAAA,CAAAyE,gBAAA,EAAAzE,EAAA,CAAA0E,iBAAA,EAAA1E,EAAA,CAAA2E,iBAAA,EAAA3E,EAAA,CAAA4E,oBAAA,EAAA5E,EAAA,CAAA6E,iBAAA,EAAA7E,EAAA,CAAA8E,eAAA,EAAA9E,EAAA,CAAA+E,qBAAA,EAAA/E,EAAA,CAAAgF,qBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAEhjB,kBAAkB,EAAEI,mBAAmB;MAAA6iB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}