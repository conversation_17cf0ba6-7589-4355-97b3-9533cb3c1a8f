{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/services/message.service\";\nimport * as i3 from \"src/services/api/services/File.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"fileInput\"];\nfunction FileUploadComponent_h3_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h3\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.config.helpText);\n  }\n}\nfunction FileUploadComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_12_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.clearFile());\n    });\n    i0.ɵɵelement(4, \"i\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"span\", 16)(2, \"a\", 17);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_13_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openFile(ctx_r1.currentFileUrl));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentFileUrl, \" \");\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"img\", 31);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r7));\n    })(\"error\", function FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r7));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵelement(3, \"i\", 33);\n    i0.ɵɵtext(4, \"\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r7), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r7.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"span\", 36);\n    i0.ɵɵtext(3, \"PDF\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u64CA\\u958B\\u555F: \" + file_r7.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const file_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick(file_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3, \"CAD\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u64CA\\u4E0B\\u8F09: \" + file_r7.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_14_div_1_div_1_div_2_Template, 5, 2, \"div\", 24)(3, FileUploadComponent_div_14_div_1_div_1_div_3_Template, 4, 1, \"div\", 25)(4, FileUploadComponent_div_14_div_1_div_1_div_4_Template, 4, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 28);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_1_div_1_Template_span_click_7_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r10));\n    });\n    i0.ɵɵelement(8, \"i\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImage(file_r7.CFileType) || ctx_r1.isImageFile(file_r7.CFileName) && !ctx_r1.isPdf(file_r7.CFileType) && !ctx_r1.isCad(file_r7.CFileType));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPdf(file_r7.CFileType) || ctx_r1.isPDFByName(file_r7.CFileName));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isCad(file_r7.CFileType) || ctx_r1.isCadByName(file_r7.CFileName));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r7.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r7.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_1_div_1_Template, 9, 7, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"img\", 31);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r12.CFileName,\n        CFile: file_r12.CFile,\n        data: ctx_r1.getImageSrc(file_r12),\n        relativePath: file_r12.relativePath,\n        fileName: file_r12.fileName\n      }));\n    })(\"error\", function FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onImageError($event, file_r12));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 32);\n    i0.ɵɵelement(3, \"i\", 33);\n    i0.ɵɵtext(4, \"\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageSrc(file_r12), i0.ɵɵsanitizeUrl)(\"title\", \"\\u9EDE\\u64CA\\u653E\\u5927\\u9810\\u89BD: \" + file_r12.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_2_div_1_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r12.CFileName,\n        CFile: file_r12.CFile,\n        data: file_r12.CFile,\n        relativePath: file_r12.relativePath,\n        fileName: file_r12.fileName\n      }));\n    });\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"span\", 36);\n    i0.ɵɵtext(3, \"PDF\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u64CA\\u4E0B\\u8F09: \" + file_r12.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_14_div_2_div_1_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const file_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.handleFileClick({\n        CFileName: file_r12.CFileName,\n        CFile: file_r12.CFile,\n        data: file_r12.CFile,\n        relativePath: file_r12.relativePath,\n        fileName: file_r12.fileName\n      }));\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementStart(2, \"span\", 39);\n    i0.ɵɵtext(3, \"CAD\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u64CA\\u4E0B\\u8F09: \" + file_r12.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_14_div_2_div_1_div_2_Template, 5, 2, \"div\", 24)(3, FileUploadComponent_div_14_div_2_div_1_div_3_Template, 4, 1, \"div\", 25)(4, FileUploadComponent_div_14_div_2_div_1_div_4_Template, 4, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 27);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isImageFile(file_r12.CFileName || file_r12.CFile) && !ctx_r1.isPDFString(file_r12.CFileName || file_r12.CFile) && !ctx_r1.isCadString(file_r12.CFileName || file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPDFString(file_r12.CFileName || file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isCadString(file_r12.CFileName || file_r12.CFile));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"title\", file_r12.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(file_r12.CFileName);\n  }\n}\nfunction FileUploadComponent_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_2_div_1_Template, 7, 5, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.existingFiles);\n  }\n}\nfunction FileUploadComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, FileUploadComponent_div_14_div_1_Template, 2, 1, \"div\", 19)(2, FileUploadComponent_div_14_div_2_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileList && ctx_r1.fileList.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.existingFiles && ctx_r1.existingFiles.length > 0);\n  }\n}\nfunction FileUploadComponent_div_15_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"span\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_div_15_div_2_Template_button_click_3_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r16));\n    });\n    i0.ɵɵelement(4, \"i\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r17.CFileName);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"hidden\", ctx_r1.config.disabled);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.config.disabled);\n  }\n}\nfunction FileUploadComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 40);\n    i0.ɵɵtemplate(2, FileUploadComponent_div_15_div_2_Template, 5, 4, \"div\", 41);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.fileList);\n  }\n}\nexport class FileUploadComponent extends BaseComponent {\n  constructor(_allow, message, fileService) {\n    super(_allow);\n    this._allow = _allow;\n    this.message = message;\n    this.fileService = fileService;\n    this.config = {\n      acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n      acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n      acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n      label: '上傳檔案',\n      helpText: '*請上傳PDF格式',\n      required: false,\n      disabled: false,\n      autoFillName: false,\n      buttonText: '上傳',\n      buttonIcon: 'fa-solid fa-cloud-arrow-up',\n      maxFileSize: 10,\n      multiple: false,\n      showPreview: false\n    };\n    this.currentFileName = null;\n    this.currentFileUrl = null;\n    this.labelMinWidth = '172px';\n    this.fileList = []; // 用於多檔案模式的檔案列表\n    this.existingFiles = []; // 已存在的檔案列表（編輯模式用）\n    this.fileSelected = new EventEmitter();\n    this.fileCleared = new EventEmitter();\n    this.nameAutoFilled = new EventEmitter();\n    this.multiFileSelected = new EventEmitter(); // 多檔案選擇事件\n    this.fileRemoved = new EventEmitter(); // 檔案移除事件\n    this.fileName = null;\n  }\n  ngOnInit() {\n    this.fileName = this.currentFileName; // 設置預設配置\n    this.config = {\n      ...{\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\n        label: '上傳檔案',\n        helpText: '*請上傳PDF格式',\n        required: false,\n        disabled: false,\n        autoFillName: false,\n        buttonText: '上傳',\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\n        maxFileSize: 10,\n        multiple: false,\n        showPreview: false\n      },\n      ...this.config\n    };\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (!files || files.length === 0) {\n      return;\n    }\n    if (this.config.multiple) {\n      this.handleMultipleFiles(files);\n    } else {\n      this.handleSingleFile(files[0]);\n    }\n  }\n  handleSingleFile(file) {\n    // 檔案格式檢查\n    if (!this.config.acceptedFileRegex.test(file.name)) {\n      const acceptedFormats = this.getAcceptedFormatsText();\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\n      return;\n    }\n    // 檔案大小檢查\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    if (file.size > maxSizeInBytes) {\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\n      return;\n    }\n    this.fileName = file.name;\n    // 自動填入名稱功能\n    if (this.config.autoFillName) {\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const reader = new FileReader();\n    reader.onload = e => {\n      // 判斷檔案類型\n      let fileType;\n      if (file.type.startsWith('image/')) {\n        fileType = EnumFileType.JPG; // 圖片\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\n        fileType = 3; // CAD檔案\n      } else {\n        fileType = EnumFileType.PDF; // PDF\n      }\n      const result = {\n        CName: file.name,\n        CFile: e.target?.result?.toString().split(',')[1],\n        Cimg: file.name.includes('pdf') ? file : file,\n        CFileUpload: file,\n        CFileType: fileType,\n        fileName: file.name\n      };\n      this.fileSelected.emit(result);\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null;\n      }\n    };\n    reader.readAsDataURL(file);\n  }\n  handleMultipleFiles(files) {\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\n    // 如果是第一次選擇檔案且支援自動填入名稱\n    if (this.config.autoFillName && files.length > 0) {\n      const firstFile = files[0];\n      const fileName = firstFile.name;\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\n    }\n    const newFiles = [];\n    let processedCount = 0;\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      // 檔案格式檢查\n      if (!fileRegex.test(file.name)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n        processedCount++;\n        continue;\n      }\n      // 檔案大小檢查\n      if (file.size > maxSizeInBytes) {\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\n        processedCount++;\n        continue;\n      }\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n        const reader = new FileReader();\n        reader.onload = e => {\n          // 判斷檔案類型\n          let fileType = 1; // 預設為其他\n          const fileName = file.name.toLowerCase();\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n            fileType = 2; // 圖片\n          } else if (fileName.endsWith('.pdf')) {\n            fileType = 1; // PDF\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n            fileType = 3; // CAD\n          }\n          newFiles.push({\n            data: e.target.result,\n            CFileBlood: this.removeBase64Prefix(e.target.result),\n            CFileName: file.name,\n            CFileType: fileType\n          });\n          processedCount++;\n          if (processedCount === files.length) {\n            this.fileList = [...this.fileList, ...newFiles];\n            this.multiFileSelected.emit(this.fileList);\n            if (this.fileInput) {\n              this.fileInput.nativeElement.value = null;\n            }\n          }\n        };\n        reader.readAsDataURL(file);\n      } else {\n        processedCount++;\n      }\n    }\n  }\n  clearFile() {\n    this.fileName = null;\n    this.fileCleared.emit();\n    if (this.fileInput) {\n      this.fileInput.nativeElement.value = null;\n    }\n  }\n  openFile(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  removeFile(index) {\n    this.fileList.splice(index, 1);\n    this.fileRemoved.emit(index);\n    this.multiFileSelected.emit(this.fileList);\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(fileType) {\n    return fileType === 1;\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  // 處理檔案點擊事件\n  handleFileClick(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    const displayName = file.CFileName || fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(displayName);\n    const isPdfByName = this.isPDFString(displayName);\n    const isCadByName = this.isCadString(displayName);\n    // 統一使用 GetFile API 取得檔案\n    // 優先使用 relativePath 和 fileName，如果不存在則嘗試從 CFile 中取得路徑\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    if (relativePath && serverFileName) {\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      // 如果沒有路徑資訊，使用本地檔案處理邏輯作為後備方案\n      console.warn('檔案缺少路徑資訊，使用本地處理:', file);\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\n    }\n  }\n  // 處理本地檔案的後備方法\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileUrl = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    if (isImage) {\n      const imageUrl = this.getImageSrc(file);\n      this.openImagePreview(imageUrl, fileName);\n    } else if (isPdf) {\n      this.openPdfInBrowser(fileUrl);\n    } else {\n      this.downloadFileDirectly(fileUrl, fileName);\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          // 圖片預覽\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          // PDF 檔案另開視窗顯示\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          // 其他檔案直接下載\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.location.href = blobUrl;\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 下載 base64 檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 從檔案名稱判斷 MIME 類型\n      let mimeType = 'application/octet-stream';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          mimeType = 'application/pdf';\n          break;\n        case 'jpg':\n        case 'jpeg':\n          mimeType = 'image/jpeg';\n          break;\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'dwg':\n          mimeType = 'application/acad';\n          break;\n        case 'dxf':\n          mimeType = 'application/dxf';\n          break;\n      }\n      // 將 base64 轉換為 Blob\n      const byteCharacters = atob(base64Content);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: mimeType\n      });\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('處理 base64 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('處理檔案時發生錯誤');\n    }\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 判斷 URL 是否為圖片（根據 base64 data URL）\n  isImageUrl(url) {\n    return url.startsWith('data:image/');\n  } // 取得正確的圖片 src，處理 base64 字串\n  getImageSrc(file) {\n    // 支援多種檔案物件格式\n    const fileData = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    console.log('getImageSrc - 處理檔案:', fileName, 'fileData 長度:', fileData?.length, 'fileData 開頭:', fileData?.substring(0, 50));\n    if (!fileData) {\n      return '';\n    }\n    // 如果已經是完整的 data URL，直接返回\n    if (fileData.startsWith('data:')) {\n      console.log('getImageSrc - 已是完整 data URL');\n      return fileData;\n    }\n    // 如果是 HTTP URL，直接返回\n    if (fileData.startsWith('http')) {\n      console.log('getImageSrc - 是 HTTP URL');\n      return fileData;\n    }\n    // 如果是純 base64 字串，需要添加前綴\n    if (fileData && !fileData.startsWith('data:')) {\n      const extension = fileName.split('.').pop()?.toLowerCase() || 'jpeg';\n      // 根據檔案副檔名決定 MIME 類型\n      let mimeType = 'image/jpeg';\n      switch (extension) {\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'gif':\n          mimeType = 'image/gif';\n          break;\n        case 'bmp':\n          mimeType = 'image/bmp';\n          break;\n        case 'webp':\n          mimeType = 'image/webp';\n          break;\n        case 'jpg':\n        case 'jpeg':\n        default:\n          mimeType = 'image/jpeg';\n          break;\n      }\n      const result = `data:${mimeType};base64,${fileData}`;\n      console.log('getImageSrc - 轉換為 data URL:', fileName, 'mimeType:', mimeType, 'result 長度:', result.length);\n      return result;\n    }\n    return fileData;\n  }\n  // HTML 模板中使用的方法\n  isImageByName(fileName) {\n    return this.isImageFile(fileName);\n  }\n  isPDFByName(fileName) {\n    return this.isPDFString(fileName);\n  }\n  isCadByName(fileName) {\n    return this.isCadString(fileName);\n  }\n  // 圖片載入錯誤處理\n  onImageError(event, file) {\n    const fileName = file.CFileName || file.fileName || '檔案';\n    console.warn('圖片載入失敗:', fileName);\n    // 可以設置預設圖片或其他處理\n    event.target.style.display = 'none';\n    // 在圖片載入失敗時，顯示檔案類型圖示\n    const container = event.target.parentElement;\n    if (container) {\n      container.innerHTML = `\n        <div class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-gray-50 rounded-lg\">\n          <i class=\"fa fa-file text-gray-500 text-2xl mb-1\"></i>\n          <span class=\"text-gray-600 text-xs font-medium\">檔案</span>\n        </div>\n      `;\n    }\n  }\n  getAcceptedFormatsText() {\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\n      if (type.includes('png')) return 'PNG';\n      if (type.includes('.dwg')) return 'DWG';\n      if (type.includes('.dxf')) return 'DXF';\n      return type;\n    }).join('、');\n    return `僅限${extensions}格式`;\n  }\n  // 在瀏覽器中打開 PDF  // 在瀏覽器中打開 PDF（用於本地檔案的後備方案）\n  openPdfInBrowser(fileUrl) {\n    try {\n      const fullUrl = fileUrl.startsWith('http') ? fileUrl : `${environment.BASE_WITHOUT_FILEROOT}${fileUrl}`;\n      // 如果是 base64 PDF，使用新視窗方式顯示\n      if (fileUrl.startsWith('data:application/pdf')) {\n        this.openPdfInNewWindow(fileUrl, 'PDF 檔案');\n      } else {\n        // 其他情況直接開啟新分頁\n        window.open(fullUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('打開 PDF 時發生錯誤:', error);\n      this.message.showErrorMSG('打開 PDF 失敗');\n    }\n  }\n  // 直接下載檔案（用於 CAD 和其他類型檔案）\n  downloadFileDirectly(fileUrl, fileName) {\n    try {\n      // 如果是 base64 字串，需要轉換為可下載的檔案\n      if (fileUrl.startsWith('data:') || !fileUrl.startsWith('http')) {\n        this.downloadBase64File(fileUrl, fileName);\n      } else {\n        // 如果是一般 URL，添加 BASE_WITHOUT_FILEROOT 並強制下載\n        const fullUrl = fileUrl.startsWith('http') ? fileUrl : `${environment.BASE_WITHOUT_FILEROOT}${fileUrl}`;\n        const link = document.createElement('a');\n        link.href = fullUrl;\n        link.download = fileName;\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      }\n    } catch (error) {\n      console.error('下載檔案時發生錯誤:', error);\n      this.message.showErrorMSG('下載檔案失敗');\n    }\n  }\n  // 開啟圖片預覽\n  openImagePreview(imageUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background-color: #f5f5f5;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100vh;\n                  object-fit: contain;\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n                  background-color: white;\n                }\n                .title {\n                  position: fixed;\n                  top: 10px;\n                  left: 20px;\n                  background: rgba(0, 0, 0, 0.7);\n                  color: white;\n                  padding: 10px;\n                  border-radius: 4px;\n                  font-family: Arial, sans-serif;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"title\">${fileName}</div>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      window.open(imageUrl, '_blank');\n    }\n  }\n  openNewTab(url) {\n    if (url) {\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\n      window.open(fullUrl, '_blank');\n    }\n  }\n  static {\n    this.ɵfac = function FileUploadComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileUploadComponent,\n      selectors: [[\"app-file-upload\"]],\n      viewQuery: function FileUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      inputs: {\n        config: \"config\",\n        currentFileName: \"currentFileName\",\n        currentFileUrl: \"currentFileUrl\",\n        labelMinWidth: \"labelMinWidth\",\n        fileList: \"fileList\",\n        existingFiles: \"existingFiles\"\n      },\n      outputs: {\n        fileSelected: \"fileSelected\",\n        fileCleared: \"fileCleared\",\n        nameAutoFilled: \"nameAutoFilled\",\n        multiFileSelected: \"multiFileSelected\",\n        fileRemoved: \"fileRemoved\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 20,\n      consts: [[\"fileInput\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", 1, \"label\", \"col-3\"], [\"style\", \"color:red;\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"accept\", \"multiple\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [\"class\", \"flex items-center space-x-2 mt-2\", 4, \"ngIf\"], [\"class\", \"w-full mt-2\", 4, \"ngIf\"], [2, \"color\", \"red\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mt-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\", \"disabled\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [1, \"w-full\", \"mt-2\"], [\"class\", \"flex flex-wrap mt-2 file-type-container\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"mt-2\", \"file-type-container\"], [\"class\", \"relative w-28 h-28 mr-3 mb-6 file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"relative\", \"w-28\", \"h-28\", \"mr-3\", \"mb-6\", \"file-item\"], [1, \"w-full\", \"h-full\", \"border\", \"border-gray-300\", \"rounded-lg\", \"shadow-sm\", \"bg-white\", \"hover:shadow-md\", \"transition-shadow\", \"duration-200\"], [\"class\", \"w-full h-full relative rounded-lg overflow-hidden\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\", 3, \"title\", \"click\", 4, \"ngIf\"], [\"class\", \"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\", 3, \"title\", \"click\", 4, \"ngIf\"], [1, \"absolute\", \"-bottom-6\", \"left-0\", \"w-full\", \"text-xs\", \"truncate\", \"px-1\", \"text-center\", \"text-gray-600\", 3, \"title\"], [\"title\", \"\\u79FB\\u9664\\u6A94\\u6848\", 1, \"absolute\", \"-top-2\", \"-right-2\", \"cursor-pointer\", \"bg-red-500\", \"text-white\", \"rounded-full\", \"w-6\", \"h-6\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-red-600\", \"transition-colors\", \"duration-200\", \"remove-btn\", 3, \"click\"], [1, \"fa\", \"fa-times\", \"text-xs\"], [1, \"w-full\", \"h-full\", \"relative\", \"rounded-lg\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"cursor-pointer\", 3, \"click\", \"error\", \"src\", \"title\"], [1, \"absolute\", \"top-1\", \"left-1\", \"bg-blue-500\", \"text-white\", \"text-xs\", \"px-1\", \"py-0.5\", \"rounded\", \"file-type-label\"], [1, \"fa\", \"fa-image\", \"mr-1\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-red-50\", \"rounded-lg\", 3, \"click\", \"title\"], [1, \"fa\", \"fa-file-pdf-o\", \"text-red-500\", \"text-2xl\", \"mb-1\", \"pdf-icon\"], [1, \"text-red-600\", \"text-xs\", \"font-medium\"], [1, \"w-full\", \"h-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"cursor-pointer\", \"bg-green-50\", \"rounded-lg\", 3, \"click\", \"title\"], [1, \"fa\", \"fa-cube\", \"text-green-600\", \"text-2xl\", \"mb-1\", \"cad-icon\"], [1, \"text-green-700\", \"text-xs\", \"font-medium\"], [1, \"space-y-2\"], [\"class\", \"flex items-center justify-between bg-gray-50 p-2 rounded\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"bg-gray-50\", \"p-2\", \"rounded\"], [1, \"text-sm\", \"text-gray-700\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", \"text-sm\", 3, \"click\", \"disabled\"], [1, \"fa\", \"fa-trash\"]],\n      template: function FileUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"label\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, FileUploadComponent_h3_5_Template, 2, 1, \"h3\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"input\", 7, 0);\n          i0.ɵɵlistener(\"change\", function FileUploadComponent_Template_input_change_7_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"label\", 8);\n          i0.ɵɵelement(10, \"i\");\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, FileUploadComponent_div_12_Template, 5, 2, \"div\", 9)(13, FileUploadComponent_div_13_Template, 4, 1, \"div\", 9)(14, FileUploadComponent_div_14_Template, 3, 2, \"div\", 10)(15, FileUploadComponent_div_15_Template, 3, 1, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"min-width\", ctx.labelMinWidth);\n          i0.ɵɵclassProp(\"required-field\", ctx.config.required);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.label, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.helpText);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"accept\", ctx.config.acceptAttribute)(\"multiple\", ctx.config.multiple)(\"disabled\", ctx.config.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"opacity-50\", ctx.config.disabled)(\"cursor-pointer\", !ctx.config.disabled);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.config.buttonIcon + \" mr-2\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.config.buttonText, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.fileName && !ctx.config.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentFileUrl && !ctx.fileName && !ctx.config.multiple);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && ctx.config.showPreview);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.config.multiple && !ctx.config.showPreview && ctx.fileList && ctx.fileList.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf],\n      styles: [\".file-upload-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n}\\n\\n.file-upload-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n}\\n\\n.file-upload-label-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  margin-right: 12px;\\n}\\n\\n.file-upload-label[_ngcontent-%COMP%] {\\n  min-width: 172px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: red;\\n}\\n\\n.file-upload-help-text[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 14px;\\n  margin-top: 4px;\\n}\\n\\n.file-upload-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  flex: 1;\\n  padding: 0;\\n  align-items: flex-start;\\n}\\n\\n.file-input-hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.file-upload-button[_ngcontent-%COMP%] {\\n  background-color: #3b82f6;\\n  color: white;\\n  font-weight: bold;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  border: none;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.file-upload-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #1d4ed8;\\n}\\n\\n.file-upload-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.file-display-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 14px;\\n}\\n\\n.file-delete-button[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  padding: 4px;\\n  transition: color 0.2s;\\n}\\n\\n.file-delete-button[_ngcontent-%COMP%]:hover {\\n  color: #dc2626;\\n}\\n\\n.current-file-link[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  cursor: pointer;\\n  font-size: 14px;\\n  text-decoration: underline;\\n}\\n\\n.current-file-link[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n}\\n\\n.hidden[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.upload-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "BaseComponent", "EnumFileType", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "config", "helpText", "ɵɵlistener", "FileUploadComponent_div_12_Template_button_click_3_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "clearFile", "ɵɵelement", "fileName", "ɵɵproperty", "disabled", "FileUploadComponent_div_13_Template_a_click_2_listener", "_r4", "openFile", "currentFileUrl", "ɵɵtextInterpolate1", "FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_click_1_listener", "_r6", "file_r7", "$implicit", "handleFileClick", "FileUploadComponent_div_14_div_1_div_1_div_2_Template_img_error_1_listener", "$event", "onImageError", "getImageSrc", "ɵɵsanitizeUrl", "CFileName", "FileUploadComponent_div_14_div_1_div_1_div_3_Template_div_click_0_listener", "_r8", "FileUploadComponent_div_14_div_1_div_1_div_4_Template_div_click_0_listener", "_r9", "ɵɵtemplate", "FileUploadComponent_div_14_div_1_div_1_div_2_Template", "FileUploadComponent_div_14_div_1_div_1_div_3_Template", "FileUploadComponent_div_14_div_1_div_1_div_4_Template", "FileUploadComponent_div_14_div_1_div_1_Template_span_click_7_listener", "i_r10", "_r5", "index", "removeFile", "isImage", "CFileType", "isImageFile", "isPdf", "isCad", "isPDFByName", "isCadByName", "ɵɵclassProp", "FileUploadComponent_div_14_div_1_div_1_Template", "fileList", "FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_click_1_listener", "_r11", "file_r12", "CFile", "data", "relativePath", "FileUploadComponent_div_14_div_2_div_1_div_2_Template_img_error_1_listener", "FileUploadComponent_div_14_div_2_div_1_div_3_Template_div_click_0_listener", "_r13", "FileUploadComponent_div_14_div_2_div_1_div_4_Template_div_click_0_listener", "_r14", "FileUploadComponent_div_14_div_2_div_1_div_2_Template", "FileUploadComponent_div_14_div_2_div_1_div_3_Template", "FileUploadComponent_div_14_div_2_div_1_div_4_Template", "isPDFString", "isCadString", "FileUploadComponent_div_14_div_2_div_1_Template", "existingFiles", "FileUploadComponent_div_14_div_1_Template", "FileUploadComponent_div_14_div_2_Template", "length", "FileUploadComponent_div_15_div_2_Template_button_click_3_listener", "i_r16", "_r15", "file_r17", "FileUploadComponent_div_15_div_2_Template", "FileUploadComponent", "constructor", "_allow", "message", "fileService", "acceptedTypes", "acceptedFileRegex", "acceptAttribute", "label", "required", "autoFillName", "buttonText", "buttonIcon", "maxFileSize", "multiple", "showPreview", "currentFileName", "labelMinWidth", "fileSelected", "fileCleared", "nameAutoFilled", "multiFileSelected", "fileRemoved", "ngOnInit", "onFileSelected", "event", "files", "target", "handleMultipleFiles", "handleSingleFile", "file", "test", "name", "acceptedFormats", "getAcceptedFormatsText", "showErrorMSG", "maxSizeInBytes", "size", "fileNameWithoutExtension", "substring", "lastIndexOf", "emit", "reader", "FileReader", "onload", "e", "fileType", "type", "startsWith", "JPG", "toLowerCase", "includes", "PDF", "result", "CName", "toString", "split", "Cimg", "CFileUpload", "fileInput", "nativeElement", "value", "readAsDataURL", "allowedTypes", "fileRegex", "firstFile", "newFiles", "processedCount", "i", "match", "endsWith", "push", "CFileBlood", "removeBase64Prefix", "url", "fullUrl", "BASE_WITHOUT_FILEROOT", "window", "open", "splice", "base64String", "prefixIndex", "indexOf", "str", "lowerStr", "displayName", "isImageByName", "isPdfByName", "serverFileName", "getFileFromServer", "console", "warn", "handleLocalFile", "fileUrl", "imageUrl", "openImagePreview", "openPdfInBrowser", "downloadFileDirectly", "getFile", "subscribe", "next", "blob", "URL", "createObjectURL", "openPdfInNewWindow", "downloadBlobFile", "setTimeout", "revokeObjectURL", "error", "blobUrl", "newWindow", "document", "write", "close", "location", "href", "link", "createElement", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "downloadBase64File", "base64Data", "base64Content", "mimeType", "extension", "pop", "byteCharacters", "atob", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "Blob", "imageExtensions", "isImageUrl", "fileData", "log", "style", "display", "container", "parentElement", "innerHTML", "extensions", "map", "join", "openNewTab", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "MessageService", "i3", "FileService", "selectors", "viewQuery", "FileUploadComponent_Query", "rf", "ctx", "FileUploadComponent_h3_5_Template", "FileUploadComponent_Template_input_change_7_listener", "_r1", "FileUploadComponent_div_12_Template", "FileUploadComponent_div_13_Template", "FileUploadComponent_div_14_Template", "FileUploadComponent_div_15_Template", "ɵɵstyleProp", "ɵɵclassMap", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\file-upload\\file-upload.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { environment } from 'src/environments/environment';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\nexport interface FileUploadResult {\r\n  CName: string;\r\n  CFile: string;\r\n  Cimg: File;\r\n  CFileUpload: File;\r\n  CFileType: number;\r\n  fileName: string;\r\n}\r\n\r\nexport interface MultiFileUploadResult {\r\n  data: string;\r\n  CFileBlood: string;\r\n  CFileName: string;\r\n  CFileType: number;\r\n}\r\n\r\nexport interface FileUploadConfig {\r\n  acceptedTypes?: string[];\r\n  acceptedFileRegex?: RegExp;\r\n  acceptAttribute?: string;\r\n  label?: string;\r\n  helpText?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  autoFillName?: boolean;\r\n  buttonText?: string;\r\n  buttonIcon?: string;\r\n  maxFileSize?: number; // in MB\r\n  multiple?: boolean; // 是否支援多檔案上傳\r\n  showPreview?: boolean; // 是否顯示檔案預覽\r\n}\r\n\r\n@Component({\r\n  selector: 'app-file-upload',\r\n  templateUrl: './file-upload.component.html',\r\n  styleUrls: ['./file-upload.component.css'],\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n})\r\nexport class FileUploadComponent extends BaseComponent implements OnInit {\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @Input() config: FileUploadConfig = {\r\n    acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n    acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n    acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n    label: '上傳檔案',\r\n    helpText: '*請上傳PDF格式',\r\n    required: false,\r\n    disabled: false,\r\n    autoFillName: false,\r\n    buttonText: '上傳',\r\n    buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n    maxFileSize: 10,\r\n    multiple: false,\r\n    showPreview: false\r\n  };\r\n  @Input() currentFileName: string | null = null;\r\n  @Input() currentFileUrl: string | null = null;\r\n  @Input() labelMinWidth: string = '172px';\r\n  @Input() fileList: MultiFileUploadResult[] = []; // 用於多檔案模式的檔案列表\r\n  @Input() existingFiles: any[] = []; // 已存在的檔案列表（編輯模式用）\r\n\r\n  @Output() fileSelected = new EventEmitter<FileUploadResult>();\r\n  @Output() fileCleared = new EventEmitter<void>();\r\n  @Output() nameAutoFilled = new EventEmitter<string>();\r\n  @Output() multiFileSelected = new EventEmitter<MultiFileUploadResult[]>(); // 多檔案選擇事件\r\n  @Output() fileRemoved = new EventEmitter<number>(); // 檔案移除事件\r\n\r\n  fileName: string | null = null;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private message: MessageService,\r\n    private fileService: FileService\r\n  ) {\r\n    super(_allow);\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.fileName = this.currentFileName;    // 設置預設配置\r\n    this.config = {\r\n      ...{\r\n        acceptedTypes: ['image/jpeg', 'image/jpg', 'application/pdf'],\r\n        acceptedFileRegex: /pdf|jpg|jpeg|png/i,\r\n        acceptAttribute: 'image/jpeg, image/jpg, application/pdf',\r\n        label: '上傳檔案',\r\n        helpText: '*請上傳PDF格式',\r\n        required: false,\r\n        disabled: false,\r\n        autoFillName: false,\r\n        buttonText: '上傳',\r\n        buttonIcon: 'fa-solid fa-cloud-arrow-up',\r\n        maxFileSize: 10,\r\n        multiple: false,\r\n        showPreview: false\r\n      },\r\n      ...this.config\r\n    };\r\n  }\r\n  onFileSelected(event: any) {\r\n    const files: FileList = event.target.files;\r\n\r\n    if (!files || files.length === 0) {\r\n      return;\r\n    }\r\n\r\n    if (this.config.multiple) {\r\n      this.handleMultipleFiles(files);\r\n    } else {\r\n      this.handleSingleFile(files[0]);\r\n    }\r\n  }\r\n\r\n  private handleSingleFile(file: File) {\r\n    // 檔案格式檢查\r\n    if (!this.config.acceptedFileRegex!.test(file.name)) {\r\n      const acceptedFormats = this.getAcceptedFormatsText();\r\n      this.message.showErrorMSG(`檔案格式錯誤，${acceptedFormats}`);\r\n      return;\r\n    }\r\n\r\n    // 檔案大小檢查\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n    if (file.size > maxSizeInBytes) {\r\n      this.message.showErrorMSG(`檔案大小超過限制（${this.config.maxFileSize}MB）`);\r\n      return;\r\n    }\r\n\r\n    this.fileName = file.name;\r\n\r\n    // 自動填入名稱功能\r\n    if (this.config.autoFillName) {\r\n      const fileNameWithoutExtension = file.name.substring(0, file.name.lastIndexOf('.')) || file.name;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = (e: any) => {\r\n      // 判斷檔案類型\r\n      let fileType: number;\r\n      if (file.type.startsWith('image/')) {\r\n        fileType = EnumFileType.JPG; // 圖片\r\n      } else if (file.name.toLowerCase().includes('.dwg') || file.name.toLowerCase().includes('.dxf')) {\r\n        fileType = 3; // CAD檔案\r\n      } else {\r\n        fileType = EnumFileType.PDF; // PDF\r\n      }\r\n\r\n      const result: FileUploadResult = {\r\n        CName: file.name,\r\n        CFile: e.target?.result?.toString().split(',')[1],\r\n        Cimg: file.name.includes('pdf') ? file : file,\r\n        CFileUpload: file,\r\n        CFileType: fileType,\r\n        fileName: file.name\r\n      };\r\n\r\n      this.fileSelected.emit(result);\r\n\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null;\r\n      }\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  private handleMultipleFiles(files: FileList) {\r\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n    const fileRegex = this.config.acceptedFileRegex || /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n    const maxSizeInBytes = (this.config.maxFileSize || 10) * 1024 * 1024;\r\n\r\n    // 如果是第一次選擇檔案且支援自動填入名稱\r\n    if (this.config.autoFillName && files.length > 0) {\r\n      const firstFile = files[0];\r\n      const fileName = firstFile.name;\r\n      const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n      this.nameAutoFilled.emit(fileNameWithoutExtension);\r\n    }\r\n\r\n    const newFiles: MultiFileUploadResult[] = [];\r\n    let processedCount = 0;\r\n\r\n    for (let i = 0; i < files.length; i++) {\r\n      const file = files[i];\r\n\r\n      // 檔案格式檢查\r\n      if (!fileRegex.test(file.name)) {\r\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      // 檔案大小檢查\r\n      if (file.size > maxSizeInBytes) {\r\n        this.message.showErrorMSG(`檔案 ${file.name} 大小超過限制（${this.config.maxFileSize}MB）`);\r\n        processedCount++;\r\n        continue;\r\n      }\r\n\r\n      if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          // 判斷檔案類型\r\n          let fileType = 1; // 預設為其他\r\n          const fileName = file.name.toLowerCase();\r\n\r\n          if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n            fileType = 2; // 圖片\r\n          } else if (fileName.endsWith('.pdf')) {\r\n            fileType = 1; // PDF\r\n          } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n            fileType = 3; // CAD\r\n          }\r\n\r\n          newFiles.push({\r\n            data: e.target.result,\r\n            CFileBlood: this.removeBase64Prefix(e.target.result),\r\n            CFileName: file.name,\r\n            CFileType: fileType\r\n          });\r\n\r\n          processedCount++;\r\n          if (processedCount === files.length) {\r\n            this.fileList = [...this.fileList, ...newFiles];\r\n            this.multiFileSelected.emit(this.fileList);\r\n\r\n            if (this.fileInput) {\r\n              this.fileInput.nativeElement.value = null;\r\n            }\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      } else {\r\n        processedCount++;\r\n      }\r\n    }\r\n  }\r\n  clearFile() {\r\n    this.fileName = null;\r\n    this.fileCleared.emit();\r\n    if (this.fileInput) {\r\n      this.fileInput.nativeElement.value = null;\r\n    }\r\n  }\r\n  openFile(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  removeFile(index: number) {\r\n    this.fileList.splice(index, 1);\r\n    this.fileRemoved.emit(index);\r\n    this.multiFileSelected.emit(this.fileList);\r\n  }\r\n\r\n  private removeBase64Prefix(base64String: string): string {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(fileType: number): boolean {\r\n    return fileType === 1;\r\n  }\r\n\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 處理檔案點擊事件\r\n  handleFileClick(file: any) {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const displayName = file.CFileName || fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(displayName);\r\n    const isPdfByName = this.isPDFString(displayName);\r\n    const isCadByName = this.isCadString(displayName);\r\n\r\n    // 統一使用 GetFile API 取得檔案\r\n    // 優先使用 relativePath 和 fileName，如果不存在則嘗試從 CFile 中取得路徑\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    if (relativePath && serverFileName) {\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      // 如果沒有路徑資訊，使用本地檔案處理邏輯作為後備方案\r\n      console.warn('檔案缺少路徑資訊，使用本地處理:', file);\r\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\r\n    }\r\n  }\r\n\r\n  // 處理本地檔案的後備方法\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileUrl = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (isImage) {\r\n      const imageUrl = this.getImageSrc(file);\r\n      this.openImagePreview(imageUrl, fileName);\r\n    } else if (isPdf) {\r\n      this.openPdfInBrowser(fileUrl);\r\n    } else {\r\n      this.downloadFileDirectly(fileUrl, fileName);\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n        \r\n        if (isImage) {\r\n          // 圖片預覽\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          // PDF 檔案另開視窗顯示\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          // 其他檔案直接下載\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n        \r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.location.href = blobUrl;\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    \r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n\r\n  // 下載 base64 檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\r\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 從檔案名稱判斷 MIME 類型\r\n      let mimeType = 'application/octet-stream';\r\n      const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n      switch (extension) {\r\n        case 'pdf':\r\n          mimeType = 'application/pdf';\r\n          break;\r\n        case 'jpg':\r\n        case 'jpeg':\r\n          mimeType = 'image/jpeg';\r\n          break;\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'dwg':\r\n          mimeType = 'application/acad';\r\n          break;\r\n        case 'dxf':\r\n          mimeType = 'application/dxf';\r\n          break;\r\n      }\r\n\r\n      // 將 base64 轉換為 Blob\r\n      const byteCharacters = atob(base64Content);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: mimeType });\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理 URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('處理 base64 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('處理檔案時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n  // 判斷 URL 是否為圖片（根據 base64 data URL）\r\n  private isImageUrl(url: string): boolean {\r\n    return url.startsWith('data:image/');\r\n  }  // 取得正確的圖片 src，處理 base64 字串\r\n  getImageSrc(file: any): string {\r\n    // 支援多種檔案物件格式\r\n    const fileData = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    console.log('getImageSrc - 處理檔案:', fileName, 'fileData 長度:', fileData?.length, 'fileData 開頭:', fileData?.substring(0, 50));\r\n\r\n    if (!fileData) {\r\n      return '';\r\n    }\r\n\r\n    // 如果已經是完整的 data URL，直接返回\r\n    if (fileData.startsWith('data:')) {\r\n      console.log('getImageSrc - 已是完整 data URL');\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是 HTTP URL，直接返回\r\n    if (fileData.startsWith('http')) {\r\n      console.log('getImageSrc - 是 HTTP URL');\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是純 base64 字串，需要添加前綴\r\n    if (fileData && !fileData.startsWith('data:')) {\r\n      const extension = fileName.split('.').pop()?.toLowerCase() || 'jpeg';\r\n\r\n      // 根據檔案副檔名決定 MIME 類型\r\n      let mimeType = 'image/jpeg';\r\n      switch (extension) {\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'gif':\r\n          mimeType = 'image/gif';\r\n          break;\r\n        case 'bmp':\r\n          mimeType = 'image/bmp';\r\n          break;\r\n        case 'webp':\r\n          mimeType = 'image/webp';\r\n          break;\r\n        case 'jpg':\r\n        case 'jpeg':\r\n        default:\r\n          mimeType = 'image/jpeg';\r\n          break;\r\n      }\r\n\r\n      const result = `data:${mimeType};base64,${fileData}`;\r\n      console.log('getImageSrc - 轉換為 data URL:', fileName, 'mimeType:', mimeType, 'result 長度:', result.length);\r\n      return result;\r\n    }\r\n\r\n    return fileData;\r\n  }\r\n\r\n  // HTML 模板中使用的方法\r\n  isImageByName(fileName: string): boolean {\r\n    return this.isImageFile(fileName);\r\n  }\r\n\r\n  isPDFByName(fileName: string): boolean {\r\n    return this.isPDFString(fileName);\r\n  }\r\n\r\n  isCadByName(fileName: string): boolean {\r\n    return this.isCadString(fileName);\r\n  }\r\n  // 圖片載入錯誤處理\r\n  onImageError(event: any, file: any) {\r\n    const fileName = file.CFileName || file.fileName || '檔案';\r\n    console.warn('圖片載入失敗:', fileName);\r\n    // 可以設置預設圖片或其他處理\r\n    event.target.style.display = 'none';\r\n\r\n    // 在圖片載入失敗時，顯示檔案類型圖示\r\n    const container = event.target.parentElement;\r\n    if (container) {\r\n      container.innerHTML = `\r\n        <div class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-gray-50 rounded-lg\">\r\n          <i class=\"fa fa-file text-gray-500 text-2xl mb-1\"></i>\r\n          <span class=\"text-gray-600 text-xs font-medium\">檔案</span>\r\n        </div>\r\n      `;\r\n    }\r\n  }\r\n\r\n  private getAcceptedFormatsText(): string {\r\n    const extensions = this.config.acceptAttribute?.split(',').map(type => {\r\n      if (type.includes('pdf')) return 'PDF';\r\n      if (type.includes('jpeg') || type.includes('jpg')) return 'JPG';\r\n      if (type.includes('png')) return 'PNG';\r\n      if (type.includes('.dwg')) return 'DWG';\r\n      if (type.includes('.dxf')) return 'DXF';\r\n      return type;\r\n    }).join('、');\r\n\r\n    return `僅限${extensions}格式`;\r\n  }\r\n\r\n  // 在瀏覽器中打開 PDF  // 在瀏覽器中打開 PDF（用於本地檔案的後備方案）\r\n  openPdfInBrowser(fileUrl: string) {\r\n    try {\r\n      const fullUrl = fileUrl.startsWith('http') ? fileUrl : `${environment.BASE_WITHOUT_FILEROOT}${fileUrl}`;\r\n      \r\n      // 如果是 base64 PDF，使用新視窗方式顯示\r\n      if (fileUrl.startsWith('data:application/pdf')) {\r\n        this.openPdfInNewWindow(fileUrl, 'PDF 檔案');\r\n      } else {\r\n        // 其他情況直接開啟新分頁\r\n        window.open(fullUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('打開 PDF 時發生錯誤:', error);\r\n      this.message.showErrorMSG('打開 PDF 失敗');\r\n    }\r\n  }\r\n\r\n  // 直接下載檔案（用於 CAD 和其他類型檔案）\r\n  downloadFileDirectly(fileUrl: string, fileName: string) {\r\n    try {\r\n      // 如果是 base64 字串，需要轉換為可下載的檔案\r\n      if (fileUrl.startsWith('data:') || !fileUrl.startsWith('http')) {\r\n        this.downloadBase64File(fileUrl, fileName);\r\n      } else {\r\n        // 如果是一般 URL，添加 BASE_WITHOUT_FILEROOT 並強制下載\r\n        const fullUrl = fileUrl.startsWith('http') ? fileUrl : `${environment.BASE_WITHOUT_FILEROOT}${fileUrl}`;\r\n        const link = document.createElement('a');\r\n        link.href = fullUrl;\r\n        link.download = fileName;\r\n        link.target = '_blank';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      }\r\n    } catch (error) {\r\n      console.error('下載檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('下載檔案失敗');\r\n    }\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(imageUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background-color: #f5f5f5;\r\n                  display: flex;\r\n                  justify-content: center;\r\n                  align-items: center;\r\n                  min-height: 100vh;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  max-height: 100vh;\r\n                  object-fit: contain;\r\n                  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n                  background-color: white;\r\n                }\r\n                .title {\r\n                  position: fixed;\r\n                  top: 10px;\r\n                  left: 20px;\r\n                  background: rgba(0, 0, 0, 0.7);\r\n                  color: white;\r\n                  padding: 10px;\r\n                  border-radius: 4px;\r\n                  font-family: Arial, sans-serif;\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"title\">${fileName}</div>\r\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      window.open(imageUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  openNewTab(url: string) {\r\n    if (url) {\r\n      // 如果 URL 不是完整的 http/https 連結，則添加 base URL\r\n      const fullUrl = url.startsWith('http') ? url : `${environment.BASE_WITHOUT_FILEROOT}${url}`;\r\n      window.open(fullUrl, '_blank');\r\n    }\r\n  }\r\n}\r\n", "<div class=\"form-group d-flex align-items-center\">\r\n  <div class=\"form-group d-flex align-items-center w-full\">\r\n    <div class=\"d-flex flex-col mr-3\">\r\n      <label for=\"file\" class=\"label col-3\" [class.required-field]=\"config.required\" [style.min-width]=\"labelMinWidth\">\r\n        {{ config.label }}\r\n      </label>\r\n      <h3 style=\"color:red;\" *ngIf=\"config.helpText\">{{ config.helpText }}</h3>\r\n    </div>\r\n\r\n    <div class=\"flex flex-col col-9 px-0 items-start\">\r\n      <input #fileInput type=\"file\" id=\"fileInput\" [accept]=\"config.acceptAttribute\" [multiple]=\"config.multiple\"\r\n        class=\"hidden\" style=\"display: none\" (change)=\"onFileSelected($event)\" [disabled]=\"config.disabled\">\r\n\r\n      <label for=\"fileInput\" [class.opacity-50]=\"config.disabled\" [class.cursor-pointer]=\"!config.disabled\"\r\n        class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n        <i [class]=\"config.buttonIcon + ' mr-2'\"></i> {{ config.buttonText }}\r\n      </label>\r\n\r\n      <!-- 單檔案模式：已選擇的檔案顯示 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"fileName && !config.multiple\">\r\n        <span class=\"text-gray-600\">{{ fileName }}</span>\r\n        <button type=\"button\" (click)=\"clearFile()\" class=\"text-red-500 hover:text-red-700\"\r\n          [disabled]=\"config.disabled\">\r\n          <i class=\"fa-solid fa-trash\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- 單檔案模式：已存在的檔案連結 -->\r\n      <div class=\"flex items-center space-x-2 mt-2\" *ngIf=\"currentFileUrl && !fileName && !config.multiple\">\r\n        <span class=\"text-sm\">\r\n          <a (click)=\"openFile(currentFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n            {{ currentFileUrl }}\r\n          </a>\r\n        </span>\r\n      </div>\r\n\r\n      <!-- 多檔案模式：檔案預覽和列表 -->\r\n      <div class=\"w-full mt-2\" *ngIf=\"config.multiple && config.showPreview\"> <!-- 上傳的檔案預覽 -->\r\n        <div class=\"flex flex-wrap mt-2 file-type-container\" *ngIf=\"fileList && fileList.length > 0\">\r\n          <div *ngFor=\"let file of fileList; let i = index\" class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div\r\n              class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n              <!-- 圖片類型 - 先檢查是否為圖片檔案類型 -->\r\n              <div\r\n                *ngIf=\"isImage(file.CFileType) || (isImageFile(file.CFileName) && !isPdf(file.CFileType) && !isCad(file.CFileType))\"\r\n                class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"getImageSrc(file)\"\r\n                  (click)=\"handleFileClick(file)\" [title]=\"'點擊放大預覽: ' + file.CFileName\"\r\n                  (error)=\"onImageError($event, file)\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label\">\r\n                  <i class=\"fa fa-image mr-1\"></i>圖片\r\n                </div>\r\n              </div>              <!-- PDF類型 -->\r\n              <div *ngIf=\"isPdf(file.CFileType) || isPDFByName(file.CFileName)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\"\r\n                (click)=\"handleFileClick(file)\" [title]=\"'點擊開啟: ' + file.CFileName\">\r\n                <i class=\"fa fa-file-pdf-o text-red-500 text-2xl mb-1 pdf-icon\"></i>\r\n                <span class=\"text-red-600 text-xs font-medium\">PDF</span>\r\n              </div>\r\n\r\n              <!-- CAD類型 -->\r\n              <div *ngIf=\"isCad(file.CFileType) || isCadByName(file.CFileName)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\"\r\n                (click)=\"handleFileClick(file)\" [title]=\"'點擊下載: ' + file.CFileName\">\r\n                <i class=\"fa fa-cube text-green-600 text-2xl mb-1 cad-icon\"></i>\r\n                <span class=\"text-green-700 text-xs font-medium\">CAD</span>\r\n              </div>\r\n            </div>\r\n\r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\"\r\n              [title]=\"file.CFileName\">{{ file.CFileName }}</p>\r\n            <span\r\n              class=\"absolute -top-2 -right-2 cursor-pointer bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors duration-200 remove-btn\"\r\n              (click)=\"removeFile(i)\" title=\"移除檔案\" [class.hidden]=\"config.disabled\">\r\n              <i class=\"fa fa-times text-xs\"></i>\r\n            </span>\r\n          </div>\r\n        </div> <!-- 已存在的檔案預覽（編輯模式） -->\r\n        <div class=\"flex flex-wrap mt-2 file-type-container\" *ngIf=\"existingFiles && existingFiles.length > 0\">\r\n          <div *ngFor=\"let file of existingFiles; let i = index\" class=\"relative w-28 h-28 mr-3 mb-6 file-item\">\r\n            <div\r\n              class=\"w-full h-full border border-gray-300 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200\">\r\n              <!-- 圖片類型 -->\r\n              <div\r\n                *ngIf=\"isImageFile(file.CFileName || file.CFile) && !isPDFString(file.CFileName || file.CFile) && !isCadString(file.CFileName || file.CFile)\"\r\n                class=\"w-full h-full relative rounded-lg overflow-hidden\">\r\n                <img class=\"w-full h-full object-cover cursor-pointer\" [src]=\"getImageSrc(file)\"\r\n                  (click)=\"handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: getImageSrc(file), relativePath: file.relativePath, fileName: file.fileName})\"\r\n                  [title]=\"'點擊放大預覽: ' + file.CFileName\" (error)=\"onImageError($event, file)\">\r\n                <div class=\"absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded file-type-label\">\r\n                  <i class=\"fa fa-image mr-1\"></i>圖片\r\n                </div>\r\n              </div>\r\n\r\n              <!-- PDF類型 -->\r\n              <div *ngIf=\"isPDFString(file.CFileName || file.CFile)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-red-50 rounded-lg\"\r\n                (click)=\"handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: file.CFile, relativePath: file.relativePath, fileName: file.fileName})\"\r\n                [title]=\"'點擊下載: ' + file.CFileName\">\r\n                <i class=\"fa fa-file-pdf-o text-red-500 text-2xl mb-1 pdf-icon\"></i>\r\n                <span class=\"text-red-600 text-xs font-medium\">PDF</span>\r\n              </div>\r\n\r\n              <!-- CAD類型 -->\r\n              <div *ngIf=\"isCadString(file.CFileName || file.CFile)\"\r\n                class=\"w-full h-full flex flex-col items-center justify-center cursor-pointer bg-green-50 rounded-lg\"\r\n                (click)=\"handleFileClick({CFileName: file.CFileName, CFile: file.CFile, data: file.CFile, relativePath: file.relativePath, fileName: file.fileName})\"\r\n                [title]=\"'點擊下載: ' + file.CFileName\">\r\n                <i class=\"fa fa-cube text-green-600 text-2xl mb-1 cad-icon\"></i>\r\n                <span class=\"text-green-700 text-xs font-medium\">CAD</span>\r\n              </div>\r\n            </div>\r\n\r\n            <p class=\"absolute -bottom-6 left-0 w-full text-xs truncate px-1 text-center text-gray-600\"\r\n              [title]=\"file.CFileName\">{{ file.CFileName }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 多檔案模式：簡單列表模式 -->\r\n      <div class=\"w-full mt-2\" *ngIf=\"config.multiple && !config.showPreview && fileList && fileList.length > 0\">\r\n        <div class=\"space-y-2\">\r\n          <div *ngFor=\"let file of fileList; let i = index\"\r\n            class=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\r\n            <span class=\"text-sm text-gray-700\">{{ file.CFileName }}</span>\r\n            <button type=\"button\" (click)=\"removeFile(i)\" class=\"text-red-500 hover:text-red-700 text-sm\"\r\n              [disabled]=\"config.disabled\" [class.hidden]=\"config.disabled\">\r\n              <i class=\"fa fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;AACrG,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;ICApDC,EAAA,CAAAC,cAAA,aAA+C;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,QAAA,CAAqB;;;;;;IAclER,EADF,CAAAC,cAAA,cAAmF,eACrD;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAC+B;IADTD,EAAA,CAAAS,UAAA,mBAAAC,4DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,SAAA,EAAW;IAAA,EAAC;IAEzCf,EAAA,CAAAgB,SAAA,YAAiC;IAErChB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALwBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAW,QAAA,CAAc;IAExCjB,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAkB,UAAA,aAAAZ,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAA4B;;;;;;IAQ5BnB,EAFJ,CAAAC,cAAA,cAAsG,eAC9E,YACuD;IAAxED,EAAA,CAAAS,UAAA,mBAAAW,uDAAA;MAAApB,EAAA,CAAAW,aAAA,CAAAU,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAgB,QAAA,CAAAhB,MAAA,CAAAiB,cAAA,CAAwB;IAAA,EAAC;IACnCvB,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACC,EACH;;;;IAHAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAwB,kBAAA,MAAAlB,MAAA,CAAAiB,cAAA,MACF;;;;;;IAcMvB,EAHF,CAAAC,cAAA,cAE4D,cAGnB;IAArCD,EADA,CAAAS,UAAA,mBAAAgB,2EAAA;MAAAzB,EAAA,CAAAW,aAAA,CAAAe,GAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAF,OAAA,CAAqB;IAAA,EAAC,mBAAAG,2EAAAC,MAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAe,GAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CACtBR,MAAA,CAAA0B,YAAA,CAAAD,MAAA,EAAAJ,OAAA,CAA0B;IAAA,EAAC;IAFtC3B,EAAA,CAAAG,YAAA,EAEuC;IACvCH,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAgB,SAAA,YAAgC;IAAAhB,EAAA,CAAAE,MAAA,oBAClC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IANmDH,EAAA,CAAAI,SAAA,EAAyB;IAC9CJ,EADqB,CAAAkB,UAAA,QAAAZ,MAAA,CAAA2B,WAAA,CAAAN,OAAA,GAAA3B,EAAA,CAAAkC,aAAA,CAAyB,qDAAAP,OAAA,CAAAQ,SAAA,CACT;;;;;;IAMzEnC,EAAA,CAAAC,cAAA,cAEsE;IAApED,EAAA,CAAAS,UAAA,mBAAA2B,2EAAA;MAAApC,EAAA,CAAAW,aAAA,CAAA0B,GAAA;MAAA,MAAAV,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAF,OAAA,CAAqB;IAAA,EAAC;IAC/B3B,EAAA,CAAAgB,SAAA,YAAoE;IACpEhB,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAE,MAAA,UAAG;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IAH4BH,EAAA,CAAAkB,UAAA,yCAAAS,OAAA,CAAAQ,SAAA,CAAmC;;;;;;IAMrEnC,EAAA,CAAAC,cAAA,cAEsE;IAApED,EAAA,CAAAS,UAAA,mBAAA6B,2EAAA;MAAAtC,EAAA,CAAAW,aAAA,CAAA4B,GAAA;MAAA,MAAAZ,OAAA,GAAA3B,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAAF,OAAA,CAAqB;IAAA,EAAC;IAC/B3B,EAAA,CAAAgB,SAAA,YAAgE;IAChEhB,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;;;;IAH4BH,EAAA,CAAAkB,UAAA,yCAAAS,OAAA,CAAAQ,SAAA,CAAmC;;;;;;IAvBvEnC,EADF,CAAAC,cAAA,cAAiG,cAE6B;IAoB1HD,EAlBA,CAAAwC,UAAA,IAAAC,qDAAA,kBAE4D,IAAAC,qDAAA,kBAUU,IAAAC,qDAAA,kBAQA;IAIxE3C,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,YAC2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnDH,EAAA,CAAAC,cAAA,eAEwE;IAAtED,EAAA,CAAAS,UAAA,mBAAAmC,sEAAA;MAAA,MAAAC,KAAA,GAAA7C,EAAA,CAAAW,aAAA,CAAAmC,GAAA,EAAAC,KAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA0C,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IACvB7C,EAAA,CAAAgB,SAAA,YAAmC;IAEvChB,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IAhCCH,EAAA,CAAAI,SAAA,GAAkH;IAAlHJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA2C,OAAA,CAAAtB,OAAA,CAAAuB,SAAA,KAAA5C,MAAA,CAAA6C,WAAA,CAAAxB,OAAA,CAAAQ,SAAA,MAAA7B,MAAA,CAAA8C,KAAA,CAAAzB,OAAA,CAAAuB,SAAA,MAAA5C,MAAA,CAAA+C,KAAA,CAAA1B,OAAA,CAAAuB,SAAA,EAAkH;IAS/GlD,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA8C,KAAA,CAAAzB,OAAA,CAAAuB,SAAA,KAAA5C,MAAA,CAAAgD,WAAA,CAAA3B,OAAA,CAAAQ,SAAA,EAA0D;IAQ1DnC,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA+C,KAAA,CAAA1B,OAAA,CAAAuB,SAAA,KAAA5C,MAAA,CAAAiD,WAAA,CAAA5B,OAAA,CAAAQ,SAAA,EAA0D;IAShEnC,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkB,UAAA,UAAAS,OAAA,CAAAQ,SAAA,CAAwB;IAACnC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAsB,OAAA,CAAAQ,SAAA,CAAoB;IAGRnC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAwD,WAAA,WAAAlD,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAAgC;;;;;IAnC3EnB,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAwC,UAAA,IAAAiB,+CAAA,kBAAiG;IAsCnGzD,EAAA,CAAAG,YAAA,EAAM;;;;IAtCkBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAoD,QAAA,CAAa;;;;;;IA+C7B1D,EAHF,CAAAC,cAAA,cAE4D,cAGmB;IAArCD,EADtC,CAAAS,UAAA,mBAAAkD,2EAAA;MAAA3D,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAAC,QAAA,GAAA7D,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAgB;QAAAM,SAAA,EAAA0B,QAAA,CAAA1B,SAAA;QAAA2B,KAAA,EAAAD,QAAA,CAAAC,KAAA;QAAAC,IAAA,EAAqDzD,MAAA,CAAA2B,WAAA,CAAA4B,QAAA,CAAiB;QAAAG,YAAA,EAAAH,QAAA,CAAAG,YAAA;QAAA/C,QAAA,EAAA4C,QAAA,CAAA5C;MAAA,CAA2D,CAAC;IAAA,EAAC,mBAAAgD,2EAAAlC,MAAA;MAAA/B,EAAA,CAAAW,aAAA,CAAAiD,IAAA;MAAA,MAAAC,QAAA,GAAA7D,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAC7GR,MAAA,CAAA0B,YAAA,CAAAD,MAAA,EAAA8B,QAAA,CAA0B;IAAA,EAAC;IAF5E7D,EAAA,CAAAG,YAAA,EAE6E;IAC7EH,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAgB,SAAA,YAAgC;IAAAhB,EAAA,CAAAE,MAAA,oBAClC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IANmDH,EAAA,CAAAI,SAAA,EAAyB;IAE9EJ,EAFqD,CAAAkB,UAAA,QAAAZ,MAAA,CAAA2B,WAAA,CAAA4B,QAAA,GAAA7D,EAAA,CAAAkC,aAAA,CAAyB,qDAAA2B,QAAA,CAAA1B,SAAA,CAEzC;;;;;;IAOzCnC,EAAA,CAAAC,cAAA,cAGsC;IADpCD,EAAA,CAAAS,UAAA,mBAAAyD,2EAAA;MAAAlE,EAAA,CAAAW,aAAA,CAAAwD,IAAA;MAAA,MAAAN,QAAA,GAAA7D,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAgB;QAAAM,SAAA,EAAA0B,QAAA,CAAA1B,SAAA;QAAA2B,KAAA,EAAAD,QAAA,CAAAC,KAAA;QAAAC,IAAA,EAAAF,QAAA,CAAAC,KAAA;QAAAE,YAAA,EAAAH,QAAA,CAAAG,YAAA;QAAA/C,QAAA,EAAA4C,QAAA,CAAA5C;MAAA,CAA0H,CAAC;IAAA,EAAC;IAErJjB,EAAA,CAAAgB,SAAA,YAAoE;IACpEhB,EAAA,CAAAC,cAAA,eAA+C;IAAAD,EAAA,CAAAE,MAAA,UAAG;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;IAHJH,EAAA,CAAAkB,UAAA,yCAAA2C,QAAA,CAAA1B,SAAA,CAAmC;;;;;;IAMrCnC,EAAA,CAAAC,cAAA,cAGsC;IADpCD,EAAA,CAAAS,UAAA,mBAAA2D,2EAAA;MAAApE,EAAA,CAAAW,aAAA,CAAA0D,IAAA;MAAA,MAAAR,QAAA,GAAA7D,EAAA,CAAAa,aAAA,GAAAe,SAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAuB,eAAA,CAAgB;QAAAM,SAAA,EAAA0B,QAAA,CAAA1B,SAAA;QAAA2B,KAAA,EAAAD,QAAA,CAAAC,KAAA;QAAAC,IAAA,EAAAF,QAAA,CAAAC,KAAA;QAAAE,YAAA,EAAAH,QAAA,CAAAG,YAAA;QAAA/C,QAAA,EAAA4C,QAAA,CAAA5C;MAAA,CAA0H,CAAC;IAAA,EAAC;IAErJjB,EAAA,CAAAgB,SAAA,YAAgE;IAChEhB,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAE,MAAA,UAAG;IACtDF,EADsD,CAAAG,YAAA,EAAO,EACvD;;;;IAHJH,EAAA,CAAAkB,UAAA,yCAAA2C,QAAA,CAAA1B,SAAA,CAAmC;;;;;IA3BvCnC,EADF,CAAAC,cAAA,cAAsG,cAEwB;IAuB1HD,EArBA,CAAAwC,UAAA,IAAA8B,qDAAA,kBAE4D,IAAAC,qDAAA,kBAatB,IAAAC,qDAAA,kBASA;IAIxCxE,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,YAC2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjDF,EADiD,CAAAG,YAAA,EAAI,EAC/C;;;;;IA/BCH,EAAA,CAAAI,SAAA,GAA2I;IAA3IJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA6C,WAAA,CAAAU,QAAA,CAAA1B,SAAA,IAAA0B,QAAA,CAAAC,KAAA,MAAAxD,MAAA,CAAAmE,WAAA,CAAAZ,QAAA,CAAA1B,SAAA,IAAA0B,QAAA,CAAAC,KAAA,MAAAxD,MAAA,CAAAoE,WAAA,CAAAb,QAAA,CAAA1B,SAAA,IAAA0B,QAAA,CAAAC,KAAA,EAA2I;IAWxI9D,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAmE,WAAA,CAAAZ,QAAA,CAAA1B,SAAA,IAAA0B,QAAA,CAAAC,KAAA,EAA+C;IAS/C9D,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAoE,WAAA,CAAAb,QAAA,CAAA1B,SAAA,IAAA0B,QAAA,CAAAC,KAAA,EAA+C;IAUrD9D,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkB,UAAA,UAAA2C,QAAA,CAAA1B,SAAA,CAAwB;IAACnC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAwD,QAAA,CAAA1B,SAAA,CAAoB;;;;;IApCnDnC,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAwC,UAAA,IAAAmC,+CAAA,kBAAsG;IAqCxG3E,EAAA,CAAAG,YAAA,EAAM;;;;IArCkBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAsE,aAAA,CAAkB;;;;;IA1C5C5E,EAAA,CAAAC,cAAA,cAAuE;IAyCrED,EAxCA,CAAAwC,UAAA,IAAAqC,yCAAA,kBAA6F,IAAAC,yCAAA,kBAwCU;IAuCzG9E,EAAA,CAAAG,YAAA,EAAM;;;;IA/EkDH,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAoD,QAAA,IAAApD,MAAA,CAAAoD,QAAA,CAAAqB,MAAA,KAAqC;IAwCrC/E,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAsE,aAAA,IAAAtE,MAAA,CAAAsE,aAAA,CAAAG,MAAA,KAA+C;;;;;;IA8CjG/E,EAFF,CAAAC,cAAA,cACmE,eAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,iBACgE;IAD1CD,EAAA,CAAAS,UAAA,mBAAAuE,kEAAA;MAAA,MAAAC,KAAA,GAAAjF,EAAA,CAAAW,aAAA,CAAAuE,IAAA,EAAAnC,KAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAA0C,UAAA,CAAAiC,KAAA,CAAa;IAAA,EAAC;IAE3CjF,EAAA,CAAAgB,SAAA,YAA2B;IAE/BhB,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IALgCH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA8E,QAAA,CAAAhD,SAAA,CAAoB;IAEzBnC,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAwD,WAAA,WAAAlD,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAAgC;IAA7DnB,EAAA,CAAAkB,UAAA,aAAAZ,MAAA,CAAAC,MAAA,CAAAY,QAAA,CAA4B;;;;;IALlCnB,EADF,CAAAC,cAAA,cAA2G,cAClF;IACrBD,EAAA,CAAAwC,UAAA,IAAA4C,yCAAA,kBACmE;IAQvEpF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAToBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAoD,QAAA,CAAa;;;AD1E7C,OAAM,MAAO2B,mBAAoB,SAAQxF,aAAa;EA8BpDyF,YACUC,MAAmB,EACnBC,OAAuB,EACvBC,WAAwB;IAEhC,KAAK,CAACF,MAAM,CAAC;IAJL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IA/BZ,KAAAlF,MAAM,GAAqB;MAClCmF,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAC7DC,iBAAiB,EAAE,mBAAmB;MACtCC,eAAe,EAAE,wCAAwC;MACzDC,KAAK,EAAE,MAAM;MACbrF,QAAQ,EAAE,WAAW;MACrBsF,QAAQ,EAAE,KAAK;MACf3E,QAAQ,EAAE,KAAK;MACf4E,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,4BAA4B;MACxCC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE;KACd;IACQ,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAA9E,cAAc,GAAkB,IAAI;IACpC,KAAA+E,aAAa,GAAW,OAAO;IAC/B,KAAA5C,QAAQ,GAA4B,EAAE,CAAC,CAAC;IACxC,KAAAkB,aAAa,GAAU,EAAE,CAAC,CAAC;IAE1B,KAAA2B,YAAY,GAAG,IAAI5G,YAAY,EAAoB;IACnD,KAAA6G,WAAW,GAAG,IAAI7G,YAAY,EAAQ;IACtC,KAAA8G,cAAc,GAAG,IAAI9G,YAAY,EAAU;IAC3C,KAAA+G,iBAAiB,GAAG,IAAI/G,YAAY,EAA2B,CAAC,CAAC;IACjE,KAAAgH,WAAW,GAAG,IAAIhH,YAAY,EAAU,CAAC,CAAC;IAEpD,KAAAsB,QAAQ,GAAkB,IAAI;EAO9B;EAES2F,QAAQA,CAAA;IACf,IAAI,CAAC3F,QAAQ,GAAG,IAAI,CAACoF,eAAe,CAAC,CAAI;IACzC,IAAI,CAAC9F,MAAM,GAAG;MACZ,GAAG;QACDmF,aAAa,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;QAC7DC,iBAAiB,EAAE,mBAAmB;QACtCC,eAAe,EAAE,wCAAwC;QACzDC,KAAK,EAAE,MAAM;QACbrF,QAAQ,EAAE,WAAW;QACrBsF,QAAQ,EAAE,KAAK;QACf3E,QAAQ,EAAE,KAAK;QACf4E,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,4BAA4B;QACxCC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE;OACd;MACD,GAAG,IAAI,CAAC7F;KACT;EACH;EACAsG,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAE1C,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAChC,MAAM,KAAK,CAAC,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACxE,MAAM,CAAC4F,QAAQ,EAAE;MACxB,IAAI,CAACc,mBAAmB,CAACF,KAAK,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACG,gBAAgB,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EAEQG,gBAAgBA,CAACC,IAAU;IACjC;IACA,IAAI,CAAC,IAAI,CAAC5G,MAAM,CAACoF,iBAAkB,CAACyB,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACnD,MAAMC,eAAe,GAAG,IAAI,CAACC,sBAAsB,EAAE;MACrD,IAAI,CAAC/B,OAAO,CAACgC,YAAY,CAAC,UAAUF,eAAe,EAAE,CAAC;MACtD;IACF;IAEA;IACA,MAAMG,cAAc,GAAG,CAAC,IAAI,CAAClH,MAAM,CAAC2F,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IACpE,IAAIiB,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;MAC9B,IAAI,CAACjC,OAAO,CAACgC,YAAY,CAAC,YAAY,IAAI,CAACjH,MAAM,CAAC2F,WAAW,KAAK,CAAC;MACnE;IACF;IAEA,IAAI,CAACjF,QAAQ,GAAGkG,IAAI,CAACE,IAAI;IAEzB;IACA,IAAI,IAAI,CAAC9G,MAAM,CAACwF,YAAY,EAAE;MAC5B,MAAM4B,wBAAwB,GAAGR,IAAI,CAACE,IAAI,CAACO,SAAS,CAAC,CAAC,EAAET,IAAI,CAACE,IAAI,CAACQ,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIV,IAAI,CAACE,IAAI;MAChG,IAAI,CAACZ,cAAc,CAACqB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAMI,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB;MACA,IAAIC,QAAgB;MACpB,IAAIhB,IAAI,CAACiB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAClCF,QAAQ,GAAGrI,YAAY,CAACwI,GAAG,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAInB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,IAAIrB,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC/FL,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACLA,QAAQ,GAAGrI,YAAY,CAAC2I,GAAG,CAAC,CAAC;MAC/B;MAEA,MAAMC,MAAM,GAAqB;QAC/BC,KAAK,EAAExB,IAAI,CAACE,IAAI;QAChBvD,KAAK,EAAEoE,CAAC,CAAClB,MAAM,EAAE0B,MAAM,EAAEE,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjDC,IAAI,EAAE3B,IAAI,CAACE,IAAI,CAACmB,QAAQ,CAAC,KAAK,CAAC,GAAGrB,IAAI,GAAGA,IAAI;QAC7C4B,WAAW,EAAE5B,IAAI;QACjBjE,SAAS,EAAEiF,QAAQ;QACnBlH,QAAQ,EAAEkG,IAAI,CAACE;OAChB;MAED,IAAI,CAACd,YAAY,CAACuB,IAAI,CAACY,MAAM,CAAC;MAE9B,IAAI,IAAI,CAACM,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;MAC3C;IACF,CAAC;IACDnB,MAAM,CAACoB,aAAa,CAAChC,IAAI,CAAC;EAC5B;EAEQF,mBAAmBA,CAACF,KAAe;IACzC,MAAMqC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;IAChJ,MAAMC,SAAS,GAAG,IAAI,CAAC9I,MAAM,CAACoF,iBAAiB,IAAI,2BAA2B;IAC9E,MAAM8B,cAAc,GAAG,CAAC,IAAI,CAAClH,MAAM,CAAC2F,WAAW,IAAI,EAAE,IAAI,IAAI,GAAG,IAAI;IAEpE;IACA,IAAI,IAAI,CAAC3F,MAAM,CAACwF,YAAY,IAAIgB,KAAK,CAAChC,MAAM,GAAG,CAAC,EAAE;MAChD,MAAMuE,SAAS,GAAGvC,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAM9F,QAAQ,GAAGqI,SAAS,CAACjC,IAAI;MAC/B,MAAMM,wBAAwB,GAAG1G,QAAQ,CAAC2G,SAAS,CAAC,CAAC,EAAE3G,QAAQ,CAAC4G,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI5G,QAAQ;MAC7F,IAAI,CAACwF,cAAc,CAACqB,IAAI,CAACH,wBAAwB,CAAC;IACpD;IAEA,MAAM4B,QAAQ,GAA4B,EAAE;IAC5C,IAAIC,cAAc,GAAG,CAAC;IAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,KAAK,CAAChC,MAAM,EAAE0E,CAAC,EAAE,EAAE;MACrC,MAAMtC,IAAI,GAAGJ,KAAK,CAAC0C,CAAC,CAAC;MAErB;MACA,IAAI,CAACJ,SAAS,CAACjC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC7B,OAAO,CAACgC,YAAY,CAAC,kCAAkC,CAAC;QAC7DgC,cAAc,EAAE;QAChB;MACF;MAEA;MACA,IAAIrC,IAAI,CAACO,IAAI,GAAGD,cAAc,EAAE;QAC9B,IAAI,CAACjC,OAAO,CAACgC,YAAY,CAAC,MAAML,IAAI,CAACE,IAAI,WAAW,IAAI,CAAC9G,MAAM,CAAC2F,WAAW,KAAK,CAAC;QACjFsD,cAAc,EAAE;QAChB;MACF;MAEA,IAAIJ,YAAY,CAACZ,QAAQ,CAACrB,IAAI,CAACiB,IAAI,CAAC,IAAIiB,SAAS,CAACjC,IAAI,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;QACjE,MAAMU,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB;UACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;UAClB,MAAMlH,QAAQ,GAAGkG,IAAI,CAACE,IAAI,CAACkB,WAAW,EAAE;UAExC,IAAItH,QAAQ,CAACyI,KAAK,CAAC,mBAAmB,CAAC,EAAE;YACvCvB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIlH,QAAQ,CAAC0I,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpCxB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB,CAAC,MAAM,IAAIlH,QAAQ,CAACyI,KAAK,CAAC,cAAc,CAAC,EAAE;YACzCvB,QAAQ,GAAG,CAAC,CAAC,CAAC;UAChB;UAEAoB,QAAQ,CAACK,IAAI,CAAC;YACZ7F,IAAI,EAAEmE,CAAC,CAAClB,MAAM,CAAC0B,MAAM;YACrBmB,UAAU,EAAE,IAAI,CAACC,kBAAkB,CAAC5B,CAAC,CAAClB,MAAM,CAAC0B,MAAM,CAAC;YACpDvG,SAAS,EAAEgF,IAAI,CAACE,IAAI;YACpBnE,SAAS,EAAEiF;WACZ,CAAC;UAEFqB,cAAc,EAAE;UAChB,IAAIA,cAAc,KAAKzC,KAAK,CAAChC,MAAM,EAAE;YACnC,IAAI,CAACrB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,GAAG6F,QAAQ,CAAC;YAC/C,IAAI,CAAC7C,iBAAiB,CAACoB,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC;YAE1C,IAAI,IAAI,CAACsF,SAAS,EAAE;cAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;YAC3C;UACF;QACF,CAAC;QACDnB,MAAM,CAACoB,aAAa,CAAChC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLqC,cAAc,EAAE;MAClB;IACF;EACF;EACAzI,SAASA,CAAA;IACP,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACuF,WAAW,CAACsB,IAAI,EAAE;IACvB,IAAI,IAAI,CAACkB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;IAC3C;EACF;EACA5H,QAAQA,CAACyI,GAAW;IAClB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAC1B,UAAU,CAAC,MAAM,CAAC,GAAG0B,GAAG,GAAG,GAAGhK,WAAW,CAACkK,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEAhH,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACW,QAAQ,CAAC0G,MAAM,CAACrH,KAAK,EAAE,CAAC,CAAC;IAC9B,IAAI,CAAC4D,WAAW,CAACmB,IAAI,CAAC/E,KAAK,CAAC;IAC5B,IAAI,CAAC2D,iBAAiB,CAACoB,IAAI,CAAC,IAAI,CAACpE,QAAQ,CAAC;EAC5C;EAEQoG,kBAAkBA,CAACO,YAAoB;IAC7C,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACzC,SAAS,CAAC0C,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEApH,OAAOA,CAACkF,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA9E,KAAKA,CAAC8E,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA/E,KAAKA,CAAC+E,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEA1D,WAAWA,CAAC+F,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACjC,WAAW,EAAE,CAACoB,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAjF,WAAWA,CAAC8F,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAMC,QAAQ,GAAGD,GAAG,CAACjC,WAAW,EAAE;MAClC,OAAOkC,QAAQ,CAACd,QAAQ,CAAC,MAAM,CAAC,IAAIc,QAAQ,CAACd,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EAEA;EACA9H,eAAeA,CAACsF,IAAS;IACvB,MAAMlG,QAAQ,GAAGkG,IAAI,CAAChF,SAAS,IAAIgF,IAAI,CAAClG,QAAQ,IAAI,EAAE;IACtD,MAAMyJ,WAAW,GAAGvD,IAAI,CAAChF,SAAS,IAAIlB,QAAQ;IAE9C;IACA,MAAM0J,aAAa,GAAG,IAAI,CAACxH,WAAW,CAACuH,WAAW,CAAC;IACnD,MAAME,WAAW,GAAG,IAAI,CAACnG,WAAW,CAACiG,WAAW,CAAC;IACjD,MAAMnH,WAAW,GAAG,IAAI,CAACmB,WAAW,CAACgG,WAAW,CAAC;IAEjD;IACA;IACA,MAAM1G,YAAY,GAAGmD,IAAI,CAACnD,YAAY,IAAImD,IAAI,CAACrD,KAAK;IACpD,MAAM+G,cAAc,GAAG1D,IAAI,CAAClG,QAAQ,IAAIkG,IAAI,CAAChF,SAAS;IAEtD,IAAI6B,YAAY,IAAI6G,cAAc,EAAE;MAClC,IAAI,CAACC,iBAAiB,CAAC9G,YAAY,EAAE6G,cAAc,EAAEH,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAErH,WAAW,CAAC;IAC5G,CAAC,MAAM;MACL;MACAwH,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAE7D,IAAI,CAAC;MACtC,IAAI,CAAC8D,eAAe,CAAC9D,IAAI,EAAEwD,aAAa,EAAEC,WAAW,EAAErH,WAAW,CAAC;IACrE;EACF;EAEA;EACQ0H,eAAeA,CAAC9D,IAAS,EAAElE,OAAgB,EAAEG,KAAc,EAAEC,KAAc;IACjF,MAAM6H,OAAO,GAAG/D,IAAI,CAACrD,KAAK,IAAIqD,IAAI,CAACpD,IAAI;IACvC,MAAM9C,QAAQ,GAAGkG,IAAI,CAAChF,SAAS,IAAIgF,IAAI,CAAClG,QAAQ,IAAI,EAAE;IAEtD,IAAIgC,OAAO,EAAE;MACX,MAAMkI,QAAQ,GAAG,IAAI,CAAClJ,WAAW,CAACkF,IAAI,CAAC;MACvC,IAAI,CAACiE,gBAAgB,CAACD,QAAQ,EAAElK,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAImC,KAAK,EAAE;MAChB,IAAI,CAACiI,gBAAgB,CAACH,OAAO,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACI,oBAAoB,CAACJ,OAAO,EAAEjK,QAAQ,CAAC;IAC9C;EACF;EAEA;EACQ6J,iBAAiBA,CAAC9G,YAAoB,EAAE/C,QAAgB,EAAEyJ,WAAmB,EAAEzH,OAAgB,EAAEG,KAAc,EAAEC,KAAc;IACrI,IAAI,CAACoC,WAAW,CAAC8F,OAAO,CAACvH,YAAY,EAAE/C,QAAQ,CAAC,CAACuK,SAAS,CAAC;MACzDC,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAM3B,GAAG,GAAG4B,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC;QAErC,IAAIzI,OAAO,EAAE;UACX;UACA,IAAI,CAACmI,gBAAgB,CAACrB,GAAG,EAAEW,WAAW,CAAC;QACzC,CAAC,MAAM,IAAItH,KAAK,EAAE;UAChB;UACA,IAAI,CAACyI,kBAAkB,CAAC9B,GAAG,EAAEW,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL;UACA,IAAI,CAACoB,gBAAgB,CAACJ,IAAI,EAAEhB,WAAW,CAAC;QAC1C;QAEA;QACAqB,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACjC,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACflB,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACzG,OAAO,CAACgC,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACQqE,kBAAkBA,CAACK,OAAe,EAAEjL,QAAgB;IAC1D,IAAI;MACF,MAAMkL,SAAS,GAAGjC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIgC,SAAS,EAAE;QACbA,SAAS,CAACC,QAAQ,CAACC,KAAK,CAAC;;;uBAGVpL,QAAQ;;;;;;;6BAOFiL,OAAO;;;SAG3B,CAAC;QACFC,SAAS,CAACC,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACApC,MAAM,CAACqC,QAAQ,CAACC,IAAI,GAAGN,OAAO;MAChC;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA/B,MAAM,CAACC,IAAI,CAAC+B,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQJ,gBAAgBA,CAACJ,IAAU,EAAEzK,QAAgB;IACnD,MAAM8I,GAAG,GAAG4B,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC;IACrC,MAAMe,IAAI,GAAGL,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACD,IAAI,GAAGzC,GAAG;IACf0C,IAAI,CAACE,QAAQ,GAAG1L,QAAQ;IACxBmL,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;IAC/BA,IAAI,CAACK,KAAK,EAAE;IACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;IAE/B;IACAV,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACjC,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EAEA;EACQiD,kBAAkBA,CAACC,UAAkB,EAAEhM,QAAgB;IAC7D,IAAI;MACF;MACA,MAAMiM,aAAa,GAAGD,UAAU,CAACzE,QAAQ,CAAC,GAAG,CAAC,GAAGyE,UAAU,CAACpE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGoE,UAAU;MAEtF;MACA,IAAIE,QAAQ,GAAG,0BAA0B;MACzC,MAAMC,SAAS,GAAGnM,QAAQ,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAACwE,GAAG,EAAE,EAAE9E,WAAW,EAAE;MAE1D,QAAQ6E,SAAS;QACf,KAAK,KAAK;UACRD,QAAQ,GAAG,iBAAiB;UAC5B;QACF,KAAK,KAAK;QACV,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,kBAAkB;UAC7B;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,iBAAiB;UAC5B;MACJ;MAEA;MACA,MAAMG,cAAc,GAAGC,IAAI,CAACL,aAAa,CAAC;MAC1C,MAAMM,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACvI,MAAM,CAAC;MACpD,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,cAAc,CAACvI,MAAM,EAAE0E,CAAC,EAAE,EAAE;QAC9C+D,WAAW,CAAC/D,CAAC,CAAC,GAAG6D,cAAc,CAACI,UAAU,CAACjE,CAAC,CAAC;MAC/C;MACA,MAAMkE,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7C,MAAM9B,IAAI,GAAG,IAAImC,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;QAAEvF,IAAI,EAAE+E;MAAQ,CAAE,CAAC;MAEtD;MACA,MAAMpD,GAAG,GAAG4B,GAAG,CAACC,eAAe,CAACF,IAAI,CAAC;MACrC,MAAMe,IAAI,GAAGL,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACD,IAAI,GAAGzC,GAAG;MACf0C,IAAI,CAACE,QAAQ,GAAG1L,QAAQ;MACxBmL,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;MAC/BA,IAAI,CAACK,KAAK,EAAE;MACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;MAE/B;MACAV,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACjC,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACzG,OAAO,CAACgC,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACArE,WAAWA,CAAClC,QAAgB;IAC1B,MAAM6M,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMV,SAAS,GAAGnM,QAAQ,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAACwE,GAAG,EAAE,EAAE9E,WAAW,EAAE;IAC1D,OAAOuF,eAAe,CAACtF,QAAQ,CAAC4E,SAAS,IAAI,EAAE,CAAC;EAClD;EACA;EACQW,UAAUA,CAAChE,GAAW;IAC5B,OAAOA,GAAG,CAAC1B,UAAU,CAAC,aAAa,CAAC;EACtC,CAAC,CAAE;EACHpG,WAAWA,CAACkF,IAAS;IACnB;IACA,MAAM6G,QAAQ,GAAG7G,IAAI,CAACrD,KAAK,IAAIqD,IAAI,CAACpD,IAAI;IACxC,MAAM9C,QAAQ,GAAGkG,IAAI,CAAChF,SAAS,IAAIgF,IAAI,CAAClG,QAAQ,IAAI,EAAE;IAEtD8J,OAAO,CAACkD,GAAG,CAAC,qBAAqB,EAAEhN,QAAQ,EAAE,cAAc,EAAE+M,QAAQ,EAAEjJ,MAAM,EAAE,cAAc,EAAEiJ,QAAQ,EAAEpG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAE1H,IAAI,CAACoG,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IAEA;IACA,IAAIA,QAAQ,CAAC3F,UAAU,CAAC,OAAO,CAAC,EAAE;MAChC0C,OAAO,CAACkD,GAAG,CAAC,6BAA6B,CAAC;MAC1C,OAAOD,QAAQ;IACjB;IAEA;IACA,IAAIA,QAAQ,CAAC3F,UAAU,CAAC,MAAM,CAAC,EAAE;MAC/B0C,OAAO,CAACkD,GAAG,CAAC,0BAA0B,CAAC;MACvC,OAAOD,QAAQ;IACjB;IAEA;IACA,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAAC3F,UAAU,CAAC,OAAO,CAAC,EAAE;MAC7C,MAAM+E,SAAS,GAAGnM,QAAQ,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAACwE,GAAG,EAAE,EAAE9E,WAAW,EAAE,IAAI,MAAM;MAEpE;MACA,IAAI4E,QAAQ,GAAG,YAAY;MAC3B,QAAQC,SAAS;QACf,KAAK,KAAK;UACRD,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF,KAAK,KAAK;QACV,KAAK,MAAM;QACX;UACEA,QAAQ,GAAG,YAAY;UACvB;MACJ;MAEA,MAAMzE,MAAM,GAAG,QAAQyE,QAAQ,WAAWa,QAAQ,EAAE;MACpDjD,OAAO,CAACkD,GAAG,CAAC,6BAA6B,EAAEhN,QAAQ,EAAE,WAAW,EAAEkM,QAAQ,EAAE,YAAY,EAAEzE,MAAM,CAAC3D,MAAM,CAAC;MACxG,OAAO2D,MAAM;IACf;IAEA,OAAOsF,QAAQ;EACjB;EAEA;EACArD,aAAaA,CAAC1J,QAAgB;IAC5B,OAAO,IAAI,CAACkC,WAAW,CAAClC,QAAQ,CAAC;EACnC;EAEAqC,WAAWA,CAACrC,QAAgB;IAC1B,OAAO,IAAI,CAACwD,WAAW,CAACxD,QAAQ,CAAC;EACnC;EAEAsC,WAAWA,CAACtC,QAAgB;IAC1B,OAAO,IAAI,CAACyD,WAAW,CAACzD,QAAQ,CAAC;EACnC;EACA;EACAe,YAAYA,CAAC8E,KAAU,EAAEK,IAAS;IAChC,MAAMlG,QAAQ,GAAGkG,IAAI,CAAChF,SAAS,IAAIgF,IAAI,CAAClG,QAAQ,IAAI,IAAI;IACxD8J,OAAO,CAACC,IAAI,CAAC,SAAS,EAAE/J,QAAQ,CAAC;IACjC;IACA6F,KAAK,CAACE,MAAM,CAACkH,KAAK,CAACC,OAAO,GAAG,MAAM;IAEnC;IACA,MAAMC,SAAS,GAAGtH,KAAK,CAACE,MAAM,CAACqH,aAAa;IAC5C,IAAID,SAAS,EAAE;MACbA,SAAS,CAACE,SAAS,GAAG;;;;;OAKrB;IACH;EACF;EAEQ/G,sBAAsBA,CAAA;IAC5B,MAAMgH,UAAU,GAAG,IAAI,CAAChO,MAAM,CAACqF,eAAe,EAAEiD,KAAK,CAAC,GAAG,CAAC,CAAC2F,GAAG,CAACpG,IAAI,IAAG;MACpE,IAAIA,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MAC/D,IAAIJ,IAAI,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;MACtC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;MACvC,OAAOJ,IAAI;IACb,CAAC,CAAC,CAACqG,IAAI,CAAC,GAAG,CAAC;IAEZ,OAAO,KAAKF,UAAU,IAAI;EAC5B;EAEA;EACAlD,gBAAgBA,CAACH,OAAe;IAC9B,IAAI;MACF,MAAMlB,OAAO,GAAGkB,OAAO,CAAC7C,UAAU,CAAC,MAAM,CAAC,GAAG6C,OAAO,GAAG,GAAGnL,WAAW,CAACkK,qBAAqB,GAAGiB,OAAO,EAAE;MAEvG;MACA,IAAIA,OAAO,CAAC7C,UAAU,CAAC,sBAAsB,CAAC,EAAE;QAC9C,IAAI,CAACwD,kBAAkB,CAACX,OAAO,EAAE,QAAQ,CAAC;MAC5C,CAAC,MAAM;QACL;QACAhB,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAACzG,OAAO,CAACgC,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACA8D,oBAAoBA,CAACJ,OAAe,EAAEjK,QAAgB;IACpD,IAAI;MACF;MACA,IAAIiK,OAAO,CAAC7C,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC6C,OAAO,CAAC7C,UAAU,CAAC,MAAM,CAAC,EAAE;QAC9D,IAAI,CAAC2E,kBAAkB,CAAC9B,OAAO,EAAEjK,QAAQ,CAAC;MAC5C,CAAC,MAAM;QACL;QACA,MAAM+I,OAAO,GAAGkB,OAAO,CAAC7C,UAAU,CAAC,MAAM,CAAC,GAAG6C,OAAO,GAAG,GAAGnL,WAAW,CAACkK,qBAAqB,GAAGiB,OAAO,EAAE;QACvG,MAAMuB,IAAI,GAAGL,QAAQ,CAACM,aAAa,CAAC,GAAG,CAAC;QACxCD,IAAI,CAACD,IAAI,GAAGxC,OAAO;QACnByC,IAAI,CAACE,QAAQ,GAAG1L,QAAQ;QACxBwL,IAAI,CAACzF,MAAM,GAAG,QAAQ;QACtBoF,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC;QAC/BA,IAAI,CAACK,KAAK,EAAE;QACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACN,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACzG,OAAO,CAACgC,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;EAEA;EACQ4D,gBAAgBA,CAACD,QAAgB,EAAElK,QAAgB;IACzD,IAAI;MACF,MAAMkL,SAAS,GAAGjC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIgC,SAAS,EAAE;QACbA,SAAS,CAACC,QAAQ,CAACC,KAAK,CAAC;;;uBAGVpL,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA+BIA,QAAQ;0BACjBkK,QAAQ,UAAUlK,QAAQ;;;SAG3C,CAAC;QACFkL,SAAS,CAACC,QAAQ,CAACE,KAAK,EAAE;MAC5B;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC/B,MAAM,CAACC,IAAI,CAACgB,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF;EAEAuD,UAAUA,CAAC3E,GAAW;IACpB,IAAIA,GAAG,EAAE;MACP;MACA,MAAMC,OAAO,GAAGD,GAAG,CAAC1B,UAAU,CAAC,MAAM,CAAC,GAAG0B,GAAG,GAAG,GAAGhK,WAAW,CAACkK,qBAAqB,GAAGF,GAAG,EAAE;MAC3FG,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;;;uCArnBW3E,mBAAmB,EAAArF,EAAA,CAAA2O,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7O,EAAA,CAAA2O,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/O,EAAA,CAAA2O,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnB5J,mBAAmB;MAAA6J,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UC7C1BrP,EAHN,CAAAC,cAAA,aAAkD,aACS,aACrB,eACiF;UAC/GD,EAAA,CAAAE,MAAA,GACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAwC,UAAA,IAAA+M,iCAAA,gBAA+C;UACjDvP,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,aAAkD,kBAEsD;UAA/DD,EAAA,CAAAS,UAAA,oBAAA+O,qDAAAzN,MAAA;YAAA/B,EAAA,CAAAW,aAAA,CAAA8O,GAAA;YAAA,OAAAzP,EAAA,CAAAc,WAAA,CAAUwO,GAAA,CAAAzI,cAAA,CAAA9E,MAAA,CAAsB;UAAA,EAAC;UADxE/B,EAAA,CAAAG,YAAA,EACsG;UAEtGH,EAAA,CAAAC,cAAA,eAC+E;UAC7ED,EAAA,CAAAgB,SAAA,SAA6C;UAAChB,EAAA,CAAAE,MAAA,IAChD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAwGRH,EArGA,CAAAwC,UAAA,KAAAkN,mCAAA,iBAAmF,KAAAC,mCAAA,iBASmB,KAAAC,mCAAA,kBAS/B,KAAAC,mCAAA,kBAmFoC;UAcjH7P,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;UAnI+EH,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAA8P,WAAA,cAAAR,GAAA,CAAAhJ,aAAA,CAAiC;UAA1EtG,EAAA,CAAAwD,WAAA,mBAAA8L,GAAA,CAAA/O,MAAA,CAAAuF,QAAA,CAAwC;UAC5E9F,EAAA,CAAAI,SAAA,EACF;UADEJ,EAAA,CAAAwB,kBAAA,MAAA8N,GAAA,CAAA/O,MAAA,CAAAsF,KAAA,MACF;UACwB7F,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAkB,UAAA,SAAAoO,GAAA,CAAA/O,MAAA,CAAAC,QAAA,CAAqB;UAIAR,EAAA,CAAAI,SAAA,GAAiC;UACLJ,EAD5B,CAAAkB,UAAA,WAAAoO,GAAA,CAAA/O,MAAA,CAAAqF,eAAA,CAAiC,aAAA0J,GAAA,CAAA/O,MAAA,CAAA4F,QAAA,CAA6B,aAAAmJ,GAAA,CAAA/O,MAAA,CAAAY,QAAA,CACN;UAE9EnB,EAAA,CAAAI,SAAA,GAAoC;UAACJ,EAArC,CAAAwD,WAAA,eAAA8L,GAAA,CAAA/O,MAAA,CAAAY,QAAA,CAAoC,oBAAAmO,GAAA,CAAA/O,MAAA,CAAAY,QAAA,CAA0C;UAEhGnB,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAA+P,UAAA,CAAAT,GAAA,CAAA/O,MAAA,CAAA0F,UAAA,WAAqC;UAAMjG,EAAA,CAAAI,SAAA,EAChD;UADgDJ,EAAA,CAAAwB,kBAAA,MAAA8N,GAAA,CAAA/O,MAAA,CAAAyF,UAAA,MAChD;UAG+ChG,EAAA,CAAAI,SAAA,EAAkC;UAAlCJ,EAAA,CAAAkB,UAAA,SAAAoO,GAAA,CAAArO,QAAA,KAAAqO,GAAA,CAAA/O,MAAA,CAAA4F,QAAA,CAAkC;UASlCnG,EAAA,CAAAI,SAAA,EAAqD;UAArDJ,EAAA,CAAAkB,UAAA,SAAAoO,GAAA,CAAA/N,cAAA,KAAA+N,GAAA,CAAArO,QAAA,KAAAqO,GAAA,CAAA/O,MAAA,CAAA4F,QAAA,CAAqD;UAS1EnG,EAAA,CAAAI,SAAA,EAA2C;UAA3CJ,EAAA,CAAAkB,UAAA,SAAAoO,GAAA,CAAA/O,MAAA,CAAA4F,QAAA,IAAAmJ,GAAA,CAAA/O,MAAA,CAAA6F,WAAA,CAA2C;UAmF3CpG,EAAA,CAAAI,SAAA,EAA+E;UAA/EJ,EAAA,CAAAkB,UAAA,SAAAoO,GAAA,CAAA/O,MAAA,CAAA4F,QAAA,KAAAmJ,GAAA,CAAA/O,MAAA,CAAA6F,WAAA,IAAAkJ,GAAA,CAAA5L,QAAA,IAAA4L,GAAA,CAAA5L,QAAA,CAAAqB,MAAA,KAA+E;;;qBD1EnGnF,YAAY,EAAAoQ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}