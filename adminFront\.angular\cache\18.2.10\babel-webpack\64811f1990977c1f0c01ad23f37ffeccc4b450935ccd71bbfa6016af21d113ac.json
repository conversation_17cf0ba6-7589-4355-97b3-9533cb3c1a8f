{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport { Calendar } from '@fullcalendar/core';\nimport { CustomRenderingStore } from '@fullcalendar/core/internal';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = [\"rootEl\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction TransportContainerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nfunction TransportContainerComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 2, 0);\n    i0.ɵɵelementContainer(2, 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.elClasses || \"\")(\"ngStyle\", ctx_r0.elStyle || null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c2, ctx_r0.renderProps));\n  }\n}\nconst _c3 = [\"dayHeaderContent\"];\nconst _c4 = [\"dayCellContent\"];\nconst _c5 = [\"weekNumberContent\"];\nconst _c6 = [\"nowIndicatorContent\"];\nconst _c7 = [\"eventContent\"];\nconst _c8 = [\"slotLaneContent\"];\nconst _c9 = [\"slotLabelContent\"];\nconst _c10 = [\"allDayContent\"];\nconst _c11 = [\"moreLinkContent\"];\nconst _c12 = [\"noEventsContent\"];\nconst _c13 = [\"resourceAreaHeaderContent\"];\nconst _c14 = [\"resourceGroupLabelContent\"];\nconst _c15 = [\"resourceLabelContent\"];\nconst _c16 = [\"resourceLaneContent\"];\nconst _c17 = [\"resourceGroupLaneContent\"];\nfunction FullCalendarComponent_transport_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"transport-container\", 1);\n  }\n  if (rf & 2) {\n    const customRendering_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"inPlaceOf\", customRendering_r1.containerEl)(\"reportEl\", customRendering_r1.reportNewContainerEl)(\"elTag\", customRendering_r1.elTag)(\"elClasses\", customRendering_r1.elClasses)(\"elStyle\", customRendering_r1.elStyle)(\"elAttrs\", customRendering_r1.elAttrs)(\"template\", ctx_r1.templateMap[customRendering_r1.generatorName])(\"renderProps\", customRendering_r1.renderProps);\n  }\n}\nconst OPTION_IS_DEEP = {\n  headerToolbar: true,\n  footerToolbar: true,\n  events: true,\n  eventSources: true,\n  resources: true\n};\n/*\nNOTE: keep synced with component\n*/\nconst OPTION_INPUT_NAMES = ['events', 'eventSources', 'resources'];\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n/*\nReally simple clone utility. Only copies plain arrays, objects, and Dates. Transfers everything else as-is.\nWanted to use a third-party lib, but none did exactly this.\n*/\nfunction deepCopy(input) {\n  if (Array.isArray(input)) {\n    return input.map(deepCopy);\n  } else if (input instanceof Date) {\n    return new Date(input.valueOf());\n  } else if (typeof input === 'object' && input) {\n    // non-null object\n    return mapHash(input, deepCopy);\n  } else {\n    // everything else (null, function, etc)\n    return input;\n  }\n}\nfunction mapHash(input, func) {\n  const output = {};\n  for (const key in input) {\n    if (hasOwnProperty.call(input, key)) {\n      output[key] = func(input[key], key);\n    }\n  }\n  return output;\n}\n\n/*\nForked from https://github.com/epoberezkin/fast-deep-equal (also has MIT license)\nNeeded ESM support or else Angular complains about treeshaking\n(https://github.com/fullcalendar/fullcalendar-angular/issues/421)\n*/\nfunction deepEqual(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!deepEqual(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!deepEqual(a[key], b[key])) return false;\n    }\n    return true;\n  }\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n}\nconst dummyContainer$1 = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass OffscreenFragmentComponent {\n  constructor(element) {\n    this.element = element;\n  }\n  ngAfterViewInit() {\n    if (dummyContainer$1) {\n      dummyContainer$1.appendChild(this.element.nativeElement);\n    }\n  }\n  // invoked BEFORE component removed from DOM\n  ngOnDestroy() {\n    if (dummyContainer$1) {\n      dummyContainer$1.removeChild(this.element.nativeElement);\n    }\n  }\n}\nOffscreenFragmentComponent.ɵfac = function OffscreenFragmentComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || OffscreenFragmentComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\nOffscreenFragmentComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: OffscreenFragmentComponent,\n  selectors: [[\"offscreen-fragment\"]],\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 0,\n  template: function OffscreenFragmentComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OffscreenFragmentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'offscreen-fragment',\n      template: '<ng-content></ng-content>',\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nconst dummyContainer = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass TransportContainerComponent {\n  ngAfterViewInit() {\n    const rootEl = this.rootElRef?.nativeElement; // assumed defined\n    replaceEl(rootEl, this.inPlaceOf);\n    applyElAttrs(rootEl, undefined, this.elAttrs);\n    // insurance for if Preact recreates and reroots inPlaceOf element\n    this.inPlaceOf.style.display = 'none';\n    this.reportEl(rootEl);\n  }\n  ngOnChanges(changes) {\n    const rootEl = this.rootElRef?.nativeElement;\n    // ngOnChanges is called before ngAfterViewInit (and before DOM initializes)\n    // so make sure rootEl is defined before doing anything\n    if (rootEl) {\n      // If the ContentContainer's tagName changed, it will create a new DOM element in its\n      // original place. Detect this and re-replace.\n      if (this.inPlaceOf.parentNode !== dummyContainer) {\n        replaceEl(rootEl, this.inPlaceOf);\n        applyElAttrs(rootEl, undefined, this.elAttrs);\n        this.reportEl(rootEl);\n      } else {\n        const elAttrsChange = changes['elAttrs'];\n        if (elAttrsChange) {\n          applyElAttrs(rootEl, elAttrsChange.previousValue, elAttrsChange.currentValue);\n        }\n      }\n    }\n  }\n  // invoked BEFORE component removed from DOM\n  ngOnDestroy() {\n    if (\n    // protect against Preact recreating and rerooting inPlaceOf element\n    this.inPlaceOf.parentNode === dummyContainer && dummyContainer) {\n      dummyContainer.removeChild(this.inPlaceOf);\n    }\n    this.reportEl(null);\n  }\n}\nTransportContainerComponent.ɵfac = function TransportContainerComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || TransportContainerComponent)();\n};\nTransportContainerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TransportContainerComponent,\n  selectors: [[\"transport-container\"]],\n  viewQuery: function TransportContainerComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootElRef = _t.first);\n    }\n  },\n  inputs: {\n    inPlaceOf: \"inPlaceOf\",\n    reportEl: \"reportEl\",\n    elTag: \"elTag\",\n    elClasses: \"elClasses\",\n    elStyle: \"elStyle\",\n    elAttrs: \"elAttrs\",\n    template: \"template\",\n    renderProps: \"renderProps\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 6,\n  vars: 6,\n  consts: [[\"rootEl\", \"\"], [3, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function TransportContainerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TransportContainerComponent_ng_template_0_Template, 3, 6, \"ng-template\", 1)(1, TransportContainerComponent_ng_template_1_Template, 3, 6, \"ng-template\", 1)(2, TransportContainerComponent_ng_template_2_Template, 3, 6, \"ng-template\", 1)(3, TransportContainerComponent_ng_template_3_Template, 3, 6, \"ng-template\", 1)(4, TransportContainerComponent_ng_template_4_Template, 3, 6, \"ng-template\", 1)(5, TransportContainerComponent_ng_template_5_Template, 3, 6, \"ng-template\", 1);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"div\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"span\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"a\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"tr\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"th\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.elTag == \"td\");\n    }\n  },\n  dependencies: [i1.NgIf, i1.NgClass, i1.NgStyle, i1.NgTemplateOutlet],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransportContainerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'transport-container',\n      encapsulation: ViewEncapsulation.None,\n      template: \"<ng-template [ngIf]=\\\"elTag == 'div'\\\">\\n  <div #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </div>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'span'\\\">\\n  <span #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </span>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'a'\\\">\\n  <a #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </a>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'tr'\\\">\\n  <tr #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </tr>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'th'\\\">\\n  <th #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </th>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'td'\\\">\\n  <td #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </td>\\n</ng-template>\\n\"\n    }]\n  }], null, {\n    inPlaceOf: [{\n      type: Input\n    }],\n    reportEl: [{\n      type: Input\n    }],\n    elTag: [{\n      type: Input\n    }],\n    elClasses: [{\n      type: Input\n    }],\n    elStyle: [{\n      type: Input\n    }],\n    elAttrs: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    renderProps: [{\n      type: Input\n    }],\n    rootElRef: [{\n      type: ViewChild,\n      args: ['rootEl']\n    }]\n  });\n})();\nfunction replaceEl(subject, inPlaceOf) {\n  inPlaceOf.parentNode?.insertBefore(subject, inPlaceOf.nextSibling);\n  if (dummyContainer) {\n    dummyContainer.appendChild(inPlaceOf);\n  }\n}\nfunction applyElAttrs(el, previousAttrs = {}, currentAttrs = {}) {\n  // these are called \"attributes\" but they manipulate DOM node *properties*\n  for (const attrName in previousAttrs) {\n    if (!(attrName in currentAttrs)) {\n      el[attrName] = null;\n    }\n  }\n  for (const attrName in currentAttrs) {\n    el[attrName] = currentAttrs[attrName];\n  }\n}\nclass FullCalendarComponent {\n  constructor(element, changeDetector) {\n    this.element = element;\n    this.calendar = null;\n    this.optionSnapshot = {}; // for diffing\n    this.customRenderingMap = new Map();\n    this.templateMap = {};\n    const customRenderingStore = new CustomRenderingStore();\n    customRenderingStore.subscribe(customRenderingMap => {\n      this.customRenderingMap = customRenderingMap;\n      this.customRenderingArray = undefined; // clear cache\n      changeDetector.detectChanges();\n    });\n    this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore);\n    this.templateMap = this; // alias to this\n  }\n  ngAfterViewInit() {\n    const {\n      deepChangeDetection\n    } = this;\n    const options = {\n      ...this.options,\n      ...this.buildInputOptions()\n    };\n    // initialize snapshot\n    this.optionSnapshot = mapHash(options, (optionVal, optionName) => deepChangeDetection && OPTION_IS_DEEP[optionName] ? deepCopy(optionVal) : optionVal);\n    const calendarEl = this.element.nativeElement;\n    const calendar = this.calendar = new Calendar(calendarEl, {\n      ...options,\n      ...this.buildExtraOptions()\n    });\n    // Ionic dimensions hack\n    // https://github.com/fullcalendar/fullcalendar/issues/4976\n    const ionContent = calendarEl.closest('ion-content');\n    if (ionContent && ionContent.componentOnReady) {\n      ionContent.componentOnReady().then(() => {\n        window.requestAnimationFrame(() => {\n          calendar.render();\n        });\n      });\n    } else {\n      calendar.render();\n    }\n  }\n  /*\n  allows us to manually detect complex input changes, internal mutations to certain options.\n  called before ngOnChanges. called much more often than ngOnChanges.\n  */\n  ngDoCheck() {\n    if (this.calendar) {\n      // not the initial render\n      const {\n        deepChangeDetection,\n        optionSnapshot\n      } = this;\n      const newOptions = {\n        ...this.options,\n        ...this.buildInputOptions()\n      };\n      const newProcessedOptions = {};\n      const changedOptionNames = [];\n      // detect adds and updates (and update snapshot)\n      for (const optionName in newOptions) {\n        if (newOptions.hasOwnProperty(optionName)) {\n          let optionVal = newOptions[optionName];\n          if (deepChangeDetection && OPTION_IS_DEEP[optionName]) {\n            if (!deepEqual(optionSnapshot[optionName], optionVal)) {\n              optionSnapshot[optionName] = deepCopy(optionVal);\n              changedOptionNames.push(optionName);\n            }\n          } else {\n            if (optionSnapshot[optionName] !== optionVal) {\n              optionSnapshot[optionName] = optionVal;\n              changedOptionNames.push(optionName);\n            }\n          }\n          newProcessedOptions[optionName] = optionVal;\n        }\n      }\n      const oldOptionNames = Object.keys(optionSnapshot);\n      // detect removals (and update snapshot)\n      for (const optionName of oldOptionNames) {\n        if (!(optionName in newOptions)) {\n          // doesn't exist in new options?\n          delete optionSnapshot[optionName];\n          changedOptionNames.push(optionName);\n        }\n      }\n      if (changedOptionNames.length) {\n        this.calendar.pauseRendering();\n        this.calendar.resetOptions({\n          ...newProcessedOptions,\n          ...this.buildExtraOptions()\n        }, changedOptionNames);\n      }\n    }\n  }\n  ngAfterContentChecked() {\n    if (this.calendar) {\n      // too defensive?\n      this.calendar.resumeRendering();\n    }\n  }\n  ngOnDestroy() {\n    if (this.calendar) {\n      // too defensive?\n      this.calendar.destroy();\n      this.calendar = null;\n    }\n  }\n  get customRenderings() {\n    return this.customRenderingArray || (this.customRenderingArray = [...this.customRenderingMap.values()]);\n  }\n  getApi() {\n    return this.calendar;\n  }\n  buildInputOptions() {\n    const options = {};\n    for (const inputName of OPTION_INPUT_NAMES) {\n      const inputValue = this[inputName];\n      if (inputValue != null) {\n        // exclude both null and undefined\n        options[inputName] = inputValue;\n      }\n    }\n    return options;\n  }\n  buildExtraOptions() {\n    return {\n      handleCustomRendering: this.handleCustomRendering,\n      customRenderingMetaMap: this.templateMap,\n      customRenderingReplaces: true\n    };\n  }\n  // for `trackBy` in loop\n  trackCustomRendering(index, customRendering) {\n    return customRendering.id;\n  }\n}\nFullCalendarComponent.ɵfac = function FullCalendarComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || FullCalendarComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\nFullCalendarComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: FullCalendarComponent,\n  selectors: [[\"full-calendar\"]],\n  contentQueries: function FullCalendarComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, _c3, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c4, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c5, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c6, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c7, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c8, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c9, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c10, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c11, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c12, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c13, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c14, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c15, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c16, 7);\n      i0.ɵɵcontentQuery(dirIndex, _c17, 7);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayHeaderContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayCellContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.weekNumberContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nowIndicatorContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.eventContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLaneContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slotLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allDayContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.moreLinkContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.noEventsContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceAreaHeaderContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLabelContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceLaneContent = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resourceGroupLaneContent = _t.first);\n    }\n  },\n  inputs: {\n    options: \"options\",\n    deepChangeDetection: \"deepChangeDetection\",\n    events: \"events\",\n    eventSources: \"eventSources\",\n    resources: \"resources\"\n  },\n  decls: 2,\n  vars: 2,\n  consts: [[3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\"]],\n  template: function FullCalendarComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"offscreen-fragment\");\n      i0.ɵɵtemplate(1, FullCalendarComponent_transport_container_1_Template, 1, 8, \"transport-container\", 0);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngForOf\", ctx.customRenderings)(\"ngForTrackBy\", ctx.trackCustomRendering);\n    }\n  },\n  dependencies: [OffscreenFragmentComponent, TransportContainerComponent, i1.NgForOf],\n  encapsulation: 2\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullCalendarComponent, [{\n    type: Component,\n    args: [{\n      selector: 'full-calendar',\n      encapsulation: ViewEncapsulation.None // the styles are root-level, not scoped within the component\n      ,\n      template: \"<offscreen-fragment>\\n  <transport-container *ngFor=\\\"let customRendering of customRenderings; trackBy:trackCustomRendering\\\"\\n    [inPlaceOf]=\\\"customRendering.containerEl\\\"\\n    [reportEl]=\\\"customRendering.reportNewContainerEl\\\"\\n    [elTag]=\\\"customRendering.elTag\\\"\\n    [elClasses]=\\\"customRendering.elClasses\\\"\\n    [elStyle]=\\\"customRendering.elStyle\\\"\\n    [elAttrs]=\\\"customRendering.elAttrs\\\"\\n    [template]=\\\"templateMap[customRendering.generatorName]!\\\"\\n    [renderProps]=\\\"customRendering.renderProps\\\"\\n  ></transport-container>\\n</offscreen-fragment>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    options: [{\n      type: Input\n    }],\n    deepChangeDetection: [{\n      type: Input\n    }],\n    events: [{\n      type: Input\n    }],\n    eventSources: [{\n      type: Input\n    }],\n    resources: [{\n      type: Input\n    }],\n    dayHeaderContent: [{\n      type: ContentChild,\n      args: ['dayHeaderContent', {\n        static: true\n      }]\n    }],\n    dayCellContent: [{\n      type: ContentChild,\n      args: ['dayCellContent', {\n        static: true\n      }]\n    }],\n    weekNumberContent: [{\n      type: ContentChild,\n      args: ['weekNumberContent', {\n        static: true\n      }]\n    }],\n    nowIndicatorContent: [{\n      type: ContentChild,\n      args: ['nowIndicatorContent', {\n        static: true\n      }]\n    }],\n    eventContent: [{\n      type: ContentChild,\n      args: ['eventContent', {\n        static: true\n      }]\n    }],\n    slotLaneContent: [{\n      type: ContentChild,\n      args: ['slotLaneContent', {\n        static: true\n      }]\n    }],\n    slotLabelContent: [{\n      type: ContentChild,\n      args: ['slotLabelContent', {\n        static: true\n      }]\n    }],\n    allDayContent: [{\n      type: ContentChild,\n      args: ['allDayContent', {\n        static: true\n      }]\n    }],\n    moreLinkContent: [{\n      type: ContentChild,\n      args: ['moreLinkContent', {\n        static: true\n      }]\n    }],\n    noEventsContent: [{\n      type: ContentChild,\n      args: ['noEventsContent', {\n        static: true\n      }]\n    }],\n    resourceAreaHeaderContent: [{\n      type: ContentChild,\n      args: ['resourceAreaHeaderContent', {\n        static: true\n      }]\n    }],\n    resourceGroupLabelContent: [{\n      type: ContentChild,\n      args: ['resourceGroupLabelContent', {\n        static: true\n      }]\n    }],\n    resourceLabelContent: [{\n      type: ContentChild,\n      args: ['resourceLabelContent', {\n        static: true\n      }]\n    }],\n    resourceLaneContent: [{\n      type: ContentChild,\n      args: ['resourceLaneContent', {\n        static: true\n      }]\n    }],\n    resourceGroupLaneContent: [{\n      type: ContentChild,\n      args: ['resourceGroupLaneContent', {\n        static: true\n      }]\n    }]\n  });\n})();\nclass FullCalendarModule {}\nFullCalendarModule.ɵfac = function FullCalendarModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || FullCalendarModule)();\n};\nFullCalendarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FullCalendarModule\n});\nFullCalendarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullCalendarModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [FullCalendarComponent, OffscreenFragmentComponent, TransportContainerComponent],\n      imports: [CommonModule],\n      exports: [FullCalendarComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of lib\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FullCalendarComponent, FullCalendarModule };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "Input", "ViewChild", "ContentChild", "NgModule", "Calendar", "CustomRenderingStore", "i1", "CommonModule", "_c0", "_c1", "_c2", "a0", "$implicit", "TransportContainerComponent_ng_template_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainer", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "elClasses", "elStyle", "ɵɵadvance", "template", "ɵɵpureFunction1", "renderProps", "TransportContainerComponent_ng_template_1_Template", "TransportContainerComponent_ng_template_2_Template", "TransportContainerComponent_ng_template_3_Template", "TransportContainerComponent_ng_template_4_Template", "TransportContainerComponent_ng_template_5_Template", "_c3", "_c4", "_c5", "_c6", "_c7", "_c8", "_c9", "_c10", "_c11", "_c12", "_c13", "_c14", "_c15", "_c16", "_c17", "FullCalendarComponent_transport_container_1_Template", "ɵɵelement", "customRendering_r1", "ctx_r1", "containerEl", "reportNewContainerEl", "elTag", "elAttrs", "templateMap", "generatorName", "OPTION_IS_DEEP", "headerToolbar", "footerT<PERSON>bar", "events", "eventSources", "resources", "OPTION_INPUT_NAMES", "hasOwnProperty", "Object", "prototype", "deepCopy", "input", "Array", "isArray", "map", "Date", "valueOf", "mapHash", "func", "output", "key", "call", "deepEqual", "a", "b", "constructor", "length", "i", "keys", "RegExp", "source", "flags", "toString", "dummyContainer$1", "document", "createDocumentFragment", "OffscreenFragmentComponent", "element", "ngAfterViewInit", "append<PERSON><PERSON><PERSON>", "nativeElement", "ngOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "OffscreenFragmentComponent_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "ngContentSelectors", "decls", "vars", "OffscreenFragmentComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "du<PERSON><PERSON><PERSON><PERSON>", "TransportContainerComponent", "rootEl", "rootElRef", "replaceEl", "inPlaceOf", "applyElAttrs", "undefined", "style", "display", "reportEl", "ngOnChanges", "changes", "parentNode", "elAttrsChange", "previousValue", "currentValue", "TransportContainerComponent_Factory", "viewQuery", "TransportContainerComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "features", "ɵɵNgOnChangesFeature", "consts", "TransportContainerComponent_Template", "ɵɵtemplate", "dependencies", "NgIf", "Ng<PERSON><PERSON>", "NgStyle", "NgTemplateOutlet", "subject", "insertBefore", "nextS<PERSON>ling", "el", "previousAttrs", "currentAttrs", "attrName", "FullCalendarComponent", "changeDetector", "calendar", "optionSnapshot", "customRenderingMap", "Map", "customRenderingStore", "subscribe", "customRenderingArray", "detectChanges", "handleCustomRendering", "handle", "bind", "deepChangeDetection", "options", "buildInputOptions", "optionVal", "optionName", "calendarEl", "buildExtraOptions", "ionContent", "closest", "componentOnReady", "then", "window", "requestAnimationFrame", "render", "ngDoCheck", "newOptions", "newProcessedOptions", "changedOptionNames", "push", "oldOptionNames", "pauseRendering", "resetOptions", "ngAfterContentChecked", "resumeRendering", "destroy", "customRenderings", "values", "getApi", "inputName", "inputValue", "customRenderingMetaMap", "customRenderingReplaces", "trackCustomRendering", "index", "customRendering", "id", "FullCalendarComponent_Factory", "ChangeDetectorRef", "contentQueries", "FullCalendarComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weekNumberContent", "nowIndicatorContent", "eventContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allDayContent", "moreLinkContent", "noEventsContent", "resourceAreaHeaderContent", "resourceGroupLabelContent", "resourceLabelContent", "resourceLaneContent", "resourceGroupLaneContent", "FullCalendarComponent_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "static", "FullCalendarModule", "FullCalendarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "declarations", "exports"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@fullcalendar/angular/fesm2020/fullcalendar-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ViewChild, ContentChild, NgModule } from '@angular/core';\nimport { Calendar } from '@fullcalendar/core';\nimport { CustomRenderingStore } from '@fullcalendar/core/internal';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nconst OPTION_IS_DEEP = {\n    headerToolbar: true,\n    footerToolbar: true,\n    events: true,\n    eventSources: true,\n    resources: true\n};\n/*\nNOTE: keep synced with component\n*/\nconst OPTION_INPUT_NAMES = [\n    'events',\n    'eventSources',\n    'resources',\n];\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n/*\nReally simple clone utility. Only copies plain arrays, objects, and Dates. Transfers everything else as-is.\nWanted to use a third-party lib, but none did exactly this.\n*/\nfunction deepCopy(input) {\n    if (Array.isArray(input)) {\n        return input.map(deepCopy);\n    }\n    else if (input instanceof Date) {\n        return new Date(input.valueOf());\n    }\n    else if (typeof input === 'object' && input) { // non-null object\n        return mapHash(input, deepCopy);\n    }\n    else { // everything else (null, function, etc)\n        return input;\n    }\n}\nfunction mapHash(input, func) {\n    const output = {};\n    for (const key in input) {\n        if (hasOwnProperty.call(input, key)) {\n            output[key] = func(input[key], key);\n        }\n    }\n    return output;\n}\n\n/*\nForked from https://github.com/epoberezkin/fast-deep-equal (also has MIT license)\nNeeded ESM support or else Angular complains about treeshaking\n(https://github.com/fullcalendar/fullcalendar-angular/issues/421)\n*/\nfunction deepEqual(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n        if (a.constructor !== b.constructor)\n            return false;\n        var length, i, keys;\n        if (Array.isArray(a)) {\n            length = a.length;\n            if (length != b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!deepEqual(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (a.constructor === RegExp)\n            return a.source === b.source && a.flags === b.flags;\n        if (a.valueOf !== Object.prototype.valueOf)\n            return a.valueOf() === b.valueOf();\n        if (a.toString !== Object.prototype.toString)\n            return a.toString() === b.toString();\n        keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!Object.prototype.hasOwnProperty.call(b, keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            var key = keys[i];\n            if (!deepEqual(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    // true if both NaN, false otherwise\n    return a !== a && b !== b;\n}\n\nconst dummyContainer$1 = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass OffscreenFragmentComponent {\n    constructor(element) {\n        this.element = element;\n    }\n    ngAfterViewInit() {\n        if (dummyContainer$1) {\n            dummyContainer$1.appendChild(this.element.nativeElement);\n        }\n    }\n    // invoked BEFORE component removed from DOM\n    ngOnDestroy() {\n        if (dummyContainer$1) {\n            dummyContainer$1.removeChild(this.element.nativeElement);\n        }\n    }\n}\nOffscreenFragmentComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: OffscreenFragmentComponent, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nOffscreenFragmentComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: OffscreenFragmentComponent, selector: \"offscreen-fragment\", ngImport: i0, template: '<ng-content></ng-content>', isInline: true, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: OffscreenFragmentComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'offscreen-fragment',\n                    template: '<ng-content></ng-content>',\n                    encapsulation: ViewEncapsulation.None\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n\nconst dummyContainer = typeof document !== 'undefined' ? document.createDocumentFragment() : null;\nclass TransportContainerComponent {\n    ngAfterViewInit() {\n        const rootEl = this.rootElRef?.nativeElement; // assumed defined\n        replaceEl(rootEl, this.inPlaceOf);\n        applyElAttrs(rootEl, undefined, this.elAttrs);\n        // insurance for if Preact recreates and reroots inPlaceOf element\n        this.inPlaceOf.style.display = 'none';\n        this.reportEl(rootEl);\n    }\n    ngOnChanges(changes) {\n        const rootEl = this.rootElRef?.nativeElement;\n        // ngOnChanges is called before ngAfterViewInit (and before DOM initializes)\n        // so make sure rootEl is defined before doing anything\n        if (rootEl) {\n            // If the ContentContainer's tagName changed, it will create a new DOM element in its\n            // original place. Detect this and re-replace.\n            if (this.inPlaceOf.parentNode !== dummyContainer) {\n                replaceEl(rootEl, this.inPlaceOf);\n                applyElAttrs(rootEl, undefined, this.elAttrs);\n                this.reportEl(rootEl);\n            }\n            else {\n                const elAttrsChange = changes['elAttrs'];\n                if (elAttrsChange) {\n                    applyElAttrs(rootEl, elAttrsChange.previousValue, elAttrsChange.currentValue);\n                }\n            }\n        }\n    }\n    // invoked BEFORE component removed from DOM\n    ngOnDestroy() {\n        if (\n        // protect against Preact recreating and rerooting inPlaceOf element\n        this.inPlaceOf.parentNode === dummyContainer &&\n            dummyContainer) {\n            dummyContainer.removeChild(this.inPlaceOf);\n        }\n        this.reportEl(null);\n    }\n}\nTransportContainerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: TransportContainerComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nTransportContainerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: TransportContainerComponent, selector: \"transport-container\", inputs: { inPlaceOf: \"inPlaceOf\", reportEl: \"reportEl\", elTag: \"elTag\", elClasses: \"elClasses\", elStyle: \"elStyle\", elAttrs: \"elAttrs\", template: \"template\", renderProps: \"renderProps\" }, viewQueries: [{ propertyName: \"rootElRef\", first: true, predicate: [\"rootEl\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<ng-template [ngIf]=\\\"elTag == 'div'\\\">\\n  <div #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </div>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'span'\\\">\\n  <span #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </span>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'a'\\\">\\n  <a #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </a>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'tr'\\\">\\n  <tr #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </tr>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'th'\\\">\\n  <th #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </th>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'td'\\\">\\n  <td #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </td>\\n</ng-template>\\n\", directives: [{ type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: TransportContainerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'transport-container', encapsulation: ViewEncapsulation.None, template: \"<ng-template [ngIf]=\\\"elTag == 'div'\\\">\\n  <div #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </div>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'span'\\\">\\n  <span #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </span>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'a'\\\">\\n  <a #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </a>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'tr'\\\">\\n  <tr #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </tr>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'th'\\\">\\n  <th #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </th>\\n</ng-template>\\n<ng-template [ngIf]=\\\"elTag == 'td'\\\">\\n  <td #rootEl [ngClass]=\\\"elClasses || ''\\\" [ngStyle]=\\\"elStyle || null\\\">\\n    <ng-container\\n      [ngTemplateOutlet]=\\\"template\\\"\\n      [ngTemplateOutletContext]=\\\"{ $implicit: renderProps }\\\"\\n    ></ng-container>\\n  </td>\\n</ng-template>\\n\" }]\n        }], propDecorators: { inPlaceOf: [{\n                type: Input\n            }], reportEl: [{\n                type: Input\n            }], elTag: [{\n                type: Input\n            }], elClasses: [{\n                type: Input\n            }], elStyle: [{\n                type: Input\n            }], elAttrs: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }], renderProps: [{\n                type: Input\n            }], rootElRef: [{\n                type: ViewChild,\n                args: ['rootEl']\n            }] } });\nfunction replaceEl(subject, inPlaceOf) {\n    inPlaceOf.parentNode?.insertBefore(subject, inPlaceOf.nextSibling);\n    if (dummyContainer) {\n        dummyContainer.appendChild(inPlaceOf);\n    }\n}\nfunction applyElAttrs(el, previousAttrs = {}, currentAttrs = {}) {\n    // these are called \"attributes\" but they manipulate DOM node *properties*\n    for (const attrName in previousAttrs) {\n        if (!(attrName in currentAttrs)) {\n            el[attrName] = null;\n        }\n    }\n    for (const attrName in currentAttrs) {\n        el[attrName] = currentAttrs[attrName];\n    }\n}\n\nclass FullCalendarComponent {\n    constructor(element, changeDetector) {\n        this.element = element;\n        this.calendar = null;\n        this.optionSnapshot = {}; // for diffing\n        this.customRenderingMap = new Map();\n        this.templateMap = {};\n        const customRenderingStore = new CustomRenderingStore();\n        customRenderingStore.subscribe((customRenderingMap) => {\n            this.customRenderingMap = customRenderingMap;\n            this.customRenderingArray = undefined; // clear cache\n            changeDetector.detectChanges();\n        });\n        this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore);\n        this.templateMap = this; // alias to this\n    }\n    ngAfterViewInit() {\n        const { deepChangeDetection } = this;\n        const options = {\n            ...this.options,\n            ...this.buildInputOptions(),\n        };\n        // initialize snapshot\n        this.optionSnapshot = mapHash(options, (optionVal, optionName) => ((deepChangeDetection && OPTION_IS_DEEP[optionName])\n            ? deepCopy(optionVal)\n            : optionVal));\n        const calendarEl = this.element.nativeElement;\n        const calendar = this.calendar = new Calendar(calendarEl, {\n            ...options,\n            ...this.buildExtraOptions(),\n        });\n        // Ionic dimensions hack\n        // https://github.com/fullcalendar/fullcalendar/issues/4976\n        const ionContent = calendarEl.closest('ion-content');\n        if (ionContent && ionContent.componentOnReady) {\n            ionContent.componentOnReady().then(() => {\n                window.requestAnimationFrame(() => {\n                    calendar.render();\n                });\n            });\n        }\n        else {\n            calendar.render();\n        }\n    }\n    /*\n    allows us to manually detect complex input changes, internal mutations to certain options.\n    called before ngOnChanges. called much more often than ngOnChanges.\n    */\n    ngDoCheck() {\n        if (this.calendar) { // not the initial render\n            const { deepChangeDetection, optionSnapshot } = this;\n            const newOptions = {\n                ...this.options,\n                ...this.buildInputOptions(),\n            };\n            const newProcessedOptions = {};\n            const changedOptionNames = [];\n            // detect adds and updates (and update snapshot)\n            for (const optionName in newOptions) {\n                if (newOptions.hasOwnProperty(optionName)) {\n                    let optionVal = newOptions[optionName];\n                    if (deepChangeDetection && OPTION_IS_DEEP[optionName]) {\n                        if (!deepEqual(optionSnapshot[optionName], optionVal)) {\n                            optionSnapshot[optionName] = deepCopy(optionVal);\n                            changedOptionNames.push(optionName);\n                        }\n                    }\n                    else {\n                        if (optionSnapshot[optionName] !== optionVal) {\n                            optionSnapshot[optionName] = optionVal;\n                            changedOptionNames.push(optionName);\n                        }\n                    }\n                    newProcessedOptions[optionName] = optionVal;\n                }\n            }\n            const oldOptionNames = Object.keys(optionSnapshot);\n            // detect removals (and update snapshot)\n            for (const optionName of oldOptionNames) {\n                if (!(optionName in newOptions)) { // doesn't exist in new options?\n                    delete optionSnapshot[optionName];\n                    changedOptionNames.push(optionName);\n                }\n            }\n            if (changedOptionNames.length) {\n                this.calendar.pauseRendering();\n                this.calendar.resetOptions({\n                    ...newProcessedOptions,\n                    ...this.buildExtraOptions(),\n                }, changedOptionNames);\n            }\n        }\n    }\n    ngAfterContentChecked() {\n        if (this.calendar) { // too defensive?\n            this.calendar.resumeRendering();\n        }\n    }\n    ngOnDestroy() {\n        if (this.calendar) { // too defensive?\n            this.calendar.destroy();\n            this.calendar = null;\n        }\n    }\n    get customRenderings() {\n        return this.customRenderingArray ||\n            (this.customRenderingArray = [...this.customRenderingMap.values()]);\n    }\n    getApi() {\n        return this.calendar;\n    }\n    buildInputOptions() {\n        const options = {};\n        for (const inputName of OPTION_INPUT_NAMES) {\n            const inputValue = this[inputName];\n            if (inputValue != null) { // exclude both null and undefined\n                options[inputName] = inputValue;\n            }\n        }\n        return options;\n    }\n    buildExtraOptions() {\n        return {\n            handleCustomRendering: this.handleCustomRendering,\n            customRenderingMetaMap: this.templateMap,\n            customRenderingReplaces: true,\n        };\n    }\n    // for `trackBy` in loop\n    trackCustomRendering(index, customRendering) {\n        return customRendering.id;\n    }\n}\nFullCalendarComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarComponent, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nFullCalendarComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.12\", type: FullCalendarComponent, selector: \"full-calendar\", inputs: { options: \"options\", deepChangeDetection: \"deepChangeDetection\", events: \"events\", eventSources: \"eventSources\", resources: \"resources\" }, queries: [{ propertyName: \"dayHeaderContent\", first: true, predicate: [\"dayHeaderContent\"], descendants: true, static: true }, { propertyName: \"dayCellContent\", first: true, predicate: [\"dayCellContent\"], descendants: true, static: true }, { propertyName: \"weekNumberContent\", first: true, predicate: [\"weekNumberContent\"], descendants: true, static: true }, { propertyName: \"nowIndicatorContent\", first: true, predicate: [\"nowIndicatorContent\"], descendants: true, static: true }, { propertyName: \"eventContent\", first: true, predicate: [\"eventContent\"], descendants: true, static: true }, { propertyName: \"slotLaneContent\", first: true, predicate: [\"slotLaneContent\"], descendants: true, static: true }, { propertyName: \"slotLabelContent\", first: true, predicate: [\"slotLabelContent\"], descendants: true, static: true }, { propertyName: \"allDayContent\", first: true, predicate: [\"allDayContent\"], descendants: true, static: true }, { propertyName: \"moreLinkContent\", first: true, predicate: [\"moreLinkContent\"], descendants: true, static: true }, { propertyName: \"noEventsContent\", first: true, predicate: [\"noEventsContent\"], descendants: true, static: true }, { propertyName: \"resourceAreaHeaderContent\", first: true, predicate: [\"resourceAreaHeaderContent\"], descendants: true, static: true }, { propertyName: \"resourceGroupLabelContent\", first: true, predicate: [\"resourceGroupLabelContent\"], descendants: true, static: true }, { propertyName: \"resourceLabelContent\", first: true, predicate: [\"resourceLabelContent\"], descendants: true, static: true }, { propertyName: \"resourceLaneContent\", first: true, predicate: [\"resourceLaneContent\"], descendants: true, static: true }, { propertyName: \"resourceGroupLaneContent\", first: true, predicate: [\"resourceGroupLaneContent\"], descendants: true, static: true }], ngImport: i0, template: \"<offscreen-fragment>\\n  <transport-container *ngFor=\\\"let customRendering of customRenderings; trackBy:trackCustomRendering\\\"\\n    [inPlaceOf]=\\\"customRendering.containerEl\\\"\\n    [reportEl]=\\\"customRendering.reportNewContainerEl\\\"\\n    [elTag]=\\\"customRendering.elTag\\\"\\n    [elClasses]=\\\"customRendering.elClasses\\\"\\n    [elStyle]=\\\"customRendering.elStyle\\\"\\n    [elAttrs]=\\\"customRendering.elAttrs\\\"\\n    [template]=\\\"templateMap[customRendering.generatorName]!\\\"\\n    [renderProps]=\\\"customRendering.renderProps\\\"\\n  ></transport-container>\\n</offscreen-fragment>\\n\", components: [{ type: OffscreenFragmentComponent, selector: \"offscreen-fragment\" }, { type: TransportContainerComponent, selector: \"transport-container\", inputs: [\"inPlaceOf\", \"reportEl\", \"elTag\", \"elClasses\", \"elStyle\", \"elAttrs\", \"template\", \"renderProps\"] }], directives: [{ type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'full-calendar', encapsulation: ViewEncapsulation.None // the styles are root-level, not scoped within the component\n                    , template: \"<offscreen-fragment>\\n  <transport-container *ngFor=\\\"let customRendering of customRenderings; trackBy:trackCustomRendering\\\"\\n    [inPlaceOf]=\\\"customRendering.containerEl\\\"\\n    [reportEl]=\\\"customRendering.reportNewContainerEl\\\"\\n    [elTag]=\\\"customRendering.elTag\\\"\\n    [elClasses]=\\\"customRendering.elClasses\\\"\\n    [elStyle]=\\\"customRendering.elStyle\\\"\\n    [elAttrs]=\\\"customRendering.elAttrs\\\"\\n    [template]=\\\"templateMap[customRendering.generatorName]!\\\"\\n    [renderProps]=\\\"customRendering.renderProps\\\"\\n  ></transport-container>\\n</offscreen-fragment>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { options: [{\n                type: Input\n            }], deepChangeDetection: [{\n                type: Input\n            }], events: [{\n                type: Input\n            }], eventSources: [{\n                type: Input\n            }], resources: [{\n                type: Input\n            }], dayHeaderContent: [{\n                type: ContentChild,\n                args: ['dayHeaderContent', { static: true }]\n            }], dayCellContent: [{\n                type: ContentChild,\n                args: ['dayCellContent', { static: true }]\n            }], weekNumberContent: [{\n                type: ContentChild,\n                args: ['weekNumberContent', { static: true }]\n            }], nowIndicatorContent: [{\n                type: ContentChild,\n                args: ['nowIndicatorContent', { static: true }]\n            }], eventContent: [{\n                type: ContentChild,\n                args: ['eventContent', { static: true }]\n            }], slotLaneContent: [{\n                type: ContentChild,\n                args: ['slotLaneContent', { static: true }]\n            }], slotLabelContent: [{\n                type: ContentChild,\n                args: ['slotLabelContent', { static: true }]\n            }], allDayContent: [{\n                type: ContentChild,\n                args: ['allDayContent', { static: true }]\n            }], moreLinkContent: [{\n                type: ContentChild,\n                args: ['moreLinkContent', { static: true }]\n            }], noEventsContent: [{\n                type: ContentChild,\n                args: ['noEventsContent', { static: true }]\n            }], resourceAreaHeaderContent: [{\n                type: ContentChild,\n                args: ['resourceAreaHeaderContent', { static: true }]\n            }], resourceGroupLabelContent: [{\n                type: ContentChild,\n                args: ['resourceGroupLabelContent', { static: true }]\n            }], resourceLabelContent: [{\n                type: ContentChild,\n                args: ['resourceLabelContent', { static: true }]\n            }], resourceLaneContent: [{\n                type: ContentChild,\n                args: ['resourceLaneContent', { static: true }]\n            }], resourceGroupLaneContent: [{\n                type: ContentChild,\n                args: ['resourceGroupLaneContent', { static: true }]\n            }] } });\n\nclass FullCalendarModule {\n}\nFullCalendarModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nFullCalendarModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarModule, declarations: [FullCalendarComponent,\n        OffscreenFragmentComponent,\n        TransportContainerComponent], imports: [CommonModule], exports: [FullCalendarComponent] });\nFullCalendarModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarModule, imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: FullCalendarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        FullCalendarComponent,\n                        OffscreenFragmentComponent,\n                        TransportContainerComponent\n                    ],\n                    imports: [\n                        CommonModule\n                    ],\n                    exports: [\n                        FullCalendarComponent\n                    ]\n                }]\n        }] });\n\n/*\n * Public API Surface of lib\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FullCalendarComponent, FullCalendarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACtG,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,mDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA6G+DjB,EAAE,CAAAmB,cAAA,eAqD6f,CAAC;IArDhgBnB,EAAE,CAAAoB,kBAAA,KAqD6oB,CAAC;IArDhpBpB,EAAE,CAAAqB,YAAA,CAqDupB,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArD1pBtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqD8d,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArD/f1B,EAAE,CAAA2B,SAAA,EAqDujB,CAAC;IArD1jB3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqDujB,CAAC,4BArD1jB5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqDunB,CAAC;EAAA;AAAA;AAAA,SAAAC,mDAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArD1nBjB,EAAE,CAAAmB,cAAA,gBAqD+xB,CAAC;IArDlyBnB,EAAE,CAAAoB,kBAAA,KAqD+6B,CAAC;IArDl7BpB,EAAE,CAAAqB,YAAA,CAqD07B,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArD77BtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqDgwB,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArDjyB1B,EAAE,CAAA2B,SAAA,EAqDy1B,CAAC;IArD51B3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqDy1B,CAAC,4BArD51B5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqDy5B,CAAC;EAAA;AAAA;AAAA,SAAAE,mDAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArD55BjB,EAAE,CAAAmB,cAAA,aAqD4jC,CAAC;IArD/jCnB,EAAE,CAAAoB,kBAAA,KAqD4sC,CAAC;IArD/sCpB,EAAE,CAAAqB,YAAA,CAqDotC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArDvtCtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqD6hC,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArD9jC1B,EAAE,CAAA2B,SAAA,EAqDsnC,CAAC;IArDznC3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqDsnC,CAAC,4BArDznC5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqDsrC,CAAC;EAAA;AAAA;AAAA,SAAAG,mDAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArDzrCjB,EAAE,CAAAmB,cAAA,cAqDw1C,CAAC;IArD31CnB,EAAE,CAAAoB,kBAAA,KAqDw+C,CAAC;IArD3+CpB,EAAE,CAAAqB,YAAA,CAqDi/C,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArDp/CtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqDyzC,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArD11C1B,EAAE,CAAA2B,SAAA,EAqDk5C,CAAC;IArDr5C3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqDk5C,CAAC,4BArDr5C5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqDk9C,CAAC;EAAA;AAAA;AAAA,SAAAI,mDAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArDr9CjB,EAAE,CAAAmB,cAAA,cAqDqnD,CAAC;IArDxnDnB,EAAE,CAAAoB,kBAAA,KAqDqwD,CAAC;IArDxwDpB,EAAE,CAAAqB,YAAA,CAqD8wD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArDjxDtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqDslD,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArDvnD1B,EAAE,CAAA2B,SAAA,EAqD+qD,CAAC;IArDlrD3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqD+qD,CAAC,4BArDlrD5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqD+uD,CAAC;EAAA;AAAA;AAAA,SAAAK,mDAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArDlvDjB,EAAE,CAAAmB,cAAA,cAqDk5D,CAAC;IArDr5DnB,EAAE,CAAAoB,kBAAA,KAqDkiE,CAAC;IArDriEpB,EAAE,CAAAqB,YAAA,CAqD2iE,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GArD9iEtB,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,YAAAF,MAAA,CAAAG,SAAA,MAqDm3D,CAAC,YAAAH,MAAA,CAAAI,OAAA,QAA6B,CAAC;IArDp5D1B,EAAE,CAAA2B,SAAA,EAqD48D,CAAC;IArD/8D3B,EAAE,CAAAwB,UAAA,qBAAAF,MAAA,CAAAM,QAqD48D,CAAC,4BArD/8D5B,EAAE,CAAA6B,eAAA,IAAAhB,GAAA,EAAAS,MAAA,CAAAQ,WAAA,CAqD4gE,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,SAAAC,qDAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArD/gEjB,EAAE,CAAAoD,SAAA,4BAsO8gF,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAoC,kBAAA,GAAAnC,GAAA,CAAAH,SAAA;IAAA,MAAAuC,MAAA,GAtOjhFtD,EAAE,CAAAuB,aAAA;IAAFvB,EAAE,CAAAwB,UAAA,cAAA6B,kBAAA,CAAAE,WAsO2pE,CAAC,aAAAF,kBAAA,CAAAG,oBAAwD,CAAC,UAAAH,kBAAA,CAAAI,KAAsC,CAAC,cAAAJ,kBAAA,CAAA5B,SAA8C,CAAC,YAAA4B,kBAAA,CAAA3B,OAA0C,CAAC,YAAA2B,kBAAA,CAAAK,OAA0C,CAAC,aAAAJ,MAAA,CAAAK,WAAA,CAAAN,kBAAA,CAAAO,aAAA,CAA+D,CAAC,gBAAAP,kBAAA,CAAAvB,WAAkD,CAAC;EAAA;AAAA;AAjVpmF,MAAM+B,cAAc,GAAG;EACnBC,aAAa,EAAE,IAAI;EACnBC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE;AACf,CAAC;AACD;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,CACvB,QAAQ,EACR,cAAc,EACd,WAAW,CACd;AAED,MAAMC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AACtD;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAACG,GAAG,CAACJ,QAAQ,CAAC;EAC9B,CAAC,MACI,IAAIC,KAAK,YAAYI,IAAI,EAAE;IAC5B,OAAO,IAAIA,IAAI,CAACJ,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC;EACpC,CAAC,MACI,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,EAAE;IAAE;IAC3C,OAAOM,OAAO,CAACN,KAAK,EAAED,QAAQ,CAAC;EACnC,CAAC,MACI;IAAE;IACH,OAAOC,KAAK;EAChB;AACJ;AACA,SAASM,OAAOA,CAACN,KAAK,EAAEO,IAAI,EAAE;EAC1B,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAMC,GAAG,IAAIT,KAAK,EAAE;IACrB,IAAIJ,cAAc,CAACc,IAAI,CAACV,KAAK,EAAES,GAAG,CAAC,EAAE;MACjCD,MAAM,CAACC,GAAG,CAAC,GAAGF,IAAI,CAACP,KAAK,CAACS,GAAG,CAAC,EAAEA,GAAG,CAAC;IACvC;EACJ;EACA,OAAOD,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAID,CAAC,KAAKC,CAAC,EACP,OAAO,IAAI;EACf,IAAID,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IACxD,IAAID,CAAC,CAACE,WAAW,KAAKD,CAAC,CAACC,WAAW,EAC/B,OAAO,KAAK;IAChB,IAAIC,MAAM,EAAEC,CAAC,EAAEC,IAAI;IACnB,IAAIhB,KAAK,CAACC,OAAO,CAACU,CAAC,CAAC,EAAE;MAClBG,MAAM,GAAGH,CAAC,CAACG,MAAM;MACjB,IAAIA,MAAM,IAAIF,CAAC,CAACE,MAAM,EAClB,OAAO,KAAK;MAChB,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACtB,IAAI,CAACL,SAAS,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EACtB,OAAO,KAAK;MACpB,OAAO,IAAI;IACf;IACA,IAAIJ,CAAC,CAACE,WAAW,KAAKI,MAAM,EACxB,OAAON,CAAC,CAACO,MAAM,KAAKN,CAAC,CAACM,MAAM,IAAIP,CAAC,CAACQ,KAAK,KAAKP,CAAC,CAACO,KAAK;IACvD,IAAIR,CAAC,CAACP,OAAO,KAAKR,MAAM,CAACC,SAAS,CAACO,OAAO,EACtC,OAAOO,CAAC,CAACP,OAAO,CAAC,CAAC,KAAKQ,CAAC,CAACR,OAAO,CAAC,CAAC;IACtC,IAAIO,CAAC,CAACS,QAAQ,KAAKxB,MAAM,CAACC,SAAS,CAACuB,QAAQ,EACxC,OAAOT,CAAC,CAACS,QAAQ,CAAC,CAAC,KAAKR,CAAC,CAACQ,QAAQ,CAAC,CAAC;IACxCJ,IAAI,GAAGpB,MAAM,CAACoB,IAAI,CAACL,CAAC,CAAC;IACrBG,MAAM,GAAGE,IAAI,CAACF,MAAM;IACpB,IAAIA,MAAM,KAAKlB,MAAM,CAACoB,IAAI,CAACJ,CAAC,CAAC,CAACE,MAAM,EAChC,OAAO,KAAK;IAChB,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACtB,IAAI,CAACnB,MAAM,CAACC,SAAS,CAACF,cAAc,CAACc,IAAI,CAACG,CAAC,EAAEI,IAAI,CAACD,CAAC,CAAC,CAAC,EACjD,OAAO,KAAK;IACpB,KAAKA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;MACzB,IAAIP,GAAG,GAAGQ,IAAI,CAACD,CAAC,CAAC;MACjB,IAAI,CAACL,SAAS,CAACC,CAAC,CAACH,GAAG,CAAC,EAAEI,CAAC,CAACJ,GAAG,CAAC,CAAC,EAC1B,OAAO,KAAK;IACpB;IACA,OAAO,IAAI;EACf;EACA;EACA,OAAOG,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;AAC7B;AAEA,MAAMS,gBAAgB,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACC,sBAAsB,CAAC,CAAC,GAAG,IAAI;AACnG,MAAMC,0BAA0B,CAAC;EAC7BX,WAAWA,CAACY,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAIL,gBAAgB,EAAE;MAClBA,gBAAgB,CAACM,WAAW,CAAC,IAAI,CAACF,OAAO,CAACG,aAAa,CAAC;IAC5D;EACJ;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,IAAIR,gBAAgB,EAAE;MAClBA,gBAAgB,CAACS,WAAW,CAAC,IAAI,CAACL,OAAO,CAACG,aAAa,CAAC;IAC5D;EACJ;AACJ;AACAJ,0BAA0B,CAACO,IAAI,YAAAC,mCAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAyFT,0BAA0B,EAApCjG,EAAE,CAAA2G,iBAAA,CAAoD3G,EAAE,CAAC4G,UAAU;AAAA,CAA4C;AAC7NX,0BAA0B,CAACY,IAAI,kBAD+E7G,EAAE,CAAA8G,iBAAA;EAAAC,IAAA,EACJd,0BAA0B;EAAAe,SAAA;EAAAC,kBAAA,EAAAtG,GAAA;EAAAuG,KAAA;EAAAC,IAAA;EAAAvF,QAAA,WAAAwF,oCAAAnG,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MADxBjB,EAAE,CAAAqH,eAAA;MAAFrH,EAAE,CAAAsH,YAAA,EACyG,CAAC;IAAA;EAAA;EAAAC,aAAA;AAAA,EAA8D;AACxR;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAF8GxH,EAAE,CAAAyH,iBAAA,CAEpBxB,0BAA0B,EAAc,CAAC;IACzHc,IAAI,EAAE9G,SAAS;IACfyH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9B/F,QAAQ,EAAE,2BAA2B;MACrC2F,aAAa,EAAErH,iBAAiB,CAAC0H;IACrC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEb,IAAI,EAAE/G,EAAE,CAAC4G;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAE7E,MAAMiB,cAAc,GAAG,OAAO9B,QAAQ,KAAK,WAAW,GAAGA,QAAQ,CAACC,sBAAsB,CAAC,CAAC,GAAG,IAAI;AACjG,MAAM8B,2BAA2B,CAAC;EAC9B3B,eAAeA,CAAA,EAAG;IACd,MAAM4B,MAAM,GAAG,IAAI,CAACC,SAAS,EAAE3B,aAAa,CAAC,CAAC;IAC9C4B,SAAS,CAACF,MAAM,EAAE,IAAI,CAACG,SAAS,CAAC;IACjCC,YAAY,CAACJ,MAAM,EAAEK,SAAS,EAAE,IAAI,CAAC1E,OAAO,CAAC;IAC7C;IACA,IAAI,CAACwE,SAAS,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IACrC,IAAI,CAACC,QAAQ,CAACR,MAAM,CAAC;EACzB;EACAS,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMV,MAAM,GAAG,IAAI,CAACC,SAAS,EAAE3B,aAAa;IAC5C;IACA;IACA,IAAI0B,MAAM,EAAE;MACR;MACA;MACA,IAAI,IAAI,CAACG,SAAS,CAACQ,UAAU,KAAKb,cAAc,EAAE;QAC9CI,SAAS,CAACF,MAAM,EAAE,IAAI,CAACG,SAAS,CAAC;QACjCC,YAAY,CAACJ,MAAM,EAAEK,SAAS,EAAE,IAAI,CAAC1E,OAAO,CAAC;QAC7C,IAAI,CAAC6E,QAAQ,CAACR,MAAM,CAAC;MACzB,CAAC,MACI;QACD,MAAMY,aAAa,GAAGF,OAAO,CAAC,SAAS,CAAC;QACxC,IAAIE,aAAa,EAAE;UACfR,YAAY,CAACJ,MAAM,EAAEY,aAAa,CAACC,aAAa,EAAED,aAAa,CAACE,YAAY,CAAC;QACjF;MACJ;IACJ;EACJ;EACA;EACAvC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAAC4B,SAAS,CAACQ,UAAU,KAAKb,cAAc,IACxCA,cAAc,EAAE;MAChBA,cAAc,CAACtB,WAAW,CAAC,IAAI,CAAC2B,SAAS,CAAC;IAC9C;IACA,IAAI,CAACK,QAAQ,CAAC,IAAI,CAAC;EACvB;AACJ;AACAT,2BAA2B,CAACtB,IAAI,YAAAsC,oCAAApC,iBAAA;EAAA,YAAAA,iBAAA,IAAyFoB,2BAA2B;AAAA,CAAmD;AACvMA,2BAA2B,CAACjB,IAAI,kBArD8E7G,EAAE,CAAA8G,iBAAA;EAAAC,IAAA,EAqDHe,2BAA2B;EAAAd,SAAA;EAAA+B,SAAA,WAAAC,kCAAA/H,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MArD1BjB,EAAE,CAAAiJ,WAAA,CAAArI,GAAA;IAAA;IAAA,IAAAK,EAAA;MAAA,IAAAiI,EAAA;MAAFlJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA8G,SAAA,GAAAkB,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAApB,SAAA;IAAAK,QAAA;IAAA9E,KAAA;IAAAhC,SAAA;IAAAC,OAAA;IAAAgC,OAAA;IAAA9B,QAAA;IAAAE,WAAA;EAAA;EAAAyH,QAAA,GAAFvJ,EAAE,CAAAwJ,oBAAA;EAAAtC,KAAA;EAAAC,IAAA;EAAAsC,MAAA;EAAA7H,QAAA,WAAA8H,qCAAAzI,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFjB,EAAE,CAAA2J,UAAA,IAAA3I,kDAAA,wBAqDgb,CAAC,IAAAe,kDAAA,wBAAgS,CAAC,IAAAC,kDAAA,wBAA+R,CAAC,IAAAC,kDAAA,wBAA0R,CAAC,IAAAC,kDAAA,wBAA4R,CAAC,IAAAC,kDAAA,wBAA4R,CAAC;IAAA;IAAA,IAAAlB,EAAA;MArDz0DjB,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,SAqD+a,CAAC;MArDlbzD,EAAE,CAAA2B,SAAA,CAqDgtB,CAAC;MArDntB3B,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,UAqDgtB,CAAC;MArDntBzD,EAAE,CAAA2B,SAAA,CAqDg/B,CAAC;MArDn/B3B,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,OAqDg/B,CAAC;MArDn/BzD,EAAE,CAAA2B,SAAA,CAqD2wC,CAAC;MArD9wC3B,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,QAqD2wC,CAAC;MArD9wCzD,EAAE,CAAA2B,SAAA,CAqDwiD,CAAC;MArD3iD3B,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,QAqDwiD,CAAC;MArD3iDzD,EAAE,CAAA2B,SAAA,CAqDq0D,CAAC;MArDx0D3B,EAAE,CAAAwB,UAAA,SAAAN,GAAA,CAAAuC,KAAA,QAqDq0D,CAAC;IAAA;EAAA;EAAAmG,YAAA,GAAgRnJ,EAAE,CAACoJ,IAAI,EAA0EpJ,EAAE,CAACqJ,OAAO,EAAiErJ,EAAE,CAACsJ,OAAO,EAAwDtJ,EAAE,CAACuJ,gBAAgB;EAAAzC,aAAA;AAAA,EAAyI;AAChkF;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtD8GxH,EAAE,CAAAyH,iBAAA,CAsDpBK,2BAA2B,EAAc,CAAC;IAC1Hf,IAAI,EAAE9G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEJ,aAAa,EAAErH,iBAAiB,CAAC0H,IAAI;MAAEhG,QAAQ,EAAE;IAAurD,CAAC;EACvxD,CAAC,CAAC,QAAkB;IAAEsG,SAAS,EAAE,CAAC;MAC1BnB,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEoI,QAAQ,EAAE,CAAC;MACXxB,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEsD,KAAK,EAAE,CAAC;MACRsD,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEsB,SAAS,EAAE,CAAC;MACZsF,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEuB,OAAO,EAAE,CAAC;MACVqF,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEuD,OAAO,EAAE,CAAC;MACVqD,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEyB,QAAQ,EAAE,CAAC;MACXmF,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE2B,WAAW,EAAE,CAAC;MACdiF,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE6H,SAAS,EAAE,CAAC;MACZjB,IAAI,EAAE3G,SAAS;MACfsH,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC;EAAE,CAAC;AAAA;AAChB,SAASO,SAASA,CAACgC,OAAO,EAAE/B,SAAS,EAAE;EACnCA,SAAS,CAACQ,UAAU,EAAEwB,YAAY,CAACD,OAAO,EAAE/B,SAAS,CAACiC,WAAW,CAAC;EAClE,IAAItC,cAAc,EAAE;IAChBA,cAAc,CAACzB,WAAW,CAAC8B,SAAS,CAAC;EACzC;AACJ;AACA,SAASC,YAAYA,CAACiC,EAAE,EAAEC,aAAa,GAAG,CAAC,CAAC,EAAEC,YAAY,GAAG,CAAC,CAAC,EAAE;EAC7D;EACA,KAAK,MAAMC,QAAQ,IAAIF,aAAa,EAAE;IAClC,IAAI,EAAEE,QAAQ,IAAID,YAAY,CAAC,EAAE;MAC7BF,EAAE,CAACG,QAAQ,CAAC,GAAG,IAAI;IACvB;EACJ;EACA,KAAK,MAAMA,QAAQ,IAAID,YAAY,EAAE;IACjCF,EAAE,CAACG,QAAQ,CAAC,GAAGD,YAAY,CAACC,QAAQ,CAAC;EACzC;AACJ;AAEA,MAAMC,qBAAqB,CAAC;EACxBlF,WAAWA,CAACY,OAAO,EAAEuE,cAAc,EAAE;IACjC,IAAI,CAACvE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACwE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,IAAI,CAAClH,WAAW,GAAG,CAAC,CAAC;IACrB,MAAMmH,oBAAoB,GAAG,IAAItK,oBAAoB,CAAC,CAAC;IACvDsK,oBAAoB,CAACC,SAAS,CAAEH,kBAAkB,IAAK;MACnD,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;MAC5C,IAAI,CAACI,oBAAoB,GAAG5C,SAAS,CAAC,CAAC;MACvCqC,cAAc,CAACQ,aAAa,CAAC,CAAC;IAClC,CAAC,CAAC;IACF,IAAI,CAACC,qBAAqB,GAAGJ,oBAAoB,CAACK,MAAM,CAACC,IAAI,CAACN,oBAAoB,CAAC;IACnF,IAAI,CAACnH,WAAW,GAAG,IAAI,CAAC,CAAC;EAC7B;EACAwC,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEkF;IAAoB,CAAC,GAAG,IAAI;IACpC,MAAMC,OAAO,GAAG;MACZ,GAAG,IAAI,CAACA,OAAO;MACf,GAAG,IAAI,CAACC,iBAAiB,CAAC;IAC9B,CAAC;IACD;IACA,IAAI,CAACZ,cAAc,GAAG7F,OAAO,CAACwG,OAAO,EAAE,CAACE,SAAS,EAAEC,UAAU,KAAOJ,mBAAmB,IAAIxH,cAAc,CAAC4H,UAAU,CAAC,GAC/GlH,QAAQ,CAACiH,SAAS,CAAC,GACnBA,SAAU,CAAC;IACjB,MAAME,UAAU,GAAG,IAAI,CAACxF,OAAO,CAACG,aAAa;IAC7C,MAAMqE,QAAQ,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAInK,QAAQ,CAACmL,UAAU,EAAE;MACtD,GAAGJ,OAAO;MACV,GAAG,IAAI,CAACK,iBAAiB,CAAC;IAC9B,CAAC,CAAC;IACF;IACA;IACA,MAAMC,UAAU,GAAGF,UAAU,CAACG,OAAO,CAAC,aAAa,CAAC;IACpD,IAAID,UAAU,IAAIA,UAAU,CAACE,gBAAgB,EAAE;MAC3CF,UAAU,CAACE,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACrCC,MAAM,CAACC,qBAAqB,CAAC,MAAM;UAC/BvB,QAAQ,CAACwB,MAAM,CAAC,CAAC;QACrB,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACDxB,QAAQ,CAACwB,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACzB,QAAQ,EAAE;MAAE;MACjB,MAAM;QAAEW,mBAAmB;QAAEV;MAAe,CAAC,GAAG,IAAI;MACpD,MAAMyB,UAAU,GAAG;QACf,GAAG,IAAI,CAACd,OAAO;QACf,GAAG,IAAI,CAACC,iBAAiB,CAAC;MAC9B,CAAC;MACD,MAAMc,mBAAmB,GAAG,CAAC,CAAC;MAC9B,MAAMC,kBAAkB,GAAG,EAAE;MAC7B;MACA,KAAK,MAAMb,UAAU,IAAIW,UAAU,EAAE;QACjC,IAAIA,UAAU,CAAChI,cAAc,CAACqH,UAAU,CAAC,EAAE;UACvC,IAAID,SAAS,GAAGY,UAAU,CAACX,UAAU,CAAC;UACtC,IAAIJ,mBAAmB,IAAIxH,cAAc,CAAC4H,UAAU,CAAC,EAAE;YACnD,IAAI,CAACtG,SAAS,CAACwF,cAAc,CAACc,UAAU,CAAC,EAAED,SAAS,CAAC,EAAE;cACnDb,cAAc,CAACc,UAAU,CAAC,GAAGlH,QAAQ,CAACiH,SAAS,CAAC;cAChDc,kBAAkB,CAACC,IAAI,CAACd,UAAU,CAAC;YACvC;UACJ,CAAC,MACI;YACD,IAAId,cAAc,CAACc,UAAU,CAAC,KAAKD,SAAS,EAAE;cAC1Cb,cAAc,CAACc,UAAU,CAAC,GAAGD,SAAS;cACtCc,kBAAkB,CAACC,IAAI,CAACd,UAAU,CAAC;YACvC;UACJ;UACAY,mBAAmB,CAACZ,UAAU,CAAC,GAAGD,SAAS;QAC/C;MACJ;MACA,MAAMgB,cAAc,GAAGnI,MAAM,CAACoB,IAAI,CAACkF,cAAc,CAAC;MAClD;MACA,KAAK,MAAMc,UAAU,IAAIe,cAAc,EAAE;QACrC,IAAI,EAAEf,UAAU,IAAIW,UAAU,CAAC,EAAE;UAAE;UAC/B,OAAOzB,cAAc,CAACc,UAAU,CAAC;UACjCa,kBAAkB,CAACC,IAAI,CAACd,UAAU,CAAC;QACvC;MACJ;MACA,IAAIa,kBAAkB,CAAC/G,MAAM,EAAE;QAC3B,IAAI,CAACmF,QAAQ,CAAC+B,cAAc,CAAC,CAAC;QAC9B,IAAI,CAAC/B,QAAQ,CAACgC,YAAY,CAAC;UACvB,GAAGL,mBAAmB;UACtB,GAAG,IAAI,CAACV,iBAAiB,CAAC;QAC9B,CAAC,EAAEW,kBAAkB,CAAC;MAC1B;IACJ;EACJ;EACAK,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACjC,QAAQ,EAAE;MAAE;MACjB,IAAI,CAACA,QAAQ,CAACkC,eAAe,CAAC,CAAC;IACnC;EACJ;EACAtG,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACoE,QAAQ,EAAE;MAAE;MACjB,IAAI,CAACA,QAAQ,CAACmC,OAAO,CAAC,CAAC;MACvB,IAAI,CAACnC,QAAQ,GAAG,IAAI;IACxB;EACJ;EACA,IAAIoC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC9B,oBAAoB,KAC3B,IAAI,CAACA,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACJ,kBAAkB,CAACmC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3E;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtC,QAAQ;EACxB;EACAa,iBAAiBA,CAAA,EAAG;IAChB,MAAMD,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,MAAM2B,SAAS,IAAI9I,kBAAkB,EAAE;MACxC,MAAM+I,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC;MAClC,IAAIC,UAAU,IAAI,IAAI,EAAE;QAAE;QACtB5B,OAAO,CAAC2B,SAAS,CAAC,GAAGC,UAAU;MACnC;IACJ;IACA,OAAO5B,OAAO;EAClB;EACAK,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACHT,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;MACjDiC,sBAAsB,EAAE,IAAI,CAACxJ,WAAW;MACxCyJ,uBAAuB,EAAE;IAC7B,CAAC;EACL;EACA;EACAC,oBAAoBA,CAACC,KAAK,EAAEC,eAAe,EAAE;IACzC,OAAOA,eAAe,CAACC,EAAE;EAC7B;AACJ;AACAhD,qBAAqB,CAAChE,IAAI,YAAAiH,8BAAA/G,iBAAA;EAAA,YAAAA,iBAAA,IAAyF8D,qBAAqB,EArO1BxK,EAAE,CAAA2G,iBAAA,CAqO0C3G,EAAE,CAAC4G,UAAU,GArOzD5G,EAAE,CAAA2G,iBAAA,CAqOoE3G,EAAE,CAAC0N,iBAAiB;AAAA,CAA4C;AACpPlD,qBAAqB,CAAC3D,IAAI,kBAtOoF7G,EAAE,CAAA8G,iBAAA;EAAAC,IAAA,EAsOTyD,qBAAqB;EAAAxD,SAAA;EAAA2G,cAAA,WAAAC,qCAAA3M,EAAA,EAAAC,GAAA,EAAA2M,QAAA;IAAA,IAAA5M,EAAA;MAtOdjB,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAzL,GAAA;MAAFpC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAxL,GAAA;MAAFrC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAvL,GAAA;MAAFtC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAtL,GAAA;MAAFvC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAArL,GAAA;MAAFxC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAApL,GAAA;MAAFzC,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAnL,GAAA;MAAF1C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAlL,IAAA;MAAF3C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAjL,IAAA;MAAF5C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAAhL,IAAA;MAAF7C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAA/K,IAAA;MAAF9C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAA9K,IAAA;MAAF/C,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAA7K,IAAA;MAAFhD,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAA5K,IAAA;MAAFjD,EAAE,CAAA8N,cAAA,CAAAD,QAAA,EAAA3K,IAAA;IAAA;IAAA,IAAAjC,EAAA;MAAA,IAAAiI,EAAA;MAAFlJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA6M,gBAAA,GAAA7E,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA8M,cAAA,GAAA9E,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA+M,iBAAA,GAAA/E,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAgN,mBAAA,GAAAhF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAiN,YAAA,GAAAjF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAkN,eAAA,GAAAlF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAmN,gBAAA,GAAAnF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAoN,aAAA,GAAApF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAqN,eAAA,GAAArF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAsN,eAAA,GAAAtF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAuN,yBAAA,GAAAvF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAwN,yBAAA,GAAAxF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAAyN,oBAAA,GAAAzF,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA0N,mBAAA,GAAA1F,EAAA,CAAAG,KAAA;MAAFrJ,EAAE,CAAAmJ,cAAA,CAAAD,EAAA,GAAFlJ,EAAE,CAAAoJ,WAAA,QAAAlI,GAAA,CAAA2N,wBAAA,GAAA3F,EAAA,CAAAG,KAAA;IAAA;EAAA;EAAAC,MAAA;IAAAgC,OAAA;IAAAD,mBAAA;IAAArH,MAAA;IAAAC,YAAA;IAAAC,SAAA;EAAA;EAAAgD,KAAA;EAAAC,IAAA;EAAAsC,MAAA;EAAA7H,QAAA,WAAAkN,+BAAA7N,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAFjB,EAAE,CAAAmB,cAAA,wBAsOigE,CAAC;MAtOpgEnB,EAAE,CAAA2J,UAAA,IAAAxG,oDAAA,gCAsOw/E,CAAC;MAtO3/EnD,EAAE,CAAAqB,YAAA,CAsOqiF,CAAC;IAAA;IAAA,IAAAJ,EAAA;MAtOxiFjB,EAAE,CAAA2B,SAAA,CAsO4kE,CAAC;MAtO/kE3B,EAAE,CAAAwB,UAAA,YAAAN,GAAA,CAAA4L,gBAsO4kE,CAAC,iBAAA5L,GAAA,CAAAmM,oBAA2B,CAAC;IAAA;EAAA;EAAAzD,YAAA,GAAud3D,0BAA0B,EAA4C6B,2BAA2B,EAAqKrH,EAAE,CAACsO,OAAO;EAAAxH,aAAA;AAAA,EAAoI;AACpkG;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvO8GxH,EAAE,CAAAyH,iBAAA,CAuOpB+C,qBAAqB,EAAc,CAAC;IACpHzD,IAAI,EAAE9G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEJ,aAAa,EAAErH,iBAAiB,CAAC0H,IAAI,CAAC;MAAA;MAChEhG,QAAQ,EAAE;IAA6jB,CAAC;EACtlB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEmF,IAAI,EAAE/G,EAAE,CAAC4G;IAAW,CAAC,EAAE;MAAEG,IAAI,EAAE/G,EAAE,CAAC0N;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEpC,OAAO,EAAE,CAAC;MAC3HvE,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAEkL,mBAAmB,EAAE,CAAC;MACtBtE,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE6D,MAAM,EAAE,CAAC;MACT+C,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE8D,YAAY,EAAE,CAAC;MACf8C,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE+D,SAAS,EAAE,CAAC;MACZ6C,IAAI,EAAE5G;IACV,CAAC,CAAC;IAAE4N,gBAAgB,EAAE,CAAC;MACnBhH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEhB,cAAc,EAAE,CAAC;MACjBjH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,gBAAgB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAEf,iBAAiB,EAAE,CAAC;MACpBlH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,mBAAmB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAChD,CAAC,CAAC;IAAEd,mBAAmB,EAAE,CAAC;MACtBnH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,qBAAqB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAClD,CAAC,CAAC;IAAEb,YAAY,EAAE,CAAC;MACfpH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,cAAc,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAEZ,eAAe,EAAE,CAAC;MAClBrH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEX,gBAAgB,EAAE,CAAC;MACnBtH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAEV,aAAa,EAAE,CAAC;MAChBvH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,eAAe,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAET,eAAe,EAAE,CAAC;MAClBxH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAER,eAAe,EAAE,CAAC;MAClBzH,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEP,yBAAyB,EAAE,CAAC;MAC5B1H,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,2BAA2B,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAAEN,yBAAyB,EAAE,CAAC;MAC5B3H,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,2BAA2B,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAAEL,oBAAoB,EAAE,CAAC;MACvB5H,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,sBAAsB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IACnD,CAAC,CAAC;IAAEJ,mBAAmB,EAAE,CAAC;MACtB7H,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,qBAAqB,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IAClD,CAAC,CAAC;IAAEH,wBAAwB,EAAE,CAAC;MAC3B9H,IAAI,EAAE1G,YAAY;MAClBqH,IAAI,EAAE,CAAC,0BAA0B,EAAE;QAAEsH,MAAM,EAAE;MAAK,CAAC;IACvD,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,kBAAkB,CAAC;AAEzBA,kBAAkB,CAACzI,IAAI,YAAA0I,2BAAAxI,iBAAA;EAAA,YAAAA,iBAAA,IAAyFuI,kBAAkB;AAAA,CAAkD;AACpLA,kBAAkB,CAACE,IAAI,kBAvSuFnP,EAAE,CAAAoP,gBAAA;EAAArI,IAAA,EAuSCkI;AAAkB,EAEjC;AAClGA,kBAAkB,CAACI,IAAI,kBA1SuFrP,EAAE,CAAAsP,gBAAA;EAAAC,OAAA,GA0S+B,CACnI7O,YAAY,CACf;AAAA,EAAI;AACb;EAAA,QAAA8G,SAAA,oBAAAA,SAAA,KA7S8GxH,EAAE,CAAAyH,iBAAA,CA6SpBwH,kBAAkB,EAAc,CAAC;IACjHlI,IAAI,EAAEzG,QAAQ;IACdoH,IAAI,EAAE,CAAC;MACC8H,YAAY,EAAE,CACVhF,qBAAqB,EACrBvE,0BAA0B,EAC1B6B,2BAA2B,CAC9B;MACDyH,OAAO,EAAE,CACL7O,YAAY,CACf;MACD+O,OAAO,EAAE,CACLjF,qBAAqB;IAE7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,qBAAqB,EAAEyE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}