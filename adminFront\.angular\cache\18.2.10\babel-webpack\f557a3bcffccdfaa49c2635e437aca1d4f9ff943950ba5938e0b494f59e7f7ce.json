{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n  detach() {\n    let host = this._attachedHost;\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector, componentFactoryResolver, projectableNodes) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n    this.projectableNodes = projectableNodes;\n  }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n  constructor( /** The embedded template that will be used to instantiate an embedded View in the host. */\n  templateRef, /** Reference to the ViewContainer into which the template will be stamped out. */\n  viewContainerRef, /** Contextual data to be passed in to the embedded view. */\n  context, /** The injector to use for the embedded view. */\n  injector) {\n    super();\n    this.templateRef = templateRef;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n    this.injector = injector;\n  }\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n  constructor() {\n    /** Whether this host has already been permanently disposed. */\n    this._isDisposed = false;\n    // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n    this.attachDomPortal = null;\n  }\n  /** Whether this host has an attached portal. */\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal);\n      // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n      this._attachedPortal = null;\n    }\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n    this._invokeDisposeFn();\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n      this._disposeFn = null;\n    }\n  }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _componentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor( /** Element into which the content is projected. */\n  outletElement, _componentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      // @breaking-change 10.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n      throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n    }\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef;\n    // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector, portal.projectableNodes || undefined);\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n      this._appRef.attachView(componentRef.hostView);\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (this._appRef.viewCount > 0) {\n          this._appRef.detachView(componentRef.hostView);\n        }\n        componentRef.destroy();\n      });\n    }\n    // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n    // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal;\n    // TODO(jelbourn): Return locals from view.\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n  constructor(templateRef, viewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n  static {\n    this.ɵfac = function CdkPortal_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortal)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortal,\n      selectors: [[\"\", \"cdkPortal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }], null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵTemplatePortalDirective_BaseFactory;\n      return function TemplatePortalDirective_Factory(__ngFactoryType__) {\n        return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(__ngFactoryType__ || TemplatePortalDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TemplatePortalDirective,\n      selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n      exportAs: [\"cdkPortal\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n  constructor(_componentFactoryResolver, _viewContainerRef,\n  /**\n   * @deprecated `_document` parameter to be made required.\n   * @breaking-change 9.0.0\n   */\n  _document) {\n    super();\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._viewContainerRef = _viewContainerRef;\n    /** Whether the portal component is initialized. */\n    this._isInitialized = false;\n    /** Emits when a portal is attached to the outlet. */\n    this.attached = new EventEmitter();\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      // @breaking-change 9.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n      const element = portal.element;\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      }\n      // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n      const anchorNode = this._document.createComment('dom-portal');\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n      this._getRootNode().appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n    this._document = _document;\n  }\n  /** Portal associated with the Portal outlet. */\n  get portal() {\n    return this._attachedPortal;\n  }\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n    if (this.hasAttached()) {\n      super.detach();\n    }\n    if (portal) {\n      super.attach(portal);\n    }\n    this._attachedPortal = portal || null;\n  }\n  /** Component or view reference that is attached to the portal. */\n  get attachedRef() {\n    return this._attachedRef;\n  }\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedRef = this._attachedPortal = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this);\n    // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector, portal.projectableNodes || undefined);\n    // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n      injector: portal.injector\n    });\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /** Gets the root node of the portal outlet. */\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement;\n    // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n  static {\n    this.ɵfac = function CdkPortalOutlet_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkPortalOutlet)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkPortalOutlet,\n      selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalOutlet\", \"portal\"]\n      },\n      outputs: {\n        attached: \"attached\"\n      },\n      exportAs: [\"cdkPortalOutlet\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    portal: [{\n      type: Input,\n      args: ['cdkPortalOutlet']\n    }],\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵPortalHostDirective_BaseFactory;\n      return function PortalHostDirective_Factory(__ngFactoryType__) {\n        return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(__ngFactoryType__ || PortalHostDirective);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: PortalHostDirective,\n      selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n      inputs: {\n        portal: [0, \"cdkPortalHost\", \"portal\"]\n      },\n      exportAs: [\"cdkPortalHost\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: [{\n        name: 'portal',\n        alias: 'cdkPortalHost'\n      }],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }],\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass PortalModule {\n  static {\n    this.ɵfac = function PortalModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PortalModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PortalModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n    return this._parentInjector.get(token, notFoundValue);\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };", "map": {"version": 3, "names": ["i0", "ElementRef", "Injector", "Directive", "EventEmitter", "Inject", "Input", "Output", "NgModule", "DOCUMENT", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "_attachedHost", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "constructor", "component", "viewContainerRef", "injector", "componentFactoryResolver", "projectableNodes", "TemplatePortal", "templateRef", "context", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_isDisposed", "attachDomPortal", "_attachedPortal", "portal", "attachComponentPortal", "attachTemplatePortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "_disposeFn", "BasePortalHost", "DomPortalOutlet", "outletElement", "_componentFactoryResolver", "_appRef", "_defaultInjector", "_document", "parentNode", "anchorNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resolver", "componentFactory", "resolveComponentFactory", "componentRef", "createComponent", "length", "destroy", "create", "NULL", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "index", "indexOf", "remove", "DomPortalHost", "CdkPortal", "ɵfac", "CdkPortal_Factory", "__ngFactoryType__", "ɵɵdirectiveInject", "TemplateRef", "ViewContainerRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "exportAs", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵsetClassMetadata", "args", "selector", "TemplatePortalDirective", "ɵTemplatePortalDirective_BaseFactory", "TemplatePortalDirective_Factory", "ɵɵgetInheritedFactory", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "CdkPortalOutlet", "_viewContainerRef", "_isInitialized", "attached", "_getRootNode", "attachedRef", "_attachedRef", "ngOnInit", "ngOnDestroy", "ref", "emit", "clear", "nodeType", "ELEMENT_NODE", "CdkPortalOutlet_Factory", "ComponentFactoryResolver", "inputs", "outputs", "decorators", "PortalHostDirective", "ɵPortalHostDirective_BaseFactory", "PortalHostDirective_Factory", "name", "alias", "PortalModule", "PortalModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "PortalInjector", "_parentInjector", "_customTokens", "get", "token", "notFoundValue", "value"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@angular/cdk/fesm2022/portal.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    constructor(component, viewContainerRef, injector, componentFactoryResolver, projectableNodes) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.componentFactoryResolver = componentFactoryResolver;\n        this.projectableNodes = projectableNodes;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    constructor(\n    /** The embedded template that will be used to instantiate an embedded View in the host. */\n    templateRef, \n    /** Reference to the ViewContainer into which the template will be stamped out. */\n    viewContainerRef, \n    /** Contextual data to be passed in to the embedded view. */\n    context, \n    /** The injector to use for the embedded view. */\n    injector) {\n        super();\n        this.templateRef = templateRef;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n        this.injector = injector;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    constructor() {\n        /** Whether this host has already been permanently disposed. */\n        this._isDisposed = false;\n        // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n        this.attachDomPortal = null;\n    }\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {\n}\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _componentFactoryResolver Used to resolve the component factory.\n     *   Only required when attaching component portals.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n     *   become a required parameter.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, _componentFactoryResolver, _appRef, _defaultInjector, \n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document) {\n        super();\n        this.outletElement = outletElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n        /**\n         * Attaches a DOM portal by transferring its content into the outlet.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 10.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            element.parentNode.insertBefore(anchorNode, element);\n            this.outletElement.appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                // We can't use `replaceWith` here because IE doesn't support it.\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        const resolver = (portal.componentFactoryResolver || this._componentFactoryResolver);\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n            throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n        }\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector, portal.projectableNodes || undefined);\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n            this._appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (this._appRef.viewCount > 0) {\n                    this._appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {\n}\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor(templateRef, viewContainerRef) {\n        super(templateRef, viewContainerRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkPortal, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkPortal, isStandalone: true, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }] });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: TemplatePortalDirective, isStandalone: true, selector: \"[cdk-portal], [portal]\", providers: [\n            {\n                provide: CdkPortal,\n                useExisting: TemplatePortalDirective,\n            },\n        ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                    standalone: true,\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    constructor(_componentFactoryResolver, _viewContainerRef, \n    /**\n     * @deprecated `_document` parameter to be made required.\n     * @breaking-change 9.0.0\n     */\n    _document) {\n        super();\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._viewContainerRef = _viewContainerRef;\n        /** Whether the portal component is initialized. */\n        this._isInitialized = false;\n        /** Emits when a portal is attached to the outlet. */\n        this.attached = new EventEmitter();\n        /**\n         * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 9.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            portal.setAttachedHost(this);\n            element.parentNode.insertBefore(anchorNode, element);\n            this._getRootNode().appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedRef = this._attachedPortal = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector, portal.projectableNodes || undefined);\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context, {\n            injector: portal.injector,\n        });\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkPortalOutlet, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: CdkPortalOutlet, isStandalone: true, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { portal: [{\n                type: Input,\n                args: ['cdkPortalOutlet']\n            }], attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", type: PortalHostDirective, isStandalone: true, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n            {\n                provide: CdkPortalOutlet,\n                useExisting: PortalHostDirective,\n            },\n        ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: [{ name: 'portal', alias: 'cdkPortalHost' }],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                    standalone: true,\n                }]\n        }] });\nclass PortalModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalModule, imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.0-next.2\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n    constructor(_parentInjector, _customTokens) {\n        this._parentInjector = _parentInjector;\n        this._customTokens = _customTokens;\n    }\n    get(token, notFoundValue) {\n        const value = this._customTokens.get(token);\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n        return this._parentInjector.get(token, notFoundValue);\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC9G,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,MAAMC,KAAK,CAAC,iCAAiC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC,MAAMD,KAAK,CAAC,oCAAoC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,SAASE,qCAAqCA,CAAA,EAAG;EAC7C,MAAMF,KAAK,CAAC,6CAA6C,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,SAASG,2BAA2BA,CAAA,EAAG;EACnC,MAAMH,KAAK,CAAC,+EAA+E,GACvF,wCAAwC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASI,0BAA0BA,CAAA,EAAG;EAClC,MAAMJ,KAAK,CAAC,sDAAsD,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAASK,0BAA0BA,CAAA,EAAG;EAClC,MAAML,KAAK,CAAC,8DAA8D,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA,MAAMM,MAAM,CAAC;EACT;EACAC,MAAMA,CAACC,IAAI,EAAE;IACT,IAAI,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAID,IAAI,IAAI,IAAI,EAAE;QACdJ,0BAA0B,CAAC,CAAC;MAChC;MACA,IAAII,IAAI,CAACE,WAAW,CAAC,CAAC,EAAE;QACpBT,+BAA+B,CAAC,CAAC;MACrC;IACJ;IACA,IAAI,CAACU,aAAa,GAAGH,IAAI;IACzB,OAAOA,IAAI,CAACD,MAAM,CAAC,IAAI,CAAC;EAC5B;EACA;EACAK,MAAMA,CAAA,EAAG;IACL,IAAIJ,IAAI,GAAG,IAAI,CAACG,aAAa;IAC7B,IAAIH,IAAI,IAAI,IAAI,EAAE;MACd,IAAI,CAACG,aAAa,GAAG,IAAI;MACzBH,IAAI,CAACI,MAAM,CAAC,CAAC;IACjB,CAAC,MACI,IAAI,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpDJ,0BAA0B,CAAC,CAAC;IAChC;EACJ;EACA;EACA,IAAIQ,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,aAAa,IAAI,IAAI;EACrC;EACA;AACJ;AACA;AACA;EACIG,eAAeA,CAACN,IAAI,EAAE;IAClB,IAAI,CAACG,aAAa,GAAGH,IAAI;EAC7B;AACJ;AACA;AACA;AACA;AACA,MAAMO,eAAe,SAAST,MAAM,CAAC;EACjCU,WAAWA,CAACC,SAAS,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,gBAAgB,EAAE;IAC3F,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;IACxD,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;AACJ;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAAShB,MAAM,CAAC;EAChCU,WAAWA,CAAA,CACX;EACAO,WAAW,EACX;EACAL,gBAAgB,EAChB;EACAM,OAAO,EACP;EACAL,QAAQ,EAAE;IACN,KAAK,CAAC,CAAC;IACP,IAAI,CAACI,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACL,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACM,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACL,QAAQ,GAAGA,QAAQ;EAC5B;EACA,IAAIM,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,WAAW,CAACG,UAAU;EACtC;EACA;AACJ;AACA;AACA;AACA;EACInB,MAAMA,CAACC,IAAI,EAAEgB,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IACjC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,OAAO,KAAK,CAACjB,MAAM,CAACC,IAAI,CAAC;EAC7B;EACAI,MAAMA,CAAA,EAAG;IACL,IAAI,CAACY,OAAO,GAAGG,SAAS;IACxB,OAAO,KAAK,CAACf,MAAM,CAAC,CAAC;EACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,SAAS,SAAStB,MAAM,CAAC;EAC3BU,WAAWA,CAACa,OAAO,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO,YAAYvC,UAAU,GAAGuC,OAAO,CAACC,aAAa,GAAGD,OAAO;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,CAAC;EACnBf,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACgB,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;EAC/B;EACA;EACAvB,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,IAAI,CAACwB,eAAe;EACjC;EACA;EACA3B,MAAMA,CAAC4B,MAAM,EAAE;IACX,IAAI,OAAO1B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC0B,MAAM,EAAE;QACTpC,oBAAoB,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACW,WAAW,CAAC,CAAC,EAAE;QACpBT,+BAA+B,CAAC,CAAC;MACrC;MACA,IAAI,IAAI,CAAC+B,WAAW,EAAE;QAClB9B,qCAAqC,CAAC,CAAC;MAC3C;IACJ;IACA,IAAIiC,MAAM,YAAYpB,eAAe,EAAE;MACnC,IAAI,CAACmB,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;IAC7C,CAAC,MACI,IAAIA,MAAM,YAAYb,cAAc,EAAE;MACvC,IAAI,CAACY,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACE,oBAAoB,CAACF,MAAM,CAAC;MACxC;IACJ,CAAC,MACI,IAAI,IAAI,CAACF,eAAe,IAAIE,MAAM,YAAYP,SAAS,EAAE;MAC1D,IAAI,CAACM,eAAe,GAAGC,MAAM;MAC7B,OAAO,IAAI,CAACF,eAAe,CAACE,MAAM,CAAC;IACvC;IACA,IAAI,OAAO1B,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CN,2BAA2B,CAAC,CAAC;IACjC;EACJ;EACA;EACAS,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACsB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACpB,eAAe,CAAC,IAAI,CAAC;MAC1C,IAAI,CAACoB,eAAe,GAAG,IAAI;IAC/B;IACA,IAAI,CAACI,gBAAgB,CAAC,CAAC;EAC3B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC7B,WAAW,CAAC,CAAC,EAAE;MACpB,IAAI,CAACE,MAAM,CAAC,CAAC;IACjB;IACA,IAAI,CAAC0B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACN,WAAW,GAAG,IAAI;EAC3B;EACA;EACAQ,YAAYA,CAACC,EAAE,EAAE;IACb,IAAI,CAACC,UAAU,GAAGD,EAAE;EACxB;EACAH,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACI,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC,CAAC;MACjB,IAAI,CAACA,UAAU,GAAG,IAAI;IAC1B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASZ,gBAAgB,CAAC;;AAG9C;AACA;AACA;AACA;AACA,MAAMa,eAAe,SAASb,gBAAgB,CAAC;EAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,WAAWA,CAAA,CACX;EACA6B,aAAa,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,gBAAgB;EACnE;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACf,eAAe,GAAIE,MAAM,IAAK;MAC/B;MACA;MACA,IAAI,CAAC,IAAI,CAACc,SAAS,KAAK,OAAOxC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACpE,MAAMT,KAAK,CAAC,kEAAkE,CAAC;MACnF;MACA,MAAM6B,OAAO,GAAGM,MAAM,CAACN,OAAO;MAC9B,IAAI,CAACA,OAAO,CAACqB,UAAU,KAAK,OAAOzC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMT,KAAK,CAAC,uDAAuD,CAAC;MACxE;MACA;MACA;MACA,MAAMmD,UAAU,GAAG,IAAI,CAACF,SAAS,CAACG,aAAa,CAAC,YAAY,CAAC;MAC7DvB,OAAO,CAACqB,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEtB,OAAO,CAAC;MACpD,IAAI,CAACgB,aAAa,CAACS,WAAW,CAACzB,OAAO,CAAC;MACvC,IAAI,CAACK,eAAe,GAAGC,MAAM;MAC7B,KAAK,CAACK,YAAY,CAAC,MAAM;QACrB;QACA,IAAIW,UAAU,CAACD,UAAU,EAAE;UACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAAC1B,OAAO,EAAEsB,UAAU,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIb,qBAAqBA,CAACD,MAAM,EAAE;IAC1B,MAAMqB,QAAQ,GAAIrB,MAAM,CAACf,wBAAwB,IAAI,IAAI,CAAC0B,yBAA0B;IACpF,IAAI,CAAC,OAAOrC,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC+C,QAAQ,EAAE;MAC9D,MAAMxD,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,MAAMyD,gBAAgB,GAAGD,QAAQ,CAACE,uBAAuB,CAACvB,MAAM,CAAClB,SAAS,CAAC;IAC3E,IAAI0C,YAAY;IAChB;IACA;IACA;IACA;IACA,IAAIxB,MAAM,CAACjB,gBAAgB,EAAE;MACzByC,YAAY,GAAGxB,MAAM,CAACjB,gBAAgB,CAAC0C,eAAe,CAACH,gBAAgB,EAAEtB,MAAM,CAACjB,gBAAgB,CAAC2C,MAAM,EAAE1B,MAAM,CAAChB,QAAQ,IAAIgB,MAAM,CAACjB,gBAAgB,CAACC,QAAQ,EAAEgB,MAAM,CAACd,gBAAgB,IAAIM,SAAS,CAAC;MACnM,IAAI,CAACa,YAAY,CAAC,MAAMmB,YAAY,CAACG,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,MACI;MACD,IAAI,CAAC,OAAOrD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC,IAAI,CAACsC,OAAO,EAAE;QAClE,MAAM/C,KAAK,CAAC,qEAAqE,CAAC;MACtF;MACA2D,YAAY,GAAGF,gBAAgB,CAACM,MAAM,CAAC5B,MAAM,CAAChB,QAAQ,IAAI,IAAI,CAAC6B,gBAAgB,IAAIzD,QAAQ,CAACyE,IAAI,CAAC;MACjG,IAAI,CAACjB,OAAO,CAACkB,UAAU,CAACN,YAAY,CAACO,QAAQ,CAAC;MAC9C,IAAI,CAAC1B,YAAY,CAAC,MAAM;QACpB;QACA;QACA,IAAI,IAAI,CAACO,OAAO,CAACoB,SAAS,GAAG,CAAC,EAAE;UAC5B,IAAI,CAACpB,OAAO,CAACqB,UAAU,CAACT,YAAY,CAACO,QAAQ,CAAC;QAClD;QACAP,YAAY,CAACG,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACA;IACA;IACA,IAAI,CAACjB,aAAa,CAACS,WAAW,CAAC,IAAI,CAACe,qBAAqB,CAACV,YAAY,CAAC,CAAC;IACxE,IAAI,CAACzB,eAAe,GAAGC,MAAM;IAC7B,OAAOwB,YAAY;EACvB;EACA;AACJ;AACA;AACA;AACA;EACItB,oBAAoBA,CAACF,MAAM,EAAE;IACzB,IAAImC,aAAa,GAAGnC,MAAM,CAACjB,gBAAgB;IAC3C,IAAIqD,OAAO,GAAGD,aAAa,CAACE,kBAAkB,CAACrC,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;MAC/EL,QAAQ,EAAEgB,MAAM,CAAChB;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACAoD,OAAO,CAACE,SAAS,CAACC,OAAO,CAACC,QAAQ,IAAI,IAAI,CAAC9B,aAAa,CAACS,WAAW,CAACqB,QAAQ,CAAC,CAAC;IAC/E;IACA;IACA;IACAJ,OAAO,CAACK,aAAa,CAAC,CAAC;IACvB,IAAI,CAACpC,YAAY,CAAC,MAAM;MACpB,IAAIqC,KAAK,GAAGP,aAAa,CAACQ,OAAO,CAACP,OAAO,CAAC;MAC1C,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;QACdP,aAAa,CAACS,MAAM,CAACF,KAAK,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC3C,eAAe,GAAGC,MAAM;IAC7B;IACA,OAAOoC,OAAO;EAClB;EACA;AACJ;AACA;EACIhC,OAAOA,CAAA,EAAG;IACN,KAAK,CAACA,OAAO,CAAC,CAAC;IACf,IAAI,CAACM,aAAa,CAACkC,MAAM,CAAC,CAAC;EAC/B;EACA;EACAV,qBAAqBA,CAACV,YAAY,EAAE;IAChC,OAAOA,YAAY,CAACO,QAAQ,CAACO,SAAS,CAAC,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMO,aAAa,SAASpC,eAAe,CAAC;;AAG5C;AACA;AACA;AACA;AACA,MAAMqC,SAAS,SAAS3D,cAAc,CAAC;EACnCN,WAAWA,CAACO,WAAW,EAAEL,gBAAgB,EAAE;IACvC,KAAK,CAACK,WAAW,EAAEL,gBAAgB,CAAC;EACxC;EACA;IAAS,IAAI,CAACgE,IAAI,YAAAC,kBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAA+FH,SAAS,EAAnB5F,EAAE,CAAAgG,iBAAA,CAAmChG,EAAE,CAACiG,WAAW,GAAnDjG,EAAE,CAAAgG,iBAAA,CAA8DhG,EAAE,CAACkG,gBAAgB;IAAA,CAA4C;EAAE;EACxO;IAAS,IAAI,CAACC,IAAI,kBADqFnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EACJT,SAAS;MAAAU,SAAA;MAAAC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADPzG,EAAE,CAAA0G,0BAAA;IAAA,EACmH;EAAE;AAClO;AACA;EAAA,QAAAtF,SAAA,oBAAAA,SAAA,KAH2GpB,EAAE,CAAA2G,iBAAA,CAGXf,SAAS,EAAc,CAAC;IAC9GS,IAAI,EAAElG,SAAS;IACfyG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aAAa;MACvBN,QAAQ,EAAE,WAAW;MACrBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAErG,EAAE,CAACiG;EAAY,CAAC,EAAE;IAAEI,IAAI,EAAErG,EAAE,CAACkG;EAAiB,CAAC,CAAC;AAAA;AAC3F;AACA;AACA;AACA;AACA,MAAMY,uBAAuB,SAASlB,SAAS,CAAC;EAC5C;IAAS,IAAI,CAACC,IAAI;MAAA,IAAAkB,oCAAA;MAAA,gBAAAC,gCAAAjB,iBAAA;QAAA,QAAAgB,oCAAA,KAAAA,oCAAA,GAhBqF/G,EAAE,CAAAiH,qBAAA,CAgBQH,uBAAuB,IAAAf,iBAAA,IAAvBe,uBAAuB;MAAA;IAAA,IAAqD;EAAE;EAC/L;IAAS,IAAI,CAACX,IAAI,kBAjBqFnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EAiBJS,uBAAuB;MAAAR,SAAA;MAAAC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAjBrBzG,EAAE,CAAAkH,kBAAA,CAiBwF,CACzL;QACIC,OAAO,EAAEvB,SAAS;QAClBwB,WAAW,EAAEN;MACjB,CAAC,CACJ,GAtBkG9G,EAAE,CAAA0G,0BAAA;IAAA,EAsBnC;EAAE;AAC5E;AACA;EAAA,QAAAtF,SAAA,oBAAAA,SAAA,KAxB2GpB,EAAE,CAAA2G,iBAAA,CAwBXG,uBAAuB,EAAc,CAAC;IAC5HT,IAAI,EAAElG,SAAS;IACfyG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCN,QAAQ,EAAE,WAAW;MACrBc,SAAS,EAAE,CACP;QACIF,OAAO,EAAEvB,SAAS;QAClBwB,WAAW,EAAEN;MACjB,CAAC,CACJ;MACDN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,eAAe,SAAS5E,gBAAgB,CAAC;EAC3Cf,WAAWA,CAAC8B,yBAAyB,EAAE8D,iBAAiB;EACxD;AACJ;AACA;AACA;EACI3D,SAAS,EAAE;IACP,KAAK,CAAC,CAAC;IACP,IAAI,CAACH,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAAC8D,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAIrH,YAAY,CAAC,CAAC;IAClC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACwC,eAAe,GAAIE,MAAM,IAAK;MAC/B;MACA;MACA,IAAI,CAAC,IAAI,CAACc,SAAS,KAAK,OAAOxC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACpE,MAAMT,KAAK,CAAC,kEAAkE,CAAC;MACnF;MACA,MAAM6B,OAAO,GAAGM,MAAM,CAACN,OAAO;MAC9B,IAAI,CAACA,OAAO,CAACqB,UAAU,KAAK,OAAOzC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACxE,MAAMT,KAAK,CAAC,uDAAuD,CAAC;MACxE;MACA;MACA;MACA,MAAMmD,UAAU,GAAG,IAAI,CAACF,SAAS,CAACG,aAAa,CAAC,YAAY,CAAC;MAC7DjB,MAAM,CAACrB,eAAe,CAAC,IAAI,CAAC;MAC5Be,OAAO,CAACqB,UAAU,CAACG,YAAY,CAACF,UAAU,EAAEtB,OAAO,CAAC;MACpD,IAAI,CAACkF,YAAY,CAAC,CAAC,CAACzD,WAAW,CAACzB,OAAO,CAAC;MACxC,IAAI,CAACK,eAAe,GAAGC,MAAM;MAC7B,KAAK,CAACK,YAAY,CAAC,MAAM;QACrB,IAAIW,UAAU,CAACD,UAAU,EAAE;UACvBC,UAAU,CAACD,UAAU,CAACK,YAAY,CAAC1B,OAAO,EAAEsB,UAAU,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACF,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA,IAAId,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACD,eAAe;EAC/B;EACA,IAAIC,MAAMA,CAACA,MAAM,EAAE;IACf;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACzB,WAAW,CAAC,CAAC,IAAI,CAACyB,MAAM,IAAI,CAAC,IAAI,CAAC0E,cAAc,EAAE;MACvD;IACJ;IACA,IAAI,IAAI,CAACnG,WAAW,CAAC,CAAC,EAAE;MACpB,KAAK,CAACE,MAAM,CAAC,CAAC;IAClB;IACA,IAAIuB,MAAM,EAAE;MACR,KAAK,CAAC5B,MAAM,CAAC4B,MAAM,CAAC;IACxB;IACA,IAAI,CAACD,eAAe,GAAGC,MAAM,IAAI,IAAI;EACzC;EACA;EACA,IAAI6E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,cAAc,GAAG,IAAI;EAC9B;EACAM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC5E,OAAO,CAAC,CAAC;IACf,IAAI,CAAC0E,YAAY,GAAG,IAAI,CAAC/E,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,qBAAqBA,CAACD,MAAM,EAAE;IAC1BA,MAAM,CAACrB,eAAe,CAAC,IAAI,CAAC;IAC5B;IACA;IACA,MAAMI,gBAAgB,GAAGiB,MAAM,CAACjB,gBAAgB,IAAI,IAAI,GAAGiB,MAAM,CAACjB,gBAAgB,GAAG,IAAI,CAAC0F,iBAAiB;IAC3G,MAAMpD,QAAQ,GAAGrB,MAAM,CAACf,wBAAwB,IAAI,IAAI,CAAC0B,yBAAyB;IAClF,MAAMW,gBAAgB,GAAGD,QAAQ,CAACE,uBAAuB,CAACvB,MAAM,CAAClB,SAAS,CAAC;IAC3E,MAAMmG,GAAG,GAAGlG,gBAAgB,CAAC0C,eAAe,CAACH,gBAAgB,EAAEvC,gBAAgB,CAAC2C,MAAM,EAAE1B,MAAM,CAAChB,QAAQ,IAAID,gBAAgB,CAACC,QAAQ,EAAEgB,MAAM,CAACd,gBAAgB,IAAIM,SAAS,CAAC;IAC3K;IACA;IACA;IACA,IAAIT,gBAAgB,KAAK,IAAI,CAAC0F,iBAAiB,EAAE;MAC7C,IAAI,CAACG,YAAY,CAAC,CAAC,CAACzD,WAAW,CAAC8D,GAAG,CAAClD,QAAQ,CAACO,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9D;IACA,KAAK,CAACjC,YAAY,CAAC,MAAM4E,GAAG,CAACtD,OAAO,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC5B,eAAe,GAAGC,MAAM;IAC7B,IAAI,CAAC8E,YAAY,GAAGG,GAAG;IACvB,IAAI,CAACN,QAAQ,CAACO,IAAI,CAACD,GAAG,CAAC;IACvB,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;EACI/E,oBAAoBA,CAACF,MAAM,EAAE;IACzBA,MAAM,CAACrB,eAAe,CAAC,IAAI,CAAC;IAC5B,MAAMyD,OAAO,GAAG,IAAI,CAACqC,iBAAiB,CAACpC,kBAAkB,CAACrC,MAAM,CAACZ,WAAW,EAAEY,MAAM,CAACX,OAAO,EAAE;MAC1FL,QAAQ,EAAEgB,MAAM,CAAChB;IACrB,CAAC,CAAC;IACF,KAAK,CAACqB,YAAY,CAAC,MAAM,IAAI,CAACoE,iBAAiB,CAACU,KAAK,CAAC,CAAC,CAAC;IACxD,IAAI,CAACpF,eAAe,GAAGC,MAAM;IAC7B,IAAI,CAAC8E,YAAY,GAAG1C,OAAO;IAC3B,IAAI,CAACuC,QAAQ,CAACO,IAAI,CAAC9C,OAAO,CAAC;IAC3B,OAAOA,OAAO;EAClB;EACA;EACAwC,YAAYA,CAAA,EAAG;IACX,MAAMjF,aAAa,GAAG,IAAI,CAAC8E,iBAAiB,CAAC/E,OAAO,CAACC,aAAa;IAClE;IACA;IACA,OAAQA,aAAa,CAACyF,QAAQ,KAAKzF,aAAa,CAAC0F,YAAY,GACvD1F,aAAa,GACbA,aAAa,CAACoB,UAAU;EAClC;EACA;IAAS,IAAI,CAACgC,IAAI,YAAAuC,wBAAArC,iBAAA;MAAA,YAAAA,iBAAA,IAA+FuB,eAAe,EA5KzBtH,EAAE,CAAAgG,iBAAA,CA4KyChG,EAAE,CAACqI,wBAAwB,GA5KtErI,EAAE,CAAAgG,iBAAA,CA4KiFhG,EAAE,CAACkG,gBAAgB,GA5KtGlG,EAAE,CAAAgG,iBAAA,CA4KiHvF,QAAQ;IAAA,CAA4C;EAAE;EAChR;IAAS,IAAI,CAAC0F,IAAI,kBA7KqFnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EA6KJiB,eAAe;MAAAhB,SAAA;MAAAgC,MAAA;QAAAxF,MAAA;MAAA;MAAAyF,OAAA;QAAAd,QAAA;MAAA;MAAAlB,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA7KbzG,EAAE,CAAA0G,0BAAA;IAAA,EA6K2N;EAAE;AAC1U;AACA;EAAA,QAAAtF,SAAA,oBAAAA,SAAA,KA/K2GpB,EAAE,CAAA2G,iBAAA,CA+KXW,eAAe,EAAc,CAAC;IACpHjB,IAAI,EAAElG,SAAS;IACfyG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BN,QAAQ,EAAE,iBAAiB;MAC3BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAErG,EAAE,CAACqI;EAAyB,CAAC,EAAE;IAAEhC,IAAI,EAAErG,EAAE,CAACkG;EAAiB,CAAC,EAAE;IAAEG,IAAI,EAAE/D,SAAS;IAAEkG,UAAU,EAAE,CAAC;MACrHnC,IAAI,EAAEhG,MAAM;MACZuG,IAAI,EAAE,CAACnG,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEqC,MAAM,EAAE,CAAC;MAClCuD,IAAI,EAAE/F,KAAK;MACXsG,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEa,QAAQ,EAAE,CAAC;MACXpB,IAAI,EAAE9F;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMkI,mBAAmB,SAASnB,eAAe,CAAC;EAC9C;IAAS,IAAI,CAACzB,IAAI;MAAA,IAAA6C,gCAAA;MAAA,gBAAAC,4BAAA5C,iBAAA;QAAA,QAAA2C,gCAAA,KAAAA,gCAAA,GApMqF1I,EAAE,CAAAiH,qBAAA,CAoMQwB,mBAAmB,IAAA1C,iBAAA,IAAnB0C,mBAAmB;MAAA;IAAA,IAAqD;EAAE;EAC3L;IAAS,IAAI,CAACtC,IAAI,kBArMqFnG,EAAE,CAAAoG,iBAAA;MAAAC,IAAA,EAqMJoC,mBAAmB;MAAAnC,SAAA;MAAAgC,MAAA;QAAAxF,MAAA;MAAA;MAAAyD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GArMjBzG,EAAE,CAAAkH,kBAAA,CAqM4I,CAC7O;QACIC,OAAO,EAAEG,eAAe;QACxBF,WAAW,EAAEqB;MACjB,CAAC,CACJ,GA1MkGzI,EAAE,CAAA0G,0BAAA;IAAA,EA0M/B;EAAE;AAChF;AACA;EAAA,QAAAtF,SAAA,oBAAAA,SAAA,KA5M2GpB,EAAE,CAAA2G,iBAAA,CA4MX8B,mBAAmB,EAAc,CAAC;IACxHpC,IAAI,EAAElG,SAAS;IACfyG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCN,QAAQ,EAAE,eAAe;MACzB+B,MAAM,EAAE,CAAC;QAAEM,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAgB,CAAC,CAAC;MACpDxB,SAAS,EAAE,CACP;QACIF,OAAO,EAAEG,eAAe;QACxBF,WAAW,EAAEqB;MACjB,CAAC,CACJ;MACDjC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMsC,YAAY,CAAC;EACf;IAAS,IAAI,CAACjD,IAAI,YAAAkD,qBAAAhD,iBAAA;MAAA,YAAAA,iBAAA,IAA+F+C,YAAY;IAAA,CAAkD;EAAE;EACjL;IAAS,IAAI,CAACE,IAAI,kBA7NqFhJ,EAAE,CAAAiJ,gBAAA;MAAA5C,IAAA,EA6NSyC;IAAY,EAA6K;EAAE;EAC7S;IAAS,IAAI,CAACI,IAAI,kBA9NqFlJ,EAAE,CAAAmJ,gBAAA,IA8NwB;EAAE;AACvI;AACA;EAAA,QAAA/H,SAAA,oBAAAA,SAAA,KAhO2GpB,EAAE,CAAA2G,iBAAA,CAgOXmC,YAAY,EAAc,CAAC;IACjHzC,IAAI,EAAE7F,QAAQ;IACdoG,IAAI,EAAE,CAAC;MACCwC,OAAO,EAAE,CAACxD,SAAS,EAAE0B,eAAe,EAAER,uBAAuB,EAAE2B,mBAAmB,CAAC;MACnFY,OAAO,EAAE,CAACzD,SAAS,EAAE0B,eAAe,EAAER,uBAAuB,EAAE2B,mBAAmB;IACtF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,cAAc,CAAC;EACjB3H,WAAWA,CAAC4H,eAAe,EAAEC,aAAa,EAAE;IACxC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAC,GAAGA,CAACC,KAAK,EAAEC,aAAa,EAAE;IACtB,MAAMC,KAAK,GAAG,IAAI,CAACJ,aAAa,CAACC,GAAG,CAACC,KAAK,CAAC;IAC3C,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,OAAO,IAAI,CAACL,eAAe,CAACE,GAAG,CAACC,KAAK,EAAEC,aAAa,CAAC;EACzD;AACJ;;AAEA;AACA;AACA;;AAEA,SAASrG,cAAc,EAAEZ,gBAAgB,EAAEkD,SAAS,EAAE0B,eAAe,EAAE5F,eAAe,EAAEa,SAAS,EAAEoD,aAAa,EAAEpC,eAAe,EAAEtC,MAAM,EAAEwH,mBAAmB,EAAEa,cAAc,EAAER,YAAY,EAAE7G,cAAc,EAAE6E,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}