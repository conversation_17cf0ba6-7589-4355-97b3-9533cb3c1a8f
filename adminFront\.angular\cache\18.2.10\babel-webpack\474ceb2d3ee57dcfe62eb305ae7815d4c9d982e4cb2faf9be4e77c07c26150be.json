{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs/operators';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = (a0, a1) => ({\n  \"bg-green-500\": a0,\n  \"bg-red-500\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"pr-7\": a0,\n  \"pl-7\": a1\n});\nconst _c2 = (a0, a1) => ({\n  \"translate-x-14\": a0,\n  \"translate-x-0\": a1\n});\nfunction ContentManagementSalesAccountComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_15_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(ctx_r2.listFormItem.CIsLock));\n    });\n    i0.ɵɵelementStart(2, \"span\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"span\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_ng_container_15_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(ctx_r2.listFormItem.CIsLock));\n    });\n    i0.ɵɵelement(6, \"i\", 25);\n    i0.ɵɵtext(7, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, !ctx_r2.listFormItem.CIsLock, ctx_r2.listFormItem.CIsLock));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c1, ctx_r2.listFormItem.CIsLock, !ctx_r2.listFormItem.CIsLock));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", !ctx_r2.listFormItem.CIsLock ? \"\\u89E3\\u9664\" : \"\\u9396\\u5B9A\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c2, ctx_r2.listFormItem.CIsLock, !ctx_r2.listFormItem.CIsLock));\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u770B\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ContentManagementSalesAccountComponent_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵelement(1, \"i\", 29);\n    i0.ɵɵtext(2, \" \\u65B0\\u589E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_29_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.$implicit;\n    const ix_r9 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r9 > 0 ? \"\\u3001\" : \"\", \" \", i_r8.CHousehold, \" \");\n  }\n}\nfunction ContentManagementSalesAccountComponent_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementSalesAccountComponent_tr_29_span_4_Template, 2, 2, \"span\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r10.tblFormItemHouseholds);\n  }\n}\nexport class ContentManagementSalesAccountComponent extends BaseComponent {\n  toggleSwitch(CIsLock) {\n    if (CIsLock) {\n      this.unLock();\n    } else {\n      this.onLock();\n    }\n  }\n  constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.router = router;\n    this.message = message;\n    this._buildCaseService = _buildCaseService;\n    this._formItemService = _formItemService;\n    this._eventService = _eventService;\n    this.tempBuildCaseID = -1;\n    this.selectedBuilding = null;\n    this.buildingSelectedOptions = [{\n      value: '',\n      label: '全部'\n    }];\n    this.formItems = [];\n    this.listFormItem = null;\n    this.pageSize = 20;\n    this.cBuildCaseSelected = null;\n    this.userBuildCaseOptions = [];\n    this.typeContentManagementSalesAccount = {\n      CFormType: EnumHouseType.銷售戶\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n        this.tempBuildCaseID = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.cBuildCaseSelected = null;\n    this.getUserBuildCase();\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            CBuildCaseName: res.CBuildCaseName,\n            cID: res.cID\n          };\n        });\n        if (this.tempBuildCaseID != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n          this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n        } else {\n          this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n        }\n        if (this.cBuildCaseSelected.cID) {\n          this.getListFormItem();\n        }\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CIsPaging: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.formItems = res.Entries.formItems;\n        this.listFormItem = res.Entries;\n        this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    this.getListFormItem();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListFormItem();\n  }\n  onLock() {\n    this._formItemService.apiFormItemLockFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        // this.message.showErrorMSG(res.Message!);\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\n      }\n      this.getListFormItem();\n    });\n  }\n  unLock() {\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\n      body: {\n        CBuildCaseID: this.cBuildCaseSelected.cID,\n        CFormId: this.listFormItem.CFormId\n      }\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n      this.getListFormItem();\n    });\n  }\n  navidateDetai() {\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\n  }\n  static {\n    this.ɵfac = function ContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 32,\n      vars: 11,\n      consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-success mx-1\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"relative\", \"inline-block\", \"w-24\", \"h-10\", \"rounded-full\", \"cursor-pointer\", \"transition-colors\", \"duration-300\", \"mx-2\", 3, \"click\", \"ngClass\"], [1, \"absolute\", \"top-0\", \"left-0\", \"right-0\", \"bottom-0\", \"flex\", \"items-center\", \"justify-center\", \"text-white\", \"text-lg\", 3, \"ngClass\"], [1, \"absolute\", \"left-0\", \"top-0\", \"h-10\", \"w-10\", \"bg-white\", \"rounded-full\", \"shadow-md\", \"transform\", \"transition-transform\", \"duration-300\", 3, \"ngClass\"], [1, \"btn\", \"btn-danger\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-lock\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"mx-1\", 3, \"click\"], [1, \"fas\", \"fa-plus\"]],\n      template: function ContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n          i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener() {\n            return ctx.onSelectionChangeBuildCase();\n          });\n          i0.ɵɵtemplate(12, ContentManagementSalesAccountComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 8);\n          i0.ɵɵtemplate(15, ContentManagementSalesAccountComponent_ng_container_15_Template, 8, 13, \"ng-container\", 9)(16, ContentManagementSalesAccountComponent_button_16_Template, 2, 0, \"button\", 10)(17, ContentManagementSalesAccountComponent_button_17_Template, 2, 0, \"button\", 11)(18, ContentManagementSalesAccountComponent_button_18_Template, 2, 0, \"button\", 10)(19, ContentManagementSalesAccountComponent_button_19_Template, 3, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"table\", 14)(22, \"thead\")(23, \"tr\", 15)(24, \"th\", 16);\n          i0.ɵɵtext(25, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 16);\n          i0.ɵɵtext(27, \"\\u9069\\u7528\\u6236\\u5225 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"tbody\");\n          i0.ɵɵtemplate(29, ContentManagementSalesAccountComponent_tr_29_Template, 5, 2, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"nb-card-footer\", 18)(31, \"ngb-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"pageChange\", function ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_31_listener($event) {\n            return ctx.pageChanged($event);\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.isUpdate && !ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.formItems && ctx.isUpdate && !ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.formItems && ctx.isUpdate && ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.formItems && ctx.isCreate && !ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.CIsLock);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjb250ZW50LW1hbmFnZW1lbnQtc2FsZXMtYWNjb3VudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQvY29udGVudC1tYW5hZ2VtZW50LXNhbGVzLWFjY291bnQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLG9NQUFvTSIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "BaseComponent", "EEvent", "EnumHouseType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵelementContainerStart", "ɵɵlistener", "ContentManagementSalesAccountComponent_ng_container_15_Template_div_click_1_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "toggleSwitch", "listFormItem", "CIsLock", "ɵɵelement", "ContentManagementSalesAccountComponent_ng_container_15_Template_button_click_5_listener", "ɵɵpureFunction2", "_c0", "_c1", "_c2", "ContentManagementSalesAccountComponent_button_16_Template_button_click_0_listener", "_r4", "navid<PERSON><PERSON><PERSON><PERSON>", "ContentManagementSalesAccountComponent_button_17_Template_button_click_0_listener", "_r5", "ContentManagementSalesAccountComponent_button_18_Template_button_click_0_listener", "_r6", "ContentManagementSalesAccountComponent_button_19_Template_button_click_0_listener", "_r7", "ɵɵtextInterpolate2", "ix_r9", "i_r8", "CHousehold", "ɵɵtemplate", "ContentManagementSalesAccountComponent_tr_29_span_4_Template", "ɵɵtextInterpolate", "item_r10", "CItemName", "tblFormItemHouseholds", "ContentManagementSalesAccountComponent", "unLock", "onLock", "constructor", "_allow", "router", "message", "_buildCaseService", "_formItemService", "_eventService", "tempBuildCaseID", "selectedBuilding", "buildingSelectedOptions", "value", "label", "formItems", "pageSize", "cBuildCaseSelected", "userBuildCaseOptions", "typeContentManagementSalesAccount", "CFormType", "銷售戶", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "getUserBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "body", "CBuildCaseId", "buildCaseId", "Entries", "StatusCode", "map", "cID", "index", "findIndex", "x", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "PageIndex", "pageIndex", "PageSize", "CIsPaging", "totalRecords", "TotalItems", "onSelectionChangeBuildCase", "pageChanged", "newPage", "apiFormItemLockFormItemPost$Json", "CFormId", "showSucessMSG", "showErrorMSG", "apiFormItemUnlockFormItemPost$Json", "CBuildCaseID", "Message", "navigate", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "Router", "i3", "MessageService", "i4", "BuildCaseService", "FormItemService", "i5", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContentManagementSalesAccountComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ContentManagementSalesAccountComponent_Template_nb_select_ngModelChange_11_listener", "$event", "ɵɵtwoWayBindingSet", "ContentManagementSalesAccountComponent_Template_nb_select_selectedChange_11_listener", "ContentManagementSalesAccountComponent_nb_option_12_Template", "ContentManagementSalesAccountComponent_ng_container_15_Template", "ContentManagementSalesAccountComponent_button_16_Template", "ContentManagementSalesAccountComponent_button_17_Template", "ContentManagementSalesAccountComponent_button_18_Template", "ContentManagementSalesAccountComponent_button_19_Template", "ContentManagementSalesAccountComponent_tr_29_Template", "ContentManagementSalesAccountComponent_Template_ngb_pagination_pageChange_31_listener", "ɵɵtwoWayProperty", "isUpdate", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "NgControlStatus", "NgModel", "i8", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i9", "NgbPagination", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { tap } from 'rxjs/operators';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, FormItemService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { GetListFormItemRes, BuildCaseGetListReponse } from 'src/services/api/models';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n// 建案選項介面  \r\nexport interface BuildCaseOption {\r\n  CBuildCaseName: string | null | undefined;\r\n  cID: number | undefined;\r\n}\r\n\r\n// 戶型選項介面\r\nexport interface HouseholdOption {\r\n  CHousehold: string;\r\n}\r\n\r\n// 表單項目介面\r\nexport interface FormItemDisplay {\r\n  CItemName: string;\r\n  tblFormItemHouseholds: HouseholdOption[];\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-content-management-sales-account',\r\n  templateUrl: './content-management-sales-account.component.html',\r\n  styleUrls: ['./content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule,],\r\n})\r\nexport class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {\r\n\r\n  toggleSwitch(CIsLock: boolean): void {\r\n    if(CIsLock) {\r\n      this.unLock()\r\n    } else {\r\n      this.onLock()\r\n    }\r\n  }\r\n\r\n  tempBuildCaseID: number = -1\r\n  selectedBuilding: BuildCaseOption | null = null;\r\n  buildingSelectedOptions: selectItem[] = [{ value: '', label: '全部' }];\r\n\r\n  formItems: FormItemDisplay[] = [];\r\n  listFormItem: GetListFormItemRes | null = null;\r\n  override pageSize = 20;\r\n\r\n  buildCaseId!: number;\r\n  cBuildCaseSelected: BuildCaseOption | null = null;\r\n  userBuildCaseOptions: BuildCaseOption[] = [];\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private router: Router,\r\n    private message: MessageService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _formItemService: FormItemService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    super(_allow);\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {\r\n          this.tempBuildCaseID = res.payload\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.cBuildCaseSelected = null;\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              CBuildCaseName: res.CBuildCaseName,\r\n              cID: res.cID\r\n            };\r\n          });\r\n\r\n          if (this.tempBuildCaseID != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index]\r\n          } else {\r\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\r\n          }\r\n          if (this.cBuildCaseSelected.cID) {\r\n            this.getListFormItem();\r\n          }\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: EnumHouseType.銷售戶,\r\n  }\r\n\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CIsPaging: true\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.formItems = res.Entries.formItems;\r\n          this.listFormItem = res.Entries;\r\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  onSelectionChangeBuildCase() {\r\n    this.getListFormItem();\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListFormItem();\r\n  }\r\n\r\n  onLock() {\r\n    this._formItemService.apiFormItemLockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        // this.message.showErrorMSG(res.Message!);\r\n        this.message.showErrorMSG(\"無資料，不可鎖定\");\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  unLock() {\r\n    this._formItemService.apiFormItemUnlockFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.cBuildCaseSelected.cID,\r\n        CFormId: this.listFormItem.CFormId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!);\r\n      }\r\n      this.getListFormItem();\r\n    });\r\n  }\r\n\r\n  navidateDetai() {\r\n    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);\r\n  }\r\n}\r\n", "<!-- 3.6  3.7 = 1, 3.6 = 2-->\r\n<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">\r\n      您可將於建材管理及方案管理設定好的方案及材料，於此組合成選樣內容，並可設定各方案、材料可選擇之戶型。\r\n    </h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"cBuildCaseSelected\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n\r\n          <ng-container *ngIf=\"listFormItem && isUpdate && !listFormItem.CIsLock\">\r\n            <div class=\"relative inline-block w-24 h-10 rounded-full cursor-pointer transition-colors duration-300 mx-2\"\r\n              [ngClass]=\"{'bg-green-500': !listFormItem.CIsLock, 'bg-red-500': listFormItem.CIsLock}\"\r\n              (click)=\"toggleSwitch(listFormItem.CIsLock)\">\r\n              <span class=\"absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center text-white text-lg\"\r\n                [ngClass]=\"{'pr-7': listFormItem.CIsLock, 'pl-7': !listFormItem.CIsLock}\">\r\n                {{ !listFormItem.CIsLock ? '解除' : '鎖定' }}\r\n              </span>\r\n              <span\r\n                class=\"absolute left-0 top-0 h-10 w-10 bg-white rounded-full shadow-md transform transition-transform duration-300\"\r\n                [ngClass]=\"{'translate-x-14': listFormItem.CIsLock, 'translate-x-0': !listFormItem.CIsLock}\"></span>\r\n            </div>\r\n\r\n            <!-- 新增鎖定按鈕，只在未鎖定時顯示 -->\r\n            <button class=\"btn btn-danger mx-1\" (click)=\"toggleSwitch(listFormItem.CIsLock)\">\r\n              <i class=\"fas fa-lock\"></i> 鎖定\r\n            </button>\r\n          </ng-container>\r\n\r\n          <!-- 編輯按鈕，只在未鎖定時顯示 -->\r\n          <button class=\"btn btn-info\"\r\n            *ngIf=\"listFormItem && listFormItem.formItems && isUpdate && !listFormItem.CIsLock\"\r\n            (click)=\"navidateDetai()\">\r\n            編輯內容\r\n          </button>\r\n\r\n          <!-- 查看按鈕，只在已鎖定時顯示 -->\r\n          <button class=\"btn btn-secondary\"\r\n            *ngIf=\"listFormItem && listFormItem.formItems && isUpdate && listFormItem.CIsLock\"\r\n            (click)=\"navidateDetai()\">\r\n            查看內容\r\n          </button>\r\n\r\n          <!-- 原有的新增按鈕，修改為只有在未鎖定時顯示 -->\r\n          <button class=\"btn btn-info\"\r\n            *ngIf=\"listFormItem && !listFormItem.formItems && isCreate && !listFormItem.CIsLock\"\r\n            (click)=\"navidateDetai()\">\r\n            新增\r\n          </button>\r\n\r\n          <!-- 新增按鈕，只有在已鎖定時顯示 -->\r\n          <button class=\"btn btn-success mx-1\" *ngIf=\"listFormItem && listFormItem.CIsLock\" (click)=\"navidateDetai()\">\r\n            <i class=\"fas fa-plus\"></i> 新增\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">方案名稱/建材位置</th>\r\n            <th scope=\"col\" class=\"col-1\">適用戶別 </th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of formItems ; let i = index\">\r\n            <td>{{ item.CItemName}}</td>\r\n            <td>\r\n              <span *ngFor=\"let i of item.tblFormItemHouseholds ; let ix = index\">\r\n                {{ix > 0 ? '、' :''}} {{i.CHousehold}}\r\n              </span>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,GAAG,QAAQ,gBAAgB;AAKpC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAA+BC,MAAM,QAAQ,uCAAuC;AACpF,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;ICIrDC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;;IAOFR,EAAA,CAAAS,uBAAA,GAAwE;IACtET,EAAA,CAAAC,cAAA,cAE+C;IAA7CD,EAAA,CAAAU,UAAA,mBAAAC,qFAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAH,MAAA,CAAAI,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC;IAC5CnB,EAAA,CAAAC,cAAA,eAC4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAoB,SAAA,eAEsG;IACxGpB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,iBAAiF;IAA7CD,EAAA,CAAAU,UAAA,mBAAAW,wFAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAH,MAAA,CAAAI,YAAA,CAAAC,OAAA,CAAkC;IAAA,EAAC;IAC9EnB,EAAA,CAAAoB,SAAA,YAA2B;IAACpB,EAAA,CAAAE,MAAA,qBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdPH,EAAA,CAAAM,SAAA,EAAuF;IAAvFN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsB,eAAA,IAAAC,GAAA,GAAAT,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAuF;IAGrFnB,EAAA,CAAAM,SAAA,EAAyE;IAAzEN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsB,eAAA,IAAAE,GAAA,EAAAV,MAAA,CAAAI,YAAA,CAAAC,OAAA,GAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAAyE;IACzEnB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,OAAAO,MAAA,CAAAI,YAAA,CAAAC,OAAA,wCACF;IAGEnB,EAAA,CAAAM,SAAA,EAA4F;IAA5FN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAsB,eAAA,KAAAG,GAAA,EAAAX,MAAA,CAAAI,YAAA,CAAAC,OAAA,GAAAL,MAAA,CAAAI,YAAA,CAAAC,OAAA,EAA4F;;;;;;IAUlGnB,EAAA,CAAAC,cAAA,iBAE4B;IAA1BD,EAAA,CAAAU,UAAA,mBAAAgB,kFAAA;MAAA1B,EAAA,CAAAY,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACzB5B,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAE4B;IAA1BD,EAAA,CAAAU,UAAA,mBAAAmB,kFAAA;MAAA7B,EAAA,CAAAY,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACzB5B,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAE4B;IAA1BD,EAAA,CAAAU,UAAA,mBAAAqB,kFAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACzB5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAA4G;IAA1BD,EAAA,CAAAU,UAAA,mBAAAuB,kFAAA;MAAAjC,EAAA,CAAAY,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IACzG5B,EAAA,CAAAoB,SAAA,YAA2B;IAACpB,EAAA,CAAAE,MAAA,qBAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAiBLH,EAAA,CAAAC,cAAA,WAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAmC,kBAAA,MAAAC,KAAA,2BAAAC,IAAA,CAAAC,UAAA,MACF;;;;;IAJFtC,EADF,CAAAC,cAAA,SAAmD,SAC7C;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAuC,UAAA,IAAAC,4DAAA,mBAAoE;IAIxExC,EADE,CAAAG,YAAA,EAAK,EACF;;;;IANCH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyC,iBAAA,CAAAC,QAAA,CAAAC,SAAA,CAAmB;IAED3C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAsC,QAAA,CAAAE,qBAAA,CAAgC;;;ADzClE,OAAM,MAAOC,sCAAuC,SAAQhD,aAAa;EAEvEoB,YAAYA,CAACE,OAAgB;IAC3B,IAAGA,OAAO,EAAE;MACV,IAAI,CAAC2B,MAAM,EAAE;IACf,CAAC,MAAM;MACL,IAAI,CAACC,MAAM,EAAE;IACf;EACF;EAcAC,YACUC,MAAmB,EACnBC,MAAc,EACdC,OAAuB,EACvBC,iBAAmC,EACnCC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACL,MAAM,CAAC;IAPL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAlBvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IAC5B,KAAAC,gBAAgB,GAA2B,IAAI;IAC/C,KAAAC,uBAAuB,GAAiB,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IAEpE,KAAAC,SAAS,GAAsB,EAAE;IACjC,KAAA1C,YAAY,GAA8B,IAAI;IACrC,KAAA2C,QAAQ,GAAG,EAAE;IAGtB,KAAAC,kBAAkB,GAA2B,IAAI;IACjD,KAAAC,oBAAoB,GAAsB,EAAE;IAqD5C,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAElE,aAAa,CAACmE;KAC1B;IA5CC,IAAI,CAACZ,aAAa,CAACa,OAAO,EAAE,CAACC,IAAI,CAC/BzE,GAAG,CAAE0E,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,kDAA4B,CAAC,CAACD,GAAG,CAACE,OAAO,EAAE;QACvD,IAAI,CAAChB,eAAe,GAAGc,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAESC,QAAQA,CAAA;IACf,IAAI,CAACX,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACY,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACtB,iBAAiB,CAACuB,qCAAqC,CAAC;MAC3DC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACC;;KAEtB,CAAC,CAACV,IAAI,CACLzE,GAAG,CAAC0E,GAAG,IAAG;MACR,IAAIA,GAAG,CAACU,OAAO,IAAIV,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACjB,oBAAoB,GAAGM,GAAG,CAACU,OAAO,CAACE,GAAG,CAACZ,GAAG,IAAG;UAChD,OAAO;YACL7D,cAAc,EAAE6D,GAAG,CAAC7D,cAAc;YAClC0E,GAAG,EAAEb,GAAG,CAACa;WACV;QACH,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC3B,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAI4B,KAAK,GAAG,IAAI,CAACpB,oBAAoB,CAACqB,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAC3B,eAAe,CAAC;UAC1F,IAAI,CAACO,kBAAkB,GAAG,IAAI,CAACC,oBAAoB,CAACoB,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACrB,kBAAkB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;QACxD;QACA,IAAI,IAAI,CAACD,kBAAkB,CAACoB,GAAG,EAAE;UAC/B,IAAI,CAACI,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACd,SAAS,EAAE;EACf;EAMAc,eAAeA,CAAA;IACb,IAAI,CAACjC,gBAAgB,CAACkC,mCAAmC,CAAC;MACxDX,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACf,kBAAkB,CAACoB,GAAG;QACzCjB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DuB,SAAS,EAAE,IAAI,CAACC,SAAS;QACzBC,QAAQ,EAAE,IAAI,CAAC7B,QAAQ;QACvB8B,SAAS,EAAE;;KAEd,CAAC,CAACvB,IAAI,CACLzE,GAAG,CAAC0E,GAAG,IAAG;MACR,IAAIA,GAAG,CAACU,OAAO,IAAIV,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACpB,SAAS,GAAGS,GAAG,CAACU,OAAO,CAACnB,SAAS;QACtC,IAAI,CAAC1C,YAAY,GAAGmD,GAAG,CAACU,OAAO;QAC/B,IAAI,CAACa,YAAY,GAAGvB,GAAG,CAACwB,UAAU,GAAGxB,GAAG,CAACwB,UAAU,GAAG,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACrB,SAAS,EAAE;EACf;EAEAsB,0BAA0BA,CAAA;IACxB,IAAI,CAACR,eAAe,EAAE;EACxB;EAEAS,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACP,SAAS,GAAGO,OAAO;IACxB,IAAI,CAACV,eAAe,EAAE;EACxB;EAEAvC,MAAMA,CAAA;IACJ,IAAI,CAACM,gBAAgB,CAAC4C,gCAAgC,CAAC;MACrDrB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACf,kBAAkB,CAACoB,GAAG;QACzCgB,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF;;KAE9B,CAAC,CAAC1B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC7B,OAAO,CAACgD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL;QACA,IAAI,CAAChD,OAAO,CAACiD,YAAY,CAAC,UAAU,CAAC;MACvC;MACA,IAAI,CAACd,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEAxC,MAAMA,CAAA;IACJ,IAAI,CAACO,gBAAgB,CAACgD,kCAAkC,CAAC;MACvDzB,IAAI,EAAE;QACJ0B,YAAY,EAAE,IAAI,CAACxC,kBAAkB,CAACoB,GAAG;QACzCgB,OAAO,EAAE,IAAI,CAAChF,YAAY,CAACgF;;KAE9B,CAAC,CAAC1B,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACW,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC7B,OAAO,CAACgD,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAChD,OAAO,CAACiD,YAAY,CAAC/B,GAAG,CAACkC,OAAQ,CAAC;MACzC;MACA,IAAI,CAACjB,eAAe,EAAE;IACxB,CAAC,CAAC;EACJ;EAEA1D,aAAaA,CAAA;IACX,IAAI,CAACsB,MAAM,CAACsD,QAAQ,CAAC,CAAC,0CAA0C,IAAI,CAAC1C,kBAAkB,CAACoB,GAAG,EAAE,CAAC,CAAC;EACjG;;;uCA9IWrC,sCAAsC,EAAA7C,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3G,EAAA,CAAAyG,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA7G,EAAA,CAAAyG,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA/G,EAAA,CAAAyG,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAjH,EAAA,CAAAyG,iBAAA,CAAAO,EAAA,CAAAE,eAAA,GAAAlH,EAAA,CAAAyG,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtCvE,sCAAsC;MAAAwE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvH,EAAA,CAAAwH,0BAAA,EAAAxH,EAAA,CAAAyH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzCjD/H,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UACnCD,EAAA,CAAAE,MAAA,qTACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAICH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAAiI,gBAAA,2BAAAC,oFAAAC,MAAA;YAAAnI,EAAA,CAAAoI,kBAAA,CAAAJ,GAAA,CAAAlE,kBAAA,EAAAqE,MAAA,MAAAH,GAAA,CAAAlE,kBAAA,GAAAqE,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgC;UAC1DnI,EAAA,CAAAU,UAAA,4BAAA2H,qFAAA;YAAA,OAAkBL,GAAA,CAAAlC,0BAAA,EAA4B;UAAA,EAAC;UAC/C9F,EAAA,CAAAuC,UAAA,KAAA+F,4DAAA,uBAAoE;UAK1EtI,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,cAC2B;UA2C7CD,EAzCA,CAAAuC,UAAA,KAAAgG,+DAAA,2BAAwE,KAAAC,yDAAA,qBAsB5C,KAAAC,yDAAA,qBAOA,KAAAC,yDAAA,qBAOA,KAAAC,yDAAA,qBAKgF;UAKlH3I,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC4C,aACpE,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,yDAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAK;UAEvCF,EAFuC,CAAAG,YAAA,EAAK,EACrC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuC,UAAA,KAAAqG,qDAAA,iBAAmD;UAW3D5I,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAiI,gBAAA,wBAAAY,sFAAAV,MAAA;YAAAnI,EAAA,CAAAoI,kBAAA,CAAAJ,GAAA,CAAAvC,SAAA,EAAA0C,MAAA,MAAAH,GAAA,CAAAvC,SAAA,GAAA0C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAClCnI,EAAA,CAAAU,UAAA,wBAAAmI,sFAAAV,MAAA;YAAA,OAAcH,GAAA,CAAAjC,WAAA,CAAAoC,MAAA,CAAmB;UAAA,EAAC;UAGxCnI,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;;;UArF4BH,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAA8I,gBAAA,YAAAd,GAAA,CAAAlE,kBAAA,CAAgC;UAE9B9D,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA4H,GAAA,CAAAjE,oBAAA,CAAuB;UAStC/D,EAAA,CAAAM,SAAA,GAAuD;UAAvDN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAAe,QAAA,KAAAf,GAAA,CAAA9G,YAAA,CAAAC,OAAA,CAAuD;UAqBnEnB,EAAA,CAAAM,SAAA,EAAiF;UAAjFN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAA9G,YAAA,CAAA0C,SAAA,IAAAoE,GAAA,CAAAe,QAAA,KAAAf,GAAA,CAAA9G,YAAA,CAAAC,OAAA,CAAiF;UAOjFnB,EAAA,CAAAM,SAAA,EAAgF;UAAhFN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAA9G,YAAA,CAAA0C,SAAA,IAAAoE,GAAA,CAAAe,QAAA,IAAAf,GAAA,CAAA9G,YAAA,CAAAC,OAAA,CAAgF;UAOhFnB,EAAA,CAAAM,SAAA,EAAkF;UAAlFN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,KAAA8G,GAAA,CAAA9G,YAAA,CAAA0C,SAAA,IAAAoE,GAAA,CAAAgB,QAAA,KAAAhB,GAAA,CAAA9G,YAAA,CAAAC,OAAA,CAAkF;UAM/CnB,EAAA,CAAAM,SAAA,EAA0C;UAA1CN,EAAA,CAAAI,UAAA,SAAA4H,GAAA,CAAA9G,YAAA,IAAA8G,GAAA,CAAA9G,YAAA,CAAAC,OAAA,CAA0C;UAgB3DnB,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAI,UAAA,YAAA4H,GAAA,CAAApE,SAAA,CAAe;UAa1B5D,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAA8I,gBAAA,SAAAd,GAAA,CAAAvC,SAAA,CAAoB;UAAuBzF,EAAtB,CAAAI,UAAA,aAAA4H,GAAA,CAAAnE,QAAA,CAAqB,mBAAAmE,GAAA,CAAApC,YAAA,CAAgC;;;qBDrDlFlG,YAAY,EAAAuJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAExJ,YAAY,EAAAyJ,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,qBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}