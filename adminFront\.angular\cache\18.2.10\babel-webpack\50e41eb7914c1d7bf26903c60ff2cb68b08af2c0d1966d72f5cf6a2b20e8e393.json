{"ast": null, "code": "import { operate } from '../util/lift';\nexport function finalize(callback) {\n  return operate((source, subscriber) => {\n    try {\n      source.subscribe(subscriber);\n    } finally {\n      subscriber.add(callback);\n    }\n  });\n}", "map": {"version": 3, "names": ["operate", "finalize", "callback", "source", "subscriber", "subscribe", "add"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/rxjs/dist/esm/internal/operators/finalize.js"], "sourcesContent": ["import { operate } from '../util/lift';\nexport function finalize(callback) {\n    return operate((source, subscriber) => {\n        try {\n            source.subscribe(subscriber);\n        }\n        finally {\n            subscriber.add(callback);\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,QAAQA,CAACC,QAAQ,EAAE;EAC/B,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAI;MACAD,MAAM,CAACE,SAAS,CAACD,UAAU,CAAC;IAChC,CAAC,SACO;MACJA,UAAU,CAACE,GAAG,CAACJ,QAAQ,CAAC;IAC5B;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}