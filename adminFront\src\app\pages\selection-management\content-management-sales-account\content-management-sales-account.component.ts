import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { tap } from 'rxjs/operators';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { BuildCaseService, FormItemService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';
import { GetListFormItemRes } from 'src/services/api/models';
import { SharedModule } from '../../components/shared.module';
import { BaseComponent } from '../../components/base/baseComponent';
import { EventService, IEvent, EEvent } from 'src/app/shared/services/event.service';

export interface selectItem {
  label: string,
  value: number,
  key?: string
}

@Component({
  selector: 'ngx-content-management-sales-account',
  templateUrl: './content-management-sales-account.component.html',
  styleUrls: ['./content-management-sales-account.component.scss'],
  standalone: true,
  imports: [CommonModule, SharedModule,],
})
export class ContentManagementSalesAccountComponent extends BaseComponent implements OnInit {


  toggleSwitch(CIsLock: any) {
    if(CIsLock) {
      this.unLock()
    } else {
      this.onLock()
    }
  }

  tempBuildCaseID: number = -1
  selectedBuilding: any;
  buildingSelectedOptions: any[] = [{ value: '', label: '全部' }];

  formItems: any;
  listFormItem: GetListFormItemRes;
  override pageSize = 20;

  buildCaseId: number;
  cBuildCaseSelected: any;
  userBuildCaseOptions: any;

  constructor(
    private _allow: AllowHelper,
    private router: Router,
    private message: MessageService,
    private _buildCaseService: BuildCaseService,
    private _formItemService: FormItemService,
    private _eventService: EventService,
  ) {
    super(_allow);
    this._eventService.receive().pipe(
      tap((res: IEvent) => {
        if (res.action == EEvent.GET_BUILDCASE && !!res.payload) {
          this.tempBuildCaseID = res.payload
        }
      })
    ).subscribe()
  }

  override ngOnInit(): void {
    this.cBuildCaseSelected = null;
    this.getUserBuildCase()
  }

  getUserBuildCase() {
    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({
      body: {
        CBuildCaseId: this.buildCaseId
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.userBuildCaseOptions = res.Entries.map(res => {
            return {
              CBuildCaseName: res.CBuildCaseName,
              cID: res.cID
            };
          });

          if (this.tempBuildCaseID != -1) {
            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseID)
            this.cBuildCaseSelected = this.userBuildCaseOptions[index]
          } else {
            this.cBuildCaseSelected = this.userBuildCaseOptions[0];
          }
          if (this.cBuildCaseSelected.cID) {
            this.getListFormItem();
          }
        }
      })
    ).subscribe();
  }

  typeContentManagementSalesAccount = {
    CFormType: 2,
  }


  getListFormItem() {
    this._formItemService.apiFormItemGetListFormItemPost$Json({
      body: {
        CBuildCaseId: this.cBuildCaseSelected.cID,
        CFormType: this.typeContentManagementSalesAccount.CFormType,
        PageIndex: this.pageIndex,
        PageSize: this.pageSize,
        CIsPaging: true
      }
    }).pipe(
      tap(res => {
        if (res.Entries && res.StatusCode == 0) {
          this.formItems = res.Entries.formItems;
          this.listFormItem = res.Entries;
          this.totalRecords = res.TotalItems ? res.TotalItems : 0
        }
      })
    ).subscribe();
  }

  onSelectionChangeBuildCase() {
    this.getListFormItem();
  }

  pageChanged(newPage: number) {
    this.pageIndex = newPage;
    this.getListFormItem();
  }

  onLock() {
    this._formItemService.apiFormItemLockFormItemPost$Json({
      body: {
        CBuildCaseId: this.cBuildCaseSelected.cID,
        CFormId: this.listFormItem.CFormId
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
      } else {
        // this.message.showErrorMSG(res.Message!);
        this.message.showErrorMSG("無資料，不可鎖定");
      }
      this.getListFormItem();
    });
  }

  unLock() {
    this._formItemService.apiFormItemUnlockFormItemPost$Json({
      body: {
        CBuildCaseID: this.cBuildCaseSelected.cID,
        CFormId: this.listFormItem.CFormId
      }
    }).subscribe(res => {
      if (res.StatusCode == 0) {
        this.message.showSucessMSG("執行成功");
      } else {
        this.message.showErrorMSG(res.Message!);
      }
      this.getListFormItem();
    });
  }

  navidateDetai() {
    this.router.navigate([`pages/content-management-sales-account/${this.cBuildCaseSelected.cID}`]);
  }
}
