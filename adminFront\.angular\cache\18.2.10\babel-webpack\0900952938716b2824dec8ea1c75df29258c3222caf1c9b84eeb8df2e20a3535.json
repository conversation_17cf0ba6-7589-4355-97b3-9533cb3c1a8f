{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { VisitorsAnalyticsData } from '../data/visitors-analytics';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./periods.service\";\nexport class VisitorsAnalyticsService extends VisitorsAnalyticsData {\n  constructor(periodService) {\n    super();\n    this.periodService = periodService;\n    this.pieChartValue = 75;\n    this.innerLinePoints = [94, 188, 225, 244, 253, 254, 249, 235, 208, 173, 141, 118, 105, 97, 94, 96, 104, 121, 147, 183, 224, 265, 302, 333, 358, 375, 388, 395, 400, 400, 397, 390, 377, 360, 338, 310, 278, 241, 204, 166, 130, 98, 71, 49, 32, 20, 13, 9];\n    this.outerLinePoints = [85, 71, 59, 50, 45, 42, 41, 44, 58, 88, 136, 199, 267, 326, 367, 391, 400, 397, 376, 319, 200, 104, 60, 41, 36, 37, 44, 55, 74, 100, 131, 159, 180, 193, 199, 200, 195, 184, 164, 135, 103, 73, 50, 33, 22, 15, 11];\n  }\n  generateOutlineLineData() {\n    const months = this.periodService.getMonths();\n    const outerLinePointsLength = this.outerLinePoints.length;\n    const monthsLength = months.length;\n    return this.outerLinePoints.map((p, index) => {\n      const monthIndex = Math.round(index / 4);\n      const label = index % Math.round(outerLinePointsLength / monthsLength) === 0 ? months[monthIndex] : '';\n      return {\n        label,\n        value: p\n      };\n    });\n  }\n  getInnerLineChartData() {\n    return observableOf(this.innerLinePoints);\n  }\n  getOutlineLineChartData() {\n    return observableOf(this.generateOutlineLineData());\n  }\n  getPieChartData() {\n    return observableOf(this.pieChartValue);\n  }\n  static {\n    this.ɵfac = function VisitorsAnalyticsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VisitorsAnalyticsService)(i0.ɵɵinject(i1.PeriodsService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: VisitorsAnalyticsService,\n      factory: VisitorsAnalyticsService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "VisitorsAnalyticsData", "VisitorsAnalyticsService", "constructor", "periodService", "pieChartValue", "innerLinePoints", "outerLinePoints", "generateOutlineLineData", "months", "getMonths", "outerLinePointsLength", "length", "<PERSON><PERSON><PERSON><PERSON>", "map", "p", "index", "monthIndex", "Math", "round", "label", "value", "getInnerLineChartData", "getOutlineLineChartData", "getPieChartData", "i0", "ɵɵinject", "i1", "PeriodsService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\visitors-analytics.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf, Observable } from 'rxjs';\r\nimport { PeriodsService } from './periods.service';\r\nimport { OutlineData, VisitorsAnalyticsData } from '../data/visitors-analytics';\r\n\r\n@Injectable()\r\nexport class VisitorsAnalyticsService extends VisitorsAnalyticsData {\r\n\r\n  constructor(private periodService: PeriodsService) {\r\n    super();\r\n  }\r\n\r\n  private pieChartValue = 75;\r\n  private innerLinePoints: number[] = [\r\n    94, 188, 225, 244, 253, 254, 249, 235, 208,\r\n    173, 141, 118, 105, 97, 94, 96, 104, 121, 147,\r\n    183, 224, 265, 302, 333, 358, 375, 388, 395,\r\n    400, 400, 397, 390, 377, 360, 338, 310, 278,\r\n    241, 204, 166, 130, 98, 71, 49, 32, 20, 13, 9,\r\n  ];\r\n  private outerLinePoints: number[] = [\r\n    85, 71, 59, 50, 45, 42, 41, 44 , 58, 88,\r\n    136 , 199, 267, 326, 367, 391, 400, 397,\r\n    376, 319, 200, 104, 60, 41, 36, 37, 44,\r\n    55, 74, 100 , 131, 159, 180, 193, 199, 200,\r\n    195, 184, 164, 135, 103, 73, 50, 33, 22, 15, 11,\r\n  ];\r\n  private generateOutlineLineData(): OutlineData[] {\r\n    const months = this.periodService.getMonths();\r\n    const outerLinePointsLength = this.outerLinePoints.length;\r\n    const monthsLength = months.length;\r\n\r\n    return this.outerLinePoints.map((p, index) => {\r\n      const monthIndex = Math.round(index / 4);\r\n      const label = (index % Math.round(outerLinePointsLength / monthsLength) === 0)\r\n        ? months[monthIndex]\r\n        : '';\r\n\r\n      return {\r\n        label,\r\n        value: p,\r\n      };\r\n    });\r\n  }\r\n\r\n  getInnerLineChartData(): Observable<number[]> {\r\n    return observableOf(this.innerLinePoints);\r\n  }\r\n\r\n  getOutlineLineChartData(): Observable<OutlineData[]> {\r\n    return observableOf(this.generateOutlineLineData());\r\n  }\r\n\r\n  getPieChartData(): Observable<number> {\r\n    return observableOf(this.pieChartValue);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAoB,MAAM;AAErD,SAAsBC,qBAAqB,QAAQ,4BAA4B;;;AAG/E,OAAM,MAAOC,wBAAyB,SAAQD,qBAAqB;EAEjEE,YAAoBC,aAA6B;IAC/C,KAAK,EAAE;IADW,KAAAA,aAAa,GAAbA,aAAa;IAIzB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,eAAe,GAAa,CAClC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC1C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC7C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAC9C;IACO,KAAAC,eAAe,GAAa,CAClC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAG,EAAE,EAAE,EAAE,EACvC,GAAG,EAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACvC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACtC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC1C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAChD;EAhBD;EAiBQC,uBAAuBA,CAAA;IAC7B,MAAMC,MAAM,GAAG,IAAI,CAACL,aAAa,CAACM,SAAS,EAAE;IAC7C,MAAMC,qBAAqB,GAAG,IAAI,CAACJ,eAAe,CAACK,MAAM;IACzD,MAAMC,YAAY,GAAGJ,MAAM,CAACG,MAAM;IAElC,OAAO,IAAI,CAACL,eAAe,CAACO,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAI;MAC3C,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC;MACxC,MAAMI,KAAK,GAAIJ,KAAK,GAAGE,IAAI,CAACC,KAAK,CAACR,qBAAqB,GAAGE,YAAY,CAAC,KAAK,CAAC,GACzEJ,MAAM,CAACQ,UAAU,CAAC,GAClB,EAAE;MAEN,OAAO;QACLG,KAAK;QACLC,KAAK,EAAEN;OACR;IACH,CAAC,CAAC;EACJ;EAEAO,qBAAqBA,CAAA;IACnB,OAAOtB,YAAY,CAAC,IAAI,CAACM,eAAe,CAAC;EAC3C;EAEAiB,uBAAuBA,CAAA;IACrB,OAAOvB,YAAY,CAAC,IAAI,CAACQ,uBAAuB,EAAE,CAAC;EACrD;EAEAgB,eAAeA,CAAA;IACb,OAAOxB,YAAY,CAAC,IAAI,CAACK,aAAa,CAAC;EACzC;;;uCAjDWH,wBAAwB,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;aAAxB1B,wBAAwB;MAAA2B,OAAA,EAAxB3B,wBAAwB,CAAA4B;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}