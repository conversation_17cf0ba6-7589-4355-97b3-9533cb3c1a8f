{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nimport { enableProdMode } from '@angular/core';\nif (environment.IS_PRODUCTION) {\n  enableProdMode();\n  window.console.log = function () {};\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}