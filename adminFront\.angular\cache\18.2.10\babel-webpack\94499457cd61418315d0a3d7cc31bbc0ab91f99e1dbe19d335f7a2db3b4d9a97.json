{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Bengali (Bangladesh) [bn-bd]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/ashwoolford\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '১',\n      2: '২',\n      3: '৩',\n      4: '৪',\n      5: '৫',\n      6: '৬',\n      7: '৭',\n      8: '৮',\n      9: '৯',\n      0: '০'\n    },\n    numberMap = {\n      '১': '1',\n      '২': '2',\n      '৩': '3',\n      '৪': '4',\n      '৫': '5',\n      '৬': '6',\n      '৭': '7',\n      '৮': '8',\n      '৯': '9',\n      '০': '0'\n    };\n  var bnBd = moment.defineLocale('bn-bd', {\n    months: 'জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর'.split('_'),\n    monthsShort: 'জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে'.split('_'),\n    weekdays: 'রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার'.split('_'),\n    weekdaysShort: 'রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি'.split('_'),\n    weekdaysMin: 'রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm সময়',\n      LTS: 'A h:mm:ss সময়',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm সময়',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm সময়'\n    },\n    calendar: {\n      sameDay: '[আজ] LT',\n      nextDay: '[আগামীকাল] LT',\n      nextWeek: 'dddd, LT',\n      lastDay: '[গতকাল] LT',\n      lastWeek: '[গত] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s পরে',\n      past: '%s আগে',\n      s: 'কয়েক সেকেন্ড',\n      ss: '%d সেকেন্ড',\n      m: 'এক মিনিট',\n      mm: '%d মিনিট',\n      h: 'এক ঘন্টা',\n      hh: '%d ঘন্টা',\n      d: 'এক দিন',\n      dd: '%d দিন',\n      M: 'এক মাস',\n      MM: '%d মাস',\n      y: 'এক বছর',\n      yy: '%d বছর'\n    },\n    preparse: function (string) {\n      return string.replace(/[১২৩৪৫৬৭৮৯০]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    meridiemParse: /রাত|ভোর|সকাল|দুপুর|বিকাল|সন্ধ্যা|রাত/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'রাত') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'ভোর') {\n        return hour;\n      } else if (meridiem === 'সকাল') {\n        return hour;\n      } else if (meridiem === 'দুপুর') {\n        return hour >= 3 ? hour : hour + 12;\n      } else if (meridiem === 'বিকাল') {\n        return hour + 12;\n      } else if (meridiem === 'সন্ধ্যা') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'রাত';\n      } else if (hour < 6) {\n        return 'ভোর';\n      } else if (hour < 12) {\n        return 'সকাল';\n      } else if (hour < 15) {\n        return 'দুপুর';\n      } else if (hour < 18) {\n        return 'বিকাল';\n      } else if (hour < 20) {\n        return 'সন্ধ্যা';\n      } else {\n        return 'রাত';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return bnBd;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}