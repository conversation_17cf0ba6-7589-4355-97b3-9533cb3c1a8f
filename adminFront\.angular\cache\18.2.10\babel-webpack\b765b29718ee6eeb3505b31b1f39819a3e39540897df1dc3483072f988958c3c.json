{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param {Date|Number} dateLeft - the first date to compare\n * @param {Date|Number} dateRight - the second date to compare\n * @returns {Boolean} the dates are equal\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport default function isEqual(dirtyLeftDate, dirtyRightDate) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyLeftDate);\n  var dateRight = toDate(dirtyRightDate);\n  return dateLeft.getTime() === dateRight.getTime();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}