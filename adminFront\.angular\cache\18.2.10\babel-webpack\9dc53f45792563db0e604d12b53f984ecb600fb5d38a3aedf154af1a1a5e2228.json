{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let BooleanStringPipe = /*#__PURE__*/(() => {\n  class BooleanStringPipe {\n    transform(value, ...args) {\n      return value ? '是' : '否';\n    }\n    static {\n      this.ɵfac = function BooleanStringPipe_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BooleanStringPipe)();\n      };\n    }\n    static {\n      this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n        name: \"booleanString\",\n        type: BooleanStringPipe,\n        pure: true,\n        standalone: true\n      });\n    }\n  }\n  return BooleanStringPipe;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}