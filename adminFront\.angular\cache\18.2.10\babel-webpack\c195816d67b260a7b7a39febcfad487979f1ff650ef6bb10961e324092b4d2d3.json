{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class ImageModalComponent {\n  constructor(cdr, elementRef) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.images = [];\n    this.currentIndex = 0;\n    this.isVisible = false;\n    this.showThumbnails = true;\n    this.showCounter = true;\n    this.showNavigation = true;\n    this.showImageInfo = true;\n    this.enableKeyboard = true;\n    this.enableClickOutsideToClose = true;\n    this.zIndex = 9999;\n    this.close = new EventEmitter();\n    this.indexChange = new EventEmitter();\n    this.imageClick = new EventEmitter();\n  }\n  ngOnInit() {\n    if (this.currentIndex >= this.images.length) {\n      this.currentIndex = 0;\n    }\n    // 監聽 isVisible 變化\n    this.handleModalVisibility();\n  }\n  ngOnDestroy() {\n    // 確保清理\n    if (this.isVisible) {\n      document.body.classList.remove('modal-open');\n      document.body.style.overflow = 'auto';\n    }\n  }\n  handleKeyboardEvent(event) {\n    if (!this.enableKeyboard || !this.isVisible) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        event.preventDefault();\n        this.goToPrevious();\n        break;\n      case 'ArrowRight':\n        event.preventDefault();\n        this.goToNext();\n        break;\n      case 'Escape':\n        event.preventDefault();\n        this.closeModal();\n        break;\n    }\n  }\n  getCurrentImage() {\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\n      return null;\n    }\n    return this.images[this.currentIndex];\n  }\n  goToNext() {\n    if (this.images.length <= 1) return;\n    const newIndex = (this.currentIndex + 1) % this.images.length;\n    this.setCurrentIndex(newIndex);\n  }\n  goToPrevious() {\n    if (this.images.length <= 1) return;\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\n    this.setCurrentIndex(newIndex);\n  }\n  goToIndex(index) {\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\n      this.setCurrentIndex(index);\n    }\n  }\n  onBackdropClick(event) {\n    if (this.enableClickOutsideToClose && event.target === event.currentTarget) {\n      this.closeModal();\n    }\n  }\n  onImageClick() {\n    const currentImage = this.getCurrentImage();\n    if (currentImage) {\n      this.imageClick.emit({\n        index: this.currentIndex,\n        image: currentImage\n      });\n    }\n  }\n  closeModal() {\n    this.isVisible = false;\n    this.close.emit();\n    this.handleModalVisibility();\n  }\n  setCurrentIndex(index) {\n    this.currentIndex = index;\n    this.indexChange.emit(index);\n    this.cdr.detectChanges();\n  }\n  handleModalVisibility() {\n    if (this.isVisible) {\n      document.body.classList.add('modal-open');\n      document.body.style.overflow = 'hidden';\n      // 聚焦到模態窗口以支持鍵盤導航\n      setTimeout(() => {\n        const modalElement = this.elementRef.nativeElement.querySelector('.image-modal-container');\n        if (modalElement) {\n          modalElement.focus();\n        }\n      }, 100);\n    } else {\n      document.body.classList.remove('modal-open');\n      document.body.style.overflow = 'auto';\n    }\n  }\n  // 阻止事件冒泡\n  stopPropagation(event) {\n    event.stopPropagation();\n  }\n  static {\n    this.ɵfac = function ImageModalComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImageModalComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ImageModalComponent,\n      selectors: [[\"ngx-image-modal\"]],\n      hostBindings: function ImageModalComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ImageModalComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeyboardEvent($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        images: \"images\",\n        currentIndex: \"currentIndex\",\n        isVisible: \"isVisible\",\n        showThumbnails: \"showThumbnails\",\n        showCounter: \"showCounter\",\n        showNavigation: \"showNavigation\",\n        showImageInfo: \"showImageInfo\",\n        enableKeyboard: \"enableKeyboard\",\n        enableClickOutsideToClose: \"enableClickOutsideToClose\",\n        zIndex: \"zIndex\"\n      },\n      outputs: {\n        close: \"close\",\n        indexChange: \"indexChange\",\n        imageClick: \"imageClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function ImageModalComponent_Template(rf, ctx) {},\n      dependencies: [CommonModule],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJpbWFnZS1tb2RhbC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvQHRoZW1lL2NvbXBvbmVudHMvaW1hZ2UtbW9kYWwvaW1hZ2UtbW9kYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLHdLQUF3SyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "ImageModalComponent", "constructor", "cdr", "elementRef", "images", "currentIndex", "isVisible", "showThumbnails", "showCounter", "showNavigation", "showImageInfo", "enableKeyboard", "enableClickOutsideToClose", "zIndex", "close", "indexChange", "imageClick", "ngOnInit", "length", "handleModalVisibility", "ngOnDestroy", "document", "body", "classList", "remove", "style", "overflow", "handleKeyboardEvent", "event", "key", "preventDefault", "goToPrevious", "goToNext", "closeModal", "getCurrentImage", "newIndex", "setCurrentIndex", "goToIndex", "index", "onBackdropClick", "target", "currentTarget", "onImageClick", "currentImage", "emit", "image", "detectChanges", "add", "setTimeout", "modalElement", "nativeElement", "querySelector", "focus", "stopPropagation", "i0", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "selectors", "hostBindings", "ImageModalComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "ImageModalComponent_keydown_HostBindingHandler", "$event", "ɵɵresolveDocument", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\image-modal\\image-modal.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, On<PERSON><PERSON>roy, HostListener, ChangeDetectorRef, ElementRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface ImageModalItem {\r\n  url: string;\r\n  name?: string;\r\n  description?: string;\r\n  alt?: string;\r\n  title?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-image-modal',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './image-modal.component.html',\r\n  styleUrls: ['./image-modal.component.scss']\r\n})\r\nexport class ImageModalComponent implements OnInit, OnDestroy {\r\n  @Input() images: ImageModalItem[] = [];\r\n  @Input() currentIndex: number = 0;\r\n  @Input() isVisible: boolean = false;\r\n  @Input() showThumbnails: boolean = true;\r\n  @Input() showCounter: boolean = true;\r\n  @Input() showNavigation: boolean = true;\r\n  @Input() showImageInfo: boolean = true;\r\n  @Input() enableKeyboard: boolean = true;\r\n  @Input() enableClickOutsideToClose: boolean = true;\r\n  @Input() zIndex: number = 9999;\r\n\r\n  @Output() close = new EventEmitter<void>();\r\n  @Output() indexChange = new EventEmitter<number>();\r\n  @Output() imageClick = new EventEmitter<{ index: number; image: ImageModalItem }>();\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private elementRef: ElementRef\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    if (this.currentIndex >= this.images.length) {\r\n      this.currentIndex = 0;\r\n    }\r\n    // 監聽 isVisible 變化\r\n    this.handleModalVisibility();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保清理\r\n    if (this.isVisible) {\r\n      document.body.classList.remove('modal-open');\r\n      document.body.style.overflow = 'auto';\r\n    }\r\n  }\r\n\r\n  @HostListener('document:keydown', ['$event'])\r\n  handleKeyboardEvent(event: KeyboardEvent): void {\r\n    if (!this.enableKeyboard || !this.isVisible) return;\r\n\r\n    switch (event.key) {\r\n      case 'ArrowLeft':\r\n        event.preventDefault();\r\n        this.goToPrevious();\r\n        break;\r\n      case 'ArrowRight':\r\n        event.preventDefault();\r\n        this.goToNext();\r\n        break;\r\n      case 'Escape':\r\n        event.preventDefault();\r\n        this.closeModal();\r\n        break;\r\n    }\r\n  }\r\n\r\n  getCurrentImage(): ImageModalItem | null {\r\n    if (this.images.length === 0 || this.currentIndex < 0 || this.currentIndex >= this.images.length) {\r\n      return null;\r\n    }\r\n    return this.images[this.currentIndex];\r\n  }\r\n\r\n  goToNext(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = (this.currentIndex + 1) % this.images.length;\r\n    this.setCurrentIndex(newIndex);\r\n  }\r\n\r\n  goToPrevious(): void {\r\n    if (this.images.length <= 1) return;\r\n    \r\n    const newIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;\r\n    this.setCurrentIndex(newIndex);\r\n  }\r\n\r\n  goToIndex(index: number): void {\r\n    if (index >= 0 && index < this.images.length && index !== this.currentIndex) {\r\n      this.setCurrentIndex(index);\r\n    }\r\n  }\r\n\r\n  onBackdropClick(event: Event): void {\r\n    if (this.enableClickOutsideToClose && event.target === event.currentTarget) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\n  onImageClick(): void {\r\n    const currentImage = this.getCurrentImage();\r\n    if (currentImage) {\r\n      this.imageClick.emit({ \r\n        index: this.currentIndex, \r\n        image: currentImage \r\n      });\r\n    }\r\n  }\r\n\r\n  closeModal(): void {\r\n    this.isVisible = false;\r\n    this.close.emit();\r\n    this.handleModalVisibility();\r\n  }\r\n\r\n  private setCurrentIndex(index: number): void {\r\n    this.currentIndex = index;\r\n    this.indexChange.emit(index);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private handleModalVisibility(): void {\r\n    if (this.isVisible) {\r\n      document.body.classList.add('modal-open');\r\n      document.body.style.overflow = 'hidden';\r\n      // 聚焦到模態窗口以支持鍵盤導航\r\n      setTimeout(() => {\r\n        const modalElement = this.elementRef.nativeElement.querySelector('.image-modal-container');\r\n        if (modalElement) {\r\n          modalElement.focus();\r\n        }\r\n      }, 100);\r\n    } else {\r\n      document.body.classList.remove('modal-open');\r\n      document.body.style.overflow = 'auto';\r\n    }\r\n  }\r\n\r\n  // 阻止事件冒泡\r\n  stopPropagation(event: Event): void {\r\n    event.stopPropagation();\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAwE,eAAe;AACtI,SAASC,YAAY,QAAQ,iBAAiB;;AAiB9C,OAAM,MAAOC,mBAAmB;EAgB9BC,YACUC,GAAsB,EACtBC,UAAsB;IADtB,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IAjBX,KAAAC,MAAM,GAAqB,EAAE;IAC7B,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,aAAa,GAAY,IAAI;IAC7B,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,yBAAyB,GAAY,IAAI;IACzC,KAAAC,MAAM,GAAW,IAAI;IAEpB,KAAAC,KAAK,GAAG,IAAIhB,YAAY,EAAQ;IAChC,KAAAiB,WAAW,GAAG,IAAIjB,YAAY,EAAU;IACxC,KAAAkB,UAAU,GAAG,IAAIlB,YAAY,EAA4C;EAKhF;EAEHmB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACZ,YAAY,IAAI,IAAI,CAACD,MAAM,CAACc,MAAM,EAAE;MAC3C,IAAI,CAACb,YAAY,GAAG,CAAC;IACvB;IACA;IACA,IAAI,CAACc,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACd,SAAS,EAAE;MAClBe,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC5CH,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,MAAM;IACvC;EACF;EAGAC,mBAAmBA,CAACC,KAAoB;IACtC,IAAI,CAAC,IAAI,CAACjB,cAAc,IAAI,CAAC,IAAI,CAACL,SAAS,EAAE;IAE7C,QAAQsB,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACdD,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACC,YAAY,EAAE;QACnB;MACF,KAAK,YAAY;QACfH,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACE,QAAQ,EAAE;QACf;MACF,KAAK,QAAQ;QACXJ,KAAK,CAACE,cAAc,EAAE;QACtB,IAAI,CAACG,UAAU,EAAE;QACjB;IACJ;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC9B,MAAM,CAACc,MAAM,KAAK,CAAC,IAAI,IAAI,CAACb,YAAY,GAAG,CAAC,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACD,MAAM,CAACc,MAAM,EAAE;MAChG,OAAO,IAAI;IACb;IACA,OAAO,IAAI,CAACd,MAAM,CAAC,IAAI,CAACC,YAAY,CAAC;EACvC;EAEA2B,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5B,MAAM,CAACc,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMiB,QAAQ,GAAG,CAAC,IAAI,CAAC9B,YAAY,GAAG,CAAC,IAAI,IAAI,CAACD,MAAM,CAACc,MAAM;IAC7D,IAAI,CAACkB,eAAe,CAACD,QAAQ,CAAC;EAChC;EAEAJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC3B,MAAM,CAACc,MAAM,IAAI,CAAC,EAAE;IAE7B,MAAMiB,QAAQ,GAAG,IAAI,CAAC9B,YAAY,KAAK,CAAC,GAAG,IAAI,CAACD,MAAM,CAACc,MAAM,GAAG,CAAC,GAAG,IAAI,CAACb,YAAY,GAAG,CAAC;IACzF,IAAI,CAAC+B,eAAe,CAACD,QAAQ,CAAC;EAChC;EAEAE,SAASA,CAACC,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAClC,MAAM,CAACc,MAAM,IAAIoB,KAAK,KAAK,IAAI,CAACjC,YAAY,EAAE;MAC3E,IAAI,CAAC+B,eAAe,CAACE,KAAK,CAAC;IAC7B;EACF;EAEAC,eAAeA,CAACX,KAAY;IAC1B,IAAI,IAAI,CAAChB,yBAAyB,IAAIgB,KAAK,CAACY,MAAM,KAAKZ,KAAK,CAACa,aAAa,EAAE;MAC1E,IAAI,CAACR,UAAU,EAAE;IACnB;EACF;EAEAS,YAAYA,CAAA;IACV,MAAMC,YAAY,GAAG,IAAI,CAACT,eAAe,EAAE;IAC3C,IAAIS,YAAY,EAAE;MAChB,IAAI,CAAC3B,UAAU,CAAC4B,IAAI,CAAC;QACnBN,KAAK,EAAE,IAAI,CAACjC,YAAY;QACxBwC,KAAK,EAAEF;OACR,CAAC;IACJ;EACF;EAEAV,UAAUA,CAAA;IACR,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACQ,KAAK,CAAC8B,IAAI,EAAE;IACjB,IAAI,CAACzB,qBAAqB,EAAE;EAC9B;EAEQiB,eAAeA,CAACE,KAAa;IACnC,IAAI,CAACjC,YAAY,GAAGiC,KAAK;IACzB,IAAI,CAACvB,WAAW,CAAC6B,IAAI,CAACN,KAAK,CAAC;IAC5B,IAAI,CAACpC,GAAG,CAAC4C,aAAa,EAAE;EAC1B;EAEQ3B,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACb,SAAS,EAAE;MAClBe,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACwB,GAAG,CAAC,YAAY,CAAC;MACzC1B,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,QAAQ;MACvC;MACAsB,UAAU,CAAC,MAAK;QACd,MAAMC,YAAY,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,aAAa,CAACC,aAAa,CAAC,wBAAwB,CAAC;QAC1F,IAAIF,YAAY,EAAE;UAChBA,YAAY,CAACG,KAAK,EAAE;QACtB;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACL/B,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MAC5CH,QAAQ,CAACC,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAG,MAAM;IACvC;EACF;EAEA;EACA2B,eAAeA,CAACzB,KAAY;IAC1BA,KAAK,CAACyB,eAAe,EAAE;EACzB;;;uCApIWrD,mBAAmB,EAAAsD,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,iBAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,UAAA;IAAA;EAAA;;;YAAnBzD,mBAAmB;MAAA0D,SAAA;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAnBP,EAAA,CAAAS,UAAA,qBAAAC,+CAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAnC,mBAAA,CAAAsC,MAAA,CAA2B;UAAA,UAAAX,EAAA,CAAAY,iBAAA,CAAR;;;;;;;;;;;;;;;;;;;;;;;;;qBAJpBnE,YAAY;MAAAoE,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}