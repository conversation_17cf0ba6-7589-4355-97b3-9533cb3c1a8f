{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../components/breadcrumb/breadcrumb.component\";\nfunction CategoryManagementComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const dialog_r3 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r1.addNew(dialog_r3));\n    });\n    i0.ɵɵelementStart(1, \"button\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵtext(3, \" \\u65B0\\u589E\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CategoryManagementComponent_tr_32_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_tr_32_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const dialog_r3 = i0.ɵɵreference(35);\n      return i0.ɵɵresetView(ctx_r1.addNew(dialog_r3));\n    });\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CategoryManagementComponent_tr_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 21);\n    i0.ɵɵtemplate(10, CategoryManagementComponent_tr_32_button_10_Template, 2, 0, \"button\", 22);\n    i0.ɵɵelementStart(11, \"button\", 23);\n    i0.ɵɵtext(12, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r6 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cBuildCaseld);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.cCategoryName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isCreate);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"nb-tabset\")(2, \"nb-tab\", 28)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 29);\n    i0.ɵɵelementStart(5, \"input\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_ng_container_2_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.houseTypeName, $event) || (ctx_r1.houseTypeName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 31)(7, \"span\", 32);\n    i0.ɵɵtext(8, \"\\u6A19\\u6E96\\u5716:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"button\", 34);\n    i0.ɵɵelement(11, \"nb-icon\", 35);\n    i0.ɵɵtext(12, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_ng_container_2_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmitDetail());\n    });\n    i0.ɵɵtext(14, \"\\u63D0\\u4EA4\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.houseTypeName);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_10_listener() {\n      const building_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onEdit(building_r10));\n    });\n    i0.ɵɵtext(11, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template_button_click_12_listener() {\n      const building_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDelete(building_r10));\n    });\n    i0.ɵɵtext(13, \" \\u522A\\u9664 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const building_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.buildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.floor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r10.units);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_nb_tabset_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-tabset\")(1, \"nb-tab\", 28)(2, \"form\", 37);\n    i0.ɵɵlistener(\"ngSubmit\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_form_ngSubmit_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"label\", 39);\n    i0.ɵɵtext(5, \"\\u68DF\\u5225\\u540D\\u7A31:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.buildingName, $event) || (ctx_r1.buildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 38)(8, \"label\", 41);\n    i0.ɵɵtext(9, \"\\u6A13\\u5C64:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.floor, $event) || (ctx_r1.floor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38)(12, \"label\", 43);\n    i0.ɵɵtext(13, \"\\u6236\\u6578:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"input\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CategoryManagementComponent_ng_template_34_nb_tabset_3_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.units, $event) || (ctx_r1.units = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 45);\n    i0.ɵɵtext(16, \"\\u63D0\\u4EA4\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"nb-tab\", 46)(18, \"div\", 3)(19, \"div\", 6)(20, \"nb-form-field\");\n    i0.ɵɵelement(21, \"input\", 7);\n    i0.ɵɵelementStart(22, \"button\", 8);\n    i0.ɵɵelement(23, \"nb-icon\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(24, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"table\", 48)(26, \"thead\")(27, \"tr\", 49)(28, \"th\");\n    i0.ɵɵtext(29, \"\\u68DF\\u5225\\u4EE3\\u865F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"th\");\n    i0.ɵɵtext(31, \"\\u68DF\\u5225\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\");\n    i0.ɵɵtext(33, \"\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"th\");\n    i0.ɵɵtext(35, \"\\u6236\\u6578\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"th\");\n    i0.ɵɵtext(37, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"tbody\");\n    i0.ɵɵtemplate(39, CategoryManagementComponent_ng_template_34_nb_tabset_3_tr_39_Template, 14, 4, \"tr\", 16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.buildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.floor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.units);\n    i0.ɵɵadvance(25);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction CategoryManagementComponent_ng_template_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 25)(1, \"nb-card-body\", 26);\n    i0.ɵɵtemplate(2, CategoryManagementComponent_ng_template_34_ng_container_2_Template, 15, 1, \"ng-container\", 27)(3, CategoryManagementComponent_ng_template_34_nb_tabset_3_Template, 40, 4, \"nb-tabset\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"nb-card-footer\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.step == \"edit-detail\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.step !== \"edit-detail\");\n  }\n}\nexport let CategoryManagementComponent = /*#__PURE__*/(() => {\n  class CategoryManagementComponent extends BaseComponent {\n    onCreate() {\n      return;\n    }\n    onEdit(building) {\n      // Xử lý sự kiện khi nhấn nút \"編輯\"\n      // Ví dụ: mở modal để chỉnh sửa thông tin tòa nhà\n      this.step = 'edit-detail';\n    }\n    onDelete(building) {\n      // Xử lý sự kiện khi nhấn nút \"刪除\"\n      // Ví dụ: xác nhận xóa và xóa tòa nhà khỏi danh sách\n    }\n    constructor(_allow, dialogService, message) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.buildingName = '';\n      this.floor = null;\n      this.units = null;\n      this.step = \"detail\"; // detail, edit, delete\n      this.buildings = [{\n        id: 1,\n        buildingName: 'A棟',\n        floor: 10,\n        units: 50\n      }, {\n        id: 2,\n        buildingName: 'B棟',\n        floor: 15,\n        units: 80\n      }];\n      this.houseTypeName = ''; // Biến để lưu giá trị của trường nhập liệu\n      this.productList = [{\n        cID: 1,\n        cName: \"Product 1\",\n        cFile: \"https://example.com/product1.file\",\n        cBuildCaseld: 123,\n        cCategoryName: \"Electronics\",\n        cStatus: 1,\n        cCreateDT: \"2024-07-02T00:00:00.000Z\",\n        cCreator: \"user123\"\n      }, {\n        cID: 2,\n        cName: \"Product 2\",\n        cFile: \"https://example.com/product2.file\",\n        cCategoryName: \"Clothing\",\n        cStatus: 0,\n        cCreateDT: \"2024-07-02T00:00:00.000Z\",\n        cCreator: \"user456\"\n      }, {\n        cID: 3,\n        cName: \"Product 3\",\n        cFile: \"https://example.com/product3.file\",\n        cBuildCaseld: 456,\n        cCategoryName: \"Furniture\",\n        cStatus: 1,\n        cCreateDT: \"2024-07-02T00:00:00.000Z\",\n        cCreator: \"user789\"\n      }];\n      this.imgSrc = null;\n      this.uploadedFiles = [];\n    }\n    onSubmitDetail() {\n      // Kiểm tra tính hợp lệ (ví dụ: kiểm tra xem trường có rỗng không)\n      if (this.houseTypeName.trim() === '') {\n        // Xử lý trường hợp không hợp lệ (ví dụ: hiển thị thông báo lỗi)\n        return;\n      }\n      // Xử lý logic submit form tại đây\n      console.log(this.houseTypeName);\n    }\n    ngOnInit() {}\n    addNew(ref) {\n      this.isNew = true;\n      this.uploadedFiles = [];\n      this.project = {\n        cName: '',\n        cFile: '',\n        CStatus: '',\n        CSystemInstruction: ''\n      };\n      this.dialogService.open(ref);\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files[0]) {\n        const file = input.files[0];\n        const reader = new FileReader();\n        reader.onload = () => {\n          this.imgSrc = reader.result;\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    uploadFile(e) {\n      let files = e.target.files;\n      const fileRegex = /pdf|jpg|jpeg|png/i;\n      for (let i = 0; i < files.length; i++) {\n        if (!fileRegex.test(files[i].type)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n          return;\n        }\n        let file;\n        if (files[i].name.includes('pdf')) {\n          file = files[i];\n          let reader = new FileReader();\n          reader.onload = e => {\n            this.uploadedFiles.push({\n              cID: i,\n              cName: files[i].name,\n              cFile: e.target?.result?.toString().split(',')[1],\n              cImg: null,\n              cFileType: EnumFileType.PDF\n            });\n          };\n          reader.readAsDataURL(files[i]);\n        } else {\n          file = URL.createObjectURL(files[i]);\n          let reader = new FileReader();\n          reader.onload = e => {\n            this.uploadedFiles.push({\n              cID: i,\n              cName: files[i].name,\n              cFile: e.target?.result?.toString().split(',')[1],\n              cImg: file,\n              cFileType: EnumFileType.JPG\n            });\n          };\n          URL.revokeObjectURL(files[i]);\n          reader.readAsDataURL(files[i]);\n        }\n      }\n    }\n    deleteItem(item) {\n      if (window.confirm(`確定要移除【${item.cName}】?`)) {\n        this.uploadedFiles = this.uploadedFiles.filter(i => i.cID !== item.cID);\n      }\n    }\n    onSubmit() {}\n    static {\n      this.ɵfac = function CategoryManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CategoryManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CategoryManagementComponent,\n        selectors: [[\"ngx-category-management\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 2,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"row\"], [1, \"col-1\"], [\"for\", \"project\", 1, \"text-nowrap\", \"mr-1\", \"h-full\", \"mt-2\"], [1, \"col-5\"], [\"type\", \"text\", \"nbInput\", \"\"], [\"nbSuffix\", \"\", \"nbButton\", \"\", \"ghost\", \"\"], [\"icon\", \"search\", \"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\"], [1, \"col-6\"], [\"class\", \"d-flex justify-content-end\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"d-flex\", \"justify-content-end\", 3, \"click\"], [1, \"btn\", \"btn-info\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"text-center\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [4, \"ngIf\"], [\"tabTitle\", \"\\u7DE8\\u8F2F\"], [\"nbPrefix\", \"\", \"icon\", \"home-outline\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"name\", \"houseTypeName\", \"placeholder\", \"xxx1\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"upload-section\"], [1, \"label\"], [1, \"upload-button\"], [\"nbButton\", \"\", \"outline\", \"\", \"status\", \"primary\"], [\"icon\", \"plus-outline\"], [\"nbButton\", \"\", \"status\", \"primary\", \"type\", \"submit\", 3, \"click\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"buildingName\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"buildingName\", \"name\", \"buildingName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"floor\"], [\"type\", \"number\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"floor\", \"name\", \"floor\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"units\"], [\"type\", \"number\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"units\", \"name\", \"units\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"nbButton\", \"\", \"status\", \"primary\", \"type\", \"submit\"], [\"tabTitle\", \"\\u660E\\u7D30\"], [1, \"col-7\"], [1, \"w-full\", \"mt-2\", \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"600px\", \"background-color\", \"#f3f3f3\"], [1, \"bg-[#27ae60]\", \"text-white\"], [\"nbButton\", \"\", \"ghost\", \"\", \"size\", \"small\", 3, \"click\"], [\"nbButton\", \"\", \"ghost\", \"\", \"status\", \"danger\", \"size\", \"small\", 3, \"click\"]],\n        template: function CategoryManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n            i0.ɵɵtext(5, \"\\u5217\\u8868\\u9801 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"label\", 5);\n            i0.ɵɵtext(9, \"\\u5EFA\\u6848\\u540D\\u7A31 \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"nb-form-field\");\n            i0.ɵɵelement(12, \"input\", 7);\n            i0.ɵɵelementStart(13, \"button\", 8);\n            i0.ɵɵelement(14, \"nb-icon\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 10);\n            i0.ɵɵtemplate(16, CategoryManagementComponent_div_16_Template, 4, 0, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"div\", 12)(18, \"table\", 13)(19, \"thead\")(20, \"tr\", 14)(21, \"th\", 15);\n            i0.ɵɵtext(22, \"\\u6D41\\u6C34\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"th\", 15);\n            i0.ɵɵtext(24, \"\\u68DF\\u5225\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 15);\n            i0.ɵɵtext(26, \"\\u6A13\\u5C64\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"th\", 15);\n            i0.ɵɵtext(28, \"\\u6236\\u6578\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"th\", 15);\n            i0.ɵɵtext(30, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"tbody\");\n            i0.ɵɵtemplate(32, CategoryManagementComponent_tr_32_Template, 13, 5, \"tr\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelement(33, \"nb-card-footer\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, CategoryManagementComponent_ng_template_34_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngForOf\", ctx.productList);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, SharedModule, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbButtonComponent, i2.NbTabsetComponent, i2.NbTabComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbSuffixDirective, i2.NbIconComponent, i6.BreadcrumbComponent]\n      });\n    }\n  }\n  return CategoryManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}