{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nlet ApproveWaiting3Component = class ApproveWaiting3Component {};\nApproveWaiting3Component = __decorate([Component({\n  selector: 'app-approve-waiting-buildcasefile',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting-buildcasefile.component.html',\n  styleUrl: './approve-waiting-buildcasefile.component.css'\n})], ApproveWaiting3Component);\nexport { ApproveWaiting3Component };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}