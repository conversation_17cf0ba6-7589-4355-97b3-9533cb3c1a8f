{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PluralPipe {\n  transform(input, label, pluralLabel = '') {\n    input = input || 0;\n    return input === 1 ? `${input} ${label}` : pluralLabel ? `${input} ${pluralLabel}` : `${input} ${label}s`;\n  }\n  static {\n    this.ɵfac = function PluralPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PluralPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"ngxPlural\",\n      type: PluralPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["PluralPipe", "transform", "input", "label", "plural<PERSON><PERSON>l", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\plural.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'ngxPlural',\r\n    standalone: true\r\n})\r\nexport class PluralPipe implements PipeTransform {\r\n\r\n  transform(input: number, label: string, pluralLabel: string = ''): string {\r\n    input = input || 0;\r\n    return input === 1\r\n      ? `${input} ${label}`\r\n      : pluralLabel\r\n        ? `${input} ${pluralLabel}`\r\n        : `${input} ${label}s`;\r\n  }\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,UAAU;EAErBC,SAASA,CAACC,KAAa,EAAEC,KAAa,EAAEC,WAAA,GAAsB,EAAE;IAC9DF,KAAK,GAAGA,KAAK,IAAI,CAAC;IAClB,OAAOA,KAAK,KAAK,CAAC,GACd,GAAGA,KAAK,IAAIC,KAAK,EAAE,GACnBC,WAAW,GACT,GAAGF,KAAK,IAAIE,WAAW,EAAE,GACzB,GAAGF,KAAK,IAAIC,KAAK,GAAG;EAC5B;;;uCATWH,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAK,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}