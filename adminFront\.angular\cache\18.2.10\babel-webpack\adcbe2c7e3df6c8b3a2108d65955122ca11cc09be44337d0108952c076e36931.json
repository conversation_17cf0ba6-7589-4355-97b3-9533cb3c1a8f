{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * ANSI X.923 padding strategy.\n   */\n  CryptoJS.pad.AnsiX923 = {\n    pad: function (data, blockSize) {\n      // Shortcuts\n      var dataSigBytes = data.sigBytes;\n      var blockSizeBytes = blockSize * 4;\n\n      // Count padding bytes\n      var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes;\n\n      // Compute last byte position\n      var lastBytePos = dataSigBytes + nPaddingBytes - 1;\n\n      // Pad\n      data.clamp();\n      data.words[lastBytePos >>> 2] |= nPaddingBytes << 24 - lastBytePos % 4 * 8;\n      data.sigBytes += nPaddingBytes;\n    },\n    unpad: function (data) {\n      // Get number of padding bytes from last byte\n      var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n      // Remove padding\n      data.sigBytes -= nPaddingBytes;\n    }\n  };\n  return CryptoJS.pad.Ansix923;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}