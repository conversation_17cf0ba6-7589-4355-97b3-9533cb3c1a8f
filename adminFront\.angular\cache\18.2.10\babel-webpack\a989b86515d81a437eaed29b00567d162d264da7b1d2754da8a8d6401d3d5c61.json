{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularChangeItemGetSumaryRegularChangeItemPost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetSumaryRegularChangeItemPost$Json.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularChangeItemGetSumaryRegularChangeItemPost$Json.PATH = '/api/RegularChangeItem/GetSumaryRegularChangeItem';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularChangeItemGetSumaryRegularChangeItemPost$Json", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\regular-change-item\\api-regular-change-item-get-sumary-regular-change-item-post-json.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetSumaryRegularChangeItemResListResponseBase } from '../../models/get-sumary-regular-change-item-res-list-response-base';\r\n\r\nexport interface ApiRegularChangeItemGetSumaryRegularChangeItemPost$Json$Params {\r\n}\r\n\r\nexport function apiRegularChangeItemGetSumaryRegularChangeItemPost$Json(http: HttpClient, rootUrl: string, params?: ApiRegularChangeItemGetSumaryRegularChangeItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetSumaryRegularChangeItemResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularChangeItemGetSumaryRegularChangeItemPost$Json.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'json', accept: 'text/json', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetSumaryRegularChangeItemResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularChangeItemGetSumaryRegularChangeItemPost$Json.PATH = '/api/RegularChangeItem/GetSumaryRegularChangeItem';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,uDAAuDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAuE,EAAEC,OAAqB;EACvM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,uDAAuD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC5G,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN;EAAO,CAAE,CAAC,CACjE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAsE;EAC/E,CAAC,CAAC,CACH;AACH;AAEAZ,uDAAuD,CAACM,IAAI,GAAG,mDAAmD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}