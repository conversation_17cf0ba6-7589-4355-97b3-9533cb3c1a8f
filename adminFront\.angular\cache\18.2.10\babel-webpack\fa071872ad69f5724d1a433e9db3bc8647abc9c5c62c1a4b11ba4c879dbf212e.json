{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport differenceInCalendarYears from \"../differenceInCalendarYears/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInYears\n * @category Year Helpers\n * @summary Get the number of full years between the given dates.\n *\n * @description\n * Get the number of full years between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInYears(new Date(2015, 1, 11), new Date(2013, 11, 31))\n * //=> 1\n */\nexport default function differenceInYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarYears(dateLeft, dateRight));\n\n  // Set both dates to a valid leap year for accurate comparison when dealing\n  // with leap days\n  dateLeft.setFullYear(1584);\n  dateRight.setFullYear(1584);\n\n  // Math.abs(diff in full years - diff in calendar years) === 1 if last calendar year is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastYearNotFull = compareAsc(dateLeft, dateRight) === -sign;\n  var result = sign * (difference - Number(isLastYearNotFull));\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["toDate", "differenceInCalendarYears", "compareAsc", "requiredArgs", "differenceInYears", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "sign", "difference", "Math", "abs", "setFullYear", "isLastYearNotFull", "result", "Number"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/differenceInYears/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport differenceInCalendarYears from \"../differenceInCalendarYears/index.js\";\nimport compareAsc from \"../compareAsc/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInYears\n * @category Year Helpers\n * @summary Get the number of full years between the given dates.\n *\n * @description\n * Get the number of full years between the given dates.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of full years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInYears(new Date(2015, 1, 11), new Date(2013, 11, 31))\n * //=> 1\n */\nexport default function differenceInYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  var sign = compareAsc(dateLeft, dateRight);\n  var difference = Math.abs(differenceInCalendarYears(dateLeft, dateRight));\n\n  // Set both dates to a valid leap year for accurate comparison when dealing\n  // with leap days\n  dateLeft.setFullYear(1584);\n  dateRight.setFullYear(1584);\n\n  // Math.abs(diff in full years - diff in calendar years) === 1 if last calendar year is not full\n  // If so, result must be decreased by 1 in absolute value\n  var isLastYearNotFull = compareAsc(dateLeft, dateRight) === -sign;\n  var result = sign * (difference - Number(isLastYearNotFull));\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,yBAAyB,MAAM,uCAAuC;AAC7E,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACvEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGR,MAAM,CAACK,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGT,MAAM,CAACM,cAAc,CAAC;EACtC,IAAII,IAAI,GAAGR,UAAU,CAACM,QAAQ,EAAEC,SAAS,CAAC;EAC1C,IAAIE,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACZ,yBAAyB,CAACO,QAAQ,EAAEC,SAAS,CAAC,CAAC;;EAEzE;EACA;EACAD,QAAQ,CAACM,WAAW,CAAC,IAAI,CAAC;EAC1BL,SAAS,CAACK,WAAW,CAAC,IAAI,CAAC;;EAE3B;EACA;EACA,IAAIC,iBAAiB,GAAGb,UAAU,CAACM,QAAQ,EAAEC,SAAS,CAAC,KAAK,CAACC,IAAI;EACjE,IAAIM,MAAM,GAAGN,IAAI,IAAIC,UAAU,GAAGM,MAAM,CAACF,iBAAiB,CAAC,CAAC;EAC5D;EACA,OAAOC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}