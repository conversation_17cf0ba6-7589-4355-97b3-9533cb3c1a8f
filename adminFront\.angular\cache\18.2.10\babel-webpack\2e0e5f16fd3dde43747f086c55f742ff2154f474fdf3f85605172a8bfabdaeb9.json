{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ApproveWaiting1Component {\n  static {\n    this.ɵfac = function ApproveWaiting1Component_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApproveWaiting1Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApproveWaiting1Component,\n      selectors: [[\"app-approve-waiting1\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"type\", \"1\"]],\n      template: function ApproveWaiting1Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-approve-waiting\", 0);\n        }\n      },\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcHJvdmUtd2FpdGluZzEuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJmaWxlIjoiYXBwcm92ZS13YWl0aW5nMS5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xuICBkaXNwbGF5OiBibG9jaztcbn1cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYXBwcm92ZS13YWl0aW5nMS9hcHByb3ZlLXdhaXRpbmcxLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBLDRUQUE0VCIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgZGlzcGxheTogYmxvY2s7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["ApproveWaiting1Component", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ApproveWaiting1Component_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting1\\approve-waiting1.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting1\\approve-waiting1.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n  selector: 'app-approve-waiting1',\n  standalone: true,\n  imports: [],\n  templateUrl: './approve-waiting1.component.html',\n  styleUrl: './approve-waiting1.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class ApproveWaiting1Component { }\n", "<app-approve-waiting type=\"1\"></app-approve-waiting>\n"], "mappings": ";AAUA,OAAM,MAAOA,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVrCP,EAAA,CAAAS,SAAA,6BAAoD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}