{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInSecond } from \"../constants/index.js\";\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\nexport default function secondsToMilliseconds(seconds) {\n  requiredArgs(1, arguments);\n  return seconds * millisecondsInSecond;\n}", "map": {"version": 3, "names": ["requiredArgs", "millisecondsInSecond", "secondsToMilliseconds", "seconds", "arguments"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/secondsToMilliseconds/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInSecond } from \"../constants/index.js\";\n/**\n * @name secondsToMilliseconds\n * @category Conversion Helpers\n * @summary Convert seconds to milliseconds.\n *\n * @description\n * Convert a number of seconds to a full number of milliseconds.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 seconds into milliseconds\n * const result = secondsToMilliseconds(2)\n * //=> 2000\n */\nexport default function secondsToMilliseconds(seconds) {\n  requiredArgs(1, arguments);\n  return seconds * millisecondsInSecond;\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOD,OAAO,GAAGF,oBAAoB;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}