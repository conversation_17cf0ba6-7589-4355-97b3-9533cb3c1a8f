{"ast": null, "code": "import { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\nimport * as i0 from \"@angular/core\";\nexport class ApproveWaiting2Component {\n  static {\n    this.ɵfac = function ApproveWaiting2Component_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApproveWaiting2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ApproveWaiting2Component,\n      selectors: [[\"app-approve-waiting2\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"type\"]],\n      template: function ApproveWaiting2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-approve-waiting\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"type\", 2);\n        }\n      },\n      dependencies: [ApproveWaitingComponent],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcHJvdmUtd2FpdGluZzIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJmaWxlIjoiYXBwcm92ZS13YWl0aW5nMi5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xuICBkaXNwbGF5OiBibG9jaztcbn1cbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvYXBwcm92ZS13YWl0aW5nMi9hcHByb3ZlLXdhaXRpbmcyLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBLDRUQUE0VCIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgZGlzcGxheTogYmxvY2s7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ApproveWaitingComponent", "ApproveWaiting2Component", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ApproveWaiting2Component_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting2\\approve-waiting2.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\approve-waiting2\\approve-waiting2.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { ApproveWaitingComponent } from '../approve-waiting/approve-waiting.component';\n\n@Component({\n  selector: 'app-approve-waiting2',\n  standalone: true,\n  imports: [ApproveWaitingComponent],\n  templateUrl: './approve-waiting2.component.html',\n  styleUrl: './approve-waiting2.component.css',\n})\nexport class ApproveWaiting2Component { }\n", "<app-approve-waiting [type]=\"2\"></app-approve-waiting>"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,8CAA8C;;AAStF,OAAM,MAAOC,wBAAwB;;;uCAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVrCP,EAAA,CAAAS,SAAA,6BAAsD;;;UAAjCT,EAAA,CAAAU,UAAA,WAAU;;;qBDMnBf,uBAAuB;MAAAgB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}