{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class StatusPipe {\n  transform(value) {\n    if (value === 0) {\n      return '停用';\n    } else if (value === 1) {\n      return '啟用';\n    } else if (value === 9) {\n      return '刪除';\n    }\n  }\n  static {\n    this.ɵfac = function StatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getStatusName\",\n      type: StatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class DocumentStatusPipe {\n  transform(value) {\n    if (value === 1) {\n      return '待審核'; //'To be reviewed'\n    } else if (value === 2) {\n      return '已駁回'; //'Rejected'\n    } else if (value === 3) {\n      return '待客戶簽回'; //'To be signed back by the customer'\n    } else if (value === 4) {\n      return '客戶已簽回'; //'The customer has signed back'\n    } else return '';\n  }\n  static {\n    this.ɵfac = function DocumentStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DocumentStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getDocumentStatus\",\n      type: DocumentStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class StatusMailPipe {\n  transform(value) {\n    if (value === 0) {\n      return '停用';\n    } else if (value === 1) {\n      return '啟用';\n    }\n  }\n  static {\n    this.ɵfac = function StatusMailPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || StatusMailPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getStatusMailName\",\n      type: StatusMailPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class TypeMailPipe {\n  transform(value) {\n    if (value === 1) {\n      return '簽署完成';\n    } else if (value === 2) {\n      return '已預約客變';\n    } else {\n      return '全部';\n    }\n  }\n  static {\n    this.ɵfac = function TypeMailPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TypeMailPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getTypeMailName\",\n      type: TypeMailPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class DefaultKeyPipe {\n  transform(value) {\n    if (value) {\n      return '是';\n    } else {\n      return '否';\n    }\n  }\n  static {\n    this.ɵfac = function DefaultKeyPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultKeyPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getDefaultKeyName\",\n      type: DefaultKeyPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class ApprovalWaitingPipe {\n  transform(value) {\n    if (value == 1) {\n      return '客變圖上傳';\n    }\n    if (value == 2) {\n      return '確認客變圖';\n    }\n    if (value == 3) {\n      return '相關文件';\n    }\n    if (value == 4) {\n      return '客變原則';\n    }\n    if (value == 5) {\n      return '審閱文件';\n    }\n  }\n  static {\n    this.ɵfac = function ApprovalWaitingPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApprovalWaitingPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getTypeApprovalWaiting\",\n      type: ApprovalWaitingPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class TaskStatusPipe {\n  transform(value) {\n    if (value === 0) {\n      return '處理完成';\n    } else if (value === 1) {\n      return '等待中';\n    } else if (value === 2) {\n      return '處理中';\n    } else if (value === 3) {\n      return '資料異常';\n    }\n  }\n  static {\n    this.ɵfac = function TaskStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TaskStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getTaskStatusName\",\n      type: TaskStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class TaskLogStatusPipe {\n  transform(value) {\n    if (value === 0) {\n      return '未處理';\n    } else if (value === 1) {\n      return '已處理';\n    }\n  }\n  static {\n    this.ɵfac = function TaskLogStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TaskLogStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getTaskLogStatusName\",\n      type: TaskLogStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class PlanUsePipe {\n  transform(value) {\n    if (value === true) {\n      return '選樣';\n    } else if (value === false) {\n      return '方案';\n    }\n  }\n  static {\n    this.ɵfac = function PlanUsePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlanUsePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getPlanUse\",\n      type: PlanUsePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class SettingTimeStatusPipe {\n  transform(cChangeStartDate, cChangeEndDate, isStatus) {\n    const now = new Date();\n    // Convert datetime string to Date\n    const startDate = cChangeStartDate ? new Date(cChangeStartDate) : null;\n    const endDate = cChangeEndDate ? new Date(cChangeEndDate) : null;\n    if (isStatus && cChangeStartDate && cChangeEndDate) {\n      return `${cChangeStartDate.split(\"T\")[0]} ~ ${cChangeEndDate.split(\"T\")[0]}`;\n    }\n    if (!startDate && !endDate) {\n      return '未設定';\n    }\n    if (startDate && endDate && startDate <= now && now <= endDate) {\n      return '已開放';\n    }\n    if (endDate && endDate < now) {\n      return '已結束';\n    }\n    if (startDate && startDate > now) {\n      return '未開放';\n    }\n    return '__'; // Unknown case\n  }\n  static {\n    this.ɵfac = function SettingTimeStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimeStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getSettingTimeStatus\",\n      type: SettingTimeStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class cApproveStatusPipe {\n  transform(value) {\n    if (value === 0) {\n      return '待審核';\n    }\n    if (value === 1) {\n      return '審核通過';\n    }\n    if (value === 2) {\n      return '退回';\n    }\n  }\n  static {\n    this.ɵfac = function cApproveStatusPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || cApproveStatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"cApproveStatus\",\n      type: cApproveStatusPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class LabelInOptionsPipe {\n  transform(status, statusOptions) {\n    if (status === null) {\n      return '';\n    }\n    const matchingOption = statusOptions.find(option => option.value === status);\n    return matchingOption ? matchingOption.label : '';\n  }\n  static {\n    this.ɵfac = function LabelInOptionsPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LabelInOptionsPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"getLabelInOptions\",\n      type: LabelInOptionsPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["StatusPipe", "transform", "value", "pure", "standalone", "DocumentStatusPipe", "StatusMailPipe", "TypeMailPipe", "DefaultKeyPipe", "ApprovalWaitingPipe", "TaskStatusPipe", "TaskLogStatusPipe", "PlanUsePipe", "SettingTimeStatusPipe", "cChangeStartDate", "cChangeEndDate", "isStatus", "now", "Date", "startDate", "endDate", "split", "cApproveStatusPipe", "LabelInOptionsPipe", "status", "statusOptions", "matchingOption", "find", "option", "label"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\mapping.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'getStatusName',\r\n  standalone: true\r\n})\r\nexport class StatusPipe implements PipeTransform {\r\n  transform(value?: number): any {\r\n    if (value === 0) {\r\n      return '停用';\r\n    } else if (value === 1) {\r\n      return '啟用';\r\n    } else if (value === 9) {\r\n      return '刪除';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getDocumentStatus',\r\n  standalone: true\r\n})\r\nexport class DocumentStatusPipe implements PipeTransform {\r\n  transform(value?: number): any {\r\n    if (value === 1) {\r\n      return '待審核'; //'To be reviewed'\r\n    } else if (value === 2) {\r\n      return '已駁回'; //'Rejected'\r\n    } else if (value === 3) {\r\n      return '待客戶簽回'; //'To be signed back by the customer'\r\n    } else if (value === 4) {\r\n      return '客戶已簽回'; //'The customer has signed back'\r\n    } else return ''\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getStatusMailName',\r\n  standalone: true\r\n})\r\nexport class StatusMailPipe implements PipeTransform {\r\n  transform(value?: number): any {\r\n    if (value === 0) {\r\n      return '停用';\r\n    } else if (value === 1) {\r\n      return '啟用';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getTypeMailName',\r\n  standalone: true\r\n})\r\nexport class TypeMailPipe implements PipeTransform {\r\n  transform(value?: number): any {\r\n    if (value === 1) {\r\n      return '簽署完成';\r\n    } else if (value === 2) {\r\n      return '已預約客變';\r\n    } else {\r\n      return '全部';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getDefaultKeyName',\r\n  standalone: true\r\n})\r\nexport class DefaultKeyPipe implements PipeTransform {\r\n  transform(value: boolean): any {\r\n    if (value) {\r\n      return '是';\r\n    } else {\r\n      return '否';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getTypeApprovalWaiting',\r\n  standalone: true\r\n})\r\nexport class ApprovalWaitingPipe implements PipeTransform {\r\n  transform(value: number): any {\r\n    if (value == 1) {\r\n      return '客變圖上傳';\r\n    }\r\n    if (value == 2) {\r\n      return '確認客變圖';\r\n    }\r\n    if (value == 3) {\r\n      return '相關文件';\r\n    }\r\n    if (value == 4) {\r\n      return '客變原則';\r\n    }\r\n    if (value == 5) {\r\n      return '審閱文件';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getTaskStatusName',\r\n  standalone: true\r\n})\r\nexport class TaskStatusPipe implements PipeTransform {\r\n  transform(value: number): any {\r\n    if (value === 0) {\r\n      return '處理完成';\r\n    } else if (value === 1) {\r\n      return '等待中';\r\n    } else if (value === 2) {\r\n      return '處理中';\r\n    } else if (value === 3) {\r\n      return '資料異常';\r\n    }\r\n  }\r\n}\r\n\r\n\r\n@Pipe({\r\n  name: 'getTaskLogStatusName',\r\n  standalone: true\r\n})\r\nexport class TaskLogStatusPipe implements PipeTransform {\r\n  transform(value: number): any {\r\n    if (value === 0) {\r\n      return '未處理';\r\n    } else if (value === 1) {\r\n      return '已處理';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getPlanUse',\r\n  standalone: true\r\n})\r\nexport class PlanUsePipe implements PipeTransform {\r\n  transform(value: boolean): any {\r\n    if (value === true) {\r\n      return '選樣';\r\n    } else if (value === false) {\r\n      return '方案';\r\n    }\r\n  }\r\n}\r\n\r\n\r\n@Pipe({\r\n  name: 'getSettingTimeStatus',\r\n  standalone: true\r\n})\r\nexport class SettingTimeStatusPipe implements PipeTransform {\r\n  transform(cChangeStartDate: string | null, cChangeEndDate: string | null, isStatus: boolean): string {\r\n    const now = new Date();\r\n\r\n    // Convert datetime string to Date\r\n    const startDate = cChangeStartDate ? new Date(cChangeStartDate) : null;\r\n    const endDate = cChangeEndDate ? new Date(cChangeEndDate) : null;\r\n    if (isStatus && cChangeStartDate && cChangeEndDate) {\r\n      return `${cChangeStartDate.split(\"T\")[0]} ~ ${cChangeEndDate.split(\"T\")[0]}`;\r\n    }\r\n\r\n    if (!startDate && !endDate) {\r\n      return '未設定';\r\n    }\r\n\r\n    if (startDate && endDate && startDate <= now && now <= endDate) {\r\n      return '已開放';\r\n    }\r\n\r\n    if (endDate && endDate < now) {\r\n      return '已結束';\r\n    }\r\n\r\n    if (startDate && startDate > now) {\r\n      return '未開放';\r\n    }\r\n\r\n    return '__'; // Unknown case\r\n  }\r\n}\r\n\r\n\r\n@Pipe({\r\n  name: 'cApproveStatus',\r\n  standalone: true\r\n})\r\nexport class cApproveStatusPipe implements PipeTransform {\r\n  transform(value: number): any {\r\n    if (value === 0) {\r\n      return '待審核';\r\n    }\r\n    if (value === 1) {\r\n      return '審核通過';\r\n    }\r\n    if (value === 2) {\r\n      return '退回';\r\n    }\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'getLabelInOptions' ,\r\n  standalone: true\r\n})\r\nexport class LabelInOptionsPipe implements PipeTransform   \r\n {\r\n  transform(status:number | undefined | null, statusOptions: { value: number, label: string }[]): string {\r\n    if (status === null) { \r\n      return ''; \r\n    }\r\n\r\n    const matchingOption = statusOptions.find(option => option.value === status);\r\n    return matchingOption ? matchingOption.label : '';\r\n  }\r\n\r\n}"], "mappings": ";AAMA,OAAM,MAAOA,UAAU;EACrBC,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,IAAI;IACb;EACF;;;uCATWF,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAG,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAgBvB,OAAM,MAAOC,kBAAkB;EAC7BJ,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,KAAK,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,OAAO,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,OAAO,CAAC,CAAC;IAClB,CAAC,MAAM,OAAO,EAAE;EAClB;;;uCAXWG,kBAAkB;IAAA;EAAA;;;;YAAlBA,kBAAkB;MAAAF,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAkB/B,OAAM,MAAOE,cAAc;EACzBL,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,IAAI;IACb;EACF;;;uCAPWI,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAH,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAc3B,OAAM,MAAOG,YAAY;EACvBN,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,MAAM;IACf,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;;uCATWK,YAAY;IAAA;EAAA;;;;YAAZA,YAAY;MAAAJ,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAgBzB,OAAM,MAAOI,cAAc;EACzBP,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,EAAE;MACT,OAAO,GAAG;IACZ,CAAC,MAAM;MACL,OAAO,GAAG;IACZ;EACF;;;uCAPWM,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAL,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAc3B,OAAM,MAAOK,mBAAmB;EAC9BR,SAASA,CAACC,KAAa;IACrB,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,OAAO;IAChB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,OAAO;IAChB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,MAAM;IACf;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,MAAM;IACf;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,MAAM;IACf;EACF;;;uCAjBWO,mBAAmB;IAAA;EAAA;;;;YAAnBA,mBAAmB;MAAAN,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAwBhC,OAAM,MAAOM,cAAc;EACzBT,SAASA,CAACC,KAAa;IACrB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,MAAM;IACf,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,MAAM;IACf;EACF;;;uCAXWQ,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAP,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAmB3B,OAAM,MAAOO,iBAAiB;EAC5BV,SAASA,CAACC,KAAa;IACrB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,KAAK;IACd,CAAC,MAAM,IAAIA,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;EACF;;;uCAPWS,iBAAiB;IAAA;EAAA;;;;YAAjBA,iBAAiB;MAAAR,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAc9B,OAAM,MAAOQ,WAAW;EACtBX,SAASA,CAACC,KAAc;IACtB,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,KAAK,KAAK,KAAK,EAAE;MAC1B,OAAO,IAAI;IACb;EACF;;;uCAPWU,WAAW;IAAA;EAAA;;;;YAAXA,WAAW;MAAAT,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAexB,OAAM,MAAOS,qBAAqB;EAChCZ,SAASA,CAACa,gBAA+B,EAAEC,cAA6B,EAAEC,QAAiB;IACzF,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IAEtB;IACA,MAAMC,SAAS,GAAGL,gBAAgB,GAAG,IAAII,IAAI,CAACJ,gBAAgB,CAAC,GAAG,IAAI;IACtE,MAAMM,OAAO,GAAGL,cAAc,GAAG,IAAIG,IAAI,CAACH,cAAc,CAAC,GAAG,IAAI;IAChE,IAAIC,QAAQ,IAAIF,gBAAgB,IAAIC,cAAc,EAAE;MAClD,OAAO,GAAGD,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAMN,cAAc,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9E;IAEA,IAAI,CAACF,SAAS,IAAI,CAACC,OAAO,EAAE;MAC1B,OAAO,KAAK;IACd;IAEA,IAAID,SAAS,IAAIC,OAAO,IAAID,SAAS,IAAIF,GAAG,IAAIA,GAAG,IAAIG,OAAO,EAAE;MAC9D,OAAO,KAAK;IACd;IAEA,IAAIA,OAAO,IAAIA,OAAO,GAAGH,GAAG,EAAE;MAC5B,OAAO,KAAK;IACd;IAEA,IAAIE,SAAS,IAAIA,SAAS,GAAGF,GAAG,EAAE;MAChC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAAC,CAAC;EACf;;;uCA5BWJ,qBAAqB;IAAA;EAAA;;;;YAArBA,qBAAqB;MAAAV,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAoClC,OAAM,MAAOkB,kBAAkB;EAC7BrB,SAASA,CAACC,KAAa;IACrB,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,KAAK;IACd;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,MAAM;IACf;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,IAAI;IACb;EACF;;;uCAXWoB,kBAAkB;IAAA;EAAA;;;;YAAlBA,kBAAkB;MAAAnB,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAkB/B,OAAM,MAAOmB,kBAAkB;EAE7BtB,SAASA,CAACuB,MAAgC,EAAEC,aAAiD;IAC3F,IAAID,MAAM,KAAK,IAAI,EAAE;MACnB,OAAO,EAAE;IACX;IAEA,MAAME,cAAc,GAAGD,aAAa,CAACE,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC1B,KAAK,KAAKsB,MAAM,CAAC;IAC5E,OAAOE,cAAc,GAAGA,cAAc,CAACG,KAAK,GAAG,EAAE;EACnD;;;uCATWN,kBAAkB;IAAA;EAAA;;;;YAAlBA,kBAAkB;MAAApB,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}