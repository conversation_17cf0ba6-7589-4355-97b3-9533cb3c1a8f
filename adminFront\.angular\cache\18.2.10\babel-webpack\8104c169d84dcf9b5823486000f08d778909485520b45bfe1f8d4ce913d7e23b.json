{"ast": null, "code": "import { DEFAULT_THEME as baseTheme } from '@nebular/theme';\nconst baseThemeVariables = baseTheme.variables;\nexport const DEFAULT_THEME = {\n  name: 'default',\n  base: 'default',\n  variables: {\n    temperature: {\n      arcFill: [baseThemeVariables['primary'], baseThemeVariables['primary'], baseThemeVariables['primary'], baseThemeVariables['primary'], baseThemeVariables['primary']],\n      arcEmpty: baseThemeVariables['bg2'],\n      thumbBg: baseThemeVariables['bg2'],\n      thumbBorder: baseThemeVariables['primary']\n    },\n    solar: {\n      gradientLeft: baseThemeVariables['primary'],\n      gradientRight: baseThemeVariables['primary'],\n      shadowColor: 'rgba(0, 0, 0, 0)',\n      secondSeriesFill: baseThemeVariables['bg2'],\n      radius: ['80%', '90%']\n    },\n    traffic: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      yAxisSplitLine: baseThemeVariables['separator'],\n      lineBg: baseThemeVariables['border4'],\n      lineShadowBlur: '1',\n      itemColor: baseThemeVariables['border4'],\n      itemBorderColor: baseThemeVariables['border4'],\n      itemEmphasisBorderColor: baseThemeVariables['primary'],\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\n      gradFrom: baseThemeVariables['bg2'],\n      gradTo: baseThemeVariables['bg2']\n    },\n    electricity: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: baseThemeVariables['fgText'],\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      axisLineColor: baseThemeVariables['border3'],\n      xAxisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      lineGradFrom: baseThemeVariables['primary'],\n      lineGradTo: baseThemeVariables['primary'],\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: baseThemeVariables['bg2'],\n      areaGradTo: baseThemeVariables['bg2'],\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    bubbleMap: {\n      titleColor: baseThemeVariables['fgText'],\n      areaColor: baseThemeVariables['bg4'],\n      areaHoverColor: baseThemeVariables['fgHighlight'],\n      areaBorderColor: baseThemeVariables['border5']\n    },\n    profitBarAnimationEchart: {\n      textColor: baseThemeVariables['fgText'],\n      firstAnimationBarColor: baseThemeVariables['primary'],\n      secondAnimationBarColor: baseThemeVariables['success'],\n      splitLineStyleOpacity: '1',\n      splitLineStyleWidth: '1',\n      splitLineStyleColor: baseThemeVariables['separator'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    },\n    trafficBarEchart: {\n      gradientFrom: baseThemeVariables['warningLight'],\n      gradientTo: baseThemeVariables['warning'],\n      shadow: baseThemeVariables['warningLight'],\n      shadowBlur: '0',\n      axisTextColor: baseThemeVariables['fgText'],\n      axisFontSize: '12',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal'\n    },\n    countryOrders: {\n      countryBorderColor: baseThemeVariables['border4'],\n      countryFillColor: baseThemeVariables['bg3'],\n      countryBorderWidth: '1',\n      hoveredCountryBorderColor: baseThemeVariables['primary'],\n      hoveredCountryFillColor: baseThemeVariables['primaryLight'],\n      hoveredCountryBorderWidth: '1',\n      chartAxisLineColor: baseThemeVariables['border4'],\n      chartAxisTextColor: baseThemeVariables['fg'],\n      chartAxisFontSize: '16',\n      chartGradientTo: baseThemeVariables['primary'],\n      chartGradientFrom: baseThemeVariables['primaryLight'],\n      chartAxisSplitLine: baseThemeVariables['separator'],\n      chartShadowLineColor: baseThemeVariables['primaryLight'],\n      chartLineBottomShadowColor: baseThemeVariables['primary'],\n      chartInnerLineColor: baseThemeVariables['bg2']\n    },\n    echarts: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['fgText'],\n      splitLineColor: baseThemeVariables['separator'],\n      itemHoverShadowColor: 'rgba(0, 0, 0, 0.5)',\n      tooltipBackgroundColor: baseThemeVariables['primary'],\n      areaOpacity: '0.7'\n    },\n    chartjs: {\n      axisLineColor: baseThemeVariables['separator'],\n      textColor: baseThemeVariables['fgText']\n    },\n    orders: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '0',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'solid',\n      lineWidth: '4',\n      // first line\n      firstAreaGradFrom: baseThemeVariables['bg3'],\n      firstAreaGradTo: baseThemeVariables['bg3'],\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // second line\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondAreaGradFrom: 'rgba(51, 102, 255, 0.2)',\n      secondAreaGradTo: 'rgba(51, 102, 255, 0)',\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // third line\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['successLight'],\n      thirdAreaGradFrom: 'rgba(0, 214, 143, 0.2)',\n      thirdAreaGradTo: 'rgba(0, 214, 143, 0)',\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    profit: {\n      bg: baseThemeVariables['bg'],\n      textColor: baseThemeVariables['fgText'],\n      axisLineColor: baseThemeVariables['border4'],\n      splitLineColor: baseThemeVariables['separator'],\n      areaOpacity: '1',\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      // first bar\n      firstLineGradFrom: baseThemeVariables['bg3'],\n      firstLineGradTo: baseThemeVariables['bg3'],\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\n      // second bar\n      secondLineGradFrom: baseThemeVariables['primary'],\n      secondLineGradTo: baseThemeVariables['primary'],\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\n      // third bar\n      thirdLineGradFrom: baseThemeVariables['success'],\n      thirdLineGradTo: baseThemeVariables['successLight'],\n      thirdLineShadow: 'rgba(0, 0, 0, 0)'\n    },\n    orderProfitLegend: {\n      firstItem: baseThemeVariables['success'],\n      secondItem: baseThemeVariables['primary'],\n      thirdItem: baseThemeVariables['bg3']\n    },\n    visitors: {\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '1',\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: baseThemeVariables['border4'],\n      axisFontSize: '16',\n      axisTextColor: baseThemeVariables['fg'],\n      yAxisSplitLine: baseThemeVariables['separator'],\n      itemBorderColor: baseThemeVariables['primary'],\n      lineStyle: 'dotted',\n      lineWidth: '6',\n      lineGradFrom: '#ffffff',\n      lineGradTo: '#ffffff',\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: baseThemeVariables['primary'],\n      areaGradTo: baseThemeVariables['primaryLight'],\n      innerLineStyle: 'solid',\n      innerLineWidth: '1',\n      innerAreaGradFrom: baseThemeVariables['success'],\n      innerAreaGradTo: baseThemeVariables['success']\n    },\n    visitorsLegend: {\n      firstIcon: baseThemeVariables['success'],\n      secondIcon: baseThemeVariables['primary']\n    },\n    visitorsPie: {\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      firstPieRadius: ['70%', '90%'],\n      secondPieGradientLeft: baseThemeVariables['warning'],\n      secondPieGradientRight: baseThemeVariables['warningLight'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieRadius: ['60%', '97%'],\n      shadowOffsetX: '0',\n      shadowOffsetY: '0'\n    },\n    visitorsPieLegend: {\n      firstSection: baseThemeVariables['warning'],\n      secondSection: baseThemeVariables['success']\n    },\n    earningPie: {\n      radius: ['65%', '100%'],\n      center: ['50%', '50%'],\n      fontSize: '22',\n      firstPieGradientLeft: baseThemeVariables['success'],\n      firstPieGradientRight: baseThemeVariables['success'],\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieGradientLeft: baseThemeVariables['primary'],\n      secondPieGradientRight: baseThemeVariables['primary'],\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      thirdPieGradientLeft: baseThemeVariables['warning'],\n      thirdPieGradientRight: baseThemeVariables['warning'],\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)'\n    },\n    earningLine: {\n      gradFrom: baseThemeVariables['primary'],\n      gradTo: baseThemeVariables['primary'],\n      tooltipTextColor: baseThemeVariables['fgText'],\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: baseThemeVariables['bg'],\n      tooltipBorderColor: baseThemeVariables['border2'],\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}