// 選中行的樣式
.table-active {
    background-color: #e3f2fd !important;
    border-left: 3px solid #2196f3;
}

// 新增模板按鈕區域樣式
.template-creation-controls {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        font-weight: 500;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        &:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 0.75rem;
        }
    }

    .btn-outline-secondary {
        border-color: #6c757d;
        color: #6c757d;
        transition: all 0.2s ease;

        &:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }
    }

    .text-muted {
        font-size: 0.875rem;
        font-style: italic;
    }

    .text-success {
        font-size: 0.875rem;
        font-weight: 500;
    }
}

// 表格頭部複選框樣式
.table thead th {
    .form-check-input {
        transform: scale(1.1);
        margin: 0;
    }
}

// 表格行複選框樣式
.table tbody td {
    .form-check-input {
        transform: scale(1.1);
        margin: 0;
    }
}

// 選中狀態的行動畫效果
.table tbody tr {
    transition: all 0.2s ease;

    &.table-active {
        animation: selectRow 0.3s ease-out;
    }
}

@keyframes selectRow {
    0% {
        background-color: transparent;
        transform: scale(1);
    }

    50% {
        transform: scale(1.01);
    }

    100% {
        background-color: #e3f2fd;
        transform: scale(1);
    }
}

// 響應式設計
@media (max-width: 768px) {
    .template-creation-controls {
        .d-flex {
            flex-direction: column;
            gap: 0.5rem;

            .d-flex {
                flex-direction: row;
                gap: 0.25rem;
            }
        }

        .btn {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    }
}
