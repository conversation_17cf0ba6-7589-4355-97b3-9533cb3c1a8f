{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/utility.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"src/services/api/services/File.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../@theme/pipes/mapping.pipe\";\nfunction DetailApprovalWaitingComponent_div_2_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u9644\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_30_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_div_30_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleFileClick(item_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"div\", 5)(6, \"span\", 30);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 31);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 33);\n    i0.ɵɵelement(13, \"i\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getFileIcon(item_r3.CFileName || \"\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getDisplayFileName(item_r3), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFileTypeText(item_r3.CFileName || \"\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u9EDE\\u64CA\\u4EE5\", ctx_r3.isImageFile(item_r3.CFileName || \"\") ? \"\\u9810\\u89BD\" : ctx_r3.isPDFString(item_r3.CFileName || \"\") ? \"\\u6AA2\\u8996\" : \"\\u4E0B\\u8F09\", \"\\u6A94\\u6848 \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, DetailApprovalWaitingComponent_div_2_div_30_div_1_Template, 14, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.CFile && (item_r3.CFileName || item_r3.CFile));\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 41)(1, \"th\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 43);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 6, record_r5.CRecordDate ? record_r5.CRecordDate : ctx_r3.approvalWaiting.CCreateDT, \"yyyy/MM/dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", record_r5.CAction === 1 ? \"\\u9001\\u51FA\\u5BE9\\u6838\" : \"\", \" \", record_r5.CAction === 2 ? \"\\u5BE9\\u6838\\u901A\\u904E\" : \"\", \" \", record_r5.CAction === 3 ? \"\\u5BE9\\u6838\\u99C1\\u56DE\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CRemark, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"table\", 36)(2, \"thead\", 37)(3, \"tr\", 38)(4, \"th\", 39);\n    i0.ɵɵtext(5, \" \\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 39);\n    i0.ɵɵtext(7, \" \\u52D5\\u4F5C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 39);\n    i0.ɵɵtext(9, \" \\u4F7F\\u7528\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 39);\n    i0.ɵɵtext(11, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template, 10, 9, \"tr\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CApproveRecord);\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"span\", 7);\n    i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 6)(13, \"span\", 7);\n    i0.ɵɵtext(14, \" \\u985E\\u5225 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"span\", 8);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\", 6)(26, \"span\", 7);\n    i0.ɵɵtext(27, \" \\u6A94\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 8);\n    i0.ɵɵtemplate(29, DetailApprovalWaitingComponent_div_2_div_29_Template, 4, 0, \"div\", 11)(30, DetailApprovalWaitingComponent_div_2_div_30_Template, 2, 1, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 6)(33, \"span\", 7);\n    i0.ɵɵtext(34, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"span\", 8);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 13)(38, \"div\", 14)(39, \"div\", 6)(40, \"span\", 7);\n    i0.ɵɵtext(41, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 15)(43, \"textarea\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.remark, $event) || (ctx_r3.remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 17)(45, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵtext(46, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(false));\n    });\n    i0.ɵɵtext(48, \"\\u99C1\\u56DE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(true));\n    });\n    i0.ɵɵtext(50, \"\\u540C\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, DetailApprovalWaitingComponent_div_2_div_51_Template, 14, 1, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CBuildcaseName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 12, ctx_r3.approvalWaiting.CType), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.approvalWaiting.CFileApproves || ctx_r3.approvalWaiting.CFileApproves.length === 0 || !ctx_r3.hasValidFiles());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CFileApproves);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CApprovalRemark, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.remark);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r3.approvalWaiting);\n  }\n}\nexport class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.fileService = fileService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n      }\n    })).subscribe();\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n  // 檢查是否有有效的檔案\n  hasValidFiles() {\n    if (!this.approvalWaiting?.CFileApproves) {\n      return false;\n    }\n    return this.approvalWaiting.CFileApproves.some(file => file.CFile && (file.CFileName || file.CFile.length > 0));\n  }\n  // 處理檔案點擊事件\n  handleFileClick(file) {\n    const fileName = file.CFileName || file.fileName || '';\n    const displayName = file.CFileName || fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(displayName);\n    const isPdfByName = this.isPDFString(displayName);\n    const isCadByName = this.isCadString(displayName);\n    // 統一使用 GetFile API 取得檔案\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    if (relativePath && serverFileName) {\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      console.error('檔案缺少必要路徑資訊:', file);\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\n    }\n  }\n  // 處理本地檔案的後備方法\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileUrl = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    if (isImage) {\n      const imageUrl = this.getImageSrc(file);\n      this.openImagePreview(imageUrl, fileName);\n    } else if (isPdf) {\n      this.openPdfInBrowser(fileUrl);\n    } else {\n      this.downloadFileDirectly(fileUrl, fileName);\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 取得檔案圖標\n  getFileIcon(fileName) {\n    if (!fileName) return 'fas fa-file text-gray-500';\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'fas fa-file-pdf text-red-500';\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n      case 'bmp':\n      case 'webp':\n        return 'fas fa-file-image text-blue-500';\n      case 'dwg':\n      case 'dxf':\n        return 'fas fa-drafting-compass text-green-500';\n      case 'doc':\n      case 'docx':\n        return 'fas fa-file-word text-blue-600';\n      case 'xls':\n      case 'xlsx':\n        return 'fas fa-file-excel text-green-600';\n      default:\n        return 'fas fa-file text-gray-500';\n    }\n  }\n  // 取得顯示檔案名稱\n  getDisplayFileName(file) {\n    return file.CFileName || file.fileName || '未知檔案';\n  }\n  // 取得檔案類型文字\n  getFileTypeText(fileName) {\n    if (!fileName) return '';\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'pdf':\n        return 'PDF';\n      case 'jpg':\n      case 'jpeg':\n        return 'JPG';\n      case 'png':\n        return 'PNG';\n      case 'gif':\n        return 'GIF';\n      case 'dwg':\n        return 'DWG';\n      case 'dxf':\n        return 'DXF';\n      default:\n        return extension?.toUpperCase() || '';\n    }\n  }\n  // 判斷檔案是否為圖片\n  isImageFile(fileName) {\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 判斷是否為 PDF\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  // 判斷是否為 CAD 檔案\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  // 取得正確的圖片 src，處理 base64 字串\n  getImageSrc(file) {\n    const fileData = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    if (!fileData) {\n      return '';\n    }\n    // 如果已經是完整的 data URL，直接返回\n    if (fileData.startsWith('data:')) {\n      return fileData;\n    }\n    // 如果是 HTTP URL，直接返回\n    if (fileData.startsWith('http')) {\n      return fileData;\n    }\n    // 如果是純 base64 字串，需要添加前綴\n    if (fileData && !fileData.startsWith('data:')) {\n      const extension = fileName.split('.').pop()?.toLowerCase() || 'jpeg';\n      // 根據檔案副檔名決定 MIME 類型\n      let mimeType = 'image/jpeg';\n      switch (extension) {\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'gif':\n          mimeType = 'image/gif';\n          break;\n        case 'webp':\n          mimeType = 'image/webp';\n          break;\n        default:\n          mimeType = 'image/jpeg';\n      }\n      return `data:${mimeType};base64,${fileData}`;\n    }\n    return fileData;\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100%; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n      } else {\n        // 如果無法開啟新視窗，使用直接開啟方式\n        window.open(blobUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 在瀏覽器中打開 PDF（用於本地檔案的後備方案）\n  openPdfInBrowser(fileUrl) {\n    try {\n      if (fileUrl.startsWith('data:application/pdf')) {\n        // 處理 base64 PDF\n        const newWindow = window.open('', '_blank');\n        if (newWindow) {\n          newWindow.document.write(`\n            <html>\n              <head>\n                <title>PDF 檢視</title>\n                <style>body { margin: 0; }</style>\n              </head>\n              <body>\n                <iframe src=\"${fileUrl}\" width=\"100%\" height=\"100%\" style=\"border: none;\"></iframe>\n              </body>\n            </html>\n          `);\n        }\n      } else {\n        window.open(fileUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟 PDF 失敗:', error);\n      this.message.showErrorMSG('無法開啟 PDF 檔案');\n    }\n  }\n  // 直接下載檔案（用於 CAD 和其他類型檔案）\n  downloadFileDirectly(fileUrl, fileName) {\n    try {\n      if (fileUrl.startsWith('data:')) {\n        // 處理 base64 檔案\n        this.downloadBase64File(fileUrl, fileName);\n      } else {\n        // 處理 URL 檔案\n        const link = document.createElement('a');\n        link.href = fileUrl;\n        link.download = fileName;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      }\n    } catch (error) {\n      console.error('下載檔案失敗:', error);\n      this.message.showErrorMSG('無法下載檔案');\n    }\n  }\n  // 下載 base64 檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 從檔案名稱判斷 MIME 類型\n      let mimeType = 'application/octet-stream';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          mimeType = 'application/pdf';\n          break;\n        case 'jpg':\n        case 'jpeg':\n          mimeType = 'image/jpeg';\n          break;\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'dwg':\n          mimeType = 'application/acad';\n          break;\n        case 'dxf':\n          mimeType = 'application/dxf';\n          break;\n      }\n      // 將 base64 轉換為 Blob\n      const byteCharacters = atob(base64Content);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: mimeType\n      });\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('處理 base64 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('處理檔案時發生錯誤');\n    }\n  }\n  // 開啟圖片預覽\n  openImagePreview(imageUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background: #f0f0f0;\n                  display: flex;\n                  justify-content: center;\n                  align-items: center;\n                  min-height: 100vh;\n                }\n                img {\n                  max-width: 100%;\n                  max-height: 100%;\n                  box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n                }\n              </style>\n            </head>\n            <body>\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\n            </body>\n          </html>\n        `);\n      } else {\n        window.open(imageUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      this.message.showErrorMSG('無法預覽圖片');\n    }\n  }\n  static {\n    this.ɵfac = function DetailApprovalWaitingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailApprovalWaitingComponent)(i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService), i0.ɵɵdirectiveInject(i8.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailApprovalWaitingComponent,\n      selectors: [[\"app-detail-approval-waiting\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[\"class\", \"flex flex-col justify-items-center items-center m-auto w-[50%]\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"justify-items-center\", \"items-center\", \"m-auto\", \"w-[50%]\"], [1, \"border-b-2\", \"border-b-black\", \"w-full\"], [1, \"text-xl\", \"font-bold\"], [1, \"px-3\", \"py-4\"], [1, \"flex\", \"items-center\"], [1, \"w-[100px]\"], [1, \"font-bold\"], [1, \"w-[80%]\", \"break-words\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"flex\", \"items-start\", \"mt-3\"], [\"class\", \"no-files\", 4, \"ngIf\"], [\"class\", \"file-item-wrapper\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"w-full\"], [1, \"flex\", \"px-3\", \"py-4\", \"w-full\"], [1, \"w-full\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"mt-3\", \"flex\", \"items-center\"], [\"nbButton\", \"\", 1, \"btn\", \"border-black\", \"border\", \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"danger\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"primary\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"table-responsive relative overflow-x-auto mt-3\", 4, \"ngIf\"], [1, \"no-files\"], [1, \"fas\", \"fa-folder-open\", \"text-gray-400\", \"text-2xl\", \"mb-2\"], [1, \"file-item-wrapper\"], [\"class\", \"file-item cursor-pointer\", 3, \"click\", 4, \"ngIf\"], [1, \"file-item\", \"cursor-pointer\", 3, \"click\"], [1, \"file-icon\", \"mr-3\"], [1, \"text-xl\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-type-badge\"], [1, \"file-action-hint\"], [1, \"flex-shrink-0\", \"ml-3\"], [1, \"fas\", \"fa-external-link-alt\", \"file-action-icon\"], [1, \"table-responsive\", \"relative\", \"overflow-x-auto\", \"mt-3\"], [1, \"table\", \"table-bordered\", \"w-full\", \"text-sm\", \"text-left\", \"rtl:text-right\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"text-xs\", \"text-gray-700\", \"uppercase\", \"bg-gray-50\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\"], [\"class\", \"bg-white text-black\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"text-black\"], [\"scope\", \"row\", 1, \"px-6\", \"py-4\", \"font-medium\", \"whitespace-nowrap\"], [1, \"px-6\", \"py-4\"]],\n      template: function DetailApprovalWaitingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\");\n          i0.ɵɵtemplate(2, DetailApprovalWaitingComponent_div_2_Template, 52, 14, \"div\", 0);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !!ctx.approvalWaiting);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbInputDirective, i10.NbButtonComponent, i11.ApprovalWaitingPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.file-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px;\\n  margin-bottom: 12px;\\n  background: white;\\n}\\n.file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  border-color: #3b82f6;\\n}\\n\\n.file-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f3f4f6;\\n  border-radius: 8px;\\n  transition: background-color 0.3s ease;\\n}\\n.file-icon[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n}\\n\\n.file-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  min-width: 0;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  color: #1d4ed8;\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.file-name[_ngcontent-%COMP%]:hover {\\n  color: #1e40af;\\n}\\n\\n.file-type-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  background: #dbeafe;\\n  color: #1e40af;\\n  border-radius: 999px;\\n  margin-left: 8px;\\n}\\n\\n.file-action-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n\\n.file-action-icon[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n  transition: color 0.3s ease;\\n}\\n.file-action-icon[_ngcontent-%COMP%]:hover {\\n  color: #6b7280;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .file-item[_ngcontent-%COMP%] {\\n    padding: 8px;\\n    margin-bottom: 8px;\\n  }\\n  .file-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .file-name[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .file-action-hint[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n\\n.no-files[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-style: italic;\\n  padding: 20px;\\n  text-align: center;\\n  background: #f9fafb;\\n  border-radius: 8px;\\n  border: 1px dashed #d1d5db;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "EEvent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DetailApprovalWaitingComponent_div_2_div_30_div_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "item_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "handleFileClick", "ɵɵadvance", "ɵɵclassMap", "getFileIcon", "CFileName", "ɵɵtextInterpolate1", "getDisplayFileName", "getFileTypeText", "isImageFile", "isPDFString", "ɵɵtemplate", "DetailApprovalWaitingComponent_div_2_div_30_div_1_Template", "ɵɵproperty", "CFile", "ɵɵpipeBind2", "record_r5", "CRecordDate", "approvalWaiting", "CCreateDT", "ɵɵtextInterpolate3", "CAction", "CCreator", "CRemark", "DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template", "CApproveRecord", "DetailApprovalWaitingComponent_div_2_div_29_Template", "DetailApprovalWaitingComponent_div_2_div_30_Template", "ɵɵtwoWayListener", "DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_43_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "remark", "DetailApprovalWaitingComponent_div_2_Template_button_click_45_listener", "goBack", "DetailApprovalWaitingComponent_div_2_Template_button_click_47_listener", "handleAction", "DetailApprovalWaitingComponent_div_2_Template_button_click_49_listener", "DetailApprovalWaitingComponent_div_2_div_51_Template", "CName", "CBuildcaseName", "ɵɵpipeBind1", "CType", "CFileApproves", "length", "hasValidFiles", "CApprovalRemark", "ɵɵtwoWayProperty", "CIsApprove", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "fileService", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "Entries", "downloadFile", "window", "open", "isApprove", "validation", "errorMessages", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "showSucessMSG", "push", "userName", "Date", "toISOString", "action", "payload", "back", "clear", "required", "some", "file", "fileName", "displayName", "isImageByName", "isPdfByName", "isCadByName", "isCadString", "relativePath", "serverFileName", "getFileFromServer", "console", "error", "showErrorMSG", "handleLocalFile", "isImage", "isPdf", "isCad", "fileUrl", "data", "imageUrl", "getImageSrc", "openImagePreview", "openPdfInBrowser", "downloadFileDirectly", "getFile", "next", "blob", "url", "URL", "createObjectURL", "openPdfInNewWindow", "downloadBlobFile", "setTimeout", "revokeObjectURL", "extension", "split", "pop", "toLowerCase", "toUpperCase", "imageExtensions", "includes", "str", "endsWith", "lowerStr", "fileData", "startsWith", "mimeType", "blobUrl", "newWindow", "document", "write", "link", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "downloadBase64File", "base64Data", "base64Content", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "Blob", "type", "ɵɵdirectiveInject", "i1", "SpecialChangeService", "i2", "ActivatedRoute", "i3", "UtilityService", "i4", "Location", "i5", "MessageService", "i6", "ValidationHelper", "i7", "EventService", "i8", "FileService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailApprovalWaitingComponent_Template", "rf", "ctx", "DetailApprovalWaitingComponent_div_2_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbInputDirective", "NbButtonComponent", "i11", "ApprovalWaitingPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.html"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n  decodeJWT: any\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private fileService: FileService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n\r\n  // 檢查是否有有效的檔案\r\n  hasValidFiles(): boolean {\r\n    if (!this.approvalWaiting?.CFileApproves) {\r\n      return false;\r\n    }\r\n\r\n    return this.approvalWaiting.CFileApproves.some(file =>\r\n      file.CFile && (file.CFileName || file.CFile.length > 0)\r\n    );\r\n  }\r\n  // 處理檔案點擊事件\r\n  handleFileClick(file: any) {\r\n    const fileName = file.CFileName || file.fileName || '';\r\n    const displayName = file.CFileName || fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(displayName);\r\n    const isPdfByName = this.isPDFString(displayName);\r\n    const isCadByName = this.isCadString(displayName);\r\n\r\n    // 統一使用 GetFile API 取得檔案\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    if (relativePath && serverFileName) {\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      console.error('檔案缺少必要路徑資訊:', file);\r\n      this.message.showErrorMSG('檔案路徑資訊不完整，無法開啟檔案');\r\n    }\r\n  }\r\n\r\n  // 處理本地檔案的後備方法\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileUrl = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (isImage) {\r\n      const imageUrl = this.getImageSrc(file);\r\n      this.openImagePreview(imageUrl, fileName);\r\n    } else if (isPdf) {\r\n      this.openPdfInBrowser(fileUrl);\r\n    } else {\r\n      this.downloadFileDirectly(fileUrl, fileName);\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n\r\n        if (isImage) {\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n\r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 取得檔案圖標\r\n  getFileIcon(fileName: string): string {\r\n    if (!fileName) return 'fas fa-file text-gray-500';\r\n\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'fas fa-file-pdf text-red-500';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n      case 'bmp':\r\n      case 'webp':\r\n        return 'fas fa-file-image text-blue-500';\r\n      case 'dwg':\r\n      case 'dxf':\r\n        return 'fas fa-drafting-compass text-green-500';\r\n      case 'doc':\r\n      case 'docx':\r\n        return 'fas fa-file-word text-blue-600';\r\n      case 'xls':\r\n      case 'xlsx':\r\n        return 'fas fa-file-excel text-green-600';\r\n      default:\r\n        return 'fas fa-file text-gray-500';\r\n    }\r\n  }\r\n\r\n  // 取得顯示檔案名稱\r\n  getDisplayFileName(file: any): string {\r\n    return file.CFileName || file.fileName || '未知檔案';\r\n  }\r\n\r\n  // 取得檔案類型文字\r\n  getFileTypeText(fileName: string): string {\r\n    if (!fileName) return '';\r\n\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return 'PDF';\r\n      case 'jpg':\r\n      case 'jpeg':\r\n        return 'JPG';\r\n      case 'png':\r\n        return 'PNG';\r\n      case 'gif':\r\n        return 'GIF';\r\n      case 'dwg':\r\n        return 'DWG';\r\n      case 'dxf':\r\n        return 'DXF';\r\n      default:\r\n        return extension?.toUpperCase() || '';\r\n    }\r\n  }\r\n\r\n  // 判斷檔案是否為圖片\r\n  isImageFile(fileName: string): boolean {\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n\r\n  // 判斷是否為 PDF\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 判斷是否為 CAD 檔案\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 取得正確的圖片 src，處理 base64 字串\r\n  getImageSrc(file: any): string {\r\n    const fileData = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (!fileData) {\r\n      return '';\r\n    }\r\n\r\n    // 如果已經是完整的 data URL，直接返回\r\n    if (fileData.startsWith('data:')) {\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是 HTTP URL，直接返回\r\n    if (fileData.startsWith('http')) {\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是純 base64 字串，需要添加前綴\r\n    if (fileData && !fileData.startsWith('data:')) {\r\n      const extension = fileName.split('.').pop()?.toLowerCase() || 'jpeg';\r\n\r\n      // 根據檔案副檔名決定 MIME 類型\r\n      let mimeType = 'image/jpeg';\r\n      switch (extension) {\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'gif':\r\n          mimeType = 'image/gif';\r\n          break;\r\n        case 'webp':\r\n          mimeType = 'image/webp';\r\n          break;\r\n        default:\r\n          mimeType = 'image/jpeg';\r\n      }\r\n\r\n      return `data:${mimeType};base64,${fileData}`;\r\n    }\r\n\r\n    return fileData;\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100%; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n      } else {\r\n        // 如果無法開啟新視窗，使用直接開啟方式\r\n        window.open(blobUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n\r\n  // 在瀏覽器中打開 PDF（用於本地檔案的後備方案）\r\n  openPdfInBrowser(fileUrl: string) {\r\n    try {\r\n      if (fileUrl.startsWith('data:application/pdf')) {\r\n        // 處理 base64 PDF\r\n        const newWindow = window.open('', '_blank');\r\n        if (newWindow) {\r\n          newWindow.document.write(`\r\n            <html>\r\n              <head>\r\n                <title>PDF 檢視</title>\r\n                <style>body { margin: 0; }</style>\r\n              </head>\r\n              <body>\r\n                <iframe src=\"${fileUrl}\" width=\"100%\" height=\"100%\" style=\"border: none;\"></iframe>\r\n              </body>\r\n            </html>\r\n          `);\r\n        }\r\n      } else {\r\n        window.open(fileUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 失敗:', error);\r\n      this.message.showErrorMSG('無法開啟 PDF 檔案');\r\n    }\r\n  }\r\n\r\n  // 直接下載檔案（用於 CAD 和其他類型檔案）\r\n  downloadFileDirectly(fileUrl: string, fileName: string) {\r\n    try {\r\n      if (fileUrl.startsWith('data:')) {\r\n        // 處理 base64 檔案\r\n        this.downloadBase64File(fileUrl, fileName);\r\n      } else {\r\n        // 處理 URL 檔案\r\n        const link = document.createElement('a');\r\n        link.href = fileUrl;\r\n        link.download = fileName;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      }\r\n    } catch (error) {\r\n      console.error('下載檔案失敗:', error);\r\n      this.message.showErrorMSG('無法下載檔案');\r\n    }\r\n  }\r\n\r\n  // 下載 base64 檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\r\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 從檔案名稱判斷 MIME 類型\r\n      let mimeType = 'application/octet-stream';\r\n      const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n      switch (extension) {\r\n        case 'pdf':\r\n          mimeType = 'application/pdf';\r\n          break;\r\n        case 'jpg':\r\n        case 'jpeg':\r\n          mimeType = 'image/jpeg';\r\n          break;\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'dwg':\r\n          mimeType = 'application/acad';\r\n          break;\r\n        case 'dxf':\r\n          mimeType = 'application/dxf';\r\n          break;\r\n      }\r\n\r\n      // 將 base64 轉換為 Blob\r\n      const byteCharacters = atob(base64Content);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: mimeType });\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理 URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('處理 base64 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('處理檔案時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(imageUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background: #f0f0f0;\r\n                  display: flex;\r\n                  justify-content: center;\r\n                  align-items: center;\r\n                  min-height: 100vh;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  max-height: 100%;\r\n                  box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <img src=\"${imageUrl}\" alt=\"${fileName}\" />\r\n            </body>\r\n          </html>\r\n        `);\r\n      } else {\r\n        window.open(imageUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      this.message.showErrorMSG('無法預覽圖片');\r\n    }\r\n  }\r\n}\r\n", "<nb-card>\r\n  <!-- <nb-card-header></nb-card-header> -->\r\n  <nb-card-body>\r\n    <div *ngIf=\"!!approvalWaiting\" class=\"flex flex-col justify-items-center items-center m-auto w-[50%]\">\r\n      <div class=\"border-b-2 border-b-black w-full\">\r\n        <span class=\"text-xl font-bold\">\r\n          {{approvalWaiting.CName}}\r\n        </span>\r\n        <div class=\"px-3 py-4\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                建案\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CBuildcaseName}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                類別\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CType! | getTypeApprovalWaiting}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                名稱\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CName}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-start mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                檔案\r\n              </span>\r\n            </div>\r\n            <div class=\"w-[80%] break-words\">\r\n              <div\r\n                *ngIf=\"!approvalWaiting.CFileApproves || approvalWaiting.CFileApproves.length === 0 || !hasValidFiles()\"\r\n                class=\"no-files\">\r\n                <i class=\"fas fa-folder-open text-gray-400 text-2xl mb-2\"></i>\r\n                <div>無附件</div>\r\n              </div>\r\n              <div *ngFor=\"let item of approvalWaiting.CFileApproves; let i = index\" class=\"file-item-wrapper\">\r\n                <div *ngIf=\"item.CFile && (item.CFileName || item.CFile)\" class=\"file-item cursor-pointer\"\r\n                  (click)=\"handleFileClick(item)\">\r\n                  <!-- 檔案圖標 -->\r\n                  <div class=\"flex items-center\">\r\n                    <div class=\"file-icon mr-3\">\r\n                      <i [class]=\"getFileIcon(item.CFileName || '')\" class=\"text-xl\"></i>\r\n                    </div> <!-- 檔案資訊 -->\r\n                    <div class=\"file-info\">\r\n                      <div class=\"flex items-center\">\r\n                        <span class=\"file-name\">\r\n                          {{getDisplayFileName(item)}}\r\n                        </span>\r\n                        <span class=\"file-type-badge\">\r\n                          {{getFileTypeText(item.CFileName || '')}}\r\n                        </span>\r\n                      </div>\r\n                      <div class=\"file-action-hint\">\r\n                        點擊以{{isImageFile(item.CFileName || '') ? '預覽' : isPDFString(item.CFileName || '') ? '檢視' :\r\n                        '下載'}}檔案\r\n                      </div>\r\n                    </div> <!-- 操作圖標 -->\r\n                    <div class=\"flex-shrink-0 ml-3\">\r\n                      <i class=\"fas fa-external-link-alt file-action-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                審核說明\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CApprovalRemark}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"mt-2 w-full\">\r\n        <div class=\"flex px-3 py-4 w-full\">\r\n          <div class=\"w-[100px]\">\r\n            <span class=\"font-bold\">\r\n              備註\r\n            </span>\r\n          </div>\r\n          <div class=\"w-full\">\r\n            <textarea nbInput [(ngModel)]=\"remark\" [rows]=\"4\" class=\"resize-none !max-w-full w-full\"></textarea>\r\n            <div class=\"mt-3 flex items-center\">\r\n              <button nbButton class=\"btn border-black border mr-2\" (click)=\"goBack()\">取消</button>\r\n              <button nbButton [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                status=\"danger\" class=\"btn mr-2\" (click)=\"handleAction(false)\">駁回</button>\r\n              <button nbButton [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                status=\"primary\" class=\"btn mr-2\" (click)=\"handleAction(true)\">同意</button>\r\n            </div>\r\n            <div class=\"table-responsive relative overflow-x-auto mt-3\" *ngIf=\"!!approvalWaiting\">\r\n              <table\r\n                class=\"table table-bordered w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400\">\r\n                <thead class=\"text-xs text-gray-700 uppercase bg-gray-50\">\r\n                  <tr style=\"background-color: #27ae60; color: white;\">\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      日期\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      動作\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      使用者\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      備註\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr class=\"bg-white text-black\" *ngFor=\"let record of approvalWaiting.CApproveRecord\">\r\n                    <th scope=\"row\" class=\"px-6 py-4 font-medium whitespace-nowrap\">\r\n                      {{\r\n                      (record.CRecordDate ? record.CRecordDate : approvalWaiting.CCreateDT) |\r\n                      date: \"yyyy/MM/dd HH:mm:ss\"\r\n                      }}\r\n                    </th>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CAction === 1 ? '送出審核' : ''}}\r\n                      {{record.CAction === 2 ? '審核通過' : ''}}\r\n                      {{record.CAction === 3 ? '審核駁回' : ''}}\r\n                    </td>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CCreator}}\r\n                    </td>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CRemark}}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAASA,YAAY,QAAkB,iBAAiB;AAGxD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAI9D,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;;;ICgC9DC,EAAA,CAAAC,cAAA,cAEmB;IACjBD,EAAA,CAAAE,SAAA,YAA8D;IAC9DF,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAG,MAAA,yBAAG;IACVH,EADU,CAAAI,YAAA,EAAM,EACV;;;;;;IAEJJ,EAAA,CAAAC,cAAA,cACkC;IAAhCD,EAAA,CAAAK,UAAA,mBAAAC,gFAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASD,MAAA,CAAAE,eAAA,CAAAL,OAAA,CAAqB;IAAA,EAAC;IAG7BT,EADF,CAAAC,cAAA,aAA+B,cACD;IAC1BD,EAAA,CAAAE,SAAA,YAAmE;IACrEF,EAAA,CAAAI,YAAA,EAAM;IAGFJ,EAFJ,CAAAC,cAAA,cAAuB,aACU,eACL;IACtBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,IAEF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAE,SAAA,aAAyD;IAG/DF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IApBGJ,EAAA,CAAAe,SAAA,GAA2C;IAA3Cf,EAAA,CAAAgB,UAAA,CAAAJ,MAAA,CAAAK,WAAA,CAAAR,OAAA,CAAAS,SAAA,QAA2C;IAK1ClB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAQ,kBAAA,CAAAX,OAAA,OACF;IAEET,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAS,eAAA,CAAAZ,OAAA,CAAAS,SAAA,aACF;IAGAlB,EAAA,CAAAe,SAAA,GAEF;IAFEf,EAAA,CAAAmB,kBAAA,wBAAAP,MAAA,CAAAU,WAAA,CAAAb,OAAA,CAAAS,SAAA,2BAAAN,MAAA,CAAAW,WAAA,CAAAd,OAAA,CAAAS,SAAA,2DAEF;;;;;IApBRlB,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAwB,UAAA,IAAAC,0DAAA,mBACkC;IAyBpCzB,EAAA,CAAAI,YAAA,EAAM;;;;IA1BEJ,EAAA,CAAAe,SAAA,EAAkD;IAAlDf,EAAA,CAAA0B,UAAA,SAAAjB,OAAA,CAAAkB,KAAA,KAAAlB,OAAA,CAAAS,SAAA,IAAAT,OAAA,CAAAkB,KAAA,EAAkD;;;;;IA8EpD3B,EADF,CAAAC,cAAA,aAAsF,aACpB;IAC9DD,EAAA,CAAAG,MAAA,GAIF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAK,EACF;;;;;IAhBDJ,EAAA,CAAAe,SAAA,GAIF;IAJEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAA4B,WAAA,OAAAC,SAAA,CAAAC,WAAA,GAAAD,SAAA,CAAAC,WAAA,GAAAlB,MAAA,CAAAmB,eAAA,CAAAC,SAAA,8BAIF;IAEEhC,EAAA,CAAAe,SAAA,GAGF;IAHEf,EAAA,CAAAiC,kBAAA,MAAAJ,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,8CAGF;IAEElC,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAU,SAAA,CAAAM,QAAA,MACF;IAEEnC,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAU,SAAA,CAAAO,OAAA,MACF;;;;;IAhCApC,EALR,CAAAC,cAAA,cAAsF,gBAEoB,gBAC5C,aACH,aACjB;IAChCD,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAkC;IAChCD,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAkC;IAChCD,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAG,MAAA,sBACF;IAEJH,EAFI,CAAAI,YAAA,EAAK,EACF,EACC;IACRJ,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAwB,UAAA,KAAAa,0DAAA,kBAAsF;IAqB5FrC,EAFI,CAAAI,YAAA,EAAQ,EACF,EACJ;;;;IArBmDJ,EAAA,CAAAe,SAAA,IAAiC;IAAjCf,EAAA,CAAA0B,UAAA,YAAAd,MAAA,CAAAmB,eAAA,CAAAO,cAAA,CAAiC;;;;;;IA7H9FtC,EAFJ,CAAAC,cAAA,aAAsG,aACtD,cACZ;IAC9BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIDJ,EAHN,CAAAC,cAAA,aAAuB,aACU,aACN,cACG;IACtBD,EAAA,CAAAG,MAAA,qBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,eAAmC,cACV,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,cAAiC;IAO/BD,EANA,CAAAwB,UAAA,KAAAe,oDAAA,kBAEmB,KAAAC,oDAAA,kBAI8E;IA6BrGxC,EADE,CAAAI,YAAA,EAAM,EACF;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,kCACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;IAIAJ,EAHN,CAAAC,cAAA,eAAyB,eACY,cACV,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAEJJ,EADF,CAAAC,cAAA,eAAoB,oBACuE;IAAvED,EAAA,CAAAyC,gBAAA,2BAAAC,iFAAAC,MAAA;MAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAhC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAA6C,kBAAA,CAAAjC,MAAA,CAAAkC,MAAA,EAAAH,MAAA,MAAA/B,MAAA,CAAAkC,MAAA,GAAAH,MAAA;MAAA,OAAA3C,EAAA,CAAAa,WAAA,CAAA8B,MAAA;IAAA,EAAoB;IAAmD3C,EAAA,CAAAI,YAAA,EAAW;IAElGJ,EADF,CAAAC,cAAA,eAAoC,kBACuC;IAAnBD,EAAA,CAAAK,UAAA,mBAAA0C,uEAAA;MAAA/C,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAhC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASD,MAAA,CAAAoC,MAAA,EAAQ;IAAA,EAAC;IAAChD,EAAA,CAAAG,MAAA,oBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpFJ,EAAA,CAAAC,cAAA,kBACiE;IAA9BD,EAAA,CAAAK,UAAA,mBAAA4C,uEAAA;MAAAjD,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAhC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASD,MAAA,CAAAsC,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAAClD,EAAA,CAAAG,MAAA,oBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5EJ,EAAA,CAAAC,cAAA,kBACiE;IAA7BD,EAAA,CAAAK,UAAA,mBAAA8C,uEAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAAqC,GAAA;MAAA,MAAAhC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASD,MAAA,CAAAsC,YAAA,CAAa,IAAI,CAAC;IAAA,EAAC;IAAClD,EAAA,CAAAG,MAAA,oBAAE;IACrEH,EADqE,CAAAI,YAAA,EAAS,EACxE;IACNJ,EAAA,CAAAwB,UAAA,KAAA4B,oDAAA,mBAAsF;IA6C9FpD,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;IArJAJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAmB,eAAA,CAAAsB,KAAA,MACF;IASMrD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAmB,eAAA,CAAAuB,cAAA,MACF;IASEtD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAuD,WAAA,SAAA3C,MAAA,CAAAmB,eAAA,CAAAyB,KAAA,OACF;IASExD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAmB,eAAA,CAAAsB,KAAA,MACF;IAUKrD,EAAA,CAAAe,SAAA,GAAsG;IAAtGf,EAAA,CAAA0B,UAAA,UAAAd,MAAA,CAAAmB,eAAA,CAAA0B,aAAA,IAAA7C,MAAA,CAAAmB,eAAA,CAAA0B,aAAA,CAAAC,MAAA,WAAA9C,MAAA,CAAA+C,aAAA,GAAsG;IAKnF3D,EAAA,CAAAe,SAAA,EAAkC;IAAlCf,EAAA,CAAA0B,UAAA,YAAAd,MAAA,CAAAmB,eAAA,CAAA0B,aAAA,CAAkC;IAqCxDzD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAP,MAAA,CAAAmB,eAAA,CAAA6B,eAAA,MACF;IAYkB5D,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAA6D,gBAAA,YAAAjD,MAAA,CAAAkC,MAAA,CAAoB;IAAC9C,EAAA,CAAA0B,UAAA,WAAU;IAG9B1B,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAA0B,UAAA,aAAAd,MAAA,CAAAmB,eAAA,CAAA+B,UAAA,aAAAlD,MAAA,CAAAmB,eAAA,CAAA+B,UAAA,CAA8E;IAE9E9D,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAA0B,UAAA,aAAAd,MAAA,CAAAmB,eAAA,CAAA+B,UAAA,aAAAlD,MAAA,CAAAmB,eAAA,CAAA+B,UAAA,CAA8E;IAGpC9D,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAA0B,UAAA,WAAAd,MAAA,CAAAmB,eAAA,CAAuB;;;ADlFhG,OAAM,MAAOgC,8BAA8B;EASzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B,EAC3BC,WAAwB;IAPxB,KAAAP,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAhB,KAAK,GAAW,CAAC;IAEjB,KAAAV,MAAM,GAAW,EAAE;IAejB,IAAI,CAAC2B,SAAS,GAAG7E,gBAAgB,CAACC,mBAAmB,CAAC6E,eAAe,CAAC5E,WAAW,CAAC6E,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACX,eAAe,CAACY,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACX,eAAe,CAACY,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAACd,eAAe,CAACgB,WAAW,CAACC,IAAI,CACnCzF,GAAG,CAAC0F,CAAC,IAAG;MACN,IAAI,CAAC5B,KAAK,GAAG4B,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACAA,sBAAsBA,CAAA;IACpB,IAAI,CAACtB,qBAAqB,CAACuB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbpB,KAAK,EAAE,IAAI,CAACA,KAAK,CAACkC,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACLzF,GAAG,CAACiG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC7D,eAAe,GAAG4D,GAAG,CAACE,OAAQ;MACrC;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EAEAS,YAAYA,CAACnE,KAAU,EAAET,SAAc;IACrC;IACA;IACA;IACA;IACA;IACA6E,MAAM,CAACC,IAAI,CAACrE,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEAuB,YAAYA,CAAC+C,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAC5B,iBAAiB,CAAC6B,aAAa,CAACzC,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACW,OAAO,CAAC+B,aAAa,CAAC,IAAI,CAAC9B,iBAAiB,CAAC6B,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAAClC,qBAAqB,CAACoC,6CAA6C,CAAC;MACvEZ,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbpB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBM,UAAU,EAAEmC,SAAS;QACrB7D,OAAO,EAAE,IAAI,CAACU;;KAEjB,CAAC,CAACqC,IAAI,CACLzF,GAAG,CAACiG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvB,OAAO,CAACiC,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACf,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACxD,eAAe,CAACO,cAAc,EAAEoB,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAAC3B,eAAe,CAACO,cAAc,EAAEiE,IAAI,CAAC;YACxCpE,QAAQ,EAAE,IAAI,CAACsC,SAAS,CAAC+B,QAAQ;YACjC1E,WAAW,EAAE,IAAI2E,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCtE,OAAO,EAAE,IAAI,CAACU;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACE,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAACqC,SAAS,EAAE;EACf;EAEArC,MAAMA,CAAA;IACJ,IAAI,CAACuB,aAAa,CAACgC,IAAI,CAAC;MACtBI,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3B;KACf,CAAC;IACF,IAAI,CAACb,SAAS,CAACyC,IAAI,EAAE;EACvB;EAEAX,UAAUA,CAAA;IACR,IAAI,CAAC5B,iBAAiB,CAACwC,KAAK,EAAE;IAC9B,IAAI,CAACxC,iBAAiB,CAACyC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACjE,MAAM,CAAC;EACtD;EAEA;EACAa,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAC5B,eAAe,EAAE0B,aAAa,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAAC1B,eAAe,CAAC0B,aAAa,CAACuD,IAAI,CAACC,IAAI,IACjDA,IAAI,CAACtF,KAAK,KAAKsF,IAAI,CAAC/F,SAAS,IAAI+F,IAAI,CAACtF,KAAK,CAAC+B,MAAM,GAAG,CAAC,CAAC,CACxD;EACH;EACA;EACA5C,eAAeA,CAACmG,IAAS;IACvB,MAAMC,QAAQ,GAAGD,IAAI,CAAC/F,SAAS,IAAI+F,IAAI,CAACC,QAAQ,IAAI,EAAE;IACtD,MAAMC,WAAW,GAAGF,IAAI,CAAC/F,SAAS,IAAIgG,QAAQ;IAE9C;IACA,MAAME,aAAa,GAAG,IAAI,CAAC9F,WAAW,CAAC6F,WAAW,CAAC;IACnD,MAAME,WAAW,GAAG,IAAI,CAAC9F,WAAW,CAAC4F,WAAW,CAAC;IACjD,MAAMG,WAAW,GAAG,IAAI,CAACC,WAAW,CAACJ,WAAW,CAAC;IAEjD;IACA,MAAMK,YAAY,GAAGP,IAAI,CAACO,YAAY,IAAIP,IAAI,CAACtF,KAAK;IACpD,MAAM8F,cAAc,GAAGR,IAAI,CAACC,QAAQ,IAAID,IAAI,CAAC/F,SAAS;IAEtD,IAAIsG,YAAY,IAAIC,cAAc,EAAE;MAClC,IAAI,CAACC,iBAAiB,CAACF,YAAY,EAAEC,cAAc,EAAEN,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,CAAC;IAC5G,CAAC,MAAM;MACLK,OAAO,CAACC,KAAK,CAAC,aAAa,EAAEX,IAAI,CAAC;MAClC,IAAI,CAAC5C,OAAO,CAACwD,YAAY,CAAC,kBAAkB,CAAC;IAC/C;EACF;EAEA;EACQC,eAAeA,CAACb,IAAS,EAAEc,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACjF,MAAMC,OAAO,GAAGjB,IAAI,CAACtF,KAAK,IAAIsF,IAAI,CAACkB,IAAI;IACvC,MAAMjB,QAAQ,GAAGD,IAAI,CAAC/F,SAAS,IAAI+F,IAAI,CAACC,QAAQ,IAAI,EAAE;IAEtD,IAAIa,OAAO,EAAE;MACX,MAAMK,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACpB,IAAI,CAAC;MACvC,IAAI,CAACqB,gBAAgB,CAACF,QAAQ,EAAElB,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAIc,KAAK,EAAE;MAChB,IAAI,CAACO,gBAAgB,CAACL,OAAO,CAAC;IAChC,CAAC,MAAM;MACL,IAAI,CAACM,oBAAoB,CAACN,OAAO,EAAEhB,QAAQ,CAAC;IAC9C;EACF;EAEA;EACQQ,iBAAiBA,CAACF,YAAoB,EAAEN,QAAgB,EAAEC,WAAmB,EAAEY,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACrI,IAAI,CAACzD,WAAW,CAACiE,OAAO,CAACjB,YAAY,EAAEN,QAAQ,CAAC,CAAC7B,SAAS,CAAC;MACzDqD,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAErC,IAAIZ,OAAO,EAAE;UACX,IAAI,CAACO,gBAAgB,CAACM,GAAG,EAAEzB,WAAW,CAAC;QACzC,CAAC,MAAM,IAAIa,KAAK,EAAE;UAChB,IAAI,CAACe,kBAAkB,CAACH,GAAG,EAAEzB,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL,IAAI,CAAC6B,gBAAgB,CAACL,IAAI,EAAExB,WAAW,CAAC;QAC1C;QAEA;QACA8B,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACDhB,KAAK,EAAGA,KAAK,IAAI;QACfD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACA5G,WAAWA,CAACiG,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,2BAA2B;IAEjD,MAAMiC,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;IAE1D,QAAQH,SAAS;MACf,KAAK,KAAK;QACR,OAAO,8BAA8B;MACvC,KAAK,KAAK;MACV,KAAK,MAAM;MACX,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,iCAAiC;MAC1C,KAAK,KAAK;MACV,KAAK,KAAK;QACR,OAAO,wCAAwC;MACjD,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,gCAAgC;MACzC,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,kCAAkC;MAC3C;QACE,OAAO,2BAA2B;IACtC;EACF;EAEA;EACA/H,kBAAkBA,CAAC6F,IAAS;IAC1B,OAAOA,IAAI,CAAC/F,SAAS,IAAI+F,IAAI,CAACC,QAAQ,IAAI,MAAM;EAClD;EAEA;EACA7F,eAAeA,CAAC6F,QAAgB;IAC9B,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,MAAMiC,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;IAE1D,QAAQH,SAAS;MACf,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;MACV,KAAK,MAAM;QACT,OAAO,KAAK;MACd,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;QACR,OAAO,KAAK;MACd,KAAK,KAAK;QACR,OAAO,KAAK;MACd;QACE,OAAOA,SAAS,EAAEI,WAAW,EAAE,IAAI,EAAE;IACzC;EACF;EAEA;EACAjI,WAAWA,CAAC4F,QAAgB;IAC1B,MAAMsC,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAML,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;IAC1D,OAAOE,eAAe,CAACC,QAAQ,CAACN,SAAS,IAAI,EAAE,CAAC;EAClD;EAEA;EACA5H,WAAWA,CAACmI,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACJ,WAAW,EAAE,CAACK,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA;EACApC,WAAWA,CAACmC,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAACJ,WAAW,EAAE;MAClC,OAAOM,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EAEA;EACAtB,WAAWA,CAACpB,IAAS;IACnB,MAAM4C,QAAQ,GAAG5C,IAAI,CAACtF,KAAK,IAAIsF,IAAI,CAACkB,IAAI;IACxC,MAAMjB,QAAQ,GAAGD,IAAI,CAAC/F,SAAS,IAAI+F,IAAI,CAACC,QAAQ,IAAI,EAAE;IAEtD,IAAI,CAAC2C,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IAEA;IACA,IAAIA,QAAQ,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MAChC,OAAOD,QAAQ;IACjB;IAEA;IACA,IAAIA,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC/B,OAAOD,QAAQ;IACjB;IAEA;IACA,IAAIA,QAAQ,IAAI,CAACA,QAAQ,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;MAC7C,MAAMX,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE,IAAI,MAAM;MAEpE;MACA,IAAIS,QAAQ,GAAG,YAAY;MAC3B,QAAQZ,SAAS;QACf,KAAK,KAAK;UACRY,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF;UACEA,QAAQ,GAAG,YAAY;MAC3B;MAEA,OAAO,QAAQA,QAAQ,WAAWF,QAAQ,EAAE;IAC9C;IAEA,OAAOA,QAAQ;EACjB;EAEA;EACQd,kBAAkBA,CAACiB,OAAe,EAAE9C,QAAgB;IAC1D,IAAI;MACF,MAAM+C,SAAS,GAAGlE,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIiE,SAAS,EAAE;QACbA,SAAS,CAACC,QAAQ,CAACC,KAAK,CAAC;;;uBAGVjD,QAAQ;;;;;;;6BAOF8C,OAAO;;;SAG3B,CAAC;MACJ,CAAC,MAAM;QACL;QACAjE,MAAM,CAACC,IAAI,CAACgE,OAAO,EAAE,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA7B,MAAM,CAACC,IAAI,CAACgE,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQhB,gBAAgBA,CAACL,IAAU,EAAEzB,QAAgB;IACnD,MAAM0B,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMyB,IAAI,GAAGF,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACE,IAAI,GAAG1B,GAAG;IACfwB,IAAI,CAACG,QAAQ,GAAGrD,QAAQ;IACxBgD,QAAQ,CAACzE,IAAI,CAAC+E,WAAW,CAACJ,IAAI,CAAC;IAC/BA,IAAI,CAACK,KAAK,EAAE;IACZP,QAAQ,CAACzE,IAAI,CAACiF,WAAW,CAACN,IAAI,CAAC;IAE/B;IACAnB,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EAEA;EACAL,gBAAgBA,CAACL,OAAe;IAC9B,IAAI;MACF,IAAIA,OAAO,CAAC4B,UAAU,CAAC,sBAAsB,CAAC,EAAE;QAC9C;QACA,MAAMG,SAAS,GAAGlE,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;QAC3C,IAAIiE,SAAS,EAAE;UACbA,SAAS,CAACC,QAAQ,CAACC,KAAK,CAAC;;;;;;;+BAOJjC,OAAO;;;WAG3B,CAAC;QACJ;MACF,CAAC,MAAM;QACLnC,MAAM,CAACC,IAAI,CAACkC,OAAO,EAAE,QAAQ,CAAC;MAChC;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,aAAa,CAAC;IAC1C;EACF;EAEA;EACAW,oBAAoBA,CAACN,OAAe,EAAEhB,QAAgB;IACpD,IAAI;MACF,IAAIgB,OAAO,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC/B;QACA,IAAI,CAACa,kBAAkB,CAACzC,OAAO,EAAEhB,QAAQ,CAAC;MAC5C,CAAC,MAAM;QACL;QACA,MAAMkD,IAAI,GAAGF,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;QACxCD,IAAI,CAACE,IAAI,GAAGpC,OAAO;QACnBkC,IAAI,CAACG,QAAQ,GAAGrD,QAAQ;QACxBgD,QAAQ,CAACzE,IAAI,CAAC+E,WAAW,CAACJ,IAAI,CAAC;QAC/BA,IAAI,CAACK,KAAK,EAAE;QACZP,QAAQ,CAACzE,IAAI,CAACiF,WAAW,CAACN,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;EAEA;EACQ8C,kBAAkBA,CAACC,UAAkB,EAAE1D,QAAgB;IAC7D,IAAI;MACF;MACA,MAAM2D,aAAa,GAAGD,UAAU,CAACnB,QAAQ,CAAC,GAAG,CAAC,GAAGmB,UAAU,CAACxB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGwB,UAAU;MAEtF;MACA,IAAIb,QAAQ,GAAG,0BAA0B;MACzC,MAAMZ,SAAS,GAAGjC,QAAQ,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;MAE1D,QAAQH,SAAS;QACf,KAAK,KAAK;UACRY,QAAQ,GAAG,iBAAiB;UAC5B;QACF,KAAK,KAAK;QACV,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,kBAAkB;UAC7B;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,iBAAiB;UAC5B;MACJ;MAEA;MACA,MAAMe,cAAc,GAAGC,IAAI,CAACF,aAAa,CAAC;MAC1C,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACpH,MAAM,CAAC;MACpD,KAAK,IAAIwH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACpH,MAAM,EAAEwH,CAAC,EAAE,EAAE;QAC9CF,WAAW,CAACE,CAAC,CAAC,GAAGJ,cAAc,CAACK,UAAU,CAACD,CAAC,CAAC;MAC/C;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7C,MAAMrC,IAAI,GAAG,IAAI2C,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;QAAEG,IAAI,EAAExB;MAAQ,CAAE,CAAC;MAEtD;MACA,MAAMnB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMyB,IAAI,GAAGF,QAAQ,CAACG,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAG1B,GAAG;MACfwB,IAAI,CAACG,QAAQ,GAAGrD,QAAQ;MACxBgD,QAAQ,CAACzE,IAAI,CAAC+E,WAAW,CAACJ,IAAI,CAAC;MAC/BA,IAAI,CAACK,KAAK,EAAE;MACZP,QAAQ,CAACzE,IAAI,CAACiF,WAAW,CAACN,IAAI,CAAC;MAE/B;MACAnB,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACQS,gBAAgBA,CAACF,QAAgB,EAAElB,QAAgB;IACzD,IAAI;MACF,MAAM+C,SAAS,GAAGlE,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIiE,SAAS,EAAE;QACbA,SAAS,CAACC,QAAQ,CAACC,KAAK,CAAC;;;uBAGVjD,QAAQ;;;;;;;;;;;;;;;;;;;0BAmBLkB,QAAQ,UAAUlB,QAAQ;;;SAG3C,CAAC;MACJ,CAAC,MAAM;QACLnB,MAAM,CAACC,IAAI,CAACoC,QAAQ,EAAE,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAACvD,OAAO,CAACwD,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;;;uCAzeW9D,8BAA8B,EAAA/D,EAAA,CAAAwL,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA1L,EAAA,CAAAwL,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA5L,EAAA,CAAAwL,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAAwL,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAAhM,EAAA,CAAAwL,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAlM,EAAA,CAAAwL,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAApM,EAAA,CAAAwL,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAAtM,EAAA,CAAAwL,iBAAA,CAAAe,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BzI,8BAA8B;MAAA0I,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3M,EAAA,CAAA4M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BzClN,EAFF,CAAAC,cAAA,cAAS,mBAEO;UACZD,EAAA,CAAAwB,UAAA,IAAA4L,6CAAA,mBAAsG;UA0J1GpN,EADE,CAAAI,YAAA,EAAe,EACP;;;UA1JAJ,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAA0B,UAAA,WAAAyL,GAAA,CAAApL,eAAA,CAAuB;;;qBDmB7BtC,YAAY,EAAAsM,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,IAAA,EAAAvB,EAAA,CAAAwB,QAAA,EACZ5N,YAAY,EAAA6N,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,gBAAA,EAAAH,GAAA,CAAAI,iBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}