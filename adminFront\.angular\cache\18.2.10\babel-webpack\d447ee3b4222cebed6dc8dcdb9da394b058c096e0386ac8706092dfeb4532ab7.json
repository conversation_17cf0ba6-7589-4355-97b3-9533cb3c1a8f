{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nimport { enableProdMode } from '@angular/core';\nif (environment.IS_PRODUCTION) {\n  enableProdMode();\n  window.console.log = function () {};\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": {"version": 3, "names": ["AppModule", "environment", "enableProdMode", "IS_PRODUCTION", "window", "console", "log", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err", "error"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\main.ts"], "sourcesContent": ["import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\nimport { AppModule } from './app/app.module';\r\nimport { environment } from './environments/environment';\r\nimport { enableProdMode } from '@angular/core';\r\n\r\nif (environment.IS_PRODUCTION) {\r\n  enableProdMode();\r\n  window.console.log = function(){};\r\n}\r\n\r\nplatformBrowserDynamic().bootstrapModule(AppModule)\r\n  .catch(err => console.error(err));\r\n"], "mappings": ";AAEA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,cAAc,QAAQ,eAAe;AAE9C,IAAID,WAAW,CAACE,aAAa,EAAE;EAC7BD,cAAc,EAAE;EAChBE,MAAM,CAACC,OAAO,CAACC,GAAG,GAAG,aAAW,CAAC;AACnC;AAEAC,mBAAA,CAAAC,eAAA,EAAwB,CAACC,eAAe,CAACT,SAAS,CAAC,CAChDU,KAAK,CAACC,GAAG,IAAIN,OAAO,CAACO,KAAK,CAACD,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}