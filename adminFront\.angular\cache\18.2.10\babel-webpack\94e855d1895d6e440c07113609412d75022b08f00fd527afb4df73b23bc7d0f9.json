{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { StatsProgressBarData } from '../data/stats-progress-bar';\nimport * as i0 from \"@angular/core\";\nexport class StatsProgressBarService extends StatsProgressBarData {\n  constructor() {\n    super(...arguments);\n    this.progressInfoData = [{\n      title: 'Today’s Profit',\n      value: 572900,\n      activeProgress: 70,\n      description: 'Better than last week (70%)'\n    }, {\n      title: 'New Orders',\n      value: 6378,\n      activeProgress: 30,\n      description: 'Better than last week (30%)'\n    }, {\n      title: 'New Comments',\n      value: 200,\n      activeProgress: 55,\n      description: 'Better than last week (55%)'\n    }];\n  }\n  getProgressInfoData() {\n    return observableOf(this.progressInfoData);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵStatsProgressBarService_BaseFactory;\n      return function StatsProgressBarService_Factory(__ngFactoryType__) {\n        return (ɵStatsProgressBarService_BaseFactory || (ɵStatsProgressBarService_BaseFactory = i0.ɵɵgetInheritedFactory(StatsProgressBarService)))(__ngFactoryType__ || StatsProgressBarService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StatsProgressBarService,\n      factory: StatsProgressBarService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "StatsProgressBarData", "StatsProgressBarService", "constructor", "progressInfoData", "title", "value", "activeProgress", "description", "getProgressInfoData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\stats-progress-bar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf, Observable } from 'rxjs';\r\nimport { ProgressInfo, StatsProgressBarData } from '../data/stats-progress-bar';\r\n\r\n@Injectable()\r\nexport class StatsProgressBarService extends StatsProgressBarData {\r\n  private progressInfoData: ProgressInfo[] = [\r\n    {\r\n      title: 'Today’s Profit',\r\n      value: 572900,\r\n      activeProgress: 70,\r\n      description: 'Better than last week (70%)',\r\n    },\r\n    {\r\n      title: 'New Orders',\r\n      value: 6378,\r\n      activeProgress: 30,\r\n      description: 'Better than last week (30%)',\r\n    },\r\n    {\r\n      title: 'New Comments',\r\n      value: 200,\r\n      activeProgress: 55,\r\n      description: 'Better than last week (55%)',\r\n    },\r\n  ];\r\n\r\n  getProgressInfoData(): Observable<ProgressInfo[]> {\r\n    return observableOf(this.progressInfoData);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAoB,MAAM;AACrD,SAAuBC,oBAAoB,QAAQ,4BAA4B;;AAG/E,OAAM,MAAOC,uBAAwB,SAAQD,oBAAoB;EADjEE,YAAA;;IAEU,KAAAC,gBAAgB,GAAmB,CACzC;MACEC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAE,MAAM;MACbC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,GAAG;MACVC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;KACd,CACF;;EAEDC,mBAAmBA,CAAA;IACjB,OAAOT,YAAY,CAAC,IAAI,CAACI,gBAAgB,CAAC;EAC5C;;;;;yHAxBWF,uBAAuB,IAAAQ,iBAAA,IAAvBR,uBAAuB;MAAA;IAAA;EAAA;;;aAAvBA,uBAAuB;MAAAS,OAAA,EAAvBT,uBAAuB,CAAAU;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}