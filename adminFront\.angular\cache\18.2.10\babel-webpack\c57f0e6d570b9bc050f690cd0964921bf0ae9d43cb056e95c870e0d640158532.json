{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbAuthModule, NbDummyAuthStrategy } from '@nebular/auth';\nimport { NbSecurityModule, NbRoleProvider } from '@nebular/security';\nimport { of as observableOf } from 'rxjs';\nimport { throwIfAlreadyLoaded } from './module-import-guard';\nimport { AnalyticsService, LayoutService, PlayerService, SeoService, StateService } from './utils';\nimport { UserData } from './data/users';\nimport { ElectricityData } from './data/electricity';\nimport { SmartTableData } from './data/smart-table';\nimport { UserActivityData } from './data/user-activity';\nimport { OrdersChartData } from './data/orders-chart';\nimport { ProfitChartData } from './data/profit-chart';\nimport { TrafficListData } from './data/traffic-list';\nimport { OrdersProfitChartData } from './data/orders-profit-chart';\nimport { TrafficBarData } from './data/traffic-bar';\nimport { ProfitBarAnimationChartData } from './data/profit-bar-animation-chart';\nimport { TemperatureHumidityData } from './data/temperature-humidity';\nimport { SolarData } from './data/solar';\nimport { TrafficChartData } from './data/traffic-chart';\nimport { StatsBarData } from './data/stats-bar';\nimport { CountryOrderData } from './data/country-order';\nimport { StatsProgressBarData } from './data/stats-progress-bar';\nimport { VisitorsAnalyticsData } from './data/visitors-analytics';\nimport { SecurityCamerasData } from './data/security-cameras';\nimport { UserService } from './mock/users.service';\nimport { ElectricityService } from './mock/electricity.service';\nimport { SmartTableService } from './mock/smart-table.service';\nimport { UserActivityService } from './mock/user-activity.service';\nimport { OrdersChartService } from './mock/orders-chart.service';\nimport { ProfitChartService } from './mock/profit-chart.service';\nimport { TrafficListService } from './mock/traffic-list.service';\n// import { EarningService } from './mock/earning.service';\nimport { OrdersProfitChartService } from './mock/orders-profit-chart.service';\nimport { TrafficBarService } from './mock/traffic-bar.service';\nimport { ProfitBarAnimationChartService } from './mock/profit-bar-animation-chart.service';\nimport { TemperatureHumidityService } from './mock/temperature-humidity.service';\nimport { SolarService } from './mock/solar.service';\nimport { TrafficChartService } from './mock/traffic-chart.service';\nimport { StatsBarService } from './mock/stats-bar.service';\nimport { CountryOrderService } from './mock/country-order.service';\nimport { StatsProgressBarService } from './mock/stats-progress-bar.service';\nimport { VisitorsAnalyticsService } from './mock/visitors-analytics.service';\nimport { SecurityCamerasService } from './mock/security-cameras.service';\nimport { MockDataModule } from './mock/mock-data.module';\nimport * as i0 from \"@angular/core\";\nconst socialLinks = [{\n  url: 'https://github.com/akveo/nebular',\n  target: '_blank',\n  icon: 'github'\n}, {\n  url: 'https://www.facebook.com/akveo/',\n  target: '_blank',\n  icon: 'facebook'\n}, {\n  url: 'https://twitter.com/akveo_inc',\n  target: '_blank',\n  icon: 'twitter'\n}];\nconst DATA_SERVICES = [{\n  provide: UserData,\n  useClass: UserService\n}, {\n  provide: ElectricityData,\n  useClass: ElectricityService\n}, {\n  provide: SmartTableData,\n  useClass: SmartTableService\n}, {\n  provide: UserActivityData,\n  useClass: UserActivityService\n}, {\n  provide: OrdersChartData,\n  useClass: OrdersChartService\n}, {\n  provide: ProfitChartData,\n  useClass: ProfitChartService\n}, {\n  provide: TrafficListData,\n  useClass: TrafficListService\n},\n// { provide: EarningData, useClass: EarningService },\n{\n  provide: OrdersProfitChartData,\n  useClass: OrdersProfitChartService\n}, {\n  provide: TrafficBarData,\n  useClass: TrafficBarService\n}, {\n  provide: ProfitBarAnimationChartData,\n  useClass: ProfitBarAnimationChartService\n}, {\n  provide: TemperatureHumidityData,\n  useClass: TemperatureHumidityService\n}, {\n  provide: SolarData,\n  useClass: SolarService\n}, {\n  provide: TrafficChartData,\n  useClass: TrafficChartService\n}, {\n  provide: StatsBarData,\n  useClass: StatsBarService\n}, {\n  provide: CountryOrderData,\n  useClass: CountryOrderService\n}, {\n  provide: StatsProgressBarData,\n  useClass: StatsProgressBarService\n}, {\n  provide: VisitorsAnalyticsData,\n  useClass: VisitorsAnalyticsService\n}, {\n  provide: SecurityCamerasData,\n  useClass: SecurityCamerasService\n}];\nexport class NbSimpleRoleProvider extends NbRoleProvider {\n  getRole() {\n    // here you could provide any role based on any auth flow\n    return observableOf('guest');\n  }\n}\nexport const NB_CORE_PROVIDERS = [...MockDataModule.forRoot().providers, ...DATA_SERVICES, ...NbAuthModule.forRoot({\n  strategies: [NbDummyAuthStrategy.setup({\n    name: 'email',\n    delay: 3000\n  })],\n  forms: {\n    login: {\n      socialLinks: socialLinks\n    },\n    register: {\n      socialLinks: socialLinks\n    }\n  }\n}).providers, NbSecurityModule.forRoot({\n  accessControl: {\n    guest: {\n      view: '*'\n    },\n    user: {\n      parent: 'guest',\n      create: '*',\n      edit: '*',\n      remove: '*'\n    }\n  }\n}).providers, {\n  provide: NbRoleProvider,\n  useClass: NbSimpleRoleProvider\n}, AnalyticsService, LayoutService, PlayerService, SeoService, StateService];\nexport class CoreModule {\n  constructor(parentModule) {\n    throwIfAlreadyLoaded(parentModule, 'CoreModule');\n  }\n  static forRoot() {\n    return {\n      ngModule: CoreModule,\n      providers: [...NB_CORE_PROVIDERS]\n    };\n  }\n  static {\n    this.ɵfac = function CoreModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CoreModule)(i0.ɵɵinject(CoreModule, 12));\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CoreModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, NbAuthModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreModule, {\n    imports: [CommonModule],\n    exports: [NbAuthModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "NbAuthModule", "NbDummyAuthStrategy", "NbSecurityModule", "NbRoleProvider", "of", "observableOf", "throwIfAlreadyLoaded", "AnalyticsService", "LayoutService", "PlayerService", "SeoService", "StateService", "UserData", "ElectricityData", "SmartTableData", "UserActivityData", "OrdersChartData", "ProfitChartData", "TrafficListData", "OrdersProfitChartData", "TrafficBarData", "ProfitBarAnimationChartData", "TemperatureHumidityData", "SolarData", "TrafficChartData", "StatsBarData", "CountryOrderData", "StatsProgressBarData", "VisitorsAnalyticsData", "SecurityCamerasData", "UserService", "ElectricityService", "SmartTableService", "UserActivityService", "OrdersChartService", "ProfitChartService", "TrafficListService", "OrdersProfitChartService", "TrafficBarService", "ProfitBarAnimationChartService", "TemperatureHumidityService", "SolarService", "TrafficChartService", "StatsBarService", "CountryOrderService", "StatsProgressBarService", "VisitorsAnalyticsService", "SecurityCamerasService", "MockDataModule", "socialLinks", "url", "target", "icon", "DATA_SERVICES", "provide", "useClass", "NbSimpleRoleProvider", "getRole", "NB_CORE_PROVIDERS", "forRoot", "providers", "strategies", "setup", "name", "delay", "forms", "login", "register", "accessControl", "guest", "view", "user", "parent", "create", "edit", "remove", "CoreModule", "constructor", "parentModule", "ngModule", "i0", "ɵɵinject", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\core.module.ts"], "sourcesContent": ["import { ModuleWithProviders, NgModule, Optional, SkipSelf } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbAuthModule, NbDummyAuthStrategy } from '@nebular/auth';\r\nimport { NbSecurityModule, NbRoleProvider } from '@nebular/security';\r\nimport { of as observableOf } from 'rxjs';\r\n\r\nimport { throwIfAlreadyLoaded } from './module-import-guard';\r\nimport {\r\n  AnalyticsService,\r\n  LayoutService,\r\n  PlayerService,\r\n  SeoService,\r\n  StateService,\r\n} from './utils';\r\nimport { UserData } from './data/users';\r\nimport { ElectricityData } from './data/electricity';\r\nimport { SmartTableData } from './data/smart-table';\r\nimport { UserActivityData } from './data/user-activity';\r\nimport { OrdersChartData } from './data/orders-chart';\r\nimport { ProfitChartData } from './data/profit-chart';\r\nimport { TrafficListData } from './data/traffic-list';\r\nimport { EarningData } from './data/earning';\r\nimport { OrdersProfitChartData } from './data/orders-profit-chart';\r\nimport { TrafficBarData } from './data/traffic-bar';\r\nimport { ProfitBarAnimationChartData } from './data/profit-bar-animation-chart';\r\nimport { TemperatureHumidityData } from './data/temperature-humidity';\r\nimport { SolarData } from './data/solar';\r\nimport { TrafficChartData } from './data/traffic-chart';\r\nimport { StatsBarData } from './data/stats-bar';\r\nimport { CountryOrderData } from './data/country-order';\r\nimport { StatsProgressBarData } from './data/stats-progress-bar';\r\nimport { VisitorsAnalyticsData } from './data/visitors-analytics';\r\nimport { SecurityCamerasData } from './data/security-cameras';\r\n\r\nimport { UserService } from './mock/users.service';\r\nimport { ElectricityService } from './mock/electricity.service';\r\nimport { SmartTableService } from './mock/smart-table.service';\r\nimport { UserActivityService } from './mock/user-activity.service';\r\nimport { OrdersChartService } from './mock/orders-chart.service';\r\nimport { ProfitChartService } from './mock/profit-chart.service';\r\nimport { TrafficListService } from './mock/traffic-list.service';\r\n// import { EarningService } from './mock/earning.service';\r\nimport { OrdersProfitChartService } from './mock/orders-profit-chart.service';\r\nimport { TrafficBarService } from './mock/traffic-bar.service';\r\nimport { ProfitBarAnimationChartService } from './mock/profit-bar-animation-chart.service';\r\nimport { TemperatureHumidityService } from './mock/temperature-humidity.service';\r\nimport { SolarService } from './mock/solar.service';\r\nimport { TrafficChartService } from './mock/traffic-chart.service';\r\nimport { StatsBarService } from './mock/stats-bar.service';\r\nimport { CountryOrderService } from './mock/country-order.service';\r\nimport { StatsProgressBarService } from './mock/stats-progress-bar.service';\r\nimport { VisitorsAnalyticsService } from './mock/visitors-analytics.service';\r\nimport { SecurityCamerasService } from './mock/security-cameras.service';\r\nimport { MockDataModule } from './mock/mock-data.module';\r\n\r\nconst socialLinks = [\r\n  {\r\n    url: 'https://github.com/akveo/nebular',\r\n    target: '_blank',\r\n    icon: 'github',\r\n  },\r\n  {\r\n    url: 'https://www.facebook.com/akveo/',\r\n    target: '_blank',\r\n    icon: 'facebook',\r\n  },\r\n  {\r\n    url: 'https://twitter.com/akveo_inc',\r\n    target: '_blank',\r\n    icon: 'twitter',\r\n  },\r\n];\r\n\r\nconst DATA_SERVICES = [\r\n  { provide: UserData, useClass: UserService },\r\n  { provide: ElectricityData, useClass: ElectricityService },\r\n  { provide: SmartTableData, useClass: SmartTableService },\r\n  { provide: UserActivityData, useClass: UserActivityService },\r\n  { provide: OrdersChartData, useClass: OrdersChartService },\r\n  { provide: ProfitChartData, useClass: ProfitChartService },\r\n  { provide: TrafficListData, useClass: TrafficListService },\r\n  // { provide: EarningData, useClass: EarningService },\r\n  { provide: OrdersProfitChartData, useClass: OrdersProfitChartService },\r\n  { provide: TrafficBarData, useClass: TrafficBarService },\r\n  { provide: ProfitBarAnimationChartData, useClass: ProfitBarAnimationChartService },\r\n  { provide: TemperatureHumidityData, useClass: TemperatureHumidityService },\r\n  { provide: SolarData, useClass: SolarService },\r\n  { provide: TrafficChartData, useClass: TrafficChartService },\r\n  { provide: StatsBarData, useClass: StatsBarService },\r\n  { provide: CountryOrderData, useClass: CountryOrderService },\r\n  { provide: StatsProgressBarData, useClass: StatsProgressBarService },\r\n  { provide: VisitorsAnalyticsData, useClass: VisitorsAnalyticsService },\r\n  { provide: SecurityCamerasData, useClass: SecurityCamerasService },\r\n];\r\n\r\nexport class NbSimpleRoleProvider extends NbRoleProvider {\r\n  getRole() {\r\n    // here you could provide any role based on any auth flow\r\n    return observableOf('guest');\r\n  }\r\n}\r\n\r\nexport const NB_CORE_PROVIDERS = [\r\n  ...MockDataModule.forRoot().providers!,\r\n  ...DATA_SERVICES,\r\n  ...NbAuthModule.forRoot({\r\n\r\n    strategies: [\r\n      NbDummyAuthStrategy.setup({\r\n        name: 'email',\r\n        delay: 3000,\r\n      }),\r\n    ],\r\n    forms: {\r\n      login: {\r\n        socialLinks: socialLinks,\r\n      },\r\n      register: {\r\n        socialLinks: socialLinks,\r\n      },\r\n    },\r\n  }).providers!,\r\n\r\n  NbSecurityModule.forRoot({\r\n    accessControl: {\r\n      guest: {\r\n        view: '*',\r\n      },\r\n      user: {\r\n        parent: 'guest',\r\n        create: '*',\r\n        edit: '*',\r\n        remove: '*',\r\n      },\r\n    },\r\n  }).providers,\r\n\r\n  {\r\n    provide: NbRoleProvider, useClass: NbSimpleRoleProvider,\r\n  },\r\n  AnalyticsService,\r\n  LayoutService,\r\n  PlayerService,\r\n  SeoService,\r\n  StateService,\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n  ],\r\n  exports: [\r\n    NbAuthModule,\r\n  ],\r\n  declarations: [],\r\n})\r\nexport class CoreModule {\r\n  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {\r\n    throwIfAlreadyLoaded(parentModule, 'CoreModule');\r\n  }\r\n\r\n  static forRoot(): ModuleWithProviders<CoreModule> {\r\n    return {\r\n      ngModule: CoreModule,\r\n      providers: [\r\n        ...NB_CORE_PROVIDERS as any,\r\n      ],\r\n    };\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,eAAe;AACjE,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,mBAAmB;AACpE,SAASC,EAAE,IAAIC,YAAY,QAAQ,MAAM;AAEzC,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SACEC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,UAAU,EACVC,YAAY,QACP,SAAS;AAChB,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,eAAe,QAAQ,qBAAqB;AAErD,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,2BAA2B,QAAQ,mCAAmC;AAC/E,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7D,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE;AACA,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,8BAA8B,QAAQ,2CAA2C;AAC1F,SAASC,0BAA0B,QAAQ,qCAAqC;AAChF,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,wBAAwB,QAAQ,mCAAmC;AAC5E,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,cAAc,QAAQ,yBAAyB;;AAExD,MAAMC,WAAW,GAAG,CAClB;EACEC,GAAG,EAAE,kCAAkC;EACvCC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;CACP,EACD;EACEF,GAAG,EAAE,iCAAiC;EACtCC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;CACP,EACD;EACEF,GAAG,EAAE,+BAA+B;EACpCC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE;CACP,CACF;AAED,MAAMC,aAAa,GAAG,CACpB;EAAEC,OAAO,EAAE1C,QAAQ;EAAE2C,QAAQ,EAAEzB;AAAW,CAAE,EAC5C;EAAEwB,OAAO,EAAEzC,eAAe;EAAE0C,QAAQ,EAAExB;AAAkB,CAAE,EAC1D;EAAEuB,OAAO,EAAExC,cAAc;EAAEyC,QAAQ,EAAEvB;AAAiB,CAAE,EACxD;EAAEsB,OAAO,EAAEvC,gBAAgB;EAAEwC,QAAQ,EAAEtB;AAAmB,CAAE,EAC5D;EAAEqB,OAAO,EAAEtC,eAAe;EAAEuC,QAAQ,EAAErB;AAAkB,CAAE,EAC1D;EAAEoB,OAAO,EAAErC,eAAe;EAAEsC,QAAQ,EAAEpB;AAAkB,CAAE,EAC1D;EAAEmB,OAAO,EAAEpC,eAAe;EAAEqC,QAAQ,EAAEnB;AAAkB,CAAE;AAC1D;AACA;EAAEkB,OAAO,EAAEnC,qBAAqB;EAAEoC,QAAQ,EAAElB;AAAwB,CAAE,EACtE;EAAEiB,OAAO,EAAElC,cAAc;EAAEmC,QAAQ,EAAEjB;AAAiB,CAAE,EACxD;EAAEgB,OAAO,EAAEjC,2BAA2B;EAAEkC,QAAQ,EAAEhB;AAA8B,CAAE,EAClF;EAAEe,OAAO,EAAEhC,uBAAuB;EAAEiC,QAAQ,EAAEf;AAA0B,CAAE,EAC1E;EAAEc,OAAO,EAAE/B,SAAS;EAAEgC,QAAQ,EAAEd;AAAY,CAAE,EAC9C;EAAEa,OAAO,EAAE9B,gBAAgB;EAAE+B,QAAQ,EAAEb;AAAmB,CAAE,EAC5D;EAAEY,OAAO,EAAE7B,YAAY;EAAE8B,QAAQ,EAAEZ;AAAe,CAAE,EACpD;EAAEW,OAAO,EAAE5B,gBAAgB;EAAE6B,QAAQ,EAAEX;AAAmB,CAAE,EAC5D;EAAEU,OAAO,EAAE3B,oBAAoB;EAAE4B,QAAQ,EAAEV;AAAuB,CAAE,EACpE;EAAES,OAAO,EAAE1B,qBAAqB;EAAE2B,QAAQ,EAAET;AAAwB,CAAE,EACtE;EAAEQ,OAAO,EAAEzB,mBAAmB;EAAE0B,QAAQ,EAAER;AAAsB,CAAE,CACnE;AAED,OAAM,MAAOS,oBAAqB,SAAQrD,cAAc;EACtDsD,OAAOA,CAAA;IACL;IACA,OAAOpD,YAAY,CAAC,OAAO,CAAC;EAC9B;;AAGF,OAAO,MAAMqD,iBAAiB,GAAG,CAC/B,GAAGV,cAAc,CAACW,OAAO,EAAE,CAACC,SAAU,EACtC,GAAGP,aAAa,EAChB,GAAGrD,YAAY,CAAC2D,OAAO,CAAC;EAEtBE,UAAU,EAAE,CACV5D,mBAAmB,CAAC6D,KAAK,CAAC;IACxBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE;GACR,CAAC,CACH;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLjB,WAAW,EAAEA;KACd;IACDkB,QAAQ,EAAE;MACRlB,WAAW,EAAEA;;;CAGlB,CAAC,CAACW,SAAU,EAEb1D,gBAAgB,CAACyD,OAAO,CAAC;EACvBS,aAAa,EAAE;IACbC,KAAK,EAAE;MACLC,IAAI,EAAE;KACP;IACDC,IAAI,EAAE;MACJC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,GAAG;MACXC,IAAI,EAAE,GAAG;MACTC,MAAM,EAAE;;;CAGb,CAAC,CAACf,SAAS,EAEZ;EACEN,OAAO,EAAEnD,cAAc;EAAEoD,QAAQ,EAAEC;CACpC,EACDjD,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,UAAU,EACVC,YAAY,CACb;AAWD,OAAM,MAAOiE,UAAU;EACrBC,YAAoCC,YAAwB;IAC1DxE,oBAAoB,CAACwE,YAAY,EAAE,YAAY,CAAC;EAClD;EAEA,OAAOnB,OAAOA,CAAA;IACZ,OAAO;MACLoB,QAAQ,EAAEH,UAAU;MACpBhB,SAAS,EAAE,CACT,GAAGF,iBAAwB;KAE9B;EACH;;;uCAZWkB,UAAU,EAAAI,EAAA,CAAAC,QAAA,CAAAL,UAAA;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAPnB7E,YAAY,EAGZC,YAAY;IAAA;EAAA;;;2EAIH4E,UAAU;IAAAM,OAAA,GAPnBnF,YAAY;IAAAoF,OAAA,GAGZnF,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}