{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport { DateFormatHourPipe } from 'src/app/@theme/pipes';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/@core/service/review.service\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/directives/label.directive\";\nimport * as i13 from \"../../../../@theme/pipes/date-format.pipe\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = a0 => ({\n  \"!text-red\": a0\n});\nfunction ReviewDocumentManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSearch());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9, item_r11));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 36);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 37);\n    i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_tr_60_button_18_Template, 2, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 7, item_r11.CReviewType, ctx_r6.reviewTypeOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CReviewName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CHouse, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(10, 10, item_r11.CStatus, ctx_r6.statusOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 13, item_r11.CExamineStatus, ctx_r6.examineStatusOptionsQuery), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CActionDate ? i0.ɵɵpipeBind1(16, 16, item_r11.CActionDate) : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isUpdate);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r13.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_div_26_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.fileName);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 69)(2, \"a\", 70);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_div_27_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.openPdfInNewTab(ctx_r6.selectedReview.CFileUrl));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.imageUrl ? \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedReview.CFileUrl, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r16.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_42_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"nb-checkbox\", 74);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_42_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const idx_r18 = i0.ɵɵrestoreView(_r17).index;\n      const ctx_r6 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r6.enableAllAtIndex($event, idx_r18));\n    });\n    i0.ɵɵelementStart(2, \"span\", 75);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r19 = ctx.$implicit;\n    const idx_r18 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllColumnChecked(idx_r18))(\"disabled\", ctx_r6.latestAction === 1 || ctx_r6.checkAllHouseIsValid(house_r19.CHouseHold, null));\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_42_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, ReviewDocumentManagementComponent_ng_template_63_div_42_tr_3_th_2_Template, 4, 2, \"th\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D[0]);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"nb-checkbox\", 74);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const house_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r23.CIsSelect, $event) || (house_r23.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"span\", 76);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r23 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", house_r23.CIsSelect);\n    i0.ɵɵproperty(\"disabled\", !house_r23.CHouseHold || !house_r23.CIsEnable || ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, house_r23.CID));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", house_r23.CHouseHold || \"null\", \" - \", house_r23.CFloor, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 74);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const row_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.enableAllRow($event, row_r21));\n    });\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_td_5_Template, 5, 7, \"td\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r21 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllRowChecked(row_r21))(\"disabled\", ctx_r6.latestAction === 1 || ctx_r6.checkAllHouseIsValid(null, row_r21[0].CFloor));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r21);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"table\", 72)(2, \"thead\");\n    i0.ɵɵtemplate(3, ReviewDocumentManagementComponent_ng_template_63_div_42_tr_3_Template, 3, 1, \"tr\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_42_tr_5_Template, 6, 3, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.houseList2D.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_48_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormatHour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r25 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r25.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r25.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.getActionName(row_r25.CAction));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r25.CExamineNote);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 44)(2, \"label\", 78);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 72)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_div_48_tr_16_Template, 10, 6, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.selectedReview.tblExamineLogs);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 40)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 41)(4, \"div\", 5)(5, \"label\", 42);\n    i0.ɵɵtext(6, \"\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-select\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.selectedReviewType, $event) || (ctx_r6.selectedReview.selectedReviewType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(8, ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"div\", 5)(11, \"label\", 45);\n    i0.ɵɵtext(12, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CReviewName, $event) || (ctx_r6.selectedReview.CReviewName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 44)(15, \"div\", 5)(16, \"div\", 47)(17, \"label\", 48);\n    i0.ɵɵtext(18, \" \\u4E0A\\u50B3\\u6A94\\u6848 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"h3\", 49);\n    i0.ɵɵtext(20, \"*\\u8ACB\\u4E0A\\u50B3PDF\\u683C\\u5F0F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 50)(22, \"input\", 51);\n    i0.ɵɵlistener(\"change\", function ReviewDocumentManagementComponent_ng_template_63_Template_input_change_22_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"label\", 52);\n    i0.ɵɵelement(24, \"i\", 53);\n    i0.ɵɵtext(25, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ReviewDocumentManagementComponent_ng_template_63_div_26_Template, 5, 1, \"div\", 54)(27, ReviewDocumentManagementComponent_ng_template_63_div_27_Template, 4, 2, \"div\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 44)(29, \"div\", 5)(30, \"label\", 55);\n    i0.ɵɵtext(31, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"nb-select\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_32_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.seletedStatus, $event) || (ctx_r6.selectedReview.seletedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(33, ReviewDocumentManagementComponent_ng_template_63_nb_option_33_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 44)(35, \"div\", 5)(36, \"label\", 57);\n    i0.ɵɵtext(37, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"textarea\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CExamineNote, $event) || (ctx_r6.selectedReview.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 59)(40, \"label\", 60);\n    i0.ɵɵtext(41, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(42, ReviewDocumentManagementComponent_ng_template_63_div_42_Template, 6, 2, \"div\", 61);\n    i0.ɵɵelementStart(43, \"div\", 29)(44, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_44_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r24));\n    });\n    i0.ɵɵtext(45, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_46_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSaveReview(ref_r24));\n    });\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(48, ReviewDocumentManagementComponent_ng_template_63_div_48_Template, 17, 1, \"div\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u5BE9\\u95B1\\u6587\\u4EF6\" : \"\\u7DE8\\u8F2F\\u5BE9\\u95B1\\u6587\\u4EF6\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.selectedReviewType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.reviewTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CReviewName);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"opacity-50\", ctx_r6.latestAction === 1)(\"cursor-pointer\", ctx_r6.latestAction !== 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedReview.CFileUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.seletedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4)(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isHouseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u4E26\\u9001\\u51FA\\u5BE9\\u6838\" : \"\\u9001\\u51FA\\u5BE9\\u6838\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isNew);\n  }\n}\nexport let ReviewDocumentManagementComponent = /*#__PURE__*/(() => {\n  class ReviewDocumentManagementComponent extends BaseComponent {\n    constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._buildCaseService = _buildCaseService;\n      this._reviewService = _reviewService;\n      this.reviewService = reviewService;\n      this.utilityService = utilityService;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.reviewTypeOptions = [{\n        value: 1,\n        label: '標準圖' //standard drawing\n      }, {\n        value: 2,\n        label: '設備圖' //equipment drawing\n      }];\n      this.reviewTypeOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 1,\n        label: '標準圖' //standard drawing\n      }, {\n        value: 2,\n        label: '設備圖' //equipment drawing\n      }];\n      this.examineStatusOptions = [{\n        value: -1,\n        label: '待審核' //Pending review\n      }, {\n        value: 1,\n        label: '已通過' //passed\n      }, {\n        value: 2,\n        label: '已駁回' //rejected\n      }];\n      this.examineStatusOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 0,\n        label: '待審核' //Pending review\n      }, {\n        value: 1,\n        label: '已通過' //passed\n      }, {\n        value: 2,\n        label: '已駁回' //rejected\n      }];\n      this.statusOptions = [{\n        value: 1,\n        //0停用 1啟用 9刪除\n        label: '啟用' //enable\n      }, {\n        value: 2,\n        label: '停用' //Disable\n      }];\n      this.statusOptionsQuery = [{\n        value: -1,\n        label: '全部'\n      }, {\n        value: 1,\n        //0停用 1啟用 9刪除\n        label: '啟用' //enable\n      }, {\n        value: 2,\n        label: '停用' //Disable\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.fileName = null;\n      this.imageUrl = undefined;\n      this.isHouseList = false;\n      this.latestAction = 0;\n      this.isNew = true;\n    }\n    ngOnInit() {\n      if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n        let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n        this.searchQuery = {\n          selectedBuildCase: null,\n          selectedReviewType: this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) ? this.reviewTypeOptionsQuery.find(x => x.value == previous_search.CReviewType.value) : this.reviewTypeOptionsQuery[0],\n          selectedExamineStatus: this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) ? this.examineStatusOptionsQuery.find(x => x.value == previous_search.CExamineStatus.value) : this.examineStatusOptionsQuery[0],\n          seletedStatus: this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) ? this.statusOptionsQuery.find(x => x.value == previous_search.CSeletedStatus.value) : this.statusOptionsQuery[0],\n          CReviewName: previous_search.CReviewName\n        };\n      } else {\n        this.searchQuery = {\n          selectedBuildCase: null,\n          selectedReviewType: this.reviewTypeOptionsQuery[0],\n          selectedExamineStatus: this.examineStatusOptionsQuery[0],\n          seletedStatus: this.statusOptionsQuery[0],\n          CReviewName: ''\n        };\n      }\n      this.getUserBuildCase();\n    }\n    clearImage() {\n      if (this.imageUrl) {\n        this.imageUrl = null;\n        this.fileName = null;\n        if (this.fileInput) {\n          this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n        }\n      }\n    }\n    onFileSelected(event) {\n      const file = event.target.files[0];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg/i;\n      if (!fileRegex.test(file.name)) {\n        this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\n        return;\n      }\n      if (file) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = {\n            CName: file.name,\n            CFile: e.target?.result?.toString().split(',')[1],\n            Cimg: file.name.includes('pdf') ? file : file,\n            CFileUpload: file,\n            CFileType: EnumFileType.PDF\n          };\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n    isCheckAllRowChecked(row) {\n      return row.every(item => item.CIsSelect);\n    }\n    isCheckAllColumnChecked(index) {\n      if (this.isHouseList) {\n        if (index < 0 || index >= this.houseList2D[0].length) {\n          throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n        }\n        for (const floorData of this.houseList2D) {\n          if (index >= floorData.length || !floorData[index].CIsSelect) {\n            return false; // Found a customer with CIsEnable not true (or missing)\n          }\n        }\n        return true; // All customers at the given index have CIsEnable as true\n      }\n      return false;\n    }\n    enableAllAtIndex(checked, index) {\n      if (index < 0) {\n        throw new Error(\"Invalid index. Index must be a non-negative number.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index < floorData.length) {\n          // Check if index is valid for this floor\n          floorData[index].CIsSelect = checked;\n        }\n      }\n    }\n    enableAllRow(checked, row) {\n      for (const item of row) {\n        item.CIsSelect = checked;\n      }\n    }\n    getItemByValue(value, options) {\n      for (const item of options) {\n        if (item.value === value) {\n          return item;\n        }\n      }\n      return null;\n    }\n    getReviewById(item, ref) {\n      this._reviewService.apiReviewGetReviewByIdPost$Json({\n        body: item.CReviewId\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          const data = res.Entries;\n          this.selectedReview = {\n            CBuildCaseId: data.tblReview?.CBuildCaseId,\n            CReviewId: data.tblReview?.CReviewId,\n            CReviewType: data.tblReview?.CReviewType,\n            CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n            CSort: data.tblReview?.CSort,\n            CStatus: data.tblReview?.CStatus,\n            CFileUrl: data.tblReview?.CFileUrl,\n            CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n            seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n            selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n            tblExamineLogs: data.tblExamineLogs,\n            reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n            tblReview: data.tblReview\n          };\n          if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n            if (data?.tblExamineLogs.length === 0) return undefined;\n            this.latestAction = data?.tblExamineLogs[0].CAction;\n            let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n            for (let i = 1; i < data.tblExamineLogs.length; i++) {\n              if (data.tblExamineLogs[i].CCreateDt) {\n                const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n                if (currentDate > latestDate) {\n                  latestDate = currentDate;\n                  this.latestAction = data?.tblExamineLogs[i].CAction;\n                }\n              }\n            }\n          }\n          this.getHouseList();\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    flattenAndFilter(data) {\n      const flattened = [];\n      for (const floorData of data) {\n        for (const house of floorData) {\n          if (house.CIsSelect && house.CIsEnable) {\n            flattened.push({\n              CHouseID: house.CHouseID,\n              CIsSelect: house.CIsSelect,\n              CFloor: house.CFloor,\n              CHouseHold: house.CHouseHold\n            });\n          }\n        }\n      }\n      return flattened;\n    }\n    onSaveReview(ref) {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.saveReviewPostRes = {\n        CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n        CReviewId: this.selectedReview.CReviewId,\n        CReviewType: this.selectedReview.selectedReviewType.value,\n        CReviewName: this.selectedReview.CReviewName,\n        CSort: this.selectedReview?.CSort,\n        CStatus: this.selectedReview.seletedStatus.value,\n        CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n        CExamineNote: this.selectedReview.CExamineNote,\n        HouseReviews: this.houseList2D != null && this.houseList2D != undefined && this.houseList2D.length > 0 ? this.flattenAndFilter(this.houseList2D) : []\n      };\n      this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n        if (res && res.body && res.body.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          this.clearImage();\n          this.getReviewList();\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res && res.body && res.body.Message);\n        }\n      });\n    }\n    onSearch() {\n      let previous_search = {\n        CReviewName: this.searchQuery.CReviewName,\n        CSelectedBuildCase: this.searchQuery.selectedBuildCase,\n        CSeletedStatus: this.searchQuery.seletedStatus,\n        CReviewType: this.searchQuery.selectedReviewType,\n        CExamineStatus: this.searchQuery.selectedExamineStatus\n      };\n      LocalStorageService.AddSessionStorage(STORAGE_KEY.REVIEW_SEARCH, JSON.stringify(previous_search));\n      this.getReviewList();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getReviewList();\n    }\n    groupByFloor(customerData, isDefaut) {\n      const groupedData = [];\n      const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n      for (const floor of uniqueFloors) {\n        groupedData.push([]);\n      }\n      for (const customer of customerData) {\n        const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n        if (floorIndex !== -1) {\n          groupedData[floorIndex].push({\n            CIsSelect: customer?.CIsSelect || false,\n            CHouseID: customer.CID,\n            CHouseType: customer.CHouseType,\n            CFloor: customer.CFloor,\n            CHouseHold: customer.CHouseHold,\n            CIsEnable: customer.CIsEnable\n          });\n        }\n      }\n      return groupedData;\n    }\n    addCIsSelectToA(A, B) {\n      const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n      return A.map(item => {\n        const key = `${item.CHouseHold}-${item.CFloor}`;\n        return {\n          ...item,\n          CIsSelect: mapB.has(key) ? mapB.get(key) : false\n        };\n      });\n    }\n    sortByFloorDescending(arr) {\n      return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n    }\n    getHouseList() {\n      return this._houseService.apiHouseGetHouseListPost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n          CIsPagi: false\n        }\n      }).pipe(tap(res => {\n        if (res && res.StatusCode === 0 && res.Entries) {\n          const rest = this.sortByFloorDescending(res.Entries);\n          this.houseListEnable = [...rest];\n          if (this.selectedReview.CReviewId) {\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []));\n          } else {\n            this.houseList2D = this.groupByFloor([...rest]);\n          }\n          this.isHouseList = true;\n        }\n      })).subscribe();\n    }\n    openPdfInNewTab(CFileUrl) {\n      if (CFileUrl) {\n        this.utilityService.openFileNewTab(CFileUrl);\n      }\n    }\n    getReviewList() {\n      return this._reviewService.apiReviewGetReviewListPost$Json({\n        body: {\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize,\n          CReviewName: this.searchQuery.CReviewName,\n          CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n          CStatus: this.searchQuery.seletedStatus.value,\n          CReviewType: this.searchQuery.selectedReviewType.value,\n          CExamineStatus: this.searchQuery.selectedExamineStatus.value\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.reviewList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      })).subscribe();\n    }\n    onSelectionChangeBuildCase() {\n      if (this.searchQuery.selectedBuildCase.value) {\n        this.getReviewList();\n      }\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries.map(res => {\n            return {\n              label: res.CBuildCaseName,\n              value: res.cID\n            };\n          });\n          if (LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != null && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != undefined && LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH) != \"\") {\n            let previous_search = JSON.parse(LocalStorageService.GetSessionStorage(STORAGE_KEY.REVIEW_SEARCH));\n            if (previous_search.CSelectedBuildCase != null && previous_search.CSelectedBuildCase != undefined) {\n              let index = this.userBuildCaseOptions.findIndex(x => x.value == previous_search.CSelectedBuildCase.value);\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[index];\n            } else {\n              this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n            }\n          } else {\n            this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n          }\n          if (this.searchQuery.selectedBuildCase.value) {\n            this.getReviewList();\n          }\n        }\n      })).subscribe();\n    }\n    openModel(ref, item) {\n      this.latestAction = 0;\n      this.isHouseList = false;\n      this.isNew = true;\n      this.clearImage();\n      this.selectedReview = {\n        selectedReviewType: this.reviewTypeOptions[0],\n        seletedStatus: this.statusOptions[0],\n        selectedExamineStatus: this.examineStatusOptions[0],\n        CReviewName: '',\n        CSort: 0,\n        CFileUrl: '',\n        CExamineNote: '',\n        CIsSelectAll: false\n      };\n      if (item) {\n        this.isNew = false;\n        this.getReviewById(item, ref);\n      } else {\n        this.isNew = true;\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmit(ref) {}\n    onClose(ref) {\n      ref.close();\n    }\n    validation() {\n      this.valid.clear();\n      if (this.isNew && !this.imageUrl) {\n        this.valid.addErrorMessage(`前台圖片`);\n      }\n      this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n    }\n    getActionName(actionID) {\n      let textR = \"\";\n      if (actionID != undefined) {\n        switch (actionID) {\n          case 1:\n            textR = \"傳送\";\n            break;\n          case 2:\n            textR = \"通過\";\n            break;\n          case 3:\n            textR = \"駁回\";\n            break;\n          default:\n            break;\n        }\n      }\n      return textR;\n    }\n    checkAllHouseIsValid(household, floor) {\n      let count = 0;\n      let total = 0;\n      if (household != null) {\n        for (let i = 1; i < this.houseList2D.length; i++) {\n          this.houseList2D[i].map(val => {\n            if (val.CHouseHold && val.CHouseHold == household) {\n              total++;\n              if (val.CIsEnable == true) {\n                count++;\n              }\n            }\n          });\n        }\n      }\n      if (floor != null) {\n        for (let i = 0; i < this.houseList2D.length; i++) {\n          this.houseList2D[i].map(val => {\n            if (val.CFloor == floor) {\n              total++;\n              if (val.CIsEnable == true) {\n                count++;\n              }\n            }\n          });\n        }\n      }\n      if (count == total) {\n        return false;\n      }\n      return true;\n    }\n    static {\n      this.ɵfac = function ReviewDocumentManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ReviewDocumentManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.ReviewService), i0.ɵɵdirectiveInject(i6.ReviewServiceCustom), i0.ɵɵdirectiveInject(i7.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReviewDocumentManagementComponent,\n        selectors: [[\"ngx-review-document-management\"]],\n        viewQuery: function ReviewDocumentManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 65,\n        vars: 15,\n        consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cReviewType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cReviewName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"cReviewName\", \"nbInput\", \"\", 1, \"w-full\", \"!max-w-[290px]\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-end\", \"justify-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-2\", \"text-center\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [\"for\", \"ReviewType\", 1, \"required-field\", \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"CReviewName\", 1, \"label\", \"col-3\", \"required-field\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u540D\\u7A31\", 1, \"col-9\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"d-flex\", \"flex-col\", \"mr-3\"], [\"for\", \"file\", 1, \"label\", \"col-3\", \"required-field\", 2, \"min-width\", \"172px\"], [2, \"color\", \"red\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf, .dwg\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\", \"required-field\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"cExamineNote\", 1, \"label\", \"col-3\", \"required-field\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"col-9\", \"!max-w-[320px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"class\", \"table-responsive mt-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", \"min-w-[90px]\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\", \"disabled\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\", 3, \"ngClass\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngIf\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\"], [1, \"font-bold\", 3, \"ngClass\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"]],\n        template: function ReviewDocumentManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n            i0.ɵɵtext(5, \"\\u53EF\\u4E0A\\u50B3\\u8981\\u63D0\\u4F9B\\u5BA2\\u6236\\u6A19\\u6E96\\u5716\\u8AAA\\uFF0C\\u6587\\u4EF6\\u5167\\u578B\\u5206\\u70BA\\u6A19\\u6E96\\u5716\\u53CA\\u8A2D\\u5099\\uFF0C\\u4E26\\u8A2D\\u5B9A\\u8A72\\u6A94\\u6848\\u9069\\u7528\\u7684\\u6236\\u5225\\u3002\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 7);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedBuildCase, $event) || (ctx.searchQuery.selectedBuildCase = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function ReviewDocumentManagementComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n            });\n            i0.ɵɵtemplate(12, ReviewDocumentManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5)(15, \"label\", 9);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedReviewType, $event) || (ctx.searchQuery.selectedReviewType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"label\", 11);\n            i0.ɵɵtext(22, \" \\u540D\\u7A31 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 12)(24, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CReviewName, $event) || (ctx.searchQuery.CReviewName = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 14);\n            i0.ɵɵtext(28, \" \\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"nb-select\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.seletedStatus, $event) || (ctx.searchQuery.seletedStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(30, ReviewDocumentManagementComponent_nb_option_30_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 4)(32, \"div\", 5)(33, \"label\", 16);\n            i0.ɵɵtext(34, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"nb-select\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedExamineStatus, $event) || (ctx.searchQuery.selectedExamineStatus = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(36, ReviewDocumentManagementComponent_nb_option_36_Template, 2, 2, \"nb-option\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 18);\n            i0.ɵɵtemplate(39, ReviewDocumentManagementComponent_button_39_Template, 3, 0, \"button\", 19)(40, ReviewDocumentManagementComponent_button_40_Template, 3, 0, \"button\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 21)(42, \"table\", 22)(43, \"thead\")(44, \"tr\", 23)(45, \"th\", 24);\n            i0.ɵɵtext(46, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"th\", 24);\n            i0.ɵɵtext(48, \"\\u540D\\u7A31\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"th\", 25);\n            i0.ɵɵtext(50, \"\\u9069\\u7528\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"th\", 24);\n            i0.ɵɵtext(52, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"th\", 24);\n            i0.ɵɵtext(54, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"th\", 26);\n            i0.ɵɵtext(56, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"th\", 27);\n            i0.ɵɵtext(58, \"\\u52D5\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"tbody\");\n            i0.ɵɵtemplate(60, ReviewDocumentManagementComponent_tr_60_Template, 19, 18, \"tr\", 28);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(61, \"nb-card-footer\", 29)(62, \"ngb-pagination\", 30);\n            i0.ɵɵtwoWayListener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(63, ReviewDocumentManagementComponent_ng_template_63_Template, 49, 23, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedBuildCase);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedReviewType);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.reviewTypeOptionsQuery);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CReviewName);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.seletedStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptionsQuery);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedExamineStatus);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.examineStatusOptionsQuery);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(20);\n            i0.ɵɵproperty(\"ngForOf\", ctx.reviewList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, i13.DateFormatPipe, NbDatepickerModule, NbDateFnsDateModule, LabelInOptionsPipe, DateFormatHourPipe]\n      });\n    }\n  }\n  return ReviewDocumentManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}