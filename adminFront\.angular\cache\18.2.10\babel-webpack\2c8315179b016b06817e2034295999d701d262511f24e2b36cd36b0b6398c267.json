{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiBuildCaseGetBuildCaseListPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseListPost$Plain.PATH, 'post');\n  if (params) {}\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiBuildCaseGetBuildCaseListPost$Plain.PATH = '/api/BuildCase/GetBuildCaseList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiBuildCaseGetBuildCaseListPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\build-case\\api-build-case-get-build-case-list-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { BuildCaseGetListReponseListResponseBase } from '../../models/build-case-get-list-reponse-list-response-base';\r\n\r\nexport interface ApiBuildCaseGetBuildCaseListPost$Plain$Params {\r\n}\r\n\r\nexport function apiBuildCaseGetBuildCaseListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiBuildCaseGetBuildCaseListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<BuildCaseGetListReponseListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiBuildCaseGetBuildCaseListPost$Plain.PATH, 'post');\r\n  if (params) {\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<BuildCaseGetListReponseListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiBuildCaseGetBuildCaseListPost$Plain.PATH = '/api/BuildCase/GetBuildCaseList';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAOtD,OAAM,SAAUC,sCAAsCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAsD,EAAEC,OAAqB;EACrK,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,sCAAsC,CAACM,IAAI,EAAE,MAAM,CAAC;EAC3F,IAAIH,MAAM,EAAE,CACZ;EAEA,OAAOF,IAAI,CAACM,OAAO,CACjBF,EAAE,CAACG,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEN;EAAO,CAAE,CAAC,CAClE,CAACO,IAAI,CACJd,MAAM,CAAEe,CAAM,IAA6BA,CAAC,YAAYhB,YAAY,CAAC,EACrEE,GAAG,CAAEc,CAAoB,IAAI;IAC3B,OAAOA,CAAgE;EACzE,CAAC,CAAC,CACH;AACH;AAEAZ,sCAAsC,CAACM,IAAI,GAAG,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}