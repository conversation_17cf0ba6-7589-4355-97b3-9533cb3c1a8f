{"ast": null, "code": "export var EnumHouseProgress = /*#__PURE__*/function (EnumHouseProgress) {\n  EnumHouseProgress[EnumHouseProgress[\"\\u5C1A\\u672A\\u958B\\u59CB\"] = 0] = \"\\u5C1A\\u672A\\u958B\\u59CB\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u5DF2\\u95B1\\u8B80\\u64CD\\u4F5C\\u8AAA\\u660E\"] = 1] = \"\\u5DF2\\u95B1\\u8B80\\u64CD\\u4F5C\\u8AAA\\u660E\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u9078\\u6A23\\u5B8C\\u6210\"] = 2] = \"\\u9078\\u6A23\\u5B8C\\u6210\";\n  EnumHouseProgress[EnumHouseProgress[\"\\u7C3D\\u7F72\\u5B8C\\u6210\"] = 3] = \"\\u7C3D\\u7F72\\u5B8C\\u6210\";\n  return EnumHouseProgress;\n}(EnumHouseProgress || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}