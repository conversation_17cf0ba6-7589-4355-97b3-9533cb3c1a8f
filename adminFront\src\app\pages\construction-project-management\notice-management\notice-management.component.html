<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <h1 class="font-bold text-[#818181]">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="buildingName" class="label col-3">建案</label>
          <nb-select placeholder="建案" [(ngModel)]="selectedCBuildCase" class="col-9"
            (selectedChange)="onChangeBuildCase()">
            <nb-option *ngFor="let case of userBuildCaseOptions" [value]="case">
              {{ case.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="col-md-6">
        <div class="d-flex justify-content-end w-full">
          <button class="btn btn-secondary btn-sm" (click)="openModel(dialog)" *ngIf="isCreate">
            新增檔案 <i class="fas fa-plus"></i>
          </button>
        </div>
      </div>
    </div>

    <ng-container *ngIf="listSpecialNoticeFile">
      <div class="table-responsive mt-4">
        <h4 class="text-xl font-bold">地主戶 </h4>
        <table class="table table-striped border mt-3" style="background-color:#f3f3f3;">
          <thead>
            <tr style="background-color: #27ae60; color: white;" class="text-center">
              <th scope="col" class="col-1 ">檔案名稱</th>
              <th scope="col" class="col-1">適用戶別 </th>
              <th scope="col" class="col-1">審核狀態</th>
              <th scope="col" class="col-1">審核日期</th>
              <th scope="col" class="col-1">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of listSpecialNoticeFile?.ListLandLords ; let i = index" class="text-center">
              <td>
                <a class="cursor-pointer text-blue-500" (click)="openPdfInNewTab(item?.CFile)">{{ item?.CFileName}}</a>
              </td>
              <td>{{ item.CHouseHold?.join('、')}}</td>
              <td>{{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?
                cExamineStatusOption[item.CExamineStatus] : '' }}</td>
              <td>{{ item?.CApproveDate | date:'yyyy/MM/dd HH:mm:ss' }}</td>
              <td>
                <button class="btn btn-outline-success btn-sm text-left m-[2px]" *ngIf="isUpdate"
                  (click)="openModel(dialog, item)">
                  編輯
                </button>
                <button class="btn btn-outline-danger btn-sm m-[2px]" *ngIf="isDelete" (click)="onDelete(item)">
                  刪除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex justify-center my-3">
        <ngb-pagination [(page)]="pageIndex" [pageSize]="pageSize" [collectionSize]="totalRecords"
          (pageChange)="pageChanged($event)" aria-label="Pagination">
        </ngb-pagination>
      </div>
      <div class="table-responsive mt-4">
        <h4 class="text-xl font-bold">銷售戶</h4>
        <table class="table table-striped border mt-3" style="background-color:#f3f3f3;">
          <thead>
            <tr style="background-color: #27ae60; color: white;" class="text-center">
              <th scope="col" class="col-1 ">檔案名稱</th>
              <th scope="col" class="col-1">適用戶別 </th>
              <th scope="col" class="col-1">審核狀態</th>
              <th scope="col" class="col-1">審核日期</th>
              <th scope="col" class="col-1">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of listSpecialNoticeFile?.ListSales ; let i = index" class="text-center">
              <td>
                <a class="cursor-pointer text-blue-500" (click)="openPdfInNewTab(item?.CFile)">{{ item?.CFileName}}</a>
              </td>
              <td>{{ item.CHouseHold?.join('、')}}</td>
              <td>
                {{ item.CExamineStatus != null && [0, 1, 2].includes(item.CExamineStatus) ?
                cExamineStatusOption[item.CExamineStatus] : '' }}
              </td>
              <td>{{ item?.CApproveDate | date:'yyyy-MM-dd HH:mm:ss'}}</td>
              <td>
                <button class="btn btn-outline-success btn-sm text-left m-[2px]" (click)="openModel(dialog, item)"
                  *ngIf="isUpdate">
                  編輯
                </button>
                <button class="btn btn-outline-danger btn-sm m-[2px]" *ngIf="isDelete" (click)="onDelete(item)">
                  刪除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="flex justify-center my-3">
        <ngb-pagination [(page)]="pageIndexSales" [pageSize]="pageSizeSales" [collectionSize]="totalRecordsSales"
          (pageChange)="pageChangedSales($event)" aria-label="Pagination">
        </ngb-pagination>
      </div>
    </ng-container>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">

  </nb-card-footer>
</nb-card>



<ng-template #dialog let-dialog let-ref="dialogRef">
  <nb-card style="width:800px; max-height: 95vh">
    <nb-card-header> {{isNew ? '新增檔案-地主戶': '編輯' }}
    </nb-card-header>
    <nb-card-body class="px-4" *ngIf="isHouseList">
      <div class="form-group d-flex">
        <label for="remark" style="min-width:75px" class="required-field mr-4" baseLabel>檔案類型</label>
        <nb-select placeholder="Select Status" [(ngModel)]="saveSpecialNoticeFile.selectedCNoticeType" class="w-full"
          [disabled]="!isNew" (ngModelChange)="onStatusChange($event)">
          <nb-option *ngFor="let status of cNoticeTypeOptions" [value]="status">
            {{ status.label }}
          </nb-option>
        </nb-select>
      </div>
      <div class="form-group d-flex align-items-baseline">
        <div class="d-flex flex-col mr-3">
          <label for="file" class="required-field  mb-0" style="min-width:75px" baseLabel>上傳檔案
          </label>
          <span style="font-size: 10px; color:red;">只接受pdf</span>
        </div>
        <div class="flex flex-col items-start space-y-4">
          <input type="file" id="fileInput" accept="image/jpeg, image/jpg, application/pdf" class="hidden"
            style="display: none" (change)="onFileSelected($event)">
          <label for="fileInput"
            class="cursor-pointer bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            <i class="fa-solid fa-cloud-arrow-up mr-2"></i> 上傳
          </label>
          <div class="flex items-center space-x-2" *ngIf="fileName">
            <span class="text-gray-600">{{ fileName }}</span>
            <button type="button" (click)="clearImage()" class="text-red-500 hover:text-red-700">
              <i class="fa-solid fa-trash"></i>
            </button>
          </div>
        </div>
        <span class="text-sm ml-4" *ngIf="saveSpecialNoticeFile && saveSpecialNoticeFile?.CFileUrl"
          [ngClass]="imageUrl ? 'hidden':''">
          <a (click)="openPdfInNewTab(saveSpecialNoticeFile.CFileUrl)" class="cursor-pointer text-blue-500">
            {{saveSpecialNoticeFile.CFileUrl}}
          </a>
        </span>
      </div>
      <div class="form-group d-flex mb-0">
        <label for="houseList2D" baseLabel class="required-field mr-3" style="min-width:75px">適用戶別</label>
      </div>
      <div class="table-responsive mt-1" *ngIf="isHouseList">
        <table class="table table-bordered" style="background-color:#f3f3f3;">
          <thead>
            <tr *ngIf="houseList2D.length">
              <th></th>
              <th *ngFor="let house of houseList2D[0]; let idx = index;">
                <nb-checkbox status="basic" (checkedChange)="enableAllAtIndex($event, idx)"
                  [checked]="isCheckAllColumnChecked(idx)"
                  [disabled]="saveSpecialNoticeFile.selectedCNoticeType.value == 1 ? true : false">
                  <span class="font-medium">全選</span>
                </nb-checkbox>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of houseList2D">
              <td>
                <nb-checkbox status="basic" [checked]="isCheckAllRowChecked(row)"
                  (checkedChange)="enableAllRow($event,row)"
                  [disabled]="saveSpecialNoticeFile.selectedCNoticeType.value== 1 ? true : false">
                  <span class="font-medium">全選</span>
                </nb-checkbox>
              </td>
              <td *ngFor="let house of row">
                <!-- *ngIf="saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType" -->
                <ng-container>
                  <nb-checkbox status="basic" [(checked)]="house.CIsSelect"
                    [disabled]="!house.CHouseHold || !house.CIsEnable || !(saveSpecialNoticeFile.selectedCNoticeType.value === house.CHouseType)||saveSpecialNoticeFile.CExamineStauts==0">
                    <span class="font-bold" [ngClass]="{ '!text-red': house.CID }"> {{ house.CHouseHold || 'null' }} -
                      {{ house.CFloor }}</span>
                  </nb-checkbox>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="form-group d-flex align-items-center">
        <label for="status" baseLabel class="required-field mr-3" style="min-width:75px">送審備註</label>
        <textarea nbInput [(ngModel)]="saveSpecialNoticeFile.CExamineNote" [rows]="4"
          class="resize-none !max-w-full w-full"></textarea>
      </div>

      <div class="d-flex justify-content-center min-w-[90px]">
        <button class="btn btn-danger btn-sm mr-4" (click)="onClose(ref)">
          取消
        </button>
        <button class="btn btn-success btn-sm min-w-[90px]" (click)="onSaveSpecialNoticeFile(ref)">
          儲存</button>
      </div>
      <div class="w-full" *ngIf="!isNew">
        <div class="form-group d-flex align-items-center">
          <label for="status" baseLabel class="mr-3" style="min-width:75px">審核歷程</label>
        </div>
        <table class="table table-bordered" style="background-color:#f3f3f3;">
          <thead>
            <tr>
              <th>時間</th>
              <th>使用者</th>
              <th>動作</th>
              <th>說明</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of saveSpecialNoticeFile.tblExamineLogs">
              <td>{{row.CCreateDt | dateFormatHour}}</td>
              <td>{{row.CCreator}}</td>
              <td>{{getActionName(row.CAction)}}</td>
              <td>{{row.CExamineNote}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </nb-card-body>
  </nb-card>
</ng-template>