{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FilterListItemsPipe } from 'src/app/@theme/pipes/searchText.pipe';\nimport { concatMap, finalize, tap } from 'rxjs';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { SharedModule } from '../../components/shared.module';\nimport { FilterByKeyPipe } from 'src/app/@theme/pipes/filterByKey.pipe';\nimport * as _ from 'lodash';\nlet ProjectManagementComponent = class ProjectManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _buildCaseService, router, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._utilityService = _utilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.statusOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '停用'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '啟用'\n    }];\n    this.isConfirmOptions = [{\n      cID: 0,\n      CValue: 0,\n      CTitle: '未確認'\n    }, {\n      cID: 1,\n      CValue: 1,\n      CTitle: '已確認'\n    }];\n    this.statusOptionsShow = [{\n      cID: 0,\n      CValue: false,\n      CTitle: '停用'\n    }, {\n      cID: 1,\n      CValue: true,\n      CTitle: '啟用'\n    }];\n    // CFinalFileNotice: string = \"\"\n    this.listBuildCase = [];\n    this.search = \"\";\n    this.initBuildCase = {\n      CBuildCaseName: '',\n      CFrontImage: '',\n      CSystemInstruction: '',\n      ImageList: null,\n      cID: undefined,\n      CStatus: undefined,\n      CFinalFileNotice: ''\n    };\n    this.isNew = true;\n    this.imgSrc = null;\n    this.imageUrl = null;\n    this.fileName = null;\n  }\n  onRelatedDocument(id) {\n    this.router.navigateByUrl(`/pages/related-documents/${id}`);\n  }\n  ngOnInit() {\n    this.getListBuildCase().subscribe();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedShowPrice = this.statusOptionsShow[0];\n    this.selectedShowAllSign = this.statusOptionsShow[0];\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n  }\n  getListBuildCase() {\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CName: this.search,\n        CIsPagi: true\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listBuildCase = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  addNew(ref) {\n    this.isNew = true;\n    this.clearForm();\n    this.selectedStatus = this.statusOptions[0];\n    this.selectedShowPrice = this.statusOptionsShow[0];\n    this.selectedShowAllSign = this.statusOptionsShow[0];\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue;\n    this.selectedIsConfirm = this.isConfirmOptions[0];\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue;\n    this.selectedBuildCase.CCustomerSign = true;\n    this.dialogService.open(ref);\n  }\n  getBase64Image(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = e.target.result;\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  clearForm() {\n    this.selectedBuildCase = {\n      ...this.initBuildCase\n    };\n    this.clearImage();\n  }\n  onSelectedBuildCase(data, ref) {\n    console.log(data);\n    this.isNew = false;\n    this.dialogService.open(ref);\n    this.clearImage();\n    this.selectedBuildCase = _.cloneDeep(data);\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction);\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus];\n    this.selectedShowPrice = this.statusOptionsShow[this.selectedBuildCase.CShowPrice == true ? 1 : 0];\n    this.selectedShowAllSign = this.statusOptionsShow[this.selectedBuildCase.CShowSignAll == true ? 1 : 0];\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName);\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50);\n    if (this.isNew) {\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage);\n    }\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction);\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString());\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  nl2br(str) {\n    if (typeof str === 'undefined' || str === null) {\n      return '';\n    }\n    return str.replace(/\\n/g, '<br>');\n    ;\n  }\n  onSubmit(ref) {\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\n    this.selectedBuildCase.CShowPrice = this.selectedShowPrice.CValue;\n    this.selectedBuildCase.CShowSignAll = this.selectedShowAllSign.CValue;\n    const requestBody = {\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\n      CStatus: this.selectedStatus.CValue,\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction),\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice,\n      CShowPrice: this.selectedShowPrice.CValue,\n      CShowSignAll: this.selectedShowAllSign.CValue,\n      CCustomerSign: this.selectedBuildCase.CCustomerSign == true ? true : false\n    };\n    if (this.isNew && this.imageUrl) {\n      // NEW\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl); // as string\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n    } else {\n      // EDIT\n      if (this.imageUrl) {\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl);\n      }\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID;\n    }\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({\n      body: requestBody\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n      } else {\n        this.message.showSucessMSG(\"建案名稱不可重複\");\n      }\n    }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n  }\n  checkedCustomerSign(checked) {\n    this.selectedBuildCase.CCustomerSign = checked;\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListBuildCase().subscribe();\n  }\n  onDelete(data, ref) {\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\n        body: {\n          \"CBuildCaseID\": data.cID\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      }), concatMap(() => this.getListBuildCase()), finalize(() => ref.close())).subscribe();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n};\n__decorate([ViewChild('fileInput')], ProjectManagementComponent.prototype, \"fileInput\", void 0);\nProjectManagementComponent = __decorate([Component({\n  selector: 'ngx-project-management',\n  templateUrl: './project-management.component.html',\n  styleUrls: ['./project-management.component.scss'],\n  standalone: true,\n  imports: [CommonModule, SharedModule, FilterListItemsPipe, FilterByKeyPipe]\n})], ProjectManagementComponent);\nexport { ProjectManagementComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "FilterListItemsPipe", "concatMap", "finalize", "tap", "BaseComponent", "SharedModule", "FilterByKeyPipe", "_", "ProjectManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_buildCaseService", "router", "_utilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "statusOptions", "cID", "CValue", "<PERSON><PERSON><PERSON>", "isConfirmOptions", "statusOptionsShow", "listBuildCase", "search", "initBuildCase", "CBuildCaseName", "CFrontImage", "CSystemInstruction", "ImageList", "undefined", "CStatus", "CFinalFileNotice", "isNew", "imgSrc", "imageUrl", "fileName", "onRelatedDocument", "id", "navigateByUrl", "ngOnInit", "getListBuildCase", "subscribe", "selectedStatus", "selectedShowPrice", "selectedShowAllSign", "selectedIsConfirm", "selectedBuildCase", "apiBuildCaseGetAllBuildCasePost$Json", "body", "PageIndex", "PageSize", "CName", "CIsPagi", "pipe", "res", "Entries", "StatusCode", "TotalItems", "addNew", "ref", "clearForm", "CIsConfirm", "CCustomerSign", "open", "getBase64Image", "file", "Promise", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "onload", "result", "onerror", "error", "onFileSelected", "event", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "includes", "name", "e", "fileInput", "nativeElement", "value", "clearImage", "onSelectedBuildCase", "data", "console", "log", "cloneDeep", "htmltoText", "CShowPrice", "CShowSignAll", "validation", "clear", "required", "isStringMaxLength", "toString", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "nl2br", "str", "replace", "onSubmit", "requestBody", "CBuildCaseID", "errorMessages", "length", "showErrorMSGs", "apiBuildCaseSaveBuildCasePost$Json", "showSucessMSG", "close", "checkedCustomerSign", "checked", "pageChanged", "newPage", "onDelete", "window", "confirm", "apiBuildCaseDeleteBuildCasePost$Json", "Message", "onClose", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\construction-project-management\\project-management\\project-management.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, SaveBuildCaseArgs } from 'src/services/api/models';\r\nimport { Router } from '@angular/router';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FilterListItemsPipe } from 'src/app/@theme/pipes/searchText.pipe';\r\nimport { concatMap, finalize, tap } from 'rxjs';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { FilterByKeyPipe } from 'src/app/@theme/pipes/filterByKey.pipe';\r\nimport * as _ from 'lodash';\r\n@Component({\r\n  selector: 'ngx-project-management',\r\n  templateUrl: './project-management.component.html',\r\n  styleUrls: ['./project-management.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    FilterListItemsPipe,\r\n    FilterByKeyPipe,\r\n  ],\r\n})\r\n\r\nexport class ProjectManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n  statusOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '停用',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '啟用',\r\n    }\r\n  ]\r\n\r\n  isConfirmOptions: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: 0,\r\n      CTitle: '未確認',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: 1,\r\n      CTitle: '已確認',\r\n    }\r\n  ]\r\n\r\n  statusOptionsShow: any[] = [\r\n    {\r\n      cID: 0,\r\n      CValue: false,\r\n      CTitle: '停用',\r\n    },\r\n    {\r\n      cID: 1,\r\n      CValue: true,\r\n      CTitle: '啟用',\r\n    }\r\n  ]\r\n\r\n  selectedStatus: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  selectedIsConfirm: {\r\n    cID: number, CValue: number, CTitle: string\r\n  }\r\n  selectedShowPrice: {\r\n    cID: number, CValue: boolean, CTitle: string\r\n  }\r\n  selectedShowAllSign: {\r\n    cID: number, CValue: boolean, CTitle: string\r\n  }\r\n  // CFinalFileNotice: string = \"\"\r\n\r\n  listBuildCase: BuildCaseGetListReponse[] = []\r\n  selectedBuildCase: BuildCaseGetListReponse & {\r\n    CIsConfirm?: number\r\n  }\r\n  search: string = \"\"\r\n\r\n  initBuildCase: BuildCaseGetListReponse = {\r\n    CBuildCaseName: '',\r\n    CFrontImage: '',\r\n    CSystemInstruction: '',\r\n    ImageList: null,\r\n    cID: undefined,\r\n    CStatus: undefined,\r\n    CFinalFileNotice: ''\r\n  }\r\n\r\n  isNew = true\r\n\r\n  imgSrc: string | ArrayBuffer | null = null;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n\r\n  imageUrl: string | ArrayBuffer | null = null;\r\n  fileName: string | null = null;\r\n\r\n  onRelatedDocument(id: any) {\r\n    this.router.navigateByUrl(`/pages/related-documents/${id}`)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase().subscribe();\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedShowPrice = this.statusOptionsShow[0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[0]\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n  }\r\n\r\n  getListBuildCase() {\r\n    return this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({ body: {\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize,\r\n      CName: this.search,\r\n      CIsPagi: true\r\n    } }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listBuildCase = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    this.isNew = true;\r\n    this.clearForm()\r\n    this.selectedStatus = this.statusOptions[0]\r\n    this.selectedShowPrice = this.statusOptionsShow[0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[0]\r\n    this.selectedBuildCase.CStatus = this.statusOptions[0].CValue\r\n    this.selectedIsConfirm = this.isConfirmOptions[0]\r\n    this.selectedBuildCase.CIsConfirm = this.isConfirmOptions[0].CValue\r\n    this.selectedBuildCase.CCustomerSign = true;\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n\r\n  getBase64Image(file: File): Promise<string | ArrayBuffer | null> {\r\n    return new Promise((resolve, reject) => {\r\n      const reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => resolve(reader.result);\r\n      reader.onerror = error => reject(error);\r\n    });\r\n  }\r\n\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = e.target.result;\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  clearForm() {\r\n    this.selectedBuildCase = { ...this.initBuildCase }\r\n    this.clearImage()\r\n  }\r\n\r\n  onSelectedBuildCase(data: any, ref: any) {\r\n    console.log(data);\r\n    this.isNew = false;\r\n    this.dialogService.open(ref);\r\n    this.clearImage()\r\n    this.selectedBuildCase = _.cloneDeep(data);\r\n    this.selectedBuildCase.CSystemInstruction = this._utilityService.htmltoText(data.CSystemInstruction)\r\n    this.selectedStatus = this.statusOptions[this.selectedBuildCase.CStatus!]\r\n    this.selectedShowPrice = this.statusOptionsShow[this.selectedBuildCase.CShowPrice! == true ? 1 : 0]\r\n    this.selectedShowAllSign = this.statusOptionsShow[this.selectedBuildCase.CShowSignAll! == true ? 1 : 0]\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[建案名稱]', this.selectedBuildCase.CBuildCaseName)\r\n    this.valid.isStringMaxLength('[建案名稱]', this.selectedBuildCase.CBuildCaseName, 50)\r\n    if (this.isNew) {\r\n      this.valid.required('[前台圖片]', this.selectedBuildCase.CFrontImage)\r\n    }\r\n    this.valid.required('[系統操作說明]', this.selectedBuildCase.CSystemInstruction)\r\n    this.valid.required('[狀態]', this.selectedBuildCase.CStatus?.toString())\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  }\r\n\r\n  nl2br(str: string) {\r\n    if (typeof str === 'undefined' || str === null) {\r\n      return '';\r\n    }\r\n    return str.replace(/\\n/g, '<br>');;\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.selectedBuildCase.CStatus = this.selectedStatus.CValue;\r\n    this.selectedBuildCase.CShowPrice = this.selectedShowPrice.CValue;\r\n    this.selectedBuildCase.CShowSignAll = this.selectedShowAllSign.CValue;\r\n    const requestBody: SaveBuildCaseArgs = {\r\n      CBuildCaseName: this.selectedBuildCase.CBuildCaseName,\r\n      CStatus: this.selectedStatus.CValue,\r\n      CSystemInstruction: this.nl2br(this.selectedBuildCase.CSystemInstruction!),\r\n      CIsConfirm: this.selectedIsConfirm.CValue == 0 ? false : true,\r\n      CFinalFileNotice: this.selectedBuildCase.CFinalFileNotice,\r\n      CShowPrice: this.selectedShowPrice.CValue,\r\n      CShowSignAll: this.selectedShowAllSign.CValue,\r\n      CCustomerSign: this.selectedBuildCase.CCustomerSign == true ? true : false,\r\n    }\r\n\r\n    if (this.isNew && this.imageUrl) { // NEW\r\n      this.selectedBuildCase.CFrontImage = this.removeBase64Prefix(this.imageUrl)// as string\r\n      requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n    } else { // EDIT\r\n      if (this.imageUrl) {\r\n        requestBody.CFrontImage = this.removeBase64Prefix(this.imageUrl)\r\n      }\r\n      requestBody.CBuildCaseID = this.selectedBuildCase.cID\r\n    }\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._buildCaseService.apiBuildCaseSaveBuildCasePost$Json({ body: requestBody }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n        } else {\r\n          this.message.showSucessMSG(\"建案名稱不可重複\")\r\n        }\r\n      }),\r\n      concatMap(() => this.getListBuildCase()),\r\n      finalize(() => ref.close())\r\n    ).subscribe();\r\n  }\r\n\r\n  checkedCustomerSign(checked: boolean) {\r\n    this.selectedBuildCase.CCustomerSign = checked\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListBuildCase().subscribe()\r\n  }\r\n\r\n  onDelete(data: any, ref: any) {\r\n    if (window.confirm(`確定要刪除【項目${data.CBuildCaseName}】?`)) {\r\n      this._buildCaseService.apiBuildCaseDeleteBuildCasePost$Json({\r\n        body: {\r\n          \"CBuildCaseID\": data.cID\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.message.showSucessMSG(\"執行成功\");\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n        }),\r\n        concatMap(() => this.getListBuildCase()),\r\n        finalize(() => ref.close())\r\n      ).subscribe()\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACxE,SAASC,YAAY,QAAQ,iBAAiB;AAQ9C,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,MAAM;AAC/C,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,eAAe,QAAQ,uCAAuC;AACvE,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAcpB,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA2B,SAAQJ,aAAa;EAC3DK,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,iBAAmC,EACnCC,MAAc,EACdC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAKhB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IACzB,KAAAC,aAAa,GAAU,CACrB;MACEC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;KACT,EACD;MACEF,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;KACT,CACF;IAED,KAAAC,gBAAgB,GAAU,CACxB;MACEH,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;KACT,EACD;MACEF,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;KACT,CACF;IAED,KAAAE,iBAAiB,GAAU,CACzB;MACEJ,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;KACT,EACD;MACEF,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACT,CACF;IAcD;IAEA,KAAAG,aAAa,GAA8B,EAAE;IAI7C,KAAAC,MAAM,GAAW,EAAE;IAEnB,KAAAC,aAAa,GAA4B;MACvCC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,EAAE;MACtBC,SAAS,EAAE,IAAI;MACfX,GAAG,EAAEY,SAAS;MACdC,OAAO,EAAED,SAAS;MAClBE,gBAAgB,EAAE;KACnB;IAED,KAAAC,KAAK,GAAG,IAAI;IAEZ,KAAAC,MAAM,GAAgC,IAAI;IAG1C,KAAAC,QAAQ,GAAgC,IAAI;IAC5C,KAAAC,QAAQ,GAAkB,IAAI;EAjF9B;EAmFAC,iBAAiBA,CAACC,EAAO;IACvB,IAAI,CAAC3B,MAAM,CAAC4B,aAAa,CAAC,4BAA4BD,EAAE,EAAE,CAAC;EAC7D;EAESE,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE,CAACC,SAAS,EAAE;IACnC,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC1B,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACtB,iBAAiB,CAAC,CAAC,CAAC;IAClD,IAAI,CAACuB,mBAAmB,GAAG,IAAI,CAACvB,iBAAiB,CAAC,CAAC,CAAC;IACpD,IAAI,CAACwB,iBAAiB,GAAG,IAAI,CAACzB,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC0B,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAACtB;IAAa,CAAE;EACpD;EAEAgB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC/B,iBAAiB,CAACsC,oCAAoC,CAAC;MAAEC,IAAI,EAAE;QACzEC,SAAS,EAAE,IAAI,CAACnC,SAAS;QACzBoC,QAAQ,EAAE,IAAI,CAACrC,QAAQ;QACvBsC,KAAK,EAAE,IAAI,CAAC5B,MAAM;QAClB6B,OAAO,EAAE;;IACV,CAAE,CAAC,CAACC,IAAI,CACPvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAClC,aAAa,GAAGgC,GAAG,CAACC,OAAQ,IAAI,EAAE;QACvC,IAAI,CAACxC,YAAY,GAAGuC,GAAG,CAACG,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,MAAMA,CAACC,GAAQ;IACb,IAAI,CAAC3B,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC4B,SAAS,EAAE;IAChB,IAAI,CAAClB,cAAc,GAAG,IAAI,CAAC1B,aAAa,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACtB,iBAAiB,CAAC,CAAC,CAAC;IAClD,IAAI,CAACuB,mBAAmB,GAAG,IAAI,CAACvB,iBAAiB,CAAC,CAAC,CAAC;IACpD,IAAI,CAACyB,iBAAiB,CAAChB,OAAO,GAAG,IAAI,CAACd,aAAa,CAAC,CAAC,CAAC,CAACE,MAAM;IAC7D,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACzB,gBAAgB,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC0B,iBAAiB,CAACe,UAAU,GAAG,IAAI,CAACzC,gBAAgB,CAAC,CAAC,CAAC,CAACF,MAAM;IACnE,IAAI,CAAC4B,iBAAiB,CAACgB,aAAa,GAAG,IAAI;IAC3C,IAAI,CAACxD,aAAa,CAACyD,IAAI,CAACJ,GAAG,CAAC;EAC9B;EAGAK,cAAcA,CAACC,IAAU;IACvB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC1BI,MAAM,CAACG,MAAM,GAAG,MAAML,OAAO,CAACE,MAAM,CAACI,MAAM,CAAC;MAC5CJ,MAAM,CAACK,OAAO,GAAGC,KAAK,IAAIP,MAAM,CAACO,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EAGAC,cAAcA,CAACC,KAAU;IACvB,MAAMZ,IAAI,GAASY,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAAChB,IAAI,CAACiB,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC3E,OAAO,CAAC4E,YAAY,CAAC,kBAAkB,CAAC;MAC7C;IACF;IACA,IAAIlB,IAAI,EAAE;MACR,MAAMmB,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;MACnE,IAAIA,YAAY,CAACC,QAAQ,CAACpB,IAAI,CAACiB,IAAI,CAAC,EAAE;QACpC,IAAI,CAAC/C,QAAQ,GAAG8B,IAAI,CAACqB,IAAI;QACzB,MAAMjB,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACG,MAAM,GAAIe,CAAM,IAAI;UACzB,IAAI,CAACrD,QAAQ,GAAGqD,CAAC,CAACT,MAAM,CAACL,MAAM;UAC/B,IAAI,IAAI,CAACe,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACDrB,MAAM,CAACE,aAAa,CAACN,IAAI,CAAC;MAC5B;IACF;EACF;EAEA0B,UAAUA,CAAA;IACR,IAAI,IAAI,CAACzD,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACqD,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACC,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEA9B,SAASA,CAAA;IACP,IAAI,CAACd,iBAAiB,GAAG;MAAE,GAAG,IAAI,CAACtB;IAAa,CAAE;IAClD,IAAI,CAACmE,UAAU,EAAE;EACnB;EAEAC,mBAAmBA,CAACC,IAAS,EAAElC,GAAQ;IACrCmC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;IACjB,IAAI,CAAC7D,KAAK,GAAG,KAAK;IAClB,IAAI,CAAC1B,aAAa,CAACyD,IAAI,CAACJ,GAAG,CAAC;IAC5B,IAAI,CAACgC,UAAU,EAAE;IACjB,IAAI,CAAC7C,iBAAiB,GAAG5C,CAAC,CAAC8F,SAAS,CAACH,IAAI,CAAC;IAC1C,IAAI,CAAC/C,iBAAiB,CAACnB,kBAAkB,GAAG,IAAI,CAAChB,eAAe,CAACsF,UAAU,CAACJ,IAAI,CAAClE,kBAAkB,CAAC;IACpG,IAAI,CAACe,cAAc,GAAG,IAAI,CAAC1B,aAAa,CAAC,IAAI,CAAC8B,iBAAiB,CAAChB,OAAQ,CAAC;IACzE,IAAI,CAACa,iBAAiB,GAAG,IAAI,CAACtB,iBAAiB,CAAC,IAAI,CAACyB,iBAAiB,CAACoD,UAAW,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACnG,IAAI,CAACtD,mBAAmB,GAAG,IAAI,CAACvB,iBAAiB,CAAC,IAAI,CAACyB,iBAAiB,CAACqD,YAAa,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACzG;EAEAC,UAAUA,CAAA;IACR,IAAI,CAAC5F,KAAK,CAAC6F,KAAK,EAAE;IAClB,IAAI,CAAC7F,KAAK,CAAC8F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxD,iBAAiB,CAACrB,cAAc,CAAC;IACpE,IAAI,CAACjB,KAAK,CAAC+F,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAACzD,iBAAiB,CAACrB,cAAc,EAAE,EAAE,CAAC;IACjF,IAAI,IAAI,CAACO,KAAK,EAAE;MACd,IAAI,CAACxB,KAAK,CAAC8F,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACxD,iBAAiB,CAACpB,WAAW,CAAC;IACnE;IACA,IAAI,CAAClB,KAAK,CAAC8F,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACxD,iBAAiB,CAACnB,kBAAkB,CAAC;IAC1E,IAAI,CAACnB,KAAK,CAAC8F,QAAQ,CAAC,MAAM,EAAE,IAAI,CAACxD,iBAAiB,CAAChB,OAAO,EAAE0E,QAAQ,EAAE,CAAC;EACzE;EAEAC,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAEAI,KAAKA,CAACC,GAAW;IACf,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC9C,OAAO,EAAE;IACX;IACA,OAAOA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;IAAC;EACpC;EAEAC,QAAQA,CAACtD,GAAQ;IACf,IAAI,CAACb,iBAAiB,CAAChB,OAAO,GAAG,IAAI,CAACY,cAAc,CAACxB,MAAM;IAC3D,IAAI,CAAC4B,iBAAiB,CAACoD,UAAU,GAAG,IAAI,CAACvD,iBAAiB,CAACzB,MAAM;IACjE,IAAI,CAAC4B,iBAAiB,CAACqD,YAAY,GAAG,IAAI,CAACvD,mBAAmB,CAAC1B,MAAM;IACrE,MAAMgG,WAAW,GAAsB;MACrCzF,cAAc,EAAE,IAAI,CAACqB,iBAAiB,CAACrB,cAAc;MACrDK,OAAO,EAAE,IAAI,CAACY,cAAc,CAACxB,MAAM;MACnCS,kBAAkB,EAAE,IAAI,CAACmF,KAAK,CAAC,IAAI,CAAChE,iBAAiB,CAACnB,kBAAmB,CAAC;MAC1EkC,UAAU,EAAE,IAAI,CAAChB,iBAAiB,CAAC3B,MAAM,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;MAC7Da,gBAAgB,EAAE,IAAI,CAACe,iBAAiB,CAACf,gBAAgB;MACzDmE,UAAU,EAAE,IAAI,CAACvD,iBAAiB,CAACzB,MAAM;MACzCiF,YAAY,EAAE,IAAI,CAACvD,mBAAmB,CAAC1B,MAAM;MAC7C4C,aAAa,EAAE,IAAI,CAAChB,iBAAiB,CAACgB,aAAa,IAAI,IAAI,GAAG,IAAI,GAAG;KACtE;IAED,IAAI,IAAI,CAAC9B,KAAK,IAAI,IAAI,CAACE,QAAQ,EAAE;MAAE;MACjC,IAAI,CAACY,iBAAiB,CAACpB,WAAW,GAAG,IAAI,CAAC+E,kBAAkB,CAAC,IAAI,CAACvE,QAAQ,CAAC;MAC3EgF,WAAW,CAACxF,WAAW,GAAG,IAAI,CAAC+E,kBAAkB,CAAC,IAAI,CAACvE,QAAQ,CAAC;IAClE,CAAC,MAAM;MAAE;MACP,IAAI,IAAI,CAACA,QAAQ,EAAE;QACjBgF,WAAW,CAACxF,WAAW,GAAG,IAAI,CAAC+E,kBAAkB,CAAC,IAAI,CAACvE,QAAQ,CAAC;MAClE;MACAgF,WAAW,CAACC,YAAY,GAAG,IAAI,CAACrE,iBAAiB,CAAC7B,GAAG;IACvD;IACA,IAAI,CAACmF,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5F,KAAK,CAAC4G,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC9G,OAAO,CAAC+G,aAAa,CAAC,IAAI,CAAC9G,KAAK,CAAC4G,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAC3G,iBAAiB,CAAC8G,kCAAkC,CAAC;MAAEvE,IAAI,EAAEkE;IAAW,CAAE,CAAC,CAAC7D,IAAI,CACnFvD,GAAG,CAACwD,GAAG,IAAG;MACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACjD,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAACjH,OAAO,CAACiH,aAAa,CAAC,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,EACF5H,SAAS,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC,EACxC3C,QAAQ,CAAC,MAAM8D,GAAG,CAAC8D,KAAK,EAAE,CAAC,CAC5B,CAAChF,SAAS,EAAE;EACf;EAEAiF,mBAAmBA,CAACC,OAAgB;IAClC,IAAI,CAAC7E,iBAAiB,CAACgB,aAAa,GAAG6D,OAAO;EAChD;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC/G,SAAS,GAAG+G,OAAO;IACxB,IAAI,CAACrF,gBAAgB,EAAE,CAACC,SAAS,EAAE;EACrC;EAEAqF,QAAQA,CAACjC,IAAS,EAAElC,GAAQ;IAC1B,IAAIoE,MAAM,CAACC,OAAO,CAAC,WAAWnC,IAAI,CAACpE,cAAc,IAAI,CAAC,EAAE;MACtD,IAAI,CAAChB,iBAAiB,CAACwH,oCAAoC,CAAC;QAC1DjF,IAAI,EAAE;UACJ,cAAc,EAAE6C,IAAI,CAAC5E;;OAExB,CAAC,CAACoC,IAAI,CACLvD,GAAG,CAACwD,GAAG,IAAG;QACR,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI,CAACjD,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QACpC,CAAC,MAAM;UACL,IAAI,CAACjH,OAAO,CAAC4E,YAAY,CAAC7B,GAAG,CAAC4E,OAAQ,CAAC;QACzC;MACF,CAAC,CAAC,EACFtI,SAAS,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC,EACxC3C,QAAQ,CAAC,MAAM8D,GAAG,CAAC8D,KAAK,EAAE,CAAC,CAC5B,CAAChF,SAAS,EAAE;IACf;EACF;EAEA0F,OAAOA,CAACxE,GAAQ;IACdA,GAAG,CAAC8D,KAAK,EAAE;EACb;CACD;AAjNyBW,UAAA,EAAvB3I,SAAS,CAAC,WAAW,CAAC,C,4DAAwB;AAzFpCU,0BAA0B,GAAAiI,UAAA,EAbtC5I,SAAS,CAAC;EACT6I,QAAQ,EAAE,wBAAwB;EAClCC,WAAW,EAAE,qCAAqC;EAClDC,SAAS,EAAE,CAAC,qCAAqC,CAAC;EAClDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP/I,YAAY,EACZM,YAAY,EACZL,mBAAmB,EACnBM,eAAe;CAElB,CAAC,C,EAEWE,0BAA0B,CA0StC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}