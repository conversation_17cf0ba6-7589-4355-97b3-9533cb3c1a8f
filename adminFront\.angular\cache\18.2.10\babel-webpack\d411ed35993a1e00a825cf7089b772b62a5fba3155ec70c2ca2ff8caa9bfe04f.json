{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nexport var EnumQuotationItemType;\n(function (EnumQuotationItemType) {\n  /** 客變需求 */\n  EnumQuotationItemType[EnumQuotationItemType[\"\\u5BA2\\u8B8A\\u9700\\u6C42\"] = 1] = \"\\u5BA2\\u8B8A\\u9700\\u6C42\";\n  /** 自定義 */\n  EnumQuotationItemType[EnumQuotationItemType[\"\\u81EA\\u5B9A\\u7FA9\"] = 2] = \"\\u81EA\\u5B9A\\u7FA9\";\n  /** 選樣 */\n  EnumQuotationItemType[EnumQuotationItemType[\"\\u9078\\u6A23\"] = 3] = \"\\u9078\\u6A23\";\n})(EnumQuotationItemType || (EnumQuotationItemType = {}));", "map": {"version": 3, "names": ["EnumQuotationItemType"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\models\\enum-quotation-item-type.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nexport enum EnumQuotationItemType {\r\n  /** 客變需求 */\r\n  客變需求 = 1,\r\n  /** 自定義 */\r\n  自定義 = 2,\r\n  /** 選樣 */\r\n  選樣 = 3\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,WAAYA,qBAOX;AAPD,WAAYA,qBAAqB;EAC/B;EACAA,qBAAA,CAAAA,qBAAA,8DAAQ;EACR;EACAA,qBAAA,CAAAA,qBAAA,kDAAO;EACP;EACAA,qBAAA,CAAAA,qBAAA,sCAAM;AACR,CAAC,EAPWA,qBAAqB,KAArBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}