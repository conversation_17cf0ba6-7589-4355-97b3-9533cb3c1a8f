{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet SettingTimePeriodComponent = class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  getHouseChangeDate() {\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n        }\n      }\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n};\nSettingTimePeriodComponent = __decorate([Component({\n  selector: 'ngx-setting-time-period',\n  templateUrl: './setting-time-period.component.html',\n  styleUrls: ['./setting-time-period.component.scss'],\n  standalone: true,\n  providers: [],\n  imports: [CommonModule, SharedModule, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule, SettingTimeStatusPipe]\n})], SettingTimePeriodComponent);\nexport { SettingTimePeriodComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "RadioButtonModule", "SettingTimeStatusPipe", "moment", "SharedModule", "BaseComponent", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "isStatus", "selectedHouseChangeDate", "CChangeStartDate", "CChangeEndDate", "CFloor", "undefined", "CHouseHold", "CHouseId", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "openModel", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "onSubmit", "validation", "errorMessages", "length", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "CBuildCaseName", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "floors", "sort", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "startDate", "endDate", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "houseChangeDates", "convertedHouseArray", "onClose", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "providers", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string;\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule, RadioButtonModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAMvD,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AA+B5D,IAAMC,0BAA0B,GAAhC,MAAMA,0BAA2B,SAAQD,aAAa;EAE3DE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA0C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAC,QAAQ,GAAY,IAAI;IAhCtB,IAAI,CAACC,uBAAuB,GAAG;MAC7BC,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAEC,SAAS;MACjBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAEF;KACX;IAED,IAAI,CAACV,aAAa,CAACa,OAAO,EAAE,CAACC,IAAI,CAC/B/B,GAAG,CAAEgC,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACf,eAAe,GAAGc,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAwBSC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,CAAC;MAC/CK,gBAAgB,EAAEG,SAAS;MAC3BF,cAAc,EAAEE;KACjB;IACD,IAAI,CAACa,gBAAgB,EAAE;EACzB;EAEAC,SAASA,CAACC,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACd,QAAQ,EAAE;MACjB,IAAI,CAACN,uBAAuB,GAAG;QAC7B,GAAGoB,IAAI;QACPnB,gBAAgB,EAAEmB,IAAI,CAACnB,gBAAgB,GAAG,IAAIoB,IAAI,CAACD,IAAI,CAACnB,gBAAgB,CAAC,GAAGG,SAAS;QACrFF,cAAc,EAAEkB,IAAI,CAAClB,cAAc,GAAG,IAAImB,IAAI,CAACD,IAAI,CAAClB,cAAc,CAAC,GAAGE;OACvE;MACD,IAAI,CAAChB,aAAa,CAACkC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO1C,MAAM,CAAC0C,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAACP,GAAQ;IACf,IAAI,CAACQ,UAAU,EAAE;IACjB,IAAI,IAAI,CAACrC,KAAK,CAACsC,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxC,OAAO,CAACyC,aAAa,CAAC,IAAI,CAACxC,KAAK,CAACsC,aAAa,CAAC;MACpD;IACF;IACA,MAAMG,KAAK,GAAG;MACZzB,QAAQ,EAAE,IAAI,CAACN,uBAAuB,CAACM,QAAQ;MAC/CL,gBAAgB,EAAE,IAAI,CAACsB,UAAU,CAAC,IAAI,CAACvB,uBAAuB,CAACC,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACqB,UAAU,CAAC,IAAI,CAACvB,uBAAuB,CAACE,cAAc;KAC5E;IAED,IAAI,CAACX,aAAa,CAACyC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACnB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACyB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC7C,OAAO,CAAC8C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBjB,GAAG,CAACkB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEApB,gBAAgBA,CAAA;IACd,IAAI,CAACzB,iBAAiB,CAAC8C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACzB,IAAI,CAC7E/B,GAAG,CAACgC,GAAG,IAAG;MACR,MAAM8B,OAAO,GAAG9B,GAAG,CAAC+B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACV,MAAM,IAAIpB,GAAG,CAACyB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChDC,cAAc,EAAED,KAAK,CAACC,cAAc;UACpCC,GAAG,EAAEF,KAAK,CAACE;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAClD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAImD,KAAK,GAAG,IAAI,CAACL,oBAAoB,CAACM,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAClD,eAAe,CAAC;UAC1F,IAAI,CAACmB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC0B,oBAAoB,CAACK,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAChC,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC0B,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMQ,WAAW,GAAG,IAAI,CAACnC,WAAW,EAAEC,kBAAkB,EAAE8B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACb,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACxB,SAAS,EAAE;EACf;EAEAsC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAACrD,MAAM;QAC1B,IAAI,CAACiD,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBrD,UAAU,EAAEiD,SAAS,CAACjD,UAAU;UAChCC,QAAQ,EAAEkD,KAAK,CAAClD,QAAQ;UACxBH,MAAM,EAAEqD,KAAK,CAACrD,MAAM;UACpBF,gBAAgB,EAAEuD,KAAK,CAACvD,gBAAgB;UACxCC,cAAc,EAAEsD,KAAK,CAACtD;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACyD,MAAM,CAACC,IAAI,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACJ,MAAM,CAACjB,GAAG,CAAEe,KAAU,IAAI;MAChE,OAAO,IAAI,CAACO,UAAU,CAACtB,GAAG,CAAEY,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACQ,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC7D,UAAU,KAAKiD,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACdnD,UAAU,EAAEiD,SAAS;UACrBhD,QAAQ,EAAE,IAAI;UACdH,MAAM,EAAEsD,KAAK;UACbxD,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO6D,MAAM;EACf;EAEAI,sBAAsBA,CAAChB,GAAU;IAC/B,MAAMiB,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ClB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBgB,aAAa,CAACC,GAAG,CAACjB,SAAS,CAACjD,UAAU,CAAC;MACvCiD,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCY,SAAS,CAACG,GAAG,CAACf,KAAK,CAACrD,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACwD,MAAM,GAAGa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLX,MAAM,EAAEa,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5D,WAAW,CAACb,gBAAgB,IAAI,IAAI,CAACa,WAAW,CAACZ,cAAc,EAAE;MACxE,MAAMyE,SAAS,GAAG,IAAItD,IAAI,CAAC,IAAI,CAACP,WAAW,CAACb,gBAAgB,CAAC;MAC7D,MAAM2E,OAAO,GAAG,IAAIvD,IAAI,CAAC,IAAI,CAACP,WAAW,CAACZ,cAAc,CAAC;MACzD,IAAIyE,SAAS,IAAIC,OAAO,IAAID,SAAS,GAAGC,OAAO,EAAE;QAC/C,IAAI,CAACvF,OAAO,CAACyC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEAM,kBAAkBA,CAAA;IAChB,IAAI,CAACsC,cAAc,EAAE;IACrB,IAAI,CAACnF,aAAa,CAACsF,mCAAmC,CAAC;MACrD5C,IAAI,EAAE;QACJ6C,YAAY,EAAE,IAAI,CAAChE,WAAW,CAACC,kBAAkB,CAAC8B,GAAG;QACrD5C,gBAAgB,EAAE,IAAI,CAACa,WAAW,CAACb,gBAAgB,GAAG,IAAI,CAACsB,UAAU,CAAC,IAAI,CAACT,WAAW,CAACb,gBAAgB,CAAC,GAAGG,SAAS;QACpHF,cAAc,EAAE,IAAI,CAACY,WAAW,CAACZ,cAAc,GAAG,IAAI,CAACqB,UAAU,CAAC,IAAI,CAACT,WAAW,CAACZ,cAAc,CAAC,GAAGE;;KAExG,CAAC,CAACQ,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC+B,OAAO,IAAI/B,GAAG,CAACyB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC6C,gBAAgB,GAAGtE,GAAG,CAAC+B,OAAO,GAAG/B,GAAG,CAAC+B,OAAO,GAAG,EAAE;QACtD,IAAI/B,GAAG,CAAC+B,OAAO,EAAE;UACf,IAAI,CAACuC,gBAAgB,GAAG,CAAC,GAAGtE,GAAG,CAAC+B,OAAO,CAAC;UACxC,IAAI,CAAC2B,sBAAsB,CAAC1D,GAAG,CAAC+B,OAAO,CAAC;UACxC,IAAI,CAACwC,mBAAmB,GAAG,IAAI,CAAC9B,8BAA8B,CAACzC,GAAG,CAAC+B,OAAO,CAAC;QAC7E;MACF;IACF,CAAC,CAAC;EACJ;EAEAyC,OAAOA,CAAC9D,GAAQ;IACdA,GAAG,CAACkB,KAAK,EAAE;EACb;EAEAV,UAAUA,CAAA;IACR,IAAI,CAACrC,KAAK,CAAC4F,KAAK,EAAE;IAClB,IAAI,CAAC5F,KAAK,CAAC6F,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnF,uBAAuB,CAACC,gBAAgB,CAAC;IAC9E,IAAI,CAACX,KAAK,CAAC6F,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACnF,uBAAuB,CAACE,cAAc,CAAC;IAC5E,IAAI,CAACZ,KAAK,CAAC8F,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACpF,uBAAuB,CAACC,gBAAgB,EAAE,IAAI,CAACD,uBAAuB,CAACE,cAAc,CAAC;EACtI;EAEAmF,gBAAgBA,CAAA;IACd,IAAI,CAAC5F,MAAM,CAAC6F,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACxE,WAAW,EAAEC,kBAAkB,EAAE8B,GAAG,EAAE,CAAC,CAAC;EACnG;CACD;AAnOY5D,0BAA0B,GAAAsG,UAAA,EAbtChH,SAAS,CAAC;EACTiH,QAAQ,EAAE,yBAAyB;EACnCC,WAAW,EAAE,sCAAsC;EACnDC,SAAS,EAAE,CAAC,sCAAsC,CAAC;EACnDC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,CACPrH,YAAY,EAAEO,YAAY,EAAEH,iBAAiB,EAC7CF,kBAAkB,EAAEC,mBAAmB,EACvCE,qBAAqB;CAExB,CAAC,C,EAEWI,0BAA0B,CAmOtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}