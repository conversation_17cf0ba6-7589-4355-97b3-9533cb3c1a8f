{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { ProfitBarAnimationChartData } from '../data/profit-bar-animation-chart';\nimport * as i0 from \"@angular/core\";\nexport class ProfitBarAnimationChartService extends ProfitBarAnimationChartData {\n  constructor() {\n    super();\n    this.data = {\n      firstLine: this.getDataForFirstLine(),\n      secondLine: this.getDataForSecondLine()\n    };\n  }\n  getDataForFirstLine() {\n    return this.createEmptyArray(100).map((_, index) => {\n      const oneFifth = index / 5;\n      return (Math.sin(oneFifth) * (oneFifth - 10) + index / 6) * 5;\n    });\n  }\n  getDataForSecondLine() {\n    return this.createEmptyArray(100).map((_, index) => {\n      const oneFifth = index / 5;\n      return (Math.cos(oneFifth) * (oneFifth - 10) + index / 6) * 5;\n    });\n  }\n  createEmptyArray(nPoints) {\n    return Array.from(Array(nPoints));\n  }\n  getChartData() {\n    return observableOf(this.data);\n  }\n  static {\n    this.ɵfac = function ProfitBarAnimationChartService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfitBarAnimationChartService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfitBarAnimationChartService,\n      factory: ProfitBarAnimationChartService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "ProfitBarAnimationChartData", "ProfitBarAnimationChartService", "constructor", "data", "firstLine", "getDataForFirstLine", "secondLine", "getDataForSecondLine", "createEmptyArray", "map", "_", "index", "one<PERSON><PERSON>h", "Math", "sin", "cos", "nPoints", "Array", "from", "getChartData", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\profit-bar-animation-chart.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf,  Observable } from 'rxjs';\r\nimport { ProfitBarAnimationChartData } from '../data/profit-bar-animation-chart';\r\n\r\n@Injectable()\r\nexport class ProfitBarAnimationChartService extends ProfitBarAnimationChartData {\r\n\r\n  private data: any;\r\n\r\n  constructor() {\r\n    super();\r\n    this.data = {\r\n      firstLine: this.getDataForFirstLine(),\r\n      secondLine: this.getDataForSecondLine(),\r\n    };\r\n  }\r\n\r\n  getDataForFirstLine(): number[] {\r\n    return this.createEmptyArray(100)\r\n      .map((_, index) => {\r\n        const oneFifth = index / 5;\r\n\r\n        return (Math.sin(oneFifth) * (oneFifth - 10) + index / 6) * 5;\r\n      });\r\n  }\r\n\r\n  getDataForSecondLine(): number[] {\r\n    return this.createEmptyArray(100)\r\n      .map((_, index) => {\r\n        const oneFifth = index / 5;\r\n\r\n        return (Math.cos(oneFifth) * (oneFifth - 10) + index / 6) * 5;\r\n      });\r\n  }\r\n\r\n  createEmptyArray(nPoints: number) {\r\n    return Array.from(Array(nPoints));\r\n  }\r\n\r\n  getChartData(): Observable<{ firstLine: number[]; secondLine: number[]; }> {\r\n    return observableOf(this.data);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAqB,MAAM;AACtD,SAASC,2BAA2B,QAAQ,oCAAoC;;AAGhF,OAAM,MAAOC,8BAA+B,SAAQD,2BAA2B;EAI7EE,YAAA;IACE,KAAK,EAAE;IACP,IAAI,CAACC,IAAI,GAAG;MACVC,SAAS,EAAE,IAAI,CAACC,mBAAmB,EAAE;MACrCC,UAAU,EAAE,IAAI,CAACC,oBAAoB;KACtC;EACH;EAEAF,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACG,gBAAgB,CAAC,GAAG,CAAC,CAC9BC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAI;MAChB,MAAMC,QAAQ,GAAGD,KAAK,GAAG,CAAC;MAE1B,OAAO,CAACE,IAAI,CAACC,GAAG,CAACF,QAAQ,CAAC,IAAIA,QAAQ,GAAG,EAAE,CAAC,GAAGD,KAAK,GAAG,CAAC,IAAI,CAAC;IAC/D,CAAC,CAAC;EACN;EAEAJ,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACC,gBAAgB,CAAC,GAAG,CAAC,CAC9BC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAI;MAChB,MAAMC,QAAQ,GAAGD,KAAK,GAAG,CAAC;MAE1B,OAAO,CAACE,IAAI,CAACE,GAAG,CAACH,QAAQ,CAAC,IAAIA,QAAQ,GAAG,EAAE,CAAC,GAAGD,KAAK,GAAG,CAAC,IAAI,CAAC;IAC/D,CAAC,CAAC;EACN;EAEAH,gBAAgBA,CAACQ,OAAe;IAC9B,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACD,OAAO,CAAC,CAAC;EACnC;EAEAG,YAAYA,CAAA;IACV,OAAOpB,YAAY,CAAC,IAAI,CAACI,IAAI,CAAC;EAChC;;;uCApCWF,8BAA8B;IAAA;EAAA;;;aAA9BA,8BAA8B;MAAAmB,OAAA,EAA9BnB,8BAA8B,CAAAoB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}