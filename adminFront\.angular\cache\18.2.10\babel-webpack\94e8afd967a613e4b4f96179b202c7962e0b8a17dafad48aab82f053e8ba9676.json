{"ast": null, "code": "export class VisitorsAnalyticsData {}", "map": {"version": 3, "names": ["VisitorsAnalyticsData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\visitors-analytics.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface OutlineData {\r\n  label: string;\r\n  value: number;\r\n}\r\n\r\nexport abstract class VisitorsAnalyticsData {\r\n  abstract getInnerLineChartData(): Observable<number[]>;\r\n  abstract getOutlineLineChartData(): Observable<OutlineData[]>;\r\n  abstract getPieChartData(): Observable<number>;\r\n}\r\n"], "mappings": "AAOA,OAAM,MAAgBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}