{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SystemManagementRoutingModule } from './system-management-routing.module';\nimport { RolePermissionsComponent } from './role-permissions/role-permissions.component';\nimport { UserManagementComponent } from './user-management/user-management.component';\nimport { LogsManagementComponent } from './logs-management/logs-management.component';\nimport { SharedModule } from '../components/shared.module';\nimport { NotificationSettingComponent } from './notification-setting/notification-setting.component';\nimport * as i0 from \"@angular/core\";\nexport let SystemManagementModule = /*#__PURE__*/(() => {\n  class SystemManagementModule {\n    static {\n      this.ɵfac = function SystemManagementModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SystemManagementModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: SystemManagementModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [CommonModule, SharedModule, SystemManagementRoutingModule, RolePermissionsComponent, UserManagementComponent, LogsManagementComponent, NotificationSettingComponent]\n      });\n    }\n  }\n  return SystemManagementModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}