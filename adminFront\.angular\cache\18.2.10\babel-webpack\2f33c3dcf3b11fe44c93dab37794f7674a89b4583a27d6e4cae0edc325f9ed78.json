{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} milliseconds - the milliseconds of the new date\n * @returns {Date} the new date with the milliseconds set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport default function setMilliseconds(dirtyDate, dirtyMilliseconds) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = toInteger(dirtyMilliseconds);\n  date.setMilliseconds(milliseconds);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}