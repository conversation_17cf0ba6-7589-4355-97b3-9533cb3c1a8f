{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BooleanStringPipe {\n  transform(value, ...args) {\n    return value ? '是' : '否';\n  }\n  static {\n    this.ɵfac = function BooleanStringPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BooleanStringPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"booleanString\",\n      type: BooleanStringPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["BooleanStringPipe", "transform", "value", "args", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\BooleanString.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from \"@angular/core\";\r\n\r\n@Pipe({\r\n  name: 'booleanString',\r\n  standalone: true\r\n})\r\nexport class BooleanStringPipe implements PipeTransform {\r\n  transform(value: boolean | undefined, ...args: any[]) {\r\n    return value ? '是' : '否'\r\n  }\r\n}"], "mappings": ";AAMA,OAAM,MAAOA,iBAAiB;EAC5BC,SAASA,CAACC,KAA0B,EAAE,GAAGC,IAAW;IAClD,OAAOD,KAAK,GAAG,GAAG,GAAG,GAAG;EAC1B;;;uCAHWF,iBAAiB;IAAA;EAAA;;;;YAAjBA,iBAAiB;MAAAI,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}