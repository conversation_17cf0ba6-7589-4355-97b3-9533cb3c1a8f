{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport addQuarters from \"../addQuarters/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of quarters to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the quarters subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\nexport default function subQuarters(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addQuarters(dirtyDate, -amount);\n}", "map": {"version": 3, "names": ["toInteger", "addQuarters", "requiredArgs", "subQuarters", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/subQuarters/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport addQuarters from \"../addQuarters/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of quarters to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the quarters subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\nexport default function subQuarters(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addQuarters(dirtyDate, -amount);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC1DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGP,SAAS,CAACK,WAAW,CAAC;EACnC,OAAOJ,WAAW,CAACG,SAAS,EAAE,CAACG,MAAM,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}