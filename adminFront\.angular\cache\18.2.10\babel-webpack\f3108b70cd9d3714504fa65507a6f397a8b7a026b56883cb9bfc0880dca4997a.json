{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nfunction TemplateViewerComponent_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TemplateViewerComponent_tr_18_Template_button_click_6_listener() {\n      const tpl_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectTemplate(tpl_r2));\n    });\n    i0.ɵɵtext(7, \"\\u67E5\\u770B\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tpl_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tpl_r2.description);\n  }\n}\nfunction TemplateViewerComponent_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 10);\n    i0.ɵɵtext(2, \"\\u66AB\\u7121\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TemplateViewerComponent {\n  constructor() {\n    this.templates = [];\n    this.addTemplate = new EventEmitter();\n    this.selectTemplate = new EventEmitter();\n  }\n  onAddTemplate() {\n    this.addTemplate.emit();\n  }\n  onSelectTemplate(template) {\n    this.selectTemplate.emit(template);\n  }\n  static {\n    this.ɵfac = function TemplateViewerComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateViewerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateViewerComponent,\n      selectors: [[\"app-template-viewer\"]],\n      inputs: {\n        templates: \"templates\"\n      },\n      outputs: {\n        addTemplate: \"addTemplate\",\n        selectTemplate: \"selectTemplate\"\n      },\n      decls: 20,\n      vars: 2,\n      consts: [[1, \"template-viewer-modal\"], [1, \"template-viewer-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-3\"], [1, \"mb-0\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"template-list\"], [1, \"table\", \"table-bordered\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [\"colspan\", \"3\", 1, \"text-center\"]],\n      template: function TemplateViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h5\", 2);\n          i0.ɵɵtext(3, \"\\u6A21\\u677F\\u6E05\\u55AE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function TemplateViewerComponent_Template_button_click_4_listener() {\n            return ctx.onAddTemplate();\n          });\n          i0.ɵɵelement(5, \"i\", 4);\n          i0.ɵɵtext(6, \"\\u65B0\\u589E \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"table\", 6)(9, \"thead\")(10, \"tr\")(11, \"th\");\n          i0.ɵɵtext(12, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"th\");\n          i0.ɵɵtext(14, \"\\u63CF\\u8FF0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"th\");\n          i0.ɵɵtext(16, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"tbody\");\n          i0.ɵɵtemplate(18, TemplateViewerComponent_tr_18_Template, 8, 2, \"tr\", 7)(19, TemplateViewerComponent_tr_19_Template, 3, 0, \"tr\", 8);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templates);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.templates || ctx.templates.length === 0);\n        }\n      },\n      styles: [\".template-viewer-modal[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  min-width: 400px;\\n  max-width: 600px;\\n}\\n\\n.template-viewer-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e0e0e0;\\n  padding-bottom: 0.5rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKIiwiZmlsZSI6InRlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi50ZW1wbGF0ZS12aWV3ZXItbW9kYWwge1xyXG4gICAgYmFja2dyb3VuZDogI2ZmZjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIG1pbi13aWR0aDogNDAwcHg7XHJcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtdmlld2VyLWhlYWRlciB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UwZTBlMDtcclxuICAgIHBhZGRpbmctYm90dG9tOiAwLjVyZW07XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgYmFja2dyb3VuZDogI2Y5ZjlmOTtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvdGVtcGxhdGUtdmlld2VyL3RlbXBsYXRlLXZpZXdlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0NBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxtQkFBQTtBQUNKO0FBQ0EsNDJCQUE0MkIiLCJzb3VyY2VzQ29udGVudCI6WyIudGVtcGxhdGUtdmlld2VyLW1vZGFsIHtcclxuICAgIGJhY2tncm91bmQ6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBtaW4td2lkdGg6IDQwMHB4O1xyXG4gICAgbWF4LXdpZHRoOiA2MDBweDtcclxufVxyXG5cclxuLnRlbXBsYXRlLXZpZXdlci1oZWFkZXIge1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XHJcbiAgICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGJhY2tncm91bmQ6ICNmOWY5Zjk7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TemplateViewerComponent_tr_18_Template_button_click_6_listener", "tpl_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onSelectTemplate", "ɵɵadvance", "ɵɵtextInterpolate", "name", "description", "TemplateViewerComponent", "constructor", "templates", "addTemplate", "selectTemplate", "onAddTemplate", "emit", "template", "selectors", "inputs", "outputs", "decls", "vars", "consts", "TemplateViewerComponent_Template", "rf", "ctx", "TemplateViewerComponent_Template_button_click_4_listener", "ɵɵelement", "ɵɵtemplate", "TemplateViewerComponent_tr_18_Template", "TemplateViewerComponent_tr_19_Template", "ɵɵproperty", "length"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\template-viewer\\template-viewer.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-template-viewer',\r\n  templateUrl: './template-viewer.component.html',\r\n  styleUrls: ['./template-viewer.component.scss']\r\n})\r\nexport class TemplateViewerComponent {\r\n  @Input() templates: any[] = [];\r\n  @Output() addTemplate = new EventEmitter<void>();\r\n  @Output() selectTemplate = new EventEmitter<any>();\r\n\r\n  onAddTemplate() {\r\n    this.addTemplate.emit();\r\n  }\r\n\r\n  onSelectTemplate(template: any) {\r\n    this.selectTemplate.emit(template);\r\n  }\r\n}\r\n", "<div class=\"template-viewer-modal\">\r\n  <div class=\"template-viewer-header d-flex justify-content-between align-items-center mb-3\">\r\n    <h5 class=\"mb-0\">模板清單</h5>\r\n    <button class=\"btn btn-success btn-sm\" (click)=\"onAddTemplate()\">\r\n      <i class=\"fas fa-plus mr-1\"></i>新增\r\n    </button>\r\n  </div>\r\n  <div class=\"template-list\">\r\n    <table class=\"table table-bordered table-hover\">\r\n      <thead>\r\n        <tr>\r\n          <th>模板名稱</th>\r\n          <th>描述</th>\r\n          <th>操作</th>\r\n        </tr>\r\n      </thead>\r\n      <tbody>\r\n        <tr *ngFor=\"let tpl of templates\">\r\n          <td>{{ tpl.name }}</td>\r\n          <td>{{ tpl.description }}</td>\r\n          <td>\r\n            <button class=\"btn btn-info btn-sm\" (click)=\"onSelectTemplate(tpl)\">查看</button>\r\n          </td>\r\n        </tr>\r\n        <tr *ngIf=\"!templates || templates.length === 0\">\r\n          <td colspan=\"3\" class=\"text-center\">暫無模板</td>\r\n        </tr>\r\n      </tbody>\r\n    </table>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;;;;;ICkB5DC,EADF,CAAAC,cAAA,SAAkC,SAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EADF,CAAAC,cAAA,SAAI,gBACkE;IAAhCD,EAAA,CAAAI,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAP,MAAA,CAAqB;IAAA,EAAC;IAACN,EAAA,CAAAE,MAAA,mBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAC5E,EACF;;;;IALCH,EAAA,CAAAc,SAAA,GAAc;IAAdd,EAAA,CAAAe,iBAAA,CAAAT,MAAA,CAAAU,IAAA,CAAc;IACdhB,EAAA,CAAAc,SAAA,GAAqB;IAArBd,EAAA,CAAAe,iBAAA,CAAAT,MAAA,CAAAW,WAAA,CAAqB;;;;;IAMzBjB,EADF,CAAAC,cAAA,SAAiD,aACX;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAC1CF,EAD0C,CAAAG,YAAA,EAAK,EAC1C;;;ADnBb,OAAM,MAAOe,uBAAuB;EALpCC,YAAA;IAMW,KAAAC,SAAS,GAAU,EAAE;IACpB,KAAAC,WAAW,GAAG,IAAItB,YAAY,EAAQ;IACtC,KAAAuB,cAAc,GAAG,IAAIvB,YAAY,EAAO;;EAElDwB,aAAaA,CAAA;IACX,IAAI,CAACF,WAAW,CAACG,IAAI,EAAE;EACzB;EAEAX,gBAAgBA,CAACY,QAAa;IAC5B,IAAI,CAACH,cAAc,CAACE,IAAI,CAACC,QAAQ,CAAC;EACpC;;;uCAXWP,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAQ,SAAA;MAAAC,MAAA;QAAAP,SAAA;MAAA;MAAAQ,OAAA;QAAAP,WAAA;QAAAC,cAAA;MAAA;MAAAO,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAN,QAAA,WAAAO,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLhCjC,EAFJ,CAAAC,cAAA,aAAmC,aAC0D,YACxE;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,gBAAiE;UAA1BD,EAAA,CAAAI,UAAA,mBAAA+B,yDAAA;YAAA,OAASD,GAAA,CAAAX,aAAA,EAAe;UAAA,EAAC;UAC9DvB,EAAA,CAAAoC,SAAA,WAAgC;UAAApC,EAAA,CAAAE,MAAA,oBAClC;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UAKEH,EAJR,CAAAC,cAAA,aAA2B,eACuB,YACvC,UACD,UACE;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UAQLD,EAPA,CAAAqC,UAAA,KAAAC,sCAAA,gBAAkC,KAAAC,sCAAA,gBAOe;UAMzDvC,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACF;;;UAbsBH,EAAA,CAAAc,SAAA,IAAY;UAAZd,EAAA,CAAAwC,UAAA,YAAAN,GAAA,CAAAd,SAAA,CAAY;UAO3BpB,EAAA,CAAAc,SAAA,EAA0C;UAA1Cd,EAAA,CAAAwC,UAAA,UAAAN,GAAA,CAAAd,SAAA,IAAAc,GAAA,CAAAd,SAAA,CAAAqB,MAAA,OAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}