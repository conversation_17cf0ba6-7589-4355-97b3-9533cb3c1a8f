{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { StatsProgressBarData } from '../data/stats-progress-bar';\nimport * as i0 from \"@angular/core\";\nexport let StatsProgressBarService = /*#__PURE__*/(() => {\n  class StatsProgressBarService extends StatsProgressBarData {\n    constructor() {\n      super(...arguments);\n      this.progressInfoData = [{\n        title: 'Today’s Profit',\n        value: 572900,\n        activeProgress: 70,\n        description: 'Better than last week (70%)'\n      }, {\n        title: 'New Orders',\n        value: 6378,\n        activeProgress: 30,\n        description: 'Better than last week (30%)'\n      }, {\n        title: 'New Comments',\n        value: 200,\n        activeProgress: 55,\n        description: 'Better than last week (55%)'\n      }];\n    }\n    getProgressInfoData() {\n      return observableOf(this.progressInfoData);\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵStatsProgressBarService_BaseFactory;\n        return function StatsProgressBarService_Factory(__ngFactoryType__) {\n          return (ɵStatsProgressBarService_BaseFactory || (ɵStatsProgressBarService_BaseFactory = i0.ɵɵgetInheritedFactory(StatsProgressBarService)))(__ngFactoryType__ || StatsProgressBarService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: StatsProgressBarService,\n        factory: StatsProgressBarService.ɵfac\n      });\n    }\n  }\n  return StatsProgressBarService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}