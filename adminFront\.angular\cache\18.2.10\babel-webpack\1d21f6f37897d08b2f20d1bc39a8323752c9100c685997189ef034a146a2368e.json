{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';\nimport { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';\nimport { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';\nimport { apiQuotationDeleteDataPost$Json } from '../fn/quotation/api-quotation-delete-data-post-json';\nimport { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class QuotationService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiQuotationGetListPost()` */\n  static {\n    this.ApiQuotationGetListPostPath = '/api/Quotation/GetList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Json$Response(params, context) {\n    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListPost$Json(params, context) {\n    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationGetDataPost()` */\n  static {\n    this.ApiQuotationGetDataPostPath = '/api/Quotation/GetData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Json$Response(params, context) {\n    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetDataPost$Json(params, context) {\n    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationSaveDataPost()` */\n  static {\n    this.ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Json$Response(params, context) {\n    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationSaveDataPost$Json(params, context) {\n    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationDeleteDataPost()` */\n  static {\n    this.ApiQuotationDeleteDataPostPath = '/api/Quotation/DeleteData';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationDeleteDataPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationDeleteDataPost$Json$Response(params, context) {\n    return apiQuotationDeleteDataPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationDeleteDataPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationDeleteDataPost$Json(params, context) {\n    return this.apiQuotationDeleteDataPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */\n  static {\n    this.ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Json$Response(params, context) {\n    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiQuotationGetListByHouseIdPost$Json(params, context) {\n    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiQuotationGetListPost$Json", "apiQuotationGetDataPost$Json", "apiQuotationSaveDataPost$Json", "apiQuotationDeleteDataPost$Json", "apiQuotationGetListByHouseIdPost$Json", "QuotationService", "constructor", "config", "http", "ApiQuotationGetListPostPath", "apiQuotationGetListPost$Json$Response", "params", "context", "rootUrl", "pipe", "r", "body", "ApiQuotationGetDataPostPath", "apiQuotationGetDataPost$Json$Response", "ApiQuotationSaveDataPostPath", "apiQuotationSaveDataPost$Json$Response", "ApiQuotationDeleteDataPostPath", "apiQuotationDeleteDataPost$Json$Response", "ApiQuotationGetListByHouseIdPostPath", "apiQuotationGetListByHouseIdPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\quotation.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiQuotationGetListPost$Json } from '../fn/quotation/api-quotation-get-list-post-json';\r\nimport { ApiQuotationGetListPost$Json$Params } from '../fn/quotation/api-quotation-get-list-post-json';\r\nimport { apiQuotationGetDataPost$Json } from '../fn/quotation/api-quotation-get-data-post-json';\r\nimport { ApiQuotationGetDataPost$Json$Params } from '../fn/quotation/api-quotation-get-data-post-json';\r\nimport { apiQuotationSaveDataPost$Json } from '../fn/quotation/api-quotation-save-data-post-json';\r\nimport { ApiQuotationSaveDataPost$Json$Params } from '../fn/quotation/api-quotation-save-data-post-json';\r\nimport { apiQuotationDeleteDataPost$Json } from '../fn/quotation/api-quotation-delete-data-post-json';\r\nimport { ApiQuotationDeleteDataPost$Json$Params } from '../fn/quotation/api-quotation-delete-data-post-json';\r\nimport { apiQuotationGetListByHouseIdPost$Json } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\r\nimport { ApiQuotationGetListByHouseIdPost$Json$Params } from '../fn/quotation/api-quotation-get-list-by-house-id-post-json';\r\nimport { GetQuotationListResponseBase } from '../models/get-quotation-list-response-base';\r\nimport { GetQuotationResponseBase } from '../models/get-quotation-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class QuotationService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetListPost()` */\r\n  static readonly ApiQuotationGetListPostPath = '/api/Quotation/GetList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Json$Response(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationGetListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListPost$Json(params?: ApiQuotationGetListPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationGetListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetDataPost()` */\r\n  static readonly ApiQuotationGetDataPostPath = '/api/Quotation/GetData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Json$Response(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationResponseBase>> {\r\n    return apiQuotationGetDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetDataPost$Json(params?: ApiQuotationGetDataPost$Json$Params, context?: HttpContext): Observable<GetQuotationResponseBase> {\r\n    return this.apiQuotationGetDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationResponseBase>): GetQuotationResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationSaveDataPost()` */\r\n  static readonly ApiQuotationSaveDataPostPath = '/api/Quotation/SaveData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationSaveDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationSaveDataPost$Json$Response(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationSaveDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationSaveDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */  apiQuotationSaveDataPost$Json(params?: ApiQuotationSaveDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationSaveDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationDeleteDataPost()` */\r\n  static readonly ApiQuotationDeleteDataPostPath = '/api/Quotation/DeleteData';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationDeleteDataPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationDeleteDataPost$Json$Response(params?: ApiQuotationDeleteDataPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiQuotationDeleteDataPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationDeleteDataPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationDeleteDataPost$Json(params?: ApiQuotationDeleteDataPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiQuotationDeleteDataPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiQuotationGetListByHouseIdPost()` */\r\n  static readonly ApiQuotationGetListByHouseIdPostPath = '/api/Quotation/GetListByHouseID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiQuotationGetListByHouseIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Json$Response(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetQuotationListResponseBase>> {\r\n    return apiQuotationGetListByHouseIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiQuotationGetListByHouseIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiQuotationGetListByHouseIdPost$Json(params?: ApiQuotationGetListByHouseIdPost$Json$Params, context?: HttpContext): Observable<GetQuotationListResponseBase> {\r\n    return this.apiQuotationGetListByHouseIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetQuotationListResponseBase>): GetQuotationListResponseBase => r.body)\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,4BAA4B,QAAQ,kDAAkD;AAE/F,SAASC,4BAA4B,QAAQ,kDAAkD;AAE/F,SAASC,6BAA6B,QAAQ,mDAAmD;AAEjG,SAASC,+BAA+B,QAAQ,qDAAqD;AAErG,SAASC,qCAAqC,QAAQ,8DAA8D;;;;AAOpH,OAAM,MAAOC,gBAAiB,SAAQN,WAAW;EAC/CO,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,qCAAqCA,CAACC,MAA4C,EAAEC,OAAqB;IACvG,OAAOZ,4BAA4B,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAZ,4BAA4BA,CAACW,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACF,qCAAqC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrEhB,GAAG,CAAEiB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAC,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,qCAAqCA,CAACP,MAA4C,EAAEC,OAAqB;IACvG,OAAOX,4BAA4B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAX,4BAA4BA,CAACU,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACM,qCAAqC,CAACP,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrEhB,GAAG,CAAEiB,CAA+C,IAA+BA,CAAC,CAACC,IAAI,CAAC,CAC3F;EACH;EAEA;;IACgB,KAAAG,4BAA4B,GAAG,yBAAyB;EAAC;EAEzE;;;;;;EAMAC,sCAAsCA,CAACT,MAA6C,EAAEC,OAAqB;IACzG,OAAOV,6BAA6B,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAKKV,6BAA6BA,CAACS,MAA6C,EAAEC,OAAqB;IACrG,OAAO,IAAI,CAACQ,sCAAsC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtEhB,GAAG,CAAEiB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,wCAAwCA,CAACX,MAA+C,EAAEC,OAAqB;IAC7G,OAAOT,+BAA+B,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAT,+BAA+BA,CAACQ,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACU,wCAAwC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxEhB,GAAG,CAAEiB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAO,oCAAoC,GAAG,iCAAiC;EAAC;EAEzF;;;;;;EAMAC,8CAA8CA,CAACb,MAAqD,EAAEC,OAAqB;IACzH,OAAOR,qCAAqC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAR,qCAAqCA,CAACO,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACY,8CAA8C,CAACb,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9EhB,GAAG,CAAEiB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;;;uCA/HWX,gBAAgB,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBzB,gBAAgB;MAAA0B,OAAA,EAAhB1B,gBAAgB,CAAA2B,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}