{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseIds = []; // 改為：排除的戶別ID（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseIds = []; // 改為：使用 houseId 作為選擇的key\n    this.buildings = [];\n    this.floors = []; // 新增：當前棧別的樓層列表\n    this.filteredHouseholds = []; // 保持為字串陣列用於UI顯示\n    this.selectedByBuilding = {}; // 改為：儲存 houseId\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0]);\n    if (!value || value.length === 0) {\n      this.selectedHouseIds = [];\n    } else {\n      // 檢查傳入的是 houseId 還是 houseName\n      const firstItem = value[0];\n      if (typeof firstItem === 'number') {\n        // 如果是數字，直接使用\n        this.selectedHouseIds = value;\n        console.log('使用傳入的 houseId 陣列');\n      } else if (typeof firstItem === 'string') {\n        // 如果是字串（houseName），這是一個不推薦的使用方式\n        console.error('⚠️ 警告：收到 houseName 陣列而不是 houseId 陣列！');\n        console.error('⚠️ 這會導致同名戶別的選擇問題！');\n        console.error('⚠️ 建議父元件改用 houseId 陣列:', value);\n        // 暫時跳過轉換，保持當前選擇不變\n        console.error('⚠️ 跳過此次 writeValue 以避免錯誤選擇');\n        return;\n      } else {\n        console.error('writeValue 收到未知格式的資料:', value);\n        this.selectedHouseIds = [];\n      }\n    }\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseIds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseIds.forEach(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseId);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(houseId) {\n    console.log('onHouseholdToggle called with houseId:', houseId);\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\n    if (!houseId) {\n      console.log(`無效的 houseId: ${houseId}`);\n      return;\n    }\n    // 防止選擇已排除的戶別\n    if (this.isHouseIdExcluded(houseId)) {\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.isHouseIdSelected(houseId);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\n    } else {\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseIds, houseId];\n    }\n    this.selectedHouseIds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(houseId) {\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的過濾戶別ID\n    const unselectedFilteredIds = [];\n    for (const houseName of this.filteredHouseholds) {\n      const houseId = this.getHouseIdByHouseName(houseName);\n      if (houseId && !this.isHouseIdSelected(houseId) && !this.isHouseIdExcluded(houseId)) {\n        unselectedFilteredIds.push(houseId);\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseIds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount;\n    // 取得尚未選擇且未被排除的棟別戶別 ID\n    const unselectedBuildingIds = [];\n    for (const household of buildingHouseholds) {\n      if (household.houseId && !this.selectedHouseIds.includes(household.houseId) && !this.isHouseholdExcluded(household.houseId)) {\n        unselectedBuildingIds.push(household.houseId);\n      }\n    }\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined);\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseIds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\n    this.onChange([...this.selectedHouseIds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseIds.map(houseId => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    console.log('Selected items to emit:', selectedItems);\n    this.selectionChange.emit(selectedItems);\n    // 回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    console.log('House IDs to emit:', houseIds);\n    this.houseIdChange.emit(houseIds);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(houseId) {\n    if (!houseId) return false;\n    return this.selectedHouseIds.includes(houseId);\n  }\n  isHouseholdExcluded(houseId) {\n    if (!houseId) return false;\n    return this.excludedHouseIds.includes(houseId);\n  }\n  isHouseholdDisabled(houseId) {\n    if (!houseId) return true;\n    return this.isHouseholdExcluded(houseId) || !this.canSelectMore() && !this.isHouseholdSelected(houseId);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled && h.houseId !== undefined);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseIds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\n  getBuildingSelectedHouseIds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棧別的樓層計數\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棧別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n  // 新增：根據 houseId 取得戶別的完整資訊\n  getHouseholdInfoById(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: `ID:${houseId}`,\n      floor: ''\n    };\n  }\n  // 新增：檢查搜尋是否有結果\n  hasNoSearchResults() {\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return false;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length === 0;\n  }\n  // 新增：取得過濾後的戶別數量\n  getFilteredHouseholdsCount() {\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\n      return 0;\n    }\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    });\n    return filtered.length;\n  }\n  // 新增：產生戶別的唯一識別符\n  getHouseholdUniqueId(household) {\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\n  }\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\n  getHouseholdByHouseId(houseId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseId === houseId);\n      if (household) return household;\n    }\n    return null;\n  }\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\n  getHouseIdByHouseName(houseName) {\n    const matchingHouseholds = [];\n    // 收集所有符合名稱的戶別\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const matches = households.filter(h => h.houseName === houseName);\n      matches.forEach(household => {\n        matchingHouseholds.push({\n          building,\n          household\n        });\n      });\n    }\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\n    if (matchingHouseholds.length === 0) {\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\n      return null;\n    }\n    if (matchingHouseholds.length > 1) {\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\n    }\n    const firstMatch = matchingHouseholds[0];\n    return firstMatch.household.houseId || null;\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\n  isHouseIdSelected(houseId) {\n    return this.selectedHouseIds.includes(houseId);\n  }\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\n  isHouseIdExcluded(houseId) {\n    return this.excludedHouseIds.includes(houseId);\n  }\n  // 新增：從唯一識別符獲取戶別物件\n  getHouseholdFromUniqueId(uniqueId) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\n      if (household) return household;\n    }\n    return null;\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseIds\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseIds", "selectionChange", "houseIdChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseIds", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "console", "log", "length", "firstItem", "error", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "houseName", "HouseName", "code", "Building", "floor", "Floor", "houseId", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "onBuildingSelect", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "isHouseIdExcluded", "isHouseIdSelected", "newSelection", "id", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFilteredIds", "getHouseIdByHouseName", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuildingIds", "household", "isHouseholdExcluded", "onUnselectAllBuilding", "buildingHouseIds", "undefined", "onClearAll", "selectedItems", "emit", "houseIds", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseIds", "hasBuildingSelected", "floorSet", "Set", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "householdCode", "getHouseholdInfo", "getHouseholdInfoById", "hasNoSearchResults", "filtered", "getFilteredHouseholdsCount", "getHouseholdUniqueId", "toString", "getHouseholdByHouseId", "matchingHouseholds", "matches", "warn", "m", "firstMatch", "getHouseholdFromUniqueId", "uniqueId", "__decorate", "static", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {}; @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseIds: number[] = []; // 改為：排除的戶別ID（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseIds: number[] = []; // 改為：使用 houseId 作為選擇的key\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棧別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 保持為字串陣列用於UI顯示\r\n  selectedByBuilding: { [building: string]: number[] } = {}; // 改為：儲存 houseId\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: number[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { } writeValue(value: any[]): void {\r\n    console.log('writeValue called with:', value, 'type:', typeof value?.[0]);\r\n\r\n    if (!value || value.length === 0) {\r\n      this.selectedHouseIds = [];\r\n    } else {\r\n      // 檢查傳入的是 houseId 還是 houseName\r\n      const firstItem = value[0];\r\n      if (typeof firstItem === 'number') {\r\n        // 如果是數字，直接使用\r\n        this.selectedHouseIds = value as number[];\r\n        console.log('使用傳入的 houseId 陣列');\r\n      } else if (typeof firstItem === 'string') {\r\n        // 如果是字串（houseName），這是一個不推薦的使用方式\r\n        console.error('⚠️ 警告：收到 houseName 陣列而不是 houseId 陣列！');\r\n        console.error('⚠️ 這會導致同名戶別的選擇問題！');\r\n        console.error('⚠️ 建議父元件改用 houseId 陣列:', value);\r\n\r\n        // 暫時跳過轉換，保持當前選擇不變\r\n        console.error('⚠️ 跳過此次 writeValue 以避免錯誤選擇');\r\n        return;\r\n      } else {\r\n        console.error('writeValue 收到未知格式的資料:', value);\r\n        this.selectedHouseIds = [];\r\n      }\r\n    }\r\n\r\n    console.log('selectedHouseIds set to:', this.selectedHouseIds);\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: number[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    } if (changes['excludedHouseIds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded house IDs updated:', this.excludedHouseIds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: number[] } = {};\r\n\r\n    this.selectedHouseIds.forEach(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseId);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(houseId: number | undefined) {\r\n    console.log('onHouseholdToggle called with houseId:', houseId);\r\n    console.log('Current selectedHouseIds:', this.selectedHouseIds);\r\n\r\n    if (!houseId) {\r\n      console.log(`無效的 houseId: ${houseId}`);\r\n      return;\r\n    }\r\n\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseIdExcluded(houseId)) {\r\n      console.log(`戶別 ID ${houseId} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.isHouseIdSelected(houseId);\r\n    let newSelection: number[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseIds.filter(id => id !== houseId);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseIds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseIds, houseId];\r\n    }\r\n\r\n    this.selectedHouseIds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onRemoveHousehold(houseId: number) {\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => id !== houseId);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的過濾戶別ID\r\n    const unselectedFilteredIds: number[] = [];\r\n    for (const houseName of this.filteredHouseholds) {\r\n      const houseId = this.getHouseIdByHouseName(houseName);\r\n      if (houseId && !this.isHouseIdSelected(houseId) && !this.isHouseIdExcluded(houseId)) {\r\n        unselectedFilteredIds.push(houseId);\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFilteredIds.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  } onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseIds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;\r\n\r\n    // 取得尚未選擇且未被排除的棟別戶別 ID\r\n    const unselectedBuildingIds: number[] = [];\r\n    for (const household of buildingHouseholds) {\r\n      if (household.houseId &&\r\n        !this.selectedHouseIds.includes(household.houseId) &&\r\n        !this.isHouseholdExcluded(household.houseId)) {\r\n        unselectedBuildingIds.push(household.houseId);\r\n      }\r\n    }\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuildingIds.slice(0, remainingSlots); if (toAdd.length > 0) {\r\n      this.selectedHouseIds = [...this.selectedHouseIds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    const buildingHouseIds = buildingHouseholds.map(h => h.houseId).filter(id => id !== undefined) as number[];\r\n    this.selectedHouseIds = this.selectedHouseIds.filter(id => !buildingHouseIds.includes(id));\r\n    this.emitChanges();\r\n  }\r\n\r\n  onClearAll() {\r\n    this.selectedHouseIds = [];\r\n    this.emitChanges();\r\n  } private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    console.log('Emitting changes - selectedHouseIds:', this.selectedHouseIds);\r\n    this.onChange([...this.selectedHouseIds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseIds.map(houseId => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseId === houseId);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    console.log('Selected items to emit:', selectedItems);\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    console.log('House IDs to emit:', houseIds);\r\n    this.houseIdChange.emit(houseIds);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n  isHouseholdSelected(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdExcluded(houseId: number | undefined): boolean {\r\n    if (!houseId) return false;\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  isHouseholdDisabled(houseId: number | undefined): boolean {\r\n    if (!houseId) return true;\r\n    return this.isHouseholdExcluded(houseId) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(houseId));\r\n  }\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseIds.length < this.maxSelections;\r\n  } isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled && h.houseId !== undefined);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  }\r\n\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding] || [];\r\n    return buildingHouseholds.some(household => household.houseId && this.selectedHouseIds.includes(household.houseId));\r\n  } getSelectedByBuilding(): { [building: string]: number[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseIds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別 ID\r\n  getBuildingSelectedHouseIds(building: string): number[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棧別的樓層計數\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棧別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n\r\n  // 新增：根據 houseId 取得戶別的完整資訊\r\n  getHouseholdInfoById(houseId: number): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: `ID:${houseId}`, floor: '' };\r\n  }\r\n\r\n  // 新增：檢查搜尋是否有結果\r\n  hasNoSearchResults(): boolean {\r\n    if (!this.searchTerm || !this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return false;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length === 0;\r\n  }\r\n\r\n  // 新增：取得過濾後的戶別數量\r\n  getFilteredHouseholdsCount(): number {\r\n    if (!this.selectedBuilding || !this.buildingData[this.selectedBuilding]) {\r\n      return 0;\r\n    }\r\n\r\n    const filtered = this.buildingData[this.selectedBuilding].filter(h => {\r\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n      const searchMatch = !this.searchTerm || h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n      return floorMatch && searchMatch;\r\n    });\r\n\r\n    return filtered.length;\r\n  }\r\n\r\n  // 新增：產生戶別的唯一識別符\r\n  getHouseholdUniqueId(household: HouseholdItem): string {\r\n    return household.houseId ? household.houseId.toString() : `${household.houseName}_${household.floor}`;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseId 查找 HouseholdItem\r\n  private getHouseholdByHouseId(houseId: number): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseId === houseId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n  // 新增：輔助方法 - 根據 houseName 查找 houseId\r\n  private getHouseIdByHouseName(houseName: string): number | null {\r\n    const matchingHouseholds: { building: string, household: HouseholdItem }[] = [];\r\n\r\n    // 收集所有符合名稱的戶別\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const matches = households.filter(h => h.houseName === houseName);\r\n      matches.forEach(household => {\r\n        matchingHouseholds.push({ building, household });\r\n      });\r\n    }\r\n\r\n    console.log(`查找 houseName \"${houseName}\" 的結果:`, matchingHouseholds);\r\n\r\n    if (matchingHouseholds.length === 0) {\r\n      console.warn(`找不到 houseName \"${houseName}\" 對應的戶別`);\r\n      return null;\r\n    }\r\n\r\n    if (matchingHouseholds.length > 1) {\r\n      console.warn(`發現多個同名戶別 \"${houseName}\":`, matchingHouseholds.map(m => `${m.building}-${m.household.floor}`));\r\n      console.warn(`將返回第一個找到的: ${matchingHouseholds[0].building}-${matchingHouseholds[0].household.floor}`);\r\n    }\r\n\r\n    const firstMatch = matchingHouseholds[0];\r\n    return firstMatch.household.houseId || null;\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被選中\r\n  private isHouseIdSelected(houseId: number): boolean {\r\n    return this.selectedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：輔助方法 - 檢查 houseId 是否被排除\r\n  private isHouseIdExcluded(houseId: number): boolean {\r\n    return this.excludedHouseIds.includes(houseId);\r\n  }\r\n\r\n  // 新增：從唯一識別符獲取戶別物件\r\n  getHouseholdFromUniqueId(uniqueId: string): HouseholdItem | null {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => this.getHouseholdUniqueId(h) === uniqueId);\r\n      if (household) return household;\r\n    }\r\n    return null;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA8BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA3Bd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAAW,KAAAC,gBAAgB,GAAY,IAAI;IAC1E,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IAEhC,KAAAC,eAAe,GAAG,IAAIlB,YAAY,EAAmB;IACrD,KAAAmB,aAAa,GAAG,IAAInB,YAAY,EAAY,CAAC,CAAC;IACxD,KAAAoB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE,CAAC,CAAC;IAC3D,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAC5B;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAAEC,UAAUA,CAACF,KAAY;IAC3BG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,KAAK,EAAE,OAAO,EAAE,OAAOA,KAAK,GAAG,CAAC,CAAC,CAAC;IAEzE,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI,CAACZ,gBAAgB,GAAG,EAAE;IAC5B,CAAC,MAAM;MACL;MACA,MAAMa,SAAS,GAAGN,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAI,OAAOM,SAAS,KAAK,QAAQ,EAAE;QACjC;QACA,IAAI,CAACb,gBAAgB,GAAGO,KAAiB;QACzCG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MACjC,CAAC,MAAM,IAAI,OAAOE,SAAS,KAAK,QAAQ,EAAE;QACxC;QACAH,OAAO,CAACI,KAAK,CAAC,sCAAsC,CAAC;QACrDJ,OAAO,CAACI,KAAK,CAAC,mBAAmB,CAAC;QAClCJ,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEP,KAAK,CAAC;QAE9C;QACAG,OAAO,CAACI,KAAK,CAAC,4BAA4B,CAAC;QAC3C;MACF,CAAC,MAAM;QACLJ,OAAO,CAACI,KAAK,CAAC,uBAAuB,EAAEP,KAAK,CAAC;QAC7C,IAAI,CAACP,gBAAgB,GAAG,EAAE;MAC5B;IACF;IAEAU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAC9D,IAAI,CAACe,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACX,QAAQ,GAAGW,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACT,SAAS,GAAGS,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACjC,QAAQ,GAAGiC,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACpC,WAAW,EAAE;MAC9C;MACA,IAAI,CAACkC,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAACvB,SAAS,GAAGwB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,YAAY,IAAI,EAAE,CAAC;MACrDqB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACtB,YAAY,CAAC;MACvD,IAAI,CAACsC,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IAAE,IAAIS,OAAO,CAAC,kBAAkB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BjB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAClB,gBAAgB,CAAC;IACnE;EACF;EAAU6B,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAACjC,YAAY,IAAIoC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,YAAY,CAAC,CAACuB,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACX,SAAS,GAAGwB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,YAAY,CAAC;MAC/CqB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACV,SAAS,CAAC;MAChF,IAAI,CAACc,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAAC3B,WAAW,EAAE;MAC3B;MACA,IAAI,CAACwC,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC3B,SAAS,GAAG,EAAE;MACnBS,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQiB,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACxC,WAAW,EAAE;IAEvB,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACtB,kBAAkB,CAAC8C,WAAW,CAAC,IAAI,CAACzC,WAAW,CAAC,CAAC0C,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBtB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,QAAQ,CAAC;QACtC,IAAI,CAAC3C,YAAY,GAAG,IAAI,CAAC4C,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAACjC,SAAS,GAAGwB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrC,YAAY,CAAC;QAC/C,IAAI,CAAC0B,wBAAwB,EAAE;QAC/B,IAAI,CAACV,SAAS,GAAG,KAAK;QACtB,IAAI,CAACvB,GAAG,CAACqD,aAAa,EAAE;MAC1B,CAAC;MAAErB,KAAK,EAAGA,KAAK,IAAI;QAClBJ,OAAO,CAACI,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAACzB,YAAY,GAAG,EAAE;QACtB,IAAI,CAACY,SAAS,GAAG,EAAE;QACnB,IAAI,CAACI,SAAS,GAAG,KAAK;QACtB,IAAI,CAACvB,GAAG,CAACqD,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACG,OAAiC;IACxE,MAAM/C,YAAY,GAAiB,EAAE;IAErCoC,MAAM,CAACW,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDlD,YAAY,CAACiD,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,SAAS,EAAED,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,IAAI;QAC3DN,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACI,QAAQ,IAAIP,QAAQ;QACtDQ,KAAK,EAAEL,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACM,KAAK;QACjCC,OAAO,EAAEP,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjB9B,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO/B,YAAY;EACrB;EAAU0B,wBAAwBA,CAAA;IAChC,MAAMoC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACnD,gBAAgB,CAACqC,OAAO,CAACW,OAAO,IAAG;MACtC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;QACrC,MAAMmD,IAAI,GAAG,IAAI,CAAC/D,YAAY,CAACiD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACP,OAAO,CAAC;UAC/B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAAC5C,kBAAkB,GAAG+C,OAAO;EACnC;EAAEK,gBAAgBA,CAAClB,QAAgB;IACjC5B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2B,QAAQ,CAAC;IAC3C,IAAI,CAACzC,gBAAgB,GAAGyC,QAAQ;IAChC,IAAI,CAACvC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC2D,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC9B,wBAAwB,EAAE;IAC/BjB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;IACzE;IACA,IAAI,CAAC9B,GAAG,CAACqD,aAAa,EAAE;EAC1B;EAEAuB,eAAeA,CAACpB,QAAgB;IAC9B5B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2B,QAAQ,CAAC;EACxD;EAAEX,wBAAwBA,CAAA;IACxBjB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACd,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACM,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMwD,UAAU,GAAG,IAAI,CAACtE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjEa,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgD,UAAU,CAAC/C,MAAM,CAAC;IAEpE;IACA,IAAI,CAACT,kBAAkB,GAAGwD,UAAU,CACjCC,MAAM,CAACN,CAAC,IAAG;MACV;MACA,MAAMO,UAAU,GAAG,CAAC,IAAI,CAAC9D,aAAa,IAAIuD,CAAC,CAACR,KAAK,KAAK,IAAI,CAAC/C,aAAa;MACxE;MACA,MAAM+D,WAAW,GAAGR,CAAC,CAACZ,SAAS,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClE,UAAU,CAACiE,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACDtB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;IAExBhC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACR,kBAAkB,CAACS,MAAM,CAAC;EAC5E;EAEAqD,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACpE,UAAU,GAAGoE,KAAK,CAACC,MAAM,CAAC5D,KAAK;IACpC,IAAI,CAACoB,wBAAwB,EAAE;IAC/BjB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACb,UAAU,CAAC;EACtD;EACAsE,WAAWA,CAAA;IACT,IAAI,CAACtE,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC6B,wBAAwB,EAAE;IAC/BjB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACA0D,iBAAiBA,CAACrB,OAA2B;IAC3CtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqC,OAAO,CAAC;IAC9DtC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAE/D,IAAI,CAACgD,OAAO,EAAE;MACZtC,OAAO,CAACC,GAAG,CAAC,gBAAgBqC,OAAO,EAAE,CAAC;MACtC;IACF;IAEA;IACA,IAAI,IAAI,CAACsB,iBAAiB,CAACtB,OAAO,CAAC,EAAE;MACnCtC,OAAO,CAACC,GAAG,CAAC,SAASqC,OAAO,kBAAkB,CAAC;MAC/C;IACF;IAEA,MAAME,UAAU,GAAG,IAAI,CAACqB,iBAAiB,CAACvB,OAAO,CAAC;IAClD,IAAIwB,YAAsB;IAE1B,IAAItB,UAAU,EAAE;MACdsB,YAAY,GAAG,IAAI,CAACxE,gBAAgB,CAAC4D,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKzB,OAAO,CAAC;IACnE,CAAC,MAAM;MACL,IAAI,IAAI,CAAC9D,aAAa,IAAI,IAAI,CAACc,gBAAgB,CAACY,MAAM,IAAI,IAAI,CAAC1B,aAAa,EAAE;QAC5EwB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACA6D,YAAY,GAAG,CAAC,GAAG,IAAI,CAACxE,gBAAgB,EAAEgD,OAAO,CAAC;IACpD;IAEA,IAAI,CAAChD,gBAAgB,GAAGwE,YAAY;IACpC,IAAI,CAACE,WAAW,EAAE;EACpB;EACAC,iBAAiBA,CAAC3B,OAAe;IAC/B,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4D,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKzB,OAAO,CAAC;IAC1E,IAAI,CAAC0B,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,IAAI,IAAI,CAACM,kBAAkB,CAACS,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAMiE,YAAY,GAAG,IAAI,CAAC7E,gBAAgB,CAACY,MAAM;IACjD,MAAMkE,UAAU,GAAG,IAAI,CAAC5F,aAAa,IAAI6F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMI,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMvC,SAAS,IAAI,IAAI,CAACvC,kBAAkB,EAAE;MAC/C,MAAM6C,OAAO,GAAG,IAAI,CAACkC,qBAAqB,CAACxC,SAAS,CAAC;MACrD,IAAIM,OAAO,IAAI,CAAC,IAAI,CAACuB,iBAAiB,CAACvB,OAAO,CAAC,IAAI,CAAC,IAAI,CAACsB,iBAAiB,CAACtB,OAAO,CAAC,EAAE;QACnFiC,qBAAqB,CAAC1B,IAAI,CAACP,OAAO,CAAC;MACrC;IACF;IAEA;IACA,MAAMmC,KAAK,GAAGF,qBAAqB,CAACG,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAE5D,IAAIG,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACZ,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGmF,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClBhE,OAAO,CAACC,GAAG,CAAC,aAAawE,KAAK,CAACvE,MAAM,MAAM,CAAC;IAC9C;EACF;EAAEyE,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACxF,gBAAgB,EAAE;IAE5B;IACA,MAAMyF,kBAAkB,GAAG,IAAI,CAACjG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IAEzE;IACA,MAAMgF,YAAY,GAAG,IAAI,CAAC7E,gBAAgB,CAACY,MAAM;IACjD,MAAMkE,UAAU,GAAG,IAAI,CAAC5F,aAAa,IAAI6F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY;IAEhD;IACA,MAAMU,qBAAqB,GAAa,EAAE;IAC1C,KAAK,MAAMC,SAAS,IAAIF,kBAAkB,EAAE;MAC1C,IAAIE,SAAS,CAACxC,OAAO,IACnB,CAAC,IAAI,CAAChD,gBAAgB,CAACgE,QAAQ,CAACwB,SAAS,CAACxC,OAAO,CAAC,IAClD,CAAC,IAAI,CAACyC,mBAAmB,CAACD,SAAS,CAACxC,OAAO,CAAC,EAAE;QAC9CuC,qBAAqB,CAAChC,IAAI,CAACiC,SAAS,CAACxC,OAAO,CAAC;MAC/C;IACF;IAEA;IACA,MAAMmC,KAAK,GAAGI,qBAAqB,CAACH,KAAK,CAAC,CAAC,EAAEJ,cAAc,CAAC;IAAE,IAAIG,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;MAClF,IAAI,CAACZ,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACA,gBAAgB,EAAE,GAAGmF,KAAK,CAAC;MAC5D,IAAI,CAACT,WAAW,EAAE;MAClBhE,OAAO,CAACC,GAAG,CAAC,aAAawE,KAAK,CAACvE,MAAM,MAAM,CAAC;IAC9C;EACF;EAEA8E,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC7F,gBAAgB,EAAE;IAE5B,MAAMyF,kBAAkB,GAAG,IAAI,CAACjG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACzE,MAAM8F,gBAAgB,GAAGL,kBAAkB,CAAC9C,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACN,OAAO,CAAC,CAACY,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAa;IAC1G,IAAI,CAAC5F,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC4D,MAAM,CAACa,EAAE,IAAI,CAACkB,gBAAgB,CAAC3B,QAAQ,CAACS,EAAE,CAAC,CAAC;IAC1F,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAmB,UAAUA,CAAA;IACR,IAAI,CAAC7F,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAC0E,WAAW,EAAE;EACpB;EAAUA,WAAWA,CAAA;IACnB,IAAI,CAAC3D,wBAAwB,EAAE;IAC/BL,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACX,gBAAgB,CAAC;IAC1E,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACN,gBAAgB,CAAC,CAAC;IACzC,IAAI,CAACQ,SAAS,EAAE;IAEhB,MAAMsF,aAAa,GAAG,IAAI,CAAC9F,gBAAgB,CAACwC,GAAG,CAACQ,OAAO,IAAG;MACxD,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;QACrC,MAAMmD,IAAI,GAAG,IAAI,CAAC/D,YAAY,CAACiD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;QAC1E,IAAII,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACQ,MAAM,CAACR,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD1C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmF,aAAa,CAAC;IACrD,IAAI,CAACpG,eAAe,CAACqG,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAACtD,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACJ,OAAQ,CAAC,CAACY,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAC;IACxFlF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEqF,QAAQ,CAAC;IAC3C,IAAI,CAACrG,aAAa,CAACoG,IAAI,CAACC,QAAQ,CAAC;EACnC;EAAEC,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC9G,QAAQ,EAAE;MAClB,IAAI,CAAC+G,UAAU,EAAE;MACjBxF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACV,SAAS,CAAC;IACrD;EACF;EAEAiG,UAAUA,CAAA;IACR,IAAI,CAAClH,aAAa,CAACmH,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAEFC,mBAAmBA,CAAC1D,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAAChD,gBAAgB,CAACgE,QAAQ,CAAChB,OAAO,CAAC;EAChD;EAEAyC,mBAAmBA,CAACzC,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAO,IAAI,CAACvD,gBAAgB,CAACuE,QAAQ,CAAChB,OAAO,CAAC;EAChD;EAEA2D,mBAAmBA,CAAC3D,OAA2B;IAC7C,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,OAAO,IAAI,CAACyC,mBAAmB,CAACzC,OAAO,CAAC,IACrC,CAAC,IAAI,CAAC4D,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAAC1D,OAAO,CAAE;EACjE;EACA4D,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAC1H,aAAa,IAAI,IAAI,CAACc,gBAAgB,CAACY,MAAM,GAAG,IAAI,CAAC1B,aAAa;EACjF;EAAE2H,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAChH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMyF,kBAAkB,GAAG,IAAI,CAACjG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAChE+D,MAAM,CAACN,CAAC,IAAI,CAACA,CAAC,CAAClC,UAAU,IAAIkC,CAAC,CAACN,OAAO,KAAK4C,SAAS,CAAC;IACxD,OAAON,kBAAkB,CAAC1E,MAAM,GAAG,CAAC,IAClC0E,kBAAkB,CAACwB,KAAK,CAACtB,SAAS,IAAIA,SAAS,CAACxC,OAAO,IAAI,IAAI,CAAChD,gBAAgB,CAACgE,QAAQ,CAACwB,SAAS,CAACxC,OAAO,CAAC,CAAC;EACjH;EAEA+D,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAClH,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMyF,kBAAkB,GAAG,IAAI,CAACjG,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACzE,OAAOyF,kBAAkB,CAAC0B,IAAI,CAACxB,SAAS,IAAIA,SAAS,CAACxC,OAAO,IAAI,IAAI,CAAChD,gBAAgB,CAACgE,QAAQ,CAACwB,SAAS,CAACxC,OAAO,CAAC,CAAC;EACrH;EAAEiE,qBAAqBA,CAAA;IACrB,OAAO,IAAI,CAAC7G,kBAAkB;EAChC;EAEA8G,gBAAgBA,CAAC5E,QAAgB;IAC/B,OAAO,IAAI,CAACjD,YAAY,CAACiD,QAAQ,CAAC,EAAE1B,MAAM,IAAI,CAAC;EACjD;EAEAuG,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACnH,gBAAgB,CAACY,MAAM;EACrC;EAEA;EACAwG,2BAA2BA,CAAC9E,QAAgB;IAC1C,OAAO,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA+E,mBAAmBA,CAAC/E,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,IAAI,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,CAAC1B,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ6C,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC5D,gBAAgB,EAAE;MAC1B,IAAI,CAACK,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMyD,UAAU,GAAG,IAAI,CAACtE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMyH,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElC5D,UAAU,CAACtB,OAAO,CAACmD,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAAC1C,KAAK,EAAE;QACnBwE,QAAQ,CAACE,GAAG,CAAChC,SAAS,CAAC1C,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC5C,MAAM,GAAGuH,KAAK,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFvH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACd,gBAAgB,EAAE,IAAI,CAACK,MAAM,CAAC;EACjF;EAEA;EACAgI,aAAaA,CAACpF,KAAa;IACzBpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmC,KAAK,CAAC;IACrC,IAAI,CAAC/C,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK+C,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACnB,wBAAwB,EAAE;IAC/B,IAAI,CAAC7C,GAAG,CAACqD,aAAa,EAAE;EAC1B;EAEA;EACAgG,aAAaA,CAACrF,KAAa;IACzB,IAAI,CAAC,IAAI,CAACjD,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAM8D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAO8D,UAAU,CAACC,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACR,KAAK,KAAKA,KAAK,CAAC,CAAClC,MAAM;EACzD;EACA;EACAwH,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACxI,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAM8D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAM2F,SAAS,GAAG7B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK2F,aAAa,CAAC;IACrE,OAAO7C,SAAS,EAAE1C,KAAK,IAAI,EAAE;EAC/B;EACA;EACAwF,gBAAgBA,CAACD,aAAqB;IACpC,KAAK,MAAM/F,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;MACrC,MAAM0D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAACiD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkD,SAAS,GAAG7B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK2F,aAAa,CAAC;MACrE,IAAI7C,SAAS,EAAE;QACb,OAAO;UACL9C,SAAS,EAAE8C,SAAS,CAAC9C,SAAS;UAC9BI,KAAK,EAAE0C,SAAS,CAAC1C,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE2F,aAAa;MAAEvF,KAAK,EAAE;IAAE,CAAE;EAChD;EAEA;EACAyF,oBAAoBA,CAACvF,OAAe;IAClC,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;MACrC,MAAM0D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAACiD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkD,SAAS,GAAG7B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIwC,SAAS,EAAE;QACb,OAAO;UACL9C,SAAS,EAAE8C,SAAS,CAAC9C,SAAS;UAC9BI,KAAK,EAAE0C,SAAS,CAAC1C,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE,MAAMM,OAAO,EAAE;MAAEF,KAAK,EAAE;IAAE,CAAE;EAClD;EAEA;EACA0F,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC1I,UAAU,IAAI,CAAC,IAAI,CAACD,gBAAgB,IAAI,CAAC,IAAI,CAACR,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;IAEA,MAAM4I,QAAQ,GAAG,IAAI,CAACpJ,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAAC+D,MAAM,CAACN,CAAC,IAAG;MACnE,MAAMO,UAAU,GAAG,CAAC,IAAI,CAAC9D,aAAa,IAAIuD,CAAC,CAACR,KAAK,KAAK,IAAI,CAAC/C,aAAa;MACxE,MAAM+D,WAAW,GAAGR,CAAC,CAACZ,SAAS,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClE,UAAU,CAACiE,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO2E,QAAQ,CAAC7H,MAAM,KAAK,CAAC;EAC9B;EAEA;EACA8H,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC7I,gBAAgB,IAAI,CAAC,IAAI,CAACR,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE;MACvE,OAAO,CAAC;IACV;IAEA,MAAM4I,QAAQ,GAAG,IAAI,CAACpJ,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAAC+D,MAAM,CAACN,CAAC,IAAG;MACnE,MAAMO,UAAU,GAAG,CAAC,IAAI,CAAC9D,aAAa,IAAIuD,CAAC,CAACR,KAAK,KAAK,IAAI,CAAC/C,aAAa;MACxE,MAAM+D,WAAW,GAAG,CAAC,IAAI,CAAChE,UAAU,IAAIwD,CAAC,CAACZ,SAAS,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAClE,UAAU,CAACiE,WAAW,EAAE,CAAC;MACzG,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC;IAEF,OAAO2E,QAAQ,CAAC7H,MAAM;EACxB;EAEA;EACA+H,oBAAoBA,CAACnD,SAAwB;IAC3C,OAAOA,SAAS,CAACxC,OAAO,GAAGwC,SAAS,CAACxC,OAAO,CAAC4F,QAAQ,EAAE,GAAG,GAAGpD,SAAS,CAAC9C,SAAS,IAAI8C,SAAS,CAAC1C,KAAK,EAAE;EACvG;EACA;EACQ+F,qBAAqBA,CAAC7F,OAAe;IAC3C,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;MACrC,MAAM0D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAACiD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkD,SAAS,GAAG7B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,KAAKA,OAAO,CAAC;MAC7D,IAAIwC,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;EACA;EACQN,qBAAqBA,CAACxC,SAAiB;IAC7C,MAAMoG,kBAAkB,GAAqD,EAAE;IAE/E;IACA,KAAK,MAAMxG,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;MACrC,MAAM0D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAACiD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMyG,OAAO,GAAGpF,UAAU,CAACC,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;MACjEqG,OAAO,CAAC1G,OAAO,CAACmD,SAAS,IAAG;QAC1BsD,kBAAkB,CAACvF,IAAI,CAAC;UAAEjB,QAAQ;UAAEkD;QAAS,CAAE,CAAC;MAClD,CAAC,CAAC;IACJ;IAEA9E,OAAO,CAACC,GAAG,CAAC,iBAAiB+B,SAAS,QAAQ,EAAEoG,kBAAkB,CAAC;IAEnE,IAAIA,kBAAkB,CAAClI,MAAM,KAAK,CAAC,EAAE;MACnCF,OAAO,CAACsI,IAAI,CAAC,kBAAkBtG,SAAS,SAAS,CAAC;MAClD,OAAO,IAAI;IACb;IAEA,IAAIoG,kBAAkB,CAAClI,MAAM,GAAG,CAAC,EAAE;MACjCF,OAAO,CAACsI,IAAI,CAAC,aAAatG,SAAS,IAAI,EAAEoG,kBAAkB,CAACtG,GAAG,CAACyG,CAAC,IAAI,GAAGA,CAAC,CAAC3G,QAAQ,IAAI2G,CAAC,CAACzD,SAAS,CAAC1C,KAAK,EAAE,CAAC,CAAC;MAC3GpC,OAAO,CAACsI,IAAI,CAAC,cAAcF,kBAAkB,CAAC,CAAC,CAAC,CAACxG,QAAQ,IAAIwG,kBAAkB,CAAC,CAAC,CAAC,CAACtD,SAAS,CAAC1C,KAAK,EAAE,CAAC;IACvG;IAEA,MAAMoG,UAAU,GAAGJ,kBAAkB,CAAC,CAAC,CAAC;IACxC,OAAOI,UAAU,CAAC1D,SAAS,CAACxC,OAAO,IAAI,IAAI;EAC7C;EAEA;EACQuB,iBAAiBA,CAACvB,OAAe;IACvC,OAAO,IAAI,CAAChD,gBAAgB,CAACgE,QAAQ,CAAChB,OAAO,CAAC;EAChD;EAEA;EACQsB,iBAAiBA,CAACtB,OAAe;IACvC,OAAO,IAAI,CAACvD,gBAAgB,CAACuE,QAAQ,CAAChB,OAAO,CAAC;EAChD;EAEA;EACAmG,wBAAwBA,CAACC,QAAgB;IACvC,KAAK,MAAM9G,QAAQ,IAAI,IAAI,CAACrC,SAAS,EAAE;MACrC,MAAM0D,UAAU,GAAG,IAAI,CAACtE,YAAY,CAACiD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAMkD,SAAS,GAAG7B,UAAU,CAACN,IAAI,CAACC,CAAC,IAAI,IAAI,CAACqF,oBAAoB,CAACrF,CAAC,CAAC,KAAK8F,QAAQ,CAAC;MACjF,IAAI5D,SAAS,EAAE,OAAOA,SAAS;IACjC;IACA,OAAO,IAAI;EACb;CACD;AAvjBkD6D,UAAA,EAAhD3K,SAAS,CAAC,iBAAiB,EAAE;EAAE4K,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAE3ED,UAAA,EAAR/K,KAAK,EAAE,C,6DAA+B;AAC9B+K,UAAA,EAAR/K,KAAK,EAAE,C,+DAAqC;AACpC+K,UAAA,EAAR/K,KAAK,EAAE,C,0DAA2B;AAC1B+K,UAAA,EAAR/K,KAAK,EAAE,C,6DAAmC;AAClC+K,UAAA,EAAR/K,KAAK,EAAE,C,8DAAiC;AAAU+K,UAAA,EAAR/K,KAAK,EAAE,C,kEAAkC;AAC3E+K,UAAA,EAAR/K,KAAK,EAAE,C,6DAA6B;AAC5B+K,UAAA,EAAR/K,KAAK,EAAE,C,kEAAkC;AACjC+K,UAAA,EAAR/K,KAAK,EAAE,C,kEAAiC;AAE/B+K,UAAA,EAAT9K,MAAM,EAAE,C,iEAAuD;AACtD8K,UAAA,EAAT9K,MAAM,EAAE,C,+DAA8C;AAb5CK,yBAAyB,GAAAyK,UAAA,EAZrChL,SAAS,CAAC;EACTkL,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAEhL,iBAAiB;IAC1BiL,WAAW,EAAEnL,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxDiL,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWjL,yBAAyB,CAwjBrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}