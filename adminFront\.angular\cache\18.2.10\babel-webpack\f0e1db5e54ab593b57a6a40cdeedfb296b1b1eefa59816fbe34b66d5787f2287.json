{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/pipes/base-file.pipe\";\nfunction PictureMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction PictureMaterialComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(40);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5));\n    });\n    i0.ɵɵtext(1, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_36_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_36_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(40);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_36_Template_a_click_4_listener() {\n      const item_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openPdfInNewTab(item_r7 == null ? null : item_r7.CFile));\n    });\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 23);\n    i0.ɵɵtemplate(18, PictureMaterialComponent_tr_36_button_18_Template, 2, 0, \"button\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r7 == null ? null : item_r7.CName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPictureCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 8, item_r7 == null ? null : item_r7.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"img\", 33);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, ctx_r3.currentImageShowing), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 38);\n    i0.ɵɵlistener(\"blur\", function PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 39);\n    i0.ɵɵelement(4, \"img\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 23)(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_button_click_6_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const inputFile_r11 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(1, \"\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"input\", 34, 2);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_39_ng_template_6_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"table\", 36)(6, \"thead\")(7, \"tr\", 15)(8, \"th\", 37);\n    i0.ɵɵtext(9, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 16);\n    i0.ɵɵtext(11, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"th\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template, 8, 2, \"tr\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listPictures);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.uploadImage(ref_r15));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 26)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 27);\n    i0.ɵɵtemplate(5, PictureMaterialComponent_ng_template_39_div_5_Template, 3, 3, \"div\", 28)(6, PictureMaterialComponent_ng_template_39_ng_template_6_Template, 15, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-card-footer\")(9, \"div\", 29)(10, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_Template_button_click_10_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ref_r15.close();\n      return i0.ɵɵresetView(ctx_r3.currentImageShowing = \"\");\n    });\n    i0.ɵɵtext(11, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, PictureMaterialComponent_ng_template_39_button_12_Template, 2, 0, \"button\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r17 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currentImageShowing ? \"\\u6AA2\\u8996\" : \"\\u5716\\u7247\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentImageShowing)(\"ngIfElse\", upload_r17);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.currentImageShowing);\n  }\n}\nexport class PictureMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _pictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.currentImageShowing = \"\";\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getPicturelList(1))).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    return this._pictureService.apiPictureGetPicturelListPost$Json({\n      body: {\n        PageIndex: pageIndex,\n        PageSize: this.pageSize,\n        CBuildCaseId: this.selectedBuildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.images = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    }));\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    this.dialogService.open(ref);\n    if (!!item) {\n      this.currentImageShowing = item.CFile;\n    }\n  }\n  validation() {\n    this.valid.clear();\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          // Get name file ( no extension)\n          const fileNameWithoutExtension = file.name.split('.')[0];\n          // Find files with duplicate names\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n          if (existingFileIndex !== -1) {\n            // If name is duplicate, update file data\n            this.listPictures[existingFileIndex] = {\n              ...this.listPictures[existingFileIndex],\n              data: base64Str,\n              CFile: file,\n              extension: this._utilityService.getFileExtension(file.name)\n            };\n          } else {\n            // If not duplicate, add new file\n            file.id = new Date().getTime();\n            this.listPictures.push({\n              id: new Date().getTime(),\n              name: fileNameWithoutExtension,\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n          }\n          // Reset input file to be able to select the old file again\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    this._pictureService.apiPictureUploadListPicturePost$Json({\n      body: {\n        CBuildCaseId: this.selectedBuildCaseId,\n        CPath: \"picture\",\n        CFile: this.listPictures.map(x => x.CFile)\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG('執行成功');\n        this.listPictures = [];\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  static {\n    this.ɵfac = function PictureMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PictureMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PictureMaterialComponent,\n      selectors: [[\"ngx-picture-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 41,\n      vars: 7,\n      consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function PictureMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u5EFA\\u6750\\u5716\\u7247\\u7DE8\\u865F\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedChange($event));\n          });\n          i0.ɵɵtemplate(12, PictureMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 11);\n          i0.ɵɵtemplate(15, PictureMaterialComponent_button_15_Template, 2, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"table\", 14)(18, \"thead\")(19, \"tr\", 15)(20, \"th\", 16);\n          i0.ɵɵtext(21, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 16);\n          i0.ɵɵtext(23, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 16);\n          i0.ɵɵtext(25, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 16);\n          i0.ɵɵtext(27, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 16);\n          i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 16);\n          i0.ɵɵtext(31, \"\\u5EFA\\u6750\\u5716\\u7247\\u7DE8\\u865F\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"th\", 16);\n          i0.ɵɵtext(33, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"th\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"tbody\");\n          i0.ɵɵtemplate(36, PictureMaterialComponent_tr_36_Template, 19, 11, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"nb-card-footer\", 18)(38, \"ngx-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(39, PictureMaterialComponent_ng_template_39_Template, 13, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.images);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseFilePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwaWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvcGljdHVyZS1tYXRlcmlhbC9waWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "of", "tap", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "PictureMaterialComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "addNew", "PictureMaterialComponent_tr_36_button_18_Template_button_click_0_listener", "_r8", "item_r7", "$implicit", "PictureMaterialComponent_tr_36_Template_a_click_4_listener", "_r6", "openPdfInNewTab", "CFile", "ɵɵtemplate", "PictureMaterialComponent_tr_36_button_18_Template", "ɵɵtextInterpolate", "CId", "CName", "<PERSON>art", "CLocation", "CSelectName", "CPictureCode", "ɵɵpipeBind2", "CUpdateDT", "isRead", "ɵɵelement", "ɵɵpipeBind1", "currentImageShowing", "ɵɵsanitizeUrl", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_input_blur_2_listener", "$event", "i_r13", "_r12", "index", "renameFile", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_button_click_6_listener", "picture_r14", "removeImage", "id", "name", "data", "PictureMaterialComponent_ng_template_39_ng_template_6_Template_button_click_0_listener", "_r10", "inputFile_r11", "click", "PictureMaterialComponent_ng_template_39_ng_template_6_Template_input_change_2_listener", "detectFiles", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template", "listPictures", "PictureMaterialComponent_ng_template_39_button_12_Template_button_click_0_listener", "_r16", "ref_r15", "dialogRef", "uploadImage", "PictureMaterialComponent_ng_template_39_div_5_Template", "PictureMaterialComponent_ng_template_39_ng_template_6_Template", "ɵɵtemplateRefExtractor", "PictureMaterialComponent_ng_template_39_Template_button_click_10_listener", "_r9", "close", "PictureMaterialComponent_ng_template_39_button_12_Template", "upload_r17", "PictureMaterialComponent", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_buildCaseService", "message", "_utilityService", "images", "listUserBuildCases", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getPicturelList", "subscribe", "CFileUrl", "openFileInNewTab", "pageIndex", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "totalRecords", "TotalItems", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "ref", "item", "open", "validation", "clear", "onSubmit", "event", "target", "files", "length", "file", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "fileNameWithoutExtension", "split", "existingFileIndex", "findIndex", "picture", "extension", "getFileExtension", "Date", "getTime", "push", "value", "pictureId", "filter", "x", "apiPictureUploadListPicturePost$Json", "CPath", "map", "showSucessMSG", "showErrorMSG", "Message", "blob", "slice", "size", "type", "newFile", "File", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "PictureService", "BuildCaseService", "i5", "MessageService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PictureMaterialComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ɵɵtwoWayBindingSet", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "PictureMaterialComponent_nb_option_12_Template", "PictureMaterialComponent_button_15_Template", "PictureMaterialComponent_tr_36_Template", "PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_38_listener", "PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_38_listener", "PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener", "PictureMaterialComponent_ng_template_39_Template", "ɵɵtwoWayProperty", "isCreate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n\r\n  images: GetPictureListResponse[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n\r\n  selectedBuildCaseId: number\r\n\r\n  currentImageShowing: string = \"\"\r\n\r\n  listPictures: any[] = []\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getPicturelList(pageIndex: number) {\r\n    return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n      body: {\r\n        PageIndex: pageIndex,\r\n        PageSize: this.pageSize,\r\n        CBuildCaseId: this.selectedBuildCaseId\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.images = res.Entries! ?? []\r\n          this.totalRecords = res.TotalItems!;\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  }\r\n\r\n  addNew(ref: any, item?: GetPictureListResponse) {\r\n    this.dialogService.open(ref)\r\n    if (!!item) {\r\n      this.currentImageShowing = item.CFile!\r\n    }\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          // Get name file ( no extension)\r\n          const fileNameWithoutExtension = file.name.split('.')[0];\r\n          // Find files with duplicate names\r\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n          if (existingFileIndex !== -1) {\r\n            // If name is duplicate, update file data\r\n            this.listPictures[existingFileIndex] = {\r\n              ...this.listPictures[existingFileIndex],\r\n              data: base64Str,\r\n              CFile: file,\r\n              extension: this._utilityService.getFileExtension(file.name)\r\n            };\r\n          } else {\r\n            // If not duplicate, add new file\r\n            file.id = new Date().getTime();\r\n            this.listPictures.push({\r\n              id: new Date().getTime(),\r\n              name: fileNameWithoutExtension,\r\n              data: base64Str,\r\n              extension: this._utilityService.getFileExtension(file.name),\r\n              CFile: file\r\n            });\r\n          }\r\n          // Reset input file to be able to select the old file again\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n    this._pictureService.apiPictureUploadListPicturePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.selectedBuildCaseId,\r\n        CPath: \"picture\",\r\n        CFile: this.listPictures.map(x => x.CFile)!\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.message.showSucessMSG('執行成功')\r\n          this.listPictures = []\r\n        } else {\r\n          this.message.showErrorMSG(res.Message!)\r\n        }\r\n      }),\r\n      concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null))\r\n    ).subscribe()\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可設定上傳建材示意圖片，上傳前請將圖片檔案改為建材圖片編號。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" (selectedChange)=\"selectedChange($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listUserBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            圖片上傳</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">建材選項名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">建材圖片編號（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-1\">最新圖片上傳時間</th>\r\n            <th scope=\"col\" class=\"col-1\"></th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of images ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>\r\n              <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CName}}</a>\r\n            </td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td>{{ item.CSelectName}}</td>\r\n            <td>{{ item.CPictureCode}}</td>\r\n            <td>{{ item?.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"addNew(dialog, item)\">檢視</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        {{currentImageShowing ? '檢視' : '圖片上傳'}}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\" *ngIf=\"currentImageShowing else upload\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing | addBaseFile\">\r\n      </div>\r\n      <ng-template #upload>\r\n        <button class=\"btn btn-info\" (click)=\"inputFile.click()\">圖片上傳</button>\r\n        <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\"\r\n          accept=\"image/png, image/gif, image/jpeg\" multiple>\r\n        <div class=\"mt-3 w-full flex flex-col\">\r\n          <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr style=\"background-color: #27ae60; color: white;\">\r\n                <th scope=\"col\" class=\"col-4\">文件名</th>\r\n                <th scope=\"col\" class=\"col-1\">檢視</th>\r\n                <th scope=\"col\" class=\"col-1\"></th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let picture of listPictures; let i = index\">\r\n                <td>\r\n                  <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                    (blur)=\"renameFile($event, i)\">\r\n                </td>\r\n                <td class=\"w-[100px] h-auto\">\r\n                  <img class=\"fit-size\" [src]=\"picture.data\">\r\n                </td>\r\n                <td class=\"text-center w-32\">\r\n                  <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeImage(picture.id)\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-template>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close(); this.currentImageShowing = ''\">取消</button>\r\n        <button *ngIf=\"!currentImageShowing\" class=\"btn btn-success\" (click)=\"uploadImage(ref)\">儲存</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAInD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;;ICCvDC,EAAA,CAAAC,cAAA,oBAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IAC7EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAMFT,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAU,UAAA,mBAAAC,oEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IACnDhB,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA+BXH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAU,UAAA,mBAAAU,0EAAA;MAAApB,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAe,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,EAAAM,OAAA,CAAoB;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAX9CH,EADF,CAAAC,cAAA,SAAgD,SAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEpBH,EADF,CAAAC,cAAA,SAAI,YAC6E;IAAvCD,EAAA,CAAAU,UAAA,mBAAAc,2DAAA;MAAA,MAAAF,OAAA,GAAAtB,EAAA,CAAAY,aAAA,CAAAa,GAAA,EAAAF,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAY,eAAA,CAAAJ,OAAA,kBAAAA,OAAA,CAAAK,KAAA,CAA4B;IAAA,EAAC;IAAC3B,EAAA,CAAAE,MAAA,GAAgB;IACjGF,EADiG,CAAAG,YAAA,EAAI,EAChG;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAA4B,UAAA,KAAAC,iDAAA,qBACiC;IAErC7B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAS,GAAA,CAAa;IAEgE/B,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,kBAAAA,OAAA,CAAAU,KAAA,CAAgB;IAE7FhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAW,KAAA,CAAe;IACfjC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAY,SAAA,CAAmB;IACnBlC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAa,WAAA,CAAqB;IACrBnC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAc,YAAA,CAAsB;IACtBpC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAqC,WAAA,QAAAf,OAAA,kBAAAA,OAAA,CAAAgB,SAAA,yBAAkD;IAEAtC,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyB,MAAA,CAAY;;;;;IAuBxEvC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAwC,SAAA,cAAgE;;IAClExC,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAAyC,WAAA,OAAA3B,MAAA,CAAA4B,mBAAA,GAAA1C,EAAA,CAAA2C,aAAA,CAAyC;;;;;;IAkBrD3C,EAFJ,CAAAC,cAAA,SAAwD,SAClD,gBAE+B;IAA/BD,EAAA,CAAAU,UAAA,kBAAAkC,2FAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA9C,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAQJ,MAAA,CAAAmC,UAAA,CAAAJ,MAAA,EAAAC,KAAA,CAAqB;IAAA,EAAC;IAClC9C,EAFE,CAAAG,YAAA,EACiC,EAC9B;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAwC,SAAA,cAA2C;IAC7CxC,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA6B,iBACyD;IAAlCD,EAAA,CAAAU,UAAA,mBAAAwC,6FAAA;MAAA,MAAAC,WAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAxB,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAsC,WAAA,CAAAD,WAAA,CAAAE,EAAA,CAAuB;IAAA,EAAC;IAACrD,EAAA,CAAAE,MAAA,mBAAE;IAE1FF,EAF0F,CAAAG,YAAA,EAAS,EAC5F,EACF;;;;IATwDH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,UAAA+C,WAAA,CAAAG,IAAA,CAAsB;IAIzDtD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,QAAA+C,WAAA,CAAAI,IAAA,EAAAvD,EAAA,CAAA2C,aAAA,CAAoB;;;;;;IAnBpD3C,EAAA,CAAAC,cAAA,iBAAyD;IAA5BD,EAAA,CAAAU,UAAA,mBAAA8C,uFAAA;MAAAxD,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAAC,aAAA,GAAA1D,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASwC,aAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAC3D,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACtEH,EAAA,CAAAC,cAAA,mBACqD;IADRD,EAAA,CAAAU,UAAA,oBAAAkD,uFAAAf,MAAA;MAAA7C,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAUJ,MAAA,CAAA+C,WAAA,CAAAhB,MAAA,CAAmB;IAAA,EAAC;IAA3E7C,EAAA,CAAAG,YAAA,EACqD;IAK7CH,EAJR,CAAAC,cAAA,cAAuC,gBACuC,YACnE,aACgD,aACrB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAwC,SAAA,cAAmC;IAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA4B,UAAA,KAAAkC,oEAAA,iBAAwD;IAc9D9D,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAdwBH,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAiD,YAAA,CAAiB;;;;;;IAoB/C/D,EAAA,CAAAC,cAAA,iBAAwF;IAA3BD,EAAA,CAAAU,UAAA,mBAAAsD,mFAAA;MAAAhE,EAAA,CAAAY,aAAA,CAAAqD,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAe,aAAA,GAAAoD,SAAA;MAAA,MAAArD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAsD,WAAA,CAAAF,OAAA,CAAgB;IAAA,EAAC;IAAClE,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1CrGH,EAFJ,CAAAC,cAAA,kBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IACjBH,EAAA,CAAAC,cAAA,uBAAwC;IAItCD,EAHA,CAAA4B,UAAA,IAAAyC,sDAAA,kBAAmE,IAAAC,8DAAA,iCAAAtE,EAAA,CAAAuE,sBAAA,CAG9C;IA8BvBvE,EAAA,CAAAG,YAAA,EAAe;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,cACgC,kBAC6C;IAArDD,EAAA,CAAAU,UAAA,mBAAA8D,0EAAA;MAAA,MAAAN,OAAA,GAAAlE,EAAA,CAAAY,aAAA,CAAA6D,GAAA,EAAAN,SAAA;MAAA,MAAArD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAASmD,OAAA,CAAAQ,KAAA,EAAW;MAAA,OAAA1E,EAAA,CAAAkB,WAAA,CAAAJ,MAAA,CAAA4B,mBAAA,GAA6B,EAAE;IAAA,EAAC;IAAC1C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAA4B,UAAA,KAAA+C,0DAAA,qBAAwF;IAG9F3E,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;;IA5CJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA4B,mBAAA,oDACF;IAG4B1C,EAAA,CAAAO,SAAA,GAA0B;IAAAP,EAA1B,CAAAI,UAAA,SAAAU,MAAA,CAAA4B,mBAAA,CAA0B,aAAAkC,UAAA,CAAW;IAqCtD5E,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAA4B,mBAAA,CAA0B;;;ADvF3C,OAAM,MAAOmC,wBAAyB,SAAQ9E,aAAa;EAWzD+E,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACN,MAAM,CAAC;IARL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IAhBzB,KAAAC,MAAM,GAA6B,EAAE;IACrC,KAAAC,kBAAkB,GAA8B,EAAE;IAIlD,KAAA7C,mBAAmB,GAAW,EAAE;IAEhC,KAAAqB,YAAY,GAAU,EAAE;EAYxB;EAESyB,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACN,iBAAiB,CAACO,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACH9F,GAAG,CAAC+F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACN,kBAAkB,GAAGK,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAACjF,GAAI;MAC5D;IACF,CAAC,CAAC,EACFX,SAAS,CAAC,MAAM,IAAI,CAACqG,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACC,SAAS,EAAE;EACjB;EAEAvE,eAAeA,CAACwE,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAACb,eAAe,CAACc,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EAEAF,eAAeA,CAACI,SAAiB;IAC/B,OAAO,IAAI,CAAClB,eAAe,CAACmB,kCAAkC,CAAC;MAC7DC,IAAI,EAAE;QACJC,SAAS,EAAEH,SAAS;QACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;QACvBC,YAAY,EAAE,IAAI,CAACX;;KAEtB,CAAC,CAACJ,IAAI,CACL9F,GAAG,CAAC+F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACP,MAAM,GAAGM,GAAG,CAACE,OAAQ,IAAI,EAAE;QAChC,IAAI,CAACa,YAAY,GAAGf,GAAG,CAACgB,UAAW;MACrC;IACF,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAACT,SAAiB;IAC3B;IACA,IAAI,CAACJ,eAAe,CAACI,SAAS,CAAC,CAACH,SAAS,EAAE;EAC7C;EAEAa,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAAChB,mBAAmB,GAAGgB,WAAW;IACtC,IAAI,CAACf,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAEA9E,MAAMA,CAAC6F,GAAQ,EAAEC,IAA6B;IAC5C,IAAI,CAACjC,aAAa,CAACkC,IAAI,CAACF,GAAG,CAAC;IAC5B,IAAI,CAAC,CAACC,IAAI,EAAE;MACV,IAAI,CAACvE,mBAAmB,GAAGuE,IAAI,CAACtF,KAAM;IACxC;EACF;EAEAwF,UAAUA,CAAA;IACR,IAAI,CAAClC,KAAK,CAACmC,KAAK,EAAE;EACpB;EAEAC,QAAQA,CAACL,GAAQ,GACjB;EAEAnD,WAAWA,CAACyD,KAAU;IACpB,KAAK,IAAItE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGsE,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,MAAM,EAAEzE,KAAK,EAAE,EAAE;MAC9D,MAAM0E,IAAI,GAAGJ,KAAK,CAACC,MAAM,CAACC,KAAK,CAACxE,KAAK,CAAC;MACtC,IAAI0E,IAAI,EAAE;QACR,IAAIC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACH,IAAI,CAAC;QAC1BC,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;UAC/C,IAAI,CAACD,SAAS,EAAE;YACd;UACF;UACA;UACA,MAAME,wBAAwB,GAAGP,IAAI,CAACpE,IAAI,CAAC4E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACxD;UACA,MAAMC,iBAAiB,GAAG,IAAI,CAACpE,YAAY,CAACqE,SAAS,CAACC,OAAO,IAAIA,OAAO,CAAC/E,IAAI,KAAK2E,wBAAwB,CAAC;UAC3G,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5B;YACA,IAAI,CAACpE,YAAY,CAACoE,iBAAiB,CAAC,GAAG;cACrC,GAAG,IAAI,CAACpE,YAAY,CAACoE,iBAAiB,CAAC;cACvC5E,IAAI,EAAEwE,SAAS;cACfpG,KAAK,EAAE+F,IAAI;cACXY,SAAS,EAAE,IAAI,CAACjD,eAAe,CAACkD,gBAAgB,CAACb,IAAI,CAACpE,IAAI;aAC3D;UACH,CAAC,MAAM;YACL;YACAoE,IAAI,CAACrE,EAAE,GAAG,IAAImF,IAAI,EAAE,CAACC,OAAO,EAAE;YAC9B,IAAI,CAAC1E,YAAY,CAAC2E,IAAI,CAAC;cACrBrF,EAAE,EAAE,IAAImF,IAAI,EAAE,CAACC,OAAO,EAAE;cACxBnF,IAAI,EAAE2E,wBAAwB;cAC9B1E,IAAI,EAAEwE,SAAS;cACfO,SAAS,EAAE,IAAI,CAACjD,eAAe,CAACkD,gBAAgB,CAACb,IAAI,CAACpE,IAAI,CAAC;cAC3D3B,KAAK,EAAE+F;aACR,CAAC;UACJ;UACA;UACAJ,KAAK,CAACC,MAAM,CAACoB,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEAvF,WAAWA,CAACwF,SAAiB;IAC3B,IAAI,CAAC7E,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC8E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzF,EAAE,IAAIuF,SAAS,CAAC;EACtE;EAEAxE,WAAWA,CAAC4C,GAAQ;IAClB,IAAI,CAAC9B,eAAe,CAAC6D,oCAAoC,CAAC;MACxDzC,IAAI,EAAE;QACJI,YAAY,EAAE,IAAI,CAACX,mBAAmB;QACtCiD,KAAK,EAAE,SAAS;QAChBrH,KAAK,EAAE,IAAI,CAACoC,YAAY,CAACkF,GAAG,CAACH,CAAC,IAAIA,CAAC,CAACnH,KAAK;;KAE5C,CAAC,CAACgE,IAAI,CACL9F,GAAG,CAAC+F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACT,OAAO,CAAC8D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnF,YAAY,GAAG,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACqB,OAAO,CAAC+D,YAAY,CAACvD,GAAG,CAACwD,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC,EACFzJ,SAAS,CAAEiG,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC,GAAGpG,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAACqG,SAAS,EAAE;EACf;EAEAhD,UAAUA,CAACqE,KAAU,EAAEtE,KAAa;IAClC,IAAIqG,IAAI,GAAG,IAAI,CAACtF,YAAY,CAACf,KAAK,CAAC,CAACrB,KAAK,CAAC2H,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvF,YAAY,CAACf,KAAK,CAAC,CAACrB,KAAK,CAAC4H,IAAI,EAAE,IAAI,CAACxF,YAAY,CAACf,KAAK,CAAC,CAACrB,KAAK,CAAC6H,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAG/B,KAAK,CAACC,MAAM,CAACoB,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC5E,YAAY,CAACf,KAAK,CAAC,CAACsF,SAAS,EAAE,EAAE;MAAEkB,IAAI,EAAE,IAAI,CAACzF,YAAY,CAACf,KAAK,CAAC,CAACrB,KAAK,CAAC6H;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAACzF,YAAY,CAACf,KAAK,CAAC,CAACrB,KAAK,GAAG8H,OAAO;EAC1C;;;uCA7JW5E,wBAAwB,EAAA7E,EAAA,CAAA2J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7J,EAAA,CAAA2J,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/J,EAAA,CAAA2J,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAjK,EAAA,CAAA2J,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnK,EAAA,CAAA2J,iBAAA,CAAAO,EAAA,CAAAE,gBAAA,GAAApK,EAAA,CAAA2J,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAtK,EAAA,CAAA2J,iBAAA,CAAAY,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxB3F,wBAAwB;MAAA4F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3K,EAAA,CAAA4K,0BAAA,EAAA5K,EAAA,CAAA6K,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCzBnCnL,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAwC,SAAA,qBAAiC;UACnCxC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2LAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKlEH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,oBACiB;UADWD,EAAA,CAAAqL,gBAAA,2BAAAC,sEAAAzI,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAJ,GAAA,CAAArF,mBAAA,EAAAlD,MAAA,MAAAuI,GAAA,CAAArF,mBAAA,GAAAlD,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAiC;UAAC7C,EAAA,CAAAU,UAAA,4BAAA+K,uEAAA5I,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAA,OAAAvL,EAAA,CAAAkB,WAAA,CAAkBkK,GAAA,CAAAtE,cAAA,CAAAjE,MAAA,CAAsB;UAAA,EAAC;UAErG7C,EAAA,CAAA4B,UAAA,KAAA8J,8CAAA,wBAAgF;UAKtF1L,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAC7CD,EAAA,CAAA4B,UAAA,KAAA+J,2CAAA,qBAAuE;UAI7E3L,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAwC,SAAA,cAAmC;UAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA4B,UAAA,KAAAgK,uCAAA,mBAAgD;UAkBxD5L,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAAqL,gBAAA,kCAAAQ,kFAAAhJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAJ,GAAA,CAAAzE,YAAA,EAAA9D,MAAA,MAAAuI,GAAA,CAAAzE,YAAA,GAAA9D,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAiC,4BAAAiJ,4EAAAjJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAJ,GAAA,CAAA3E,QAAA,EAAA5D,MAAA,MAAAuI,GAAA,CAAA3E,QAAA,GAAA5D,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAwB,wBAAAkJ,wEAAAlJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAAvL,EAAA,CAAAwL,kBAAA,CAAAJ,GAAA,CAAAhF,SAAA,EAAAvD,MAAA,MAAAuI,GAAA,CAAAhF,SAAA,GAAAvD,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAqB;UAC5F7C,EAAA,CAAAU,UAAA,wBAAAqL,wEAAAlJ,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAA2K,GAAA;YAAA,OAAAvL,EAAA,CAAAkB,WAAA,CAAckK,GAAA,CAAAvE,WAAA,CAAAhE,MAAA,CAAmB;UAAA,EAAC;UAGxC7C,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA4B,UAAA,KAAAoK,gDAAA,iCAAAhM,EAAA,CAAAuE,sBAAA,CAAkD;;;UAzDZvE,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAiM,gBAAA,YAAAb,GAAA,CAAArF,mBAAA,CAAiC;UAE1B/F,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAA7F,kBAAA,CAAqB;UAQDvF,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAgL,GAAA,CAAAc,QAAA,CAAc;UAqBhDlM,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAgL,GAAA,CAAA9F,MAAA,CAAY;UAoBvBtF,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAAiM,gBAAA,mBAAAb,GAAA,CAAAzE,YAAA,CAAiC,aAAAyE,GAAA,CAAA3E,QAAA,CAAwB,SAAA2E,GAAA,CAAAhF,SAAA,CAAqB;;;qBD1C9F1G,YAAY,EAAAyM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZxM,YAAY,EAAAyM,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,gBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}