{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport * as moment from 'moment';\nimport { BaseComponent } from '../../components/base/baseComponent';\nlet CustomerChangePictureComponent = class CustomerChangePictureComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _specialChangeService, _houseService, route, message, location, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._specialChangeService = _specialChangeService;\n    this._houseService = _houseService;\n    this.route = route;\n    this.message = message;\n    this.location = location;\n    this._eventService = _eventService;\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.statusOptions = [{\n      value: 0,\n      key: 'allow',\n      label: '允許'\n    }, {\n      value: 1,\n      key: 'not allowed',\n      label: '不允許'\n    }];\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.listPictures = [];\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id1');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        const idParam2 = params.get('id2');\n        const id2 = idParam2 ? +idParam2 : 0;\n        this.houseId = id2;\n        this.getListSpecialChange();\n        this.getHouseById();\n      }\n    });\n  }\n  openPdfInNewTab(data) {\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\n  }\n  getListSpecialChange() {\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\n      body: {\n        CHouseId: this.houseId,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.listSpecialChange = res.Entries ?? [];\n        this.totalRecords = res.TotalItems;\n      }\n    });\n  }\n  getHouseById() {\n    this._houseService.apiHouseGetHouseByIdPost$Json({\n      body: {\n        CHouseID: this.houseId\n      }\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.house = res.Entries;\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`;\n      }\n    });\n  }\n  getSpecialChangeById(ref, CSpecialChangeID) {\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({\n      body: CSpecialChangeID\n    }).subscribe(res => {\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\n        this.SpecialChange = res.Entries;\n        this.formSpecialChange = {\n          CApproveRemark: this.SpecialChange.CApproveRemark,\n          CBuildCaseID: this.buildCaseId,\n          CDrawingName: this.SpecialChange.CDrawingName,\n          CHouseID: this.houseId,\n          SpecialChangeFiles: null\n        };\n        if (this.SpecialChange.CChangeDate) {\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\n        }\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  onSaveSpecialChange(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({\n      body: this.formatParam()\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getListSpecialChange();\n        ref.close();\n      }\n    });\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getListSpecialChange();\n  }\n  addNew(ref) {\n    this.imageUrlList = [];\n    this.isEdit = false;\n    this.formSpecialChange = {\n      CApproveRemark: '',\n      CBuildCaseID: this.buildCaseId,\n      CChangeDate: '',\n      CDrawingName: '',\n      CHouseID: this.houseId,\n      SpecialChangeFiles: null\n    };\n    this.dialogService.open(ref);\n  }\n  onEdit(ref, specialChange) {\n    this.imageUrlList = [];\n    this.isEdit = true;\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID);\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate);\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName);\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark);\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DD');\n    }\n    return '';\n  }\n  deleteDataFields(array) {\n    for (const item of array) {\n      delete item.data;\n    }\n    return array;\n  }\n  formatParam() {\n    const result = {\n      ...this.formSpecialChange,\n      SpecialChangeFiles: this.imageUrlList\n    };\n    this.deleteDataFields(result.SpecialChangeFiles);\n    if (this.formSpecialChange.CChangeDate) {\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate);\n    }\n    return result;\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  removeBase64Prefix(base64String) {\n    const prefixIndex = base64String.indexOf(\",\");\n    if (prefixIndex !== -1) {\n      return base64String.substring(prefixIndex + 1);\n    }\n    return base64String;\n  }\n  detectFiles(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\n        const firstFile = files[0];\n        const fileName = firstFile.name;\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\n      }\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (!fileRegex.test(file.name)) {\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\n          continue;\n        }\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\n          const reader = new FileReader();\n          reader.onload = e => {\n            // 判斷檔案類型\n            let fileType = 1; // 預設為其他\n            const fileName = file.name.toLowerCase();\n            if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\n              fileType = 2; // 圖片\n            } else if (fileName.endsWith('.pdf')) {\n              fileType = 1; // PDF\n            } else if (fileName.match(/\\.(dwg|dxf)$/)) {\n              fileType = 3; // CAD\n            }\n            this.imageUrlList.push({\n              data: e.target.result,\n              CFileBlood: this.removeBase64Prefix(e.target.result),\n              CFileName: file.name,\n              CFileType: fileType\n            });\n            if (this.imageUrlList.length === files.length) {\n              console.log('this.imageUrlList', this.imageUrlList);\n              if (this.fileInput) {\n                this.fileInput.nativeElement.value = null;\n              }\n            }\n          };\n          reader.readAsDataURL(file);\n        }\n      }\n    }\n  }\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  isImage(fileType) {\n    return fileType === 2;\n  }\n  isCad(fileType) {\n    return fileType === 3;\n  }\n  isPdf(extension) {\n    return extension.toLowerCase() === 'pdf';\n  }\n  removeFile(index) {\n    this.imageUrlList.splice(index, 1);\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {}\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  openNewTab(url) {\n    if (url) window.open(url, \"_blank\");\n  }\n};\n__decorate([ViewChild('fileInput')], CustomerChangePictureComponent.prototype, \"fileInput\", void 0);\nCustomerChangePictureComponent = __decorate([Component({\n  selector: 'ngx-customer-change-picture',\n  templateUrl: './customer-change-picture.component.html',\n  styleUrls: ['./customer-change-picture.component.scss']\n})], CustomerChangePictureComponent);\nexport { CustomerChangePictureComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "moment", "BaseComponent", "CustomerChangePictureComponent", "constructor", "_allow", "dialogService", "valid", "_specialChangeService", "_houseService", "route", "message", "location", "_eventService", "imageUrlList", "isEdit", "statusOptions", "value", "key", "label", "pageFirst", "pageSize", "pageIndex", "totalRecords", "listPictures", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "id", "buildCaseId", "idParam2", "id2", "houseId", "getListSpecialChange", "getHouseById", "openPdfInNewTab", "data", "CFileRes", "CFile", "window", "open", "apiSpecialChangeGetListSpecialChangePost$Json", "body", "CHouseId", "PageIndex", "PageSize", "res", "TotalItems", "Entries", "StatusCode", "listSpecialChange", "apiHouseGetHouseByIdPost$Json", "CHouseID", "house", "houseTitle", "CHousehold", "CFloor", "getSpecialChangeById", "ref", "CSpecialChangeID", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "SpecialChange", "formSpecialChange", "CApproveRemark", "CBuildCaseID", "CDrawingName", "SpecialChangeFiles", "CChangeDate", "Date", "onSaveSpecialChange", "validation", "errorMessages", "length", "showErrorMSGs", "apiSpecialChangeSaveSpecialChangePost$Json", "formatParam", "showSucessMSG", "close", "pageChanged", "newPage", "addNew", "onEdit", "specialChange", "clear", "required", "formatDate", "format", "deleteDataFields", "array", "item", "result", "onClose", "removeBase64Prefix", "base64String", "prefixIndex", "indexOf", "substring", "detectFiles", "event", "files", "target", "allowedTypes", "fileRegex", "firstFile", "fileName", "name", "fileNameWithoutExtension", "lastIndexOf", "i", "file", "test", "showErrorMSG", "includes", "type", "reader", "FileReader", "onload", "e", "fileType", "toLowerCase", "match", "endsWith", "push", "CFileBlood", "CFileName", "CFileType", "console", "log", "fileInput", "nativeElement", "readAsDataURL", "isPDFString", "str", "isCadString", "lowerStr", "isImage", "isCad", "isPdf", "extension", "removeFile", "index", "splice", "removeImage", "pictureId", "filter", "x", "uploadImage", "renameFile", "blob", "slice", "size", "newFile", "File", "goBack", "action", "payload", "back", "openNewTab", "url", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\household-management\\customer-change-picture\\customer-change-picture.component.ts"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { HouseService, SpecialChangeService } from 'src/services/api/services';\r\nimport { TblHouse, SpecialChangeRes } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number,\r\n  key?: string\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-customer-change-picture',\r\n  templateUrl: './customer-change-picture.component.html',\r\n  styleUrls: ['./customer-change-picture.component.scss'],\r\n})\r\n\r\nexport class CustomerChangePictureComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _houseService: HouseService,\r\n\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private location: Location,\r\n    private _eventService: EventService\r\n  ) { super(_allow) }\r\n\r\n  @ViewChild('fileInput') fileInput: ElementRef;\r\n  imageUrlList: any[] = [];\r\n  isEdit = false\r\n  statusOptions: selectItem[] = [\r\n    {\r\n      value: 0,\r\n      key: 'allow',\r\n      label: '允許',\r\n    },\r\n    {\r\n      value: 1,\r\n      key: 'not allowed',\r\n      label: '不允許',\r\n    }\r\n  ]\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  selectedBuildCase: selectItem\r\n\r\n  buildCaseId: number\r\n  houseId: number\r\n  house: TblHouse\r\n  houseTitle: string\r\n\r\n  override ngOnInit(): void {\r\n\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id1');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n        const idParam2 = params.get('id2');\r\n        const id2 = idParam2 ? +idParam2 : 0;\r\n        this.houseId = id2\r\n        this.getListSpecialChange()\r\n        this.getHouseById()\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  openPdfInNewTab(data?: any) {\r\n    if (data && data.CFileRes.CFile) window.open(data.CFileRes.CFile, '_blank');\r\n  }\r\n\r\n\r\n  listSpecialChange: any[]\r\n\r\n  getListSpecialChange() {\r\n    this._specialChangeService.apiSpecialChangeGetListSpecialChangePost$Json({\r\n      body: {\r\n        CHouseId: this.houseId,\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.listSpecialChange = res.Entries! ?? []\r\n        this.totalRecords = res.TotalItems;\r\n      }\r\n    })\r\n  }\r\n\r\n  getHouseById() {\r\n    this._houseService.apiHouseGetHouseByIdPost$Json({\r\n      body: {\r\n        CHouseID: this.houseId\r\n      }\r\n    }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.house = res.Entries\r\n        this.houseTitle = `${this.house.CHousehold} ${this.house.CFloor}F`\r\n      }\r\n    })\r\n  }\r\n\r\n  SpecialChange: SpecialChangeRes\r\n  fileUrl: any\r\n  getSpecialChangeById(ref: any, CSpecialChangeID: any) {\r\n    this._specialChangeService.apiSpecialChangeGetSpecialChangeByIdPost$Json({ body: CSpecialChangeID }).subscribe(res => {\r\n      if (res.TotalItems && res.Entries && res.StatusCode == 0) {\r\n        this.SpecialChange = res.Entries\r\n        this.formSpecialChange = {\r\n          CApproveRemark: this.SpecialChange.CApproveRemark,\r\n          CBuildCaseID: this.buildCaseId,\r\n          CDrawingName: this.SpecialChange.CDrawingName,\r\n          CHouseID: this.houseId,\r\n          SpecialChangeFiles: null\r\n        }\r\n        if (this.SpecialChange.CChangeDate) {\r\n          this.formSpecialChange.CChangeDate = new Date(this.SpecialChange.CChangeDate);\r\n        }\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  formSpecialChange: any\r\n\r\n  onSaveSpecialChange(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n\r\n    this._specialChangeService.apiSpecialChangeSaveSpecialChangePost$Json({ body: this.formatParam() }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getListSpecialChange()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getListSpecialChange();\r\n  }\r\n\r\n\r\n  addNew(ref: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = false\r\n    this.formSpecialChange = {\r\n      CApproveRemark: '',\r\n      CBuildCaseID: this.buildCaseId,\r\n      CChangeDate: '',\r\n      CDrawingName: '',\r\n      CHouseID: this.houseId,\r\n      SpecialChangeFiles: null\r\n    }\r\n    this.dialogService.open(ref)\r\n  }\r\n\r\n  onEdit(ref: any, specialChange: any) {\r\n    this.imageUrlList = [];\r\n    this.isEdit = true\r\n    this.getSpecialChangeById(ref, specialChange.CSpecialChangeID)\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[討論日期]', this.formSpecialChange.CChangeDate)\r\n    this.valid.required('[圖面名稱]', this.formSpecialChange.CDrawingName)\r\n    this.valid.required('[審核說明]', this.formSpecialChange.CApproveRemark)\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DD');\r\n    }\r\n    return ''\r\n  }\r\n\r\n  deleteDataFields(array: any[]) {\r\n    for (const item of array) {\r\n      delete item.data;\r\n    }\r\n    return array;\r\n  }\r\n\r\n  formatParam() {\r\n    const result = {\r\n      ...this.formSpecialChange,\r\n      SpecialChangeFiles: this.imageUrlList\r\n    }\r\n    this.deleteDataFields(result.SpecialChangeFiles)\r\n\r\n    if (this.formSpecialChange.CChangeDate) {\r\n      result.CChangeDate = this.formatDate(this.formSpecialChange.CChangeDate)\r\n    }\r\n    return result\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  removeBase64Prefix(base64String: any) {\r\n    const prefixIndex = base64String.indexOf(\",\");\r\n    if (prefixIndex !== -1) {\r\n      return base64String.substring(prefixIndex + 1);\r\n    }\r\n    return base64String;\r\n  } detectFiles(event: any) {\r\n    const files: FileList = event.target.files;\r\n    if (files && files.length > 0) {\r\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'application/pdf', 'application/acad', 'application/x-autocad', 'image/vnd.dwg', 'image/x-dwg'];\r\n      const fileRegex = /pdf|jpg|jpeg|png|dwg|dxf/i;\r\n\r\n      // 如果圖面名稱為空且是第一次選擇檔案，自動填入第一個檔案的檔名（去除副檔名）\r\n      if (!this.formSpecialChange.CDrawingName && files.length > 0) {\r\n        const firstFile = files[0];\r\n        const fileName = firstFile.name;\r\n        const fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        this.formSpecialChange.CDrawingName = fileNameWithoutExtension;\r\n      }\r\n\r\n      for (let i = 0; i < files.length; i++) {\r\n        const file = files[i];\r\n        if (!fileRegex.test(file.name)) {\r\n          this.message.showErrorMSG('檔案格式錯誤，僅限pdf、圖片檔、CAD檔案（dwg, dxf）');\r\n          continue;\r\n        }\r\n        if (allowedTypes.includes(file.type) || fileRegex.test(file.name)) {\r\n          const reader = new FileReader();\r\n          reader.onload = (e: any) => {\r\n            // 判斷檔案類型\r\n            let fileType = 1; // 預設為其他\r\n            const fileName = file.name.toLowerCase();\r\n\r\n            if (fileName.match(/\\.(jpg|jpeg|png)$/)) {\r\n              fileType = 2; // 圖片\r\n            } else if (fileName.endsWith('.pdf')) {\r\n              fileType = 1; // PDF\r\n            } else if (fileName.match(/\\.(dwg|dxf)$/)) {\r\n              fileType = 3; // CAD\r\n            }\r\n\r\n            this.imageUrlList.push({\r\n              data: e.target.result,\r\n              CFileBlood: this.removeBase64Prefix(e.target.result),\r\n              CFileName: file.name,\r\n              CFileType: fileType\r\n            });\r\n\r\n            if (this.imageUrlList.length === files.length) {\r\n              console.log('this.imageUrlList', this.imageUrlList);\r\n              if (this.fileInput) {\r\n                this.fileInput.nativeElement.value = null;\r\n              }\r\n            }\r\n          };\r\n          reader.readAsDataURL(file);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  isPDFString(str: any): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\")\r\n    }\r\n    return false\r\n  }\r\n\r\n  isCadString(str: any): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false\r\n  }\r\n  isImage(fileType: number): boolean {\r\n    return fileType === 2;\r\n  }\r\n\r\n  isCad(fileType: number): boolean {\r\n    return fileType === 3;\r\n  }\r\n\r\n  isPdf(extension: string): boolean {\r\n    return extension.toLowerCase() === 'pdf';\r\n  }\r\n\r\n  listPictures: any[] = []\r\n\r\n  removeFile(index: number) {\r\n    this.imageUrlList.splice(index, 1);\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n  openNewTab(url: any) {\r\n    if (url) window.open(url, \"_blank\");\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AASxE,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,aAAa,QAAQ,qCAAqC;AAe5D,IAAMC,8BAA8B,GAApC,MAAMA,8BAA+B,SAAQD,aAAa;EAC/DE,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,qBAA2C,EAC3CC,aAA2B,EAE3BC,KAAqB,EACrBC,OAAuB,EACvBC,QAAkB,EAClBC,aAA2B;IACjC,KAAK,CAACR,MAAM,CAAC;IAVP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,aAAa,GAAbA,aAAa;IAEb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,aAAa,GAAiB,CAC5B;MACEC,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE;KACR,EACD;MACEF,KAAK,EAAE,CAAC;MACRC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;KACR,CACF;IAEQ,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IA2PzB,KAAAC,YAAY,GAAU,EAAE;EAhRN;EA8BTC,QAAQA,CAAA;IAEf,IAAI,CAACf,KAAK,CAACgB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QACjC,MAAMC,EAAE,GAAGF,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACG,WAAW,GAAGD,EAAE;QACrB,MAAME,QAAQ,GAAGL,MAAM,CAACE,GAAG,CAAC,KAAK,CAAC;QAClC,MAAMI,GAAG,GAAGD,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC;QACpC,IAAI,CAACE,OAAO,GAAGD,GAAG;QAClB,IAAI,CAACE,oBAAoB,EAAE;QAC3B,IAAI,CAACC,YAAY,EAAE;MACrB;IACF,CAAC,CAAC;EACJ;EAGAC,eAAeA,CAACC,IAAU;IACxB,IAAIA,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAACC,KAAK,EAAEC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAACC,QAAQ,CAACC,KAAK,EAAE,QAAQ,CAAC;EAC7E;EAKAL,oBAAoBA,CAAA;IAClB,IAAI,CAAC5B,qBAAqB,CAACoC,6CAA6C,CAAC;MACvEC,IAAI,EAAE;QACJC,QAAQ,EAAE,IAAI,CAACX,OAAO;QACtBY,SAAS,EAAE,IAAI,CAACzB,SAAS;QACzB0B,QAAQ,EAAE,IAAI,CAAC3B;;KAElB,CAAC,CAACM,SAAS,CAACsB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACC,iBAAiB,GAAGJ,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC3C,IAAI,CAAC5B,YAAY,GAAG0B,GAAG,CAACC,UAAU;MACpC;IACF,CAAC,CAAC;EACJ;EAEAb,YAAYA,CAAA;IACV,IAAI,CAAC5B,aAAa,CAAC6C,6BAA6B,CAAC;MAC/CT,IAAI,EAAE;QACJU,QAAQ,EAAE,IAAI,CAACpB;;KAElB,CAAC,CAACR,SAAS,CAACsB,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACI,KAAK,GAAGP,GAAG,CAACE,OAAO;QACxB,IAAI,CAACM,UAAU,GAAG,GAAG,IAAI,CAACD,KAAK,CAACE,UAAU,IAAI,IAAI,CAACF,KAAK,CAACG,MAAM,GAAG;MACpE;IACF,CAAC,CAAC;EACJ;EAIAC,oBAAoBA,CAACC,GAAQ,EAAEC,gBAAqB;IAClD,IAAI,CAACtD,qBAAqB,CAACuD,6CAA6C,CAAC;MAAElB,IAAI,EAAEiB;IAAgB,CAAE,CAAC,CAACnC,SAAS,CAACsB,GAAG,IAAG;MACnH,IAAIA,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,UAAU,IAAI,CAAC,EAAE;QACxD,IAAI,CAACY,aAAa,GAAGf,GAAG,CAACE,OAAO;QAChC,IAAI,CAACc,iBAAiB,GAAG;UACvBC,cAAc,EAAE,IAAI,CAACF,aAAa,CAACE,cAAc;UACjDC,YAAY,EAAE,IAAI,CAACnC,WAAW;UAC9BoC,YAAY,EAAE,IAAI,CAACJ,aAAa,CAACI,YAAY;UAC7Cb,QAAQ,EAAE,IAAI,CAACpB,OAAO;UACtBkC,kBAAkB,EAAE;SACrB;QACD,IAAI,IAAI,CAACL,aAAa,CAACM,WAAW,EAAE;UAClC,IAAI,CAACL,iBAAiB,CAACK,WAAW,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACP,aAAa,CAACM,WAAW,CAAC;QAC/E;QACA,IAAI,CAAChE,aAAa,CAACqC,IAAI,CAACkB,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAW,mBAAmBA,CAACX,GAAQ;IAC1B,IAAI,CAACY,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClE,KAAK,CAACmE,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAChE,OAAO,CAACiE,aAAa,CAAC,IAAI,CAACrE,KAAK,CAACmE,aAAa,CAAC;MACpD;IACF;IAEA,IAAI,CAAClE,qBAAqB,CAACqE,0CAA0C,CAAC;MAAEhC,IAAI,EAAE,IAAI,CAACiC,WAAW;IAAE,CAAE,CAAC,CAACnD,SAAS,CAACsB,GAAG,IAAG;MAClH,IAAIA,GAAG,CAACG,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzC,OAAO,CAACoE,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC3C,oBAAoB,EAAE;QAC3ByB,GAAG,CAACmB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,OAAe;IACzB,IAAI,CAAC5D,SAAS,GAAG4D,OAAO;IACxB,IAAI,CAAC9C,oBAAoB,EAAE;EAC7B;EAGA+C,MAAMA,CAACtB,GAAQ;IACb,IAAI,CAAC/C,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACkD,iBAAiB,GAAG;MACvBC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,IAAI,CAACnC,WAAW;MAC9BsC,WAAW,EAAE,EAAE;MACfF,YAAY,EAAE,EAAE;MAChBb,QAAQ,EAAE,IAAI,CAACpB,OAAO;MACtBkC,kBAAkB,EAAE;KACrB;IACD,IAAI,CAAC/D,aAAa,CAACqC,IAAI,CAACkB,GAAG,CAAC;EAC9B;EAEAuB,MAAMA,CAACvB,GAAQ,EAAEwB,aAAkB;IACjC,IAAI,CAACvE,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC6C,oBAAoB,CAACC,GAAG,EAAEwB,aAAa,CAACvB,gBAAgB,CAAC;EAChE;EAEAW,UAAUA,CAAA;IACR,IAAI,CAAClE,KAAK,CAAC+E,KAAK,EAAE;IAClB,IAAI,CAAC/E,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACK,WAAW,CAAC;IACjE,IAAI,CAAC/D,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACG,YAAY,CAAC;IAClE,IAAI,CAAC7D,KAAK,CAACgF,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtB,iBAAiB,CAACC,cAAc,CAAC;EACtE;EAEAsB,UAAUA,CAAClB,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOrE,MAAM,CAACqE,WAAW,CAAC,CAACmB,MAAM,CAAC,YAAY,CAAC;IACjD;IACA,OAAO,EAAE;EACX;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,KAAK,MAAMC,IAAI,IAAID,KAAK,EAAE;MACxB,OAAOC,IAAI,CAACrD,IAAI;IAClB;IACA,OAAOoD,KAAK;EACd;EAEAb,WAAWA,CAAA;IACT,MAAMe,MAAM,GAAG;MACb,GAAG,IAAI,CAAC5B,iBAAiB;MACzBI,kBAAkB,EAAE,IAAI,CAACvD;KAC1B;IACD,IAAI,CAAC4E,gBAAgB,CAACG,MAAM,CAACxB,kBAAkB,CAAC;IAEhD,IAAI,IAAI,CAACJ,iBAAiB,CAACK,WAAW,EAAE;MACtCuB,MAAM,CAACvB,WAAW,GAAG,IAAI,CAACkB,UAAU,CAAC,IAAI,CAACvB,iBAAiB,CAACK,WAAW,CAAC;IAC1E;IACA,OAAOuB,MAAM;EACf;EAEAC,OAAOA,CAACjC,GAAQ;IACdA,GAAG,CAACmB,KAAK,EAAE;EACb;EAEAe,kBAAkBA,CAACC,YAAiB;IAClC,MAAMC,WAAW,GAAGD,YAAY,CAACE,OAAO,CAAC,GAAG,CAAC;IAC7C,IAAID,WAAW,KAAK,CAAC,CAAC,EAAE;MACtB,OAAOD,YAAY,CAACG,SAAS,CAACF,WAAW,GAAG,CAAC,CAAC;IAChD;IACA,OAAOD,YAAY;EACrB;EAAEI,WAAWA,CAACC,KAAU;IACtB,MAAMC,KAAK,GAAaD,KAAK,CAACE,MAAM,CAACD,KAAK;IAC1C,IAAIA,KAAK,IAAIA,KAAK,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM6B,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,CAAC;MAChJ,MAAMC,SAAS,GAAG,2BAA2B;MAE7C;MACA,IAAI,CAAC,IAAI,CAACxC,iBAAiB,CAACG,YAAY,IAAIkC,KAAK,CAAC3B,MAAM,GAAG,CAAC,EAAE;QAC5D,MAAM+B,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;QAC1B,MAAMK,QAAQ,GAAGD,SAAS,CAACE,IAAI;QAC/B,MAAMC,wBAAwB,GAAGF,QAAQ,CAACR,SAAS,CAAC,CAAC,EAAEQ,QAAQ,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIH,QAAQ;QAC7F,IAAI,CAAC1C,iBAAiB,CAACG,YAAY,GAAGyC,wBAAwB;MAChE;MAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAAC3B,MAAM,EAAEoC,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGV,KAAK,CAACS,CAAC,CAAC;QACrB,IAAI,CAACN,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UAC9B,IAAI,CAACjG,OAAO,CAACuG,YAAY,CAAC,kCAAkC,CAAC;UAC7D;QACF;QACA,IAAIV,YAAY,CAACW,QAAQ,CAACH,IAAI,CAACI,IAAI,CAAC,IAAIX,SAAS,CAACQ,IAAI,CAACD,IAAI,CAACJ,IAAI,CAAC,EAAE;UACjE,MAAMS,MAAM,GAAG,IAAIC,UAAU,EAAE;UAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;YACzB;YACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAClB,MAAMd,QAAQ,GAAGK,IAAI,CAACJ,IAAI,CAACc,WAAW,EAAE;YAExC,IAAIf,QAAQ,CAACgB,KAAK,CAAC,mBAAmB,CAAC,EAAE;cACvCF,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAId,QAAQ,CAACiB,QAAQ,CAAC,MAAM,CAAC,EAAE;cACpCH,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB,CAAC,MAAM,IAAId,QAAQ,CAACgB,KAAK,CAAC,cAAc,CAAC,EAAE;cACzCF,QAAQ,GAAG,CAAC,CAAC,CAAC;YAChB;YAEA,IAAI,CAAC3G,YAAY,CAAC+G,IAAI,CAAC;cACrBtF,IAAI,EAAEiF,CAAC,CAACjB,MAAM,CAACV,MAAM;cACrBiC,UAAU,EAAE,IAAI,CAAC/B,kBAAkB,CAACyB,CAAC,CAACjB,MAAM,CAACV,MAAM,CAAC;cACpDkC,SAAS,EAAEf,IAAI,CAACJ,IAAI;cACpBoB,SAAS,EAAEP;aACZ,CAAC;YAEF,IAAI,IAAI,CAAC3G,YAAY,CAAC6D,MAAM,KAAK2B,KAAK,CAAC3B,MAAM,EAAE;cAC7CsD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACpH,YAAY,CAAC;cACnD,IAAI,IAAI,CAACqH,SAAS,EAAE;gBAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACnH,KAAK,GAAG,IAAI;cAC3C;YACF;UACF,CAAC;UACDoG,MAAM,CAACgB,aAAa,CAACrB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;EAEAsB,WAAWA,CAACC,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACb,WAAW,EAAE,CAACE,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEAY,WAAWA,CAACD,GAAQ;IAClB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAACb,WAAW,EAAE;MAClC,OAAOe,QAAQ,CAACb,QAAQ,CAAC,MAAM,CAAC,IAAIa,QAAQ,CAACb,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACAc,OAAOA,CAACjB,QAAgB;IACtB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAkB,KAAKA,CAAClB,QAAgB;IACpB,OAAOA,QAAQ,KAAK,CAAC;EACvB;EAEAmB,KAAKA,CAACC,SAAiB;IACrB,OAAOA,SAAS,CAACnB,WAAW,EAAE,KAAK,KAAK;EAC1C;EAIAoB,UAAUA,CAACC,KAAa;IACtB,IAAI,CAACjI,YAAY,CAACkI,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;EACpC;EAEAE,WAAWA,CAACC,SAAiB;IAC3B,IAAI,CAAC1H,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC2H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrH,EAAE,IAAImH,SAAS,CAAC;EACtE;EAEAG,WAAWA,CAACxF,GAAQ,GACpB;EAEAyF,UAAUA,CAACjD,KAAU,EAAE0C,KAAa;IAClC,IAAIQ,IAAI,GAAG,IAAI,CAAC/H,YAAY,CAACuH,KAAK,CAAC,CAACtG,KAAK,CAAC+G,KAAK,CAAC,CAAC,EAAE,IAAI,CAAChI,YAAY,CAACuH,KAAK,CAAC,CAACtG,KAAK,CAACgH,IAAI,EAAE,IAAI,CAACjI,YAAY,CAACuH,KAAK,CAAC,CAACtG,KAAK,CAAC2E,IAAI,CAAC;IAC5H,IAAIsC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,IAAI,CAAC,EAAE,GAAGlD,KAAK,CAACE,MAAM,CAACtF,KAAK,GAAG,GAAG,GAAG,IAAI,CAACO,YAAY,CAACuH,KAAK,CAAC,CAACF,SAAS,EAAE,EAAE;MAAEzB,IAAI,EAAE,IAAI,CAAC5F,YAAY,CAACuH,KAAK,CAAC,CAACtG,KAAK,CAAC2E;IAAI,CAAE,CAAC;IACjJ,IAAI,CAAC5F,YAAY,CAACuH,KAAK,CAAC,CAACtG,KAAK,GAAGiH,OAAO;EAC1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC/I,aAAa,CAACgH,IAAI,CAAC;MACtBgC,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC9H;KACf,CAAC;IACF,IAAI,CAACpB,QAAQ,CAACmJ,IAAI,EAAE;EACtB;EACAC,UAAUA,CAACC,GAAQ;IACjB,IAAIA,GAAG,EAAEvH,MAAM,CAACC,IAAI,CAACsH,GAAG,EAAE,QAAQ,CAAC;EACrC;CAED;AA5SyBC,UAAA,EAAvBlK,SAAS,CAAC,WAAW,CAAC,C,gEAAuB;AAdnCG,8BAA8B,GAAA+J,UAAA,EAN1CnK,SAAS,CAAC;EACToK,QAAQ,EAAE,6BAA6B;EACvCC,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EAEWlK,8BAA8B,CA0T1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}