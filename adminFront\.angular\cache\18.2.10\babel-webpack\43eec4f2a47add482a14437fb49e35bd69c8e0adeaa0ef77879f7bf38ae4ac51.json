{"ast": null, "code": "import { Page } from \"../page.model\";\nexport class ShareRequest extends Page {\n  constructor() {\n    super(...arguments);\n    this.CName = \"\";\n    this.CStatus = -1;\n    this.CUserName = \"\";\n    this.FunctionId = -1;\n    this.RefBaseTaskStatus = -1;\n  }\n}\nexport class ShareMenuButtonRequest {}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}