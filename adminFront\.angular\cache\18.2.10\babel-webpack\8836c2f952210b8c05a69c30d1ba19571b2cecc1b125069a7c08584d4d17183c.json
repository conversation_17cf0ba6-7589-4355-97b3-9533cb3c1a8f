{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    if (this.buildCaseId) {\n      // 使用API載入資料\n      this.loadBuildingDataFromApi();\n    } else {\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n        this.buildingData = this.generateMockData();\n      }\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with buildings:', this.buildings);\n      this.updateSelectedByBuilding();\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // 如果API載入失敗，使用mock資料作為備援\n        this.buildingData = this.generateMockData();\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋過濾\n    this.filteredHouseholds = households.map(h => h.code).filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n};\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseholds\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseholds", "selectionChange", "isOpen", "selectedBuilding", "searchTerm", "selectedHouseholds", "buildings", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "console", "log", "loadBuildingDataFromApi", "length", "generateMockData", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "code", "HouseName", "Building", "floor", "Floor", "houseId", "HouseId", "houseName", "isSelected", "grouped", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "codes", "Math", "parseInt", "slice", "onBuildingSelect", "onBuildingClick", "households", "filter", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "householdCode", "isHouseholdExcluded", "newSelection", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuilding", "onUnselectAllBuilding", "onClearAll", "selectedItems", "emit", "toggleDropdown", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseholds", "hasBuildingSelected", "__decorate", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseName?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedHouseholds: string[] = [];  buildings: string[] = [];\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n  \r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  }  ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  }\r\n\r\n  private initializeData() {\r\n    if (this.buildCaseId) {\r\n      // 使用API載入資料\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\r\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n        this.buildingData = this.generateMockData();\r\n      }\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with buildings:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // 如果API載入失敗，使用mock資料作為備援\r\n        this.buildingData = this.generateMockData();\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: HouseItem[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n    \r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋過濾\r\n    this.filteredHouseholds = households\r\n      .map(h => h.code)\r\n      .filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;AAmCjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAyBpCC,YACUC,GAAsB,EACtBC,kBAAsC;IADtC,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA1BnB,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIhB,YAAY,EAAmB;IAE/D,KAAAiB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,kBAAkB,GAAa,EAAE;IAAG,KAAAC,SAAS,GAAa,EAAE;IAC5D,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE;IACzD,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACN,kBAAkB,GAAGM,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACzB,QAAQ,GAAGyB,UAAU;EAC5B;EAAGC,QAAQA,CAAA;IACT,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC5B,WAAW,EAAE;MAC9C;MACA,IAAI,CAAC0B,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAACjB,SAAS,GAAGkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAC;MAC/C,IAAI,CAAC8B,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC5B,kBAAkB,CAAC;IACtE;EACF;EAEQqB,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAC1B,WAAW,EAAE;MACpB;MACA,IAAI,CAACkC,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACjC,YAAY,IAAI4B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAC,CAACkC,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAClC,YAAY,GAAG,IAAI,CAACmC,gBAAgB,EAAE;MAC7C;MACA,IAAI,CAACzB,SAAS,GAAGkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAC;MAC/C+B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACtB,SAAS,CAAC;MACpE,IAAI,CAACQ,wBAAwB,EAAE;IACjC;EACF;EAEQe,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAClC,WAAW,EAAE;IAEvB,IAAI,CAACc,SAAS,GAAG,IAAI;IACrB,IAAI,CAAClB,kBAAkB,CAACyC,WAAW,CAAC,IAAI,CAACrC,WAAW,CAAC,CAACsC,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAAC;QACtC,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACwC,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC/B,SAAS,GAAGkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAC;QAC/C,IAAI,CAACkB,wBAAwB,EAAE;QAC/B,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACnB,GAAG,CAACgD,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAC3C,YAAY,GAAG,IAAI,CAACmC,gBAAgB,EAAE;QAC3C,IAAI,CAACzB,SAAS,GAAGkB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7B,YAAY,CAAC;QAC/C,IAAI,CAACkB,wBAAwB,EAAE;QAC/B,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACnB,GAAG,CAACgD,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,gCAAgCA,CAACI,OAAuC;IAC9E,MAAM5C,YAAY,GAAiB,EAAE;IAErC4B,MAAM,CAACgB,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrD/C,YAAY,CAAC8C,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,IAAI,EAAED,KAAK,CAACE,SAAS;QACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;QACxBC,KAAK,EAAEJ,KAAK,CAACK,KAAK;QAClBC,OAAO,EAAEN,KAAK,CAACO,OAAO;QACtBC,SAAS,EAAER,KAAK,CAACE,SAAS;QAC1BO,UAAU,EAAE,KAAK;QACjBnC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOvB,YAAY;EACrB;EAEQkB,wBAAwBA,CAAA;IAC9B,MAAMyC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAClD,kBAAkB,CAACoC,OAAO,CAACK,IAAI,IAAG;MACrC,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpC,SAAS,EAAE;QACrC,MAAMkD,IAAI,GAAG,IAAI,CAAC5D,YAAY,CAAC8C,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACb,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACtC,kBAAkB,GAAG+C,OAAO;EACnC;EACQxB,gBAAgBA,CAAA;IACtB;IACA,MAAM6B,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEhC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEhC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEhC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEhC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEhC,MAAM,EAAE;MAAE,CAAE,EAAE,CAACiC,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAMtE,YAAY,GAAiB,EAAE;IACrC4B,MAAM,CAACgB,OAAO,CAACoB,cAAc,CAAC,CAACnB,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEyB,KAAK,CAAC,KAAI;MAC3DvE,YAAY,CAAC8C,QAAQ,CAAC,GAAGyB,KAAK,CAACvB,GAAG,CAACE,IAAI,KAAK;QAC1CA,IAAI;QACJJ,QAAQ;QACRO,KAAK,EAAE,GAAGmB,IAAI,CAACnB,KAAK,CAAC,CAACoB,QAAQ,CAACvB,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DhB,UAAU,EAAE,KAAK;QACjBnC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOvB,YAAY;EACrB;EAAE2E,gBAAgBA,CAAC7B,QAAgB;IACjCf,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEc,QAAQ,CAAC;IAC3C,IAAI,CAACvC,gBAAgB,GAAGuC,QAAQ;IAChC,IAAI,CAACtC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACsB,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACrB,kBAAkB,CAACuB,MAAM,CAAC;IACzE;IACA,IAAI,CAACxC,GAAG,CAACgD,aAAa,EAAE;EAC1B;EAEAkC,eAAeA,CAAC9B,QAAgB;IAC9Bf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEc,QAAQ,CAAC;EACxD;EAAEhB,wBAAwBA,CAAA;IACxBC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACzB,gBAAgB,CAAC;IAChF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAACI,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMkE,UAAU,GAAG,IAAI,CAAC7E,YAAY,CAAC,IAAI,CAACO,gBAAgB,CAAC,IAAI,EAAE;IACjEwB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE6C,UAAU,CAAC3C,MAAM,CAAC;IAEpE;IACA,IAAI,CAACvB,kBAAkB,GAAGkE,UAAU,CACjC7B,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,CAChB4B,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAAC6B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACxE,UAAU,CAACuE,WAAW,EAAE,CAAC,CAAC;IAE7EhD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrB,kBAAkB,CAACuB,MAAM,CAAC;EAC5E;EAEA+C,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC1E,UAAU,GAAG0E,KAAK,CAACC,MAAM,CAACpE,KAAK;IACpC,IAAI,CAACe,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACxB,UAAU,CAAC;EACtD;EAEA4E,WAAWA,CAAA;IACT,IAAI,CAAC5E,UAAU,GAAG,EAAE;IACpB,IAAI,CAACsB,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACAqD,iBAAiBA,CAACC,aAAqB;IACrC;IACA,IAAI,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC,EAAE;MAC3CvD,OAAO,CAACC,GAAG,CAAC,MAAMsD,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAM5B,UAAU,GAAG,IAAI,CAACjD,kBAAkB,CAACuE,QAAQ,CAACM,aAAa,CAAC;IAClE,IAAIE,YAAsB;IAE1B,IAAI9B,UAAU,EAAE;MACd8B,YAAY,GAAG,IAAI,CAAC/E,kBAAkB,CAACqE,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKwB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACzF,aAAa,IAAI,IAAI,CAACY,kBAAkB,CAACyB,MAAM,IAAI,IAAI,CAACrC,aAAa,EAAE;QAC9EkC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAwD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC/E,kBAAkB,EAAE6E,aAAa,CAAC;IAC5D;IAEA,IAAI,CAAC7E,kBAAkB,GAAG+E,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,iBAAiBA,CAACJ,aAAqB;IACrC,IAAI,CAAC7E,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqE,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKwB,aAAa,CAAC;IAClF,IAAI,CAACG,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACpF,gBAAgB,IAAI,IAAI,CAACI,kBAAkB,CAACuB,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAM0D,YAAY,GAAG,IAAI,CAACnF,kBAAkB,CAACyB,MAAM;IACnD,MAAM2D,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAACrF,kBAAkB,CAACmE,MAAM,CAAC5B,IAAI,IAC5D,CAAC,IAAI,CAACzC,kBAAkB,CAACuE,QAAQ,CAAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACqC,mBAAmB,CAACrC,IAAI,CAAC,CAC3E;IAED;IACA,MAAM+C,KAAK,GAAGD,kBAAkB,CAACtB,KAAK,CAAC,CAAC,EAAEqB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/D,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACzB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAGwF,KAAK,CAAC;MAChE,IAAI,CAACR,WAAW,EAAE;MAClB1D,OAAO,CAACC,GAAG,CAAC,aAAaiE,KAAK,CAAC/D,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAgE,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC3F,gBAAgB,EAAE;IAE5B;IACA,MAAM4F,kBAAkB,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACO,gBAAgB,CAAC,EAAEyC,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAE3F;IACA,MAAM0C,YAAY,GAAG,IAAI,CAACnF,kBAAkB,CAACyB,MAAM;IACnD,MAAM2D,UAAU,GAAG,IAAI,CAAChG,aAAa,IAAIiG,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMQ,kBAAkB,GAAGD,kBAAkB,CAACrB,MAAM,CAAC5B,IAAI,IACvD,CAAC,IAAI,CAACzC,kBAAkB,CAACuE,QAAQ,CAAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACqC,mBAAmB,CAACrC,IAAI,CAAC,CAC3E;IAED;IACA,MAAM+C,KAAK,GAAGG,kBAAkB,CAAC1B,KAAK,CAAC,CAAC,EAAEqB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/D,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACzB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAGwF,KAAK,CAAC;MAChE,IAAI,CAACR,WAAW,EAAE;MAClB1D,OAAO,CAACC,GAAG,CAAC,aAAaiE,KAAK,CAAC/D,MAAM,MAAM,CAAC;IAC9C;EACF;EACAmE,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9F,gBAAgB,EAAE;IAE5B,MAAM4F,kBAAkB,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACO,gBAAgB,CAAC,EAAEyC,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAACzC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqE,MAAM,CAAChB,CAAC,IAAI,CAACqC,kBAAkB,CAACnB,QAAQ,CAAClB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAAC2B,WAAW,EAAE;EACpB;EACAa,UAAUA,CAAA;IACR,IAAI,CAAC7F,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACgF,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACvE,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACL,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACO,SAAS,EAAE;IAEhB,MAAMuF,aAAa,GAAG,IAAI,CAAC9F,kBAAkB,CAACuC,GAAG,CAACE,IAAI,IAAG;MACvD,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpC,SAAS,EAAE;QACrC,MAAMkD,IAAI,GAAG,IAAI,CAAC5D,YAAY,CAAC8C,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIU,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACkB,MAAM,CAAClB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAACvD,eAAe,CAACmG,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC3G,QAAQ,EAAE;MAClB,IAAI,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1ByB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC1B,MAAM,CAAC;MACrDyB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtB,SAAS,CAAC;IACrD;EACF;EAEAgG,aAAaA,CAAA;IACX,IAAI,CAACpG,MAAM,GAAG,KAAK;EACrB;EAEAqG,mBAAmBA,CAACrB,aAAqB;IACvC,OAAO,IAAI,CAAC7E,kBAAkB,CAACuE,QAAQ,CAACM,aAAa,CAAC;EACxD;EAEAC,mBAAmBA,CAACD,aAAqB;IACvC,OAAO,IAAI,CAAClF,kBAAkB,CAAC4E,QAAQ,CAACM,aAAa,CAAC;EACxD;EAEAsB,mBAAmBA,CAACtB,aAAqB;IACvC,OAAO,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACuB,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAACrB,aAAa,CAAE;EACvE;EAEAuB,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAChH,aAAa,IAAI,IAAI,CAACY,kBAAkB,CAACyB,MAAM,GAAG,IAAI,CAACrC,aAAa;EACnF;EAEAiH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACvG,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM4F,kBAAkB,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACO,gBAAgB,CAAC,CAChEuE,MAAM,CAAChB,CAAC,IAAI,CAACA,CAAC,CAACvC,UAAU,CAAC,CAC1ByB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC;IACnB,OAAOiD,kBAAkB,CAACjE,MAAM,GAAG,CAAC,IAClCiE,kBAAkB,CAACY,KAAK,CAAC7D,IAAI,IAAI,IAAI,CAACzC,kBAAkB,CAACuE,QAAQ,CAAC9B,IAAI,CAAC,CAAC;EAC5E;EACA8D,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzG,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAM4F,kBAAkB,GAAG,IAAI,CAACnG,YAAY,CAAC,IAAI,CAACO,gBAAgB,CAAC,EAAEyC,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAOiD,kBAAkB,CAACc,IAAI,CAAC/D,IAAI,IAAI,IAAI,CAACzC,kBAAkB,CAACuE,QAAQ,CAAC9B,IAAI,CAAC,CAAC;EAChF;EACAgE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACtG,kBAAkB;EAChC;EAEAuG,gBAAgBA,CAACrE,QAAgB;IAC/B,OAAO,IAAI,CAAC9C,YAAY,CAAC8C,QAAQ,CAAC,EAAEZ,MAAM,IAAI,CAAC;EACjD;EAEAkF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC3G,kBAAkB,CAACyB,MAAM;EACvC;EAEA;EACAmF,6BAA6BA,CAACvE,QAAgB;IAC5C,OAAO,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAwE,mBAAmBA,CAACxE,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,IAAI,IAAI,CAAClC,kBAAkB,CAACkC,QAAQ,CAAC,CAACZ,MAAM,GAAG,CAAC,CAAC;EAC9F;CACD;AA3WUqF,UAAA,EAARpI,KAAK,EAAE,C,6DAA+B;AAC9BoI,UAAA,EAARpI,KAAK,EAAE,C,+DAAqC;AACpCoI,UAAA,EAARpI,KAAK,EAAE,C,0DAA2B;AAC1BoI,UAAA,EAARpI,KAAK,EAAE,C,6DAAmC;AAClCoI,UAAA,EAARpI,KAAK,EAAE,C,8DAAiC;AAChCoI,UAAA,EAARpI,KAAK,EAAE,C,kEAAkC;AACjCoI,UAAA,EAARpI,KAAK,EAAE,C,6DAA6B;AAC5BoI,UAAA,EAARpI,KAAK,EAAE,C,kEAAkC;AACjCoI,UAAA,EAARpI,KAAK,EAAE,C,oEAAmC;AAEjCoI,UAAA,EAATnI,MAAM,EAAE,C,iEAAuD;AAXrDI,yBAAyB,GAAA+H,UAAA,EAZrCrI,SAAS,CAAC;EACTsI,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAErI,iBAAiB;IAC1BsI,WAAW,EAAEvI,UAAU,CAAC,MAAME,yBAAyB,CAAC;IACxDsI,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWtI,yBAAyB,CA4WrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}