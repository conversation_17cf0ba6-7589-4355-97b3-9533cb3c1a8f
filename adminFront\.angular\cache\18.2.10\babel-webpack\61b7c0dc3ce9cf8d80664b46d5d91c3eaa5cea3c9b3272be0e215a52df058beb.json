{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport addDays from \"../addDays/index.js\";\nimport getISODay from \"../getISODay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday etc.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} day - the day of the ISO week of the new date\n * @returns {Date} the new date with the day of the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport default function setISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = getISODay(date);\n  var diff = day - currentDay;\n  return addDays(date, diff);\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "addDays", "getISODay", "requiredArgs", "setISODay", "dirtyDate", "dirtyDay", "arguments", "date", "day", "currentDay", "diff"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/date-fns/esm/setISODay/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport addDays from \"../addDays/index.js\";\nimport getISODay from \"../getISODay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday etc.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} day - the day of the ISO week of the new date\n * @returns {Date} the new date with the day of the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport default function setISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = getISODay(date);\n  var diff = day - currentDay;\n  return addDays(date, diff);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACrDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACK,SAAS,CAAC;EAC5B,IAAII,GAAG,GAAGV,SAAS,CAACO,QAAQ,CAAC;EAC7B,IAAII,UAAU,GAAGR,SAAS,CAACM,IAAI,CAAC;EAChC,IAAIG,IAAI,GAAGF,GAAG,GAAGC,UAAU;EAC3B,OAAOT,OAAO,CAACO,IAAI,EAAEG,IAAI,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}