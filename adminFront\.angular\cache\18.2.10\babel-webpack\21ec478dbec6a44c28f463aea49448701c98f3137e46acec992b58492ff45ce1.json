{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/helper/validationHelper\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/services/utility.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i10 from \"../../components/pagination/pagination.component\";\nimport * as i11 from \"../../../@theme/pipes/base-file.pipe\";\nfunction PictureMaterialComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCase_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCase_r2.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCase_r2.CBuildCaseName, \" \");\n  }\n}\nfunction PictureMaterialComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(40);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5));\n    });\n    i0.ɵɵtext(1, \" \\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_36_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_36_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(40);\n      return i0.ɵɵresetView(ctx_r3.addNew(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_36_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_tr_36_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      const dialog_r5 = i0.ɵɵreference(40);\n      return i0.ɵɵresetView(ctx_r3.changePicture(dialog_r5, item_r7));\n    });\n    i0.ɵɵtext(1, \"\\u6539\\u8B8A\\u5716\\u7247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_tr_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 22);\n    i0.ɵɵtemplate(17, PictureMaterialComponent_tr_36_button_17_Template, 2, 0, \"button\", 23)(18, PictureMaterialComponent_tr_36_button_18_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r7 == null ? null : item_r7.CName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPart);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CLocation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CSelectName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r7.CPictureCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 9, item_r7 == null ? null : item_r7.CUpdateDT, \"yyyy/MM/dd HH:mm:ss\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isRead);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 32);\n    i0.ɵɵpipe(2, \"addBaseFile\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 1, ctx_r3.currentImageShowing), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"input\", 38);\n    i0.ɵɵlistener(\"blur\", function PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_input_blur_2_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\", 39);\n    i0.ɵɵelement(4, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 22)(6, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_button_click_6_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id));\n    });\n    i0.ɵɵtext(7, \"\\u5220\\u9664\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const picture_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const inputFile_r11 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵtext(1, \"\\u5716\\u7247\\u4E0A\\u50B3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"input\", 34, 2);\n    i0.ɵɵlistener(\"change\", function PictureMaterialComponent_ng_template_39_ng_template_6_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"table\", 36)(6, \"thead\")(7, \"tr\", 15)(8, \"th\", 37);\n    i0.ɵɵtext(9, \"\\u6587\\u4EF6\\u540D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 16);\n    i0.ɵɵtext(11, \"\\u6AA2\\u8996\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"th\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"tbody\");\n    i0.ɵɵtemplate(14, PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template, 8, 2, \"tr\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(!ctx_r3.isEdit ? \"btn btn-info\" : ctx_r3.listPictures.length < 1 ? \"btn btn-info\" : \"btn btn-info disable\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listPictures);\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ref_r15 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.uploadImage(ref_r15));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PictureMaterialComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 25)(1, \"nb-card-header\")(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 26);\n    i0.ɵɵtemplate(5, PictureMaterialComponent_ng_template_39_div_5_Template, 3, 3, \"div\", 27)(6, PictureMaterialComponent_ng_template_39_ng_template_6_Template, 15, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nb-card-footer\")(9, \"div\", 28)(10, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function PictureMaterialComponent_ng_template_39_Template_button_click_10_listener() {\n      const ref_r15 = i0.ɵɵrestoreView(_r9).dialogRef;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ref_r15.close();\n      return i0.ɵɵresetView(ctx_r3.currentImageShowing = \"\");\n    });\n    i0.ɵɵtext(11, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, PictureMaterialComponent_ng_template_39_button_12_Template, 2, 0, \"button\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const upload_r17 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.currentImageShowing ? \"\\u6AA2\\u8996\" : \"\\u5716\\u7247\\u4E0A\\u50B3\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentImageShowing)(\"ngIfElse\", upload_r17);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.currentImageShowing);\n  }\n}\nexport class PictureMaterialComponent extends BaseComponent {\n  constructor(_allow, dialogService, valid, _pictureService, _infoPictureService, _buildCaseService, message, _utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.valid = valid;\n    this._pictureService = _pictureService;\n    this._infoPictureService = _infoPictureService;\n    this._buildCaseService = _buildCaseService;\n    this.message = message;\n    this._utilityService = _utilityService;\n    this.images = [];\n    this.listUserBuildCases = [];\n    this.selectedCategory = 'building-material'; // 'building-material' or 'schematic'\n    this.currentImageShowing = \"\";\n    this.listPictures = [];\n    this.isEdit = false;\n    // 類別選項\n    this.categoryOptions = [{\n      value: 'building-material',\n      label: '建材圖片'\n    }, {\n      value: 'schematic',\n      label: '示意圖片'\n    }];\n  }\n  ngOnInit() {\n    this.getListBuildCase();\n  }\n  getListBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.listUserBuildCases = res.Entries ?? [];\n        this.selectedBuildCaseId = this.listUserBuildCases[0].cID;\n      }\n    }), concatMap(() => this.getPicturelList(1))).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this._utilityService.openFileInNewTab(CFileUrl);\n    }\n  }\n  getPicturelList(pageIndex) {\n    if (this.selectedCategory === 'building-material') {\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    } else {\n      return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\n        body: {\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CBuildCaseId: this.selectedBuildCaseId\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.images = res.Entries ?? [];\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n  }\n  pageChanged(pageIndex) {\n    // this.pageIndex = newPage;\n    this.getPicturelList(pageIndex).subscribe();\n  }\n  selectedChange(buildCaseId) {\n    this.selectedBuildCaseId = buildCaseId;\n    this.getPicturelList(1).subscribe();\n  }\n  categoryChanged(category) {\n    this.selectedCategory = category;\n    this.getPicturelList(1).subscribe();\n  }\n  addNew(ref, item) {\n    this.listPictures = [];\n    this.dialogService.open(ref);\n    this.isEdit = false;\n    if (!!item) {\n      this.currentImageShowing = item.CFile;\n    } else {\n      this.listPictures = [];\n    }\n  }\n  changePicture(ref, item) {\n    if (!!item && item.CId) {\n      this.dialogService.open(ref);\n      this.isEdit = true;\n      this.currentEditItem = item.CId;\n      this.listPictures = [];\n    }\n  }\n  validation(CFile) {\n    this.valid.clear();\n    const nameSet = new Set();\n    for (const item of CFile) {\n      if (nameSet.has(item.name)) {\n        this.valid.addErrorMessage('檔名不可重複');\n        return;\n      }\n      nameSet.add(item.name);\n    }\n  }\n  onSubmit(ref) {}\n  detectFiles(event) {\n    for (let index = 0; index < event.target.files.length; index++) {\n      const file = event.target.files[index];\n      if (file) {\n        let reader = new FileReader();\n        reader.readAsDataURL(file);\n        reader.onload = () => {\n          let base64Str = reader.result;\n          if (!base64Str) {\n            return;\n          }\n          // Get name file ( no extension)\n          const fileNameWithoutExtension = file.name.split('.')[0];\n          // Find files with duplicate names\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\n          if (existingFileIndex !== -1) {\n            // If name is duplicate, update file data\n            this.listPictures[existingFileIndex] = {\n              ...this.listPictures[existingFileIndex],\n              data: base64Str,\n              CFile: file,\n              extension: this._utilityService.getFileExtension(file.name)\n            };\n          } else {\n            // If not duplicate, add new file\n            file.id = new Date().getTime();\n            this.listPictures.push({\n              id: new Date().getTime(),\n              name: fileNameWithoutExtension,\n              data: base64Str,\n              extension: this._utilityService.getFileExtension(file.name),\n              CFile: file\n            });\n          }\n          // Reset input file to be able to select the old file again\n          event.target.value = null;\n        };\n      }\n    }\n  }\n  removeImage(pictureId) {\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId);\n  }\n  uploadImage(ref) {\n    if (!this.isEdit) {\n      const CFile = this.listPictures.map(x => x.CFile);\n      this.validation(CFile);\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._pictureService.apiPictureUploadListPicturePost$Json({\n        body: {\n          CBuildCaseId: this.selectedBuildCaseId,\n          CPath: \"picture\",\n          CFile: CFile\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG('執行成功');\n          this.listPictures = [];\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        ref.close();\n      }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n    } else {\n      if (this.listPictures.length > 0 && this.listPictures[0].CFile) {\n        this._pictureService.apiPictureUpdatePicturePost$Json({\n          body: {\n            CBuildCaseID: this.selectedBuildCaseId,\n            CPictureID: this.currentEditItem,\n            CFile: this.listPictures[0].CFile\n          }\n        }).pipe(tap(res => {\n          if (res.StatusCode == 0) {\n            this.message.showSucessMSG('執行成功');\n            this.listPictures = [];\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n          ref.close();\n        }), concatMap(res => res.StatusCode == 0 ? this.getPicturelList(1) : of(null))).subscribe();\n      }\n    }\n  }\n  renameFile(event, index) {\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, {\n      type: this.listPictures[index].CFile.type\n    });\n    this.listPictures[index].CFile = newFile;\n  }\n  static {\n    this.ɵfac = function PictureMaterialComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PictureMaterialComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.ValidationHelper), i0.ɵɵdirectiveInject(i4.PictureService), i0.ɵɵdirectiveInject(i4.InfoPictureService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PictureMaterialComponent,\n      selectors: [[\"ngx-picture-material\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 41,\n      vars: 7,\n      consts: [[\"dialog\", \"\"], [\"upload\", \"\"], [\"inputFile\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"mr-2\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"w-full\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"CollectionSizeChange\", \"PageSizeChange\", \"PageChange\", \"CollectionSize\", \"PageSize\", \"Page\"], [3, \"value\"], [1, \"btn\", \"btn-info\", 3, \"click\"], [1, \"text-center\", \"w-32\"], [\"class\", \"btn btn-outline-primary btn-sm m-1\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"width\", \"700px\"], [2, \"padding\", \"1rem 2rem\"], [\"class\", \"w-full h-auto\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"justify-center\", \"items-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"class\", \"btn btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"w-full\", \"h-auto\"], [1, \"fit-size\", 3, \"src\"], [3, \"click\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", \"multiple\", \"\", 1, \"hidden\", 3, \"change\"], [1, \"mt-3\", \"w-full\", \"flex\", \"flex-col\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [\"scope\", \"col\", 1, \"col-4\"], [\"nbInput\", \"\", \"type\", \"text\", 1, \"w-100\", \"p-2\", \"text-[13px]\", 3, \"blur\", \"value\"], [1, \"w-[100px]\", \"h-auto\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"m-1\", 3, \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"]],\n      template: function PictureMaterialComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 3)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"\\u53EF\\u8A2D\\u5B9A\\u4E0A\\u50B3\\u5EFA\\u6750\\u793A\\u610F\\u5716\\u7247\\uFF0C\\u4E0A\\u50B3\\u524D\\u8ACB\\u5C07\\u5716\\u7247\\u6A94\\u6848\\u6539\\u70BA\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"label\", 8);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 9);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuildCaseId, $event) || (ctx.selectedBuildCaseId = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function PictureMaterialComponent_Template_nb_select_selectedChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectedChange($event));\n          });\n          i0.ɵɵtemplate(12, PictureMaterialComponent_nb_option_12_Template, 2, 2, \"nb-option\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 11);\n          i0.ɵɵtemplate(15, PictureMaterialComponent_button_15_Template, 2, 0, \"button\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"table\", 14)(18, \"thead\")(19, \"tr\", 15)(20, \"th\", 16);\n          i0.ɵɵtext(21, \"\\u9805\\u6B21\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"th\", 16);\n          i0.ɵɵtext(23, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"th\", 16);\n          i0.ɵɵtext(25, \"\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"th\", 16);\n          i0.ɵɵtext(27, \"\\u4F4D\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"th\", 16);\n          i0.ɵɵtext(29, \"\\u5EFA\\u6750\\u9078\\u9805\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"th\", 16);\n          i0.ɵɵtext(31, \"\\u5EFA\\u6750\\u5716\\u7247\\u6A94\\u540D\\uFF08\\u76F8\\u540C\\u5EFA\\u6848\\u4E0D\\u53EF\\u91CD\\u8907\\uFF09\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"th\", 16);\n          i0.ɵɵtext(33, \"\\u6700\\u65B0\\u5716\\u7247\\u4E0A\\u50B3\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"th\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"tbody\");\n          i0.ɵɵtemplate(36, PictureMaterialComponent_tr_36_Template, 19, 12, \"tr\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"nb-card-footer\", 18)(38, \"ngx-pagination\", 19);\n          i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageSizeChange\", function PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n            return i0.ɵɵresetView($event);\n          })(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(39, PictureMaterialComponent_ng_template_39_Template, 13, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuildCaseId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.listUserBuildCases);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"ngForOf\", ctx.images);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"PageSize\", ctx.pageSize)(\"Page\", ctx.pageIndex);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.DatePipe, SharedModule, i8.NgControlStatus, i8.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i9.BreadcrumbComponent, i10.PaginationComponent, i11.BaseFilePipe],\n      styles: [\".disable[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  pointer-events: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBpY3R1cmUtbWF0ZXJpYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxZQUFBO0VBQ0Esb0JBQUE7QUFDRiIsImZpbGUiOiJwaWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmRpc2FibGV7XHJcbiAgb3BhY2l0eTogLjU7XHJcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XHJcbn1cclxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvc2VsZWN0aW9uLW1hbmFnZW1lbnQvcGljdHVyZS1tYXRlcmlhbC9waWN0dXJlLW1hdGVyaWFsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsWUFBQTtFQUNBLG9CQUFBO0FBQ0Y7QUFDQSxvWEFBb1giLCJzb3VyY2VzQ29udGVudCI6WyIuZGlzYWJsZXtcclxuICBvcGFjaXR5OiAuNTtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "concatMap", "of", "tap", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "buildCase_r2", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵlistener", "PictureMaterialComponent_button_15_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "dialog_r5", "ɵɵreference", "ɵɵresetView", "addNew", "PictureMaterialComponent_tr_36_button_17_Template_button_click_0_listener", "_r6", "item_r7", "$implicit", "PictureMaterialComponent_tr_36_button_18_Template_button_click_0_listener", "_r8", "changePicture", "ɵɵtemplate", "PictureMaterialComponent_tr_36_button_17_Template", "PictureMaterialComponent_tr_36_button_18_Template", "ɵɵtextInterpolate", "CId", "CName", "<PERSON>art", "CLocation", "CSelectName", "CPictureCode", "ɵɵpipeBind2", "CUpdateDT", "isRead", "ɵɵelement", "ɵɵpipeBind1", "currentImageShowing", "ɵɵsanitizeUrl", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_input_blur_2_listener", "$event", "i_r13", "_r12", "index", "renameFile", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template_button_click_6_listener", "picture_r14", "removeImage", "id", "name", "data", "PictureMaterialComponent_ng_template_39_ng_template_6_Template_button_click_0_listener", "_r10", "inputFile_r11", "click", "PictureMaterialComponent_ng_template_39_ng_template_6_Template_input_change_2_listener", "detectFiles", "PictureMaterialComponent_ng_template_39_ng_template_6_tr_14_Template", "ɵɵclassMap", "isEdit", "listPictures", "length", "PictureMaterialComponent_ng_template_39_button_12_Template_button_click_0_listener", "_r16", "ref_r15", "dialogRef", "uploadImage", "PictureMaterialComponent_ng_template_39_div_5_Template", "PictureMaterialComponent_ng_template_39_ng_template_6_Template", "ɵɵtemplateRefExtractor", "PictureMaterialComponent_ng_template_39_Template_button_click_10_listener", "_r9", "close", "PictureMaterialComponent_ng_template_39_button_12_Template", "upload_r17", "PictureMaterialComponent", "constructor", "_allow", "dialogService", "valid", "_pictureService", "_infoPictureService", "_buildCaseService", "message", "_utilityService", "images", "listUserBuildCases", "selectedCate<PERSON><PERSON>", "categoryOptions", "value", "label", "ngOnInit", "getListBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "selectedBuildCaseId", "getPicturelList", "subscribe", "openPdfInNewTab", "CFileUrl", "openFileInNewTab", "pageIndex", "apiPictureGetPicturelListPost$Json", "body", "PageIndex", "PageSize", "pageSize", "CBuildCaseId", "totalRecords", "TotalItems", "apiInfoPictureGetInfoPicturelListPost$Json", "pageChanged", "<PERSON><PERSON><PERSON><PERSON>", "buildCaseId", "categoryChanged", "category", "ref", "item", "open", "CFile", "currentEditItem", "validation", "clear", "nameSet", "Set", "has", "addErrorMessage", "add", "onSubmit", "event", "target", "files", "file", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "fileNameWithoutExtension", "split", "existingFileIndex", "findIndex", "picture", "extension", "getFileExtension", "Date", "getTime", "push", "pictureId", "filter", "x", "map", "errorMessages", "showErrorMSGs", "apiPictureUploadListPicturePost$Json", "CPath", "showSucessMSG", "showErrorMSG", "Message", "apiPictureUpdatePicturePost$Json", "CBuildCaseID", "CPictureID", "blob", "slice", "size", "type", "newFile", "File", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "ValidationHelper", "i4", "PictureService", "InfoPictureService", "BuildCaseService", "i5", "MessageService", "i6", "UtilityService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PictureMaterialComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "PictureMaterialComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ɵɵtwoWayBindingSet", "PictureMaterialComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "PictureMaterialComponent_nb_option_12_Template", "PictureMaterialComponent_button_15_Template", "PictureMaterialComponent_tr_36_Template", "PictureMaterialComponent_Template_ngx_pagination_CollectionSizeChange_38_listener", "PictureMaterialComponent_Template_ngx_pagination_PageSizeChange_38_listener", "PictureMaterialComponent_Template_ngx_pagination_PageChange_38_listener", "PictureMaterialComponent_ng_template_39_Template", "ɵɵtwoWayProperty", "isCreate", "i7", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i8", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i9", "BreadcrumbComponent", "i10", "PaginationComponent", "i11", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\picture-material\\picture-material.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule, formatDate } from '@angular/common';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, PictureService, InfoPictureService } from 'src/services/api/services';\r\nimport { BuildCaseGetListReponse, GetPictureListResponse, GetInfoPictureListResponse } from 'src/services/api/models';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { concatMap, finalize, of, tap } from 'rxjs';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport * as JSZip from 'jszip';\r\n\r\n@Component({\r\n  selector: 'ngx-picture-material',\r\n  templateUrl: './picture-material.component.html',\r\n  styleUrls: ['./picture-material.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe\r\n  ],\r\n})\r\n\r\nexport class PictureMaterialComponent extends BaseComponent implements OnInit {\r\n\r\n  images: (GetPictureListResponse | GetInfoPictureListResponse)[] = [];\r\n  listUserBuildCases: BuildCaseGetListReponse[] = []\r\n\r\n  selectedBuildCaseId: number\r\n  selectedCategory: string = 'building-material' // 'building-material' or 'schematic'\r\n\r\n  currentImageShowing: string = \"\"\r\n\r\n  listPictures: any[] = []\r\n\r\n  isEdit: boolean = false;\r\n  currentEditItem: number;\r\n\r\n  // 類別選項\r\n  categoryOptions = [\r\n    { value: 'building-material', label: '建材圖片' },\r\n    { value: 'schematic', label: '示意圖片' }\r\n  ]\r\n\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private valid: ValidationHelper,\r\n    private _pictureService: PictureService,\r\n    private _infoPictureService: InfoPictureService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private message: MessageService,\r\n    private _utilityService: UtilityService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n\r\n  override ngOnInit(): void {\r\n    this.getListBuildCase();\r\n  }\r\n\r\n  getListBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({})\r\n      .pipe(\r\n        tap(res => {\r\n          if (res.StatusCode === 0) {\r\n            this.listUserBuildCases = res.Entries! ?? []\r\n            this.selectedBuildCaseId = this.listUserBuildCases[0].cID!\r\n          }\r\n        }),\r\n        concatMap(() => this.getPicturelList(1))\r\n      ).subscribe()\r\n  }\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this._utilityService.openFileInNewTab(CFileUrl)\r\n    }\r\n  }\r\n  getPicturelList(pageIndex: number) {\r\n    if (this.selectedCategory === 'building-material') {\r\n      return this._pictureService.apiPictureGetPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        })\r\n      )\r\n    } else {\r\n      return this._infoPictureService.apiInfoPictureGetInfoPicturelListPost$Json({\r\n        body: {\r\n          PageIndex: pageIndex,\r\n          PageSize: this.pageSize,\r\n          CBuildCaseId: this.selectedBuildCaseId\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.images = res.Entries! ?? []\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        })\r\n      )\r\n    }\r\n  }\r\n\r\n  pageChanged(pageIndex: number) {\r\n    // this.pageIndex = newPage;\r\n    this.getPicturelList(pageIndex).subscribe();\r\n  }\r\n  selectedChange(buildCaseId: number) {\r\n    this.selectedBuildCaseId = buildCaseId;\r\n    this.getPicturelList(1).subscribe();\r\n  }\r\n\r\n  categoryChanged(category: string) {\r\n    this.selectedCategory = category;\r\n    this.getPicturelList(1).subscribe();\r\n  }\r\n  addNew(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    this.listPictures = []\r\n    this.dialogService.open(ref)\r\n    this.isEdit = false;\r\n    if (!!item) {\r\n      this.currentImageShowing = item.CFile!\r\n    }\r\n    else{\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  changePicture(ref: any, item?: GetPictureListResponse | GetInfoPictureListResponse) {\r\n    if (!!item && item.CId) {\r\n      this.dialogService.open(ref)\r\n      this.isEdit = true;\r\n      this.currentEditItem = item.CId;\r\n      this.listPictures = [];\r\n    }\r\n  }\r\n\r\n  validation(CFile: any) {\r\n    this.valid.clear();\r\n    const nameSet = new Set();\r\n    for (const item of CFile) {\r\n      if (nameSet.has(item.name)) {\r\n        this.valid.addErrorMessage('檔名不可重複')\r\n        return;\r\n      }\r\n      nameSet.add(item.name);\r\n    }\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n  }\r\n\r\n  detectFiles(event: any) {\r\n    for (let index = 0; index < event.target.files.length; index++) {\r\n      const file = event.target.files[index];\r\n      if (file) {\r\n        let reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          let base64Str: string = reader.result as string;\r\n          if (!base64Str) {\r\n            return;\r\n          }\r\n          // Get name file ( no extension)\r\n          const fileNameWithoutExtension = file.name.split('.')[0];\r\n          // Find files with duplicate names\r\n          const existingFileIndex = this.listPictures.findIndex(picture => picture.name === fileNameWithoutExtension);\r\n          if (existingFileIndex !== -1) {\r\n            // If name is duplicate, update file data\r\n            this.listPictures[existingFileIndex] = {\r\n              ...this.listPictures[existingFileIndex],\r\n              data: base64Str,\r\n              CFile: file,\r\n              extension: this._utilityService.getFileExtension(file.name)\r\n            };\r\n          } else {\r\n            // If not duplicate, add new file\r\n            file.id = new Date().getTime();\r\n            this.listPictures.push({\r\n              id: new Date().getTime(),\r\n              name: fileNameWithoutExtension,\r\n              data: base64Str,\r\n              extension: this._utilityService.getFileExtension(file.name),\r\n              CFile: file\r\n            });\r\n          }\r\n          // Reset input file to be able to select the old file again\r\n          event.target.value = null;\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number) {\r\n    this.listPictures = this.listPictures.filter(x => x.id != pictureId)\r\n  }\r\n\r\n  uploadImage(ref: any) {\r\n    if(!this.isEdit){\r\n      const CFile = this.listPictures.map(x => x.CFile)\r\n      this.validation(CFile)\r\n      if (this.valid.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this.valid.errorMessages);\r\n        return\r\n      }\r\n      this._pictureService.apiPictureUploadListPicturePost$Json({\r\n        body: {\r\n          CBuildCaseId: this.selectedBuildCaseId,\r\n          CPath: \"picture\",\r\n          CFile: CFile\r\n        }\r\n      }).pipe(\r\n        tap(res => {\r\n          if (res.StatusCode == 0) {\r\n            this.message.showSucessMSG('執行成功')\r\n            this.listPictures = []\r\n          } else {\r\n            this.message.showErrorMSG(res.Message!)\r\n          }\r\n          ref.close();\r\n        }),\r\n        concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n      ).subscribe()\r\n    }\r\n    else{\r\n      if(this.listPictures.length > 0 && this.listPictures[0].CFile){\r\n        this._pictureService.apiPictureUpdatePicturePost$Json({\r\n          body: {\r\n            CBuildCaseID: this.selectedBuildCaseId,\r\n            CPictureID: this.currentEditItem,\r\n            CFile: this.listPictures[0].CFile\r\n          }\r\n        }).pipe(\r\n          tap(res => {\r\n            if (res.StatusCode == 0) {\r\n              this.message.showSucessMSG('執行成功')\r\n              this.listPictures = []\r\n            } else {\r\n              this.message.showErrorMSG(res.Message!)\r\n            }\r\n            ref.close();\r\n          }),\r\n          concatMap((res) => res.StatusCode! == 0 ? this.getPicturelList(1) : of(null)),\r\n        ).subscribe()\r\n      }\r\n    }\r\n  }\r\n\r\n  renameFile(event: any, index: number) {\r\n    var blob = this.listPictures[index].CFile.slice(0, this.listPictures[index].CFile.size, this.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + this.listPictures[index].extension}`, { type: this.listPictures[index].CFile.type });\r\n\r\n    this.listPictures[index].CFile = newFile\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可設定上傳建材示意圖片，上傳前請將圖片檔案改為建材圖片檔名。</h1>\r\n\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label mr-2\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"selectedBuildCaseId\" (selectedChange)=\"selectedChange($event)\"\r\n            class=\"w-full\">\r\n            <nb-option *ngFor=\"let buildCase of listUserBuildCases\" [value]=\"buildCase.cID\">\r\n              {{ buildCase.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"d-flex justify-content-end w-full\">\r\n          <button class=\"btn btn-info\" (click)=\"addNew(dialog)\" *ngIf=\"isCreate\">\r\n            圖片上傳</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"min-width: 1000px; background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1\">項次</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">項目</th>\r\n            <th scope=\"col\" class=\"col-1\">位置</th>\r\n            <th scope=\"col\" class=\"col-1\">建材選項名稱</th>\r\n            <th scope=\"col\" class=\"col-1\">建材圖片檔名（相同建案不可重複）</th>\r\n            <th scope=\"col\" class=\"col-1\">最新圖片上傳時間</th>\r\n            <th scope=\"col\" class=\"col-1\"></th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of images ; let i = index\">\r\n            <td>{{ item.CId}}</td>\r\n            <td>\r\n              <!-- <a class=\"cursor-pointer text-blue-500\" (click)=\"openPdfInNewTab(item?.CFile)\">{{ item?.CName}}</a> -->\r\n              {{ item?.CName}}\r\n            </td>\r\n            <td>{{ item.CPart}}</td>\r\n            <td>{{ item.CLocation}}</td>\r\n            <td>{{ item.CSelectName}}</td>\r\n            <td>{{ item.CPictureCode}}</td>\r\n            <td>{{ item?.CUpdateDT | date: \"yyyy/MM/dd HH:mm:ss\"}}</td>\r\n            <td class=\"text-center w-32\">\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"addNew(dialog, item)\">檢視</button>\r\n              <button class=\"btn btn-outline-primary btn-sm m-1\" *ngIf=\"isRead\"\r\n                (click)=\"changePicture(dialog, item)\">改變圖片</button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(CollectionSize)]=\"totalRecords\" [(PageSize)]=\"pageSize\" [(Page)]=\"pageIndex\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; width: 700px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span>\r\n        {{currentImageShowing ? '檢視' : '圖片上傳'}}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"w-full h-auto\" *ngIf=\"currentImageShowing else upload\">\r\n        <img class=\"fit-size\" [src]=\"currentImageShowing | addBaseFile\">\r\n      </div>\r\n      <ng-template #upload>\r\n        <button [class]=\"!isEdit ? 'btn btn-info' : listPictures.length < 1 ? 'btn btn-info' : 'btn btn-info disable'\"\r\n          (click)=\"inputFile.click()\">圖片上傳</button>\r\n        <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event)\"\r\n          accept=\"image/png, image/gif, image/jpeg\" multiple>\r\n        <div class=\"mt-3 w-full flex flex-col\">\r\n          <table class=\"table table-striped border\" style=\"background-color:#f3f3f3;\">\r\n            <thead>\r\n              <tr style=\"background-color: #27ae60; color: white;\">\r\n                <th scope=\"col\" class=\"col-4\">文件名</th>\r\n                <th scope=\"col\" class=\"col-1\">檢視</th>\r\n                <th scope=\"col\" class=\"col-1\"></th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let picture of listPictures; let i = index\">\r\n                <td>\r\n                  <input nbInput class=\"w-100 p-2 text-[13px]\" type=\"text\" [value]=\"picture.name\"\r\n                    (blur)=\"renameFile($event, i)\">\r\n                </td>\r\n                <td class=\"w-[100px] h-auto\">\r\n                  <img class=\"fit-size\" [src]=\"picture.data\">\r\n                </td>\r\n                <td class=\"text-center w-32\">\r\n                  <button class=\"btn btn-outline-danger btn-sm m-1\" (click)=\"removeImage(picture.id)\">删除</button>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-template>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"flex justify-center items-center\">\r\n        <button class=\"btn btn-danger mr-2\" (click)=\"ref.close(); this.currentImageShowing = ''\">取消</button>\r\n        <button *ngIf=\"!currentImageShowing\" class=\"btn btn-success\" (click)=\"uploadImage(ref)\">儲存</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAoB,iBAAiB;AAM1D,SAASC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAInD,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;;;;;ICCvDC,EAAA,CAAAC,cAAA,oBAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF4CH,EAAA,CAAAI,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAuB;IAC7EN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,YAAA,CAAAI,cAAA,MACF;;;;;;IAMFT,EAAA,CAAAC,cAAA,iBAAuE;IAA1CD,EAAA,CAAAU,UAAA,mBAAAC,oEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,CAAc;IAAA,EAAC;IACnDhB,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAgCXH,EAAA,CAAAC,cAAA,iBACiC;IAA/BD,EAAA,CAAAU,UAAA,mBAAAU,0EAAA;MAAApB,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAe,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAK,MAAA,CAAAH,SAAA,EAAAM,OAAA,CAAoB;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBACwC;IAAtCD,EAAA,CAAAU,UAAA,mBAAAc,0EAAA;MAAAxB,EAAA,CAAAY,aAAA,CAAAa,GAAA;MAAA,MAAAH,OAAA,GAAAtB,EAAA,CAAAe,aAAA,GAAAQ,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAY,aAAA,CAAAV,SAAA,EAAAM,OAAA,CAA2B;IAAA,EAAC;IAACtB,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdvDH,EADF,CAAAC,cAAA,SAAgD,SAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAEFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAA2B,UAAA,KAAAC,iDAAA,qBACiC,KAAAC,iDAAA,qBAEO;IAE5C7B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAhBCH,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAS,GAAA,CAAa;IAGf/B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAc,OAAA,kBAAAA,OAAA,CAAAU,KAAA,MACF;IACIhC,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAW,KAAA,CAAe;IACfjC,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAY,SAAA,CAAmB;IACnBlC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAa,WAAA,CAAqB;IACrBnC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA8B,iBAAA,CAAAR,OAAA,CAAAc,YAAA,CAAsB;IACtBpC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAqC,WAAA,QAAAf,OAAA,kBAAAA,OAAA,CAAAgB,SAAA,yBAAkD;IAEAtC,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyB,MAAA,CAAY;IAEZvC,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,SAAAU,MAAA,CAAAyB,MAAA,CAAY;;;;;IAuBxEvC,EAAA,CAAAC,cAAA,cAAmE;IACjED,EAAA,CAAAwC,SAAA,cAAgE;;IAClExC,EAAA,CAAAG,YAAA,EAAM;;;;IADkBH,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAAyC,WAAA,OAAA3B,MAAA,CAAA4B,mBAAA,GAAA1C,EAAA,CAAA2C,aAAA,CAAyC;;;;;;IAmBrD3C,EAFJ,CAAAC,cAAA,SAAwD,SAClD,gBAE+B;IAA/BD,EAAA,CAAAU,UAAA,kBAAAkC,2FAAAC,MAAA;MAAA,MAAAC,KAAA,GAAA9C,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAQJ,MAAA,CAAAmC,UAAA,CAAAJ,MAAA,EAAAC,KAAA,CAAqB;IAAA,EAAC;IAClC9C,EAFE,CAAAG,YAAA,EACiC,EAC9B;IACLH,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAwC,SAAA,cAA2C;IAC7CxC,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,aAA6B,iBACyD;IAAlCD,EAAA,CAAAU,UAAA,mBAAAwC,6FAAA;MAAA,MAAAC,WAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAmC,IAAA,EAAAxB,SAAA;MAAA,MAAAT,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAsC,WAAA,CAAAD,WAAA,CAAAE,EAAA,CAAuB;IAAA,EAAC;IAACrD,EAAA,CAAAE,MAAA,mBAAE;IAE1FF,EAF0F,CAAAG,YAAA,EAAS,EAC5F,EACF;;;;IATwDH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,UAAA+C,WAAA,CAAAG,IAAA,CAAsB;IAIzDtD,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAI,UAAA,QAAA+C,WAAA,CAAAI,IAAA,EAAAvD,EAAA,CAAA2C,aAAA,CAAoB;;;;;;IApBpD3C,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAU,UAAA,mBAAA8C,uFAAA;MAAAxD,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAAC,aAAA,GAAA1D,EAAA,CAAAiB,WAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASwC,aAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IAAC3D,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3CH,EAAA,CAAAC,cAAA,mBACqD;IADRD,EAAA,CAAAU,UAAA,oBAAAkD,uFAAAf,MAAA;MAAA7C,EAAA,CAAAY,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAUJ,MAAA,CAAA+C,WAAA,CAAAhB,MAAA,CAAmB;IAAA,EAAC;IAA3E7C,EAAA,CAAAG,YAAA,EACqD;IAK7CH,EAJR,CAAAC,cAAA,cAAuC,gBACuC,YACnE,aACgD,aACrB;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAwC,SAAA,cAAmC;IAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2B,UAAA,KAAAmC,oEAAA,iBAAwD;IAc9D9D,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IA5BEH,EAAA,CAAA+D,UAAA,EAAAjD,MAAA,CAAAkD,MAAA,oBAAAlD,MAAA,CAAAmD,YAAA,CAAAC,MAAA,+CAAsG;IAchFlE,EAAA,CAAAO,SAAA,IAAiB;IAAjBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAmD,YAAA,CAAiB;;;;;;IAoB/CjE,EAAA,CAAAC,cAAA,iBAAwF;IAA3BD,EAAA,CAAAU,UAAA,mBAAAyD,mFAAA;MAAAnE,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAArE,EAAA,CAAAe,aAAA,GAAAuD,SAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASJ,MAAA,CAAAyD,WAAA,CAAAF,OAAA,CAAgB;IAAA,EAAC;IAACrE,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3CrGH,EAFJ,CAAAC,cAAA,kBAAqF,qBACnE,WACR;IACJD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACQ;IACjBH,EAAA,CAAAC,cAAA,uBAAwC;IAItCD,EAHA,CAAA2B,UAAA,IAAA6C,sDAAA,kBAAmE,IAAAC,8DAAA,iCAAAzE,EAAA,CAAA0E,sBAAA,CAG9C;IA+BvB1E,EAAA,CAAAG,YAAA,EAAe;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,cACgC,kBAC6C;IAArDD,EAAA,CAAAU,UAAA,mBAAAiE,0EAAA;MAAA,MAAAN,OAAA,GAAArE,EAAA,CAAAY,aAAA,CAAAgE,GAAA,EAAAN,SAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAASsD,OAAA,CAAAQ,KAAA,EAAW;MAAA,OAAA7E,EAAA,CAAAkB,WAAA,CAAAJ,MAAA,CAAA4B,mBAAA,GAA6B,EAAE;IAAA,EAAC;IAAC1C,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpGH,EAAA,CAAA2B,UAAA,KAAAmD,0DAAA,qBAAwF;IAG9F9E,EAFI,CAAAG,YAAA,EAAM,EACS,EACT;;;;;IA7CJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAM,MAAA,CAAA4B,mBAAA,oDACF;IAG4B1C,EAAA,CAAAO,SAAA,GAA0B;IAAAP,EAA1B,CAAAI,UAAA,SAAAU,MAAA,CAAA4B,mBAAA,CAA0B,aAAAqC,UAAA,CAAW;IAsCtD/E,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,UAAAU,MAAA,CAAA4B,mBAAA,CAA0B;;;AD1F3C,OAAM,MAAOsC,wBAAyB,SAAQjF,aAAa;EAqBzDkF,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,KAAuB,EACvBC,eAA+B,EAC/BC,mBAAuC,EACvCC,iBAAmC,EACnCC,OAAuB,EACvBC,eAA+B;IAEvC,KAAK,CAACP,MAAM,CAAC;IATL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,eAAe,GAAfA,eAAe;IA3BzB,KAAAC,MAAM,GAA4D,EAAE;IACpE,KAAAC,kBAAkB,GAA8B,EAAE;IAGlD,KAAAC,gBAAgB,GAAW,mBAAmB,EAAC;IAE/C,KAAAlD,mBAAmB,GAAW,EAAE;IAEhC,KAAAuB,YAAY,GAAU,EAAE;IAExB,KAAAD,MAAM,GAAY,KAAK;IAGvB;IACA,KAAA6B,eAAe,GAAG,CAChB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,KAAK,EAAE;IAAM,CAAE,EAC7C;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAM,CAAE,CACtC;EAaD;EAESC,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACV,iBAAiB,CAACW,qCAAqC,CAAC,EAAE,CAAC,CAC7DC,IAAI,CACHtG,GAAG,CAACuG,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACV,kBAAkB,GAAGS,GAAG,CAACE,OAAQ,IAAI,EAAE;QAC5C,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACZ,kBAAkB,CAAC,CAAC,CAAC,CAACrF,GAAI;MAC5D;IACF,CAAC,CAAC,EACFX,SAAS,CAAC,MAAM,IAAI,CAAC6G,eAAe,CAAC,CAAC,CAAC,CAAC,CACzC,CAACC,SAAS,EAAE;EACjB;EAEAC,eAAeA,CAACC,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,CAACD,QAAQ,CAAC;IACjD;EACF;EACAH,eAAeA,CAACK,SAAiB;IAC/B,IAAI,IAAI,CAACjB,gBAAgB,KAAK,mBAAmB,EAAE;MACjD,OAAO,IAAI,CAACP,eAAe,CAACyB,kCAAkC,CAAC;QAC7DC,IAAI,EAAE;UACJC,SAAS,EAAEH,SAAS;UACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACZ;;OAEtB,CAAC,CAACJ,IAAI,CACLtG,GAAG,CAACuG,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACX,MAAM,GAAGU,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QACrC;MACF,CAAC,CAAC,CACH;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAAC/B,mBAAmB,CAACgC,0CAA0C,CAAC;QACzEP,IAAI,EAAE;UACJC,SAAS,EAAEH,SAAS;UACpBI,QAAQ,EAAE,IAAI,CAACC,QAAQ;UACvBC,YAAY,EAAE,IAAI,CAACZ;;OAEtB,CAAC,CAACJ,IAAI,CACLtG,GAAG,CAACuG,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACX,MAAM,GAAGU,GAAG,CAACE,OAAQ,IAAI,EAAE;UAChC,IAAI,CAACc,YAAY,GAAGhB,GAAG,CAACiB,UAAW;QACrC;MACF,CAAC,CAAC,CACH;IACH;EACF;EAEAE,WAAWA,CAACV,SAAiB;IAC3B;IACA,IAAI,CAACL,eAAe,CAACK,SAAS,CAAC,CAACJ,SAAS,EAAE;EAC7C;EACAe,cAAcA,CAACC,WAAmB;IAChC,IAAI,CAAClB,mBAAmB,GAAGkB,WAAW;IACtC,IAAI,CAACjB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EAEAiB,eAAeA,CAACC,QAAgB;IAC9B,IAAI,CAAC/B,gBAAgB,GAAG+B,QAAQ;IAChC,IAAI,CAACnB,eAAe,CAAC,CAAC,CAAC,CAACC,SAAS,EAAE;EACrC;EACAtF,MAAMA,CAACyG,GAAQ,EAAEC,IAA0D;IACzE,IAAI,CAAC5D,YAAY,GAAG,EAAE;IACtB,IAAI,CAACkB,aAAa,CAAC2C,IAAI,CAACF,GAAG,CAAC;IAC5B,IAAI,CAAC5D,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC,CAAC6D,IAAI,EAAE;MACV,IAAI,CAACnF,mBAAmB,GAAGmF,IAAI,CAACE,KAAM;IACxC,CAAC,MACG;MACF,IAAI,CAAC9D,YAAY,GAAG,EAAE;IACxB;EACF;EAEAvC,aAAaA,CAACkG,GAAQ,EAAEC,IAA0D;IAChF,IAAI,CAAC,CAACA,IAAI,IAAIA,IAAI,CAAC9F,GAAG,EAAE;MACtB,IAAI,CAACoD,aAAa,CAAC2C,IAAI,CAACF,GAAG,CAAC;MAC5B,IAAI,CAAC5D,MAAM,GAAG,IAAI;MAClB,IAAI,CAACgE,eAAe,GAAGH,IAAI,CAAC9F,GAAG;MAC/B,IAAI,CAACkC,YAAY,GAAG,EAAE;IACxB;EACF;EAEAgE,UAAUA,CAACF,KAAU;IACnB,IAAI,CAAC3C,KAAK,CAAC8C,KAAK,EAAE;IAClB,MAAMC,OAAO,GAAG,IAAIC,GAAG,EAAE;IACzB,KAAK,MAAMP,IAAI,IAAIE,KAAK,EAAE;MACxB,IAAII,OAAO,CAACE,GAAG,CAACR,IAAI,CAACvE,IAAI,CAAC,EAAE;QAC1B,IAAI,CAAC8B,KAAK,CAACkD,eAAe,CAAC,QAAQ,CAAC;QACpC;MACF;MACAH,OAAO,CAACI,GAAG,CAACV,IAAI,CAACvE,IAAI,CAAC;IACxB;EACF;EAEAkF,QAAQA,CAACZ,GAAQ,GACjB;EAEA/D,WAAWA,CAAC4E,KAAU;IACpB,KAAK,IAAIzF,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGyF,KAAK,CAACC,MAAM,CAACC,KAAK,CAACzE,MAAM,EAAElB,KAAK,EAAE,EAAE;MAC9D,MAAM4F,IAAI,GAAGH,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC3F,KAAK,CAAC;MACtC,IAAI4F,IAAI,EAAE;QACR,IAAIC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC7BD,MAAM,CAACE,aAAa,CAACH,IAAI,CAAC;QAC1BC,MAAM,CAACG,MAAM,GAAG,MAAK;UACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;UAC/C,IAAI,CAACD,SAAS,EAAE;YACd;UACF;UACA;UACA,MAAME,wBAAwB,GAAGP,IAAI,CAACtF,IAAI,CAAC8F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACxD;UACA,MAAMC,iBAAiB,GAAG,IAAI,CAACpF,YAAY,CAACqF,SAAS,CAACC,OAAO,IAAIA,OAAO,CAACjG,IAAI,KAAK6F,wBAAwB,CAAC;UAC3G,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;YAC5B;YACA,IAAI,CAACpF,YAAY,CAACoF,iBAAiB,CAAC,GAAG;cACrC,GAAG,IAAI,CAACpF,YAAY,CAACoF,iBAAiB,CAAC;cACvC9F,IAAI,EAAE0F,SAAS;cACflB,KAAK,EAAEa,IAAI;cACXY,SAAS,EAAE,IAAI,CAAC/D,eAAe,CAACgE,gBAAgB,CAACb,IAAI,CAACtF,IAAI;aAC3D;UACH,CAAC,MAAM;YACL;YACAsF,IAAI,CAACvF,EAAE,GAAG,IAAIqG,IAAI,EAAE,CAACC,OAAO,EAAE;YAC9B,IAAI,CAAC1F,YAAY,CAAC2F,IAAI,CAAC;cACrBvG,EAAE,EAAE,IAAIqG,IAAI,EAAE,CAACC,OAAO,EAAE;cACxBrG,IAAI,EAAE6F,wBAAwB;cAC9B5F,IAAI,EAAE0F,SAAS;cACfO,SAAS,EAAE,IAAI,CAAC/D,eAAe,CAACgE,gBAAgB,CAACb,IAAI,CAACtF,IAAI,CAAC;cAC3DyE,KAAK,EAAEa;aACR,CAAC;UACJ;UACA;UACAH,KAAK,CAACC,MAAM,CAAC5C,KAAK,GAAG,IAAI;QAC3B,CAAC;MACH;IACF;EACF;EAEA1C,WAAWA,CAACyG,SAAiB;IAC3B,IAAI,CAAC5F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC6F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1G,EAAE,IAAIwG,SAAS,CAAC;EACtE;EAEAtF,WAAWA,CAACqD,GAAQ;IAClB,IAAG,CAAC,IAAI,CAAC5D,MAAM,EAAC;MACd,MAAM+D,KAAK,GAAG,IAAI,CAAC9D,YAAY,CAAC+F,GAAG,CAACD,CAAC,IAAIA,CAAC,CAAChC,KAAK,CAAC;MACjD,IAAI,CAACE,UAAU,CAACF,KAAK,CAAC;MACtB,IAAI,IAAI,CAAC3C,KAAK,CAAC6E,aAAa,CAAC/F,MAAM,GAAG,CAAC,EAAE;QACvC,IAAI,CAACsB,OAAO,CAAC0E,aAAa,CAAC,IAAI,CAAC9E,KAAK,CAAC6E,aAAa,CAAC;QACpD;MACF;MACA,IAAI,CAAC5E,eAAe,CAAC8E,oCAAoC,CAAC;QACxDpD,IAAI,EAAE;UACJI,YAAY,EAAE,IAAI,CAACZ,mBAAmB;UACtC6D,KAAK,EAAE,SAAS;UAChBrC,KAAK,EAAEA;;OAEV,CAAC,CAAC5B,IAAI,CACLtG,GAAG,CAACuG,GAAG,IAAG;QACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;UACvB,IAAI,CAACb,OAAO,CAAC6E,aAAa,CAAC,MAAM,CAAC;UAClC,IAAI,CAACpG,YAAY,GAAG,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACuB,OAAO,CAAC8E,YAAY,CAAClE,GAAG,CAACmE,OAAQ,CAAC;QACzC;QACA3C,GAAG,CAAC/C,KAAK,EAAE;MACb,CAAC,CAAC,EACFlF,SAAS,CAAEyG,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG5G,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC6G,SAAS,EAAE;IACf,CAAC,MACG;MACF,IAAG,IAAI,CAACxC,YAAY,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC,CAAC8D,KAAK,EAAC;QAC5D,IAAI,CAAC1C,eAAe,CAACmF,gCAAgC,CAAC;UACpDzD,IAAI,EAAE;YACJ0D,YAAY,EAAE,IAAI,CAAClE,mBAAmB;YACtCmE,UAAU,EAAE,IAAI,CAAC1C,eAAe;YAChCD,KAAK,EAAE,IAAI,CAAC9D,YAAY,CAAC,CAAC,CAAC,CAAC8D;;SAE/B,CAAC,CAAC5B,IAAI,CACLtG,GAAG,CAACuG,GAAG,IAAG;UACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;YACvB,IAAI,CAACb,OAAO,CAAC6E,aAAa,CAAC,MAAM,CAAC;YAClC,IAAI,CAACpG,YAAY,GAAG,EAAE;UACxB,CAAC,MAAM;YACL,IAAI,CAACuB,OAAO,CAAC8E,YAAY,CAAClE,GAAG,CAACmE,OAAQ,CAAC;UACzC;UACA3C,GAAG,CAAC/C,KAAK,EAAE;QACb,CAAC,CAAC,EACFlF,SAAS,CAAEyG,GAAG,IAAKA,GAAG,CAACC,UAAW,IAAI,CAAC,GAAG,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC,GAAG5G,EAAE,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC6G,SAAS,EAAE;MACf;IACF;EACF;EAEAxD,UAAUA,CAACwF,KAAU,EAAEzF,KAAa;IAClC,IAAI2H,IAAI,GAAG,IAAI,CAAC1G,YAAY,CAACjB,KAAK,CAAC,CAAC+E,KAAK,CAAC6C,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC3G,YAAY,CAACjB,KAAK,CAAC,CAAC+E,KAAK,CAAC8C,IAAI,EAAE,IAAI,CAAC5G,YAAY,CAACjB,KAAK,CAAC,CAAC+E,KAAK,CAAC+C,IAAI,CAAC;IAC5H,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGlC,KAAK,CAACC,MAAM,CAAC5C,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC7B,YAAY,CAACjB,KAAK,CAAC,CAACwG,SAAS,EAAE,EAAE;MAAEsB,IAAI,EAAE,IAAI,CAAC7G,YAAY,CAACjB,KAAK,CAAC,CAAC+E,KAAK,CAAC+C;IAAI,CAAE,CAAC;IAEjJ,IAAI,CAAC7G,YAAY,CAACjB,KAAK,CAAC,CAAC+E,KAAK,GAAGgD,OAAO;EAC1C;;;uCAhPW/F,wBAAwB,EAAAhF,EAAA,CAAAiL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAAiL,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArL,EAAA,CAAAiL,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAvL,EAAA,CAAAiL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzL,EAAA,CAAAiL,iBAAA,CAAAO,EAAA,CAAAE,kBAAA,GAAA1L,EAAA,CAAAiL,iBAAA,CAAAO,EAAA,CAAAG,gBAAA,GAAA3L,EAAA,CAAAiL,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAA7L,EAAA,CAAAiL,iBAAA,CAAAa,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAxB/G,wBAAwB;MAAAgH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlM,EAAA,CAAAmM,0BAAA,EAAAnM,EAAA,CAAAoM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC1BnC1M,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAwC,SAAA,qBAAiC;UACnCxC,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,2LAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKlEH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,oBACiB;UADWD,EAAA,CAAA4M,gBAAA,2BAAAC,sEAAAhK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA9M,EAAA,CAAA+M,kBAAA,CAAAJ,GAAA,CAAApG,mBAAA,EAAA1D,MAAA,MAAA8J,GAAA,CAAApG,mBAAA,GAAA1D,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAiC;UAAC7C,EAAA,CAAAU,UAAA,4BAAAsM,uEAAAnK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA,OAAA9M,EAAA,CAAAkB,WAAA,CAAkByL,GAAA,CAAAnF,cAAA,CAAA3E,MAAA,CAAsB;UAAA,EAAC;UAErG7C,EAAA,CAAA2B,UAAA,KAAAsL,8CAAA,wBAAgF;UAKtFjN,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAEJH,EADF,CAAAC,cAAA,cAAsB,eAC2B;UAC7CD,EAAA,CAAA2B,UAAA,KAAAuL,2CAAA,qBAAuE;UAI7ElN,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAMEH,EAJR,CAAAC,cAAA,eAAmC,iBAC+D,aACvF,cACgD,cACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wGAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,wDAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAwC,SAAA,cAAmC;UAEvCxC,EADE,CAAAG,YAAA,EAAK,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2B,UAAA,KAAAwL,uCAAA,mBAAgD;UAqBxDnN,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADqCD,EAA1D,CAAA4M,gBAAA,kCAAAQ,kFAAAvK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA9M,EAAA,CAAA+M,kBAAA,CAAAJ,GAAA,CAAAvF,YAAA,EAAAvE,MAAA,MAAA8J,GAAA,CAAAvF,YAAA,GAAAvE,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAiC,4BAAAwK,4EAAAxK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA9M,EAAA,CAAA+M,kBAAA,CAAAJ,GAAA,CAAAzF,QAAA,EAAArE,MAAA,MAAA8J,GAAA,CAAAzF,QAAA,GAAArE,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAwB,wBAAAyK,wEAAAzK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA9M,EAAA,CAAA+M,kBAAA,CAAAJ,GAAA,CAAA9F,SAAA,EAAAhE,MAAA,MAAA8J,GAAA,CAAA9F,SAAA,GAAAhE,MAAA;YAAA,OAAA7C,EAAA,CAAAkB,WAAA,CAAA2B,MAAA;UAAA,EAAqB;UAC5F7C,EAAA,CAAAU,UAAA,wBAAA4M,wEAAAzK,MAAA;YAAA7C,EAAA,CAAAY,aAAA,CAAAkM,GAAA;YAAA,OAAA9M,EAAA,CAAAkB,WAAA,CAAcyL,GAAA,CAAApF,WAAA,CAAA1E,MAAA,CAAmB;UAAA,EAAC;UAGxC7C,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAEVH,EAAA,CAAA2B,UAAA,KAAA4L,gDAAA,iCAAAvN,EAAA,CAAA0E,sBAAA,CAAkD;;;UA5DZ1E,EAAA,CAAAO,SAAA,IAAiC;UAAjCP,EAAA,CAAAwN,gBAAA,YAAAb,GAAA,CAAApG,mBAAA,CAAiC;UAE1BvG,EAAA,CAAAO,SAAA,EAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAuM,GAAA,CAAAhH,kBAAA,CAAqB;UAQD3F,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,SAAAuM,GAAA,CAAAc,QAAA,CAAc;UAqBhDzN,EAAA,CAAAO,SAAA,IAAY;UAAZP,EAAA,CAAAI,UAAA,YAAAuM,GAAA,CAAAjH,MAAA,CAAY;UAuBvB1F,EAAA,CAAAO,SAAA,GAAiC;UAAyBP,EAA1D,CAAAwN,gBAAA,mBAAAb,GAAA,CAAAvF,YAAA,CAAiC,aAAAuF,GAAA,CAAAzF,QAAA,CAAwB,SAAAyF,GAAA,CAAA9F,SAAA,CAAqB;;;qBD5C9FnH,YAAY,EAAAgO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EACZ/N,YAAY,EAAAgO,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAA5C,EAAA,CAAA6C,eAAA,EAAA7C,EAAA,CAAA8C,mBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,gBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}