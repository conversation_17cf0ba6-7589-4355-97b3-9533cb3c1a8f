{"ast": null, "code": "import { reduce } from './reduce';\nimport { isFunction } from '../util/isFunction';\nexport function max(comparer) {\n  return reduce(isFunction(comparer) ? (x, y) => comparer(x, y) > 0 ? x : y : (x, y) => x > y ? x : y);\n}", "map": {"version": 3, "names": ["reduce", "isFunction", "max", "comparer", "x", "y"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/rxjs/dist/esm/internal/operators/max.js"], "sourcesContent": ["import { reduce } from './reduce';\nimport { isFunction } from '../util/isFunction';\nexport function max(comparer) {\n    return reduce(isFunction(comparer) ? (x, y) => (comparer(x, y) > 0 ? x : y) : (x, y) => (x > y ? x : y));\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,GAAGA,CAACC,QAAQ,EAAE;EAC1B,OAAOH,MAAM,CAACC,UAAU,CAACE,QAAQ,CAAC,GAAG,CAACC,CAAC,EAAEC,CAAC,KAAMF,QAAQ,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAE,GAAG,CAACD,CAAC,EAAEC,CAAC,KAAMD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAE,CAAC;AAC5G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}