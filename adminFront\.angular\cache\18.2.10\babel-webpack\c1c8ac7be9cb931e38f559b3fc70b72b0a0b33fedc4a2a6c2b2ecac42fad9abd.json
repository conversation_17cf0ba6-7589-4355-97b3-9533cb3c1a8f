{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FileNamePipe {\n  transform(value) {\n    if (!value) return value;\n    const segments = value.split('/');\n    return segments.pop() || '';\n  }\n  static {\n    this.ɵfac = function FileNamePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FileNamePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"fileName\",\n      type: FileNamePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["FileNamePipe", "transform", "value", "segments", "split", "pop", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\file-name.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'fileName',\r\n  standalone: true \r\n})\r\nexport class FileNamePipe implements PipeTransform {\r\n\r\n  transform(value: string): string {\r\n    if (!value) return value;\r\n    const segments = value.split('/');\r\n    return segments.pop() || '';\r\n  }\r\n}"], "mappings": ";AAMA,OAAM,MAAOA,YAAY;EAEvBC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;IACxB,MAAMC,QAAQ,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;IACjC,OAAOD,QAAQ,CAACE,GAAG,EAAE,IAAI,EAAE;EAC7B;;;uCANWL,YAAY;IAAA;EAAA;;;;YAAZA,YAAY;MAAAM,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}