{"ast": null, "code": "import { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class QuotationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.BASE_URL_API}/api/quotation`;\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    return this.http.get(`${this.apiUrl}/house/${houseId}`);\n  }\n  // 取得預設報價項目\n  getDefaultQuotationItems() {\n    return this.http.get(`${this.apiUrl}/default`);\n  }\n  // 儲存報價單\n  saveQuotation(request) {\n    return this.http.post(this.apiUrl, request);\n  }\n  // 更新報價項目\n  updateQuotationItem(quotationId, item) {\n    return this.http.put(`${this.apiUrl}/${quotationId}`, item);\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.http.delete(`${this.apiUrl}/${quotationId}`);\n  }\n  // 匯出報價單\n  exportQuotation(houseId) {\n    return this.http.get(`${this.apiUrl}/export/${houseId}`, {\n      responseType: 'blob'\n    });\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "QuotationService", "constructor", "http", "apiUrl", "BASE_URL_API", "getQuotationByHouseId", "houseId", "get", "getDefaultQuotationItems", "saveQuotation", "request", "post", "updateQuotationItem", "quotationId", "item", "put", "deleteQuotationItem", "delete", "exportQuotation", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n  private apiUrl = `${environment.BASE_URL_API}/api/quotation`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<QuotationResponse> {\r\n    return this.http.get<QuotationResponse>(`${this.apiUrl}/house/${houseId}`);\r\n  }\r\n\r\n  // 取得預設報價項目\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    return this.http.get<QuotationResponse>(`${this.apiUrl}/default`);\r\n  }\r\n\r\n  // 儲存報價單\r\n  saveQuotation(request: QuotationRequest): Observable<QuotationResponse> {\r\n    return this.http.post<QuotationResponse>(this.apiUrl, request);\r\n  }\r\n\r\n  // 更新報價項目\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    return this.http.put<QuotationResponse>(`${this.apiUrl}/${quotationId}`, item);\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.http.delete<QuotationResponse>(`${this.apiUrl}/${quotationId}`);\r\n  }\r\n\r\n  // 匯出報價單\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    return this.http.get(`${this.apiUrl}/export/${houseId}`, {\r\n      responseType: 'blob'\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAIA,SAASA,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,YAAY,gBAAgB;EAEpB;EAExC;EACAC,qBAAqBA,CAACC,OAAe;IACnC,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAoB,GAAG,IAAI,CAACJ,MAAM,UAAUG,OAAO,EAAE,CAAC;EAC5E;EAEA;EACAE,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,CAAoB,GAAG,IAAI,CAACJ,MAAM,UAAU,CAAC;EACnE;EAEA;EACAM,aAAaA,CAACC,OAAyB;IACrC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAoB,IAAI,CAACR,MAAM,EAAEO,OAAO,CAAC;EAChE;EAEA;EACAE,mBAAmBA,CAACC,WAAmB,EAAEC,IAAmB;IAC1D,OAAO,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAoB,GAAG,IAAI,CAACZ,MAAM,IAAIU,WAAW,EAAE,EAAEC,IAAI,CAAC;EAChF;EAEA;EACAE,mBAAmBA,CAACH,WAAmB;IACrC,OAAO,IAAI,CAACX,IAAI,CAACe,MAAM,CAAoB,GAAG,IAAI,CAACd,MAAM,IAAIU,WAAW,EAAE,CAAC;EAC7E;EAEA;EACAK,eAAeA,CAACZ,OAAe;IAC7B,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,GAAG,IAAI,CAACJ,MAAM,WAAWG,OAAO,EAAE,EAAE;MACvDa,YAAY,EAAE;KACf,CAAC;EACJ;;;uCAnCWnB,gBAAgB,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBvB,gBAAgB;MAAAwB,OAAA,EAAhBxB,gBAAgB,CAAAyB,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}