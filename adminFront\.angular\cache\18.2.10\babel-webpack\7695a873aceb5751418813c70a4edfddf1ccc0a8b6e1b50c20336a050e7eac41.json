{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FilterByKeyPipe {\n  transform(snapshotArr, typeToSearch, searchValue) {\n    if (!snapshotArr) {\n      return [];\n    }\n    if (!searchValue) {\n      return snapshotArr;\n    }\n    if (snapshotArr && searchValue) {\n      return snapshotArr.filter(snapshot => {\n        return snapshot[`${typeToSearch}`].toLowerCase().includes(searchValue.toLowerCase());\n      });\n    }\n  }\n  static {\n    this.ɵfac = function FilterByKeyPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FilterByKeyPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"filterBy<PERSON><PERSON>\",\n      type: FilterByKeyPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["FilterByKeyPipe", "transform", "snapshotArr", "typeToSearch", "searchValue", "filter", "snapshot", "toLowerCase", "includes", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\filterByKey.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from \"@angular/core\";\r\n\r\n@Pipe({ name: \"filterBy<PERSON><PERSON>\", standalone: true  })\r\nexport class FilterByKeyPipe implements PipeTransform {\r\n    transform(snapshotArr: any, typeToSearch: string, searchValue: string) {\r\n        if (!snapshotArr) {\r\n            return [];\r\n        }\r\n        if (!searchValue) {\r\n            return snapshotArr;\r\n        }\r\n        if (snapshotArr && searchValue) {\r\n            return snapshotArr.filter((snapshot: any) => {\r\n                return snapshot[`${typeToSearch}`].toLowerCase().includes(searchValue.toLowerCase());\r\n            });\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAGA,OAAM,MAAOA,eAAe;EACxBC,SAASA,CAACC,WAAgB,EAAEC,YAAoB,EAAEC,WAAmB;IACjE,IAAI,CAACF,WAAW,EAAE;MACd,OAAO,EAAE;IACb;IACA,IAAI,CAACE,WAAW,EAAE;MACd,OAAOF,WAAW;IACtB;IACA,IAAIA,WAAW,IAAIE,WAAW,EAAE;MAC5B,OAAOF,WAAW,CAACG,MAAM,CAAEC,QAAa,IAAI;QACxC,OAAOA,QAAQ,CAAC,GAAGH,YAAY,EAAE,CAAC,CAACI,WAAW,EAAE,CAACC,QAAQ,CAACJ,WAAW,CAACG,WAAW,EAAE,CAAC;MACxF,CAAC,CAAC;IACN;EACJ;;;uCAbSP,eAAe;IAAA;EAAA;;;;YAAfA,eAAe;MAAAS,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}