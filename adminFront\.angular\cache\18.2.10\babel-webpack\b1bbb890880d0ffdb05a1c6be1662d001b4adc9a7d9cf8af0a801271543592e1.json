{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/api/services/HouseCustom.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@nebular/theme\";\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getHouseholdInfo(householdCode_r4).floor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"div\", 22)(2, \"span\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener() {\n      const householdCode_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onRemoveHousehold(householdCode_r4));\n    });\n    i0.ɵɵelement(6, \"nb-icon\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const householdCode_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(householdCode_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getHouseholdInfo(householdCode_r4).floor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 19);\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template, 7, 3, \"span\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", building_r5, \":\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getBuildingSelectedHouseholds(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_div_9_ng_container_1_Template, 5, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasBuildingSelected(building_r5));\n  }\n}\nfunction HouseholdBindingComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"nb-icon\", 12);\n    i0.ɵɵelementStart(4, \"span\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵtext(7, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15);\n    i0.ɵɵtemplate(9, HouseholdBindingComponent_div_1_div_9_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7\\u6236\\u5225 (\", ctx_r1.getSelectedCount(), \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n  }\n}\nfunction HouseholdBindingComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nb-icon\", 28);\n    i0.ɵɵtext(2, \" \\u8F09\\u5165\\u4E2D... \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HouseholdBindingComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getSelectedCount() > 0 ? \"\\u5DF2\\u9078\\u64C7 \" + ctx_r1.getSelectedCount() + \" \\u500B\\u6236\\u5225\" : ctx_r1.placeholder, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"nb-icon\", 54);\n    i0.ɵɵelementStart(3, \"p\", 55);\n    i0.ɵɵtext(4, \"\\u8F09\\u5165\\u6236\\u5225\\u8CC7\\u6599\\u4E2D...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_button_6_Template_button_click_0_listener() {\n      const building_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onBuildingSelect(building_r8));\n    });\n    i0.ɵɵelementStart(1, \"span\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 72);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const building_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedBuilding === building_r8 ? \"#e3f2fd\" : \"transparent\")(\"border-left\", ctx_r1.selectedBuilding === building_r8 ? \"3px solid #007bff\" : \"3px solid transparent\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(building_r8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getBuildingCount(building_r8), \"\\u6236\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.selectedBuilding, \")\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r1.selectedFloor, \"\");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onUnselectAllBuilding());\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllFiltered());\n    });\n    i0.ɵɵtext(2, \" \\u5168\\u9078\\u7576\\u524D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSelectAllBuilding());\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template, 2, 0, \"button\", 78);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"opacity\", ctx_r1.canSelectMore() ? \"1\" : \"0.5\");\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canSelectMore());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u9078\", ctx_r1.selectedBuilding, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSomeBuildingSelected());\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u627E\\u4E0D\\u5230\\u7B26\\u5408 \\\"\", ctx_r1.searchTerm, \"\\\" \\u7684\\u6236\\u5225 \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81)(2, \"input\", 82);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchTerm, $event) || (ctx_r1.searchTerm = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"input\", function HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_input_2_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"nb-icon\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template, 2, 1, \"div\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm && ctx_r1.filteredHouseholds.length === 0);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(\"\"));\n    });\n    i0.ɵɵtext(1, \" \\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template_button_click_0_listener() {\n      const floor_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onFloorSelect(floor_r14));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.selectedFloor === floor_r14 ? \"#007bff\" : \"#f8f9fa\")(\"color\", ctx_r1.selectedFloor === floor_r14 ? \"#fff\" : \"#495057\")(\"border-color\", ctx_r1.selectedFloor === floor_r14 ? \"#007bff\" : \"#ced4da\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r14, \" (\", ctx_r1.getFloorCount(floor_r14), \") \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵelement(2, \"nb-icon\", 88);\n    i0.ɵɵelementStart(3, \"span\", 89);\n    i0.ɵɵtext(4, \"\\u6A13\\u5C64\\u7BE9\\u9078:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template, 2, 0, \"button\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 91);\n    i0.ɵɵtemplate(7, HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template, 2, 8, \"button\", 92);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.floors);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"nb-icon\", 96);\n    i0.ɵɵelementStart(2, \"p\", 55);\n    i0.ɵɵtext(3, \"\\u8ACB\\u5148\\u9078\\u64C7\\u68DF\\u5225\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r16 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getHouseholdFloor(householdCode_r16), \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵtext(1, \" \\u2715 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template_button_click_0_listener() {\n      const householdCode_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onHouseholdToggle(householdCode_r16));\n    });\n    i0.ɵɵelementStart(1, \"span\", 100);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template, 2, 1, \"span\", 101)(4, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template, 2, 0, \"div\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const householdCode_r16 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#f8f9fa\" : \"#fff\")(\"color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#fff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#6c757d\" : \"#495057\")(\"border-color\", ctx_r1.isHouseholdSelected(householdCode_r16) ? \"#007bff\" : ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"#dee2e6\" : \"#ced4da\")(\"opacity\", ctx_r1.isHouseholdDisabled(householdCode_r16) ? \"0.6\" : \"1\")(\"cursor\", ctx_r1.isHouseholdDisabled(householdCode_r16) ? \"not-allowed\" : \"pointer\");\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isHouseholdDisabled(householdCode_r16));\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"text-decoration\", ctx_r1.isHouseholdExcluded(householdCode_r16) ? \"line-through\" : \"none\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", householdCode_r16, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getHouseholdFloor(householdCode_r16));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isHouseholdExcluded(householdCode_r16));\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 97);\n    i0.ɵɵtemplate(1, HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template, 5, 16, \"button\", 98);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHouseholds);\n  }\n}\nfunction HouseholdBindingComponent_div_8_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 56)(2, \"div\", 57)(3, \"h6\", 58);\n    i0.ɵɵtext(4, \"\\u68DF\\u5225\\u5217\\u8868\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 59);\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_div_8_ng_container_13_button_6_Template, 5, 6, \"button\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 61)(8, \"div\", 57)(9, \"div\", 62)(10, \"h6\", 58);\n    i0.ɵɵtext(11, \" \\u6236\\u5225\\u9078\\u64C7 \");\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_div_8_ng_container_13_span_12_Template, 2, 1, \"span\", 63)(13, HouseholdBindingComponent_div_8_ng_container_13_span_13_Template, 2, 1, \"span\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, HouseholdBindingComponent_div_8_ng_container_13_div_14_Template, 6, 8, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, HouseholdBindingComponent_div_8_ng_container_13_div_15_Template, 5, 2, \"div\", 66)(16, HouseholdBindingComponent_div_8_ng_container_13_div_16_Template, 8, 2, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 68);\n    i0.ɵɵtemplate(18, HouseholdBindingComponent_div_8_ng_container_13_div_18_Template, 4, 0, \"div\", 69)(19, HouseholdBindingComponent_div_8_ng_container_13_div_19_Template, 2, 1, \"div\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.buildings);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowBatchSelect && ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.floors.length > 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding && ctx_r1.filteredHouseholds.length > 0);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"nb-icon\", 105);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u9650\\u5236: \\u6700\\u591A \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" \\u500B\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.maxSelections);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_25_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵelement(1, \"nb-icon\", 108);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFloor, \" \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"nb-icon\", 33);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"\\u7576\\u524D\\u68DF\\u5225: \");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, HouseholdBindingComponent_div_8_div_25_span_6_Template, 3, 1, \"span\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedFloor);\n  }\n}\nfunction HouseholdBindingComponent_div_8_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \\u641C\\u5C0B: \\\"\", ctx_r1.searchTerm, \"\\\" (\", ctx_r1.filteredHouseholds.length, \" \\u500B\\u7D50\\u679C) \");\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClearAll());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 111);\n    i0.ɵɵtext(2, \" \\u6E05\\u7A7A\\u5168\\u90E8 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.resetSearch());\n    });\n    i0.ɵɵelement(1, \"nb-icon\", 113);\n    i0.ɵɵtext(2, \" \\u91CD\\u7F6E\\u641C\\u5C0B \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdBindingComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"div\", 31)(3, \"div\", 32);\n    i0.ɵɵelement(4, \"nb-icon\", 33);\n    i0.ɵɵelementStart(5, \"span\", 34);\n    i0.ɵɵtext(6, \"\\u9078\\u64C7\\u6236\\u5225\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 35);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 36);\n    i0.ɵɵtemplate(12, HouseholdBindingComponent_div_8_div_12_Template, 5, 0, \"div\", 37)(13, HouseholdBindingComponent_div_8_ng_container_13_Template, 20, 8, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 38)(15, \"div\", 39)(16, \"div\", 40)(17, \"div\", 41);\n    i0.ɵɵelement(18, \"nb-icon\", 42);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"\\u5DF2\\u9078\\u64C7: \");\n    i0.ɵɵelementStart(21, \"strong\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" \\u500B\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, HouseholdBindingComponent_div_8_div_24_Template, 7, 1, \"div\", 43)(25, HouseholdBindingComponent_div_8_div_25_Template, 7, 2, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, HouseholdBindingComponent_div_8_div_26_Template, 2, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵtemplate(29, HouseholdBindingComponent_div_8_button_29_Template, 3, 0, \"button\", 47)(30, HouseholdBindingComponent_div_8_button_30_Template, 3, 0, \"button\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 46)(32, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(33, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_8_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelement(35, \"nb-icon\", 51);\n    i0.ɵɵtext(36, \" \\u78BA\\u5B9A\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.buildings.length, \" \\u500B\\u68DF\\u5225)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5DF2\\u9078\\u64C7: \", ctx_r1.getSelectedCount(), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.getSelectedCount());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maxSelections);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedBuilding);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.searchTerm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedHouseholds.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.allowSearch && ctx_r1.searchTerm);\n  }\n}\nfunction HouseholdBindingComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.floors = []; // 新增：當前棟別的樓層列表\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    if (this.buildCaseId) {\n      // 使用API載入資料\n      this.loadBuildingDataFromApi();\n    } else {\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n        this.buildingData = this.generateMockData();\n      }\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with buildings:', this.buildings);\n      this.updateSelectedByBuilding();\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // 如果API載入失敗，使用mock資料作為備援\n        this.buildingData = this.generateMockData();\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        code: house.HouseName,\n        building: house.Building,\n        floor: house.Floor,\n        houseId: house.HouseId,\n        houseName: house.HouseName,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.code);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棟別的樓層列表\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棟別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.code === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.code === householdCode);\n      if (household) {\n        return {\n          code: household.code,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      code: householdCode,\n      floor: ''\n    };\n  }\n  static {\n    this.ɵfac = function HouseholdBindingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseholdBindingComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.HouseCustomService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HouseholdBindingComponent,\n      selectors: [[\"app-household-binding\"]],\n      inputs: {\n        placeholder: \"placeholder\",\n        maxSelections: \"maxSelections\",\n        disabled: \"disabled\",\n        buildCaseId: \"buildCaseId\",\n        buildingData: \"buildingData\",\n        showSelectedArea: \"showSelectedArea\",\n        allowSearch: \"allowSearch\",\n        allowBatchSelect: \"allowBatchSelect\",\n        excludedHouseholds: \"excludedHouseholds\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => HouseholdBindingComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature],\n      decls: 10,\n      vars: 10,\n      consts: [[1, \"household-binding-container\"], [\"class\", \"selected-households-area\", 4, \"ngIf\"], [1, \"selector-container\", 2, \"position\", \"relative\"], [\"type\", \"button\", 1, \"selector-button\", 2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"padding\", \"0.5rem 0.75rem\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"0.375rem\", \"background-color\", \"#fff\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [1, \"selector-text\"], [4, \"ngIf\"], [\"icon\", \"chevron-down-outline\", 1, \"chevron-icon\"], [\"style\", \"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\", 4, \"ngIf\"], [\"class\", \"backdrop\", 3, \"click\", 4, \"ngIf\"], [1, \"selected-households-area\"], [1, \"selected-header\"], [1, \"selected-info\"], [\"icon\", \"people-outline\", 1, \"text-primary\"], [1, \"selected-count\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"selected-content\"], [\"class\", \"building-group\", 4, \"ngFor\", \"ngForOf\"], [1, \"building-group\"], [1, \"building-label\"], [1, \"households-tags\"], [\"class\", \"household-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"household-tag\"], [1, \"household-info\"], [1, \"household-code\"], [\"class\", \"household-floor\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-btn\", 3, \"click\", \"disabled\"], [\"icon\", \"close-outline\"], [1, \"household-floor\"], [\"icon\", \"loader-outline\", 1, \"spin\"], [2, \"position\", \"absolute\", \"top\", \"calc(100% + 4px)\", \"left\", \"0\", \"right\", \"0\", \"z-index\", \"99999\", \"background\", \"white\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"8px\", \"box-shadow\", \"0 4px 12px rgba(0,0,0,0.15)\", \"padding\", \"0\", \"margin\", \"0\", \"max-height\", \"min(80vh, 600px)\", \"overflow\", \"hidden\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\"], [\"icon\", \"home-outline\", 2, \"color\", \"#007bff\"], [2, \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"font-size\", \"0.875rem\", \"color\", \"#6c757d\"], [2, \"display\", \"flex\", \"height\", \"calc(100% - 160px)\", \"min-height\", \"300px\", \"max-height\", \"400px\"], [\"style\", \"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\", 4, \"ngIf\"], [2, \"padding\", \"16px\", \"border-top\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"16px\", \"font-size\", \"0.875rem\", \"color\", \"#495057\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\"], [\"icon\", \"checkmark-circle-outline\", 2, \"color\", \"#28a745\"], [\"style\", \"display: flex; align-items: center; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"gap\", \"8px\"], [2, \"display\", \"flex\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"style\", \"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"8px 16px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"8px 20px\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"checkmark-outline\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", \"padding\", \"40px\"], [2, \"text-align\", \"center\", \"color\", \"#6c757d\"], [\"icon\", \"loader-outline\", 1, \"spin\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\"], [2, \"width\", \"40%\", \"border-right\", \"1px solid #e9ecef\", \"background-color\", \"#f8f9fa\"], [2, \"padding\", \"12px 16px\", \"border-bottom\", \"1px solid #e9ecef\"], [2, \"margin\", \"0\", \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [2, \"max-height\", \"calc(100% - 52px)\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\", 3, \"background-color\", \"border-left\", \"click\", 4, \"ngFor\", \"ngForOf\"], [2, \"flex\", \"1\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", \"margin-bottom\", \"8px\"], [\"style\", \"color: #007bff;\", 4, \"ngIf\"], [\"style\", \"color: #28a745; font-size: 0.75rem;\", 4, \"ngIf\"], [\"style\", \"display: flex; gap: 4px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 8px;\", 4, \"ngIf\"], [\"style\", \"margin-top: 12px;\", 4, \"ngIf\"], [2, \"flex\", \"1\", \"padding\", \"16px\", \"overflow-y\", \"auto\"], [\"style\", \"text-align: center; padding: 40px 20px; color: #6c757d;\", 4, \"ngIf\"], [\"style\", \"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"width\", \"100%\", \"text-align\", \"left\", \"padding\", \"12px 16px\", \"border\", \"none\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"space-between\", 3, \"click\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"2px 6px\", \"border-radius\", \"10px\"], [2, \"color\", \"#007bff\"], [2, \"color\", \"#28a745\", \"font-size\", \"0.75rem\"], [2, \"display\", \"flex\", \"gap\", \"4px\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\", \"disabled\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"font-size\", \"0.75rem\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"margin-top\", \"8px\"], [2, \"position\", \"relative\"], [\"type\", \"text\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u5225\\u4EE3\\u78BC...\", 2, \"width\", \"100%\", \"padding\", \"6px 32px 6px 12px\", \"border\", \"1px solid #ced4da\", \"border-radius\", \"4px\", \"font-size\", \"0.875rem\", \"outline\", \"none\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"icon\", \"search-outline\", 2, \"position\", \"absolute\", \"right\", \"10px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [\"style\", \"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\", 4, \"ngIf\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#dc3545\", \"margin-top\", \"4px\"], [2, \"margin-top\", \"12px\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"8px\", \"margin-bottom\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#6c757d\", \"font-size\", \"0.875rem\"], [2, \"font-size\", \"0.875rem\", \"font-weight\", \"500\", \"color\", \"#495057\"], [\"type\", \"button\", \"style\", \"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\", 3, \"click\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"flex-wrap\", \"wrap\", \"gap\", \"4px\", \"max-height\", \"100px\", \"overflow-y\", \"auto\"], [\"type\", \"button\", \"style\", \"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\", 3, \"background-color\", \"color\", \"border-color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"font-size\", \"0.75rem\", \"color\", \"#007bff\", \"background\", \"none\", \"border\", \"none\", \"text-decoration\", \"underline\", \"cursor\", \"pointer\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"4px 8px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"font-size\", \"0.75rem\", \"cursor\", \"pointer\", \"transition\", \"all 0.15s ease\", \"white-space\", \"nowrap\", 3, \"click\"], [2, \"text-align\", \"center\", \"padding\", \"40px 20px\", \"color\", \"#6c757d\"], [\"icon\", \"home-outline\", 2, \"font-size\", \"2rem\", \"margin-bottom\", \"8px\", \"opacity\", \"0.5\"], [2, \"display\", \"grid\", \"grid-template-columns\", \"repeat(auto-fill, minmax(90px, 1fr))\", \"gap\", \"8px\"], [\"type\", \"button\", \"style\", \"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\", 3, \"disabled\", \"background-color\", \"color\", \"border-color\", \"opacity\", \"cursor\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 2, \"padding\", \"6px 4px\", \"border\", \"1px solid\", \"border-radius\", \"4px\", \"transition\", \"all 0.15s ease\", \"font-size\", \"0.75rem\", \"text-align\", \"center\", \"min-height\", \"40px\", \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", 3, \"click\", \"disabled\"], [2, \"font-weight\", \"500\", \"line-height\", \"1.2\"], [\"style\", \"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\", 4, \"ngIf\"], [\"style\", \"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\", 4, \"ngIf\"], [2, \"font-size\", \"0.6rem\", \"opacity\", \"0.8\", \"margin-top\", \"2px\"], [2, \"position\", \"absolute\", \"top\", \"-8px\", \"right\", \"-8px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border-radius\", \"50%\", \"width\", \"16px\", \"height\", \"16px\", \"font-size\", \"10px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\"], [\"icon\", \"alert-circle-outline\", 2, \"color\", \"#ffc107\"], [\"style\", \"color: #28a745; margin-left: 8px;\", 4, \"ngIf\"], [2, \"color\", \"#28a745\", \"margin-left\", \"8px\"], [\"icon\", \"layers-outline\", 2, \"color\", \"#28a745\"], [2, \"font-size\", \"0.75rem\", \"color\", \"#6c757d\", \"background-color\", \"#e9ecef\", \"padding\", \"4px 8px\", \"border-radius\", \"12px\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#dc3545\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"trash-2-outline\"], [\"type\", \"button\", 2, \"padding\", \"8px 12px\", \"background\", \"#6c757d\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.875rem\", \"display\", \"flex\", \"align-items\", \"center\", \"gap\", \"4px\", 3, \"click\"], [\"icon\", \"refresh-outline\"], [1, \"backdrop\", 3, \"click\"]],\n      template: function HouseholdBindingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, HouseholdBindingComponent_div_1_Template, 10, 3, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function HouseholdBindingComponent_Template_button_click_3_listener() {\n            return ctx.toggleDropdown();\n          });\n          i0.ɵɵelementStart(4, \"span\", 4);\n          i0.ɵɵtemplate(5, HouseholdBindingComponent_ng_container_5_Template, 3, 0, \"ng-container\", 5)(6, HouseholdBindingComponent_ng_container_6_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"nb-icon\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, HouseholdBindingComponent_div_8_Template, 37, 10, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, HouseholdBindingComponent_div_9_Template, 1, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSelectedArea && ctx.selectedHouseholds.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"rotated\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.NbIconComponent],\n      styles: [\".household-binding-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  padding: 1rem;\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 0.375rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n  min-width: 3rem;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  font-weight: 500;\\n  margin-top: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-size: 0.75rem;\\n  border-radius: 1.25rem;\\n  border: 1px solid #bbdefb;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 0.1rem;\\n  line-height: 1.2;\\n  min-width: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #0d47a1;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .household-info[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  font-size: 0.65rem;\\n  opacity: 0.8;\\n  font-weight: 400;\\n  color: #1565c0;\\n  background-color: rgba(255, 255, 255, 0.3);\\n  padding: 0.1rem 0.25rem;\\n  border-radius: 0.25rem;\\n  min-width: fit-content;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 0;\\n  margin: 0;\\n  cursor: pointer;\\n  color: #1976d2;\\n  border-radius: 50%;\\n  width: 1rem;\\n  height: 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #bbdefb;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .households-tags[_ngcontent-%COMP%]   .household-tag[_ngcontent-%COMP%]   .remove-btn[_ngcontent-%COMP%]   nb-icon[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 0.5rem 0.75rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n  border-color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.65;\\n  cursor: not-allowed;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .selector-text[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  transition: transform 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]   .chevron-icon.rotated[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: calc(100% + 0.25rem);\\n  left: 0;\\n  right: 0;\\n  z-index: 1050;\\n  background-color: #fff;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.375rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n  max-height: 24rem;\\n  overflow: hidden;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 20rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n  width: 33.333%;\\n  border-right: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .sidebar-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n  max-height: 17rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: left;\\n  padding: 0.75rem;\\n  border: none;\\n  background: none;\\n  cursor: pointer;\\n  transition: background-color 0.15s ease-in-out;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e3f2fd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]   .building-info[_ngcontent-%COMP%]   .building-count[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n  width: 66.667%;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-bottom: 1px solid #e9ecef;\\n  background-color: #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]   .building-indicator[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  max-height: 14rem;\\n  overflow-y: auto;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  color: #adb5bd;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 0.25rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #ced4da;\\n  border-radius: 0.25rem;\\n  background-color: #fff;\\n  cursor: pointer;\\n  transition: all 0.15s ease-in-out;\\n  text-align: center;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border-color: #1976d2;\\n  color: #1976d2;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n  cursor: not-allowed;\\n  opacity: 0.65;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-code[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]   .household-floor[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin-top: 0.125rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  border-top: 1px solid #e9ecef;\\n  background-color: #f8f9fa;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .max-selections-text[_ngcontent-%COMP%], \\n.household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]   .footer-info[_ngcontent-%COMP%]   .current-selections-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n}\\n.household-binding-container[_ngcontent-%COMP%]   .backdrop[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 1040;\\n  background: transparent;\\n}\\n\\n@media (max-width: 768px) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: auto;\\n    max-height: 20rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-height: 8rem;\\n    border-right: none;\\n    border-bottom: 1px solid #e9ecef;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%] {\\n    max-height: 5rem;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-header[_ngcontent-%COMP%]   .selected-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selected-households-area[_ngcontent-%COMP%]   .selected-content[_ngcontent-%COMP%]   .building-group[_ngcontent-%COMP%]   .building-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .selector-button.disabled[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%] {\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item[_ngcontent-%COMP%]:hover {\\n    background-color: #495057;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .buildings-sidebar[_ngcontent-%COMP%]   .buildings-list[_ngcontent-%COMP%]   .building-item.active[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%] {\\n    background-color: #495057;\\n    border-color: #6c757d;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\\n    border-color: #6c757d;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border-color: #adb5bd;\\n    color: #f8f9fa;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button[_ngcontent-%COMP%]:hover:not(.disabled) {\\n    background-color: #5a6268;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-content[_ngcontent-%COMP%]   .households-main[_ngcontent-%COMP%]   .households-grid[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .household-button.selected[_ngcontent-%COMP%] {\\n    background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: #fff;\\n  }\\n  .household-binding-container[_ngcontent-%COMP%]   .selector-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%] {\\n    background-color: #343a40;\\n    border-color: #495057;\\n    color: #f8f9fa;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getHouseholdInfo", "householdCode_r4", "floor", "ɵɵtemplate", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_span_4_Template", "ɵɵlistener", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template_button_click_5_listener", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRemoveHousehold", "ɵɵelement", "ɵɵtextInterpolate", "ɵɵproperty", "disabled", "ɵɵelementContainerStart", "HouseholdBindingComponent_div_1_div_9_ng_container_1_span_4_Template", "building_r5", "getBuildingSelectedHouseholds", "HouseholdBindingComponent_div_1_div_9_ng_container_1_Template", "hasBuildingSelected", "HouseholdBindingComponent_div_1_Template_button_click_6_listener", "_r1", "onClearAll", "HouseholdBindingComponent_div_1_div_9_Template", "getSelectedCount", "buildings", "placeholder", "HouseholdBindingComponent_div_8_ng_container_13_button_6_Template_button_click_0_listener", "building_r8", "_r7", "onBuildingSelect", "ɵɵstyleProp", "selectedBuilding", "getBuildingCount", "selectedF<PERSON>or", "HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template_button_click_0_listener", "_r10", "onUnselectAllBuilding", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_1_listener", "_r9", "onSelectAllFiltered", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template_button_click_3_listener", "onSelectAllBuilding", "HouseholdBindingComponent_div_8_ng_container_13_div_14_button_5_Template", "canSelectMore", "isSomeBuildingSelected", "searchTerm", "ɵɵtwoWayListener", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_ngModelChange_2_listener", "$event", "_r11", "ɵɵtwoWayBindingSet", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template_input_input_2_listener", "onSearchChange", "HouseholdBindingComponent_div_8_ng_container_13_div_15_div_4_Template", "ɵɵtwoWayProperty", "filteredHouseholds", "length", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template_button_click_0_listener", "_r12", "onFloorSelect", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template_button_click_0_listener", "floor_r14", "_r13", "ɵɵtextInterpolate2", "getFloorCount", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_5_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_16_button_7_Template", "floors", "getHouseholdFloor", "householdCode_r16", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template_button_click_0_listener", "_r15", "onHouseholdToggle", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_span_3_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_div_4_Template", "isHouseholdSelected", "isHouseholdExcluded", "isHouseholdDisabled", "HouseholdBindingComponent_div_8_ng_container_13_div_19_button_1_Template", "HouseholdBindingComponent_div_8_ng_container_13_button_6_Template", "HouseholdBindingComponent_div_8_ng_container_13_span_12_Template", "HouseholdBindingComponent_div_8_ng_container_13_span_13_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_14_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_15_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_16_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_18_Template", "HouseholdBindingComponent_div_8_ng_container_13_div_19_Template", "allowBatchSelect", "allowSearch", "maxSelections", "HouseholdBindingComponent_div_8_div_25_span_6_Template", "HouseholdBindingComponent_div_8_button_29_Template_button_click_0_listener", "_r17", "HouseholdBindingComponent_div_8_button_30_Template_button_click_0_listener", "_r18", "resetSearch", "HouseholdBindingComponent_div_8_div_12_Template", "HouseholdBindingComponent_div_8_ng_container_13_Template", "HouseholdBindingComponent_div_8_div_24_Template", "HouseholdBindingComponent_div_8_div_25_Template", "HouseholdBindingComponent_div_8_div_26_Template", "HouseholdBindingComponent_div_8_button_29_Template", "HouseholdBindingComponent_div_8_button_30_Template", "HouseholdBindingComponent_div_8_Template_button_click_32_listener", "_r6", "closeDropdown", "HouseholdBindingComponent_div_8_Template_button_click_34_listener", "isLoading", "selectedHouseholds", "HouseholdBindingComponent_div_9_Template_div_click_0_listener", "_r19", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "buildCaseId", "buildingData", "showSelectedArea", "excludedHouseholds", "selectionChange", "isOpen", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "updateFilteredHouseholds", "console", "log", "loadBuildingDataFromApi", "generateMockData", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "code", "HouseName", "Building", "Floor", "houseId", "HouseId", "houseName", "isSelected", "grouped", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "codes", "Math", "parseInt", "slice", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "event", "target", "householdCode", "newSelection", "emitChanges", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "buildingHouseholds", "unselectedBuilding", "selectedItems", "emit", "toggleDropdown", "isAllBuildingSelected", "every", "some", "getSelectedByBuilding", "floorSet", "Set", "household", "add", "sort", "a", "b", "numA", "replace", "numB", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "HouseCustomService", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "HouseholdBindingComponent_Template", "rf", "ctx", "HouseholdBindingComponent_div_1_Template", "HouseholdBindingComponent_Template_button_click_3_listener", "HouseholdBindingComponent_ng_container_5_Template", "HouseholdBindingComponent_ng_container_6_Template", "HouseholdBindingComponent_div_8_Template", "HouseholdBindingComponent_div_9_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  houseName?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棟別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  }\r\n\r\n  private initializeData() {\r\n    if (this.buildCaseId) {\r\n      // 使用API載入資料\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 如果沒有提供建案ID且沒有提供 buildingData，使用 mock 資料\r\n      if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n        this.buildingData = this.generateMockData();\r\n      }\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with buildings:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // 如果API載入失敗，使用mock資料作為備援\r\n        this.buildingData = this.generateMockData();\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: HouseItem[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        code: house.HouseName,\r\n        building: house.Building,\r\n        floor: house.Floor,\r\n        houseId: house.HouseId,\r\n        houseName: house.HouseName,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.code.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.code);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棟別的樓層列表\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棟別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.code === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { code: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.code === householdCode);\r\n      if (household) {\r\n        return {\r\n          code: household.code,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { code: householdCode, floor: '' };\r\n  }\r\n}\r\n", "<div class=\"household-binding-container\">\r\n  <!-- 已選擇戶別顯示區域 -->\r\n  <div *ngIf=\"showSelectedArea && selectedHouseholds.length > 0\" class=\"selected-households-area\">\r\n    <div class=\"selected-header\">\r\n      <div class=\"selected-info\">\r\n        <nb-icon icon=\"people-outline\" class=\"text-primary\"></nb-icon>\r\n        <span class=\"selected-count\">已選擇戶別 ({{getSelectedCount()}})</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" [disabled]=\"disabled\" (click)=\"onClearAll()\">\r\n        清空全部\r\n      </button>\r\n    </div>\r\n    <div class=\"selected-content\">\r\n      <div *ngFor=\"let building of buildings\" class=\"building-group\">\r\n        <ng-container *ngIf=\"hasBuildingSelected(building)\">\r\n          <div class=\"building-label\">{{building}}:</div>          <div class=\"households-tags\">\r\n            <span *ngFor=\"let householdCode of getBuildingSelectedHouseholds(building)\" class=\"household-tag\">\r\n              <div class=\"household-info\">\r\n                <span class=\"household-code\">{{householdCode}}</span>\r\n                <span *ngIf=\"getHouseholdInfo(householdCode).floor\" class=\"household-floor\">\r\n                  {{getHouseholdInfo(householdCode).floor}}\r\n                </span>\r\n              </div>\r\n              <button type=\"button\" class=\"remove-btn\" [disabled]=\"disabled\" (click)=\"onRemoveHousehold(householdCode)\">\r\n                <nb-icon icon=\"close-outline\"></nb-icon>\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n  </div> <!-- 選擇器 -->\r\n  <div class=\"selector-container\" style=\"position: relative;\">\r\n    <button type=\"button\" class=\"selector-button\" [class.disabled]=\"disabled || isLoading\"\r\n      [disabled]=\"disabled || isLoading\" (click)=\"toggleDropdown()\"\r\n      style=\"width: 100%; display: flex; align-items: center; justify-content: space-between; padding: 0.5rem 0.75rem; border: 1px solid #ced4da; border-radius: 0.375rem; background-color: #fff; cursor: pointer;\">\r\n      <span class=\"selector-text\">\r\n        <ng-container *ngIf=\"isLoading\">\r\n          <nb-icon icon=\"loader-outline\" class=\"spin\"></nb-icon>\r\n          載入中...\r\n        </ng-container>\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          {{getSelectedCount() > 0 ? '已選擇 ' + getSelectedCount() + ' 個戶別' : placeholder}}\r\n        </ng-container>\r\n      </span>\r\n      <nb-icon icon=\"chevron-down-outline\" [class.rotated]=\"isOpen\" class=\"chevron-icon\">\r\n      </nb-icon>\r\n    </button> <!-- 下拉選單 --> <!-- 下拉選單 - 完全簡化版本 --> <!-- 下拉選單 -->\r\n    <div *ngIf=\"isOpen\"\r\n      style=\"position: absolute; top: calc(100% + 4px); left: 0; right: 0; z-index: 99999; background: white; border: 1px solid #ced4da; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 0; margin: 0; max-height: min(80vh, 600px); overflow: hidden;\">\r\n\r\n      <!-- 標題區域 -->\r\n      <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n          <div style=\"display: flex; align-items: center; gap: 8px;\">\r\n            <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n            <span style=\"font-weight: 500; color: #495057;\">選擇戶別</span>\r\n            <span style=\"font-size: 0.875rem; color: #6c757d;\">({{buildings.length}} 個棟別)</span>\r\n          </div>\r\n          <span style=\"font-size: 0.875rem; color: #6c757d;\">已選擇: {{getSelectedCount()}}</span>\r\n        </div>\r\n      </div> <!-- 主要內容區域 -->\r\n      <div style=\"display: flex; height: calc(100% - 160px); min-height: 300px; max-height: 400px;\">\r\n\r\n        <!-- 載入狀態 -->\r\n        <div *ngIf=\"isLoading\"\r\n          style=\"width: 100%; display: flex; align-items: center; justify-content: center; padding: 40px;\">\r\n          <div style=\"text-align: center; color: #6c757d;\">\r\n            <nb-icon icon=\"loader-outline\" class=\"spin\" style=\"font-size: 2rem; margin-bottom: 8px;\"></nb-icon>\r\n            <p style=\"margin: 0; font-size: 0.875rem;\">載入戶別資料中...</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 正常內容 -->\r\n        <ng-container *ngIf=\"!isLoading\">\r\n          <!-- 棟別選擇側邊欄 -->\r\n          <div style=\"width: 40%; border-right: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n            <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n              <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">棟別列表</h6>\r\n            </div>\r\n            <div style=\"max-height: calc(100% - 52px); overflow-y: auto;\">\r\n              <button *ngFor=\"let building of buildings\" type=\"button\" (click)=\"onBuildingSelect(building)\"\r\n                [style.background-color]=\"selectedBuilding === building ? '#e3f2fd' : 'transparent'\"\r\n                [style.border-left]=\"selectedBuilding === building ? '3px solid #007bff' : '3px solid transparent'\"\r\n                style=\"width: 100%; text-align: left; padding: 12px 16px; border: none; cursor: pointer; transition: all 0.15s ease; display: flex; align-items: center; justify-content: space-between;\">\r\n                <span style=\"font-weight: 500; color: #495057;\">{{building}}</span>\r\n                <span\r\n                  style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 2px 6px; border-radius: 10px;\">{{getBuildingCount(building)}}戶</span>\r\n              </button>\r\n            </div>\r\n          </div><!-- 戶別選擇主區域 -->\r\n          <div style=\"flex: 1; display: flex; flex-direction: column;\">\r\n            <div style=\"padding: 12px 16px; border-bottom: 1px solid #e9ecef;\">\r\n              <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;\">\r\n                <h6 style=\"margin: 0; font-size: 0.875rem; font-weight: 500; color: #495057;\">\r\n                  戶別選擇\r\n                  <span *ngIf=\"selectedBuilding\" style=\"color: #007bff;\">({{selectedBuilding}})</span>\r\n                  <span *ngIf=\"selectedFloor\" style=\"color: #28a745; font-size: 0.75rem;\"> - {{selectedFloor}}</span>\r\n                </h6>\r\n                <div *ngIf=\"allowBatchSelect && selectedBuilding && filteredHouseholds.length > 0\"\r\n                  style=\"display: flex; gap: 4px;\">\r\n                  <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllFiltered()\"\r\n                    [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    全選當前\r\n                  </button>\r\n                  <button type=\"button\" [disabled]=\"!canSelectMore()\" (click)=\"onSelectAllBuilding()\"\r\n                    [style.opacity]=\"canSelectMore() ? '1' : '0.5'\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    全選{{selectedBuilding}}\r\n                  </button>\r\n                  <button type=\"button\" *ngIf=\"isSomeBuildingSelected()\" (click)=\"onUnselectAllBuilding()\"\r\n                    style=\"padding: 4px 8px; font-size: 0.75rem; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;\">\r\n                    清除\r\n                  </button>\r\n                </div>\r\n              </div> <!-- 搜尋框 -->\r\n              <div *ngIf=\"allowSearch && selectedBuilding\" style=\"margin-top: 8px;\">\r\n                <div style=\"position: relative;\">\r\n                  <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange($event)\" placeholder=\"搜尋戶別代碼...\"\r\n                    style=\"width: 100%; padding: 6px 32px 6px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.875rem; outline: none;\">\r\n                  <nb-icon icon=\"search-outline\"\r\n                    style=\"position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                </div>\r\n                <div *ngIf=\"searchTerm && filteredHouseholds.length === 0\"\r\n                  style=\"font-size: 0.75rem; color: #dc3545; margin-top: 4px;\">\r\n                  找不到符合 \"{{searchTerm}}\" 的戶別\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 樓層篩選器 -->\r\n              <div *ngIf=\"selectedBuilding && floors.length > 1\" style=\"margin-top: 12px;\">\r\n                <div style=\"display: flex; align-items: center; gap: 8px; margin-bottom: 8px;\">\r\n                  <nb-icon icon=\"layers-outline\" style=\"color: #6c757d; font-size: 0.875rem;\"></nb-icon>\r\n                  <span style=\"font-size: 0.875rem; font-weight: 500; color: #495057;\">樓層篩選:</span>\r\n                  <button type=\"button\" *ngIf=\"selectedFloor\" (click)=\"onFloorSelect('')\"\r\n                    style=\"font-size: 0.75rem; color: #007bff; background: none; border: none; text-decoration: underline; cursor: pointer;\">\r\n                    清除篩選\r\n                  </button>\r\n                </div>\r\n                <div style=\"display: flex; flex-wrap: wrap; gap: 4px; max-height: 100px; overflow-y: auto;\">\r\n                  <button type=\"button\" *ngFor=\"let floor of floors\" (click)=\"onFloorSelect(floor)\"\r\n                    [style.background-color]=\"selectedFloor === floor ? '#007bff' : '#f8f9fa'\"\r\n                    [style.color]=\"selectedFloor === floor ? '#fff' : '#495057'\"\r\n                    [style.border-color]=\"selectedFloor === floor ? '#007bff' : '#ced4da'\"\r\n                    style=\"padding: 4px 8px; border: 1px solid; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.15s ease; white-space: nowrap;\">\r\n                    {{floor}} ({{getFloorCount(floor)}})\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 戶別網格或空狀態 -->\r\n            <div style=\"flex: 1; padding: 16px; overflow-y: auto;\">\r\n              <div *ngIf=\"!selectedBuilding\" style=\"text-align: center; padding: 40px 20px; color: #6c757d;\">\r\n                <nb-icon icon=\"home-outline\" style=\"font-size: 2rem; margin-bottom: 8px; opacity: 0.5;\"></nb-icon>\r\n                <p style=\"margin: 0; font-size: 0.875rem;\">請先選擇棟別</p>\r\n              </div>\r\n              <div *ngIf=\"selectedBuilding && filteredHouseholds.length > 0\"\r\n                style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); gap: 8px;\">\r\n                <button *ngFor=\"let householdCode of filteredHouseholds\" type=\"button\"\r\n                  (click)=\"onHouseholdToggle(householdCode)\" [disabled]=\"isHouseholdDisabled(householdCode)\"\r\n                  [style.background-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#f8f9fa' : '#fff')\"\r\n                  [style.color]=\"isHouseholdSelected(householdCode) ? '#fff' : (isHouseholdExcluded(householdCode) ? '#6c757d' : '#495057')\"\r\n                  [style.border-color]=\"isHouseholdSelected(householdCode) ? '#007bff' : (isHouseholdExcluded(householdCode) ? '#dee2e6' : '#ced4da')\"\r\n                  [style.opacity]=\"isHouseholdDisabled(householdCode) ? '0.6' : '1'\"\r\n                  [style.cursor]=\"isHouseholdDisabled(householdCode) ? 'not-allowed' : 'pointer'\"\r\n                  style=\"padding: 6px 4px; border: 1px solid; border-radius: 4px; transition: all 0.15s ease; font-size: 0.75rem; text-align: center; min-height: 40px; position: relative; display: flex; flex-direction: column; justify-content: center;\">\r\n                  <span [style.text-decoration]=\"isHouseholdExcluded(householdCode) ? 'line-through' : 'none'\"\r\n                    style=\"font-weight: 500; line-height: 1.2;\">\r\n                    {{householdCode}}\r\n                  </span>\r\n                  <span *ngIf=\"getHouseholdFloor(householdCode)\"\r\n                    style=\"font-size: 0.6rem; opacity: 0.8; margin-top: 2px;\">\r\n                    {{getHouseholdFloor(householdCode)}}\r\n                  </span>\r\n                  <div *ngIf=\"isHouseholdExcluded(householdCode)\"\r\n                    style=\"position: absolute; top: -8px; right: -8px; background: #dc3545; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center;\">\r\n                    ✕\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div> <!-- 優化的底部操作區 -->\r\n      <div style=\"padding: 16px; border-top: 1px solid #e9ecef; background-color: #f8f9fa;\">\r\n        <!-- 統計資訊行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;\">\r\n          <div style=\"display: flex; align-items: center; gap: 16px; font-size: 0.875rem; color: #495057;\">\r\n            <div style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-circle-outline\" style=\"color: #28a745;\"></nb-icon>\r\n              <span>已選擇: <strong>{{getSelectedCount()}}</strong> 個戶別</span>\r\n            </div>\r\n            <div *ngIf=\"maxSelections\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"alert-circle-outline\" style=\"color: #ffc107;\"></nb-icon>\r\n              <span>限制: 最多 <strong>{{maxSelections}}</strong> 個</span>\r\n            </div>\r\n            <div *ngIf=\"selectedBuilding\" style=\"display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"home-outline\" style=\"color: #007bff;\"></nb-icon>\r\n              <span>當前棟別: <strong>{{selectedBuilding}}</strong></span>\r\n              <span *ngIf=\"selectedFloor\" style=\"color: #28a745; margin-left: 8px;\">\r\n                <nb-icon icon=\"layers-outline\" style=\"color: #28a745;\"></nb-icon>\r\n                {{selectedFloor}}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <div *ngIf=\"searchTerm\"\r\n            style=\"font-size: 0.75rem; color: #6c757d; background-color: #e9ecef; padding: 4px 8px; border-radius: 12px;\">\r\n            搜尋: \"{{searchTerm}}\" ({{filteredHouseholds.length}} 個結果)\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 操作按鈕行 -->\r\n        <div style=\"display: flex; align-items: center; justify-content: space-between; gap: 8px;\">\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" *ngIf=\"selectedHouseholds.length > 0\" (click)=\"onClearAll()\"\r\n              style=\"padding: 8px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"trash-2-outline\"></nb-icon>\r\n              清空全部\r\n            </button>\r\n            <button type=\"button\" *ngIf=\"allowSearch && searchTerm\" (click)=\"resetSearch()\"\r\n              style=\"padding: 8px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"refresh-outline\"></nb-icon>\r\n              重置搜尋\r\n            </button>\r\n          </div>\r\n\r\n          <div style=\"display: flex; gap: 8px;\">\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem;\">\r\n              取消\r\n            </button>\r\n            <button type=\"button\" (click)=\"closeDropdown()\"\r\n              style=\"padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.875rem; font-weight: 500; display: flex; align-items: center; gap: 4px;\">\r\n              <nb-icon icon=\"checkmark-outline\"></nb-icon>\r\n              確定選擇\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 點擊外部關閉下拉選單 -->\r\n  <div *ngIf=\"isOpen\" class=\"backdrop\" (click)=\"closeDropdown()\"></div>"], "mappings": "AAAA,SAAmCA,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;;;;;;;;ICkBxDC,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,MACF;;;;;;IAHAT,EAFJ,CAAAC,cAAA,eAAkG,cACpE,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrDH,EAAA,CAAAU,UAAA,IAAAC,2EAAA,mBAA4E;IAG9EX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA0G;IAA3CD,EAAA,CAAAY,UAAA,mBAAAC,6FAAA;MAAA,MAAAL,gBAAA,GAAAR,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAa,iBAAA,CAAAX,gBAAA,CAAgC;IAAA,EAAC;IACvGR,EAAA,CAAAoB,SAAA,kBAAwC;IAE5CpB,EADE,CAAAG,YAAA,EAAS,EACJ;;;;;IAR0BH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAb,gBAAA,CAAiB;IACvCR,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAC,gBAAA,CAAAC,gBAAA,EAAAC,KAAA,CAA2C;IAIXT,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,QAAA,CAAqB;;;;;IATpEvB,EAAA,CAAAwB,uBAAA,GAAoD;IAClDxB,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAAUH,EAAA,CAAAC,cAAA,cAA6B;IACpFD,EAAA,CAAAU,UAAA,IAAAe,oEAAA,mBAAkG;IAWpGzB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZsBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,kBAAA,KAAAqB,WAAA,MAAa;IACP1B,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAqB,6BAAA,CAAAD,WAAA,EAA0C;;;;;IAHhF1B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAU,UAAA,IAAAkB,6DAAA,0BAAoD;IAetD5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAfWH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAuB,mBAAA,CAAAH,WAAA,EAAmC;;;;;;IAVpD1B,EAFJ,CAAAC,cAAA,aAAgG,cACjE,cACA;IACzBD,EAAA,CAAAoB,SAAA,kBAA8D;IAC9DpB,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IACNH,EAAA,CAAAC,cAAA,iBAAyG;IAAvBD,EAAA,CAAAY,UAAA,mBAAAkB,iEAAA;MAAA9B,EAAA,CAAAc,aAAA,CAAAiB,GAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0B,UAAA,EAAY;IAAA,EAAC;IACtGhC,EAAA,CAAAE,MAAA,iCACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAU,UAAA,IAAAuB,8CAAA,kBAA+D;IAkBnEjC,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAzB6BH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,qCAAAC,MAAA,CAAA4B,gBAAA,QAA8B;IAEDlC,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAiB,QAAA,CAAqB;IAKvDvB,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA6B,SAAA,CAAY;;;;;IAwBpCnC,EAAA,CAAAwB,uBAAA,GAAgC;IAC9BxB,EAAA,CAAAoB,SAAA,kBAAsD;IACtDpB,EAAA,CAAAE,MAAA,8BACF;;;;;;IACAF,EAAA,CAAAwB,uBAAA,GAAiC;IAC/BxB,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA4B,gBAAA,iCAAA5B,MAAA,CAAA4B,gBAAA,6BAAA5B,MAAA,CAAA8B,WAAA,MACF;;;;;IAwBEpC,EAFF,CAAAC,cAAA,cACmG,cAChD;IAC/CD,EAAA,CAAAoB,SAAA,kBAAmG;IACnGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,oDAAU;IAEzDF,EAFyD,CAAAG,YAAA,EAAI,EACrD,EACF;;;;;;IAUAH,EAAA,CAAAC,cAAA,iBAG4L;IAHnID,EAAA,CAAAY,UAAA,mBAAAyB,0FAAA;MAAA,MAAAC,WAAA,GAAAtC,EAAA,CAAAc,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAkC,gBAAA,CAAAF,WAAA,CAA0B;IAAA,EAAC;IAI3FtC,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,eACgH;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACjJF,EADiJ,CAAAG,YAAA,EAAO,EAC/I;;;;;IALPH,EADA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAoC,gBAAA,KAAAJ,WAAA,6BAAoF,gBAAAhC,MAAA,CAAAoC,gBAAA,KAAAJ,WAAA,iDACe;IAEnDtC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAqB,iBAAA,CAAAiB,WAAA,CAAY;IAEoDtC,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAqC,gBAAA,CAAAL,WAAA,YAA+B;;;;;IAS7ItC,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAoC,gBAAA,MAAsB;;;;;IAC7E1C,EAAA,CAAAC,cAAA,eAAwE;IAACD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1BH,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAK,kBAAA,QAAAC,MAAA,CAAAsC,aAAA,KAAmB;;;;;;IAc5F5C,EAAA,CAAAC,cAAA,iBACsI;IAD/ED,EAAA,CAAAY,UAAA,mBAAAiC,iGAAA;MAAA7C,EAAA,CAAAc,aAAA,CAAAgC,IAAA;MAAA,MAAAxC,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAyC,qBAAA,EAAuB;IAAA,EAAC;IAEtF/C,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAbTH,EAFF,CAAAC,cAAA,cACmC,iBAGqG;IAFlFD,EAAA,CAAAY,UAAA,mBAAAoC,wFAAA;MAAAhD,EAAA,CAAAc,aAAA,CAAAmC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA4C,mBAAA,EAAqB;IAAA,EAAC;IAGjFlD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAEsI;IAFlFD,EAAA,CAAAY,UAAA,mBAAAuC,wFAAA;MAAAnD,EAAA,CAAAc,aAAA,CAAAmC,GAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA8C,mBAAA,EAAqB;IAAA,EAAC;IAGjFpD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,IAAA2C,wEAAA,qBACsI;IAGxIrD,EAAA,CAAAG,YAAA,EAAM;;;;IAbFH,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAAgD,aAAA,iBAA+C;IAD3BtD,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAAgD,aAAA,GAA6B;IAMjDtD,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAyC,WAAA,YAAAnC,MAAA,CAAAgD,aAAA,iBAA+C;IAD3BtD,EAAA,CAAAsB,UAAA,cAAAhB,MAAA,CAAAgD,aAAA,GAA6B;IAGjDtD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAoC,gBAAA,MACF;IACuB1C,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiD,sBAAA,GAA8B;;;;;IAavDvD,EAAA,CAAAC,cAAA,cAC+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,uCAAAC,MAAA,CAAAkD,UAAA,2BACF;;;;;;IARExD,EAFJ,CAAAC,cAAA,cAAsE,cACnC,gBAEuG;IADnHD,EAAA,CAAAyD,gBAAA,2BAAAC,+FAAAC,MAAA;MAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAA6D,kBAAA,CAAAvD,MAAA,CAAAkD,UAAA,EAAAG,MAAA,MAAArD,MAAA,CAAAkD,UAAA,GAAAG,MAAA;MAAA,OAAA3D,EAAA,CAAAkB,WAAA,CAAAyC,MAAA;IAAA,EAAwB;IAAC3D,EAAA,CAAAY,UAAA,mBAAAkD,uFAAAH,MAAA;MAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA;MAAA,MAAAtD,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAyD,cAAA,CAAAJ,MAAA,CAAsB;IAAA,EAAC;IAA5E3D,EAAA,CAAAG,YAAA,EACsI;IACtIH,EAAA,CAAAoB,SAAA,kBACiI;IACnIpB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,IAAAsD,qEAAA,kBAC+D;IAGjEhE,EAAA,CAAAG,YAAA,EAAM;;;;IATiBH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAiE,gBAAA,YAAA3D,MAAA,CAAAkD,UAAA,CAAwB;IAKvCxD,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAkD,UAAA,IAAAlD,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,OAAmD;;;;;;IAWvDnE,EAAA,CAAAC,cAAA,iBAC2H;IAD/ED,EAAA,CAAAY,UAAA,mBAAAwD,iGAAA;MAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAc,EAAE,CAAC;IAAA,EAAC;IAErEtE,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAIyJ;IAJtGD,EAAA,CAAAY,UAAA,mBAAA2D,iGAAA;MAAA,MAAAC,SAAA,GAAAxE,EAAA,CAAAc,aAAA,CAAA2D,IAAA,EAAAzD,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAgE,aAAA,CAAAE,SAAA,CAAoB;IAAA,EAAC;IAK/ExE,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAFA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,yBAA0E,UAAAlE,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,sBACd,iBAAAlE,MAAA,CAAAsC,aAAA,KAAA4B,SAAA,yBACU;IAEtExE,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,MAAAF,SAAA,QAAAlE,MAAA,CAAAqE,aAAA,CAAAH,SAAA,QACF;;;;;IAfFxE,EADF,CAAAC,cAAA,cAA6E,cACI;IAC7ED,EAAA,CAAAoB,SAAA,kBAAsF;IACtFpB,EAAA,CAAAC,cAAA,eAAqE;IAAAD,EAAA,CAAAE,MAAA,gCAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,IAAAkE,wEAAA,qBAC2H;IAG7H5E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAU,UAAA,IAAAmE,wEAAA,qBAIyJ;IAI7J7E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAdqBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;IAMF5C,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAAwE,MAAA,CAAS;;;;;IAarD9E,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAoB,SAAA,kBAAkG;IAClGpB,EAAA,CAAAC,cAAA,YAA2C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IACnDF,EADmD,CAAAG,YAAA,EAAI,EACjD;;;;;IAeFH,EAAA,CAAAC,cAAA,gBAC4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAyE,iBAAA,CAAAC,iBAAA,OACF;;;;;IACAhF,EAAA,CAAAC,cAAA,eACsN;IACpND,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAnBRH,EAAA,CAAAC,cAAA,iBAO6O;IAN3OD,EAAA,CAAAY,UAAA,mBAAAqE,iGAAA;MAAA,MAAAD,iBAAA,GAAAhF,EAAA,CAAAc,aAAA,CAAAoE,IAAA,EAAAlE,SAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA6E,iBAAA,CAAAH,iBAAA,CAAgC;IAAA,EAAC;IAO1ChF,EAAA,CAAAC,cAAA,gBAC8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKPH,EAJA,CAAAU,UAAA,IAAA0E,+EAAA,oBAC4D,IAAAC,8EAAA,mBAI0J;IAGxNrF,EAAA,CAAAG,YAAA,EAAS;;;;;IAdPH,EAJA,CAAAyC,WAAA,qBAAAnC,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,gBAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,uBAAqI,UAAA1E,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,aAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,0BACX,iBAAA1E,MAAA,CAAAgF,mBAAA,CAAAN,iBAAA,gBAAA1E,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,0BACU,YAAA1E,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,gBAClE,WAAA1E,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,8BACa;IALpChF,EAAA,CAAAsB,UAAA,aAAAhB,MAAA,CAAAkF,mBAAA,CAAAR,iBAAA,EAA+C;IAOpFhF,EAAA,CAAAI,SAAA,EAAsF;IAAtFJ,EAAA,CAAAyC,WAAA,oBAAAnC,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,4BAAsF;IAE1FhF,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA2E,iBAAA,MACF;IACOhF,EAAA,CAAAI,SAAA,EAAsC;IAAtCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAyE,iBAAA,CAAAC,iBAAA,EAAsC;IAIvChF,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiF,mBAAA,CAAAP,iBAAA,EAAwC;;;;;IAlBlDhF,EAAA,CAAAC,cAAA,cACgG;IAC9FD,EAAA,CAAAU,UAAA,IAAA+E,wEAAA,sBAO6O;IAc/OzF,EAAA,CAAAG,YAAA,EAAM;;;;IArB8BH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA4D,kBAAA,CAAqB;;;;;IAtF/DlE,EAAA,CAAAwB,uBAAA,GAAiC;IAI3BxB,EAFJ,CAAAC,cAAA,cAAqF,cAChB,aACa;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACpFF,EADoF,CAAAG,YAAA,EAAK,EACnF;IACNH,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,UAAA,IAAAgF,iEAAA,qBAG4L;IAMhM1F,EADE,CAAAG,YAAA,EAAM,EACF;IAIAH,EAHN,CAAAC,cAAA,cAA6D,cACQ,cACoC,cACrB;IAC5ED,EAAA,CAAAE,MAAA,kCACA;IACAF,EADA,CAAAU,UAAA,KAAAiF,gEAAA,mBAAuD,KAAAC,gEAAA,mBACiB;IAC1E5F,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAU,UAAA,KAAAmF,+DAAA,kBACmC;IAgBrC7F,EAAA,CAAAG,YAAA,EAAM;IAeNH,EAdA,CAAAU,UAAA,KAAAoF,+DAAA,kBAAsE,KAAAC,+DAAA,kBAcO;IAmB/E/F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAuD;IAKrDD,EAJA,CAAAU,UAAA,KAAAsF,+DAAA,kBAA+F,KAAAC,+DAAA,kBAKC;IAwBpGjG,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAtG2BH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAsB,UAAA,YAAAhB,MAAA,CAAA6B,SAAA,CAAY;IAe9BnC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,CAAsB;IACtB1C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;IAEtB5C,EAAA,CAAAI,SAAA,EAA2E;IAA3EJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA4F,gBAAA,IAAA5F,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,KAA2E;IAkB7EnE,EAAA,CAAAI,SAAA,EAAqC;IAArCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA6F,WAAA,IAAA7F,MAAA,CAAAoC,gBAAA,CAAqC;IAcrC1C,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAAwE,MAAA,CAAAX,MAAA,KAA2C;IAuB3CnE,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAsB,UAAA,UAAAhB,MAAA,CAAAoC,gBAAA,CAAuB;IAIvB1C,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,KAAuD;;;;;IAoC/DnE,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAoB,SAAA,mBAAuE;IACvEpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,kCAAO;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,cAAC;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;;;;IADiBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA8F,aAAA,CAAiB;;;;;IAKtCpG,EAAA,CAAAC,cAAA,gBAAsE;IACpED,EAAA,CAAAoB,SAAA,mBAAiE;IACjEpB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsC,aAAA,MACF;;;;;IANF5C,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAoB,SAAA,kBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iCAAM;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAASF,EAAT,CAAAG,YAAA,EAAS,EAAO;IACxDH,EAAA,CAAAU,UAAA,IAAA2F,sDAAA,oBAAsE;IAIxErG,EAAA,CAAAG,YAAA,EAAM;;;;IALgBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAoC,gBAAA,CAAoB;IACjC1C,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAsC,aAAA,CAAmB;;;;;IAM9B5C,EAAA,CAAAC,cAAA,eACgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAA0E,kBAAA,sBAAApE,MAAA,CAAAkD,UAAA,UAAAlD,MAAA,CAAA4D,kBAAA,CAAAC,MAAA,0BACF;;;;;;IAMEnE,EAAA,CAAAC,cAAA,kBACsL;IAD1HD,EAAA,CAAAY,UAAA,mBAAA0F,2EAAA;MAAAtG,EAAA,CAAAc,aAAA,CAAAyF,IAAA;MAAA,MAAAjG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA0B,UAAA,EAAY;IAAA,EAAC;IAEhFhC,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBACsL;IAD9HD,EAAA,CAAAY,UAAA,mBAAA4F,2EAAA;MAAAxG,EAAA,CAAAc,aAAA,CAAA2F,IAAA;MAAA,MAAAnG,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAAoG,WAAA,EAAa;IAAA,EAAC;IAE7E1G,EAAA,CAAAoB,SAAA,mBAA0C;IAC1CpB,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA3KXH,EANN,CAAAC,cAAA,cAC0Q,cAG1K,cACX,cACpB;IACzDD,EAAA,CAAAoB,SAAA,kBAA+D;IAC/DpB,EAAA,CAAAC,cAAA,eAAgD;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAC/EF,EAD+E,CAAAG,YAAA,EAAO,EAChF;IACNH,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAElFF,EAFkF,CAAAG,YAAA,EAAO,EACjF,EACF;IACNH,EAAA,CAAAC,cAAA,eAA8F;IAY5FD,EATA,CAAAU,UAAA,KAAAiG,+CAAA,kBACmG,KAAAC,wDAAA,2BAQlE;IA+GnC5G,EAAA,CAAAG,YAAA,EAAM;IAKAH,EAJN,CAAAC,cAAA,eAAsF,eAEkB,eACH,eACpC;IACzDD,EAAA,CAAAoB,SAAA,mBAA2E;IAC3EpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4BAAK;IAAAF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAG;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAKNH,EAJA,CAAAU,UAAA,KAAAmG,+CAAA,kBAAiF,KAAAC,+CAAA,kBAIG;IAQtF9G,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAAqG,+CAAA,kBACgH;IAGlH/G,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA2F,eACnD;IAMpCD,EALA,CAAAU,UAAA,KAAAsG,kDAAA,qBACsL,KAAAC,kDAAA,qBAKA;IAIxLjH,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAsC,kBAEoG;IADlHD,EAAA,CAAAY,UAAA,mBAAAsG,kEAAA;MAAAlH,EAAA,CAAAc,aAAA,CAAAqG,GAAA;MAAA,MAAA7G,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA8G,aAAA,EAAe;IAAA,EAAC;IAE7CpH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACwM;IADlLD,EAAA,CAAAY,UAAA,mBAAAyG,kEAAA;MAAArH,EAAA,CAAAc,aAAA,CAAAqG,GAAA;MAAA,MAAA7G,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA8G,aAAA,EAAe;IAAA,EAAC;IAE7CpH,EAAA,CAAAoB,SAAA,mBAA4C;IAC5CpB,EAAA,CAAAE,MAAA,kCACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAxLqDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA6B,SAAA,CAAAgC,MAAA,yBAA0B;IAE5BnE,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,yBAAAC,MAAA,CAAA4B,gBAAA,OAA2B;IAM1ElC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAgH,SAAA,CAAe;IASNtH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,UAAAhB,MAAA,CAAAgH,SAAA,CAAgB;IAsHNtH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA4B,gBAAA,GAAsB;IAErClC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA8F,aAAA,CAAmB;IAInBpG,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAoC,gBAAA,CAAsB;IASxB1C,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAkD,UAAA,CAAgB;IASGxD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAAiH,kBAAA,CAAApD,MAAA,KAAmC;IAKnCnE,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAsB,UAAA,SAAAhB,MAAA,CAAA6F,WAAA,IAAA7F,MAAA,CAAAkD,UAAA,CAA+B;;;;;;IAwBhExD,EAAA,CAAAC,cAAA,eAA+D;IAA1BD,EAAA,CAAAY,UAAA,mBAAA4G,8DAAA;MAAAxH,EAAA,CAAAc,aAAA,CAAA2G,IAAA;MAAA,MAAAnH,MAAA,GAAAN,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASZ,MAAA,CAAA8G,aAAA,EAAe;IAAA,EAAC;IAACpH,EAAA,CAAAG,YAAA,EAAM;;;ADjNvE,OAAM,MAAOuH,yBAAyB;EA2BpCC,YACUC,GAAsB,EACtBC,kBAAsC;IADtC,KAAAD,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA5BnB,KAAAzF,WAAW,GAAW,OAAO;IAC7B,KAAAgE,aAAa,GAAkB,IAAI;IACnC,KAAA7E,QAAQ,GAAY,KAAK;IACzB,KAAAuG,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAA7B,WAAW,GAAY,IAAI;IAC3B,KAAAD,gBAAgB,GAAY,IAAI;IAChC,KAAA+B,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIrI,YAAY,EAAmB;IAC/D,KAAAsI,MAAM,GAAG,KAAK;IACd,KAAAzF,gBAAgB,GAAG,EAAE;IACrB,KAAAc,UAAU,GAAG,EAAE;IACf,KAAAZ,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAA2E,kBAAkB,GAAa,EAAE;IACjC,KAAApF,SAAS,GAAa,EAAE;IACxB,KAAA2C,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAZ,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAkE,kBAAkB,GAAqC,EAAE;IACzD,KAAAd,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAe,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACf,kBAAkB,GAAGe,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACvH,QAAQ,GAAGuH,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAACpB,WAAW,EAAE;MAC9C;MACA,IAAI,CAACkB,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAAC/G,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;MAC/C,IAAI,CAACsB,wBAAwB,EAAE;MAC/B,IAAI,CAACZ,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACG,wBAAwB,EAAE;MAC/BC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACtB,kBAAkB,CAAC;IACtE;EACF;EAEQe,cAAcA,CAAA;IACpB,IAAI,IAAI,CAAClB,WAAW,EAAE;MACpB;MACA,IAAI,CAAC0B,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACzB,YAAY,IAAIoB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC,CAAC5D,MAAM,KAAK,CAAC,EAAE;QACrE,IAAI,CAAC4D,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,EAAE;MAC7C;MACA,IAAI,CAACtH,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;MAC/CuB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACpH,SAAS,CAAC;MACpE,IAAI,CAACsG,wBAAwB,EAAE;IACjC;EACF;EAEQe,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE;IAEvB,IAAI,CAACR,SAAS,GAAG,IAAI;IACrB,IAAI,CAACO,kBAAkB,CAAC6B,WAAW,CAAC,IAAI,CAAC5B,WAAW,CAAC,CAAC6B,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,QAAQ,CAAC;QACtC,IAAI,CAAC9B,YAAY,GAAG,IAAI,CAAC+B,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC5H,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;QAC/C,IAAI,CAACU,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACoC,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAClC,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,EAAE;QAC3C,IAAI,CAACtH,SAAS,GAAGgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,YAAY,CAAC;QAC/C,IAAI,CAACU,wBAAwB,EAAE;QAC/B,IAAI,CAACnB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACM,GAAG,CAACoC,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEQF,gCAAgCA,CAACI,OAAuC;IAC9E,MAAMnC,YAAY,GAAiB,EAAE;IAErCoB,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDtC,YAAY,CAACqC,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,IAAI,EAAED,KAAK,CAACE,SAAS;QACrBL,QAAQ,EAAEG,KAAK,CAACG,QAAQ;QACxBjK,KAAK,EAAE8J,KAAK,CAACI,KAAK;QAClBC,OAAO,EAAEL,KAAK,CAACM,OAAO;QACtBC,SAAS,EAAEP,KAAK,CAACE,SAAS;QAC1BM,UAAU,EAAE,KAAK;QACjBjC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOf,YAAY;EACrB;EAEQU,wBAAwBA,CAAA;IAC9B,MAAMuC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACzD,kBAAkB,CAAC4C,OAAO,CAACK,IAAI,IAAG;MACrC,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACjI,SAAS,EAAE;QACrC,MAAM8I,IAAI,GAAG,IAAI,CAAClD,YAAY,CAACqC,QAAQ,CAAC,EAAEc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIS,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACZ,QAAQ,CAAC,EAAEY,OAAO,CAACZ,QAAQ,CAAC,GAAG,EAAE;UAC9CY,OAAO,CAACZ,QAAQ,CAAC,CAACgB,IAAI,CAACZ,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACpC,kBAAkB,GAAG4C,OAAO;EACnC;EACQvB,gBAAgBA,CAAA;IACtB;IACA,MAAM4B,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEpH,MAAM,EAAE;MAAE,CAAE,EAAE,CAACqH,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAM5D,YAAY,GAAiB,EAAE;IACrCoB,MAAM,CAACe,OAAO,CAACmB,cAAc,CAAC,CAAClB,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEwB,KAAK,CAAC,KAAI;MAC3D7D,YAAY,CAACqC,QAAQ,CAAC,GAAGwB,KAAK,CAACtB,GAAG,CAACE,IAAI,KAAK;QAC1CA,IAAI;QACJJ,QAAQ;QACR3J,KAAK,EAAE,GAAGoL,IAAI,CAACpL,KAAK,CAAC,CAACqL,QAAQ,CAACtB,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DhB,UAAU,EAAE,KAAK;QACjBjC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOf,YAAY;EACrB;EAAEvF,gBAAgBA,CAAC4H,QAAgB;IACjCd,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEa,QAAQ,CAAC;IAC3C,IAAI,CAAC1H,gBAAgB,GAAG0H,QAAQ;IAChC,IAAI,CAACxH,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACY,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwI,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAC3C,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACrF,kBAAkB,CAACC,MAAM,CAAC;IACzE;IACA,IAAI,CAACyD,GAAG,CAACoC,aAAa,EAAE;EAC1B;EAEAiC,eAAeA,CAAC7B,QAAgB;IAC9Bd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEa,QAAQ,CAAC;EACxD;EAAEf,wBAAwBA,CAAA;IACxBC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC7G,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACwB,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMgI,UAAU,GAAG,IAAI,CAACnE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE4G,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE2C,UAAU,CAAC/H,MAAM,CAAC;IAEpE;IACA,IAAI,CAACD,kBAAkB,GAAGgI,UAAU,CACjCC,MAAM,CAAChB,CAAC,IAAG;MACV;MACA,MAAMiB,UAAU,GAAG,CAAC,IAAI,CAACxJ,aAAa,IAAIuI,CAAC,CAAC1K,KAAK,KAAK,IAAI,CAACmC,aAAa;MACxE;MACA,MAAMyJ,WAAW,GAAGlB,CAAC,CAACX,IAAI,CAAC8B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC/I,UAAU,CAAC8I,WAAW,EAAE,CAAC;MAChF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACD/B,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC;IAEnBlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrF,kBAAkB,CAACC,MAAM,CAAC;EAC5E;EAEAJ,cAAcA,CAACyI,KAAU;IACvB,IAAI,CAAChJ,UAAU,GAAGgJ,KAAK,CAACC,MAAM,CAACnE,KAAK;IACpC,IAAI,CAACe,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC/F,UAAU,CAAC;EACtD;EAEAkD,WAAWA,CAAA;IACT,IAAI,CAAClD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC6F,wBAAwB,EAAE;IAC/BC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACApE,iBAAiBA,CAACuH,aAAqB;IACrC;IACA,IAAI,IAAI,CAACnH,mBAAmB,CAACmH,aAAa,CAAC,EAAE;MAC3CpD,OAAO,CAACC,GAAG,CAAC,MAAMmD,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAM3B,UAAU,GAAG,IAAI,CAACxD,kBAAkB,CAACgF,QAAQ,CAACG,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAI5B,UAAU,EAAE;MACd4B,YAAY,GAAG,IAAI,CAACpF,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACtG,aAAa,IAAI,IAAI,CAACmB,kBAAkB,CAACpD,MAAM,IAAI,IAAI,CAACiC,aAAa,EAAE;QAC9EkD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAoD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpF,kBAAkB,EAAEmF,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACnF,kBAAkB,GAAGoF,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAzL,iBAAiBA,CAACuL,aAAqB;IACrC,IAAI,CAACnF,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAIA,CAAC,KAAKuB,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EAAE1J,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACR,gBAAgB,IAAI,IAAI,CAACwB,kBAAkB,CAACC,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAM0I,YAAY,GAAG,IAAI,CAACtF,kBAAkB,CAACpD,MAAM;IACnD,MAAM2I,UAAU,GAAG,IAAI,CAAC1G,aAAa,IAAI2G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAC/I,kBAAkB,CAACiI,MAAM,CAAC3B,IAAI,IAC5D,CAAC,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACjF,mBAAmB,CAACiF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM0C,KAAK,GAAGD,kBAAkB,CAAClB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACoD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG2F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBtD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAAC/I,MAAM,MAAM,CAAC;IAC9C;EACF;EAEAf,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACV,gBAAgB,EAAE;IAE5B;IACA,MAAMyK,kBAAkB,GAAG,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAE3F;IACA,MAAMqC,YAAY,GAAG,IAAI,CAACtF,kBAAkB,CAACpD,MAAM;IACnD,MAAM2I,UAAU,GAAG,IAAI,CAAC1G,aAAa,IAAI2G,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMO,kBAAkB,GAAGD,kBAAkB,CAAChB,MAAM,CAAC3B,IAAI,IACvD,CAAC,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACjF,mBAAmB,CAACiF,IAAI,CAAC,CAC3E;IAED;IACA,MAAM0C,KAAK,GAAGE,kBAAkB,CAACrB,KAAK,CAAC,CAAC,EAAEiB,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAAC/I,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACoD,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAG2F,KAAK,CAAC;MAChE,IAAI,CAACN,WAAW,EAAE;MAClBtD,OAAO,CAACC,GAAG,CAAC,aAAa2D,KAAK,CAAC/I,MAAM,MAAM,CAAC;IAC9C;EACF;EACApB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACL,gBAAgB,EAAE;IAE5B,MAAMyK,kBAAkB,GAAG,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAACjD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC4E,MAAM,CAAChB,CAAC,IAAI,CAACgC,kBAAkB,CAACZ,QAAQ,CAACpB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACyB,WAAW,EAAE;EACpB;EACA5K,UAAUA,CAAA;IACR,IAAI,CAACuF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACqF,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACnE,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACd,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACgB,SAAS,EAAE;IAEhB,MAAM8E,aAAa,GAAG,IAAI,CAAC9F,kBAAkB,CAAC+C,GAAG,CAACE,IAAI,IAAG;MACvD,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACjI,SAAS,EAAE;QACrC,MAAM8I,IAAI,GAAG,IAAI,CAAClD,YAAY,CAACqC,QAAQ,CAAC,EAAEc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIS,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACkB,MAAM,CAAClB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAAC/C,eAAe,CAACoF,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChM,QAAQ,EAAE;MAClB,IAAI,CAAC4G,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1BmB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACpB,MAAM,CAAC;MACrDmB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACpH,SAAS,CAAC;IACrD;EACF;EAEAiF,aAAaA,CAAA;IACX,IAAI,CAACe,MAAM,GAAG,KAAK;EACrB;EAEA7C,mBAAmBA,CAACoH,aAAqB;IACvC,OAAO,IAAI,CAACnF,kBAAkB,CAACgF,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEAnH,mBAAmBA,CAACmH,aAAqB;IACvC,OAAO,IAAI,CAACzE,kBAAkB,CAACsE,QAAQ,CAACG,aAAa,CAAC;EACxD;EAEAlH,mBAAmBA,CAACkH,aAAqB;IACvC,OAAO,IAAI,CAACnH,mBAAmB,CAACmH,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACpJ,aAAa,EAAE,IAAI,CAAC,IAAI,CAACgC,mBAAmB,CAACoH,aAAa,CAAE;EACvE;EAEApJ,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAAC8C,aAAa,IAAI,IAAI,CAACmB,kBAAkB,CAACpD,MAAM,GAAG,IAAI,CAACiC,aAAa;EACnF;EAEAoH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9K,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMyK,kBAAkB,GAAG,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,CAChEyJ,MAAM,CAAChB,CAAC,IAAI,CAACA,CAAC,CAACrC,UAAU,CAAC,CAC1BwB,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC;IACnB,OAAO2C,kBAAkB,CAAChJ,MAAM,GAAG,CAAC,IAClCgJ,kBAAkB,CAACM,KAAK,CAACjD,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,CAAC;EAC5E;EACAjH,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACb,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMyK,kBAAkB,GAAG,IAAI,CAACpF,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,EAAE4H,GAAG,CAACa,CAAC,IAAIA,CAAC,CAACX,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAO2C,kBAAkB,CAACO,IAAI,CAAClD,IAAI,IAAI,IAAI,CAACjD,kBAAkB,CAACgF,QAAQ,CAAC/B,IAAI,CAAC,CAAC;EAChF;EACAmD,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACvF,kBAAkB;EAChC;EAEAzF,gBAAgBA,CAACyH,QAAgB;IAC/B,OAAO,IAAI,CAACrC,YAAY,CAACqC,QAAQ,CAAC,EAAEjG,MAAM,IAAI,CAAC;EACjD;EAEAjC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACqF,kBAAkB,CAACpD,MAAM;EACvC;EAEA;EACAxC,6BAA6BA,CAACyI,QAAgB;IAC5C,OAAO,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAvI,mBAAmBA,CAACuI,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,IAAI,IAAI,CAAChC,kBAAkB,CAACgC,QAAQ,CAAC,CAACjG,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ6H,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACtJ,gBAAgB,EAAE;MAC1B,IAAI,CAACoC,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMoH,UAAU,GAAG,IAAI,CAACnE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMkL,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElC3B,UAAU,CAAC/B,OAAO,CAAC2D,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACrN,KAAK,EAAE;QACnBmN,QAAQ,CAACG,GAAG,CAACD,SAAS,CAACrN,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACqE,MAAM,GAAGwG,KAAK,CAACC,IAAI,CAACqC,QAAQ,CAAC,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGrC,QAAQ,CAACmC,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGvC,QAAQ,CAACoC,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOD,IAAI,GAAGE,IAAI;IACpB,CAAC,CAAC;IAEF/E,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC7G,gBAAgB,EAAE,IAAI,CAACoC,MAAM,CAAC;EACjF;EAEA;EACAR,aAAaA,CAAC7D,KAAa;IACzB6I,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE9I,KAAK,CAAC;IACrC,IAAI,CAACmC,aAAa,GAAG,IAAI,CAACA,aAAa,KAAKnC,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAAC4I,wBAAwB,EAAE;IAC/B,IAAI,CAACzB,GAAG,CAACoC,aAAa,EAAE;EAC1B;EAEA;EACArF,aAAaA,CAAClE,KAAa;IACzB,IAAI,CAAC,IAAI,CAACiC,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAMwJ,UAAU,GAAG,IAAI,CAACnE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAOwJ,UAAU,CAACC,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAAC1K,KAAK,KAAKA,KAAK,CAAC,CAAC0D,MAAM;EACzD;EAEA;EACAY,iBAAiBA,CAAC2H,aAAqB;IACrC,IAAI,CAAC,IAAI,CAAChK,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAMwJ,UAAU,GAAG,IAAI,CAACnE,YAAY,CAAC,IAAI,CAACrF,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMoL,SAAS,GAAG5B,UAAU,CAAChB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKkC,aAAa,CAAC;IAChE,OAAOoB,SAAS,EAAErN,KAAK,IAAI,EAAE;EAC/B;EAEA;EACAF,gBAAgBA,CAACmM,aAAqB;IACpC,KAAK,MAAMtC,QAAQ,IAAI,IAAI,CAACjI,SAAS,EAAE;MACrC,MAAM+J,UAAU,GAAG,IAAI,CAACnE,YAAY,CAACqC,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM0D,SAAS,GAAG5B,UAAU,CAAChB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAKkC,aAAa,CAAC;MAChE,IAAIoB,SAAS,EAAE;QACb,OAAO;UACLtD,IAAI,EAAEsD,SAAS,CAACtD,IAAI;UACpB/J,KAAK,EAAEqN,SAAS,CAACrN,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAE+J,IAAI,EAAEkC,aAAa;MAAEjM,KAAK,EAAE;IAAE,CAAE;EAC3C;;;uCArbWiH,yBAAyB,EAAA1H,EAAA,CAAAsO,iBAAA,CAAAtO,EAAA,CAAAuO,iBAAA,GAAAvO,EAAA,CAAAsO,iBAAA,CAAAE,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAzB/G,yBAAyB;MAAAgH,SAAA;MAAAC,MAAA;QAAAvM,WAAA;QAAAgE,aAAA;QAAA7E,QAAA;QAAAuG,WAAA;QAAAC,YAAA;QAAAC,gBAAA;QAAA7B,WAAA;QAAAD,gBAAA;QAAA+B,kBAAA;MAAA;MAAA2G,OAAA;QAAA1G,eAAA;MAAA;MAAA2G,QAAA,GAAA7O,EAAA,CAAA8O,kBAAA,CARzB,CACT;QACEC,OAAO,EAAEhP,iBAAiB;QAC1BiP,WAAW,EAAElP,UAAU,CAAC,MAAM4H,yBAAyB,CAAC;QACxDuH,KAAK,EAAE;OACR,CACF,GAAAjP,EAAA,CAAAkP,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClCHxP,EAAA,CAAAC,cAAA,aAAyC;UAEvCD,EAAA,CAAAU,UAAA,IAAAgP,wCAAA,kBAAgG;UA+B9F1P,EADF,CAAAC,cAAA,aAA4D,gBAGuJ;UAD5KD,EAAA,CAAAY,UAAA,mBAAA+O,2DAAA;YAAA,OAASF,GAAA,CAAAlC,cAAA,EAAgB;UAAA,EAAC;UAE7DvN,EAAA,CAAAC,cAAA,cAA4B;UAK1BD,EAJA,CAAAU,UAAA,IAAAkP,iDAAA,0BAAgC,IAAAC,iDAAA,0BAIC;UAGnC7P,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAoB,SAAA,iBACU;UACZpB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAU,UAAA,IAAAoP,wCAAA,mBAC0Q;UAiM5Q9P,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAU,UAAA,IAAAqP,wCAAA,iBAA+D;UArPjE/P,EAAA,CAAAG,YAAA,EAAyC;;;UAEjCH,EAAA,CAAAI,SAAA,EAAuD;UAAvDJ,EAAA,CAAAsB,UAAA,SAAAmO,GAAA,CAAAzH,gBAAA,IAAAyH,GAAA,CAAAlI,kBAAA,CAAApD,MAAA,KAAuD;UA+BbnE,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAgQ,WAAA,aAAAP,GAAA,CAAAlO,QAAA,IAAAkO,GAAA,CAAAnI,SAAA,CAAwC;UACpFtH,EAAA,CAAAsB,UAAA,aAAAmO,GAAA,CAAAlO,QAAA,IAAAkO,GAAA,CAAAnI,SAAA,CAAkC;UAGjBtH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAsB,UAAA,SAAAmO,GAAA,CAAAnI,SAAA,CAAe;UAIftH,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAsB,UAAA,UAAAmO,GAAA,CAAAnI,SAAA,CAAgB;UAIItH,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAAgQ,WAAA,YAAAP,GAAA,CAAAtH,MAAA,CAAwB;UAGzDnI,EAAA,CAAAI,SAAA,EAAY;UAAZJ,EAAA,CAAAsB,UAAA,SAAAmO,GAAA,CAAAtH,MAAA,CAAY;UAqMdnI,EAAA,CAAAI,SAAA,EAAY;UAAZJ,EAAA,CAAAsB,UAAA,SAAAmO,GAAA,CAAAtH,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}