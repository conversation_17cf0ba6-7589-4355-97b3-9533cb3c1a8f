{"ast": null, "code": "export class TrafficListData {}", "map": {"version": 3, "names": ["TrafficListData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\traffic-list.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface TrafficList {\r\n  date: string;\r\n  value: number;\r\n  delta: {\r\n    up: boolean;\r\n    value: number;\r\n  };\r\n  comparison: {\r\n    prevDate: string;\r\n    prevValue: number;\r\n    nextDate: string;\r\n    nextValue: number;\r\n  };\r\n}\r\n\r\nexport abstract class TrafficListData {\r\n  abstract getTrafficListData(period: string): Observable<TrafficList>;\r\n}\r\n"], "mappings": "AAiBA,OAAM,MAAgBA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}