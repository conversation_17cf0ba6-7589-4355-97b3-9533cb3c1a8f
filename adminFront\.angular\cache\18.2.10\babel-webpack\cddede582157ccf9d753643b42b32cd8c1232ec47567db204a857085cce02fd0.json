{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { NbCheckboxModule } from '@nebular/theme';\nimport { tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/utility.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/shared/services/event.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/pipes/base-file.pipe\";\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 89);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r3)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_8_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.prevImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 23);\n    i0.ɵɵelement(2, \"path\", 91);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_9_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      ctx_r3.nextImage(formItemReq_r3);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 23);\n    i0.ɵɵelement(2, \"path\", 93);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", (formItemReq_r3.currentImageIndex || 0) + 1, \" / \", formItemReq_r3.CMatrialUrl.length, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_button_1_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3, i_r8));\n    });\n    i0.ɵɵelement(1, \"img\", 98);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r9 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-blue-500\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"border-gray-300\", i_r8 !== (formItemReq_r3.currentImageIndex || 0))(\"ring-2\", i_r8 === (formItemReq_r3.currentImageIndex || 0))(\"ring-blue-200\", i_r8 === (formItemReq_r3.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u9EDE\\u9078\\u653E\\u5927\\u7B2C \" + (i_r8 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 10, imageUrl_r9), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_button_1_Template, 3, 12, \"button\", 96);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 78);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_24_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.openImageModal(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_img_2_Template, 2, 3, \"img\", 79);\n    i0.ɵɵelementStart(3, \"div\", 80)(4, \"div\", 81)(5, \"div\", 82);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 83);\n    i0.ɵɵelement(7, \"path\", 84);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_8_Template, 3, 0, \"button\", 85)(9, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_9_Template, 3, 0, \"button\", 86)(10, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_10_Template, 2, 2, \"div\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_Template, 2, 1, \"div\", 88);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r3));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 100);\n    i0.ɵɵelement(2, \"path\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 102);\n    i0.ɵɵtext(4, \"\\u7121\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_nb_option_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 103);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r10.label, \" \");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 105)(1, \"div\", 106);\n    i0.ɵɵelement(2, \"img\", 107)(3, \"div\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 109);\n    i0.ɵɵlistener(\"blur\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template_input_blur_4_listener($event) {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.renameFile($event, i_r13, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template_button_click_5_listener() {\n      const picture_r14 = i0.ɵɵrestoreView(_r12).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.removeImage(picture_r14.id, formItemReq_r3));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 111);\n    i0.ɵɵelement(7, \"path\", 112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" \\u522A\\u9664\\u5716\\u7247 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_11_0;\n    let tmp_12_0;\n    const picture_r14 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", picture_r14.data, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", picture_r14.name)(\"disabled\", (tmp_11_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_11_0 !== undefined ? tmp_11_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", (tmp_12_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_12_0 !== undefined ? tmp_12_0 : false);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template, 9, 4, \"div\", 104);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r3.listPictures);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"label\", 114);\n    i0.ɵɵtext(2, \"\\u9810\\u8A2D\\u6982\\u5FF5\\u5716\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106);\n    i0.ɵɵelement(4, \"img\", 115);\n    i0.ɵɵpipe(5, \"addBaseFile\");\n    i0.ɵɵelement(6, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(5, 1, formItemReq_r3.CDesignFileUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 116);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 117);\n    i0.ɵɵelement(2, \"path\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 119);\n    i0.ɵɵtext(4, \"\\u7121\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 120)(1, \"nb-checkbox\", 121);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.allSelected, $event) || (formItemReq_r3.allSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template_nb_checkbox_checkedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckAllChange($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 122);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.allSelected);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 120)(1, \"nb-checkbox\", 125);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedItems[item_r17], $event) || (formItemReq_r3.selectedItems[item_r17] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxHouseHoldListChange($event, item_r17, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 126);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedItems[item_r17]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r17);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template, 4, 3, \"label\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.houseHoldList);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_ng_template_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 128);\n    i0.ɵɵelement(2, \"path\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 130);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u6236\\u5225\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 125);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedRemarkType[remark_r19], $event) || (formItemReq_r3.selectedRemarkType[remark_r19] = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"checkedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const remark_r19 = i0.ɵɵnextContext().$implicit;\n      const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onCheckboxRemarkChange($event, remark_r19, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const remark_r19 = i0.ɵɵnextContext().$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"checked\", formItemReq_r3.selectedRemarkType[remark_r19]);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"label\", 136);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template, 1, 2, \"nb-checkbox\", 137);\n    i0.ɵɵelementStart(2, \"span\", 126);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const remark_r19 = ctx.$implicit;\n    const formItemReq_r3 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedRemarkType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(remark_r19);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_Template, 4, 2, \"label\", 135);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CRemarkTypeOptions);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_87_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 128);\n    i0.ɵɵelement(2, \"path\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\", 130);\n    i0.ɵɵtext(4, \"\\u5C1A\\u7121\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131)(1, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 132);\n    i0.ɵɵelement(3, \"path\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h5\", 134);\n    i0.ɵɵtext(5, \"\\u5099\\u8A3B\\u9078\\u9805\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_Template, 2, 1, \"div\", 76)(7, DetailContentManagementSalesAccountComponent_ng_container_23_div_87_ng_template_7_Template, 5, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noRemarkOptions_r20 = i0.ɵɵreference(8);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.CRemarkTypeOptions && ctx_r3.CRemarkTypeOptions.length > 0)(\"ngIfElse\", noRemarkOptions_r20);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29)(2, \"div\", 30)(3, \"div\", 6)(4, \"div\", 14)(5, \"div\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\")(8, \"h4\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 33);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 9)(13, \"span\", 34);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 35)(16, \"div\", 36)(17, \"div\", 37)(18, \"div\", 38)(19, \"div\", 9);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 39);\n    i0.ɵɵelement(21, \"path\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(22, \"label\", 41);\n    i0.ɵɵtext(23, \"\\u4E3B\\u8981\\u6750\\u6599\\u793A\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, DetailContentManagementSalesAccountComponent_ng_container_23_div_24_Template, 12, 5, \"div\", 42)(25, DetailContentManagementSalesAccountComponent_ng_container_23_div_25_Template, 5, 0, \"div\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 44)(27, \"div\", 45)(28, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(29, \"svg\", 39);\n    i0.ɵɵelement(30, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"label\", 41);\n    i0.ɵɵtext(32, \"\\u57FA\\u672C\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 38)(34, \"div\", 47)(35, \"label\", 48);\n    i0.ɵɵtext(36, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 49)(38, \"span\", 50);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_ngModelChange_40_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CItemName, $event) || (formItemReq_r3.CItemName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 47)(42, \"label\", 48);\n    i0.ɵɵtext(43, \"\\u5FC5\\u586B\\u6578\\u91CF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 52)(45, \"input\", 53);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_ngModelChange_45_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.CRequireAnswer, $event) || (formItemReq_r3.CRequireAnswer = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 47)(47, \"label\", 48);\n    i0.ɵɵtext(48, \"\\u524D\\u53F0UI\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"nb-select\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_nb_select_ngModelChange_49_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(formItemReq_r3.selectedCUiType, $event) || (formItemReq_r3.selectedCUiType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_nb_select_selectedChange_49_listener() {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.changeSelectCUiType(formItemReq_r3));\n    });\n    i0.ɵɵtemplate(50, DetailContentManagementSalesAccountComponent_ng_container_23_nb_option_50_Template, 2, 2, \"nb-option\", 55);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(51, \"div\", 37)(52, \"div\", 38)(53, \"div\", 9);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(54, \"svg\", 39);\n    i0.ɵɵelement(55, \"path\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(56, \"label\", 41);\n    i0.ɵɵtext(57, \"\\u6982\\u5FF5\\u8A2D\\u8A08\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const inputFile_r11 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(inputFile_r11.click());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(59, \"svg\", 58);\n    i0.ɵɵelement(60, \"path\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(61, \"span\");\n    i0.ɵɵtext(62, \"\\u4E0A\\u50B3\\u6982\\u5FF5\\u8A2D\\u8A08\\u5716\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"input\", 60, 0);\n    i0.ɵɵlistener(\"change\", function DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_change_63_listener($event) {\n      const formItemReq_r3 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.detectFiles($event, formItemReq_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(65, DetailContentManagementSalesAccountComponent_ng_container_23_div_65_Template, 2, 1, \"div\", 61)(66, DetailContentManagementSalesAccountComponent_ng_container_23_div_66_Template, 7, 3, \"div\", 62)(67, DetailContentManagementSalesAccountComponent_ng_container_23_div_67_Template, 5, 0, \"div\", 63);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 64)(69, \"div\", 52)(70, \"div\", 65);\n    i0.ɵɵelement(71, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 67)(73, \"span\", 68);\n    i0.ɵɵtext(74, \"\\u8A2D\\u5B9A\\u9078\\u9805\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(75, \"div\", 69)(76, \"div\", 70)(77, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(78, \"svg\", 71);\n    i0.ɵɵelement(79, \"path\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(80, \"h5\", 73);\n    i0.ɵɵtext(81, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"div\", 74);\n    i0.ɵɵtemplate(83, DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template, 4, 2, \"label\", 75)(84, DetailContentManagementSalesAccountComponent_ng_container_23_div_84_Template, 2, 1, \"div\", 76)(85, DetailContentManagementSalesAccountComponent_ng_container_23_ng_template_85_Template, 5, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(87, DetailContentManagementSalesAccountComponent_ng_container_23_div_87_Template, 9, 2, \"div\", 77);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_15_0;\n    let tmp_19_0;\n    let tmp_23_0;\n    const formItemReq_r3 = ctx.$implicit;\n    const idx_r21 = ctx.index;\n    const noHouseholds_r22 = i0.ɵɵreference(86);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", idx_r21 + 1, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u9805\\u76EE\\u7DE8\\u865F #\", idx_r21 + 1, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (formItemReq_r3.selectedCUiType == null ? null : formItemReq_r3.selectedCUiType.label) || \"\\u672A\\u8A2D\\u5B9A\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CMatrialUrl && formItemReq_r3.CMatrialUrl.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CMatrialUrl || formItemReq_r3.CMatrialUrl.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"for\", \"CItemName_\" + idx_r21);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", formItemReq_r3.CName, \"-\", formItemReq_r3.CPart, \"-\", formItemReq_r3.CLocation, \": \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"CItemName_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CItemName);\n    i0.ɵɵproperty(\"disabled\", (tmp_15_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"cRequireAnswer_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.CRequireAnswer);\n    i0.ɵɵproperty(\"disabled\", formItemReq_r3.selectedCUiType.value === 3 || ((tmp_19_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_19_0 !== undefined ? tmp_19_0 : false));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", \"uiType_\" + idx_r21);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"uiType_\" + idx_r21);\n    i0.ɵɵtwoWayProperty(\"ngModel\", formItemReq_r3.selectedCUiType);\n    i0.ɵɵproperty(\"disabled\", (tmp_23_0 = ctx_r3.listFormItem.CIsLock) !== null && tmp_23_0 !== undefined ? tmp_23_0 : false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.CUiTypeOptions);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.listFormItem.CIsLock);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.listPictures && formItemReq_r3.listPictures.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !formItemReq_r3.CDesignFileUrl && (!formItemReq_r3.listPictures || formItemReq_r3.listPictures.length === 0));\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.houseHoldList.length > 0)(\"ngIfElse\", noHouseholds_r22);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r3.selectedCUiType.value === 3);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_img_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 153);\n    i0.ɵɵpipe(1, \"base64Image\");\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(1, 1, ctx_r3.getCurrentImage(formItemReq_r24)), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.prevImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 155);\n    i0.ɵɵelement(2, \"path\", 91);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.nextImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 155);\n    i0.ɵɵelement(2, \"path\", 93);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"div\", 14);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 58);\n    i0.ɵɵelement(3, \"path\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\", 158);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", (formItemReq_r24.currentImageIndex || 0) + 1, \" / \", formItemReq_r24.CMatrialUrl.length, \"\");\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_button_2_Template_button_click_0_listener() {\n      const i_r28 = i0.ɵɵrestoreView(_r27).index;\n      const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n      return i0.ɵɵresetView(formItemReq_r24.currentImageIndex = i_r28);\n    });\n    i0.ɵɵelement(1, \"img\", 163);\n    i0.ɵɵpipe(2, \"base64Image\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const imageUrl_r29 = ctx.$implicit;\n    const i_r28 = ctx.index;\n    const formItemReq_r24 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵclassProp(\"border-white\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"border-gray-400\", i_r28 !== (formItemReq_r24.currentImageIndex || 0))(\"ring-3\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"ring-white\", i_r28 === (formItemReq_r24.currentImageIndex || 0))(\"ring-opacity-50\", i_r28 === (formItemReq_r24.currentImageIndex || 0));\n    i0.ɵɵproperty(\"title\", \"\\u8DF3\\u81F3\\u7B2C \" + (i_r28 + 1) + \" \\u5F35\\u5716\\u7247\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 12, imageUrl_r29), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 159)(1, \"div\", 160);\n    i0.ɵɵtemplate(2, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_button_2_Template, 3, 14, \"button\", 161);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", formItemReq_r24.CMatrialUrl);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    })(\"keydown\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onKeydown($event, formItemReq_r24));\n    });\n    i0.ɵɵelementStart(1, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.closeImageModal(formItemReq_r24));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 142);\n    i0.ɵɵelement(3, \"path\", 143);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"div\", 144);\n    i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(5, \"div\", 145);\n    i0.ɵɵtemplate(6, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_img_6_Template, 2, 3, \"img\", 146)(7, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_7_Template, 3, 0, \"button\", 147)(8, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_8_Template, 3, 0, \"button\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_9_Template, 6, 2, \"div\", 149);\n    i0.ɵɵelementStart(10, \"div\", 150)(11, \"div\", 9);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 23);\n    i0.ɵɵelement(13, \"path\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\", 151);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_Template, 3, 1, \"div\", 152);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getCurrentImage(formItemReq_r24));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", formItemReq_r24.CName, \"-\", formItemReq_r24.CPart, \"-\", formItemReq_r24.CLocation, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.CMatrialUrl && formItemReq_r24.CMatrialUrl.length > 1);\n  }\n}\nfunction DetailContentManagementSalesAccountComponent_ng_container_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template, 17, 8, \"div\", 139);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formItemReq_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", formItemReq_r24.isModalOpen);\n  }\n}\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent {\n  constructor(_allow, route, message, _formItemService, _regularNoticeFileService, _utilityService, valid, location, _materialService, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.route = route;\n    this.message = message;\n    this._formItemService = _formItemService;\n    this._regularNoticeFileService = _regularNoticeFileService;\n    this._utilityService = _utilityService;\n    this.valid = valid;\n    this.location = location;\n    this._materialService = _materialService;\n    this._eventService = _eventService;\n    this.typeContentManagementSalesAccount = {\n      CFormType: 2,\n      CNoticeType: 2\n    };\n    // 通知類型選項映射\n    this.cNoticeTypeOptions = [{\n      label: '地主戶',\n      value: EnumHouseType.地主戶\n    }, {\n      label: '銷售戶',\n      value: EnumHouseType.銷售戶\n    }];\n    this.CUiTypeOptions = [{\n      value: 1,\n      label: '建材選色'\n    }, {\n      value: 2,\n      label: '群組選樣_選色'\n    }, {\n      value: 3,\n      label: '建材選樣'\n    }];\n    this.CRemarkTypeOptions = [\"正常\", \"留料\"];\n    this.selectedItems = {};\n    this.selectedRemarkType = {};\n    this.isNew = true;\n  }\n  // 動態獲取標題文字\n  get dynamicTitle() {\n    const option = this.cNoticeTypeOptions.find(option => option.value === this.typeContentManagementSalesAccount.CNoticeType);\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\n  }\n  // 設置通知類型（可供外部調用）\n  setCNoticeType(noticeType) {\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\n    }\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      if (params) {\n        const idParam = params.get('id');\n        const id = idParam ? +idParam : 0;\n        this.buildCaseId = id;\n        if (this.buildCaseId > 0) {\n          this.getListRegularNoticeFileHouseHold();\n        } else {\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\n          console.error('Invalid buildCaseId:', this.buildCaseId);\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\n          this.goBack();\n        }\n      }\n    });\n    // 處理查詢參數中的戶型\n    this.route.queryParams.subscribe(queryParams => {\n      if (queryParams['houseType']) {\n        const houseType = +queryParams['houseType'];\n        this.setCNoticeType(houseType);\n      }\n    });\n  }\n  ngOnDestroy() {\n    // 確保在組件銷毀時恢復body的滾動\n    document.body.style.overflow = 'auto';\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  detectFiles(event, formItemReq_) {\n    const file = event.target.files[0];\n    if (file) {\n      let reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => {\n        let base64Str = reader.result;\n        if (!base64Str) {\n          return;\n        }\n        if (formItemReq_.listPictures.length > 0) {\n          formItemReq_.listPictures[0] = {\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          };\n        } else {\n          formItemReq_.listPictures.push({\n            id: new Date().getTime(),\n            name: file.name.split('.')[0],\n            data: base64Str,\n            extension: this._utilityService.getFileExtension(file.name),\n            CFile: file\n          });\n        }\n        event.target.value = null;\n      };\n    }\n  }\n  removeImage(pictureId, formItemReq_) {\n    if (formItemReq_.listPictures.length) {\n      formItemReq_.listPictures = formItemReq_.listPictures.filter(x => x.id != pictureId);\n    }\n  }\n  renameFile(event, index, formItemReq_) {\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, {\n      type: formItemReq_.listPictures[index].CFile.type\n    });\n    formItemReq_.listPictures[index].CFile = newFile;\n  }\n  // 輪播功能方法\n  nextImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\n    }\n  }\n  prevImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0 ? formItemReq.CMatrialUrl.length - 1 : formItemReq.currentImageIndex - 1;\n    }\n  }\n  getCurrentImage(formItemReq) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\n    }\n    return null;\n  }\n  // 放大功能方法\n  openImageModal(formItemReq, imageIndex) {\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\n      if (imageIndex !== undefined) {\n        formItemReq.currentImageIndex = imageIndex;\n      }\n      formItemReq.isModalOpen = true;\n      // 防止背景滾動\n      document.body.style.overflow = 'hidden';\n    }\n  }\n  closeImageModal(formItemReq) {\n    formItemReq.isModalOpen = false;\n    // 恢復背景滾動\n    document.body.style.overflow = 'auto';\n  }\n  // 模態窗口中的輪播方法\n  nextImageModal(formItemReq) {\n    this.nextImage(formItemReq);\n  }\n  prevImageModal(formItemReq) {\n    this.prevImage(formItemReq);\n  }\n  // 鍵盤事件處理\n  onKeydown(event, formItemReq) {\n    if (formItemReq.isModalOpen) {\n      switch (event.key) {\n        case 'ArrowLeft':\n          event.preventDefault();\n          this.prevImageModal(formItemReq);\n          break;\n        case 'ArrowRight':\n          event.preventDefault();\n          this.nextImageModal(formItemReq);\n          break;\n        case 'Escape':\n          event.preventDefault();\n          this.closeImageModal(formItemReq);\n          break;\n      }\n    }\n  }\n  onCheckAllChange(checked, formItemReq_) {\n    formItemReq_.allSelected = checked;\n    this.houseHoldList.forEach(item => {\n      formItemReq_.selectedItems[item] = checked;\n    });\n  }\n  onCheckboxHouseHoldListChange(checked, item, formItemReq_) {\n    if (checked) {\n      formItemReq_.selectedItems[item] = checked;\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\n    } else {\n      formItemReq_.allSelected = false;\n    }\n  }\n  onCheckboxRemarkChange(checked, item, formItemReq_) {\n    formItemReq_.selectedRemarkType[item] = checked;\n  }\n  createRemarkObject(CRemarkTypeOptions, CRemarkType) {\n    const remarkObject = {};\n    for (const option of CRemarkTypeOptions) {\n      remarkObject[option] = false;\n    }\n    const remarkTypes = CRemarkType.split('-');\n    for (const type of remarkTypes) {\n      if (CRemarkTypeOptions.includes(type)) {\n        remarkObject[type] = true;\n      }\n    }\n    return remarkObject;\n  }\n  mergeItems(items) {\n    const map = new Map();\n    items.forEach(item => {\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\n      if (map.has(key)) {\n        const existing = map.get(key);\n        existing.count += 1;\n      } else {\n        map.set(key, {\n          item: {\n            ...item\n          },\n          count: 1\n        });\n      }\n    });\n    return Array.from(map.values()).map(({\n      item,\n      count\n    }) => ({\n      ...item,\n      CTotalAnswer: count\n    }));\n  }\n  getMaterialList() {\n    this._materialService.apiMaterialGetMaterialListPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n        this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n        this.arrListFormItemReq = res.Entries.map(o => {\n          return {\n            CDesignFileUrl: null,\n            CFormItemHouseHold: null,\n            CFormId: null,\n            CLocation: o.CLocation,\n            CName: o.CName,\n            CPart: o.CPart,\n            CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\n            CRemarkType: null,\n            CTotalAnswer: 0,\n            CRequireAnswer: 1,\n            CUiType: 0,\n            selectedItems: {},\n            selectedRemarkType: this.selectedRemarkType,\n            allSelected: false,\n            listPictures: [],\n            selectedCUiType: this.CUiTypeOptions[0],\n            currentImageIndex: 0,\n            isModalOpen: false,\n            CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64) : []\n          };\n        });\n        this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)];\n      }\n    })).subscribe();\n  }\n  getListFormItem() {\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\n      body: {\n        CBuildCaseId: this.buildCaseId,\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\n        CIsPaging: false\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.listFormItem = res.Entries;\n        this.isNew = res.Entries.formItems ? false : true;\n        if (res.Entries.formItems) {\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\n          this.arrListFormItemReq = res.Entries.formItems.map(o => {\n            return {\n              CFormId: this.listFormItem.CFormId,\n              CDesignFileUrl: o.CDesignFileUrl,\n              CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\n              CFile: o.CFile,\n              CFormItemHouseHold: o.CFormItemHouseHold,\n              CFormItemId: o.CFormItemId,\n              CLocation: o.CLocation,\n              CName: o.CName,\n              CPart: o.CPart,\n              CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\n              CRemarkType: o.CRemarkType,\n              CTotalAnswer: o.CTotalAnswer,\n              CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\n              CUiType: o.CUiType,\n              selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : {\n                ...this.selectedItems\n              },\n              selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : {\n                ...this.selectedRemarkType\n              },\n              allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\n              listPictures: [],\n              selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\n              currentImageIndex: 0,\n              isModalOpen: false\n            };\n          });\n        } else {\n          this.getMaterialList();\n        }\n      }\n    })).subscribe();\n  }\n  changeSelectCUiType(formItemReq) {\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\n      formItemReq.CRequireAnswer = 1;\n    }\n  }\n  getHouseHoldListByNoticeType(data) {\n    for (let item of data) {\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\n        return item.CHouseHoldList;\n      }\n    }\n    return [];\n  }\n  getKeysWithTrueValue(obj) {\n    return Object.keys(obj).filter(key => obj[key]);\n  }\n  getKeysWithTrueValueJoined(obj) {\n    return Object.keys(obj).filter(key => obj[key]).join('-');\n  }\n  getCRemarkType(selectedCUiType, selectedRemarkType) {\n    if (selectedCUiType && selectedCUiType.value == 3) {\n      return this.getKeysWithTrueValueJoined(selectedRemarkType);\n    }\n  }\n  getStringAfterComma(inputString) {\n    const parts = inputString.split(',');\n    if (parts.length > 1) {\n      return parts[1];\n    } else return \"\";\n  }\n  formatFile(listPictures) {\n    if (listPictures && listPictures.length > 0) {\n      return {\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\n        FileExtension: listPictures[0].extension || null,\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null\n      };\n    } else return undefined;\n  }\n  validation() {\n    this.valid.clear();\n    let hasInvalidCUiType = false;\n    let hasInvalidCRequireAnswer = false;\n    let hasInvalidItemName = false;\n    for (const item of this.saveListFormItemReq) {\n      if (!hasInvalidCUiType && !item.CUiType) {\n        hasInvalidCUiType = true;\n      }\n      if (!hasInvalidCRequireAnswer && !item.CRequireAnswer) {\n        hasInvalidCRequireAnswer = true;\n      }\n      if (item.CTotalAnswer && item.CRequireAnswer) {\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\n        }\n      }\n      if (!hasInvalidItemName && !item.CItemName) {\n        hasInvalidItemName = true;\n      }\n    }\n    if (hasInvalidCUiType) {\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\n    }\n    if (hasInvalidCRequireAnswer) {\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\n    }\n    if (hasInvalidItemName) {\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\n    }\n  }\n  onSubmit() {\n    this.saveListFormItemReq = this.arrListFormItemReq.map(e => {\n      return {\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\n        CName: e.CName,\n        CPart: e.CPart,\n        CLocation: e.CLocation,\n        CItemName: e.CItemName,\n        //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\n        CTotalAnswer: e.CTotalAnswer,\n        CRequireAnswer: e.CRequireAnswer,\n        CUiType: e.selectedCUiType.value\n      };\n    });\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    if (this.isNew) {\n      this.createListFormItem();\n    } else {\n      this.saveListFormItem();\n    }\n  }\n  saveListFormItem() {\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\n      body: this.saveListFormItemReq\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createListFormItem() {\n    this.creatListFormItem = {\n      CBuildCaseId: this.buildCaseId,\n      CFormItem: this.saveListFormItemReq || null,\n      CFormType: this.typeContentManagementSalesAccount.CFormType\n    };\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\n      body: this.creatListFormItem\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        // this.getListFormItem()\n        this.goBack();\n      }\n    });\n  }\n  createArrayObjectFromArray(a, b) {\n    const c = {};\n    for (const item of a) {\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\n      c[item] = !!matchingItem;\n    }\n    return c;\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\n  getListRegularNoticeFileHouseHold() {\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\n      body: this.buildCaseId\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries);\n        this.getListFormItem();\n      }\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this.location.back();\n  }\n  static {\n    this.ɵfac = function DetailContentManagementSalesAccountComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailContentManagementSalesAccountComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i4.RegularNoticeFileService), i0.ɵɵdirectiveInject(i5.UtilityService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i4.MaterialService), i0.ɵɵdirectiveInject(i8.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailContentManagementSalesAccountComponent,\n      selectors: [[\"ngx-detail-content-management-sales-account\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 43,\n      vars: 7,\n      consts: [[\"inputFile\", \"\"], [\"noHouseholds\", \"\"], [\"noRemarkOptions\", \"\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-gray-50\", \"to-gray-100\"], [1, \"shadow-xl\", \"border-0\", \"rounded-xl\", \"overflow-hidden\"], [1, \"bg-white\", \"border-b\", \"border-gray-200\", \"p-6\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"w-1\", \"h-8\", \"bg-green-500\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-3\", \"py-1\", \"text-sm\", \"bg-green-100\", \"text-green-800\", \"rounded-full\", \"font-medium\"], [1, \"p-6\", \"bg-gray-50\"], [1, \"space-y-8\"], [1, \"bg-white\", \"rounded-xl\", \"p-6\", \"shadow-sm\", \"border\", \"border-gray-200\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"bg-blue-100\", \"rounded-lg\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [1, \"text-xl\", \"font-bold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\", \"mt-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"border-t\", \"border-gray-200\", \"p-6\", \"sticky\", \"bottom-0\", \"shadow-lg\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"text-sm\", \"text-gray-600\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"px-6\", \"py-3\", \"bg-gray-200\", \"hover:bg-gray-300\", \"text-gray-700\", \"font-medium\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"space-x-2\", \"shadow-sm\", \"hover:shadow-md\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"px-6\", \"py-3\", \"bg-gradient-to-r\", \"from-green-500\", \"to-green-600\", \"hover:from-green-600\", \"hover:to-green-700\", \"text-white\", \"font-medium\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 13l4 4L19 7\"], [1, \"bg-white\", \"rounded-xl\", \"shadow-lg\", \"border\", \"border-gray-200\", \"overflow-hidden\", \"transition-all\", \"duration-300\", \"hover:shadow-xl\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-indigo-50\", \"px-6\", \"py-4\", \"border-b\", \"border-gray-200\"], [1, \"w-8\", \"h-8\", \"bg-blue-500\", \"text-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"text-sm\", \"font-bold\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"px-2\", \"py-1\", \"text-xs\", \"bg-blue-100\", \"text-blue-800\", \"rounded-full\"], [1, \"p-6\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-4\", \"gap-6\"], [1, \"lg:col-span-1\"], [1, \"space-y-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\", \"font-semibold\", \"text-gray-700\"], [\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"lg:col-span-2\"], [1, \"space-y-6\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"group\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-2\", 3, \"for\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"bg-gray-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-gray-200\"], [1, \"text-sm\", \"text-gray-600\", \"font-medium\", \"px-2\", \"py-1\", \"bg-blue-100\", \"rounded-md\", \"whitespace-nowrap\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u4F8B\\u5982\\uFF1A\\u5EDA\\u623F\\u6AAF\\u9762\", 1, \"flex-1\", \"border-0\", \"bg-transparent\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", \"rounded-md\", \"p-2\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [1, \"relative\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u8F38\\u5165\\u6578\\u91CF\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"focus:ring-2\", \"focus:ring-blue-200\", \"rounded-lg\", \"p-3\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"id\", \"ngModel\", \"disabled\"], [\"placeholder\", \"\\u9078\\u64C7UI\\u985E\\u578B\", 1, \"w-full\", \"border-2\", \"border-gray-200\", \"focus:border-blue-500\", \"rounded-lg\", \"transition-all\", \"duration-200\", 3, \"ngModelChange\", \"selectedChange\", \"id\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"w-full\", \"bg-gradient-to-r\", \"from-blue-500\", \"to-blue-600\", \"hover:from-blue-600\", \"hover:to-blue-700\", \"text-white\", \"font-medium\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"justify-center\", \"space-x-2\", \"shadow-md\", \"hover:shadow-lg\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"], [\"type\", \"file\", \"accept\", \"image/png, image/gif, image/jpeg\", 1, \"hidden\", 3, \"change\"], [\"class\", \"space-y-3\", 4, \"ngIf\"], [\"class\", \"space-y-2\", 4, \"ngIf\"], [\"class\", \"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\", 4, \"ngIf\"], [1, \"my-8\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\"], [1, \"w-full\", \"border-t\", \"border-gray-300\"], [1, \"relative\", \"flex\", \"justify-center\", \"text-sm\"], [1, \"px-4\", \"bg-white\", \"text-gray-500\", \"font-medium\"], [1, \"grid\", \"grid-cols-1\", \"lg:grid-cols-2\", \"gap-6\"], [1, \"bg-blue-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-blue-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"font-semibold\", \"text-blue-800\"], [1, \"space-y-3\"], [\"class\", \"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 gap-2\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"bg-orange-50 p-4 rounded-lg border border-orange-200\", 4, \"ngIf\"], [1, \"aspect-square\", \"w-full\", \"relative\", \"overflow-hidden\", \"rounded-xl\", \"border-2\", \"border-gray-200\", \"cursor-pointer\", \"group\", \"shadow-md\", \"hover:shadow-lg\", \"transition-all\", \"duration-300\", 3, \"click\"], [\"class\", \"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\", 3, \"src\", 4, \"ngIf\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-30\", \"transition-all\", \"duration-300\", \"flex\", \"items-center\", \"justify-center\"], [1, \"transform\", \"scale-75\", \"group-hover:scale-100\", \"transition-transform\", \"duration-300\"], [1, \"w-12\", \"h-12\", \"bg-white\", \"bg-opacity-90\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-gray-800\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"], [\"class\", \"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\", 4, \"ngIf\"], [\"class\", \"flex gap-2 mt-3 overflow-x-auto pb-2\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-500\", \"group-hover:scale-110\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"left-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M15 19l-7-7 7-7\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247\", 1, \"absolute\", \"right-2\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"text-gray-800\", \"rounded-full\", \"w-8\", \"h-8\", \"flex\", \"items-center\", \"justify-center\", \"hover:bg-opacity-100\", \"hover:shadow-md\", \"transition-all\", \"z-10\", \"opacity-0\", \"group-hover:opacity-100\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M9 5l7 7-7 7\"], [1, \"absolute\", \"bottom-2\", \"right-2\", \"bg-black\", \"bg-opacity-80\", \"text-white\", \"text-xs\", \"px-2\", \"py-1\", \"rounded-lg\", \"backdrop-blur-sm\"], [1, \"flex\", \"gap-2\", \"mt-3\", \"overflow-x-auto\", \"pb-2\"], [\"class\", \"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\", 3, \"border-blue-500\", \"border-gray-300\", \"ring-2\", \"ring-blue-200\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-12\", \"h-12\", \"border-2\", \"rounded-lg\", \"overflow-hidden\", \"hover:border-blue-400\", \"transition-all\", \"duration-200\", \"cursor-pointer\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"hover:scale-110\", 3, \"src\"], [1, \"aspect-square\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-xl\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-12\", \"h-12\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-sm\"], [3, \"value\"], [\"class\", \"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-gray-50\", \"border\", \"border-gray-200\", \"p-3\", \"rounded-lg\", \"hover:shadow-md\", \"transition-all\", \"duration-200\"], [1, \"relative\", \"group\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"mb-3\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"absolute\", \"inset-0\", \"bg-black\", \"bg-opacity-0\", \"group-hover:bg-opacity-20\", \"transition-all\", \"duration-200\", \"rounded-lg\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5716\\u7247\\u8AAA\\u660E/\\u6A94\\u540D\", 1, \"w-full\", \"p-2\", \"text-sm\", \"mb-2\", \"border\", \"border-gray-200\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-blue-500\", \"focus:border-transparent\", 3, \"blur\", \"value\", \"disabled\"], [1, \"w-full\", \"bg-red-100\", \"hover:bg-red-200\", \"text-red-700\", \"font-medium\", \"py-2\", \"px-3\", \"rounded-md\", \"transition-colors\", \"duration-200\", \"text-sm\", 3, \"click\", \"disabled\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"inline\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"space-y-2\"], [1, \"block\", \"text-xs\", \"font-medium\", \"text-gray-600\"], [1, \"w-full\", \"h-32\", \"object-cover\", \"rounded-lg\", \"border\", \"border-gray-200\", 3, \"src\"], [1, \"h-32\", \"w-full\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"border-2\", \"border-dashed\", \"border-gray-300\", \"rounded-lg\", \"bg-gray-50\", \"text-gray-400\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"mb-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\"], [1, \"text-xs\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-blue-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-medium\", \"text-blue-700\"], [1, \"grid\", \"grid-cols-1\", \"gap-2\"], [\"class\", \"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"item\", 1, \"mr-3\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"text-gray-700\"], [1, \"text-center\", \"py-4\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\", \"text-gray-400\", \"mx-auto\", \"mb-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"text-gray-500\", \"text-sm\"], [1, \"bg-orange-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-5\", \"h-5\", \"text-orange-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [1, \"font-semibold\", \"text-orange-800\"], [\"class\", \"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"cursor-pointer\", \"hover:bg-orange-100\", \"p-2\", \"rounded-md\", \"transition-colors\"], [\"value\", \"item\", \"class\", \"mr-3\", 3, \"checked\", \"disabled\", \"checkedChange\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\"], [\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\", \"tabindex\", \"0\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-sm\", \"p-4\", \"animate-fade-in-up\", 3, \"click\", \"keydown\"], [\"title\", \"\\u95DC\\u9589\\u5716\\u7247\\u6AA2\\u8996 (\\u6309 ESC \\u9375)\", 1, \"modal-close-btn\", \"fixed\", \"top-6\", \"right-6\", \"z-[60]\", \"bg-red-500\", \"bg-opacity-95\", \"hover:bg-red-600\", \"hover:bg-opacity-100\", \"text-white\", \"rounded-full\", \"w-14\", \"h-14\", \"flex\", \"items-center\", \"justify-center\", \"shadow-2xl\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-7\", \"h-7\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2.5\", \"d\", \"M6 18L18 6M6 6l12 12\"], [1, \"relative\", \"max-w-7xl\", \"max-h-full\", \"w-full\", \"h-full\", \"flex\", \"items-center\", \"justify-center\", \"animate-slide-in-left\", 3, \"click\"], [1, \"relative\", \"max-w-full\", \"max-h-full\", \"bg-white\", \"rounded-2xl\", \"p-2\", \"shadow-2xl\"], [\"class\", \"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\", \"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-6\", \"right-6\", \"bg-gradient-to-r\", \"from-blue-600\", \"to-blue-700\", \"text-white\", \"px-4\", \"py-3\", \"rounded-lg\", \"text-sm\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\"], [\"class\", \"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\", 4, \"ngIf\"], [1, \"max-w-full\", \"max-h-[85vh]\", \"object-contain\", \"rounded-xl\", \"animate-fade-in-up\", 3, \"src\"], [\"title\", \"\\u4E0A\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2190 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"left-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-8\", \"h-8\"], [\"title\", \"\\u4E0B\\u4E00\\u5F35\\u5716\\u7247 (\\u6309 \\u2192 \\u9375)\", 1, \"modal-nav-btn\", \"absolute\", \"right-4\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"bg-white\", \"bg-opacity-95\", \"hover:bg-opacity-100\", \"text-gray-800\", \"rounded-full\", \"w-16\", \"h-16\", \"flex\", \"items-center\", \"justify-center\", \"shadow-lg\", \"z-[55]\", 3, \"click\"], [1, \"absolute\", \"bottom-24\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-90\", \"text-white\", \"px-6\", \"py-3\", \"rounded-full\", \"backdrop-blur-sm\", \"shadow-lg\"], [1, \"font-medium\", \"text-lg\"], [1, \"absolute\", \"bottom-32\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"bg-black\", \"bg-opacity-80\", \"backdrop-blur-md\", \"p-4\", \"rounded-xl\", \"shadow-2xl\", \"max-w-full\"], [1, \"flex\", \"gap-3\", \"overflow-x-auto\", \"max-w-[80vw]\", \"modal-thumbnails\"], [\"class\", \"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\", 3, \"border-white\", \"border-gray-400\", \"ring-3\", \"ring-white\", \"ring-opacity-50\", \"title\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-shrink-0\", \"w-20\", \"h-20\", \"border-3\", \"rounded-xl\", \"overflow-hidden\", \"hover:border-white\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\", \"title\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"transition-transform\", \"duration-200\", 3, \"src\"]],\n      template: function DetailContentManagementSalesAccountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3)(1, \"nb-card\", 4)(2, \"nb-card-header\", 5)(3, \"div\", 6)(4, \"div\", 7);\n          i0.ɵɵelement(5, \"div\", 8);\n          i0.ɵɵelementStart(6, \"div\");\n          i0.ɵɵelement(7, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 9)(9, \"span\", 10);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"nb-card-body\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"div\", 14)(15, \"div\", 15);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 16);\n          i0.ɵɵelement(17, \"path\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(18, \"div\")(19, \"h3\", 18);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 19);\n          i0.ɵɵtext(22, \"\\u7BA1\\u7406\\u9078\\u6A23\\u9805\\u76EE\\u7684\\u8A73\\u7D30\\u8A2D\\u5B9A\\u8207\\u914D\\u7F6E\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(23, DetailContentManagementSalesAccountComponent_ng_container_23_Template, 88, 32, \"ng-container\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"nb-card-footer\", 21)(25, \"div\", 6)(26, \"div\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(27, \"svg\", 23);\n          i0.ɵɵelement(28, \"path\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 7)(32, \"button\", 25);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_32_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(33, \"svg\", 23);\n          i0.ɵɵelement(34, \"path\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36, \"\\u53D6\\u6D88\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"button\", 27);\n          i0.ɵɵlistener(\"click\", function DetailContentManagementSalesAccountComponent_Template_button_click_37_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 23);\n          i0.ɵɵelement(39, \"path\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(40, \"span\");\n          i0.ɵɵtext(41, \"\\u5132\\u5B58\\u8B8A\\u66F4\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(42, DetailContentManagementSalesAccountComponent_ng_container_42_Template, 2, 1, \"ng-container\", 20);\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.dynamicTitle, \" \");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.dynamicTitle);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx.arrListFormItemReq.length || 0, \" \\u500B\\u9078\\u6A23\\u9805\\u76EE\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (tmp_4_0 = ctx.listFormItem.CIsLock) !== null && tmp_4_0 !== undefined ? tmp_4_0 : false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", (tmp_5_0 = ctx.listFormItem.CIsLock) !== null && tmp_5_0 !== undefined ? tmp_5_0 : false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrListFormItemReq);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbCardFooterComponent, i10.NbCardHeaderComponent, i10.NbCheckboxComponent, i10.NbInputDirective, i10.NbSelectComponent, i10.NbOptionComponent, i11.BreadcrumbComponent, i12.BaseFilePipe, NbCheckboxModule, Base64ImagePipe],\n      styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  min-height: 100vh;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  animation: _ngcontent-%COMP%_gradientShift 20s ease infinite;\\n  background-size: 400% 400%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientShift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\nnb-card[_ngcontent-%COMP%] {\\n  border-radius: 1rem !important;\\n  overflow: hidden;\\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\nnb-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;\\n}\\n\\n.item-card[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.item-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);\\n}\\n\\ninput[nbInput][_ngcontent-%COMP%], \\nnb-select[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in-out !important;\\n}\\ninput[nbInput][_ngcontent-%COMP%]:focus, \\nnb-select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;\\n}\\n\\n.btn-enhanced[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s;\\n}\\n.btn-enhanced[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n.image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.image-container[_ngcontent-%COMP%]:hover::after {\\n  opacity: 1;\\n}\\n\\n.status-badge[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.status-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: inherit;\\n  border-radius: inherit;\\n  opacity: 0.1;\\n  transform: scale(0);\\n  transition: transform 0.3s ease;\\n}\\n.status-badge[_ngcontent-%COMP%]:hover::before {\\n  transform: scale(1.1);\\n}\\n\\n.responsive-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n}\\n@media (min-width: 768px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  }\\n}\\n@media (min-width: 1024px) {\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 2rem;\\n  }\\n}\\n\\n.image-carousel[_ngcontent-%COMP%]:hover   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.image-carousel[_ngcontent-%COMP%]   .carousel-controls[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.carousel-btn[_ngcontent-%COMP%] {\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.carousel-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n@media (max-width: 768px) {\\n  .carousel-btn[_ngcontent-%COMP%] {\\n    opacity: 1 !important;\\n    width: 2.5rem;\\n    height: 2.5rem;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1rem;\\n    height: 1rem;\\n  }\\n}\\n\\n.thumbnail-navigation[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 0, 0, 0.1);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\\n  border-radius: 3px;\\n}\\n.thumbnail-navigation[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #2563eb, #7c3aed);\\n}\\n\\n.image-modal[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-image[_ngcontent-%COMP%] {\\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\\n  transition: transform 0.3s ease;\\n  border-radius: 0.75rem;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n  max-height: 20vh;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.5));\\n  border-radius: 4px;\\n}\\n.image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.7));\\n}\\n\\n.modal-nav-btn[_ngcontent-%COMP%] {\\n  -webkit-backdrop-filter: blur(8px);\\n          backdrop-filter: blur(8px);\\n  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  z-index: 9995 !important;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-50%) scale(1.1);\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.modal-nav-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n@media (max-width: 768px) {\\n  .modal-nav-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .modal-nav-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n.modal-close-btn[_ngcontent-%COMP%] {\\n  z-index: 9999 !important;\\n  pointer-events: auto;\\n  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(255, 255, 255, 0.1) !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.2) !important;\\n  transform: scale(1.1) rotate(90deg);\\n}\\n.modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n    top: 1rem;\\n    right: 1rem;\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.5rem;\\n    height: 1.5rem;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_modalFadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.9) translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1) translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromLeft {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.animate-slide-in-left[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromLeft 0.5s ease-out;\\n}\\n\\n.animate-slide-in-right[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.5s ease-out;\\n}\\n\\n.animate-fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .image-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    margin: 1rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%] {\\n    width: 3rem;\\n    height: 3rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-controls[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 1.25rem;\\n    height: 1.25rem;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%] {\\n    max-height: 15vh;\\n  }\\n  .image-modal[_ngcontent-%COMP%]   .modal-thumbnails[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 3.5rem;\\n    height: 3.5rem;\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .responsive-grid[_ngcontent-%COMP%] {\\n    gap: 1rem;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n    background: rgba(255, 255, 255, 0.95);\\n  }\\n  .modal-close-btn[_ngcontent-%COMP%] {\\n    border: 2px solid currentColor;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%], \\n   *[_ngcontent-%COMP%]::before, \\n   *[_ngcontent-%COMP%]::after {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .page-background[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  }\\n  .item-card[_ngcontent-%COMP%] {\\n    background: #374151;\\n    border-color: #4b5563;\\n  }\\n  .carousel-btn[_ngcontent-%COMP%], \\n   .modal-nav-btn[_ngcontent-%COMP%] {\\n    background: rgba(31, 41, 55, 0.9);\\n    border-color: rgba(156, 163, 175, 0.3);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "NbCheckboxModule", "tap", "SharedModule", "BaseComponent", "EEvent", "Base64ImagePipe", "EnumHouseType", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵpipeBind1", "ctx_r3", "getCurrentImage", "formItemReq_r3", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_8_Template_button_click_0_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "prevImage", "ɵɵresetView", "stopPropagation", "ɵɵelementEnd", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_9_Template_button_click_0_listener", "_r6", "nextImage", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate2", "currentImageIndex", "CMatrialUrl", "length", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_button_1_Template_button_click_0_listener", "i_r8", "_r7", "index", "openImageModal", "ɵɵclassProp", "imageUrl_r9", "ɵɵtemplate", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_button_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_Template_div_click_1_listener", "_r2", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_img_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_button_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_10_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_div_11_Template", "case_r10", "ɵɵtextInterpolate1", "label", "DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template_input_blur_4_listener", "i_r13", "_r12", "renameFile", "DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template_button_click_5_listener", "picture_r14", "removeImage", "id", "data", "name", "tmp_11_0", "listFormItem", "CIsLock", "undefined", "tmp_12_0", "DetailContentManagementSalesAccountComponent_ng_container_23_div_65_div_1_Template", "listPictures", "CDesignFileUrl", "ɵɵtwoWayListener", "DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template_nb_checkbox_checkedChange_1_listener", "_r15", "ɵɵtwoWayBindingSet", "allSelected", "onCheckAllChange", "ɵɵtwoWayProperty", "DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template_nb_checkbox_checkedChange_1_listener", "item_r17", "_r16", "selectedItems", "onCheckboxHouseHoldListChange", "ɵɵtextInterpolate", "DetailContentManagementSalesAccountComponent_ng_container_23_div_84_label_1_Template", "houseHoldList", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template_nb_checkbox_checkedChange_0_listener", "_r18", "remark_r19", "selectedRemarkType", "onCheckboxRemarkChange", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_nb_checkbox_1_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_label_1_Template", "CRemarkTypeOptions", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_div_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_ng_template_7_Template", "ɵɵtemplateRefExtractor", "noRemarkOptions_r20", "ɵɵelementContainerStart", "DetailContentManagementSalesAccountComponent_ng_container_23_div_24_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_25_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_ngModelChange_40_listener", "_r1", "CItemName", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_ngModelChange_45_listener", "CRequireAnswer", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_nb_select_ngModelChange_49_listener", "selectedCUiType", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_nb_select_selectedChange_49_listener", "changeSelectCUiType", "DetailContentManagementSalesAccountComponent_ng_container_23_nb_option_50_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_button_click_58_listener", "inputFile_r11", "ɵɵreference", "click", "DetailContentManagementSalesAccountComponent_ng_container_23_Template_input_change_63_listener", "detectFiles", "DetailContentManagementSalesAccountComponent_ng_container_23_div_65_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_66_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_67_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_label_83_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_84_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_ng_template_85_Template", "DetailContentManagementSalesAccountComponent_ng_container_23_div_87_Template", "idx_r21", "ɵɵtextInterpolate3", "CName", "<PERSON>art", "CLocation", "tmp_15_0", "value", "tmp_19_0", "tmp_23_0", "CUiTypeOptions", "noHouseholds_r22", "formItemReq_r24", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_7_Template_button_click_0_listener", "_r25", "prevImageModal", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_8_Template_button_click_0_listener", "_r26", "nextImageModal", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_button_2_Template_button_click_0_listener", "i_r28", "_r27", "imageUrl_r29", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_button_2_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_click_0_listener", "_r23", "closeImageModal", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_keydown_0_listener", "onKeydown", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_button_click_1_listener", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template_div_click_4_listener", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_img_6_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_7_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_button_8_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_9_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_div_16_Template", "DetailContentManagementSalesAccountComponent_ng_container_42_div_1_Template", "isModalOpen", "DetailContentManagementSalesAccountComponent", "constructor", "_allow", "route", "message", "_formItemService", "_regularNoticeFileService", "_utilityService", "valid", "location", "_materialService", "_eventService", "typeContentManagementSalesAccount", "CFormType", "CNoticeType", "cNoticeTypeOptions", "地主戶", "銷售戶", "isNew", "dynamicTitle", "option", "find", "setCNoticeType", "noticeType", "some", "ngOnInit", "paramMap", "subscribe", "params", "idParam", "get", "buildCaseId", "getListRegularNoticeFileHouseHold", "console", "error", "showErrorMSG", "goBack", "queryParams", "houseType", "ngOnDestroy", "document", "body", "style", "overflow", "getItemByValue", "options", "item", "event", "formItemReq_", "file", "target", "files", "reader", "FileReader", "readAsDataURL", "onload", "base64Str", "result", "Date", "getTime", "split", "extension", "getFileExtension", "CFile", "push", "pictureId", "filter", "x", "blob", "slice", "size", "type", "newFile", "File", "formItemReq", "imageIndex", "key", "preventDefault", "checked", "for<PERSON>ach", "every", "createRemarkObject", "CRemarkType", "remarkObject", "remarkTypes", "includes", "mergeItems", "items", "map", "Map", "has", "existing", "count", "set", "Array", "from", "values", "CTotalAnswer", "getMaterialList", "apiMaterialGetMaterialListPost$Json", "CBuildCaseId", "CPagi", "pipe", "res", "Entries", "StatusCode", "arrListFormItemReq", "o", "CFormItemHouseHold", "CFormId", "CUiType", "CSelectPicture", "x1", "CBase64", "getListFormItem", "apiFormItemGetListFormItemPost$Json", "CIsPaging", "formItems", "CFirstMatrialUrl", "CFormItemId", "tblFormItemHouseholds", "createArrayObjectFromArray", "getHouseHoldListByNoticeType", "CHouseHoldList", "getKeysWithTrueValue", "obj", "Object", "keys", "getKeysWithTrueValueJoined", "join", "getCRemarkType", "getStringAfterComma", "inputString", "parts", "formatFile", "Base64String", "FileExtension", "FileName", "validation", "clear", "hasInvalidCUiType", "hasInvalidCRequireAnswer", "hasInvalidItemName", "saveListFormItemReq", "addErrorMessage", "onSubmit", "e", "CFormID", "errorMessages", "showErrorMSGs", "createListFormItem", "saveListFormItem", "apiFormItemSaveListFormItemPost$Json", "showSucessMSG", "creatListFormItem", "CFormItem", "apiFormItemCreateListFormItemPost$Json", "a", "b", "c", "matchingItem", "bItem", "CHousehold", "CIsSelect", "apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json", "action", "payload", "back", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "ActivatedRoute", "i3", "MessageService", "i4", "FormItemService", "RegularNoticeFileService", "i5", "UtilityService", "i6", "ValidationHelper", "i7", "Location", "MaterialService", "i8", "EventService", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailContentManagementSalesAccountComponent_Template", "rf", "ctx", "DetailContentManagementSalesAccountComponent_ng_container_23_Template", "DetailContentManagementSalesAccountComponent_Template_button_click_32_listener", "DetailContentManagementSalesAccountComponent_Template_button_click_37_listener", "DetailContentManagementSalesAccountComponent_ng_container_42_Template", "tmp_4_0", "tmp_5_0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i11", "BreadcrumbComponent", "i12", "BaseFilePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\content-management-sales-account\\detail-content-management-sales-account\\detail-content-management-sales-account.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { NbCheckboxModule } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { FormItemService, MaterialService, RegularNoticeFileService } from 'src/services/api/services';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { tap } from 'rxjs';\r\nimport { CreateListFormItem, FileViewModel, GetListFormItemRes, GetMaterialListResponse, SaveListFormItemReq } from 'src/services/api/models';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { BaseComponent } from 'src/app/pages/components/base/baseComponent';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\nimport { Base64ImagePipe } from \"../../../../@theme/pipes/base64-image.pipe\";\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\n\r\n\r\nexport interface ExtendedSaveListFormItemReq {\r\n  CDesignFileUrl?: string | null;\r\n  CMatrialUrl?: string[] | null;\r\n  CFile?: FileViewModel,\r\n  CFormItemHouseHold?: Array<string> | null;\r\n  CFormItemId?: number;\r\n  CItemName?: string;\r\n  CFormID?: number;\r\n  CPart?: string | null;\r\n  CName?: string | null;\r\n  CLocation?: string | null;\r\n  CRemarkType?: string | null;\r\n  CTotalAnswer?: number;\r\n  CRequireAnswer?: number;\r\n  CUiType?: number;\r\n  selectedCUiType: any | null;\r\n  selectedItems: { [key: string]: boolean }\r\n  selectedRemarkType?: { [key: string]: boolean }\r\n  allSelected: boolean,\r\n  listPictures: any[],\r\n  currentImageIndex?: number; // 當前顯示的圖片索引\r\n  isModalOpen?: boolean; // 是否打開放大模態窗口\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-detail-content-management-sales-account',\r\n  templateUrl: './detail-content-management-sales-account.component.html',\r\n  styleUrls: ['./detail-content-management-sales-account.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, NbCheckboxModule, BaseFilePipe, Base64ImagePipe],\r\n})\r\n\r\nexport class DetailContentManagementSalesAccountComponent extends BaseComponent implements OnInit, OnDestroy {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private route: ActivatedRoute,\r\n    private message: MessageService,\r\n    private _formItemService: FormItemService,\r\n    private _regularNoticeFileService: RegularNoticeFileService,\r\n    private _utilityService: UtilityService,\r\n    private valid: ValidationHelper,\r\n    private location: Location,\r\n    private _materialService: MaterialService,\r\n    private _eventService: EventService\r\n  ) {\r\n    super(_allow)\r\n  }\r\n  typeContentManagementSalesAccount = {\r\n    CFormType: 2,\r\n    CNoticeType: 2\r\n  }\r\n  // 通知類型選項映射\r\n  cNoticeTypeOptions = [\r\n    { label: '地主戶', value: EnumHouseType.地主戶 },\r\n    { label: '銷售戶', value: EnumHouseType.銷售戶 }\r\n  ];\r\n  // 動態獲取標題文字\r\n  get dynamicTitle(): string {\r\n    const option = this.cNoticeTypeOptions.find(option =>\r\n      option.value === this.typeContentManagementSalesAccount.CNoticeType\r\n    );\r\n    return option ? `類型 - ${option.label}` : '類型 - 獨立選樣';\r\n  }\r\n  // 設置通知類型（可供外部調用）\r\n  setCNoticeType(noticeType: EnumHouseType): void {\r\n    if (this.cNoticeTypeOptions.some(option => option.value === noticeType)) {\r\n      this.typeContentManagementSalesAccount.CNoticeType = noticeType;\r\n    }\r\n  }\r\n\r\n  CUiTypeOptions: any[] = [\r\n    {\r\n      value: 1, label: '建材選色'\r\n    },\r\n    {\r\n      value: 2, label: '群組選樣_選色'\r\n    }, {\r\n      value: 3, label: '建材選樣'\r\n    }]\r\n  CRemarkTypeOptions = [\"正常\", \"留料\"]\r\n  buildCaseId: number; override ngOnInit(): void {\r\n    this.route.paramMap.subscribe(params => {\r\n      if (params) {\r\n        const idParam = params.get('id');\r\n        const id = idParam ? +idParam : 0;\r\n        this.buildCaseId = id\r\n\r\n        if (this.buildCaseId > 0) {\r\n          this.getListRegularNoticeFileHouseHold()\r\n        } else {\r\n          // 如果 buildCaseId 為 0 或無效，顯示錯誤訊息並返回\r\n          console.error('Invalid buildCaseId:', this.buildCaseId);\r\n          this.message.showErrorMSG(\"無效的建案ID，請重新選擇建案\");\r\n          this.goBack();\r\n        }\r\n      }\r\n    });\r\n\r\n    // 處理查詢參數中的戶型\r\n    this.route.queryParams.subscribe(queryParams => {\r\n      if (queryParams['houseType']) {\r\n        const houseType = +queryParams['houseType'];\r\n        this.setCNoticeType(houseType);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // 確保在組件銷毀時恢復body的滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n\r\n  selectedItems: { [key: string]: boolean } = {};\r\n  selectedRemarkType: { [key: string]: boolean } = {};\r\n\r\n\r\n\r\n  detectFiles(event: any, formItemReq_: any) {\r\n    const file = event.target.files[0]\r\n    if (file) {\r\n      let reader = new FileReader();\r\n      reader.readAsDataURL(file);\r\n      reader.onload = () => {\r\n        let base64Str: string = reader.result as string;\r\n        if (!base64Str) {\r\n          return;\r\n        }\r\n        if (formItemReq_.listPictures.length > 0) {\r\n          formItemReq_.listPictures[0] = {\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          };\r\n        } else {\r\n          formItemReq_.listPictures.push({\r\n            id: new Date().getTime(),\r\n            name: file.name.split('.')[0],\r\n            data: base64Str,\r\n            extension: this._utilityService.getFileExtension(file.name),\r\n            CFile: file\r\n          });\r\n        }\r\n        event.target.value = null;\r\n      };\r\n    }\r\n  }\r\n\r\n  removeImage(pictureId: number, formItemReq_: any) {\r\n    if (formItemReq_.listPictures.length) {\r\n      formItemReq_.listPictures = formItemReq_.listPictures.filter((x: any) => x.id != pictureId)\r\n    }\r\n  }\r\n  renameFile(event: any, index: number, formItemReq_: any) {\r\n    var blob = formItemReq_.listPictures[index].CFile.slice(0, formItemReq_.listPictures[index].CFile.size, formItemReq_.listPictures[index].CFile.type);\r\n    var newFile = new File([blob], `${event.target.value + '.' + formItemReq_.listPictures[index].extension}`, { type: formItemReq_.listPictures[index].CFile.type });\r\n    formItemReq_.listPictures[index].CFile = newFile\r\n  }\r\n\r\n  // 輪播功能方法\r\n  nextImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = (formItemReq.currentImageIndex + 1) % formItemReq.CMatrialUrl.length;\r\n    }\r\n  }\r\n\r\n  prevImage(formItemReq: any) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      formItemReq.currentImageIndex = formItemReq.currentImageIndex === 0\r\n        ? formItemReq.CMatrialUrl.length - 1\r\n        : formItemReq.currentImageIndex - 1;\r\n    }\r\n  }\r\n  getCurrentImage(formItemReq: any): string | null {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0 && formItemReq.currentImageIndex !== undefined) {\r\n      return formItemReq.CMatrialUrl[formItemReq.currentImageIndex];\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 放大功能方法\r\n  openImageModal(formItemReq: any, imageIndex?: number) {\r\n    if (formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0) {\r\n      if (imageIndex !== undefined) {\r\n        formItemReq.currentImageIndex = imageIndex;\r\n      }\r\n      formItemReq.isModalOpen = true;\r\n      // 防止背景滾動\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n  }\r\n\r\n  closeImageModal(formItemReq: any) {\r\n    formItemReq.isModalOpen = false;\r\n    // 恢復背景滾動\r\n    document.body.style.overflow = 'auto';\r\n  }\r\n\r\n  // 模態窗口中的輪播方法\r\n  nextImageModal(formItemReq: any) {\r\n    this.nextImage(formItemReq);\r\n  }\r\n\r\n  prevImageModal(formItemReq: any) {\r\n    this.prevImage(formItemReq);\r\n  }\r\n\r\n  // 鍵盤事件處理\r\n  onKeydown(event: KeyboardEvent, formItemReq: any) {\r\n    if (formItemReq.isModalOpen) {\r\n      switch (event.key) {\r\n        case 'ArrowLeft':\r\n          event.preventDefault();\r\n          this.prevImageModal(formItemReq);\r\n          break;\r\n        case 'ArrowRight':\r\n          event.preventDefault();\r\n          this.nextImageModal(formItemReq);\r\n          break;\r\n        case 'Escape':\r\n          event.preventDefault();\r\n          this.closeImageModal(formItemReq);\r\n          break;\r\n      }\r\n    }\r\n  }\r\n\r\n  onCheckAllChange(checked: boolean, formItemReq_: any) {\r\n    formItemReq_.allSelected = checked;\r\n    this.houseHoldList.forEach(item => {\r\n      formItemReq_.selectedItems[item] = checked;\r\n    });\r\n  }\r\n\r\n  onCheckboxHouseHoldListChange(checked: boolean, item: string, formItemReq_: any) {\r\n    if (checked) {\r\n      formItemReq_.selectedItems[item] = checked;\r\n      formItemReq_.allSelected = this.houseHoldList.every(item => formItemReq_.selectedItems[item] == checked);\r\n    } else {\r\n      formItemReq_.allSelected = false\r\n    }\r\n  }\r\n\r\n\r\n\r\n  onCheckboxRemarkChange(checked: boolean, item: string, formItemReq_: any) {\r\n    formItemReq_.selectedRemarkType[item] = checked;\r\n  }\r\n\r\n  createRemarkObject(CRemarkTypeOptions: string[], CRemarkType: string): { [key: string]: boolean } {\r\n    const remarkObject: { [key: string]: boolean } = {};\r\n    for (const option of CRemarkTypeOptions) {\r\n      remarkObject[option] = false;\r\n    }\r\n    const remarkTypes = CRemarkType.split('-');\r\n    for (const type of remarkTypes) {\r\n      if (CRemarkTypeOptions.includes(type)) {\r\n        remarkObject[type] = true;\r\n      }\r\n    }\r\n    return remarkObject;\r\n  }\r\n\r\n  mergeItems(items: ExtendedSaveListFormItemReq[]): ExtendedSaveListFormItemReq[] {\r\n    const map = new Map<string, { item: ExtendedSaveListFormItemReq, count: number }>();\r\n\r\n    items.forEach(item => {\r\n      const key = `${item.CLocation}_${item.CName}_${item.CPart}`;\r\n      if (map.has(key)) {\r\n        const existing = map.get(key)!;\r\n        existing.count += 1;\r\n      } else {\r\n        map.set(key, { item: { ...item }, count: 1 });\r\n      }\r\n    });\r\n\r\n    return Array.from(map.values()).map(({ item, count }) => ({\r\n      ...item,\r\n      CTotalAnswer: count\r\n    }));\r\n  }\r\n\r\n\r\n  getMaterialList() { // call when create\r\n    this._materialService.apiMaterialGetMaterialListPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CPagi: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n\r\n          this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n\r\n          this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n          this.arrListFormItemReq = res.Entries.map((o: GetMaterialListResponse) => {\r\n            return {\r\n              CDesignFileUrl: null,\r\n              CFormItemHouseHold: null,\r\n              CFormId: null,\r\n              CLocation: o.CLocation,\r\n              CName: o.CName,\r\n              CPart: o.CPart,\r\n              CItemName: `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n              CRemarkType: null,\r\n              CTotalAnswer: 0,\r\n              CRequireAnswer: 1,\r\n              CUiType: 0, selectedItems: {},\r\n              selectedRemarkType: this.selectedRemarkType,\r\n              allSelected: false,\r\n              listPictures: [], selectedCUiType: this.CUiTypeOptions[0],\r\n              currentImageIndex: 0,\r\n              isModalOpen: false,\r\n              CMatrialUrl: o.CSelectPicture ? o.CSelectPicture.map(x1 => x1.CBase64) : []\r\n            }\r\n          })\r\n          this.arrListFormItemReq = [...this.mergeItems(this.arrListFormItemReq)]\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  listFormItem: GetListFormItemRes\r\n  isNew: boolean = true\r\n\r\n  getListFormItem() {\r\n    this._formItemService.apiFormItemGetListFormItemPost$Json({\r\n      body: {\r\n        CBuildCaseId: this.buildCaseId,\r\n        CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n        CIsPaging: false\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.listFormItem = res.Entries\r\n          this.isNew = res.Entries.formItems ? false : true\r\n          if (res.Entries.formItems) {\r\n            this.houseHoldList.forEach(item => this.selectedItems[item] = false);\r\n            this.CRemarkTypeOptions.forEach(item => this.selectedRemarkType[item] = false);\r\n\r\n            this.arrListFormItemReq = res.Entries.formItems.map((o: any) => {\r\n              return {\r\n                CFormId: this.listFormItem.CFormId,\r\n                CDesignFileUrl: o.CDesignFileUrl,\r\n                CMatrialUrl: o.CMatrialUrl || (o.CFirstMatrialUrl ? [o.CFirstMatrialUrl] : []),\r\n                CFile: o.CFile,\r\n                CFormItemHouseHold: o.CFormItemHouseHold,\r\n                CFormItemId: o.CFormItemId,\r\n                CLocation: o.CLocation,\r\n                CName: o.CName,\r\n                CPart: o.CPart,\r\n                CItemName: o.CItemName ? o.CItemName : `${o.CName}-${o.CPart}-${o.CLocation}`,\r\n                CRemarkType: o.CRemarkType,\r\n                CTotalAnswer: o.CTotalAnswer,\r\n                CRequireAnswer: o.CUiType === 3 ? 1 : o.CRequireAnswer,\r\n                CUiType: o.CUiType,\r\n                selectedItems: o.tblFormItemHouseholds.length ? this.createArrayObjectFromArray(this.houseHoldList, o.tblFormItemHouseholds) : { ...this.selectedItems }, selectedRemarkType: o.CRemarkType ? this.createRemarkObject(this.CRemarkTypeOptions, o.CRemarkType) : { ...this.selectedRemarkType },\r\n                allSelected: o.tblFormItemHouseholds.length === this.houseHoldList.length,\r\n                listPictures: [], selectedCUiType: o.CUiType ? this.getItemByValue(o.CUiType, this.CUiTypeOptions) : this.CUiTypeOptions[0],\r\n                currentImageIndex: 0,\r\n                isModalOpen: false\r\n              }\r\n            })\r\n          } else {\r\n            this.getMaterialList()\r\n          }\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n  changeSelectCUiType(formItemReq: any) {\r\n    if (formItemReq.selectedCUiType && formItemReq.selectedCUiType.value === 3) {\r\n      formItemReq.CRequireAnswer = 1\r\n    }\r\n  }\r\n  getHouseHoldListByNoticeType(data: any[]) {\r\n    for (let item of data) {\r\n      if (item.CNoticeType === this.typeContentManagementSalesAccount.CNoticeType) {\r\n        return item.CHouseHoldList;\r\n      }\r\n    }\r\n    return [];\r\n  }\r\n\r\n  arrListFormItemReq: ExtendedSaveListFormItemReq[]\r\n\r\n  getKeysWithTrueValue(obj: Record<string, boolean>): string[] { // {\"key\": true } => [\"key\"]\r\n    return Object.keys(obj).filter(key => obj[key]);\r\n  }\r\n\r\n  getKeysWithTrueValueJoined(obj: Record<string, boolean>): string {\r\n    return Object.keys(obj)\r\n      .filter(key => obj[key])\r\n      .join('-');\r\n  }\r\n\r\n  getCRemarkType(selectedCUiType: any, selectedRemarkType: any): any {\r\n    if (selectedCUiType && selectedCUiType.value == 3) {\r\n      return this.getKeysWithTrueValueJoined(selectedRemarkType)\r\n    }\r\n  }\r\n\r\n  getStringAfterComma(inputString: string): string {\r\n    const parts = inputString.split(',');\r\n    if (parts.length > 1) {\r\n      return parts[1];\r\n    } else return \"\"\r\n  }\r\n\r\n  formatFile(listPictures: any) {\r\n    if (listPictures && listPictures.length > 0) {\r\n      return {\r\n        Base64String: this.getStringAfterComma(listPictures[0].data) || null,\r\n        FileExtension: listPictures[0].extension || null,\r\n        FileName: listPictures[0].CFile.name || listPictures[0].name || null,\r\n      }\r\n    } else return undefined\r\n\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    let hasInvalidCUiType = false;\r\n    let hasInvalidCRequireAnswer = false;\r\n    let hasInvalidItemName = false;\r\n\r\n    for (const item of this.saveListFormItemReq) {\r\n      if (!hasInvalidCUiType && (!item.CUiType)) {\r\n        hasInvalidCUiType = true;\r\n      }\r\n      if (!hasInvalidCRequireAnswer && (!item.CRequireAnswer)) {\r\n        hasInvalidCRequireAnswer = true;\r\n      }\r\n      if (item.CTotalAnswer && item.CRequireAnswer) {\r\n        if (item.CRequireAnswer > item.CTotalAnswer || item.CRequireAnswer < 0) {\r\n          this.valid.addErrorMessage('[必填數量]' + ' <= ' + item.CTotalAnswer + ` (${item.CItemName}) `);\r\n        }\r\n      }\r\n\r\n      if (!hasInvalidItemName && (!item.CItemName)) {\r\n        hasInvalidItemName = true;\r\n      }\r\n    }\r\n    if (hasInvalidCUiType) {\r\n      this.valid.addErrorMessage('[前台UI類型]' + ' 必填');\r\n    }\r\n    if (hasInvalidCRequireAnswer) {\r\n      this.valid.addErrorMessage('[必填數量]' + ' 必填且>0');\r\n    }\r\n    if (hasInvalidItemName) {\r\n      this.valid.addErrorMessage('[廚房-廚具-櫃體]' + ' 必填');\r\n    }\r\n  }\r\n\r\n  saveListFormItemReq: SaveListFormItemReq[]\r\n\r\n  onSubmit() {\r\n    this.saveListFormItemReq = this.arrListFormItemReq.map((e: any) => {\r\n      return {\r\n        CDesignFileUrl: e.CDesignFileUrl ? e.CDesignFileUrl : null,\r\n        CFile: e.listPictures ? this.formatFile(e.listPictures) : undefined,\r\n        CFormItemHouseHold: this.getKeysWithTrueValue(e.selectedItems),\r\n        CFormItemId: e.CFormItemId ? e.CFormItemId : null,\r\n        CFormID: this.isNew ? null : this.listFormItem.CFormId,\r\n        CName: e.CName,\r\n        CPart: e.CPart,\r\n        CLocation: e.CLocation,\r\n        CItemName: e.CItemName, //? e.CItemName : `${e.CName}-${e.CPart}-${e.CLocation}`,\r\n        CRemarkType: e.selectedCUiType.value !== 3 ? null : this.getCRemarkType(e.selectedCUiType, e.selectedRemarkType) || null,\r\n        CTotalAnswer: e.CTotalAnswer,\r\n        CRequireAnswer: e.CRequireAnswer,\r\n        CUiType: e.selectedCUiType.value,\r\n      }\r\n    })\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    if (this.isNew) {\r\n      this.createListFormItem()\r\n\r\n    } else {\r\n      this.saveListFormItem()\r\n    }\r\n  }\r\n\r\n  saveListFormItem() {\r\n    this._formItemService.apiFormItemSaveListFormItemPost$Json({\r\n      body: this.saveListFormItemReq\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  creatListFormItem: CreateListFormItem\r\n\r\n  createListFormItem() {\r\n    this.creatListFormItem = {\r\n      CBuildCaseId: this.buildCaseId,\r\n      CFormItem: this.saveListFormItemReq || null,\r\n      CFormType: this.typeContentManagementSalesAccount.CFormType,\r\n    }\r\n\r\n    this._formItemService.apiFormItemCreateListFormItemPost$Json({\r\n      body: this.creatListFormItem\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        // this.getListFormItem()\r\n        this.goBack()\r\n      }\r\n    })\r\n  }\r\n\r\n  createArrayObjectFromArray(a: string[], b: { CFormItemHouseholdId: number, CFormItemId: number, CHousehold: string, CIsSelect: boolean }[]): { [key: string]: boolean } {\r\n    const c: { [key: string]: boolean } = {};\r\n    for (const item of a) {\r\n      const matchingItem = b.find(bItem => bItem.CHousehold === item && bItem.CIsSelect);\r\n      c[item] = !!matchingItem;\r\n    }\r\n    return c;\r\n  } //[\"House1\", \"House2\", \"House3\"] => [{CHousehold: \"House1\", CIsSelect: true,... }, ... ]\r\n\r\n\r\n  houseHoldList: any[]\r\n\r\n  getListRegularNoticeFileHouseHold() {\r\n    this._regularNoticeFileService.apiRegularNoticeFileGetListRegularNoticeFileHouseHoldPost$Json({\r\n      body: this.buildCaseId\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.houseHoldList = this.getHouseHoldListByNoticeType(res.Entries)\r\n          this.getListFormItem()\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this.location.back()\r\n  }\r\n}\r\n", "<!-- 3.7.1  CNoticeType = 1 -->\r\n<div class=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\">\r\n  <nb-card class=\"shadow-xl border-0 rounded-xl overflow-hidden\">\r\n    <nb-card-header class=\"bg-white border-b border-gray-200 p-6\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-4\">\r\n          <div class=\"w-1 h-8 bg-green-500 rounded-full\"></div>\r\n          <div>\r\n            <ngx-breadcrumb></ngx-breadcrumb>\r\n          </div>\r\n        </div>\r\n        <div class=\"flex items-center space-x-2\">\r\n          <span class=\"px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full font-medium\">\r\n            {{ dynamicTitle }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"p-6 bg-gray-50\">\r\n      <div class=\"space-y-8\">\r\n        <!-- Page Title Section -->\r\n        <div class=\"bg-white rounded-xl p-6 shadow-sm border border-gray-200\">\r\n          <div class=\"flex items-center space-x-3\">\r\n            <div class=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n              <svg class=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                  d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                </path>\r\n              </svg>\r\n            </div>\r\n            <div>\r\n              <h3 class=\"text-xl font-bold text-gray-800\">{{ dynamicTitle }}</h3>\r\n              <p class=\"text-sm text-gray-600 mt-1\">管理選樣項目的詳細設定與配置</p>\r\n            </div>\r\n          </div>\r\n        </div> <!-- Form Items Section -->\r\n        <ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n          <div\r\n            class=\"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-xl\">\r\n            <!-- Item Header -->\r\n            <div class=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200\">\r\n              <div class=\"flex items-center justify-between\">\r\n                <div class=\"flex items-center space-x-3\">\r\n                  <div\r\n                    class=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold\">\r\n                    {{idx + 1}}\r\n                  </div>\r\n                  <div>\r\n                    <h4 class=\"text-lg font-semibold text-gray-800\">\r\n                      {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}\r\n                    </h4>\r\n                    <p class=\"text-sm text-gray-600\">項目編號 #{{idx + 1}}</p>\r\n                  </div>\r\n                </div>\r\n                <div class=\"flex items-center space-x-2\">\r\n                  <span class=\"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full\">\r\n                    {{formItemReq.selectedCUiType?.label || '未設定'}}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- Main Content Area -->\r\n            <div class=\"p-6\">\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n\r\n                <!-- Material Images Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">主要材料示意</label>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 0\" class=\"relative\">\r\n                      <!-- Enhanced Image carousel container -->\r\n                      <div\r\n                        class=\"aspect-square w-full relative overflow-hidden rounded-xl border-2 border-gray-200 cursor-pointer group shadow-md hover:shadow-lg transition-all duration-300\"\r\n                        (click)=\"openImageModal(formItemReq)\">\r\n                        <img class=\"w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\"\r\n                          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n                        <!-- Enhanced Zoom overlay -->\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\r\n                          <div class=\"transform scale-75 group-hover:scale-100 transition-transform duration-300\">\r\n                            <div class=\"w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center\">\r\n                              <svg class=\"w-6 h-6 text-gray-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7\"></path>\r\n                              </svg>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <!-- Navigation buttons -->\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute left-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"prevImage(formItemReq); $event.stopPropagation()\" title=\"上一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <button *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute right-2 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-100 hover:shadow-md transition-all z-10 opacity-0 group-hover:opacity-100\"\r\n                          (click)=\"nextImage(formItemReq); $event.stopPropagation()\" title=\"下一張圖片\">\r\n                          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\">\r\n                            </path>\r\n                          </svg>\r\n                        </button>\r\n\r\n                        <!-- Enhanced Image counter -->\r\n                        <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\"\r\n                          class=\"absolute bottom-2 right-2 bg-black bg-opacity-80 text-white text-xs px-2 py-1 rounded-lg backdrop-blur-sm\">\r\n                          {{(formItemReq.currentImageIndex || 0) + 1}} / {{formItemReq.CMatrialUrl.length}}\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- Enhanced Thumbnail navigation -->\r\n                      <div *ngIf=\"formItemReq.CMatrialUrl.length > 1\" class=\"flex gap-2 mt-3 overflow-x-auto pb-2\">\r\n                        <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n                          class=\"flex-shrink-0 w-12 h-12 border-2 rounded-lg overflow-hidden hover:border-blue-400 transition-all duration-200 cursor-pointer hover:scale-105\"\r\n                          [class.border-blue-500]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.border-gray-300]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-2]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          [class.ring-blue-200]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n                          (click)=\"openImageModal(formItemReq, i)\" [title]=\"'點選放大第 ' + (i + 1) + ' 張圖片'\">\r\n                          <img class=\"w-full h-full object-cover transition-transform hover:scale-110\"\r\n                            [src]=\"imageUrl | base64Image\">\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"!formItemReq.CMatrialUrl || formItemReq.CMatrialUrl.length === 0\"\r\n                      class=\"aspect-square w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-xl bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-12 h-12 mb-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-sm\">無主要材料示意</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Form Fields Section (Enhanced) -->\r\n                <div class=\"lg:col-span-2\">\r\n                  <div class=\"space-y-6\">\r\n                    <div class=\"flex items-center space-x-2 mb-4\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">基本設定</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Form Groups -->\r\n                    <div class=\"space-y-4\">\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'CItemName_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">項目名稱</label>\r\n                        <div class=\"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border border-gray-200\">\r\n                          <span\r\n                            class=\"text-sm text-gray-600 font-medium px-2 py-1 bg-blue-100 rounded-md whitespace-nowrap\">\r\n                            {{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}:\r\n                          </span>\r\n                          <input type=\"text\" [id]=\"'CItemName_' + idx\"\r\n                            class=\"flex-1 border-0 bg-transparent focus:ring-2 focus:ring-blue-500 focus:border-transparent rounded-md p-2\"\r\n                            nbInput [(ngModel)]=\"formItemReq.CItemName\" placeholder=\"例如：廚房檯面\"\r\n                            [disabled]=\"listFormItem.CIsLock ?? false\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'cRequireAnswer_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">必填數量</label>\r\n                        <div class=\"relative\">\r\n                          <input type=\"number\" [id]=\"'cRequireAnswer_' + idx\"\r\n                            class=\"w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 rounded-lg p-3 transition-all duration-200\"\r\n                            nbInput placeholder=\"輸入數量\" [(ngModel)]=\"formItemReq.CRequireAnswer\"\r\n                            [disabled]=\"formItemReq.selectedCUiType.value === 3 || (listFormItem.CIsLock ?? false)\" />\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div class=\"group\">\r\n                        <label [for]=\"'uiType_' + idx\"\r\n                          class=\"block text-sm font-medium text-gray-700 mb-2\">前台UI類型</label>\r\n                        <nb-select placeholder=\"選擇UI類型\" [id]=\"'uiType_' + idx\" [(ngModel)]=\"formItemReq.selectedCUiType\"\r\n                          class=\"w-full border-2 border-gray-200 focus:border-blue-500 rounded-lg transition-all duration-200\"\r\n                          (selectedChange)=\"changeSelectCUiType(formItemReq)\"\r\n                          [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                          <nb-option *ngFor=\"let case of CUiTypeOptions\" [value]=\"case\">\r\n                            {{ case.label }}\r\n                          </nb-option>\r\n                        </nb-select>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Concept Design Section (Enhanced) -->\r\n                <div class=\"lg:col-span-1\">\r\n                  <div class=\"space-y-4\">\r\n                    <div class=\"flex items-center space-x-2\">\r\n                      <svg class=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <label class=\"text-sm font-semibold text-gray-700\">概念設計</label>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Upload Button -->\r\n                    <button\r\n                      class=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                      [disabled]=\"listFormItem.CIsLock\" (click)=\"inputFile.click()\">\r\n                      <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                          d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\">\r\n                        </path>\r\n                      </svg>\r\n                      <span>上傳概念設計圖</span>\r\n                    </button>\r\n                    <input #inputFile type=\"file\" class=\"hidden\" (change)=\"detectFiles($event, formItemReq)\"\r\n                      accept=\"image/png, image/gif, image/jpeg\">\r\n\r\n                    <!-- Enhanced Uploaded Pictures List -->\r\n                    <div *ngIf=\"formItemReq.listPictures && formItemReq.listPictures.length > 0\" class=\"space-y-3\">\r\n                      <div *ngFor=\"let picture of formItemReq.listPictures; let i = index\"\r\n                        class=\"bg-gray-50 border border-gray-200 p-3 rounded-lg hover:shadow-md transition-all duration-200\">\r\n                        <div class=\"relative group\">\r\n                          <img class=\"w-full h-32 object-cover rounded-lg mb-3 border border-gray-200\"\r\n                            [src]=\"picture.data\">\r\n                          <div\r\n                            class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                          </div>\r\n                        </div>\r\n                        <input nbInput\r\n                          class=\"w-full p-2 text-sm mb-2 border border-gray-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          type=\"text\" placeholder=\"圖片說明/檔名\" [value]=\"picture.name\"\r\n                          (blur)=\"renameFile($event, i, formItemReq)\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                        <button\r\n                          class=\"w-full bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-3 rounded-md transition-colors duration-200 text-sm\"\r\n                          (click)=\"removeImage(picture.id, formItemReq)\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n                          <svg class=\"w-4 h-4 inline mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                              d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\">\r\n                            </path>\r\n                          </svg>\r\n                          刪除圖片\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- Enhanced Default Concept Design Image -->\r\n                    <div class=\"space-y-2\"\r\n                      *ngIf=\"formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\">\r\n                      <label class=\"block text-xs font-medium text-gray-600\">預設概念圖</label>\r\n                      <div class=\"relative group\">\r\n                        <img class=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\r\n                          [src]=\"formItemReq.CDesignFileUrl | addBaseFile\">\r\n                        <div\r\n                          class=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg\">\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div\r\n                      *ngIf=\"!formItemReq.CDesignFileUrl && (!formItemReq.listPictures || formItemReq.listPictures.length === 0)\"\r\n                      class=\"h-32 w-full flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 text-gray-400\">\r\n                      <svg class=\"w-8 h-8 mb-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-xs\">無概念設計圖</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- Enhanced Separator -->\r\n              <div class=\"my-8\">\r\n                <div class=\"relative\">\r\n                  <div class=\"absolute inset-0 flex items-center\">\r\n                    <div class=\"w-full border-t border-gray-300\"></div>\r\n                  </div>\r\n                  <div class=\"relative flex justify-center text-sm\">\r\n                    <span class=\"px-4 bg-white text-gray-500 font-medium\">設定選項</span>\r\n                  </div>\r\n                </div>\r\n              </div> <!-- Enhanced Applicable Households Section -->\r\n              <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                <div class=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-blue-800\">適用戶別</h5>\r\n                  </div>\r\n\r\n                  <div class=\"space-y-3\">\r\n                    <label class=\"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\"\r\n                      *ngIf=\"houseHoldList.length\">\r\n                      <nb-checkbox [(checked)]=\"formItemReq.allSelected\" [disabled]=\"listFormItem.CIsLock\"\r\n                        (checkedChange)=\"onCheckAllChange($event, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"font-medium text-blue-700\">全選</span>\r\n                    </label>\r\n\r\n                    <div class=\"grid grid-cols-1 gap-2\" *ngIf=\"houseHoldList.length > 0; else noHouseholds\">\r\n                      <label *ngFor=\"let item of houseHoldList\"\r\n                        class=\"flex items-center cursor-pointer hover:bg-blue-100 p-2 rounded-md transition-colors\">\r\n                        <nb-checkbox [(checked)]=\"formItemReq.selectedItems[item]\" value=\"item\"\r\n                          [disabled]=\"listFormItem.CIsLock\"\r\n                          (checkedChange)=\"onCheckboxHouseHoldListChange($event, item, formItemReq)\" class=\"mr-3\">\r\n                        </nb-checkbox>\r\n                        <span class=\"text-gray-700\">{{ item }}</span>\r\n                      </label>\r\n                    </div>\r\n\r\n                    <ng-template #noHouseholds>\r\n                      <div class=\"text-center py-4\">\r\n                        <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                          viewBox=\"0 0 24 24\">\r\n                          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                            d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\">\r\n                          </path>\r\n                        </svg>\r\n                        <span class=\"text-gray-500 text-sm\">尚無戶別資料</span>\r\n                      </div>\r\n                    </ng-template>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- Enhanced Remark Options Section -->\r\n                <div class=\"bg-orange-50 p-4 rounded-lg border border-orange-200\"\r\n                  *ngIf=\"formItemReq.selectedCUiType.value === 3\">\r\n                  <div class=\"flex items-center space-x-2 mb-4\">\r\n                    <svg class=\"w-5 h-5 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n                        d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                      </path>\r\n                    </svg>\r\n                    <h5 class=\"font-semibold text-orange-800\">備註選項</h5>\r\n                  </div>\r\n\r\n                  <div class=\"grid grid-cols-1 gap-2\"\r\n                    *ngIf=\"CRemarkTypeOptions && CRemarkTypeOptions.length > 0; else noRemarkOptions\">\r\n                    <label *ngFor=\"let remark of CRemarkTypeOptions\"\r\n                      class=\"flex items-center cursor-pointer hover:bg-orange-100 p-2 rounded-md transition-colors\">\r\n                      <nb-checkbox *ngIf=\"formItemReq.selectedRemarkType\"\r\n                        [(checked)]=\"formItemReq.selectedRemarkType[remark]\" [disabled]=\"listFormItem.CIsLock\"\r\n                        value=\"item\" (checkedChange)=\"onCheckboxRemarkChange($event, remark, formItemReq)\" class=\"mr-3\">\r\n                      </nb-checkbox>\r\n                      <span class=\"text-gray-700\">{{ remark }}</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  <ng-template #noRemarkOptions>\r\n                    <div class=\"text-center py-4\">\r\n                      <svg class=\"w-8 h-8 text-gray-400 mx-auto mb-2\" fill=\"none\" stroke=\"currentColor\"\r\n                        viewBox=\"0 0 24 24\">\r\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"1.5\"\r\n                          d=\"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z\">\r\n                        </path>\r\n                      </svg>\r\n                      <span class=\"text-gray-500 text-sm\">尚無備註選項</span>\r\n                    </div>\r\n                  </ng-template>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </ng-container>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <!-- Enhanced Footer -->\r\n    <nb-card-footer class=\"bg-white border-t border-gray-200 p-6 sticky bottom-0 shadow-lg\">\r\n      <div class=\"flex items-center justify-between\">\r\n        <div class=\"flex items-center space-x-2 text-sm text-gray-600\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span>共 {{arrListFormItemReq.length || 0}} 個選樣項目</span>\r\n        </div>\r\n\r\n        <div class=\"flex items-center space-x-4\">\r\n          <button\r\n            class=\"px-6 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-sm hover:shadow-md\"\r\n            (click)=\"goBack()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 19l-7-7m0 0l7-7m-7 7h18\">\r\n              </path>\r\n            </svg>\r\n            <span>取消</span>\r\n          </button>\r\n\r\n          <button\r\n            class=\"px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            (click)=\"onSubmit()\" [disabled]=\"listFormItem.CIsLock ?? false\">\r\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\r\n            </svg>\r\n            <span>儲存變更</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</div>\r\n\r\n<!-- Enhanced Image Modal for each formItemReq -->\r\n<ng-container *ngFor=\"let formItemReq of arrListFormItemReq; let idx = index\">\r\n  <div *ngIf=\"formItemReq.isModalOpen\"\r\n    class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm p-4 animate-fade-in-up\"\r\n    (click)=\"closeImageModal(formItemReq)\" (keydown)=\"onKeydown($event, formItemReq)\" tabindex=\"0\">\r\n\r\n    <!-- Enhanced Close Button -->\r\n    <button\r\n      class=\"modal-close-btn fixed top-6 right-6 z-[60] bg-red-500 bg-opacity-95 hover:bg-red-600 hover:bg-opacity-100 text-white rounded-full w-14 h-14 flex items-center justify-center shadow-2xl\"\r\n      (click)=\"closeImageModal(formItemReq)\" title=\"關閉圖片檢視 (按 ESC 鍵)\">\r\n      <svg class=\"w-7 h-7\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n      </svg>\r\n    </button>\r\n\r\n    <!-- Enhanced Modal Content -->\r\n    <div class=\"relative max-w-7xl max-h-full w-full h-full flex items-center justify-center animate-slide-in-left\"\r\n      (click)=\"$event.stopPropagation()\">\r\n\r\n      <!-- Main Image Container -->\r\n      <div class=\"relative max-w-full max-h-full bg-white rounded-2xl p-2 shadow-2xl\">\r\n        <img class=\"max-w-full max-h-[85vh] object-contain rounded-xl animate-fade-in-up\"\r\n          [src]=\"getCurrentImage(formItemReq) | base64Image\" *ngIf=\"getCurrentImage(formItemReq)\">\r\n\r\n        <!-- Enhanced Navigation Buttons -->\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"prevImageModal(formItemReq)\" title=\"上一張圖片 (按 ← 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M15 19l-7-7 7-7\"></path>\r\n          </svg>\r\n        </button>\r\n\r\n        <button *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n          class=\"modal-nav-btn absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-95 hover:bg-opacity-100 text-gray-800 rounded-full w-16 h-16 flex items-center justify-center shadow-lg z-[55]\"\r\n          (click)=\"nextImageModal(formItemReq)\" title=\"下一張圖片 (按 → 鍵)\">\r\n          <svg class=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2.5\" d=\"M9 5l7 7-7 7\"></path>\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Counter -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-90 text-white px-6 py-3 rounded-full backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-3\">\r\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\">\r\n            </path>\r\n          </svg>\r\n          <span class=\"font-medium text-lg\">{{(formItemReq.currentImageIndex || 0) + 1}} /\r\n            {{formItemReq.CMatrialUrl.length}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Image Info -->\r\n      <div\r\n        class=\"absolute bottom-6 right-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 rounded-lg text-sm backdrop-blur-sm shadow-lg\">\r\n        <div class=\"flex items-center space-x-2\">\r\n          <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\r\n              d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\r\n          </svg>\r\n          <span class=\"font-medium\">{{formItemReq.CName}}-{{formItemReq.CPart}}-{{formItemReq.CLocation}}</span>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Enhanced Thumbnail Strip for Modal -->\r\n      <div *ngIf=\"formItemReq.CMatrialUrl && formItemReq.CMatrialUrl.length > 1\"\r\n        class=\"absolute bottom-32 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 backdrop-blur-md p-4 rounded-xl shadow-2xl max-w-full\">\r\n        <div class=\"flex gap-3 overflow-x-auto max-w-[80vw] modal-thumbnails\">\r\n          <button *ngFor=\"let imageUrl of formItemReq.CMatrialUrl; let i = index\"\r\n            class=\"flex-shrink-0 w-20 h-20 border-3 rounded-xl overflow-hidden hover:border-white transition-all duration-200 hover:scale-105\"\r\n            [class.border-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.border-gray-400]=\"i !== (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-3]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-white]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            [class.ring-opacity-50]=\"i === (formItemReq.currentImageIndex || 0)\"\r\n            (click)=\"formItemReq.currentImageIndex = i\" [title]=\"'跳至第 ' + (i + 1) + ' 張圖片'\">\r\n            <img class=\"w-full h-full object-cover transition-transform duration-200\" [src]=\"imageUrl | base64Image\">\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,gBAAgB,QAAQ,gBAAgB;AAKjD,SAASC,GAAG,QAAQ,MAAM;AAK1B,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SAASC,aAAa,QAAQ,6CAA6C;AAC3E,SAAuBC,MAAM,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,mCAAmC;;;;;;;;;;;;;;;;ICoEzCC,EAAA,CAAAC,SAAA,cAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,cAAA,IAAAN,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAgBpDP,EAAA,CAAAQ,cAAA,iBAE2E;IAAzER,EAAA,CAAAS,UAAA,mBAAAC,8GAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAP,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAY,SAAA,CAAAV,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eACO;IAEXD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAETnB,EAAA,CAAAQ,cAAA,iBAE2E;IAAzER,EAAA,CAAAS,UAAA,mBAAAW,8GAAAT,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAS,GAAA;MAAA,MAAAf,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAASV,MAAA,CAAAkB,SAAA,CAAAhB,cAAA,CAAsB;MAAA,OAAAN,EAAA,CAAAiB,WAAA,CAAEN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;;IAC1DlB,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eACO;IAEXD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAGTnB,EAAA,CAAAQ,cAAA,cACoH;IAClHR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;;;;IADJnB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAyB,kBAAA,OAAAnB,cAAA,CAAAoB,iBAAA,mBAAApB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,MACF;;;;;;IAKA5B,EAAA,CAAAQ,cAAA,iBAMiF;IAA/ER,EAAA,CAAAS,UAAA,mBAAAoB,qHAAA;MAAA,MAAAC,IAAA,GAAA9B,EAAA,CAAAY,aAAA,CAAAmB,GAAA,EAAAC,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,EAAAwB,IAAA,CAA8B;IAAA,EAAC;IACxC9B,EAAA,CAAAC,SAAA,cACiC;;IACnCD,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAJPnB,EAHA,CAAAkC,WAAA,oBAAAJ,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OAAoE,oBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACA,WAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACT,kBAAAI,IAAA,MAAAxB,cAAA,CAAAoB,iBAAA,OACO;IACzB1B,EAAA,CAAAE,UAAA,+CAAA4B,IAAA,8BAAqC;IAE5E9B,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,QAAAgC,WAAA,GAAAnC,EAAA,CAAAO,aAAA,CAA8B;;;;;IATpCP,EAAA,CAAAQ,cAAA,cAA6F;IAC3FR,EAAA,CAAAoC,UAAA,IAAAC,4FAAA,sBAMiF;IAInFrC,EAAA,CAAAmB,YAAA,EAAM;;;;IAVyBnB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAAqB,WAAA,CAA4B;;;;;;IA/C3D3B,EAFF,CAAAQ,cAAA,cAA4F,cAIlD;IAAtCR,EAAA,CAAAS,UAAA,mBAAA6B,kGAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,GAAA;MAAA,MAAAjC,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA6B,cAAA,CAAA3B,cAAA,CAA2B;IAAA,EAAC;IACrCN,EAAA,CAAAoC,UAAA,IAAAI,kFAAA,kBAC0F;IAMtFxC,EAHJ,CAAAQ,cAAA,cACwI,cAC9C,cACM;;IAC1FR,EAAA,CAAAQ,cAAA,cAAyF;IACvFR,EAAA,CAAAC,SAAA,eACmF;IAI3FD,EAHM,CAAAmB,YAAA,EAAM,EACF,EACF,EACF;IAsBNnB,EAnBA,CAAAoC,UAAA,IAAAK,qFAAA,qBAE2E,IAAAC,qFAAA,qBASA,KAAAC,mFAAA,kBASyC;IAGtH3C,EAAA,CAAAmB,YAAA,EAAM;IAGNnB,EAAA,CAAAoC,UAAA,KAAAQ,mFAAA,kBAA6F;IAY/F5C,EAAA,CAAAmB,YAAA,EAAM;;;;;IAtDoDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAAC,cAAA,EAAkC;IAgB/EN,EAAA,CAAAwB,SAAA,GAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IASxC5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAU3C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;IAO1C5B,EAAA,CAAAwB,SAAA,EAAwC;IAAxCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAwC;;;;;IAchD5B,EAAA,CAAAQ,cAAA,cACoJ;;IAClJR,EAAA,CAAAQ,cAAA,eAAkF;IAChFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAsB;IAAAR,EAAA,CAAAuB,MAAA,iDAAO;IAC/BvB,EAD+B,CAAAmB,YAAA,EAAO,EAChC;;;;;IAmDAnB,EAAA,CAAAQ,cAAA,qBAA8D;IAC5DR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAY;;;;IAFmCnB,EAAA,CAAAE,UAAA,UAAA2C,QAAA,CAAc;IAC3D7C,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAD,QAAA,CAAAE,KAAA,MACF;;;;;;IAqCF/C,EAFF,CAAAQ,cAAA,eACuG,eACzE;IAG1BR,EAFA,CAAAC,SAAA,eACuB,eAGjB;IACRD,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAQ,cAAA,iBAGyF;IAAvFR,EAAA,CAAAS,UAAA,kBAAAuC,yGAAArC,MAAA;MAAA,MAAAsC,KAAA,GAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAlB,KAAA;MAAA,MAAA1B,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAQb,MAAA,CAAA+C,UAAA,CAAAxC,MAAA,EAAAsC,KAAA,EAAA3C,cAAA,CAAkC;IAAA,EAAC;IAH7CN,EAAA,CAAAmB,YAAA,EAGyF;IACzFnB,EAAA,CAAAQ,cAAA,kBAE4F;IAA1FR,EAAA,CAAAS,UAAA,mBAAA2C,2GAAA;MAAA,MAAAC,WAAA,GAAArD,EAAA,CAAAY,aAAA,CAAAsC,IAAA,EAAAnC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAkD,WAAA,CAAAD,WAAA,CAAAE,EAAA,EAAAjD,cAAA,CAAoC;IAAA,EAAC;;IAC9CN,EAAA,CAAAQ,cAAA,eAAuF;IACrFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAuB,MAAA,iCACF;IACFvB,EADE,CAAAmB,YAAA,EAAS,EACL;;;;;;;IAnBAnB,EAAA,CAAAwB,SAAA,GAAoB;IAApBxB,EAAA,CAAAE,UAAA,QAAAmD,WAAA,CAAAG,IAAA,EAAAxD,EAAA,CAAAO,aAAA,CAAoB;IAOYP,EAAA,CAAAwB,SAAA,GAAsB;IACZxB,EADV,CAAAE,UAAA,UAAAmD,WAAA,CAAAI,IAAA,CAAsB,cAAAC,QAAA,GAAAtD,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,SAC8B;IAGvC1D,EAAA,CAAAwB,SAAA,EAA0C;IAA1CxB,EAAA,CAAAE,UAAA,cAAA4D,QAAA,GAAA1D,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAE,QAAA,KAAAD,SAAA,GAAAC,QAAA,SAA0C;;;;;IAhB/F9D,EAAA,CAAAQ,cAAA,cAA+F;IAC7FR,EAAA,CAAAoC,UAAA,IAAA2B,kFAAA,mBACuG;IAuBzG/D,EAAA,CAAAmB,YAAA,EAAM;;;;IAxBqBnB,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAE,UAAA,YAAAI,cAAA,CAAA0D,YAAA,CAA6B;;;;;IA6BtDhE,EAFF,CAAAQ,cAAA,eAC6G,iBACpD;IAAAR,EAAA,CAAAuB,MAAA,qCAAK;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACpEnB,EAAA,CAAAQ,cAAA,eAA4B;IAC1BR,EAAA,CAAAC,SAAA,eACmD;;IACnDD,EAAA,CAAAC,SAAA,eAEM;IAEVD,EADE,CAAAmB,YAAA,EAAM,EACF;;;;IALAnB,EAAA,CAAAwB,SAAA,GAAgD;IAAhDxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAG,cAAA,CAAA2D,cAAA,GAAAjE,EAAA,CAAAO,aAAA,CAAgD;;;;;IAOtDP,EAAA,CAAAQ,cAAA,eAE2I;;IACzIR,EAAA,CAAAQ,cAAA,eAAgF;IAC9ER,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAsB;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC9BvB,EAD8B,CAAAmB,YAAA,EAAO,EAC/B;;;;;;IA8BJnB,EAFF,CAAAQ,cAAA,iBAC+B,uBAE0C;IAD1DR,EAAA,CAAAkE,gBAAA,2BAAAC,oHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgE,WAAA,EAAA3D,MAAA,MAAAL,cAAA,CAAAgE,WAAA,GAAA3D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAqC;IAChDX,EAAA,CAAAS,UAAA,2BAAA0D,oHAAAxD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAwD,IAAA;MAAA,MAAA9D,cAAA,GAAAN,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAmE,gBAAA,CAAA5D,MAAA,EAAAL,cAAA,CAAqC;IAAA,EAAC;IACzDN,EAAA,CAAAmB,YAAA,EAAc;IACdnB,EAAA,CAAAQ,cAAA,gBAAwC;IAAAR,EAAA,CAAAuB,MAAA,mBAAE;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC3C;;;;;IAJOnB,EAAA,CAAAwB,SAAA,EAAqC;IAArCxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgE,WAAA,CAAqC;IAACtE,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;;IASlF5D,EAFF,CAAAQ,cAAA,iBAC8F,uBAGF;IAF7ER,EAAA,CAAAkE,gBAAA,2BAAAO,0HAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAsE,aAAA,CAAAF,QAAA,GAAA/D,MAAA,MAAAL,cAAA,CAAAsE,aAAA,CAAAF,QAAA,IAAA/D,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAA6C;IAExDX,EAAA,CAAAS,UAAA,2BAAAgE,0HAAA9D,MAAA;MAAA,MAAA+D,QAAA,GAAA1E,EAAA,CAAAY,aAAA,CAAA+D,IAAA,EAAA5D,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAyE,6BAAA,CAAAlE,MAAA,EAAA+D,QAAA,EAAApE,cAAA,CAAwD;IAAA,EAAC;IAC5EN,EAAA,CAAAmB,YAAA,EAAc;IACdnB,EAAA,CAAAQ,cAAA,gBAA4B;IAAAR,EAAA,CAAAuB,MAAA,GAAU;IACxCvB,EADwC,CAAAmB,YAAA,EAAO,EACvC;;;;;;IALOnB,EAAA,CAAAwB,SAAA,EAA6C;IAA7CxB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAsE,aAAA,CAAAF,QAAA,EAA6C;IACxD1E,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAGP5D,EAAA,CAAAwB,SAAA,GAAU;IAAVxB,EAAA,CAAA8E,iBAAA,CAAAJ,QAAA,CAAU;;;;;IAP1C1E,EAAA,CAAAQ,cAAA,eAAwF;IACtFR,EAAA,CAAAoC,UAAA,IAAA2C,oFAAA,qBAC8F;IAOhG/E,EAAA,CAAAmB,YAAA,EAAM;;;;IARoBnB,EAAA,CAAAwB,SAAA,EAAgB;IAAhBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4E,aAAA,CAAgB;;;;;IAWxChF,EAAA,CAAAQ,cAAA,eAA8B;;IAC5BR,EAAA,CAAAQ,cAAA,eACsB;IACpBR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC7C;;;;;;IAqBNnB,EAAA,CAAAQ,cAAA,uBAEkG;IADhGR,EAAA,CAAAkE,gBAAA,2BAAAe,8IAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,GAAAxE,MAAA,MAAAL,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,IAAAxE,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAoD;IACvCX,EAAA,CAAAS,UAAA,2BAAAwE,8IAAAtE,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAsE,IAAA;MAAA,MAAAC,UAAA,GAAAnF,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAT,cAAA,GAAAN,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAiBb,MAAA,CAAAiF,sBAAA,CAAA1E,MAAA,EAAAwE,UAAA,EAAA7E,cAAA,CAAmD;IAAA,EAAC;IACpFN,EAAA,CAAAmB,YAAA,EAAc;;;;;;IAFZnB,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8E,kBAAA,CAAAD,UAAA,EAAoD;IAACnF,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;;;;;IAH1F5D,EAAA,CAAAQ,cAAA,iBACgG;IAC9FR,EAAA,CAAAoC,UAAA,IAAAkD,wGAAA,2BAEkG;IAElGtF,EAAA,CAAAQ,cAAA,gBAA4B;IAAAR,EAAA,CAAAuB,MAAA,GAAY;IAC1CvB,EAD0C,CAAAmB,YAAA,EAAO,EACzC;;;;;IALQnB,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA8E,kBAAA,CAAoC;IAItBpF,EAAA,CAAAwB,SAAA,GAAY;IAAZxB,EAAA,CAAA8E,iBAAA,CAAAK,UAAA,CAAY;;;;;IAR5CnF,EAAA,CAAAQ,cAAA,eACoF;IAClFR,EAAA,CAAAoC,UAAA,IAAAmD,0FAAA,qBACgG;IAOlGvF,EAAA,CAAAmB,YAAA,EAAM;;;;IARsBnB,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAAoF,kBAAA,CAAqB;;;;;IAW/CxF,EAAA,CAAAQ,cAAA,eAA8B;;IAC5BR,EAAA,CAAAQ,cAAA,eACsB;IACpBR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAoC;IAAAR,EAAA,CAAAuB,MAAA,2CAAM;IAC5CvB,EAD4C,CAAAmB,YAAA,EAAO,EAC7C;;;;;IA9BRnB,EAFF,CAAAQ,cAAA,eACkD,cACF;;IAC5CR,EAAA,CAAAQ,cAAA,eAA2F;IACzFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,cAA0C;IAAAR,EAAA,CAAAuB,MAAA,+BAAI;IAChDvB,EADgD,CAAAmB,YAAA,EAAK,EAC/C;IAcNnB,EAZA,CAAAoC,UAAA,IAAAqD,kFAAA,kBACoF,IAAAC,0FAAA,gCAAA1F,EAAA,CAAA2F,sBAAA,CAWtD;IAWhC3F,EAAA,CAAAmB,YAAA,EAAM;;;;;IAtBDnB,EAAA,CAAAwB,SAAA,GAA2D;IAAAxB,EAA3D,CAAAE,UAAA,SAAAE,MAAA,CAAAoF,kBAAA,IAAApF,MAAA,CAAAoF,kBAAA,CAAA5D,MAAA,KAA2D,aAAAgE,mBAAA,CAAoB;;;;;;IAlU5F5F,EAAA,CAAA6F,uBAAA,GAA8E;IAOpE7F,EANR,CAAAQ,cAAA,cAC2H,cAE9B,aAC1C,cACJ,cAEkE;IACvGR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAM;IAEJnB,EADF,CAAAQ,cAAA,UAAK,aAC6C;IAC9CR,EAAA,CAAAuB,MAAA,GACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAK;IACLnB,EAAA,CAAAQ,cAAA,aAAiC;IAAAR,EAAA,CAAAuB,MAAA,IAAiB;IAEtDvB,EAFsD,CAAAmB,YAAA,EAAI,EAClD,EACF;IAEJnB,EADF,CAAAQ,cAAA,cAAyC,gBACgC;IACrER,EAAA,CAAAuB,MAAA,IACF;IAGNvB,EAHM,CAAAmB,YAAA,EAAO,EACH,EACF,EACF;IASEnB,EANR,CAAAQ,cAAA,eAAiB,eACoC,eAGtB,eACF,cACoB;;IACvCR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,4CAAM;IAC3DvB,EAD2D,CAAAmB,YAAA,EAAQ,EAC7D;IAgENnB,EA9DA,CAAAoC,UAAA,KAAA0D,4EAAA,mBAA4F,KAAAC,4EAAA,kBA+DwD;IASxJ/F,EADE,CAAAmB,YAAA,EAAM,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAA2B,eACF,eACyB;;IAC5CR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IACzDvB,EADyD,CAAAmB,YAAA,EAAQ,EAC3D;IAKFnB,EAFJ,CAAAQ,cAAA,eAAuB,eACF,iBAEsC;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAEjEnB,EADF,CAAAQ,cAAA,eAA0F,gBAEO;IAC7FR,EAAA,CAAAuB,MAAA,IACF;IAAAvB,EAAA,CAAAmB,YAAA,EAAO;IACPnB,EAAA,CAAAQ,cAAA,iBAG+C;IADrCR,EAAA,CAAAkE,gBAAA,2BAAA8B,sGAAArF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA4F,SAAA,EAAAvF,MAAA,MAAAL,cAAA,CAAA4F,SAAA,GAAAvF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAmC;IAGjDX,EALI,CAAAmB,YAAA,EAG+C,EAC3C,EACF;IAGJnB,EADF,CAAAQ,cAAA,eAAmB,iBAEsC;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IAEjEnB,EADF,CAAAQ,cAAA,eAAsB,iBAIwE;IAD/DR,EAAA,CAAAkE,gBAAA,2BAAAiC,sGAAAxF,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAA8F,cAAA,EAAAzF,MAAA,MAAAL,cAAA,CAAA8F,cAAA,GAAAzF,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAwC;IAGzEX,EALI,CAAAmB,YAAA,EAG4F,EACxF,EACF;IAGJnB,EADF,CAAAQ,cAAA,eAAmB,iBAEsC;IAAAR,EAAA,CAAAuB,MAAA,kCAAM;IAAAvB,EAAA,CAAAmB,YAAA,EAAQ;IACrEnB,EAAA,CAAAQ,cAAA,qBAG6C;IAHUR,EAAA,CAAAkE,gBAAA,2BAAAmC,0GAAA1F,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAAf,EAAA,CAAAqE,kBAAA,CAAA/D,cAAA,CAAAgG,eAAA,EAAA3F,MAAA,MAAAL,cAAA,CAAAgG,eAAA,GAAA3F,MAAA;MAAA,OAAAX,EAAA,CAAAiB,WAAA,CAAAN,MAAA;IAAA,EAAyC;IAE9FX,EAAA,CAAAS,UAAA,4BAAA8F,2GAAA;MAAA,MAAAjG,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAkBb,MAAA,CAAAoG,mBAAA,CAAAlG,cAAA,CAAgC;IAAA,EAAC;IAEnDN,EAAA,CAAAoC,UAAA,KAAAqE,kFAAA,wBAA8D;IAOxEzG,EAJQ,CAAAmB,YAAA,EAAY,EACR,EACF,EACF,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAA2B,eACF,cACoB;;IACvCR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAAmD;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IACzDvB,EADyD,CAAAmB,YAAA,EAAQ,EAC3D;IAGNnB,EAAA,CAAAQ,cAAA,kBAEgE;IAA5BR,EAAA,CAAAS,UAAA,mBAAAiG,+FAAA;MAAA1G,EAAA,CAAAY,aAAA,CAAAqF,GAAA;MAAA,MAAAU,aAAA,GAAA3G,EAAA,CAAA4G,WAAA;MAAA,OAAA5G,EAAA,CAAAiB,WAAA,CAAS0F,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;;IAC7D7G,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAuB,MAAA,kDAAO;IACfvB,EADe,CAAAmB,YAAA,EAAO,EACb;IACTnB,EAAA,CAAAQ,cAAA,oBAC4C;IADCR,EAAA,CAAAS,UAAA,oBAAAqG,+FAAAnG,MAAA;MAAA,MAAAL,cAAA,GAAAN,EAAA,CAAAY,aAAA,CAAAqF,GAAA,EAAAlF,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAUb,MAAA,CAAA2G,WAAA,CAAApG,MAAA,EAAAL,cAAA,CAAgC;IAAA,EAAC;IAAxFN,EAAA,CAAAmB,YAAA,EAC4C;IA2C5CnB,EAxCA,CAAAoC,UAAA,KAAA4E,4EAAA,kBAA+F,KAAAC,4EAAA,kBA6Bc,KAAAC,4EAAA,kBAa8B;IAUjJlH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;IAKFnB,EAFJ,CAAAQ,cAAA,eAAkB,eACM,eAC4B;IAC9CR,EAAA,CAAAC,SAAA,eAAmD;IACrDD,EAAA,CAAAmB,YAAA,EAAM;IAEJnB,EADF,CAAAQ,cAAA,eAAkD,gBACM;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAGhEvB,EAHgE,CAAAmB,YAAA,EAAO,EAC7D,EACF,EACF;IAGFnB,EAFJ,CAAAQ,cAAA,eAAmD,eACa,eACd;;IAC5CR,EAAA,CAAAQ,cAAA,eAAyF;IACvFR,EAAA,CAAAC,SAAA,gBAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,cAAwC;IAAAR,EAAA,CAAAuB,MAAA,gCAAI;IAC9CvB,EAD8C,CAAAmB,YAAA,EAAK,EAC7C;IAENnB,EAAA,CAAAQ,cAAA,eAAuB;IAoBrBR,EAnBA,CAAAoC,UAAA,KAAA+E,8EAAA,oBAC+B,KAAAC,4EAAA,kBAOyD,KAAAC,oFAAA,gCAAArH,EAAA,CAAA2F,sBAAA,CAW7D;IAY/B3F,EADE,CAAAmB,YAAA,EAAM,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAkF,4EAAA,kBACkD;IAoCxDtH,EAFI,CAAAmB,YAAA,EAAM,EACF,EACF;;;;;;;;;;;IAlVInB,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAA8C,kBAAA,MAAAyE,OAAA,UACF;IAGIvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAwH,kBAAA,MAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,KAAA,OAAApH,cAAA,CAAAqH,SAAA,MACF;IACiC3H,EAAA,CAAAwB,SAAA,GAAiB;IAAjBxB,EAAA,CAAA8C,kBAAA,+BAAAyE,OAAA,SAAiB;IAKlDvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAA8C,kBAAA,OAAAxC,cAAA,CAAAgG,eAAA,kBAAAhG,cAAA,CAAAgG,eAAA,CAAAvD,KAAA,+BACF;IAqBQ/C,EAAA,CAAAwB,SAAA,IAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,KAAmE;IA8DnE5B,EAAA,CAAAwB,SAAA,EAAsE;IAAtExB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAAqB,WAAA,IAAArB,cAAA,CAAAqB,WAAA,CAAAC,MAAA,OAAsE;IA2BjE5B,EAAA,CAAAwB,SAAA,IAA0B;IAA1BxB,EAAA,CAAAE,UAAA,uBAAAqH,OAAA,CAA0B;IAK7BvH,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAwH,kBAAA,MAAAlH,cAAA,CAAAmH,KAAA,OAAAnH,cAAA,CAAAoH,KAAA,OAAApH,cAAA,CAAAqH,SAAA,OACF;IACmB3H,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAE,UAAA,sBAAAqH,OAAA,CAAyB;IAElCvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA4F,SAAA,CAAmC;IAC3ClG,EAAA,CAAAE,UAAA,cAAA0H,QAAA,GAAAxH,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAgE,QAAA,KAAA/D,SAAA,GAAA+D,QAAA,SAA0C;IAKvC5H,EAAA,CAAAwB,SAAA,GAA+B;IAA/BxB,EAAA,CAAAE,UAAA,4BAAAqH,OAAA,CAA+B;IAGfvH,EAAA,CAAAwB,SAAA,GAA8B;IAA9BxB,EAAA,CAAAE,UAAA,2BAAAqH,OAAA,CAA8B;IAEtBvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAA8F,cAAA,CAAwC;IACnEpG,EAAA,CAAAE,UAAA,aAAAI,cAAA,CAAAgG,eAAA,CAAAuB,KAAA,YAAAC,QAAA,GAAA1H,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAkE,QAAA,KAAAjE,SAAA,GAAAiE,QAAA,UAAuF;IAKpF9H,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAE,UAAA,oBAAAqH,OAAA,CAAuB;IAEEvH,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAE,UAAA,mBAAAqH,OAAA,CAAsB;IAACvH,EAAA,CAAAwE,gBAAA,YAAAlE,cAAA,CAAAgG,eAAA,CAAyC;IAG9FtG,EAAA,CAAAE,UAAA,cAAA6H,QAAA,GAAA3H,MAAA,CAAAuD,YAAA,CAAAC,OAAA,cAAAmE,QAAA,KAAAlE,SAAA,GAAAkE,QAAA,SAA0C;IACd/H,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAE,UAAA,YAAAE,MAAA,CAAA4H,cAAA,CAAiB;IAwBjDhI,EAAA,CAAAwB,SAAA,GAAiC;IAAjCxB,EAAA,CAAAE,UAAA,aAAAE,MAAA,CAAAuD,YAAA,CAAAC,OAAA,CAAiC;IAY7B5D,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,KAAqE;IA6BxE5B,EAAA,CAAAwB,SAAA,EAAwG;IAAxGxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAwG;IAYxG5B,EAAA,CAAAwB,SAAA,EAAyG;IAAzGxB,EAAA,CAAAE,UAAA,UAAAI,cAAA,CAAA2D,cAAA,MAAA3D,cAAA,CAAA0D,YAAA,IAAA1D,cAAA,CAAA0D,YAAA,CAAApC,MAAA,QAAyG;IAqCzG5B,EAAA,CAAAwB,SAAA,IAA0B;IAA1BxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,CAA0B;IAOQ5B,EAAA,CAAAwB,SAAA,EAAgC;IAAAxB,EAAhC,CAAAE,UAAA,SAAAE,MAAA,CAAA4E,aAAA,CAAApD,MAAA,KAAgC,aAAAqG,gBAAA,CAAiB;IA2BvFjI,EAAA,CAAAwB,SAAA,GAA6C;IAA7CxB,EAAA,CAAAE,UAAA,SAAAI,cAAA,CAAAgG,eAAA,CAAAuB,KAAA,OAA6C;;;;;IAkGxD7H,EAAA,CAAAC,SAAA,eAC0F;;;;;;IAAxFD,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAA6H,eAAA,IAAAlI,EAAA,CAAAO,aAAA,CAAkD;;;;;;IAGpDP,EAAA,CAAAQ,cAAA,kBAE8D;IAA5DR,EAAA,CAAAS,UAAA,mBAAA0H,6GAAA;MAAAnI,EAAA,CAAAY,aAAA,CAAAwH,IAAA;MAAA,MAAAF,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAiI,cAAA,CAAAH,eAAA,CAA2B;IAAA,EAAC;;IACrClI,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,eAAmG;IAEvGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;;IAETnB,EAAA,CAAAQ,cAAA,kBAE8D;IAA5DR,EAAA,CAAAS,UAAA,mBAAA6H,6GAAA;MAAAtI,EAAA,CAAAY,aAAA,CAAA2H,IAAA;MAAA,MAAAL,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAAoI,cAAA,CAAAN,eAAA,CAA2B;IAAA,EAAC;;IACrClI,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,eAAgG;IAEpGD,EADE,CAAAmB,YAAA,EAAM,EACC;;;;;IAMTnB,EAFF,CAAAQ,cAAA,eACqJ,cAC1G;;IACvCR,EAAA,CAAAQ,cAAA,cAA2E;IACzER,EAAA,CAAAC,SAAA,eAEO;IACTD,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,gBAAkC;IAAAR,EAAA,CAAAuB,MAAA,GACE;IAExCvB,EAFwC,CAAAmB,YAAA,EAAO,EACvC,EACF;;;;IAHgCnB,EAAA,CAAAwB,SAAA,GACE;IADFxB,EAAA,CAAAyB,kBAAA,MAAAyG,eAAA,CAAAxG,iBAAA,mBAAAwG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KACE;;;;;;IAoBpC5B,EAAA,CAAAQ,cAAA,kBAOkF;IAAhFR,EAAA,CAAAS,UAAA,mBAAAgI,oHAAA;MAAA,MAAAC,KAAA,GAAA1I,EAAA,CAAAY,aAAA,CAAA+H,IAAA,EAAA3G,KAAA;MAAA,MAAAkG,eAAA,GAAAlI,EAAA,CAAAc,aAAA,IAAAC,SAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAAAiH,eAAA,CAAAxG,iBAAA,GAAAgH,KAAA;IAAA,EAA2C;IAC3C1I,EAAA,CAAAC,SAAA,eAAyG;;IAC3GD,EAAA,CAAAmB,YAAA,EAAS;;;;;;IAHPnB,EAJA,CAAAkC,WAAA,iBAAAwG,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OAAiE,oBAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACG,WAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACT,eAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACI,oBAAAgH,KAAA,MAAAR,eAAA,CAAAxG,iBAAA,OACK;IACxB1B,EAAA,CAAAE,UAAA,mCAAAwI,KAAA,8BAAmC;IACL1I,EAAA,CAAAwB,SAAA,EAA8B;IAA9BxB,EAAA,CAAAE,UAAA,QAAAF,EAAA,CAAAG,WAAA,QAAAyI,YAAA,GAAA5I,EAAA,CAAAO,aAAA,CAA8B;;;;;IAT5GP,EAFF,CAAAQ,cAAA,eAC8I,eACtE;IACpER,EAAA,CAAAoC,UAAA,IAAAyG,2FAAA,uBAOkF;IAItF7I,EADE,CAAAmB,YAAA,EAAM,EACF;;;;IAX2BnB,EAAA,CAAAwB,SAAA,GAA4B;IAA5BxB,EAAA,CAAAE,UAAA,YAAAgI,eAAA,CAAAvG,WAAA,CAA4B;;;;;;IAtEjE3B,EAAA,CAAAQ,cAAA,eAEiG;IAAxDR,EAAvC,CAAAS,UAAA,mBAAAqI,iGAAA;MAAA9I,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA4I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC,qBAAAe,mGAAAtI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAYb,MAAA,CAAA8I,SAAA,CAAAvI,MAAA,EAAAuH,eAAA,CAA8B;IAAA,EAAC;IAGjFlI,EAAA,CAAAQ,cAAA,kBAEkE;IAAhER,EAAA,CAAAS,UAAA,mBAAA0I,oGAAA;MAAAnJ,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,MAAAb,eAAA,GAAAlI,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAJ,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASb,MAAA,CAAA4I,eAAA,CAAAd,eAAA,CAA4B;IAAA,EAAC;;IACtClI,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,gBAAwG;IAE5GD,EADE,CAAAmB,YAAA,EAAM,EACC;;IAGTnB,EAAA,CAAAQ,cAAA,eACqC;IAAnCR,EAAA,CAAAS,UAAA,mBAAA2I,iGAAAzI,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAmI,IAAA;MAAA,OAAA/I,EAAA,CAAAiB,WAAA,CAASN,MAAA,CAAAO,eAAA,EAAwB;IAAA,EAAC;IAGlClB,EAAA,CAAAQ,cAAA,eAAgF;IAa9ER,EAZA,CAAAoC,UAAA,IAAAiH,iFAAA,mBAC0F,IAAAC,oFAAA,sBAK5B,IAAAC,oFAAA,sBAQA;IAKhEvJ,EAAA,CAAAmB,YAAA,EAAM;IAGNnB,EAAA,CAAAoC,UAAA,IAAAoH,iFAAA,mBACqJ;IAenJxJ,EAFF,CAAAQ,cAAA,gBACkJ,cACvG;;IACvCR,EAAA,CAAAQ,cAAA,eAA2E;IACzER,EAAA,CAAAC,SAAA,gBACuE;IACzED,EAAA,CAAAmB,YAAA,EAAM;;IACNnB,EAAA,CAAAQ,cAAA,iBAA0B;IAAAR,EAAA,CAAAuB,MAAA,IAAqE;IAEnGvB,EAFmG,CAAAmB,YAAA,EAAO,EAClG,EACF;IAGNnB,EAAA,CAAAoC,UAAA,KAAAqH,kFAAA,mBAC8I;IAelJzJ,EADE,CAAAmB,YAAA,EAAM,EACF;;;;;IA/DsDnB,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAE,UAAA,SAAAE,MAAA,CAAAC,eAAA,CAAA6H,eAAA,EAAkC;IAG/ElI,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAQnE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAUxE5B,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;IAqB3C5B,EAAA,CAAAwB,SAAA,GAAqE;IAArExB,EAAA,CAAAwH,kBAAA,KAAAU,eAAA,CAAAT,KAAA,OAAAS,eAAA,CAAAR,KAAA,OAAAQ,eAAA,CAAAP,SAAA,KAAqE;IAK7F3H,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAvG,WAAA,IAAAuG,eAAA,CAAAvG,WAAA,CAAAC,MAAA,KAAmE;;;;;IApE/E5B,EAAA,CAAA6F,uBAAA,GAA8E;IAC5E7F,EAAA,CAAAoC,UAAA,IAAAsH,2EAAA,oBAEiG;;;;;IAF3F1J,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAE,UAAA,SAAAgI,eAAA,CAAAyB,WAAA,CAA6B;;;ADxXrC,OAAM,MAAOC,4CAA6C,SAAQhK,aAAa;EAC7EiK,YACUC,MAAmB,EACnBC,KAAqB,EACrBC,OAAuB,EACvBC,gBAAiC,EACjCC,yBAAmD,EACnDC,eAA+B,EAC/BC,KAAuB,EACvBC,QAAkB,EAClBC,gBAAiC,EACjCC,aAA2B;IAEnC,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IAIvB,KAAAC,iCAAiC,GAAG;MAClCC,SAAS,EAAE,CAAC;MACZC,WAAW,EAAE;KACd;IACD;IACA,KAAAC,kBAAkB,GAAG,CACnB;MAAE5H,KAAK,EAAE,KAAK;MAAE8E,KAAK,EAAE9H,aAAa,CAAC6K;IAAG,CAAE,EAC1C;MAAE7H,KAAK,EAAE,KAAK;MAAE8E,KAAK,EAAE9H,aAAa,CAAC8K;IAAG,CAAE,CAC3C;IAeD,KAAA7C,cAAc,GAAU,CACtB;MACEH,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,EACD;MACE8E,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,EAAE;MACD8E,KAAK,EAAE,CAAC;MAAE9E,KAAK,EAAE;KAClB,CAAC;IACJ,KAAAyC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IA2CjC,KAAAZ,aAAa,GAA+B,EAAE;IAC9C,KAAAQ,kBAAkB,GAA+B,EAAE;IAqNnD,KAAA0F,KAAK,GAAY,IAAI;EAlSrB;EAUA;EACA,IAAIC,YAAYA,CAAA;IACd,MAAMC,MAAM,GAAG,IAAI,CAACL,kBAAkB,CAACM,IAAI,CAACD,MAAM,IAChDA,MAAM,CAACnD,KAAK,KAAK,IAAI,CAAC2C,iCAAiC,CAACE,WAAW,CACpE;IACD,OAAOM,MAAM,GAAG,QAAQA,MAAM,CAACjI,KAAK,EAAE,GAAG,WAAW;EACtD;EACA;EACAmI,cAAcA,CAACC,UAAyB;IACtC,IAAI,IAAI,CAACR,kBAAkB,CAACS,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACnD,KAAK,KAAKsD,UAAU,CAAC,EAAE;MACvE,IAAI,CAACX,iCAAiC,CAACE,WAAW,GAAGS,UAAU;IACjE;EACF;EAY8BE,QAAQA,CAAA;IACpC,IAAI,CAACtB,KAAK,CAACuB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,IAAIA,MAAM,EAAE;QACV,MAAMC,OAAO,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;QAChC,MAAMnI,EAAE,GAAGkI,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC;QACjC,IAAI,CAACE,WAAW,GAAGpI,EAAE;QAErB,IAAI,IAAI,CAACoI,WAAW,GAAG,CAAC,EAAE;UACxB,IAAI,CAACC,iCAAiC,EAAE;QAC1C,CAAC,MAAM;UACL;UACAC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACH,WAAW,CAAC;UACvD,IAAI,CAAC3B,OAAO,CAAC+B,YAAY,CAAC,iBAAiB,CAAC;UAC5C,IAAI,CAACC,MAAM,EAAE;QACf;MACF;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACjC,KAAK,CAACkC,WAAW,CAACV,SAAS,CAACU,WAAW,IAAG;MAC7C,IAAIA,WAAW,CAAC,WAAW,CAAC,EAAE;QAC5B,MAAMC,SAAS,GAAG,CAACD,WAAW,CAAC,WAAW,CAAC;QAC3C,IAAI,CAACf,cAAc,CAACgB,SAAS,CAAC;MAChC;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT;IACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEAC,cAAcA,CAAC3E,KAAU,EAAE4E,OAAc;IACvC,KAAK,MAAMC,IAAI,IAAID,OAAO,EAAE;MAC1B,IAAIC,IAAI,CAAC7E,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAO6E,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAQA3F,WAAWA,CAAC4F,KAAU,EAAEC,YAAiB;IACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR,IAAIG,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC7BD,MAAM,CAACE,aAAa,CAACL,IAAI,CAAC;MAC1BG,MAAM,CAACG,MAAM,GAAG,MAAK;QACnB,IAAIC,SAAS,GAAWJ,MAAM,CAACK,MAAgB;QAC/C,IAAI,CAACD,SAAS,EAAE;UACd;QACF;QACA,IAAIR,YAAY,CAAC5I,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;UACxCgL,YAAY,CAAC5I,YAAY,CAAC,CAAC,CAAC,GAAG;YAC7BT,EAAE,EAAE,IAAI+J,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9J,IAAI,EAAEoJ,IAAI,CAACpJ,IAAI,CAAC+J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhK,IAAI,EAAE4J,SAAS;YACfK,SAAS,EAAE,IAAI,CAACtD,eAAe,CAACuD,gBAAgB,CAACb,IAAI,CAACpJ,IAAI,CAAC;YAC3DkK,KAAK,EAAEd;WACR;QACH,CAAC,MAAM;UACLD,YAAY,CAAC5I,YAAY,CAAC4J,IAAI,CAAC;YAC7BrK,EAAE,EAAE,IAAI+J,IAAI,EAAE,CAACC,OAAO,EAAE;YACxB9J,IAAI,EAAEoJ,IAAI,CAACpJ,IAAI,CAAC+J,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7BhK,IAAI,EAAE4J,SAAS;YACfK,SAAS,EAAE,IAAI,CAACtD,eAAe,CAACuD,gBAAgB,CAACb,IAAI,CAACpJ,IAAI,CAAC;YAC3DkK,KAAK,EAAEd;WACR,CAAC;QACJ;QACAF,KAAK,CAACG,MAAM,CAACjF,KAAK,GAAG,IAAI;MAC3B,CAAC;IACH;EACF;EAEAvE,WAAWA,CAACuK,SAAiB,EAAEjB,YAAiB;IAC9C,IAAIA,YAAY,CAAC5I,YAAY,CAACpC,MAAM,EAAE;MACpCgL,YAAY,CAAC5I,YAAY,GAAG4I,YAAY,CAAC5I,YAAY,CAAC8J,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACxK,EAAE,IAAIsK,SAAS,CAAC;IAC7F;EACF;EACA1K,UAAUA,CAACwJ,KAAU,EAAE3K,KAAa,EAAE4K,YAAiB;IACrD,IAAIoB,IAAI,GAAGpB,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAAC2L,KAAK,CAACM,KAAK,CAAC,CAAC,EAAErB,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAAC2L,KAAK,CAACO,IAAI,EAAEtB,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAAC2L,KAAK,CAACQ,IAAI,CAAC;IACpJ,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE,GAAGrB,KAAK,CAACG,MAAM,CAACjF,KAAK,GAAG,GAAG,GAAG+E,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAACyL,SAAS,EAAE,EAAE;MAAEU,IAAI,EAAEvB,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAAC2L,KAAK,CAACQ;IAAI,CAAE,CAAC;IACjKvB,YAAY,CAAC5I,YAAY,CAAChC,KAAK,CAAC,CAAC2L,KAAK,GAAGS,OAAO;EAClD;EAEA;EACA9M,SAASA,CAACgN,WAAgB;IACxB,IAAIA,WAAW,CAAC3M,WAAW,IAAI2M,WAAW,CAAC3M,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE0M,WAAW,CAAC5M,iBAAiB,GAAG,CAAC4M,WAAW,CAAC5M,iBAAiB,GAAG,CAAC,IAAI4M,WAAW,CAAC3M,WAAW,CAACC,MAAM;IACtG;EACF;EAEAZ,SAASA,CAACsN,WAAgB;IACxB,IAAIA,WAAW,CAAC3M,WAAW,IAAI2M,WAAW,CAAC3M,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE0M,WAAW,CAAC5M,iBAAiB,GAAG4M,WAAW,CAAC5M,iBAAiB,KAAK,CAAC,GAC/D4M,WAAW,CAAC3M,WAAW,CAACC,MAAM,GAAG,CAAC,GAClC0M,WAAW,CAAC5M,iBAAiB,GAAG,CAAC;IACvC;EACF;EACArB,eAAeA,CAACiO,WAAgB;IAC9B,IAAIA,WAAW,CAAC3M,WAAW,IAAI2M,WAAW,CAAC3M,WAAW,CAACC,MAAM,GAAG,CAAC,IAAI0M,WAAW,CAAC5M,iBAAiB,KAAKmC,SAAS,EAAE;MAChH,OAAOyK,WAAW,CAAC3M,WAAW,CAAC2M,WAAW,CAAC5M,iBAAiB,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;EAEA;EACAO,cAAcA,CAACqM,WAAgB,EAAEC,UAAmB;IAClD,IAAID,WAAW,CAAC3M,WAAW,IAAI2M,WAAW,CAAC3M,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACjE,IAAI2M,UAAU,KAAK1K,SAAS,EAAE;QAC5ByK,WAAW,CAAC5M,iBAAiB,GAAG6M,UAAU;MAC5C;MACAD,WAAW,CAAC3E,WAAW,GAAG,IAAI;MAC9B;MACAyC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;EACF;EAEAvD,eAAeA,CAACsF,WAAgB;IAC9BA,WAAW,CAAC3E,WAAW,GAAG,KAAK;IAC/B;IACAyC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACvC;EAEA;EACA/D,cAAcA,CAAC8F,WAAgB;IAC7B,IAAI,CAAChN,SAAS,CAACgN,WAAW,CAAC;EAC7B;EAEAjG,cAAcA,CAACiG,WAAgB;IAC7B,IAAI,CAACtN,SAAS,CAACsN,WAAW,CAAC;EAC7B;EAEA;EACApF,SAASA,CAACyD,KAAoB,EAAE2B,WAAgB;IAC9C,IAAIA,WAAW,CAAC3E,WAAW,EAAE;MAC3B,QAAQgD,KAAK,CAAC6B,GAAG;QACf,KAAK,WAAW;UACd7B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACpG,cAAc,CAACiG,WAAW,CAAC;UAChC;QACF,KAAK,YAAY;UACf3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACjG,cAAc,CAAC8F,WAAW,CAAC;UAChC;QACF,KAAK,QAAQ;UACX3B,KAAK,CAAC8B,cAAc,EAAE;UACtB,IAAI,CAACzF,eAAe,CAACsF,WAAW,CAAC;UACjC;MACJ;IACF;EACF;EAEA/J,gBAAgBA,CAACmK,OAAgB,EAAE9B,YAAiB;IAClDA,YAAY,CAACtI,WAAW,GAAGoK,OAAO;IAClC,IAAI,CAAC1J,aAAa,CAAC2J,OAAO,CAACjC,IAAI,IAAG;MAChCE,YAAY,CAAChI,aAAa,CAAC8H,IAAI,CAAC,GAAGgC,OAAO;IAC5C,CAAC,CAAC;EACJ;EAEA7J,6BAA6BA,CAAC6J,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IAC7E,IAAI8B,OAAO,EAAE;MACX9B,YAAY,CAAChI,aAAa,CAAC8H,IAAI,CAAC,GAAGgC,OAAO;MAC1C9B,YAAY,CAACtI,WAAW,GAAG,IAAI,CAACU,aAAa,CAAC4J,KAAK,CAAClC,IAAI,IAAIE,YAAY,CAAChI,aAAa,CAAC8H,IAAI,CAAC,IAAIgC,OAAO,CAAC;IAC1G,CAAC,MAAM;MACL9B,YAAY,CAACtI,WAAW,GAAG,KAAK;IAClC;EACF;EAIAe,sBAAsBA,CAACqJ,OAAgB,EAAEhC,IAAY,EAAEE,YAAiB;IACtEA,YAAY,CAACxH,kBAAkB,CAACsH,IAAI,CAAC,GAAGgC,OAAO;EACjD;EAEAG,kBAAkBA,CAACrJ,kBAA4B,EAAEsJ,WAAmB;IAClE,MAAMC,YAAY,GAA+B,EAAE;IACnD,KAAK,MAAM/D,MAAM,IAAIxF,kBAAkB,EAAE;MACvCuJ,YAAY,CAAC/D,MAAM,CAAC,GAAG,KAAK;IAC9B;IACA,MAAMgE,WAAW,GAAGF,WAAW,CAACtB,KAAK,CAAC,GAAG,CAAC;IAC1C,KAAK,MAAMW,IAAI,IAAIa,WAAW,EAAE;MAC9B,IAAIxJ,kBAAkB,CAACyJ,QAAQ,CAACd,IAAI,CAAC,EAAE;QACrCY,YAAY,CAACZ,IAAI,CAAC,GAAG,IAAI;MAC3B;IACF;IACA,OAAOY,YAAY;EACrB;EAEAG,UAAUA,CAACC,KAAoC;IAC7C,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAgE;IAEnFF,KAAK,CAACR,OAAO,CAACjC,IAAI,IAAG;MACnB,MAAM8B,GAAG,GAAG,GAAG9B,IAAI,CAAC/E,SAAS,IAAI+E,IAAI,CAACjF,KAAK,IAAIiF,IAAI,CAAChF,KAAK,EAAE;MAC3D,IAAI0H,GAAG,CAACE,GAAG,CAACd,GAAG,CAAC,EAAE;QAChB,MAAMe,QAAQ,GAAGH,GAAG,CAAC1D,GAAG,CAAC8C,GAAG,CAAE;QAC9Be,QAAQ,CAACC,KAAK,IAAI,CAAC;MACrB,CAAC,MAAM;QACLJ,GAAG,CAACK,GAAG,CAACjB,GAAG,EAAE;UAAE9B,IAAI,EAAE;YAAE,GAAGA;UAAI,CAAE;UAAE8C,KAAK,EAAE;QAAC,CAAE,CAAC;MAC/C;IACF,CAAC,CAAC;IAEF,OAAOE,KAAK,CAACC,IAAI,CAACP,GAAG,CAACQ,MAAM,EAAE,CAAC,CAACR,GAAG,CAAC,CAAC;MAAE1C,IAAI;MAAE8C;IAAK,CAAE,MAAM;MACxD,GAAG9C,IAAI;MACPmD,YAAY,EAAEL;KACf,CAAC,CAAC;EACL;EAGAM,eAAeA,CAAA;IACb,IAAI,CAACxF,gBAAgB,CAACyF,mCAAmC,CAAC;MACxD1D,IAAI,EAAE;QACJ2D,YAAY,EAAE,IAAI,CAACrE,WAAW;QAC9BsE,KAAK,EAAE;;KAEV,CAAC,CAACC,IAAI,CACLxQ,GAAG,CAACyQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QAEtC,IAAI,CAACrL,aAAa,CAAC2J,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC9H,aAAa,CAAC8H,IAAI,CAAC,GAAG,KAAK,CAAC;QAEpE,IAAI,CAAClH,kBAAkB,CAACmJ,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACtH,kBAAkB,CAACsH,IAAI,CAAC,GAAG,KAAK,CAAC;QAE9E,IAAI,CAAC4D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAAChB,GAAG,CAAEmB,CAA0B,IAAI;UACvE,OAAO;YACLtM,cAAc,EAAE,IAAI;YACpBuM,kBAAkB,EAAE,IAAI;YACxBC,OAAO,EAAE,IAAI;YACb9I,SAAS,EAAE4I,CAAC,CAAC5I,SAAS;YACtBF,KAAK,EAAE8I,CAAC,CAAC9I,KAAK;YACdC,KAAK,EAAE6I,CAAC,CAAC7I,KAAK;YACdxB,SAAS,EAAE,GAAGqK,CAAC,CAAC9I,KAAK,IAAI8I,CAAC,CAAC7I,KAAK,IAAI6I,CAAC,CAAC5I,SAAS,EAAE;YACjDmH,WAAW,EAAE,IAAI;YACjBe,YAAY,EAAE,CAAC;YACfzJ,cAAc,EAAE,CAAC;YACjBsK,OAAO,EAAE,CAAC;YAAE9L,aAAa,EAAE,EAAE;YAC7BQ,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;YAC3Cd,WAAW,EAAE,KAAK;YAClBN,YAAY,EAAE,EAAE;YAAEsC,eAAe,EAAE,IAAI,CAAC0B,cAAc,CAAC,CAAC,CAAC;YACzDtG,iBAAiB,EAAE,CAAC;YACpBiI,WAAW,EAAE,KAAK;YAClBhI,WAAW,EAAE4O,CAAC,CAACI,cAAc,GAAGJ,CAAC,CAACI,cAAc,CAACvB,GAAG,CAACwB,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,GAAG;WAC1E;QACH,CAAC,CAAC;QACF,IAAI,CAACP,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACpB,UAAU,CAAC,IAAI,CAACoB,kBAAkB,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CACH,CAAC/E,SAAS,EAAE;EACf;EAKAuF,eAAeA,CAAA;IACb,IAAI,CAAC7G,gBAAgB,CAAC8G,mCAAmC,CAAC;MACxD1E,IAAI,EAAE;QACJ2D,YAAY,EAAE,IAAI,CAACrE,WAAW;QAC9BlB,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC,SAAS;QAC3DuG,SAAS,EAAE;;KAEd,CAAC,CAACd,IAAI,CACLxQ,GAAG,CAACyQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC1M,YAAY,GAAGwM,GAAG,CAACC,OAAO;QAC/B,IAAI,CAACtF,KAAK,GAAGqF,GAAG,CAACC,OAAO,CAACa,SAAS,GAAG,KAAK,GAAG,IAAI;QACjD,IAAId,GAAG,CAACC,OAAO,CAACa,SAAS,EAAE;UACzB,IAAI,CAACjM,aAAa,CAAC2J,OAAO,CAACjC,IAAI,IAAI,IAAI,CAAC9H,aAAa,CAAC8H,IAAI,CAAC,GAAG,KAAK,CAAC;UACpE,IAAI,CAAClH,kBAAkB,CAACmJ,OAAO,CAACjC,IAAI,IAAI,IAAI,CAACtH,kBAAkB,CAACsH,IAAI,CAAC,GAAG,KAAK,CAAC;UAE9E,IAAI,CAAC4D,kBAAkB,GAAGH,GAAG,CAACC,OAAO,CAACa,SAAS,CAAC7B,GAAG,CAAEmB,CAAM,IAAI;YAC7D,OAAO;cACLE,OAAO,EAAE,IAAI,CAAC9M,YAAY,CAAC8M,OAAO;cAClCxM,cAAc,EAAEsM,CAAC,CAACtM,cAAc;cAChCtC,WAAW,EAAE4O,CAAC,CAAC5O,WAAW,KAAK4O,CAAC,CAACW,gBAAgB,GAAG,CAACX,CAAC,CAACW,gBAAgB,CAAC,GAAG,EAAE,CAAC;cAC9EvD,KAAK,EAAE4C,CAAC,CAAC5C,KAAK;cACd6C,kBAAkB,EAAED,CAAC,CAACC,kBAAkB;cACxCW,WAAW,EAAEZ,CAAC,CAACY,WAAW;cAC1BxJ,SAAS,EAAE4I,CAAC,CAAC5I,SAAS;cACtBF,KAAK,EAAE8I,CAAC,CAAC9I,KAAK;cACdC,KAAK,EAAE6I,CAAC,CAAC7I,KAAK;cACdxB,SAAS,EAAEqK,CAAC,CAACrK,SAAS,GAAGqK,CAAC,CAACrK,SAAS,GAAG,GAAGqK,CAAC,CAAC9I,KAAK,IAAI8I,CAAC,CAAC7I,KAAK,IAAI6I,CAAC,CAAC5I,SAAS,EAAE;cAC7EmH,WAAW,EAAEyB,CAAC,CAACzB,WAAW;cAC1Be,YAAY,EAAEU,CAAC,CAACV,YAAY;cAC5BzJ,cAAc,EAAEmK,CAAC,CAACG,OAAO,KAAK,CAAC,GAAG,CAAC,GAAGH,CAAC,CAACnK,cAAc;cACtDsK,OAAO,EAAEH,CAAC,CAACG,OAAO;cAClB9L,aAAa,EAAE2L,CAAC,CAACa,qBAAqB,CAACxP,MAAM,GAAG,IAAI,CAACyP,0BAA0B,CAAC,IAAI,CAACrM,aAAa,EAAEuL,CAAC,CAACa,qBAAqB,CAAC,GAAG;gBAAE,GAAG,IAAI,CAACxM;cAAa,CAAE;cAAEQ,kBAAkB,EAAEmL,CAAC,CAACzB,WAAW,GAAG,IAAI,CAACD,kBAAkB,CAAC,IAAI,CAACrJ,kBAAkB,EAAE+K,CAAC,CAACzB,WAAW,CAAC,GAAG;gBAAE,GAAG,IAAI,CAAC1J;cAAkB,CAAE;cAC9Rd,WAAW,EAAEiM,CAAC,CAACa,qBAAqB,CAACxP,MAAM,KAAK,IAAI,CAACoD,aAAa,CAACpD,MAAM;cACzEoC,YAAY,EAAE,EAAE;cAAEsC,eAAe,EAAEiK,CAAC,CAACG,OAAO,GAAG,IAAI,CAAClE,cAAc,CAAC+D,CAAC,CAACG,OAAO,EAAE,IAAI,CAAC1I,cAAc,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAAC;cAC3HtG,iBAAiB,EAAE,CAAC;cACpBiI,WAAW,EAAE;aACd;UACH,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACmG,eAAe,EAAE;QACxB;MACF;IACF,CAAC,CAAC,CACH,CAACvE,SAAS,EAAE;EACf;EAEA/E,mBAAmBA,CAAC8H,WAAgB;IAClC,IAAIA,WAAW,CAAChI,eAAe,IAAIgI,WAAW,CAAChI,eAAe,CAACuB,KAAK,KAAK,CAAC,EAAE;MAC1EyG,WAAW,CAAClI,cAAc,GAAG,CAAC;IAChC;EACF;EACAkL,4BAA4BA,CAAC9N,IAAW;IACtC,KAAK,IAAIkJ,IAAI,IAAIlJ,IAAI,EAAE;MACrB,IAAIkJ,IAAI,CAAChC,WAAW,KAAK,IAAI,CAACF,iCAAiC,CAACE,WAAW,EAAE;QAC3E,OAAOgC,IAAI,CAAC6E,cAAc;MAC5B;IACF;IACA,OAAO,EAAE;EACX;EAIAC,oBAAoBA,CAACC,GAA4B;IAC/C,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC3D,MAAM,CAACU,GAAG,IAAIiD,GAAG,CAACjD,GAAG,CAAC,CAAC;EACjD;EAEAoD,0BAA0BA,CAACH,GAA4B;IACrD,OAAOC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACpB3D,MAAM,CAACU,GAAG,IAAIiD,GAAG,CAACjD,GAAG,CAAC,CAAC,CACvBqD,IAAI,CAAC,GAAG,CAAC;EACd;EAEAC,cAAcA,CAACxL,eAAoB,EAAElB,kBAAuB;IAC1D,IAAIkB,eAAe,IAAIA,eAAe,CAACuB,KAAK,IAAI,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC+J,0BAA0B,CAACxM,kBAAkB,CAAC;IAC5D;EACF;EAEA2M,mBAAmBA,CAACC,WAAmB;IACrC,MAAMC,KAAK,GAAGD,WAAW,CAACxE,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIyE,KAAK,CAACrQ,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOqQ,KAAK,CAAC,CAAC,CAAC;IACjB,CAAC,MAAM,OAAO,EAAE;EAClB;EAEAC,UAAUA,CAAClO,YAAiB;IAC1B,IAAIA,YAAY,IAAIA,YAAY,CAACpC,MAAM,GAAG,CAAC,EAAE;MAC3C,OAAO;QACLuQ,YAAY,EAAE,IAAI,CAACJ,mBAAmB,CAAC/N,YAAY,CAAC,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,IAAI;QACpE4O,aAAa,EAAEpO,YAAY,CAAC,CAAC,CAAC,CAACyJ,SAAS,IAAI,IAAI;QAChD4E,QAAQ,EAAErO,YAAY,CAAC,CAAC,CAAC,CAAC2J,KAAK,CAAClK,IAAI,IAAIO,YAAY,CAAC,CAAC,CAAC,CAACP,IAAI,IAAI;OACjE;IACH,CAAC,MAAM,OAAOI,SAAS;EAEzB;EAGAyO,UAAUA,CAAA;IACR,IAAI,CAAClI,KAAK,CAACmI,KAAK,EAAE;IAClB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,wBAAwB,GAAG,KAAK;IACpC,IAAIC,kBAAkB,GAAG,KAAK;IAE9B,KAAK,MAAMhG,IAAI,IAAI,IAAI,CAACiG,mBAAmB,EAAE;MAC3C,IAAI,CAACH,iBAAiB,IAAK,CAAC9F,IAAI,CAACgE,OAAQ,EAAE;QACzC8B,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAI,CAACC,wBAAwB,IAAK,CAAC/F,IAAI,CAACtG,cAAe,EAAE;QACvDqM,wBAAwB,GAAG,IAAI;MACjC;MACA,IAAI/F,IAAI,CAACmD,YAAY,IAAInD,IAAI,CAACtG,cAAc,EAAE;QAC5C,IAAIsG,IAAI,CAACtG,cAAc,GAAGsG,IAAI,CAACmD,YAAY,IAAInD,IAAI,CAACtG,cAAc,GAAG,CAAC,EAAE;UACtE,IAAI,CAACgE,KAAK,CAACwI,eAAe,CAAC,QAAQ,GAAG,MAAM,GAAGlG,IAAI,CAACmD,YAAY,GAAG,KAAKnD,IAAI,CAACxG,SAAS,IAAI,CAAC;QAC7F;MACF;MAEA,IAAI,CAACwM,kBAAkB,IAAK,CAAChG,IAAI,CAACxG,SAAU,EAAE;QAC5CwM,kBAAkB,GAAG,IAAI;MAC3B;IACF;IACA,IAAIF,iBAAiB,EAAE;MACrB,IAAI,CAACpI,KAAK,CAACwI,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAChD;IACA,IAAIH,wBAAwB,EAAE;MAC5B,IAAI,CAACrI,KAAK,CAACwI,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACjD;IACA,IAAIF,kBAAkB,EAAE;MACtB,IAAI,CAACtI,KAAK,CAACwI,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;IAClD;EACF;EAIAC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,GAAG,IAAI,CAACrC,kBAAkB,CAAClB,GAAG,CAAE0D,CAAM,IAAI;MAChE,OAAO;QACL7O,cAAc,EAAE6O,CAAC,CAAC7O,cAAc,GAAG6O,CAAC,CAAC7O,cAAc,GAAG,IAAI;QAC1D0J,KAAK,EAAEmF,CAAC,CAAC9O,YAAY,GAAG,IAAI,CAACkO,UAAU,CAACY,CAAC,CAAC9O,YAAY,CAAC,GAAGH,SAAS;QACnE2M,kBAAkB,EAAE,IAAI,CAACgB,oBAAoB,CAACsB,CAAC,CAAClO,aAAa,CAAC;QAC9DuM,WAAW,EAAE2B,CAAC,CAAC3B,WAAW,GAAG2B,CAAC,CAAC3B,WAAW,GAAG,IAAI;QACjD4B,OAAO,EAAE,IAAI,CAACjI,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnH,YAAY,CAAC8M,OAAO;QACtDhJ,KAAK,EAAEqL,CAAC,CAACrL,KAAK;QACdC,KAAK,EAAEoL,CAAC,CAACpL,KAAK;QACdC,SAAS,EAAEmL,CAAC,CAACnL,SAAS;QACtBzB,SAAS,EAAE4M,CAAC,CAAC5M,SAAS;QAAE;QACxB4I,WAAW,EAAEgE,CAAC,CAACxM,eAAe,CAACuB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACiK,cAAc,CAACgB,CAAC,CAACxM,eAAe,EAAEwM,CAAC,CAAC1N,kBAAkB,CAAC,IAAI,IAAI;QACxHyK,YAAY,EAAEiD,CAAC,CAACjD,YAAY;QAC5BzJ,cAAc,EAAE0M,CAAC,CAAC1M,cAAc;QAChCsK,OAAO,EAAEoC,CAAC,CAACxM,eAAe,CAACuB;OAC5B;IACH,CAAC,CAAC;IACF,IAAI,CAACyK,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClI,KAAK,CAAC4I,aAAa,CAACpR,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACoI,OAAO,CAACiJ,aAAa,CAAC,IAAI,CAAC7I,KAAK,CAAC4I,aAAa,CAAC;MACpD;IACF;IACA,IAAI,IAAI,CAAClI,KAAK,EAAE;MACd,IAAI,CAACoI,kBAAkB,EAAE;IAE3B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,EAAE;IACzB;EACF;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClJ,gBAAgB,CAACmJ,oCAAoC,CAAC;MACzD/G,IAAI,EAAE,IAAI,CAACsG;KACZ,CAAC,CAACpH,SAAS,CAAC4E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACrG,OAAO,CAACqJ,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACrH,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAIAkH,kBAAkBA,CAAA;IAChB,IAAI,CAACI,iBAAiB,GAAG;MACvBtD,YAAY,EAAE,IAAI,CAACrE,WAAW;MAC9B4H,SAAS,EAAE,IAAI,CAACZ,mBAAmB,IAAI,IAAI;MAC3ClI,SAAS,EAAE,IAAI,CAACD,iCAAiC,CAACC;KACnD;IAED,IAAI,CAACR,gBAAgB,CAACuJ,sCAAsC,CAAC;MAC3DnH,IAAI,EAAE,IAAI,CAACiH;KACZ,CAAC,CAAC/H,SAAS,CAAC4E,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACrG,OAAO,CAACqJ,aAAa,CAAC,MAAM,CAAC;QAClC;QACA,IAAI,CAACrH,MAAM,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEAqF,0BAA0BA,CAACoC,CAAW,EAAEC,CAAkG;IACxI,MAAMC,CAAC,GAA+B,EAAE;IACxC,KAAK,MAAMjH,IAAI,IAAI+G,CAAC,EAAE;MACpB,MAAMG,YAAY,GAAGF,CAAC,CAACzI,IAAI,CAAC4I,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKpH,IAAI,IAAImH,KAAK,CAACE,SAAS,CAAC;MAClFJ,CAAC,CAACjH,IAAI,CAAC,GAAG,CAAC,CAACkH,YAAY;IAC1B;IACA,OAAOD,CAAC;EACV,CAAC,CAAC;EAKF/H,iCAAiCA,CAAA;IAC/B,IAAI,CAAC1B,yBAAyB,CAAC8J,8DAA8D,CAAC;MAC5F3H,IAAI,EAAE,IAAI,CAACV;KACZ,CAAC,CAACuE,IAAI,CACLxQ,GAAG,CAACyQ,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACrL,aAAa,GAAG,IAAI,CAACsM,4BAA4B,CAACnB,GAAG,CAACC,OAAO,CAAC;QACnE,IAAI,CAACU,eAAe,EAAE;MACxB;IACF,CAAC,CAAC,CACH,CAACvF,SAAS,EAAE;EACf;EACAS,MAAMA,CAAA;IACJ,IAAI,CAACzB,aAAa,CAACqD,IAAI,CAAC;MACtBqG,MAAM;MACNC,OAAO,EAAE,IAAI,CAACvI;KACf,CAAC;IACF,IAAI,CAACtB,QAAQ,CAAC8J,IAAI,EAAE;EACtB;;;uCArhBWvK,4CAA4C,EAAA5J,EAAA,CAAAoU,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtU,EAAA,CAAAoU,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxU,EAAA,CAAAoU,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA1U,EAAA,CAAAoU,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5U,EAAA,CAAAoU,iBAAA,CAAAO,EAAA,CAAAE,wBAAA,GAAA7U,EAAA,CAAAoU,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA/U,EAAA,CAAAoU,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAAjV,EAAA,CAAAoU,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAnV,EAAA,CAAAoU,iBAAA,CAAAO,EAAA,CAAAS,eAAA,GAAApV,EAAA,CAAAoU,iBAAA,CAAAiB,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA5C1L,4CAA4C;MAAA2L,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzV,EAAA,CAAA0V,0BAAA,EAAA1V,EAAA,CAAA2V,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9CjDjW,EAJR,CAAAQ,cAAA,aAAqE,iBACJ,wBACC,aACb,aACJ;UACvCR,EAAA,CAAAC,SAAA,aAAqD;UACrDD,EAAA,CAAAQ,cAAA,UAAK;UACHR,EAAA,CAAAC,SAAA,qBAAiC;UAErCD,EADE,CAAAmB,YAAA,EAAM,EACF;UAEJnB,EADF,CAAAQ,cAAA,aAAyC,eAC8C;UACnFR,EAAA,CAAAuB,MAAA,IACF;UAGNvB,EAHM,CAAAmB,YAAA,EAAO,EACH,EACF,EACS;UAOTnB,EALR,CAAAQ,cAAA,wBAAqC,eACZ,eAEiD,eAC3B,eACwC;;UAC7ER,EAAA,CAAAQ,cAAA,eAAyF;UACvFR,EAAA,CAAAC,SAAA,gBAEO;UAEXD,EADE,CAAAmB,YAAA,EAAM,EACF;;UAEJnB,EADF,CAAAQ,cAAA,WAAK,cACyC;UAAAR,EAAA,CAAAuB,MAAA,IAAkB;UAAAvB,EAAA,CAAAmB,YAAA,EAAK;UACnEnB,EAAA,CAAAQ,cAAA,aAAsC;UAAAR,EAAA,CAAAuB,MAAA,4FAAc;UAG1DvB,EAH0D,CAAAmB,YAAA,EAAI,EACpD,EACF,EACF;UACNnB,EAAA,CAAAoC,UAAA,KAAA+T,qEAAA,6BAA8E;UA8VlFnW,EADE,CAAAmB,YAAA,EAAM,EACO;UAKXnB,EAFJ,CAAAQ,cAAA,0BAAwF,cACvC,eACkB;;UAC7DR,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBACuE;UACzED,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,IAA0C;UAClDvB,EADkD,CAAAmB,YAAA,EAAO,EACnD;UAGJnB,EADF,CAAAQ,cAAA,cAAyC,kBAGyB;UAA9DR,EAAA,CAAAS,UAAA,mBAAA2V,+EAAA;YAAA,OAASF,GAAA,CAAAlK,MAAA,EAAQ;UAAA,EAAC;;UAClBhM,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBACO;UACTD,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,oBAAE;UACVvB,EADU,CAAAmB,YAAA,EAAO,EACR;UAETnB,EAAA,CAAAQ,cAAA,kBAEkE;UAAhER,EAAA,CAAAS,UAAA,mBAAA4V,+EAAA;YAAA,OAASH,GAAA,CAAArD,QAAA,EAAU;UAAA,EAAC;;UACpB7S,EAAA,CAAAQ,cAAA,eAA2E;UACzER,EAAA,CAAAC,SAAA,gBAAgG;UAClGD,EAAA,CAAAmB,YAAA,EAAM;;UACNnB,EAAA,CAAAQ,cAAA,YAAM;UAAAR,EAAA,CAAAuB,MAAA,gCAAI;UAMtBvB,EANsB,CAAAmB,YAAA,EAAO,EACV,EACL,EACF,EACS,EACT,EACN;UAGNnB,EAAA,CAAAoC,UAAA,KAAAkU,qEAAA,2BAA8E;;;;;UA7ZlEtW,EAAA,CAAAwB,SAAA,IACF;UADExB,EAAA,CAAA8C,kBAAA,MAAAoT,GAAA,CAAAnL,YAAA,MACF;UAkBgD/K,EAAA,CAAAwB,SAAA,IAAkB;UAAlBxB,EAAA,CAAA8E,iBAAA,CAAAoR,GAAA,CAAAnL,YAAA,CAAkB;UAK9B/K,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAgW,GAAA,CAAA5F,kBAAA,CAAuB;UAwWrDtQ,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAA8C,kBAAA,YAAAoT,GAAA,CAAA5F,kBAAA,CAAA1O,MAAA,yCAA0C;UAM3B5B,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAAqW,OAAA,GAAAL,GAAA,CAAAvS,YAAA,CAAAC,OAAA,cAAA2S,OAAA,KAAA1S,SAAA,GAAA0S,OAAA,SAA0C;UAUxCvW,EAAA,CAAAwB,SAAA,GAA0C;UAA1CxB,EAAA,CAAAE,UAAA,cAAAsW,OAAA,GAAAN,GAAA,CAAAvS,YAAA,CAAAC,OAAA,cAAA4S,OAAA,KAAA3S,SAAA,GAAA2S,OAAA,SAA0C;UAarCxW,EAAA,CAAAwB,SAAA,GAAuB;UAAvBxB,EAAA,CAAAE,UAAA,YAAAgW,GAAA,CAAA5F,kBAAA,CAAuB;;;qBD1XjD9Q,YAAY,EAAA0V,EAAA,CAAAuB,OAAA,EAAAvB,EAAA,CAAAwB,IAAA,EAAE/W,YAAY,EAAAgX,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,qBAAA,EAAAH,GAAA,CAAAI,qBAAA,EAAAJ,GAAA,CAAAK,mBAAA,EAAAL,GAAA,CAAAM,gBAAA,EAAAN,GAAA,CAAAO,iBAAA,EAAAP,GAAA,CAAAQ,iBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,YAAA,EAAEnY,gBAAgB,EAAgBK,eAAe;MAAA+X,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}