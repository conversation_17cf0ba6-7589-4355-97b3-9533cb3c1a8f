{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef, ViewChild } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr, houseCustomService, dialogService) {\n    this.cdr = cdr;\n    this.houseCustomService = houseCustomService;\n    this.dialogService = dialogService;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildCaseId = null; // 新增：建案ID\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.excludedHouseholds = []; // 新增：排除的戶別（已被其他元件選擇）\n    this.selectionChange = new EventEmitter();\n    this.houseIdChange = new EventEmitter(); // 新增：回傳 houseId 陣列\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedFloor = ''; // 新增：選中的樓層\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.floors = []; // 新增：當前棟別的樓層列表\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    this.isLoading = false; // 新增：載入狀態\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    this.initializeData();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildCaseId'] && this.buildCaseId) {\n      // 當建案ID變更時，重新載入資料\n      this.initializeData();\n    }\n    if (changes['buildingData']) {\n      // 當 buildingData 變更時，重新初始化\n      this.buildings = Object.keys(this.buildingData || {});\n      console.log('buildingData updated:', this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n    if (changes['excludedHouseholds']) {\n      // 當排除列表變化時，重新更新顯示\n      this.updateFilteredHouseholds();\n      console.log('Excluded households updated:', this.excludedHouseholds);\n    }\n  }\n  initializeData() {\n    // 優先檢查是否有傳入 buildingData\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\n      // 使用傳入的 buildingData\n      this.buildings = Object.keys(this.buildingData);\n      console.log('Component initialized with provided buildingData:', this.buildings);\n      this.updateSelectedByBuilding();\n    } else if (this.buildCaseId) {\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\n      this.loadBuildingDataFromApi();\n    } else {\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\n      this.buildings = [];\n      console.log('No buildingData or buildCaseId provided');\n    }\n  }\n  loadBuildingDataFromApi() {\n    if (!this.buildCaseId) return;\n    this.isLoading = true;\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\n      next: response => {\n        console.log('API response:', response);\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\n        this.buildings = Object.keys(this.buildingData);\n        this.updateSelectedByBuilding();\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading building data:', error);\n        // API載入失敗時，不使用備援資料，保持空狀態\n        this.buildingData = {};\n        this.buildings = [];\n        this.isLoading = false;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  convertApiResponseToBuildingData(entries) {\n    const buildingData = {};\n    Object.entries(entries).forEach(([building, houses]) => {\n      buildingData[building] = houses.map(house => ({\n        houseName: house.houseName || house.HouseName || house.code,\n        building: house.building || house.Building || building,\n        floor: house.floor || house.Floor,\n        houseId: house.houseId || house.HouseId,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(houseName => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(houseName);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.selectedFloor = ''; // 重置樓層選擇\n    this.searchTerm = '';\n    this.updateFloorsForBuilding(); // 更新樓層列表\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋和樓層過濾\n    this.filteredHouseholds = households.filter(h => {\n      // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\n      const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\n      // 搜尋篩選：戶別代碼包含搜尋詞\n      const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      return floorMatch && searchMatch;\n    }).map(h => h.houseName);\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n    console.log('Search term changed:', this.searchTerm);\n  }\n  resetSearch() {\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Search reset');\n  }\n  onHouseholdToggle(householdCode) {\n    // 防止選擇已排除的戶別\n    if (this.isHouseholdExcluded(householdCode)) {\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\n      return;\n    }\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        console.log('已達到最大選擇數量');\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的過濾戶別\n    const unselectedFiltered = this.filteredHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    // 取得當前棟別的所有戶別\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    // 計算可以新增的戶別數量\n    const currentCount = this.selectedHouseholds.length;\n    const maxAllowed = this.maxSelections || Infinity;\n    const remainingSlots = maxAllowed - currentCount; // 取得尚未選擇且未被排除的棟別戶別\n    const unselectedBuilding = buildingHouseholds.filter(code => !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code));\n    // 根據剩餘空間決定要新增的戶別\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\n    if (toAdd.length > 0) {\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\n      this.emitChanges();\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\n    }\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(houseName => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n    // 新增：回傳 houseId 陣列\n    const houseIds = selectedItems.map(item => item.houseId).filter(id => id !== undefined);\n    this.houseIdChange.emit(houseIds);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.openDialog();\n      console.log('Opening household selection dialog');\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  openDialog() {\n    this.dialogService.open(this.householdDialog, {\n      context: {},\n      closeOnBackdropClick: false,\n      closeOnEsc: true,\n      autoFocus: false\n    });\n  }\n  closeDropdown() {\n    // 這個方法現在用於關閉對話框\n    // 對話框的關閉將由 NbDialogRef 處理\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  isHouseholdExcluded(householdCode) {\n    return this.excludedHouseholds.includes(householdCode);\n  }\n  isHouseholdDisabled(householdCode) {\n    return this.isHouseholdExcluded(householdCode) || !this.canSelectMore() && !this.isHouseholdSelected(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.houseName);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(houseName => this.selectedHouseholds.includes(houseName));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\n    return buildingHouseholds.some(houseName => this.selectedHouseholds.includes(houseName));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n  // 新增：更新當前棟別的樓層列表\n  updateFloorsForBuilding() {\n    if (!this.selectedBuilding) {\n      this.floors = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const floorSet = new Set();\n    households.forEach(household => {\n      if (household.floor) {\n        floorSet.add(household.floor);\n      }\n    });\n    // 對樓層進行自然排序\n    this.floors = Array.from(floorSet).sort((a, b) => {\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\n      return numA - numB;\n    });\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\n  }\n  // 新增：樓層選擇處理\n  onFloorSelect(floor) {\n    console.log('Floor selected:', floor);\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\n    this.updateFilteredHouseholds();\n    this.cdr.detectChanges();\n  }\n  // 新增：取得當前棟別的樓層計數\n  getFloorCount(floor) {\n    if (!this.selectedBuilding) return 0;\n    const households = this.buildingData[this.selectedBuilding] || [];\n    return households.filter(h => h.floor === floor).length;\n  }\n  // 新增：取得戶別的樓層資訊\n  getHouseholdFloor(householdCode) {\n    if (!this.selectedBuilding) return '';\n    const households = this.buildingData[this.selectedBuilding] || [];\n    const household = households.find(h => h.houseName === householdCode);\n    return household?.floor || '';\n  }\n  // 新增：取得戶別的完整資訊（包含樓層）\n  getHouseholdInfo(householdCode) {\n    for (const building of this.buildings) {\n      const households = this.buildingData[building] || [];\n      const household = households.find(h => h.houseName === householdCode);\n      if (household) {\n        return {\n          houseName: household.houseName,\n          floor: household.floor || ''\n        };\n      }\n    }\n    return {\n      houseName: householdCode,\n      floor: ''\n    };\n  }\n};\n__decorate([ViewChild('householdDialog', {\n  static: false\n})], HouseholdBindingComponent.prototype, \"householdDialog\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildCaseId\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"excludedHouseholds\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"houseIdChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "ViewChild", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "houseCustomService", "dialogService", "placeholder", "maxSelections", "disabled", "buildCaseId", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "excludedHouseholds", "selectionChange", "houseIdChange", "isOpen", "selectedBuilding", "searchTerm", "selectedF<PERSON>or", "selectedHouseholds", "buildings", "floors", "filteredHouseholds", "selectedByBuilding", "isLoading", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "initializeData", "ngOnChanges", "changes", "Object", "keys", "console", "log", "updateFilteredHouseholds", "length", "loadBuildingDataFromApi", "getDropDown", "subscribe", "next", "response", "convertApiResponseToBuildingData", "Entries", "detectChanges", "error", "entries", "for<PERSON>ach", "building", "houses", "map", "house", "houseName", "HouseName", "code", "Building", "floor", "Floor", "houseId", "HouseId", "isSelected", "grouped", "item", "find", "h", "push", "onBuildingSelect", "updateFloorsForBuilding", "onBuildingClick", "households", "filter", "floorMatch", "searchMatch", "toLowerCase", "includes", "onSearchChange", "event", "target", "resetSearch", "onHouseholdToggle", "householdCode", "isHouseholdExcluded", "newSelection", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "currentCount", "maxAllowed", "Infinity", "remainingSlots", "unselectedFiltered", "toAdd", "slice", "onSelectAllBuilding", "buildingHouseholds", "unselectedBuilding", "onUnselectAllBuilding", "onClearAll", "selectedItems", "emit", "houseIds", "id", "undefined", "toggleDropdown", "openDialog", "open", "householdDialog", "context", "closeOnBackdropClick", "closeOnEsc", "autoFocus", "closeDropdown", "isHouseholdSelected", "isHouseholdDisabled", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseholds", "hasBuildingSelected", "floorSet", "Set", "household", "add", "Array", "from", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "onFloorSelect", "getFloorCount", "getHouseholdFloor", "getHouseholdInfo", "__decorate", "static", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef, TemplateRef, ViewChild } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { HouseCustomService } from '../../../../services/api/services/HouseCustom.service';\r\nimport { HouseItem } from '../../../../services/api/models/house.model';\r\n\r\nexport interface HouseholdItem {\r\n  houseName: string;\r\n  building: string;\r\n  floor?: string;\r\n  houseId?: number;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @ViewChild('householdDialog', { static: false }) householdDialog!: TemplateRef<any>;\r\n\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildCaseId: number | null = null; // 新增：建案ID\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n  @Input() excludedHouseholds: string[] = []; // 新增：排除的戶別（已被其他元件選擇）\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n  @Output() houseIdChange = new EventEmitter<number[]>(); // 新增：回傳 houseId 陣列\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedFloor = ''; // 新增：選中的樓層\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  floors: string[] = []; // 新增：當前棟別的樓層列表\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  isLoading: boolean = false; // 新增：載入狀態\r\n\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private houseCustomService: HouseCustomService,\r\n    private dialogService: NbDialogService\r\n  ) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    this.initializeData();\r\n  }\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildCaseId'] && this.buildCaseId) {\r\n      // 當建案ID變更時，重新載入資料\r\n      this.initializeData();\r\n    }\r\n    if (changes['buildingData']) {\r\n      // 當 buildingData 變更時，重新初始化\r\n      this.buildings = Object.keys(this.buildingData || {});\r\n      console.log('buildingData updated:', this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n    if (changes['excludedHouseholds']) {\r\n      // 當排除列表變化時，重新更新顯示\r\n      this.updateFilteredHouseholds();\r\n      console.log('Excluded households updated:', this.excludedHouseholds);\r\n    }\r\n  } private initializeData() {\r\n    // 優先檢查是否有傳入 buildingData\r\n    if (this.buildingData && Object.keys(this.buildingData).length > 0) {\r\n      // 使用傳入的 buildingData\r\n      this.buildings = Object.keys(this.buildingData);\r\n      console.log('Component initialized with provided buildingData:', this.buildings);\r\n      this.updateSelectedByBuilding();\r\n    } else if (this.buildCaseId) {\r\n      // 只有在沒有傳入 buildingData 且有建案ID時才呼叫API\r\n      this.loadBuildingDataFromApi();\r\n    } else {\r\n      // 既沒有 buildingData 也沒有 buildCaseId，保持空狀態\r\n      this.buildings = [];\r\n      console.log('No buildingData or buildCaseId provided');\r\n    }\r\n  }\r\n\r\n  private loadBuildingDataFromApi() {\r\n    if (!this.buildCaseId) return;\r\n\r\n    this.isLoading = true;\r\n    this.houseCustomService.getDropDown(this.buildCaseId).subscribe({\r\n      next: (response) => {\r\n        console.log('API response:', response);\r\n        this.buildingData = this.convertApiResponseToBuildingData(response.Entries);\r\n        this.buildings = Object.keys(this.buildingData);\r\n        this.updateSelectedByBuilding();\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }, error: (error) => {\r\n        console.error('Error loading building data:', error);\r\n        // API載入失敗時，不使用備援資料，保持空狀態\r\n        this.buildingData = {};\r\n        this.buildings = [];\r\n        this.isLoading = false;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n  private convertApiResponseToBuildingData(entries: { [key: string]: any[] }): BuildingData {\r\n    const buildingData: BuildingData = {};\r\n\r\n    Object.entries(entries).forEach(([building, houses]) => {\r\n      buildingData[building] = houses.map(house => ({\r\n        houseName: house.houseName || house.HouseName || house.code,\r\n        building: house.building || house.Building || building,\r\n        floor: house.floor || house.Floor,\r\n        houseId: house.houseId || house.HouseId,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(houseName => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(houseName);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.selectedFloor = ''; // 重置樓層選擇\r\n    this.searchTerm = '';\r\n    this.updateFloorsForBuilding(); // 更新樓層列表\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding, 'floor:', this.selectedFloor);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋和樓層過濾\r\n    this.filteredHouseholds = households\r\n      .filter(h => {\r\n        // 樓層篩選：如果有選擇樓層，只顯示該樓層的戶別\r\n        const floorMatch = !this.selectedFloor || h.floor === this.selectedFloor;\r\n        // 搜尋篩選：戶別代碼包含搜尋詞\r\n        const searchMatch = h.houseName.toLowerCase().includes(this.searchTerm.toLowerCase());\r\n        return floorMatch && searchMatch;\r\n      })\r\n      .map(h => h.houseName);\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search term changed:', this.searchTerm);\r\n  }\r\n\r\n  resetSearch() {\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Search reset');\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    // 防止選擇已排除的戶別\r\n    if (this.isHouseholdExcluded(householdCode)) {\r\n      console.log(`戶別 ${householdCode} 已被其他元件選擇，無法重複選擇`);\r\n      return;\r\n    }\r\n\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        console.log('已達到最大選擇數量');\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  } onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的過濾戶別\r\n    const unselectedFiltered = this.filteredHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedFiltered.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選當前: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  }\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    // 取得當前棟別的所有戶別\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n\r\n    // 計算可以新增的戶別數量\r\n    const currentCount = this.selectedHouseholds.length;\r\n    const maxAllowed = this.maxSelections || Infinity;\r\n    const remainingSlots = maxAllowed - currentCount;    // 取得尚未選擇且未被排除的棟別戶別\r\n    const unselectedBuilding = buildingHouseholds.filter(code =>\r\n      !this.selectedHouseholds.includes(code) && !this.isHouseholdExcluded(code)\r\n    );\r\n\r\n    // 根據剩餘空間決定要新增的戶別\r\n    const toAdd = unselectedBuilding.slice(0, remainingSlots);\r\n\r\n    if (toAdd.length > 0) {\r\n      this.selectedHouseholds = [...this.selectedHouseholds, ...toAdd];\r\n      this.emitChanges();\r\n      console.log(`全選棟別: 新增了 ${toAdd.length} 個戶別`);\r\n    }\r\n  } onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(houseName => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.houseName === houseName);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n\r\n    // 新增：回傳 houseId 陣列\r\n    const houseIds = selectedItems.map(item => item.houseId!).filter(id => id !== undefined);\r\n    this.houseIdChange.emit(houseIds);\r\n  } toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.openDialog();\r\n      console.log('Opening household selection dialog');\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  openDialog() {\r\n    this.dialogService.open(this.householdDialog, {\r\n      context: {},\r\n      closeOnBackdropClick: false,\r\n      closeOnEsc: true,\r\n      autoFocus: false,\r\n    });\r\n  }\r\n\r\n  closeDropdown() {\r\n    // 這個方法現在用於關閉對話框\r\n    // 對話框的關閉將由 NbDialogRef 處理\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdExcluded(householdCode: string): boolean {\r\n    return this.excludedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  isHouseholdDisabled(householdCode: string): boolean {\r\n    return this.isHouseholdExcluded(householdCode) ||\r\n      (!this.canSelectMore() && !this.isHouseholdSelected(householdCode));\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.houseName);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(houseName => this.selectedHouseholds.includes(houseName));\r\n  } isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.houseName) || [];\r\n    return buildingHouseholds.some(houseName => this.selectedHouseholds.includes(houseName));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n\r\n  // 新增：更新當前棟別的樓層列表\r\n  private updateFloorsForBuilding() {\r\n    if (!this.selectedBuilding) {\r\n      this.floors = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const floorSet = new Set<string>();\r\n\r\n    households.forEach(household => {\r\n      if (household.floor) {\r\n        floorSet.add(household.floor);\r\n      }\r\n    });\r\n\r\n    // 對樓層進行自然排序\r\n    this.floors = Array.from(floorSet).sort((a, b) => {\r\n      const numA = parseInt(a.replace(/[^0-9]/g, ''));\r\n      const numB = parseInt(b.replace(/[^0-9]/g, ''));\r\n      return numA - numB;\r\n    });\r\n\r\n    console.log('Updated floors for building:', this.selectedBuilding, this.floors);\r\n  }\r\n\r\n  // 新增：樓層選擇處理\r\n  onFloorSelect(floor: string) {\r\n    console.log('Floor selected:', floor);\r\n    this.selectedFloor = this.selectedFloor === floor ? '' : floor; // 切換選擇\r\n    this.updateFilteredHouseholds();\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // 新增：取得當前棟別的樓層計數\r\n  getFloorCount(floor: string): number {\r\n    if (!this.selectedBuilding) return 0;\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    return households.filter(h => h.floor === floor).length;\r\n  }\r\n  // 新增：取得戶別的樓層資訊\r\n  getHouseholdFloor(householdCode: string): string {\r\n    if (!this.selectedBuilding) return '';\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    const household = households.find(h => h.houseName === householdCode);\r\n    return household?.floor || '';\r\n  }\r\n  // 新增：取得戶別的完整資訊（包含樓層）\r\n  getHouseholdInfo(householdCode: string): { houseName: string, floor: string } {\r\n    for (const building of this.buildings) {\r\n      const households = this.buildingData[building] || [];\r\n      const household = households.find(h => h.houseName === householdCode);\r\n      if (household) {\r\n        return {\r\n          houseName: household.houseName,\r\n          floor: household.floor || ''\r\n        };\r\n      }\r\n    }\r\n    return { houseName: householdCode, floor: '' };\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,EAAkCC,SAAS,QAAQ,eAAe;AAC/J,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA8BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA6BpCC,YACUC,GAAsB,EACtBC,kBAAsC,EACtCC,aAA8B;IAF9B,KAAAF,GAAG,GAAHA,GAAG;IACH,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IA7Bd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,WAAW,GAAkB,IAAI,CAAC,CAAC;IACnC,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAC;IAElC,KAAAC,eAAe,GAAG,IAAIlB,YAAY,EAAmB;IACrD,KAAAmB,aAAa,GAAG,IAAInB,YAAY,EAAY,CAAC,CAAC;IACxD,KAAAoB,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,aAAa,GAAG,EAAE,CAAC,CAAC;IACpB,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAC;IACvB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE;IACzD,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAE5B;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAKzB;EAEJC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACP,kBAAkB,GAAGO,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAAC5B,QAAQ,GAAG4B,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR,IAAI,CAACC,cAAc,EAAE;EACvB;EACAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC/B,WAAW,EAAE;MAC9C;MACA,IAAI,CAAC6B,cAAc,EAAE;IACvB;IACA,IAAIE,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B;MACA,IAAI,CAAClB,SAAS,GAAGmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChC,YAAY,IAAI,EAAE,CAAC;MACrDiC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAClC,YAAY,CAAC;MACvD,IAAI,CAACmC,wBAAwB,EAAE;MAC/B,IAAI,CAACd,wBAAwB,EAAE;IACjC;IACA,IAAIS,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjC;MACA,IAAI,CAACK,wBAAwB,EAAE;MAC/BF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC9B,kBAAkB,CAAC;IACtE;EACF;EAAUwB,cAAcA,CAAA;IACtB;IACA,IAAI,IAAI,CAAC5B,YAAY,IAAI+B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC,CAACoC,MAAM,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAACxB,SAAS,GAAGmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC;MAC/CiC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE,IAAI,CAACtB,SAAS,CAAC;MAChF,IAAI,CAACS,wBAAwB,EAAE;IACjC,CAAC,MAAM,IAAI,IAAI,CAACtB,WAAW,EAAE;MAC3B;MACA,IAAI,CAACsC,uBAAuB,EAAE;IAChC,CAAC,MAAM;MACL;MACA,IAAI,CAACzB,SAAS,GAAG,EAAE;MACnBqB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACxD;EACF;EAEQG,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAACtC,WAAW,EAAE;IAEvB,IAAI,CAACiB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACtB,kBAAkB,CAAC4C,WAAW,CAAC,IAAI,CAACvC,WAAW,CAAC,CAACwC,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAQ,IAAI;QACjBR,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAAC;QACtC,IAAI,CAACzC,YAAY,GAAG,IAAI,CAAC0C,gCAAgC,CAACD,QAAQ,CAACE,OAAO,CAAC;QAC3E,IAAI,CAAC/B,SAAS,GAAGmB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChC,YAAY,CAAC;QAC/C,IAAI,CAACqB,wBAAwB,EAAE;QAC/B,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACvB,GAAG,CAACmD,aAAa,EAAE;MAC1B,CAAC;MAAEC,KAAK,EAAGA,KAAK,IAAI;QAClBZ,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD;QACA,IAAI,CAAC7C,YAAY,GAAG,EAAE;QACtB,IAAI,CAACY,SAAS,GAAG,EAAE;QACnB,IAAI,CAACI,SAAS,GAAG,KAAK;QACtB,IAAI,CAACvB,GAAG,CAACmD,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EACQF,gCAAgCA,CAACI,OAAiC;IACxE,MAAM9C,YAAY,GAAiB,EAAE;IAErC+B,MAAM,CAACe,OAAO,CAACA,OAAO,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,MAAM,CAAC,KAAI;MACrDjD,YAAY,CAACgD,QAAQ,CAAC,GAAGC,MAAM,CAACC,GAAG,CAACC,KAAK,KAAK;QAC5CC,SAAS,EAAED,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACG,IAAI;QAC3DN,QAAQ,EAAEG,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACI,QAAQ,IAAIP,QAAQ;QACtDQ,KAAK,EAAEL,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACM,KAAK;QACjCC,OAAO,EAAEP,KAAK,CAACO,OAAO,IAAIP,KAAK,CAACQ,OAAO;QACvCC,UAAU,EAAE,KAAK;QACjBlC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO1B,YAAY;EACrB;EAAUqB,wBAAwBA,CAAA;IAChC,MAAMwC,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAAClD,kBAAkB,CAACoC,OAAO,CAACK,SAAS,IAAG;MAC1C,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpC,SAAS,EAAE;QACrC,MAAMkD,IAAI,GAAG,IAAI,CAAC9D,YAAY,CAACgD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;QAC9E,IAAIU,IAAI,EAAE;UACR,IAAI,CAACD,OAAO,CAACb,QAAQ,CAAC,EAAEa,OAAO,CAACb,QAAQ,CAAC,GAAG,EAAE;UAC9Ca,OAAO,CAACb,QAAQ,CAAC,CAACiB,IAAI,CAACb,SAAS,CAAC;UACjC;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACrC,kBAAkB,GAAG8C,OAAO;EACnC;EAAEK,gBAAgBA,CAAClB,QAAgB;IACjCf,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEc,QAAQ,CAAC;IAC3C,IAAI,CAACxC,gBAAgB,GAAGwC,QAAQ;IAChC,IAAI,CAACtC,aAAa,GAAG,EAAE,CAAC,CAAC;IACzB,IAAI,CAACD,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC0D,uBAAuB,EAAE,CAAC,CAAC;IAChC,IAAI,CAAChC,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACpB,kBAAkB,CAACsB,MAAM,CAAC;IACzE;IACA,IAAI,CAAC3C,GAAG,CAACmD,aAAa,EAAE;EAC1B;EAEAwB,eAAeA,CAACpB,QAAgB;IAC9Bf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEc,QAAQ,CAAC;EACxD;EAAEb,wBAAwBA,CAAA;IACxBF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC1B,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAACE,aAAa,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;MAC1B,IAAI,CAACM,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMuD,UAAU,GAAG,IAAI,CAACrE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjEyB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmC,UAAU,CAACjC,MAAM,CAAC;IAEpE;IACA,IAAI,CAACtB,kBAAkB,GAAGuD,UAAU,CACjCC,MAAM,CAACN,CAAC,IAAG;MACV;MACA,MAAMO,UAAU,GAAG,CAAC,IAAI,CAAC7D,aAAa,IAAIsD,CAAC,CAACR,KAAK,KAAK,IAAI,CAAC9C,aAAa;MACxE;MACA,MAAM8D,WAAW,GAAGR,CAAC,CAACZ,SAAS,CAACqB,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACjE,UAAU,CAACgE,WAAW,EAAE,CAAC;MACrF,OAAOF,UAAU,IAAIC,WAAW;IAClC,CAAC,CAAC,CACDtB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;IAExBnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACpB,kBAAkB,CAACsB,MAAM,CAAC;EAC5E;EAEAuC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACnE,UAAU,GAAGmE,KAAK,CAACC,MAAM,CAAC3D,KAAK;IACpC,IAAI,CAACiB,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACzB,UAAU,CAAC;EACtD;EAEAqE,WAAWA,CAAA;IACT,IAAI,CAACrE,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC0B,wBAAwB,EAAE;IAC/BF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7B;EACA6C,iBAAiBA,CAACC,aAAqB;IACrC;IACA,IAAI,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC,EAAE;MAC3C/C,OAAO,CAACC,GAAG,CAAC,MAAM8C,aAAa,kBAAkB,CAAC;MAClD;IACF;IAEA,MAAMpB,UAAU,GAAG,IAAI,CAACjD,kBAAkB,CAAC+D,QAAQ,CAACM,aAAa,CAAC;IAClE,IAAIE,YAAsB;IAE1B,IAAItB,UAAU,EAAE;MACdsB,YAAY,GAAG,IAAI,CAACvE,kBAAkB,CAAC2D,MAAM,CAACN,CAAC,IAAIA,CAAC,KAAKgB,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACnF,aAAa,IAAI,IAAI,CAACc,kBAAkB,CAACyB,MAAM,IAAI,IAAI,CAACvC,aAAa,EAAE;QAC9EoC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,CAAC;MACV;MACAgD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACvE,kBAAkB,EAAEqE,aAAa,CAAC;IAC5D;IAEA,IAAI,CAACrE,kBAAkB,GAAGuE,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,iBAAiBA,CAACJ,aAAqB;IACrC,IAAI,CAACrE,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC2D,MAAM,CAACN,CAAC,IAAIA,CAAC,KAAKgB,aAAa,CAAC;IAClF,IAAI,CAACG,WAAW,EAAE;EACpB;EAAEE,mBAAmBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,IAAI,IAAI,CAACM,kBAAkB,CAACsB,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAMkD,YAAY,GAAG,IAAI,CAAC3E,kBAAkB,CAACyB,MAAM;IACnD,MAAMmD,UAAU,GAAG,IAAI,CAAC1F,aAAa,IAAI2F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMI,kBAAkB,GAAG,IAAI,CAAC5E,kBAAkB,CAACwD,MAAM,CAAChB,IAAI,IAC5D,CAAC,IAAI,CAAC3C,kBAAkB,CAAC+D,QAAQ,CAACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC2B,mBAAmB,CAAC3B,IAAI,CAAC,CAC3E;IAED;IACA,MAAMqC,KAAK,GAAGD,kBAAkB,CAACE,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAACvD,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACzB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAGgF,KAAK,CAAC;MAChE,IAAI,CAACR,WAAW,EAAE;MAClBlD,OAAO,CAACC,GAAG,CAAC,aAAayD,KAAK,CAACvD,MAAM,MAAM,CAAC;IAC9C;EACF;EACAyD,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACrF,gBAAgB,EAAE;IAE5B;IACA,MAAMsF,kBAAkB,GAAG,IAAI,CAAC9F,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE0C,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC,IAAI,EAAE;IAEhG;IACA,MAAMkC,YAAY,GAAG,IAAI,CAAC3E,kBAAkB,CAACyB,MAAM;IACnD,MAAMmD,UAAU,GAAG,IAAI,CAAC1F,aAAa,IAAI2F,QAAQ;IACjD,MAAMC,cAAc,GAAGF,UAAU,GAAGD,YAAY,CAAC,CAAI;IACrD,MAAMS,kBAAkB,GAAGD,kBAAkB,CAACxB,MAAM,CAAChB,IAAI,IACvD,CAAC,IAAI,CAAC3C,kBAAkB,CAAC+D,QAAQ,CAACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC2B,mBAAmB,CAAC3B,IAAI,CAAC,CAC3E;IAED;IACA,MAAMqC,KAAK,GAAGI,kBAAkB,CAACH,KAAK,CAAC,CAAC,EAAEH,cAAc,CAAC;IAEzD,IAAIE,KAAK,CAACvD,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI,CAACzB,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACA,kBAAkB,EAAE,GAAGgF,KAAK,CAAC;MAChE,IAAI,CAACR,WAAW,EAAE;MAClBlD,OAAO,CAACC,GAAG,CAAC,aAAayD,KAAK,CAACvD,MAAM,MAAM,CAAC;IAC9C;EACF;EAAE4D,qBAAqBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACxF,gBAAgB,EAAE;IAE5B,MAAMsF,kBAAkB,GAAG,IAAI,CAAC9F,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE0C,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC,IAAI,EAAE;IAChG,IAAI,CAACzC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC2D,MAAM,CAACN,CAAC,IAAI,CAAC8B,kBAAkB,CAACpB,QAAQ,CAACV,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACmB,WAAW,EAAE;EACpB;EACAc,UAAUA,CAAA;IACR,IAAI,CAACtF,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACwE,WAAW,EAAE;EACpB;EACQA,WAAWA,CAAA;IACjB,IAAI,CAAC9D,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACQ,SAAS,EAAE;IAEhB,MAAM+E,aAAa,GAAG,IAAI,CAACvF,kBAAkB,CAACuC,GAAG,CAACE,SAAS,IAAG;MAC5D,KAAK,MAAMJ,QAAQ,IAAI,IAAI,CAACpC,SAAS,EAAE;QACrC,MAAMkD,IAAI,GAAG,IAAI,CAAC9D,YAAY,CAACgD,QAAQ,CAAC,EAAEe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAKA,SAAS,CAAC;QAC9E,IAAIU,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACQ,MAAM,CAACR,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAACzD,eAAe,CAAC8F,IAAI,CAACD,aAAa,CAAC;IAExC;IACA,MAAME,QAAQ,GAAGF,aAAa,CAAChD,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACJ,OAAQ,CAAC,CAACY,MAAM,CAAC+B,EAAE,IAAIA,EAAE,KAAKC,SAAS,CAAC;IACxF,IAAI,CAAChG,aAAa,CAAC6F,IAAI,CAACC,QAAQ,CAAC;EACnC;EAAEG,cAAcA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzG,QAAQ,EAAE;MAClB,IAAI,CAAC0G,UAAU,EAAE;MACjBvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtB,SAAS,CAAC;IACrD;EACF;EAEA4F,UAAUA,CAAA;IACR,IAAI,CAAC7G,aAAa,CAAC8G,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MAC5CC,OAAO,EAAE,EAAE;MACXC,oBAAoB,EAAE,KAAK;MAC3BC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX;IACA;EAAA;EAGFC,mBAAmBA,CAAChC,aAAqB;IACvC,OAAO,IAAI,CAACrE,kBAAkB,CAAC+D,QAAQ,CAACM,aAAa,CAAC;EACxD;EAEAC,mBAAmBA,CAACD,aAAqB;IACvC,OAAO,IAAI,CAAC5E,kBAAkB,CAACsE,QAAQ,CAACM,aAAa,CAAC;EACxD;EAEAiC,mBAAmBA,CAACjC,aAAqB;IACvC,OAAO,IAAI,CAACC,mBAAmB,CAACD,aAAa,CAAC,IAC3C,CAAC,IAAI,CAACkC,aAAa,EAAE,IAAI,CAAC,IAAI,CAACF,mBAAmB,CAAChC,aAAa,CAAE;EACvE;EAEAkC,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACrH,aAAa,IAAI,IAAI,CAACc,kBAAkB,CAACyB,MAAM,GAAG,IAAI,CAACvC,aAAa;EACnF;EACAsH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC3G,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMsF,kBAAkB,GAAG,IAAI,CAAC9F,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,CAChE8D,MAAM,CAACN,CAAC,IAAI,CAACA,CAAC,CAACtC,UAAU,CAAC,CAC1BwB,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC;IACxB,OAAO0C,kBAAkB,CAAC1D,MAAM,GAAG,CAAC,IAClC0D,kBAAkB,CAACsB,KAAK,CAAChE,SAAS,IAAI,IAAI,CAACzC,kBAAkB,CAAC+D,QAAQ,CAACtB,SAAS,CAAC,CAAC;EACtF;EAAEiE,sBAAsBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC7G,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMsF,kBAAkB,GAAG,IAAI,CAAC9F,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,EAAE0C,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACZ,SAAS,CAAC,IAAI,EAAE;IAChG,OAAO0C,kBAAkB,CAACwB,IAAI,CAAClE,SAAS,IAAI,IAAI,CAACzC,kBAAkB,CAAC+D,QAAQ,CAACtB,SAAS,CAAC,CAAC;EAC1F;EACAmE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACxG,kBAAkB;EAChC;EAEAyG,gBAAgBA,CAACxE,QAAgB;IAC/B,OAAO,IAAI,CAAChD,YAAY,CAACgD,QAAQ,CAAC,EAAEZ,MAAM,IAAI,CAAC;EACjD;EAEAqF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC9G,kBAAkB,CAACyB,MAAM;EACvC;EAEA;EACAsF,6BAA6BA,CAAC1E,QAAgB;IAC5C,OAAO,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACA2E,mBAAmBA,CAAC3E,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,IAAI,IAAI,CAACjC,kBAAkB,CAACiC,QAAQ,CAAC,CAACZ,MAAM,GAAG,CAAC,CAAC;EAC9F;EAEA;EACQ+B,uBAAuBA,CAAA;IAC7B,IAAI,CAAC,IAAI,CAAC3D,gBAAgB,EAAE;MAC1B,IAAI,CAACK,MAAM,GAAG,EAAE;MAChB;IACF;IAEA,MAAMwD,UAAU,GAAG,IAAI,CAACrE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMoH,QAAQ,GAAG,IAAIC,GAAG,EAAU;IAElCxD,UAAU,CAACtB,OAAO,CAAC+E,SAAS,IAAG;MAC7B,IAAIA,SAAS,CAACtE,KAAK,EAAE;QACnBoE,QAAQ,CAACG,GAAG,CAACD,SAAS,CAACtE,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3C,MAAM,GAAGmH,KAAK,CAACC,IAAI,CAACL,QAAQ,CAAC,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC/C,MAAMC,IAAI,GAAGC,QAAQ,CAACH,CAAC,CAACI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,MAAMC,IAAI,GAAGF,QAAQ,CAACF,CAAC,CAACG,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;MAC/C,OAAOF,IAAI,GAAGG,IAAI;IACpB,CAAC,CAAC;IAEFvG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC1B,gBAAgB,EAAE,IAAI,CAACK,MAAM,CAAC;EACjF;EAEA;EACA4H,aAAaA,CAACjF,KAAa;IACzBvB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEsB,KAAK,CAAC;IACrC,IAAI,CAAC9C,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK8C,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC,CAAC;IAChE,IAAI,CAACrB,wBAAwB,EAAE;IAC/B,IAAI,CAAC1C,GAAG,CAACmD,aAAa,EAAE;EAC1B;EAEA;EACA8F,aAAaA,CAAClF,KAAa;IACzB,IAAI,CAAC,IAAI,CAAChD,gBAAgB,EAAE,OAAO,CAAC;IACpC,MAAM6D,UAAU,GAAG,IAAI,CAACrE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,OAAO6D,UAAU,CAACC,MAAM,CAACN,CAAC,IAAIA,CAAC,CAACR,KAAK,KAAKA,KAAK,CAAC,CAACpB,MAAM;EACzD;EACA;EACAuG,iBAAiBA,CAAC3D,aAAqB;IACrC,IAAI,CAAC,IAAI,CAACxE,gBAAgB,EAAE,OAAO,EAAE;IACrC,MAAM6D,UAAU,GAAG,IAAI,CAACrE,YAAY,CAAC,IAAI,CAACQ,gBAAgB,CAAC,IAAI,EAAE;IACjE,MAAMsH,SAAS,GAAGzD,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK4B,aAAa,CAAC;IACrE,OAAO8C,SAAS,EAAEtE,KAAK,IAAI,EAAE;EAC/B;EACA;EACAoF,gBAAgBA,CAAC5D,aAAqB;IACpC,KAAK,MAAMhC,QAAQ,IAAI,IAAI,CAACpC,SAAS,EAAE;MACrC,MAAMyD,UAAU,GAAG,IAAI,CAACrE,YAAY,CAACgD,QAAQ,CAAC,IAAI,EAAE;MACpD,MAAM8E,SAAS,GAAGzD,UAAU,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,SAAS,KAAK4B,aAAa,CAAC;MACrE,IAAI8C,SAAS,EAAE;QACb,OAAO;UACL1E,SAAS,EAAE0E,SAAS,CAAC1E,SAAS;UAC9BI,KAAK,EAAEsE,SAAS,CAACtE,KAAK,IAAI;SAC3B;MACH;IACF;IACA,OAAO;MAAEJ,SAAS,EAAE4B,aAAa;MAAExB,KAAK,EAAE;IAAE,CAAE;EAChD;CACD;AAjakDqF,UAAA,EAAhDxJ,SAAS,CAAC,iBAAiB,EAAE;EAAEyJ,MAAM,EAAE;AAAK,CAAE,CAAC,C,iEAAoC;AAE3ED,UAAA,EAAR5J,KAAK,EAAE,C,6DAA+B;AAC9B4J,UAAA,EAAR5J,KAAK,EAAE,C,+DAAqC;AACpC4J,UAAA,EAAR5J,KAAK,EAAE,C,0DAA2B;AAC1B4J,UAAA,EAAR5J,KAAK,EAAE,C,6DAAmC;AAClC4J,UAAA,EAAR5J,KAAK,EAAE,C,8DAAiC;AAChC4J,UAAA,EAAR5J,KAAK,EAAE,C,kEAAkC;AACjC4J,UAAA,EAAR5J,KAAK,EAAE,C,6DAA6B;AAC5B4J,UAAA,EAAR5J,KAAK,EAAE,C,kEAAkC;AACjC4J,UAAA,EAAR5J,KAAK,EAAE,C,oEAAmC;AAEjC4J,UAAA,EAAT3J,MAAM,EAAE,C,iEAAuD;AACtD2J,UAAA,EAAT3J,MAAM,EAAE,C,+DAA8C;AAd5CK,yBAAyB,GAAAsJ,UAAA,EAZrC7J,SAAS,CAAC;EACT+J,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAE7J,iBAAiB;IAC1B8J,WAAW,EAAEhK,UAAU,CAAC,MAAMG,yBAAyB,CAAC;IACxD8J,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACW9J,yBAAyB,CAkarC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}