{"ast": null, "code": "import { decodeJwtPayload } from '@nebular/auth';\nimport { FormsModule } from '@angular/forms';\nimport { NbLayoutModule, NbCardModule, NbInputModule, NbButtonModule } from '@nebular/theme';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/app/shared/helper/allowHelper\";\nimport * as i6 from \"src/app/shared/helper/petternHelper\";\nimport * as i7 from \"@nebular/theme\";\nimport * as i8 from \"@angular/forms\";\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(router, userService, message, valid, allow, pettern) {\n      this.router = router;\n      this.userService = userService;\n      this.message = message;\n      this.valid = valid;\n      this.allow = allow;\n      this.pettern = pettern;\n      this.account = \"\";\n      this.password = \"\";\n    }\n    ngOnInit() {}\n    login() {\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this.userService.apiUserUserLoginPost$Json({\n        body: {\n          Account: this.account,\n          Password: this.password\n        }\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          const jwt = decodeJwtPayload(res.Entries);\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.TOKEN, res.Entries);\n          LocalStorageService.AddLocalStorage(STORAGE_KEY.BUID, jwt.BuId);\n          this.message.showSucessMSG('登入成功');\n          this.router.navigateByUrl('home');\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n      });\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[帳號]', this.account);\n      this.valid.pattern('[帳號]', this.account, this.pettern.AccountPettern);\n      this.valid.required('[密碼]', this.password);\n      this.valid.pattern('[密碼]', this.password, this.pettern.PasswordPettern);\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.AllowHelper), i0.ɵɵdirectiveInject(i6.PetternHelper));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"ngx-login\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 17,\n        vars: 2,\n        consts: [[\"center\", \"\"], [1, \"form-group\"], [\"for\", \"exampleInputEmail1\", 1, \"label\"], [\"type\", \"text\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"exampleInputEmail1\", \"placeholder\", \"\\u5E33\\u865F\", \"name\", \"account\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"exampleInputPassword1\", 1, \"label\"], [\"type\", \"password\", \"nbInput\", \"\", \"fullWidth\", \"\", \"id\", \"exampleInputPassword1\", \"placeholder\", \"\\u5BC6\\u78BC\", \"name\", \"password\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", \"nbButton\", \"\", \"status\", \"primary\", 3, \"click\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-layout\", 0)(1, \"nb-layout-column\")(2, \"nb-card\")(3, \"nb-card-header\");\n            i0.ɵɵtext(4, \"\\u767B\\u5165\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"form\")(7, \"div\", 1)(8, \"label\", 2);\n            i0.ɵɵtext(9, \"\\u5E33\\u865F\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"input\", 3);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_10_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.account, $event) || (ctx.account = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 1)(12, \"label\", 4);\n            i0.ɵɵtext(13, \"\\u5BC6\\u78BC\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"input\", 5);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.password, $event) || (ctx.password = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_15_listener() {\n              return ctx.login();\n            });\n            i0.ɵɵtext(16, \" \\u767B\\u5165 \");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(10);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.account);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.password);\n          }\n        },\n        dependencies: [NbLayoutModule, i7.NbLayoutComponent, i7.NbLayoutColumnComponent, NbCardModule, i7.NbCardComponent, i7.NbCardBodyComponent, i7.NbCardHeaderComponent, FormsModule, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.NgModel, i8.NgForm, NbInputModule, i7.NbInputDirective, NbButtonModule, i7.NbButtonComponent]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}