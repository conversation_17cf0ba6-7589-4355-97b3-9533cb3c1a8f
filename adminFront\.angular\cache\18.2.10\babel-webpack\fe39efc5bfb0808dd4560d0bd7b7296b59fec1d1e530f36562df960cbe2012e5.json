{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NbCardModule, NbCheckboxModule, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\nimport { FormsModule } from '@angular/forms';\nimport { NgFor, NgIf } from '@angular/common';\nimport { PaginationComponent } from '../components/pagination/pagination.component';\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/forms\";\nconst _c0 = () => [];\nfunction RequirementManagementComponent_div_6_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.CBuildCaseName, \" \");\n  }\n}\nfunction RequirementManagementComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"label\", 43);\n    i0.ɵɵtext(2, \"\\u5EFA\\u6848\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-select\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.getListRequirementRequest.CBuildCaseID, $event) || (ctx_r2.getListRequirementRequest.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(4, RequirementManagementComponent_div_6_nb_option_4_Template, 2, 2, \"nb-option\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.getListRequirementRequest.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_nb_option_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r5.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r5.label, \" \");\n  }\n}\nfunction RequirementManagementComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_50_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(109);\n      return i0.ɵɵresetView(ctx_r2.add(dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_button_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_button_51_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateViewerDialog_r9 = i0.ɵɵreference(113);\n      return i0.ɵɵresetView(ctx_r2.openTemplateViewer(templateViewerDialog_r9));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \"\\u67E5\\u770B\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_80_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_80_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const data_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(109);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r11, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_80_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_80_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const data_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r11));\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 48)(1, \"td\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\", 50);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 50);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 50);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 49);\n    i0.ɵɵtemplate(20, RequirementManagementComponent_tr_80_button_20_Template, 3, 0, \"button\", 51)(21, RequirementManagementComponent_tr_80_button_21_Template, 3, 0, \"button\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.CBuildCaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r11.CHouseType || i0.ɵɵpureFunction0(14, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r11.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(13, 10, data_r11.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 12, data_r11.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_tr_106_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_106_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const data_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const dialog_r7 = i0.ɵɵreference(109);\n      return i0.ɵɵresetView(ctx_r2.onEdit(data_r14, dialog_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_106_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_tr_106_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const data_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDelete(data_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵtext(2, \"\\u522A\\u9664\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_tr_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 48)(1, \"td\", 49);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 50);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 50);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"getStatusName\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 50);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 50);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"ngxNumberWithCommas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 57);\n    i0.ɵɵtemplate(18, RequirementManagementComponent_tr_106_button_18_Template, 3, 0, \"button\", 51)(19, RequirementManagementComponent_tr_106_button_19_Template, 3, 0, \"button\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const data_r14 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r14.CRequirement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r14.CGroupName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getHouseType(data_r14.CHouseType || i0.ɵɵpureFunction0(13, _c0)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(data_r14.CSort);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, data_r14.CStatus));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getCIsShowText(data_r14));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 11, data_r14.CUnitPrice || 0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u5EFA\\u6848\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_app_form_group_10_nb_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const b_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", b_r18.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", b_r18.CBuildCaseName, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_app_form_group_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-group\", 63)(1, \"nb-select\", 76);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_108_app_form_group_10_Template_nb_select_selectedChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CBuildCaseID, $event) || (ctx_r2.saveRequirement.CBuildCaseID = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_108_app_form_group_10_nb_option_2_Template, 2, 2, \"nb-option\", 68);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", \"\\u5EFA\\u6848\\u540D\\u7A31\")(\"labelFor\", \"CBuildCaseID\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CBuildCaseID);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.buildCaseList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r19.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r19.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_nb_option_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_108_span_2_Template, 2, 0, \"span\", 59)(3, RequirementManagementComponent_ng_template_108_span_3_Template, 2, 0, \"span\", 59)(4, RequirementManagementComponent_ng_template_108_span_4_Template, 2, 0, \"span\", 59)(5, RequirementManagementComponent_ng_template_108_span_5_Template, 2, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 60)(7, \"div\", 8)(8, \"div\", 61)(9, \"div\", 8);\n    i0.ɵɵtemplate(10, RequirementManagementComponent_ng_template_108_app_form_group_10_Template, 3, 5, \"app-form-group\", 62);\n    i0.ɵɵelementStart(11, \"app-form-group\", 63)(12, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"app-form-group\", 63)(14, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"app-form-group\", 63)(16, \"input\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 63)(18, \"nb-select\", 67);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_108_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_108_nb_option_19_Template, 2, 2, \"nb-option\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 63)(21, \"nb-select\", 69);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_108_Template_nb_select_selectedChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(22, RequirementManagementComponent_ng_template_108_nb_option_22_Template, 2, 2, \"nb-option\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"app-form-group\", 63)(24, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"app-form-group\", 63)(26, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 63)(28, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_nb_checkbox_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(29, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"app-form-group\", 63)(31, \"textarea\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_108_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(32, \"nb-card-footer\")(33, \"div\", 8)(34, \"div\", 74)(35, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_108_Template_button_click_35_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.save(ref_r21));\n    });\n    i0.ɵɵtext(36, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_108_Template_button_click_37_listener() {\n      const ref_r21 = i0.ɵɵrestoreView(_r16).dialogRef;\n      return i0.ɵɵresetView(ref_r21.close());\n    });\n    i0.ɵɵtext(38, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false && ctx_r2.currentTab === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentTab === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_110_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u65B0\\u589E\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_110_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u7DE8\\u8F2F\\u6A21\\u677F\\u9700\\u6C42\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RequirementManagementComponent_ng_template_110_nb_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r23.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r23.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_110_nb_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r24.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r24.label, \"\");\n  }\n}\nfunction RequirementManagementComponent_ng_template_110_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 58)(1, \"nb-card-header\");\n    i0.ɵɵtemplate(2, RequirementManagementComponent_ng_template_110_span_2_Template, 2, 0, \"span\", 59)(3, RequirementManagementComponent_ng_template_110_span_3_Template, 2, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 60)(5, \"div\", 8)(6, \"div\", 61)(7, \"div\", 8)(8, \"app-form-group\", 63)(9, \"input\", 64);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRequirement, $event) || (ctx_r2.saveRequirement.CRequirement = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"app-form-group\", 63)(11, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CGroupName, $event) || (ctx_r2.saveRequirement.CGroupName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"app-form-group\", 63)(13, \"input\", 66);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CSort, $event) || (ctx_r2.saveRequirement.CSort = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"app-form-group\", 63)(15, \"nb-select\", 67);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_110_Template_nb_select_selectedChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CHouseType, $event) || (ctx_r2.saveRequirement.CHouseType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(16, RequirementManagementComponent_ng_template_110_nb_option_16_Template, 2, 2, \"nb-option\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"app-form-group\", 63)(18, \"nb-select\", 69);\n    i0.ɵɵtwoWayListener(\"selectedChange\", function RequirementManagementComponent_ng_template_110_Template_nb_select_selectedChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CStatus, $event) || (ctx_r2.saveRequirement.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(19, RequirementManagementComponent_ng_template_110_nb_option_19_Template, 2, 2, \"nb-option\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"app-form-group\", 63)(21, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnitPrice, $event) || (ctx_r2.saveRequirement.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"app-form-group\", 63)(23, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CUnit, $event) || (ctx_r2.saveRequirement.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"app-form-group\", 63)(25, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_nb_checkbox_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CIsShow, $event) || (ctx_r2.saveRequirement.CIsShow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(26, \" \\u986F\\u793A\\u5728\\u5BA2\\u8B8A\\u9700\\u6C42 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"app-form-group\", 63)(28, \"textarea\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_110_Template_textarea_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.saveRequirement.CRemark, $event) || (ctx_r2.saveRequirement.CRemark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\")(30, \"div\", 8)(31, \"div\", 74)(32, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_110_Template_button_click_32_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r22).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveTemplate(ref_r25));\n    });\n    i0.ɵɵtext(33, \"\\u78BA\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_110_Template_button_click_34_listener() {\n      const ref_r25 = i0.ɵɵrestoreView(_r22).dialogRef;\n      return i0.ɵɵresetView(ref_r25.close());\n    });\n    i0.ɵɵtext(35, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isNew === false);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"label\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\")(\"labelFor\", \"CRequirement\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRequirement);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u7FA4\\u7D44\\u985E\\u5225\")(\"labelFor\", \"CGroupName\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CGroupName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u6392\\u5E8F\")(\"labelFor\", \"CSort\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CSort);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u985E\\u578B\")(\"labelFor\", \"CHouseType\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CHouseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.houseType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u72C0\\u614B\")(\"labelFor\", \"CStatus\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"selected\", ctx_r2.saveRequirement.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.statusOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u50F9\")(\"labelFor\", \"CUnitPrice\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnitPrice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u55AE\\u4F4D\")(\"labelFor\", \"CUnit\")(\"isRequired\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CUnit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\")(\"labelFor\", \"CIsShow\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CIsShow);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", \"\\u5099\\u8A3B\\u8AAA\\u660E\")(\"labelFor\", \"CRemark\")(\"isRequired\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.saveRequirement.CRemark);\n  }\n}\nfunction RequirementManagementComponent_ng_template_112_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 78)(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵtext(3, \"\\u6A21\\u677F\\u7BA1\\u7406\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"nb-card-body\", 79)(5, \"app-template-viewer\", 80);\n    i0.ɵɵlistener(\"addTemplate\", function RequirementManagementComponent_ng_template_112_Template_app_template_viewer_addTemplate_5_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAddTemplate());\n    })(\"deleteTemplate\", function RequirementManagementComponent_ng_template_112_Template_app_template_viewer_deleteTemplate_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDeleteTemplate($event));\n    })(\"selectTemplate\", function RequirementManagementComponent_ng_template_112_Template_app_template_viewer_selectTemplate_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSelectTemplate($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"nb-card-footer\")(7, \"div\", 81)(8, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_112_Template_button_click_8_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      return i0.ɵɵresetView(ref_r27.close());\n    });\n    i0.ɵɵtext(9, \"\\u95DC\\u9589\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"templates\", ctx_r2.templateList)(\"templateDetails\", ctx_r2.templateDetailList);\n  }\n}\nfunction RequirementManagementComponent_ng_template_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 83)(1, \"nb-card-header\")(2, \"h5\");\n    i0.ɵɵelement(3, \"i\", 84);\n    i0.ɵɵtext(4, \"\\u65B0\\u589E\\u6A21\\u677F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"form\", 85, 4);\n    i0.ɵɵlistener(\"ngSubmit\", function RequirementManagementComponent_ng_template_114_Template_form_ngSubmit_6_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveNewTemplate(ref_r29));\n    });\n    i0.ɵɵelementStart(8, \"div\", 86)(9, \"label\", 87)(10, \"strong\");\n    i0.ɵɵtext(11, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementStart(12, \"span\", 88);\n    i0.ɵɵtext(13, \"*\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"input\", 89);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newTemplate.TemplateName, $event) || (ctx_r2.newTemplate.TemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"small\", 90);\n    i0.ɵɵtext(16, \"\\u6700\\u591A50\\u500B\\u5B57\\u5143\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 86)(18, \"label\", 91)(19, \"strong\");\n    i0.ɵɵtext(20, \"\\u6A21\\u677F\\u63CF\\u8FF0\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"textarea\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_ng_template_114_Template_textarea_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.newTemplate.Description, $event) || (ctx_r2.newTemplate.Description = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"small\", 90);\n    i0.ɵɵtext(23, \"\\u6700\\u591A200\\u500B\\u5B57\\u5143\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\")(25, \"div\", 93)(26, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_114_Template_button_click_26_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      return i0.ɵɵresetView(ref_r29.close());\n    });\n    i0.ɵɵelement(27, \"i\", 94);\n    i0.ɵɵtext(28, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function RequirementManagementComponent_ng_template_114_Template_button_click_29_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveNewTemplate(ref_r29));\n    });\n    i0.ɵɵelement(30, \"i\", 96);\n    i0.ɵɵtext(31, \"\\u5132\\u5B58 \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newTemplate.TemplateName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.newTemplate.Description);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.newTemplate.TemplateName || ctx_r2.newTemplate.TemplateName.trim() === \"\");\n  }\n}\nexport class RequirementManagementComponent extends BaseComponent {\n  constructor(_allow, enumHelper, dialogService, message, valid, buildCaseService, requirementService, pettern, router, destroyref) {\n    super(_allow);\n    this._allow = _allow;\n    this.enumHelper = enumHelper;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this.buildCaseService = buildCaseService;\n    this.requirementService = requirementService;\n    this.pettern = pettern;\n    this.router = router;\n    this.destroyref = destroyref;\n    // request\n    this.getListRequirementRequest = {};\n    this.getRequirementRequest = {};\n    // response\n    this.buildCaseList = [];\n    this.requirementList = [];\n    this.saveRequirement = {\n      CHouseType: []\n    };\n    this.statusOptions = [{\n      value: 0,\n      label: '停用'\n    }, {\n      value: 1,\n      label: '啟用'\n    }];\n    this.houseType = this.enumHelper.getEnumOptions(EnumHouseType);\n    this.isNew = false;\n    this.currentBuildCase = 0;\n    this.currentTab = 0; // 追蹤當前選中的 tab\n    // 模板資料\n    this.templateList = [{\n      TemplateID: 1,\n      TemplateName: '模板A',\n      Description: '範例模板A'\n    }, {\n      TemplateID: 2,\n      TemplateName: '模板B',\n      Description: '範例模板B'\n    }];\n    this.templateDetailList = [{\n      TemplateDetailID: 1,\n      TemplateID: 1,\n      RefID: 101,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目A'\n    }, {\n      TemplateDetailID: 2,\n      TemplateID: 1,\n      RefID: 102,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目B'\n    }, {\n      TemplateDetailID: 3,\n      TemplateID: 2,\n      RefID: 201,\n      ModuleType: 'Requirement',\n      FieldName: 'CRequirement',\n      FieldValue: '工程項目C'\n    }];\n    // 新增模板表單資料\n    this.newTemplate = {\n      TemplateName: '',\n      Description: ''\n    };\n    // Tab 切換事件處理\n    this.isFirstTabChange = true;\n    this.initializeSearchForm();\n    this.getBuildCaseList();\n  }\n  ngOnInit() {}\n  // 初始化搜尋表單\n  initializeSearchForm() {\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\n    this.getListRequirementRequest.CStatus = -1;\n    this.getListRequirementRequest.CIsShow = null;\n    this.getListRequirementRequest.CRequirement = '';\n    this.getListRequirementRequest.CGroupName = '';\n    // 預設全選所有房屋類型\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\n  }\n  // 重置搜尋\n  resetSearch() {\n    this.initializeSearchForm();\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\n      setTimeout(() => {\n        if (this.currentTab === 0) {\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        } else {\n          this.getListRequirementRequest.CBuildCaseID = 0;\n        }\n        this.getList();\n      }, 0);\n    } else {\n      this.getList();\n    }\n  }\n  getHouseType(hTypes) {\n    if (!hTypes) {\n      return '';\n    }\n    if (!Array.isArray(hTypes)) {\n      hTypes = [hTypes];\n    }\n    let labels = [];\n    hTypes.forEach(htype => {\n      let findH = this.houseType.find(x => x.value == htype);\n      if (findH) {\n        labels.push(findH.label);\n      }\n    });\n    return labels.join(', ');\n  }\n  validation() {\n    this.valid.clear();\n    // 根據當前 tab 決定是否需要驗證建案名稱\n    if (this.currentTab === 0) {\n      // 建案頁面需要驗證建案名稱\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\n    }\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n  }\n  add(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 根據當前 tab 決定是否需要建案ID\n    if (this.currentTab === 0) {\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\n      if (this.currentBuildCase != 0) {\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    } else {\n      // 模板頁面 - 設定建案ID為0\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.dialogService.open(dialog);\n  }\n  onEdit(data, dialog) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this.isNew = false;\n      try {\n        yield _this.getData();\n        _this.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get function data\", error);\n      }\n    })();\n  }\n  save(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\n    if (this.currentTab === 1) {\n      this.saveRequirement.CBuildCaseID = 0;\n    }\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: this.saveRequirement\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  onDelete(data) {\n    this.saveRequirement.CRequirementID = data.CRequirementID;\n    this.isNew = false;\n    if (window.confirm('是否確定刪除?')) {\n      this.remove();\n    } else {\n      return;\n    }\n  }\n  remove() {\n    this.requirementService.apiRequirementDeleteDataPost$Json({\n      body: {\n        CRequirementID: this.saveRequirement.CRequirementID\n      }\n    }).subscribe(res => {\n      this.message.showSucessMSG('執行成功');\n      this.getList();\n    });\n  }\n  getBuildCaseList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\n      this.buildCaseList = res.Entries;\n      // 只在建案 tab 下且有建案時才查詢\n      if (this.currentTab === 0 && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n        this.getList();\n      } else if (this.currentTab === 1) {\n        this.getListRequirementRequest.CBuildCaseID = 0;\n        this.getList();\n      }\n    });\n  }\n  getList() {\n    this.getListRequirementRequest.PageSize = this.pageSize;\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\n    this.requirementList = [];\n    this.totalRecords = 0;\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\n    if (this.currentTab === 1) {\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      // 建案頁面的邏輯保持不變\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\n      }\n    }\n    this.requirementService.apiRequirementGetListPost$Json({\n      body: this.getListRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.requirementList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }\n    });\n  }\n  getData() {\n    this.requirementService.apiRequirementGetDataPost$Json({\n      body: this.getRequirementRequest\n    }).pipe().subscribe(res => {\n      if (res.StatusCode == 0) {\n        if (res.Entries) {\n          this.saveRequirement = {\n            CHouseType: [],\n            CIsShow: false\n          };\n          this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\n          this.saveRequirement.CGroupName = res.Entries.CGroupName;\n          this.saveRequirement.CHouseType = res.Entries.CHouseType ? Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType] : [];\n          this.saveRequirement.CRemark = res.Entries.CRemark;\n          this.saveRequirement.CRequirement = res.Entries.CRequirement;\n          this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\n          this.saveRequirement.CSort = res.Entries.CSort;\n          this.saveRequirement.CStatus = res.Entries.CStatus;\n          this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\n          this.saveRequirement.CUnit = res.Entries.CUnit;\n          // TODO: 等後端API更新後啟用這行\n          this.saveRequirement.CIsShow = res.Entries.CIsShow || false;\n        }\n      }\n    });\n  }\n  onHouseTypeChange(value, checked) {\n    console.log(checked);\n    if (checked) {\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\n        this.saveRequirement.CHouseType?.push(value);\n      }\n      console.log(this.saveRequirement.CHouseType);\n    } else {\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\n    }\n  }\n  getCIsShowText(data) {\n    return data.CIsShow ? '是' : '否';\n  }\n  onTabChange(event) {\n    // 避免頁面初始化時自動觸發重複查詢\n    if (this.isFirstTabChange) {\n      this.isFirstTabChange = false;\n      return;\n    }\n    // 根據 tabTitle 來判斷當前頁面\n    if (event.tabTitle === '共用') {\n      this.currentTab = 1;\n      this.getListRequirementRequest.CBuildCaseID = 0;\n    } else {\n      this.currentTab = 0;\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\n      }\n    }\n    this.getList();\n  }\n  // 新增模板\n  addTemplate(dialog) {\n    this.isNew = true;\n    this.saveRequirement = {\n      CHouseType: [],\n      CIsShow: false\n    };\n    this.saveRequirement.CStatus = 1;\n    this.saveRequirement.CUnitPrice = 0;\n    // 模板設定建案ID為0\n    this.saveRequirement.CBuildCaseID = 0;\n    this.dialogService.open(dialog);\n  }\n  // 編輯模板\n  onEditTemplate(data, dialog) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.getRequirementRequest.CRequirementID = data.CRequirementID;\n      _this2.isNew = false;\n      try {\n        yield _this2.getData();\n        _this2.dialogService.open(dialog);\n      } catch (error) {\n        console.log(\"Failed to get template data\", error);\n      }\n    })();\n  }\n  // 保存模板\n  saveTemplate(ref) {\n    // 模板驗證（不包含建案名稱）\n    this.valid.clear();\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\n    this.valid.required('[安排]', this.saveRequirement.CSort);\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\n    // 群組名稱長度驗證\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\n    }\n    // 備註說明長度驗證\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\n    }\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 確保模板建案ID為0\n    const templateData = {\n      ...this.saveRequirement\n    };\n    templateData.CBuildCaseID = 0;\n    this.requirementService.apiRequirementSaveDataPost$Json({\n      body: templateData\n    }).subscribe(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG('執行成功');\n        this.getList();\n      } else {\n        this.message.showErrorMSG(res.Message);\n      }\n    });\n    ref.close();\n  }\n  openTemplateViewer(templateViewerDialog) {\n    this.dialogService.open(templateViewerDialog);\n  }\n  onAddTemplate() {\n    // 重置新增模板表單\n    this.newTemplate = {\n      TemplateName: '',\n      Description: ''\n    };\n    // 打開新增模板對話框\n    this.dialogService.open(this.addTemplateDialog);\n  }\n  // 儲存新模板\n  saveNewTemplate(ref) {\n    // 驗證模板名稱\n    if (!this.newTemplate.TemplateName || this.newTemplate.TemplateName.trim() === '') {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return;\n    }\n    // 檢查模板名稱是否重複\n    const existingTemplate = this.templateList.find(t => t.TemplateName.toLowerCase() === this.newTemplate.TemplateName.trim().toLowerCase());\n    if (existingTemplate) {\n      this.message.showErrorMSG('模板名稱已存在，請使用其他名稱');\n      return;\n    }\n    // 生成新的模板ID\n    const newId = Math.max(...this.templateList.map(t => t.TemplateID || 0), 0) + 1;\n    // 創建新模板\n    const template = {\n      TemplateID: newId,\n      TemplateName: this.newTemplate.TemplateName.trim(),\n      Description: this.newTemplate.Description?.trim() || ''\n    };\n    // 添加到模板列表\n    this.templateList.push(template);\n    // 顯示成功消息並關閉對話框\n    this.message.showSucessMSG(`模板 \"${template.TemplateName}\" 已成功創建！`);\n    ref.close();\n  }\n  onSelectTemplate(tpl) {\n    // 查看模板邏輯\n    alert('查看模板: ' + tpl.TemplateName);\n  }\n  onDeleteTemplate(templateID) {\n    this.templateList = this.templateList.filter(t => t.TemplateID !== templateID);\n    this.templateDetailList = this.templateDetailList.filter(d => d.TemplateID !== templateID);\n  }\n  static {\n    this.ɵfac = function RequirementManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RequirementManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i6.RequirementService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.DestroyRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequirementManagementComponent,\n      selectors: [[\"app-requirement-management\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 116,\n      vars: 23,\n      consts: [[\"dialog\", \"\"], [\"templateDialog\", \"\"], [\"templateViewerDialog\", \"\"], [\"addTemplateDialog\", \"\"], [\"addTemplateForm\", \"ngForm\"], [\"accent\", \"success\"], [1, \"bg-white\", \"pb-0\"], [1, \"col-12\"], [1, \"row\"], [\"class\", \"form-group col-12 col-md-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-12\", \"col-md-4\"], [\"for\", \"requirement\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"requirement\", \"name\", \"requirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"groupName\", 1, \"label\", \"mr-2\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"groupName\", \"name\", \"groupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"houseType\", 1, \"label\", \"mr-2\"], [\"multiple\", \"\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"status\", 1, \"label\", \"mr-2\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"isShow\", 1, \"label\", \"mr-2\"], [1, \"col-md-6\"], [1, \"form-group\", \"col-12\", \"col-md-6\", \"text-right\"], [1, \"btn\", \"btn-secondary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"mr-1\"], [1, \"btn\", \"btn-info\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [\"class\", \"btn btn-success mr-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary mr-2\", 3, \"click\", 4, \"ngIf\"], [3, \"changeTab\"], [\"tabTitle\", \"\\u5EFA\\u6848\"], [1, \"pt-3\"], [1, \"col-12\", \"mt-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"800px\", \"background-color\", \"#f3f3f3\"], [1, \"d-flex\", \"text-white\", 2, \"background-color\", \"#27ae60\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-1\"], [\"class\", \"d-flex\", 4, \"ngFor\", \"ngForOf\"], [3, \"PageChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [\"tabTitle\", \"\\u5171\\u7528\"], [\"scope\", \"col\", 1, \"col-3\"], [\"for\", \"buildCase\", 1, \"label\", \"mr-2\"], [1, \"btn\", \"btn-success\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"mr-1\"], [1, \"btn\", \"btn-primary\", \"mr-2\", 3, \"click\"], [1, \"fas\", \"fa-eye\", \"mr-1\"], [1, \"d-flex\"], [1, \"col-2\"], [1, \"col-1\"], [\"type\", \"button\", \"class\", \"btn btn-outline-success m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-outline-danger m-1  btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-success\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"mr-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-danger\", \"m-1\", \"btn-sm\", 3, \"click\"], [1, \"far\", \"fa-trash-alt\", \"mr-1\"], [1, \"col-3\"], [1, \"mr-md-5\", \"ml-md-5\", 2, \"height\", \"100%\", \"overflow\", \"auto\", \"max-width\", \"500px\"], [4, \"ngIf\"], [2, \"padding\", \"1rem 2rem\"], [1, \"col-12\", \"col-md-12\"], [3, \"label\", \"labelFor\", \"isRequired\", 4, \"ngIf\"], [3, \"label\", \"labelFor\", \"isRequired\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CRequirement\", \"name\", \"CRequirement\", \"placeholder\", \"\\u5DE5\\u7A0B\\u9805\\u76EE\", \"maxlength\", \"50\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CGroupName\", \"name\", \"CGroupName\", \"placeholder\", \"\\u7FA4\\u7D44\\u985E\\u5225\", \"maxlength\", \"20\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CSort\", \"name\", \"CSort\", \"placeholder\", \"\\u6392\\u5E8F\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CHouseType\", \"name\", \"CHouseType\", \"multiple\", \"\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CStatus\", \"name\", \"CStatus\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"type\", \"number\", \"nbInput\", \"\", \"id\", \"CUnitPrice\", \"name\", \"CUnitPrice\", \"placeholder\", \"\\u55AE\\u50F9\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"nbInput\", \"\", \"id\", \"CUnit\", \"name\", \"CUnit\", \"placeholder\", \"\\u55AE\\u4F4D\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"CIsShow\", \"name\", \"CIsShow\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [\"nbInput\", \"\", \"id\", \"CRemark\", \"name\", \"CRemark\", \"placeholder\", \"\\u5099\\u8A3B\\u8AAA\\u660E\", \"maxlength\", \"100\", \"rows\", \"3\", 1, \"flex-grow-1\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-12\", \"text-center\"], [1, \"btn\", \"btn-danger\", \"mr-2\", 3, \"click\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\", \"id\", \"CBuildCaseID\", \"name\", \"CBuildCaseID\", 1, \"flex-grow-1\", 3, \"selectedChange\", \"selected\"], [\"langg\", \"\", 3, \"value\"], [2, \"width\", \"90vw\", \"max-width\", \"1200px\", \"height\", \"80vh\"], [2, \"overflow\", \"auto\"], [3, \"addTemplate\", \"deleteTemplate\", \"selectTemplate\", \"templates\", \"templateDetails\"], [1, \"text-center\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [2, \"width\", \"500px\"], [1, \"fas\", \"fa-plus\", \"mr-2\"], [3, \"ngSubmit\"], [1, \"form-group\", \"mb-3\"], [\"for\", \"templateName\", 1, \"form-label\"], [1, \"text-danger\"], [\"type\", \"text\", \"id\", \"templateName\", \"name\", \"templateName\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"required\", \"\", \"maxlength\", \"50\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"templateDescription\", 1, \"form-label\"], [\"id\", \"templateDescription\", \"name\", \"templateDescription\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u63CF\\u8FF0\\uFF08\\u53EF\\u9078\\uFF09\", \"rows\", \"3\", \"maxlength\", \"200\", 1, \"form-control\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-save\", \"mr-1\"]],\n      template: function RequirementManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\", 6)(4, \"div\", 7)(5, \"div\", 8);\n          i0.ɵɵtemplate(6, RequirementManagementComponent_div_6_Template, 5, 2, \"div\", 9);\n          i0.ɵɵelementStart(7, \"div\", 10)(8, \"label\", 11);\n          i0.ɵɵtext(9, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_10_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CRequirement, $event) || (ctx.getListRequirementRequest.CRequirement = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"label\", 13);\n          i0.ɵɵtext(13, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_input_ngModelChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CGroupName, $event) || (ctx.getListRequirementRequest.CGroupName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 8)(16, \"div\", 10)(17, \"label\", 15);\n          i0.ɵɵtext(18, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CHouseType, $event) || (ctx.getListRequirementRequest.CHouseType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(20, RequirementManagementComponent_nb_option_20_Template, 2, 2, \"nb-option\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 18);\n          i0.ɵɵtext(23, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CStatus, $event) || (ctx.getListRequirementRequest.CStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(25, \"nb-option\", 20);\n          i0.ɵɵtext(26, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 20);\n          i0.ɵɵtext(28, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-option\", 20);\n          i0.ɵɵtext(30, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 10)(32, \"label\", 21);\n          i0.ɵɵtext(33, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.getListRequirementRequest.CIsShow, $event) || (ctx.getListRequirementRequest.CIsShow = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 20);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 20);\n          i0.ɵɵtext(38, \"\\u662F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 20);\n          i0.ɵɵtext(40, \"\\u5426\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 8);\n          i0.ɵɵelement(42, \"div\", 22);\n          i0.ɵɵelementStart(43, \"div\", 23)(44, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetSearch());\n          });\n          i0.ɵɵelement(45, \"i\", 25);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function RequirementManagementComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelement(48, \"i\", 27);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(50, RequirementManagementComponent_button_50_Template, 3, 0, \"button\", 28)(51, RequirementManagementComponent_button_51_Template, 3, 0, \"button\", 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(52, \"nb-card-body\", 6)(53, \"nb-tabset\", 30);\n          i0.ɵɵlistener(\"changeTab\", function RequirementManagementComponent_Template_nb_tabset_changeTab_53_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onTabChange($event));\n          });\n          i0.ɵɵelementStart(54, \"nb-tab\", 31)(55, \"div\", 32)(56, \"div\", 33)(57, \"div\", 34)(58, \"table\", 35)(59, \"thead\")(60, \"tr\", 36)(61, \"th\", 37);\n          i0.ɵɵtext(62, \"\\u5EFA\\u6848\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 37);\n          i0.ɵɵtext(64, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 38);\n          i0.ɵɵtext(66, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 38);\n          i0.ɵɵtext(68, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 38);\n          i0.ɵɵtext(70, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 38);\n          i0.ɵɵtext(72, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"th\", 38);\n          i0.ɵɵtext(74, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"th\", 38);\n          i0.ɵɵtext(76, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\", 37);\n          i0.ɵɵtext(78, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"tbody\");\n          i0.ɵɵtemplate(80, RequirementManagementComponent_tr_80_Template, 22, 15, \"tr\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"ngx-pagination\", 40);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_81_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_81_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(82, \"nb-tab\", 41)(83, \"div\", 32)(84, \"div\", 33)(85, \"div\", 34)(86, \"table\", 35)(87, \"thead\")(88, \"tr\", 36)(89, \"th\", 37);\n          i0.ɵɵtext(90, \"\\u5DE5\\u7A0B\\u9805\\u76EE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"th\", 37);\n          i0.ɵɵtext(92, \"\\u7FA4\\u7D44\\u985E\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 38);\n          i0.ɵɵtext(94, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 38);\n          i0.ɵɵtext(96, \"\\u6392\\u5E8F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 38);\n          i0.ɵɵtext(98, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 38);\n          i0.ɵɵtext(100, \"\\u5BA2\\u8B8A\\u9700\\u6C42\\u986F\\u793A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 38);\n          i0.ɵɵtext(102, \"\\u55AE\\u50F9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"th\", 42);\n          i0.ɵɵtext(104, \"\\u64CD\\u4F5C\\u529F\\u80FD\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(105, \"tbody\");\n          i0.ɵɵtemplate(106, RequirementManagementComponent_tr_106_Template, 20, 14, \"tr\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"ngx-pagination\", 40);\n          i0.ɵɵtwoWayListener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_107_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function RequirementManagementComponent_Template_ngx_pagination_PageChange_107_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getList());\n          });\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(108, RequirementManagementComponent_ng_template_108_Template, 39, 43, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(110, RequirementManagementComponent_ng_template_110_Template, 36, 40, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(112, RequirementManagementComponent_ng_template_112_Template, 10, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(114, RequirementManagementComponent_ng_template_114_Template, 32, 3, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CRequirement);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CGroupName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CHouseType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.houseType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.getListRequirementRequest.CIsShow);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentTab === 1);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.requirementList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"CollectionSize\", ctx.totalRecords);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize);\n        }\n      },\n      dependencies: [NbCardModule, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, BreadcrumbComponent, NbInputModule, i3.NbInputDirective, FormsModule, i9.ɵNgNoValidate, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.NgControlStatusGroup, i9.RequiredValidator, i9.MaxLengthValidator, i9.NgModel, i9.NgForm, NbSelectModule, i3.NbSelectComponent, i3.NbOptionComponent, NbOptionModule, NgIf, NgFor, PaginationComponent, StatusPipe, NbCheckboxModule, i3.NbCheckboxComponent, FormGroupComponent, NumberWithCommasPipe, NbTabsetModule, i3.NbTabsetComponent, i3.NbTabComponent, TemplateViewerComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVxdWlyZW1lbnQtbWFuYWdlbWVudC9yZXF1aXJlbWVudC1tYW5hZ2VtZW50LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3TEFBd0wiLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NbCardModule", "NbCheckboxModule", "NbInputModule", "NbOptionModule", "NbSelectModule", "NbTabsetModule", "BaseComponent", "takeUntilDestroyed", "BreadcrumbComponent", "FormsModule", "<PERSON><PERSON><PERSON>", "NgIf", "PaginationComponent", "StatusPipe", "FormGroupComponent", "NumberWithCommasPipe", "EnumHouseType", "TemplateViewerComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r4", "cID", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "ɵɵtwoWayListener", "RequirementManagementComponent_div_6_Template_nb_select_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "getListRequirementRequest", "CBuildCaseID", "ɵɵresetView", "ɵɵtemplate", "RequirementManagementComponent_div_6_nb_option_4_Template", "ɵɵtwoWayProperty", "buildCaseList", "type_r5", "value", "label", "ɵɵlistener", "RequirementManagementComponent_button_50_Template_button_click_0_listener", "_r6", "dialog_r7", "ɵɵreference", "add", "ɵɵelement", "RequirementManagementComponent_button_51_Template_button_click_0_listener", "_r8", "templateViewerDialog_r9", "openTemplateViewer", "RequirementManagementComponent_tr_80_button_20_Template_button_click_0_listener", "_r10", "data_r11", "$implicit", "onEdit", "RequirementManagementComponent_tr_80_button_21_Template_button_click_0_listener", "_r12", "onDelete", "RequirementManagementComponent_tr_80_button_20_Template", "RequirementManagementComponent_tr_80_button_21_Template", "ɵɵtextInterpolate", "CRequirement", "CGroupName", "getHouseType", "CHouseType", "ɵɵpureFunction0", "_c0", "CSort", "ɵɵpipeBind1", "CStatus", "getCIsShowText", "CUnitPrice", "isUpdate", "isDelete", "RequirementManagementComponent_tr_106_button_18_Template_button_click_0_listener", "_r13", "data_r14", "RequirementManagementComponent_tr_106_button_19_Template_button_click_0_listener", "_r15", "RequirementManagementComponent_tr_106_button_18_Template", "RequirementManagementComponent_tr_106_button_19_Template", "b_r18", "RequirementManagementComponent_ng_template_108_app_form_group_10_Template_nb_select_selectedChange_1_listener", "_r17", "saveRequirement", "RequirementManagementComponent_ng_template_108_app_form_group_10_nb_option_2_Template", "type_r19", "status_r20", "RequirementManagementComponent_ng_template_108_span_2_Template", "RequirementManagementComponent_ng_template_108_span_3_Template", "RequirementManagementComponent_ng_template_108_span_4_Template", "RequirementManagementComponent_ng_template_108_span_5_Template", "RequirementManagementComponent_ng_template_108_app_form_group_10_Template", "RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_12_listener", "_r16", "RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_16_listener", "RequirementManagementComponent_ng_template_108_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_108_nb_option_19_Template", "RequirementManagementComponent_ng_template_108_Template_nb_select_selectedChange_21_listener", "RequirementManagementComponent_ng_template_108_nb_option_22_Template", "RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_24_listener", "RequirementManagementComponent_ng_template_108_Template_input_ngModelChange_26_listener", "CUnit", "RequirementManagementComponent_ng_template_108_Template_nb_checkbox_ngModelChange_28_listener", "CIsShow", "RequirementManagementComponent_ng_template_108_Template_textarea_ngModelChange_31_listener", "CRemark", "RequirementManagementComponent_ng_template_108_Template_button_click_35_listener", "ref_r21", "dialogRef", "save", "RequirementManagementComponent_ng_template_108_Template_button_click_37_listener", "close", "isNew", "currentTab", "houseType", "statusOptions", "type_r23", "status_r24", "RequirementManagementComponent_ng_template_110_span_2_Template", "RequirementManagementComponent_ng_template_110_span_3_Template", "RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_9_listener", "_r22", "RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_11_listener", "RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_13_listener", "RequirementManagementComponent_ng_template_110_Template_nb_select_selectedChange_15_listener", "RequirementManagementComponent_ng_template_110_nb_option_16_Template", "RequirementManagementComponent_ng_template_110_Template_nb_select_selectedChange_18_listener", "RequirementManagementComponent_ng_template_110_nb_option_19_Template", "RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_21_listener", "RequirementManagementComponent_ng_template_110_Template_input_ngModelChange_23_listener", "RequirementManagementComponent_ng_template_110_Template_nb_checkbox_ngModelChange_25_listener", "RequirementManagementComponent_ng_template_110_Template_textarea_ngModelChange_28_listener", "RequirementManagementComponent_ng_template_110_Template_button_click_32_listener", "ref_r25", "saveTemplate", "RequirementManagementComponent_ng_template_110_Template_button_click_34_listener", "RequirementManagementComponent_ng_template_112_Template_app_template_viewer_addTemplate_5_listener", "_r26", "onAddTemplate", "RequirementManagementComponent_ng_template_112_Template_app_template_viewer_deleteTemplate_5_listener", "onDeleteTemplate", "RequirementManagementComponent_ng_template_112_Template_app_template_viewer_selectTemplate_5_listener", "onSelectTemplate", "RequirementManagementComponent_ng_template_112_Template_button_click_8_listener", "ref_r27", "templateList", "templateDetailList", "RequirementManagementComponent_ng_template_114_Template_form_ngSubmit_6_listener", "ref_r29", "_r28", "saveNewTemplate", "RequirementManagementComponent_ng_template_114_Template_input_ngModelChange_14_listener", "newTemplate", "TemplateName", "RequirementManagementComponent_ng_template_114_Template_textarea_ngModelChange_21_listener", "Description", "RequirementManagementComponent_ng_template_114_Template_button_click_26_listener", "RequirementManagementComponent_ng_template_114_Template_button_click_29_listener", "trim", "RequirementManagementComponent", "constructor", "_allow", "enum<PERSON>elper", "dialogService", "message", "valid", "buildCaseService", "requirementService", "pettern", "router", "destroyref", "getRequirementRequest", "requirementList", "getEnumOptions", "currentBuildCase", "TemplateID", "TemplateDetailID", "RefID", "ModuleType", "FieldName", "FieldValue", "isFirstTabChange", "initializeSearchForm", "getBuildCaseList", "ngOnInit", "map", "type", "resetSearch", "length", "setTimeout", "getList", "hTypes", "Array", "isArray", "labels", "for<PERSON>ach", "htype", "findH", "find", "x", "push", "join", "validation", "clear", "required", "errorMessages", "dialog", "open", "data", "_this", "_asyncToGenerator", "CRequirementID", "getData", "error", "console", "log", "ref", "showErrorMSGs", "apiRequirementSaveDataPost$Json", "body", "subscribe", "res", "StatusCode", "showSucessMSG", "showErrorMSG", "Message", "window", "confirm", "remove", "apiRequirementDeleteDataPost$Json", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "Entries", "PageSize", "pageSize", "PageIndex", "pageIndex", "totalRecords", "apiRequirementGetListPost$Json", "TotalItems", "apiRequirementGetDataPost$Json", "onHouseTypeChange", "checked", "includes", "filter", "v", "onTabChange", "event", "tabTitle", "addTemplate", "onEditTemplate", "_this2", "templateData", "templateViewerDialog", "addTemplateDialog", "existingTemplate", "t", "toLowerCase", "newId", "Math", "max", "template", "tpl", "alert", "templateID", "d", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i3", "NbDialogService", "i4", "MessageService", "i5", "ValidationHelper", "i6", "BuildCaseService", "RequirementService", "i7", "PetternHelper", "i8", "Router", "DestroyRef", "selectors", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "RequirementManagementComponent_Template", "rf", "ctx", "RequirementManagementComponent_div_6_Template", "RequirementManagementComponent_Template_input_ngModelChange_10_listener", "_r1", "RequirementManagementComponent_Template_input_ngModelChange_14_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_19_listener", "RequirementManagementComponent_nb_option_20_Template", "RequirementManagementComponent_Template_nb_select_ngModelChange_24_listener", "RequirementManagementComponent_Template_nb_select_ngModelChange_34_listener", "RequirementManagementComponent_Template_button_click_44_listener", "RequirementManagementComponent_Template_button_click_47_listener", "RequirementManagementComponent_button_50_Template", "RequirementManagementComponent_button_51_Template", "RequirementManagementComponent_Template_nb_tabset_changeTab_53_listener", "RequirementManagementComponent_tr_80_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_81_listener", "RequirementManagementComponent_tr_106_Template", "RequirementManagementComponent_Template_ngx_pagination_PageChange_107_listener", "RequirementManagementComponent_ng_template_108_Template", "ɵɵtemplateRefExtractor", "RequirementManagementComponent_ng_template_110_Template", "RequirementManagementComponent_ng_template_112_Template", "RequirementManagementComponent_ng_template_114_Template", "isCreate", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "i9", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "NbSelectComponent", "NbOptionComponent", "NbCheckboxComponent", "NbTabsetComponent", "NbTabComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\requirement-management\\requirement-management.component.html"], "sourcesContent": ["import { Component, DestroyRef, OnInit, TemplateRef, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NbCardModule, NbCheckboxModule, NbDialogService, NbInputModule, NbOptionModule, NbSelectModule, NbTabsetModule } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { EnumHelper } from 'src/app/shared/helper/enumHelper';\r\nimport { PetternHelper } from 'src/app/shared/helper/petternHelper';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, RequirementService } from 'src/services/api/services';\r\nimport { BaseComponent } from '../components/base/baseComponent';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetListRequirementRequest, GetRequirement, GetRequirementByIdRequest, SaveDataRequirement } from 'src/services/api/models';\r\nimport { BreadcrumbComponent } from '../components/breadcrumb/breadcrumb.component';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NgFor, NgIf } from '@angular/common';\r\nimport { PaginationComponent } from '../components/pagination/pagination.component';\r\nimport { StatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { FormGroupComponent } from 'src/app/@theme/components/form-group/form-group.component';\r\nimport { NumberWithCommasPipe } from 'src/app/@theme/pipes/number-with-commas.pipe';\r\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\r\nimport { Template, TemplateDetail, TemplateViewerComponent } from 'src/app/shared/components/template-viewer/template-viewer.component';\r\n\r\n@Component({\r\n  selector: 'app-requirement-management',\r\n  standalone: true, imports: [\r\n    NbCardModule,\r\n    BreadcrumbComponent,\r\n    NbInputModule,\r\n    FormsModule,\r\n    NbSelectModule,\r\n    NbOptionModule,\r\n    NgIf,\r\n    NgFor,\r\n    PaginationComponent,\r\n    StatusPipe,\r\n    NbCheckboxModule,\r\n    FormGroupComponent,\r\n    NumberWithCommasPipe,\r\n    NbTabsetModule,\r\n    TemplateViewerComponent\r\n  ],\r\n  templateUrl: './requirement-management.component.html',\r\n  styleUrl: './requirement-management.component.scss'\r\n})\r\nexport class RequirementManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private enumHelper: EnumHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private requirementService: RequirementService,\r\n    private pettern: PetternHelper,\r\n    private router: Router,\r\n    private destroyref: DestroyRef\r\n  ) {\r\n    super(_allow);\r\n    this.initializeSearchForm();\r\n    this.getBuildCaseList();\r\n  }\r\n  // request\r\n  getListRequirementRequest = {} as GetListRequirementRequest & { CIsShow?: boolean | null };\r\n  getRequirementRequest: GetRequirementByIdRequest = {};\r\n  // response\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  requirementList: GetRequirement[] = [];\r\n  saveRequirement: SaveDataRequirement & { CIsShow?: boolean } = { CHouseType: [] };\r\n\r\n  statusOptions = [\r\n    { value: 0, label: '停用' },\r\n    { value: 1, label: '啟用' },\r\n  ];\r\n  houseType = this.enumHelper.getEnumOptions(EnumHouseType);\r\n  isNew = false;\r\n  currentBuildCase = 0;\r\n  currentTab = 0; // 追蹤當前選中的 tab\r\n\r\n  // 模板資料\r\n  templateList: Template[] = [\r\n    { TemplateID: 1, TemplateName: '模板A', Description: '範例模板A' },\r\n    { TemplateID: 2, TemplateName: '模板B', Description: '範例模板B' }\r\n  ];\r\n  templateDetailList: TemplateDetail[] = [\r\n    { TemplateDetailID: 1, TemplateID: 1, RefID: 101, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目A' },\r\n    { TemplateDetailID: 2, TemplateID: 1, RefID: 102, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目B' },\r\n    { TemplateDetailID: 3, TemplateID: 2, RefID: 201, ModuleType: 'Requirement', FieldName: 'CRequirement', FieldValue: '工程項目C' }\r\n  ];\r\n\r\n  // 新增模板表單資料\r\n  newTemplate: Template = {\r\n    TemplateName: '',\r\n    Description: ''\r\n  };\r\n\r\n  override ngOnInit(): void { }\r\n  // 初始化搜尋表單\r\n  initializeSearchForm() {\r\n    // this.getListRequirementRequest.CBuildCaseID = -1; // <-- 移除此行，避免預設為 -1\r\n    this.getListRequirementRequest.CStatus = -1;\r\n    this.getListRequirementRequest.CIsShow = null;\r\n    this.getListRequirementRequest.CRequirement = '';\r\n    this.getListRequirementRequest.CGroupName = '';\r\n    // 預設全選所有房屋類型\r\n    this.getListRequirementRequest.CHouseType = this.houseType.map(type => type.value);\r\n  }\r\n\r\n  // 重置搜尋\r\n  resetSearch() {\r\n    this.initializeSearchForm();\r\n    // 重置後如果有建案資料，重新設定預設選擇第一個建案\r\n    if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n      setTimeout(() => {\r\n        if (this.currentTab === 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n        } else {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n        }\r\n        this.getList();\r\n      }, 0);\r\n    } else {\r\n      this.getList();\r\n    }\r\n  }\r\n\r\n  getHouseType(hTypes: number[] | number | null | undefined): string {\r\n    if (!hTypes) {\r\n      return '';\r\n    }\r\n\r\n    if (!Array.isArray(hTypes)) {\r\n      hTypes = [hTypes];\r\n    }\r\n\r\n    let labels: string[] = [];\r\n    hTypes.forEach(htype => {\r\n      let findH = this.houseType.find(x => x.value == htype);\r\n      if (findH) {\r\n        labels.push(findH.label);\r\n      }\r\n    });\r\n    return labels.join(', ');\r\n  } validation() {\r\n    this.valid.clear();\r\n\r\n    // 根據當前 tab 決定是否需要驗證建案名稱\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面需要驗證建案名稱\r\n      this.valid.required('[建案名稱]', this.saveRequirement.CBuildCaseID);\r\n    }\r\n\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n  }\r\n\r\n  add(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n\r\n    // 根據當前 tab 決定是否需要建案ID\r\n    if (this.currentTab === 0) {\r\n      // 建案頁面 - 使用當前選擇的建案或第一個建案\r\n      if (this.currentBuildCase != 0) {\r\n        this.saveRequirement.CBuildCaseID = this.currentBuildCase;\r\n      } else if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.saveRequirement.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    } else {\r\n      // 模板頁面 - 設定建案ID為0\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  async onEdit(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get function data\", error)\r\n    }\r\n  }\r\n\r\n  save(ref: any) {\r\n    this.validation();\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 如果是模板頁面，確保 CBuildCaseID 設定為 0\r\n    if (this.currentTab === 1) {\r\n      this.saveRequirement.CBuildCaseID = 0;\r\n    }\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: this.saveRequirement\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  onDelete(data: GetRequirement) {\r\n    this.saveRequirement.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    if (window.confirm('是否確定刪除?')) {\r\n      this.remove();\r\n    } else {\r\n      return;\r\n    }\r\n  }\r\n\r\n  remove() {\r\n    this.requirementService.apiRequirementDeleteDataPost$Json({\r\n      body: {\r\n        CRequirementID: this.saveRequirement.CRequirementID!\r\n      }\r\n    }).subscribe(res => {\r\n      this.message.showSucessMSG('執行成功');\r\n      this.getList();\r\n    });\r\n  }\r\n\r\n  getBuildCaseList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} })\r\n      .pipe(takeUntilDestroyed(this.destroyref)).subscribe(res => {\r\n        this.buildCaseList = res.Entries!;\r\n        // 只在建案 tab 下且有建案時才查詢\r\n        if (this.currentTab === 0 && this.buildCaseList.length > 0) {\r\n          this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n          this.getList();\r\n        } else if (this.currentTab === 1) {\r\n          this.getListRequirementRequest.CBuildCaseID = 0;\r\n          this.getList();\r\n        }\r\n      })\r\n  }\r\n\r\n  getList() {\r\n    this.getListRequirementRequest.PageSize = this.pageSize;\r\n    this.getListRequirementRequest.PageIndex = this.pageIndex;\r\n    this.requirementList = [] as GetRequirement[];\r\n    this.totalRecords = 0;\r\n    // 當選擇模板頁面時，強制設定 CBuildCaseID = 0 且不可異動\r\n    if (this.currentTab === 1) {\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      // 建案頁面的邏輯保持不變\r\n      if (this.getListRequirementRequest.CBuildCaseID && this.getListRequirementRequest.CBuildCaseID != 0) {\r\n        this.currentBuildCase = this.getListRequirementRequest.CBuildCaseID;\r\n      }\r\n    }\r\n\r\n    this.requirementService.apiRequirementGetListPost$Json({ body: this.getListRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.requirementList = res.Entries;\r\n            this.totalRecords = res.TotalItems!;\r\n          }\r\n        }\r\n      })\r\n  } getData() {\r\n    this.requirementService.apiRequirementGetDataPost$Json({ body: this.getRequirementRequest })\r\n      .pipe()\r\n      .subscribe(res => {\r\n        if (res.StatusCode == 0) {\r\n          if (res.Entries) {\r\n            this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n            this.saveRequirement.CBuildCaseID = res.Entries.CBuildCaseID;\r\n            this.saveRequirement.CGroupName = res.Entries.CGroupName;\r\n            this.saveRequirement.CHouseType = res.Entries.CHouseType ? (Array.isArray(res.Entries.CHouseType) ? res.Entries.CHouseType : [res.Entries.CHouseType]) : [];\r\n            this.saveRequirement.CRemark = res.Entries.CRemark;\r\n            this.saveRequirement.CRequirement = res.Entries.CRequirement;\r\n            this.saveRequirement.CRequirementID = res.Entries.CRequirementID;\r\n            this.saveRequirement.CSort = res.Entries.CSort;\r\n            this.saveRequirement.CStatus = res.Entries.CStatus;\r\n            this.saveRequirement.CUnitPrice = res.Entries.CUnitPrice;\r\n            this.saveRequirement.CUnit = res.Entries.CUnit;\r\n            // TODO: 等後端API更新後啟用這行\r\n            this.saveRequirement.CIsShow = (res.Entries as any).CIsShow || false;\r\n          }\r\n        }\r\n      })\r\n  }\r\n\r\n  onHouseTypeChange(value: number, checked: any) {\r\n    console.log(checked);\r\n\r\n    if (checked) {\r\n      if (!this.saveRequirement.CHouseType?.includes(value)) {\r\n        this.saveRequirement.CHouseType?.push(value);\r\n      }\r\n      console.log(this.saveRequirement.CHouseType);\r\n    } else {\r\n      this.saveRequirement.CHouseType = this.saveRequirement.CHouseType?.filter(v => v !== value);\r\n    }\r\n  }\r\n\r\n  getCIsShowText(data: any): string {\r\n    return data.CIsShow ? '是' : '否';\r\n  }\r\n\r\n  // Tab 切換事件處理\r\n  private isFirstTabChange = true;\r\n  onTabChange(event: any) {\r\n    // 避免頁面初始化時自動觸發重複查詢\r\n    if (this.isFirstTabChange) {\r\n      this.isFirstTabChange = false;\r\n      return;\r\n    }\r\n    // 根據 tabTitle 來判斷當前頁面\r\n    if (event.tabTitle === '共用') {\r\n      this.currentTab = 1;\r\n      this.getListRequirementRequest.CBuildCaseID = 0;\r\n    } else {\r\n      this.currentTab = 0;\r\n      // 切換回建案頁面時，如果有建案資料，預設選擇第一個\r\n      if (this.buildCaseList && this.buildCaseList.length > 0) {\r\n        this.getListRequirementRequest.CBuildCaseID = this.buildCaseList[0].cID;\r\n      }\r\n    }\r\n    this.getList();\r\n  }\r\n\r\n  // 新增模板\r\n  addTemplate(dialog: TemplateRef<any>) {\r\n    this.isNew = true;\r\n    this.saveRequirement = { CHouseType: [], CIsShow: false };\r\n    this.saveRequirement.CStatus = 1;\r\n    this.saveRequirement.CUnitPrice = 0;\r\n    // 模板設定建案ID為0\r\n    this.saveRequirement.CBuildCaseID = 0;\r\n    this.dialogService.open(dialog);\r\n  }\r\n\r\n  // 編輯模板\r\n  async onEditTemplate(data: GetRequirement, dialog: TemplateRef<any>) {\r\n    this.getRequirementRequest.CRequirementID = data.CRequirementID!;\r\n    this.isNew = false;\r\n    try {\r\n      await this.getData();\r\n      this.dialogService.open(dialog);\r\n    } catch (error) {\r\n      console.log(\"Failed to get template data\", error);\r\n    }\r\n  }\r\n\r\n  // 保存模板\r\n  saveTemplate(ref: any) {\r\n    // 模板驗證（不包含建案名稱）\r\n    this.valid.clear();\r\n    this.valid.required('[需求]', this.saveRequirement.CRequirement);\r\n    this.valid.required('[地主戶]', this.saveRequirement.CHouseType);\r\n    this.valid.required('[安排]', this.saveRequirement.CSort);\r\n    this.valid.required('[狀態]', this.saveRequirement.CStatus);\r\n    this.valid.required('[單價]', this.saveRequirement.CUnitPrice);\r\n    this.valid.required('[單位]', this.saveRequirement.CUnit);\r\n\r\n    // 群組名稱長度驗證\r\n    if (this.saveRequirement.CGroupName && this.saveRequirement.CGroupName.length > 20) {\r\n      this.valid.errorMessages.push('[群組名稱] 不能超過20個字');\r\n    }\r\n\r\n    // 備註說明長度驗證\r\n    if (this.saveRequirement.CRemark && this.saveRequirement.CRemark.length > 100) {\r\n      this.valid.errorMessages.push('[備註說明] 不能超過100個字');\r\n    }\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 確保模板建案ID為0\r\n    const templateData = { ...this.saveRequirement };\r\n    templateData.CBuildCaseID = 0;\r\n\r\n    this.requirementService.apiRequirementSaveDataPost$Json({\r\n      body: templateData\r\n    }).subscribe(res => {\r\n      if (res.StatusCode === 0) {\r\n        this.message.showSucessMSG('執行成功');\r\n        this.getList();\r\n      } else {\r\n        this.message.showErrorMSG(res.Message!)\r\n      }\r\n    });\r\n    ref.close();\r\n  }\r\n\r\n  openTemplateViewer(templateViewerDialog: TemplateRef<any>) {\r\n    this.dialogService.open(templateViewerDialog);\r\n  }\r\n  onAddTemplate() {\r\n    // 重置新增模板表單\r\n    this.newTemplate = {\r\n      TemplateName: '',\r\n      Description: ''\r\n    };\r\n\r\n    // 打開新增模板對話框\r\n    this.dialogService.open(this.addTemplateDialog);\r\n  }\r\n\r\n  // 儲存新模板\r\n  saveNewTemplate(ref: any) {\r\n    // 驗證模板名稱\r\n    if (!this.newTemplate.TemplateName || this.newTemplate.TemplateName.trim() === '') {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return;\r\n    }\r\n\r\n    // 檢查模板名稱是否重複\r\n    const existingTemplate = this.templateList.find(t =>\r\n      t.TemplateName.toLowerCase() === this.newTemplate.TemplateName.trim().toLowerCase()\r\n    );\r\n\r\n    if (existingTemplate) {\r\n      this.message.showErrorMSG('模板名稱已存在，請使用其他名稱');\r\n      return;\r\n    }\r\n\r\n    // 生成新的模板ID\r\n    const newId = Math.max(...this.templateList.map(t => t.TemplateID || 0), 0) + 1;\r\n\r\n    // 創建新模板\r\n    const template: Template = {\r\n      TemplateID: newId,\r\n      TemplateName: this.newTemplate.TemplateName.trim(),\r\n      Description: this.newTemplate.Description?.trim() || ''\r\n    };\r\n\r\n    // 添加到模板列表\r\n    this.templateList.push(template);\r\n\r\n    // 顯示成功消息並關閉對話框\r\n    this.message.showSucessMSG(`模板 \"${template.TemplateName}\" 已成功創建！`);\r\n    ref.close();\r\n  }\r\n  onSelectTemplate(tpl: Template) {\r\n    // 查看模板邏輯\r\n    alert('查看模板: ' + tpl.TemplateName);\r\n  }\r\n\r\n  onDeleteTemplate(templateID: number) {\r\n    this.templateList = this.templateList.filter(t => t.TemplateID !== templateID);\r\n    this.templateDetailList = this.templateDetailList.filter(d => d.TemplateID !== templateID);\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n\r\n  <!-- 共用搜尋區域 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <div class=\"col-12\">\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\" *ngIf=\"currentTab === 0\">\r\n          <label for=\"buildCase\" class=\"label mr-2\">建案</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CBuildCaseID\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of buildCaseList\" [value]=\"case.cID\">\r\n              {{ case.CBuildCaseName }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"requirement\" class=\"label mr-2\">工程項目</label>\r\n          <input type=\"text\" nbInput id=\"requirement\" name=\"requirement\" placeholder=\"工程項目\"\r\n            [(ngModel)]=\"getListRequirementRequest.CRequirement\">\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"groupName\" class=\"label mr-2\">群組類別</label>\r\n          <input type=\"text\" nbInput id=\"groupName\" name=\"groupName\" placeholder=\"群組類別\"\r\n            [(ngModel)]=\"getListRequirementRequest.CGroupName\">\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"houseType\" class=\"label mr-2\">類型</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CHouseType\" class=\"col-9\" multiple>\r\n            <nb-option *ngFor=\"let type of houseType\" [value]=\"type.value\">\r\n              {{ type.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"status\" class=\"label mr-2\">狀態</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CStatus\" class=\"col-9\">\r\n            <nb-option [value]=\"-1\">全部</nb-option>\r\n            <nb-option [value]=\"1\">啟用</nb-option>\r\n            <nb-option [value]=\"0\">停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"form-group col-12 col-md-4\">\r\n          <label for=\"isShow\" class=\"label mr-2\">客變需求顯示</label>\r\n          <nb-select [(ngModel)]=\"getListRequirementRequest.CIsShow\" class=\"col-9\">\r\n            <nb-option [value]=\"null\">全部</nb-option>\r\n            <nb-option [value]=\"true\">是</nb-option>\r\n            <nb-option [value]=\"false\">否</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"row\">\r\n        <div class=\"col-md-6\"></div>\r\n        <div class=\"form-group col-12 col-md-6 text-right\">\r\n          <button class=\"btn btn-secondary mr-2\" (click)=\"resetSearch()\"><i class=\"fas fa-undo mr-1\"></i>重置</button>\r\n          <button class=\"btn btn-info mr-2\" (click)=\"getList()\"><i class=\"fas fa-search mr-1\"></i>查詢</button>\r\n          <button class=\"btn btn-success mr-2\" (click)=\"add(dialog)\" *ngIf=\"isCreate\"><i\r\n              class=\"fas fa-plus mr-1\"></i>新增</button>\r\n          <button class=\"btn btn-primary mr-2\" (click)=\"openTemplateViewer(templateViewerDialog)\"\r\n            *ngIf=\"currentTab === 1\">\r\n            <i class=\"fas fa-eye mr-1\"></i>查看模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n\r\n  <!-- Tab 導航 -->\r\n  <nb-card-body class=\"bg-white pb-0\">\r\n    <nb-tabset (changeTab)=\"onTabChange($event)\">\r\n      <nb-tab tabTitle=\"建案\">\r\n        <div class=\"pt-3\">\r\n          <!-- 建案列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">建案名稱</th>\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-1\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-2\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CBuildCaseName }}</td>\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-1\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-2\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n\r\n      <nb-tab tabTitle=\"共用\">\r\n        <div class=\"pt-3\">\r\n          <!-- 模板列表 -->\r\n          <div class=\"col-12 mt-3\">\r\n            <div class=\"table-responsive\">\r\n              <table class=\"table table-striped border \" style=\"min-width: 800px; background-color:#f3f3f3;\">\r\n                <thead>\r\n                  <tr style=\"background-color: #27ae60;\" class=\"d-flex text-white\">\r\n                    <th scope=\"col\" class=\"col-2\">工程項目</th>\r\n                    <th scope=\"col\" class=\"col-2\">群組類別</th>\r\n                    <th scope=\"col\" class=\"col-1\">類型</th>\r\n                    <th scope=\"col\" class=\"col-1\">排序</th>\r\n                    <th scope=\"col\" class=\"col-1\">狀態</th>\r\n                    <th scope=\"col\" class=\"col-1\">客變需求顯示</th>\r\n                    <th scope=\"col\" class=\"col-1\">單價</th>\r\n                    <th scope=\"col\" class=\"col-3\">操作功能</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let data of requirementList; let i = index\" class=\"d-flex\">\r\n                    <td class=\"col-2\">{{ data.CRequirement }}</td>\r\n                    <td class=\"col-2\">{{ data.CGroupName }}</td>\r\n                    <td class=\"col-1\">{{ getHouseType(data.CHouseType || []) }}</td>\r\n                    <td class=\"col-1\">{{ data.CSort }}</td>\r\n                    <td class=\"col-1\">{{ data.CStatus | getStatusName }}</td>\r\n                    <td class=\"col-1\">{{ getCIsShowText(data) }}</td>\r\n                    <td class=\"col-1\">{{ (data.CUnitPrice || 0) | ngxNumberWithCommas }}</td>\r\n                    <td class=\"col-3\">\r\n                      <button *ngIf=\"isUpdate\" type=\"button\" class=\"btn btn-outline-success m-1  btn-sm\"\r\n                        (click)=\"onEdit(data,dialog)\"><i class=\"fas fa-edit mr-1\"></i>編輯</button>\r\n                      <button *ngIf=\"isDelete\" type=\"button\" class=\"btn btn-outline-danger m-1  btn-sm\"\r\n                        (click)=\"onDelete(data)\"><i class=\"far fa-trash-alt mr-1\"></i>刪除</button>\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n            <ngx-pagination [CollectionSize]=\"totalRecords\" [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\"\r\n              (PageChange)=\"getList()\">\r\n            </ngx-pagination>\r\n          </div>\r\n        </div>\r\n      </nb-tab>\r\n    </nb-tabset>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 建案對話框 -->\r\n<ng-template #dialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true && currentTab === 0\">新增建案需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 0\">編輯建案需求</span>\r\n      <span *ngIf=\"isNew===true && currentTab === 1\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false && currentTab === 1\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'建案名稱'\" [labelFor]=\"'CBuildCaseID'\" [isRequired]=\"true\" *ngIf=\"currentTab === 0\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CBuildCaseID\" name=\"CBuildCaseID\"\r\n                [(selected)]=\"saveRequirement.CBuildCaseID\">\r\n                <nb-option langg *ngFor=\"let b of buildCaseList\" [value]=\"b.cID\"> {{b.CBuildCaseName}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"save(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 模板對話框 -->\r\n<ng-template #templateDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"height: 100%; overflow: auto; max-width: 500px;\" class=\"mr-md-5 ml-md-5\">\r\n    <nb-card-header>\r\n      <span *ngIf=\"isNew===true\">新增模板需求</span>\r\n      <span *ngIf=\"isNew===false\">編輯模板需求</span>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"padding:1rem 2rem\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12 col-md-12\">\r\n          <div class=\"row\">\r\n            <app-form-group [label]=\"'工程項目'\" [labelFor]=\"'CRequirement'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CRequirement\" name=\"CRequirement\" placeholder=\"工程項目\"\r\n                [(ngModel)]=\"saveRequirement.CRequirement\" maxlength=\"50\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'群組類別'\" [labelFor]=\"'CGroupName'\" [isRequired]=\"false\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CGroupName\" name=\"CGroupName\" placeholder=\"群組類別\"\r\n                [(ngModel)]=\"saveRequirement.CGroupName\" maxlength=\"20\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'排序'\" [labelFor]=\"'CSort'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CSort\" name=\"CSort\" placeholder=\"排序\"\r\n                [(ngModel)]=\"saveRequirement.CSort\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'類型'\" [labelFor]=\"'CHouseType'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CHouseType\" name=\"CHouseType\"\r\n                [(selected)]=\"saveRequirement.CHouseType\" multiple>\r\n                <nb-option langg *ngFor=\"let type of houseType\" [value]=\"type.value\"> {{type.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'狀態'\" [labelFor]=\"'CStatus'\" [isRequired]=\"true\">\r\n              <nb-select class=\"flex-grow-1\" placeholder=\"請選擇\" id=\"CStatus\" name=\"CStatus\"\r\n                [(selected)]=\"saveRequirement.CStatus\">\r\n                <nb-option langg *ngFor=\"let status of statusOptions\" [value]=\"status.value\">\r\n                  {{status.label}}</nb-option>\r\n              </nb-select>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單價'\" [labelFor]=\"'CUnitPrice'\" [isRequired]=\"true\">\r\n              <input type=\"number\" nbInput class=\"flex-grow-1\" id=\"CUnitPrice\" name=\"CUnitPrice\" placeholder=\"單價\"\r\n                [(ngModel)]=\"saveRequirement.CUnitPrice\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'單位'\" [labelFor]=\"'CUnit'\" [isRequired]=\"true\">\r\n              <input type=\"text\" nbInput class=\"flex-grow-1\" id=\"CUnit\" name=\"CUnit\" placeholder=\"單位\"\r\n                [(ngModel)]=\"saveRequirement.CUnit\">\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'客變需求顯示'\" [labelFor]=\"'CIsShow'\" [isRequired]=\"false\">\r\n              <nb-checkbox id=\"CIsShow\" name=\"CIsShow\" [(ngModel)]=\"saveRequirement.CIsShow\" class=\"flex-grow-1\">\r\n                顯示在客變需求\r\n              </nb-checkbox>\r\n            </app-form-group>\r\n            <app-form-group [label]=\"'備註說明'\" [labelFor]=\"'CRemark'\" [isRequired]=\"false\">\r\n              <textarea nbInput class=\"flex-grow-1\" id=\"CRemark\" name=\"CRemark\" placeholder=\"備註說明\"\r\n                [(ngModel)]=\"saveRequirement.CRemark\" maxlength=\"100\" rows=\"3\"></textarea>\r\n            </app-form-group>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"row\">\r\n        <div class=\"col-12 text-center\">\r\n          <button class=\"btn btn-success mr-2\" (click)=\"saveTemplate(ref)\">確定</button>\r\n          <button class=\"btn btn-danger mr-2\" (click)=\"ref.close()\">取消</button>\r\n        </div>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 共用模板查看元件 Dialog -->\r\n<ng-template #templateViewerDialog let-data let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 90vw; max-width: 1200px; height: 80vh;\">\r\n    <nb-card-header>\r\n      <h5>模板管理</h5>\r\n    </nb-card-header>\r\n    <nb-card-body style=\"overflow: auto;\">\r\n      <app-template-viewer [templates]=\"templateList\" [templateDetails]=\"templateDetailList\"\r\n        (addTemplate)=\"onAddTemplate()\" (deleteTemplate)=\"onDeleteTemplate($event)\"\r\n        (selectTemplate)=\"onSelectTemplate($event)\">\r\n      </app-template-viewer>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"text-center\">\r\n        <button class=\"btn btn-secondary\" (click)=\"ref.close()\">關閉</button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<!-- 新增模板對話框 -->\r\n<ng-template #addTemplateDialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 500px;\">\r\n    <nb-card-header>\r\n      <h5><i class=\"fas fa-plus mr-2\"></i>新增模板</h5>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <form #addTemplateForm=\"ngForm\" (ngSubmit)=\"saveNewTemplate(ref)\">\r\n        <div class=\"form-group mb-3\">\r\n          <label for=\"templateName\" class=\"form-label\">\r\n            <strong>模板名稱 <span class=\"text-danger\">*</span></strong>\r\n          </label>\r\n          <input type=\"text\" id=\"templateName\" class=\"form-control\" [(ngModel)]=\"newTemplate.TemplateName\"\r\n            name=\"templateName\" placeholder=\"請輸入模板名稱\" required maxlength=\"50\">\r\n          <small class=\"form-text text-muted\">最多50個字元</small>\r\n        </div>\r\n\r\n        <div class=\"form-group mb-3\">\r\n          <label for=\"templateDescription\" class=\"form-label\">\r\n            <strong>模板描述</strong>\r\n          </label>\r\n          <textarea id=\"templateDescription\" class=\"form-control\" [(ngModel)]=\"newTemplate.Description\"\r\n            name=\"templateDescription\" placeholder=\"請輸入模板描述（可選）\" rows=\"3\" maxlength=\"200\"></textarea>\r\n          <small class=\"form-text text-muted\">最多200個字元</small>\r\n        </div>\r\n      </form>\r\n    </nb-card-body>\r\n    <nb-card-footer>\r\n      <div class=\"d-flex justify-content-end\">\r\n        <button class=\"btn btn-secondary mr-2\" (click)=\"ref.close()\">\r\n          <i class=\"fas fa-times mr-1\"></i>取消\r\n        </button>\r\n        <button class=\"btn btn-success\" (click)=\"saveNewTemplate(ref)\"\r\n          [disabled]=\"!newTemplate.TemplateName || newTemplate.TemplateName.trim() === ''\">\r\n          <i class=\"fas fa-save mr-1\"></i>儲存\r\n        </button>\r\n      </div>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": ";AAEA,SAASA,YAAY,EAAEC,gBAAgB,EAAmBC,aAAa,EAAEC,cAAc,EAAEC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAO/I,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAC7C,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,2DAA2D;AAC9F,SAASC,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAAmCC,uBAAuB,QAAQ,qEAAqE;;;;;;;;;;;;;;ICR3HC,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAC9DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,cAAA,MACF;;;;;;IAJFT,EADF,CAAAC,cAAA,cAAiE,gBACrB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpDH,EAAA,CAAAC,cAAA,oBAA8E;IAAnED,EAAA,CAAAU,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAG,yBAAA,CAAAC,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAAG,yBAAA,CAAAC,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAoD;IAC7DZ,EAAA,CAAAqB,UAAA,IAAAC,yDAAA,wBAAiE;IAIrEtB,EADE,CAAAG,YAAA,EAAY,EACR;;;;IALOH,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAG,yBAAA,CAAAC,YAAA,CAAoD;IACjCnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAoB5CxB,EAAA,CAAAC,cAAA,oBAA+D;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAqB,OAAA,CAAAC,KAAA,CAAoB;IAC5D1B,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAiB,OAAA,CAAAE,KAAA,MACF;;;;;;IAyBF3B,EAAA,CAAAC,cAAA,iBAA4E;IAAvCD,EAAA,CAAA4B,UAAA,mBAAAC,0EAAA;MAAA7B,EAAA,CAAAa,aAAA,CAAAiB,GAAA;MAAA,MAAAf,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAkB,GAAA,CAAAF,SAAA,CAAW;IAAA,EAAC;IAAkB/B,EAAA,CAAAkC,SAAA,YAC3C;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC5CH,EAAA,CAAAC,cAAA,iBAC2B;IADUD,EAAA,CAAA4B,UAAA,mBAAAO,0EAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAqB,uBAAA,GAAArC,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAuB,kBAAA,CAAAD,uBAAA,CAAwC;IAAA,EAAC;IAErFrC,EAAA,CAAAkC,SAAA,YAA+B;IAAAlC,EAAA,CAAAE,MAAA,gCACjC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAuCGH,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAAW,gFAAA;MAAAvC,EAAA,CAAAa,aAAA,CAAA2B,IAAA;MAAA,MAAAC,QAAA,GAAAzC,EAAA,CAAAgB,aAAA,GAAA0B,SAAA;MAAA,MAAA3B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA4B,MAAA,CAAAF,QAAA,EAAAV,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAgB,gFAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,IAAA;MAAA,MAAAJ,QAAA,GAAAzC,EAAA,CAAAgB,aAAA,GAAA0B,SAAA;MAAA,MAAA3B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA+B,QAAA,CAAAL,QAAA,CAAc;IAAA,EAAC;IAACzC,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAZ7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAA0B,uDAAA,qBACgC,KAAAC,uDAAA,qBAEL;IAE/BhD,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAdeH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAiD,iBAAA,CAAAR,QAAA,CAAAhC,cAAA,CAAyB;IACzBT,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAiD,iBAAA,CAAAR,QAAA,CAAAS,YAAA,CAAuB;IACvBlD,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAiD,iBAAA,CAAAR,QAAA,CAAAU,UAAA,CAAqB;IACrBnD,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAiD,iBAAA,CAAAlC,MAAA,CAAAqC,YAAA,CAAAX,QAAA,CAAAY,UAAA,IAAArD,EAAA,CAAAsD,eAAA,KAAAC,GAAA,GAAyC;IACzCvD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAiD,iBAAA,CAAAR,QAAA,CAAAe,KAAA,CAAgB;IAChBxD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAAyD,WAAA,SAAAhB,QAAA,CAAAiB,OAAA,EAAkC;IAClC1D,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAiD,iBAAA,CAAAlC,MAAA,CAAA4C,cAAA,CAAAlB,QAAA,EAA0B;IAC1BzC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAAyD,WAAA,SAAAhB,QAAA,CAAAmB,UAAA,OAAkD;IAEzD5D,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8C,QAAA,CAAc;IAEd7D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA+C,QAAA,CAAc;;;;;;IA0CvB9D,EAAA,CAAAC,cAAA,iBACgC;IAA9BD,EAAA,CAAA4B,UAAA,mBAAAmC,iFAAA;MAAA/D,EAAA,CAAAa,aAAA,CAAAmD,IAAA;MAAA,MAAAC,QAAA,GAAAjE,EAAA,CAAAgB,aAAA,GAAA0B,SAAA;MAAA,MAAA3B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAe,SAAA,GAAA/B,EAAA,CAAAgC,WAAA;MAAA,OAAAhC,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA4B,MAAA,CAAAsB,QAAA,EAAAlC,SAAA,CAAmB;IAAA,EAAC;IAAC/B,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAC3EH,EAAA,CAAAC,cAAA,iBAC2B;IAAzBD,EAAA,CAAA4B,UAAA,mBAAAsC,iFAAA;MAAAlE,EAAA,CAAAa,aAAA,CAAAsD,IAAA;MAAA,MAAAF,QAAA,GAAAjE,EAAA,CAAAgB,aAAA,GAAA0B,SAAA;MAAA,MAAA3B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA+B,QAAA,CAAAmB,QAAA,CAAc;IAAA,EAAC;IAACjE,EAAA,CAAAkC,SAAA,YAAqC;IAAAlC,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAX7EH,EADF,CAAAC,cAAA,aAAuE,aACnD;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,aAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAkD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzEH,EAAA,CAAAC,cAAA,cAAkB;IAGhBD,EAFA,CAAAqB,UAAA,KAAA+C,wDAAA,qBACgC,KAAAC,wDAAA,qBAEL;IAE/BrE,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAbeH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAiD,iBAAA,CAAAgB,QAAA,CAAAf,YAAA,CAAuB;IACvBlD,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAiD,iBAAA,CAAAgB,QAAA,CAAAd,UAAA,CAAqB;IACrBnD,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAiD,iBAAA,CAAAlC,MAAA,CAAAqC,YAAA,CAAAa,QAAA,CAAAZ,UAAA,IAAArD,EAAA,CAAAsD,eAAA,KAAAC,GAAA,GAAyC;IACzCvD,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAiD,iBAAA,CAAAgB,QAAA,CAAAT,KAAA,CAAgB;IAChBxD,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAAyD,WAAA,QAAAQ,QAAA,CAAAP,OAAA,EAAkC;IAClC1D,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAiD,iBAAA,CAAAlC,MAAA,CAAA4C,cAAA,CAAAM,QAAA,EAA0B;IAC1BjE,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAAyD,WAAA,SAAAQ,QAAA,CAAAL,UAAA,OAAkD;IAEzD5D,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8C,QAAA,CAAc;IAEd7D,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA+C,QAAA,CAAc;;;;;IAqBvC9D,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7DH,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5DH,EAAA,CAAAC,cAAA,WAAgD;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IASnDH,EAAA,CAAAC,cAAA,oBAAiE;IAACD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAjDH,EAAA,CAAAI,UAAA,UAAAkE,KAAA,CAAAhE,GAAA,CAAe;IAAEN,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAQ,kBAAA,MAAA8D,KAAA,CAAA7D,cAAA,KAAoB;;;;;;IAFxFT,EADF,CAAAC,cAAA,yBAA0G,oBAE1D;IAA5CD,EAAA,CAAAU,gBAAA,4BAAA6D,8GAAA3D,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA2D,IAAA;MAAA,MAAAzD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAtD,YAAA,EAAAP,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAtD,YAAA,GAAAP,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA2C;IAC3CZ,EAAA,CAAAqB,UAAA,IAAAqD,qFAAA,wBAAiE;IAErE1E,EADE,CAAAG,YAAA,EAAY,EACG;;;;IAL4CH,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA2C;IAA3CP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAA0D,eAAA,CAAAtD,YAAA,CAA2C;IACZnB,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAS,aAAA,CAAgB;;;;;IAkB/CxB,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAuE,QAAA,CAAAjD,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAmE,QAAA,CAAAhD,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAwE,UAAA,CAAAlD,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAoE,UAAA,CAAAjD,KAAA,KAAgB;;;;;;IAtC9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAIdD,EAHA,CAAAqB,UAAA,IAAAwD,8DAAA,mBAA+C,IAAAC,8DAAA,mBACC,IAAAC,8DAAA,mBACD,IAAAC,8DAAA,mBACC;IAClDhF,EAAA,CAAAG,YAAA,EAAiB;IAIXH,EAHN,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX;IACfD,EAAA,CAAAqB,UAAA,KAAA4D,yEAAA,6BAA0G;IAOxGjF,EADF,CAAAC,cAAA,0BAAiF,iBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAwE,wFAAAtE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,EAAAtC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,GAAAtC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAA0E,wFAAAxE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,EAAAvC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,GAAAvC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA2E,wFAAAzE,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,GAAA5C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAU,gBAAA,4BAAA4E,6FAAA1E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAApB,UAAA,EAAAzC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAApB,UAAA,GAAAzC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAAkE,oEAAA,wBAAqE;IAEzEvF,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAA8E,6FAAA5E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAf,OAAA,EAAA9C,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAf,OAAA,GAAA9C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAAoE,oEAAA,wBAA6E;IAGjFzF,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAAgF,wFAAA9E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAb,UAAA,EAAAhD,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAb,UAAA,GAAAhD,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAiF,wFAAA/E,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,EAAAhF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,GAAAhF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAmF,8FAAAjF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,EAAAlF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,GAAAlF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAAqF,2FAAAnF,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,EAAApF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,GAAApF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBAC2B;IAApBD,EAAA,CAAA4B,UAAA,mBAAAqE,iFAAA;MAAA,MAAAC,OAAA,GAAAlG,EAAA,CAAAa,aAAA,CAAAsE,IAAA,EAAAgB,SAAA;MAAA,MAAApF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAAqF,IAAA,CAAAF,OAAA,CAAS;IAAA,EAAC;IAAClG,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAAyE,iFAAA;MAAA,MAAAH,OAAA,GAAAlG,EAAA,CAAAa,aAAA,CAAAsE,IAAA,EAAAgB,SAAA;MAAA,OAAAnG,EAAA,CAAAoB,WAAA,CAAS8E,OAAA,CAAAI,KAAA,EAAW;IAAA,EAAC;IAACtG,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IArECH,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,aAAAxF,MAAA,CAAAyF,UAAA,OAAsC;IACtCxG,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,cAAAxF,MAAA,CAAAyF,UAAA,OAAuC;IACvCxG,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,aAAAxF,MAAA,CAAAyF,UAAA,OAAsC;IACtCxG,EAAA,CAAAO,SAAA,EAAuC;IAAvCP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,cAAAxF,MAAA,CAAAyF,UAAA,OAAuC;IAM0CxG,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAyF,UAAA,OAAsB;IAMxFxG,EAAA,CAAAO,SAAA,EAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,CAA0C;IAE9BlD,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,CAAwC;IAE5BnD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,CAAmC;IAEvBxD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAA0D,eAAA,CAAApB,UAAA,CAAyC;IACPrD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA0F,SAAA,CAAY;IAGlCzG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAA0D,eAAA,CAAAf,OAAA,CAAsC;IACF1D,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA2F,aAAA,CAAgB;IAIxC1G,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAb,UAAA,CAAwC;IAE5B5D,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,CAAmC;IAEvB5F,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,CAAqC;IAIhE9F,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,CAAqC;;;;;IAqB/ChG,EAAA,CAAAC,cAAA,WAA2B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAqB/BH,EAAA,CAAAC,cAAA,oBAAqE;IAACD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAAhDH,EAAA,CAAAI,UAAA,UAAAuG,QAAA,CAAAjF,KAAA,CAAoB;IAAE1B,EAAA,CAAAO,SAAA,EAAc;IAAdP,EAAA,CAAAQ,kBAAA,MAAAmG,QAAA,CAAAhF,KAAA,KAAc;;;;;IAMpF3B,EAAA,CAAAC,cAAA,oBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADwBH,EAAA,CAAAI,UAAA,UAAAwG,UAAA,CAAAlF,KAAA,CAAsB;IAC1E1B,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,kBAAA,MAAAoG,UAAA,CAAAjF,KAAA,KAAgB;;;;;;IA9B9B3B,EADF,CAAAC,cAAA,kBAAyF,qBACvE;IAEdD,EADA,CAAAqB,UAAA,IAAAwF,8DAAA,mBAA2B,IAAAC,8DAAA,mBACC;IAC9B9G,EAAA,CAAAG,YAAA,EAAiB;IAMPH,EALV,CAAAC,cAAA,uBAAwC,aACrB,cACe,aACX,yBACkE,gBAEnB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAAqG,uFAAAnG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,EAAAtC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,GAAAtC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAA0C;IAC9CZ,EAFE,CAAAG,YAAA,EAC4D,EAC7C;IAEfH,EADF,CAAAC,cAAA,0BAAgF,iBAEpB;IAAxDD,EAAA,CAAAU,gBAAA,2BAAAuG,wFAAArG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,EAAAvC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,GAAAvC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC0D,EAC3C;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAAwG,wFAAAtG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,GAAA5C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA6E,qBAEtB;IAAnDD,EAAA,CAAAU,gBAAA,4BAAAyG,6FAAAvG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAApB,UAAA,EAAAzC,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAApB,UAAA,GAAAzC,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAyC;IACzCZ,EAAA,CAAAqB,UAAA,KAAA+F,oEAAA,wBAAqE;IAEzEpH,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA0E,qBAE/B;IAAvCD,EAAA,CAAAU,gBAAA,4BAAA2G,6FAAAzG,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAf,OAAA,EAAA9C,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAf,OAAA,GAAA9C,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IACtCZ,EAAA,CAAAqB,UAAA,KAAAiG,oEAAA,wBAA6E;IAGjFtH,EADE,CAAAG,YAAA,EAAY,EACG;IAEfH,EADF,CAAAC,cAAA,0BAA6E,iBAEhC;IAAzCD,EAAA,CAAAU,gBAAA,2BAAA6G,wFAAA3G,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAb,UAAA,EAAAhD,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAb,UAAA,GAAAhD,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAwC;IAC5CZ,EAFE,CAAAG,YAAA,EAC2C,EAC5B;IAEfH,EADF,CAAAC,cAAA,0BAAwE,iBAEhC;IAApCD,EAAA,CAAAU,gBAAA,2BAAA8G,wFAAA5G,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,EAAAhF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,GAAAhF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAmC;IACvCZ,EAFE,CAAAG,YAAA,EACsC,EACvB;IAEfH,EADF,CAAAC,cAAA,0BAA+E,uBACsB;IAA1DD,EAAA,CAAAU,gBAAA,2BAAA+G,8FAAA7G,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,EAAAlF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,GAAAlF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAC5EZ,EAAA,CAAAE,MAAA,oDACF;IACFF,EADE,CAAAG,YAAA,EAAc,EACC;IAEfH,EADF,CAAAC,cAAA,0BAA6E,oBAEV;IAA/DD,EAAA,CAAAU,gBAAA,2BAAAgH,2FAAA9G,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,EAAApF,MAAA,MAAAG,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,GAAApF,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAKjDZ,EAL2E,CAAAG,YAAA,EAAW,EAC7D,EACb,EACF,EACF,EACO;IAITH,EAHN,CAAAC,cAAA,sBAAgB,cACG,eACiB,kBACmC;IAA5BD,EAAA,CAAA4B,UAAA,mBAAA+F,iFAAA;MAAA,MAAAC,OAAA,GAAA5H,EAAA,CAAAa,aAAA,CAAAmG,IAAA,EAAAb,SAAA;MAAA,MAAApF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA8G,YAAA,CAAAD,OAAA,CAAiB;IAAA,EAAC;IAAC5H,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,kBAA0D;IAAtBD,EAAA,CAAA4B,UAAA,mBAAAkG,iFAAA;MAAA,MAAAF,OAAA,GAAA5H,EAAA,CAAAa,aAAA,CAAAmG,IAAA,EAAAb,SAAA;MAAA,OAAAnG,EAAA,CAAAoB,WAAA,CAASwG,OAAA,CAAAtB,KAAA,EAAW;IAAA,EAAC;IAACtG,EAAA,CAAAE,MAAA,oBAAE;IAIpEF,EAJoE,CAAAG,YAAA,EAAS,EACjE,EACF,EACS,EACT;;;;IA7DCH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,UAAkB;IAClBvG,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAwF,KAAA,WAAmB;IAMJvG,EAAA,CAAAO,SAAA,GAAgB;IAA6BP,EAA7C,CAAAI,UAAA,qCAAgB,4BAA4B,oBAAoB;IAE5EJ,EAAA,CAAAO,SAAA,EAA0C;IAA1CP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAvB,YAAA,CAA0C;IAE9BlD,EAAA,CAAAO,SAAA,EAAgB;IAA2BP,EAA3C,CAAAI,UAAA,qCAAgB,0BAA0B,qBAAqB;IAE3EJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAtB,UAAA,CAAwC;IAE5BnD,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAjB,KAAA,CAAmC;IAEvBxD,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAyC;IAAzCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAA0D,eAAA,CAAApB,UAAA,CAAyC;IACPrD,EAAA,CAAAO,SAAA,EAAY;IAAZP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA0F,SAAA,CAAY;IAGlCzG,EAAA,CAAAO,SAAA,EAAc;IAAwBP,EAAtC,CAAAI,UAAA,yBAAc,uBAAuB,oBAAoB;IAErEJ,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,aAAAR,MAAA,CAAA0D,eAAA,CAAAf,OAAA,CAAsC;IACF1D,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA2F,aAAA,CAAgB;IAIxC1G,EAAA,CAAAO,SAAA,EAAc;IAA2BP,EAAzC,CAAAI,UAAA,yBAAc,0BAA0B,oBAAoB;IAExEJ,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAb,UAAA,CAAwC;IAE5B5D,EAAA,CAAAO,SAAA,EAAc;IAAsBP,EAApC,CAAAI,UAAA,yBAAc,qBAAqB,oBAAoB;IAEnEJ,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAmB,KAAA,CAAmC;IAEvB5F,EAAA,CAAAO,SAAA,EAAkB;IAAwBP,EAA1C,CAAAI,UAAA,iDAAkB,uBAAuB,qBAAqB;IACnCJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAqB,OAAA,CAAqC;IAIhE9F,EAAA,CAAAO,SAAA,GAAgB;IAAwBP,EAAxC,CAAAI,UAAA,qCAAgB,uBAAuB,qBAAqB;IAExEJ,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAA0D,eAAA,CAAAuB,OAAA,CAAqC;;;;;;IAqB/ChG,EAFJ,CAAAC,cAAA,kBAA+D,qBAC7C,SACV;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACVF,EADU,CAAAG,YAAA,EAAK,EACE;IAEfH,EADF,CAAAC,cAAA,uBAAsC,8BAGU;IAA5CD,EADA,CAAA4B,UAAA,yBAAAmG,mGAAA;MAAA/H,EAAA,CAAAa,aAAA,CAAAmH,IAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAeL,MAAA,CAAAkH,aAAA,EAAe;IAAA,EAAC,4BAAAC,sGAAAtH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmH,IAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAmBL,MAAA,CAAAoH,gBAAA,CAAAvH,MAAA,CAAwB;IAAA,EAAC,4BAAAwH,sGAAAxH,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAmH,IAAA;MAAA,MAAAjH,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CACzDL,MAAA,CAAAsH,gBAAA,CAAAzH,MAAA,CAAwB;IAAA,EAAC;IAE/CZ,EADE,CAAAG,YAAA,EAAsB,EACT;IAGXH,EAFJ,CAAAC,cAAA,qBAAgB,cACW,iBACiC;IAAtBD,EAAA,CAAA4B,UAAA,mBAAA0G,gFAAA;MAAA,MAAAC,OAAA,GAAAvI,EAAA,CAAAa,aAAA,CAAAmH,IAAA,EAAA7B,SAAA;MAAA,OAAAnG,EAAA,CAAAoB,WAAA,CAASmH,OAAA,CAAAjC,KAAA,EAAW;IAAA,EAAC;IAACtG,EAAA,CAAAE,MAAA,mBAAE;IAGhEF,EAHgE,CAAAG,YAAA,EAAS,EAC/D,EACS,EACT;;;;IAVeH,EAAA,CAAAO,SAAA,GAA0B;IAACP,EAA3B,CAAAI,UAAA,cAAAW,MAAA,CAAAyH,YAAA,CAA0B,oBAAAzH,MAAA,CAAA0H,kBAAA,CAAuC;;;;;;IAiBtFzI,EAFJ,CAAAC,cAAA,kBAA+B,qBACb,SACV;IAAAD,EAAA,CAAAkC,SAAA,YAAgC;IAAAlC,EAAA,CAAAE,MAAA,+BAAI;IAC1CF,EAD0C,CAAAG,YAAA,EAAK,EAC9B;IAEfH,EADF,CAAAC,cAAA,mBAAc,kBACsD;IAAlCD,EAAA,CAAA4B,UAAA,sBAAA8G,iFAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAAa,aAAA,CAAA+H,IAAA,EAAAzC,SAAA;MAAA,MAAApF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAAYL,MAAA,CAAA8H,eAAA,CAAAF,OAAA,CAAoB;IAAA,EAAC;IAG3D3I,EAFJ,CAAAC,cAAA,cAA6B,gBACkB,cACnC;IAAAD,EAAA,CAAAE,MAAA,iCAAK;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAAS,EAClD;IACRH,EAAA,CAAAC,cAAA,iBACoE;IADVD,EAAA,CAAAU,gBAAA,2BAAAoI,wFAAAlI,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+H,IAAA;MAAA,MAAA7H,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAgI,WAAA,CAAAC,YAAA,EAAApI,MAAA,MAAAG,MAAA,CAAAgI,WAAA,CAAAC,YAAA,GAAApI,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAsC;IAAhGZ,EAAA,CAAAG,YAAA,EACoE;IACpEH,EAAA,CAAAC,cAAA,iBAAoC;IAAAD,EAAA,CAAAE,MAAA,wCAAO;IAC7CF,EAD6C,CAAAG,YAAA,EAAQ,EAC/C;IAIFH,EAFJ,CAAAC,cAAA,eAA6B,iBACyB,cAC1C;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IACdF,EADc,CAAAG,YAAA,EAAS,EACf;IACRH,EAAA,CAAAC,cAAA,oBACgF;IADxBD,EAAA,CAAAU,gBAAA,2BAAAuI,2FAAArI,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAA+H,IAAA;MAAA,MAAA7H,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiB,kBAAA,CAAAF,MAAA,CAAAgI,WAAA,CAAAG,WAAA,EAAAtI,MAAA,MAAAG,MAAA,CAAAgI,WAAA,CAAAG,WAAA,GAAAtI,MAAA;MAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;IAAA,EAAqC;IACbZ,EAAA,CAAAG,YAAA,EAAW;IAC3FH,EAAA,CAAAC,cAAA,iBAAoC;IAAAD,EAAA,CAAAE,MAAA,yCAAQ;IAGlDF,EAHkD,CAAAG,YAAA,EAAQ,EAChD,EACD,EACM;IAGXH,EAFJ,CAAAC,cAAA,sBAAgB,eAC0B,kBACuB;IAAtBD,EAAA,CAAA4B,UAAA,mBAAAuH,iFAAA;MAAA,MAAAR,OAAA,GAAA3I,EAAA,CAAAa,aAAA,CAAA+H,IAAA,EAAAzC,SAAA;MAAA,OAAAnG,EAAA,CAAAoB,WAAA,CAASuH,OAAA,CAAArC,KAAA,EAAW;IAAA,EAAC;IAC1DtG,EAAA,CAAAkC,SAAA,aAAiC;IAAAlC,EAAA,CAAAE,MAAA,qBACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACmF;IADnDD,EAAA,CAAA4B,UAAA,mBAAAwH,iFAAA;MAAA,MAAAT,OAAA,GAAA3I,EAAA,CAAAa,aAAA,CAAA+H,IAAA,EAAAzC,SAAA;MAAA,MAAApF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAoB,WAAA,CAASL,MAAA,CAAA8H,eAAA,CAAAF,OAAA,CAAoB;IAAA,EAAC;IAE5D3I,EAAA,CAAAkC,SAAA,aAAgC;IAAAlC,EAAA,CAAAE,MAAA,qBAClC;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACS,EACT;;;;IA1BwDH,EAAA,CAAAO,SAAA,IAAsC;IAAtCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAgI,WAAA,CAAAC,YAAA,CAAsC;IASxChJ,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAuB,gBAAA,YAAAR,MAAA,CAAAgI,WAAA,CAAAG,WAAA,CAAqC;IAY7FlJ,EAAA,CAAAO,SAAA,GAAgF;IAAhFP,EAAA,CAAAI,UAAA,cAAAW,MAAA,CAAAgI,WAAA,CAAAC,YAAA,IAAAjI,MAAA,CAAAgI,WAAA,CAAAC,YAAA,CAAAK,IAAA,UAAgF;;;AD/T1F,OAAM,MAAOC,8BAA+B,SAAQlK,aAAa;EAC/DmK,YACUC,MAAmB,EACnBC,UAAsB,EACtBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,gBAAkC,EAClCC,kBAAsC,EACtCC,OAAsB,EACtBC,MAAc,EACdC,UAAsB;IAE9B,KAAK,CAACT,MAAM,CAAC;IAXL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IAMpB;IACA,KAAA/I,yBAAyB,GAAG,EAA8D;IAC1F,KAAAgJ,qBAAqB,GAA8B,EAAE;IACrD;IACA,KAAA1I,aAAa,GAA8B,EAAE;IAC7C,KAAA2I,eAAe,GAAqB,EAAE;IACtC,KAAA1F,eAAe,GAAgD;MAAEpB,UAAU,EAAE;IAAE,CAAE;IAEjF,KAAAqD,aAAa,GAAG,CACd;MAAEhF,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,EACzB;MAAED,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC1B;IACD,KAAA8E,SAAS,GAAG,IAAI,CAACgD,UAAU,CAACW,cAAc,CAACtK,aAAa,CAAC;IACzD,KAAAyG,KAAK,GAAG,KAAK;IACb,KAAA8D,gBAAgB,GAAG,CAAC;IACpB,KAAA7D,UAAU,GAAG,CAAC,CAAC,CAAC;IAEhB;IACA,KAAAgC,YAAY,GAAe,CACzB;MAAE8B,UAAU,EAAE,CAAC;MAAEtB,YAAY,EAAE,KAAK;MAAEE,WAAW,EAAE;IAAO,CAAE,EAC5D;MAAEoB,UAAU,EAAE,CAAC;MAAEtB,YAAY,EAAE,KAAK;MAAEE,WAAW,EAAE;IAAO,CAAE,CAC7D;IACD,KAAAT,kBAAkB,GAAqB,CACrC;MAAE8B,gBAAgB,EAAE,CAAC;MAAED,UAAU,EAAE,CAAC;MAAEE,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAEJ,gBAAgB,EAAE,CAAC;MAAED,UAAU,EAAE,CAAC;MAAEE,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,EAC7H;MAAEJ,gBAAgB,EAAE,CAAC;MAAED,UAAU,EAAE,CAAC;MAAEE,KAAK,EAAE,GAAG;MAAEC,UAAU,EAAE,aAAa;MAAEC,SAAS,EAAE,cAAc;MAAEC,UAAU,EAAE;IAAO,CAAE,CAC9H;IAED;IACA,KAAA5B,WAAW,GAAa;MACtBC,YAAY,EAAE,EAAE;MAChBE,WAAW,EAAE;KACd;IA4OD;IACQ,KAAA0B,gBAAgB,GAAG,IAAI;IAhR7B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAmCSC,QAAQA,CAAA,GAAW;EAC5B;EACAF,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAAC3J,yBAAyB,CAACwC,OAAO,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACxC,yBAAyB,CAAC4E,OAAO,GAAG,IAAI;IAC7C,IAAI,CAAC5E,yBAAyB,CAACgC,YAAY,GAAG,EAAE;IAChD,IAAI,CAAChC,yBAAyB,CAACiC,UAAU,GAAG,EAAE;IAC9C;IACA,IAAI,CAACjC,yBAAyB,CAACmC,UAAU,GAAG,IAAI,CAACoD,SAAS,CAACuE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACvJ,KAAK,CAAC;EACpF;EAEA;EACAwJ,WAAWA,CAAA;IACT,IAAI,CAACL,oBAAoB,EAAE;IAC3B;IACA,IAAI,IAAI,CAACrJ,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2J,MAAM,GAAG,CAAC,EAAE;MACvDC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC5E,UAAU,KAAK,CAAC,EAAE;UACzB,IAAI,CAACtF,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;QACzE,CAAC,MAAM;UACL,IAAI,CAACY,yBAAyB,CAACC,YAAY,GAAG,CAAC;QACjD;QACA,IAAI,CAACkK,OAAO,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACA,OAAO,EAAE;IAChB;EACF;EAEAjI,YAAYA,CAACkI,MAA4C;IACvD,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IAEA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;IACnB;IAEA,IAAIG,MAAM,GAAa,EAAE;IACzBH,MAAM,CAACI,OAAO,CAACC,KAAK,IAAG;MACrB,IAAIC,KAAK,GAAG,IAAI,CAACnF,SAAS,CAACoF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpK,KAAK,IAAIiK,KAAK,CAAC;MACtD,IAAIC,KAAK,EAAE;QACTH,MAAM,CAACM,IAAI,CAACH,KAAK,CAACjK,KAAK,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAO8J,MAAM,CAACO,IAAI,CAAC,IAAI,CAAC;EAC1B;EAAEC,UAAUA,CAAA;IACV,IAAI,CAACrC,KAAK,CAACsC,KAAK,EAAE;IAElB;IACA,IAAI,IAAI,CAAC1F,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,CAACoD,KAAK,CAACuC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC1H,eAAe,CAACtD,YAAY,CAAC;IAClE;IAEA,IAAI,CAACyI,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAAC0G,KAAK,CAACuC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1H,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACuG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAACoG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAACkG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAACgG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAACgI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACvB,KAAK,CAACwC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtH,eAAe,CAACuB,OAAO,IAAI,IAAI,CAACvB,eAAe,CAACuB,OAAO,CAACmF,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACvB,KAAK,CAACwC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;EACF;EAEA9J,GAAGA,CAACoK,MAAwB;IAC1B,IAAI,CAAC9F,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9B,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACrB,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IAEnC;IACA,IAAI,IAAI,CAAC4C,UAAU,KAAK,CAAC,EAAE;MACzB;MACA,IAAI,IAAI,CAAC6D,gBAAgB,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC5F,eAAe,CAACtD,YAAY,GAAG,IAAI,CAACkJ,gBAAgB;MAC3D,CAAC,MAAM,IAAI,IAAI,CAAC7I,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2J,MAAM,GAAG,CAAC,EAAE;QAC9D,IAAI,CAAC1G,eAAe,CAACtD,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MAC/D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACmE,eAAe,CAACtD,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAACuI,aAAa,CAAC4C,IAAI,CAACD,MAAM,CAAC;EACjC;EAEM1J,MAAMA,CAAC4J,IAAoB,EAAEF,MAAwB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACzDD,KAAI,CAACtC,qBAAqB,CAACwC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEF,KAAI,CAACjG,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAMiG,KAAI,CAACG,OAAO,EAAE;QACpBH,KAAI,CAAC9C,aAAa,CAAC4C,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEAxG,IAAIA,CAAC2G,GAAQ;IACX,IAAI,CAACd,UAAU,EAAE;IACjB,IAAI,IAAI,CAACrC,KAAK,CAACwC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxB,OAAO,CAACqD,aAAa,CAAC,IAAI,CAACpD,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,IAAI,IAAI,CAAC5F,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAAC/B,eAAe,CAACtD,YAAY,GAAG,CAAC;IACvC;IAEA,IAAI,CAAC2I,kBAAkB,CAACmD,+BAA+B,CAAC;MACtDC,IAAI,EAAE,IAAI,CAACzI;KACZ,CAAC,CAAC0I,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1D,OAAO,CAAC2D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAAC4D,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACzG,KAAK,EAAE;EACb;EAEAxD,QAAQA,CAACyJ,IAAoB;IAC3B,IAAI,CAAC9H,eAAe,CAACiI,cAAc,GAAGH,IAAI,CAACG,cAAe;IAC1D,IAAI,CAACnG,KAAK,GAAG,KAAK;IAClB,IAAIkH,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MAC7B,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEAA,MAAMA,CAAA;IACJ,IAAI,CAAC7D,kBAAkB,CAAC8D,iCAAiC,CAAC;MACxDV,IAAI,EAAE;QACJR,cAAc,EAAE,IAAI,CAACjI,eAAe,CAACiI;;KAExC,CAAC,CAACS,SAAS,CAACC,GAAG,IAAG;MACjB,IAAI,CAACzD,OAAO,CAAC2D,aAAa,CAAC,MAAM,CAAC;MAClC,IAAI,CAACjC,OAAO,EAAE;IAChB,CAAC,CAAC;EACJ;EAEAP,gBAAgBA,CAAA;IACd,IAAI,CAACjB,gBAAgB,CAACgE,qCAAqC,CAAC;MAAEX,IAAI,EAAE;IAAE,CAAE,CAAC,CACtEY,IAAI,CAACzO,kBAAkB,CAAC,IAAI,CAAC4K,UAAU,CAAC,CAAC,CAACkD,SAAS,CAACC,GAAG,IAAG;MACzD,IAAI,CAAC5L,aAAa,GAAG4L,GAAG,CAACW,OAAQ;MACjC;MACA,IAAI,IAAI,CAACvH,UAAU,KAAK,CAAC,IAAI,IAAI,CAAChF,aAAa,CAAC2J,MAAM,GAAG,CAAC,EAAE;QAC1D,IAAI,CAACjK,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;QACvE,IAAI,CAAC+K,OAAO,EAAE;MAChB,CAAC,MAAM,IAAI,IAAI,CAAC7E,UAAU,KAAK,CAAC,EAAE;QAChC,IAAI,CAACtF,yBAAyB,CAACC,YAAY,GAAG,CAAC;QAC/C,IAAI,CAACkK,OAAO,EAAE;MAChB;IACF,CAAC,CAAC;EACN;EAEAA,OAAOA,CAAA;IACL,IAAI,CAACnK,yBAAyB,CAAC8M,QAAQ,GAAG,IAAI,CAACC,QAAQ;IACvD,IAAI,CAAC/M,yBAAyB,CAACgN,SAAS,GAAG,IAAI,CAACC,SAAS;IACzD,IAAI,CAAChE,eAAe,GAAG,EAAsB;IAC7C,IAAI,CAACiE,YAAY,GAAG,CAAC;IACrB;IACA,IAAI,IAAI,CAAC5H,UAAU,KAAK,CAAC,EAAE;MACzB,IAAI,CAACtF,yBAAyB,CAACC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,IAAI,IAAI,CAACD,yBAAyB,CAACC,YAAY,IAAI,CAAC,EAAE;QACnG,IAAI,CAACkJ,gBAAgB,GAAG,IAAI,CAACnJ,yBAAyB,CAACC,YAAY;MACrE;IACF;IAEA,IAAI,CAAC2I,kBAAkB,CAACuE,8BAA8B,CAAC;MAAEnB,IAAI,EAAE,IAAI,CAAChM;IAAyB,CAAE,CAAC,CAC7F4M,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAAC5D,eAAe,GAAGiD,GAAG,CAACW,OAAO;UAClC,IAAI,CAACK,YAAY,GAAGhB,GAAG,CAACkB,UAAW;QACrC;MACF;IACF,CAAC,CAAC;EACN;EAAE3B,OAAOA,CAAA;IACP,IAAI,CAAC7C,kBAAkB,CAACyE,8BAA8B,CAAC;MAAErB,IAAI,EAAE,IAAI,CAAChD;IAAqB,CAAE,CAAC,CACzF4D,IAAI,EAAE,CACNX,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAID,GAAG,CAACW,OAAO,EAAE;UACf,IAAI,CAACtJ,eAAe,GAAG;YAAEpB,UAAU,EAAE,EAAE;YAAEyC,OAAO,EAAE;UAAK,CAAE;UACzD,IAAI,CAACrB,eAAe,CAACtD,YAAY,GAAGiM,GAAG,CAACW,OAAO,CAAC5M,YAAY;UAC5D,IAAI,CAACsD,eAAe,CAACtB,UAAU,GAAGiK,GAAG,CAACW,OAAO,CAAC5K,UAAU;UACxD,IAAI,CAACsB,eAAe,CAACpB,UAAU,GAAG+J,GAAG,CAACW,OAAO,CAAC1K,UAAU,GAAIkI,KAAK,CAACC,OAAO,CAAC4B,GAAG,CAACW,OAAO,CAAC1K,UAAU,CAAC,GAAG+J,GAAG,CAACW,OAAO,CAAC1K,UAAU,GAAG,CAAC+J,GAAG,CAACW,OAAO,CAAC1K,UAAU,CAAC,GAAI,EAAE;UAC3J,IAAI,CAACoB,eAAe,CAACuB,OAAO,GAAGoH,GAAG,CAACW,OAAO,CAAC/H,OAAO;UAClD,IAAI,CAACvB,eAAe,CAACvB,YAAY,GAAGkK,GAAG,CAACW,OAAO,CAAC7K,YAAY;UAC5D,IAAI,CAACuB,eAAe,CAACiI,cAAc,GAAGU,GAAG,CAACW,OAAO,CAACrB,cAAc;UAChE,IAAI,CAACjI,eAAe,CAACjB,KAAK,GAAG4J,GAAG,CAACW,OAAO,CAACvK,KAAK;UAC9C,IAAI,CAACiB,eAAe,CAACf,OAAO,GAAG0J,GAAG,CAACW,OAAO,CAACrK,OAAO;UAClD,IAAI,CAACe,eAAe,CAACb,UAAU,GAAGwJ,GAAG,CAACW,OAAO,CAACnK,UAAU;UACxD,IAAI,CAACa,eAAe,CAACmB,KAAK,GAAGwH,GAAG,CAACW,OAAO,CAACnI,KAAK;UAC9C;UACA,IAAI,CAACnB,eAAe,CAACqB,OAAO,GAAIsH,GAAG,CAACW,OAAe,CAACjI,OAAO,IAAI,KAAK;QACtE;MACF;IACF,CAAC,CAAC;EACN;EAEA0I,iBAAiBA,CAAC9M,KAAa,EAAE+M,OAAY;IAC3C5B,OAAO,CAACC,GAAG,CAAC2B,OAAO,CAAC;IAEpB,IAAIA,OAAO,EAAE;MACX,IAAI,CAAC,IAAI,CAAChK,eAAe,CAACpB,UAAU,EAAEqL,QAAQ,CAAChN,KAAK,CAAC,EAAE;QACrD,IAAI,CAAC+C,eAAe,CAACpB,UAAU,EAAE0I,IAAI,CAACrK,KAAK,CAAC;MAC9C;MACAmL,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrI,eAAe,CAACpB,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACoB,eAAe,CAACpB,UAAU,GAAG,IAAI,CAACoB,eAAe,CAACpB,UAAU,EAAEsL,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKlN,KAAK,CAAC;IAC7F;EACF;EAEAiC,cAAcA,CAAC4I,IAAS;IACtB,OAAOA,IAAI,CAACzG,OAAO,GAAG,GAAG,GAAG,GAAG;EACjC;EAIA+I,WAAWA,CAACC,KAAU;IACpB;IACA,IAAI,IAAI,CAAClE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B;IACF;IACA;IACA,IAAIkE,KAAK,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC3B,IAAI,CAACvI,UAAU,GAAG,CAAC;MACnB,IAAI,CAACtF,yBAAyB,CAACC,YAAY,GAAG,CAAC;IACjD,CAAC,MAAM;MACL,IAAI,CAACqF,UAAU,GAAG,CAAC;MACnB;MACA,IAAI,IAAI,CAAChF,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2J,MAAM,GAAG,CAAC,EAAE;QACvD,IAAI,CAACjK,yBAAyB,CAACC,YAAY,GAAG,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC,CAAClB,GAAG;MACzE;IACF;IACA,IAAI,CAAC+K,OAAO,EAAE;EAChB;EAEA;EACA2D,WAAWA,CAAC3C,MAAwB;IAClC,IAAI,CAAC9F,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC9B,eAAe,GAAG;MAAEpB,UAAU,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAK,CAAE;IACzD,IAAI,CAACrB,eAAe,CAACf,OAAO,GAAG,CAAC;IAChC,IAAI,CAACe,eAAe,CAACb,UAAU,GAAG,CAAC;IACnC;IACA,IAAI,CAACa,eAAe,CAACtD,YAAY,GAAG,CAAC;IACrC,IAAI,CAACuI,aAAa,CAAC4C,IAAI,CAACD,MAAM,CAAC;EACjC;EAEA;EACM4C,cAAcA,CAAC1C,IAAoB,EAAEF,MAAwB;IAAA,IAAA6C,MAAA;IAAA,OAAAzC,iBAAA;MACjEyC,MAAI,CAAChF,qBAAqB,CAACwC,cAAc,GAAGH,IAAI,CAACG,cAAe;MAChEwC,MAAI,CAAC3I,KAAK,GAAG,KAAK;MAClB,IAAI;QACF,MAAM2I,MAAI,CAACvC,OAAO,EAAE;QACpBuC,MAAI,CAACxF,aAAa,CAAC4C,IAAI,CAACD,MAAM,CAAC;MACjC,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,KAAK,CAAC;MACnD;IAAC;EACH;EAEA;EACA/E,YAAYA,CAACkF,GAAQ;IACnB;IACA,IAAI,CAACnD,KAAK,CAACsC,KAAK,EAAE;IAClB,IAAI,CAACtC,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACvB,YAAY,CAAC;IAC9D,IAAI,CAAC0G,KAAK,CAACuC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC1H,eAAe,CAACpB,UAAU,CAAC;IAC7D,IAAI,CAACuG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACjB,KAAK,CAAC;IACvD,IAAI,CAACoG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACf,OAAO,CAAC;IACzD,IAAI,CAACkG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACb,UAAU,CAAC;IAC5D,IAAI,CAACgG,KAAK,CAACuC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC1H,eAAe,CAACmB,KAAK,CAAC;IAEvD;IACA,IAAI,IAAI,CAACnB,eAAe,CAACtB,UAAU,IAAI,IAAI,CAACsB,eAAe,CAACtB,UAAU,CAACgI,MAAM,GAAG,EAAE,EAAE;MAClF,IAAI,CAACvB,KAAK,CAACwC,aAAa,CAACL,IAAI,CAAC,iBAAiB,CAAC;IAClD;IAEA;IACA,IAAI,IAAI,CAACtH,eAAe,CAACuB,OAAO,IAAI,IAAI,CAACvB,eAAe,CAACuB,OAAO,CAACmF,MAAM,GAAG,GAAG,EAAE;MAC7E,IAAI,CAACvB,KAAK,CAACwC,aAAa,CAACL,IAAI,CAAC,kBAAkB,CAAC;IACnD;IAEA,IAAI,IAAI,CAACnC,KAAK,CAACwC,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACxB,OAAO,CAACqD,aAAa,CAAC,IAAI,CAACpD,KAAK,CAACwC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAM+C,YAAY,GAAG;MAAE,GAAG,IAAI,CAAC1K;IAAe,CAAE;IAChD0K,YAAY,CAAChO,YAAY,GAAG,CAAC;IAE7B,IAAI,CAAC2I,kBAAkB,CAACmD,+BAA+B,CAAC;MACtDC,IAAI,EAAEiC;KACP,CAAC,CAAChC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC1D,OAAO,CAAC2D,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACjC,OAAO,EAAE;MAChB,CAAC,MAAM;QACL,IAAI,CAAC1B,OAAO,CAAC4D,YAAY,CAACH,GAAG,CAACI,OAAQ,CAAC;MACzC;IACF,CAAC,CAAC;IACFT,GAAG,CAACzG,KAAK,EAAE;EACb;EAEAhE,kBAAkBA,CAAC8M,oBAAsC;IACvD,IAAI,CAAC1F,aAAa,CAAC4C,IAAI,CAAC8C,oBAAoB,CAAC;EAC/C;EACAnH,aAAaA,CAAA;IACX;IACA,IAAI,CAACc,WAAW,GAAG;MACjBC,YAAY,EAAE,EAAE;MAChBE,WAAW,EAAE;KACd;IAED;IACA,IAAI,CAACQ,aAAa,CAAC4C,IAAI,CAAC,IAAI,CAAC+C,iBAAiB,CAAC;EACjD;EAEA;EACAxG,eAAeA,CAACkE,GAAQ;IACtB;IACA,IAAI,CAAC,IAAI,CAAChE,WAAW,CAACC,YAAY,IAAI,IAAI,CAACD,WAAW,CAACC,YAAY,CAACK,IAAI,EAAE,KAAK,EAAE,EAAE;MACjF,IAAI,CAACM,OAAO,CAAC4D,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA;IACA,MAAM+B,gBAAgB,GAAG,IAAI,CAAC9G,YAAY,CAACqD,IAAI,CAAC0D,CAAC,IAC/CA,CAAC,CAACvG,YAAY,CAACwG,WAAW,EAAE,KAAK,IAAI,CAACzG,WAAW,CAACC,YAAY,CAACK,IAAI,EAAE,CAACmG,WAAW,EAAE,CACpF;IAED,IAAIF,gBAAgB,EAAE;MACpB,IAAI,CAAC3F,OAAO,CAAC4D,YAAY,CAAC,iBAAiB,CAAC;MAC5C;IACF;IAEA;IACA,MAAMkC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACnH,YAAY,CAACwC,GAAG,CAACuE,CAAC,IAAIA,CAAC,CAACjF,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;IAE/E;IACA,MAAMsF,QAAQ,GAAa;MACzBtF,UAAU,EAAEmF,KAAK;MACjBzG,YAAY,EAAE,IAAI,CAACD,WAAW,CAACC,YAAY,CAACK,IAAI,EAAE;MAClDH,WAAW,EAAE,IAAI,CAACH,WAAW,CAACG,WAAW,EAAEG,IAAI,EAAE,IAAI;KACtD;IAED;IACA,IAAI,CAACb,YAAY,CAACuD,IAAI,CAAC6D,QAAQ,CAAC;IAEhC;IACA,IAAI,CAACjG,OAAO,CAAC2D,aAAa,CAAC,OAAOsC,QAAQ,CAAC5G,YAAY,UAAU,CAAC;IAClE+D,GAAG,CAACzG,KAAK,EAAE;EACb;EACA+B,gBAAgBA,CAACwH,GAAa;IAC5B;IACAC,KAAK,CAAC,QAAQ,GAAGD,GAAG,CAAC7G,YAAY,CAAC;EACpC;EAEAb,gBAAgBA,CAAC4H,UAAkB;IACjC,IAAI,CAACvH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACmG,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACjF,UAAU,KAAKyF,UAAU,CAAC;IAC9E,IAAI,CAACtH,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACkG,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAAC1F,UAAU,KAAKyF,UAAU,CAAC;EAC5F;;;uCA9aWzG,8BAA8B,EAAAtJ,EAAA,CAAAiQ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnQ,EAAA,CAAAiQ,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAArQ,EAAA,CAAAiQ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAvQ,EAAA,CAAAiQ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzQ,EAAA,CAAAiQ,iBAAA,CAAAS,EAAA,CAAAC,gBAAA,GAAA3Q,EAAA,CAAAiQ,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA7Q,EAAA,CAAAiQ,iBAAA,CAAAW,EAAA,CAAAE,kBAAA,GAAA9Q,EAAA,CAAAiQ,iBAAA,CAAAc,EAAA,CAAAC,aAAA,GAAAhR,EAAA,CAAAiQ,iBAAA,CAAAgB,EAAA,CAAAC,MAAA,GAAAlR,EAAA,CAAAiQ,iBAAA,CAAAjQ,EAAA,CAAAmR,UAAA;IAAA;EAAA;;;YAA9B7H,8BAA8B;MAAA8H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtR,EAAA,CAAAuR,0BAAA,EAAAvR,EAAA,CAAAwR,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA/B,QAAA,WAAAgC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC3CzC7R,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAkC,SAAA,qBAAiC;UACnClC,EAAA,CAAAG,YAAA,EAAiB;UAKbH,EAFJ,CAAAC,cAAA,sBAAoC,aACd,aACD;UACfD,EAAA,CAAAqB,UAAA,IAAA0Q,6CAAA,iBAAiE;UAS/D/R,EADF,CAAAC,cAAA,cAAwC,gBACM;UAAAD,EAAA,CAAAE,MAAA,+BAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,iBACuD;UAArDD,EAAA,CAAAU,gBAAA,2BAAAsR,wEAAApR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA5Q,yBAAA,CAAAgC,YAAA,EAAAtC,MAAA,MAAAkR,GAAA,CAAA5Q,yBAAA,CAAAgC,YAAA,GAAAtC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoD;UACxDZ,EAFE,CAAAG,YAAA,EACuD,EACnD;UAEJH,EADF,CAAAC,cAAA,eAAwC,iBACI;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,iBACqD;UAAnDD,EAAA,CAAAU,gBAAA,2BAAAwR,wEAAAtR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA5Q,yBAAA,CAAAiC,UAAA,EAAAvC,MAAA,MAAAkR,GAAA,CAAA5Q,yBAAA,CAAAiC,UAAA,GAAAvC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAExDZ,EAHI,CAAAG,YAAA,EACqD,EACjD,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAiB,eACyB,iBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBAAqF;UAA1ED,EAAA,CAAAU,gBAAA,2BAAAyR,4EAAAvR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA5Q,yBAAA,CAAAmC,UAAA,EAAAzC,MAAA,MAAAkR,GAAA,CAAA5Q,yBAAA,CAAAmC,UAAA,GAAAzC,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAkD;UAC3DZ,EAAA,CAAAqB,UAAA,KAAA+Q,oDAAA,wBAA+D;UAInEpS,EADE,CAAAG,YAAA,EAAY,EACR;UAEJH,EADF,CAAAC,cAAA,eAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA2R,4EAAAzR,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA5Q,yBAAA,CAAAwC,OAAA,EAAA9C,MAAA,MAAAkR,GAAA,CAAA5Q,yBAAA,CAAAwC,OAAA,GAAA9C,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE7BF,EAF6B,CAAAG,YAAA,EAAY,EAC3B,EACR;UAEJH,EADF,CAAAC,cAAA,eAAwC,iBACC;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,qBAAyE;UAA9DD,EAAA,CAAAU,gBAAA,2BAAA4R,4EAAA1R,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA5Q,yBAAA,CAAA4E,OAAA,EAAAlF,MAAA,MAAAkR,GAAA,CAAA5Q,yBAAA,CAAA4E,OAAA,GAAAlF,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAA+C;UACxDZ,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACxCH,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAGlCF,EAHkC,CAAAG,YAAA,EAAY,EAC9B,EACR,EACF;UACNH,EAAA,CAAAC,cAAA,cAAiB;UACfD,EAAA,CAAAkC,SAAA,eAA4B;UAE1BlC,EADF,CAAAC,cAAA,eAAmD,kBACc;UAAxBD,EAAA,CAAA4B,UAAA,mBAAA2Q,iEAAA;YAAAvS,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAA,OAAAjS,EAAA,CAAAoB,WAAA,CAAS0Q,GAAA,CAAA5G,WAAA,EAAa;UAAA,EAAC;UAAClL,EAAA,CAAAkC,SAAA,aAAgC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1GH,EAAA,CAAAC,cAAA,kBAAsD;UAApBD,EAAA,CAAA4B,UAAA,mBAAA4Q,iEAAA;YAAAxS,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAA,OAAAjS,EAAA,CAAAoB,WAAA,CAAS0Q,GAAA,CAAAzG,OAAA,EAAS;UAAA,EAAC;UAACrL,EAAA,CAAAkC,SAAA,aAAkC;UAAAlC,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnGH,EAFA,CAAAqB,UAAA,KAAAoR,iDAAA,qBAA4E,KAAAC,iDAAA,qBAGjD;UAMnC1S,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACO;UAIbH,EADF,CAAAC,cAAA,uBAAoC,qBACW;UAAlCD,EAAA,CAAA4B,UAAA,uBAAA+Q,wEAAA/R,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAA,OAAAjS,EAAA,CAAAoB,WAAA,CAAa0Q,GAAA,CAAAjD,WAAA,CAAAjO,MAAA,CAAmB;UAAA,EAAC;UAS5BZ,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAqB,UAAA,KAAAuR,6CAAA,mBAAuE;UAkB7E5S,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,0BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAAmS,8EAAAjS,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA3D,SAAA,EAAAvN,MAAA,MAAAkR,GAAA,CAAA3D,SAAA,GAAAvN,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAAiR,8EAAA;YAAA7S,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAA,OAAAjS,EAAA,CAAAoB,WAAA,CAAc0Q,GAAA,CAAAzG,OAAA,EAAS;UAAA,EAAC;UAIhCrL,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACC;UAUKH,EARd,CAAAC,cAAA,kBAAsB,eACF,eAES,eACO,iBACmE,aACtF,cAC4D,cACjC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,6CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,eAA8B;UAAAD,EAAA,CAAAE,MAAA,iCAAI;UAEtCF,EAFsC,CAAAG,YAAA,EAAK,EACpC,EACC;UACRH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAqB,UAAA,MAAAyR,8CAAA,mBAAuE;UAiB7E9S,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;UACNH,EAAA,CAAAC,cAAA,2BAC2B;UADqBD,EAAA,CAAAU,gBAAA,wBAAAqS,+EAAAnS,MAAA;YAAAZ,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAAjS,EAAA,CAAAiB,kBAAA,CAAA6Q,GAAA,CAAA3D,SAAA,EAAAvN,MAAA,MAAAkR,GAAA,CAAA3D,SAAA,GAAAvN,MAAA;YAAA,OAAAZ,EAAA,CAAAoB,WAAA,CAAAR,MAAA;UAAA,EAAoB;UAClEZ,EAAA,CAAA4B,UAAA,wBAAAmR,+EAAA;YAAA/S,EAAA,CAAAa,aAAA,CAAAoR,GAAA;YAAA,OAAAjS,EAAA,CAAAoB,WAAA,CAAc0Q,GAAA,CAAAzG,OAAA,EAAS;UAAA,EAAC;UAOtCrL,EANY,CAAAG,YAAA,EAAiB,EACb,EACF,EACC,EACC,EACC,EACP;UAuKVH,EApKA,CAAAqB,UAAA,MAAA2R,uDAAA,kCAAAhT,EAAA,CAAAiT,sBAAA,CAAkD,MAAAC,uDAAA,kCAAAlT,EAAA,CAAAiT,sBAAA,CA4EQ,MAAAE,uDAAA,iCAAAnT,EAAA,CAAAiT,sBAAA,CAoEM,MAAAG,uDAAA,iCAAApT,EAAA,CAAAiT,sBAAA,CAoBZ;;;UAlUHjT,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA0R,GAAA,CAAAtL,UAAA,OAAsB;UAW3DxG,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAuB,gBAAA,YAAAuQ,GAAA,CAAA5Q,yBAAA,CAAAgC,YAAA,CAAoD;UAKpDlD,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAuQ,GAAA,CAAA5Q,yBAAA,CAAAiC,UAAA,CAAkD;UAMzCnD,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAuB,gBAAA,YAAAuQ,GAAA,CAAA5Q,yBAAA,CAAAmC,UAAA,CAAkD;UAC/BrD,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA0R,GAAA,CAAArL,SAAA,CAAY;UAO/BzG,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAuQ,GAAA,CAAA5Q,yBAAA,CAAAwC,OAAA,CAA+C;UAC7C1D,EAAA,CAAAO,SAAA,EAAY;UAAZP,EAAA,CAAAI,UAAA,aAAY;UACZJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UACXJ,EAAA,CAAAO,SAAA,GAAW;UAAXP,EAAA,CAAAI,UAAA,YAAW;UAKbJ,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAuB,gBAAA,YAAAuQ,GAAA,CAAA5Q,yBAAA,CAAA4E,OAAA,CAA+C;UAC7C9F,EAAA,CAAAO,SAAA,EAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAc;UAAdP,EAAA,CAAAI,UAAA,eAAc;UACdJ,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAI,UAAA,gBAAe;UASgCJ,EAAA,CAAAO,SAAA,IAAc;UAAdP,EAAA,CAAAI,UAAA,SAAA0R,GAAA,CAAAuB,QAAA,CAAc;UAGvErT,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,SAAA0R,GAAA,CAAAtL,UAAA,OAAsB;UA+BIxG,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA0R,GAAA,CAAA3H,eAAA,CAAoB;UAmB/BnK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA0R,GAAA,CAAA1D,YAAA,CAA+B;UAACpO,EAAA,CAAAuB,gBAAA,SAAAuQ,GAAA,CAAA3D,SAAA,CAAoB;UAACnO,EAAA,CAAAI,UAAA,aAAA0R,GAAA,CAAA7D,QAAA,CAAqB;UA0B/DjO,EAAA,CAAAO,SAAA,IAAoB;UAApBP,EAAA,CAAAI,UAAA,YAAA0R,GAAA,CAAA3H,eAAA,CAAoB;UAkB/BnK,EAAA,CAAAO,SAAA,EAA+B;UAA/BP,EAAA,CAAAI,UAAA,mBAAA0R,GAAA,CAAA1D,YAAA,CAA+B;UAACpO,EAAA,CAAAuB,gBAAA,SAAAuQ,GAAA,CAAA3D,SAAA,CAAoB;UAACnO,EAAA,CAAAI,UAAA,aAAA0R,GAAA,CAAA7D,QAAA,CAAqB;;;qBDnIlGnP,YAAY,EAAAwR,EAAA,CAAAgD,eAAA,EAAAhD,EAAA,CAAAiD,mBAAA,EAAAjD,EAAA,CAAAkD,qBAAA,EAAAlD,EAAA,CAAAmD,qBAAA,EACZnU,mBAAmB,EACnBN,aAAa,EAAAsR,EAAA,CAAAoD,gBAAA,EACbnU,WAAW,EAAAoU,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,mBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,kBAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,MAAA,EACXlV,cAAc,EAAAoR,EAAA,CAAA+D,iBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EACdrV,cAAc,EACdQ,IAAI,EACJD,KAAK,EACLE,mBAAmB,EACnBC,UAAU,EACVZ,gBAAgB,EAAAuR,EAAA,CAAAiE,mBAAA,EAChB3U,kBAAkB,EAClBC,oBAAoB,EACpBV,cAAc,EAAAmR,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,cAAA,EACd1U,uBAAuB;MAAA2U,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}