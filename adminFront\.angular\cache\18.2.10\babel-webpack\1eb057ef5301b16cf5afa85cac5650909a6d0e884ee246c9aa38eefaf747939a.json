{"ast": null, "code": "// previous version:\n// https://github.com/angular-ui/bootstrap/blob/07c31d0731f7cb068a1932b8e01d2312b796b4ec/src/position/position.js\nvar Positioning = /** @class */function () {\n  function Positioning() {}\n  Positioning.prototype.getAllStyles = function (element) {\n    return window.getComputedStyle(element);\n  };\n  Positioning.prototype.getStyle = function (element, prop) {\n    return this.getAllStyles(element)[prop];\n  };\n  Positioning.prototype.isStaticPositioned = function (element) {\n    return (this.getStyle(element, 'position') || 'static') === 'static';\n  };\n  Positioning.prototype.offsetParent = function (element) {\n    var offsetParentEl = element.offsetParent || document.documentElement;\n    while (offsetParentEl && offsetParentEl !== document.documentElement && this.isStaticPositioned(offsetParentEl)) {\n      offsetParentEl = offsetParentEl.offsetParent;\n    }\n    return offsetParentEl || document.documentElement;\n  };\n  Positioning.prototype.position = function (element, round) {\n    if (round === void 0) {\n      round = true;\n    }\n    var elPosition;\n    var parentOffset = {\n      width: 0,\n      height: 0,\n      top: 0,\n      bottom: 0,\n      left: 0,\n      right: 0\n    };\n    if (this.getStyle(element, 'position') === 'fixed') {\n      elPosition = element.getBoundingClientRect();\n      elPosition = {\n        top: elPosition.top,\n        bottom: elPosition.bottom,\n        left: elPosition.left,\n        right: elPosition.right,\n        height: elPosition.height,\n        width: elPosition.width\n      };\n    } else {\n      var offsetParentEl = this.offsetParent(element);\n      elPosition = this.offset(element, false);\n      if (offsetParentEl !== document.documentElement) {\n        parentOffset = this.offset(offsetParentEl, false);\n      }\n      parentOffset.top += offsetParentEl.clientTop;\n      parentOffset.left += offsetParentEl.clientLeft;\n    }\n    elPosition.top -= parentOffset.top;\n    elPosition.bottom -= parentOffset.top;\n    elPosition.left -= parentOffset.left;\n    elPosition.right -= parentOffset.left;\n    if (round) {\n      elPosition.top = Math.round(elPosition.top);\n      elPosition.bottom = Math.round(elPosition.bottom);\n      elPosition.left = Math.round(elPosition.left);\n      elPosition.right = Math.round(elPosition.right);\n    }\n    return elPosition;\n  };\n  Positioning.prototype.offset = function (element, round) {\n    if (round === void 0) {\n      round = true;\n    }\n    var elBcr = element.getBoundingClientRect();\n    var viewportOffset = {\n      top: window.pageYOffset - document.documentElement.clientTop,\n      left: window.pageXOffset - document.documentElement.clientLeft\n    };\n    var elOffset = {\n      height: elBcr.height || element.offsetHeight,\n      width: elBcr.width || element.offsetWidth,\n      top: elBcr.top + viewportOffset.top,\n      bottom: elBcr.bottom + viewportOffset.top,\n      left: elBcr.left + viewportOffset.left,\n      right: elBcr.right + viewportOffset.left\n    };\n    if (round) {\n      elOffset.height = Math.round(elOffset.height);\n      elOffset.width = Math.round(elOffset.width);\n      elOffset.top = Math.round(elOffset.top);\n      elOffset.bottom = Math.round(elOffset.bottom);\n      elOffset.left = Math.round(elOffset.left);\n      elOffset.right = Math.round(elOffset.right);\n    }\n    return elOffset;\n  };\n  /*\n    Return false if the element to position is outside the viewport\n  */\n  Positioning.prototype.positionElements = function (hostElement, targetElement, placement, appendToBody) {\n    var _a = placement.split('-'),\n      _b = _a[0],\n      placementPrimary = _b === void 0 ? 'top' : _b,\n      _c = _a[1],\n      placementSecondary = _c === void 0 ? 'center' : _c;\n    var hostElPosition = appendToBody ? this.offset(hostElement, false) : this.position(hostElement, false);\n    var targetElStyles = this.getAllStyles(targetElement);\n    var marginTop = parseFloat(targetElStyles.marginTop);\n    var marginBottom = parseFloat(targetElStyles.marginBottom);\n    var marginLeft = parseFloat(targetElStyles.marginLeft);\n    var marginRight = parseFloat(targetElStyles.marginRight);\n    var topPosition = 0;\n    var leftPosition = 0;\n    switch (placementPrimary) {\n      case 'top':\n        topPosition = hostElPosition.top - (targetElement.offsetHeight + marginTop + marginBottom);\n        break;\n      case 'bottom':\n        topPosition = hostElPosition.top + hostElPosition.height;\n        break;\n      case 'left':\n        leftPosition = hostElPosition.left - (targetElement.offsetWidth + marginLeft + marginRight);\n        break;\n      case 'right':\n        leftPosition = hostElPosition.left + hostElPosition.width;\n        break;\n    }\n    switch (placementSecondary) {\n      case 'top':\n        topPosition = hostElPosition.top;\n        break;\n      case 'bottom':\n        topPosition = hostElPosition.top + hostElPosition.height - targetElement.offsetHeight;\n        break;\n      case 'left':\n        leftPosition = hostElPosition.left;\n        break;\n      case 'right':\n        leftPosition = hostElPosition.left + hostElPosition.width - targetElement.offsetWidth;\n        break;\n      case 'center':\n        if (placementPrimary === 'top' || placementPrimary === 'bottom') {\n          leftPosition = hostElPosition.left + hostElPosition.width / 2 - targetElement.offsetWidth / 2;\n        } else {\n          topPosition = hostElPosition.top + hostElPosition.height / 2 - targetElement.offsetHeight / 2;\n        }\n        break;\n    }\n    /// The translate3d/gpu acceleration render a blurry text on chrome, the next line is commented until a browser fix\n    // targetElement.style.transform = `translate3d(${Math.round(leftPosition)}px, ${Math.floor(topPosition)}px, 0px)`;\n    targetElement.style.transform = \"translate(\" + Math.round(leftPosition) + \"px, \" + Math.round(topPosition) + \"px)\";\n    // Check if the targetElement is inside the viewport\n    var targetElBCR = targetElement.getBoundingClientRect();\n    var html = document.documentElement;\n    var windowHeight = window.innerHeight || html.clientHeight;\n    var windowWidth = window.innerWidth || html.clientWidth;\n    return targetElBCR.left >= 0 && targetElBCR.top >= 0 && targetElBCR.right <= windowWidth && targetElBCR.bottom <= windowHeight;\n  };\n  return Positioning;\n}();\nexport { Positioning };\nvar placementSeparator = /\\s+/;\nvar positionService = new Positioning();\n/*\n * Accept the placement array and applies the appropriate placement dependent on the viewport.\n * Returns the applied placement.\n * In case of auto placement, placements are selected in order\n *   'top', 'bottom', 'left', 'right',\n *   'top-left', 'top-right',\n *   'bottom-left', 'bottom-right',\n *   'left-top', 'left-bottom',\n *   'right-top', 'right-bottom'.\n * */\nexport function positionElements(hostElement, targetElement, placement, appendToBody, baseClass) {\n  var placementVals = Array.isArray(placement) ? placement : placement.split(placementSeparator);\n  var allowedPlacements = ['top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'left-top', 'left-bottom', 'right-top', 'right-bottom'];\n  var classList = targetElement.classList;\n  var addClassesToTarget = function (targetPlacement) {\n    var _a = targetPlacement.split('-'),\n      primary = _a[0],\n      secondary = _a[1];\n    var classes = [];\n    if (baseClass) {\n      classes.push(baseClass + \"-\" + primary);\n      if (secondary) {\n        classes.push(baseClass + \"-\" + primary + \"-\" + secondary);\n      }\n      classes.forEach(function (classname) {\n        classList.add(classname);\n      });\n    }\n    return classes;\n  };\n  // Remove old placement classes to avoid issues\n  if (baseClass) {\n    allowedPlacements.forEach(function (placementToRemove) {\n      classList.remove(baseClass + \"-\" + placementToRemove);\n    });\n  }\n  // replace auto placement with other placements\n  var hasAuto = placementVals.findIndex(function (val) {\n    return val === 'auto';\n  });\n  if (hasAuto >= 0) {\n    allowedPlacements.forEach(function (obj) {\n      if (placementVals.find(function (val) {\n        return val.search('^' + obj) !== -1;\n      }) == null) {\n        placementVals.splice(hasAuto++, 1, obj);\n      }\n    });\n  }\n  // coordinates where to position\n  // Required for transform:\n  var style = targetElement.style;\n  style.position = 'absolute';\n  style.top = '0';\n  style.left = '0';\n  style['will-change'] = 'transform';\n  var testPlacement;\n  var isInViewport = false;\n  for (var _i = 0, placementVals_1 = placementVals; _i < placementVals_1.length; _i++) {\n    testPlacement = placementVals_1[_i];\n    var addedClasses = addClassesToTarget(testPlacement);\n    if (positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody)) {\n      isInViewport = true;\n      break;\n    }\n    // Remove the baseClasses for further calculation\n    if (baseClass) {\n      addedClasses.forEach(function (classname) {\n        classList.remove(classname);\n      });\n    }\n  }\n  if (!isInViewport) {\n    // If nothing match, the first placement is the default one\n    testPlacement = placementVals[0];\n    addClassesToTarget(testPlacement);\n    positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody);\n  }\n  return testPlacement;\n}", "map": {"version": 3, "names": ["Positioning", "prototype", "getAllStyles", "element", "window", "getComputedStyle", "getStyle", "prop", "isStaticPositioned", "offsetParent", "offsetParentEl", "document", "documentElement", "position", "round", "elPosition", "parentOffset", "width", "height", "top", "bottom", "left", "right", "getBoundingClientRect", "offset", "clientTop", "clientLeft", "Math", "elBcr", "viewportOffset", "pageYOffset", "pageXOffset", "elOffset", "offsetHeight", "offsetWidth", "positionElements", "hostElement", "targetElement", "placement", "appendToBody", "_a", "split", "_b", "placementPrimary", "_c", "placementSecondary", "hostElPosition", "targetElStyles", "marginTop", "parseFloat", "marginBottom", "marginLeft", "marginRight", "topPosition", "leftPosition", "style", "transform", "targetElBCR", "html", "windowHeight", "innerHeight", "clientHeight", "windowWidth", "innerWidth", "clientWidth", "placementSeparator", "positionService", "baseClass", "placementVals", "Array", "isArray", "allowedPlacements", "classList", "addClassesToTarget", "targetPlacement", "primary", "secondary", "classes", "push", "for<PERSON>ach", "classname", "add", "placementToRemove", "remove", "hasAuto", "findIndex", "val", "obj", "find", "search", "splice", "testPlacement", "isInViewport", "_i", "placementVals_1", "length", "addedClasses"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/positioning/dist/positioning.js"], "sourcesContent": ["// previous version:\n// https://github.com/angular-ui/bootstrap/blob/07c31d0731f7cb068a1932b8e01d2312b796b4ec/src/position/position.js\nvar Positioning = /** @class */ (function () {\n    function Positioning() {\n    }\n    Positioning.prototype.getAllStyles = function (element) { return window.getComputedStyle(element); };\n    Positioning.prototype.getStyle = function (element, prop) { return this.getAllStyles(element)[prop]; };\n    Positioning.prototype.isStaticPositioned = function (element) {\n        return (this.getStyle(element, 'position') || 'static') === 'static';\n    };\n    Positioning.prototype.offsetParent = function (element) {\n        var offsetParentEl = element.offsetParent || document.documentElement;\n        while (offsetParentEl && offsetParentEl !== document.documentElement && this.isStaticPositioned(offsetParentEl)) {\n            offsetParentEl = offsetParentEl.offsetParent;\n        }\n        return offsetParentEl || document.documentElement;\n    };\n    Positioning.prototype.position = function (element, round) {\n        if (round === void 0) { round = true; }\n        var elPosition;\n        var parentOffset = { width: 0, height: 0, top: 0, bottom: 0, left: 0, right: 0 };\n        if (this.getStyle(element, 'position') === 'fixed') {\n            elPosition = element.getBoundingClientRect();\n            elPosition = {\n                top: elPosition.top,\n                bottom: elPosition.bottom,\n                left: elPosition.left,\n                right: elPosition.right,\n                height: elPosition.height,\n                width: elPosition.width\n            };\n        }\n        else {\n            var offsetParentEl = this.offsetParent(element);\n            elPosition = this.offset(element, false);\n            if (offsetParentEl !== document.documentElement) {\n                parentOffset = this.offset(offsetParentEl, false);\n            }\n            parentOffset.top += offsetParentEl.clientTop;\n            parentOffset.left += offsetParentEl.clientLeft;\n        }\n        elPosition.top -= parentOffset.top;\n        elPosition.bottom -= parentOffset.top;\n        elPosition.left -= parentOffset.left;\n        elPosition.right -= parentOffset.left;\n        if (round) {\n            elPosition.top = Math.round(elPosition.top);\n            elPosition.bottom = Math.round(elPosition.bottom);\n            elPosition.left = Math.round(elPosition.left);\n            elPosition.right = Math.round(elPosition.right);\n        }\n        return elPosition;\n    };\n    Positioning.prototype.offset = function (element, round) {\n        if (round === void 0) { round = true; }\n        var elBcr = element.getBoundingClientRect();\n        var viewportOffset = {\n            top: window.pageYOffset - document.documentElement.clientTop,\n            left: window.pageXOffset - document.documentElement.clientLeft\n        };\n        var elOffset = {\n            height: elBcr.height || element.offsetHeight,\n            width: elBcr.width || element.offsetWidth,\n            top: elBcr.top + viewportOffset.top,\n            bottom: elBcr.bottom + viewportOffset.top,\n            left: elBcr.left + viewportOffset.left,\n            right: elBcr.right + viewportOffset.left\n        };\n        if (round) {\n            elOffset.height = Math.round(elOffset.height);\n            elOffset.width = Math.round(elOffset.width);\n            elOffset.top = Math.round(elOffset.top);\n            elOffset.bottom = Math.round(elOffset.bottom);\n            elOffset.left = Math.round(elOffset.left);\n            elOffset.right = Math.round(elOffset.right);\n        }\n        return elOffset;\n    };\n    /*\n      Return false if the element to position is outside the viewport\n    */\n    Positioning.prototype.positionElements = function (hostElement, targetElement, placement, appendToBody) {\n        var _a = placement.split('-'), _b = _a[0], placementPrimary = _b === void 0 ? 'top' : _b, _c = _a[1], placementSecondary = _c === void 0 ? 'center' : _c;\n        var hostElPosition = appendToBody ? this.offset(hostElement, false) : this.position(hostElement, false);\n        var targetElStyles = this.getAllStyles(targetElement);\n        var marginTop = parseFloat(targetElStyles.marginTop);\n        var marginBottom = parseFloat(targetElStyles.marginBottom);\n        var marginLeft = parseFloat(targetElStyles.marginLeft);\n        var marginRight = parseFloat(targetElStyles.marginRight);\n        var topPosition = 0;\n        var leftPosition = 0;\n        switch (placementPrimary) {\n            case 'top':\n                topPosition = (hostElPosition.top - (targetElement.offsetHeight + marginTop + marginBottom));\n                break;\n            case 'bottom':\n                topPosition = (hostElPosition.top + hostElPosition.height);\n                break;\n            case 'left':\n                leftPosition = (hostElPosition.left - (targetElement.offsetWidth + marginLeft + marginRight));\n                break;\n            case 'right':\n                leftPosition = (hostElPosition.left + hostElPosition.width);\n                break;\n        }\n        switch (placementSecondary) {\n            case 'top':\n                topPosition = hostElPosition.top;\n                break;\n            case 'bottom':\n                topPosition = hostElPosition.top + hostElPosition.height - targetElement.offsetHeight;\n                break;\n            case 'left':\n                leftPosition = hostElPosition.left;\n                break;\n            case 'right':\n                leftPosition = hostElPosition.left + hostElPosition.width - targetElement.offsetWidth;\n                break;\n            case 'center':\n                if (placementPrimary === 'top' || placementPrimary === 'bottom') {\n                    leftPosition = (hostElPosition.left + hostElPosition.width / 2 - targetElement.offsetWidth / 2);\n                }\n                else {\n                    topPosition = (hostElPosition.top + hostElPosition.height / 2 - targetElement.offsetHeight / 2);\n                }\n                break;\n        }\n        /// The translate3d/gpu acceleration render a blurry text on chrome, the next line is commented until a browser fix\n        // targetElement.style.transform = `translate3d(${Math.round(leftPosition)}px, ${Math.floor(topPosition)}px, 0px)`;\n        targetElement.style.transform = \"translate(\" + Math.round(leftPosition) + \"px, \" + Math.round(topPosition) + \"px)\";\n        // Check if the targetElement is inside the viewport\n        var targetElBCR = targetElement.getBoundingClientRect();\n        var html = document.documentElement;\n        var windowHeight = window.innerHeight || html.clientHeight;\n        var windowWidth = window.innerWidth || html.clientWidth;\n        return targetElBCR.left >= 0 && targetElBCR.top >= 0 && targetElBCR.right <= windowWidth &&\n            targetElBCR.bottom <= windowHeight;\n    };\n    return Positioning;\n}());\nexport { Positioning };\nvar placementSeparator = /\\s+/;\nvar positionService = new Positioning();\n/*\n * Accept the placement array and applies the appropriate placement dependent on the viewport.\n * Returns the applied placement.\n * In case of auto placement, placements are selected in order\n *   'top', 'bottom', 'left', 'right',\n *   'top-left', 'top-right',\n *   'bottom-left', 'bottom-right',\n *   'left-top', 'left-bottom',\n *   'right-top', 'right-bottom'.\n * */\nexport function positionElements(hostElement, targetElement, placement, appendToBody, baseClass) {\n    var placementVals = Array.isArray(placement) ? placement : placement.split(placementSeparator);\n    var allowedPlacements = [\n        'top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right', 'left-top', 'left-bottom',\n        'right-top', 'right-bottom'\n    ];\n    var classList = targetElement.classList;\n    var addClassesToTarget = function (targetPlacement) {\n        var _a = targetPlacement.split('-'), primary = _a[0], secondary = _a[1];\n        var classes = [];\n        if (baseClass) {\n            classes.push(baseClass + \"-\" + primary);\n            if (secondary) {\n                classes.push(baseClass + \"-\" + primary + \"-\" + secondary);\n            }\n            classes.forEach(function (classname) { classList.add(classname); });\n        }\n        return classes;\n    };\n    // Remove old placement classes to avoid issues\n    if (baseClass) {\n        allowedPlacements.forEach(function (placementToRemove) { classList.remove(baseClass + \"-\" + placementToRemove); });\n    }\n    // replace auto placement with other placements\n    var hasAuto = placementVals.findIndex(function (val) { return val === 'auto'; });\n    if (hasAuto >= 0) {\n        allowedPlacements.forEach(function (obj) {\n            if (placementVals.find(function (val) { return val.search('^' + obj) !== -1; }) == null) {\n                placementVals.splice(hasAuto++, 1, obj);\n            }\n        });\n    }\n    // coordinates where to position\n    // Required for transform:\n    var style = targetElement.style;\n    style.position = 'absolute';\n    style.top = '0';\n    style.left = '0';\n    style['will-change'] = 'transform';\n    var testPlacement;\n    var isInViewport = false;\n    for (var _i = 0, placementVals_1 = placementVals; _i < placementVals_1.length; _i++) {\n        testPlacement = placementVals_1[_i];\n        var addedClasses = addClassesToTarget(testPlacement);\n        if (positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody)) {\n            isInViewport = true;\n            break;\n        }\n        // Remove the baseClasses for further calculation\n        if (baseClass) {\n            addedClasses.forEach(function (classname) { classList.remove(classname); });\n        }\n    }\n    if (!isInViewport) {\n        // If nothing match, the first placement is the default one\n        testPlacement = placementVals[0];\n        addClassesToTarget(testPlacement);\n        positionService.positionElements(hostElement, targetElement, testPlacement, appendToBody);\n    }\n    return testPlacement;\n}\n"], "mappings": "AAAA;AACA;AACA,IAAIA,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACAA,WAAW,CAACC,SAAS,CAACC,YAAY,GAAG,UAAUC,OAAO,EAAE;IAAE,OAAOC,MAAM,CAACC,gBAAgB,CAACF,OAAO,CAAC;EAAE,CAAC;EACpGH,WAAW,CAACC,SAAS,CAACK,QAAQ,GAAG,UAAUH,OAAO,EAAEI,IAAI,EAAE;IAAE,OAAO,IAAI,CAACL,YAAY,CAACC,OAAO,CAAC,CAACI,IAAI,CAAC;EAAE,CAAC;EACtGP,WAAW,CAACC,SAAS,CAACO,kBAAkB,GAAG,UAAUL,OAAO,EAAE;IAC1D,OAAO,CAAC,IAAI,CAACG,QAAQ,CAACH,OAAO,EAAE,UAAU,CAAC,IAAI,QAAQ,MAAM,QAAQ;EACxE,CAAC;EACDH,WAAW,CAACC,SAAS,CAACQ,YAAY,GAAG,UAAUN,OAAO,EAAE;IACpD,IAAIO,cAAc,GAAGP,OAAO,CAACM,YAAY,IAAIE,QAAQ,CAACC,eAAe;IACrE,OAAOF,cAAc,IAAIA,cAAc,KAAKC,QAAQ,CAACC,eAAe,IAAI,IAAI,CAACJ,kBAAkB,CAACE,cAAc,CAAC,EAAE;MAC7GA,cAAc,GAAGA,cAAc,CAACD,YAAY;IAChD;IACA,OAAOC,cAAc,IAAIC,QAAQ,CAACC,eAAe;EACrD,CAAC;EACDZ,WAAW,CAACC,SAAS,CAACY,QAAQ,GAAG,UAAUV,OAAO,EAAEW,KAAK,EAAE;IACvD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIC,UAAU;IACd,IAAIC,YAAY,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IAChF,IAAI,IAAI,CAAChB,QAAQ,CAACH,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;MAChDY,UAAU,GAAGZ,OAAO,CAACoB,qBAAqB,CAAC,CAAC;MAC5CR,UAAU,GAAG;QACTI,GAAG,EAAEJ,UAAU,CAACI,GAAG;QACnBC,MAAM,EAAEL,UAAU,CAACK,MAAM;QACzBC,IAAI,EAAEN,UAAU,CAACM,IAAI;QACrBC,KAAK,EAAEP,UAAU,CAACO,KAAK;QACvBJ,MAAM,EAAEH,UAAU,CAACG,MAAM;QACzBD,KAAK,EAAEF,UAAU,CAACE;MACtB,CAAC;IACL,CAAC,MACI;MACD,IAAIP,cAAc,GAAG,IAAI,CAACD,YAAY,CAACN,OAAO,CAAC;MAC/CY,UAAU,GAAG,IAAI,CAACS,MAAM,CAACrB,OAAO,EAAE,KAAK,CAAC;MACxC,IAAIO,cAAc,KAAKC,QAAQ,CAACC,eAAe,EAAE;QAC7CI,YAAY,GAAG,IAAI,CAACQ,MAAM,CAACd,cAAc,EAAE,KAAK,CAAC;MACrD;MACAM,YAAY,CAACG,GAAG,IAAIT,cAAc,CAACe,SAAS;MAC5CT,YAAY,CAACK,IAAI,IAAIX,cAAc,CAACgB,UAAU;IAClD;IACAX,UAAU,CAACI,GAAG,IAAIH,YAAY,CAACG,GAAG;IAClCJ,UAAU,CAACK,MAAM,IAAIJ,YAAY,CAACG,GAAG;IACrCJ,UAAU,CAACM,IAAI,IAAIL,YAAY,CAACK,IAAI;IACpCN,UAAU,CAACO,KAAK,IAAIN,YAAY,CAACK,IAAI;IACrC,IAAIP,KAAK,EAAE;MACPC,UAAU,CAACI,GAAG,GAAGQ,IAAI,CAACb,KAAK,CAACC,UAAU,CAACI,GAAG,CAAC;MAC3CJ,UAAU,CAACK,MAAM,GAAGO,IAAI,CAACb,KAAK,CAACC,UAAU,CAACK,MAAM,CAAC;MACjDL,UAAU,CAACM,IAAI,GAAGM,IAAI,CAACb,KAAK,CAACC,UAAU,CAACM,IAAI,CAAC;MAC7CN,UAAU,CAACO,KAAK,GAAGK,IAAI,CAACb,KAAK,CAACC,UAAU,CAACO,KAAK,CAAC;IACnD;IACA,OAAOP,UAAU;EACrB,CAAC;EACDf,WAAW,CAACC,SAAS,CAACuB,MAAM,GAAG,UAAUrB,OAAO,EAAEW,KAAK,EAAE;IACrD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIc,KAAK,GAAGzB,OAAO,CAACoB,qBAAqB,CAAC,CAAC;IAC3C,IAAIM,cAAc,GAAG;MACjBV,GAAG,EAAEf,MAAM,CAAC0B,WAAW,GAAGnB,QAAQ,CAACC,eAAe,CAACa,SAAS;MAC5DJ,IAAI,EAAEjB,MAAM,CAAC2B,WAAW,GAAGpB,QAAQ,CAACC,eAAe,CAACc;IACxD,CAAC;IACD,IAAIM,QAAQ,GAAG;MACXd,MAAM,EAAEU,KAAK,CAACV,MAAM,IAAIf,OAAO,CAAC8B,YAAY;MAC5ChB,KAAK,EAAEW,KAAK,CAACX,KAAK,IAAId,OAAO,CAAC+B,WAAW;MACzCf,GAAG,EAAES,KAAK,CAACT,GAAG,GAAGU,cAAc,CAACV,GAAG;MACnCC,MAAM,EAAEQ,KAAK,CAACR,MAAM,GAAGS,cAAc,CAACV,GAAG;MACzCE,IAAI,EAAEO,KAAK,CAACP,IAAI,GAAGQ,cAAc,CAACR,IAAI;MACtCC,KAAK,EAAEM,KAAK,CAACN,KAAK,GAAGO,cAAc,CAACR;IACxC,CAAC;IACD,IAAIP,KAAK,EAAE;MACPkB,QAAQ,CAACd,MAAM,GAAGS,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACd,MAAM,CAAC;MAC7Cc,QAAQ,CAACf,KAAK,GAAGU,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACf,KAAK,CAAC;MAC3Ce,QAAQ,CAACb,GAAG,GAAGQ,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACb,GAAG,CAAC;MACvCa,QAAQ,CAACZ,MAAM,GAAGO,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACZ,MAAM,CAAC;MAC7CY,QAAQ,CAACX,IAAI,GAAGM,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACX,IAAI,CAAC;MACzCW,QAAQ,CAACV,KAAK,GAAGK,IAAI,CAACb,KAAK,CAACkB,QAAQ,CAACV,KAAK,CAAC;IAC/C;IACA,OAAOU,QAAQ;EACnB,CAAC;EACD;AACJ;AACA;EACIhC,WAAW,CAACC,SAAS,CAACkC,gBAAgB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,EAAE;IACpG,IAAIC,EAAE,GAAGF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC;MAAEC,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;MAAEG,gBAAgB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAAEE,EAAE,GAAGJ,EAAE,CAAC,CAAC,CAAC;MAAEK,kBAAkB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,EAAE;IACxJ,IAAIE,cAAc,GAAGP,YAAY,GAAG,IAAI,CAACf,MAAM,CAACY,WAAW,EAAE,KAAK,CAAC,GAAG,IAAI,CAACvB,QAAQ,CAACuB,WAAW,EAAE,KAAK,CAAC;IACvG,IAAIW,cAAc,GAAG,IAAI,CAAC7C,YAAY,CAACmC,aAAa,CAAC;IACrD,IAAIW,SAAS,GAAGC,UAAU,CAACF,cAAc,CAACC,SAAS,CAAC;IACpD,IAAIE,YAAY,GAAGD,UAAU,CAACF,cAAc,CAACG,YAAY,CAAC;IAC1D,IAAIC,UAAU,GAAGF,UAAU,CAACF,cAAc,CAACI,UAAU,CAAC;IACtD,IAAIC,WAAW,GAAGH,UAAU,CAACF,cAAc,CAACK,WAAW,CAAC;IACxD,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,QAAQX,gBAAgB;MACpB,KAAK,KAAK;QACNU,WAAW,GAAIP,cAAc,CAAC3B,GAAG,IAAIkB,aAAa,CAACJ,YAAY,GAAGe,SAAS,GAAGE,YAAY,CAAE;QAC5F;MACJ,KAAK,QAAQ;QACTG,WAAW,GAAIP,cAAc,CAAC3B,GAAG,GAAG2B,cAAc,CAAC5B,MAAO;QAC1D;MACJ,KAAK,MAAM;QACPoC,YAAY,GAAIR,cAAc,CAACzB,IAAI,IAAIgB,aAAa,CAACH,WAAW,GAAGiB,UAAU,GAAGC,WAAW,CAAE;QAC7F;MACJ,KAAK,OAAO;QACRE,YAAY,GAAIR,cAAc,CAACzB,IAAI,GAAGyB,cAAc,CAAC7B,KAAM;QAC3D;IACR;IACA,QAAQ4B,kBAAkB;MACtB,KAAK,KAAK;QACNQ,WAAW,GAAGP,cAAc,CAAC3B,GAAG;QAChC;MACJ,KAAK,QAAQ;QACTkC,WAAW,GAAGP,cAAc,CAAC3B,GAAG,GAAG2B,cAAc,CAAC5B,MAAM,GAAGmB,aAAa,CAACJ,YAAY;QACrF;MACJ,KAAK,MAAM;QACPqB,YAAY,GAAGR,cAAc,CAACzB,IAAI;QAClC;MACJ,KAAK,OAAO;QACRiC,YAAY,GAAGR,cAAc,CAACzB,IAAI,GAAGyB,cAAc,CAAC7B,KAAK,GAAGoB,aAAa,CAACH,WAAW;QACrF;MACJ,KAAK,QAAQ;QACT,IAAIS,gBAAgB,KAAK,KAAK,IAAIA,gBAAgB,KAAK,QAAQ,EAAE;UAC7DW,YAAY,GAAIR,cAAc,CAACzB,IAAI,GAAGyB,cAAc,CAAC7B,KAAK,GAAG,CAAC,GAAGoB,aAAa,CAACH,WAAW,GAAG,CAAE;QACnG,CAAC,MACI;UACDmB,WAAW,GAAIP,cAAc,CAAC3B,GAAG,GAAG2B,cAAc,CAAC5B,MAAM,GAAG,CAAC,GAAGmB,aAAa,CAACJ,YAAY,GAAG,CAAE;QACnG;QACA;IACR;IACA;IACA;IACAI,aAAa,CAACkB,KAAK,CAACC,SAAS,GAAG,YAAY,GAAG7B,IAAI,CAACb,KAAK,CAACwC,YAAY,CAAC,GAAG,MAAM,GAAG3B,IAAI,CAACb,KAAK,CAACuC,WAAW,CAAC,GAAG,KAAK;IAClH;IACA,IAAII,WAAW,GAAGpB,aAAa,CAACd,qBAAqB,CAAC,CAAC;IACvD,IAAImC,IAAI,GAAG/C,QAAQ,CAACC,eAAe;IACnC,IAAI+C,YAAY,GAAGvD,MAAM,CAACwD,WAAW,IAAIF,IAAI,CAACG,YAAY;IAC1D,IAAIC,WAAW,GAAG1D,MAAM,CAAC2D,UAAU,IAAIL,IAAI,CAACM,WAAW;IACvD,OAAOP,WAAW,CAACpC,IAAI,IAAI,CAAC,IAAIoC,WAAW,CAACtC,GAAG,IAAI,CAAC,IAAIsC,WAAW,CAACnC,KAAK,IAAIwC,WAAW,IACpFL,WAAW,CAACrC,MAAM,IAAIuC,YAAY;EAC1C,CAAC;EACD,OAAO3D,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,IAAIiE,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,eAAe,GAAG,IAAIlE,WAAW,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmC,gBAAgBA,CAACC,WAAW,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,EAAE4B,SAAS,EAAE;EAC7F,IAAIC,aAAa,GAAGC,KAAK,CAACC,OAAO,CAAChC,SAAS,CAAC,GAAGA,SAAS,GAAGA,SAAS,CAACG,KAAK,CAACwB,kBAAkB,CAAC;EAC9F,IAAIM,iBAAiB,GAAG,CACpB,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EACnH,WAAW,EAAE,cAAc,CAC9B;EACD,IAAIC,SAAS,GAAGnC,aAAa,CAACmC,SAAS;EACvC,IAAIC,kBAAkB,GAAG,SAAAA,CAAUC,eAAe,EAAE;IAChD,IAAIlC,EAAE,GAAGkC,eAAe,CAACjC,KAAK,CAAC,GAAG,CAAC;MAAEkC,OAAO,GAAGnC,EAAE,CAAC,CAAC,CAAC;MAAEoC,SAAS,GAAGpC,EAAE,CAAC,CAAC,CAAC;IACvE,IAAIqC,OAAO,GAAG,EAAE;IAChB,IAAIV,SAAS,EAAE;MACXU,OAAO,CAACC,IAAI,CAACX,SAAS,GAAG,GAAG,GAAGQ,OAAO,CAAC;MACvC,IAAIC,SAAS,EAAE;QACXC,OAAO,CAACC,IAAI,CAACX,SAAS,GAAG,GAAG,GAAGQ,OAAO,GAAG,GAAG,GAAGC,SAAS,CAAC;MAC7D;MACAC,OAAO,CAACE,OAAO,CAAC,UAAUC,SAAS,EAAE;QAAER,SAAS,CAACS,GAAG,CAACD,SAAS,CAAC;MAAE,CAAC,CAAC;IACvE;IACA,OAAOH,OAAO;EAClB,CAAC;EACD;EACA,IAAIV,SAAS,EAAE;IACXI,iBAAiB,CAACQ,OAAO,CAAC,UAAUG,iBAAiB,EAAE;MAAEV,SAAS,CAACW,MAAM,CAAChB,SAAS,GAAG,GAAG,GAAGe,iBAAiB,CAAC;IAAE,CAAC,CAAC;EACtH;EACA;EACA,IAAIE,OAAO,GAAGhB,aAAa,CAACiB,SAAS,CAAC,UAAUC,GAAG,EAAE;IAAE,OAAOA,GAAG,KAAK,MAAM;EAAE,CAAC,CAAC;EAChF,IAAIF,OAAO,IAAI,CAAC,EAAE;IACdb,iBAAiB,CAACQ,OAAO,CAAC,UAAUQ,GAAG,EAAE;MACrC,IAAInB,aAAa,CAACoB,IAAI,CAAC,UAAUF,GAAG,EAAE;QAAE,OAAOA,GAAG,CAACG,MAAM,CAAC,GAAG,GAAGF,GAAG,CAAC,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,IAAI,IAAI,EAAE;QACrFnB,aAAa,CAACsB,MAAM,CAACN,OAAO,EAAE,EAAE,CAAC,EAAEG,GAAG,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACA;EACA;EACA,IAAIhC,KAAK,GAAGlB,aAAa,CAACkB,KAAK;EAC/BA,KAAK,CAAC1C,QAAQ,GAAG,UAAU;EAC3B0C,KAAK,CAACpC,GAAG,GAAG,GAAG;EACfoC,KAAK,CAAClC,IAAI,GAAG,GAAG;EAChBkC,KAAK,CAAC,aAAa,CAAC,GAAG,WAAW;EAClC,IAAIoC,aAAa;EACjB,IAAIC,YAAY,GAAG,KAAK;EACxB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,eAAe,GAAG1B,aAAa,EAAEyB,EAAE,GAAGC,eAAe,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IACjFF,aAAa,GAAGG,eAAe,CAACD,EAAE,CAAC;IACnC,IAAIG,YAAY,GAAGvB,kBAAkB,CAACkB,aAAa,CAAC;IACpD,IAAIzB,eAAe,CAAC/B,gBAAgB,CAACC,WAAW,EAAEC,aAAa,EAAEsD,aAAa,EAAEpD,YAAY,CAAC,EAAE;MAC3FqD,YAAY,GAAG,IAAI;MACnB;IACJ;IACA;IACA,IAAIzB,SAAS,EAAE;MACX6B,YAAY,CAACjB,OAAO,CAAC,UAAUC,SAAS,EAAE;QAAER,SAAS,CAACW,MAAM,CAACH,SAAS,CAAC;MAAE,CAAC,CAAC;IAC/E;EACJ;EACA,IAAI,CAACY,YAAY,EAAE;IACf;IACAD,aAAa,GAAGvB,aAAa,CAAC,CAAC,CAAC;IAChCK,kBAAkB,CAACkB,aAAa,CAAC;IACjCzB,eAAe,CAAC/B,gBAAgB,CAACC,WAAW,EAAEC,aAAa,EAAEsD,aAAa,EAAEpD,YAAY,CAAC;EAC7F;EACA,OAAOoD,aAAa;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}