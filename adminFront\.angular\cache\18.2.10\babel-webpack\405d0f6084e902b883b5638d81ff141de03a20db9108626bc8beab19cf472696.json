{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Cambodian [km]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/k<PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '១',\n      2: '២',\n      3: '៣',\n      4: '៤',\n      5: '៥',\n      6: '៦',\n      7: '៧',\n      8: '៨',\n      9: '៩',\n      0: '០'\n    },\n    numberMap = {\n      '១': '1',\n      '២': '2',\n      '៣': '3',\n      '៤': '4',\n      '៥': '5',\n      '៦': '6',\n      '៧': '7',\n      '៨': '8',\n      '៩': '9',\n      '០': '0'\n    };\n  var km = moment.defineLocale('km', {\n    months: 'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split('_'),\n    monthsShort: 'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split('_'),\n    weekdays: 'អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍'.split('_'),\n    weekdaysShort: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n    weekdaysMin: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd, D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ព្រឹក|ល្ងាច/,\n    isPM: function (input) {\n      return input === 'ល្ងាច';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ព្រឹក';\n      } else {\n        return 'ល្ងាច';\n      }\n    },\n    calendar: {\n      sameDay: '[ថ្ងៃនេះ ម៉ោង] LT',\n      nextDay: '[ស្អែក ម៉ោង] LT',\n      nextWeek: 'dddd [ម៉ោង] LT',\n      lastDay: '[ម្សិលមិញ ម៉ោង] LT',\n      lastWeek: 'dddd [សប្តាហ៍មុន] [ម៉ោង] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%sទៀត',\n      past: '%sមុន',\n      s: 'ប៉ុន្មានវិនាទី',\n      ss: '%d វិនាទី',\n      m: 'មួយនាទី',\n      mm: '%d នាទី',\n      h: 'មួយម៉ោង',\n      hh: '%d ម៉ោង',\n      d: 'មួយថ្ងៃ',\n      dd: '%d ថ្ងៃ',\n      M: 'មួយខែ',\n      MM: '%d ខែ',\n      y: 'មួយឆ្នាំ',\n      yy: '%d ឆ្នាំ'\n    },\n    dayOfMonthOrdinalParse: /ទី\\d{1,2}/,\n    ordinal: 'ទី%d',\n    preparse: function (string) {\n      return string.replace(/[១២៣៤៥៦៧៨៩០]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return km;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "km", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "preparse", "string", "replace", "match", "postformat", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/km.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Cambodian [km]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/k<PERSON><PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '១',\n            2: '២',\n            3: '៣',\n            4: '៤',\n            5: '៥',\n            6: '៦',\n            7: '៧',\n            8: '៨',\n            9: '៩',\n            0: '០',\n        },\n        numberMap = {\n            '១': '1',\n            '២': '2',\n            '៣': '3',\n            '៤': '4',\n            '៥': '5',\n            '៦': '6',\n            '៧': '7',\n            '៨': '8',\n            '៩': '9',\n            '០': '0',\n        };\n\n    var km = moment.defineLocale('km', {\n        months: 'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split(\n            '_'\n        ),\n        monthsShort:\n            'មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ'.split(\n                '_'\n            ),\n        weekdays: 'អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍'.split('_'),\n        weekdaysShort: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n        weekdaysMin: 'អា_ច_អ_ព_ព្រ_សុ_ស'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd, D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ព្រឹក|ល្ងាច/,\n        isPM: function (input) {\n            return input === 'ល្ងាច';\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ព្រឹក';\n            } else {\n                return 'ល្ងាច';\n            }\n        },\n        calendar: {\n            sameDay: '[ថ្ងៃនេះ ម៉ោង] LT',\n            nextDay: '[ស្អែក ម៉ោង] LT',\n            nextWeek: 'dddd [ម៉ោង] LT',\n            lastDay: '[ម្សិលមិញ ម៉ោង] LT',\n            lastWeek: 'dddd [សប្តាហ៍មុន] [ម៉ោង] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%sទៀត',\n            past: '%sមុន',\n            s: 'ប៉ុន្មានវិនាទី',\n            ss: '%d វិនាទី',\n            m: 'មួយនាទី',\n            mm: '%d នាទី',\n            h: 'មួយម៉ោង',\n            hh: '%d ម៉ោង',\n            d: 'មួយថ្ងៃ',\n            dd: '%d ថ្ងៃ',\n            M: 'មួយខែ',\n            MM: '%d ខែ',\n            y: 'មួយឆ្នាំ',\n            yy: '%d ឆ្នាំ',\n        },\n        dayOfMonthOrdinalParse: /ទី\\d{1,2}/,\n        ordinal: 'ទី%d',\n        preparse: function (string) {\n            return string.replace(/[១២៣៤៥៦៧៨៩០]/g, function (match) {\n                return numberMap[match];\n            });\n        },\n        postformat: function (string) {\n            return string.replace(/\\d/g, function (match) {\n                return symbolMap[match];\n            });\n        },\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return km;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;EAEL,IAAIC,EAAE,GAAGH,MAAM,CAACI,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wEAAwE,CAACC,KAAK,CAClF,GACJ,CAAC;IACDC,WAAW,EACP,wEAAwE,CAACD,KAAK,CAC1E,GACJ,CAAC;IACLE,QAAQ,EAAE,gDAAgD,CAACF,KAAK,CAAC,GAAG,CAAC;IACrEG,aAAa,EAAE,mBAAmB,CAACH,KAAK,CAAC,GAAG,CAAC;IAC7CI,WAAW,EAAE,mBAAmB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC3CK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,aAAa;IAC5BC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,OAAO;IAC5B,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,OAAO;MAClB,CAAC,MAAM;QACH,OAAO,OAAO;MAClB;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE,iBAAiB;MAC1BC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,oBAAoB;MAC7BC,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,OAAO;MACbC,CAAC,EAAE,gBAAgB;MACnBC,EAAE,EAAE,WAAW;MACfC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,OAAO;MACXC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACxB,OAAOA,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;QACpD,OAAOnD,SAAS,CAACmD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDC,UAAU,EAAE,SAAAA,CAAUH,MAAM,EAAE;MAC1B,OAAOA,MAAM,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;QAC1C,OAAOpD,SAAS,CAACoD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC;IACDE,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOtD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}