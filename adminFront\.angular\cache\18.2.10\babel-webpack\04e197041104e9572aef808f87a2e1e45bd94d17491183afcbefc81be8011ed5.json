{"ast": null, "code": "export var EnumSignStatus;\n(function (EnumSignStatus) {\n  EnumSignStatus[EnumSignStatus[\"\\u5DF2\\u7C3D\\u56DE\"] = 1] = \"\\u5DF2\\u7C3D\\u56DE\";\n  EnumSignStatus[EnumSignStatus[\"\\u672A\\u7C3D\\u56DE\"] = 2] = \"\\u672A\\u7C3D\\u56DE\"; //not signed back\n})(EnumSignStatus || (EnumSignStatus = {}));", "map": {"version": 3, "names": ["EnumSignStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumSignStatus.ts"], "sourcesContent": ["export enum EnumSignStatus {\r\n  已簽回 = 1, //signed back\r\n  未簽回 = 2  //not signed back\r\n}\r\n"], "mappings": "AAAA,WAAYA,cAGX;AAHD,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,kDAAO;EACPA,cAAA,CAAAA,cAAA,kDAAO,EAAE;AACX,CAAC,EAHWA,cAAc,KAAdA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}