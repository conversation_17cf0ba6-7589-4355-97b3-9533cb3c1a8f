{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let ValidationHelper = /*#__PURE__*/(() => {\n  class ValidationHelper {\n    constructor() {\n      this.errorMessages = [];\n    }\n    required(name, value) {\n      if (value == null || value === undefined) {\n        this.addErrorMessage(name + ' 必填');\n      }\n      if (typeof String) {\n        if (value === '') {\n          this.addErrorMessage(name + ' 必填');\n        }\n      }\n    }\n    checkStartBeforeEnd(name, start, end) {\n      if (start && end) {\n        const startDate = new Date(start);\n        const endDate = new Date(end);\n        if (startDate > endDate) {\n          this.addErrorMessage(name + ' 开始日期不能大于结束日期');\n        }\n      }\n    }\n    isPhoneNumber(name, value) {\n      if (value !== null && value !== undefined && value !== '') {\n        // const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/;\n        // if (!phoneRegex.test(value)) {\n        //   this.addErrorMessage(name + '電話號碼格式不正確');\n        // }\n        if (value.length > 15 || isNaN(value)) {\n          this.addErrorMessage(name + ' 長度<15');\n        }\n      }\n    }\n    isNaturalNumberInRange(name, value, min, max) {\n      if (value == null || value === undefined || value === '') {\n        this.addErrorMessage(name + ' 必填');\n      } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\n        this.addErrorMessage(name + ' 必須是數字');\n      } else {\n        const numValue = parseInt(value, 10);\n        if (numValue < min || numValue > max) {\n          this.addErrorMessage(name + ` 必須介於${min}到${max}之間`);\n        }\n      }\n    }\n    isStringMaxLength(name, value, maxLength) {\n      if (typeof value === 'string' && value.length > maxLength) {\n        this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`);\n      }\n    }\n    pattern(name, value, pattern, errorDetail = '') {\n      if (!value) return;\n      const regex = new RegExp(pattern);\n      if (regex.test(value) === false) {\n        this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\n      }\n    }\n    regExp(name, value, regEx) {\n      if (regEx.test(value) === false) {\n        this.addErrorMessage(name + ' 格式錯誤');\n      }\n    }\n    equal(name1, name2, value1, value2) {\n      if (value1 !== value2) {\n        this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\n      }\n    }\n    selected(name, value) {\n      if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\n        this.addErrorMessage(name + ' 尚未全部選擇');\n      }\n    }\n    CheckTaiwanID(userid) {\n      // 正規表達式：匹配身分證 (1 字母 + 9 數字) 或居留證 (2 字母 + 8 數字)\n      const idRegex = /^(?:[A-Z][12][0-9]{8}|[A-Z]{2}[0-9]{8})$/;\n      if (!idRegex.test(userid)) {\n        return {\n          isValid: false,\n          errorMessage: '格式錯誤：請輸入正確的身分證或居留證號碼'\n        };\n      }\n      // 身分證與居留證的字母對應表 (A=10, B=11, ..., Z=33)\n      const letterMap = {\n        A: '10',\n        B: '11',\n        C: '12',\n        D: '13',\n        E: '14',\n        F: '15',\n        G: '16',\n        H: '17',\n        I: '34',\n        J: '18',\n        K: '19',\n        L: '20',\n        M: '21',\n        N: '22',\n        O: '35',\n        P: '23',\n        Q: '24',\n        R: '25',\n        S: '26',\n        T: '27',\n        U: '28',\n        V: '29',\n        W: '32',\n        X: '30',\n        Y: '31',\n        Z: '33'\n      };\n      let tempId;\n      let weights;\n      let isResidentId = userid.length === 10; // 居留證 (10 位) 或身分證 (11 位)\n      if (isResidentId) {\n        // 居留證：2 字母 + 8 數字\n        const firstLetter = userid.charAt(0);\n        const secondLetter = userid.charAt(1);\n        tempId = letterMap[firstLetter] + letterMap[secondLetter].charAt(1) + userid.slice(2);\n        weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 居留證加權\n      } else {\n        // 身分證：1 字母 + 9 數字\n        const firstLetter = userid.charAt(0);\n        tempId = letterMap[firstLetter] + userid.slice(1);\n        weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 身分證加權\n      }\n      // 計算檢查碼\n      let sum = 0;\n      for (let i = 0; i < tempId.length; i++) {\n        sum += parseInt(tempId.charAt(i)) * weights[i];\n      }\n      // 檢查碼驗證：總和必須能被 10 整除\n      if (sum % 10 === 0) {\n        return {\n          isValid: true\n        };\n      } else {\n        return {\n          isValid: false,\n          errorMessage: '檢查碼錯誤：請確認身分證或居留證號碼'\n        };\n      }\n    }\n    Date(StrDt, EndDt) {\n      if (EndDt != null) {\n        if (EndDt < StrDt) {\n          this.addErrorMessage(\"起始時間不能大於結束時間！\");\n        }\n      }\n    }\n    addErrorMessage(message) {\n      this.errorMessages.push(message);\n    }\n    clear() {\n      this.errorMessages = [];\n    }\n    static {\n      this.ɵfac = function ValidationHelper_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ValidationHelper)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ValidationHelper,\n        factory: ValidationHelper.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ValidationHelper;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}