{"ast": null, "code": "export * from './header/header.component';\nexport * from './footer/footer.component';\nexport * from './search-input/search-input.component';\nexport * from './tiny-mce/tiny-mce.component';", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\components\\index.ts"], "sourcesContent": ["export * from './header/header.component';\r\nexport * from './footer/footer.component';\r\nexport * from './search-input/search-input.component';\r\nexport * from './tiny-mce/tiny-mce.component';\r\n"], "mappings": "AAAA,cAAc,2BAA2B;AACzC,cAAc,2BAA2B;AACzC,cAAc,uCAAuC;AACrD,cAAc,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}