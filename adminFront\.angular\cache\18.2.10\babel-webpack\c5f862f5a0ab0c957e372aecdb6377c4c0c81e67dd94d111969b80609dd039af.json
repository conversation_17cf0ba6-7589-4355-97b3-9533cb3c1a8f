{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./cipher-core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Output Feedback block mode.\n   */\n  CryptoJS.mode.OFB = function () {\n    var OFB = CryptoJS.lib.BlockCipherMode.extend();\n    var Encryptor = OFB.Encryptor = OFB.extend({\n      processBlock: function (words, offset) {\n        // Shortcuts\n        var cipher = this._cipher;\n        var blockSize = cipher.blockSize;\n        var iv = this._iv;\n        var keystream = this._keystream;\n\n        // Generate keystream\n        if (iv) {\n          keystream = this._keystream = iv.slice(0);\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        }\n        cipher.encryptBlock(keystream, 0);\n\n        // Encrypt\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= keystream[i];\n        }\n      }\n    });\n    OFB.Decryptor = Encryptor;\n    return OFB;\n  }();\n  return CryptoJS.mode.OFB;\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "mode", "OFB", "lib", "BlockCipherMode", "extend", "Encryptor", "processBlock", "words", "offset", "cipher", "_cipher", "blockSize", "iv", "_iv", "keystream", "_keystream", "slice", "undefined", "encryptBlock", "i", "Decryptor"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/crypto-js/mode-ofb.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./cipher-core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./cipher-core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Output Feedback block mode.\n\t */\n\tCryptoJS.mode.OFB = (function () {\n\t    var OFB = CryptoJS.lib.BlockCipherMode.extend();\n\n\t    var Encryptor = OFB.Encryptor = OFB.extend({\n\t        processBlock: function (words, offset) {\n\t            // Shortcuts\n\t            var cipher = this._cipher\n\t            var blockSize = cipher.blockSize;\n\t            var iv = this._iv;\n\t            var keystream = this._keystream;\n\n\t            // Generate keystream\n\t            if (iv) {\n\t                keystream = this._keystream = iv.slice(0);\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            }\n\t            cipher.encryptBlock(keystream, 0);\n\n\t            // Encrypt\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= keystream[i];\n\t            }\n\t        }\n\t    });\n\n\t    OFB.Decryptor = Encryptor;\n\n\t    return OFB;\n\t}());\n\n\n\treturn CryptoJS.mode.OFB;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,eAAe,CAAC,CAAC;EAChF,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,EAAEL,OAAO,CAAC;EAC7C,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,IAAI,CAACC,GAAG,GAAI,YAAY;IAC7B,IAAIA,GAAG,GAAGF,QAAQ,CAACG,GAAG,CAACC,eAAe,CAACC,MAAM,CAAC,CAAC;IAE/C,IAAIC,SAAS,GAAGJ,GAAG,CAACI,SAAS,GAAGJ,GAAG,CAACG,MAAM,CAAC;MACvCE,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;QACnC;QACA,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO;QACzB,IAAIC,SAAS,GAAGF,MAAM,CAACE,SAAS;QAChC,IAAIC,EAAE,GAAG,IAAI,CAACC,GAAG;QACjB,IAAIC,SAAS,GAAG,IAAI,CAACC,UAAU;;QAE/B;QACA,IAAIH,EAAE,EAAE;UACJE,SAAS,GAAG,IAAI,CAACC,UAAU,GAAGH,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC;;UAEzC;UACA,IAAI,CAACH,GAAG,GAAGI,SAAS;QACxB;QACAR,MAAM,CAACS,YAAY,CAACJ,SAAS,EAAE,CAAC,CAAC;;QAEjC;QACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,SAAS,EAAEQ,CAAC,EAAE,EAAE;UAChCZ,KAAK,CAACC,MAAM,GAAGW,CAAC,CAAC,IAAIL,SAAS,CAACK,CAAC,CAAC;QACrC;MACJ;IACJ,CAAC,CAAC;IAEFlB,GAAG,CAACmB,SAAS,GAAGf,SAAS;IAEzB,OAAOJ,GAAG;EACd,CAAC,CAAC,CAAE;EAGJ,OAAOF,QAAQ,CAACC,IAAI,CAACC,GAAG;AAEzB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}