{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { map } from 'rxjs/operators';\nlet QuotationService = class QuotationService {\n  constructor(apiQuotationService) {\n    this.apiQuotationService = apiQuotationService;\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單\n  saveQuotation(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  }\n  // 刪除報價單\n  deleteQuotation(quotationId) {\n    const request = {\n      id: quotationId\n    };\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({\n      body: request\n    });\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.success || false,\n        message: response.errorMessage || '',\n        data: response.data || []\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      id: quotationId\n      // 需要根據實際的 SaveDataQuotation 介面來填充其他必要欄位\n      // 這裡可能需要根據 item 的內容來構建完整的 SaveDataQuotation 物件\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.success || false,\n        message: response.errorMessage || '',\n        data: item\n      };\n    }));\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.deleteQuotation(quotationId).pipe(map(response => {\n      return {\n        success: response.success || false,\n        message: response.errorMessage || '',\n        data: null\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n};\nQuotationService = __decorate([Injectable({\n  providedIn: 'root'\n})], QuotationService);\nexport { QuotationService };", "map": {"version": 3, "names": ["Injectable", "map", "QuotationService", "constructor", "apiQuotationService", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotation", "quotation", "apiQuotationSaveDataPost$Json", "deleteQuotation", "id", "apiQuotationDeleteDataPost$Json", "getDefaultQuotationItems", "pageIndex", "pageSize", "pipe", "response", "success", "message", "errorMessage", "data", "updateQuotationItem", "item", "saveData", "deleteQuotationItem", "exportQuotation", "Error", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport { \r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n\r\n  constructor(private apiQuotationService: ApiQuotationService) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n\r\n  // 儲存報價單\r\n  saveQuotation(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }\r\n\r\n  // 刪除報價單\r\n  deleteQuotation(quotationId: number): Observable<any> {\r\n    const request: DeleteQuotationRequest = { id: quotationId };\r\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = { \r\n      pageIndex: 0, \r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.success || false,\r\n          message: response.errorMessage || '',\r\n          data: response.data || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      id: quotationId,\r\n      // 需要根據實際的 SaveDataQuotation 介面來填充其他必要欄位\r\n      // 這裡可能需要根據 item 的內容來構建完整的 SaveDataQuotation 物件\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.success || false,\r\n          message: response.errorMessage || '',\r\n          data: item\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.deleteQuotation(quotationId).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.success || false,\r\n          message: response.errorMessage || '',\r\n          data: null\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,GAAG,QAAQ,gBAAgB;AAc7B,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAE3BC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;EAAyB;EAChE;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACF,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACN,mBAAmB,CAACQ,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EAEA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACV,mBAAmB,CAACY,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EAEA;EACAW,aAAaA,CAACC,SAA4B;IACxC,OAAO,IAAI,CAACd,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF;EAEA;EACAE,eAAeA,CAACV,WAAmB;IACjC,MAAMJ,OAAO,GAA2B;MAAEe,EAAE,EAAEX;IAAW,CAAE;IAC3D,OAAO,IAAI,CAACN,mBAAmB,CAACkB,+BAA+B,CAAC;MAAEd,IAAI,EAAEF;IAAO,CAAE,CAAC;EACpF;EAEA;EACAiB,wBAAwBA,CAAA;IACtB;IACA,MAAMjB,OAAO,GAA4B;MACvCkB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAACrB,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACoB,IAAI,CAClFzB,GAAG,CAAC0B,QAAQ,IAAG;MACb;MACA,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI,KAAK;QAClCC,OAAO,EAAEF,QAAQ,CAACG,YAAY,IAAI,EAAE;QACpCC,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI;OACH;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAC,mBAAmBA,CAACtB,WAAmB,EAAEuB,IAAmB;IAC1D,MAAMC,QAAQ,GAAsB;MAClCb,EAAE,EAAEX;MACJ;MACA;KACD;IACD,OAAO,IAAI,CAACN,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAE0B;IAAQ,CAAE,CAAC,CAACR,IAAI,CACpFzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI,KAAK;QAClCC,OAAO,EAAEF,QAAQ,CAACG,YAAY,IAAI,EAAE;QACpCC,IAAI,EAAEE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAE,mBAAmBA,CAACzB,WAAmB;IACrC,OAAO,IAAI,CAACU,eAAe,CAACV,WAAW,CAAC,CAACgB,IAAI,CAC3CzB,GAAG,CAAC0B,QAAQ,IAAG;MACb,OAAO;QACLC,OAAO,EAAED,QAAQ,CAACC,OAAO,IAAI,KAAK;QAClCC,OAAO,EAAEF,QAAQ,CAACG,YAAY,IAAI,EAAE;QACpCC,IAAI,EAAE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAK,eAAeA,CAACtB,OAAe;IAC7B;IACA;IACA,MAAM,IAAIuB,KAAK,CAAC,mEAAmE,CAAC;EACtF;CACD;AAvFYnC,gBAAgB,GAAAoC,UAAA,EAH5BtC,UAAU,CAAC;EACVuC,UAAU,EAAE;CACb,CAAC,C,EACWrC,gBAAgB,CAuF5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}