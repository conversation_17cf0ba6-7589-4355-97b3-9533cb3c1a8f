{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Urdu [ur]\n//! author : <PERSON><PERSON> : https://github.com/ibnesayeed\n//! author : Zack : https://github.com/ZackVision\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = ['جنوری', 'فروری', 'مارچ', 'اپریل', 'مئی', 'جون', 'جولائی', 'اگست', 'ستمبر', 'اکتوبر', 'نومبر', 'دسمبر'],\n    days = ['اتوار', 'پیر', 'منگل', 'بدھ', 'جمعرات', 'جمعہ', 'ہفتہ'];\n  var ur = moment.defineLocale('ur', {\n    months: months,\n    monthsShort: months,\n    weekdays: days,\n    weekdaysShort: days,\n    weekdaysMin: days,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd، D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /صبح|شام/,\n    isPM: function (input) {\n      return 'شام' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'صبح';\n      }\n      return 'شام';\n    },\n    calendar: {\n      sameDay: '[آج بوقت] LT',\n      nextDay: '[کل بوقت] LT',\n      nextWeek: 'dddd [بوقت] LT',\n      lastDay: '[گذشتہ روز بوقت] LT',\n      lastWeek: '[گذشتہ] dddd [بوقت] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s بعد',\n      past: '%s قبل',\n      s: 'چند سیکنڈ',\n      ss: '%d سیکنڈ',\n      m: 'ایک منٹ',\n      mm: '%d منٹ',\n      h: 'ایک گھنٹہ',\n      hh: '%d گھنٹے',\n      d: 'ایک دن',\n      dd: '%d دن',\n      M: 'ایک ماہ',\n      MM: '%d ماہ',\n      y: 'ایک سال',\n      yy: '%d سال'\n    },\n    preparse: function (string) {\n      return string.replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/,/g, '،');\n    },\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ur;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}