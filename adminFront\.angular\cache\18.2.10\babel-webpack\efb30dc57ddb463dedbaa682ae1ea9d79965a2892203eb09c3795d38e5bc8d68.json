{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Italian (Switzerland) [it-ch]\n//! author : xfh : https://github.com/xfh\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var itCh = moment.defineLocale('it-ch', {\n    months: 'gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre'.split('_'),\n    monthsShort: 'gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic'.split('_'),\n    weekdays: 'domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato'.split('_'),\n    weekdaysShort: 'dom_lun_mar_mer_gio_ven_sab'.split('_'),\n    weekdaysMin: 'do_lu_ma_me_gi_ve_sa'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[Oggi alle] LT',\n      nextDay: '[Domani alle] LT',\n      nextWeek: 'dddd [alle] LT',\n      lastDay: '[Ieri alle] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[la scorsa] dddd [alle] LT';\n          default:\n            return '[lo scorso] dddd [alle] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: function (s) {\n        return (/^[0-9].+$/.test(s) ? 'tra' : 'in') + ' ' + s;\n      },\n      past: '%s fa',\n      s: 'alcuni secondi',\n      ss: '%d secondi',\n      m: 'un minuto',\n      mm: '%d minuti',\n      h: \"un'ora\",\n      hh: '%d ore',\n      d: 'un giorno',\n      dd: '%d giorni',\n      M: 'un mese',\n      MM: '%d mesi',\n      y: 'un anno',\n      yy: '%d anni'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return itCh;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}