{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport class BaseFilePipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n    this.BASE_FILE = environment.BASE_FILE;\n  }\n  transform(value) {\n    if (!value) return value;\n    if (value.includes(\"/Files\")) {\n      if (value.includes(environment.BASE_WITHOUT_FILEROOT)) {\n        return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n      }\n      return this.sanitizer.bypassSecurityTrustResourceUrl(environment.BASE_WITHOUT_FILEROOT + value);\n    }\n    return this.sanitizer.bypassSecurityTrustResourceUrl(this.BASE_FILE + value);\n  }\n  static {\n    this.ɵfac = function BaseFilePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseFilePipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON><PERSON><PERSON><PERSON>, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"addBaseFile\",\n      type: BaseFilePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "BaseFilePipe", "constructor", "sanitizer", "BASE_FILE", "transform", "value", "includes", "BASE_WITHOUT_FILEROOT", "bypassSecurityTrustResourceUrl", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\pipes\\base-file.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { environment } from '../../../environments/environment';\r\nimport { DomSanitizer, SafeUrl } from '@angular/platform-browser';\r\n\r\n@Pipe({\r\n  name: 'addBaseFile',\r\n  standalone: true\r\n})\r\nexport class BaseFilePipe implements PipeTransform {\r\n  constructor(private sanitizer: DomSanitizer) { }\r\n  readonly BASE_FILE = environment.BASE_FILE;\r\n  transform(value: string | null | undefined): string | null | undefined | SafeUrl {\r\n    if (!value) return value;\r\n    if (value.includes(\"/Files\")) {\r\n      if (value.includes(environment.BASE_WITHOUT_FILEROOT)) {\r\n        return this.sanitizer.bypassSecurityTrustResourceUrl(value)\r\n      }\r\n      return this.sanitizer.bypassSecurityTrustResourceUrl(environment.BASE_WITHOUT_FILEROOT + value)\r\n    }\r\n    return this.sanitizer.bypassSecurityTrustResourceUrl(this.BASE_FILE + value)\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,mCAAmC;;;AAO/D,OAAM,MAAOC,YAAY;EACvBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;IACpB,KAAAC,SAAS,GAAGJ,WAAW,CAACI,SAAS;EADK;EAE/CC,SAASA,CAACC,KAAgC;IACxC,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;IACxB,IAAIA,KAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC5B,IAAID,KAAK,CAACC,QAAQ,CAACP,WAAW,CAACQ,qBAAqB,CAAC,EAAE;QACrD,OAAO,IAAI,CAACL,SAAS,CAACM,8BAA8B,CAACH,KAAK,CAAC;MAC7D;MACA,OAAO,IAAI,CAACH,SAAS,CAACM,8BAA8B,CAACT,WAAW,CAACQ,qBAAqB,GAAGF,KAAK,CAAC;IACjG;IACA,OAAO,IAAI,CAACH,SAAS,CAACM,8BAA8B,CAAC,IAAI,CAACL,SAAS,GAAGE,KAAK,CAAC;EAC9E;;;uCAZWL,YAAY,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;IAAA;EAAA;;;;YAAZZ,YAAY;MAAAa,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}