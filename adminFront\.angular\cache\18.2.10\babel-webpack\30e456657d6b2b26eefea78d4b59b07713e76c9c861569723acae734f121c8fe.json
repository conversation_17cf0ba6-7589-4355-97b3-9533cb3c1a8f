{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport * as moment from 'moment';\nimport { CommonModule } from '@angular/common';\nimport { CalendarModule, CalendarView } from 'angular-calendar';\nimport { DialogModule } from 'primeng/dialog';\nimport { BehaviorSubject, concatMap, of, tap } from 'rxjs';\nimport { SharedModule } from 'src/app/pages/components/shared.module';\nlet EditAvailableTimeSlotComponent = class EditAvailableTimeSlotComponent {\n  constructor(changeDetector, preOderSettingService, route, _router, messageService, _location, dialogService, _eventService) {\n    this.changeDetector = changeDetector;\n    this.preOderSettingService = preOderSettingService;\n    this.route = route;\n    this._router = _router;\n    this.messageService = messageService;\n    this._location = _location;\n    this.dialogService = dialogService;\n    this._eventService = _eventService;\n    this.locale = 'zh';\n    this.getPreOderSettingRes = [];\n    this.activeDayIsOpen = true;\n    this.savePreOrderSetting = [];\n    this.listEvent$ = new BehaviorSubject([]);\n    this.view = CalendarView.Week;\n    this.viewDate = new Date();\n    this.paramInfo = null;\n    this.listDate = [];\n    this.test = 0;\n    this.buildCaseId = this.route.snapshot.paramMap.get('id');\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'));\n  }\n  ngOnInit() {\n    if (this.paramInfo) {\n      this.viewDate = new Date(this.paramInfo.CDateStart);\n    }\n    this.getPreOrderSetting().subscribe();\n  }\n  getPreOrderSetting() {\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.buildCaseId\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : [];\n        this.initEvent();\n      }\n    }));\n  }\n  initEvent() {\n    let temp = [];\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\n      this.getPreOderSettingRes.forEach(e => {\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\n          let date = e.CDate ? new Date(e.CDate) : undefined;\n          date = date ? new Date(date.setMinutes(0)) : date;\n          date = date ? new Date(date.setSeconds(0)) : date;\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0;\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0;\n          temp.push({\n            id: e.CID,\n            CBuildCaseId: this.buildCaseId,\n            CDate: e.CDate,\n            CHour: e.CHour,\n            CIsDelete: false,\n            isChange: false,\n            start: startDate,\n            end: endDate,\n            display: \"background\",\n            IsOld: true\n          });\n        }\n      });\n    }\n    this.listEvent$.next(temp);\n  }\n  handleDateSelect(selectInfo) {\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\n      let temp = [...this.listEvent$.value];\n      const calendarApi = selectInfo.view.calendar;\n      calendarApi.unselect(); // clear date selection\n      calendarApi.addEvent({\n        start: selectInfo.startStr,\n        end: selectInfo.endStr,\n        display: 'background'\n      });\n      temp.push({\n        CBuildCaseId: this.buildCaseId,\n        CDate: selectInfo.startStr,\n        CHour: selectInfo.start.getHours(),\n        CIsDelete: false,\n        isChange: true,\n        start: selectInfo.start,\n        end: selectInfo.end\n      });\n      this.listEvent$.next(temp);\n    }\n  }\n  save() {\n    this.savedData(true).subscribe(res => {\n      this.backToList();\n    });\n  }\n  backToList() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseId\n    });\n    this._location.back();\n  }\n  // Function to check if the cell is empty\n  isCellEmpty(day) {\n    const startHour = day.getHours();\n    let check = false;\n    this.listEvent$.value.some(event => {\n      // console.log(event);\n      if (event.CHour === startHour && new Date(event.start).getDate() === day.getDate() && new Date(event.start).getMonth() === day.getMonth() && new Date(event.start).getFullYear() === day.getFullYear() && !event.CIsDelete) check = true;\n    });\n    return check;\n  }\n  checkDisable(day) {\n    if (day.getTime() < new Date().getTime()) {\n      return true;\n    }\n    return false;\n  }\n  handleEventClick(clickInfo) {\n    let eventIndex = this.listEvent$.value.findIndex(x => typeof x.start === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime() && typeof x.end === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime());\n    if (eventIndex !== -1) {\n      this.listEvent$.value[eventIndex].CIsDelete = true;\n      this.listEvent$.value[eventIndex].isChange = true;\n      clickInfo.event.remove();\n    }\n  }\n  onCheck(e, segment) {\n    let eventIndex = this.listEvent$.value.findIndex(x => x.start.getFullYear() === segment.getFullYear() && x.start.getMonth() === segment.getMonth() && x.start.getDate() === segment.getDate() && x.CHour === segment.getHours());\n    // click empty checkbox\n    if (eventIndex === -1) {\n      // create new event\n      if (e.target.checked) {\n        let temp = [...this.listEvent$.value];\n        temp.push({\n          CBuildCaseId: this.buildCaseId,\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\n          CHour: segment.getHours(),\n          CIsDelete: false,\n          isChange: true,\n          start: segment,\n          IsOld: false\n        });\n        this.listEvent$.next(temp);\n      }\n    } else {\n      // unchecked checkbox\n      if (e.target.checked) {\n        this.listEvent$.value[eventIndex].CIsDelete = false;\n        this.listEvent$.value[eventIndex].isChange = true;\n      } else {\n        this.listEvent$.value[eventIndex].CIsDelete = true;\n        this.listEvent$.value[eventIndex].isChange = true;\n      }\n    }\n  }\n  addNew(ref) {\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\n      this.dialogService.open(ref);\n    } else {\n      this.getPreOderSettingRes = this.handleDataWeek().dataFromPrevious;\n      const updatedEvents = [...this.listEvent$.value];\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\n        updatedEvents.push({\n          CBuildCaseId: e.CBuildCaseId,\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete,\n          isChange: true,\n          start: this.add_weeks(e.CDate, 1),\n          IsOld: false\n        });\n      });\n      this.listEvent$.next(updatedEvents);\n      this.changeDetector.markForCheck();\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  confirmCopy(ref) {\n    this.savedData(false).subscribe(() => this.onClose(ref));\n  }\n  handleDataWeek() {\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1);\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1);\n    let dataFromPrevious = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime() && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime());\n    let dataFromCurrent = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime() && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime());\n    return {\n      dataFromCurrent,\n      dataFromPrevious\n    };\n  }\n  handleShowButton() {\n    var result = new Date(this.viewDate);\n    result.setDate(result.getDate() + 7);\n    if (result.getTime() < Date.now()) {\n      return false;\n    }\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\n      return false;\n    }\n    return true;\n  }\n  savedData(needToFilter) {\n    if (needToFilter) {\n      this.savePreOrderSetting = this.listEvent$.value.filter(x => x.isChange === true).map(e => {\n        this.listDate.push(new Date(e.CDate));\n        return {\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: e.CDate,\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        };\n      });\n    } else {\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: e.CIsDelete\n          });\n        } else {\n          this.savePreOrderSetting.push({\n            CBuildCaseID: parseInt(e.CBuildCaseId),\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\n            CHour: e.CHour,\n            CIsDelete: true\n          });\n        }\n      });\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\n        this.savePreOrderSetting.push({\n          CBuildCaseID: parseInt(e.CBuildCaseId),\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\n          CHour: e.CHour,\n          CIsDelete: e.CIsDelete\n        });\n      });\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\n        this.listDate.push(new Date(x.CDate));\n      });\n    }\n    if (this.savePreOrderSetting.length == 0) {\n      this.backToList();\n      return of();\n    }\n    let minDate = new Date(Math.min.apply(null, this.listDate));\n    let paramSave = {\n      buildCaseId: this.buildCaseId,\n      minDate: minDate\n    };\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\n      body: {\n        CSavePreOrderSetting: this.savePreOrderSetting\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.messageService.showSucessMSG('Save Successfully!');\n        this.savePreOrderSetting = [];\n        this.listDate = [];\n        this.listEvent$.value.forEach(x => {\n          if (x.isChange) {\n            let id = document.getElementById(x.start.toISOString());\n            setTimeout(() => {\n              id?.click();\n            }, 200);\n          }\n        });\n        LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave));\n      }\n    }), concatMap(() => this.getPreOrderSetting()));\n  }\n  add_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + n * 7));\n  }\n  sub_weeks(dt, n) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - n * 7));\n  }\n  get_date(dt) {\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\n  }\n  fromDateToString(date) {\n    date = new Date(+date);\n    date.setTime(date.getTime() - date.getTimezoneOffset() * 60000);\n    let dateAsString = date.toISOString().substr(0, 19);\n    return dateAsString;\n  }\n};\nEditAvailableTimeSlotComponent = __decorate([Component({\n  selector: 'app-edit-available-time-slot',\n  standalone: true,\n  imports: [CommonModule, SharedModule, CalendarModule, DialogModule],\n  templateUrl: './edit-available-time-slot.component.html',\n  styleUrls: ['./edit-available-time-slot.component.scss'],\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], EditAvailableTimeSlotComponent);\nexport { EditAvailableTimeSlotComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "LocalStorageService", "moment", "CommonModule", "CalendarModule", "CalendarView", "DialogModule", "BehaviorSubject", "concatMap", "of", "tap", "SharedModule", "EditAvailableTimeSlotComponent", "constructor", "changeDetector", "preOderSettingService", "route", "_router", "messageService", "_location", "dialogService", "_eventService", "locale", "getPreOderSettingRes", "activeDayIsOpen", "savePreOrderSetting", "listEvent$", "view", "Week", "viewDate", "Date", "paramInfo", "listDate", "test", "buildCaseId", "snapshot", "paramMap", "get", "JSON", "parse", "GetLocalStorage", "ngOnInit", "CDateStart", "getPreOrderSetting", "subscribe", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "pipe", "res", "StatusCode", "Entries", "filter", "x", "CHour", "initEvent", "temp", "length", "for<PERSON>ach", "e", "date", "CDate", "undefined", "setMinutes", "setSeconds", "startDate", "setHours", "endDate", "push", "id", "CID", "CBuildCaseId", "CIsDelete", "isChange", "start", "end", "display", "IsOld", "next", "handleDateSelect", "selectInfo", "getDate", "getHours", "value", "calendarApi", "calendar", "unselect", "addEvent", "startStr", "endStr", "save", "savedData", "backToList", "action", "payload", "back", "isCellEmpty", "day", "startHour", "check", "some", "event", "getMonth", "getFullYear", "checkDisable", "getTime", "handleEventClick", "clickInfo", "eventIndex", "findIndex", "remove", "onCheck", "segment", "target", "checked", "format", "addNew", "ref", "handleDataWeek", "dataFromCurrent", "open", "dataFromPrevious", "updatedEvents", "fromDateToString", "add_weeks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onClose", "close", "confirmCopy", "firstDateofLastWeek", "sub_weeks", "lastDateofCurrentWeek", "handleShowButton", "result", "setDate", "now", "needToFilter", "map", "parseInt", "get_date", "minDate", "Math", "min", "apply", "paramSave", "apiPreOrderSettingSavePreOrderSettingPost$Json", "CSavePreOrderSetting", "showSucessMSG", "document", "getElementById", "toISOString", "setTimeout", "click", "AddLocalStorage", "stringify", "dt", "n", "setTime", "getTimezoneOffset", "dateAsString", "substr", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls", "changeDetection", "OnPush"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\available-time-slot\\edit-available-time-slot\\edit-available-time-slot.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, signal } from '@angular/core';\r\nimport { PreOrderSettingService } from 'src/services/api/services';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { GetPreOrderSettingResponse, PreOrderSetting } from 'src/services/api/models';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport * as moment from 'moment';\r\nimport { CommonModule, Location } from '@angular/common';\r\nimport { CalendarModule, CalendarView } from 'angular-calendar';\r\nimport { DialogModule } from 'primeng/dialog'\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { BehaviorSubject, concatMap, mergeMap, of, tap } from 'rxjs';\r\nimport { SharedModule } from 'src/app/pages/components/shared.module';\r\nimport { EventService, EEvent } from 'src/app/shared/services/event.service';\r\n\r\n@Component({\r\n  selector: 'app-edit-available-time-slot',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    CalendarModule,\r\n    DialogModule\r\n  ],\r\n  templateUrl: './edit-available-time-slot.component.html',\r\n  styleUrls: ['./edit-available-time-slot.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class EditAvailableTimeSlotComponent implements OnInit {\r\n  locale: string = 'zh'\r\n  buildCaseId: any | null\r\n  getPreOderSettingRes = [] as GetPreOrderSettingResponse[]\r\n  activeDayIsOpen: boolean = true;\r\n  savePreOrderSetting = [] as PreOrderSetting[]\r\n\r\n  listEvent$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);\r\n\r\n  view: CalendarView = CalendarView.Week;\r\n  viewDate: Date = new Date();\r\n  paramInfo: any = null\r\n  listDate: any = []\r\n  test = 0;\r\n\r\n  constructor(\r\n    private changeDetector: ChangeDetectorRef,\r\n    private preOderSettingService: PreOrderSettingService,\r\n    private route: ActivatedRoute,\r\n    private _router: Router,\r\n    private messageService: MessageService,\r\n    private _location: Location,\r\n    private dialogService: NbDialogService,\r\n    private _eventService: EventService,\r\n  ) {\r\n    this.buildCaseId = this.route.snapshot.paramMap.get('id')\r\n    this.paramInfo = JSON.parse(LocalStorageService.GetLocalStorage('paramInfo'))\r\n  }\r\n\r\n  ngOnInit() {\r\n    if (this.paramInfo) {\r\n      this.viewDate = new Date(this.paramInfo.CDateStart)\r\n    }\r\n    this.getPreOrderSetting().subscribe()\r\n  }\r\n\r\n  getPreOrderSetting() {\r\n    return this.preOderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.buildCaseId,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.getPreOderSettingRes = res.Entries ? res.Entries.filter(x => x.CHour ? x.CHour > 8 && x.CHour < 22 : null) : []\r\n          this.initEvent()\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  initEvent() {\r\n    let temp : any[] = [];\r\n    if (this.getPreOderSettingRes && this.getPreOderSettingRes.length > 0) {\r\n      this.getPreOderSettingRes.forEach(e => {\r\n        if (e.CHour && e.CHour > 8 && e.CHour < 22) {\r\n          let date = e.CDate ? new Date(e.CDate) : undefined;\r\n          date = date ? new Date(date.setMinutes(0)) : date\r\n          date = date ? new Date(date.setSeconds(0)) : date\r\n          let startDate = date && e.CHour ? new Date(date.setHours(e.CHour)) : 0\r\n          let endDate = date && e.CHour ? new Date(date.setHours(e.CHour + 1)) : 0\r\n          temp.push({\r\n            id: e.CID,\r\n            CBuildCaseId: this.buildCaseId,\r\n            CDate: e.CDate,\r\n            CHour: e.CHour,\r\n            CIsDelete: false,\r\n            isChange: false,\r\n            start: startDate,\r\n            end: endDate,\r\n            display: \"background\",\r\n            IsOld: true\r\n          })\r\n        }\r\n      })\r\n    }\r\n    this.listEvent$.next(temp);\r\n  }\r\n\r\n  handleDateSelect(selectInfo: any) {\r\n    if (selectInfo.start.getDate() === selectInfo.end.getDate() && selectInfo.end.getHours() - selectInfo.start.getHours() === 1) {\r\n      let temp = [...this.listEvent$.value];\r\n      const calendarApi = selectInfo.view.calendar;\r\n\r\n      calendarApi.unselect(); // clear date selection\r\n\r\n      calendarApi.addEvent({\r\n        start: selectInfo.startStr,\r\n        end: selectInfo.endStr,\r\n        display: 'background',\r\n      });\r\n\r\n      temp.push({\r\n        CBuildCaseId: this.buildCaseId,\r\n        CDate: selectInfo.startStr,\r\n        CHour: selectInfo.start.getHours(),\r\n        CIsDelete: false,\r\n        isChange: true,\r\n        start: selectInfo.start,\r\n        end: selectInfo.end,\r\n      })\r\n      this.listEvent$.next(temp);\r\n    }\r\n  }\r\n\r\n  save() {\r\n    this.savedData(true).subscribe(res => {\r\n      this.backToList()\r\n    })\r\n  }\r\n\r\n  backToList() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseId\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  // Function to check if the cell is empty\r\n  isCellEmpty(day: Date): boolean {\r\n    const startHour = day.getHours();\r\n    let check = false;\r\n    this.listEvent$.value.some(event => {\r\n      // console.log(event);\r\n      if (event.CHour === startHour\r\n        && new Date(event.start).getDate() === day.getDate()\r\n        && new Date(event.start).getMonth() === day.getMonth()\r\n        && new Date(event.start).getFullYear() === day.getFullYear()\r\n        && !event.CIsDelete\r\n      )\r\n        check = true;\r\n    });\r\n    return check;\r\n  }\r\n\r\n  checkDisable(day: Date) {\r\n    if (day.getTime() < new Date().getTime()) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  handleEventClick(clickInfo: any) {\r\n    let eventIndex = this.listEvent$.value.findIndex(x => typeof (x.start) === 'number' ? x.start === clickInfo.event.start.getTime() : x.start.getTime() === clickInfo.event.start.getTime()\r\n      && typeof (x.end) === 'number' ? x.end === clickInfo.event.end.getTime() : x.end.getTime() === clickInfo.event.end.getTime())\r\n    if (eventIndex !== -1) {\r\n      this.listEvent$.value[eventIndex].CIsDelete = true\r\n      this.listEvent$.value[eventIndex].isChange = true\r\n      clickInfo.event.remove();\r\n    }\r\n  }\r\n\r\n  onCheck(e: any, segment: Date) {\r\n    let eventIndex = this.listEvent$.value.findIndex(x => x.start.getFullYear() === segment.getFullYear()\r\n      && x.start.getMonth() === segment.getMonth()\r\n      && x.start.getDate() === segment.getDate()\r\n      && x.CHour === segment.getHours())\r\n\r\n    // click empty checkbox\r\n    if (eventIndex === -1) {\r\n      // create new event\r\n      if (e.target.checked) {\r\n        let temp = [...this.listEvent$.value]\r\n        temp.push({\r\n          CBuildCaseId: this.buildCaseId,\r\n          CDate: moment(segment).format('YYYY-MM-DDTHH:mm:ss'),\r\n          CHour: segment.getHours(),\r\n          CIsDelete: false,\r\n          isChange: true,\r\n          start: segment,\r\n          IsOld: false\r\n        })\r\n        this.listEvent$.next(temp);\r\n      }\r\n    } else { // unchecked checkbox\r\n      if (e.target.checked) {\r\n        this.listEvent$.value[eventIndex].CIsDelete = false\r\n        this.listEvent$.value[eventIndex].isChange = true\r\n      } else {\r\n        this.listEvent$.value[eventIndex].CIsDelete = true\r\n        this.listEvent$.value[eventIndex].isChange = true\r\n      }\r\n    }\r\n  }\r\n\r\n  addNew(ref: any) {\r\n    if (this.handleDataWeek().dataFromCurrent.length > 0) {\r\n      this.dialogService.open(ref);\r\n    } else {\r\n      this.getPreOderSettingRes = this.handleDataWeek().dataFromPrevious;\r\n      const updatedEvents = [...this.listEvent$.value];\r\n      this.handleDataWeek().dataFromPrevious.forEach((e) => {\r\n        updatedEvents.push({\r\n          CBuildCaseId: e.CBuildCaseId,\r\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete,\r\n          isChange: true,\r\n          start: this.add_weeks(e.CDate, 1),\r\n          IsOld: false\r\n        });\r\n      });\r\n      this.listEvent$.next(updatedEvents);\r\n      this.changeDetector.markForCheck();\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close()\r\n  }\r\n\r\n  confirmCopy(ref: any) {\r\n    this.savedData(false).subscribe(() => this.onClose(ref))\r\n  }\r\n\r\n  handleDataWeek() {\r\n    let firstDateofLastWeek = this.sub_weeks(this.viewDate, 1)\r\n    let lastDateofCurrentWeek = this.add_weeks(this.viewDate, 1)\r\n\r\n    let dataFromPrevious = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() >= new Date(firstDateofLastWeek).getTime()\r\n      && new Date(x.CDate).getTime() <= new Date(this.viewDate).getTime())\r\n\r\n    let dataFromCurrent = this.listEvent$.value.filter(x => new Date(x.CDate).getTime() <= new Date(lastDateofCurrentWeek).getTime()\r\n      && new Date(x.CDate).getTime() >= new Date(this.viewDate).getTime())\r\n\r\n    return { dataFromCurrent, dataFromPrevious }\r\n  }\r\n\r\n  handleShowButton() {\r\n    var result = new Date(this.viewDate);\r\n    result.setDate(result.getDate() + 7);\r\n    if (result.getTime() < Date.now()) {\r\n      return false;\r\n    }\r\n    if (this.handleDataWeek().dataFromCurrent.length == 0 && this.handleDataWeek().dataFromPrevious.length == 0) {\r\n      return false;\r\n    }\r\n    return true\r\n  }\r\n\r\n  savedData(needToFilter: boolean) {\r\n    if (needToFilter) {\r\n      this.savePreOrderSetting = this.listEvent$.value.filter(x => x.isChange === true).map(e => {\r\n        this.listDate.push(new Date(e.CDate))\r\n        return {\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: e.CDate,\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        }\r\n      })\r\n    } else {\r\n      this.handleDataWeek().dataFromCurrent.forEach(e => {\r\n        if (this.handleDataWeek().dataFromPrevious.some(x => new Date(e.CDate).getDate() == this.add_weeks(x.CDate, 1).getDate() && x.CHour === e.CHour)) {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: e.CIsDelete\r\n          })\r\n        } else {\r\n          this.savePreOrderSetting.push({\r\n            CBuildCaseID: parseInt(e.CBuildCaseId),\r\n            CDate: this.fromDateToString(this.get_date(e.CDate)),\r\n            CHour: e.CHour,\r\n            CIsDelete: true\r\n          })\r\n        }\r\n      })\r\n      this.handleDataWeek().dataFromPrevious.forEach(e => {\r\n        this.savePreOrderSetting.push({\r\n          CBuildCaseID: parseInt(e.CBuildCaseId),\r\n          CDate: this.fromDateToString(this.add_weeks(e.CDate, 1)),\r\n          CHour: e.CHour,\r\n          CIsDelete: e.CIsDelete\r\n        })\r\n      })\r\n\r\n      this.savePreOrderSetting.filter(x => !x.CIsDelete).forEach(x => {\r\n        this.listDate.push(new Date(x.CDate!))\r\n      })\r\n    }\r\n    if (this.savePreOrderSetting.length == 0) {\r\n      this.backToList()\r\n      return of();\r\n    }\r\n    let minDate = new Date(Math.min.apply(null, this.listDate))\r\n    let paramSave = {\r\n      buildCaseId: this.buildCaseId,\r\n      minDate: minDate\r\n    }\r\n    return this.preOderSettingService.apiPreOrderSettingSavePreOrderSettingPost$Json({\r\n      body: {\r\n        CSavePreOrderSetting: this.savePreOrderSetting\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.messageService.showSucessMSG('Save Successfully!')\r\n          this.savePreOrderSetting = []\r\n          this.listDate = []\r\n          this.listEvent$.value.forEach(x => {\r\n            if (x.isChange) {\r\n              let id = document.getElementById(x.start.toISOString())\r\n              setTimeout(() => {\r\n                id?.click()\r\n              }, 200);\r\n            }\r\n          });\r\n          LocalStorageService.AddLocalStorage('paramSave', JSON.stringify(paramSave))\r\n        }\r\n      }),\r\n      concatMap(() => this.getPreOrderSetting()),\r\n    )\r\n  }\r\n\r\n  add_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() + (n * 7)));\r\n  }\r\n\r\n  sub_weeks(dt: Date, n: number) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate() - (n * 7)));\r\n  }\r\n\r\n  get_date(dt: Date) {\r\n    return new Date(new Date(dt).setDate(new Date(dt).getDate()));\r\n  }\r\n\r\n  fromDateToString(date: any) {\r\n    date = new Date(+date);\r\n    date.setTime(date.getTime() - (date.getTimezoneOffset() * 60000));\r\n    let dateAsString = date.toISOString().substr(0, 19);\r\n    return dateAsString;\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,uBAAuB,EAAqBC,SAAS,QAAmC,eAAe;AAEhH,SAASC,mBAAmB,QAAQ,+CAA+C;AAInF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAkB,iBAAiB;AACxD,SAASC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AAC/D,SAASC,YAAY,QAAQ,gBAAgB;AAE7C,SAASC,eAAe,EAAEC,SAAS,EAAYC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACpE,SAASC,YAAY,QAAQ,wCAAwC;AAgB9D,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EAezCC,YACUC,cAAiC,EACjCC,qBAA6C,EAC7CC,KAAqB,EACrBC,OAAe,EACfC,cAA8B,EAC9BC,SAAmB,EACnBC,aAA8B,EAC9BC,aAA2B;IAP3B,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IAtBvB,KAAAC,MAAM,GAAW,IAAI;IAErB,KAAAC,oBAAoB,GAAG,EAAkC;IACzD,KAAAC,eAAe,GAAY,IAAI;IAC/B,KAAAC,mBAAmB,GAAG,EAAuB;IAE7C,KAAAC,UAAU,GAA2B,IAAInB,eAAe,CAAQ,EAAE,CAAC;IAEnE,KAAAoB,IAAI,GAAiBtB,YAAY,CAACuB,IAAI;IACtC,KAAAC,QAAQ,GAAS,IAAIC,IAAI,EAAE;IAC3B,KAAAC,SAAS,GAAQ,IAAI;IACrB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,IAAI,GAAG,CAAC;IAYN,IAAI,CAACC,WAAW,GAAG,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACzD,IAAI,CAACN,SAAS,GAAGO,IAAI,CAACC,KAAK,CAACtC,mBAAmB,CAACuC,eAAe,CAAC,WAAW,CAAC,CAAC;EAC/E;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,EAAE;MAClB,IAAI,CAACF,QAAQ,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACC,SAAS,CAACW,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,kBAAkB,EAAE,CAACC,SAAS,EAAE;EACvC;EAEAD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC5B,qBAAqB,CAAC8B,6CAA6C,CAAC;MAC9EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACb;;KAEtB,CAAC,CAACc,IAAI,CACLtC,GAAG,CAACuC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC3B,oBAAoB,GAAG0B,GAAG,CAACE,OAAO,GAAGF,GAAG,CAACE,OAAO,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,GAAGD,CAAC,CAACC,KAAK,GAAG,CAAC,IAAID,CAAC,CAACC,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;QACpH,IAAI,CAACC,SAAS,EAAE;MAClB;IACF,CAAC,CAAC,CACH;EACH;EAEAA,SAASA,CAAA;IACP,IAAIC,IAAI,GAAW,EAAE;IACrB,IAAI,IAAI,CAACjC,oBAAoB,IAAI,IAAI,CAACA,oBAAoB,CAACkC,MAAM,GAAG,CAAC,EAAE;MACrE,IAAI,CAAClC,oBAAoB,CAACmC,OAAO,CAACC,CAAC,IAAG;QACpC,IAAIA,CAAC,CAACL,KAAK,IAAIK,CAAC,CAACL,KAAK,GAAG,CAAC,IAAIK,CAAC,CAACL,KAAK,GAAG,EAAE,EAAE;UAC1C,IAAIM,IAAI,GAAGD,CAAC,CAACE,KAAK,GAAG,IAAI/B,IAAI,CAAC6B,CAAC,CAACE,KAAK,CAAC,GAAGC,SAAS;UAClDF,IAAI,GAAGA,IAAI,GAAG,IAAI9B,IAAI,CAAC8B,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGH,IAAI;UACjDA,IAAI,GAAGA,IAAI,GAAG,IAAI9B,IAAI,CAAC8B,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGJ,IAAI;UACjD,IAAIK,SAAS,GAAGL,IAAI,IAAID,CAAC,CAACL,KAAK,GAAG,IAAIxB,IAAI,CAAC8B,IAAI,CAACM,QAAQ,CAACP,CAAC,CAACL,KAAK,CAAC,CAAC,GAAG,CAAC;UACtE,IAAIa,OAAO,GAAGP,IAAI,IAAID,CAAC,CAACL,KAAK,GAAG,IAAIxB,IAAI,CAAC8B,IAAI,CAACM,QAAQ,CAACP,CAAC,CAACL,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;UACxEE,IAAI,CAACY,IAAI,CAAC;YACRC,EAAE,EAAEV,CAAC,CAACW,GAAG;YACTC,YAAY,EAAE,IAAI,CAACrC,WAAW;YAC9B2B,KAAK,EAAEF,CAAC,CAACE,KAAK;YACdP,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdkB,SAAS,EAAE,KAAK;YAChBC,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAET,SAAS;YAChBU,GAAG,EAAER,OAAO;YACZS,OAAO,EAAE,YAAY;YACrBC,KAAK,EAAE;WACR,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAACnD,UAAU,CAACoD,IAAI,CAACtB,IAAI,CAAC;EAC5B;EAEAuB,gBAAgBA,CAACC,UAAe;IAC9B,IAAIA,UAAU,CAACN,KAAK,CAACO,OAAO,EAAE,KAAKD,UAAU,CAACL,GAAG,CAACM,OAAO,EAAE,IAAID,UAAU,CAACL,GAAG,CAACO,QAAQ,EAAE,GAAGF,UAAU,CAACN,KAAK,CAACQ,QAAQ,EAAE,KAAK,CAAC,EAAE;MAC5H,IAAI1B,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC9B,UAAU,CAACyD,KAAK,CAAC;MACrC,MAAMC,WAAW,GAAGJ,UAAU,CAACrD,IAAI,CAAC0D,QAAQ;MAE5CD,WAAW,CAACE,QAAQ,EAAE,CAAC,CAAC;MAExBF,WAAW,CAACG,QAAQ,CAAC;QACnBb,KAAK,EAAEM,UAAU,CAACQ,QAAQ;QAC1Bb,GAAG,EAAEK,UAAU,CAACS,MAAM;QACtBb,OAAO,EAAE;OACV,CAAC;MAEFpB,IAAI,CAACY,IAAI,CAAC;QACRG,YAAY,EAAE,IAAI,CAACrC,WAAW;QAC9B2B,KAAK,EAAEmB,UAAU,CAACQ,QAAQ;QAC1BlC,KAAK,EAAE0B,UAAU,CAACN,KAAK,CAACQ,QAAQ,EAAE;QAClCV,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAEM,UAAU,CAACN,KAAK;QACvBC,GAAG,EAAEK,UAAU,CAACL;OACjB,CAAC;MACF,IAAI,CAACjD,UAAU,CAACoD,IAAI,CAACtB,IAAI,CAAC;IAC5B;EACF;EAEAkC,IAAIA,CAAA;IACF,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC,CAAC/C,SAAS,CAACK,GAAG,IAAG;MACnC,IAAI,CAAC2C,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEAA,UAAUA,CAAA;IACR,IAAI,CAACvE,aAAa,CAAC+C,IAAI,CAAC;MACtByB,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC5D;KACf,CAAC;IACF,IAAI,CAACf,SAAS,CAAC4E,IAAI,EAAE;EACvB;EAEA;EACAC,WAAWA,CAACC,GAAS;IACnB,MAAMC,SAAS,GAAGD,GAAG,CAACf,QAAQ,EAAE;IAChC,IAAIiB,KAAK,GAAG,KAAK;IACjB,IAAI,CAACzE,UAAU,CAACyD,KAAK,CAACiB,IAAI,CAACC,KAAK,IAAG;MACjC;MACA,IAAIA,KAAK,CAAC/C,KAAK,KAAK4C,SAAS,IACxB,IAAIpE,IAAI,CAACuE,KAAK,CAAC3B,KAAK,CAAC,CAACO,OAAO,EAAE,KAAKgB,GAAG,CAAChB,OAAO,EAAE,IACjD,IAAInD,IAAI,CAACuE,KAAK,CAAC3B,KAAK,CAAC,CAAC4B,QAAQ,EAAE,KAAKL,GAAG,CAACK,QAAQ,EAAE,IACnD,IAAIxE,IAAI,CAACuE,KAAK,CAAC3B,KAAK,CAAC,CAAC6B,WAAW,EAAE,KAAKN,GAAG,CAACM,WAAW,EAAE,IACzD,CAACF,KAAK,CAAC7B,SAAS,EAEnB2B,KAAK,GAAG,IAAI;IAChB,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;EAEAK,YAAYA,CAACP,GAAS;IACpB,IAAIA,GAAG,CAACQ,OAAO,EAAE,GAAG,IAAI3E,IAAI,EAAE,CAAC2E,OAAO,EAAE,EAAE;MACxC,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAC,gBAAgBA,CAACC,SAAc;IAC7B,IAAIC,UAAU,GAAG,IAAI,CAAClF,UAAU,CAACyD,KAAK,CAAC0B,SAAS,CAACxD,CAAC,IAAI,OAAQA,CAAC,CAACqB,KAAM,KAAK,QAAQ,GAAGrB,CAAC,CAACqB,KAAK,KAAKiC,SAAS,CAACN,KAAK,CAAC3B,KAAK,CAAC+B,OAAO,EAAE,GAAGpD,CAAC,CAACqB,KAAK,CAAC+B,OAAO,EAAE,KAAKE,SAAS,CAACN,KAAK,CAAC3B,KAAK,CAAC+B,OAAO,EAAE,IACpL,OAAQpD,CAAC,CAACsB,GAAI,KAAK,QAAQ,GAAGtB,CAAC,CAACsB,GAAG,KAAKgC,SAAS,CAACN,KAAK,CAAC1B,GAAG,CAAC8B,OAAO,EAAE,GAAGpD,CAAC,CAACsB,GAAG,CAAC8B,OAAO,EAAE,KAAKE,SAAS,CAACN,KAAK,CAAC1B,GAAG,CAAC8B,OAAO,EAAE,CAAC;IAC/H,IAAIG,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAI,CAAClF,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACpC,SAAS,GAAG,IAAI;MAClD,IAAI,CAAC9C,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACnC,QAAQ,GAAG,IAAI;MACjDkC,SAAS,CAACN,KAAK,CAACS,MAAM,EAAE;IAC1B;EACF;EAEAC,OAAOA,CAACpD,CAAM,EAAEqD,OAAa;IAC3B,IAAIJ,UAAU,GAAG,IAAI,CAAClF,UAAU,CAACyD,KAAK,CAAC0B,SAAS,CAACxD,CAAC,IAAIA,CAAC,CAACqB,KAAK,CAAC6B,WAAW,EAAE,KAAKS,OAAO,CAACT,WAAW,EAAE,IAChGlD,CAAC,CAACqB,KAAK,CAAC4B,QAAQ,EAAE,KAAKU,OAAO,CAACV,QAAQ,EAAE,IACzCjD,CAAC,CAACqB,KAAK,CAACO,OAAO,EAAE,KAAK+B,OAAO,CAAC/B,OAAO,EAAE,IACvC5B,CAAC,CAACC,KAAK,KAAK0D,OAAO,CAAC9B,QAAQ,EAAE,CAAC;IAEpC;IACA,IAAI0B,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB;MACA,IAAIjD,CAAC,CAACsD,MAAM,CAACC,OAAO,EAAE;QACpB,IAAI1D,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC9B,UAAU,CAACyD,KAAK,CAAC;QACrC3B,IAAI,CAACY,IAAI,CAAC;UACRG,YAAY,EAAE,IAAI,CAACrC,WAAW;UAC9B2B,KAAK,EAAE3D,MAAM,CAAC8G,OAAO,CAAC,CAACG,MAAM,CAAC,qBAAqB,CAAC;UACpD7D,KAAK,EAAE0D,OAAO,CAAC9B,QAAQ,EAAE;UACzBV,SAAS,EAAE,KAAK;UAChBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAEsC,OAAO;UACdnC,KAAK,EAAE;SACR,CAAC;QACF,IAAI,CAACnD,UAAU,CAACoD,IAAI,CAACtB,IAAI,CAAC;MAC5B;IACF,CAAC,MAAM;MAAE;MACP,IAAIG,CAAC,CAACsD,MAAM,CAACC,OAAO,EAAE;QACpB,IAAI,CAACxF,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACpC,SAAS,GAAG,KAAK;QACnD,IAAI,CAAC9C,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACnC,QAAQ,GAAG,IAAI;MACnD,CAAC,MAAM;QACL,IAAI,CAAC/C,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACpC,SAAS,GAAG,IAAI;QAClD,IAAI,CAAC9C,UAAU,CAACyD,KAAK,CAACyB,UAAU,CAAC,CAACnC,QAAQ,GAAG,IAAI;MACnD;IACF;EACF;EAEA2C,MAAMA,CAACC,GAAQ;IACb,IAAI,IAAI,CAACC,cAAc,EAAE,CAACC,eAAe,CAAC9D,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACrC,aAAa,CAACoG,IAAI,CAACH,GAAG,CAAC;IAC9B,CAAC,MAAM;MACL,IAAI,CAAC9F,oBAAoB,GAAG,IAAI,CAAC+F,cAAc,EAAE,CAACG,gBAAgB;MAClE,MAAMC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAChG,UAAU,CAACyD,KAAK,CAAC;MAChD,IAAI,CAACmC,cAAc,EAAE,CAACG,gBAAgB,CAAC/D,OAAO,CAAEC,CAAC,IAAI;QACnD+D,aAAa,CAACtD,IAAI,CAAC;UACjBG,YAAY,EAAEZ,CAAC,CAACY,YAAY;UAC5BV,KAAK,EAAE,IAAI,CAAC8D,gBAAgB,CAAC,IAAI,CAACC,SAAS,CAACjE,CAAC,CAACE,KAAK,EAAE,CAAC,CAAC,CAAC;UACxDP,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdkB,SAAS,EAAEb,CAAC,CAACa,SAAS;UACtBC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE,IAAI,CAACkD,SAAS,CAACjE,CAAC,CAACE,KAAK,EAAE,CAAC,CAAC;UACjCgB,KAAK,EAAE;SACR,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnD,UAAU,CAACoD,IAAI,CAAC4C,aAAa,CAAC;MACnC,IAAI,CAAC5G,cAAc,CAAC+G,YAAY,EAAE;IACpC;EACF;EAEAC,OAAOA,CAACT,GAAQ;IACdA,GAAG,CAACU,KAAK,EAAE;EACb;EAEAC,WAAWA,CAACX,GAAQ;IAClB,IAAI,CAAC1B,SAAS,CAAC,KAAK,CAAC,CAAC/C,SAAS,CAAC,MAAM,IAAI,CAACkF,OAAO,CAACT,GAAG,CAAC,CAAC;EAC1D;EAEAC,cAAcA,CAAA;IACZ,IAAIW,mBAAmB,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACrG,QAAQ,EAAE,CAAC,CAAC;IAC1D,IAAIsG,qBAAqB,GAAG,IAAI,CAACP,SAAS,CAAC,IAAI,CAAC/F,QAAQ,EAAE,CAAC,CAAC;IAE5D,IAAI4F,gBAAgB,GAAG,IAAI,CAAC/F,UAAU,CAACyD,KAAK,CAAC/B,MAAM,CAACC,CAAC,IAAI,IAAIvB,IAAI,CAACuB,CAAC,CAACQ,KAAK,CAAC,CAAC4C,OAAO,EAAE,IAAI,IAAI3E,IAAI,CAACmG,mBAAmB,CAAC,CAACxB,OAAO,EAAE,IAC1H,IAAI3E,IAAI,CAACuB,CAAC,CAACQ,KAAK,CAAC,CAAC4C,OAAO,EAAE,IAAI,IAAI3E,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC4E,OAAO,EAAE,CAAC;IAEtE,IAAIc,eAAe,GAAG,IAAI,CAAC7F,UAAU,CAACyD,KAAK,CAAC/B,MAAM,CAACC,CAAC,IAAI,IAAIvB,IAAI,CAACuB,CAAC,CAACQ,KAAK,CAAC,CAAC4C,OAAO,EAAE,IAAI,IAAI3E,IAAI,CAACqG,qBAAqB,CAAC,CAAC1B,OAAO,EAAE,IAC3H,IAAI3E,IAAI,CAACuB,CAAC,CAACQ,KAAK,CAAC,CAAC4C,OAAO,EAAE,IAAI,IAAI3E,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC4E,OAAO,EAAE,CAAC;IAEtE,OAAO;MAAEc,eAAe;MAAEE;IAAgB,CAAE;EAC9C;EAEAW,gBAAgBA,CAAA;IACd,IAAIC,MAAM,GAAG,IAAIvG,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC;IACpCwG,MAAM,CAACC,OAAO,CAACD,MAAM,CAACpD,OAAO,EAAE,GAAG,CAAC,CAAC;IACpC,IAAIoD,MAAM,CAAC5B,OAAO,EAAE,GAAG3E,IAAI,CAACyG,GAAG,EAAE,EAAE;MACjC,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACjB,cAAc,EAAE,CAACC,eAAe,CAAC9D,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC6D,cAAc,EAAE,CAACG,gBAAgB,CAAChE,MAAM,IAAI,CAAC,EAAE;MAC3G,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEAkC,SAASA,CAAC6C,YAAqB;IAC7B,IAAIA,YAAY,EAAE;MAChB,IAAI,CAAC/G,mBAAmB,GAAG,IAAI,CAACC,UAAU,CAACyD,KAAK,CAAC/B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACoB,QAAQ,KAAK,IAAI,CAAC,CAACgE,GAAG,CAAC9E,CAAC,IAAG;QACxF,IAAI,CAAC3B,QAAQ,CAACoC,IAAI,CAAC,IAAItC,IAAI,CAAC6B,CAAC,CAACE,KAAK,CAAC,CAAC;QACrC,OAAO;UACLd,YAAY,EAAE2F,QAAQ,CAAC/E,CAAC,CAACY,YAAY,CAAC;UACtCV,KAAK,EAAEF,CAAC,CAACE,KAAK;UACdP,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdkB,SAAS,EAAEb,CAAC,CAACa;SACd;MACH,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC8C,cAAc,EAAE,CAACC,eAAe,CAAC7D,OAAO,CAACC,CAAC,IAAG;QAChD,IAAI,IAAI,CAAC2D,cAAc,EAAE,CAACG,gBAAgB,CAACrB,IAAI,CAAC/C,CAAC,IAAI,IAAIvB,IAAI,CAAC6B,CAAC,CAACE,KAAK,CAAC,CAACoB,OAAO,EAAE,IAAI,IAAI,CAAC2C,SAAS,CAACvE,CAAC,CAACQ,KAAK,EAAE,CAAC,CAAC,CAACoB,OAAO,EAAE,IAAI5B,CAAC,CAACC,KAAK,KAAKK,CAAC,CAACL,KAAK,CAAC,EAAE;UAChJ,IAAI,CAAC7B,mBAAmB,CAAC2C,IAAI,CAAC;YAC5BrB,YAAY,EAAE2F,QAAQ,CAAC/E,CAAC,CAACY,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAAC8D,gBAAgB,CAAC,IAAI,CAACgB,QAAQ,CAAChF,CAAC,CAACE,KAAK,CAAC,CAAC;YACpDP,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdkB,SAAS,EAAEb,CAAC,CAACa;WACd,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAC/C,mBAAmB,CAAC2C,IAAI,CAAC;YAC5BrB,YAAY,EAAE2F,QAAQ,CAAC/E,CAAC,CAACY,YAAY,CAAC;YACtCV,KAAK,EAAE,IAAI,CAAC8D,gBAAgB,CAAC,IAAI,CAACgB,QAAQ,CAAChF,CAAC,CAACE,KAAK,CAAC,CAAC;YACpDP,KAAK,EAAEK,CAAC,CAACL,KAAK;YACdkB,SAAS,EAAE;WACZ,CAAC;QACJ;MACF,CAAC,CAAC;MACF,IAAI,CAAC8C,cAAc,EAAE,CAACG,gBAAgB,CAAC/D,OAAO,CAACC,CAAC,IAAG;QACjD,IAAI,CAAClC,mBAAmB,CAAC2C,IAAI,CAAC;UAC5BrB,YAAY,EAAE2F,QAAQ,CAAC/E,CAAC,CAACY,YAAY,CAAC;UACtCV,KAAK,EAAE,IAAI,CAAC8D,gBAAgB,CAAC,IAAI,CAACC,SAAS,CAACjE,CAAC,CAACE,KAAK,EAAE,CAAC,CAAC,CAAC;UACxDP,KAAK,EAAEK,CAAC,CAACL,KAAK;UACdkB,SAAS,EAAEb,CAAC,CAACa;SACd,CAAC;MACJ,CAAC,CAAC;MAEF,IAAI,CAAC/C,mBAAmB,CAAC2B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACmB,SAAS,CAAC,CAACd,OAAO,CAACL,CAAC,IAAG;QAC7D,IAAI,CAACrB,QAAQ,CAACoC,IAAI,CAAC,IAAItC,IAAI,CAACuB,CAAC,CAACQ,KAAM,CAAC,CAAC;MACxC,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACpC,mBAAmB,CAACgC,MAAM,IAAI,CAAC,EAAE;MACxC,IAAI,CAACmC,UAAU,EAAE;MACjB,OAAOnF,EAAE,EAAE;IACb;IACA,IAAImI,OAAO,GAAG,IAAI9G,IAAI,CAAC+G,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC/G,QAAQ,CAAC,CAAC;IAC3D,IAAIgH,SAAS,GAAG;MACd9G,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B0G,OAAO,EAAEA;KACV;IACD,OAAO,IAAI,CAAC7H,qBAAqB,CAACkI,8CAA8C,CAAC;MAC/EnG,IAAI,EAAE;QACJoG,oBAAoB,EAAE,IAAI,CAACzH;;KAE9B,CAAC,CAACuB,IAAI,CACLtC,GAAG,CAACuC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChC,cAAc,CAACiI,aAAa,CAAC,oBAAoB,CAAC;QACvD,IAAI,CAAC1H,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAACO,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACN,UAAU,CAACyD,KAAK,CAACzB,OAAO,CAACL,CAAC,IAAG;UAChC,IAAIA,CAAC,CAACoB,QAAQ,EAAE;YACd,IAAIJ,EAAE,GAAG+E,QAAQ,CAACC,cAAc,CAAChG,CAAC,CAACqB,KAAK,CAAC4E,WAAW,EAAE,CAAC;YACvDC,UAAU,CAAC,MAAK;cACdlF,EAAE,EAAEmF,KAAK,EAAE;YACb,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,CAAC;QACFvJ,mBAAmB,CAACwJ,eAAe,CAAC,WAAW,EAAEnH,IAAI,CAACoH,SAAS,CAACV,SAAS,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC,EACFxI,SAAS,CAAC,MAAM,IAAI,CAACmC,kBAAkB,EAAE,CAAC,CAC3C;EACH;EAEAiF,SAASA,CAAC+B,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAI9H,IAAI,CAAC,IAAIA,IAAI,CAAC6H,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxG,IAAI,CAAC6H,EAAE,CAAC,CAAC1E,OAAO,EAAE,GAAI2E,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEA1B,SAASA,CAACyB,EAAQ,EAAEC,CAAS;IAC3B,OAAO,IAAI9H,IAAI,CAAC,IAAIA,IAAI,CAAC6H,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxG,IAAI,CAAC6H,EAAE,CAAC,CAAC1E,OAAO,EAAE,GAAI2E,CAAC,GAAG,CAAE,CAAC,CAAC;EACzE;EAEAjB,QAAQA,CAACgB,EAAQ;IACf,OAAO,IAAI7H,IAAI,CAAC,IAAIA,IAAI,CAAC6H,EAAE,CAAC,CAACrB,OAAO,CAAC,IAAIxG,IAAI,CAAC6H,EAAE,CAAC,CAAC1E,OAAO,EAAE,CAAC,CAAC;EAC/D;EAEA0C,gBAAgBA,CAAC/D,IAAS;IACxBA,IAAI,GAAG,IAAI9B,IAAI,CAAC,CAAC8B,IAAI,CAAC;IACtBA,IAAI,CAACiG,OAAO,CAACjG,IAAI,CAAC6C,OAAO,EAAE,GAAI7C,IAAI,CAACkG,iBAAiB,EAAE,GAAG,KAAM,CAAC;IACjE,IAAIC,YAAY,GAAGnG,IAAI,CAAC0F,WAAW,EAAE,CAACU,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IACnD,OAAOD,YAAY;EACrB;CAED;AAjVYnJ,8BAA8B,GAAAqJ,UAAA,EAb1CjK,SAAS,CAAC;EACTkK,QAAQ,EAAE,8BAA8B;EACxCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPjK,YAAY,EACZQ,YAAY,EACZP,cAAc,EACdE,YAAY,CACb;EACD+J,WAAW,EAAE,2CAA2C;EACxDC,SAAS,EAAE,CAAC,2CAA2C,CAAC;EACxDC,eAAe,EAAExK,uBAAuB,CAACyK;CAC1C,CAAC,C,EACW5J,8BAA8B,CAiV1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}