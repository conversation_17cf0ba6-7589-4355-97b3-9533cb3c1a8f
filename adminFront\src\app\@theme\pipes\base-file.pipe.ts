// import { Pipe, PipeTransform } from '@angular/core';
// import { environment } from '../../../environments/environment';

// @Pipe({
//     name: 'addBaseFile',
//     standalone: true
// })
// export class BaseFilePipe implements PipeTransform {
//     readonly BASE_FILE = environment.BASE_FILE;
//     transform(value: string | null | undefined ): string | null | undefined {
//         if (!value) return value;
//         return this.BASE_FILE + value
//     }
// }

import { Pipe, PipeTransform } from '@angular/core';
import { environment } from '../../../environments/environment';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Pipe({
    name: 'addBaseFile',
    standalone: true
})
export class BaseFilePipe implements PipeTransform {
    constructor(private sanitizer: DomSanitizer) { }
    readonly BASE_FILE = environment.BASE_FILE;
    transform(value: string | null | undefined): string | null | undefined | SafeUrl {
        if (!value) return value;
        if (value.includes("/Files")) {
            if (value.includes(environment.BASE_WITHOUT_FILEROOT)) {
                return this.sanitizer.bypassSecurityTrustResourceUrl(value)
            }
            return this.sanitizer.bypassSecurityTrustResourceUrl(environment.BASE_WITHOUT_FILEROOT + value)
        }
        return this.sanitizer.bypassSecurityTrustResourceUrl(this.BASE_FILE + value)
    }
}