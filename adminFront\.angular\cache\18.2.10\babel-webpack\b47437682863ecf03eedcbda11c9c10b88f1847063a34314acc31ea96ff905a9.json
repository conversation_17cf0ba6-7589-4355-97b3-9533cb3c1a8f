{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/services/api/services\";\nimport * as i5 from \"src/app/shared/services/event.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@nebular/theme\";\nimport * as i9 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nfunction ContentManagementLandownerComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r1.CBuildCaseName, \" \");\n  }\n}\nfunction ContentManagementLandownerComponent_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_15_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.unLock());\n    });\n    i0.ɵɵtext(1, \" \\u89E3\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLock());\n    });\n    i0.ɵɵtext(1, \" \\u9396\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F\\u5167\\u5BB9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ContentManagementLandownerComponent_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navidateDetai());\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContentManagementLandownerComponent_tr_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.$implicit;\n    const ix_r8 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ix_r8 > 0 ? \"\\u3001\" : \"\", \" \", i_r7.CHousehold, \" \");\n  }\n}\nfunction ContentManagementLandownerComponent_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtemplate(4, ContentManagementLandownerComponent_tr_28_span_4_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r9.CItemName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", item_r9.tblFormItemHouseholds);\n  }\n}\nexport let ContentManagementLandownerComponent = /*#__PURE__*/(() => {\n  class ContentManagementLandownerComponent extends BaseComponent {\n    constructor(_allow, router, message, _buildCaseService, _formItemService, _eventService) {\n      super(_allow);\n      this._allow = _allow;\n      this.router = router;\n      this.message = message;\n      this._buildCaseService = _buildCaseService;\n      this._formItemService = _formItemService;\n      this._eventService = _eventService;\n      this.tempBuildCaseID = -1;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this.pageSize = 20;\n      this.typeContentManagementLandowner = {\n        CFormType: 1,\n        CNoticeType: 1\n      };\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    navidateDetai() {\n      this.router.navigate([`pages/content-management-landowner/${this.cBuildCaseSelected.cID}`]);\n    }\n    getListFormItem() {\n      this._formItemService.apiFormItemGetListFormItemPost$Json({\n        body: {\n          CBuildCaseId: this.cBuildCaseSelected.cID,\n          CFormType: this.typeContentManagementLandowner.CFormType,\n          PageIndex: this.pageIndex,\n          PageSize: this.pageSize,\n          CIsPaging: true\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.formItems = res.Entries.formItems;\n          this.listFormItem = res.Entries;\n          this.totalRecords = res.TotalItems ? res.TotalItems : 0;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.cBuildCaseSelected = null;\n      this.getUserBuildCase();\n    }\n    onSelectionChangeBuildCase() {\n      this.getListFormItem();\n    }\n    getUserBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n        body: {}\n      }).pipe(tap(res => {\n        // if (res.Entries && res.StatusCode == 0) {\n        //   this.userBuildCaseOptions = res.Entries.map(res => {\n        //     return {\n        //       CBuildCaseName: res.CBuildCaseName,\n        //       cID: res.cID\n        //     }\n        //   })\n        //   this.cBuildCaseSelected = this.userBuildCaseOptions[0]\n        //   if (this.cBuildCaseSelected.cID) {\n        //     this.getListFormItem()\n        //   }\n        // }\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries.map(res => {\n            return {\n              CBuildCaseName: res.CBuildCaseName,\n              cID: res.cID\n            };\n          });\n          if (this.tempBuildCaseID != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n            this.cBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.cBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n          if (this.cBuildCaseSelected.cID) {\n            this.getListFormItem();\n          }\n        }\n      })).subscribe();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getListFormItem();\n    }\n    onLock() {\n      this._formItemService.apiFormItemLockFormItemPost$Json({\n        body: {\n          CBuildCaseId: this.cBuildCaseSelected.cID,\n          CFormId: this.listFormItem.CFormId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        this.getListFormItem();\n      });\n    }\n    unLock() {\n      this._formItemService.apiFormItemUnlockFormItemPost$Json({\n        body: {\n          CBuildCaseID: this.cBuildCaseSelected.cID,\n          CFormId: this.listFormItem.CFormId\n        }\n      }).subscribe(res => {\n        if (res.StatusCode == 0) {\n          this.message.showSucessMSG(\"執行成功\");\n        } else {\n          this.message.showErrorMSG(res.Message);\n        }\n        this.getListFormItem();\n      });\n    }\n    static {\n      this.ɵfac = function ContentManagementLandownerComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ContentManagementLandownerComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.BuildCaseService), i0.ɵɵdirectiveInject(i4.FormItemService), i0.ɵɵdirectiveInject(i5.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContentManagementLandownerComponent,\n        selectors: [[\"ngx-content-management-landowner\"]],\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 31,\n        vars: 10,\n        consts: [[\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [\"class\", \"btn btn-warning mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-secondary mx-2\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-warning\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-info\", 3, \"click\"]],\n        template: function ContentManagementLandownerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 1);\n            i0.ɵɵtext(5, \" \\u60A8\\u53EF\\u5C07\\u65BC\\u5EFA\\u6750\\u7BA1\\u7406\\u53CA\\u65B9\\u6848\\u7BA1\\u7406\\u8A2D\\u5B9A\\u597D\\u7684\\u65B9\\u6848\\u53CA\\u6750\\u6599\\uFF0C\\u65BC\\u6B64\\u7D44\\u5408\\u6210\\u9078\\u6A23\\u5167\\u5BB9\\uFF0C\\u4E26\\u53EF\\u8A2D\\u5B9A\\u5404\\u65B9\\u6848\\u3001\\u6750\\u6599\\u53EF\\u9078\\u64C7\\u4E4B\\u6236\\u578B\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 2)(7, \"div\", 3)(8, \"div\", 4)(9, \"label\", 5);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 6);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ContentManagementLandownerComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.cBuildCaseSelected, $event) || (ctx.cBuildCaseSelected = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"selectedChange\", function ContentManagementLandownerComponent_Template_nb_select_selectedChange_11_listener() {\n              return ctx.onSelectionChangeBuildCase();\n            });\n            i0.ɵɵtemplate(12, ContentManagementLandownerComponent_nb_option_12_Template, 2, 2, \"nb-option\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 3)(14, \"div\", 8);\n            i0.ɵɵtemplate(15, ContentManagementLandownerComponent_button_15_Template, 2, 0, \"button\", 9)(16, ContentManagementLandownerComponent_button_16_Template, 2, 0, \"button\", 10)(17, ContentManagementLandownerComponent_button_17_Template, 2, 0, \"button\", 11)(18, ContentManagementLandownerComponent_button_18_Template, 2, 0, \"button\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 12)(20, \"table\", 13)(21, \"thead\")(22, \"tr\", 14)(23, \"th\", 15);\n            i0.ɵɵtext(24, \"\\u65B9\\u6848\\u540D\\u7A31/\\u5EFA\\u6750\\u4F4D\\u7F6E\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"th\", 15);\n            i0.ɵɵtext(26, \"\\u9069\\u7528\\u6236\\u5225 \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(27, \"tbody\");\n            i0.ɵɵtemplate(28, ContentManagementLandownerComponent_tr_28_Template, 5, 2, \"tr\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(29, \"nb-card-footer\", 17)(30, \"ngb-pagination\", 18);\n            i0.ɵɵtwoWayListener(\"pageChange\", function ContentManagementLandownerComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"pageChange\", function ContentManagementLandownerComponent_Template_ngb_pagination_pageChange_30_listener($event) {\n              return ctx.pageChanged($event);\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.cBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.CIsLock === false && ctx.isUpdate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.CIsLock == false && ctx.isUpdate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && ctx.listFormItem.formItems && ctx.isUpdate);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.listFormItem && !ctx.listFormItem.formItems && ctx.isCreate);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngForOf\", ctx.formItems);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, SharedModule, i7.NgControlStatus, i7.NgModel, i8.NbCardComponent, i8.NbCardBodyComponent, i8.NbCardFooterComponent, i8.NbCardHeaderComponent, i8.NbSelectComponent, i8.NbOptionComponent, i9.NgbPagination, i10.BreadcrumbComponent]\n      });\n    }\n  }\n  return ContentManagementLandownerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}