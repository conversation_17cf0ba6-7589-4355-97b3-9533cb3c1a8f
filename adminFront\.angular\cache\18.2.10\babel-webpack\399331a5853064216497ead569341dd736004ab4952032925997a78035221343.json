{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiReviewDeleteReviewPost$Json } from '../fn/review/api-review-delete-review-post-json';\nimport { apiReviewDeleteReviewPost$Plain } from '../fn/review/api-review-delete-review-post-plain';\nimport { apiReviewGetReviewByIdPost$Json } from '../fn/review/api-review-get-review-by-id-post-json';\nimport { apiReviewGetReviewByIdPost$Plain } from '../fn/review/api-review-get-review-by-id-post-plain';\nimport { apiReviewGetReviewListPost$Json } from '../fn/review/api-review-get-review-list-post-json';\nimport { apiReviewGetReviewListPost$Plain } from '../fn/review/api-review-get-review-list-post-plain';\nimport { apiReviewSaveReviewPost$Json } from '../fn/review/api-review-save-review-post-json';\nimport { apiReviewSaveReviewPost$Plain } from '../fn/review/api-review-save-review-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class ReviewService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiReviewGetReviewListPost()` */\n  static {\n    this.ApiReviewGetReviewListPostPath = '/api/Review/GetReviewList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewGetReviewListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewListPost$Plain$Response(params, context) {\n    return apiReviewGetReviewListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewListPost$Plain(params, context) {\n    return this.apiReviewGetReviewListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewGetReviewListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewListPost$Json$Response(params, context) {\n    return apiReviewGetReviewListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewListPost$Json(params, context) {\n    return this.apiReviewGetReviewListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiReviewDeleteReviewPost()` */\n  static {\n    this.ApiReviewDeleteReviewPostPath = '/api/Review/DeleteReview';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewDeleteReviewPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewDeleteReviewPost$Plain$Response(params, context) {\n    return apiReviewDeleteReviewPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewDeleteReviewPost$Plain(params, context) {\n    return this.apiReviewDeleteReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewDeleteReviewPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewDeleteReviewPost$Json$Response(params, context) {\n    return apiReviewDeleteReviewPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewDeleteReviewPost$Json(params, context) {\n    return this.apiReviewDeleteReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiReviewSaveReviewPost()` */\n  static {\n    this.ApiReviewSaveReviewPostPath = '/api/Review/SaveReview';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewSaveReviewPost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiReviewSaveReviewPost$Plain$Response(params, context) {\n    return apiReviewSaveReviewPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiReviewSaveReviewPost$Plain(params, context) {\n    return this.apiReviewSaveReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewSaveReviewPost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiReviewSaveReviewPost$Json$Response(params, context) {\n    return apiReviewSaveReviewPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiReviewSaveReviewPost$Json(params, context) {\n    return this.apiReviewSaveReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiReviewGetReviewByIdPost()` */\n  static {\n    this.ApiReviewGetReviewByIdPostPath = '/api/Review/GetReviewById';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewGetReviewByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewByIdPost$Plain$Response(params, context) {\n    return apiReviewGetReviewByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewByIdPost$Plain(params, context) {\n    return this.apiReviewGetReviewByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiReviewGetReviewByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewByIdPost$Json$Response(params, context) {\n    return apiReviewGetReviewByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiReviewGetReviewByIdPost$Json(params, context) {\n    return this.apiReviewGetReviewByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function ReviewService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReviewService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ReviewService,\n      factory: ReviewService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiReviewDeleteReviewPost$Json", "apiReviewDeleteReviewPost$Plain", "apiReviewGetReviewByIdPost$Json", "apiReviewGetReviewByIdPost$Plain", "apiReviewGetReviewListPost$Json", "apiReviewGetReviewListPost$Plain", "apiReviewSaveReviewPost$Json", "apiReviewSaveReviewPost$Plain", "ReviewService", "constructor", "config", "http", "ApiReviewGetReviewListPostPath", "apiReviewGetReviewListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiReviewGetReviewListPost$Json$Response", "ApiReviewDeleteReviewPostPath", "apiReviewDeleteReviewPost$Plain$Response", "apiReviewDeleteReviewPost$Json$Response", "ApiReviewSaveReviewPostPath", "apiReviewSaveReviewPost$Plain$Response", "apiReviewSaveReviewPost$Json$Response", "ApiReviewGetReviewByIdPostPath", "apiReviewGetReviewByIdPost$Plain$Response", "apiReviewGetReviewByIdPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\review.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiReviewDeleteReviewPost$Json } from '../fn/review/api-review-delete-review-post-json';\r\nimport { ApiReviewDeleteReviewPost$Json$Params } from '../fn/review/api-review-delete-review-post-json';\r\nimport { apiReviewDeleteReviewPost$Plain } from '../fn/review/api-review-delete-review-post-plain';\r\nimport { ApiReviewDeleteReviewPost$Plain$Params } from '../fn/review/api-review-delete-review-post-plain';\r\nimport { apiReviewGetReviewByIdPost$Json } from '../fn/review/api-review-get-review-by-id-post-json';\r\nimport { ApiReviewGetReviewByIdPost$Json$Params } from '../fn/review/api-review-get-review-by-id-post-json';\r\nimport { apiReviewGetReviewByIdPost$Plain } from '../fn/review/api-review-get-review-by-id-post-plain';\r\nimport { ApiReviewGetReviewByIdPost$Plain$Params } from '../fn/review/api-review-get-review-by-id-post-plain';\r\nimport { apiReviewGetReviewListPost$Json } from '../fn/review/api-review-get-review-list-post-json';\r\nimport { ApiReviewGetReviewListPost$Json$Params } from '../fn/review/api-review-get-review-list-post-json';\r\nimport { apiReviewGetReviewListPost$Plain } from '../fn/review/api-review-get-review-list-post-plain';\r\nimport { ApiReviewGetReviewListPost$Plain$Params } from '../fn/review/api-review-get-review-list-post-plain';\r\nimport { apiReviewSaveReviewPost$Json } from '../fn/review/api-review-save-review-post-json';\r\nimport { ApiReviewSaveReviewPost$Json$Params } from '../fn/review/api-review-save-review-post-json';\r\nimport { apiReviewSaveReviewPost$Plain } from '../fn/review/api-review-save-review-post-plain';\r\nimport { ApiReviewSaveReviewPost$Plain$Params } from '../fn/review/api-review-save-review-post-plain';\r\nimport { GetReviewByIdResResponseBase } from '../models/get-review-by-id-res-response-base';\r\nimport { GetReviewListResListResponseBase } from '../models/get-review-list-res-list-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class ReviewService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiReviewGetReviewListPost()` */\r\n  static readonly ApiReviewGetReviewListPostPath = '/api/Review/GetReviewList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewGetReviewListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewListPost$Plain$Response(params?: ApiReviewGetReviewListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetReviewListResListResponseBase>> {\r\n    return apiReviewGetReviewListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewListPost$Plain(params?: ApiReviewGetReviewListPost$Plain$Params, context?: HttpContext): Observable<GetReviewListResListResponseBase> {\r\n    return this.apiReviewGetReviewListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetReviewListResListResponseBase>): GetReviewListResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewGetReviewListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewListPost$Json$Response(params?: ApiReviewGetReviewListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetReviewListResListResponseBase>> {\r\n    return apiReviewGetReviewListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewListPost$Json(params?: ApiReviewGetReviewListPost$Json$Params, context?: HttpContext): Observable<GetReviewListResListResponseBase> {\r\n    return this.apiReviewGetReviewListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetReviewListResListResponseBase>): GetReviewListResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiReviewDeleteReviewPost()` */\r\n  static readonly ApiReviewDeleteReviewPostPath = '/api/Review/DeleteReview';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewDeleteReviewPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewDeleteReviewPost$Plain$Response(params?: ApiReviewDeleteReviewPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiReviewDeleteReviewPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewDeleteReviewPost$Plain(params?: ApiReviewDeleteReviewPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiReviewDeleteReviewPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewDeleteReviewPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewDeleteReviewPost$Json$Response(params?: ApiReviewDeleteReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiReviewDeleteReviewPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewDeleteReviewPost$Json(params?: ApiReviewDeleteReviewPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiReviewDeleteReviewPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiReviewSaveReviewPost()` */\r\n  static readonly ApiReviewSaveReviewPostPath = '/api/Review/SaveReview';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewSaveReviewPost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiReviewSaveReviewPost$Plain$Response(params?: ApiReviewSaveReviewPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiReviewSaveReviewPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiReviewSaveReviewPost$Plain(params?: ApiReviewSaveReviewPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiReviewSaveReviewPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewSaveReviewPost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiReviewSaveReviewPost$Json$Response(params?: ApiReviewSaveReviewPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiReviewSaveReviewPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiReviewSaveReviewPost$Json(params?: ApiReviewSaveReviewPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiReviewSaveReviewPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiReviewGetReviewByIdPost()` */\r\n  static readonly ApiReviewGetReviewByIdPostPath = '/api/Review/GetReviewById';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewGetReviewByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewByIdPost$Plain$Response(params?: ApiReviewGetReviewByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetReviewByIdResResponseBase>> {\r\n    return apiReviewGetReviewByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewByIdPost$Plain(params?: ApiReviewGetReviewByIdPost$Plain$Params, context?: HttpContext): Observable<GetReviewByIdResResponseBase> {\r\n    return this.apiReviewGetReviewByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetReviewByIdResResponseBase>): GetReviewByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiReviewGetReviewByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewByIdPost$Json$Response(params?: ApiReviewGetReviewByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetReviewByIdResResponseBase>> {\r\n    return apiReviewGetReviewByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiReviewGetReviewByIdPost$Json(params?: ApiReviewGetReviewByIdPost$Json$Params, context?: HttpContext): Observable<GetReviewByIdResResponseBase> {\r\n    return this.apiReviewGetReviewByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetReviewByIdResResponseBase>): GetReviewByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAOA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,8BAA8B,QAAQ,iDAAiD;AAEhG,SAASC,+BAA+B,QAAQ,kDAAkD;AAElG,SAASC,+BAA+B,QAAQ,oDAAoD;AAEpG,SAASC,gCAAgC,QAAQ,qDAAqD;AAEtG,SAASC,+BAA+B,QAAQ,mDAAmD;AAEnG,SAASC,gCAAgC,QAAQ,oDAAoD;AAErG,SAASC,4BAA4B,QAAQ,+CAA+C;AAE5F,SAASC,6BAA6B,QAAQ,gDAAgD;;;;AAO9F,OAAM,MAAOC,aAAc,SAAQT,WAAW;EAC5CU,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,yCAAyCA,CAACC,MAAgD,EAAEC,OAAqB;IAC/G,OAAOV,gCAAgC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAV,gCAAgCA,CAACS,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACF,yCAAyC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzEnB,GAAG,CAAEoB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAC,wCAAwCA,CAACN,MAA+C,EAAEC,OAAqB;IAC7G,OAAOX,+BAA+B,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAX,+BAA+BA,CAACU,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACK,wCAAwC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxEnB,GAAG,CAAEoB,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAE,6BAA6B,GAAG,0BAA0B;EAAC;EAE3E;;;;;;EAMAC,wCAAwCA,CAACR,MAA+C,EAAEC,OAAqB;IAC7G,OAAOd,+BAA+B,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAd,+BAA+BA,CAACa,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACO,wCAAwC,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxEnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,uCAAuCA,CAACT,MAA8C,EAAEC,OAAqB;IAC3G,OAAOf,8BAA8B,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjF;EAEA;;;;;;EAMAf,8BAA8BA,CAACc,MAA8C,EAAEC,OAAqB;IAClG,OAAO,IAAI,CAACQ,uCAAuC,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvEnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,2BAA2B,GAAG,wBAAwB;EAAC;EAEvE;;;;;;EAMAC,sCAAsCA,CAACX,MAA6C,EAAEC,OAAqB;IACzG,OAAOR,6BAA6B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChF;EAEA;;;;;;EAMAR,6BAA6BA,CAACO,MAA6C,EAAEC,OAAqB;IAChG,OAAO,IAAI,CAACU,sCAAsC,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtEnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,qCAAqCA,CAACZ,MAA4C,EAAEC,OAAqB;IACvG,OAAOT,4BAA4B,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAT,4BAA4BA,CAACQ,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACW,qCAAqC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrEnB,GAAG,CAAEoB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,8BAA8B,GAAG,2BAA2B;EAAC;EAE7E;;;;;;EAMAC,yCAAyCA,CAACd,MAAgD,EAAEC,OAAqB;IAC/G,OAAOZ,gCAAgC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAZ,gCAAgCA,CAACW,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACa,yCAAyC,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzEnB,GAAG,CAAEoB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAU,wCAAwCA,CAACf,MAA+C,EAAEC,OAAqB;IAC7G,OAAOb,+BAA+B,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClF;EAEA;;;;;;EAMAb,+BAA+BA,CAACY,MAA+C,EAAEC,OAAqB;IACpG,OAAO,IAAI,CAACc,wCAAwC,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxEnB,GAAG,CAAEoB,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;;;uCA/LWX,aAAa,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAb3B,aAAa;MAAA4B,OAAA,EAAb5B,aAAa,CAAA6B,IAAA;MAAAC,UAAA,EADA;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}