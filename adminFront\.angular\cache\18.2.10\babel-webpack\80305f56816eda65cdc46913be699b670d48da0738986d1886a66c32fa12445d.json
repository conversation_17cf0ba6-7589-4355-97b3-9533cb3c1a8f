{"ast": null, "code": "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInCalendarISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of calendar ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of calendar ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of calendar ISO week-numbering years\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many calendar ISO week-numbering years are 1 January 2010 and 1 January 2012?\n * const result = differenceInCalendarISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 2\n */\nexport default function differenceInCalendarISOWeekYears(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  return getISOWeekYear(dirtyDateLeft) - getISOWeekYear(dirtyDateRight);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}