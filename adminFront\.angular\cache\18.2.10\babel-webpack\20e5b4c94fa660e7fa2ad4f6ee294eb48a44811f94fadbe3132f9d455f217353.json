{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./api-configuration\";\nimport * as i2 from \"@angular/common/http\";\n/**\n * Base class for services\n */\nexport class BaseService {\n  constructor(config, http) {\n    this.config = config;\n    this.http = http;\n  }\n  /**\n   * Returns the root url for all operations in this service. If not set directly in this\n   * service, will fallback to `ApiConfiguration.rootUrl`.\n   */\n  get rootUrl() {\n    return this._rootUrl || this.config.rootUrl;\n  }\n  /**\n   * Sets the root URL for API operations in this service.\n   */\n  set rootUrl(rootUrl) {\n    this._rootUrl = rootUrl;\n  }\n  static {\n    this.ɵfac = function BaseService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: BaseService,\n      factory: BaseService.ɵfac\n    });\n  }\n}\nexport class ServiceBase {\n  constructor(http) {\n    this.http = http;\n    this.httpOptions = {\n      headers: new HttpHeaders({\n        'Content-Type': 'application/json'\n      })\n    };\n    this.apiBaseUrl = `${environment.BASE_URL_API}`;\n  }\n  convertUtcDate(data) {\n    return data;\n  }\n  getUtcDate() {\n    return moment().utc();\n  }\n  // 實作moment\n  convertLocalDate(data) {\n    return data;\n  }\n  formatDate(data, format) {\n    return moment(data).format(format);\n  }\n  static {\n    this.ɵfac = function ServiceBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ServiceBase)(i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ServiceBase,\n      factory: ServiceBase.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "moment", "BaseService", "constructor", "config", "http", "rootUrl", "_rootUrl", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "ServiceBase", "httpOptions", "headers", "apiBaseUrl", "BASE_URL_API", "convertUtcDate", "data", "getUtcDate", "utc", "convertLocalDate", "formatDate", "format"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\base-service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { ApiConfiguration } from './api-configuration';\r\nimport { environment } from '../../environments/environment';\r\nimport * as moment from 'moment';\r\n\r\n/**\r\n * Base class for services\r\n */\r\n@Injectable()\r\nexport class BaseService {\r\n  constructor(\r\n    protected config: ApiConfiguration,\r\n    protected http: HttpClient\r\n  ) {\r\n  }\r\n\r\n  private _rootUrl?: string;\r\n\r\n  /**\r\n   * Returns the root url for all operations in this service. If not set directly in this\r\n   * service, will fallback to `ApiConfiguration.rootUrl`.\r\n   */\r\n  get rootUrl(): string {\r\n    return this._rootUrl || this.config.rootUrl;\r\n  }\r\n\r\n  /**\r\n   * Sets the root URL for API operations in this service.\r\n   */\r\n  set rootUrl(rootUrl: string) {\r\n    this._rootUrl = rootUrl;\r\n  }\r\n}\r\n\r\n@Injectable()\r\nexport class ServiceBase {\r\n  protected httpOptions = {\r\n    headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n  };\r\n  protected apiBaseUrl: string = `${environment.BASE_URL_API}`;\r\n\r\n  constructor(protected http: HttpClient) { }\r\n\r\n  convertUtcDate(data: Date) {\r\n    return data;\r\n  }\r\n\r\n  getUtcDate() {\r\n    return moment().utc();\r\n  }\r\n\r\n  // 實作moment\r\n  convertLocalDate(data: Date) {\r\n    return data;\r\n  }\r\n\r\n  formatDate(data: Date, format: string) {\r\n    return moment(data).format(format);\r\n  }\r\n\r\n}\r\n"], "mappings": "AAGA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;;AAEhC;;;AAIA,OAAM,MAAOC,WAAW;EACtBC,YACYC,MAAwB,EACxBC,IAAgB;IADhB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;EAEhB;EAIA;;;;EAIA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACH,MAAM,CAACE,OAAO;EAC7C;EAEA;;;EAGA,IAAIA,OAAOA,CAACA,OAAe;IACzB,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACzB;;;uCAtBWJ,WAAW,EAAAM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXX,WAAW;MAAAY,OAAA,EAAXZ,WAAW,CAAAa;IAAA;EAAA;;AA0BxB,OAAM,MAAOC,WAAW;EAMtBb,YAAsBE,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IALhB,KAAAY,WAAW,GAAG;MACtBC,OAAO,EAAE,IAAInB,WAAW,CAAC;QAAE,cAAc,EAAE;MAAkB,CAAE;KAChE;IACS,KAAAoB,UAAU,GAAW,GAAGnB,WAAW,CAACoB,YAAY,EAAE;EAElB;EAE1CC,cAAcA,CAACC,IAAU;IACvB,OAAOA,IAAI;EACb;EAEAC,UAAUA,CAAA;IACR,OAAOtB,MAAM,EAAE,CAACuB,GAAG,EAAE;EACvB;EAEA;EACAC,gBAAgBA,CAACH,IAAU;IACzB,OAAOA,IAAI;EACb;EAEAI,UAAUA,CAACJ,IAAU,EAAEK,MAAc;IACnC,OAAO1B,MAAM,CAACqB,IAAI,CAAC,CAACK,MAAM,CAACA,MAAM,CAAC;EACpC;;;uCAvBWX,WAAW,EAAAR,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXG,WAAW;MAAAF,OAAA,EAAXE,WAAW,CAAAD;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}