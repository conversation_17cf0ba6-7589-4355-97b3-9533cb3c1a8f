{"ast": null, "code": "import { PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { NavigationEnd } from '@angular/router';\nimport { NB_DOCUMENT } from '@nebular/theme';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let SeoService = /*#__PURE__*/(() => {\n  class SeoService {\n    constructor(router, document, platformId) {\n      this.router = router;\n      this.destroy$ = new Subject();\n      this.isBrowser = isPlatformBrowser(platformId);\n      this.dom = document;\n      if (this.isBrowser) {\n        this.createCanonicalTag();\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createCanonicalTag() {\n      this.linkCanonical = this.dom.createElement('link');\n      this.linkCanonical.setAttribute('rel', 'canonical');\n      this.dom.head.appendChild(this.linkCanonical);\n      this.linkCanonical.setAttribute('href', this.getCanonicalUrl());\n    }\n    trackCanonicalChanges() {\n      if (!this.isBrowser) {\n        return;\n      }\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(() => {\n        this.linkCanonical.setAttribute('href', this.getCanonicalUrl());\n      });\n    }\n    getCanonicalUrl() {\n      return this.dom.location.origin + this.dom.location.pathname;\n    }\n    static {\n      this.ɵfac = function SeoService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SeoService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(NB_DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SeoService,\n        factory: SeoService.ɵfac\n      });\n    }\n  }\n  return SeoService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}