{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name daysToWeeks\n * @category Conversion Helpers\n * @summary Convert days to weeks.\n *\n * @description\n * Convert a number of days to a full number of weeks.\n *\n * @param {number} days - number of days to be converted\n *\n * @returns {number} the number of days converted in weeks\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 14 days to weeks:\n * const result = daysToWeeks(14)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = daysToWeeks(13)\n * //=> 1\n */\nexport default function daysToWeeks(days) {\n  requiredArgs(1, arguments);\n  var weeks = days / daysInWeek;\n  return Math.floor(weeks);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}