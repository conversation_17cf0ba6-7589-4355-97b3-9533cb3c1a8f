{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DestroyRef, inject } from '@angular/core';\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as moment from 'moment';\nimport { concatMap, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@nebular/theme\";\nimport * as i6 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i7 from \"@fullcalendar/angular\";\nfunction CalendarComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r1.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.CBuildCaseName);\n  }\n}\nexport let CalendarComponent = /*#__PURE__*/(() => {\n  class CalendarComponent extends BaseComponent {\n    constructor(allow, buildCaseService, preOrderSettingService) {\n      super(allow);\n      this.allow = allow;\n      this.buildCaseService = buildCaseService;\n      this.preOrderSettingService = preOrderSettingService;\n      this.buildCaseList = [];\n      this.events = [];\n      this.listPreOrder = [];\n      this.calendarOptions = {\n        initialView: 'timeGridWeek',\n        plugins: [dayGridPlugin, timeGridPlugin, listPlugin],\n        headerToolbar: {\n          left: 'prev,next today',\n          center: 'title',\n          right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\n        },\n        views: {\n          timeGrid: {\n            allDaySlot: false // Hide all-day line in time grid view,\n          }\n        },\n        height: 'auto',\n        slotMaxTime: '22:00:00',\n        slotMinTime: '09:00:00',\n        datesSet: agr => {\n          if (this.dateStart !== agr.start || this.dateEnd !== agr.end) {\n            this.dateStart = agr.start;\n            this.dateEnd = agr.end;\n            this.getListPreOrder(agr.start, agr.end).subscribe();\n          }\n        }\n      };\n      this.destroy = inject(DestroyRef);\n    }\n    ngOnInit() {\n      this.initialList();\n    }\n    initialList() {\n      this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(takeUntilDestroyed(this.destroy)).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.buildCaseList = res.Entries ?? [];\n          this.currentBuildCase = this.buildCaseList[0]?.cID;\n        }\n      }), concatMap(() => this.getListPreOrder(this.dateStart, this.dateEnd))).subscribe();\n    }\n    getListPreOrder(dateStart, dateEnd) {\n      return this.preOrderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n        body: {\n          CBuildCaseID: this.currentBuildCase,\n          CDateStart: dateStart.toISOString(),\n          CDateEnd: dateEnd.toISOString()\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.listPreOrder = res.Entries;\n          this.initEvent();\n        }\n      }));\n    }\n    initEvent() {\n      this.events = (this.listPreOrder ?? []).map(i => {\n        return {\n          title: i.CStatus ? `${i.CHouseHoldName ?? \"\"}${i.CFloor ? \"-\" + i.CFloor + 'F' : \"\"}\\n${i.CCustomerName ?? \"\"} ${i.CPeoples}P` : '',\n          start: moment(i.CDate).hour(i.CHour ?? 0).toISOString(),\n          end: moment(i.CDate).hour(i.CHour ?? 0).add(1, 'hours').toISOString(),\n          color: i.CStatus ? '#008080' : '#81d3f8',\n          backgroundColor: i.CStatus ? '#008080' : '#81d3f8',\n          display: i.CStatus ? undefined : 'background'\n        };\n      });\n      this.calendarOptions = {\n        ...this.calendarOptions,\n        // Spread the existing options to avoid mutation\n        events: this.events // Update events property with new data\n      };\n    }\n    changeBuildCase(buildCaseId) {\n      this.currentBuildCase = buildCaseId;\n      this.getListPreOrder(this.dateStart, this.dateEnd).subscribe();\n    }\n    static {\n      this.ɵfac = function CalendarComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || CalendarComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.BuildCaseService), i0.ɵɵdirectiveInject(i2.PreOrderSettingService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CalendarComponent,\n        selectors: [[\"app-calendar\"]],\n        viewQuery: function CalendarComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(FullCalendarComponent, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 21,\n        vars: 3,\n        consts: [[\"accent\", \"success\"], [1, \"row\"], [1, \"col-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"project\", 1, \"text-nowrap\", \"m-0\", \"mr-2\", \"col-4\"], [\"fullWidth\", \"\", 3, \"selectedChange\", \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"mb-4\"], [1, \"d-flex\"], [1, \"slot-description\", \"unavailable\"], [1, \"slot-description\", \"available\"], [1, \"slot-description\", \"reserved\"], [1, \"col-12\"], [3, \"options\"], [3, \"value\"]],\n        template: function CalendarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 1)(5, \"div\", 2)(6, \"div\", 3)(7, \"label\", 4);\n            i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"nb-select\", 5);\n            i0.ɵɵlistener(\"selectedChange\", function CalendarComponent_Template_nb_select_selectedChange_9_listener($event) {\n              return ctx.changeBuildCase($event);\n            });\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_Template_nb_select_ngModelChange_9_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.currentBuildCase, $event) || (ctx.currentBuildCase = $event);\n              return $event;\n            });\n            i0.ɵɵtemplate(10, CalendarComponent_nb_option_10_Template, 2, 2, \"nb-option\", 6);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9);\n            i0.ɵɵtext(14, \"\\u672A\\u958B\\u653E\\u9810\\u7D04\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 10);\n            i0.ɵɵtext(16, \"\\u5DF2\\u958B\\u653E\\u9810\\u7D04\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 11);\n            i0.ɵɵtext(18, \"\\u5DF2\\u88AB\\u9810\\u7D04\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 12);\n            i0.ɵɵelement(20, \"full-calendar\", 13);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentBuildCase);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"options\", ctx.calendarOptions);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, SharedModule, i4.NgControlStatus, i4.NgModel, i5.NbCardComponent, i5.NbCardBodyComponent, i5.NbCardHeaderComponent, i5.NbSelectComponent, i5.NbOptionComponent, i6.BreadcrumbComponent, FullCalendarModule, i7.FullCalendarComponent],\n        styles: [\".unavailable[_ngcontent-%COMP%]{background-color:#f2f2f2}.available[_ngcontent-%COMP%]{background-color:#81d3f8}.reserved[_ngcontent-%COMP%]{background-color:teal}.slot-description[_ngcontent-%COMP%]{padding:.5rem .75rem;margin-right:1rem;border-radius:.25rem}.fc-time-grid-week[_ngcontent-%COMP%]   .fc-time-grid-slot[_ngcontent-%COMP%]{background-color:#f0f0f0}  .fc-day .fc-timegrid-col-frame{background-color:#f2f2f2}  .fc .fc-button{background-color:#008cff;color:#fff;border:none;padding:.5rem 1rem;font-size:1rem;border-radius:.25rem;cursor:pointer}  .fc .fc-button:hover{background-color:#0056b3}  .fc .fc-button.fc-button-active{background-color:#0056b3}\"]\n      });\n    }\n  }\n  return CalendarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}