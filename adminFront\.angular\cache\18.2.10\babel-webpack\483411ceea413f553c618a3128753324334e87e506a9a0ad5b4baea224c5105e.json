{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { StatsBarData } from '../data/stats-bar';\nimport * as i0 from \"@angular/core\";\nexport class StatsBarService extends StatsBarData {\n  constructor() {\n    super(...arguments);\n    this.statsBarData = [300, 520, 435, 530, 730, 620, 660, 860];\n  }\n  getStatsBarData() {\n    return observableOf(this.statsBarData);\n  }\n  static {\n    this.ɵfac = /*@__PURE__*/(() => {\n      let ɵStatsBarService_BaseFactory;\n      return function StatsBarService_Factory(__ngFactoryType__) {\n        return (ɵStatsBarService_BaseFactory || (ɵStatsBarService_BaseFactory = i0.ɵɵgetInheritedFactory(StatsBarService)))(__ngFactoryType__ || StatsBarService);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: StatsBarService,\n      factory: StatsBarService.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "observableOf", "StatsBarData", "StatsBarService", "constructor", "statsBarData", "getStatsBarData", "__ngFactoryType__", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\mock\\stats-bar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { of as observableOf, Observable } from 'rxjs';\r\nimport { StatsBarData } from '../data/stats-bar';\r\n\r\n@Injectable()\r\nexport class StatsBarService extends StatsBarData {\r\n\r\n  private statsBarData: number[] = [\r\n    300, 520, 435, 530,\r\n    730, 620, 660, 860,\r\n  ];\r\n\r\n  getStatsBarData(): Observable<number[]> {\r\n    return observableOf(this.statsBarData);\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,EAAE,IAAIC,YAAY,QAAoB,MAAM;AACrD,SAASC,YAAY,QAAQ,mBAAmB;;AAGhD,OAAM,MAAOC,eAAgB,SAAQD,YAAY;EADjDE,YAAA;;IAGU,KAAAC,YAAY,GAAa,CAC/B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAClB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACnB;;EAEDC,eAAeA,CAAA;IACb,OAAOL,YAAY,CAAC,IAAI,CAACI,YAAY,CAAC;EACxC;;;;;yGATWF,eAAe,IAAAI,iBAAA,IAAfJ,eAAe;MAAA;IAAA;EAAA;;;aAAfA,eAAe;MAAAK,OAAA,EAAfL,eAAe,CAAAM;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}