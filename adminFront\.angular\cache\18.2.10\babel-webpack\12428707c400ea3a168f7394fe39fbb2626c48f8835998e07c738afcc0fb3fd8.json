{"ast": null, "code": "import endOfWeek from \"../endOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport default function endOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return endOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}", "map": {"version": 3, "names": ["endOfWeek", "requiredArgs", "endOfISOWeek", "dirtyDate", "arguments", "weekStartsOn"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/endOfISOWeek/index.js"], "sourcesContent": ["import endOfWeek from \"../endOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport default function endOfISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  return endOfWeek(dirtyDate, {\n    weekStartsOn: 1\n  });\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,SAAS,CAACG,SAAS,EAAE;IAC1BE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}