{"ast": null, "code": "export function createInvalidObservableTypeError(input) {\n  return new TypeError(`You provided ${input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`);\n}\n//# sourceMappingURL=throwUnobservableError.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}