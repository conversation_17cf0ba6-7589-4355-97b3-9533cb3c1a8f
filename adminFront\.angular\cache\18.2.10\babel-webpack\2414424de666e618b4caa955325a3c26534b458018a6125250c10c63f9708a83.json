{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiInfoPictureGetInfoPicturelListPost$Json } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-json';\nimport { apiInfoPictureGetInfoPicturelListPost$Plain } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-plain';\nimport { apiInfoPictureUploadListInfoPicturePost$Json } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-json';\nimport { apiInfoPictureUploadListInfoPicturePost$Plain } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class InfoPictureService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiInfoPictureGetInfoPicturelListPost()` */\n  static {\n    this.ApiInfoPictureGetInfoPicturelListPostPath = '/api/InfoPicture/GetInfoPicturelList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiInfoPictureGetInfoPicturelListPost$Plain$Response(params, context) {\n    return apiInfoPictureGetInfoPicturelListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiInfoPictureGetInfoPicturelListPost$Plain(params, context) {\n    return this.apiInfoPictureGetInfoPicturelListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiInfoPictureGetInfoPicturelListPost$Json$Response(params, context) {\n    return apiInfoPictureGetInfoPicturelListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiInfoPictureGetInfoPicturelListPost$Json(params, context) {\n    return this.apiInfoPictureGetInfoPicturelListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiInfoPictureUploadListInfoPicturePost()` */\n  static {\n    this.ApiInfoPictureUploadListInfoPicturePostPath = '/api/InfoPicture/UploadListInfoPicture';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Plain()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiInfoPictureUploadListInfoPicturePost$Plain$Response(params, context) {\n    return apiInfoPictureUploadListInfoPicturePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Plain$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiInfoPictureUploadListInfoPicturePost$Plain(params, context) {\n    return this.apiInfoPictureUploadListInfoPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Json()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiInfoPictureUploadListInfoPicturePost$Json$Response(params, context) {\n    return apiInfoPictureUploadListInfoPicturePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Json$Response()` instead.\n   *\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n   */\n  apiInfoPictureUploadListInfoPicturePost$Json(params, context) {\n    return this.apiInfoPictureUploadListInfoPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function InfoPictureService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || InfoPictureService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: InfoPictureService,\n      factory: InfoPictureService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiInfoPictureGetInfoPicturelListPost$Json", "apiInfoPictureGetInfoPicturelListPost$Plain", "apiInfoPictureUploadListInfoPicturePost$Json", "apiInfoPictureUploadListInfoPicturePost$Plain", "InfoPictureService", "constructor", "config", "http", "ApiInfoPictureGetInfoPicturelListPostPath", "apiInfoPictureGetInfoPicturelListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiInfoPictureGetInfoPicturelListPost$Json$Response", "ApiInfoPictureUploadListInfoPicturePostPath", "apiInfoPictureUploadListInfoPicturePost$Plain$Response", "apiInfoPictureUploadListInfoPicturePost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\info-picture.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiInfoPictureGetInfoPicturelListPost$Json } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-json';\r\nimport { ApiInfoPictureGetInfoPicturelListPost$Json$Params } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-json';\r\nimport { apiInfoPictureGetInfoPicturelListPost$Plain } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-plain';\r\nimport { ApiInfoPictureGetInfoPicturelListPost$Plain$Params } from '../fn/info-picture/api-info-picture-get-info-picturel-list-post-plain';\r\nimport { apiInfoPictureUploadListInfoPicturePost$Json } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-json';\r\nimport { ApiInfoPictureUploadListInfoPicturePost$Json$Params } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-json';\r\nimport { apiInfoPictureUploadListInfoPicturePost$Plain } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-plain';\r\nimport { ApiInfoPictureUploadListInfoPicturePost$Plain$Params } from '../fn/info-picture/api-info-picture-upload-list-info-picture-post-plain';\r\nimport { GetInfoPictureListResponseListResponseBase } from '../models/get-info-picture-list-response-list-response-base';\r\nimport { UploadFileResponseResponseBase } from '../models/upload-file-response-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class InfoPictureService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiInfoPictureGetInfoPicturelListPost()` */\r\n  static readonly ApiInfoPictureGetInfoPicturelListPostPath = '/api/InfoPicture/GetInfoPicturelList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiInfoPictureGetInfoPicturelListPost$Plain$Response(params?: ApiInfoPictureGetInfoPicturelListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetInfoPictureListResponseListResponseBase>> {\r\n    return apiInfoPictureGetInfoPicturelListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiInfoPictureGetInfoPicturelListPost$Plain(params?: ApiInfoPictureGetInfoPicturelListPost$Plain$Params, context?: HttpContext): Observable<GetInfoPictureListResponseListResponseBase> {\r\n    return this.apiInfoPictureGetInfoPicturelListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetInfoPictureListResponseListResponseBase>): GetInfoPictureListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiInfoPictureGetInfoPicturelListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiInfoPictureGetInfoPicturelListPost$Json$Response(params?: ApiInfoPictureGetInfoPicturelListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetInfoPictureListResponseListResponseBase>> {\r\n    return apiInfoPictureGetInfoPicturelListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiInfoPictureGetInfoPicturelListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiInfoPictureGetInfoPicturelListPost$Json(params?: ApiInfoPictureGetInfoPicturelListPost$Json$Params, context?: HttpContext): Observable<GetInfoPictureListResponseListResponseBase> {\r\n    return this.apiInfoPictureGetInfoPicturelListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetInfoPictureListResponseListResponseBase>): GetInfoPictureListResponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiInfoPictureUploadListInfoPicturePost()` */\r\n  static readonly ApiInfoPictureUploadListInfoPicturePostPath = '/api/InfoPicture/UploadListInfoPicture';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Plain()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiInfoPictureUploadListInfoPicturePost$Plain$Response(params?: ApiInfoPictureUploadListInfoPicturePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiInfoPictureUploadListInfoPicturePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiInfoPictureUploadListInfoPicturePost$Plain(params?: ApiInfoPictureUploadListInfoPicturePost$Plain$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiInfoPictureUploadListInfoPicturePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiInfoPictureUploadListInfoPicturePost$Json()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiInfoPictureUploadListInfoPicturePost$Json$Response(params?: ApiInfoPictureUploadListInfoPicturePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<UploadFileResponseResponseBase>> {\r\n    return apiInfoPictureUploadListInfoPicturePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiInfoPictureUploadListInfoPicturePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\r\n   */\r\n  apiInfoPictureUploadListInfoPicturePost$Json(params?: ApiInfoPictureUploadListInfoPicturePost$Json$Params, context?: HttpContext): Observable<UploadFileResponseResponseBase> {\r\n    return this.apiInfoPictureUploadListInfoPicturePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<UploadFileResponseResponseBase>): UploadFileResponseResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,0CAA0C,QAAQ,sEAAsE;AAEjI,SAASC,2CAA2C,QAAQ,uEAAuE;AAEnI,SAASC,4CAA4C,QAAQ,wEAAwE;AAErI,SAASC,6CAA6C,QAAQ,yEAAyE;;;;AAMvI,OAAM,MAAOC,kBAAmB,SAAQL,WAAW;EACjDM,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,yCAAyC,GAAG,sCAAsC;EAAC;EAEnG;;;;;;EAMAC,oDAAoDA,CAACC,MAA2D,EAAEC,OAAqB;IACrI,OAAOV,2CAA2C,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAV,2CAA2CA,CAACS,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACF,oDAAoD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpFf,GAAG,CAAEgB,CAAiE,IAAiDA,CAAC,CAACC,IAAI,CAAC,CAC/H;EACH;EAEA;;;;;;EAMAC,mDAAmDA,CAACN,MAA0D,EAAEC,OAAqB;IACnI,OAAOX,0CAA0C,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAX,0CAA0CA,CAACU,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACK,mDAAmD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFf,GAAG,CAAEgB,CAAiE,IAAiDA,CAAC,CAACC,IAAI,CAAC,CAC/H;EACH;EAEA;;IACgB,KAAAE,2CAA2C,GAAG,wCAAwC;EAAC;EAEvG;;;;;;EAMAC,sDAAsDA,CAACR,MAA6D,EAAEC,OAAqB;IACzI,OAAOR,6CAA6C,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAR,6CAA6CA,CAACO,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACO,sDAAsD,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFf,GAAG,CAAEgB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAI,qDAAqDA,CAACT,MAA4D,EAAEC,OAAqB;IACvI,OAAOT,4CAA4C,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/F;EAEA;;;;;;EAMAT,4CAA4CA,CAACQ,MAA4D,EAAEC,OAAqB;IAC9H,OAAO,IAAI,CAACQ,qDAAqD,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrFf,GAAG,CAAEgB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;;;uCAjGWX,kBAAkB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBrB,kBAAkB;MAAAsB,OAAA,EAAlBtB,kBAAkB,CAAAuB,IAAA;MAAAC,UAAA,EADL;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}