{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nlet HouseholdBindingComponent = class HouseholdBindingComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.placeholder = '請選擇戶別';\n    this.maxSelections = null;\n    this.disabled = false;\n    this.buildingData = {};\n    this.showSelectedArea = true;\n    this.allowSearch = true;\n    this.allowBatchSelect = true;\n    this.selectionChange = new EventEmitter();\n    this.isOpen = false;\n    this.selectedBuilding = '';\n    this.searchTerm = '';\n    this.selectedHouseholds = [];\n    this.buildings = [];\n    this.filteredHouseholds = []; // 簡化為字串陣列\n    this.selectedByBuilding = {};\n    // ControlValueAccessor implementation\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  writeValue(value) {\n    this.selectedHouseholds = value || [];\n    this.updateSelectedByBuilding();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  ngOnInit() {\n    // 如果沒有提供 buildingData，使用 mock 資料\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\n      this.buildingData = this.generateMockData();\n    }\n    this.buildings = Object.keys(this.buildingData);\n    console.log('Component initialized with buildings:', this.buildings);\n    console.log('Building data keys:', Object.keys(this.buildingData));\n    this.updateSelectedByBuilding();\n  }\n  ngOnChanges(changes) {\n    if (changes['buildingData']) {\n      this.buildings = Object.keys(this.buildingData);\n      this.updateFilteredHouseholds();\n      this.updateSelectedByBuilding();\n    }\n  }\n  updateSelectedByBuilding() {\n    const grouped = {};\n    this.selectedHouseholds.forEach(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) {\n          if (!grouped[building]) grouped[building] = [];\n          grouped[building].push(code);\n          break;\n        }\n      }\n    });\n    this.selectedByBuilding = grouped;\n  }\n  generateMockData() {\n    // 簡化版本 - 直接生成字串陣列\n    const simpleMockData = {\n      'A棟': Array.from({\n        length: 50\n      }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\n      'B棟': Array.from({\n        length: 40\n      }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\n      'C棟': Array.from({\n        length: 60\n      }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\n      'D棟': Array.from({\n        length: 35\n      }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\n      'E棟': Array.from({\n        length: 45\n      }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\n    };\n    // 轉換為 BuildingData 格式\n    const buildingData = {};\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\n      buildingData[building] = codes.map(code => ({\n        code,\n        building,\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\n        isSelected: false,\n        isDisabled: false\n      }));\n    });\n    return buildingData;\n  }\n  onBuildingSelect(building) {\n    console.log('Building selected:', building);\n    this.selectedBuilding = building;\n    this.searchTerm = '';\n    this.updateFilteredHouseholds();\n    console.log('Filtered households count:', this.filteredHouseholds.length);\n    // 手動觸發變更偵測\n    this.cdr.detectChanges();\n  }\n  onBuildingClick(building) {\n    console.log('Building clicked (mousedown):', building);\n  }\n  updateFilteredHouseholds() {\n    console.log('Updating filtered households for building:', this.selectedBuilding);\n    if (!this.selectedBuilding) {\n      this.filteredHouseholds = [];\n      return;\n    }\n    const households = this.buildingData[this.selectedBuilding] || [];\n    console.log('Available households for building:', households.length);\n    // 提取戶別代碼並進行搜尋過濾\n    this.filteredHouseholds = households.map(h => h.code).filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\n    console.log('Filtered households result:', this.filteredHouseholds.length);\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.updateFilteredHouseholds();\n  }\n  onHouseholdToggle(householdCode) {\n    const isSelected = this.selectedHouseholds.includes(householdCode);\n    let newSelection;\n    if (isSelected) {\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\n    } else {\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\n        return; // 達到最大選擇數量\n      }\n      newSelection = [...this.selectedHouseholds, householdCode];\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onRemoveHousehold(householdCode) {\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\n    this.emitChanges();\n  }\n  onSelectAllFiltered() {\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\n    // 直接使用過濾後的戶別代碼\n    const availableHouseholds = this.filteredHouseholds;\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onSelectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\n      return; // 超過最大選擇數量\n    }\n    this.selectedHouseholds = newSelection;\n    this.emitChanges();\n  }\n  onUnselectAllBuilding() {\n    if (!this.selectedBuilding) return;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\n    this.emitChanges();\n  }\n  onClearAll() {\n    this.selectedHouseholds = [];\n    this.emitChanges();\n  }\n  emitChanges() {\n    this.updateSelectedByBuilding();\n    this.onChange([...this.selectedHouseholds]);\n    this.onTouched();\n    const selectedItems = this.selectedHouseholds.map(code => {\n      for (const building of this.buildings) {\n        const item = this.buildingData[building]?.find(h => h.code === code);\n        if (item) return item;\n      }\n      return null;\n    }).filter(item => item !== null);\n    this.selectionChange.emit(selectedItems);\n  }\n  toggleDropdown() {\n    if (!this.disabled) {\n      this.isOpen = !this.isOpen;\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\n      console.log('Buildings available:', this.buildings);\n    }\n  }\n  closeDropdown() {\n    this.isOpen = false;\n  }\n  isHouseholdSelected(householdCode) {\n    return this.selectedHouseholds.includes(householdCode);\n  }\n  canSelectMore() {\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\n  }\n  isAllBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding].filter(h => !h.isDisabled).map(h => h.code);\n    return buildingHouseholds.length > 0 && buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\n  }\n  isSomeBuildingSelected() {\n    if (!this.selectedBuilding) return false;\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\n  }\n  getSelectedByBuilding() {\n    return this.selectedByBuilding;\n  }\n  getBuildingCount(building) {\n    return this.buildingData[building]?.length || 0;\n  }\n  getSelectedCount() {\n    return this.selectedHouseholds.length;\n  }\n  // 輔助方法：安全地獲取建築物的已選戶別\n  getBuildingSelectedHouseholds(building) {\n    return this.selectedByBuilding[building] || [];\n  }\n  // 輔助方法：檢查建築物是否有已選戶別\n  hasBuildingSelected(building) {\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\n  }\n};\n__decorate([Input()], HouseholdBindingComponent.prototype, \"placeholder\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"maxSelections\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"buildingData\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"showSelectedArea\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowSearch\", void 0);\n__decorate([Input()], HouseholdBindingComponent.prototype, \"allowBatchSelect\", void 0);\n__decorate([Output()], HouseholdBindingComponent.prototype, \"selectionChange\", void 0);\nHouseholdBindingComponent = __decorate([Component({\n  selector: 'app-household-binding',\n  templateUrl: './household-binding.component.html',\n  styleUrls: ['./household-binding.component.scss'],\n  providers: [{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => HouseholdBindingComponent),\n    multi: true\n  }]\n})], HouseholdBindingComponent);\nexport { HouseholdBindingComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "HouseholdBindingComponent", "constructor", "cdr", "placeholder", "maxSelections", "disabled", "buildingData", "showSelectedArea", "allowSearch", "allowBatchSelect", "selectionChange", "isOpen", "selectedBuilding", "searchTerm", "selectedHouseholds", "buildings", "filteredHouseholds", "selectedByBuilding", "onChange", "value", "onTouched", "writeValue", "updateSelectedByBuilding", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ngOnInit", "Object", "keys", "length", "generateMockData", "console", "log", "ngOnChanges", "changes", "updateFilteredHouseholds", "grouped", "for<PERSON>ach", "code", "building", "item", "find", "h", "push", "simpleMockData", "Array", "from", "_", "i", "String", "padStart", "entries", "codes", "map", "floor", "Math", "parseInt", "slice", "isSelected", "onBuildingSelect", "detectChanges", "onBuildingClick", "households", "filter", "toLowerCase", "includes", "onSearchChange", "event", "target", "onHouseholdToggle", "householdCode", "newSelection", "emitChanges", "onRemoveHousehold", "onSelectAllFiltered", "availableHouseholds", "Set", "onSelectAllBuilding", "buildingHouseholds", "onUnselectAllBuilding", "onClearAll", "selectedItems", "emit", "toggleDropdown", "closeDropdown", "isHouseholdSelected", "canSelectMore", "isAllBuildingSelected", "every", "isSomeBuildingSelected", "some", "getSelectedByBuilding", "getBuildingCount", "getSelectedCount", "getBuildingSelectedHouseholds", "hasBuildingSelected", "__decorate", "selector", "templateUrl", "styleUrls", "providers", "provide", "useExisting", "multi"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\components\\household-binding\\household-binding.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, forwardRef, ChangeDetectorRef } from '@angular/core';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\r\n\r\nexport interface HouseholdItem {\r\n  code: string;\r\n  building: string;\r\n  floor?: string;\r\n  isSelected?: boolean;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nexport interface BuildingData {\r\n  [key: string]: HouseholdItem[];\r\n}\r\n\r\n// 簡化版本 - 使用字串陣列\r\nexport interface SimpleBuildingData {\r\n  [key: string]: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-household-binding',\r\n  templateUrl: './household-binding.component.html',\r\n  styleUrls: ['./household-binding.component.scss'],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => HouseholdBindingComponent),\r\n      multi: true,\r\n    },\r\n  ],\r\n})\r\nexport class HouseholdBindingComponent implements OnInit, OnChanges, ControlValueAccessor {\r\n  @Input() placeholder: string = '請選擇戶別';\r\n  @Input() maxSelections: number | null = null;\r\n  @Input() disabled: boolean = false;\r\n  @Input() buildingData: BuildingData = {};\r\n  @Input() showSelectedArea: boolean = true;\r\n  @Input() allowSearch: boolean = true;\r\n  @Input() allowBatchSelect: boolean = true;\r\n\r\n  @Output() selectionChange = new EventEmitter<HouseholdItem[]>();\r\n\r\n  isOpen = false;\r\n  selectedBuilding = '';\r\n  searchTerm = '';\r\n  selectedHouseholds: string[] = [];\r\n  buildings: string[] = [];\r\n  filteredHouseholds: string[] = [];  // 簡化為字串陣列\r\n  selectedByBuilding: { [building: string]: string[] } = {};\r\n  // ControlValueAccessor implementation\r\n  private onChange = (value: string[]) => { };\r\n  private onTouched = () => { };\r\n\r\n  constructor(private cdr: ChangeDetectorRef) { }\r\n\r\n  writeValue(value: string[]): void {\r\n    this.selectedHouseholds = value || [];\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  registerOnChange(fn: (value: string[]) => void): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: () => void): void {\r\n    this.onTouched = fn;\r\n  }\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  } ngOnInit() {\r\n    // 如果沒有提供 buildingData，使用 mock 資料\r\n    if (!this.buildingData || Object.keys(this.buildingData).length === 0) {\r\n      this.buildingData = this.generateMockData();\r\n    }\r\n\r\n    this.buildings = Object.keys(this.buildingData);\r\n    console.log('Component initialized with buildings:', this.buildings);\r\n    console.log('Building data keys:', Object.keys(this.buildingData));\r\n    this.updateSelectedByBuilding();\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes['buildingData']) {\r\n      this.buildings = Object.keys(this.buildingData);\r\n      this.updateFilteredHouseholds();\r\n      this.updateSelectedByBuilding();\r\n    }\r\n  }\r\n\r\n  private updateSelectedByBuilding() {\r\n    const grouped: { [building: string]: string[] } = {};\r\n\r\n    this.selectedHouseholds.forEach(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) {\r\n          if (!grouped[building]) grouped[building] = [];\r\n          grouped[building].push(code);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n\r\n    this.selectedByBuilding = grouped;\r\n  }\r\n  private generateMockData(): BuildingData {\r\n    // 簡化版本 - 直接生成字串陣列\r\n    const simpleMockData = {\r\n      'A棟': Array.from({ length: 50 }, (_, i) => `A${String(i + 1).padStart(3, '0')}`),\r\n      'B棟': Array.from({ length: 40 }, (_, i) => `B${String(i + 1).padStart(3, '0')}`),\r\n      'C棟': Array.from({ length: 60 }, (_, i) => `C${String(i + 1).padStart(3, '0')}`),\r\n      'D棟': Array.from({ length: 35 }, (_, i) => `D${String(i + 1).padStart(3, '0')}`),\r\n      'E棟': Array.from({ length: 45 }, (_, i) => `E${String(i + 1).padStart(3, '0')}`)\r\n    };\r\n\r\n    // 轉換為 BuildingData 格式\r\n    const buildingData: BuildingData = {};\r\n    Object.entries(simpleMockData).forEach(([building, codes]) => {\r\n      buildingData[building] = codes.map(code => ({\r\n        code,\r\n        building,\r\n        floor: `${Math.floor((parseInt(code.slice(1)) - 1) / 4) + 1}F`,\r\n        isSelected: false,\r\n        isDisabled: false\r\n      }));\r\n    });\r\n\r\n    return buildingData;\r\n  } onBuildingSelect(building: string) {\r\n    console.log('Building selected:', building);\r\n    this.selectedBuilding = building;\r\n    this.searchTerm = '';\r\n    this.updateFilteredHouseholds();\r\n    console.log('Filtered households count:', this.filteredHouseholds.length);\r\n    // 手動觸發變更偵測\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  onBuildingClick(building: string) {\r\n    console.log('Building clicked (mousedown):', building);\r\n  } updateFilteredHouseholds() {\r\n    console.log('Updating filtered households for building:', this.selectedBuilding);\r\n    if (!this.selectedBuilding) {\r\n      this.filteredHouseholds = [];\r\n      return;\r\n    }\r\n\r\n    const households = this.buildingData[this.selectedBuilding] || [];\r\n    console.log('Available households for building:', households.length);\r\n\r\n    // 提取戶別代碼並進行搜尋過濾\r\n    this.filteredHouseholds = households\r\n      .map(h => h.code)\r\n      .filter(code => code.toLowerCase().includes(this.searchTerm.toLowerCase()));\r\n\r\n    console.log('Filtered households result:', this.filteredHouseholds.length);\r\n  }\r\n\r\n  onSearchChange(event: any) {\r\n    this.searchTerm = event.target.value;\r\n    this.updateFilteredHouseholds();\r\n  }\r\n  onHouseholdToggle(householdCode: string) {\r\n    const isSelected = this.selectedHouseholds.includes(householdCode);\r\n    let newSelection: string[];\r\n\r\n    if (isSelected) {\r\n      newSelection = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    } else {\r\n      if (this.maxSelections && this.selectedHouseholds.length >= this.maxSelections) {\r\n        return; // 達到最大選擇數量\r\n      }\r\n      newSelection = [...this.selectedHouseholds, householdCode];\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n\r\n  onRemoveHousehold(householdCode: string) {\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => h !== householdCode);\r\n    this.emitChanges();\r\n  }\r\n  onSelectAllFiltered() {\r\n    if (!this.selectedBuilding || this.filteredHouseholds.length === 0) return;\r\n\r\n    // 直接使用過濾後的戶別代碼\r\n    const availableHouseholds = this.filteredHouseholds;\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...availableHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onSelectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    const newSelection = [...new Set([...this.selectedHouseholds, ...buildingHouseholds])];\r\n\r\n    if (this.maxSelections && newSelection.length > this.maxSelections) {\r\n      return; // 超過最大選擇數量\r\n    }\r\n\r\n    this.selectedHouseholds = newSelection;\r\n    this.emitChanges();\r\n  }\r\n  onUnselectAllBuilding() {\r\n    if (!this.selectedBuilding) return;\r\n\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    this.selectedHouseholds = this.selectedHouseholds.filter(h => !buildingHouseholds.includes(h));\r\n    this.emitChanges();\r\n  }\r\n  onClearAll() {\r\n    this.selectedHouseholds = [];\r\n    this.emitChanges();\r\n  }\r\n\r\n  private emitChanges() {\r\n    this.updateSelectedByBuilding();\r\n    this.onChange([...this.selectedHouseholds]);\r\n    this.onTouched();\r\n\r\n    const selectedItems = this.selectedHouseholds.map(code => {\r\n      for (const building of this.buildings) {\r\n        const item = this.buildingData[building]?.find(h => h.code === code);\r\n        if (item) return item;\r\n      }\r\n      return null;\r\n    }).filter(item => item !== null) as HouseholdItem[];\r\n\r\n    this.selectionChange.emit(selectedItems);\r\n  }\r\n  toggleDropdown() {\r\n    if (!this.disabled) {\r\n      this.isOpen = !this.isOpen;\r\n      console.log('Dropdown toggled. isOpen:', this.isOpen);\r\n      console.log('Buildings available:', this.buildings);\r\n    }\r\n  }\r\n\r\n  closeDropdown() {\r\n    this.isOpen = false;\r\n  }\r\n\r\n  isHouseholdSelected(householdCode: string): boolean {\r\n    return this.selectedHouseholds.includes(householdCode);\r\n  }\r\n\r\n  canSelectMore(): boolean {\r\n    return !this.maxSelections || this.selectedHouseholds.length < this.maxSelections;\r\n  }\r\n\r\n  isAllBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]\r\n      .filter(h => !h.isDisabled)\r\n      .map(h => h.code);\r\n    return buildingHouseholds.length > 0 &&\r\n      buildingHouseholds.every(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  isSomeBuildingSelected(): boolean {\r\n    if (!this.selectedBuilding) return false;\r\n    const buildingHouseholds = this.buildingData[this.selectedBuilding]?.map(h => h.code) || [];\r\n    return buildingHouseholds.some(code => this.selectedHouseholds.includes(code));\r\n  }\r\n  getSelectedByBuilding(): { [building: string]: string[] } {\r\n    return this.selectedByBuilding;\r\n  }\r\n\r\n  getBuildingCount(building: string): number {\r\n    return this.buildingData[building]?.length || 0;\r\n  }\r\n\r\n  getSelectedCount(): number {\r\n    return this.selectedHouseholds.length;\r\n  }\r\n\r\n  // 輔助方法：安全地獲取建築物的已選戶別\r\n  getBuildingSelectedHouseholds(building: string): string[] {\r\n    return this.selectedByBuilding[building] || [];\r\n  }\r\n\r\n  // 輔助方法：檢查建築物是否有已選戶別\r\n  hasBuildingSelected(building: string): boolean {\r\n    return !!(this.selectedByBuilding[building] && this.selectedByBuilding[building].length > 0);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAoCC,UAAU,QAA2B,eAAe;AACvI,SAA+BC,iBAAiB,QAAQ,gBAAgB;AA+BjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAsBpCC,YAAoBC,GAAsB;IAAtB,KAAAA,GAAG,GAAHA,GAAG;IArBd,KAAAC,WAAW,GAAW,OAAO;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,YAAY,GAAiB,EAAE;IAC/B,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,gBAAgB,GAAY,IAAI;IAE/B,KAAAC,eAAe,GAAG,IAAIb,YAAY,EAAmB;IAE/D,KAAAc,MAAM,GAAG,KAAK;IACd,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,SAAS,GAAa,EAAE;IACxB,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,kBAAkB,GAAqC,EAAE;IACzD;IACQ,KAAAC,QAAQ,GAAIC,KAAe,IAAI,CAAG,CAAC;IACnC,KAAAC,SAAS,GAAG,MAAK,CAAG,CAAC;EAEiB;EAE9CC,UAAUA,CAACF,KAAe;IACxB,IAAI,CAACL,kBAAkB,GAAGK,KAAK,IAAI,EAAE;IACrC,IAAI,CAACG,wBAAwB,EAAE;EACjC;EAEAC,gBAAgBA,CAACC,EAA6B;IAC5C,IAAI,CAACN,QAAQ,GAAGM,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACJ,SAAS,GAAGI,EAAE;EACrB;EACAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,CAACtB,QAAQ,GAAGsB,UAAU;EAC5B;EAAEC,QAAQA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACtB,YAAY,IAAIuB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAAC,CAACyB,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAACzB,YAAY,GAAG,IAAI,CAAC0B,gBAAgB,EAAE;IAC7C;IAEA,IAAI,CAACjB,SAAS,GAAGc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAAC;IAC/C2B,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACnB,SAAS,CAAC;IACpEkB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEL,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAAC,CAAC;IAClE,IAAI,CAACgB,wBAAwB,EAAE;EACjC;EAEAa,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MAC3B,IAAI,CAACrB,SAAS,GAAGc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxB,YAAY,CAAC;MAC/C,IAAI,CAAC+B,wBAAwB,EAAE;MAC/B,IAAI,CAACf,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,MAAMgB,OAAO,GAAqC,EAAE;IAEpD,IAAI,CAACxB,kBAAkB,CAACyB,OAAO,CAACC,IAAI,IAAG;MACrC,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAAC1B,SAAS,EAAE;QACrC,MAAM2B,IAAI,GAAG,IAAI,CAACpC,YAAY,CAACmC,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIE,IAAI,EAAE;UACR,IAAI,CAACJ,OAAO,CAACG,QAAQ,CAAC,EAAEH,OAAO,CAACG,QAAQ,CAAC,GAAG,EAAE;UAC9CH,OAAO,CAACG,QAAQ,CAAC,CAACI,IAAI,CAACL,IAAI,CAAC;UAC5B;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACvB,kBAAkB,GAAGqB,OAAO;EACnC;EACQN,gBAAgBA,CAAA;IACtB;IACA,MAAMc,cAAc,GAAG;MACrB,IAAI,EAAEC,KAAK,CAACC,IAAI,CAAC;QAAEjB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEjB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEjB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEjB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;MAChF,IAAI,EAAEL,KAAK,CAACC,IAAI,CAAC;QAAEjB,MAAM,EAAE;MAAE,CAAE,EAAE,CAACkB,CAAC,EAAEC,CAAC,KAAK,IAAIC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;KAChF;IAED;IACA,MAAM9C,YAAY,GAAiB,EAAE;IACrCuB,MAAM,CAACwB,OAAO,CAACP,cAAc,CAAC,CAACP,OAAO,CAAC,CAAC,CAACE,QAAQ,EAAEa,KAAK,CAAC,KAAI;MAC3DhD,YAAY,CAACmC,QAAQ,CAAC,GAAGa,KAAK,CAACC,GAAG,CAACf,IAAI,KAAK;QAC1CA,IAAI;QACJC,QAAQ;QACRe,KAAK,EAAE,GAAGC,IAAI,CAACD,KAAK,CAAC,CAACE,QAAQ,CAAClB,IAAI,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG;QAC9DC,UAAU,EAAE,KAAK;QACjBjC,UAAU,EAAE;OACb,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAOrB,YAAY;EACrB;EAAEuD,gBAAgBA,CAACpB,QAAgB;IACjCR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAAC;IAC3C,IAAI,CAAC7B,gBAAgB,GAAG6B,QAAQ;IAChC,IAAI,CAAC5B,UAAU,GAAG,EAAE;IACpB,IAAI,CAACwB,wBAAwB,EAAE;IAC/BJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAClB,kBAAkB,CAACe,MAAM,CAAC;IACzE;IACA,IAAI,CAAC7B,GAAG,CAAC4D,aAAa,EAAE;EAC1B;EAEAC,eAAeA,CAACtB,QAAgB;IAC9BR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEO,QAAQ,CAAC;EACxD;EAAEJ,wBAAwBA,CAAA;IACxBJ,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACtB,gBAAgB,CAAC;IAChF,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAACI,kBAAkB,GAAG,EAAE;MAC5B;IACF;IAEA,MAAMgD,UAAU,GAAG,IAAI,CAAC1D,YAAY,CAAC,IAAI,CAACM,gBAAgB,CAAC,IAAI,EAAE;IACjEqB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE8B,UAAU,CAACjC,MAAM,CAAC;IAEpE;IACA,IAAI,CAACf,kBAAkB,GAAGgD,UAAU,CACjCT,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,CAChByB,MAAM,CAACzB,IAAI,IAAIA,IAAI,CAAC0B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACtD,UAAU,CAACqD,WAAW,EAAE,CAAC,CAAC;IAE7EjC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAClB,kBAAkB,CAACe,MAAM,CAAC;EAC5E;EAEAqC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACxD,UAAU,GAAGwD,KAAK,CAACC,MAAM,CAACnD,KAAK;IACpC,IAAI,CAACkB,wBAAwB,EAAE;EACjC;EACAkC,iBAAiBA,CAACC,aAAqB;IACrC,MAAMZ,UAAU,GAAG,IAAI,CAAC9C,kBAAkB,CAACqD,QAAQ,CAACK,aAAa,CAAC;IAClE,IAAIC,YAAsB;IAE1B,IAAIb,UAAU,EAAE;MACda,YAAY,GAAG,IAAI,CAAC3D,kBAAkB,CAACmD,MAAM,CAACrB,CAAC,IAAIA,CAAC,KAAK4B,aAAa,CAAC;IACzE,CAAC,MAAM;MACL,IAAI,IAAI,CAACpE,aAAa,IAAI,IAAI,CAACU,kBAAkB,CAACiB,MAAM,IAAI,IAAI,CAAC3B,aAAa,EAAE;QAC9E,OAAO,CAAC;MACV;MACAqE,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC3D,kBAAkB,EAAE0D,aAAa,CAAC;IAC5D;IAEA,IAAI,CAAC1D,kBAAkB,GAAG2D,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,iBAAiBA,CAACH,aAAqB;IACrC,IAAI,CAAC1D,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACmD,MAAM,CAACrB,CAAC,IAAIA,CAAC,KAAK4B,aAAa,CAAC;IAClF,IAAI,CAACE,WAAW,EAAE;EACpB;EACAE,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAChE,gBAAgB,IAAI,IAAI,CAACI,kBAAkB,CAACe,MAAM,KAAK,CAAC,EAAE;IAEpE;IACA,MAAM8C,mBAAmB,GAAG,IAAI,CAAC7D,kBAAkB;IACnD,MAAMyD,YAAY,GAAG,CAAC,GAAG,IAAIK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAChE,kBAAkB,EAAE,GAAG+D,mBAAmB,CAAC,CAAC,CAAC;IAEvF,IAAI,IAAI,CAACzE,aAAa,IAAIqE,YAAY,CAAC1C,MAAM,GAAG,IAAI,CAAC3B,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACU,kBAAkB,GAAG2D,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EACAK,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACnE,gBAAgB,EAAE;IAE5B,MAAMoE,kBAAkB,GAAG,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAACM,gBAAgB,CAAC,EAAE2C,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,MAAMiC,YAAY,GAAG,CAAC,GAAG,IAAIK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAChE,kBAAkB,EAAE,GAAGkE,kBAAkB,CAAC,CAAC,CAAC;IAEtF,IAAI,IAAI,CAAC5E,aAAa,IAAIqE,YAAY,CAAC1C,MAAM,GAAG,IAAI,CAAC3B,aAAa,EAAE;MAClE,OAAO,CAAC;IACV;IAEA,IAAI,CAACU,kBAAkB,GAAG2D,YAAY;IACtC,IAAI,CAACC,WAAW,EAAE;EACpB;EACAO,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACrE,gBAAgB,EAAE;IAE5B,MAAMoE,kBAAkB,GAAG,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAACM,gBAAgB,CAAC,EAAE2C,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,IAAI,CAAC1B,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACmD,MAAM,CAACrB,CAAC,IAAI,CAACoC,kBAAkB,CAACb,QAAQ,CAACvB,CAAC,CAAC,CAAC;IAC9F,IAAI,CAAC8B,WAAW,EAAE;EACpB;EACAQ,UAAUA,CAAA;IACR,IAAI,CAACpE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAC4D,WAAW,EAAE;EACpB;EAEQA,WAAWA,CAAA;IACjB,IAAI,CAACpD,wBAAwB,EAAE;IAC/B,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IAC3C,IAAI,CAACM,SAAS,EAAE;IAEhB,MAAM+D,aAAa,GAAG,IAAI,CAACrE,kBAAkB,CAACyC,GAAG,CAACf,IAAI,IAAG;MACvD,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAAC1B,SAAS,EAAE;QACrC,MAAM2B,IAAI,GAAG,IAAI,CAACpC,YAAY,CAACmC,QAAQ,CAAC,EAAEE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKA,IAAI,CAAC;QACpE,IAAIE,IAAI,EAAE,OAAOA,IAAI;MACvB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAACuB,MAAM,CAACvB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAoB;IAEnD,IAAI,CAAChC,eAAe,CAAC0E,IAAI,CAACD,aAAa,CAAC;EAC1C;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;MAClB,IAAI,CAACM,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1BsB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACvB,MAAM,CAAC;MACrDsB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnB,SAAS,CAAC;IACrD;EACF;EAEAuE,aAAaA,CAAA;IACX,IAAI,CAAC3E,MAAM,GAAG,KAAK;EACrB;EAEA4E,mBAAmBA,CAACf,aAAqB;IACvC,OAAO,IAAI,CAAC1D,kBAAkB,CAACqD,QAAQ,CAACK,aAAa,CAAC;EACxD;EAEAgB,aAAaA,CAAA;IACX,OAAO,CAAC,IAAI,CAACpF,aAAa,IAAI,IAAI,CAACU,kBAAkB,CAACiB,MAAM,GAAG,IAAI,CAAC3B,aAAa;EACnF;EAEAqF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC7E,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMoE,kBAAkB,GAAG,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAACM,gBAAgB,CAAC,CAChEqD,MAAM,CAACrB,CAAC,IAAI,CAACA,CAAC,CAACjB,UAAU,CAAC,CAC1B4B,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC;IACnB,OAAOwC,kBAAkB,CAACjD,MAAM,GAAG,CAAC,IAClCiD,kBAAkB,CAACU,KAAK,CAAClD,IAAI,IAAI,IAAI,CAAC1B,kBAAkB,CAACqD,QAAQ,CAAC3B,IAAI,CAAC,CAAC;EAC5E;EACAmD,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC/E,gBAAgB,EAAE,OAAO,KAAK;IACxC,MAAMoE,kBAAkB,GAAG,IAAI,CAAC1E,YAAY,CAAC,IAAI,CAACM,gBAAgB,CAAC,EAAE2C,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACJ,IAAI,CAAC,IAAI,EAAE;IAC3F,OAAOwC,kBAAkB,CAACY,IAAI,CAACpD,IAAI,IAAI,IAAI,CAAC1B,kBAAkB,CAACqD,QAAQ,CAAC3B,IAAI,CAAC,CAAC;EAChF;EACAqD,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC5E,kBAAkB;EAChC;EAEA6E,gBAAgBA,CAACrD,QAAgB;IAC/B,OAAO,IAAI,CAACnC,YAAY,CAACmC,QAAQ,CAAC,EAAEV,MAAM,IAAI,CAAC;EACjD;EAEAgE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjF,kBAAkB,CAACiB,MAAM;EACvC;EAEA;EACAiE,6BAA6BA,CAACvD,QAAgB;IAC5C,OAAO,IAAI,CAACxB,kBAAkB,CAACwB,QAAQ,CAAC,IAAI,EAAE;EAChD;EAEA;EACAwD,mBAAmBA,CAACxD,QAAgB;IAClC,OAAO,CAAC,EAAE,IAAI,CAACxB,kBAAkB,CAACwB,QAAQ,CAAC,IAAI,IAAI,CAACxB,kBAAkB,CAACwB,QAAQ,CAAC,CAACV,MAAM,GAAG,CAAC,CAAC;EAC9F;CACD;AAnQUmE,UAAA,EAARvG,KAAK,EAAE,C,6DAA+B;AAC9BuG,UAAA,EAARvG,KAAK,EAAE,C,+DAAqC;AACpCuG,UAAA,EAARvG,KAAK,EAAE,C,0DAA2B;AAC1BuG,UAAA,EAARvG,KAAK,EAAE,C,8DAAiC;AAChCuG,UAAA,EAARvG,KAAK,EAAE,C,kEAAkC;AACjCuG,UAAA,EAARvG,KAAK,EAAE,C,6DAA6B;AAC5BuG,UAAA,EAARvG,KAAK,EAAE,C,kEAAkC;AAEhCuG,UAAA,EAATtG,MAAM,EAAE,C,iEAAuD;AATrDI,yBAAyB,GAAAkG,UAAA,EAZrCxG,SAAS,CAAC;EACTyG,QAAQ,EAAE,uBAAuB;EACjCC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC,CAAC;EACjDC,SAAS,EAAE,CACT;IACEC,OAAO,EAAExG,iBAAiB;IAC1ByG,WAAW,EAAE1G,UAAU,CAAC,MAAME,yBAAyB,CAAC;IACxDyG,KAAK,EAAE;GACR;CAEJ,CAAC,C,EACWzG,yBAAyB,CAoQrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}