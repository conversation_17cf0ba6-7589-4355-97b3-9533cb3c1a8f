{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayGridView as DayTableView, TableDateProfileGenerator } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nvar index = createPlugin({\n  name: '@fullcalendar/daygrid',\n  initialView: 'dayGridMonth',\n  views: {\n    dayGrid: {\n      component: DayTableView,\n      dateProfileGeneratorClass: TableDateProfileGenerator\n    },\n    dayGridDay: {\n      type: 'dayGrid',\n      duration: {\n        days: 1\n      }\n    },\n    dayGridWeek: {\n      type: 'dayGrid',\n      duration: {\n        weeks: 1\n      }\n    },\n    dayGridMonth: {\n      type: 'dayGrid',\n      duration: {\n        months: 1\n      },\n      fixedWeekCount: true\n    },\n    dayGridYear: {\n      type: 'dayGrid',\n      duration: {\n        years: 1\n      }\n    }\n  }\n});\nexport { index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}