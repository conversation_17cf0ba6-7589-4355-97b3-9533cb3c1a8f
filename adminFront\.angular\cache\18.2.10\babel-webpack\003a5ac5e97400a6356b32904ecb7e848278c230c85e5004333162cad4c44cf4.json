{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetHourListAppointmentPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHourListAppointmentPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetHourListAppointmentPost$Plain.PATH = '/api/House/GetHourListAppointment';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetHourListAppointmentPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-hour-list-appointment-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetHourListAppointmentReq } from '../../models/get-hour-list-appointment-req';\r\nimport { GetHourListResponeListResponseBase } from '../../models/get-hour-list-respone-list-response-base';\r\n\r\nexport interface ApiHouseGetHourListAppointmentPost$Plain$Params {\r\n      body?: GetHourListAppointmentReq\r\n}\r\n\r\nexport function apiHouseGetHourListAppointmentPost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetHourListAppointmentPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHourListResponeListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHourListAppointmentPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetHourListResponeListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetHourListAppointmentPost$Plain.PATH = '/api/House/GetHourListAppointment';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,wCAAwCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAwD,EAAEC,OAAqB;EACzK,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,wCAAwC,CAACM,IAAI,EAAE,MAAM,CAAC;EAC7F,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA2D;EACpE,CAAC,CAAC,CACH;AACH;AAEAb,wCAAwC,CAACM,IAAI,GAAG,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}