{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiHouseGetHouseChangeDatePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHouseChangeDatePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiHouseGetHouseChangeDatePost$Plain.PATH = '/api/House/GetHouseChangeDate';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiHouseGetHouseChangeDatePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\house\\api-house-get-house-change-date-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetHouseChangeDateReq } from '../../models/get-house-change-date-req';\r\nimport { GetHouseChangeDateResListResponseBase } from '../../models/get-house-change-date-res-list-response-base';\r\n\r\nexport interface ApiHouseGetHouseChangeDatePost$Plain$Params {\r\n      body?: GetHouseChangeDateReq\r\n}\r\n\r\nexport function apiHouseGetHouseChangeDatePost$Plain(http: HttpClient, rootUrl: string, params?: ApiHouseGetHouseChangeDatePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetHouseChangeDateResListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiHouseGetHouseChangeDatePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetHouseChangeDateResListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiHouseGetHouseChangeDatePost$Plain.PATH = '/api/House/GetHouseChangeDate';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,oCAAoCA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAoD,EAAEC,OAAqB;EACjK,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,oCAAoC,CAACM,IAAI,EAAE,MAAM,CAAC;EACzF,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA8D;EACvE,CAAC,CAAC,CACH;AACH;AAEAb,oCAAoC,CAACM,IAAI,GAAG,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}