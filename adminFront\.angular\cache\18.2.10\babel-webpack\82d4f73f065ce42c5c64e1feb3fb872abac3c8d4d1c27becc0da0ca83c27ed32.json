{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-json';\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-plain';\nimport { apiSpecialChangeGetApproveWaitingListPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-json';\nimport { apiSpecialChangeGetApproveWaitingListPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-plain';\nimport { apiSpecialChangeGetListSpecialChangePost$Json } from '../fn/special-change/api-special-change-get-list-special-change-post-json';\nimport { apiSpecialChangeGetListSpecialChangePost$Plain } from '../fn/special-change/api-special-change-get-list-special-change-post-plain';\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Json } from '../fn/special-change/api-special-change-get-special-change-by-id-post-json';\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Plain } from '../fn/special-change/api-special-change-get-special-change-by-id-post-plain';\nimport { apiSpecialChangeGetSpecialChangeFilePost$Json } from '../fn/special-change/api-special-change-get-special-change-file-post-json';\nimport { apiSpecialChangeGetSpecialChangeFilePost$Plain } from '../fn/special-change/api-special-change-get-special-change-file-post-plain';\nimport { apiSpecialChangeSaveSpecialChangePost$Json } from '../fn/special-change/api-special-change-save-special-change-post-json';\nimport { apiSpecialChangeSaveSpecialChangePost$Plain } from '../fn/special-change/api-special-change-save-special-change-post-plain';\nimport { apiSpecialChangeUpdateApproveWaitingPost$Json } from '../fn/special-change/api-special-change-update-approve-waiting-post-json';\nimport { apiSpecialChangeUpdateApproveWaitingPost$Plain } from '../fn/special-change/api-special-change-update-approve-waiting-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let SpecialChangeService = /*#__PURE__*/(() => {\n  class SpecialChangeService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiSpecialChangeGetSpecialChangeFilePost()` */\n    static {\n      this.ApiSpecialChangeGetSpecialChangeFilePostPath = '/api/SpecialChange/GetSpecialChangeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params, context) {\n      return apiSpecialChangeGetSpecialChangeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeFilePost$Plain(params, context) {\n      return this.apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params, context) {\n      return apiSpecialChangeGetSpecialChangeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeFilePost$Json(params, context) {\n      return this.apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeGetListSpecialChangePost()` */\n    static {\n      this.ApiSpecialChangeGetListSpecialChangePostPath = '/api/SpecialChange/GetListSpecialChange';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetListSpecialChangePost$Plain$Response(params, context) {\n      return apiSpecialChangeGetListSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetListSpecialChangePost$Plain(params, context) {\n      return this.apiSpecialChangeGetListSpecialChangePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetListSpecialChangePost$Json$Response(params, context) {\n      return apiSpecialChangeGetListSpecialChangePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetListSpecialChangePost$Json(params, context) {\n      return this.apiSpecialChangeGetListSpecialChangePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeGetSpecialChangeByIdPost()` */\n    static {\n      this.ApiSpecialChangeGetSpecialChangeByIdPostPath = '/api/SpecialChange/GetSpecialChangeByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params, context) {\n      return apiSpecialChangeGetSpecialChangeByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeByIdPost$Plain(params, context) {\n      return this.apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params, context) {\n      return apiSpecialChangeGetSpecialChangeByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetSpecialChangeByIdPost$Json(params, context) {\n      return this.apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeSaveSpecialChangePost()` */\n    static {\n      this.ApiSpecialChangeSaveSpecialChangePostPath = '/api/SpecialChange/SaveSpecialChange';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeSaveSpecialChangePost$Plain$Response(params, context) {\n      return apiSpecialChangeSaveSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeSaveSpecialChangePost$Plain(params, context) {\n      return this.apiSpecialChangeSaveSpecialChangePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeSaveSpecialChangePost$Json$Response(params, context) {\n      return apiSpecialChangeSaveSpecialChangePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeSaveSpecialChangePost$Json(params, context) {\n      return this.apiSpecialChangeSaveSpecialChangePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeGetApproveWaitingListPost()` */\n    static {\n      this.ApiSpecialChangeGetApproveWaitingListPostPath = '/api/SpecialChange/GetApproveWaitingList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params, context) {\n      return apiSpecialChangeGetApproveWaitingListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingListPost$Plain(params, context) {\n      return this.apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingListPost$Json$Response(params, context) {\n      return apiSpecialChangeGetApproveWaitingListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingListPost$Json(params, context) {\n      return this.apiSpecialChangeGetApproveWaitingListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeGetApproveWaitingByIdPost()` */\n    static {\n      this.ApiSpecialChangeGetApproveWaitingByIdPostPath = '/api/SpecialChange/GetApproveWaitingByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params, context) {\n      return apiSpecialChangeGetApproveWaitingByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingByIdPost$Plain(params, context) {\n      return this.apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params, context) {\n      return apiSpecialChangeGetApproveWaitingByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeGetApproveWaitingByIdPost$Json(params, context) {\n      return this.apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialChangeUpdateApproveWaitingPost()` */\n    static {\n      this.ApiSpecialChangeUpdateApproveWaitingPostPath = '/api/SpecialChange/UpdateApproveWaiting';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params, context) {\n      return apiSpecialChangeUpdateApproveWaitingPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeUpdateApproveWaitingPost$Plain(params, context) {\n      return this.apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params, context) {\n      return apiSpecialChangeUpdateApproveWaitingPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialChangeUpdateApproveWaitingPost$Json(params, context) {\n      return this.apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function SpecialChangeService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpecialChangeService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SpecialChangeService,\n        factory: SpecialChangeService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SpecialChangeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}