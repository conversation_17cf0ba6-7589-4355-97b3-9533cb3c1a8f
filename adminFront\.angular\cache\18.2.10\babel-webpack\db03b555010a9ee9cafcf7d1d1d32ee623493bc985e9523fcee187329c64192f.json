{"ast": null, "code": "'use strict';\n\nmodule.exports = ansiHTML;\n\n// Reference to https://github.com/sindresorhus/ansi-regex\nvar _regANSI = /(?:(?:\\u001b\\[)|\\u009b)(?:(?:[0-9]{1,3})?(?:(?:;[0-9]{0,3})*)?[A-M|f-m])|\\u001b[A-M]/;\nvar _defColors = {\n  reset: ['fff', '000'],\n  // [FOREGROUD_COLOR, BACKGROUND_COLOR]\n  black: '000',\n  red: 'ff0000',\n  green: '209805',\n  yellow: 'e8bf03',\n  blue: '0000ff',\n  magenta: 'ff00ff',\n  cyan: '00ffee',\n  lightgrey: 'f0f0f0',\n  darkgrey: '888'\n};\nvar _styles = {\n  30: 'black',\n  31: 'red',\n  32: 'green',\n  33: 'yellow',\n  34: 'blue',\n  35: 'magenta',\n  36: 'cyan',\n  37: 'lightgrey'\n};\nvar _openTags = {\n  '1': 'font-weight:bold',\n  // bold\n  '2': 'opacity:0.5',\n  // dim\n  '3': '<i>',\n  // italic\n  '4': '<u>',\n  // underscore\n  '8': 'display:none',\n  // hidden\n  '9': '<del>' // delete\n};\nvar _closeTags = {\n  '23': '</i>',\n  // reset italic\n  '24': '</u>',\n  // reset underscore\n  '29': '</del>' // reset delete\n};\n[0, 21, 22, 27, 28, 39, 49].forEach(function (n) {\n  _closeTags[n] = '</span>';\n});\n\n/**\n * Converts text with ANSI color codes to HTML markup.\n * @param {String} text\n * @returns {*}\n */\nfunction ansiHTML(text) {\n  // Returns the text if the string has no ANSI escape code.\n  if (!_regANSI.test(text)) {\n    return text;\n  }\n\n  // Cache opened sequence.\n  var ansiCodes = [];\n  // Replace with markup.\n  var ret = text.replace(/\\033\\[(\\d+)m/g, function (match, seq) {\n    var ot = _openTags[seq];\n    if (ot) {\n      // If current sequence has been opened, close it.\n      if (!!~ansiCodes.indexOf(seq)) {\n        // eslint-disable-line no-extra-boolean-cast\n        ansiCodes.pop();\n        return '</span>';\n      }\n      // Open tag.\n      ansiCodes.push(seq);\n      return ot[0] === '<' ? ot : '<span style=\"' + ot + ';\">';\n    }\n    var ct = _closeTags[seq];\n    if (ct) {\n      // Pop sequence\n      ansiCodes.pop();\n      return ct;\n    }\n    return '';\n  });\n\n  // Make sure tags are closed.\n  var l = ansiCodes.length;\n  l > 0 && (ret += Array(l + 1).join('</span>'));\n  return ret;\n}\n\n/**\n * Customize colors.\n * @param {Object} colors reference to _defColors\n */\nansiHTML.setColors = function (colors) {\n  if (typeof colors !== 'object') {\n    throw new Error('`colors` parameter must be an Object.');\n  }\n  var _finalColors = {};\n  for (var key in _defColors) {\n    var hex = colors.hasOwnProperty(key) ? colors[key] : null;\n    if (!hex) {\n      _finalColors[key] = _defColors[key];\n      continue;\n    }\n    if ('reset' === key) {\n      if (typeof hex === 'string') {\n        hex = [hex];\n      }\n      if (!Array.isArray(hex) || hex.length === 0 || hex.some(function (h) {\n        return typeof h !== 'string';\n      })) {\n        throw new Error('The value of `' + key + '` property must be an Array and each item could only be a hex string, e.g.: FF0000');\n      }\n      var defHexColor = _defColors[key];\n      if (!hex[0]) {\n        hex[0] = defHexColor[0];\n      }\n      if (hex.length === 1 || !hex[1]) {\n        hex = [hex[0]];\n        hex.push(defHexColor[1]);\n      }\n      hex = hex.slice(0, 2);\n    } else if (typeof hex !== 'string') {\n      throw new Error('The value of `' + key + '` property must be a hex string, e.g.: FF0000');\n    }\n    _finalColors[key] = hex;\n  }\n  _setTags(_finalColors);\n};\n\n/**\n * Reset colors.\n */\nansiHTML.reset = function () {\n  _setTags(_defColors);\n};\n\n/**\n * Expose tags, including open and close.\n * @type {Object}\n */\nansiHTML.tags = {};\nif (Object.defineProperty) {\n  Object.defineProperty(ansiHTML.tags, 'open', {\n    get: function () {\n      return _openTags;\n    }\n  });\n  Object.defineProperty(ansiHTML.tags, 'close', {\n    get: function () {\n      return _closeTags;\n    }\n  });\n} else {\n  ansiHTML.tags.open = _openTags;\n  ansiHTML.tags.close = _closeTags;\n}\nfunction _setTags(colors) {\n  // reset all\n  _openTags['0'] = 'font-weight:normal;opacity:1;color:#' + colors.reset[0] + ';background:#' + colors.reset[1];\n  // inverse\n  _openTags['7'] = 'color:#' + colors.reset[1] + ';background:#' + colors.reset[0];\n  // dark grey\n  _openTags['90'] = 'color:#' + colors.darkgrey;\n  for (var code in _styles) {\n    var color = _styles[code];\n    var oriColor = colors[color] || '000';\n    _openTags[code] = 'color:#' + oriColor;\n    code = parseInt(code);\n    _openTags[(code + 10).toString()] = 'background:#' + oriColor;\n  }\n}\nansiHTML.reset();", "map": {"version": 3, "names": ["module", "exports", "ansiHTML", "_regANSI", "_defColors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "_styles", "_openTags", "_closeTags", "for<PERSON>ach", "n", "text", "test", "ansiCodes", "ret", "replace", "match", "seq", "ot", "indexOf", "pop", "push", "ct", "l", "length", "Array", "join", "setColors", "colors", "Error", "_finalColors", "key", "hex", "hasOwnProperty", "isArray", "some", "h", "defHexColor", "slice", "_setTags", "tags", "Object", "defineProperty", "get", "open", "close", "code", "color", "oriColor", "parseInt", "toString"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/ansi-html-community/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ansiHTML\n\n// Reference to https://github.com/sindresorhus/ansi-regex\nvar _regANSI = /(?:(?:\\u001b\\[)|\\u009b)(?:(?:[0-9]{1,3})?(?:(?:;[0-9]{0,3})*)?[A-M|f-m])|\\u001b[A-M]/\n\nvar _defColors = {\n  reset: ['fff', '000'], // [FOREGROUD_COLOR, BACKGROUND_COLOR]\n  black: '000',\n  red: 'ff0000',\n  green: '209805',\n  yellow: 'e8bf03',\n  blue: '0000ff',\n  magenta: 'ff00ff',\n  cyan: '00ffee',\n  lightgrey: 'f0f0f0',\n  darkgrey: '888'\n}\nvar _styles = {\n  30: 'black',\n  31: 'red',\n  32: 'green',\n  33: 'yellow',\n  34: 'blue',\n  35: 'magenta',\n  36: 'cyan',\n  37: 'lightgrey'\n}\nvar _openTags = {\n  '1': 'font-weight:bold', // bold\n  '2': 'opacity:0.5', // dim\n  '3': '<i>', // italic\n  '4': '<u>', // underscore\n  '8': 'display:none', // hidden\n  '9': '<del>' // delete\n}\nvar _closeTags = {\n  '23': '</i>', // reset italic\n  '24': '</u>', // reset underscore\n  '29': '</del>' // reset delete\n}\n\n;[0, 21, 22, 27, 28, 39, 49].forEach(function (n) {\n  _closeTags[n] = '</span>'\n})\n\n/**\n * Converts text with ANSI color codes to HTML markup.\n * @param {String} text\n * @returns {*}\n */\nfunction ansiHTML (text) {\n  // Returns the text if the string has no ANSI escape code.\n  if (!_regANSI.test(text)) {\n    return text\n  }\n\n  // Cache opened sequence.\n  var ansiCodes = []\n  // Replace with markup.\n  var ret = text.replace(/\\033\\[(\\d+)m/g, function (match, seq) {\n    var ot = _openTags[seq]\n    if (ot) {\n      // If current sequence has been opened, close it.\n      if (!!~ansiCodes.indexOf(seq)) { // eslint-disable-line no-extra-boolean-cast\n        ansiCodes.pop()\n        return '</span>'\n      }\n      // Open tag.\n      ansiCodes.push(seq)\n      return ot[0] === '<' ? ot : '<span style=\"' + ot + ';\">'\n    }\n\n    var ct = _closeTags[seq]\n    if (ct) {\n      // Pop sequence\n      ansiCodes.pop()\n      return ct\n    }\n    return ''\n  })\n\n  // Make sure tags are closed.\n  var l = ansiCodes.length\n  ;(l > 0) && (ret += Array(l + 1).join('</span>'))\n\n  return ret\n}\n\n/**\n * Customize colors.\n * @param {Object} colors reference to _defColors\n */\nansiHTML.setColors = function (colors) {\n  if (typeof colors !== 'object') {\n    throw new Error('`colors` parameter must be an Object.')\n  }\n\n  var _finalColors = {}\n  for (var key in _defColors) {\n    var hex = colors.hasOwnProperty(key) ? colors[key] : null\n    if (!hex) {\n      _finalColors[key] = _defColors[key]\n      continue\n    }\n    if ('reset' === key) {\n      if (typeof hex === 'string') {\n        hex = [hex]\n      }\n      if (!Array.isArray(hex) || hex.length === 0 || hex.some(function (h) {\n        return typeof h !== 'string'\n      })) {\n        throw new Error('The value of `' + key + '` property must be an Array and each item could only be a hex string, e.g.: FF0000')\n      }\n      var defHexColor = _defColors[key]\n      if (!hex[0]) {\n        hex[0] = defHexColor[0]\n      }\n      if (hex.length === 1 || !hex[1]) {\n        hex = [hex[0]]\n        hex.push(defHexColor[1])\n      }\n\n      hex = hex.slice(0, 2)\n    } else if (typeof hex !== 'string') {\n      throw new Error('The value of `' + key + '` property must be a hex string, e.g.: FF0000')\n    }\n    _finalColors[key] = hex\n  }\n  _setTags(_finalColors)\n}\n\n/**\n * Reset colors.\n */\nansiHTML.reset = function () {\n  _setTags(_defColors)\n}\n\n/**\n * Expose tags, including open and close.\n * @type {Object}\n */\nansiHTML.tags = {}\n\nif (Object.defineProperty) {\n  Object.defineProperty(ansiHTML.tags, 'open', {\n    get: function () { return _openTags }\n  })\n  Object.defineProperty(ansiHTML.tags, 'close', {\n    get: function () { return _closeTags }\n  })\n} else {\n  ansiHTML.tags.open = _openTags\n  ansiHTML.tags.close = _closeTags\n}\n\nfunction _setTags (colors) {\n  // reset all\n  _openTags['0'] = 'font-weight:normal;opacity:1;color:#' + colors.reset[0] + ';background:#' + colors.reset[1]\n  // inverse\n  _openTags['7'] = 'color:#' + colors.reset[1] + ';background:#' + colors.reset[0]\n  // dark grey\n  _openTags['90'] = 'color:#' + colors.darkgrey\n\n  for (var code in _styles) {\n    var color = _styles[code]\n    var oriColor = colors[color] || '000'\n    _openTags[code] = 'color:#' + oriColor\n    code = parseInt(code)\n    _openTags[(code + 10).toString()] = 'background:#' + oriColor\n  }\n}\n\nansiHTML.reset()\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;;AAEzB;AACA,IAAIC,QAAQ,GAAG,sFAAsF;AAErG,IAAIC,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAAE;EACvBC,KAAK,EAAE,KAAK;EACZC,GAAG,EAAE,QAAQ;EACbC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,OAAO,GAAG;EACZ,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,QAAQ;EACZ,EAAE,EAAE,MAAM;EACV,EAAE,EAAE,SAAS;EACb,EAAE,EAAE,MAAM;EACV,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,SAAS,GAAG;EACd,GAAG,EAAE,kBAAkB;EAAE;EACzB,GAAG,EAAE,aAAa;EAAE;EACpB,GAAG,EAAE,KAAK;EAAE;EACZ,GAAG,EAAE,KAAK;EAAE;EACZ,GAAG,EAAE,cAAc;EAAE;EACrB,GAAG,EAAE,OAAO,CAAC;AACf,CAAC;AACD,IAAIC,UAAU,GAAG;EACf,IAAI,EAAE,MAAM;EAAE;EACd,IAAI,EAAE,MAAM;EAAE;EACd,IAAI,EAAE,QAAQ,CAAC;AACjB,CAAC;AAEA,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;EAChDF,UAAU,CAACE,CAAC,CAAC,GAAG,SAAS;AAC3B,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,SAASjB,QAAQA,CAAEkB,IAAI,EAAE;EACvB;EACA,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACD,IAAI,CAAC,EAAE;IACxB,OAAOA,IAAI;EACb;;EAEA;EACA,IAAIE,SAAS,GAAG,EAAE;EAClB;EACA,IAAIC,GAAG,GAAGH,IAAI,CAACI,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC5D,IAAIC,EAAE,GAAGX,SAAS,CAACU,GAAG,CAAC;IACvB,IAAIC,EAAE,EAAE;MACN;MACA,IAAI,CAAC,CAAC,CAACL,SAAS,CAACM,OAAO,CAACF,GAAG,CAAC,EAAE;QAAE;QAC/BJ,SAAS,CAACO,GAAG,CAAC,CAAC;QACf,OAAO,SAAS;MAClB;MACA;MACAP,SAAS,CAACQ,IAAI,CAACJ,GAAG,CAAC;MACnB,OAAOC,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,EAAE,GAAG,eAAe,GAAGA,EAAE,GAAG,KAAK;IAC1D;IAEA,IAAII,EAAE,GAAGd,UAAU,CAACS,GAAG,CAAC;IACxB,IAAIK,EAAE,EAAE;MACN;MACAT,SAAS,CAACO,GAAG,CAAC,CAAC;MACf,OAAOE,EAAE;IACX;IACA,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,IAAIC,CAAC,GAAGV,SAAS,CAACW,MAAM;EACtBD,CAAC,GAAG,CAAC,KAAMT,GAAG,IAAIW,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,SAAS,CAAC,CAAC;EAEjD,OAAOZ,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACArB,QAAQ,CAACkC,SAAS,GAAG,UAAUC,MAAM,EAAE;EACrC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,uCAAuC,CAAC;EAC1D;EAEA,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,KAAK,IAAIC,GAAG,IAAIpC,UAAU,EAAE;IAC1B,IAAIqC,GAAG,GAAGJ,MAAM,CAACK,cAAc,CAACF,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC,GAAG,IAAI;IACzD,IAAI,CAACC,GAAG,EAAE;MACRF,YAAY,CAACC,GAAG,CAAC,GAAGpC,UAAU,CAACoC,GAAG,CAAC;MACnC;IACF;IACA,IAAI,OAAO,KAAKA,GAAG,EAAE;MACnB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;QAC3BA,GAAG,GAAG,CAACA,GAAG,CAAC;MACb;MACA,IAAI,CAACP,KAAK,CAACS,OAAO,CAACF,GAAG,CAAC,IAAIA,GAAG,CAACR,MAAM,KAAK,CAAC,IAAIQ,GAAG,CAACG,IAAI,CAAC,UAAUC,CAAC,EAAE;QACnE,OAAO,OAAOA,CAAC,KAAK,QAAQ;MAC9B,CAAC,CAAC,EAAE;QACF,MAAM,IAAIP,KAAK,CAAC,gBAAgB,GAAGE,GAAG,GAAG,oFAAoF,CAAC;MAChI;MACA,IAAIM,WAAW,GAAG1C,UAAU,CAACoC,GAAG,CAAC;MACjC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE;QACXA,GAAG,CAAC,CAAC,CAAC,GAAGK,WAAW,CAAC,CAAC,CAAC;MACzB;MACA,IAAIL,GAAG,CAACR,MAAM,KAAK,CAAC,IAAI,CAACQ,GAAG,CAAC,CAAC,CAAC,EAAE;QAC/BA,GAAG,GAAG,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC;QACdA,GAAG,CAACX,IAAI,CAACgB,WAAW,CAAC,CAAC,CAAC,CAAC;MAC1B;MAEAL,GAAG,GAAGA,GAAG,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,OAAON,GAAG,KAAK,QAAQ,EAAE;MAClC,MAAM,IAAIH,KAAK,CAAC,gBAAgB,GAAGE,GAAG,GAAG,+CAA+C,CAAC;IAC3F;IACAD,YAAY,CAACC,GAAG,CAAC,GAAGC,GAAG;EACzB;EACAO,QAAQ,CAACT,YAAY,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACArC,QAAQ,CAACG,KAAK,GAAG,YAAY;EAC3B2C,QAAQ,CAAC5C,UAAU,CAAC;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACAF,QAAQ,CAAC+C,IAAI,GAAG,CAAC,CAAC;AAElB,IAAIC,MAAM,CAACC,cAAc,EAAE;EACzBD,MAAM,CAACC,cAAc,CAACjD,QAAQ,CAAC+C,IAAI,EAAE,MAAM,EAAE;IAC3CG,GAAG,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAOpC,SAAS;IAAC;EACtC,CAAC,CAAC;EACFkC,MAAM,CAACC,cAAc,CAACjD,QAAQ,CAAC+C,IAAI,EAAE,OAAO,EAAE;IAC5CG,GAAG,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAOnC,UAAU;IAAC;EACvC,CAAC,CAAC;AACJ,CAAC,MAAM;EACLf,QAAQ,CAAC+C,IAAI,CAACI,IAAI,GAAGrC,SAAS;EAC9Bd,QAAQ,CAAC+C,IAAI,CAACK,KAAK,GAAGrC,UAAU;AAClC;AAEA,SAAS+B,QAAQA,CAAEX,MAAM,EAAE;EACzB;EACArB,SAAS,CAAC,GAAG,CAAC,GAAG,sCAAsC,GAAGqB,MAAM,CAAChC,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,GAAGgC,MAAM,CAAChC,KAAK,CAAC,CAAC,CAAC;EAC7G;EACAW,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,GAAGqB,MAAM,CAAChC,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,GAAGgC,MAAM,CAAChC,KAAK,CAAC,CAAC,CAAC;EAChF;EACAW,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,GAAGqB,MAAM,CAACvB,QAAQ;EAE7C,KAAK,IAAIyC,IAAI,IAAIxC,OAAO,EAAE;IACxB,IAAIyC,KAAK,GAAGzC,OAAO,CAACwC,IAAI,CAAC;IACzB,IAAIE,QAAQ,GAAGpB,MAAM,CAACmB,KAAK,CAAC,IAAI,KAAK;IACrCxC,SAAS,CAACuC,IAAI,CAAC,GAAG,SAAS,GAAGE,QAAQ;IACtCF,IAAI,GAAGG,QAAQ,CAACH,IAAI,CAAC;IACrBvC,SAAS,CAAC,CAACuC,IAAI,GAAG,EAAE,EAAEI,QAAQ,CAAC,CAAC,CAAC,GAAG,cAAc,GAAGF,QAAQ;EAC/D;AACF;AAEAvD,QAAQ,CAACG,KAAK,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}