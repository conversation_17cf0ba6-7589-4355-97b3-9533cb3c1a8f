{"ast": null, "code": "import { ReplaySubject } from \"rxjs\";\nimport * as i0 from \"@angular/core\";\nexport class EventService {\n  constructor() {\n    this.subjet = new ReplaySubject();\n  }\n  push(data) {\n    this.subjet.next(data);\n  }\n  receive() {\n    return this.subjet;\n  }\n  static {\n    this.ɵfac = function EventService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EventService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EventService,\n      factory: EventService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ReplaySubject", "EventService", "constructor", "subjet", "push", "data", "next", "receive", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\services\\event.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ReplaySubject } from \"rxjs\";\r\n\r\nexport const enum EEvent {\r\n    GET_BUILDCASE = 'GET_BUILDCASE',\r\n    CHANGE_ROLE = 'CHANGE_ROLE'\r\n}\r\n\r\nexport interface IEvent {\r\n    action: EEvent;\r\n    payload: any;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class EventService {\r\n    private subjet = new ReplaySubject<IEvent>();\r\n\r\n    constructor() { }\r\n\r\n    push(data: IEvent) {\r\n        this.subjet.next(data);\r\n    }\r\n\r\n    receive() {\r\n        return this.subjet;\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,MAAM;;AAepC,OAAM,MAAOC,YAAY;EAGrBC,YAAA;IAFQ,KAAAC,MAAM,GAAG,IAAIH,aAAa,EAAU;EAE5B;EAEhBI,IAAIA,CAACC,IAAY;IACb,IAAI,CAACF,MAAM,CAACG,IAAI,CAACD,IAAI,CAAC;EAC1B;EAEAE,OAAOA,CAAA;IACH,OAAO,IAAI,CAACJ,MAAM;EACtB;;;uCAXSF,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAO,OAAA,EAAZP,YAAY,CAAAQ,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}