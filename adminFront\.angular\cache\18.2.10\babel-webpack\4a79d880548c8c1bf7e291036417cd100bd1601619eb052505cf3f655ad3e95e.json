{"ast": null, "code": "import { SharedModule } from '../../../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { LabelInOptionsPipe } from 'src/app/@theme/pipes/mapping.pipe';\nimport { BaseComponent } from '../../../components/base/baseComponent';\nimport { tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"src/app/@core/service/review.service\";\nimport * as i7 from \"src/app/shared/services/utility.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"../../../components/breadcrumb/breadcrumb.component\";\nimport * as i12 from \"../../../../@theme/directives/label.directive\";\nimport * as i13 from \"../../../../@theme/pipes/date-format.pipe\";\nconst _c0 = [\"fileInput\"];\nconst _c1 = a0 => ({\n  \"!text-red\": a0\n});\nfunction ReviewDocumentManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_nb_option_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSearch());\n    });\n    i0.ɵɵtext(1, \" \\u67E5\\u8A62 \");\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9));\n    });\n    i0.ɵɵtext(1, \" \\u65B0\\u589E \");\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      const dialog_r9 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r6.openModel(dialog_r9, item_r11));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReviewDocumentManagementComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"getLabelInOptions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\", 36);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\", 37);\n    i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_tr_60_button_18_Template, 2, 0, \"button\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 7, item_r11.CReviewType, ctx_r6.reviewTypeOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CReviewName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CHouse, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(10, 10, item_r11.CStatus, ctx_r6.statusOptions), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(13, 13, item_r11.CExamineStatus, ctx_r6.examineStatusOptionsQuery), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.CActionDate ? i0.ɵɵpipeBind1(16, 16, item_r11.CActionDate) : \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isUpdate);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r13);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r13.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_div_23_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.clearImage());\n    });\n    i0.ɵɵelement(4, \"i\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.fileName);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\", 67)(2, \"a\", 68);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_div_24_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.openPdfInNewTab(ctx_r6.selectedReview.CFileUrl));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.imageUrl ? \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedReview.CFileUrl, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_nb_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r16);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r16.label, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\")(1, \"nb-checkbox\", 72);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener($event) {\n      const idx_r18 = i0.ɵɵrestoreView(_r17).index;\n      const ctx_r6 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r6.enableAllAtIndex($event, idx_r18));\n    });\n    i0.ɵɵelementStart(2, \"span\", 73);\n    i0.ɵɵtext(3, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const idx_r18 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllColumnChecked(idx_r18));\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_th_2_Template, 4, 1, \"th\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D[0]);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\");\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"nb-checkbox\", 74);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const house_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r22.CIsSelect, $event) || (house_r22.CIsSelect = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(3, \"span\", 75);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r22 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"checked\", house_r22.CIsSelect);\n    i0.ɵɵproperty(\"disabled\", !house_r22.CHouseHold || !house_r22.CIsEnable || ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, house_r22.CID));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", house_r22.CHouseHold || \"null\", \" - \", house_r22.CFloor, \" \");\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 74);\n    i0.ɵɵlistener(\"checkedChange\", function ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_Template_nb_checkbox_checkedChange_2_listener($event) {\n      const row_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.enableAllRow($event, row_r20));\n    });\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_td_5_Template, 5, 7, \"td\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r20 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r6.isCheckAllRowChecked(row_r20))(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", row_r20);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"table\", 70)(2, \"thead\");\n    i0.ɵɵtemplate(3, ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_Template, 3, 1, \"tr\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"tbody\");\n    i0.ɵɵtemplate(5, ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_Template, 6, 3, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.houseList2D.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.houseList2D);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_45_tr_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"dateFormat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r24 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 4, row_r24.CCreateDt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(row_r24.CCreator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r24.CAction);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r24.CExamineNote);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"div\", 44)(2, \"label\", 77);\n    i0.ɵɵtext(3, \"\\u5BE9\\u6838\\u6B77\\u7A0B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"table\", 70)(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"\\u6642\\u9593\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"\\u4F7F\\u7528\\u8005\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"\\u52D5\\u4F5C\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"\\u8AAA\\u660E\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"tbody\");\n    i0.ɵɵtemplate(16, ReviewDocumentManagementComponent_ng_template_63_div_45_tr_16_Template, 10, 6, \"tr\", 28);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.selectedReview.tblExamineLogs);\n  }\n}\nfunction ReviewDocumentManagementComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 40)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 41)(4, \"div\", 5)(5, \"label\", 42);\n    i0.ɵɵtext(6, \"\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"nb-select\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.selectedReviewType, $event) || (ctx_r6.selectedReview.selectedReviewType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(8, ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 44)(10, \"div\", 5)(11, \"label\", 45);\n    i0.ɵɵtext(12, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 46);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CReviewName, $event) || (ctx_r6.selectedReview.CReviewName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 44)(15, \"div\", 5)(16, \"label\", 47);\n    i0.ɵɵtext(17, \" \\u4E0A\\u50B3\\u6A94\\u6848 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 48)(19, \"input\", 49);\n    i0.ɵɵlistener(\"change\", function ReviewDocumentManagementComponent_ng_template_63_Template_input_change_19_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 50);\n    i0.ɵɵelement(21, \"i\", 51);\n    i0.ɵɵtext(22, \" \\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ReviewDocumentManagementComponent_ng_template_63_div_23_Template, 5, 1, \"div\", 52)(24, ReviewDocumentManagementComponent_ng_template_63_div_24_Template, 4, 2, \"div\", 52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 44)(26, \"div\", 5)(27, \"label\", 53);\n    i0.ɵɵtext(28, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-select\", 54);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.seletedStatus, $event) || (ctx_r6.selectedReview.seletedStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(30, ReviewDocumentManagementComponent_ng_template_63_nb_option_30_Template, 2, 2, \"nb-option\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 44)(32, \"div\", 5)(33, \"label\", 55);\n    i0.ɵɵtext(34, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"textarea\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r6 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r6.selectedReview.CExamineNote, $event) || (ctx_r6.selectedReview.CExamineNote = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 57)(37, \"label\", 58);\n    i0.ɵɵtext(38, \"\\u9069\\u7528\\u6236\\u5225\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(39, ReviewDocumentManagementComponent_ng_template_63_div_39_Template, 6, 2, \"div\", 59);\n    i0.ɵɵelementStart(40, \"div\", 29)(41, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_41_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onClose(ref_r23));\n    });\n    i0.ɵɵtext(42, \" \\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function ReviewDocumentManagementComponent_ng_template_63_Template_button_click_43_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r12).dialogRef;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onSaveReview(ref_r23));\n    });\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(45, ReviewDocumentManagementComponent_ng_template_63_div_45_Template, 17, 1, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u5BE9\\u95B1\\u6587\\u4EF6\" : \"\\u7DE8\\u8F2F\\u5BE9\\u95B1\\u6587\\u4EF6\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.selectedReviewType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.reviewTypeOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CReviewName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"opacity-50\", ctx_r6.latestAction === 1)(\"cursor-pointer\", ctx_r6.latestAction !== 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.fileName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedReview.CFileUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.seletedStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r6.selectedReview.CExamineNote);\n    i0.ɵɵproperty(\"rows\", 4)(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isHouseList);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r6.latestAction === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.isNew ? \"\\u65B0\\u589E\\u4E26\\u9001\\u51FA\\u5BE9\\u6838\" : \"\\u9001\\u51FA\\u5BE9\\u6838\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isNew);\n  }\n}\nexport class ReviewDocumentManagementComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, _reviewService, reviewService, utilityService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this._reviewService = _reviewService;\n    this.reviewService = reviewService;\n    this.utilityService = utilityService;\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    this.reviewTypeOptions = [{\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.reviewTypeOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      label: '標準圖' //standard drawing\n    }, {\n      value: 2,\n      label: '設備圖' //equipment drawing\n    }];\n    this.examineStatusOptions = [{\n      value: -1,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.examineStatusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 0,\n      label: '待審核' //Pending review\n    }, {\n      value: 1,\n      label: '已通過' //passed\n    }, {\n      value: 2,\n      label: '已駁回' //rejected\n    }];\n    this.statusOptions = [{\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.statusOptionsQuery = [{\n      value: -1,\n      label: '全部'\n    }, {\n      value: 1,\n      //0停用 1啟用 9刪除\n      label: '啟用' //enable\n    }, {\n      value: 2,\n      label: '停用' //Disable\n    }];\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.fileName = null;\n    this.imageUrl = undefined;\n    this.isHouseList = false;\n    this.latestAction = 0;\n    this.isNew = true;\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      selectedBuildCase: null,\n      selectedReviewType: this.reviewTypeOptionsQuery[0],\n      selectedExamineStatus: this.examineStatusOptionsQuery[0],\n      seletedStatus: this.statusOptionsQuery[0],\n      CReviewName: ''\n    };\n    this.getUserBuildCase();\n  }\n  clearImage() {\n    if (this.imageUrl) {\n      this.imageUrl = null;\n      this.fileName = null;\n      if (this.fileInput) {\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\n      }\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    const fileRegex = /pdf|jpg|jpeg|png/i;\n    if (!fileRegex.test(file.type)) {\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\n      return;\n    }\n    if (file) {\n      const allowedTypes = ['application/pdf'];\n      if (allowedTypes.includes(file.type)) {\n        this.fileName = file.name;\n        const reader = new FileReader();\n        reader.onload = e => {\n          this.imageUrl = {\n            CName: file.name,\n            CFile: e.target?.result?.toString().split(',')[1],\n            Cimg: file.name.includes('pdf') ? file : file,\n            CFileUpload: file,\n            CFileType: EnumFileType.PDF\n          };\n          if (this.fileInput) {\n            this.fileInput.nativeElement.value = null;\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    }\n  }\n  isCheckAllRowChecked(row) {\n    return row.every(item => item.CIsSelect);\n  }\n  isCheckAllColumnChecked(index) {\n    if (this.isHouseList) {\n      if (index < 0 || index >= this.houseList2D[0].length) {\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\n      }\n      for (const floorData of this.houseList2D) {\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\n          return false; // Found a customer with CIsEnable not true (or missing)\n        }\n      }\n      return true; // All customers at the given index have CIsEnable as true\n    }\n    return false;\n  }\n  enableAllAtIndex(checked, index) {\n    if (index < 0) {\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\n    }\n    for (const floorData of this.houseList2D) {\n      if (index < floorData.length) {\n        // Check if index is valid for this floor\n        floorData[index].CIsSelect = checked;\n      }\n    }\n  }\n  enableAllRow(checked, row) {\n    for (const item of row) {\n      item.CIsSelect = checked;\n    }\n  }\n  getItemByValue(value, options) {\n    for (const item of options) {\n      if (item.value === value) {\n        return item;\n      }\n    }\n    return null;\n  }\n  getReviewById(item, ref) {\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\n      body: item.CReviewId\n    }).subscribe(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        const data = res.Entries;\n        this.selectedReview = {\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\n          CReviewId: data.tblReview?.CReviewId,\n          CReviewType: data.tblReview?.CReviewType,\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\n          CSort: data.tblReview?.CSort,\n          CStatus: data.tblReview?.CStatus,\n          CFileUrl: data.tblReview?.CFileUrl,\n          // CIsSelectAll?: boolean;\n          // selectedExamineStatus?: any | null;\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\n          tblExamineLogs: data.tblExamineLogs,\n          reviewHouseHolds: data?.reviewHouseHolds?.filter(i => i.CIsSelect),\n          tblReview: data.tblReview\n        };\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\n          if (data?.tblExamineLogs.length === 0) return undefined;\n          this.latestAction = data?.tblExamineLogs[0].CAction;\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : '';\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\n            if (data.tblExamineLogs[i].CCreateDt) {\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt);\n              if (currentDate > latestDate) {\n                latestDate = currentDate;\n                this.latestAction = data?.tblExamineLogs[i].CAction;\n              }\n            }\n          }\n        }\n        this.getHouseList();\n        this.dialogService.open(ref);\n      }\n    });\n  }\n  flattenAndFilter(data) {\n    const flattened = [];\n    for (const floorData of data) {\n      for (const house of floorData) {\n        if (house.CIsSelect && house.CIsEnable) {\n          flattened.push({\n            CHouseID: house.CHouseID,\n            CIsSelect: house.CIsSelect,\n            CFloor: house.CFloor,\n            CHouseHold: house.CHouseHold\n          });\n        }\n      }\n    }\n    return flattened;\n  }\n  onSaveReview(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    this.saveReviewPostRes = {\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\n      CReviewId: this.selectedReview.CReviewId,\n      CReviewType: this.selectedReview.selectedReviewType.value,\n      CReviewName: this.selectedReview.CReviewName,\n      CSort: this.selectedReview?.CSort,\n      CStatus: this.selectedReview.seletedStatus.value,\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\n      CExamineNote: this.selectedReview.CExamineNote,\n      HouseReviews: this.flattenAndFilter(this.houseList2D)\n    };\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\n      if (res && res.body && res.body.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.clearImage();\n        this.getReviewList();\n        ref.close();\n      } else {\n        this.message.showErrorMSG(res && res.body && res.body.Message);\n      }\n    });\n  }\n  onSearch() {\n    this.getReviewList();\n  }\n  pageChanged(newPage) {\n    this.pageIndex = newPage;\n    this.getReviewList();\n  }\n  groupByFloor(customerData, isDefaut) {\n    const groupedData = [];\n    const uniqueFloors = Array.from(new Set(customerData.map(customer => customer.CFloor).filter(floor => floor !== null)));\n    for (const floor of uniqueFloors) {\n      groupedData.push([]);\n    }\n    for (const customer of customerData) {\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor);\n      if (floorIndex !== -1) {\n        groupedData[floorIndex].push({\n          CIsSelect: customer?.CIsSelect || false,\n          CHouseID: customer.CID,\n          CHouseType: customer.CHouseType,\n          CFloor: customer.CFloor,\n          CHouseHold: customer.CHouseHold,\n          CIsEnable: customer.CIsEnable\n        });\n      }\n    }\n    return groupedData;\n  }\n  addCIsSelectToA(A, B) {\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\n    return A.map(item => {\n      const key = `${item.CHouseHold}-${item.CFloor}`;\n      return {\n        ...item,\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\n      };\n    });\n  }\n  sortByFloorDescending(arr) {\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n  }\n  getHouseList() {\n    return this._houseService.apiHouseGetHouseListPost$Json({\n      body: {\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CIsPagi: false\n      }\n    }).pipe(tap(res => {\n      if (res && res.StatusCode === 0 && res.Entries) {\n        const rest = this.sortByFloorDescending(res.Entries);\n        this.houseListEnable = [...rest];\n        if (this.selectedReview.CReviewId) {\n          this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []));\n        } else {\n          this.houseList2D = this.groupByFloor([...rest]);\n        }\n        this.isHouseList = true;\n      }\n    })).subscribe();\n  }\n  openPdfInNewTab(CFileUrl) {\n    if (CFileUrl) {\n      this.utilityService.openFileNewTab(CFileUrl);\n    }\n  }\n  getReviewList() {\n    return this._reviewService.apiReviewGetReviewListPost$Json({\n      body: {\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize,\n        CReviewName: this.searchQuery.CReviewName,\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\n        CStatus: this.searchQuery.seletedStatus.value,\n        CReviewType: this.searchQuery.selectedReviewType.value,\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value\n      }\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.reviewList = res.Entries;\n        this.totalRecords = res.TotalItems;\n      }\n    })).subscribe();\n  }\n  onSelectionChangeBuildCase() {\n    if (this.searchQuery.selectedBuildCase.value) {\n      this.getReviewList();\n    }\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      if (res.Entries && res.StatusCode == 0) {\n        this.userBuildCaseOptions = res.Entries.map(res => {\n          return {\n            label: res.CBuildCaseName,\n            value: res.cID\n          };\n        });\n        this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0];\n        if (this.searchQuery.selectedBuildCase.value) {\n          this.getReviewList();\n        }\n      }\n    })).subscribe();\n  }\n  openModel(ref, item) {\n    this.latestAction = 0;\n    this.isHouseList = false;\n    this.isNew = true;\n    this.clearImage();\n    this.selectedReview = {\n      selectedReviewType: this.reviewTypeOptions[0],\n      seletedStatus: this.statusOptions[0],\n      selectedExamineStatus: this.examineStatusOptions[0],\n      CReviewName: '',\n      CSort: 0,\n      CFileUrl: '',\n      CExamineNote: '',\n      CIsSelectAll: false\n    };\n    if (item) {\n      this.isNew = false;\n      this.getReviewById(item, ref);\n    } else {\n      this.isNew = true;\n      this.getHouseList();\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {}\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    if (this.isNew && !this.imageUrl) {\n      this.valid.addErrorMessage(`前台圖片`);\n    }\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote);\n  }\n  static {\n    this.ɵfac = function ReviewDocumentManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ReviewDocumentManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i5.ReviewService), i0.ɵɵdirectiveInject(i6.ReviewServiceCustom), i0.ɵɵdirectiveInject(i7.UtilityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReviewDocumentManagementComponent,\n      selectors: [[\"ngx-review-document-management\"]],\n      viewQuery: function ReviewDocumentManagementComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 65,\n      vars: 15,\n      consts: [[\"dialog\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cReviewType\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cReviewName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"cReviewName\", \"nbInput\", \"\", 1, \"w-full\", \"!max-w-[290px]\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-end\", \"justify-end\", \"w-full\"], [\"class\", \"btn btn-secondary btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-2\"], [\"scope\", \"col\", 1, \"col-2\", \"text-center\"], [\"scope\", \"col\", 1, \"col-1\", \"text-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"text-center\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\"], [1, \"px-4\"], [\"for\", \"ReviewType\", 1, \"required-field\", \"label\", \"col-3\"], [\"placeholder\", \"\\u985E\\u578B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"CReviewName\", 1, \"label\", \"col-3\", \"required-field\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u540D\\u7A31\", 1, \"col-9\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"file\", 1, \"label\", \"col-3\", \"required-field\"], [1, \"flex\", \"flex-col\", \"col-9\", \"px-0\", \"items-start\"], [\"type\", \"file\", \"id\", \"fileInput\", \"accept\", \"image/jpeg, image/jpg, application/pdf\", 1, \"hidden\", 2, \"display\", \"none\", 3, \"change\", \"disabled\"], [\"for\", \"fileInput\", 1, \"bg-blue-500\", \"hover:bg-blue-700\", \"text-white\", \"font-bold\", \"py-2\", \"px-4\", \"rounded\"], [1, \"fa-solid\", \"fa-cloud-arrow-up\", \"mr-2\"], [\"class\", \"flex items-center space-x-2\", 4, \"ngIf\"], [\"for\", \"CExamineStatus\", 1, \"label\", \"col-3\", \"required-field\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", \"px-0\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"cExamineNote\", 1, \"label\", \"col-3\", \"required-field\"], [\"nbInput\", \"\", 1, \"resize-none\", \"w-full\", \"col-9\", \"!max-w-[320px]\", 3, \"ngModelChange\", \"ngModel\", \"rows\", \"disabled\"], [1, \"form-group\", \"d-flex\", \"mb-0\"], [\"for\", \"houseList2D\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-3\", 2, \"min-width\", \"75px\"], [\"class\", \"table-responsive mt-1\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"mr-4\", \"min-w-[90px]\", 3, \"click\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"min-w-[90px]\", 3, \"click\", \"disabled\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-gray-600\"], [\"type\", \"button\", 1, \"text-red-500\", \"hover:text-red-700\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"text-sm\", 3, \"ngClass\"], [1, \"cursor-pointer\", \"text-blue-500\", 3, \"click\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"table-bordered\", 2, \"background-color\", \"#f3f3f3\"], [4, \"ngIf\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [1, \"font-medium\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\", \"disabled\"], [1, \"font-bold\", 3, \"ngClass\"], [1, \"w-full\"], [\"for\", \"status\", \"baseLabel\", \"\", 1, \"mr-3\", 2, \"min-width\", \"75px\"]],\n      template: function ReviewDocumentManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 1)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 2);\n          i0.ɵɵtext(5, \"\\u53EF\\u4E0A\\u50B3\\u8981\\u63D0\\u4F9B\\u5BA2\\u6236\\u5BE9\\u95B1\\u7684\\u6587\\u4EF6\\uFF0C\\u6587\\u4EF6\\u5167\\u578B\\u5206\\u70BA\\u6A19\\u6E96\\u5716\\u53CA\\u8A2D\\u5099\\uFF0C\\u4E26\\u8A2D\\u5B9A\\u8A72\\u6A94\\u6848\\u9069\\u7528\\u7684\\u6236\\u5225\\u3002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"div\", 4)(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nb-select\", 7);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedBuildCase, $event) || (ctx.searchQuery.selectedBuildCase = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function ReviewDocumentManagementComponent_Template_nb_select_selectedChange_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n          });\n          i0.ɵɵtemplate(12, ReviewDocumentManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 4)(14, \"div\", 5)(15, \"label\", 9);\n          i0.ɵɵtext(16, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"nb-select\", 10);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedReviewType, $event) || (ctx.searchQuery.selectedReviewType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(18, ReviewDocumentManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 4)(20, \"div\", 5)(21, \"label\", 11);\n          i0.ɵɵtext(22, \" \\u540D\\u7A31 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CReviewName, $event) || (ctx.searchQuery.CReviewName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 14);\n          i0.ɵɵtext(28, \" \\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"nb-select\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.seletedStatus, $event) || (ctx.searchQuery.seletedStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(30, ReviewDocumentManagementComponent_nb_option_30_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 4)(32, \"div\", 5)(33, \"label\", 16);\n          i0.ɵɵtext(34, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"nb-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.selectedExamineStatus, $event) || (ctx.searchQuery.selectedExamineStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵtemplate(36, ReviewDocumentManagementComponent_nb_option_36_Template, 2, 2, \"nb-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 4)(38, \"div\", 18);\n          i0.ɵɵtemplate(39, ReviewDocumentManagementComponent_button_39_Template, 3, 0, \"button\", 19)(40, ReviewDocumentManagementComponent_button_40_Template, 3, 0, \"button\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 21)(42, \"table\", 22)(43, \"thead\")(44, \"tr\", 23)(45, \"th\", 24);\n          i0.ɵɵtext(46, \"\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\", 24);\n          i0.ɵɵtext(48, \"\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\", 25);\n          i0.ɵɵtext(50, \"\\u9069\\u7528\\u6236\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\", 24);\n          i0.ɵɵtext(52, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"th\", 24);\n          i0.ɵɵtext(54, \"\\u5BE9\\u6838\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"th\", 26);\n          i0.ɵɵtext(56, \"\\u5BE9\\u6838\\u65E5\\u671F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\", 27);\n          i0.ɵɵtext(58, \"\\u52D5\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"tbody\");\n          i0.ɵɵtemplate(60, ReviewDocumentManagementComponent_tr_60_Template, 19, 18, \"tr\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(61, \"nb-card-footer\", 29)(62, \"ngb-pagination\", 30);\n          i0.ɵɵtwoWayListener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"pageChange\", function ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(63, ReviewDocumentManagementComponent_ng_template_63_Template, 46, 23, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedReviewType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.reviewTypeOptionsQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CReviewName);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.seletedStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptionsQuery);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.selectedExamineStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.examineStatusOptionsQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.reviewList);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i8.NgClass, i8.NgForOf, i8.NgIf, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i10.NgbPagination, i11.BreadcrumbComponent, i12.BaseLabelDirective, i13.DateFormatPipe, NbDatepickerModule, NbDateFnsDateModule, LabelInOptionsPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29uc3RydWN0aW9uLXByb2plY3QtbWFuYWdlbWVudC9ub3RpY2UtbWFuYWdlbWVudC9yZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC9yZXZpZXctZG9jdW1lbnQtbWFuYWdlbWVudC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNExBQTRMIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SharedModule", "CommonModule", "NbDatepickerModule", "LabelInOptionsPipe", "BaseComponent", "tap", "NbDateFnsDateModule", "moment", "EnumFileType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "case_r3", "case_r4", "case_r5", "ɵɵlistener", "ReviewDocumentManagementComponent_button_39_Template_button_click_0_listener", "ɵɵrestoreView", "_r6", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "onSearch", "ɵɵelement", "ReviewDocumentManagementComponent_button_40_Template_button_click_0_listener", "_r8", "dialog_r9", "ɵɵreference", "openModel", "ReviewDocumentManagementComponent_tr_60_button_18_Template_button_click_0_listener", "_r10", "item_r11", "$implicit", "ɵɵtemplate", "ReviewDocumentManagementComponent_tr_60_button_18_Template", "ɵɵpipeBind2", "CReviewType", "reviewTypeOptions", "CReviewName", "CHouse", "CStatus", "statusOptions", "CExamineStatus", "examineStatusOptionsQuery", "CActionDate", "ɵɵpipeBind1", "isUpdate", "case_r13", "ReviewDocumentManagementComponent_ng_template_63_div_23_Template_button_click_3_listener", "_r14", "clearImage", "ɵɵtextInterpolate", "fileName", "ReviewDocumentManagementComponent_ng_template_63_div_24_Template_a_click_2_listener", "_r15", "openPdfInNewTab", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CFileUrl", "imageUrl", "case_r16", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_th_2_Template_nb_checkbox_checkedChange_1_listener", "$event", "idx_r18", "_r17", "index", "enableAllAtIndex", "isCheckAllColumnChecked", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_th_2_Template", "houseList2D", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_td_5_Template_nb_checkbox_checkedChange_2_listener", "house_r22", "_r21", "ɵɵtwoWayBindingSet", "CIsSelect", "ɵɵtwoWayProperty", "CHouseHold", "CIsEnable", "latestAction", "ɵɵpureFunction1", "_c1", "CID", "ɵɵtextInterpolate2", "CFloor", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_Template_nb_checkbox_checkedChange_2_listener", "row_r20", "_r19", "enableAllRow", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_td_5_Template", "isCheckAllRowChecked", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_3_Template", "ReviewDocumentManagementComponent_ng_template_63_div_39_tr_5_Template", "length", "row_r24", "CCreateDt", "CCreator", "CAction", "CExamineNote", "ReviewDocumentManagementComponent_ng_template_63_div_45_tr_16_Template", "tblExamineLogs", "ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_7_listener", "_r12", "selectedReviewType", "ReviewDocumentManagementComponent_ng_template_63_nb_option_8_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_input_ngModelChange_13_listener", "ReviewDocumentManagementComponent_ng_template_63_Template_input_change_19_listener", "onFileSelected", "ReviewDocumentManagementComponent_ng_template_63_div_23_Template", "ReviewDocumentManagementComponent_ng_template_63_div_24_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_nb_select_ngModelChange_29_listener", "se<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReviewDocumentManagementComponent_ng_template_63_nb_option_30_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_textarea_ngModelChange_35_listener", "ReviewDocumentManagementComponent_ng_template_63_div_39_Template", "ReviewDocumentManagementComponent_ng_template_63_Template_button_click_41_listener", "ref_r23", "dialogRef", "onClose", "ReviewDocumentManagementComponent_ng_template_63_Template_button_click_43_listener", "onSaveReview", "ReviewDocumentManagementComponent_ng_template_63_div_45_Template", "isNew", "ɵɵclassProp", "isHouseList", "ReviewDocumentManagementComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "_reviewService", "reviewService", "utilityService", "pageFirst", "pageSize", "pageIndex", "totalRecords", "value", "reviewTypeOptionsQuery", "examineStatusOptions", "statusOptionsQuery", "buildCaseOptions", "undefined", "ngOnInit", "searchQuery", "selectedBuildCase", "selectedExamineStatus", "getUserBuildCase", "fileInput", "nativeElement", "event", "file", "target", "files", "fileRegex", "test", "type", "showErrorMSG", "allowedTypes", "includes", "name", "reader", "FileReader", "onload", "e", "CName", "CFile", "result", "toString", "split", "Cimg", "CFileUpload", "CFileType", "PDF", "readAsDataURL", "row", "every", "item", "Error", "floorData", "checked", "getItemByValue", "options", "getReviewById", "ref", "apiReviewGetReviewByIdPost$Json", "body", "CReviewId", "subscribe", "res", "Entries", "StatusCode", "data", "CBuildCaseId", "tblReview", "CSort", "reviewHouseHolds", "filter", "i", "latestDate", "Date", "currentDate", "getHouseList", "open", "flattenAndFilter", "flattened", "house", "push", "CHouseID", "validation", "errorMessages", "showErrorMSGs", "saveReviewPostRes", "HouseReviews", "SaveReview", "showSucessMSG", "getReviewList", "close", "Message", "pageChanged", "newPage", "groupByFloor", "customerData", "isDefaut", "groupedData", "uniqueFloors", "Array", "from", "Set", "map", "customer", "floor", "floorIndex", "indexOf", "CHouseType", "addCIsSelectToA", "A", "B", "mapB", "Map", "key", "has", "get", "sortByFloorDescending", "arr", "sort", "a", "b", "apiHouseGetHouseListPost$Json", "CBuildCaseID", "CIsPagi", "pipe", "rest", "houseListEnable", "openFileNewTab", "apiReviewGetReviewListPost$Json", "PageIndex", "PageSize", "reviewList", "TotalItems", "onSelectionChangeBuildCase", "apiBuildCaseGetUserBuildCasePost$Json", "userBuildCaseOptions", "CBuildCaseName", "cID", "CIsSelectAll", "formatDate", "CChangeDate", "format", "onSubmit", "clear", "addErrorMessage", "required", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "ReviewService", "i6", "ReviewServiceCustom", "i7", "UtilityService", "selectors", "viewQuery", "ReviewDocumentManagementComponent_Query", "rf", "ctx", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_11_listener", "_r1", "ReviewDocumentManagementComponent_Template_nb_select_selected<PERSON><PERSON>e_11_listener", "ReviewDocumentManagementComponent_nb_option_12_Template", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_17_listener", "ReviewDocumentManagementComponent_nb_option_18_Template", "ReviewDocumentManagementComponent_Template_input_ngModelChange_24_listener", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_29_listener", "ReviewDocumentManagementComponent_nb_option_30_Template", "ReviewDocumentManagementComponent_Template_nb_select_ngModelChange_35_listener", "ReviewDocumentManagementComponent_nb_option_36_Template", "ReviewDocumentManagementComponent_button_39_Template", "ReviewDocumentManagementComponent_button_40_Template", "ReviewDocumentManagementComponent_tr_60_Template", "ReviewDocumentManagementComponent_Template_ngb_pagination_pageChange_62_listener", "ReviewDocumentManagementComponent_ng_template_63_Template", "ɵɵtemplateRefExtractor", "isRead", "isCreate", "i8", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "i10", "NgbPagination", "i11", "BreadcrumbComponent", "i12", "BaseLabelDirective", "i13", "DateFormatPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.ts", "C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\pages\\construction-project-management\\notice-management\\review-document-management\\review-document-management.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { SharedModule } from '../../../components/shared.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NbDatepickerModule, NbDialogService } from '@nebular/theme';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { BuildCaseService, HouseService, ReviewService } from 'src/services/api/services';\r\nimport { LabelInOptionsPipe, TypeMailPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { BaseComponent } from '../../../components/base/baseComponent';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseListArgs, GetHouseListRes, GetReviewListRes, HouseReview, ReviewHouseHold, TblExamineLog, TblReview } from 'src/services/api/models';\r\nimport { tap } from 'rxjs';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport * as moment from 'moment';\r\nimport { EnumFileType } from 'src/app/shared/enum/enumFileType';\r\nimport { ReviewServiceCustom } from 'src/app/@core/service/review.service';\r\nimport { DateFormatPipe } from 'src/app/@theme/pipes';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\n\r\nexport interface selectItem {\r\n  label: string,\r\n  value: number | string,\r\n}\r\n\r\nexport interface HouseList {\r\n  CFloor?: number | null;\r\n  CHouseHold?: string | null;\r\n  CHouseID?: number;\r\n  CID?: number;\r\n  CIsSelect?: boolean | null;\r\n  CHouseType?: number | null;\r\n  CIsEnable?: boolean | null;\r\n}\r\n\r\nexport interface SaveReviewPostParam {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any\r\n}\r\n\r\nexport interface ReviewType {\r\n  CBuildCaseId?: number;\r\n  CReviewId?: number;\r\n  CReviewType?: number | null;\r\n  CReviewName?: string;\r\n  CSort?: number;\r\n  CStatus?: number;\r\n  CFile?: Blob;\r\n  CExamineNote?: string;\r\n  HouseReviews?: Array<HouseReview>;\r\n  CIsSelectAll?: boolean;\r\n  selectedCNoticeType?: any,\r\n  CFileUrl?: string | null;\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  reviewHouseHolds?: Array<ReviewHouseHold>;\r\n  tblExamineLogs?: Array<TblExamineLog> | null;\r\n  tblReview?: TblReview;\r\n}\r\nexport interface SearchQuery {\r\n  selectedBuildCase?: any | null;\r\n  selectedReviewType?: any | null;\r\n  selectedExamineStatus?: any | null;\r\n  seletedStatus?: any | null;\r\n  CReviewName?: any | null;\r\n}\r\n@Component({\r\n  selector: 'ngx-review-document-management',\r\n  templateUrl: './review-document-management.component.html',\r\n  styleUrls: ['./review-document-management.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, TypeMailPipe, NbDatepickerModule, NbDateFnsDateModule, DateFormatPipe, LabelInOptionsPipe],\r\n})\r\n\r\nexport class ReviewDocumentManagementComponent extends BaseComponent implements OnInit {\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private _reviewService: ReviewService,\r\n    private reviewService: ReviewServiceCustom,\r\n    private utilityService: UtilityService,\r\n  ) { super(_allow) }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  reviewTypeOptions = [{\r\n    value: 1,\r\n    label: '標準圖', //standard drawing\r\n  }, {\r\n    value: 2,\r\n    label: '設備圖', //equipment drawing\r\n  }\r\n  ]\r\n\r\n  reviewTypeOptionsQuery = [\r\n    {\r\n      value: -1,\r\n      label: '全部'\r\n    },\r\n    {\r\n      value: 1,\r\n      label: '標準圖', //standard drawing\r\n    }, {\r\n      value: 2,\r\n      label: '設備圖', //equipment drawing\r\n    }\r\n  ]\r\n\r\n  examineStatusOptions = [\r\n    {\r\n      value: -1,\r\n      label: '待審核' //Pending review\r\n\r\n    }, {\r\n      value: 1,\r\n      label: '已通過' //passed\r\n\r\n    },\r\n    {\r\n      value: 2,\r\n      label: '已駁回' //rejected\r\n\r\n    }\r\n  ]\r\n\r\n  examineStatusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  },\r\n  {\r\n    value: 0,\r\n    label: '待審核' //Pending review\r\n\r\n  }, {\r\n    value: 1,\r\n    label: '已通過' //passed\r\n\r\n  },\r\n  {\r\n    value: 2,\r\n    label: '已駁回' //rejected\r\n  }\r\n  ]\r\n\r\n  statusOptions = [{\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n  statusOptionsQuery = [{\r\n    value: -1,\r\n    label: '全部'\r\n  }, {\r\n    value: 1, //0停用 1啟用 9刪除\r\n    label: '啟用' //enable\r\n  }, {\r\n    value: 2,\r\n    label: '停用' //Disable\r\n  },]\r\n\r\n\r\n  searchQuery: SearchQuery\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      selectedBuildCase: null,\r\n      selectedReviewType: this.reviewTypeOptionsQuery[0],\r\n      selectedExamineStatus: this.examineStatusOptionsQuery[0],\r\n      seletedStatus: this.statusOptionsQuery[0],\r\n      CReviewName: ''\r\n\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  fileName: string | null = null;\r\n  imageUrl: any = undefined;\r\n\r\n  clearImage() {\r\n    if (this.imageUrl) {\r\n      this.imageUrl = null;\r\n      this.fileName = null;\r\n      if (this.fileInput) {\r\n        this.fileInput.nativeElement.value = null; // Xóa giá trị input file\r\n      }\r\n    }\r\n  }\r\n\r\n  onFileSelected(event: any) {\r\n    const file: File = event.target.files[0];\r\n    const fileRegex = /pdf|jpg|jpeg|png/i;\r\n    if (!fileRegex.test(file.type)) {\r\n      this.message.showErrorMSG('檔案格式錯誤，僅限pdf');\r\n      return;\r\n    }\r\n    if (file) {\r\n      const allowedTypes = ['application/pdf'];\r\n      if (allowedTypes.includes(file.type)) {\r\n        this.fileName = file.name;\r\n        const reader = new FileReader();\r\n        reader.onload = (e: any) => {\r\n          this.imageUrl = {\r\n            CName: file.name,\r\n            CFile: e.target?.result?.toString().split(',')[1],\r\n            Cimg: file.name.includes('pdf') ? file : file,\r\n            CFileUpload: file,\r\n            CFileType: EnumFileType.PDF,\r\n          };\r\n          if (this.fileInput) {\r\n            this.fileInput.nativeElement.value = null;\r\n          }\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    }\r\n  }\r\n\r\n  isHouseList = false\r\n\r\n  houseListDefault: any\r\n\r\n  isCheckAllRowChecked(row: any[]): boolean {\r\n    return row.every((item: { CIsSelect: any; }) => item.CIsSelect);\r\n  }\r\n\r\n  isCheckAllColumnChecked(index: number): boolean {\r\n    if (this.isHouseList) {\r\n      if (index < 0 || index >= this.houseList2D[0].length) {\r\n        throw new Error(\"Invalid index. Index must be within the bounds of the array.\");\r\n      }\r\n      for (const floorData of this.houseList2D) {\r\n        if (index >= floorData.length || !floorData[index].CIsSelect) {\r\n          return false; // Found a customer with CIsEnable not true (or missing)\r\n        }\r\n      }\r\n      return true; // All customers at the given index have CIsEnable as true\r\n    }\r\n    return false\r\n  }\r\n\r\n  enableAllAtIndex(checked: boolean, index: number): void {\r\n    if (index < 0) {\r\n      throw new Error(\"Invalid index. Index must be a non-negative number.\");\r\n    }\r\n    for (const floorData of this.houseList2D) {\r\n      if (index < floorData.length) { // Check if index is valid for this floor\r\n        floorData[index].CIsSelect = checked;\r\n      }\r\n    }\r\n  }\r\n\r\n  enableAllRow(checked: boolean, row: any[]) {\r\n    for (const item of row) {\r\n      item.CIsSelect = checked;\r\n    }\r\n  }\r\n  selectedReview: ReviewType\r\n\r\n  getItemByValue(value: any, options: any[]) {\r\n    for (const item of options) {\r\n      if (item.value === value) {\r\n        return item;\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  latestAction: any = 0\r\n\r\n  getReviewById(item: any, ref: any) {\r\n\r\n    this._reviewService.apiReviewGetReviewByIdPost$Json({\r\n      body: item.CReviewId\r\n    }).subscribe(res => {\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        const data = res.Entries\r\n        this.selectedReview = {\r\n          CBuildCaseId: data.tblReview?.CBuildCaseId,\r\n          CReviewId: data.tblReview?.CReviewId,\r\n          CReviewType: data.tblReview?.CReviewType,\r\n          CReviewName: data.tblReview?.CReviewName ? data.tblReview?.CReviewName : '',\r\n          CSort: data.tblReview?.CSort,\r\n          CStatus: data.tblReview?.CStatus,\r\n          CFileUrl: data.tblReview?.CFileUrl,\r\n          // CIsSelectAll?: boolean;\r\n          // selectedExamineStatus?: any | null;\r\n          CExamineNote: data?.CExamineNote ? data?.CExamineNote : '',\r\n          seletedStatus: data.tblReview?.CStatus ? this.getItemByValue(data.tblReview?.CStatus, this.statusOptions) : this.statusOptions[0],\r\n          selectedReviewType: data.tblReview?.CReviewType ? this.getItemByValue(data.tblReview?.CReviewType, this.reviewTypeOptions) : this.reviewTypeOptions[0],\r\n          tblExamineLogs: data.tblExamineLogs,\r\n          reviewHouseHolds: data?.reviewHouseHolds?.filter((i: any) => i.CIsSelect),\r\n          tblReview: data.tblReview\r\n        }\r\n\r\n        if (data && data?.tblExamineLogs && data?.tblExamineLogs.length) {\r\n          if (data?.tblExamineLogs.length === 0) return undefined;\r\n          this.latestAction = data?.tblExamineLogs[0].CAction;\r\n          let latestDate = data?.tblExamineLogs[0].CCreateDt ? new Date(data?.tblExamineLogs[0].CCreateDt) : ''\r\n          for (let i = 1; i < data.tblExamineLogs.length; i++) {\r\n            if (data.tblExamineLogs[i].CCreateDt) {\r\n              const currentDate = new Date(data.tblExamineLogs[i].CCreateDt!)\r\n              if (currentDate > latestDate) {\r\n                latestDate = currentDate;\r\n                this.latestAction = data?.tblExamineLogs[i].CAction;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.getHouseList()\r\n        this.dialogService.open(ref)\r\n      }\r\n    })\r\n  }\r\n\r\n  saveReviewPostRes: SaveReviewPostParam\r\n\r\n  flattenAndFilter(data: any[][]): HouseReview[] {\r\n    const flattened: HouseReview[] = [];\r\n    for (const floorData of data) {\r\n      for (const house of floorData) {\r\n        if (house.CIsSelect && house.CIsEnable) {\r\n          flattened.push({\r\n            CHouseID: house.CHouseID,\r\n            CIsSelect: house.CIsSelect,\r\n            CFloor: house.CFloor,\r\n            CHouseHold: house.CHouseHold,\r\n          });\r\n        }\r\n      }\r\n    }\r\n    return flattened;\r\n  }\r\n\r\n  houseList2D: HouseList[][]\r\n\r\n  onSaveReview(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    this.saveReviewPostRes = {\r\n      CBuildCaseId: this.searchQuery.selectedBuildCase.value,\r\n      CReviewId: this.selectedReview.CReviewId,\r\n      CReviewType: this.selectedReview.selectedReviewType.value,\r\n      CReviewName: this.selectedReview.CReviewName,\r\n      CSort: this.selectedReview?.CSort,\r\n      CStatus: this.selectedReview.seletedStatus.value,\r\n      CFile: this.imageUrl ? this.imageUrl.CFileUpload : undefined,\r\n      CExamineNote: this.selectedReview.CExamineNote,\r\n      HouseReviews: this.flattenAndFilter(this.houseList2D),\r\n    }\r\n    this.reviewService.SaveReview(this.saveReviewPostRes).subscribe(res => {\r\n      if (res && res.body! && res.body.StatusCode! === 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.clearImage()\r\n        this.getReviewList()\r\n        ref.close();\r\n      } else {\r\n        this.message.showErrorMSG(res && res.body && res.body.Message!);\r\n      }\r\n    })\r\n  }\r\n\r\n  onSearch() {\r\n    this.getReviewList()\r\n  }\r\n\r\n  pageChanged(newPage: number) {\r\n    this.pageIndex = newPage;\r\n    this.getReviewList()\r\n  }\r\n\r\n  bodyRequest: GetHouseListArgs\r\n\r\n  groupByFloor(customerData: HouseList[], isDefaut?: any): HouseList[][] {\r\n    const groupedData: HouseList[][] = [];\r\n    const uniqueFloors = Array.from(new Set(\r\n      customerData.map(customer => customer.CFloor).filter(floor => floor !== null) as number[]\r\n    ));\r\n    for (const floor of uniqueFloors) {\r\n      groupedData.push([]);\r\n    }\r\n    for (const customer of customerData) {\r\n      const floorIndex = uniqueFloors.indexOf(customer.CFloor as number);\r\n      if (floorIndex !== -1) {\r\n        groupedData[floorIndex].push({\r\n          CIsSelect: customer?.CIsSelect || false,\r\n          CHouseID: customer.CID,\r\n          CHouseType: customer.CHouseType,\r\n          CFloor: customer.CFloor,\r\n          CHouseHold: customer.CHouseHold,\r\n          CIsEnable: customer.CIsEnable,\r\n        });\r\n      }\r\n    }\r\n    return groupedData;\r\n  }\r\n\r\n\r\n  houseListEnable: any[]\r\n\r\n\r\n  addCIsSelectToA(A: any[], B: any[]): any[] {\r\n    const mapB = new Map(B.map(item => [`${item.CHouseHold}-${item.CFloor}`, item.CIsSelect]));\r\n    return A.map(item => {\r\n      const key = `${item.CHouseHold}-${item.CFloor}`;\r\n      return {\r\n        ...item,\r\n        CIsSelect: mapB.has(key) ? mapB.get(key) : false\r\n      };\r\n    });\r\n  }\r\n\r\n  sortByFloorDescending(arr: GetHouseListRes[]): GetHouseListRes[] {\r\n    return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\r\n  }\r\n\r\n  getHouseList() {\r\n    return this._houseService.apiHouseGetHouseListPost$Json({\r\n      body: { CBuildCaseID: this.searchQuery.selectedBuildCase.value, CIsPagi: false }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res && res.StatusCode === 0 && res.Entries) {\r\n          const rest = this.sortByFloorDescending(res.Entries)\r\n          this.houseListEnable = [...rest]\r\n          if (this.selectedReview.CReviewId) {\r\n            this.houseList2D = this.groupByFloor(this.addCIsSelectToA([...this.houseListEnable], this.selectedReview.reviewHouseHolds ? [...this.selectedReview.reviewHouseHolds] : []))\r\n          } else {\r\n            this.houseList2D = this.groupByFloor([...rest])\r\n          }\r\n          this.isHouseList = true\r\n        }\r\n      }),\r\n\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  reviewList: GetReviewListRes[] | undefined\r\n\r\n  isNew = true\r\n\r\n  openPdfInNewTab(CFileUrl?: any) {\r\n    if (CFileUrl) {\r\n      this.utilityService.openFileNewTab(CFileUrl)\r\n    }\r\n  }\r\n\r\n  getReviewList() {\r\n    return this._reviewService.apiReviewGetReviewListPost$Json({\r\n      body: {\r\n        PageIndex: this.pageIndex,\r\n        PageSize: this.pageSize,\r\n        CReviewName: this.searchQuery.CReviewName,\r\n        CBuildCaseID: this.searchQuery.selectedBuildCase.value,\r\n        CStatus: this.searchQuery.seletedStatus.value,\r\n        CReviewType: this.searchQuery.selectedReviewType.value,\r\n        CExamineStatus: this.searchQuery.selectedExamineStatus.value,\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.reviewList = res.Entries\r\n          this.totalRecords = res.TotalItems!\r\n        }\r\n      })\r\n    ).subscribe()\r\n  }\r\n\r\n\r\n  onSelectionChangeBuildCase() {\r\n    if (this.searchQuery.selectedBuildCase.value) {\r\n      this.getReviewList()\r\n    }\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        if (res.Entries && res.StatusCode == 0) {\r\n          this.userBuildCaseOptions = res.Entries.map(res => {\r\n            return {\r\n              label: res.CBuildCaseName,\r\n              value: res.cID\r\n            }\r\n          })\r\n          this.searchQuery.selectedBuildCase = this.userBuildCaseOptions[0]\r\n          if (this.searchQuery.selectedBuildCase.value) {\r\n            this.getReviewList()\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe()\r\n  }\r\n\r\n  openModel(ref: any, item?: any) {\r\n    this.latestAction = 0\r\n    this.isHouseList = false\r\n    this.isNew = true\r\n    this.clearImage()\r\n    this.selectedReview = {\r\n      selectedReviewType: this.reviewTypeOptions[0],\r\n      seletedStatus: this.statusOptions[0],\r\n      selectedExamineStatus: this.examineStatusOptions[0],\r\n      CReviewName: '',\r\n      CSort: 0,\r\n      CFileUrl: '',\r\n      CExamineNote: '',\r\n      CIsSelectAll: false\r\n    }\r\n    if (item) {\r\n      this.isNew = false\r\n      this.getReviewById(item, ref)\r\n    } else {\r\n      this.isNew = true\r\n      this.getHouseList()\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    if (this.isNew && !this.imageUrl) {\r\n      this.valid.addErrorMessage(`前台圖片`);\r\n    }\r\n    this.valid.required('[送審說明]', this.selectedReview.CExamineNote)\r\n  }\r\n\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">可上傳要提供客戶審閱的文件，文件內型分為標準圖及設備，並設定該檔案適用的戶別。</h1>\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n          <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.selectedBuildCase\" class=\"col-9\"\r\n            (selectedChange)=\"onSelectionChangeBuildCase()\">\r\n            <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cReviewType\" class=\"label col-3\">類型</label>\r\n          <nb-select placeholder=\"類型\" [(ngModel)]=\"searchQuery.selectedReviewType\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of reviewTypeOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cReviewName\" class=\"label col-3\"> 名稱 </label>\r\n          <div class=\"col-9\">\r\n            <input type=\"text\" id=\"cReviewName\" nbInput class=\"w-full !max-w-[290px]\"\r\n              [(ngModel)]=\"searchQuery.CReviewName\">\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cStatus\" class=\"label col-3\">\r\n            狀態\r\n          </label>\r\n          <nb-select placeholder=\"狀態\" [(ngModel)]=\"searchQuery.seletedStatus\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of statusOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"CExamineStatus\" class=\"label col-3\">\r\n            簽回狀態\r\n          </label>\r\n          <nb-select placeholder=\"簽回狀態\" [(ngModel)]=\"searchQuery.selectedExamineStatus\" class=\"col-9\">\r\n            <nb-option *ngFor=\"let case of examineStatusOptionsQuery\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-end justify-end w-full\">\r\n          <button class=\"btn btn-secondary btn-sm\" *ngIf=\"isRead\" (click)=\"onSearch()\">\r\n            查詢 <i class=\"fas fa-search\"></i>\r\n          </button>\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openModel(dialog)\">\r\n            新增 <i class=\"fas fa-plus\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table table-striped border \" style=\"background-color:#f3f3f3;\">\r\n        <thead>\r\n          <tr style=\"background-color: #27ae60; color: white;\">\r\n            <th scope=\"col\" class=\"col-1 \">類型</th>\r\n            <th scope=\"col\" class=\"col-1\">名稱</th>\r\n            <th scope=\"col\" class=\"col-2\">適用戶型</th>\r\n            <th scope=\"col\" class=\"col-1\">狀態</th>\r\n            <th scope=\"col\" class=\"col-1\">審核狀態</th>\r\n            <th scope=\"col\" class=\"col-2 text-center\">審核日期</th>\r\n            <th scope=\"col\" class=\"col-1 text-center\">動作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let item of reviewList ; let i = index\">\r\n            <td>\r\n              {{item.CReviewType | getLabelInOptions : reviewTypeOptions}}\r\n            </td>\r\n            <td>\r\n              {{ item.CReviewName}}\r\n            </td>\r\n            <td>\r\n              {{ item.CHouse }}\r\n            </td>\r\n            <td>\r\n              {{item.CStatus | getLabelInOptions : statusOptions}}\r\n            </td>\r\n            <td>\r\n              {{item.CExamineStatus | getLabelInOptions : examineStatusOptionsQuery}}\r\n            </td>\r\n            <td class=\"text-center\">\r\n              {{ item.CActionDate ? (item.CActionDate | dateFormat) : ''}}\r\n            </td>\r\n            <td class=\"text-center w-32 px-0\">\r\n              <button *ngIf=\"isUpdate\" (click)=\"openModel(dialog, item)\"\r\n                class=\"btn btn-outline-success btn-sm text-left m-[2px]\">\r\n                編輯\r\n              </button>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngb-pagination [(page)]=\"pageIndex\" [pageSize]=\"pageSize\" [collectionSize]=\"totalRecords\"\r\n      (pageChange)=\"pageChanged($event)\" aria-label=\"Pagination\">\r\n    </ngb-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:800px; max-height: 95vh\">\r\n    <nb-card-header> {{isNew? '新增審閱文件': '編輯審閱文件'}} </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center w-full\">\r\n        <label for=\"ReviewType\" class=\"required-field label col-3\">類型\r\n        </label>\r\n        <nb-select [disabled]=\"latestAction===1\" placeholder=\"類型\" [(ngModel)]=\"selectedReview.selectedReviewType\"\r\n          class=\"col-9 px-0\">\r\n          <nb-option *ngFor=\"let case of reviewTypeOptions\" [value]=\"case\">\r\n            {{ case.label }}\r\n          </nb-option>\r\n        </nb-select>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"CReviewName\" class=\"label col-3 required-field\">\r\n            名稱\r\n          </label>\r\n          <input [disabled]=\"latestAction===1\" type=\"text\" class=\"col-9\" nbInput placeholder=\"名稱\"\r\n            [(ngModel)]=\"selectedReview.CReviewName\" />\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"file\" class=\"label col-3 required-field\">\r\n            上傳檔案\r\n          </label>\r\n          <div class=\"flex flex-col col-9 px-0 items-start\">\r\n            <input type=\"file\" id=\"fileInput\" accept=\"image/jpeg, image/jpg, application/pdf\" class=\"hidden\"\r\n              style=\"display: none\" (change)=\"onFileSelected($event)\" [disabled]=\"latestAction===1\">\r\n            <label for=\"fileInput\"\r\n              [class.opacity-50]=\"latestAction === 1\"\r\n              [class.cursor-pointer]=\"latestAction !== 1\"\r\n              class=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">\r\n              <i class=\"fa-solid fa-cloud-arrow-up mr-2\"></i> 上傳\r\n            </label>\r\n            <div class=\"flex items-center space-x-2\" *ngIf=\"fileName\">\r\n              <span class=\"text-gray-600\">{{ fileName }}</span>\r\n              <button type=\"button\" (click)=\"clearImage()\" class=\"text-red-500 hover:text-red-700\">\r\n                <i class=\"fa-solid fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n            <div class=\"flex items-center space-x-2\" *ngIf=\"selectedReview.CFileUrl\">\r\n              <span class=\"text-sm\" [ngClass]=\"imageUrl ? 'hidden':''\">\r\n                <a (click)=\"openPdfInNewTab(selectedReview.CFileUrl)\" class=\"cursor-pointer text-blue-500\">\r\n                  {{selectedReview.CFileUrl}}\r\n                </a>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"CExamineStatus\" class=\"label col-3 required-field\">\r\n            狀態\r\n          </label>\r\n          <nb-select [disabled]=\"latestAction===1\" placeholder=\"狀態\" [(ngModel)]=\"selectedReview.seletedStatus\"\r\n            class=\"col-9 px-0\">\r\n            <nb-option *ngFor=\"let case of statusOptions\" [value]=\"case\">\r\n              {{ case.label }}\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"cExamineNote\" class=\"label col-3 required-field\">\r\n            審核說明\r\n          </label>\r\n          <textarea nbInput [(ngModel)]=\"selectedReview.CExamineNote\" [rows]=\"4\" [disabled]=\"latestAction===1\"\r\n            class=\"resize-none w-full col-9 !max-w-[320px]\"></textarea>\r\n        </div>\r\n      </div>\r\n      <div class=\"form-group d-flex mb-0\">\r\n        <label for=\"houseList2D\" baseLabel class=\"required-field mr-3\" style=\"min-width:75px\">適用戶別</label>\r\n      </div>\r\n      <div class=\"table-responsive mt-1\" *ngIf=\"isHouseList\">\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr *ngIf=\"houseList2D.length\">\r\n              <th></th>\r\n              <th *ngFor=\"let house of houseList2D[0]; let idx = index;\">\r\n                <nb-checkbox status=\"basic\" (checkedChange)=\"enableAllAtIndex($event, idx)\"\r\n                  [checked]=\"isCheckAllColumnChecked(idx)\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of houseList2D\">\r\n              <td>\r\n                <nb-checkbox status=\"basic\" [checked]=\"isCheckAllRowChecked(row)\"\r\n                  (checkedChange)=\"enableAllRow($event,row)\" [disabled]=\" latestAction===1\">\r\n                  <span class=\"font-medium\">全選</span>\r\n                </nb-checkbox>\r\n              </td>\r\n              <td *ngFor=\"let house of row\">\r\n                <ng-container>\r\n                  <nb-checkbox status=\"basic\" [(checked)]=\"house.CIsSelect\"\r\n                    [disabled]=\"!house.CHouseHold || !house.CIsEnable || latestAction===1\">\r\n                    <span class=\"font-bold\" [ngClass]=\"{ '!text-red': house.CID }\">{{ house.CHouseHold || 'null' }} - {{\r\n                      house.CFloor }} </span>\r\n                  </nb-checkbox>\r\n                  <!-- <span class=\"font-bold\" *ngIf=\"!house.CHouseHold || !house.CIsEnable\">{{ house.CHouseHold || 'null' }} - {{\r\n                    house.CFloor }}</span> -->\r\n                </ng-container>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div class=\"d-flex justify-content-center\">\r\n        <button class=\"btn btn-danger btn-sm mr-4 min-w-[90px]\" (click)=\"onClose(ref)\">\r\n          取消\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm min-w-[90px]\" (click)=\"onSaveReview(ref)\" [disabled]=\"latestAction===1\">\r\n          {{isNew? '新增並送出審核': '送出審核'}}\r\n        </button>\r\n      </div>\r\n      <div class=\"w-full\" *ngIf=\"!isNew\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"status\" baseLabel class=\"mr-3\" style=\"min-width:75px\">審核歷程</label>\r\n        </div>\r\n        <table class=\"table table-bordered\" style=\"background-color:#f3f3f3;\">\r\n          <thead>\r\n            <tr>\r\n              <th>時間</th>\r\n              <th>使用者</th>\r\n              <th>動作</th>\r\n              <th>說明</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let row of selectedReview.tblExamineLogs\">\r\n              <td>{{row.CCreateDt | dateFormat}}</td>\r\n              <td>{{row.CCreator}}</td>\r\n              <td>{{row.CAction}}</td>\r\n              <td>{{row.CExamineNote}}</td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </nb-card-body>\r\n  </nb-card>\r\n</ng-template>\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,mCAAmC;AAChE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAyB,gBAAgB;AAIpE,SAASC,kBAAkB,QAAsB,mCAAmC;AACpF,SAASC,aAAa,QAAQ,wCAAwC;AAGtE,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,kCAAkC;;;;;;;;;;;;;;;;;;;;;ICFnDC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,KAAA,MACF;;;;;IAQAR,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF2CH,EAAA,CAAAI,UAAA,UAAAK,OAAA,CAAc;IACnET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,OAAA,CAAAD,KAAA,MACF;;;;;IAoBAR,EAAA,CAAAC,cAAA,oBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFuCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAc;IAC/DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,OAAA,CAAAF,KAAA,MACF;;;;;IAWAR,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8CH,EAAA,CAAAI,UAAA,UAAAO,OAAA,CAAc;IACtEX,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,OAAA,CAAAH,KAAA,MACF;;;;;;IAOFR,EAAA,CAAAC,cAAA,iBAA6E;IAArBD,EAAA,CAAAY,UAAA,mBAAAC,6EAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC1EnB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAoB,SAAA,YAA6B;IAClCpB,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAAsF;IAA5BD,EAAA,CAAAY,UAAA,mBAAAS,6EAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAM,SAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,SAAA,CAAiB;IAAA,EAAC;IACnFvB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAoB,SAAA,YAA2B;IAChCpB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAsCLH,EAAA,CAAAC,cAAA,iBAC2D;IADlCD,EAAA,CAAAY,UAAA,mBAAAc,mFAAA;MAAA1B,EAAA,CAAAc,aAAA,CAAAa,IAAA;MAAA,MAAAC,QAAA,GAAA5B,EAAA,CAAAiB,aAAA,GAAAY,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,MAAAM,SAAA,GAAAvB,EAAA,CAAAwB,WAAA;MAAA,OAAAxB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAS,SAAA,CAAAF,SAAA,EAAAK,QAAA,CAAuB;IAAA,EAAC;IAExD5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAtBXH,EADF,CAAAC,cAAA,SAAoD,SAC9C;IACFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,MAAA,IACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAA8B,UAAA,KAAAC,0DAAA,qBAC2D;IAI/D/B,EADE,CAAAG,YAAA,EAAK,EACF;;;;;IAvBDH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,OAAAJ,QAAA,CAAAK,WAAA,EAAAjB,MAAA,CAAAkB,iBAAA,OACF;IAEElC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAO,WAAA,MACF;IAEEnC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAQ,MAAA,MACF;IAEEpC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,SAAAJ,QAAA,CAAAS,OAAA,EAAArB,MAAA,CAAAsB,aAAA,OACF;IAEEtC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgC,WAAA,SAAAJ,QAAA,CAAAW,cAAA,EAAAvB,MAAA,CAAAwB,yBAAA,OACF;IAEExC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAAa,WAAA,GAAAzC,EAAA,CAAA0C,WAAA,SAAAd,QAAA,CAAAa,WAAA,YACF;IAEWzC,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAA2B,QAAA,CAAc;;;;;IA2B3B3C,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAwC,QAAA,CAAc;IAC9D5C,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqC,QAAA,CAAApC,KAAA,MACF;;;;;;IA6BIR,EADF,CAAAC,cAAA,cAA0D,eAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,iBAAqF;IAA/DD,EAAA,CAAAY,UAAA,mBAAAiC,yFAAA;MAAA7C,EAAA,CAAAc,aAAA,CAAAgC,IAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA+B,UAAA,EAAY;IAAA,EAAC;IAC1C/C,EAAA,CAAAoB,SAAA,YAAiC;IAErCpB,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAJwBH,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAgD,iBAAA,CAAAhC,MAAA,CAAAiC,QAAA,CAAc;;;;;;IAOxCjD,EAFJ,CAAAC,cAAA,cAAyE,eACd,YACoC;IAAxFD,EAAA,CAAAY,UAAA,mBAAAsC,oFAAA;MAAAlD,EAAA,CAAAc,aAAA,CAAAqC,IAAA;MAAA,MAAAnC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAoC,eAAA,CAAApC,MAAA,CAAAqC,cAAA,CAAAC,QAAA,CAAwC;IAAA,EAAC;IACnDtD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACC,EACH;;;;IALkBH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAuC,QAAA,iBAAkC;IAEpDvD,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAqC,cAAA,CAAAC,QAAA,MACF;;;;;IAaJtD,EAAA,CAAAC,cAAA,oBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAoD,QAAA,CAAc;IAC1DxD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAiD,QAAA,CAAAhD,KAAA,MACF;;;;;;IAuBIR,EADF,CAAAC,cAAA,SAA2D,sBAEd;IADfD,EAAA,CAAAY,UAAA,2BAAA6C,gHAAAC,MAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAc,aAAA,CAAA8C,IAAA,EAAAC,KAAA;MAAA,MAAA7C,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAA8C,gBAAA,CAAAJ,MAAA,EAAAC,OAAA,CAA6B;IAAA,EAAC;IAEzE3D,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;;;;;IAHDH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAA+C,uBAAA,CAAAJ,OAAA,EAAwC;;;;;IAJ9C3D,EAAA,CAAAC,cAAA,SAA+B;IAC7BD,EAAA,CAAAoB,SAAA,SAAS;IACTpB,EAAA,CAAA8B,UAAA,IAAAkC,0EAAA,iBAA2D;IAM7DhE,EAAA,CAAAG,YAAA,EAAK;;;;IANmBH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAiD,WAAA,IAAmB;;;;;;IAgBzCjE,EAAA,CAAAC,cAAA,SAA8B;IAC5BD,EAAA,CAAAkE,uBAAA,GAAc;IACZlE,EAAA,CAAAC,cAAA,sBACyE;IAD7CD,EAAA,CAAAmE,gBAAA,2BAAAC,gHAAAV,MAAA;MAAA,MAAAW,SAAA,GAAArE,EAAA,CAAAc,aAAA,CAAAwD,IAAA,EAAAzC,SAAA;MAAA7B,EAAA,CAAAuE,kBAAA,CAAAF,SAAA,CAAAG,SAAA,EAAAd,MAAA,MAAAW,SAAA,CAAAG,SAAA,GAAAd,MAAA;MAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;IAAA,EAA6B;IAEvD1D,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAC7C;IACpBF,EADoB,CAAAG,YAAA,EAAO,EACb;;IAIlBH,EAAA,CAAAG,YAAA,EAAK;;;;;IAR2BH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAyE,gBAAA,YAAAJ,SAAA,CAAAG,SAAA,CAA6B;IACvDxE,EAAA,CAAAI,UAAA,cAAAiE,SAAA,CAAAK,UAAA,KAAAL,SAAA,CAAAM,SAAA,IAAA3D,MAAA,CAAA4D,YAAA,OAAsE;IAC9C5E,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA6E,eAAA,IAAAC,GAAA,EAAAT,SAAA,CAAAU,GAAA,EAAsC;IAAC/E,EAAA,CAAAM,SAAA,EAC7C;IAD6CN,EAAA,CAAAgF,kBAAA,KAAAX,SAAA,CAAAK,UAAA,mBAAAL,SAAA,CAAAY,MAAA,MAC7C;;;;;;IAVtBjF,EAFJ,CAAAC,cAAA,SAAoC,SAC9B,sBAE0E;IAA1ED,EAAA,CAAAY,UAAA,2BAAAsE,2GAAAxB,MAAA;MAAA,MAAAyB,OAAA,GAAAnF,EAAA,CAAAc,aAAA,CAAAsE,IAAA,EAAAvD,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAiBF,MAAA,CAAAqE,YAAA,CAAA3B,MAAA,EAAAyB,OAAA,CAAwB;IAAA,EAAC;IAC1CnF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAEhCF,EAFgC,CAAAG,YAAA,EAAO,EACvB,EACX;IACLH,EAAA,CAAA8B,UAAA,IAAAwD,0EAAA,iBAA8B;IAWhCtF,EAAA,CAAAG,YAAA,EAAK;;;;;IAhB2BH,EAAA,CAAAM,SAAA,GAAqC;IACpBN,EADjB,CAAAI,UAAA,YAAAY,MAAA,CAAAuE,oBAAA,CAAAJ,OAAA,EAAqC,aAAAnE,MAAA,CAAA4D,YAAA,OACU;IAIvD5E,EAAA,CAAAM,SAAA,GAAM;IAANN,EAAA,CAAAI,UAAA,YAAA+E,OAAA,CAAM;;;;;IAnBhCnF,EAFJ,CAAAC,cAAA,cAAuD,gBACiB,YAC7D;IACLD,EAAA,CAAA8B,UAAA,IAAA0D,qEAAA,iBAA+B;IASjCxF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,YAAO;IACLD,EAAA,CAAA8B,UAAA,IAAA2D,qEAAA,iBAAoC;IAqB1CzF,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAhCKH,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAiD,WAAA,CAAAyB,MAAA,CAAwB;IAWT1F,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAiD,WAAA,CAAc;;;;;IA6ChCjE,EADF,CAAAC,cAAA,SAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAC1BF,EAD0B,CAAAG,YAAA,EAAK,EAC1B;;;;IAJCH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAgD,iBAAA,CAAAhD,EAAA,CAAA0C,WAAA,OAAAiD,OAAA,CAAAC,SAAA,EAA8B;IAC9B5F,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAgD,iBAAA,CAAA2C,OAAA,CAAAE,QAAA,CAAgB;IAChB7F,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAgD,iBAAA,CAAA2C,OAAA,CAAAG,OAAA,CAAe;IACf9F,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAgD,iBAAA,CAAA2C,OAAA,CAAAI,YAAA,CAAoB;;;;;IAhB5B/F,EAFJ,CAAAC,cAAA,cAAmC,cACiB,gBACkB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IACxEF,EADwE,CAAAG,YAAA,EAAQ,EAC1E;IAIAH,EAHN,CAAAC,cAAA,gBAAsE,YAC7D,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAEVF,EAFU,CAAAG,YAAA,EAAK,EACR,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA8B,UAAA,KAAAkE,sEAAA,kBAAsD;IAQ5DhG,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IARoBH,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAqC,cAAA,CAAA4C,cAAA,CAAgC;;;;;;IAzI5DjG,EADF,CAAAC,cAAA,kBAA+C,qBAC7B;IAACD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAG5DH,EAFJ,CAAAC,cAAA,uBAA2B,aACgC,gBACI;IAAAD,EAAA,CAAAE,MAAA,oBAC3D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBACqB;IADqCD,EAAA,CAAAmE,gBAAA,2BAAA+B,6FAAAxC,MAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuE,kBAAA,CAAAvD,MAAA,CAAAqC,cAAA,CAAA+C,kBAAA,EAAA1C,MAAA,MAAA1C,MAAA,CAAAqC,cAAA,CAAA+C,kBAAA,GAAA1C,MAAA;MAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;IAAA,EAA+C;IAEvG1D,EAAA,CAAA8B,UAAA,IAAAuE,qEAAA,uBAAiE;IAIrErG,EADE,CAAAG,YAAA,EAAY,EACR;IAGFH,EAFJ,CAAAC,cAAA,cAAkD,cACS,iBACK;IAC1DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,iBAC6C;IAA3CD,EAAA,CAAAmE,gBAAA,2BAAAmC,0FAAA5C,MAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuE,kBAAA,CAAAvD,MAAA,CAAAqC,cAAA,CAAAlB,WAAA,EAAAuB,MAAA,MAAA1C,MAAA,CAAAqC,cAAA,CAAAlB,WAAA,GAAAuB,MAAA;MAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;IAAA,EAAwC;IAE9C1D,EAHI,CAAAG,YAAA,EAC6C,EACzC,EACF;IAKFH,EAHJ,CAAAC,cAAA,eAAkD,cAES,iBACF;IACnDD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAENH,EADF,CAAAC,cAAA,eAAkD,iBAEwC;IAAhED,EAAA,CAAAY,UAAA,oBAAA2F,mFAAA7C,MAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAUF,MAAA,CAAAwF,cAAA,CAAA9C,MAAA,CAAsB;IAAA,EAAC;IADzD1D,EAAA,CAAAG,YAAA,EACwF;IACxFH,EAAA,CAAAC,cAAA,iBAG+E;IAC7ED,EAAA,CAAAoB,SAAA,aAA+C;IAACpB,EAAA,CAAAE,MAAA,sBAClD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAORH,EANA,CAAA8B,UAAA,KAAA2E,gEAAA,kBAA0D,KAAAC,gEAAA,kBAMe;IAS/E1G,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAkD,cACS,iBACQ;IAC7DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBACqB;IADqCD,EAAA,CAAAmE,gBAAA,2BAAAwC,8FAAAjD,MAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuE,kBAAA,CAAAvD,MAAA,CAAAqC,cAAA,CAAAuD,aAAA,EAAAlD,MAAA,MAAA1C,MAAA,CAAAqC,cAAA,CAAAuD,aAAA,GAAAlD,MAAA;MAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;IAAA,EAA0C;IAElG1D,EAAA,CAAA8B,UAAA,KAAA+E,sEAAA,uBAA6D;IAKnE7G,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAkD,cACS,iBACM;IAC3DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,oBACkD;IADhCD,EAAA,CAAAmE,gBAAA,2BAAA2C,6FAAApD,MAAA;MAAA1D,EAAA,CAAAc,aAAA,CAAAqF,IAAA;MAAA,MAAAnF,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAAjB,EAAA,CAAAuE,kBAAA,CAAAvD,MAAA,CAAAqC,cAAA,CAAA0C,YAAA,EAAArC,MAAA,MAAA1C,MAAA,CAAAqC,cAAA,CAAA0C,YAAA,GAAArC,MAAA;MAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;IAAA,EAAyC;IAG/D1D,EAFsD,CAAAG,YAAA,EAAW,EACzD,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoC,iBACoD;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAC5FF,EAD4F,CAAAG,YAAA,EAAQ,EAC9F;IACNH,EAAA,CAAA8B,UAAA,KAAAiF,gEAAA,kBAAuD;IAqCrD/G,EADF,CAAAC,cAAA,eAA2C,kBACsC;IAAvBD,EAAA,CAAAY,UAAA,mBAAAoG,mFAAA;MAAA,MAAAC,OAAA,GAAAjH,EAAA,CAAAc,aAAA,CAAAqF,IAAA,EAAAe,SAAA;MAAA,MAAAlG,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAmG,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAC5EjH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8G;IAA1DD,EAAA,CAAAY,UAAA,mBAAAwG,mFAAA;MAAA,MAAAH,OAAA,GAAAjH,EAAA,CAAAc,aAAA,CAAAqF,IAAA,EAAAe,SAAA;MAAA,MAAAlG,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAqG,YAAA,CAAAJ,OAAA,CAAiB;IAAA,EAAC;IAC7EjH,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAA8B,UAAA,KAAAwF,gEAAA,mBAAmC;IAwBvCtH,EADE,CAAAG,YAAA,EAAe,EACP;;;;IAnJSH,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAuG,KAAA,wFAA8B;IAKhCvH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAA4D,YAAA,OAA6B;IAAkB5E,EAAA,CAAAyE,gBAAA,YAAAzD,MAAA,CAAAqC,cAAA,CAAA+C,kBAAA,CAA+C;IAE3EpG,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAkB,iBAAA,CAAoB;IAUzClC,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAA4D,YAAA,OAA6B;IAClC5E,EAAA,CAAAyE,gBAAA,YAAAzD,MAAA,CAAAqC,cAAA,CAAAlB,WAAA,CAAwC;IAYkBnC,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAA4D,YAAA,OAA6B;IAErF5E,EAAA,CAAAM,SAAA,EAAuC;IACvCN,EADA,CAAAwH,WAAA,eAAAxG,MAAA,CAAA4D,YAAA,OAAuC,mBAAA5D,MAAA,CAAA4D,YAAA,OACI;IAIH5E,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAiC,QAAA,CAAc;IAMdjD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAqC,cAAA,CAAAC,QAAA,CAA6B;IAe9DtD,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAA4D,YAAA,OAA6B;IAAkB5E,EAAA,CAAAyE,gBAAA,YAAAzD,MAAA,CAAAqC,cAAA,CAAAuD,aAAA,CAA0C;IAEtE5G,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAI,UAAA,YAAAY,MAAA,CAAAsB,aAAA,CAAgB;IAY5BtC,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyE,gBAAA,YAAAzD,MAAA,CAAAqC,cAAA,CAAA0C,YAAA,CAAyC;IAAY/F,EAAX,CAAAI,UAAA,WAAU,aAAAY,MAAA,CAAA4D,YAAA,OAA8B;IAOpE5E,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAI,UAAA,SAAAY,MAAA,CAAAyG,WAAA,CAAiB;IAwC6BzH,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,aAAAY,MAAA,CAAA4D,YAAA,OAA6B;IAC3G5E,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAuG,KAAA,kFACF;IAEmBvH,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,UAAAY,MAAA,CAAAuG,KAAA,CAAY;;;ADxKvC,OAAM,MAAOG,iCAAkC,SAAQ/H,aAAa;EAClEgI,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,cAA6B,EAC7BC,aAAkC,EAClCC,cAA8B;IACpC,KAAK,CAACR,MAAM,CAAC;IATP,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAGf,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB,KAAAtG,iBAAiB,GAAG,CAAC;MACnBuG,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAE;KACf,CACA;IAED,KAAAkI,sBAAsB,GAAG,CACvB;MACED,KAAK,EAAE,CAAC,CAAC;MACTjI,KAAK,EAAE;KACR,EACD;MACEiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAE;KACf,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAE;KACf,CACF;IAED,KAAAmI,oBAAoB,GAAG,CACrB;MACEF,KAAK,EAAE,CAAC,CAAC;MACTjI,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACEiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAC;KAEd,CACF;IAED,KAAAgC,yBAAyB,GAAG,CAAC;MAC3BiG,KAAK,EAAE,CAAC,CAAC;MACTjI,KAAK,EAAE;KACR,EACD;MACEiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAC;KAEd,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAC;KAEd,EACD;MACEiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,KAAK,CAAC;KACd,CACA;IAED,KAAA8B,aAAa,GAAG,CAAC;MACfmG,KAAK,EAAE,CAAC;MAAE;MACVjI,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAEH,KAAAoI,kBAAkB,GAAG,CAAC;MACpBH,KAAK,EAAE,CAAC,CAAC;MACTjI,KAAK,EAAE;KACR,EAAE;MACDiI,KAAK,EAAE,CAAC;MAAE;MACVjI,KAAK,EAAE,IAAI,CAAC;KACb,EAAE;MACDiI,KAAK,EAAE,CAAC;MACRjI,KAAK,EAAE,IAAI,CAAC;KACb,CAAE;IAKH,KAAAqI,gBAAgB,GAAU,CAAC;MAAErI,KAAK,EAAE,IAAI;MAAEiI,KAAK,EAAE;IAAE,CAAE,CAAC;IAkBtD,KAAAxF,QAAQ,GAAkB,IAAI;IAC9B,KAAAM,QAAQ,GAAQuF,SAAS;IAyCzB,KAAArB,WAAW,GAAG,KAAK;IAiDnB,KAAA7C,YAAY,GAAQ,CAAC;IA8KrB,KAAA2C,KAAK,GAAG,IAAI;EAnXM;EA6FTwB,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,iBAAiB,EAAE,IAAI;MACvB7C,kBAAkB,EAAE,IAAI,CAACsC,sBAAsB,CAAC,CAAC,CAAC;MAClDQ,qBAAqB,EAAE,IAAI,CAAC1G,yBAAyB,CAAC,CAAC,CAAC;MACxDoE,aAAa,EAAE,IAAI,CAACgC,kBAAkB,CAAC,CAAC,CAAC;MACzCzG,WAAW,EAAE;KAEd;IACD,IAAI,CAACgH,gBAAgB,EAAE;EACzB;EAMApG,UAAUA,CAAA;IACR,IAAI,IAAI,CAACQ,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACN,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACmG,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACZ,KAAK,GAAG,IAAI,CAAC,CAAC;MAC7C;IACF;EACF;EAEAjC,cAAcA,CAAC8C,KAAU;IACvB,MAAMC,IAAI,GAASD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAG,mBAAmB;IACrC,IAAI,CAACA,SAAS,CAACC,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MAC9B,IAAI,CAAC9B,OAAO,CAAC+B,YAAY,CAAC,cAAc,CAAC;MACzC;IACF;IACA,IAAIN,IAAI,EAAE;MACR,MAAMO,YAAY,GAAG,CAAC,iBAAiB,CAAC;MACxC,IAAIA,YAAY,CAACC,QAAQ,CAACR,IAAI,CAACK,IAAI,CAAC,EAAE;QACpC,IAAI,CAAC3G,QAAQ,GAAGsG,IAAI,CAACS,IAAI;QACzB,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;QAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;UACzB,IAAI,CAAC7G,QAAQ,GAAG;YACd8G,KAAK,EAAEd,IAAI,CAACS,IAAI;YAChBM,KAAK,EAAEF,CAAC,CAACZ,MAAM,EAAEe,MAAM,EAAEC,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACjDC,IAAI,EAAEnB,IAAI,CAACS,IAAI,CAACD,QAAQ,CAAC,KAAK,CAAC,GAAGR,IAAI,GAAGA,IAAI;YAC7CoB,WAAW,EAAEpB,IAAI;YACjBqB,SAAS,EAAE7K,YAAY,CAAC8K;WACzB;UACD,IAAI,IAAI,CAACzB,SAAS,EAAE;YAClB,IAAI,CAACA,SAAS,CAACC,aAAa,CAACZ,KAAK,GAAG,IAAI;UAC3C;QACF,CAAC;QACDwB,MAAM,CAACa,aAAa,CAACvB,IAAI,CAAC;MAC5B;IACF;EACF;EAMAhE,oBAAoBA,CAACwF,GAAU;IAC7B,OAAOA,GAAG,CAACC,KAAK,CAAEC,IAAyB,IAAKA,IAAI,CAACzG,SAAS,CAAC;EACjE;EAEAT,uBAAuBA,CAACF,KAAa;IACnC,IAAI,IAAI,CAAC4D,WAAW,EAAE;MACpB,IAAI5D,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACI,WAAW,CAAC,CAAC,CAAC,CAACyB,MAAM,EAAE;QACpD,MAAM,IAAIwF,KAAK,CAAC,8DAA8D,CAAC;MACjF;MACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAClH,WAAW,EAAE;QACxC,IAAIJ,KAAK,IAAIsH,SAAS,CAACzF,MAAM,IAAI,CAACyF,SAAS,CAACtH,KAAK,CAAC,CAACW,SAAS,EAAE;UAC5D,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MACA,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd;EAEAV,gBAAgBA,CAACsH,OAAgB,EAAEvH,KAAa;IAC9C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,MAAM,IAAIqH,KAAK,CAAC,qDAAqD,CAAC;IACxE;IACA,KAAK,MAAMC,SAAS,IAAI,IAAI,CAAClH,WAAW,EAAE;MACxC,IAAIJ,KAAK,GAAGsH,SAAS,CAACzF,MAAM,EAAE;QAAE;QAC9ByF,SAAS,CAACtH,KAAK,CAAC,CAACW,SAAS,GAAG4G,OAAO;MACtC;IACF;EACF;EAEA/F,YAAYA,CAAC+F,OAAgB,EAAEL,GAAU;IACvC,KAAK,MAAME,IAAI,IAAIF,GAAG,EAAE;MACtBE,IAAI,CAACzG,SAAS,GAAG4G,OAAO;IAC1B;EACF;EAGAC,cAAcA,CAAC5C,KAAU,EAAE6C,OAAc;IACvC,KAAK,MAAML,IAAI,IAAIK,OAAO,EAAE;MAC1B,IAAIL,IAAI,CAACxC,KAAK,KAAKA,KAAK,EAAE;QACxB,OAAOwC,IAAI;MACb;IACF;IACA,OAAO,IAAI;EACb;EAGAM,aAAaA,CAACN,IAAS,EAAEO,GAAQ;IAE/B,IAAI,CAACtD,cAAc,CAACuD,+BAA+B,CAAC;MAClDC,IAAI,EAAET,IAAI,CAACU;KACZ,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,MAAMC,IAAI,GAAGH,GAAG,CAACC,OAAO;QACxB,IAAI,CAACzI,cAAc,GAAG;UACpB4I,YAAY,EAAED,IAAI,CAACE,SAAS,EAAED,YAAY;UAC1CN,SAAS,EAAEK,IAAI,CAACE,SAAS,EAAEP,SAAS;UACpC1J,WAAW,EAAE+J,IAAI,CAACE,SAAS,EAAEjK,WAAW;UACxCE,WAAW,EAAE6J,IAAI,CAACE,SAAS,EAAE/J,WAAW,GAAG6J,IAAI,CAACE,SAAS,EAAE/J,WAAW,GAAG,EAAE;UAC3EgK,KAAK,EAAEH,IAAI,CAACE,SAAS,EAAEC,KAAK;UAC5B9J,OAAO,EAAE2J,IAAI,CAACE,SAAS,EAAE7J,OAAO;UAChCiB,QAAQ,EAAE0I,IAAI,CAACE,SAAS,EAAE5I,QAAQ;UAClC;UACA;UACAyC,YAAY,EAAEiG,IAAI,EAAEjG,YAAY,GAAGiG,IAAI,EAAEjG,YAAY,GAAG,EAAE;UAC1Da,aAAa,EAAEoF,IAAI,CAACE,SAAS,EAAE7J,OAAO,GAAG,IAAI,CAACgJ,cAAc,CAACW,IAAI,CAACE,SAAS,EAAE7J,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC,GAAG,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC;UACjI8D,kBAAkB,EAAE4F,IAAI,CAACE,SAAS,EAAEjK,WAAW,GAAG,IAAI,CAACoJ,cAAc,CAACW,IAAI,CAACE,SAAS,EAAEjK,WAAW,EAAE,IAAI,CAACC,iBAAiB,CAAC,GAAG,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC;UACtJ+D,cAAc,EAAE+F,IAAI,CAAC/F,cAAc;UACnCmG,gBAAgB,EAAEJ,IAAI,EAAEI,gBAAgB,EAAEC,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAAC9H,SAAS,CAAC;UACzE0H,SAAS,EAAEF,IAAI,CAACE;SACjB;QAED,IAAIF,IAAI,IAAIA,IAAI,EAAE/F,cAAc,IAAI+F,IAAI,EAAE/F,cAAc,CAACP,MAAM,EAAE;UAC/D,IAAIsG,IAAI,EAAE/F,cAAc,CAACP,MAAM,KAAK,CAAC,EAAE,OAAOoD,SAAS;UACvD,IAAI,CAAClE,YAAY,GAAGoH,IAAI,EAAE/F,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;UACnD,IAAIyG,UAAU,GAAGP,IAAI,EAAE/F,cAAc,CAAC,CAAC,CAAC,CAACL,SAAS,GAAG,IAAI4G,IAAI,CAACR,IAAI,EAAE/F,cAAc,CAAC,CAAC,CAAC,CAACL,SAAS,CAAC,GAAG,EAAE;UACrG,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAAC/F,cAAc,CAACP,MAAM,EAAE4G,CAAC,EAAE,EAAE;YACnD,IAAIN,IAAI,CAAC/F,cAAc,CAACqG,CAAC,CAAC,CAAC1G,SAAS,EAAE;cACpC,MAAM6G,WAAW,GAAG,IAAID,IAAI,CAACR,IAAI,CAAC/F,cAAc,CAACqG,CAAC,CAAC,CAAC1G,SAAU,CAAC;cAC/D,IAAI6G,WAAW,GAAGF,UAAU,EAAE;gBAC5BA,UAAU,GAAGE,WAAW;gBACxB,IAAI,CAAC7H,YAAY,GAAGoH,IAAI,EAAE/F,cAAc,CAACqG,CAAC,CAAC,CAACxG,OAAO;cACrD;YACF;UACF;QACF;QACA,IAAI,CAAC4G,YAAY,EAAE;QACnB,IAAI,CAAC7E,aAAa,CAAC8E,IAAI,CAACnB,GAAG,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EAIAoB,gBAAgBA,CAACZ,IAAa;IAC5B,MAAMa,SAAS,GAAkB,EAAE;IACnC,KAAK,MAAM1B,SAAS,IAAIa,IAAI,EAAE;MAC5B,KAAK,MAAMc,KAAK,IAAI3B,SAAS,EAAE;QAC7B,IAAI2B,KAAK,CAACtI,SAAS,IAAIsI,KAAK,CAACnI,SAAS,EAAE;UACtCkI,SAAS,CAACE,IAAI,CAAC;YACbC,QAAQ,EAAEF,KAAK,CAACE,QAAQ;YACxBxI,SAAS,EAAEsI,KAAK,CAACtI,SAAS;YAC1BS,MAAM,EAAE6H,KAAK,CAAC7H,MAAM;YACpBP,UAAU,EAAEoI,KAAK,CAACpI;WACnB,CAAC;QACJ;MACF;IACF;IACA,OAAOmI,SAAS;EAClB;EAIAxF,YAAYA,CAACmE,GAAQ;IACnB,IAAI,CAACyB,UAAU,EAAE;IACjB,IAAI,IAAI,CAAClF,KAAK,CAACmF,aAAa,CAACxH,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACoC,OAAO,CAACqF,aAAa,CAAC,IAAI,CAACpF,KAAK,CAACmF,aAAa,CAAC;MACpD;IACF;IACA,IAAI,CAACE,iBAAiB,GAAG;MACvBnB,YAAY,EAAE,IAAI,CAACjD,WAAW,CAACC,iBAAiB,CAACR,KAAK;MACtDkD,SAAS,EAAE,IAAI,CAACtI,cAAc,CAACsI,SAAS;MACxC1J,WAAW,EAAE,IAAI,CAACoB,cAAc,CAAC+C,kBAAkB,CAACqC,KAAK;MACzDtG,WAAW,EAAE,IAAI,CAACkB,cAAc,CAAClB,WAAW;MAC5CgK,KAAK,EAAE,IAAI,CAAC9I,cAAc,EAAE8I,KAAK;MACjC9J,OAAO,EAAE,IAAI,CAACgB,cAAc,CAACuD,aAAa,CAAC6B,KAAK;MAChD6B,KAAK,EAAE,IAAI,CAAC/G,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoH,WAAW,GAAG7B,SAAS;MAC5D/C,YAAY,EAAE,IAAI,CAAC1C,cAAc,CAAC0C,YAAY;MAC9CsH,YAAY,EAAE,IAAI,CAACT,gBAAgB,CAAC,IAAI,CAAC3I,WAAW;KACrD;IACD,IAAI,CAACkE,aAAa,CAACmF,UAAU,CAAC,IAAI,CAACF,iBAAiB,CAAC,CAACxB,SAAS,CAACC,GAAG,IAAG;MACpE,IAAIA,GAAG,IAAIA,GAAG,CAACH,IAAK,IAAIG,GAAG,CAACH,IAAI,CAACK,UAAW,KAAK,CAAC,EAAE;QAClD,IAAI,CAACjE,OAAO,CAACyF,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACxK,UAAU,EAAE;QACjB,IAAI,CAACyK,aAAa,EAAE;QACpBhC,GAAG,CAACiC,KAAK,EAAE;MACb,CAAC,MAAM;QACL,IAAI,CAAC3F,OAAO,CAAC+B,YAAY,CAACgC,GAAG,IAAIA,GAAG,CAACH,IAAI,IAAIG,GAAG,CAACH,IAAI,CAACgC,OAAQ,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAEAvM,QAAQA,CAAA;IACN,IAAI,CAACqM,aAAa,EAAE;EACtB;EAEAG,WAAWA,CAACC,OAAe;IACzB,IAAI,CAACrF,SAAS,GAAGqF,OAAO;IACxB,IAAI,CAACJ,aAAa,EAAE;EACtB;EAIAK,YAAYA,CAACC,YAAyB,EAAEC,QAAc;IACpD,MAAMC,WAAW,GAAkB,EAAE;IACrC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CACrCN,YAAY,CAACO,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACrJ,MAAM,CAAC,CAACoH,MAAM,CAACkC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAa,CAC1F,CAAC;IACF,KAAK,MAAMA,KAAK,IAAIN,YAAY,EAAE;MAChCD,WAAW,CAACjB,IAAI,CAAC,EAAE,CAAC;IACtB;IACA,KAAK,MAAMuB,QAAQ,IAAIR,YAAY,EAAE;MACnC,MAAMU,UAAU,GAAGP,YAAY,CAACQ,OAAO,CAACH,QAAQ,CAACrJ,MAAgB,CAAC;MAClE,IAAIuJ,UAAU,KAAK,CAAC,CAAC,EAAE;QACrBR,WAAW,CAACQ,UAAU,CAAC,CAACzB,IAAI,CAAC;UAC3BvI,SAAS,EAAE8J,QAAQ,EAAE9J,SAAS,IAAI,KAAK;UACvCwI,QAAQ,EAAEsB,QAAQ,CAACvJ,GAAG;UACtB2J,UAAU,EAAEJ,QAAQ,CAACI,UAAU;UAC/BzJ,MAAM,EAAEqJ,QAAQ,CAACrJ,MAAM;UACvBP,UAAU,EAAE4J,QAAQ,CAAC5J,UAAU;UAC/BC,SAAS,EAAE2J,QAAQ,CAAC3J;SACrB,CAAC;MACJ;IACF;IACA,OAAOqJ,WAAW;EACpB;EAMAW,eAAeA,CAACC,CAAQ,EAAEC,CAAQ;IAChC,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAACF,CAAC,CAACR,GAAG,CAACpD,IAAI,IAAI,CAAC,GAAGA,IAAI,CAACvG,UAAU,IAAIuG,IAAI,CAAChG,MAAM,EAAE,EAAEgG,IAAI,CAACzG,SAAS,CAAC,CAAC,CAAC;IAC1F,OAAOoK,CAAC,CAACP,GAAG,CAACpD,IAAI,IAAG;MAClB,MAAM+D,GAAG,GAAG,GAAG/D,IAAI,CAACvG,UAAU,IAAIuG,IAAI,CAAChG,MAAM,EAAE;MAC/C,OAAO;QACL,GAAGgG,IAAI;QACPzG,SAAS,EAAEsK,IAAI,CAACG,GAAG,CAACD,GAAG,CAAC,GAAGF,IAAI,CAACI,GAAG,CAACF,GAAG,CAAC,GAAG;OAC5C;IACH,CAAC,CAAC;EACJ;EAEAG,qBAAqBA,CAACC,GAAsB;IAC1C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACtK,MAAM,IAAI,CAAC,KAAKqK,CAAC,CAACrK,MAAM,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEAyH,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC1E,aAAa,CAACwH,6BAA6B,CAAC;MACtD9D,IAAI,EAAE;QAAE+D,YAAY,EAAE,IAAI,CAACzG,WAAW,CAACC,iBAAiB,CAACR,KAAK;QAAEiH,OAAO,EAAE;MAAK;KAC/E,CAAC,CAACC,IAAI,CACL/P,GAAG,CAACiM,GAAG,IAAG;MACR,IAAIA,GAAG,IAAIA,GAAG,CAACE,UAAU,KAAK,CAAC,IAAIF,GAAG,CAACC,OAAO,EAAE;QAC9C,MAAM8D,IAAI,GAAG,IAAI,CAACT,qBAAqB,CAACtD,GAAG,CAACC,OAAO,CAAC;QACpD,IAAI,CAAC+D,eAAe,GAAG,CAAC,GAAGD,IAAI,CAAC;QAChC,IAAI,IAAI,CAACvM,cAAc,CAACsI,SAAS,EAAE;UACjC,IAAI,CAAC1H,WAAW,GAAG,IAAI,CAAC4J,YAAY,CAAC,IAAI,CAACc,eAAe,CAAC,CAAC,GAAG,IAAI,CAACkB,eAAe,CAAC,EAAE,IAAI,CAACxM,cAAc,CAAC+I,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC/I,cAAc,CAAC+I,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9K,CAAC,MAAM;UACL,IAAI,CAACnI,WAAW,GAAG,IAAI,CAAC4J,YAAY,CAAC,CAAC,GAAG+B,IAAI,CAAC,CAAC;QACjD;QACA,IAAI,CAACnI,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,CAAC,CAEH,CAACmE,SAAS,EAAE;EACf;EAOAxI,eAAeA,CAACE,QAAc;IAC5B,IAAIA,QAAQ,EAAE;MACZ,IAAI,CAAC8E,cAAc,CAAC0H,cAAc,CAACxM,QAAQ,CAAC;IAC9C;EACF;EAEAkK,aAAaA,CAAA;IACX,OAAO,IAAI,CAACtF,cAAc,CAAC6H,+BAA+B,CAAC;MACzDrE,IAAI,EAAE;QACJsE,SAAS,EAAE,IAAI,CAACzH,SAAS;QACzB0H,QAAQ,EAAE,IAAI,CAAC3H,QAAQ;QACvBnG,WAAW,EAAE,IAAI,CAAC6G,WAAW,CAAC7G,WAAW;QACzCsN,YAAY,EAAE,IAAI,CAACzG,WAAW,CAACC,iBAAiB,CAACR,KAAK;QACtDpG,OAAO,EAAE,IAAI,CAAC2G,WAAW,CAACpC,aAAa,CAAC6B,KAAK;QAC7CxG,WAAW,EAAE,IAAI,CAAC+G,WAAW,CAAC5C,kBAAkB,CAACqC,KAAK;QACtDlG,cAAc,EAAE,IAAI,CAACyG,WAAW,CAACE,qBAAqB,CAACT;;KAE1D,CAAC,CAACkH,IAAI,CACL/P,GAAG,CAACiM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACmE,UAAU,GAAGrE,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACtD,YAAY,GAAGqD,GAAG,CAACsE,UAAW;MACrC;IACF,CAAC,CAAC,CACH,CAACvE,SAAS,EAAE;EACf;EAGAwE,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACpH,WAAW,CAACC,iBAAiB,CAACR,KAAK,EAAE;MAC5C,IAAI,CAAC+E,aAAa,EAAE;IACtB;EACF;EAEArE,gBAAgBA,CAAA;IACd,IAAI,CAAClB,iBAAiB,CAACoI,qCAAqC,CAAC;MAAE3E,IAAI,EAAE;IAAE,CAAE,CAAC,CAACiE,IAAI,CAC7E/P,GAAG,CAACiM,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACuE,oBAAoB,GAAGzE,GAAG,CAACC,OAAO,CAACuC,GAAG,CAACxC,GAAG,IAAG;UAChD,OAAO;YACLrL,KAAK,EAAEqL,GAAG,CAAC0E,cAAc;YACzB9H,KAAK,EAAEoD,GAAG,CAAC2E;WACZ;QACH,CAAC,CAAC;QACF,IAAI,CAACxH,WAAW,CAACC,iBAAiB,GAAG,IAAI,CAACqH,oBAAoB,CAAC,CAAC,CAAC;QACjE,IAAI,IAAI,CAACtH,WAAW,CAACC,iBAAiB,CAACR,KAAK,EAAE;UAC5C,IAAI,CAAC+E,aAAa,EAAE;QACtB;MACF;IACF,CAAC,CAAC,CACH,CAAC5B,SAAS,EAAE;EACf;EAEAnK,SAASA,CAAC+J,GAAQ,EAAEP,IAAU;IAC5B,IAAI,CAACrG,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC6C,WAAW,GAAG,KAAK;IACxB,IAAI,CAACF,KAAK,GAAG,IAAI;IACjB,IAAI,CAACxE,UAAU,EAAE;IACjB,IAAI,CAACM,cAAc,GAAG;MACpB+C,kBAAkB,EAAE,IAAI,CAAClE,iBAAiB,CAAC,CAAC,CAAC;MAC7C0E,aAAa,EAAE,IAAI,CAACtE,aAAa,CAAC,CAAC,CAAC;MACpC4G,qBAAqB,EAAE,IAAI,CAACP,oBAAoB,CAAC,CAAC,CAAC;MACnDxG,WAAW,EAAE,EAAE;MACfgK,KAAK,EAAE,CAAC;MACR7I,QAAQ,EAAE,EAAE;MACZyC,YAAY,EAAE,EAAE;MAChB0K,YAAY,EAAE;KACf;IACD,IAAIxF,IAAI,EAAE;MACR,IAAI,CAAC1D,KAAK,GAAG,KAAK;MAClB,IAAI,CAACgE,aAAa,CAACN,IAAI,EAAEO,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAACjE,KAAK,GAAG,IAAI;MACjB,IAAI,CAACmF,YAAY,EAAE;MACnB,IAAI,CAAC7E,aAAa,CAAC8E,IAAI,CAACnB,GAAG,CAAC;IAC9B;EACF;EAEAkF,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAO7Q,MAAM,CAAC6Q,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAC,QAAQA,CAACrF,GAAQ,GAEjB;EAEArE,OAAOA,CAACqE,GAAQ;IACdA,GAAG,CAACiC,KAAK,EAAE;EACb;EAGAR,UAAUA,CAAA;IACR,IAAI,CAAClF,KAAK,CAAC+I,KAAK,EAAE;IAClB,IAAI,IAAI,CAACvJ,KAAK,IAAI,CAAC,IAAI,CAAChE,QAAQ,EAAE;MAChC,IAAI,CAACwE,KAAK,CAACgJ,eAAe,CAAC,MAAM,CAAC;IACpC;IACA,IAAI,CAAChJ,KAAK,CAACiJ,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC3N,cAAc,CAAC0C,YAAY,CAAC;EACjE;;;uCApeW2B,iCAAiC,EAAA1H,EAAA,CAAAiR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnR,EAAA,CAAAiR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArR,EAAA,CAAAiR,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvR,EAAA,CAAAiR,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAzR,EAAA,CAAAiR,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA3R,EAAA,CAAAiR,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAA5R,EAAA,CAAAiR,iBAAA,CAAAS,EAAA,CAAAG,aAAA,GAAA7R,EAAA,CAAAiR,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAA/R,EAAA,CAAAiR,iBAAA,CAAAe,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAjCvK,iCAAiC;MAAAwK,SAAA;MAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UCnF5CrS,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoB,SAAA,qBAAiC;UACnCpB,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,YACyB;UAAAD,EAAA,CAAAE,MAAA,iPAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI3EH,EAHN,CAAAC,cAAA,aAA8B,aACN,aACqC,eACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,oBACkD;UADtBD,EAAA,CAAAmE,gBAAA,2BAAAoO,+EAAA7O,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAAtJ,WAAA,CAAAC,iBAAA,EAAAvF,MAAA,MAAA4O,GAAA,CAAAtJ,WAAA,CAAAC,iBAAA,GAAAvF,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAA2C;UACrE1D,EAAA,CAAAY,UAAA,4BAAA6R,gFAAA;YAAAzS,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAA,OAAAxS,EAAA,CAAAkB,WAAA,CAAkBoR,GAAA,CAAAlC,0BAAA,EAA4B;UAAA,EAAC;UAC/CpQ,EAAA,CAAA8B,UAAA,KAAA4Q,uDAAA,uBAAoE;UAK1E1S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,gBACV;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,qBAAuF;UAA3DD,EAAA,CAAAmE,gBAAA,2BAAAwO,+EAAAjP,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAAtJ,WAAA,CAAA5C,kBAAA,EAAA1C,MAAA,MAAA4O,GAAA,CAAAtJ,WAAA,CAAA5C,kBAAA,GAAA1C,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAA4C;UACtE1D,EAAA,CAAA8B,UAAA,KAAA8Q,uDAAA,uBAAsE;UAK5E5S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACV;UAACD,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEvDH,EADF,CAAAC,cAAA,eAAmB,iBAEuB;UAAtCD,EAAA,CAAAmE,gBAAA,2BAAA0O,2EAAAnP,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAAtJ,WAAA,CAAA7G,WAAA,EAAAuB,MAAA,MAAA4O,GAAA,CAAAtJ,WAAA,CAAA7G,WAAA,GAAAuB,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAAqC;UAG7C1D,EAJM,CAAAG,YAAA,EACwC,EACpC,EACF,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACd;UACvCD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAAkF;UAAtDD,EAAA,CAAAmE,gBAAA,2BAAA2O,+EAAApP,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAAtJ,WAAA,CAAApC,aAAA,EAAAlD,MAAA,MAAA4O,GAAA,CAAAtJ,WAAA,CAAApC,aAAA,GAAAlD,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAAuC;UACjE1D,EAAA,CAAA8B,UAAA,KAAAiR,uDAAA,uBAAkE;UAKxE/S,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACP;UAC9CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,qBAA4F;UAA9DD,EAAA,CAAAmE,gBAAA,2BAAA6O,+EAAAtP,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAAtJ,WAAA,CAAAE,qBAAA,EAAAxF,MAAA,MAAA4O,GAAA,CAAAtJ,WAAA,CAAAE,qBAAA,GAAAxF,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAA+C;UAC3E1D,EAAA,CAAA8B,UAAA,KAAAmR,uDAAA,uBAAyE;UAK/EjT,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGJH,EADF,CAAAC,cAAA,cAAsB,eAC8C;UAIhED,EAHA,CAAA8B,UAAA,KAAAoR,oDAAA,qBAA6E,KAAAC,oDAAA,qBAGS;UAK5FnT,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;UAKEH,EAJR,CAAAC,cAAA,eAAmC,iBAC4C,aACpE,cACgD,cACpB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAA0C;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAEhDF,EAFgD,CAAAG,YAAA,EAAK,EAC9C,EACC;UACRH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA8B,UAAA,KAAAsR,gDAAA,mBAAoD;UA6B5DpT,EAHM,CAAAG,YAAA,EAAQ,EACF,EACJ,EACO;UAEbH,EADF,CAAAC,cAAA,0BAAsD,0BAES;UAD7CD,EAAA,CAAAmE,gBAAA,wBAAAkP,iFAAA3P,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAAxS,EAAA,CAAAuE,kBAAA,CAAA+N,GAAA,CAAA/J,SAAA,EAAA7E,MAAA,MAAA4O,GAAA,CAAA/J,SAAA,GAAA7E,MAAA;YAAA,OAAA1D,EAAA,CAAAkB,WAAA,CAAAwC,MAAA;UAAA,EAAoB;UAClC1D,EAAA,CAAAY,UAAA,wBAAAyS,iFAAA3P,MAAA;YAAA1D,EAAA,CAAAc,aAAA,CAAA0R,GAAA;YAAA,OAAAxS,EAAA,CAAAkB,WAAA,CAAcoR,GAAA,CAAA3E,WAAA,CAAAjK,MAAA,CAAmB;UAAA,EAAC;UAGxC1D,EAFI,CAAAG,YAAA,EAAiB,EACF,EACT;UAGVH,EAAA,CAAA8B,UAAA,KAAAwR,yDAAA,kCAAAtT,EAAA,CAAAuT,sBAAA,CAAoD;;;UArHdvT,EAAA,CAAAM,SAAA,IAA2C;UAA3CN,EAAA,CAAAyE,gBAAA,YAAA6N,GAAA,CAAAtJ,WAAA,CAAAC,iBAAA,CAA2C;UAEzCjJ,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAkS,GAAA,CAAAhC,oBAAA,CAAuB;UASzBtQ,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAyE,gBAAA,YAAA6N,GAAA,CAAAtJ,WAAA,CAAA5C,kBAAA,CAA4C;UAC1CpG,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAI,UAAA,YAAAkS,GAAA,CAAA5J,sBAAA,CAAyB;UAYnD1I,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAyE,gBAAA,YAAA6N,GAAA,CAAAtJ,WAAA,CAAA7G,WAAA,CAAqC;UASbnC,EAAA,CAAAM,SAAA,GAAuC;UAAvCN,EAAA,CAAAyE,gBAAA,YAAA6N,GAAA,CAAAtJ,WAAA,CAAApC,aAAA,CAAuC;UACrC5G,EAAA,CAAAM,SAAA,EAAqB;UAArBN,EAAA,CAAAI,UAAA,YAAAkS,GAAA,CAAA1J,kBAAA,CAAqB;UAYrB5I,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAyE,gBAAA,YAAA6N,GAAA,CAAAtJ,WAAA,CAAAE,qBAAA,CAA+C;UAC/ClJ,EAAA,CAAAM,SAAA,EAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAkS,GAAA,CAAA9P,yBAAA,CAA4B;UAShBxC,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,SAAAkS,GAAA,CAAAkB,MAAA,CAAY;UAGZxT,EAAA,CAAAM,SAAA,EAAc;UAAdN,EAAA,CAAAI,UAAA,SAAAkS,GAAA,CAAAmB,QAAA,CAAc;UAoBnCzT,EAAA,CAAAM,SAAA,IAAgB;UAAhBN,EAAA,CAAAI,UAAA,YAAAkS,GAAA,CAAApC,UAAA,CAAgB;UA+B3BlQ,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAyE,gBAAA,SAAA6N,GAAA,CAAA/J,SAAA,CAAoB;UAAuBvI,EAAtB,CAAAI,UAAA,aAAAkS,GAAA,CAAAhK,QAAA,CAAqB,mBAAAgK,GAAA,CAAA9J,YAAA,CAAgC;;;qBDvClFhJ,YAAY,EAAAkU,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAEtU,YAAY,EAAAuU,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAA7C,EAAA,CAAA8C,eAAA,EAAA9C,EAAA,CAAA+C,mBAAA,EAAA/C,EAAA,CAAAgD,qBAAA,EAAAhD,EAAA,CAAAiD,qBAAA,EAAAjD,EAAA,CAAAkD,mBAAA,EAAAlD,EAAA,CAAAmD,gBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAApD,EAAA,CAAAqD,iBAAA,EAAAC,GAAA,CAAAC,aAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAAC,GAAA,CAAAC,kBAAA,EAAAC,GAAA,CAAAC,cAAA,EAAgBxV,kBAAkB,EAAEI,mBAAmB,EAAkBH,kBAAkB;MAAAwV,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}