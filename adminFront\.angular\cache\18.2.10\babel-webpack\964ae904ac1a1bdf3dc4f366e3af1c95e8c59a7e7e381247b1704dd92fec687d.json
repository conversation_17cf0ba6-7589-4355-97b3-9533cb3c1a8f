{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Norwegian Bokmål [nb]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/rexxars\n//!           <PERSON><PERSON><PERSON> : https://github.com/sigurdga\n//!           <PERSON> : https://github.com/stephenramthun\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var nb = moment.defineLocale('nb', {\n    months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n    weekdaysShort: 'sø._ma._ti._on._to._fr._lø.'.split('_'),\n    weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] HH:mm',\n      LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[i dag kl.] LT',\n      nextDay: '[i morgen kl.] LT',\n      nextWeek: 'dddd [kl.] LT',\n      lastDay: '[i går kl.] LT',\n      lastWeek: '[forrige] dddd [kl.] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: '%s siden',\n      s: 'noen sekunder',\n      ss: '%d sekunder',\n      m: 'ett minutt',\n      mm: '%d minutter',\n      h: 'en time',\n      hh: '%d timer',\n      d: 'en dag',\n      dd: '%d dager',\n      w: 'en uke',\n      ww: '%d uker',\n      M: 'en måned',\n      MM: '%d måneder',\n      y: 'ett år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return nb;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "nb", "defineLocale", "months", "split", "monthsShort", "monthsParseExact", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "w", "ww", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/nb.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Norwegian Bokmål [nb]\n//! authors : <PERSON><PERSON><PERSON> : https://github.com/rexxars\n//!           <PERSON><PERSON><PERSON> : https://github.com/sigurdga\n//!           <PERSON> : https://github.com/stephenramthun\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var nb = moment.defineLocale('nb', {\n        months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split(\n            '_'\n        ),\n        monthsShort:\n            'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n        monthsParseExact: true,\n        weekdays: 'søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag'.split('_'),\n        weekdaysShort: 'sø._ma._ti._on._to._fr._lø.'.split('_'),\n        weekdaysMin: 'sø_ma_ti_on_to_fr_lø'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY [kl.] HH:mm',\n            LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm',\n        },\n        calendar: {\n            sameDay: '[i dag kl.] LT',\n            nextDay: '[i morgen kl.] LT',\n            nextWeek: 'dddd [kl.] LT',\n            lastDay: '[i går kl.] LT',\n            lastWeek: '[forrige] dddd [kl.] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'om %s',\n            past: '%s siden',\n            s: 'noen sekunder',\n            ss: '%d sekunder',\n            m: 'ett minutt',\n            mm: '%d minutter',\n            h: 'en time',\n            hh: '%d timer',\n            d: 'en dag',\n            dd: '%d dager',\n            w: 'en uke',\n            ww: '%d uker',\n            M: 'en måned',\n            MM: '%d måneder',\n            y: 'ett år',\n            yy: '%d år',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return nb;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,oFAAoF,CAACC,KAAK,CAC9F,GACJ,CAAC;IACDC,WAAW,EACP,4DAA4D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC3EE,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE,oDAAoD,CAACH,KAAK,CAAC,GAAG,CAAC;IACzEI,aAAa,EAAE,6BAA6B,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvDK,WAAW,EAAE,sBAAsB,CAACL,KAAK,CAAC,GAAG,CAAC;IAC9CM,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,0BAA0B;MAC/BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,gBAAgB;MACzBC,OAAO,EAAE,mBAAmB;MAC5BC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,gBAAgB;MACzBC,QAAQ,EAAE,yBAAyB;MACnCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,UAAU;MAChBC,CAAC,EAAE,eAAe;MAClBC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,CAAC,EAAE,SAAS;MACZC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAO7C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}