{"ast": null, "code": "export class ElectricityData {}", "map": {"version": 3, "names": ["ElectricityData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\electricity.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface Month {\r\n  month: string;\r\n  delta: string;\r\n  down: boolean;\r\n  kWatts: string;\r\n  cost: string;\r\n}\r\n\r\nexport interface Electricity {\r\n  title: string;\r\n  active?: boolean;\r\n  months: Month[];\r\n}\r\n\r\nexport interface ElectricityChart {\r\n  label: string;\r\n  value: number;\r\n}\r\n\r\nexport abstract class ElectricityData {\r\n  abstract getListData(): Observable<Electricity[]>;\r\n  abstract getChartData(): Observable<ElectricityChart[]>;\r\n}\r\n"], "mappings": "AAqBA,OAAM,MAAgBA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}