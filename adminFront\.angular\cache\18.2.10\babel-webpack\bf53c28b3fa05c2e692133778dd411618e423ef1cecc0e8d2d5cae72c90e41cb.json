{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Json } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-json';\nimport { apiHouseHoldDetailGetHouseHoldDetailListPost$Plain } from '../fn/house-hold-detail/api-house-hold-detail-get-house-hold-detail-list-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let HouseHoldDetailService = /*#__PURE__*/(() => {\n  class HouseHoldDetailService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiHouseHoldDetailGetHouseHoldDetailListPost()` */\n    static {\n      this.ApiHouseHoldDetailGetHouseHoldDetailListPostPath = '/api/HouseHoldDetail/GetHouseHoldDetailList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params, context) {\n      return apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldDetailGetHouseHoldDetailListPost$Plain(params, context) {\n      return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHoldDetailGetHouseHoldDetailListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params, context) {\n      return apiHouseHoldDetailGetHouseHoldDetailListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHoldDetailGetHouseHoldDetailListPost$Json(params, context) {\n      return this.apiHouseHoldDetailGetHouseHoldDetailListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function HouseHoldDetailService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseHoldDetailService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: HouseHoldDetailService,\n        factory: HouseHoldDetailService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HouseHoldDetailService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}