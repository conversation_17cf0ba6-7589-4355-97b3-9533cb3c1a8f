{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from '../components/shared.module';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { switchMap, tap } from 'rxjs';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/shared/services/event.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@nebular/theme\";\nimport * as i8 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../components/pagination/pagination.component\";\nimport * as i10 from \"../../@theme/pipes/mapping.pipe\";\nfunction ApproveWaitingComponent_ngx_breadcrumb_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ngx-breadcrumb\");\n  }\n}\nfunction ApproveWaitingComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1, \"\\u5F85\\u5BE9\\u6838\\u5217\\u8868 / \\u6D3D\\u8AC7\\u7D00\\u9304\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApproveWaitingComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1, \"\\u5F85\\u5BE9\\u6838\\u5217\\u8868 / \\u5BA2\\u8B8A\\u78BA\\u8A8D\\u5716\\u8AAA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApproveWaitingComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1, \"\\u5F85\\u5BE9\\u6838\\u5217\\u8868 / \\u5EFA\\u6848\\u516C\\u4F48\\u6B04\\u6587\\u4EF6\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApproveWaitingComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1, \"\\u5F85\\u5BE9\\u6838\\u5217\\u8868 / \\u5BA2\\u8B8A\\u539F\\u5247\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApproveWaitingComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtext(1, \"\\u5F85\\u5BE9\\u6838\\u5217\\u8868 / \\u6A19\\u6E96\\u5716\\u8AAA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ApproveWaitingComponent_nb_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2.name, \" \");\n  }\n}\nfunction ApproveWaitingComponent_nb_select_32_nb_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildCaseData_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", buildCaseData_r5.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", buildCaseData_r5.CBuildCaseName, \" \");\n  }\n}\nfunction ApproveWaitingComponent_nb_select_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-select\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ApproveWaitingComponent_nb_select_32_Template_nb_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.buildCaseId, $event) || (ctx_r3.buildCaseId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(1, ApproveWaitingComponent_nb_select_32_nb_option_1_Template, 2, 2, \"nb-option\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.buildCaseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listUserBuildCases);\n  }\n}\nfunction ApproveWaitingComponent_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function ApproveWaitingComponent_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.searchList());\n    });\n    i0.ɵɵelement(1, \"i\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", \"\\u67E5\\u8A62\", \" \");\n  }\n}\nfunction ApproveWaitingComponent_tbody_52_tr_1_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ApproveWaitingComponent_tbody_52_tr_1_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const item_r8 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.edit(item_r8));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", \"\\u67E5\\u770B\", \" \");\n  }\n}\nfunction ApproveWaitingComponent_tbody_52_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 32);\n    i0.ɵɵtemplate(16, ApproveWaitingComponent_tbody_52_tr_1_button_16_Template, 2, 1, \"button\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r8 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CID);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r8.CBuildcaseName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 7, item_r8.CType), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.CName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 9, item_r8.CCreateDT, \"yyyy-MM-dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r8.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isUpdate);\n  }\n}\nfunction ApproveWaitingComponent_tbody_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tbody\");\n    i0.ɵɵtemplate(1, ApproveWaitingComponent_tbody_52_tr_1_Template, 17, 12, \"tr\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.listApprovalWaitingList);\n  }\n}\nexport let ApproveWaitingComponent = /*#__PURE__*/(() => {\n  class ApproveWaitingComponent extends BaseComponent {\n    constructor(allow, _buildCaseService, _specialChangeService, _router, _eventService) {\n      super(allow);\n      this.allow = allow;\n      this._buildCaseService = _buildCaseService;\n      this._specialChangeService = _specialChangeService;\n      this._router = _router;\n      this._eventService = _eventService;\n      this.CType = -1;\n      this.CDateStart = \"\";\n      this.CDateEnd = \"\";\n      this.isReadOnly = false;\n      this.TYPE_WAITING_APPROVE = [{\n        value: -1,\n        name: '全部'\n      }, {\n        value: 1,\n        name: '洽談紀錄'\n      }, {\n        value: 2,\n        name: '客變確認圖說'\n      }, {\n        value: 3,\n        name: '建案公佈欄文件'\n      }, {\n        value: 4,\n        name: '客變原則'\n      }, {\n        value: 5,\n        name: '標準圖說'\n      }];\n      this.listUserBuildCases = [];\n      this.listApprovalWaitingList = [];\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.buildCaseId = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.listUserBuildCases = res.Entries ?? [];\n          this.listUserBuildCases.unshift({\n            CBuildCaseName: \"全部\",\n            cID: -1\n          });\n          if (!this.buildCaseId) {\n            this.buildCaseId = this.listUserBuildCases[0].cID;\n          }\n        }\n      }), switchMap(() => this.getListApproval(1))).subscribe();\n    }\n    getListApproval(pageIndex) {\n      return this._specialChangeService.apiSpecialChangeGetApproveWaitingListPost$Json({\n        body: {\n          CBuilCaseID: this.buildCaseId,\n          PageIndex: pageIndex,\n          PageSize: this.pageSize,\n          CDateEnd: this.CDateEnd == \"\" ? null : new Date(this.CDateEnd).toISOString(),\n          CDateStart: this.CDateEnd == \"\" ? null : new Date(this.CDateStart).toISOString(),\n          CType: +this.CType\n        }\n      }).pipe(tap(res => {\n        if (res.StatusCode == 0) {\n          this.listApprovalWaitingList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    searchList() {\n      this.getListApproval(1).subscribe();\n    }\n    edit(id) {\n      this._router.navigate([`/pages/approve-waiting/${id.CBuildCaseId}/${id.CID}`], {\n        queryParams: {\n          type: id.CType\n        }\n      });\n    }\n    getListPageChange(pageIndex) {\n      this.getListApproval(pageIndex).subscribe();\n    }\n    ngOnChanges() {\n      //Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.\n      //Add '${implements OnChanges}' to the class.\n      console.log(this.type);\n      if (this.type > 0) {\n        this.CType = this.type;\n        this.isReadOnly = true;\n        this.searchList();\n      }\n    }\n    static {\n      this.ɵfac = function ApproveWaitingComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApproveWaitingComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.BuildCaseService), i0.ɵɵdirectiveInject(i2.SpecialChangeService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.EventService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ApproveWaitingComponent,\n        selectors: [[\"app-approve-waiting\"]],\n        inputs: {\n          type: \"type\"\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n        decls: 54,\n        vars: 32,\n        consts: [[\"dateStartpicker\", \"\"], [\"dateEndpicker\", \"\"], [\"accent\", \"success\"], [4, \"ngIf\"], [\"style\", \"font-size: 32px;\", 4, \"ngIf\"], [1, \"bg-white\"], [1, \"my-2\", \"w-100\"], [1, \"flex\", \"items-center\", \"w-[50%]\"], [1, \"w-full\", \"flex\", \"flex-col\", \"items-start\", \"mr-4\"], [\"for\", \"classification\", 1, \"mb-3\"], [1, \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"nbInput\", \"\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5 -- --:--\", 3, \"ngModelChange\", \"ngModel\", \"nbDatepicker\"], [\"format\", \"yyyy/MM/dd hh mm:ss\", \"withSeconds\", \"\", 3, \"max\"], [1, \"w-full\", \"flex\", \"flex-col\", \"items-start\"], [\"for\", \"classification\", 1, \"mb-3\", \"mr-2\"], [\"format\", \"yyyy/MM/dd hh mm:ss\", \"withSeconds\", \"\", 3, \"min\"], [1, \"flex\", \"justify-between\", \"mt-6\"], [1, \"w-full\", \"flex\", \"items-center\"], [\"for\", \"classification\", 1, \"mr-4\"], [\"class\", \"w-[40%]\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"class\", \"w-[10%] btn btn-info ml-2\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\"], [1, \"text-white\", 2, \"background-color\", \"#27ae60\"], [3, \"CollectionSizeChange\", \"PageChange\", \"PageSizeChange\", \"CollectionSize\", \"Page\", \"PageSize\"], [2, \"font-size\", \"32px\"], [3, \"value\"], [1, \"w-[40%]\", 3, \"ngModelChange\", \"ngModel\"], [1, \"w-[10%]\", \"btn\", \"btn-info\", \"ml-2\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"mr-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\"], [\"class\", \"btn btn-sm btn-outline-success mr-2\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-sm\", \"btn-outline-success\", \"mr-2\", 3, \"click\"]],\n        template: function ApproveWaitingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n            i0.ɵɵtemplate(2, ApproveWaitingComponent_ngx_breadcrumb_2_Template, 1, 0, \"ngx-breadcrumb\", 3)(3, ApproveWaitingComponent_div_3_Template, 2, 0, \"div\", 4)(4, ApproveWaitingComponent_div_4_Template, 2, 0, \"div\", 4)(5, ApproveWaitingComponent_div_5_Template, 2, 0, \"div\", 4)(6, ApproveWaitingComponent_div_6_Template, 2, 0, \"div\", 4)(7, ApproveWaitingComponent_div_7_Template, 2, 0, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"nb-card-body\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"span\", 9);\n            i0.ɵɵtext(13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"nb-select\", 10);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ApproveWaitingComponent_Template_nb_select_ngModelChange_14_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CType, $event) || (ctx.CType = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(15, ApproveWaitingComponent_nb_option_15_Template, 2, 2, \"nb-option\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 8)(17, \"span\", 9);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ApproveWaitingComponent_Template_input_ngModelChange_19_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CDateStart, $event) || (ctx.CDateStart = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"nb-date-timepicker\", 13, 0);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 14)(23, \"span\", 15);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ApproveWaitingComponent_Template_input_ngModelChange_25_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.CDateEnd, $event) || (ctx.CDateEnd = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(26, \"nb-date-timepicker\", 16, 1);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"div\", 17)(29, \"div\", 18)(30, \"span\", 19);\n            i0.ɵɵtext(31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, ApproveWaitingComponent_nb_select_32_Template, 2, 2, \"nb-select\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, ApproveWaitingComponent_button_33_Template, 3, 1, \"button\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 22)(35, \"table\", 23)(36, \"thead\")(37, \"tr\", 24)(38, \"th\");\n            i0.ɵɵtext(39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"th\");\n            i0.ɵɵtext(41);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"th\");\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"th\");\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"th\");\n            i0.ɵɵtext(47);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"th\");\n            i0.ɵɵtext(49);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"th\");\n            i0.ɵɵtext(51);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(52, ApproveWaitingComponent_tbody_52_Template, 2, 1, \"tbody\", 3);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(53, \"ngx-pagination\", 25);\n            i0.ɵɵtwoWayListener(\"CollectionSizeChange\", function ApproveWaitingComponent_Template_ngx_pagination_CollectionSizeChange_53_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.totalRecords, $event) || (ctx.totalRecords = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageChange\", function ApproveWaitingComponent_Template_ngx_pagination_PageChange_53_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            })(\"PageSizeChange\", function ApproveWaitingComponent_Template_ngx_pagination_PageSizeChange_53_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageSize, $event) || (ctx.pageSize = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"PageChange\", function ApproveWaitingComponent_Template_ngx_pagination_PageChange_53_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.getListPageChange($event));\n            });\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const dateStartpicker_r9 = i0.ɵɵreference(21);\n            const dateEndpicker_r10 = i0.ɵɵreference(27);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.type);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.type == 1);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.type == 2);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.type == 3);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.type == 4);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.type == 5);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(\"\\u5206\\u985E\");\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CType);\n            i0.ɵɵproperty(\"disabled\", ctx.isReadOnly);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.TYPE_WAITING_APPROVE);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(\"\\u9001\\u5BE9\\u958B\\u59CB\\u6642\\u9593\");\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CDateStart);\n            i0.ɵɵproperty(\"nbDatepicker\", dateStartpicker_r9);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"max\", ctx.CDateEnd);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate(\"\\u9001\\u5BE9\\u7D50\\u675F\\u6642\\u9593\");\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.CDateEnd);\n            i0.ɵɵproperty(\"nbDatepicker\", dateEndpicker_r10);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"min\", ctx.CDateStart);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(\"\\u5EFA\\u6848\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !!ctx.listUserBuildCases);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isRead);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(\"\\u9805\\u6B21\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u5EFA\\u6848\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u985E\\u5225\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u540D\\u7A31\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u9001\\u5BE9\\u6642\\u9593\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u9001\\u5BE9\\u5E33\\u865F\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(\"\\u7BA1\\u7406\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !!ctx.listApprovalWaitingList && ctx.listApprovalWaitingList.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵtwoWayProperty(\"CollectionSize\", ctx.totalRecords)(\"Page\", ctx.pageIndex)(\"PageSize\", ctx.pageSize);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DatePipe, SharedModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.NbCardComponent, i7.NbCardBodyComponent, i7.NbCardHeaderComponent, i7.NbInputDirective, i7.NbSelectComponent, i7.NbOptionComponent, i7.NbDatepickerDirective, i7.NbDateTimePickerComponent, i8.BreadcrumbComponent, i9.PaginationComponent, i10.ApprovalWaitingPipe, NbDatepickerModule]\n      });\n    }\n  }\n  return ApproveWaitingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}