{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip(...args) {\n  const resultSelector = popResultSelector(args);\n  const sources = argsOrArgArray(args);\n  return sources.length ? new Observable(subscriber => {\n    let buffers = sources.map(() => []);\n    let completed = sources.map(() => false);\n    subscriber.add(() => {\n      buffers = completed = null;\n    });\n    for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, value => {\n        buffers[sourceIndex].push(value);\n        if (buffers.every(buffer => buffer.length)) {\n          const result = buffers.map(buffer => buffer.shift());\n          subscriber.next(resultSelector ? resultSelector(...result) : result);\n          if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n            subscriber.complete();\n          }\n        }\n      }, () => {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    }\n    return () => {\n      buffers = completed = null;\n    };\n  }) : EMPTY;\n}\n//# sourceMappingURL=zip.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}