{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PeriodsService = /*#__PURE__*/(() => {\n  class PeriodsService {\n    getYears() {\n      return ['2010', '2011', '2012', '2013', '2014', '2015', '2016', '2017', '2018'];\n    }\n    getMonths() {\n      return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    }\n    getWeeks() {\n      return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    }\n    static {\n      this.ɵfac = function PeriodsService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || PeriodsService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PeriodsService,\n        factory: PeriodsService.ɵfac\n      });\n    }\n  }\n  return PeriodsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}