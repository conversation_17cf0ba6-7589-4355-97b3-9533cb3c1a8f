{"ast": null, "code": "import * as i0 from \"@angular/core\";\n/**\n * Global configuration\n */\nexport class ApiConfiguration {\n  constructor() {\n    this.rootUrl = '';\n  }\n  static {\n    this.ɵfac = function ApiConfiguration_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApiConfiguration)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ApiConfiguration,\n      factory: ApiConfiguration.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ApiConfiguration", "constructor", "rootUrl", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\api-configuration.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { Injectable } from '@angular/core';\r\n\r\n/**\r\n * Global configuration\r\n */\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ApiConfiguration {\r\n  rootUrl: string = '';\r\n}\r\n\r\n/**\r\n * Parameters for `ApiModule.forRoot()`\r\n */\r\nexport interface ApiConfigurationParams {\r\n  rootUrl?: string;\r\n}\r\n"], "mappings": ";AAIA;;;AAMA,OAAM,MAAOA,gBAAgB;EAH7BC,YAAA;IAIE,KAAAC,OAAO,GAAW,EAAE;;;;uCADTF,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAG,OAAA,EAAhBH,gBAAgB,CAAAI,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}