{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@nebular/theme\";\nconst _c0 = [\"autoInput\"];\nfunction DefaultTagAutoCompleteComponent_nb_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", option_r2, \" \");\n  }\n}\nexport class DefaultTagAutoCompleteComponent {\n  constructor() {\n    this.IsAutocomplete = true;\n    this.IsDefault = false;\n    this.OutputTag = new EventEmitter();\n    this.request = new ShareRequest();\n    this.options = [];\n    // autocompleteTag = [] as AutocompleteTag[];\n    this.isAutocomplete = false;\n    this.tagValue = '';\n  }\n  ngOnInit() {}\n  filter(value) {\n    const filterValue = value.toLowerCase();\n    return this.options.filter(optionValue => optionValue.toLowerCase().includes(filterValue));\n  }\n  getFilteredOptions(value) {\n    return of(value).pipe(map(filterString => this.filter(filterString)));\n  }\n  onAutocompleteChange() {\n    if (this.IsAutocomplete === false) {\n      return;\n    }\n    this.request.CName = this.tagValue;\n    if (this.isAutocomplete === false) {\n      this.isAutocomplete = true;\n      setTimeout(() => {\n        this.getAutocomplete();\n      }, 100);\n    }\n  }\n  getAutocomplete() {\n    this.request.IsDefault = this.IsDefault;\n    // this.tagService.getAutocompleteList(this.request).subscribe(res => {\n    //   this.autocompleteTag = res.Entries!;\n    //   this.options = [...this.autocompleteTag.map(x => x.CTagValue!)];\n    //   this.filteredOptions$ = this.getFilteredOptions(this.input.nativeElement.value);\n    //   const result = new AutocompleteTag();\n    //   result.CTagKeyId = 0;\n    //   result.CTagValue = this.tagValue;\n    //   this.OutputTag.emit(result);\n    //   this.isAutocomplete = false;\n    // });\n  }\n  onAutocompleteSelectionChange($event) {\n    this.filteredOptions$ = this.getFilteredOptions($event);\n    // const tag = this.autocompleteTag.find(x => x.CTagValue === $event);\n    // if (tag !== undefined) {\n    //   const result = new AutocompleteTag();\n    //   result.CTagKeyId = tag.CTagKeyId;\n    //   result.CTagValue = tag.CTagValue;\n    //   this.OutputTag.emit(result);\n    // }\n  }\n  static {\n    this.ɵfac = function DefaultTagAutoCompleteComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DefaultTagAutoCompleteComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DefaultTagAutoCompleteComponent,\n      selectors: [[\"ngx-default-tag-auto-complete\"]],\n      viewQuery: function DefaultTagAutoCompleteComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      inputs: {\n        IsAutocomplete: \"IsAutocomplete\",\n        IsDefault: \"IsDefault\"\n      },\n      outputs: {\n        OutputTag: \"OutputTag\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 5,\n      consts: [[\"autoInput\", \"\"], [\"auto\", \"\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u6A19\\u7C64\\u540D\\u7A31\", 3, \"input\", \"ngModelChange\", \"nbAutocomplete\", \"ngModel\"], [3, \"selectedChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"]],\n      template: function DefaultTagAutoCompleteComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"input\", 2, 0);\n          i0.ɵɵlistener(\"input\", function DefaultTagAutoCompleteComponent_Template_input_input_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAutocompleteChange());\n          });\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function DefaultTagAutoCompleteComponent_Template_input_ngModelChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.tagValue, $event) || (ctx.tagValue = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"nb-autocomplete\", 3, 1);\n          i0.ɵɵlistener(\"selectedChange\", function DefaultTagAutoCompleteComponent_Template_nb_autocomplete_selectedChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAutocompleteSelectionChange($event));\n          });\n          i0.ɵɵtemplate(4, DefaultTagAutoCompleteComponent_nb_option_4_Template, 2, 2, \"nb-option\", 4);\n          i0.ɵɵpipe(5, \"async\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const auto_r3 = i0.ɵɵreference(3);\n          i0.ɵɵproperty(\"nbAutocomplete\", auto_r3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tagValue);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(5, 3, ctx.filteredOptions$));\n        }\n      },\n      dependencies: [i1.NgForOf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.NbInputDirective, i3.NbOptionComponent, i3.NbAutocompleteComponent, i3.NbAutocompleteDirective, i1.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJkZWZhdWx0LXRhZy1hdXRvLWNvbXBsZXRlLmNvbXBvbmVudC5zY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvY29tcG9uZW50cy9kZWZhdWx0LXRhZy1hdXRvLWNvbXBsZXRlL2RlZmF1bHQtdGFnLWF1dG8tY29tcGxldGUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLDRMQUE0TCIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "of", "map", "ShareRequest", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "DefaultTagAutoCompleteComponent", "constructor", "IsAutocomplete", "<PERSON><PERSON><PERSON><PERSON>", "OutputTag", "request", "options", "isAutocomplete", "tagValue", "ngOnInit", "filter", "value", "filterValue", "toLowerCase", "optionValue", "includes", "getFilteredOptions", "pipe", "filterString", "onAutocompleteChange", "CName", "setTimeout", "getAutocomplete", "onAutocompleteSelectionChange", "$event", "filteredOptions$", "selectors", "viewQuery", "DefaultTagAutoCompleteComponent_Query", "rf", "ctx", "ɵɵlistener", "DefaultTagAutoCompleteComponent_Template_input_input_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵtwoWayListener", "DefaultTagAutoCompleteComponent_Template_input_ngModelChange_0_listener", "ɵɵtwoWayBindingSet", "DefaultTagAutoCompleteComponent_Template_nb_autocomplete_selectedChange_2_listener", "ɵɵtemplate", "DefaultTagAutoCompleteComponent_nb_option_4_Template", "auto_r3", "ɵɵtwoWayProperty", "ɵɵpipeBind1"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\default-tag-auto-complete\\default-tag-auto-complete.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\components\\default-tag-auto-complete\\default-tag-auto-complete.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { NgFor, AsyncPipe } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NbInputModule, NbAutocompleteModule, NbOptionModule } from '@nebular/theme';\r\nimport { ShareRequest } from 'src/app/shared/model/requests/shareRequest';\r\n\r\n@Component({\r\n    selector: 'ngx-default-tag-auto-complete',\r\n    templateUrl: './default-tag-auto-complete.component.html',\r\n    styleUrls: ['./default-tag-auto-complete.component.scss'],\r\n    standalone: true,\r\n    imports: [NbInputModule, NbAutocompleteModule, FormsModule, NgFor, NbOptionModule, AsyncPipe]\r\n})\r\nexport class DefaultTagAutoCompleteComponent implements OnInit {\r\n\r\n  @Input() IsAutocomplete = true;\r\n  @Input() IsDefault = false;\r\n  @Output() OutputTag = new EventEmitter();\r\n\r\n  @ViewChild('autoInput') input: any;\r\n\r\n  constructor(\r\n    // private tagService: TagService\r\n  ) { }\r\n\r\n  request = new ShareRequest();\r\n  options = [] as string[];\r\n  filteredOptions$: Observable<string[]> | undefined;\r\n  // autocompleteTag = [] as AutocompleteTag[];\r\n\r\n  isAutocomplete = false;\r\n  tagValue = '';\r\n\r\n  ngOnInit(): void {\r\n\r\n  }\r\n\r\n  private filter(value: string): string[] {\r\n    const filterValue = value.toLowerCase();\r\n    return this.options.filter(optionValue => optionValue.toLowerCase().includes(filterValue));\r\n  }\r\n\r\n  getFilteredOptions(value: string): Observable<string[]> {\r\n    return of(value).pipe(\r\n      map(filterString => this.filter(filterString)),\r\n    );\r\n  }\r\n\r\n  onAutocompleteChange() {\r\n    if (this.IsAutocomplete === false) {\r\n      return;\r\n    }\r\n    this.request.CName = this.tagValue;\r\n    if (this.isAutocomplete === false) {\r\n      this.isAutocomplete = true;\r\n      setTimeout(() => { this.getAutocomplete(); }, 100);\r\n    }\r\n  }\r\n\r\n  getAutocomplete() {\r\n    this.request.IsDefault = this.IsDefault;\r\n    // this.tagService.getAutocompleteList(this.request).subscribe(res => {\r\n    //   this.autocompleteTag = res.Entries!;\r\n    //   this.options = [...this.autocompleteTag.map(x => x.CTagValue!)];\r\n    //   this.filteredOptions$ = this.getFilteredOptions(this.input.nativeElement.value);\r\n\r\n    //   const result = new AutocompleteTag();\r\n    //   result.CTagKeyId = 0;\r\n    //   result.CTagValue = this.tagValue;\r\n    //   this.OutputTag.emit(result);\r\n\r\n    //   this.isAutocomplete = false;\r\n    // });\r\n  }\r\n\r\n  onAutocompleteSelectionChange($event: any) {\r\n    this.filteredOptions$ = this.getFilteredOptions($event);\r\n    // const tag = this.autocompleteTag.find(x => x.CTagValue === $event);\r\n\r\n    // if (tag !== undefined) {\r\n    //   const result = new AutocompleteTag();\r\n    //   result.CTagKeyId = tag.CTagKeyId;\r\n    //   result.CTagValue = tag.CTagValue;\r\n    //   this.OutputTag.emit(result);\r\n    // }\r\n  }\r\n\r\n}\r\n", "<input #autoInput nbInput type=\"text\" (input)=\"onAutocompleteChange()\" placeholder=\"標籤名稱\" [nbAutocomplete]=\"auto\"\r\n  [(ngModel)]=\"tagValue\" />\r\n\r\n<nb-autocomplete #auto (selectedChange)=\"onAutocompleteSelectionChange($event)\">\r\n  <nb-option *ngFor=\"let option of filteredOptions$ | async\" [value]=\"option\">\r\n    {{ option }}\r\n  </nb-option>\r\n</nb-autocomplete>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AACzF,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,QAAQ,gBAAgB;AAIpC,SAASC,YAAY,QAAQ,4CAA4C;;;;;;;;ICFvEC,EAAA,CAAAC,cAAA,mBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IACzEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,SAAA,MACF;;;ADSF,OAAM,MAAOG,+BAA+B;EAQ1CC,YAAA;IANS,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,SAAS,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,IAAIhB,YAAY,EAAE;IAQxC,KAAAiB,OAAO,GAAG,IAAId,YAAY,EAAE;IAC5B,KAAAe,OAAO,GAAG,EAAc;IAExB;IAEA,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,QAAQ,GAAG,EAAE;EART;EAUJC,QAAQA,CAAA,GAER;EAEQC,MAAMA,CAACC,KAAa;IAC1B,MAAMC,WAAW,GAAGD,KAAK,CAACE,WAAW,EAAE;IACvC,OAAO,IAAI,CAACP,OAAO,CAACI,MAAM,CAACI,WAAW,IAAIA,WAAW,CAACD,WAAW,EAAE,CAACE,QAAQ,CAACH,WAAW,CAAC,CAAC;EAC5F;EAEAI,kBAAkBA,CAACL,KAAa;IAC9B,OAAOtB,EAAE,CAACsB,KAAK,CAAC,CAACM,IAAI,CACnB3B,GAAG,CAAC4B,YAAY,IAAI,IAAI,CAACR,MAAM,CAACQ,YAAY,CAAC,CAAC,CAC/C;EACH;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACjB,cAAc,KAAK,KAAK,EAAE;MACjC;IACF;IACA,IAAI,CAACG,OAAO,CAACe,KAAK,GAAG,IAAI,CAACZ,QAAQ;IAClC,IAAI,IAAI,CAACD,cAAc,KAAK,KAAK,EAAE;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;MAC1Bc,UAAU,CAAC,MAAK;QAAG,IAAI,CAACC,eAAe,EAAE;MAAE,CAAC,EAAE,GAAG,CAAC;IACpD;EACF;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACjB,OAAO,CAACF,SAAS,GAAG,IAAI,CAACA,SAAS;IACvC;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;EACF;EAEAoB,6BAA6BA,CAACC,MAAW;IACvC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACT,kBAAkB,CAACQ,MAAM,CAAC;IACvD;IAEA;IACA;IACA;IACA;IACA;IACA;EACF;;;uCAxEWxB,+BAA+B;IAAA;EAAA;;;YAA/BA,+BAA+B;MAAA0B,SAAA;MAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;UCf5CrC,EAAA,CAAAC,cAAA,kBAC2B;UADWD,EAAA,CAAAuC,UAAA,mBAAAC,gEAAA;YAAAxC,EAAA,CAAAyC,aAAA,CAAAC,GAAA;YAAA,OAAA1C,EAAA,CAAA2C,WAAA,CAASL,GAAA,CAAAX,oBAAA,EAAsB;UAAA,EAAC;UACpE3B,EAAA,CAAA4C,gBAAA,2BAAAC,wEAAAb,MAAA;YAAAhC,EAAA,CAAAyC,aAAA,CAAAC,GAAA;YAAA1C,EAAA,CAAA8C,kBAAA,CAAAR,GAAA,CAAAtB,QAAA,EAAAgB,MAAA,MAAAM,GAAA,CAAAtB,QAAA,GAAAgB,MAAA;YAAA,OAAAhC,EAAA,CAAA2C,WAAA,CAAAX,MAAA;UAAA,EAAsB;UADxBhC,EAAA,CAAAG,YAAA,EAC2B;UAE3BH,EAAA,CAAAC,cAAA,4BAAgF;UAAzDD,EAAA,CAAAuC,UAAA,4BAAAQ,mFAAAf,MAAA;YAAAhC,EAAA,CAAAyC,aAAA,CAAAC,GAAA;YAAA,OAAA1C,EAAA,CAAA2C,WAAA,CAAkBL,GAAA,CAAAP,6BAAA,CAAAC,MAAA,CAAqC;UAAA,EAAC;UAC7EhC,EAAA,CAAAgD,UAAA,IAAAC,oDAAA,uBAA4E;;UAG9EjD,EAAA,CAAAG,YAAA,EAAkB;;;;UAPwEH,EAAA,CAAAI,UAAA,mBAAA8C,OAAA,CAAuB;UAC/GlD,EAAA,CAAAmD,gBAAA,YAAAb,GAAA,CAAAtB,QAAA,CAAsB;UAGQhB,EAAA,CAAAM,SAAA,GAA2B;UAA3BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoD,WAAA,OAAAd,GAAA,CAAAL,gBAAA,EAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}