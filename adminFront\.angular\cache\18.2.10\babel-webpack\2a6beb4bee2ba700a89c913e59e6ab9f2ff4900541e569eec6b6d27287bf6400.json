{"ast": null, "code": "//! moment.js locale configuration\n//! locale : si<PERSON><PERSON> [ss]\n//! author : <PERSON><PERSON><<EMAIL>> : https://github.com/nicolaidavies\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ss = moment.defineLocale('ss', {\n    months: \"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni\".split('_'),\n    monthsShort: 'Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo'.split('_'),\n    weekdays: 'Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo'.split('_'),\n    weekdaysShort: 'Lis_Umb_Lsb_Les_Lsi_Lsh_Umg'.split('_'),\n    weekdaysMin: 'Li_Us_Lb_Lt_Ls_Lh_Ug'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'h:mm A',\n      LTS: 'h:mm:ss A',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY h:mm A',\n      LLLL: 'dddd, D MMMM YYYY h:mm A'\n    },\n    calendar: {\n      sameDay: '[Namuhla nga] LT',\n      nextDay: '[Kusasa nga] LT',\n      nextWeek: 'dddd [nga] LT',\n      lastDay: '[Itolo nga] LT',\n      lastWeek: 'dddd [leliphelile] [nga] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'nga %s',\n      past: 'wenteka nga %s',\n      s: 'emizuzwana lomcane',\n      ss: '%d mzuzwana',\n      m: 'umzuzu',\n      mm: '%d emizuzu',\n      h: 'lihora',\n      hh: '%d emahora',\n      d: 'lilanga',\n      dd: '%d emalanga',\n      M: 'inyanga',\n      MM: '%d tinyanga',\n      y: 'umnyaka',\n      yy: '%d iminyaka'\n    },\n    meridiemParse: /ekuseni|emini|entsambama|ebusuku/,\n    meridiem: function (hours, minutes, isLower) {\n      if (hours < 11) {\n        return 'ekuseni';\n      } else if (hours < 15) {\n        return 'emini';\n      } else if (hours < 19) {\n        return 'entsambama';\n      } else {\n        return 'ebusuku';\n      }\n    },\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'ekuseni') {\n        return hour;\n      } else if (meridiem === 'emini') {\n        return hour >= 11 ? hour : hour + 12;\n      } else if (meridiem === 'entsambama' || meridiem === 'ebusuku') {\n        if (hour === 0) {\n          return 0;\n        }\n        return hour + 12;\n      }\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}/,\n    ordinal: '%d',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return ss;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}