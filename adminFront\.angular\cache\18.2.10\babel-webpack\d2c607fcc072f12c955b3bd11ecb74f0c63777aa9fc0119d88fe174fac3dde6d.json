{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-json';\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-plain';\nimport { apiSpecialChangeGetApproveWaitingListPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-json';\nimport { apiSpecialChangeGetApproveWaitingListPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-plain';\nimport { apiSpecialChangeGetListSpecialChangePost$Json } from '../fn/special-change/api-special-change-get-list-special-change-post-json';\nimport { apiSpecialChangeGetListSpecialChangePost$Plain } from '../fn/special-change/api-special-change-get-list-special-change-post-plain';\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Json } from '../fn/special-change/api-special-change-get-special-change-by-id-post-json';\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Plain } from '../fn/special-change/api-special-change-get-special-change-by-id-post-plain';\nimport { apiSpecialChangeGetSpecialChangeFilePost$Json } from '../fn/special-change/api-special-change-get-special-change-file-post-json';\nimport { apiSpecialChangeGetSpecialChangeFilePost$Plain } from '../fn/special-change/api-special-change-get-special-change-file-post-plain';\nimport { apiSpecialChangeSaveSpecialChangePost$Json } from '../fn/special-change/api-special-change-save-special-change-post-json';\nimport { apiSpecialChangeSaveSpecialChangePost$Plain } from '../fn/special-change/api-special-change-save-special-change-post-plain';\nimport { apiSpecialChangeUpdateApproveWaitingPost$Json } from '../fn/special-change/api-special-change-update-approve-waiting-post-json';\nimport { apiSpecialChangeUpdateApproveWaitingPost$Plain } from '../fn/special-change/api-special-change-update-approve-waiting-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class SpecialChangeService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiSpecialChangeGetSpecialChangeFilePost()` */\n  static {\n    this.ApiSpecialChangeGetSpecialChangeFilePostPath = '/api/SpecialChange/GetSpecialChangeFile';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params, context) {\n    return apiSpecialChangeGetSpecialChangeFilePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeFilePost$Plain(params, context) {\n    return this.apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params, context) {\n    return apiSpecialChangeGetSpecialChangeFilePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeFilePost$Json(params, context) {\n    return this.apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeGetListSpecialChangePost()` */\n  static {\n    this.ApiSpecialChangeGetListSpecialChangePostPath = '/api/SpecialChange/GetListSpecialChange';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetListSpecialChangePost$Plain$Response(params, context) {\n    return apiSpecialChangeGetListSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetListSpecialChangePost$Plain(params, context) {\n    return this.apiSpecialChangeGetListSpecialChangePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetListSpecialChangePost$Json$Response(params, context) {\n    return apiSpecialChangeGetListSpecialChangePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetListSpecialChangePost$Json(params, context) {\n    return this.apiSpecialChangeGetListSpecialChangePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeGetSpecialChangeByIdPost()` */\n  static {\n    this.ApiSpecialChangeGetSpecialChangeByIdPostPath = '/api/SpecialChange/GetSpecialChangeByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params, context) {\n    return apiSpecialChangeGetSpecialChangeByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeByIdPost$Plain(params, context) {\n    return this.apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params, context) {\n    return apiSpecialChangeGetSpecialChangeByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetSpecialChangeByIdPost$Json(params, context) {\n    return this.apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeSaveSpecialChangePost()` */\n  static {\n    this.ApiSpecialChangeSaveSpecialChangePostPath = '/api/SpecialChange/SaveSpecialChange';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeSaveSpecialChangePost$Plain$Response(params, context) {\n    return apiSpecialChangeSaveSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeSaveSpecialChangePost$Plain(params, context) {\n    return this.apiSpecialChangeSaveSpecialChangePost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeSaveSpecialChangePost$Json$Response(params, context) {\n    return apiSpecialChangeSaveSpecialChangePost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeSaveSpecialChangePost$Json(params, context) {\n    return this.apiSpecialChangeSaveSpecialChangePost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeGetApproveWaitingListPost()` */\n  static {\n    this.ApiSpecialChangeGetApproveWaitingListPostPath = '/api/SpecialChange/GetApproveWaitingList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params, context) {\n    return apiSpecialChangeGetApproveWaitingListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingListPost$Plain(params, context) {\n    return this.apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingListPost$Json$Response(params, context) {\n    return apiSpecialChangeGetApproveWaitingListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingListPost$Json(params, context) {\n    return this.apiSpecialChangeGetApproveWaitingListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeGetApproveWaitingByIdPost()` */\n  static {\n    this.ApiSpecialChangeGetApproveWaitingByIdPostPath = '/api/SpecialChange/GetApproveWaitingByID';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params, context) {\n    return apiSpecialChangeGetApproveWaitingByIdPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingByIdPost$Plain(params, context) {\n    return this.apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params, context) {\n    return apiSpecialChangeGetApproveWaitingByIdPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeGetApproveWaitingByIdPost$Json(params, context) {\n    return this.apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiSpecialChangeUpdateApproveWaitingPost()` */\n  static {\n    this.ApiSpecialChangeUpdateApproveWaitingPostPath = '/api/SpecialChange/UpdateApproveWaiting';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params, context) {\n    return apiSpecialChangeUpdateApproveWaitingPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeUpdateApproveWaitingPost$Plain(params, context) {\n    return this.apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params, context) {\n    return apiSpecialChangeUpdateApproveWaitingPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiSpecialChangeUpdateApproveWaitingPost$Json(params, context) {\n    return this.apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function SpecialChangeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SpecialChangeService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: SpecialChangeService,\n      factory: SpecialChangeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "apiSpecialChangeGetApproveWaitingByIdPost$Plain", "apiSpecialChangeGetApproveWaitingListPost$Json", "apiSpecialChangeGetApproveWaitingListPost$Plain", "apiSpecialChangeGetListSpecialChangePost$Json", "apiSpecialChangeGetListSpecialChangePost$Plain", "apiSpecialChangeGetSpecialChangeByIdPost$Json", "apiSpecialChangeGetSpecialChangeByIdPost$Plain", "apiSpecialChangeGetSpecialChangeFilePost$Json", "apiSpecialChangeGetSpecialChangeFilePost$Plain", "apiSpecialChangeSaveSpecialChangePost$Json", "apiSpecialChangeSaveSpecialChangePost$Plain", "apiSpecialChangeUpdateApproveWaitingPost$Json", "apiSpecialChangeUpdateApproveWaitingPost$Plain", "SpecialChangeService", "constructor", "config", "http", "ApiSpecialChangeGetSpecialChangeFilePostPath", "apiSpecialChangeGetSpecialChangeFilePost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiSpecialChangeGetSpecialChangeFilePost$Json$Response", "ApiSpecialChangeGetListSpecialChangePostPath", "apiSpecialChangeGetListSpecialChangePost$Plain$Response", "apiSpecialChangeGetListSpecialChangePost$Json$Response", "ApiSpecialChangeGetSpecialChangeByIdPostPath", "apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response", "apiSpecialChangeGetSpecialChangeByIdPost$Json$Response", "ApiSpecialChangeSaveSpecialChangePostPath", "apiSpecialChangeSaveSpecialChangePost$Plain$Response", "apiSpecialChangeSaveSpecialChangePost$Json$Response", "ApiSpecialChangeGetApproveWaitingListPostPath", "apiSpecialChangeGetApproveWaitingListPost$Plain$Response", "apiSpecialChangeGetApproveWaitingListPost$Json$Response", "ApiSpecialChangeGetApproveWaitingByIdPostPath", "apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response", "apiSpecialChangeGetApproveWaitingByIdPost$Json$Response", "ApiSpecialChangeUpdateApproveWaitingPostPath", "apiSpecialChangeUpdateApproveWaitingPost$Plain$Response", "apiSpecialChangeUpdateApproveWaitingPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\services\\special-change.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-json';\r\nimport { ApiSpecialChangeGetApproveWaitingByIdPost$Json$Params } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-json';\r\nimport { apiSpecialChangeGetApproveWaitingByIdPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-plain';\r\nimport { ApiSpecialChangeGetApproveWaitingByIdPost$Plain$Params } from '../fn/special-change/api-special-change-get-approve-waiting-by-id-post-plain';\r\nimport { apiSpecialChangeGetApproveWaitingListPost$Json } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-json';\r\nimport { ApiSpecialChangeGetApproveWaitingListPost$Json$Params } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-json';\r\nimport { apiSpecialChangeGetApproveWaitingListPost$Plain } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-plain';\r\nimport { ApiSpecialChangeGetApproveWaitingListPost$Plain$Params } from '../fn/special-change/api-special-change-get-approve-waiting-list-post-plain';\r\nimport { apiSpecialChangeGetListSpecialChangePost$Json } from '../fn/special-change/api-special-change-get-list-special-change-post-json';\r\nimport { ApiSpecialChangeGetListSpecialChangePost$Json$Params } from '../fn/special-change/api-special-change-get-list-special-change-post-json';\r\nimport { apiSpecialChangeGetListSpecialChangePost$Plain } from '../fn/special-change/api-special-change-get-list-special-change-post-plain';\r\nimport { ApiSpecialChangeGetListSpecialChangePost$Plain$Params } from '../fn/special-change/api-special-change-get-list-special-change-post-plain';\r\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Json } from '../fn/special-change/api-special-change-get-special-change-by-id-post-json';\r\nimport { ApiSpecialChangeGetSpecialChangeByIdPost$Json$Params } from '../fn/special-change/api-special-change-get-special-change-by-id-post-json';\r\nimport { apiSpecialChangeGetSpecialChangeByIdPost$Plain } from '../fn/special-change/api-special-change-get-special-change-by-id-post-plain';\r\nimport { ApiSpecialChangeGetSpecialChangeByIdPost$Plain$Params } from '../fn/special-change/api-special-change-get-special-change-by-id-post-plain';\r\nimport { apiSpecialChangeGetSpecialChangeFilePost$Json } from '../fn/special-change/api-special-change-get-special-change-file-post-json';\r\nimport { ApiSpecialChangeGetSpecialChangeFilePost$Json$Params } from '../fn/special-change/api-special-change-get-special-change-file-post-json';\r\nimport { apiSpecialChangeGetSpecialChangeFilePost$Plain } from '../fn/special-change/api-special-change-get-special-change-file-post-plain';\r\nimport { ApiSpecialChangeGetSpecialChangeFilePost$Plain$Params } from '../fn/special-change/api-special-change-get-special-change-file-post-plain';\r\nimport { apiSpecialChangeSaveSpecialChangePost$Json } from '../fn/special-change/api-special-change-save-special-change-post-json';\r\nimport { ApiSpecialChangeSaveSpecialChangePost$Json$Params } from '../fn/special-change/api-special-change-save-special-change-post-json';\r\nimport { apiSpecialChangeSaveSpecialChangePost$Plain } from '../fn/special-change/api-special-change-save-special-change-post-plain';\r\nimport { ApiSpecialChangeSaveSpecialChangePost$Plain$Params } from '../fn/special-change/api-special-change-save-special-change-post-plain';\r\nimport { apiSpecialChangeUpdateApproveWaitingPost$Json } from '../fn/special-change/api-special-change-update-approve-waiting-post-json';\r\nimport { ApiSpecialChangeUpdateApproveWaitingPost$Json$Params } from '../fn/special-change/api-special-change-update-approve-waiting-post-json';\r\nimport { apiSpecialChangeUpdateApproveWaitingPost$Plain } from '../fn/special-change/api-special-change-update-approve-waiting-post-plain';\r\nimport { ApiSpecialChangeUpdateApproveWaitingPost$Plain$Params } from '../fn/special-change/api-special-change-update-approve-waiting-post-plain';\r\nimport { ApproveWaitingByIdResResponseBase } from '../models/approve-waiting-by-id-res-response-base';\r\nimport { ApproveWaitingResListResponseBase } from '../models/approve-waiting-res-list-response-base';\r\nimport { SpecialChangeFileGroupListResponseBase } from '../models/special-change-file-group-list-response-base';\r\nimport { SpecialChangeResListResponseBase } from '../models/special-change-res-list-response-base';\r\nimport { SpecialChangeResResponseBase } from '../models/special-change-res-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class SpecialChangeService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeGetSpecialChangeFilePost()` */\r\n  static readonly ApiSpecialChangeGetSpecialChangeFilePostPath = '/api/SpecialChange/GetSpecialChangeFile';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params?: ApiSpecialChangeGetSpecialChangeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeFileGroupListResponseBase>> {\r\n    return apiSpecialChangeGetSpecialChangeFilePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeFilePost$Plain(params?: ApiSpecialChangeGetSpecialChangeFilePost$Plain$Params, context?: HttpContext): Observable<SpecialChangeFileGroupListResponseBase> {\r\n    return this.apiSpecialChangeGetSpecialChangeFilePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeFileGroupListResponseBase>): SpecialChangeFileGroupListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeFilePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params?: ApiSpecialChangeGetSpecialChangeFilePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeFileGroupListResponseBase>> {\r\n    return apiSpecialChangeGetSpecialChangeFilePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeFilePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeFilePost$Json(params?: ApiSpecialChangeGetSpecialChangeFilePost$Json$Params, context?: HttpContext): Observable<SpecialChangeFileGroupListResponseBase> {\r\n    return this.apiSpecialChangeGetSpecialChangeFilePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeFileGroupListResponseBase>): SpecialChangeFileGroupListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeGetListSpecialChangePost()` */\r\n  static readonly ApiSpecialChangeGetListSpecialChangePostPath = '/api/SpecialChange/GetListSpecialChange';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetListSpecialChangePost$Plain$Response(params?: ApiSpecialChangeGetListSpecialChangePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeResListResponseBase>> {\r\n    return apiSpecialChangeGetListSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetListSpecialChangePost$Plain(params?: ApiSpecialChangeGetListSpecialChangePost$Plain$Params, context?: HttpContext): Observable<SpecialChangeResListResponseBase> {\r\n    return this.apiSpecialChangeGetListSpecialChangePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeResListResponseBase>): SpecialChangeResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetListSpecialChangePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetListSpecialChangePost$Json$Response(params?: ApiSpecialChangeGetListSpecialChangePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeResListResponseBase>> {\r\n    return apiSpecialChangeGetListSpecialChangePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetListSpecialChangePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetListSpecialChangePost$Json(params?: ApiSpecialChangeGetListSpecialChangePost$Json$Params, context?: HttpContext): Observable<SpecialChangeResListResponseBase> {\r\n    return this.apiSpecialChangeGetListSpecialChangePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeResListResponseBase>): SpecialChangeResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeGetSpecialChangeByIdPost()` */\r\n  static readonly ApiSpecialChangeGetSpecialChangeByIdPostPath = '/api/SpecialChange/GetSpecialChangeByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params?: ApiSpecialChangeGetSpecialChangeByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeResResponseBase>> {\r\n    return apiSpecialChangeGetSpecialChangeByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeByIdPost$Plain(params?: ApiSpecialChangeGetSpecialChangeByIdPost$Plain$Params, context?: HttpContext): Observable<SpecialChangeResResponseBase> {\r\n    return this.apiSpecialChangeGetSpecialChangeByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeResResponseBase>): SpecialChangeResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetSpecialChangeByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params?: ApiSpecialChangeGetSpecialChangeByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeResResponseBase>> {\r\n    return apiSpecialChangeGetSpecialChangeByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetSpecialChangeByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetSpecialChangeByIdPost$Json(params?: ApiSpecialChangeGetSpecialChangeByIdPost$Json$Params, context?: HttpContext): Observable<SpecialChangeResResponseBase> {\r\n    return this.apiSpecialChangeGetSpecialChangeByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<SpecialChangeResResponseBase>): SpecialChangeResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeSaveSpecialChangePost()` */\r\n  static readonly ApiSpecialChangeSaveSpecialChangePostPath = '/api/SpecialChange/SaveSpecialChange';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeSaveSpecialChangePost$Plain$Response(params?: ApiSpecialChangeSaveSpecialChangePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialChangeSaveSpecialChangePost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeSaveSpecialChangePost$Plain(params?: ApiSpecialChangeSaveSpecialChangePost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialChangeSaveSpecialChangePost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeSaveSpecialChangePost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeSaveSpecialChangePost$Json$Response(params?: ApiSpecialChangeSaveSpecialChangePost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialChangeSaveSpecialChangePost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeSaveSpecialChangePost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeSaveSpecialChangePost$Json(params?: ApiSpecialChangeSaveSpecialChangePost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialChangeSaveSpecialChangePost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeGetApproveWaitingListPost()` */\r\n  static readonly ApiSpecialChangeGetApproveWaitingListPostPath = '/api/SpecialChange/GetApproveWaitingList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params?: ApiSpecialChangeGetApproveWaitingListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ApproveWaitingResListResponseBase>> {\r\n    return apiSpecialChangeGetApproveWaitingListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingListPost$Plain(params?: ApiSpecialChangeGetApproveWaitingListPost$Plain$Params, context?: HttpContext): Observable<ApproveWaitingResListResponseBase> {\r\n    return this.apiSpecialChangeGetApproveWaitingListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ApproveWaitingResListResponseBase>): ApproveWaitingResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingListPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingListPost$Json$Response(params?: ApiSpecialChangeGetApproveWaitingListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ApproveWaitingResListResponseBase>> {\r\n    return apiSpecialChangeGetApproveWaitingListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingListPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingListPost$Json(params?: ApiSpecialChangeGetApproveWaitingListPost$Json$Params, context?: HttpContext): Observable<ApproveWaitingResListResponseBase> {\r\n    return this.apiSpecialChangeGetApproveWaitingListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ApproveWaitingResListResponseBase>): ApproveWaitingResListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeGetApproveWaitingByIdPost()` */\r\n  static readonly ApiSpecialChangeGetApproveWaitingByIdPostPath = '/api/SpecialChange/GetApproveWaitingByID';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params?: ApiSpecialChangeGetApproveWaitingByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ApproveWaitingByIdResResponseBase>> {\r\n    return apiSpecialChangeGetApproveWaitingByIdPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingByIdPost$Plain(params?: ApiSpecialChangeGetApproveWaitingByIdPost$Plain$Params, context?: HttpContext): Observable<ApproveWaitingByIdResResponseBase> {\r\n    return this.apiSpecialChangeGetApproveWaitingByIdPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ApproveWaitingByIdResResponseBase>): ApproveWaitingByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeGetApproveWaitingByIdPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params?: ApiSpecialChangeGetApproveWaitingByIdPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<ApproveWaitingByIdResResponseBase>> {\r\n    return apiSpecialChangeGetApproveWaitingByIdPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeGetApproveWaitingByIdPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeGetApproveWaitingByIdPost$Json(params?: ApiSpecialChangeGetApproveWaitingByIdPost$Json$Params, context?: HttpContext): Observable<ApproveWaitingByIdResResponseBase> {\r\n    return this.apiSpecialChangeGetApproveWaitingByIdPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<ApproveWaitingByIdResResponseBase>): ApproveWaitingByIdResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiSpecialChangeUpdateApproveWaitingPost()` */\r\n  static readonly ApiSpecialChangeUpdateApproveWaitingPostPath = '/api/SpecialChange/UpdateApproveWaiting';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params?: ApiSpecialChangeUpdateApproveWaitingPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialChangeUpdateApproveWaitingPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeUpdateApproveWaitingPost$Plain(params?: ApiSpecialChangeUpdateApproveWaitingPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialChangeUpdateApproveWaitingPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiSpecialChangeUpdateApproveWaitingPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params?: ApiSpecialChangeUpdateApproveWaitingPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiSpecialChangeUpdateApproveWaitingPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiSpecialChangeUpdateApproveWaitingPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiSpecialChangeUpdateApproveWaitingPost$Json(params?: ApiSpecialChangeUpdateApproveWaitingPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiSpecialChangeUpdateApproveWaitingPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,8CAA8C,QAAQ,6EAA6E;AAE5I,SAASC,+CAA+C,QAAQ,8EAA8E;AAE9I,SAASC,8CAA8C,QAAQ,4EAA4E;AAE3I,SAASC,+CAA+C,QAAQ,6EAA6E;AAE7I,SAASC,6CAA6C,QAAQ,2EAA2E;AAEzI,SAASC,8CAA8C,QAAQ,4EAA4E;AAE3I,SAASC,6CAA6C,QAAQ,4EAA4E;AAE1I,SAASC,8CAA8C,QAAQ,6EAA6E;AAE5I,SAASC,6CAA6C,QAAQ,2EAA2E;AAEzI,SAASC,8CAA8C,QAAQ,4EAA4E;AAE3I,SAASC,0CAA0C,QAAQ,uEAAuE;AAElI,SAASC,2CAA2C,QAAQ,wEAAwE;AAEpI,SAASC,6CAA6C,QAAQ,0EAA0E;AAExI,SAASC,8CAA8C,QAAQ,2EAA2E;;;;AAU1I,OAAM,MAAOC,oBAAqB,SAAQf,WAAW;EACnDgB,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACC,MAA8D,EAAEC,OAAqB;IAC3I,OAAOZ,8CAA8C,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAZ,8CAA8CA,CAACW,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACF,uDAAuD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;;;;;EAMAC,sDAAsDA,CAACN,MAA6D,EAAEC,OAAqB;IACzI,OAAOb,6CAA6C,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAb,6CAA6CA,CAACY,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACK,sDAAsD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFzB,GAAG,CAAE0B,CAA6D,IAA6CA,CAAC,CAACC,IAAI,CAAC,CACvH;EACH;EAEA;;IACgB,KAAAE,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACR,MAA8D,EAAEC,OAAqB;IAC3I,OAAOhB,8CAA8C,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAhB,8CAA8CA,CAACe,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACO,uDAAuD,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;;;;;EAMAI,sDAAsDA,CAACT,MAA6D,EAAEC,OAAqB;IACzI,OAAOjB,6CAA6C,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAjB,6CAA6CA,CAACgB,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACQ,sDAAsD,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFzB,GAAG,CAAE0B,CAAuD,IAAuCA,CAAC,CAACC,IAAI,CAAC,CAC3G;EACH;EAEA;;IACgB,KAAAK,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACX,MAA8D,EAAEC,OAAqB;IAC3I,OAAOd,8CAA8C,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAd,8CAA8CA,CAACa,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACU,uDAAuD,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;;;;;EAMAO,sDAAsDA,CAACZ,MAA6D,EAAEC,OAAqB;IACzI,OAAOf,6CAA6C,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAf,6CAA6CA,CAACc,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACW,sDAAsD,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFzB,GAAG,CAAE0B,CAAmD,IAAmCA,CAAC,CAACC,IAAI,CAAC,CACnG;EACH;EAEA;;IACgB,KAAAQ,yCAAyC,GAAG,sCAAsC;EAAC;EAEnG;;;;;;EAMAC,oDAAoDA,CAACd,MAA2D,EAAEC,OAAqB;IACrI,OAAOV,2CAA2C,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9F;EAEA;;;;;;EAMAV,2CAA2CA,CAACS,MAA2D,EAAEC,OAAqB;IAC5H,OAAO,IAAI,CAACa,oDAAoD,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpFzB,GAAG,CAAE0B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,mDAAmDA,CAACf,MAA0D,EAAEC,OAAqB;IACnI,OAAOX,0CAA0C,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAX,0CAA0CA,CAACU,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACc,mDAAmD,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFzB,GAAG,CAAE0B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,6CAA6C,GAAG,0CAA0C;EAAC;EAE3G;;;;;;EAMAC,wDAAwDA,CAACjB,MAA+D,EAAEC,OAAqB;IAC7I,OAAOlB,+CAA+C,CAAC,IAAI,CAACc,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClG;EAEA;;;;;;EAMAlB,+CAA+CA,CAACiB,MAA+D,EAAEC,OAAqB;IACpI,OAAO,IAAI,CAACgB,wDAAwD,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxFzB,GAAG,CAAE0B,CAAwD,IAAwCA,CAAC,CAACC,IAAI,CAAC,CAC7G;EACH;EAEA;;;;;;EAMAa,uDAAuDA,CAAClB,MAA8D,EAAEC,OAAqB;IAC3I,OAAOnB,8CAA8C,CAAC,IAAI,CAACe,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAnB,8CAA8CA,CAACkB,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACiB,uDAAuD,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAAwD,IAAwCA,CAAC,CAACC,IAAI,CAAC,CAC7G;EACH;EAEA;;IACgB,KAAAc,6CAA6C,GAAG,0CAA0C;EAAC;EAE3G;;;;;;EAMAC,wDAAwDA,CAACpB,MAA+D,EAAEC,OAAqB;IAC7I,OAAOpB,+CAA+C,CAAC,IAAI,CAACgB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAClG;EAEA;;;;;;EAMApB,+CAA+CA,CAACmB,MAA+D,EAAEC,OAAqB;IACpI,OAAO,IAAI,CAACmB,wDAAwD,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACxFzB,GAAG,CAAE0B,CAAwD,IAAwCA,CAAC,CAACC,IAAI,CAAC,CAC7G;EACH;EAEA;;;;;;EAMAgB,uDAAuDA,CAACrB,MAA8D,EAAEC,OAAqB;IAC3I,OAAOrB,8CAA8C,CAAC,IAAI,CAACiB,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMArB,8CAA8CA,CAACoB,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACoB,uDAAuD,CAACrB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAAwD,IAAwCA,CAAC,CAACC,IAAI,CAAC,CAC7G;EACH;EAEA;;IACgB,KAAAiB,4CAA4C,GAAG,yCAAyC;EAAC;EAEzG;;;;;;EAMAC,uDAAuDA,CAACvB,MAA8D,EAAEC,OAAqB;IAC3I,OAAOR,8CAA8C,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACjG;EAEA;;;;;;EAMAR,8CAA8CA,CAACO,MAA8D,EAAEC,OAAqB;IAClI,OAAO,IAAI,CAACsB,uDAAuD,CAACvB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACvFzB,GAAG,CAAE0B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAmB,sDAAsDA,CAACxB,MAA6D,EAAEC,OAAqB;IACzI,OAAOT,6CAA6C,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAChG;EAEA;;;;;;EAMAT,6CAA6CA,CAACQ,MAA6D,EAAEC,OAAqB;IAChI,OAAO,IAAI,CAACuB,sDAAsD,CAACxB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACtFzB,GAAG,CAAE0B,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA5UWX,oBAAoB,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBpC,oBAAoB;MAAAqC,OAAA,EAApBrC,oBAAoB,CAAAsC,IAAA;MAAAC,UAAA,EADP;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}