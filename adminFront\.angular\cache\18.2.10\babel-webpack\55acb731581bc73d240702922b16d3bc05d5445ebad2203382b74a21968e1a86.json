{"ast": null, "code": "// styles are inspired by `react-error-overlay`\n\nvar msgStyles = {\n  error: {\n    backgroundColor: \"rgba(206, 17, 38, 0.1)\",\n    color: \"#fccfcf\"\n  },\n  warning: {\n    backgroundColor: \"rgba(251, 245, 180, 0.1)\",\n    color: \"#fbf5b4\"\n  }\n};\nvar iframeStyle = {\n  position: \"fixed\",\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  border: \"none\",\n  \"z-index\": 9999999999\n};\nvar containerStyle = {\n  position: \"fixed\",\n  boxSizing: \"border-box\",\n  left: 0,\n  top: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  fontSize: \"large\",\n  padding: \"2rem 2rem 4rem 2rem\",\n  lineHeight: \"1.2\",\n  whiteSpace: \"pre-wrap\",\n  overflow: \"auto\",\n  backgroundColor: \"rgba(0, 0, 0, 0.9)\",\n  color: \"white\"\n};\nvar headerStyle = {\n  color: \"#e83b46\",\n  fontSize: \"2em\",\n  whiteSpace: \"pre-wrap\",\n  fontFamily: \"sans-serif\",\n  margin: \"0 2rem 2rem 0\",\n  flex: \"0 0 auto\",\n  maxHeight: \"50%\",\n  overflow: \"auto\"\n};\nvar dismissButtonStyle = {\n  color: \"#ffffff\",\n  lineHeight: \"1rem\",\n  fontSize: \"1.5rem\",\n  padding: \"1rem\",\n  cursor: \"pointer\",\n  position: \"absolute\",\n  right: 0,\n  top: 0,\n  backgroundColor: \"transparent\",\n  border: \"none\"\n};\nvar msgTypeStyle = {\n  color: \"#e83b46\",\n  fontSize: \"1.2em\",\n  marginBottom: \"1rem\",\n  fontFamily: \"sans-serif\"\n};\nvar msgTextStyle = {\n  lineHeight: \"1.5\",\n  fontSize: \"1rem\",\n  fontFamily: \"Menlo, Consolas, monospace\"\n};\nexport { msgStyles, iframeStyle, containerStyle, headerStyle, dismissButtonStyle, msgTypeStyle, msgTextStyle };", "map": {"version": 3, "names": ["msgStyles", "error", "backgroundColor", "color", "warning", "iframeStyle", "position", "top", "left", "right", "bottom", "width", "height", "border", "containerStyle", "boxSizing", "fontSize", "padding", "lineHeight", "whiteSpace", "overflow", "headerStyle", "fontFamily", "margin", "flex", "maxHeight", "dismissButtonStyle", "cursor", "msgTypeStyle", "marginBottom", "msgTextStyle"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/webpack-dev-server/client/overlay/styles.js"], "sourcesContent": ["// styles are inspired by `react-error-overlay`\n\nvar msgStyles = {\n  error: {\n    backgroundColor: \"rgba(206, 17, 38, 0.1)\",\n    color: \"#fccfcf\"\n  },\n  warning: {\n    backgroundColor: \"rgba(251, 245, 180, 0.1)\",\n    color: \"#fbf5b4\"\n  }\n};\nvar iframeStyle = {\n  position: \"fixed\",\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  border: \"none\",\n  \"z-index\": 9999999999\n};\nvar containerStyle = {\n  position: \"fixed\",\n  boxSizing: \"border-box\",\n  left: 0,\n  top: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  fontSize: \"large\",\n  padding: \"2rem 2rem 4rem 2rem\",\n  lineHeight: \"1.2\",\n  whiteSpace: \"pre-wrap\",\n  overflow: \"auto\",\n  backgroundColor: \"rgba(0, 0, 0, 0.9)\",\n  color: \"white\"\n};\nvar headerStyle = {\n  color: \"#e83b46\",\n  fontSize: \"2em\",\n  whiteSpace: \"pre-wrap\",\n  fontFamily: \"sans-serif\",\n  margin: \"0 2rem 2rem 0\",\n  flex: \"0 0 auto\",\n  maxHeight: \"50%\",\n  overflow: \"auto\"\n};\nvar dismissButtonStyle = {\n  color: \"#ffffff\",\n  lineHeight: \"1rem\",\n  fontSize: \"1.5rem\",\n  padding: \"1rem\",\n  cursor: \"pointer\",\n  position: \"absolute\",\n  right: 0,\n  top: 0,\n  backgroundColor: \"transparent\",\n  border: \"none\"\n};\nvar msgTypeStyle = {\n  color: \"#e83b46\",\n  fontSize: \"1.2em\",\n  marginBottom: \"1rem\",\n  fontFamily: \"sans-serif\"\n};\nvar msgTextStyle = {\n  lineHeight: \"1.5\",\n  fontSize: \"1rem\",\n  fontFamily: \"Menlo, Consolas, monospace\"\n};\nexport { msgStyles, iframeStyle, containerStyle, headerStyle, dismissButtonStyle, msgTypeStyle, msgTextStyle };"], "mappings": "AAAA;;AAEA,IAAIA,SAAS,GAAG;EACdC,KAAK,EAAE;IACLC,eAAe,EAAE,wBAAwB;IACzCC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE;IACPF,eAAe,EAAE,0BAA0B;IAC3CC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,MAAM;EACd,SAAS,EAAE;AACb,CAAC;AACD,IAAIC,cAAc,GAAG;EACnBR,QAAQ,EAAE,OAAO;EACjBS,SAAS,EAAE,YAAY;EACvBP,IAAI,EAAE,CAAC;EACPD,GAAG,EAAE,CAAC;EACNE,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,OAAO;EACfI,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,qBAAqB;EAC9BC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,UAAU;EACtBC,QAAQ,EAAE,MAAM;EAChBlB,eAAe,EAAE,oBAAoB;EACrCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIkB,WAAW,GAAG;EAChBlB,KAAK,EAAE,SAAS;EAChBa,QAAQ,EAAE,KAAK;EACfG,UAAU,EAAE,UAAU;EACtBG,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE,KAAK;EAChBL,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIM,kBAAkB,GAAG;EACvBvB,KAAK,EAAE,SAAS;EAChBe,UAAU,EAAE,MAAM;EAClBF,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,MAAM;EACfU,MAAM,EAAE,SAAS;EACjBrB,QAAQ,EAAE,UAAU;EACpBG,KAAK,EAAE,CAAC;EACRF,GAAG,EAAE,CAAC;EACNL,eAAe,EAAE,aAAa;EAC9BW,MAAM,EAAE;AACV,CAAC;AACD,IAAIe,YAAY,GAAG;EACjBzB,KAAK,EAAE,SAAS;EAChBa,QAAQ,EAAE,OAAO;EACjBa,YAAY,EAAE,MAAM;EACpBP,UAAU,EAAE;AACd,CAAC;AACD,IAAIQ,YAAY,GAAG;EACjBZ,UAAU,EAAE,KAAK;EACjBF,QAAQ,EAAE,MAAM;EAChBM,UAAU,EAAE;AACd,CAAC;AACD,SAAStB,SAAS,EAAEK,WAAW,EAAES,cAAc,EAAEO,WAAW,EAAEK,kBAAkB,EAAEE,YAAY,EAAEE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}