{"ast": null, "code": "import endOfDay from \"../endOfDay/index.js\";\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport default function endOfToday() {\n  return endOfDay(Date.now());\n}", "map": {"version": 3, "names": ["endOfDay", "endOfToday", "Date", "now"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/endOfToday/index.js"], "sourcesContent": ["import endOfDay from \"../endOfDay/index.js\";\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport default function endOfToday() {\n  return endOfDay(Date.now());\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,OAAOD,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}