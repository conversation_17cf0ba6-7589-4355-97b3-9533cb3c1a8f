{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\nlet DetailApprovalWaitingComponent = class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.fileService = fileService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n      }\n    })).subscribe();\n  }\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\n  handleFileClick(file) {\n    const fileName = file.CFileName || '';\n    const fileData = file.CFile || '';\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(fileName);\n    const isPdfByName = this.isPDFString(fileName);\n    const isCadByName = this.isCadString(fileName);\n    if (isImageByName) {\n      this.openImagePreview(fileData, fileName);\n    } else if (isPdfByName) {\n      this.openPdfInBrowser(fileData, fileName);\n    } else {\n      this.downloadFileDirectly(fileData, fileName);\n    }\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    if (!fileName) return false;\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 判斷是否為 PDF\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  // 判斷是否為 CAD 檔案\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  // 取得檔案類型圖標\n  getFileIcon(fileName) {\n    if (this.isImageFile(fileName)) {\n      return 'fa fa-image text-green-500';\n    } else if (this.isPDFString(fileName)) {\n      return 'fa fa-file-pdf text-red-500';\n    } else if (this.isCadString(fileName)) {\n      return 'fa fa-cube text-blue-500';\n    } else {\n      return 'fa fa-file text-gray-500';\n    }\n  }\n  // 取得檔案類型文字\n  getFileTypeText(fileName) {\n    if (this.isImageFile(fileName)) {\n      return '圖片';\n    } else if (this.isPDFString(fileName)) {\n      return 'PDF';\n    } else if (this.isCadString(fileName)) {\n      return 'CAD';\n    } else {\n      return '檔案';\n    }\n  }\n  // 開啟圖片預覽\n  openImagePreview(fileUrl, fileName) {\n    try {\n      const imageUrl = this.getImageSrc(fileUrl);\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { \n                  margin: 0; \n                  padding: 20px; \n                  background: #f0f0f0; \n                  display: flex; \n                  flex-direction: column; \n                  align-items: center; \n                  font-family: Arial, sans-serif;\n                }\n                .header {\n                  background: white;\n                  padding: 10px 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  margin-bottom: 20px;\n                  font-weight: bold;\n                }\n                .image-container {\n                  background: white;\n                  padding: 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  max-width: 90vw;\n                  max-height: 80vh;\n                  overflow: auto;\n                }\n                img { \n                  max-width: 100%; \n                  height: auto; \n                  display: block; \n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"header\">${fileName}</div>\n              <div class=\"image-container\">\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\n              </div>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.open(imageUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      this.message.showErrorMSG('開啟圖片預覽失敗');\n    }\n  }\n  // 取得正確的圖片 src\n  getImageSrc(fileData) {\n    if (!fileData) return '';\n    // 如果已經是完整的 data URL，直接返回\n    if (fileData.startsWith('data:')) {\n      return fileData;\n    }\n    // 如果是 HTTP URL，直接返回\n    if (fileData.startsWith('http')) {\n      return fileData;\n    }\n    // 如果是純 base64 字串，需要添加前綴\n    return `data:image/jpeg;base64,${fileData}`;\n  }\n  // 在瀏覽器中打開 PDF\n  openPdfInBrowser(fileData, fileName) {\n    try {\n      let pdfUrl = fileData;\n      // 如果是 base64，需要轉換為 blob URL\n      if (!fileData.startsWith('http')) {\n        if (fileData.startsWith('data:application/pdf')) {\n          pdfUrl = fileData;\n        } else {\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\n        }\n      }\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        window.open(pdfUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('打開 PDF 時發生錯誤:', error);\n      this.message.showErrorMSG('打開 PDF 失敗');\n    }\n  }\n  // 直接下載檔案\n  downloadFileDirectly(fileData, fileName) {\n    try {\n      if (fileData.startsWith('http')) {\n        // 如果是 URL，直接開啟下載\n        const link = document.createElement('a');\n        link.href = fileData;\n        link.download = fileName;\n        link.target = '_blank';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      } else {\n        // 如果是 base64，轉換後下載\n        const base64Content = fileData.includes(',') ? fileData.split(',')[1] : fileData;\n        const byteCharacters = atob(base64Content);\n        const byteNumbers = new Array(byteCharacters.length);\n        for (let i = 0; i < byteCharacters.length; i++) {\n          byteNumbers[i] = byteCharacters.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        const blob = new Blob([byteArray], {\n          type: 'application/octet-stream'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = fileName;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        setTimeout(() => URL.revokeObjectURL(url), 1000);\n      }\n    } catch (error) {\n      console.error('下載檔案時發生錯誤:', error);\n      this.message.showErrorMSG('下載檔案失敗');\n    }\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n};\nDetailApprovalWaitingComponent = __decorate([Component({\n  selector: 'app-detail-approval-waiting',\n  standalone: true,\n  imports: [CommonModule, SharedModule, BaseFilePipe, FileUploadComponent],\n  templateUrl: './detail-approval-waiting.component.html',\n  styleUrls: ['./detail-approval-waiting.component.scss']\n})], DetailApprovalWaitingComponent);\nexport { DetailApprovalWaitingComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "BaseFilePipe", "FileUploadComponent", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "fileService", "CType", "remark", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "approvalWaiting", "Entries", "handleFileClick", "file", "fileName", "CFileName", "fileData", "CFile", "isImageByName", "isImageFile", "isPdfByName", "isPDFString", "isCadByName", "isCadString", "openImagePreview", "openPdfInBrowser", "downloadFileDirectly", "imageExtensions", "extension", "split", "pop", "toLowerCase", "includes", "str", "endsWith", "lowerStr", "getFileIcon", "getFileTypeText", "fileUrl", "imageUrl", "getImageSrc", "newWindow", "window", "open", "document", "write", "close", "error", "console", "showErrorMSG", "startsWith", "pdfUrl", "link", "createElement", "href", "download", "target", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "base64Content", "byteCharacters", "atob", "byteNumbers", "Array", "length", "i", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "type", "url", "URL", "createObjectURL", "setTimeout", "revokeObjectURL", "downloadFile", "handleAction", "isApprove", "validation", "errorMessages", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "CIsApprove", "CRemark", "showSucessMSG", "CApproveRecord", "push", "CCreator", "userName", "CRecordDate", "Date", "toISOString", "goBack", "action", "payload", "back", "clear", "required", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\r\nimport { FileService } from 'src/app/shared/services/file.service';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BaseFilePipe,\r\n    FileUploadComponent\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n\r\n  decodeJWT: any\r\n\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private fileService: FileService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\r\n  handleFileClick(file: any) {\r\n    const fileName = file.CFileName || '';\r\n    const fileData = file.CFile || '';\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(fileName);\r\n    const isPdfByName = this.isPDFString(fileName);\r\n    const isCadByName = this.isCadString(fileName);\r\n\r\n    if (isImageByName) {\r\n      this.openImagePreview(fileData, fileName);\r\n    } else if (isPdfByName) {\r\n      this.openPdfInBrowser(fileData, fileName);\r\n    } else {\r\n      this.downloadFileDirectly(fileData, fileName);\r\n    }\r\n  }\r\n\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    if (!fileName) return false;\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n\r\n  // 判斷是否為 PDF\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 判斷是否為 CAD 檔案\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 取得檔案類型圖標\r\n  getFileIcon(fileName: string): string {\r\n    if (this.isImageFile(fileName)) {\r\n      return 'fa fa-image text-green-500';\r\n    } else if (this.isPDFString(fileName)) {\r\n      return 'fa fa-file-pdf text-red-500';\r\n    } else if (this.isCadString(fileName)) {\r\n      return 'fa fa-cube text-blue-500';\r\n    } else {\r\n      return 'fa fa-file text-gray-500';\r\n    }\r\n  }\r\n\r\n  // 取得檔案類型文字\r\n  getFileTypeText(fileName: string): string {\r\n    if (this.isImageFile(fileName)) {\r\n      return '圖片';\r\n    } else if (this.isPDFString(fileName)) {\r\n      return 'PDF';\r\n    } else if (this.isCadString(fileName)) {\r\n      return 'CAD';\r\n    } else {\r\n      return '檔案';\r\n    }\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(fileUrl: string, fileName: string) {\r\n    try {\r\n      const imageUrl = this.getImageSrc(fileUrl);\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { \r\n                  margin: 0; \r\n                  padding: 20px; \r\n                  background: #f0f0f0; \r\n                  display: flex; \r\n                  flex-direction: column; \r\n                  align-items: center; \r\n                  font-family: Arial, sans-serif;\r\n                }\r\n                .header {\r\n                  background: white;\r\n                  padding: 10px 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  margin-bottom: 20px;\r\n                  font-weight: bold;\r\n                }\r\n                .image-container {\r\n                  background: white;\r\n                  padding: 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  max-width: 90vw;\r\n                  max-height: 80vh;\r\n                  overflow: auto;\r\n                }\r\n                img { \r\n                  max-width: 100%; \r\n                  height: auto; \r\n                  display: block; \r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"header\">${fileName}</div>\r\n              <div class=\"image-container\">\r\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\r\n              </div>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.open(imageUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      this.message.showErrorMSG('開啟圖片預覽失敗');\r\n    }\r\n  }\r\n\r\n  // 取得正確的圖片 src\r\n  private getImageSrc(fileData: string): string {\r\n    if (!fileData) return '';\r\n\r\n    // 如果已經是完整的 data URL，直接返回\r\n    if (fileData.startsWith('data:')) {\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是 HTTP URL，直接返回\r\n    if (fileData.startsWith('http')) {\r\n      return fileData;\r\n    }\r\n\r\n    // 如果是純 base64 字串，需要添加前綴\r\n    return `data:image/jpeg;base64,${fileData}`;\r\n  }\r\n\r\n  // 在瀏覽器中打開 PDF\r\n  private openPdfInBrowser(fileData: string, fileName: string) {\r\n    try {\r\n      let pdfUrl = fileData;\r\n\r\n      // 如果是 base64，需要轉換為 blob URL\r\n      if (!fileData.startsWith('http')) {\r\n        if (fileData.startsWith('data:application/pdf')) {\r\n          pdfUrl = fileData;\r\n        } else {\r\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\r\n        }\r\n      }\r\n\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        window.open(pdfUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('打開 PDF 時發生錯誤:', error);\r\n      this.message.showErrorMSG('打開 PDF 失敗');\r\n    }\r\n  }\r\n\r\n  // 直接下載檔案\r\n  private downloadFileDirectly(fileData: string, fileName: string) {\r\n    try {\r\n      if (fileData.startsWith('http')) {\r\n        // 如果是 URL，直接開啟下載\r\n        const link = document.createElement('a');\r\n        link.href = fileData;\r\n        link.download = fileName;\r\n        link.target = '_blank';\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n      } else {\r\n        // 如果是 base64，轉換後下載\r\n        const base64Content = fileData.includes(',') ? fileData.split(',')[1] : fileData;\r\n        const byteCharacters = atob(base64Content);\r\n        const byteNumbers = new Array(byteCharacters.length);\r\n        for (let i = 0; i < byteCharacters.length; i++) {\r\n          byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n        }\r\n        const byteArray = new Uint8Array(byteNumbers);\r\n        const blob = new Blob([byteArray], { type: 'application/octet-stream' });\r\n\r\n        const url = URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.download = fileName;\r\n        document.body.appendChild(link);\r\n        link.click();\r\n        document.body.removeChild(link);\r\n\r\n        setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n      }\r\n    } catch (error) {\r\n      console.error('下載檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('下載檔案失敗');\r\n    }\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,YAAY,QAAkB,iBAAiB;AACxD,SAASC,SAAS,QAAgB,eAAe;AAEjD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,YAAY,QAAQ,qCAAqC;AAIlE,SAASC,mBAAmB,QAAQ,oDAAoD;AAgBjF,IAAMC,8BAA8B,GAApC,MAAMA,8BAA8B;EAWzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B,EAC3BC,WAAwB;IAPxB,KAAAP,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAjBrB,KAAAC,KAAK,GAAW,CAAC;IAEjB,KAAAC,MAAM,GAAW,EAAE;IAiBjB,IAAI,CAACC,SAAS,GAAGjB,gBAAgB,CAACC,mBAAmB,CAACiB,eAAe,CAAChB,WAAW,CAACiB,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACb,eAAe,CAACc,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAAChB,eAAe,CAACkB,WAAW,CAACC,IAAI,CACnC7B,GAAG,CAAC8B,CAAC,IAAG;MACN,IAAI,CAACb,KAAK,GAAGa,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACxB,qBAAqB,CAACyB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK,CAACmB,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACL7B,GAAG,CAACqC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACC,eAAe,GAAGF,GAAG,CAACG,OAAQ;MACrC;IACF,CAAC,CAAC,CACH,CAACT,SAAS,EAAE;EACf;EAEA;EACAU,eAAeA,CAACC,IAAS;IACvB,MAAMC,QAAQ,GAAGD,IAAI,CAACE,SAAS,IAAI,EAAE;IACrC,MAAMC,QAAQ,GAAGH,IAAI,CAACI,KAAK,IAAI,EAAE;IAEjC;IACA,MAAMC,aAAa,GAAG,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;IAChD,MAAMM,WAAW,GAAG,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;IAC9C,MAAMQ,WAAW,GAAG,IAAI,CAACC,WAAW,CAACT,QAAQ,CAAC;IAE9C,IAAII,aAAa,EAAE;MACjB,IAAI,CAACM,gBAAgB,CAACR,QAAQ,EAAEF,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAIM,WAAW,EAAE;MACtB,IAAI,CAACK,gBAAgB,CAACT,QAAQ,EAAEF,QAAQ,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACY,oBAAoB,CAACV,QAAQ,EAAEF,QAAQ,CAAC;IAC/C;EACF;EAEA;EACAK,WAAWA,CAACL,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;IAC3B,MAAMa,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMC,SAAS,GAAGd,QAAQ,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE,EAAEC,WAAW,EAAE;IAC1D,OAAOJ,eAAe,CAACK,QAAQ,CAACJ,SAAS,IAAI,EAAE,CAAC;EAClD;EAEA;EACAP,WAAWA,CAACY,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACF,WAAW,EAAE,CAACG,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA;EACAX,WAAWA,CAACU,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAME,QAAQ,GAAGF,GAAG,CAACF,WAAW,EAAE;MAClC,OAAOI,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EAEA;EACAE,WAAWA,CAACtB,QAAgB;IAC1B,IAAI,IAAI,CAACK,WAAW,CAACL,QAAQ,CAAC,EAAE;MAC9B,OAAO,4BAA4B;IACrC,CAAC,MAAM,IAAI,IAAI,CAACO,WAAW,CAACP,QAAQ,CAAC,EAAE;MACrC,OAAO,6BAA6B;IACtC,CAAC,MAAM,IAAI,IAAI,CAACS,WAAW,CAACT,QAAQ,CAAC,EAAE;MACrC,OAAO,0BAA0B;IACnC,CAAC,MAAM;MACL,OAAO,0BAA0B;IACnC;EACF;EAEA;EACAuB,eAAeA,CAACvB,QAAgB;IAC9B,IAAI,IAAI,CAACK,WAAW,CAACL,QAAQ,CAAC,EAAE;MAC9B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,IAAI,CAACO,WAAW,CAACP,QAAQ,CAAC,EAAE;MACrC,OAAO,KAAK;IACd,CAAC,MAAM,IAAI,IAAI,CAACS,WAAW,CAACT,QAAQ,CAAC,EAAE;MACrC,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEA;EACQU,gBAAgBA,CAACc,OAAe,EAAExB,QAAgB;IACxD,IAAI;MACF,MAAMyB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACF,OAAO,CAAC;MAC1C,MAAMG,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGV/B,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAoCKA,QAAQ;;4BAEhByB,QAAQ,UAAUzB,QAAQ;;;;SAI7C,CAAC;QACF2B,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACAJ,MAAM,CAACC,IAAI,CAACJ,QAAQ,EAAE,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAC/D,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC;IACvC;EACF;EAEA;EACQT,WAAWA,CAACxB,QAAgB;IAClC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB;IACA,IAAIA,QAAQ,CAACkC,UAAU,CAAC,OAAO,CAAC,EAAE;MAChC,OAAOlC,QAAQ;IACjB;IAEA;IACA,IAAIA,QAAQ,CAACkC,UAAU,CAAC,MAAM,CAAC,EAAE;MAC/B,OAAOlC,QAAQ;IACjB;IAEA;IACA,OAAO,0BAA0BA,QAAQ,EAAE;EAC7C;EAEA;EACQS,gBAAgBA,CAACT,QAAgB,EAAEF,QAAgB;IACzD,IAAI;MACF,IAAIqC,MAAM,GAAGnC,QAAQ;MAErB;MACA,IAAI,CAACA,QAAQ,CAACkC,UAAU,CAAC,MAAM,CAAC,EAAE;QAChC,IAAIlC,QAAQ,CAACkC,UAAU,CAAC,sBAAsB,CAAC,EAAE;UAC/CC,MAAM,GAAGnC,QAAQ;QACnB,CAAC,MAAM;UACLmC,MAAM,GAAG,+BAA+BnC,QAAQ,EAAE;QACpD;MACF;MAEA,MAAMyB,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGV/B,QAAQ;;;;;;;6BAOFqC,MAAM;;;SAG1B,CAAC;QACFV,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACLJ,MAAM,CAACC,IAAI,CAACQ,MAAM,EAAE,QAAQ,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAAC/D,OAAO,CAACiE,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACQvB,oBAAoBA,CAACV,QAAgB,EAAEF,QAAgB;IAC7D,IAAI;MACF,IAAIE,QAAQ,CAACkC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC/B;QACA,MAAME,IAAI,GAAGR,QAAQ,CAACS,aAAa,CAAC,GAAG,CAAC;QACxCD,IAAI,CAACE,IAAI,GAAGtC,QAAQ;QACpBoC,IAAI,CAACG,QAAQ,GAAGzC,QAAQ;QACxBsC,IAAI,CAACI,MAAM,GAAG,QAAQ;QACtBZ,QAAQ,CAACtC,IAAI,CAACmD,WAAW,CAACL,IAAI,CAAC;QAC/BA,IAAI,CAACM,KAAK,EAAE;QACZd,QAAQ,CAACtC,IAAI,CAACqD,WAAW,CAACP,IAAI,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMQ,aAAa,GAAG5C,QAAQ,CAACgB,QAAQ,CAAC,GAAG,CAAC,GAAGhB,QAAQ,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGb,QAAQ;QAChF,MAAM6C,cAAc,GAAGC,IAAI,CAACF,aAAa,CAAC;QAC1C,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACI,MAAM,CAAC;QACpD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,cAAc,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;UAC9CH,WAAW,CAACG,CAAC,CAAC,GAAGL,cAAc,CAACM,UAAU,CAACD,CAAC,CAAC;QAC/C;QACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACN,WAAW,CAAC;QAC7C,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;UAAEI,IAAI,EAAE;QAA0B,CAAE,CAAC;QAExE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QACrC,MAAMlB,IAAI,GAAGR,QAAQ,CAACS,aAAa,CAAC,GAAG,CAAC;QACxCD,IAAI,CAACE,IAAI,GAAGmB,GAAG;QACfrB,IAAI,CAACG,QAAQ,GAAGzC,QAAQ;QACxB8B,QAAQ,CAACtC,IAAI,CAACmD,WAAW,CAACL,IAAI,CAAC;QAC/BA,IAAI,CAACM,KAAK,EAAE;QACZd,QAAQ,CAACtC,IAAI,CAACqD,WAAW,CAACP,IAAI,CAAC;QAE/BwB,UAAU,CAAC,MAAMF,GAAG,CAACG,eAAe,CAACJ,GAAG,CAAC,EAAE,IAAI,CAAC;MAClD;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAI,CAAC/D,OAAO,CAACiE,YAAY,CAAC,QAAQ,CAAC;IACrC;EACF;EAEA6B,YAAYA,CAAC7D,KAAU,EAAEF,SAAc;IACrC;IACA;IACA;IACA;IACA;IACA2B,MAAM,CAACC,IAAI,CAAC1B,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEA8D,YAAYA,CAACC,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAChG,iBAAiB,CAACiG,aAAa,CAACjB,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACjF,OAAO,CAACmG,aAAa,CAAC,IAAI,CAAClG,iBAAiB,CAACiG,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAACtG,qBAAqB,CAACwG,6CAA6C,CAAC;MACvE9E,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbL,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBiG,UAAU,EAAEL,SAAS;QACrBM,OAAO,EAAE,IAAI,CAACjG;;KAEjB,CAAC,CAACW,IAAI,CACL7B,GAAG,CAACqC,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACzB,OAAO,CAACuG,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACnF,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACM,eAAe,CAAC8E,cAAc,EAAEvB,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAACvD,eAAe,CAAC8E,cAAc,EAAEC,IAAI,CAAC;YACxCC,QAAQ,EAAE,IAAI,CAACpG,SAAS,CAACqG,QAAQ;YACjCC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCR,OAAO,EAAE,IAAI,CAACjG;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAAC0G,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAAC7F,SAAS,EAAE;EACf;EAEA6F,MAAMA,CAAA;IACJ,IAAI,CAAC7G,aAAa,CAACuG,IAAI,CAAC;MACtBO,MAAM;MACNC,OAAO,EAAE,IAAI,CAACnG;KACf,CAAC;IACF,IAAI,CAACf,SAAS,CAACmH,IAAI,EAAE;EACvB;EAEAjB,UAAUA,CAAA;IACR,IAAI,CAAChG,iBAAiB,CAACkH,KAAK,EAAE;IAC9B,IAAI,CAAClH,iBAAiB,CAACmH,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC/G,MAAM,CAAC;EACtD;CACD;AA/UYX,8BAA8B,GAAA2H,UAAA,EAZ1CnI,SAAS,CAAC;EACToI,QAAQ,EAAE,6BAA6B;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPvI,YAAY,EACZG,YAAY,EACZI,YAAY,EACZC,mBAAmB,CACpB;EACDgI,WAAW,EAAE,0CAA0C;EACvDC,SAAS,EAAE,CAAC,0CAA0C;CACvD,CAAC,C,EACWhI,8BAA8B,CA+U1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}