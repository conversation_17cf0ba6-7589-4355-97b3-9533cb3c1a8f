{"ast": null, "code": "export class UserActivityData {}", "map": {"version": 3, "names": ["UserActivityData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\user-activity.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport interface UserActive {\r\n  date: string;\r\n  pagesVisitCount: number;\r\n  deltaUp: boolean;\r\n  newVisits: number;\r\n}\r\n\r\nexport abstract class UserActivityData {\r\n  abstract getUserActivityData(period: string): Observable<UserActive[]>;\r\n}\r\n"], "mappings": "AASA,OAAM,MAAgBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}