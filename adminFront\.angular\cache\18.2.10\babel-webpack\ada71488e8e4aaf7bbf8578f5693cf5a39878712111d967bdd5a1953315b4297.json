{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@nebular/theme\";\nexport let MessageService = /*#__PURE__*/(() => {\n  class MessageService {\n    showErrorMSG(content, canDuplicates = false) {\n      this.toastrService.show(content, '錯誤', {\n        status: 'danger',\n        preventDuplicates: !canDuplicates\n      });\n    }\n    showSucessMSG(content, canDuplicates = false) {\n      this.toastrService.show(content, '成功', {\n        status: 'success',\n        preventDuplicates: !canDuplicates\n      });\n    }\n    showErrorMSGs(content, canDuplicates = false) {\n      this.toastrService.show(content.map(x => x).join('\\n'), '錯誤', {\n        status: 'danger',\n        preventDuplicates: !canDuplicates\n      });\n    }\n    constructor(toastrService) {\n      this.toastrService = toastrService;\n    }\n    static {\n      this.ɵfac = function MessageService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || MessageService)(i0.ɵɵinject(i1.NbToastrService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MessageService,\n        factory: MessageService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MessageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}