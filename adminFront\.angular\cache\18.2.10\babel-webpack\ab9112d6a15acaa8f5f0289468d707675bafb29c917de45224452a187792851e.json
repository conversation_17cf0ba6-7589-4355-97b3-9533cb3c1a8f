{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/api/services/quotation.service\";\nexport class QuotationService {\n  constructor(apiQuotationService) {\n    this.apiQuotationService = apiQuotationService;\n  }\n  // 取得報價單列表\n  getQuotationList(request) {\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    });\n  }\n  // 取得單筆報價單資料\n  getQuotationData(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({\n      body: request\n    });\n  }\n  // 取得戶別的報價項目\n  getQuotationByHouseId(houseId) {\n    const request = {\n      cHouseID: houseId\n    };\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({\n      body: request\n    });\n  }\n  // 儲存報價單 (支援單一項目)\n  saveQuotationItem(quotation) {\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: quotation\n    });\n  }\n  // 儲存報價單 (支援批量保存多個項目)\n  saveQuotation(request) {\n    // 由於 API 只支援單一項目保存，我們需要逐一保存每個項目\n    const saveObservables = request.items.map(item => {\n      const saveData = {\n        cQuotationID: item.cQuotationID || null,\n        cHouseID: request.houseId,\n        cItemName: item.cItemName,\n        cUnitPrice: item.cUnitPrice,\n        cCount: item.cCount,\n        cStatus: item.cStatus || 1,\n        cVersion: item.cVersion || 1,\n        cIsDeafult: item.cIsDefault || false\n      };\n      return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n        body: saveData\n      });\n    }); // 等待所有項目保存完成\n    return new Observable(observer => {\n      Promise.all(saveObservables.map(obs => obs.toPromise())).then(responses => {\n        // 檢查是否所有回應都成功\n        const allSuccess = responses.every(response => response?.StatusCode === 0);\n        observer.next({\n          success: allSuccess,\n          message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\n          data: request.items\n        });\n        observer.complete();\n      }).catch(error => {\n        observer.next({\n          success: false,\n          message: '報價單保存失敗',\n          data: []\n        });\n        observer.complete();\n      });\n    });\n  }\n  // 刪除報價單\n  deleteQuotation(quotationId) {\n    const request = {\n      cQuotationID: quotationId\n    };\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({\n      body: request\n    });\n  }\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\n  getDefaultQuotationItems() {\n    // 使用 GetList 方法獲取預設項目\n    const request = {\n      pageIndex: 0,\n      pageSize: 100\n      // 其他預設參數可能需要根據實際 API 需求調整\n    };\n    return this.apiQuotationService.apiQuotationGetListPost$Json({\n      body: request\n    }).pipe(map(response => {\n      // 轉換 API 響應格式以保持兼容性\n      return {\n        success: response.statusCode === 0,\n        // 假設 statusCode 0 表示成功\n        message: response.message || '',\n        data: response.entries || []\n      };\n    }));\n  }\n  // 更新報價項目 (使用 SaveData 方法)\n  updateQuotationItem(quotationId, item) {\n    const saveData = {\n      cQuotationID: quotationId,\n      cHouseID: item.cHouseID,\n      cItemName: item.cItemName,\n      cUnitPrice: item.cUnitPrice,\n      cCount: item.cCount,\n      cStatus: item.cStatus || 1,\n      cVersion: item.cVersion || 1,\n      cIsDeafult: item.cIsDefault\n    };\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({\n      body: saveData\n    }).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: [item] // 包裝為陣列以符合 QuotationResponse 格式\n      };\n    }));\n  }\n  // 刪除報價項目\n  deleteQuotationItem(quotationId) {\n    return this.deleteQuotation(quotationId).pipe(map(response => {\n      return {\n        success: response.StatusCode === 0,\n        // 假設 StatusCode 0 表示成功\n        message: response.Message || '',\n        data: []\n      };\n    }));\n  }\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\n  exportQuotation(houseId) {\n    // 這個方法可能需要使用其他 API 或保持原有實作\n    // 暫時拋出錯誤提示需要實作\n    throw new Error('Export quotation functionality needs to be implemented separately');\n  }\n  static {\n    this.ɵfac = function QuotationService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || QuotationService)(i0.ɵɵinject(i1.QuotationService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QuotationService,\n      factory: QuotationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "map", "QuotationService", "constructor", "apiQuotationService", "getQuotationList", "request", "apiQuotationGetListPost$Json", "body", "getQuotationData", "quotationId", "cQuotationID", "apiQuotationGetDataPost$Json", "getQuotationByHouseId", "houseId", "cHouseID", "apiQuotationGetListByHouseIdPost$Json", "saveQuotationItem", "quotation", "apiQuotationSaveDataPost$Json", "saveQuotation", "saveObservables", "items", "item", "saveData", "cItemName", "cUnitPrice", "cCount", "cStatus", "cVersion", "cIs<PERSON><PERSON><PERSON>t", "cIsDefault", "observer", "Promise", "all", "obs", "to<PERSON>romise", "then", "responses", "allSuccess", "every", "response", "StatusCode", "next", "success", "message", "data", "complete", "catch", "error", "deleteQuotation", "apiQuotationDeleteDataPost$Json", "getDefaultQuotationItems", "pageIndex", "pageSize", "pipe", "statusCode", "entries", "updateQuotationItem", "Message", "deleteQuotationItem", "exportQuotation", "Error", "i0", "ɵɵinject", "i1", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\services\\quotation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { QuotationItem, QuotationRequest, QuotationResponse } from '../models/quotation.model';\r\nimport { QuotationService as ApiQuotationService } from '../../services/api/services/quotation.service';\r\nimport {\r\n  GetListQuotationRequest,\r\n  GetQuotationByIdRequest,\r\n  SaveDataQuotation,\r\n  DeleteQuotationRequest,\r\n  GetListByHouseIdRequest\r\n} from '../../services/api/models';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class QuotationService {\r\n\r\n  constructor(private apiQuotationService: ApiQuotationService) { }\r\n  // 取得報價單列表\r\n  getQuotationList(request: GetListQuotationRequest): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request });\r\n  }\r\n  // 取得單筆報價單資料\r\n  getQuotationData(quotationId: number): Observable<any> {\r\n    const request: GetQuotationByIdRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationGetDataPost$Json({ body: request });\r\n  }\r\n\r\n  // 取得戶別的報價項目\r\n  getQuotationByHouseId(houseId: number): Observable<any> {\r\n    const request: GetListByHouseIdRequest = { cHouseID: houseId };\r\n    return this.apiQuotationService.apiQuotationGetListByHouseIdPost$Json({ body: request });\r\n  }\r\n  // 儲存報價單 (支援單一項目)\r\n  saveQuotationItem(quotation: SaveDataQuotation): Observable<any> {\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: quotation });\r\n  }\r\n\r\n  // 儲存報價單 (支援批量保存多個項目)\r\n  saveQuotation(request: { houseId: number; items: QuotationItem[] }): Observable<QuotationResponse> {\r\n    // 由於 API 只支援單一項目保存，我們需要逐一保存每個項目\r\n    const saveObservables = request.items.map(item => {\r\n      const saveData: SaveDataQuotation = {\r\n        cQuotationID: item.cQuotationID || null,\r\n        cHouseID: request.houseId,\r\n        cItemName: item.cItemName,\r\n        cUnitPrice: item.cUnitPrice,\r\n        cCount: item.cCount,\r\n        cStatus: item.cStatus || 1,\r\n        cVersion: item.cVersion || 1,\r\n        cIsDeafult: item.cIsDefault || false,\r\n      };\r\n      return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData });\r\n    });    // 等待所有項目保存完成\r\n    return new Observable(observer => {\r\n      Promise.all(saveObservables.map(obs => obs.toPromise()))\r\n        .then(responses => {\r\n          // 檢查是否所有回應都成功\r\n          const allSuccess = responses.every(response => response?.StatusCode === 0);\r\n          observer.next({\r\n            success: allSuccess,\r\n            message: allSuccess ? '報價單保存成功' : '部分項目保存失敗',\r\n            data: request.items\r\n          } as QuotationResponse);\r\n          observer.complete();\r\n        })\r\n        .catch(error => {\r\n          observer.next({\r\n            success: false,\r\n            message: '報價單保存失敗',\r\n            data: []\r\n          } as QuotationResponse);\r\n          observer.complete();\r\n        });\r\n    });\r\n  }\r\n  // 刪除報價單\r\n  deleteQuotation(quotationId: number): Observable<any> {\r\n    const request: DeleteQuotationRequest = { cQuotationID: quotationId };\r\n    return this.apiQuotationService.apiQuotationDeleteDataPost$Json({ body: request });\r\n  }\r\n  // 取得預設報價項目 (保持原有方法以兼容現有代碼)\r\n  getDefaultQuotationItems(): Observable<QuotationResponse> {\r\n    // 使用 GetList 方法獲取預設項目\r\n    const request: GetListQuotationRequest = {\r\n      pageIndex: 0,\r\n      pageSize: 100,\r\n      // 其他預設參數可能需要根據實際 API 需求調整\r\n    };\r\n    return this.apiQuotationService.apiQuotationGetListPost$Json({ body: request }).pipe(\r\n      map(response => {\r\n        // 轉換 API 響應格式以保持兼容性\r\n        return {\r\n          success: response.statusCode === 0, // 假設 statusCode 0 表示成功\r\n          message: response.message || '',\r\n          data: response.entries || []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n  // 更新報價項目 (使用 SaveData 方法)\r\n  updateQuotationItem(quotationId: number, item: QuotationItem): Observable<QuotationResponse> {\r\n    const saveData: SaveDataQuotation = {\r\n      cQuotationID: quotationId,\r\n      cHouseID: item.cHouseID,\r\n      cItemName: item.cItemName,\r\n      cUnitPrice: item.cUnitPrice,\r\n      cCount: item.cCount,\r\n      cStatus: item.cStatus || 1,\r\n      cVersion: item.cVersion || 1,\r\n      cIsDeafult: item.cIsDefault,\r\n    };\r\n    return this.apiQuotationService.apiQuotationSaveDataPost$Json({ body: saveData }).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: [item] // 包裝為陣列以符合 QuotationResponse 格式\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 刪除報價項目\r\n  deleteQuotationItem(quotationId: number): Observable<QuotationResponse> {\r\n    return this.deleteQuotation(quotationId).pipe(\r\n      map(response => {\r\n        return {\r\n          success: response.StatusCode === 0, // 假設 StatusCode 0 表示成功\r\n          message: response.Message || '',\r\n          data: []\r\n        } as QuotationResponse;\r\n      })\r\n    );\r\n  }\r\n\r\n  // 匯出報價單 (需要另外實作，因為 swagger 中沒有此 API)\r\n  exportQuotation(houseId: number): Observable<Blob> {\r\n    // 這個方法可能需要使用其他 API 或保持原有實作\r\n    // 暫時拋出錯誤提示需要實作\r\n    throw new Error('Export quotation functionality needs to be implemented separately');\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,MAAM;AACjC,SAASC,GAAG,QAAmB,gBAAgB;;;AAc/C,OAAM,MAAOC,gBAAgB;EAE3BC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;EAAyB;EAChE;EACAC,gBAAgBA,CAACC,OAAgC;IAC/C,OAAO,IAAI,CAACF,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EACA;EACAG,gBAAgBA,CAACC,WAAmB;IAClC,MAAMJ,OAAO,GAA4B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACtE,OAAO,IAAI,CAACN,mBAAmB,CAACQ,4BAA4B,CAAC;MAAEJ,IAAI,EAAEF;IAAO,CAAE,CAAC;EACjF;EAEA;EACAO,qBAAqBA,CAACC,OAAe;IACnC,MAAMR,OAAO,GAA4B;MAAES,QAAQ,EAAED;IAAO,CAAE;IAC9D,OAAO,IAAI,CAACV,mBAAmB,CAACY,qCAAqC,CAAC;MAAER,IAAI,EAAEF;IAAO,CAAE,CAAC;EAC1F;EACA;EACAW,iBAAiBA,CAACC,SAA4B;IAC5C,OAAO,IAAI,CAACd,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAEU;IAAS,CAAE,CAAC;EACpF;EAEA;EACAE,aAAaA,CAACd,OAAoD;IAChE;IACA,MAAMe,eAAe,GAAGf,OAAO,CAACgB,KAAK,CAACrB,GAAG,CAACsB,IAAI,IAAG;MAC/C,MAAMC,QAAQ,GAAsB;QAClCb,YAAY,EAAEY,IAAI,CAACZ,YAAY,IAAI,IAAI;QACvCI,QAAQ,EAAET,OAAO,CAACQ,OAAO;QACzBW,SAAS,EAAEF,IAAI,CAACE,SAAS;QACzBC,UAAU,EAAEH,IAAI,CAACG,UAAU;QAC3BC,MAAM,EAAEJ,IAAI,CAACI,MAAM;QACnBC,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,CAAC;QAC1BC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,CAAC;QAC5BC,UAAU,EAAEP,IAAI,CAACQ,UAAU,IAAI;OAChC;MACD,OAAO,IAAI,CAAC3B,mBAAmB,CAACe,6BAA6B,CAAC;QAAEX,IAAI,EAAEgB;MAAQ,CAAE,CAAC;IACnF,CAAC,CAAC,CAAC,CAAI;IACP,OAAO,IAAIxB,UAAU,CAACgC,QAAQ,IAAG;MAC/BC,OAAO,CAACC,GAAG,CAACb,eAAe,CAACpB,GAAG,CAACkC,GAAG,IAAIA,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC,CACrDC,IAAI,CAACC,SAAS,IAAG;QAChB;QACA,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAIA,QAAQ,EAAEC,UAAU,KAAK,CAAC,CAAC;QAC1EV,QAAQ,CAACW,IAAI,CAAC;UACZC,OAAO,EAAEL,UAAU;UACnBM,OAAO,EAAEN,UAAU,GAAG,SAAS,GAAG,UAAU;UAC5CO,IAAI,EAAExC,OAAO,CAACgB;SACM,CAAC;QACvBU,QAAQ,CAACe,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbjB,QAAQ,CAACW,IAAI,CAAC;UACZC,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,SAAS;UAClBC,IAAI,EAAE;SACc,CAAC;QACvBd,QAAQ,CAACe,QAAQ,EAAE;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EACA;EACAG,eAAeA,CAACxC,WAAmB;IACjC,MAAMJ,OAAO,GAA2B;MAAEK,YAAY,EAAED;IAAW,CAAE;IACrE,OAAO,IAAI,CAACN,mBAAmB,CAAC+C,+BAA+B,CAAC;MAAE3C,IAAI,EAAEF;IAAO,CAAE,CAAC;EACpF;EACA;EACA8C,wBAAwBA,CAAA;IACtB;IACA,MAAM9C,OAAO,GAA4B;MACvC+C,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;MACV;KACD;IACD,OAAO,IAAI,CAAClD,mBAAmB,CAACG,4BAA4B,CAAC;MAAEC,IAAI,EAAEF;IAAO,CAAE,CAAC,CAACiD,IAAI,CAClFtD,GAAG,CAACwC,QAAQ,IAAG;MACb;MACA,OAAO;QACLG,OAAO,EAAEH,QAAQ,CAACe,UAAU,KAAK,CAAC;QAAE;QACpCX,OAAO,EAAEJ,QAAQ,CAACI,OAAO,IAAI,EAAE;QAC/BC,IAAI,EAAEL,QAAQ,CAACgB,OAAO,IAAI;OACN;IACxB,CAAC,CAAC,CACH;EACH;EACA;EACAC,mBAAmBA,CAAChD,WAAmB,EAAEa,IAAmB;IAC1D,MAAMC,QAAQ,GAAsB;MAClCb,YAAY,EAAED,WAAW;MACzBK,QAAQ,EAAEQ,IAAI,CAACR,QAAQ;MACvBU,SAAS,EAAEF,IAAI,CAACE,SAAS;MACzBC,UAAU,EAAEH,IAAI,CAACG,UAAU;MAC3BC,MAAM,EAAEJ,IAAI,CAACI,MAAM;MACnBC,OAAO,EAAEL,IAAI,CAACK,OAAO,IAAI,CAAC;MAC1BC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,CAAC;MAC5BC,UAAU,EAAEP,IAAI,CAACQ;KAClB;IACD,OAAO,IAAI,CAAC3B,mBAAmB,CAACe,6BAA6B,CAAC;MAAEX,IAAI,EAAEgB;IAAQ,CAAE,CAAC,CAAC+B,IAAI,CACpFtD,GAAG,CAACwC,QAAQ,IAAG;MACb,OAAO;QACLG,OAAO,EAAEH,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCG,OAAO,EAAEJ,QAAQ,CAACkB,OAAO,IAAI,EAAE;QAC/Bb,IAAI,EAAE,CAACvB,IAAI,CAAC,CAAC;OACO;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAqC,mBAAmBA,CAAClD,WAAmB;IACrC,OAAO,IAAI,CAACwC,eAAe,CAACxC,WAAW,CAAC,CAAC6C,IAAI,CAC3CtD,GAAG,CAACwC,QAAQ,IAAG;MACb,OAAO;QACLG,OAAO,EAAEH,QAAQ,CAACC,UAAU,KAAK,CAAC;QAAE;QACpCG,OAAO,EAAEJ,QAAQ,CAACkB,OAAO,IAAI,EAAE;QAC/Bb,IAAI,EAAE;OACc;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAe,eAAeA,CAAC/C,OAAe;IAC7B;IACA;IACA,MAAM,IAAIgD,KAAK,CAAC,mEAAmE,CAAC;EACtF;;;uCA9HW5D,gBAAgB,EAAA6D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA/D,gBAAA;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAgE,OAAA,EAAhBhE,gBAAgB,CAAAiE,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}