{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ValidationHelper {\n  constructor() {\n    this.errorMessages = [];\n  }\n  required(name, value) {\n    if (value == null || value === undefined) {\n      this.addErrorMessage(name + ' 必填');\n    }\n    if (typeof String) {\n      if (value === '') {\n        this.addErrorMessage(name + ' 必填');\n      }\n    }\n  }\n  checkStartBeforeEnd(name, start, end) {\n    if (start && end) {\n      const startDate = new Date(start);\n      const endDate = new Date(end);\n      if (startDate > endDate) {\n        this.addErrorMessage(name + ' 开始日期不能大于结束日期');\n      }\n    }\n  }\n  isPhoneNumber(name, value) {\n    if (value !== null && value !== undefined && value !== '') {\n      const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/;\n      if (!phoneRegex.test(value)) {\n        this.addErrorMessage(name + '電話號碼格式不正確');\n      }\n      if (value.length !== 10 || isNaN(value)) {\n        this.addErrorMessage(name + ' 長度=10');\n      }\n    }\n  }\n  isNaturalNumberInRange(name, value, min, max) {\n    if (value == null || value === undefined || value === '') {\n      this.addErrorMessage(name + ' 必填');\n    } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\n      this.addErrorMessage(name + ' 必須是數字');\n    } else {\n      const numValue = parseInt(value, 10);\n      if (numValue < min || numValue > max) {\n        this.addErrorMessage(name + ` 必須介於${min}到${max}之間`);\n      }\n    }\n  }\n  isStringMaxLength(name, value, maxLength) {\n    if (typeof value === 'string' && value.length > maxLength) {\n      this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`);\n    }\n  }\n  pattern(name, value, pattern, errorDetail = '') {\n    if (!value) return;\n    const regex = new RegExp(pattern);\n    if (regex.test(value) === false) {\n      this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\n    }\n  }\n  regExp(name, value, regEx) {\n    if (regEx.test(value) === false) {\n      this.addErrorMessage(name + ' 格式錯誤');\n    }\n  }\n  equal(name1, name2, value1, value2) {\n    if (value1 !== value2) {\n      this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\n    }\n  }\n  selected(name, value) {\n    if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\n      this.addErrorMessage(name + ' 尚未全部選擇');\n    }\n  }\n  CheckTaiwanID(userid) {\n    // 正規表達式：匹配身分證 (1 字母 + 9 數字) 或居留證 (2 字母 + 8 數字)\n    const idRegex = /^(?:[A-Z][12][0-9]{8}|[A-Z]{2}[0-9]{8})$/;\n    if (!idRegex.test(userid)) {\n      return {\n        isValid: false,\n        errorMessage: '格式錯誤：請輸入正確的身分證或居留證號碼'\n      };\n    }\n    // 身分證與居留證的字母對應表 (A=10, B=11, ..., Z=33)\n    const letterMap = {\n      A: '10',\n      B: '11',\n      C: '12',\n      D: '13',\n      E: '14',\n      F: '15',\n      G: '16',\n      H: '17',\n      I: '34',\n      J: '18',\n      K: '19',\n      L: '20',\n      M: '21',\n      N: '22',\n      O: '35',\n      P: '23',\n      Q: '24',\n      R: '25',\n      S: '26',\n      T: '27',\n      U: '28',\n      V: '29',\n      W: '32',\n      X: '30',\n      Y: '31',\n      Z: '33'\n    };\n    let tempId;\n    let weights;\n    let isResidentId = userid.length === 10; // 居留證 (10 位) 或身分證 (11 位)\n    if (isResidentId) {\n      // 居留證：2 字母 + 8 數字\n      const firstLetter = userid.charAt(0);\n      const secondLetter = userid.charAt(1);\n      tempId = letterMap[firstLetter] + letterMap[secondLetter].charAt(1) + userid.slice(2);\n      weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 居留證加權\n    } else {\n      // 身分證：1 字母 + 9 數字\n      const firstLetter = userid.charAt(0);\n      tempId = letterMap[firstLetter] + userid.slice(1);\n      weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 身分證加權\n    }\n    // 計算檢查碼\n    let sum = 0;\n    for (let i = 0; i < tempId.length; i++) {\n      sum += parseInt(tempId.charAt(i)) * weights[i];\n    }\n    // 檢查碼驗證：總和必須能被 10 整除\n    if (sum % 10 === 0) {\n      return {\n        isValid: true\n      };\n    } else {\n      return {\n        isValid: false,\n        errorMessage: '檢查碼錯誤：請確認身分證或居留證號碼'\n      };\n    }\n  }\n  Date(StrDt, EndDt) {\n    if (EndDt != null) {\n      if (EndDt < StrDt) {\n        this.addErrorMessage(\"起始時間不能大於結束時間！\");\n      }\n    }\n  }\n  addErrorMessage(message) {\n    this.errorMessages.push(message);\n  }\n  clear() {\n    this.errorMessages = [];\n  }\n  static {\n    this.ɵfac = function ValidationHelper_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ValidationHelper)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ValidationHelper,\n      factory: ValidationHelper.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ValidationHelper", "constructor", "errorMessages", "required", "name", "value", "undefined", "addErrorMessage", "String", "checkStartBeforeEnd", "start", "end", "startDate", "Date", "endDate", "isPhoneNumber", "phoneRegex", "test", "length", "isNaN", "isNaturalNumberInRange", "min", "max", "numValue", "parseInt", "isStringMaxLength", "max<PERSON><PERSON><PERSON>", "pattern", "errorDetail", "regex", "RegExp", "regExp", "regEx", "equal", "name1", "name2", "value1", "value2", "selected", "filter", "x", "CheckTaiwanID", "userid", "idRegex", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "letterMap", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "tempId", "weights", "isResidentId", "firstLetter", "char<PERSON>t", "secondLetter", "slice", "sum", "i", "StrDt", "EndDt", "message", "push", "clear", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\shared\\helper\\validationHelper.ts"], "sourcesContent": ["import { filter } from 'rxjs/operators';\r\nimport { Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class ValidationHelper {\r\n\r\n  errorMessages = [] as string[];\r\n\r\n  constructor() { }\r\n\r\n  required(name: string, value: any) {\r\n    if (value == null || value === undefined) {\r\n      this.addErrorMessage(name + ' 必填');\r\n    }\r\n    if (typeof (String)) {\r\n      if (value === '') {\r\n        this.addErrorMessage(name + ' 必填');\r\n      }\r\n    }\r\n  }\r\n\r\n  checkStartBeforeEnd(name: string, start?: string, end?: string) {\r\n    if (start && end) {\r\n      const startDate = new Date(start);\r\n      const endDate = new Date(end);\r\n      if (startDate > endDate) {\r\n        this.addErrorMessage(name + ' 开始日期不能大于结束日期');\r\n      }\r\n    }\r\n  }\r\n\r\n  isPhoneNumber(name: string, value: any) {\r\n    if (value !== null && value !== undefined && value !== '') {\r\n      const phoneRegex = /^\\+?\\d{1,3}[-.\\s]?\\(?\\d{1,3}\\)?[-.\\s]?\\d{3,4}[-.\\s]?\\d{4}$/;\r\n      if (!phoneRegex.test(value)) {\r\n        this.addErrorMessage(name + '電話號碼格式不正確');\r\n      }\r\n      if (value.length !== 10 || isNaN(value)) {\r\n        this.addErrorMessage(name + ' 長度=10');\r\n      }\r\n    }\r\n  }\r\n\r\n  isNaturalNumberInRange(name: string, value: any, min: number, max: number) {\r\n    if (value == null || value === undefined || value === '') {\r\n      this.addErrorMessage(name + ' 必填');\r\n    } else if (typeof value === 'string' && !/^\\d+$/.test(value)) {\r\n      this.addErrorMessage(name + ' 必須是數字');\r\n    } else {\r\n      const numValue = parseInt(value, 10);\r\n      if (numValue < min || numValue > max) {\r\n        this.addErrorMessage(name + ` 必須介於${min}到${max}之間`);\r\n      }\r\n    }\r\n  }\r\n\r\n  isStringMaxLength(name: string, value: any, maxLength: number) {\r\n    if (typeof value === 'string' && value.length > maxLength) {\r\n      this.addErrorMessage(name + ` 長度不能超過 ${maxLength} 個字元`);\r\n    }\r\n  }\r\n\r\n  pattern(name: string, value: string | null | undefined, pattern: string, errorDetail: string = '') {\r\n    if (!value) return\r\n    const regex = new RegExp(pattern);\r\n\r\n    if (regex.test(value!) === false) {\r\n      this.addErrorMessage(name + ' 格式錯誤' + errorDetail);\r\n    }\r\n  }\r\n\r\n  regExp(name: string, value: string, regEx: RegExp) {\r\n    if (regEx.test(value) === false) {\r\n      this.addErrorMessage(name + ' 格式錯誤');\r\n    }\r\n  }\r\n\r\n  equal(name1: string, name2: string, value1: string | undefined | null, value2: string | undefined | null) {\r\n    if (value1 !== value2) {\r\n      this.addErrorMessage(name1 + ' 與 ' + name2 + ' 不相同');\r\n    }\r\n  }\r\n\r\n\r\n  selected(name: string, value: string[]) {\r\n    if (value.filter(x => x === '' || x === null || x === undefined).length > 0) {\r\n      this.addErrorMessage(name + ' 尚未全部選擇');\r\n    }\r\n  }\r\n\r\n  CheckTaiwanID(userid: string): { isValid: boolean; errorMessage?: string } {\r\n    // 正規表達式：匹配身分證 (1 字母 + 9 數字) 或居留證 (2 字母 + 8 數字)\r\n    const idRegex = /^(?:[A-Z][12][0-9]{8}|[A-Z]{2}[0-9]{8})$/;\r\n\r\n    if (!idRegex.test(userid)) {\r\n      return { isValid: false, errorMessage: '格式錯誤：請輸入正確的身分證或居留證號碼' };\r\n    }\r\n\r\n    // 身分證與居留證的字母對應表 (A=10, B=11, ..., Z=33)\r\n    const letterMap: { [key: string]: string } = {\r\n      A: '10', B: '11', C: '12', D: '13', E: '14', F: '15', G: '16', H: '17', I: '34',\r\n      J: '18', K: '19', L: '20', M: '21', N: '22', O: '35', P: '23', Q: '24', R: '25',\r\n      S: '26', T: '27', U: '28', V: '29', W: '32', X: '30', Y: '31', Z: '33'\r\n    };\r\n\r\n    let tempId: string;\r\n    let weights: number[];\r\n    let isResidentId = userid.length === 10; // 居留證 (10 位) 或身分證 (11 位)\r\n\r\n    if (isResidentId) {\r\n      // 居留證：2 字母 + 8 數字\r\n      const firstLetter = userid.charAt(0);\r\n      const secondLetter = userid.charAt(1);\r\n      tempId = letterMap[firstLetter] + letterMap[secondLetter].charAt(1) + userid.slice(2);\r\n      weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 居留證加權\r\n    } else {\r\n      // 身分證：1 字母 + 9 數字\r\n      const firstLetter = userid.charAt(0);\r\n      tempId = letterMap[firstLetter] + userid.slice(1);\r\n      weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1]; // 身分證加權\r\n    }\r\n\r\n    // 計算檢查碼\r\n    let sum = 0;\r\n    for (let i = 0; i < tempId.length; i++) {\r\n      sum += parseInt(tempId.charAt(i)) * weights[i];\r\n    }\r\n\r\n    // 檢查碼驗證：總和必須能被 10 整除\r\n    if (sum % 10 === 0) {\r\n      return { isValid: true };\r\n    } else {\r\n      return { isValid: false, errorMessage: '檢查碼錯誤：請確認身分證或居留證號碼' };\r\n    }\r\n  }\r\n\r\n  Date(StrDt: Date, EndDt: Date) {\r\n    if (EndDt != null) {\r\n      if (EndDt < StrDt) {\r\n        this.addErrorMessage(\"起始時間不能大於結束時間！\");\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  addErrorMessage(message: string) {\r\n    this.errorMessages.push(message);\r\n  }\r\n\r\n  clear() {\r\n    this.errorMessages = [];\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,gBAAgB;EAI3BC,YAAA;IAFA,KAAAC,aAAa,GAAG,EAAc;EAEd;EAEhBC,QAAQA,CAACC,IAAY,EAAEC,KAAU;IAC/B,IAAIA,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACxC,IAAI,CAACC,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;IACpC;IACA,IAAI,OAAQI,MAAO,EAAE;MACnB,IAAIH,KAAK,KAAK,EAAE,EAAE;QAChB,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;MACpC;IACF;EACF;EAEAK,mBAAmBA,CAACL,IAAY,EAAEM,KAAc,EAAEC,GAAY;IAC5D,IAAID,KAAK,IAAIC,GAAG,EAAE;MAChB,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACH,KAAK,CAAC;MACjC,MAAMI,OAAO,GAAG,IAAID,IAAI,CAACF,GAAG,CAAC;MAC7B,IAAIC,SAAS,GAAGE,OAAO,EAAE;QACvB,IAAI,CAACP,eAAe,CAACH,IAAI,GAAG,eAAe,CAAC;MAC9C;IACF;EACF;EAEAW,aAAaA,CAACX,IAAY,EAAEC,KAAU;IACpC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MACzD,MAAMW,UAAU,GAAG,4DAA4D;MAC/E,IAAI,CAACA,UAAU,CAACC,IAAI,CAACZ,KAAK,CAAC,EAAE;QAC3B,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,WAAW,CAAC;MAC1C;MACA,IAAIC,KAAK,CAACa,MAAM,KAAK,EAAE,IAAIC,KAAK,CAACd,KAAK,CAAC,EAAE;QACvC,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,QAAQ,CAAC;MACvC;IACF;EACF;EAEAgB,sBAAsBA,CAAChB,IAAY,EAAEC,KAAU,EAAEgB,GAAW,EAAEC,GAAW;IACvE,IAAIjB,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MACxD,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,KAAK,CAAC;IACpC,CAAC,MAAM,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAI,CAAC,OAAO,CAACY,IAAI,CAACZ,KAAK,CAAC,EAAE;MAC5D,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,MAAMmB,QAAQ,GAAGC,QAAQ,CAACnB,KAAK,EAAE,EAAE,CAAC;MACpC,IAAIkB,QAAQ,GAAGF,GAAG,IAAIE,QAAQ,GAAGD,GAAG,EAAE;QACpC,IAAI,CAACf,eAAe,CAACH,IAAI,GAAG,QAAQiB,GAAG,IAAIC,GAAG,IAAI,CAAC;MACrD;IACF;EACF;EAEAG,iBAAiBA,CAACrB,IAAY,EAAEC,KAAU,EAAEqB,SAAiB;IAC3D,IAAI,OAAOrB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACa,MAAM,GAAGQ,SAAS,EAAE;MACzD,IAAI,CAACnB,eAAe,CAACH,IAAI,GAAG,WAAWsB,SAAS,MAAM,CAAC;IACzD;EACF;EAEAC,OAAOA,CAACvB,IAAY,EAAEC,KAAgC,EAAEsB,OAAe,EAAEC,WAAA,GAAsB,EAAE;IAC/F,IAAI,CAACvB,KAAK,EAAE;IACZ,MAAMwB,KAAK,GAAG,IAAIC,MAAM,CAACH,OAAO,CAAC;IAEjC,IAAIE,KAAK,CAACZ,IAAI,CAACZ,KAAM,CAAC,KAAK,KAAK,EAAE;MAChC,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,OAAO,GAAGwB,WAAW,CAAC;IACpD;EACF;EAEAG,MAAMA,CAAC3B,IAAY,EAAEC,KAAa,EAAE2B,KAAa;IAC/C,IAAIA,KAAK,CAACf,IAAI,CAACZ,KAAK,CAAC,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACE,eAAe,CAACH,IAAI,GAAG,OAAO,CAAC;IACtC;EACF;EAEA6B,KAAKA,CAACC,KAAa,EAAEC,KAAa,EAAEC,MAAiC,EAAEC,MAAiC;IACtG,IAAID,MAAM,KAAKC,MAAM,EAAE;MACrB,IAAI,CAAC9B,eAAe,CAAC2B,KAAK,GAAG,KAAK,GAAGC,KAAK,GAAG,MAAM,CAAC;IACtD;EACF;EAGAG,QAAQA,CAAClC,IAAY,EAAEC,KAAe;IACpC,IAAIA,KAAK,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKlC,SAAS,CAAC,CAACY,MAAM,GAAG,CAAC,EAAE;MAC3E,IAAI,CAACX,eAAe,CAACH,IAAI,GAAG,SAAS,CAAC;IACxC;EACF;EAEAqC,aAAaA,CAACC,MAAc;IAC1B;IACA,MAAMC,OAAO,GAAG,0CAA0C;IAE1D,IAAI,CAACA,OAAO,CAAC1B,IAAI,CAACyB,MAAM,CAAC,EAAE;MACzB,OAAO;QAAEE,OAAO,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAsB,CAAE;IACjE;IAEA;IACA,MAAMC,SAAS,GAA8B;MAC3CC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAC/EC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAC/EC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE;KACnE;IAED,IAAIC,MAAc;IAClB,IAAIC,OAAiB;IACrB,IAAIC,YAAY,GAAGjC,MAAM,CAACxB,MAAM,KAAK,EAAE,CAAC,CAAC;IAEzC,IAAIyD,YAAY,EAAE;MAChB;MACA,MAAMC,WAAW,GAAGlC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC;MACpC,MAAMC,YAAY,GAAGpC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC;MACrCJ,MAAM,GAAG3B,SAAS,CAAC8B,WAAW,CAAC,GAAG9B,SAAS,CAACgC,YAAY,CAAC,CAACD,MAAM,CAAC,CAAC,CAAC,GAAGnC,MAAM,CAACqC,KAAK,CAAC,CAAC,CAAC;MACrFL,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL;MACA,MAAME,WAAW,GAAGlC,MAAM,CAACmC,MAAM,CAAC,CAAC,CAAC;MACpCJ,MAAM,GAAG3B,SAAS,CAAC8B,WAAW,CAAC,GAAGlC,MAAM,CAACqC,KAAK,CAAC,CAAC,CAAC;MACjDL,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5C;IAEA;IACA,IAAIM,GAAG,GAAG,CAAC;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACvD,MAAM,EAAE+D,CAAC,EAAE,EAAE;MACtCD,GAAG,IAAIxD,QAAQ,CAACiD,MAAM,CAACI,MAAM,CAACI,CAAC,CAAC,CAAC,GAAGP,OAAO,CAACO,CAAC,CAAC;IAChD;IAEA;IACA,IAAID,GAAG,GAAG,EAAE,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEpC,OAAO,EAAE;MAAI,CAAE;IAC1B,CAAC,MAAM;MACL,OAAO;QAAEA,OAAO,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAoB,CAAE;IAC/D;EACF;EAEAhC,IAAIA,CAACqE,KAAW,EAAEC,KAAW;IAC3B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIA,KAAK,GAAGD,KAAK,EAAE;QACjB,IAAI,CAAC3E,eAAe,CAAC,eAAe,CAAC;MACvC;IACF;EACF;EAGAA,eAAeA,CAAC6E,OAAe;IAC7B,IAAI,CAAClF,aAAa,CAACmF,IAAI,CAACD,OAAO,CAAC;EAClC;EAEAE,KAAKA,CAAA;IACH,IAAI,CAACpF,aAAa,GAAG,EAAE;EACzB;;;uCAnJWF,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAuF,OAAA,EAAhBvF,gBAAgB,CAAAwF,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}