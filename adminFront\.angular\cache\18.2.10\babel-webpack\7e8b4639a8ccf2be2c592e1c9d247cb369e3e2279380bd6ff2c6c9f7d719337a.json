{"ast": null, "code": ";\n(function (root, factory) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  (function () {\n    // Check if typed arrays are supported\n    if (typeof ArrayBuffer != 'function') {\n      return;\n    }\n\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var WordArray = C_lib.WordArray;\n\n    // Reference original init\n    var superInit = WordArray.init;\n\n    // Augment WordArray.init to handle typed arrays\n    var subInit = WordArray.init = function (typedArray) {\n      // Convert buffers to uint8\n      if (typedArray instanceof ArrayBuffer) {\n        typedArray = new Uint8Array(typedArray);\n      }\n\n      // Convert other array views to uint8\n      if (typedArray instanceof Int8Array || typeof Uint8ClampedArray !== \"undefined\" && typedArray instanceof Uint8ClampedArray || typedArray instanceof Int16Array || typedArray instanceof Uint16Array || typedArray instanceof Int32Array || typedArray instanceof Uint32Array || typedArray instanceof Float32Array || typedArray instanceof Float64Array) {\n        typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n      }\n\n      // Handle Uint8Array\n      if (typedArray instanceof Uint8Array) {\n        // Shortcut\n        var typedArrayByteLength = typedArray.byteLength;\n\n        // Extract bytes\n        var words = [];\n        for (var i = 0; i < typedArrayByteLength; i++) {\n          words[i >>> 2] |= typedArray[i] << 24 - i % 4 * 8;\n        }\n\n        // Initialize this word array\n        superInit.call(this, words, typedArrayByteLength);\n      } else {\n        // Else call normal init\n        superInit.apply(this, arguments);\n      }\n    };\n    subInit.prototype = WordArray;\n  })();\n  return CryptoJS.lib.WordArray;\n});", "map": {"version": 3, "names": ["root", "factory", "exports", "module", "require", "define", "amd", "CryptoJS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "C", "C_lib", "lib", "WordArray", "superInit", "init", "subInit", "typedArray", "Uint8Array", "Int8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "buffer", "byteOffset", "byteLength", "typedArrayByteLength", "words", "i", "call", "apply", "arguments", "prototype"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/lib-typedarrays.js"], "sourcesContent": [";(function (root, factory) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t(function () {\n\t    // Check if typed arrays are supported\n\t    if (typeof ArrayBuffer != 'function') {\n\t        return;\n\t    }\n\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var WordArray = C_lib.WordArray;\n\n\t    // Reference original init\n\t    var superInit = WordArray.init;\n\n\t    // Augment WordArray.init to handle typed arrays\n\t    var subInit = WordArray.init = function (typedArray) {\n\t        // Convert buffers to uint8\n\t        if (typedArray instanceof ArrayBuffer) {\n\t            typedArray = new Uint8Array(typedArray);\n\t        }\n\n\t        // Convert other array views to uint8\n\t        if (\n\t            typedArray instanceof Int8Array ||\n\t            (typeof Uint8ClampedArray !== \"undefined\" && typedArray instanceof Uint8ClampedArray) ||\n\t            typedArray instanceof Int16Array ||\n\t            typedArray instanceof Uint16Array ||\n\t            typedArray instanceof Int32Array ||\n\t            typedArray instanceof Uint32Array ||\n\t            typedArray instanceof Float32Array ||\n\t            typedArray instanceof Float64Array\n\t        ) {\n\t            typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);\n\t        }\n\n\t        // Handle Uint8Array\n\t        if (typedArray instanceof Uint8Array) {\n\t            // Shortcut\n\t            var typedArrayByteLength = typedArray.byteLength;\n\n\t            // Extract bytes\n\t            var words = [];\n\t            for (var i = 0; i < typedArrayByteLength; i++) {\n\t                words[i >>> 2] |= typedArray[i] << (24 - (i % 4) * 8);\n\t            }\n\n\t            // Initialize this word array\n\t            superInit.call(this, words, typedArrayByteLength);\n\t        } else {\n\t            // Else call normal init\n\t            superInit.apply(this, arguments);\n\t        }\n\t    };\n\n\t    subInit.prototype = WordArray;\n\t}());\n\n\n\treturn CryptoJS.lib.WordArray;\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAE;EAC1B,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGD,OAAO,CAACG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACtD,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAEJ,OAAO,CAAC;EAC5B,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACO,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE1B,aAAY;IACT;IACA,IAAI,OAAOC,WAAW,IAAI,UAAU,EAAE;MAClC;IACJ;;IAEA;IACA,IAAIC,CAAC,GAAGF,QAAQ;IAChB,IAAIG,KAAK,GAAGD,CAAC,CAACE,GAAG;IACjB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;;IAE/B;IACA,IAAIC,SAAS,GAAGD,SAAS,CAACE,IAAI;;IAE9B;IACA,IAAIC,OAAO,GAAGH,SAAS,CAACE,IAAI,GAAG,UAAUE,UAAU,EAAE;MACjD;MACA,IAAIA,UAAU,YAAYR,WAAW,EAAE;QACnCQ,UAAU,GAAG,IAAIC,UAAU,CAACD,UAAU,CAAC;MAC3C;;MAEA;MACA,IACIA,UAAU,YAAYE,SAAS,IAC9B,OAAOC,iBAAiB,KAAK,WAAW,IAAIH,UAAU,YAAYG,iBAAkB,IACrFH,UAAU,YAAYI,UAAU,IAChCJ,UAAU,YAAYK,WAAW,IACjCL,UAAU,YAAYM,UAAU,IAChCN,UAAU,YAAYO,WAAW,IACjCP,UAAU,YAAYQ,YAAY,IAClCR,UAAU,YAAYS,YAAY,EACpC;QACET,UAAU,GAAG,IAAIC,UAAU,CAACD,UAAU,CAACU,MAAM,EAAEV,UAAU,CAACW,UAAU,EAAEX,UAAU,CAACY,UAAU,CAAC;MAChG;;MAEA;MACA,IAAIZ,UAAU,YAAYC,UAAU,EAAE;QAClC;QACA,IAAIY,oBAAoB,GAAGb,UAAU,CAACY,UAAU;;QAEhD;QACA,IAAIE,KAAK,GAAG,EAAE;QACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,oBAAoB,EAAEE,CAAC,EAAE,EAAE;UAC3CD,KAAK,CAACC,CAAC,KAAK,CAAC,CAAC,IAAIf,UAAU,CAACe,CAAC,CAAC,IAAK,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE;QACzD;;QAEA;QACAlB,SAAS,CAACmB,IAAI,CAAC,IAAI,EAAEF,KAAK,EAAED,oBAAoB,CAAC;MACrD,CAAC,MAAM;QACH;QACAhB,SAAS,CAACoB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACpC;IACJ,CAAC;IAEDnB,OAAO,CAACoB,SAAS,GAAGvB,SAAS;EACjC,CAAC,EAAC,CAAC;EAGH,OAAOL,QAAQ,CAACI,GAAG,CAACC,SAAS;AAE9B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}