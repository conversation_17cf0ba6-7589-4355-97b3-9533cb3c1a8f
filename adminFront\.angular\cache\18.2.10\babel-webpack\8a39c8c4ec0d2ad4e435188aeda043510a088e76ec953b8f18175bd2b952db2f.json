{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-json';\nimport { apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-delete-special-notice-file-post-plain';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-json';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-by-id-post-plain';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-json';\nimport { apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain } from '../fn/special-notice-file/api-special-notice-file-get-special-notice-file-list-post-plain';\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-json';\nimport { apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain } from '../fn/special-notice-file/api-special-notice-file-save-special-notice-file-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let SpecialNoticeFileService = /*#__PURE__*/(() => {\n  class SpecialNoticeFileService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiSpecialNoticeFileDeleteSpecialNoticeFilePost()` */\n    static {\n      this.ApiSpecialNoticeFileDeleteSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/DeleteSpecialNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params, context) {\n      return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain(params, context) {\n      return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params, context) {\n      return apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(params, context) {\n      return this.apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileListPost()` */\n    static {\n      this.ApiSpecialNoticeFileGetSpecialNoticeFileListPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params, context) {\n      return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain(params, context) {\n      return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params, context) {\n      return apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json(params, context) {\n      return this.apiSpecialNoticeFileGetSpecialNoticeFileListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost()` */\n    static {\n      this.ApiSpecialNoticeFileGetSpecialNoticeFileByIdPostPath = '/api/SpecialNoticeFile/GetSpecialNoticeFileById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params, context) {\n      return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain(params, context) {\n      return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params, context) {\n      return apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json(params, context) {\n      return this.apiSpecialNoticeFileGetSpecialNoticeFileByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiSpecialNoticeFileSaveSpecialNoticeFilePost()` */\n    static {\n      this.ApiSpecialNoticeFileSaveSpecialNoticeFilePostPath = '/api/SpecialNoticeFile/SaveSpecialNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params, context) {\n      return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain(params, context) {\n      return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params, context) {\n      return apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json(params, context) {\n      return this.apiSpecialNoticeFileSaveSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function SpecialNoticeFileService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || SpecialNoticeFileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: SpecialNoticeFileService,\n        factory: SpecialNoticeFileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return SpecialNoticeFileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}