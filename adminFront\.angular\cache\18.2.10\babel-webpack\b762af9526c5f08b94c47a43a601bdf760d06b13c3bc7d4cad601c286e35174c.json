{"ast": null, "code": "export var EnumStatus;\n(function (EnumStatus) {\n  EnumStatus[EnumStatus[\"Disable\"] = 0] = \"Disable\";\n  EnumStatus[EnumStatus[\"Enable\"] = 1] = \"Enable\";\n  EnumStatus[EnumStatus[\"Obsolete\"] = 2] = \"Obsolete\";\n  EnumStatus[EnumStatus[\"Locked\"] = 3] = \"Locked\";\n  EnumStatus[EnumStatus[\"Delete\"] = 9] = \"Delete\";\n})(EnumStatus || (EnumStatus = {}));\nexport class EnumStatusHelper {\n  static getDisplayName(status) {\n    switch (status) {\n      case EnumStatus.Disable:\n        return '停用';\n      case EnumStatus.Enable:\n        return '啟用';\n      case EnumStatus.Obsolete:\n        return '作廢';\n      case EnumStatus.Locked:\n        return '鎖定';\n      case EnumStatus.Delete:\n        return '刪除';\n      default:\n        return '未知';\n    }\n  }\n  static getStatusList() {\n    return [{\n      value: EnumStatus.Disable,\n      label: '停用'\n    }, {\n      value: EnumStatus.Enable,\n      label: '啟用'\n    }, {\n      value: EnumStatus.Obsolete,\n      label: '作廢'\n    }, {\n      value: EnumStatus.Locked,\n      label: '鎖定'\n    }, {\n      value: EnumStatus.Delete,\n      label: '刪除'\n    }];\n  }\n  static getStatusBadgeClass(status) {\n    switch (status) {\n      case EnumStatus.Enable:\n        return 'badge-success';\n      case EnumStatus.Disable:\n        return 'badge-secondary';\n      case EnumStatus.Obsolete:\n        return 'badge-warning';\n      case EnumStatus.Locked:\n        return 'badge-info';\n      case EnumStatus.Delete:\n        return 'badge-danger';\n      default:\n        return 'badge-light';\n    }\n  }\n}", "map": {"version": 3, "names": ["EnumStatus", "EnumStatusHelper", "getDisplayName", "status", "Disable", "Enable", "Obsolete", "Locked", "Delete", "getStatusList", "value", "label", "getStatusBadgeClass"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\shared\\enum\\enumStatus.ts"], "sourcesContent": ["export enum EnumStatus {\r\n  Disable = 0,\r\n  Enable = 1,\r\n  Obsolete = 2,\r\n  Locked = 3,\r\n  Delete = 9,\r\n}\r\n\r\nexport class EnumStatusHelper {\r\n  static getDisplayName(status: EnumStatus): string {\r\n    switch (status) {\r\n      case EnumStatus.Disable:\r\n        return '停用';\r\n      case EnumStatus.Enable:\r\n        return '啟用';\r\n      case EnumStatus.Obsolete:\r\n        return '作廢';\r\n      case EnumStatus.Locked:\r\n        return '鎖定';\r\n      case EnumStatus.Delete:\r\n        return '刪除';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n  static getStatusList(): Array<{ value: EnumStatus; label: string }> {\r\n    return [\r\n      { value: EnumStatus.Disable, label: '停用' },\r\n      { value: EnumStatus.Enable, label: '啟用' },\r\n      { value: EnumStatus.Obsolete, label: '作廢' },\r\n      { value: EnumStatus.Locked, label: '鎖定' },\r\n      { value: EnumStatus.Delete, label: '刪除' },\r\n    ];\r\n  }\r\n\r\n  static getStatusBadgeClass(status: EnumStatus): string {\r\n    switch (status) {\r\n      case EnumStatus.Enable:\r\n        return 'badge-success';\r\n      case EnumStatus.Disable:\r\n        return 'badge-secondary';\r\n      case EnumStatus.Obsolete:\r\n        return 'badge-warning';\r\n      case EnumStatus.Locked:\r\n        return 'badge-info';\r\n      case EnumStatus.Delete:\r\n        return 'badge-danger';\r\n      default:\r\n        return 'badge-light';\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAAA,WAAYA,UAMX;AAND,WAAYA,UAAU;EACpBA,UAAA,CAAAA,UAAA,4BAAW;EACXA,UAAA,CAAAA,UAAA,0BAAU;EACVA,UAAA,CAAAA,UAAA,8BAAY;EACZA,UAAA,CAAAA,UAAA,0BAAU;EACVA,UAAA,CAAAA,UAAA,0BAAU;AACZ,CAAC,EANWA,UAAU,KAAVA,UAAU;AAQtB,OAAM,MAAOC,gBAAgB;EAC3B,OAAOC,cAAcA,CAACC,MAAkB;IACtC,QAAQA,MAAM;MACZ,KAAKH,UAAU,CAACI,OAAO;QACrB,OAAO,IAAI;MACb,KAAKJ,UAAU,CAACK,MAAM;QACpB,OAAO,IAAI;MACb,KAAKL,UAAU,CAACM,QAAQ;QACtB,OAAO,IAAI;MACb,KAAKN,UAAU,CAACO,MAAM;QACpB,OAAO,IAAI;MACb,KAAKP,UAAU,CAACQ,MAAM;QACpB,OAAO,IAAI;MACb;QACE,OAAO,IAAI;IACf;EACF;EACA,OAAOC,aAAaA,CAAA;IAClB,OAAO,CACL;MAAEC,KAAK,EAAEV,UAAU,CAACI,OAAO;MAAEO,KAAK,EAAE;IAAI,CAAE,EAC1C;MAAED,KAAK,EAAEV,UAAU,CAACK,MAAM;MAAEM,KAAK,EAAE;IAAI,CAAE,EACzC;MAAED,KAAK,EAAEV,UAAU,CAACM,QAAQ;MAAEK,KAAK,EAAE;IAAI,CAAE,EAC3C;MAAED,KAAK,EAAEV,UAAU,CAACO,MAAM;MAAEI,KAAK,EAAE;IAAI,CAAE,EACzC;MAAED,KAAK,EAAEV,UAAU,CAACQ,MAAM;MAAEG,KAAK,EAAE;IAAI,CAAE,CAC1C;EACH;EAEA,OAAOC,mBAAmBA,CAACT,MAAkB;IAC3C,QAAQA,MAAM;MACZ,KAAKH,UAAU,CAACK,MAAM;QACpB,OAAO,eAAe;MACxB,KAAKL,UAAU,CAACI,OAAO;QACrB,OAAO,iBAAiB;MAC1B,KAAKJ,UAAU,CAACM,QAAQ;QACtB,OAAO,eAAe;MACxB,KAAKN,UAAU,CAACO,MAAM;QACpB,OAAO,YAAY;MACrB,KAAKP,UAAU,CAACQ,MAAM;QACpB,OAAO,cAAc;MACvB;QACE,OAAO,aAAa;IACxB;EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}