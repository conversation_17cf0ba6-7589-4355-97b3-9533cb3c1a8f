{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic [ar]\n//! author : <PERSON><PERSON> <PERSON>: https://github.com/abdelsaid\n//! author : <PERSON>\n//! author : forabi https://github.com/forabi\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '١',\n      2: '٢',\n      3: '٣',\n      4: '٤',\n      5: '٥',\n      6: '٦',\n      7: '٧',\n      8: '٨',\n      9: '٩',\n      0: '٠'\n    },\n    numberMap = {\n      '١': '1',\n      '٢': '2',\n      '٣': '3',\n      '٤': '4',\n      '٥': '5',\n      '٦': '6',\n      '٧': '7',\n      '٨': '8',\n      '٩': '9',\n      '٠': '0'\n    },\n    pluralForm = function (n) {\n      return n === 0 ? 0 : n === 1 ? 1 : n === 2 ? 2 : n % 100 >= 3 && n % 100 <= 10 ? 3 : n % 100 >= 11 ? 4 : 5;\n    },\n    plurals = {\n      s: ['أقل من ثانية', 'ثانية واحدة', ['ثانيتان', 'ثانيتين'], '%d ثوان', '%d ثانية', '%d ثانية'],\n      m: ['أقل من دقيقة', 'دقيقة واحدة', ['دقيقتان', 'دقيقتين'], '%d دقائق', '%d دقيقة', '%d دقيقة'],\n      h: ['أقل من ساعة', 'ساعة واحدة', ['ساعتان', 'ساعتين'], '%d ساعات', '%d ساعة', '%d ساعة'],\n      d: ['أقل من يوم', 'يوم واحد', ['يومان', 'يومين'], '%d أيام', '%d يومًا', '%d يوم'],\n      M: ['أقل من شهر', 'شهر واحد', ['شهران', 'شهرين'], '%d أشهر', '%d شهرا', '%d شهر'],\n      y: ['أقل من عام', 'عام واحد', ['عامان', 'عامين'], '%d أعوام', '%d عامًا', '%d عام']\n    },\n    pluralize = function (u) {\n      return function (number, withoutSuffix, string, isFuture) {\n        var f = pluralForm(number),\n          str = plurals[u][pluralForm(number)];\n        if (f === 2) {\n          str = str[withoutSuffix ? 0 : 1];\n        }\n        return str.replace(/%d/i, number);\n      };\n    },\n    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];\n  var ar = moment.defineLocale('ar', {\n    months: months,\n    monthsShort: months,\n    weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'D/\\u200FM/\\u200FYYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    meridiemParse: /ص|م/,\n    isPM: function (input) {\n      return 'م' === input;\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ص';\n      } else {\n        return 'م';\n      }\n    },\n    calendar: {\n      sameDay: '[اليوم عند الساعة] LT',\n      nextDay: '[غدًا عند الساعة] LT',\n      nextWeek: 'dddd [عند الساعة] LT',\n      lastDay: '[أمس عند الساعة] LT',\n      lastWeek: 'dddd [عند الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'بعد %s',\n      past: 'منذ %s',\n      s: pluralize('s'),\n      ss: pluralize('s'),\n      m: pluralize('m'),\n      mm: pluralize('m'),\n      h: pluralize('h'),\n      hh: pluralize('h'),\n      d: pluralize('d'),\n      dd: pluralize('d'),\n      M: pluralize('M'),\n      MM: pluralize('M'),\n      y: pluralize('y'),\n      yy: pluralize('y')\n    },\n    preparse: function (string) {\n      return string.replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n        return numberMap[match];\n      }).replace(/،/g, ',');\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      }).replace(/,/g, '،');\n    },\n    week: {\n      dow: 6,\n      // Saturday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return ar;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "symbolMap", "numberMap", "pluralForm", "n", "plurals", "s", "m", "h", "d", "M", "y", "pluralize", "u", "number", "withoutSuffix", "string", "isFuture", "f", "str", "replace", "months", "ar", "defineLocale", "monthsShort", "weekdays", "split", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "meridiem", "hour", "minute", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "ss", "mm", "hh", "dd", "MM", "yy", "preparse", "match", "postformat", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/ar.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Arabic [ar]\n//! author : <PERSON><PERSON> <PERSON>: https://github.com/abdelsaid\n//! author : <PERSON>\n//! author : forabi https://github.com/forabi\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var symbolMap = {\n            1: '١',\n            2: '٢',\n            3: '٣',\n            4: '٤',\n            5: '٥',\n            6: '٦',\n            7: '٧',\n            8: '٨',\n            9: '٩',\n            0: '٠',\n        },\n        numberMap = {\n            '١': '1',\n            '٢': '2',\n            '٣': '3',\n            '٤': '4',\n            '٥': '5',\n            '٦': '6',\n            '٧': '7',\n            '٨': '8',\n            '٩': '9',\n            '٠': '0',\n        },\n        pluralForm = function (n) {\n            return n === 0\n                ? 0\n                : n === 1\n                ? 1\n                : n === 2\n                ? 2\n                : n % 100 >= 3 && n % 100 <= 10\n                ? 3\n                : n % 100 >= 11\n                ? 4\n                : 5;\n        },\n        plurals = {\n            s: [\n                'أقل من ثانية',\n                'ثانية واحدة',\n                ['ثانيتان', 'ثانيتين'],\n                '%d ثوان',\n                '%d ثانية',\n                '%d ثانية',\n            ],\n            m: [\n                'أقل من دقيقة',\n                'دقيقة واحدة',\n                ['دقيقتان', 'دقيقتين'],\n                '%d دقائق',\n                '%d دقيقة',\n                '%d دقيقة',\n            ],\n            h: [\n                'أقل من ساعة',\n                'ساعة واحدة',\n                ['ساعتان', 'ساعتين'],\n                '%d ساعات',\n                '%d ساعة',\n                '%d ساعة',\n            ],\n            d: [\n                'أقل من يوم',\n                'يوم واحد',\n                ['يومان', 'يومين'],\n                '%d أيام',\n                '%d يومًا',\n                '%d يوم',\n            ],\n            M: [\n                'أقل من شهر',\n                'شهر واحد',\n                ['شهران', 'شهرين'],\n                '%d أشهر',\n                '%d شهرا',\n                '%d شهر',\n            ],\n            y: [\n                'أقل من عام',\n                'عام واحد',\n                ['عامان', 'عامين'],\n                '%d أعوام',\n                '%d عامًا',\n                '%d عام',\n            ],\n        },\n        pluralize = function (u) {\n            return function (number, withoutSuffix, string, isFuture) {\n                var f = pluralForm(number),\n                    str = plurals[u][pluralForm(number)];\n                if (f === 2) {\n                    str = str[withoutSuffix ? 0 : 1];\n                }\n                return str.replace(/%d/i, number);\n            };\n        },\n        months = [\n            'يناير',\n            'فبراير',\n            'مارس',\n            'أبريل',\n            'مايو',\n            'يونيو',\n            'يوليو',\n            'أغسطس',\n            'سبتمبر',\n            'أكتوبر',\n            'نوفمبر',\n            'ديسمبر',\n        ];\n\n    var ar = moment.defineLocale('ar', {\n        months: months,\n        monthsShort: months,\n        weekdays: 'الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n        weekdaysShort: 'أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت'.split('_'),\n        weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'D/\\u200FM/\\u200FYYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        meridiemParse: /ص|م/,\n        isPM: function (input) {\n            return 'م' === input;\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 12) {\n                return 'ص';\n            } else {\n                return 'م';\n            }\n        },\n        calendar: {\n            sameDay: '[اليوم عند الساعة] LT',\n            nextDay: '[غدًا عند الساعة] LT',\n            nextWeek: 'dddd [عند الساعة] LT',\n            lastDay: '[أمس عند الساعة] LT',\n            lastWeek: 'dddd [عند الساعة] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'بعد %s',\n            past: 'منذ %s',\n            s: pluralize('s'),\n            ss: pluralize('s'),\n            m: pluralize('m'),\n            mm: pluralize('m'),\n            h: pluralize('h'),\n            hh: pluralize('h'),\n            d: pluralize('d'),\n            dd: pluralize('d'),\n            M: pluralize('M'),\n            MM: pluralize('M'),\n            y: pluralize('y'),\n            yy: pluralize('y'),\n        },\n        preparse: function (string) {\n            return string\n                .replace(/[١٢٣٤٥٦٧٨٩٠]/g, function (match) {\n                    return numberMap[match];\n                })\n                .replace(/،/g, ',');\n        },\n        postformat: function (string) {\n            return string\n                .replace(/\\d/g, function (match) {\n                    return symbolMap[match];\n                })\n                .replace(/,/g, '،');\n        },\n        week: {\n            dow: 6, // Saturday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return ar;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,SAAS,GAAG;MACR,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE,GAAG;MACN,CAAC,EAAE;IACP,CAAC;IACDC,SAAS,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE,GAAG;MACR,GAAG,EAAE;IACT,CAAC;IACDC,UAAU,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACtB,OAAOA,CAAC,KAAK,CAAC,GACR,CAAC,GACDA,CAAC,KAAK,CAAC,GACP,CAAC,GACDA,CAAC,KAAK,CAAC,GACP,CAAC,GACDA,CAAC,GAAG,GAAG,IAAI,CAAC,IAAIA,CAAC,GAAG,GAAG,IAAI,EAAE,GAC7B,CAAC,GACDA,CAAC,GAAG,GAAG,IAAI,EAAE,GACb,CAAC,GACD,CAAC;IACX,CAAC;IACDC,OAAO,GAAG;MACNC,CAAC,EAAE,CACC,cAAc,EACd,aAAa,EACb,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB,SAAS,EACT,UAAU,EACV,UAAU,CACb;MACDC,CAAC,EAAE,CACC,cAAc,EACd,aAAa,EACb,CAAC,SAAS,EAAE,SAAS,CAAC,EACtB,UAAU,EACV,UAAU,EACV,UAAU,CACb;MACDC,CAAC,EAAE,CACC,aAAa,EACb,YAAY,EACZ,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACpB,UAAU,EACV,SAAS,EACT,SAAS,CACZ;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,SAAS,EACT,UAAU,EACV,QAAQ,CACX;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,SAAS,EACT,SAAS,EACT,QAAQ,CACX;MACDC,CAAC,EAAE,CACC,YAAY,EACZ,UAAU,EACV,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,UAAU,EACV,UAAU,EACV,QAAQ;IAEhB,CAAC;IACDC,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAE;MACrB,OAAO,UAAUC,MAAM,EAAEC,aAAa,EAAEC,MAAM,EAAEC,QAAQ,EAAE;QACtD,IAAIC,CAAC,GAAGf,UAAU,CAACW,MAAM,CAAC;UACtBK,GAAG,GAAGd,OAAO,CAACQ,CAAC,CAAC,CAACV,UAAU,CAACW,MAAM,CAAC,CAAC;QACxC,IAAII,CAAC,KAAK,CAAC,EAAE;UACTC,GAAG,GAAGA,GAAG,CAACJ,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC;QACA,OAAOI,GAAG,CAACC,OAAO,CAAC,KAAK,EAAEN,MAAM,CAAC;MACrC,CAAC;IACL,CAAC;IACDO,MAAM,GAAG,CACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,CACX;EAEL,IAAIC,EAAE,GAAGtB,MAAM,CAACuB,YAAY,CAAC,IAAI,EAAE;IAC/BF,MAAM,EAAEA,MAAM;IACdG,WAAW,EAAEH,MAAM;IACnBI,QAAQ,EAAE,qDAAqD,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1EC,aAAa,EAAE,uCAAuC,CAACD,KAAK,CAAC,GAAG,CAAC;IACjEE,WAAW,EAAE,eAAe,CAACF,KAAK,CAAC,GAAG,CAAC;IACvCG,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,sBAAsB;MACzBC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,KAAK;IACpBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,GAAG,KAAKA,KAAK;IACxB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,EAAE,EAAE;QACX,OAAO,GAAG;MACd,CAAC,MAAM;QACH,OAAO,GAAG;MACd;IACJ,CAAC;IACDG,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,sBAAsB;MAC/BC,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,QAAQ;MAChBC,IAAI,EAAE,QAAQ;MACd/C,CAAC,EAAEM,SAAS,CAAC,GAAG,CAAC;MACjB0C,EAAE,EAAE1C,SAAS,CAAC,GAAG,CAAC;MAClBL,CAAC,EAAEK,SAAS,CAAC,GAAG,CAAC;MACjB2C,EAAE,EAAE3C,SAAS,CAAC,GAAG,CAAC;MAClBJ,CAAC,EAAEI,SAAS,CAAC,GAAG,CAAC;MACjB4C,EAAE,EAAE5C,SAAS,CAAC,GAAG,CAAC;MAClBH,CAAC,EAAEG,SAAS,CAAC,GAAG,CAAC;MACjB6C,EAAE,EAAE7C,SAAS,CAAC,GAAG,CAAC;MAClBF,CAAC,EAAEE,SAAS,CAAC,GAAG,CAAC;MACjB8C,EAAE,EAAE9C,SAAS,CAAC,GAAG,CAAC;MAClBD,CAAC,EAAEC,SAAS,CAAC,GAAG,CAAC;MACjB+C,EAAE,EAAE/C,SAAS,CAAC,GAAG;IACrB,CAAC;IACDgD,QAAQ,EAAE,SAAAA,CAAU5C,MAAM,EAAE;MACxB,OAAOA,MAAM,CACRI,OAAO,CAAC,eAAe,EAAE,UAAUyC,KAAK,EAAE;QACvC,OAAO3D,SAAS,CAAC2D,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDzC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACD0C,UAAU,EAAE,SAAAA,CAAU9C,MAAM,EAAE;MAC1B,OAAOA,MAAM,CACRI,OAAO,CAAC,KAAK,EAAE,UAAUyC,KAAK,EAAE;QAC7B,OAAO5D,SAAS,CAAC4D,KAAK,CAAC;MAC3B,CAAC,CAAC,CACDzC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC3B,CAAC;IACD2C,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAO3C,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}