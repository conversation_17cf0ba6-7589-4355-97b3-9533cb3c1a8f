{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BaseLabelDirective {\n  constructor(el, rennder) {\n    this.el = el;\n    this.rennder = rennder;\n  }\n  ngOnInit() {\n    this.rennder.addClass(this.el.nativeElement, 'mr-2');\n    this.rennder.addClass(this.el.nativeElement, 'label');\n  }\n  static {\n    this.ɵfac = function BaseLabelDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BaseLabelDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: BaseLabelDirective,\n      selectors: [[\"\", \"baseLabel\", \"\"]],\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseLabelDirective", "constructor", "el", "rennder", "ngOnInit", "addClass", "nativeElement", "i0", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "selectors", "standalone"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\directives\\label.directive.ts"], "sourcesContent": ["import { Directive, ElementRef, OnInit, Renderer2 } from '@angular/core';\r\n\r\n@Directive({\r\n  selector: '[baseLabel]',\r\n  standalone: true\r\n})\r\nexport class BaseLabelDirective implements OnInit{\r\n\r\n  constructor(\r\n    private el: ElementRef,\r\n    private rennder: Renderer2\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.rennder.addClass(this.el.nativeElement,  'mr-2')\r\n    this.rennder.addClass(this.el.nativeElement, 'label')\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAMA,OAAM,MAAOA,kBAAkB;EAE7BC,YACUC,EAAc,EACdC,OAAkB;IADlB,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,OAAO,GAAPA,OAAO;EACd;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,OAAO,CAACE,QAAQ,CAAC,IAAI,CAACH,EAAE,CAACI,aAAa,EAAG,MAAM,CAAC;IACrD,IAAI,CAACH,OAAO,CAACE,QAAQ,CAAC,IAAI,CAACH,EAAE,CAACI,aAAa,EAAE,OAAO,CAAC;EACvD;;;uCAVWN,kBAAkB,EAAAO,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,SAAA;IAAA;EAAA;;;YAAlBV,kBAAkB;MAAAW,SAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}