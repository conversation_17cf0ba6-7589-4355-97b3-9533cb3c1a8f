{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Arabic (Kuwait) [ar-kw]\n//! author : <PERSON><PERSON><PERSON>: https://github.com/nusretparlak\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var arKw = moment.defineLocale('ar-kw', {\n    months: 'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split('_'),\n    monthsShort: 'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split('_'),\n    weekdays: 'الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n    weekdaysShort: 'احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت'.split('_'),\n    weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY HH:mm',\n      LLLL: 'dddd D MMMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[اليوم على الساعة] LT',\n      nextDay: '[غدا على الساعة] LT',\n      nextWeek: 'dddd [على الساعة] LT',\n      lastDay: '[أمس على الساعة] LT',\n      lastWeek: 'dddd [على الساعة] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'في %s',\n      past: 'منذ %s',\n      s: 'ثوان',\n      ss: '%d ثانية',\n      m: 'دقيقة',\n      mm: '%d دقائق',\n      h: 'ساعة',\n      hh: '%d ساعات',\n      d: 'يوم',\n      dd: '%d أيام',\n      M: 'شهر',\n      MM: '%d أشهر',\n      y: 'سنة',\n      yy: '%d سنوات'\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 12 // The week that contains Jan 12th is the first week of the year.\n    }\n  });\n  return arKw;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "arKw", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "weekdaysParseExact", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "week", "dow", "doy"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/moment/locale/ar-kw.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Arabic (Kuwait) [ar-kw]\n//! author : <PERSON><PERSON><PERSON>: https://github.com/nusretparlak\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var arKw = moment.defineLocale('ar-kw', {\n        months: 'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split(\n            '_'\n        ),\n        monthsShort:\n            'يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر'.split(\n                '_'\n            ),\n        weekdays: 'الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت'.split('_'),\n        weekdaysShort: 'احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت'.split('_'),\n        weekdaysMin: 'ح_ن_ث_ر_خ_ج_س'.split('_'),\n        weekdaysParseExact: true,\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D MMMM YYYY',\n            LLL: 'D MMMM YYYY HH:mm',\n            LLLL: 'dddd D MMMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[اليوم على الساعة] LT',\n            nextDay: '[غدا على الساعة] LT',\n            nextWeek: 'dddd [على الساعة] LT',\n            lastDay: '[أمس على الساعة] LT',\n            lastWeek: 'dddd [على الساعة] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'في %s',\n            past: 'منذ %s',\n            s: 'ثوان',\n            ss: '%d ثانية',\n            m: 'دقيقة',\n            mm: '%d دقائق',\n            h: 'ساعة',\n            hh: '%d ساعات',\n            d: 'يوم',\n            dd: '%d أيام',\n            M: 'شهر',\n            MM: '%d أشهر',\n            y: 'سنة',\n            yy: '%d سنوات',\n        },\n        week: {\n            dow: 0, // Sunday is the first day of the week.\n            doy: 12, // The week that contains Jan 12th is the first week of the year.\n        },\n    });\n\n    return arKw;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,IAAI,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,EAAE;IACpCC,MAAM,EAAE,uEAAuE,CAACC,KAAK,CACjF,GACJ,CAAC;IACDC,WAAW,EACP,uEAAuE,CAACD,KAAK,CACzE,GACJ,CAAC;IACLE,QAAQ,EAAE,qDAAqD,CAACF,KAAK,CAAC,GAAG,CAAC;IAC1EG,aAAa,EAAE,uCAAuC,CAACH,KAAK,CAAC,GAAG,CAAC;IACjEI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,kBAAkB,EAAE,IAAI;IACxBC,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,aAAa;MACjBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,uBAAuB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,OAAO,EAAE,qBAAqB;MAC9BC,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,QAAQ;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,OAAO;MACVC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE;IACR,CAAC;IACDC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,EAAE,CAAE;IACb;EACJ,CAAC,CAAC;EAEF,OAAOxC,IAAI;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}