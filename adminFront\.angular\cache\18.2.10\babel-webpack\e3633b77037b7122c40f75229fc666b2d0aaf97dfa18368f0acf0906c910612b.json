{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'json',\n    accept: 'text/json',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiSpecialNoticeFileDeleteSpecialNoticeFilePost$Json.PATH = '/api/SpecialNoticeFile/DeleteSpecialNoticeFile';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}