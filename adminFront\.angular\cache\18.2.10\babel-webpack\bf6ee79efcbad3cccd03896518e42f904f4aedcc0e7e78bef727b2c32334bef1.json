{"ast": null, "code": "import * as i0 from \"@angular/core\";\n/**\n * Global configuration\n */\nexport let ApiConfiguration = /*#__PURE__*/(() => {\n  class ApiConfiguration {\n    constructor() {\n      this.rootUrl = '';\n    }\n    static {\n      this.ɵfac = function ApiConfiguration_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ApiConfiguration)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ApiConfiguration,\n        factory: ApiConfiguration.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ApiConfiguration;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}