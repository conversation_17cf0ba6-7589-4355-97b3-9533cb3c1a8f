{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n  constructor(schedulerActionCtor = VirtualAction, maxFrames = Infinity) {\n    super(schedulerActionCtor, () => this.frame);\n    this.maxFrames = maxFrames;\n    this.frame = 0;\n    this.index = -1;\n  }\n  flush() {\n    const {\n      actions,\n      maxFrames\n    } = this;\n    let error;\n    let action;\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n  constructor(scheduler, work, index = scheduler.index += 1) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.index = index;\n    this.active = true;\n    this.index = scheduler.index = index;\n  }\n  schedule(state, delay = 0) {\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return super.schedule(state, delay);\n      }\n      this.active = false;\n      const action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    this.delay = scheduler.frame + delay;\n    const {\n      actions\n    } = scheduler;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    return undefined;\n  }\n  _execute(state, delay) {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n  static sortActions(a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "Subscription", "AsyncScheduler", "VirtualTimeScheduler", "constructor", "schedulerActionCtor", "VirtualAction", "maxFrames", "Infinity", "frame", "index", "flush", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "Number", "isFinite", "id", "add", "EMPTY", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["C:/Users/<USER>/Documents/jeansalechange/adminFront/node_modules/rxjs/dist/esm/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n    constructor(schedulerActionCtor = VirtualAction, maxFrames = Infinity) {\n        super(schedulerActionCtor, () => this.frame);\n        this.maxFrames = maxFrames;\n        this.frame = 0;\n        this.index = -1;\n    }\n    flush() {\n        const { actions, maxFrames } = this;\n        let error;\n        let action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        }\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n    constructor(scheduler, work, index = (scheduler.index += 1)) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.index = index;\n        this.active = true;\n        this.index = scheduler.index = index;\n    }\n    schedule(state, delay = 0) {\n        if (Number.isFinite(delay)) {\n            if (!this.id) {\n                return super.schedule(state, delay);\n            }\n            this.active = false;\n            const action = new VirtualAction(this.scheduler, this.work);\n            this.add(action);\n            return action.schedule(state, delay);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        this.delay = scheduler.frame + delay;\n        const { actions } = scheduler;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return 1;\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        return undefined;\n    }\n    _execute(state, delay) {\n        if (this.active === true) {\n            return super._execute(state, delay);\n        }\n    }\n    static sortActions(a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,oBAAoB,SAASD,cAAc,CAAC;EACrDE,WAAWA,CAACC,mBAAmB,GAAGC,aAAa,EAAEC,SAAS,GAAGC,QAAQ,EAAE;IACnE,KAAK,CAACH,mBAAmB,EAAE,MAAM,IAAI,CAACI,KAAK,CAAC;IAC5C,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACAC,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEC,OAAO;MAAEL;IAAU,CAAC,GAAG,IAAI;IACnC,IAAIM,KAAK;IACT,IAAIC,MAAM;IACV,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAC,CAAC,KAAKE,MAAM,CAACC,KAAK,IAAIR,SAAS,EAAE;MACvDK,OAAO,CAACI,KAAK,CAAC,CAAC;MACf,IAAI,CAACP,KAAK,GAAGK,MAAM,CAACC,KAAK;MACzB,IAAKF,KAAK,GAAGC,MAAM,CAACG,OAAO,CAACH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACC,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ;IACA,IAAIF,KAAK,EAAE;MACP,OAAQC,MAAM,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAG;QAC/BF,MAAM,CAACK,WAAW,CAAC,CAAC;MACxB;MACA,MAAMN,KAAK;IACf;EACJ;AACJ;AACAV,oBAAoB,CAACiB,eAAe,GAAG,EAAE;AACzC,OAAO,MAAMd,aAAa,SAASN,WAAW,CAAC;EAC3CI,WAAWA,CAACiB,SAAS,EAAEC,IAAI,EAAEZ,KAAK,GAAIW,SAAS,CAACX,KAAK,IAAI,CAAE,EAAE;IACzD,KAAK,CAACW,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACZ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACa,MAAM,GAAG,IAAI;IAClB,IAAI,CAACb,KAAK,GAAGW,SAAS,CAACX,KAAK,GAAGA,KAAK;EACxC;EACAc,QAAQA,CAACN,KAAK,EAAEH,KAAK,GAAG,CAAC,EAAE;IACvB,IAAIU,MAAM,CAACC,QAAQ,CAACX,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC,IAAI,CAACY,EAAE,EAAE;QACV,OAAO,KAAK,CAACH,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;MACvC;MACA,IAAI,CAACQ,MAAM,GAAG,KAAK;MACnB,MAAMT,MAAM,GAAG,IAAIR,aAAa,CAAC,IAAI,CAACe,SAAS,EAAE,IAAI,CAACC,IAAI,CAAC;MAC3D,IAAI,CAACM,GAAG,CAACd,MAAM,CAAC;MAChB,OAAOA,MAAM,CAACU,QAAQ,CAACN,KAAK,EAAEH,KAAK,CAAC;IACxC,CAAC,MACI;MACD,OAAOd,YAAY,CAAC4B,KAAK;IAC7B;EACJ;EACAC,cAAcA,CAACT,SAAS,EAAEM,EAAE,EAAEZ,KAAK,GAAG,CAAC,EAAE;IACrC,IAAI,CAACA,KAAK,GAAGM,SAAS,CAACZ,KAAK,GAAGM,KAAK;IACpC,MAAM;MAAEH;IAAQ,CAAC,GAAGS,SAAS;IAC7BT,OAAO,CAACmB,IAAI,CAAC,IAAI,CAAC;IAClBnB,OAAO,CAACoB,IAAI,CAAC1B,aAAa,CAAC2B,WAAW,CAAC;IACvC,OAAO,CAAC;EACZ;EACAC,cAAcA,CAACb,SAAS,EAAEM,EAAE,EAAEZ,KAAK,GAAG,CAAC,EAAE;IACrC,OAAOoB,SAAS;EACpB;EACAC,QAAQA,CAAClB,KAAK,EAAEH,KAAK,EAAE;IACnB,IAAI,IAAI,CAACQ,MAAM,KAAK,IAAI,EAAE;MACtB,OAAO,KAAK,CAACa,QAAQ,CAAClB,KAAK,EAAEH,KAAK,CAAC;IACvC;EACJ;EACA,OAAOkB,WAAWA,CAACI,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAID,CAAC,CAACtB,KAAK,KAAKuB,CAAC,CAACvB,KAAK,EAAE;MACrB,IAAIsB,CAAC,CAAC3B,KAAK,KAAK4B,CAAC,CAAC5B,KAAK,EAAE;QACrB,OAAO,CAAC;MACZ,CAAC,MACI,IAAI2B,CAAC,CAAC3B,KAAK,GAAG4B,CAAC,CAAC5B,KAAK,EAAE;QACxB,OAAO,CAAC;MACZ,CAAC,MACI;QACD,OAAO,CAAC,CAAC;MACb;IACJ,CAAC,MACI,IAAI2B,CAAC,CAACtB,KAAK,GAAGuB,CAAC,CAACvB,KAAK,EAAE;MACxB,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,CAAC,CAAC;IACb;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}