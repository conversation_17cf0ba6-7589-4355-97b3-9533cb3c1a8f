{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TimingPipe {\n  transform(time) {\n    if (time) {\n      const minutes = Math.floor(time / 60);\n      const seconds = Math.floor(time % 60);\n      return `${this.initZero(minutes)}${minutes}:${this.initZero(seconds)}${seconds}`;\n    }\n    return '00:00';\n  }\n  initZero(time) {\n    return time < 10 ? '0' : '';\n  }\n  static {\n    this.ɵfac = function TimingPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TimingPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"timing\",\n      type: TimingPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\nexport class FormatHourPipe {\n  transform(value, ...args) {\n    if (value === 24) {\n      return '00:00';\n    } else {\n      return `${value}:00`;\n    }\n  }\n  static {\n    this.ɵfac = function FormatHourPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormatHourPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"hour\",\n      type: FormatHourPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "time", "minutes", "Math", "floor", "seconds", "initZero", "pure", "standalone", "FormatHourPipe", "value", "args"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@theme\\pipes\\timing.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n    name: 'timing',\r\n    standalone: true\r\n})\r\nexport class TimingPipe implements PipeTransform {\r\n  transform(time: number): string {\r\n    if (time) {\r\n      const minutes = Math.floor(time / 60);\r\n      const seconds = Math.floor(time % 60);\r\n      return `${this.initZero(minutes)}${minutes}:${this.initZero(seconds)}${seconds}`;\r\n    }\r\n\r\n    return '00:00';\r\n  }\r\n\r\n  private initZero(time: number): string {\r\n    return time < 10 ? '0' : '';\r\n  }\r\n}\r\n\r\n@Pipe({\r\n  name: 'hour',\r\n  standalone: true\r\n})\r\nexport class FormatHourPipe implements PipeTransform {\r\n  transform(value: number | undefined, ...args: any[]) {\r\n    if(value === 24) {\r\n      return '00:00'\r\n    } else {\r\n      return `${value}:00`\r\n    }\r\n  }\r\n}"], "mappings": ";AAMA,OAAM,MAAOA,UAAU;EACrBC,SAASA,CAACC,IAAY;IACpB,IAAIA,IAAI,EAAE;MACR,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAG,EAAE,CAAC;MACrC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,GAAG,EAAE,CAAC;MACrC,OAAO,GAAG,IAAI,CAACK,QAAQ,CAACJ,OAAO,CAAC,GAAGA,OAAO,IAAI,IAAI,CAACI,QAAQ,CAACD,OAAO,CAAC,GAAGA,OAAO,EAAE;IAClF;IAEA,OAAO,OAAO;EAChB;EAEQC,QAAQA,CAACL,IAAY;IAC3B,OAAOA,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE;EAC7B;;;uCAbWF,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAQ,IAAA;MAAAC,UAAA;IAAA;EAAA;;AAoBvB,OAAM,MAAOC,cAAc;EACzBT,SAASA,CAACU,KAAyB,EAAE,GAAGC,IAAW;IACjD,IAAGD,KAAK,KAAK,EAAE,EAAE;MACf,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,GAAGA,KAAK,KAAK;IACtB;EACF;;;uCAPWD,cAAc;IAAA;EAAA;;;;YAAdA,cAAc;MAAAF,IAAA;MAAAC,UAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}