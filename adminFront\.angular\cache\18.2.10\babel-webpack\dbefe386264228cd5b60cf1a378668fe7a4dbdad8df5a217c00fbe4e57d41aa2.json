{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { decodeJwtPayload } from '@nebular/auth';\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/services/api/services\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/shared/services/utility.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"src/app/shared/services/message.service\";\nimport * as i6 from \"src/app/shared/helper/validationHelper\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"src/services/api/services/File.service\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@nebular/theme\";\nimport * as i11 from \"../../../@theme/pipes/mapping.pipe\";\nfunction DetailApprovalWaitingComponent_div_2_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3, \"\\u7121\\u9644\\u4EF6\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_div_30_Template_div_click_0_listener() {\n      const item_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.handleFileClick(item_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 27)(5, \"div\", 5)(6, \"span\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 31);\n    i0.ɵɵelement(13, \"i\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r3.getFileIcon(item_r3.CFileName));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", item_r3.CFileName || \"\\u672A\\u547D\\u540D\\u6A94\\u6848\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFileTypeText(item_r3.CFileName), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u9EDE\\u64CA\\u4EE5\", ctx_r3.isImageFile(item_r3.CFileName || \"\") ? \"\\u9810\\u89BD\" : ctx_r3.isPDFString(item_r3.CFileName || \"\") ? \"\\u6AA2\\u8996\" : \"\\u4E0B\\u8F09\", \"\\u6A94\\u6848 \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 39)(1, \"th\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\", 41);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 41);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const record_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 6, record_r5.CRecordDate ? record_r5.CRecordDate : ctx_r3.approvalWaiting.CCreateDT, \"yyyy/MM/dd HH:mm:ss\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" \", record_r5.CAction === 1 ? \"\\u9001\\u51FA\\u5BE9\\u6838\" : \"\", \" \", record_r5.CAction === 2 ? \"\\u5BE9\\u6838\\u901A\\u904E\" : \"\", \" \", record_r5.CAction === 3 ? \"\\u5BE9\\u6838\\u99C1\\u56DE\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CCreator, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", record_r5.CRemark, \" \");\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"table\", 34)(2, \"thead\", 35)(3, \"tr\", 36)(4, \"th\", 37);\n    i0.ɵɵtext(5, \" \\u65E5\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 37);\n    i0.ɵɵtext(7, \" \\u52D5\\u4F5C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 37);\n    i0.ɵɵtext(9, \" \\u4F7F\\u7528\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 37);\n    i0.ɵɵtext(11, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"tbody\");\n    i0.ɵɵtemplate(13, DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template, 10, 9, \"tr\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CApproveRecord);\n  }\n}\nfunction DetailApprovalWaitingComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"span\", 7);\n    i0.ɵɵtext(8, \" \\u5EFA\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 6)(13, \"span\", 7);\n    i0.ɵɵtext(14, \" \\u985E\\u5225 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 8);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"getTypeApprovalWaiting\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 6)(20, \"span\", 7);\n    i0.ɵɵtext(21, \" \\u540D\\u7A31 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"span\", 8);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\", 6)(26, \"span\", 7);\n    i0.ɵɵtext(27, \" \\u6A94\\u6848 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 8);\n    i0.ɵɵtemplate(29, DetailApprovalWaitingComponent_div_2_div_29_Template, 4, 0, \"div\", 11)(30, DetailApprovalWaitingComponent_div_2_div_30_Template, 14, 5, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 9)(32, \"div\", 6)(33, \"span\", 7);\n    i0.ɵɵtext(34, \" \\u5BE9\\u6838\\u8AAA\\u660E \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"span\", 8);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 13)(38, \"div\", 14)(39, \"div\", 6)(40, \"span\", 7);\n    i0.ɵɵtext(41, \" \\u5099\\u8A3B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 15)(43, \"textarea\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r3.remark, $event) || (ctx_r3.remark = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 17)(45, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.goBack());\n    });\n    i0.ɵɵtext(46, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(false));\n    });\n    i0.ɵɵtext(48, \"\\u99C1\\u56DE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DetailApprovalWaitingComponent_div_2_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleAction(true));\n    });\n    i0.ɵɵtext(50, \"\\u540C\\u610F\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, DetailApprovalWaitingComponent_div_2_div_51_Template, 14, 1, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CBuildcaseName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 12, ctx_r3.approvalWaiting.CType), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CName, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.approvalWaiting.CFileApproves || ctx_r3.approvalWaiting.CFileApproves.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.approvalWaiting.CFileApproves);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.approvalWaiting.CApprovalRemark, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.remark);\n    i0.ɵɵproperty(\"rows\", 4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.approvalWaiting.CIsApprove !== null && ctx_r3.approvalWaiting.CIsApprove);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r3.approvalWaiting);\n  }\n}\nexport class DetailApprovalWaitingComponent {\n  constructor(_specialChangeService, _activatedRoute, _ultilityService, _location, message, _validationHelper, _eventService, fileService) {\n    this._specialChangeService = _specialChangeService;\n    this._activatedRoute = _activatedRoute;\n    this._ultilityService = _ultilityService;\n    this._location = _location;\n    this.message = message;\n    this._validationHelper = _validationHelper;\n    this._eventService = _eventService;\n    this.fileService = fileService;\n    this.CType = 1;\n    this.remark = \"\";\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN));\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"id\"));\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap.get(\"buildCaseId\"));\n    this._activatedRoute.queryParams.pipe(tap(p => {\n      this.CType = p[\"type\"];\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.getApprovalWaitingById();\n  }\n  getApprovalWaitingById() {\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType.toString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.approvalWaiting = res.Entries;\n      }\n    })).subscribe();\n  }\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\n  handleFileClick(file) {\n    const fileName = file.CFileName || '';\n    const displayName = fileName;\n    // 判斷檔案類型\n    const isImageByName = this.isImageFile(fileName);\n    const isPdfByName = this.isPDFString(fileName);\n    const isCadByName = this.isCadString(fileName);\n    console.log('handleFileClick - 處理檔案:', fileName, 'file:', file);\n    // 統一使用 GetFile API 取得檔案（非 base64 才呼叫）\n    const relativePath = file.relativePath || file.CFile;\n    const serverFileName = file.fileName || file.CFileName;\n    if (relativePath && serverFileName && !this.isBase64String(relativePath)) {\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\n    } else {\n      // 如果沒有路徑資訊或是 base64，使用本地檔案處理邏輯作為後備方案\n      console.warn('檔案缺少路徑資訊或為 base64，使用本地處理:', file);\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\n    }\n  }\n  // 判斷是否為 base64 字串\n  isBase64String(str) {\n    if (!str) return false;\n    // 簡單檢查是否為 base64 格式\n    return str.startsWith('data:') || str.length > 100 && /^[A-Za-z0-9+/=]+$/.test(str);\n  }\n  // 處理本地檔案的後備方法\n  handleLocalFile(file, isImage, isPdf, isCad) {\n    const fileUrl = file.CFile || file.data;\n    const fileName = file.CFileName || file.fileName || '';\n    if (isImage) {\n      const imageUrl = this.getImageSrc(file);\n      this.openImagePreview(imageUrl, fileName);\n    } else if (isPdf) {\n      // 使用 base64 處理 PDF\n      this.openPdfWithBase64(fileUrl, fileName);\n    } else {\n      this.downloadFileDirectly(fileUrl, fileName);\n    }\n  }\n  // 從後端取得檔案 blob\n  getFileFromServer(relativePath, fileName, displayName, isImage, isPdf, isCad) {\n    this.fileService.getFile(relativePath, fileName).subscribe({\n      next: blob => {\n        const url = URL.createObjectURL(blob);\n        if (isImage) {\n          // 圖片預覽\n          this.openImagePreview(url, displayName);\n        } else if (isPdf) {\n          // PDF 檔案另開視窗顯示\n          this.openPdfInNewWindow(url, displayName);\n        } else {\n          // 其他檔案直接下載\n          this.downloadBlobFile(blob, displayName);\n        }\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\n      },\n      error: error => {\n        console.error('取得檔案失敗:', error);\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\n      }\n    });\n  }\n  // 在新視窗中開啟 PDF\n  openPdfInNewWindow(blobUrl, fileName) {\n    try {\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.location.href = blobUrl;\n      }\n    } catch (error) {\n      console.error('開啟 PDF 視窗失敗:', error);\n      // 後備方案：直接開啟 URL\n      window.open(blobUrl, '_blank');\n    }\n  }\n  // 使用 base64 開啟 PDF（本地檔案後備方案）\n  openPdfWithBase64(fileData, fileName) {\n    try {\n      let pdfUrl = fileData;\n      // 如果是 base64，需要轉換為 blob URL\n      if (!fileData.startsWith('http')) {\n        if (fileData.startsWith('data:application/pdf')) {\n          pdfUrl = fileData;\n        } else {\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\n        }\n      }\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body { margin: 0; padding: 0; }\n                iframe { width: 100vw; height: 100vh; border: none; }\n              </style>\n            </head>\n            <body>\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        window.open(pdfUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('打開 PDF 時發生錯誤:', error);\n      this.message.showErrorMSG('打開 PDF 失敗');\n    }\n  }\n  // 下載 blob 檔案\n  downloadBlobFile(blob, fileName) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = fileName;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    // 清理 URL\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\n  }\n  // 下載檔案（處理 base64 或 URL）\n  downloadFileDirectly(fileData, fileName) {\n    if (!fileData) {\n      this.message.showErrorMSG('檔案資料不存在');\n      return;\n    }\n    if (this.isBase64String(fileData)) {\n      this.downloadBase64File(fileData, fileName);\n    } else {\n      // 假設是 URL，嘗試直接下載\n      window.open(fileData, '_blank');\n    }\n  }\n  // 下載 base64 檔案\n  downloadBase64File(base64Data, fileName) {\n    try {\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\n      // 從檔案名稱判斷 MIME 類型\n      let mimeType = 'application/octet-stream';\n      const extension = fileName.split('.').pop()?.toLowerCase();\n      switch (extension) {\n        case 'pdf':\n          mimeType = 'application/pdf';\n          break;\n        case 'jpg':\n        case 'jpeg':\n          mimeType = 'image/jpeg';\n          break;\n        case 'png':\n          mimeType = 'image/png';\n          break;\n        case 'dwg':\n          mimeType = 'application/acad';\n          break;\n        case 'dxf':\n          mimeType = 'application/dxf';\n          break;\n      }\n      // 將 base64 轉換為 Blob\n      const byteCharacters = atob(base64Content);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: mimeType\n      });\n      // 創建下載連結\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      // 清理 URL\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\n    } catch (error) {\n      console.error('處理 base64 檔案時發生錯誤:', error);\n      this.message.showErrorMSG('處理檔案時發生錯誤');\n    }\n  }\n  // 判斷檔案是否為圖片（根據檔名）\n  isImageFile(fileName) {\n    if (!fileName) return false;\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n    const extension = fileName.split('.').pop()?.toLowerCase();\n    return imageExtensions.includes(extension || '');\n  }\n  // 判斷是否為 PDF\n  isPDFString(str) {\n    if (str) {\n      return str.toLowerCase().endsWith(\".pdf\");\n    }\n    return false;\n  }\n  // 判斷是否為 CAD 檔案\n  isCadString(str) {\n    if (str) {\n      const lowerStr = str.toLowerCase();\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\n    }\n    return false;\n  }\n  // 取得檔案類型圖標\n  getFileIcon(fileName) {\n    const name = fileName || '';\n    if (this.isImageFile(name)) {\n      return 'fa fa-image text-green-500';\n    } else if (this.isPDFString(name)) {\n      return 'fa fa-file-pdf text-red-500';\n    } else if (this.isCadString(name)) {\n      return 'fa fa-cube text-blue-500';\n    } else {\n      return 'fa fa-file text-gray-500';\n    }\n  }\n  // 取得檔案類型文字\n  getFileTypeText(fileName) {\n    const name = fileName || '';\n    if (this.isImageFile(name)) {\n      return '圖片';\n    } else if (this.isPDFString(name)) {\n      return 'PDF';\n    } else if (this.isCadString(name)) {\n      return 'CAD';\n    } else {\n      return '檔案';\n    }\n  }\n  // 開啟圖片預覽\n  openImagePreview(fileUrl, fileName) {\n    try {\n      const imageUrl = this.getImageSrc(fileUrl);\n      const newWindow = window.open('', '_blank');\n      if (newWindow) {\n        newWindow.document.write(`\n          <html>\n            <head>\n              <title>${fileName}</title>\n              <style>\n                body {\n                  margin: 0;\n                  padding: 20px;\n                  background: #f0f0f0;\n                  display: flex;\n                  flex-direction: column;\n                  align-items: center;\n                  font-family: Arial, sans-serif;\n                }\n                .header {\n                  background: white;\n                  padding: 10px 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  margin-bottom: 20px;\n                  font-weight: bold;\n                }\n                .image-container {\n                  background: white;\n                  padding: 20px;\n                  border-radius: 8px;\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n                  max-width: 90vw;\n                  max-height: 80vh;\n                  overflow: auto;\n                }\n                img {\n                  max-width: 100%;\n                  height: auto;\n                  display: block;\n                }\n              </style>\n            </head>\n            <body>\n              <div class=\"header\">${fileName}</div>\n              <div class=\"image-container\">\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\n              </div>\n            </body>\n          </html>\n        `);\n        newWindow.document.close();\n      } else {\n        // 如果彈出視窗被阻擋，直接開啟 URL\n        window.open(imageUrl, '_blank');\n      }\n    } catch (error) {\n      console.error('開啟圖片預覽失敗:', error);\n      this.message.showErrorMSG('開啟圖片預覽失敗');\n    }\n  }\n  // 取得正確的圖片 src\n  getImageSrc(fileDataOrUrl) {\n    if (!fileDataOrUrl) return '';\n    // 如果已經是完整的 data URL，直接返回\n    if (fileDataOrUrl.startsWith('data:')) {\n      return fileDataOrUrl;\n    }\n    // 如果是 HTTP URL，直接返回\n    if (fileDataOrUrl.startsWith('http')) {\n      return fileDataOrUrl;\n    }\n    // 如果是純 base64 字串，需要添加前綴\n    return `data:image/jpeg;base64,${fileDataOrUrl}`;\n  }\n  downloadFile(CFile, CFileName) {\n    // if (CFile && CFileName) {\n    //   this._ultilityService.downloadFileFullUrl(\n    //     CFile, CFileName\n    //   )\n    // }\n    window.open(CFile, \"_blank\");\n  }\n  handleAction(isApprove) {\n    if (!isApprove) {\n      this.validation();\n      if (this._validationHelper.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\n        return;\n      }\n    }\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\n      body: {\n        CID: this.CID,\n        CType: this.CType,\n        CIsApprove: isApprove,\n        CRemark: this.remark\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode === 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getApprovalWaitingById();\n        if (this.approvalWaiting.CApproveRecord?.length == 0) {\n          this.approvalWaiting.CApproveRecord?.push({\n            CCreator: this.decodeJWT.userName,\n            CRecordDate: new Date().toISOString(),\n            CRemark: this.remark\n          });\n        }\n        this.remark = \"\";\n      }\n      this.goBack();\n    })).subscribe();\n  }\n  goBack() {\n    this._eventService.push({\n      action: \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */,\n      payload: this.buildCaseID\n    });\n    this._location.back();\n  }\n  validation() {\n    this._validationHelper.clear();\n    this._validationHelper.required(\"[備註]\", this.remark);\n  }\n  static {\n    this.ɵfac = function DetailApprovalWaitingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DetailApprovalWaitingComponent)(i0.ɵɵdirectiveInject(i1.SpecialChangeService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.UtilityService), i0.ɵɵdirectiveInject(i4.Location), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.ValidationHelper), i0.ɵɵdirectiveInject(i7.EventService), i0.ɵɵdirectiveInject(i8.FileService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DetailApprovalWaitingComponent,\n      selectors: [[\"app-detail-approval-waiting\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[\"class\", \"flex flex-col justify-items-center items-center m-auto w-[50%]\", 4, \"ngIf\"], [1, \"flex\", \"flex-col\", \"justify-items-center\", \"items-center\", \"m-auto\", \"w-[50%]\"], [1, \"border-b-2\", \"border-b-black\", \"w-full\"], [1, \"text-xl\", \"font-bold\"], [1, \"px-3\", \"py-4\"], [1, \"flex\", \"items-center\"], [1, \"w-[100px]\"], [1, \"font-bold\"], [1, \"w-[80%]\", \"break-words\"], [1, \"flex\", \"items-center\", \"mt-3\"], [1, \"flex\", \"items-start\", \"mt-3\"], [\"class\", \"no-files\", 4, \"ngIf\"], [\"class\", \"file-item cursor-pointer\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"mt-2\", \"w-full\"], [1, \"flex\", \"px-3\", \"py-4\", \"w-full\"], [1, \"w-full\"], [\"nbInput\", \"\", 1, \"resize-none\", \"!max-w-full\", \"w-full\", 3, \"ngModelChange\", \"ngModel\", \"rows\"], [1, \"mt-3\", \"flex\", \"items-center\"], [\"nbButton\", \"\", 1, \"btn\", \"border-black\", \"border\", \"mr-2\", 3, \"click\"], [\"nbButton\", \"\", \"status\", \"danger\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"nbButton\", \"\", \"status\", \"primary\", 1, \"btn\", \"mr-2\", 3, \"click\", \"disabled\"], [\"class\", \"table-responsive relative overflow-x-auto mt-3\", 4, \"ngIf\"], [1, \"no-files\"], [1, \"fas\", \"fa-folder-open\", \"text-gray-400\", \"text-2xl\", \"mb-2\"], [1, \"file-item\", \"cursor-pointer\", 3, \"click\"], [1, \"file-icon\", \"mr-3\"], [1, \"text-xl\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-type-badge\"], [1, \"file-action-hint\"], [1, \"flex-shrink-0\", \"ml-3\"], [1, \"fas\", \"fa-external-link-alt\", \"file-action-icon\"], [1, \"table-responsive\", \"relative\", \"overflow-x-auto\", \"mt-3\"], [1, \"table\", \"table-bordered\", \"w-full\", \"text-sm\", \"text-left\", \"rtl:text-right\", \"text-gray-500\", \"dark:text-gray-400\"], [1, \"text-xs\", \"text-gray-700\", \"uppercase\", \"bg-gray-50\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"px-6\", \"py-3\"], [\"class\", \"bg-white text-black\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"text-black\"], [\"scope\", \"row\", 1, \"px-6\", \"py-4\", \"font-medium\", \"whitespace-nowrap\"], [1, \"px-6\", \"py-4\"]],\n      template: function DetailApprovalWaitingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\")(1, \"nb-card-body\");\n          i0.ɵɵtemplate(2, DetailApprovalWaitingComponent_div_2_Template, 52, 14, \"div\", 0);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !!ctx.approvalWaiting);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i10.NbCardComponent, i10.NbCardBodyComponent, i10.NbInputDirective, i10.NbButtonComponent, i11.ApprovalWaitingPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.file-item[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  padding: 12px;\\n  margin-bottom: 12px;\\n  background: white;\\n}\\n.file-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  border-color: #3b82f6;\\n}\\n\\n.file-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #f3f4f6;\\n  border-radius: 8px;\\n  transition: background-color 0.3s ease;\\n}\\n.file-icon[_ngcontent-%COMP%]:hover {\\n  background: #e5e7eb;\\n}\\n\\n.file-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  min-width: 0;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  color: #1d4ed8;\\n  font-weight: 500;\\n  transition: color 0.3s ease;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.file-name[_ngcontent-%COMP%]:hover {\\n  color: #1e40af;\\n}\\n\\n.file-type-badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 12px;\\n  background: #dbeafe;\\n  color: #1e40af;\\n  border-radius: 999px;\\n  margin-left: 8px;\\n}\\n\\n.file-action-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6b7280;\\n  margin-top: 4px;\\n}\\n\\n.file-action-icon[_ngcontent-%COMP%] {\\n  color: #9ca3af;\\n  transition: color 0.3s ease;\\n}\\n.file-action-icon[_ngcontent-%COMP%]:hover {\\n  color: #6b7280;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .file-item[_ngcontent-%COMP%] {\\n    padding: 8px;\\n    margin-bottom: 8px;\\n  }\\n  .file-icon[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .file-name[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .file-action-hint[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n\\n\\n.no-files[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-style: italic;\\n  padding: 20px;\\n  text-align: center;\\n  background: #f9fafb;\\n  border-radius: 8px;\\n  border: 1px dashed #d1d5db;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "SharedModule", "decodeJwtPayload", "LocalStorageService", "STORAGE_KEY", "EEvent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DetailApprovalWaitingComponent_div_2_div_30_Template_div_click_0_listener", "item_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "handleFileClick", "ɵɵadvance", "ɵɵclassMap", "getFileIcon", "CFileName", "ɵɵtextInterpolate1", "getFileTypeText", "isImageFile", "isPDFString", "ɵɵpipeBind2", "record_r5", "CRecordDate", "approvalWaiting", "CCreateDT", "ɵɵtextInterpolate3", "CAction", "CCreator", "CRemark", "ɵɵtemplate", "DetailApprovalWaitingComponent_div_2_div_51_tr_13_Template", "ɵɵproperty", "CApproveRecord", "DetailApprovalWaitingComponent_div_2_div_29_Template", "DetailApprovalWaitingComponent_div_2_div_30_Template", "ɵɵtwoWayListener", "DetailApprovalWaitingComponent_div_2_Template_textarea_ngModelChange_43_listener", "$event", "_r1", "ɵɵtwoWayBindingSet", "remark", "DetailApprovalWaitingComponent_div_2_Template_button_click_45_listener", "goBack", "DetailApprovalWaitingComponent_div_2_Template_button_click_47_listener", "handleAction", "DetailApprovalWaitingComponent_div_2_Template_button_click_49_listener", "DetailApprovalWaitingComponent_div_2_div_51_Template", "CName", "CBuildcaseName", "ɵɵpipeBind1", "CType", "CFileApproves", "length", "CApprovalRemark", "ɵɵtwoWayProperty", "CIsApprove", "DetailApprovalWaitingComponent", "constructor", "_specialChangeService", "_activatedRoute", "_ultilityService", "_location", "message", "_validationHelper", "_eventService", "fileService", "decodeJWT", "GetLocalStorage", "TOKEN", "CID", "parseInt", "snapshot", "paramMap", "get", "buildCaseID", "queryParams", "pipe", "p", "subscribe", "ngOnInit", "getApprovalWaitingById", "apiSpecialChangeGetApproveWaitingByIdPost$Json", "body", "toString", "res", "StatusCode", "Entries", "file", "fileName", "displayName", "isImageByName", "isPdfByName", "isCadByName", "isCadString", "console", "log", "relativePath", "CFile", "serverFileName", "isBase64String", "getFileFromServer", "warn", "handleLocalFile", "str", "startsWith", "test", "isImage", "isPdf", "isCad", "fileUrl", "data", "imageUrl", "getImageSrc", "openImagePreview", "openPdfWithBase64", "downloadFileDirectly", "getFile", "next", "blob", "url", "URL", "createObjectURL", "openPdfInNewWindow", "downloadBlobFile", "setTimeout", "revokeObjectURL", "error", "showErrorMSG", "blobUrl", "newWindow", "window", "open", "document", "write", "close", "location", "href", "fileData", "pdfUrl", "link", "createElement", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "downloadBase64File", "base64Data", "base64Content", "includes", "split", "mimeType", "extension", "pop", "toLowerCase", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "Blob", "type", "imageExtensions", "endsWith", "lowerStr", "name", "fileDataOrUrl", "downloadFile", "isApprove", "validation", "errorMessages", "showErrorMSGs", "apiSpecialChangeUpdateApproveWaitingPost$Json", "showSucessMSG", "push", "userName", "Date", "toISOString", "action", "payload", "back", "clear", "required", "ɵɵdirectiveInject", "i1", "SpecialChangeService", "i2", "ActivatedRoute", "i3", "UtilityService", "i4", "Location", "i5", "MessageService", "i6", "ValidationHelper", "i7", "EventService", "i8", "FileService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DetailApprovalWaitingComponent_Template", "rf", "ctx", "DetailApprovalWaitingComponent_div_2_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NgControlStatus", "NgModel", "i10", "NbCardComponent", "NbCardBodyComponent", "NbInputDirective", "NbButtonComponent", "i11", "ApprovalWaitingPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\approve-waiting\\detail-approval-waiting\\detail-approval-waiting.component.html"], "sourcesContent": ["import { CommonModule, Location } from '@angular/common';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { tap } from 'rxjs';\r\nimport { ApproveWaitingByIdRes } from 'src/services/api/models';\r\nimport { SpecialChangeService } from 'src/services/api/services';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { UtilityService } from 'src/app/shared/services/utility.service';\r\nimport { decodeJwtPayload } from '@nebular/auth';\r\nimport { LocalStorageService } from 'src/app/shared/services/local-storage.service';\r\nimport { STORAGE_KEY } from 'src/app/shared/constant/constant';\r\nimport { BaseFilePipe } from 'src/app/@theme/pipes/base-file.pipe';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { EEvent, EventService } from 'src/app/shared/services/event.service';\r\nimport { FileUploadComponent } from '../../components/file-upload/file-upload.component';\r\nimport { FileService } from 'src/services/api/services/File.service';\r\n\r\n@Component({\r\n  selector: 'app-detail-approval-waiting',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule\r\n  ],\r\n  templateUrl: './detail-approval-waiting.component.html',\r\n  styleUrls: ['./detail-approval-waiting.component.scss']\r\n})\r\nexport class DetailApprovalWaitingComponent implements OnInit {\r\n\r\n  CType: number = 1;\r\n  CID: number\r\n  remark: string = \"\"\r\n  buildCaseID: number\r\n\r\n  approvalWaiting: ApproveWaitingByIdRes\r\n  decodeJWT: any\r\n  constructor(\r\n    private _specialChangeService: SpecialChangeService,\r\n    private _activatedRoute: ActivatedRoute,\r\n    private _ultilityService: UtilityService,\r\n    private _location: Location,\r\n    private message: MessageService,\r\n    private _validationHelper: ValidationHelper,\r\n    private _eventService: EventService,\r\n    private fileService: FileService,\r\n  ) {\r\n    this.decodeJWT = decodeJwtPayload(LocalStorageService.GetLocalStorage(STORAGE_KEY.TOKEN))\r\n    this.CID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"id\")!);\r\n    this.buildCaseID = parseInt(this._activatedRoute.snapshot.paramMap!.get(\"buildCaseId\")!)\r\n    this._activatedRoute.queryParams.pipe(\r\n      tap(p => {\r\n        this.CType = p[\"type\"]\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.getApprovalWaitingById()\r\n  }\r\n\r\n  getApprovalWaitingById() {\r\n    this._specialChangeService.apiSpecialChangeGetApproveWaitingByIdPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType.toString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.approvalWaiting = res.Entries!\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n  // 檔案處理方法 - 參考 FileUploadComponent 的邏輯\r\n  handleFileClick(file: any) {\r\n    const fileName = file.CFileName || '';\r\n    const displayName = fileName;\r\n\r\n    // 判斷檔案類型\r\n    const isImageByName = this.isImageFile(fileName);\r\n    const isPdfByName = this.isPDFString(fileName);\r\n    const isCadByName = this.isCadString(fileName);\r\n\r\n    console.log('handleFileClick - 處理檔案:', fileName, 'file:', file);\r\n\r\n    // 統一使用 GetFile API 取得檔案（非 base64 才呼叫）\r\n    const relativePath = file.relativePath || file.CFile;\r\n    const serverFileName = file.fileName || file.CFileName;\r\n\r\n    if (relativePath && serverFileName && !this.isBase64String(relativePath)) {\r\n      this.getFileFromServer(relativePath, serverFileName, displayName, isImageByName, isPdfByName, isCadByName);\r\n    } else {\r\n      // 如果沒有路徑資訊或是 base64，使用本地檔案處理邏輯作為後備方案\r\n      console.warn('檔案缺少路徑資訊或為 base64，使用本地處理:', file);\r\n      this.handleLocalFile(file, isImageByName, isPdfByName, isCadByName);\r\n    }\r\n  }\r\n\r\n  // 判斷是否為 base64 字串\r\n  private isBase64String(str: string): boolean {\r\n    if (!str) return false;\r\n    // 簡單檢查是否為 base64 格式\r\n    return str.startsWith('data:') || (str.length > 100 && /^[A-Za-z0-9+/=]+$/.test(str));\r\n  }\r\n  // 處理本地檔案的後備方法\r\n  private handleLocalFile(file: any, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    const fileUrl = file.CFile || file.data;\r\n    const fileName = file.CFileName || file.fileName || '';\r\n\r\n    if (isImage) {\r\n      const imageUrl = this.getImageSrc(file);\r\n      this.openImagePreview(imageUrl, fileName);\r\n    } else if (isPdf) {\r\n      // 使用 base64 處理 PDF\r\n      this.openPdfWithBase64(fileUrl, fileName);\r\n    } else {\r\n      this.downloadFileDirectly(fileUrl, fileName);\r\n    }\r\n  }\r\n\r\n  // 從後端取得檔案 blob\r\n  private getFileFromServer(relativePath: string, fileName: string, displayName: string, isImage: boolean, isPdf: boolean, isCad: boolean) {\r\n    this.fileService.getFile(relativePath, fileName).subscribe({\r\n      next: (blob: Blob) => {\r\n        const url = URL.createObjectURL(blob);\r\n\r\n        if (isImage) {\r\n          // 圖片預覽\r\n          this.openImagePreview(url, displayName);\r\n        } else if (isPdf) {\r\n          // PDF 檔案另開視窗顯示\r\n          this.openPdfInNewWindow(url, displayName);\r\n        } else {\r\n          // 其他檔案直接下載\r\n          this.downloadBlobFile(blob, displayName);\r\n        }\r\n\r\n        // 清理 URL (延遲清理以確保檔案能正確下載/預覽)\r\n        setTimeout(() => URL.revokeObjectURL(url), 10000);\r\n      },\r\n      error: (error) => {\r\n        console.error('取得檔案失敗:', error);\r\n        this.message.showErrorMSG('取得檔案失敗，請稍後再試');\r\n      }\r\n    });\r\n  }\r\n\r\n  // 在新視窗中開啟 PDF\r\n  private openPdfInNewWindow(blobUrl: string, fileName: string) {\r\n    try {\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${blobUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.location.href = blobUrl;\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟 PDF 視窗失敗:', error);\r\n      // 後備方案：直接開啟 URL\r\n      window.open(blobUrl, '_blank');\r\n    }\r\n  }\r\n\r\n  // 使用 base64 開啟 PDF（本地檔案後備方案）\r\n  private openPdfWithBase64(fileData: string, fileName: string) {\r\n    try {\r\n      let pdfUrl = fileData;\r\n\r\n      // 如果是 base64，需要轉換為 blob URL\r\n      if (!fileData.startsWith('http')) {\r\n        if (fileData.startsWith('data:application/pdf')) {\r\n          pdfUrl = fileData;\r\n        } else {\r\n          pdfUrl = `data:application/pdf;base64,${fileData}`;\r\n        }\r\n      }\r\n\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body { margin: 0; padding: 0; }\r\n                iframe { width: 100vw; height: 100vh; border: none; }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <iframe src=\"${pdfUrl}\" type=\"application/pdf\"></iframe>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        window.open(pdfUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('打開 PDF 時發生錯誤:', error);\r\n      this.message.showErrorMSG('打開 PDF 失敗');\r\n    }\r\n  }\r\n\r\n  // 下載 blob 檔案\r\n  private downloadBlobFile(blob: Blob, fileName: string) {\r\n    const url = URL.createObjectURL(blob);\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = fileName;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // 清理 URL\r\n    setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n  }\r\n\r\n  // 下載檔案（處理 base64 或 URL）\r\n  private downloadFileDirectly(fileData: string, fileName: string) {\r\n    if (!fileData) {\r\n      this.message.showErrorMSG('檔案資料不存在');\r\n      return;\r\n    }\r\n\r\n    if (this.isBase64String(fileData)) {\r\n      this.downloadBase64File(fileData, fileName);\r\n    } else {\r\n      // 假設是 URL，嘗試直接下載\r\n      window.open(fileData, '_blank');\r\n    }\r\n  }\r\n\r\n  // 下載 base64 檔案\r\n  private downloadBase64File(base64Data: string, fileName: string) {\r\n    try {\r\n      // 如果 base64Data 包含 data:type/subtype;base64, 前綴，需要提取\r\n      const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;\r\n\r\n      // 從檔案名稱判斷 MIME 類型\r\n      let mimeType = 'application/octet-stream';\r\n      const extension = fileName.split('.').pop()?.toLowerCase();\r\n\r\n      switch (extension) {\r\n        case 'pdf':\r\n          mimeType = 'application/pdf';\r\n          break;\r\n        case 'jpg':\r\n        case 'jpeg':\r\n          mimeType = 'image/jpeg';\r\n          break;\r\n        case 'png':\r\n          mimeType = 'image/png';\r\n          break;\r\n        case 'dwg':\r\n          mimeType = 'application/acad';\r\n          break;\r\n        case 'dxf':\r\n          mimeType = 'application/dxf';\r\n          break;\r\n      }\r\n\r\n      // 將 base64 轉換為 Blob\r\n      const byteCharacters = atob(base64Content);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: mimeType });\r\n\r\n      // 創建下載連結\r\n      const url = URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = fileName;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // 清理 URL\r\n      setTimeout(() => URL.revokeObjectURL(url), 1000);\r\n    } catch (error) {\r\n      console.error('處理 base64 檔案時發生錯誤:', error);\r\n      this.message.showErrorMSG('處理檔案時發生錯誤');\r\n    }\r\n  }\r\n\r\n  // 判斷檔案是否為圖片（根據檔名）\r\n  isImageFile(fileName: string): boolean {\r\n    if (!fileName) return false;\r\n    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return imageExtensions.includes(extension || '');\r\n  }\r\n\r\n  // 判斷是否為 PDF\r\n  isPDFString(str: string): boolean {\r\n    if (str) {\r\n      return str.toLowerCase().endsWith(\".pdf\");\r\n    }\r\n    return false;\r\n  }\r\n\r\n  // 判斷是否為 CAD 檔案\r\n  isCadString(str: string): boolean {\r\n    if (str) {\r\n      const lowerStr = str.toLowerCase();\r\n      return lowerStr.endsWith(\".dwg\") || lowerStr.endsWith(\".dxf\");\r\n    }\r\n    return false;\r\n  }\r\n  // 取得檔案類型圖標\r\n  getFileIcon(fileName: string | null | undefined): string {\r\n    const name = fileName || '';\r\n    if (this.isImageFile(name)) {\r\n      return 'fa fa-image text-green-500';\r\n    } else if (this.isPDFString(name)) {\r\n      return 'fa fa-file-pdf text-red-500';\r\n    } else if (this.isCadString(name)) {\r\n      return 'fa fa-cube text-blue-500';\r\n    } else {\r\n      return 'fa fa-file text-gray-500';\r\n    }\r\n  }\r\n\r\n  // 取得檔案類型文字\r\n  getFileTypeText(fileName: string | null | undefined): string {\r\n    const name = fileName || '';\r\n    if (this.isImageFile(name)) {\r\n      return '圖片';\r\n    } else if (this.isPDFString(name)) {\r\n      return 'PDF';\r\n    } else if (this.isCadString(name)) {\r\n      return 'CAD';\r\n    } else {\r\n      return '檔案';\r\n    }\r\n  }\r\n\r\n  // 開啟圖片預覽\r\n  private openImagePreview(fileUrl: string, fileName: string) {\r\n    try {\r\n      const imageUrl = this.getImageSrc(fileUrl);\r\n      const newWindow = window.open('', '_blank');\r\n      if (newWindow) {\r\n        newWindow.document.write(`\r\n          <html>\r\n            <head>\r\n              <title>${fileName}</title>\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 20px;\r\n                  background: #f0f0f0;\r\n                  display: flex;\r\n                  flex-direction: column;\r\n                  align-items: center;\r\n                  font-family: Arial, sans-serif;\r\n                }\r\n                .header {\r\n                  background: white;\r\n                  padding: 10px 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  margin-bottom: 20px;\r\n                  font-weight: bold;\r\n                }\r\n                .image-container {\r\n                  background: white;\r\n                  padding: 20px;\r\n                  border-radius: 8px;\r\n                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n                  max-width: 90vw;\r\n                  max-height: 80vh;\r\n                  overflow: auto;\r\n                }\r\n                img {\r\n                  max-width: 100%;\r\n                  height: auto;\r\n                  display: block;\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div class=\"header\">${fileName}</div>\r\n              <div class=\"image-container\">\r\n                <img src=\"${imageUrl}\" alt=\"${fileName}\" onload=\"console.log('圖片載入成功')\" onerror=\"console.error('圖片載入失敗'); this.style.display='none'; this.parentElement.innerHTML='<p>圖片載入失敗</p>';\">\r\n              </div>\r\n            </body>\r\n          </html>\r\n        `);\r\n        newWindow.document.close();\r\n      } else {\r\n        // 如果彈出視窗被阻擋，直接開啟 URL\r\n        window.open(imageUrl, '_blank');\r\n      }\r\n    } catch (error) {\r\n      console.error('開啟圖片預覽失敗:', error);\r\n      this.message.showErrorMSG('開啟圖片預覽失敗');\r\n    }\r\n  }\r\n  // 取得正確的圖片 src\r\n  private getImageSrc(fileDataOrUrl: string): string {\r\n    if (!fileDataOrUrl) return '';\r\n\r\n    // 如果已經是完整的 data URL，直接返回\r\n    if (fileDataOrUrl.startsWith('data:')) {\r\n      return fileDataOrUrl;\r\n    }\r\n\r\n    // 如果是 HTTP URL，直接返回\r\n    if (fileDataOrUrl.startsWith('http')) {\r\n      return fileDataOrUrl;\r\n    }\r\n\r\n    // 如果是純 base64 字串，需要添加前綴\r\n    return `data:image/jpeg;base64,${fileDataOrUrl}`;\r\n  }\r\n\r\n  downloadFile(CFile: any, CFileName: any) {\r\n    // if (CFile && CFileName) {\r\n    //   this._ultilityService.downloadFileFullUrl(\r\n    //     CFile, CFileName\r\n    //   )\r\n    // }\r\n    window.open(CFile, \"_blank\");\r\n  }\r\n\r\n  handleAction(isApprove: boolean) {\r\n    if (!isApprove) {\r\n      this.validation()\r\n      if (this._validationHelper.errorMessages.length > 0) {\r\n        this.message.showErrorMSGs(this._validationHelper.errorMessages);\r\n        return\r\n      }\r\n    }\r\n    this._specialChangeService.apiSpecialChangeUpdateApproveWaitingPost$Json({\r\n      body: {\r\n        CID: this.CID,\r\n        CType: this.CType,\r\n        CIsApprove: isApprove,\r\n        CRemark: this.remark\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode === 0) {\r\n          this.message.showSucessMSG(\"執行成功\");\r\n          this.getApprovalWaitingById()\r\n          if (this.approvalWaiting.CApproveRecord?.length == 0) {\r\n            this.approvalWaiting.CApproveRecord?.push({\r\n              CCreator: this.decodeJWT.userName,\r\n              CRecordDate: new Date().toISOString(),\r\n              CRemark: this.remark\r\n            })\r\n          }\r\n          this.remark = \"\"\r\n        }\r\n        this.goBack();\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  goBack() {\r\n    this._eventService.push({\r\n      action: EEvent.GET_BUILDCASE,\r\n      payload: this.buildCaseID\r\n    })\r\n    this._location.back()\r\n  }\r\n\r\n  validation() {\r\n    this._validationHelper.clear();\r\n    this._validationHelper.required(\"[備註]\", this.remark)\r\n  }\r\n}\r\n", "<nb-card>\r\n  <!-- <nb-card-header></nb-card-header> -->\r\n  <nb-card-body>\r\n    <div *ngIf=\"!!approvalWaiting\" class=\"flex flex-col justify-items-center items-center m-auto w-[50%]\">\r\n      <div class=\"border-b-2 border-b-black w-full\">\r\n        <span class=\"text-xl font-bold\">\r\n          {{approvalWaiting.CName}}\r\n        </span>\r\n        <div class=\"px-3 py-4\">\r\n          <div class=\"flex items-center\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                建案\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CBuildcaseName}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                類別\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CType! | getTypeApprovalWaiting}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                名稱\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CName}}\r\n            </span>\r\n          </div>\r\n          <div class=\"flex items-start mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                檔案\r\n              </span>\r\n            </div>\r\n            <div class=\"w-[80%] break-words\">\r\n              <div *ngIf=\"!approvalWaiting.CFileApproves || approvalWaiting.CFileApproves.length === 0\"\r\n                class=\"no-files\">\r\n                <i class=\"fas fa-folder-open text-gray-400 text-2xl mb-2\"></i>\r\n                <div>無附件</div>\r\n              </div>\r\n              <div *ngFor=\"let item of approvalWaiting.CFileApproves; let i = index\" class=\"file-item cursor-pointer\"\r\n                (click)=\"handleFileClick(item)\">\r\n                <!-- 檔案圖標 -->\r\n                <div class=\"flex items-center\">\r\n                  <div class=\"file-icon mr-3\">\r\n                    <i [class]=\"getFileIcon(item.CFileName)\" class=\"text-xl\"></i>\r\n                  </div>\r\n\r\n                  <!-- 檔案資訊 -->\r\n                  <div class=\"file-info\">\r\n                    <div class=\"flex items-center\">\r\n                      <span class=\"file-name\">\r\n                        {{item.CFileName || '未命名檔案'}}\r\n                      </span>\r\n                      <span class=\"file-type-badge\">\r\n                        {{getFileTypeText(item.CFileName)}}\r\n                      </span>\r\n                    </div>\r\n                    <div class=\"file-action-hint\">\r\n                      點擊以{{isImageFile(item.CFileName || '') ? '預覽' : isPDFString(item.CFileName || '') ? '檢視' :\r\n                      '下載'}}檔案\r\n                    </div>\r\n                  </div>\r\n\r\n                  <!-- 操作圖標 -->\r\n                  <div class=\"flex-shrink-0 ml-3\">\r\n                    <i class=\"fas fa-external-link-alt file-action-icon\"></i>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"flex items-center mt-3\">\r\n            <div class=\"w-[100px]\">\r\n              <span class=\"font-bold\">\r\n                審核說明\r\n              </span>\r\n            </div>\r\n            <span class=\"w-[80%] break-words\">\r\n              {{approvalWaiting.CApprovalRemark}}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"mt-2 w-full\">\r\n        <div class=\"flex px-3 py-4 w-full\">\r\n          <div class=\"w-[100px]\">\r\n            <span class=\"font-bold\">\r\n              備註\r\n            </span>\r\n          </div>\r\n          <div class=\"w-full\">\r\n            <textarea nbInput [(ngModel)]=\"remark\" [rows]=\"4\" class=\"resize-none !max-w-full w-full\"></textarea>\r\n            <div class=\"mt-3 flex items-center\">\r\n              <button nbButton class=\"btn border-black border mr-2\" (click)=\"goBack()\">取消</button>\r\n              <button nbButton [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                status=\"danger\" class=\"btn mr-2\" (click)=\"handleAction(false)\">駁回</button>\r\n              <button nbButton [disabled]=\"approvalWaiting.CIsApprove !== null && approvalWaiting.CIsApprove\"\r\n                status=\"primary\" class=\"btn mr-2\" (click)=\"handleAction(true)\">同意</button>\r\n            </div>\r\n            <div class=\"table-responsive relative overflow-x-auto mt-3\" *ngIf=\"!!approvalWaiting\">\r\n              <table\r\n                class=\"table table-bordered w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400\">\r\n                <thead class=\"text-xs text-gray-700 uppercase bg-gray-50\">\r\n                  <tr style=\"background-color: #27ae60; color: white;\">\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      日期\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      動作\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      使用者\r\n                    </th>\r\n                    <th scope=\"col\" class=\"px-6 py-3\">\r\n                      備註\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr class=\"bg-white text-black\" *ngFor=\"let record of approvalWaiting.CApproveRecord\">\r\n                    <th scope=\"row\" class=\"px-6 py-4 font-medium whitespace-nowrap\">\r\n                      {{\r\n                      (record.CRecordDate ? record.CRecordDate : approvalWaiting.CCreateDT) |\r\n                      date: \"yyyy/MM/dd HH:mm:ss\"\r\n                      }}\r\n                    </th>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CAction === 1 ? '送出審核' : ''}}\r\n                      {{record.CAction === 2 ? '審核通過' : ''}}\r\n                      {{record.CAction === 3 ? '審核駁回' : ''}}\r\n                    </td>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CCreator}}\r\n                    </td>\r\n                    <td class=\"px-6 py-4\">\r\n                      {{record.CRemark}}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAASA,YAAY,QAAkB,iBAAiB;AAGxD,SAASC,GAAG,QAAQ,MAAM;AAG1B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,WAAW,QAAQ,kCAAkC;AAI9D,SAASC,MAAM,QAAsB,uCAAuC;;;;;;;;;;;;;;;ICgC9DC,EAAA,CAAAC,cAAA,cACmB;IACjBD,EAAA,CAAAE,SAAA,YAA8D;IAC9DF,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAG,MAAA,yBAAG;IACVH,EADU,CAAAI,YAAA,EAAM,EACV;;;;;;IACNJ,EAAA,CAAAC,cAAA,cACkC;IAAhCD,EAAA,CAAAK,UAAA,mBAAAC,0EAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,eAAA,CAAAP,OAAA,CAAqB;IAAA,EAAC;IAG7BP,EADF,CAAAC,cAAA,aAA+B,cACD;IAC1BD,EAAA,CAAAE,SAAA,YAA6D;IAC/DF,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAFJ,CAAAC,cAAA,cAAuB,aACU,eACL;IACtBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAG,MAAA,IAEF;IACFH,EADE,CAAAI,YAAA,EAAM,EACF;IAGNJ,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAE,SAAA,aAAyD;IAG/DF,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IAxBGJ,EAAA,CAAAe,SAAA,GAAqC;IAArCf,EAAA,CAAAgB,UAAA,CAAAL,MAAA,CAAAM,WAAA,CAAAV,OAAA,CAAAW,SAAA,EAAqC;IAOpClB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAZ,OAAA,CAAAW,SAAA,0CACF;IAEElB,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAS,eAAA,CAAAb,OAAA,CAAAW,SAAA,OACF;IAGAlB,EAAA,CAAAe,SAAA,GAEF;IAFEf,EAAA,CAAAmB,kBAAA,wBAAAR,MAAA,CAAAU,WAAA,CAAAd,OAAA,CAAAW,SAAA,2BAAAP,MAAA,CAAAW,WAAA,CAAAf,OAAA,CAAAW,SAAA,2DAEF;;;;;IA4DAlB,EADF,CAAAC,cAAA,aAAsF,aACpB;IAC9DD,EAAA,CAAAG,MAAA,GAIF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GAGF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAK,EACF;;;;;IAhBDJ,EAAA,CAAAe,SAAA,GAIF;IAJEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAuB,WAAA,OAAAC,SAAA,CAAAC,WAAA,GAAAD,SAAA,CAAAC,WAAA,GAAAd,MAAA,CAAAe,eAAA,CAAAC,SAAA,8BAIF;IAEE3B,EAAA,CAAAe,SAAA,GAGF;IAHEf,EAAA,CAAA4B,kBAAA,MAAAJ,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,+CAAAL,SAAA,CAAAK,OAAA,8CAGF;IAEE7B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAK,SAAA,CAAAM,QAAA,MACF;IAEE9B,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAK,SAAA,CAAAO,OAAA,MACF;;;;;IAhCA/B,EALR,CAAAC,cAAA,cAAsF,gBAEoB,gBAC5C,aACH,aACjB;IAChCD,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAkC;IAChCD,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAAkC;IAChCD,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAG,MAAA,sBACF;IAEJH,EAFI,CAAAI,YAAA,EAAK,EACF,EACC;IACRJ,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAgC,UAAA,KAAAC,0DAAA,kBAAsF;IAqB5FjC,EAFI,CAAAI,YAAA,EAAQ,EACF,EACJ;;;;IArBmDJ,EAAA,CAAAe,SAAA,IAAiC;IAAjCf,EAAA,CAAAkC,UAAA,YAAAvB,MAAA,CAAAe,eAAA,CAAAS,cAAA,CAAiC;;;;;;IA9H9FnC,EAFJ,CAAAC,cAAA,aAAsG,aACtD,cACZ;IAC9BD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIDJ,EAHN,CAAAC,cAAA,aAAuB,aACU,aACN,cACG;IACtBD,EAAA,CAAAG,MAAA,qBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGFJ,EAFJ,CAAAC,cAAA,eAAmC,cACV,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,cAAiC;IAM/BD,EALA,CAAAgC,UAAA,KAAAI,oDAAA,kBACmB,KAAAC,oDAAA,mBAKe;IA8BtCrC,EADE,CAAAI,YAAA,EAAM,EACF;IAGFJ,EAFJ,CAAAC,cAAA,cAAoC,cACX,eACG;IACtBD,EAAA,CAAAG,MAAA,kCACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;IAIAJ,EAHN,CAAAC,cAAA,eAAyB,eACY,cACV,eACG;IACtBD,EAAA,CAAAG,MAAA,sBACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAEJJ,EADF,CAAAC,cAAA,eAAoB,oBACuE;IAAvED,EAAA,CAAAsC,gBAAA,2BAAAC,iFAAAC,MAAA;MAAAxC,EAAA,CAAAQ,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAA0C,kBAAA,CAAA/B,MAAA,CAAAgC,MAAA,EAAAH,MAAA,MAAA7B,MAAA,CAAAgC,MAAA,GAAAH,MAAA;MAAA,OAAAxC,EAAA,CAAAa,WAAA,CAAA2B,MAAA;IAAA,EAAoB;IAAmDxC,EAAA,CAAAI,YAAA,EAAW;IAElGJ,EADF,CAAAC,cAAA,eAAoC,kBACuC;IAAnBD,EAAA,CAAAK,UAAA,mBAAAuC,uEAAA;MAAA5C,EAAA,CAAAQ,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkC,MAAA,EAAQ;IAAA,EAAC;IAAC7C,EAAA,CAAAG,MAAA,oBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpFJ,EAAA,CAAAC,cAAA,kBACiE;IAA9BD,EAAA,CAAAK,UAAA,mBAAAyC,uEAAA;MAAA9C,EAAA,CAAAQ,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAAC/C,EAAA,CAAAG,MAAA,oBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5EJ,EAAA,CAAAC,cAAA,kBACiE;IAA7BD,EAAA,CAAAK,UAAA,mBAAA2C,uEAAA;MAAAhD,EAAA,CAAAQ,aAAA,CAAAiC,GAAA;MAAA,MAAA9B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAoC,YAAA,CAAa,IAAI,CAAC;IAAA,EAAC;IAAC/C,EAAA,CAAAG,MAAA,oBAAE;IACrEH,EADqE,CAAAI,YAAA,EAAS,EACxE;IACNJ,EAAA,CAAAgC,UAAA,KAAAiB,oDAAA,mBAAsF;IA6C9FjD,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;IAtJAJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAe,eAAA,CAAAwB,KAAA,MACF;IASMlD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAe,eAAA,CAAAyB,cAAA,MACF;IASEnD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAnB,EAAA,CAAAoD,WAAA,SAAAzC,MAAA,CAAAe,eAAA,CAAA2B,KAAA,OACF;IASErD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAe,eAAA,CAAAwB,KAAA,MACF;IASQlD,EAAA,CAAAe,SAAA,GAAkF;IAAlFf,EAAA,CAAAkC,UAAA,UAAAvB,MAAA,CAAAe,eAAA,CAAA4B,aAAA,IAAA3C,MAAA,CAAAe,eAAA,CAAA4B,aAAA,CAAAC,MAAA,OAAkF;IAKlEvD,EAAA,CAAAe,SAAA,EAAkC;IAAlCf,EAAA,CAAAkC,UAAA,YAAAvB,MAAA,CAAAe,eAAA,CAAA4B,aAAA,CAAkC;IAuCxDtD,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAR,MAAA,CAAAe,eAAA,CAAA8B,eAAA,MACF;IAYkBxD,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAyD,gBAAA,YAAA9C,MAAA,CAAAgC,MAAA,CAAoB;IAAC3C,EAAA,CAAAkC,UAAA,WAAU;IAG9BlC,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAAkC,UAAA,aAAAvB,MAAA,CAAAe,eAAA,CAAAgC,UAAA,aAAA/C,MAAA,CAAAe,eAAA,CAAAgC,UAAA,CAA8E;IAE9E1D,EAAA,CAAAe,SAAA,GAA8E;IAA9Ef,EAAA,CAAAkC,UAAA,aAAAvB,MAAA,CAAAe,eAAA,CAAAgC,UAAA,aAAA/C,MAAA,CAAAe,eAAA,CAAAgC,UAAA,CAA8E;IAGpC1D,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAkC,UAAA,WAAAvB,MAAA,CAAAe,eAAA,CAAuB;;;ADnFhG,OAAM,MAAOiC,8BAA8B;EASzCC,YACUC,qBAA2C,EAC3CC,eAA+B,EAC/BC,gBAAgC,EAChCC,SAAmB,EACnBC,OAAuB,EACvBC,iBAAmC,EACnCC,aAA2B,EAC3BC,WAAwB;IAPxB,KAAAP,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IAfrB,KAAAf,KAAK,GAAW,CAAC;IAEjB,KAAAV,MAAM,GAAW,EAAE;IAejB,IAAI,CAAC0B,SAAS,GAAGzE,gBAAgB,CAACC,mBAAmB,CAACyE,eAAe,CAACxE,WAAW,CAACyE,KAAK,CAAC,CAAC;IACzF,IAAI,CAACC,GAAG,GAAGC,QAAQ,CAAC,IAAI,CAACX,eAAe,CAACY,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,IAAI,CAAE,CAAC;IACvE,IAAI,CAACC,WAAW,GAAGJ,QAAQ,CAAC,IAAI,CAACX,eAAe,CAACY,QAAQ,CAACC,QAAS,CAACC,GAAG,CAAC,aAAa,CAAE,CAAC;IACxF,IAAI,CAACd,eAAe,CAACgB,WAAW,CAACC,IAAI,CACnCrF,GAAG,CAACsF,CAAC,IAAG;MACN,IAAI,CAAC3B,KAAK,GAAG2B,CAAC,CAAC,MAAM,CAAC;IACxB,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACtB,qBAAqB,CAACuB,8CAA8C,CAAC;MACxEC,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbnB,KAAK,EAAE,IAAI,CAACA,KAAK,CAACiC,QAAQ;;KAE7B,CAAC,CAACP,IAAI,CACLrF,GAAG,CAAC6F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAAC9D,eAAe,GAAG6D,GAAG,CAACE,OAAQ;MACrC;IACF,CAAC,CAAC,CACH,CAACR,SAAS,EAAE;EACf;EACA;EACAnE,eAAeA,CAAC4E,IAAS;IACvB,MAAMC,QAAQ,GAAGD,IAAI,CAACxE,SAAS,IAAI,EAAE;IACrC,MAAM0E,WAAW,GAAGD,QAAQ;IAE5B;IACA,MAAME,aAAa,GAAG,IAAI,CAACxE,WAAW,CAACsE,QAAQ,CAAC;IAChD,MAAMG,WAAW,GAAG,IAAI,CAACxE,WAAW,CAACqE,QAAQ,CAAC;IAC9C,MAAMI,WAAW,GAAG,IAAI,CAACC,WAAW,CAACL,QAAQ,CAAC;IAE9CM,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEP,QAAQ,EAAE,OAAO,EAAED,IAAI,CAAC;IAE/D;IACA,MAAMS,YAAY,GAAGT,IAAI,CAACS,YAAY,IAAIT,IAAI,CAACU,KAAK;IACpD,MAAMC,cAAc,GAAGX,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACxE,SAAS;IAEtD,IAAIiF,YAAY,IAAIE,cAAc,IAAI,CAAC,IAAI,CAACC,cAAc,CAACH,YAAY,CAAC,EAAE;MACxE,IAAI,CAACI,iBAAiB,CAACJ,YAAY,EAAEE,cAAc,EAAET,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,CAAC;IAC5G,CAAC,MAAM;MACL;MACAE,OAAO,CAACO,IAAI,CAAC,2BAA2B,EAAEd,IAAI,CAAC;MAC/C,IAAI,CAACe,eAAe,CAACf,IAAI,EAAEG,aAAa,EAAEC,WAAW,EAAEC,WAAW,CAAC;IACrE;EACF;EAEA;EACQO,cAAcA,CAACI,GAAW;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB;IACA,OAAOA,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,IAAKD,GAAG,CAACnD,MAAM,GAAG,GAAG,IAAI,mBAAmB,CAACqD,IAAI,CAACF,GAAG,CAAE;EACvF;EACA;EACQD,eAAeA,CAACf,IAAS,EAAEmB,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACjF,MAAMC,OAAO,GAAGtB,IAAI,CAACU,KAAK,IAAIV,IAAI,CAACuB,IAAI;IACvC,MAAMtB,QAAQ,GAAGD,IAAI,CAACxE,SAAS,IAAIwE,IAAI,CAACC,QAAQ,IAAI,EAAE;IAEtD,IAAIkB,OAAO,EAAE;MACX,MAAMK,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACzB,IAAI,CAAC;MACvC,IAAI,CAAC0B,gBAAgB,CAACF,QAAQ,EAAEvB,QAAQ,CAAC;IAC3C,CAAC,MAAM,IAAImB,KAAK,EAAE;MAChB;MACA,IAAI,CAACO,iBAAiB,CAACL,OAAO,EAAErB,QAAQ,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAAC2B,oBAAoB,CAACN,OAAO,EAAErB,QAAQ,CAAC;IAC9C;EACF;EAEA;EACQY,iBAAiBA,CAACJ,YAAoB,EAAER,QAAgB,EAAEC,WAAmB,EAAEiB,OAAgB,EAAEC,KAAc,EAAEC,KAAc;IACrI,IAAI,CAAC3C,WAAW,CAACmD,OAAO,CAACpB,YAAY,EAAER,QAAQ,CAAC,CAACV,SAAS,CAAC;MACzDuC,IAAI,EAAGC,IAAU,IAAI;QACnB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAErC,IAAIZ,OAAO,EAAE;UACX;UACA,IAAI,CAACO,gBAAgB,CAACM,GAAG,EAAE9B,WAAW,CAAC;QACzC,CAAC,MAAM,IAAIkB,KAAK,EAAE;UAChB;UACA,IAAI,CAACe,kBAAkB,CAACH,GAAG,EAAE9B,WAAW,CAAC;QAC3C,CAAC,MAAM;UACL;UACA,IAAI,CAACkC,gBAAgB,CAACL,IAAI,EAAE7B,WAAW,CAAC;QAC1C;QAEA;QACAmC,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,KAAK,CAAC;MACnD,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAChE,OAAO,CAACiE,YAAY,CAAC,cAAc,CAAC;MAC3C;KACD,CAAC;EACJ;EAEA;EACQL,kBAAkBA,CAACM,OAAe,EAAExC,QAAgB;IAC1D,IAAI;MACF,MAAMyC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGV7C,QAAQ;;;;;;;6BAOFwC,OAAO;;;SAG3B,CAAC;QACFC,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACAJ,MAAM,CAACK,QAAQ,CAACC,IAAI,GAAGR,OAAO;MAChC;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACAI,MAAM,CAACC,IAAI,CAACH,OAAO,EAAE,QAAQ,CAAC;IAChC;EACF;EAEA;EACQd,iBAAiBA,CAACuB,QAAgB,EAAEjD,QAAgB;IAC1D,IAAI;MACF,IAAIkD,MAAM,GAAGD,QAAQ;MAErB;MACA,IAAI,CAACA,QAAQ,CAACjC,UAAU,CAAC,MAAM,CAAC,EAAE;QAChC,IAAIiC,QAAQ,CAACjC,UAAU,CAAC,sBAAsB,CAAC,EAAE;UAC/CkC,MAAM,GAAGD,QAAQ;QACnB,CAAC,MAAM;UACLC,MAAM,GAAG,+BAA+BD,QAAQ,EAAE;QACpD;MACF;MAEA,MAAMR,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGV7C,QAAQ;;;;;;;6BAOFkD,MAAM;;;SAG1B,CAAC;QACFT,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACLJ,MAAM,CAACC,IAAI,CAACO,MAAM,EAAE,QAAQ,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAAChE,OAAO,CAACiE,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACQJ,gBAAgBA,CAACL,IAAU,EAAE9B,QAAgB;IACnD,MAAM+B,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;IACrC,MAAMqB,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,GAAG,CAAC;IACxCD,IAAI,CAACH,IAAI,GAAGjB,GAAG;IACfoB,IAAI,CAACE,QAAQ,GAAGrD,QAAQ;IACxB4C,QAAQ,CAAClD,IAAI,CAAC4D,WAAW,CAACH,IAAI,CAAC;IAC/BA,IAAI,CAACI,KAAK,EAAE;IACZX,QAAQ,CAAClD,IAAI,CAAC8D,WAAW,CAACL,IAAI,CAAC;IAE/B;IACAf,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;EAClD;EAEA;EACQJ,oBAAoBA,CAACsB,QAAgB,EAAEjD,QAAgB;IAC7D,IAAI,CAACiD,QAAQ,EAAE;MACb,IAAI,CAAC3E,OAAO,CAACiE,YAAY,CAAC,SAAS,CAAC;MACpC;IACF;IAEA,IAAI,IAAI,CAAC5B,cAAc,CAACsC,QAAQ,CAAC,EAAE;MACjC,IAAI,CAACQ,kBAAkB,CAACR,QAAQ,EAAEjD,QAAQ,CAAC;IAC7C,CAAC,MAAM;MACL;MACA0C,MAAM,CAACC,IAAI,CAACM,QAAQ,EAAE,QAAQ,CAAC;IACjC;EACF;EAEA;EACQQ,kBAAkBA,CAACC,UAAkB,EAAE1D,QAAgB;IAC7D,IAAI;MACF;MACA,MAAM2D,aAAa,GAAGD,UAAU,CAACE,QAAQ,CAAC,GAAG,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,UAAU;MAEtF;MACA,IAAII,QAAQ,GAAG,0BAA0B;MACzC,MAAMC,SAAS,GAAG/D,QAAQ,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,EAAE,EAAEC,WAAW,EAAE;MAE1D,QAAQF,SAAS;QACf,KAAK,KAAK;UACRD,QAAQ,GAAG,iBAAiB;UAC5B;QACF,KAAK,KAAK;QACV,KAAK,MAAM;UACTA,QAAQ,GAAG,YAAY;UACvB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,WAAW;UACtB;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,kBAAkB;UAC7B;QACF,KAAK,KAAK;UACRA,QAAQ,GAAG,iBAAiB;UAC5B;MACJ;MAEA;MACA,MAAMI,cAAc,GAAGC,IAAI,CAACR,aAAa,CAAC;MAC1C,MAAMS,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACtG,MAAM,CAAC;MACpD,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAACtG,MAAM,EAAE0G,CAAC,EAAE,EAAE;QAC9CF,WAAW,CAACE,CAAC,CAAC,GAAGJ,cAAc,CAACK,UAAU,CAACD,CAAC,CAAC;MAC/C;MACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;MAC7C,MAAMtC,IAAI,GAAG,IAAI4C,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;QAAEG,IAAI,EAAEb;MAAQ,CAAE,CAAC;MAEtD;MACA,MAAM/B,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,MAAMqB,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACH,IAAI,GAAGjB,GAAG;MACfoB,IAAI,CAACE,QAAQ,GAAGrD,QAAQ;MACxB4C,QAAQ,CAAClD,IAAI,CAAC4D,WAAW,CAACH,IAAI,CAAC;MAC/BA,IAAI,CAACI,KAAK,EAAE;MACZX,QAAQ,CAAClD,IAAI,CAAC8D,WAAW,CAACL,IAAI,CAAC;MAE/B;MACAf,UAAU,CAAC,MAAMJ,GAAG,CAACK,eAAe,CAACN,GAAG,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAAChE,OAAO,CAACiE,YAAY,CAAC,WAAW,CAAC;IACxC;EACF;EAEA;EACA7G,WAAWA,CAACsE,QAAgB;IAC1B,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;IAC3B,MAAM4E,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACpE,MAAMb,SAAS,GAAG/D,QAAQ,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAACG,GAAG,EAAE,EAAEC,WAAW,EAAE;IAC1D,OAAOW,eAAe,CAAChB,QAAQ,CAACG,SAAS,IAAI,EAAE,CAAC;EAClD;EAEA;EACApI,WAAWA,CAACoF,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,OAAOA,GAAG,CAACkD,WAAW,EAAE,CAACY,QAAQ,CAAC,MAAM,CAAC;IAC3C;IACA,OAAO,KAAK;EACd;EAEA;EACAxE,WAAWA,CAACU,GAAW;IACrB,IAAIA,GAAG,EAAE;MACP,MAAM+D,QAAQ,GAAG/D,GAAG,CAACkD,WAAW,EAAE;MAClC,OAAOa,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC,IAAIC,QAAQ,CAACD,QAAQ,CAAC,MAAM,CAAC;IAC/D;IACA,OAAO,KAAK;EACd;EACA;EACAvJ,WAAWA,CAAC0E,QAAmC;IAC7C,MAAM+E,IAAI,GAAG/E,QAAQ,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACtE,WAAW,CAACqJ,IAAI,CAAC,EAAE;MAC1B,OAAO,4BAA4B;IACrC,CAAC,MAAM,IAAI,IAAI,CAACpJ,WAAW,CAACoJ,IAAI,CAAC,EAAE;MACjC,OAAO,6BAA6B;IACtC,CAAC,MAAM,IAAI,IAAI,CAAC1E,WAAW,CAAC0E,IAAI,CAAC,EAAE;MACjC,OAAO,0BAA0B;IACnC,CAAC,MAAM;MACL,OAAO,0BAA0B;IACnC;EACF;EAEA;EACAtJ,eAAeA,CAACuE,QAAmC;IACjD,MAAM+E,IAAI,GAAG/E,QAAQ,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACtE,WAAW,CAACqJ,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,IAAI,CAACpJ,WAAW,CAACoJ,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM,IAAI,IAAI,CAAC1E,WAAW,CAAC0E,IAAI,CAAC,EAAE;MACjC,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEA;EACQtD,gBAAgBA,CAACJ,OAAe,EAAErB,QAAgB;IACxD,IAAI;MACF,MAAMuB,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;MAC1C,MAAMoB,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC3C,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,QAAQ,CAACC,KAAK,CAAC;;;uBAGV7C,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAoCKA,QAAQ;;4BAEhBuB,QAAQ,UAAUvB,QAAQ;;;;SAI7C,CAAC;QACFyC,SAAS,CAACG,QAAQ,CAACE,KAAK,EAAE;MAC5B,CAAC,MAAM;QACL;QACAJ,MAAM,CAACC,IAAI,CAACpB,QAAQ,EAAE,QAAQ,CAAC;MACjC;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAI,CAAChE,OAAO,CAACiE,YAAY,CAAC,UAAU,CAAC;IACvC;EACF;EACA;EACQf,WAAWA,CAACwD,aAAqB;IACvC,IAAI,CAACA,aAAa,EAAE,OAAO,EAAE;IAE7B;IACA,IAAIA,aAAa,CAAChE,UAAU,CAAC,OAAO,CAAC,EAAE;MACrC,OAAOgE,aAAa;IACtB;IAEA;IACA,IAAIA,aAAa,CAAChE,UAAU,CAAC,MAAM,CAAC,EAAE;MACpC,OAAOgE,aAAa;IACtB;IAEA;IACA,OAAO,0BAA0BA,aAAa,EAAE;EAClD;EAEAC,YAAYA,CAACxE,KAAU,EAAElF,SAAc;IACrC;IACA;IACA;IACA;IACA;IACAmH,MAAM,CAACC,IAAI,CAAClC,KAAK,EAAE,QAAQ,CAAC;EAC9B;EAEArD,YAAYA,CAAC8H,SAAkB;IAC7B,IAAI,CAACA,SAAS,EAAE;MACd,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,IAAI,CAAC5G,iBAAiB,CAAC6G,aAAa,CAACxH,MAAM,GAAG,CAAC,EAAE;QACnD,IAAI,CAACU,OAAO,CAAC+G,aAAa,CAAC,IAAI,CAAC9G,iBAAiB,CAAC6G,aAAa,CAAC;QAChE;MACF;IACF;IACA,IAAI,CAAClH,qBAAqB,CAACoH,6CAA6C,CAAC;MACvE5F,IAAI,EAAE;QACJb,GAAG,EAAE,IAAI,CAACA,GAAG;QACbnB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBK,UAAU,EAAEmH,SAAS;QACrB9I,OAAO,EAAE,IAAI,CAACY;;KAEjB,CAAC,CAACoC,IAAI,CACLrF,GAAG,CAAC6F,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,KAAK,CAAC,EAAE;QACxB,IAAI,CAACvB,OAAO,CAACiH,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC/F,sBAAsB,EAAE;QAC7B,IAAI,IAAI,CAACzD,eAAe,CAACS,cAAc,EAAEoB,MAAM,IAAI,CAAC,EAAE;UACpD,IAAI,CAAC7B,eAAe,CAACS,cAAc,EAAEgJ,IAAI,CAAC;YACxCrJ,QAAQ,EAAE,IAAI,CAACuC,SAAS,CAAC+G,QAAQ;YACjC3J,WAAW,EAAE,IAAI4J,IAAI,EAAE,CAACC,WAAW,EAAE;YACrCvJ,OAAO,EAAE,IAAI,CAACY;WACf,CAAC;QACJ;QACA,IAAI,CAACA,MAAM,GAAG,EAAE;MAClB;MACA,IAAI,CAACE,MAAM,EAAE;IACf,CAAC,CAAC,CACH,CAACoC,SAAS,EAAE;EACf;EAEApC,MAAMA,CAAA;IACJ,IAAI,CAACsB,aAAa,CAACgH,IAAI,CAAC;MACtBI,MAAM;MACNC,OAAO,EAAE,IAAI,CAAC3G;KACf,CAAC;IACF,IAAI,CAACb,SAAS,CAACyH,IAAI,EAAE;EACvB;EAEAX,UAAUA,CAAA;IACR,IAAI,CAAC5G,iBAAiB,CAACwH,KAAK,EAAE;IAC9B,IAAI,CAACxH,iBAAiB,CAACyH,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAChJ,MAAM,CAAC;EACtD;;;uCA9cWgB,8BAA8B,EAAA3D,EAAA,CAAA4L,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA9L,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhM,EAAA,CAAA4L,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAlM,EAAA,CAAA4L,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAApM,EAAA,CAAA4L,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAtM,EAAA,CAAA4L,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAAxM,EAAA,CAAA4L,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAA1M,EAAA,CAAA4L,iBAAA,CAAAe,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA9BjJ,8BAA8B;MAAAkJ,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/M,EAAA,CAAAgN,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BzCtN,EAFF,CAAAC,cAAA,cAAS,mBAEO;UACZD,EAAA,CAAAgC,UAAA,IAAAwL,6CAAA,mBAAsG;UA2J1GxN,EADE,CAAAI,YAAA,EAAe,EACP;;;UA3JAJ,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAAkC,UAAA,WAAAqL,GAAA,CAAA7L,eAAA,CAAuB;;;qBDmB7BjC,YAAY,EAAA0M,EAAA,CAAAsB,OAAA,EAAAtB,EAAA,CAAAuB,IAAA,EAAAvB,EAAA,CAAAwB,QAAA,EACZhO,YAAY,EAAAiO,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAC,GAAA,CAAAC,eAAA,EAAAD,GAAA,CAAAE,mBAAA,EAAAF,GAAA,CAAAG,gBAAA,EAAAH,GAAA,CAAAI,iBAAA,EAAAC,GAAA,CAAAC,mBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}