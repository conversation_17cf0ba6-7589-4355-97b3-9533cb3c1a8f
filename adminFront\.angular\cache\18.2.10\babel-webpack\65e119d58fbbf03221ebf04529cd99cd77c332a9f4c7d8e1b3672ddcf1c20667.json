{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Slovak [sk]\n//! author : <PERSON> : https://github.com/k2s\n//! based on work of petrbela : https://github.com/petrbela\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = 'január_február_marec_apríl_máj_jún_júl_august_september_október_november_december'.split('_'),\n    monthsShort = 'jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec'.split('_');\n  function plural(n) {\n    return n > 1 && n < 5;\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        // a few seconds / in a few seconds / a few seconds ago\n        return withoutSuffix || isFuture ? 'pár sekúnd' : 'pár sekundami';\n      case 'ss':\n        // 9 seconds / in 9 seconds / 9 seconds ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'sekundy' : 'sekúnd');\n        } else {\n          return result + 'sekundami';\n        }\n      case 'm':\n        // a minute / in a minute / a minute ago\n        return withoutSuffix ? 'minúta' : isFuture ? 'minútu' : 'minútou';\n      case 'mm':\n        // 9 minutes / in 9 minutes / 9 minutes ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'minúty' : 'minút');\n        } else {\n          return result + 'minútami';\n        }\n      case 'h':\n        // an hour / in an hour / an hour ago\n        return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n      case 'hh':\n        // 9 hours / in 9 hours / 9 hours ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'hodiny' : 'hodín');\n        } else {\n          return result + 'hodinami';\n        }\n      case 'd':\n        // a day / in a day / a day ago\n        return withoutSuffix || isFuture ? 'deň' : 'dňom';\n      case 'dd':\n        // 9 days / in 9 days / 9 days ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'dni' : 'dní');\n        } else {\n          return result + 'dňami';\n        }\n      case 'M':\n        // a month / in a month / a month ago\n        return withoutSuffix || isFuture ? 'mesiac' : 'mesiacom';\n      case 'MM':\n        // 9 months / in 9 months / 9 months ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'mesiace' : 'mesiacov');\n        } else {\n          return result + 'mesiacmi';\n        }\n      case 'y':\n        // a year / in a year / a year ago\n        return withoutSuffix || isFuture ? 'rok' : 'rokom';\n      case 'yy':\n        // 9 years / in 9 years / 9 years ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'roky' : 'rokov');\n        } else {\n          return result + 'rokmi';\n        }\n    }\n  }\n  var sk = moment.defineLocale('sk', {\n    months: months,\n    monthsShort: monthsShort,\n    weekdays: 'nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota'.split('_'),\n    weekdaysShort: 'ne_po_ut_st_št_pi_so'.split('_'),\n    weekdaysMin: 'ne_po_ut_st_št_pi_so'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[dnes o] LT',\n      nextDay: '[zajtra o] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[v nedeľu o] LT';\n          case 1:\n          case 2:\n            return '[v] dddd [o] LT';\n          case 3:\n            return '[v stredu o] LT';\n          case 4:\n            return '[vo štvrtok o] LT';\n          case 5:\n            return '[v piatok o] LT';\n          case 6:\n            return '[v sobotu o] LT';\n        }\n      },\n      lastDay: '[včera o] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[minulú nedeľu o] LT';\n          case 1:\n          case 2:\n            return '[minulý] dddd [o] LT';\n          case 3:\n            return '[minulú stredu o] LT';\n          case 4:\n          case 5:\n            return '[minulý] dddd [o] LT';\n          case 6:\n            return '[minulú sobotu o] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'pred %s',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sk;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}