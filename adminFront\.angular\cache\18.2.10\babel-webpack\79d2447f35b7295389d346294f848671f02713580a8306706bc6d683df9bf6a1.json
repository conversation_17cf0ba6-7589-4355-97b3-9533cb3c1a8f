{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiSpecialChangeGetSpecialChangeFilePost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiSpecialChangeGetSpecialChangeFilePost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiSpecialChangeGetSpecialChangeFilePost$Plain.PATH = '/api/SpecialChange/GetSpecialChangeFile';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiSpecialChangeGetSpecialChangeFilePost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\special-change\\api-special-change-get-special-change-file-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetSpecialChangeFileArgs } from '../../models/get-special-change-file-args';\r\nimport { SpecialChangeFileGroupListResponseBase } from '../../models/special-change-file-group-list-response-base';\r\n\r\nexport interface ApiSpecialChangeGetSpecialChangeFilePost$Plain$Params {\r\n      body?: GetSpecialChangeFileArgs\r\n}\r\n\r\nexport function apiSpecialChangeGetSpecialChangeFilePost$Plain(http: HttpClient, rootUrl: string, params?: ApiSpecialChangeGetSpecialChangeFilePost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<SpecialChangeFileGroupListResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiSpecialChangeGetSpecialChangeFilePost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<SpecialChangeFileGroupListResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiSpecialChangeGetSpecialChangeFilePost$Plain.PATH = '/api/SpecialChange/GetSpecialChangeFile';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAStD,OAAM,SAAUC,8CAA8CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA8D,EAAEC,OAAqB;EACrL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,8CAA8C,CAACM,IAAI,EAAE,MAAM,CAAC;EACnG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAA+D;EACxE,CAAC,CAAC,CACH;AACH;AAEAb,8CAA8C,CAACM,IAAI,GAAG,yCAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}