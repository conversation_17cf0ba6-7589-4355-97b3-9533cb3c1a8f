{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiMaterialExportExcelMaterialListPost$Json } from '../fn/material/api-material-export-excel-material-list-post-json';\nimport { apiMaterialExportExcelMaterialListPost$Plain } from '../fn/material/api-material-export-excel-material-list-post-plain';\nimport { apiMaterialGetMaterialListPost$Json } from '../fn/material/api-material-get-material-list-post-json';\nimport { apiMaterialGetMaterialListPost$Plain } from '../fn/material/api-material-get-material-list-post-plain';\nimport { apiMaterialImportExcelMaterialListPost$Json } from '../fn/material/api-material-import-excel-material-list-post-json';\nimport { apiMaterialImportExcelMaterialListPost$Plain } from '../fn/material/api-material-import-excel-material-list-post-plain';\nimport { apiMaterialSaveMaterialAdminPost$Json } from '../fn/material/api-material-save-material-admin-post-json';\nimport { apiMaterialSaveMaterialAdminPost$Plain } from '../fn/material/api-material-save-material-admin-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let MaterialService = /*#__PURE__*/(() => {\n  class MaterialService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiMaterialGetMaterialListPost()` */\n    static {\n      this.ApiMaterialGetMaterialListPostPath = '/api/Material/GetMaterialList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialGetMaterialListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialGetMaterialListPost$Plain$Response(params, context) {\n      return apiMaterialGetMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialGetMaterialListPost$Plain(params, context) {\n      return this.apiMaterialGetMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialGetMaterialListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialGetMaterialListPost$Json$Response(params, context) {\n      return apiMaterialGetMaterialListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialGetMaterialListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialGetMaterialListPost$Json(params, context) {\n      return this.apiMaterialGetMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiMaterialSaveMaterialAdminPost()` */\n    static {\n      this.ApiMaterialSaveMaterialAdminPostPath = '/api/Material/SaveMaterialAdmin';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialSaveMaterialAdminPost$Plain$Response(params, context) {\n      return apiMaterialSaveMaterialAdminPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialSaveMaterialAdminPost$Plain(params, context) {\n      return this.apiMaterialSaveMaterialAdminPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialSaveMaterialAdminPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialSaveMaterialAdminPost$Json$Response(params, context) {\n      return apiMaterialSaveMaterialAdminPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialSaveMaterialAdminPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialSaveMaterialAdminPost$Json(params, context) {\n      return this.apiMaterialSaveMaterialAdminPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiMaterialExportExcelMaterialListPost()` */\n    static {\n      this.ApiMaterialExportExcelMaterialListPostPath = '/api/Material/ExportExcelMaterialList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialExportExcelMaterialListPost$Plain$Response(params, context) {\n      return apiMaterialExportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialExportExcelMaterialListPost$Plain(params, context) {\n      return this.apiMaterialExportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialExportExcelMaterialListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialExportExcelMaterialListPost$Json$Response(params, context) {\n      return apiMaterialExportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialExportExcelMaterialListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiMaterialExportExcelMaterialListPost$Json(params, context) {\n      return this.apiMaterialExportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiMaterialImportExcelMaterialListPost()` */\n    static {\n      this.ApiMaterialImportExcelMaterialListPostPath = '/api/Material/ImportExcelMaterialList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiMaterialImportExcelMaterialListPost$Plain$Response(params, context) {\n      return apiMaterialImportExcelMaterialListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiMaterialImportExcelMaterialListPost$Plain(params, context) {\n      return this.apiMaterialImportExcelMaterialListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiMaterialImportExcelMaterialListPost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiMaterialImportExcelMaterialListPost$Json$Response(params, context) {\n      return apiMaterialImportExcelMaterialListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiMaterialImportExcelMaterialListPost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiMaterialImportExcelMaterialListPost$Json(params, context) {\n      return this.apiMaterialImportExcelMaterialListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function MaterialService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || MaterialService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MaterialService,\n        factory: MaterialService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MaterialService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}