{"ast": null, "code": "export class OrdersProfitChartData {}", "map": {"version": 3, "names": ["OrdersProfitChartData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\orders-profit-chart.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\nimport { OrdersChart } from './orders-chart';\r\nimport { ProfitChart  } from './profit-chart';\r\n\r\nexport interface OrderProfitChartSummary {\r\n  title: string;\r\n  value: number;\r\n}\r\n\r\nexport abstract class OrdersProfitChartData {\r\n  abstract getOrderProfitChartSummary(): Observable<OrderProfitChartSummary[]>;\r\n  abstract getOrdersChartData(period: string): Observable<OrdersChart>;\r\n  abstract getProfitChartData(period: string): Observable<ProfitChart>;\r\n}\r\n"], "mappings": "AASA,OAAM,MAAgBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}