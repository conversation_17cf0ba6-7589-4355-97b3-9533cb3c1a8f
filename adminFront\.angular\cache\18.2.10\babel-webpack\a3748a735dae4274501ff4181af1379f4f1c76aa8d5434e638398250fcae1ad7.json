{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiReviewDeleteReviewPost$Json } from '../fn/review/api-review-delete-review-post-json';\nimport { apiReviewDeleteReviewPost$Plain } from '../fn/review/api-review-delete-review-post-plain';\nimport { apiReviewGetReviewByIdPost$Json } from '../fn/review/api-review-get-review-by-id-post-json';\nimport { apiReviewGetReviewByIdPost$Plain } from '../fn/review/api-review-get-review-by-id-post-plain';\nimport { apiReviewGetReviewListPost$Json } from '../fn/review/api-review-get-review-list-post-json';\nimport { apiReviewGetReviewListPost$Plain } from '../fn/review/api-review-get-review-list-post-plain';\nimport { apiReviewSaveReviewPost$Json } from '../fn/review/api-review-save-review-post-json';\nimport { apiReviewSaveReviewPost$Plain } from '../fn/review/api-review-save-review-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let ReviewService = /*#__PURE__*/(() => {\n  class ReviewService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiReviewGetReviewListPost()` */\n    static {\n      this.ApiReviewGetReviewListPostPath = '/api/Review/GetReviewList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewGetReviewListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewListPost$Plain$Response(params, context) {\n      return apiReviewGetReviewListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewListPost$Plain(params, context) {\n      return this.apiReviewGetReviewListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewGetReviewListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewListPost$Json$Response(params, context) {\n      return apiReviewGetReviewListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewGetReviewListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewListPost$Json(params, context) {\n      return this.apiReviewGetReviewListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiReviewDeleteReviewPost()` */\n    static {\n      this.ApiReviewDeleteReviewPostPath = '/api/Review/DeleteReview';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewDeleteReviewPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewDeleteReviewPost$Plain$Response(params, context) {\n      return apiReviewDeleteReviewPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewDeleteReviewPost$Plain(params, context) {\n      return this.apiReviewDeleteReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewDeleteReviewPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewDeleteReviewPost$Json$Response(params, context) {\n      return apiReviewDeleteReviewPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewDeleteReviewPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewDeleteReviewPost$Json(params, context) {\n      return this.apiReviewDeleteReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiReviewSaveReviewPost()` */\n    static {\n      this.ApiReviewSaveReviewPostPath = '/api/Review/SaveReview';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewSaveReviewPost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiReviewSaveReviewPost$Plain$Response(params, context) {\n      return apiReviewSaveReviewPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiReviewSaveReviewPost$Plain(params, context) {\n      return this.apiReviewSaveReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewSaveReviewPost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiReviewSaveReviewPost$Json$Response(params, context) {\n      return apiReviewSaveReviewPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewSaveReviewPost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiReviewSaveReviewPost$Json(params, context) {\n      return this.apiReviewSaveReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiReviewGetReviewByIdPost()` */\n    static {\n      this.ApiReviewGetReviewByIdPostPath = '/api/Review/GetReviewById';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewGetReviewByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewByIdPost$Plain$Response(params, context) {\n      return apiReviewGetReviewByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewByIdPost$Plain(params, context) {\n      return this.apiReviewGetReviewByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiReviewGetReviewByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewByIdPost$Json$Response(params, context) {\n      return apiReviewGetReviewByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiReviewGetReviewByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiReviewGetReviewByIdPost$Json(params, context) {\n      return this.apiReviewGetReviewByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function ReviewService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || ReviewService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ReviewService,\n        factory: ReviewService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ReviewService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}