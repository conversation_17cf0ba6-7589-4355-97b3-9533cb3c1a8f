{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits } from \"../utils.js\";\nexport var QuarterParser = /*#__PURE__*/function (_Parser) {\n  _inherits(QuarterParser, _Parser);\n  var _super = _createSuper(QuarterParser);\n  function QuarterParser() {\n    var _this;\n    _classCallCheck(this, QuarterParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 120);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'q', 'M', 'L', 'w', 'I', 'd', 'D', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(QuarterParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        // 1, 2, 3, 4\n        case 'Q':\n        case 'QQ':\n          // 01, 02, 03, 04\n          return parseNDigits(token.length, dateString);\n        // 1st, 2nd, 3rd, 4th\n        case 'Qo':\n          return match.ordinalNumber(dateString, {\n            unit: 'quarter'\n          });\n        // Q1, Q2, Q3, Q4\n        case 'QQQ':\n          return match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n        case 'QQQQQ':\n          return match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n        // 1st quarter, 2nd quarter, ...\n        case 'QQQQ':\n        default:\n          return match.quarter(dateString, {\n            width: 'wide',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'abbreviated',\n            context: 'formatting'\n          }) || match.quarter(dateString, {\n            width: 'narrow',\n            context: 'formatting'\n          });\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value >= 1 && value <= 4;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCMonth((value - 1) * 3, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return QuarterParser;\n}(Parser);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}