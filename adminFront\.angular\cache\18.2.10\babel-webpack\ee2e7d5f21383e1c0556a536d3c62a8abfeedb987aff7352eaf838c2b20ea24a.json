{"ast": null, "code": "export class ProfitChartData {}", "map": {"version": 3, "names": ["ProfitChartData"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@core\\data\\profit-chart.ts"], "sourcesContent": ["export interface ProfitChart {\r\n  chartLabel: string[];\r\n  data: number[][];\r\n}\r\n\r\nexport abstract class ProfitChartData {\r\n  abstract getProfitChartData(period: string): ProfitChart;\r\n}\r\n"], "mappings": "AAKA,OAAM,MAAgBA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}