{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain.PATH = '/api/RegularNoticeFile/GetRegularNoticeFileById';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\fn\\regular-notice-file\\api-regular-notice-file-get-regular-notice-file-by-id-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { GetRegularNoticeFileByIdResResponseBase } from '../../models/get-regular-notice-file-by-id-res-response-base';\r\n\r\nexport interface ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain(http: HttpClient, rootUrl: string, params?: ApiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<GetRegularNoticeFileByIdResResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiRegularNoticeFileGetRegularNoticeFileByIdPost$Plain.PATH = '/api/RegularNoticeFile/GetRegularNoticeFileById';\r\n"], "mappings": "AAAA;AACA;AACA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,sDAAsDA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAAsE,EAAEC,OAAqB;EACrM,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,sDAAsD,CAACM,IAAI,EAAE,MAAM,CAAC;EAC3G,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAgE;EACzE,CAAC,CAAC,CACH;AACH;AAEAb,sDAAsD,CAACM,IAAI,GAAG,iDAAiD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}