{"ast": null, "code": "// ===================================\n// 自定義金色主題 (Custom Gold Theme)\n// ===================================\nimport { DEFAULT_THEME as baseTheme } from '@nebular/theme';\nconst baseThemeVariables = baseTheme.variables;\n// 金色主題變數覆蓋\nconst goldThemeVariables = {\n  // 主要品牌色系\n  primary: '#AE9B66',\n  // 主要金色-深色\n  primaryLight: '#B8A676',\n  // 主要金色-淺色\n  primaryDark: '#9B8A5A',\n  // 主要金色-更深\n  // 文字色系\n  fgText: '#231815',\n  // 主要文字\n  fg: '#3A4246',\n  // 次要文字\n  fgHeading: '#231815',\n  // 標題文字\n  fgHint: '#818181',\n  // 輔助文字\n  // 背景色系\n  bg: '#FFFFFF',\n  // 主要背景\n  bg2: '#F3F1EA',\n  // 次要背景\n  bg3: '#f8f9fa',\n  // 第三層背景\n  bg4: '#f0f0f0',\n  // 第四層背景\n  // 邊框色系\n  border: 'rgba(144, 150, 157, 0.4)',\n  // 主要邊框\n  border2: 'rgba(144, 150, 157, 0.2)',\n  // 次要邊框\n  border3: 'rgba(144, 150, 157, 0.1)',\n  // 第三層邊框\n  border4: 'rgba(174, 155, 102, 0.3)',\n  // 金色邊框\n  border5: 'rgba(174, 155, 102, 0.1)',\n  // 淺金色邊框\n  // 系統色彩保持現有\n  success: '#27ae60',\n  // 成功色 (保留專案現有)\n  successLight: '#2ecc71',\n  // 淺成功色\n  warning: '#f39c12',\n  // 警告色\n  warningLight: '#f1c40f',\n  // 淺警告色\n  danger: '#e74c3c',\n  // 危險色\n  dangerLight: '#ec7063',\n  // 淺危險色\n  info: '#169BD5',\n  // 資訊色 (保留專案現有)\n  infoLight: '#81d3f8',\n  // 淺資訊色\n  // 分隔線\n  separator: 'rgba(144, 150, 157, 0.2)',\n  // 特殊色彩\n  disabled: '#90969D' // 停用狀態\n};\nexport const GOLD_THEME = {\n  name: 'gold',\n  base: 'default',\n  variables: {\n    ...baseThemeVariables,\n    ...goldThemeVariables,\n    // 圖表相關色彩\n    temperature: {\n      arcFill: ['#B8A676', '#AE9B66', '#9B8A5A', '#C4B382', '#D4C49A'],\n      arcEmpty: goldThemeVariables.bg2,\n      thumbBg: goldThemeVariables.bg2,\n      thumbBorder: goldThemeVariables.primary\n    },\n    solar: {\n      gradientLeft: goldThemeVariables.primary,\n      gradientRight: goldThemeVariables.primaryLight,\n      shadowColor: 'rgba(174, 155, 102, 0.2)',\n      secondSeriesFill: goldThemeVariables.bg2,\n      radius: ['80%', '90%']\n    },\n    traffic: {\n      tooltipBg: goldThemeVariables.bg,\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      yAxisSplitLine: goldThemeVariables.separator,\n      lineBg: goldThemeVariables.border4,\n      lineShadowBlur: '1',\n      itemColor: goldThemeVariables.border4,\n      itemBorderColor: goldThemeVariables.border4,\n      itemEmphasisBorderColor: goldThemeVariables.primary,\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      shadowLineShadow: 'rgba(0, 0, 0, 0)',\n      gradFrom: goldThemeVariables.bg2,\n      gradTo: goldThemeVariables.bg2\n    },\n    electricity: {\n      tooltipBg: goldThemeVariables.bg,\n      tooltipLineColor: goldThemeVariables.fgText,\n      tooltipLineWidth: '0',\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      axisLineColor: goldThemeVariables.border3,\n      xAxisTextColor: goldThemeVariables.fg,\n      yAxisSplitLine: goldThemeVariables.separator,\n      itemBorderColor: goldThemeVariables.primary,\n      lineStyle: 'solid',\n      lineWidth: '4',\n      lineGradFrom: goldThemeVariables.primary,\n      lineGradTo: goldThemeVariables.primary,\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: goldThemeVariables.bg2,\n      areaGradTo: goldThemeVariables.bg2,\n      shadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    bubbleMap: {\n      titleColor: goldThemeVariables.fgText,\n      areaColor: goldThemeVariables.bg4,\n      areaHoverColor: goldThemeVariables.primary,\n      areaBorderColor: goldThemeVariables.border5\n    },\n    profitBarAnimationEchart: {\n      textColor: goldThemeVariables.fgText,\n      firstAnimationBarColor: goldThemeVariables.primary,\n      secondAnimationBarColor: goldThemeVariables.success,\n      splitLineStyleOpacity: '1',\n      splitLineStyleWidth: '1',\n      splitLineStyleColor: goldThemeVariables.separator,\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: goldThemeVariables.bg,\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    },\n    trafficBarEchart: {\n      gradientFrom: goldThemeVariables.primaryLight,\n      gradientTo: goldThemeVariables.primary,\n      shadow: goldThemeVariables.primaryLight,\n      shadowBlur: '0',\n      axisTextColor: goldThemeVariables.fgText,\n      axisFontSize: '12',\n      tooltipBg: goldThemeVariables.bg,\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal'\n    },\n    countryOrders: {\n      countryBorderColor: goldThemeVariables.border4,\n      countryFillColor: goldThemeVariables.bg3,\n      countryBorderWidth: '1',\n      hoveredCountryBorderColor: goldThemeVariables.primary,\n      hoveredCountryFillColor: goldThemeVariables.primaryLight,\n      hoveredCountryBorderWidth: '1',\n      chartAxisLineColor: goldThemeVariables.border4,\n      chartAxisTextColor: goldThemeVariables.fg,\n      chartAxisFontSize: '16',\n      chartGradientTo: goldThemeVariables.primary,\n      chartGradientFrom: goldThemeVariables.primaryLight,\n      chartAxisSplitLine: goldThemeVariables.separator,\n      chartShadowLineColor: goldThemeVariables.primaryLight,\n      chartLineBottomShadowColor: goldThemeVariables.primary,\n      chartInnerLineColor: goldThemeVariables.bg2\n    },\n    echarts: {\n      bg: goldThemeVariables.bg,\n      textColor: goldThemeVariables.fgText,\n      axisLineColor: goldThemeVariables.fgText,\n      splitLineColor: goldThemeVariables.separator,\n      itemHoverShadowColor: 'rgba(174, 155, 102, 0.5)',\n      tooltipBackgroundColor: goldThemeVariables.primary,\n      areaOpacity: '0.7'\n    },\n    chartjs: {\n      axisLineColor: goldThemeVariables.separator,\n      textColor: goldThemeVariables.fgText\n    },\n    orders: {\n      tooltipBg: goldThemeVariables.bg,\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '0',\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: goldThemeVariables.border4,\n      axisFontSize: '16',\n      axisTextColor: goldThemeVariables.fg,\n      yAxisSplitLine: goldThemeVariables.separator,\n      itemBorderColor: goldThemeVariables.primary,\n      lineStyle: 'solid',\n      lineWidth: '4',\n      // 第一條線\n      firstAreaGradFrom: goldThemeVariables.bg3,\n      firstAreaGradTo: goldThemeVariables.bg3,\n      firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // 第二條線\n      secondLineGradFrom: goldThemeVariables.primary,\n      secondLineGradTo: goldThemeVariables.primary,\n      secondAreaGradFrom: 'rgba(174, 155, 102, 0.2)',\n      secondAreaGradTo: 'rgba(174, 155, 102, 0)',\n      secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\n      // 第三條線\n      thirdLineGradFrom: goldThemeVariables.success,\n      thirdLineGradTo: goldThemeVariables.successLight,\n      thirdAreaGradFrom: 'rgba(39, 174, 96, 0.2)',\n      thirdAreaGradTo: 'rgba(39, 174, 96, 0)',\n      thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)'\n    },\n    profit: {\n      bg: goldThemeVariables.bg,\n      textColor: goldThemeVariables.fgText,\n      axisLineColor: goldThemeVariables.border4,\n      splitLineColor: goldThemeVariables.separator,\n      areaOpacity: '1',\n      axisFontSize: '16',\n      axisTextColor: goldThemeVariables.fg,\n      // 第一個柱狀圖\n      firstLineGradFrom: goldThemeVariables.bg3,\n      firstLineGradTo: goldThemeVariables.bg3,\n      firstLineShadow: 'rgba(0, 0, 0, 0)',\n      // 第二個柱狀圖\n      secondLineGradFrom: goldThemeVariables.primary,\n      secondLineGradTo: goldThemeVariables.primary,\n      secondLineShadow: 'rgba(0, 0, 0, 0)',\n      // 第三個柱狀圖\n      thirdLineGradFrom: goldThemeVariables.success,\n      thirdLineGradTo: goldThemeVariables.success,\n      thirdLineShadow: 'rgba(0, 0, 0, 0)'\n    },\n    orderProfitLegend: {\n      firstItem: goldThemeVariables.success,\n      secondItem: goldThemeVariables.primary,\n      thirdItem: goldThemeVariables.bg3\n    },\n    visitors: {\n      tooltipBg: goldThemeVariables.bg,\n      tooltipLineColor: 'rgba(0, 0, 0, 0)',\n      tooltipLineWidth: '1',\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '20',\n      axisLineColor: goldThemeVariables.border4,\n      axisFontSize: '16',\n      axisTextColor: goldThemeVariables.fg,\n      yAxisSplitLine: goldThemeVariables.separator,\n      itemBorderColor: goldThemeVariables.primary,\n      lineStyle: 'dotted',\n      lineWidth: '6',\n      lineGradFrom: '#ffffff',\n      lineGradTo: '#ffffff',\n      lineShadow: 'rgba(0, 0, 0, 0)',\n      areaGradFrom: goldThemeVariables.primary,\n      areaGradTo: goldThemeVariables.primaryLight,\n      innerLineStyle: 'solid',\n      innerLineWidth: '1',\n      innerAreaGradFrom: goldThemeVariables.success,\n      innerAreaGradTo: goldThemeVariables.success\n    },\n    visitorsLegend: {\n      firstIcon: goldThemeVariables.success,\n      secondIcon: goldThemeVariables.primary\n    },\n    visitorsPie: {\n      firstPieGradientLeft: goldThemeVariables.success,\n      firstPieGradientRight: goldThemeVariables.success,\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      firstPieRadius: ['70%', '90%'],\n      secondPieGradientLeft: goldThemeVariables.warning,\n      secondPieGradientRight: goldThemeVariables.warningLight,\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieRadius: ['60%', '97%'],\n      shadowOffsetX: '0',\n      shadowOffsetY: '0'\n    },\n    visitorsPieLegend: {\n      firstSection: goldThemeVariables.warning,\n      secondSection: goldThemeVariables.success\n    },\n    earningPie: {\n      radius: ['65%', '100%'],\n      center: ['50%', '50%'],\n      fontSize: '22',\n      firstPieGradientLeft: goldThemeVariables.success,\n      firstPieGradientRight: goldThemeVariables.success,\n      firstPieShadowColor: 'rgba(0, 0, 0, 0)',\n      secondPieGradientLeft: goldThemeVariables.primary,\n      secondPieGradientRight: goldThemeVariables.primary,\n      secondPieShadowColor: 'rgba(0, 0, 0, 0)',\n      thirdPieGradientLeft: goldThemeVariables.warning,\n      thirdPieGradientRight: goldThemeVariables.warning,\n      thirdPieShadowColor: 'rgba(0, 0, 0, 0)'\n    },\n    earningLine: {\n      gradFrom: goldThemeVariables.primary,\n      gradTo: goldThemeVariables.primary,\n      tooltipTextColor: goldThemeVariables.fgText,\n      tooltipFontWeight: 'normal',\n      tooltipFontSize: '16',\n      tooltipBg: goldThemeVariables.bg,\n      tooltipBorderColor: goldThemeVariables.border2,\n      tooltipBorderWidth: '1',\n      tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;'\n    }\n  }\n};", "map": {"version": 3, "names": ["DEFAULT_THEME", "baseTheme", "baseThemeVariables", "variables", "goldThemeVariables", "primary", "primaryLight", "primaryDark", "fgText", "fg", "fgHeading", "fgHint", "bg", "bg2", "bg3", "bg4", "border", "border2", "border3", "border4", "border5", "success", "successLight", "warning", "warningLight", "danger", "dangerLight", "info", "infoLight", "separator", "disabled", "GOLD_THEME", "name", "base", "temperature", "arcFill", "arcEmpty", "thumbBg", "thumbBorder", "solar", "gradientLeft", "gradientRight", "shadowColor", "secondSeriesFill", "radius", "traffic", "tooltipBg", "tooltipBorderColor", "tooltipExtraCss", "tooltipTextColor", "tooltipFontWeight", "yAxisSplitLine", "lineBg", "lineShadowBlur", "itemColor", "itemBorderColor", "itemEmphasisBorderColor", "shadowLineDarkBg", "shadowLineShadow", "gradFrom", "gradTo", "electricity", "tooltipLineColor", "tooltipLineWidth", "axisLineColor", "xAxisTextColor", "lineStyle", "lineWidth", "lineGradFrom", "lineGradTo", "lineShadow", "areaGradFrom", "areaGradTo", "bubbleMap", "titleColor", "areaColor", "areaHoverColor", "areaBorderColor", "profitBarAnimationEchart", "textColor", "firstAnimationBarColor", "secondAnimationBarColor", "splitLineStyleOpacity", "splitLineStyleWidth", "splitLineStyleColor", "tooltipFontSize", "tooltipBorderWidth", "trafficBarEchart", "gradientFrom", "gradientTo", "shadow", "<PERSON><PERSON><PERSON><PERSON>", "axisTextColor", "axisFontSize", "countryOrders", "countryBorderColor", "countryFillColor", "countryBorderWidth", "hoveredCountryBorderColor", "hoveredCountryFillColor", "hoveredCountryBorderWidth", "chartAxisLineColor", "chartAxisTextColor", "chartAxisFontSize", "chartGradientTo", "chartGradientFrom", "chartAxisSplitLine", "chartShadowLineColor", "chartLineBottomShadowColor", "chartInnerLineColor", "echarts", "splitLineColor", "itemHoverShadowColor", "tooltipBackgroundColor", "areaOpacity", "chartjs", "orders", "firstAreaGradFrom", "firstAreaGradTo", "firstShadowLineDarkBg", "secondLineGradFrom", "secondLineGradTo", "secondAreaGradFrom", "secondAreaGradTo", "secondShadowLineDarkBg", "thirdLineGradFrom", "thirdLineGradTo", "thirdAreaGradFrom", "thirdAreaGradTo", "thirdShadowLineDarkBg", "profit", "firstLineGradFrom", "firstLineGradTo", "firstLineShadow", "secondLineShadow", "thirdLineShadow", "orderProfitLegend", "firstItem", "secondItem", "thirdItem", "visitors", "innerLineStyle", "innerLineWidth", "innerAreaGradFrom", "innerAreaGradTo", "visitorsLegend", "firstIcon", "secondIcon", "visitors<PERSON>ie", "firstPieGradientLeft", "firstPieGradientRight", "firstPieShadowColor", "firstPieRadius", "secondPieGradientLeft", "secondPieGradientRight", "secondPieShadowColor", "secondPieRadius", "shadowOffsetX", "shadowOffsetY", "visitorsPieLegend", "firstSection", "secondSection", "earning<PERSON>ie", "center", "fontSize", "thirdPieGradientLeft", "thirdPieGradientRight", "thirdPieShadowColor", "earningLine"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\styles\\theme.gold.ts"], "sourcesContent": ["// ===================================\r\n// 自定義金色主題 (Custom Gold Theme)\r\n// ===================================\r\n\r\nimport { NbJSThemeOptions, DEFAULT_THEME as baseTheme } from '@nebular/theme';\r\n\r\nconst baseThemeVariables = baseTheme.variables!;\r\n\r\n// 金色主題變數覆蓋\r\nconst goldThemeVariables = {\r\n    // 主要品牌色系\r\n    primary: '#AE9B66',           // 主要金色-深色\r\n    primaryLight: '#B8A676',      // 主要金色-淺色\r\n    primaryDark: '#9B8A5A',       // 主要金色-更深\r\n\r\n    // 文字色系\r\n    fgText: '#231815',            // 主要文字\r\n    fg: '#3A4246',                // 次要文字\r\n    fgHeading: '#231815',         // 標題文字\r\n    fgHint: '#818181',            // 輔助文字\r\n\r\n    // 背景色系\r\n    bg: '#FFFFFF',                // 主要背景\r\n    bg2: '#F3F1EA',               // 次要背景\r\n    bg3: '#f8f9fa',               // 第三層背景\r\n    bg4: '#f0f0f0',               // 第四層背景\r\n\r\n    // 邊框色系\r\n    border: 'rgba(144, 150, 157, 0.4)',     // 主要邊框\r\n    border2: 'rgba(144, 150, 157, 0.2)',    // 次要邊框\r\n    border3: 'rgba(144, 150, 157, 0.1)',    // 第三層邊框\r\n    border4: 'rgba(174, 155, 102, 0.3)',    // 金色邊框\r\n    border5: 'rgba(174, 155, 102, 0.1)',    // 淺金色邊框\r\n\r\n    // 系統色彩保持現有\r\n    success: '#27ae60',           // 成功色 (保留專案現有)\r\n    successLight: '#2ecc71',      // 淺成功色\r\n    warning: '#f39c12',           // 警告色\r\n    warningLight: '#f1c40f',      // 淺警告色\r\n    danger: '#e74c3c',            // 危險色\r\n    dangerLight: '#ec7063',       // 淺危險色\r\n    info: '#169BD5',              // 資訊色 (保留專案現有)\r\n    infoLight: '#81d3f8',         // 淺資訊色\r\n\r\n    // 分隔線\r\n    separator: 'rgba(144, 150, 157, 0.2)',\r\n\r\n    // 特殊色彩\r\n    disabled: '#90969D',          // 停用狀態\r\n};\r\n\r\nexport const GOLD_THEME = {\r\n    name: 'gold',\r\n    base: 'default',\r\n    variables: {\r\n        ...baseThemeVariables,\r\n        ...goldThemeVariables,\r\n\r\n        // 圖表相關色彩\r\n        temperature: {\r\n            arcFill: ['#B8A676', '#AE9B66', '#9B8A5A', '#C4B382', '#D4C49A'],\r\n            arcEmpty: goldThemeVariables.bg2,\r\n            thumbBg: goldThemeVariables.bg2,\r\n            thumbBorder: goldThemeVariables.primary,\r\n        },\r\n\r\n        solar: {\r\n            gradientLeft: goldThemeVariables.primary,\r\n            gradientRight: goldThemeVariables.primaryLight,\r\n            shadowColor: 'rgba(174, 155, 102, 0.2)',\r\n            secondSeriesFill: goldThemeVariables.bg2,\r\n            radius: ['80%', '90%'],\r\n        },\r\n\r\n        traffic: {\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n\r\n            yAxisSplitLine: goldThemeVariables.separator,\r\n\r\n            lineBg: goldThemeVariables.border4,\r\n            lineShadowBlur: '1',\r\n            itemColor: goldThemeVariables.border4,\r\n            itemBorderColor: goldThemeVariables.border4,\r\n            itemEmphasisBorderColor: goldThemeVariables.primary,\r\n            shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n            shadowLineShadow: 'rgba(0, 0, 0, 0)',\r\n            gradFrom: goldThemeVariables.bg2,\r\n            gradTo: goldThemeVariables.bg2,\r\n        },\r\n\r\n        electricity: {\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipLineColor: goldThemeVariables.fgText,\r\n            tooltipLineWidth: '0',\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n\r\n            axisLineColor: goldThemeVariables.border3,\r\n            xAxisTextColor: goldThemeVariables.fg,\r\n            yAxisSplitLine: goldThemeVariables.separator,\r\n\r\n            itemBorderColor: goldThemeVariables.primary,\r\n            lineStyle: 'solid',\r\n            lineWidth: '4',\r\n            lineGradFrom: goldThemeVariables.primary,\r\n            lineGradTo: goldThemeVariables.primary,\r\n            lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n            areaGradFrom: goldThemeVariables.bg2,\r\n            areaGradTo: goldThemeVariables.bg2,\r\n            shadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n        },\r\n\r\n        bubbleMap: {\r\n            titleColor: goldThemeVariables.fgText,\r\n            areaColor: goldThemeVariables.bg4,\r\n            areaHoverColor: goldThemeVariables.primary,\r\n            areaBorderColor: goldThemeVariables.border5,\r\n        },\r\n\r\n        profitBarAnimationEchart: {\r\n            textColor: goldThemeVariables.fgText,\r\n\r\n            firstAnimationBarColor: goldThemeVariables.primary,\r\n            secondAnimationBarColor: goldThemeVariables.success,\r\n\r\n            splitLineStyleOpacity: '1',\r\n            splitLineStyleWidth: '1',\r\n            splitLineStyleColor: goldThemeVariables.separator,\r\n\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n            tooltipFontSize: '16',\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipBorderWidth: '1',\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n        },\r\n\r\n        trafficBarEchart: {\r\n            gradientFrom: goldThemeVariables.primaryLight,\r\n            gradientTo: goldThemeVariables.primary,\r\n            shadow: goldThemeVariables.primaryLight,\r\n            shadowBlur: '0',\r\n\r\n            axisTextColor: goldThemeVariables.fgText,\r\n            axisFontSize: '12',\r\n\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n        },\r\n\r\n        countryOrders: {\r\n            countryBorderColor: goldThemeVariables.border4,\r\n            countryFillColor: goldThemeVariables.bg3,\r\n            countryBorderWidth: '1',\r\n            hoveredCountryBorderColor: goldThemeVariables.primary,\r\n            hoveredCountryFillColor: goldThemeVariables.primaryLight,\r\n            hoveredCountryBorderWidth: '1',\r\n\r\n            chartAxisLineColor: goldThemeVariables.border4,\r\n            chartAxisTextColor: goldThemeVariables.fg,\r\n            chartAxisFontSize: '16',\r\n            chartGradientTo: goldThemeVariables.primary,\r\n            chartGradientFrom: goldThemeVariables.primaryLight,\r\n            chartAxisSplitLine: goldThemeVariables.separator,\r\n            chartShadowLineColor: goldThemeVariables.primaryLight,\r\n\r\n            chartLineBottomShadowColor: goldThemeVariables.primary,\r\n\r\n            chartInnerLineColor: goldThemeVariables.bg2,\r\n        },\r\n\r\n        echarts: {\r\n            bg: goldThemeVariables.bg,\r\n            textColor: goldThemeVariables.fgText,\r\n            axisLineColor: goldThemeVariables.fgText,\r\n            splitLineColor: goldThemeVariables.separator,\r\n            itemHoverShadowColor: 'rgba(174, 155, 102, 0.5)',\r\n            tooltipBackgroundColor: goldThemeVariables.primary,\r\n            areaOpacity: '0.7',\r\n        },\r\n\r\n        chartjs: {\r\n            axisLineColor: goldThemeVariables.separator,\r\n            textColor: goldThemeVariables.fgText,\r\n        },\r\n\r\n        orders: {\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n            tooltipLineWidth: '0',\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n            tooltipFontSize: '20',\r\n\r\n            axisLineColor: goldThemeVariables.border4,\r\n            axisFontSize: '16',\r\n            axisTextColor: goldThemeVariables.fg,\r\n            yAxisSplitLine: goldThemeVariables.separator,\r\n\r\n            itemBorderColor: goldThemeVariables.primary,\r\n            lineStyle: 'solid',\r\n            lineWidth: '4',\r\n\r\n            // 第一條線\r\n            firstAreaGradFrom: goldThemeVariables.bg3,\r\n            firstAreaGradTo: goldThemeVariables.bg3,\r\n            firstShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n            // 第二條線\r\n            secondLineGradFrom: goldThemeVariables.primary,\r\n            secondLineGradTo: goldThemeVariables.primary,\r\n\r\n            secondAreaGradFrom: 'rgba(174, 155, 102, 0.2)',\r\n            secondAreaGradTo: 'rgba(174, 155, 102, 0)',\r\n            secondShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n\r\n            // 第三條線\r\n            thirdLineGradFrom: goldThemeVariables.success,\r\n            thirdLineGradTo: goldThemeVariables.successLight,\r\n\r\n            thirdAreaGradFrom: 'rgba(39, 174, 96, 0.2)',\r\n            thirdAreaGradTo: 'rgba(39, 174, 96, 0)',\r\n            thirdShadowLineDarkBg: 'rgba(0, 0, 0, 0)',\r\n        },\r\n\r\n        profit: {\r\n            bg: goldThemeVariables.bg,\r\n            textColor: goldThemeVariables.fgText,\r\n            axisLineColor: goldThemeVariables.border4,\r\n            splitLineColor: goldThemeVariables.separator,\r\n            areaOpacity: '1',\r\n\r\n            axisFontSize: '16',\r\n            axisTextColor: goldThemeVariables.fg,\r\n\r\n            // 第一個柱狀圖\r\n            firstLineGradFrom: goldThemeVariables.bg3,\r\n            firstLineGradTo: goldThemeVariables.bg3,\r\n            firstLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n            // 第二個柱狀圖\r\n            secondLineGradFrom: goldThemeVariables.primary,\r\n            secondLineGradTo: goldThemeVariables.primary,\r\n            secondLineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n            // 第三個柱狀圖\r\n            thirdLineGradFrom: goldThemeVariables.success,\r\n            thirdLineGradTo: goldThemeVariables.success,\r\n            thirdLineShadow: 'rgba(0, 0, 0, 0)',\r\n        },\r\n\r\n        orderProfitLegend: {\r\n            firstItem: goldThemeVariables.success,\r\n            secondItem: goldThemeVariables.primary,\r\n            thirdItem: goldThemeVariables.bg3,\r\n        },\r\n\r\n        visitors: {\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipLineColor: 'rgba(0, 0, 0, 0)',\r\n            tooltipLineWidth: '1',\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 8px 24px;',\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n            tooltipFontSize: '20',\r\n\r\n            axisLineColor: goldThemeVariables.border4,\r\n            axisFontSize: '16',\r\n            axisTextColor: goldThemeVariables.fg,\r\n            yAxisSplitLine: goldThemeVariables.separator,\r\n\r\n            itemBorderColor: goldThemeVariables.primary,\r\n            lineStyle: 'dotted',\r\n            lineWidth: '6',\r\n            lineGradFrom: '#ffffff',\r\n            lineGradTo: '#ffffff',\r\n            lineShadow: 'rgba(0, 0, 0, 0)',\r\n\r\n            areaGradFrom: goldThemeVariables.primary,\r\n            areaGradTo: goldThemeVariables.primaryLight,\r\n\r\n            innerLineStyle: 'solid',\r\n            innerLineWidth: '1',\r\n\r\n            innerAreaGradFrom: goldThemeVariables.success,\r\n            innerAreaGradTo: goldThemeVariables.success,\r\n        },\r\n\r\n        visitorsLegend: {\r\n            firstIcon: goldThemeVariables.success,\r\n            secondIcon: goldThemeVariables.primary,\r\n        },\r\n\r\n        visitorsPie: {\r\n            firstPieGradientLeft: goldThemeVariables.success,\r\n            firstPieGradientRight: goldThemeVariables.success,\r\n            firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n            firstPieRadius: ['70%', '90%'],\r\n\r\n            secondPieGradientLeft: goldThemeVariables.warning,\r\n            secondPieGradientRight: goldThemeVariables.warningLight,\r\n            secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n            secondPieRadius: ['60%', '97%'],\r\n            shadowOffsetX: '0',\r\n            shadowOffsetY: '0',\r\n        },\r\n\r\n        visitorsPieLegend: {\r\n            firstSection: goldThemeVariables.warning,\r\n            secondSection: goldThemeVariables.success,\r\n        },\r\n\r\n        earningPie: {\r\n            radius: ['65%', '100%'],\r\n            center: ['50%', '50%'],\r\n\r\n            fontSize: '22',\r\n\r\n            firstPieGradientLeft: goldThemeVariables.success,\r\n            firstPieGradientRight: goldThemeVariables.success,\r\n            firstPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n            secondPieGradientLeft: goldThemeVariables.primary,\r\n            secondPieGradientRight: goldThemeVariables.primary,\r\n            secondPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n\r\n            thirdPieGradientLeft: goldThemeVariables.warning,\r\n            thirdPieGradientRight: goldThemeVariables.warning,\r\n            thirdPieShadowColor: 'rgba(0, 0, 0, 0)',\r\n        },\r\n\r\n        earningLine: {\r\n            gradFrom: goldThemeVariables.primary,\r\n            gradTo: goldThemeVariables.primary,\r\n\r\n            tooltipTextColor: goldThemeVariables.fgText,\r\n            tooltipFontWeight: 'normal',\r\n            tooltipFontSize: '16',\r\n            tooltipBg: goldThemeVariables.bg,\r\n            tooltipBorderColor: goldThemeVariables.border2,\r\n            tooltipBorderWidth: '1',\r\n            tooltipExtraCss: 'border-radius: 10px; padding: 4px 16px;',\r\n        },\r\n    },\r\n} as NbJSThemeOptions;\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAA2BA,aAAa,IAAIC,SAAS,QAAQ,gBAAgB;AAE7E,MAAMC,kBAAkB,GAAGD,SAAS,CAACE,SAAU;AAE/C;AACA,MAAMC,kBAAkB,GAAG;EACvB;EACAC,OAAO,EAAE,SAAS;EAAY;EAC9BC,YAAY,EAAE,SAAS;EAAO;EAC9BC,WAAW,EAAE,SAAS;EAAQ;EAE9B;EACAC,MAAM,EAAE,SAAS;EAAa;EAC9BC,EAAE,EAAE,SAAS;EAAiB;EAC9BC,SAAS,EAAE,SAAS;EAAU;EAC9BC,MAAM,EAAE,SAAS;EAAa;EAE9B;EACAC,EAAE,EAAE,SAAS;EAAiB;EAC9BC,GAAG,EAAE,SAAS;EAAgB;EAC9BC,GAAG,EAAE,SAAS;EAAgB;EAC9BC,GAAG,EAAE,SAAS;EAAgB;EAE9B;EACAC,MAAM,EAAE,0BAA0B;EAAM;EACxCC,OAAO,EAAE,0BAA0B;EAAK;EACxCC,OAAO,EAAE,0BAA0B;EAAK;EACxCC,OAAO,EAAE,0BAA0B;EAAK;EACxCC,OAAO,EAAE,0BAA0B;EAAK;EAExC;EACAC,OAAO,EAAE,SAAS;EAAY;EAC9BC,YAAY,EAAE,SAAS;EAAO;EAC9BC,OAAO,EAAE,SAAS;EAAY;EAC9BC,YAAY,EAAE,SAAS;EAAO;EAC9BC,MAAM,EAAE,SAAS;EAAa;EAC9BC,WAAW,EAAE,SAAS;EAAQ;EAC9BC,IAAI,EAAE,SAAS;EAAe;EAC9BC,SAAS,EAAE,SAAS;EAAU;EAE9B;EACAC,SAAS,EAAE,0BAA0B;EAErC;EACAC,QAAQ,EAAE,SAAS,CAAW;CACjC;AAED,OAAO,MAAMC,UAAU,GAAG;EACtBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,SAAS;EACf9B,SAAS,EAAE;IACP,GAAGD,kBAAkB;IACrB,GAAGE,kBAAkB;IAErB;IACA8B,WAAW,EAAE;MACTC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAChEC,QAAQ,EAAEhC,kBAAkB,CAACS,GAAG;MAChCwB,OAAO,EAAEjC,kBAAkB,CAACS,GAAG;MAC/ByB,WAAW,EAAElC,kBAAkB,CAACC;KACnC;IAEDkC,KAAK,EAAE;MACHC,YAAY,EAAEpC,kBAAkB,CAACC,OAAO;MACxCoC,aAAa,EAAErC,kBAAkB,CAACE,YAAY;MAC9CoC,WAAW,EAAE,0BAA0B;MACvCC,gBAAgB,EAAEvC,kBAAkB,CAACS,GAAG;MACxC+B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK;KACxB;IAEDC,OAAO,EAAE;MACLC,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCmC,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9C+B,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAE3BC,cAAc,EAAE/C,kBAAkB,CAACyB,SAAS;MAE5CuB,MAAM,EAAEhD,kBAAkB,CAACe,OAAO;MAClCkC,cAAc,EAAE,GAAG;MACnBC,SAAS,EAAElD,kBAAkB,CAACe,OAAO;MACrCoC,eAAe,EAAEnD,kBAAkB,CAACe,OAAO;MAC3CqC,uBAAuB,EAAEpD,kBAAkB,CAACC,OAAO;MACnDoD,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,kBAAkB;MACpCC,QAAQ,EAAEvD,kBAAkB,CAACS,GAAG;MAChC+C,MAAM,EAAExD,kBAAkB,CAACS;KAC9B;IAEDgD,WAAW,EAAE;MACTf,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCkD,gBAAgB,EAAE1D,kBAAkB,CAACI,MAAM;MAC3CuD,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9C+B,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAE3Bc,aAAa,EAAE5D,kBAAkB,CAACc,OAAO;MACzC+C,cAAc,EAAE7D,kBAAkB,CAACK,EAAE;MACrC0C,cAAc,EAAE/C,kBAAkB,CAACyB,SAAS;MAE5C0B,eAAe,EAAEnD,kBAAkB,CAACC,OAAO;MAC3C6D,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAEhE,kBAAkB,CAACC,OAAO;MACxCgE,UAAU,EAAEjE,kBAAkB,CAACC,OAAO;MACtCiE,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAEnE,kBAAkB,CAACS,GAAG;MACpC2D,UAAU,EAAEpE,kBAAkB,CAACS,GAAG;MAClC4C,gBAAgB,EAAE;KACrB;IAEDgB,SAAS,EAAE;MACPC,UAAU,EAAEtE,kBAAkB,CAACI,MAAM;MACrCmE,SAAS,EAAEvE,kBAAkB,CAACW,GAAG;MACjC6D,cAAc,EAAExE,kBAAkB,CAACC,OAAO;MAC1CwE,eAAe,EAAEzE,kBAAkB,CAACgB;KACvC;IAED0D,wBAAwB,EAAE;MACtBC,SAAS,EAAE3E,kBAAkB,CAACI,MAAM;MAEpCwE,sBAAsB,EAAE5E,kBAAkB,CAACC,OAAO;MAClD4E,uBAAuB,EAAE7E,kBAAkB,CAACiB,OAAO;MAEnD6D,qBAAqB,EAAE,GAAG;MAC1BC,mBAAmB,EAAE,GAAG;MACxBC,mBAAmB,EAAEhF,kBAAkB,CAACyB,SAAS;MAEjDoB,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCmC,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9CqE,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;KACpB;IAEDuC,gBAAgB,EAAE;MACdC,YAAY,EAAEpF,kBAAkB,CAACE,YAAY;MAC7CmF,UAAU,EAAErF,kBAAkB,CAACC,OAAO;MACtCqF,MAAM,EAAEtF,kBAAkB,CAACE,YAAY;MACvCqF,UAAU,EAAE,GAAG;MAEfC,aAAa,EAAExF,kBAAkB,CAACI,MAAM;MACxCqF,YAAY,EAAE,IAAI;MAElB/C,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCmC,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9C+B,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE;KACtB;IAED4C,aAAa,EAAE;MACXC,kBAAkB,EAAE3F,kBAAkB,CAACe,OAAO;MAC9C6E,gBAAgB,EAAE5F,kBAAkB,CAACU,GAAG;MACxCmF,kBAAkB,EAAE,GAAG;MACvBC,yBAAyB,EAAE9F,kBAAkB,CAACC,OAAO;MACrD8F,uBAAuB,EAAE/F,kBAAkB,CAACE,YAAY;MACxD8F,yBAAyB,EAAE,GAAG;MAE9BC,kBAAkB,EAAEjG,kBAAkB,CAACe,OAAO;MAC9CmF,kBAAkB,EAAElG,kBAAkB,CAACK,EAAE;MACzC8F,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAEpG,kBAAkB,CAACC,OAAO;MAC3CoG,iBAAiB,EAAErG,kBAAkB,CAACE,YAAY;MAClDoG,kBAAkB,EAAEtG,kBAAkB,CAACyB,SAAS;MAChD8E,oBAAoB,EAAEvG,kBAAkB,CAACE,YAAY;MAErDsG,0BAA0B,EAAExG,kBAAkB,CAACC,OAAO;MAEtDwG,mBAAmB,EAAEzG,kBAAkB,CAACS;KAC3C;IAEDiG,OAAO,EAAE;MACLlG,EAAE,EAAER,kBAAkB,CAACQ,EAAE;MACzBmE,SAAS,EAAE3E,kBAAkB,CAACI,MAAM;MACpCwD,aAAa,EAAE5D,kBAAkB,CAACI,MAAM;MACxCuG,cAAc,EAAE3G,kBAAkB,CAACyB,SAAS;MAC5CmF,oBAAoB,EAAE,0BAA0B;MAChDC,sBAAsB,EAAE7G,kBAAkB,CAACC,OAAO;MAClD6G,WAAW,EAAE;KAChB;IAEDC,OAAO,EAAE;MACLnD,aAAa,EAAE5D,kBAAkB,CAACyB,SAAS;MAC3CkD,SAAS,EAAE3E,kBAAkB,CAACI;KACjC;IAED4G,MAAM,EAAE;MACJtE,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCkD,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9C+B,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAE5D,kBAAkB,CAACe,OAAO;MACzC0E,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAExF,kBAAkB,CAACK,EAAE;MACpC0C,cAAc,EAAE/C,kBAAkB,CAACyB,SAAS;MAE5C0B,eAAe,EAAEnD,kBAAkB,CAACC,OAAO;MAC3C6D,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,GAAG;MAEd;MACAkD,iBAAiB,EAAEjH,kBAAkB,CAACU,GAAG;MACzCwG,eAAe,EAAElH,kBAAkB,CAACU,GAAG;MACvCyG,qBAAqB,EAAE,kBAAkB;MAEzC;MACAC,kBAAkB,EAAEpH,kBAAkB,CAACC,OAAO;MAC9CoH,gBAAgB,EAAErH,kBAAkB,CAACC,OAAO;MAE5CqH,kBAAkB,EAAE,0BAA0B;MAC9CC,gBAAgB,EAAE,wBAAwB;MAC1CC,sBAAsB,EAAE,kBAAkB;MAE1C;MACAC,iBAAiB,EAAEzH,kBAAkB,CAACiB,OAAO;MAC7CyG,eAAe,EAAE1H,kBAAkB,CAACkB,YAAY;MAEhDyG,iBAAiB,EAAE,wBAAwB;MAC3CC,eAAe,EAAE,sBAAsB;MACvCC,qBAAqB,EAAE;KAC1B;IAEDC,MAAM,EAAE;MACJtH,EAAE,EAAER,kBAAkB,CAACQ,EAAE;MACzBmE,SAAS,EAAE3E,kBAAkB,CAACI,MAAM;MACpCwD,aAAa,EAAE5D,kBAAkB,CAACe,OAAO;MACzC4F,cAAc,EAAE3G,kBAAkB,CAACyB,SAAS;MAC5CqF,WAAW,EAAE,GAAG;MAEhBrB,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAExF,kBAAkB,CAACK,EAAE;MAEpC;MACA0H,iBAAiB,EAAE/H,kBAAkB,CAACU,GAAG;MACzCsH,eAAe,EAAEhI,kBAAkB,CAACU,GAAG;MACvCuH,eAAe,EAAE,kBAAkB;MAEnC;MACAb,kBAAkB,EAAEpH,kBAAkB,CAACC,OAAO;MAC9CoH,gBAAgB,EAAErH,kBAAkB,CAACC,OAAO;MAC5CiI,gBAAgB,EAAE,kBAAkB;MAEpC;MACAT,iBAAiB,EAAEzH,kBAAkB,CAACiB,OAAO;MAC7CyG,eAAe,EAAE1H,kBAAkB,CAACiB,OAAO;MAC3CkH,eAAe,EAAE;KACpB;IAEDC,iBAAiB,EAAE;MACfC,SAAS,EAAErI,kBAAkB,CAACiB,OAAO;MACrCqH,UAAU,EAAEtI,kBAAkB,CAACC,OAAO;MACtCsI,SAAS,EAAEvI,kBAAkB,CAACU;KACjC;IAED8H,QAAQ,EAAE;MACN9F,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCkD,gBAAgB,EAAE,kBAAkB;MACpCC,gBAAgB,EAAE,GAAG;MACrBhB,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9C+B,eAAe,EAAE,yCAAyC;MAC1DC,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MAErBrB,aAAa,EAAE5D,kBAAkB,CAACe,OAAO;MACzC0E,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAExF,kBAAkB,CAACK,EAAE;MACpC0C,cAAc,EAAE/C,kBAAkB,CAACyB,SAAS;MAE5C0B,eAAe,EAAEnD,kBAAkB,CAACC,OAAO;MAC3C6D,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE,SAAS;MACvBC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE,kBAAkB;MAE9BC,YAAY,EAAEnE,kBAAkB,CAACC,OAAO;MACxCmE,UAAU,EAAEpE,kBAAkB,CAACE,YAAY;MAE3CuI,cAAc,EAAE,OAAO;MACvBC,cAAc,EAAE,GAAG;MAEnBC,iBAAiB,EAAE3I,kBAAkB,CAACiB,OAAO;MAC7C2H,eAAe,EAAE5I,kBAAkB,CAACiB;KACvC;IAED4H,cAAc,EAAE;MACZC,SAAS,EAAE9I,kBAAkB,CAACiB,OAAO;MACrC8H,UAAU,EAAE/I,kBAAkB,CAACC;KAClC;IAED+I,WAAW,EAAE;MACTC,oBAAoB,EAAEjJ,kBAAkB,CAACiB,OAAO;MAChDiI,qBAAqB,EAAElJ,kBAAkB,CAACiB,OAAO;MACjDkI,mBAAmB,EAAE,kBAAkB;MACvCC,cAAc,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAE9BC,qBAAqB,EAAErJ,kBAAkB,CAACmB,OAAO;MACjDmI,sBAAsB,EAAEtJ,kBAAkB,CAACoB,YAAY;MACvDmI,oBAAoB,EAAE,kBAAkB;MACxCC,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAC/BC,aAAa,EAAE,GAAG;MAClBC,aAAa,EAAE;KAClB;IAEDC,iBAAiB,EAAE;MACfC,YAAY,EAAE5J,kBAAkB,CAACmB,OAAO;MACxC0I,aAAa,EAAE7J,kBAAkB,CAACiB;KACrC;IAED6I,UAAU,EAAE;MACRtH,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACvBuH,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;MAEtBC,QAAQ,EAAE,IAAI;MAEdf,oBAAoB,EAAEjJ,kBAAkB,CAACiB,OAAO;MAChDiI,qBAAqB,EAAElJ,kBAAkB,CAACiB,OAAO;MACjDkI,mBAAmB,EAAE,kBAAkB;MAEvCE,qBAAqB,EAAErJ,kBAAkB,CAACC,OAAO;MACjDqJ,sBAAsB,EAAEtJ,kBAAkB,CAACC,OAAO;MAClDsJ,oBAAoB,EAAE,kBAAkB;MAExCU,oBAAoB,EAAEjK,kBAAkB,CAACmB,OAAO;MAChD+I,qBAAqB,EAAElK,kBAAkB,CAACmB,OAAO;MACjDgJ,mBAAmB,EAAE;KACxB;IAEDC,WAAW,EAAE;MACT7G,QAAQ,EAAEvD,kBAAkB,CAACC,OAAO;MACpCuD,MAAM,EAAExD,kBAAkB,CAACC,OAAO;MAElC4C,gBAAgB,EAAE7C,kBAAkB,CAACI,MAAM;MAC3C0C,iBAAiB,EAAE,QAAQ;MAC3BmC,eAAe,EAAE,IAAI;MACrBvC,SAAS,EAAE1C,kBAAkB,CAACQ,EAAE;MAChCmC,kBAAkB,EAAE3C,kBAAkB,CAACa,OAAO;MAC9CqE,kBAAkB,EAAE,GAAG;MACvBtC,eAAe,EAAE;;;CAGR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}