{"ast": null, "code": "/* tslint:disable */\n/* eslint-disable */\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\nimport { HttpResponse } from '@angular/common/http';\nimport { filter, map } from 'rxjs/operators';\nimport { RequestBuilder } from '../../request-builder';\nexport function apiMaterialExportExcelMaterialListPost$Plain(http, rootUrl, params, context) {\n  const rb = new RequestBuilder(rootUrl, apiMaterialExportExcelMaterialListPost$Plain.PATH, 'post');\n  if (params) {\n    rb.body(params.body, 'application/*+json');\n  }\n  return http.request(rb.build({\n    responseType: 'text',\n    accept: 'text/plain',\n    context\n  })).pipe(filter(r => r instanceof HttpResponse), map(r => {\n    return r;\n  }));\n}\napiMaterialExportExcelMaterialListPost$Plain.PATH = '/api/Material/ExportExcelMaterialList';", "map": {"version": 3, "names": ["HttpResponse", "filter", "map", "RequestBuilder", "apiMaterialExportExcelMaterialListPost$Plain", "http", "rootUrl", "params", "context", "rb", "PATH", "body", "request", "build", "responseType", "accept", "pipe", "r"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\services\\api\\fn\\material\\api-material-export-excel-material-list-post-plain.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\n/* Code generated by ng-openapi-gen DO NOT EDIT. */\r\n\r\nimport { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { filter, map } from 'rxjs/operators';\r\nimport { StrictHttpResponse } from '../../strict-http-response';\r\nimport { RequestBuilder } from '../../request-builder';\r\n\r\nimport { ExportExcelMaterialsResponseBase } from '../../models/export-excel-materials-response-base';\r\n\r\nexport interface ApiMaterialExportExcelMaterialListPost$Plain$Params {\r\n      body?: number\r\n}\r\n\r\nexport function apiMaterialExportExcelMaterialListPost$Plain(http: HttpClient, rootUrl: string, params?: ApiMaterialExportExcelMaterialListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<ExportExcelMaterialsResponseBase>> {\r\n  const rb = new RequestBuilder(rootUrl, apiMaterialExportExcelMaterialListPost$Plain.PATH, 'post');\r\n  if (params) {\r\n    rb.body(params.body, 'application/*+json');\r\n  }\r\n\r\n  return http.request(\r\n    rb.build({ responseType: 'text', accept: 'text/plain', context })\r\n  ).pipe(\r\n    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),\r\n    map((r: HttpResponse<any>) => {\r\n      return r as StrictHttpResponse<ExportExcelMaterialsResponseBase>;\r\n    })\r\n  );\r\n}\r\n\r\napiMaterialExportExcelMaterialListPost$Plain.PATH = '/api/Material/ExportExcelMaterialList';\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,SAAkCA,YAAY,QAAQ,sBAAsB;AAE5E,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAE5C,SAASC,cAAc,QAAQ,uBAAuB;AAQtD,OAAM,SAAUC,4CAA4CA,CAACC,IAAgB,EAAEC,OAAe,EAAEC,MAA4D,EAAEC,OAAqB;EACjL,MAAMC,EAAE,GAAG,IAAIN,cAAc,CAACG,OAAO,EAAEF,4CAA4C,CAACM,IAAI,EAAE,MAAM,CAAC;EACjG,IAAIH,MAAM,EAAE;IACVE,EAAE,CAACE,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE,oBAAoB,CAAC;EAC5C;EAEA,OAAON,IAAI,CAACO,OAAO,CACjBH,EAAE,CAACI,KAAK,CAAC;IAAEC,YAAY,EAAE,MAAM;IAAEC,MAAM,EAAE,YAAY;IAAEP;EAAO,CAAE,CAAC,CAClE,CAACQ,IAAI,CACJf,MAAM,CAAEgB,CAAM,IAA6BA,CAAC,YAAYjB,YAAY,CAAC,EACrEE,GAAG,CAAEe,CAAoB,IAAI;IAC3B,OAAOA,CAAyD;EAClE,CAAC,CAAC,CACH;AACH;AAEAb,4CAA4C,CAACM,IAAI,GAAG,uCAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}