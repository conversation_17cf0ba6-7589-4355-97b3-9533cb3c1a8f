{"ast": null, "code": "export * from './header/header.component';\nexport * from './footer/footer.component';\nexport * from './search-input/search-input.component';\nexport * from './tiny-mce/tiny-mce.component';\nexport * from './image-carousel/image-carousel.component';\nexport * from './image-modal/image-modal.component';\nexport * from './image-gallery/image-gallery.component';", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\@theme\\components\\index.ts"], "sourcesContent": ["export * from './header/header.component';\r\nexport * from './footer/footer.component';\r\nexport * from './search-input/search-input.component';\r\nexport * from './tiny-mce/tiny-mce.component';\r\nexport * from './image-carousel/image-carousel.component';\r\nexport * from './image-modal/image-modal.component';\r\nexport * from './image-gallery/image-gallery.component';\r\n"], "mappings": "AAAA,cAAc,2BAA2B;AACzC,cAAc,2BAA2B;AACzC,cAAc,uCAAuC;AACrD,cAAc,+BAA+B;AAC7C,cAAc,2CAA2C;AACzD,cAAc,qCAAqC;AACnD,cAAc,yCAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}