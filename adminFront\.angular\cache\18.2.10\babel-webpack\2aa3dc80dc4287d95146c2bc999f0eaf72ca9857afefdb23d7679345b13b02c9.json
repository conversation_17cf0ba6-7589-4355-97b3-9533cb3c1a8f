{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFormItemCreateListFormItemPost$Json } from '../fn/form-item/api-form-item-create-list-form-item-post-json';\nimport { apiFormItemCreateListFormItemPost$Plain } from '../fn/form-item/api-form-item-create-list-form-item-post-plain';\nimport { apiFormItemGetListFormItemPost$Json } from '../fn/form-item/api-form-item-get-list-form-item-post-json';\nimport { apiFormItemGetListFormItemPost$Plain } from '../fn/form-item/api-form-item-get-list-form-item-post-plain';\nimport { apiFormItemLockFormItemPost$Json } from '../fn/form-item/api-form-item-lock-form-item-post-json';\nimport { apiFormItemLockFormItemPost$Plain } from '../fn/form-item/api-form-item-lock-form-item-post-plain';\nimport { apiFormItemSaveListFormItemPost$Json } from '../fn/form-item/api-form-item-save-list-form-item-post-json';\nimport { apiFormItemSaveListFormItemPost$Plain } from '../fn/form-item/api-form-item-save-list-form-item-post-plain';\nimport { apiFormItemUnlockFormItemPost$Json } from '../fn/form-item/api-form-item-unlock-form-item-post-json';\nimport { apiFormItemUnlockFormItemPost$Plain } from '../fn/form-item/api-form-item-unlock-form-item-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let FormItemService = /*#__PURE__*/(() => {\n  class FormItemService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiFormItemGetListFormItemPost()` */\n    static {\n      this.ApiFormItemGetListFormItemPostPath = '/api/FormItem/GetListFormItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemGetListFormItemPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemGetListFormItemPost$Plain$Response(params, context) {\n      return apiFormItemGetListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemGetListFormItemPost$Plain(params, context) {\n      return this.apiFormItemGetListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemGetListFormItemPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemGetListFormItemPost$Json$Response(params, context) {\n      return apiFormItemGetListFormItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemGetListFormItemPost$Json(params, context) {\n      return this.apiFormItemGetListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFormItemSaveListFormItemPost()` */\n    static {\n      this.ApiFormItemSaveListFormItemPostPath = '/api/FormItem/SaveListFormItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemSaveListFormItemPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemSaveListFormItemPost$Plain$Response(params, context) {\n      return apiFormItemSaveListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemSaveListFormItemPost$Plain(params, context) {\n      return this.apiFormItemSaveListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemSaveListFormItemPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemSaveListFormItemPost$Json$Response(params, context) {\n      return apiFormItemSaveListFormItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemSaveListFormItemPost$Json(params, context) {\n      return this.apiFormItemSaveListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFormItemLockFormItemPost()` */\n    static {\n      this.ApiFormItemLockFormItemPostPath = '/api/FormItem/LockFormItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemLockFormItemPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemLockFormItemPost$Plain$Response(params, context) {\n      return apiFormItemLockFormItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemLockFormItemPost$Plain(params, context) {\n      return this.apiFormItemLockFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemLockFormItemPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemLockFormItemPost$Json$Response(params, context) {\n      return apiFormItemLockFormItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemLockFormItemPost$Json(params, context) {\n      return this.apiFormItemLockFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFormItemCreateListFormItemPost()` */\n    static {\n      this.ApiFormItemCreateListFormItemPostPath = '/api/FormItem/CreateListFormItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemCreateListFormItemPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemCreateListFormItemPost$Plain$Response(params, context) {\n      return apiFormItemCreateListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemCreateListFormItemPost$Plain(params, context) {\n      return this.apiFormItemCreateListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemCreateListFormItemPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemCreateListFormItemPost$Json$Response(params, context) {\n      return apiFormItemCreateListFormItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemCreateListFormItemPost$Json(params, context) {\n      return this.apiFormItemCreateListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiFormItemUnlockFormItemPost()` */\n    static {\n      this.ApiFormItemUnlockFormItemPostPath = '/api/FormItem/UnlockFormItem';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemUnlockFormItemPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemUnlockFormItemPost$Plain$Response(params, context) {\n      return apiFormItemUnlockFormItemPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemUnlockFormItemPost$Plain(params, context) {\n      return this.apiFormItemUnlockFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFormItemUnlockFormItemPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemUnlockFormItemPost$Json$Response(params, context) {\n      return apiFormItemUnlockFormItemPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiFormItemUnlockFormItemPost$Json(params, context) {\n      return this.apiFormItemUnlockFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function FormItemService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FormItemService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FormItemService,\n        factory: FormItemService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FormItemService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}