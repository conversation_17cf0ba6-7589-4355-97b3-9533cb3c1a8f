{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Json } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-json';\nimport { apiHouseHoldMainAddHouseHoldMainPost$Plain } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class HouseHoldMainService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiHouseHoldMainAddHouseHoldMainPost()` */\n  static {\n    this.ApiHouseHoldMainAddHouseHoldMainPostPath = '/api/HouseHoldMain/AddHouseHoldMain';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context) {\n    return apiHouseHoldMainAddHouseHoldMainPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldMainAddHouseHoldMainPost$Plain(params, context) {\n    return this.apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context) {\n    return apiHouseHoldMainAddHouseHoldMainPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiHouseHoldMainAddHouseHoldMainPost$Json(params, context) {\n    return this.apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function HouseHoldMainService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HouseHoldMainService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HouseHoldMainService,\n      factory: HouseHoldMainService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiHouseHoldMainAddHouseHoldMainPost$Json", "apiHouseHoldMainAddHouseHoldMainPost$Plain", "HouseHoldMainService", "constructor", "config", "http", "ApiHouseHoldMainAddHouseHoldMainPostPath", "apiHouseHoldMainAddHouseHoldMainPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiHouseHoldMainAddHouseHoldMainPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\house-hold-main.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiHouseHoldMainAddHouseHoldMainPost$Json } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-json';\r\nimport { ApiHouseHoldMainAddHouseHoldMainPost$Json$Params } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-json';\r\nimport { apiHouseHoldMainAddHouseHoldMainPost$Plain } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-plain';\r\nimport { ApiHouseHoldMainAddHouseHoldMainPost$Plain$Params } from '../fn/house-hold-main/api-house-hold-main-add-house-hold-main-post-plain';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class HouseHoldMainService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiHouseHoldMainAddHouseHoldMainPost()` */\r\n  static readonly ApiHouseHoldMainAddHouseHoldMainPostPath = '/api/HouseHoldMain/AddHouseHoldMain';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params?: ApiHouseHoldMainAddHouseHoldMainPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseHoldMainAddHouseHoldMainPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldMainAddHouseHoldMainPost$Plain(params?: ApiHouseHoldMainAddHouseHoldMainPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseHoldMainAddHouseHoldMainPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiHouseHoldMainAddHouseHoldMainPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params?: ApiHouseHoldMainAddHouseHoldMainPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiHouseHoldMainAddHouseHoldMainPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiHouseHoldMainAddHouseHoldMainPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiHouseHoldMainAddHouseHoldMainPost$Json(params?: ApiHouseHoldMainAddHouseHoldMainPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiHouseHoldMainAddHouseHoldMainPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,yCAAyC,QAAQ,yEAAyE;AAEnI,SAASC,0CAA0C,QAAQ,0EAA0E;;;;AAKrI,OAAM,MAAOC,oBAAqB,SAAQH,WAAW;EACnDI,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,wCAAwC,GAAG,qCAAqC;EAAC;EAEjG;;;;;;EAMAC,mDAAmDA,CAACC,MAA0D,EAAEC,OAAqB;IACnI,OAAOR,0CAA0C,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC7F;EAEA;;;;;;EAMAR,0CAA0CA,CAACO,MAA0D,EAAEC,OAAqB;IAC1H,OAAO,IAAI,CAACF,mDAAmD,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACnFb,GAAG,CAAEc,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAC,kDAAkDA,CAACN,MAAyD,EAAEC,OAAqB;IACjI,OAAOT,yCAAyC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC5F;EAEA;;;;;;EAMAT,yCAAyCA,CAACQ,MAAyD,EAAEC,OAAqB;IACxH,OAAO,IAAI,CAACK,kDAAkD,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAClFb,GAAG,CAAEc,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCAlDWX,oBAAoB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBlB,oBAAoB;MAAAmB,OAAA,EAApBnB,oBAAoB,CAAAoB,IAAA;MAAAC,UAAA,EADP;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}