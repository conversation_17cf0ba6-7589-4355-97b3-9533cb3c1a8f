{"ast": null, "code": "export default function assign(target, object) {\n  if (target == null) {\n    throw new TypeError('assign requires that input parameter not be null or undefined');\n  }\n  for (var property in object) {\n    if (Object.prototype.hasOwnProperty.call(object, property)) {\n      ;\n      target[property] = object[property];\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["assign", "target", "object", "TypeError", "property", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/date-fns/esm/_lib/assign/index.js"], "sourcesContent": ["export default function assign(target, object) {\n  if (target == null) {\n    throw new TypeError('assign requires that input parameter not be null or undefined');\n  }\n  for (var property in object) {\n    if (Object.prototype.hasOwnProperty.call(object, property)) {\n      ;\n      target[property] = object[property];\n    }\n  }\n  return target;\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC7C,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,SAAS,CAAC,+DAA+D,CAAC;EACtF;EACA,KAAK,IAAIC,QAAQ,IAAIF,MAAM,EAAE;IAC3B,IAAIG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,MAAM,EAAEE,QAAQ,CAAC,EAAE;MAC1D;MACAH,MAAM,CAACG,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC;IACrC;EACF;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}