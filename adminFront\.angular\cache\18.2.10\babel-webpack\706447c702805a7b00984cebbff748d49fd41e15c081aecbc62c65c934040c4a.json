{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiBuildCaseMailDeleteBuildCaseMailPost$Json } from '../fn/build-case-mail/api-build-case-mail-delete-build-case-mail-post-json';\nimport { apiBuildCaseMailDeleteBuildCaseMailPost$Plain } from '../fn/build-case-mail/api-build-case-mail-delete-build-case-mail-post-plain';\nimport { apiBuildCaseMailGetBuildCaseMailListPost$Json } from '../fn/build-case-mail/api-build-case-mail-get-build-case-mail-list-post-json';\nimport { apiBuildCaseMailGetBuildCaseMailListPost$Plain } from '../fn/build-case-mail/api-build-case-mail-get-build-case-mail-list-post-plain';\nimport { apiBuildCaseMailSaveBuildCaseMailPost$Json } from '../fn/build-case-mail/api-build-case-mail-save-build-case-mail-post-json';\nimport { apiBuildCaseMailSaveBuildCaseMailPost$Plain } from '../fn/build-case-mail/api-build-case-mail-save-build-case-mail-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let BuildCaseMailService = /*#__PURE__*/(() => {\n  class BuildCaseMailService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiBuildCaseMailSaveBuildCaseMailPost()` */\n    static {\n      this.ApiBuildCaseMailSaveBuildCaseMailPostPath = '/api/BuildCaseMail/SaveBuildCaseMail';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailSaveBuildCaseMailPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailSaveBuildCaseMailPost$Plain$Response(params, context) {\n      return apiBuildCaseMailSaveBuildCaseMailPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailSaveBuildCaseMailPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailSaveBuildCaseMailPost$Plain(params, context) {\n      return this.apiBuildCaseMailSaveBuildCaseMailPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailSaveBuildCaseMailPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailSaveBuildCaseMailPost$Json$Response(params, context) {\n      return apiBuildCaseMailSaveBuildCaseMailPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailSaveBuildCaseMailPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailSaveBuildCaseMailPost$Json(params, context) {\n      return this.apiBuildCaseMailSaveBuildCaseMailPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseMailGetBuildCaseMailListPost()` */\n    static {\n      this.ApiBuildCaseMailGetBuildCaseMailListPostPath = '/api/BuildCaseMail/GetBuildCaseMailList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailGetBuildCaseMailListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailGetBuildCaseMailListPost$Plain$Response(params, context) {\n      return apiBuildCaseMailGetBuildCaseMailListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailGetBuildCaseMailListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailGetBuildCaseMailListPost$Plain(params, context) {\n      return this.apiBuildCaseMailGetBuildCaseMailListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailGetBuildCaseMailListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailGetBuildCaseMailListPost$Json$Response(params, context) {\n      return apiBuildCaseMailGetBuildCaseMailListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailGetBuildCaseMailListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailGetBuildCaseMailListPost$Json(params, context) {\n      return this.apiBuildCaseMailGetBuildCaseMailListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiBuildCaseMailDeleteBuildCaseMailPost()` */\n    static {\n      this.ApiBuildCaseMailDeleteBuildCaseMailPostPath = '/api/BuildCaseMail/DeleteBuildCaseMail';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailDeleteBuildCaseMailPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailDeleteBuildCaseMailPost$Plain$Response(params, context) {\n      return apiBuildCaseMailDeleteBuildCaseMailPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailDeleteBuildCaseMailPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailDeleteBuildCaseMailPost$Plain(params, context) {\n      return this.apiBuildCaseMailDeleteBuildCaseMailPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiBuildCaseMailDeleteBuildCaseMailPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailDeleteBuildCaseMailPost$Json$Response(params, context) {\n      return apiBuildCaseMailDeleteBuildCaseMailPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiBuildCaseMailDeleteBuildCaseMailPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiBuildCaseMailDeleteBuildCaseMailPost$Json(params, context) {\n      return this.apiBuildCaseMailDeleteBuildCaseMailPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function BuildCaseMailService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || BuildCaseMailService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: BuildCaseMailService,\n        factory: BuildCaseMailService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return BuildCaseMailService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}