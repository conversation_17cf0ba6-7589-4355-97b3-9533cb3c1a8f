{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Hebrew [he]\n//! author : <PERSON><PERSON> : https://github.com/tomer\n//! author : <PERSON><PERSON> : https://github.com/DevelopmentIL\n//! author : Tal Ater : https://github.com/TalAter\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var he = moment.defineLocale('he', {\n    months: 'ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר'.split('_'),\n    monthsShort: 'ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳'.split('_'),\n    weekdays: 'ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת'.split('_'),\n    weekdaysShort: 'א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳'.split('_'),\n    weekdaysMin: 'א_ב_ג_ד_ה_ו_ש'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D [ב]MMMM YYYY',\n      LLL: 'D [ב]MMMM YYYY HH:mm',\n      LLLL: 'dddd, D [ב]MMMM YYYY HH:mm',\n      l: 'D/M/YYYY',\n      ll: 'D MMM YYYY',\n      lll: 'D MMM YYYY HH:mm',\n      llll: 'ddd, D MMM YYYY HH:mm'\n    },\n    calendar: {\n      sameDay: '[היום ב־]LT',\n      nextDay: '[מחר ב־]LT',\n      nextWeek: 'dddd [בשעה] LT',\n      lastDay: '[אתמול ב־]LT',\n      lastWeek: '[ביום] dddd [האחרון בשעה] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'בעוד %s',\n      past: 'לפני %s',\n      s: 'מספר שניות',\n      ss: '%d שניות',\n      m: 'דקה',\n      mm: '%d דקות',\n      h: 'שעה',\n      hh: function (number) {\n        if (number === 2) {\n          return 'שעתיים';\n        }\n        return number + ' שעות';\n      },\n      d: 'יום',\n      dd: function (number) {\n        if (number === 2) {\n          return 'יומיים';\n        }\n        return number + ' ימים';\n      },\n      M: 'חודש',\n      MM: function (number) {\n        if (number === 2) {\n          return 'חודשיים';\n        }\n        return number + ' חודשים';\n      },\n      y: 'שנה',\n      yy: function (number) {\n        if (number === 2) {\n          return 'שנתיים';\n        } else if (number % 10 === 0 && number !== 10) {\n          return number + ' שנה';\n        }\n        return number + ' שנים';\n      }\n    },\n    meridiemParse: /אחה\"צ|לפנה\"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,\n    isPM: function (input) {\n      return /^(אחה\"צ|אחרי הצהריים|בערב)$/.test(input);\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 5) {\n        return 'לפנות בוקר';\n      } else if (hour < 10) {\n        return 'בבוקר';\n      } else if (hour < 12) {\n        return isLower ? 'לפנה\"צ' : 'לפני הצהריים';\n      } else if (hour < 18) {\n        return isLower ? 'אחה\"צ' : 'אחרי הצהריים';\n      } else {\n        return 'בערב';\n      }\n    }\n  });\n  return he;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "he", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "number", "d", "dd", "M", "MM", "y", "yy", "meridiemParse", "isPM", "input", "test", "meridiem", "hour", "minute", "isLower"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/moment/locale/he.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Hebrew [he]\n//! author : <PERSON><PERSON> : https://github.com/tomer\n//! author : <PERSON><PERSON> : https://github.com/DevelopmentIL\n//! author : Tal Ater : https://github.com/TalAter\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var he = moment.defineLocale('he', {\n        months: 'ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר'.split(\n            '_'\n        ),\n        monthsShort:\n            'ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳'.split('_'),\n        weekdays: 'ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת'.split('_'),\n        weekdaysShort: 'א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳'.split('_'),\n        weekdaysMin: 'א_ב_ג_ד_ה_ו_ש'.split('_'),\n        longDateFormat: {\n            LT: 'HH:mm',\n            LTS: 'HH:mm:ss',\n            L: 'DD/MM/YYYY',\n            LL: 'D [ב]MMMM YYYY',\n            LLL: 'D [ב]MMMM YYYY HH:mm',\n            LLLL: 'dddd, D [ב]MMMM YYYY HH:mm',\n            l: 'D/M/YYYY',\n            ll: 'D MMM YYYY',\n            lll: 'D MMM YYYY HH:mm',\n            llll: 'ddd, D MMM YYYY HH:mm',\n        },\n        calendar: {\n            sameDay: '[היום ב־]LT',\n            nextDay: '[מחר ב־]LT',\n            nextWeek: 'dddd [בשעה] LT',\n            lastDay: '[אתמול ב־]LT',\n            lastWeek: '[ביום] dddd [האחרון בשעה] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'בעוד %s',\n            past: 'לפני %s',\n            s: 'מספר שניות',\n            ss: '%d שניות',\n            m: 'דקה',\n            mm: '%d דקות',\n            h: 'שעה',\n            hh: function (number) {\n                if (number === 2) {\n                    return 'שעתיים';\n                }\n                return number + ' שעות';\n            },\n            d: 'יום',\n            dd: function (number) {\n                if (number === 2) {\n                    return 'יומיים';\n                }\n                return number + ' ימים';\n            },\n            M: 'חודש',\n            MM: function (number) {\n                if (number === 2) {\n                    return 'חודשיים';\n                }\n                return number + ' חודשים';\n            },\n            y: 'שנה',\n            yy: function (number) {\n                if (number === 2) {\n                    return 'שנתיים';\n                } else if (number % 10 === 0 && number !== 10) {\n                    return number + ' שנה';\n                }\n                return number + ' שנים';\n            },\n        },\n        meridiemParse:\n            /אחה\"צ|לפנה\"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,\n        isPM: function (input) {\n            return /^(אחה\"צ|אחרי הצהריים|בערב)$/.test(input);\n        },\n        meridiem: function (hour, minute, isLower) {\n            if (hour < 5) {\n                return 'לפנות בוקר';\n            } else if (hour < 10) {\n                return 'בבוקר';\n            } else if (hour < 12) {\n                return isLower ? 'לפנה\"צ' : 'לפני הצהריים';\n            } else if (hour < 18) {\n                return isLower ? 'אחה\"צ' : 'אחרי הצהריים';\n            } else {\n                return 'בערב';\n            }\n        },\n    });\n\n    return he;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,yEAAyE,CAACC,KAAK,CACnF,GACJ,CAAC;IACDC,WAAW,EACP,2DAA2D,CAACD,KAAK,CAAC,GAAG,CAAC;IAC1EE,QAAQ,EAAE,sCAAsC,CAACF,KAAK,CAAC,GAAG,CAAC;IAC3DG,aAAa,EAAE,sBAAsB,CAACH,KAAK,CAAC,GAAG,CAAC;IAChDI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,gBAAgB;MACpBC,GAAG,EAAE,sBAAsB;MAC3BC,IAAI,EAAE,4BAA4B;MAClCC,CAAC,EAAE,UAAU;MACbC,EAAE,EAAE,YAAY;MAChBC,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,YAAY;MACrBC,QAAQ,EAAE,gBAAgB;MAC1BC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,8BAA8B;MACxCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,SAAS;MACjBC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,UAAU;MACdC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAS;MACbC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAClB,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,OAAO,QAAQ;QACnB;QACA,OAAOA,MAAM,GAAG,OAAO;MAC3B,CAAC;MACDC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAAA,CAAUF,MAAM,EAAE;QAClB,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,OAAO,QAAQ;QACnB;QACA,OAAOA,MAAM,GAAG,OAAO;MAC3B,CAAC;MACDG,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,SAAAA,CAAUJ,MAAM,EAAE;QAClB,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,OAAO,SAAS;QACpB;QACA,OAAOA,MAAM,GAAG,SAAS;MAC7B,CAAC;MACDK,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,SAAAA,CAAUN,MAAM,EAAE;QAClB,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,OAAO,QAAQ;QACnB,CAAC,MAAM,IAAIA,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,KAAK,EAAE,EAAE;UAC3C,OAAOA,MAAM,GAAG,MAAM;QAC1B;QACA,OAAOA,MAAM,GAAG,OAAO;MAC3B;IACJ,CAAC;IACDO,aAAa,EACT,+DAA+D;IACnEC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,6BAA6B,CAACC,IAAI,CAACD,KAAK,CAAC;IACpD,CAAC;IACDE,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,IAAIF,IAAI,GAAG,CAAC,EAAE;QACV,OAAO,YAAY;MACvB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAO,OAAO;MAClB,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,EAAE;QAClB,OAAOE,OAAO,GAAG,QAAQ,GAAG,cAAc;MAC9C,CAAC,MAAM,IAAIF,IAAI,GAAG,EAAE,EAAE;QAClB,OAAOE,OAAO,GAAG,OAAO,GAAG,cAAc;MAC7C,CAAC,MAAM;QACH,OAAO,MAAM;MACjB;IACJ;EACJ,CAAC,CAAC;EAEF,OAAOjD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}