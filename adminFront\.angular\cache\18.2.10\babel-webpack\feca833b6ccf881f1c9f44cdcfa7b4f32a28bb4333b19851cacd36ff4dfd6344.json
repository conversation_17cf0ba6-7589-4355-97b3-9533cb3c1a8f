{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFormItemCreateListFormItemPost$Json } from '../fn/form-item/api-form-item-create-list-form-item-post-json';\nimport { apiFormItemCreateListFormItemPost$Plain } from '../fn/form-item/api-form-item-create-list-form-item-post-plain';\nimport { apiFormItemGetListFormItemPost$Json } from '../fn/form-item/api-form-item-get-list-form-item-post-json';\nimport { apiFormItemGetListFormItemPost$Plain } from '../fn/form-item/api-form-item-get-list-form-item-post-plain';\nimport { apiFormItemLockFormItemPost$Json } from '../fn/form-item/api-form-item-lock-form-item-post-json';\nimport { apiFormItemLockFormItemPost$Plain } from '../fn/form-item/api-form-item-lock-form-item-post-plain';\nimport { apiFormItemSaveListFormItemPost$Json } from '../fn/form-item/api-form-item-save-list-form-item-post-json';\nimport { apiFormItemSaveListFormItemPost$Plain } from '../fn/form-item/api-form-item-save-list-form-item-post-plain';\nimport { apiFormItemUnlockFormItemPost$Json } from '../fn/form-item/api-form-item-unlock-form-item-post-json';\nimport { apiFormItemUnlockFormItemPost$Plain } from '../fn/form-item/api-form-item-unlock-form-item-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class FormItemService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiFormItemGetListFormItemPost()` */\n  static {\n    this.ApiFormItemGetListFormItemPostPath = '/api/FormItem/GetListFormItem';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemGetListFormItemPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemGetListFormItemPost$Plain$Response(params, context) {\n    return apiFormItemGetListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemGetListFormItemPost$Plain(params, context) {\n    return this.apiFormItemGetListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemGetListFormItemPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemGetListFormItemPost$Json$Response(params, context) {\n    return apiFormItemGetListFormItemPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemGetListFormItemPost$Json(params, context) {\n    return this.apiFormItemGetListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFormItemSaveListFormItemPost()` */\n  static {\n    this.ApiFormItemSaveListFormItemPostPath = '/api/FormItem/SaveListFormItem';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemSaveListFormItemPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemSaveListFormItemPost$Plain$Response(params, context) {\n    return apiFormItemSaveListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemSaveListFormItemPost$Plain(params, context) {\n    return this.apiFormItemSaveListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemSaveListFormItemPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemSaveListFormItemPost$Json$Response(params, context) {\n    return apiFormItemSaveListFormItemPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemSaveListFormItemPost$Json(params, context) {\n    return this.apiFormItemSaveListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFormItemLockFormItemPost()` */\n  static {\n    this.ApiFormItemLockFormItemPostPath = '/api/FormItem/LockFormItem';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemLockFormItemPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemLockFormItemPost$Plain$Response(params, context) {\n    return apiFormItemLockFormItemPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemLockFormItemPost$Plain(params, context) {\n    return this.apiFormItemLockFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemLockFormItemPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemLockFormItemPost$Json$Response(params, context) {\n    return apiFormItemLockFormItemPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemLockFormItemPost$Json(params, context) {\n    return this.apiFormItemLockFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFormItemCreateListFormItemPost()` */\n  static {\n    this.ApiFormItemCreateListFormItemPostPath = '/api/FormItem/CreateListFormItem';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemCreateListFormItemPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemCreateListFormItemPost$Plain$Response(params, context) {\n    return apiFormItemCreateListFormItemPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemCreateListFormItemPost$Plain(params, context) {\n    return this.apiFormItemCreateListFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemCreateListFormItemPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemCreateListFormItemPost$Json$Response(params, context) {\n    return apiFormItemCreateListFormItemPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemCreateListFormItemPost$Json(params, context) {\n    return this.apiFormItemCreateListFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  /** Path part for operation `apiFormItemUnlockFormItemPost()` */\n  static {\n    this.ApiFormItemUnlockFormItemPostPath = '/api/FormItem/UnlockFormItem';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemUnlockFormItemPost$Plain()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemUnlockFormItemPost$Plain$Response(params, context) {\n    return apiFormItemUnlockFormItemPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Plain$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemUnlockFormItemPost$Plain(params, context) {\n    return this.apiFormItemUnlockFormItemPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiFormItemUnlockFormItemPost$Json()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemUnlockFormItemPost$Json$Response(params, context) {\n    return apiFormItemUnlockFormItemPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Json$Response()` instead.\n   *\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\n   */\n  apiFormItemUnlockFormItemPost$Json(params, context) {\n    return this.apiFormItemUnlockFormItemPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function FormItemService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FormItemService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FormItemService,\n      factory: FormItemService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiFormItemCreateListFormItemPost$Json", "apiFormItemCreateListFormItemPost$Plain", "apiFormItemGetListFormItemPost$Json", "apiFormItemGetListFormItemPost$Plain", "apiFormItemLockFormItemPost$Json", "apiFormItemLockFormItemPost$Plain", "apiFormItemSaveListFormItemPost$Json", "apiFormItemSaveListFormItemPost$Plain", "apiFormItemUnlockFormItemPost$Json", "apiFormItemUnlockFormItemPost$Plain", "FormItemService", "constructor", "config", "http", "ApiFormItemGetListFormItemPostPath", "apiFormItemGetListFormItemPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiFormItemGetListFormItemPost$Json$Response", "ApiFormItemSaveListFormItemPostPath", "apiFormItemSaveListFormItemPost$Plain$Response", "apiFormItemSaveListFormItemPost$Json$Response", "ApiFormItemLockFormItemPostPath", "apiFormItemLockFormItemPost$Plain$Response", "apiFormItemLockFormItemPost$Json$Response", "ApiFormItemCreateListFormItemPostPath", "apiFormItemCreateListFormItemPost$Plain$Response", "apiFormItemCreateListFormItemPost$Json$Response", "ApiFormItemUnlockFormItemPostPath", "apiFormItemUnlockFormItemPost$Plain$Response", "apiFormItemUnlockFormItemPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\form-item.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiFormItemCreateListFormItemPost$Json } from '../fn/form-item/api-form-item-create-list-form-item-post-json';\r\nimport { ApiFormItemCreateListFormItemPost$Json$Params } from '../fn/form-item/api-form-item-create-list-form-item-post-json';\r\nimport { apiFormItemCreateListFormItemPost$Plain } from '../fn/form-item/api-form-item-create-list-form-item-post-plain';\r\nimport { ApiFormItemCreateListFormItemPost$Plain$Params } from '../fn/form-item/api-form-item-create-list-form-item-post-plain';\r\nimport { apiFormItemGetListFormItemPost$Json } from '../fn/form-item/api-form-item-get-list-form-item-post-json';\r\nimport { ApiFormItemGetListFormItemPost$Json$Params } from '../fn/form-item/api-form-item-get-list-form-item-post-json';\r\nimport { apiFormItemGetListFormItemPost$Plain } from '../fn/form-item/api-form-item-get-list-form-item-post-plain';\r\nimport { ApiFormItemGetListFormItemPost$Plain$Params } from '../fn/form-item/api-form-item-get-list-form-item-post-plain';\r\nimport { apiFormItemLockFormItemPost$Json } from '../fn/form-item/api-form-item-lock-form-item-post-json';\r\nimport { ApiFormItemLockFormItemPost$Json$Params } from '../fn/form-item/api-form-item-lock-form-item-post-json';\r\nimport { apiFormItemLockFormItemPost$Plain } from '../fn/form-item/api-form-item-lock-form-item-post-plain';\r\nimport { ApiFormItemLockFormItemPost$Plain$Params } from '../fn/form-item/api-form-item-lock-form-item-post-plain';\r\nimport { apiFormItemSaveListFormItemPost$Json } from '../fn/form-item/api-form-item-save-list-form-item-post-json';\r\nimport { ApiFormItemSaveListFormItemPost$Json$Params } from '../fn/form-item/api-form-item-save-list-form-item-post-json';\r\nimport { apiFormItemSaveListFormItemPost$Plain } from '../fn/form-item/api-form-item-save-list-form-item-post-plain';\r\nimport { ApiFormItemSaveListFormItemPost$Plain$Params } from '../fn/form-item/api-form-item-save-list-form-item-post-plain';\r\nimport { apiFormItemUnlockFormItemPost$Json } from '../fn/form-item/api-form-item-unlock-form-item-post-json';\r\nimport { ApiFormItemUnlockFormItemPost$Json$Params } from '../fn/form-item/api-form-item-unlock-form-item-post-json';\r\nimport { apiFormItemUnlockFormItemPost$Plain } from '../fn/form-item/api-form-item-unlock-form-item-post-plain';\r\nimport { ApiFormItemUnlockFormItemPost$Plain$Params } from '../fn/form-item/api-form-item-unlock-form-item-post-plain';\r\nimport { GetListFormItemResResponseBase } from '../models/get-list-form-item-res-response-base';\r\nimport { StringResponseBase } from '../models/string-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class FormItemService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiFormItemGetListFormItemPost()` */\r\n  static readonly ApiFormItemGetListFormItemPostPath = '/api/FormItem/GetListFormItem';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemGetListFormItemPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemGetListFormItemPost$Plain$Response(params?: ApiFormItemGetListFormItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListFormItemResResponseBase>> {\r\n    return apiFormItemGetListFormItemPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemGetListFormItemPost$Plain(params?: ApiFormItemGetListFormItemPost$Plain$Params, context?: HttpContext): Observable<GetListFormItemResResponseBase> {\r\n    return this.apiFormItemGetListFormItemPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListFormItemResResponseBase>): GetListFormItemResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemGetListFormItemPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemGetListFormItemPost$Json$Response(params?: ApiFormItemGetListFormItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<GetListFormItemResResponseBase>> {\r\n    return apiFormItemGetListFormItemPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemGetListFormItemPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemGetListFormItemPost$Json(params?: ApiFormItemGetListFormItemPost$Json$Params, context?: HttpContext): Observable<GetListFormItemResResponseBase> {\r\n    return this.apiFormItemGetListFormItemPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<GetListFormItemResResponseBase>): GetListFormItemResResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFormItemSaveListFormItemPost()` */\r\n  static readonly ApiFormItemSaveListFormItemPostPath = '/api/FormItem/SaveListFormItem';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemSaveListFormItemPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemSaveListFormItemPost$Plain$Response(params?: ApiFormItemSaveListFormItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemSaveListFormItemPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemSaveListFormItemPost$Plain(params?: ApiFormItemSaveListFormItemPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemSaveListFormItemPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemSaveListFormItemPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemSaveListFormItemPost$Json$Response(params?: ApiFormItemSaveListFormItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemSaveListFormItemPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemSaveListFormItemPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemSaveListFormItemPost$Json(params?: ApiFormItemSaveListFormItemPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemSaveListFormItemPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFormItemLockFormItemPost()` */\r\n  static readonly ApiFormItemLockFormItemPostPath = '/api/FormItem/LockFormItem';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemLockFormItemPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemLockFormItemPost$Plain$Response(params?: ApiFormItemLockFormItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemLockFormItemPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemLockFormItemPost$Plain(params?: ApiFormItemLockFormItemPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemLockFormItemPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemLockFormItemPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemLockFormItemPost$Json$Response(params?: ApiFormItemLockFormItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemLockFormItemPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemLockFormItemPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemLockFormItemPost$Json(params?: ApiFormItemLockFormItemPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemLockFormItemPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFormItemCreateListFormItemPost()` */\r\n  static readonly ApiFormItemCreateListFormItemPostPath = '/api/FormItem/CreateListFormItem';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemCreateListFormItemPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemCreateListFormItemPost$Plain$Response(params?: ApiFormItemCreateListFormItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemCreateListFormItemPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemCreateListFormItemPost$Plain(params?: ApiFormItemCreateListFormItemPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemCreateListFormItemPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemCreateListFormItemPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemCreateListFormItemPost$Json$Response(params?: ApiFormItemCreateListFormItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemCreateListFormItemPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemCreateListFormItemPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemCreateListFormItemPost$Json(params?: ApiFormItemCreateListFormItemPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemCreateListFormItemPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /** Path part for operation `apiFormItemUnlockFormItemPost()` */\r\n  static readonly ApiFormItemUnlockFormItemPostPath = '/api/FormItem/UnlockFormItem';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemUnlockFormItemPost$Plain()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemUnlockFormItemPost$Plain$Response(params?: ApiFormItemUnlockFormItemPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemUnlockFormItemPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Plain$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemUnlockFormItemPost$Plain(params?: ApiFormItemUnlockFormItemPost$Plain$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemUnlockFormItemPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiFormItemUnlockFormItemPost$Json()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemUnlockFormItemPost$Json$Response(params?: ApiFormItemUnlockFormItemPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<StringResponseBase>> {\r\n    return apiFormItemUnlockFormItemPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiFormItemUnlockFormItemPost$Json$Response()` instead.\r\n   *\r\n   * This method sends `application/*+json` and handles request body of type `application/*+json`.\r\n   */\r\n  apiFormItemUnlockFormItemPost$Json(params?: ApiFormItemUnlockFormItemPost$Json$Params, context?: HttpContext): Observable<StringResponseBase> {\r\n    return this.apiFormItemUnlockFormItemPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<StringResponseBase>): StringResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,sCAAsC,QAAQ,+DAA+D;AAEtH,SAASC,uCAAuC,QAAQ,gEAAgE;AAExH,SAASC,mCAAmC,QAAQ,4DAA4D;AAEhH,SAASC,oCAAoC,QAAQ,6DAA6D;AAElH,SAASC,gCAAgC,QAAQ,wDAAwD;AAEzG,SAASC,iCAAiC,QAAQ,yDAAyD;AAE3G,SAASC,oCAAoC,QAAQ,6DAA6D;AAElH,SAASC,qCAAqC,QAAQ,8DAA8D;AAEpH,SAASC,kCAAkC,QAAQ,0DAA0D;AAE7G,SAASC,mCAAmC,QAAQ,2DAA2D;;;;AAM/G,OAAM,MAAOC,eAAgB,SAAQX,WAAW;EAC9CY,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,kCAAkC,GAAG,+BAA+B;EAAC;EAErF;;;;;;EAMAC,6CAA6CA,CAACC,MAAoD,EAAEC,OAAqB;IACvH,OAAOd,oCAAoC,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAd,oCAAoCA,CAACa,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACF,6CAA6C,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7ErB,GAAG,CAAEsB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;;;;;EAMAC,4CAA4CA,CAACN,MAAmD,EAAEC,OAAqB;IACrH,OAAOf,mCAAmC,CAAC,IAAI,CAACW,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAf,mCAAmCA,CAACc,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACK,4CAA4C,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5ErB,GAAG,CAAEsB,CAAqD,IAAqCA,CAAC,CAACC,IAAI,CAAC,CACvG;EACH;EAEA;;IACgB,KAAAE,mCAAmC,GAAG,gCAAgC;EAAC;EAEvF;;;;;;EAMAC,8CAA8CA,CAACR,MAAqD,EAAEC,OAAqB;IACzH,OAAOV,qCAAqC,CAAC,IAAI,CAACM,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACxF;EAEA;;;;;;EAMAV,qCAAqCA,CAACS,MAAqD,EAAEC,OAAqB;IAChH,OAAO,IAAI,CAACO,8CAA8C,CAACR,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC9ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAI,6CAA6CA,CAACT,MAAoD,EAAEC,OAAqB;IACvH,OAAOX,oCAAoC,CAAC,IAAI,CAACO,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACvF;EAEA;;;;;;EAMAX,oCAAoCA,CAACU,MAAoD,EAAEC,OAAqB;IAC9G,OAAO,IAAI,CAACQ,6CAA6C,CAACT,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC7ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAK,+BAA+B,GAAG,4BAA4B;EAAC;EAE/E;;;;;;EAMAC,0CAA0CA,CAACX,MAAiD,EAAEC,OAAqB;IACjH,OAAOZ,iCAAiC,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACpF;EAEA;;;;;;EAMAZ,iCAAiCA,CAACW,MAAiD,EAAEC,OAAqB;IACxG,OAAO,IAAI,CAACU,0CAA0C,CAACX,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC1ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAO,yCAAyCA,CAACZ,MAAgD,EAAEC,OAAqB;IAC/G,OAAOb,gCAAgC,CAAC,IAAI,CAACS,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACnF;EAEA;;;;;;EAMAb,gCAAgCA,CAACY,MAAgD,EAAEC,OAAqB;IACtG,OAAO,IAAI,CAACW,yCAAyC,CAACZ,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACzErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAQ,qCAAqC,GAAG,kCAAkC;EAAC;EAE3F;;;;;;EAMAC,gDAAgDA,CAACd,MAAuD,EAAEC,OAAqB;IAC7H,OAAOhB,uCAAuC,CAAC,IAAI,CAACY,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC1F;EAEA;;;;;;EAMAhB,uCAAuCA,CAACe,MAAuD,EAAEC,OAAqB;IACpH,OAAO,IAAI,CAACa,gDAAgD,CAACd,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAChFrB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAU,+CAA+CA,CAACf,MAAsD,EAAEC,OAAqB;IAC3H,OAAOjB,sCAAsC,CAAC,IAAI,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACzF;EAEA;;;;;;EAMAjB,sCAAsCA,CAACgB,MAAsD,EAAEC,OAAqB;IAClH,OAAO,IAAI,CAACc,+CAA+C,CAACf,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC/ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;IACgB,KAAAW,iCAAiC,GAAG,8BAA8B;EAAC;EAEnF;;;;;;EAMAC,4CAA4CA,CAACjB,MAAmD,EAAEC,OAAqB;IACrH,OAAOR,mCAAmC,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACtF;EAEA;;;;;;EAMAR,mCAAmCA,CAACO,MAAmD,EAAEC,OAAqB;IAC5G,OAAO,IAAI,CAACgB,4CAA4C,CAACjB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC5ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;EAEA;;;;;;EAMAa,2CAA2CA,CAAClB,MAAkD,EAAEC,OAAqB;IACnH,OAAOT,kCAAkC,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EACrF;EAEA;;;;;;EAMAT,kCAAkCA,CAACQ,MAAkD,EAAEC,OAAqB;IAC1G,OAAO,IAAI,CAACiB,2CAA2C,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CAC3ErB,GAAG,CAAEsB,CAAyC,IAAyBA,CAAC,CAACC,IAAI,CAAC,CAC/E;EACH;;;uCA9OWX,eAAe,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf9B,eAAe;MAAA+B,OAAA,EAAf/B,eAAe,CAAAgC,IAAA;MAAAC,UAAA,EADF;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}