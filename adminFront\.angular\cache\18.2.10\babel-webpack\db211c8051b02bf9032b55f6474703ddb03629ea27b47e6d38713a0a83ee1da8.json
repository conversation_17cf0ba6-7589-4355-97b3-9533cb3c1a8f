{"ast": null, "code": "import { of as observableOf } from 'rxjs';\nimport { CountryOrderData } from '../data/country-order';\nimport * as i0 from \"@angular/core\";\nexport let CountryOrderService = /*#__PURE__*/(() => {\n  class CountryOrderService extends CountryOrderData {\n    constructor() {\n      super(...arguments);\n      this.countriesCategories = ['Sofas', 'Furniture', 'Lighting', 'Tables', 'Textiles'];\n      this.countriesCategoriesLength = this.countriesCategories.length;\n    }\n    generateRandomData(nPoints) {\n      return Array.from(Array(nPoints)).map(() => {\n        return Math.round(Math.random() * 20);\n      });\n    }\n    getCountriesCategories() {\n      return observableOf(this.countriesCategories);\n    }\n    getCountriesCategoriesData(country) {\n      return observableOf(this.generateRandomData(this.countriesCategoriesLength));\n    }\n    static {\n      this.ɵfac = /*@__PURE__*/(() => {\n        let ɵCountryOrderService_BaseFactory;\n        return function CountryOrderService_Factory(__ngFactoryType__) {\n          return (ɵCountryOrderService_BaseFactory || (ɵCountryOrderService_BaseFactory = i0.ɵɵgetInheritedFactory(CountryOrderService)))(__ngFactoryType__ || CountryOrderService);\n        };\n      })();\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: CountryOrderService,\n        factory: CountryOrderService.ɵfac\n      });\n    }\n  }\n  return CountryOrderService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}