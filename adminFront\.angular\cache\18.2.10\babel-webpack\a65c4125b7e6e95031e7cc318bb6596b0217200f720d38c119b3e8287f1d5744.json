{"ast": null, "code": "import isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isSaturday from \"../isSaturday/index.js\";\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of business days to be added. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the business days added\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport default function addBusinessDays(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var startedOnWeekend = isWeekend(date);\n  var amount = toInteger(dirtyAmount);\n  if (isNaN(amount)) return new Date(NaN);\n  var hours = date.getHours();\n  var sign = amount < 0 ? -1 : 1;\n  var fullWeeks = toInteger(amount / 5);\n  date.setDate(date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  var restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    date.setDate(date.getDate() + sign);\n    if (!isWeekend(date)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(date) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(date)) date.setDate(date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(date)) date.setDate(date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  date.setHours(hours);\n  return date;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}