{"ast": null, "code": "export class StatsBarData {}", "map": {"version": 3, "names": ["StatsBarData"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\app\\@core\\data\\stats-bar.ts"], "sourcesContent": ["import { Observable } from 'rxjs';\r\n\r\nexport abstract class StatsBarData {\r\n  abstract getStatsBarData(): Observable<number[]>;\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAgBA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}