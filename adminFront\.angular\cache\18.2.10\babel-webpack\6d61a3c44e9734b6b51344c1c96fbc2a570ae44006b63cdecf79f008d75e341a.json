{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiHouseAgreeDisclaimerPost$Json } from '../fn/house/api-house-agree-disclaimer-post-json';\nimport { apiHouseAgreeDisclaimerPost$Plain } from '../fn/house/api-house-agree-disclaimer-post-plain';\nimport { apiHouseCancelChangePreOrderPost$Json } from '../fn/house/api-house-cancel-change-pre-order-post-json';\nimport { apiHouseCancelChangePreOrderPost$Plain } from '../fn/house/api-house-cancel-change-pre-order-post-plain';\nimport { apiHouseChangePreOrderPost$Json } from '../fn/house/api-house-change-pre-order-post-json';\nimport { apiHouseChangePreOrderPost$Plain } from '../fn/house/api-house-change-pre-order-post-plain';\nimport { apiHouseCheckIsReviewPost$Json } from '../fn/house/api-house-check-is-review-post-json';\nimport { apiHouseCheckIsReviewPost$Plain } from '../fn/house/api-house-check-is-review-post-plain';\nimport { apiHouseCreateAppointmentPost$Json } from '../fn/house/api-house-create-appointment-post-json';\nimport { apiHouseCreateAppointmentPost$Plain } from '../fn/house/api-house-create-appointment-post-plain';\nimport { apiHouseDeleteAppointmentPost$Json } from '../fn/house/api-house-delete-appointment-post-json';\nimport { apiHouseDeleteAppointmentPost$Plain } from '../fn/house/api-house-delete-appointment-post-plain';\nimport { apiHouseDeleteRegularPicturePost$Json } from '../fn/house/api-house-delete-regular-picture-post-json';\nimport { apiHouseDeleteRegularPicturePost$Plain } from '../fn/house/api-house-delete-regular-picture-post-plain';\nimport { apiHouseEditAppointmentPost$Json } from '../fn/house/api-house-edit-appointment-post-json';\nimport { apiHouseEditAppointmentPost$Plain } from '../fn/house/api-house-edit-appointment-post-plain';\nimport { apiHouseEditHouseInfoPost$Json } from '../fn/house/api-house-edit-house-info-post-json';\nimport { apiHouseEditHouseInfoPost$Plain } from '../fn/house/api-house-edit-house-info-post-plain';\nimport { apiHouseEditHousePost$Json } from '../fn/house/api-house-edit-house-post-json';\nimport { apiHouseEditHousePost$Plain } from '../fn/house/api-house-edit-house-post-plain';\nimport { apiHouseEditHouseRegularPicPost$Json } from '../fn/house/api-house-edit-house-regular-pic-post-json';\nimport { apiHouseEditHouseRegularPicPost$Plain } from '../fn/house/api-house-edit-house-regular-pic-post-plain';\nimport { apiHouseEditListHousePost$Json } from '../fn/house/api-house-edit-list-house-post-json';\nimport { apiHouseEditListHousePost$Plain } from '../fn/house/api-house-edit-list-house-post-plain';\nimport { apiHouseExportExcelListAppointmentsPost$Json } from '../fn/house/api-house-export-excel-list-appointments-post-json';\nimport { apiHouseExportExcelListAppointmentsPost$Plain } from '../fn/house/api-house-export-excel-list-appointments-post-plain';\nimport { apiHouseExportHousePost$Json } from '../fn/house/api-house-export-house-post-json';\nimport { apiHouseExportHousePost$Plain } from '../fn/house/api-house-export-house-post-plain';\nimport { apiHouseGetAppointmentByIdPost$Json } from '../fn/house/api-house-get-appointment-by-id-post-json';\nimport { apiHouseGetAppointmentByIdPost$Plain } from '../fn/house/api-house-get-appointment-by-id-post-plain';\nimport { apiHouseGetChangeDatePost$Json } from '../fn/house/api-house-get-change-date-post-json';\nimport { apiHouseGetChangeDatePost$Plain } from '../fn/house/api-house-get-change-date-post-plain';\nimport { apiHouseGetChangePreOrderPost$Json } from '../fn/house/api-house-get-change-pre-order-post-json';\nimport { apiHouseGetChangePreOrderPost$Plain } from '../fn/house/api-house-get-change-pre-order-post-plain';\nimport { apiHouseGetHourListAppointmentPost$Json } from '../fn/house/api-house-get-hour-list-appointment-post-json';\nimport { apiHouseGetHourListAppointmentPost$Plain } from '../fn/house/api-house-get-hour-list-appointment-post-plain';\nimport { apiHouseGetHourListPost$Json } from '../fn/house/api-house-get-hour-list-post-json';\nimport { apiHouseGetHourListPost$Plain } from '../fn/house/api-house-get-hour-list-post-plain';\nimport { apiHouseGetHouseByIdPost$Json } from '../fn/house/api-house-get-house-by-id-post-json';\nimport { apiHouseGetHouseByIdPost$Plain } from '../fn/house/api-house-get-house-by-id-post-plain';\nimport { apiHouseGetHouseChangeDatePost$Json } from '../fn/house/api-house-get-house-change-date-post-json';\nimport { apiHouseGetHouseChangeDatePost$Plain } from '../fn/house/api-house-get-house-change-date-post-plain';\nimport { apiHouseGetHouseInfoPost$Json } from '../fn/house/api-house-get-house-info-post-json';\nimport { apiHouseGetHouseInfoPost$Plain } from '../fn/house/api-house-get-house-info-post-plain';\nimport { apiHouseGetHouseListPost$Json } from '../fn/house/api-house-get-house-list-post-json';\nimport { apiHouseGetHouseListPost$Plain } from '../fn/house/api-house-get-house-list-post-plain';\nimport { apiHouseGetHouseProgressPost$Json } from '../fn/house/api-house-get-house-progress-post-json';\nimport { apiHouseGetHouseProgressPost$Plain } from '../fn/house/api-house-get-house-progress-post-plain';\nimport { apiHouseGetHouseRegularPicturePost$Json } from '../fn/house/api-house-get-house-regular-picture-post-json';\nimport { apiHouseGetHouseRegularPicturePost$Plain } from '../fn/house/api-house-get-house-regular-picture-post-plain';\nimport { apiHouseGetHouseRequirementPost$Json } from '../fn/house/api-house-get-house-requirement-post-json';\nimport { apiHouseGetHouseRequirementPost$Plain } from '../fn/house/api-house-get-house-requirement-post-plain';\nimport { apiHouseGetHouseReviewPost$Json } from '../fn/house/api-house-get-house-review-post-json';\nimport { apiHouseGetHouseReviewPost$Plain } from '../fn/house/api-house-get-house-review-post-plain';\nimport { apiHouseGetListAppointmentsPost$Json } from '../fn/house/api-house-get-list-appointments-post-json';\nimport { apiHouseGetListAppointmentsPost$Plain } from '../fn/house/api-house-get-list-appointments-post-plain';\nimport { apiHouseGetListBuildingPost$Json } from '../fn/house/api-house-get-list-building-post-json';\nimport { apiHouseGetListBuildingPost$Plain } from '../fn/house/api-house-get-list-building-post-plain';\nimport { apiHouseGetListHouseHoldPost$Json } from '../fn/house/api-house-get-list-house-hold-post-json';\nimport { apiHouseGetListHouseHoldPost$Plain } from '../fn/house/api-house-get-list-house-hold-post-plain';\nimport { apiHouseGetListHouseRegularPicPost$Json } from '../fn/house/api-house-get-list-house-regular-pic-post-json';\nimport { apiHouseGetListHouseRegularPicPost$Plain } from '../fn/house/api-house-get-list-house-regular-pic-post-plain';\nimport { apiHouseGetMilestonePost$Json } from '../fn/house/api-house-get-milestone-post-json';\nimport { apiHouseGetMilestonePost$Plain } from '../fn/house/api-house-get-milestone-post-plain';\nimport { apiHouseGetPayStatusPost$Json } from '../fn/house/api-house-get-pay-status-post-json';\nimport { apiHouseGetPayStatusPost$Plain } from '../fn/house/api-house-get-pay-status-post-plain';\nimport { apiHouseGetRegularNoticeFilePost$Json } from '../fn/house/api-house-get-regular-notice-file-post-json';\nimport { apiHouseGetRegularNoticeFilePost$Plain } from '../fn/house/api-house-get-regular-notice-file-post-plain';\nimport { apiHouseGetSpecialNoticeFilePost$Json } from '../fn/house/api-house-get-special-notice-file-post-json';\nimport { apiHouseGetSpecialNoticeFilePost$Plain } from '../fn/house/api-house-get-special-notice-file-post-plain';\nimport { apiHouseHouseLoginStep2Post$Json } from '../fn/house/api-house-house-login-step-2-post-json';\nimport { apiHouseHouseLoginStep2Post$Plain } from '../fn/house/api-house-house-login-step-2-post-plain';\nimport { apiHouseImportHousePost$Json } from '../fn/house/api-house-import-house-post-json';\nimport { apiHouseImportHousePost$Plain } from '../fn/house/api-house-import-house-post-plain';\nimport { apiHouseLoginPost$Json } from '../fn/house/api-house-login-post-json';\nimport { apiHouseLoginPost$Plain } from '../fn/house/api-house-login-post-plain';\nimport { apiHouseSaveHouseChangeDatePost$Json } from '../fn/house/api-house-save-house-change-date-post-json';\nimport { apiHouseSaveHouseChangeDatePost$Plain } from '../fn/house/api-house-save-house-change-date-post-plain';\nimport { apiHouseSaveMilestonePost$Json } from '../fn/house/api-house-save-milestone-post-json';\nimport { apiHouseSaveMilestonePost$Plain } from '../fn/house/api-house-save-milestone-post-plain';\nimport { apiHouseUpdateHouseProgressPost$Json } from '../fn/house/api-house-update-house-progress-post-json';\nimport { apiHouseUpdateHouseProgressPost$Plain } from '../fn/house/api-house-update-house-progress-post-plain';\nimport { apiHouseUpdateHouseRequirementPost$Json } from '../fn/house/api-house-update-house-requirement-post-json';\nimport { apiHouseUpdateHouseRequirementPost$Plain } from '../fn/house/api-house-update-house-requirement-post-plain';\nimport { apiHouseUpdateHouseReviewPost$Json } from '../fn/house/api-house-update-house-review-post-json';\nimport { apiHouseUpdateHouseReviewPost$Plain } from '../fn/house/api-house-update-house-review-post-plain';\nimport { apiHouseUploadRegularPicPost$Json } from '../fn/house/api-house-upload-regular-pic-post-json';\nimport { apiHouseUploadRegularPicPost$Plain } from '../fn/house/api-house-upload-regular-pic-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let HouseService = /*#__PURE__*/(() => {\n  class HouseService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiHouseLoginPost()` */\n    static {\n      this.ApiHouseLoginPostPath = '/api/House/Login';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseLoginPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseLoginPost$Plain$Response(params, context) {\n      return apiHouseLoginPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseLoginPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseLoginPost$Plain(params, context) {\n      return this.apiHouseLoginPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseLoginPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseLoginPost$Json$Response(params, context) {\n      return apiHouseLoginPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseLoginPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseLoginPost$Json(params, context) {\n      return this.apiHouseLoginPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetChangeDatePost()` */\n    static {\n      this.ApiHouseGetChangeDatePostPath = '/api/House/GetChangeDate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetChangeDatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangeDatePost$Plain$Response(params, context) {\n      return apiHouseGetChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangeDatePost$Plain(params, context) {\n      return this.apiHouseGetChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetChangeDatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangeDatePost$Json$Response(params, context) {\n      return apiHouseGetChangeDatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetChangeDatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangeDatePost$Json(params, context) {\n      return this.apiHouseGetChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseChangePreOrderPost()` */\n    static {\n      this.ApiHouseChangePreOrderPostPath = '/api/House/ChangePreOrder';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseChangePreOrderPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseChangePreOrderPost$Plain$Response(params, context) {\n      return apiHouseChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseChangePreOrderPost$Plain(params, context) {\n      return this.apiHouseChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseChangePreOrderPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseChangePreOrderPost$Json$Response(params, context) {\n      return apiHouseChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseChangePreOrderPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseChangePreOrderPost$Json(params, context) {\n      return this.apiHouseChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHourListPost()` */\n    static {\n      this.ApiHouseGetHourListPostPath = '/api/House/GetHourList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHourListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListPost$Plain$Response(params, context) {\n      return apiHouseGetHourListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHourListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListPost$Plain(params, context) {\n      return this.apiHouseGetHourListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHourListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListPost$Json$Response(params, context) {\n      return apiHouseGetHourListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHourListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListPost$Json(params, context) {\n      return this.apiHouseGetHourListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseCancelChangePreOrderPost()` */\n    static {\n      this.ApiHouseCancelChangePreOrderPostPath = '/api/House/CancelChangePreOrder';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCancelChangePreOrderPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCancelChangePreOrderPost$Plain$Response(params, context) {\n      return apiHouseCancelChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCancelChangePreOrderPost$Plain(params, context) {\n      return this.apiHouseCancelChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCancelChangePreOrderPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCancelChangePreOrderPost$Json$Response(params, context) {\n      return apiHouseCancelChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCancelChangePreOrderPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCancelChangePreOrderPost$Json(params, context) {\n      return this.apiHouseCancelChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseRegularPicturePost()` */\n    static {\n      this.ApiHouseGetHouseRegularPicturePostPath = '/api/House/GetHouseRegularPicture';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRegularPicturePost$Plain$Response(params, context) {\n      return apiHouseGetHouseRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRegularPicturePost$Plain(params, context) {\n      return this.apiHouseGetHouseRegularPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseRegularPicturePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRegularPicturePost$Json$Response(params, context) {\n      return apiHouseGetHouseRegularPicturePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseRegularPicturePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRegularPicturePost$Json(params, context) {\n      return this.apiHouseGetHouseRegularPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseHouseLoginStep2Post()` */\n    static {\n      this.ApiHouseHouseLoginStep2PostPath = '/api/House/HouseLoginStep2';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHouseLoginStep2Post$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHouseLoginStep2Post$Plain$Response(params, context) {\n      return apiHouseHouseLoginStep2Post$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHouseLoginStep2Post$Plain(params, context) {\n      return this.apiHouseHouseLoginStep2Post$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseHouseLoginStep2Post$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHouseLoginStep2Post$Json$Response(params, context) {\n      return apiHouseHouseLoginStep2Post$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseHouseLoginStep2Post$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseHouseLoginStep2Post$Json(params, context) {\n      return this.apiHouseHouseLoginStep2Post$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseAgreeDisclaimerPost()` */\n    static {\n      this.ApiHouseAgreeDisclaimerPostPath = '/api/House/AgreeDisclaimer';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseAgreeDisclaimerPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseAgreeDisclaimerPost$Plain$Response(params, context) {\n      return apiHouseAgreeDisclaimerPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseAgreeDisclaimerPost$Plain(params, context) {\n      return this.apiHouseAgreeDisclaimerPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseAgreeDisclaimerPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseAgreeDisclaimerPost$Json$Response(params, context) {\n      return apiHouseAgreeDisclaimerPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseAgreeDisclaimerPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseAgreeDisclaimerPost$Json(params, context) {\n      return this.apiHouseAgreeDisclaimerPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetRegularNoticeFilePost()` */\n    static {\n      this.ApiHouseGetRegularNoticeFilePostPath = '/api/House/GetRegularNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetRegularNoticeFilePost$Plain$Response(params, context) {\n      return apiHouseGetRegularNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetRegularNoticeFilePost$Plain(params, context) {\n      return this.apiHouseGetRegularNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetRegularNoticeFilePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetRegularNoticeFilePost$Json$Response(params, context) {\n      return apiHouseGetRegularNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetRegularNoticeFilePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetRegularNoticeFilePost$Json(params, context) {\n      return this.apiHouseGetRegularNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetSpecialNoticeFilePost()` */\n    static {\n      this.ApiHouseGetSpecialNoticeFilePostPath = '/api/House/GetSpecialNoticeFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetSpecialNoticeFilePost$Plain$Response(params, context) {\n      return apiHouseGetSpecialNoticeFilePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetSpecialNoticeFilePost$Plain(params, context) {\n      return this.apiHouseGetSpecialNoticeFilePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetSpecialNoticeFilePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetSpecialNoticeFilePost$Json$Response(params, context) {\n      return apiHouseGetSpecialNoticeFilePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetSpecialNoticeFilePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetSpecialNoticeFilePost$Json(params, context) {\n      return this.apiHouseGetSpecialNoticeFilePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetChangePreOrderPost()` */\n    static {\n      this.ApiHouseGetChangePreOrderPostPath = '/api/House/GetChangePreOrder';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetChangePreOrderPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangePreOrderPost$Plain$Response(params, context) {\n      return apiHouseGetChangePreOrderPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangePreOrderPost$Plain(params, context) {\n      return this.apiHouseGetChangePreOrderPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetChangePreOrderPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangePreOrderPost$Json$Response(params, context) {\n      return apiHouseGetChangePreOrderPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetChangePreOrderPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetChangePreOrderPost$Json(params, context) {\n      return this.apiHouseGetChangePreOrderPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseReviewPost()` */\n    static {\n      this.ApiHouseGetHouseReviewPostPath = '/api/House/GetHouseReview';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseReviewPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseReviewPost$Plain$Response(params, context) {\n      return apiHouseGetHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseReviewPost$Plain(params, context) {\n      return this.apiHouseGetHouseReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseReviewPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseReviewPost$Json$Response(params, context) {\n      return apiHouseGetHouseReviewPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseReviewPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseReviewPost$Json(params, context) {\n      return this.apiHouseGetHouseReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseUpdateHouseReviewPost()` */\n    static {\n      this.ApiHouseUpdateHouseReviewPostPath = '/api/House/UpdateHouseReview';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseReviewPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseReviewPost$Plain$Response(params, context) {\n      return apiHouseUpdateHouseReviewPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseReviewPost$Plain(params, context) {\n      return this.apiHouseUpdateHouseReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseReviewPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseReviewPost$Json$Response(params, context) {\n      return apiHouseUpdateHouseReviewPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseReviewPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseReviewPost$Json(params, context) {\n      return this.apiHouseUpdateHouseReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseRequirementPost()` */\n    static {\n      this.ApiHouseGetHouseRequirementPostPath = '/api/House/GetHouseRequirement';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseRequirementPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRequirementPost$Plain$Response(params, context) {\n      return apiHouseGetHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRequirementPost$Plain(params, context) {\n      return this.apiHouseGetHouseRequirementPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseRequirementPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRequirementPost$Json$Response(params, context) {\n      return apiHouseGetHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseRequirementPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseRequirementPost$Json(params, context) {\n      return this.apiHouseGetHouseRequirementPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseUpdateHouseRequirementPost()` */\n    static {\n      this.ApiHouseUpdateHouseRequirementPostPath = '/api/House/UpdateHouseRequirement';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseRequirementPost$Plain$Response(params, context) {\n      return apiHouseUpdateHouseRequirementPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseRequirementPost$Plain(params, context) {\n      return this.apiHouseUpdateHouseRequirementPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseRequirementPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseRequirementPost$Json$Response(params, context) {\n      return apiHouseUpdateHouseRequirementPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseRequirementPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseRequirementPost$Json(params, context) {\n      return this.apiHouseUpdateHouseRequirementPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseCheckIsReviewPost()` */\n    static {\n      this.ApiHouseCheckIsReviewPostPath = '/api/House/CheckIsReview';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCheckIsReviewPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseCheckIsReviewPost$Plain$Response(params, context) {\n      return apiHouseCheckIsReviewPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseCheckIsReviewPost$Plain(params, context) {\n      return this.apiHouseCheckIsReviewPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCheckIsReviewPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseCheckIsReviewPost$Json$Response(params, context) {\n      return apiHouseCheckIsReviewPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCheckIsReviewPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseCheckIsReviewPost$Json(params, context) {\n      return this.apiHouseCheckIsReviewPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseInfoPost()` */\n    static {\n      this.ApiHouseGetHouseInfoPostPath = '/api/House/GetHouseInfo';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseInfoPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseInfoPost$Plain$Response(params, context) {\n      return apiHouseGetHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseInfoPost$Plain(params, context) {\n      return this.apiHouseGetHouseInfoPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseInfoPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseInfoPost$Json$Response(params, context) {\n      return apiHouseGetHouseInfoPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseInfoPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseInfoPost$Json(params, context) {\n      return this.apiHouseGetHouseInfoPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseEditHouseInfoPost()` */\n    static {\n      this.ApiHouseEditHouseInfoPostPath = '/api/House/EditHouseInfo';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHouseInfoPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseInfoPost$Plain$Response(params, context) {\n      return apiHouseEditHouseInfoPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseInfoPost$Plain(params, context) {\n      return this.apiHouseEditHouseInfoPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHouseInfoPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseInfoPost$Json$Response(params, context) {\n      return apiHouseEditHouseInfoPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHouseInfoPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseInfoPost$Json(params, context) {\n      return this.apiHouseEditHouseInfoPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetMilestonePost()` */\n    static {\n      this.ApiHouseGetMilestonePostPath = '/api/House/GetMilestone';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetMilestonePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetMilestonePost$Plain$Response(params, context) {\n      return apiHouseGetMilestonePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetMilestonePost$Plain(params, context) {\n      return this.apiHouseGetMilestonePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetMilestonePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetMilestonePost$Json$Response(params, context) {\n      return apiHouseGetMilestonePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetMilestonePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetMilestonePost$Json(params, context) {\n      return this.apiHouseGetMilestonePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseSaveMilestonePost()` */\n    static {\n      this.ApiHouseSaveMilestonePostPath = '/api/House/SaveMilestone';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseSaveMilestonePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveMilestonePost$Plain$Response(params, context) {\n      return apiHouseSaveMilestonePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveMilestonePost$Plain(params, context) {\n      return this.apiHouseSaveMilestonePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseSaveMilestonePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveMilestonePost$Json$Response(params, context) {\n      return apiHouseSaveMilestonePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseSaveMilestonePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveMilestonePost$Json(params, context) {\n      return this.apiHouseSaveMilestonePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseProgressPost()` */\n    static {\n      this.ApiHouseGetHouseProgressPostPath = '/api/House/GetHouseProgress';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseProgressPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseProgressPost$Plain$Response(params, context) {\n      return apiHouseGetHouseProgressPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseProgressPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseProgressPost$Plain(params, context) {\n      return this.apiHouseGetHouseProgressPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseProgressPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseProgressPost$Json$Response(params, context) {\n      return apiHouseGetHouseProgressPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseProgressPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetHouseProgressPost$Json(params, context) {\n      return this.apiHouseGetHouseProgressPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseUpdateHouseProgressPost()` */\n    static {\n      this.ApiHouseUpdateHouseProgressPostPath = '/api/House/UpdateHouseProgress';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseProgressPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseProgressPost$Plain$Response(params, context) {\n      return apiHouseUpdateHouseProgressPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseProgressPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseProgressPost$Plain(params, context) {\n      return this.apiHouseUpdateHouseProgressPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUpdateHouseProgressPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseProgressPost$Json$Response(params, context) {\n      return apiHouseUpdateHouseProgressPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUpdateHouseProgressPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUpdateHouseProgressPost$Json(params, context) {\n      return this.apiHouseUpdateHouseProgressPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetPayStatusPost()` */\n    static {\n      this.ApiHouseGetPayStatusPostPath = '/api/House/GetPayStatus';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetPayStatusPost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetPayStatusPost$Plain$Response(params, context) {\n      return apiHouseGetPayStatusPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetPayStatusPost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetPayStatusPost$Plain(params, context) {\n      return this.apiHouseGetPayStatusPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetPayStatusPost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetPayStatusPost$Json$Response(params, context) {\n      return apiHouseGetPayStatusPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetPayStatusPost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseGetPayStatusPost$Json(params, context) {\n      return this.apiHouseGetPayStatusPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetListBuildingPost()` */\n    static {\n      this.ApiHouseGetListBuildingPostPath = '/api/House/GetListBuilding';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListBuildingPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListBuildingPost$Plain$Response(params, context) {\n      return apiHouseGetListBuildingPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListBuildingPost$Plain(params, context) {\n      return this.apiHouseGetListBuildingPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListBuildingPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListBuildingPost$Json$Response(params, context) {\n      return apiHouseGetListBuildingPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListBuildingPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListBuildingPost$Json(params, context) {\n      return this.apiHouseGetListBuildingPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetListHouseHoldPost()` */\n    static {\n      this.ApiHouseGetListHouseHoldPostPath = '/api/House/GetListHouseHold';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListHouseHoldPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseHoldPost$Plain$Response(params, context) {\n      return apiHouseGetListHouseHoldPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseHoldPost$Plain(params, context) {\n      return this.apiHouseGetListHouseHoldPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListHouseHoldPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseHoldPost$Json$Response(params, context) {\n      return apiHouseGetListHouseHoldPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListHouseHoldPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseHoldPost$Json(params, context) {\n      return this.apiHouseGetListHouseHoldPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseListPost()` */\n    static {\n      this.ApiHouseGetHouseListPostPath = '/api/House/GetHouseList';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseListPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseListPost$Plain$Response(params, context) {\n      return apiHouseGetHouseListPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseListPost$Plain(params, context) {\n      return this.apiHouseGetHouseListPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseListPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseListPost$Json$Response(params, context) {\n      return apiHouseGetHouseListPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseListPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseListPost$Json(params, context) {\n      return this.apiHouseGetHouseListPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseByIdPost()` */\n    static {\n      this.ApiHouseGetHouseByIdPostPath = '/api/House/GetHouseByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseByIdPost$Plain$Response(params, context) {\n      return apiHouseGetHouseByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseByIdPost$Plain(params, context) {\n      return this.apiHouseGetHouseByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseByIdPost$Json$Response(params, context) {\n      return apiHouseGetHouseByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseByIdPost$Json(params, context) {\n      return this.apiHouseGetHouseByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseEditHousePost()` */\n    static {\n      this.ApiHouseEditHousePostPath = '/api/House/EditHouse';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHousePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHousePost$Plain$Response(params, context) {\n      return apiHouseEditHousePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHousePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHousePost$Plain(params, context) {\n      return this.apiHouseEditHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHousePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHousePost$Json$Response(params, context) {\n      return apiHouseEditHousePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHousePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHousePost$Json(params, context) {\n      return this.apiHouseEditHousePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseEditListHousePost()` */\n    static {\n      this.ApiHouseEditListHousePostPath = '/api/House/EditListHouse';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditListHousePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditListHousePost$Plain$Response(params, context) {\n      return apiHouseEditListHousePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditListHousePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditListHousePost$Plain(params, context) {\n      return this.apiHouseEditListHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditListHousePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditListHousePost$Json$Response(params, context) {\n      return apiHouseEditListHousePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditListHousePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditListHousePost$Json(params, context) {\n      return this.apiHouseEditListHousePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetListHouseRegularPicPost()` */\n    static {\n      this.ApiHouseGetListHouseRegularPicPostPath = '/api/House/GetListHouseRegularPic';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseRegularPicPost$Plain$Response(params, context) {\n      return apiHouseGetListHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseRegularPicPost$Plain(params, context) {\n      return this.apiHouseGetListHouseRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListHouseRegularPicPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseRegularPicPost$Json$Response(params, context) {\n      return apiHouseGetListHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListHouseRegularPicPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListHouseRegularPicPost$Json(params, context) {\n      return this.apiHouseGetListHouseRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseEditHouseRegularPicPost()` */\n    static {\n      this.ApiHouseEditHouseRegularPicPostPath = '/api/House/EditHouseRegularPic';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHouseRegularPicPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseRegularPicPost$Plain$Response(params, context) {\n      return apiHouseEditHouseRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseRegularPicPost$Plain(params, context) {\n      return this.apiHouseEditHouseRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditHouseRegularPicPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseRegularPicPost$Json$Response(params, context) {\n      return apiHouseEditHouseRegularPicPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditHouseRegularPicPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditHouseRegularPicPost$Json(params, context) {\n      return this.apiHouseEditHouseRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseDeleteRegularPicturePost()` */\n    static {\n      this.ApiHouseDeleteRegularPicturePostPath = '/api/House/DeleteRegularPicture';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseDeleteRegularPicturePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteRegularPicturePost$Plain$Response(params, context) {\n      return apiHouseDeleteRegularPicturePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteRegularPicturePost$Plain(params, context) {\n      return this.apiHouseDeleteRegularPicturePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseDeleteRegularPicturePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteRegularPicturePost$Json$Response(params, context) {\n      return apiHouseDeleteRegularPicturePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseDeleteRegularPicturePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteRegularPicturePost$Json(params, context) {\n      return this.apiHouseDeleteRegularPicturePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseUploadRegularPicPost()` */\n    static {\n      this.ApiHouseUploadRegularPicPostPath = '/api/House/UploadRegularPic';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUploadRegularPicPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUploadRegularPicPost$Plain$Response(params, context) {\n      return apiHouseUploadRegularPicPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUploadRegularPicPost$Plain(params, context) {\n      return this.apiHouseUploadRegularPicPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseUploadRegularPicPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUploadRegularPicPost$Json$Response(params, context) {\n      return apiHouseUploadRegularPicPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseUploadRegularPicPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseUploadRegularPicPost$Json(params, context) {\n      return this.apiHouseUploadRegularPicPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetListAppointmentsPost()` */\n    static {\n      this.ApiHouseGetListAppointmentsPostPath = '/api/House/GetListAppointments';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListAppointmentsPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListAppointmentsPost$Plain$Response(params, context) {\n      return apiHouseGetListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListAppointmentsPost$Plain(params, context) {\n      return this.apiHouseGetListAppointmentsPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetListAppointmentsPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListAppointmentsPost$Json$Response(params, context) {\n      return apiHouseGetListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetListAppointmentsPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetListAppointmentsPost$Json(params, context) {\n      return this.apiHouseGetListAppointmentsPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetAppointmentByIdPost()` */\n    static {\n      this.ApiHouseGetAppointmentByIdPostPath = '/api/House/GetAppointmentByID';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetAppointmentByIdPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetAppointmentByIdPost$Plain$Response(params, context) {\n      return apiHouseGetAppointmentByIdPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetAppointmentByIdPost$Plain(params, context) {\n      return this.apiHouseGetAppointmentByIdPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetAppointmentByIdPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetAppointmentByIdPost$Json$Response(params, context) {\n      return apiHouseGetAppointmentByIdPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetAppointmentByIdPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetAppointmentByIdPost$Json(params, context) {\n      return this.apiHouseGetAppointmentByIdPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseEditAppointmentPost()` */\n    static {\n      this.ApiHouseEditAppointmentPostPath = '/api/House/EditAppointment';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditAppointmentPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditAppointmentPost$Plain$Response(params, context) {\n      return apiHouseEditAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditAppointmentPost$Plain(params, context) {\n      return this.apiHouseEditAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseEditAppointmentPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditAppointmentPost$Json$Response(params, context) {\n      return apiHouseEditAppointmentPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseEditAppointmentPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseEditAppointmentPost$Json(params, context) {\n      return this.apiHouseEditAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseDeleteAppointmentPost()` */\n    static {\n      this.ApiHouseDeleteAppointmentPostPath = '/api/House/DeleteAppointment';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseDeleteAppointmentPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteAppointmentPost$Plain$Response(params, context) {\n      return apiHouseDeleteAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteAppointmentPost$Plain(params, context) {\n      return this.apiHouseDeleteAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseDeleteAppointmentPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteAppointmentPost$Json$Response(params, context) {\n      return apiHouseDeleteAppointmentPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseDeleteAppointmentPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseDeleteAppointmentPost$Json(params, context) {\n      return this.apiHouseDeleteAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseCreateAppointmentPost()` */\n    static {\n      this.ApiHouseCreateAppointmentPostPath = '/api/House/CreateAppointment';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCreateAppointmentPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCreateAppointmentPost$Plain$Response(params, context) {\n      return apiHouseCreateAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCreateAppointmentPost$Plain(params, context) {\n      return this.apiHouseCreateAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseCreateAppointmentPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCreateAppointmentPost$Json$Response(params, context) {\n      return apiHouseCreateAppointmentPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseCreateAppointmentPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseCreateAppointmentPost$Json(params, context) {\n      return this.apiHouseCreateAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHourListAppointmentPost()` */\n    static {\n      this.ApiHouseGetHourListAppointmentPostPath = '/api/House/GetHourListAppointment';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHourListAppointmentPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListAppointmentPost$Plain$Response(params, context) {\n      return apiHouseGetHourListAppointmentPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListAppointmentPost$Plain(params, context) {\n      return this.apiHouseGetHourListAppointmentPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHourListAppointmentPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListAppointmentPost$Json$Response(params, context) {\n      return apiHouseGetHourListAppointmentPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHourListAppointmentPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHourListAppointmentPost$Json(params, context) {\n      return this.apiHouseGetHourListAppointmentPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseExportExcelListAppointmentsPost()` */\n    static {\n      this.ApiHouseExportExcelListAppointmentsPostPath = '/api/House/ExportExcelListAppointments';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseExportExcelListAppointmentsPost$Plain$Response(params, context) {\n      return apiHouseExportExcelListAppointmentsPost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseExportExcelListAppointmentsPost$Plain(params, context) {\n      return this.apiHouseExportExcelListAppointmentsPost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseExportExcelListAppointmentsPost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseExportExcelListAppointmentsPost$Json$Response(params, context) {\n      return apiHouseExportExcelListAppointmentsPost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseExportExcelListAppointmentsPost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseExportExcelListAppointmentsPost$Json(params, context) {\n      return this.apiHouseExportExcelListAppointmentsPost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseGetHouseChangeDatePost()` */\n    static {\n      this.ApiHouseGetHouseChangeDatePostPath = '/api/House/GetHouseChangeDate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseChangeDatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseChangeDatePost$Plain$Response(params, context) {\n      return apiHouseGetHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseChangeDatePost$Plain(params, context) {\n      return this.apiHouseGetHouseChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseGetHouseChangeDatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseChangeDatePost$Json$Response(params, context) {\n      return apiHouseGetHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseGetHouseChangeDatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseGetHouseChangeDatePost$Json(params, context) {\n      return this.apiHouseGetHouseChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseSaveHouseChangeDatePost()` */\n    static {\n      this.ApiHouseSaveHouseChangeDatePostPath = '/api/House/SaveHouseChangeDate';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Plain()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveHouseChangeDatePost$Plain$Response(params, context) {\n      return apiHouseSaveHouseChangeDatePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Plain$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveHouseChangeDatePost$Plain(params, context) {\n      return this.apiHouseSaveHouseChangeDatePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseSaveHouseChangeDatePost$Json()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveHouseChangeDatePost$Json$Response(params, context) {\n      return apiHouseSaveHouseChangeDatePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseSaveHouseChangeDatePost$Json$Response()` instead.\n     *\n     * This method sends `application/*+json` and handles request body of type `application/*+json`.\n     */\n    apiHouseSaveHouseChangeDatePost$Json(params, context) {\n      return this.apiHouseSaveHouseChangeDatePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseExportHousePost()` */\n    static {\n      this.ApiHouseExportHousePostPath = '/api/House/ExportHouse';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseExportHousePost$Plain()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseExportHousePost$Plain$Response(params, context) {\n      return apiHouseExportHousePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseExportHousePost$Plain$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseExportHousePost$Plain(params, context) {\n      return this.apiHouseExportHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseExportHousePost$Json()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseExportHousePost$Json$Response(params, context) {\n      return apiHouseExportHousePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseExportHousePost$Json$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiHouseExportHousePost$Json(params, context) {\n      return this.apiHouseExportHousePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    /** Path part for operation `apiHouseImportHousePost()` */\n    static {\n      this.ApiHouseImportHousePostPath = '/api/House/ImportHouse';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseImportHousePost$Plain()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiHouseImportHousePost$Plain$Response(params, context) {\n      return apiHouseImportHousePost$Plain(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseImportHousePost$Plain$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiHouseImportHousePost$Plain(params, context) {\n      return this.apiHouseImportHousePost$Plain$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiHouseImportHousePost$Json()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiHouseImportHousePost$Json$Response(params, context) {\n      return apiHouseImportHousePost$Json(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiHouseImportHousePost$Json$Response()` instead.\n     *\n     * This method sends `multipart/form-data` and handles request body of type `multipart/form-data`.\n     */\n    apiHouseImportHousePost$Json(params, context) {\n      return this.apiHouseImportHousePost$Json$Response(params, context).pipe(map(r => r.body));\n    }\n    static {\n      this.ɵfac = function HouseService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: HouseService,\n        factory: HouseService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return HouseService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}