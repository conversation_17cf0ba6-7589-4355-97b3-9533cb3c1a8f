{"ast": null, "code": "import { SharedModule } from '../components/shared.module';\nimport { CommonModule } from '@angular/common';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { BaseComponent } from '../components/base/baseComponent';\nimport { concatMap, tap } from 'rxjs';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport * as moment from 'moment';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport { EnumHouseProgress } from 'src/app/shared/enum/enumHouseProgress';\nimport { EnumHouseType } from 'src/app/shared/enum/enumHouseType';\nimport { EnumPayStatus } from 'src/app/shared/enum/enumPayStatus';\nimport { EnumSignStatus } from 'src/app/shared/enum/enumSignStatus';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/app/shared/helper/enumHelper\";\nimport * as i3 from \"@nebular/theme\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"src/services/api/services\";\nimport * as i7 from \"src/app/shared/helper/petternHelper\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"src/app/shared/services/event.service\";\nimport * as i10 from \"src/app/shared/services/utility.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i14 from \"../components/breadcrumb/breadcrumb.component\";\nimport * as i15 from \"../../@theme/directives/label.directive\";\nconst _c0 = [\"fileInput\"];\nfunction HouseholdManagementComponent_nb_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r3.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r4.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r5.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r6.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r7.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r8.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_nb_option_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r9.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_button_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_button_72_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogHouseholdMain_r12 = i0.ɵɵreference(112);\n      return i0.ɵɵresetView(ctx_r10.openModel(dialogHouseholdMain_r12));\n    });\n    i0.ɵɵtext(1, \" \\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      const dialogUpdateHousehold_r16 = i0.ɵɵreference(110);\n      return i0.ɵɵresetView(ctx_r10.openModelDetail(dialogUpdateHousehold_r16, item_r15));\n    });\n    i0.ɵɵtext(1, \" \\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const item_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"customer-change-picture\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(1, \" \\u5BA2\\u8B8A\\u5716\\u4E0A\\u50B3 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_tr_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\", 49);\n    i0.ɵɵtemplate(20, HouseholdManagementComponent_tr_106_button_20_Template, 2, 0, \"button\", 50)(21, HouseholdManagementComponent_tr_106_button_21_Template, 2, 0, \"button\", 51);\n    i0.ɵɵelementStart(22, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_Template_button_click_22_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"sample-selection-result\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(23, \" \\u9078\\u6A23\\u53CA\\u5BA2\\u8B8A\\u7D50\\u679C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_tr_106_Template_button_click_24_listener() {\n      const item_r15 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNavidateBuildCaseIdHouseId(\"finaldochouse_management\", ctx_r10.searchQuery.CBuildCaseSelected.cID, item_r15.CID));\n    });\n    i0.ɵɵtext(25, \" \\u7C3D\\u7F72\\u6587\\u4EF6\\u6B77\\u7A0B \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CFloor);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", item_r15.CHouseType === 2 ? \"\\u92B7\\u552E\\u6236\" : \"\", \" \", item_r15.CHouseType === 1 ? \"\\u5730\\u4E3B\\u6236\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsChange === true ? \"\\u5BA2\\u8B8A\" : item_r15.CIsChange === false ? \"\\u6A19\\u6E96\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CProgressName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\" \", item_r15.CPayStatus === 0 ? \"\\u672A\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 1 ? \"\\u5DF2\\u4ED8\\u6B3E\" : \"\", \" \", item_r15.CPayStatus === 2 ? \"\\u7121\\u9808\\u4ED8\\u6B3E\" : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CSignStatus === 0 || item_r15.CSignStatus == null ? \"\\u672A\\u7C3D\\u56DE\" : \"\\u5DF2\\u7C3D\\u56DE\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r15.CIsEnable ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r15.CPayStatus !== 2);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r20);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r20.CBuildCaseName, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r21);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r21.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r22);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r22.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r23.label, \" \");\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card-body\", 58)(1, \"div\", 59)(2, \"label\", 60);\n    i0.ɵɵtext(3, \" \\u5EFA\\u6848\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"nb-select\", 61);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CBuildCaseSelected, $event) || (ctx_r10.detailSelected.CBuildCaseSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_5_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 59)(7, \"label\", 62);\n    i0.ɵɵtext(8, \" \\u6236\\u578B\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 63);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CHousehold, $event) || (ctx_r10.houseDetail.CHousehold = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 59)(11, \"label\", 64);\n    i0.ɵɵtext(12, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CFloor, $event) || (ctx_r10.houseDetail.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 59)(15, \"label\", 66);\n    i0.ɵɵtext(16, \" \\u5BA2\\u6236\\u59D3\\u540D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"input\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CCustomerName, $event) || (ctx_r10.houseDetail.CCustomerName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 59)(19, \"label\", 68);\n    i0.ɵɵtext(20, \" \\u8EAB\\u5206\\u8B49\\u5B57\\u865F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"input\", 69);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CNationalId, $event) || (ctx_r10.houseDetail.CNationalId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"label\", 70);\n    i0.ɵɵtext(24, \" \\u96FB\\u5B50\\u90F5\\u4EF6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 71);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CMail, $event) || (ctx_r10.houseDetail.CMail = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 59)(27, \"label\", 72);\n    i0.ɵɵtext(28, \" \\u806F\\u7D61\\u96FB\\u8A71 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"input\", 73);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CPhone, $event) || (ctx_r10.houseDetail.CPhone = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 59)(31, \"label\", 74);\n    i0.ɵɵtext(32, \" \\u6236\\u5225\\u985E\\u578B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-select\", 75);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CHouseTypeSelected, $event) || (ctx_r10.detailSelected.CHouseTypeSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(34, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_34_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 59)(36, \"label\", 76);\n    i0.ɵɵtext(37, \" \\u4ED8\\u6B3E\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"nb-select\", 77);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CPayStatusSelected, $event) || (ctx_r10.detailSelected.CPayStatusSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(39, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_39_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 59)(41, \"label\", 78);\n    i0.ɵɵtext(42, \" \\u9032\\u5EA6 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"nb-select\", 79);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_select_ngModelChange_43_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.detailSelected.CProgressSelected, $event) || (ctx_r10.detailSelected.CProgressSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(44, HouseholdManagementComponent_ng_template_109_nb_card_body_1_nb_option_44_Template, 2, 2, \"nb-option\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 59)(46, \"label\", 80);\n    i0.ɵɵtext(47, \" \\u662F\\u5426\\u5BA2\\u8B8A \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_48_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsChange, $event) || (ctx_r10.houseDetail.CIsChange = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(49, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 59)(51, \"label\", 82);\n    i0.ɵɵtext(52, \" \\u662F\\u5426\\u555F\\u7528 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"nb-checkbox\", 81);\n    i0.ɵɵtwoWayListener(\"checkedChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_nb_checkbox_checkedChange_53_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.CIsEnable, $event) || (ctx_r10.houseDetail.CIsEnable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(54, \"\\u662F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 83)(56, \"label\", 84);\n    i0.ɵɵtext(57, \" \\u5BA2\\u8B8A\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 85)(59, \"nb-form-field\", 86);\n    i0.ɵɵelement(60, \"nb-icon\", 87);\n    i0.ɵɵelementStart(61, \"input\", 88);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_61_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeStartDate, $event) || (ctx_r10.houseDetail.changeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(62, \"nb-datepicker\", 89, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"nb-form-field\", 86);\n    i0.ɵɵelement(65, \"nb-icon\", 87);\n    i0.ɵɵelementStart(66, \"input\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template_input_ngModelChange_66_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseDetail.changeEndDate, $event) || (ctx_r10.houseDetail.changeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(67, \"nb-datepicker\", 89, 4);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const StartDate_r24 = i0.ɵɵreference(63);\n    const EndDate_r25 = i0.ɵɵreference(68);\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CBuildCaseSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.userBuildCaseOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CHousehold);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CFloor);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CCustomerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CNationalId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CMail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.CPhone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CHouseTypeSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.houseTypeOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CPayStatusSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.payStatusOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.detailSelected.CProgressSelected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.options.progressOptions);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsChange);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"checked\", ctx_r10.houseDetail.CIsEnable);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"nbDatepicker\", StartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeStartDate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"nbDatepicker\", EndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseDetail.changeEndDate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_109_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ref_r26 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onSubmitDetail(ref_r26));\n    });\n    i0.ɵɵtext(1, \"\\u9001\\u51FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_109_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54);\n    i0.ɵɵtemplate(1, HouseholdManagementComponent_ng_template_109_nb_card_body_1_Template, 69, 20, \"nb-card-body\", 55);\n    i0.ɵɵelementStart(2, \"nb-card-footer\", 46)(3, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_109_Template_button_click_3_listener() {\n      const ref_r26 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r26));\n    });\n    i0.ɵɵtext(4, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, HouseholdManagementComponent_ng_template_109_button_5_Template, 2, 0, \"button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.houseDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_button_19_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ref_r29 = i0.ɵɵnextContext().dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.addHouseHoldMain(ref_r29));\n    });\n    i0.ɵɵtext(1, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HouseholdManagementComponent_ng_template_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 54)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6236\\u5225\\u7BA1\\u7406 \\u300B\\u6279\\u6B21\\u65B0\\u589E\\u6236\\u5225\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\", 58)(4, \"div\", 59)(5, \"label\", 92);\n    i0.ɵɵtext(6, \" \\u68DF\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 93);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CBuildingName, $event) || (ctx_r10.houseHoldMain.CBuildingName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 59)(9, \"label\", 94);\n    i0.ɵɵtext(10, \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 95);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CHouseHoldCount, $event) || (ctx_r10.houseHoldMain.CHouseHoldCount = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 59)(13, \"label\", 96);\n    i0.ɵɵtext(14, \"\\u672C\\u68DF\\u7E3D\\u6A13\\u5C64 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 97);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_ng_template_111_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r10 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r10.houseHoldMain.CFloor, $event) || (ctx_r10.houseHoldMain.CFloor = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"nb-card-footer\", 46)(17, \"button\", 98);\n    i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_ng_template_111_Template_button_click_17_listener() {\n      const ref_r29 = i0.ɵɵrestoreView(_r28).dialogRef;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onClose(ref_r29));\n    });\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, HouseholdManagementComponent_ng_template_111_button_19_Template, 2, 0, \"button\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CBuildingName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CHouseHoldCount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r10.houseHoldMain.CFloor);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(\"\\u95DC\\u9589\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isCreate);\n  }\n}\nexport let HouseholdManagementComponent = /*#__PURE__*/(() => {\n  class HouseholdManagementComponent extends BaseComponent {\n    constructor(_allow, enumHelper, dialogService, message, valid, _houseService, _houseHoldMainService, _buildCaseService, pettern, router, _eventService, _ultilityService) {\n      super(_allow);\n      this._allow = _allow;\n      this.enumHelper = enumHelper;\n      this.dialogService = dialogService;\n      this.message = message;\n      this.valid = valid;\n      this._houseService = _houseService;\n      this._houseHoldMainService = _houseHoldMainService;\n      this._buildCaseService = _buildCaseService;\n      this.pettern = pettern;\n      this.router = router;\n      this._eventService = _eventService;\n      this._ultilityService = _ultilityService;\n      this.tempBuildCaseID = -1;\n      this.pageFirst = 1;\n      this.pageSize = 10;\n      this.pageIndex = 1;\n      this.totalRecords = 0;\n      this.statusOptions = [{\n        value: 0,\n        key: 'allow',\n        label: '允許'\n      }, {\n        value: 1,\n        key: 'not allowed',\n        label: '不允許'\n      }];\n      this.cIsEnableOptions = [{\n        value: null,\n        key: 'all',\n        label: '全部'\n      }, {\n        value: true,\n        key: 'enable',\n        label: '啟用'\n      }, {\n        value: false,\n        key: 'deactivate',\n        label: '停用'\n      }];\n      this.buildCaseOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.houseHoldOptions = [{\n        label: '全部',\n        value: ''\n      }];\n      this.progressOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.houseTypeOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.payStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.signStatusOptions = [{\n        label: '全部',\n        value: -1\n      }];\n      this.options = {\n        progressOptions: this.enumHelper.getEnumOptions(EnumHouseProgress),\n        payStatusOptions: this.enumHelper.getEnumOptions(EnumPayStatus),\n        houseTypeOptions: this.enumHelper.getEnumOptions(EnumHouseType)\n      };\n      this.initDetail = {\n        CHouseID: 0,\n        CMail: \"\",\n        CIsChange: false,\n        CPayStatus: 0,\n        CIsEnable: false,\n        CCustomerName: \"\",\n        CNationalID: \"\",\n        CProgress: \"\",\n        CHouseType: 0,\n        CHouseHold: \"\",\n        CPhone: \"\"\n      };\n      this.selectedFile = null;\n      this.buildingSelectedOptions = [{\n        value: '',\n        label: '全部'\n      }];\n      this.searchQuery = {\n        CBuildCaseSelected: null,\n        CHouseTypeSelected: this.houseTypeOptions[0],\n        CBuildingNameSelected: this.buildingSelectedOptions[0],\n        CHouseHoldSelected: this.houseHoldOptions[0],\n        CPayStatusSelected: this.payStatusOptions[0],\n        CProgressSelected: this.progressOptions[0],\n        CSignStatusSelected: this.signStatusOptions[0],\n        CIsEnableSeleted: this.cIsEnableOptions[0],\n        CFrom: '',\n        CTo: ''\n      };\n      this._eventService.receive().pipe(tap(res => {\n        if (res.action == \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */ && !!res.payload) {\n          this.tempBuildCaseID = res.payload;\n        }\n      })).subscribe();\n    }\n    ngOnInit() {\n      this.getListBuildCase();\n      this.progressOptions = [...this.progressOptions, ...this.enumHelper.getEnumOptions(EnumHouseProgress)];\n      this.houseTypeOptions = [...this.houseTypeOptions, ...this.enumHelper.getEnumOptions(EnumHouseType)];\n      this.payStatusOptions = [...this.payStatusOptions, ...this.enumHelper.getEnumOptions(EnumPayStatus)];\n      this.signStatusOptions = [...this.signStatusOptions, ...this.enumHelper.getEnumOptions(EnumSignStatus)];\n    }\n    onSearch() {\n      this.getHouseList().subscribe();\n    }\n    pageChanged(newPage) {\n      this.pageIndex = newPage;\n      this.getHouseList().subscribe();\n    }\n    exportHouse() {\n      if (this.searchQuery.CBuildCaseSelected.cID) {\n        this._houseService.apiHouseExportHousePost$Json({\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }).subscribe(res => {\n          if (res.Entries && res.StatusCode == 0) {\n            this._ultilityService.downloadExcelFile(res.Entries, '戶別資訊範本');\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    triggerFileInput() {\n      this.fileInput.nativeElement.click();\n    }\n    onFileSelected(event) {\n      const input = event.target;\n      if (input.files && input.files.length > 0) {\n        this.selectedFile = input.files[0];\n        this.importExcel();\n      }\n    }\n    importExcel() {\n      if (this.selectedFile) {\n        const formData = new FormData();\n        formData.append('CFile', this.selectedFile);\n        this._houseService.apiHouseImportHousePost$Json({\n          body: {\n            CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n            CFile: this.selectedFile\n          }\n        }).subscribe(res => {\n          if (res.StatusCode === 0) {\n            this.message.showSucessMSG(res.Message);\n            this.getHouseList().subscribe();\n          } else {\n            this.message.showErrorMSG(res.Message);\n          }\n        });\n      }\n    }\n    getListHouseHold() {\n      this._houseService.apiHouseGetListHouseHoldPost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseHoldOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n          this.searchQuery.CHouseHoldSelected = this.houseHoldOptions[0];\n        }\n      });\n    }\n    formatQuery() {\n      this.bodyRequest = {\n        CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID,\n        PageIndex: this.pageIndex,\n        PageSize: this.pageSize\n      };\n      if (this.searchQuery.CBuildingNameSelected.value) {\n        this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value;\n      }\n      if (this.searchQuery.CBuildingNameSelected.value) {\n        this.bodyRequest['CBuildingName'] = this.searchQuery.CBuildingNameSelected.value;\n      }\n      if (this.searchQuery.CFrom && this.searchQuery.CTo) {\n        this.bodyRequest['CFloor'] = {\n          CFrom: this.searchQuery.CFrom,\n          CTo: this.searchQuery.CTo\n        };\n      }\n      if (this.searchQuery.CHouseHoldSelected.value) {\n        this.bodyRequest['CHouseHold'] = this.searchQuery.CHouseHoldSelected.value;\n      }\n      if (this.searchQuery.CHouseTypeSelected.value) {\n        this.bodyRequest['CHouseType'] = this.searchQuery.CHouseTypeSelected.value;\n      }\n      if (typeof this.searchQuery.CIsEnableSeleted.value === \"boolean\") {\n        this.bodyRequest['CIsEnable'] = this.searchQuery.CIsEnableSeleted.value;\n      }\n      if (this.searchQuery.CPayStatusSelected.value) {\n        this.bodyRequest['CPayStatus'] = this.searchQuery.CPayStatusSelected.value;\n      }\n      if (this.searchQuery.CProgressSelected.value) {\n        this.bodyRequest['CProgress'] = this.searchQuery.CProgressSelected.value;\n      }\n      if (this.searchQuery.CSignStatusSelected.value) {\n        this.bodyRequest['CSignStatus'] = this.searchQuery.CSignStatusSelected.value;\n      }\n      return this.bodyRequest;\n    }\n    sortByFloorDescending(arr) {\n      return arr.sort((a, b) => (b.CFloor ?? 0) - (a.CFloor ?? 0));\n    }\n    getHouseList() {\n      return this._houseService.apiHouseGetHouseListPost$Json({\n        body: this.formatQuery()\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.houseList = res.Entries;\n          this.totalRecords = res.TotalItems;\n        }\n      }));\n    }\n    onSelectionChangeBuildCase() {\n      this.getListBuilding();\n      this.getListHouseHold();\n      this.getHouseList().subscribe();\n    }\n    getListBuildCase() {\n      this._buildCaseService.apiBuildCaseGetAllBuildCasePost$Json({\n        body: {\n          CIsPagi: false,\n          CStatus: 1\n        }\n      }).pipe(tap(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.userBuildCaseOptions = res.Entries?.length ? res.Entries.map(res => {\n            return {\n              CBuildCaseName: res.CBuildCaseName,\n              cID: res.cID\n            };\n          }) : [];\n          if (this.tempBuildCaseID != -1) {\n            let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseID);\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index];\n          } else {\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0];\n          }\n        }\n      }), tap(() => {\n        this.getListBuilding();\n        this.getListHouseHold();\n      }), concatMap(() => this.getHouseList())).subscribe();\n    }\n    getListBuilding() {\n      this._houseService.apiHouseGetListBuildingPost$Json({\n        body: {\n          CBuildCaseID: this.searchQuery.CBuildCaseSelected.cID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          this.buildingSelectedOptions = [{\n            value: '',\n            label: '全部'\n          }, ...res.Entries.map(e => {\n            return {\n              value: e,\n              label: e\n            };\n          })];\n          this.searchQuery.CBuildingNameSelected = this.buildingSelectedOptions[0];\n        }\n      });\n    }\n    getHouseById(CID, ref) {\n      this.detailSelected = {};\n      this._houseService.apiHouseGetHouseByIdPost$Json({\n        body: {\n          CHouseID: CID\n        }\n      }).subscribe(res => {\n        if (res.Entries && res.StatusCode == 0) {\n          console.log(res.Entries);\n          this.houseDetail = {\n            ...res.Entries,\n            changeStartDate: res.Entries.CChangeStartDate ? new Date(res.Entries.CChangeStartDate) : undefined,\n            changeEndDate: res.Entries.CChangeEndDate ? new Date(res.Entries.CChangeEndDate) : undefined\n          };\n          if (res.Entries.CBuildCaseId) {\n            this.detailSelected.CBuildCaseSelected = this.findItemInArray(this.userBuildCaseOptions, 'cID', res.Entries.CBuildCaseId);\n          }\n          this.detailSelected.CPayStatusSelected = this.findItemInArray(this.options.payStatusOptions, 'value', res.Entries.CPayStatus);\n          if (res.Entries.CHouseType) {\n            this.detailSelected.CHouseTypeSelected = this.findItemInArray(this.options.houseTypeOptions, 'value', res.Entries.CHouseType);\n          } else {\n            this.detailSelected.CHouseTypeSelected = this.options.houseTypeOptions[1];\n          }\n          this.detailSelected.CProgressSelected = this.findItemInArray(this.options.progressOptions, 'value', res.Entries.CProgress);\n          if (res.Entries.CBuildCaseId) {\n            if (this.houseHoldMain) {\n              this.houseHoldMain.CBuildCaseID = res.Entries.CBuildCaseId;\n            }\n          }\n          this.dialogService.open(ref);\n        }\n      });\n    }\n    findItemInArray(array, key, value) {\n      return array.find(item => item[key] === value);\n    }\n    openModelDetail(ref, item) {\n      this.getHouseById(item.CID, ref);\n    }\n    openModel(ref) {\n      this.houseHoldMain = {\n        CBuildingName: '',\n        CFloor: undefined,\n        CHouseHoldCount: undefined\n      };\n      this.dialogService.open(ref);\n    }\n    formatDate(CChangeDate) {\n      if (CChangeDate) {\n        return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n      }\n      return '';\n    }\n    onSubmitDetail(ref) {\n      this.houseDetail.CChangeStartDate = this.houseDetail.changeStartDate ? this.formatDate(this.houseDetail.changeStartDate) : '', this.houseDetail.CChangeEndDate = this.houseDetail.changeEndDate ? this.formatDate(this.houseDetail.changeEndDate) : '', this.editHouseArgsParam = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.detailSelected.CHouseTypeSelected ? this.detailSelected.CHouseTypeSelected.value : null,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.detailSelected.CPayStatusSelected ? this.detailSelected.CPayStatusSelected.value : null,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.detailSelected.CProgressSelected ? this.detailSelected.CProgressSelected.value : null,\n        CChangeStartDate: this.houseDetail.CChangeStartDate,\n        CChangeEndDate: this.houseDetail.CChangeEndDate\n      };\n      this.validation();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseService.apiHouseEditHousePost$Json({\n        body: this.editHouseArgsParam\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        } else {\n          this.message.showErrorMSG(res.Message);\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    }\n    onSubmit(ref) {\n      let bodyReq = {\n        CCustomerName: this.houseDetail.CCustomerName,\n        CHouseHold: this.houseDetail.CHousehold,\n        CHouseID: this.houseDetail.CId,\n        CHouseType: this.houseDetail.CHouseType,\n        CIsChange: this.houseDetail.CIsChange,\n        CIsEnable: this.houseDetail.CIsEnable,\n        CMail: this.houseDetail.CMail,\n        CNationalID: this.houseDetail.CNationalId,\n        CPayStatus: this.houseDetail.CPayStatus,\n        CPhone: this.houseDetail.CPhone,\n        CProgress: this.houseDetail.CProgress\n      };\n      this._houseService.apiHouseEditHousePost$Json({\n        body: bodyReq\n      }).subscribe(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      });\n    }\n    onClose(ref) {\n      ref.close();\n    }\n    onNavidateId(type, id) {\n      const idURL = id ? id : this.searchQuery.CBuildCaseSelected.cID;\n      this.router.navigate([`/pages/household-management/${type}`, idURL]);\n    }\n    onNavidateBuildCaseIdHouseId(type, buildCaseId, houseId) {\n      this.router.navigate([`/pages/household-management/${type}`, buildCaseId, houseId]);\n    }\n    validation() {\n      this.valid.clear();\n      this.valid.required('[建案名稱]', this.houseDetail.CId);\n      this.valid.required('[戶型名稱]', this.editHouseArgsParam.CHouseHold);\n      this.valid.isStringMaxLength('[棟別]', this.editHouseArgsParam.CHouseHold, 50);\n      this.valid.required('[樓層]', this.houseDetail.CFloor);\n      this.valid.isStringMaxLength('[客戶姓名]', this.editHouseArgsParam.CCustomerName, 50);\n      if (this.editHouseArgsParam.CNationalID) {\n        this.valid.CheckTaiwanID(this.editHouseArgsParam.CNationalID);\n      }\n      this.valid.pattern('[電子郵件]', this.editHouseArgsParam.CMail, this.pettern.MailPettern);\n      this.valid.isPhoneNumber('[聯絡電話]', this.editHouseArgsParam.CPhone);\n      this.valid.required('[進度]', this.editHouseArgsParam.CProgress);\n      this.valid.required('[戶別類型]', this.detailSelected.CHouseTypeSelected.value);\n      this.valid.required('[付款狀態]', this.detailSelected.CPayStatusSelected.value);\n      if (this.houseDetail.CChangeStartDate) {\n        this.valid.required('[客變結束日期]', this.houseDetail.CChangeEndDate);\n      }\n      if (this.houseDetail.CChangeEndDate) {\n        this.valid.required('[客變開始日期]', this.houseDetail.CChangeStartDate);\n      }\n      this.valid.checkStartBeforeEnd('[開放日期]', this.houseDetail.CChangeStartDate ? this.houseDetail.CChangeStartDate : '', this.houseDetail.CChangeEndDate ? this.houseDetail.CChangeEndDate : '');\n    }\n    validationHouseHoldMain() {\n      this.valid.clear();\n      this.valid.required('[建案]', this.houseHoldMain.CBuildCaseID);\n      this.valid.required('[棟別]', this.houseHoldMain.CBuildingName);\n      this.valid.isStringMaxLength('[棟別]', this.houseHoldMain.CBuildingName, 10);\n      this.valid.isNaturalNumberInRange('[當層最多戶數]', this.houseHoldMain.CFloor, 1, 100);\n      this.valid.isNaturalNumberInRange('[本棟總樓層]', this.houseHoldMain.CHouseHoldCount, 1, 100);\n    }\n    addHouseHoldMain(ref) {\n      this.houseHoldMain.CBuildCaseID = this.searchQuery.CBuildCaseSelected.cID, this.validationHouseHoldMain();\n      if (this.valid.errorMessages.length > 0) {\n        this.message.showErrorMSGs(this.valid.errorMessages);\n        return;\n      }\n      this._houseHoldMainService.apiHouseHoldMainAddHouseHoldMainPost$Json({\n        body: this.houseHoldMain\n      }).pipe(tap(res => {\n        if (res.StatusCode === 0) {\n          this.message.showSucessMSG(\"執行成功\");\n          ref.close();\n        }\n      }), concatMap(() => this.getHouseList())).subscribe();\n    }\n    static {\n      this.ɵfac = function HouseholdManagementComponent_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdManagementComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.EnumHelper), i0.ɵɵdirectiveInject(i3.NbDialogService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper), i0.ɵɵdirectiveInject(i6.HouseService), i0.ɵɵdirectiveInject(i6.HouseHoldMainService), i0.ɵɵdirectiveInject(i6.BuildCaseService), i0.ɵɵdirectiveInject(i7.PetternHelper), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i9.EventService), i0.ɵɵdirectiveInject(i10.UtilityService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HouseholdManagementComponent,\n        selectors: [[\"ngx-household-management\"]],\n        viewQuery: function HouseholdManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          }\n        },\n        standalone: true,\n        features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n        decls: 113,\n        vars: 23,\n        consts: [[\"fileInput\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialogHouseholdMain\", \"\"], [\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"cHouseType\", 1, \"label\", \"col-3\"], [1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"type\", \"text\", \"id\", \"CFrom\", \"nbInput\", \"\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloorTo\", 1, \"label\", \"col-1\"], [1, \"mr-3\"], [\"type\", \"text\", \"id\", \"CTo\", \"nbInput\", \"\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u6236\\u578B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7E73\\u6B3E\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u7C3D\\u56DE\\u72C0\\u614B\", 1, \"col-9\", 3, \"ngModelChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"col-md-12\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [\"type\", \"file\", \"accept\", \".xlsx, .xls\", 2, \"display\", \"none\", 3, \"change\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"table-striped\", \"border\", 2, \"min-width\", \"1000px\", \"background-color\", \"#f3f3f3\"], [2, \"background-color\", \"#27ae60\", \"color\", \"white\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-center\"], [\"aria-label\", \"Pagination\", 3, \"pageChange\", \"page\", \"pageSize\", \"collectionSize\"], [3, \"value\"], [1, \"text-center\", \"w-32\", \"px-0\"], [\"class\", \"btn btn-outline-success btn-sm text-left m-[2px]\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-success btn-sm m-[2px]\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"m-[2px]\", 3, \"click\"], [1, \"btn\", \"btn-outline-success\", \"btn-sm\", \"text-left\", \"m-[2px]\", 3, \"click\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [\"class\", \"px-4\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", \"m-2\", \"px-8\", 3, \"click\"], [\"class\", \"btn btn-primary m-2 bg-[#169BD5] px-8\", 3, \"click\", 4, \"ngIf\"], [1, \"px-4\"], [1, \"form-group\"], [\"for\", \"cBuildCaseId\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u5EFA\\u6848\\u540D\\u7A31\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHousehold\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u6236\\u578B\\u540D\\u7A31\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u6A13\\u5C64\", \"min\", \"1\", \"max\", \"100\", \"disabled\", \"true\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cCustomerName\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u5BA2\\u6236\\u59D3\\u540D\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cNationalId\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u8EAB\\u5206\\u8B49\\u5B57\\u865F\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cMail\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u96FB\\u5B50\\u90F5\\u4EF6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPhone\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u806F\\u7D61\\u96FB\\u8A71\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cHouseType\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u6236\\u5225\\u985E\\u578B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cPayStatus\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u4ED8\\u6B3E\\u72C0\\u614B\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cProgress\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"placeholder\", \"\\u9032\\u5EA6\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"cIsChange\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [\"status\", \"basic\", 3, \"checkedChange\", \"checked\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", 2, \"min-width\", \"75px\"], [1, \"form-group\", \"flex\", \"flex-row\"], [\"for\", \"cIsEnable\", \"baseLabel\", \"\", 1, \"mr-4\", \"content-center\", 2, \"min-width\", \"75px\"], [1, \"max-w-xs\", \"flex\", \"flex-row\"], [1, \"w-1/2\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"mr-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"yyyy-mm-dd\", 1, \"w-[42%]\", \"ml-2\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"m-2\", \"bg-[#169BD5]\", \"px-8\", 3, \"click\"], [\"for\", \"cBuildingName\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"text\", \"nbInput\", \"\", \"placeholder\", \"\\u68DF\\u5225\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CHouseHoldCount\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u7576\\u5C64\\u6700\\u591A\\u6236\\u6578\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"CFloor\", \"baseLabel\", \"\", 1, \"required-field\", \"mr-4\", 2, \"min-width\", \"75px\"], [\"type\", \"number\", \"nbInput\", \"\", \"placeholder\", \"\\u672C\\u68DF\\u7E3D\\u6A13\\u5C64\", 1, \"w-full\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"mr-4\", 3, \"click\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n        template: function HouseholdManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"nb-card\", 5)(1, \"nb-card-header\");\n            i0.ɵɵelement(2, \"ngx-breadcrumb\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 6);\n            i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"label\", 10);\n            i0.ɵɵtext(10, \"\\u5EFA\\u6848\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"nb-select\", 11);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_11_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"selectedChange\", function HouseholdManagementComponent_Template_nb_select_selectedChange_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSelectionChangeBuildCase());\n            });\n            i0.ɵɵtemplate(12, HouseholdManagementComponent_nb_option_12_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"label\", 13);\n            i0.ɵɵtext(16, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"nb-select\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_17_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseTypeSelected, $event) || (ctx.searchQuery.CHouseTypeSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(18, HouseholdManagementComponent_nb_option_18_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 9)(21, \"label\", 10);\n            i0.ɵɵtext(22, \" \\u68DF\\u5225 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"nb-select\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_23_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildingNameSelected, $event) || (ctx.searchQuery.CBuildingNameSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(24, HouseholdManagementComponent_nb_option_24_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"div\", 8)(26, \"div\", 15)(27, \"label\", 16);\n            i0.ɵɵtext(28, \"\\u6A13 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"nb-form-field\", 17)(30, \"input\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_30_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CFrom, $event) || (ctx.searchQuery.CFrom = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"label\", 19);\n            i0.ɵɵtext(32, \"~ \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"nb-form-field\", 20)(34, \"input\", 21);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_input_ngModelChange_34_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CTo, $event) || (ctx.searchQuery.CTo = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(35, \"div\", 8)(36, \"div\", 9)(37, \"label\", 22);\n            i0.ɵɵtext(38, \" \\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"nb-select\", 23);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_39_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CHouseHoldSelected, $event) || (ctx.searchQuery.CHouseHoldSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(40, HouseholdManagementComponent_nb_option_40_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 8)(42, \"div\", 9)(43, \"label\", 24);\n            i0.ɵɵtext(44, \" \\u7E73\\u6B3E\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"nb-select\", 25);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_45_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CPayStatusSelected, $event) || (ctx.searchQuery.CPayStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(46, HouseholdManagementComponent_nb_option_46_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(47, \"div\", 8)(48, \"div\", 9)(49, \"label\", 26);\n            i0.ɵɵtext(50, \" \\u9032\\u5EA6 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"nb-select\", 27);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_51_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CProgressSelected, $event) || (ctx.searchQuery.CProgressSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(52, HouseholdManagementComponent_nb_option_52_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(53, \"div\", 8)(54, \"div\", 9)(55, \"label\", 28);\n            i0.ɵɵtext(56, \" \\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"nb-select\", 29);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_57_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CIsEnableSeleted, $event) || (ctx.searchQuery.CIsEnableSeleted = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(58, HouseholdManagementComponent_nb_option_58_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"div\", 8)(60, \"div\", 9)(61, \"label\", 30);\n            i0.ɵɵtext(62, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"nb-select\", 31);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function HouseholdManagementComponent_Template_nb_select_ngModelChange_63_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CSignStatusSelected, $event) || (ctx.searchQuery.CSignStatusSelected = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵtemplate(64, HouseholdManagementComponent_nb_option_64_Template, 2, 2, \"nb-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(65, \"div\", 8)(66, \"div\", 32)(67, \"button\", 33);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_67_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearch());\n            });\n            i0.ɵɵtext(68, \" \\u67E5\\u8A62 \");\n            i0.ɵɵelement(69, \"i\", 34);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(70, \"div\", 35)(71, \"div\", 32);\n            i0.ɵɵtemplate(72, HouseholdManagementComponent_button_72_Template, 2, 0, \"button\", 36);\n            i0.ɵɵelementStart(73, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_73_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onNavidateId(\"modify-floor-plan\"));\n            });\n            i0.ɵɵtext(74, \" \\u4FEE\\u6539\\u6A13\\u5C64\\u6236\\u578B \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_75_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.exportHouse());\n            });\n            i0.ɵɵtext(76, \" \\u532F\\u51FA\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(77, \"input\", 38, 0);\n            i0.ɵɵlistener(\"change\", function HouseholdManagementComponent_Template_input_change_77_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelected($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"button\", 39);\n            i0.ɵɵlistener(\"click\", function HouseholdManagementComponent_Template_button_click_79_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.triggerFileInput());\n            });\n            i0.ɵɵtext(80, \" \\u532F\\u5165\\u6236\\u5225\\u660E\\u7D30\\u6A94 \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(81, \"div\", 40)(82, \"table\", 41)(83, \"thead\")(84, \"tr\", 42)(85, \"th\", 43);\n            i0.ɵɵtext(86, \"\\u68DF\\u5225\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"th\", 43);\n            i0.ɵɵtext(88, \"\\u6236\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(89, \"th\", 43);\n            i0.ɵɵtext(90, \"\\u6A13\\u5C64\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"th\", 43);\n            i0.ɵɵtext(92, \"\\u5730\\u4E3B\\u6236\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"th\", 43);\n            i0.ɵɵtext(94, \"\\u985E\\u578B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(95, \"th\", 43);\n            i0.ɵɵtext(96, \"\\u9032\\u5EA6\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(97, \"th\", 43);\n            i0.ɵɵtext(98, \"\\u7E73\\u6B3E\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(99, \"th\", 43);\n            i0.ɵɵtext(100, \"\\u7C3D\\u56DE\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(101, \"th\", 43);\n            i0.ɵɵtext(102, \"\\u72C0\\u614B\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(103, \"th\", 44);\n            i0.ɵɵtext(104, \"\\u64CD\\u4F5C\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(105, \"tbody\");\n            i0.ɵɵtemplate(106, HouseholdManagementComponent_tr_106_Template, 26, 14, \"tr\", 45);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(107, \"nb-card-footer\", 46)(108, \"ngb-pagination\", 47);\n            i0.ɵɵtwoWayListener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_108_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"pageChange\", function HouseholdManagementComponent_Template_ngb_pagination_pageChange_108_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.pageChanged($event));\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(109, HouseholdManagementComponent_ng_template_109_Template, 6, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(111, HouseholdManagementComponent_ng_template_111_Template, 20, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseTypeSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseTypeOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildingNameSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.buildingSelectedOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CFrom);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CTo);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CHouseHoldSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseHoldOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CPayStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.payStatusOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CProgressSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.progressOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CIsEnableSeleted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.cIsEnableOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CSignStatusSelected);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.signStatusOptions);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n            i0.ɵɵadvance(34);\n            i0.ɵɵproperty(\"ngForOf\", ctx.houseList);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtwoWayProperty(\"page\", ctx.pageIndex);\n            i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"collectionSize\", ctx.totalRecords);\n          }\n        },\n        dependencies: [CommonModule, i11.NgForOf, i11.NgIf, SharedModule, i12.DefaultValueAccessor, i12.NumberValueAccessor, i12.NgControlStatus, i12.MinValidator, i12.MaxValidator, i12.NgModel, i3.NbCardComponent, i3.NbCardBodyComponent, i3.NbCardFooterComponent, i3.NbCardHeaderComponent, i3.NbCheckboxComponent, i3.NbInputDirective, i3.NbSelectComponent, i3.NbOptionComponent, i3.NbFormFieldComponent, i3.NbPrefixDirective, i3.NbIconComponent, i3.NbDatepickerDirective, i3.NbDatepickerComponent, i13.NgbPagination, i14.BreadcrumbComponent, i15.BaseLabelDirective, NbDatepickerModule, NbDateFnsDateModule]\n      });\n    }\n  }\n  return HouseholdManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}