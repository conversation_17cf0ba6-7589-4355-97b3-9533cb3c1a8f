{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DestroyRef, inject } from '@angular/core';\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport listPlugin from '@fullcalendar/list';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport * as moment from 'moment';\nimport { concatMap, tap } from 'rxjs';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"src/services/api/services\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@nebular/theme\";\nimport * as i6 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i7 from \"@fullcalendar/angular\";\nfunction CalendarComponent_nb_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", item_r1.cID);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r1.CBuildCaseName);\n  }\n}\nexport class CalendarComponent extends BaseComponent {\n  constructor(allow, buildCaseService, preOrderSettingService) {\n    super(allow);\n    this.allow = allow;\n    this.buildCaseService = buildCaseService;\n    this.preOrderSettingService = preOrderSettingService;\n    this.buildCaseList = [];\n    this.events = [];\n    this.listPreOrder = [];\n    this.calendarOptions = {\n      initialView: 'timeGridWeek',\n      plugins: [dayGridPlugin, timeGridPlugin, listPlugin],\n      headerToolbar: {\n        left: 'prev,next today',\n        center: 'title',\n        right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\n      },\n      views: {\n        timeGrid: {\n          allDaySlot: false // Hide all-day line in time grid view,\n        }\n      },\n      height: 'auto',\n      slotMaxTime: '22:00:00',\n      slotMinTime: '09:00:00',\n      datesSet: agr => {\n        if (this.dateStart !== agr.start || this.dateEnd !== agr.end) {\n          this.dateStart = agr.start;\n          this.dateEnd = agr.end;\n          this.getListPreOrder(agr.start, agr.end).subscribe();\n        }\n      }\n    };\n    this.destroy = inject(DestroyRef);\n  }\n  ngOnInit() {\n    this.initialList();\n  }\n  initialList() {\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(takeUntilDestroyed(this.destroy)).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.buildCaseList = res.Entries ?? [];\n        this.currentBuildCase = this.buildCaseList[0]?.cID;\n      }\n    }), concatMap(() => this.getListPreOrder(this.dateStart, this.dateEnd))).subscribe();\n  }\n  getListPreOrder(dateStart, dateEnd) {\n    return this.preOrderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\n      body: {\n        CBuildCaseID: this.currentBuildCase,\n        CDateStart: dateStart.toISOString(),\n        CDateEnd: dateEnd.toISOString()\n      }\n    }).pipe(tap(res => {\n      if (res.StatusCode == 0) {\n        this.listPreOrder = res.Entries;\n        this.initEvent();\n      }\n    }));\n  }\n  initEvent() {\n    this.events = (this.listPreOrder ?? []).map(i => {\n      return {\n        title: i.CStatus ? `${i.CHouseHoldName ?? \"\"}${i.CFloor ? \"-\" + i.CFloor + 'F' : \"\"}\\n${i.CCustomerName ?? \"\"} ${i.CPeoples}P` : '',\n        start: moment(i.CDate).hour(i.CHour ?? 0).toISOString(),\n        end: moment(i.CDate).hour(i.CHour ?? 0).add(1, 'hours').toISOString(),\n        color: i.CStatus ? '#008080' : '#81d3f8',\n        backgroundColor: i.CStatus ? '#008080' : '#81d3f8',\n        display: i.CStatus ? undefined : 'background'\n      };\n    });\n    this.calendarOptions = {\n      ...this.calendarOptions,\n      // Spread the existing options to avoid mutation\n      events: this.events // Update events property with new data\n    };\n  }\n  changeBuildCase(buildCaseId) {\n    this.currentBuildCase = buildCaseId;\n    this.getListPreOrder(this.dateStart, this.dateEnd).subscribe();\n  }\n  static {\n    this.ɵfac = function CalendarComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CalendarComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.BuildCaseService), i0.ɵɵdirectiveInject(i2.PreOrderSettingService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CalendarComponent,\n      selectors: [[\"app-calendar\"]],\n      viewQuery: function CalendarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(FullCalendarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 3,\n      consts: [[\"accent\", \"success\"], [1, \"row\"], [1, \"col-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"project\", 1, \"text-nowrap\", \"m-0\", \"mr-2\", \"col-4\"], [\"fullWidth\", \"\", 3, \"selectedChange\", \"ngModelChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"mb-4\"], [1, \"d-flex\"], [1, \"slot-description\", \"unavailable\"], [1, \"slot-description\", \"available\"], [1, \"slot-description\", \"reserved\"], [1, \"col-12\"], [3, \"options\"], [3, \"value\"]],\n      template: function CalendarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nb-card\", 0)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 1)(5, \"div\", 2)(6, \"div\", 3)(7, \"label\", 4);\n          i0.ɵɵtext(8, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"nb-select\", 5);\n          i0.ɵɵlistener(\"selectedChange\", function CalendarComponent_Template_nb_select_selectedChange_9_listener($event) {\n            return ctx.changeBuildCase($event);\n          });\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_Template_nb_select_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.currentBuildCase, $event) || (ctx.currentBuildCase = $event);\n            return $event;\n          });\n          i0.ɵɵtemplate(10, CalendarComponent_nb_option_10_Template, 2, 2, \"nb-option\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9);\n          i0.ɵɵtext(14, \"\\u672A\\u958B\\u653E\\u9810\\u7D04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 10);\n          i0.ɵɵtext(16, \"\\u5DF2\\u958B\\u653E\\u9810\\u7D04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 11);\n          i0.ɵɵtext(18, \"\\u5DF2\\u88AB\\u9810\\u7D04\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 12);\n          i0.ɵɵelement(20, \"full-calendar\", 13);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.currentBuildCase);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildCaseList);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"options\", ctx.calendarOptions);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, SharedModule, i4.NgControlStatus, i4.NgModel, i5.NbCardComponent, i5.NbCardBodyComponent, i5.NbCardHeaderComponent, i5.NbSelectComponent, i5.NbOptionComponent, i6.BreadcrumbComponent, FullCalendarModule, i7.FullCalendarComponent],\n      styles: [\".unavailable[_ngcontent-%COMP%] {\\n  background-color: rgb(242, 242, 242);\\n}\\n\\n.available[_ngcontent-%COMP%] {\\n  background-color: rgb(129, 211, 248);\\n}\\n\\n.reserved[_ngcontent-%COMP%] {\\n  background-color: rgb(0, 128, 128);\\n}\\n\\n.slot-description[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0.75rem;\\n  margin-right: 1rem;\\n  border-radius: 0.25rem;\\n}\\n\\n.fc-time-grid-week[_ngcontent-%COMP%]   .fc-time-grid-slot[_ngcontent-%COMP%] {\\n  background-color: #f0f0f0; \\n\\n}\\n\\n .fc-day .fc-timegrid-col-frame {\\n  background-color: rgb(242, 242, 242);\\n}\\n\\n .fc .fc-button {\\n  background-color: #008cff; \\n\\n  color: #fff; \\n\\n  border: none;\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.25rem;\\n  cursor: pointer;\\n}\\n .fc .fc-button:hover {\\n  background-color: #0056b3;\\n}\\n .fc .fc-button.fc-button-active {\\n  background-color: #0056b3;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImNhbGVuZGFyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usb0NBQUE7QUFDRjs7QUFDQTtFQUNFLG9DQUFBO0FBRUY7O0FBQUE7RUFDRSxrQ0FBQTtBQUdGOztBQURBO0VBQ0UsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0FBSUY7O0FBRkE7RUFDRSx5QkFBQSxFQUFBLDREQUFBO0FBS0Y7O0FBRkU7RUFDRSxvQ0FBQTtBQUtKOztBQUZBO0VBQ0UseUJBQUEsRUFBQSxxQ0FBQTtFQUNBLFdBQUEsRUFBQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxvQkFBQTtFQUNBLGVBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7QUFLRjtBQUpFO0VBQ0UseUJBQUE7QUFNSjtBQUpFO0VBQ0UseUJBQUE7QUFNSiIsImZpbGUiOiJjYWxlbmRhci5jb21wb25lbnQuc2NzcyIsInNvdXJjZXNDb250ZW50IjpbIi51bmF2YWlsYWJsZSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNDIsIDI0MiwgMjQyLCAxKTtcclxufVxyXG4uYXZhaWxhYmxlIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEyOSwgMjExLCAyNDgsIDEpO1xyXG59XHJcbi5yZXNlcnZlZCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAxMjgsIDEyOCwgMSk7XHJcbn1cclxuLnNsb3QtZGVzY3JpcHRpb24ge1xyXG4gIHBhZGRpbmc6IDAuNXJlbSAwLjc1cmVtO1xyXG4gIG1hcmdpbi1yaWdodDogMXJlbTtcclxuICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xyXG59XHJcbi5mYy10aW1lLWdyaWQtd2VlayAuZmMtdGltZS1ncmlkLXNsb3Qge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmMGYwZjA7IC8qIGNoYW5nZSB0aGUgYmFja2dyb3VuZCBjb2xvciB0byBsaWdodCBncmF5IGZvciB3ZWVrIHZpZXcgKi9cclxufVxyXG46Om5nLWRlZXAuZmMtZGF5IHtcclxuICAuZmMtdGltZWdyaWQtY29sLWZyYW1lIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQyLCAyNDIsIDI0MiwgMSk7XHJcbiAgfVxyXG59XHJcbjo6bmctZGVlcC5mYyAuZmMtYnV0dG9uIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA4Y2ZmOyAvKiBTZXQgdGhlIGRlc2lyZWQgYmFja2dyb3VuZCBjb2xvciAqL1xyXG4gIGNvbG9yOiAjZmZmOyAvKiBTZXQgdGhlIHRleHQgY29sb3IgKi9cclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogMC41cmVtIDFyZW07XHJcbiAgZm9udC1zaXplOiAxcmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICY6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzAwNTZiMztcclxuICB9XHJcbiAgJi5mYy1idXR0b24tYWN0aXZlIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDU2YjM7XHJcbiAgfVxyXG59XHJcbiJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcmVzZXJ2YXRpb24tdGltZS1tYW5hZ2VtZW50L2NhbGVuZGFyL2NhbGVuZGFyLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usb0NBQUE7QUFDRjs7QUFDQTtFQUNFLG9DQUFBO0FBRUY7O0FBQUE7RUFDRSxrQ0FBQTtBQUdGOztBQURBO0VBQ0UsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLHNCQUFBO0FBSUY7O0FBRkE7RUFDRSx5QkFBQSxFQUFBLDREQUFBO0FBS0Y7O0FBRkU7RUFDRSxvQ0FBQTtBQUtKOztBQUZBO0VBQ0UseUJBQUEsRUFBQSxxQ0FBQTtFQUNBLFdBQUEsRUFBQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxvQkFBQTtFQUNBLGVBQUE7RUFDQSxzQkFBQTtFQUNBLGVBQUE7QUFLRjtBQUpFO0VBQ0UseUJBQUE7QUFNSjtBQUpFO0VBQ0UseUJBQUE7QUFNSjtBQUNBLG80REFBbzREIiwic291cmNlc0NvbnRlbnQiOlsiLnVuYXZhaWxhYmxlIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0MiwgMjQyLCAyNDIsIDEpO1xyXG59XHJcbi5hdmFpbGFibGUge1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTI5LCAyMTEsIDI0OCwgMSk7XHJcbn1cclxuLnJlc2VydmVkIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDEyOCwgMTI4LCAxKTtcclxufVxyXG4uc2xvdC1kZXNjcmlwdGlvbiB7XHJcbiAgcGFkZGluZzogMC41cmVtIDAuNzVyZW07XHJcbiAgbWFyZ2luLXJpZ2h0OiAxcmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDAuMjVyZW07XHJcbn1cclxuLmZjLXRpbWUtZ3JpZC13ZWVrIC5mYy10aW1lLWdyaWQtc2xvdCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjBmMDsgLyogY2hhbmdlIHRoZSBiYWNrZ3JvdW5kIGNvbG9yIHRvIGxpZ2h0IGdyYXkgZm9yIHdlZWsgdmlldyAqL1xyXG59XHJcbjo6bmctZGVlcC5mYy1kYXkge1xyXG4gIC5mYy10aW1lZ3JpZC1jb2wtZnJhbWUge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNDIsIDI0MiwgMjQyLCAxKTtcclxuICB9XHJcbn1cclxuOjpuZy1kZWVwLmZjIC5mYy1idXR0b24ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDhjZmY7IC8qIFNldCB0aGUgZGVzaXJlZCBiYWNrZ3JvdW5kIGNvbG9yICovXHJcbiAgY29sb3I6ICNmZmY7IC8qIFNldCB0aGUgdGV4dCBjb2xvciAqL1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICBmb250LXNpemU6IDFyZW07XHJcbiAgYm9yZGVyLXJhZGl1czogMC4yNXJlbTtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgJjpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA1NmIzO1xyXG4gIH1cclxuICAmLmZjLWJ1dHRvbi1hY3RpdmUge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzAwNTZiMztcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "DestroyRef", "inject", "FullCalendarComponent", "FullCalendarModule", "dayGridPlugin", "timeGridPlugin", "listPlugin", "takeUntilDestroyed", "moment", "concatMap", "tap", "SharedModule", "BaseComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r1", "cID", "ɵɵadvance", "ɵɵtextInterpolate", "CBuildCaseName", "CalendarComponent", "constructor", "allow", "buildCaseService", "preOrderSettingService", "buildCaseList", "events", "listPreOrder", "calendarOptions", "initialView", "plugins", "headerToolbar", "left", "center", "right", "views", "timeGrid", "allDaySlot", "height", "slotMaxTime", "slotMinTime", "datesSet", "agr", "dateStart", "start", "dateEnd", "end", "getListPreOrder", "subscribe", "destroy", "ngOnInit", "initialList", "apiBuildCaseGetUserBuildCasePost$Json", "pipe", "res", "StatusCode", "Entries", "currentBuildCase", "apiPreOrderSettingGetPreOrderSettingPost$Json", "body", "CBuildCaseID", "CDateStart", "toISOString", "CDateEnd", "initEvent", "map", "i", "title", "CStatus", "CHouseHoldName", "CFloor", "CCustomerName", "CPeoples", "CDate", "hour", "CHour", "add", "color", "backgroundColor", "display", "undefined", "changeBuildCase", "buildCaseId", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "BuildCaseService", "PreOrderSettingService", "selectors", "viewQuery", "CalendarComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "CalendarComponent_Template_nb_select_selectedChange_9_listener", "$event", "ɵɵtwoWayListener", "CalendarComponent_Template_nb_select_ngModelChange_9_listener", "ɵɵtwoWayBindingSet", "ɵɵtemplate", "CalendarComponent_nb_option_10_Template", "ɵɵtwoWayProperty", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i4", "NgControlStatus", "NgModel", "i5", "NbCardComponent", "NbCardBodyComponent", "NbCardHeaderComponent", "NbSelectComponent", "NbOptionComponent", "i6", "BreadcrumbComponent", "i7", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\calendar\\calendar.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\reservation-time-management\\calendar\\calendar.component.html"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, DestroyRef, OnInit, ViewChild, inject, signal } from '@angular/core';\r\nimport { FullCalendarComponent, FullCalendarModule } from '@fullcalendar/angular';\r\nimport { CalendarOptions, EventInput } from '@fullcalendar/core';\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport listPlugin from '@fullcalendar/list';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BuildCaseService, PreOrderSettingService } from 'src/services/api/services';\r\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\r\nimport { BuildCaseGetListReponse, GetPreOrderSettingResponse } from 'src/services/api/models';\r\nimport * as moment from 'moment';\r\nimport { concatMap, tap } from 'rxjs';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\n\r\n@Component({\r\n  selector: 'app-calendar',\r\n  templateUrl: './calendar.component.html',\r\n  styleUrls: ['./calendar.component.scss'],\r\n  standalone: true,\r\n  imports: [CommonModule, SharedModule, FullCalendarModule],\r\n})\r\nexport class CalendarComponent extends BaseComponent implements OnInit {\r\n  currentBuildCase?: number;\r\n  buildCaseList: BuildCaseGetListReponse[] = [];\r\n  events: EventInput[] = [];\r\n\r\n  dateStart: Date;\r\n  dateEnd: Date\r\n\r\n  listPreOrder: GetPreOrderSettingResponse[] = []\r\n\r\n  calendarOptions: CalendarOptions = {\r\n    initialView: 'timeGridWeek',\r\n    plugins: [dayGridPlugin, timeGridPlugin, listPlugin],\r\n    headerToolbar: {\r\n      left: 'prev,next today',\r\n      center: 'title',\r\n      right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek',\r\n    },\r\n    views: {\r\n      timeGrid: {\r\n        allDaySlot: false, // Hide all-day line in time grid view,\r\n      },\r\n    },\r\n    height: 'auto',\r\n    slotMaxTime: '22:00:00',\r\n    slotMinTime: '09:00:00',\r\n    datesSet: (agr) => {\r\n      if (this.dateStart !== agr.start || this.dateEnd !== agr.end) {\r\n        this.dateStart = agr.start\r\n        this.dateEnd = agr.end\r\n        this.getListPreOrder(agr.start, agr.end).subscribe()\r\n      }\r\n    }\r\n  };\r\n  destroy = inject(DestroyRef)\r\n  @ViewChild(FullCalendarComponent) calendarComponent: FullCalendarComponent;\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private buildCaseService: BuildCaseService,\r\n    private preOrderSettingService: PreOrderSettingService\r\n  ) {\r\n    super(allow)\r\n  }\r\n  override ngOnInit(): void {\r\n    this.initialList();\r\n  }\r\n\r\n  initialList() {\r\n    this.buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({}).pipe(\r\n      takeUntilDestroyed(this.destroy)\r\n    ).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.buildCaseList = res.Entries ?? [];\r\n          this.currentBuildCase = this.buildCaseList[0]?.cID;\r\n        }\r\n      }),\r\n      concatMap(() => this.getListPreOrder(this.dateStart, this.dateEnd))\r\n    ).subscribe();\r\n  }\r\n\r\n  getListPreOrder(dateStart: Date, dateEnd: Date) {\r\n    return this.preOrderSettingService.apiPreOrderSettingGetPreOrderSettingPost$Json({\r\n      body: {\r\n        CBuildCaseID: this.currentBuildCase,\r\n        CDateStart: dateStart.toISOString(),\r\n        CDateEnd: dateEnd.toISOString()\r\n      }\r\n    }).pipe(\r\n      tap(res => {\r\n        if (res.StatusCode == 0) {\r\n          this.listPreOrder = res.Entries!\r\n          this.initEvent();\r\n        }\r\n      })\r\n    )\r\n  }\r\n\r\n  initEvent() {\r\n    this.events = (this.listPreOrder ?? []).map(i => {\r\n      return {\r\n        title: i.CStatus ? `${i.CHouseHoldName ?? \"\"}${i.CFloor ? \"-\" + i.CFloor + 'F' : \"\"}\\n${i.CCustomerName ?? \"\"} ${i.CPeoples}P` : '',\r\n        start: moment(i.CDate).hour(i.CHour ?? 0).toISOString(),\r\n        end: moment(i.CDate).hour(i.CHour ?? 0).add(1, 'hours').toISOString(),\r\n        color: i.CStatus ? '#008080' : '#81d3f8',\r\n        backgroundColor: i.CStatus ? '#008080' : '#81d3f8',\r\n        display: i.CStatus ? undefined : 'background'\r\n      }\r\n    })\r\n    this.calendarOptions = {\r\n      ...this.calendarOptions,  // Spread the existing options to avoid mutation\r\n      events: this.events    // Update events property with new data\r\n    };\r\n  }\r\n\r\n  changeBuildCase(buildCaseId: number) {\r\n    this.currentBuildCase = buildCaseId;\r\n    this.getListPreOrder(this.dateStart, this.dateEnd).subscribe()\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"row\">\r\n      <div class=\"col-6\">\r\n        <div class=\"form-group d-flex align-items-center\">\r\n          <label for=\"project\" class=\"text-nowrap m-0 mr-2 col-4\">建案</label>\r\n          <nb-select (selectedChange)=\"changeBuildCase($event)\" [(ngModel)]=\"currentBuildCase\" fullWidth>\r\n            <nb-option *ngFor=\"let item of buildCaseList\" [value]=\"item.cID\">{{\r\n              item.CBuildCaseName\r\n              }}</nb-option>\r\n          </nb-select>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-12 mb-4\">\r\n        <div class=\"d-flex\">\r\n          <div class=\"slot-description unavailable\">未開放預約</div>\r\n          <div class=\"slot-description available\">已開放預約</div>\r\n          <div class=\"slot-description reserved\">已被預約</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-12\">\r\n        <full-calendar [options]=\"calendarOptions\"></full-calendar>\r\n      </div>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAoBC,UAAU,EAAqBC,MAAM,QAAgB,eAAe;AACxF,SAASC,qBAAqB,EAAEC,kBAAkB,QAAQ,uBAAuB;AAEjF,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAG3C,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AACrC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;;;;;;;;;;;ICJvDC,EAAA,CAAAC,cAAA,oBAAiE;IAAAD,EAAA,CAAAE,MAAA,GAE7D;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,GAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,EAE7D;IAF6DP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,cAAA,CAE7D;;;ADWhB,OAAM,MAAOC,iBAAkB,SAAQX,aAAa;EAoClDY,YACqBC,KAAkB,EAC7BC,gBAAkC,EAClCC,sBAA8C;IAEtD,KAAK,CAACF,KAAK,CAAC;IAJO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IArChC,KAAAC,aAAa,GAA8B,EAAE;IAC7C,KAAAC,MAAM,GAAiB,EAAE;IAKzB,KAAAC,YAAY,GAAiC,EAAE;IAE/C,KAAAC,eAAe,GAAoB;MACjCC,WAAW,EAAE,cAAc;MAC3BC,OAAO,EAAE,CAAC7B,aAAa,EAAEC,cAAc,EAAEC,UAAU,CAAC;MACpD4B,aAAa,EAAE;QACbC,IAAI,EAAE,iBAAiB;QACvBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE;OACR;MACDC,KAAK,EAAE;QACLC,QAAQ,EAAE;UACRC,UAAU,EAAE,KAAK,CAAE;;OAEtB;MACDC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,UAAU;MACvBC,QAAQ,EAAGC,GAAG,IAAI;QAChB,IAAI,IAAI,CAACC,SAAS,KAAKD,GAAG,CAACE,KAAK,IAAI,IAAI,CAACC,OAAO,KAAKH,GAAG,CAACI,GAAG,EAAE;UAC5D,IAAI,CAACH,SAAS,GAAGD,GAAG,CAACE,KAAK;UAC1B,IAAI,CAACC,OAAO,GAAGH,GAAG,CAACI,GAAG;UACtB,IAAI,CAACC,eAAe,CAACL,GAAG,CAACE,KAAK,EAAEF,GAAG,CAACI,GAAG,CAAC,CAACE,SAAS,EAAE;QACtD;MACF;KACD;IACD,KAAAC,OAAO,GAAGnD,MAAM,CAACD,UAAU,CAAC;EAQ5B;EACSqD,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC5B,gBAAgB,CAAC6B,qCAAqC,CAAC,EAAE,CAAC,CAACC,IAAI,CAClEjD,kBAAkB,CAAC,IAAI,CAAC6C,OAAO,CAAC,CACjC,CAACI,IAAI,CACJ9C,GAAG,CAAC+C,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC9B,aAAa,GAAG6B,GAAG,CAACE,OAAO,IAAI,EAAE;QACtC,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAChC,aAAa,CAAC,CAAC,CAAC,EAAET,GAAG;MACpD;IACF,CAAC,CAAC,EACFV,SAAS,CAAC,MAAM,IAAI,CAACyC,eAAe,CAAC,IAAI,CAACJ,SAAS,EAAE,IAAI,CAACE,OAAO,CAAC,CAAC,CACpE,CAACG,SAAS,EAAE;EACf;EAEAD,eAAeA,CAACJ,SAAe,EAAEE,OAAa;IAC5C,OAAO,IAAI,CAACrB,sBAAsB,CAACkC,6CAA6C,CAAC;MAC/EC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI,CAACH,gBAAgB;QACnCI,UAAU,EAAElB,SAAS,CAACmB,WAAW,EAAE;QACnCC,QAAQ,EAAElB,OAAO,CAACiB,WAAW;;KAEhC,CAAC,CAACT,IAAI,CACL9C,GAAG,CAAC+C,GAAG,IAAG;MACR,IAAIA,GAAG,CAACC,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5B,YAAY,GAAG2B,GAAG,CAACE,OAAQ;QAChC,IAAI,CAACQ,SAAS,EAAE;MAClB;IACF,CAAC,CAAC,CACH;EACH;EAEAA,SAASA,CAAA;IACP,IAAI,CAACtC,MAAM,GAAG,CAAC,IAAI,CAACC,YAAY,IAAI,EAAE,EAAEsC,GAAG,CAACC,CAAC,IAAG;MAC9C,OAAO;QACLC,KAAK,EAAED,CAAC,CAACE,OAAO,GAAG,GAAGF,CAAC,CAACG,cAAc,IAAI,EAAE,GAAGH,CAAC,CAACI,MAAM,GAAG,GAAG,GAAGJ,CAAC,CAACI,MAAM,GAAG,GAAG,GAAG,EAAE,KAAKJ,CAAC,CAACK,aAAa,IAAI,EAAE,IAAIL,CAAC,CAACM,QAAQ,GAAG,GAAG,EAAE;QACnI5B,KAAK,EAAEvC,MAAM,CAAC6D,CAAC,CAACO,KAAK,CAAC,CAACC,IAAI,CAACR,CAAC,CAACS,KAAK,IAAI,CAAC,CAAC,CAACb,WAAW,EAAE;QACvDhB,GAAG,EAAEzC,MAAM,CAAC6D,CAAC,CAACO,KAAK,CAAC,CAACC,IAAI,CAACR,CAAC,CAACS,KAAK,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAACd,WAAW,EAAE;QACrEe,KAAK,EAAEX,CAAC,CAACE,OAAO,GAAG,SAAS,GAAG,SAAS;QACxCU,eAAe,EAAEZ,CAAC,CAACE,OAAO,GAAG,SAAS,GAAG,SAAS;QAClDW,OAAO,EAAEb,CAAC,CAACE,OAAO,GAAGY,SAAS,GAAG;OAClC;IACH,CAAC,CAAC;IACF,IAAI,CAACpD,eAAe,GAAG;MACrB,GAAG,IAAI,CAACA,eAAe;MAAG;MAC1BF,MAAM,EAAE,IAAI,CAACA,MAAM,CAAI;KACxB;EACH;EAEAuD,eAAeA,CAACC,WAAmB;IACjC,IAAI,CAACzB,gBAAgB,GAAGyB,WAAW;IACnC,IAAI,CAACnC,eAAe,CAAC,IAAI,CAACJ,SAAS,EAAE,IAAI,CAACE,OAAO,CAAC,CAACG,SAAS,EAAE;EAChE;;;uCAlGW5B,iBAAiB,EAAAV,EAAA,CAAAyE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3E,EAAA,CAAAyE,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA7E,EAAA,CAAAyE,iBAAA,CAAAG,EAAA,CAAAE,sBAAA;IAAA;EAAA;;;YAAjBpE,iBAAiB;MAAAqE,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAmCjB7F,qBAAqB;;;;;;;;;;;;;;UCzDhCW,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAoF,SAAA,qBAAiC;UACnCpF,EAAA,CAAAG,YAAA,EAAiB;UAKTH,EAJR,CAAAC,cAAA,mBAAc,aACK,aACI,aACiC,eACQ;UAAAD,EAAA,CAAAE,MAAA,mBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAC,cAAA,mBAA+F;UAApFD,EAAA,CAAAqF,UAAA,4BAAAC,+DAAAC,MAAA;YAAA,OAAkBJ,GAAA,CAAAZ,eAAA,CAAAgB,MAAA,CAAuB;UAAA,EAAC;UAACvF,EAAA,CAAAwF,gBAAA,2BAAAC,8DAAAF,MAAA;YAAAvF,EAAA,CAAA0F,kBAAA,CAAAP,GAAA,CAAApC,gBAAA,EAAAwC,MAAA,MAAAJ,GAAA,CAAApC,gBAAA,GAAAwC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA8B;UAClFvF,EAAA,CAAA2F,UAAA,KAAAC,uCAAA,uBAAiE;UAKvE5F,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAyB,cACH,cACwB;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACrDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,sCAAK;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,eAAuC;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAE/CF,EAF+C,CAAAG,YAAA,EAAM,EAC7C,EACF;UACNH,EAAA,CAAAC,cAAA,eAAoB;UAClBD,EAAA,CAAAoF,SAAA,yBAA2D;UAInEpF,EAHM,CAAAG,YAAA,EAAM,EACF,EACO,EACP;;;UAnBsDH,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAA6F,gBAAA,YAAAV,GAAA,CAAApC,gBAAA,CAA8B;UACtD/C,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAApE,aAAA,CAAgB;UAcjCf,EAAA,CAAAO,SAAA,IAA2B;UAA3BP,EAAA,CAAAI,UAAA,YAAA+E,GAAA,CAAAjE,eAAA,CAA2B;;;qBDHtChC,YAAY,EAAA4G,EAAA,CAAAC,OAAA,EAAEjG,YAAY,EAAAkG,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,qBAAA,EAAAH,EAAA,CAAAI,iBAAA,EAAAJ,EAAA,CAAAK,iBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAEpH,kBAAkB,EAAAqH,EAAA,CAAAtH,qBAAA;MAAAuH,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}