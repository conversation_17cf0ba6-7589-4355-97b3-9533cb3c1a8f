{"ast": null, "code": ";\n(function (root, factory, undef) {\n  if (typeof exports === \"object\") {\n    // CommonJS\n    module.exports = exports = factory(require(\"./core\"), require(\"./evpkdf\"));\n  } else if (typeof define === \"function\" && define.amd) {\n    // AMD\n    define([\"./core\", \"./evpkdf\"], factory);\n  } else {\n    // Global (browser)\n    factory(root.CryptoJS);\n  }\n})(this, function (CryptoJS) {\n  /**\n   * Cipher core components.\n   */\n  CryptoJS.lib.Cipher || function (undefined) {\n    // Shortcuts\n    var C = CryptoJS;\n    var C_lib = C.lib;\n    var Base = C_lib.Base;\n    var WordArray = C_lib.WordArray;\n    var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm;\n    var C_enc = C.enc;\n    var Utf8 = C_enc.Utf8;\n    var Base64 = C_enc.Base64;\n    var C_algo = C.algo;\n    var EvpKDF = C_algo.EvpKDF;\n\n    /**\n     * Abstract base cipher template.\n     *\n     * @property {number} keySize This cipher's key size. Default: 4 (128 bits)\n     * @property {number} ivSize This cipher's IV size. Default: 4 (128 bits)\n     * @property {number} _ENC_XFORM_MODE A constant representing encryption mode.\n     * @property {number} _DEC_XFORM_MODE A constant representing decryption mode.\n     */\n    var Cipher = C_lib.Cipher = BufferedBlockAlgorithm.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {WordArray} iv The IV to use for this operation.\n       */\n      cfg: Base.extend(),\n      /**\n       * Creates this cipher in encryption mode.\n       *\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {Cipher} A cipher instance.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.createEncryptor(keyWordArray, { iv: ivWordArray });\n       */\n      createEncryptor: function (key, cfg) {\n        return this.create(this._ENC_XFORM_MODE, key, cfg);\n      },\n      /**\n       * Creates this cipher in decryption mode.\n       *\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {Cipher} A cipher instance.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.createDecryptor(keyWordArray, { iv: ivWordArray });\n       */\n      createDecryptor: function (key, cfg) {\n        return this.create(this._DEC_XFORM_MODE, key, cfg);\n      },\n      /**\n       * Initializes a newly created cipher.\n       *\n       * @param {number} xformMode Either the encryption or decryption transormation mode constant.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @example\n       *\n       *     var cipher = CryptoJS.algo.AES.create(CryptoJS.algo.AES._ENC_XFORM_MODE, keyWordArray, { iv: ivWordArray });\n       */\n      init: function (xformMode, key, cfg) {\n        // Apply config defaults\n        this.cfg = this.cfg.extend(cfg);\n\n        // Store transform mode and key\n        this._xformMode = xformMode;\n        this._key = key;\n\n        // Set initial values\n        this.reset();\n      },\n      /**\n       * Resets this cipher to its initial state.\n       *\n       * @example\n       *\n       *     cipher.reset();\n       */\n      reset: function () {\n        // Reset data buffer\n        BufferedBlockAlgorithm.reset.call(this);\n\n        // Perform concrete-cipher logic\n        this._doReset();\n      },\n      /**\n       * Adds data to be encrypted or decrypted.\n       *\n       * @param {WordArray|string} dataUpdate The data to encrypt or decrypt.\n       *\n       * @return {WordArray} The data after processing.\n       *\n       * @example\n       *\n       *     var encrypted = cipher.process('data');\n       *     var encrypted = cipher.process(wordArray);\n       */\n      process: function (dataUpdate) {\n        // Append\n        this._append(dataUpdate);\n\n        // Process available blocks\n        return this._process();\n      },\n      /**\n       * Finalizes the encryption or decryption process.\n       * Note that the finalize operation is effectively a destructive, read-once operation.\n       *\n       * @param {WordArray|string} dataUpdate The final data to encrypt or decrypt.\n       *\n       * @return {WordArray} The data after final processing.\n       *\n       * @example\n       *\n       *     var encrypted = cipher.finalize();\n       *     var encrypted = cipher.finalize('data');\n       *     var encrypted = cipher.finalize(wordArray);\n       */\n      finalize: function (dataUpdate) {\n        // Final data update\n        if (dataUpdate) {\n          this._append(dataUpdate);\n        }\n\n        // Perform concrete-cipher logic\n        var finalProcessedData = this._doFinalize();\n        return finalProcessedData;\n      },\n      keySize: 128 / 32,\n      ivSize: 128 / 32,\n      _ENC_XFORM_MODE: 1,\n      _DEC_XFORM_MODE: 2,\n      /**\n       * Creates shortcut functions to a cipher's object interface.\n       *\n       * @param {Cipher} cipher The cipher to create a helper for.\n       *\n       * @return {Object} An object with encrypt and decrypt shortcut functions.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var AES = CryptoJS.lib.Cipher._createHelper(CryptoJS.algo.AES);\n       */\n      _createHelper: function () {\n        function selectCipherStrategy(key) {\n          if (typeof key == 'string') {\n            return PasswordBasedCipher;\n          } else {\n            return SerializableCipher;\n          }\n        }\n        return function (cipher) {\n          return {\n            encrypt: function (message, key, cfg) {\n              return selectCipherStrategy(key).encrypt(cipher, message, key, cfg);\n            },\n            decrypt: function (ciphertext, key, cfg) {\n              return selectCipherStrategy(key).decrypt(cipher, ciphertext, key, cfg);\n            }\n          };\n        };\n      }()\n    });\n\n    /**\n     * Abstract base stream cipher template.\n     *\n     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 1 (32 bits)\n     */\n    var StreamCipher = C_lib.StreamCipher = Cipher.extend({\n      _doFinalize: function () {\n        // Process partial blocks\n        var finalProcessedBlocks = this._process(!!'flush');\n        return finalProcessedBlocks;\n      },\n      blockSize: 1\n    });\n\n    /**\n     * Mode namespace.\n     */\n    var C_mode = C.mode = {};\n\n    /**\n     * Abstract base block cipher mode template.\n     */\n    var BlockCipherMode = C_lib.BlockCipherMode = Base.extend({\n      /**\n       * Creates this mode for encryption.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.createEncryptor(cipher, iv.words);\n       */\n      createEncryptor: function (cipher, iv) {\n        return this.Encryptor.create(cipher, iv);\n      },\n      /**\n       * Creates this mode for decryption.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.createDecryptor(cipher, iv.words);\n       */\n      createDecryptor: function (cipher, iv) {\n        return this.Decryptor.create(cipher, iv);\n      },\n      /**\n       * Initializes a newly created mode.\n       *\n       * @param {Cipher} cipher A block cipher instance.\n       * @param {Array} iv The IV words.\n       *\n       * @example\n       *\n       *     var mode = CryptoJS.mode.CBC.Encryptor.create(cipher, iv.words);\n       */\n      init: function (cipher, iv) {\n        this._cipher = cipher;\n        this._iv = iv;\n      }\n    });\n\n    /**\n     * Cipher Block Chaining mode.\n     */\n    var CBC = C_mode.CBC = function () {\n      /**\n       * Abstract base CBC mode.\n       */\n      var CBC = BlockCipherMode.extend();\n\n      /**\n       * CBC encryptor.\n       */\n      CBC.Encryptor = CBC.extend({\n        /**\n         * Processes the data block at offset.\n         *\n         * @param {Array} words The data words to operate on.\n         * @param {number} offset The offset where the block starts.\n         *\n         * @example\n         *\n         *     mode.processBlock(data.words, offset);\n         */\n        processBlock: function (words, offset) {\n          // Shortcuts\n          var cipher = this._cipher;\n          var blockSize = cipher.blockSize;\n\n          // XOR and encrypt\n          xorBlock.call(this, words, offset, blockSize);\n          cipher.encryptBlock(words, offset);\n\n          // Remember this block to use with next block\n          this._prevBlock = words.slice(offset, offset + blockSize);\n        }\n      });\n\n      /**\n       * CBC decryptor.\n       */\n      CBC.Decryptor = CBC.extend({\n        /**\n         * Processes the data block at offset.\n         *\n         * @param {Array} words The data words to operate on.\n         * @param {number} offset The offset where the block starts.\n         *\n         * @example\n         *\n         *     mode.processBlock(data.words, offset);\n         */\n        processBlock: function (words, offset) {\n          // Shortcuts\n          var cipher = this._cipher;\n          var blockSize = cipher.blockSize;\n\n          // Remember this block to use with next block\n          var thisBlock = words.slice(offset, offset + blockSize);\n\n          // Decrypt and XOR\n          cipher.decryptBlock(words, offset);\n          xorBlock.call(this, words, offset, blockSize);\n\n          // This block becomes the previous block\n          this._prevBlock = thisBlock;\n        }\n      });\n      function xorBlock(words, offset, blockSize) {\n        var block;\n\n        // Shortcut\n        var iv = this._iv;\n\n        // Choose mixing block\n        if (iv) {\n          block = iv;\n\n          // Remove IV for subsequent blocks\n          this._iv = undefined;\n        } else {\n          block = this._prevBlock;\n        }\n\n        // XOR blocks\n        for (var i = 0; i < blockSize; i++) {\n          words[offset + i] ^= block[i];\n        }\n      }\n      return CBC;\n    }();\n\n    /**\n     * Padding namespace.\n     */\n    var C_pad = C.pad = {};\n\n    /**\n     * PKCS #5/7 padding strategy.\n     */\n    var Pkcs7 = C_pad.Pkcs7 = {\n      /**\n       * Pads data using the algorithm defined in PKCS #5/7.\n       *\n       * @param {WordArray} data The data to pad.\n       * @param {number} blockSize The multiple that the data should be padded to.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     CryptoJS.pad.Pkcs7.pad(wordArray, 4);\n       */\n      pad: function (data, blockSize) {\n        // Shortcut\n        var blockSizeBytes = blockSize * 4;\n\n        // Count padding bytes\n        var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n        // Create padding word\n        var paddingWord = nPaddingBytes << 24 | nPaddingBytes << 16 | nPaddingBytes << 8 | nPaddingBytes;\n\n        // Create padding\n        var paddingWords = [];\n        for (var i = 0; i < nPaddingBytes; i += 4) {\n          paddingWords.push(paddingWord);\n        }\n        var padding = WordArray.create(paddingWords, nPaddingBytes);\n\n        // Add padding\n        data.concat(padding);\n      },\n      /**\n       * Unpads data that had been padded using the algorithm defined in PKCS #5/7.\n       *\n       * @param {WordArray} data The data to unpad.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     CryptoJS.pad.Pkcs7.unpad(wordArray);\n       */\n      unpad: function (data) {\n        // Get number of padding bytes from last byte\n        var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 0xff;\n\n        // Remove padding\n        data.sigBytes -= nPaddingBytes;\n      }\n    };\n\n    /**\n     * Abstract base block cipher template.\n     *\n     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 4 (128 bits)\n     */\n    var BlockCipher = C_lib.BlockCipher = Cipher.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {Mode} mode The block mode to use. Default: CBC\n       * @property {Padding} padding The padding strategy to use. Default: Pkcs7\n       */\n      cfg: Cipher.cfg.extend({\n        mode: CBC,\n        padding: Pkcs7\n      }),\n      reset: function () {\n        var modeCreator;\n\n        // Reset cipher\n        Cipher.reset.call(this);\n\n        // Shortcuts\n        var cfg = this.cfg;\n        var iv = cfg.iv;\n        var mode = cfg.mode;\n\n        // Reset block mode\n        if (this._xformMode == this._ENC_XFORM_MODE) {\n          modeCreator = mode.createEncryptor;\n        } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{\n            modeCreator = mode.createDecryptor;\n            // Keep at least one block in the buffer for unpadding\n            this._minBufferSize = 1;\n          }\n        if (this._mode && this._mode.__creator == modeCreator) {\n          this._mode.init(this, iv && iv.words);\n        } else {\n          this._mode = modeCreator.call(mode, this, iv && iv.words);\n          this._mode.__creator = modeCreator;\n        }\n      },\n      _doProcessBlock: function (words, offset) {\n        this._mode.processBlock(words, offset);\n      },\n      _doFinalize: function () {\n        var finalProcessedBlocks;\n\n        // Shortcut\n        var padding = this.cfg.padding;\n\n        // Finalize\n        if (this._xformMode == this._ENC_XFORM_MODE) {\n          // Pad data\n          padding.pad(this._data, this.blockSize);\n\n          // Process final blocks\n          finalProcessedBlocks = this._process(!!'flush');\n        } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{\n            // Process final blocks\n            finalProcessedBlocks = this._process(!!'flush');\n\n            // Unpad data\n            padding.unpad(finalProcessedBlocks);\n          }\n        return finalProcessedBlocks;\n      },\n      blockSize: 128 / 32\n    });\n\n    /**\n     * A collection of cipher parameters.\n     *\n     * @property {WordArray} ciphertext The raw ciphertext.\n     * @property {WordArray} key The key to this ciphertext.\n     * @property {WordArray} iv The IV used in the ciphering operation.\n     * @property {WordArray} salt The salt used with a key derivation function.\n     * @property {Cipher} algorithm The cipher algorithm.\n     * @property {Mode} mode The block mode used in the ciphering operation.\n     * @property {Padding} padding The padding scheme used in the ciphering operation.\n     * @property {number} blockSize The block size of the cipher.\n     * @property {Format} formatter The default formatting strategy to convert this cipher params object to a string.\n     */\n    var CipherParams = C_lib.CipherParams = Base.extend({\n      /**\n       * Initializes a newly created cipher params object.\n       *\n       * @param {Object} cipherParams An object with any of the possible cipher parameters.\n       *\n       * @example\n       *\n       *     var cipherParams = CryptoJS.lib.CipherParams.create({\n       *         ciphertext: ciphertextWordArray,\n       *         key: keyWordArray,\n       *         iv: ivWordArray,\n       *         salt: saltWordArray,\n       *         algorithm: CryptoJS.algo.AES,\n       *         mode: CryptoJS.mode.CBC,\n       *         padding: CryptoJS.pad.PKCS7,\n       *         blockSize: 4,\n       *         formatter: CryptoJS.format.OpenSSL\n       *     });\n       */\n      init: function (cipherParams) {\n        this.mixIn(cipherParams);\n      },\n      /**\n       * Converts this cipher params object to a string.\n       *\n       * @param {Format} formatter (Optional) The formatting strategy to use.\n       *\n       * @return {string} The stringified cipher params.\n       *\n       * @throws Error If neither the formatter nor the default formatter is set.\n       *\n       * @example\n       *\n       *     var string = cipherParams + '';\n       *     var string = cipherParams.toString();\n       *     var string = cipherParams.toString(CryptoJS.format.OpenSSL);\n       */\n      toString: function (formatter) {\n        return (formatter || this.formatter).stringify(this);\n      }\n    });\n\n    /**\n     * Format namespace.\n     */\n    var C_format = C.format = {};\n\n    /**\n     * OpenSSL formatting strategy.\n     */\n    var OpenSSLFormatter = C_format.OpenSSL = {\n      /**\n       * Converts a cipher params object to an OpenSSL-compatible string.\n       *\n       * @param {CipherParams} cipherParams The cipher params object.\n       *\n       * @return {string} The OpenSSL-compatible string.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var openSSLString = CryptoJS.format.OpenSSL.stringify(cipherParams);\n       */\n      stringify: function (cipherParams) {\n        var wordArray;\n\n        // Shortcuts\n        var ciphertext = cipherParams.ciphertext;\n        var salt = cipherParams.salt;\n\n        // Format\n        if (salt) {\n          wordArray = WordArray.create([0x53616c74, 0x65645f5f]).concat(salt).concat(ciphertext);\n        } else {\n          wordArray = ciphertext;\n        }\n        return wordArray.toString(Base64);\n      },\n      /**\n       * Converts an OpenSSL-compatible string to a cipher params object.\n       *\n       * @param {string} openSSLStr The OpenSSL-compatible string.\n       *\n       * @return {CipherParams} The cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var cipherParams = CryptoJS.format.OpenSSL.parse(openSSLString);\n       */\n      parse: function (openSSLStr) {\n        var salt;\n\n        // Parse base64\n        var ciphertext = Base64.parse(openSSLStr);\n\n        // Shortcut\n        var ciphertextWords = ciphertext.words;\n\n        // Test for salt\n        if (ciphertextWords[0] == 0x53616c74 && ciphertextWords[1] == 0x65645f5f) {\n          // Extract salt\n          salt = WordArray.create(ciphertextWords.slice(2, 4));\n\n          // Remove salt from ciphertext\n          ciphertextWords.splice(0, 4);\n          ciphertext.sigBytes -= 16;\n        }\n        return CipherParams.create({\n          ciphertext: ciphertext,\n          salt: salt\n        });\n      }\n    };\n\n    /**\n     * A cipher wrapper that returns ciphertext as a serializable cipher params object.\n     */\n    var SerializableCipher = C_lib.SerializableCipher = Base.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {Formatter} format The formatting strategy to convert cipher param objects to and from a string. Default: OpenSSL\n       */\n      cfg: Base.extend({\n        format: OpenSSLFormatter\n      }),\n      /**\n       * Encrypts a message.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {WordArray|string} message The message to encrypt.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {CipherParams} A cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key);\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv });\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       */\n      encrypt: function (cipher, message, key, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Encrypt\n        var encryptor = cipher.createEncryptor(key, cfg);\n        var ciphertext = encryptor.finalize(message);\n\n        // Shortcut\n        var cipherCfg = encryptor.cfg;\n\n        // Create and return serializable cipher params\n        return CipherParams.create({\n          ciphertext: ciphertext,\n          key: key,\n          iv: cipherCfg.iv,\n          algorithm: cipher,\n          mode: cipherCfg.mode,\n          padding: cipherCfg.padding,\n          blockSize: cipher.blockSize,\n          formatter: cfg.format\n        });\n      },\n      /**\n       * Decrypts serialized ciphertext.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n       * @param {WordArray} key The key.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {WordArray} The plaintext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n       */\n      decrypt: function (cipher, ciphertext, key, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, cfg.format);\n\n        // Decrypt\n        var plaintext = cipher.createDecryptor(key, cfg).finalize(ciphertext.ciphertext);\n        return plaintext;\n      },\n      /**\n       * Converts serialized ciphertext to CipherParams,\n       * else assumed CipherParams already and returns ciphertext unchanged.\n       *\n       * @param {CipherParams|string} ciphertext The ciphertext.\n       * @param {Formatter} format The formatting strategy to use to parse serialized ciphertext.\n       *\n       * @return {CipherParams} The unserialized ciphertext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);\n       */\n      _parse: function (ciphertext, format) {\n        if (typeof ciphertext == 'string') {\n          return format.parse(ciphertext, this);\n        } else {\n          return ciphertext;\n        }\n      }\n    });\n\n    /**\n     * Key derivation function namespace.\n     */\n    var C_kdf = C.kdf = {};\n\n    /**\n     * OpenSSL key derivation function.\n     */\n    var OpenSSLKdf = C_kdf.OpenSSL = {\n      /**\n       * Derives a key and IV from a password.\n       *\n       * @param {string} password The password to derive from.\n       * @param {number} keySize The size in words of the key to generate.\n       * @param {number} ivSize The size in words of the IV to generate.\n       * @param {WordArray|string} salt (Optional) A 64-bit salt to use. If omitted, a salt will be generated randomly.\n       *\n       * @return {CipherParams} A cipher params object with the key, IV, and salt.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32);\n       *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32, 'saltsalt');\n       */\n      execute: function (password, keySize, ivSize, salt, hasher) {\n        // Generate random salt\n        if (!salt) {\n          salt = WordArray.random(64 / 8);\n        }\n\n        // Derive key and IV\n        if (!hasher) {\n          var key = EvpKDF.create({\n            keySize: keySize + ivSize\n          }).compute(password, salt);\n        } else {\n          var key = EvpKDF.create({\n            keySize: keySize + ivSize,\n            hasher: hasher\n          }).compute(password, salt);\n        }\n\n        // Separate key and IV\n        var iv = WordArray.create(key.words.slice(keySize), ivSize * 4);\n        key.sigBytes = keySize * 4;\n\n        // Return params\n        return CipherParams.create({\n          key: key,\n          iv: iv,\n          salt: salt\n        });\n      }\n    };\n\n    /**\n     * A serializable cipher wrapper that derives the key from a password,\n     * and returns ciphertext as a serializable cipher params object.\n     */\n    var PasswordBasedCipher = C_lib.PasswordBasedCipher = SerializableCipher.extend({\n      /**\n       * Configuration options.\n       *\n       * @property {KDF} kdf The key derivation function to use to generate a key and IV from a password. Default: OpenSSL\n       */\n      cfg: SerializableCipher.cfg.extend({\n        kdf: OpenSSLKdf\n      }),\n      /**\n       * Encrypts a message using a password.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {WordArray|string} message The message to encrypt.\n       * @param {string} password The password.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {CipherParams} A cipher params object.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password');\n       *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password', { format: CryptoJS.format.OpenSSL });\n       */\n      encrypt: function (cipher, message, password, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Derive key and other params\n        var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, cfg.salt, cfg.hasher);\n\n        // Add IV to config\n        cfg.iv = derivedParams.iv;\n\n        // Encrypt\n        var ciphertext = SerializableCipher.encrypt.call(this, cipher, message, derivedParams.key, cfg);\n\n        // Mix in derived params\n        ciphertext.mixIn(derivedParams);\n        return ciphertext;\n      },\n      /**\n       * Decrypts serialized ciphertext using a password.\n       *\n       * @param {Cipher} cipher The cipher algorithm to use.\n       * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n       * @param {string} password The password.\n       * @param {Object} cfg (Optional) The configuration options to use for this operation.\n       *\n       * @return {WordArray} The plaintext.\n       *\n       * @static\n       *\n       * @example\n       *\n       *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, 'password', { format: CryptoJS.format.OpenSSL });\n       *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, 'password', { format: CryptoJS.format.OpenSSL });\n       */\n      decrypt: function (cipher, ciphertext, password, cfg) {\n        // Apply config defaults\n        cfg = this.cfg.extend(cfg);\n\n        // Convert string to CipherParams\n        ciphertext = this._parse(ciphertext, cfg.format);\n\n        // Derive key and other params\n        var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, ciphertext.salt, cfg.hasher);\n\n        // Add IV to config\n        cfg.iv = derivedParams.iv;\n\n        // Decrypt\n        var plaintext = SerializableCipher.decrypt.call(this, cipher, ciphertext, derivedParams.key, cfg);\n        return plaintext;\n      }\n    });\n  }();\n});", "map": {"version": 3, "names": ["root", "factory", "undef", "exports", "module", "require", "define", "amd", "CryptoJS", "lib", "Cipher", "undefined", "C", "C_lib", "Base", "WordArray", "BufferedBlockAlgorithm", "C_enc", "enc", "Utf8", "Base64", "C_algo", "algo", "EvpKDF", "extend", "cfg", "createEncryptor", "key", "create", "_ENC_XFORM_MODE", "createDecryptor", "_DEC_XFORM_MODE", "init", "xformMode", "_xformMode", "_key", "reset", "call", "_doReset", "process", "dataUpdate", "_append", "_process", "finalize", "finalProcessedData", "_doFinalize", "keySize", "ivSize", "_createHelper", "selectCipherStrategy", "PasswordBasedCipher", "SerializableCipher", "cipher", "encrypt", "message", "decrypt", "ciphertext", "StreamCipher", "finalProcessedBlocks", "blockSize", "C_mode", "mode", "BlockCipherMode", "iv", "Encryptor", "Decryptor", "_cipher", "_iv", "CBC", "processBlock", "words", "offset", "xorBlock", "encryptBlock", "_prevBlock", "slice", "thisBlock", "decryptBlock", "block", "i", "C_pad", "pad", "Pkcs7", "data", "blockSizeBytes", "nPaddingBytes", "sigBytes", "paddingWord", "paddingWords", "push", "padding", "concat", "unpad", "BlockCipher", "modeCreator", "_minBufferSize", "_mode", "__creator", "_doProcessBlock", "_data", "CipherParams", "cipherParams", "mixIn", "toString", "formatter", "stringify", "C_format", "format", "OpenSSLFormatter", "OpenSSL", "wordArray", "salt", "parse", "openSSLStr", "ciphertextWords", "splice", "encryptor", "cipherCfg", "algorithm", "_parse", "plaintext", "C_kdf", "kdf", "OpenSSLKdf", "execute", "password", "hasher", "random", "compute", "derivedParams"], "sources": ["C:/Users/<USER>/Documents/salechange-product/adminFront/node_modules/crypto-js/cipher-core.js"], "sourcesContent": [";(function (root, factory, undef) {\n\tif (typeof exports === \"object\") {\n\t\t// CommonJS\n\t\tmodule.exports = exports = factory(require(\"./core\"), require(\"./evpkdf\"));\n\t}\n\telse if (typeof define === \"function\" && define.amd) {\n\t\t// AMD\n\t\tdefine([\"./core\", \"./evpkdf\"], factory);\n\t}\n\telse {\n\t\t// Global (browser)\n\t\tfactory(root.CryptoJS);\n\t}\n}(this, function (CryptoJS) {\n\n\t/**\n\t * Cipher core components.\n\t */\n\tCryptoJS.lib.Cipher || (function (undefined) {\n\t    // Shortcuts\n\t    var C = CryptoJS;\n\t    var C_lib = C.lib;\n\t    var Base = C_lib.Base;\n\t    var WordArray = C_lib.WordArray;\n\t    var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm;\n\t    var C_enc = C.enc;\n\t    var Utf8 = C_enc.Utf8;\n\t    var Base64 = C_enc.Base64;\n\t    var C_algo = C.algo;\n\t    var EvpKDF = C_algo.EvpKDF;\n\n\t    /**\n\t     * Abstract base cipher template.\n\t     *\n\t     * @property {number} keySize This cipher's key size. Default: 4 (128 bits)\n\t     * @property {number} ivSize This cipher's IV size. Default: 4 (128 bits)\n\t     * @property {number} _ENC_XFORM_MODE A constant representing encryption mode.\n\t     * @property {number} _DEC_XFORM_MODE A constant representing decryption mode.\n\t     */\n\t    var Cipher = C_lib.Cipher = BufferedBlockAlgorithm.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {WordArray} iv The IV to use for this operation.\n\t         */\n\t        cfg: Base.extend(),\n\n\t        /**\n\t         * Creates this cipher in encryption mode.\n\t         *\n\t         * @param {WordArray} key The key.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {Cipher} A cipher instance.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var cipher = CryptoJS.algo.AES.createEncryptor(keyWordArray, { iv: ivWordArray });\n\t         */\n\t        createEncryptor: function (key, cfg) {\n\t            return this.create(this._ENC_XFORM_MODE, key, cfg);\n\t        },\n\n\t        /**\n\t         * Creates this cipher in decryption mode.\n\t         *\n\t         * @param {WordArray} key The key.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {Cipher} A cipher instance.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var cipher = CryptoJS.algo.AES.createDecryptor(keyWordArray, { iv: ivWordArray });\n\t         */\n\t        createDecryptor: function (key, cfg) {\n\t            return this.create(this._DEC_XFORM_MODE, key, cfg);\n\t        },\n\n\t        /**\n\t         * Initializes a newly created cipher.\n\t         *\n\t         * @param {number} xformMode Either the encryption or decryption transormation mode constant.\n\t         * @param {WordArray} key The key.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @example\n\t         *\n\t         *     var cipher = CryptoJS.algo.AES.create(CryptoJS.algo.AES._ENC_XFORM_MODE, keyWordArray, { iv: ivWordArray });\n\t         */\n\t        init: function (xformMode, key, cfg) {\n\t            // Apply config defaults\n\t            this.cfg = this.cfg.extend(cfg);\n\n\t            // Store transform mode and key\n\t            this._xformMode = xformMode;\n\t            this._key = key;\n\n\t            // Set initial values\n\t            this.reset();\n\t        },\n\n\t        /**\n\t         * Resets this cipher to its initial state.\n\t         *\n\t         * @example\n\t         *\n\t         *     cipher.reset();\n\t         */\n\t        reset: function () {\n\t            // Reset data buffer\n\t            BufferedBlockAlgorithm.reset.call(this);\n\n\t            // Perform concrete-cipher logic\n\t            this._doReset();\n\t        },\n\n\t        /**\n\t         * Adds data to be encrypted or decrypted.\n\t         *\n\t         * @param {WordArray|string} dataUpdate The data to encrypt or decrypt.\n\t         *\n\t         * @return {WordArray} The data after processing.\n\t         *\n\t         * @example\n\t         *\n\t         *     var encrypted = cipher.process('data');\n\t         *     var encrypted = cipher.process(wordArray);\n\t         */\n\t        process: function (dataUpdate) {\n\t            // Append\n\t            this._append(dataUpdate);\n\n\t            // Process available blocks\n\t            return this._process();\n\t        },\n\n\t        /**\n\t         * Finalizes the encryption or decryption process.\n\t         * Note that the finalize operation is effectively a destructive, read-once operation.\n\t         *\n\t         * @param {WordArray|string} dataUpdate The final data to encrypt or decrypt.\n\t         *\n\t         * @return {WordArray} The data after final processing.\n\t         *\n\t         * @example\n\t         *\n\t         *     var encrypted = cipher.finalize();\n\t         *     var encrypted = cipher.finalize('data');\n\t         *     var encrypted = cipher.finalize(wordArray);\n\t         */\n\t        finalize: function (dataUpdate) {\n\t            // Final data update\n\t            if (dataUpdate) {\n\t                this._append(dataUpdate);\n\t            }\n\n\t            // Perform concrete-cipher logic\n\t            var finalProcessedData = this._doFinalize();\n\n\t            return finalProcessedData;\n\t        },\n\n\t        keySize: 128/32,\n\n\t        ivSize: 128/32,\n\n\t        _ENC_XFORM_MODE: 1,\n\n\t        _DEC_XFORM_MODE: 2,\n\n\t        /**\n\t         * Creates shortcut functions to a cipher's object interface.\n\t         *\n\t         * @param {Cipher} cipher The cipher to create a helper for.\n\t         *\n\t         * @return {Object} An object with encrypt and decrypt shortcut functions.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var AES = CryptoJS.lib.Cipher._createHelper(CryptoJS.algo.AES);\n\t         */\n\t        _createHelper: (function () {\n\t            function selectCipherStrategy(key) {\n\t                if (typeof key == 'string') {\n\t                    return PasswordBasedCipher;\n\t                } else {\n\t                    return SerializableCipher;\n\t                }\n\t            }\n\n\t            return function (cipher) {\n\t                return {\n\t                    encrypt: function (message, key, cfg) {\n\t                        return selectCipherStrategy(key).encrypt(cipher, message, key, cfg);\n\t                    },\n\n\t                    decrypt: function (ciphertext, key, cfg) {\n\t                        return selectCipherStrategy(key).decrypt(cipher, ciphertext, key, cfg);\n\t                    }\n\t                };\n\t            };\n\t        }())\n\t    });\n\n\t    /**\n\t     * Abstract base stream cipher template.\n\t     *\n\t     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 1 (32 bits)\n\t     */\n\t    var StreamCipher = C_lib.StreamCipher = Cipher.extend({\n\t        _doFinalize: function () {\n\t            // Process partial blocks\n\t            var finalProcessedBlocks = this._process(!!'flush');\n\n\t            return finalProcessedBlocks;\n\t        },\n\n\t        blockSize: 1\n\t    });\n\n\t    /**\n\t     * Mode namespace.\n\t     */\n\t    var C_mode = C.mode = {};\n\n\t    /**\n\t     * Abstract base block cipher mode template.\n\t     */\n\t    var BlockCipherMode = C_lib.BlockCipherMode = Base.extend({\n\t        /**\n\t         * Creates this mode for encryption.\n\t         *\n\t         * @param {Cipher} cipher A block cipher instance.\n\t         * @param {Array} iv The IV words.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var mode = CryptoJS.mode.CBC.createEncryptor(cipher, iv.words);\n\t         */\n\t        createEncryptor: function (cipher, iv) {\n\t            return this.Encryptor.create(cipher, iv);\n\t        },\n\n\t        /**\n\t         * Creates this mode for decryption.\n\t         *\n\t         * @param {Cipher} cipher A block cipher instance.\n\t         * @param {Array} iv The IV words.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var mode = CryptoJS.mode.CBC.createDecryptor(cipher, iv.words);\n\t         */\n\t        createDecryptor: function (cipher, iv) {\n\t            return this.Decryptor.create(cipher, iv);\n\t        },\n\n\t        /**\n\t         * Initializes a newly created mode.\n\t         *\n\t         * @param {Cipher} cipher A block cipher instance.\n\t         * @param {Array} iv The IV words.\n\t         *\n\t         * @example\n\t         *\n\t         *     var mode = CryptoJS.mode.CBC.Encryptor.create(cipher, iv.words);\n\t         */\n\t        init: function (cipher, iv) {\n\t            this._cipher = cipher;\n\t            this._iv = iv;\n\t        }\n\t    });\n\n\t    /**\n\t     * Cipher Block Chaining mode.\n\t     */\n\t    var CBC = C_mode.CBC = (function () {\n\t        /**\n\t         * Abstract base CBC mode.\n\t         */\n\t        var CBC = BlockCipherMode.extend();\n\n\t        /**\n\t         * CBC encryptor.\n\t         */\n\t        CBC.Encryptor = CBC.extend({\n\t            /**\n\t             * Processes the data block at offset.\n\t             *\n\t             * @param {Array} words The data words to operate on.\n\t             * @param {number} offset The offset where the block starts.\n\t             *\n\t             * @example\n\t             *\n\t             *     mode.processBlock(data.words, offset);\n\t             */\n\t            processBlock: function (words, offset) {\n\t                // Shortcuts\n\t                var cipher = this._cipher;\n\t                var blockSize = cipher.blockSize;\n\n\t                // XOR and encrypt\n\t                xorBlock.call(this, words, offset, blockSize);\n\t                cipher.encryptBlock(words, offset);\n\n\t                // Remember this block to use with next block\n\t                this._prevBlock = words.slice(offset, offset + blockSize);\n\t            }\n\t        });\n\n\t        /**\n\t         * CBC decryptor.\n\t         */\n\t        CBC.Decryptor = CBC.extend({\n\t            /**\n\t             * Processes the data block at offset.\n\t             *\n\t             * @param {Array} words The data words to operate on.\n\t             * @param {number} offset The offset where the block starts.\n\t             *\n\t             * @example\n\t             *\n\t             *     mode.processBlock(data.words, offset);\n\t             */\n\t            processBlock: function (words, offset) {\n\t                // Shortcuts\n\t                var cipher = this._cipher;\n\t                var blockSize = cipher.blockSize;\n\n\t                // Remember this block to use with next block\n\t                var thisBlock = words.slice(offset, offset + blockSize);\n\n\t                // Decrypt and XOR\n\t                cipher.decryptBlock(words, offset);\n\t                xorBlock.call(this, words, offset, blockSize);\n\n\t                // This block becomes the previous block\n\t                this._prevBlock = thisBlock;\n\t            }\n\t        });\n\n\t        function xorBlock(words, offset, blockSize) {\n\t            var block;\n\n\t            // Shortcut\n\t            var iv = this._iv;\n\n\t            // Choose mixing block\n\t            if (iv) {\n\t                block = iv;\n\n\t                // Remove IV for subsequent blocks\n\t                this._iv = undefined;\n\t            } else {\n\t                block = this._prevBlock;\n\t            }\n\n\t            // XOR blocks\n\t            for (var i = 0; i < blockSize; i++) {\n\t                words[offset + i] ^= block[i];\n\t            }\n\t        }\n\n\t        return CBC;\n\t    }());\n\n\t    /**\n\t     * Padding namespace.\n\t     */\n\t    var C_pad = C.pad = {};\n\n\t    /**\n\t     * PKCS #5/7 padding strategy.\n\t     */\n\t    var Pkcs7 = C_pad.Pkcs7 = {\n\t        /**\n\t         * Pads data using the algorithm defined in PKCS #5/7.\n\t         *\n\t         * @param {WordArray} data The data to pad.\n\t         * @param {number} blockSize The multiple that the data should be padded to.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     CryptoJS.pad.Pkcs7.pad(wordArray, 4);\n\t         */\n\t        pad: function (data, blockSize) {\n\t            // Shortcut\n\t            var blockSizeBytes = blockSize * 4;\n\n\t            // Count padding bytes\n\t            var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;\n\n\t            // Create padding word\n\t            var paddingWord = (nPaddingBytes << 24) | (nPaddingBytes << 16) | (nPaddingBytes << 8) | nPaddingBytes;\n\n\t            // Create padding\n\t            var paddingWords = [];\n\t            for (var i = 0; i < nPaddingBytes; i += 4) {\n\t                paddingWords.push(paddingWord);\n\t            }\n\t            var padding = WordArray.create(paddingWords, nPaddingBytes);\n\n\t            // Add padding\n\t            data.concat(padding);\n\t        },\n\n\t        /**\n\t         * Unpads data that had been padded using the algorithm defined in PKCS #5/7.\n\t         *\n\t         * @param {WordArray} data The data to unpad.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     CryptoJS.pad.Pkcs7.unpad(wordArray);\n\t         */\n\t        unpad: function (data) {\n\t            // Get number of padding bytes from last byte\n\t            var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;\n\n\t            // Remove padding\n\t            data.sigBytes -= nPaddingBytes;\n\t        }\n\t    };\n\n\t    /**\n\t     * Abstract base block cipher template.\n\t     *\n\t     * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 4 (128 bits)\n\t     */\n\t    var BlockCipher = C_lib.BlockCipher = Cipher.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {Mode} mode The block mode to use. Default: CBC\n\t         * @property {Padding} padding The padding strategy to use. Default: Pkcs7\n\t         */\n\t        cfg: Cipher.cfg.extend({\n\t            mode: CBC,\n\t            padding: Pkcs7\n\t        }),\n\n\t        reset: function () {\n\t            var modeCreator;\n\n\t            // Reset cipher\n\t            Cipher.reset.call(this);\n\n\t            // Shortcuts\n\t            var cfg = this.cfg;\n\t            var iv = cfg.iv;\n\t            var mode = cfg.mode;\n\n\t            // Reset block mode\n\t            if (this._xformMode == this._ENC_XFORM_MODE) {\n\t                modeCreator = mode.createEncryptor;\n\t            } else /* if (this._xformMode == this._DEC_XFORM_MODE) */ {\n\t                modeCreator = mode.createDecryptor;\n\t                // Keep at least one block in the buffer for unpadding\n\t                this._minBufferSize = 1;\n\t            }\n\n\t            if (this._mode && this._mode.__creator == modeCreator) {\n\t                this._mode.init(this, iv && iv.words);\n\t            } else {\n\t                this._mode = modeCreator.call(mode, this, iv && iv.words);\n\t                this._mode.__creator = modeCreator;\n\t            }\n\t        },\n\n\t        _doProcessBlock: function (words, offset) {\n\t            this._mode.processBlock(words, offset);\n\t        },\n\n\t        _doFinalize: function () {\n\t            var finalProcessedBlocks;\n\n\t            // Shortcut\n\t            var padding = this.cfg.padding;\n\n\t            // Finalize\n\t            if (this._xformMode == this._ENC_XFORM_MODE) {\n\t                // Pad data\n\t                padding.pad(this._data, this.blockSize);\n\n\t                // Process final blocks\n\t                finalProcessedBlocks = this._process(!!'flush');\n\t            } else /* if (this._xformMode == this._DEC_XFORM_MODE) */ {\n\t                // Process final blocks\n\t                finalProcessedBlocks = this._process(!!'flush');\n\n\t                // Unpad data\n\t                padding.unpad(finalProcessedBlocks);\n\t            }\n\n\t            return finalProcessedBlocks;\n\t        },\n\n\t        blockSize: 128/32\n\t    });\n\n\t    /**\n\t     * A collection of cipher parameters.\n\t     *\n\t     * @property {WordArray} ciphertext The raw ciphertext.\n\t     * @property {WordArray} key The key to this ciphertext.\n\t     * @property {WordArray} iv The IV used in the ciphering operation.\n\t     * @property {WordArray} salt The salt used with a key derivation function.\n\t     * @property {Cipher} algorithm The cipher algorithm.\n\t     * @property {Mode} mode The block mode used in the ciphering operation.\n\t     * @property {Padding} padding The padding scheme used in the ciphering operation.\n\t     * @property {number} blockSize The block size of the cipher.\n\t     * @property {Format} formatter The default formatting strategy to convert this cipher params object to a string.\n\t     */\n\t    var CipherParams = C_lib.CipherParams = Base.extend({\n\t        /**\n\t         * Initializes a newly created cipher params object.\n\t         *\n\t         * @param {Object} cipherParams An object with any of the possible cipher parameters.\n\t         *\n\t         * @example\n\t         *\n\t         *     var cipherParams = CryptoJS.lib.CipherParams.create({\n\t         *         ciphertext: ciphertextWordArray,\n\t         *         key: keyWordArray,\n\t         *         iv: ivWordArray,\n\t         *         salt: saltWordArray,\n\t         *         algorithm: CryptoJS.algo.AES,\n\t         *         mode: CryptoJS.mode.CBC,\n\t         *         padding: CryptoJS.pad.PKCS7,\n\t         *         blockSize: 4,\n\t         *         formatter: CryptoJS.format.OpenSSL\n\t         *     });\n\t         */\n\t        init: function (cipherParams) {\n\t            this.mixIn(cipherParams);\n\t        },\n\n\t        /**\n\t         * Converts this cipher params object to a string.\n\t         *\n\t         * @param {Format} formatter (Optional) The formatting strategy to use.\n\t         *\n\t         * @return {string} The stringified cipher params.\n\t         *\n\t         * @throws Error If neither the formatter nor the default formatter is set.\n\t         *\n\t         * @example\n\t         *\n\t         *     var string = cipherParams + '';\n\t         *     var string = cipherParams.toString();\n\t         *     var string = cipherParams.toString(CryptoJS.format.OpenSSL);\n\t         */\n\t        toString: function (formatter) {\n\t            return (formatter || this.formatter).stringify(this);\n\t        }\n\t    });\n\n\t    /**\n\t     * Format namespace.\n\t     */\n\t    var C_format = C.format = {};\n\n\t    /**\n\t     * OpenSSL formatting strategy.\n\t     */\n\t    var OpenSSLFormatter = C_format.OpenSSL = {\n\t        /**\n\t         * Converts a cipher params object to an OpenSSL-compatible string.\n\t         *\n\t         * @param {CipherParams} cipherParams The cipher params object.\n\t         *\n\t         * @return {string} The OpenSSL-compatible string.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var openSSLString = CryptoJS.format.OpenSSL.stringify(cipherParams);\n\t         */\n\t        stringify: function (cipherParams) {\n\t            var wordArray;\n\n\t            // Shortcuts\n\t            var ciphertext = cipherParams.ciphertext;\n\t            var salt = cipherParams.salt;\n\n\t            // Format\n\t            if (salt) {\n\t                wordArray = WordArray.create([0x53616c74, 0x65645f5f]).concat(salt).concat(ciphertext);\n\t            } else {\n\t                wordArray = ciphertext;\n\t            }\n\n\t            return wordArray.toString(Base64);\n\t        },\n\n\t        /**\n\t         * Converts an OpenSSL-compatible string to a cipher params object.\n\t         *\n\t         * @param {string} openSSLStr The OpenSSL-compatible string.\n\t         *\n\t         * @return {CipherParams} The cipher params object.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var cipherParams = CryptoJS.format.OpenSSL.parse(openSSLString);\n\t         */\n\t        parse: function (openSSLStr) {\n\t            var salt;\n\n\t            // Parse base64\n\t            var ciphertext = Base64.parse(openSSLStr);\n\n\t            // Shortcut\n\t            var ciphertextWords = ciphertext.words;\n\n\t            // Test for salt\n\t            if (ciphertextWords[0] == 0x53616c74 && ciphertextWords[1] == 0x65645f5f) {\n\t                // Extract salt\n\t                salt = WordArray.create(ciphertextWords.slice(2, 4));\n\n\t                // Remove salt from ciphertext\n\t                ciphertextWords.splice(0, 4);\n\t                ciphertext.sigBytes -= 16;\n\t            }\n\n\t            return CipherParams.create({ ciphertext: ciphertext, salt: salt });\n\t        }\n\t    };\n\n\t    /**\n\t     * A cipher wrapper that returns ciphertext as a serializable cipher params object.\n\t     */\n\t    var SerializableCipher = C_lib.SerializableCipher = Base.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {Formatter} format The formatting strategy to convert cipher param objects to and from a string. Default: OpenSSL\n\t         */\n\t        cfg: Base.extend({\n\t            format: OpenSSLFormatter\n\t        }),\n\n\t        /**\n\t         * Encrypts a message.\n\t         *\n\t         * @param {Cipher} cipher The cipher algorithm to use.\n\t         * @param {WordArray|string} message The message to encrypt.\n\t         * @param {WordArray} key The key.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {CipherParams} A cipher params object.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key);\n\t         *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv });\n\t         *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n\t         */\n\t        encrypt: function (cipher, message, key, cfg) {\n\t            // Apply config defaults\n\t            cfg = this.cfg.extend(cfg);\n\n\t            // Encrypt\n\t            var encryptor = cipher.createEncryptor(key, cfg);\n\t            var ciphertext = encryptor.finalize(message);\n\n\t            // Shortcut\n\t            var cipherCfg = encryptor.cfg;\n\n\t            // Create and return serializable cipher params\n\t            return CipherParams.create({\n\t                ciphertext: ciphertext,\n\t                key: key,\n\t                iv: cipherCfg.iv,\n\t                algorithm: cipher,\n\t                mode: cipherCfg.mode,\n\t                padding: cipherCfg.padding,\n\t                blockSize: cipher.blockSize,\n\t                formatter: cfg.format\n\t            });\n\t        },\n\n\t        /**\n\t         * Decrypts serialized ciphertext.\n\t         *\n\t         * @param {Cipher} cipher The cipher algorithm to use.\n\t         * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n\t         * @param {WordArray} key The key.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {WordArray} The plaintext.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n\t         *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, key, { iv: iv, format: CryptoJS.format.OpenSSL });\n\t         */\n\t        decrypt: function (cipher, ciphertext, key, cfg) {\n\t            // Apply config defaults\n\t            cfg = this.cfg.extend(cfg);\n\n\t            // Convert string to CipherParams\n\t            ciphertext = this._parse(ciphertext, cfg.format);\n\n\t            // Decrypt\n\t            var plaintext = cipher.createDecryptor(key, cfg).finalize(ciphertext.ciphertext);\n\n\t            return plaintext;\n\t        },\n\n\t        /**\n\t         * Converts serialized ciphertext to CipherParams,\n\t         * else assumed CipherParams already and returns ciphertext unchanged.\n\t         *\n\t         * @param {CipherParams|string} ciphertext The ciphertext.\n\t         * @param {Formatter} format The formatting strategy to use to parse serialized ciphertext.\n\t         *\n\t         * @return {CipherParams} The unserialized ciphertext.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);\n\t         */\n\t        _parse: function (ciphertext, format) {\n\t            if (typeof ciphertext == 'string') {\n\t                return format.parse(ciphertext, this);\n\t            } else {\n\t                return ciphertext;\n\t            }\n\t        }\n\t    });\n\n\t    /**\n\t     * Key derivation function namespace.\n\t     */\n\t    var C_kdf = C.kdf = {};\n\n\t    /**\n\t     * OpenSSL key derivation function.\n\t     */\n\t    var OpenSSLKdf = C_kdf.OpenSSL = {\n\t        /**\n\t         * Derives a key and IV from a password.\n\t         *\n\t         * @param {string} password The password to derive from.\n\t         * @param {number} keySize The size in words of the key to generate.\n\t         * @param {number} ivSize The size in words of the IV to generate.\n\t         * @param {WordArray|string} salt (Optional) A 64-bit salt to use. If omitted, a salt will be generated randomly.\n\t         *\n\t         * @return {CipherParams} A cipher params object with the key, IV, and salt.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32);\n\t         *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32, 'saltsalt');\n\t         */\n\t        execute: function (password, keySize, ivSize, salt, hasher) {\n\t            // Generate random salt\n\t            if (!salt) {\n\t                salt = WordArray.random(64/8);\n\t            }\n\n\t            // Derive key and IV\n\t            if (!hasher) {\n\t                var key = EvpKDF.create({ keySize: keySize + ivSize }).compute(password, salt);\n\t            } else {\n\t                var key = EvpKDF.create({ keySize: keySize + ivSize, hasher: hasher }).compute(password, salt);\n\t            }\n\n\n\t            // Separate key and IV\n\t            var iv = WordArray.create(key.words.slice(keySize), ivSize * 4);\n\t            key.sigBytes = keySize * 4;\n\n\t            // Return params\n\t            return CipherParams.create({ key: key, iv: iv, salt: salt });\n\t        }\n\t    };\n\n\t    /**\n\t     * A serializable cipher wrapper that derives the key from a password,\n\t     * and returns ciphertext as a serializable cipher params object.\n\t     */\n\t    var PasswordBasedCipher = C_lib.PasswordBasedCipher = SerializableCipher.extend({\n\t        /**\n\t         * Configuration options.\n\t         *\n\t         * @property {KDF} kdf The key derivation function to use to generate a key and IV from a password. Default: OpenSSL\n\t         */\n\t        cfg: SerializableCipher.cfg.extend({\n\t            kdf: OpenSSLKdf\n\t        }),\n\n\t        /**\n\t         * Encrypts a message using a password.\n\t         *\n\t         * @param {Cipher} cipher The cipher algorithm to use.\n\t         * @param {WordArray|string} message The message to encrypt.\n\t         * @param {string} password The password.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {CipherParams} A cipher params object.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password');\n\t         *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password', { format: CryptoJS.format.OpenSSL });\n\t         */\n\t        encrypt: function (cipher, message, password, cfg) {\n\t            // Apply config defaults\n\t            cfg = this.cfg.extend(cfg);\n\n\t            // Derive key and other params\n\t            var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, cfg.salt, cfg.hasher);\n\n\t            // Add IV to config\n\t            cfg.iv = derivedParams.iv;\n\n\t            // Encrypt\n\t            var ciphertext = SerializableCipher.encrypt.call(this, cipher, message, derivedParams.key, cfg);\n\n\t            // Mix in derived params\n\t            ciphertext.mixIn(derivedParams);\n\n\t            return ciphertext;\n\t        },\n\n\t        /**\n\t         * Decrypts serialized ciphertext using a password.\n\t         *\n\t         * @param {Cipher} cipher The cipher algorithm to use.\n\t         * @param {CipherParams|string} ciphertext The ciphertext to decrypt.\n\t         * @param {string} password The password.\n\t         * @param {Object} cfg (Optional) The configuration options to use for this operation.\n\t         *\n\t         * @return {WordArray} The plaintext.\n\t         *\n\t         * @static\n\t         *\n\t         * @example\n\t         *\n\t         *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, 'password', { format: CryptoJS.format.OpenSSL });\n\t         *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, 'password', { format: CryptoJS.format.OpenSSL });\n\t         */\n\t        decrypt: function (cipher, ciphertext, password, cfg) {\n\t            // Apply config defaults\n\t            cfg = this.cfg.extend(cfg);\n\n\t            // Convert string to CipherParams\n\t            ciphertext = this._parse(ciphertext, cfg.format);\n\n\t            // Derive key and other params\n\t            var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, ciphertext.salt, cfg.hasher);\n\n\t            // Add IV to config\n\t            cfg.iv = derivedParams.iv;\n\n\t            // Decrypt\n\t            var plaintext = SerializableCipher.decrypt.call(this, cipher, ciphertext, derivedParams.key, cfg);\n\n\t            return plaintext;\n\t        }\n\t    });\n\t}());\n\n\n}));"], "mappings": "AAAA;AAAE,WAAUA,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;IAChC;IACAC,MAAM,CAACD,OAAO,GAAGA,OAAO,GAAGF,OAAO,CAACI,OAAO,CAAC,QAAQ,CAAC,EAAEA,OAAO,CAAC,UAAU,CAAC,CAAC;EAC3E,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IACpD;IACAD,MAAM,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAEL,OAAO,CAAC;EACxC,CAAC,MACI;IACJ;IACAA,OAAO,CAACD,IAAI,CAACQ,QAAQ,CAAC;EACvB;AACD,CAAC,EAAC,IAAI,EAAE,UAAUA,QAAQ,EAAE;EAE3B;AACD;AACA;EACCA,QAAQ,CAACC,GAAG,CAACC,MAAM,IAAK,UAAUC,SAAS,EAAE;IACzC;IACA,IAAIC,CAAC,GAAGJ,QAAQ;IAChB,IAAIK,KAAK,GAAGD,CAAC,CAACH,GAAG;IACjB,IAAIK,IAAI,GAAGD,KAAK,CAACC,IAAI;IACrB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC/B,IAAIC,sBAAsB,GAAGH,KAAK,CAACG,sBAAsB;IACzD,IAAIC,KAAK,GAAGL,CAAC,CAACM,GAAG;IACjB,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACrB,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACzB,IAAIC,MAAM,GAAGT,CAAC,CAACU,IAAI;IACnB,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;;IAE1B;AACL;AACA;AACA;AACA;AACA;AACA;AACA;IACK,IAAIb,MAAM,GAAGG,KAAK,CAACH,MAAM,GAAGM,sBAAsB,CAACQ,MAAM,CAAC;MACtD;AACT;AACA;AACA;AACA;MACSC,GAAG,EAAEX,IAAI,CAACU,MAAM,CAAC,CAAC;MAElB;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,eAAe,EAAE,SAAAA,CAAUC,GAAG,EAAEF,GAAG,EAAE;QACjC,OAAO,IAAI,CAACG,MAAM,CAAC,IAAI,CAACC,eAAe,EAAEF,GAAG,EAAEF,GAAG,CAAC;MACtD,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSK,eAAe,EAAE,SAAAA,CAAUH,GAAG,EAAEF,GAAG,EAAE;QACjC,OAAO,IAAI,CAACG,MAAM,CAAC,IAAI,CAACG,eAAe,EAAEJ,GAAG,EAAEF,GAAG,CAAC;MACtD,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSO,IAAI,EAAE,SAAAA,CAAUC,SAAS,EAAEN,GAAG,EAAEF,GAAG,EAAE;QACjC;QACA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;;QAE/B;QACA,IAAI,CAACS,UAAU,GAAGD,SAAS;QAC3B,IAAI,CAACE,IAAI,GAAGR,GAAG;;QAEf;QACA,IAAI,CAACS,KAAK,CAAC,CAAC;MAChB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;MACSA,KAAK,EAAE,SAAAA,CAAA,EAAY;QACf;QACApB,sBAAsB,CAACoB,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;;QAEvC;QACA,IAAI,CAACC,QAAQ,CAAC,CAAC;MACnB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,OAAO,EAAE,SAAAA,CAAUC,UAAU,EAAE;QAC3B;QACA,IAAI,CAACC,OAAO,CAACD,UAAU,CAAC;;QAExB;QACA,OAAO,IAAI,CAACE,QAAQ,CAAC,CAAC;MAC1B,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSC,QAAQ,EAAE,SAAAA,CAAUH,UAAU,EAAE;QAC5B;QACA,IAAIA,UAAU,EAAE;UACZ,IAAI,CAACC,OAAO,CAACD,UAAU,CAAC;QAC5B;;QAEA;QACA,IAAII,kBAAkB,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;QAE3C,OAAOD,kBAAkB;MAC7B,CAAC;MAEDE,OAAO,EAAE,GAAG,GAAC,EAAE;MAEfC,MAAM,EAAE,GAAG,GAAC,EAAE;MAEdlB,eAAe,EAAE,CAAC;MAElBE,eAAe,EAAE,CAAC;MAElB;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSiB,aAAa,EAAG,YAAY;QACxB,SAASC,oBAAoBA,CAACtB,GAAG,EAAE;UAC/B,IAAI,OAAOA,GAAG,IAAI,QAAQ,EAAE;YACxB,OAAOuB,mBAAmB;UAC9B,CAAC,MAAM;YACH,OAAOC,kBAAkB;UAC7B;QACJ;QAEA,OAAO,UAAUC,MAAM,EAAE;UACrB,OAAO;YACHC,OAAO,EAAE,SAAAA,CAAUC,OAAO,EAAE3B,GAAG,EAAEF,GAAG,EAAE;cAClC,OAAOwB,oBAAoB,CAACtB,GAAG,CAAC,CAAC0B,OAAO,CAACD,MAAM,EAAEE,OAAO,EAAE3B,GAAG,EAAEF,GAAG,CAAC;YACvE,CAAC;YAED8B,OAAO,EAAE,SAAAA,CAAUC,UAAU,EAAE7B,GAAG,EAAEF,GAAG,EAAE;cACrC,OAAOwB,oBAAoB,CAACtB,GAAG,CAAC,CAAC4B,OAAO,CAACH,MAAM,EAAEI,UAAU,EAAE7B,GAAG,EAAEF,GAAG,CAAC;YAC1E;UACJ,CAAC;QACL,CAAC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;IACK,IAAIgC,YAAY,GAAG5C,KAAK,CAAC4C,YAAY,GAAG/C,MAAM,CAACc,MAAM,CAAC;MAClDqB,WAAW,EAAE,SAAAA,CAAA,EAAY;QACrB;QACA,IAAIa,oBAAoB,GAAG,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;QAEnD,OAAOgB,oBAAoB;MAC/B,CAAC;MAEDC,SAAS,EAAE;IACf,CAAC,CAAC;;IAEF;AACL;AACA;IACK,IAAIC,MAAM,GAAGhD,CAAC,CAACiD,IAAI,GAAG,CAAC,CAAC;;IAExB;AACL;AACA;IACK,IAAIC,eAAe,GAAGjD,KAAK,CAACiD,eAAe,GAAGhD,IAAI,CAACU,MAAM,CAAC;MACtD;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,eAAe,EAAE,SAAAA,CAAU0B,MAAM,EAAEW,EAAE,EAAE;QACnC,OAAO,IAAI,CAACC,SAAS,CAACpC,MAAM,CAACwB,MAAM,EAAEW,EAAE,CAAC;MAC5C,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSjC,eAAe,EAAE,SAAAA,CAAUsB,MAAM,EAAEW,EAAE,EAAE;QACnC,OAAO,IAAI,CAACE,SAAS,CAACrC,MAAM,CAACwB,MAAM,EAAEW,EAAE,CAAC;MAC5C,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACS/B,IAAI,EAAE,SAAAA,CAAUoB,MAAM,EAAEW,EAAE,EAAE;QACxB,IAAI,CAACG,OAAO,GAAGd,MAAM;QACrB,IAAI,CAACe,GAAG,GAAGJ,EAAE;MACjB;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;IACK,IAAIK,GAAG,GAAGR,MAAM,CAACQ,GAAG,GAAI,YAAY;MAChC;AACT;AACA;MACS,IAAIA,GAAG,GAAGN,eAAe,CAACtC,MAAM,CAAC,CAAC;;MAElC;AACT;AACA;MACS4C,GAAG,CAACJ,SAAS,GAAGI,GAAG,CAAC5C,MAAM,CAAC;QACvB;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACa6C,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;UACnC;UACA,IAAInB,MAAM,GAAG,IAAI,CAACc,OAAO;UACzB,IAAIP,SAAS,GAAGP,MAAM,CAACO,SAAS;;UAEhC;UACAa,QAAQ,CAACnC,IAAI,CAAC,IAAI,EAAEiC,KAAK,EAAEC,MAAM,EAAEZ,SAAS,CAAC;UAC7CP,MAAM,CAACqB,YAAY,CAACH,KAAK,EAAEC,MAAM,CAAC;;UAElC;UACA,IAAI,CAACG,UAAU,GAAGJ,KAAK,CAACK,KAAK,CAACJ,MAAM,EAAEA,MAAM,GAAGZ,SAAS,CAAC;QAC7D;MACJ,CAAC,CAAC;;MAEF;AACT;AACA;MACSS,GAAG,CAACH,SAAS,GAAGG,GAAG,CAAC5C,MAAM,CAAC;QACvB;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACa6C,YAAY,EAAE,SAAAA,CAAUC,KAAK,EAAEC,MAAM,EAAE;UACnC;UACA,IAAInB,MAAM,GAAG,IAAI,CAACc,OAAO;UACzB,IAAIP,SAAS,GAAGP,MAAM,CAACO,SAAS;;UAEhC;UACA,IAAIiB,SAAS,GAAGN,KAAK,CAACK,KAAK,CAACJ,MAAM,EAAEA,MAAM,GAAGZ,SAAS,CAAC;;UAEvD;UACAP,MAAM,CAACyB,YAAY,CAACP,KAAK,EAAEC,MAAM,CAAC;UAClCC,QAAQ,CAACnC,IAAI,CAAC,IAAI,EAAEiC,KAAK,EAAEC,MAAM,EAAEZ,SAAS,CAAC;;UAE7C;UACA,IAAI,CAACe,UAAU,GAAGE,SAAS;QAC/B;MACJ,CAAC,CAAC;MAEF,SAASJ,QAAQA,CAACF,KAAK,EAAEC,MAAM,EAAEZ,SAAS,EAAE;QACxC,IAAImB,KAAK;;QAET;QACA,IAAIf,EAAE,GAAG,IAAI,CAACI,GAAG;;QAEjB;QACA,IAAIJ,EAAE,EAAE;UACJe,KAAK,GAAGf,EAAE;;UAEV;UACA,IAAI,CAACI,GAAG,GAAGxD,SAAS;QACxB,CAAC,MAAM;UACHmE,KAAK,GAAG,IAAI,CAACJ,UAAU;QAC3B;;QAEA;QACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,SAAS,EAAEoB,CAAC,EAAE,EAAE;UAChCT,KAAK,CAACC,MAAM,GAAGQ,CAAC,CAAC,IAAID,KAAK,CAACC,CAAC,CAAC;QACjC;MACJ;MAEA,OAAOX,GAAG;IACd,CAAC,CAAC,CAAE;;IAEJ;AACL;AACA;IACK,IAAIY,KAAK,GAAGpE,CAAC,CAACqE,GAAG,GAAG,CAAC,CAAC;;IAEtB;AACL;AACA;IACK,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK,GAAG;MACtB;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSD,GAAG,EAAE,SAAAA,CAAUE,IAAI,EAAExB,SAAS,EAAE;QAC5B;QACA,IAAIyB,cAAc,GAAGzB,SAAS,GAAG,CAAC;;QAElC;QACA,IAAI0B,aAAa,GAAGD,cAAc,GAAGD,IAAI,CAACG,QAAQ,GAAGF,cAAc;;QAEnE;QACA,IAAIG,WAAW,GAAIF,aAAa,IAAI,EAAE,GAAKA,aAAa,IAAI,EAAG,GAAIA,aAAa,IAAI,CAAE,GAAGA,aAAa;;QAEtG;QACA,IAAIG,YAAY,GAAG,EAAE;QACrB,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,aAAa,EAAEN,CAAC,IAAI,CAAC,EAAE;UACvCS,YAAY,CAACC,IAAI,CAACF,WAAW,CAAC;QAClC;QACA,IAAIG,OAAO,GAAG3E,SAAS,CAACa,MAAM,CAAC4D,YAAY,EAAEH,aAAa,CAAC;;QAE3D;QACAF,IAAI,CAACQ,MAAM,CAACD,OAAO,CAAC;MACxB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,KAAK,EAAE,SAAAA,CAAUT,IAAI,EAAE;QACnB;QACA,IAAIE,aAAa,GAAGF,IAAI,CAACb,KAAK,CAAEa,IAAI,CAACG,QAAQ,GAAG,CAAC,KAAM,CAAC,CAAC,GAAG,IAAI;;QAEhE;QACAH,IAAI,CAACG,QAAQ,IAAID,aAAa;MAClC;IACJ,CAAC;;IAED;AACL;AACA;AACA;AACA;IACK,IAAIQ,WAAW,GAAGhF,KAAK,CAACgF,WAAW,GAAGnF,MAAM,CAACc,MAAM,CAAC;MAChD;AACT;AACA;AACA;AACA;AACA;MACSC,GAAG,EAAEf,MAAM,CAACe,GAAG,CAACD,MAAM,CAAC;QACnBqC,IAAI,EAAEO,GAAG;QACTsB,OAAO,EAAER;MACb,CAAC,CAAC;MAEF9C,KAAK,EAAE,SAAAA,CAAA,EAAY;QACf,IAAI0D,WAAW;;QAEf;QACApF,MAAM,CAAC0B,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;;QAEvB;QACA,IAAIZ,GAAG,GAAG,IAAI,CAACA,GAAG;QAClB,IAAIsC,EAAE,GAAGtC,GAAG,CAACsC,EAAE;QACf,IAAIF,IAAI,GAAGpC,GAAG,CAACoC,IAAI;;QAEnB;QACA,IAAI,IAAI,CAAC3B,UAAU,IAAI,IAAI,CAACL,eAAe,EAAE;UACzCiE,WAAW,GAAGjC,IAAI,CAACnC,eAAe;QACtC,CAAC,MAAM,kDAAmD;YACtDoE,WAAW,GAAGjC,IAAI,CAAC/B,eAAe;YAClC;YACA,IAAI,CAACiE,cAAc,GAAG,CAAC;UAC3B;QAEA,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,SAAS,IAAIH,WAAW,EAAE;UACnD,IAAI,CAACE,KAAK,CAAChE,IAAI,CAAC,IAAI,EAAE+B,EAAE,IAAIA,EAAE,CAACO,KAAK,CAAC;QACzC,CAAC,MAAM;UACH,IAAI,CAAC0B,KAAK,GAAGF,WAAW,CAACzD,IAAI,CAACwB,IAAI,EAAE,IAAI,EAAEE,EAAE,IAAIA,EAAE,CAACO,KAAK,CAAC;UACzD,IAAI,CAAC0B,KAAK,CAACC,SAAS,GAAGH,WAAW;QACtC;MACJ,CAAC;MAEDI,eAAe,EAAE,SAAAA,CAAU5B,KAAK,EAAEC,MAAM,EAAE;QACtC,IAAI,CAACyB,KAAK,CAAC3B,YAAY,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC1C,CAAC;MAED1B,WAAW,EAAE,SAAAA,CAAA,EAAY;QACrB,IAAIa,oBAAoB;;QAExB;QACA,IAAIgC,OAAO,GAAG,IAAI,CAACjE,GAAG,CAACiE,OAAO;;QAE9B;QACA,IAAI,IAAI,CAACxD,UAAU,IAAI,IAAI,CAACL,eAAe,EAAE;UACzC;UACA6D,OAAO,CAACT,GAAG,CAAC,IAAI,CAACkB,KAAK,EAAE,IAAI,CAACxC,SAAS,CAAC;;UAEvC;UACAD,oBAAoB,GAAG,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;QACnD,CAAC,MAAM,kDAAmD;YACtD;YACAgB,oBAAoB,GAAG,IAAI,CAAChB,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC;;YAE/C;YACAgD,OAAO,CAACE,KAAK,CAAClC,oBAAoB,CAAC;UACvC;QAEA,OAAOA,oBAAoB;MAC/B,CAAC;MAEDC,SAAS,EAAE,GAAG,GAAC;IACnB,CAAC,CAAC;;IAEF;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACK,IAAIyC,YAAY,GAAGvF,KAAK,CAACuF,YAAY,GAAGtF,IAAI,CAACU,MAAM,CAAC;MAChD;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSQ,IAAI,EAAE,SAAAA,CAAUqE,YAAY,EAAE;QAC1B,IAAI,CAACC,KAAK,CAACD,YAAY,CAAC;MAC5B,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSE,QAAQ,EAAE,SAAAA,CAAUC,SAAS,EAAE;QAC3B,OAAO,CAACA,SAAS,IAAI,IAAI,CAACA,SAAS,EAAEC,SAAS,CAAC,IAAI,CAAC;MACxD;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;IACK,IAAIC,QAAQ,GAAG9F,CAAC,CAAC+F,MAAM,GAAG,CAAC,CAAC;;IAE5B;AACL;AACA;IACK,IAAIC,gBAAgB,GAAGF,QAAQ,CAACG,OAAO,GAAG;MACtC;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSJ,SAAS,EAAE,SAAAA,CAAUJ,YAAY,EAAE;QAC/B,IAAIS,SAAS;;QAEb;QACA,IAAItD,UAAU,GAAG6C,YAAY,CAAC7C,UAAU;QACxC,IAAIuD,IAAI,GAAGV,YAAY,CAACU,IAAI;;QAE5B;QACA,IAAIA,IAAI,EAAE;UACND,SAAS,GAAG/F,SAAS,CAACa,MAAM,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC+D,MAAM,CAACoB,IAAI,CAAC,CAACpB,MAAM,CAACnC,UAAU,CAAC;QAC1F,CAAC,MAAM;UACHsD,SAAS,GAAGtD,UAAU;QAC1B;QAEA,OAAOsD,SAAS,CAACP,QAAQ,CAACnF,MAAM,CAAC;MACrC,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACS4F,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;QACzB,IAAIF,IAAI;;QAER;QACA,IAAIvD,UAAU,GAAGpC,MAAM,CAAC4F,KAAK,CAACC,UAAU,CAAC;;QAEzC;QACA,IAAIC,eAAe,GAAG1D,UAAU,CAACc,KAAK;;QAEtC;QACA,IAAI4C,eAAe,CAAC,CAAC,CAAC,IAAI,UAAU,IAAIA,eAAe,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE;UACtE;UACAH,IAAI,GAAGhG,SAAS,CAACa,MAAM,CAACsF,eAAe,CAACvC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEpD;UACAuC,eAAe,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5B3D,UAAU,CAAC8B,QAAQ,IAAI,EAAE;QAC7B;QAEA,OAAOc,YAAY,CAACxE,MAAM,CAAC;UAAE4B,UAAU,EAAEA,UAAU;UAAEuD,IAAI,EAAEA;QAAK,CAAC,CAAC;MACtE;IACJ,CAAC;;IAED;AACL;AACA;IACK,IAAI5D,kBAAkB,GAAGtC,KAAK,CAACsC,kBAAkB,GAAGrC,IAAI,CAACU,MAAM,CAAC;MAC5D;AACT;AACA;AACA;AACA;MACSC,GAAG,EAAEX,IAAI,CAACU,MAAM,CAAC;QACbmF,MAAM,EAAEC;MACZ,CAAC,CAAC;MAEF;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSvD,OAAO,EAAE,SAAAA,CAAUD,MAAM,EAAEE,OAAO,EAAE3B,GAAG,EAAEF,GAAG,EAAE;QAC1C;QACAA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;;QAE1B;QACA,IAAI2F,SAAS,GAAGhE,MAAM,CAAC1B,eAAe,CAACC,GAAG,EAAEF,GAAG,CAAC;QAChD,IAAI+B,UAAU,GAAG4D,SAAS,CAACzE,QAAQ,CAACW,OAAO,CAAC;;QAE5C;QACA,IAAI+D,SAAS,GAAGD,SAAS,CAAC3F,GAAG;;QAE7B;QACA,OAAO2E,YAAY,CAACxE,MAAM,CAAC;UACvB4B,UAAU,EAAEA,UAAU;UACtB7B,GAAG,EAAEA,GAAG;UACRoC,EAAE,EAAEsD,SAAS,CAACtD,EAAE;UAChBuD,SAAS,EAAElE,MAAM;UACjBS,IAAI,EAAEwD,SAAS,CAACxD,IAAI;UACpB6B,OAAO,EAAE2B,SAAS,CAAC3B,OAAO;UAC1B/B,SAAS,EAAEP,MAAM,CAACO,SAAS;UAC3B6C,SAAS,EAAE/E,GAAG,CAACkF;QACnB,CAAC,CAAC;MACN,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSpD,OAAO,EAAE,SAAAA,CAAUH,MAAM,EAAEI,UAAU,EAAE7B,GAAG,EAAEF,GAAG,EAAE;QAC7C;QACAA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;;QAE1B;QACA+B,UAAU,GAAG,IAAI,CAAC+D,MAAM,CAAC/D,UAAU,EAAE/B,GAAG,CAACkF,MAAM,CAAC;;QAEhD;QACA,IAAIa,SAAS,GAAGpE,MAAM,CAACtB,eAAe,CAACH,GAAG,EAAEF,GAAG,CAAC,CAACkB,QAAQ,CAACa,UAAU,CAACA,UAAU,CAAC;QAEhF,OAAOgE,SAAS;MACpB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSD,MAAM,EAAE,SAAAA,CAAU/D,UAAU,EAAEmD,MAAM,EAAE;QAClC,IAAI,OAAOnD,UAAU,IAAI,QAAQ,EAAE;UAC/B,OAAOmD,MAAM,CAACK,KAAK,CAACxD,UAAU,EAAE,IAAI,CAAC;QACzC,CAAC,MAAM;UACH,OAAOA,UAAU;QACrB;MACJ;IACJ,CAAC,CAAC;;IAEF;AACL;AACA;IACK,IAAIiE,KAAK,GAAG7G,CAAC,CAAC8G,GAAG,GAAG,CAAC,CAAC;;IAEtB;AACL;AACA;IACK,IAAIC,UAAU,GAAGF,KAAK,CAACZ,OAAO,GAAG;MAC7B;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSe,OAAO,EAAE,SAAAA,CAAUC,QAAQ,EAAE/E,OAAO,EAAEC,MAAM,EAAEgE,IAAI,EAAEe,MAAM,EAAE;QACxD;QACA,IAAI,CAACf,IAAI,EAAE;UACPA,IAAI,GAAGhG,SAAS,CAACgH,MAAM,CAAC,EAAE,GAAC,CAAC,CAAC;QACjC;;QAEA;QACA,IAAI,CAACD,MAAM,EAAE;UACT,IAAInG,GAAG,GAAGJ,MAAM,CAACK,MAAM,CAAC;YAAEkB,OAAO,EAAEA,OAAO,GAAGC;UAAO,CAAC,CAAC,CAACiF,OAAO,CAACH,QAAQ,EAAEd,IAAI,CAAC;QAClF,CAAC,MAAM;UACH,IAAIpF,GAAG,GAAGJ,MAAM,CAACK,MAAM,CAAC;YAAEkB,OAAO,EAAEA,OAAO,GAAGC,MAAM;YAAE+E,MAAM,EAAEA;UAAO,CAAC,CAAC,CAACE,OAAO,CAACH,QAAQ,EAAEd,IAAI,CAAC;QAClG;;QAGA;QACA,IAAIhD,EAAE,GAAGhD,SAAS,CAACa,MAAM,CAACD,GAAG,CAAC2C,KAAK,CAACK,KAAK,CAAC7B,OAAO,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC;QAC/DpB,GAAG,CAAC2D,QAAQ,GAAGxC,OAAO,GAAG,CAAC;;QAE1B;QACA,OAAOsD,YAAY,CAACxE,MAAM,CAAC;UAAED,GAAG,EAAEA,GAAG;UAAEoC,EAAE,EAAEA,EAAE;UAAEgD,IAAI,EAAEA;QAAK,CAAC,CAAC;MAChE;IACJ,CAAC;;IAED;AACL;AACA;AACA;IACK,IAAI7D,mBAAmB,GAAGrC,KAAK,CAACqC,mBAAmB,GAAGC,kBAAkB,CAAC3B,MAAM,CAAC;MAC5E;AACT;AACA;AACA;AACA;MACSC,GAAG,EAAE0B,kBAAkB,CAAC1B,GAAG,CAACD,MAAM,CAAC;QAC/BkG,GAAG,EAAEC;MACT,CAAC,CAAC;MAEF;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACStE,OAAO,EAAE,SAAAA,CAAUD,MAAM,EAAEE,OAAO,EAAEuE,QAAQ,EAAEpG,GAAG,EAAE;QAC/C;QACAA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;;QAE1B;QACA,IAAIwG,aAAa,GAAGxG,GAAG,CAACiG,GAAG,CAACE,OAAO,CAACC,QAAQ,EAAEzE,MAAM,CAACN,OAAO,EAAEM,MAAM,CAACL,MAAM,EAAEtB,GAAG,CAACsF,IAAI,EAAEtF,GAAG,CAACqG,MAAM,CAAC;;QAElG;QACArG,GAAG,CAACsC,EAAE,GAAGkE,aAAa,CAAClE,EAAE;;QAEzB;QACA,IAAIP,UAAU,GAAGL,kBAAkB,CAACE,OAAO,CAAChB,IAAI,CAAC,IAAI,EAAEe,MAAM,EAAEE,OAAO,EAAE2E,aAAa,CAACtG,GAAG,EAAEF,GAAG,CAAC;;QAE/F;QACA+B,UAAU,CAAC8C,KAAK,CAAC2B,aAAa,CAAC;QAE/B,OAAOzE,UAAU;MACrB,CAAC;MAED;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACSD,OAAO,EAAE,SAAAA,CAAUH,MAAM,EAAEI,UAAU,EAAEqE,QAAQ,EAAEpG,GAAG,EAAE;QAClD;QACAA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC;;QAE1B;QACA+B,UAAU,GAAG,IAAI,CAAC+D,MAAM,CAAC/D,UAAU,EAAE/B,GAAG,CAACkF,MAAM,CAAC;;QAEhD;QACA,IAAIsB,aAAa,GAAGxG,GAAG,CAACiG,GAAG,CAACE,OAAO,CAACC,QAAQ,EAAEzE,MAAM,CAACN,OAAO,EAAEM,MAAM,CAACL,MAAM,EAAES,UAAU,CAACuD,IAAI,EAAEtF,GAAG,CAACqG,MAAM,CAAC;;QAEzG;QACArG,GAAG,CAACsC,EAAE,GAAGkE,aAAa,CAAClE,EAAE;;QAEzB;QACA,IAAIyD,SAAS,GAAGrE,kBAAkB,CAACI,OAAO,CAAClB,IAAI,CAAC,IAAI,EAAEe,MAAM,EAAEI,UAAU,EAAEyE,aAAa,CAACtG,GAAG,EAAEF,GAAG,CAAC;QAEjG,OAAO+F,SAAS;MACpB;IACJ,CAAC,CAAC;EACN,CAAC,CAAC,CAAE;AAGL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}