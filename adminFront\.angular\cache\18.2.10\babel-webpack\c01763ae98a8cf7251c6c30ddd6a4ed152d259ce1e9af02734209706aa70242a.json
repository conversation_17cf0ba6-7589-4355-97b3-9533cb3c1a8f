{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Nynorsk [nn]\n//! authors : https://github.com/mechuwind\n//!           <PERSON> : https://github.com/stephen<PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var nn = moment.defineLocale('nn', {\n    months: 'januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember'.split('_'),\n    monthsShort: 'jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag'.split('_'),\n    weekdaysShort: 'su._må._ty._on._to._fr._lau.'.split('_'),\n    weekdaysMin: 'su_må_ty_on_to_fr_la'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY [kl.] H:mm',\n      LLLL: 'dddd D. MMMM YYYY [kl.] HH:mm'\n    },\n    calendar: {\n      sameDay: '[I dag klokka] LT',\n      nextDay: '[I morgon klokka] LT',\n      nextWeek: 'dddd [klokka] LT',\n      lastDay: '[I går klokka] LT',\n      lastWeek: '[Føregåande] dddd [klokka] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'om %s',\n      past: '%s sidan',\n      s: 'nokre sekund',\n      ss: '%d sekund',\n      m: 'eit minutt',\n      mm: '%d minutt',\n      h: 'ein time',\n      hh: '%d timar',\n      d: 'ein dag',\n      dd: '%d dagar',\n      w: 'ei veke',\n      ww: '%d veker',\n      M: 'ein månad',\n      MM: '%d månader',\n      y: 'eit år',\n      yy: '%d år'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return nn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}