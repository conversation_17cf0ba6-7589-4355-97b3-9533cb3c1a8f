{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiPlanGetPlanListPost$Json } from '../fn/plan/api-plan-get-plan-list-post-json';\nimport { apiPlanGetPlanListPost$Plain } from '../fn/plan/api-plan-get-plan-list-post-plain';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport class PlanService extends BaseService {\n  constructor(config, http) {\n    super(config, http);\n  }\n  /** Path part for operation `apiPlanGetPlanListPost()` */\n  static {\n    this.ApiPlanGetPlanListPostPath = '/api/Plan/GetPlanList';\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPlanGetPlanListPost$Plain()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPlanGetPlanListPost$Plain$Response(params, context) {\n    return apiPlanGetPlanListPost$Plain(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Plain$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPlanGetPlanListPost$Plain(params, context) {\n    return this.apiPlanGetPlanListPost$Plain$Response(params, context).pipe(map(r => r.body));\n  }\n  /**\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\n   * To access only the response body, use `apiPlanGetPlanListPost$Json()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPlanGetPlanListPost$Json$Response(params, context) {\n    return apiPlanGetPlanListPost$Json(this.http, this.rootUrl, params, context);\n  }\n  /**\n   * This method provides access only to the response body.\n   * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Json$Response()` instead.\n   *\n   * This method doesn't expect any request body.\n   */\n  apiPlanGetPlanListPost$Json(params, context) {\n    return this.apiPlanGetPlanListPost$Json$Response(params, context).pipe(map(r => r.body));\n  }\n  static {\n    this.ɵfac = function PlanService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlanService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PlanService,\n      factory: PlanService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "BaseService", "apiPlanGetPlanListPost$Json", "apiPlanGetPlanListPost$Plain", "PlanService", "constructor", "config", "http", "ApiPlanGetPlanListPostPath", "apiPlanGetPlanListPost$Plain$Response", "params", "context", "rootUrl", "pipe", "r", "body", "apiPlanGetPlanListPost$Json$Response", "i0", "ɵɵinject", "i1", "ApiConfiguration", "i2", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\jeansalechange\\adminFront\\src\\services\\api\\services\\plan.service.ts"], "sourcesContent": ["/* tslint:disable */\r\n/* eslint-disable */\r\nimport { HttpClient, HttpContext } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\nimport { BaseService } from '../base-service';\r\nimport { ApiConfiguration } from '../api-configuration';\r\nimport { StrictHttpResponse } from '../strict-http-response';\r\n\r\nimport { apiPlanGetPlanListPost$Json } from '../fn/plan/api-plan-get-plan-list-post-json';\r\nimport { ApiPlanGetPlanListPost$Json$Params } from '../fn/plan/api-plan-get-plan-list-post-json';\r\nimport { apiPlanGetPlanListPost$Plain } from '../fn/plan/api-plan-get-plan-list-post-plain';\r\nimport { ApiPlanGetPlanListPost$Plain$Params } from '../fn/plan/api-plan-get-plan-list-post-plain';\r\nimport { PlanListReponseListResponseBase } from '../models/plan-list-reponse-list-response-base';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class PlanService extends BaseService {\r\n  constructor(config: ApiConfiguration, http: HttpClient) {\r\n    super(config, http);\r\n  }\r\n\r\n  /** Path part for operation `apiPlanGetPlanListPost()` */\r\n  static readonly ApiPlanGetPlanListPostPath = '/api/Plan/GetPlanList';\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPlanGetPlanListPost$Plain()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPlanGetPlanListPost$Plain$Response(params?: ApiPlanGetPlanListPost$Plain$Params, context?: HttpContext): Observable<StrictHttpResponse<PlanListReponseListResponseBase>> {\r\n    return apiPlanGetPlanListPost$Plain(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Plain$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPlanGetPlanListPost$Plain(params?: ApiPlanGetPlanListPost$Plain$Params, context?: HttpContext): Observable<PlanListReponseListResponseBase> {\r\n    return this.apiPlanGetPlanListPost$Plain$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<PlanListReponseListResponseBase>): PlanListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * This method provides access to the full `HttpResponse`, allowing access to response headers.\r\n   * To access only the response body, use `apiPlanGetPlanListPost$Json()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPlanGetPlanListPost$Json$Response(params?: ApiPlanGetPlanListPost$Json$Params, context?: HttpContext): Observable<StrictHttpResponse<PlanListReponseListResponseBase>> {\r\n    return apiPlanGetPlanListPost$Json(this.http, this.rootUrl, params, context);\r\n  }\r\n\r\n  /**\r\n   * This method provides access only to the response body.\r\n   * To access the full response (for headers, for example), `apiPlanGetPlanListPost$Json$Response()` instead.\r\n   *\r\n   * This method doesn't expect any request body.\r\n   */\r\n  apiPlanGetPlanListPost$Json(params?: ApiPlanGetPlanListPost$Json$Params, context?: HttpContext): Observable<PlanListReponseListResponseBase> {\r\n    return this.apiPlanGetPlanListPost$Json$Response(params, context).pipe(\r\n      map((r: StrictHttpResponse<PlanListReponseListResponseBase>): PlanListReponseListResponseBase => r.body)\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAKA,SAASA,GAAG,QAAQ,gBAAgB;AAEpC,SAASC,WAAW,QAAQ,iBAAiB;AAI7C,SAASC,2BAA2B,QAAQ,6CAA6C;AAEzF,SAASC,4BAA4B,QAAQ,8CAA8C;;;;AAK3F,OAAM,MAAOC,WAAY,SAAQH,WAAW;EAC1CI,YAAYC,MAAwB,EAAEC,IAAgB;IACpD,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;EACrB;EAEA;;IACgB,KAAAC,0BAA0B,GAAG,uBAAuB;EAAC;EAErE;;;;;;EAMAC,qCAAqCA,CAACC,MAA4C,EAAEC,OAAqB;IACvG,OAAOR,4BAA4B,CAAC,IAAI,CAACI,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC/E;EAEA;;;;;;EAMAR,4BAA4BA,CAACO,MAA4C,EAAEC,OAAqB;IAC9F,OAAO,IAAI,CAACF,qCAAqC,CAACC,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACrEb,GAAG,CAAEc,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;EAEA;;;;;;EAMAC,oCAAoCA,CAACN,MAA2C,EAAEC,OAAqB;IACrG,OAAOT,2BAA2B,CAAC,IAAI,CAACK,IAAI,EAAE,IAAI,CAACK,OAAO,EAAEF,MAAM,EAAEC,OAAO,CAAC;EAC9E;EAEA;;;;;;EAMAT,2BAA2BA,CAACQ,MAA2C,EAAEC,OAAqB;IAC5F,OAAO,IAAI,CAACK,oCAAoC,CAACN,MAAM,EAAEC,OAAO,CAAC,CAACE,IAAI,CACpEb,GAAG,CAAEc,CAAsD,IAAsCA,CAAC,CAACC,IAAI,CAAC,CACzG;EACH;;;uCAlDWX,WAAW,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXlB,WAAW;MAAAmB,OAAA,EAAXnB,WAAW,CAAAoB,IAAA;MAAAC,UAAA,EADE;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}